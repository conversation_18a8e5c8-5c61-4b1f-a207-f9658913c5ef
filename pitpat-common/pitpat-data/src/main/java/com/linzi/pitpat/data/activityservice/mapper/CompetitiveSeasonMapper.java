package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonActivityRangeDto;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveScoreConfigByActivityDateQuery;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;


/**
 * 竞技赛配置 数据访问对象
 *
 * @since 2024年7月15日
 */
@Mapper
public interface CompetitiveSeasonMapper extends BaseMapper<CompetitiveSeasonDo> {

    /**
     * 获取指定类型的活动id
     * 只判断活动是否已经结束，不判断活动是否发奖
     *
     * @param startTime
     * @param endTime
     * @param types
     * @return
     */
    List<CompetitiveSeasonActivityRangeDto> findCompetitiveSeasonActivityIdByTime(ZonedDateTime startTime, ZonedDateTime endTime,
                                                                                  List<ActivityCompetitiveSeasonType> types);

    Integer existCompetitiveActivity(String seasonType);

    /**
     * 获取在线的竞技赛活动
     *
     * @param competitiveSeasonType
     * @param startDate
     * @param endDate
     * @return
     */
    Integer countCompetitiveActivity(@Param("competitiveSeasonType") String competitiveSeasonType,
                                     @Param("startDate") ZonedDateTime startDate,
                                     @Param("endDate") ZonedDateTime endDate,
                                     @Param("maxCount") int count);

    Integer checkExistSeasonalActivity(@Param("seasonId") Long seasonId);

    Integer checkExistAnnualActivity(@Param("seasonId") Long seasonId, @Param("es") List<ActivityStateEnum> es);

    List<CompetitiveSeasonDo> findWaitSendScoreSeason();

    List<Long> queryCompetitiveScoreConfigByActivityDate(@Param("query") CompetitiveScoreConfigByActivityDateQuery query);

    List<Long> findActivityIdByCompetitiveScoreConfigIdAndStartTime(@Param("configIds") List<Long> configIds,
                                                                    @Param("min") ZonedDateTime activityStateDateMin,
                                                                    @Param("max") ZonedDateTime activityStateDateMax);

    List<CompetitiveSeasonActivityRangeDto> findCompetitiveSeasonActivityIdSeasonId(@Param("seasonIds") List<Long> seasonIds);

    /**
     * 查询当前赛事等级是否存在在线的活动
     *
     * @param configId
     * @return
     */
    boolean checkUserScoreConfigActivityOnlineWithBreakAward(Long configId);
}
