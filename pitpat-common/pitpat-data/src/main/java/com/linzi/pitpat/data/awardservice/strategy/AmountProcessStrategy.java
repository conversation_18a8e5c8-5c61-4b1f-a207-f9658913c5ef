package com.linzi.pitpat.data.awardservice.strategy;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardDoProcessResultDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendProcessDto;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 发送金币奖励
 */
@Component("amountProcessStrategy")
@Slf4j
public class AmountProcessStrategy extends AbstractAwardProcessStrategy implements InitializingBean {
    @Autowired
    private AwardConfigAmountService awardConfigAmountService;
    @Autowired
    private AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ActivityAwardConfigService activityAwardConfigService;
    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;
    @Autowired
    private UserCouponManager userCouponManager;

    @Override
    public void doProcess(AwardSendDto dto, List<Long> list, String batchNo) {

        list.forEach(i -> {
            try {
                BigDecimal sendAmount = BigDecimal.ZERO;
                AwardConfigAmount awardConfigAmount = awardConfigAmountService.findByAwardConfigId(i);
                Currency userCurrency = userAccountService.getUserCurrency(dto.getUserId());
                AwardConfigAmountCurrency amountCurrency = awardConfigAmountCurrencyDataService.findByAmountIdAndCurrencyCode(awardConfigAmount.getId(), userCurrency.getCurrencyCode());
                if (Objects.nonNull(amountCurrency)) {
                    sendAmount = amountCurrency.getAmount();
                }
                ActivityAwardConfig awardConfig = activityAwardConfigService.findByAwardId(awardConfigAmount.getAwardConfigId());
                if (awardConfig.getIsDivide() == 1) {
                    if (Objects.isNull(dto.getDivideUserCount())) {
                        return;
                    }
                    sendAmount = sendAmount.divide(new BigDecimal(dto.getDivideUserCount()), 2, RoundingMode.UP);
                }
                Integer rightsInterestsType = null;
                Integer privilegeBrand = null;
                BigDecimal rightsInterestsMultiple = null;
                BigDecimal brandAward = BigDecimal.ZERO;
                // 权益奖励判定
                if (brandRightsListType.contains(dto.getType())) {
                    Integer statusCode = getBrandRightsInterestEnumBySendType(dto.getType());
                    //权益处理
                    ActivityBrandRightsInterests enjoyBenefits = activityBrandInterestsBizService.getBrandRightsInterests(statusCode, dto.getActivityId(), dto.getUserId());
                    if (Objects.nonNull(enjoyBenefits)) {
                        rightsInterestsMultiple = enjoyBenefits.getMultiple();
                        rightsInterestsType = enjoyBenefits.getRightsInterestsType();
                        privilegeBrand = enjoyBenefits.getBrand();
                        sendAmount = sendAmount.multiply(enjoyBenefits.getMultiple()).setScale(2, RoundingMode.UP);

                    }
                }
                // 比较金额大小
                if (sendAmount.compareTo(BigDecimal.ZERO) > 0) {
                    //翻倍奖励券
                    if (Objects.nonNull(dto.getDoubleUserCoupon())) {
                        userCouponManager.endUseCoupon(dto.getDoubleUserCoupon(), sendAmount);
                        sendAmount = sendAmount.add(sendAmount);
                    }
                    //币种格式化
                    sendAmount = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), sendAmount);
                    userAccountService.increaseAmount(sendAmount, dto.getUserId(), true);
                    // 新增用户奖励余额明细
                    String billNo = NanoId.randomNanoId();
                    ;
                    ZonedDateTime tradeTime = ZonedDateTime.now();
                    Long detailId = userAccountDetailService.addRunActivityAccountDetail0131(dto.getUserId(), AccountDetailTypeEnum.NEW_ACTIVITY_100,
                            dto.getType(), 1, sendAmount, billNo, tradeTime, dto.getDetailId(),
                            dto.getActivityId(), dto.getDetailId(), 0, 0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, brandAward);
                    //add log
                    // 更新用户报名表奖金
                    ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(dto.getActivityId(), dto.getUserId());
                    znsRunActivityUserService.updateUserRunAwardAmountById(activityUser.getId(), sendAmount);
                    //添加记录
                    activityUserAwardBizService.saveNew(dto, detailId, AwardTypeEnum.AMOUNT.getType(), batchNo, dto.getTotalBatchNo());

                    putAwardSendFlagIntoCache(dto, i);
                }
            } catch (Exception e) {
                log.error("sendScore doProcess error award_config_id:{}", i, e);
            }
        });
    }

    @Override
    protected AwardDoProcessResultDto doProcess(AwardSendProcessDto awardSendProcessDto) {
        BigDecimal sendAmount = new BigDecimal(awardSendProcessDto.getAwardValue());

        userAccountService.increaseAmount(sendAmount, awardSendProcessDto.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        Long detailId = userAccountDetailService.addRunActivityAccountDetail0131(awardSendProcessDto.getUserId(), AccountDetailTypeEnum.NEW_PERSON_TASK,
                1, 1, sendAmount, billNo, tradeTime, null,
                null, null, 0, 0L, "", null, null, null, null);
        return new AwardDoProcessResultDto().setRefIdList(Collections.singletonList(detailId));
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        AwardProcessStrategyFactory.register(1, this);
    }

    @Override
    public void amountSendProcess(BigDecimal amount, Integer tradeType, Integer tradeSubtype, AwardSendDto dto) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        //币种格式化
        String currencyCode = userAccountService.getUserCurrency(dto.getUserId()).getCurrencyCode();
        amount = I18nConstant.currencyFormat(currencyCode, amount);

        userAccountService.increaseAmount(amount, dto.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(dto.getUserId(), AccountDetailTypeEnum.NEW_ACTIVITY,
                dto.getType(), 1, amount, billNo, tradeTime, dto.getDetailId(),
                dto.getActivityId(), dto.getDetailId(), 0, 0L, "", null, null, BigDecimal.ONE, BigDecimal.ZERO);

        if (Objects.nonNull(dto.getActivityId()) && dto.getActivityId() > 0) {
            // 更新用户报名表奖金
            ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(dto.getActivityId(), dto.getUserId());
            znsRunActivityUserService.updateUserRunAwardAmountById(activityUser.getId(), amount);
        }
    }

    @Override
    public void deleteAwardConfig(List<Long> longList) {
        longList.forEach(i -> {
            AwardConfigAmount awardConfigAmount = awardConfigAmountService.findByAwardConfigId(i);
            awardConfigAmountService.deleteAwardConfigAmountById(awardConfigAmount.getId());
        });
    }
}
