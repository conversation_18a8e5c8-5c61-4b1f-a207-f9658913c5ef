package com.linzi.pitpat.data.activityservice.constant.enums;


import com.google.common.collect.Lists;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 作弊场景枚举
 *
 * <AUTHOR>
 * @date 2024/12/26 17:29
 */
public enum CheatSceneEnum {
    //13:3.0 赛事，15：段位赛，17：用户赛，18：道具赛

    FREE_RUNNING(-1, "自由跑", Lists.newArrayList(-1, -3, -6), 1),
    COURSE_RUNNING(-2, "课程跑", Lists.newArrayList(-2, 19), 1),
    OFFLINE_PK_MATCHING(23, "离线pk", Lists.newArrayList(2), 3),
    NEW_PERSON_GUIDANCE_PK(24, "新人pk", Lists.newArrayList(2, 12), 4),
    NEW_ACTIVITY_ENTRY_TYPER(13, "新赛事活动-单赛事", Lists.newArrayList(13, 14), 1),
    NEW_RANKED_ACTIVITY(15, "新赛事活动-段位赛", Lists.newArrayList(15), 1),
    NEW_PK_ACTIVITY(17, "新pk活动", Lists.newArrayList(17), 5),
    PROP_ACTIVITY(18, "新赛事活动-道具赛", Lists.newArrayList(18), 1),
    FREE_CHALLENGE_ACTIVITY(22, "自由挑战赛", Lists.newArrayList(22), 1),
    ;
    private Integer scene;

    private String desc;
    /**
     * 跑步类型
     *
     * @see RunActivityTypeEnum
     */
    private List<Integer> activityTypes;
    /**
     * 活动子类型
     *
     * @see RunActivitySubTypeEnum
     */
    private Integer activitySubType;

    CheatSceneEnum(Integer scene, String desc, List<Integer> activityTypes, Integer activitySubType) {
        this.scene = scene;
        this.desc = desc;
        this.activityTypes = activityTypes;
        this.activitySubType = activitySubType;
    }

    public static List<CheatSceneEnum> valueList(List<Integer> sceneList) {
        return Arrays.stream(CheatSceneEnum.values()).filter(e -> sceneList.contains(e.getScene())).collect(Collectors.toList());
    }

    public static String findDesc(MainActivity mainActivity, ZnsRunActivityEntity runActivity) {
        if (MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
            return PROP_ACTIVITY.desc;
        } else if (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
            return NEW_RANKED_ACTIVITY.desc;
        } else if (MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
            if (Objects.isNull(runActivity)) {
                return MainActivityTypeEnum.OLD.getRemark();
            } else {
                CheatSceneEnum cheatSceneEnum = Arrays.stream(CheatSceneEnum.values()).filter(e -> e.activityTypes.contains(runActivity.getActivityType()) && e.activitySubType.equals(runActivity.getActivityTypeSub())).findFirst().orElse(null);
                if (Objects.nonNull(cheatSceneEnum)) {
                    return cheatSceneEnum.desc;
                } else {
                    return MainActivityTypeEnum.OLD.getRemark();
                }
            }
        } else {
            return NEW_ACTIVITY_ENTRY_TYPER.desc;
        }
    }

    public Integer getScene() {
        return scene;
    }

    public String getDesc() {
        return desc;
    }

    public List<Integer> getActivityTypes() {
        return activityTypes;
    }

    public Integer getActivitySubType() {
        return activitySubType;
    }
}
