package com.linzi.pitpat.data.activityservice.dto.api.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.filler.base.InnerFiller;
import com.linzi.pitpat.data.userservice.dto.api.response.LimitedEditionAwardResponse;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@Data
@Accessors(chain = true)
public class FreeChallengeActivityTopListResponse {
    /**
     * 活动选项数据，用于下拉选项，包括la等
     */
    private List<FreeChallengeActivityResponse> activityList;
    /**
     * 榜单数据
     */
    @InnerFiller
    private Page<TopPlayerResponse> topList;
    /**
     * 下次榜单时间
     */
    private ZonedDateTime nextRankingTime;
    /**
     * 个人数据
     */
    @InnerFiller
    private TopPersonalPlayerResponse personalData;
    /**
     * 是否发生活动切换，表明进入下一场榜单
     */
    private Boolean isChangeActivity;
    /**
     * 奖品列表
     */
    private List<LimitedEditionAwardResponse> awardList;
}
