package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 用户服装背包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface UserWearsBagDao extends BaseMapper<UserWearsBag> {

    UserWearsBag findById(@Param("id") Long id);

    List<UserWearsBag> getUserWearsByUserIdAndIsNew(@Param("userId") Long userId, @Param("isNew") Integer isNew);

    UserWearsBag getByQuery(UserWearBagQuery query);

    void deleteByIds(@Param("ids") List<Long> ids);

    List<UserWearsBag> selectListByUserIdAndActivityId(Long userId, Long activityId);

    List<UserWearsBag> queryList(Long userId, Long activityId, ZonedDateTime createTime);
}
