package com.linzi.pitpat.data.awardservice.model.dto;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class EggActivityConfigDto {
    private ZonedDateTime gmtDay;
    private BigDecimal freeRun;
    private BigDecimal composeRun;
    private BigDecimal challengeRun;
    private BigDecimal rankRun;
    private BigDecimal guanfangComposeRun;
    private BigDecimal allAmount;
}
