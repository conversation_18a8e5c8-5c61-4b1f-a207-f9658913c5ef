package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 彩蛋活动配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-26
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.entity.EggActivityConfig;
import com.linzi.pitpat.data.awardservice.model.dto.EggActivityConfigDto;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IsEmpty;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface EggActivityConfigDao extends BaseMapper<EggActivityConfig> {


    EggActivityConfig selectEggActivityConfigById(@Param("id") Long id);


    Long insertEggActivityConfig(EggActivityConfig eggActivityConfig);


    Long insertOrUpdateEggActivityConfig(EggActivityConfig eggActivityConfig);


    int updateEggActivityConfigById(EggActivityConfig eggActivityConfig);


    int updateCoverEggActivityConfigById(EggActivityConfig eggActivityConfig);


    int deleteEggActivityConfigById(@Param("id") Long id);

    @OrderByIdDescLimit_1
    EggActivityConfig selectEggActivityConfigByActivityId(Long activityId, @IF Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);

    List<EggActivityConfig> selectEggActivityConfigByActivityIdNew(Long activityId, @IF Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);


    //	@OrderByIdDescLimit_1
    List<EggActivityConfig> selectEggActivityConfigByTypeGmtStartTimeGmtEndTime(Integer activityType, @IF Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);


    @OrderByIdDescLimit_1
    EggActivityConfig selectEggActivityConfigByIdGmtStartTimeGmtEndTime(Long id, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime, Integer status);


    List<EggActivityConfig> selectEggActivityConfigByGroupByActivityType();

    Page<EggActivityConfigDto> selectPageByCondition(IPage page, @Param("tradeType") Integer tradeType,
                                                     @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime, @IF @Param("urgeType") List<Integer> urgeType);

    @OrderByIdDescLimit_1
    EggActivityConfig selectEggActivityConfigByActivityIdRouteIdDateDate(Long activityId, @IsEmpty Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);

    List<EggActivityConfig> selectEggActivityConfigByActivityIdRouteIdDateDateNew(Long activityId, @IsEmpty Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);

    //	@OrderByIdDescLimit_1
    List<EggActivityConfig> selectEggActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(Integer activityType, @IsEmpty Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);

    List<EggActivityConfig> selectEggActivityConfigListByActivityId(Long activityId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);
}
