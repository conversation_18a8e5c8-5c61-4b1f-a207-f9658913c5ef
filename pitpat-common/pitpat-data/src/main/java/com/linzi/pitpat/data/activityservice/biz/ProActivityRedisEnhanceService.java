package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.CacheSync;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.ProActivityCacheType;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.event.KolUserChangeEvent;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.param.EnrollParam;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.param.FriendsParam;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.param.PageViewParam;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityHighlightDo;
import com.linzi.pitpat.data.activityservice.model.query.ProActivityHighlightQuery;
import com.linzi.pitpat.data.activityservice.service.ProActivityHighlightService;
import com.linzi.pitpat.data.clubservice.constant.enums.KolTypeEnum;
import com.linzi.pitpat.data.enums.UserKolStateEnum;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserKol;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.lz.druid.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProActivityRedisEnhanceService {

    public static final String CACHE_KEY = "PRO_ACTIVITY_HIGHLIGHT";
    private final RedissonClient redissonClient;
    private final ISysConfigService sysConfigService;
    private final ProActivityHighlightService proActivityHighlightService;
    private final UserKolService userKolService;
    private final ZnsUserService userService;
    @Autowired
    private List<CacheSync> cacheSyncList;
    private Map<ProActivityCacheType, CacheSync> cacheSyncMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(cacheSyncList)) {
            return;
        }
        for (CacheSync cacheSync : cacheSyncList) {
            cacheSyncMap.put(cacheSync.getType(), cacheSync);
        }
    }


    public String getHighlightCacheKey(Long mainActivityId) {
        return CACHE_KEY + mainActivityId;
    }

    public ProActivityHighlightDo getProActivityHighlight(Long mainActivityId) {
        RBucket<String> bucket = redissonClient.getBucket(getHighlightCacheKey(mainActivityId));
        if (bucket.isExists()) {
            return JsonUtil.readValue(bucket.get(), ProActivityHighlightDo.class);
        }
        ProActivityHighlightDo res = proActivityHighlightService.findByMainActivityId(mainActivityId);
        if (res == null) {
            res = new ProActivityHighlightDo();
        }
        bucket.set(JsonUtil.writeString(res), 2, TimeUnit.MINUTES);
        return res;
    }

    public void updateNo1(Long activityId, Long userId, String firstName, String gradeStr, String gameAwardAmountStr) {

        redissonClient.getBucket(getHighlightCacheKey(activityId)).delete();
        proActivityHighlightService.updateNo1(activityId, userId, firstName, gradeStr, gameAwardAmountStr);
    }

    //    // 浏览量
//    private Long pageView;
//    // 报名人数
//    private Long enrollCount;
//    // CEO是否报名
//    private Integer ceoEnroll;
//    // 第一个报名的kol用户id
//    private Long firstEnrollKolUserId;
//    // 奖励区名次
//    private Integer awardRank;
//    // 第一名用户id
//    private Long rankNo1UserId;
//    // 第一名成绩
//    private Long rankNo1Grade;
//    // 第一名奖励
//    private String rankNo1Award;
    public void addPageViewByUserId(Long mainActivityId, Long userId) {
        redissonClient.getBucket(getHighlightCacheKey(mainActivityId)).delete();
        if (userId != null) {
            PageViewParam pageViewParam = new PageViewParam();
            pageViewParam.setMainId(mainActivityId);
            pageViewParam.setUserId(userId);
            cacheSyncMap.get(ProActivityCacheType.PAGE_VIEW_USER).add(pageViewParam);
        }
    }

    public void addPageViewByEmailAddress(Long activityId, String emailAddress) {
        redissonClient.getBucket(getHighlightCacheKey(activityId)).delete();
        if (StringUtils.isEmpty(emailAddress)) {
            return;
        }
        ZnsUserEntity byEmail = userService.findByEmail(emailAddress);
        if (byEmail != null) {
            PageViewParam pageViewParam = new PageViewParam();
            pageViewParam.setMainId(activityId);
            pageViewParam.setUserId(byEmail.getId());
            cacheSyncMap.get(ProActivityCacheType.PAGE_VIEW_USER).add(pageViewParam);
        }

    }

    public void addPV(Long activityId) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(CACHE_KEY + activityId + ":pv");
        atomicLong.expire(30, TimeUnit.DAYS);
        long l = atomicLong.addAndGet(1);
    }

    /**
     * 用户查看数量
     *
     * @param mainActivityId
     * @return
     */
    public long getPageViewCount(Long mainActivityId) {
        return redissonClient.getAtomicLong(CACHE_KEY + mainActivityId + ":pv").get();
    }

    public void addEnroll(Long mainActivityId, Long userId) {
        redissonClient.getBucket(getHighlightCacheKey(mainActivityId)).delete();
        proActivityHighlightService.addEnroll(mainActivityId);
        String s = sysConfigService.selectConfigByKey("ceo_user_id", true);
        if (String.valueOf(userId).equals(s)) {
            proActivityHighlightService.setCeoEnroll(mainActivityId);
        }
        UserKol byUserId = userKolService.findByUserId(userId);
        if (byUserId != null && KolTypeEnum.GAME.getCode().equals(byUserId.getType()) && UserKolStateEnum.SIGNED.getCode().equals(byUserId.getState())) {
            proActivityHighlightService.setFirstEnrollKolUserId(mainActivityId, userId);
        }
        EnrollParam param = new EnrollParam();
        param.setMainId(mainActivityId);
        param.setUserId(userId);
        cacheSyncMap.get(ProActivityCacheType.ACTIVITY_ENROLL).add(param);
    }

    /**
     * 报名数量
     *
     * @param mainActivityId
     * @return
     */
    public Integer getEnrollCount(Long mainActivityId) {
        EnrollParam enrollParam = new EnrollParam();
        enrollParam.setMainId(mainActivityId);
        RSet<Long> enrollSet = (RSet<Long>) cacheSyncMap.get(ProActivityCacheType.ACTIVITY_ENROLL).getValue(enrollParam);
        if (enrollSet.isExists()) {
            return enrollSet.size();
        }
        return 0;
    }


    /**
     * 好友查看数量
     *
     * @param mainActivityId
     * @param userId
     * @return
     */
    public Integer getFriendsViewCount(Long mainActivityId, Long userId) {
        FriendsParam friendsParam = new FriendsParam();
        friendsParam.setMainId(userId);
        RSet<Long> friends = (RSet<Long>) cacheSyncMap.get(ProActivityCacheType.FRIENDS).getValue(friendsParam);
        PageViewParam pageViewParam = new PageViewParam();
        pageViewParam.setMainId(mainActivityId);
        RSet<Long> pageViewUser = (RSet<Long>) cacheSyncMap.get(ProActivityCacheType.PAGE_VIEW_USER).getValue(pageViewParam);
        if (pageViewUser.isExists()) {
            return friends.readIntersection(pageViewUser.getName()).size();
        } else {
            return 0;
        }
    }

    /**
     * 好友报名数量
     *
     * @param mainActivityId
     * @param userId
     * @return
     */
    public Integer getFriendEnrollCount(Long mainActivityId, Long userId) {
        FriendsParam friendsParam = new FriendsParam();
        friendsParam.setMainId(userId);
        RSet<Long> friends = (RSet<Long>) cacheSyncMap.get(ProActivityCacheType.FRIENDS).getValue(friendsParam);
        EnrollParam enrollParam = new EnrollParam();
        enrollParam.setMainId(mainActivityId);
        String key = cacheSyncMap.get(ProActivityCacheType.ACTIVITY_ENROLL).getKey(enrollParam);
        return friends.readIntersection(key).size();
    }

    /**
     * kol 身份变更统计
     *
     * @param kolUserChangeEvent
     */
    @EventListener
    public void kolChange(KolUserChangeEvent kolUserChangeEvent) {
        if (kolUserChangeEvent.getUserId() == null) {
            return;
        }
        UserKol byUserId = userKolService.findByUserId(kolUserChangeEvent.getUserId());
        boolean b = byUserId != null && KolTypeEnum.GAME.getCode().equals(byUserId.getType()) && UserKolStateEnum.SIGNED.getCode().equals(byUserId.getState());
        if (b) {
            //成为符合要求的kol
            proActivityHighlightService.fillNewKolUser(byUserId.getUserId());

            ProActivityHighlightQuery query = new ProActivityHighlightQuery();
            query.setFirstEnrollKolUserId(byUserId.getUserId());
            List<ProActivityHighlightDo> listByQuery = proActivityHighlightService.findListByQuery(query);
            if (!CollectionUtils.isEmpty(listByQuery)) {
                listByQuery.stream().forEach(proActivityHighlightDo -> {
                    redissonClient.getBucket(getHighlightCacheKey(proActivityHighlightDo.getMainActivityId())).delete();
                });
            }
        } else {
            //被除名了。
            List<Long> userFirstKolActivityId = proActivityHighlightService.userFirstKolActivityId(kolUserChangeEvent.getUserId());
            for (Long l : userFirstKolActivityId) {
                proActivityHighlightService.resetKolUser(l);
                redissonClient.getBucket(getHighlightCacheKey(l)).delete();
            }

        }
    }

}
