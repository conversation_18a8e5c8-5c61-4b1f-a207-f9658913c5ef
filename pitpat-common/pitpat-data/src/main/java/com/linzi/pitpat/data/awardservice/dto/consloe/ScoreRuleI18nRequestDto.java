package com.linzi.pitpat.data.awardservice.dto.consloe;


import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleList;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/3 15:33
 */
@Data
@NoArgsConstructor
public class ScoreRuleI18nRequestDto {
    // 规则id
    private Long ruleId;
    // 语言code
    private String langCode;
    // 语言名称
    private String langName;
    //积分兑换活动名称
    private String activityName;
    //兑换规则,文字
    private String exchangeRule;
    //物品宣传图
    private String advertiseImage;
    // 详情介绍图
    private List<ExchangeScoreRuleList> detailDescImgList;

    /**
     * @since 4.7.0
     * 富文本内容
     */
    private String textContent;

}
