package com.linzi.pitpat.data.awardservice.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class ExchangeScoreRuleResp extends ExchangeScoreRule {
    // ===================优惠券===================
    /**
     * 优惠券标题
     */
    private String title;

    // 优惠券名字
    private String name;

    // 优惠券库存 ，如果为-1 ，则表示不做限制
    private Integer remainQuota;

    // 券来源类型【1：刮刮卡券,2 排行榜,3活动获取,4 后台发放,5 积分兑换 】
    private Integer type;

    // 券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券】
    private Integer couponType;

    //兑换规则图片
    private String exchangeRuleImage;

    // ===================服装===================
    /**
     * 部位类型
     */
    private Integer wearType;

    /**
     * 服装id
     */
    private Integer wearId;

    /**
     * 服装名称
     */
    private String wearName;
    /**
     * 有效期时间 num 数字> 0 永久 不需要,新版不用
     *
     * @tag 2.10.0
     */
    @Deprecated
    private Integer expiredTime;
    //有效期类型【1:days固定天数，2:range固定时间范围】
    private Integer expiryType;
    //有效期开始时间
    private ZonedDateTime gmtStart;
    //有效期结束时间
    private ZonedDateTime gmtEnd;

    // ===================其他===================


    // ===================实物物品===================


    // ===================道具===================


    // ===================音乐===================

    /**
     * 兑换物品类型：1优惠券、2服装、5音乐、6道具、7实物、8其他
     */
    @NotNull
    private Integer exchangeType;

    /**
     * 兑换物品在各自表的主键id
     */
    private Long exchangeId;

    /**
     * 积分是否充足:0：积分不足；1：积分充足
     */
    private Integer pointsEnough;

    // 用户是否可兑换:true-可兑换，false-不可
    private Boolean redeemable;

    /**
     * 兑换商品名称
     */
    private String exchangeName;

    /**
     * 币种类型
     */
    private Currency currency;

    /**
     * 多币种金额数据
     */
    private List<CurrencyAmount> currencyAmountList;
    /**
     * 多币种金额数据
     */
    private List<CurrencyAmount> currencyOriginalExchangeAmountList;
    /**
     * 多币种金额
     */
    @JsonIgnore
    private BigDecimal currencyAmount;

    /**
     * 实物商品Id
     * @since 4.7.0
     */
    private Long goodsId;

    /**
     * 富文本内容
     * @since 4.7.0
     */
    private String textContent;


}
