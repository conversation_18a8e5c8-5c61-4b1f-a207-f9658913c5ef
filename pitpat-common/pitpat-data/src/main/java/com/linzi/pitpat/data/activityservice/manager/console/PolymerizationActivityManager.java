package com.linzi.pitpat.data.activityservice.manager.console;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonBizService;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.constant.ActivityStringConstant.RedisLock;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.PolymerizationActQuery;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCategoryItem;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.service.ActivityAreaService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryItemService;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityImpracticalAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.ActivityPropConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.ActivityRotSettingService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserGroupService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PacerConfigService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.cron.CronUtils;
import com.linzi.pitpat.data.util.cron.TaskScheduleModel;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 聚合活动处理类
 *
 * <AUTHOR>
 * @date 2024/6/12 16:12
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PolymerizationActivityManager {
    private final RedisTemplate redisTemplate;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;
    private final PolymerizationActivityPoleService polymerizationActivityPoleService;
    private final TransactionTemplate transactionTemplate;
    private final ApplicationContext applicationContext;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final ActivityIDGenerateService idGenerateService;
    private final AwardActivityBizService awardActivityBizService;
    private final ActivityCategoryItemService activityCategoryItemService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;

    private final RoomIdBizService roomIdBizService;
    private final static List<Class> PARTICLE_COLLECTOR;
    private final static String FIND_METHOD = "findByActId";
    private final static String STORAGE_METHOD = "saveBatch";
    private final static Long DAY_MILS = 24 * 60 * 60 * 1000l;
    private final static Long DAY_SECONDS = 24 * 60 * 60l;
    private final static Integer JOB_START_HOUR = 8;
    private final static String ZONE = "UTC";
    private final RedissonClient redissonClient;

    static {
        PARTICLE_COLLECTOR =
                Arrays.asList(ActivityPropConfigService.class,
                        ActivityRateLimitService.class,
                        ActivityTeamService.class,
                        ActivityPlaylistRelService.class,
                        ActivityEquipmentConfigService.class,
                        ActivityAreaService.class,
                        ActivityUserGroupService.class,
                        PacerConfigService.class,
                        ActivityRotSettingService.class,
                        ActivityFeeService.class,
                        ActivityBrandRightsInterestsService.class,
                        RunActivityMedalService.class,
                        ActivityDisseminateService.class,
                        ActivityImpracticalAwardConfigService.class,
                        ActivityParamsService.class);

    }


    /**
     * 聚合活动创建或修改
     *
     * @param query
     * @return
     */
    public int actCreateOrUpdate(PolymerizationActQuery query) {
        PolymerizationActivityPole pole = polymerizationActivityPoleService.findByActivityId(query.getMainActivityId());
        if (Objects.nonNull(pole)) {
            pole.setGmtModify(ZonedDateTime.now());
            if (Objects.equals(pole.getIsCreateRole(), 0)) {
                //填充字段
                fillValue(query, pole);
                int result = polymerizationActivityPoleService.update(pole);
                //新增时调聚合活动任务
                log.info("开始聚合活动任务生成----");
                //跑聚合之前删除聚合执行缓存，以免执行时被拦截
                String key = RedisConstants.AGGREGATION_TASK_EXECUTION + pole.getId();
                if (redisTemplate.hasKey(key)) {
                    redisTemplate.delete(key);
                }
                executePolymerization(pole, true);
                return result;
            } else {
                fillValue(query, pole);
                return polymerizationActivityPoleService.update(pole);
            }

        }
        return -1;
    }


    /**
     * 填充字段
     *
     * @param query
     * @param polymerizationActivityPole
     */
    private static void fillValue(PolymerizationActQuery query, PolymerizationActivityPole polymerizationActivityPole) {
        polymerizationActivityPole.setMainActivityId(query.getMainActivityId());
        polymerizationActivityPole.setTaskStartTime(DateUtil.convertTimeZone(query.getTaskStartTime(), "Asia/Shanghai", "UTC"));
        polymerizationActivityPole.setTaskEndTime(DateUtil.convertTimeZone(query.getTaskEndTime(), "Asia/Shanghai", "UTC"));
        polymerizationActivityPole.setGenerateFrequency(query.getGenerateFrequency());
        polymerizationActivityPole.setJobType(query.getJobType()); //每周or每天
        if (Objects.nonNull(query.getDuration())) {
            polymerizationActivityPole.setDuration(query.getDuration() * 60 * 60);
        }
        polymerizationActivityPole.setIsCreateRole(1);
        polymerizationActivityPole.setAutoStatus(1);
        String generateTimeStr = query.getGenerateTime();
        if (StringUtils.hasText(generateTimeStr)) {
            LocalTime startTime = LocalTime.parse("00:00");
            LocalTime endTime = LocalTime.parse(generateTimeStr);
            long generateTime = startTime.until(endTime, ChronoUnit.SECONDS);
            //因为前台展示东八区，后台保存零时区
            if (generateTime >= 60 * 60 * 8) {
                polymerizationActivityPole.setGenerateTime(generateTime - 60 * 60 * 8);
            } else {
                polymerizationActivityPole.setGenerateTime(60 * 60 * 24 - (60 * 60 * 8 - generateTime));
            }
        }
    }


    public void executePolymerization(PolymerizationActivityPole pole) {
        executePolymerization(pole, false, null);
    }


    public void executePolymerization(PolymerizationActivityPole pole, Boolean manual) {
        executePolymerization(pole, manual, null);
    }

    /**
     * 按规则执行聚合活动创建
     *
     * @param pole        创建规则
     * @param manual      是否定时任务触发，true：是，false：不是
     * @param executeDate 执行时间，为空：表示当前时间，只给测试手动执行用，其他时候没有用到
     */
    public String executePolymerization(PolymerizationActivityPole pole, Boolean manual, ZonedDateTime executeDate) {
        Long currentTimeMillis = System.currentTimeMillis();

        Long recentlyHourTime = findRecentlyHourTime(ZONE, JOB_START_HOUR);

        String key = RedisConstants.AGGREGATION_TASK_EXECUTION + pole.getId();
        if (redisTemplate.hasKey(key)) {
            log.info("聚合任务已执行过 ：{}", key);
            return "聚合任务已执行过";
        }
        String excutingKey = RedisConstants.AGGREGATION_TASK_EXECUTION + pole.getMainActivityId();
        redisTemplate.opsForValue().set(excutingKey, "0", 30, TimeUnit.MINUTES);
        log.info("=========聚合任务开始执行=========");
        String batchNo = OrderUtil.getBatchNo();
        EngineContainer container = new EngineContainer();
        String msg;
        if (Objects.equals(pole.getJobType(), 1)) {
            //每天创建聚合活动
            dayPlan(pole, manual, batchNo, container, recentlyHourTime, currentTimeMillis);
            msg = "按日创建活动完成";
            redisTemplate.opsForValue().set(key, "0", recentlyHourTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
            //自动绑定分类
            autoBindCategory(pole);
        } else {
            //每周创建聚合活动
            msg = weekPlan(pole, batchNo, container, executeDate);
            if (Objects.isNull(msg)) {
                msg = "按周创建活动完成";
                redisTemplate.opsForValue().set(key, "0", recentlyHourTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
                //自动绑定分类
                autoBindCategory(pole);
            }
            //里面判断执行状态,所以这里删除
            redisTemplate.delete(key);
        }
        redisTemplate.delete(excutingKey);
        return msg;
    }

    /**
     * 自动绑定分类
     *
     * @param pole
     */
    private void autoBindCategory(PolymerizationActivityPole pole) {
        if (Objects.isNull(pole.getTemplateId())) {
            log.info("PolymerizationActTask#run-----autoBindCategory任务id:{},模板id为空，无需绑定分类", pole.getId());
            return;
        }
        //查询模板是否在活动列表
        List<ActivityCategoryItem> categoryItems = activityCategoryItemService.findListByActivityIds(List.of(pole.getTemplateId()));
        if (!org.springframework.util.CollectionUtils.isEmpty(categoryItems)) {
            List<ActivityCategoryItem> list = activityCategoryItemService.findListByActivityIds(List.of(pole.getMainActivityId()));
            if (!CollectionUtils.isEmpty(list)) {
                log.info("PolymerizationActTask#run-----autoBindCategory任务id:{},此活动绑定分类无需再绑定，activityId:{}", pole.getId(), pole.getMainActivityId());
                return;
            }
            log.info("PolymerizationActTask#run-----autoBindCategory任务id:{},把新活动绑定列表分类下面，activityId:{}", pole.getId(), pole.getMainActivityId());
            //模板在活动列表则也把新活动也绑定列表分类下面
            ActivityCategoryItem categoryItem = categoryItems.get(0);
            categoryItem.setActivityId(pole.getMainActivityId());
            categoryItem.setGmtCreate(ZonedDateTime.now());
            categoryItem.setGmtModified(ZonedDateTime.now());
            categoryItem.setId(null);
            categoryItem.setIsShowHomepage(null);
            categoryItem.setShowHomepageSort(null);
            categoryItem.setHomepageCover(null);
            activityCategoryItemService.insert(categoryItem);
        }
    }

    /**
     * 每周创建聚合活动
     *
     * @param pole
     * @param batchNo
     * @param container
     */
    private String weekPlan(PolymerizationActivityPole pole, String batchNo, EngineContainer container, ZonedDateTime executeDate) {
        //校验规则
        Long mainActivityId = pole.getMainActivityId();
        log.info("PolymerizationActTask#run-----weekPlan任务id:{},开始创建活动，mainActivityId:{}，batchNo:{}，executeDate:{}", pole.getId(), mainActivityId, batchNo, DateUtil.formatDate(executeDate, DateUtil.YYYY_MM_DD_HH_MM_SS));
        executeDate = Optional.ofNullable(executeDate).orElse(ZonedDateTime.now());
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        if (mainActivity.getTimeStyle() == 1) {
            log.info("PolymerizationActTask#run-----weekPlan任务id:{},非法的时间类型，mainActivityId:{}", pole.getId(), mainActivityId);
            return "周计划-非法的时间类型";
        }
        Long generateFrequency = pole.getGenerateFrequency(); //生成频率 【每周(w):1-7】
        if (generateFrequency < 1 || generateFrequency > 7) {
            log.info("PolymerizationActTask#run-----weekPlan任务id:{},开始生成时间:{}不符合规则", pole.getId(), generateFrequency);
            return "周计划-开始生成时间不符合规则";
        }
        if (pole.getTaskEndTime().isBefore(executeDate)) {
            log.info("PolymerizationActTask#run-----weekPlan任务id:{},执行时间大于结束时间", pole.getId());
            return "周计划-执行时间大于结束时间";
        }

        //根据规则计算下一次执行时间
        TaskScheduleModel taskScheduleModel = new TaskScheduleModel();
        taskScheduleModel.setJobType(3);//每周执行一次
        Integer[] dayOfWeeks = new Integer[]{Math.toIntExact(generateFrequency)};
        taskScheduleModel.setDayOfWeeks(dayOfWeeks);//周几
        taskScheduleModel.setHour(8);  //8点30分0秒【就是北京时间16点】
        taskScheduleModel.setMinute(30); //多留30分钟，就是说8:00 - 8:30之间执行都是可以的，防止其他代码执行时间太长而做的兼容
        taskScheduleModel.setSecond(0);
        String cronExp = CronUtils.createCronExpression(taskScheduleModel);
        ZonedDateTime nextRunTime = CronUtils.getCurrentExecTime(cronExp, executeDate);
        nextRunTime = DateUtil.formateDate(nextRunTime, DateUtil.YYYY_MM_DD_HH); //精确到小时

        //计算本次执行的时间
        ZonedDateTime runDate = DateUtil.formateDate(executeDate, DateUtil.YYYY_MM_DD_HH); //精确到小时

        //判断是否可以执行
        boolean allow = DateUtil.eq(runDate, nextRunTime);
        log.info("PolymerizationActTask#run-----weekPlan任务id:{},下一次执行时间：{}，本次运行时间：{},是否可以执行；{}", pole.getId(),
                DateUtil.formatDate(nextRunTime, DateUtil.YYYY_MM_DD_HH_MM_SS),
                DateUtil.formatDate(runDate, DateUtil.YYYY_MM_DD_HH_MM_SS),
                allow);
        if (!allow) {
            //时间规则不满足
            return "周计划-时间规则不满足,下一次执行时间：" + DateUtil.formatDate(nextRunTime, DateUtil.YYYY_MM_DD_HH_MM_SS) + " 本次运行时间：" + DateUtil.formatDate(runDate, DateUtil.YYYY_MM_DD_HH_MM_SS);
        }

        //计算活动开始时间(明天+模板时刻) 2024-03-26 06:15:13
        String activityStartTime = mainActivity.getActivityStartTime();
        String hhmmss = activityStartTime.split(" ")[1];
        String yyyyMMdd = DateUtil.formateDateStr(DateUtil.addDays(runDate, 1), DateUtil.YYYY_MM_DD);
        ZonedDateTime startTime = DateUtil.formateDate(yyyyMMdd + " " + hhmmss, DateUtil.YYYY_MM_DD_HH_MM_SS);

        //校验活动是否创建过
        List<ActivityPolymerizationRecord> list = activityPolymerizationRecordService.findByPole(pole.getId());
        ActivityPolymerizationRecord activityPolymerizationRecord = list.stream().filter(item -> DateUtil.eq(item.getStartTime(), startTime)).findFirst().orElse(null);
        if (activityPolymerizationRecord != null) {
            log.info("PolymerizationActTask#run-----weekPlan任务id:{},该时段活动已创建过", pole.getId());
            return "周计划-该时段活动已创建过";
        }

        //创建活动
        doCreateAct(pole, batchNo, container, mainActivity, startTime.toInstant().toEpochMilli());
        return null;
    }

    /**
     * 每天创建聚合活动
     *
     * @param pole
     * @param manual
     * @param batchNo
     * @param container
     * @param recentlyHourTime
     * @param currentTimeMillis
     */
    private void dayPlan(PolymerizationActivityPole pole, Boolean manual, String batchNo, EngineContainer container, Long recentlyHourTime, Long currentTimeMillis) {
        //TODO job补丁 针对24h情况下打的额外补丁
        if (!manual) {
            patchGenerateActivity24h(pole, batchNo, container, recentlyHourTime);
        }
        //聚合活动及相关配置生成
        for (int i = 0; i < 2; i++) {
            pole.setGenerateTime(pole.getGenerateTime() + i * DAY_SECONDS);
            generatePolymerizationActivity(pole, batchNo, currentTimeMillis, recentlyHourTime, container, manual);
        }
    }

    private void patchGenerateActivity24h(PolymerizationActivityPole pole, String batchNo, EngineContainer container, Long recentlyHourTime) {
        Long mainActivityId = pole.getMainActivityId();
        log.info("=======job 补丁启动模版活动ID mainActivityId:" + mainActivityId);
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        if (mainActivity.getTimeStyle() == 1) {
            log.info("非法的时间类型，活动{}", mainActivityId);
            return;
        }
        Long generateFrequency = pole.getGenerateFrequency() * 1000;
        Long endTime = DateUtil.startOfDate(ZonedDateTime.now()).toInstant().toEpochMilli() + pole.getGenerateTime() * 1000;

        //从当天八点补到下一个偏移量
        Long startTime = recentlyHourTime - DAY_MILS;

        while (startTime < endTime) {
            log.info("开始创建聚合活动,mainActivityId:{},,startTime:{}", mainActivityId, new Date(startTime));
            doCreateAct(pole, batchNo, container, mainActivity, startTime);
            startTime += generateFrequency;
        }


    }

    private void generatePolymerizationActivity(PolymerizationActivityPole pole, String batchNo, Long currentTimeMillis, Long endTimeStamp, EngineContainer container, Boolean manual) {
        Long mainActivityId = pole.getMainActivityId();
        log.info("=======模版活动ID mainActivityId:" + mainActivityId);
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        if (mainActivity.getTimeStyle() == 1) {
            log.info("非法的时间类型，活动{}", mainActivityId);
            return;
        }
        //一天偏移量
        Long todayOffset = pole.getGenerateTime() * 1000;
        //持续时间(s)
        Long duration = pole.getDuration() * 1000;
        Long generateFrequency = pole.getGenerateFrequency() * 1000;
        Long batch = duration / generateFrequency;
        log.info("聚合配置id={},batch={}", pole.getId(), batch);

        //创建活动
        for (Long i = 0l; i < batch; i++) {
            Long offset = todayOffset + i * generateFrequency;
            //是否可以继续生成
            Long startTime = isContinue(offset, currentTimeMillis, endTimeStamp, pole, manual);
            if (Objects.isNull(startTime)) {
                continue;
            }

            log.info("开始创建聚合活动,mainActivityId:{},offset:{},startTime:{}", mainActivityId, offset, new Date(startTime));
            doCreateAct(pole, batchNo, container, mainActivity, startTime);

        }


    }

    private void doCreateAct(PolymerizationActivityPole pole, String batchNo, EngineContainer container, MainActivity mainActivity, Long startTime) {
        RLock lock = redissonClient.getLock(RedisLock.LOCK_ACTIVITY_LISTING_COUNT);
        LockHolder.tryLock(lock, 100, () -> {
            transactionTemplate.execute(status -> {
                //生成新活动
                MainActivity newMainActivity = inoculationNewActivity(mainActivity, startTime);
                log.info("生成新活动 newMainActivity：" + newMainActivity);

                //复制保存奖励
                List<ActivityTargetAwardDto> targetAwardDtos = awardActivityBizService.getAwardByAct(mainActivity);
                awardActivityBizService.saveTargetAward(targetAwardDtos, newMainActivity, subActivityService.getAllSingleActByMain(newMainActivity.getId()));
                //启动复制引擎
                for (Class cls : PARTICLE_COLLECTOR) {
                    Engine engine = container.getEngine(cls);
                    engine.run(mainActivity.getId(), newMainActivity.getId());
                }
                log.info("=========开始保存记录=========");
                mainActivityService.insert(newMainActivity);

                Long runningId = activityPolymerizationRecordService.findOnlyRunningActivityIdByParentId(mainActivity.getId());
                //如果是第一次生成就把第一条记录的mainType设置成running的聚合
                if (Objects.isNull(runningId)) {
                    newMainActivity.setMainType(MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType());
                    mainActivityService.update(newMainActivity);
                }
                //保存聚合记录
                savePolymerizationRecord(mainActivity, newMainActivity, batchNo, pole.getId());
                return Boolean.TRUE;
            });
        });
    }

    private Long isContinue(Long offset, Long currentTimeMillis, Long endTimeStamp, PolymerizationActivityPole pole, Boolean manual) {

        log.info("offset={},currentTimeMillis={},endTimeStamp={}", offset, currentTimeMillis, endTimeStamp);
        Long dayStartTime = DateUtil.startOfDate(ZonedDateTime.now()).toInstant().toEpochMilli();

        //00:00 -08:00-> 前一天16:00 - 24:00， 时间回拨一天
        if (manual) { // && offset>= 57600*1000
            dayStartTime -= DAY_MILS;
            log.info("手动调用，时间回拨一天,dayStartTime={}", dayStartTime);
        }
        Long actStartTime = dayStartTime + offset;
        //不能大于下一个周期开始
        if (actStartTime >= endTimeStamp) {
            log.info("不能大于下一个周期开始,pole.id{}", pole.getId());
            return null;
        }
        //只有手动执行
        if (manual) {
            //不能小于当前时间
            if (actStartTime < currentTimeMillis) {
                log.info("不能小于当前时间,pole.id{}", pole.getId());

                return null;
            }
        }

        //不能超过任务结束时间
        if (actStartTime >= pole.getTaskEndTime().toInstant().toEpochMilli()) {
            log.info("不能超过任务结束时间,pole.id{}", pole.getId());

            return null;
        }
        //不能小于任务开始时间
        if (actStartTime < pole.getTaskStartTime().toInstant().toEpochMilli()) {
            log.info("不能小于任务开始时间,pole.id{}", pole.getId());

            return null;
        }
        //不能生成重复活动
        List<ActivityPolymerizationRecord> records = activityPolymerizationRecordService.findListByParActIdAndStartTime(pole.getMainActivityId(), new Date(actStartTime));
        if (!CollectionUtils.isEmpty(records)) {
            log.info("不能生成重复活动,pole.id{}", pole.getId());
            return null;
        }
        Function<Long, ZonedDateTime> convert = epochMilli -> Instant.ofEpochMilli(epochMilli).atZone(ZoneId.of(ZONE));
        log.info("actStartTime={},currentTimeMillis={},endTimeStamp={}", convert.apply(actStartTime), convert.apply(currentTimeMillis), convert.apply(endTimeStamp));
        return actStartTime;
    }

    public static Long findRecentlyHourTime(String zoneId, int hour) {
        //如果当前时间超过零时区8点，则执行时间+ 1day
        long epochMilli = ZonedDateTime.now(ZoneId.of(zoneId)).with(LocalTime.MIN).withHour(hour).toInstant().toEpochMilli();
        if (System.currentTimeMillis() >= epochMilli) {
            epochMilli = epochMilli + DAY_MILS;
        }
        return epochMilli;
    }

    private void savePolymerizationRecord(MainActivity mainActivity, MainActivity newMainActivity, String batchNo, Long poyId) {
        ActivityPolymerizationRecord record = new ActivityPolymerizationRecord();
        record.setPolyId(poyId);
        record.setParentActivityId(mainActivity.getId());
        record.setActivityId(newMainActivity.getId());
        record.setApplicationStartTime(DateUtil.getDateByStrAndZone(newMainActivity.getApplicationStartTime(), "UTC"));
        record.setApplicationEndTime(DateUtil.getDateByStrAndZone(newMainActivity.getApplicationEndTime(), "UTC"));
        record.setStartTime(DateUtil.getDateByStrAndZone(newMainActivity.getActivityStartTime(), "UTC"));
        record.setEndTime(DateUtil.getDateByStrAndZone(newMainActivity.getActivityEndTime(), "UTC"));
        record.setBatchNo(batchNo);
        activityPolymerizationRecordService.insert(record);
    }

    private MainActivity inoculationNewActivity(MainActivity parentActivity, Long startTime) {

        Long dayStartTime = DateUtil.startOfDate(ZonedDateTime.now()).toInstant().toEpochMilli();

        //创建新活动
        MainActivity newActivity = new MainActivity();
        BeanUtil.copyPropertiesIgnoreNull(parentActivity, newActivity);

        //计算活动持续时间
        Long parStart = DateUtil.getStampByZone(parentActivity.getActivityStartTime(), null);
        Long parEnd = DateUtil.getStampByZone(parentActivity.getActivityEndTime(), null);
        Long durMils = parEnd - parStart;

        //计算活动开始报名时间
        Long beforeCanReportMils;
        if (Objects.equals(parentActivity.getReportTimeType(), 1)) {
            //说明创建就可以报名
            beforeCanReportMils = 0l;
        } else {
            Long parAppStart = DateUtil.getStampByZone(parentActivity.getApplicationStartTime(), null);
            beforeCanReportMils = parStart - parAppStart;
        }
        //计算活动结束报名时间
        Long parAppEnd = DateUtil.getStampByZone(parentActivity.getApplicationEndTime(), null);
        Long afterCanReportMils = parAppEnd - parStart;

        //计算活动开始时间
        //Long newStart = dayStartTime + offset; //startTime
        Long newStart = startTime;
        Long newEnd = newStart + durMils;
        Long newAppStart = newStart - beforeCanReportMils;
        Long newAppEnd = newStart + afterCanReportMils;
        if (Objects.equals(parentActivity.getReportTimeType(), 1)) {
            newAppStart = ZonedDateTime.now().toInstant().toEpochMilli();
        }

        String newStartStr = DateUtil.getStrByDateAndZone(new Date(newStart), TimeZone.getTimeZone("UTC"));
        String newEndStr = DateUtil.getStrByDateAndZone(new Date(newEnd), TimeZone.getTimeZone("UTC"));
        String newAppStartStr = DateUtil.getStrByDateAndZone(new Date(newAppStart), TimeZone.getTimeZone("UTC"));
        String newAppEndStr = DateUtil.getStrByDateAndZone(new Date(newAppEnd), TimeZone.getTimeZone("UTC"));


        //初始化新活动
        newActivity.setId(idGenerateService.generateActivityID());
        newActivity.setActivityNo(null);
        newActivity.setGmtCreate(null);
        newActivity.setGmtModified(null);
        newActivity.setStatus(0);
        newActivity.setActivityState(0);
        newActivity.setMainType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
        newActivity.setOnlineTime(ZonedDateTime.now());
        newActivity.setIsShowInMore(parentActivity.getIsShowInMore());

        //装填活动和报名起止时间
        newActivity.setActivityStartTime(newStartStr);
        newActivity.setActivityEndTime(newEndStr);
        newActivity.setApplicationStartTime(newAppStartStr);
        newActivity.setApplicationEndTime(newAppEndStr);

        //处理子活动
        List<SubActivity> subActivities = subActivityService.getAllSingleActByMain(parentActivity.getId());
        List<SubActivity> newSubList = subActivities.stream().map(k -> {
            SubActivity subActivity = new SubActivity();
            BeanUtil.copyPropertiesIgnoreNull(k, subActivity);
            subActivity.setId(idGenerateService.generateActivityID());
            subActivity.setMainActivityId(newActivity.getId());
            subActivity.setGmtCreate(null);
            subActivity.setGmtModified(null);
            return subActivity;
        }).toList();
        //sava
        subActivityService.savaBatch(newSubList);

        //创建房间
        roomIdBizService.createGameRoom(newActivity, subActivities);
        return newActivity;
    }


    @Data
    @NoArgsConstructor
    @Component
    public class EngineContainer {
        private Map<Class, Engine> container = new ConcurrentHashMap();

        public Engine getEngine(Class cls) {
            if (container.containsKey(cls)) {
                return container.get(cls);
            } else {
                Object service = applicationContext.getBean(cls);
                Engine engine = new Engine();
                engine.initService(service);
                container.put(cls, engine);
                return engine;
            }

        }

    }

    /**
     * 聚合活动规则创建修改
     *
     * @param query
     * @return
     */
    public Boolean polymerizationActCreateOrUpdate(PolymerizationActQuery query) {
        filterCompetitiveCreatePolymerizationActivity(query);
        int i = actCreateOrUpdate(query);
        return i > 0;
    }

    /**
     * 静止竞技赛开始聚合赛事等。
     *
     * @param polymerizationActQuery
     */
    private void filterCompetitiveCreatePolymerizationActivity(PolymerizationActQuery polymerizationActQuery) {
        MainActivity byId = mainActivityService.findById(polymerizationActQuery.getMainActivityId());
        if (byId == null) {
            throw new BaseException("Not Found Activity");
        }
        //开启聚合，检查是否是竞技赛
        if (1 == polymerizationActQuery.getAutoStatus()) {
            if (competitiveSeasonBizService.isCompetitiveActivity(byId.getId())) {
                throw new BaseException("竞技赛无法开启聚合");
            }
        }
    }

    /**
     * 修改自动创建活动状态
     *
     * @param polymerizationActQuery
     * @return
     */
    public boolean autoCreateStatus(PolymerizationActQuery polymerizationActQuery) {
        PolymerizationActivityPole pole = polymerizationActivityPoleService.findByActivityId(polymerizationActQuery.getMainActivityId());
        filterCompetitiveCreatePolymerizationActivity(polymerizationActQuery);
        //修改状态
        if (Objects.nonNull(pole)) {
            pole.setAutoStatus(polymerizationActQuery.getAutoStatus());
            return polymerizationActivityPoleService.update(pole) > 0;
        } else {
            PolymerizationActivityPole polymerizationActivityPole = new PolymerizationActivityPole();
            polymerizationActivityPole.setMainActivityId(polymerizationActQuery.getMainActivityId());
            polymerizationActivityPole.setIsCreateRole(0);
            polymerizationActivityPole.setAutoStatus(polymerizationActQuery.getAutoStatus());
            return polymerizationActivityPoleService.insert(polymerizationActivityPole) > 0;
        }
    }

    @Data
    @NoArgsConstructor
    public class Engine {

        private Function<Long, List> obtain;
        private Function<List, List> process;
        private Consumer<List> storage;
        private Object service;

        public void run(Long mainActId, Long newId) {
            initProcess(newId);
            Function<Long, List> longListFunction = obtain.andThen(process);
            List applied = longListFunction.apply(mainActId);
            storage.accept(applied);
        }

        public void initService(Object service) {
            this.service = service;
            initObtain(FIND_METHOD);
            initStorage(STORAGE_METHOD);
        }

        public void initService(Object service, String findName, String saveName) {
            this.service = service;
            initObtain(findName);
            initStorage(saveName);
        }

        private void initStorage(String saveName) {
            this.setStorage(k -> {
                if (org.apache.commons.collections.CollectionUtils.isEmpty(k)) {
                    return;
                }
                try {
                    findMethod(service.getClass(), saveName, Collection.class).invoke(service, k);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

        }

        private void initObtain(String findName) {
            this.setObtain(finId -> {
                try {
                    Object resFind = findMethod(service.getClass(), findName, Long.class).invoke(service, finId);
                    log.info("Obtain->serviceName:{},resFind:{}", service.getClass().getSimpleName(), resFind);
                    if (Objects.isNull(resFind)) {
                        return new ArrayList<>();
                    }
                    if (!(resFind instanceof List)) {
                        return Arrays.asList(resFind);
                    } else {
                        return (List) resFind;
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }

        private void initProcess(Long newId) {
            this.setProcess(list -> {
                for (Object o : list) {
                    if (Objects.isNull(o)) {
                        continue;
                    }
                    BeanWrapperImpl beanWrapper = new BeanWrapperImpl(o);
                    if (beanWrapper.isReadableProperty("gmtModified")) {
                        beanWrapper.setPropertyValue("gmtModified", null);
                    }
                    if (beanWrapper.isReadableProperty("gmtCreate")) {
                        beanWrapper.setPropertyValue("gmtCreate", null);
                    }
                    if (beanWrapper.isReadableProperty("mainActivityId")) {
                        beanWrapper.setPropertyValue("mainActivityId", newId);
                    } else {
                        beanWrapper.setPropertyValue("activityId", newId);
                    }
                    beanWrapper.setPropertyValue("id", null);
                }
                return list;
            });
        }

        public Method findMethod(Class<?> clazz, String methodName, Class<?> paramType) throws NoSuchMethodException {
            Method[] methods = clazz.getMethods();

            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    Class<?>[] parameterTypes = method.getParameterTypes();
                    if (parameterTypes.length != 1) {
                        continue;
                    }
                    if (paramType.isAssignableFrom(parameterTypes[0])) {
                        return method;
                    }
                }
            }
            throw new NoSuchMethodException("未找到方法：" + methodName + " " + paramType.getSimpleName());
        }


    }
}
