package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 好友对战表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.utils.DF_Const;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface UserFriendMatchDao extends BaseMapper<UserFriendMatch> {


    UserFriendMatch selectUserFriendMatchById(@Param("id") Long id);

    Long insertUserFriendMatch(UserFriendMatch userFriendMatch);


    int updateUserFriendMatchById(UserFriendMatch userFriendMatch);

    void updateUserFriendMatchIsPopById(ZonedDateTime gmtModified, Integer isPop, @By Long id);

    List<UserFriendMatch> selectUserFriendMatchByMatchFriendIdStatusIsPopActivityStartTimeNeedPop(Long matchFriendId, Integer status, Integer isPop, @DateFormat @GE ZonedDateTime activityStartTime, Integer needPop);

    List<UserFriendMatch> selectUserFriendMatchByMatchFriendIdStatus(Long matchFriendId, Integer status);

    List<UserFriendMatch> selectUserFriendMatchByMatchFriendIdIsFriendCurrentDay(Long matchFriendId, Integer isFriend, @DateFormat(DF_Const.y_m_d) ZonedDateTime gmtCreate, Integer activityTypeSub);

    @LIMIT
    UserFriendMatch selectUserFriendMatchByActivityId(Long activityId);
}
