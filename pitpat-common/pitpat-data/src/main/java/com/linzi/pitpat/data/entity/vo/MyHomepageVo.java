package com.linzi.pitpat.data.entity.vo;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.user.KocIdentityCredentialDataFiller;
import com.linzi.pitpat.data.filler.user.KocIdentityTypeDataFiller;
import com.linzi.pitpat.data.systemservice.dto.response.AppCommunityContentVo;
import com.linzi.pitpat.data.systemservice.model.vo.ContactUsListVO;
import com.linzi.pitpat.data.userservice.enums.UserIdentityEnum;
import com.linzi.pitpat.data.userservice.model.vo.ClubInfoDto;
import com.linzi.pitpat.data.vo.UserExpDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@ToString
public class MyHomepageVo {

    private String nickname;
    private String headPortrait;
    private String userCode;
    private Integer followCount;
    private Integer fansCount;
    //用户等级
    private Integer userLevel;
    /**
     * 是否显示kol标识
     */
    private boolean kol;
    /**
     * 当前用户是否拥有club
     */
    private boolean hasClub;
    /**
     * 是否可以新建俱乐部
     */
    private boolean canCreateClub;
    /**
     *
     */
    private UserExpDto userExp;
    /**
     * 用户等级
     */
    private Integer historyUserLevel;
    /**
     * 等级应该有的经验值
     */
    private Integer levelMaxExp;
    /**
     * 等级应该有的经验值
     */
    private Integer levelMinExp;
    /**
     *
     */
    private Integer runDays;
    /**
     *
     */
    private Integer totalRunCount;
    /**
     *
     */
    private Integer runMileage;
    /**
     *
     */
    private Integer totalMedalCount;
    /**
     *
     */
    private Integer hasNoShowMedal;
    /**
     * 是否盛和版本
     */
    private Integer shc;
    /**
     * 赛事奖金
     */
    private BigDecimal totalBonus;
    /**
     * 赛事奖金的货币信息
     */
    private Currency currency;
    /**
     *
     */
    private List<Map<String, Object>> userMedalList;

    //添加商城和分销开关
    private String mallSwitch;
    private String distributionSwitch;

    private ContactUsListVO contactUs;

    private Integer myRunScore;

    private Integer showMonthBoard;
    /**
     * 用户卷数量
     */
    private Integer userCouponCount;

    /**
     * 会员金额展示图片
     */
    private String memberAmountUrl;
    /**
     * 会员不带金额图片
     */
    private String memberShipUrl;

    /**
     * 我的证书数量
     */
    private Long certificateNum;
    /**
     * 道具橱窗数量
     */
    private Long userPropBagNum;

    //会员类型 0：非付费，1：付费会员
    private Integer memberType;

    //会员过期时间
    private ZonedDateTime memberExpireTime;
    /**
     * 当前经验值
     */
    private Integer currentExp;

    /**
     * 历史经验值
     */
    private Integer historyExp;
    /**
     * 未支付订单数
     */
    private Long noPayOrderNum;

    /**
     * 在游戏中
     */
    private Integer inGame;


    /**
     * 我的数量
     */
    private Long myInteractionNum;


    /**
     * 我的俱乐部列表
     * @since 4.7.0
     */
    private List<ClubInfoDto> myClubList;

    /**
     * 我的社区发帖列表
     * @since 4.7.0
     */
    private List<AppCommunityContentVo> myCommunityList;


    /**
     * 头像框
     * @since 4.6.5
     */
    private String avatarFrame;
    /**
     * 用户身份类型
     * @see UserIdentityEnum
     * @since 4.6.5
     */
    @Filler(filler = KocIdentityTypeDataFiller.class, relationFieldName = "userId")
    private String identityType;
    /**
     * 用户认证标识
     * @since 4.6.5
     */
    @Filler(filler = KocIdentityCredentialDataFiller.class, relationFieldName = "userId")
    private String identityCredential;
}
