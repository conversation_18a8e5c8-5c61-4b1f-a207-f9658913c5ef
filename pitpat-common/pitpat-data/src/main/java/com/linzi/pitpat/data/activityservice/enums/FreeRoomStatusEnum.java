package com.linzi.pitpat.data.activityservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免费房间状态枚举
 * 
 * @since 2025年1月
 */
@Getter
@AllArgsConstructor
public enum FreeRoomStatusEnum {
    
    /**
     * 等待中
     */
    WAITING("WAITING", "等待中"),
    
    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FreeRoomStatusEnum getByCode(String code) {
        for (FreeRoomStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 