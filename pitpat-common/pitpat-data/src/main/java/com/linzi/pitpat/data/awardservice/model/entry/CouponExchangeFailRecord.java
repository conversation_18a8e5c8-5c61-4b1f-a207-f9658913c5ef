package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户优惠券兑换失败记录表
 *
 * <AUTHOR>
 * @since 2023-11-03
 */

@Data
@NoArgsConstructor
@TableName("zns_coupon_exchange_fail_record")
public class CouponExchangeFailRecord implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.CouponExchangeFailRecord:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                         // 主键
    public final static String gmt_create = CLASS_NAME + "gmt_create";          // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";      // 最后修改时间
    public final static String is_delete = CLASS_NAME + "is_delete";            // 是否删除
    public final static String user_id = CLASS_NAME + "user_id";                // 用户id
    public final static String coupon_id = CLASS_NAME + "coupon_id";            // 优惠券id
    public final static String coupon_title = CLASS_NAME + "coupon_title";      // 优惠券名称
    public final static String exchange_code = CLASS_NAME + "exchange_code";    // 用户输入兑换码
    public final static String fail_code = CLASS_NAME + "fail_code";            // 失败原因：0-其他原因,1-优惠劵过期 ,2-优惠劵失效,3-优惠劵库存不足,4-优惠劵发放失败
    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //是否删除
    private Integer isDelete;
    //用户id
    private Long userId;
    //优惠券id
    private Long couponId;
    //优惠券名称
    private String couponTitle;
    //用户输入兑换码
    private String exchangeCode;
    //失败原因：0-其他原因,1-优惠劵过期 ,2-优惠劵失效,3-优惠劵库存不足,4-优惠劵发放失败
    private Integer failCode;

    @Override
    public String toString() {
        return "CouponExchangeFailRecord{" +
                ",id=" + id +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",isDelete=" + isDelete +
                ",userId=" + userId +
                ",couponId=" + couponId +
                ",couponTitle=" + couponTitle +
                ",exchangeCode=" + exchangeCode +
                ",failCode=" + failCode +
                "}";
    }
}
