package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.WearConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户服装背包
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

@Data
@Builder
@TableName("zns_user_wears_bag")
@AllArgsConstructor
@NoArgsConstructor
public class UserWearsBag implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                           // 主键ID
    public final static String wear_type = CLASS_NAME + "wear_type";              // 获取的服装类型: 1:发色（头发、帽子）、2:肤色、3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子
    public final static String wear_name = CLASS_NAME + "wear_name";              // 服装名称
    public final static String wear_value = CLASS_NAME + "wear_value";            // 服装ID
    public final static String wear_image_url = CLASS_NAME + "wear_image_url";    // 服装图片URL
    public final static String expired_time = CLASS_NAME + "expired_time";        // 失效时间
    public final static String status_ = CLASS_NAME + "status";                   // 状态： 0:未过期，1:已过期 2:待领取
    public final static String is_new = CLASS_NAME + "is_new";                    // 新标识 0: 否, 1: 是
    public final static String user_id = CLASS_NAME + "user_id";                  // 用户id
    public final static String activity_id = CLASS_NAME + "activity_id";          // 发放奖励的时候对应的活动id
    public final static String milepost_ = CLASS_NAME + "milepost";               // 里程id
    public final static String is_delete = CLASS_NAME + "is_delete";              // 是否删除（0否 1是）
    public final static String gmt_create = CLASS_NAME + "gmt_create";            //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";        //
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 获取的服装类型: 1:发色（头发、帽子）, 2:肤色, 3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子, 8:套装，9:背部服饰，10：动作
     *
     * @see WearConstant.WearTypeEnum
     */
    private Integer wearType;
    //服装名称
    private String wearName;
    //服装ID
    private Integer wearValue;
    //服装图片URL
    private String wearImageUrl;
    //失效时间
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private ZonedDateTime expiredTime;
    //状态： 0:未过期，1:已过期 2:待领取
    private Integer status;
    //新标识 0: 否, 1: 是
    private Integer isNew;
    //用户id
    private Long userId;
    //发放奖励的时候对应的活动id
    private Long activityId;
    //里程id
    private Integer milepostId;
    //是否删除（0否 1是）
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    /**
     * 奖励来源，0：活动奖励，1：活动进阶奖励, 2：积分兑换 40 衍生品绑定
     *
     * @see WearConstant.SourceTypeEnum
     */
    private Integer source;

    @Override
    public String toString() {
        return "UserWearsBag{" +
                ",id=" + id +
                ",wearType=" + wearType +
                ",wearName=" + wearName +
                ",wearValue=" + wearValue +
                ",status=" + status +
                ",isNew=" + isNew +
                ",userId=" + userId +
                ",activityId=" + activityId +
                ",milepost=" + milepostId +
                "}";
    }
}
