package com.linzi.pitpat.data.awardservice.model.resp;

import com.linzi.pitpat.data.activityservice.model.query.award.MedalAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponSendDetailVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.List;

/**
 *
 */
@Data
@NoArgsConstructor
@ToString
public class UserCouponSendBatchDetailResp {

    /**
     * 任务标题
     */
    @Size(max = 20, message = "标题过长")
    private String title;
    /**
     * 券ids
     */
    private String couponIds;
    //卷 明细列表
    private List<UserCouponSendDetailVo> couponLists;
    /**
     * 删除 1 删除 0 不删除
     */
    private Integer isDelete;
    /**
     * 状态 【 0: 未生效 1 生效 -1 失效】
     */
    private Integer status;
    //推送用户(，分割)
    private String pushUserEmails;
    //发放积分数
    private Integer score;
    //发放服装
    private List<WearAwardDto> wears;
    //发放勋章
    private List<MedalAwardDto> medalIds;
    //会员发放类型，0:有期限，1：永久
    private Integer vipSendType;
    //会员天数
    private Integer vipDays;
    //excel文件地址
    private String fileUrl;
    //发送人数
    private Integer sendCount;
    //目标人数
    private Integer targetCount;
    //发送时间
    private ZonedDateTime gmtSend;
    //发送状态
    private Integer sendStatus;
}
