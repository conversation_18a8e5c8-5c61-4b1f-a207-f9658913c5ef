package com.linzi.pitpat.data.activityservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityBrandInterestsBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityParamsBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityResultBusiness;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserAwardBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveUserScoreBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RankActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RunCheatBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.TeamShareTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.pro.ProActivityType;
import com.linzi.pitpat.data.activityservice.dto.RecordBreakingTryDto;
import com.linzi.pitpat.data.activityservice.dto.RecordBreakingUserActivityDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.AwardSendRecalculateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.AwardSendStageRecalculateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.ChangeCheatRequestDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserStageDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserTeamDto;
import com.linzi.pitpat.data.activityservice.model.dto.LevelPercentDto;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.dto.UserPropRecordDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityImpracticalAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.CheatDataDto;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveScoreConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.PlacementLevelScoreRuleDo;
import com.linzi.pitpat.data.activityservice.model.entity.PlacementMileageScoreRuleDo;
import com.linzi.pitpat.data.activityservice.model.entity.PlacementMileageSpeedRuleDo;
import com.linzi.pitpat.data.activityservice.model.entity.PlacementScoreCalcConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityDo;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityTimelineDo;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityStageUser;
import com.linzi.pitpat.data.activityservice.model.entity.RunTemplateDo;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.TeamEffectiveGrade;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.NoEndRunDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.query.PlacementMileageSpeedRuleQuery;
import com.linzi.pitpat.data.activityservice.model.query.PlacementScoreCalcConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.RankedUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityStageUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.request.ActivityIdRequest;
import com.linzi.pitpat.data.activityservice.model.resp.PlacementV2SpeedConfig;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityGoalRankVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityTeamUserRankVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityUserRankVo;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityUserQuery;
import com.linzi.pitpat.data.activityservice.query.PropRankedUserQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityImpracticalAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveScoreConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayAwardStageConfigService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.PlacementLevelScoreRuleService;
import com.linzi.pitpat.data.activityservice.service.PlacementMileageScoreRuleService;
import com.linzi.pitpat.data.activityservice.service.PlacementMileageSpeedRuleService;
import com.linzi.pitpat.data.activityservice.service.PlacementScoreCalcConfigService;
import com.linzi.pitpat.data.activityservice.service.ProActivityService;
import com.linzi.pitpat.data.activityservice.service.ProActivityTimelineService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.RecordBreakingService;
import com.linzi.pitpat.data.activityservice.service.RunActivityStageUserService;
import com.linzi.pitpat.data.activityservice.service.RunTemplateService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SeriesGameplayService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.TeamEffectiveGradeService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.activityservice.strategy.TeamAwardCalcStrategy;
import com.linzi.pitpat.data.activityservice.strategy.TeamAwardCalcStrategyFactory;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.RankedConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategy;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategyFactory;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubRunDataService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.FetchRuleTypeEnum;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.constants.RankingByEnum;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.entity.award.ActivityUserAwardPre;
import com.linzi.pitpat.data.entity.award.ActivityUserAwardReviewLog;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.GameplayAwardStageEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillWhiteListService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.service.award.ActivityUserAwardPreService;
import com.linzi.pitpat.data.service.award.ActivityUserAwardReviewLogService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventSourceEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelDo;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelLogDo;
import com.linzi.pitpat.data.userservice.model.entity.UserPushToken;
import com.linzi.pitpat.data.userservice.model.entity.UserPushToken;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelQuery;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelLogService;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelService;
import com.linzi.pitpat.data.userservice.service.UserPushTokenService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 活动赛事结果处理类
 *
 * <AUTHOR>
 * @date 2023/12/4 10:40
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ActivityResultManager {
    private static final List<Integer> awardTypeOrder = Arrays.asList(AwardTypeEnum.AMOUNT.getType(),
            AwardTypeEnum.SCORE.getType(), AwardTypeEnum.WEAR.getType(), AwardTypeEnum.MEDAL.getType());
    private final ActivityParamsService activityParamsService;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserService userService;
    private final EntryGameplayService entryGameplayService;
    private final SeriesGameplayService seriesGameplayService;
    private final ZnsUserRunRecordService userRunRecordService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final AppMessageService appMessageService;
    private final RedissonClient redissonClient;
    private final UserPushTokenService userPushTokenService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final GameplayAwardStageConfigService gameplayAwardStageConfigService;
    private final ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    private final AwardConfigService awardConfigService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsUserAccountService userAccountService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    private final PropRankedActivityResultManager propRankedActivityResultManager;
    private final ActivityTeamService activityTeamService;
    private final UserPropRecordService userPropRecordService;
    private final ZnsUserRunDataDetailsService runDataDetailsService;
    private final UserCouponService userCouponService;
    private final AwardConfigAmountService awardConfigAmountService;
    private final TeamEffectiveGradeService teamEffectiveGradeService;
    private final RabbitTemplate rabbitTemplate;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ActivityImpracticalAwardConfigService activityImpracticalAwardConfigService;
    private final ActivityUserAwardReviewLogService activityUserAwardReviewLogService;

    private final RedisTemplate redisTemplate;
    private final ActivityUserAwardPreService activityUserAwardPreService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final UserTaskDetailService userTaskDetailService;
    private final MainActivityBizService mainActivityBizService;
    private final ActivityUserAwardBizService activityUserAwardBizService;
    private final ActivityBrandInterestsBizService activityBrandInterestsBizService;
    private final ActivityUserBizService activityUserBizService;
    private final CompetitiveUserScoreBizService competitiveUserScoreBizService;
    private final AwardActivityBizService awardActivityBizService;
    private final RankActivityBizService rankActivityBizService;
    private final AwardConfigBizService awardConfigBizService;
    private final ActivityParamsBizService activityParamsBizService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ActivityStageService activityStageService;
    private final ActivityStageBusiness activityStageBusiness;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final ZnsRunActivityService runActivityService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final RunActivityStageUserService runActivityStageUserService;
    private final ActivityTeamManager activityTeamManager;
    private final ClubRunDataService clubRunDataService;
    private final ClubActivityTeamService clubActivityTeamService;
    private final ActivityResultBusiness activityResultBusiness;
    private final RunCheatBizService runCheatBizService;
    private final CompetitiveSeasonService competitiveSeasonService;
    private final RecordBreakingService recordBreakingService;
    private final ProActivityService proActivityService;
    private final ISysConfigService sysConfigService;
    private final PlacementMileageSpeedRuleService placementMileageSpeedRuleService;
    private final PlacementMileageScoreRuleService placementMileageScoreRuleService;
    private final PlacementScoreCalcConfigService placementScoreCalcConfigService;
    private final UserPlacementLevelService userPlacementLevelService;
    private final UserPlacementLevelLogService userPlacementLevelLogService;
    private final PlacementLevelScoreRuleService placementLevelScoreRuleService;

    private final AppProActivityCardManager appProActivityCardManager;
    private final ProActivityTimelineService proActivityTimelineService;
    private final PropRunRankedActivityUserService propRunRankedActivityUserService;
    private final MedalConfigService medalConfigService;
    private final RunTemplateService runTemplateService;
    private final TreadmillWhiteListService treadmillWhiteListService;
    private final WearsService wearsService;
    private final QueueMessageService queueMessageService;
    /**
     * 段位赛结算延迟队列
     */
    @Value("${" + RabbitQueueConstants.RANKED_AWARD_SETTLE_DELAY_EXCHANGE + "}")
    private String ranked_award_settle_delay_exchange;

    /**
     * 段位赛结算延迟队列
     */
    @Value("${" + RabbitQueueConstants.PROP_RANKED_AWARD_SETTLE_DELAY_EXCHANGE + "}")
    private String prop_ranked_award_settle_delay_exchange;
    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    /**
     * 跑步结束
     *
     * @param userRunDataDetail
     * @param activityTypeDto
     * @param needLock
     * @param ignoreActivityStatus
     */
    public void runEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityTypeDto, Boolean needLock, Boolean isClubEvent, boolean ignoreActivityStatus) {
        String spanId = MDC.get("spanId");
        MDC.put("spanId", spanId + "_" + userRunDataDetail.getId());
        log.info("runEnd come,detail:{},activityTypeDto:{}", userRunDataDetail, activityTypeDto);

        if (userRunDataDetail.getRunTime() < 60) {
            log.info("runEnd 运动时间小于60s,detail:{}", userRunDataDetail.getId());
            return;
        }

        String lockKey = RedisConstants.RUN_DATA_END_ACTIVITY_RESULT + userRunDataDetail.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean tryLock = false;
        String batchNo = OrderUtil.getBatchNo();

        try {
            if (needLock) {
                tryLock = lock.tryLock(1, 3600, TimeUnit.SECONDS);
                log.info("runEnd 获取锁");
                if (!tryLock) {
                    log.info("runEnd 获取锁失败");
                    return;
                }
                log.info("runEnd 获取锁成功,runDataDetailsId={}", userRunDataDetail.getId());
            }
            //发布tb跑步结束增加运动里程时长事件
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(),new TurbolinkApplicationEvent(TurbolinkEventEnum.RUN, userRunDataDetail.getUserId(),
                    Map.of("mileage", userRunDataDetail.getRunMileage(), "time", userRunDataDetail.getRunTime()), TurbolinkEventSourceEnum.RUNEND));
            boolean isComplete = false;
            if (Objects.nonNull(activityTypeDto) && activityTypeDto.getId() > 1) {
                //用户赛
                ZnsRunActivityUserEntity userEventActivityUser = runActivityUserService.findActivityUser(activityTypeDto.getId(), userRunDataDetail.getUserId());
                if (Objects.nonNull(userEventActivityUser)) {
                    isComplete = Objects.equals(userEventActivityUser.getIsComplete(), 1);
                }
                //俱乐部用户赛事，活动绑定
                if (isClubEvent) {
                    //俱乐部用户赛关联主活动ID
                    MainRunActivityRelationDo relationDo = mainRunActivityRelationService.findByQuery(MainRunActivityRelationQuery.builder().runActivityId(activityTypeDto.getId()).build());
                    activityTypeDto = runActivityService.getActivityNew(relationDo.getMainActivityId());
                }
                log.info("是否完赛：{},是否俱乐部用户赛：{},activityTypeDto:{}", isComplete, isClubEvent, activityTypeDto);
            }
            List<ActivityTypeDto> list = new ArrayList<>();
            if (Objects.nonNull(activityTypeDto)) {
                list.add(activityTypeDto);
            }
            ZnsUserEntity user = userService.findById(userRunDataDetail.getUserId());
            //查询所有数据来源活动
            List<ActivityTypeDto> proceedActivityList = mainActivityService.findProceedActivity(userRunDataDetail.getUserId(), user.getZoneId(), 2, userRunDataDetail.getDeviceType());
            if (!CollectionUtils.isEmpty(proceedActivityList)) {
                list.addAll(proceedActivityList);
            }
            if (CollectionUtils.isEmpty(list)) {
                log.info("runEnd end,没有需要处理的活动数据");
                return;
            }
            List<Long> excutedList = new ArrayList<>();

            for (ActivityTypeDto dto : list) {
                if (excutedList.contains(dto.getId())) {
                    log.info("已经处理过了,activityId={}", dto.getId());
                    continue;
                }
                excutedList.add(dto.getId());
                log.info("runEnd activity start,activityId={}", dto.getId());
                if (MainActivityTypeEnum.OLD.getType().equals(dto.getMainType())) {
                    log.info("runEnd end,mainType is old");
                    continue;
                }
                if (!MainActivityStateEnum.STARTED.getCode().equals(dto.getActivityState()) && !ignoreActivityStatus) {
                    log.info("runEnd end,activity is not started,id{},state{}", dto.getId(), dto.getActivityState());
                    continue;
                }


                //查询activityUser
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(dto.getId(), user.getId());
                if (Objects.isNull(activityUser)) {
                    log.info("runEnd end,用户未参赛");
                    continue;
                }
                MainActivity mainActivity = mainActivityService.findById(dto.getId());
                EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
                try {
                    if (Objects.isNull(entryGameplay)) {
                        log.error("runEnd end,entryGameplay 不存在");
                        continue;
                    }
                    //targetType补充
                    if (Objects.isNull(dto.getTargetType())) {
                        dto.setTargetType(entryGameplay.getTargetType());
                    }

                    if (dto.getTargetType() == 0) {
                        //无目标处理
                        noTargetDataDeal(userRunDataDetail, user, dto, activityUser, mainActivity, entryGameplay, batchNo);
                    } else {
                        //有目标处理
                        hasTargetDataDeal(userRunDataDetail, user, dto, activityUser, mainActivity, entryGameplay, batchNo);
                    }
                    //更新团队成绩
                    addTeamGrade(mainActivity, activityUser, userRunDataDetail, isComplete, entryGameplay, ignoreActivityStatus);
                } catch (Exception e) {
                    log.error("runEnd error,e=", e);
                }
            }

            propRankedActivityResultManager.updateRankedActivityUserHideScore(userRunDataDetail);
            rankActivityBizService.updateRankedActivityUserHideScore(userRunDataDetail);
        } catch (Exception e) {
            log.error("runEnd error,e=", e);
            if (tryLock) {
                lock.unlock();
            }
        }
        MDC.put("spanId", spanId);
    }

    /**
     * 活动结束
     *
     * @param mainActivity
     * @param needLock
     */
    public boolean activityEnd(MainActivity mainActivity, Boolean needLock) {
        String spanId = MDC.get("spanId");
        MDC.put("spanId", spanId + "_" + mainActivity.getId());
        String lockKey = RedisConstants.ACTIVITY_DATA_END_ACTIVITY_RESULT + mainActivity.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean tryLock = false;
        try {
            if (needLock) {
                tryLock = lock.tryLock(1, 3600, TimeUnit.SECONDS);
                if (!tryLock) {
                    log.info("activityEnd getLock 失败");
                    return false;
                }
            }

            //取消聚合标记
            if (MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType())) {
                log.info("取消聚合标记");
                mainActivityService.removePolymerizationMark(Arrays.asList(mainActivity.getId()));
            }
            activityEndSendAward(mainActivity, null, false);
        } catch (Exception e) {
            log.error("runEnd error,e=", e);
            if (tryLock) {
                lock.unlock();
            }
            return false;
        }
        MDC.put("spanId", spanId);
        return true;
    }

    /**
     * 活动结束奖励下发
     *
     * @param mainActivity
     * @param cheatUserIdList
     * @param isReview
     */
    public void activityEndSendAward(MainActivity mainActivity, List<Long> cheatUserIdList, boolean isReview) {
        //用户排名
        if (MainActivityTypeEnum.SINGLE.getType().equals(mainActivity.getMainType())
                || MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(mainActivity.getMainType())
                || MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType())) {
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            if (entryGameplay.getCompetitionFormat() == 0) {
                singleActivityEnd(mainActivity, entryGameplay, cheatUserIdList);
            } else {
                //团队成绩更新
                singleTeamActivityEnd(mainActivity, cheatUserIdList);
            }
        } else if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            seriesActivityEnd(mainActivity, cheatUserIdList, isReview);
        } else if (MainActivityTypeEnum.isFreeChallengeActivity(mainActivity.getMainType())) {
            freeChallengeActivityEnd(mainActivity);
        }
        sendCompetitiveScore(mainActivity);
        //发卡片的。
        proActivityEnd(mainActivity);
    }

    /**
     * 职业赛结束
     *
     * @param mainActivity
     */
    public void proActivityEnd(MainActivity mainActivity) {
        if (!proActivityService.isProActivity(mainActivity.getId())) {
            return;
        }
        //下发卡片，更新 评级数据
        Optional<ProActivityDo> byMainActivityId = proActivityService.findByMainActivityId(mainActivity.getId());
        if (byMainActivityId.isPresent() && ProActivityType.isPro(byMainActivityId.get().getProActivityType())) {
            log.info("职业赛结束，开始发卡片，更新 评级数据,mainActivityId:{}", mainActivity.getId());
            ProActivityTimelineDo yearByActivityStartTime = proActivityTimelineService.findYearByActivityStartTime(mainActivity.getActivityStartTime());
            if (yearByActivityStartTime == null) {
                log.info("职业赛结束，未找到对应的年份,mainActivityId:{}", mainActivity.getId());
                return;
            }
            ProActivityDo proActivityDo = byMainActivityId.get();
            boolean sendCard = Integer.valueOf(1).equals(proActivityDo.getIsSendProActivityCard());
            Integer sendProActivityCardCount = proActivityDo.getSendProActivityCardCount();
            long pageIndex = 1L;
            long pageSize = 100L;
            Page<ZnsRunActivityUserEntity> pageActivityCompleteUser = runActivityUserService.findPageHaveRankUser(mainActivity.getId(), pageIndex++, pageSize);
            while (!CollectionUtils.isEmpty(pageActivityCompleteUser.getRecords())) {
                List<ZnsRunActivityUserEntity> records = pageActivityCompleteUser.getRecords();

                for (ZnsRunActivityUserEntity znsRunActivityUserEntity : records) {
                    if (znsRunActivityUserEntity.getRank() != -1) {
                        if (sendCard && sendProActivityCardCount > 0) {
                            try {
                                ProActivityType proActivityType = byMainActivityId.get().getProActivityType();
                                ProActivityType next = ProActivityType.next(proActivityType.getCode());
                                if (!ProActivityType.NONE.equals(next)) {
                                    Boolean sendStatus = appProActivityCardManager.sendCard(znsRunActivityUserEntity.getUserId(),
                                            mainActivity.getId(),
                                            proActivityDo.getProActivityType(),
                                            yearByActivityStartTime.getYear()
                                    );
                                    if (sendStatus) {
                                        sendProActivityCardCount--;
                                    }
                                }
                            } catch (BizException e) {
                                log.info("赛事卡发放失败:{},{}", mainActivity.getId(), e);
                            }
                        }
                        //todo 活动结束评级
                        calcPlacementScoreAndSaveUserPlacementScore(mainActivity, znsRunActivityUserEntity);
                    }
                }

                pageActivityCompleteUser = runActivityUserService.findPageHaveRankUser(mainActivity.getId(), pageIndex++, pageSize);
            }
        }
    }

    /**
     *
     * @param mainActivity
     * @param znsRunActivityUserEntity
     */
    public void calcPlacementScoreAndSaveUserPlacementScore(MainActivity mainActivity, ZnsRunActivityUserEntity znsRunActivityUserEntity) {
        var detailsServiceById = runDataDetailsService.findById(znsRunActivityUserEntity.getRunDataDetailsId());
        ZnsUserEntity user = userService.findById(znsRunActivityUserEntity.getUserId());
        BigDecimal score = calcPlacementScore(mainActivity, znsRunActivityUserEntity, detailsServiceById, user);
        saveUserPlacementScore(score,mainActivity,user,detailsServiceById);
    }

    private void freeChallengeActivityEnd(MainActivity mainActivity) {
        //获取奖励配置
        List<AwardConfigDto> awardConfigDtoList = activityAwardConfigService.selectAwardConfigDtoListBySendTypes(mainActivity.getId(), Collections.singletonList(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()), null);
        Map<Integer, List<AwardConfigDto>> rankAwardMap = awardConfigDtoList.stream().filter(Objects::nonNull).flatMap(dto -> IntStream.rangeClosed(dto.getRank(), dto.getRankMax()).mapToObj(rank -> new AbstractMap.SimpleEntry<>(rank, dto)))
                .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        FreeActivityConfig activityConfig = activityParamsService.findCacheOne(mainActivity.getId(), ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
        if (FreeActivityModeEnum.COMPETE.getCode().equals(activityConfig.getMode())) {
            //判断活动竞速类型
            RunActivityUserQuery query = RunActivityUserQuery.builder().activityId(mainActivity.getId()).minRank(0).isComplete(1).build();
            query.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_COMPETE);
            List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(query);
            if (!CollectionUtils.isEmpty(list)) {
                for (int i = 0; i < list.size(); i++) {
                    String batchNo = OrderUtil.getBatchNo();
                    ZnsRunActivityUserEntity activityUser = list.get(i);
                    activityUser.setRank(i + 1);
                    activityUser.setRewardTime(ZonedDateTime.now());
                    log.info("总榜重新排名，用户：{}，活动id：{}，成绩ms：{}，重新排名为：{}", activityUser.getUserId(), mainActivity.getId(), activityUser.getRunTimeMillisecond(),i + 1);
                    //排名奖励
                    AwardSendDto awardSendDto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId())
                            .setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(activityUser.getRank()).setTotalBatchNo(batchNo)
                            .setTarget(activityUser.getTargetRunTime() > 0 ? activityUser.getTargetRunTime() : activityUser.getTargetRunMileage());
                    awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);
                    //奖励推送
                    freeChallengeActivityRankPush(activityUser.getUserId(), activityUser.getRank(), activityUser.getActivityId(), rankAwardMap.get(activityUser.getRank()));
                }
                runActivityUserService.updateBatchById(list);
            }
            // 查询未上榜用户
            List<ZnsRunActivityUserEntity> noTopList = runActivityUserService.findList(RunActivityUserQuery.builder().activityId(mainActivity.getId()).maxRank(-1).isComplete(0).build());
            if (!CollectionUtils.isEmpty(noTopList)) {
                for (int i = 0; i < noTopList.size(); i++) {
                    ZnsRunActivityUserEntity activityUser = noTopList.get(i);
                    freeChallengeActivityRankPush(activityUser.getUserId(), activityUser.getRank(), activityUser.getActivityId(), null);
                }
            }
        } else {
            //道具模式
            PropRankedActivityUserQuery query = new PropRankedActivityUserQuery().setIsComplete(1).setActivityId(mainActivity.getId());
            query.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_PROP);
            List<PropRunRankedActivityUser> list = propRunRankedActivityUserService.findList(query);
            if (!CollectionUtils.isEmpty(list)) {
                for (int i = 0; i < list.size(); i++) {
                    String batchNo = OrderUtil.getBatchNo();
                    PropRunRankedActivityUser activityUser = list.get(i);
                    activityUser.setRank(i + 1);
                    activityUser.setAwardSendStatus(RankedConstant.AwardSendEnum.AWARD_SEND_2.code);
                    log.info("总榜重新排名，用户：{}，活动id：{}，成绩：{}，重新排名为：{}", activityUser.getUserId(), mainActivity.getId(), activityUser.getRunTimeMils(),i + 1);
                    //排名奖励
                    AwardSendDto awardSendDto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId())
                            .setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(activityUser.getRank()).setTotalBatchNo(batchNo)
                            .setTarget(activityUser.getTargetRunTime() > 0 ? activityUser.getTargetRunTime() : activityUser.getTargetRunMileage());
                    awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);
                    freeChallengeActivityRankPush(activityUser.getUserId(), activityUser.getRank(), activityUser.getActivityId(), rankAwardMap.get(activityUser.getRank()));
                }
                propRunRankedActivityUserService.updateBatchById(list);
            }
            // 查询未上榜用户
            List<PropRunRankedActivityUser> noTopList = propRunRankedActivityUserService.findList(new PropRankedActivityUserQuery().setRankLt(0).setActivityId(mainActivity.getId()));
            if (!CollectionUtils.isEmpty(noTopList)) {
                for (int i = 0; i < noTopList.size(); i++) {
                    PropRunRankedActivityUser activityUser = noTopList.get(i);
                    freeChallengeActivityRankPush(activityUser.getUserId(), activityUser.getRank(), activityUser.getActivityId(), null);
                }
            }
        }



    }

    private void freeChallengeActivityRankPush(Long userId, Integer rank, Long activityId, List<AwardConfigDto> awardConfigDtos) {
        String message = "";
        if (Objects.nonNull(rank) && rank >= 0) {
            message = I18nMsgUtils.getMessage("free.challenge.activity.ranked.desc.onTop", rank, formatAwardValues(awardConfigDtos));
        } else {
            message = I18nMsgUtils.getMessage("free.challenge.activity.ranked.desc.noTop");
        }
        // 发送自定义消息
        ImMessageBo imMessageBo = new ImMessageBo();
        imMessageBo.setJumpType("0");
        imMessageBo.setJumpValue(mallH5Url+"/free-challenge/" + activityId + "?isCusNavBar=true&isLightStatusBarStyle=1");
        Map<String, Object> params = new HashMap<>();
        imMessageBo.setParams(params);
        imMessageBo.setMsg(message);
        imMessageBo.setBusinessID("system_notification");
        imMessageBo.setImageUrl(ActivityConstants.FREE_CHALLENGE_PUSH_IMAGE);
        String jsonStr = JsonUtil.writeString(imMessageBo);
        appMessageService.sendIm("administrator", Collections.singletonList(userId), jsonStr, TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
    }

    /**
     * 格式奖励消息
     * @param awardConfigDtos
     */
    private String formatAwardValues(List<AwardConfigDto> awardConfigDtos) {
        if (CollectionUtils.isEmpty(awardConfigDtos)) return "";
        Map<Integer, AwardConfigDto> dtoMap = awardConfigDtos.stream().collect(Collectors.toMap(AwardConfigDto::getAwardType, Function.identity(), (a, b) -> a));
        List<String> values = new ArrayList<>(awardTypeOrder.size());
        for (Integer type : awardTypeOrder) {
            AwardConfigDto awardConfigDto = dtoMap.get(type);
            if (awardConfigDto == null) {
                continue;
            }
            String valueStr = AwardTypeEnum.getValueStr(awardConfigDto);
            if (AwardTypeEnum.MEDAL.getType().equals(awardConfigDto.getAwardType())) {
                MedalConfig medalConfig = medalConfigService.selectMedalConfigById(awardConfigDto.getMedalId());
                valueStr = Objects.nonNull(medalConfig)?medalConfig.getName():"";
            } else if (AwardTypeEnum.WEAR.getType().equals(awardConfigDto.getAwardType())) {
                Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(awardConfigDto.getWearType()), awardConfigDto.getWearValue());
                if (Objects.nonNull(wear)) {
                    valueStr = wear.getWearName();
                }
            }

            if (StringUtils.hasText(valueStr)) {
                values.add(valueStr);
            }
        }
        if (values.isEmpty()) {
            return "";
        }
        String msg = String.join(" ", values);
        return I18nMsgUtils.getMessage("free.challenge.activity.ranked.desc.onTop.2", msg);
    }

    /**
     * 可以多次调用，内部进行了幂等操作。
     *
     * @param mainActivity
     */
    private void sendCompetitiveScore(MainActivity mainActivity) {
        if (mainActivity.isCompetitive()) {
            //发放竞技分
            try {
                competitiveUserScoreBizService.sendCompetitiveScore(mainActivity.getId());
            } catch (Exception e) {
                log.error("发放竞技分失败:{}", mainActivity.getId(), e);
            }
        }
    }


    /**
     * 系列赛活动结束
     *
     * @param mainActivity
     * @param cheatUserIdList
     * @param isReview
     */
    private void seriesActivityEnd(MainActivity mainActivity, List<Long> cheatUserIdList, boolean isReview) {
        log.info("seriesActivityEnd start");
        //查询子活动
        List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
        for (MainActivity activity : allMainActivity) {
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(activity.getPlayId());
            if (entryGameplay.getCompetitionFormat() == 0 && !isReview) {
                singleActivityEnd(activity, entryGameplay, null);
            } else {
                //团队成绩更新
                singleTeamActivityEnd(activity, null);
            }
        }
        List<ZnsRunActivityUserEntity> allActivityUserList = runActivityUserService.findAllActivityUser(mainActivity.getId());
        if (CollectionUtils.isEmpty(allActivityUserList)) {
            log.info("seriesActivityEnd end，用户为空");
            return;
        }

        //过滤手动标记作弊用户
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            allActivityUserList = allActivityUserList.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
            cheatUserIdList.forEach(i -> {
                ZnsRunActivityUserEntity runActivityUserEntity = runActivityUserService.findActivityUserWithNoState(i, mainActivity.getId());
                if (Objects.nonNull(runActivityUserEntity)) {
                    runActivityUserEntity.setRank(-1);
                    runActivityUserService.updateById(runActivityUserEntity);
                }
            });
        }

        SeriesGameplay seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());

        if (!StringUtils.hasText(seriesGameplay.getRankingBy())) {
            log.info("seriesActivityEnd end，没有排名依据");
            return;
        }

        List<Long> subActivityIds = allMainActivity.stream().map(MainActivity::getId).collect(Collectors.toList());
        //主活动数据处理
        for (ZnsRunActivityUserEntity activityUser : allActivityUserList) {
            List<ZnsRunActivityUserEntity> subActivityUserList = runActivityUserService.findActivityUsers(subActivityIds, activityUser.getUserId());
            if (CollectionUtils.isEmpty(subActivityUserList)) {
                activityUser.setAverageVelocity(BigDecimal.ZERO);
                activityUser.setRunMileage(BigDecimal.ZERO);
                activityUser.setRunTime(0);
                activityUser.setPropRunTime(0);
                activityUser.setCompleteTime(ZonedDateTime.now());
                continue;
            }

            updateSeriesActivityGrade(activityUser);
            if (Objects.isNull(activityUser.getPropRunTime())) {
                activityUser.setAverageVelocity(SportsDataUnit.getVelocity(activityUser.getRunTime(), activityUser.getRunMileage()));
            } else {
                activityUser.setAverageVelocity(SportsDataUnit.getVelocity(activityUser.getPropRunTime() / 1000, activityUser.getRunMileage()));
            }
        }

        //用户刷选
        if (2 == seriesGameplay.getRankingUser()) {
            allActivityUserList = allActivityUserList.stream().filter(a -> activityUserBizService.joinSeriesAllSegment(a.getActivityId(), a.getUserId())).collect(Collectors.toList());
        } else if (3 == seriesGameplay.getRankingUser()) {
            allActivityUserList = allActivityUserList.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
        }


        //用户排名当前单选
        if ("1".equals(seriesGameplay.getRankingBy())) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
        } else if ("2".equals(seriesGameplay.getRankingBy())) {
            if (2 == seriesGameplay.getRankingUser() || 1 == seriesGameplay.getRankingUser()) {
                allActivityUserList = allActivityUserList.stream().sorted((o1, o2) -> {
                    Integer propRunTime1 = o1.getPropRunTime();
                    Integer propRunTime2 = o2.getPropRunTime();
                    if (Objects.isNull(propRunTime1)) {
                        propRunTime1 = o1.getRunTimeMillisecond();
                    }
                    if (Objects.isNull(propRunTime2)) {
                        propRunTime2 = o2.getRunTimeMillisecond();
                    }
                    return propRunTime2 - propRunTime1;
                }).collect(Collectors.toList());
            } else {
                allActivityUserList = allActivityUserList.stream().sorted((o1, o2) -> {
                    Integer propRunTime1 = o1.getPropRunTime();
                    Integer propRunTime2 = o2.getPropRunTime();
                    if (Objects.isNull(propRunTime1)) {
                        propRunTime1 = o1.getRunTimeMillisecond();
                    }
                    if (Objects.isNull(propRunTime2)) {
                        propRunTime2 = o2.getRunTimeMillisecond();
                    }
                    return propRunTime1 - propRunTime2;
                }).collect(Collectors.toList());
            }
        } else if ("3".equals(seriesGameplay.getRankingBy())) {
            ZonedDateTime now = ZonedDateTime.now();
            allActivityUserList = allActivityUserList.stream().sorted((o1, o2) -> {
                ZonedDateTime o1CompleteTime = o1.getCompleteTime();
                ZonedDateTime o2CompleteTime = o2.getCompleteTime();
                if (Objects.isNull(o1CompleteTime) || o1.getIsComplete() == 0) {
                    o1CompleteTime = DateUtil.addDays(now, 1);
                }
                if (Objects.isNull(o2CompleteTime) || o1.getIsComplete() == 0) {
                    o2CompleteTime = DateUtil.addDays(now, 1);
                }
                return o1CompleteTime.compareTo(o2CompleteTime);
            }).collect(Collectors.toList());
        } else if ("4".equals(seriesGameplay.getRankingBy())) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getAverageVelocity).reversed()).collect(Collectors.toList());
        }
        log.info("seriesActivityEnd end，rankingUser={}，rankingBy={}", seriesGameplay.getRankingUser(), seriesGameplay.getRankingBy());
        Long count = allActivityUserList.stream().filter(user -> user.getIsComplete() == 1).count();
        for (int i = 0; i < allActivityUserList.size(); i++) {
            ZnsRunActivityUserEntity runActivityUser = allActivityUserList.get(i);
            runActivityUser.setRank(i + 1);
            runActivityUserService.updateById(runActivityUser);
            String batchNo = OrderUtil.getBatchNo();

            //奖励发放
            AwardSendDto awardSendDto = new AwardSendDto().setActivityId(runActivityUser.getActivityId()).setUserId(runActivityUser.getUserId())
                    .setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(runActivityUser.getRank()).setTarget(0).setTotalBatchNo(batchNo);
            // 团队赛排名奖励审核处理 内部已处理这里不处理
            awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);

            if (runActivityUser.getIsComplete() == 1) {
                AwardSendDto dto = new AwardSendDto(runActivityUser.getActivityId(), AwardSentTypeEnum.COMPLETING_THE_GAME.getType(),
                        0, 0, entryGameplay.getTargetType(), runActivityUser.getUserId()).setTotalBatchNo(batchNo).setDivideUserCount(count.intValue());
                awardActivityBizService.sendActivityAwardByConfigAndStage(dto);
            }
        }
        //破纪录奖励
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(entryGameplay.getGameplayId(), 1);
        sendRecordBreakingAward(stageList, entryGameplay.getRankingBy(), mainActivity);
        log.info("seriesActivityEnd end");
    }

    public void singleTeamActivityEnd(MainActivity mainActivity, List<Long> cheatUserIdList) {
        log.info("singleTeamActivityEnd start mainActivity:{}", mainActivity);
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(mainActivity.getId());
        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("singleTeamActivityEnd 无报名用户");
            return;
        }

        //无目标
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (mainActivity.getTargetType() == 0) {
            //更新团队成绩
            updateTeamGrade(mainActivity.getId(), entryGameplay.getRankingBy(), cheatUserIdList);
            //再查一遍，用户成绩可能被修改
            allActivityUser = runActivityUserService.findAllActivityUser(mainActivity.getId());
        } else {
            //更新有目标团队成绩
            updateGoalTeamGrade(mainActivity, entryGameplay, cheatUserIdList);
        }

        //过滤手动标记作弊用户
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            allActivityUser = allActivityUser.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
            cheatUserIdList.forEach(i -> {
                ZnsRunActivityUserEntity runActivityUserEntity = runActivityUserService.findActivityUserWithNoState(i, mainActivity.getId());
                if (Objects.nonNull(runActivityUserEntity)) {
                    runActivityUserEntity.setRank(-1);
                    runActivityUserService.updateById(runActivityUserEntity);
                }
            });
        }

        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("singleTeamActivityEnd 过滤手动标记作弊后用户无用户");
            return;
        }

        if (entryGameplay.getDataSource() == 6) {
            // 更新有目标并且是targetType = 3 的团队中个人的成绩
            updateUserPersonalGrade(mainActivity, allActivityUser);
        }

        //查询是否有排名奖励
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(mainActivity.getPlayId(), 2);
        if (CollectionUtils.isEmpty(stageList) || !stageList.contains(4)) {
            log.info("singleTeamActivityEnd 无奖励配置");
            return;
        }
        ZnsRunActivityEntity runActivity = new ZnsRunActivityEntity();
        runActivity.setId(mainActivity.getId());
        runActivity.setStatus(1);
        List<ActivityBrandRightsInterests> interestsList = activityBrandRightsInterestsService.findByActId(mainActivity.getId());
        if (!CollectionUtils.isEmpty(interestsList)) {
            ActivityBrandRightsInterests activityBrandRightsInterests = interestsList.stream().filter(i -> i.getRightsInterestsType() == 3).findFirst().orElse(null);
            if (Objects.nonNull(activityBrandRightsInterests)) {
                runActivity.setPrivilegeBrand(activityBrandRightsInterests.getBrand());
            }
        } else {
            runActivity.setPrivilegeBrand(-1);
        }


        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(mainActivity.getId());
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
        awardConfigDtos = awardConfigDtos.stream().filter(a ->
                AwardSentTypeEnum.RANKING_BASED_REWARDS.getType().equals(a.getSendType())
                        || AwardSentTypeEnum.RANKING_HEAD_REWARD.getType().equals(a.getSendType())
                        || AwardSentTypeEnum.RANKING_PERSONAL_AWARD.getType().equals(a.getSendType())
                        || AwardSentTypeEnum.PRESIDENT_AWARD.getType().equals(a.getSendType())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            log.info("singleTeamActivityEnd 无奖励配置");
            return;
        }

        Integer targetType = mainActivity.getTargetType();
        String rankingBy = entryGameplay.getRankingBy();
        //查询活动队伍
        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivity.getId());
        Map<Long, List<ZnsRunActivityUserEntity>> teamUserMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTeamId));
        for (ActivityTeam team : teams) {
            List<ZnsRunActivityUserEntity> activityUsers = teamUserMap.get(team.getId());
            //未完赛不发奖励
            if (mainActivity.getTargetType() != 0 && team.getCompleteTime() == null) {
                log.info("team{}未完赛", team.getId());
                // 团队用户赛 发个人奖励 其他奖励不发
                if (entryGameplay.getDataSource() == 6) {
                    String batchNo = OrderUtil.getBatchNo();
                    for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                        sendTeamPersonalRankAward(activityUser, batchNo, stageList);
                    }
                }
                continue;
            }
            if (mainActivity.getTargetType() == 0 && team.getRunTime() == 0) {
                log.info("team{} 无目标 无成绩", team.getId());
                continue;
            }
            BigDecimal totalMillage = BigDecimal.valueOf(team.getMillage());
            BigDecimal currentNum = BigDecimal.valueOf(team.getCurrentNum());
            //查询队伍成员成绩
            log.info("activityUsers {}", activityUsers);
            if (CollectionUtils.isEmpty(activityUsers)) {
                continue;
            }
            BigDecimal teamAward = BigDecimal.ZERO;
            for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                try {
                    log.info("开始发放用户{}活动{}奖励", activityUser.getUserId(), activityUser);
                    String batchNo = OrderUtil.getBatchNo();

                    Integer target = getActivityUserTarget(activityUser);
                    BigDecimal awardRatio = BigDecimal.ZERO;

                    BigDecimal userGrade = BigDecimal.ZERO;
                    BigDecimal teamGrade = BigDecimal.ZERO;

                    //计算有效成绩
                    if (targetType == 0) {
                        RankingByEnum rankingByEnum = RankingByEnum.findByType(rankingBy);
                        TeamEffectiveGrade teamEffectiveGrade = teamEffectiveGradeService.findByActivityTeamAndUser(mainActivity.getId(), team.getId(), activityUser.getUserId());
                        switch (rankingByEnum) {
                            case RUN_TIME:
                                userGrade = BigDecimal.valueOf(activityUser.getRunTime());
                                teamGrade = BigDecimal.valueOf(team.getRunTime());
                                break;
                            case RUN_MILEAGE:
                                userGrade = activityUser.getRunMileage();
                                teamGrade = BigDecimal.valueOf(team.getMillage());
                                break;
                            case RUN_COUNT:
                                userGrade = (Objects.nonNull(teamEffectiveGrade) && Objects.nonNull(teamEffectiveGrade.getRunCount())) ? new BigDecimal(teamEffectiveGrade.getRunCount()) : BigDecimal.ZERO;
                                teamGrade = BigDecimal.valueOf(team.getRunCount());
                                break;
                            case REACH_NUMS:
                                userGrade = (Objects.nonNull(teamEffectiveGrade) && Objects.equals(teamEffectiveGrade.getIsReach(), 1)) ? BigDecimal.ONE : BigDecimal.ZERO;
                                teamGrade = BigDecimal.valueOf(team.getReachNums());
                                break;
                        }
                    } else if (targetType == 1) {
                        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activityUser.getActivityId(), activityUser.getUserId());
                        if (Objects.nonNull(effectiveGrade)) {
                            userGrade = BigDecimal.valueOf(effectiveGrade.getRunMileage());
                        }
                        teamGrade = BigDecimal.valueOf(team.getMillage());
                    } else if (targetType == 2) {
                        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activityUser.getActivityId(), activityUser.getUserId());
                        if (Objects.nonNull(effectiveGrade)) {
                            userGrade = BigDecimal.valueOf(effectiveGrade.getRunTime());
                        }
                        teamGrade = BigDecimal.valueOf(team.getRunTime());
                    } else if (Objects.equals(targetType, 3)) {
                        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activityUser.getActivityId(), activityUser.getUserId());
                        userGrade = BigDecimal.valueOf(effectiveGrade.getRunCount());
                        teamGrade = BigDecimal.valueOf(team.getRunCount());
                        log.info("场次目标成绩 userGrade :{},teamGrade:{}", userGrade, teamGrade);
                    }

                    // 计算奖励比例
                    //为0不计算，避免0值异常
                    if (!BigDecimal.ZERO.equals(teamGrade)) {
                        awardRatio = userGrade.divide(teamGrade, 8, RoundingMode.HALF_UP);
                    }

                    log.info("用户成绩userGrade:{}，{},teamGrade{}awardRatio{} ", activityUser.getUserId(), userGrade, teamGrade, awardRatio);
                    Currency userCurrency = userAccountService.getUserCurrency(activityUser.getUserId());
                    ActivityImpracticalAwardConfig awardConfigServiceByActId = activityImpracticalAwardConfigService.findByActId(mainActivity.getId());
                    Integer teamAwardType = 0;
                    if (Objects.nonNull(awardConfigServiceByActId)) {
                        teamAwardType = awardConfigServiceByActId.getTeamAwardType();
                    }
                    //计算奖金
                    BigDecimal awardAmount = BigDecimal.ZERO;
                    BigDecimal baseReward = getAmountAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), userCurrency, team.getRank());
                    BigDecimal headReward = getAmountAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_HEAD_REWARD.getType(), userCurrency, team.getRank());
                    if (Objects.nonNull(baseReward) && baseReward.compareTo(BigDecimal.ZERO) > 0) {
                        awardAmount = awardAmount.add(baseReward);
                    }
                    if (Objects.nonNull(headReward) && headReward.compareTo(BigDecimal.ZERO) > 0) {
                        awardAmount = BigDecimalUtil.multiply(headReward, currentNum).add(awardAmount);
                    }
                    teamAward = awardAmount;
                    TeamAwardCalcStrategy strategy = TeamAwardCalcStrategyFactory.getStrategyByType(teamAwardType);
                    // 最终金额奖励
                    awardAmount = strategy.calcTeamAmountAward(awardAmount, awardRatio, activityUsers, activityUser);
                    // 计算积分
                    Integer awardScore = 0;
                    Integer baseScoreReward = getScoreAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), team.getRank());
                    Integer headScoreReward = getScoreAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_HEAD_REWARD.getType(), team.getRank());
                    log.info("teamID" + team.getId() + "baseScoreReward: " + baseScoreReward + " headScoreReward: " + headScoreReward);
                    if (Objects.nonNull(baseScoreReward) && baseScoreReward > 0) {
                        awardScore = awardScore + baseScoreReward;
                    }
                    if (Objects.nonNull(headScoreReward) && headScoreReward > 0) {
                        awardScore = headScoreReward * team.getCurrentNum() + awardScore;
                    }
                    // 最终积分奖励
                    awardScore = strategy.calcTeamScoreAward(awardScore, awardRatio, activityUsers, activityUser);

                    //权益处理
                    ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.RANK_REWARD.getStatusCode(), runActivity, activityUser.getUserId());
                    Integer rightsInterestsType = null;
                    Integer privilegeBrand = null;
                    BigDecimal rightsInterestsMultiple = null;

                    if (Objects.nonNull(brandRightsInterests)) {
                        rightsInterestsMultiple = brandRightsInterests.getMultiple();
                        rightsInterestsType = brandRightsInterests.getRightsInterestsType();
                        privilegeBrand = brandRightsInterests.getBrand();
                    }
                    if (activityParamsService.checkAwardSendType(mainActivity.getId())) {
                        MainActivity mainActivity1 = mainActivityService.findById(mainActivity.getId());
                        Integer awardSendStatus = mainActivity1.getAwardSendStatus();
                        log.info("开启人工审核奖开启team ：mainActivityId{},awardSendStatus:{}", mainActivity.getId(), awardSendStatus);
                        if (awardSendStatus == 0) {
                            AwardSendDto dto = new AwardSendDto().setActivityId(mainActivity.getId()).setTarget(target)
                                    .setUserId(activityUser.getUserId()).setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(team.getRank())
                                    .setTotalBatchNo(batchNo);
                            savePreTeamAward(dto);
                            continue;
                        }
                        if (awardSendStatus == 1) {
                            log.info("奖励发放已经结束");
                            continue;
                        }
                        if (awardSendStatus == 2) {
                            updatePreTeamAward(activityUser.getUserId(), activityUser.getActivityId());
                        }
                    }
                    // 给用户余额发送奖励
                    if (BigDecimal.ZERO.compareTo(awardAmount) != 0) {
                        // 切换币种不发放奖励判断,过滤金额奖励
                        List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                        if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
                            log.info("切换币种不发放奖励判断,过滤金额奖励");
                        } else {
                            log.info("singleTeamActivityEnd 金额发放");
                            if (Objects.nonNull(rightsInterestsMultiple)) {
                                awardAmount = awardAmount.multiply(rightsInterestsMultiple).setScale(2, RoundingMode.UP);
                            }
                            awardAmount = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), awardAmount);
                            log.info("用户awardAmount{}，{}", activityUser.getUserId(), awardAmount);
                            userAccountService.increaseAmount(awardAmount, activityUser.getUserId(), true);
                            // 新增用户奖励余额明细
                            String billNo = NanoId.randomNanoId();
                            ;
                            ZonedDateTime tradeTime = ZonedDateTime.now();
                            Long detailId = userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.NEW_ACTIVITY_100,
                                    1, 1, awardAmount, billNo, tradeTime,
                                    activityUser.getActivityId(), activityUser.getActivityId(), null, activityUser.getActivityType(),
                                    0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, BigDecimal.ZERO);

                            AwardSendDto dto = new AwardSendDto().setActivityId(mainActivity.getId()).setTarget(target)
                                    .setUserId(activityUser.getUserId()).setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(team.getRank())
                                    .setTotalBatchNo(batchNo);

                            activityUserAwardBizService.saveNew(dto, detailId, AwardTypeEnum.AMOUNT.getType(), batchNo, batchNo);
                        }
                    }

                    if (awardScore > 0) {
                        log.info("singleTeamActivityEnd 积分发放");
                        if (Objects.nonNull(rightsInterestsMultiple)) {
                            awardScore = new BigDecimal(awardScore).multiply(rightsInterestsMultiple).setScale(0, RoundingMode.UP).intValue();
                        }
                        log.info("用户awardScore{}，{}", activityUser.getUserId(), awardScore);
                        // 发放积分
                        Long detailId = activityUserScoreService.increaseAmount(awardScore, activityUser.getActivityId(), activityUser.getUserId(), team.getRank(), 0, ScoreConstant.SourceTypeEnum.source_type_100.getType() + 1);

                        AwardSendDto dto = new AwardSendDto().setActivityId(mainActivity.getId()).setTarget(target)
                                .setUserId(activityUser.getUserId()).setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(team.getRank())
                                .setTotalBatchNo(batchNo);
                        activityUserAwardBizService.saveNew(dto, detailId, AwardTypeEnum.SCORE.getType(), batchNo, batchNo);

                    }
                    if ((activityUser.getRunTime() > 0 && teamAwardType.equals(TeamShareTypeEnum.SHARE_RUN.getType()))
                            || (userGrade.compareTo(BigDecimal.ZERO) > 0 && teamAwardType.equals(TeamShareTypeEnum.NORMAL.getType()))
                            || teamAwardType.equals(TeamShareTypeEnum.SHARE_ALL.getType())) {
                        log.info("singleTeamActivityEnd 券发放");
                        AwardConfigDto awardConfigType1 = getAwardConfigType(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), AwardTypeEnum.COUPON.getType(), team.getRank());
                        awardActivityBizService.sendActivityAwardByConfig(awardConfigType1, new AwardSendDto().setActivityId(mainActivity.getId()).setTarget(target)
                                .setUserId(activityUser.getUserId()).setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(team.getRank())
                                .setTotalBatchNo(batchNo));

                        log.info("singleTeamActivityEnd 服装发放");
                        AwardConfigDto awardConfigType2 = getAwardConfigType(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), AwardTypeEnum.WEAR.getType(), team.getRank());
                        awardActivityBizService.sendActivityAwardByConfig(awardConfigType2, new AwardSendDto().setActivityId(mainActivity.getId()).setTarget(target)
                                .setUserId(activityUser.getUserId()).setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(team.getRank())
                                .setTotalBatchNo(batchNo));

                        if (Objects.isNull(awardConfigType1) && Objects.isNull(awardConfigType2)) {
                            // 无具体奖励发送完赛 勋章/证书
                            AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(1);
                            awardProcessStrategy.awardSendProcess(new ArrayList<>(), new AwardSendDto().setActivityId(mainActivity.getId()).setTarget(target)
                                    .setUserId(activityUser.getUserId()).setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(team.getRank())
                                    .setTotalBatchNo(batchNo), batchNo);
                        }
                    }

                    // 用户赛 场次 团队赛
                    if (entryGameplay.getDataSource() == 6) {
                        // 队长排名奖励
                        if (activityUser.getUserId().equals(team.getTeamManagerId())) {
                            AwardSendDto awardSendDto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId())
                                    .setType(AwardSentTypeEnum.PRESIDENT_AWARD.getType()).setRank(team.getRank()).setTotalBatchNo(batchNo);
                            if (!BigDecimal.ZERO.equals(teamGrade)) {
                                sendAward(stageList, awardSendDto, GameplayAwardStageEnum.PRESIDENT_AWARD.getType());
                            }
                        }
                        sendTeamPersonalRankAward(activityUser, batchNo, stageList);
                    }

                } catch (Exception e) {
                    log.error("团队赛用户发送奖励异常:userId:{} activityId:{}", activityUser.getUserId(), activityUser.getActivityId(), e);
                }
            }
            team.setRankAward(teamAward);
            Optional<ClubActivityTeam> byTeamId = clubActivityTeamService.findByTeamId(team.getId());
            if (byTeamId.isPresent()) {
                clubRunDataService.addRunAward(byTeamId.get().getClubId(), teamAward);
            }
            activityTeamService.updateActivityTeamById(team);
        }
        log.info("singleTeamActivityEnd end");
    }

    /**
     * 发送团队个人奖励
     *
     * @param activityUser
     * @param batchNo
     * @param stageList
     */
    private void sendTeamPersonalRankAward(ZnsRunActivityUserEntity activityUser, String batchNo, List<Integer> stageList) {
        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activityUser.getActivityId(), activityUser.getUserId());
        // 个人排名奖励
        if (Objects.nonNull(effectiveGrade.getPersonalRank()) && effectiveGrade.getPersonalRank() > 0) {
            AwardSendDto awardSendDto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId())
                    .setType(AwardSentTypeEnum.RANKING_PERSONAL_AWARD.getType()).setRank(effectiveGrade.getPersonalRank()).setTotalBatchNo(batchNo);
            sendAward(stageList, awardSendDto, GameplayAwardStageEnum.RANKING_PERSONAL_AWARD.getType());
        }
    }

    private void updateUserPersonalGrade(MainActivity mainActivity, List<ZnsRunActivityUserEntity> allActivityUser) {
        List<TeamEffectiveGrade> allTeamUser = new ArrayList<>();
        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivity.getId());
        teams.forEach(team -> {
            List<TeamEffectiveGrade> teamEffectiveGradeServiceByTeam = teamEffectiveGradeService.findByTeam(team.getId());
            allTeamUser.addAll(teamEffectiveGradeServiceByTeam);
        });
        // 实时排序根据runCount排序 排除没有参与的
        List<TeamEffectiveGrade> allTeamUserResult = allTeamUser.stream().filter(t -> t.getRunCount() > 0)
                .sorted((a, b) -> Integer.compare(b.getRunCount(), a.getRunCount()))
                .sorted((a, b) -> {
                    if (a.getRunCount().equals(b.getRunCount())) {
                        return a.getGmtModified().compareTo(b.getGmtModified());
                    }
                    return b.getRunCount().compareTo(a.getRunCount());
                })
                .toList();
        // 给 personalRankDto 赋值 rank 数值
        int currentRank = 1;
        for (TeamEffectiveGrade currentGrade : allTeamUserResult) {
            currentGrade.setPersonalRank(currentRank++);
            teamEffectiveGradeService.update(currentGrade);
        }
    }

    private void updatePreTeamAward(Long userId, Long activityId) {
        var awardPre = activityUserAwardPreService.selectByActivityIdAndUserId(activityId, userId);
        if (Objects.nonNull(awardPre)) {
            awardPre.setStatus(YesNoStatus.YES.getCode());
            activityUserAwardPreService.update(awardPre);
        }
    }

    /**
     * 有目标团队赛排名
     *
     * @param mainActivity
     * @param entryGameplay
     * @param cheatUserIdList
     * @param teams
     * @param update
     */
    private void goalTeamGradeRank(MainActivity mainActivity, EntryGameplay entryGameplay, List<Long> cheatUserIdList, List<ActivityTeam> teams, boolean update) {
        teams.forEach(team -> {
            Integer millage = 0;
            Integer runTime = 0;
            Integer runCount = 0;
            List<TeamEffectiveGrade> gradeList = teamEffectiveGradeService.findByTeam(team.getId());
            for (TeamEffectiveGrade teamEffectiveGrade : gradeList) {
                if (!cheatUserIdList.contains(teamEffectiveGrade.getUserId())) {
                    runTime += teamEffectiveGrade.getRunTime();
                    millage += teamEffectiveGrade.getRunMileage();
                    runCount += teamEffectiveGrade.getRunCount();
                    continue;
                }
                team.setRank(-1);
                team.setCompleteTime(null);
                if (update) {
                    teamEffectiveGradeService.update(teamEffectiveGrade);
                    teamEffectiveGradeService.deleteById(teamEffectiveGrade.getId());
                    activityTeamService.clearCompleteTime(team.getId());

                }
            }
            team.setRunTime(runTime);
            team.setMillage(millage);
            team.setRunCount(runCount);
            //达标人数
            if (Objects.equals(entryGameplay.getRankingBy(), RankingByEnum.REACH_NUMS.getType())) {
                team.setReachNums(activityTeamManager.getTeamReachNums(team.getId()));
            }
        });

        //计算队伍排名
        if (!CollectionUtils.isEmpty(teams)) {
            for (int i = 0; i < teams.size(); i++) {
                //重置排名
                teams.get(i).setRank(1);
                //确认排名
                for (int j = 0; j < teams.size(); j++) {
                    if (teams.get(i).lowThan(teams.get(j), entryGameplay.getRankingBy(), mainActivity.getTargetType())) {
                        teams.get(i).setRank(teams.get(i).getRank() + 1);
                    }
                }
            }
        }
    }

    /**
     * 更新有目标团队赛成绩
     *
     * @param mainActivity
     * @param entryGameplay
     * @param cheatUserIdList
     */
    private void updateGoalTeamGrade(MainActivity mainActivity, EntryGameplay entryGameplay, List<Long> cheatUserIdList) {
        if (CollectionUtils.isEmpty(cheatUserIdList)) {
            log.info("singleTeamActivityEnd cheatUserIdList is empty");
            return;
        }
        //查询活动队伍
        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivity.getId());
        goalTeamGradeRank(mainActivity, entryGameplay, cheatUserIdList, teams, true);
        //保存队伍成绩排名
        activityTeamService.updateBatchById(teams);
    }

    private void savePreTeamAward(AwardSendDto dto) {
        var awardPre = activityUserAwardPreService.selectByActivityIdAndUserId(dto.getActivityId(), dto.getUserId());
        if (Objects.isNull(awardPre)) {
            var activityUserAwardPre = new ActivityUserAwardPre();
            activityUserAwardPre.setActivityId(dto.getActivityId());
            activityUserAwardPre.setUserId(dto.getUserId());
            ZnsUserEntity user = userService.findById(dto.getUserId());
            activityUserAwardPre.setUserCode(user.getUserCode());
            activityUserAwardPre.setStatus(0);
            activityUserAwardPre.setRank(dto.getRank());
            // 作弊数据query
            CheatDataDto cheatDataDto = userRunDataDetailsCheatService.getCheatData(dto.getDetailId());
            activityUserAwardPre.setCheatingTime(cheatDataDto.getCheatingTime());
            activityUserAwardPre.setCheatingDistance(cheatDataDto.getCheatingDistance());
            activityUserAwardPreService.insert(activityUserAwardPre);
            awardPre = activityUserAwardPre;
        }
        activityUserAwardPreService.update(awardPre);
    }

    //无目标及老团队赛用
    public void updateTeamGrade(Long actId, String rankBy, List<Long> cheatUserIdList) {
        try {
            //查找需要处理的用户相关activityUsers
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .isDelete(0).userStateIn(Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                            ActivityUserStateEnum.RUNING.getState(),
                            ActivityUserStateEnum.ENDED.getState()))
                    .activityId(actId)
                    .build();

            List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findList(userQuery);
            //过滤手动标记作弊用户
            if (!CollectionUtils.isEmpty(cheatUserIdList)) {
                activityUsers = activityUsers.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
            }

            List<ZnsUserRunDataDetailsEntity> detailsEntityList = runDataDetailsService.sumDataGroupByUser(actId);
            if (!CollectionUtils.isEmpty(detailsEntityList)) {
                Map<Long, ZnsUserRunDataDetailsEntity> detailsEntityMap = detailsEntityList.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsEntity::getUserId, Function.identity(), (x, y) -> x));
                //更新用户跑步里程
                for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                    ZnsUserRunDataDetailsEntity details = detailsEntityMap.get(activityUser.getUserId());
                    if (Objects.isNull(details)) {
                        continue;
                    }

                    activityUser.setRunMileage(details.getRunMileage());
                    activityUser.setRunTime(details.getRunTime());
                    //更新跑步里程
                    runActivityUserService.updateById(activityUser);
                }
            }
            //判断是否俱乐部用户赛
            List<MainRunActivityRelationDo> relationDoList = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(actId).build());
            if (!CollectionUtils.isEmpty(relationDoList)) {
                //更新用户跑步里程
                List<Long> activityIdList = relationDoList.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
                for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                    List<ZnsUserRunDataDetailsEntity> detailsList = runDataDetailsService.findListByQuery(new UserRunDataDetailsQuery().setUserId(activityUser.getUserId()).setActivityIds(activityIdList).setIsCheat(0
                    ));
                    if (CollectionUtils.isEmpty(detailsList)) continue;
                    activityUser.setRunMileage(detailsList.stream().map(ZnsUserRunDataDetailsEntity::getRunMileage).reduce(BigDecimal.ZERO, BigDecimal::add));
                    activityUser.setRunTime(detailsList.stream().mapToInt(ZnsUserRunDataDetailsEntity::getRunTime).sum());
                    //更新跑步里程
                    runActivityUserService.updateById(activityUser);
                }
            }


            //查找该活动的所有队伍
            List<ActivityTeam> teams = activityTeamService.findByActId(actId);

            Map<Long, List<ZnsRunActivityUserEntity>> teamUserMap =
                    activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTeamId));

            //更新队伍成绩
            teamUserMap.forEach((k, v) -> {
                for (ActivityTeam team : teams) {
                    if (team.getId().equals(k)) {
                        BigDecimal millage = v.stream().map(ZnsRunActivityUserEntity::getRunMileage)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        Integer runTime = v.stream().mapToInt(ZnsRunActivityUserEntity::getRunTime).sum();
                        team.setMillage(millage.intValue());
                        team.setRunTime(runTime);
                        List<TeamEffectiveGrade> gradeList = teamEffectiveGradeService.findByTeam(team.getId());
                        if (!CollectionUtils.isEmpty(gradeList)) {
                            team.setRunCount(gradeList.stream().filter(Objects::nonNull).mapToInt(g -> g.getRunCount() != null ? g.getRunCount() : 0).sum());
                            team.setReachNums((int) gradeList.stream().filter(Objects::nonNull).filter(g -> Objects.equals(g.getIsReach(), 1)).count());
                        }
                    }
                }
            });

            MainActivity mainActivity = mainActivityService.findById(actId);

            Boolean isNew = Objects.nonNull(mainActivity) && !MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType());
            if (isNew) {
                log.info("该赛事为新赛事。id{}", actId);
                EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
                //计算队伍排名
                if (!CollectionUtils.isEmpty(teams)) {
                    for (int i = 0; i < teams.size(); i++) {
                        //重置排名
                        teams.get(i).setRank(1);
                        //确认排名
                        for (int j = 0; j < teams.size(); j++) {
                            if (teams.get(i).lowThan(teams.get(j), entryGameplay.getRankingBy(), entryGameplay.getTargetType())) {
                                teams.get(i).setRank(teams.get(i).getRank() + 1);
                            }
                        }
                    }
                }
            } else {
                //计算队伍排名
                if (!CollectionUtils.isEmpty(teams)) {
                    for (int i = 0; i < teams.size(); i++) {
                        //重置排名
                        teams.get(i).setRank(1);
                        //确认排名
                        for (int j = 0; j < teams.size(); j++) {
                            if (teams.get(i).lowThan(teams.get(j), rankBy != null ? rankBy : "1")) {
                                teams.get(i).setRank(teams.get(i).getRank() + 1);
                            }
                        }
                    }
                }
            }


            //保存队伍成绩排名
            activityTeamService.updateBatchById(teams);
        } catch (Exception e) {
            log.error("成绩统计异常。活动id{}", actId, e);
        }
    }

    private Integer getScoreAward(Integer target, List<AwardConfigDto> awardConfigDtos, Integer type, Integer rank) {
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            return 0;
        }

        AwardConfigDto awardConfigType = getAwardConfigType(target, awardConfigDtos, type, AwardTypeEnum.SCORE.getType(), rank);
        if (Objects.isNull(awardConfigType)) {
            return 0;
        }
        return awardConfigType.getScore();
    }

    public BigDecimal getAmountAward(Integer target, List<AwardConfigDto> awardConfigDtos, Integer type, Currency userCurrency, Integer rank) {
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            return BigDecimal.ZERO;
        }

        AwardConfigDto awardConfigType = getAwardConfigType(target, awardConfigDtos, type, AwardTypeEnum.AMOUNT.getType(), rank);
        if (Objects.isNull(awardConfigType)) {
            return BigDecimal.ZERO;
        }
        AwardConfigAmount awardConfigAmount = awardConfigAmountService.findByAwardConfigId(awardConfigType.getAwardConfigId());
        if (Objects.nonNull(awardConfigAmount)) {
            AwardConfigAmountCurrency amountCurrency = awardConfigAmountCurrencyDataService.findByAmountIdAndCurrencyCode(awardConfigAmount.getId(), userCurrency.getCurrencyCode());
            if (Objects.nonNull(amountCurrency)) {
                return amountCurrency.getAmount();
            }
        }

        return awardConfigType.getAmount();
    }

    private AwardConfigDto getAwardConfigType(Integer target, List<AwardConfigDto> awardConfigDtos, Integer type, Integer awardType, Integer rank) {
        List<AwardConfigDto> awardConfig = getAwardConfig(target, awardConfigDtos, type);
        if (CollectionUtils.isEmpty(awardConfig)) {
            return null;
        }
        if (Objects.nonNull(rank)) {
            awardConfig = awardConfig.stream().filter(a -> rank >= a.getRank() && rank <= a.getRankMax()).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(awardConfig)) {
            return null;
        }
        return awardConfig.stream().filter(a -> a.getAwardType().equals(awardType)).findFirst().orElse(null);
    }

    private List<AwardConfigDto> getAwardConfig(Integer target, List<AwardConfigDto> awardConfigDtos, Integer type) {
        if (Objects.nonNull(target) && target > 0) {
            awardConfigDtos = awardConfigDtos.stream().filter(a -> a.getTarget().equals(target)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            return null;
        }
        awardConfigDtos = awardConfigDtos.stream().filter(a -> a.getSendType().equals(type)).collect(Collectors.toList());
        return awardConfigDtos;
    }

    private Integer getActivityUserTarget(ZnsRunActivityUserEntity activityUser) {
        if (Objects.isNull(activityUser)) {
            return null;
        }
        if (Objects.nonNull(activityUser.getTargetRunTime()) && activityUser.getTargetRunTime() > 0) {
            return activityUser.getTargetRunTime();
        }
        if (Objects.nonNull(activityUser.getTargetRunMileage()) && activityUser.getTargetRunMileage() > 0) {
            return activityUser.getTargetRunMileage();
        }
        return null;
    }

    /**
     * 活动排名处理
     *
     * @param mainActivityId
     * @param cheatUserIdList
     * @return
     */
    public List<ActivityGoalRankVo> activityRank(Long mainActivityId, List<Long> cheatUserIdList) {
        log.info("singleActivityRank start");
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        if (MainActivityTypeEnum.SINGLE.getType().equals(mainActivity.getMainType())
                || MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(mainActivity.getMainType())
                || MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType())) {
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            if (entryGameplay.getCompetitionFormat() == 0) {
                return singleActivityRank(mainActivity, entryGameplay, cheatUserIdList);
            }
        } else if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            return seriesActivityRank(mainActivity, cheatUserIdList);
        }
        return null;
    }

    /**
     * 团队赛排名
     *
     * @param mainActivityId
     * @param cheatUserIdList
     * @return
     */
    public List<ActivityTeamUserRankVo> teamActivityRank(Long mainActivityId, List<Long> cheatUserIdList) {
        log.info("teamActivityRank start");
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        List<ActivityTeamUserRankVo> userRankList = new ArrayList<>();
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(mainActivityId);
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            allActivityUser = allActivityUser.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("teamActivityRank 无报名用户");
            return userRankList;
        }

        //查询是否有排名奖励
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(mainActivity.getPlayId(), 2);
        if (CollectionUtils.isEmpty(stageList) || !stageList.contains(4)) {
            log.info("teamActivityRank 无奖励配置");
            return userRankList;
        }


        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(mainActivity.getId());
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
        awardConfigDtos = awardConfigDtos.stream().filter(a -> AwardSentTypeEnum.RANKING_BASED_REWARDS.getType().equals(a.getSendType())
                || AwardSentTypeEnum.RANKING_HEAD_REWARD.getType().equals(a.getSendType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            log.info("teamActivityRank 无奖励配置");
            return userRankList;
        }

        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivity.getId());
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());

        //有作弊用户重新计算,但不保存
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            //更新团队成绩
            if (mainActivity.getTargetType() == 0) {
                updateTeamGradeNoSave(mainActivity, allActivityUser, teams, entryGameplay);
            } else {
                goalTeamGradeRank(mainActivity, entryGameplay, cheatUserIdList, teams, false);
            }
        }

        Integer targetType = mainActivity.getTargetType();
        String rankingBy = entryGameplay.getRankingBy();
        Map<Long, List<ZnsRunActivityUserEntity>> teamUserMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTeamId));
        for (ActivityTeam team : teams) {
            //未完赛不发奖励
            if (mainActivity.getTargetType() != 0 && team.getCompleteTime() == null) {
                log.info("team{}未完赛", team.getId());
                continue;
            }
            if (mainActivity.getTargetType() == 0 && team.getRunTime() == 0) {
                log.info("team{} 无目标 无成绩", team.getId());
                continue;
            }
            BigDecimal currentNum = BigDecimal.valueOf(team.getCurrentNum());
            //查询队伍成员成绩
            List<ZnsRunActivityUserEntity> activityUsers = teamUserMap.get(team.getId());
            log.info("activityUsers {}", activityUsers);
            if (CollectionUtils.isEmpty(activityUsers)) {
                continue;
            }
            for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                try {
                    log.info("开始计算发放用户{}活动{}奖励", activityUser.getUserId(), activityUser);
                    String batchNo = OrderUtil.getBatchNo();

                    Integer target = getActivityUserTarget(activityUser);
                    BigDecimal awardRatio = BigDecimal.ZERO;

                    BigDecimal userGrade = BigDecimal.ZERO;
                    BigDecimal teamGrade = BigDecimal.ZERO;
                    ActivityTeamUserRankVo vo = new ActivityTeamUserRankVo();
                    vo.setUserId(activityUser.getUserId());
                    vo.setRank(team.getRank());
                    vo.setActivityId(mainActivity.getId());
                    vo.setTeamName(team.getTeamName());

                    //计算有效成绩
                    if (targetType == 0) {
                        if ("1".equals(rankingBy)) {
                            userGrade = activityUser.getRunMileage();
                            teamGrade = BigDecimal.valueOf(team.getMillage());
                        } else if ("2".equals(rankingBy)) {
                            userGrade = BigDecimal.valueOf(activityUser.getRunTime());
                            teamGrade = BigDecimal.valueOf(team.getRunTime());
                        }
                    } else if (targetType == 1) {
                        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activityUser.getActivityId(), activityUser.getUserId());
                        if (!cheatUserIdList.contains(activityUser.getUserId())) {
                            userGrade = BigDecimal.valueOf(effectiveGrade.getRunMileage());
                        }
                        teamGrade = BigDecimal.valueOf(team.getMillage());
                    } else if (targetType == 2) {
                        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activityUser.getActivityId(), activityUser.getUserId());
                        if (!cheatUserIdList.contains(activityUser.getUserId())) {
                            userGrade = BigDecimal.valueOf(effectiveGrade.getRunTime());
                        }
                        teamGrade = BigDecimal.valueOf(team.getRunTime());
                    }

                    // 计算奖励比例
                    //为0不计算，避免0值异常
                    if (!BigDecimal.ZERO.equals(teamGrade)) {
                        awardRatio = userGrade.divide(teamGrade, 8, RoundingMode.HALF_UP);
                    }

                    log.info("用户成绩userGrade:{}，{},teamGrade{}awardRatio{} ", activityUser.getUserId(), userGrade, teamGrade, awardRatio);
                    Currency userCurrency = userAccountService.getUserCurrency(activityUser.getUserId());
                    vo.setCurrency(userCurrency);

                    ActivityImpracticalAwardConfig awardConfigServiceByActId = activityImpracticalAwardConfigService.findByActId(mainActivity.getId());
                    Integer teamAwardType = 0;
                    if (Objects.nonNull(awardConfigServiceByActId)) {
                        teamAwardType = awardConfigServiceByActId.getTeamAwardType();
                    }
                    //计算奖金
                    BigDecimal awardAmount = BigDecimal.ZERO;
                    BigDecimal baseReward = getAmountAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), userCurrency, team.getRank());
                    BigDecimal headReward = getAmountAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_HEAD_REWARD.getType(), userCurrency, team.getRank());
                    if (Objects.nonNull(baseReward) && baseReward.compareTo(BigDecimal.ZERO) > 0) {
                        awardAmount = awardAmount.add(baseReward);
                    }
                    if (Objects.nonNull(headReward) && headReward.compareTo(BigDecimal.ZERO) > 0) {
                        awardAmount = BigDecimalUtil.multiply(headReward, currentNum).add(awardAmount);
                    }
                    vo.setTeamAmount(I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), awardAmount));
                    TeamAwardCalcStrategy strategy = TeamAwardCalcStrategyFactory.getStrategyByType(teamAwardType);
                    // 最终金额奖励
                    awardAmount = strategy.calcTeamAmountAward(awardAmount, awardRatio, activityUsers, activityUser);
                    // 计算积分
                    Integer awardScore = 0;
                    Integer baseScoreReward = getScoreAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), team.getRank());
                    Integer headScoreReward = getScoreAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_HEAD_REWARD.getType(), team.getRank());
                    log.info("teamID" + team.getId() + "baseScoreReward: " + baseScoreReward + " headScoreReward: " + headScoreReward);
                    if (Objects.nonNull(baseScoreReward) && baseScoreReward > 0) {
                        awardScore = awardScore + baseScoreReward;
                    }
                    if (Objects.nonNull(headScoreReward) && headScoreReward > 0) {
                        awardScore = headScoreReward * team.getCurrentNum() + awardScore;
                    }
                    vo.setTeamScore(awardScore);
                    // 最终积分奖励
                    awardScore = strategy.calcTeamScoreAward(awardScore, awardRatio, activityUsers, activityUser);

                    //发放奖励
                    // 给用户余额发送奖励
                    if (BigDecimal.ZERO.compareTo(awardAmount) != 0) {
                        // 切换币种不发放奖励判断,过滤金额奖励
                        List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                        if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
                            log.info("切换币种不发放奖励判断,过滤金额奖励");
                        } else {
                            log.info("teamActivityRank 金额发放");
                            awardAmount = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), awardAmount);
                            vo.setAmount(awardAmount);
                            log.info("用户awardAmount{}，{}", activityUser.getUserId(), awardAmount);
                        }
                    }

                    if (awardScore > 0) {
                        log.info("teamActivityRank 积分发放");
                        vo.setScore(awardScore);
                        log.info("用户awardScore{}，{}", activityUser.getUserId(), awardScore);

                    }
                    userRankList.add(vo);
                } catch (Exception e) {
                    log.error("团队赛用户发送奖励异常:userId:{} activityId:{}", activityUser.getUserId(), activityUser.getActivityId(), e);
                    throw e;
                }
            }
        }
        log.info("teamActivityRank end");
        return userRankList;
    }

    /**
     * 重新计算团队成绩但不保存
     *
     * @param mainActivity
     * @param activityUsers
     * @param teams
     * @param entryGameplay
     */
    private void updateTeamGradeNoSave(MainActivity mainActivity, List<ZnsRunActivityUserEntity> activityUsers, List<ActivityTeam> teams, EntryGameplay entryGameplay) {
        try {
            List<ZnsUserRunDataDetailsEntity> detailsEntityList = runDataDetailsService.sumDataGroupByUser(mainActivity.getId());
            if (!CollectionUtils.isEmpty(detailsEntityList)) {
                Map<Long, ZnsUserRunDataDetailsEntity> detailsEntityMap = detailsEntityList.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsEntity::getUserId, Function.identity(), (x, y) -> x));
                //更新用户跑步里程
                for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                    ZnsUserRunDataDetailsEntity details = detailsEntityMap.get(activityUser.getUserId());
                    if (Objects.isNull(details)) {
                        continue;
                    }
                    activityUser.setRunMileage(details.getRunMileage());
                    activityUser.setRunTime(details.getRunTime());
                }
            }


            Map<Long, List<ZnsRunActivityUserEntity>> teamUserMap =
                    activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTeamId));

            //更新队伍成绩
            teamUserMap.forEach((k, v) -> {
                for (ActivityTeam team : teams) {
                    if (team.getId().equals(k)) {
                        BigDecimal millage = v.stream().map(ZnsRunActivityUserEntity::getRunMileage)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        Integer runTime = v.stream().mapToInt(ZnsRunActivityUserEntity::getRunTime).sum();
                        team.setMillage(millage.intValue());
                        team.setRunTime(runTime);
                    }
                }
            });

            Boolean isNew = Objects.nonNull(mainActivity) && !MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType());
            if (isNew) {
                log.info("该赛事为新赛事。id{}", mainActivity.getId());
                //计算队伍排名
                if (!CollectionUtils.isEmpty(teams)) {
                    for (int i = 0; i < teams.size(); i++) {
                        //重置排名
                        teams.get(i).setRank(1);
                        //确认排名
                        for (int j = 0; j < teams.size(); j++) {
                            if (teams.get(i).lowThan(teams.get(j), entryGameplay.getRankingBy(), entryGameplay.getTargetType())) {
                                teams.get(i).setRank(teams.get(i).getRank() + 1);
                            }
                        }
                    }
                }
            } else {
                //计算队伍排名
                if (!CollectionUtils.isEmpty(teams)) {
                    for (int i = 0; i < teams.size(); i++) {
                        //重置排名
                        teams.get(i).setRank(1);
                        //确认排名
                        for (int j = 0; j < teams.size(); j++) {
                            if (teams.get(i).lowThan(teams.get(j), entryGameplay.getRankingBy() != null ? entryGameplay.getRankingBy() : "1")) {
                                teams.get(i).setRank(teams.get(i).getRank() + 1);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("成绩统计异常。活动id{}", mainActivity.getId(), e);
        }
    }

    /**
     * 系列赛排名
     *
     * @param mainActivity
     * @param cheatUserIdList
     * @return
     */
    private List<ActivityGoalRankVo> seriesActivityRank(MainActivity mainActivity, List<Long> cheatUserIdList) {
        log.info("seriesActivityRank start");
        List<ActivityGoalRankVo> list = new ArrayList<>();

        List<ZnsRunActivityUserEntity> allActivityUserList = runActivityUserService.findAllActivityUser(mainActivity.getId());
        //过滤作弊用户
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            allActivityUserList = allActivityUserList.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(allActivityUserList)) {
            log.info("seriesActivityRank end，用户为空");
            return list;
        }

        SeriesGameplay seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
        if (!StringUtils.hasText(seriesGameplay.getRankingBy())) {
            log.info("seriesActivityRank end，没有排名依据");
            return list;
        }

        //主活动数据处理，因为是重新排名，无需重新处理数据
        for (ZnsRunActivityUserEntity activityUser : allActivityUserList) {
            if (Objects.isNull(activityUser.getPropRunTime())) {
                activityUser.setAverageVelocity(SportsDataUnit.getVelocity(activityUser.getRunTime(), activityUser.getRunMileage()));
            } else {
                activityUser.setAverageVelocity(SportsDataUnit.getVelocity(activityUser.getPropRunTime() / 1000, activityUser.getRunMileage()));
            }
        }

        //用户刷选
        if (2 == seriesGameplay.getRankingUser()) {
            allActivityUserList = allActivityUserList.stream().filter(a -> activityUserBizService.joinSeriesAllSegment(a.getActivityId(), a.getUserId())).collect(Collectors.toList());
        } else if (3 == seriesGameplay.getRankingUser()) {
            allActivityUserList = allActivityUserList.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
        }

        //用户排名当前单选
        if ("1".equals(seriesGameplay.getRankingBy())) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
        } else if ("2".equals(seriesGameplay.getRankingBy())) {
            if (2 == seriesGameplay.getRankingUser() || 1 == seriesGameplay.getRankingUser()) {
                allActivityUserList = allActivityUserList.stream().sorted((o1, o2) -> {
                    Integer propRunTime1 = o1.getPropRunTime();
                    Integer propRunTime2 = o2.getPropRunTime();
                    if (Objects.isNull(propRunTime1)) {
                        propRunTime1 = o1.getRunTimeMillisecond();
                    }
                    if (Objects.isNull(propRunTime2)) {
                        propRunTime2 = o2.getRunTimeMillisecond();
                    }
                    return propRunTime2 - propRunTime1;
                }).collect(Collectors.toList());
            } else {
                allActivityUserList = allActivityUserList.stream().sorted((o1, o2) -> {
                    Integer propRunTime1 = o1.getPropRunTime();
                    Integer propRunTime2 = o2.getPropRunTime();
                    if (Objects.isNull(propRunTime1)) {
                        propRunTime1 = o1.getRunTimeMillisecond();
                    }
                    if (Objects.isNull(propRunTime2)) {
                        propRunTime2 = o2.getRunTimeMillisecond();
                    }
                    return propRunTime1 - propRunTime2;
                }).collect(Collectors.toList());
            }
        } else if ("3".equals(seriesGameplay.getRankingBy())) {
            ZonedDateTime now = ZonedDateTime.now();
            allActivityUserList = allActivityUserList.stream().sorted((o1, o2) -> {
                ZonedDateTime o1CompleteTime = o1.getCompleteTime();
                ZonedDateTime o2CompleteTime = o2.getCompleteTime();
                if (Objects.isNull(o1CompleteTime) || o1.getIsComplete() == 0) {
                    o1CompleteTime = DateUtil.addDays(now, 1);
                }
                if (Objects.isNull(o2CompleteTime) || o2.getIsComplete() == 0) {
                    o2CompleteTime = DateUtil.addDays(now, 1);
                }
                return o1CompleteTime.compareTo(o2CompleteTime);
            }).collect(Collectors.toList());
        } else if ("4".equals(seriesGameplay.getRankingBy())) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getAverageVelocity).reversed()).collect(Collectors.toList());
        }
        log.info("seriesActivityRank end，rankingUser={}，rankingBy={}", seriesGameplay.getRankingUser(), seriesGameplay.getRankingBy());
        ActivityGoalRankVo vo = new ActivityGoalRankVo();
        List<ActivityUserRankVo> userRankList = new ArrayList<>();
        for (int i = 0; i < allActivityUserList.size(); i++) {
            ActivityUserRankVo userRankVo = new ActivityUserRankVo();
            ZnsRunActivityUserEntity runActivityUser = allActivityUserList.get(i);
            userRankVo.setUserId(runActivityUser.getUserId());
            userRankVo.setRank(i + 1);
            userRankList.add(userRankVo);
        }
        vo.setUserRankList(userRankList);
        list.add(vo);
        log.info("seriesActivityRank end");
        return list;
    }

    /**
     * 单赛事排名
     *
     * @param mainActivity
     * @param entryGameplay
     * @param cheatUserIdList
     * @return
     */
    private List<ActivityGoalRankVo> singleActivityRank(MainActivity mainActivity, EntryGameplay entryGameplay, List<Long> cheatUserIdList) {
        log.info("singleActivityRank mainActivity start");
        List<ActivityGoalRankVo> list = new ArrayList<>();
        String rankingBy = entryGameplay.getRankingBy();
        if (!StringUtils.hasText(rankingBy)) {
            log.info("activityEnd end,活动不排名，活动id：{}", mainActivity.getId());
            return list;
        }
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(mainActivity.getId());
        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("activityEnd end,活动用户为空，活动id：{}", mainActivity.getId());
            return list;
        }
        if (entryGameplay.getTargetType() != 0) {
            allActivityUser = allActivityUser.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("activityEnd end,活动用户为空，活动id：{}", mainActivity.getId());
            return list;
        }

        //过滤作弊用户
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            allActivityUser = allActivityUser.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("activityEnd end,活动用户过滤作弊用户后为空，活动id：{}", mainActivity.getId());
            return list;
        }

        //排名数据更新
        Map<Integer, List<ZnsRunActivityUserEntity>> goalMap = null;
        if (entryGameplay.getTargetType() == 1) {
            goalMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTargetRunMileage));
        } else {
            goalMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTargetRunTime));
        }
        for (Map.Entry<Integer, List<ZnsRunActivityUserEntity>> entry : goalMap.entrySet()) {
            ActivityGoalRankVo activityGoalRankVo = new ActivityGoalRankVo();
            activityGoalRankVo.setTarget(entry.getKey());
            activityGoalRankVo.setTargetType(entryGameplay.getTargetType());
            Map<Long, Integer> oldRunTimeMillisecond = new HashMap<>();
            List<ZnsRunActivityUserEntity> activityUserEntityList = entry.getValue();
            if ("2".equals(entryGameplay.getRankingBy())) {
                if (entryGameplay.getTargetType() != 0) {
                    activityUserEntityList = activityUserEntityList.stream().map(e -> {
                        Integer propRunTime = e.getPropRunTime();
                        oldRunTimeMillisecond.put(e.getId(), e.getRunTimeMillisecond());

                        if (Objects.nonNull(propRunTime)) {
                            //用于处理使用道具后的排名逻辑
                            e.setRunTimeMillisecond(propRunTime);
                        }
                        return e;
                    }).sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond)).collect(Collectors.toList());
                } else {
                    activityUserEntityList = activityUserEntityList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond).reversed()).collect(Collectors.toList());
                }
            } else {
                activityUserEntityList = activityUserEntityList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
            }

            //排名并列处理
            Integer previousRank = 1;
            Integer previousGoal = 0;
            List<ActivityUserRankVo> userRankList = new ArrayList<>();
            for (int i = 0; i < activityUserEntityList.size(); i++) {
                ZnsRunActivityUserEntity activityUser = activityUserEntityList.get(i);
                activityUser.setRank(i + 1);

                if ("2".equals(entryGameplay.getRankingBy())) {
                    if (activityUser.getRunTimeMillisecond().equals(previousGoal)) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunTimeMillisecond();
                    previousRank = activityUser.getRank();
                } else {
                    log.info("completeRuleType = 2");
                    if (activityUser.getRunMileage().compareTo(new BigDecimal(previousGoal)) == 0) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunMileage().intValue();
                    previousRank = activityUser.getRank();
                }

                log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",rank = " + activityUser.getRank());

                //排名逻辑后修正回道具使用前的跑步时长
                Integer runTimeMillisecond = oldRunTimeMillisecond.get(activityUser.getId());
                if (Objects.nonNull(runTimeMillisecond)) {
                    activityUser.setRunTimeMillisecond(runTimeMillisecond);
                }
                ActivityUserRankVo vo = new ActivityUserRankVo();
                vo.setRank(activityUser.getRank());
                vo.setUserId(activityUser.getUserId());
                userRankList.add(vo);
            }
            activityGoalRankVo.setUserRankList(userRankList);
            list.add(activityGoalRankVo);
        }
        return list;
    }


    /**
     * 单赛事活动结束
     *
     * @param mainActivity
     * @param entryGameplay
     * @param cheatUserIdList
     */
    public void singleActivityEnd(MainActivity mainActivity, EntryGameplay entryGameplay, List<Long> cheatUserIdList) {
        log.info("singleActivityEnd start mainActivity:{}", mainActivity);
        String rankingBy = entryGameplay.getRankingBy();
        if (!StringUtils.hasText(rankingBy) || "0".equals(rankingBy)) {
            log.info("activityEnd end,活动不排名，活动id：{}", mainActivity.getId());
            return;
        }
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(mainActivity.getId());
        //过滤作弊用户
        if (!CollectionUtils.isEmpty(cheatUserIdList)) {
            allActivityUser = allActivityUser.stream().filter(a -> !cheatUserIdList.contains(a.getUserId())).collect(Collectors.toList());
            cheatUserIdList.forEach(i -> {
                ZnsRunActivityUserEntity runActivityUserEntity = runActivityUserService.findActivityUserWithNoState(i, mainActivity.getId());
                if (Objects.nonNull(runActivityUserEntity)) {
                    runActivityUserEntity.setRank(-1);
                    runActivityUserService.updateById(runActivityUserEntity);
                }
            });
        }

        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("activityEnd end,活动用户为空，活动id：{}", mainActivity.getId());
            return;
        }
        if (entryGameplay.getTargetType() != 0) {
            allActivityUser = allActivityUser.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(allActivityUser)) {
            log.info("activityEnd end,活动用户为空，活动id：{}", mainActivity.getId());
            return;
        }
        //判断活动是否有排名奖励
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(entryGameplay.getGameplayId(), 2);

        //瓜分保证金额计算(总金额转美元)
        BigDecimal totalBonus = activityDeposit2UsdAmount(mainActivity.getId());
        BigDecimal avePartitionAmount = !allActivityUser.isEmpty() ? totalBonus.divide(new BigDecimal(allActivityUser.size()), 2, RoundingMode.DOWN) : BigDecimal.ZERO;

        Map<Integer, List<ZnsRunActivityUserEntity>> goalMap = null;
        //排名数据更新
        if (entryGameplay.getTargetType() == 1) {
            goalMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTargetRunMileage));
        } else {
            goalMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTargetRunTime));
        }

        boolean andAwardSendType = activityParamsService.findListByMainActivityAndAwardSendType(mainActivity.getId(),
                ActivitySettingConfigEnum.AWARD_SEND_TYPE);

        for (Map.Entry<Integer, List<ZnsRunActivityUserEntity>> entry : goalMap.entrySet()) {
            Map<Long, Integer> oldRunTimeMillisecond = new HashMap<>();
            List<ZnsRunActivityUserEntity> activityUserEntityList = entry.getValue();
            int userCount = activityUserEntityList.size();

            if ("2".equals(entryGameplay.getRankingBy())) {
                if (entryGameplay.getTargetType() != 0) {
                    activityUserEntityList = activityUserEntityList.stream().map(e -> {
                        Integer propRunTime = e.getPropRunTime();
                        oldRunTimeMillisecond.put(e.getId(), e.getRunTimeMillisecond());

                        if (Objects.nonNull(propRunTime)) {
                            //用于处理使用道具后的排名逻辑
                            e.setRunTimeMillisecond(propRunTime);
                        }
                        return e;
                    }).sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond)).collect(Collectors.toList());
                } else {
                    activityUserEntityList = activityUserEntityList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond).reversed()).collect(Collectors.toList());
                }
            } else {
                activityUserEntityList = activityUserEntityList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
            }
            Boolean isStageActivity = activityStageService.isStageACtivity(mainActivity.getId());
            if (isStageActivity) {
                //重新对阶段活动进行排名处理
                activityUserEntityList.sort(Comparator.comparing(ZnsRunActivityUserEntity::getIsComplete).reversed()
                        .thenComparing(ZnsRunActivityUserEntity::getRunTimeMillisecond)
                        .thenComparing(ZnsRunActivityUserEntity::getCreateTime)
                        .thenComparing(ZnsRunActivityUserEntity::getId));
            }
            //排名并列处理
            Integer previousRank = 1;
            Integer previousGoal = 0;
            for (int i = 0; i < activityUserEntityList.size(); i++) {
                String batchNo = OrderUtil.getBatchNo();

                ZnsRunActivityUserEntity activityUser = activityUserEntityList.get(i);
                activityUser.setRank(i + 1);
                BigDecimal award = BigDecimal.ZERO;

                if ("2".equals(entryGameplay.getRankingBy())) {
                    if (activityUser.getRunTimeMillisecond().equals(previousGoal) && !isStageActivity) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunTimeMillisecond();
                    previousRank = activityUser.getRank();
                } else {
                    log.info("completeRuleType = 2");
                    if (activityUser.getRunMileage().compareTo(new BigDecimal(previousGoal)) == 0) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunMileage().intValue();
                    previousRank = activityUser.getRank();
                }


                log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",award = " + award);
                // 开启审核
//                if (andAwardSendType) {
//                    MainActivity mainActivity1 = mainActivityService.findById(mainActivity.getId());
//                    Integer awardSendStatus = mainActivity1.getAwardSendStatus();
//                    log.info("开启人工审核奖开启 push 不做推送");
//                    if (awardSendStatus == 2) {
                        //排行奖励 push
//                        if (activityUser.getRank() <= 10 && 1 <= activityUser.getRank()) {
//                            排名push
//                            handleSendRankPush(activityUser, mainActivity);
//                        }
//                    }
//                }

                //排名逻辑后修正回道具使用前的跑步时长
                Integer runTimeMillisecond = oldRunTimeMillisecond.get(activityUser.getId());
                if (Objects.nonNull(runTimeMillisecond)) {
                    activityUser.setRunTimeMillisecond(runTimeMillisecond);
                }
                runActivityUserService.updateById(activityUser);

                Integer rank = activityUser.getRank();
                //必胜券判断
                UserCoupon useCoupon = userCouponService.getUserCouponByActIdUserIdCouponType(activityUser.getActivityId(), activityUser.getUserId(), Arrays.asList(CouponTypeEnum.WIN_COUPON.getCode()));
                if (Objects.nonNull(useCoupon)) {
                    rank = 1;
                }

                if (Objects.equals(rank, 1)) {
                    userTaskDetailService.initUserDailyTask(activityUser.getUserId());
                    //每日任务，官方赛第一名
                    userTaskDetailService.completeLevelTask(activityUser.getUserId(), UserExpObtainSubTypeEnum.WIN_RACE_ONCE.getCode(), true);
                }

                //排名奖励
                AwardSendDto awardSendDto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId())
                        .setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()).setRank(rank).setTotalBatchNo(batchNo);
                if (activityUser.getTargetRunTime() > 0) {
                    awardSendDto.setTarget(activityUser.getTargetRunTime());
                } else {
                    awardSendDto.setTarget(activityUser.getTargetRunMileage());
                }
                sendAward(stageList, awardSendDto, GameplayAwardStageEnum.RANKING_AWARD.getType());
                //保证金(转车对应币种计算)
                ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(activityUser.getUserId());
                if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(accountEntity.getCurrencyCode())) {
                    ExchangeRateConfigEntity rateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(accountEntity.getCurrencyCode());
                    if (rateConfigEntity != null) {
                        avePartitionAmount = avePartitionAmount.multiply(rateConfigEntity.getExchangeRate()).setScale(2, RoundingMode.HALF_UP);
                    }
                }
                awardActivityBizService.sendAmount(activityUser, avePartitionAmount, AccountDetailTypeEnum.NEW_ACTIVITY.getType(), AwardSentTypeEnum.AVE_PARTITION_AWARD.getType());

                //完赛奖励
                if (!CollectionUtils.isEmpty(stageList) && stageList.contains(GameplayAwardStageEnum.COMPLETE_AWARD.getType()) && activityUser.getIsComplete() == 1) {
                    AwardSendDto completeAwardSendDto = new AwardSendDto().setType(AwardSentTypeEnum.COMPLETING_THE_GAME.getType()).setActivityId(activityUser.getActivityId())
                            .setRank(rank).setUserId(activityUser.getUserId()).setTotalBatchNo(batchNo).setDetailId(activityUser.getRunDataDetailsId())
                            .setDivideUserCount(userCount);
                    if (activityUser.getTargetRunMileage() > 0) {
                        completeAwardSendDto.setTarget(activityUser.getTargetRunMileage());
                    } else if (activityUser.getTargetRunTime() > 0) {
                        completeAwardSendDto.setTarget(activityUser.getTargetRunTime());
                    }
                    sendAward(stageList, completeAwardSendDto, GameplayAwardStageEnum.COMPLETE_AWARD.getType());
                }
            }

            if (entryGameplay.getIsAllowChanllenge() == 1) {
                //排名奖励/团队排名
                for (ZnsRunActivityUserEntity runActivityUser : activityUserEntityList) {
                    //挑战奖励
                    handleBeChallengedCountAward(runActivityUser, stageList);
                }
            }

            sendSurpassAward(stageList, activityUserEntityList, mainActivity);

        }

        sendRecordBreakingAward(stageList, entryGameplay.getRankingBy(), mainActivity);
        log.info("singleActivityEnd end");
    }

    private final CompetitiveScoreConfigService competitiveScoreConfigService;

    /**
     * 发放破纪律奖励
     *
     * @param mainActivity
     */
    public void sendRecordBreakingAward(List<Integer> stageList, String rankingByStr, MainActivity mainActivity) {


        try {
            //如果活动需要审核后，就不进行发送
            Integer awardSendStatus = mainActivity.getAwardSendStatus();
            log.info("破记录奖励：活动id：「{}」,审核状态：{}", mainActivity.getId(), awardSendStatus);
            if (awardSendStatus == 0) {
                return;
            }
            if (awardSendStatus == 1) {
                log.info("奖励发放已经结束");
                return;
            }
            if (doSendRecordBreakingAwadrd(stageList, rankingByStr, mainActivity, awardSendStatus)) return;

        } catch (Exception e) {
            log.error("sendRecordBreakingAward error:{}", e);
        }
    }

    public boolean doSendRecordBreakingAwadrd(List<Integer> stageList, String rankingByStr, MainActivity mainActivity, Integer awardSendStatus) {
        log.info("破记录奖励：开始检查成绩「{}」,审核状态：{}", mainActivity.getId(), awardSendStatus);
        ActivityConstants.TargetTypeEnum targetType = null;
//        排名依据，多选逗号隔开，1：里程，2：时长，3：完赛时间 4：平均配速 5：场次 6：达标人数
        if ("1".equals(rankingByStr)) {
            targetType = ActivityConstants.TargetTypeEnum.TARGETTYPE_2;
        } else if ("2".equals(rankingByStr)) {
            targetType = ActivityConstants.TargetTypeEnum.TARGETTYPE_1;
        } else {
            log.info("破记录不支持的排序方式:{}", rankingByStr);
            return true;
        }
        Optional<CompetitiveSeasonDo> byActivityId = competitiveSeasonService.findByActivityId(mainActivity.getId());
        if (byActivityId.isEmpty()) {
            log.info("活动非竞技赛:{}", mainActivity.getId());
            return true;
        }
        //获取第一名的数据
        RunActivityUserQuery build = RunActivityUserQuery.builder().activityId(mainActivity.getId()).rank(1).build();
        List<ZnsRunActivityUserEntity> firstRankUser = runActivityUserService.findActivityUserByQuery(build);
        if (CollectionUtils.isEmpty(firstRankUser)) {
            log.info("没有第一名:{}", mainActivity.getId());
            return true;
        }
        Long scoreConfigId = byActivityId.get().getScoreConfigId();
        Optional<CompetitiveScoreConfigDo> scoreConfigDo = competitiveScoreConfigService.findById(scoreConfigId);
        if (scoreConfigDo.isEmpty()) {
            log.info("score config 不存在");
            return true;
        }
        Optional<MainActivity> mainActOp = mainActivityBizService.getMainActivity(mainActivity.getId());
        if (mainActOp.isEmpty()) {
            log.info("活动信息不存在:{}", mainActivity.getId());
            return true;
        }
        MainActivity mainAct = mainActOp.get();
        Map<String, RecordBreakingTryDto> goal = new HashMap<>();
        for (ZnsRunActivityUserEntity activityUser : firstRankUser) {
            RecordBreakingUserActivityDto userItem = new RecordBreakingUserActivityDto();
            userItem.setUserId(activityUser.getUserId());
            userItem.setMainActivityId(mainAct.getId());
            userItem.setSubActivityId(mainAct.getId());
            userItem.setRunDataDetailId(activityUser.getRunDataDetailsId());
            userItem.setTargetType(targetType);
            String mapKey = "";
            RecordBreakingTryDto tryDto = new RecordBreakingTryDto();
            tryDto.setTargetType(targetType);
            tryDto.setActivityId(userItem.getMainActivityId());
            mapKey += tryDto.getTargetType();
            //里程
            if (ActivityConstants.TargetTypeEnum.TARGETTYPE_1.equals(targetType)) {
                tryDto.setTargetRunMileage(activityUser.getTargetRunMileage());
                tryDto.setRunTimeMillisecond(activityUser.getRunTimeMillisecond());
                userItem.setRunTimeMillisecond(activityUser.getRunTimeMillisecond());
                mapKey += tryDto.getTargetRunMileage();
                //时间
            } else if (ActivityConstants.TargetTypeEnum.TARGETTYPE_2.equals(targetType)) {
                tryDto.setTargetRunTime(activityUser.getTargetRunTime());
                tryDto.setRunMileage(activityUser.getRunMileage());
                userItem.setRunMileage(activityUser.getRunMileage());
                mapKey += tryDto.getTargetRunTime();
            }
            tryDto.setCompetitiveScoreConfigId(scoreConfigDo.get().getConfigId());
            RecordBreakingTryDto recordBreakingDto = goal.getOrDefault(mapKey, tryDto);
            recordBreakingDto.addUser(userItem);
            goal.put(mapKey, recordBreakingDto);
        }
        for (RecordBreakingTryDto tryDto : goal.values()) {
            String batchNo = OrderUtil.getBatchNo();
            //破纪录，发奖励, //获取锁，开启新事务，查询当前成绩，是否打破当前记录,如果打破就进行记录
            if (recordBreakingService.tryBreak(tryDto) && recordBreakingService.doubleTryAndSave(tryDto)) {
                final ActivityConstants.TargetTypeEnum targetTypeCopy = targetType;
                tryDto.getUsers().forEach(user -> {
                    log.info("用户破纪录.{},{}", user.getUserId(), mainAct.getId());
                    AwardSendDto awardSendDto = new AwardSendDto()
                            .setActivityId(mainAct.getId())
                            .setUserId(user.getUserId())
                            .setType(AwardSentTypeEnum.RECORD_BREAKING_AWARD.getType())
                            .setRank(1).setTotalBatchNo(batchNo);
                    if (ActivityConstants.TargetTypeEnum.TARGETTYPE_1.equals(targetTypeCopy)) {
                        awardSendDto.setTarget(tryDto.getTargetRunMileage().intValue());
                    } else if (ActivityConstants.TargetTypeEnum.TARGETTYPE_2.equals(targetTypeCopy)) {
                        awardSendDto.setTarget(tryDto.getTargetRunTime());
                    }
                    if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                        awardSendDto.setTarget(0);
                    }
                    //判断活动是否有破纪录奖励
                    if (!CollectionUtils.isEmpty(stageList) && stageList.contains(GameplayAwardStageEnum.RECORD_BREAKING_AWARD.getType())) {
                        log.info("玩法有破纪律奖励：{}", mainActivity.getId());
                        sendAward(stageList, awardSendDto, GameplayAwardStageEnum.RECORD_BREAKING_AWARD.getType());
                    }
                });
            }
        }
        return false;
    }

    /**
     * 超越奖励
     *
     * @param stageList
     * @param activityUserEntityList
     * @param mainActivity
     */
    private void sendSurpassAward(List<Integer> stageList, List<ZnsRunActivityUserEntity> activityUserEntityList, MainActivity mainActivity) {
        if (CollectionUtils.isEmpty(stageList) || !stageList.contains(GameplayAwardStageEnum.SURPASS_AWARD.getType())) {
            log.info("未开启超越奖励");
            return;
        }
        ActivityParams activityParams = activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.SURPASS_USER_CODE).orElse(null);
        if (Objects.isNull(activityParams)) {
            log.info("未配置超越奖励用户");
            return;
        }
        ZnsUserEntity user = userService.findByUserCode(activityParams.getParamValue());
        if (Objects.isNull(user)) {
            log.info("未找到超越奖励用户");
            return;
        }
        ZnsRunActivityUserEntity znsRunActivityUserEntity = activityUserEntityList.stream().filter(a -> a.getUserId().equals(user.getId())).findFirst().orElse(null);
        activityUserEntityList = activityUserEntityList.stream().filter(a -> activityUserBizService.isSurpass(znsRunActivityUserEntity, a)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityUserEntityList)) {
            log.info("未找到超过用户");
            return;
        }
        for (ZnsRunActivityUserEntity runActivityUserEntity : activityUserEntityList) {
            String batchNo = OrderUtil.getBatchNo();

            AwardSendDto awardSendDto = new AwardSendDto().setActivityId(runActivityUserEntity.getActivityId()).setUserId(runActivityUserEntity.getUserId())
                    .setType(AwardSentTypeEnum.SURPASS_AWARD.getType()).setRank(runActivityUserEntity.getRank()).setTotalBatchNo(batchNo)
                    .setDivideUserCount(activityUserEntityList.size());
            if (runActivityUserEntity.getTargetRunMileage() > 0) {
                awardSendDto.setTarget(runActivityUserEntity.getTargetRunMileage());
            } else if (runActivityUserEntity.getTargetRunTime() > 0) {
                awardSendDto.setTarget(runActivityUserEntity.getTargetRunTime());
            }
            sendAward(stageList, awardSendDto, GameplayAwardStageEnum.SURPASS_AWARD.getType());
        }
    }

    /**
     * 瓜分保证金额计算(总金额转美元)
     *
     * @param activityId
     * @return
     */
    public BigDecimal activityDeposit2UsdAmount(Long activityId) {
        //查询活动指定类型的交易金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<CurrencyAmount> currencyAmountList = userAccountDetailService.findActCurrencyAmountByTradeType(activityId, Arrays.asList(AccountDetailTypeEnum.SECURITY_FUND.getType()), 2);
        if (CollectionUtils.isEmpty(currencyAmountList)) {
            return totalAmount;
        }
        for (CurrencyAmount currencyAmount : currencyAmountList) {
            if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                totalAmount = totalAmount.add(currencyAmount.getAmount());
                continue;
            }
            //查询汇率(usd -> 其他币种)
            ExchangeRateConfigEntity rateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(currencyAmount.getCurrencyCode());
            if (rateConfigEntity == null) {
                log.error("美元转" + currencyAmount.getCurrencyCode() + "汇率不存在");
                totalAmount = totalAmount.add(currencyAmount.getAmount());
                continue;
            }
            //其他币种转美元 = amount / 汇率
            BigDecimal exchangeAmount = currencyAmount.getAmount().divide(rateConfigEntity.getExchangeRate(), 2, RoundingMode.HALF_UP);
            totalAmount = totalAmount.add(exchangeAmount);
        }
        return totalAmount;
    }

    private void sendAward(List<Integer> stageList, AwardSendDto awardSendDto, int stage) {
        if (CollectionUtils.isEmpty(stageList)) {
            return;
        }
        if (!stageList.contains(stage)) {
            return;
        }
        awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);
    }
//
//    private void handleSendRankPush(ZnsRunActivityUserEntity activityUser, MainActivity mainActivity) {
//        List<Long> userIds = Arrays.asList(activityUser.getUserId());
//        ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), I18nConstant.LanguageCodeEnum.en_US.getCode());
//        String content = "Congratulations, you have achieved " + activityUser.getRank() + "th in " + activityDisseminate.getTitle() + ", keep up the good work!";
//        MessageBo messageBo = new MessageBo();
//        messageBo.setTitle("Hello!" + activityUser.getNickname());
//        messageBo.setContent(content);
//        messageBo.setJumpType("5");
//        messageBo.setRouteType(1);
//        messageBo.setCollapseKey("PitPat");
//        Map<String, Object> extras = new HashMap<>();
//        UserPushToken userToken = userPushTokenService.findByUserId(activityUser.getUserId());
//        if (Objects.isNull(userToken)) {
//            log.warn("用户={}push token 为空", activityUser.getUserId());
//            userToken = new UserPushToken();
//        }
//        if (StringUtils.hasText(userToken.getIosPushToken()) && !"-1".equals(userToken.getIosPushToken())) {
//            messageBo.setRouteValue("lznative://main/newrunningreport");
//            extras.put("type", 1);
//            extras.put("detailId", activityUser.getRunDataDetailsId());
//        }
//        if (StringUtils.hasText(userToken.getAndroidPushToken()) && !"-1".equals(userToken.getAndroidPushToken())) {
//            messageBo.setRouteValue("lznative://main/newrunningreport");
//            extras.put("activityId", mainActivity.getId());
//            extras.put("REPORT_DETAIL_ID", activityUser.getRunDataDetailsId());
//            extras.put("REPORT_DETAIL_TYPE", 13);
//        }
//        messageBo.setData(extras);
//        messageBo.setNotificationType(NoticeTypeEnum.ACTIVITY_RANK_NOTICE.getType());
//        messageBo.setActivityId(mainActivity.getId());
//        appMessageService.push(userIds, messageBo, "");
//    }

    private void handleBeChallengedCountAward(ZnsRunActivityUserEntity activityUser, List<Integer> stageList) {
        String batchNo = OrderUtil.getBatchNo();

        Integer challengedCount = userRunRecordService.officialEventChallengedCount(activityUser.getUserId(), activityUser.getActivityId());
        if (challengedCount <= 0) {
            log.info("被挑战次数小于等于0");
            return;
        }

        AwardSendDto awardSendDto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId()).
                setType(AwardSentTypeEnum.BEING_CHALLENGED.getType()).setRank(challengedCount);
        if (activityUser.getTargetRunTime() > 0) {
            awardSendDto.setTarget(activityUser.getTargetRunTime());
            awardSendDto.setTargetType(2);
        } else {
            awardSendDto.setTarget(activityUser.getTargetRunMileage());
            awardSendDto.setTargetType(1);
        }
        awardSendDto.setTotalBatchNo(batchNo);
        sendAward(stageList, awardSendDto, GameplayAwardStageEnum.CHALLENGE_AWARD.getType());
    }

    private void hasTargetDataDeal(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto activityTypeDto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        String activityType = activityTypeDto.getMainType();
        MainActivityTypeEnum activityTypeEnum = MainActivityTypeEnum.findByType(activityType);
        switch (activityTypeEnum) {
            case SINGLE, SINGLE_POLYMERIZATION, SINGLE_POLYMERIZATION_RUNNING ->
                    singleActivityRunEnd(userRunDataDetail, user, activityTypeDto, activityUser, mainActivity, entryGameplay, batchNo);
            case RANK ->
                    rankActivityRunEnd(userRunDataDetail, user, activityTypeDto, activityUser, mainActivity, entryGameplay, batchNo);
            case PROP ->
                    propActivityRunEnd(userRunDataDetail, user, activityTypeDto, activityUser, mainActivity, entryGameplay, batchNo);
            case SERIES_SUB -> {
                // 阶段处理
                singleActivityRunEnd(userRunDataDetail, user, activityTypeDto, activityUser, mainActivity, entryGameplay, batchNo);
                // 系列处理
                seriesActivityRunEnd(mainActivity.getId(), user, userRunDataDetail, batchNo);
            }
            case FREE_CHALLENGE_MAIN ->
                freeChallengeActivityRunEnd(userRunDataDetail, user, activityTypeDto, activityUser, mainActivity, entryGameplay, batchNo);
            case PLACEMENT ->
                    //系列处理
                placementActivityRunEnd(userRunDataDetail, user, activityTypeDto, activityUser, mainActivity, entryGameplay, batchNo);
            default -> log.warn("未知活动类型: {}", activityType);
        }

    }

    /**
     * 跑步结束-自由挑战处理
     * @param userRunDataDetail
     * @param user
     * @param activityTypeDto
     * @param activityUser
     * @param mainActivity
     * @param entryGameplay
     * @param batchNo
     */
    private void freeChallengeActivityRunEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto activityTypeDto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        log.info("freeChallengeActivityRunEnd start,detail :{}", userRunDataDetail.getId());
        if (userRunDataDetail.getRunTime() < 60) {
            log.info("freeChallengeActivityRunEnd 运动时间小于60s");
            return;
        }
        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("freeChallengeActivityRunEnd 结束，作弊不处理");
            return;
        }
        //查询玩法
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        //判断是否完赛
        boolean complete = isComplete(subActivity.getTarget(), entryGameplay.getTargetType(), userRunDataDetail);
        Integer propRunTime = null;
        if (entryGameplay.getTargetType() == 1 && YesNoStatus.YES.getCode().equals(subActivity.getAllowProp())) {
            Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUser.getUserId(), activityUser.getActivityId(), userRunDataDetail.getId(), null);
            if (effectValue > 0) {
                propRunTime = Math.max(userRunDataDetail.getRunTimeMillisecond() - effectValue * 1000, 0);
            }
        }
        List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
        MainActivity subMainMainActivity = allMainActivity.get(0);
        ZnsRunActivityUserEntity laActivityUser = runActivityUserService.findActivityUser(subMainMainActivity.getId(), activityUser.getUserId());
       if (entryGameplay.getFetchRule() == 2) {
            //最佳成绩
            if (activityUser.getUserState() == 1 && userRunDataDetail.getRunTime() >= 60) {
                activityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
                laActivityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
            }
            boolean best = false;
            if (complete) {
                activityUser.setIsComplete(1);
                laActivityUser.setIsComplete(1);
                if (Objects.isNull(activityUser.getCompleteTime())) {
                    activityUser.setCompleteTime(ZonedDateTime.now());
                }
                if(Objects.isNull(laActivityUser.getCompleteTime())){
                    laActivityUser.setCompleteTime(ZonedDateTime.now());
                }
                //判定上榜
                FreeActivityConfig config = activityParamsService.findCacheOne(mainActivity.getId(), ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
                if (makeTheList(userRunDataDetail,config)) {
                    // LA 设备更新子活动成绩
                    Long treadmillId = userRunDataDetail.getTreadmillId();
                    boolean isLaEquipment = treadmillWhiteListService.checkEquipmentWhiteList(treadmillId);
                    if (isLaEquipment) {
                        // 上榜
                        laActivityUser.setRank(0);
                    }
                    // 上榜
                    activityUser.setRank(0);
                    // 奖励发送
                    if (config.getMode().equals(FreeActivityModeEnum.COMPETE.getCode())) {
                        sendMakeTheListAward(mainActivity, activityUser, activityTypeDto, userRunDataDetail, batchNo);
                    }
                    best = isBest(entryGameplay.getTargetType(), userRunDataDetail, activityUser, propRunTime, complete);
                    if (best) {
                        activityUser.setRunTime(userRunDataDetail.getRunTime());
                        activityUser.setRunTimeMillisecond(userRunDataDetail.getRunTimeMillisecond());
                        activityUser.setRunMileage(userRunDataDetail.getRunMileage());
                        activityUser.setModifieTime(ZonedDateTime.now());
                        activityUser.setRunDataDetailsId(userRunDataDetail.getId());
                        if (Objects.nonNull(propRunTime)) {
                            activityUser.setPropRunTime(propRunTime);
                        } else {
                            activityUser.setPropRunTime(userRunDataDetail.getRunTimeMillisecond());
                        }
                        // 更新la 成绩
                        if(isLaEquipment){
                            laActivityUser.setRunTime(userRunDataDetail.getRunTime());
                            laActivityUser.setRunTimeMillisecond(userRunDataDetail.getRunTimeMillisecond());
                            laActivityUser.setRunMileage(userRunDataDetail.getRunMileage());
                            laActivityUser.setModifieTime(ZonedDateTime.now());
                            laActivityUser.setRunDataDetailsId(userRunDataDetail.getId());
                            if (Objects.nonNull(propRunTime)) {
                                laActivityUser.setPropRunTime(propRunTime);
                            } else {
                                laActivityUser.setPropRunTime(userRunDataDetail.getRunTimeMillisecond());
                            }
                        }
                    }
                }
            }
            runActivityUserService.updateById(activityUser);
            runActivityUserService.updateById(laActivityUser);
       }
    }

    /**
     *
     * 定级赛完赛
     * @param userRunDataDetail
     * @param user
     * @param activityTypeDto
     * @param activityUser
     * @param mainActivity
     * @param entryGameplay
     * @param batchNo
     */
    private void placementActivityRunEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto activityTypeDto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        log.info("placementActivityRunEnd start,detail :{}", userRunDataDetail.getId());
        if (userRunDataDetail.getRunTime() < 60) {
            log.info("placementActivityRunEnd 运动时间小于60s");
            return;
        }
        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("placementActivityRunEnd 结束，作弊不处理");
            activityStageBusiness.runEnd(userRunDataDetail, null, false, false);
            return;
        }
        //查询玩法
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        //判断是否完赛
        boolean complete = isComplete(subActivity.getTarget(), 1, userRunDataDetail);

        //首次完赛
        if (activityUser.getIsComplete() == 0 && userRunDataDetail.getRunTime() >= 60) {
            //更新活动用户表
            activityUserBizService.updateRunDataDetail(activityUser, userRunDataDetail, entryGameplay, subActivity.getAllowProp());
        }

        //完赛计算用户定级分数
        if (complete) {
            BigDecimal score = calcPlacementScore(mainActivity, activityUser, userRunDataDetail, user);
            saveUserPlacementScore(score,mainActivity,user,userRunDataDetail);
        }

        log.info("singleActivityRunEnd end");
    }

    /**
     * @param score
     * @param mainActivity
     * @param user
     * @param userRunDataDetail
     */
    private void saveUserPlacementScore(BigDecimal score, MainActivity mainActivity, ZnsUserEntity user, ZnsUserRunDataDetailsEntity userRunDataDetail) {
        UserPlacementLevelLogDo userPlacementLevelLogDo = new UserPlacementLevelLogDo();
        UserPlacementLevelDo userPlacementLevelDo = userPlacementLevelService.findByQuery(new UserPlacementLevelQuery().setUserId(user.getId()));
        if(Objects.isNull(userPlacementLevelDo)){
            userPlacementLevelDo = new UserPlacementLevelDo();
            userPlacementLevelDo.setUserId(user.getId());
            userPlacementLevelDo.setScenario(user.getGender());
            PlacementLevelScoreRuleDo placementLevelScoreRuleDo = placementUserLevel(score, user.getGender());
            userPlacementLevelDo.setLevelScore(score.intValue());
            userPlacementLevelDo.setLevelCode(placementLevelScoreRuleDo.getLevelCode());
            userPlacementLevelDo.setLevelName(placementLevelScoreRuleDo.getLevelName());
            userPlacementLevelService.create(userPlacementLevelDo);
        }else {
            // 更新最新定级积分
            userPlacementLevelLogDo.setLastLevelCode(userPlacementLevelDo.getLevelCode());
            userPlacementLevelLogDo.setLastLevelName(userPlacementLevelDo.getLevelName());
            userPlacementLevelLogDo.setLastLevelScore(userPlacementLevelDo.getLevelScore());
            PlacementLevelScoreRuleDo placementLevelScoreRuleDo = placementUserLevel(score, user.getGender());
            userPlacementLevelDo.setLevelScore(score.intValue());
            userPlacementLevelDo.setLevelCode(placementLevelScoreRuleDo.getLevelCode());
            userPlacementLevelDo.setLevelName(placementLevelScoreRuleDo.getLevelName());
            userPlacementLevelService.update(userPlacementLevelDo);
        }
        userPlacementLevelLogDo.setCurrentAcitivytId(mainActivity.getId());
        userPlacementLevelLogDo.setDetailId(userRunDataDetail.getId());
        userPlacementLevelLogDo.setUserId(user.getId());
        userPlacementLevelLogDo.setCurrentLevelCode(userPlacementLevelDo.getLevelCode());
        userPlacementLevelLogDo.setCurrentLevelName(userPlacementLevelDo.getLevelName());
        userPlacementLevelLogDo.setCurrentLevelScore(userPlacementLevelDo.getLevelScore());
        userPlacementLevelLogDo.setTargetMileage(userRunDataDetail.getDistanceTarget().intValue());
        userPlacementLevelLogDo.setScenario(user.getGender());
        userPlacementLevelLogService.create(userPlacementLevelLogDo);
    }

    private PlacementLevelScoreRuleDo placementUserLevel(BigDecimal score, Integer gender) {
        return placementLevelScoreRuleService.findMatchingLevel(score, gender);
    }

    /**
     * 是否上榜
     *
     * @param userRunDataDetail
     * @param config
     * @return
     */
    private boolean makeTheList(ZnsUserRunDataDetailsEntity userRunDataDetail, FreeActivityConfig config) {
        if (Objects.nonNull(config)) {
            if (config.getMode().equals(FreeActivityModeEnum.PROP.getCode())) {
                return true;
            }
            Long rubTemplateId = config.getRubTemplateId();
            RunTemplateDo runTemplateDo = runTemplateService.findById(rubTemplateId);
            // 超越目标数据
            if(Objects.nonNull(runTemplateDo)){
                ZnsUserRunDataDetailsEntity target = runDataDetailsService.findById(runTemplateDo.getDetailId());
                return userRunDataDetail.getRunTime() < target.getRunTime();
            }
        }
        return false;
    }

    /**
     * 计算定级分数核心方法
     *
     * @param mainActivity
     * @param activityUser
     * @param userRunDataDetail
     * @param user
     */
    public BigDecimal calcPlacementScore(MainActivity mainActivity, ZnsRunActivityUserEntity activityUser, ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user) {
        Integer targetRunMileage = activityUser.getTargetRunMileage();
        int runMileageKm = targetRunMileage / 1000;
        PlacementMileageSpeedRuleDo rule = getSpeedRuleFromDB(runMileageKm, user.getGender());
        // 算速度分数
        BigDecimal baseValue = getBaseValue(rule);
        // 计算用户当前速度
        BigDecimal maxSpeedV2 = new BigDecimal(3600).divide(new BigDecimal(userRunDataDetail.getAveragePace()), 2, RoundingMode.HALF_UP);
        BigDecimal speedScore = maxSpeedV2.divide(baseValue, 2, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(rule.getSpeedWeight()))
                .setScale(2, RoundingMode.DOWN);
        log.info("calcPlacementScore speedScore:{}", speedScore);
        // 计算用户里程分数
        PlacementMileageScoreRuleDo placementMileageScoreRuleDo = placementMileageScoreRuleService.findMatchingRule(runMileageKm, user.getGender());
        BigDecimal finalScore = BigDecimal.ZERO;
        if (Objects.nonNull(placementMileageScoreRuleDo)) {
            List<LevelPercentDto> levelPercentDtos = sysConfigService.selectConfigListByKey(ConfigKeyEnums.LEVEL_PERCENT.getCode(), LevelPercentDto.class);
            Map<Integer, Integer> scenarioToPercentMap = levelPercentDtos.stream()
                    .collect(Collectors.toMap(
                            LevelPercentDto::getScenario,            // 键：性别
                            LevelPercentDto::getLevelScorePercent,   // 值：等级分数占比
                            (existing, replacement) -> existing       // 合并函数：保留第一个遇到的值
                    ));
            Integer perCent = scenarioToPercentMap.getOrDefault(user.getGender(), 0);
            finalScore = new BigDecimal(placementMileageScoreRuleDo.getScore()).multiply(new BigDecimal(100 - perCent))
                    .divide(new BigDecimal(100), 2, RoundingMode.DOWN).add(speedScore.multiply(new BigDecimal(perCent)).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
            log.info("calcPlacementScore finalScore:{}", speedScore);
        }
        // 如果是新职业赛 这次% + 老% 之和
        ProActivityType proActivityType = mainActivity.getProActivityType();
        if (Objects.nonNull(proActivityType) && !Objects.equals(proActivityType.getCode(), ProActivityType.NONE.getCode())
                && !Objects.equals(proActivityType.getCode(), ProActivityType.ENTERTAINMENT.getCode())) {
            finalScore = finalScore.multiply(new BigDecimal(100 - proActivityType.getPriority())).divide(new BigDecimal(100), 2, RoundingMode.DOWN)
                    .add(new BigDecimal(proActivityType.getPriority()));
            PlacementScoreCalcConfigDo placementScoreCalcConfigDo = placementScoreCalcConfigService.findByQuery(new PlacementScoreCalcConfigQuery());
            if (Objects.nonNull(placementScoreCalcConfigDo)) {
                BigDecimal thisTimeScore = finalScore.multiply(new BigDecimal(placementScoreCalcConfigDo.getNewScoreRatio()).divide(new BigDecimal(100), 2, RoundingMode.DOWN));
                UserPlacementLevelDo userPlacementLevelDo = userPlacementLevelService.findByQuery(new UserPlacementLevelQuery().setUserId(user.getId()).setScenario(user.getGender()));
                finalScore = thisTimeScore.add(new BigDecimal(userPlacementLevelDo.getLevelScore()).multiply(new BigDecimal(placementScoreCalcConfigDo.getOriginalScoreRatio()).divide(new BigDecimal(100), 2, RoundingMode.DOWN)));
            }
            log.info("calcPlacementScore proActivityType finalScore:{}", speedScore);
        }
        // 匹配配用户等级
        return finalScore.setScale(0, RoundingMode.DOWN);
    }
    /**
     * 上榜奖励
     * @param mainActivity
     * @param activityUser
     * @param activityTypeDto
     * @param userRunDataDetail
     * @param batchNo
     */
    private void sendMakeTheListAward(MainActivity mainActivity, ZnsRunActivityUserEntity activityUser, ActivityTypeDto activityTypeDto, ZnsUserRunDataDetailsEntity userRunDataDetail, String batchNo) {
        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        int surplusTime = DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.addDays(endTime, 1));
        if (surplusTime < 0) {
            surplusTime = 0;
        }
        //判断是否已经发放奖励
        String key = RedisConstants.MAIN_ACTIVITY_ONLINE_RANK_AWARD + activityUser.getActivityId() + "_" + activityUser.getUserId();
        boolean exists = redissonClient.getBucket(key).isExists();
        if (exists) {
            log.info("sendMakeTheListAward end，已经发放奖励");
            return;
        }
        sendAward(AwardSentTypeEnum.ONLINE_RANK.getType(), activityTypeDto.getTargetType(), activityUser.getUserId(), activityUser.getActivityId(), activityUser.getTargetRunMileage(), activityUser.getTargetRunTime(), 0, activityUser.getRunDataDetailsId(), batchNo);
        redissonClient.getBucket(key).set("1", surplusTime, TimeUnit.SECONDS);
        //单赛事发放完赛奖励设置缓存
        String awardPopKey = String.format(RedisKeyConstant.USER_REWARD_POP, userRunDataDetail.getId());
        redissonClient.getBucket(awardPopKey).set("1", surplusTime, TimeUnit.SECONDS);
    }

    private PlacementMileageSpeedRuleDo getSpeedRuleFromDB(int runMileageKm, Integer gender) {
        PlacementMileageSpeedRuleDo rule = placementMileageSpeedRuleService.findByQuery(new PlacementMileageSpeedRuleQuery().setScenario(gender).setMileage(runMileageKm));
        if(Objects.isNull(rule)){
            rule = placementMileageSpeedRuleService.findByQuery(new PlacementMileageSpeedRuleQuery().setScenario(gender).setMileage(0));
        }
        return rule;
    }

    // 根据计算类型获取基准值 (V2)
    private BigDecimal getBaseValue(PlacementMileageSpeedRuleDo rule) {
        PlacementV2SpeedConfig levelPercentDtos = sysConfigService.selectConfigVoByKey(ConfigKeyEnums.PLACEMENT_SPEED_CONFIG.getCode(), PlacementV2SpeedConfig.class);
        return switch (rule.getV2CalcType()) {
            case 0 -> // 手动输入
                    rule.getV2CalcValue();
            case 1 -> // 平均数
                    levelPercentDtos.getAvgSpeed(); // 实际项目中应实现具体逻辑
            case 2 -> // 中位数
                    levelPercentDtos.getMiddleSpeed(); // 实际项目中应实现具体逻辑
            case 3 -> // 前1/3位数
                    levelPercentDtos.getTheFirstThirdSpeed(); // 实际项目中应实现具体逻辑
            default -> rule.getV2CalcValue();
        };
    }


    /**
     * 跑步结束-系列赛事处理
     *
     * @param sunActivityId
     * @param user
     * @param userRunDataDetail
     * @param batchNo
     */
    private void seriesActivityRunEnd(Long sunActivityId, ZnsUserEntity user, ZnsUserRunDataDetailsEntity userRunDataDetail, String batchNo) {
        log.info("seriesActivityRunEnd sunActivityId=" + sunActivityId);

        //查询系列赛关联表
        SeriesActivityRel seriesActivityRel = seriesActivityRelService.findBySubId(sunActivityId);

        //查询系列所有关卡
        List<Long> subActivityId = seriesActivityRelService.findSubActivityId(seriesActivityRel.getParentActivityId());
        //判断是否全部完成
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(subActivityId, user.getId());


        //查询主活动用户记录
        ZnsRunActivityUserEntity mainActivityUser = runActivityUserService.findActivityUser(seriesActivityRel.getParentActivityId(), user.getId());
        if (Objects.isNull(mainActivityUser)) {
            log.info("hasTargetDataDeal series-sub deal end,mainActivityUser is null");
            return;
        }
        //更新用户主赛事成绩
        updateSeriesActivityGrade(mainActivityUser);

        mainActivityUser.setRunDataDetailsId(userRunDataDetail.getId());
        runActivityUserService.updateById(mainActivityUser);
        log.info("更新系列赛主赛事detailId，{}->{}", mainActivityUser.getActivityId(), mainActivityUser.getRunDataDetailsId());

        //关卡数量和activityUsers不一致表示没有全部参加
        if (activityUsers.size() < subActivityId.size()) {
            log.info("hasTargetDataDeal series-sub deal end,没有完成所有关卡");
            return;
        }


        long noCompleteCount = activityUsers.stream().filter(a -> a.getIsComplete() == 0).count();
        if (noCompleteCount == 0) {

            mainActivityUser.setIsComplete(1);
            mainActivityUser.setCompleteTime(ZonedDateTime.now());
            mainActivityUser.setRunDataDetailsId(userRunDataDetail.getId());
            runActivityUserService.updateById(mainActivityUser);

            activityStageBusiness.reRank(seriesActivityRel.getParentActivityId());

            //判断是否有作弊情况
            ZnsRunActivityUserEntity activityUser = activityUsers.stream().filter(a -> a.getIsCheat() == 1).findFirst().orElse(null);
            if (Objects.nonNull(activityUser)) {
                log.info("hasTargetDataDeal series-main deal end,用户有作弊行为");
                return;
            }
            MainActivity mainActivity = mainActivityService.findById(seriesActivityRel.getParentActivityId());

            //全部奖励发放
            List<Integer> stageList = gameplayAwardStageConfigService.findStageList(mainActivity.getPlayId(), 1);
            if (!CollectionUtils.isEmpty(stageList) && !stageList.contains(1)
                    && Objects.equals(mainActivity.getFinishedCertificate(), -1)
                    && Objects.equals(mainActivity.getFirstCertificate(), -1)) {
                log.info("hasTargetDataDeal series-main deal end,无完赛奖励");
                return;
            }
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            AwardSendDto dto = new AwardSendDto(seriesActivityRel.getParentActivityId(), AwardSentTypeEnum.COMPLETING_THE_GAME.getType(),
                    0, 0, entryGameplay.getTargetType(), user.getId());
            dto.setTotalBatchNo(batchNo);
            awardActivityBizService.sendActivityAwardByConfigAndStage(dto);


            //PULS会员完赛钉钉消息通知
//            CompletableFuture.runAsync(() -> activityUserManager.sendPlusVipDingTalkMessages(userRunDataDetail.getId(), userRunDataDetail.getUserId(), MainActivityTypeEnum.SERIES_MAIN.getType()));
        }
    }

    public void updateSeriesActivityGrade(ZnsRunActivityUserEntity mainActivityUser) {

        if (Objects.isNull(mainActivityUser)) {
            log.info("hasTargetDataDeal series-sub deal end,mainActivityUser is null");
            return;
        }

        List<SeriesActivityRel> seriesActivityRelList = seriesActivityRelService.findSubActivity(mainActivityUser.getActivityId());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(
                seriesActivityRelList.stream().map(SeriesActivityRel::getSegmentActivityId).toList(), mainActivityUser.getUserId());


        mainActivityUser.setPropRunTime(null);
        mainActivityUser.setRunTimeMillisecond(0);
        mainActivityUser.setRunMileage(BigDecimal.ZERO);
        mainActivityUser.setRunTime(0);
        mainActivityUser.setRank(-1);
        for (ZnsRunActivityUserEntity activityUser : activityUsers) {
            mainActivityUser.setRunTimeMillisecond(mainActivityUser.getRunTimeMillisecond() + activityUser.getRunTimeMillisecond());
            mainActivityUser.setRunTime(mainActivityUser.getRunTime() + activityUser.getRunTime());
            mainActivityUser.setRunMileage(mainActivityUser.getRunMileage().add(activityUser.getRunMileage()));
            //计算道具时长
            if (Objects.nonNull(mainActivityUser.getPropRunTime()) || Objects.nonNull(activityUser.getPropRunTime())) {
                if (Objects.isNull(mainActivityUser.getPropRunTime())) {
                    mainActivityUser.setPropRunTime(0);
                }
                if (Objects.isNull(activityUser.getPropRunTime())) {
                    activityUser.setPropRunTime(activityUser.getRunTimeMillisecond());
                }
                mainActivityUser.setPropRunTime(mainActivityUser.getPropRunTime() + activityUser.getPropRunTime());
            }
            if (activityUser.getIsComplete() == 0) {
                mainActivityUser.setIsComplete(0);
            }
        }
        runActivityUserService.updateById(mainActivityUser);

    }

    /**
     * 单赛事或阶段 目标结束处理
     *
     * @param userRunDataDetail
     * @param user
     * @param activityTypeDto
     * @param activityUser
     * @param mainActivity
     * @param entryGameplay
     * @param batchNo
     */
    private void singleActivityRunEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto activityTypeDto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        log.info("singleActivityRunEnd start,detail :{}", userRunDataDetail.getId());
        if (userRunDataDetail.getRunTime() < 60) {
            log.info("singleActivityRunEnd 运动时间小于60s");
            return;
        }
        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("hasTargetDataDeal 结束，作弊不处理");
            activityStageBusiness.runEnd(userRunDataDetail, null, false, false);
            return;
        }
        //查询玩法
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        //判断是否完赛
        boolean complete = isComplete(subActivity.getTarget(), entryGameplay.getTargetType(), userRunDataDetail);
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(entryGameplay.getGameplayId(), 2);

        Integer propRunTime = null;
        if (entryGameplay.getTargetType() == 1 && YesNoStatus.YES.getCode().equals(subActivity.getAllowProp())) {
            Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUser.getUserId(), activityUser.getActivityId(), userRunDataDetail.getId(), null);
            if (effectValue > 0) {
                propRunTime = userRunDataDetail.getRunTimeMillisecond() - effectValue * 1000 < 0 ? 0 : userRunDataDetail.getRunTimeMillisecond() - effectValue * 1000;
            }
        }

        if (entryGameplay.getFetchRule() == 1) {
            //首次完赛
            if (activityUser.getIsComplete() == 0 && userRunDataDetail.getRunTime() >= 60) {
                //更新活动用户表
                activityUserBizService.updateRunDataDetail(activityUser, userRunDataDetail, entryGameplay, subActivity.getAllowProp());
            }
        } else if (entryGameplay.getFetchRule() == 2) {
            //最佳成绩
            if (activityUser.getUserState() == 1 && userRunDataDetail.getRunTime() >= 60) {
                activityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
            }
            boolean best = false;
            if (complete) {
                activityUser.setIsComplete(1);
                if (Objects.isNull(activityUser.getCompleteTime())) {
                    activityUser.setCompleteTime(ZonedDateTime.now());
                }
                best = isBest(entryGameplay.getTargetType(), userRunDataDetail, activityUser, propRunTime, complete);
                if (best) {
                    activityUser.setRunTime(userRunDataDetail.getRunTime());
                    activityUser.setRunTimeMillisecond(userRunDataDetail.getRunTimeMillisecond());
                    activityUser.setRunMileage(userRunDataDetail.getRunMileage());
                    activityUser.setModifieTime(ZonedDateTime.now());
                    activityUser.setRunDataDetailsId(userRunDataDetail.getId());
                    if (Objects.nonNull(propRunTime)) {
                        activityUser.setPropRunTime(propRunTime);
                    } else {
                        activityUser.setPropRunTime(userRunDataDetail.getRunTimeMillisecond());
                    }
                }
            }

            runActivityUserService.updateById(activityUser);
            activityStageBusiness.runEnd(userRunDataDetail, propRunTime, complete, best);
            //因为reRank依赖activityUser，而activityUser更新不在runEnd里，所以不在runEnd里使用reRank
            activityStageBusiness.reRank(userRunDataDetail.getActivityId());
        } else if (FetchRuleTypeEnum.ACCUMULATED_PARTICIPATION_DATA.getType().equals(entryGameplay.getFetchRule())) {
            //累计参赛数据
            Integer effectValue = 0;
            if (entryGameplay.getTargetType() == 1 && YesNoStatus.YES.getCode().equals(subActivity.getAllowProp())) {
                //累计参数计算道具时间根据活动id查询
                effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUser.getUserId(), activityUser.getActivityId(), null, null);
            }

            if (activityUser.getUserState() == 1) {
                activityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
            }
            int runTime = activityUser.getRunTime() + userRunDataDetail.getRunTime();
            BigDecimal runMileage = activityUser.getRunMileage().add(userRunDataDetail.getRunMileage());
            if (entryGameplay.getTargetType() == 1) {
                complete = runMileage.compareTo(new BigDecimal(activityUser.getTargetRunMileage())) >= 0;
            } else if (entryGameplay.getTargetType() == 2) {
                complete = runTime >= activityUser.getTargetRunTime();
            } else if (entryGameplay.getTargetType() == 3) {
                List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(mainActivity.getId()).build());
                if (!CollectionUtils.isEmpty(list)) {
                    List<Long> runActivityIds = list.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
                    List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findList(RunActivityUserQuery.builder().activityIds(runActivityIds).userId(user.getId()).isComplete(1).build());
                    complete = activityUsers.size() >= subActivity.getTarget();
                } else {
                    complete = false;
                }
            }
            if (complete) {
                activityUser.setIsComplete(1);
                if (Objects.isNull(activityUser.getCompleteTime())) {
                    activityUser.setCompleteTime(ZonedDateTime.now());
                }
            }
            activityUser.setRunTime(activityUser.getRunTime() + userRunDataDetail.getRunTime());
            activityUser.setRunTimeMillisecond(activityUser.getRunTimeMillisecond() + userRunDataDetail.getRunTimeMillisecond());
            activityUser.setRunMileage(activityUser.getRunMileage().add(userRunDataDetail.getRunMileage()));
            activityUser.setModifieTime(ZonedDateTime.now());
            activityUser.setRunDataDetailsId(userRunDataDetail.getId());
            if (effectValue > 0) {
                //使用道具之后的时间
                propRunTime = activityUser.getRunTimeMillisecond() - (effectValue * 1000);
                activityUser.setPropRunTime(Math.max(propRunTime, 0));
            }
            runActivityUserService.updateById(activityUser);
        } else if (FetchRuleTypeEnum.ACCUMULATED_COMPLETION_DATA.getType().equals(entryGameplay.getFetchRule())) {
            //累计完赛数据
            Integer effectValue = 0;
            if (entryGameplay.getTargetType() == 1 && YesNoStatus.YES.getCode().equals(subActivity.getAllowProp())) {
                //累计完赛数计算道具时间根据活动id查询
                effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUser.getUserId(), activityUser.getActivityId(), null, null);
            }
            if (activityUser.getUserState() == 1) {
                activityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
            }
            if (entryGameplay.getTargetType() == 1) {
                complete = userRunDataDetail.getRunMileage().compareTo(new BigDecimal(activityUser.getTargetRunMileage())) >= 0;
            } else if (entryGameplay.getTargetType() == 2) {
                complete = userRunDataDetail.getRunTime() >= activityUser.getTargetRunTime();
            }
            if (complete) {
                activityUser.setIsComplete(1);
                if (Objects.isNull(activityUser.getCompleteTime())) {
                    activityUser.setCompleteTime(ZonedDateTime.now());
                }
                activityUser.setRunTime(activityUser.getRunTime() + userRunDataDetail.getRunTime());
                activityUser.setRunTimeMillisecond(activityUser.getRunTimeMillisecond() + userRunDataDetail.getRunTimeMillisecond());
                activityUser.setRunMileage(activityUser.getRunMileage().add(userRunDataDetail.getRunMileage()));
                activityUser.setModifieTime(ZonedDateTime.now());
                activityUser.setRunDataDetailsId(userRunDataDetail.getId());
                if (effectValue > 0) {
                    //使用道具之后的时间
                    propRunTime = activityUser.getRunTimeMillisecond() - (effectValue * 1000);
                    activityUser.setPropRunTime(Math.max(propRunTime, 0));
                }
            }

            runActivityUserService.updateById(activityUser);
        }

        //判断是否挑战
        if (entryGameplay.getIsAllowChanllenge() == 1) {
            //挑战数据处理
            challengeDataDeal(activityTypeDto, user.getId(), userRunDataDetail, complete, entryGameplay.getTargetType(), stageList, batchNo);
        }


        //完赛奖励 因为完赛勋章证书的关系，这里变成了必掉
        if (complete) {
            sendCompleteAward(mainActivity, activityUser, activityTypeDto, userRunDataDetail, batchNo);
        }

        if (!CollectionUtils.isEmpty(stageList)) {
            if (userRunDataDetail.getIsCheat() == 1) {
                log.info("noTargetDataDeal end，数据作弊");
                return;
            }
            //时长/目标奖励
            if (stageList.contains(GameplayAwardStageEnum.MILEAGE_GOAL_AWARD.getType()) || stageList.contains(GameplayAwardStageEnum.TIME_GOAL_AWARD.getType())) {
                // 有目标的时长/里程奖励取所有的活动跑步记录
//                List<ZnsUserRunDataDetailsEntity> list = runDataDetailsService.getUserDetailByActivityId(activityUser.getUserId(), activityUser.getActivityId());
                UserRunDataDetailsQuery userRunDataDetailsQuery = new UserRunDataDetailsQuery().setIsCheat(0).setUserId(activityUser.getUserId()).setActivityId(activityUser.getActivityId());
                userRunDataDetailsQuery.setOrders(List.of(OrderItem.desc("id")));
                List<ZnsUserRunDataDetailsEntity> list = runDataDetailsService.findListByQuery(userRunDataDetailsQuery);
                if (!CollectionUtils.isEmpty(list)) {
                    Integer totalRunTime = 0;
                    BigDecimal totalRunMileage = BigDecimal.ZERO;
                    for (ZnsUserRunDataDetailsEntity entity : list) {
                        totalRunTime = totalRunTime + entity.getRunTime();
                        totalRunMileage = totalRunMileage.add(entity.getRunMileage());
                    }
                    activityUser.setRunMileage(totalRunMileage);
                    activityUser.setRunTime(totalRunTime);
                }
                goalCompleteAward(userRunDataDetail, user, activityUser, mainActivity, batchNo);
            }
            //阶段奖励
            if (stageList.contains(GameplayAwardStageEnum.PERSONAL_STAGE_AWARD.getType())) {
                goalCompleteAward(userRunDataDetail, user, activityUser, mainActivity, batchNo);
            }
        }

        log.info("singleActivityRunEnd end");
    }

    private void sendCompleteAward(MainActivity mainActivity, ZnsRunActivityUserEntity activityUser, ActivityTypeDto activityTypeDto, ZnsUserRunDataDetailsEntity userRunDataDetail, String batchNo) {
        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        int surplusTime = DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.addDays(endTime, 1));
        if (surplusTime < 0) {
            surplusTime = 0;
        }
        //判断是否已经发放奖励
        String key = RedisConstants.MAIN_ACTIVITY_COMPLETE_AWARD + activityUser.getActivityId() + "_" + activityUser.getUserId();
        boolean exists = redissonClient.getBucket(key).isExists();
        if (exists) {
            log.info("sendCompleteAward end，已经发放奖励");
            return;
        }

        sendAward(AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), activityTypeDto.getTargetType(), activityUser.getUserId(), activityUser.getActivityId(), activityUser.getTargetRunMileage(), activityUser.getTargetRunTime(), 0, activityUser.getRunDataDetailsId(), batchNo);
        redissonClient.getBucket(key).set("1", surplusTime, TimeUnit.SECONDS);
        //单赛事发放完赛奖励设置缓存
        String awardPopKey = String.format(RedisKeyConstant.USER_REWARD_POP, userRunDataDetail.getId());
        redissonClient.getBucket(awardPopKey).set("1", surplusTime, TimeUnit.SECONDS);
    }


    /**
     * 段位赛
     *
     * @param userRunDataDetail
     * @param user
     * @param activityTypeDto
     * @param activityUser
     * @param mainActivity
     * @param entryGameplay
     * @param batchNo
     */
    private void rankActivityRunEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto activityTypeDto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        //查询玩法
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        //判断是否完赛
        boolean complete = isComplete(subActivity.getTarget(), entryGameplay.getTargetType(), userRunDataDetail);
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(entryGameplay.getGameplayId(), 2);

        if (entryGameplay.getFetchRule() == 1) {
            //首次完赛
            if (activityUser.getIsComplete() == 0) {
                //更新活动用户表
                activityUserBizService.updateRunDataDetail(activityUser, userRunDataDetail, entryGameplay, subActivity.getAllowProp());
            }
        }

        //段位赛-所有人都结束了则关闭活动
        mainActivityBizService.closedRankActivity(userRunDataDetail.getActivityId());

        //更新用户（完成状态、时间、运动里程、运动时间）
        rankActivityBizService.updateRunRankedActivityUser(userRunDataDetail, -1);

        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("noTargetDataDeal end，数据作弊");
            return;
        }

        if (!complete) {
            log.warn("用户未完赛，无法获得段位赛奖励，userId={}, detailId={}", userRunDataDetail.getUserId(), userRunDataDetail.getId());
            return;
        }
        if (!CollectionUtils.isEmpty(stageList) && stageList.contains(GameplayAwardStageEnum.COMPLETE_AWARD.getType())) {
            log.info("段位赛发放完赛奖励，activityId={},userId={}", activityTypeDto.getId(), user.getId());
            //完赛奖励
            sendAward(AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), activityTypeDto.getTargetType(), activityUser.getUserId(), activityUser.getActivityId(), activityUser.getTargetRunMileage(), activityUser.getTargetRunTime(), 0, userRunDataDetail.getId(), batchNo);
        }

        //奖励结算延迟消息
        RankedUserQuery rankedUserQuery = new RankedUserQuery(userRunDataDetail.getActivityId(), user.getId());
        log.info("ActivityResultManager#rankActivityRunEnd-----段位赛跑步结束延迟2.5秒发送奖励，userId:{},activityId:{}", rankedUserQuery.getUserId(), rankedUserQuery.getActivityId());
        final int waitMillisecond = 2500; //延迟2.5秒做开始用户奖励结算
        rabbitTemplate.convertAndSend(ranked_award_settle_delay_exchange, "", JsonUtil.writeString(rankedUserQuery), message -> {
            message.getMessageProperties().setDelay(waitMillisecond);// 毫秒为单位，指定此消息的延时时长,
            return message;
        });
    }

    private void propActivityRunEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto activityTypeDto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        //查询玩法
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        if (entryGameplay.getFetchRule() == 1) {
            //首次完赛
            if (activityUser.getIsComplete() == 0) {
                //更新活动用户表
                activityUserBizService.updateRunDataDetail(activityUser, userRunDataDetail, entryGameplay, subActivity.getAllowProp());
            }
        }

        //道具赛-所有人都结束了则关闭活动
        closedPropActivity(userRunDataDetail.getActivityId());

        //更新用户（完成状态、时间、运动里程、运动时间）
        //propRankedActivityBizService.updateRunRankedActivityUser(userRunDataDetail,-1);

        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("noTargetDataDeal end，数据作弊");
            return;
        }
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(entryGameplay.getGameplayId(), 2);

//        if (!CollectionUtils.isEmpty(stageList) && stageList.contains(GameplayAwardStageEnum.COMPLETE_AWARD.getType())) {
//            log.info("道具赛发放完赛奖励，activityId={},userId={}", activityTypeDto.getId(), user.getId());
//            //完赛奖励
//            sendAward(AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), mainActivity.getTargetType(), activityUser.getUserId(), activityUser.getActivityId(), activityUser.getTargetRunMileage(), activityUser.getTargetRunTime(), 0, userRunDataDetail.getId(), batchNo);
//        }


        //奖励结算延迟消息
        PropRankedUserQuery rankedUserQuery = new PropRankedUserQuery(userRunDataDetail.getActivityId(), user.getId());
        log.info("ActivityResultManager#propActivityRunEnd-----道具赛跑步结束延迟2.5秒发送奖励，userId:{},activityId:{}", rankedUserQuery.getUserId(), rankedUserQuery.getActivityId());
        final int waitMillisecond = 0; //延迟2.5秒做开始用户奖励结算
        rabbitTemplate.convertAndSend(prop_ranked_award_settle_delay_exchange, "", JsonUtil.writeString(rankedUserQuery), message -> {
            message.getMessageProperties().setDelay(waitMillisecond);// 毫秒为单位，指定此消息的延时时长,
            return message;
        });
    }

    private void sendAward(Integer type, Integer targetType, Long userId, Long activityId, Integer targetRunMileage, Integer targetRunTime, Integer rank, Long runDataDetailsId, String batchNo) {
        log.info("sendAward,type={},targetType={}", type, targetType);
        //奖励发放
        AwardSendDto awardSendDto = new AwardSendDto();
        awardSendDto.setType(type);
        if (targetType == 1) {
            awardSendDto.setTarget(targetRunMileage);
        } else if (targetType == 2) {
            awardSendDto.setTarget(targetRunTime);
        }
        awardSendDto.setUserId(userId);
        awardSendDto.setActivityId(activityId);
        awardSendDto.setRank(rank);
        awardSendDto.setDetailId(runDataDetailsId);
        awardSendDto.setTotalBatchNo(batchNo);
        awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);
    }

    /**
     * 挑战数据处理
     *
     * @param activityTypeDto
     * @param userId
     * @param userRunDataDetail
     * @param complete
     * @param targetType
     * @param stageList
     * @param batchNo
     */
    private void challengeDataDeal(ActivityTypeDto activityTypeDto, Long userId, ZnsUserRunDataDetailsEntity userRunDataDetail, boolean complete, Integer targetType, List<Integer> stageList, String batchNo) {
        // 查找用户官方赛事最新跑步记录
        ZnsUserRunRecordEntity lastRecord = userRunRecordService.findLastRecord(activityTypeDto.getId(), userId, userRunDataDetail.getId());
        if (null == lastRecord) {
            log.info("未查到用户官方赛事最新跑步记录");
            return;
        }
        if (complete) {
            //查询当前排名
            Integer currentRank = runActivityUserService.getCurrentRank(activityTypeDto.getId(), targetType, userId, userRunDataDetail.getRunTimeMillisecond(), userRunDataDetail.getRunMileage());
            lastRecord.setRank(currentRank);
            // 更新用户跑步记录的完成状态
            lastRecord.setIsComplete(1);
            lastRecord.setModifieTime(ZonedDateTime.now());
            lastRecord.setCompleteTime(ZonedDateTime.now());
            userRunRecordService.update(lastRecord);
        }

        // 完成比赛并比较成绩之后, 更新用户跑步成功。
        ZnsRunActivityUserEntity challengedUser = lastRecord.getChallengedUserId() > 0 ? runActivityUserService.findActivityUser(activityTypeDto.getId(), lastRecord.getChallengedUserId()) : null;
        if (Objects.isNull(challengedUser)) {
            log.info("未查到官方赛事的被挑战用户");
            return;
        }

        boolean challengeSuccess = false;

        if (1 == targetType) {
            if (challengedUser.getRunTimeMillisecond() == 0) {
                challengedUser.setRunTimeMillisecond(challengedUser.getRunTime() * 1000 + 999);
            }
            // 先判断用户是否挑战成功
            if (challengedUser.getRunTimeMillisecond().compareTo(userRunDataDetail.getRunTimeMillisecond()) >= 0 && complete) {
                challengeSuccess = true;
            }
        } else if (2 == targetType) {
            // 先判断用户是否挑战成功
            if (challengedUser.getRunMileage().compareTo(userRunDataDetail.getRunMileage()) <= 0 && complete) {
                challengeSuccess = true;
            }
        }

        if (complete && challengeSuccess) {
            if (!CollectionUtils.isEmpty(stageList) && stageList.contains(GameplayAwardStageEnum.CHALLENGE_AWARD.getType())) {
                if (userRunDataDetail.getIsCheat() != 1) {
                    // 处理挑战成功奖励逻辑
                    sendAward(AwardSentTypeEnum.CHALLENGE_SUCCESS.getType(), activityTypeDto.getTargetType(), userRunDataDetail.getUserId(), userRunDataDetail.getActivityId(),
                            challengedUser.getTargetRunMileage(), challengedUser.getTargetRunTime(), lastRecord.getChallengeRank(), userRunDataDetail.getId(), batchNo);
                }

            }
            lastRecord.setIsChallengeSuccess(1);
            lastRecord.setModifieTime(ZonedDateTime.now());
//            lastRecord.setChallengeAward(award);
            userRunRecordService.update(lastRecord);
        }

        if (false == challengeSuccess && userRunDataDetail.getRunTime() >= 60) {
            if (!CollectionUtils.isEmpty(stageList) && stageList.contains(GameplayAwardStageEnum.CHALLENGE_AWARD.getType())) {
                if (userRunDataDetail.getIsCheat() != 1) {
                    // 挑战失败奖励发放
                    sendAward(AwardSentTypeEnum.CHALLENGE_FAIL.getType(), activityTypeDto.getTargetType(), userRunDataDetail.getUserId(), userRunDataDetail.getActivityId(),
                            challengedUser.getTargetRunMileage(), challengedUser.getTargetRunTime(), lastRecord.getChallengeRank(), userRunDataDetail.getId(), batchNo);
                }
            }

            lastRecord.setModifieTime(ZonedDateTime.now());
//            lastRecord.setChallengeAward(award);
            userRunRecordService.update(lastRecord);
        }
    }

    /**
     * 是否最佳
     *
     * @param targetType
     * @param userRunDataDetail
     * @param activityUser
     * @param propRunTime
     * @param complete
     * @return
     */
    private boolean isBest(Integer targetType, ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsRunActivityUserEntity activityUser, Integer propRunTime, boolean complete) {
        if (userRunDataDetail.getRunTimeMillisecond() <= 0) {
            return false;
        }
        if (activityUser.getRunTimeMillisecond() <= 0) {
            return true;
        }
        //原成绩未完赛表示新成绩一定是最佳
        if (activityUser.getIsComplete() == 0 && complete) {
            return true;
        } else if (activityUser.getIsComplete() == 1 && !complete) {
            //原成绩已完赛新成绩未完赛一定不是最佳
            return false;
        } else if (activityUser.getIsComplete() == 0 && !complete) {
            if (userRunDataDetail.getRunMileage().compareTo(activityUser.getRunMileage()) > 0) {
                return true;
            }
        }

        if (targetType == 1) {
            if (Objects.nonNull(propRunTime)) {
                if (Objects.isNull(activityUser.getPropRunTime())) {
                    return propRunTime < activityUser.getRunTimeMillisecond();
                } else if (propRunTime < activityUser.getPropRunTime()) {
                    return true;
                }
            } else if (userRunDataDetail.getRunTimeMillisecond() < activityUser.getRunTimeMillisecond()) {
                return true;
            }
        }
        if (targetType == 2 && userRunDataDetail.getRunMileage().compareTo(activityUser.getRunMileage()) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 是否完赛
     *
     * @param target
     * @param targetType
     * @param userRunDataDetail
     * @return
     */
    private boolean isComplete(Integer target, Integer targetType, ZnsUserRunDataDetailsEntity userRunDataDetail) {
        if (Objects.isNull(userRunDataDetail)) {
            return false;
        }
        if (targetType == 1) {
            return userRunDataDetail.getRunMileage().intValue() >= target;
        } else if (Objects.equals(targetType, 2)) {
            return userRunDataDetail.getRunTime() >= target;
        } else {
            return userRunDataDetail.getRunTime() >= target;
        }
    }

    /**
     * 无目标数据处理/里程/时长奖励
     *
     * @param userRunDataDetail
     * @param user
     * @param dto
     * @param activityUser
     * @param mainActivity
     * @param entryGameplay
     * @param batchNo
     */
    private void noTargetDataDeal(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ActivityTypeDto dto, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, EntryGameplay entryGameplay, String batchNo) {
        log.info("noTargetDataDeal start");
        if (userRunDataDetail.getDataSource() != 0) {
            log.info("noTargetDataDeal end，数据上传非app");
            return;
        }
        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("noTargetDataDeal end，数据作弊");
            return;
        }
        //判断数据来源
        if (entryGameplay.getDataSource() == 1 && !dto.getId().equals(userRunDataDetail.getActivityId())) {
            log.info("noTargetDataDeal end，数据来源非目标活动，活动id：{}，跑步明细id：{}", dto.getId(), userRunDataDetail.getId());
            return;
        }

        if (entryGameplay.getFetchRule() == 3 || entryGameplay.getFetchRule() == 4 || entryGameplay.getFetchRule() == 5) {
            if (activityUser.getUserState() == 1) {
                activityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
            }
            activityUser.setRunTime(activityUser.getRunTime() + userRunDataDetail.getRunTime());
            activityUser.setRunTimeMillisecond(activityUser.getRunTimeMillisecond() + userRunDataDetail.getRunTimeMillisecond());
            activityUser.setRunMileage(activityUser.getRunMileage().add(userRunDataDetail.getRunMileage()));
            activityUser.setModifieTime(ZonedDateTime.now());
            activityUser.setRunDataDetailsId(userRunDataDetail.getId());
            runActivityUserService.updateById(activityUser);
        }

        //时长里程奖励发放
        goalCompleteAward(userRunDataDetail, user, activityUser, mainActivity, batchNo);
        log.info("noTargetDataDeal end");
    }

    //新活动更新团队赛成绩（通用，不区分有无目标）
    private void addTeamGrade(MainActivity mainActivity, ZnsRunActivityUserEntity activityUser, ZnsUserRunDataDetailsEntity userRunDataDetail, Boolean isComplete, EntryGameplay entryGameplay, boolean ignoreActivityStatus) {
        log.info("开始更新团队成绩,mainActivity:{},activityUser:{},detailsId:{}", mainActivity, activityUser, userRunDataDetail.getId());
        if (entryGameplay.getCompetitionFormat() == 0) {
            log.info("updateTeamGrade end entryGameplay.getCompetitionFormat() == 0");
            return;
        }
        if (userRunDataDetail.getRunTime() < 60) {
            log.info("updateTeamGrade 运动时间小于60s");
            return;
        }
        if (Objects.isNull(activityUser) || Objects.isNull(activityUser.getTeamId()) || activityUser.getTeamId() <= 0) {
            log.info("updateTeamGrade end ,没有队伍");
            return;
        }

        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("团队赛成绩作弊，detailId{}", userRunDataDetail.getId());
            return;
        }
        if (!MainActivityStateEnum.STARTED.getCode().equals(mainActivity.getActivityState()) && !ignoreActivityStatus) {
            log.info("活动不在进行中，mainActivity{}", mainActivity.getId());
            return;
        }

        Integer targetType = mainActivity.getTargetType();

        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivity.getId());
        for (ActivityTeam team : teams) {
            if (team.getId().equals(activityUser.getTeamId()) && team.getCompleteTime() == null) {
                log.info("开始处理成绩,team:{}", team);
                if (targetType == 0) {
                    calculateNonTargetTeamGrade(mainActivity, userRunDataDetail, team, entryGameplay, isComplete);
                } else {
                    calculateTargetTeamGrade(mainActivity, activityUser, userRunDataDetail, team, entryGameplay, isComplete);
                }
            }
        }

        //计算队伍排名
        if (!CollectionUtils.isEmpty(teams)) {
            for (int i = 0; i < teams.size(); i++) {
                //重置排名
                teams.get(i).setRank(1);
                //确认排名
                for (int j = 0; j < teams.size(); j++) {
                    if (teams.get(i).lowThan(teams.get(j), entryGameplay.getRankingBy(), mainActivity.getTargetType())) {
                        teams.get(i).setRank(teams.get(i).getRank() + 1);
                    }
                }
            }
        }

        //保存队伍成绩排名
        activityTeamService.updateBatchById(teams);
    }

    private void calculateNonTargetTeamGrade(MainActivity mainActivity, ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTeam team, EntryGameplay entryGameplay, Boolean isComplete) {
        log.info("无目标团对赛成绩处理");
        team.setMillage(team.getMillage() + userRunDataDetail.getRunMileage().intValue());
        team.setRunTime(team.getRunTime() + userRunDataDetail.getRunTime());
        TeamEffectiveGrade teamEffectiveGrade = teamEffectiveGradeService.findByActivityAndUser(mainActivity.getId(), userRunDataDetail.getUserId());

        // 完赛处理
        if (isComplete) {
            team.setRunCount(team.getRunCount() + 1);
            processTeamEffectiveGrade(teamEffectiveGrade, userRunDataDetail, mainActivity, team.getId(), true);
            team.setReachNums(activityTeamManager.getTeamReachNums(team.getId()));
            log.info("完赛后团队成绩展示:{}", team);
        } else if (Objects.equals(entryGameplay.getRankingBy(), RankingByEnum.RUN_MILEAGE.getType()) || Objects.equals(entryGameplay.getRankingBy(), RankingByEnum.RUN_TIME.getType())) {
            // 里程和时间的排名依据进行 未完赛处理
            processTeamEffectiveGrade(teamEffectiveGrade, userRunDataDetail, mainActivity, team.getId(), false);
            log.info("未完赛团队成绩展示:{}", team);
        }

    }

    private void processTeamEffectiveGrade(TeamEffectiveGrade teamEffectiveGrade,
                                           ZnsUserRunDataDetailsEntity userRunDataDetail,
                                           MainActivity mainActivity,
                                           Long teamId,
                                           boolean isComplete) {
        boolean isNew = Objects.isNull(teamEffectiveGrade.getId());
        if (isNew) {
            teamEffectiveGrade = new TeamEffectiveGrade();
            teamEffectiveGrade.setActivityId(mainActivity.getId());
            teamEffectiveGrade.setUserId(userRunDataDetail.getUserId());
            teamEffectiveGrade.setTeamId(teamId);
            teamEffectiveGrade.setRunMileage(0);
            teamEffectiveGrade.setRunTime(0);
            teamEffectiveGrade.setRunCount(0);
            teamEffectiveGrade.setIsReach(0);
        }

        // 更新/创建通用逻辑
        teamEffectiveGrade.setRunMileage(teamEffectiveGrade.getRunMileage() + userRunDataDetail.getRunMileage().intValue());
        teamEffectiveGrade.setRunTime(teamEffectiveGrade.getRunTime() + userRunDataDetail.getRunTime());
        if (isComplete) {
            teamEffectiveGrade.setRunCount(teamEffectiveGrade.getRunCount() + 1);
            teamEffectiveGrade.setIsReach(activityUserBizService.isReach(mainActivity.getId(), userRunDataDetail.getUserId()) ? 1 : 0);
        }

        if (isNew) {
            teamEffectiveGradeService.insert(teamEffectiveGrade);
            log.info("用户有效成绩,newTeamEffectiveGrade:{}", teamEffectiveGrade);
        } else {
            teamEffectiveGradeService.update(teamEffectiveGrade);
            log.info("用户有效成绩,teamEffectiveGrade:{}", teamEffectiveGrade);
        }
    }

    //计算有目标的团队赛成绩
    private void calculateTargetTeamGrade(MainActivity activity, ZnsRunActivityUserEntity activityUser, ZnsUserRunDataDetailsEntity runDataDetail, ActivityTeam team, EntryGameplay entryGameplay, boolean isComplete) {
        log.info("Calculating target team actId:{},userId:{},detail:{}", activity.getId(), activityUser.getId(), runDataDetail.getId());
        if (team.getCompleteTime() != null) {
            log.info("队伍已达到目标无需计算，userID{}，teamid{}，detailId{}", activityUser.getUserId(), team.getId(), runDataDetail.getId());
            return;
        }
        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(activity.getId(), activityUser.getUserId());

        log.info("EffectiveGrade:{}", effectiveGrade);
        //避免重复更新成绩
        if (effectiveGrade.getDetailId() != null && Objects.equals(effectiveGrade.getDetailId(), runDataDetail.getId())) {
            log.info("重复更新成绩");
            return;
        }
        Long activityId = activity.getId();
        Long userId = activityUser.getUserId();
        Integer targetType = activity.getTargetType();
        Long detailsId = runDataDetail.getId();

        List<Integer> targets = subActivityService.getSingleActivityTargets(activityId);
        Integer target = targets.get(0);

        //真实成绩
        Integer userRunTime = runDataDetail.getRunTime();
        Integer userRunMileage = runDataDetail.getRunMileage().intValue();

        //有效成绩
        Integer effectiveRunTime = userRunTime;
        Integer effectiveRunMileage = userRunMileage;

        RLock lock = redissonClient.getLock(RedisConstants.TEAM_GRADE_CALCULATE + team.getId());

        boolean teamLock = LockHolder.tryLock(lock, 30);
        try {
            if (teamLock) {
                if (team.getCompleteTime() != null) {
                    log.info("二次确认队伍已达到目标无需计算，userID{}，teamid{}，detailId{}", activityUser.getUserId(), team.getId(), runDataDetail.getId());
                    return;
                }
                Integer teamMillage = team.getMillage();
                Integer teamRunTime = team.getRunTime();
                Integer teamGrade = 0;
                BigDecimal effectiveRatio = BigDecimal.ONE;
                //0：无，1：里程，2：时长 3:场次
                if (targetType == 1) {
                    //超出边界
                    if (target < teamMillage + userRunMileage) {
                        effectiveRunMileage = target - teamMillage;
                        //有效比例
                        effectiveRatio = BigDecimalUtil.div(BigDecimal.valueOf(effectiveRunMileage), userRunMileage);
                        effectiveRunTime = effectiveRatio.multiply(BigDecimal.valueOf(userRunTime)).intValue();
                    }
                    teamGrade = team.getMillage() + effectiveRunMileage;
                } else if (targetType == 2) {
                    //超出边界
                    if (target < teamRunTime + userRunTime) {
                        effectiveRunTime = target - teamRunTime;
                        //有效比例
                        effectiveRatio = BigDecimalUtil.div(BigDecimal.valueOf(effectiveRunTime), userRunTime);
                        effectiveRunMileage = effectiveRatio.multiply(BigDecimal.valueOf(userRunMileage)).intValue();

                    }
                    teamGrade = team.getRunTime() + effectiveRunTime;
                } else {
                    effectiveRunMileage = runDataDetail.getRunMileage().intValue();
                    effectiveRunTime = runDataDetail.getRunTime();
                }
                if (Objects.equals(targetType, 3)) {
                    teamGrade = isComplete ? team.getRunCount() + 1 : team.getRunCount();
                }
                log.info("effectiveRatio :{}", effectiveRatio);
                log.info("userId:{},teamId{},detailId:{},effectiveRunTime :{},effectiveRunMileage :{},userRunTime :{},userRunMileage :{}",
                        userId, team.getId(), detailsId, effectiveRunTime, effectiveRunMileage, userRunTime, userRunMileage);

                team.setMillage(team.getMillage() + effectiveRunMileage);
                team.setRunTime(team.getRunTime() + effectiveRunTime);
                if (teamGrade >= target) {
                    team.setCompleteTime(ZonedDateTime.now());
                }


                effectiveGrade.setDetailId(detailsId);
                effectiveGrade.setRunMileage(effectiveGrade.getRunMileage() + effectiveRunMileage);
                effectiveGrade.setRunTime(effectiveGrade.getRunTime() + effectiveRunTime);
                if (isComplete) {
                    effectiveGrade.setRunCount(effectiveGrade.getRunCount() + 1);
                    activityTeamService.addRunCountByTeam(team.getId(), 1);
                    Optional<ActivityParams> targetCount = activityParamsService.findOneByMainActivityAndParamType(activityId, ActivitySettingConfigEnum.TARGET_USER_COUNT);
                    if (targetCount.isPresent() && effectiveGrade.getRunCount() >= Integer.parseInt(targetCount.get().getParamValue())) {
                        effectiveGrade.setIsReach(YesNoStatus.YES.getCode());
                    }
                }
                log.info("team :{},effectiveGrade:{}", team, effectiveGrade);
                teamEffectiveGradeService.insertOrUpdate(effectiveGrade);
                //有效成绩更新后
                team.setReachNums(activityTeamManager.getTeamReachNums(team.getId()));
                team.setRunCount(null);
                activityTeamService.updateActivityTeamById(team);
            }
        } catch (Exception e) {
            log.error("团队赛成绩计算error", e);
        } finally {
            if (lock.isHeldByCurrentThread()) { //判断锁是否存在，和是否当前线程加的锁。
                log.info("获取锁 后删除锁" + lock.getName());
                lock.unlock();
            }
        }


    }

    /**
     * 目标达成奖励，时长或里程奖励
     *
     * @param userRunDataDetail
     * @param user
     * @param activityUser
     * @param mainActivity
     * @param batchNo
     */
    private void goalCompleteAward(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity user, ZnsRunActivityUserEntity activityUser, MainActivity mainActivity, String batchNo) {
        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        //计算结束剩余时间
        int surplusTime = DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.addDays(endTime, 1));
        List<AwardConfigDto> awardConfigDtos = activityAwardConfigService.selectAwardConfigDtoListBySendTypes(activityUser.getActivityId(), Arrays.asList(AwardSentTypeEnum.TIME_AWARD.getType(), AwardSentTypeEnum.MILEAGE_AWARD.getType(), AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType()), null);
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            log.info("noTargetDataDeal end，无奖励配置");
            return;
        }
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), user.getLanguageCode());
        Integer target = activityUser.getTargetRunMileage();
        if (activityUser.getTargetRunTime() > 0) {
            target = activityUser.getTargetRunTime();
        }
        //里程奖励
        List<Integer> runMileageGoals = awardConfigDtos.stream().filter(a -> Objects.nonNull(a.getTargetMileage()) && a.getTargetMileage() > 0).map(AwardConfigDto::getTargetMileage).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(runMileageGoals)) {
            for (int i = 0; i < runMileageGoals.size(); i++) {
                Integer runMileageGoal = runMileageGoals.get(i);
                BigDecimal runMileage = activityUser.getRunMileage();
                log.info("用户跑步里程 runMileage: {}， activityUserEntity: {}, userRunDataDetail: {}", runMileage, activityUser.getRunMileage(), userRunDataDetail.getRunMileage());
                if (runMileage.intValue() >= runMileageGoal) {
                    noTargetDataDealSendAward(mainActivity, user, runMileageGoal, disseminate, activityUser, i, surplusTime, AwardSentTypeEnum.MILEAGE_AWARD.getType(), 1, target, runMileageGoals.size(), batchNo, userRunDataDetail.getId());
                } else {
                    //判断是否剩余20%以内
                    Integer surplus = runMileageGoal - runMileage.intValue();
                    BigDecimal surplusRatio = new BigDecimal(surplus).divide(new BigDecimal(runMileageGoal), 2, BigDecimal.ROUND_DOWN);
                    log.info("surplus = {},surplusRatio = {}", surplus, surplusRatio);

                    surplusNotification(user.getId(), mainActivity.getId(), surplus, surplusRatio, 1, runMileageGoal, surplusTime, user.getMeasureUnit());
                }
            }
        }

        //时长奖励
        List<Integer> timeGoals = awardConfigDtos.stream().filter(a -> Objects.nonNull(a.getTargetTime()) && a.getTargetTime() > 0).map(AwardConfigDto::getTargetTime).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(timeGoals)) {
            for (int i = 0; i < timeGoals.size(); i++) {
                Integer timeGoal = timeGoals.get(i);
                Integer runTime = activityUser.getRunTime();
                log.info("用户跑步时长 runTime: {}， activityUserEntity: {}, userRunDataDetail: {}", runTime, activityUser.getRunTime(), userRunDataDetail.getRunTime());
                if (runTime >= timeGoal) {
                    noTargetDataDealSendAward(mainActivity, user, timeGoal, disseminate, activityUser, i, surplusTime, AwardSentTypeEnum.TIME_AWARD.getType(), 2, target, timeGoals.size(), batchNo, userRunDataDetail.getId());
                } else {
                    //判断是否剩余20%以内
                    Integer surplus = timeGoal - runTime;
                    BigDecimal surplusRatio = new BigDecimal(surplus).divide(new BigDecimal(timeGoal), 2, BigDecimal.ROUND_DOWN);
                    log.info("surplus = {},surplusRatio = {}", surplus, surplusRatio);

                    surplusNotification(user.getId(), mainActivity.getId(), surplus, surplusRatio, 2, timeGoal, surplusTime, user.getMeasureUnit());
                }
            }
        }

        //个人阶段--场次奖励
        List<Integer> countGoals = awardConfigDtos.stream().filter(a -> Objects.nonNull(a.getTargetCount()) && a.getTargetCount() > 0).map(AwardConfigDto::getTargetCount).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(countGoals)) {
            List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(mainActivity.getId()).build());
            int runCount = 0;
            if (!CollectionUtils.isEmpty(list)) {
                List<Long> runActivityIds = list.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
                List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findList(RunActivityUserQuery.builder().activityIds(runActivityIds).userId(user.getId()).isComplete(1).build());
                runCount = activityUsers.size();
            }
            for (int i = 0; i < countGoals.size(); i++) {
                Integer countGoal = countGoals.get(i);
                log.info("用户跑步场次 countGoal: {}， activityUserEntity: {}, userRunDataDetail: {}", countGoal, activityUser.getRunMileage(), userRunDataDetail.getRunMileage());
                if (runCount >= countGoal) {
                    noTargetDataDealSendAward(mainActivity, user, countGoal, disseminate, activityUser, i, surplusTime, AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType(), 3, countGoal, runMileageGoals.size(), batchNo, userRunDataDetail.getId());
                } else {
                    //判断是否剩余20%以内
                    Integer surplus = countGoal - runCount;
                    BigDecimal surplusRatio = new BigDecimal(surplus).divide(new BigDecimal(countGoal), 2, BigDecimal.ROUND_DOWN);
                    log.info("surplus = {},surplusRatio = {}", surplus, surplusRatio);

                    surplusNotification(user.getId(), mainActivity.getId(), surplus, surplusRatio, 1, countGoal, surplusTime, user.getMeasureUnit());
                }
            }
        }
    }

    private void noTargetDataDealSendAward(MainActivity mainActivity, ZnsUserEntity user, Integer goal, ActivityDisseminate disseminate, ZnsRunActivityUserEntity activityUser,
                                           int i, int surplusTime, Integer type, int targetType, Integer target, int totalLevel, String batchNo, Long runDataDetailsId) {
        //判断是否已经发放奖励
        String key = RedisConstants._OFFICIAL_CUMULATIVE_RUN_GOAL_AWARD + mainActivity.getId() + "_" + user.getId() + "_" + 1 + "_" + goal;
        boolean exists = redissonClient.getBucket(key).isExists();
        if (exists) {
            return;
        }
        AwardSendDto awardSendDto = new AwardSendDto();
        awardSendDto.setActivityId(mainActivity.getId());
        awardSendDto.setUserId(user.getId());
        awardSendDto.setTargetType(targetType);
        if (Objects.nonNull(target) && target > 0) {
            awardSendDto.setTarget(target);
        }
        awardSendDto.setType(type);
        if (awardSendDto.getType().equals(AwardSentTypeEnum.MILEAGE_AWARD.getType())) {
            awardSendDto.setTargetMileage(goal);
        } else if (awardSendDto.getType().equals(AwardSentTypeEnum.TIME_AWARD.getType())) {
            awardSendDto.setTargetTime(goal);
        } else if (awardSendDto.getType().equals(AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType())) {
            awardSendDto.setTargetCount(goal);
        }
        awardSendDto.setTotalBatchNo(batchNo);
        awardSendDto.setDetailId(runDataDetailsId);
        awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);

        //保存关卡
        ZnsRunActivityUserEntity update = new ZnsRunActivityUserEntity();
        update.setId(activityUser.getId());
        update.setModifieTime(ZonedDateTime.now());
        if (targetType == 1) {
            update.setCompletedLevel(i + 1);
        }

        if (mainActivity.getTargetType() == 0 && Objects.nonNull(update.getCompletedLevel()) && update.getCompletedLevel() >= totalLevel) {
            update.setIsComplete(1);
            update.setCompleteTime(ZonedDateTime.now());
        }
        runActivityUserService.updateById(update);

        //点亮城市
        awardActivityBizService.sendUserLightCity(disseminate, user, update.getCompletedLevel());
        if (surplusTime < 0) {
            surplusTime = 0;
        }
        redissonClient.getBucket(key).set("1", surplusTime, TimeUnit.SECONDS);
        String awardPopKey = String.format(RedisKeyConstant.USER_REWARD_POP, runDataDetailsId);
        redissonClient.getBucket(awardPopKey).set("1", surplusTime, TimeUnit.SECONDS);
    }

    private void surplusNotification(Long userId, Long activityId, Integer surplus, BigDecimal surplusRatio, int targetType, Integer goal, int surplusTime, Integer measureUnit) {
        if (surplusRatio.compareTo(new BigDecimal(0.2)) >= 0) {
            log.info("surplusNotification end, 剩余目标超过20%");
            return;
        }

        //防止重复通知
        String key = RedisConstants.SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN_NOTIFICATION + activityId + "_" + userId + "_" + targetType + "_" + goal;
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (!bucket.isExists()) {
            return;
        }
        String surplusGoal = "";
        if (targetType == 1) {
            surplusGoal = new BigDecimal(surplus).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_DOWN).toString() + " km";
            if (measureUnit == 1) {
                surplusGoal = new BigDecimal(surplus).divide(new BigDecimal(1600), 2, BigDecimal.ROUND_HALF_DOWN).toString() + " miles";
            }
        } else if (targetType == 2) {
            surplusGoal = surplus / 60 + " min";
        }

        //通知【Only %s left to the next milestone! Come on! Bonus is waiting for you!】
        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN;
        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN"), surplusGoal);
        MessageBo message = appMessageService.assembleMessage(activityId, content, "4", NoticeTypeEnum.ACTIVITY_INVITATION.getType());
        message.setActivityId(activityId);
        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
        activityEntity.setId(activityId);
        activityEntity.setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType());
        ImMessageBo imMessageBo = appMessageService.assembleImActivityMessage(activityEntity, content);
        appMessageService.sendImAndPushUserIds(Arrays.asList(userId), imMessageBo, message);
        if (surplusTime < 0) {
            surplusTime = 24 * 60 * 60;
        }
        bucket.set("1", surplusTime, TimeUnit.SECONDS);
    }

    public Integer closedPropActivity(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (mainActivity == null) {
            //比赛不存在
            return null;
        }
        Integer activityState = mainActivity.getActivityState();
        if (!MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
            //不是道具赛
            return activityState;
        }
        if (!MainActivityStateEnum.STARTED.getCode().equals(activityState)) {
            //活动不是运动中状态
            return activityState;
        }

        //查询未完赛的记录
        NoEndRunDetailsQuery query = new NoEndRunDetailsQuery();
        query.setActivityId(activityId);
        List<ZnsUserRunDataDetailsEntity> detailsEntities = runDataDetailsService.findNoEndDetails(query);
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(detailsEntities)) {
            //还有用户未完赛
            return activityState;
        }
        //更新活动为已结束
        MainActivity updateMainActivity = new MainActivity();
        updateMainActivity.setId(activityId);
        updateMainActivity.setActivityState(MainActivityStateEnum.ENDED.getCode());
        mainActivityService.update(updateMainActivity);
        return MainActivityStateEnum.ENDED.getCode();
    }


    public List<AwardReviewUserDto> recalculate(AwardSendRecalculateRequest request) {
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(request.getActivityId());
        Map<Long, ZnsRunActivityUserEntity> activityUserEntityMap = allActivityUser.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, Function.identity(), (v1, v2) -> v1));
        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        ActivityResultBusiness.ActivityContext context = activityResultBusiness.buildActivityContext(request.getActivityId());
        Map<Long, ZnsUserEntity> userMap = userService.findUserMap(userIds);
        Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap = activityResultBusiness.getUserRunDataMap(context, userIds);
        Map<Long, Long> userCheatHitMap = activityResultBusiness.getUserCheatHitMap(userRunDataDetailsMap);

        List<ActivityGoalRankVo> activityGoalRankVos = activityRank(context.getMainActivity().getId(), request.getDisqualifiedLists());
        if (CollectionUtils.isEmpty(activityGoalRankVos)) {
            return new ArrayList<>();
        }

        ActivityGoalRankVo activityGoalRankVo = activityGoalRankVos.get(0);
        List<AwardConfigDto> awardConfigs = activityResultBusiness.getAwardConfigs(context.getMainActivity().getId());

        // 获取排名 正向排名 by rank limit 1000 获取前1000名
        List<ActivityUserRankVo> collected = activityGoalRankVo.getUserRankList().stream().sorted(Comparator.comparing(ActivityUserRankVo::getRank)).limit(1000).toList();
        List<ZnsRunActivityUserEntity> list = collected.stream().map(c -> {
            ZnsRunActivityUserEntity activityUser = activityUserEntityMap.get(c.getUserId());
            activityUser.setRank(c.getRank());
            return activityUser;
        }).collect(Collectors.toList());

        ActivityResultBusiness.ActivityResultContext resultContext = activityResultBusiness.buildActivityResultContext(request.getActivityId(), list);

        List<AwardReviewUserDto> result = activityResultBusiness.buildReviewResults(list, context, resultContext, awardConfigs, userMap, userCheatHitMap, userRunDataDetailsMap);

        // 废弃用户成绩展示
        request.getDisqualifiedLists().forEach(i -> {
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUserWithNoState(i, request.getActivityId());
            AwardReviewUserDto awardReviewUserDto = activityResultBusiness.getAwardReviewUserDto(activityUser, userMap.get(activityUser.getUserId()), context.getRankingBy(), userCheatHitMap, userRunDataDetailsMap);
            awardReviewUserDto.setRank(null);
            awardReviewUserDto.setStatus(-1);
            result.add(awardReviewUserDto);
        });
        return result;
    }

    private Map<Long, Integer> getUserCheatMap(Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap) {
        Map<Long, Integer> userCheatMap = new HashMap<>(userRunDataDetailsMap.size());
        for (Map.Entry<Long, List<ZnsUserRunDataDetailsEntity>> entry : userRunDataDetailsMap.entrySet()) {
            Long cheatCount = entry.getValue().stream().filter(d -> d.getIsCheat() == 1).count();
            userCheatMap.put(entry.getKey(), cheatCount.intValue());
        }
        return userCheatMap;

    }

    public List<AwardReviewUserTeamDto> teamRecalculate(AwardSendRecalculateRequest request) {
        if (CollectionUtils.isEmpty(request.getDisqualifiedLists())) {
            log.info("不需要重新计算排名，无作废用户成绩");
            throw new BaseException("不需要重新计算排名，无作废用户成绩");
        }
        List<AwardReviewUserTeamDto> result = new ArrayList<>();
        List<ActivityTeamUserRankVo> activityTeamUserRankVos = teamActivityRank(request.getActivityId(), request.getDisqualifiedLists());
        ActivityResultBusiness.ActivityContext context = activityResultBusiness.buildActivityContext(request.getActivityId());
        String rankingBy = context.getRankingBy();
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(request.getActivityId());
        Map<Long, ZnsRunActivityUserEntity> activityUserEntityMap = allActivityUser.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, Function.identity(), (v1, v2) -> v1));
        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        Map<Long, ZnsUserEntity> userMap = userService.findUserMap(userIds);
        Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap = activityResultBusiness.getUserRunDataMap(context, userIds);
        Map<Long, Long> userCheatHitMap = activityResultBusiness.getUserCheatHitMap(userRunDataDetailsMap);

        // 获取排名 正向排名 by rank
        activityTeamUserRankVos = activityTeamUserRankVos.stream().sorted(Comparator.comparing(ActivityTeamUserRankVo::getRank)).limit(1000).toList();
        List<AwardReviewUserTeamDto> finalRecords = result;
        activityTeamUserRankVos.forEach(i -> {
            AwardReviewUserTeamDto awardReviewUserTeamDto = getAwardReviewUserTeamDto(i, activityUserEntityMap.get(i.getUserId()), userMap.get(i.getUserId()), context.getRankingBy(), userCheatHitMap, userRunDataDetailsMap);
            awardReviewUserTeamDto.setCheatingState(YesNoStatus.NO.getCode());
            finalRecords.add(awardReviewUserTeamDto);
        });

        result = sortedTeamRecord(finalRecords, rankingBy);

        // 废弃用户成绩展示
        List<AwardReviewUserTeamDto> finalRecords1 = new ArrayList<>(result);
        request.getDisqualifiedLists().forEach(i -> {
            ZnsRunActivityUserEntity activityUser = activityUserEntityMap.get(i);
            AwardReviewUserDto awardReviewUserDto = activityResultBusiness.getAwardReviewUserDto(activityUser, userMap.get(i), rankingBy, userCheatHitMap, userRunDataDetailsMap);
            AwardReviewUserTeamDto awardReviewUserTeamDto = new AwardReviewUserTeamDto();
            BeanUtils.copyProperties(awardReviewUserDto, awardReviewUserTeamDto);
            ActivityTeam activityTeam = activityTeamService.findById(activityUser.getTeamId());
            awardReviewUserTeamDto.setTeamName(activityTeam.getTeamName());
            awardReviewUserTeamDto.setRank(null);
            awardReviewUserTeamDto.setStatus(-1);
            awardReviewUserDto.setCheatingState(YesNoStatus.YES.getCode());
            finalRecords1.add(awardReviewUserTeamDto);
        });
        return finalRecords1;
    }

    private List<AwardReviewUserTeamDto> sortedTeamRecord(List<AwardReviewUserTeamDto> finalRecords, String rankingBy) {
        return finalRecords.stream().sorted(Comparator.comparing(AwardReviewUserTeamDto::getCheatingState)
                .thenComparing(AwardReviewUserTeamDto::getRank)
                .thenComparing(AwardReviewUserTeamDto::getTeamName)
                .thenComparing(AwardReviewUserTeamDto::getRaceResult, Comparator.nullsLast((a, b) -> {
                    if (a.equals("0") || b.equals("0")) {
                        // 如果有一个是 "0"，则 "0" 在后面
                        return a.equals("0") ? 1 : (b.equals("0") ? -1 : 0);
                    }
                    switch (rankingBy) {
                        case "1": // 里程 倒序
                            // 数字比较，直接转换成 Double 进行比较
                            return Double.compare(Double.parseDouble(b), Double.parseDouble(a));
                        case "2": // 时长 倒序
                            // 数字比较，直接转换成 Double 进行比较
                            return Double.compare(Double.parseDouble(b), Double.parseDouble(a));
                        case "3": // 时间类型，正序
                            try {
                                // 假设时间格式是 "HH:mm:ss"
                                LocalTime timeA = LocalTime.parse(a, DateTimeFormatter.ofPattern("HH:mm:ss"));
                                LocalTime timeB = LocalTime.parse(b, DateTimeFormatter.ofPattern("HH:mm:ss"));
                                return timeA.compareTo(timeB);
                            } catch (DateTimeParseException e) {
                                // 处理解析异常
                                log.error("Invalid time format", e);
                            }
                        default:
                            // 默认情况下，按字符串比较
                            return a.compareTo(b);
                    }
                }))).toList();
    }


    public List<AwardReviewUserTeamDto> queryTeamReviewList(ActivityIdRequest request) {
        List<AwardReviewUserTeamDto> records = new ArrayList<>();
        ActivityResultBusiness.ActivityContext context = activityResultBusiness.buildActivityContext(request.getActivityId());
        String rankingBy = context.getRankingBy();
        List<Long> cheatUserIdList;
        if (context.getMainActivity().getAwardSendStatus() == 0) {
            cheatUserIdList = new ArrayList<>();
        } else {
            cheatUserIdList = activityUserAwardPreService.cheatUserIdListList(request.getActivityId()).stream().map(ActivityUserAwardPre::getUserId).toList();
        }
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(request.getActivityId());
        Map<Long, ZnsRunActivityUserEntity> activityUserEntityMap = allActivityUser.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, Function.identity(), (v1, v2) -> v1));
        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        Map<Long, ZnsUserEntity> userMap = userService.findUserMap(userIds);
        Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap = activityResultBusiness.getUserRunDataMap(context, userIds);
        Map<Long, Long> userCheatHitMap = activityResultBusiness.getUserCheatHitMap(userRunDataDetailsMap);

        List<ActivityTeamUserRankVo> activityTeamUserRankVos = teamActivityRank(request.getActivityId(), cheatUserIdList);
        // 获取排名 正向排名 by rank
        activityTeamUserRankVos = activityTeamUserRankVos.stream().sorted(Comparator.comparing(ActivityTeamUserRankVo::getRank)).limit(request.getPageSize()).toList();
        List<AwardReviewUserTeamDto> finalRecords = records;
        activityTeamUserRankVos.forEach(i -> {
            AwardReviewUserTeamDto awardReviewUserTeamDto = getAwardReviewUserTeamDto(i, activityUserEntityMap.get(i.getUserId()), userMap.get(i.getUserId()), context.getRankingBy(), userCheatHitMap, userRunDataDetailsMap);
            if (awardReviewUserTeamDto.getRank() == -1) {
                if (context.getMainActivity().getAwardSendStatus().equals(YesNoStatus.YES.getCode())) {
                    awardReviewUserTeamDto.setStatus(-1);
                }
                awardReviewUserTeamDto.setCheatingState(YesNoStatus.YES.getCode());
            }
            finalRecords.add(awardReviewUserTeamDto);
        });

        records = sortedTeamRecord(finalRecords, rankingBy);

        // 废弃用户成绩展示
        List<AwardReviewUserTeamDto> finalRecords1 = new ArrayList<>(records);
        if (context.getMainActivity().getAwardSendStatus() == 1) {
            List<ActivityUserAwardPre> activityUserAwardPres = activityUserAwardPreService.cheatUserIdListList(request.getActivityId());
            activityUserAwardPres.forEach(i -> {
                ZnsRunActivityUserEntity activityUser = activityUserEntityMap.get(i.getUserId());
                AwardReviewUserTeamDto awardReviewUserTeamDto = getAwardReviewUserTeamDto(new ActivityTeamUserRankVo(), activityUserEntityMap.get(i.getUserId()), userMap.get(i.getUserId()), context.getRankingBy(), userCheatHitMap, userRunDataDetailsMap);
                ActivityTeam activityTeam = activityTeamService.findById(activityUser.getTeamId());

                awardReviewUserTeamDto.setTeamName(activityTeam.getTeamName());
                awardReviewUserTeamDto.setRank(null);
                awardReviewUserTeamDto.setStatus(-1);
                finalRecords1.add(awardReviewUserTeamDto);
            });
        }
        return finalRecords1;
    }

    private AwardReviewUserTeamDto getAwardReviewUserTeamDto(ActivityTeamUserRankVo rankVo, ZnsRunActivityUserEntity activityUser, ZnsUserEntity znsUserEntity, String rankingBy, Map<Long, Long> userCheatHitMap, Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap) {
        AwardReviewUserDto awardReviewUserDto = activityResultBusiness.getAwardReviewUserDto(activityUser, znsUserEntity, rankingBy, userCheatHitMap, userRunDataDetailsMap);
        AwardReviewUserTeamDto awardReviewUserTeamDto = new AwardReviewUserTeamDto();
        BeanUtils.copyProperties(awardReviewUserDto, awardReviewUserTeamDto);
        awardReviewUserTeamDto.setRank(rankVo.getRank());
        awardReviewUserTeamDto.setRewardAmounts(rankVo.getAmount());
        awardReviewUserTeamDto.setRewardCurrency(rankVo.getCurrency());
        awardReviewUserTeamDto.setRewardPoints(rankVo.getScore());
        awardReviewUserTeamDto.setTeamName(rankVo.getTeamName());
        awardReviewUserTeamDto.setRewardTeamPoints(rankVo.getTeamScore());
        awardReviewUserTeamDto.setRewardTeamAmounts(rankVo.getTeamAmount());
        awardReviewUserTeamDto.setRewardTeamCurrency(rankVo.getCurrency());
        awardReviewUserTeamDto.setCheatingState(YesNoStatus.NO.getCode());
        awardReviewUserTeamDto.setUserId(activityUser.getUserId());
        return awardReviewUserTeamDto;
    }

    @Transactional
    public MainActivity review(AwardSendRecalculateRequest request, SysUser user) {
        MainActivity mainActivity = mainActivityService.findById(request.getActivityId());
        String lockKey = RedisConstants.ACTIVITY_SEND_AWARD_REVIEW_ACTIVITY_LOCK + mainActivity.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean tryLock = false;
        try {
            tryLock = lock.tryLock(1, 3600, TimeUnit.SECONDS);
            if (!tryLock) {
                log.info("activityEnd getLock 失败");
                return mainActivity;
            }
            mainActivity = mainActivityService.findById(request.getActivityId());
            if (mainActivity.getAwardSendStatus().equals(AwardSendStatusEnum.NO_VIEW.getCode())) {
                mainActivity.setAwardSendStatus(AwardSendStatusEnum.VIEW_SENDING.getCode());
                mainActivityService.update(mainActivity);
                // 修改风控审核状态
                runCheatBizService.reviewCheatByActivityId(mainActivity);
                ActivityUserAwardReviewLog activityUserAwardReviewLog = new ActivityUserAwardReviewLog();
                activityUserAwardReviewLog.setActivityId(request.getActivityId());
                activityUserAwardReviewLog.setReviewName(user.getNickName());
                activityUserAwardReviewLog.setStatus(YesNoStatus.YES.getCode());
                activityUserAwardReviewLogService.insert(activityUserAwardReviewLog);
                List<Long> disqualifiedLists = request.getDisqualifiedLists();
                disqualifiedLists.forEach(i -> {
                    var awardPre = activityUserAwardPreService.selectByActivityIdAndUserId(request.getActivityId(), i);
                    if (Objects.nonNull(awardPre)) {
                        awardPre.setStatus(-1);
                        activityUserAwardPreService.update(awardPre);
                    }
                });
                activityEndSendAward(mainActivity, disqualifiedLists, true);
                mainActivity.setAwardSendStatus(AwardSendStatusEnum.VIEW_PASS.getCode());
                mainActivityService.update(mainActivity);
            }
        } catch (Exception e) {
            log.error("review error,e=", e);
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return mainActivity;
    }

    public void exportTeamReviewList(ActivityIdRequest activityIdRequest, HttpServletResponse response, HttpServletRequest request) {
        List<AwardReviewUserTeamDto> list = queryTeamReviewList(activityIdRequest);
        ExcelUtil<AwardReviewUserTeamDto> util = new ExcelUtil<AwardReviewUserTeamDto>(AwardReviewUserTeamDto.class);
        MainActivity mainActivity = mainActivityService.findById(activityIdRequest.getActivityId());
        String fileName = "奖励审核" + mainActivity.getRemark() + AwardSendStatusEnum.findDesc(mainActivity.getAwardSendStatus());
        util.exportExcel(list, fileName, fileName, response, request);
    }

    public List<AwardReviewUserStageDto> queryStageReviewList(Long activityId, Integer status) {
        return activityStageBusiness.queryStageReviewList(activityId, status != 0);
    }

    public List<AwardReviewUserStageDto> stageRecalculate(AwardSendStageRecalculateRequest request) {

        return activityStageBusiness.stageRecalculatePreview(request.getActivityId(), request.getDisqualifiedLists());

    }

    @Transactional
    public MainActivity stageReview(AwardSendStageRecalculateRequest request, SysUser user) {
        MainActivity mainActivity = mainActivityService.findById(request.getActivityId());
        String lockKey = RedisConstants.ACTIVITY_SEND_AWARD_REVIEW_ACTIVITY_LOCK + mainActivity.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean tryLock = false;
        try {
            tryLock = lock.tryLock(1, 3600, TimeUnit.SECONDS);
            if (!tryLock) {
                log.info("activityEnd getLock 失败");
                return mainActivity;
            }
            mainActivity = mainActivityService.findById(request.getActivityId());
            if (mainActivity.getAwardSendStatus().equals(AwardSendStatusEnum.NO_VIEW.getCode())) {
                mainActivity.setAwardSendStatus(AwardSendStatusEnum.VIEW_SENDING.getCode());
                mainActivityService.update(mainActivity);
                // 修改风控审核状态
                runCheatBizService.reviewCheatByActivityId(mainActivity);

                ActivityUserAwardReviewLog activityUserAwardReviewLog = new ActivityUserAwardReviewLog();
                activityUserAwardReviewLog.setActivityId(request.getActivityId());
                activityUserAwardReviewLog.setReviewName(user.getNickName());
                activityUserAwardReviewLog.setStatus(YesNoStatus.YES.getCode());
                activityUserAwardReviewLogService.insert(activityUserAwardReviewLog);

                List<Long> disqualifiedLists = activityStageBusiness.stageRecalculate(request.getActivityId(), request.getDisqualifiedLists());

                activityEndSendAward(mainActivity, disqualifiedLists, true);
                mainActivity.setAwardSendStatus(AwardSendStatusEnum.VIEW_PASS.getCode());
                mainActivityService.update(mainActivity);
            }
        } catch (Exception e) {
            log.error("review error,e=", e);
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return mainActivity;
    }

    //俱乐部团赛预计最大金额
    public BigDecimal clubExpectedTotalAward(Long activityId, Integer clubMemberNums) {
        //计算奖金
        BigDecimal awardAmount = BigDecimal.ZERO;
        //奖励配置
        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(activityId);
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        if (CollectionUtils.isEmpty(configs)) {
            return awardAmount;
        }
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);

        awardConfigDtos = awardConfigDtos.stream().filter(a -> AwardSentTypeEnum.RANKING_BASED_REWARDS.getType().equals(a.getSendType())
                || AwardSentTypeEnum.RANKING_HEAD_REWARD.getType().equals(a.getSendType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            log.info("teamActivityRank 无奖励配置");
            return awardAmount;
        }
        //找到活动的最大目标
        List<Integer> singleActivityTargets = subActivityService.getSingleActivityTargets(activityId);
        Integer target = singleActivityTargets.stream().max(Integer::compareTo).orElse(-1);

        Currency currency = new Currency(I18nConstant.CurrencyCodeEnum.USD.getName(), I18nConstant.CurrencyCodeEnum.USD.getCode(), I18nConstant.CurrencyCodeEnum.USD.getSymbol());
        BigDecimal baseReward = getAmountAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), currency, 1);
        BigDecimal headReward = getAmountAward(target, awardConfigDtos, AwardSentTypeEnum.RANKING_HEAD_REWARD.getType(), currency, 1);
        if (Objects.nonNull(baseReward) && baseReward.compareTo(BigDecimal.ZERO) > 0) {
            awardAmount = awardAmount.add(baseReward);
        }
        if (Objects.nonNull(headReward) && headReward.compareTo(BigDecimal.ZERO) > 0) {
            awardAmount = BigDecimalUtil.multiply(headReward, BigDecimal.valueOf(clubMemberNums)).add(awardAmount);
        }
        return awardAmount;
    }


    public void changeCheat(ChangeCheatRequestDto requestDto) {
        ZnsUserRunDataDetailsEntity userRunDataDetailsEntity = new ZnsUserRunDataDetailsEntity();
        userRunDataDetailsEntity.setId(requestDto.getUserRunDataDetailsId());
        userRunDataDetailsEntity.setIsCheat(requestDto.getIsCheat());
        runDataDetailsService.update(userRunDataDetailsEntity);

        RealPersonRunDataDetails details = realPersonRunDataDetailsService.findByDetailsId(requestDto.getUserRunDataDetailsId());
        details.setIsCheat(requestDto.getIsCheat());
        realPersonRunDataDetailsService.update(details);
        MainActivity mainActivity = mainActivityService.findById(requestDto.getActivityId());
        // 子活动id
        Long activityId = requestDto.getActivityId();
        if (mainActivity.getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())) {
            activityId = details.getActivityId();
        }

        ActivityResultBusiness.ActivityContext context = activityResultBusiness.buildActivityContext(requestDto.getActivityId());
        context.setActivityIdList(Collections.singletonList(activityId));
        List<ZnsUserRunDataDetailsEntity> userRunDataDetailsEntities = activityResultBusiness.getUserRunData(context, Collections.singletonList(details.getUserId()), 0);

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, details.getUserId());
        ZonedDateTime startTime = activityUser.getCreateTime();
        if (!StringUtils.isEmpty(mainActivity.getActivityStartTime()) && DateTimeUtil.parse(mainActivity.getActivityStartTime()).isAfter(startTime)) {
            startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
        }
        ZonedDateTime finalStartTime = startTime;
        userRunDataDetailsEntities = userRunDataDetailsEntities.stream().filter(d -> d.getLastTime().isAfter(finalStartTime)).collect(Collectors.toList());

        Map<Long, UserPropRecordDto> propMap = activityResultBusiness.getPropRecordMap(activityId);
        //更新成绩排名
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        activityResultBusiness.resetUserEntryResult(entryGameplay, activityUser, userRunDataDetailsEntities, propMap);
        if (!NumberUtils.geZero(activityUser.getTargetRunTime()) && !NumberUtils.geZero(activityUser.getTargetRunMileage())) {
            //无目标情况下不修改是否完成字段，防止影响到客户端展示
            activityUser.setIsComplete(null);
        }
        runActivityUserService.updateById(activityUser);

        //修改风控检测中记录
        runCheatBizService.reviewCheat(details);
        //更新系列赛排名
        if (mainActivity.getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())) {
            // 主活动修改
            ZnsRunActivityUserEntity mainActivityUser = runActivityUserService.findActivityUser(requestDto.getActivityId(), details.getUserId());
            mainActivityUser.setIsComplete(0);
            mainActivityUser.setCompleteTime(null);
            updateSeriesActivityGrade(mainActivityUser);
            //查询子赛事
            List<Long> activityIdList = seriesActivityRelService.findSubActivityId(mainActivity.getId());
            List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIdList, details.getUserId());
            long noCompleteCount = activityUsers.stream().filter(a -> a.getIsComplete() == 0).count();

            if (noCompleteCount == 0 && activityIdList.size() == activityUsers.size()) {
                mainActivityUser.setIsComplete(1);
                ZnsRunActivityUserEntity lastActivityUser = activityUsers.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getId).reversed()).findFirst().get();
                mainActivityUser.setCompleteTime(lastActivityUser.getCompleteTime());
                mainActivityUser.setRunDataDetailsId(requestDto.getUserRunDataDetailsId());
                runActivityUserService.updateById(mainActivityUser);
            }
        }

        //阶段判断
        if (NumberUtils.geZero(requestDto.getStageId())) {
            RunActivityStageUser stageUser = runActivityStageUserService.findByQuery(new RunActivityStageUserQuery().setRunDataDetailsId(requestDto.getUserRunDataDetailsId()));
            if (Objects.nonNull(stageUser)) {
                stageUser.setIsCheat(requestDto.getIsCheat());
                ZnsUserRunDataDetailsEntity runDataDetails = runDataDetailsService.findById(requestDto.getUserRunDataDetailsId());
                if (activityResultBusiness.completeTarget(runDataDetails, activityUser)) {
                    stageUser.setIsComplete(1);
                } else {
                    stageUser.setIsComplete(0);
                }
                runActivityStageUserService.update(stageUser);
                activityStageBusiness.reRank(requestDto.getActivityId(), requestDto.getStageId(), true);
                activityStageBusiness.reRank(requestDto.getActivityId());
            }
        }
    }


}
