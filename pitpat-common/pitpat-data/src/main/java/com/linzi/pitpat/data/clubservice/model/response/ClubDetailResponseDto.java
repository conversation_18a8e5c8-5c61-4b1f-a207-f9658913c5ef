package com.linzi.pitpat.data.clubservice.model.response;

import com.linzi.pitpat.data.clubservice.constant.enums.ClubStateEnum;
import com.linzi.pitpat.data.filler.ClubLevelDataFiller;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.user.UserEmailAddressDataFiller;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 俱乐部详情响应DTO
 */
@Data
public class ClubDetailResponseDto {
    
    /**
     * 俱乐部ID
     */
    private Long clubId;
    
    /**
     * 俱乐部名称
     */
    private String name;
    
    /**
     * 俱乐部描述
     */
    private String description;
    
    /**
     * 俱乐部LOGO
     */
    private String logo;
    
    /**
     * 俱乐部等级
     */
    private String clubLevel;
    
    /**
     * 俱乐部等级名称
     */
    @Filler(relationFieldName = "clubLevel", filler = ClubLevelDataFiller.class)
    private String clubLevelName;
    
    /**
     * 俱乐部状态 normal（正常）, frozen（冻结）, disbanded（解散）
     * @see ClubStateEnum
     */
    private String state;
    
    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;
    
    /**
     * 修改时间
     */
    private ZonedDateTime gmtModified;
    
    /**
     * 俱乐部负责人ID
     */
    private Long ownerUserId;
    
    /**
     * 负责人邮箱地址
     */
    @Filler(relationFieldName = "ownerUserId", filler = UserEmailAddressDataFiller.class)
    private String ownerEmailAddress;
    
    /**
     * 俱乐部会员人数
     */
    private Integer memberCount;
    
    /**
     * 俱乐部参赛次数
     */
    private Integer matchCount;
    
    /**
     * 邀请码
     */
    private String inviteCode;
    
    /**
     * 申请是否需要邀请码
     */
    private Boolean requiredInviteCode;
    
    /**
     * 申请是否需要审核
     */
    private Boolean requiresApproval;
    
    /**
     * 俱乐部操作备注
     */
    private String operateNote;
    
    /**
     * 是否推荐(0:不推荐，1：推荐)
     */
    private Integer isRecommend;
    
    /**
     * 俱乐部类型（0：默认，1：新用户参加一次）
     */
    private Integer type;

    

} 