/**
 * @description: APP用户跑步信息
 * @author: yangpeng projectName: pitpat-server fileName: AppUserRunInfo.java packageName:
 * com.linzi.pitpat.admin.entity.dto date: 2022-02-16 10:33 AM copyright(c) 2018-2020
 * 杭州霖扬网络科技有限公司版权所有
 */
package com.linzi.pitpat.data.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * @description: APP用户跑步信息
 * @author: yangpeng
 * @className: AppUserRunInfo
 * @packageName: com.linzi.pitpat.admin.entity.dto
 * @version: V1.0
 * @date: 2022-02-16 10:33 AM
 **/

@Data
@NoArgsConstructor
public class AppUserRunInfo {
    /**
     * 最新跑步时间
     */
    private ZonedDateTime latestRunTime;
    /**
     * 总的跑步时长(s)
     */
    private Integer totalRunTime;
    /**
     * 总的跑步里程(m)
     */
    private BigDecimal totalRunMileage;
    /**
     * 总的跑步次数
     */
    private Integer totalRunCount;
    /**
     * 总消耗卡路里
     */
    private BigDecimal totalKilocalorie;

    /**
     * 最近30天跑步时长(s)
     */
    private Integer lastThirtyRunTime;
    /**
     * 最近30天跑步里程(m)
     */
    private BigDecimal lastThirtyRunMileage;
    /**
     * 最近30天跑步次数
     */
    private Integer lastThirtyRunCount;
    /**
     * 最近30天消耗卡路里
     */
    private BigDecimal lastThirtyKilocalorie;

    /**
     * 最近7天跑步时长(s)
     */
    private Integer lastSevenRunTime;
    /**
     * 最近7天跑步里程(m)
     */
    private BigDecimal lastSevenRunMileage;
    /**
     * 最近7天跑步次数
     */
    private Integer lastSevenRunCount;
    /**
     * 最近7天耗卡路里
     */
    private BigDecimal lastSevenKilocalorie;
    /**
     * 平均配速
     */
    private Integer averagePace;
    /**
     * 最高配速
     */
    private Integer maxPace;

}
