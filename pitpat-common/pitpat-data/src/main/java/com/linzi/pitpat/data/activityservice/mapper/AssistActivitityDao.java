package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.vo.AssistActivityDropListVo;
import com.lz.mybatis.plugin.annotations.GE;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface AssistActivitityDao extends BaseMapper<AssistActivitity> {


    AssistActivitity selectAssistActivitityById(@Param("id") Long id);

    int updateAssistActivitityById(AssistActivitity assistActivitity);

    List<AssistActivityDropListVo> selectByStatusAndGmtEnd(Integer status, @GE ZonedDateTime endTime);
}
