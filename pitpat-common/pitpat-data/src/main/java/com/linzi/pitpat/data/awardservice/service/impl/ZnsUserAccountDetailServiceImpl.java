package com.linzi.pitpat.data.awardservice.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.awardservice.dto.ActivityUserPairDto;
import com.linzi.pitpat.data.awardservice.dto.UserAmountPair;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDao;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDetailDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivitySumAwardBySubTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.AccountDetailPo;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.model.vo.AccountTotalVo;
import com.linzi.pitpat.data.awardservice.model.vo.AccountWithdrawalVo;
import com.linzi.pitpat.data.awardservice.model.vo.InitCurrencyBo;
import com.linzi.pitpat.data.awardservice.model.vo.UserAccountSimpleVo;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.domian.bo.exchangeRate.ExchangeRateBo;
import com.linzi.pitpat.data.entity.dto.AppUserConsumeInfo;
import com.linzi.pitpat.data.entity.dto.AppUserDto;
import com.linzi.pitpat.data.entity.vo.UserConsumeStatisticVo;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentModelAwardDataVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentAwardDataVo;
import com.linzi.pitpat.data.query.exchangeRate.ExchangeRateQuery;
import com.linzi.pitpat.data.resp.LoopsResp;
import com.linzi.pitpat.data.resp.RunAwardDto;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.useractive.RunDataYearVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


@Service("znsUserAccountDetailService")
@Slf4j
public class ZnsUserAccountDetailServiceImpl implements ZnsUserAccountDetailService {


    @Autowired
    private ZnsUserAccountDao znsUserAccountDao;
    @Autowired
    private ZnsUserAccountDetailDao znsUserAccountDetailDao;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ZnsUserDao znsUserDao;
    @Autowired
    private ExchangeRateConfigService exchangeRateConfigService;
    @Resource
    private RedissonClient redissonClient;

    //多币种初始化 消息队列
    @Value("${" + RabbitQueueConstants.CURRENCY_INIT_QUEUE + "}")
    private String currency_init_queue;

    @Override
    public Page getAccountDetail(Integer pageNum, Integer pageSize, Long userId, Integer type, ZonedDateTime startTime, Long userAccountId) {
        Page page = new Page<>(pageNum, pageSize);

        page = znsUserAccountDetailDao.selectPage(page, Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .select(ZnsUserAccountDetailEntity::getId, ZnsUserAccountDetailEntity::getCreateTime, ZnsUserAccountDetailEntity::getTitle, ZnsUserAccountDetailEntity::getType,
                        ZnsUserAccountDetailEntity::getAmount, ZnsUserAccountDetailEntity::getRemark, ZnsUserAccountDetailEntity::getBillNo,
                        ZnsUserAccountDetailEntity::getRefundStatus, ZnsUserAccountDetailEntity::getTradeStatus,
                        ZnsUserAccountDetailEntity::getTradeType, ZnsUserAccountDetailEntity::getTradeSubtype,
                        ZnsUserAccountDetailEntity::getActivityType)
                .eq(Objects.nonNull(type) && type != 0, ZnsUserAccountDetailEntity::getType, type)
                .eq(ZnsUserAccountDetailEntity::getIsDelete, 0)
                .eq(ZnsUserAccountDetailEntity::getUserId, userId)
                .eq(Objects.nonNull(userAccountId), ZnsUserAccountDetailEntity::getUserAccountId, userAccountId)
                .in(ZnsUserAccountDetailEntity::getTradeStatus, 1, 2)
                .le(Objects.nonNull(startTime), ZnsUserAccountDetailEntity::getCreateTime, startTime)
                .orderByDesc(ZnsUserAccountDetailEntity::getCreateTime));

        return page;
    }

    /**
     * 获取查询开始时间
     *
     * @param userId
     * @param type
     * @param year
     * @param month
     * @return
     */
    @Override
    public ZonedDateTime getStartTime(Long userId, Integer type, Integer year, Integer month) {
        if (Objects.isNull(month) || Objects.isNull(month)) {
            return null;
        }
        LocalDate date = LocalDate.of(year, month, 1);
        ZonedDateTime startTime = ZonedDateTime.of(date.atStartOfDay(), ZoneId.systemDefault()).with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
        return startTime;
    }

    @Override
    public Map<String, Object> getMonthData(Long userId, String timeStr, Long userAccountId) {
        try {
            ZonedDateTime startTime = DateTimeUtil.parseYearMonthWithPattern(timeStr, "yyyy年MM月");
            ZonedDateTime endTime = DateUtil.addMonths(startTime, 1);
            return znsUserAccountDetailDao.selectMaps(new QueryWrapper<ZnsUserAccountDetailEntity>()
                    .select("sum(case type when 1 then amount else 0 end) income, sum(case type when 2 then amount else 0 end) expend")
                    .eq("user_id", userId)
                    .eq("is_delete", 0)
                    .eq("refund_status", 0)
                    .ge("create_time", startTime)
                    .eq(Objects.nonNull(userAccountId), "user_account_id", userAccountId)
                    .lt("create_time", endTime)
                    .in("trade_status", Arrays.asList(1, 2))).get(0);
        } catch (ParseException e) {
            log.error("ParseException e", e);
        }

        return null;
    }

    @Override
    public ZonedDateTime getFirstTime(Long userId) {
        //cache 不需要过期
        RBucket<ZonedDateTime> cache = redissonClient.getBucket(RedisConstants.FIRST_PAYMENT_TIME + userId);
        if (cache.isExists()) {
            return cache.get();
        }
        ZnsUserAccountDetailEntity detailEntity = znsUserAccountDetailDao.selectOne(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(ZnsUserAccountDetailEntity::getUserId, userId)
                .eq(ZnsUserAccountDetailEntity::getIsDelete, 0)
                .last("limit 1"));
        if (Objects.isNull(detailEntity)) {
            return ZonedDateTime.now();
        }
        cache.set(detailEntity.getCreateTime());
        cache.expire(30, TimeUnit.DAYS);
        return detailEntity.getCreateTime();
    }

    /**
     * 添加跑步活动奖励账户明细
     */
    @Override
    public Long addRunActivityAccountDetail0131(Long userId, AccountDetailTypeEnum accountDetailTypeEnum, Integer subtype, Integer type,
                                                BigDecimal amount, String billNo, ZonedDateTime tradeTime, Long refId, Long activityId,
                                                Long detailsId, Integer activityType,
                                                Long taskId, String remark, Integer privilegeBrand, Integer brandRightsInterests, BigDecimal rightsInterestsMultiple, BigDecimal extraAward) {
        ZnsUserService userService = SpringContextUtils.getBean(ZnsUserService.class);
        ZnsUserEntity user = userService.findById(userId);
        if (Objects.isNull(user)) {
            return null;
        }
        ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(userId);
        if (Objects.nonNull(accountEntity)) {
            amount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), amount);
            extraAward = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), extraAward);
        }
        ZonedDateTime now = ZonedDateTime.now();
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        userAccountDetailEntity.setCreateTime(now);
        userAccountDetailEntity.setModifieTime(now);
        userAccountDetailEntity.setUserId(userId);
        userAccountDetailEntity.setType(type);
        userAccountDetailEntity.setTitle(accountDetailTypeEnum.getName());
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setBillNo(billNo);
        userAccountDetailEntity.setTradeStatus(2);
        userAccountDetailEntity.setTradeType(accountDetailTypeEnum.getType());
        userAccountDetailEntity.setTradeSubtype(subtype);
        userAccountDetailEntity.setTradeTime(tradeTime);
        userAccountDetailEntity.setRefId(refId);
        userAccountDetailEntity.setRemark(accountDetailTypeEnum.getName());
        userAccountDetailEntity.setExtraAmount(extraAward);
        if (StringUtils.hasText(remark)) {
            userAccountDetailEntity.setRemark(remark);
            //新PK活动特殊处理
            if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityType)) {
                userAccountDetailEntity.setTitle(remark);
            }
        }
        if (AccountDetailTypeEnum.OFFICIAL_TEAM_AWARD.equals(accountDetailTypeEnum)) {
            userAccountDetailEntity.setTitle(AccountDetailSubtypeEnum.getAccountDetailSubtype(accountDetailTypeEnum, subtype).getName());
        }
        userAccountDetailEntity.setIsRobot(user.getIsRobot());
        userAccountDetailEntity.setIsTest(user.getIsTest());

        if (Objects.nonNull(activityId)) {
            userAccountDetailEntity.setActivityId(activityId);
        }
        userAccountDetailEntity.setDetailsId(detailsId);
        userAccountDetailEntity.setActivityType(activityType);
        userAccountDetailEntity.setTaskId(taskId);
        if (Objects.nonNull(privilegeBrand)) {
            userAccountDetailEntity.setPrivilegeBrand(privilegeBrand);
        }
        if (Objects.nonNull(brandRightsInterests)) {
            userAccountDetailEntity.setBrandRightsInterests(brandRightsInterests);
        }
        if (Objects.nonNull(rightsInterestsMultiple)) {
            userAccountDetailEntity.setBrandRightsInterestsMultiple(rightsInterestsMultiple);
        }
        userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        znsUserAccountDetailDao.insert(userAccountDetailEntity);
        return userAccountDetailEntity.getId();
    }

    @Override
    public BigDecimal getAllAmountByStatus(Long userId, List<Integer> tradeStatus, Integer type, Integer refund_status, Long userAccountId) {
        ZnsUserAccountDetailEntity one = znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>()
                .select("ifnull(sum(amount),0) amount")
                .eq("user_id", userId)
                .eq("is_delete", 0)
                .eq(Objects.nonNull(refund_status), "refund_status", refund_status)
                .eq("type", type)
                .in(!CollectionUtils.isEmpty(tradeStatus), "trade_status", tradeStatus));
        if (Objects.isNull(one)) {
            return BigDecimal.ZERO;
        }
        return one.getAmount();
    }

    @Override
    public Long addAccountDetail(Long userId, int type, AccountDetailTypeEnum detailTypeEnum, BigDecimal amount, String billNo,
                                 String payAccount, Integer tradeStatus, String tradeNo, Long refId, BigDecimal taxAmount, BigDecimal serviceAmount, Long userCouponId) {
        return addAccountDetailAddActivityId(userId, type, detailTypeEnum, null,
                amount, billNo, payAccount, tradeStatus, tradeNo, refId, taxAmount, serviceAmount, null, null, userCouponId, null, null);
    }


    @Override
    public Long addAccountDetailAddActivityId(Long userId, int type, AccountDetailTypeEnum detailTypeEnum, AccountDetailSubtypeEnum subtypeEnum, BigDecimal amount, String billNo, String payAccount,
                                              Integer tradeStatus, String tradeNo, Long refId, BigDecimal taxAmount, BigDecimal serviceAmount, Long activityId,
                                              Integer activityType, Long userCouponId, Integer privilegeBrand, Integer brandRightsInterests) {
        ZonedDateTime now = ZonedDateTime.now();
        ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(userId);
        List<AccountDetailTypeEnum> ignoreTypes = List.of(AccountDetailTypeEnum.WITHDRAW, AccountDetailTypeEnum.RECHARGE);
        if (Objects.nonNull(accountEntity) && !ignoreTypes.contains(detailTypeEnum)) {
            //充值/提现 不做小数处理
            amount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), amount);
            serviceAmount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), serviceAmount);
        }
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        userAccountDetailEntity.setCreateTime(now);
        userAccountDetailEntity.setModifieTime(now);
        userAccountDetailEntity.setUserId(userId);
        userAccountDetailEntity.setType(type);
        userAccountDetailEntity.setTitle(detailTypeEnum.getName());
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setBillNo(billNo);
        userAccountDetailEntity.setUserCouponId(userCouponId);
        userAccountDetailEntity.setTradeType(detailTypeEnum.getType());
        if (Objects.nonNull(subtypeEnum)) {
            userAccountDetailEntity.setTradeSubtype(subtypeEnum.getType());
        }
        userAccountDetailEntity.setRemark(detailTypeEnum.getName());
        userAccountDetailEntity.setPaypalAccount(payAccount);
        userAccountDetailEntity.setTradeStatus(tradeStatus);
        userAccountDetailEntity.setTradeNo(tradeNo);
        userAccountDetailEntity.setRefId(refId);
        userAccountDetailEntity.setServiceFee(serviceAmount);
        userAccountDetailEntity.setTaxesFee(taxAmount);
        userAccountDetailEntity.setTradeTime(ZonedDateTime.now());
        userAccountDetailEntity.setActivityId(activityId);
        userAccountDetailEntity.setActivityType(activityType);
        if (Objects.nonNull(privilegeBrand)) {
            userAccountDetailEntity.setPrivilegeBrand(privilegeBrand);
        }
        if (Objects.nonNull(brandRightsInterests)) {
            userAccountDetailEntity.setBrandRightsInterests(brandRightsInterests);
        }
        userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        znsUserAccountDetailDao.insert(userAccountDetailEntity);
        return userAccountDetailEntity.getId();
    }

    @Override
    public void updateAccountDetail(Long accountDetailId, Integer tradeStatus, String tradeNo, ZonedDateTime tradeTime, ZonedDateTime tradeSuccessTime, BigDecimal actualAmount, String actualPaypalAccount, String ourSidePaypalAccount, String transferAccountsRemark, Long operatorId) {
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        userAccountDetailEntity.setId(accountDetailId);
        userAccountDetailEntity.setIsDelete(0);
        userAccountDetailEntity.setModifieTime(ZonedDateTime.now());
        userAccountDetailEntity.setTradeStatus(tradeStatus);
        userAccountDetailEntity.setTradeTime(tradeTime);
        userAccountDetailEntity.setTradeNo(tradeNo);
        userAccountDetailEntity.setTradeSuccessTime(tradeSuccessTime);
        userAccountDetailEntity.setActualAmount(actualAmount);
        userAccountDetailEntity.setActualPaypalAccount(actualPaypalAccount);
        userAccountDetailEntity.setOurSidePaypalAccount(ourSidePaypalAccount);
        userAccountDetailEntity.setTransferAccountsRemark(transferAccountsRemark);
        userAccountDetailEntity.setTransferAccountsId(operatorId);
        this.updateById(userAccountDetailEntity);
    }

    @Override
    public ZnsUserAccountDetailEntity getAccountDetailByBillNo(String billNo) {
        return znsUserAccountDetailDao.selectOne(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery().eq(ZnsUserAccountDetailEntity::getBillNo, billNo).eq(ZnsUserAccountDetailEntity::getIsDelete, 0).last("limit 1"));
    }

    /**
     * 查询流水
     *
     * @param type                  收入/支出
     * @param accountDetailTypeEnum 交易类型
     * @param userId                用户id
     * @param refId                 关联id
     */
    @Override
    public ZnsUserAccountDetailEntity selectAccountDetail(Integer type, AccountDetailTypeEnum accountDetailTypeEnum, Long userId, Long refId) {
        if (null == accountDetailTypeEnum || null == userId || null == refId || null == type) {
            return null;
        }
        QueryWrapper<ZnsUserAccountDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        wrapper.eq("trade_type", accountDetailTypeEnum.getType());
        wrapper.eq("type", type);
        wrapper.eq("user_id", userId);
        wrapper.eq("activity_id", refId);
        wrapper.eq("refund_status", 0);
        wrapper.last("limit 1");
        return znsUserAccountDetailDao.selectOne(wrapper);
    }

    @Override
    public boolean refundAccountDetail(Long id, String refundRemark) {
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        userAccountDetailEntity.setModifieTime(ZonedDateTime.now());
        userAccountDetailEntity.setRefundStatus(1);
        userAccountDetailEntity.setRefundRemark(refundRemark);
        userAccountDetailEntity.setRefundTime(ZonedDateTime.now());
        return znsUserAccountDetailDao.update(userAccountDetailEntity, Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery().eq(ZnsUserAccountDetailEntity::getId, id).eq(ZnsUserAccountDetailEntity::getRefundStatus, 0)) > 0;
    }

    @Override
    public ZnsUserAccountDetailEntity getAccountDetailByTradeNo(String tradeNo) {
        return znsUserAccountDetailDao.selectOne(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery().eq(ZnsUserAccountDetailEntity::getTradeNo, tradeNo).eq(ZnsUserAccountDetailEntity::getIsDelete, 0).last("limit 1"));
    }

    @Override
    public void delete(Long id, String remark) {
        znsUserAccountDetailDao.update(new UpdateWrapper<ZnsUserAccountDetailEntity>().lambda().set(ZnsUserAccountDetailEntity::getIsDelete, 1)
                .set(ZnsUserAccountDetailEntity::getModifieTime, ZonedDateTime.now())
                .set(ZnsUserAccountDetailEntity::getRemark, remark)
                .eq(ZnsUserAccountDetailEntity::getId, id));
    }

    @Override
    public ZnsUserAccountDetailEntity getAccountDetail(Long userId, Long activityId, Integer trade_type, Integer trade_subtype) {
        return znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>()
                .eq(Objects.nonNull(userId), "user_id", userId)
                .eq("activity_id", activityId).eq("trade_type", trade_type)
                .eq(Objects.nonNull(trade_subtype), "trade_subtype", trade_subtype)
                .eq("is_delete", 0)
                .last("limit 1"));
    }

    @Override
    public ZnsUserAccountDetailEntity getAccountDetail(Long refId, Integer type, Integer subType) {
        return znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>().eq("ref_id", refId).eq("trade_type", type).eq("trade_subtype", subType).eq("is_delete", 0).last("limit 1"));
    }

    @Override
    public BigDecimal selectAccountDetailByTradeTypeRef(Long userId, Integer tradeType, Long refId, ZonedDateTime createTime, String dateType) {

        return znsUserAccountDetailDao.selectAccountDetailByTradeTypeRef(userId, tradeType, refId, createTime, dateType);
    }


    //===========================================

    @Resource
    private ZnsUserAccountDetailDao userAccountDetailDao;


    /**
     * 查询用户消费信息
     */
    @Override
    public AppUserConsumeInfo userConsumeInfo(Long userId) {
        AppUserConsumeInfo userConsumeInfo = new AppUserConsumeInfo();
        // 查询用户总的收入支出信息
        UserConsumeStatisticVo allConsumeStatisticVo = userAccountDetailDao.userConsumptionStatistic(userId, null);
        if (null != allConsumeStatisticVo) {
            userConsumeInfo.setTotalMonetary(allConsumeStatisticVo.getMonetary());
            userConsumeInfo.setTotalConsumptionNum(allConsumeStatisticVo.getConsumptionNum());
            userConsumeInfo.setTotalIncome(allConsumeStatisticVo.getIncome());
        }
        // 查询近30天收入支出信息
        ZonedDateTime lastThirtyDay = ZonedDateTime.now().minusDays(29).with(LocalTime.MIN);
        UserConsumeStatisticVo lastThirtyConsumeStatisticVo = userAccountDetailDao.userConsumptionStatistic(userId, lastThirtyDay);
        if (null != lastThirtyConsumeStatisticVo) {
            userConsumeInfo.setLastThirtyMonetary(lastThirtyConsumeStatisticVo.getMonetary());
            userConsumeInfo.setLastThirtyConsumptionNum(lastThirtyConsumeStatisticVo.getConsumptionNum());
            userConsumeInfo.setLastThirtyIncome(lastThirtyConsumeStatisticVo.getIncome());
        }
        // 查询最近一次消费事件
        QueryWrapper<ZnsUserAccountDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        wrapper.eq("type", 2);
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("id");
        wrapper.last("limit 1");
        ZnsUserAccountDetailEntity accountDetailEntity = znsUserAccountDetailDao.selectOne(wrapper);
        if (null != accountDetailEntity) {
            userConsumeInfo.setLatestConsumptionTime(accountDetailEntity.getModifieTime());
        }
        return userConsumeInfo;
    }


    /**
     * 添加跑步活动奖励账户明细
     */
    @Override
    public void addRunActivityAccountDetail(Integer userId, AccountDetailTypeEnum accountDetailTypeEnum, Integer subtype, Integer type,
                                            BigDecimal amount, String billNo, ZonedDateTime tradeTime, Integer refId) {
        ZonedDateTime now = ZonedDateTime.now();
        ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(Long.valueOf(userId));
        if (Objects.nonNull(accountEntity)) {
            amount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), amount);
        }
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        ZnsUserService userService = SpringContextUtils.getBean(ZnsUserService.class);
        ZnsUserEntity user = userService.findById(Long.parseLong(userId + ""));
        userAccountDetailEntity.setCreateTime(now);
        userAccountDetailEntity.setModifieTime(now);
        userAccountDetailEntity.setUserId(Long.parseLong(userId + ""));
        userAccountDetailEntity.setType(type);
        userAccountDetailEntity.setTitle(accountDetailTypeEnum.getName());
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setBillNo(billNo);
        userAccountDetailEntity.setTradeStatus(2);
        userAccountDetailEntity.setTradeType(accountDetailTypeEnum.getType());
        userAccountDetailEntity.setTradeSubtype(subtype);
        userAccountDetailEntity.setTradeTime(tradeTime);
        userAccountDetailEntity.setIsRobot(user.getIsRobot());
        userAccountDetailEntity.setIsTest(user.getIsTest());
        if (Objects.nonNull(refId)) {
            try {
                userAccountDetailEntity.setRefId(Long.parseLong(refId + ""));
            } catch (Exception e) {
                log.error("addRunActivityAccountDetail setRefId error;" + e);
            }
        }
        userAccountDetailEntity.setRemark(accountDetailTypeEnum.getName());
        userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        znsUserAccountDetailDao.insert(userAccountDetailEntity);
    }

    @Override
    public Page<AccountWithdrawalVo> userAccountPage(AccountDetailPo po) {
        Page<AccountWithdrawalVo> page = userAccountDetailDao.userAccountPage(new Page<>(po.getPageNum(), po.getPageSize()), po);
        return page;
    }

    @Override
    public List<AccountWithdrawalVo> userAccountList(AccountDetailPo po) {
        return userAccountDetailDao.userAccountList(po);
    }


    @Override
    public BigDecimal sumOfficialActivityAward(Integer activityType, Long refId, Integer isRealUser) {
        Integer tradeType = AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType();
        if (activityType == 4) {
            tradeType = AccountDetailTypeEnum.OFFICIAL_TEAM_AWARD.getType();
        } else if (activityType == 5) {
            tradeType = AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD.getType();
        }
        return userAccountDetailDao.sumOfficialActivityAward(tradeType, refId, isRealUser);
    }


    @Override
    public ZnsUserAccountDetailEntity getAccountDetail(Integer refId, Integer type, Integer subType) {
        return znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>().eq("ref_id", refId).eq("trade_type", type).eq("trade_subtype", subType).eq("is_delete", 0).last("limit 1"));
    }

    @Override
    public BigDecimal getIssuedAward(ZonedDateTime startDate) {
        String date = DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD, ZonedDateTime.now());
        //从缓存中获取
        String key = RedisConstants.AWARDS_ISSUED_KEY + date;
        Object value = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(value)) {
            return new BigDecimal(value.toString());
        }

        BigDecimal issuedAwardNoCache = getIssuedAwardNoCache(startDate);

        redisTemplate.opsForValue().set(key, issuedAwardNoCache.toString(), 5 * 60, TimeUnit.SECONDS);
        return issuedAwardNoCache;
    }

    @Override
    public Integer getIssuedUserCount(ZonedDateTime startDate) {
        String date = DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD, ZonedDateTime.now());
        //从缓存中获取
        String key = RedisConstants.AWARDS_ISSUED_COUNT_KEY + date;
        Object value = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(value)) {
            return Integer.valueOf(value.toString());
        }
        Integer issuedUserCountNoCache = getIssuedUserCountNoCache(startDate);
        redisTemplate.opsForValue().set(key, issuedUserCountNoCache.toString(), 5 * 60, TimeUnit.SECONDS);
        return issuedUserCountNoCache;
    }

    @Override
    public List<UserAccountSimpleVo> getLastUserAccountDetail(ZonedDateTime startDate) {
        return userAccountDetailDao.getLastUserAccountDetail(startDate);
    }

    @Override
    public ZnsUserAccountDetailEntity getLastOneUserAccountDetail(Long userId) {
        return znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>().eq("is_delete", 0)
                .eq("user_id", userId)
                .eq("type", 1).orderByDesc("id")
                .ne("trade_type", 5)
                .eq("trade_status", 2)
                .eq("refund_status", 0)
                .last("limit 1"));
    }

    @Override
    public BigDecimal sumAward(Long userId, Integer tradeType, List<Integer> subTypes) {
        ZnsUserAccountDetailEntity entity = znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>()
                .select("sum(amount) as amount")
                .eq("user_id", userId)
                .eq("is_delete", 0)
                .eq("trade_type", tradeType)
                .eq("trade_status", 2)
                .eq("refund_status", 0)
                .in(!CollectionUtils.isEmpty(subTypes), "trade_subtype", subTypes));
        return Objects.isNull(entity) ? null : entity.getAmount();
    }

    @Override
    public Map<String, Object> getMilepostAwardReport(Long refId, Integer type, int subType, Integer isRealUser) {
        return userAccountDetailDao.getMilepostAwardReport(refId, type, subType, isRealUser);
    }


    @Override
    public BigDecimal selectAccountDetailByUserIdTypeRefIdsDateRule(Long userId, Integer tradeType, ZonedDateTime createTime, String awardRule) {
        return userAccountDetailDao.selectAccountDetailByUserIdTypeRefIdsDateRule(userId, tradeType, createTime, awardRule);
    }

    @Override
    public Integer selectAccountDetailByUserIdTypeRefIdsDateRuleCount(Long userId, Integer tradeType, ZonedDateTime createTime, String awardRule) {
        return userAccountDetailDao.selectAccountDetailByUserIdTypeRefIdsDateRuleCount(userId, tradeType, createTime, awardRule);
    }

    @Override
    public void addRunActivityAccountDetail3D(Long userId, AccountDetailTypeEnum accountDetailTypeEnum, Integer subtype, Integer type, BigDecimal amount, String billNo, ZonedDateTime tradeTime, Long refId, Long activityId) {
        // 金币墙发奖励,不处理金额小数点
        ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(userId);
        ZonedDateTime now = ZonedDateTime.now();
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        userAccountDetailEntity.setCreateTime(now);
        userAccountDetailEntity.setModifieTime(now);
        userAccountDetailEntity.setUserId(userId);
        userAccountDetailEntity.setType(type);
        userAccountDetailEntity.setTitle(accountDetailTypeEnum.getName());
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setBillNo(billNo);
        userAccountDetailEntity.setTradeStatus(2);
        userAccountDetailEntity.setTradeType(accountDetailTypeEnum.getType());
        userAccountDetailEntity.setTradeSubtype(subtype);
        userAccountDetailEntity.setTradeTime(tradeTime);
        userAccountDetailEntity.setRefId(refId);
        userAccountDetailEntity.setRemark(accountDetailTypeEnum.getName());
        userAccountDetailEntity.setActivityId(activityId);
        userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        znsUserAccountDetailDao.insert(userAccountDetailEntity);
    }

    @Override
    public BigDecimal sumAward(Long userId, ZonedDateTime startDate, ZonedDateTime endDate) {
        ZnsUserAccountDetailEntity entity = znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>()
                .select("sum(amount) as amount")
                .eq("user_id", userId)
                .ge(true, "create_time", startDate)
                .le(true, "create_time", endDate)
                .eq("is_delete", 0)
                .eq("type", 1)
                .ne("trade_type", 5)
                .eq("trade_status", 2)
                .eq("refund_status", 0));
        return Objects.isNull(entity) ? BigDecimal.ZERO : entity.getAmount();
    }

    @Override
    public void addAccountDetail(Long userId, int type, AccountDetailTypeEnum typeEnum, AccountDetailSubtypeEnum subtypeEnum, BigDecimal amount, Long activityId, String remark) {
        ZnsUserService userService = SpringContextUtils.getBean(ZnsUserService.class);
        ZnsUserEntity user = userService.findById(userId);
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime now = ZonedDateTime.now();
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(userId);
        if (Objects.nonNull(accountEntity)) {
            amount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), amount);
            userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        }
        userAccountDetailEntity.setCreateTime(now);
        userAccountDetailEntity.setModifieTime(now);
        userAccountDetailEntity.setUserId(userId);
        userAccountDetailEntity.setType(type);
        userAccountDetailEntity.setTitle(StringUtils.hasText(remark) ? remark : subtypeEnum.getName());
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setBillNo(billNo);
        userAccountDetailEntity.setTradeType(typeEnum.getType());
        userAccountDetailEntity.setTradeSubtype(subtypeEnum.getType());
        userAccountDetailEntity.setRemark(StringUtils.hasText(remark) ? remark : subtypeEnum.getName());
        userAccountDetailEntity.setPaypalAccount("");
        userAccountDetailEntity.setTradeStatus(2);
        userAccountDetailEntity.setTradeTime(ZonedDateTime.now());
        userAccountDetailEntity.setActivityId(activityId);
        userAccountDetailEntity.setIsTest(user.getIsTest());
        userAccountDetailEntity.setIsRobot(user.getIsRobot());
        znsUserAccountDetailDao.insert(userAccountDetailEntity);
    }

    @Override
    public List<AppUserDto> getWithdrawal(List<Long> userIds) {
        return userAccountDetailDao.getWithdrawal(userIds);
    }

    @Override
    public List<ZnsUserAccountDetailEntity> getAccountDetails(List<Long> userIds, Long activityId, Integer tradeType, Integer tradeSubtype) {
        return znsUserAccountDetailDao.selectList(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .in(ZnsUserAccountDetailEntity::getUserId, userIds)
                .eq(ZnsUserAccountDetailEntity::getRefId, activityId)
                .eq(ZnsUserAccountDetailEntity::getTradeType, tradeType)
                .eq(ZnsUserAccountDetailEntity::getTradeSubtype, tradeSubtype));
    }

    @Override
    public List<ZnsUserAccountDetailEntity> getChallengeAmount(List<Long> userIds, Long activityId) {
        return userAccountDetailDao.getChallengeAmount(userIds, activityId);
    }

    @Override
    public BigDecimal getIssuedAwardNoCache(ZonedDateTime startDate) {
        //从数据中获取
        ZnsUserAccountDetailEntity entity = znsUserAccountDetailDao.selectOne(new QueryWrapper<ZnsUserAccountDetailEntity>()
                .select("sum(amount) amount")
                .eq("is_delete", 0)
                .eq("type", 1)
                .ne("trade_type", 5)
                .eq("trade_status", 2)
                .ge("create_time", startDate)
                .eq("refund_status", 0));
        if (Objects.isNull(entity) || Objects.isNull(entity.getAmount())) {
            return BigDecimal.ZERO;
        }
        return entity.getAmount();
    }

    @Override
    public Integer getIssuedUserCountNoCache(ZonedDateTime startDate) {
        //从数据中获取
        Map<String, Object> map = znsUserAccountDetailDao.selectMaps(new QueryWrapper<ZnsUserAccountDetailEntity>()
                .select("count(DISTINCT(user_id)) num")
                .eq("is_delete", 0)
                .eq("type", 1)
                .ne("trade_type", 5)
                .ge("create_time", startDate)
                .eq("trade_status", 2)
                .eq("refund_status", 0)).get(0);
        if (Objects.isNull(map)) {
            return 0;
        }
        Integer num = MapUtils.getInteger(map, "num");
        return num;
    }

    @Override
    public void updateIssuedAwardAndUserCount(ZonedDateTime startDate) {
        String date = DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD, ZonedDateTime.now());
        BigDecimal issuedAward = getIssuedAwardNoCache(startDate);
        redisTemplate.opsForValue().set(RedisConstants.AWARDS_ISSUED_KEY + date, issuedAward.toString(), 5 * 60, TimeUnit.SECONDS);

        Integer num = getIssuedUserCountNoCache(startDate);
        redisTemplate.opsForValue().set(RedisConstants.AWARDS_ISSUED_COUNT_KEY + date, num.toString(), 5 * 60, TimeUnit.SECONDS);
    }

    @Override
    public List<ActivitySumAwardBySubTypeDto> selectSumByActivityIdTradeType(Long activityId, Integer tradeType, Integer isRealUser) {
        return userAccountDetailDao.selectSumByActivityIdTradeType(activityId, tradeType, isRealUser);
    }

    @Override
    public ZnsUserAccountDetailEntity selectById(Long id) {
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = userAccountDetailDao.selectById(id);
        return znsUserAccountDetailEntity;
    }

    @Override
    public BigDecimal sumAward(Long activityId, Long userId, List<Integer> tradeType, Integer tradeStatus) {
        return userAccountDetailDao.selectSumAward(activityId, userId, tradeType, tradeStatus);
    }

    @Override
    public void updateAccountRemark(Long activityId, Long userId, Integer tradeType, String remark) {
        ZnsUserAccountDetailEntity accountDetail = znsUserAccountDetailDao.selectOne(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(ZnsUserAccountDetailEntity::getUserId, userId)
                .eq(ZnsUserAccountDetailEntity::getActivityId, activityId)
                .eq(ZnsUserAccountDetailEntity::getTradeType, tradeType)
                .eq(ZnsUserAccountDetailEntity::getIsDelete, 0).last("limit 1"));
        if (Objects.isNull(accountDetail)) {
            return;
        }
        ZnsUserAccountDetailEntity update = new ZnsUserAccountDetailEntity();
        update.setId(accountDetail.getId());
        update.setTitle(remark);
        update.setRemark(remark);
        this.updateById(update);
    }

    @Override
    public BigDecimal selectSumAmountWithUserIdAndActivityIdAndTime(Long activityId, ZnsUserRunDataDetailsEntity entity) {
        QueryWrapper<ZnsUserAccountDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ifnull(sum(amount),0) as amount")
                .eq("user_id", entity.getUserId()).eq("activity_id", activityId).eq("details_id", entity.getId()).ge("create_time", entity.getCreateTime());
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = znsUserAccountDetailDao.selectOne(queryWrapper);
        return znsUserAccountDetailEntity.getAmount();
    }

    @Override
    public BigDecimal getMonthReportAccountData(Long userId, ZonedDateTime startTime, ZonedDateTime endTime) {
        return userAccountDetailDao.selectMonthReportAccountData(userId, startTime, endTime);
    }

    /**
     * 初始化用户资金明细金额
     *
     * @param userId
     */
    @Override
    public void initUserAccountDetail(Long userId) {
        if (userId != null) {
            //初始化用户优惠券币种
            ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(userId);
            if (accountEntity == null) {
                throw new BaseException(userId + "用户账户不存在！");
            }
            if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(accountEntity.getCurrencyCode())) {
                throw new BaseException(userId + "账户币种无需转换！");
            }
            InitCurrencyBo initCurrencyBo = new InitCurrencyBo(accountEntity.getCurrencyCode(), userId, InitCurrencyBo.InitTypeEnum.INITTYPE_6.getType());
            rabbitTemplate.convertAndSend(currency_init_queue, JsonUtil.writeString(initCurrencyBo));
            return;
        }

        //初始化所有用户资金明细多币种
        initAllUserAccountDetail(0L);
    }

    /**
     * 初始化所有用户多币种资金明细
     *
     * @param startUserId
     */
    private void initAllUserAccountDetail(long startUserId) {
        //每次处理100个用户
        LambdaQueryWrapper<ZnsUserAccountEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(ZnsUserAccountEntity::getUserId, ZnsUserAccountEntity::getCurrencyCode);
        queryWrapper.eq(ZnsUserAccountEntity::getIsDelete, 0);
        queryWrapper.ne(ZnsUserAccountEntity::getCurrencyCode, I18nConstant.CurrencyCodeEnum.USD.getCode());
        queryWrapper.gt(ZnsUserAccountEntity::getUserId, startUserId);
        queryWrapper.orderByAsc(ZnsUserAccountEntity::getUserId);
        queryWrapper.last("limit 100");
        List<ZnsUserAccountEntity> accountEntities = znsUserAccountDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(accountEntities) || accountEntities.get(0) == null) {
            return;
        }
        for (ZnsUserAccountEntity accountEntity : accountEntities) {
            InitCurrencyBo initCurrencyBo = new InitCurrencyBo(accountEntity.getCurrencyCode(), accountEntity.getUserId(), InitCurrencyBo.InitTypeEnum.INITTYPE_6.getType());
            rabbitTemplate.convertAndSend(currency_init_queue, JsonUtil.writeString(initCurrencyBo));
        }
        ZnsUserAccountEntity accountEntity = accountEntities.get(accountEntities.size() - 1);
        initAllUserAccountDetail(accountEntity.getUserId());
    }

    /**
     * 初始化单个用户资金明细
     *
     * @param userId
     * @param currencyCode
     */
    @Override
    public void initSingleUserAccountDetail(Long userId, String currencyCode) {
        I18nConstant.CurrencyCodeEnum currencyCodeEnum = I18nConstant.CurrencyCodeEnum.findByCode(currencyCode);
        if (currencyCodeEnum == null) {
            log.info("ZnsUserAccountDetailServiceImpl#initSingleUserAccountDetail------- 初始化单个用户资金明细,用户Id：{}，币种：{}，币种类型未配置", userId, currencyCode);
            return;
        }
        List<ExchangeRateBo> list = exchangeRateConfigService.findList(new ExchangeRateQuery(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode));
        if (org.springframework.util.CollectionUtils.isEmpty(list) || list.get(0) == null) {
            log.info("ZnsUserAccountDetailServiceImpl#initSingleUserAccountDetail------- 初始化单个用户资金明细,用户Id：{}，币种：{}，币种汇率未配置", userId, currencyCode);
            return;
        }
        ExchangeRateBo exchangeRateBo = list.get(0);
        BigDecimal exchangeRate = Optional.ofNullable(exchangeRateBo.getExchangeRate()).orElse(BigDecimal.ZERO);
        if (exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("ZnsUserAccountDetailServiceImpl#initSingleUserAccountDetail------- 初始化单个用户资金明细,用户Id：{}，币种：{}，币种汇率等于0", userId, currencyCode);
            return;
        }

        //多币种初始化-更新资金记录
        int i = znsUserAccountDetailDao.updateUserAccountDetailByCurrency(exchangeRate, userId);
        log.info("ZnsUserAccountDetailServiceImpl#initSingleUserAccountDetail------- 初始化单个用户资金明细,用户Id：{}，币种：{}，更新数量：{}", userId, currencyCode, i);
    }

    /**
     * 更新资金记录
     *
     * @param exchangeRate 汇率
     * @param userId       用户
     * @return 更新数量
     */
    @Override
    public Integer updateUserAccountDetailByCurrency(BigDecimal exchangeRate, Long userId) {
        return znsUserAccountDetailDao.updateUserAccountDetailByCurrency(exchangeRate, userId);
    }

    @Override
    public List<ZnsUserAccountDetailEntity> selectByUserIdsAndActivityId(List<Long> userIds, Long mainActivityId) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        return znsUserAccountDetailDao.selectList(buildQueryWrappers(new UserAccountDetailByQuery().setActivityId(mainActivityId).setUserIds(userIds)));
    }

    @Override
    public List<ZnsUserAccountDetailEntity> getAccountDetailsByActivityIds(List<Long> activityIds, List<Integer> tradeTypes) {
        return znsUserAccountDetailDao.selectList(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .in(ZnsUserAccountDetailEntity::getActivityId, activityIds)
                .in(!CollectionUtils.isEmpty(tradeTypes), ZnsUserAccountDetailEntity::getTradeType, tradeTypes));
    }

    @Override
    public BigDecimal sumAward(List<Long> activityIds, Long userId, List<Integer> tradeType, int tradeStatus) {
        return userAccountDetailDao.selectSumAwardByActivityIds(activityIds, userId, tradeType, tradeStatus);
    }

    @Override
    public List<ZnsUserAccountDetailEntity> selectByAndActivityIds(List<Long> mainActIds) {
        List<ZnsUserAccountDetailEntity> list = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(mainActIds)) {
            list = znsUserAccountDetailDao.selectList(buildQueryWrappers(new UserAccountDetailByQuery().setActivityIds(mainActIds)));
        }
        return list;
    }

    @Override
    public RunDataYearVo selectMostAmountDay(Long userId, ZonedDateTime startTime, ZonedDateTime endTime) {
        return userAccountDetailDao.selectMostAmountDay(userId, startTime, endTime);
    }

    /**
     * 查询活动指定类型的交易金额
     *
     * @param activityId
     * @param tradeTypes
     * @param tradeStatus
     * @return
     */
    @Override
    public List<CurrencyAmount> findActCurrencyAmountByTradeType(Long activityId, List<Integer> tradeTypes, Integer tradeStatus) {
        return userAccountDetailDao.findActCurrencyAmountByTradeType(activityId, tradeTypes, tradeStatus);
    }

    @Override
    public void bindUserAccountId(Long userAccountId, Long userId) {
        ZnsUserAccountDetailEntity update = new ZnsUserAccountDetailEntity();
        update.setUserAccountId(userAccountId);
        znsUserAccountDetailDao.update(update, Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(ZnsUserAccountDetailEntity::getUserId, userId).eq(ZnsUserAccountDetailEntity::getUserAccountId, 0));
        log.info("账户明细绑定accountId");
    }

    @Override
    public ZnsUserAccountDetailEntity getAccountIncomeDetail(Long userId, Long activityId) {
        return znsUserAccountDetailDao.selectOne(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(ZnsUserAccountDetailEntity::getUserId, userId)
                .eq(ZnsUserAccountDetailEntity::getActivityId, activityId)
                .eq(ZnsUserAccountDetailEntity::getType, 1)
                .eq(ZnsUserAccountDetailEntity::getIsDelete, 0)
                .orderByDesc(ZnsUserAccountDetailEntity::getCreateTime)
                .last("limit 1"));
    }

    @Override
    public List<ZnsUserAccountDetailEntity> getUserAccountDetailByQuery(UserAccountDetailByQuery query) {
        return znsUserAccountDetailDao.selectList(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(Objects.nonNull(query.getUserId()), ZnsUserAccountDetailEntity::getUserId, query.getUserId())
                .eq(Objects.nonNull(query.getActivityId()), ZnsUserAccountDetailEntity::getActivityId, query.getActivityId())
                .eq(Objects.nonNull(query.getType()), ZnsUserAccountDetailEntity::getType, query.getType())
                .in(!CollectionUtils.isEmpty(query.getTradeType()), ZnsUserAccountDetailEntity::getTradeType, query.getTradeType())
                .eq(Objects.nonNull(query.getTradeStatus()), ZnsUserAccountDetailEntity::getTradeStatus, query.getTradeStatus())
                .eq(Objects.nonNull(query.getActivityType()), ZnsUserAccountDetailEntity::getActivityType, query.getActivityType())
                .in(!CollectionUtils.isEmpty(query.getTradeSubtype()), ZnsUserAccountDetailEntity::getTradeSubtype, query.getTradeSubtype()));
    }

    /**
     * 通过资金明细计算 总支出、总收入
     *
     * @param userId
     */
    @Override
    public List<AccountTotalVo> getUserAccountTotalVo(Long userId) {
        return znsUserAccountDetailDao.getUserAccountTotalVo(userId);
    }

    @Override
    public Page<RunAwardDto> selectPageByCondition(IPage page, ZonedDateTime gmtStartTime, ZonedDateTime gmtEndTime, String activityNo) {
        return znsUserAccountDetailDao.selectPageByCondition(page, gmtStartTime, gmtEndTime, activityNo);
    }

    @Override
    public BigDecimal selectAccountByGmtCreateIsRobotTradeType(ZonedDateTime start, ZonedDateTime end, Integer isRobot, Integer type) {
        return znsUserAccountDetailDao.selectAccountByGmtCreateIsRobotTradeType(start, end, isRobot, type);
    }

    @Override
    public BigDecimal selectAccountByTradeType(Long userId, Integer type) {
        return znsUserAccountDetailDao.selectAccountByTradeType(userId, type);
    }

    @Override
    public Integer countAccountByTradeType(Long userId, Integer type) {
        return znsUserAccountDetailDao.countAccountByTradeType(userId, type);
    }

    @Override
    public BigDecimal selectAccountByCreateTimeTradeType(Long userId, ZonedDateTime befor30Date, Integer type) {
        return znsUserAccountDetailDao.selectAccountByCreateTimeTradeType(userId, befor30Date, type);
    }

    @Override
    public Integer countAccountByCreateTimeTradeType(Long userId, ZonedDateTime befor30Date, Integer type) {
        return znsUserAccountDetailDao.countAccountByCreateTimeTradeType(userId, befor30Date, type);
    }

    @Override
    public BigDecimal selectAccountByTradeTypeSubType(ZonedDateTime gmtStartTime, ZonedDateTime gmtEndTime, Integer type, String activityType) {
        return znsUserAccountDetailDao.selectAccountByTradeTypeSubType(gmtStartTime, gmtEndTime, type, activityType);
    }

    @Override
    public List<LoopsResp> selectAccountByBatchNo(String batchNo) {
        return znsUserAccountDetailDao.selectAccountByBatchNo(batchNo);
    }

    @Override
    public BigDecimal selectAccountByActivityId(Long id, Integer tradeType) {
        return znsUserAccountDetailDao.selectAccountByActivityId(id, tradeType);
    }

    @Override
    public BigDecimal selectSumAward(Long activityId, Long userId, List<Integer> tradeType, int i) {
        return znsUserAccountDetailDao.selectSumAward(activityId, userId, tradeType, i);
    }

    @Override
    public BigDecimal selectAccountByActivityIdUserIdTradeType(Long aLong, Long userId, List<Integer> list) {
        return znsUserAccountDetailDao.selectAccountByActivityIdUserIdTradeType(aLong, userId, list);
    }

    @Override
    public BigDecimal selectAccountDetailByUserIdTradeType(Long userId, List<Integer> list, Long id) {
        return znsUserAccountDetailDao.selectAccountDetailByUserIdTradeType(userId, list, id);
    }

    @Override
    public BigDecimal selectSumByUserIdActivityType(Integer type, Long userId, int i, Integer refundStatus) {
        return znsUserAccountDetailDao.selectSumByUserIdActivityType(type, userId, i, refundStatus);
    }

    @Override
    public ZnsUserAccountDetailEntity selectAccountDetailByBillNo(String billNo) {
        return znsUserAccountDetailDao.selectAccountDetailByBillNo(billNo);
    }

    @Override
    public Integer countAccountByTradeTypeTradeSubtype(Integer type, List<Integer> list, Long userId) {
        return znsUserAccountDetailDao.countAccountByTradeTypeTradeSubtype(type, list, userId);
    }

    /**
     * 合并资金记录
     *
     * @param newAccountId
     * @param newUserId
     * @param oldUserIds
     */
    @Override
    public void mergeAccountDetail(Long newAccountId, Long newUserId, List<Long> oldUserIds) {
        znsUserAccountDetailDao.mergeAccountDetail(newAccountId, newUserId, oldUserIds);
    }

    @Override
    public List<ZnsUserAccountDetailEntity> listByIds(List<Long> userAccountDetailIdList) {
        return znsUserAccountDetailDao.selectBatchIds(userAccountDetailIdList);
    }

    @Override
    public List<ZnsUserAccountDetailEntity> findList(UserAccountDetailByQuery userAccountDetailByQuery) {
        return znsUserAccountDetailDao.selectList(buildQueryWrappers(userAccountDetailByQuery));
    }

    private static Wrapper<ZnsUserAccountDetailEntity> buildQueryWrappers(UserAccountDetailByQuery query) {
        return Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(ZnsUserAccountDetailEntity::getIsDelete, 0)
                .eq(Objects.nonNull(query.getActivityId()), ZnsUserAccountDetailEntity::getActivityId, query.getActivityId())
                .ge(Objects.nonNull(query.getStartTime()), ZnsUserAccountDetailEntity::getCreateTime, query.getStartTime())
                .le(Objects.nonNull(query.getEndTime()), ZnsUserAccountDetailEntity::getCreateTime, query.getEndTime())
                .eq(Objects.nonNull(query.getType()), ZnsUserAccountDetailEntity::getType, query.getType())
                .eq(Objects.nonNull(query.getUserId()), ZnsUserAccountDetailEntity::getUserId, query.getUserId())
                .eq(Objects.nonNull(query.getTradeStatus()), ZnsUserAccountDetailEntity::getTradeStatus, query.getTradeStatus())
                .in(!CollectionUtils.isEmpty(query.getTradeType()), ZnsUserAccountDetailEntity::getTradeType, query.getTradeType())
                .in(!CollectionUtils.isEmpty(query.getUserIds()), ZnsUserAccountDetailEntity::getUserId, query.getUserIds())
                .in(!CollectionUtils.isEmpty(query.getActivityIds()), ZnsUserAccountDetailEntity::getActivityId, query.getActivityIds());
    }

    @Override
    public void updateById(ZnsUserAccountDetailEntity update) {
        znsUserAccountDetailDao.updateById(update);
    }

    @Override
    public List<ZnsUserAccountDetailEntity> findListForNonPayment(ZonedDateTime start, ZonedDateTime end) {
        return znsUserAccountDetailDao.selectList(Wrappers.<ZnsUserAccountDetailEntity>lambdaQuery()
                .eq(ZnsUserAccountDetailEntity::getIsDelete, 0)
                .eq(ZnsUserAccountDetailEntity::getTradeStatus, 0)
                .eq(ZnsUserAccountDetailEntity::getTradeType, AccountDetailTypeEnum.RECHARGE.getType())
                .ge(true, ZnsUserAccountDetailEntity::getCreateTime, start)
                .lt(true, ZnsUserAccountDetailEntity::getCreateTime, end)
                .eq(ZnsUserAccountDetailEntity::getRefundStatus, 0).last("limit 5"));
    }

    @Override
    public boolean save(ZnsUserAccountDetailEntity userAccountDetailEntity) {
        return znsUserAccountDetailDao.insert(userAccountDetailEntity) > 0;
    }

    @Override
    public boolean update(ZnsUserAccountDetailEntity znsUserAccountDetailEntity) {
        return znsUserAccountDetailDao.updateById(znsUserAccountDetailEntity) > 0;
    }

    /**
     * 查询设备奖励数据
     */
    @Override
    public List<EquipmentModelAwardDataVo> findEquipmentModelAwardData(Long userId) {
        return znsUserAccountDetailDao.findEquipmentModelAwardData(userId);
    }

    /**
     * 获取前一天的设备奖励数据
     */
    @Override
    public List<UserEquipmentAwardDataVo> findUserEquipmentAwardVo(ZonedDateTime startDate) {
        return znsUserAccountDetailDao.findUserEquipmentAwardVo(startDate);
    }

    @Override
    public List<UserAmountPair> sumByActivityIdTradeTypeAndUserIds(List<ActivityUserPairDto> loadData, AccountDetailTypeEnum accountDetailTypeEnum) {
        if (CollectionUtils.isEmpty(loadData)) {
            return Lists.newArrayList();
        }
        return znsUserAccountDetailDao.sumByActivityIdTradeTypeAndUserIds(loadData, accountDetailTypeEnum.getType());
    }
}
