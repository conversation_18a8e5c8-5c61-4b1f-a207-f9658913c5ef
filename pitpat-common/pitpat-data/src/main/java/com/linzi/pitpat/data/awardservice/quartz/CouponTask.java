package com.linzi.pitpat.data.awardservice.quartz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.query.CouponsPageQuery;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/27 16:58
 */
@Component("couponTask")
@Slf4j
public class CouponTask {
    @Resource
    private UserCouponService userCouponService;
    @Resource
    private CouponService couponService;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private AppMessageService appMessageService;

    /**
     * 券过期处理
     */
    public void overdue() {
        log.info("couponTask overdue 开始");
        //查询一天内的，减少查询量
        List<UserCoupon> list = userCouponService.findListByTimeAndStatus(0, DateUtil.addDays(ZonedDateTime.now(), -1), ZonedDateTime.now());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (UserCoupon userCoupon : list) {
            userCoupon.setStatus(3);
            userCoupon.setGmtModified(ZonedDateTime.now());
        }
        userCouponService.updateBatchByIds(list);
        log.info("couponTask overdue 结束");
    }

//    /**
//     * 券过期提醒
//     */
//    public void overdueRemind() {
//        log.info("couponTask overdueRemind 开始");
//        List<CouponOverdueRemindListDto> list = userCouponService.overdueRemindList();
//
//        if (CollectionUtils.isEmpty(list)) {
//            log.info("overdueRemind overdueRemind end,list isEmpty");
//            return;
//        }
//        int betweenSecond = DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.getEndOfDate(ZonedDateTime.now()));
//
//        for (CouponOverdueRemindListDto userCoupon : list) {
//            if (userCoupon.getExpirationRemindType() == 1) {
//                String couponOverdueRemind = "coupon_overdue_remind_user:" + userCoupon.getUserId();
//                String lockNum = redisUtil.get(couponOverdueRemind);
//                if (!StringUtils.hasText(lockNum)) {
//                    appMessageService.sendImAndPush(userCoupon);
//                    redisUtil.set(couponOverdueRemind, 1, betweenSecond, TimeUnit.SECONDS);
//                } else if (Integer.valueOf(lockNum) <= 3) {
//                    appMessageService.sendImAndPush(userCoupon);
//                    redisUtil.set(couponOverdueRemind, Integer.valueOf(lockNum) + 1, betweenSecond, TimeUnit.SECONDS);
//                } else {
//                    log.info("overdueRemind overdueRemind end,lockNum > 3");
//                }
//            } else {
//                //默认的推一一条
//                String couponOverdueRemindType = "coupon_overdue_remind_user_type:" + userCoupon.getUserId();
//                String couponOverdueRemindStr = redisUtil.get(couponOverdueRemindType);
//                if (StringUtils.hasText(couponOverdueRemindStr)) {
//                    continue;
//                }
//                appMessageService.sendImAndPush(userCoupon);
//                redisUtil.set(couponOverdueRemindType, "1", betweenSecond, TimeUnit.SECONDS);
//
//            }
//
//
//        }
//        log.info("couponTask overdueRemind 结束");
//    }


    /**
     * 超过领取时间自动关闭券配置
     */
    public void stopCoupon() {
        log.info("couponTask stopCoupon 开始");
        //已过期 & 未停止的
        CouponsPageQuery pageQuery = new CouponsPageQuery()
                .setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type)
                .setReceiveStartLe(ZonedDateTime.now()).setReceiveEndLe(ZonedDateTime.now())
                .setStatus(CouponConstant.CouponStatusEnum.STATUS_1.getType());
        pageQuery.setPageSize(300);
        pageQuery.setPageNum(1);
        Page<Coupon> page = couponService.findPage(pageQuery);
        List<Coupon> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        //批量失效券配置
        List<Long> couponIds = records.stream().map(Coupon::getId).toList();
        couponService.expireByCouponIds(couponIds);

        log.info("couponTask stopCoupon 结束");
    }


}
