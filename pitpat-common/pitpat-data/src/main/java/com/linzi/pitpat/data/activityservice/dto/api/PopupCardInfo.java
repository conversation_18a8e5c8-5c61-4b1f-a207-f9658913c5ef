package com.linzi.pitpat.data.activityservice.dto.api;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 弹窗卡片信息DTO
 *
 * @since 2025年6月19日
 */
@Data
@Accessors(chain = true)
public class PopupCardInfo implements Serializable {

    /**
     * 赛事卡ID
     */
    private Long cardId;

    /**
     * 卡类型
     *
     * @see com.linzi.pitpat.data.activityservice.enums.ProActivityCardTypeEnum
     */
    private String cardType;

    /**
     * 卡标题
     */
    private String cardTitle;

    /**
     * 说明
     */
    private String remark;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 开始时间
     */
    private ZonedDateTime startTime;

    /**
     * 结束时间
     */
    private ZonedDateTime endTime;

    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;

    /**
     * 赛事类型
     *
     * @See com.linzi.pitpat.data.activityservice.constant.enums.pro.ProActivityType
     */
    private String competitiveType;

    /**
     * 状态
     */
    private String status;

    /**
     * 活动标题
     */
    private String activityTitle;
}
