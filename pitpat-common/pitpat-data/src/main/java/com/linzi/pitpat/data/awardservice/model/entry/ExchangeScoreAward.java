package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 积分兑换物品表
 *
 * <AUTHOR>
 * @since 2023-10-10
 */

@Data
@NoArgsConstructor
@TableName("zns_exchange_score_award")
public class ExchangeScoreAward implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                         // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";            // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";        // 创建时间
    public final static String modifie_time = CLASS_NAME + "modifie_time";      // 最后修改时间
    public final static String exchange_name = CLASS_NAME + "exchange_name";    // 兑换物品名称
    public final static String exchange_type = CLASS_NAME + "exchange_type";    // 兑换类型：1优惠券、2服装、3标识补卡、4活动使用、5音乐、6道具、7实物、8其他
    public final static String exchange_id = CLASS_NAME + "exchange_id";        // 兑换物品id
    public final static String rule_id = CLASS_NAME + "rule_id";                // 兑换规则id
    public final static String expired_time = CLASS_NAME + "expired_time";      // 道具有效期 目前只有服装在用
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //最后修改时间
    private ZonedDateTime modifieTime;
    //兑换物品名称
    private String exchangeName;
    //兑换类型：1优惠券、2服装、3标识补卡、4活动使用、5音乐、6道具、7实物、8其他
    private Integer exchangeType;
    //兑换物品id
    private Long exchangeId;
    //兑换规则id
    private Long ruleId;
    //道具有效期 目前只有服装在用
    private Integer expiredTime;

    @Override
    public String toString() {
        return "ExchangeScoreAward{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",createTime=" + createTime +
                ",modifieTime=" + modifieTime +
                ",exchangeName=" + exchangeName +
                ",exchangeType=" + exchangeType +
                ",exchangeId=" + exchangeId +
                ",ruleId=" + ruleId +
                ",expiredTime=" + expiredTime +
                "}";
    }
}
