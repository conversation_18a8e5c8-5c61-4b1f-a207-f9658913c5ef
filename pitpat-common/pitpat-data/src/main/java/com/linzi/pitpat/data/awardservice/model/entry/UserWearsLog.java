package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_user_wears_log")
public class UserWearsLog implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //最后修改时间
    private ZonedDateTime modifieTime;
    //用户id
    private Long userId;
    //发色
    private Integer hairColor;
    //肤色
    private Integer skinColour;
    //上衣
    private Integer jacket;
    //下装
    private Integer trousers;
    //鞋子
    private Integer shoes;
    //头型
    private Integer head;
    //脸部装饰
    private Integer faceDecoration;
    //套装
    private Integer suit;
    //用户穿戴表id
    private Long userWearId;

    //-1：其他修改,1:进入游戏前修改，2：退出游戏修改
    private Integer sourceType;

    // 年龄类型 0：青年，1：中年，2：老年
    private Integer ageType;
}
