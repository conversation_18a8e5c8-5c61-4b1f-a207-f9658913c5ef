package com.linzi.pitpat.data.clubservice.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberApplyStateEnum;
import com.linzi.pitpat.data.clubservice.mapper.ClubMemberApplyMapper;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMemberApply;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberApplyQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 俱乐部会员申请表 服务类
 *
 * @since 2024-04-22
 */
@Service
public class ClubMemberApplyServiceImpl extends ServiceImpl<ClubMemberApplyMapper, ClubMemberApply> implements ClubMemberApplyService {

    @Autowired
    private ClubMemberApplyMapper clubMemberApplyMapper;

    @Override
    public ClubMemberApply findById(Long id) {
        return clubMemberApplyMapper.selectById(id);
    }

    @Override
    public int insert(ClubMemberApply clubMemberApply) {
        return clubMemberApplyMapper.insert(clubMemberApply);
    }

    @Override
    public int update(ClubMemberApply clubMemberApply) {
        return clubMemberApplyMapper.updateById(clubMemberApply);
    }

    @Override
    public int deleteById(Long id) {
        return clubMemberApplyMapper.deleteById(id);
    }


    /**
     * 获取待审批的申请
     *
     * @param clubIds
     * @param userId
     * @return
     */

    @Override
    public List<ClubMemberApply> findWaitAuditByBatchClubIdsAndUserId(List<Long> clubIds, Long userId) {
        if (CollectionUtils.isEmpty(clubIds) || userId == null) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ClubMemberApply> eq = Wrappers.lambdaQuery(ClubMemberApply.class)
                .in(ClubMemberApply::getClubId, clubIds)
                .eq(ClubMemberApply::getApplyUserId, userId)
                .eq(ClubMemberApply::getState, ClubMemberApplyStateEnum.APPLYING.getCode())
                .ge(ClubMemberApply::getExpiryDate, ZonedDateTime.now())
                .select(ClubMemberApply::getClubId);
        return clubMemberApplyMapper.selectList(eq);
    }

    @Override
    public Page<ClubMemberApply> findPageWaitAuditByClubId(int pageNum, int pageSize, Long clubId) {
        LambdaQueryWrapper<ClubMemberApply> eq = Wrappers.lambdaQuery(ClubMemberApply.class)
                .eq(ClubMemberApply::getClubId, clubId)
                .eq(ClubMemberApply::getState, ClubMemberApplyStateEnum.APPLYING.getCode())
                .ge(ClubMemberApply::getExpiryDate, ZonedDateTime.now())
                .orderByDesc(ClubMemberApply::getId);
        return clubMemberApplyMapper.selectPage(Page.of(pageNum, pageSize), eq);
    }

    @Override
    public void rejectApply(Long id, Long userId) {
        if (id == null) {
            return;
        }
        ClubMemberApply apply = new ClubMemberApply();
        apply.setId(id);
        apply.setApplyDate(ZonedDateTime.now());
        apply.setState(ClubMemberApplyStateEnum.REJECTED.getCode());
        apply.setAuditUserId(userId);
        clubMemberApplyMapper.updateById(apply);
    }

    @Override
    public void joinApply(Long id, Long userId) {
        if (id == null) {
            return;
        }
        ClubMemberApply apply = new ClubMemberApply();
        apply.setId(id);
        apply.setState(ClubMemberApplyStateEnum.JOINED.getCode());
        apply.setApplyDate(ZonedDateTime.now());
        apply.setAuditUserId(userId);
        clubMemberApplyMapper.updateById(apply);
    }

    @Override
    public void removeAll(Long clubId) {
        LambdaQueryWrapper<ClubMemberApply> eq = Wrappers.lambdaQuery(ClubMemberApply.class).eq(ClubMemberApply::getClubId, clubId).last("limit 500");
        while (clubMemberApplyMapper.delete(eq) != 0) {
        }
    }

    @Override
    public List<ClubMemberApply> findListByQuery(ClubMemberApplyQuery clubMemberApplyQuery) {
        LambdaQueryWrapper<ClubMemberApply> eq = Wrappers.lambdaQuery(ClubMemberApply.class)
                .eq(ClubMemberApply::getClubId, clubMemberApplyQuery.getClubId())
                .eq(ClubMemberApply::getState, clubMemberApplyQuery.getState())
                .ge(ClubMemberApply::getExpiryDate, ZonedDateTime.now());
        return clubMemberApplyMapper.selectList(eq);
    }

    @Override
    public Optional<ClubMemberApply> findWaitAuditByClubAndUserId(Long clubId, Long userId) {
        LambdaQueryWrapper<ClubMemberApply> eq = Wrappers.lambdaQuery(ClubMemberApply.class)
                .eq(ClubMemberApply::getClubId, clubId)
                .eq(ClubMemberApply::getApplyUserId, userId)
                .eq(ClubMemberApply::getState, ClubMemberApplyStateEnum.APPLYING.getCode())
                .ge(ClubMemberApply::getExpiryDate, ZonedDateTime.now())
                .last("limit 1");
        return Optional.ofNullable(clubMemberApplyMapper.selectOne(eq));

    }
}
