package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户批次发放多张优惠券id配置表
 *
 * <AUTHOR>
 * @since 2023-06-14
 */

@Data
@NoArgsConstructor
@TableName("zns_user_coupon_send_batch_config")
public class UserCouponSendBatchConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendBatchConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";        // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    // 最后修改时间
    public final static String coupon_id = CLASS_NAME + "coupon_id";          // 发放优惠券id
    public final static String batch_id = CLASS_NAME + "batch_id";            // batch表批次id
    public final static String num_ = CLASS_NAME + "num";                     // 发送数量默认一个
    public final static String send_count = CLASS_NAME + "send_count";        // 这个批次发送总数量(限制用)
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //发放优惠券id
    private Long couponId;
    //batch表批次id
    private Long batchId;
    //发送数量默认一个
    private Integer num;
    //这个批次发送总数量(限制用)
    private Integer sendCount;

    @Override
    public String toString() {
        return "UserCouponSendBatchConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",couponId=" + couponId +
                ",batchId=" + batchId +
                ",num=" + num +
                ",sendCount=" + sendCount +
                "}";
    }
}
