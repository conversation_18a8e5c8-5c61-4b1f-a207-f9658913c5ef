package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*奖金池变化记录表
 *
 * <AUTHOR>
 * @since 2022-06-13
 */

@Data
@NoArgsConstructor
@TableName("zns_bonus_pool_log")
public class BonusPoolLog implements java.io.Serializable {
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //收支类型：1表示增加，2表示减少
    private Integer type;
    //收入/支出金额
    private BigDecimal amount;

    @Override
    public String toString() {
        return "BonusPoolLog{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",createTime=" + createTime +
                ",type=" + type +
                ",amount=" + amount +
                "}";
    }
}
