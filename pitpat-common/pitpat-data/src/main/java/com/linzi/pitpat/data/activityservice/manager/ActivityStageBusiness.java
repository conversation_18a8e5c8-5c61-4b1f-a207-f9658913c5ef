package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.biz.ActivityParamsBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserAwardBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.ActivityStageUserInfo;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SingleStageRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.StageRankDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.StageDisqualifiedDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserStageDto;
import com.linzi.pitpat.data.activityservice.model.dto.CheatRiskLogDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityStageUser;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RunActivityStageUserService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SeriesGameplayService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.service.award.ActivityUserAwardPreService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityStageBusiness {
    private final ActivityUserAwardBizService activityUserAwardBizService;

    private final ActivityStageService activityStageService;

    private final RunActivityStageUserService runActivityStageUserService;

    private final ZnsRunActivityUserService runActivityUserService;

    private final MainActivityService mainActivityService;

    private final SeriesActivityRelService seriesActivityRelService;

    private final RedissonClient redissonClient;

    private final ZnsUserService userService;

    private final ZnsTreadmillService treadmillService;

    private final ActivityParamsBizService activityParamsBizService;

    private final ActivityUserBizService activityUserBizService;

    private final ZnsUserRunDataDetailsService userRunDataDetailsService;

    private final SeriesGameplayService seriesGameplayService;

    private final EntryGameplayService entryGameplayService;

    private final ZnsUserAccountService userAccountService;

    private final ActivityAwardConfigService activityAwardConfigService;

    private final AwardConfigService awardConfigService;

    private final AwardConfigBizService awardConfigBizService;

    private final ActivityUserAwardPreService activityUserAwardPreService;
    private final MongoTemplate mongoTemplate;


    public void runEnd(ZnsUserRunDataDetailsEntity userRunDataDetail, Integer propRunTime, boolean complete, boolean best) {
        log.info("stage activity runEnd come,detail:{},activityId:{},userId:{}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
        ActivityStageUserInfo stageUserInfo = findStageUserInfo(userRunDataDetail.getActivityId(), userRunDataDetail.getCreateTime(), userRunDataDetail.getUserId());
        if (!stageUserInfo.getIsStageActivity()) {
            log.info("当前活动不是阶段活动");
            return;
        }
        if (stageUserInfo.getCurrentStage() != null) {
            RunActivityStageUser activityStageUser = stageUserInfo.getCurrentRunActivityStageUser();
            if (activityStageUser.getRunDataDetailsId() != null) {
                log.info("不是第一次参加");
                return;
            }


            Long treadmillId = userRunDataDetail.getTreadmillId();
            ZnsTreadmillEntity treadmill = treadmillService.findById(treadmillId);

            activityStageUser.setActivityId(userRunDataDetail.getActivityId());
            activityStageUser.setCompleteTime(ZonedDateTime.now());
            activityStageUser.setRunTime(userRunDataDetail.getRunTime());
            activityStageUser.setRunTimeMillisecond(userRunDataDetail.getRunTimeMillisecond());
            activityStageUser.setIsCheat(userRunDataDetail.getIsCheat());
            activityStageUser.setRunMileage(userRunDataDetail.getRunMileage());
            activityStageUser.setPropRunTime(propRunTime);
            activityStageUser.setIsComplete(complete ? 1 : 0);
            activityStageUser.setTargetRunMileage(userRunDataDetail.getDistanceTarget().intValue());
            activityStageUser.setTargetRunTime(userRunDataDetail.getTimeTarget());
            activityStageUser.setRunDataDetailsId(userRunDataDetail.getId());
            activityStageUser.setIsBest(best && activityStageUser.getIsCheat() == 0 ? 1 : 0);

            activityStageUser.setEquipmentModel(treadmill.getProductCode());
            if (activityStageUser.getIsBest() == 1) {
                List<RunActivityStageUser> activityStageUsers = runActivityStageUserService.findListByUserAndAct(activityStageUser.getUserId(), activityStageUser.getActivityId());
                activityStageUsers = activityStageUsers.stream().filter(k -> k.getIsBest() == 1).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(activityStageUsers)) {
                    for (RunActivityStageUser stageUser : activityStageUsers) {
                        stageUser.setIsBest(0);
                        log.info("取消最好成绩:{}", stageUser.getStageId());
                        runActivityStageUserService.update(stageUser);
                    }
                }
            }
            runActivityStageUserService.update(activityStageUser);

            RLock lock = redissonClient.getLock(RedisConstants.STAGE_ACTIVITY_STAGE_RERANK + activityStageUser.getStageId());
            LockHolder.tryLock(lock, 10, () -> {
                //单赛事 系列赛子阶段
                reRank(activityStageUser.getActivityId(), activityStageUser.getStageId(), false);
            });

        }
    }


    public ActivityStageUserInfo findStageUserInfo(Long activityId, ZonedDateTime createTime, Long userId) {
        ActivityStageUserInfo stageUserInfo = new ActivityStageUserInfo();
        List<ActivityStage> stageList = activityStageService.findByActId(activityId);
        stageUserInfo.setStageList(stageList);
        stageUserInfo.setIsStageActivity(!CollectionUtils.isEmpty(stageList));

        MainActivity mainActivity = mainActivityService.findById(activityId);
        //找出当前阶段
        for (ActivityStage stage : stageList) {

            if (createTime.isAfter(stage.getStartTime().minusMinutes(mainActivity.getWaitTime())) && createTime.isBefore(stage.getEndTime())) {
                stageUserInfo.setCurrentStage(stage);
                log.info("当前阶段:{}", stage.getId());
                break;
            }
        }
        if (stageUserInfo.getCurrentStage() != null) {
            RunActivityStageUser runActivityStageUser = runActivityStageUserService.findByUserAndStage(userId, stageUserInfo.getCurrentStage().getId());
            stageUserInfo.setCurrentRunActivityStageUser(runActivityStageUser);
            log.info("当前阶段用户:{}", runActivityStageUser);
        }

        log.info("Starting activity stage stageUserInfo:{}", stageUserInfo);
        return stageUserInfo;
    }

    public boolean canRun(Long activityEntityId, Long userId, ZonedDateTime date) {
        ActivityStageUserInfo stageUserInfo = findStageUserInfo(activityEntityId, date, userId);
        RunActivityStageUser currentRunActivityStageUser = stageUserInfo.getCurrentRunActivityStageUser();
        if (currentRunActivityStageUser != null && currentRunActivityStageUser.getRunDataDetailsId() != null) {
            return false;
        }
        return true;
    }

    //重新排名
    public void reRank(Long activityId, Long stageId, boolean isCheatRank) {
        List<RunActivityStageUser> activityStageUsers = getReRankUser(activityId, stageId, isCheatRank);
        runActivityStageUserService.update(activityStageUsers);

    }

    private List<RunActivityStageUser> getReRankUser(Long activityId, Long stageId, boolean isRankCheat) {
        List<ActivityStage> stageList = activityStageService.findByActId(activityId);
        if (CollectionUtils.isEmpty(stageList)) {
            log.info("当前活动没有阶段");
            return null;
        }
        List<RunActivityStageUser> activityStageUsers = runActivityStageUserService.findByStageId(stageId);
        ActivityStage activityStage = activityStageService.findById(stageId);
        if (activityStage.getEndTime().isAfter(ZonedDateTime.now())) {
            log.info("当前阶段未结束，不排无成绩名");
            //过滤掉无成绩
            activityStageUsers = activityStageUsers.stream().filter(k -> k.getRunTime() > 0).collect(Collectors.toList());
        }

        for (RunActivityStageUser activityStageUser : activityStageUsers) {
            if (isRankCheat) {
                if (activityStageUser.getIsInvalid() == 1) {
                    activityStageUser.setSortField(3);
                    continue;
                }
            } else {
                if (activityStageUser.getIsCheat() == 1 || activityStageUser.getIsInvalid() == 1) {
                    activityStageUser.setSortField(3);
                    continue;
                }
            }

            if (activityStageUser.getIsComplete() == 0 && activityStageUser.getRunTime() == 0) {
                activityStageUser.setSortField(2);
                continue;
            }
            if (activityStageUser.getIsComplete() == 0 && activityStageUser.getRunTime() > 0) {
                activityStageUser.setSortField(1);
            }
        }
        for (RunActivityStageUser activityStageUser : activityStageUsers) {
            if (activityStageUser.getPropRunTime() == null || activityStageUser.getPropRunTime() == 0) {
                activityStageUser.setPropRunTime(activityStageUser.getRunTimeMillisecond());
            }
        }

        activityStageUsers.sort(Comparator.comparingInt(RunActivityStageUser::getIsComplete).reversed()
                .thenComparing(RunActivityStageUser::getPropRunTime)
                .thenComparing(RunActivityStageUser::getSortField)
                .thenComparing(RunActivityStageUser::getReportTime)
                .thenComparing(RunActivityStageUser::getId));
        for (int i = 0; i < activityStageUsers.size(); i++) {
            RunActivityStageUser activityStageUser = activityStageUsers.get(i);
            activityStageUser.setRank(i + 1);
            log.info("阶段重新排名，用户：{}，阶段id：{}，重新排名为：{}", activityStageUser.getUserId(), stageId, i + 1);
        }
        return activityStageUsers;
    }


    //系列赛 单赛事
    public void reRank(Long activityId) {
        List<ActivityStage> stageList = activityStageService.findByActId(activityId);
        if (CollectionUtils.isEmpty(stageList)) {
            log.info("当前活动没有阶段");
            return;
        }
//        Long preActivityId = activityId;
//        Long subActivityId = null;
//
//        MainActivity mainActivity = mainActivityService.findById(activityId);
//        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())){
//            List<Long> subIds = seriesActivityRelService.findSubActivityId(activityId);
//            if (!CollectionUtils.isEmpty(subIds)){
//                subActivityId = subIds.get(0);
//            }
//        }
//        if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
//            MainActivity preActivity = seriesActivityRelService.getPreActivity(activityId);
//            preActivityId = preActivity.getId();
//            subActivityId = activityId;
//        }
//        doRank(preActivityId);
        RLock lock = redissonClient.getLock(RedisConstants.STAGE_ACTIVITY_RERANK + activityId);
        LockHolder.tryLock(lock, 10, () -> {
            doRank(activityId);
        });

    }

    private void doRank(Long activityId) {
        if (activityId == null) {
            return;
        }
        List<RunActivityStageUser> activityStageUsers = runActivityStageUserService.findByActId(activityId);
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityId);
        allActivityUser = allActivityUser.stream().filter(k -> k.getIsComplete() == 1).collect(Collectors.toList());
        for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
            if (activityUser.getPropRunTime() == null || activityUser.getPropRunTime() == 0) {
                activityUser.setPropRunTime(activityUser.getRunTimeMillisecond());
            }
        }
        allActivityUser.sort(Comparator.comparingInt(ZnsRunActivityUserEntity::getPropRunTime)
                .thenComparing(ZnsRunActivityUserEntity::getCreateTime)
                .thenComparing(ZnsRunActivityUserEntity::getId));
        for (int i = 0; i < allActivityUser.size(); i++) {
            ZnsRunActivityUserEntity activityUser = allActivityUser.get(i);
            activityUser.setRank(i + 1);
            activityStageUsers.stream().filter(k -> k.getUserId().equals(activityUser.getUserId())
                    && k.getIsBest() == 1).findFirst().ifPresent(a -> activityUser.setRunDataDetailsId(a.getRunDataDetailsId()));
            long noCheatNum = activityStageUsers.stream().filter(k -> k.getIsCheat() == 0).count();
            if (noCheatNum == 0) {
                activityUser.setIsCheat(1);
            } else {
                activityUser.setIsCheat(0);
            }
            log.info("总榜重新排名，用户：{}，活动id：{}，成绩：{}，重新排名为：{}", activityUser.getUserId(), activityId, activityUser.getPropRunTime(), i + 1);
        }
        runActivityUserService.updateBatchById(allActivityUser);
    }


    public List<SingleStageRankDto> singleStageRank(Long mainActivityId) {
        List<SingleStageRankDto> singleStageRankDtoList = new ArrayList<>();

        List<Long> userIds = runActivityUserService.findAllActivityUser(mainActivityId).stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        List<ZnsUserEntity> users = userService.findByIds(userIds);
        Map<Long, ZnsUserEntity> userMap = users.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));

        List<RunActivityStageUser> allActivityStageUser = runActivityStageUserService.findByActId(mainActivityId);


        List<ActivityStage> stageList = activityStageService.findByActId(mainActivityId);
        //阶段组装
        for (ActivityStage stage : stageList) {
            SingleStageRankDto singleStageRankDto = new SingleStageRankDto();
            List<RunActivityStageUser> activityStageUsers = allActivityStageUser.stream().filter(k -> k.getStageId().equals(stage.getId()) && k.getRank() > 0).collect(Collectors.toList());
            List<StageRankDto> rankDtos = activityStageUsers.stream().map(k -> toRankDto(k, userMap)).sorted(Comparator.comparing(StageRankDto::getRank)).collect(Collectors.toList());
            singleStageRankDto.setRankDtos(rankDtos);
            singleStageRankDto.setLevel(stage.getLevel());
            singleStageRankDtoList.add(singleStageRankDto);
        }
        //总榜组装
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(mainActivityId);
        //TODO: 还需要过滤掉审核成绩(活动已经结束)失败用户(可以用rank)
        allActivityUser = allActivityUser.stream().filter(k -> k.getIsComplete() == 1 && k.getIsCheat() == 0 && k.getRank() > 0).collect(Collectors.toList());
        List<StageRankDto> rankDtos = allActivityUser.stream().map(k -> toRankDto(k, userMap, allActivityStageUser)).sorted(Comparator.comparing(StageRankDto::getRank)).collect(Collectors.toList());
        SingleStageRankDto singleStageRankDto = new SingleStageRankDto();
        singleStageRankDto.setRankDtos(rankDtos);
        singleStageRankDto.setLevel(0);
        singleStageRankDtoList.add(singleStageRankDto);

        singleStageRankDtoList.sort(Comparator.comparing(SingleStageRankDto::getLevel));

        Optional<MainActivity> mainActivityOp = mainActivityBizService.getMainActivity(mainActivityId);
        if (mainActivityOp.isPresent()) {
            MainActivity mainActivity = mainActivityOp.get();
            if (mainActivity.isFinishAndAwardSend() && !CollectionUtils.isEmpty(rankDtos)) {
                Map<Long, BigDecimal> userGainsAmount = activityUserAwardBizService.sumUserActivityGainsAmount(mainActivityId);
                rankDtos.stream().forEach(a -> {
                    a.setBonus(userGainsAmount.getOrDefault(a.getUserId(), new BigDecimal(0)));
                });
            }
        }
        return singleStageRankDtoList;
    }

    private final MainActivityBizService mainActivityBizService;

    private StageRankDto toRankDto(ZnsRunActivityUserEntity activityUser, Map<Long, ZnsUserEntity> userMap, List<RunActivityStageUser> allActivityStageUser) {
        ZnsUserEntity user = userMap.get(activityUser.getUserId());

        StageRankDto rankDto = new StageRankDto();
        rankDto.setRank(activityUser.getRank());
        rankDto.setUserId(activityUser.getUserId());
        rankDto.setNickname(user != null ? user.getFirstName() : null);
        rankDto.setAvatar(user != null ? user.getHeadPortrait() : null);
        rankDto.setRunTime(activityUser.getRunTimeMillisecond());
        rankDto.setRunMileage(activityUser.getRunMileage().intValue());
        rankDto.setCompleteTime(activityUser.getCompleteTime());
        rankDto.setCompleteState(1);
        rankDto.setBonus(null);
        rankDto.setCheatingState(activityUser.getIsCheat());
        allActivityStageUser.stream().filter(k -> k.getUserId().equals(activityUser.getUserId()) && k.getIsBest() == 1)
                .findFirst().ifPresent(m -> rankDto.setEquipmentModel(m.getEquipmentModel()));

        return rankDto;
    }

    private StageRankDto toRankDto(RunActivityStageUser runActivityStageUser, Map<Long, ZnsUserEntity> userMap) {
        ZnsUserEntity user = userMap.get(runActivityStageUser.getUserId());
        StageRankDto rankDto = new StageRankDto();
        rankDto.setRank(runActivityStageUser.getRank());
        rankDto.setUserId(runActivityStageUser.getUserId());
        rankDto.setNickname(user != null ? user.getFirstName() : null);
        rankDto.setAvatar(user != null ? user.getHeadPortrait() : null);
        rankDto.setRunTime(runActivityStageUser.getRunTimeMillisecond());
        rankDto.setRunMileage(runActivityStageUser.getRunMileage().intValue());
        rankDto.setCompleteTime(runActivityStageUser.getCompleteTime());
        rankDto.setCheatingState(runActivityStageUser.getIsCheat());
        rankDto.setEquipmentModel(runActivityStageUser.getEquipmentModel());
        if (runActivityStageUser.getIsCheat() == 1 || runActivityStageUser.getIsInvalid() == 1) {
            //作弊
            rankDto.setCompleteState(3);
        } else {
            if (runActivityStageUser.getIsComplete() == 1) {
                //完成
                rankDto.setCompleteState(1);
            } else {
                if (runActivityStageUser.getRunTime() > 0) {
                    //有数据但未完成
                    rankDto.setCompleteState(0);
                } else {
                    //无数据未完成
                    rankDto.setCompleteState(2);
                }
            }
        }
        return rankDto;
    }

    public List<AwardReviewUserStageDto> queryStageReviewList(Long activityId, List<RunActivityStageUser> allActivityStageUsers, Boolean isShowTotal) {
        List<AwardReviewUserStageDto> list = new ArrayList<>();
        MainActivity mainActivity = mainActivityService.findById(activityId);

        List<ActivityStage> stageList = activityStageService.findByActId(activityId);
        //活动阶段
        for (ActivityStage stage : stageList) {
            AwardReviewUserStageDto awardReviewUserStageDto = new AwardReviewUserStageDto();
            //isRankCheat 审核状态并且不显示总榜展示所有成绩
            List<RunActivityStageUser> activityStageUsers = getReRankUser(activityId, stage.getId(), !mainActivity.isNotReview() && !isShowTotal).stream()
                    .filter(k -> k.getStageId().equals(stage.getId()) && k.getRank() > 0).collect(Collectors.toList());
            activityStageUsers.sort(Comparator.comparing(RunActivityStageUser::getRank));
            List<AwardReviewUserDto> reviewUserDtos = activityStageUsers.stream().map(k -> activityStageUserToAwardReviewUserDto(mainActivity, k, isShowTotal)).collect(Collectors.toList());
            awardReviewUserStageDto.setAwardReviewUserDtoList(reviewUserDtos);
            awardReviewUserStageDto.setStageId(stage.getId());
            awardReviewUserStageDto.setLevel(stage.getLevel());
            list.add(awardReviewUserStageDto);
        }
        if (isShowTotal) {
            //加入总榜
            List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityId);
            list.add(allActivityUserToAwardReviewUserStageDto(allActivityUser, activityId, allActivityStageUsers));
        }

        list.sort(Comparator.comparing(AwardReviewUserStageDto::getLevel));
        return list;

    }

    public List<AwardReviewUserStageDto> queryStageReviewList(Long activityId, Boolean isShowTotal) {
        List<RunActivityStageUser> allActivityStageUsers = runActivityStageUserService.findByActId(activityId);

        return queryStageReviewList(activityId, allActivityStageUsers, isShowTotal);

    }

    private AwardReviewUserStageDto allActivityUserToAwardReviewUserStageDto(List<ZnsRunActivityUserEntity> allActivityUser, Long activityId, List<RunActivityStageUser> allActivityStageUsers) {

        AwardReviewUserStageDto awardReviewUserStageDto = new AwardReviewUserStageDto();

        //重新计算总榜
        recalculateTotalRank(allActivityUser, allActivityStageUsers);


        MainActivity mainActivity = mainActivityService.findById(activityId);
        List<Long> activityIdList;
        if (mainActivity.getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())) {
            activityIdList = seriesActivityRelService.findSubActivityId(mainActivity.getId());
        } else {
            activityIdList = new ArrayList<>();
            activityIdList.add(activityId);
        }
        ZnsUserEntity surpassUser = activityParamsBizService.findSurpassUser(activityId);
        ZnsRunActivityUserEntity surpassRunActivityUser;
        Long surpassCount = 0l;
        if (Objects.nonNull(surpassUser)) {
            surpassRunActivityUser = allActivityUser.stream().filter(a -> a.getUserId().equals(surpassUser.getId())).findFirst().orElse(null);
            ZnsRunActivityUserEntity finalSurpassRunActivityUser = surpassRunActivityUser;
            surpassCount = allActivityUser.stream().filter(a -> activityUserBizService.isSurpass(finalSurpassRunActivityUser, a)).count();
        } else {
            surpassRunActivityUser = null;
        }

        allActivityUser = allActivityUser.stream().filter(znsRunActivityUserEntity -> znsRunActivityUserEntity.getRank() > 0).sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRank)).toList();
        long completeCount = allActivityUser.stream().filter(a -> a.getIsComplete() == 1).count();

        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(mainActivity.getId());
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        List<AwardConfigDto> awardConfigDtos = new ArrayList<>();
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(collect)) {
            awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
        }
        List<AwardConfigDto> finalAwardConfigDtos = awardConfigDtos;
        Long finalSurpassCount = surpassCount;

        List<AwardReviewUserDto> reviewUserDtos = allActivityUser.stream().map(activityUser -> {

            ZnsUserEntity user = userService.findByIdWithoutLogicDelete(activityUser.getUserId());

            AwardReviewUserDto reviewUserDto = new AwardReviewUserDto();
            reviewUserDto.setId(activityUser.getId());
            reviewUserDto.setRank(activityUser.getRank());
            reviewUserDto.setUserId(activityUser.getUserId());
            reviewUserDto.setUserCode(user.getUserCode());
            reviewUserDto.setUserNickname(user.getFirstName());
            reviewUserDto.setUserCountry(user.getCountry());
            reviewUserDto.setRaceResult(String.valueOf(activityUser.getPropRunTime() / 1000));
            reviewUserDto.setRaceResultType("2");

            List<RunActivityStageUser> activityStageUsers = allActivityStageUsers.stream().filter(k -> k.getUserId().equals(user.getId())).collect(Collectors.toList());
            reviewUserDto.setStatus(activityStageUsers.stream().filter(k -> k.getIsComplete() == 1).anyMatch(k -> k.getIsInvalid() == 0) ? 1 : -1);


            Currency userCurrency = userAccountService.getUserCurrency(activityUser.getUserId());
            reviewUserDto.setRewardCurrency(userCurrency);
            if (activityUser.getIsComplete() == 1) {
                reviewUserDto.setCompleteRewardAmounts(awardConfigBizService.getRewardAmount(finalAwardConfigDtos, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), userCurrency, null, completeCount));
                reviewUserDto.setCompleteRewardPoints(awardConfigBizService.getRewardPoints(finalAwardConfigDtos, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), null, completeCount));
            }
            if (activityUserBizService.isSurpass(surpassRunActivityUser, activityUser)) {
                reviewUserDto.setSurpassRewardAmounts(awardConfigBizService.getRewardAmount(finalAwardConfigDtos, AwardSentTypeEnum.SURPASS_AWARD.getType(), userCurrency, null, finalSurpassCount));
                reviewUserDto.setSurpassRewardPoints(awardConfigBizService.getRewardPoints(finalAwardConfigDtos, AwardSentTypeEnum.SURPASS_AWARD.getType(), null, finalSurpassCount));
            }

            if (Objects.nonNull(activityUser.getRank())) {
                reviewUserDto.setRewardAmounts(awardConfigBizService.getRewardAmount(finalAwardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), userCurrency, activityUser.getRank(), completeCount));
                reviewUserDto.setRewardPoints(awardConfigBizService.getRewardPoints(finalAwardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), activityUser.getRank(), completeCount));
            }

            List<ZnsUserRunDataDetailsEntity> userRunDataDetailsEntityList = userRunDataDetailsService.getUserDetailsByActivityIds(user.getId(), activityIdList);
            if (!CollectionUtils.isEmpty(userRunDataDetailsEntityList)) {
                Long cheatRunCount = userRunDataDetailsEntityList.stream().filter(u -> u.getIsCheat() == 1).count();
                reviewUserDto.setCheatRunCount(cheatRunCount.intValue());
                List<Long> runIds = userRunDataDetailsEntityList.stream().map(ZnsUserRunDataDetailsEntity::getId).collect(Collectors.toList());
                Query query = Query.query(Criteria.where("run_id").in(runIds));
                List<CheatRiskLogDto> cheatRiskLogDtos = mongoTemplate.find(query, CheatRiskLogDto.class, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
                Long count = cheatRiskLogDtos.stream().filter(c -> c.getMsg().contains("空载持续时间大于") || "用户空跑".equals(c.getMsg())).count();
                reviewUserDto.setCheatCheatHitCount(count);
            }

            return reviewUserDto;


        }).collect(Collectors.toList());
        reviewUserDtos.sort(Comparator.comparing(AwardReviewUserDto::getStatus).reversed().thenComparing(AwardReviewUserDto::getRank));
        awardReviewUserStageDto.setLevel(0);
        awardReviewUserStageDto.setAwardReviewUserDtoList(reviewUserDtos);

        return awardReviewUserStageDto;
    }

    private void recalculateTotalRank(List<ZnsRunActivityUserEntity> allActivityUser, List<RunActivityStageUser> allActivityStageUsers) {
        for (RunActivityStageUser stageUser : allActivityStageUsers) {
            if (stageUser.getPropRunTime() == null || stageUser.getPropRunTime() == 0) {
                stageUser.setPropRunTime(stageUser.getRunTimeMillisecond());
            }
        }

        for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
            RunActivityStageUser runActivityStageUser = allActivityStageUsers.stream()
                    .filter(k -> k.getUserId().equals(activityUser.getUserId()) && k.getIsComplete() == 1
                            && k.getIsInvalid() == 0 && k.getIsCheat() == 0).min(Comparator.comparing(RunActivityStageUser::getPropRunTime)).orElse(null);

            if (runActivityStageUser != null) {
                activityUser.setPropRunTime(runActivityStageUser.getPropRunTime());
                activityUser.setRunTime(runActivityStageUser.getRunTime());
                activityUser.setRunMileage(runActivityStageUser.getRunMileage());
            } else {
                activityUser.setRank(-1);
            }
        }
        allActivityUser = allActivityUser.stream().filter(k -> k.getRank() > 0).collect(Collectors.toList());
        allActivityUser.sort(Comparator.comparingInt(ZnsRunActivityUserEntity::getPropRunTime)
                .thenComparing(ZnsRunActivityUserEntity::getCreateTime)
                .thenComparing(ZnsRunActivityUserEntity::getId));
        for (int i = 0; i < allActivityUser.size(); i++) {
            ZnsRunActivityUserEntity activityUser = allActivityUser.get(i);
            activityUser.setRank(i + 1);
        }
    }

    private String getBestGrade(Long userId, List<RunActivityStageUser> allActivityStageUsers) {
        for (RunActivityStageUser stageUser : allActivityStageUsers) {
            if (stageUser.getPropRunTime() == null || stageUser.getPropRunTime() == 0) {
                stageUser.setPropRunTime(stageUser.getRunTimeMillisecond());
            }
        }
        RunActivityStageUser runActivityStageUser = allActivityStageUsers.stream()
                .filter(k -> k.getIsInvalid() == 0 && k.getIsComplete() == 1 && k.getRank() > 0 && userId.equals(k.getUserId()))
                .min(Comparator.comparing(RunActivityStageUser::getPropRunTime)).orElse(null);
        return runActivityStageUser == null ? null : String.valueOf((runActivityStageUser.getPropRunTime() / 1000));
    }


    private Long relocateActivity(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);

        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            //系列赛查阶段活动
            List<Long> list = seriesActivityRelService.findSubActivityId(activityId);
            activityId = list.stream().findFirst().orElse(activityId);
        }
        return activityId;
    }

    @NotNull
    private AwardReviewUserDto activityStageUserToAwardReviewUserDto(MainActivity mainActivity, RunActivityStageUser k, Boolean isShowTotal) {
        ZnsUserEntity user = userService.findByIdWithoutLogicDelete(k.getUserId());

        AwardReviewUserDto reviewUserDto = new AwardReviewUserDto();
        reviewUserDto.setId(k.getId());
        reviewUserDto.setUserId(k.getUserId());
        reviewUserDto.setUserCode(user.getUserCode());
        reviewUserDto.setUserNickname(user.getFirstName());
        reviewUserDto.setUserCountry(user.getCountry());
        reviewUserDto.setRaceResult(k.getRunTime().toString());
        reviewUserDto.setRaceResultType("2");
        if (k.getIsCheat() == 1) {
            reviewUserDto.setCheatingDuration(k.getRunTime());
            reviewUserDto.setCheatingDistance(k.getRunMileage());
        }
        reviewUserDto.setRank(k.getRank());

        reviewUserDto.setStatus(k.getIsInvalid() == 0 ? 1 : -1);
        if (isShowTotal && k.getIsCheat() == 1) {
            // 作弊也表示
            reviewUserDto.setStatus(-1);
        }

        if (NumberUtils.geZero(k.getRunDataDetailsId())) {
            ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.findByIdActually(k.getRunDataDetailsId());
            reviewUserDto.setCheatRunCount(details.getIsCheat() == 1 ? 1 : 0);
            Query query = Query.query(Criteria.where("run_id").is(k.getRunDataDetailsId()));
            List<CheatRiskLogDto> cheatRiskLogDtos = mongoTemplate.find(query, CheatRiskLogDto.class, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
            Long count = cheatRiskLogDtos.stream().filter(c -> c.getMsg().contains("空载持续时间大于") || "用户空跑".equals(c.getMsg())).count();
            reviewUserDto.setCheatCheatHitCount(count);
        }

        return reviewUserDto;
    }

    //预览
    public List<AwardReviewUserStageDto> stageRecalculatePreview(Long activityId, List<StageDisqualifiedDto> disqualifiedLists) {

        List<RunActivityStageUser> activityStageUsers = runActivityStageUserService.findByActId(activityId);

        //标记无效成绩
        for (StageDisqualifiedDto disqualifiedDto : disqualifiedLists) {
            activityStageUsers.stream()
                    .filter(k -> k.getStageId().equals(disqualifiedDto.getStageId()) && disqualifiedDto.getDisqualifiedLists().contains(k.getUserId()))
                    .forEach(k -> k.setIsInvalid(1));
        }

        return queryStageReviewList(activityId, activityStageUsers, true);
    }

    public List<Long> stageRecalculate(Long activityId, List<StageDisqualifiedDto> disqualifiedLists) {

        List<RunActivityStageUser> activityStageUsers = runActivityStageUserService.findByActId(activityId);

        List<Long> disqualifiedUserIds = disqualifiedLists.stream().flatMap(t -> t.getDisqualifiedLists().stream()).distinct().collect(Collectors.toList());

        //过滤掉不受影响到用户
        activityStageUsers = activityStageUsers.stream().filter(k -> disqualifiedUserIds.contains(k.getUserId())).collect(Collectors.toList());

        for (RunActivityStageUser activityStageUser : activityStageUsers) {
            if (activityStageUser.getPropRunTime() == null || activityStageUser.getPropRunTime() == 0) {
                activityStageUser.setPropRunTime(activityStageUser.getRunTimeMillisecond());
            }
        }

        //标记无效成绩
        for (StageDisqualifiedDto disqualifiedDto : disqualifiedLists) {
            activityStageUsers.stream()
                    .filter(k -> k.getStageId().equals(disqualifiedDto.getStageId()) && disqualifiedDto.getDisqualifiedLists().contains(k.getUserId()))
                    .forEach(k -> {
                        k.setIsInvalid(1);
                        k.setIsBest(0);
                        log.info("用户{}在{}阶段被取消", k.getUserId(), disqualifiedDto.getStageId());
                    });
        }
        runActivityStageUserService.update(activityStageUsers);

        //重新计算活动成绩,重选最佳成绩
        Map<Long, List<RunActivityStageUser>> activityStageUserMap = activityStageUsers.stream().collect(Collectors.groupingBy(RunActivityStageUser::getUserId));
        activityStageUserMap.forEach((userId, stageUsers) -> {
            //重新计算赛事成绩
            if (stageUsers.stream().noneMatch(k -> k.getIsBest() == 1)) {
                RunActivityStageUser runActivityStageUser = stageUsers.stream().filter(k -> k.getIsComplete() == 1 && k.getIsInvalid() == 0 && k.getIsCheat() == 0)
                        .min(Comparator.comparing(RunActivityStageUser::getPropRunTime))
                        .orElse(null);
                //重新更新用户成绩
                reUpdateUserGrade(activityId, userId, runActivityStageUser);
            }
        });


        Map<Long, List<RunActivityStageUser>> map = activityStageUsers.stream().collect(Collectors.groupingBy(RunActivityStageUser::getUserId));
        List<Long> list = new ArrayList<>(map.keySet());
        map.forEach((userId, stageUsers) -> {
            if (stageUsers.stream().anyMatch(k -> k.getIsInvalid() == 0 && k.getIsComplete() == 1 && k.getIsCheat() == 0)) {
                list.remove(userId);
            }
        });
        return list;
    }

    private void reUpdateUserGrade(Long activityId, Long userId, RunActivityStageUser runActivityStageUser) {
        log.info("reUpdateUserGrade,activityId:{},userId:{},runActivityStageUser:{}", activityId, userId, runActivityStageUser);
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            List<Long> subActivityIds = seriesActivityRelService.findSubActivityId(activityId);
            Long segmentActivityId = subActivityIds.get(0);
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(segmentActivityId, userId);
            if (runActivityStageUser == null) {
                activityUser.setRank(-1);
            } else {
                activityUser.setRunMileage(runActivityStageUser.getRunMileage());
                activityUser.setRunDataDetailsId(runActivityStageUser.getRunDataDetailsId());
                activityUser.setRunTimeMillisecond(runActivityStageUser.getRunTimeMillisecond());
                activityUser.setRunTime(runActivityStageUser.getRunTime());
                activityUser.setPropRunTime(runActivityStageUser.getPropRunTime());
            }
            runActivityUserService.updateById(activityUser);
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userId);
        if (runActivityStageUser == null) {
            activityUser.setRank(-1);
        } else {
            activityUser.setRunMileage(runActivityStageUser.getRunMileage());
            activityUser.setRunDataDetailsId(runActivityStageUser.getRunDataDetailsId());
            activityUser.setRunTimeMillisecond(runActivityStageUser.getRunTimeMillisecond());
            activityUser.setRunTime(runActivityStageUser.getRunTime());
            activityUser.setPropRunTime(runActivityStageUser.getPropRunTime());
        }
        runActivityUserService.updateById(activityUser);

        if (runActivityStageUser != null) {
            runActivityStageUser.setIsBest(1);
            runActivityStageUserService.update(runActivityStageUser);
        }
    }
}
