package com.linzi.pitpat.data.activityservice.manager.api;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.data.activityservice.biz.NewPersonPkBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.NewUserPropGradeReportDto;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigAdminExtVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.vo.CouponAwardDto;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.courseservice.model.request.CourseUpdateRequest;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.resp.DetailInfoVo;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/20 3:43
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class NewPersonPkManager {
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final UserCouponBizService userCouponBizService;
    private final NewPersonPkBizService newPersonPkBizService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserWearsBagService userWearsBagService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;


    /**
     * 新手PK弹窗
     *
     * @param runDataDetailsId
     * @return
     */
    public Map<String, Object> getNewUserOfflinePkPop(Long runDataDetailsId) {
        //查询对应活动
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .runDataDetailsId(runDataDetailsId).isDelete(0)
                .build();

        ZnsRunActivityUserEntity self = runActivityUserService.findOne(userQuery);
        if (Objects.isNull(self)) {
            return null;
        }
        ZnsRunActivityEntity byId = runActivityService.findById(self.getActivityId());
        ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.getByType(2, 4);
        Map map = new HashMap();
        if (Objects.nonNull(runActivityConfig)) {
            String activityConfig = runActivityConfig.getActivityConfig();
            map = JsonUtil.readValue(activityConfig);
        }
        if (byId.getActivityType() == 12) {
            //替换失败图片
            if (self.getIsComplete().equals(YesNoStatus.NO.getCode())) {
                String advertisingImageNoFinish = MapUtils.getString(map, "advertisingImageNoFinish");
                if (StringUtils.hasText(advertisingImageNoFinish)) {
                    map.put("advertisingImage", advertisingImageNoFinish);
                }
            }
            //替换走步机的活动跳转url
            Map<String, Object> jumpParam = JsonUtil.readValue(map.get("jumpParam"));
            map.put("jumpParam", JsonUtil.writeString(jumpParam));
            return map;
        } else {
            RunActivityUserQuery userQueryOne = RunActivityUserQuery.builder()
                    .activityId(self.getActivityId()).userId(self.getUserId()).isDelete(0)
                    .build();

            ZnsRunActivityUserEntity other = runActivityUserService.findOne(userQueryOne);

            ZnsUserRunDataDetailsEntity selfDetails = userRunDataDetailsService.findById(runDataDetailsId);
            ZnsUserRunDataDetailsEntity otherDetails = userRunDataDetailsService.findById(other.getRunDataDetailsId());
            //填充新手1v1比赛结果
            fillPkResult(selfDetails, otherDetails, map);
            return map;
        }
    }


    /**
     * 填充新手1v1比赛结果
     *
     * @param selfDetails
     * @param otherDetails
     * @param map
     */
    private void fillPkResult(ZnsUserRunDataDetailsEntity selfDetails, ZnsUserRunDataDetailsEntity otherDetails, Map map) {
        if (Objects.isNull(selfDetails)) return;
        Long userId = selfDetails.getUserId();
        Long actId = selfDetails.getActivityId();
        log.info("ZnsRunActivityServiceImpl#fillPkResult---新手1v1比赛结果,userId：{}，activityId：{}，detailId：{}，执行开始", userId, actId, selfDetails.getId());

        //比赛是否胜利
        ZnsRunActivityEntity activityEntity = runActivityService.findById(actId);
        boolean win = newPersonPkBizService.isWin(activityEntity, selfDetails, otherDetails);
        log.info("ZnsRunActivityServiceImpl#fillPkResult---新手1v1比赛结果,userId：{}，activityId：{}，detailId：{}，比赛是否胜利：{}", userId, actId, selfDetails.getId(), win);
        map.put("isWin", win);

        //跑步失败
        if (!win) {
            //发优惠券
            CourseUpdateRequest po = new CourseUpdateRequest();
            userCouponBizService.sendUserCoupon(po, userId);
            //替换失败图片
            String advertisingImageNoFinish = MapUtils.getString(map, "advertisingImageNoFinish");
            if (StringUtils.hasText(advertisingImageNoFinish)) {
                map.put("advertisingImage", advertisingImageNoFinish);
            }
        }

        //替换走步机的活动跳转url
        map.put("jumpParam", JsonUtil.writeString(map.get("jumpParam")));

        log.info("ZnsRunActivityServiceImpl#fillPkResult---新手1v1比赛结果,userId：{}，activityId：{}，detailId：{}，执行结束，map：{}", userId, actId, selfDetails.getId(), JsonUtil.writeString(map.get("jumpParam")));
    }


    /**
     * 获取新用户PK赛配多人PK配置明细
     *
     * @return
     */
    public NewPkMultipleConfigAdminExtVo getMultipleConfig() {
        NewPersonPkVo newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(2L);
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(12, null);
        // 活动配置
        String config = activityConfig.getActivityConfig();
        NewPkMultipleConfigVo newPkMultipleConfigVo = JsonUtil.readValue(config, NewPkMultipleConfigVo.class);
        newPkMultipleConfigVo.setIsMusic(newPersonPkVo.getIsMusic());
        newPkMultipleConfigVo.setMusicList(newPersonPkVo.getMusicList());
        List<DetailInfoVo> detailVoIds = new ArrayList<>();
        newPkMultipleConfigVo.getDetailIds().stream().forEach(i -> {
            DetailInfoVo infoVo = this.multipleDetailInfo(i);
            detailVoIds.add(infoVo);
        });
        NewPkMultipleConfigAdminExtVo newPkMultipleConfigAdminExtVo = new NewPkMultipleConfigAdminExtVo();
        BeanUtils.copyProperties(newPkMultipleConfigVo, newPkMultipleConfigAdminExtVo);
        newPkMultipleConfigAdminExtVo.setDetailVoIds(detailVoIds);
        newPkMultipleConfigAdminExtVo.setMusicDetailsList(newPersonPkVo.getMusicDetailsList());
        return newPkMultipleConfigAdminExtVo;
    }

    /**
     * 跑步明细detail_id 明细信息
     *
     * @param detailId
     * @return
     */
    public DetailInfoVo multipleDetailInfo(Long detailId) {
        var znsUserRunDataDetailsEntity = userRunDataDetailsService.findById(detailId);
        if (Objects.isNull(znsUserRunDataDetailsEntity)) {
            throw new BaseException("detail_id 不存在！");
        }
        var detailInfoVo = new DetailInfoVo();
        detailInfoVo.setRunMileage(znsUserRunDataDetailsEntity.getRunMileage());
        detailInfoVo.setRunTime(znsUserRunDataDetailsEntity.getRunTime());
        detailInfoVo.setDetailId(detailId);
        detailInfoVo.setCompleteType(znsUserRunDataDetailsEntity.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0 ? 1 : 2);
        return detailInfoVo;
    }


    /**
     * 上报新人游戏成绩
     *
     * @param reportDto
     * @param loginUser
     */
    public void gradeReport(NewUserPropGradeReportDto reportDto, ZnsUserEntity loginUser) {
        // 查询对应活动
        ZnsRunActivityEntity runActivity = runActivityService.findById(reportDto.getActivityId());
        if (Objects.isNull(runActivity)) {
            log.info("gradeReport end,无对应活动");
            return;
        }
        ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.findRunActivityConfig(runActivity.getActivityConfigId());
        if (Objects.isNull(runActivityConfig)) {
            log.info("gradeReport end,无对应runActivityConfig");
            return;
        }
        NewPkMultipleConfigVo newPkMultipleConfigVo = JsonUtil.readValue(runActivityConfig.getActivityConfig(), NewPkMultipleConfigVo.class);
        if(Objects.isNull(newPkMultipleConfigVo)){
            log.info("gradeReport end,无对应runActivityConfig字段配置");
            return;
        }
        Integer newPersonActivityType = newPkMultipleConfigVo.getNewPersonActivityType();
        if (!ActivityConstants.NewPersonActivityTypeEnum.PROP.getType().equals(newPersonActivityType)) {
            log.info("gradeReport end，非新人道具活动");
            return;
        }
        Integer propTargetMileage = newPkMultipleConfigVo.getPropTargetMileage();

        //更新成绩
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(runActivity.getId(), loginUser.getId());
        if (Objects.isNull(activityUser)) {
            activityUser = createActivityUser(loginUser, runActivity.getId(), propTargetMileage);
            runActivityUserService.save(activityUser);
        }
        activityUser.setRunMileage(reportDto.getRunMileage());
        activityUser.setRunTime(reportDto.getRunTime()/1000);
        activityUser.setRunTimeMillisecond(reportDto.getRunTime());
        activityUser.setRank(reportDto.getRank());
        //完赛
        if (reportDto.getRunMileage().compareTo(new BigDecimal(propTargetMileage)) >= 0) {
            activityUser.setIsComplete(1);
            processAward(newPkMultipleConfigVo, activityUser, runActivity.getId());
        }
        runActivityUserService.updateById(activityUser);
    }

    private ZnsRunActivityUserEntity createActivityUser(ZnsUserEntity loginUser, Long activityId, Integer propTargetMileage) {
        ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
        activityUser.setActivityId(activityId);
        activityUser.setUserId(loginUser.getId());
        activityUser.setIsRobot(loginUser.getIsRobot());
        activityUser.setIsTest(loginUser.getIsTest());
        activityUser.setNickname(loginUser.getFirstName());
        activityUser.setActivityType(RunActivityTypeEnum.NEW_USER_PROP.getType());
        activityUser.setUserState(1);
        // 活动参与者
        activityUser.setInviterUserId(loginUser.getId());
        activityUser.setTargetRunMileage(propTargetMileage);
        activityUser.setTargetRunTime(null);
        return activityUser;
    }

    private void processAward(NewPkMultipleConfigVo newPkMultipleConfigVo, ZnsRunActivityUserEntity activityUser, Long activityId) {
        UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
        if(!newPkMultipleConfigVo.getIsFinishAward().equals(YesNoStatus.YES.getCode())){
            return;
        }
        if (!CollectionUtils.isEmpty(finishAward.getAmountList())) {
            CurrencyAmount currencyAmount = finishAward.getAmountList().stream().filter(x -> I18nConstant.CurrencyCodeEnum.USD.getCode().equals(x.getCurrencyCode())).findFirst().orElse(null);
            if (Objects.nonNull(currencyAmount) && Objects.nonNull(currencyAmount.getAmount()) && currencyAmount.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                activityUser.setRunAward(currencyAmount.getAmount());
                // 给用户余额发送奖励
                userAccountService.increaseAmount(currencyAmount.getAmount(), activityUser.getUserId(), true);
                // 新增用户奖励余额明细
                String billNo = NanoId.randomNanoId();;
                ZonedDateTime tradeTime = ZonedDateTime.now();
                userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.PK_RUN_BONUS_C,
                        null,1, currencyAmount.getAmount(), billNo, tradeTime,
                        activityUser.getActivityId(), activityUser.getActivityId(),null , activityUser.getActivityType(),
                        0L, "", null, null, null, BigDecimal.ZERO);
            }
        }
        if(Objects.nonNull(finishAward.getScore()) && finishAward.getScore() > 0){
            Integer score = finishAward.getScore();
            log.info("[gradeReport]---,活动={},发放新人pk积分={},userId={}",activityId,score,activityUser.getUserId());
            activityUserScoreService.increaseAmount(score, activityUser.getActivityId(), activityUser.getUserId(), 1, 0, ScoreConstant.SourceTypeEnum.source_type_24.getType());
        }
        if(Objects.nonNull(finishAward.getCouponAwardDto())){
            CouponAwardDto awardDto = finishAward.getCouponAwardDto();
            log.info("[gradeReport]---,活动={},发放新人pk优惠券={},userId={}",activityId,awardDto.getCouponId(),activityUser.getUserId());
            userCouponBizService.sendUserCouponSource(awardDto.getCouponId(), activityUser.getUserId(), activityUser.getActivityId(), CouponConstant.SourceTypeEnum.source_type_6.getType(), false);
        }
        if (!CollectionUtils.isEmpty(finishAward.getWears())) {
            for (WearAwardDto wear : finishAward.getWears()) {
                userWearsBagService.sendUserWear(activityUser.getUserId(), wear, activityUser.getActivityId());
            }
        }
    }
}
