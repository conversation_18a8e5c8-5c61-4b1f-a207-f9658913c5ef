package com.linzi.pitpat.data.activityservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomStatusEnum;
import com.linzi.pitpat.data.activityservice.dto.api.FreeActivityChallengeDto;
import com.linzi.pitpat.data.activityservice.dto.api.FreeActivityConfigDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeRoomDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeRoomRespDto;
import com.linzi.pitpat.data.activityservice.dto.console.FreeActivityDto;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.enums.FreeRoomStatusEnum;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityRoomUserDo;
import com.linzi.pitpat.data.activityservice.model.entity.FreeRoomDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.FreeActivityRoomUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.FreeRoomPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.FreeRoomQuery;
import com.linzi.pitpat.data.activityservice.service.FreeActivityRoomUserService;
import com.linzi.pitpat.data.activityservice.service.FreeRoomService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.userservice.dto.api.response.UserNameAndHeadResponseDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class AppFreeActivityBiz {

    private final FreeRoomService freeRoomService;
    private final FreeActivityRoomUserService freeActivityRoomUserService;
    private final ZnsUserService znsUserService;
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final FreeActivityManager freeActivityManager;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RedisTemplate redisTemplate;
    private final SeriesActivityRelService seriesActivityRelService;
    @Value("${admin.server.gamepush}")
    private String gameDomain;



    /**
     * 房间分页查询
     * 排序第一优先级：等待中 > 进行中
     * 第二优先级：创建时间（创建时间早的优先展示）
     * 不显示已结束的活动
     * 展示前3个加入房间的用户头像
     *
     * @param pageQuery 分页查询参数
     * @return 房间分页数据
     */
    public Page<FreeRoomDto> roomPage(PageQuery pageQuery) {
        // 1. 查询房间数据，排除已结束的房间（roomStatus != 2），并在SQL中排序
        FreeRoomPageQuery roomPageQuery = new FreeRoomPageQuery();
        roomPageQuery.setPageNum(pageQuery.getPageNum());
        roomPageQuery.setPageSize(pageQuery.getPageSize());
        roomPageQuery.setNeRoomStatus(2); // 排除已结束的房间
        roomPageQuery.setOrderType(1); // 启用SQL排序
        MainActivity currentFreeActivity = mainActivityService.getCurrentFreeActivity(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType());
        roomPageQuery.setActivityId(currentFreeActivity.getId());
        Page<FreeRoomDo> roomPage = freeRoomService.findPage(roomPageQuery);
        
        if (CollectionUtils.isEmpty(roomPage.getRecords())) {
            return new Page<>();
        }

        // 2. 获取房间编号列表
        List<Long> roomNumbers = roomPage.getRecords().stream()
                .map(FreeRoomDo::getRoomNumber)
                .collect(Collectors.toList());

        // 3. 查询房间用户数据
        List<FreeActivityRoomUserDo> roomUsers = findRoomUsersByRoomNumbers(roomNumbers);
        
        // 4. 获取用户ID列表并查询用户头像
        List<Long> userIds = roomUsers.stream()
                .map(FreeActivityRoomUserDo::getUserId)
                .distinct()
                .collect(Collectors.toList());
        
        Map<Long, ZnsUserEntity> userMap = CollectionUtils.isEmpty(userIds) ? 
                Map.of() : znsUserService.getMapByIds(userIds);

        // 5. 构建房间用户映射关系
        Map<Long, List<FreeActivityRoomUserDo>> roomUserMap = roomUsers.stream()
                .collect(Collectors.groupingBy(FreeActivityRoomUserDo::getRoomNum));

        // 6. 转换为FreeRoomDto（SQL已经排序，无需再次排序）
        List<FreeRoomDto> roomDtos = roomPage.getRecords().stream()
                .map(room -> convertToFreeRoomDto(room, roomUserMap.get(room.getRoomNumber()), userMap))
                .collect(Collectors.toList());

        // 7. 构建分页结果
        Page<FreeRoomDto> result = new Page<>();
        result.setRecords(roomDtos);
        result.setTotal(roomPage.getTotal());
        result.setSize(roomPage.getSize());
        result.setCurrent(roomPage.getCurrent());
        result.setPages(roomPage.getPages());

        return result;
    }

    /**
     * 根据房间编号查询房间用户数据
     */
    private List<FreeActivityRoomUserDo> findRoomUsersByRoomNumbers(List<Long> roomNumbers) {
        if (CollectionUtils.isEmpty(roomNumbers)) {
            return new ArrayList<>();
        }
        
        FreeActivityRoomUserQuery query = new FreeActivityRoomUserQuery();
        query.setRoomNums(roomNumbers);
        query.setStatus("in"); // 只查询在房间的用户
        
        return freeActivityRoomUserService.findList(query);
    }

    /**
     * 转换为FreeRoomDto
     */
    private FreeRoomDto convertToFreeRoomDto(FreeRoomDo room, List<FreeActivityRoomUserDo> roomUsers, Map<Long, ZnsUserEntity> userMap) {
        FreeRoomDto dto = new FreeRoomDto();
        dto.setRoomNumber(room.getRoomNumber());
        
        // 设置房间状态
        if (room.getRoomStatus() == 0) {
            dto.setStatus(FreeRoomStatusEnum.WAITING.getCode());
        } else if (room.getRoomStatus() == 1) {
            dto.setStatus(FreeRoomStatusEnum.IN_PROGRESS.getCode());
        }
        
        // 设置创建时间
        if (room.getGmtCreate() != null) {
            dto.setCreateTime(room.getGmtCreate().toInstant().atZone(ZoneId.systemDefault()));
        }

        if (!CollectionUtils.isEmpty(roomUsers)){
            // 设置当前房间人数
            dto.setCurrentNum(roomUsers.size());

            // 获取前3个用户头像
            List<UserNameAndHeadResponseDto> userAvatars = new ArrayList<>();
            userAvatars = roomUsers.stream()
                    .limit(3) // 只取前3个
                    .map(user -> {
                        ZnsUserEntity userEntity = userMap.get(user.getUserId());
                        if (Objects.nonNull(userEntity)) {
                            return new UserNameAndHeadResponseDto()
                                    .setHeadPortrait(userEntity.getHeadPortrait())
                                    .setUserId(userEntity.getId())
                                    .setNickname(userEntity.getFirstName());
                        } else {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            dto.setUserList(userAvatars);
        }

        
        return dto;
    }

    /**
     * 加入房间
     * 如果房间号为空，则随机返回一个等待中的房间号
     * 数据落到zns_run_activity_user表
     * 如果已有就不用加
     * 活动id从mainActivityService.getCurrentFreeActivity()取出
     *
     * @param
     * @return 房间号
     */
    public Long joinRoom(Long roomNumber,ZnsUserEntity user) {
        // 1. 获取当前活动
        MainActivity currentFreeActivity = mainActivityService.getCurrentFreeActivity(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType());
        if (currentFreeActivity == null) {
            log.warn("当前没有可用的活动");
          return null;
        }

        // 2. 确定要加入的房间号
        if (roomNumber == null || roomNumber == 0) {
            // 随机选择一个等待中的房间
            roomNumber = getRandomWaitingRoom(currentFreeActivity.getId());
            if (roomNumber == null) {
                log.warn("没有可用的等待中房间");
                return null;
            }
        }

        // 3. 创建用户活动记录
       createActivityUser(user, currentFreeActivity);

        log.info("用户{}成功加入房间{}，活动{}", user.getId(), roomNumber, currentFreeActivity.getId());
        return roomNumber;
    }



    /**
     * 随机获取一个等待中的房间
     */
    private Long getRandomWaitingRoom(Long activityId) {
        FreeRoomPageQuery query = new FreeRoomPageQuery();
        query.setPageNum(1);
        query.setPageSize(100); // 获取足够多的房间用于随机选择
        query.setActivityId(activityId);
        query.setNeRoomStatus(2); // 排除已结束的房间
        query.setOrderType(1); // 启用SQL排序

        Page<FreeRoomDo> roomPage = freeRoomService.findPage(query);
        if (CollectionUtils.isEmpty(roomPage.getRecords())) {
            return null;
        }

        // 过滤出等待中的房间
        List<FreeRoomDo> waitingRooms = roomPage.getRecords().stream()
                .filter(room -> room.getRoomStatus() == 0) // 0表示等待中
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(waitingRooms)) {
            return null;
        }

        // 随机选择一个等待中的房间
        Random random = new Random();
        int randomIndex = random.nextInt(waitingRooms.size());
        return waitingRooms.get(randomIndex).getRoomNumber();
    }



    /**
     * 创建用户活动记录
     */
    private void createActivityUser(ZnsUserEntity user, MainActivity activity) {

        ZnsRunActivityUserEntity existingUsers = znsRunActivityUserService.findActivityUser(activity.getId(), user.getId());
        if (existingUsers != null) {
            log.info("用户{}已经加入活动{}", user.getId(), activity.getId());
            return;
        }
        ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
        FreeActivityConfigDto freeActivityConfig = getFreeActivityConfig();
        activityUser.setTargetRunMileage(freeActivityConfig.getTargetMileage());
        activityUser.setActivityId(activity.getId());
        activityUser.setUserId(user.getId());
        //activityUser.setTargetRunMileage(activity.getRunMileage().intValue());
        activityUser.setNickname(user.getFirstName());
        activityUser.setUserType(2); // 2表示活动参与者
        activityUser.setUserState(1); // 1表示已接受
        //activityUser.setActivityType(activity.getActivityType());
        activityUser.setIsDelete(0);
        activityUser.setIsComplete(0); // 0表示未完成
        activityUser.setIsRobot(0); // 0表示非机器人
        activityUser.setIsTest(0); // 0表示非测试用户
        activityUser.setIsCheat(0); // 0表示非作弊
        znsRunActivityUserService.save(activityUser);

        if (MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(activity.getMainType())){
            List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(activity.getId());
            for (MainActivity mainActivity : allMainActivity) {
                createActivityUser(user,mainActivity);

            }
        }

    }


    public FreeActivityConfigDto getFreeActivityConfig() {
        MainActivity currentFreeActivity = mainActivityService.getCurrentFreeActivity(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType());
        FreeActivityDto activityDto = freeActivityManager.getByActivityId(currentFreeActivity.getId(),false);
        FreeActivityConfigDto configDto = new FreeActivityConfigDto();
        if (activityDto != null) {

            configDto.setActivityId(activityDto.getId());
            configDto.setMode(activityDto.getMode());
            if (activityDto.getRoute() != null){
                configDto.setRouteId(activityDto.getRoute().getRouteId());
                configDto.setPic(activityDto.getRoute().getRouteCoverPic());
            }
            configDto.setTargetMileage(activityDto.getTargetMileage());
            if (activityDto.getRunTemplateDo() != null){
                FreeActivityChallengeDto freeActivityChallengeDto = new FreeActivityChallengeDto();
                freeActivityChallengeDto.setName(activityDto.getRunTemplateDo().getName());
                freeActivityChallengeDto.setDetailId(activityDto.getRunTemplateDo().getDetailId());
                ZnsUserRunDataDetailsEntity userRunDataDetailsEntity = userRunDataDetailsService.findById(activityDto.getRunTemplateDo().getDetailId());
                if (userRunDataDetailsEntity != null){
                    freeActivityChallengeDto.setGrade(userRunDataDetailsEntity.getRunTimeMillisecond());

                }
                configDto.setChallengeDto(freeActivityChallengeDto);
            }

        }
        return configDto;
    }

    public Long createRoom(ZnsUserEntity loginUser,Integer appVersion) {
        MainActivity currentFreeActivity = mainActivityService.getCurrentFreeActivity(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType());
        Long gennedRoomNumber = genRoomNumber();
        freeRoomService.createRoom(currentFreeActivity.getId(), loginUser.getId(), gennedRoomNumber);
        FreeActivityConfigDto freeActivityConfig = getFreeActivityConfig();
        createActivityUser(loginUser, currentFreeActivity);
        GamePushUtils.addRoomWithMaster(gameDomain, gennedRoomNumber, FreeActivityModeEnum.PROP.getCode().equals(freeActivityConfig.getMode()) ? 7 : 2, null, loginUser.getId(),appVersion);
        return gennedRoomNumber;
    }

    private Long genRoomNumber() {
        String key = RedisConstants.APP_ROOM_NUMBER;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            redisTemplate.opsForValue().increment(key);
        } else {
            redisTemplate.opsForValue().set(key, "100001");
        }
        return Long.parseLong(Objects.requireNonNull(redisTemplate.opsForValue().get(key)).toString());
    }

    /**
     * 自由挑战跑活动 房间开赛
     * @param roomNumber
     * @param activityId
     */
    public void roomGameStart(Long roomNumber,Long activityId){

        //只处理自由挑战跑活动数据, 非自由挑战跑活动不处理
        MainActivity activity = mainActivityService.findCacheById(activityId);
        if (Objects.nonNull(activity) && MainActivityTypeEnum.isFreeChallengeActivity(activity.getMainType())) {
            //获取房间信息
            FreeRoomDo freeRoomDo = freeRoomService.findByQuery(new FreeRoomQuery().setRoomNumber(roomNumber).setActivityId(activityId));
            if (Objects.nonNull(freeRoomDo) && RoomStatusEnum.NOT_STARTED.getCode().equals(freeRoomDo.getRoomStatus())) {
                log.warn("房间上报开始活动，房间ID:{},活动ID:{}", roomNumber, activityId);
                freeRoomDo.setRoomStatus(RoomStatusEnum.IN_PROGRESS.getCode());
                freeRoomService.update(freeRoomDo);
            }
        }
    }

    public FreeRoomRespDto roomDetail(Long roomNumber) {
        FreeRoomDo freeRoomDo = freeRoomService.findByQuery(new FreeRoomQuery().setRoomNumber(roomNumber));
        if (Objects.isNull(freeRoomDo)) {
            return null;
        }
        FreeRoomRespDto respDto = new FreeRoomRespDto();
        respDto.setRoomStatus(freeRoomDo.getRoomStatus());
        respDto.setRoomNumber(roomNumber);
        return respDto;
    }
}
