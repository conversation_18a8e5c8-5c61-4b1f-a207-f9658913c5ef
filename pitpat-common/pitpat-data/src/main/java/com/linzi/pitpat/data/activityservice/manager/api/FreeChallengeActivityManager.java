package com.linzi.pitpat.data.activityservice.manager.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.converter.api.RunActivityUserConverter;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeChallengeActivityResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeChallengeActivityTopListResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.TopPersonalPlayerResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.TopPlayerResponse;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.MainActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityPageQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.systemservice.enums.PopRecordConstant;
import com.linzi.pitpat.data.systemservice.model.entity.PopRecord;
import com.linzi.pitpat.data.systemservice.model.query.PopRecordQuery;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.userservice.dto.api.response.LimitedEditionAwardResponse;
import com.linzi.pitpat.data.userservice.dto.api.response.traffic.investment.TrafficInvestmentAwardResponse;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FreeChallengeActivityManager {
    private final MainActivityService mainActivityService;
    private final MainActivityBizService mainActivityBizService;
    private final ActivityParamsService activityParamsService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final PropRunRankedActivityUserService propRunRankedActivityUserService;
    private final RunActivityUserConverter runActivityUserConverter;
    private final PopRecordService popRecordService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final MedalConfigService medalConfigService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final WearsService wearsService;
    private final ISysConfigService sysConfigService;
    public FreeChallengeActivityTopListResponse topListLocal(String deviceLocation, Page page) {
        String mainType = MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType();
        if ("LA".equals(deviceLocation)) {
            mainType = MainActivityTypeEnum.FREE_CHALLENGE_SUB.getType();
        }
        MainActivity activity = mainActivityService.getCurrentFreeActivity(mainType);
        if (Objects.isNull(activity)) {
            return null;
        }
        return topList(activity.getId(), 1, null, page);
    }

    public FreeChallengeActivityTopListResponse topList(Long activityId, Integer isRefresh, Long userId, Page page) {
        // 1、获取活动信息
        MainActivity activity = mainActivityService.findById(activityId);
        if (activity == null) {
            log.info("活动不存在: " + activityId);
            return new FreeChallengeActivityTopListResponse();
        }
        // 2. 判断时间：结束时间+2小时
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime rankingEndTime = DateUtil.addHours(DateTimeUtil.parse(activity.getActivityEndTime()), 2);
        Boolean isChangeActivity = false;
        if (now.isAfter(rankingEndTime) && YesNoStatus.YES.getCode().equals(isRefresh)) {
            //3、获取下一场
            MainActivity nextActivity = mainActivityService.findOne(MainActivityQuery.builder().mainType(activity.getMainType()).idGe(activity.getId()).build());
            if (nextActivity == null) {
                //下场不存在，返回空
                return new FreeChallengeActivityTopListResponse();
            }
            log.info("获取下一场：{}", activityId);
            activity = nextActivity;
            activityId = nextActivity.getId();
            rankingEndTime = DateUtil.addHours(DateTimeUtil.parse(nextActivity.getActivityEndTime()), 2);
            page.setCurrent(1);
            isChangeActivity = true;
        }
        FreeActivityConfig freeActivityConfig = activityParamsService.findCacheOne(activityId, ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
        // 3. 获取封装List<FreeChallengeActivityResponse>
        List<FreeChallengeActivityResponse> activityResponses = mainActivityBizService.findFreeChallengeActivityList(activity);

        // 4. 获取分页的TopPlayerResponse
        Page<TopPlayerResponse> topPlayersPage = getTopPlayers(activity, page, freeActivityConfig);

        // 5. 获取个人数据
        TopPersonalPlayerResponse personalData = getPersonalPlayerData(activityId, userId, freeActivityConfig);

        // 6. 构建奖励弹窗
        List<LimitedEditionAwardResponse> awardList = getPersonAwardList(activityId, userId, personalData);
        // 构建返回对象
        return new FreeChallengeActivityTopListResponse()
                .setNextRankingTime(rankingEndTime)
                .setActivityList(activityResponses)
                .setTopList(topPlayersPage)
                .setPersonalData(personalData)
                .setIsChangeActivity(isChangeActivity)
                .setAwardList(awardList);
    }

    private List<LimitedEditionAwardResponse> getPersonAwardList(Long activityId, Long userId, TopPersonalPlayerResponse personalData) {
        // 1、检查是否获得奖励
        if (Objects.isNull(userId) || Objects.isNull(personalData) || !personalData.getSendRankAwardStatus()) {
            return null;
        }
        // 2、查询弹窗记录
        PopRecordQuery popRecordQuery = PopRecordQuery.builder()
                .userId(userId).activityId(activityId)
                .type(PopRecordConstant.PopRecordTypeEnum.FREE_CHALLENGE_ACTIVITY_AWARD_POP.getType()).build();
        PopRecord popRecord = popRecordService.findByQuery(popRecordQuery);
        if (Objects.nonNull(popRecord)) {
            return null;
        }
        // 添加弹窗记录
        PopRecord popRecordInsert = new PopRecord().setActivityId(activityId).setUserId(userId)
                .setType(PopRecordConstant.PopRecordTypeEnum.FREE_CHALLENGE_ACTIVITY_AWARD_POP.getType());
        popRecordService.insertPopRecord(popRecordInsert);

        String awardJson = sysConfigService.selectConfigByKey("limited.edition.award", true);
        List<TrafficInvestmentAwardResponse> baseInfo = JsonUtil.readList(awardJson, TrafficInvestmentAwardResponse.class);

        // 3、获取奖励
        List<AwardConfigDto> awardConfigDtoList = activityAwardConfigService.selectCacheAwardConfigDtoListBySendTypes(activityId, Collections.singletonList(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType()), null);
        List<AwardConfigDto> dtoList = awardConfigDtoList.stream().filter(i -> personalData.getRank() >= i.getRank() && personalData.getRank() <= i.getRankMax()).collect(Collectors.toList());
        return dtoList.stream().map(d -> {
            LimitedEditionAwardResponse limitedEditionAwardResponse = new LimitedEditionAwardResponse();

            if (AwardTypeEnum.MEDAL.getType().equals(d.getAwardType())) {
                MedalConfig medalConfig = medalConfigService.selectMedalConfigById(d.getMedalId());
                if (Objects.nonNull(medalConfig)) {
                    d.setMedalName(medalConfig.getName());
                    d.setMedalImageUrl(medalConfig.getUrl());
                }
            } else if (AwardTypeEnum.WEAR.getType().equals(d.getAwardType())) {
                Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(d.getWearType()), d.getWearValue());
                if (Objects.nonNull(wear)) {
                    d.setWearName(wear.getWearName());
                    d.setWearImageUrl(wear.getWearImageUrl());
                }
            }

            baseInfo.stream().filter(a -> Objects.equals(a.getType().getType(), d.getAwardType())).findFirst().ifPresent(a -> {
                String titleKey = "limited.edition.award." + a.getType().getType() + ".title";
                if (AwardTypeEnum.SCORE.getType().equals(d.getAwardType())) {
                    limitedEditionAwardResponse.setTitle(I18nMsgUtils.getMessage(titleKey, d.getScore() != null ? d.getScore().toString() : ""));
                } else if (AwardTypeEnum.AMOUNT.getType().equals(d.getAwardType())) {
                    limitedEditionAwardResponse.setTitle(I18nMsgUtils.getMessage(titleKey, d.getAmount() != null ? d.getAmount().toString() : ""));
                }
                limitedEditionAwardResponse.setImageUrl(a.getImageUrl());
            });

            limitedEditionAwardResponse.setFreeChallengeAward(d);
            return limitedEditionAwardResponse;
        }).toList();

    }

    private TopPersonalPlayerResponse getPersonalPlayerData(Long activityId, Long userId, FreeActivityConfig freeActivityConfig) {
        TopPersonalPlayerResponse response = new TopPersonalPlayerResponse().setUserId(userId).setOnLeaderboard(0);
        if (Objects.isNull(userId)) {
            return response;
        }
        Integer runTimeMillisecond = null;
        //竞速模式
        if (FreeActivityModeEnum.COMPETE.getCode().equals(freeActivityConfig.getMode())) {
            // 获得个人的排名
            Query query = new Query();
            query.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_COMPETE);
            Integer currentRank = runActivityUserService.findCurrentRank(activityId, userId, PageHelper.ofOrderSql(query));
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userId);
            if (Objects.nonNull(activityUser)) {
                response.setRunTime(activityUser.getRunTime())
                        .setRank(activityUser.getRank()>0?activityUser.getRank():currentRank)
                        .setOnLeaderboard(activityUser.getRank()>=0?1:0)
                        .setUserId(activityUser.getUserId())
                        .setSendRankAwardStatus(activityUser.getRank()>0);
                runTimeMillisecond = activityUser.getRunTimeMillisecond();
            }

        } else {
            Query query = new Query();
            query.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_PROP);
            Integer currentRank = propRunRankedActivityUserService.findCurrentRank(activityId, userId, PageHelper.ofOrderSql(query));
            PropRunRankedActivityUser activityUser = propRunRankedActivityUserService.findByActivityIdAndUserId(activityId, userId);
            if (Objects.nonNull(activityUser)) {
                response.setRunTime(activityUser.getRunTime())
                        .setRank(activityUser.getRank()>0?activityUser.getRank():currentRank)
                        .setOnLeaderboard(activityUser.getIsComplete())
                        .setUserId(activityUser.getUserId())
                        .setSendRankAwardStatus(activityUser.getRank()>0);
                runTimeMillisecond = activityUser.getRunTimeMils();
            }
        }
        //文案填充
        if (Objects.isNull(response.getRunTime()) || response.getRunTime() == 0) {
            response.setDisplayText(I18nMsgUtils.getMessage("free.challenge.activity.ranking.desc.no"));
        } else if (response.getOnLeaderboard() == 0) {
            // 获取榜单标的信息
            if (FreeActivityModeEnum.COMPETE.getCode().equals(freeActivityConfig.getMode())) {
                ZnsUserRunDataDetailsEntity dataDetails = userRunDataDetailsService.findById(response.getRunDataDetailsId());
                if (Objects.nonNull(dataDetails)) {
                    int sub = Math.max(response.getRunTime() - dataDetails.getRunTime(), 0);
                    response.setDisplayText(I18nMsgUtils.getMessage("free.challenge.activity.ranking.desc.noTop", sub));
                }
            }
        } else if (!Integer.valueOf(1).equals(response.getRank())){
            //获取前一名数据
            //竞速模式
            int i = 0;
            if (FreeActivityModeEnum.COMPETE.getCode().equals(freeActivityConfig.getMode())) {
                RunActivityUserQuery query = RunActivityUserQuery.builder().activityId(activityId).minRank(0).isComplete(1).runTimeMillisecondLt(runTimeMillisecond).build();
                query.setOrders(List.of(OrderItem.desc("run_time_millisecond")));
                ZnsRunActivityUserEntity lastUser = runActivityUserService.findByQuery(query);
                if (lastUser != null) {
                    i = Math.max(response.getRunTime() - lastUser.getRunTime(), 0);
                }
            } else {
                PropRankedActivityPageQuery query = PropRankedActivityPageQuery.builder().activityId(activityId).minRank(0).isComplete(true).runTimeMillisecondLt(runTimeMillisecond).build();
                query.setOrders(List.of(OrderItem.desc("run_time_mils")));
                PropRunRankedActivityUser lastUser = propRunRankedActivityUserService.findByQuery(query);
                if (lastUser != null) {
                    i = Math.max(response.getRunTime() - lastUser.getRunTime(), 0);
                }
            }

            response.setDisplayText(I18nMsgUtils.getMessage("free.challenge.activity.ranking.desc.noFirst", i));
        }
        return response;
    }


    private Page<TopPlayerResponse> getTopPlayers(MainActivity activity, Page page, FreeActivityConfig freeActivityConfig) {
        Long activityId = activity.getId();
        String deviceLocation = MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(activity.getMainType()) ? "ALL" : "LA";
        Page<TopPlayerResponse> responsePage = new Page<>(page.getCurrent(), page.getSize());
        int offset = Math.toIntExact(page.getSize() * (page.getCurrent()-1));
        List<TopPlayerResponse> list = new ArrayList<>();
        if (FreeActivityModeEnum.COMPETE.getCode().equals(freeActivityConfig.getMode())) {
            RunActivityUserPageQuery pageQuery = RunActivityUserPageQuery.builder().activityId(activityId).isComplete(1).minRank(0).build();
            pageQuery.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_COMPETE);
            pageQuery.setPageNum((int) page.getCurrent());
            pageQuery.setPageSize((int) page.getSize());
            Page<ZnsRunActivityUserEntity> userPage = runActivityUserService.findPage(pageQuery);
            List<ZnsRunActivityUserEntity> records = userPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return responsePage;
            }
            for (int i = 0; i < records.size(); i++) {
                TopPlayerResponse topPlayerDto = runActivityUserConverter.toTopPlayerDto(records.get(i));
                topPlayerDto.setRank(i + 1 + offset);
                topPlayerDto.setDeviceLocation(deviceLocation);
                list.add(topPlayerDto);
            }
            responsePage.setRecords(list).setTotal(userPage.getTotal());
        } else {
            PropRankedActivityPageQuery pageQuery = PropRankedActivityPageQuery.builder().activityId(activityId).isComplete(true).build();
            pageQuery.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_PROP);
            Page<PropRunRankedActivityUser> userPage = propRunRankedActivityUserService.findPageByQuery(pageQuery);
            List<PropRunRankedActivityUser> records = userPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return responsePage;
            }
            for (int i = 0; i < records.size(); i++) {
                TopPlayerResponse topPlayerDto = runActivityUserConverter.toTopPlayerDto(records.get(i));
                topPlayerDto.setRank(i + 1 + offset);
                topPlayerDto.setDeviceLocation(deviceLocation);
                list.add(topPlayerDto);
            }
            responsePage.setRecords(list).setTotal(userPage.getTotal());
        }
        return responsePage;

    }


}
