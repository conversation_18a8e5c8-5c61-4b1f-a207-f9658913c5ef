package com.linzi.pitpat.data.awardservice.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/21 11:28
 */
@Data
@NoArgsConstructor
public class LastActivityUserDto {
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动状态：0表示未开始，1 表示进行中，2表示已结束，-1表示活动已取消
     */
    private Integer activityState;
    /**
     * 跑步活动id
     */
    private Long activityId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 活动跑步排名：越小越靠前
     */
    private Integer rank;
    /**
     * 是否完成比赛:0表示未完成，1表示完成
     */
    private Integer isComplete;
}
