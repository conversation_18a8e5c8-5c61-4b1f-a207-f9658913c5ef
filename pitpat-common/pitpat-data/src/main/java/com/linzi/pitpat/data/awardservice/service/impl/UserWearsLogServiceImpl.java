package com.linzi.pitpat.data.awardservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linzi.pitpat.data.awardservice.mapper.UserWearsLogMapper;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsLog;
import com.linzi.pitpat.data.awardservice.service.UserWearsLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 用户穿戴表 服务类
 *
 * @since 2024-03-05
 */
@Service
public class UserWearsLogServiceImpl implements UserWearsLogService {

    @Autowired
    private UserWearsLogMapper userWearsLogMapper;

    @Override
    public UserWearsLog findById(Long id) {
        return userWearsLogMapper.selectById(id);
    }

    @Override
    public int insert(UserWearsLog userWearsLog) {
        userWearsLog.setCreateTime(ZonedDateTime.now());
        userWearsLog.setModifieTime(ZonedDateTime.now());
        return userWearsLogMapper.insert(userWearsLog);
    }

    @Override
    public int update(UserWearsLog userWearsLog) {
        return userWearsLogMapper.updateById(userWearsLog);
    }

    @Override
    public int deleteById(Long id) {
        return userWearsLogMapper.deleteById(id);
    }

    @Override
    public UserWearsLog findByUserId(Long userId) {
        return userWearsLogMapper.selectOne(new QueryWrapper<UserWearsLog>().lambda()
                .eq(UserWearsLog::getUserId, userId).eq(UserWearsLog::getIsDelete, 0)
                .orderByDesc(UserWearsLog::getId).last("limit 1"));
    }

    @Override
    public List<UserWearsLog> findListByUserId(Long userId) {
        return userWearsLogMapper.selectList(new QueryWrapper<UserWearsLog>().lambda()
                .eq(UserWearsLog::getUserId, userId).eq(UserWearsLog::getIsDelete, 0)
                .orderByDesc(UserWearsLog::getId).last("limit 2"));
    }

}
