package com.linzi.pitpat.data.activityservice.strategy.rundata;

import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.data.activityservice.biz.DeskBikeRunDataBizService;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsMileageService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.RunningData;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.vo.runData.DataProcessingVo;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DeskBikeRunDataHandleStrategy implements EquipmentRunDataHandleStrategy, InitializingBean {

    @Resource
    private DeskBikeRunDataBizService deskBikeRunDataBizService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private ZnsUserRunDataDetailsMileageService userRunDataDetailsMileageService;
    @Resource
    private UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Value("${zns.config.rabbitQueue.run}")
    private String run;
    @Value("${zns.config.rabbitQueue.dataEnd}")
    private String dataEnd;
    @Value("${" + RabbitQueueConstants.RUN_DATA_SYNC_QUEUE + "}")
    private String dataSync;
    @Resource
    private ZnsTreadmillService treadmillService;
    @Resource
    private EquipmentConfigService equipmentConfigService;
    @Resource
    private UserTaskBizService userTaskBizService;

    @Override
    public void afterPropertiesSet() throws Exception {
        EquipmentRunDataHandleStrategyFactory.register(EquipmentDeviceTypeEnum.BICYCLE.getCode(), this);
    }

    @Override
    public Result<DataProcessingVo> primaryRunDataProcessing(RunDataRequest runDataRequest) {
        return deskBikeRunDataBizService.dataProcessing(runDataRequest);
    }

    @Override
    public void userRunDataHandle(RunDataRequest runData) {
        ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.findById(runData.getRunDataDetailsId());
        ActivityTypeDto activityTypeDto = runActivityService.getActivityNew(runData.getActivityId());
        if (Objects.nonNull(activityTypeDto)) {
            runData.setMainType(activityTypeDto.getMainType());
        }

        List<ZnsUserRunDataDetailsSecondEntity> data = runData.getDeskBikeData();

        List<ZnsUserRunDataDetailsSecondEntity> list = dealRunDataDetailsSecondList(data, userRunDataDetail, runData.getFirmwareVersion(), runData.getRun_status());
        // 如果正在跑步中，则不向后走了，run_status :  运动状态 0:运动中，1：结束，2：暂停，3：开始跑步
        if (CollectionUtils.isEmpty(data) && (runData.getRun_status() == 0 || runData.getRun_status() == 3)) {
            userRunDataDetailsService.update(userRunDataDetail);
            return;
        }
        // 保存每次上传的数据,存储到 zns_user_run_data_details_minute中
        deskBikeRunDataBizService.saveUserRunDataDetailMinute(userRunDataDetail.getId(), list, userRunDataDetail.getUserId(), runData.getRun_status(), activityTypeDto);
        List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(userRunDataDetail.getId());

        // 保存detail数据
        deskBikeRunDataBizService.saveUserRunDataDetail(userRunDataDetail, runData, activityTypeDto, detailsSecondEntities);
        // 保存每公里/英里的数据
        userRunDataDetailsMileageService.saveUserRunDataDetailMileage(userRunDataDetail.getId(), list, runData.getRun_status(), userRunDataDetail);
        // 防作弊处理  单车无作弊
//        runCheatManager.preventionCheatDeal(userRunDataDetail, activityTypeDto, detailsSecondEntities);

//        applicationEventPublisher.publishEvent(new RunningEvent(this, runData,userRunDataDetail,activityTypeDto, MDC.get("spanId")));

        if (runData.getRun_status() == 1) {
            log.info("userRunDataHandle脚踏车骑行结束处理 ：" + JsonUtil.writeString(runData));
            runData.setRunDataDetailsId(userRunDataDetail.getId());
            rabbitTemplate.convertAndSend(dataEnd, JsonUtil.writeString(runData));
            userTaskBizService.completeEvent(new EventTriggerDto().setUserId(userRunDataDetail.getUserId()).setEventType(TaskConstant.TakEventTypeEnum.RUN.getCode()));
        } else {
            RunningData runningData = new RunningData(userRunDataDetail.getUserId(), userRunDataDetail.getActivityId(), 0, userRunDataDetail.getId(), "");
            log.info("userRunDataHandle脚踏车骑行中处理 ：" + JsonUtil.writeString(runningData));
            rabbitTemplate.convertAndSend(run, JsonUtil.writeString(runningData));
        }
        if (!CollectionUtils.isEmpty(list) && EnvUtils.isReallyOnline(SpringContextUtils.getActiveProfile())) {
            rabbitTemplate.convertAndSend(dataSync, JsonUtil.writeString(list));
        }
    }

    @Override
    public void userRunDataHandle(Long userId, ZnsUserEntity user) {
        deskBikeRunDataBizService.userRunDataHandle(userId, user);
    }

    /**
     * 处理秒级详细数据
     *
     * @param data
     * @param userRunDataDetail
     * @param firmwareVersion
     * @param run_status
     * @return
     */
    private List<ZnsUserRunDataDetailsSecondEntity> dealRunDataDetailsSecondList(List<ZnsUserRunDataDetailsSecondEntity> data, ZnsUserRunDataDetailsEntity userRunDataDetail, Integer firmwareVersion, Integer run_status) {
        userRunDataDetailsSecondService.dataCompensation(data, run_status, userRunDataDetail, true);

        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
//        Integer treadmillRunType = getTreadmillRunType(userRunDataDetail.getTreadmillId());

        BigDecimal lastRunMileage = userRunDataDetail.getRunMileage();
        BigDecimal lastKilocalorie = userRunDataDetail.getKilocalorie();
        Integer maxHeartRate = userRunDataDetail.getMaxHeartRate();
        Integer maxPace = userRunDataDetail.getMaxPace();
        Integer runTime = userRunDataDetail.getRunTime();
        Integer totalTime = userRunDataDetail.getTotalTime();
        Integer rotateNum = userRunDataDetail.getRotateNum();
        Integer runTimeMillisecond = userRunDataDetail.getRunTimeMillisecond();

        List<ZnsUserRunDataDetailsSecondEntity> list = new ArrayList<>();

        //固件版本号判断，新版本将传毫秒
        Boolean isNewVersion = false;
        if (Objects.nonNull(firmwareVersion) && firmwareVersion > 19) {
            isNewVersion = true;
        }


        List range = redisTemplate.opsForList().range(RedisConstants.RUN_DATA_DETAIL_SECOND_LIST_KEY + userRunDataDetail.getId(), 0, -1);
        List<Integer> runTimeList = new ArrayList<>();
        Integer heartRateDeviceType = null;

        for (int i = 0; i < data.size(); i++) {
            ZnsUserRunDataDetailsSecondEntity entity = data.get(i);
            Integer runTimeMillisecond1 = entity.getRunTime();
            // RunTime 这个值，以前传的是秒，后面传的是毫秒
            if (isNewVersion) {
                entity.setRunTime(runTimeMillisecond1 / 1000);
            } else {
                runTimeMillisecond1 = runTimeMillisecond1 * 1000 + 999;
            }
            entity.setRunTimeMillisecond(runTimeMillisecond1);
            log.info("deskBike dealRunDataDetailsSecondList mil=" + entity.getMileage() + ",time=" + entity.getRunTime() + ",isNewVersion=" + isNewVersion);
            if (runTimeList.contains(entity.getRunTime())) {
                continue;
            } else {
                runTimeList.add(entity.getRunTime());
            }
//            Boolean res = redisTemplate.opsForValue().getBit(RedisConstants.RUN_DATA_DETAIL_SECOND_KEY + userRunDataDetail.getId(), entity.getRunTime());
            // 之前将 zns_user_run_data_details_second 中的每条数据从redis取，然后进行比对 。 后面发现这种效率有点低。
            // 因此，将一个 zns_user_run_data_details_second List 集合存储到 redis 中，如果redis 中已经存在数据，说明数据重复，不做处理
            if (!CollectionUtils.isEmpty(range) && range.contains(entity.getRunTime().toString()) && i != data.size() - 1) {
                log.info("dealRunDataDetailsSecondList 数据重复 mil=" + entity.getMileage() + ",time=" + entity.getRunTime());
                continue;
            }
            //数据异常不处理，小于2秒里程大于100
            if (entity.getRunTime() <= 2 && entity.getMileage().compareTo(new BigDecimal(100)) > 0) {
                log.info("dealRunDataDetailsSecondList 数据异常不处理，小于2秒里程大于100, mil=" + entity.getMileage() + ",time=" + entity.getRunTime());
                continue;
            }
            //时长跑跑步数据超过目标时间
            if (userRunDataDetail.getIsRobot() == 1 && userRunDataDetail.getTimeTarget() > 0 && userRunDataDetail.getRunTime() > userRunDataDetail.getTimeTarget()) {
                log.info("dealRunDataDetailsSecondList 数据超过目标时间，目标时间=" + userRunDataDetail.getTimeTarget() + ",mil=" + entity.getMileage() + ",time=" + entity.getRunTime());
                continue;
            }

            // 前端传过来的速度 Velocity 为米每秒
            // 此时计算得到的速度为 千米 每秒
            entity.setVelocity(BigDecimalUtil.divHalfDown(entity.getVelocity(), new BigDecimal(1000), 2));
            Integer pace = SportsDataUnit.velocityToPace(entity.getVelocity());
            // 计算配速 配速(时间/公里)
            entity.setPace(pace);

            // 计算最大配速 (时间/公里)
            if (pace > 0 && (pace.compareTo(maxPace) < 0 || maxPace == 0)) {
                maxPace = pace;
            }
            // 心率计算
            Integer heartRate = entity.getHeartRate();
            entity.setHeartRate(heartRate);
            if (Objects.nonNull(heartRate)) {
                if (maxHeartRate.compareTo(heartRate) < 0 || maxHeartRate == 0) {
                    maxHeartRate = heartRate;
                }
            } else {
                entity.setHeartRate(0);
            }

            if (Objects.nonNull(entity.getHeartRateDeviceType())) {
                heartRateDeviceType = entity.getHeartRateDeviceType();
            }

            lastRunMileage = entity.getMileage().compareTo(lastRunMileage) > 0 ? entity.getMileage() : lastRunMileage;
            lastKilocalorie = entity.getCalories();//卡路里
            runTime = entity.getRunTime();//运动时长
            totalTime = entity.getTotalTime();
            rotateNum = entity.getRotateNum();
            runTimeMillisecond = runTimeMillisecond1;
            entity.setRunDataDetailsId(userRunDataDetail.getId());
//            if (Objects.nonNull(entity.getRunType()) && entity.getRunType() > 2 && Objects.nonNull(treadmillRunType)) {
//                entity.setRunType(treadmillRunType);
//            }
            list.add(entity);
        }
        //设置redis标记, list 已经保存过的毫秒值
        if (!CollectionUtils.isEmpty(list)) {
            List<String> runTimes = list.stream().map(d -> d.getRunTime().toString()).collect(Collectors.toList());
            redisTemplate.opsForList().rightPushAll(RedisConstants.RUN_DATA_DETAIL_SECOND_LIST_KEY + userRunDataDetail.getId(), runTimes);
            redisTemplate.expire(RedisConstants.RUN_DATA_DETAIL_SECOND_LIST_KEY + userRunDataDetail.getId(), 2L, TimeUnit.HOURS);
        }

        userRunDataDetail.setMaxHeartRate(maxHeartRate);            // 最大心率(次/分钟)
        userRunDataDetail.setMaxPace(maxPace);                      // 最大配速(秒/公里)
        if (Objects.isNull(userRunDataDetail.getKilocalorie())) {   // 千卡路里
            userRunDataDetail.setKilocalorie(BigDecimal.ZERO);
        }
        if (Objects.isNull(lastKilocalorie)) {
            lastKilocalorie = BigDecimal.ZERO;
        }
        if (userRunDataDetail.getKilocalorie().compareTo(lastKilocalorie) < 0) {
            userRunDataDetail.setKilocalorie(lastKilocalorie);
        }
        if (userRunDataDetail.getRunTime() < runTime) {         //运动时间
            userRunDataDetail.setRunTime(runTime);
        }
        if (userRunDataDetail.getTotalTime() < totalTime) {
            userRunDataDetail.setTotalTime(totalTime);
        }
        if (userRunDataDetail.getRotateNum() < rotateNum) {
            userRunDataDetail.setRotateNum(rotateNum);
        }
        if (userRunDataDetail.getRunTimeMillisecond() < runTimeMillisecond) {
            userRunDataDetail.setRunTimeMillisecondLargeThenOneSecond(runTimeMillisecond);                   // 跑步毫秒值
        }
        if (userRunDataDetail.getRunMileage().compareTo(lastRunMileage) < 0) {                      // 跑就距离
            userRunDataDetail.setRunMileage(lastRunMileage.setScale(2, RoundingMode.HALF_DOWN));
        }
        if (Objects.nonNull(heartRateDeviceType)) {
            userRunDataDetail.setHeartRateDeviceType(heartRateDeviceType);
        }
        return list;
    }

    /**
     * 获取设备跑步类型，只返回跑步机和走步机的
     * @param treadmillId
     * @return
     */
//    private Integer getTreadmillRunType(Long treadmillId) {
//        ZnsTreadmillEntity treadmillEntity = treadmillService.findById(treadmillId);
//        if (Objects.isNull(treadmillEntity) || StringUtil.isEmpty(treadmillEntity.getProductCode())) {
//            return null;
//        }
//
//        EquipmentConfig equipmentConfig = equipmentConfigService.selectEquipmentConfigByTypeEquipmentInfo(2, treadmillEntity.getProductCode());
//        if (Objects.isNull(equipmentConfig)) {
//            return null;
//        }
//
//        if (equipmentConfig.getEquipmentType() == 1 || equipmentConfig.getEquipmentType() == 2) {
//            return equipmentConfig.getEquipmentType();
//        }
//        return null;
//    }


}
