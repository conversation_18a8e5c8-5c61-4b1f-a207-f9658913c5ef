package com.linzi.pitpat.data.activityservice.biz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.award.ActivityUserAwardCalculator;
import com.linzi.pitpat.data.activityservice.biz.dynamic.SpringElEval;
import com.linzi.pitpat.data.activityservice.biz.dynamic.SpringElRunResult;
import com.linzi.pitpat.data.activityservice.biz.dynamic.SpringElRunnerService;
import com.linzi.pitpat.data.activityservice.dto.rank.change.push.UserRankChangePushContext;
import com.linzi.pitpat.data.activityservice.enums.ActivityTempRankBizEnum;
import com.linzi.pitpat.data.activityservice.manager.api.UserRunDataReportManager;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDetailDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRunRankTempDetailPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRunRankTempDetailQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityRunRankTempDetailService;
import com.linzi.pitpat.data.activityservice.service.ActivityRunRankTempService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserExtraService;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.model.entity.I18nKeyValueDo;
import com.linzi.pitpat.data.systemservice.model.query.I18nKeyValueQuery;
import com.linzi.pitpat.data.systemservice.service.I18nKeyValueService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserRankChangePushBizService {
    private final ISysConfigService sysConfigService;
    private final SpringElRunnerService springElRunnerService;
    private final SpelExpressionParser springElParser = new SpelExpressionParser();
    private final ZnsUserService userService;

    /**
     * @param contexts
     * @param userRankChangePushRule
     * @return
     */
    private Map<Long, String> userRankChangePushSend(List<UserRankChangePushContext> contexts, String userRankChangePushRule) {
        SpringElEval springElEval = JsonUtil.readValue(userRankChangePushRule, SpringElEval.class);
        Map<Long, SpringElRunResult<Long>> run = springElRunnerService.run(springElEval, contexts);
        return run.entrySet().stream().filter(entry -> entry.getValue().isSuccess()).filter(entry -> entry.getValue().getPos() != null)
                .collect(HashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue().getPos()), HashMap::putAll);
    }

    private Map<Long, String> activityNotifyPushSend(List<UserRankChangePushContext> contexts, String activityNotifyPushRule) {
        SpringElEval springElEval = JsonUtil.readValue(activityNotifyPushRule, SpringElEval.class);
        Map<Long, SpringElRunResult<Long>> run = springElRunnerService.run(springElEval, contexts);
        return run.entrySet().stream().filter(entry -> entry.getValue().isSuccess())
                .collect(HashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue().getPos()), HashMap::putAll);
    }

    private final ActivityUserAwardCalculator calculator;
    private final ActivityRunRankTempService activityRunRankTempService;
    private final ActivityRunRankTempDetailService activityRunRankTempDetailService;
    private final UserRunDataReportManager userRunDataReportManager;
    private final RunActivityUserExtraService runActivityUserExtraService;
    private final ActivityStageService activityStageService;

    private final ActivityParamsService activityParamsService;

    public void sendUserRankChangePush(Long activityId, ZonedDateTime now) {
        log.info("开始推送:{},{}", activityId, now);
        //中途成绩计算
        boolean b = activityParamsService.allowUserRankChangePush(activityId);
        if (!b) {
            // 不支持计算，不做任何处理
            log.info("AwardCalculator - 活动不支持计算，activityId: {}", activityId);
            return;
        }
        ActivityRunRankTempDo calculate = calculator.calculateUserRank(activityId, now, ActivityTempRankBizEnum.RankChangePush);
        if (calculate == null) {
            return;
        }
        if (calculate.getPreActivityRunRankTempId() == null) {
            log.info("没有上一个版本：skip 不发送。{}", activityId);
            return;
        }
        ActivityRunRankTempDo preActivityRunRankTemp = activityRunRankTempService.findById(calculate.getPreActivityRunRankTempId());
        if (preActivityRunRankTemp == null) {
            log.info("上一个版本不存在：{},{}", activityId, calculate.getPreActivityRunRankTempId());
            return;
        }
        ActivityRunRankTempDetailQuery query = new ActivityRunRankTempDetailQuery();
        query.setActivityRunRankTempId(preActivityRunRankTemp.getId());
        Long count = activityRunRankTempDetailService.countByQuery(query);
        if (count <= 0) {
            log.info("上一个版本为空：{},{}", activityId, calculate.getPreActivityRunRankTempId());
            return;
        }

        sendPush(calculate.getId());

    }

    public void sendPush(Long tempId) {
        ActivityRunRankTempDo calculate = activityRunRankTempService.findById(tempId);
        if (calculate == null) {
            return;
        }
        ActivityRunRankTempDo preActivityRunRankTemp = activityRunRankTempService.findById(calculate.getPreActivityRunRankTempId());
        if (preActivityRunRankTemp == null) {
            return;
        }
        String userRankChangePushRule = sysConfigService.selectConfigByKey("USER_RANK_CHANGE_PUSH_RULE", true);
        String activityNotifyPushRule = sysConfigService.selectConfigByKey("ACTIVITY_NOTIFY_PUSH_RULE", true);
        int page = 1;
        ActivityRunRankTempDetailPageQuery pageQuery = new ActivityRunRankTempDetailPageQuery();
        pageQuery.setActivityRunRankTempId(calculate.getId());
        pageQuery.setIsComplete(1);
        pageQuery.setPageSize(500);
        pageQuery.setPageNum(page++);

//        准备第一名数据

        ActivityRunRankTempDetailDo top1Record = activityRunRankTempDetailService.findTop1(tempId);
        ActivityRunRankTempDetailDo preTop1Record = activityRunRankTempDetailService.findTop1(calculate.getPreActivityRunRankTempId());
        Boolean stageACtivity = activityStageService.isStageACtivity(calculate.getActivityId());
        long stageRemainCount = 0;
        if (stageACtivity) {
//            计算剩余阶段
            List<ActivityStage> byActId = activityStageService.findByActId(calculate.getActivityId());
            if (!CollectionUtils.isEmpty(byActId)) {
                ZonedDateTime date = ZonedDateTime.now();
                long count = byActId.stream().filter(a -> a.getStartTime().toInstant().toEpochMilli() > date.toInstant().toEpochMilli()).count();
                stageRemainCount = count;
            }
        }
        while (true) {
            Page<ActivityRunRankTempDetailDo> userRankPage = activityRunRankTempDetailService.findPage(pageQuery);
            if (CollectionUtils.isEmpty(userRankPage.getRecords())) {
                break;
            }
            List<UserRankChangePushContext> contexts = new ArrayList<>();
            for (ActivityRunRankTempDetailDo record : userRankPage.getRecords()) {
                UserRankChangePushContext context = new UserRankChangePushContext();
                context.setUserId(record.getUserId());
                context.setActivityId(record.getActivityId());
                context.setComplete(record.getIsComplete() == 1);
                context.setPreRank(record.getPreRank());
                context.setTop1Change(!top1Record.getUserId().equals(preTop1Record.getUserId()));
                context.setRank(record.getCurrentRank());
                context.setTarget(record.getTarget(calculate.getTargetType()));
                context.setAwardRank(calculate.getAwardRankByTarget(context.getTarget()));
                context.setUserTopSpeed(record.getTopSpeed().setScale(1, RoundingMode.HALF_UP));
                context.setActivityTopSpeed(calculate.getTopSpeed().setScale(1, BigDecimal.ROUND_HALF_UP));
                context.setPreActivityTopSpeed(preActivityRunRankTemp.getTopSpeed().setScale(1, BigDecimal.ROUND_HALF_UP));
                context.setUserEnrollCount(record.getEnrollTimes());
                if (stageACtivity) {
                    context.setEnrollLimit((int) (record.getEnrollTimes() + stageRemainCount));
                } else {
                    context.setEnrollLimit(calculate.getEnrollTimesLimit());
                }

                context.setTempDetailId(record.getId());
                context.setRecord(record);
                context.setAwardAmount(record.getGameAwardAmount());
                context.setAwardScore(record.getGameAwardScore());
                if (context.getPreRank() != context.getRank()) {
                    Optional<ActivityRunRankTempDetailDo> first = userRankPage.getRecords().stream().filter(a -> a.getCurrentRank().equals(context.getPreRank())).findFirst();
                    ActivityRunRankTempDetailDo preRank = null;
                    if (first.isPresent()) {
                        preRank = first.get();
                    } else {
                        ActivityRunRankTempDetailQuery preRankQuery = new ActivityRunRankTempDetailQuery();
                        preRankQuery.setCurrentRank(record.getPreRank());
                        preRankQuery.setActivityRunRankTempId(record.getActivityRunRankTempId());
                        preRank = activityRunRankTempDetailService.findByQuery(preRankQuery);
                    }
                    if (preRank != null) {
                        context.setPreAwardAmount(preRank.getGameAwardAmount());
                        context.setPreAwardScore(preRank.getGameAwardScore());
                    } else {
                        context.setPreAwardAmount(BigDecimal.ZERO);
                        context.setPreAwardScore(0);
                    }
                }
                log.info("context:{}", JsonUtil.writeString(context));
                contexts.add(context);

            }
            Map<Long, String> push1 = userRankChangePushSend(contexts, userRankChangePushRule);
            Map<Long, String> push2 = activityNotifyPushSend(contexts, activityNotifyPushRule);
            log.info("push1:{}", push1);
            log.info("push2:{}", push2);
            doPush(contexts, push1, tempId);
            doPush(contexts, push2, tempId);
            pageQuery.setPageNum(page++);

        }
    }

    private void doPush(List<UserRankChangePushContext> contexts, Map<Long, String> push, Long tempId) {
        PushFormat pushFormat = getPushFormat();
        List<Long> list = contexts.stream().map(UserRankChangePushContext::getId).toList();
        ActivityRunRankTempDetailDo topSpeedUser = activityRunRankTempDetailService.findTopSpeedUser(tempId);
        ActivityRunRankTempDetailDo top1Record = activityRunRankTempDetailService.findTop1(tempId);
        int awardRank1 = contexts.get(0).getAwardRank();
        ActivityRunRankTempDetailQuery awardRankQuery = new ActivityRunRankTempDetailQuery();
        awardRankQuery.setActivityRunRankTempId(tempId);
        awardRankQuery.setCurrentRank(contexts.get(0).getAwardRank());
        ActivityRunRankTempDetailDo awardRankRecord = activityRunRankTempDetailService.findByQuery(awardRankQuery);

        ZnsUserEntity top1User = userService.findById(top1Record.getUserId());
        List<ZnsUserEntity> byIds = userService.findByIds(list);
        List<Long> rankChangePushStateAllow = runActivityUserExtraService.findRankChangePushStateAllow(list, top1Record.getActivityId());
        push.forEach((userId, pos) -> {
            if ("未完赛".equals(pos)) {
                return;
            }
            if (!rankChangePushStateAllow.contains(userId)) {
                log.info("不同意推送:{}", userId);
                return;
            }
            UserRankChangePushContext context = contexts.stream().filter(item -> item.getId().equals(userId)).findFirst().orElse(null);
            if (context == null) {
                return;
            }
            ZnsUserEntity user = byIds.stream().filter(item -> item.getId().equals(userId)).findFirst().orElse(null);
            if (user == null) {
                return;
            }
            ActivityRunRankTempDetailQuery query = new ActivityRunRankTempDetailQuery();
            query.setActivityRunRankTempId(tempId);
            query.setCurrentRank(context.getPreRank());
            ActivityRunRankTempDetailDo preRankRecord = activityRunRankTempDetailService.findByQuery(query);
            if (preRankRecord == null) {

            } else {


                PushTemplateContext templateContext = new PushTemplateContext();
                templateContext.currentRank = context.getRank();
                templateContext.awardRank = context.getAwardRank();
                templateContext.preRankAward = preRankRecord.getGameAwardStr();
                templateContext.userTopSpeed = context.getUserTopSpeed() + "kph";
                templateContext.userAward = context.getRecord().getGameAwardStr();
                templateContext.lastAward = awardRankRecord == null ? "" : awardRankRecord.getGameAwardStr();
                templateContext.preRank = context.getPreRank();
                templateContext.topSpeed = context.getActivityTopSpeed() + "kph";
                templateContext.preTopSpeed = context.getPreActivityTopSpeed() + "kph";
                templateContext.top1Name = top1User.getFirstName();

                templateContext.top1Grade = top1Record.getGradeStr();
                templateContext.top1GradeNumber = top1Record.getGradeNumber();
                templateContext.gradeDelta = top1Record.getGradeDelta(context.getRecord().getGradeNumber());
                templateContext.gradeNumber = context.getRecord().getGradeNumber();
                templateContext.grade = context.getRecord().getGradeStr();
                if (context.getEnrollLimit() > 0) {
                    templateContext.enrollTimes = context.getEnrollLimit() - context.getUserEnrollCount();
                } else {
                    templateContext.enrollTimes = -1;
                }

                String pushMsg = pushFormat.getPushMsg(user.getLanguageCode(), pos, templateContext, springElParser);
                log.info("pushMsg:{},{},{}", pushMsg, user.getId(), context.getActivityId());
                if (StringUtils.hasText(pushMsg)) {
                    MessageBo messageBo = new MessageBo();
                    messageBo.setCollapseKey("pitpat");
                    messageBo.setTitle("Pitpat");
                    messageBo.setContent(pushMsg);
                    messageBo.setJumpType("5");
                    messageBo.setRouteType(1);
                    messageBo.setRouteValue("lznative://lzrace/EventDetails");
                    messageBo.setData(Map.of("activityId", context.getActivityId(), "isPolyList", 0));
                    appMessageService.push(List.of(user.getId()), messageBo, "");
                }
            }

        });
    }

    private final AppMessageService appMessageService;
    private final I18nKeyValueService i18nKeyValueService;

    private PushFormat getPushFormat() {
        I18nKeyValueQuery query = new I18nKeyValueQuery();
        query.setScene("Push_Activity_User_Rank_Change");
        List<I18nKeyValueDo> list = i18nKeyValueService.findList(query);
        PushFormat format = new PushFormat();
        format.init(list);
        return format;
    }

    @Data
    public static class PushFormat {
        private Map<String, Map<String, String>> pushTemplate;

        public void init(List<I18nKeyValueDo> list) {
            pushTemplate = new HashMap<>();
            list.forEach(item -> {
                Map<String, String> orDefault = pushTemplate.getOrDefault(item.getLangCode(), new HashMap<>());
                orDefault.put(item.getKeyCode(), item.getValue());
                pushTemplate.put(item.getLangCode(), orDefault);
            });
        }

        public String getPushMsg(String languageCode, String pos, PushTemplateContext context, SpelExpressionParser parser) {
            String pushMsgTemplate = getPushMsgTemplate(languageCode, pos, context);
            if (!StringUtils.hasText(pushMsgTemplate)) {
                return "";
            }
            //查找字符串中 { } 包裹的部分，，没一个都需要执行EL表达式，然后替换为结果
            String regex = "\\{[^\\}]+\\}";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(pushMsgTemplate);
            while (matcher.find()) {
                String group = matcher.group();
                String key = group.substring(1, group.length() - 1);
                String value = getValue(key, context, parser);
                if (!StringUtils.hasText(value)) {
                    log.info("解析失败:{},{},{}", pos, pushMsgTemplate, key);
                    return "";
                }
                pushMsgTemplate = pushMsgTemplate.replace(group, value);
            }

            return pushMsgTemplate;
        }

        private String getValue(String el, PushTemplateContext context, SpelExpressionParser parser) {

            StandardEvaluationContext elContext = new StandardEvaluationContext(context);
            String string = parser.parseExpression(el).getValue(elContext).toString();
            return string;
        }

        private String getPushMsgTemplate(String languageCode, String pos, PushTemplateContext context) {
            String byLanguageCode = getByLanguageCode(languageCode, pos, context);
            if (StringUtils.hasText(byLanguageCode)) {
                return byLanguageCode;
            }
            return getByLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode(), pos, context);
        }

        private String getByLanguageCode(String languageCode, String pos, PushTemplateContext context) {
            Map<String, String> stringStringMap = pushTemplate.get(languageCode);
            if (CollectionUtils.isEmpty(stringStringMap)) {
                return "";
            }
            if ("该用户未打破记录".equals(pos)) {
                if (RandomUtils.nextBoolean()) {
                    return stringStringMap.getOrDefault("该用户未打破记录-1", "");
                }
            }
            return stringStringMap.getOrDefault(pos, "");
        }
    }

    @Data
    private static class PushTemplateContext {
        /**
         * 当前成绩
         */
        private Integer currentRank;
        /**
         * 奖励区排名
         */
        private Integer awardRank;
        /**
         * 上一次排名的对应奖励
         */
        private String preRankAward;

        /**
         * 用户当前排名奖励
         */
        private String userAward;

        /**
         * 奖励区最后一名奖励
         */
        private String lastAward;

        /**
         * 上一次排名
         */
        private Integer preRank;

        /**
         * 当前全程最高速度
         */
        private String topSpeed;
        private String userTopSpeed;
        private String preTopSpeed;
        /**
         * top 1的名字
         */
        private String top1Name;
        /**
         * top1 的成绩
         */
        private String top1Grade;
        /**
         * top 1 的成绩
         */
        private BigDecimal top1GradeNumber;
        /**
         * 当前用户的成绩
         */
        private BigDecimal gradeNumber;
        /**
         * 用户的成绩
         */
        private String grade;
        /**
         * 与第一名的差值
         */
        private String gradeDelta;
        private Integer enrollTimes;


    }
}
