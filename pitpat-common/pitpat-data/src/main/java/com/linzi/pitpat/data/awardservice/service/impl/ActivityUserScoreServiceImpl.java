package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ISelect;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.dto.West8StartEndDto;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.mapper.ScoreConfigDao;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreIdDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig;
import com.linzi.pitpat.data.awardservice.model.query.ActivityUserScoreQuery;
import com.linzi.pitpat.data.awardservice.model.query.AddColorEggAward;
import com.linzi.pitpat.data.awardservice.model.query.UserScorePageQuery;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.enums.ActivityFeeTypeEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.userservice.dto.event.UserScoreSendEvent;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.annotation.RedisLock;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;

@Service
@Slf4j
public class ActivityUserScoreServiceImpl extends ServiceImpl<ActivityUserScoreDao, ActivityUserScore> implements ActivityUserScoreService {

    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;

    @Autowired
    private ScoreConfigDao scoreConfigDao;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private QueueMessageService queueMessageService;

    @Override
    public boolean insertOrUpdateActivityUserScore(ActivityUserScore activityUserScore) {
        boolean b = saveOrUpdate(activityUserScore);
        if (activityUserScore != null && activityUserScore.getIncome() != null && activityUserScore.getIncome() == 1) {
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserScoreSendEvent.getEventType(),UserScoreSendEvent.of(activityUserScore.getUserId(), activityUserScore.getScore()));
        }
        return b;
    }

    @Override
    public Long increaseAmount(int score, Long activityId, Long userId, Integer rank, Integer extraScore, Integer source) {
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setActivityId(activityId);
        activityUserScore.setUserId(userId);
        activityUserScore.setRank(rank);
        activityUserScore.setScore(score);
        activityUserScore.setStatus(1);
        activityUserScore.setSource(source);
        activityUserScore.setExtraScore(extraScore);
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setAwardTime(ZonedDateTime.now());
        activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
        activityUserScoreDao.insert(activityUserScore);

        Long id = activityUserScore.getId();
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserScoreSendEvent.getEventType(),UserScoreSendEvent.of(activityUserScore.getUserId(), activityUserScore.getScore()));
        return id;
    }

    @Override
    public int update(ActivityUserScore activityUserScore) {
        return activityUserScoreDao.updateById(activityUserScore);
    }

    @Override
    public void beChallengeIncreaseAmount(int score, Long activityId, Long userId) {
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setActivityId(activityId);
        activityUserScore.setUserId(userId);
        activityUserScore.setScore(score);
        activityUserScore.setStatus(1);
//		activityUserScore.setExtraScore(extraScore);
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setAwardTime(ZonedDateTime.now());
        activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
        activityUserScoreDao.insert(activityUserScore);
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserScoreSendEvent.getEventType(),UserScoreSendEvent.of(activityUserScore.getUserId(), activityUserScore.getScore()));
    }

    @Override
    public void useActivityUserScore(ExchangeScoreRule exchangeScoreRule, ScoreIdDto dto) {
        Integer sum = 0;
        Integer exchangeScore = exchangeScoreRule.getExchangeScore();
        for (int i = 1; i < 100; i++) {
            PPageUtils<ActivityUserScore> pageUtils = PPageUtils.startPage(1, 100).doSelect(new ISelect() {
                @Override
                public List doSelect(IPage page) {
                    return activityUserScoreDao.selectActivityUserScoreByUserIdStatusExpireTime(page, dto.getUserId(), Arrays.asList(1, 3), 1);
                }
            });
            if (pageUtils.getRows() != null && pageUtils.getRows().size() > 0) {
                for (ActivityUserScore activityUserScore : pageUtils.getRows()) {
                    //
                    int current = MapUtil.getInteger(activityUserScore.getScore(), 0) -
                            MapUtil.getInteger(activityUserScore.getUseScore(), 0);
                    if (sum + current > exchangeScore) {
                        current = exchangeScore - sum;                //本次需要使用的积分
                        // 本次使用的积分加之前使用的积分= 最终使用的积分
                        activityUserScore.setUseScore(current + MapUtil.getInteger(activityUserScore.getUseScore(), 0));
                        activityUserScore.setStatus(3);
                    } else {
                        activityUserScore.setUseScore(activityUserScore.getScore());
                        activityUserScore.setStatus(2);
                    }
                    sum = sum + current;
                    activityUserScore.setExchangeScoreRuleId(exchangeScoreRule.getId());
                    activityUserScore.setExchangeTime(ZonedDateTime.now());
                    activityUserScore.setType(1);
                    activityUserScoreDao.updateById(activityUserScore);
                    log.info("ActivityUserScore id=" + activityUserScore.getId() + ",score=" + activityUserScore.getScore() + ",userScore = " + activityUserScore.getUseScore()
                            + ",status = " + activityUserScore.getStatus() + ",本次使用积分 ：" + current);

                    if (sum >= exchangeScore) {
                        return;
                    }
                }
            } else {
                return;
            }
        }
    }

    @Override
    public void useActivityUserScore(int score, Long activityId, Long userId, Integer rank) {
        Integer sum = 0;
        for (int i = 1; i < 100; i++) {
            PPageUtils<ActivityUserScore> pageUtils = PPageUtils.startPage(1, 100).doSelect(new ISelect() {
                @Override
                public List doSelect(IPage page) {
                    return activityUserScoreDao.selectActivityUserScoreByUserIdStatusExpireTime(page, userId, Arrays.asList(1, 3), 1);
                }
            });
            if (pageUtils.getRows() != null && pageUtils.getRows().size() > 0) {
                for (ActivityUserScore activityUserScore : pageUtils.getRows()) {
                    int current = MapUtil.getInteger(activityUserScore.getScore(), 0) -
                            MapUtil.getInteger(activityUserScore.getUseScore(), 0);
                    if (sum + current > score) {
                        current = score - sum;                //本次需要使用的积分
                        // 本次使用的积分加之前使用的积分= 最终使用的积分
                        activityUserScore.setUseScore(current + MapUtil.getInteger(activityUserScore.getUseScore(), 0));
                        activityUserScore.setStatus(3);
                    } else {
                        activityUserScore.setUseScore(activityUserScore.getScore());
                        activityUserScore.setStatus(2);
                    }
                    sum = sum + current;
                    activityUserScore.setExchangeTime(ZonedDateTime.now());
                    activityUserScore.setType(1);
                    activityUserScoreDao.updateById(activityUserScore);
                    log.info("ActivityUserScore id=" + activityUserScore.getId() + ",score=" + activityUserScore.getScore() + ",userScore = " + activityUserScore.getUseScore()
                            + ",status = " + activityUserScore.getStatus() + ",本次使用积分 ：" + current);

                    if (sum >= score) {
                        return;
                    }
                }
            } else {
                return;
            }
        }
    }


    /**
     * @param userId
     * @param type         1. 签到 ， 2.设备连接 , 3.每运动1英里 , 4.完成一次课程 , 5.参与官方比赛 6. 参加PK赛，-1 过期 ，-2 积分兑换券， -3 积分兑换商品 7.完成新人任务 8-完成评价/调查问卷
     * @param compareCount
     * @return
     */
    @Override
    @RedisLock(value = "userId", isTry = true)
    public Result sendScore(Long userId, Integer type, Integer compareCount, Long activityId, Long courseId) {
        if (Objects.equals(compareCount, 0)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("userScore.compareCount.zero")); //TODO 校正 //"compareCount 为0 "
        }

        ScoreConfig scoreConfig = scoreConfigDao.selectScoreConfigByType(type);
        if (Objects.isNull(scoreConfig)) {
            log.error("配置不存在或被删除， type={}", type);
            return CommonResult.fail("userScore.sentCount.exceed"); //"课程的券已经发送过了"
        }
        //如果对于某些活动的话，只要发送过，则不再发送
        if (activityId != null && activityId > 0) {
            ActivityUserScore activityUserScore = activityUserScoreDao.selectActivityUserScoreByActivityUserId(activityId, userId, type);
            if (activityUserScore != null) {
                return CommonResult.fail(I18nMsgUtils.getMessage("userScore.sent.coupon")); //"券已经发送过了"
            }
        }
        if (courseId != null && courseId > 0) {
            ActivityUserScore activityUserScore = activityUserScoreDao.selectActivityUserScoreByCourseIdUserId(courseId, userId, type);
            if (activityUserScore != null) {
                return CommonResult.fail("userScore.sent.course"); //"课程的券已经发送过了"
            }
        }

        int acquireCount = 0;
        West8StartEndDto west8StartEndDto = NumberUtils.getWest8StartEndTime();
        if (StringUtil.eqList(scoreConfig.getCalStyle(), "life")) {
            acquireCount = activityUserScoreDao.selectActivityUserScoreByUserIdStartTimeEndTime(userId, null, west8StartEndDto.getEndTime(), scoreConfig.getType());
        } else {
            acquireCount = activityUserScoreDao.selectActivityUserScoreByUserIdStartTimeEndTime(userId, west8StartEndDto.getStartTime(), west8StartEndDto.getEndTime(), scoreConfig.getType());
        }
        log.info("sendScore compareCount = " + compareCount + ",acquireCount= " + acquireCount);
        if (acquireCount >= scoreConfig.getLimitCount()) {
            return CommonResult.fail(I18nMsgUtils.getMessage("userScore.sentCount.exceed")); //积分发放次数超过上限  "out of limit "
        }
        // 默认情况下发送一第券
        int last = 1;
        if (compareCount != null && compareCount > 0) {
            int sendAcount = scoreConfig.getLimitCount() - acquireCount; // 最多能发几张
            log.info("还能发多少张sendAcount= " + sendAcount);
            int aquire = compareCount - acquireCount;
            log.info("本次请求发多少张aquire= " + aquire);
            last = aquire > sendAcount ? sendAcount : aquire;
        }

        log.info("最张发多少积分last =" + last);
        if (last > 0) {
            for (int i = 0; i < last; i++) {
                // 增加积分兑换记录
                ActivityUserScore activityUserScore = new ActivityUserScore();
                activityUserScore.setScore(scoreConfig.getScore());
                activityUserScore.setStatus(0);
                activityUserScore.setIncome(1);
                activityUserScore.setScoreConfigId(scoreConfig.getId());
                activityUserScore.setSource(scoreConfig.getType());
                activityUserScore.setSendTime(ZonedDateTime.now());
                activityUserScore.setUserId(userId);
                activityUserScore.setCourseId(courseId);
                activityUserScore.setActivityId(activityId);
                activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
                activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
                insertOrUpdateActivityUserScore(activityUserScore);
            }
        }
        return CommonResult.success(scoreConfig);
    }

    @Override
    public List<ActivityUserScore> selectActivityUserScoreByActivityId(Long activityId) {
        return activityUserScoreDao.selectActivityUserScoreByActivityId(activityId);
    }

    @Override
    public ActivityUserScore selectUserScoreByActivityIdUserIdMilepost(Long userId, Long activityId, int milepost) {
        return activityUserScoreDao.selectOneByMilepost(userId, activityId, milepost);
    }

    @Override
    public List<ActivityUserScore> selectActivityUserScoreByActivityIdUserId(Long activityId, Long userId) {
        return this.list(Wrappers.<ActivityUserScore>lambdaQuery()
                .eq(ActivityUserScore::getActivityId, activityId)
                .eq(ActivityUserScore::getUserId, userId)
                .eq(ActivityUserScore::getIsDelete, 0));
    }

    @Override
    public Integer sumScore(Long activityId, Long userId, List<Integer> source, Integer status) {
        return activityUserScoreDao.selectSumScore(activityId, userId, source, status);
    }

    @Override
    public Integer sumScoreWithTime(Long activityId, Long userId, Integer status, ZonedDateTime createTime) {
        return activityUserScoreDao.selectSumScoreWithTime(activityId, userId, status, createTime);
    }

    public static void main(String[] args) {


        Integer exchangeScore = 10;

        ActivityUserScore activityUserScore1 = new ActivityUserScore();
        activityUserScore1.setId(1l);
        activityUserScore1.setScore(8);
        activityUserScore1.setUseScore(3);

        ActivityUserScore activityUserScore2 = new ActivityUserScore();
        activityUserScore2.setId(2l);
        activityUserScore2.setScore(5);
        activityUserScore2.setUseScore(0);


        ActivityUserScore activityUserScore3 = new ActivityUserScore();
        activityUserScore3.setId(3l);
        activityUserScore3.setScore(5);
        activityUserScore3.setUseScore(0);

        List<ActivityUserScore> activityUserScores = new ArrayList<>();

        activityUserScores.add(activityUserScore1);
        activityUserScores.add(activityUserScore2);
        activityUserScores.add(activityUserScore3);

        int sum = 0;
        for (ActivityUserScore activityUserScore : activityUserScores) {
            int current = MapUtil.getInteger(activityUserScore.getScore(), 0) -
                    MapUtil.getInteger(activityUserScore.getUseScore(), 0);
            if (sum + current > exchangeScore) {
                current = exchangeScore - sum;
                activityUserScore.setUseScore(current + MapUtil.getInteger(activityUserScore.getUseScore(), 0));
                activityUserScore.setStatus(3);
            } else {
                activityUserScore.setUseScore(activityUserScore.getScore());
                activityUserScore.setStatus(2);
            }
            sum = sum + current;


            System.out.println("id=" + activityUserScore.getId() + ",score=" + activityUserScore.getScore() + ",userScore = " + activityUserScore.getUseScore()
                    + ",status = " + activityUserScore.getStatus() + ",本次使用积分 ：" + current);

            if (sum >= exchangeScore) {
                return;
            }
        }
    }

    @Override
    public Integer countByUserIdTradeTypeSubType(Long userId, ZonedDateTime startOfDate, ZonedDateTime date, List<Integer> list) {
        return activityUserScoreDao.countByUserIdTradeTypeSubType(userId, startOfDate, date, list);
    }

    @Override
    public BigDecimal selectScoreByUserIdSourceType(Long userId, ZonedDateTime startOfDate, ZonedDateTime date, List<Integer> type) {
        BigDecimal score = activityUserScoreDao.selectScoreByUserIdSourceType(userId, startOfDate, date, type);
        if (Objects.isNull(score)) {
            return BigDecimal.ZERO;
        }
        return score;
    }

    @Override
    public void handleRunScore(AddColorEggAward request) {
        int score = request.getAwardValue().intValue();
        int source = 0;
        if (request.getColorEggConfigId() != null) {
            source = ScoreConstant.SourceTypeEnum.source_type_17.getType();
        }
        if (request.getUrgeActivityConfigId() != null) {
            if (Arrays.asList(6, 9).contains(request.getUrgeType())) {
                source = ScoreConstant.SourceTypeEnum.source_type_18.getType();
            } else if (Arrays.asList(7, 10).contains(request.getUrgeType())) {
                source = ScoreConstant.SourceTypeEnum.source_type_19.getType();
            } else if (Arrays.asList(8, 11).contains(request.getUrgeType())) {
                source = ScoreConstant.SourceTypeEnum.source_type_20.getType();
            }
        }
        this.increaseAmount(score, request.getActivityId(), request.getUserId(), 0, 0, source);
    }

    @Override
    public Integer sumScore(List<Long> activityIds, Long userId, List<Integer> source, Integer status) {
        return activityUserScoreDao.selectSumScoreByActivityIds(activityIds, userId, source, status);
    }

    @Override
    public void changeScoreExchangeAmount(Long userId, BigDecimal exchangeRate) {
        List<ActivityUserScore> activityUserScores = activityUserScoreDao.selectList(new QueryWrapper<ActivityUserScore>().lambda().eq(ActivityUserScore::getUserId, userId).eq(ActivityUserScore::getIsDelete, 0).gt(ActivityUserScore::getAmount, 0));
        if (CollectionUtils.isEmpty(activityUserScores)) {
            return;
        }
        activityUserScores.forEach(s -> s.setAmount(s.getAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_DOWN)));
        this.updateBatchById(activityUserScores);
    }

    @Override
    public Integer selectByExchangeScoreRuleIdSource(Long exchangeScoreRuleId, List<Integer> source) {
        return activityUserScoreDao.selectByExchangeScoreRuleIdSource(exchangeScoreRuleId, source);
    }

    @Override
    public ActivityUserScore selectActivityUserScoreByActivityIdUserIdRank(Long activityId, Long userId, Integer rank) {
        return activityUserScoreDao.selectActivityUserScoreByActivityIdUserIdRank(activityId, userId, rank);
    }

    /**
     * 合并积分账户
     *
     * @param newUserId
     * @param oldUserIds
     */
    @Override
    public void mergeUserScore(Long newUserId, List<Long> oldUserIds) {
        activityUserScoreDao.mergeUserScore(newUserId, oldUserIds);
    }

    @Override
    public ActivityUserScore selectExchangePersonCountByScoreConfigIdUserId(Long scoreConfigId, Long userId) {
        return activityUserScoreDao.selectExchangePersonCountByScoreConfigIdUserId(scoreConfigId, userId);
    }

    @Override
    public int selectActivityUserScoreByUserIdMonthExpire(Long userId, ZonedDateTime startTime, ZonedDateTime endTime, List<Integer> status) {
        return activityUserScoreDao.selectActivityUserScoreByUserIdMonthExpire(userId, startTime, endTime, status);
    }

    @Override
    public List<ActivityUserScore> selectActivityUserScoreByScoreConfigIdUserId(ZonedDateTime startTime, ZonedDateTime endTime, Long scoreConfigId, Long userId, Integer status) {
        return activityUserScoreDao.selectActivityUserScoreByScoreConfigIdUserId(startTime, endTime, scoreConfigId, userId, status);
    }

    @Override
    public void updateScoreStatusExpireTime(ZonedDateTime gmtModified, ZonedDateTime expireTime, ZonedDateTime awardTime, Integer status, Long id) {
        activityUserScoreDao.updateScoreStatusExpireTime(gmtModified, expireTime, awardTime, status, id);
    }

    @Override
    public List selectActivityUserScoreByUserIdMonthApp(IPage page, Long userId, String queryMonth, Integer income, List<Integer> source, Integer status) {
        return activityUserScoreDao.selectActivityUserScoreByUserIdMonthApp(page, userId, queryMonth, income, source, status);
    }

    @Override
    public ActivityUserScore selectFirstActivityUserScore(Long userId, Integer income, List<Integer> source, Integer status) {
        return activityUserScoreDao.selectFirstActivityUserScore(userId, income, source, status);
    }

    @Override
    public int selectActivityUserScoreByUserIdMonth(Long userId, ZonedDateTime firstOfMonth, ZonedDateTime endOfDate, Integer income, Integer status) {
        return activityUserScoreDao.selectActivityUserScoreByUserIdMonth(userId, firstOfMonth, endOfDate, income, status);
    }

    @Override
    public Integer getAllUserScore(Long userId) {
        int awardScore = activityUserScoreDao.selectAllAwardScore(userId, 1, 0);
        int consumeScore = activityUserScoreDao.selectAllAwardScore(userId, -1, 0);
        int currentScore = awardScore - consumeScore;
        return Math.max(currentScore, 0);
    }

    @Override
    public boolean checkUserActivityEntryScore(ActivityFee fee, Long userId) {
        if (!fee.getType().equals("score") &&
                !fee.getType().equals("scoreFee") &&
                !ActivityFeeTypeEnum.SCORE_OR_FEE.getType().equals(fee.getType())) {
            return true;
        }

        Integer userScore = getAllUserScore(userId);
        return userScore >= fee.getScore();
    }

    @Override
    public ActivityUserScore selectActivityUserScoreByActivityUserId(Long activityId, Long userId, Integer source) {
        return activityUserScoreDao.selectActivityUserScoreByActivityUserId(activityId, userId, source);
    }

    @Override
    public ActivityUserScore getRefundActivityUserScore(MainActivity activityEntity, Long userId, Integer score, boolean refund) {
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setActivityId(activityEntity.getId());
        activityUserScore.setExchangeScoreRuleId(-1L);
        activityUserScore.setScore(score);
        activityUserScore.setStatus(1);
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setType(4); //活动使用;
        activityUserScore.setIncome(refund ? 1 : -1); //退款会增加积分， 报名则会扣除积分
        activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_21.getType());
        activityUserScore.setUserId(userId);
        activityUserScore.setExpireTime(refund ? DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")) : null);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        return activityUserScore;
    }

    @Override
    public Page<ActivityUserScore> findPage(UserScorePageQuery pageQuery) {
        return activityUserScoreDao.findPage(Page.of(pageQuery.getPageNum(), pageQuery.getPageSize(), pageQuery.getSearchCount()), pageQuery);
    }

    private LambdaQueryWrapper<ActivityUserScore> buildWrapperQuery(UserScorePageQuery pageQuery) {
        return Wrappers.<ActivityUserScore>lambdaQuery()
                .ge(Objects.nonNull(pageQuery.getGeExpireTime()), ActivityUserScore::getExpireTime, pageQuery.getGeExpireTime())
                .lt(Objects.nonNull(pageQuery.getLtExpireTime()), ActivityUserScore::getExpireTime, pageQuery.getLtExpireTime())
                .in(!CollectionUtils.isEmpty(pageQuery.getStatusList()), ActivityUserScore::getStatus, pageQuery.getStatusList())
                .eq(ActivityUserScore::getIsDelete, 0);
    }

    @Override
    public ActivityUserScore findSumScore(ActivityUserScoreQuery query) {
        ActivityUserScore activityUserScore = activityUserScoreDao.selectOne(new QueryWrapper<ActivityUserScore>()
                .select("SUM(IFNULL(score,0)) score,SUM(IFNULL(use_score,0)) useScore")
                .eq("is_delete", 0)
				.eq(Objects.nonNull(query.getUserId()), "user_id", query.getUserId())
                .eq(Objects.nonNull(query.getStatus()), "status", query.getStatus())
                .in(!CollectionUtils.isEmpty(query.getStatusList()), "status", query.getStatusList())
                .eq(Objects.nonNull(query.getIncome()), "income", query.getIncome())
                .eq(Objects.nonNull(query.getSource()), "source", query.getSource())
                .ne(Objects.nonNull(query.getNeSource()), "source", query.getNeSource()));
    if (Objects.isNull(activityUserScore)) {
			activityUserScore = new ActivityUserScore();
			activityUserScore.setScore(0);
			activityUserScore.setUseScore(0);
		}
		return activityUserScore;
	}

    @Override
    public void deleteByQuery(ActivityUserScoreQuery query) {

        activityUserScoreDao.delete(buildQueryWrapper(query));
    }

    @Override
    public void syncUseScore(ActivityUserScoreQuery query) {
        List<ActivityUserScore> activityUserScores = activityUserScoreDao.selectList(buildQueryWrapper(query));
        if (CollectionUtils.isEmpty(activityUserScores)) {
            return;
        }
        for (ActivityUserScore activityUserScore : activityUserScores) {
            activityUserScore.setUseScore(activityUserScore.getScore());
        }
        activityUserScoreDao.updateById(activityUserScores);
    }

    @Override
    public List<ActivityUserScore> findList(ActivityUserScoreQuery query) {
        return activityUserScoreDao.selectList(buildQueryWrapper(query));
    }

    private Wrapper<ActivityUserScore> buildQueryWrapper(ActivityUserScoreQuery query) {
        return new QueryWrapper<ActivityUserScore>()
                .eq(Objects.nonNull(query.getUserId()), "user_id", query.getUserId())
                .eq(Objects.nonNull(query.getStatus()), "status", query.getStatus())
                .in(!CollectionUtils.isEmpty(query.getStatusList()), "status", query.getStatusList())
                .eq(Objects.nonNull(query.getIncome()), "income", query.getIncome())
                .eq(Objects.nonNull(query.getSource()), "source", query.getSource())
                .ne(Objects.nonNull(query.getNeSource()), "source", query.getNeSource());
    }
}
