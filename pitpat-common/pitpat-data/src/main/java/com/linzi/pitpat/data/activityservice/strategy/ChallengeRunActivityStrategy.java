package com.linzi.pitpat.data.activityservice.strategy;


import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.manager.ActivityMessageManager;
import com.linzi.pitpat.data.activityservice.mapper.PkChallengeRecordDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityUserDao;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityEntryFeeQuery;
import com.linzi.pitpat.data.activityservice.model.query.PkChallengeRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.model.vo.FriendPKAwardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.FriendPkAward;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkAward;
import com.linzi.pitpat.data.activityservice.model.vo.PKAward;
import com.linzi.pitpat.data.activityservice.model.vo.PKAwardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.SegmentAward;
import com.linzi.pitpat.data.activityservice.model.vo.SpecificPKAward;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SpeedPropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.mapper.UserCouponDao;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDao;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.dto.RunActivityRewardConfigDetails;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.UserAccountDetailSub;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.CouponAwardDto;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.domian.query.SpeedPropRecordQuery;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivitySubstateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PkFriendAwardUtil;
import com.linzi.pitpat.data.vo.UserFriendMatchVo;
import com.linzi.pitpat.data.vo.report.ChallengeRunReportVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Service
@Slf4j
public class ChallengeRunActivityStrategy extends BaseActivityStrategy {
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;

    @Resource
    private UserFriendMatchService userFriendMatchService;

    @Autowired
    private PkChallengeRecordDao pkChallengeRecordDao;
    @Resource
    protected UserCouponDao userCouponDao;

    @Autowired
    private PkChallengeRecordService pkChallengeRecordService;
    @Autowired
    private ActivityMessageManager activityMessageManager;

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private ZnsRunActivityUserDao znsRunActivityUserDao;

    @Resource
    private ZnsUserAccountDao znsUserAccountDao;
    @Resource
    private ActivityEntryFeeService activityEntryFeeService;
    @Resource
    private ExchangeRateConfigService exchangeRateConfigService;
    @Resource
    private ActivityAwardCurrencyBizService activityAwardCurrencyBizService;
    @Resource
    private AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;

    @Resource
    private SpeedPropRecordService speedPropRecordService;


    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    @Override
    public void wrapperRunActivityBasicData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity) {
        super.wrapperRunActivityBasicData(activityEntity, activityDetailVO, userEntity);
        setActivityLaunchUser(activityDetailVO, activityEntity.getId());

        if (activityEntity.getCompleteRuleType() == 1) {
            String mil = activityEntity.getRunMileage().divide(new BigDecimal(1000)).setScale(2, BigDecimal.ROUND_HALF_DOWN).stripTrailingZeros().toPlainString() + "km";
            if (Objects.nonNull(userEntity.getMeasureUnit()) && userEntity.getMeasureUnit() == 1) {
                mil = activityEntity.getRunMileage().divide(new BigDecimal(1600)).setScale(2, BigDecimal.ROUND_HALF_DOWN).stripTrailingZeros().toPlainString() + "Miles";
            }
            activityDetailVO.setActivityTitle(mil + " PK Run");
        }
        // 设置对应currency的entryFee
        activityDetailVO.setActivityEntryFee(activityEntity.getActivityEntryFee());
        ActivityEntryFeeQuery query = ActivityEntryFeeQuery.builder().activityId(activityEntity.getId()).build();
        List<ActivityEntryFee> list = activityEntryFeeService.findList(query);
        String currencyCode = userAccountService.getUserAccount(userEntity.getId()).getCurrencyCode();
        if (!CollectionUtils.isEmpty(list)) {
            ActivityEntryFee activityEntryFee = list.stream().filter(e -> e.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
            ActivityEntryFee activityUSDEntryFee = list.stream().filter(e -> e.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())).findFirst().orElse(null);
            if (runActivityService.checkNewActivity(activityEntity.getId()) && Objects.nonNull(activityEntryFee)) {
                BigDecimal entryFee = activityEntryFee.getEntryFee();
                entryFee = I18nConstant.currencyFormat(currencyCode, entryFee);
                activityDetailVO.setActivityEntryFee(entryFee);
                activityEntity.setActivityEntryFee(entryFee);
            } else if (Objects.nonNull(activityUSDEntryFee)) {
                BigDecimal entryFee = activityUSDEntryFee.getEntryFee();
                entryFee = I18nConstant.currencyFormat(currencyCode, entryFee);
                activityDetailVO.setActivityEntryFee(entryFee);
                activityEntity.setActivityEntryFee(entryFee);
            }
        }
        //pk赛类型
        UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(activityEntity.getId());
        if (Objects.nonNull(userFriendMatch)) {
            activityDetailVO.setIsRandomMatching(0);
        } else {
            activityDetailVO.setIsRandomMatching(1);
        }
        // 国际化数据
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.findRunActivityConfig(activityEntity.getActivityConfigId());
        Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
        wrapperRunActivityRules(activityEntity, jsonObject, null, 3000);
    }


    @Override
    public void wrapperRunActivityDetailData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        super.wrapperRunActivityDetailData(activityEntity, activityDetailVO, userEntity, oneself);
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(userEntity.getId());
        // 活动奖励金额
        String participateAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD));
        String winnerAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.WINNER_AWARD));

        // 挑战跑开始跑前多少分钟可入场(配置分钟)
        Integer beforeEnterMinutes = MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.CHALLENGE_ACTIVITY_BEFORE_ENTER));
        if (null == beforeEnterMinutes) {
            beforeEnterMinutes = 5;
        }
        try {
            BigDecimal participate = new BigDecimal(participateAward);
            participate = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), participate);
            participateAward = participate.toString();
        } catch (Exception e) {
            log.error("金额转换异常", e);
        }
        activityDetailVO.setParticipateAward(participateAward);

        long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;

        if (Objects.nonNull(activityEntity.getAppointmentStartTime())) {
            challengeMillisecond = 30 * 60000;
            activityDetailVO.setAppointmentStartTime(activityEntity.getAppointmentStartTime());
            activityDetailVO.setRaceSetType(2);
        }
        activityDetailVO.setBeforeEnterChallengeRunTime(challengeMillisecond);

        // 这里会把前面赋值给活动实体的奖励文案（已经做了国际化的）赋给返回值
        activityDetailVO.setAwardRule(activityEntity.getAwardRule());
        Currency currency = userAccountService.getCurrency(userEntity.getId(), activityEntity.getId(), true);
        if (RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType().equals(activityEntity.getActivityTypeSub())) {
            PkChallengeRecord challengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 1);
            PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(activityEntity.getId()).userId(userEntity.getId()).build();
            PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.findByQuery(query);
            activityDetailVO.setCurrency(I18nConstant.buildDefaultCurrency());
            if (Objects.nonNull(pkChallengeRecord)) {
                BigDecimal award = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), pkChallengeRecord.getAward());
                winnerAward = award.toString();
                if (StringUtils.hasText(pkChallengeRecord.getCurrencyCode())) {
                    // 新活动
                    activityDetailVO.setCurrency(I18nConstant.buildCurrency(pkChallengeRecord.getCurrencyCode()));
                }
            } else {
                // h5页面访问
                pkChallengeRecord = new PkChallengeRecord();
                pkChallengeRecord.setScore(challengeRecord.getScore());
                pkChallengeRecord.setCouponNum(challengeRecord.getCouponNum());
                String currencyCode = challengeRecord.getCurrencyCode();
                if (Objects.isNull(currencyCode)
                        || I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
                    pkChallengeRecord.setAward(challengeRecord.getAward());
                } else {
                    BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
                    BigDecimal award = I18nConstant.currencyFormat(currencyCode, challengeRecord.getAward().divide(nowRate, 2, RoundingMode.UP));
                    pkChallengeRecord.setAward(award);
                }
                BigDecimal award = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), pkChallengeRecord.getAward());
                winnerAward = award.toString();
            }
            // TODO i18n
            activityDetailVO.setAwardRule(I18nMsgUtils.getMessage("user.winner.reward") + ":" + fillAwardInfo(pkChallengeRecord, activityDetailVO.getCurrency()));
        }
        if (RunActivitySubTypeEnum.getNewPersonPkType().contains(activityEntity.getActivityTypeSub())) {
            winnerAward = fillAmount(activityEntity, currency.getCurrencyCode()).toString();
            // TODO i18n
            activityDetailVO.setAwardRule(I18nMsgUtils.getMessage("user.winner.reward") + ":" + currency.getCurrencySymbol() + fillAmount(activityEntity, currency.getCurrencyCode()));
        }
        activityDetailVO.setWinnerAward(winnerAward);
        if (Arrays.asList(1, 2).contains(activityEntity.getActivityTypeSub())) {
            Long activityId = activityEntity.getId();
            BigDecimal winnerAmountCurrency = awardConfigAmountCurrencyDataService.findAmountByActivityAndSendType(activityId, AwardSentTypeEnum.WINNER_AWARD.getType(), currency.getCurrencyCode());
            BigDecimal participationCurrency = awardConfigAmountCurrencyDataService.findAmountByActivityAndSendType(activityId, AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), currency.getCurrencyCode());
            //好友pk奖励重写
            if (Objects.equals(activityEntity.getActivityType(), 2)) {
                BigDecimal nowRate = null;
                if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currency.getCurrencyCode())) {
                    nowRate = BigDecimal.ONE;
                } else {
                    nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currency.getCurrencyCode()).getExchangeRate();
                }
                participationCurrency = getParticipationCurrency(activityEntity, participationCurrency);
                winnerAmountCurrency = getWinnerAmountCurrency(activityEntity, winnerAmountCurrency);

                participationCurrency = participationCurrency.multiply(nowRate).setScale(2, RoundingMode.UP);
                winnerAmountCurrency = winnerAmountCurrency.multiply(nowRate).setScale(2, RoundingMode.UP);
            }

            if (Objects.nonNull(winnerAmountCurrency)) {
                winnerAmountCurrency = I18nConstant.currencyFormat(currency.getCurrencyCode(), winnerAmountCurrency);
                activityDetailVO.setWinnerAward(winnerAmountCurrency.toString());
            }
            if (Objects.nonNull(participationCurrency)) {
                participationCurrency = I18nConstant.currencyFormat(currency.getCurrencyCode(), participationCurrency);
                activityDetailVO.setParticipateAward(participationCurrency.toString());
            }
        }
        //总的奖金金额放个人所得金额
        BigDecimal personalBonus = calculatePersonalBonus(activityEntity, 2, activityEntity.getActivityEntryFee());
        if (userEntity != null) {
            String currencyCode = userAccountService.getUserCurrency(userEntity.getId()).getCurrencyCode();
            personalBonus = I18nConstant.currencyFormat(currencyCode, personalBonus);
        }

        activityDetailVO.setActivityTotalBonus(personalBonus);
        activityDetailVO.setCurrency(currency);
    }

    private BigDecimal getWinnerAmountCurrency(ZnsRunActivityEntity activityEntity, BigDecimal winnerAmountCurrency) {

        String activityConfig = activityEntity.getActivityConfig();
        Map<String, String> map = JsonUtil.readValue(activityConfig);
        String str = map.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
        if (StringUtils.hasText(str)) {
            //活动挑战跑奖励， 2胜者奖励
            PKAward winnerAwardConfig = getPKAwardConfig(activityEntity, 2);
            if (Objects.nonNull(winnerAwardConfig)) {
                List<CurrencyAmount> amountList = winnerAwardConfig.getAmountList();
                if (!CollectionUtils.isEmpty(amountList)) {
                    for (CurrencyAmount currencyAmount : amountList) {
                        if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                            winnerAmountCurrency = currencyAmount.getAmount();
                        }
                    }
                }
            }
        }
        return winnerAmountCurrency;
    }

    private BigDecimal getParticipationCurrency(ZnsRunActivityEntity activityEntity, BigDecimal participationCurrency) {
        String activityConfig = activityEntity.getActivityConfig();
        Map<String, String> map = JsonUtil.readValue(activityConfig);
        String str = map.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
        if (StringUtils.hasText(str)) {
            //活动挑战跑奖励，1参与奖励
            PKAward participationAward = getPKAwardConfig(activityEntity, 1);
            if (Objects.nonNull(participationAward)) {
                List<CurrencyAmount> amountList = participationAward.getAmountList();
                if (!CollectionUtils.isEmpty(amountList)) {
                    for (CurrencyAmount currencyAmount : amountList) {
                        if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                            participationCurrency = currencyAmount.getAmount();
                        }
                    }
                }
            }
        }
        return participationCurrency;

    }

    private BigDecimal fillAmount(ZnsRunActivityEntity activityEntity, String currencyCode) {
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        Integer isFinishAward = MapUtils.getInteger(jsonObject, "isFinishAward");
        if (isFinishAward.equals(YesNoStatus.YES.getCode())) {
            UserGameAwardDto finishAward = JsonUtil.readValue(jsonObject.get("finishAward"), UserGameAwardDto.class);
            List<CurrencyAmount> amountList = finishAward.getAmountList();
            if (!CollectionUtils.isEmpty(amountList)) {
                CurrencyAmount currencyAmount = amountList.stream().filter(s -> s.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
                if (Objects.nonNull(currencyAmount)) {
                    if (Objects.nonNull(currencyAmount.getAmount())) {
                        return I18nConstant.currencyFormat(currencyAmount.getCurrencyCode(), currencyAmount.getAmount());
                    } else {
                        return BigDecimal.ZERO;
                    }
                }
            } else if (Objects.nonNull(finishAward.getAmount()) && finishAward.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                return I18nConstant.currencyFormat(currencyCode, finishAward.getAmount());
            }
        }
        return BigDecimal.ZERO;
    }

    @Override
    protected void wrapperRunActivityRules(ZnsRunActivityEntity runActivityEntity, Map<String, Object> jsonObject, Integer challengeRunType, Integer appVersion) {
        String awardRule = I18nMsgUtils.getMessage("activity.challenged.award.rule");

        BigDecimal priceProportion = MapUtil.getBigDecimal(jsonObject.get("priceProportion"));
        if (Objects.nonNull(priceProportion)) {
            String priceProportionStr = BigDecimalUtil.removeEndOfZero(BigDecimalUtil.multiply(priceProportion, new BigDecimal(100))).toString();
            awardRule = awardRule + "\n" + I18nMsgUtils.getMessage("activity.challenged.award.rule.append", priceProportionStr);
        }

        runActivityEntity.setAwardRule(awardRule);

        StringBuffer challengeBuffer = new StringBuffer();
        challengeBuffer.append("Both parties should enter the track within a limited time before it starts.\n" +
                "\n" +
                "Participants will run at the same time at the track.\n");
        challengeBuffer.append("\n");
        challengeBuffer.append("Rules:\n");
        challengeBuffer.append("1. If both sides finish the race at the same length, the one with less time wins" + "\n");
        challengeBuffer.append("2. If both sides finish the race at the same time, the one with more distance wins." + "\n");
        challengeBuffer.append("3. If only one side finishes the race, the user who finishes the race wins.\n");
        challengeBuffer.append("4. No winner if either party finishes the race.\n");

        challengeBuffer.append("Note: \n");
        challengeBuffer.append("1. You would not be able to return to the race, once you leave the track or exit the game.\n");
        challengeBuffer.append("2. You would not be able to return to the race, once you finish the whole race.\n");
        challengeBuffer.append("3. The race will end, once no participants are in.\n");
        challengeBuffer.append("4. Your activity pass will be returned if the race is canceled.\n");

        runActivityEntity.setChallengeRule(challengeBuffer.toString());
    }

    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {
        List<RunActivityUserVO> runActivityUsers = activityUserManager.findRunActivityUsers(activityEntity, null, userEntity.getId(), activityDetailVO, null, null, activityUserStatus);
        activityDetailVO.setActivityUsers(runActivityUsers);
        //判断是否是游客
        boolean present = runActivityUsers.stream().filter(u -> u.getIsMe() == 1).findAny().isPresent();
        if (!present) {
            activityDetailVO.setIsVisitor(1);
        }
    }

    @Override
    public Map<String, Object> getActivityConfig(RunActivityRequest runActivity, String activityConfig, Integer measureUnit, Long userId, boolean isAdd) {
        super.getActivityConfig(runActivity, activityConfig, measureUnit, userId, isAdd);
        Integer fiveMinutes = sysConfigService.selectActivityStartMinite(userId) + 1;
        jsonObjectConfig.put("challengeRunBeforeEnter", fiveMinutes);

        if (Objects.nonNull(runActivity.getAppVersion()) && runActivity.getAppVersion() < 3060) {
            jsonObjectConfig.remove(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
        }

        // TODO: 2023/7/18 2.6.0以后版本删掉
        if (runActivity.getIsRobotStart() == 1) {
            return jsonObjectConfig;
        } else {
            BigDecimal winnerAward = BigDecimal.ZERO;
            //计算完赛奖励
            String currencyCode = userAccountService.getUserAccount(userId).getCurrencyCode();
            BigDecimal nowRate = BigDecimal.ONE;
            if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
                nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
            }
            if (measureUnit == 0) {
                BigDecimal kms = runActivity.getRunMileage().divide(new BigDecimal(1000));
                BigDecimal completeAwardPerKm = MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.COMPLETE_AWARD_PER_KM), BigDecimal.ZERO);
                winnerAward = completeAwardPerKm.multiply(kms).multiply(nowRate);
            } else {
                BigDecimal miles = runActivity.getRunMileage().divide(new BigDecimal(1600));
                BigDecimal completeAwardPerMiles = MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.COMPLETE_AWARD_PER_MILES), BigDecimal.ZERO);
                winnerAward = completeAwardPerMiles.multiply(miles).multiply(nowRate);
            }
            BigDecimal maxAwardLimit = MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.MAX_AWARD_LIMIT), BigDecimal.ZERO);
            maxAwardLimit = maxAwardLimit.multiply(nowRate);
            if (maxAwardLimit.compareTo(BigDecimal.ZERO) > 0 && winnerAward.compareTo(maxAwardLimit) > 0) {
                winnerAward = maxAwardLimit;
            }
            if (Objects.nonNull(runActivity.getActivityTypeSub()) && runActivity.getActivityTypeSub() == 4) {
                //待ycx确认
                jsonObjectConfig.put("winnerAward", MapUtil.getBigDecimal(jsonObjectConfig.get("winAward"), BigDecimal.ZERO));
            } else {
                jsonObjectConfig.put("winnerAward", winnerAward);
            }
            //重写参与奖励，胜者奖励
            if (Objects.equals(runActivity.getActivityTypeSub(), 2)) {

                Object o = jsonObjectConfig.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
                if (Objects.nonNull(o)) {
                    if (StringUtils.hasText(o.toString())) {
                        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
                        activityEntity.setActivityConfig(activityConfig);
                        activityEntity.setCompleteRuleType(runActivity.getCompleteRuleType());
                        activityEntity.setRunMileage(runActivity.getRunMileage());
                        activityEntity.setActivityType(2);
                        activityEntity.setActivityTypeSub(2);
                        if (Objects.nonNull(runActivity.getRunTime())) {
                            activityEntity.setRunTime(runActivity.getRunTime().intValue());
                        }

                        FriendPkAward friendPkAward = PkFriendAwardUtil.getNewFriendPkAward(activityEntity);
                        BigDecimal winnerWard = friendPkAward.getWinnerAmount();
                        BigDecimal participateAward = friendPkAward.getParticipateAmount();

                        jsonObjectConfig.put(ApiConstants.PARTICIPATE_AWARD, participateAward);

                        jsonObjectConfig.put(ApiConstants.WINNER_AWARD, winnerWard);

                    }
                }
            }
            return jsonObjectConfig;
        }
    }

    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        if (Objects.isNull(activityEntity.getActivityStartTime())) {
            return null;
        }
        //活动开始后不可操作
        ZonedDateTime date = DateUtil.addSeconds(activityEntity.getActivityStartTime(), 10);
        if (ZonedDateTime.now().compareTo(date) > 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.timeOverDue")); //"You are not allowed to enter after the running time"
        }
        return null;
    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {
        log.info("handleUserActivityState 处理活动状态 =" + activityEntity.getId() + ", userStatus = " + userStatus);
//        String logNo = Logger.inheritableThreadLocalNo.get();
//        Long time = ch.qos.logback.classic.Logger.inheritableThreadLocalTime.get();

        // 挑战活动
        if (-1 == userStatus.intValue()) {
            // 取消挑战活动
            boolean canCancel = challengeRunActivityCanCancel(activityEntity, user);
            if (canCancel) {
                // 变更挑战跑活动为已取消
                runActivityProcessManager.updateActivityState(activityEntity, ActivityStateEnum.CANCELED, ActivitySubstateEnum.SPONSOR_CANCEL);
                // 取消活动退款
                runActivityProcessManager.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
            } else {
                return CommonResult.fail(ActivityError.ACTIVITY_CHALLENGE_NOT_CANCEL.getCode(), I18nMsgUtils.getMessage("activity.invitation.accepted")); //The invitation has been accepted and cannot be cancelled, CommonError.ACTIVITY_CHALLENGE_NOT_CANCEL.getMsg()
            }
            return CommonResult.success();
        } else if (1 == userStatus.intValue()) {
            Result result = canRefuseOrAcceptActivity(activityEntity, user);
            if (Objects.nonNull(result)) {
                return result;
            }
            // 支付保证金逻辑
            Result payResult = runActivityPayManager.handlePayRunActivity(activityEntity, user, password, request, false);
            if (null != payResult) {
                return payResult;
            }
            // 变更活动用户状态
            runActivityUserService.updateActivityUserState(activityEntity.getId(), user.getId(), ActivityUserStateEnum.ACCEPT);
            if (user.getIsRobot() == 1) {
                activityEntity.setHasRobot(1);
            }
            runActivityService.updateById(activityEntity);

        } else if (2 == userStatus.intValue()) {
            Result result = canRefuseOrAcceptActivity(activityEntity, user);
            if (Objects.nonNull(result)) {
                return result;
            }
            // 变更用户状态为已拒绝
            boolean updateResult = runActivityUserService.updateActivityUserState(activityEntity.getId(), user.getId(), ActivityUserStateEnum.REFUSED);
            if (false == updateResult) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.pkRun.updatedFailed"));//"Failed to update the user's reject PK Running activity status"
            }
            // 变更挑战跑活动为已取消
            runActivityProcessManager.updateActivityState(activityEntity, ActivityStateEnum.CANCELED, ActivitySubstateEnum.PARTICIPANT_CANCEL);
            // 取消活动退款
            runActivityProcessManager.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
        }

        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
//                    OrderUtil.addLogNo("child_"+logNo,time);
                    Thread.sleep(3000);
                    log.info("handleUserActivityState 消息回执处理 activityId = " + activityEntity.getId());
                    UserFriendMatchVo request = new UserFriendMatchVo();
                    //消息回执
                    UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(activityEntity.getId());
                    if (Objects.nonNull(userFriendMatch)) {

                        if (userFriendMatch.getUserId().equals(user.getId())) {
                            request.setUserId(user.getId());        //自已取消，不弹窗
                        }

                        request.setActivityId(activityEntity.getId());
                        request.setId(userFriendMatch.getId());
                        ZnsRunRouteEntity routeEntity = runRouteService.selectRunRouteById(activityEntity.getActivityRouteId());
                        request.setRouteType(routeEntity.getRouteType());
                        //   -1 拒绝， 1 同意
                        request.setStatus(Objects.equals(userStatus, 1) ? 1 : -1);
                        request.setActivityStartTime(activityEntity.getActivityStartTime());

                        // status , -1 拒绝， 1 同意
                        userFriendMatchService.approval(request);
                    }
                } catch (Exception e) {
                    log.error("handleUserActivityState 消息回执处理 " + activityEntity.getId(), e);
                }
            }
        });

        return CommonResult.success();
    }

    public Result checkHandleUserActivityState(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, Integer userStatus, ZnsUserEntity user) {
        if (activityEntity.getActivityState() == -1) {
            return CommonResult.fail(ActivityError.ACTIVITY_EXPIRED.getCode(), I18nMsgUtils.getMessage("activity.signup.timeNoStart"));
        }

        Result result = super.checkHandleUserActivityState(activityEntity, activityUser, userStatus, user);
        if (Objects.nonNull(result)) {
            return result;
        }

        if (-1 == userStatus.intValue()) {
            // 取消挑战活动
            boolean canCancel = challengeRunActivityCanCancel(activityEntity, user);
            if (!canCancel) {
                return CommonResult.fail(ActivityError.ACTIVITY_CHALLENGE_NOT_CANCEL.getCode(), I18nMsgUtils.getMessage("activity.invitation.accepted"));
            }
        } else {
            return canRefuseOrAcceptActivity(activityEntity, user);
        }
        return null;
    }

    /**
     * 挑战跑是否可以取消
     *
     * @param activityEntity
     * @param user
     * @return
     */
    private boolean challengeRunActivityCanCancel(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        // 活动状态=未开始 and 被挑战者参与状态=未答复 这两个条件都满足才能取消。并且只能是活动发起者才能取消

        // 查询发起挑战是不是当前用户
//        QueryWrapper<ZnsRunActivityUserEntity> wrapper1 = new QueryWrapper<>();
//        wrapper1.eq("is_delete", 0);
//        wrapper1.eq("activity_id", activityEntity.getId());
//        wrapper1.eq("user_type", 1);
//        wrapper1.eq("user_id", user.getId());
//        wrapper1.last("limit 1");
//        ZnsRunActivityUserEntity activityUser1 = runActivityUserService.getOne(wrapper1);
//        if (null == activityUser1) {
//            log.error("挑战赛只有活动发起者才能取消活动,活动id:" + activityEntity.getId() + ",取消用户ID:" + user.getId());
//            return false;
//        }
        ZonedDateTime activityStartTime = activityEntity.getActivityStartTime();
        // 查询被挑战者
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId())
                .userType(2).userId(user.getId())
                .build();

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findOne(userQuery);
        if (null != activityUser) {
            ZonedDateTime now = ZonedDateTime.now();
            // 活动状态=未开始 and 被挑战者参与状态=未答复
            // 如果对方没有答复并且当前时间小于活动开始时间，活动是可以被取消的。
            if (ActivityUserStateEnum.NO_REPLY.getState().equals(activityUser.getUserState()) && activityStartTime.isAfter(now)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return isNoReply(activityEntity.getActivityStartTime(), activityEntity.getId(), user.getId());
    }

    @Override
    public BigDecimal calculateTotalBonus(ZnsRunActivityEntity activity, Integer acceptCount, BigDecimal entryFee, Integer challengeRunType, ZnsUserEntity userEntity, Boolean view) {
        BigDecimal totalBonus = super.calculateTotalBonus(activity, acceptCount, entryFee, challengeRunType, userEntity, view);
        if (Objects.nonNull(totalBonus)) {
            return totalBonus;
        }
        String currencyCode = userAccountService.getUserCurrency(userEntity.getId()).getCurrencyCode();
        // 计算公式：平台补贴金额 * (已接受人数+1) + 单人保证金 * 已接受人数 + 胜者奖励金额
        String participateAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD));
        String winnerAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.WINNER_AWARD));
        BigDecimal participateDecimal = new BigDecimal(participateAward);
        BigDecimal winnerDecimal = new BigDecimal(winnerAward);
        BigDecimal totalAward = BigDecimal.ZERO;
        if (Objects.nonNull(challengeRunType) && challengeRunType == 1) {
            totalAward = totalAward.add(participateDecimal.multiply(acceptCountDecimal));
        } else {
            totalAward = totalAward.add(participateDecimal.multiply(BigDecimalUtil.add(acceptCount, 1)));
        }
        totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);

        if (activity.getBonusRuleType() != 1) {
            totalAward = totalAward.add(entryFee.multiply(acceptCountDecimal));
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }
        totalAward = totalAward.add(winnerDecimal);
        totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        return totalAward;
    }

    @Override
    public BigDecimal calculatePersonalBonus(ZnsRunActivityEntity activityEntity, Integer acceptCount, BigDecimal entryFee) {
        super.calculatePersonalBonus(activityEntity, acceptCount, entryFee);

        // 计算公式：平台补贴金额 * (已接受人数) + 单人保证金 * 已接受人数 + 胜者奖励金额
        String participateAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD));
        String winnerAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.WINNER_AWARD));
        BigDecimal participateDecimal = new BigDecimal(participateAward);
        BigDecimal winnerDecimal = new BigDecimal(winnerAward);
        BigDecimal totalAward = BigDecimal.ZERO;
        totalAward = totalAward.add(participateDecimal);
        if (Objects.nonNull(entryFee)) {
            totalAward = totalAward.add(entryFee.multiply(acceptCountDecimal));
        }
        totalAward = totalAward.add(winnerDecimal);
        return totalAward;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        //检查是否接受
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId()).userState(ActivityUserStateEnum.ACCEPT.getState())
                .build();
        long userCountAccept = runActivityUserService.findCount(userQuery);

        if (userCountAccept < 2) {
            activityEntity.setActivityState(ActivityStateEnum.CANCELED.getState());
            activityEntity.setSubState(ActivitySubstateEnum.TIMED_TASK_CANCEL.getState());
            // 取消活动退款
            runActivityProcessManager.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
            activityEntity.setModifieTime(ZonedDateTime.now());
            runActivityService.updateById(activityEntity);
            log.info("checkReportUserRun取消活动，活动id：" + activityEntity.getId());
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Activity cancelled");
        }

        // 挑战跑：活动开始时间+3秒的时候判断参与活动的用户是否处于跑步中的状态
        ZonedDateTime addSeconds = DateUtil.addSeconds(activityEntity.getActivityStartTime(), 10);
        if (addSeconds.isAfter(ZonedDateTime.now())) {
            return CommonResult.success();
        }


        RunActivityUserQuery userQuery1 = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId()).userState(ActivityUserStateEnum.RUNING.getState())
                .build();

        long userCount = runActivityUserService.findCount(userQuery1);

        if (userCount < 2) {
            activityEntity.setActivityState(ActivityStateEnum.CANCELED.getState());
            activityEntity.setSubState(ActivitySubstateEnum.TIMED_TASK_CANCEL.getState());
            // 取消活动退款
            runActivityProcessManager.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
            activityEntity.setModifieTime(ZonedDateTime.now());
            runActivityService.updateById(activityEntity);
            log.info("checkReportUserRun取消活动，活动id：" + activityEntity.getId());
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Activity cancelled");
        }
        return CommonResult.success();
    }

    @Override
    @Transactional
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        //判断结束的活动运动数据是否都结束
        boolean b = checkRunActivityDataDetailEnd(activityEntity.getId());
        if (!b) {
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},还有用户跑步未结束", activityEntity.getId());
            return;
        }
        super.handleRunActivityEnd(activityEntity);
        //结束未跑步结束的跑步数据
//        userRunDataDetailsService.endSportByActivityId(activityEntity);
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId())
                .build();
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findList(userQuery);

        for (ZnsRunActivityUserEntity activityUser : activityUsers) {
            if ((Objects.isNull(activityUser.getRunTimeMillisecond()) || activityUser.getRunTimeMillisecond() == 0) && activityUser.getRunTime() > 0) {
                activityUser.setRunTimeMillisecond(activityUser.getRunTime() * 1000 + 999);
            }
        }
        //查询跑步数据是否有任务
        List<Long> runDataDetailsIds = activityUsers.stream().map(ZnsRunActivityUserEntity::getRunDataDetailsId).collect(Collectors.toList());
        List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetailsEntities = null;
        if (!CollectionUtils.isEmpty(runDataDetailsIds)) {
            znsUserRunDataDetailsEntities = userRunDataDetailsService.selectBatchIds(runDataDetailsIds);
        }
        boolean hasUserTask = false;
        boolean isNewUserTask = false;
        RunActivityUserTask userTask = null;
        if (!CollectionUtils.isEmpty(znsUserRunDataDetailsEntities)) {
            ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsEntities.stream().filter(d -> d.getTaskId() > 0).findFirst().orElse(null);
            if (Objects.nonNull(znsUserRunDataDetailsEntity)) {
                hasUserTask = true;
                activityEntity.setTaskId(znsUserRunDataDetailsEntity.getTaskId());
                userTask = runActivityUserTaskService.findById(znsUserRunDataDetailsEntity.getTaskId());
                if (userTask.getActivityType() == 6) {
                    isNewUserTask = true;
                }
            }
        }
        //查询是否是新人挑战跑
        if (hasUserTask && isNewUserTask) {
            dealUserTask(activityEntity, activityUsers, userTask);
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},是新人挑战跑", activityEntity.getId());
            return;
        }

        // 获取对应的奖励配置
        BigDecimal participateAward = null;
        BigDecimal launchAward = null;
        BigDecimal winnerAward = null;
        BigDecimal offlinePkWinnerAward = BigDecimal.ZERO;
        //其他奖励发放
        List<AwardConfigDto> awardConfigDtoList = awardConfigBizService.selectAwardConfigDtoListByActivityId(activityEntity.getId(), null, null);

        Map<String, Object> jsonObject = new HashMap<>();

        if (StringUtils.hasText(activityEntity.getActivityConfig())) {
            jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
            if (null != jsonObject) {
                String participateAwardStr = MapUtil.getString(jsonObject.get(ApiConstants.PARTICIPATE_AWARD));          // 参与金额
                participateAward = new BigDecimal(participateAwardStr);
                launchAward = participateAward.add(participateAward);
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},配置中参与金额 participateAward={}", activityEntity.getId(), participateAward);
                String winnerAwardStr = MapUtil.getString(jsonObject.get(ApiConstants.WINNER_AWARD));
                winnerAward = new BigDecimal(winnerAwardStr);                   //      活动胜者奖励金额
                //只有pk和随机处理
                if (Objects.isNull(activityEntity.getActivityTypeSub()) || Arrays.asList(1, 2).contains(activityEntity.getActivityTypeSub())) {
                    participateAward = awardConfigBizService.selectAwardAmount(activityEntity.getId(), AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), participateAward);
                    launchAward = awardConfigBizService.selectAwardAmount(activityEntity.getId(), AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), launchAward);
                    winnerAward = awardConfigBizService.selectAwardAmount(activityEntity.getId(), AwardSentTypeEnum.WINNER_AWARD.getType(), winnerAward);
                }
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},配置中活动胜者奖励金额 winnerAward={}", activityEntity.getId(), winnerAward);
            }
        }
        if (null == participateAward || null == winnerAward) {
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},挑战跑活动配置参与奖励和完成奖励不存在", activityEntity.getId());
            return;
        }

        //好友pk奖励 V3.6
        if (Objects.equals(activityEntity.getActivityTypeSub(), 2)) {

            Map<String, String> map = JsonUtil.readValue(activityEntity.getActivityConfig());
            String s = map.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
            if (StringUtils.hasText(s)) {
                //重新构造awardConfigDtoList
                List<AwardConfigDto> redirectAwardConfigDtos = new ArrayList<>();
                winnerAward = BigDecimal.ZERO;
                participateAward = BigDecimal.ZERO;


                //活动挑战跑奖励，1参与奖励
                PKAward participationAward = getPKAwardConfig(activityEntity, 1);
                if (Objects.nonNull(participationAward)) {
                    if (Objects.nonNull(participationAward.getScore())) {
                        AwardConfigDto scoreAwardConfigDto = new AwardConfigDto();
                        scoreAwardConfigDto.setSendType(AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
                        scoreAwardConfigDto.setAwardType(AwardTypeEnum.SCORE.getType());
                        scoreAwardConfigDto.setScore(participationAward.getScore());
                        redirectAwardConfigDtos.add(scoreAwardConfigDto);
                    }
                    if (Objects.nonNull(participationAward.getCouponId())) {
                        AwardConfigDto couponAwardConfigDto = new AwardConfigDto();
                        couponAwardConfigDto.setAwardType(AwardTypeEnum.COUPON.getType());
                        couponAwardConfigDto.setSendType(AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
                        couponAwardConfigDto.setCouponIds(participationAward.getCouponId().toString());
                        redirectAwardConfigDtos.add(couponAwardConfigDto);
                    }

                    List<CurrencyAmount> amountList = participationAward.getAmountList();
                    if (!CollectionUtils.isEmpty(amountList)) {
                        for (CurrencyAmount currencyAmount : amountList) {
                            if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                                participateAward = currencyAmount.getAmount();
                            }
                        }
                    }
                }
                //活动挑战跑奖励， 2胜者奖励
                PKAward winnerAwardConfig = getPKAwardConfig(activityEntity, 2);
                if (Objects.nonNull(winnerAwardConfig)) {
                    if (Objects.nonNull(winnerAwardConfig.getScore())) {
                        AwardConfigDto scoreAwardConfigDto = new AwardConfigDto();
                        scoreAwardConfigDto.setSendType(AwardSentTypeEnum.WINNER_AWARD.getType());
                        scoreAwardConfigDto.setAwardType(AwardTypeEnum.SCORE.getType());
                        scoreAwardConfigDto.setScore(winnerAwardConfig.getScore());
                        redirectAwardConfigDtos.add(scoreAwardConfigDto);
                    }
                    if (Objects.nonNull(winnerAwardConfig.getCouponId())) {
                        AwardConfigDto couponAwardConfigDto = new AwardConfigDto();
                        couponAwardConfigDto.setAwardType(AwardTypeEnum.COUPON.getType());
                        couponAwardConfigDto.setSendType(AwardSentTypeEnum.WINNER_AWARD.getType());
                        couponAwardConfigDto.setCouponIds(winnerAwardConfig.getCouponId().toString());
                        redirectAwardConfigDtos.add(couponAwardConfigDto);
                    }
                    List<CurrencyAmount> amountList = winnerAwardConfig.getAmountList();
                    if (!CollectionUtils.isEmpty(amountList)) {
                        for (CurrencyAmount currencyAmount : amountList) {
                            if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                                winnerAward = currencyAmount.getAmount();
                            }
                        }
                    }
                }
                awardConfigDtoList = redirectAwardConfigDtos;

                //计算胜者金额
                //winnerAward = winnerAward.add(participateAward);

                launchAward = participateAward;
            }
        }

        if (!CollectionUtils.isEmpty(activityUsers) && activityUsers.size() == 2) {
            // 先获取两个用户
            ZnsRunActivityUserEntity userOne = activityUsers.get(0);
            ZnsRunActivityUserEntity userTwo = activityUsers.get(1);

            Map<Long, ZnsUserRunDataDetailsEntity> map = znsUserRunDataDetailsEntities.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsEntity::getUserId, Function.identity(), (k1, k2) -> k1));
            ZnsUserRunDataDetailsEntity oneDetailsEntity = map.get(activityUsers.get(0).getUserId());
            ZnsUserRunDataDetailsEntity twoDetailsEntity = map.get(activityUsers.get(1).getUserId());

            SpeedPropRecordQuery queryOne = SpeedPropRecordQuery.builder().userId(userOne.getUserId()).activityId(activityEntity.getId()).build();
            Integer speedValueOne = speedPropRecordService.querySpeedValue(queryOne);
            SpeedPropRecordQuery queryTwo = SpeedPropRecordQuery.builder().userId(userTwo.getUserId()).activityId(activityEntity.getId()).build();
            Integer speedValueTwo = speedPropRecordService.querySpeedValue(queryTwo);
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},道具影响值,speedOneUserId={},speedOneValue={},speedTwoUserId={},speedTwoValue={}", activityEntity.getId(), userOne.getUserId(), speedValueOne, userTwo.getUserId(), speedValueTwo);

            // 4.1 给胜者计算奖励金额并发放奖励
            boolean oneComplete = false;
            boolean twoComplete = false;
            boolean tieComplete = false;
            //优先使用detail表的数据
            BigDecimal runMileageOne = oneDetailsEntity == null ? userOne.getRunMileage() : oneDetailsEntity.getRunMileage();
            BigDecimal runMileageTwo = twoDetailsEntity == null ? userTwo.getRunMileage() : twoDetailsEntity.getRunMileage();
            Integer runTimeOne = oneDetailsEntity == null ? userOne.getRunTime() : oneDetailsEntity.getRunTime();
            Integer runTimeTwo = twoDetailsEntity == null ? userTwo.getRunTime() : twoDetailsEntity.getRunTime();
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},完成距离、时间,runMileageOne={},runMileageTwo={},runTimeOne={},runTimeTwo={}", activityEntity.getId(), runMileageOne, runMileageTwo, runTimeOne, runTimeTwo);
            // 完成规则类型：1表示完成跑步里程，2表示完成跑步时长
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},完成规则类型：1表示完成跑步里程，2表示完成跑步时长,completeRuleType={}", activityEntity.getId(), activityEntity.getCompleteRuleType());
            if (1 == activityEntity.getCompleteRuleType()) {
                if (runMileageOne.add(new BigDecimal(speedValueOne)).compareTo(activityEntity.getRunMileage()) >= 0) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},userOne.getRunMileage() 大于 activityEntity.getRunMileage(),userOne.userId={}", activityEntity.getId(), userOne.getUserId());
                    oneComplete = true;
                }
                if (runMileageTwo.add(new BigDecimal(speedValueTwo)).compareTo(activityEntity.getRunMileage()) >= 0) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},userTwo.getRunMileage() 大于 activityEntity.getRunMileage(),userTwo.userId={}", activityEntity.getId(), userTwo.getUserId());
                    twoComplete = true;
                }
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},距离判断,oneComplete={},twoComplete={}", activityEntity.getId(), oneComplete, twoComplete);
                if (oneComplete && twoComplete) {
                    if (userOne.getRunTimeMillisecond().compareTo(userTwo.getRunTimeMillisecond()) == 0) {
                        tieComplete = true;
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},距离判断,userOne.getRunTimeMillisecond() == userTwo.getRunTimeMillisecond()", activityEntity.getId());
                    }
                }
            } else if (2 == activityEntity.getCompleteRuleType()) {
                if (runTimeOne.compareTo(activityEntity.getRunTime()) >= 0) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},userOne.getRunTime() 大于 activityEntity.getRunTime(),userOne.userId={}", activityEntity.getId(), userOne.getUserId());
                    oneComplete = true;
                }
                if (runTimeTwo.compareTo(activityEntity.getRunTime()) >= 0) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},userTwo.getRunTime() 大于 activityEntity.getRunTime(),userTwo.userId={}", activityEntity.getId(), userOne.getUserId());
                    twoComplete = true;
                }
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},时间判断,oneComplete={},twoComplete={}", activityEntity.getId(), oneComplete, twoComplete);
                if (oneComplete && twoComplete) {
                    if (runMileageOne.compareTo(runMileageTwo) == 0) {
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},时间判断，跑步的距离相等", activityEntity.getId());
                        tieComplete = true;
                    }
                }
            }
            // 查找胜者和败者
            ZnsRunActivityUserEntity winnerUser = null;
            ZnsRunActivityUserEntity loserUser = null;
            if (false == oneComplete && false == twoComplete) {
                // 双方都没有完成,没有胜负
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都没有完成,没有胜负", activityEntity.getId());
                winnerUser = userOne;
                loserUser = userTwo;
            } else if (true == oneComplete && true == twoComplete) {
                if (tieComplete) {
                    // 双方都完成比赛并且是平局
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成比赛并且是平局", activityEntity.getId());
                    winnerUser = userOne;
                    loserUser = userTwo;
                } else {
                    // 双方都完成有胜负
                    // 1表示完成跑步里程，2表示完成跑步时长
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成有胜负", activityEntity.getId());
                    if (1 == activityEntity.getCompleteRuleType()) {
                        // 用时少者为胜
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},用时少者为胜", activityEntity.getId());
                        if (userOne.getRunTimeMillisecond().compareTo(userTwo.getRunTimeMillisecond()) > 0) {
                            winnerUser = userTwo;
                            loserUser = userOne;
                        } else if (userOne.getRunTimeMillisecond().compareTo(userTwo.getRunTimeMillisecond()) < 0) {
                            winnerUser = userOne;
                            loserUser = userTwo;
                        }
                    } else if (2 == activityEntity.getCompleteRuleType()) {
                        // 跑程长者为胜
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},跑程长者为胜", activityEntity.getId());
                        if (runMileageOne.compareTo(runMileageTwo) > 0) {
                            winnerUser = userOne;
                            loserUser = userTwo;
                        } else if (runMileageOne.compareTo(runMileageTwo) < 0) {
                            winnerUser = userTwo;
                            loserUser = userOne;
                        }
                    }
                }
            } else {
                // 一方完成，则完成目标者为胜
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},一方完成，则完成目标者为胜", activityEntity.getId());
                if (true == oneComplete) {
                    winnerUser = userOne;
                    loserUser = userTwo;
                } else if (true == twoComplete) {
                    winnerUser = userTwo;
                    loserUser = userOne;
                }
            }
            if (null == winnerUser || null == loserUser) {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},一方完成，挑战赛活动未找到胜者和败者", activityEntity.getId());
                return;
            }
            // 三种情况计算胜者/失败者奖励
            BigDecimal winnerTotalBonus = BigDecimal.ZERO;
            BigDecimal loserTotalBonus = BigDecimal.ZERO;
            List<UserAccountDetailSub> winnerDetailSubs = new ArrayList<>();
            List<UserAccountDetailSub> loserDetailSubs = new ArrayList<>();
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},三种情况计算胜者/失败者奖励,oneComplete={},twoComplete={},tieComplete={}", activityEntity.getId(), oneComplete, twoComplete, tieComplete);

            BigDecimal surplus = BigDecimal.ONE;
            //只有好友PK才涉及赛事抽佣
            if (Objects.equals(activityEntity.getActivityTypeSub(), 2)) {
                //抽佣比例
                BigDecimal priceProportion = BigDecimal.ZERO;
                if (Objects.nonNull(jsonObject.get("priceProportion")) && StringUtils.hasText(jsonObject.get("priceProportion").toString())) {
                    priceProportion = new BigDecimal(jsonObject.get("priceProportion").toString());
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},设置佣金比例={}", activityEntity.getId(), priceProportion);
                }
                //可给用户分配的剩余比例
                surplus = BigDecimal.ONE.subtract(priceProportion);
            }
            //转美刀实时汇率
            BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), I18nConstant.CurrencyCodeEnum.CAD.getCode()).getExchangeRate();
            //费用参与不退回(补充：抽佣)
            BigDecimal totalActivityEntryFee = BigDecimal.ZERO;
            List<ActivityEntryFee> entryFees = activityEntryFeeService.findByActivityId(activityEntity.getId());
            String winCurrencyCode = userAccountService.getUserCurrency(winnerUser.getUserId()).getCurrencyCode();
            String loserCurrencyCode = userAccountService.getUserCurrency(loserUser.getUserId()).getCurrencyCode();
            //费用参与不退回(补充：抽佣)
            if (Objects.equals(activityEntity.getBonusRuleType(), 2)) {
                if (!CollectionUtils.isEmpty(entryFees)) {
                    BigDecimal winnerEntryFee = entryFees.stream().filter(s -> s.getCurrencyCode().equals(winCurrencyCode)).findFirst().get().getEntryFee();
                    BigDecimal loserEntryFee = entryFees.stream().filter(s -> s.getCurrencyCode().equals(loserCurrencyCode)).findFirst().get().getEntryFee();
                    totalActivityEntryFee = getUSDAmountByUser(winnerEntryFee, winCurrencyCode).add(getUSDAmountByUser(loserEntryFee, loserCurrencyCode)).multiply(surplus);
                }
            }
            List<Integer> newPkSubType = RunActivitySubTypeEnum.getNewPersonPkType();
            //瓜分保证金
            BigDecimal averageActivityEntryFee = totalActivityEntryFee.divide(new BigDecimal(activityUsers.size()), 4, RoundingMode.UP);
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},总参与费用={},平均瓜分费用={}", activityEntity.getId(), totalActivityEntryFee, averageActivityEntryFee);
            if (false == oneComplete && false == twoComplete) {
                // 双方都没有完成,没有胜负。只去判断是否发放参与奖
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都没有完成,没有胜负。两人分的金额一样多,不发放保证金", activityEntity.getId());
            } else if (true == oneComplete && true == twoComplete && tieComplete == true) {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都没有完成,没有胜负。双方都完成比赛并且是平局.两个分的金额一样多", activityEntity.getId());
                // 双方都完成比赛并且是平局.两个分的金额一样多
                if (2 == activityEntity.getBonusRuleType()) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成比赛并且是平局.两个分的金额一样多 activityEntity.getBonusRuleType()={}", activityEntity.getId(), activityEntity.getBonusRuleType());
                    // 保证金参与
                    winnerTotalBonus = winnerTotalBonus.add(averageActivityEntryFee);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成比赛并且是平局.两个分的金额一样多 winnerTotalBonus={}", activityEntity.getId(), winnerTotalBonus);
                    userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 1, exchangeAmountByUser(winnerUser.getUserId(), averageActivityEntryFee), BigDecimal.ZERO);
                    userAccountDetailSubService.addDetailSubToList(loserDetailSubs, loserUser.getUserId(), null, loserUser.getActivityId(), 1, exchangeAmountByUser(loserUser.getUserId(), averageActivityEntryFee), BigDecimal.ZERO);
                }

                BigDecimal halfWinnerAward = winnerAward.divide(BigDecimal.valueOf(2));
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成比赛并且是平局 halfWinnerAward={}", activityEntity.getId(), halfWinnerAward);
                if (winnerUser.getIsCheat() == 0) {
                    userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 6, exchangeAmountByUser(winnerUser.getUserId(), halfWinnerAward), BigDecimal.ZERO);
                    winnerTotalBonus = winnerTotalBonus.add(halfWinnerAward);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成比赛并且是平局 winnerUser 没有作弊 winnerTotalBonus={}", activityEntity.getId(), winnerTotalBonus);
                }
                if (loserUser.getIsCheat() == 0) {
                    userAccountDetailSubService.addDetailSubToList(loserDetailSubs, loserUser.getUserId(), null, loserUser.getActivityId(), 6, exchangeAmountByUser(loserUser.getUserId(), halfWinnerAward), BigDecimal.ZERO);
                    loserTotalBonus = winnerTotalBonus;
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成比赛并且是平局 loserUser 没有作弊，loserTotalBonus={}", activityEntity.getId(), loserTotalBonus);
                }
            } else {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},一定有一个人输， 或一个人赢", activityEntity.getId());
                if (Objects.equals(activityEntity.getActivityTypeSub(), 3) && Objects.equals(activityEntity.getIsNewPk(), YesNoStatus.NO.getCode())) {
                    // 【如果挑战者没有跑完 。 则不给他发送奖励】
                    PkChallengeRecord pkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 1);
                    if (Objects.nonNull(pkChallengeRecord)) {
                        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserDao.selectByActivityIdUserId(pkChallengeRecord.getActivityId(), pkChallengeRecord.getUserId());
                        PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(activityEntity.getId()).userId(winnerUser.getUserId()).build();
                        PkChallengeRecord winPkChallengeRecord = pkChallengeRecordService.findByQuery(query);
                        log.info("id = " + znsRunActivityUserEntity.getId() + ", znsRunActivityUserEntity.getRunMileage() " + znsRunActivityUserEntity.getRunMileage()
                                + ", award = " + winPkChallengeRecord.getAward());
                        if (znsRunActivityUserEntity.getRunMileage().compareTo(new BigDecimal(Constants.target_1_Mile)) >= 0) {
                            offlinePkWinnerAward = winPkChallengeRecord.getAward();
                            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},获得金额 为 offlinePkWinnerAward={}", activityEntity.getId(), offlinePkWinnerAward);
                        }
                    }
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},离线pk奖励 offlinePkWinnerAward={}", activityEntity.getId(), offlinePkWinnerAward);
                } else if (newPkSubType.contains(activityEntity.getActivityTypeSub())) {
                    jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
                    //winAward就是0，兼容旧版本
                    offlinePkWinnerAward = MapUtil.getBigDecimal(jsonObject.get("winAward"));
                    Integer isFinishAward = MapUtil.getInteger(jsonObject.get("isFinishAward"));
                    if (isFinishAward.equals(YesNoStatus.YES.getCode())) {
                        UserGameAwardDto finishAward = JsonUtil.readValue(jsonObject.get("finishAward"), UserGameAwardDto.class);
                        if (Objects.nonNull(finishAward)) {
                            List<CurrencyAmount> amountList = finishAward.getAmountList();
                            if (!CollectionUtils.isEmpty(amountList)) {
                                offlinePkWinnerAward = amountList.stream().filter(s -> s.getCurrencyCode().equals(winCurrencyCode)).findFirst().get().getAmount();
                                // todo 兼容数据有问题的
                                if (Objects.isNull(offlinePkWinnerAward)) {
                                    offlinePkWinnerAward = BigDecimal.ZERO;
                                }
                            } else if (Objects.nonNull(finishAward.getAmount()) && finishAward.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                                offlinePkWinnerAward = finishAward.getAmount();
                            }
                        }
                    }
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},新人引导pk奖励 offlinePkWinnerAward={}", activityEntity.getId(), offlinePkWinnerAward);
                } else {
                    // 按照胜负来分奖励
                    if (2 == activityEntity.getBonusRuleType()) {
                        // 保证金参与
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},保证金参与 doubleEntryFee={}", activityEntity.getId(), totalActivityEntryFee);
                        winnerTotalBonus = winnerTotalBonus.add(totalActivityEntryFee);
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},保证金参与 winnerTotalBonus={}", activityEntity.getId(), winnerTotalBonus);
                        userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 1, exchangeAmountByUser(winnerUser.getUserId(), totalActivityEntryFee), BigDecimal.ZERO);
                    }
                    if (winnerUser.getIsCheat() == 0) {
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},没有作弊 winnerAward={}", activityEntity.getId(), winnerAward);
                        winnerTotalBonus = winnerTotalBonus.add(winnerAward);
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},没有作弊 winnerTotalBonus={}", activityEntity.getId(), winnerTotalBonus);
                        userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 6, exchangeAmountByUser(winnerUser.getUserId(), winnerAward), BigDecimal.ZERO);
                    }

                }

            }

            // 判断用户是否跑过,跑过才能获得参与补贴
            boolean winnerDidRun = userRunDataDetailsService.verifyUserDidRun(winnerUser.getRunDataDetailsId(), winnerUser.getUserId());
            boolean loserDidRun = userRunDataDetailsService.verifyUserDidRun(loserUser.getRunDataDetailsId(), loserUser.getUserId());
            if (Objects.equals(activityEntity.getActivityTypeSub(), 3)) {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},如果是离线pk ，失败者一定是得到0元", activityEntity.getId());
                loserDidRun = false;
            }
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},判断用户是否跑过,跑过才能获得参与补贴 winnerDidRun ={},loserDidRun={}", activityEntity.getId(), winnerDidRun, loserDidRun);
            // 用户参与补贴
            if (winnerDidRun) {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},用户参与补贴 winnerTotalBonus={}", activityEntity.getId(), winnerTotalBonus);
                // 发起者可获得双倍参与补贴
                if (1 == winnerUser.getUserType()) {        // 用户类型：1表示活动发起人，2表示活动参与者
                    winnerTotalBonus = winnerTotalBonus.add(launchAward);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},用户类型：1表示活动发起人 winnerTotalBonus ={}", activityEntity.getId(), winnerTotalBonus);
                    userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 3, exchangeAmountByUser(winnerUser.getUserId(), participateAward), BigDecimal.ZERO);
                } else {
                    winnerTotalBonus = winnerTotalBonus.add(participateAward);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},用户参与补贴 participateAward={},winnerTotalBonus={}", activityEntity.getId(), participateAward, winnerTotalBonus);
                    userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 2, exchangeAmountByUser(winnerUser.getUserId(), participateAward), BigDecimal.ZERO);
                }
            }
            if (loserDidRun) {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},loserDidRun 失败者before={},participateAward={}", activityEntity.getId(), loserDidRun, participateAward);
                if (1 == loserUser.getUserType()) {  // 用户类型：1表示活动发起人，2表示活动参与者
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},loserDidRun 失败者是发起者before={},loserTotalBonus={}", activityEntity.getId(), loserDidRun, loserTotalBonus);
                    loserTotalBonus = loserTotalBonus.add(launchAward);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},loserDidRun 失败者是发起者after={},loserTotalBonus={}", activityEntity.getId(), loserDidRun, loserTotalBonus);
                    userAccountDetailSubService.addDetailSubToList(loserDetailSubs, loserUser.getUserId(), null, loserUser.getActivityId(), 3, exchangeAmountByUser(winnerUser.getUserId(), participateAward), BigDecimal.ZERO);
                } else {
                    loserTotalBonus = loserTotalBonus.add(participateAward);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},loserDidRun 失败者是发起者after={},loserTotalBonus={}", activityEntity.getId(), loserDidRun, loserTotalBonus);
                    userAccountDetailSubService.addDetailSubToList(loserDetailSubs, loserUser.getUserId(), null, loserUser.getActivityId(), 2, exchangeAmountByUser(winnerUser.getUserId(), participateAward), BigDecimal.ZERO);
                }

            }

            if (RunActivitySubTypeEnum.getPkType().contains(activityEntity.getActivityTypeSub())) {
                Long winnerDetailId = handleRunAward(offlinePkWinnerAward, winnerUser, activityEntity, BigDecimal.ZERO, null);
                winnerTotalBonus = offlinePkWinnerAward;
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},ActivityTypeSub in (3,4) winnerDetailId ={},offlinePkWinnerAward={},winnerTotalBonus={}", activityEntity.getId(), winnerDetailId, offlinePkWinnerAward, winnerTotalBonus);
                userAccountDetailSubService.addDetailSubToList(winnerDetailSubs, winnerUser.getUserId(), null, winnerUser.getActivityId(), 4, exchangeAmountByUser(winnerUser.getUserId(), offlinePkWinnerAward), BigDecimal.ZERO);
                addAccountDetailSubs(winnerDetailSubs, winnerDetailId);
            } else {
                // 计算好奖励金额给完成用户发放奖励,已经挑战跑的用户排名，并且添加对应账户明细
                //修改货架币种切换处理
                List<String> winnerUserActivityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + winnerUser.getUserId(), 0, -1);
                if (!CollectionUtils.isEmpty(winnerUserActivityIds) && winnerUserActivityIds.contains(winnerUser.getActivityId().toString())) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},币种切换不发放", activityEntity.getId());
                    winnerTotalBonus = BigDecimal.ZERO;
                } else {
                    Long winnerDetailId = handleRunAward(exchangeAmountByUser(winnerUser.getUserId(), winnerTotalBonus), winnerUser, activityEntity, BigDecimal.ZERO, null);
                    addAccountDetailSubs(winnerDetailSubs, winnerDetailId);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},before 计算好奖励金额给完成用户发放奖励,已经挑战跑的用户排名，并且添加对应账户明细 winnerTotalBonus ={},winnerTotalBonus={}", activityEntity.getId(), winnerTotalBonus, winnerTotalBonus);
                }

                List<String> loserUserActivityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + loserUser.getUserId(), 0, -1);
                if (!CollectionUtils.isEmpty(loserUserActivityIds) && loserUserActivityIds.contains(loserUser.getActivityId().toString())) {
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},币种切换不发放", activityEntity.getId());
                    loserTotalBonus = BigDecimal.ZERO;
                } else {
                    Long loserDetailId = handleRunAward(exchangeAmountByUser(loserUser.getUserId(), loserTotalBonus), loserUser, activityEntity, BigDecimal.ZERO, null);
                    addAccountDetailSubs(loserDetailSubs, loserDetailId);
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},after 计算好奖励金额给完成用户发放奖励,已经挑战跑的用户排名，并且添加对应账户明细 loserTotalBonus ={},winnerTotalBonus={}", activityEntity.getId(), loserTotalBonus, winnerTotalBonus);
                }
            }

            if (tieComplete) {
                // 双方都完成并且是平局,则活动用户中的rank要设置成0
                winnerUser.setRank(0);
                loserUser.setRank(0);
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},双方都完成并且是平局,则活动用户中的rank要设置成0", activityEntity.getId());
            } else if (oneComplete || twoComplete) {
                // 只要有一方完成了,就有胜负。第一名这rank设置成1,第二名则rank设置成2
                winnerUser.setRank(1);
                loserUser.setRank(2);
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},只要有一方完成了,就有胜负。第一名这rank设置成1,第二名则rank设置成2", activityEntity.getId());
            }
            winnerUser.setRunAward(exchangeAmountByUser(winnerUser.getUserId(), winnerTotalBonus));
            loserUser.setRunAward(exchangeAmountByUser(loserUser.getUserId(), loserTotalBonus));
            winnerTotalBonus = I18nConstant.currencyFormat(winCurrencyCode, winnerTotalBonus, RoundingMode.CEILING);
            loserTotalBonus = I18nConstant.currencyFormat(loserCurrencyCode, loserTotalBonus, RoundingMode.CEILING);
            if (RunActivitySubTypeEnum.getPkType().contains(activityEntity.getActivityTypeSub())) {
                winnerUser.setRunAward(winnerTotalBonus);
                loserUser.setRunAward(loserTotalBonus);
            }
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},最后 winnerTotalBonus={},loserTotalBonus={},", activityEntity.getId(), exchangeAmountByUser(winnerUser.getUserId(), winnerTotalBonus), exchangeAmountByUser(loserUser.getUserId(), loserTotalBonus));
            if (winnerUser.getId().equals(userOne.getId())) {
                if (oneComplete) {
                    winnerUser.setIsComplete(1);
                    winnerUser.setCompleteTime(ZonedDateTime.now());
                } else {
                    winnerUser.setIsComplete(0);
                }
                if (twoComplete) {
                    loserUser.setIsComplete(1);
                    loserUser.setCompleteTime(ZonedDateTime.now());
                } else {
                    loserUser.setIsComplete(0);
                }
            } else {
                if (twoComplete) {
                    winnerUser.setCompleteTime(ZonedDateTime.now());
                    winnerUser.setIsComplete(1);
                } else {
                    winnerUser.setIsComplete(0);
                }
                if (oneComplete) {
                    loserUser.setCompleteTime(ZonedDateTime.now());
                    loserUser.setIsComplete(1);
                } else {
                    loserUser.setIsComplete(0);
                }
            }

            if (winnerUser.getIsComplete() == 1) {
                winnerUser.setSubState(1);
            } else {
                boolean verifyUserDidRun = userRunDataDetailsService.verifyUserDidRun(winnerUser.getRunDataDetailsId(), winnerUser.getUserId());
                if (verifyUserDidRun) {
                    winnerUser.setSubState(2);
                }
            }
            if (loserUser.getIsComplete() == 1) {
                loserUser.setSubState(1);
            } else {
                boolean verifyUserDidRun = userRunDataDetailsService.verifyUserDidRun(loserUser.getRunDataDetailsId(), loserUser.getUserId());
                if (verifyUserDidRun) {
                    loserUser.setSubState(2);
                }
            }

            winnerUser.setRewardTime(ZonedDateTime.now());
            loserUser.setRewardTime(ZonedDateTime.now());

            runActivityUserService.updateById(winnerUser);
            runActivityUserService.updateById(loserUser);
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},更新ActivityUser", activityEntity.getId());

            if (Objects.equals(activityEntity.getActivityTypeSub(), 3)) {
                awardNotificationOffPk(offlinePkWinnerAward, activityEntity, winnerUser);
            } else {
                // 4.3.0时跟阿谷确认pk赛只有离线pk2-3
                awardNotification(winnerUser, activityEntity);
                awardNotification(loserUser, activityEntity);
            }
            if (Objects.isNull(activityEntity.getActivityTypeSub()) || Arrays.asList(1, 2).contains(activityEntity.getActivityTypeSub())) {
                //积分/券处理
                if (winnerUser.getRunTime() >= 60) {
                    //积分一起发放
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},发放胜者积分", activityEntity.getId());
                    rewardDistributionScore(awardConfigDtoList, winnerUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), AwardSentTypeEnum.WINNER_AWARD.getType()), null);
                    rewardDistribution(awardConfigDtoList, winnerUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), AwardSentTypeEnum.WINNER_AWARD.getType()));
                }
                if (loserUser.getRunTime() >= 60) {
                    //积分一起发放
                    log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},发放败者积分", activityEntity.getId());
                    rewardDistributionScore(awardConfigDtoList, loserUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), AwardSentTypeEnum.WINNER_AWARD.getType()), null);
                    rewardDistribution(awardConfigDtoList, loserUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), AwardSentTypeEnum.WINNER_AWARD.getType()));
                }
            }
            if (RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType().equals(activityEntity.getActivityTypeSub())) {
                // 离线pk 积分和优惠券奖励发放,新离线没有奖励
                if (winnerUser.getRunTime() >= 60 && Objects.equals(activityEntity.getIsNewPk(), YesNoStatus.NO.getCode())) {
                    PkChallengeRecord pkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 1);
                    if (pkChallengeRecord.getScore() > 0) {
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},离线pk发积分", activityEntity.getId());
                        activityUserScoreService.increaseAmount(pkChallengeRecord.getScore(), winnerUser.getActivityId(), winnerUser.getUserId(), winnerUser.getRank(), 0, ScoreConstant.SourceTypeEnum.source_type_14.getType());
                    }
                    if (pkChallengeRecord.getCouponNum() > 0) {
                        // 需求说明优惠券最多有一张
                        log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},离线pk发优惠券", activityEntity.getId());
                        userCouponBizService.sendUserCouponSource(pkChallengeRecord.getCouponId(), winnerUser.getUserId(), winnerUser.getActivityId(), CouponConstant.SourceTypeEnum.source_type_6.getType(), false);
                    }
                }
            }
            // 新人pk
            if (RunActivitySubTypeEnum.getNewPersonPkType().contains(activityEntity.getActivityTypeSub())) {
                log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},处理新人pk奖励", activityEntity.getId());
                if (oneComplete || twoComplete) {
                    Integer isFinishAward = MapUtil.getInteger(jsonObject.get("isFinishAward"));
                    if (isFinishAward.equals(YesNoStatus.YES.getCode())) {
                        UserGameAwardDto finishAward = JsonUtil.readValue(jsonObject.get("finishAward"), UserGameAwardDto.class);
                        if (Objects.nonNull(finishAward.getScore()) && finishAward.getScore() > 0) {
                            Integer score = finishAward.getScore();
                            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},发放新人pk积分={},userId={}", activityEntity.getId(), score, winnerUser.getUserId());
                            activityUserScoreService.increaseAmount(score, winnerUser.getActivityId(), winnerUser.getUserId(), 1, 0, ScoreConstant.SourceTypeEnum.source_type_24.getType());
                        }
                        if (Objects.nonNull(finishAward.getCouponAwardDto())) {
                            CouponAwardDto awardDto = finishAward.getCouponAwardDto();
                            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},发放新人pk优惠券={},userId={}", activityEntity.getId(), awardDto.getCouponId(), winnerUser.getUserId());
                            userCouponBizService.sendUserCouponSource(awardDto.getCouponId(), winnerUser.getUserId(), winnerUser.getActivityId(), CouponConstant.SourceTypeEnum.source_type_6.getType(), false);
                        }
                        if (!CollectionUtils.isEmpty(finishAward.getWears())) {
                            for (WearAwardDto wear : finishAward.getWears()) {
                                userWearsBagService.sendUserWear(winnerUser.getUserId(), wear, winnerUser.getActivityId());
                            }

                        }
                    }
                }
            }
        } else {
            log.info("[ChallengeRun][handleRunActivityEnd]---处理活动结束,活动={},挑战赛活动用户不存在或者活动用户数不等于2\"", activityEntity.getId());
        }

        super.handleRunActivityData(activityEntity);
    }

    //1参与奖励 2胜者奖励
    private PKAward getPKAwardConfig(ZnsRunActivityEntity activityEntity, int i) {

        Map<String, Object> jsonObject = new HashMap<>();
        if (StringUtil.isEmpty(activityEntity.getActivityConfig())) {
            return null;
        }
        jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());

        PKAward pkAward = null;
        Integer completeRuleType = activityEntity.getCompleteRuleType();
        Object o = jsonObject.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
        if (Objects.nonNull(o)) {
            FriendPKAwardConfig friendPKAwardConfig = JsonUtil.readValue(o, FriendPKAwardConfig.class);

            //里程
            if (completeRuleType == 1) {
                //里程奖励
                pkAward = processPKAward(friendPKAwardConfig.getMileagePKAwardConfig(), activityEntity.getRunMileage().intValue(), i);
            } else if (completeRuleType == 2) {
                //时间奖励
                pkAward = processPKAward(friendPKAwardConfig.getTimePKAwardConfig(), activityEntity.getRunTime() / 60, i);
            }
        }
        return pkAward;
    }

    private PKAward processPKAward(PKAwardConfig mileagePKAwardConfig, Integer activityTarget, int i) {
        PKAward pkAward = null;
        //统一奖励
        SpecificPKAward unifiedAward = mileagePKAwardConfig.getUnifiedAward();
        if (Objects.nonNull(unifiedAward)) {
            if (i == 1) {
                //参与奖励
                pkAward = unifiedAward.getParticipationAward();
            } else if (i == 2) {
                //胜者奖励
                pkAward = unifiedAward.getWinnerAward();
            }

        } else {
            //分段奖励 与统一奖励互斥
            SegmentAward segmentAward = mileagePKAwardConfig.getSegmentAward();

            Integer start = segmentAward.getStart();
            Integer segment = segmentAward.getSegment();

            //倍数
            if (activityTarget < start) {
                return null;
            }
            //倍数
            int multiplier = (activityTarget - start) / segment + 1;
            SpecificPKAward specificPKAward = segmentAward.getAward();
            if (Objects.nonNull(specificPKAward)) {
                //参与奖励
                PKAward participationAward = specificPKAward.getParticipationAward();
                if (Objects.nonNull(participationAward)) {
                    List<CurrencyAmount> amountList = participationAward.getAmountList();
                    if (!CollectionUtils.isEmpty(amountList)) {
                        CurrencyAmount currencyAmount = amountList.stream().filter(k -> I18nConstant.CurrencyCodeEnum.USD.getCode().equals(k.getCurrencyCode()))
                                .findFirst().get();
                        if (Objects.isNull(currencyAmount.getAmount())) {
                            amountList.clear();
                        } else {
                            currencyAmount.setAmount(currencyAmount.getAmount().multiply(BigDecimal.valueOf(multiplier)));
                        }
                    }
                    Integer score = participationAward.getScore();
                    if (Objects.nonNull(score)) {
                        participationAward.setScore(score * multiplier);
                    }
                }
                //胜者奖励
                PKAward winnerAward = specificPKAward.getWinnerAward();
                if (Objects.nonNull(winnerAward)) {
                    List<CurrencyAmount> amountList = winnerAward.getAmountList();
                    if (!CollectionUtils.isEmpty(amountList)) {
                        CurrencyAmount currencyAmount = amountList.stream().filter(k -> I18nConstant.CurrencyCodeEnum.USD.getCode().equals(k.getCurrencyCode()))
                                .findFirst().get();
                        if (Objects.isNull(currencyAmount.getAmount())) {
                            amountList.clear();
                        } else {
                            currencyAmount.setAmount(currencyAmount.getAmount().multiply(BigDecimal.valueOf(multiplier)));
                        }
                    }
                    Integer score = winnerAward.getScore();
                    if (Objects.nonNull(score)) {
                        winnerAward.setScore(score * multiplier);
                    }
                }
                if (i == 1) {
                    pkAward = participationAward;
                } else if (i == 2) {
                    pkAward = winnerAward;
                }
            }
        }
        return pkAward;
    }

    //美元amount
    private BigDecimal exchangeAmountByUser(Long userId, BigDecimal amount) {
        String currencyCode = userAccountService.getUserAccount(userId).getCurrencyCode();
        if (!currencyCode.equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
            BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
            // 跟产品确认最终值保留2位向上取
            amount = amount.multiply(nowRate).setScale(2, RoundingMode.UP);
            return I18nConstant.currencyFormat(currencyCode, amount);
        }
        return amount.setScale(2, RoundingMode.UP);

    }

    private BigDecimal getUSDAmountByUser(BigDecimal amount, String currencyCode) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return amount;
        }
        if (currencyCode.equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
            return amount.setScale(4, RoundingMode.UP);
        }
        BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
        return amount.divide(nowRate, 4, RoundingMode.UP);
    }


    private void addAccountDetailSubs(List<UserAccountDetailSub> detailSubs, Long detailId) {
        if (CollectionUtils.isEmpty(detailSubs)) {
            return;
        }
        for (UserAccountDetailSub detailSub : detailSubs) {
            detailSub.setDetailId(detailId);
        }
        userAccountDetailSubService.saveBatch(detailSubs);
    }

    private void dealUserTask(ZnsRunActivityEntity activityEntity, List<ZnsRunActivityUserEntity> activityUsers, RunActivityUserTask task) {
        log.info("dealUserTask 新人竞技跑处理开始");

        if (Objects.isNull(task)) {
            return;
        }
        ZnsRunActivityUserEntity oneSelf = null;
        ZnsUserAccountEntity oneAccount = null;
        ZnsRunActivityUserEntity other = null;
        ZnsUserAccountEntity otherAccount = null;
        for (ZnsRunActivityUserEntity activityUser : activityUsers) {
            ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(task.getUserId());
            if (activityUser.getUserId().equals(task.getUserId())) {
                oneSelf = activityUser;
                oneAccount = accountEntity;
            } else {
                other = activityUser;
                otherAccount = accountEntity;
            }
        }
        //不足一分钟不算
        if (oneSelf.getRunTime() < 60) {
            runActivityService.deleteActivity(activityEntity.getId());
            if (task.getStatus() == 3) {
                runActivityUserTaskService.resetStatus(task.getId());
            }
            return;
        }

        // 完成规则类型：1表示完成跑步里程，2表示完成跑步时长
        if (1 == activityEntity.getCompleteRuleType()) {
            if (oneSelf.getRunMileage().compareTo(activityEntity.getRunMileage()) >= 0) {
                oneSelf.setCompleteTime(ZonedDateTime.now());
                oneSelf.setIsComplete(1);
            }
            if (other.getRunMileage().compareTo(activityEntity.getRunMileage()) >= 0) {
                other.setIsComplete(1);
                other.setCompleteTime(ZonedDateTime.now());
            }
            if (oneSelf.getIsComplete() == 1 && other.getIsComplete() == 1) {
                if (oneSelf.getRunTimeMillisecond() < other.getRunTimeMillisecond()) {
                    oneSelf.setRank(1);
                    other.setRank(2);
                } else if (oneSelf.getRunTimeMillisecond().equals(other.getRunTimeMillisecond())) {
                    oneSelf.setRank(0);
                    other.setRank(0);
                } else {
                    oneSelf.setRank(2);
                    other.setRank(1);
                }
            }
        } else if (2 == activityEntity.getCompleteRuleType()) {
            if (oneSelf.getRunTime().compareTo(activityEntity.getRunTime()) >= 0) {
                oneSelf.setIsComplete(1);
                oneSelf.setCompleteTime(ZonedDateTime.now());
            }
            if (other.getRunTime().compareTo(activityEntity.getRunTime()) >= 0) {
                other.setIsComplete(1);
                other.setCompleteTime(ZonedDateTime.now());
            }
            if (oneSelf.getIsComplete() == 1 && other.getIsComplete() == 1) {
                if (oneSelf.getRunMileage().compareTo(other.getRunMileage()) > 0) {
                    oneSelf.setRank(1);
                    other.setRank(2);
                } else if (oneSelf.getRunMileage().compareTo(other.getRunMileage()) == 0) {
                    oneSelf.setRank(0);
                    other.setRank(0);
                } else {
                    oneSelf.setRank(2);
                    other.setRank(1);
                }
            }
        }

        //为完成目标不算完成
        if (oneSelf.getIsComplete() == 0) {
            log.info("dealUserTask结束，未完成pk赛目标");
            if (task.getStatus() == 3) {
                runActivityUserTaskService.resetStatus(task.getId());
            }
            return;
        }

        if (task.getStatus() == 1 || task.getStatus() == 2) {
            return;
        }

        if (oneSelf.getIsComplete() == 1 && other.getIsComplete() == 0) {
            oneSelf.setRank(1);
            other.setRank(2);
        } else if (oneSelf.getIsComplete() == 0 && other.getIsComplete() == 1) {
            oneSelf.setRank(2);
            other.setRank(1);
        } else if (oneSelf.getIsComplete() == 0 && other.getIsComplete() == 0) {
            oneSelf.setRank(-1);
            other.setRank(-1);
        }
        BigDecimal winReward = task.getWinReward();
        BigDecimal failReward = task.getFailReward();
        if (oneSelf.getRank() == 1) {
            winReward = I18nConstant.currencyFormat(oneAccount.getCurrencyCode(), winReward);
            oneSelf.setRunAward(winReward);
        } else {
            failReward = I18nConstant.currencyFormat(oneAccount.getCurrencyCode(), failReward);
            oneSelf.setRunAward(failReward);
        }

        if (other.getRank() == 1) {
            winReward = I18nConstant.currencyFormat(otherAccount.getCurrencyCode(), winReward);
            other.setRunAward(winReward);
        } else {
            failReward = I18nConstant.currencyFormat(otherAccount.getCurrencyCode(), failReward);
            other.setRunAward(failReward);
        }
        if (oneSelf.getRunAward().compareTo(BigDecimal.ZERO) > 0) {
            oneSelf.setRewardTime(ZonedDateTime.now());
        }
        if (other.getRunAward().compareTo(BigDecimal.ZERO) > 0) {
            other.setRewardTime(ZonedDateTime.now());
        }

        if (oneSelf.getIsComplete() == 1) {
            oneSelf.setSubState(1);
        } else if (oneSelf.getRunTime() >= 60) {
            oneSelf.setSubState(2);
        }
        if (other.getIsComplete() == 1) {
            other.setSubState(1);
        } else if (other.getRunTime() >= 60) {
            other.setSubState(2);
        }
        runActivityUserService.updateById(oneSelf);
        runActivityUserService.updateById(other);

        super.handleRunActivityData(activityEntity);

        task.setTaskTime(ZonedDateTime.now());
        task.setActivityId(activityEntity.getId());

        if (oneSelf.getRank() == 1 || oneSelf.getRank() == 0) {
            task.setStatus(1);
            task.setAward(winReward);
        } else {
            task.setStatus(2);
            task.setAward(failReward);
        }
        runActivityUserTaskService.update(task);

        //奖励处理
        if (task.getAward().compareTo(BigDecimal.ZERO) > 0) {
            // 给用户余额发送奖励
            userAccountService.increaseAmount(task.getAward(), task.getUserId(), true);
            // 新增用户奖励余额明细
            String billNo = NanoId.randomNanoId();
            ;
            ZonedDateTime tradeTime = ZonedDateTime.now();
            userAccountDetailService.addRunActivityAccountDetail0131(task.getUserId(), AccountDetailTypeEnum.NEW_USER_AWARD,
                    task.getLevel(), 1, task.getAward(), billNo, tradeTime, task.getActivityId(),
                    task.getActivityId(), task.getRunDataDetailsId(), activityEntity.getActivityType(), 0L, "", null, null, acceptCountDecimal, BigDecimal.ZERO);
        }

        //解锁下一级任务
        runActivityUserTaskManager.unlockNextLevelTask(task.getId(), task.getUserId(), task.getLevel(), task.getAward(), 6);
    }

    private void awardNotification(ZnsRunActivityUserEntity activityUserEntity, ZnsRunActivityEntity activityEntity) {
        if (activityUserEntity.getRunAward().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        //通知
        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.AWARD_CHALLENGE_RUN;
        MessageBo message = appMessageService.assembleMessage(activityEntity.getId(), I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.AWARD_CHALLENGE_RUN"), "4",
                NoticeTypeEnum.REWARD_NOTICE.getType());
        message.setActivityId(activityEntity.getId());
        ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.AWARD_CHALLENGE_RUN"));
        appMessageService.sendImAndPushUserIds(Arrays.asList(activityUserEntity.getUserId()), imMessageBo, message);
    }

    private void awardNotificationOffPk(BigDecimal offlinePkWinnerAward, ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity winnerUser) {
        Map<String, Object> params = new HashMap<>();
        PkChallengeRecord pkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 1);
        PkChallengeRecord wasPkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 0);
        if (pkChallengeRecord == null || wasPkChallengeRecord == null) {
            return;
        }
        ZnsUserEntity pkZnsUserEntity = znsUserService.findById(pkChallengeRecord.getUserId());
        ZnsUserEntity wasPkUserEntity = znsUserService.findById(wasPkChallengeRecord.getUserId());

        params.put("activityId", activityEntity.getId());

        ZnsRunActivityUserEntity runActivityUserEntity = znsRunActivityUserDao.selectByActivityIdUserId(activityEntity.getId(), pkChallengeRecord.getUserId());
        // 肯定有一个胜利
        if (Objects.nonNull(winnerUser.getRank()) && winnerUser.getRank() == 1
                && runActivityUserEntity.getRunMileage().compareTo(new BigDecimal(Constants.target_1_Mile)) >= 0) {
            ZnsUserEntity winUser = null;
            ZnsUserEntity loserUser = null;

            if (Objects.equals(winnerUser.getUserId(), pkZnsUserEntity.getId())) {
                winUser = pkZnsUserEntity;
                loserUser = wasPkUserEntity;
            } else {
                winUser = wasPkUserEntity;
                loserUser = pkZnsUserEntity;
            }
            Map<String, Object> winReplace = new HashMap<>();
            winReplace.put("challengeName", pkZnsUserEntity.getFirstName());
            winReplace.put("wasChallengeName", wasPkUserEntity.getFirstName());
            winReplace.put("winName", winUser.getFirstName());

            Map<String, Object> loserReplace = new HashMap<>();
            loserReplace.put("challengeName", pkZnsUserEntity.getFirstName());
            loserReplace.put("wasChallengeName", wasPkUserEntity.getFirstName());
            // 离线pk im消息奖励内容
            PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(activityEntity.getId()).userId(winUser.getId()).build();
            PkChallengeRecord winPkChallengeRecord = pkChallengeRecordService.findByQuery(query);
            Currency currency = I18nConstant.buildDefaultCurrency();
            if (Objects.nonNull(winPkChallengeRecord.getCurrencyCode())) {
                currency = I18nConstant.buildCurrency(winPkChallengeRecord.getCurrencyCode());
                offlinePkWinnerAward = I18nConstant.currencyFormat(winPkChallengeRecord.getCurrencyCode(), offlinePkWinnerAward);
            }
            winPkChallengeRecord.setAward(offlinePkWinnerAward);
            loserReplace.put("award", fillAwardInfo(winPkChallengeRecord, currency));

            // 给失败者发送消息 。
//            activityMessageManager.sendPkChallengeIm(loserUser.getId(), Constants.challenge_you_win, winReplace, params, "lzrn://Race/BattleRaceDetailPage", activityEntity.getId());
            // 给赢者发送消息
//            activityMessageManager.sendPkChallengeIm(winUser.getId(), Constants.new_challenge_you_award, loserReplace, params, "lzrn://Race/BattleRaceDetailPage", activityEntity.getId());
        } else {                  //平局
            Map<String, Object> replacePk = new HashMap<>();
            replacePk.put("challengeName", pkZnsUserEntity.getFirstName());
            replacePk.put("wasChallengeName", wasPkUserEntity.getFirstName());

            if (runActivityUserEntity.getRunMileage().compareTo(new BigDecimal(Constants.target_1_Mile)) >= 0) {
//                activityMessageManager.sendPkChallengeIm(pkZnsUserEntity.getId(), Constants.challenge_you_pace, replacePk, params, "lzrn://Race/BattleRaceDetailPage", activityEntity.getId());
//                activityMessageManager.sendPkChallengeIm(wasPkUserEntity.getId(), Constants.challenge_you_pace, replacePk, params, "lzrn://Race/BattleRaceDetailPage", activityEntity.getId());
            } else {
//                activityMessageManager.sendPkChallengeIm(pkZnsUserEntity.getId(), Constants.challenge_you_no_finished, replacePk, params, "lzrn://Race/BattleRaceDetailPage", activityEntity.getId());
//                activityMessageManager.sendPkChallengeIm(wasPkUserEntity.getId(), Constants.challenge_you_no_finished, replacePk, params, "lzrn://Race/BattleRaceDetailPage", activityEntity.getId());
            }
        }
    }


    private String fillAwardInfo(PkChallengeRecord pkChallengeRecord, Currency currency) {
        StringBuffer b = new StringBuffer();
        boolean flag1 = Objects.nonNull(pkChallengeRecord.getAward()) && pkChallengeRecord.getAward().compareTo(BigDecimal.ZERO) > 0;
        boolean flag2 = Objects.nonNull(pkChallengeRecord.getScore()) && pkChallengeRecord.getScore() > 0;
        boolean flag3 = Objects.nonNull(pkChallengeRecord.getCouponNum()) && pkChallengeRecord.getCouponNum() > 0;
        if (flag1) {
            b.append(currency.getCurrencySymbol()).append(" ").append(pkChallengeRecord.getAward());
            if (flag2) {
                b.append("," + I18nMsgUtils.getMessage("user.points")).append(pkChallengeRecord.getScore());
            }
            if (flag3) {
                b.append("," + I18nMsgUtils.getMessage("user.coupon") + "*").append(pkChallengeRecord.getCouponNum());
            }
        }
        if (!flag1 && flag2) {
            b.append("," + I18nMsgUtils.getMessage("user.points")).append(pkChallengeRecord.getScore());
            if (flag3) {
                b.append("," + I18nMsgUtils.getMessage("user.coupon") + "*").append(pkChallengeRecord.getCouponNum());
            }
        }
        if (!flag1 && !flag2 && flag3) {
            b.append("," + I18nMsgUtils.getMessage("user.coupon") + "*").append(pkChallengeRecord.getCouponNum());
        }
        return b.toString();
    }

    /**
     * 奖励发放
     *
     * @param awardAmount
     * @param activityUser
     * @param activityEntity
     * @param extraAward
     * @param subType
     */
    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        if (null == awardAmount || awardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("挑战跑奖励金额为零");
            return null;
        }
        // 给用户余额发送奖励
        userAccountService.increaseAmount(awardAmount, activityUser.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        return userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.CHALLENGE_RUN_AWARD,
                AccountDetailSubtypeEnum.CHALLENGE_RUM.getType(), 1, awardAmount, billNo, tradeTime,
                activityUser.getActivityId(), activityUser.getActivityId(), null, activityEntity.getActivityType(),
                0L, "", null, null, null, BigDecimal.ZERO);
    }

    @Override
    public ChallengeRunReportVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        ChallengeRunReportVo challengeRunReportVo = new ChallengeRunReportVo();
        if (Objects.nonNull(activityEntity)) {
            challengeRunReportVo.setCompleteRuleType(activityEntity.getCompleteRuleType());
            challengeRunReportVo.setRunMileage(activityEntity.getRunMileage());
            challengeRunReportVo.setRunTime(activityEntity.getRunTime());
            challengeRunReportVo.setActivityStartTime(activityEntity.getActivityStartTime());
        } else {
            return challengeRunReportVo;
        }
        List<ChallengeRunRunningReportListVO> runRunningReportListVOS = userRunDataDetailsDao.getChallengeRunRunningReport(detail.getId(), loginUser.getId(), activityEntity.getId());
        if (CollectionUtils.isEmpty(runRunningReportListVOS)) {
            return challengeRunReportVo;
        }
        // 打补钉
        boolean flag = false;
        for (ChallengeRunRunningReportListVO challengeRunRunningReportListVO : runRunningReportListVOS) {
            //设置真实跑步时间
            challengeRunRunningReportListVO.setRealRunTimeMillisecond(challengeRunRunningReportListVO.getRunTimeMillisecond());
            //标记非官方pk赛 未完赛
            if (Objects.nonNull(challengeRunRunningReportListVO.getRunMileage()) && challengeRunRunningReportListVO.getTargetRunMileage().compareTo(challengeRunRunningReportListVO.getRunMileage()) > 0
                    && Objects.equals(challengeRunRunningReportListVO.getActivityType(), 2)) {
                flag = true;
                break;
            }
            if (Objects.nonNull(challengeRunRunningReportListVO.getRunTime()) && challengeRunRunningReportListVO.getTargetRunTime() > challengeRunRunningReportListVO.getRunTime()
                    && Objects.equals(challengeRunRunningReportListVO.getActivityType(), 2)) {
                flag = true;
                break;
            }
        }
        if (flag) {
            for (ChallengeRunRunningReportListVO challengeRunRunningReportListVO : runRunningReportListVOS) {
                challengeRunRunningReportListVO.setRank(0);
            }
        }

        //处理挑战跑 数据明细
        Integer finishStatus = dealChallengeDetail(runRunningReportListVOS, activityEntity.getCompleteRuleType(), activityEntity.getActivityType());
        challengeRunReportVo.setFinishStatus(finishStatus);

        if (RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType().equals(activityEntity.getActivityTypeSub())) {
            PkChallengeRecord pkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 1);
            for (ChallengeRunRunningReportListVO challengeRunRunningReportListVO : runRunningReportListVOS) {
                if (challengeRunRunningReportListVO.getRank() == 1) {
                    challengeRunRunningReportListVO.setRunAward(pkChallengeRecord.getAward());
                    challengeRunRunningReportListVO.setScore(pkChallengeRecord.getScore());
                    challengeRunRunningReportListVO.setCouponNum(pkChallengeRecord.getCouponNum());
                } else {
                    challengeRunRunningReportListVO.setRunAward(BigDecimal.ZERO);
                    challengeRunRunningReportListVO.setScore(0);
                    challengeRunRunningReportListVO.setCouponNum(0);
                }
            }
        } else if (activityEntity.getActivityType() == 1 || activityEntity.getActivityType() == 2) {
            //好友pk、随机pk查询用户积分记录
            List<ActivityUserScore> activityUserScores = activityUserScoreService.selectActivityUserScoreByActivityId(activityEntity.getId());
            List<UserCoupon> userCoupons = userCouponDao.selectUserCouponByActivityIdAndSource(activityEntity.getId(), 6);
            Map<Long, ActivityUserScore> scoreMap = activityUserScores.stream().filter(s -> Objects.isNull(s.getSource())).collect(Collectors.toMap(ActivityUserScore::getUserId, Function.identity(), (x, y) -> x));
            Map<Long, List<UserCoupon>> userCouponMap = userCoupons.stream().collect(Collectors.groupingBy(UserCoupon::getUserId));
            for (ChallengeRunRunningReportListVO challengeRunRunningReportListVO : runRunningReportListVOS) {
                if (Objects.nonNull(scoreMap)) {
                    ActivityUserScore activityUserScore = scoreMap.get(challengeRunRunningReportListVO.getUserId());
                    if (Objects.nonNull(activityUserScore)) {
                        challengeRunRunningReportListVO.setScore(activityUserScore.getScore());
                    }
                }
                if (Objects.nonNull(userCouponMap)) {
                    List<UserCoupon> userCouponList = userCouponMap.get(challengeRunRunningReportListVO.getUserId());
                    if (!CollectionUtils.isEmpty(userCouponList)) {
                        challengeRunRunningReportListVO.setCouponNum(userCouponList.size());
                    }
                }
            }
            if (RunActivitySubTypeEnum.getNewPersonPkType().contains(activityEntity.getActivityTypeSub())) {
                activityUserScores.forEach(i -> {
                    for (ChallengeRunRunningReportListVO challengeRunRunningReportListVO : runRunningReportListVOS) {
                        if (challengeRunRunningReportListVO.getUserId().equals(i.getUserId())) {
                            if (i.getSource().equals(ScoreConstant.SourceTypeEnum.source_type_24.getType()) ||
                                    i.getSource().equals(ScoreConstant.SourceTypeEnum.source_type_25.getType())) {
                                challengeRunRunningReportListVO.setScore(i.getScore());
                            }
                        }
                    }
                });
            }
        }
        challengeRunReportVo.setList(runRunningReportListVOS);

        return challengeRunReportVo;
    }


    private boolean checkRunActivityDataDetailEnd(Long id) {
        for (int i = 0; i < 6; i++) {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("[ChallengeRun][checkRunActivityDataDetailEnd]---判断获得是否结束,活动={},异常：", id, e);
            }
            if (userRunDataDetailsService.isEnd(id)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void wrapperActivityRewardDetailByActivityType(ZnsRunActivityEntity activityEntity, Map<String, Object> jsonObjectConfig, RunActivityRewardDetailVO runActivityRewardDetailVO, ZnsUserEntity loginUser) {
        //离线PK处理
        String currencyCode = userAccountService.getUserAccount(loginUser.getId()).getCurrencyCode();
        if (activityEntity.getActivityType() == 2 && Objects.equals(activityEntity.getActivityTypeSub(), 3)) {
            return;
        }
        Currency currency = userAccountService.getCurrency(loginUser.getId(), activityEntity.getId(), true);
        if (activityEntity.getActivityType() == 2 && (RunActivitySubTypeEnum.getNewPersonPkType().contains(activityEntity.getActivityTypeSub()))) {
            ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(activityEntity.getActivityType(), activityEntity.getActivityTypeSub());
            Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
            UserGameAwardDto userGameAwardDto = JsonUtil.readValue(jsonObject.get("finishAward"), UserGameAwardDto.class);
            if (Objects.nonNull(userGameAwardDto)) {
                List<CurrencyAmount> amountList = userGameAwardDto.getAmountList();
                if (!CollectionUtils.isEmpty(amountList)) {
                    BigDecimal amount = amountList.stream().filter(s -> s.getCurrencyCode().equals(currency.getCurrencyCode())).findFirst().get().getAmount();
                    amount = I18nConstant.currencyFormat(currency.getCurrencyCode(), amount);
                    NewPersonPkAward newPersonPkAward = new NewPersonPkAward();
                    newPersonPkAward.setWinAmount(amount);
                    newPersonPkAward.setCurrency(currency);
                    runActivityRewardDetailVO.setNewPersonPkAward(newPersonPkAward);
                    return;
                }
            }
        }
        Boolean isNewPKAwardActivity = PkFriendAwardUtil.isNewPKAwardActivity(activityEntity);
        List<AwardConfigDto> awardConfigDtoList = awardConfigBizService.selectAwardConfigDtoListByActivityId(activityEntity.getId(), null, null);
        //没有数据表示历史数据，兼容
        if (CollectionUtils.isEmpty(awardConfigDtoList) || isNewPKAwardActivity) {
            BigDecimal nowRate = null;

            if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currency.getCurrencyCode())) {
                nowRate = BigDecimal.ONE;
            } else {
                nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currency.getCurrencyCode()).getExchangeRate();
            }

            String participateAwardStr = MapUtil.getString(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD));          // 参与金额
            BigDecimal participateAward = new BigDecimal(participateAwardStr);
            String winnerAwardStr = MapUtil.getString(jsonObjectConfig.get(ApiConstants.WINNER_AWARD));
            BigDecimal winnerAward = new BigDecimal(winnerAwardStr);

            List<RunActivityRewardConfigDetails> runActivityRewardConfigDetails = new ArrayList<>();
            RunActivityRewardConfigDetails participateConfig = new RunActivityRewardConfigDetails();
            participateAward = participateAward.multiply(nowRate).setScale(2, RoundingMode.UP);
            participateAward = I18nConstant.currencyFormat(currencyCode, participateAward);
            participateConfig.setAmount(participateAward);
            participateConfig.setCurrency(currency);
            participateConfig.setSendType(AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
            runActivityRewardConfigDetails.add(participateConfig);

            RunActivityRewardConfigDetails winnerConfig = new RunActivityRewardConfigDetails();
            winnerAward = winnerAward.multiply(nowRate).setScale(2, RoundingMode.UP);
            winnerAward = I18nConstant.currencyFormat(currencyCode, winnerAward);
            winnerConfig.setAmount(winnerAward);
            winnerConfig.setCurrency(currency);
            winnerConfig.setSendType(AwardSentTypeEnum.WINNER_AWARD.getType());
            runActivityRewardConfigDetails.add(winnerConfig);
            runActivityRewardDetailVO.setAwardConfigDetailsDtoList(runActivityRewardConfigDetails);
        } else {
            List<RunActivityRewardConfigDetails> runActivityRewardConfigDetails = awardConfigBizService.assembleRunActivityRewardConfigDetails(awardConfigDtoList, loginUser);
            runActivityRewardDetailVO.setAwardConfigDetailsDtoList(runActivityRewardConfigDetails);
        }
    }

    public static void main(String[] args) {
        ZnsRunActivityUserEntity znsRunActivityUserEntity = new ZnsRunActivityUserEntity();
        znsRunActivityUserEntity.setRunMileage(new BigDecimal(1600));
        if (MapUtil.getInteger(znsRunActivityUserEntity.getRunMileage(), 0) >= Constants.target_1_Mile) {
            System.out.println("================");
        }
    }
}
