package com.linzi.pitpat.data.activityservice.biz.award.calculate;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.activityservice.biz.award.ActivityUserAwardCalculateContext;
import com.linzi.pitpat.data.activityservice.biz.award.match.AwardMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDetailDo;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class AmountCalculator implements AwardCalculator {
    @Override
    public AwardTypeEnum getAwardType() {
        return AwardTypeEnum.AMOUNT;
    }

    private final AwardConfigAmountService awardConfigAmountService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    private final ZnsUserAccountService userAccountService;
    private final ActivityAwardConfigService activityAwardConfigService;

    /**
     * copy from
     * com.linzi.pitpat.data.awardservice.strategy.AmountProcessStrategy#doProcess(com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto, java.util.List<java.lang.Long>, java.lang.String)
     *
     * @param context
     * @param detailDo
     * @param awardMatch
     * @return
     */
    @Override
    public AwardMatch cal(ActivityUserAwardCalculateContext context, ActivityRunRankTempDetailDo detailDo, AwardMatch awardMatch) {
        Long activityId = context.getMainActivity().getId();
        Long userId = detailDo.getUserId();
        log.info("AwardCalculator - 开始金额计算，activityId: {}, userId: {}, configCount: {}",
                activityId, userId, awardMatch.getAwardConfigIds().size());

        awardMatch.getAwardConfigIds().forEach(i -> {
            try {
                log.debug("AwardCalculator - 处理金额配置，activityId: {}, userId: {}, configId: {}", activityId, userId, i);
                AwardSendDto dto = awardMatch.getAwardSendDto();
                BigDecimal sendAmount = BigDecimal.ZERO;
                AwardConfigAmount awardConfigAmount = awardConfigAmountService.findByAwardConfigId(i);
                Currency userCurrency = userAccountService.getUserCurrency(detailDo.getUserId());
                AwardConfigAmountCurrency amountCurrency = awardConfigAmountCurrencyDataService.findByAmountIdAndCurrencyCode(awardConfigAmount.getId(), userCurrency.getCurrencyCode());
                if (Objects.nonNull(amountCurrency)) {
                    sendAmount = amountCurrency.getAmount();
                }
                log.debug("AwardCalculator - 金额配置详情，activityId: {}, userId: {}, configId: {}, originalAmount: {}",
                        activityId, userId, i, sendAmount);

                ActivityAwardConfig awardConfig = activityAwardConfigService.findByAwardId(awardConfigAmount.getAwardConfigId());
                if (awardConfig.getIsDivide() == 1) {
                    if (Objects.isNull(dto.getDivideUserCount())) {
                        log.warn("AwardCalculator - 瓜分用户数为空，activityId: {}, userId: {}, configId: {}", activityId, userId, i);
                        awardMatch.setFinal(true);
                        awardMatch.addAmount(sendAmount);
                        return;
                    }
                    BigDecimal originalAmount = sendAmount;
                    sendAmount = sendAmount.divide(new BigDecimal(dto.getDivideUserCount()), 2, RoundingMode.UP);
                    log.info("AwardCalculator - 金额瓜分计算，activityId: {}, userId: {}, configId: {}, originalAmount: {}, divideCount: {}, finalAmount: {}",
                            activityId, userId, i, originalAmount, dto.getDivideUserCount(), sendAmount);
                }
                Integer rightsInterestsType = null;
                Integer privilegeBrand = null;
                BigDecimal rightsInterestsMultiple = null;
                BigDecimal brandAward = BigDecimal.ZERO;
//                // 权益奖励判定
//                if (brandRightsListType.contains(dto.getType())) {
//                    Integer statusCode = getBrandRightsInterestEnumBySendType(dto.getType());
//                    //权益处理
//                    ActivityBrandRightsInterests enjoyBenefits = activityBrandInterestsBizService.getBrandRightsInterests(statusCode, dto.getActivityId(), dto.getUserId());
//                    if (Objects.nonNull(enjoyBenefits)) {
//                        rightsInterestsMultiple = enjoyBenefits.getMultiple();
//                        rightsInterestsType = enjoyBenefits.getRightsInterestsType();
//                        privilegeBrand = enjoyBenefits.getBrand();
//                        sendAmount = sendAmount.multiply(enjoyBenefits.getMultiple()).setScale(2, RoundingMode.UP);
//
//                    }
//                }
                // 比较金额大小
                if (sendAmount.compareTo(BigDecimal.ZERO) > 0) {
//                    //翻倍奖励券
//                    if (Objects.nonNull(dto.getDoubleUserCoupon())) {
//                        userCouponManager.endUseCoupon(dto.getDoubleUserCoupon(), sendAmount);
//                        sendAmount = sendAmount.add(sendAmount);
//                    }
//                    //币种格式化
//                    sendAmount = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), sendAmount);
//                    userAccountService.increaseAmount(sendAmount, dto.getUserId(), true);
//                    // 新增用户奖励余额明细
//                    String billNo = NanoId.randomNanoId();
//                    ;
//                    ZonedDateTime tradeTime = ZonedDateTime.now();
//                    Long detailId = userAccountDetailService.addRunActivityAccountDetail0131(dto.getUserId(), AccountDetailTypeEnum.NEW_ACTIVITY_100,
//                            dto.getType(), 1, sendAmount, billNo, tradeTime, dto.getDetailId(),
//                            dto.getActivityId(), dto.getDetailId(), 0, 0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, brandAward);
//                    //add log
//                    // 更新用户报名表奖金
//                    ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(dto.getActivityId(), dto.getUserId());
//                    znsRunActivityUserService.updateUserRunAwardAmountById(activityUser.getId(), sendAmount);
//                    //添加记录
//                    activityUserAwardBizService.saveNew(dto, detailId, AwardTypeEnum.AMOUNT.getType(), batchNo, dto.getTotalBatchNo());
//
//                    putAwardSendFlagIntoCache(dto, i);
                    awardMatch.setFinal(true);
                    awardMatch.addAmount(sendAmount);
                    log.info("AwardCalculator - 金额计算完成，activityId: {}, userId: {}, configId: {}, finalAmount: {}, totalAmount: {}",
                            activityId, userId, i, sendAmount, awardMatch.getAmount());
                }
            } catch (Exception e) {
                log.error("AwardCalculator - 金额计算异常，activityId: {}, userId: {}, configId: {}", activityId, userId, i, e);
            }
        });

        log.info("AwardCalculator - 金额计算总结，activityId: {}, userId: {}, totalAmount: {}",
                activityId, userId, awardMatch.getAmount());
        return awardMatch;
    }
//    @Override
//    public List<AwardTempDto> match(ActivityUserAwardCalculateContext context) {
//        context.forEach(activityRunRankTempDetailDo -> {
//            List<AwardMatch> gameAwardList = activityRunRankTempDetailDo.getGameAwardList();
//
//        });
//    }

//    public
}
