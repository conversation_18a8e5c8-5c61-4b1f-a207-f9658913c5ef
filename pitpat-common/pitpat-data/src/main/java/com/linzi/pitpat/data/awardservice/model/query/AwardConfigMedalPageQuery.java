package com.linzi.pitpat.data.awardservice.model.query;

import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.data.awardservice.model.entity.AwardConfigMedalDo;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serial;


/**
 * 奖励配置勋章表分页查询对象
 *
 * @since 2025年7月3日
 */
@Getter
@Accessors(chain = true)
public class AwardConfigMedalPageQuery extends PageQuery {

    private Long id ;
}
