package com.linzi.pitpat.data.activityservice.dto.api.response;

import com.linzi.pitpat.data.activityservice.enums.FreeRoomStatusEnum;
import com.linzi.pitpat.data.userservice.dto.api.response.UserNameAndHeadResponseDto;
import com.linzi.pitpat.data.vo.user.SimpleUserSelectListVo;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 免费房间数据传输对象
 * 
 * @since 2025年1月
 */
@Data
public class FreeRoomDto {

    /**
     * 房间编号
     */
    private Long roomNumber;

    /**
     * 房间状态
     * 
     * @see FreeRoomStatusEnum
     */
    private String status;

    /**
     * 当前房间人数
     */
    private Integer currentNum = 0;

    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 用户数据列表
     */
    private List<UserNameAndHeadResponseDto> userList;
}
