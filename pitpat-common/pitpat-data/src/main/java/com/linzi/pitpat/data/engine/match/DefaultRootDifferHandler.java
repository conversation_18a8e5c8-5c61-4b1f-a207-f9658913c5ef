package com.linzi.pitpat.data.engine.match;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.activityservice.mapper.MindUserMatchDao;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;

@Component("defaultRootDifferHandler")
@Slf4j
public class DefaultRootDifferHandler extends BaseMatchHandler {

    @Autowired
    private MindUserMatchDao mindUserMatchDao;

    @Override
    public Pair<Boolean, BigDecimal> match(Long userId, Long targetUserId, Integer targetMileage) {
        ZonedDateTime currentDate = ZonedDateTime.now();
        List<MindUserMatch> mindUserMatchList = mindUserMatchDao.selectMindUserMatchByUserIdMatchUserIdStartTimeEndTimeStatus(userId, targetUserId,
                DateUtil.addDays(currentDate, -7), currentDate, Arrays.asList(1, 2));
        log.info("defaultRootDifferHandler userId =" + userId + ", targetUserId=" + targetUserId + ",targetMileage= " + targetMileage);
        if (!CollectionUtils.isEmpty(mindUserMatchList)) {
            log.info("defaultRootDifferHandler 7天(含)内有一起1v1过：2分");
            return Pair.of(true, new BigDecimal(2));
        }
        mindUserMatchList = mindUserMatchDao.selectMindUserMatchByUserIdMatchUserIdStartTimeEndTimeStatus(userId, targetUserId, DateUtil.addDays(currentDate, -14),
                currentDate, Arrays.asList(1, 2));
        if (!CollectionUtils.isEmpty(mindUserMatchList)) {
            log.info("defaultRootDifferHandler 7-14(含)有一起1v1过：4分");
            return Pair.of(true, new BigDecimal(4));
        }
        mindUserMatchList = mindUserMatchDao.selectMindUserMatchByUserIdMatchUserIdStartTimeEndTimeStatus(userId, targetUserId, DateUtil.addDays(currentDate, -21),
                currentDate, Arrays.asList(1, 2));
        if (!CollectionUtils.isEmpty(mindUserMatchList)) {
            log.info("defaultRootDifferHandler 14-21(含)天有一起1v1过：6分");
            return Pair.of(true, new BigDecimal(6));
        }
        mindUserMatchList = mindUserMatchDao.selectMindUserMatchByUserIdMatchUserIdStartTimeEndTimeStatus(userId, targetUserId, DateUtil.addDays(currentDate, -30),
                currentDate, Arrays.asList(1, 2));
        if (!CollectionUtils.isEmpty(mindUserMatchList)) {
            log.info("defaultRootDifferHandler 21-30(含)天有一起1v1过：8分");
            return Pair.of(true, new BigDecimal(8));
        }
        log.info("defaultRootDifferHandler 30天以内没有一起1v1过：10分 ");
        return Pair.of(true, new BigDecimal(10));
    }
}
