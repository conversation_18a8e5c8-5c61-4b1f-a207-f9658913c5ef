package com.linzi.pitpat.data.activityservice.dto.api.request.activity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PropActivityUserGradeReportDto {

    //userId
    private Long userId;

    //活动id
    private Long activityId;

    //排名
    private Integer rank;

    //用户虚拟里程 m
    private BigDecimal runMileage;

    //用户虚拟时间 mils
    private Integer runTime;
    // detailId
    private Long detailId;

}
