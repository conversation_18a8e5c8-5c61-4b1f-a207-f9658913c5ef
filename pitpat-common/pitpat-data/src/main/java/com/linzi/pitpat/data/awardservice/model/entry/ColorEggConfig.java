package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*彩蛋配置表
 *
 * <AUTHOR>
 * @since 2022-05-20
 */

@Data
@Accessors(chain = true)
@TableName("zns_color_egg_config")
public class ColorEggConfig implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //规则
    private String rule;
    //枚举:RANDOM_DISTANCE,random_distance,随机多少（米）: RANDOM_TIME,random_time,随机时间 (秒) : FIX_TIME,fix_time,固定时间 （秒）:FIX_DISTANCE, fix_distance,（米） 固定距离: SUPER_PERSON,super_person, 超过（人）
    private String type;
    //彩蛋开始时间
    private ZonedDateTime gmtStart;
    //彩蛋结束时间
    private ZonedDateTime gmtEnd;
    //枚举 状态:start,1,开始: end,0,结束
    private Integer status;
    //备注
    private String remark;
    //%Y 年 ， %Y-%m 月，%Y-%m-%d  日 ， %Y-%m-%d %H 时， %Y-%m-%d %H:%i 分 ，%Y-%m-%d %H:%i:%S 秒
    private String awardRule;
    //每一次获得奖品的值
    private String awardValue;
    //在规定时间内，获取的最大值
    private String maxValue;
    //次数
    private Integer maxCount;

    private Integer routeType;          // 路线类型，2 表示2D , 3 表示 3D
    private Integer rPosition;              // 随机位置
    private String rAwardCount;         // 随机获取次数
    private Integer singleCount;        // 单场出现次数
    private Integer firstAppearSecond;   // 首次出现时间
    private Integer intervalAppearSecond; // 间隔多少秒出现
    private Integer validTime; // 墙有效时间
    // 积分配置值
    private Integer pointValue;
    //使用类型 0 新老用户 1 新用户 2老用户
    private Integer useType;


    /**
     * 枚举:RANDOM_DISTANCE,random_distance,随机多少（米）: RANDOM_TIME,random_time,随机时间 (秒) : FIX_TIME,fix_time,固定时间 （秒）:FIX_DISTANCE, fix_distance,（米） 固定距离: SUPER_PERSON,super_person, 超过（人）
     */
    public enum TYPE_ENUM {
        RANDOM_DISTANCE("random_distance", "随机多少（米）"),
        RANDOM_TIME("random_time", "随机时间 (秒)"),
        FIX_TIME("fix_time", "固定时间 （秒）"),
        FIX_DISTANCE("fix_distance", "（米） 固定距离"),
        SUPER_PERSON("super_person", "超过（人）");


        private String code;

        private String desc;

        TYPE_ENUM(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    /**
     * 枚举 状态:start,1,开始: end,0,结束
     */
    public enum STATUS_ENUM {
        start(1, "开始"),
        end(0, "结束");


        private Integer code;

        private String desc;

        STATUS_ENUM(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    @Override
    public String toString() {
        return "ColorEggConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",rule=" + rule +
                ",type=" + type +
                ",gmtStart=" + gmtStart +
                ",gmtEnd=" + gmtEnd +
                ",status=" + status +
                ",remark=" + remark +
                ",awardRule=" + awardRule +
                ",awardValue=" + awardValue +
                ",maxValue=" + maxValue +
                ",maxCount=" + maxCount +
                "}";
    }
}
