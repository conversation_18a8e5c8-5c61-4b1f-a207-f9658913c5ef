package com.linzi.pitpat.data.awardservice.biz;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.OfficialCumulativeRunDto;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityWearCacheInfo;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreIdDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsLog;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.WearsI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserWearsEntity;
import com.linzi.pitpat.data.awardservice.model.query.ExchangeWearQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.model.query.WearQuery;
import com.linzi.pitpat.data.awardservice.model.query.WearsI18nQuery;
import com.linzi.pitpat.data.awardservice.model.query.ZnsUserWearQuery;
import com.linzi.pitpat.data.awardservice.model.resp.MyWearResp;
import com.linzi.pitpat.data.awardservice.model.vo.WearAward;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagLogService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.UserWearsLogService;
import com.linzi.pitpat.data.awardservice.service.WearsI18nService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserWearsService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.UserWearTypeEnum;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户服装处理类
 *
 * <AUTHOR>
 * @date 2024/6/19 14:26
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserWearsBizService {
    private final WearsService wearsService;
    private final ZnsUserWearsService userWearsService;
    private final UserWearsLogService userWearsLogService;
    private final ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    private final ExchangeScoreAwardService exchangeScoreAwardService;
    private final ZnsRunActivityService runActivityService;
    private final WearsI18nService wearsI18nService;
    private final AppRouteService appRouteService;
    private final ZnsUserAccountService userAccountService;
    private final ISysConfigService sysConfigService;
    private final AppUpgradeService appUpgradeService;
    private final ZnsUserService userService;
    private final ExchangeScoreRuleService exchangeScoreRuleService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserWearsBagService userWearsBagService;
    private final UserWearsBagLogService userWearsBagLogService;
    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 修改用户服装
     *
     * @param userId
     * @param wearAwardDtos
     * @param sourceType
     */
    public void updateUserWear(Long userId, List<WearAwardDto> wearAwardDtos, Integer sourceType) {
        ZnsUserWearsEntity one = userWearsService.findByQuery(ZnsUserWearQuery.builder().userId(userId).build());
        for (WearAwardDto s : wearAwardDtos) {
            UserWearTypeEnum userWearTypeEnum = UserWearTypeEnum.findByType(s.getWearType());
            List<Integer> collect = new ArrayList<>();
            //表示用户穿戴了套装
            if (Objects.nonNull(one.getSuit()) && one.getSuit() >= 0) {
                Wears wear = wearsService.getWearByWearIdAndType(UserWearTypeEnum.SUIT.getType(), one.getSuit());
                List<Integer> currentUserSuitList = JsonUtil.readList(wear.getAttireTypeIncluded(), Integer.class);
                //要换服装不是套装
                if (!Objects.equals(s.getWearType(), UserWearTypeEnum.SUIT.getType())) {
                    if (currentUserSuitList.contains(s.getWearType())) {
                        one.setSuit(-1);
                    }
                    fillWearValue(userWearTypeEnum, one, s.getWearValue(), collect);
                    currentUserSuitList.forEach(v -> fillWearValue(UserWearTypeEnum.findByType(v), one, 0, collect));
                } else {
                    //要换服装是套装
                    collect.addAll(currentUserSuitList);
                    fillWearValue(UserWearTypeEnum.findByType(s.getWearType()), one, s.getWearValue(), collect);
                }
            } else {
                //用户当前穿的不是套装
                fillWearValue(userWearTypeEnum, one, s.getWearValue(), collect);
            }
        }
        userWearsService.updateById(one);
        //修改成功插入到log表
        UserWearsLog userWearsLog = new UserWearsLog();
        userWearsLog.setUserWearId(one.getId());
        userWearsLog.setSourceType(sourceType);
        BeanUtils.copyProperties(one, userWearsLog);
        userWearsLog.setId(null);
        userWearsLogService.insert(userWearsLog);
    }

    /**
     * 添加机器人服装
     *
     * @param userId
     * @param isRobot
     */
    public void addRobotWears(Long userId, Integer isRobot) {
        ZnsUserWearsEntity userWearsEntity = generateRobotWear(userId, isRobot);
        if (userWearsEntity == null) return;

        userWearsService.insert(userWearsEntity);
    }

    /**
     * 更新机器人服装
     *
     * @param userWear
     * @param isRobot
     */
    public void updateRobotWears(ZnsUserWearsEntity userWear, Integer isRobot) {
        ZnsUserWearsEntity userWearsEntity = generateRobotWear(userWear.getUserId(), isRobot);
        if (userWearsEntity == null) return;
        userWearsEntity.setId(userWear.getId());

        userWearsService.updateById(userWearsEntity);
    }


    /**
     * 填充缓存
     *
     * @param loginUser
     */
    public void fillCache(ZnsUserEntity loginUser) {
        //用户服装背包
        UserWearBagQuery userWearBagQuery = UserWearBagQuery.builder().userId(loginUser.getId()).status(0).build();
        userWearBagQuery.setOrders(List.of(OrderItem.asc("ISNULL(expired_time)"), OrderItem.asc("expired_time")));
        List<UserWearsBag> userWearsBags = userWearsBagService.findListByQuery(userWearBagQuery);
        List<Integer> suitList = Lists.newArrayList(18, 11, 33, 36, 37, 38);//套装id列表
        RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
        Set<String> keys = scan(RedisConstants.WEAR_AWARD_OBTAIN + "*");
        HashMap<Long, List<WearAward>> map = new HashMap<>();
        for (String key : keys) {
            Map<String, String> entries = redisTemplate.opsForHash().entries(key);
            for (Map.Entry<String, String> Entry : entries.entrySet()) {
                Long activityId = Long.parseLong(Entry.getKey());
                ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
                if (Objects.isNull(znsRunActivityEntity)) {
                    continue;
                }
                String wears = Entry.getValue();
                List<WearAward> wearAwards = JsonUtil.readList(wears, WearAward.class);
                if (!CollectionUtils.isEmpty(wearAwards) && !CollectionUtils.isEmpty(userWearsBags)) {
                    wearAwards = getWearAwards(suitList, wearAwards);
                    for (UserWearsBag userWearsBag : userWearsBags) {
                        List<WearAward> bagList = wearAwards.stream().filter(s -> Objects.equals(s.getWearType(), userWearsBag.getWearType()) && Objects.equals(s.getWearId(), userWearsBag.getWearValue())).collect(Collectors.toList());
                        //活动剔除背包已拥有
                        wearAwards.removeAll(bagList);
                    }
                }
                if (!CollectionUtils.isEmpty(wearAwards)) {
                    map.put(activityId, wearAwards);
                    ActivityWearCacheInfo activityWearCacheInfo = new ActivityWearCacheInfo();
                    activityWearCacheInfo.setActivityId(activityId);
                    activityWearCacheInfo.setExpireTime(znsRunActivityEntity.getApplicationEndTime());
                    activityWearCacheInfo.setWearList(wearAwards);
                    list.add(activityWearCacheInfo);
                }
            }
        }
    }

    /**
     * 兑换服装
     *
     * @param loginUser
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result exchangeWear(ZnsUserEntity loginUser, ExchangeWearQuery query) {
        // 防止重复提交
        Long loginUserId = loginUser.getId();
        String lockKey = RedisConstants.EXCHANGE_SCORE_RULE + loginUserId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean getLock = lock.tryLock();
        try {
            if (!getLock) {
                log.info("startTask 获取锁失败");
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemError"));
            }
            log.info("handleActivityFinished获取锁 成功 " + lockKey);
            List<WearQuery> exchangeList = query.getExchangeList();


            for (WearQuery wearQuery : exchangeList) {
                if (wearQuery.getRuleId() == null) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error")); //"兑换规则ID为空"
                }
                // 校验积分是否充足
                ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(wearQuery.getRuleId());
                if (exchangeScoreRule.getExchangeReserve() == 0) {
                    // 剩余可兑换量校验
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.exchange.count.exceed"));
                }
                if (exchangeScoreRule.getExchangeScore() > userService.getAllUserScore(loginUser.getId())) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.exchange.points.exceed"));
                }
                //检验兑换人次数
//                Integer exchangePersonCount = activityUserScoreDao.selectExchangePersonCountByUserIdExchangeScoreRuleId(loginUser.getId(), exchangeScoreRule.getId(), -1);
//                if (exchangePersonCount >= exchangeScoreRule.getExchangePersonCount()) {
//                    return CommonResult.fail("Exceed the redeemable limit");
//                }
                //获取当前用使用的户货币金额
                Currency currency = getUserCurrency(loginUser);
                ExchangeScoreRuleCurrencyEntity exchangeScoreRuleCurrency = exchangeScoreRuleCurrencyService.findByRuleIdAndCurrencyCode(exchangeScoreRule.getId(), currency.getCurrencyCode());

                if (Objects.isNull(exchangeScoreRuleCurrency)) {
                    exchangeScoreRule.setExchangeAmount(BigDecimal.ZERO);
                } else {
                    exchangeScoreRule.setExchangeAmount(exchangeScoreRuleCurrency.getExchangeAmount());
                }
                // 扣除余额
                if (exchangeScoreRule.getExchangeAmount().compareTo(new BigDecimal(0)) > 0) {
                    if (!StringUtils.hasText(query.getPassword())) {
                        return CommonResult.fail(I18nMsgUtils.getMessage("common.page.expired"));
                    } else {
                        // 校验密码
                        Result result = userAccountService.checkPassword(loginUser.getId(), query.getPassword(), Boolean.TRUE);
                        if (Objects.nonNull(result)) {
                            return result;
                        }
                        // 扣除余额
                        RunActivityPayRequest request = new RunActivityPayRequest();
                        request.setUserId(loginUser.getId());
                        request.setPayType(0);
                        request.setPayPassword(query.getPassword());
                        request.setAccountDetailTypeEnum(AccountDetailTypeEnum.EXCHANGE_SCORE_SPEND);
                        request.setPrivilegeBrand(-1); // 特权品牌
                        request.setBrandRightsInterests(-1); // 权益类型
                        request.setActivityId(exchangeScoreRule.getId()); // 活动id也暂时用积分规则id
                        request.setOperationalActivityId(exchangeScoreRule.getId()); // 关联的积分兑换规则id
                        request.setAmount(exchangeScoreRule.getExchangeAmount()); // 兑换所需金额
                        Result payResult = userAccountService.payByBalance(request);// 使用余额支付
                        if (Objects.nonNull(payResult) && payResult.getCode().equals(CommonError.BUSINESS_ERROR.getCode())) {
                            // 余额不足的情况
                            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("userAccount.balance.insufficient"), payResult.getData());
                        }
                    }
                }
                ScoreIdDto scoreIdDto = new ScoreIdDto();
                scoreIdDto.setUserId(loginUser.getId());
                scoreIdDto.setPassword(query.getPassword());
                scoreIdDto.setId(wearQuery.getRuleId());
                activityUserScoreService.useActivityUserScore(exchangeScoreRule, scoreIdDto);
                ExchangeScoreAward exchangeAward = exchangeScoreAwardService.getOne(Wrappers.<ExchangeScoreAward>lambdaQuery().eq(ExchangeScoreAward::getIsDelete, 0).eq(ExchangeScoreAward::getRuleId, wearQuery.getRuleId()));

                // 增加积分兑换记录(相当于扣除积分)
                ActivityUserScore activityUserScore = new ActivityUserScore();
                activityUserScore.setExchangeScoreRuleId(exchangeScoreRule.getId());
                activityUserScore.setScore(exchangeScoreRule.getExchangeScore());
                activityUserScore.setAmount(exchangeScoreRule.getExchangeAmount());
                activityUserScore.setStatus(1);
                activityUserScore.setExchangeTime(ZonedDateTime.now());
                activityUserScore.setSendTime(ZonedDateTime.now());
                activityUserScore.setType(2);
                activityUserScore.setIncome(-1);
                activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N3.getType()); // 积分兑换商品（衣服）
                activityUserScore.setUserId(loginUser.getId());
                activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
                //增加积分兑换记录
                activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);
                // 查询用户背包有没有该服装
                List<UserWearsBag> wearBag = userWearsBagService.findListByQuery(UserWearBagQuery.builder().userId(loginUser.getId()).wearType(wearQuery.getWearType()).wearValue(wearQuery.getWearId()).build());

                if (CollectionUtils.isEmpty(wearBag)) {
                    // 放入用户背包
                    UserWearsBag userWearsBag = new UserWearsBag();
                    userWearsBag.setWearType(wearQuery.getWearType());
                    userWearsBag.setWearName(wearQuery.getWearName());
                    userWearsBag.setWearValue(wearQuery.getWearId());
                    userWearsBag.setWearImageUrl(wearQuery.getWearImageUrl());
                    userWearsBag.setSource(2);
                    userWearsBag.setUserId(loginUser.getId());
                    userWearsBag.setStatus(0);
                    userWearsBag.setIsNew(0);
                    if (Objects.nonNull(exchangeAward.getExpiredTime())) {
                        userWearsBag.setExpiredTime(DateUtil.getAddHours(exchangeAward.getExpiredTime()));
                    }
                    userWearsBagService.insert(userWearsBag);
                } else {
                    // 存在限时服装 更新时间
                    UserWearsBag userWearsBag = wearBag.get(0);
                    if (Objects.nonNull(userWearsBag.getExpiredTime())) {
                        userWearsBag.setExpiredTime(DateUtil.addHours(userWearsBag.getExpiredTime(), exchangeAward.getExpiredTime() * 24));
                        userWearsBagService.update(userWearsBag);
                    }
                }
                Integer exchangeCount = activityUserScoreService.selectByExchangeScoreRuleIdSource(exchangeScoreRule.getId(), Arrays.asList(-3));
                //剩余库存
                Integer remainNum = exchangeScoreRule.getRemainNum() - 1;
                ExchangeScoreRule rule = new ExchangeScoreRule();
                rule.setExchangeCount(exchangeCount);
                rule.setRemainNum(remainNum);
                rule.setId(exchangeScoreRule.getId());
                exchangeScoreRuleService.updateExchangeScoreByIdAndExchangeReserve(rule);
                exchangeScoreRuleService.setUserScoreRuleDayExchangeCache(loginUserId, exchangeScoreRule.getId());
            }
        } catch (Exception e) {
            log.error("startTask 处理失败，e:", e);
            throw e;
        } finally {
            if (getLock) {
                log.info("startTask获取锁 后删除锁" + lockKey);
                if (lock.isHeldByCurrentThread()) {
                    //判断锁是否存在，和是否当前线程加的锁。
                    lock.unlock();
                }
            }
        }
        return CommonResult.success();
    }


    /**
     * 给新用户发放免费服装
     *
     * @param userId
     */
    public void sendFreeToNewUser(Long userId) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.FREE_WEAR_TO_NEW_USER.getCode());
        if (Objects.nonNull(sysConfig) && StringUtils.hasText(sysConfig.getConfigValue())) {
            String configValue = sysConfig.getConfigValue();
            List<Long> wearIds = NumberUtils.stringToLong(configValue.split(","));
            log.info("给新用户发放免费服装,userId:{},服装列表：{}", userId, wearIds);
            for (Long wearId : wearIds) {
                Wears wears = wearsService.selectWearsById(wearId);
                if (Objects.nonNull(wears)) {
                    UserWearsBag userWearsBag = new UserWearsBag();
                    userWearsBag.setWearType(wears.getWearType());
                    userWearsBag.setWearName(wears.getWearName());
                    userWearsBag.setWearImageUrl(wears.getWearImageUrl());
                    userWearsBag.setWearValue(wears.getWearId());
                    userWearsBag.setUserId(userId);
                    userWearsBagService.insert(userWearsBag);
                }
            }
        }
    }


    /**
     * 填充用户服装
     *
     * @param userWearTypeEnum
     * @param one
     * @param wearValue
     * @param list
     */
    private void fillWearValue(UserWearTypeEnum userWearTypeEnum, ZnsUserWearsEntity one, Integer wearValue, List<Integer> list) {
        switch (userWearTypeEnum) {
            case HAIR_COLOR -> one.setHairColor(wearValue);
            case SKIN_COLOUR -> one.setSkinColour(wearValue);
            case HEAD -> one.setHead(wearValue);
            case FACE_DECORATION -> one.setFaceDecoration(wearValue);
            case JACKET -> one.setJacket(wearValue);
            case TROUSERS -> one.setTrousers(wearValue);
            case SHOES -> one.setShoes(wearValue);
            case BACK_DECORATION -> one.setBackDecoration(wearValue);
            case SUIT -> dealSuit(userWearTypeEnum, one, wearValue, list);
        }
    }

    /**
     * 套装处理
     *
     * @param userWearTypeEnum
     * @param one
     * @param wearValue
     * @param list
     */
    private void dealSuit(UserWearTypeEnum userWearTypeEnum, ZnsUserWearsEntity one, Integer wearValue, List<Integer> list) {
        one.setSuit(wearValue);
        Wears awardWear = wearsService.getWearByWearIdAndType(userWearTypeEnum.getType(), wearValue);
        List<Integer> awardSuitList = JsonUtil.readList(awardWear.getAttireTypeIncluded(), Integer.class);
        awardSuitList.addAll(list);
        awardSuitList.forEach(s -> {
            if (Objects.equals(UserWearTypeEnum.BACK_DECORATION.getType(), s)) {
                fillWearValue(UserWearTypeEnum.findByType(s), one, -1, new ArrayList<>());
            } else {
                fillWearValue(UserWearTypeEnum.findByType(s), one, 0, new ArrayList<>());
            }
        });
    }

    /**
     * 生成机器人服装
     *
     * @param userId
     * @param isRobot
     * @return
     */
    private ZnsUserWearsEntity generateRobotWear(Long userId, Integer isRobot) {
        if (Objects.isNull(userId) || Objects.isNull(isRobot)) {
            return null;
        }
        if (isRobot == 0) {
            log.info("只能自动添加机器人服装信息");
            return null;
        }
        ZnsUserWearsEntity userWearsEntity = new ZnsUserWearsEntity();
        userWearsEntity.setUserId(userId);
        //1:发色（头发、帽子）、2:肤色、3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子
        userWearsEntity.setHead(isSuccess() ? RandomUtils.nextInt(0, 6) : wearsService.getRandomWear(3));
        userWearsEntity.setSkinColour(isSuccess() ? RandomUtils.nextInt(0, 6) : wearsService.getRandomWear(2));
        userWearsEntity.setHairColor(isSuccess() ? RandomUtils.nextInt(0, 6) : wearsService.getRandomWear(1));
        userWearsEntity.setJacket(isSuccess() ? RandomUtils.nextInt(0, 6) : wearsService.getRandomWear(5));
        userWearsEntity.setShoes(isSuccess() ? RandomUtils.nextInt(0, 6) : wearsService.getRandomWear(7));
        userWearsEntity.setFaceDecoration(wearsService.getRandomWear(4));
        userWearsEntity.setTrousers(isSuccess() ? RandomUtils.nextInt(0, 6) : wearsService.getRandomWear(6));
        return userWearsEntity;
    }

    public static boolean isSuccess() {
        Random random = new Random();
        return random.nextFloat() < 0.8;
    }


    /**
     * 填充旧活动服装信息
     *
     * @param value
     * @param znsRunActivityEntity
     * @param loginUser
     * @param query
     * @param activityList
     * @param exchangeWearList
     */
    private void fillOldActivityWear(List<WearAward> value, ZnsRunActivityEntity znsRunActivityEntity, ZnsUserEntity loginUser, WearQuery query, List<MyWearResp> activityList, List<MyWearResp> exchangeWearList) {
        if (CollectionUtils.isEmpty(value)) {
            return;
        }
        //活动条件过滤填充MyWearResp属性
        for (WearAward wearAward : value) {
            if (Objects.nonNull(znsRunActivityEntity) && Objects.equals(znsRunActivityEntity.getStatus(), 1)
                    && Objects.equals(znsRunActivityEntity.getIsShowAll(), 1)
                    && znsRunActivityEntity.getApplicationEndTime().isAfter(ZonedDateTime.now())
                    && znsRunActivityEntity.getActivityEndTime().isAfter(ZonedDateTime.now())
                    && (znsRunActivityEntity.getCountry().equals("all") || znsRunActivityEntity.getCountry().equals(loginUser.getCountry()))) {
                if (Objects.equals(query.getWearType(), 0) || Objects.equals(wearAward.getWearType(), query.getWearType())) {
                    MyWearResp myWearResp = new MyWearResp();
                    BeanUtils.copyProperties(wearAward, myWearResp);
                    myWearResp.setWearSource(3);
                    myWearResp.setObtainActivityId(znsRunActivityEntity.getId());
                    Wears wear = wearsService.findWearByTypeId(wearAward.getWearType(), wearAward.getWearId());
                    fillWearResp(loginUser.getGender(), myWearResp, wear);
                    //填充跳转地址
                    PrimitiveForest primitiveForest = new PrimitiveForest();
                    primitiveForest.setJumpType(2);
                    primitiveForest.setRunActivityId(znsRunActivityEntity.getId());
                    primitiveForest.setRunActivityType(znsRunActivityEntity.getActivityType());
                    AppRoute route = appRouteService.findRoute(primitiveForest);
                    myWearResp.setActivityJumpUrl(route.getJumpUrl());
                    if (StringUtils.hasText(route.getJumpParam())) {
                        myWearResp.setActivityJumpParam(route.getJumpParam());
                    }
                    myWearResp.setStartTime(znsRunActivityEntity.getActivityStartTime());
                    myWearResp.setCreatTime(znsRunActivityEntity.getCreateTime());
                    activityList.add(myWearResp);
                }
                if (!CollectionUtils.isEmpty(exchangeWearList)) {
                    List<MyWearResp> collect = exchangeWearList.stream().filter(s -> Objects.equals(s.getWearType(), wearAward.getWearType()) && Objects.equals(s.getWearId(), wearAward.getWearId())).collect(Collectors.toList());
                    //积分兑换剔除活动可以获取
                    exchangeWearList.removeAll(collect);
                }
            }
        }
    }


    /**
     * 填充穿着信息
     *
     * @param gender
     * @param myWearResp
     * @param wearsBag
     */
    public void fillWearResp(Integer gender, MyWearResp myWearResp, Wears wearsBag) {
        if (Objects.nonNull(wearsBag)) {
            if (gender <= 1) {
                // 配合游戏端规则：<=1为男，其他为女
                myWearResp.setWearImageUrl(wearsBag.getMenWearUrl());
            } else {
                myWearResp.setWearImageUrl(wearsBag.getWomenWearUrl());
            }
            // 国际化数据
            String langCode = I18nMsgUtils.getLangCode();
            WearsI18n i18n = wearsI18nService.findByQuery(WearsI18nQuery.builder().clothId(wearsBag.getId()).langCode(langCode).defaultLangCode(wearsBag.getDefaultLangCode()).build());
            myWearResp.setWearName(Objects.nonNull(i18n) ? i18n.getName() : wearsBag.getWearName());
            String attireTypeIncluded = wearsBag.getAttireTypeIncluded();
            if (Objects.nonNull(attireTypeIncluded)) {
                myWearResp.setAttireTypeIncludedList(JsonUtil.readList(attireTypeIncluded, Integer.class));
            }
        }
    }

    /**
     * 获取用户币种
     *
     * @param loginUser
     * @return
     */
    private Currency getUserCurrency(ZnsUserEntity loginUser) {
        ZnsUserAccountEntity accountEntity = userAccountService.getUserAccount(loginUser.getId());
        if (accountEntity == null) {
            //默认使用美元
            return I18nConstant.CurrencyCodeEnum.USD.getCurrency();
        }
        return I18nConstant.buildCurrency(accountEntity.getCurrencyCode());
    }

    private Set<String> scan(String matchKey) {
        Set<String> res = new HashSet<>();
        redisTemplate.execute(connect -> {
            Set<String> binaryKeys = new HashSet<>();
            Cursor<byte[]> cursor = connect.scan(ScanOptions.scanOptions().match(matchKey).count(50000).build());
            while (cursor.hasNext() && binaryKeys.size() < 50000) {
                binaryKeys.add(new String(cursor.next()));
            }
            res.addAll(binaryKeys);
            return binaryKeys;
        }, true);
        return res;
    }

    /**
     * 新版本兼容服装缓存数据
     *
     * @param suitList
     * @param wearAwards
     * @return
     */
    private List<WearAward> getWearAwards(List<Integer> suitList, List<WearAward> wearAwards) {
        String appType = "ios";// ios的版本更加准确
        Integer versionCode = appUpgradeService.selectShelvedLatestVersion(appType);
        if (versionCode >= 2120) {
            wearAwards = wearAwards.stream().map(e -> {
                if (suitList.contains(e.getWearId()) && UserWearTypeEnum.JACKET.getType().equals(e.getWearType())) {
                    e.setWearType(UserWearTypeEnum.SUIT.getType());
                }
                return e;
            }).collect(Collectors.toList());
        }
        return wearAwards;
    }


    /**
     * 用户 完成里程跑后，发放服装奖励
     *
     * @param milepostCouponAwardDto
     * @param activityUserEntity
     * @return
     */
    public Long sendUserWearMilepost(MilepostWearAwardDto milepostCouponAwardDto, OfficialCumulativeRunDto activityUserEntity) {
        log.info("sendUserWearMilepost milepostCouponAwardDto:{},user_id:{}", milepostCouponAwardDto, activityUserEntity.getUserId());
        UserWearBagQuery userWearBagQuery = UserWearBagQuery.builder()
                .userId(activityUserEntity.getUserId())
                .wearType(milepostCouponAwardDto.getWearType())
                .wearValue(milepostCouponAwardDto.getWearValue())
                .build();
        UserWearsBag existUserWearsBag = userWearsBagService.findByQuery(userWearBagQuery);

        //log check
        if (Objects.nonNull(existUserWearsBag)) {
            UserWearsBagLog bagSendLog = userWearsBagLogService.getBagSendLog(milepostCouponAwardDto, activityUserEntity, existUserWearsBag.getId());
            if (Objects.nonNull(bagSendLog)) {
                log.warn("用户已经获取该阶段服装奖励 bag_id:{},bag_log_id:{}", bagSendLog.getId(), bagSendLog.getBagId());
                return existUserWearsBag.getId();
            }
        }
        if (Objects.nonNull(existUserWearsBag) && existUserWearsBag.getExpiredTime() == null) {
            log.warn("用户已经获取了该服饰奖励: {}, 从活动获取的:{}#{}新奖励将被丢弃", existUserWearsBag.getId(), activityUserEntity.getActivityId(), milepostCouponAwardDto.getMilepost().intValue());
            UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
            userWearsBagLog.setBagId(existUserWearsBag.getId());
            userWearsBagLog.setExpiredTime(milepostCouponAwardDto.getExpiredTime());
            userWearsBagLog.setSortNum(milepostCouponAwardDto.getSort());
            userWearsBagLog.setActivityId(activityUserEntity.getActivityId());
            userWearsBagLog.setUserId(activityUserEntity.getUserId());
            userWearsBagLog.setIsNew(YesNoStatus.NO.getCode());
            userWearsBagLog.setSource(milepostCouponAwardDto.getSource());
            userWearsBagLogService.insert(userWearsBagLog);
            milepostCouponAwardDto.setLogId(userWearsBagLog.getId());
            return existUserWearsBag.getId();
        } else {
            UserWearsBag userWearsBag = UserWearsBag.builder()
                    .wearType(milepostCouponAwardDto.getWearType())
                    .wearName(milepostCouponAwardDto.getWearName())
                    .wearValue(milepostCouponAwardDto.getWearValue())
                    .wearImageUrl(milepostCouponAwardDto.getWearImageUrl())
                    .isNew(1)
                    .userId(activityUserEntity.getUserId())
                    .activityId(activityUserEntity.getActivityId())
                    .milepostId(milepostCouponAwardDto.getSort())
                    .expiredTime(Objects.isNull(milepostCouponAwardDto.getExpiredTime()) ? null : DateUtil.getAddHours(milepostCouponAwardDto.getExpiredTime()))
                    .build();
            if (Objects.nonNull(existUserWearsBag)) {
                UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
                userWearsBagLog.setBagId(existUserWearsBag.getId());
                userWearsBagLog.setSortNum(milepostCouponAwardDto.getSort());
                userWearsBagLog.setActivityId(activityUserEntity.getActivityId());
                userWearsBagLog.setUserId(activityUserEntity.getUserId());
                userWearsBagLog.setSource(milepostCouponAwardDto.getSource());
                if (Objects.nonNull(userWearsBag.getExpiredTime())) {
                    existUserWearsBag.setExpiredTime(DateUtil.addHours(existUserWearsBag.getExpiredTime(), milepostCouponAwardDto.getExpiredTime() * 24));
                    userWearsBagLog.setExpiredTime(milepostCouponAwardDto.getExpiredTime());
                } else {
                    if (Objects.isNull(milepostCouponAwardDto.getExpiredTime())) {
                        existUserWearsBag.setExpiredTime(null);
                    }
                    userWearsBagLog.setExpiredTime(0);
                }
                existUserWearsBag.setGmtModified(ZonedDateTime.now());
                userWearsBagService.update(existUserWearsBag);
                userWearsBagLogService.insert(userWearsBagLog);
                milepostCouponAwardDto.setLogId(userWearsBagLog.getId());
                return 1L;
            } else {
                userWearsBagService.insert(userWearsBag);
                UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
                userWearsBagLog.setBagId(userWearsBag.getId());
                userWearsBagLog.setExpiredTime(milepostCouponAwardDto.getExpiredTime());
                userWearsBagLog.setSortNum(milepostCouponAwardDto.getSort());
                userWearsBagLog.setActivityId(activityUserEntity.getActivityId());
                userWearsBagLog.setUserId(activityUserEntity.getUserId());
                userWearsBagLog.setSource(milepostCouponAwardDto.getSource());
                userWearsBagLogService.insert(userWearsBagLog);
                milepostCouponAwardDto.setLogId(userWearsBagLog.getId());
                return 1L;
            }
        }
    }

    /**
     * 根据业务key发送奖励
     *
     * @param wearAwardDto 奖励信息
     * @param sourceKey    业务key
     * @param source       业务来源
     * @param userId       用户id
     */
    public void sendUserAwardByKey(WearAwardDto wearAwardDto, Integer source, String sourceKey, Long userId) {

        log.info("[sendUserAwardByKey]--------根据业务key发送奖励,开始，sourceKey={},user_id={},wearAwardDto={}", sourceKey, userId, JsonUtil.writeString(wearAwardDto));
        UserWearBagQuery userWearBagQuery = UserWearBagQuery.builder()
                .userId(userId)
                .wearType(wearAwardDto.getWearType())
                .wearValue(wearAwardDto.getWearValue())
                .build();
        UserWearsBag existUserWearsBag = userWearsBagService.findByQuery(userWearBagQuery);

        if (Objects.nonNull(existUserWearsBag) && existUserWearsBag.getExpiredTime() == null) {
            log.warn("[sendUserAwardByKey]--------根据业务key发送奖励,奖励已发放过永久奖励,sourceKey={},user_id={},bagId={} ", sourceKey, userId, existUserWearsBag.getId());
            UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
            userWearsBagLog.setBagId(existUserWearsBag.getId());
            userWearsBagLog.setUserId(userId);
            userWearsBagLog.setIsNew(YesNoStatus.NO.getCode());
            userWearsBagLog.setSource(source);
            userWearsBagLog.setSourceKey(sourceKey);
            userWearsBagLogService.insert(userWearsBagLog);
            return;
        }
        UserWearsBag userWearsBag = UserWearsBag.builder()
                .wearType(wearAwardDto.getWearType())
                .wearName(wearAwardDto.getWearName())
                .wearValue(wearAwardDto.getWearValue())
                .wearImageUrl(wearAwardDto.getWearImageUrl())
                .isNew(1)
                .source(source)
                .userId(userId)
                .expiredTime(Objects.isNull(wearAwardDto.getExpiredTime()) ? null : DateUtil.getAddHours(wearAwardDto.getExpiredTime()))
                .build();
        if (Objects.nonNull(existUserWearsBag) && existUserWearsBag.getExpiredTime() != null) {
            log.warn("[sendUserAwardByKey]--------根据业务key发送奖励,奖励已发放过并且续期,sourceKey={},user_id={},bagId={} ", sourceKey, userId, existUserWearsBag.getId());
            UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
            userWearsBagLog.setBagId(existUserWearsBag.getId());
            userWearsBagLog.setUserId(userId);
            userWearsBagLog.setSource(source);
            userWearsBagLog.setSourceKey(sourceKey);
            if (Objects.nonNull(userWearsBag.getExpiredTime())) {
                existUserWearsBag.setExpiredTime(DateUtil.addHours(existUserWearsBag.getExpiredTime(), wearAwardDto.getExpiredTime() * 24));
                userWearsBagLog.setExpiredTime(wearAwardDto.getExpiredTime());
            } else {
                if (Objects.isNull(wearAwardDto.getExpiredTime())) {
                    existUserWearsBag.setExpiredTime(null);
                }
                userWearsBagLog.setExpiredTime(0);
            }
            existUserWearsBag.setGmtModified(ZonedDateTime.now());
            userWearsBagService.update(existUserWearsBag);
            userWearsBagLogService.insert(userWearsBagLog);
            return;
        }
        log.warn("[sendUserAwardByKey]--------根据业务key发送奖励,新发奖励,sourceKey={},user_id={}", sourceKey, userId);
        userWearsBagService.insert(userWearsBag);
        UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
        userWearsBagLog.setBagId(userWearsBag.getId());
        userWearsBagLog.setExpiredTime(wearAwardDto.getExpiredTime());
        userWearsBagLog.setUserId(userId);
        userWearsBagLog.setSource(source);
        userWearsBagLog.setSourceKey(sourceKey);
        userWearsBagLogService.insert(userWearsBagLog);
    }
}
