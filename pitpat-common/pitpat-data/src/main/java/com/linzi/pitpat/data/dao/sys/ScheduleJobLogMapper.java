/**
 * Copyright 2018 人人开源 http://www.renren.io
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.linzi.pitpat.data.dao.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.entity.sys.ScheduleJobLogEntity;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;

/**
 * 定时任务日志
 *
 * <AUTHOR>
 * @since 1.2.0 2016-11-28
 */
@Mapper
public interface ScheduleJobLogMapper extends BaseMapper<ScheduleJobLogEntity> {


    @OrderByIdDescLimit_1
    ScheduleJobLogEntity selectByBeanNameCreateTime(@Param("beanName") String beanName, @Param("createTime") ZonedDateTime createTime);
}
