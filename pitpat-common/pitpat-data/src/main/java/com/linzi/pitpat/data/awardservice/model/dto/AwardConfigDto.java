package com.linzi.pitpat.data.awardservice.model.dto;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/17 11:28
 */
@Data
@NoArgsConstructor
public class AwardConfigDto {

    private Long activityAwardId;
    /**
     * zns_award_config的id
     */
    private Long awardConfigId;
    /**
     * zns_award_amount_config的id
     */
    private Long awardConfigAmountId;
    /**
     * 奖励发放类型
     *
     * @see AwardSentTypeEnum
     */
    private Integer sendType;
    /**
     * 奖励类型
     *
     * @see AwardTypeEnum
     */
    private Integer awardType;
    /**
     * 奖励条件类型，0：无，1：付费(进阶)
     */
    private Integer awardCondition;
    //金额
    private BigDecimal amount;

    private Currency currency;
    //优惠券id数组
    private String couponIds;
    //勋章id
    private Long medalId;
    //积分
    private Integer score;
    /**
     * 衣服属性，值为wears表字段.hairColor:发色,skinColour:肤色,jacket:上衣,trousers：下装，shoes：鞋子，head：头型
     */
    private String wearType;
    //衣服属性值，例：1
    private Integer wearValue;
    //服装图片URL
    private String wearImageUrl;
    //服装名称
    private String wearName;
    //目标值（里程/时间）
    private Integer milepost;
    //目标值类型，0：公里 ，1：英里，2：时间（min）
    private Integer milepostType;
    /**
     * 排名
     */
    private Integer rank;
    /**
     * 比例
     */
    private BigDecimal proportion;
    /**
     * 目标里程
     */
    private Integer targetMileage;
    /**
     * 目标时长
     */
    private Integer targetTime;
    /**
     * 目标场次
     */
    private Integer targetCount;
    /**
     * 失效时间
     *
     * @tag 2.10.0
     */
    private Integer expiredTime;

    private Integer rankMax;
    /**
     * 多目标 目标值
     */
    private Integer target;
    /**
     * 是否瓜分，1：是，0：否
     */
    private Integer isDivide;
    /**
     * 勋章名称
     */
    private String medalName;

    private String medalImageUrl;
}
