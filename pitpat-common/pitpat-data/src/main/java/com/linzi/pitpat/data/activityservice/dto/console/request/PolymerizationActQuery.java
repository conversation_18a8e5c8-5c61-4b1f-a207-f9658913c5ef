package com.linzi.pitpat.data.activityservice.dto.console.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class PolymerizationActQuery {

    /**
     * zns_polymerization_activity_pole 主键ID
     */
    private Long Id;
    /**
     * 活动ID
     */
    @NotNull(message = "活动ID必传")
    private Long mainActivityId;
    /**
     * 自动创建状态(0:关闭，1：修改)
     */
    private Integer autoStatus;
    /**
     * 1:同一时间（仅推有一次)
     */
    private Integer pushType;
    /**
     * 任务周期开始时间（东八区）
     */
    private ZonedDateTime taskStartTime;
    /**
     * 任务周期结束时间（东八区）
     */
    private ZonedDateTime taskEndTime;

    /**
     * 任务开始生成时间(0:00->24:00)
     */
    private String generateTime;

    /**
     * 持续时间(1-24H)
     */
    private Long duration;

    /**
     * 生成频率 【每天(s):10 min，20min，30min，1h，2h，3h，6h，14h，1d，7d，15d，30d,每周(w):1-7】
     */
    private Long generateFrequency;

    /**
     * 任务类型，1：每天，2：每周
     */
    private Integer jobType;

}
