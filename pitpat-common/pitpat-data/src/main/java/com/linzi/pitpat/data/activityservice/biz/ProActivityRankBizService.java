package com.linzi.pitpat.data.activityservice.biz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.model.dto.ProActivityTimelineDto;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveUserScoreDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityDo;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserExtraDo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ProActivityTimelineQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserExtraQuery;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonRankRecalculateRecordService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveUserScoreService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.ProActivityService;
import com.linzi.pitpat.data.activityservice.service.ProActivityTimelineService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserExtraService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 职业赛积分发放
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ProActivityRankBizService {
    private final CompetitiveUserScoreService competitiveUserScoreService;

    private final MainActivityService mainActivityService;

    private final ZnsRunActivityUserService runActivityUserService;

    private final RedissonClient redissonClient;

    private final ActivityUserAwardBizService activityUserAwardBizService;

    private final TransactionTemplate transactionTemplate;


    private final CompetitiveSeasonRankRecalculateRecordService recalculateRecordService;
    private final ZnsTreadmillService treadmillService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ProActivityService proActivityService;
    private final ProActivityTimelineService proActivityTimelineService;

    private final RunActivityUserExtraService runActivityUserExtraService;


    /**
     * 发送职业赛分数
     */
    public void sendCompetitiveScore(Long mainActivityId) {
        RLock lock = redissonClient.getLock("sendCompetitiveScore");
        LockHolder.tryLock(lock, 5L, () -> {
            transactionTemplate.executeWithoutResult((state) -> {
                log.info("[职业赛][竞技分] 开始竞技分发放：{}", mainActivityId);
                MainActivity mainActivity = mainActivityService.findById(mainActivityId);
                //1. 不需要审核  。2。奖励审核完成  3. 审核发放中。均可以进行奖励分的方法
                if (AwardSendStatusEnum.NO_VIEW_CONFIG.getCode().equals(mainActivity.getAwardSendStatus())
                        || AwardSendStatusEnum.VIEW_PASS.getCode().equals(mainActivity.getAwardSendStatus())
                        || AwardSendStatusEnum.VIEW_SENDING.getCode().equals(mainActivity.getAwardSendStatus())) {


                    boolean isProActivity = proActivityService.isProActivity(mainActivityId);
                    if (!isProActivity) {
                        return;
                    }
                    Optional<ProActivityDo> proActivityDoOptional = proActivityService.findByMainActivityId(mainActivityId);
                    ProActivityDo proActivityDo = proActivityDoOptional.get();

                    if (proActivityDo.getRankStatus()) {
                        log.info("[职业赛][排行榜] 排行榜计算完成,mainActivityId:{}", mainActivityId);
                        return;
                    }
                    //下发积分
                    long pageIndex = 1L;
                    long pageSize = 100L;
                    Page<ZnsRunActivityUserEntity> pageActivityCompleteUser = runActivityUserService.findPageHaveRankUser(mainActivityId, pageIndex++, pageSize);
                    while (!CollectionUtils.isEmpty(pageActivityCompleteUser.getRecords())) {
                        List<ZnsRunActivityUserEntity> records = pageActivityCompleteUser.getRecords();
                        List<CompetitiveUserScoreDo> scoreDoList = new ArrayList<>();

                        for (ZnsRunActivityUserEntity znsRunActivityUserEntity : records) {
                            if (znsRunActivityUserEntity.getRank() != -1) {
                                CompetitiveUserScoreDo competitiveUserScoreDo = new CompetitiveUserScoreDo();
                                competitiveUserScoreDo.setUserId(znsRunActivityUserEntity.getUserId())
                                        .setActivityId(mainActivityId)
                                        .setActivityRank(znsRunActivityUserEntity.getRank());
                                //查询本次比赛得到框所有金额
                                competitiveUserScoreDo.setBounds(activityUserAwardBizService.sumUserActivityGainsAmount(mainActivityId, competitiveUserScoreDo.getUserId()));
                                RunActivityUserExtraDo userExtraDo = runActivityUserExtraService.findByQuery(new RunActivityUserExtraQuery().setUserId(znsRunActivityUserEntity.getUserId()).setMainActivityId(znsRunActivityUserEntity.getActivityId()));
                                if (userExtraDo != null) {
                                    competitiveUserScoreDo.setGender(userExtraDo.getGender());
                                }
                                scoreDoList.add(competitiveUserScoreDo);
                            }
                        }
                        //设置设备信息
                        List<ZnsUserRunDataDetailsEntity> details = userRunDataDetailsService.findByIds(records.stream().map(ZnsRunActivityUserEntity::getRunDataDetailsId).toList());
                        if (!CollectionUtils.isEmpty(details)) {
                            List<Long> treadmillIds = details.stream().map(ZnsUserRunDataDetailsEntity::getTreadmillId).toList();
                            List<ZnsTreadmillEntity> byIds = treadmillService.findByIds(treadmillIds);
                            scoreDoList.forEach(i -> {
                                details.stream().filter(detail -> i.getUserId().equals(detail.getUserId())).findFirst().ifPresent(detail -> {
                                    i.setTreadmillId(detail.getTreadmillId());
                                    byIds.stream().filter(t -> t.getId().equals(i.getTreadmillId())).findFirst().ifPresent(t -> {
                                        i.setTreadmillProductCode(t.getProductCode());
                                    });
                                });
                            });
                        }
                        competitiveUserScoreService.batchCreate(scoreDoList);
                        pageActivityCompleteUser = runActivityUserService.findPageHaveRankUser(mainActivityId, pageIndex++, pageSize);
                    }
                    //记录发放状态
                    proActivityService.rankFinish(proActivityDo.getId());
                    //记录排名更新任务,查询所有与当前获取匹配的赛季
                    ZonedDateTime startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
                    List<ProActivityTimelineDto> list = proActivityTimelineService.findList(new ProActivityTimelineQuery().setStartTimeGe(startTime).setEndTimeLe(startTime));

                    //写入年榜
                    if (!CollectionUtils.isEmpty(list)) {
                        //写入重算任务;
                        recalculateRecordService.batchSaveWaitRecalculateProActivityRecord(list.stream().map(ProActivityTimelineDto::getId).collect(Collectors.toList()));
                    }
                } else {
                    log.info("[职业赛][发放竞技分] 竞技分发放失败,活动发奖状态不对：{}，{}", mainActivityId, mainActivity.getAwardSendStatus());
                }
            });
        });
    }
}
