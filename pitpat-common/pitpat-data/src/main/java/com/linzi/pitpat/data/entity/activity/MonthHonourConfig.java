package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-02-25
 */

@Data
@NoArgsConstructor
@TableName("zns_month_honour_config")
public class MonthHonourConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.activity.MonthHonourConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";          //
    public final static String gmt_create = CLASS_NAME + "gmt_create";        //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    //
    public final static String type_ = CLASS_NAME + "type";                   // 类型
    public final static String logic_ = CLASS_NAME + "logic";                 // 逻辑
    public final static String ch_name = CLASS_NAME + "ch_name";              // 中文名
    public final static String en_name = CLASS_NAME + "en_name";              // 英文名
    public final static String ch_remark = CLASS_NAME + "ch_remark";          // 中文备注
    public final static String en_remark = CLASS_NAME + "en_remark";          // 英文备注
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //类型
    private Integer type;
    //逻辑
    private String logic;
    //中文名
    private String chName;
    //英文名
    private String enName;
    //中文备注
    private String chRemark;
    //英文备注
    private String enRemark;
    // 默认语言
    private String defaultLangCode;

    @Override
    public String toString() {
        return "MonthHonourConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",type=" + type +
                ",logic=" + logic +
                ",chName=" + chName +
                ",enName=" + enName +
                ",chRemark=" + chRemark +
                ",enRemark=" + enRemark +
                "}";
    }
}
