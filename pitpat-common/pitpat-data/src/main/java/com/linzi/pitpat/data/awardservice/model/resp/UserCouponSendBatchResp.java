package com.linzi.pitpat.data.awardservice.model.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户批次发放优惠券表
 *
 * <AUTHOR>
 * @since 2023-04-17
 */

@Data
@NoArgsConstructor
public class UserCouponSendBatchResp implements java.io.Serializable {
    private Long id;
    //创建者
    private String creator;
    //创建时间
    private ZonedDateTime gmtCreate;
    //状态 【 0: 未生效/待审核 1 生效/审核通过 -1 失效/撤销 2：审核拒绝】
    private Integer status;
    //发送状态 【 0: 未发送 1 发送成功 -1 发送失败 】
    private Integer sendStatus;
    //标题
    private String title;
    // 活动id，只有现金奖励有
    private Long activityId;
    //卷类型 券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券】
    private Integer couponType;
    //卷名称
    private String name;
    /**
     * 是否 卷包 1 是 类型展示卷包 0 否直接展示映射类型
     */
    private Integer isCouponPackage;

    //是否奖金发放
    private Boolean isCashAmountSend;

}
