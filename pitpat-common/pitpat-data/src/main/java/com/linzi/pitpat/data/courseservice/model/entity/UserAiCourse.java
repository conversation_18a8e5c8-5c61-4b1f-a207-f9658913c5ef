package com.linzi.pitpat.data.courseservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户ai课程计划表
 *
 * <AUTHOR>
 * @since 2023-05-25
 */

@Data
@NoArgsConstructor
@TableName("zns_user_ai_course")
public class UserAiCourse implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.courseservice.model.entity.UserAiCourse:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";        // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    // 最后修改时间
    public final static String course_id = CLASS_NAME + "course_id";          // 课程id
    public final static String user_id = CLASS_NAME + "user_id";              // 用户id
    public final static String status_ = CLASS_NAME + "status";               // 课程状态 0 未完成 1 完成
    public final static String baseinfo_id = CLASS_NAME + "baseinfo_id";      // 用户ai 信息关联id
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //课程id
    private Long courseId;
    //用户id
    private Long userId;
    //课程状态 0 未完成 1 完成
    private Integer status;
    //用户ai 信息关联id
    private Long baseinfoId;

    @Override
    public String toString() {
        return "UserAiCourse{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",courseId=" + courseId +
                ",userId=" + userId +
                ",status=" + status +
                ",baseinfoId=" + baseinfoId +
                "}";
    }
}
