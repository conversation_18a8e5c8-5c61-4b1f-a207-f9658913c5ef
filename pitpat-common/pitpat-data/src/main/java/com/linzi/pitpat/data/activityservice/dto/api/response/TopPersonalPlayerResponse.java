package com.linzi.pitpat.data.activityservice.dto.api.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.device.RunDataTreadmillSceneDataFiller;
import com.linzi.pitpat.data.filler.user.UserCountryDataFiller;
import com.linzi.pitpat.data.filler.user.UserCountryFlagDataFiller;
import com.linzi.pitpat.data.filler.user.UserHeadPortraitDataFiller;
import com.linzi.pitpat.data.filler.user.UserNickNameDataFiller;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@Data
@Accessors(chain = true)
public class TopPersonalPlayerResponse {
    /**
     * 国家简称
     */
    @Filler(relationFieldName = "userId", filler = UserCountryDataFiller.class)
    private String country;
    /**
     * 头像
     */
    @Filler(relationFieldName = "userId", filler = UserHeadPortraitDataFiller.class)
    private String headPortrait;
    /**
     * 排名
     */
    private Integer rank;
    /**
     * 时间成绩
     */
    private Integer runTime;
    /**
     * 设备位置
     */
    @Filler(relationFieldName = "runDataDetailsId", filler = RunDataTreadmillSceneDataFiller.class)
    private String deviceLocation;
    /**
     * 昵称
     */
    @Filler(relationFieldName = "userId", filler = UserNickNameDataFiller.class)
    private String firstName;
    /**
     * 国旗
     */
    @Filler(relationFieldName = "userId", filler = UserCountryFlagDataFiller.class)
    private String flag;
    /**
     * 显示文本
     */
    private String displayText;
    /**
     * 是否上榜，1：是，0：否
     */
    private Integer onLeaderboard = 0;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 运动数据详情id
     */
    private Long runDataDetailsId;
    /**
     * 是否发放奖励
     */
    @JsonIgnore
    private Boolean sendRankAwardStatus = false;
}
