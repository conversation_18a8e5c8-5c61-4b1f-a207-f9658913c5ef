package com.linzi.pitpat.data.activityservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.DingTalkTokenEnum;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.model.query.PkChallengeRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunAwardQuery;
import com.linzi.pitpat.data.activityservice.model.resp.GuanFangZhuiDuiResp;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SpeedPropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.activityservice.strategy.BaseOfficialActivityStrategy;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.domian.query.SpeedPropRecordQuery;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigHistoryEntity;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigHistoryService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserFriendEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动用户处理类
 *
 * <AUTHOR>
 * @date 2024/6/5 18:36
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ActivityUserManager {
    private final ZnsRunActivityUserService activityUserService;
    private final ZnsUserFriendService userFriendService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsUserAccountService userAccountService;
    private final AppMessageService appMessageService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserCouponService userCouponService;
    private final PkChallengeRecordService pkChallengeRecordService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ExchangeRateConfigHistoryService exchangeRateConfigHistoryService;
    private final UserPropRecordService userPropRecordService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ZnsUserRunRecordService userRunRecordService;
    private final ZnsUserService userService;
    private final RedisTemplate redisTemplate;
    private final MainActivityService mainActivityService;
    private final OperationalActivityService operationalActivityService;
    private final SpeedPropRecordService speedPropRecordService;

    @Value("${queue.config.puls_msg_at_phone:18625205568}")
    private String puls_msg_at_phone;

    @Value("${spring.profiles.active}")
    private String profile;


    /**
     * 查询活动用户
     *
     * @param activityEntity     跑步活动
     * @param activityDetailVO
     * @param targetRunMileage
     * @param targetRunTime
     * @param activityUserStatus
     */
    public List<RunActivityUserVO> findRunActivityUsers(ZnsRunActivityEntity activityEntity, Page page, Long userId, RunActivityDetailVO activityDetailVO, Integer targetRunMileage, Integer targetRunTime, Integer activityUserStatus) {
        // 查询用户所有好友
        List<Long> friendUserIdList = userFriendService.friendUserIdList(userId);
        // 官方赛事查询看挑战奖励
        Map<String, Object> challengeAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_AWARD);
        // 官方赛事查询比赛结束奖励，预计
        Map<String, Object> officialAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_AWARD);
        Integer activityState = null;
        ZonedDateTime date = DateTimeUtil.parse("2022-11-28 04:00:00");
        if (Objects.nonNull(activityEntity.getActivityEndTime()) && activityEntity.getActivityEndTime().compareTo(date) > 0) {
            activityState = activityEntity.getActivityState();
        }
        String currencyCode1 = userAccountService.getUserCurrency(userId).getCurrencyCode();
        //查询奖励
        Map<Long, ActivityUserScore> scoreMap = null;
        Map<Long, List<UserCoupon>> userCouponMap = null;
        if (activityEntity.getActivityType() == 1 || activityEntity.getActivityType() == 2) {
            List<ActivityUserScore> activityUserScores = activityUserScoreService.selectActivityUserScoreByActivityId(activityEntity.getId());
            scoreMap = activityUserScores.stream().filter(s -> Objects.isNull(s.getSource())).collect(Collectors.toMap(ActivityUserScore::getUserId, Function.identity(), (x, y) -> x));
            //券
            List<UserCoupon> userCoupons = userCouponService.selectUserCouponByActivityIdAndSource(activityEntity.getId(), 6);
            userCouponMap = userCoupons.stream().collect(Collectors.groupingBy(UserCoupon::getUserId));
        }

        // 查询所有活动用户
        List<RunActivityUserVO> runActivityUsers = activityUserService.findRunActivityUsers(page, activityEntity.getId(), activityEntity.getActivityType(), activityEntity.getCompleteRuleType(), targetRunMileage, targetRunTime, userId, activityState, activityUserStatus);
        if (!CollectionUtils.isEmpty(runActivityUsers)) {
            List<Long> friendIds = runActivityUsers.stream().map(RunActivityUserVO::getUserId).collect(Collectors.toList());
            List<Long> followingIdList = userFriendService.getFollowingIdList(userId, friendIds);
            Map<Long, ZnsUserEntity> userMap = userService.findByIds(friendIds).stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));

            Integer isAllAccepted = 1;
            for (int i = 0; i < runActivityUsers.size(); i++) {
                RunActivityUserVO activityUserVO = runActivityUsers.get(i);
                if (Objects.isNull(activityUserVO.getRunMileage())) {
                    activityUserVO.setRunMileage(BigDecimal.ZERO);
                }
                if (Objects.isNull(activityUserVO.getRunTime())) {
                    activityUserVO.setRunTime(0);
                }
                if (Objects.isNull(activityUserVO.getRunTimeMillisecond()) || activityUserVO.getRunTimeMillisecond() == 0) {
                    activityUserVO.setRunTimeMillisecond(activityUserVO.getRunTime() * 1000 + 999);
                }
                if (activityUserVO.getRunMileage().compareTo(BigDecimal.ZERO) == 0) {
                    activityUserVO.setPace(0);
                } else {
                    activityUserVO.setPace(SportsDataUnit.getPace(activityUserVO.getRunTime(), BigDecimalUtil.divHalfDown(activityUserVO.getRunMileage(), new BigDecimal(1000), 4)));
                }
                ZnsUserEntity user = userMap.get(activityUserVO.getUserId());
                if (Objects.nonNull(user)) {
                    activityUserVO.setIsPrivacy(user.getIsPrivacy());
                }
                // 是否是好友
                if (!CollectionUtils.isEmpty(friendUserIdList) && friendUserIdList.contains(activityUserVO.getUserId())) {
                    activityUserVO.setIsFriend(1);
                } else {
                    activityUserVO.setIsFriend(0);
                }
                activityUserVO.setIsFollow(followingIdList.contains(activityUserVO.getUserId()));
                // 是否是自己
                if (null != userId && userId.equals(activityUserVO.getUserId())) {
                    activityUserVO.setIsMe(1);
                    activityUserVO.setIsFollow(null);
                } else {
                    activityUserVO.setIsMe(0);
                }
                // 设置挑战奖励
                Integer rank = i + 1;
                if (activityEntity.getActivityType() == 3) {
                    activityUserVO.setRank(rank);
                }
                String rankStr = String.valueOf(rank);
                if (null != challengeAwardJson) {
                    if (null != challengeAwardJson.get(rankStr)) {
                        activityUserVO.setChallengeAward(String.valueOf(challengeAwardJson.get(rankStr)));
                    } else if (rank > 10 && activityUserVO.getIsMe() == 1) {
                        activityUserVO.setChallengeAward(String.valueOf(challengeAwardJson.get("-1")));
                    } else {
                        activityUserVO.setChallengeAward("0.00");
                    }
                } else {
                    activityUserVO.setChallengeAward("0.00");
                }
                if (activityEntity.getActivityType() == 3 && activityUserVO.getRunAward().compareTo(BigDecimal.ZERO) <= 0) {
                    BigDecimal award = runActivityService.officialEventRankAward(rank, officialAwardJson);
                    award = I18nConstant.currencyFormat(currencyCode1, award);
                    activityUserVO.setRunAward(award);
                }
                if (Objects.equals(activityEntity.getActivityTypeSub(), 3)) {
                    // 活动状态：0表示未开始，1 表示进行中，2表示已结束，-1表示活动已取消
                    // 如果是 0，1，-1 ，离线pk 双方的奖励都为0
                    if (Arrays.asList(0, 1, -1).contains(activityEntity.getActivityState())) {
                        activityUserVO.setRunAward(BigDecimal.ZERO);
                    } else {
                        // 如果跑步已经结束
                        PkChallengeRecord challengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(activityUserVO.getActivityId(), 1);
                        PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(activityEntity.getId()).userId(userId).build();
                        PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.findByQuery(query);
                        ZnsRunActivityUserEntity znsRunActivityUserEntity = activityUserService.findActivityUser(activityUserVO.getActivityId(), challengeRecord.getUserId());
                        // 判断用户跑的距离是否大于等于1英里 ，如果大于，才给他展示奖励
                        if (znsRunActivityUserEntity.getRunMileage().compareTo(new BigDecimal(Constants.target_1_Mile)) >= 0) {
                            if (1 == activityUserVO.getRank()) {
                                if (pkChallengeRecord != null) {
                                    activityUserVO.setCurrency(I18nConstant.buildDefaultCurrency());
                                    if (StringUtils.hasText(pkChallengeRecord.getCurrencyCode())) {
                                        // 新活动
                                        activityUserVO.setCurrency(I18nConstant.buildCurrency(pkChallengeRecord.getCurrencyCode()));
                                    }
                                    BigDecimal award = I18nConstant.currencyFormat(currencyCode1, pkChallengeRecord.getAward());
                                    activityUserVO.setRunAward(award);
                                    activityUserVO.setScoreAward(pkChallengeRecord.getScore());
                                    activityUserVO.setCouponAward(pkChallengeRecord.getCouponNum());

                                } else {
                                    // h5页面默认按美元展示
                                    activityUserVO.setCurrency(I18nConstant.buildDefaultCurrency());
                                    if (Objects.isNull(challengeRecord.getCurrencyCode())
                                            || I18nConstant.CurrencyCodeEnum.USD.getCode().equals(challengeRecord.getCurrencyCode())) {
                                        BigDecimal award = I18nConstant.currencyFormat(currencyCode1, challengeRecord.getAward());
                                        activityUserVO.setRunAward(award);
                                    } else {
                                        BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), challengeRecord.getCurrencyCode()).getExchangeRate();
                                        BigDecimal award = challengeRecord.getAward().divide(nowRate, 2, RoundingMode.UP);
                                        award = I18nConstant.currencyFormat(currencyCode1, award);
                                        activityUserVO.setRunAward(award);
                                    }
                                    activityUserVO.setScoreAward(challengeRecord.getScore());
                                    activityUserVO.setCouponAward(challengeRecord.getCouponNum());
                                }
                            } else {        // 失败者奖励为0 元
                                activityUserVO.setRunAward(BigDecimal.ZERO);
                            }
                            activityUserVO.setOfflinePkPace(1);
                        } else { // 如果用户没有跑完,则奖励金额为0, 如果没有跑完，rank为平局
                            activityUserVO.setRunAward(BigDecimal.ZERO);
                            activityUserVO.setRank(0);
                            activityUserVO.setOfflinePkPace(0);
                        }
                    }
                }
                if (activityUserVO.getUserState() == 0 || activityUserVO.getUserState() == 2) {
                    isAllAccepted = 0;
                }
                if (activityEntity.getActivityType() == 1 || activityEntity.getActivityType() == 2) {
                    if (!RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType().equals(activityEntity.getActivityTypeSub())) {
                        if (Objects.nonNull(scoreMap)) {
                            ActivityUserScore activityUserScore = scoreMap.get(activityUserVO.getUserId());
                            if (Objects.nonNull(activityUserScore)) {
                                activityUserVO.setScoreAward(activityUserScore.getScore());
                            }
                        }
                        if (Objects.nonNull(userCouponMap)) {
                            List<UserCoupon> userCoupons = userCouponMap.get(activityUserVO.getUserId());
                            if (!CollectionUtils.isEmpty(userCoupons)) {
                                activityUserVO.setCouponAward(userCoupons.size());
                            }
                        }
                    }
                    List<Integer> newPersonPkType = RunActivitySubTypeEnum.getNewPersonPkType();
                    if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType())
                            || RunActivitySubTypeEnum.RANDOM_MATCHING.getType().equals(activityEntity.getActivityTypeSub())
                            || RunActivitySubTypeEnum.FRIENDS_MATCHING.getType().equals(activityEntity.getActivityTypeSub())
                            || newPersonPkType.contains(activityEntity.getActivityTypeSub())
                    ) {
                        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(userId);
                        String currencyCode = userAccount.getCurrencyCode();
                        ZnsUserAccountEntity activityUserAccount = userAccountService.getUserAccount(activityUserVO.getUserId());
                        String activityUserCurrencyCode = activityUserAccount.getCurrencyCode();
                        processExchangeRateAmount(activityEntity, activityUserVO, currencyCode, activityUserCurrencyCode);
                    }
                }
            }
            if (YesNoStatus.YES.getCode().equals(activityEntity.getPropSupport())
                    && RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                // 官方组队跑道具逻辑处理，仅里程跑支持道具
                runActivityUsers = runActivityUsers.stream().map(e -> {
                    ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.getLatestUserActivityRunDataDetails(e.getUserId(), activityEntity.getId());
                    if (Objects.nonNull(detailsEntity)) {
                        Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(e.getUserId(), activityEntity.getId(), detailsEntity.getId(), null);
                        if (effectValue > 0) {
                            Integer runTimeMillisecond = e.getRunTimeMillisecond();
                            e.setBeforePropRunTime(runTimeMillisecond);
                            e.setPropEffectTime(effectValue);
                            e.setRunTimeMillisecond(runTimeMillisecond - effectValue * 1000 < 0 ? 0 : runTimeMillisecond - effectValue * 1000);
                        }
                    }
                    if (Objects.isNull(e.getCompleteTime())) {
                        //防止用户未完赛时完成时间为空时排序异常
                        e.setCompleteTime(DateUtil.addDays1(activityEntity.getActivityEndTime(), 7));
                    }
                    return e;
                }).sorted(Comparator.comparing(RunActivityUserVO::getIsComplete, Comparator.reverseOrder())
                        .thenComparing(RunActivityUserVO::getUserState, Comparator.reverseOrder())
                        .thenComparing(RunActivityUserVO::getRunTimeMillisecond)
                        .thenComparing(RunActivityUserVO::getCompleteTime)).collect(Collectors.toList());
            }
            activityDetailVO.setIsAllAccepted(isAllAccepted);
        }
        return runActivityUsers;
    }

    public void quitTeam(ZnsUserEntity user, ZnsRunActivityEntity activityEntity, Long teamId) {

        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .activityId(activityEntity.getId())
                .userId(user.getId())
                .teamId(teamId)
                .userState(ActivityUserStateEnum.ACCEPT.getState())
                .isDelete(0)
                .build();
        activityUserService.updateState(ActivityUserStateEnum.QUIT.getState(), userQuery);
        activityEntity.setUserCount(activityEntity.getUserCount() - 1);
        runActivityService.updateById(activityEntity);
    }


    /**
     * 活动用户分页查询
     *
     * @param activityEntity     活动
     * @param userId             用户ID
     * @param pageNum            页码
     * @param pageSize           每页数量
     * @param activityUserStatus
     */
    public Map<String, Object> pageQueryActivityUsers(ZnsRunActivityEntity activityEntity, Long userId, Integer pageNum,
                                                      Integer pageSize, Integer activityUserStatus) {
        Map<String, Object> map = new HashMap<>();
        if (Objects.isNull(pageNum)) {
            pageNum = 1;
        }
        if (Objects.isNull(pageSize)) {
            pageSize = 10;
        }
        Page page = new Page(pageNum, pageSize);
        //查询活动用户
        ZnsRunActivityUserEntity activityUser = null;
        if (Objects.nonNull(userId) && userId > 0) {
            activityUser = activityUserService.findActivityUser(activityEntity.getId(), userId);
        }
        Integer targetRunMileage = null;
        Integer targetRunTime = null;

        if (Objects.nonNull(activityUser)) {
            targetRunMileage = activityUser.getTargetRunMileage();
            targetRunTime = activityUser.getTargetRunTime();
        }

        // 查询活动用户
        List<RunActivityUserVO> runActivityUsers = findRunActivityUsers(activityEntity, page, userId, new RunActivityDetailVO(), targetRunMileage, targetRunTime, activityUserStatus);
        map.put("list", runActivityUsers);
        map.put("total", page.getTotal());
        // 查询官方赛事榜单最新更新时间
        if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityEntity.getActivityType())) {
            ZonedDateTime lastUpdateTime = activityUserService.officalActivityLastUpdateTime(activityEntity.getId());
            map.put("lastUpdateTime", lastUpdateTime);
        }
        // 是否可以一键关注 官方组队和非官方组队
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType()) || RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
//            List<ZnsRunActivityUserEntity> aLlUsers = runActivityUserDao.findRunActivityALlUsersIdWithOutSelf(activityEntity.getId(),userId,targetRunMileage);
            List<Long> collect = runActivityUsers.stream().map(RunActivityUserVO::getUserId).filter(u -> !u.equals(userId)).collect(Collectors.toList());
            List<ZnsUserFriendEntity> friendList = userFriendService.getMyFriendList(userId, collect);
            Map<Long, ZnsUserFriendEntity> friendEntityMap = friendList.stream().collect(Collectors.toMap(ZnsUserFriendEntity::getFriendId, Function.identity(), (x, y) -> x));
            Integer canNoticeAll = 0;
            for (int i = 0; i < runActivityUsers.size(); i++) {
                RunActivityUserVO runActivityUserVO = runActivityUsers.get(i);
                if (runActivityUserVO.getUserId().equals(userId)) {
                    continue;
                }
                if (Objects.nonNull(friendEntityMap.get(runActivityUserVO.getUserId()))) {
                } else {
                    canNoticeAll = 1;
                }
            }
            map.put("canNoticeAll", canNoticeAll);
        }
        return map;
    }

    /**
     * 获取活动用户信息
     *
     * @param activityEntity
     * @param dataDetailsEntity
     * @param taskId
     * @return
     */
    public void bindingRunDataDetailId(ActivityTypeDto activityEntity, ZnsUserRunDataDetailsEntity dataDetailsEntity, Long taskId) {
        Long runDataDetailsId = dataDetailsEntity.getId();
        Long userId = dataDetailsEntity.getUserId();
        if (Objects.isNull(activityEntity)) {
            log.info("绑定跑步id失败，活动不存在");
            return;
        }
        String key = RedisConstants.RUN_ACTIVITY_USER_DATA_DETAILS_ID + runDataDetailsId;
        //判断是否绑定
        Object res = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(res)) {
            log.info("绑定跑步id失败，跑步id已绑定");
            return;
        }
        RunActivityUserQuery query = RunActivityUserQuery.builder()
                .isDelete(0).userId(userId).activityId(activityEntity.getId()).build();
        ZnsRunActivityUserEntity activityUserEntity = activityUserService.findOne(query);
        if (Objects.isNull(activityUserEntity)) {
            log.info("绑定跑步id失败，活动用户不存在");
            return;
        }
        // 重新修改活动id，防止跑步机上传数据没有记录活动id
        // 这里可能是设备没有activityId，但是app传了activityId
        dataDetailsEntity.setActivityId(activityEntity.getId());
        // 修改目标记录,累计跑不修改 ，能看出，累计跑的 zns_user_run_data_details 只有一条数据
        if (!RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(activityEntity.getActivityType())) {
            dataDetailsEntity.setDistanceTarget(new BigDecimal(activityUserEntity.getTargetRunMileage()));
            dataDetailsEntity.setTimeTarget(activityUserEntity.getTargetRunTime());
        }

        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.NEW_USER_PK_MANY.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityEntity.getActivityType())) {
            if (MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType())) {
                ZnsRunActivityUserEntity update = new ZnsRunActivityUserEntity();
                update.setId(activityUserEntity.getId());
                update.setRunDataDetailsId(runDataDetailsId);           // 更新 zns_run_activity_user 表的 runDataDetailsId 数据
                activityUserService.updateById(update);
                List<Integer> newPersonPkType = RunActivitySubTypeEnum.getNewPersonPkType();
                if (newPersonPkType.contains(activityEntity.getActivityTypeSub()) || (activityEntity.getActivityType() == 12)) {
                    redisTemplate.opsForValue().set(RedisConstants.NEW_USER_OFFLINE_PK_JOIN + dataDetailsEntity.getUserId(), dataDetailsEntity.getId().toString(), 1, TimeUnit.DAYS);
                }
            }
            // 如果是排行赛事
        } else if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityEntity.getActivityType())) {
            //更新官方赛事挑战记录表

            ZnsUserRunRecordEntity runRecordEntity = userRunRecordService.findLastRecord(activityEntity.getId(), userId, 0l);
            ;
            if (Objects.isNull(runRecordEntity)) {
                //防止网络异常或其他异常导致startRun还未处理
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("更新官方赛事挑战记录表失败 error,e:{}", e);
                    return;
                }
                runRecordEntity = userRunRecordService.findLastRecord(activityEntity.getId(), userId, 0l);

                if (Objects.isNull(runRecordEntity)) {
                    log.info("更新官方赛事挑战记录表失败，runRecordEntity不存在");
                    return;
                }
            }
            runRecordEntity.setRunDataDetailsId(runDataDetailsId);
            userRunRecordService.update(runRecordEntity);
        }
        //任务绑定
        bindingTask(taskId, activityEntity.getId());
        redisTemplate.opsForValue().set(key, "1", 1L, TimeUnit.DAYS);
    }

    /**
     * 修改活动用户成绩
     *
     * @param runDataDetailsId
     * @param activity
     * @param userRunDataDetailsEntity
     * @param completeRuleType
     */
    public void updateRunDataDetail(Long runDataDetailsId, ZnsRunActivityEntity activity, ZnsUserRunDataDetailsEntity userRunDataDetailsEntity, Integer completeRuleType) {
        if (Objects.isNull(activity) || activity.getId() == 0) {
            return;
        }
        RunActivityUserQuery query1 = RunActivityUserQuery.builder()
                .isDelete(0).userId(userRunDataDetailsEntity.getUserId())
                .activityId(activity.getId())
                .build();
        ZnsRunActivityUserEntity activityUserEntity = activityUserService.findOne(query1);
        if (Objects.isNull(activityUserEntity)) {
            log.error("修改跑步数据失败，活动用户不存在");
            return;
        }
        if (!activityUserEntity.getRunDataDetailsId().equals(runDataDetailsId)) {
            log.error("修改跑步数据失败，绑定跑步id不一致");
            return;
        }
        log.info("开始修改跑步数据：activityUserEntity=" + activityUserEntity);
        if (userRunDataDetailsEntity.getRunMileage().compareTo(activityUserEntity.getRunMileage()) < 0) {
            return;
        }
        activityUserEntity.setRunTime(userRunDataDetailsEntity.getRunTime());
        activityUserEntity.setRunMileage(userRunDataDetailsEntity.getRunMileage());
        if (activityUserEntity.getRunTime() >= 60) {
            activityUserEntity.setSubState(2);
        }
        SpeedPropRecordQuery query = SpeedPropRecordQuery.builder().userId(userRunDataDetailsEntity.getUserId()).activityId(activity.getId()).build();
        Integer speedValue = speedPropRecordService.querySpeedValue(query);

        if (activity.getCompleteRuleType() == 1 && activityUserEntity.getRunMileage().intValue() + speedValue >= activityUserEntity.getTargetRunMileage()) {
            activityUserEntity.setIsComplete(1);
            activityUserEntity.setSubState(1);
            activityUserEntity.setCompleteTime(ZonedDateTime.now());
        }
        if (activity.getCompleteRuleType() == 2 && activityUserEntity.getRunTime() >= activityUserEntity.getTargetRunTime()) {
            activityUserEntity.setIsComplete(1);
            activityUserEntity.setSubState(1);
            activityUserEntity.setCompleteTime(ZonedDateTime.now());
        }
        activityUserEntity.setUserState(4);     // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
        activityUserEntity.setIsCheat(userRunDataDetailsEntity.getIsCheat());
        if (Objects.isNull(userRunDataDetailsEntity.getRunTimeMillisecond()) || userRunDataDetailsEntity.getRunTimeMillisecond() == 0) {
            activityUserEntity.setRunTimeMillisecond(userRunDataDetailsEntity.getRunTime() * 1000 + 999);
        } else {
            activityUserEntity.setRunTimeMillisecond(userRunDataDetailsEntity.getRunTimeMillisecond());
        }

        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityUserEntity.getActivityType())
                && activity.getCompleteRuleType() == 1 && YesNoStatus.YES.getCode().equals(activity.getPropSupport())) {
            Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUserEntity.getUserId(), activity.getId(), userRunDataDetailsEntity.getId(), null);
            if (effectValue > 0) {
                activityUserEntity.setPropRunTime(activityUserEntity.getRunTimeMillisecond() - effectValue * 1000 < 0 ? 0 : activityUserEntity.getRunTimeMillisecond() - effectValue * 1000);
            }
        }
        activityUserService.updateById(activityUserEntity);
        if (Objects.nonNull(userRunDataDetailsEntity.getTaskId()) && userRunDataDetailsEntity.getTaskId() != 0) {
            RunActivityUserTask userTask = runActivityUserTaskService.findById(activityUserEntity.getTaskId());
            if (Objects.nonNull(userTask.getActivityType()) && userTask.getActivityType() == 6) {
                return;
            }
            ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(userTask.getActivityId());
            ZonedDateTime runTime = userRunDataDetailsEntity.getCreateTime() == null ? ZonedDateTime.now() : userRunDataDetailsEntity.getCreateTime();
            if (znsRunActivityEntity != null && runTime.toInstant().toEpochMilli() < znsRunActivityEntity.getActivityEndTime().toInstant().toEpochMilli()) {
                log.info(" 当前运行时间小于结束时间，开始发愚人节奖励  " + userTask.getUserId() + "，taskId = " + userTask.getId());
                runActivityUserTaskService.dealUserTask(userTask, activityUserEntity);
            } else {
                log.info("活动已经结束，不发送奖励userId =  " + activityUserEntity.getUserId() + ",activityId =" + userTask.getActivityId());
            }
        }
    }

    /**
     * plus会员发送钉钉消息
     */
    public void sendPlusVipDingTalkMessages(Long runDataDetailsId, Long userId, String mainType) {
        log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{},开始执行", userId, runDataDetailsId);
        if (Objects.isNull(userId)) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，无法获取用户id", userId, runDataDetailsId);
            return;
        }
        ZnsUserEntity znsUserEntity = userService.findById(userId);
        if (!Objects.equals(znsUserEntity.getMemberType(), 1)) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，当前用户不是plus会员，无需推送消息", userId, runDataDetailsId);
            return;
        }
        //是否测试账号，1：是，0：否
        if (Optional.ofNullable(znsUserEntity.getIsTest()).orElse(0) == 1) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，当前用户是测试账号，无需推送消息", userId, runDataDetailsId);
            return;
        }
        ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.findById(runDataDetailsId);
        if (userRunDataDetail == null) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，无法获取用户跑步详情", userId, runDataDetailsId);
            return;
        }

        //判断活动类型
        Integer activityType = userRunDataDetail.getActivityType();
        List<Integer> excludeTypes = Lists.newArrayList(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(),
                RunActivityTypeEnum.NEW_USER_ACTIVITY.getType(),
                RunActivityTypeEnum.FREE_RUNNING.getType(),
                RunActivityTypeEnum.COURSE_RUNNING.getType()
        );
        if (excludeTypes.contains(activityType)) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，不是指定的活动类型，无需推送消息", userId, runDataDetailsId);
            return;
        }

        //排行赛只发一次
        Long activityId = userRunDataDetail.getActivityId();
        if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityType)) {

            ZnsUserRunRecordEntity runRecordEntity = userRunRecordService.findOneCompleted(userId, activityId, runDataDetailsId);
            if (runRecordEntity != null) {
                log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，排行赛已完成过一次，无需推送消息", userId, runDataDetailsId);
                return;
            }
        }

        //查询活动，新活动系列赛子活动不发
        if (!MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainType) && RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType().equals(activityType) && activityId > 1) {
            MainActivity mainActivity = mainActivityService.findById(activityId);
            if (Objects.nonNull(mainActivity) && MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
                log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，系列赛子活动无需推送", userId, runDataDetailsId);
                return;
            }
        }


        //查询本次跑步情况
        RunActivityUserQuery query = RunActivityUserQuery.builder()
                .isDelete(0)
                .userId(userId)
                .activityId(activityId)
                .build();
        ZnsRunActivityUserEntity activityUserEntity = activityUserService.findOne(query);
        if (Objects.isNull(activityUserEntity)) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，无法获取用户报名信息", userId, runDataDetailsId);
            return;
        }
        //是否完成比赛:0表示未完成，1表示完成
        if (activityUserEntity.getIsComplete() == 0) {
            log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，未完赛，等150ms", userId, runDataDetailsId);
            try {
                Thread.sleep(150);
            } catch (InterruptedException e) {
                log.error("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：" + userId + ",runDataDetailsId：" + runDataDetailsId + "，未完赛，再等150ms 异常:", e);
            }
            //等150ms查询本次跑步情况
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .isDelete(0).userId(userId).activityId(activityId)
                    .build();
            activityUserEntity = activityUserService.findOne(userQuery);
            if (activityUserEntity.getIsComplete() == 0) {
                log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，未完赛，再等300ms", userId, runDataDetailsId);
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    log.error("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：" + userId + ",runDataDetailsId：" + runDataDetailsId + "，未完赛，再等150ms 异常:", e);
                }
                //等450ms查询本次跑步情况
                RunActivityUserQuery userQuery450 = RunActivityUserQuery.builder()
                        .isDelete(0).userId(userId).activityId(activityId)
                        .build();
                activityUserEntity = activityUserService.findOne(userQuery);
                if (activityUserEntity.getIsComplete() == 0) {
                    log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{}，未完赛，无需推送消息", userId, runDataDetailsId);
                    return;
                }
            }
        }

        //创建统计查询参数
        ZonedDateTime startOfMonth = DateUtil.getFirstOfMonth(ZonedDateTime.now());
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).userId(userId).isComplete(1) //是否完成比赛:0表示未完成，1表示完成
                .activityTypeNotIn(excludeTypes)
                .build();
        //查询所有已完成的赛事数量
        long totalNum = activityUserService.findCount(userQuery);

        //查询本月完成赛事的赛事数量
        RunActivityUserQuery userQueryMonth = RunActivityUserQuery.builder()
                .isDelete(0).userId(userId).isComplete(1) //是否完成比赛:0表示未完成，1表示完成
                .activityTypeNotIn(excludeTypes).minCreateTime(startOfMonth)
                .build();
        Long monthNum = activityUserService.findCount(userQueryMonth);

        //查询最近完赛时间
        RunActivityUserQuery presentQuery = RunActivityUserQuery.builder()
                .select(List.of(ZnsRunActivityUserEntity::getModifieTime))
                .isDelete(0).userId(userId).isComplete(1).activityTypeNotIn(excludeTypes)
                .build();
        presentQuery.setOrders(List.of(OrderItem.desc("modifie_time")));
        ZnsRunActivityUserEntity runActivityUser = activityUserService.findOne(presentQuery);
        String lastComplete = "";
        if (runActivityUser != null && runActivityUser.getModifieTime() != null) {
            lastComplete = DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD_HH_MM_SS, runActivityUser.getModifieTime());
        }

        StringBuilder sb = new StringBuilder();
        sb.append("用户名：").append(znsUserEntity.getFirstName()).append(znsUserEntity.getLastName()).append("\r\n");
        sb.append("邮箱：").append(znsUserEntity.getEmailAddressEn()).append("\r\n");
        sb.append("注册时间(标准0时区)：").append(DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD_HH_MM_SS, znsUserEntity.getCreateTime())).append("\r\n");
        sb.append("目前已注册天数：").append(DateUtil.betweenDay(znsUserEntity.getCreateTime(), DateUtil.getNowDate())).append("\r\n");
        sb.append("最近完赛时间(标准0时区)：").append(lastComplete).append("\r\n");
        sb.append("本月完成赛事场次(按照标准0时区统计)：").append(Optional.ofNullable(monthNum).orElse(0L)).append("\r\n");
        sb.append("累计完成赛事次数(按照标准0时区统计)：").append(Optional.ofNullable(totalNum).orElse(0L)).append("\r\n");
        sb.append("赛事记录不包含里程碑、新人活动、自由跑、课程").append("\r\n");

        String message = sb.toString();
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PLUS_USER_RUNNING_NOTIFICATION_ONLINE;
        if (!EnvUtils.isOnline(profile)) {
            //测试环境
            tokenEnum = DingTalkTokenEnum.PLUS_USER_RUNNING_NOTIFICATION_TEST;
        }
        String token = tokenEnum.getToken();
        String secret = tokenEnum.getSecret();

        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(token, secret, "【PLUS通知】" + message, puls_msg_at_phone), "pre");
        log.info("ZnsRunActivityUserServiceImpl#sendPlusVipDingTalkMessages----userId：{},runDataDetailsId：{},profile：{},结束执行", userId, runDataDetailsId, profile);
    }
//
//    /**
//     * 活动开发推送
//     *
//     * @param list
//     * @param pushTime
//     */
//    @Async(value = "asyncExecutor")
//    public void startActivityPushNotification(List<ZnsRunActivityEntity> list, Integer pushTime) {
//        // 查询马拉松跳转链接
//        List<Long> taskActivityIds = list.stream().filter(e -> RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(e.getActivityType()))
//                .map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
//        Map<Long, OperationalActivity> operationalActivityMap = new HashMap<>();
//        if (!CollectionUtils.isEmpty(taskActivityIds)) {
//            List<OperationalActivity> operationalActivities = operationalActivityService.selectByRunActivityIds(taskActivityIds);
//            operationalActivityMap = operationalActivities.stream().collect(Collectors.toMap(OperationalActivity::getRunActivityId, Function.identity(), (x, y) -> x));
//        }
//
//        //查询发起者
//        List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
//
//        List<ZnsRunActivityUserEntity> userEntities = activityUserService.findList(
//                RunActivityUserQuery.builder()
//                        .select(List.of(ZnsRunActivityUserEntity::getUserId,
//                                ZnsRunActivityUserEntity::getActivityId,
//                                ZnsRunActivityUserEntity::getNickname,
//                                ZnsRunActivityUserEntity::getUserType))
//                        .userState(1).activityIds(activityIds)
//                        .build()
//
//        );
//        if (CollectionUtils.isEmpty(userEntities)) {
//            return;
//        }
//        //用户分组
//        Map<Long, List<Long>> participantMap = userEntities.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getActivityId, Collectors.mapping(ZnsRunActivityUserEntity::getUserId, Collectors.toList())));
//
//        for (ZnsRunActivityEntity activityEntity : list) {
//            ActivityNotificationEnum activityNotification = ActivityNotificationEnum.getActivityNotification(2, activityEntity.getActivityType());
//            List<Long> userIds = participantMap.get(activityEntity.getId());
//            String content = String.format(activityNotification.getNotificationContent(), activityEntity.getActivityTitle(), pushTime);
//            ImMessageBo bo = appMessageService.assembleImActivityMessage(activityEntity, content);
//            if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(activityEntity.getActivityType())) {
//                // 马拉松活动跳转链接为h5，需单独设置
//                OperationalActivity operationalActivity = operationalActivityMap.get(activityEntity.getId());
//                bo.setJumpType("0");
//                bo.setJumpValue(operationalActivity.getActivityUrl());
//            }
//            MessageBo messageBo = appMessageService.assembleMessageV2(activityNotification, activityEntity.getId(), content, "4", NoticeTypeEnum.ACTIVITY_IMMEDIATELY_BEGIN.getType());
//            messageBo.setActivityId(activityEntity.getId());
//            messageBo.setRouteValue(activityEntity.getId().toString());
//            messageBo.setRouteType(3);
//            appMessageService.sendImAndPushUserIds(userIds, bo, messageBo);
//        }
//    }

    /**
     * 获取活动历史记录用户
     *
     * @param type
     * @param isComplete
     * @return
     */
    public List<Map<String, Object>> getRunActivityHistoryUsers(Integer type, Integer isComplete, String startTime) {
        List<Map<String, Object>> runActivityHistoryUsers = activityUserService.getRunActivityHistoryUsers(type, startTime, isComplete);
        if (CollectionUtils.isEmpty(runActivityHistoryUsers)) {
            return runActivityHistoryUsers;
        }
        List<Long> userIds = runActivityHistoryUsers.stream().map(m -> MapUtils.getLong(m, "userId")).collect(Collectors.toList());
        List<ZnsUserEntity> list = userService.findByIds(userIds);
        Map<Long, ZnsUserEntity> userEntityMap = list.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        for (Map<String, Object> runActivityHistoryUser : runActivityHistoryUsers) {
            Long userId = MapUtils.getLong(runActivityHistoryUser, "userId");
            ZnsUserEntity znsUserEntity = userEntityMap.get(userId);
            if (Objects.isNull(znsUserEntity)) {
                continue;
            }
            runActivityHistoryUser.put("nickname", znsUserEntity.getFirstName());
            runActivityHistoryUser.put("headPortrait", znsUserEntity.getHeadPortrait());
        }
        return runActivityHistoryUsers;
    }


    /**
     * 查询官方组队活动
     *
     * @param query
     * @return
     */
    public Page<GuanFangZhuiDuiResp> doSelectActivityByGuanFangZhuDui(RunAwardQuery query) {
        query.setActivityType("4");
        activityUserService.initEndTime(query);
        Page<GuanFangZhuiDuiResp> page = runActivityService.selectPageByGuanFangZhuDui(PageHelper.ofPage(query), query.getActivityType(), query.getGmtStartTime(), query.getGmtEndTime(), query.getActivityNo());

        List<GuanFangZhuiDuiResp> list = page.getRecords();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        Integer limitPersionSize = activityUserService.getLimitPeopleSize();
        for (GuanFangZhuiDuiResp guanFangZhuiDuiResp : list) {
            try {
                Integer robotNum = activityUserService.selectByActivityIdTargetRunMileage(null, 1, guanFangZhuiDuiResp.getActivityId(), guanFangZhuiDuiResp.getRunMileage(), null);       //机器人数
                Integer userNum = activityUserService.selectByActivityIdTargetRunMileage(0, 0, guanFangZhuiDuiResp.getActivityId(), guanFangZhuiDuiResp.getRunMileage(), null);// 真实用户数
                Map<String, Object> jsonObjectConfig = JsonUtil.readValue(guanFangZhuiDuiResp.getActivityConfig());
                List<Map> runningGoalsAward = BaseOfficialActivityStrategy.getRunningGoalsAward(jsonObjectConfig);

                Integer allUserNum = robotNum + userNum; //所有用户数
                //非机器人完成人数
                Integer finishedPerson = activityUserService.selectByActivityIdTargetRunMileage(0, 0, guanFangZhuiDuiResp.getActivityId(), guanFangZhuiDuiResp.getRunMileage(), 1);
                Integer rankPerson = activityUserService.selectByActivityIdTargetRunMileage(null, null, guanFangZhuiDuiResp.getActivityId(), guanFangZhuiDuiResp.getRunMileage(), null);// 真实用户数
                BigDecimal accAward = getAward(runningGoalsAward, guanFangZhuiDuiResp.getRunMileage() + "");
                BigDecimal finishedAward = BigDecimalUtil.multiply(accAward, new BigDecimal(finishedPerson));   //完赛奖励 = 完赛人数(非机器人) * 奖励金额


                BigDecimal rankAward = BigDecimal.ZERO; // 名次总奖励
                if (rankPerson >= limitPersionSize) {
                    List<ZnsRunActivityUserEntity> activityUserEntities = activityUserService.selectByActivityIdRankIsTestIsRobot(guanFangZhuiDuiResp.getActivityId(), guanFangZhuiDuiResp.getRunMileage(), 1, 3, 0, 0);
                    for (ZnsRunActivityUserEntity znsRunActivityUserEntity : activityUserEntities) {
                        rankAward = getRankAward(runningGoalsAward, guanFangZhuiDuiResp.getRunMileage() + "", znsRunActivityUserEntity.getRank()).add(rankAward);
                    }
                }


                BigDecimal allAward = BigDecimalUtil.add(finishedAward, rankAward);
                guanFangZhuiDuiResp.setAllAward(allAward);
                guanFangZhuiDuiResp.setRobotNum(robotNum);
                guanFangZhuiDuiResp.setUserNum(userNum);
                guanFangZhuiDuiResp.setAllUserNum(allUserNum);
                guanFangZhuiDuiResp.setFinishedAward(finishedAward);
                guanFangZhuiDuiResp.setRankAward(rankAward);
            } catch (Exception e) {
                log.error("异常 doSelectActivityByGuanFangZhuDui " + guanFangZhuiDuiResp.getActivityId(), e);
            }
        }
        page.setRecords(list);
        return page;
    }


    /**
     * 使用汇率处理奖励币种
     *
     * @param activityEntity
     * @param activityUserVO
     * @param currencyCode
     * @param activityUserCurrencyCode
     */
    private void processExchangeRateAmount(ZnsRunActivityEntity activityEntity, RunActivityUserVO activityUserVO, String currencyCode, String activityUserCurrencyCode) {
        if (!activityUserCurrencyCode.equals(currencyCode)) {
            ZonedDateTime activityEndTime = activityEntity.getActivityEndTime();
            BigDecimal exchangeRate1;
            BigDecimal exchangeRate2 = BigDecimal.ONE;
            BigDecimal award;
            if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(activityUserCurrencyCode)) {
                exchangeRate1 = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
                if (Objects.nonNull(activityEndTime)) {
                    ZonedDateTime localDate = activityEndTime.toInstant().atZone(ZoneId.systemDefault());
                    ExchangeRateConfigHistoryEntity exchangeRateConfig1 = exchangeRateConfigHistoryService.selectBySourceCodeAndTargetCodeAndDate(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode, localDate);
                    if (Objects.nonNull(exchangeRateConfig1)) {
                        exchangeRate1 = exchangeRateConfig1.getExchangeRate();
                    }
                }
                if (runActivityService.checkNewActivity(activityEntity.getId())) {
                    award = activityUserVO.getRunAward().multiply(exchangeRate1);
                } else {
                    award = activityUserVO.getRunAward();
                }
            } else {
                exchangeRate1 = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), activityUserCurrencyCode).getExchangeRate();
                if (Objects.nonNull(activityEndTime)) {
                    ZonedDateTime localDate = activityEndTime.toInstant().atZone(ZoneId.systemDefault());
                    ExchangeRateConfigHistoryEntity exchangeRateConfig1 = exchangeRateConfigHistoryService.selectBySourceCodeAndTargetCodeAndDate(I18nConstant.CurrencyCodeEnum.USD.getCode(), activityUserCurrencyCode, localDate);
                    if (Objects.nonNull(exchangeRateConfig1)) {
                        exchangeRate1 = exchangeRateConfig1.getExchangeRate();
                    }
                    if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
                        exchangeRate2 = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
                        ExchangeRateConfigHistoryEntity exchangeRateConfig2 = exchangeRateConfigHistoryService.selectBySourceCodeAndTargetCodeAndDate(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode, localDate);
                        if (Objects.nonNull(exchangeRateConfig2)) {
                            exchangeRate2 = exchangeRateConfig2.getExchangeRate();
                        }
                    }
                }
                if (runActivityService.checkNewActivity(activityEntity.getId())) {
                    award = activityUserVO.getRunAward().divide(exchangeRate1, 4, RoundingMode.UP).multiply(exchangeRate2);
                } else {
                    award = activityUserVO.getRunAward();
                }
            }
            BigDecimal runAward = award.setScale(2, RoundingMode.UP);
            runAward = I18nConstant.currencyFormat(currencyCode, runAward);
            activityUserVO.setRunAward(runAward);
        }
    }


    /**
     * 查询用户是否已经报名
     *
     * @param activityId
     * @param userId
     */
    private void findTeamActivityUser(Long activityId, Long userId) {

        if (null == activityId || null == userId) {
            throw new BaseException("findTeamActivityUser");
        }
        List<Integer> list = Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState());
        ZnsRunActivityUserEntity userEntity = activityUserService.findOne(RunActivityUserQuery.builder()
                .isDelete(0)
                .activityId(activityId)
                .userId(userId)
                .userStateIn(list)
                .build());
        if (userEntity != null) {
            throw new BaseException("this user has signed" + userId);
        }

    }


    /**
     * 绑定任务
     *
     * @param taskId
     * @param activityId
     */
    private void bindingTask(Long taskId, Long activityId) {

        if (Objects.isNull(taskId) || taskId == 0) {
            return;
        }
        RunActivityUserTask task = runActivityUserTaskService.findById(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        if (task.getActivityType() == 6) {
            RunActivityUserTask userTask = new RunActivityUserTask();
            userTask.setId(taskId);
            userTask.setGmtModified(ZonedDateTime.now());
            userTask.setActivityId(activityId);
            runActivityUserTaskService.update(userTask);
        } else if (task.getActivityType() == 7) {
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(activityId, task.getUserId());
            if (Objects.nonNull(activityUser) && (Objects.isNull(activityUser.getTaskId()) || activityUser.getTaskId() == 0)) {
                activityUser.setTaskId(taskId);
                activityUserService.updateById(activityUser);
            }
        }
    }

    /**
     * 获取奖励
     *
     * @param runningGoalsAward
     * @param goal
     * @return
     */
    private BigDecimal getAward(List<Map> runningGoalsAward, String goal) {
        for (Map<String, Object> map : runningGoalsAward) {
            if ((map.get("goal") + "").equals(goal)) {
                return MapUtil.getBigDecimal(map.get("award"), BigDecimal.ZERO);
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取排名奖励
     *
     * @param runningGoalsAward
     * @param goal
     * @param rank
     * @return
     */
    private BigDecimal getRankAward(List<Map> runningGoalsAward, String goal, Integer rank) {
        for (Map<String, Object> map : runningGoalsAward) {
            if ((map.get("goal") + "").equals(goal)) {
                if (Objects.equals(rank, 1)) {
                    return MapUtil.getBigDecimal(map.get("firstAward"), BigDecimal.ZERO);
                } else if (Objects.equals(rank, 2)) {
                    return MapUtil.getBigDecimal(map.get("secondAward"), BigDecimal.ZERO);
                } else if (Objects.equals(rank, 3)) {
                    return MapUtil.getBigDecimal(map.get("thirdAward"), BigDecimal.ZERO);
                }
            }
        }
        return BigDecimal.ZERO;
    }
}
