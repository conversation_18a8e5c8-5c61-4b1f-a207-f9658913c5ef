package com.linzi.pitpat.data.clubservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserAwardBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomStatusEnum;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityTeamJoinSettingDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCategoryItem;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserGroup;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RoomConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomDo;
import com.linzi.pitpat.data.activityservice.model.query.ActivityTeamQuery;
import com.linzi.pitpat.data.activityservice.model.query.MainActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomQuery;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityUserAwardVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.CategoryActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRaceCalendarSubActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.UserRankDetailDto;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryItemService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsLoaderService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserGroupService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RoomConfigService;
import com.linzi.pitpat.data.activityservice.service.RoomService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubActivityTeamEnrollStateEnum;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityInvite;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityRoomRelationDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.model.entity.ClubRunDataDo;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityRoomRelationQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityTeamQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubRunDataQuery;
import com.linzi.pitpat.data.clubservice.model.request.ClubInviteMemberJoinActivityReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubJoinActivityListReqDto;
import com.linzi.pitpat.data.clubservice.model.request.KolTeamCreateStateReqDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubEventNotJoinInfoDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubEventTotalDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubRoomResponseDto;
import com.linzi.pitpat.data.clubservice.model.response.KolTeamCreateStateRespDto;
import com.linzi.pitpat.data.clubservice.service.ClubActivityInviteService;
import com.linzi.pitpat.data.clubservice.service.ClubActivityRoomRelationService;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubImService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubRunDataService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.userservice.enums.CountryFlagConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.util.page.PageConvert;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
@Slf4j
public class ClubActivityManager {
    /**
     * 批量消息数量
     */
    private final String BATCH_NUMBER = "200";
    /**
     * 真实用户消息
     */
    private final int IS_TEST_TAG = 0;


    private final ClubActivityInviteService clubActivityInviteService;

    private final ClubService clubService;

    private final ClubImService clubImService;

    private final ClubActivityTeamService clubActivityTeamService;

    private final MainActivityService mainActivityService;

    private final SubActivityService subActivityService;

    private final ClubRunDataService clubRunDataService;


    private final ZnsRunActivityUserService runActivityUserService;

    private final ActivityEquipmentConfigService activityEquipmentConfigService;

    private final ActivityFeeService activityFeeService;

    private final RotationAreaBizService rotationAreaBizService;
    private final ZnsUserAccountService userAccountService;
    private final ActivityCategoryItemService activityCategoryItemService;
    private final UserKolService userKolService;

    private final ActivityTeamService activityTeamService;
    private final ActivityParamsLoaderService activityParamsLoaderService;
    private final RedissonClient redissonClient;
    private final TransactionTemplate transactionTemplate;
    private final ClubMemberService clubMemberService;
    private final ActivityDisseminateBizService activityDisseminateBizService;

    private final ActivityUserAwardBizService activityUserAwardBizService;
    private final ClubActivityRoomRelationService clubActivityRoomRelationService;
    private final RoomService roomService;
    private final RoomConfigService roomConfigService;
    private final ZnsUserService znsUserService;
    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final ZnsTreadmillService znsTreadmillService;
    private final ActivityResultManager activityResultManager;
    private final ClubPushManager clubPushManager;
    private final ActivityUserGroupService activityUserGroupService;
    private final UserGroupRelService userGroupRelService;

    /**
     * 邀请用户加入活动
     *
     * @param request
     */
    public void inviteJoinActivity(ClubInviteMemberJoinActivityReqDto request, Long userId) {
        if (CollectionUtils.isEmpty(request.getMemberUserIds())) {
            throw new BizI18nException("club.op.fail");
        }
        //获取已经邀请过的数据
        List<Long> alreadyInviteUserIds = clubActivityInviteService.findAlreadyUserIdByActivityIdAndClubAndUserId(request.getClubId(), request.getActivityId(), request.getMemberUserIds());
        List<Long> saveUserIds = new ArrayList<>(request.getMemberUserIds());
        saveUserIds.removeAll(alreadyInviteUserIds);
        List<ClubActivityInvite> inviteList = saveUserIds.stream().map(i -> {
            ClubActivityInvite invite = new ClubActivityInvite();
            invite.setClubId(request.getClubId());
            invite.setMainActivityId(request.getActivityId());
            invite.setInviteUserId(userId);
            invite.setInviteeUserId(i);
            invite.setActivityTeamId(request.getActivityTeamId());
            return invite;
        }).toList();
        try {
            clubActivityInviteService.batchInsertInvites(inviteList);
        } catch (Exception e) {
            throw new BizI18nException("club.op.fail");
        }
        Club club = clubService.findById(request.getClubId());
        clubImService.sendImMessageInviteUserJoinActivity(club, request.getActivityId(), request.getActivityTeamId(), userId, request.getMemberUserIds());

    }

    /**
     * 获取当前俱乐部参加的活动
     *
     * @param req
     * @return
     */
    public Page<CategoryActivityVo> clubJoinedActivity(ClubJoinActivityListReqDto req, Long userId, String zoneId, String languageCode) {
        Page<Long> activityIds = clubActivityTeamService.findMainActivityIdByClub(req);
        if (CollectionUtils.isEmpty(activityIds.getRecords())) {
            return new Page<>();
        }
        List<MainActivity> listByIds = mainActivityService.findListByIds(activityIds.getRecords());
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(userId);
        Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        Map<Long, MainActivity> mainActivityMap = listByIds.stream().collect(Collectors.toMap(MainActivity::getId, Function.identity()));
        Map<Long, List<MyRaceCalendarSubActivityListVo>> subActivityMap = subActivityService.findSubActivityMapByMainActivityIds(activityIds.getRecords());
        //查询类目下活动
        List<ActivityCategoryItem> categoryItemList = activityCategoryItemService.findListByActivityIds(activityIds.getRecords());
        Map<Long, ActivityCategoryItem> categoryItemHot = categoryItemList.stream().collect(Collectors.toMap(ActivityCategoryItem::getActivityId, Function.identity(), (x, y) -> x));

        return PageConvert.dataConvert(activityIds, (id) -> {
            MainActivity mainActivity = mainActivityMap.get(id);
            if (Objects.isNull(mainActivity)) return null;
            return getCategoryActivityVo(req.getClubId(), zoneId, languageCode, id, mainActivity, subActivityMap, categoryItemHot, userAccount, currency);
        });
    }

    private CategoryActivityVo getCategoryActivityVo(Long clubId, String zoneId, String languageCode, Long id, MainActivity mainActivity, Map<Long, List<MyRaceCalendarSubActivityListVo>> subActivityMap, Map<Long, ActivityCategoryItem> categoryItemHot, ZnsUserAccountEntity userAccount, Currency currency) {
        CategoryActivityVo vo = new CategoryActivityVo();
        vo.setActivityId(id);
        vo.setMainType(mainActivity.getMainType());
        ZonedDateTime startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        vo.setActivityState(mainActivity.getActivityState());
        //设置活动时间
        if (mainActivity.getTimeStyle() == 0) {
            vo.setActivityStartTime(startTime);
            vo.setActivityEndTime(endTime);
        } else {
            vo.setActivityStartTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), zoneId)));
            vo.setActivityEndTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), zoneId)));
            //各时区判断
            vo.setActivityState(mainActivityService.getActivityState(mainActivity.getId(), zoneId));
        }


        List<MyRaceCalendarSubActivityListVo> myRaceCalendarSubActivityListVos = subActivityMap.get(mainActivity.getId());
        //设置目标
        if (!org.springframework.util.CollectionUtils.isEmpty(myRaceCalendarSubActivityListVos) && myRaceCalendarSubActivityListVos.size() == 1) {
            vo.setTarget(myRaceCalendarSubActivityListVos.get(0).getTarget());
        }
        vo.setTargetType(mainActivity.getTargetType());
        //设置图片、标题
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
        if (Objects.nonNull(disseminate)) {
            vo.setActivityCoverImage(disseminate.getDisseminatePics());
            vo.setActivityTitle(disseminate.getTitle());
            vo.setIsShowMarquee(disseminate.getMarquee());
        }

        //设置标签
        if (MapUtils.isNotEmpty(categoryItemHot)) {
            ActivityCategoryItem categoryItem = categoryItemHot.get(mainActivity.getId());
            if (Objects.nonNull(categoryItem)) {
                vo.setSign(categoryItem.getSign());
            }
        }

        //设置费用
        ActivityFee feeEntry = activityFeeService.findFeeEntry(mainActivity.getId(), userAccount.getCurrencyCode());
        if (Objects.nonNull(feeEntry)) {
            vo.setActivityEntryFee(feeEntry.getAmount());
            vo.setActivityEntryScore(feeEntry.getScore());
        }
        vo.setCurrency(currency);
        //设置设备信息
        List<ActivityEquipmentConfig> equipments = activityEquipmentConfigService.findByActId(mainActivity.getId());
        List<Integer> equipmentInfos = equipments.stream().filter(a -> Objects.nonNull(a.getEquipmentType())).map(ActivityEquipmentConfig::getEquipmentType).distinct().toList();
        vo.setDeviceType(equipmentInfos);

        //设置用户缉奖励信息
        List<Long> activityIdList = new ArrayList<>();
        activityIdList.add(vo.getActivityId());
        List<ActivityUserAwardVo> userAward = activityUserAwardBizService.findUserAward(activityIdList);
        if (!CollectionUtils.isEmpty(userAward)) {
            Collections.shuffle(userAward);
            List<ActivityUserAwardVo> randomUserAwardList = userAward.stream()
                    .limit(10)
                    .collect(Collectors.toList());
            vo.setAwardUserList(randomUserAwardList);
        }
        //查询参与用户
        List<UserSimpleVo> activitySimpleUser = runActivityUserService.findActivitySimpleUser(vo.getActivityId(), 3, mainActivity.getOldType());
        vo.setParticipantList(activitySimpleUser);
        Integer activityUserCount = runActivityUserService.findActivityUserCount(vo.getActivityId());
        vo.setParticipantCount(activityUserCount);
        //路由获取
        RotationArea route = rotationAreaBizService.getNewActivityRoute(vo.getActivityId(), mainActivity.getMainType(), 2);
        vo.setUrl(route.getUrl());
        vo.setJumpParam(route.getJumpParam());
        ActivityTeamJoinSettingDto activityTeamSetting = activityParamsLoaderService.getActivityTeamSetting(vo.getActivityId());
        vo.setActivityLimitNums(Objects.nonNull(activityTeamSetting) ? activityTeamSetting.getAllowJoinTeamMemberLimit() : 0);
        Optional<ClubActivityTeam> byActivityIdAndClubId = clubActivityTeamService.findByActivityIdAndClubId(vo.getActivityId(), clubId);
        //俱乐部队伍人数
        int memberNums = 1;
        if (byActivityIdAndClubId.isPresent()) {
            ActivityTeam activityTeam = activityTeamService.findById(byActivityIdAndClubId.get().getActivityTeamId());
            vo.setRank(Objects.nonNull(activityTeam) ? activityTeam.getRank() : -1);
            memberNums = Objects.nonNull(activityTeam) ? activityTeam.getCurrentNum() : 1;
        }
        //判断活动是否结束
        if (Objects.equals(mainActivity.getActivityState(), MainActivityStateEnum.ENDED.getCode())) {
            List<ActivityUserAwardVo> rankAward = activityUserAwardBizService.findUserRankAward(vo.getActivityId());
            if (!CollectionUtils.isEmpty(rankAward)) {
                List<UserRankDetailDto> collect = rankAward.stream().sorted(Comparator.comparing(ActivityUserAwardVo::getAward).reversed()).limit(3).map(s -> {
                    UserRankDetailDto userRankDetailDto = new UserRankDetailDto();
                    BeanUtils.copyProperties(s, userRankDetailDto);
                    userRankDetailDto.setNickname(s.getNickname());
                    return userRankDetailDto;
                }).collect(Collectors.toList());
                vo.setUserRankDetailList(collect);
            }
            vo.setTeamAward(BigDecimal.ZERO);
            byActivityIdAndClubId.ifPresent(s -> {
                ActivityTeam activityTeam = activityTeamService.findById(s.getActivityTeamId());
                vo.setTeamAward(Objects.nonNull(activityTeam) ? activityTeam.getRankAward() : BigDecimal.ZERO);
            });
        } else {
            vo.setTeamAward(activityResultManager.clubExpectedTotalAward(id, memberNums));
        }
        return vo;
    }

    /**
     * 获取当前俱乐部参加的活动
     *
     * @param req
     * @return
     */
    public List<CategoryActivityVo> clubEventsList(ClubJoinActivityListReqDto req, ZnsUserEntity user) {
        Page<Long> activityIds = clubActivityTeamService.findMainActivityIdByClub(req);
        if (CollectionUtils.isEmpty(activityIds.getRecords())) {
            return new ArrayList<>();
        }
        boolean isJoinClub = clubMemberService.findByClubAndUserId(req.getClubId(), user.getId()).isPresent();
        List<MainActivity> listByIds = mainActivityService.findListByIds(activityIds.getRecords());
        //结束的活动
        List<MainActivity> endActivityList = listByIds.stream().filter(s -> Objects.equals(s.getActivityState(), MainActivityStateEnum.ENDED.getCode())).collect(Collectors.toList());
        //进行中的活动
        List<MainActivity> startActivityList = listByIds.stream().filter(s -> Objects.equals(s.getActivityState(), MainActivityStateEnum.STARTED.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(startActivityList) && CollectionUtils.isEmpty(endActivityList)) {
            return new ArrayList<>();
        }
        //加入未加入俱乐部活动展示有区别
        if (isJoinClub) {
            //已加入展示所有进行中活动
            listByIds = startActivityList;
            if (CollectionUtils.isEmpty(listByIds)) return new ArrayList<>();
        } else {
            if (CollectionUtils.isEmpty(startActivityList)) {
                List<Long> ids = endActivityList.stream().map(MainActivity::getId).collect(Collectors.toList());
                List<ClubActivityTeam> clubActivityTeams = clubActivityTeamService.findByActivityIdSAndClubId(ids, req.getClubId());
                List<ActivityTeam> activityTeamList = activityTeamService.findList(ActivityTeamQuery.builder().ids(clubActivityTeams.stream().map(ClubActivityTeam::getActivityTeamId).collect(Collectors.toList())).build());
                // 倒序排序，只取前两条记录
                List<Long> topTwoTeams = activityTeamList.stream().sorted(Comparator.comparing(ActivityTeam::getRankAward).reversed()).limit(2).map(ActivityTeam::getActivityId).collect(Collectors.toList());
                listByIds = listByIds.stream().filter(s -> topTwoTeams.contains(s.getId())).collect(Collectors.toList());
            } else {
                List<Long> activityIdsList = new ArrayList<>();
                List<Long> memeberList = clubMemberService.findByClubId(req.getClubId());
                //进行中的活动取一条
                Long maxActivityId = startActivityList.stream()
                        .reduce((a, b) -> {
                            BigDecimal awardA = activityResultManager.clubExpectedTotalAward(a.getId(), memeberList.size());
                            BigDecimal awardB = activityResultManager.clubExpectedTotalAward(b.getId(), memeberList.size());
                            return awardA.compareTo(awardB) >= 0 ? a : b; // 返回更大的活动
                        })
                        .map(MainActivity::getId) // 提取最大值对应的活动 ID
                        .orElse(null); // 如果流为空，返回 null
                activityIdsList.add(maxActivityId);
                //结束中的取一条，没有不取
                if (!CollectionUtils.isEmpty(endActivityList)) {
                    List<Long> ids = endActivityList.stream().map(MainActivity::getId).collect(Collectors.toList());
                    List<ClubActivityTeam> clubActivityTeams = clubActivityTeamService.findByActivityIdSAndClubId(ids, req.getClubId());
                    List<ActivityTeam> activityTeamList = activityTeamService.findList(ActivityTeamQuery.builder().ids(clubActivityTeams.stream().map(ClubActivityTeam::getActivityTeamId).collect(Collectors.toList())).build());
                    // 倒序排序，只取1条记录
                    List<Long> collect = activityTeamList.stream().sorted(Comparator.comparing(ActivityTeam::getRankAward).reversed()).limit(1).map(ActivityTeam::getActivityId).collect(Collectors.toList());
                    activityIdsList.addAll(collect);
                }
                listByIds = listByIds.stream().filter(s -> activityIdsList.contains(s.getId())).collect(Collectors.toList());
            }
        }

        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
        Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        Map<Long, MainActivity> mainActivityMap = listByIds.stream().collect(Collectors.toMap(MainActivity::getId, Function.identity()));
        Map<Long, List<MyRaceCalendarSubActivityListVo>> subActivityMap = subActivityService.findSubActivityMapByMainActivityIds(activityIds.getRecords());
        //查询类目下活动
        List<ActivityCategoryItem> categoryItemList = activityCategoryItemService.findListByActivityIds(activityIds.getRecords());
        Map<Long, ActivityCategoryItem> categoryItemHot = categoryItemList.stream().collect(Collectors.toMap(ActivityCategoryItem::getActivityId, Function.identity(), (x, y) -> x));
        return listByIds.stream().map(activity -> {
            MainActivity mainActivity = mainActivityMap.get(activity.getId());
            if (Objects.isNull(mainActivity)) return null;
            return getCategoryActivityVo(req.getClubId(), user.getZoneId(), user.getLanguageCode(), activity.getId(), mainActivity, subActivityMap, categoryItemHot, userAccount, currency);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<CategoryActivityVo> teamList(ZnsUserEntity user) {
        List<MainActivity> teamActivity = mainActivityService.findTeamActivity();
        if (CollectionUtils.isEmpty(teamActivity)) {
            return Collections.emptyList();
        }
        List<Long> activityIdListAll = teamActivity.stream().map(MainActivity::getId).collect(Collectors.toList());
        List<MainActivity> list = mainActivityService.findList(MainActivityQuery.builder().activityIds(activityIdListAll).activityStateList(List.of(MainActivityStateEnum.NOT_STARTED.getCode(), MainActivityStateEnum.STARTED.getCode())).build());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<Long, List<MyRaceCalendarSubActivityListVo>> subActivityMap = subActivityService.findSubActivityMapByMainActivityIds(activityIdListAll);
        List<ActivityCategoryItem> categoryItemList = activityCategoryItemService.findListByActivityIds(activityIdListAll);
        Map<Long, ActivityCategoryItem> categoryItemHot = categoryItemList.stream().collect(Collectors.toMap(ActivityCategoryItem::getActivityId, Function.identity(), (x, y) -> x));
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
        Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        List<ActivityUserGroup> groupByActId = activityUserGroupService.findActIdListByActIds(activityIdListAll);
        Map<Long, List<ActivityUserGroup>> groupByActIdMap = groupByActId.stream().collect(Collectors.groupingBy(ActivityUserGroup::getMainActivityId));
        List<Long> groups = userGroupRelService.getGroupsByUserId(user.getId());
        return list.stream().map(mainActivity -> {
            //用户当前的目标人群
            List<ActivityUserGroup> activityAllowGroupsCopy = groupByActIdMap.getOrDefault(mainActivity.getId(), new ArrayList<>());
            List<ActivityUserGroup> retainList = activityAllowGroupsCopy.stream().filter(a -> groups.contains(a.getGroupId())).toList();
            if (!activityAllowGroupsCopy.isEmpty()) {
                Integer type = activityAllowGroupsCopy.get(0).getType();
                activityAllowGroupsCopy.retainAll(groups);
                if ((retainList.isEmpty() && type == 0) ||
                        (!retainList.isEmpty() && type == 1)) {
                    return null;
                }
            }

            CategoryActivityVo vo = new CategoryActivityVo();
            vo.setActivityId(mainActivity.getId());
            vo.setMainType(mainActivity.getMainType());
            ZonedDateTime startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
            ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
            vo.setActivityState(mainActivityService.getActivityState(mainActivity.getId(), user));
            //设置活动时间
            if (mainActivity.getTimeStyle() == 0) {
                vo.setActivityStartTime(startTime);
                vo.setActivityEndTime(endTime);
            } else {
                vo.setActivityStartTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), user.getZoneId())));
                vo.setActivityEndTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), user.getZoneId())));

            }


            List<MyRaceCalendarSubActivityListVo> myRaceCalendarSubActivityListVos = subActivityMap.get(mainActivity.getId());
            //设置目标
            if (!org.springframework.util.CollectionUtils.isEmpty(myRaceCalendarSubActivityListVos) && myRaceCalendarSubActivityListVos.size() == 1) {
                vo.setTarget(myRaceCalendarSubActivityListVos.get(0).getTarget());
            }
            vo.setTargetType(mainActivity.getTargetType());
            //设置图片、标题
            ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), user.getLanguageCode());
            if (Objects.nonNull(disseminate)) {
                vo.setActivityCoverImage(disseminate.getDisseminatePics());
                vo.setActivityTitle(disseminate.getTitle());
                vo.setIsShowMarquee(disseminate.getMarquee());
            }

            //设置标签
            if (MapUtils.isNotEmpty(categoryItemHot)) {
                ActivityCategoryItem categoryItem = categoryItemHot.get(mainActivity.getId());
                if (Objects.nonNull(categoryItem)) {
                    vo.setSign(categoryItem.getSign());
                }
            }

            //设置费用
            ActivityFee feeEntry = activityFeeService.findFeeEntry(mainActivity.getId(), userAccount.getCurrencyCode());
            if (Objects.nonNull(feeEntry)) {
                vo.setActivityEntryFee(feeEntry.getAmount());
                vo.setActivityEntryScore(feeEntry.getScore());
            }
            vo.setCurrency(currency);
            //设置设备信息
            List<ActivityEquipmentConfig> equipments = activityEquipmentConfigService.findByActId(mainActivity.getId());
            List<Integer> equipmentInfos = equipments.stream().filter(a -> Objects.nonNull(a.getEquipmentType())).map(ActivityEquipmentConfig::getEquipmentType).distinct().toList();
            vo.setDeviceType(equipmentInfos);

            //设置用户缉奖励信息
            List<Long> activityIdList = new ArrayList<>();
            activityIdList.add(vo.getActivityId());
            List<ActivityUserAwardVo> userAward = activityUserAwardBizService.findUserAward(activityIdList);
            if (!CollectionUtils.isEmpty(userAward)) {
                Collections.shuffle(userAward);
                List<ActivityUserAwardVo> randomUserAwardList = userAward.stream()
                        .limit(10)
                        .collect(Collectors.toList());
                vo.setAwardUserList(randomUserAwardList);
            }
            //查询参与用户
            List<UserSimpleVo> activitySimpleUser = runActivityUserService.findActivitySimpleUser(vo.getActivityId(), 3, mainActivity.getOldType());
            vo.setParticipantList(activitySimpleUser);
            Integer activityUserCount = runActivityUserService.findActivityUserCount(vo.getActivityId());
            vo.setParticipantCount(activityUserCount);
            //路由获取
            RotationArea route = rotationAreaBizService.getNewActivityRoute(vo.getActivityId(), mainActivity.getMainType(), 2);
            vo.setUrl(route.getUrl());
            vo.setJumpParam(route.getJumpParam());
            ActivityTeamJoinSettingDto activityTeamSetting = activityParamsLoaderService.getActivityTeamSetting(vo.getActivityId());
            vo.setActivityLimitNums(Objects.nonNull(activityTeamSetting) && Objects.nonNull(activityTeamSetting.getAllowJoinTeamMemberLimit()) ? activityTeamSetting.getAllowJoinTeamMemberLimit() : 0);
            //判断活动是否结束
            if (Objects.equals(mainActivity.getActivityState(), MainActivityStateEnum.ENDED.getCode())) {
                if (!CollectionUtils.isEmpty(userAward)) {
                    List<UserRankDetailDto> collect = userAward.stream().sorted(Comparator.comparing(ActivityUserAwardVo::getAward).reversed()).limit(3).map(s -> {
                        UserRankDetailDto userRankDetailDto = new UserRankDetailDto();
                        BeanUtils.copyProperties(s, userRankDetailDto);
                        return userRankDetailDto;
                    }).collect(Collectors.toList());
                    vo.setUserRankDetailList(collect);
                }
            }
            return vo;
        }).filter(Objects::nonNull).toList();
    }

    /**
     * 是否可以新建队伍
     *
     * @param req
     * @return
     */
    public KolTeamCreateStateRespDto kolTeamCreateState(KolTeamCreateStateReqDto req) {
        KolTeamCreateStateRespDto resp = new KolTeamCreateStateRespDto();
        Optional<Club> byOwnerId = clubService.findByOwnerId(req.getUserId());
        if (byOwnerId.isPresent() && byOwnerId.get().isValid()) {
            Optional<ClubActivityTeam> clubActivityTeam = clubActivityTeamService.findByActivityIdAndClubId(req.getMainActivityId(), byOwnerId.get().getId());
            if (clubActivityTeam.isPresent()) {
                resp.setCanCreateActivityTeam(false);
                resp.setActivityTeamId(clubActivityTeam.get().getActivityTeamId());
            } else {
                resp.setCanCreateActivityTeam(true);
            }
        } else {
            //没有新建俱乐部
            resp.setCanCreateActivityTeam(false);
        }
        return resp;
    }


    /**
     * 新建俱乐部队伍
     *
     * @param activityId
     * @param userId
     * @return
     */
    public Long createClubTeam(Long activityId, Long userId) {
        //新建一个俱乐部队伍
        String lockKey = RedisConstants.LOCK_ACTIVITY_TEAM_CREATE + activityId + ":" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean getLock = lock.tryLock();
        try {
            if (!getLock) {
                log.info("createClubTeam 获取锁失败,{},{}", activityId, userId);
                throw new BizI18nException("club.op.fail");
            }
            Optional<Club> byOwnerId = clubService.findByOwnerId(userId);
            if (byOwnerId.isEmpty()) {
                log.info("新建队伍失败，俱乐部不存在或状态异常,{},{}", activityId, userId);
                throw new BizI18nException("club.op.fail");
            }
            if (!byOwnerId.get().isNormal()) {
                throw new BizI18nException("club.apply.club_state_freeze");
            }
            if (clubActivityTeamService.findByActivityIdAndClubId(activityId, byOwnerId.get().getId()).isPresent()) {
                log.info("新建队伍失败，已经存在,{},{}", activityId, userId);
                throw new BizI18nException("club.op.fail");
            }
            ActivityTeamJoinSettingDto activityTeamSetting = activityParamsLoaderService.getActivityTeamSetting(activityId);
            Long teamCount = activityTeamService.getTeamsCountByActivityId(activityId);
            if (activityTeamSetting.checkTeamCountLimit(teamCount)) {
                log.info("新建队伍达到上限,{},{}", activityId, userId);
                throw new BizI18nException("club.activity.team_full");
            }
            return transactionTemplate.execute(status -> {
                Club club = byOwnerId.get();
                ActivityTeam activityTeam = new ActivityTeam();
                activityTeam.setTeamName(club.getName());
                activityTeam.setTeamLogo(club.getLogo());
                activityTeam.setActivityId(activityId);
                activityTeam.setMaxNum(activityTeamSetting.getAllowJoinTeamMemberLimit());
                activityTeam.setIsOfficial(0);
                activityTeam.setTeamManagerId(userId);
                activityTeam.setTeamType(ActivityTeamTypeEnum.CLUB.getCode());
                activityTeamService.save(activityTeam);

                ClubActivityTeam clubTeam = new ClubActivityTeam();
                clubTeam.setClubId(club.getId());
                clubTeam.setActivityTeamId(activityTeam.getId());
                clubTeam.setMainActivityId(activityId);
                clubTeam.setState(ClubActivityTeamEnrollStateEnum.CREATE.getCode());
                clubActivityTeamService.insert(clubTeam);

                return activityTeam.getId();
            });


        } catch (BizI18nException e) {
            throw e;
        } catch (Exception e) {
            log.error("createClubTeam 新建队伍异常,{},{}", activityId, userId, e);
            throw new BizI18nException("club.op.fail");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                //判断锁是否存在，和是否当前线程加的锁。
                lock.unlock();
            }
        }
    }

    /**
     * 俱乐部 参加比赛
     *
     * @param userId
     * @param mainActivityId
     * @param teamId         队伍id
     */
    @Transactional(rollbackFor = Throwable.class)
    public void enrollActivity(Long userId, Long mainActivityId, Long teamId) {
        Optional<ClubActivityTeam> byTeamId = clubActivityTeamService.findByTeamId(teamId);
        if (byTeamId.isEmpty()) {
            throw new BizI18nException("club.op.fail");
        }
        ClubActivityTeam clubTeam = byTeamId.get();
        //获取相关俱乐部信息，增加俱乐部参与比赛次数
        Optional<Club> clubOP = clubService.findByOwnerId(userId);
        if (clubOP.isPresent()) {
            Club club = clubOP.get();
            //已经新建了队伍，就只能参加自己的队伍了
            if (!clubTeam.getClubId().equals(club.getId())) {
                throw new BizI18nException("club.op.cannot.join.team");
            }
            if (!club.isNormal()) {
                throw new BizI18nException("club.apply.club_state_freeze");
            }
            clubService.addActivityCount(club.getId(), mainActivityId);
            //报名参赛。队伍参赛状态修改为已经参赛
            clubActivityTeamService.enrollActivity(club.getId(), mainActivityId);
//            clubPushManager.sendClubActivityNotice(club.getId());

        } else {
            //还没有新建俱乐部，走普通人逻辑
            clubMemberCheckEnrollActivity(userId, clubTeam);
        }
    }

    /**
     * 普通会员，报名
     *
     * @param userId
     * @param clubTeam
     */
    private void clubMemberCheckEnrollActivity(Long userId, ClubActivityTeam clubTeam) {
        //普通人员，检查俱乐部状态
        Club byId = clubService.findById(clubTeam.getClubId());
        if (byId == null || !byId.isNormal()) {
            throw new BizI18nException("club.activity.team_deny");
        }
        if (!ClubActivityTeamEnrollStateEnum.JOINED.getCode().equals(clubTeam.getState())) {
            // 没有报名，用户也不能报名
            throw new BizI18nException("club.activity.team_deny");
        }
        //不是会员，不可以报名
        clubMemberService.findByClubAndUserId(clubTeam.getClubId(), userId)
                .orElseThrow(() -> new BizI18nException("club.activity.team_deny"));
    }

    public List<ClubRoomResponseDto> clubRoomList(Long activityId, Long clubId) {
        List<ClubRoomResponseDto> responseDtoList = new ArrayList<>();
        List<ClubActivityRoomRelationDo> list = clubActivityRoomRelationService.findList(ClubActivityRoomRelationQuery.builder().activityId(activityId).clubId(clubId).build());
        if (CollectionUtils.isEmpty(list)) {
            return responseDtoList;
        }
        List<Long> roomIds = list.stream().map(ClubActivityRoomRelationDo::getRoomId).collect(Collectors.toList());
        Map<Long, RoomDo> roomMap = roomService.findList(RoomQuery.builder().roomIds(roomIds).build()).stream().collect(Collectors.toMap(RoomDo::getId, Function.identity()));
        for (ClubActivityRoomRelationDo s : list) {
            ClubRoomResponseDto clubRoomResponseDto = new ClubRoomResponseDto();
            RoomDo roomDo = roomMap.get(s.getRoomId());
            if (Objects.isNull(roomDo) || Objects.equals(roomDo.getRoomStatus(), RoomStatusEnum.DISMISSED.getCode())) {
                log.info("房间不存在或已解散,{}", s.getRoomId());
                continue;
            }
            RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
            BeanUtils.copyProperties(roomDo, clubRoomResponseDto);
            BeanUtils.copyProperties(roomConfigDo, clubRoomResponseDto);
            clubRoomResponseDto.setId(roomDo.getId());
            ZnsUserEntity owner = znsUserService.findById(roomDo.getOwnerUserId());
            if (Objects.nonNull(owner)) {
                clubRoomResponseDto.setHeadPortrait(owner.getHeadPortrait());
                clubRoomResponseDto.setOwnerUserName(owner.getFirstName());
                clubRoomResponseDto.setCountry(owner.getCountry());
                I18nConstant.CountryCodeEnum countryEnum = I18nConstant.CountryCodeEnum.findByEnName(owner.getCountry());
                String flag = CountryFlagConstant.FlagMap.get(Optional.ofNullable(countryEnum).orElse(I18nConstant.CountryCodeEnum.DEFAULT).enName);
                clubRoomResponseDto.setCountryImage(flag);
            }
            clubRoomResponseDto.setSelfIntroduction(roomConfigDo.getSelfIntroduction());
            ZnsUserEquipmentEntity userLastConnectedEquipment = znsUserEquipmentService.getUserLastConnectedEquipment(roomDo.getOwnerUserId());
            if (Objects.nonNull(userLastConnectedEquipment)) {
                clubRoomResponseDto.setEquipmentModel(userLastConnectedEquipment.getEquipmentModel());
                ZnsTreadmillEntity znsTreadmill = znsTreadmillService.findByUniqueCode(userLastConnectedEquipment.getEquipmentNo());
                DeviceConstant.EquipmentMainTypeEnum enumByCode = DeviceConstant.EquipmentMainTypeEnum.findEnumByCode(znsTreadmill.getEquipmentMainType());
                if (Objects.isNull(enumByCode)) {
                    enumByCode = DeviceConstant.EquipmentMainTypeEnum.TREADMILL;
                }
                clubRoomResponseDto.setEquipmentUrl(enumByCode.getImageUrl());
            }
            responseDtoList.add(clubRoomResponseDto);
        }
        if (CollectionUtils.isEmpty(responseDtoList)) {
            return responseDtoList;
        }
        responseDtoList.sort(Comparator.comparing(ClubRoomResponseDto::getRoomStatus));
        return responseDtoList;
    }

    public ClubEventTotalDto getClubEventTotal(Long clubId) {
        ClubEventTotalDto clubEventTotalDto = new ClubEventTotalDto();
        ClubJoinActivityListReqDto clubJoinActivityListReqDto = new ClubJoinActivityListReqDto();
        clubJoinActivityListReqDto.setClubId(clubId);
        Page<Long> mainActivityPage = clubActivityTeamService.findMainActivityIdByClub(clubJoinActivityListReqDto);
        List<Long> activityList = mainActivityPage.getRecords();
        if (!CollectionUtils.isEmpty(activityList)) {
            List<MainActivity> listByIds = mainActivityService.findListByIds(activityList);
            clubEventTotalDto.setNoFinishEvent((int) listByIds.stream().filter(s -> Objects.equals(s.getActivityState(), MainActivityStateEnum.STARTED.getCode()) | Objects.equals(s.getActivityState(), MainActivityStateEnum.NOT_STARTED.getCode())).count());
            clubEventTotalDto.setFinishEvent((int) listByIds.stream().filter(s -> Objects.equals(s.getActivityState(), MainActivityStateEnum.ENDED.getCode())).count());
            ClubRunDataDo clubRunDataDo = clubRunDataService.findByQuery(ClubRunDataQuery.builder().clubId(clubId).build());
            clubEventTotalDto.setTotalTeamAward(Objects.nonNull(clubRunDataDo) ? clubRunDataDo.getTotalBonus() : BigDecimal.ZERO);
        }
        return clubEventTotalDto;
    }

    public ClubEventNotJoinInfoDto getClubEventNotJoinInfoDto(Long clubId) {
        ClubEventNotJoinInfoDto clubEventNotJoinInfoDto = new ClubEventNotJoinInfoDto();
        List<ClubActivityTeam> clubActivityTeams = clubActivityTeamService.findListByQuery(ClubActivityTeamQuery.builder().clubId(clubId).build());
        if (!CollectionUtils.isEmpty(clubActivityTeams)) {
            clubEventNotJoinInfoDto.setTeamEventCount(clubActivityTeams.size());
            List<Long> teamList = clubActivityTeams.stream().map(ClubActivityTeam::getActivityTeamId).collect(Collectors.toList());
            activityTeamService.findList(ActivityTeamQuery.builder().ids(teamList).build()).stream().max(Comparator.comparing(ActivityTeam::getRank)).ifPresent(s -> clubEventNotJoinInfoDto.setTeamMaxRank(s.getRank()));
        }
        ClubRunDataDo clubRunDataDo = clubRunDataService.findByQuery(ClubRunDataQuery.builder().clubId(clubId).build());
        clubEventNotJoinInfoDto.setTotalMileage(Objects.nonNull(clubRunDataDo) ? clubRunDataDo.getRunMileage() : BigDecimal.ZERO);
        return clubEventNotJoinInfoDto;
    }

    public BigDecimal clubTotalRankAward(Long clubId, List<Long> activityList) {
        List<ClubActivityTeam> clubActivityTeams = clubActivityTeamService.findByActivityIdSAndClubId(activityList, clubId);
        if (CollectionUtils.isEmpty(clubActivityTeams)) return BigDecimal.ZERO;
        List<Long> teamIdList = clubActivityTeams.stream().map(ClubActivityTeam::getActivityTeamId).collect(Collectors.toList());
        List<ActivityTeam> activityTeamList = activityTeamService.findList(ActivityTeamQuery.builder().ids(teamIdList).build());
        return activityTeamList.stream().map(ActivityTeam::getRankAward).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
