package com.linzi.pitpat.data.activityservice.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SourceType 枚举类，用于表示来源类型
 */
@Getter
@AllArgsConstructor
public enum SourceType {
    /**
     * 传统道具赛
     */
    TRADITIONAL_PROP_RACE(0, "传统道具赛"),
    /**
     * 用户赛道具赛
     */
    USER_PROP_RACE(1, "用户赛道具赛"),
    USER_FREE_CHALLENGE_RACE(2, "自由挑战赛道具赛"),
    ;

    private final int type;
    private final String desc;

}
