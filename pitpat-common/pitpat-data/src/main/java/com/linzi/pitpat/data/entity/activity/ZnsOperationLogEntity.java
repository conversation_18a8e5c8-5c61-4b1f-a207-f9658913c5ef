package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 操作记录表
 *
 * <AUTHOR>
 * @date 2022-05-12 10:30:27
 */
@TableName("zns_operation_log")
@Data
@NoArgsConstructor
public class ZnsOperationLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 是否删除 0：不删除 1：删除
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;
    /**
     * 最后修改时间
     */
    private ZonedDateTime gmtModified;
    /**
     * 操作标题
     */
    private String optTitle;
    /**
     * 操作描述
     */
    private String optDesc;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 0：商品操作记录，1：商品审核记录，2：订单，3：售后订单
     */
    private Integer type;
    /**
     * 关联id，type为0：商品id，1：商品id,
     */
    private Long refId;
}
