package com.linzi.pitpat.data.activityservice.strategy;

import com.google.common.collect.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.DingTalkTokenEnum;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityBrandInterestsBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RunDataBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.manager.ActivityUserManager;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.manager.MindUserMatchManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityPayManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityProcessManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityUserTaskManager;
import com.linzi.pitpat.data.activityservice.mapper.MilestonePopDao;
import com.linzi.pitpat.data.activityservice.mapper.RunActivityShowUserDao;
import com.linzi.pitpat.data.activityservice.mapper.RunActivityUserTaskDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsUserRunDataDetailsDao;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.activityservice.model.vo.TeamRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.awardservice.biz.CurrencyBizService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.dto.BrandRightsInterestListDto;
import com.linzi.pitpat.data.awardservice.model.dto.RunDetailDataDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserRunCertificate;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardLimitRuleService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.UserAccountDetailSubService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserRunCertificateService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBattlePassService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.UserOnlineBussiness;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.request.course.UseCouponRequest;
import com.linzi.pitpat.data.robotservice.service.RobotRunModeService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.UserPushToken;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserPushTokenService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.vo.RunRouteVO;
import com.linzi.pitpat.data.vo.home.HomepageActivityVo;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Slf4j
@RefreshScope
public abstract class BaseActivityStrategy {

    @Autowired
    protected UserCouponService userCouponService;
    @Autowired
    protected RunActivityUserTaskDao runActivityUserTaskDao;
    @Autowired
    protected UserPropRecordService userPropRecordService;
    @Resource
    protected ZnsRunActivityService runActivityService;
    @Resource
    protected ZnsRunActivityUserService runActivityUserService;
    @Resource
    protected ActivityUserManager activityUserManager;
    @Resource
    protected ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    protected ZnsRunRouteService runRouteService;
    @Resource
    protected RedissonClient redissonClient;
    @Resource
    protected ZnsUserAccountService userAccountService;
    @Resource
    protected ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    protected ZnsUserRunRecordService userRunRecordService;
    @Resource
    protected ZnsUserRunDataDetailsDao userRunDataDetailsDao;
    @Resource
    protected ZnsUserFriendService userFriendService;
    @Resource
    protected MindUserMatchService mindUserMatchService;
    @Resource
    protected MindUserMatchManager mindUserMatchManager;
    @Resource
    protected AppMessageService appMessageService;
    @Resource
    protected ZnsUserService userService;
    @Resource
    protected UserPushTokenService userPushTokenservice;
    @Resource
    protected ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    protected RedisTemplate redisTemplate;
    @Resource
    protected MedalConfigService medalConfigService;
    @Resource
    protected ISysConfigService sysConfigService;
    @Resource
    protected RobotRunModeService robotRunModeService;
    @Value("${admin.server.gamepush}")
    protected String gamepush;
    @Resource
    protected SocketPushUtils socketPushUtils;
    @Resource
    protected UserAccountDetailSubService userAccountDetailSubService;
    @Resource
    protected UserOnlineBussiness userOnlineBussiness;
    @Resource
    protected RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    protected ActivityCouponConfigService activityCouponConfigService;

    protected Map<String, Object> jsonObjectConfig;

    protected BigDecimal acceptCountDecimal;

    @Autowired
    protected ActivityUserScoreDao activityUserScoreDao;
    @Resource
    protected ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    @Resource
    protected ActivityBrandInterestsBizService activityBrandInterestsBizService;
    @Resource
    protected MilestonePopDao milestonePopDao;
    @Value("${spring.profiles.active}")
    protected String profile;

    @Resource
    protected AwardConfigBizService awardConfigBizService;
    @Resource
    protected AwardActivityManager awardActivityManager;
    @Autowired
    protected ActivityUserScoreService activityUserScoreService;

    @Autowired
    @Lazy
    protected CouponService couponService;
    @Resource
    protected AwardLimitRuleService awardLimitRuleService;
    @Resource
    protected UserWearsBagService userWearsBagService;
    @Resource
    private ActivityTaskConfigService activityTaskConfigService;
    @Resource
    protected UserWearsBattlePassService userWearsBattlePassService;
    @Resource
    protected RunActivityShowUserDao runActivityShowUserDao;

    @Autowired
    protected ZnsUserService znsUserService;

    @Resource
    protected AppUpgradeService appUpgradeService;
    @Autowired
    protected ExchangeRateConfigService exchangeRateConfigService;

    @Resource
    protected AreaService areaService;
    @Resource
    protected RunDataBizService runDataBizService;
    @Resource
    protected RunActivityBizService runActivityBizService;
    @Resource
    protected RunActivityUserTaskManager runActivityUserTaskManager;
    @Resource
    protected UserCouponManager userCouponManager;
    @Resource
    protected CurrencyBizService currencyBizService;
    @Resource
    protected UserCouponBizService userCouponBizService;
    @Resource
    protected RunActivityPayManager runActivityPayManager;
    @Resource
    protected RunActivityProcessManager runActivityProcessManager;
    @Resource
    protected ActivityUserBizService activityUserBizService;
    @Resource
    protected MindUserMatchBizService mindUserMatchBizService;

    /**
     * 活动详情基础数据
     *
     * @param activityEntity
     * @param activityDetailVO
     * @param userEntity
     */
    public void wrapperRunActivityBasicData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity) {
        // 活动详情数据拷贝
        BeanUtils.copyProperties(activityEntity, activityDetailVO);
        String activityConfigstr = activityEntity.getActivityConfig();
        Map<String, Object> activityConfigMap = JsonUtil.readValue(activityConfigstr);
        Integer maxRunScore = MapUtil.getInteger(activityConfigMap.get("maxRunScore"), -1);
        Integer minRunScore = MapUtil.getInteger(activityConfigMap.get("minRunScore"), -1);
        activityDetailVO.setMinRunScore(minRunScore);
        activityDetailVO.setMaxRunScore(maxRunScore);

        activityDetailVO.setActivityId(activityEntity.getId());
        String appType = "ios";// ios的版本更加准确
        Integer versionCode = appUpgradeService.selectShelvedLatestVersion(appType);
        if (versionCode < 2120) {
            UseCouponRequest request = new UseCouponRequest();
            request.setPageNum(1);
            request.setPageSize(1000000);
            request.setActivityId(activityEntity.getId());
            request.setUserId(userEntity.getId());
            int userCouponCount = 0;                                //用户的可用券
            PPageUtils pageUtils = userCouponManager.selectUserCouponByUseCouponRequest(request);
            List<UserCouponDiKou> userCouponDiKous = pageUtils.getRows();
            if (userCouponDiKous != null) {
                for (UserCouponDiKou userCouponDiKou : userCouponDiKous) {
                    //过滤掉过期的优惠券
                    if (userCouponDiKou.getGmtEnd().isBefore(ZonedDateTime.now())) {
                        return;
                    }
                    if (Objects.equals(userCouponDiKou.getIsCanUse(), 1)) {
                        userCouponCount++;
                    }
                }
            }
            activityDetailVO.setCanUseCoupon(userCouponCount);
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), userEntity.getId());
        if (null != activityUser) {
            activityDetailVO.setUserState(activityUser.getUserState());
            activityDetailVO.setUserType(activityUser.getUserType());
            activityDetailVO.setTargetRunMileage(activityUser.getTargetRunMileage());
            activityDetailVO.setTargetRunTime(activityUser.getTargetRunTime());
            Map<String, Object> myGrades = new HashMap<>();
            myGrades.put("runTime", activityUser.getRunTime());
            Integer runTimeMillisecond = activityUser.getRunTimeMillisecond();
            if ((Objects.isNull(runTimeMillisecond) || runTimeMillisecond == 0) && activityUser.getRunTime() > 0) {
                runTimeMillisecond = activityUser.getRunTime() * 1000 + 999;
            }
            myGrades.put("runTimeMillisecond", runTimeMillisecond);
            myGrades.put("runMileage", activityUser.getRunMileage().intValue());
            myGrades.put("runAward", activityUser.getRunAward());
            myGrades.put("targetRunTime", activityUser.getTargetRunTime());
            myGrades.put("targetRunMileage", activityUser.getTargetRunMileage());
            myGrades.put("nickname", userEntity.getFirstName());

            if (YesNoStatus.YES.getCode().equals(activityEntity.getPropSupport())
                    && RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                // 官方组队跑道具逻辑处理，仅里程跑支持道具
                ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.getLatestUserActivityRunDataDetails(activityUser.getUserId(), activityEntity.getId());
                if (Objects.nonNull(detailsEntity)) {
                    Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUser.getUserId(), activityEntity.getId(), detailsEntity.getId(), null);
                    if (effectValue > 0) {
                        myGrades.put("propEffectTime", effectValue);
                    }
                }
            }

            activityDetailVO.setMyGrades(myGrades);
            activityDetailVO.setIsCheat(activityUser.getIsCheat());

            String chat_robot_user_ids = sysConfigService.selectConfigByKey("chat_robot_user_ids");
            if (StringUtils.hasText(chat_robot_user_ids)) {
                String[] split = chat_robot_user_ids.split(",");
                String chatRobotUserId = split[0];
                activityDetailVO.setChatId(chatRobotUserId);
                ZnsUserEntity znsUser = userService.findById(Long.valueOf(chatRobotUserId));
                if (Objects.nonNull(znsUser)) {
                    activityDetailVO.setChatName(znsUser.getFirstName());
                }
            }
        } else {
            // 设置默认状态
            activityDetailVO.setUserState(0);
        }

        //跑步报告,只有跑步完成才返回
        if (Objects.nonNull(activityUser) && activityUser.getRunDataDetailsId() > 0) {
            ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.findById(activityUser.getRunDataDetailsId());
            if (Objects.nonNull(detailsEntity) && detailsEntity.getIsDelete() == 0 && detailsEntity.getRunStatus() == 1 && detailsEntity.getRunTime() >= 60) {
                activityDetailVO.setRunDataDetailsId(detailsEntity.getId());
            }
        }
        //默认不是游客
        activityDetailVO.setIsVisitor(0);

        //活动对象
        if (activityEntity.getActivityObjectType() == 1) {
            MedalConfig medalConfig = medalConfigService.getById(activityEntity.getDemandMedalConfigId());
            if (Objects.nonNull(medalConfig)) {
                activityDetailVO.setDemandMedalName(medalConfig.getName());
                activityDetailVO.setDemandMedalUrl(medalConfig.getUrl());
            }
        } else if (activityEntity.getActivityObjectType() == 2) {
            activityDetailVO.setDemandUserLevel(activityEntity.getDemandUserLevel());
        }
        //品牌权益处理
        List<BrandRightsInterestListDto> brandRightsInterestListDtos = activityBrandRightsInterestsService.selectActivityBrandRightsInterestsByActivityId(activityEntity.getId());
        activityDetailVO.setBrandRightsInterestList(brandRightsInterestListDtos);
        if (!CollectionUtils.isEmpty(brandRightsInterestListDtos)) {
            BrandRightsInterestListDto dto = brandRightsInterestListDtos.stream().filter(b -> b.getRightsInterestsType() == 1).findFirst().orElse(null);
            if (Objects.nonNull(dto)) {
                activityDetailVO.setHasPrivilegeBrand(activityBrandInterestsBizService.hasPrivilegeBrand(activityEntity.getId(), userEntity.getId(), dto.getBrand()));
            }
        }
        //是否限速
        if (activityEntity.getRateLimitType() == -1) {
            activityDetailVO.setIsRateLimit(0);
        } else {
            activityDetailVO.setIsRateLimit(1);
        }
    }

    /**
     * 设置活动路线相关数据
     */
    public void wrapperRunActivityRouteData(Long activityRouteId, RunActivityDetailVO activityDetailVO) {
        if (Objects.isNull(activityRouteId) || activityRouteId == 0) {
            return;
        }
        ZnsRunRouteEntity routeEntity = runRouteService.selectRunRouteById(activityRouteId);
        if (null != routeEntity) {
            // 设置路线相关数据
            RunRouteVO routeVO = runRouteService.wrapperRunRouteVO(routeEntity);
            //计算挑战跑次数
            routeVO.setChallengeCount(runActivityService.getCountByRouteId(routeVO.getRouteId()));
            activityDetailVO.setRoute(routeVO);
        }
    }

    /**
     * 设置配置详情
     *
     * @param activityEntity
     * @param activityDetailVO
     * @param userEntity
     * @param oneself
     */
    public void wrapperRunActivityDetailData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        if (StringUtil.isEmpty(activityEntity.getActivityConfig())) {
            log.warn("活动详情获取配置详情失败，配置详情不存在");
            throw new BaseException("活动详情获取配置详情失败，配置详情不存在");
        }
        jsonObjectConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
        if (Objects.isNull(jsonObjectConfig)) {
            log.warn("活动详情获取配置详情失败，配置详情不存在");
            throw new BaseException("活动详情获取配置详情失败，配置详情不存在");
        }

        if (Objects.nonNull(jsonObjectConfig.get("runningGoalsUnit"))) {
            activityDetailVO.setRunningGoalsUnit(MapUtil.getInteger(jsonObjectConfig.get("runningGoalsUnit")));
        } else {
            activityDetailVO.setRunningGoalsUnit(0);
        }

        if (Objects.nonNull(jsonObjectConfig.get("runningGoals"))) {
            List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
            activityDetailVO.setRunningGoals(runningGoals);
        }

        String activityRule = String.valueOf(jsonObjectConfig.get(ApiConstants.ACTIVITY_RULE));
        activityDetailVO.setActivityRule(activityRule);

        // 活动详情图
        List<String> detailsImages = JsonUtil.readList(jsonObjectConfig.get(ApiConstants.DETAILS_IMAGE), String.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(detailsImages)) {
            activityDetailVO.setDetailsImages(detailsImages);
        }
        // 活动宣传图
        activityDetailVO.setActivityImages(Arrays.asList(String.valueOf(jsonObjectConfig.get(ApiConstants.ADVERTISING_IMAGE))));

        activityDetailVO.setMaxReward(getPreMaxReward(activityEntity.getActivityType(), activityEntity.getUserCount()));
        if (Objects.nonNull(oneself)) {
            activityDetailVO.setGoalCompleted(activityEntity.getCompleteRuleType() == 1 ? oneself.getRunMileage().intValue() : oneself.getRunTime());
            ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(oneself.getUserId());
            BigDecimal hasAward = oneself.getRunAward();
            if (accountEntity != null) {
                hasAward = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), hasAward);
            }
            activityDetailVO.setHasAward(hasAward);
        }
    }

    /**
     * 获得预计最大奖励
     *
     * @param activityType
     * @param userCount
     * @return
     */
    protected BigDecimal getPreMaxReward(Integer activityType, Integer userCount) {
        BigDecimal maxReward = BigDecimal.ZERO;
        return maxReward;
    }

    /**
     * 设置奖励规则和挑战规则
     *
     * @param runActivityEntity
     * @param jsonObject
     * @param challengeRunType
     * @param appVersion
     */
    protected void wrapperRunActivityRules(ZnsRunActivityEntity runActivityEntity, Map<String, Object> jsonObject, Integer challengeRunType, Integer appVersion) {
        jsonObjectConfig = jsonObject;
    }

    /**
     * 设置活动参与人
     *
     * @param activityEntity
     * @param activityDetailVO
     * @param userEntity
     * @param activityUserStatus
     */
    public abstract void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus);

    /**
     * 设置发起人
     *
     * @param activityDetailVO
     * @param activityId
     */
    protected void setActivityLaunchUser(RunActivityDetailVO activityDetailVO, Long activityId) {
        //查找活动发起人
        ZnsRunActivityUserEntity activityLaunchUser = runActivityUserService.findActivityLaunchUser(activityId);
        if (null != activityLaunchUser) {
            // 查找活动发起人
            ZnsUserEntity user = userService.findById(activityLaunchUser.getUserId());
            if (Objects.nonNull(user)) {
                activityDetailVO.setLaunchUserName(user.getFirstName());
                //设置活动标题
                activityDetailVO.setActivityTitle("Multi-Player Run initiated by " + user.getFirstName());
            }

        }
    }

    /**
     * 设置活动配置
     *
     * @param runActivity
     * @param activityConfig
     * @param measureUnit
     * @param userId
     * @param isAdd
     */
    public Map<String, Object> getActivityConfig(RunActivityRequest runActivity, String activityConfig, Integer measureUnit, Long userId, boolean isAdd) {
        jsonObjectConfig = JsonUtil.readValue(activityConfig);
        if (Objects.nonNull(runActivity.getRunningGoalsUnit())) {
            jsonObjectConfig.put("runningGoalsUnit", runActivity.getRunningGoalsUnit());
        }
        if (Objects.nonNull(runActivity.getParticipateAward())) {
            jsonObjectConfig.put("participateAward", runActivity.getParticipateAward());
        }
        if (Objects.nonNull(runActivity.getCompleteAward())) {
            jsonObjectConfig.put("completeAward", runActivity.getCompleteAward());
        }
        if (Objects.nonNull(runActivity.getWinnerAward())) {
            jsonObjectConfig.put("winnerAward", runActivity.getWinnerAward());
        }
        return jsonObjectConfig;
    }

    /**
     * 是否可以修改用户状态
     *
     * @param activityEntity
     * @return
     */
    public Result canUpdateUserActivityState(ZnsRunActivityEntity activityEntity) {
        if (activityEntity.getActivityState() == -1 || activityEntity.getActivityState() == 2) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid")); //"Activity ended / cancelled"
        }

        log.info("活动状态 canUpdateUserActivityState  ： " + activityEntity.getStatus());
        if (activityEntity.getStatus() == -1) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.notAvailable")); //"Activity is not available. Cannot sign up."
        }
        return checkRunActivityTime(activityEntity);
    }

    /**
     * 检查活动时间
     *
     * @param activityEntity
     * @return
     */
    protected abstract Result checkRunActivityTime(ZnsRunActivityEntity activityEntity);

    /**
     * 处理用户活动状态
     *
     * @param activityEntity
     * @param userStatus
     * @param user
     * @param password
     * @param runningGoals
     * @param immediatelyAdmission
     * @param taskId
     * @param request
     * @param checkVersion
     */
    public abstract Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion);

    /**
     * 是否可以修改活动用户状态
     *
     * @param activityEntity
     * @param user
     * @return
     */
    public abstract Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user);

    /**
     * 是否未答复状态
     *
     * @param activityStartTime
     * @param activityId
     * @param userId
     * @return
     */
    public Result isNoReply(ZonedDateTime activityStartTime, Long activityId, Long userId) {
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityId).userId(userId).userType(2)
                .build();
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findOne(userQuery);
        if (null != activityUser) {
            ZonedDateTime now = ZonedDateTime.now();
            // 活动状态=未开始 and 被挑战者参与状态=未答复
            if (ActivityUserStateEnum.NO_REPLY.getState().equals(activityUser.getUserState()) && activityStartTime.isAfter(now)) {
                return null;
            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.teamRun.misCondition"));
            }
        }
        return null;
    }

    /**
     * 计算总金额
     *
     * @param activity
     * @param acceptCount
     * @param entryFee
     * @param challengeRunType
     * @param userEntity
     * @param view             是否只查看
     * @return
     */
    public BigDecimal calculateTotalBonus(ZnsRunActivityEntity activity, Integer acceptCount, BigDecimal entryFee, Integer challengeRunType, ZnsUserEntity userEntity, Boolean view) {
        if (null == acceptCount) {
            log.warn("计算活动总奖金:活动接受人数不存在");
            return BigDecimal.ZERO;
        }
        if (null == activity) {
            log.warn("计算活动总奖金:活动配置ID不存在");
            return BigDecimal.ZERO;
        }
        if (null == entryFee) {
            log.info("计算活动总奖金:保证金不存在");
            entryFee = BigDecimal.ZERO;
        }
        acceptCountDecimal = new BigDecimal(acceptCount);
        try {
            jsonObjectConfig = JsonUtil.readValue(activity.getActivityConfig());
        } catch (Exception e) {
            log.error("计算活动总奖金池失败，activityConfig获取解析失败，e：" + e.getMessage());
        }

        return null;
    }

    /**
     * 上报用户活动状态前检查
     *
     * @param activityEntity
     * @param user
     * @return
     */
    public abstract Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user);

    /**
     * 活动结束处理
     *
     * @param activityEntity
     */
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        if (activityEntity.getActivityState() == 2) {                   // 活动状态：0表示未开始，1 表示进行中，2表示已结束，-1表示活动已取消
            log.info("活动已结束，无需处理");
            return;
        }
        //修改活动用户状态
        runActivityUserService.updateState(ActivityUserStateEnum.ENDED.getState(), RunActivityUserQuery.builder()
                .activityId(activityEntity.getId())
                .isDelete(0).userState(ActivityUserStateEnum.RUNING.getState())
                .build());
        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityEntity.getActivityType())) {
            //活动状态变更成已结束
            activityEntity.setActivityEndTime(ZonedDateTime.now());
        }
        activityEntity.setActivityState(ActivityStateEnum.FINISHED.getState());
        runActivityService.updateById(activityEntity);
        log.info("活动结束状态修改完成，活动id：{}", activityEntity.getId());
        //路线缓存添加
        Object c = redisTemplate.opsForValue().get(RedisConstants.RUN_ROUTE_COUNT + activityEntity.getActivityRouteId());
        if (Objects.isNull(c)) {
            runActivityService.getCountByRouteId(activityEntity.getActivityRouteId());
        } else {
            redisTemplate.opsForValue().increment(RedisConstants.RUN_ROUTE_COUNT + activityEntity.getActivityRouteId());
        }

    }

    public void handleRunActivityData(ZnsRunActivityEntity activityEntity) {
        // 更新跑步用户活动数据
        runDataBizService.updateRunActivityUserData(activityEntity);
        // 更新 runActivityData
        runDataBizService.updateRunActivityData(activityEntity);
    }

    /**
     * 奖励处理
     *
     * @param awardAmount
     * @param activityUser
     * @param activityEntity
     * @param extraAward
     * @param subType
     */
    public abstract Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType);

    /**
     * 处理活动历史用户
     *
     * @param activityEntity
     * @param activityDetailVO
     */
    public void wrapperRunActivityHistoryUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO) {
    }

    /**
     * 处理个人活动数据
     *
     * @param activityEntity
     * @param activityDetailVO
     * @param userId
     */
    public void wrapperRunActivityDataDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
    }

    /**
     * 获取活动跑步报告
     *
     * @param detail
     * @param loginUser
     * @param activityEntity
     * @param zoneId
     * @return
     */
    public abstract ActivityRunningReportBaseVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId);

    public Result checkHandleUserActivityState(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, Integer userStatus, ZnsUserEntity user) {
        if (activityEntity.getStatus() == -1) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.notAvailable"));
        }
        if (Objects.nonNull(activityUser)) {
            if (activityUser.getUserState() == 1 || activityUser.getUserState() == 3 || activityUser.getUserState() == 4) {
                return CommonResult.fail(ActivityError.ACTIVITY_ACCEPT.getCode(), I18nMsgUtils.getMessage("activity.validate.user.responded"));
            }
            if (activityUser.getUserState() == 2) {
                return CommonResult.fail(ActivityError.ACTIVITY_REJECT.getCode(), ActivityError.ACTIVITY_REJECT.getMsg());
            }
        }
        if (activityEntity.getActivityState() == -1) {
            return CommonResult.fail(ActivityError.ACTIVITY_EXPIRED.getCode(), I18nMsgUtils.getMessage("activity.signup.timeNoStart"));
        }

        //校验活动国家跟用户国家是否相同
        if (notContainsCountry(activityEntity, user)) {
            return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.mismatch.region"));
        }

        if (Objects.equals(user.getIsRobot(), 0)) {                     //如果是真实用户，则做限制
            String activityConfigstr = activityEntity.getActivityConfig();
            Map<String, Object> activityConfigMap = JsonUtil.readValue(activityConfigstr);
            Integer maxRunScore = MapUtil.getInteger(activityConfigMap.get("maxRunScore"), -1);
            Integer minRunScore = MapUtil.getInteger(activityConfigMap.get("minRunScore"), -1);
            log.info("最大竞技分和最小竞技分判断  maxRunScore  = " + maxRunScore + ",minRunScore = " + minRunScore + " ,activityId = " + activityEntity.getId() + ",userId = " + user.getId());
            if (!Objects.equals(maxRunScore, -1) || !Objects.equals(minRunScore, -1) || Arrays.asList(4, 5).contains(activityEntity.getBonusRuleType())) {
                Integer userScore = userService.getAllUserScore(user.getId());
                if (maxRunScore != null && maxRunScore > 0 && userScore < maxRunScore) {
                    log.info(user.getId() + ",用户竟技分为 " + userScore + ",  配置的maxRunScore = " + maxRunScore + ", userScore > maxRunScore");
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.racePoint.ge", maxRunScore));
                }
                if (minRunScore != null && minRunScore > 0 && userScore >= minRunScore) {
                    log.info(user.getId() + ", 用户竟技分为 " + userScore + ",  配置的minRunScore= " + minRunScore + ", userScore < minRunScore");
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.racePoint.le", minRunScore));
                }
                //若报名费包含积分，则判断用户积分是否足够
                if (!checkUserActivityEntryScore(activityEntity, userScore)) {
                    return CommonResult.fail(ActivityError.ACTIVITY_LACK_SCORE.getCode(), I18nMsgUtils.getMessage("activity.racePoint.insufficient"));
                }
            }
        }
        Result result = checkRunActivityTime(activityEntity);
        if (Objects.nonNull(result)) {
            return CommonResult.fail(ActivityError.ACTIVITY_EXPIRED.getCode(), I18nMsgUtils.getMessage("activity.signup.timeNoStart"));
        }

        return null;
    }

    /**
     * 检查用户积分是否大于活动报名所需积分
     *
     * @param activityEntity 活动
     * @param userScore      用户积分
     * @return true 用户积分足够， false 用户积分不足
     */
    private boolean checkUserActivityEntryScore(ZnsRunActivityEntity activityEntity, Integer userScore) {
        boolean containsEntryScore = Arrays.asList(4, 5).contains(activityEntity.getBonusRuleType());
        boolean legalEntryScore = Objects.nonNull(activityEntity.getActivityEntryScore()) && activityEntity.getActivityEntryScore().compareTo(BigDecimal.ZERO) > 0;
        if (containsEntryScore && legalEntryScore) {
            return BigDecimal.valueOf(userScore).compareTo(activityEntity.getActivityEntryScore()) >= 0;
        }
        return true;
    }

    /**
     * 计算个人奖金
     *
     * @param activityEntity
     * @param acceptCount
     * @param activityEntryFee
     * @return
     */
    public BigDecimal calculatePersonalBonus(ZnsRunActivityEntity activityEntity, Integer acceptCount, BigDecimal activityEntryFee) {
        acceptCountDecimal = new BigDecimal(acceptCount);
        return activityEntity.getActivityTotalBonus();
    }

    public void wrapperRunActivityVisitor(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        //判断是否是游客
        if (Objects.isNull(oneself)) {
            //游客返回游客房间号
            activityDetailVO.setIsVisitor(1);
        }
    }

    public void homePageActivityMap(HomepageActivityVo map, ZnsRunActivityEntity activity, Long userId, String zoneId) {
        map.setActivityTitle(activity.getActivityTitle());
        map.setActivityStartTime(activity.getActivityStartTime());
        map.setActivityEndTime(activity.getActivityEndTime());
        map.setApplicationStartTime(activity.getApplicationStartTime());
        map.setApplicationEndTime(activity.getApplicationEndTime());
        map.setActivityState(activity.getActivityState());
        map.setCompleteRuleType(activity.getCompleteRuleType());
        map.setUserState(0);
        map.setId(activity.getId());
        map.setActivityRouteId(activity.getActivityRouteId());
        ZnsRunRouteEntity znsRunRouteEntity = runRouteService.selectRunRouteById(activity.getActivityRouteId());
        map.setRouteType(znsRunRouteEntity.getRouteType());

        jsonObjectConfig = JsonUtil.readValue(activity.getActivityConfig());
    }

    public List<? extends SimpleRunActivityVO> homePageActivityList(List<ZnsRunActivityEntity> activityEntityList, Long userId) {
        return new ArrayList<>();
    }

    public List<? extends SimpleRunActivityVO> getActivityList(ZnsUserEntity user, boolean isTest, boolean checkVersion, boolean isHomepage, Integer source, Integer completeRuleType, List<Integer> runWalkStatus, Integer rateLimitType) {
        return new ArrayList<>();
    }

    /**
     * 获取倒计时
     *
     * @param jsonObjectConfig
     * @return
     */
    public long getLastEnterRunTime(Map<String, Object> jsonObjectConfig) {
        Integer lastEnterMinutes = MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
        if (null == lastEnterMinutes) {
            lastEnterMinutes = 30;
        }
        long teamMillisecond = lastEnterMinutes.intValue() * 60000;
        return teamMillisecond;
    }

    /**
     * 开始跑前多少分钟可入场(毫秒)
     *
     * @param jsonObjectConfig
     * @return
     */
    public long getRunBeforeTime(Map<String, Object> jsonObjectConfig) {
        Integer runBeforeEnterMinutes = MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
        if (null == runBeforeEnterMinutes) {
            runBeforeEnterMinutes = 5;
        }
        long runBeforeEnterMinutesMillisecond = runBeforeEnterMinutes.intValue() * 60000;
        return runBeforeEnterMinutesMillisecond;
    }

    public String getWarmPrompt(Integer type) {
        if (Objects.isNull(jsonObjectConfig)) {
            return "";
        }
        String warmPrompt = String.valueOf(jsonObjectConfig.get(ApiConstants.WARM_PROMPT));
        if (StringUtil.isEmpty(warmPrompt)) {
            ZnsRunActivityConfigEntity configEntity = runActivityConfigService.getByType(type, null);
            if (Objects.isNull(configEntity)) {
                return "";
            }
            Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
            warmPrompt = MapUtil.getString(jsonObject.get(ApiConstants.WARM_PROMPT));
        }
        return warmPrompt;
    }

    public Integer getLimitPeopleSize() {
        return null;
    }


    @Autowired
    private UserRunCertificateService userRunCertificateService;

    @Autowired
    protected ZnsTreadmillService znsTreadmillService;

    public void genUserCertificate(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, RunDetailDataDto runDetailDataDto) {
        if (StringUtil.isEmpty(runDetailDataDto.getBrand())) {
            return;
        }
        UserRunCertificate userRunCertificate = null;
        ZnsUserEntity userEntity = userService.findById(activityUser.getUserId());
        // 品牌证书明细生成
        if (activityEntity.getFinishedCertificate() != -1) {
            if (activityEntity.getFinishedCertificate().equals(runDetailDataDto.getType()) || activityEntity.getFinishedCertificate() == 0) {
                userRunCertificate = new UserRunCertificate();
                BeanUtils.copyProperties(runDetailDataDto, userRunCertificate);
                userRunCertificate.setActivityId(activityEntity.getId());
                userRunCertificate.setUserId(activityUser.getUserId());
                userRunCertificate.setActivityDate(activityEntity.getActivityEndTime());
                userRunCertificate.setCertificateType(2);
                userRunCertificate.setCertificateName(activityEntity.getActivityTitle());
                userRunCertificate.setCertificateNo(runDetailDataDto.getBrand().substring(0, 1) + DateUtil.parseDateToStr(DateUtil.YYYYMMDD, activityEntity.getActivityEndTime()) + userEntity.getUserCode() + "");
                userRunCertificateService.save(userRunCertificate);
                sendCertificationImMsg(activityUser, userRunCertificate);
            }
        }
        // 第一名证书
        if (activityEntity.getFirstCertificate() != -1 && activityUser.getRank() == 1) {
            if (activityEntity.getFirstCertificate().equals(runDetailDataDto.getType()) || activityEntity.getFirstCertificate() == 0) {
                userRunCertificate = new UserRunCertificate();
                BeanUtils.copyProperties(runDetailDataDto, userRunCertificate);
                userRunCertificate.setActivityId(activityEntity.getId());
                userRunCertificate.setCertificateType(1);
                userRunCertificate.setUserId(activityUser.getUserId());
                userRunCertificate.setActivityDate(activityEntity.getActivityEndTime());
                userRunCertificate.setCertificateName(activityEntity.getActivityTitle());
                userRunCertificate.setCertificateNo(runDetailDataDto.getBrand().substring(0, 1) + DateUtil.parseDateToStr(DateUtil.YYYYMMDD, activityEntity.getActivityEndTime()) + userEntity.getUserCode() + "");
                userRunCertificateService.save(userRunCertificate);
                sendCertificationImMsg(activityUser, userRunCertificate);
            }
        }
    }

    private void sendCertificationImMsg(ZnsRunActivityUserEntity activityUser, UserRunCertificate userRunCertificate) {
        ImMessageBo imMessageBo = new ImMessageBo();
        String msg = "Congratulation, you have received the Certificate of " + (userRunCertificate.getCertificateType() == 1 ? "Winner" : "Completion") + " for " + userRunCertificate.getCertificateName();
        imMessageBo.setJumpType("6");
        Map<String, Object> params = new HashMap<>();
        params.put("certification_id", userRunCertificate.getId());
        imMessageBo.setParams(params);
        imMessageBo.setMsg(msg);
        imMessageBo.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202305/iHR13soPkmkk0092.png");
        imMessageBo.setJumpValue("lznative://main/myCertificationsDetail");
        appMessageService.sendIm("administrator", Collections.singletonList(activityUser.getUserId()), JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
    }

    public void wrapperCouponDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long id) {

    }

    public void wrapperStageImageUrl(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long id) {
    }

    /**
     * 异常通知
     */
    protected void exceptionNotification(String message) {
        //判断时间  美国太平洋早5晚10点时间 对应0时区 13-0 0-6
        ZonedDateTime date = ZonedDateTime.now();
        if (date.getHours() >= 6 && date.getHours() < 13) {
            log.info("exceptionNotification end,不在通知时间范围");
            return;
        }
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【Exception Notification】：" + message, "***********,***********,***********"), profile);

    }

    protected void rewardDistribution(List<AwardConfigDto> awardConfigDtoList, ZnsRunActivityUserEntity activityUser, List<Integer> sendTypes) {
        log.info("rewardDistribution start");
        List<AwardConfigDto> list = awardConfigDtoList.stream().filter(a -> sendTypes.contains(a.getSendType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            log.info("rewardDistribution list");
            return;
        }
        for (AwardConfigDto awardConfigDto : list) {
            // TODO: 2023/7/19 金额奖励暂时不在这里处理
            if (Objects.equals(awardConfigDto.getAwardType(), AwardTypeEnum.AMOUNT.getType())) {
                continue;
            }
            if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.WINNER_AWARD.getType())) {
                if (activityUser.getActivityType() == 2) {
                    //平局也发
                    if (!Objects.equals(0, activityUser.getRank()) && !Objects.equals(1, activityUser.getRank())) {
                        continue;
                    }
                }
            }
            if (Objects.equals(awardConfigDto.getAwardType(), AwardTypeEnum.COUPON.getType())) {
                List<Long> couponIds = NumberUtils.stringToLong(StringUtil.split(awardConfigDto.getCouponIds(), ","));
                for (Long couponId : couponIds) {
                    userCouponBizService.sendUserCouponSource(couponId, activityUser.getUserId(), activityUser.getActivityId(), 6, false, false);
                }
            }
        }
    }

    protected void rewardDistributionScore(List<AwardConfigDto> awardConfigDtoList, ZnsRunActivityUserEntity activityUser, List<Integer> sendTypes, Map<String, Object> poolConfig) {
        log.info("rewardDistributionScore start....");
        List<AwardConfigDto> list = awardConfigDtoList.stream().filter(a -> sendTypes.contains(a.getSendType())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            log.info("rewardDistributionScore list....");
            return;
        }
        Integer totalScore = 0;
        for (AwardConfigDto awardConfigDto : list) {
            // TODO: 2023/7/19 金额奖励暂时不在这里处理
            if (Objects.equals(awardConfigDto.getAwardType(), AwardTypeEnum.AMOUNT.getType())) {
                continue;
            }
            if (Objects.equals(awardConfigDto.getAwardType(), AwardTypeEnum.SCORE.getType())) {
                //参与奖励直接加
                if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.PARTICIPATION_AWARD.getType())) {
                    totalScore += awardConfigDto.getScore();
                } else if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.WINNER_AWARD.getType())) {
                    if (Objects.equals(activityUser.getRank(), 1)) {
                        totalScore += awardConfigDto.getScore();
                    } else if (Objects.equals(activityUser.getRank(), 0)) {
                        //平局平分
                        BigDecimal score = BigDecimalUtil.divide(new BigDecimal(awardConfigDto.getScore()), new BigDecimal(2)).setScale(0, BigDecimal.ROUND_UP);
                        totalScore += score.intValue();
                    }
                } else if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.LAUNCH_AWARD.getType())) {
                    totalScore += awardConfigDto.getScore();
                } else if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
                    totalScore += awardConfigDto.getScore();
                }
            }
        }

        if (sendTypes.contains(AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType())) {
            Integer rankScore = rewardDistributionRank(awardConfigDtoList, activityUser, AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType(), poolConfig);
            totalScore += rankScore;
        }

        if (totalScore <= 0) {
            log.info("rewardDistributionScore totalScore....");
            return;
        }

        activityUserScoreService.increaseAmount(totalScore, activityUser.getActivityId(), activityUser.getUserId(), activityUser.getRank(), 0, null);
    }

    protected Integer rewardDistributionRank(List<AwardConfigDto> awardConfigDtoList, ZnsRunActivityUserEntity activityUser, Integer sendType, Map<String, Object> poolConfig) {
        if (Objects.isNull(poolConfig) || poolConfig.isEmpty()) {
            return 0;
        }
        // TODO: 2023/7/19 当前处理积分
        BigDecimal score = awardConfigBizService.selectAwardRankAmount(poolConfig, BigDecimal.ZERO, activityUser.getRank(), AwardTypeEnum.SCORE.getType());
        if (score.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        return score.intValue();
    }

    public void wrapperActivityRewardDetailByActivityType(ZnsRunActivityEntity activityEntity, Map<String, Object> jsonObjectConfig, RunActivityRewardDetailVO runActivityRewardDetailVO, ZnsUserEntity loginUser) {
    }

    /**
     * 卷字段增加
     *
     * @param map
     */
    protected void queryCouponAwardColumn(Map<String, Object> map) {
        if (Objects.isNull(map)) {
            return;
        }
        for (String key : map.keySet()) {
            Object s = map.get(key);
            if (StringUtils.hasText(String.valueOf(s))) {
                Map<String, Object> jsonObject = JsonUtil.readValue(s);
                Long couponId = MapUtil.getLong(jsonObject.get("couponId"));
                if (Objects.nonNull(couponId)) {
                    Coupon coupon = couponService.selectCouponById(couponId);
                    jsonObject.put("couponType", coupon.getCouponType());
                    jsonObject.put("amount", coupon.getAmount());
                }
                map.put(key, jsonObject);
            }
        }
    }

    /**
     * 排名push + 跳转
     *
     * @param activityUser
     * @param activityEntity
     */
    protected void handleSendRankPush(ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity) {
        List<Long> userIds = Arrays.asList(activityUser.getUserId());
        String content = "Congratulations, you have achieved " + activityUser.getRank() + "th in " + activityEntity.getActivityTitle() + ", keep up the good work!";
        MessageBo messageBo = new MessageBo();
        messageBo.setTitle("Hello!" + activityUser.getNickname());
        messageBo.setContent(content);
        messageBo.setJumpType("5");
        messageBo.setRouteType(1);
        messageBo.setCollapseKey("PitPat");
        Map<String, Object> extras = new HashMap<>();
        UserPushToken userToken = userPushTokenservice.findByUserId(activityUser.getUserId());
        if (Objects.isNull(userToken)) {
            log.warn("用户={}push token 为空", activityUser.getUserId());
            userToken = new UserPushToken();
        }
        if (StringUtils.hasText(userToken.getIosPushToken()) && !"-1".equals(userToken.getIosPushToken())) {
            messageBo.setRouteValue("lznative://main/newrunningreport");
            extras.put("type", 1);
            extras.put("detailId", activityUser.getRunDataDetailsId());
        }
        if (StringUtils.hasText(userToken.getAndroidPushToken()) && !"-1".equals(userToken.getAndroidPushToken())) {
            messageBo.setRouteValue("lznative://main/newrunningreport");
            extras.put("activityId", activityEntity.getId());
            extras.put("REPORT_DETAIL_ID", activityUser.getRunDataDetailsId());
            extras.put("REPORT_DETAIL_TYPE", activityEntity.getActivityType());
        }
        messageBo.setData(extras);
        messageBo.setNotificationType(NoticeTypeEnum.ACTIVITY_RANK_NOTICE.getType());
        messageBo.setActivityId(activityEntity.getId());
        appMessageService.push(userIds, messageBo, "");
    }

    /**
     * 报名活动-活动国家是否不包含用户国家
     *
     * @param activityEntity
     * @param userEntity
     * @return true：不包含，false：包含
     */
    public boolean notContainsCountry(ZnsRunActivityEntity activityEntity, ZnsUserEntity userEntity) {
        if (UserConstant.RoboTypeEnum.IS_ROBOT_1.getCode().equals(userEntity.getIsRobot())) {
            //机器人暂时不做限制
            return false;
        }
        //不区分国家的活动（非官方活动+里程碑活动）
        List<Integer> noCountryTypes = Lists.newArrayList(RunActivityTypeEnum.TEAM_RUN.getType(),
                RunActivityTypeEnum.CHALLENGE_RUN.getType(),
                RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType());
        if (noCountryTypes.contains(activityEntity.getActivityType())) {
            return false;
        }
        //是否不包含
        return UserConstant.notContainsCountry(activityEntity.getCountry(), userEntity.getCountry());
    }

    public void wrapperEntryLimitInfo(ZnsRunActivityEntity activityEntity, ZnsUserEntity user, RunActivityDetailVO activityDetailVO) {
        if (Objects.nonNull(activityEntity.getTaskConfigId())) {
            Long taskConfigId = activityEntity.getTaskConfigId();
            ActivityTaskConfig taskConfigServiceById = activityTaskConfigService.findById(taskConfigId);
//            List<ZnsRunActivityEntity> znsRunActivityEntities = runActivityService.selectActivityListByTaskConfigId(taskConfigId);
//            List<Long> activityIds = znsRunActivityEntities.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
//            int entryNum = runActivityUserService.countUserEntryNum(user.getId(),activityIds);
            activityDetailVO.setEntryLimit(taskConfigServiceById.getEntryLimit());
//            activityDetailVO.setUserEntryNum(entryNum);
        }
    }

    /**
     * 检查当前活动是否配置使用积分报名
     *
     * @param activityEntity 活动
     * @return true 需要积分报名， false 不需要
     */
    protected boolean checkActivityEntryScore(ZnsRunActivityEntity activityEntity) {
        boolean containsEntryScore = Arrays.asList(4, 5).contains(activityEntity.getBonusRuleType());
        boolean legalEntryScore = Objects.nonNull(activityEntity.getActivityEntryScore()) && activityEntity.getActivityEntryScore().compareTo(BigDecimal.ZERO) > 0;
        return containsEntryScore && legalEntryScore;
    }

    protected void useUserScore(ZnsRunActivityEntity activityEntity, Long userId, boolean refund) {
        if (!checkActivityEntryScore(activityEntity)) {
            //未使用积分报名，无需抵扣或回退积分
            log.info("用户={}对活动={}发起的退款没有查询的使用积分记录，不需要抵扣或回退积分", userId, activityEntity.getId());
            return;
        }
        if (refund) {
            //如果是取消比赛，需要确定是否之前使用过积分
            ActivityUserScore existedActivityUserScore = activityUserScoreDao.selectActivityUserScoreByActivityUserId(activityEntity.getId(), userId, ScoreConstant.SourceTypeEnum.source_type_21.getType());
            //没有扣除或已经退回过积分，则不要退回积分
            if (Objects.isNull(existedActivityUserScore) || Objects.equals(existedActivityUserScore.getIncome(), 1)) {
                //如果是用户发起的退款，但是没有查到积分，则抛不做任何处理
                log.info("用户={}对活动={}发起的退款没有查询的使用积分记录，不需要返还积分", userId, activityEntity.getId());
                return;
            }
        }
        ActivityUserScore activityUserScore = getActivityUserScore(activityEntity, userId, refund);
        activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);
        if (!refund) {
            //抵扣积分
            activityUserScoreService.useActivityUserScore(activityUserScore.getScore().intValue(), activityEntity.getId(), userId, 0);
        }
        log.info("用户={}报名活动={}消耗了积分={}", userId, activityEntity.getId(), activityEntity.getActivityEntryScore());
    }

    private static ActivityUserScore getActivityUserScore(ZnsRunActivityEntity activityEntity, Long userId, boolean refund) {
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setActivityId(activityEntity.getId());
        activityUserScore.setExchangeScoreRuleId(-1L);
        activityUserScore.setScore(activityEntity.getActivityEntryScore().intValue());
        activityUserScore.setStatus(1);
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setType(4); //活动使用;
        activityUserScore.setIncome(refund ? 1 : -1); //退款会增加积分， 报名则会扣除积分
        activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_21.getType()); // 积分兑换券
        activityUserScore.setUserId(userId);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        if (refund) {
            activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
        }
        return activityUserScore;
    }

    /**
     * 处理 挑战跑 数据明细
     *
     * @param runRunningReportListVOS 跑步数据明细
     * @param completeRuleType
     * @return 挑战各方完赛状态:1：都完成，2：都未完成，3：部分完赛
     * @see RunActivityTypeEnum  活动类型
     */
    public Integer dealChallengeDetail(List<ChallengeRunRunningReportListVO> runRunningReportListVOS, Integer completeRuleType, Integer activityType) {
        if (CollectionUtils.isEmpty(runRunningReportListVOS)) {
            return null;
        }

        //设置完赛状态
        setIsComplete(runRunningReportListVOS, completeRuleType);

        // 完赛数量
        long completeNum = runRunningReportListVOS.stream().filter(e -> ActivityConstants.IsCompleteEnum.ISCOMPLETE_1.getCode().equals(e.getIsComplete())).count();

        //双方完赛状态
        Integer finishStatus = null; //挑战各方完赛状态:1：都完成，2：都未完成，3：部分完赛
        if (completeNum == 0) {
            finishStatus = ActivityConstants.FinishStatusEnum.FINISH_STATUS_2.getCode();
        } else if (completeNum == runRunningReportListVOS.size()) {
            finishStatus = ActivityConstants.FinishStatusEnum.FINISH_STATUS_1.getCode();
        } else {
            finishStatus = ActivityConstants.FinishStatusEnum.FINISH_STATUS_3.getCode();
        }
        return finishStatus;
    }

    /**
     * 设置完赛状态
     *
     * @param runRunningReportListVOS
     * @param completeRuleType
     */
    private void setIsComplete(List<ChallengeRunRunningReportListVO> runRunningReportListVOS, Integer completeRuleType) {
        for (ChallengeRunRunningReportListVO challengeRunRunningReportListVO : runRunningReportListVOS) {
            BigDecimal runMileage = challengeRunRunningReportListVO.getRunMileage();
            runMileage = Optional.ofNullable(runMileage).orElse(BigDecimal.ZERO);
            Integer runTime = challengeRunRunningReportListVO.getRunTime();
            runTime = Optional.ofNullable(runTime).orElse(0);
            //判断是否完赛
            if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode().equals(completeRuleType)) {
                //里程跑
                if (runMileage.compareTo(challengeRunRunningReportListVO.getTargetRunMileage()) >= 0) {
                    //完成里程跑
                    challengeRunRunningReportListVO.setIsComplete(ActivityConstants.IsCompleteEnum.ISCOMPLETE_1.getCode());
                } else {
                    //未完成里程跑
                    challengeRunRunningReportListVO.setIsComplete(ActivityConstants.IsCompleteEnum.ISCOMPLETE_0.getCode());
                }
            } else if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_2.getCode().equals(completeRuleType)) {
                //时长跑
                if (runTime >= challengeRunRunningReportListVO.getTargetRunTime()) {
                    //完成时长跑
                    challengeRunRunningReportListVO.setIsComplete(ActivityConstants.IsCompleteEnum.ISCOMPLETE_1.getCode());
                } else {
                    //未完成时长跑
                    challengeRunRunningReportListVO.setIsComplete(ActivityConstants.IsCompleteEnum.ISCOMPLETE_0.getCode());
                }
            }
        }
    }

    public void dealTeamRunDetail(List<TeamRunRunningReportListVO> teamRunRunningReport, Integer activityType, Long activityId) {
        if (CollectionUtils.isEmpty(teamRunRunningReport)) {
            return;
        }
        for (TeamRunRunningReportListVO reportListVO : teamRunRunningReport) {
            //真实用时时间
            reportListVO.setRealRunTimeMillisecond(reportListVO.getRunTimeMillisecond());
            //道具扣减时间
            if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityType) || RunActivityTypeEnum.TEAM_RUN.getType().equals(activityType)) {
                //多人同跑-计算道具使用
                Integer propTimeEffectValue = userPropRecordService.countUsePropTimeEffectValue(reportListVO.getUserId(), activityId, reportListVO.getUserRunDataDetailId(), null);
                reportListVO.setPropEffectTime(propTimeEffectValue);
            }
            //排名时间减道具时间
            if (Optional.ofNullable(reportListVO.getPropEffectTime()).orElse(0) > 0) {
                int runTime = reportListVO.getRealRunTimeMillisecond() - (reportListVO.getPropEffectTime() * 1000);
                runTime = Math.max(runTime, 0);
                reportListVO.setRunTimeMillisecond(runTime);
            }

        }
    }

    public BigDecimal getUserCurrencyAmount(Long userId, BigDecimal dollarAwardAmount) {
        Currency userCurrency = userAccountService.getUserCurrency(userId);
        // 官当活动兼容汇率计算
        if (!userCurrency.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
            ExchangeRateConfigEntity exchangeRateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(userCurrency.getCurrencyCode());
            if (Objects.nonNull(exchangeRateConfigEntity)) {
                // 处理货币汇率问题
                dollarAwardAmount = dollarAwardAmount.multiply(exchangeRateConfigEntity.getExchangeRate()).setScale(2, RoundingMode.HALF_UP);
            }
        }
        return I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), dollarAwardAmount);
    }

}
