package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 积分配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig;
import com.linzi.pitpat.data.awardservice.model.resp.ScoreConfigResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;

@Mapper
public interface ScoreConfigDao extends BaseMapper<ScoreConfig> {

    ScoreConfig selectScoreConfigById(@Param("id") Long id);

    ScoreConfigResp selectScoreConfigByUserId(@Param("id") Long id, @Param("calStype") String calStype, @Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    ScoreConfig selectScoreConfigByType(@Param("type") Integer type);
}
