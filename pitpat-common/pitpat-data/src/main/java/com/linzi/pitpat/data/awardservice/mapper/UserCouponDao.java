package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 用户优惠券表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-26
 */

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.resp.UserCouponResp;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.model.vo.UserMilestoneCoupon;
import com.linzi.pitpat.data.courseservice.model.request.CourseListRequest;
import com.linzi.pitpat.data.entity.dto.message.CouponOverdueRemindListDto;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.request.CouponPageQuery;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.lz.mybatis.plugin.annotations.AS;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.Count;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LIKE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.Order;
import com.lz.mybatis.plugin.annotations.OrderType;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface UserCouponDao extends BaseMapper<UserCoupon> {


    UserCoupon selectUserCouponById(@Param("id") Long id);

    @LIMIT
    UserCoupon selectUserCouponByCouponId(Long userId, Long couponId);

    int updateUserCouponById(UserCoupon userCoupon);


    @AS("uc")
    @Mapping(" uc.id as id ,c.id as couponId, c.title as couponName, c.coupon_type as couponType, u.first_name as nickName , u.email_address as emailAddress , " +
            " uc.gmt_create as gmtCreate , uc.gmt_end as gmtEnd  ,uc.gmt_use as gmtUse, uc.status as status ," +
            "uc.remarks as remarks, u.is_test as isTest,c.type as type, uc.source_type as sourceType ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uc.user_id = u.id "),
            @Item(value = Coupon.class, as = "c", on = " uc.coupon_id = c.id "),
    })
    @Order({
            @By(value = {"uc.id"}, type = OrderType.DESC)
    })
    List<UserCouponResp> selectPageByCondition(IPage page,
                                               @LIKE @IF @Column("c.title") String couponName,
                                               @LIKE @IF @Column("u.first_name") String nickName,
                                               @LIKE @IF @Column("u.email_address") String emailAddress,
                                               @IF @Column("uc.status") Integer status,
                                               @IF @Column("u.is_test") Integer isTest,
                                               @IF @Column("c.type") Integer type,
                                               @IF @Column("c.id") Long couponId,
                                               @IF @Column("uc.gmt_create") @GE ZonedDateTime gmtStartTime,
                                               @IF @Column("uc.gmt_create") @LE ZonedDateTime gmtEndTime
    );

    List<UserCouponResp> findList(IPage page, @Param("query") CouponPageQuery query);


    Page<CouponPageVo> getUserCouponPage(Page page, @Param("req") CourseListRequest req);

    Long getUserCouponCount(@Param("req") CourseListRequest req);

    List<CouponPageVo> selectUserCouponList(@Param("userId") Long userId, @Param("status") int status, @Param("couponTypes") List<Integer> couponTypes);


    @Count
    @LeftJoinOns({
            @Item(value = Coupon.class, left = UserCoupon.coupon_id, right = Coupon.id_),
    })
    Integer selectUserCouponByUserId(@Column(UserCoupon.user_id) Long userId, @Column(UserCoupon.status_) Integer status, @Column(Coupon.type_) Integer type);

    List<UserCouponDiKou> selectUserCouponByCondition(IPage page, @Param("userId") Long userId, @Param("couponType") Integer couponType,
                                                      @Param("status") Integer status, @Param("activityId") Long activityId, @Param("activityType") Integer activityType,
                                                      @Param("taskConfigId") Long taskConfigId, @Param("batchNo") String batchNo, @Param("userCouponId") Long userCouponId,
                                                      @Param("currencyCode") String currencyCode, @Param("onlyCouponId") Long onlyCouponId,
                                                      @Param("newActivityType") String newActivityType, @Param("newActivityTaskId") Long newActivityTaskId, @Param("couponMainType") Integer couponMainType);

    Integer selectUserCouponCountByCondition(@Param("userId") Long userId, @Param("couponType") Integer couponType, @Param("status") Integer status, @Param("activityId") Long activityId,
                                             @Param("activityType") Integer activityType, @Param("taskConfigId") Long taskConfigId, @Param("batchNo") String batchNo,
                                             @Param("currencyCode") String currencyCode, @Param("onlyCouponId") Long onlyCouponId);

    List<UserCouponDiKou> selectUserCouponCountByConditionList(@Param("userId") Long userId, @Param("couponType") Integer couponType, @Param("status") Integer status,
                                                               @Param("activityId") Long activityId, @Param("activityType") Integer activityType, @Param("taskConfigId") Long taskConfigId,
                                                               @Param("batchNo") String batchNo, @Param("currencyCode") String currencyCode, @Param("onlyCouponId") Long onlyCouponId);

    @LIMIT
    UserCoupon selectUserCouponByActivityIdCouponIdUserIdMilepost(Long activityId, Long couponId, Long userId, String milepost);

    UserCoupon getUserCouponByActivityAndUserIdAndCouponType(@Param("activityId") Long activityId, @Param("status") int i, @Param("userId") Long userId,
                                                             @Param("couponType") int couponType, @Param("currencyCode") String currencyCode);

    @DS("slave")
    List<CouponOverdueRemindListDto> overdueRemindList();

    List<CouponPageVo> getUserNewCouponByUserId(@Param("userId") Long userId, @Param("languageCode") String languageCode);

    Integer selectHistoryUseCouponCount(@Param("userId") Long userId);

    @Mapping("user_id,coupon_id,activity_id")
    List<UserCoupon> selectUserCouponByActivityIdAndSource(Long activityId, int sourceType);

    @Count
    Integer getCountUserCouponByActivityIdAndUserId(@Column(UserCoupon.activity_id) Long activityId, @Column(UserCoupon.user_id) Long userId, @Column(UserCoupon.gmt_create) @GE ZonedDateTime createTime);

    List<UserMilestoneCoupon> selectUserMilestoneCoupon(@Param("userId") Long userId);

    @Mapping({UserCoupon.all})
    @LeftJoinOns({
            @Item(value = Coupon.class, left = UserCoupon.coupon_id, right = Coupon.id_),
    })
    UserCoupon selectUserCouponOne(@Column(UserCoupon.activity_id) Long activityId, @Column(UserCoupon.user_id) Long userId, @IF @IN @Column(Coupon.coupon_type) List<Integer> couponTypes);

    /**
     * 查询优惠券统计信息
     * @param couponIds
     * @return
     */
    @MapKey("couponId")
    List<UserCouponStatisticsDto> selectCouponStatisticsDto(@Param("couponIds") List<Long> couponIds);

    /**
     * 更新用户 优惠券多币种 价格
     * @param userId 用户id
     * @param exchangeRate 汇率
     * @param currencyCode
     * @param currencyName
     * @param currencySymbol
     */
    void updateUserCouponCurrency(@Param("userId") Long userId, @Param("exchangeRate") BigDecimal exchangeRate, @Param("currencyCode") String currencyCode,
                                  @Param("currencyName") String currencyName, @Param("currencySymbol") String currencySymbol);

    @AS("uc")
    @Mapping({UserCoupon.all})
    @LeftJoinOns({
            @Item(value = Coupon.class, as = "c", on = "uc.coupon_id = c.id"),
    })
    UserCoupon getUserCouponByActIdUserIdCouponType(@Column("uc.use_activity_id") Long useActivityId, @Column("uc.user_id") Long userId, @IF @IN @Column("c.coupon_type") List<Integer> couponTypes);

    /**
     * 查询用户有钱的券
     * @param userId
     */
    List<UserCoupon> findUserAmountCoupon(Long userId);

    /**
     * 查询用户指定类型可用的通用优惠券
     */
    List<Long> findUserWholeCouponId(@Param("userId") Long userId, @Param("couponMainType") Integer couponMainType, @Param("isNowUse") Boolean isNowUse);

    /**
     * 查询用户指定类型可用的限定优惠券
     */
    List<Long> findUserSpecialCouponId(@Param("userId") Long userId, @Param("spuIds") List<Long> spuIds, @Param("couponMainType") Integer couponMainType, @Param("isNowUse") Boolean isNowUse);

    Page<UserCouponResp> findPage(Page<Object> objectPage, CouponPageQuery query);
}
