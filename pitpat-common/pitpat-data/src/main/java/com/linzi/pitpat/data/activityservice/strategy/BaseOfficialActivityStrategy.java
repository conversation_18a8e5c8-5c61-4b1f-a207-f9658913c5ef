package com.linzi.pitpat.data.activityservice.strategy;

import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityRuleUser;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityRuleUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.RunDetailDataDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.TreadmillBrandEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Slf4j
public abstract class BaseOfficialActivityStrategy extends BaseActivityStrategy {
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;
    @Resource
    protected UserMedalService userMedalService;
    @Resource
    protected RunActivityRuleUserService runActivityRuleUserService;

    @Autowired
    protected ActivityAwardConfigService activityAwardConfigService;
    @Autowired
    protected ExchangeRateConfigService exchangeRateConfigService;
    @Autowired
    protected UserLevelService userLevelService;


    public static List<Map> getRunningGoalsAward(Map<String, Object> jsonObject) {
        Object obj = jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD);
        if (Objects.isNull(obj)) {
            return new ArrayList<>();
        }
        try {
            return JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
        } catch (Exception e) {
            log.info("getRunningGoalsAward 异常，e", e);
            return new ArrayList<>();
        }
    }


    public static List<Map> getRunningGoalsScoreAward(Map<String, Object> jsonObject, String key) {
        Object obj = jsonObject.get(key);
        if (Objects.isNull(obj)) {
            return new ArrayList<>();
        }
        try {
            return JsonUtil.readList(jsonObject.get(key), Map.class);
        } catch (Exception e) {
            log.info("getRunningGoalsAward 异常，e", e);
            return new ArrayList<>();
        }
    }


    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        //活动结束后不可操作
        //里程碑不判断结束时间，只判断活动状态
        if (!Objects.equals(activityEntity.getActivityType(), RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                && Objects.nonNull(activityEntity.getActivityEndTime()) && ZonedDateTime.now().compareTo(activityEntity.getActivityEndTime()) > 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.ended")); //"The activity has ended and cannot be attended"
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {
//        String logNo = Logger.inheritableThreadLocalNo.get();
//        Long time = ch.qos.logback.classic.Logger.inheritableThreadLocalTime.get();
        log.info("handleUserActivityState official....................");
        log.info("handleUserActivityState official activity = " + JsonUtil.writeString(activityEntity));
        // 官方赛事  : -1表示取消，1表示接受，2表示拒绝
        if (1 == userStatus.intValue()) {
            //加锁
            String key = RedisConstants.ACTIVITY_PARTICIPATION_KEY + user.getId();
            //boolean lock = redisLock.lock(key, 30L,20,200L);
            RLock lock = redissonClient.getLock(key);
            try {
                if (LockHolder.tryLock(lock, 3, 30)) {
                    //校验活动国家跟用户国家是否相同
                    if (notContainsCountry(activityEntity, user)) {
                        return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.mismatch.region")); //ActivityError.COUNTRY_ERROR.getMsg()
                    }

                    // 接受官方赛事活动
                    Result result = canRefuseOrAcceptActivity(activityEntity, user);
                    if (Objects.nonNull(result)) {
                        return result;
                    }

                    // 支付保证金逻辑
                    log.info("开始执行支付流程handlePayRunActivity activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                    Result payResult = runActivityPayManager.handlePayRunActivity(activityEntity, user, password, request, checkVersion);
                    if (null != payResult) {
                        return payResult;
                    }

                    //判断是否包含积分，如果包含积分，则需要积分抵扣
                    useUserScore(activityEntity, user.getId(), false);

                    // 添加官方赛事活动用户
                    log.info("添加官方赛事活动用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                    activityUserBizService.addOfficialActivityUser(activityEntity, user.getId(), runningGoals, taskId, request.getSource(), null, false);
                    //修改官方赛事活动参与用户
                    log.info("修改官方赛事活动参与用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                    runActivityService.addOfficialActivityUserCount(activityEntity.getId());
                    if (user.getIsRobot() == 1) {
                        log.info("用户是机器人处理 activity = " + JsonUtil.writeString(activityEntity) + ",userId=" + user.getId());
                        activityEntity.setHasRobot(1);
                        activityEntity.setUserCount(null);
                        runActivityService.updateById(activityEntity);
                        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
                        executor.execute(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                                    //添加机器人策略
                                    if (immediatelyAdmission) {
                                        ZonedDateTime endTime = DateUtil.addHours(activityEntity.getActivityStartTime(), 5);
                                        String runMode = robotRunModeService.getRobotRunMode().getMode();
                                        ZnsRunRouteEntity znsRunRouteEntity = runRouteService.selectRunRouteById(activityEntity.getActivityRouteId());
                                        MindUserMatch mindUserMatch = mindUserMatchBizService.addRobotMindUserMatch(user.getId(), activityEntity.getId(), activityEntity.getRunMileage(), runMode, endTime);
                                        if (Objects.equals(znsRunRouteEntity.getRouteType(), 2)) {   // 表示3D路线
                                            Long roomId = activityEntity.getId();
                                            if (activityEntity.getActivityType() == 4) {
                                                roomId = NumberUtils.getGoalImNumber(activityEntity.getId(), runningGoals, activityEntity.getCompleteRuleType());
                                            }
                                            // 47.110.167.249:8001/add-robot?userid=机器人id&roomid=要加入的房间号&roadid=跑道id&firstname=&lastname=&avatar=头像url&gender=性别(1：男，2：女)
                                            GamePushUtils.addRobot(gamepush, roomId, activityEntity.getActivityRouteId(), user, activityEntity.getId(), mindUserMatch.getId());
                                        }
                                        // 在线或离线推送
                                        socketPushUtils.onlinePush(activityEntity.getId(), user, 1);
                                    }
                                } catch (Exception e) {
                                    log.error("官方赛事活动处理机器人异常", e);
                                } finally {
                                    OrderUtil.removeLogNo();
                                }
                            }
                        });
                    }
                } else {
                    return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("activity.join.failed"));
                }
            } catch (Exception e) {
                log.error("handleUserActivityState 异常，e", e);
                exceptionNotification("Event registration operation failed with exception");
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
            return CommonResult.success();
        }
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.join.failed"));
    }


    public Result checkHandleUserActivityState(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, Integer userStatus, ZnsUserEntity user) {
        Result result = super.checkHandleUserActivityState(activityEntity, activityUser, userStatus, user);
        if (Objects.nonNull(result)) {
            return result;
        }
        return canRefuseOrAcceptActivity(activityEntity, user);
    }

    /**
     * 判断官方赛事活动是否可以接受
     *
     * @param activityEntity
     * @param user
     * @return
     */
    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        // 只要活动出来了，并且活动没有介绍都可以报
        ZonedDateTime activityEndTime = activityEntity.getActivityEndTime();
        ZonedDateTime now = ZonedDateTime.now();
        //判断报名时间
        if (Objects.nonNull(activityEntity.getApplicationStartTime()) && ZonedDateTime.now().compareTo(activityEntity.getApplicationStartTime()) < 0) {
            log.info("判断官方赛事活动是否可以接受结果：不可以，报名时间未开始");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.timeNoStart")); // "Not within sign-up time"
        }
        if (Objects.nonNull(activityEntity.getApplicationEndTime()) && ZonedDateTime.now().compareTo(activityEntity.getApplicationEndTime()) > 0) {
            log.info("判断官方赛事活动是否可以接受结果：不可以，报名时间已结束");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.timeNoStart"));
        }


        // 查询用户是否已报名
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId()).userId(user.getId())
                .build();
        if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(activityEntity.getActivityType())) {
            userQuery.setUserStateIn(Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                    ActivityUserStateEnum.RUNING.getState(),
                    ActivityUserStateEnum.ENDED.getState()));
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findOne(userQuery);

        if (null == activityUser) {
            // 活动状态=未开始 and 被挑战者参与状态=未答复
            if (now.isAfter(activityEndTime)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.misCondition")); //"Does not meet the conditions for accepting official events"
            }
        } else {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.misCondition")); //"Does not meet the conditions for accepting official events"
        }

        //判断是否参与互斥活动
        if (StringUtils.hasText(activityEntity.getMutexActivityIds())) {
            List<Long> mutexActivityIds = NumberUtils.stringToLong(activityEntity.getMutexActivityIds().split(","));
            if (!CollectionUtils.isEmpty(mutexActivityIds)) {
                RunActivityUserQuery userQuery2 = RunActivityUserQuery.builder()
                        .isDelete(0).userId(user.getId()).activityIds(mutexActivityIds)
                        .build();

                ZnsRunActivityUserEntity mutexEntity = runActivityUserService.findOne(userQuery2);
                if (Objects.nonNull(mutexEntity)) {
                    ZnsRunActivityEntity mutexActivityEntity = runActivityService.findById(mutexEntity.getActivityId());
                    log.info("当前用户已参加其他互斥活动");
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.count.exceed", mutexActivityEntity.getActivityTitle()));// "You have signed up "+ mutexActivityEntity.getActivityTitle() +",cannot register this event for now"
                }
            }
        }
        log.info("canRefuseOrAcceptActivity ActivityObjectType = " + activityEntity.getActivityObjectType());
        //活动对象判断
        if (activityEntity.getActivityObjectType() == 0) {
            //判断对象是否新用户--指未参加过官方活动的用户
            int participationOfficialActivityCount = runActivityUserService.getParticipationOfficialActivityCount(user.getId());
            if (participationOfficialActivityCount > 0) {
                log.info("当前活动只能新用户参加");
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.newUser.limited")); //"This event is only for new users"
            }
        } else if (activityEntity.getActivityObjectType() == 1) {
            //判断用户是否拿到勋章
            UserMedal userMedal = userMedalService.selectUserMedalByConfigId(user.getId(), activityEntity.getDemandMedalConfigId());
            if (Objects.isNull(userMedal)) {
                MedalConfig medalConfig = medalConfigService.getById(activityEntity.getDemandMedalConfigId());
                log.info("当前活动用户必须拿到" + medalConfig.getName() + "勋章");
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.medalWinner.limited", medalConfig.getName())); //"The event is only for 「"+medalConfig.getName()+"」 medal winners to participate"
            }
        } else if (activityEntity.getActivityObjectType() == 2) {
            //判断用户是否达到等级
            //新版本重写用户等级经验
            Integer level = 0;
            if (sysConfigService.enableUserNewLevel(user.getId())) {
                UserLevel userLevel = userLevelService.findByUserId(user.getId());
                level = userLevel.getLevel();
            } else {
                level = user.getUserLevel();
            }
            if (level < activityEntity.getDemandUserLevel()) {
                log.info("当前活动用户等级必须达到" + activityEntity.getDemandUserLevel() + "级");
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.level.limited", activityEntity.getDemandUserLevel())); //"This event requires level Lv."+activityEntity.getDemandUserLevel()+" or higher to participate"
            }
        } else if (activityEntity.getActivityObjectType() == 3) {
            //只有特定用户可以参加
            RunActivityRuleUser ruleUser = runActivityRuleUserService.selectByActivityIdAndUserById(activityEntity.getId(), user.getId());
            if (Objects.isNull(ruleUser)) {
                String activityConfig = activityEntity.getActivityConfig();
                Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig);
                String activityObjectErrorDesc = MapUtil.getString(jsonObject.get("activityObjectErrorDesc")); //TODO i18n 校正
                if (StringUtil.isEmpty(activityObjectErrorDesc)) {
                    activityObjectErrorDesc = I18nMsgUtils.getMessage("activity.enroll.specialUser.commonError");// "The system is busy. Please try again later";
                }
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), activityObjectErrorDesc);
            }
        }
        //参赛名额限制
        log.info("activityEntity.getApplicationUserLimit()=" + activityEntity.getApplicationUserLimit());
        if (activityEntity.getApplicationUserLimit() >= 0) {
            //查询已参与人数
            RunActivityUserQuery userQuery1 = RunActivityUserQuery.builder()
                    .isDelete(0).activityId(activityEntity.getId())
                    .build();

            long count = runActivityUserService.findCount(userQuery1);
            if (count >= activityEntity.getApplicationUserLimit()) {
                log.info("当前活动参与人数已满");
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.count.full")); //"Cannot register, it's full"
            }
        }
        return null;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return CommonResult.success();
    }

    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        return null;
    }

    @Override
    public ActivityRunningReportBaseVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        return null;
    }

    /**
     * 退回保证金
     *
     * @param activityEntity
     * @param activityUser
     */
    protected void handleOfficialEarnestMoney(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser) {
        if (activityEntity.getBonusRuleType() != 2 || activityEntity.getActivityEntryFee().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        //修改货架币种切换处理
        List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
        if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
            log.info("币种切换不发放");
        }
        // 取消活动退款
        runActivityProcessManager.cancelActivityRefund(activityEntity, Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? AccountDetailTypeEnum.FEE : AccountDetailTypeEnum.SECURITY_FUND, activityUser.getUserId(), "Deposit return");
    }


    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    protected void handlerUserCertificate(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser) {
        try {
            ZnsUserRunDataDetailsEntity detailsServiceById = znsUserRunDataDetailsService.findById(activityUser.getRunDataDetailsId());
            RunDetailDataDto runDetailDataDto = new RunDetailDataDto();
            BeanUtils.copyProperties(detailsServiceById, runDetailDataDto);
            Long treadmillId = detailsServiceById.getTreadmillId();
            ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findById(treadmillId);
            // 品牌获取
            if (Objects.isNull(treadmillEntity)) {
                log.info("设备品牌获取失败 user_id:{}", activityUser.getUserId());
                return;
            }
            String brand = treadmillEntity.getBrand();
            Integer type = TreadmillBrandEnum.findByName(brand).getType();
            runDetailDataDto.setType(type);
            runDetailDataDto.setBrand(brand);
            log.info("runDetailDataDto:{}", runDetailDataDto);
            super.genUserCertificate(activityEntity, activityUser, runDetailDataDto);
        } catch (Exception e) {
            log.error("handlerUserCertificate e", e);
        }
    }


}
