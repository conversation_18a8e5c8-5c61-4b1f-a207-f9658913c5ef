package com.linzi.pitpat.data.activityservice.manager;


import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.LightCityPicsDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTypeAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserAward;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityUserAwardQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiActivityAwardListDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiAmountAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiCouponAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardMilestonePopDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQueryUser;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.request.UpdateActivityAwardConfigRequest;
import com.linzi.pitpat.data.activityservice.model.vo.FriendPKAwardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.model.vo.PKAward;
import com.linzi.pitpat.data.activityservice.model.vo.PKAwardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.SegmentAward;
import com.linzi.pitpat.data.activityservice.model.vo.SpecificPKAward;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityTypeAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserAwardService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.dto.AwardPoolConfigRankVo;
import com.linzi.pitpat.data.awardservice.model.dto.AwardPoolConfigVo;
import com.linzi.pitpat.data.awardservice.model.dto.VisualizationAwardConfigDetailsDto;
import com.linzi.pitpat.data.awardservice.model.dto.VisualizationAwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfig;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigCouponService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigProportionService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigWearService;
import com.linzi.pitpat.data.awardservice.service.AwardLimitRuleService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagLogService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategy;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategyFactory;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillMeasureEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsEquipmentProductionBatchEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.resp.UserGameRankAwardDto;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.PopRecord;
import com.linzi.pitpat.data.systemservice.model.query.PopRecordQuery;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 奖励相关服务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AwardActivityManager {
    private final AwardConfigService awardConfigService;
    private final AwardConfigAmountService awardConfigAmountService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final AwardConfigCouponService awardConfigCouponService;
    private final AwardConfigScoreService awardConfigScoreService;
    private final AwardConfigWearService awardConfigWearService;
    private final ZnsUserAccountService userAccountService;
    private final CouponService couponService;
    private final MainActivityService mainActivityService;
    private final WearsService wearsService;
    private final PopRecordService popRecordService;
    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final ZnsEquipmentProductionBatchService znsEquipmentProductionBatchService;
    private final ZnsTreadmillService znsTreadmillService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserCouponService userCouponService;
    private final UserWearsBagLogService userWearsBagLogService;
    private final UserWearsBagService userWearsBagService;
    private final ActivityUserAwardService activityUserAwardService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ActivityTypeAwardConfigService activityTypeAwardConfigService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    private final ZnsRunActivityService znsRunActivityService;
    private final AwardConfigProportionService awardConfigProportionService;
    private final AwardLimitRuleService awardLimitRuleService;

    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final ActivityAwardCurrencyBizService activityAwardCurrencyBizService;


    public List<ApiActivityAwardListDto> findUserAward(Long activityId, ZnsUserEntity user, Integer goal, Integer targetType) {
        List<ApiActivityAwardListDto> awardListDtos = new ArrayList<>();
        try {
            Currency currency = userAccountService.getCurrency(user.getId(), activityId, true);
            //查询获取的奖励
            List<ActivityUserAward> awardList = activityUserAwardService.findByActivityId(activityId, user.getId());
            if (CollectionUtils.isEmpty(awardList)) {
                return awardListDtos;
            }
            //查询所有奖励
            List<Long> userAccountDetailIdList = new ArrayList<>();
            List<Long> userScoreDetailIdList = new ArrayList<>();
            List<Long> userCouponDetailIdList = new ArrayList<>();
            List<Long> userWearLogDetailIdList = new ArrayList<>();

            for (ActivityUserAward userAward : awardList) {
                if (Objects.nonNull(userAward.getUserAccountDetailId()) && userAward.getUserAccountDetailId() > 0) {
                    userAccountDetailIdList.add(userAward.getUserAccountDetailId());
                }
                if (Objects.nonNull(userAward.getUserCouponId()) && userAward.getUserCouponId() > 0) {
                    userCouponDetailIdList.add(userAward.getUserCouponId());
                }
                if (Objects.nonNull(userAward.getUserScoreId()) && userAward.getUserScoreId() > 0) {
                    userScoreDetailIdList.add(userAward.getUserScoreId());
                }
                if (Objects.nonNull(userAward.getUserWearsBaglogId()) && userAward.getUserWearsBaglogId() > 0) {
                    userWearLogDetailIdList.add(userAward.getUserWearsBaglogId());
                }
            }

            Map<Long, ZnsUserAccountDetailEntity> userAccountDetailEntityMap = null;
            if (!CollectionUtils.isEmpty(userAccountDetailIdList)) {
                List<ZnsUserAccountDetailEntity> znsUserAccountDetailEntities = userAccountDetailService.listByIds(userAccountDetailIdList);
                userAccountDetailEntityMap = znsUserAccountDetailEntities.stream().collect(Collectors.toMap(ZnsUserAccountDetailEntity::getId, Function.identity(), (x, y) -> x));
            }
            Map<Long, ActivityUserScore> activityUserScoreMap = null;
            if (!CollectionUtils.isEmpty(userScoreDetailIdList)) {
                List<ActivityUserScore> activityUserScores = activityUserScoreService.listByIds(userScoreDetailIdList);
                activityUserScoreMap = activityUserScores.stream().collect(Collectors.toMap(ActivityUserScore::getId, Function.identity(), (x, y) -> x));
            }
            Map<Long, UserCoupon> userCouponMap = null;
            if (!CollectionUtils.isEmpty(userCouponDetailIdList)) {
                List<UserCoupon> userCoupons = userCouponService.findListByIds(userCouponDetailIdList);
                userCouponMap = userCoupons.stream().collect(Collectors.toMap(UserCoupon::getId, Function.identity(), (x, y) -> x));
            }
            Map<Long, UserWearsBagLog> wearsBagLogMap = null;
            if (!CollectionUtils.isEmpty(userWearLogDetailIdList)) {
                List<UserWearsBagLog> wearsBagLogList = userWearsBagLogService.listByIds(userWearLogDetailIdList);
                wearsBagLogMap = wearsBagLogList.stream().collect(Collectors.toMap(UserWearsBagLog::getId, Function.identity(), (x, y) -> x));
            }

            Map<Integer, List<ActivityUserAward>> listMap = awardList.stream().collect(Collectors.groupingBy(ActivityUserAward::getSendType));
            for (Map.Entry<Integer, List<ActivityUserAward>> awardDtoMap : listMap.entrySet()) {
                Integer type = awardDtoMap.getKey();
                ApiActivityAwardListDto dto = new ApiActivityAwardListDto();
                dto.setType(type);
                List<ApiActivityAwardDto> awards = new ArrayList<>();
                //按批次分组，同一批放到同一个实体中
                Map<String, List<ActivityUserAward>> batchNoListMap = awardDtoMap.getValue().stream().collect(Collectors.groupingBy(ActivityUserAward::getBatchNo));
                for (Map.Entry<String, List<ActivityUserAward>> listEntry : batchNoListMap.entrySet()) {
                    ApiActivityAwardDto awardDto = new ApiActivityAwardDto();
                    List<ActivityUserAward> userAwardList = listEntry.getValue();
                    for (ActivityUserAward userAward : userAwardList) {
                        if (Objects.isNull(awardDto.getRankMin())) {
                            awardDto.setRankMin(userAward.getRank());
                            awardDto.setRankMax(userAward.getRank());
                        }

                        if (AwardSentTypeEnum.TIME_AWARD.getType().equals(userAward.getSendType())) {
                            awardDto.setTargetType(2).setTarget(userAward.getTargetTime());
                        }
                        if (AwardSentTypeEnum.MILEAGE_AWARD.getType().equals(userAward.getSendType())) {
                            awardDto.setTargetType(1).setTarget(userAward.getTargetMileage());
                        }
                        if (AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType().equals(userAward.getSendType())) {
                            awardDto.setTargetType(3).setTarget(userAward.getTarget());
                        }

                        if (Objects.nonNull(userAward.getUserAccountDetailId()) && userAward.getUserAccountDetailId() > 0 && Objects.nonNull(userAccountDetailEntityMap)) {
                            ZnsUserAccountDetailEntity userAccountDetailEntity = userAccountDetailEntityMap.get(userAward.getUserAccountDetailId());
                            if (Objects.nonNull(userAccountDetailEntity)) {
                                awardDto.setAmountAwardDto(new ApiAmountAwardDto(currency, userAccountDetailEntity.getAmount()));
                            }
                        }
                        if (Objects.nonNull(userAward.getUserCouponId()) && userAward.getUserCouponId() > 0 && Objects.nonNull(userCouponMap)) {
                            UserCoupon userCoupon = userCouponMap.get(userAward.getUserCouponId());
                            if (Objects.nonNull(userCoupon)) {
                                Coupon coupon = couponService.selectCouponById(userCoupon.getCouponId());
                                ApiCouponAwardDto couponAwardDto = new ApiCouponAwardDto();
                                couponAwardDto.setCouponId(userCoupon.getCouponId());
                                couponAwardDto.setCouponName(CouponTypeEnum.findNameByType(coupon.getCouponType()) + "：" + coupon.getName());
                                couponAwardDto.setCouponTitle(coupon.getTitle());
                                couponAwardDto.setCouponType(coupon.getCouponType());
                                couponAwardDto.setAmount(userCoupon.getAmount());
                                couponAwardDto.setCurrency(currency);
                                awardDto.setCouponAwardDto(couponAwardDto);
                            }
                        }
                        if (Objects.nonNull(userAward.getUserScoreId()) && userAward.getUserScoreId() > 0 && Objects.nonNull(activityUserScoreMap)) {
                            ActivityUserScore activityUserScore = activityUserScoreMap.get(userAward.getUserScoreId());
                            if (Objects.nonNull(activityUserScore)) {
                                awardDto.setScore(activityUserScore.getScore());
                            }
                        }
                        if (Objects.nonNull(userAward.getUserWearsBaglogId()) && userAward.getUserWearsBaglogId() > 0 && Objects.nonNull(wearsBagLogMap)) {
                            UserWearsBagLog wearsBagLog = wearsBagLogMap.get(userAward.getUserWearsBaglogId());
                            if (Objects.nonNull(wearsBagLog)) {
                                UserWearsBag userWearsBag = userWearsBagService.findByByIdWithoutLogicDelete(wearsBagLog.getBagId());
                                if (Objects.nonNull(userWearsBag)) {
                                    ApiWearAwardDto apiWearAwardDto = new ApiWearAwardDto();
                                    apiWearAwardDto.setWearType(userWearsBag.getWearType());
                                    apiWearAwardDto.setWearValue(userWearsBag.getWearValue());
                                    apiWearAwardDto.setWearName(userWearsBag.getWearName());
                                    Wears wearByWearIdAndType = wearsService.getWearByWearIdAndType(userWearsBag.getWearType(), userWearsBag.getWearValue());
                                    if (Objects.nonNull(wearByWearIdAndType)) {
                                        apiWearAwardDto.setWearImageUrl(wearByWearIdAndType.getWearImageUrl());
                                    }
                                    awardDto.setWearAwardDto(apiWearAwardDto);
                                }
                            }
                        }
                    }
                    awards.add(awardDto);
                }
                dto.setAwards(awards);
                awardListDtos.add(dto);
            }
            //排序
            sortAward(awardListDtos);
            return awardListDtos;
        } catch (Exception e) {
            log.error("findUserAward error", e);
        }

        return new ArrayList<>();
    }

    private void sortAward(List<ApiActivityAwardListDto> awardListDtos) {
        if (CollectionUtils.isEmpty(awardListDtos)) {
            return;
        }
        for (ApiActivityAwardListDto awardListDto : awardListDtos) {
            if (AwardSentTypeEnum.TIME_AWARD.getType().equals(awardListDto.getType()) ||
                    AwardSentTypeEnum.MILEAGE_AWARD.getType().equals(awardListDto.getType())) {
                List<ApiActivityAwardDto> awards = awardListDto.getAwards();
                if (!CollectionUtils.isEmpty(awards)) {
                    awards.sort(Comparator.comparing(ApiActivityAwardDto::getTarget));
                }
            }
        }


    }

    //里程/时长奖励 目标填充
    private void setAwardTarget(List<ApiActivityAwardListDto> awardListDtos, Long activityId) {
        if (CollectionUtils.isEmpty(awardListDtos)) {
            return;
        }
        //是否有里程时长奖励
        ApiActivityAwardListDto activityAwardListDto = awardListDtos.stream().filter(a -> AwardSentTypeEnum.MILEAGE_AWARD.getType().equals(a.getType()) || AwardSentTypeEnum.TIME_AWARD.getType().equals(a.getType())).findFirst().orElse(null);
        if (Objects.isNull(activityAwardListDto)) {
            return;
        }
        List<AwardConfigDto> awardConfigDtos = activityAwardConfigService.selectAwardConfigDtoListBySendTypes(activityId, Arrays.asList(AwardSentTypeEnum.TIME_AWARD.getType(), AwardSentTypeEnum.MILEAGE_AWARD.getType()), null);
        List<Integer> runMileageList = awardConfigDtos.stream().filter(a -> AwardSentTypeEnum.MILEAGE_AWARD.getType().equals(a.getSendType())).map(AwardConfigDto::getTargetMileage).distinct().sorted().collect(Collectors.toList());
        List<Integer> runTimeList = awardConfigDtos.stream().filter(a -> AwardSentTypeEnum.TIME_AWARD.getType().equals(a.getSendType())).map(AwardConfigDto::getTargetTime).distinct().sorted().collect(Collectors.toList());

        for (ApiActivityAwardListDto awardListDto : awardListDtos) {
            if (AwardSentTypeEnum.MILEAGE_AWARD.getType().equals(awardListDto.getType())) {
                List<ApiActivityAwardDto> awards = awardListDto.getAwards();
                if (CollectionUtils.isEmpty(awards) || CollectionUtils.isEmpty(runMileageList)) {
                    continue;
                }
                for (int i = 0; i < awards.size(); i++) {
                    Integer target = runMileageList.get(i);
                    awards.get(i).setTarget(target).setTargetType(1);
                }
            }
            if (AwardSentTypeEnum.TIME_AWARD.getType().equals(awardListDto.getType())) {
                List<ApiActivityAwardDto> awards = awardListDto.getAwards();
                if (CollectionUtils.isEmpty(awards) || CollectionUtils.isEmpty(runMileageList)) {
                    continue;
                }
                for (int i = 0; i < awards.size(); i++) {
                    Integer target = runTimeList.get(i);
                    awards.get(i).setTarget(target).setTargetType(2);
                }
            }
        }
    }

    public void sendActivityAwardByConfig(AwardConfigDto awardConfigType, AwardSendDto dto) {
        if (Objects.isNull(awardConfigType)) {
            log.info("sendActivityAwardByConfig end,awardConfigType is null");
            return;
        }
        AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(awardConfigType.getAwardType());
        awardProcessStrategy.awardSendProcess(Arrays.asList(awardConfigType.getAwardConfigId()), dto, dto.getTotalBatchNo());
    }


    public AwardMilestonePopDto getMilestonePop(AwardQueryUser query) {
        Long activityId = query.getActivityId();
        Long userId = query.getUserId();
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType()) && Arrays.asList(1, 2).contains(query.getIsPolyList())) {
            //参考聚合代码，该情况为聚合不展示弹窗
            return null;
        }
        // 查询里程奖励
        ActivityUserAwardQuery activityUserAwardQuery = ActivityUserAwardQuery.builder()
                .userId(userId).activityId(activityId).sendType(AwardSentTypeEnum.MILEAGE_AWARD.getType()).build();
        List<ActivityUserAward> list = activityUserAwardService.findList(activityUserAwardQuery);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        LinkedHashMap<Integer, ActivityUserAward> map = list.stream().collect(Collectors.toMap(ActivityUserAward::getTargetMileage, Function.identity(), (u1, u2) -> u1, LinkedHashMap::new));
        list = map.values().stream().collect(Collectors.toCollection(LinkedList::new));
        // 查询里程奖励图片
        ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, LocaleContextHolder.getLocale().toString());
        if (Objects.isNull(activityDisseminate)) {
            return null;
        }
        List<LightCityPicsDto> lightCityPicsDtoList = JsonUtil.readList(activityDisseminate.getLightCityPics(), LightCityPicsDto.class);
        if (CollectionUtils.isEmpty(lightCityPicsDtoList)) {
            return null;
        }
        ActivityUserAward activityUserAward = list.get(0);// 获取最远里程奖励
        PopRecordQuery popRecordQuery = PopRecordQuery.builder()
                .userId(userId).activityId(activityId).target(activityUserAward.getTargetMileage()).pos(4).type(6).build();
        PopRecord popRecord = popRecordService.findByQuery(popRecordQuery);
        LightCityPicsDto lightCityPicsDto = lightCityPicsDtoList.get(list.size() - 1);// 获取里程奖励对应的城市配置
        if (Objects.nonNull(lightCityPicsDto) && StringUtils.hasText(lightCityPicsDto.getPic())
                && (Objects.isNull(popRecord) || activityUserAward.getGmtCreate().isAfter(popRecord.getGmtCreate()))) {
            AwardMilestonePopDto dto = new AwardMilestonePopDto();
            String description = fillPopDescription(userId, list, activityUserAward);
            dto.setDescription(description);
            dto.setPic(lightCityPicsDto.getPic());
            // 插入弹窗记录
            PopRecord newPopRecord = new PopRecord();
            newPopRecord.setActivityId(activityId);
            newPopRecord.setUserId(userId);
            newPopRecord.setTarget(activityUserAward.getTargetMileage());
            newPopRecord.setType(6);
            newPopRecord.setPos(4);
            popRecordService.insertPopRecord(newPopRecord);
            return dto;
        }
        return null;
    }

    /**
     * 填充弹窗描述
     *
     * @param userId
     * @param list
     * @param activityUserAward
     * @return
     */
    private String fillPopDescription(Long userId, List<ActivityUserAward> list, ActivityUserAward activityUserAward) {
        ZnsUserEquipmentEntity userLastConnectedEquipment = znsUserEquipmentService.getUserLastConnectedEquipment(userId);
        ZnsTreadmillEntity treadmill = znsTreadmillService.findById(userLastConnectedEquipment.getEquipmentId());
        ZnsEquipmentProductionBatchEntity equipmentProductionBatch = znsEquipmentProductionBatchService.getByBatchNumber(treadmill.getBatchNumber());
        String description;
        if (TreadmillMeasureEnum.IMPERIAL_UNITS.getCode().equals(equipmentProductionBatch.getMeasuringSystem())) {
            BigDecimal targetMile = BigDecimal.valueOf(activityUserAward.getTargetMileage()).divide(BigDecimal.valueOf(1600), 2, RoundingMode.HALF_UP);
            description = String.format(I18nMsgUtils.getMessage("activity.award.milestone.pop.miles.msg"), targetMile, list.size());
        } else {
            BigDecimal targetMile = BigDecimal.valueOf(activityUserAward.getTargetMileage()).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
            description = String.format(I18nMsgUtils.getMessage("activity.award.milestone.pop.km.msg"), targetMile, list.size());
        }
        return description;
    }


    /**
     * 获得好友pk奖励配置
     *
     * @return
     */
    public FriendPKAwardConfig getFriendPKAwardConfig() {
        ZnsRunActivityConfigEntity config = runActivityConfigService.selectByActivityType(2, 2);

        String activityConfig = config.getActivityConfig();
        Map<String, String> map = JsonUtil.readValue(activityConfig);

        FriendPKAwardConfig friendPKAwardConfig = JsonUtil.readValue(map.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode()), FriendPKAwardConfig.class);
        fillCouponName(friendPKAwardConfig);

        return friendPKAwardConfig;

    }

    private void fillCouponName(FriendPKAwardConfig friendPKAwardConfig) {

        if (Objects.isNull(friendPKAwardConfig)) {
            return;
        }

        List<SpecificPKAward> specificPKAwards = new ArrayList<>();
        PKAwardConfig timePKAwardConfig = friendPKAwardConfig.getTimePKAwardConfig();
        PKAwardConfig mileagePKAwardConfig = friendPKAwardConfig.getMileagePKAwardConfig();


        //时长奖励
        if (Objects.nonNull(timePKAwardConfig)) {
            SegmentAward segmentAward = timePKAwardConfig.getSegmentAward();
            if (Objects.nonNull(segmentAward)) {
                specificPKAwards.add(segmentAward.getAward());
            } else if (Objects.nonNull(timePKAwardConfig.getUnifiedAward())) {
                specificPKAwards.add(timePKAwardConfig.getUnifiedAward());
            }
        }

        //里程奖励
        if (Objects.nonNull(mileagePKAwardConfig)) {
            SegmentAward segmentAward = mileagePKAwardConfig.getSegmentAward();
            if (Objects.nonNull(segmentAward)) {
                specificPKAwards.add(segmentAward.getAward());
            } else if (Objects.nonNull(mileagePKAwardConfig.getUnifiedAward())) {
                specificPKAwards.add(mileagePKAwardConfig.getUnifiedAward());
            }
        }


        for (SpecificPKAward specificPKAward : specificPKAwards) {
            PKAward winnerAward = specificPKAward.getWinnerAward();
            if (Objects.nonNull(winnerAward)) {
                Long couponId = winnerAward.getCouponId();
                Coupon coupon = couponService.selectCouponById(couponId);
                if (Objects.nonNull(coupon)) {
                    winnerAward.setCouponName(CouponTypeEnum.findNameByType(coupon.getCouponType()) + "：" + coupon.getName());
                }
            }

            PKAward participationAward = specificPKAward.getParticipationAward();
            if (Objects.nonNull(participationAward)) {
                Long couponId = participationAward.getCouponId();
                Coupon coupon = couponService.selectCouponById(couponId);
                if (Objects.nonNull(coupon)) {
                    participationAward.setCouponName(CouponTypeEnum.findNameByType(coupon.getCouponType()) + "：" + coupon.getName());
                }
            }
        }

    }


    /**
     * 组队跑奖励发送
     */
    public void handleTeamRunAward3D(BigDecimal awardAmount, Long userId, Long refId, AccountDetailTypeEnum accountDetailTypeEnum,
                                     Integer subType, Long activityId) {
        if (null == awardAmount || awardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("组队跑奖励金额为零");
            return;
        }
        Currency userCurrency = userAccountService.getUserCurrency(userId);
        ExchangeRateConfigEntity exchangeRateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(userCurrency.getCurrencyCode());
        if (Objects.nonNull(exchangeRateConfigEntity)) {
            awardAmount = awardAmount.multiply(exchangeRateConfigEntity.getExchangeRate()).setScale(2, RoundingMode.HALF_UP);
        }
        // 处理货币汇率问题
        // 给用户余额发送奖励
        userAccountService.increaseAmount(awardAmount, userId, true, true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail3D(userId, accountDetailTypeEnum, subType, 1, awardAmount, billNo, tradeTime, refId, activityId);
    }


    /**
     * 奖励复制
     *
     * @param activityConfigId
     * @param runningGoalsUnit
     * @param runMileage
     * @param runTime
     * @param activityId
     * @param jsonObject
     */
    public void copyAwardConfig(Long activityConfigId, Integer runningGoalsUnit, BigDecimal runMileage, BigDecimal runTime, Long activityId, Map<String, Object> jsonObject, Integer appVersion) {
        ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.findRunActivityConfig(activityConfigId);
        List<Integer> newPersonPkType = RunActivitySubTypeEnum.getNewPersonPkType();
        List<AwardConfigDto> activityTypeAwardConfigs = activityTypeAwardConfigService.selectAwardConfigList(runActivityConfig.getActivityType(), runActivityConfig.getSubType(), AwardSentTypeEnum.copyTypes(), runningGoalsUnit, runMileage, runTime);
        if (Objects.equals(runActivityConfig.getActivityType(), RunActivityTypeEnum.NEW_USER_PK_MANY.getType())
                || newPersonPkType.contains(runActivityConfig.getSubType())
        ) {
            addAwardConfig(activityId, runActivityConfig);
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(activityTypeAwardConfigs)) {
            return;
        }
        jsonObject.put("participateAward", BigDecimal.ZERO);
        jsonObject.put("winnerAward", BigDecimal.ZERO);
        jsonObject.put("completeAward", BigDecimal.ZERO);

        for (AwardConfigDto activityTypeAwardConfig : activityTypeAwardConfigs) {
            addCopyAwardConfig(activityTypeAwardConfig, activityId, jsonObject, appVersion);
        }
    }

    //奖励填充到zns_award_config_amount_currency
    private void addAwardConfig(Long activityId, ZnsRunActivityConfigEntity runActivityConfig) {
        NewPkMultipleConfigVo newPkMultipleConfigVo = JsonUtil.readValue(runActivityConfig.getActivityConfig(), NewPkMultipleConfigVo.class);
        //新增awardConfig
        UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
        UserGameAwardDto unFinishAward = newPkMultipleConfigVo.getUnFinishAward();
        List<UserGameRankAwardDto> userRankAward = newPkMultipleConfigVo.getUserRankAward();
        fillCurrency(activityId, finishAward, AwardSentTypeEnum.COMPLETING_THE_GAME.getType());
        fillCurrency(activityId, unFinishAward, AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
        if (!CollectionUtils.isEmpty(userRankAward)) {
            for (UserGameRankAwardDto userGameRankAwardDto : userRankAward) {
                UserGameAwardDto awardDto = userGameRankAwardDto.getAwardDto();
                fillCurrency(activityId, awardDto, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType());
            }
        }
    }

    private void fillCurrency(Long activityId, UserGameAwardDto userGameAwardDto, Integer integer) {
        if (Objects.isNull(userGameAwardDto)
                || CollectionUtils.isEmpty(userGameAwardDto.getAmountList())
                || Objects.isNull(userGameAwardDto.getAmountList().get(0).getAmount())
                || userGameAwardDto.getAmountList().get(0).getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        AwardConfig awardConfig = new AwardConfig();
        awardConfig.setAwardType(AwardTypeEnum.AMOUNT.getType());
        awardConfig.setSendType(integer);
        awardConfigService.insertAwardConfig(awardConfig);
        ActivityAwardConfig activityAwardConfig = new ActivityAwardConfig();
        activityAwardConfig.setActivityId(activityId);
        activityAwardConfig.setAwardId(awardConfig.getId());
        activityAwardConfigService.insert(activityAwardConfig);
        awardConfigAmountService.addAwardConfig(awardConfig.getId(), userGameAwardDto.getAmountList().get(0).getAmount(), false);
        AwardConfigAmount byAwardConfigId = awardConfigAmountService.findByAwardConfigId(awardConfig.getId());
        List<AwardConfigAmountCurrency> awardConfigAmountCurrencyList = userGameAwardDto.getAmountList().stream().map(s -> {
            AwardConfigAmountCurrency awardConfigAmountCurrency = new AwardConfigAmountCurrency();
            String currencyCode = s.getCurrencyCode();
            BigDecimal currencyAmount = I18nConstant.currencyFormat(currencyCode, s.getAmount());
            awardConfigAmountCurrency.setAmount(currencyAmount);
            awardConfigAmountCurrency.setCurrencyCode(currencyCode);
            awardConfigAmountCurrency.setAwardAmountId(byAwardConfigId.getId());
            return awardConfigAmountCurrency;
        }).toList();
        awardConfigAmountCurrencyDataService.saveBatch(awardConfigAmountCurrencyList);
    }

    private void addCopyAwardConfig(AwardConfigDto dto, Long activityId, Map<String, Object> jsonObject, Integer appVersion) {
        //新增awardConfig
        if (AwardTypeEnum.AMOUNT.getType().equals(dto.getAwardType())
                && !AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType().equals(dto.getSendType())
                && (Objects.isNull(dto.getAmount()) || dto.getAmount().compareTo(BigDecimal.ZERO) <= 0)) {
            return;
        }
        AwardConfig awardConfig = new AwardConfig();
        awardConfig.setAwardType(dto.getAwardType());
        awardConfig.setSendType(dto.getSendType());
        awardConfigService.insertAwardConfig(awardConfig);
        //新增zns_activity_award_config
        ActivityAwardConfig activityAwardConfig = new ActivityAwardConfig();
        activityAwardConfig.setAwardId(awardConfig.getId());
        activityAwardConfig.setActivityId(activityId);
        if (dto.getMilepostType() == 1 || dto.getMilepostType() == 2) {
            activityAwardConfig.setTargetMileage(dto.getMilepost());
        } else {
            activityAwardConfig.setTargetTime(dto.getMilepost());
        }
        activityAwardConfig.setRank(dto.getRank());
        activityAwardConfigService.insert(activityAwardConfig);
        if (AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType().equals(dto.getSendType())) {
            dto.setAwardType(5);
        }
        //新增具体配置
        switch (dto.getAwardType()) {
            case 1:
                awardConfigAmountService.addAwardConfig(awardConfig.getId(), dto.getAmount(), false);
                ZnsRunActivityEntity activity = znsRunActivityService.findById(activityId);
                if (RunActivityTypeEnum.noOfficialTypes().contains(activity.getActivityType())) {
                    activityAwardCurrencyBizService.saveActivityAwardAmountCurrency(dto, activity, awardConfig);
                }
                break;
            case 2:
                awardConfigCouponService.addAwardConfig(awardConfig.getId(), dto.getCouponIds());
                break;
            case 3:
                awardConfigScoreService.addAwardConfig(awardConfig.getId(), dto.getScore());
                break;
            case 4:
                awardConfigWearService.addAwardConfig(awardConfig.getId(), dto.getWearType(), dto.getWearValue(), dto.getWearName(), dto.getWearImageUrl(), dto.getExpiredTime());
                break;
            case 5:
                awardConfigProportionService.addAwardConfig(awardConfig.getId(), dto.getProportion());
                break;
            default:
                break;
        }
        oldActivityConfigDeal(jsonObject, dto);
    }

    /**
     * 老的活动配置处理
     *
     * @param jsonObject
     * @param awardConfigDto
     */
    private void oldActivityConfigDeal(Map<String, Object> jsonObject, AwardConfigDto awardConfigDto) {
        if (Objects.isNull(awardConfigDto)) {
            return;
        }
        //只处理金额
        if (!Objects.equals(awardConfigDto.getAwardType(), AwardTypeEnum.AMOUNT.getType())) {
            return;
        }
        if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.PARTICIPATION_AWARD.getType())) {
            jsonObject.put("participateAward", awardConfigDto.getAmount());
        } else if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.WINNER_AWARD.getType())) {
            jsonObject.put("winnerAward", awardConfigDto.getAmount());
        } else if (awardConfigDto.getSendType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
            jsonObject.put("completeAward", awardConfigDto.getAmount());
        }
    }


    public void updateActivityAwardConfig(UpdateActivityAwardConfigRequest request) {
        //删除原数据
        activityTypeAwardConfigService.deleteActivityTypeByType(request.getActivityType(), request.getSubType());
        if (!CollectionUtils.isEmpty(request.getVisualizationAwardConfigDtoList())) {
            //重新保存
            for (VisualizationAwardConfigDto dto : request.getVisualizationAwardConfigDtoList()) {
                if (Objects.equals(request.getActivityType(), 2)) {
                    addActivityTypeAwardConfig(dto, dto.getWinnerAward(), request.getActivityType(), request.getSubType(), AwardSentTypeEnum.WINNER_AWARD.getType());
                    addActivityTypeAwardConfig(dto, dto.getParticipateAward(), request.getActivityType(), request.getSubType(), AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
                } else if (Objects.equals(request.getActivityType(), 1)) {
                    addActivityTypeAwardConfig(dto, dto.getCompleteAward(), request.getActivityType(), request.getSubType(), AwardSentTypeEnum.COMPLETING_THE_GAME.getType());
                    addActivityTypeAwardConfig(dto, dto.getLaunchAward(), request.getActivityType(), request.getSubType(), AwardSentTypeEnum.LAUNCH_AWARD.getType());
                    addActivityTypeAwardConfig(dto, dto.getParticipateAward(), request.getActivityType(), request.getSubType(), AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
                }
            }
        }

        //池配置 删除原数据
        awardLimitRuleService.deleteActivityTypeByType(request.getActivityType(), request.getSubType(), 1);
        if (!CollectionUtils.isEmpty(request.getAwardPoolConfigVos())) {
            for (AwardPoolConfigVo awardPoolConfigVo : request.getAwardPoolConfigVos()) {
                addAwardConfigAmount(awardPoolConfigVo.getAwardType(), AwardSentTypeEnum.FOUNDATION_OF_BONUS_POOL.getType(), request.getActivityType(), request.getSubType(), awardPoolConfigVo.getAwardPoolBase(), awardPoolConfigVo.getAwardPoolBaseList(), null, null);
                for (AwardPoolConfigRankVo awardPoolConfigRankVo : awardPoolConfigVo.getRankAward()) {
                    addAwardConfigProportion(awardPoolConfigVo.getAwardType(), AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType(), request.getActivityType(), request.getSubType(), awardPoolConfigRankVo.getAwardProportion(), awardPoolConfigRankVo.getRank());
                }
                //新增限制
                awardLimitRuleService.addNewLimit(request.getActivityType(), request.getSubType(), 1, awardPoolConfigVo.getAwardType(), awardPoolConfigVo.getRankAwardLimit());
            }
        }
    }

    /**
     * 添加奖励金额
     *
     * @param awardType
     * @param sendType
     * @param activityType
     * @param subType
     * @param amount
     * @param milepost
     * @param milepostType
     */
    private void addAwardConfigAmount(Integer awardType, Integer sendType, Integer activityType, Integer subType, BigDecimal amount, List<CurrencyAmount> amountList, Integer milepost, Integer milepostType) {
        if (CollectionUtils.isEmpty(amountList)
                || Objects.isNull(amountList.get(0).getAmount())) {
            return;
        }
        //新增awardConfig
        AwardConfig awardConfig = new AwardConfig();
        awardConfig.setAwardType(awardType);
        awardConfig.setSendType(sendType);
        awardConfigService.insertAwardConfig(awardConfig);
        //新增zns_activity_type_award_config
        ActivityTypeAwardConfig activityTypeAwardConfig = new ActivityTypeAwardConfig();
        activityTypeAwardConfig.setAwardId(awardConfig.getId());
        activityTypeAwardConfig.setActivityType(activityType);
        activityTypeAwardConfig.setActivitySubType(subType);
        activityTypeAwardConfig.setMilepost(milepost);
        activityTypeAwardConfig.setMilepostType(milepostType);
        activityTypeAwardConfigService.insertActivityTypeAwardConfig(activityTypeAwardConfig);
        if (AwardTypeEnum.SCORE.getType().equals(awardType)) {
            awardConfigScoreService.addAwardConfig(awardConfig.getId(), amount.intValue());
        } else {
            awardConfigAmountService.addAwardConfig(awardConfig.getId(), org.apache.commons.collections4.CollectionUtils.isEmpty(amountList) ? BigDecimal.ZERO : amountList.get(0).getAmount(), true);
            AwardConfigAmount awardConfigId = awardConfigAmountService.findByAwardConfigId(awardConfig.getId());
            if (!CollectionUtils.isEmpty(amountList) && Objects.nonNull(awardConfigId)) {
                List<AwardConfigAmountCurrency> awardConfigAmountCurrencyList = amountList.stream().map(s -> {
                    AwardConfigAmountCurrency awardConfigAmountCurrency = new AwardConfigAmountCurrency();
                    BigDecimal amountCurrency = s.getAmount();
                    String currencyCode = s.getCurrencyCode();
                    amountCurrency = I18nConstant.currencyFormat(currencyCode, amountCurrency);
                    awardConfigAmountCurrency.setAmount(amountCurrency);
                    awardConfigAmountCurrency.setCurrencyCode(currencyCode);
                    awardConfigAmountCurrency.setAwardAmountId(awardConfigId.getId());
                    return awardConfigAmountCurrency;
                }).collect(Collectors.toList());
                awardConfigAmountCurrencyDataService.saveBatch(awardConfigAmountCurrencyList);
            }
        }
    }

    private void addActivityTypeAwardConfig(VisualizationAwardConfigDto dto, VisualizationAwardConfigDetailsDto awardConfigDto, Integer activityType, Integer subType, Integer sendType) {
        if (Objects.isNull(awardConfigDto)) {
            return;
        }
        awardConfigDto.setSendType(sendType);
        Integer milepost = null;
        if (Objects.nonNull(dto.getMilepost())) {
            milepost = SportsDataUnit.smallUnits(dto.getMilepost(), dto.getMilepostType());
        }
        //新增具体配置
        switch (dto.getAwardType()) {
            case 1:
                addAwardConfigAmount(dto.getAwardType(), awardConfigDto.getSendType(), activityType, subType, awardConfigDto.getAmount(), awardConfigDto.getAmountConfigDtoList(), milepost, dto.getMilepostType());
                break;
            case 2:
                addAwardConfigAmountCoupon(dto.getAwardType(), awardConfigDto.getSendType(), activityType, subType, awardConfigDto.getCouponId(), milepost, dto.getMilepostType());
                break;
            case 3:
                addAwardConfigAmountScore(dto.getAwardType(), awardConfigDto.getSendType(), activityType, subType, awardConfigDto.getScore(), milepost, dto.getMilepostType());
                break;
            default:
                break;
        }
    }

    private void addAwardConfigAmountCoupon(Integer awardType, Integer sendType, Integer activityType, Integer subType, Long couponId, Integer milepost, Integer milepostType) {
        if (Objects.isNull(couponId)) {
            return;
        }
        //新增awardConfig
        AwardConfig awardConfig = new AwardConfig();
        awardConfig.setAwardType(awardType);
        awardConfig.setSendType(sendType);
        awardConfigService.insertAwardConfig(awardConfig);
        //新增zns_activity_type_award_config
        ActivityTypeAwardConfig activityTypeAwardConfig = new ActivityTypeAwardConfig();
        activityTypeAwardConfig.setAwardId(awardConfig.getId());
        activityTypeAwardConfig.setActivityType(activityType);
        activityTypeAwardConfig.setActivitySubType(subType);
        activityTypeAwardConfig.setMilepost(milepost);
        activityTypeAwardConfig.setMilepostType(milepostType);
        activityTypeAwardConfigService.insertActivityTypeAwardConfig(activityTypeAwardConfig);
        awardConfigCouponService.addAwardConfig(awardConfig.getId(), couponId.toString());
    }

    private void addAwardConfigAmountScore(Integer awardType, Integer sendType, Integer activityType, Integer subType, Integer score, Integer milepost, Integer milepostType) {
        if (Objects.isNull(score)) {
            return;
        }
        //新增awardConfig
        AwardConfig awardConfig = new AwardConfig();
        awardConfig.setAwardType(awardType);
        awardConfig.setSendType(sendType);
        awardConfigService.insertAwardConfig(awardConfig);
        //新增zns_activity_type_award_config
        ActivityTypeAwardConfig activityTypeAwardConfig = new ActivityTypeAwardConfig();
        activityTypeAwardConfig.setAwardId(awardConfig.getId());
        activityTypeAwardConfig.setActivityType(activityType);
        activityTypeAwardConfig.setActivitySubType(subType);
        activityTypeAwardConfig.setMilepost(milepost);
        activityTypeAwardConfig.setMilepostType(milepostType);
        activityTypeAwardConfigService.insertActivityTypeAwardConfig(activityTypeAwardConfig);
        awardConfigScoreService.addAwardConfig(awardConfig.getId(), score);
    }

    /**
     * 添加奖励比例
     *
     * @param awardType
     * @param sendType
     * @param activityType
     * @param subType
     * @param awardProportion
     * @param rank
     */
    private void addAwardConfigProportion(Integer awardType, Integer sendType, Integer activityType, Integer subType, BigDecimal awardProportion, Integer rank) {
        //新增awardConfig
        AwardConfig awardConfig = new AwardConfig();
        awardConfig.setAwardType(awardType);
        awardConfig.setSendType(sendType);
        awardConfigService.insertAwardConfig(awardConfig);
        //新增zns_activity_type_award_config
        ActivityTypeAwardConfig activityTypeAwardConfig = new ActivityTypeAwardConfig();
        activityTypeAwardConfig.setAwardId(awardConfig.getId());
        activityTypeAwardConfig.setActivityType(activityType);
        activityTypeAwardConfig.setActivitySubType(subType);
        activityTypeAwardConfig.setRank(rank);
        activityTypeAwardConfigService.insertActivityTypeAwardConfig(activityTypeAwardConfig);

        awardConfigProportionService.addAwardConfig(awardConfig.getId(), awardProportion);
    }

    /**
     * 奖励金额返回
     *
     * @param awardConfigDtoList
     * @param sendType
     * @param award
     * @param userEntity
     * @return
     */
    public BigDecimal selectAwardAmount(List<AwardConfigDto> awardConfigDtoList, Integer sendType, BigDecimal award, ZnsUserEntity userEntity) {
        if (Objects.isNull(award)) {
            award = BigDecimal.ZERO;
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(awardConfigDtoList)) {
            return award;
        }
        AwardConfigDto awardConfigDto = awardConfigDtoList.stream().filter(a -> a.getAwardType().equals(AwardTypeEnum.AMOUNT.getType()) && a.getSendType().equals(sendType)).findFirst().orElse(null);
        if (Objects.isNull(awardConfigDto)) {
            return award;
        }
        if (Objects.isNull(awardConfigDto.getAmount())) {
            return award;
        }
        AwardConfigAmountCurrency awardConfigAmountCurrency = activityAwardCurrencyBizService.getAwardConfigAmountCurrency(userEntity.getId(), awardConfigDto);
        return awardConfigAmountCurrency.getAmount();
    }

}
