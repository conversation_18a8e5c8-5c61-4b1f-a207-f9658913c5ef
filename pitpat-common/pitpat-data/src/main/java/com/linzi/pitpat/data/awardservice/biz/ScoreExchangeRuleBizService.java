package com.linzi.pitpat.data.awardservice.biz;

import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleStatusDto;
import com.linzi.pitpat.data.activityservice.model.dto.InsertExchangeScoreRuleDto;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.dto.consloe.ScoreRuleI18nRequestDto;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleList;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleI18nService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleListService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.mallservice.manager.console.GoodsConsoleManager;
import com.linzi.pitpat.data.mallservice.model.entity.ScoreMallGoodsRelationDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuQuery;
import com.linzi.pitpat.data.mallservice.model.query.ScoreMallGoodsRelationQuery;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 积分商城兑换规则业务处理类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScoreExchangeRuleBizService {
    @Resource
    private ExchangeScoreRuleService exchangeScoreRuleService;
    @Resource
    private ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    @Resource
    private ExchangeScoreRuleI18nService exchangeScoreRuleI18nService;

    @Resource
    private UserLevelRuleService userLevelRuleService;
    @Resource
    private GoodsConsoleManager goodsConsoleManager;
    @Resource
    private ScoreMallGoodsRelationService scoreMallGoodsRelationService;
    @Resource
    private ZnsGoodsService znsGoodsService;
    @Resource
    private ZnsGoodsSkuService znsGoodsSkuService;
    @Autowired
    private ExchangeScoreRuleListService exchangeScoreRuleListService;

    @Resource
    private ExchangeScoreAwardService scoreAwardService;

    @Transactional(rollbackFor = Exception.class)
    public void exchangeScoreRuleInsertOrUpdate(InsertExchangeScoreRuleDto exchangeScoreRuleDto,String username) {
        Long ruleId = exchangeScoreRuleDto.getExchangeScoreRule().getId();

        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleDto.getExchangeScoreRule();
        if (Objects.isNull(exchangeScoreRule.getExchangeAmount())
                || exchangeScoreRule.getExchangeAmount().compareTo(new BigDecimal(0)) == 0) {
            exchangeScoreRule.setExchangeAmount(new BigDecimal(0).setScale(2));
        }
        if (StringUtil.isEmpty(exchangeScoreRule.getTagImage())) {
            exchangeScoreRule.setTagImage(null);
        }

        List<ScoreRuleI18nRequestDto> i18nInfo = exchangeScoreRuleDto.getI18nInfo();
        ScoreRuleI18nRequestDto defaultI18n = i18nInfo.stream().filter(item -> item.getLangCode().equals(exchangeScoreRule.getDefaultLangCode())).findFirst().orElse(null);
        if (Objects.nonNull(defaultI18n)) {
            exchangeScoreRule.setActivityName(defaultI18n.getActivityName());
        }
        exchangeScoreRule.setIsNewGoods(YesNoStatus.YES.getCode());

        exchangeScoreRuleService.saveOrUpdate(exchangeScoreRule);
        // 保存国际化数据
        Long newRuleId = exchangeScoreRule.getId();
        exchangeScoreRuleI18nService.deleteByRuleId(newRuleId);
        exchangeScoreRuleListService.deleteExchangeScoreRuleByRuleId(newRuleId);
        if (!CollectionUtils.isEmpty(i18nInfo)) {
            List<ExchangeScoreRuleI18n> exchangeScoreRuleI18ns = BeanUtil.copyBeanList(i18nInfo, ExchangeScoreRuleI18n.class);
            exchangeScoreRuleI18ns = exchangeScoreRuleI18ns.stream().filter(item -> StringUtils.hasText(item.getActivityName())
                            && StringUtils.hasText(item.getExchangeRule())
                            && StringUtils.hasText(item.getAdvertiseImage()))
                    .collect(Collectors.toList());
            exchangeScoreRuleI18ns.stream().forEach(item -> item.setRuleId(newRuleId));
            exchangeScoreRuleI18nService.insertBatch(exchangeScoreRuleI18ns);
            for (ScoreRuleI18nRequestDto i18n : i18nInfo) {
                // 详情介绍图
                List<ExchangeScoreRuleList> detailDescImgList = i18n.getDetailDescImgList();
                if (!CollectionUtils.isEmpty(detailDescImgList)) {
                    detailDescImgList.stream().forEach(item -> {
                        item.setExchangeScoreRuleId(newRuleId);
                        item.setLangCode(i18n.getLangCode());
                        exchangeScoreRuleListService.insertExchangeScoreRuleList(item);
                    });
                }
            }
        }
        // 插入积分兑换规则对应的物品表
        scoreAwardService.insertOrUpdateAward(exchangeScoreRuleDto);
        //实物,商品表插入或更新
        if (Objects.equals(exchangeScoreRuleDto.getExchangeScoreRule().getExchangeType(), ExchangeScoreTypeEnum.PHYSICAL_REWARDS.getCode())) {
            if (Objects.isNull(ruleId)) {
                goodsConsoleManager.saveScoreGood(exchangeScoreRuleDto, newRuleId, exchangeScoreRuleDto.getExchangeScoreRule().getSkuNo(),username);
            } else {
                goodsConsoleManager.updateScoreGoods(exchangeScoreRuleDto, newRuleId, exchangeScoreRuleDto.getExchangeScoreRule().getSkuNo(),username);
            }
        }

        //保存多币种
        exchangeScoreRuleCurrencyService.saveOrModifyScoreRuleCurrency(username, exchangeScoreRule.getId(),
                exchangeScoreRuleDto.getExchangeScoreRule().getCurrencyAmountList(),
                exchangeScoreRuleDto.getExchangeScoreRule().getCurrencyOriginalExchangeAmountList());
    }

    public void changeStatus(ExchangeScoreRuleStatusDto exchangeScoreRuleStatusDto,String username) {
        exchangeScoreRuleService.updateExchangeScoreRuleStatusById(ZonedDateTime.now(), exchangeScoreRuleStatusDto.getStatus(), exchangeScoreRuleStatusDto.getId());
        //实物商品更新，erp商品状态
        List<ScoreMallGoodsRelationDo> list = scoreMallGoodsRelationService.findList(new ScoreMallGoodsRelationQuery().setRuleId(exchangeScoreRuleStatusDto.getId()));
        if (!CollectionUtils.isEmpty(list)) {
            Integer status = exchangeScoreRuleStatusDto.getStatus();
            if (Objects.equals(exchangeScoreRuleStatusDto.getStatus(), 0)) {
                status = -1;
            }
            for (ScoreMallGoodsRelationDo scoreMallGoodsRelationDo : list) {
                ZnsGoodsEntity goodsEntity = znsGoodsService.findById(scoreMallGoodsRelationDo.getGoodsId());
                if (Objects.isNull(goodsEntity)) continue;
                znsGoodsService.changeStatus(scoreMallGoodsRelationDo.getGoodsId(), status, username);
                znsGoodsSkuService.changeStatus(scoreMallGoodsRelationDo.getGoodsId(), status, username);
                List<ZnsGoodsSkuEntity> skuEntityList = znsGoodsSkuService.findList(new GoodsSkuQuery().setGoodsId(goodsEntity.getId()));
                for (ZnsGoodsSkuEntity znsGoodsSkuEntity : skuEntityList) {
                    znsGoodsSkuEntity.setStatus(status);
                    goodsConsoleManager.syncErpSku(znsGoodsSkuEntity, goodsEntity,YesNoStatus.YES.getCode());
                }
            }
        }
    }
}
