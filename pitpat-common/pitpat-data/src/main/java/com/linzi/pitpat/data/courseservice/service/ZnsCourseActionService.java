package com.linzi.pitpat.data.courseservice.service;

import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseActionEntity;
import com.linzi.pitpat.data.courseservice.model.query.CourseActionQuery;
import com.linzi.pitpat.data.courseservice.model.request.CourseIdRequest;
import com.linzi.pitpat.data.courseservice.model.vo.CourseDetailVoPo;
import com.linzi.pitpat.data.courseservice.model.vo.CourseStageList;

import java.util.List;

/**
 * 课程动作表
 *
 * <AUTHOR>
 * @date 2021-10-08 16:19:22
 */
public interface ZnsCourseActionService {


    /**
     * 编辑课程动作
     *
     * @param po
     */
    void editAction(CourseDetailVoPo po, String username);

    List<CourseStageList> getStageList(Long courseId, Integer courseType, CourseIdRequest courseIdRequest, String languageCode, String defaultLangCode);

    List<ZnsCourseActionEntity> selectI18nActionByCourseId(Long courseId, String languageCode, String defaultLangCode);

    /**
     * 根据速度 课程动作明细筛选课程id
     *
     * @param max
     * @param min
     * @param exerciseType
     * @return
     */
    List<Long> filteringCourseIdsByVelocity(Integer max, Integer min, Integer exerciseType);

    List<ZnsCourseActionEntity> findList(CourseActionQuery build);
}

