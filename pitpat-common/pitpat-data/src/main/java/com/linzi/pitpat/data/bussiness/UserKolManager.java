package com.linzi.pitpat.data.bussiness;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.pro.cache.event.KolUserChangeEvent;
import com.linzi.pitpat.data.clubservice.constant.enums.UserKolClubAuthorizationEnum;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.service.ClubImService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.enums.UserKolStateEnum;
import com.linzi.pitpat.data.mapstruct.UserKolConvert;
import com.linzi.pitpat.data.userservice.dto.request.UserKolAddReqDto;
import com.linzi.pitpat.data.userservice.dto.request.UserKolListReqDto;
import com.linzi.pitpat.data.userservice.dto.request.UserKolToggleClubAuthStateReqDto;
import com.linzi.pitpat.data.userservice.dto.request.UserKolUpdateReqDto;
import com.linzi.pitpat.data.userservice.dto.response.UserKolListRespDto;
import com.linzi.pitpat.data.userservice.model.entity.UserKol;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserKolQuery;
import com.linzi.pitpat.data.userservice.service.UserBlacklistService;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.UserStatusFreezeRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.page.PageConvert;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.Result;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@AllArgsConstructor
public class UserKolManager {

    private final ZnsUserService userService;
    private final UserKolService userKolService;
    private final ClubService clubService;
    private final ClubImService clubImService;
    private final ApplicationEventPublisher applicationEventPublisher;

    private final UserBlacklistService userBlacklistService;
    private final UserStatusFreezeRecordService userFreezeRecordService;

    public Result<Boolean> addKol(UserKolAddReqDto req) {
        ZnsUserEntity userByEmail = userService.findByEmail(req.getEmailAddress());
        if (Objects.isNull(userByEmail)) {
            return CommonResult.fail("无此账号，请重试。");
        }
        Long userId = userByEmail.getId();
        UserKol kol = userKolService.findByUserIdSigned(userId);
        if (Objects.nonNull(kol)) {
            return CommonResult.fail("该用户已是KOL。");
        }

        if (userFreezeRecordService.isFreeze(userId)
                || userBlacklistService.isInBlackList(userId)) {
            return CommonResult.fail("账号异常，请确认账号信息和状态。");
        }

        UserKol userKol = new UserKol();
        userKol.setUserId(userId);
        userKol.setSignedDate(ZonedDateTime.now());
        userKol.setClubAuthorization(UserKolClubAuthorizationEnum.NO.getCode());
        userKol.setState(UserKolStateEnum.SIGNED.getCode());
        userKol.setCreator(req.getCreator());
        userKol.setType(req.getType());
        userKolService.insert(userKol);
        clubImService.sendImMessageGetClubPermission(userId);
        applicationEventPublisher.publishEvent(new KolUserChangeEvent(userId));
        return CommonResult.success(true);
    }

    /**
     * KOL解约
     *
     * @param kolId
     * @param modifyUserName
     * @return
     */
    public Result<Boolean> terminateKol(Long kolId, String modifyUserName) {
        UserKol byId = userKolService.findById(kolId);
        if (Objects.isNull(byId)) {
            return CommonResult.fail("无此KOL，请重试。");
        }
        if (clubService.findByOwnerId(byId.getUserId()).isPresent()) {
            return CommonResult.fail("请先解散俱乐部后再解除KOL权限。");
        }
        Boolean data = userKolService.terminateKol(kolId, modifyUserName);

        applicationEventPublisher.publishEvent(new KolUserChangeEvent(byId.getUserId()));
        return CommonResult.success(data);
    }

    /**
     * 分页查询 KOL
     *
     * @param req
     * @return
     */
    public Page<UserKolListRespDto> queryPage(UserKolListReqDto req) {
        UserKolQuery query = UserKolConvert.INSTANCE.listReqToQuery(req);
        if (StringUtils.hasText(req.getEmailAddress())) {
            ZnsUserEntity userByEmail = userService.findByEmail(req.getEmailAddress());
            if (userByEmail == null) {
                return new Page<>();
            }
            query.setUserId(userByEmail.getId());
        }
        Page<UserKol> page = userKolService.page(query);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<Long> userIds = page.getRecords().stream().map(UserKol::getUserId).toList();
            Map<Long, ZnsUserEntity> userMaps = userService.getMapByIds(userIds);
            Map<Long, Club> clubMap = clubService.findByOwnerIds(userIds);
            return PageConvert.dataConvert(page, userKol -> {
                UserKolListRespDto userKolListRespDto = UserKolConvert.INSTANCE.poToListResp(userKol);
                ZnsUserEntity znsUserEntity = userMaps.get(userKol.getUserId());
                if (znsUserEntity != null) {
                    userKolListRespDto.setKolUserNickname(znsUserEntity.getFirstName() + " " + znsUserEntity.getLastName());
                    userKolListRespDto.setKolUserEmailAddress(znsUserEntity.getEmailAddressEn());
                }
                userKolListRespDto.setHaveClub(clubMap.containsKey(userKol.getUserId()));
                return userKolListRespDto;
            });
        }
        return new Page<>();
    }

    public Boolean updateClubAuthState(UserKolToggleClubAuthStateReqDto req) {
        if (!req.isAuthState()) {
            UserKol userKol = userKolService.findById(req.getKolId());
            if (userKol == null) {
                throw new BaseException("kol不存在");
            }
            if (clubService.findByOwnerId(userKol.getUserId()).isPresent()) {
                throw new BaseException("请先解散俱乐部后再解除KOL权限。");
            }
        }
        return userKolService.updateClubAuthState(req);
    }

    /**
     * 编辑kol用户
     * @since 4.8.0
     */
    public void updateKol(UserKolUpdateReqDto req) {
        UserKol userKol = userKolService.findById(req.getKolId());
        if (userKol == null) {
            throw new BaseException("kol不存在");
        }
        userKol.setType(req.getType());
        userKol.setModifier(req.getModifier());
        userKolService.update(userKol);
        applicationEventPublisher.publishEvent(new KolUserChangeEvent(userKol.getUserId()));
    }
}
