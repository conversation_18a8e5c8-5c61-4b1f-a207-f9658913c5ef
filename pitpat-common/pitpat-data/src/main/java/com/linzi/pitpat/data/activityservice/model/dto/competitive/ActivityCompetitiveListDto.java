package com.linzi.pitpat.data.activityservice.model.dto.competitive;

import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsCategoryDetailDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import lombok.Data;

import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Data
public class ActivityCompetitiveListDto {

    //活动ID
    private Long activityId;

    private MainActivity mainActivity;

    //赛事类型 single单赛事 series-main系列赛主赛事 series-sub系列赛阶段赛事old老赛事 single-polymerization-running
    private String mainType;
    /**
     * 设备主类型：0 跑步机，1 单车 2 脚踏 3 划船机，
     *
     * @tag 4.5.0
     * @see DeviceConstant.EquipmentMainTypeEnum
     */
    private Integer equipmentMainType;
    //赛事封面
    private String activityCoverImage;

    //赛事标题
    private String activityTitle;
    /**
     * 竞技赛图标
     */
    private String competitiveIconUrl;

    private ZonedDateTime gmtCreate;
    /**
     * 活动状态： -1 未上架 0未开始 1已开始 2已结束 3下架
     */
    private Integer activityState;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;
    //报名开始时间
    private ZonedDateTime applicationStartTime;
    //报名结束时间
    private ZonedDateTime applicationEndTime;
    //	string	跳转路径/路由
    private String url;
    //n	string	跳转参数，json字符串
    private String jumpParam;
    //是否是vip赛事
    private Integer isMemberActivity;
    private Integer awardSendFinish;
    /**
     * 集锦id
     */
    private Long highlightsId;
    /**
     * 竞技赛类型
     *
     * @see ActivityCompetitiveSeasonType
     */
    private String competitiveSeasonType;

    /**
     * @see com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonSeasonSubType
     * competitiveSeasonType 季度子类型
     * SEASONAL
     * ANNUAL 年度写年份
     */
    private String competitiveSeasonSubType;

    /**
     * 竞技赛年份
     */
    private Integer competitiveSeasonYear;

    /**
     * icon url
     */
    private String competitiveSeasonIconUrl;

    // 竞技赛倒计时配置
    private CompetitiveCountDownDto competitiveCountDownDto;

    /**
     * 0 不是 1 是
     */
    private Integer isNew = 0;
    /**
     * 用户状态 0未报名 1已报名
     */
    private Integer userState = 0;

    /**
     * 集锦是否生效 0不生效 1生效
     */
    private Integer highlightState = 0;
    /**
     * 集锦封面视频
     */
    private String highlightsCoverDetail;
    /**
     * 集锦视频
     */
    private List<ActivityHighlightsCategoryDetailDo> categoryDetails;
    /**
     * 按钮
     */
    private List<CoverButtonEnum> button;
    /**
     * 前x位用户名
     */
    private List<String> usernames;

    //是否外卡用户 0不是 1是
    private Integer isForeign = 0;

    //达到竞技分要求 0 达到了  1 未达到
    private Integer notReachCompetitionScoreThreshold;


    public static Comparator<ActivityCompetitiveListDto> getShowInMoreComparator(Integer appVersion) {
        if (appVersion >= 4044) {
            return Comparator
                    .comparing(ActivityCompetitiveListDto::getActivityStartTime, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing((ActivityCompetitiveListDto item) -> ActivityCompetitiveListDto.getStatusIndex(item.getActivityState(), new int[]{2, 1, 4, 0, 3}), Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(ActivityCompetitiveListDto::getGmtCreate, Comparator.nullsLast(Comparator.naturalOrder()));
        }

        return Comparator.comparing((ActivityCompetitiveListDto item) -> ActivityCompetitiveListDto.getStatusIndex(item.getActivityState(), new int[]{1, 4, 0, 2, 3}), Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ActivityCompetitiveListDto::getActivityStartTime, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ActivityCompetitiveListDto::getMainType, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ActivityCompetitiveListDto::getActivityId, Comparator.nullsLast(Comparator.naturalOrder()));
    }


    public static Comparator<ActivityCompetitiveListDto> getSeasonComparator(Integer appVersion) {
        if (appVersion >= 4044) {
            return Comparator
                    .comparing(ActivityCompetitiveListDto::getActivityStartTime, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing((ActivityCompetitiveListDto item) -> ActivityCompetitiveListDto.getStatusIndex(item.getActivityState(), new int[]{2, 1, 4, 0, 3}), Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(ActivityCompetitiveListDto::getGmtCreate, Comparator.nullsLast(Comparator.naturalOrder()));
        }
        return Comparator
                .comparing(ActivityCompetitiveListDto::getActivityStartTime, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing((ActivityCompetitiveListDto item) -> ActivityCompetitiveListDto.getStatusIndex(item.getActivityState(), new int[]{2, 1, 4, 0, 3}), Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ActivityCompetitiveListDto::getMainType, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ActivityCompetitiveListDto::getActivityId, Comparator.nullsLast(Comparator.naturalOrder()));
    }

    public static int getStatusIndex(int activityState, int[] statusIndex) {
        for (int i = 0; i < statusIndex.length; i++) {
            if (statusIndex[i] == activityState) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 是否是用户待报名的活动
     *
     * @return
     */
    public boolean isUnRegisterActivity() {
        //活动状态 进行中/未开始
        return Optional.of(this)
                .filter((item -> ActivityStateEnum.IN_PROGRESS.getState().equals(item.getActivityState())
                        || ActivityStateEnum.NOT_START.getState().equals(item.getActivityState())
                        || Integer.valueOf(4).equals(item.getActivityState()))
                )
                //报名时间为截止
                .filter(item -> item.getApplicationEndTime().compareTo(ZonedDateTime.now()) > 0)
                //用户未报名
                .filter(item -> item.getUserState().equals(0)).stream().findAny().isPresent();
    }
}
