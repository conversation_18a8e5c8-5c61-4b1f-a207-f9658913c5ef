package com.linzi.pitpat.data.activityservice.dto.console.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class ActivityTimeRangeQuery {

    private List<Integer> activityStatus;

    private ZonedDateTime activityStartTimeMin;

    private ZonedDateTime activityEndTimeMax;

    private List<String> activityMainType;

}
