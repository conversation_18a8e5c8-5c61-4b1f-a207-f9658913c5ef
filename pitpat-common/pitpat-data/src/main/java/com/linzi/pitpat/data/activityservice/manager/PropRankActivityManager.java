package com.linzi.pitpat.data.activityservice.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.biz.PropRankedActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RankActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.RankedLevelEnums;
import com.linzi.pitpat.data.activityservice.constant.enums.ResultDataStateEnum;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankDataDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.EnableActStatusRequest;
import com.linzi.pitpat.data.activityservice.mapper.PropUserRankedMatchLineMapper;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.PropRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevelLog;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedMatchEntity;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedMatchLineEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.vo.PropMatchRankedLevelVo;
import com.linzi.pitpat.data.activityservice.query.MindUserMatchQuery;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityPageQuery;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.PropRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelLogService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedMatchLineService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedMatchService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.biz.UserWearsBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.PropRankedConstant;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserWearsEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserWearsService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.entity.dto.AutomaticAdmissionDealNewActivityDto;
import com.linzi.pitpat.data.entity.dto.DelayDto;
import com.linzi.pitpat.data.entity.vo.SocketRoomUserVo;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.robotservice.biz.RobotBizService;
import com.linzi.pitpat.data.robotservice.enums.RobotRunModeEnum;
import com.linzi.pitpat.data.robotservice.model.domain.RobotQuery;
import com.linzi.pitpat.data.robotservice.service.RobotQueueConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 段位赛处理类
 *
 * <AUTHOR>
 * @date 2024/6/13 13:37
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PropRankActivityManager {
    private final PropUserRankedLevelService propUserRankedLevelService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ZnsUserService userService;
    private final RabbitTemplate rabbitTemplate;
    private final RobotBizService robotBizService;

    private final PropUserRankedMatchService propUserRankedMatchService;
    private final SocketPushUtils socketPushUtils;
    private final MainActivityService mainActivityService;
    private final PropUserRankedMatchLineMapper propUserRankedMatchLineMapper;
    private final MindUserMatchService mindUserMatchService;
    private final ISysConfigService sysConfigService;
    private final PropRunRankedActivityUserService propRunRankedActivityUserService;
    private final PropRankedLevelService propRankedLevelService;
    private final ZnsUserWearsService userWearsService;
    private final PropUserRankedMatchLineService propUserRankedMatchLineService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final PropUserRankedLevelLogService propUserRankedLevelLogService;
    private final MainActivityBizService mainActivityBizService;
    private final UserWearsBizService userWearsBizService;
    private final MindUserMatchBizService mindUserMatchBizService;
    private final PropRankedActivityBizService propRankedActivityBizService;
    private final RobotQueueConfigService rotQueueConfigService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final RedissonClient redissonClient;

    private static final Random random = new Random();

    @Value("${admin.server.gamepush}")
    private String gamepush;
    //优惠券初始化 消息队列
    @Value("${" + RabbitQueueConstants.PROP_USER_RANKED_LEVEL_INIT_QUEUE + "}")
    private String userRankedLevelInitQueue;
    /**
     * 投放机器人延迟队列
     */
    @Value("${" + RabbitQueueConstants.PROP_RANK_MATCH_PUT_IN_ROBOT_DELAY_EXCHANGE_V2 + "}")
    private String rankMatchPutInRobotDelayExchange;
    /**
     * 机器人跑步数据上传延迟队列
     */
    @Value("${zns.config.rabbitQueue.delay_exchange_name}")
    private String delayExchangeName;

    private static final HashMap<Integer, BigDecimal> SEGMENT_SCORE_MAP = new HashMap<>();

    static {
        SEGMENT_SCORE_MAP.put(1, BigDecimal.valueOf(160));
        SEGMENT_SCORE_MAP.put(2, BigDecimal.valueOf(190));
        SEGMENT_SCORE_MAP.put(3, BigDecimal.valueOf(208));
    }


    /**
     * 用户段位进行初始化
     *
     * @param startUserId
     */
    public void initRankedData(Long startUserId) {
        log.info("Init user ranked level, startUserId={}", startUserId);
        if (startUserId == null) {
            return;
        }
        //用户段位进行初始化
        UserQuery query = UserQuery.builder().startUserId(startUserId).lastStr("limit 100").build();
        List<ZnsUserEntity> list = userService.findList(query);
        log.info("list={}", list.size());
        if (CollectionUtils.isEmpty(list) || list.get(0) == null) {
            log.info("Init user ranked level, startUserId={},用户段位进行初始化完成", startUserId);
            return;
        }
        list.forEach(this::initRootRankLevel);
        Long nextUserId = list.get(list.size() - 1).getId();
        rabbitTemplate.convertAndSend(userRankedLevelInitQueue, nextUserId);
    }


    /**
     * 初始化机器人用户段位信息
     *
     * @param user
     * @return
     */
    public PropUserRankedLevel initRootRankLevel(ZnsUserEntity user) {
        BigDecimal hideScore = propRankedActivityBizService.calcUserHideScore(user.getId());
        try {
            PropUserRankedLevel oldRankedLevel = propUserRankedLevelService.findByUserId(user.getId());
            if (oldRankedLevel != null) {
                //已有段位，更新隐藏分
                PropUserRankedLevel updateEntity = new PropUserRankedLevel();
                updateEntity.setId(oldRankedLevel.getId());
                updateEntity.setScore(hideScore);
                propUserRankedLevelService.update(updateEntity);
            } else {
                //没有段位新增数据
                PropUserRankedLevel userRankedLevel = buildUserRankedLevel(user, hideScore);
                propUserRankedLevelService.insert(userRankedLevel);
            }
        } catch (Exception e) {
            log.error("初始化用户段位报错，msg={}", e.getMessage(), e);
        }
        return propUserRankedLevelService.findByUserId(user.getId());
    }


    /**
     * 处理道具赛投放机器人
     *
     * @param rankedMatchId
     */
    public void dealPutInRobot(Long rankedMatchId) {
        //校验并获取道具赛需要投放的机器人
        Integer robotNum = checkAndGetCreateRobotNum(rankedMatchId); //需要投放的机器人
        if (robotNum == null) {
            //活动异常或道具赛已取消
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},活动异常或已取消或机器人不在入场时间", rankedMatchId);
            return;
        }
        PropUserRankedMatchEntity userRankedMatchEntity = propUserRankedMatchService.findById(rankedMatchId);
        if (robotNum > 0) {
            //道具赛预投放机器人（先加入）
            putInRobotPre(robotNum, userRankedMatchEntity);
        }

        //投放机器人（距离机器人结束时间小于3秒则全部投放）
        int leaveSecond = DateUtil.betweenSecond(ZonedDateTime.now(), userRankedMatchEntity.getWaitRobotEndTime()); //距离机器人投放结束时间
        log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},距离机器人结束时间={} 秒", rankedMatchId, leaveSecond);
        int putInNum = leaveSecond <= 3 ? -1 : 1; //投放机器人数量,-1：全部投放
        putInRobot(putInNum, userRankedMatchEntity);

        //计算下次投放机器人时间
        if (putInNum == -1) {
            //机器人已全部投放无需再次触发
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},机器人已全部投放无需再次触发", rankedMatchId);
            return;
        }

        //发布延迟消息投放下一批机器人(延迟时间 = 剩余时间 / (机器人数+1))
        List<PropUserRankedMatchLineEntity> matchLineEntities = propUserRankedMatchLineService.selectByMatchId(rankedMatchId);
        long leaveRobotNum = matchLineEntities.stream().filter(item -> PropRankedConstant.LineStatusEnum.LINE_STATUS_1.code.equals(item.getMatchStatus())).count(); //剩余待上线机器人
        if (leaveRobotNum == 0) {
            //没有机器人可以投放
            return;
        }
        int second = DateUtil.betweenSecond(ZonedDateTime.now(), userRankedMatchEntity.getWaitRobotEndTime());
        int leaveMillisecond = second * 1000; //剩余毫秒数
        BigDecimal delayTime = new BigDecimal(leaveMillisecond).divide(new BigDecimal(leaveRobotNum + 1), 0, RoundingMode.UP);
        log.info("道具赛延迟机器人投放延迟时间{}", delayTime);
        rabbitTemplate.convertAndSend(rankMatchPutInRobotDelayExchange, RabbitQueueConstants.PROP_RANK_MATCH_PUT_IN_ROBOT_DELAY_KEY, rankedMatchId, message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay(delayTime.intValue());// 毫秒为单位，指定此消息的延时时长
            return message;
        });
    }

    /**
     * 校验并获取道具赛需要投放的机器人
     *
     * @param rankedMatchId 道具赛id
     * @return
     */
    private Integer checkAndGetCreateRobotNum(Long rankedMatchId) {
        PropUserRankedMatchEntity userRankedMatchEntity = propUserRankedMatchService.findById(rankedMatchId);
        if (userRankedMatchEntity == null) {
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},道具赛不存在", rankedMatchId);
            return null;
        }
        if (PropRankedConstant.MatchStateEnum.MATCH_STATE_0.code.equals(userRankedMatchEntity.getState())) {
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},道具赛已关闭", rankedMatchId);
            return null;
        }
        if (ZonedDateTime.now().isAfter(userRankedMatchEntity.getWaitRobotEndTime())) {
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},当前时间已经超过机器人入场时间", rankedMatchId);
            return null;
        }
        MainActivity mainActivity = mainActivityService.findById(userRankedMatchEntity.getActivityId());
        if (mainActivity == null) {
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},活动不存在", rankedMatchId);
            return null;
        }
        List<Integer> stateList = List.of(MainActivityStateEnum.NOT_STARTED.getCode(), MainActivityStateEnum.STARTED.getCode());
        if (!stateList.contains(mainActivity.getActivityState())) {
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},活动状态不正确", rankedMatchId);
            return null;
        }

        //查询socket更新道具赛明细状态
        try {
            // -------------- 临时代码 start -------------------
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},由于预发用户切换房间没有创建房间，导致游戏报错，只能先放弃真人数量判断，后期看看能不能优化", rankedMatchId);
            //查询待进入+已进入的道具赛明细
            List<Integer> availableStats = List.of(PropRankedConstant.LineStatusEnum.LINE_STATUS_1.code, PropRankedConstant.LineStatusEnum.LINE_STATUS_2.code);
            LambdaQueryWrapper<PropUserRankedMatchLineEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(PropUserRankedMatchLineEntity::getMatchStatus, availableStats);
            queryWrapper.eq(PropUserRankedMatchLineEntity::getUserRankedMatchId, rankedMatchId);
            List<PropUserRankedMatchLineEntity> matchOnLineEntities = propUserRankedMatchLineMapper.selectList(queryWrapper);
            //真实用户数量
            long count = org.springframework.util.CollectionUtils.isEmpty(matchOnLineEntities) ? 0 : matchOnLineEntities.stream().filter(item -> UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode().equals(item.getIsRobot())).count();
            if (count == 0) {
                //没有真实用户在线，取消道具赛
                log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},房间内没有真实用户取消道具赛", rankedMatchId);
                cancelRankMatch(userRankedMatchEntity, "房间内没有真实用户");
                return null;
            }
            return userRankedMatchEntity.getUserNum() - matchOnLineEntities.size();
            // -------------- 临时代码 end -------------------


        } catch (Exception e) {
            log.error("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:" + rankedMatchId + ",查询socket更新道具赛明细状态异常", e);
        }

        //查询待进入+已进入的道具赛明细
        List<Integer> availableStats = List.of(PropRankedConstant.LineStatusEnum.LINE_STATUS_1.code, PropRankedConstant.LineStatusEnum.LINE_STATUS_2.code);
        LambdaQueryWrapper<PropUserRankedMatchLineEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PropUserRankedMatchLineEntity::getMatchStatus, availableStats);
        queryWrapper.eq(PropUserRankedMatchLineEntity::getUserRankedMatchId, rankedMatchId);
        List<PropUserRankedMatchLineEntity> matchOnLineEntities = propUserRankedMatchLineMapper.selectList(queryWrapper);
        if (org.springframework.util.CollectionUtils.isEmpty(matchOnLineEntities)) {
            //房间内没有用户取消道具赛
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},房间内没有用户取消道具赛", rankedMatchId);
            cancelRankMatch(userRankedMatchEntity, "房间内没有用户");
            return null;
        }

        //真实用户数量
        long count = matchOnLineEntities.stream().filter(item -> UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode().equals(item.getIsRobot())).count();
        if (count == 0) {
            //没有真实用户在线，取消道具赛
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},房间内没有真实用户取消道具赛", rankedMatchId);
            cancelRankMatch(userRankedMatchEntity, "房间内没有真实用户");
            return null;
        }

        //计算需要添加的机器人数量（赛事人数 - 已进人数）
        Integer robotNum = userRankedMatchEntity.getUserNum() - matchOnLineEntities.size();
        log.info("UserRankedMatchServiceImpl#dealPutInRobot----校验并获取需要投放的机器人,道具赛:{},计算需要添加的机器人数量结束，数量=" + robotNum, rankedMatchId);
        return robotNum;
    }

    /**
     * 取消道具赛
     *
     * @param userRankedMatchEntity
     */
    private void cancelRankMatch(PropUserRankedMatchEntity userRankedMatchEntity, String cancelReason) {
        List<PropUserRankedMatchLineEntity> lineEntities = propUserRankedMatchLineService.selectByMatchId(userRankedMatchEntity.getId());
        if (org.springframework.util.CollectionUtils.isEmpty(lineEntities)) {
            return;
        }

        //更新用户匹配记录为已取消
        List<Long> mindUserMatchIds = lineEntities.stream().map(PropUserRankedMatchLineEntity::getMindUserMatchId).toList();
        MindUserMatchQuery query = MindUserMatchQuery.builder().ids(mindUserMatchIds).build();
        List<MindUserMatch> mindUserMatches = mindUserMatchService.findListByQuery(query);

        if (org.springframework.util.CollectionUtils.isEmpty(mindUserMatches)) {
            return;
        }
        mindUserMatches.forEach(item -> {
            item.setStatus(-1);
            updateMindUserMatchById(item);
        });

        //取消活动
        EnableActStatusRequest request = new EnableActStatusRequest();
        request.setStatus(1);
        request.setActivityIds(List.of(userRankedMatchEntity.getActivityId()));
        mainActivityBizService.changeStatus(request);
        //关闭道具赛
        propUserRankedMatchService.closeRankMatch(userRankedMatchEntity.getId(), cancelReason);
    }

    public int updateMindUserMatchById(MindUserMatch mindUserMatch) {
        // status 0 , 1 表示机器人被使用， -1 ， 2 表示机器人恢复使用
        int status = Arrays.asList(0, 1).contains(mindUserMatch.getStatus()) ? 1 : 2;
        // 2 , 1
        ZnsUserEntity znsUserEntity = userService.findById(mindUserMatch.getUserId());
        if (znsUserEntity != null && !Objects.equals(znsUserEntity.getRobotCurrStatus(), status)) {     //如果状态不相等，则更新，如果相等，则不更新
            boolean flag = true;
            if (Objects.equals(status, 2)) {    //如果将状态为2，则看当前有没有在跑的数据，如果有，则不更新用户当前跑步状态
                MindUserMatch mindUserMatchNew = mindUserMatchService.selectMindUserMatchByIdStatusList(mindUserMatch.getId(), znsUserEntity.getId(), Arrays.asList(0, 1));
                if (mindUserMatchNew != null) {
                    log.info(" 正在跑步的mindUserMatchNewId = " + mindUserMatchNew.getId() + "，当前mindmatchId = " + mindUserMatch.getId() + ",userId=" + mindUserMatch.getUserId());
                    flag = false;
                }
            }
            if (flag) {
                userService.updateZnsUserRobotStatus(status, mindUserMatch.getUserId());
            }
        }
        return mindUserMatchBizService.updateMindUserMatchById(mindUserMatch);
    }


    /**
     * 投放机器人
     *
     * @param putInNum              投放数量 ，-1：全部投放
     * @param userRankedMatchEntity 道具赛
     */
    private void putInRobot(Integer putInNum, PropUserRankedMatchEntity userRankedMatchEntity) {
        log.info("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:{},开始处理", userRankedMatchEntity.getId());
        //获取需要投放的机器人
        LambdaQueryWrapper<PropUserRankedMatchLineEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PropUserRankedMatchLineEntity::getUserRankedMatchId, userRankedMatchEntity.getId());
        queryWrapper.eq(PropUserRankedMatchLineEntity::getIsRobot, UserConstant.RoboTypeEnum.IS_ROBOT_1.getCode()); // 机器人
        queryWrapper.eq(PropUserRankedMatchLineEntity::getMatchStatus, PropRankedConstant.LineStatusEnum.LINE_STATUS_1.code); //待进入
        if (putInNum != -1) {
            queryWrapper.last("limit " + putInNum);
        }
        List<PropUserRankedMatchLineEntity> matchLineEntities = propUserRankedMatchLineMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(matchLineEntities)) {
            return;
        }

        //房间号
        Long activityId = userRankedMatchEntity.getActivityId();
        Integer completeRuleType = userRankedMatchEntity.getTargetRunMileage() > 0 ? ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode() : ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_2.getCode();
        Long roomId = NumberUtils.getGoalImNumber(activityId, userRankedMatchEntity.getTargetRunMileage(), completeRuleType);

        //更新明细状态为已入场
        for (PropUserRankedMatchLineEntity matchLineEntity : matchLineEntities) {
            matchLineEntity.setMatchStatus(PropRankedConstant.LineStatusEnum.LINE_STATUS_2.code);
            try {
                propUserRankedMatchLineMapper.updateById(matchLineEntity);
            } catch (Exception e) {
                log.error("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:" + userRankedMatchEntity.getId() + ",robotUserId:" + matchLineEntity.getUserId() + ",更新道具明细状态为已入场异常", e);
            }
        }

        //上线机器人
        int delaySecond = DateUtil.betweenSecond(ZonedDateTime.now(), userRankedMatchEntity.getActivityStartTime());
        for (PropUserRankedMatchLineEntity matchLineEntity : matchLineEntities) {
            try {
                //给机器人设置服装
                ZnsUserEntity userEntity = userService.findById(matchLineEntity.getUserId());
                if (Objects.equals(userEntity.getIsRobot(), YesNoStatus.YES.getCode())) {
                    Long robotUserId = userEntity.getId();
                    Long matchId = userRankedMatchEntity.getId();
                    ZnsUserWearsEntity userWear = userWearsService.getByUserId(robotUserId);
                    int randomInt = random.nextInt(10);
                    if (randomInt <= 6) {
                        log.info("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:{},给机器人:{} 概率小于60%，检查机器人是否使用默认套装", matchId, robotUserId);
                        if (Objects.isNull(userWear)) {
                            log.info("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:{},给机器人:{} 机器人没有初始化服装，开始初始化机器人服装", matchId, robotUserId);
                            userWearsBizService.addRobotWears(robotUserId, userEntity.getIsRobot());
                        } else if (isDefaultWear(userWear)) {
                            log.info("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:{},给机器人:{} 机器人使用默认服装，开始初始化机器人服装", matchId, robotUserId);
                            userWearsBizService.updateRobotWears(userWear, userEntity.getIsRobot());
                        }
                    }
                }

                //将机器人添加到房间
                Integer appVersion = null;
                String key = "prop_app_version:" + activityId;
                RBucket<String> bucket = redissonClient.getBucket(key);
                if (bucket.isExists()) {
                    appVersion = Integer.valueOf(bucket.get());
                }
                log.info("activityId :{},prop lunch robot App version:{},key :{}", activityId, appVersion, key);
                GamePushUtils.addRobot(gamepush, roomId, userRankedMatchEntity.getRouteId(), userEntity, activityId, matchLineEntity.getMindUserMatchId(), appVersion);

                //机器人在线推送
                socketPushUtils.onlinePush(roomId, userEntity, 1);

                //发送延迟消息给机器人发送跑步数据
                AutomaticAdmissionDealNewActivityDto dto = new AutomaticAdmissionDealNewActivityDto(matchLineEntity.getMindUserMatchId(), DateUtil.getDateYYYY_MM_DD_HH_MM_SS(), "段位赛机器人延迟队列", 0, userRankedMatchEntity.getTargetRunMileage());
                DelayDto delayDto = new DelayDto(Constants.ROBOT_ENTRANCE_NEW, JsonUtil.writeString(dto));
                log.info("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:{},段位赛延迟队列发送消息,robotUserId:{},delayDto:{},delaySecond:{}s", userRankedMatchEntity.getId(), matchLineEntity.getUserId(), JsonUtil.writeString(delayDto), delaySecond);
                rabbitTemplate.convertAndSend(delayExchangeName, "", JsonUtil.writeString(delayDto), message -> {
                    message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    message.getMessageProperties().setDelay((delaySecond * 1000) + 1100);   // 毫秒为单位，指定此消息的延时时长
                    return message;
                });
            } catch (Exception e) {
                log.error("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:" + userRankedMatchEntity.getId() + ",robotUserId:" + matchLineEntity.getUserId() + ",上线机器人异常", e);
            }
        }
        log.info("UserRankedMatchServiceImpl#dealPutInRobot----投放机器人,道具赛:{},处理完成", userRankedMatchEntity.getId());
    }

    /**
     * 道具赛预投放机器人（先加入道具）
     *
     * @param robotNum              投放机器人数量
     * @param userRankedMatchEntity 道具赛
     */
    private void putInRobotPre(Integer robotNum, PropUserRankedMatchEntity userRankedMatchEntity) {
        Long matchId = userRankedMatchEntity.getId();
        log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},robotNum:{} 机器人预投放开始", matchId, robotNum);
        //获取可用机器人跟机器人段位信息（key-> 机器人，val->机器人段位）
        List<ZnsUserEntity> usableRobotList = new ArrayList<>();
        Map<Long, PropUserRankedLevel> userRankedLevelMap = getUsableRobotList(robotNum, usableRobotList, userRankedMatchEntity);
        if (CollectionUtils.isEmpty(usableRobotList)) {
            return;
        }

        //获取道具赛创建用户段位区间
        PropUserRankedLevel createUserRankedLevel = propUserRankedLevelService.findByUserId(userRankedMatchEntity.getUserId());
        String sysConfig = sysConfigService.selectConfigByKey("prop_activity_config");
        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);
        //获取用户的隐藏分区间 目标机器人隐藏分区间=当前段位中值*0.2+当前隐藏分*0.8 ± （10-30）
        BigDecimal hiddenScore = getUserHideScore(createUserRankedLevel, rankedSysConfig);
        log.info("hiddenScore cal is " + hiddenScore);
        int randomNumber = random.nextInt(30 - 10 + 1) + 10;
        BigDecimal minHiddenScore = hiddenScore.add(BigDecimal.valueOf(-randomNumber));
        BigDecimal maxHiddenScore = hiddenScore.add(BigDecimal.valueOf(randomNumber));
        //其中机器人隐藏分最高为223
        if (minHiddenScore.compareTo(BigDecimal.valueOf(223)) > 0) {
            minHiddenScore = BigDecimal.valueOf(223);
        }
        log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},minHiddenScore:{},maxHiddenScore:{},获取用户的隐藏分区间", matchId, minHiddenScore, maxHiddenScore);
        if (minHiddenScore.compareTo(BigDecimal.TEN) < 0) {
            minHiddenScore = BigDecimal.TEN;
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},minHiddenScore 太小,重置后=" + minHiddenScore, matchId);
        }
        //获取隐藏分对应的段位
        List<PropRankedLevel> rankedLevels = listMatchedRankedLevelList(createUserRankedLevel, minHiddenScore, maxHiddenScore);
        BigDecimal minSpace = rankedLevels.stream().map(PropRankedLevel::getMinPace).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO); //取最大配速
        BigDecimal maxSpace = rankedLevels.stream().map(PropRankedLevel::getMaxPace).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);  //取最小配速
        Integer matchRankedLevelMinSeq = rankedLevels.stream().map(PropRankedLevel::getSeq).min(Integer::compareTo).orElse(101);  //取最小段位索引
        Integer matchRankedLevelMaxSeq = rankedLevels.stream().map(PropRankedLevel::getSeq).max(Integer::compareTo).orElse(701); //取最大段位索引

        MainActivity mainActivity = mainActivityService.findById(userRankedMatchEntity.getActivityId());

        //创建机器人匹配记录
        for (ZnsUserEntity robotUser : usableRobotList) {
            Long robotUserId = robotUser.getId();
            //给机器人设置段位信息
            PropUserRankedLevel userRankedLevel = userRankedLevelMap.get(robotUserId);
            BigDecimal randomHiddenScore = getBigDecimalRandom(minHiddenScore, maxHiddenScore, 1);
            PropRankedLevel rankedLevel = rankedLevels.get(random.nextInt(0, rankedLevels.size()));
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},给机器人设置段位,机器人:{},minHiddenScore:{},maxHiddenScore:{},randomHiddenScore:{}", matchId, robotUserId, minHiddenScore, maxHiddenScore, randomHiddenScore);
            userRankedLevel = buildUserRankedLevel(robotUser, userRankedLevel, rankedLevel);
            userRankedLevel.setScore(randomHiddenScore);
            userRankedLevel.setMatchUserId(createUserRankedLevel.getUserId());
            boolean update = true;
            if (Objects.isNull(userRankedLevel.getId())) {
                //有可能段位已经初始化，但是没有捞出来，需要再次确认
                PropUserRankedLevel existedUserRankedLevel = propUserRankedLevelService.findByUserId(createUserRankedLevel.getUserId());
                if (Objects.isNull(existedUserRankedLevel)) {
                    try {
                        update = false;
                        propUserRankedLevelService.insert(userRankedLevel);
                        log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},给机器人:{} 设置段位信息,UserRankedLevel:{}", matchId, robotUserId, userRankedLevel.getName());
                    } catch (Exception e) {
                        log.error("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:" + matchId + ",机器人:" + robotUserId + " 设置段位信息异常:" + e.getMessage(), e);
                    }
                }
            }
            if (update) {
                propUserRankedLevelService.update(userRankedLevel);
                log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},给机器人:{} 更新段位信息,UserRankedLevel:{}", matchId, robotUserId, userRankedLevel.getName());
            }

            //给机器人创建匹配记录（mind_user_match）
            PropUserRankedMatchLineEntity createUserMatchLine = propUserRankedMatchLineService.findByMatchIdAndUserId(matchId, userRankedMatchEntity.getUserId()); //道具发起人明细
            ZonedDateTime activityEndTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
            BigDecimal randomPace = getBigDecimalRandom(maxSpace, minSpace, Math.max(minSpace.subtract(maxSpace).divide(BigDecimal.valueOf(10), 2, RoundingMode.HALF_UP).intValue(), 5)); //m/s 转换为 km/h
            BigDecimal randomPaceKm = BigDecimal.valueOf(3600).divide(randomPace, 2, RoundingMode.HALF_UP);
            if (randomPaceKm.compareTo(PropRankedConstant.ROBOT_MIN_SPEED) < 0) {
                //机器人配速小于2km/h 就默认为2,（3.1要求段位赛机器人最低速度是2km/h）20240124
                randomPaceKm = PropRankedConstant.ROBOT_MIN_SPEED;
            }
            MindUserMatch m = buildMindMatch(robotUserId, 1, 1, createUserMatchLine.getUserId(), activityEndTime
                    , BigDecimal.valueOf(userRankedMatchEntity.getTargetRunMileage()), userRankedMatchEntity.getActivityId(), "", 0);
            m.setMatchUserId(createUserRankedLevel.getUserId());
            m.setV(randomPaceKm);
            Map<String, String> spadeMap = new HashMap<>();
            spadeMap.put("1~1.99", "F1");
            spadeMap.put("2~2.99", "F2");
            spadeMap.put("3~3.99", "F3");
            spadeMap.put("4~4.99", "F4");
            spadeMap.put("5~5.99", "D");
            spadeMap.put("6~7.99", "C");
            spadeMap.put("8~8.99", "B");
            spadeMap.put("9~9.99", "A");
            spadeMap.put("10~11", "S");
            spadeMap.put("11~20", "S+");
            String runMode = findClosestInteger(spadeMap, randomPaceKm.doubleValue());
            m.setRunMode(runMode);
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},给机器人:{} 创建匹配记录，maxSpace:{},minSpace:{},randomPace:{}m/s,randomPaceKm:{}km/h,模式随机数:{}", matchId, robotUserId, maxSpace, minSpace, randomPace, randomPaceKm, runMode);
            mindUserMatchBizService.insertMindUserMatch(m);

            //给机器人创建道具赛明细（zns_user_ranked_match_line）
            PropUserRankedMatchLineEntity myLineEntity = new PropUserRankedMatchLineEntity(robotUserId, userRankedMatchEntity.getActivityId(), matchId, new PropMatchRankedLevelVo(matchRankedLevelMinSeq, matchRankedLevelMaxSeq), PropRankedConstant.LineStatusEnum.LINE_STATUS_1.code, m.getId(), UserConstant.RoboTypeEnum.IS_ROBOT_1.getCode());
            propUserRankedMatchLineService.insert(myLineEntity);

            //活动参与人报名活动
            addRunActivityUsers(mainActivity, userRankedMatchEntity.getUserId(), userRankedMatchEntity.getTargetRunMileage(), List.of(m));
            log.info("UserRankedMatchServiceImpl#dealPutInRobot----道具赛预投放机器人,道具赛:{},给机器人:{} 机器人预投放完成", matchId, robotUserId);
        }
    }

    /**
     * 获取可用机器人跟机器人段位信息
     *
     * @param robotNum        机器人数量
     * @param usableRobotList 存放可用机器人
     * @return 机器人段位信息 （key-> 机器人，val->机器人段位）
     */
    private Map<Long, PropUserRankedLevel> getUsableRobotList(Integer robotNum, List<ZnsUserEntity> usableRobotList, PropUserRankedMatchEntity userRankedMatchEntity) {
        log.info("getUsableRobotList {}", userRankedMatchEntity);
        String countryCode = userRankedMatchEntity.getCountryCode();

        //可用机器人段位信息
        List<PropUserRankedLevel> usableRankedLevelList = new ArrayList<>(robotNum);
        for (int i = 0; i < robotNum; i++) {
            ZnsUserEntity user = null;
            //机器人所有国家，没有就用好美国
            List<String> allCountry = rotQueueConfigService.getAllCountry();
            countryCode = allCountry.contains(countryCode) ? countryCode : I18nConstant.CountryCodeEnum.US.getCode();
            RobotQuery robotQuery = RobotQuery.builder().mode(RobotRunModeEnum.N.getMode())
                    .activityId(userRankedMatchEntity.getActivityId())
                    .countrys(List.of(countryCode)).build();
            try {
                user = robotBizService.acquireRot(robotQuery, 0L, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("[getUsableRobotList] ---国家={}，查询指定机器人异常，", countryCode, e);
            }
            if (Objects.nonNull(user)) {
                usableRobotList.add(user);
                PropUserRankedLevel rankedLevel = initRootRankLevel(user);
                usableRankedLevelList.add(rankedLevel);
            }

        }
        return usableRankedLevelList.stream().collect(Collectors.toMap(PropUserRankedLevel::getUserId, Function.identity(), (a, b) -> a));
    }

    /**
     * 构建用户段位信息
     *
     * @param user
     * @param hideScore
     * @return
     */
    private static PropUserRankedLevel buildUserRankedLevel(ZnsUserEntity user, BigDecimal hideScore) {
        PropUserRankedLevel userRankedLevel = new PropUserRankedLevel();
        userRankedLevel.setUserId(user.getId());
        userRankedLevel.setIsRobot(user.getIsRobot());
        if (Objects.equals(user.getIsRobot(), YesNoStatus.YES.getCode())) {
            userRankedLevel.setRankedLevelId(1L);
        } else {
            userRankedLevel.setRankedLevelId(0L);
        }
        userRankedLevel.setName(RankedLevelEnums.AMATEUR_LEVEL_1.getName());
        userRankedLevel.setLevel(RankedLevelEnums.AMATEUR_LEVEL_1.getLevel());
        userRankedLevel.setRank(RankedLevelEnums.AMATEUR_LEVEL_1.getRank());
        userRankedLevel.setLevelProgress(BigDecimal.ZERO);
        userRankedLevel.setScore(hideScore);
        return userRankedLevel;
    }

    /**
     * 构建用户段位信息
     *
     * @param user
     * @param userRankedLevel
     * @param rankedLevel
     * @return
     */
    private PropUserRankedLevel buildUserRankedLevel(ZnsUserEntity user, PropUserRankedLevel userRankedLevel, PropRankedLevel rankedLevel) {
        if (Objects.isNull(userRankedLevel)) {
            userRankedLevel = new PropUserRankedLevel();
            userRankedLevel.setUserId(user.getId());
            userRankedLevel.setIsRobot(user.getIsRobot());
            userRankedLevel.setLevelProgress(BigDecimal.ZERO);
        }
        setRankLevel(userRankedLevel, rankedLevel);
        return userRankedLevel;
    }

    /**
     * 设置段位信息
     *
     * @param userRankedLevel
     * @param rankedLevel
     */
    private void setRankLevel(PropUserRankedLevel userRankedLevel, PropRankedLevel rankedLevel) {
        userRankedLevel.setRankedLevelId(rankedLevel.getId());
        userRankedLevel.setName(rankedLevel.getName());
        userRankedLevel.setLevel(rankedLevel.getLevel());
        userRankedLevel.setRank(rankedLevel.getRank());
    }


    /**
     * 获取最接近的段位
     *
     * @param spadeMap
     * @param targetValue
     * @return
     */
    private static String findClosestInteger(Map<String, String> spadeMap, Double targetValue) {
        String defaultKey = "2~2.99"; //3.1要求段位赛机器人最低速度是2km/h 20240124
        for (String range : spadeMap.keySet()) {
            String[] ranges = range.split("~");
            Double min = Double.valueOf(ranges[0]);
            Double max = Double.valueOf(ranges[1]);

            if (targetValue.compareTo(min) > -1 && targetValue.compareTo(max) < 1) {
                return spadeMap.get(range);
            }
        }
        return spadeMap.get(defaultKey);
    }

    /**
     * 判断是否是默认配装
     *
     * @param userWear
     * @return
     */
    private boolean isDefaultWear(ZnsUserWearsEntity userWear) {
        Predicate<Integer> isDefault = integer -> integer == 0;
        return isDefault.test(userWear.getHairColor()) &&
                isDefault.test(userWear.getSkinColour()) &&
                isDefault.test(userWear.getJacket()) &&
                isDefault.test(userWear.getTrousers()) &&
                isDefault.test(userWear.getShoes()) &&
                isDefault.test(userWear.getHead()) &&
                isDefault.test(userWear.getFaceDecoration());
    }

    /**
     * 获取用户隐藏分
     *
     * @param userRankedLevel
     * @param rankedSysConfig
     * @return
     */
    private BigDecimal getUserHideScore(PropUserRankedLevel userRankedLevel, Map<String, Object> rankedSysConfig) {
        BigDecimal userScore = getUserScore(userRankedLevel, rankedSysConfig);

        //机器人段位：当前发起人段位中位数*0.8 + 当前发起人隐藏分*0.2 +- 3 ->  当前段位中值*0.2+当前隐藏分*0.8 ± （10-30）
        PropRankedLevel rankedLevel = propRankedLevelService.findById(userRankedLevel.getRankedLevelId());
        //处于定位赛中
        if (userRankedLevel.getIsInPlacement() == 1) {
            Integer level = propRunRankedActivityUserService.getCurrentPlacementRankLevel(userRankedLevel.getUserId());
            RankedLevelEnums rankedLevelEnum = RankActivityConstants.PLACEMENT_SCORE_MAP.get(level);
            rankedLevel = propRankedLevelService.findByLevelAndRank(rankedLevelEnum.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank());
        }
        if (Objects.isNull(rankedLevel)) {
            rankedLevel = propRankedLevelService.findByLevelAndRank(RankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank());
        }
        log.info("getUserHideScore current level{}", rankedLevel);

        //段位中位数的隐藏分
        BigDecimal hiddenScore = rankedLevel.getMaxScore().add(rankedLevel.getMinScore()).divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.2));
        //加上用户隐藏分* 0.2 -> 当前隐藏分*0.8 ± （10-30)
        hiddenScore = hiddenScore.add(userScore.multiply(BigDecimal.valueOf(0.8D))).setScale(2, RoundingMode.HALF_UP);
        return hiddenScore;
    }


    /**
     * 获取用户的隐藏分，可能会触发放挫败机制
     *
     * @param userRankedLevel
     * @param rankedSysConfig
     */
    private BigDecimal getUserScore(PropUserRankedLevel userRankedLevel, Map<String, Object> rankedSysConfig) {
        BigDecimal userScore = userRankedLevel.getScore();

        ZnsUserEntity user = userService.findById(userRankedLevel.getUserId());
        if (user.getAppVersion() >= 3070 && userRankedLevel.getIsInPlacement() == 1) {
            Integer segment = propRunRankedActivityUserService.getCurrentRankSegment(user.getId());
            //是否需要定位赛
            if (segment < 3) {
                Integer level = propRunRankedActivityUserService.getCurrentPlacementRankLevel(user.getId());
                userScore = BigDecimal.valueOf(RankActivityConstants.SEGMENT_SCORE_MAP.get(level));
            }

        }

        //判断是否需要启动防挫败机制
        List<PropRunRankedActivityUser> userRankedActivityDataList = propRunRankedActivityUserService.findListByUserId(userRankedLevel.getUserId());
        if (CollectionUtils.isEmpty(userRankedActivityDataList) || userRankedActivityDataList.size() < 10) {
            int rackedActivityCount = 0;
            if (!CollectionUtils.isEmpty(userRankedActivityDataList)) {
                rackedActivityCount = userRankedActivityDataList.size();
            }
            log.info("数据不足，不需要启动防挫败机制,userId={}, score={}, 完赛次数={}", userRankedLevel.getUserId(), userScore, rackedActivityCount);
        } else {
            double defaultAverageRank = MapUtils.getDouble(rankedSysConfig, "defaultAverageRank");
            double averageRank = userRankedActivityDataList.stream().mapToInt(PropRunRankedActivityUser::getRank).average().orElse(defaultAverageRank);
            //平均10场活动排名靠后,触发防挫败机制
            if (Double.compare(averageRank, defaultAverageRank) > 0) {
                //8 未道具赛参赛用户数量
                userScore = userRankedLevel.getScore().multiply(BigDecimal.valueOf(8).subtract(BigDecimal.valueOf(averageRank)).divide(BigDecimal.valueOf(7), 2, RoundingMode.HALF_UP).add(BigDecimal.valueOf(0.5))).setScale(2, RoundingMode.HALF_UP); //((8 - averageRank) / 7 + 0.5))
                log.info("{}*((8-{})/7+0.5) = {}", userRankedLevel.getScore(), averageRank, userScore);
                log.info("用户触发道具赛启挫败机制,userId={}, userScore={},calcUserScore={}, averageRank={} ,defaultAverageRank={}", userRankedLevel.getUserId(), userRankedLevel.getScore(), userScore, averageRank, defaultAverageRank);
            }
        }
        log.info("getUserScore={}", userScore);
        return userScore;
    }

    /**
     * 获取匹配机器人段位
     *
     * @param myUserRankedLevel
     * @param minHiddenScore
     * @param maxHiddenScore
     * @return
     */
    private List<PropRankedLevel> listMatchedRankedLevelList(PropUserRankedLevel myUserRankedLevel, BigDecimal minHiddenScore, BigDecimal maxHiddenScore) {
        List<PropRankedLevel> allRankedLevelList = propRankedLevelService.findList();
        List<PropRankedLevel> rankedLevels = allRankedLevelList.stream()
                .filter(item -> inScoreRange(item, minHiddenScore, maxHiddenScore))
                .toList();
        //如果根据用户计算出的隐藏分无法获得匹配机器人对应的段位信息，则使用当前发起者的段位信息
        if (CollectionUtils.isEmpty(rankedLevels)) {
            rankedLevels = allRankedLevelList.stream().filter(item -> Objects.equals(item.getLevel(), myUserRankedLevel.getRank()) && Objects.equals(item.getRank(), myUserRankedLevel.getRank())).toList();
        }
        rankedLevels.forEach(item -> log.info("level={}", item));
        return rankedLevels;
    }

    /**
     * 判断用户隐藏分是否在段位分数区间内
     *
     * @param item
     * @param minScore
     * @param maxScore
     * @return
     */
    private boolean inScoreRange(PropRankedLevel item, BigDecimal minScore, BigDecimal maxScore) {
        boolean result = item.getMinScore().compareTo(minScore) > -1 && item.getMinScore().compareTo(maxScore) < 1 || item.getMaxScore().compareTo(minScore) > -1 && item.getMaxScore().compareTo(maxScore) < 1;
        log.info("in range, [{},{}] in ({},{}) = {}", item.getMinScore(), item.getMaxScore(), minScore, maxScore, result);
        return result;
    }

    /**
     * 获取随机数
     *
     * @param min
     * @param max
     * @param step
     * @return
     */
    private static BigDecimal getBigDecimalRandom(BigDecimal min, BigDecimal max, Integer step) {

        List<BigDecimal> bigDecimals = new ArrayList<>();
        for (BigDecimal i = MapUtil.getBigDecimal(min, BigDecimal.ZERO);
             i.compareTo(MapUtil.getBigDecimal(max, BigDecimal.ZERO)) <= 0;
             i = i.add(MapUtil.getBigDecimal(step, BigDecimal.ZERO))) {
            bigDecimals.add(i);
        }
        Random random = new Random();
        int r = random.nextInt(bigDecimals.size());
        return bigDecimals.get(r);
    }

    /**
     * 构建MindUserMatch
     *
     * @param userId
     * @param status
     * @param isRobot
     * @param mindUserMatchId
     * @param activityEndTime
     * @param runMileage
     * @param activityId
     * @param runMode
     * @param runTime
     * @return
     */
    public MindUserMatch buildMindMatch(Long userId, Integer status, Integer isRobot, Long mindUserMatchId, ZonedDateTime activityEndTime, BigDecimal runMileage, Long activityId, String runMode, Integer runTime) {
        MindUserMatch mindUserMatch = new MindUserMatch();
        mindUserMatch.setUserId(userId);
        mindUserMatch.setStatus(status);
        mindUserMatch.setIsRobot(isRobot);
        mindUserMatch.setMindUserMatchId(mindUserMatchId);
        mindUserMatch.setActivityEndTime(activityEndTime);
        mindUserMatch.setTargetMileage(runMileage.intValue());
        mindUserMatch.setTargetTime(runTime);
        mindUserMatch.setActivityId(activityId);
        mindUserMatch.setRunMode(runMode);
        String uniqueCode = OrderUtil.getUniqueCode("un");
        mindUserMatch.setUniqueCode(uniqueCode);
        return mindUserMatch;
    }

    /**
     * 查询socket更新道具赛明细状态
     *
     * @param userRankedMatchEntity
     */
    public void updateMatchLineStateBySocket(PropUserRankedMatchEntity userRankedMatchEntity, String source) {
        //完成规则类型：1表示完成跑步里程，2表示完成跑步时长
        Integer completeRuleType = userRankedMatchEntity.getTargetRunMileage() > 0 ? ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode() : ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_2.getCode();

        //获取socket房间用户
        List<Long> onlineUserIds = null;
        Long activityId = userRankedMatchEntity.getActivityId();
        Long roomId = NumberUtils.getGoalImNumber(activityId, userRankedMatchEntity.getTargetRunMileage(), completeRuleType);
        List<SocketRoomUserVo.UserVo> roomUserVoList = socketPushUtils.getOnLineRobot(roomId);
        log.info("updateMatchLineStateBySocket------查询socket更新道具赛明细状态,activityId:{},roomId:{},roomUserVo:{}", activityId, roomId, roomUserVoList);
        if (!org.springframework.util.CollectionUtils.isEmpty(roomUserVoList)) {
//            List<String> onlineUserEmails = roomUserVoList.stream().map(SocketRoomUserVo.UserVo::getEmail).distinct().filter(Objects::nonNull).toList();
//            //查询在线用户
//            onlineUserIds = userService.selectUserIdByEmails(onlineUserEmails);
//            List<String> onlineUserEmails = roomUserVoList.stream().map(SocketRoomUserVo.UserVo::getEmail).distinct().filter(Objects::nonNull).toList();
//            //查询在线用户
            onlineUserIds = roomUserVoList.stream().map(SocketRoomUserVo.UserVo::getUserId).distinct().filter(Objects::nonNull).toList();
            log.info("activityId:{},当前在线用户为{}", activityId, onlineUserIds);
        }

        //更新明细状态(把已进入且不在线的都更新为已退出)
        LambdaUpdateWrapper<PropUserRankedMatchLineEntity> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(PropUserRankedMatchLineEntity::getUserRankedMatchId, userRankedMatchEntity.getId());
        updateWrapper.eq(PropUserRankedMatchLineEntity::getMatchStatus, PropRankedConstant.LineStatusEnum.LINE_STATUS_2.code);
        updateWrapper.notIn(onlineUserIds != null, PropUserRankedMatchLineEntity::getUserId, onlineUserIds);
        updateWrapper.set(PropUserRankedMatchLineEntity::getMatchStatus, PropRankedConstant.LineStatusEnum.LINE_STATUS_0.code);
        updateWrapper.set(PropUserRankedMatchLineEntity::getModifier, source);
        propUserRankedMatchLineMapper.update(null, updateWrapper);

        //把已退出，但房间内有用户的加回来（防止app数据已落库，但是socket未推送的时候被错误下线）
        if (!org.springframework.util.CollectionUtils.isEmpty(onlineUserIds)) {
            LambdaUpdateWrapper<PropUserRankedMatchLineEntity> updateWrapper2 = Wrappers.lambdaUpdate();
            updateWrapper2.eq(PropUserRankedMatchLineEntity::getUserRankedMatchId, userRankedMatchEntity.getId());
            updateWrapper2.eq(PropUserRankedMatchLineEntity::getMatchStatus, PropRankedConstant.LineStatusEnum.LINE_STATUS_0.code);
            updateWrapper2.in(PropUserRankedMatchLineEntity::getUserId, onlineUserIds);
            updateWrapper2.set(PropUserRankedMatchLineEntity::getMatchStatus, PropRankedConstant.LineStatusEnum.LINE_STATUS_2.code);
            updateWrapper2.set(PropUserRankedMatchLineEntity::getModifier, source);
            propUserRankedMatchLineMapper.update(null, updateWrapper2);
        }
    }

    /**
     * 活动参与人报名活动
     *
     * @param rankActivity
     * @param userId
     * @param targetRunMileage
     * @param mindUserMatches
     */
    public void addRunActivityUsers(MainActivity rankActivity, Long userId, Integer targetRunMileage, List<MindUserMatch> mindUserMatches) {
        List<Long> userIds = mindUserMatches.stream().map(MindUserMatch::getUserId).toList();
        List<ZnsUserEntity> userEntities = userService.findByIds(userIds);
        Map<Long, ZnsUserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
        mindUserMatches.forEach(mindUserMatch -> {
            ZnsUserEntity user = userEntityMap.get(mindUserMatch.getUserId());
            ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
            activityUser.setActivityId(rankActivity.getId());
            activityUser.setUserId(mindUserMatch.getUserId());
            activityUser.setIsRobot(mindUserMatch.getIsRobot());
            activityUser.setIsTest(user.getIsTest());

            activityUser.setNickname(user.getFirstName());
            activityUser.setActivityType(RunActivityTypeEnum.PROP_ACTIVITY.getType());

            //1v1 匹配直接接受
            activityUser.setUserState(1);

            // 活动参与者
            activityUser.setInviterUserId(userId);

            activityUser.setTargetRunMileage(targetRunMileage);
            activityUser.setTargetRunTime(null);

            runActivityUserService.save(activityUser);
        });

    }


    public Page<PropUserRankDataDto> findPage(PropRankedActivityPageQuery queryDto, Integer appVersion) {
        Page<PropUserRankDataDto> page = new Page<>(queryDto.getPageNum(), queryDto.getPageSize());
        queryDto.setSourceType(0);
        Page<PropUserRankDataDto> pageList = propRunRankedActivityUserService.findPage(page, queryDto);
        List<PropUserRankDataDto> records = pageList.getRecords();
        if (!org.springframework.util.CollectionUtils.isEmpty(records)) {
            //查询是否作弊
            Map<Long, Integer> cheatMap = new HashMap<>();
            List<Long> detailIds = records.stream().map(PropRunRankedActivityUser::getRunDataDetailsId).distinct().toList();
            if (!org.springframework.util.CollectionUtils.isEmpty(detailIds)) {
                List<ZnsUserRunDataDetailsEntity> detailsEntities = userRunDataDetailsService.selectBatchIds(detailIds);
                Map<Long, Integer> map = detailsEntities.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsEntity::getId, ZnsUserRunDataDetailsEntity::getIsCheat, (k1, k2) -> k2));
                cheatMap.putAll(map);
            }

            //查这个用户每年第一天跑步时间
            List<ZonedDateTime> dates = propRunRankedActivityUserService.getUserRankFirstDay(queryDto.getUserId());

            List<Long> times = dates.stream().map(DateUtil::startOfDate).map(Date::getTime).collect(Collectors.toList());
            log.info("times {}", times);


            //重设跑步状态（区分逃跑）
            for (PropUserRankDataDto record : records) {
                //跑步状态
                Integer isComplete = 2; //运动中,不是数据库备注,只是前端展示使用临时定义
                if (record.getRunStatus() == 1) {
                    //跑步结束,0表示未完成(逃跑)，1表示完成
                    isComplete = record.getIsComplete() == 1 ? 1 : 0;
                }
                record.setIsComplete(isComplete);
                if (MainActivityStateEnum.STARTED.getCode().equals(record.getActivityState())) {
                    //再次判断活动是否已结束
                    Integer activityState = mainActivityBizService.closedRankActivity(record.getActivityId());
                    if (activityState != null) {
                        record.setActivityState(activityState);
                    }
                }
                //是否作弊
                Integer isCheat = cheatMap.get(record.getRunDataDetailsId());
                record.setIsCheat(isCheat);
                //补充段位信息
                PropUserRankedLevelLog rankedLevelLog = propUserRankedLevelLogService.findByUserIdAndActivityId(record.getUserId(), record.getActivityId());
                if (rankedLevelLog != null) {
                    record.setLevel(rankedLevelLog.getLevel());
                    RankedLevelEnums rankedLevelEnum = RankedLevelEnums.resolve(rankedLevelLog.getLevel(), rankedLevelLog.getRank());
                    record.setName(I18nMsgUtils.getMessage("prop.rankedActivity.rank.RankedLevelEnums." + rankedLevelEnum));
                    record.setLevelProgress(rankedLevelLog.getLevelProgress());
                }

                //是否新年第一天数据
                for (Long time : times) {
                    ZonedDateTime startOfDate = DateUtil.startOfDate(record.getCompleteTime());
                    log.info("record.getCompleteTime(){}", startOfDate.toInstant().toEpochMilli());
                    if (record.getCompleteTime() != null && time == DateUtil.startOfDate(record.getCompleteTime()).toInstant().toEpochMilli()) {
                        record.setIsNewYearFirstDay(1);
                    }
                }
                Integer resultDataState = ResultDataStateEnum.stateConvert(isComplete, isCheat, record.getRunStatus(), null);
                record.setResultDataState(resultDataState);
                //查询是否有风控检测中
                Integer riskReview = userRunDataDetailsCheatService.isRiskReview(Collections.singletonList(record.getActivityId()));
                record.setIsRiskReview(riskReview);
            }
        }
        return pageList;
    }


}
