package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 兑换券规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleDto;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRuleResp;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LIKE;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.OrderBy;
import com.lz.mybatis.plugin.annotations.OrderType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface ExchangeScoreRuleDao extends BaseMapper<ExchangeScoreRule> {


    ExchangeScoreRule selectExchangeScoreRuleById(@Param("id") Long id);


    int deleteExchangeScoreRuleById(@Param("id") Long id);


    @Mapping({ExchangeScoreRule.all,
            Coupon.title_,
            Coupon.name_,
            Coupon.type_,
            Coupon.coupon_type,
            "(case when t1.quota >= 0 then t1.quota - t1.quota_send else t1.quota end) as remainQuota"
    })
    @LeftJoinOns({
            @Item(value = Coupon.class, left = ExchangeScoreRule.coupon_id, right = Coupon.id_),
    })
    @OrderBy(value = {ExchangeScoreRule.id_}, type = {OrderType.DESC})
    List<ExchangeScoreRuleResp> selectPageList(IPage page,
                                               @Column(ExchangeScoreRule.activity_name) @IF @LIKE String activityName,
                                               @Column(ExchangeScoreRule.status_) @IF Integer status,
                                               @IF @OrderBy("t.exchange_score") String exchangeScore,
                                               @IF @OrderBy("t.exchange_count") String exchangeCount,
                                               @IF @OrderBy(" (t1.quota - t1.quota_send) ") String remainQuota);


    Page<ExchangeScoreRuleResp> selectNewPageList(IPage page, @Param("dto") ExchangeScoreRuleDto dto);

    List<ExchangeScoreRuleResp> selectAppNewPageList(IPage page, @Param("isReview") Integer isReview, @Param("categoryCode") Integer categoryCode, @Param("currencyCode") String currencyCode, @Param("belongTo") Integer belongTo, Integer appVersion);


    int updateExchangeScoreRuleStatusById(ZonedDateTime gmtModified, Integer status, @By Long id);

    List<ExchangeScoreAward> selectawardList();

    int updateSelective(@Param("entity") ExchangeScoreRule rule, @Param("ignoredFields") List<String> ignoredFields);
}
