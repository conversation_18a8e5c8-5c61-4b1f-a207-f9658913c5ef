package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 兑换券规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleDto;
import com.linzi.pitpat.data.awardservice.mapper.ExchangeScoreRuleDao;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRuleResp;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ExchangeScoreRuleServiceImpl extends ServiceImpl<ExchangeScoreRuleDao, ExchangeScoreRule> implements ExchangeScoreRuleService {
    public static final List<String> nullableFields = List.of("tagImage");

    @Autowired
    private ExchangeScoreRuleDao exchangeScoreRuleDao;

    @Resource
    private RedisTemplate redisTemplate;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExchangeScoreByIdAndExchangeReserve(ExchangeScoreRule rule) {
        LambdaUpdateWrapper<ExchangeScoreRule> wrapper = Wrappers.<ExchangeScoreRule>lambdaUpdate()
                .eq(ExchangeScoreRule::getId, rule.getId()).ne(ExchangeScoreRule::getExchangeReserve, 0);
        ExchangeScoreRule oldRule = getById(rule.getId());
        if (oldRule.getExchangeReserve() > 0) {
            // 可兑换量有限的，需要扣减一个
            rule.setExchangeReserve(oldRule.getExchangeReserve() - 1);
        }
        rule.setGmtModified(ZonedDateTime.now());
        int i = exchangeScoreRuleDao.update(rule, wrapper);
        if (i < 1) {
            log.error("积分兑换商品失败，ruleId:{}", rule.getId());
            throw new BaseException(CommonError.SYSTEM_ERROR.getMsg(), CommonError.SYSTEM_ERROR.getCode());
        }
    }

    @Override
    public Boolean verifyUserScoreRuleDayExchange(Long userId, Long ruleId) {
        Boolean flag = false;
        Integer exchangePersonDayLimit = getById(ruleId).getExchangePersonDayLimit();
        String userDayExchangeKey = String.format(RedisKeyConstant.USER_DAY_PRODUCT_EXCHANGE, userId, ruleId);
        Object userDayExchangeNum = redisTemplate.opsForValue().get(userDayExchangeKey);
        if (Objects.isNull(userDayExchangeNum)
                || exchangePersonDayLimit == -1
                || Integer.parseInt(userDayExchangeNum.toString()) < exchangePersonDayLimit) {
            flag = true;
        }
        return flag;
    }

    @Override
    public void setUserScoreRuleDayExchangeCache(Long userId, Long ruleId) {
        String userDayExchangeKey = String.format(RedisKeyConstant.USER_DAY_PRODUCT_EXCHANGE, userId, ruleId);
        Object userDayExchangeNum = redisTemplate.opsForValue().get(userDayExchangeKey);
        ZonedDateTime now = ZonedDateTime.now();
        long expireTime = DateUtil.getEndOfDate(now).toInstant().toEpochMilli() - now.toInstant().toEpochMilli();
        if (Objects.isNull(userDayExchangeNum)) {
            redisTemplate.opsForValue().set(userDayExchangeKey, "1", expireTime, TimeUnit.MILLISECONDS);
        } else {
            redisTemplate.opsForValue().increment(userDayExchangeKey, 1);
        }
    }

    @Override
    public ExchangeScoreRule selectExchangeScoreRuleById(Long ruleId) {
        return exchangeScoreRuleDao.selectExchangeScoreRuleById(ruleId);
    }

    @Override
    public void updateExchangeScoreRuleStatusById(ZonedDateTime date, Integer status, Long id) {
        exchangeScoreRuleDao.updateExchangeScoreRuleStatusById(date, status, id);
    }

    @Override
    public Page<ExchangeScoreRuleResp> findPage(ExchangeScoreRuleDto pageQuery) {
        return exchangeScoreRuleDao.selectNewPageList(PageHelper.ofPage(pageQuery), pageQuery);
    }

    @Override
    public int deleteExchangeScoreRuleById(Long id) {
        return exchangeScoreRuleDao.deleteExchangeScoreRuleById(id);
    }

    @Override
    public List<ExchangeScoreRuleResp> selectAppNewPageList(IPage page, Integer inReview, Integer categoryCode, String currencyCode, Integer belongTo, Integer appVersion) {
        return exchangeScoreRuleDao.selectAppNewPageList(page, inReview, categoryCode, currencyCode, belongTo, appVersion);
    }

    @Override
    public void updateSelective(ExchangeScoreRule rule) {
        exchangeScoreRuleDao.updateSelective(rule, nullableFields);
    }
}
