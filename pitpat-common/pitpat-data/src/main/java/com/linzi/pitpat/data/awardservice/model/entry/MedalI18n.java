package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-12-14
 */

@Data
@NoArgsConstructor
@TableName("zns_medal_i18n")
public class MedalI18n implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //是否删除
    private Integer isDelete;
    //勋章id
    private Long medalId;
    //语言code
    private String langCode;
    //语言名称
    private String langName;
    //勋章名称
    private String name;
    //勋章描述
    private String remark;

    public MedalI18n(Long medalId, String langCode, String langName, String name, String remark) {
        this.medalId = medalId;
        this.langCode = langCode;
        this.langName = langName;
        this.name = name;
        this.remark = remark;
    }
}
