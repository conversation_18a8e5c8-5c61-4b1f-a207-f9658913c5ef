package com.linzi.pitpat.data.activityservice.service.impl;
/**
 * <p>
 * 活动主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */


import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.request.pro.RaceProActivityPageQueryRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProModuleCount;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityConditionQuery;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTimeRangeQuery;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivityDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivityTitleDto;
import com.linzi.pitpat.data.activityservice.mapper.MainActivityMapper;
import com.linzi.pitpat.data.activityservice.model.dto.RaceProModuleActivityDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCompetitiveListQuery;
import com.linzi.pitpat.data.activityservice.model.query.MainActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.MyRaceCalendarActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.MyRecordActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.ToBeInvolvedActivityQuery;
import com.linzi.pitpat.data.activityservice.model.vo.EquipmentActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MainActivityVO;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRaceCalendarActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRecordActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ToBeInvolvedActListVo;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.db.mybatis.wrapper.QueryWrapperBuilder;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;
import java.util.stream.Collectors;

@Service
public class MainActivityServiceImpl implements MainActivityService {


    @Autowired
    private MainActivityMapper mainActivityMapper;
    @Autowired
    private ActivityIDGenerateService idGenerateService;

    @Override
    public MainActivity findById(Long id) {
        return mainActivityMapper.selectById(id);
    }

    @Override
    public int insert(MainActivity mainActivity) {
        if (mainActivity.getId() == null) {
            mainActivity.setId(idGenerateService.generateActivityID());
        }
        if (mainActivity.getActivityNo() == null) {
            mainActivity.setActivityNo("HD" + mainActivity.getId());
        }
        return mainActivityMapper.insert(mainActivity);
    }


    @Override
    public int update(MainActivity mainActivity) {
        if (mainActivity.getActivityNo() == null) {
            mainActivity.setActivityNo("HD" + mainActivity.getId());
        }
        return mainActivityMapper.updateById(mainActivity);
    }


    @Override
    public int deleteById(Long id) {
        return mainActivityMapper.deleteById(id);
    }

    @Override
    public List<MyRaceCalendarActivityListVo> findMyRaceCalendarActivity(MyRaceCalendarActivityQuery query) {
        return mainActivityMapper.findMyRaceCalendarActivity(query);
    }

    @Override
    public List<MyRecordActivityListVo> myRecordActList(Page page, MyRecordActivityQuery query) {
        return mainActivityMapper.myRecordActList(page, query);
    }

    @Override
    public List<ToBeInvolvedActListVo> toBeInvolvedActivityList(ToBeInvolvedActivityQuery query) {
        return mainActivityMapper.toBeInvolvedActList(query);
    }

    @Override
    public Page<MainActivity> findAllByWrapper(AbstractWrapper wrapper, Page<MainActivity> page) {

        return mainActivityMapper.selectPage(page, wrapper);


    }

    @Override
    public List<ActivityTypeDto> findProceedActivity(Long userId, String zoneId, Integer dataSource, Integer deviceType) {
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        ZonedDateTime now = ZonedDateTime.now();
        String currentTime = DateUtil.getDate3ByTimeZone(now, timeZone, DateUtil.DATE_TIME_SHORT);

        return mainActivityMapper.findProceedActivity(userId, currentTime, dataSource, deviceType);
    }

    /**
     * 0 已上架，不可报名 1 进行中 2 已结束 3 已下架 4  可报名
     *
     * @param mainActivityId
     * @param user
     * @return
     */
    //用户视角
    @Override
    public Integer getActivityState(Long mainActivityId, ZnsUserEntity user) {
        MainActivity mainActivity = mainActivityMapper.selectById(mainActivityId);
        return getActivityStateToUser(mainActivity, user.getZoneId());
    }

    @Override
    public Integer getActivityState(MainActivity mainActivity, ZnsUserEntity user) {
        return getActivityStateToUser(mainActivity, user.getZoneId());
    }

    @Override
    public Integer getActivityStateToUser(MainActivity mainActivity, String zoneId) {
        if (mainActivity.getActivityState() == 3) {
            return mainActivity.getActivityState();
        }

        Integer timeStyle = mainActivity.getTimeStyle();

        Long startTimeStamp = 0l;
        Long endTimeStamp = 0l;
        Long appStartTimeStamp = 0l;
        Long appEndTimeStamp = 0l;
        //绝对时间
        if (timeStyle == 0) {
            startTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityStartTime(), "UTC");
            endTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityEndTime(), "UTC");
            appStartTimeStamp = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(), "UTC");
            appEndTimeStamp = DateUtil.getStampByZone(mainActivity.getApplicationEndTime(), "UTC");
        } else if (timeStyle == 1) {
            startTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityStartTime(), zoneId);
            endTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityEndTime(), zoneId);
            appStartTimeStamp = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(), zoneId);
            appEndTimeStamp = DateUtil.getStampByZone(mainActivity.getApplicationEndTime(), zoneId);
        }
        Integer state = 0;
        //0 已上架，不可报名 1 进行中 2 已结束  4  可报名
        if (System.currentTimeMillis() < appStartTimeStamp) {
            state = 0;
        } else if (System.currentTimeMillis() < startTimeStamp) {
            state = 4;
        } else if (System.currentTimeMillis() < endTimeStamp) {
            state = 1;
        } else {
            state = 2;
        }
        return state;

    }

    /**
     * 确定活动状态
     *
     * @param startTimeStamp
     * @param endTimeStamp
     * @return
     */
    private Integer determineActState(Long startTimeStamp, Long endTimeStamp) {
        Integer state = null;
        long currentStamp = System.currentTimeMillis();
        if (currentStamp < startTimeStamp) {
            state = 0;
        } else if (currentStamp < endTimeStamp) {
            state = 1;
        } else {
            state = 2;
        }

        return state;
    }


    @Override
    public Long findUseByPlayId(Long playId) {
        return mainActivityMapper.selectCount(Wrappers.<MainActivity>lambdaQuery()
                .eq(MainActivity::getPlayId, playId)
                .eq(MainActivity::getActivityState, 1)
                .eq(MainActivity::getIsDelete, 0));
    }

    @Override
    public List<Long> findUseActivityByPlayId(Long playId) {
        List<MainActivity> list = mainActivityMapper.findUseActivityByPlayId(playId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        //只返回活结束时间内的，考虑各时区
        return list.stream().filter(m -> (m.getTimeStyle() == 0 && DateTimeUtil.parse(m.getActivityEndTime()).isAfter(ZonedDateTime.now())) ||
                        (m.getTimeStyle() == 1 && DateTimeUtil.parse(m.getActivityEndTime()).isAfter(DateUtil.addHours(ZonedDateTime.now(), -12))))
                .map(MainActivity::getId).collect(Collectors.toList());
    }

    @Override
    public List<MainActivity> findListByIds(List<Long> activityIds) {
        return mainActivityMapper.selectBatchIds(activityIds);
    }

    @Override
    public List<MainActivity> findAllNoEndActivity(Page<MainActivity> page, String currentTime, List<Long> activityIds, List<String> mainTypes,
                                                   String stateCode, String countryCode, List<Long> groupsByUserId, Boolean flag, boolean checkUser) {
        page.setSearchCount(false);
        return mainActivityMapper.findAllNoEndActivity(page, currentTime, activityIds, mainTypes, stateCode, countryCode, groupsByUserId, flag, checkUser);
    }

    @Override
    public List<MainActivity> findBeginActivity() {
        //time_style = 1开始时间 +14 小时
        ZonedDateTime startTime = DateUtil.addHours(ZonedDateTime.now(), 14);
        return mainActivityMapper.findBeginActivity(startTime);
    }

    @Override
    public List<MainActivity> findNotFinishSingleActivity() {
        //time_style = 1开始时间 +14 小时
        ZonedDateTime startTime = DateUtil.addHours(ZonedDateTime.now(), 14);
        return mainActivityMapper.findNotFinishActivity(startTime);
    }

    @Override
    public List<MainActivity> findToEndActivity() {
        //time_style = 1 结束时间 -12 小时
        ZonedDateTime endTime = DateUtil.addHours(ZonedDateTime.now(), -12);
        return mainActivityMapper.findToEndActivity(endTime);
    }

    @Override
    public List<MainActivity> findMarathonActivityEndApplication() {
        return mainActivityMapper.findMarathonActivityEndApplication();
    }


    @Override
    public MainActivity findByRemark(String remark) {

        return mainActivityMapper.selectOne(Wrappers.<MainActivity>lambdaQuery()
                .eq(MainActivity::getRemark, remark)
                .eq(MainActivity::getIsDelete, 0)
                .last("limit 1"));

    }

    @Override
    public Integer getActivityState(Long startTime, Long endTime) {
        return determineActState(startTime, endTime);
    }

    @Override
    public List<MainActivity> findListByQuery(ZonedDateTime now) {
        return mainActivityMapper.selectList(new QueryWrapper<MainActivity>()
                .ge("activity_end_time", now)
                .eq("activity_state", 1)
                .eq("is_delete", YesNoStatus.NO.getCode()));
    }

    @Override
    public Page<ActivityDto> findActivityByCondition(Page<ActivityDto> page, ActivityConditionQuery query) {
        return mainActivityMapper.findActivityByCondition(page, query);
    }

    @Override
    public Integer getActivityState(MainActivity mainActivity, String zoneId) {
        if (mainActivity == null) {
            return 0;
        }
        if (mainActivity.getActivityState() == 3 || mainActivity.getActivityState() == -1) {
            return mainActivity.getActivityState();
        }

        Integer timeStyle = mainActivity.getTimeStyle();

        Long startTimeStamp = 0l;
        Long endTimeStamp = 0l;
        //绝对时间
        if (timeStyle == 0) {
            startTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityStartTime(), "UTC");
            endTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityEndTime(), "UTC");
        } else if (timeStyle == 1) {
            startTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityStartTime(), zoneId);
            endTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityEndTime(), zoneId);
        }
        return determineActState(startTimeStamp, endTimeStamp);
    }

    @Override
    public Integer getActivityState(Long mainActivityId, String zoneId) {
        MainActivity mainActivity = mainActivityMapper.selectById(mainActivityId);
        return getActivityState(mainActivity, zoneId);
    }


    @Override
    public List<MainActivity> findEndActivityByIds(Page<MainActivity> page, String currentTime, List<Long> sameBatchAct, List<String> mainTypes) {
        if (CollectionUtils.isEmpty(sameBatchAct)) {
            return null;
        }

        return mainActivityMapper.findEndActivityByIds(page, currentTime, sameBatchAct, mainTypes);
    }

    @Override
    public List<MainActivity> findList(MainActivityQuery query) {
        LambdaQueryWrapper<MainActivity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(!CollectionUtils.isEmpty(query.getActivityIds()), MainActivity::getId, query.getActivityIds());
        wrapper.in(!CollectionUtils.isEmpty(query.getActivityStateList()), MainActivity::getActivityState, query.getActivityStateList());
        wrapper.ge(Objects.nonNull(query.getApplicationStartTimeHead()), MainActivity::getApplicationStartTime, query.getApplicationStartTimeHead());
        wrapper.le(Objects.nonNull(query.getApplicationStartTimeTail()), MainActivity::getApplicationStartTime, query.getApplicationStartTimeTail());
        wrapper.eq(Objects.nonNull(query.getMainType()), MainActivity::getMainType, query.getMainType());
        wrapper.eq(MainActivity::getIsDelete, 0);
        return mainActivityMapper.selectList(wrapper);
    }

    @Override
    public List<MainActivity> findListByRotSetting(MainActivityQuery query) {
        return mainActivityMapper.findListByRotSetting(query);
    }

    @Override
    public void removePolymerizationMark(List<Long> actIds) {
        if (!CollectionUtils.isEmpty(actIds)) {
            LambdaUpdateWrapper<MainActivity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(MainActivity::getMainType, MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType())
                    .in(MainActivity::getId, actIds)
                    .eq(MainActivity::getMainType, MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType());
            mainActivityMapper.update(null, wrapper);
        }

    }

    @Override
    public List<MainActivity> findListByIdsAndState(Page page, List<Long> activityIds, String stateCode, String countryCode, List<Long> groupsByUserId, String currentTime, boolean checkUser) {
        return mainActivityMapper.findListByIdsAndState(page, activityIds, stateCode, countryCode, groupsByUserId, currentTime, checkUser);
    }

    @Override
    public void updateBatch(List<MainActivity> mainActivityList) {
        for (MainActivity mainActivity : mainActivityList) {
            update(mainActivity);
        }
    }

    @Override
    public void insertOrUpdate(MainActivity mainActivity) {
        if (mainActivity.getId() != null && findById(mainActivity.getId()) != null) {
            update(mainActivity);
        } else {
            insert(mainActivity);
        }
    }

    @Override
    public List<MainActivity> beforeSingleBegin(ZonedDateTime now) {
        return mainActivityMapper.selectBeforeSingleBegin(now);
    }

    /**
     * 查询可以报名的活动,越早开赛越靠前 （缓存60秒）
     *
     * @param currentTime      用户当前时区时间，
     * @param mainTypes        活动类型，
     * @param stateCode        用户州code，
     * @param groupsByUserId   用户所在分组id，
     * @param checkFee         免费活动是否无版本号限制，true：无限制，false：有限制，
     * @param equipmentVersion 用户设备版本号，不能为空
     * @param applyStartTime   报名最早开始时间 ，不能为空
     * @param startEndTime     活动最晚开始时间
     * @return
     */
    @Override
    @DataCache(value = {"currentTime", "mainTypes", "stateCode", "countryCode", "groupsByUserId", "checkFee", "equipmentVersion", "applyStartTime", "startEndTime", "equipmentModels"}, timeout = 60)
    public List<MainActivityVO> findCanApplyActivityId(String currentTime, List<String> mainTypes,
                                                       String stateCode, String countryCode, List<Long> groupsByUserId,
                                                       boolean checkFee, Integer equipmentVersion,
                                                       String applyStartTime, String startEndTime, List<String> equipmentModels) {
        return mainActivityMapper.findCanApplyActivityId(currentTime, mainTypes, stateCode, countryCode, groupsByUserId, checkFee, equipmentVersion, applyStartTime, startEndTime, equipmentModels);
    }

    @Override
    public List<MainActivity> findAutoReviewAwardList(int reviewTimeIntHours) {
        List<MainActivity> mainActivities = mainActivityMapper.selectList(Wrappers.<MainActivity>lambdaQuery()
                .eq(MainActivity::getAwardSendStatus, 0)
                .eq(MainActivity::getActivityState, MainActivityStateEnum.ENDED.getCode())
                .eq(MainActivity::getIsDelete, 0)
                .in(MainActivity::getMainType, Arrays.asList(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType()))
                .apply("activity_end_time <= NOW() -INTERVAL " + reviewTimeIntHours + " HOUR").last(" limit 10"));
        return mainActivities;
    }

    @Override
    public MainActivity findOneNoEndActivityUnlimited(String userTime, String nowTime, ZonedDateTime dataTime, String countryCode, String stateCode) {
        return mainActivityMapper.findOneNoEndActivityUnlimited(userTime, nowTime, dataTime, countryCode, stateCode);
    }

    @Override
    public List<MainActivity> findNoEndRateLimitActivity(String userTime, String nowTime, ZonedDateTime dataTime) {
        return mainActivityMapper.findNoEndRateLimitActivity(userTime, nowTime, dataTime);
    }

    @Override
    public List<MainActivity> findAreaActivity(List<Long> noEndRateLimitActIdList, String stateCode, String countryCode) {
        return mainActivityMapper.findAreaActivity(noEndRateLimitActIdList, stateCode, countryCode);
    }

    @Override
    public List<MainActivity> findTargetTypeActivity(String userTime, String nowTime, Integer targetType, String stateCode, String countryCode, ZonedDateTime dataTime) {
        return mainActivityMapper.findTargetTypeActivity(userTime, nowTime, targetType, stateCode, countryCode, dataTime);
    }


    @Override
    public boolean checkCountShowInMoreActivity(int maxCount, Integer equipmentMainType) {
        return mainActivityMapper.countShowInMoreActivity(maxCount, equipmentMainType) >= maxCount;
    }

    /**
     * @param query
     * @return
     */
    @Override
    public List<MainActivity> findCompetitiveActivityList(ActivityCompetitiveListQuery query) {
        if (CollectionUtils.isEmpty(query.getUserGroups())) {
            ArrayList<Long> userGroups = Lists.newArrayList();
            userGroups.add(0L);
            query.setUserGroups(userGroups);
        }
        return mainActivityMapper.findCompetitiveActivityList(query);
    }

    @Override
    public void setIsCompetitive(Long id, boolean b) {
        MainActivity activity = new MainActivity();
        activity.setId(id);
        activity.setIsCompetitive(b ? 1 : 0);
        mainActivityMapper.updateById(activity);
    }

    @Override
    public void update(MainActivity mainActivity, Wrapper<MainActivity> updateWrapper) {
        mainActivityMapper.update(mainActivity, updateWrapper);
    }

    @Override
    public void clearMutexActivity(Long mainActId) {
        if (mainActId == null) {
            return;
        }
        MainActivity activity = mainActivityMapper.selectById(mainActId);
        if (activity != null) {
            LambdaUpdateWrapper<MainActivity> set = Wrappers.lambdaUpdate(MainActivity.class).eq(MainActivity::getId, mainActId)
                    .set(MainActivity::getMutexActivityIds, null);
            mainActivityMapper.update(set);
        }
    }

    @Override
    public boolean getActivityAwardSendStatus(Long id) {
        MainActivity mainActivity = mainActivityMapper.selectById(id);
        if (Objects.isNull(mainActivity)) {
            return false;
        }
        return getActivityAwardSendStatus(mainActivity);
    }

    @Override
    public boolean getActivityAwardSendStatus(MainActivity mainActivity) {
        if (MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())
                || MainActivityStateEnum.OFF_SHELF.getCode().equals(mainActivity.getActivityState())) {
            //不是待审核和审核中，认为发放完成
            return !AwardSendStatusEnum.NO_VIEW.getCode().equals(mainActivity.getAwardSendStatus())
                    && !AwardSendStatusEnum.VIEW_SENDING.getCode().equals(mainActivity.getAwardSendStatus());
        } else {
            return false;
        }
    }

    @Override
    public List<ActivityTitleDto> findActivityByTimeRange(ActivityTimeRangeQuery request) {
        LambdaQueryWrapper<MainActivity> eq = Wrappers.lambdaQuery(MainActivity.class)
                .select(MainActivity::getId, MainActivity::getRemark)
                .eq(MainActivity::getIsDelete, 0L)
                .in(MainActivity::getMainType, request.getActivityMainType())
                .in(!CollectionUtils.isEmpty(request.getActivityStatus()), MainActivity::getActivityState, request.getActivityStatus())
                .ge(MainActivity::getActivityStartTime, request.getActivityStartTimeMin())
                .le(MainActivity::getActivityEndTime, request.getActivityEndTimeMax());
        return mainActivityMapper.selectList(eq).stream().map(item -> {
            ActivityTitleDto dto = new ActivityTitleDto();
            dto.setId(item.getId());
            dto.setTitle(item.getRemark());
            return dto;
        }).toList();
    }

    @Override
    public List<MainActivity> findClubEventActivity() {
        return mainActivityMapper.findClubEventActivity();
    }

    @Override
    public List<MainActivity> findTeamActivity() {
        return mainActivityMapper.findTeamActivity();
    }

    @Override
    public List<MainActivity> findEndNoSendAwardActivity() {
        return mainActivityMapper.findEndNoSendAwardActivity();
    }

    @Override
    public List<MainActivity> findRecommendCommunityActivityIdList(int size) {
        return mainActivityMapper.findRecommendCommunityActivityList(size);
    }

    @Override
    public List<MainActivity> findShowInMoreStartApplicationByDate(ZonedDateTime now, ZonedDateTime minus) {
        return mainActivityMapper.findShowInMoreStartApplicationByDate(now, minus);
    }

    @Override
    public Page<RaceProModuleActivityDto> findProActivityPage(RaceProActivityPageQueryRequest query) {
//        return mainActivityMapper.findProActivityRace(Page.of(query.getPageNum(), query.getPageSize()), query);
        Page<RaceProModuleActivityDto> page = new Page<>(query.getPageNum(), query.getPageSize());
        Long l = mainActivityMapper.proActivityRaceCount(query);
        page.setTotal(l);
        if (l <= 0) {
            return page;
        }
        List<RaceProModuleActivityDto> proActivityRace = mainActivityMapper.findProActivityRace(query);
        page.setRecords(proActivityRace);

        return page;
    }

    @Override
    public List<RaceProModuleCount> findProActivityTypeCountMinStartTime(RaceProActivityPageQueryRequest query) {

        List<RaceProModuleCount> result = query.getProActivityTypes().stream().map(a -> {
            RaceProModuleCount moduleCount = new RaceProModuleCount();
            moduleCount.setProActivityType(a);
            moduleCount.setActivityCount(0L);
            moduleCount.setMinStartTime(Long.MAX_VALUE);
            moduleCount.setMinActivityId(Long.MAX_VALUE);
            return moduleCount;
        }).toList();

        List<RaceProModuleCount> proActivityTypeCountMinStartTime = mainActivityMapper.findProActivityTypeCountMinStartTime(query);
        if (!CollectionUtils.isEmpty(proActivityTypeCountMinStartTime)) {
            result.forEach(r -> {
                proActivityTypeCountMinStartTime.stream().filter(p -> p.getProActivityType().equals(r.getProActivityType())).findFirst().ifPresent(p -> {
                    r.setMinStartTime(p.getMinStartTime());
                    r.setActivityCount(p.getActivityCount());
                    r.setMinActivityId(p.getMinActivityId());

                });
            });
        }
        return result.stream().sorted(Comparator.comparing(RaceProModuleCount::getMinStartTime).thenComparing(RaceProModuleCount::getMinActivityId)).toList();
    }

    @Override
    public List<Long> findProActivityIdsByTimeRange(ZonedDateTime startTime, ZonedDateTime endTime) {
        return mainActivityMapper.findProActivityIdsByTimeRange(startTime, endTime);
    }

    @Override
    public MainActivity getCurrentFreeActivity(String mainType) {

        LambdaQueryWrapper<MainActivity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MainActivity::getMainType,mainType)
                .eq(MainActivity::getActivityState, ActivityStateEnum.IN_PROGRESS.getState())
                .ge(MainActivity::getGmtCreate, DateUtil.addDays(DateUtil.getNowDate(), -30))
                .orderByDesc(MainActivity::getId);
        return mainActivityMapper.selectOne( wrapper,false);
    }

    /**
     * 查询设备进行中的的活动
     *
     * @param equipmentModel 设备型号
     * @param num            查询数量,为空查询所有的
     */
    @Override
    public List<EquipmentActivityVo> findActivityByEquipmentModel(String equipmentModel, String languageCode, Integer num, List<Long> groupsByUserId, String stateCode,
                                                                  String countryCode, boolean checkUser, String currentTime) {
        return mainActivityMapper.findActivityByEquipmentModel(equipmentModel, languageCode, num, groupsByUserId, stateCode, countryCode, checkUser, currentTime);
    }

    @Override
    @Cacheable(value = "MainActivity:findCacheById", key = "#activityId")
    public MainActivity findCacheById(Long activityId) {
        return findById(activityId);
    }

    @Override
    public MainActivity findOne(MainActivityQuery query) {
        QueryWrapper<MainActivity> queryWrapper = QueryWrapperBuilder.build(query, MainActivity.class);
        return mainActivityMapper.selectOne(queryWrapper,false);
    }

    @Override
    public List<MainActivity> findEndNoSendAwardFeeChallengeActivity() {
        return mainActivityMapper.findEndNoSendAwardFeeChallengeActivity();
    }

    @Override
    public List<Long> findProActivityIdsByTime(ZonedDateTime startTime, ZonedDateTime endTime) {
        return mainActivityMapper.findProActivityIdsByTime(startTime, endTime);
    }
}
