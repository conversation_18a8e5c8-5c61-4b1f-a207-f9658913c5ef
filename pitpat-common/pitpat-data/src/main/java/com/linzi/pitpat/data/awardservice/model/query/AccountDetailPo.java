package com.linzi.pitpat.data.awardservice.model.query;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Data
@NoArgsConstructor
public class AccountDetailPo extends PageQuery {
    /**
     * 邮箱
     */
    private String emailAddress;
    /**
     * 交易状态：0未转账，2已转账
     */
    private Integer tradeStatus;
    /**
     * 审核状态: 0未审核，1已拒绝，2已同意
     */
    private Integer auditStatus;
    /**
     * 异常状态：0正常，1异常
     */
    private Integer abnormalStatus;
    /**
     * 申请交易开始时间
     */
    private ZonedDateTime createStartTime;
    /**
     * 申请交易结束时间
     */
    private ZonedDateTime createEndTime;
    /**
     * 转账成功开始时间
     */
    private ZonedDateTime tradeSuccessStartTime;
    /**
     * 转账成功结束时间
     */
    private ZonedDateTime tradeSuccessEndTime;
    /**
     * 排序字段
     */
    private String orderFiled;
    /**
     * 排序类型，desc 降序， asc 升序
     */
    private String orderType;

    /**
     * 货币code，USD：美元，CAD：加币，EUR：欧元，GBP：英镑
     *
     * @see I18nConstant.CurrencyCodeEnum
     */
    private String currencyCode;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * id列表
     */
    private List<Long> idList;
}
