package com.linzi.pitpat.data.courseservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 课程类目表
 *
 * <AUTHOR>
 * @since 2023-02-24
 */

@Data
@NoArgsConstructor
@TableName("zns_course_category")
public class CourseCategory implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.courseservice.model.entity.CourseCategory:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                             // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";                // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";            // 创建时间
    public final static String creator_ = CLASS_NAME + "creator";                   // 创建人
    public final static String modify_time = CLASS_NAME + "modify_time";            // 最后修改时间
    public final static String modified_ = CLASS_NAME + "modified";                 // 修改人
    public final static String category_name = CLASS_NAME + "category_name";        // 类目名称
    public final static String status_ = CLASS_NAME + "status";                     // 状态，0:禁用，1:启用
    public final static String app_version_min = CLASS_NAME + "app_version_min";    // app最低版本号
    public final static String app_version_max = CLASS_NAME + "app_version_max";    // app最大版本号
    public final static String sort_ = CLASS_NAME + "sort";                         // 排序
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //创建人
    private String creator;
    //最后修改时间
    private ZonedDateTime modifyTime;
    //修改人
    private String modified;
    //类目名称
    private String categoryName;
    //法语类目名称
    private String categoryNameFr;

    private String icon;
    //状态，0:禁用，1:启用
    private Integer status;
    //排序
    private Integer sort;
    /**
     * 列表icon
     */
    private String iconListing;

    @Override
    public String toString() {
        return "CourseCategory{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",createTime=" + createTime +
                ",creator=" + creator +
                ",modifyTime=" + modifyTime +
                ",modified=" + modified +
                ",categoryName=" + categoryName +
                ",status=" + status +
                ",sort=" + sort +
                "}";
    }
}
