package com.linzi.pitpat.data.courseservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 用户ai基础信息
 *
 * <AUTHOR>
 * @since 2023-05-25
 */

@Data
@NoArgsConstructor
@TableName("zns_user_ai_baseinfo")
public class UserAiBaseinfo implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.courseservice.model.entity.UserAiBaseinfo:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                         // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";            // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";          // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";      // 最后修改时间
    public final static String user_id = CLASS_NAME + "user_id";                // 用户id
    public final static String height_ = CLASS_NAME + "height";                 // 身高
    public final static String weight_ = CLASS_NAME + "weight";                 // 体重
    public final static String fitness_goals = CLASS_NAME + "fitness_goals";    // 健身目的 1 休闲养生 2 强身健体 3 激发潜能
    public final static String exercise_type = CLASS_NAME + "exercise_type";    // 运动类型 1 走步 2 跑步
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //用户id
    private Long userId;
    //身高
    private BigDecimal height;
    //体重
    private Integer weight;
    //健身目的 1 休闲养生 2 强身健体 3 激发潜能
    private Integer fitnessGoals;
    //运动类型 1 走步 2 跑步
    private Integer exerciseType;
    //身高字符串，12进制
    private String heightStr;

    @Override
    public String toString() {
        return "UserAiBaseinfo{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",userId=" + userId +
                ",height=" + height +
                ",weight=" + weight +
                ",fitnessGoals=" + fitnessGoals +
                ",exerciseType=" + exerciseType +
                "}";
    }
}
