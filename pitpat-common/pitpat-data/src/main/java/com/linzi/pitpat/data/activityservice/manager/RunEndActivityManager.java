package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonBizService;
import com.linzi.pitpat.data.activityservice.biz.ProActivityTempRankBizService;
import com.linzi.pitpat.data.activityservice.biz.RunCheatBizService;
import com.linzi.pitpat.data.activityservice.biz.UserRunOptimalBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.CheatTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.DetailsCheatStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.dto.CheatRiskLogDto;
import com.linzi.pitpat.data.activityservice.model.dto.UpdateUserRecordDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataDetailsCheat;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsCheatQuery;
import com.linzi.pitpat.data.activityservice.model.request.RiskCheatResultRequestDto;
import com.linzi.pitpat.data.activityservice.model.resp.RiskCheatResponseDto;
import com.linzi.pitpat.data.activityservice.model.vo.RunEndActivityVo;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.UserRunOptimalRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.clubservice.manager.ClubManager;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityRoomRelationDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMember;
import com.linzi.pitpat.data.clubservice.model.entity.ClubRunDataDo;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityRoomRelationQuery;
import com.linzi.pitpat.data.clubservice.service.ClubActivityRoomRelationService;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubRunDataService;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.mongo.Id.SnowflakeId;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserExpLevelBizService;
import com.linzi.pitpat.data.userservice.dto.event.UserExpSendEvent;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.bo.UserExpIncreDto;
import com.linzi.pitpat.data.userservice.model.query.UserExpConfigQuery;
import com.linzi.pitpat.data.userservice.service.UserExpConfigService;
import com.linzi.pitpat.data.userservice.service.UserExpDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 跑步结束活动相关处理类
 * @date 2025/2/13
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunEndActivityManager {
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityService runActivityService;
    private final ActivityStrategyContext activityStrategyContext;
    private final RunActivityUserTaskManager runActivityUserTaskManager;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final ActivityResultManager activityResultManager;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final UserRunOptimalRecordService userRunOptimalRecordService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final ISysConfigService sysConfigService;
    private final ZnsUserService znsUserService;
    private final UserExpConfigService userExpConfigService;
    private final UserExpDetailService userExpDetailService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final RunCheatBizService runCheatBizService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final UserExpLevelBizService userExpLevelBizService;
    private final MongoTemplate mongoTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ActivityTeamService activityTeamService;
    @Resource
    private ClubActivityRoomRelationService clubActivityRoomRelationService;
    @Resource
    private ClubRunDataService clubRunDataService;
    @Resource
    private ClubMemberService clubMemberService;
    @Resource
    private ClubManager clubManager;
    @Resource
    private ClubActivityTeamService clubActivityTeamService;
    @Resource
    private ZnsRunActivityUserService znsRunActivityUserService;

    private final UserRunOptimalBizService userRunOptimalBizService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final QueueMessageService queueMessageService;

    private final ProActivityTempRankBizService proActivityTempRankBizService;

    /**
     * @param runData
     * @param activityId
     * @param userRunDataDetail
     * @param activityTypeDto
     * @param ignoreActivityStatus
     * @return void
     * @description 跑步结束活动数据处理
     * <AUTHOR>
     * @date 2025/2/13
     */
    public void dealRunEndActivity(RunDataRequest runData, Long activityId, ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityTypeDto, boolean ignoreActivityStatus) {
        log.info("跑步结束活动处理,detailsId:" + userRunDataDetail.getId());
        if (Objects.nonNull(activityTypeDto) && MainActivityTypeEnum.OLD.getType().equals(activityTypeDto.getMainType())) {
            ZnsRunActivityEntity activityEntity = null;
            if (Objects.nonNull(activityId) && activityId > 0) {
                activityEntity = runActivityService.findById(activityId);
            }
            // 运动时间小于60秒，则开始处理数据
            if (userRunDataDetail.getIsDelete() == 1 || userRunDataDetail.getRunTime() < 60) {
                log.error("运动结束数据处理失败，运动时间小于60");
                //离线pk处理
                List<Integer> typeList = RunActivitySubTypeEnum.getPkType();
                if (Objects.nonNull(activityEntity) && typeList.contains(activityEntity.getActivityTypeSub())) {
                    //更新活动表
                    activityStrategyContext.handleActivityFinished(activityEntity);
                }
                //新pk也处理
                if (Objects.nonNull(activityEntity) && RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityEntity.getActivityType())) {
                    //更新活动表
                    activityStrategyContext.handleActivityRunDateEnd(userRunDataDetail, activityEntity, runData.getActivityUserEnd());
                }
                return;
            }

            log.info("RunDataEndListener userRunDataDetail is : " + JsonUtil.writeString(userRunDataDetail));
            //任务处理
            runActivityUserTaskManager.dealUserTask(runData, userRunDataDetail, activityEntity);
            userRunDataDetailsService.update(userRunDataDetail);
            log.info("RunDataEndListener 新人任务处理结束");
            //更新活动表
            activityStrategyContext.handleActivityRunDateEnd(userRunDataDetail, activityEntity, runData.getActivityUserEnd());
        }

        //累计跑
        activityStrategyContext.handleCumulativeRunEnd(userRunDataDetail, runData.getActivityUserEnd());
        log.info("RunDataEndListener 活动表更新结束");
        MainRunActivityRelationDo relationDo = mainRunActivityRelationService.findByQuery(MainRunActivityRelationQuery.builder().runActivityId(activityId).build());
        Boolean isClubEvent = Boolean.FALSE;
        if (Objects.nonNull(relationDo)) {
            //不为空表示是俱乐部团赛用户赛
            isClubEvent = Boolean.TRUE;
        }
        //跑步结束处理-新活动处理
        activityResultManager.runEnd(userRunDataDetail, activityTypeDto, true, isClubEvent, ignoreActivityStatus);

        //真人用户数据更新保存
        realPersonRunDataDetailsService.updateRealPersonDetails(userRunDataDetail);

        if (Objects.equals(userRunDataDetail.getIsDelete(), 0)
                && (Objects.equals(userRunDataDetail.getDataSource(), 0) || Objects.equals(userRunDataDetail.getDataSource(), 3))
                && sysConfigService.enableUserNewLevel(userRunDataDetail.getUserId())) {
            // app跑步，跑步加经验
            handleUserExpLevel(userRunDataDetail);
        }

        //最佳配速创建更新
        UpdateUserRecordDto recordDto = UpdateUserRecordDto.builder()
                .deviceType(userRunDataDetail.getDeviceType()).runDataDetailsId(userRunDataDetail.getId())
                .distanceTarget(userRunDataDetail.getDistanceTarget()).averagePace(userRunDataDetail.getAveragePace())
                .runMileage(userRunDataDetail.getRunMileage()).runTime(userRunDataDetail.getRunTime())
                .userId(userRunDataDetail.getUserId()).isTest(userRunDataDetail.getIsTest())
                .isRobot(userRunDataDetail.getIsRobot()).isCheat(userRunDataDetail.getIsCheat()).build();
        userRunOptimalBizService.updateUserRecord(recordDto);

        //俱乐部团赛数据累计
        clubTeamDeal(userRunDataDetail);
        //职业赛中途更新任务写入
        proActivityTempRankBizService.saveTempRankTask(activityId);
    }

    /**
     * 跑步结束处理-风控超时
     *
     * @param runEndActivityVo
     */
    public void riskTimeout(RunEndActivityVo runEndActivityVo) {
        ZnsUserRunDataDetailsEntity userRunDataDetail = runEndActivityVo.getUserRunDataDetail();
        // 查询是否已经处理过
        UserRunDataDetailsCheat detailsCheat = userRunDataDetailsCheatService.findOne(new UserRunDataDetailsCheatQuery().setRunDataDetailsId(userRunDataDetail.getId()).setCheatType(CheatTypeEnum.RISK.getType()));
        if (Objects.isNull(detailsCheat)) {
            log.info("riskTimeout end, 无作弊检测中数据");
            return;
        }

        dealRiskResult(userRunDataDetail, runEndActivityVo.getActivityTypeDto(), false, detailsCheat);
    }

    /**
     * 风控结果
     *
     * @param requestDto
     */
    public void riskResult(RiskCheatResultRequestDto requestDto) {
        UserRunDataDetailsCheat detailsCheat = userRunDataDetailsCheatService.findOne(new UserRunDataDetailsCheatQuery().setRunDataDetailsId(requestDto.getRunId()).setCheatType(CheatTypeEnum.RISK.getType()));
        ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.findById(requestDto.getRunId());
        if (Objects.isNull(userRunDataDetail)) {
            log.info("riskResult end, 无跑步数据");
            return;
        }
        if (Objects.isNull(detailsCheat)) {
            log.info("riskResult end, 无作弊检测中数据");
            userRunDataDetailsCheatService.save(new UserRunDataDetailsCheat().setRunDataDetailsId(requestDto.getRunId()).setCheatType(CheatTypeEnum.RISK.getType())
                    .setIsCheat(requestDto.isCheat() ? 1 : 0).setUserId(userRunDataDetail.getUserId()).setOtherInfo(requestDto.getCheatMsg()));
            return;
        }

        RiskCheatResponseDto riskCheatResponseDto = new RiskCheatResponseDto();
        riskCheatResponseDto.set_cheat(requestDto.isCheat());
        riskCheatResponseDto.setCheat_msg(requestDto.getCheatMsg());
        Query query = Query.query(Criteria.where("run_id").is(userRunDataDetail.getId()).and("msg").is("风控作弊判定结果"));//.and("runTime").is(runTime).and("isDelete").is(0)
        CheatRiskLogDto riskLogDto = mongoTemplate.findOne(query, CheatRiskLogDto.class, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
        if (Objects.isNull(riskLogDto)) {
            CheatRiskLogDto cheatRiskLogDto = new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                    .setMsg("风控作弊判定结果").setData(JsonUtil.writeString(riskCheatResponseDto));
            cheatRiskLogDto.setId(SnowflakeId.getId());
            mongoTemplate.save(cheatRiskLogDto, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
        } else {
            Update update = new Update().set("data", JsonUtil.writeString(riskCheatResponseDto));
            mongoTemplate.updateFirst(query, update, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
        }
        ActivityTypeDto activityTypeDto = null;
        Long activityId = userRunDataDetail.getActivityId();
        if (Objects.nonNull(activityId) && activityId > 0) {
            activityTypeDto = runActivityService.getActivityNew(activityId);
            String activityTitle = activityDisseminateBizService.findActivityTitle(activityId, userRunDataDetail.getUserId(), activityTypeDto.getMainType());
            activityTypeDto.setActivityTitle(activityTitle);
        }
        detailsCheat.setOtherInfo(requestDto.getCheatMsg());
        dealRiskResult(userRunDataDetail, activityTypeDto, requestDto.isCheat(), detailsCheat);
    }

    /**
     * 处理风控结果
     *
     * @param userRunDataDetail
     * @param activityTypeDto
     * @param cheat
     * @param detailsCheat
     */
    private void dealRiskResult(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityTypeDto, boolean cheat, UserRunDataDetailsCheat detailsCheat) {
        Integer oldIsCheat = detailsCheat.getIsCheat();
        if (DetailsCheatStateEnum.hasCheatResult(oldIsCheat)) {
            log.info("dealRiskResult end, 已经有作弊结果，无需处理，detailsId:{},cheat:{}", detailsCheat.getRunDataDetailsId(), detailsCheat.getIsCheat());
            return;
        }

        detailsCheat.setIsCheat(cheat ? DetailsCheatStateEnum.CHEAT.getState() : DetailsCheatStateEnum.NO_CHEAT.getState());
        userRunDataDetailsCheatService.update(detailsCheat);

        if (!DetailsCheatStateEnum.RISK.getState().equals(oldIsCheat)) {
            log.info("dealRiskResult end, 作弊结果已被手动处理，无需处理，detailsId:{},cheat:{}", detailsCheat.getRunDataDetailsId(), detailsCheat.getIsCheat());
            return;
        }

        // 作弊处理
        if (cheat) {
            userRunDataDetail.setIsCheat(1);
            ZnsUserRunDataDetailsEntity cheatUpdate = new ZnsUserRunDataDetailsEntity();
            cheatUpdate.setId(userRunDataDetail.getId());
            cheatUpdate.setIsCheat(1);
            userRunDataDetailsService.update(cheatUpdate);

            runCheatBizService.cheatPush(userRunDataDetail, activityTypeDto);
        }

        RunDataRequest runData = new RunDataRequest();
        runData.setActivityUserEnd(1);
        dealRunEndActivity(runData, userRunDataDetail.getActivityId(), userRunDataDetail, activityTypeDto, true);
    }

    private void handleUserExpLevel(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        if (userRunDataDetail.getIsCheat() == 1) {
            // 作弊不发放里程经验和赛事经验
            return;
        }

        Long runDataDetailId = userRunDataDetail.getId();
        Long userId = userRunDataDetail.getUserId();
        Integer memberType = znsUserService.findById(userId).getMemberType();
        UserExpConfigQuery userExpConfigQuery = UserExpConfigQuery.builder().obtainTypeList(List.of(UserExpObtainTypeEnum.EVENT_EXPERIENCE.getCode())).build();
        List<UserExpConfig> list = userExpConfigService.findList(userExpConfigQuery);

        UserExpIncreDto incrExperience = new UserExpIncreDto();
        UserExpConfig userExpConfig = userExpConfigService.findByObtainTypAndValue(UserExpObtainTypeEnum.RUNNING_MILEAGE.getCode(), userRunDataDetail.getRunMileage().intValue());
        if (Objects.nonNull(userExpConfig)) {
            log.info("按跑步里程增加经验，detailId:{}", runDataDetailId);
            incrExperience = userExpDetailService.calculateExpAndInsertDetail(runDataDetailId, userId, memberType, incrExperience, userExpConfig);
        }
        boolean isCompetitive = competitiveSeasonBizService.isCompetitiveActivity(userRunDataDetail.getActivityId());
        //是否道具赛
        boolean isProp = false;
        if (userRunDataDetail.getActivityId() > 0) {
            MainActivity byId = mainActivityService.findById(userRunDataDetail.getActivityId());
            if (MainActivityTypeEnum.PROP.getType().equals(byId.getMainType())) isProp = true;
        }
        UserExpConfig eventExpConfig;
        if (RunActivityTypeEnum.FREE_RUNNING.getType().equals(userRunDataDetail.getUnActivityType())) {
            // 自由跑
            eventExpConfig = list.stream().filter(e -> UserExpObtainSubTypeEnum.FREE_RUN.getCode().equals(e.getObtainSubType())).findFirst().get();
        } else if (isCompetitive) {
            // 大奖赛
            eventExpConfig = list.stream().filter(e -> UserExpObtainSubTypeEnum.GRAND_PRIX.getCode().equals(e.getObtainSubType())).findFirst().get();
            eventExpConfig = checkEventExpConfig(userRunDataDetail, userId, eventExpConfig);
        } else {
            //日常赛
            eventExpConfig = list.stream().filter(e -> UserExpObtainSubTypeEnum.DAILY_RACE.getCode().equals(e.getObtainSubType())).findFirst().get();
            eventExpConfig = checkEventExpConfig(userRunDataDetail, userId, eventExpConfig);
        }
        if (isProp) {
            //道具赛过滤
            eventExpConfig = null;
        }
        incrExperience = userExpDetailService.calculateExpAndInsertDetail(runDataDetailId, userId, memberType, incrExperience, eventExpConfig);

        if (incrExperience.getNewExperience() > 0) {
            userExpLevelBizService.sendUserExpAndAward(userId, incrExperience.getNewExperience());
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserExpSendEvent.getEventType(),UserExpSendEvent.of(userId, incrExperience));
        }
    }

    private UserExpConfig checkEventExpConfig(ZnsUserRunDataDetailsEntity userRunDataDetail, Long userId, UserExpConfig eventExpConfig) {
        if (userRunDataDetail.getActivityId() > 1) {
            MainActivity mainActivity = mainActivityService.findById(userRunDataDetail.getActivityId());

            if (!Objects.equals(mainActivity.getTargetType(), 0)
                    && !isTargetCompleted(userRunDataDetail)) {
                eventExpConfig = null;
            }
        } else if (Objects.equals(userRunDataDetail.getUnActivityType(), -3)
                && !isTargetCompleted(userRunDataDetail)) {
            eventExpConfig = null;
        }

        return eventExpConfig;
    }


    private boolean isTargetCompleted(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        // 判断目标距离是否完成
        if (userRunDataDetail.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0
                && userRunDataDetail.getRunMileage().compareTo(userRunDataDetail.getDistanceTarget()) < 0) {
            return false;
        }

        // 判断目标时间是否完成
        if (userRunDataDetail.getTimeTarget() > 0
                && userRunDataDetail.getRunTime() < userRunDataDetail.getTimeTarget()) {
            return false;
        }

        return true;
    }

    private void clubTeamDeal(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        if (userRunDataDetail.getActivityId() < 1) {
            return;
        }
        //判断是不是俱乐部团赛
        List<ActivityTeam> teamList = activityTeamService.findByActId(userRunDataDetail.getActivityId());
        //俱乐部用户赛
        List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().runActivityId(userRunDataDetail.getActivityId()).build());
        ClubActivityRoomRelationDo clubActivityRoomRelationDo = clubActivityRoomRelationService.findByQuery(ClubActivityRoomRelationQuery.builder().subActivityId(userRunDataDetail.getActivityId()).build());
        // 数据重复上传判断
        String key = RedisConstants.CLUB_EVEN_CUMULATIVE_MILEAGE + userRunDataDetail.getId();
        boolean exists = redissonClient.getBucket(key).isExists();
        if ((!CollectionUtils.isEmpty(teamList) || !CollectionUtils.isEmpty(list) || Objects.nonNull(clubActivityRoomRelationDo)) && !exists) {
            log.info("开始累计俱乐部里程，activityId:{},runMileage:{}", userRunDataDetail.getActivityId(), userRunDataDetail.getRunMileage());
            ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
            if (Objects.isNull(activityUser)) return;
            Boolean isComplete = checkIsComplete(userRunDataDetail);
            Optional<ClubActivityTeam> clubActivityTeam = clubActivityTeamService.findByTeamId(activityUser.getTeamId());
            //俱乐部团队赛累计里程
            if (clubActivityTeam.isPresent()) {
                //团赛用户赛
                Long clubId = clubActivityTeam.get().getClubId();
                Optional<ClubMember> byClubAndUserId = clubMemberService.findByClubAndUserId(clubId, userRunDataDetail.getUserId());
                if (byClubAndUserId.isPresent()) {
                    ClubRunDataDo clubRunDataDo = clubRunDataService.addRunMileageAndRunCount(clubId, userRunDataDetail.getRunMileage(), isComplete);
                    clubManager.checkClubLevelUpgrade(clubId, clubRunDataDo);
                }
            } else if (Objects.nonNull(clubActivityRoomRelationDo)) {
                //用户赛
                Long clubId = clubActivityRoomRelationDo.getClubId();
                Optional<ClubMember> byClubAndUserId = clubMemberService.findByClubAndUserId(clubId, userRunDataDetail.getUserId());
                if (byClubAndUserId.isPresent()) {
                    ClubRunDataDo clubRunDataDo = clubRunDataService.addRunMileageAndRunCount(clubId, userRunDataDetail.getRunMileage(), isComplete);
                    clubManager.checkClubLevelUpgrade(clubId, clubRunDataDo);
                }
            }
            redissonClient.getBucket(key).set("1", 1, TimeUnit.HOURS);
        }
    }

    private Boolean checkIsComplete(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        Boolean isComplete = Boolean.FALSE;
        if (userRunDataDetail.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0 && userRunDataDetail.getRunMileage().compareTo(userRunDataDetail.getDistanceTarget()) >= 0) {
            isComplete = Boolean.TRUE;
            return isComplete;
        }
        if (userRunDataDetail.getTimeTarget() > 0 && userRunDataDetail.getRunTime() >= userRunDataDetail.getTimeTarget()) {
            isComplete = Boolean.TRUE;
            return isComplete;
        }
        if (userRunDataDetail.getDistanceTarget().compareTo(BigDecimal.ZERO) <= 0 && userRunDataDetail.getTimeTarget() <= 0) {
            isComplete = Boolean.TRUE;
        }
        return isComplete;
    }
}
