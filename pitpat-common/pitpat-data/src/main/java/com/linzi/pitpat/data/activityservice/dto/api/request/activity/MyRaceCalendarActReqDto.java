package com.linzi.pitpat.data.activityservice.dto.api.request.activity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:43
 */
@Data
@NoArgsConstructor
public class MyRaceCalendarActReqDto {
    /**
     * 查询开始时间
     */
    private ZonedDateTime startTime;
    /**
     * 查询结束时间
     */
    private ZonedDateTime endTime;
    /**
     * 是否测试用户
     */
    private boolean testUser;
    /**
     * 时区
     */
    private TimeZone timeZone;
    /**
     * 是否审核中
     */
    private boolean isAuditing;
    /**
     * app版本
     */
    private Integer appVersion;
    /**
     * 用户id
     */
    private Long userId;

    public MyRaceCalendarActReqDto(ZonedDateTime startTime, ZonedDateTime endTime, TimeZone timeZone, boolean isAuditing, Long userId) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.timeZone = timeZone;
        this.isAuditing = isAuditing;
        this.userId = userId;
    }
}
