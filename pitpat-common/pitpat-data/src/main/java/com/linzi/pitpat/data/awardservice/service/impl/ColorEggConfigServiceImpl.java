package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 彩蛋配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-18
 */

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.data.awardservice.mapper.ColorEggConfigDao;
import com.linzi.pitpat.data.awardservice.model.entry.ColorEggConfig;
import com.linzi.pitpat.data.awardservice.model.vo.ColorEggConfigVo;
import com.linzi.pitpat.data.awardservice.service.ColorEggConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;

@Service
public class ColorEggConfigServiceImpl extends ServiceImpl<ColorEggConfigDao, ColorEggConfig> implements ColorEggConfigService {


    @Autowired
    private ColorEggConfigDao colorEggConfigDao;


    @Override
    public ColorEggConfig selectColorEggConfigById(Long id) {
        return colorEggConfigDao.selectColorEggConfigById(id);
    }

    @Override
    public List<ColorEggConfigVo> selectColorEggConfigByStatusGmtStartGmtEnd(Integer status, ZonedDateTime gmtStart, ZonedDateTime gmtEnd) {
        return colorEggConfigDao.selectColorEggConfigByStatusGmtStartGmtEnd(status, gmtStart, gmtEnd);
    }


}
