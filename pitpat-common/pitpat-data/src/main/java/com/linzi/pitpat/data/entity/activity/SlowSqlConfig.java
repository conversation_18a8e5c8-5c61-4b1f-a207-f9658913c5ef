package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*慢SQL配置表
 *
 * <AUTHOR>
 * @since 2022-10-11
 */

@Data
@NoArgsConstructor
@TableName("zns_slow_sql_config")
public class SlowSqlConfig implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //mybatis的mapper_id
    private String mapperName;
    //容忍时间
    private Integer bearSecond;

    @Override
    public String toString() {
        return "SlowSqlConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",mapperName=" + mapperName +
                ",bearSecond=" + bearSecond +
                "}";
    }
}
