package com.linzi.pitpat.data.activityservice.dto.api.response;

import com.linzi.pitpat.data.activityservice.enums.FreeRoomStatusEnum;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 免费房间数据传输对象
 * 
 * @since 2025年1月
 */
@Data
public class FreeRoomRespDto {

    /**
     * 房间编号
     */
    private Long roomNumber;

    // 房间状态 0 等待中 1 进行中 2 已结束
    private Integer roomStatus;

}
