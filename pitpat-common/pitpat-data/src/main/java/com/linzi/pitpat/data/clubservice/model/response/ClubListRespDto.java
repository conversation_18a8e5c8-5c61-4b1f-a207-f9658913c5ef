package com.linzi.pitpat.data.clubservice.model.response;

import com.linzi.pitpat.data.annotation.Desensitized;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubStateEnum;
import com.linzi.pitpat.data.enums.SensitiveTypeEnum;
import com.linzi.pitpat.data.filler.ClubLevelDataFiller;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.user.UserEmailAddressDataFiller;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class ClubListRespDto {
    private Long id;
    private Long clubId;
    //修改时间
    private ZonedDateTime gmtModified;
    //俱乐部名称
    private String name;
    //俱乐部LOGO
    private String logo;
    //俱乐部负责人
    private Long ownerUserId;
    /**
     * 负责人邮箱地址
     */
    @Filler(relationFieldName = "ownerUserId", filler = UserEmailAddressDataFiller.class)
    @Desensitized(type = SensitiveTypeEnum.EMAIL)
    private String ownerEmailAddress;
    //俱乐部会员人数
    private Integer memberCount;
    //俱乐部参赛次数
    private Integer matchCount;
    //俱乐部等级
    private String clubLevel;
    //俱乐部等级名称
    @Filler(relationFieldName = "clubLevel", filler = ClubLevelDataFiller.class)
    private String clubLevelName;
    /**
     * //俱乐部状态 normal（正常）, frozen（冻结）, disbanded（解散）
     *
     * @see ClubStateEnum
     */
    private String state;

    //俱乐部操作备注
    private String operateNote;

    //是否推荐(0:不推荐，1：推荐)
    private Integer isRecommend;

    public Long getClubId() {
        return id;
    }

    public void setClubId(Long clubId) {
        this.id = clubId;
    }
}
