package com.linzi.pitpat.data.activityservice.manager;

import com.google.common.collect.Maps;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.EggActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityShelfEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.dto.PacerDto;
import com.linzi.pitpat.data.activityservice.model.dto.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponReceive;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRateLimit;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityShowUser;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRateLimitQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.RunActivitySaveOrUpdateReq;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityShelfUpdateRequest;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityConfigResp;
import com.linzi.pitpat.data.activityservice.model.resp.LevelDetailResp;
import com.linzi.pitpat.data.activityservice.model.resp.TaskResp;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityWearCacheInfo;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponReceiveService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityPropConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.PacerConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityCheatService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.RunActivityRuleUserService;
import com.linzi.pitpat.data.activityservice.service.RunActivityShowUserService;
import com.linzi.pitpat.data.activityservice.service.RunMilestoneStageConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.awardservice.biz.ActivityUserScoreBizService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.vo.WearAward;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.entity.dto.AutomaticAdmissionDealDto;
import com.linzi.pitpat.data.entity.dto.DelayDto;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityClassifyTypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivitySubstateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.mapstruct.ObjectConvertMapper;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.systemservice.model.query.OperationalActivityQuery;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 旧活动过程处理类
 *
 * <AUTHOR>
 * @date 2024/7/20 6:29
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunActivityProcessManager {
    private final ZnsUserService userService;
    private final MindUserMatchService mindUserMatchService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsRunActivityService runActivityService;
    private final UserCouponService userCouponService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsRunRouteService runRouteService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final AppMessageService appMessageService;
    private final OperationalActivityService operationalActivityService;
    private final RunMilestoneStageConfigService runMilestoneStageConfigService;
    private final ActivityPropConfigService activityPropConfigService;
    private final ActivityIDGenerateService activityIDGenerateService;
    private final MainActivityService mainActivityService;
    private final PacerConfigService pacerConfigService;
    private final ActivityEquipmentConfigService activityEquipmentConfigService;
    private final ActivityCouponReceiveService activityCouponReceivesService;
    private final RunActivityCheatService runActivityCheatService;
    private final RunActivityMedalService runActivityMedalService;
    private final ActivityRateLimitService activityRateLimitService;
    private final ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    private final RunActivityShowUserService runActivityShowUserService;
    private final RunActivityRuleUserService runActivityRuleUserService;

    private final MindUserMatchBizService mindUserMatchBizService;
    private final ActivityUserScoreBizService activityUserScoreBizService;
    private final EggActivityBizService eggActivityBizService;

    private final RabbitTemplate rabbitTemplate;
    private final RedissonClient redissonClient;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;
    @Value("${zns.config.rabbitQueue.delay_exchange_name}")
    private String delay_exchange_name;

    @Value("${admin.server.gamepush}")
    private String gameDomain;

    /**
     * 保存/更新活动赛事
     *
     * @param req
     * @param username
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateActivity(RunActivitySaveOrUpdateReq req, String username) {
        // 参数验证
        Result<Void> error = validateRequest(req);
        if (error != null) {
            return error;
        }
        ZnsRunActivityEntity runActivityEntity = ObjectConvertMapper.INSTANCE.toRunActivityEntity(req);

        runActivityEntity.setBonusRuleType(req.getBonusRuleType());
        runActivityEntity.setActivityEntryScore(req.getActivityEntryScore());
        runActivityEntity.setFinishedCertificate(req.getFinishedCertificate());
        runActivityEntity.setPrivilegeBrand(req.getPrivilegeBrand());
        if (!CollectionUtils.isEmpty(req.getBrandRightsInterests())) {
            if (req.getBrandRightsInterests().contains(0)) {
                runActivityEntity.setBrandRightsInterests(0);
            } else {
                runActivityEntity.setBrandRightsInterests(-1);
            }
            if (req.getBrandRightsInterests().contains(1)) {
                runActivityEntity.setBrandRightsInterestsAward(1);
            } else {
                runActivityEntity.setBrandRightsInterestsAward(-1);
            }
        }
        runActivityEntity.setAssistActivityId(req.getAssistActivityId());
        runActivityEntity.setAssistActivityLinkPeriod(req.getAssistActivityLinkPeriod());
        runActivityEntity.setCompleteRuleType(1);
        runActivityEntity.setRoomId(req.getRoomId());
        Long id = req.getId();

        List<ZnsRunRouteEntity> routeList = runRouteService.findList();
        Map<Long, Integer> routeId2TypeMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(routeList)) {
            routeId2TypeMap = routeList.stream().collect(Collectors.toMap(ZnsRunRouteEntity::getId, ZnsRunRouteEntity::getRouteType));
        }

        // 处理任务
        List<LevelDetailResp> levelDetailList = req.getLevelDetailList();
        ActivityConfigResp activityConfigResp = ObjectConvertMapper.INSTANCE.toActivityConfigResp(req);
        List<TaskResp> tasks = new ArrayList<>();
        for (LevelDetailResp levelDetailResp : levelDetailList) {
            TaskResp taskResp = toTaskResp(levelDetailResp);
            taskResp.setRouteType(routeId2TypeMap.get(levelDetailResp.getRouteId()));
            taskResp.setTaskType(3);
            tasks.add(taskResp);
        }
        activityConfigResp.setTasks(tasks);
        activityConfigResp.setFinishAwardImageUrl(req.getFinishAwardImageUrl());
        activityConfigResp.setFinishIconImageUrl(req.getFinishIconImageUrl());
        activityConfigResp.setFinishRankWinnerPodiumImageUrl(req.getFinishRankWinnerPodiumImageUrl());
        activityConfigResp.setFinishAward(req.getFinishAward());
        activityConfigResp.setRankAwardLists(req.getRankAwardLists());
        runActivityEntity.setActivityConfig(JsonUtil.writeString(activityConfigResp));
        runActivityEntity.setChallengePacerShow(req.getChallengePacerShow());
        runActivityEntity.setHasPacer(req.getHasPacer());
        runActivityEntity.setClotheType(req.getClotheType());
        runActivityEntity.setBrandingPromotionUrl(req.getBrandingPromotionUrl());
        ZonedDateTime activityStartTime = runActivityEntity.getActivityStartTime();
        ZonedDateTime activityEndTime = runActivityEntity.getActivityEndTime();
        ZonedDateTime now = ZonedDateTime.now();
        Integer targetState = 0;
        // 活动状态：0表示未开始，1表示进行中，2表示已结束，-1表示活动已取消
        if (now.isBefore(activityStartTime)) {
            targetState = 0;
        } else if (activityStartTime.isBefore(now) && now.isBefore(activityEndTime)) {
            targetState = 1;
        } else if (activityEndTime.isBefore(now)) {
            targetState = 2;
        }
        runActivityEntity.setActivityState(targetState);
        runActivityEntity.setShowMode(req.getShowMode());
        runActivityEntity.setActivityDetailsUrl(req.getActivityDetailsUrl());
        runActivityEntity.setPropSupport(req.getPropSupport());
        if (id != null) {
            // 更新
            runActivityService.updateById(runActivityEntity);
            // 更新banner表中冗余字段的值.
            OperationalActivity operationalActivity = new OperationalActivity();
            BigDecimal activityEntryFee1 = runActivityEntity.getActivityEntryFee();
            operationalActivity.setEntryFee(activityEntryFee1);
            operationalActivity.setApplicationStartTime(runActivityEntity.getApplicationStartTime());
            operationalActivity.setApplicationEndTime(runActivityEntity.getApplicationEndTime());
            if (activityEntryFee1 != null && activityEntryFee1.compareTo(BigDecimal.ZERO) > 0) {
                operationalActivity.setEntryRuleType(2);
            }
            // 更新banner的相关信息
            operationalActivityService.updateQuery(operationalActivity, OperationalActivityQuery.builder().runActivityId(id).build());
            // 里程碑 保存指定主题模式配置信息
            runMilestoneStageConfigService.updateRunMilestoneStageConfigList(req.getShowLists(), levelDetailList.size(), runActivityEntity, req.getShowMode());
            //更新服装奖励缓存
            List<LevelDetailResp> levelDetailList1 = req.getLevelDetailList();
            List<LevelDetailResp> collect = levelDetailList1.stream().filter(s -> Objects.nonNull(s.getWearAwardDto())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                wearCache(req, runActivityEntity);
            } else {
                RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
                list.iterator().forEachRemaining(info -> {
                    if (info.getActivityId().equals(req.getId())) {
                        list.remove(info);
                    }
                });
            }
            activityPropConfigService.deleteActivityPropConfigByActivityId(id);
            saveActivityPropConfig(req, username, id);
        } else {
            // 此处设置默认值为0,实际上这个字段是没有使用到的,页面上不提供.
            runActivityEntity.setActivityConfigId(0L);
            runActivityEntity.setActivityRouteId(0L);
            runActivityEntity.setActivityType(RunActivityTypeEnum.TASK_ACTIVITY.getType());
            runActivityEntity.setClassifyType(ActivityClassifyTypeEnum.THEMED_RACE.getType());
            runActivityEntity.setStatus(0);

            // 保存
            runActivityEntity.setIsNew(YesNoStatus.YES.getCode());//3.0版本创建的都设为1
            Long activityID = activityIDGenerateService.generateActivityID();
            runActivityEntity.setId(activityID);

            boolean save = 1 == runActivityService.insert(runActivityEntity);
            // 更新活动编号
            id = runActivityEntity.getId();
            if (save) {
                //保存新表
                MainActivity mainActivity = new MainActivity().setOldActivityId(id).setId(id).setMainType(MainActivityTypeEnum.OLD.getType())
                        .setOldType(runActivityEntity.getActivityType()).setActivityStartTime(DateUtil.formatDate(runActivityEntity.getActivityStartTime(), DateUtil.DATE_TIME_SHORT))
                        .setActivityEndTime(DateUtil.formatDate(runActivityEntity.getActivityEndTime(), DateUtil.DATE_TIME_SHORT))
                        .setApplicationStartTime(DateUtil.formatDate(runActivityEntity.getApplicationStartTime(), DateUtil.DATE_TIME_SHORT))
                        .setApplicationEndTime(DateUtil.formatDate(runActivityEntity.getApplicationEndTime(), DateUtil.DATE_TIME_SHORT));
                mainActivityService.insert(mainActivity);
            }
            ZnsRunActivityEntity newRunActivity = new ZnsRunActivityEntity();
            newRunActivity.setActivityNo("HD" + id);
            newRunActivity.setId(id);
            runActivityService.updateById(newRunActivity);
            if (YesNoStatus.YES.getCode().equals(req.getShowMode())) {
                // 里程碑 保存指定主题模式配置信息
                runMilestoneStageConfigService.saveRunMilestoneStageConfigList(req.getShowLists(), levelDetailList.size(), runActivityEntity);
            }
            //服装奖励存缓存
            wearCache(req, runActivityEntity);
            //保存道具数据
            saveActivityPropConfig(req, username, id);
        }
        //配速员处理
        pacerConfigService.addPacerConfig(runActivityEntity.getId(), req.getPacerDtos());
        // 保存主题活动相关的活动配置信息.不能修改,只能保存.
        activityEquipmentConfigService.updateActivityEquipmentConfig(id, req.getActivityEquipmentConfigs());
        // 更新的时候,先将之前的绑定关系删除,然后创建新的绑定关系
        activityCouponReceivesService.deleteActivityCouponReceiveByActivityId(id);
        if (req.getCouponIds() != null) {
            for (Long couponId : req.getCouponIds()) {
                saveActivityCouponConfig(couponId, id);
            }
        }
        //作弊开关处理
        runActivityCheatService.saveSwitchList(runActivityEntity.getId(), req.getCheatSwitchList());
        //处理活动勋章
        runActivityMedalService.dealActivityMedal(runActivityEntity.getId(), req.getMedalConfigId());
        return CommonResult.success();
    }

    private void saveActivityCouponConfig(Long couponId, Long runActivityEntityId) {
        // 保存本次活动可以使用优惠券信息
        if (couponId != null && couponId > 0) {
            ActivityCouponReceive activityCouponReceive = new ActivityCouponReceive();
            activityCouponReceive.setActivityId(runActivityEntityId);
            activityCouponReceive.setCouponId(couponId);
            activityCouponReceivesService.save(activityCouponReceive);
        }
    }

    /**
     * 保存活动道具配置
     *
     * @param req
     * @param username
     * @param activityId
     */
    private void saveActivityPropConfig(RunActivitySaveOrUpdateReq req, String username, Long activityId) {
        if (YesNoStatus.YES.getCode().equals(req.getPropSupport())
                && !CollectionUtils.isEmpty(req.getActivityPropDtoList())) {
            List<ActivityPropConfig> list = req.getActivityPropDtoList().stream().map(e -> {
                ActivityPropConfig entity = new ActivityPropConfig();
                entity.setActivityId(activityId);
                entity.setPropId(e.getPropId());
                entity.setTriggerLimit(e.getTriggerLimit());
                if (Objects.isNull(e.getTriggerLimit())) {
                    entity.setTriggerLimit(-1);
                }
                entity.setModifier(username);
                return entity;
            }).collect(Collectors.toList());
            activityPropConfigService.saveBatch(list);
        }
    }

    private void wearCache(RunActivitySaveOrUpdateReq req, ZnsRunActivityEntity runActivityEntity) {
        List<LevelDetailResp> levelDetailList1 = req.getLevelDetailList();
        List<WearAward> wearList = new ArrayList<>();
        for (LevelDetailResp levelDetailResp : levelDetailList1) {
            WearAwardDto wearAwardDto = levelDetailResp.getWearAwardDto();
            if (Objects.nonNull(wearAwardDto)) {
                WearAward wearAward = new WearAward();
                BeanUtils.copyProperties(wearAwardDto, wearAward);
                wearAward.setWearId(wearAwardDto.getWearValue());
                wearList.add(wearAward);
            }
        }
        if (!CollectionUtils.isEmpty(wearList)) {
            RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
            ActivityWearCacheInfo activityWearCacheInfo = new ActivityWearCacheInfo();
            activityWearCacheInfo.setActivityId(runActivityEntity.getId());
            activityWearCacheInfo.setExpireTime(runActivityEntity.getApplicationEndTime());
            activityWearCacheInfo.setWearList(wearList);
            list.iterator().forEachRemaining(info -> {
                if (info.getActivityId().equals(runActivityEntity.getId())) {
                    list.remove(info);
                }
            });
            list.add(activityWearCacheInfo);
        }
    }

    /**
     * 任务相关实体
     *
     * @param levelDetailResp
     * @return
     */
    private TaskResp toTaskResp(LevelDetailResp levelDetailResp) {
        if (levelDetailResp == null) {
            return null;
        }
        TaskResp taskResp = new TaskResp();
        taskResp.setLevel(levelDetailResp.getLevel());
        BigDecimal decimal = new BigDecimal("1600");
        taskResp.setMileageTarget(decimal.multiply(levelDetailResp.getMileageTarget()));
        taskResp.setRouteId(levelDetailResp.getRouteId());
        taskResp.setRoomId(levelDetailResp.getRoomId());
        taskResp.setSpecialEffectTheme(levelDetailResp.getSpecialEffectTheme());
        taskResp.setRunwayStyle(levelDetailResp.getRunwayStyle());
        taskResp.setCouponId(levelDetailResp.getCouponId());
        taskResp.setNum(levelDetailResp.getNum());
        taskResp.setCouponName(levelDetailResp.getCouponName());
        taskResp.setScore(levelDetailResp.getScore());
        taskResp.setPopImageUrl(levelDetailResp.getPopImageUrl());
        taskResp.setMaxConstantVelocity(levelDetailResp.getMaxConstantVelocity());
        taskResp.setPropAwardDto(levelDetailResp.getPropAwardDto());
        taskResp.setWearAwardDto(levelDetailResp.getWearAwardDto());
        return taskResp;
    }

    /**
     * 创建活动校验参数
     *
     * @param runActivitySaveOrUpdateReq
     * @return
     */
    private Result<Void> validateRequest(RunActivitySaveOrUpdateReq runActivitySaveOrUpdateReq) {
        String activityTitle = runActivitySaveOrUpdateReq.getActivityTitle();
        if (!StringUtils.hasText(activityTitle)) {
            return CommonResult.fail("活动赛事标题不能为空!");
        }
        Integer timeType = runActivitySaveOrUpdateReq.getTimeType();
        if (timeType == null) {
            return CommonResult.fail("日期类型不能为空!");
        }
        ZonedDateTime activityStartTime = runActivitySaveOrUpdateReq.getActivityStartTime();
        if (activityStartTime == null) {
            return CommonResult.fail("活动赛事开始时间不能为空!");
        }
        ZonedDateTime activityEndTime = runActivitySaveOrUpdateReq.getActivityEndTime();
        if (activityEndTime == null) {
            return CommonResult.fail("活动赛事结束时间不能为空!");
        }
        if (activityEndTime.isBefore(activityStartTime)) {
            return CommonResult.fail("活动赛事结束时间不能早于开始时间!");
        }
        ZonedDateTime applicationStartTime = runActivitySaveOrUpdateReq.getApplicationStartTime();
        if (applicationStartTime == null) {
            return CommonResult.fail("活动赛事报名开始时间不能为空!");
        }
        ZonedDateTime applicationEndTime = runActivitySaveOrUpdateReq.getApplicationEndTime();
        if (applicationEndTime == null) {
            return CommonResult.fail("活动赛事报名结束时间不能为空!");
        }
        Integer countdownDuration = runActivitySaveOrUpdateReq.getCountdownDuration();
        if (countdownDuration == null) {
            return CommonResult.fail("活动赛事关门时间不能为空!");
        }
        Integer cheatSwitch = runActivitySaveOrUpdateReq.getCheatSwitch();
        if (cheatSwitch == null) {
            return CommonResult.fail("是否开启作弊机制不能为空!");
        }
        BigDecimal activityEntryFee = runActivitySaveOrUpdateReq.getActivityEntryFee();
        if (activityEntryFee == null) {
            return CommonResult.fail("活动赛事报名费不能为空!");
        }
        if (BigDecimal.ZERO.compareTo(activityEntryFee) > 0) {
            return CommonResult.fail("活动赛事报名费不能为负!");
        }
        String themeColor = runActivitySaveOrUpdateReq.getThemeColor();
        if (!StringUtils.hasText(themeColor)) {
            return CommonResult.fail("活动赛事主题色不能为空!");
        }
        String shareProductUrl = runActivitySaveOrUpdateReq.getShareProductUrl();
        if (!StringUtils.hasText(shareProductUrl)) {
            return CommonResult.fail("活动赛事分享页商品链接不能为空!");
        }
        String shareCopyWriting = runActivitySaveOrUpdateReq.getShareCopyWriting();
        if (!StringUtils.hasText(shareCopyWriting)) {
            return CommonResult.fail("活动赛事站内信分享文案不能为空!");
        }
        String banner = runActivitySaveOrUpdateReq.getBanner();
        if (!StringUtils.hasText(banner)) {
            return CommonResult.fail("活动赛事顶部背景图不能为空!");
        }
        String bottomBackgroundImageUrl = runActivitySaveOrUpdateReq.getBottomBackgroundImageUrl();
        List<LevelDetailResp> levelDetailList = runActivitySaveOrUpdateReq.getLevelDetailList();
        if (CollectionUtils.isEmpty(levelDetailList)) {
            return CommonResult.fail("活动赛事至少有一个关卡!");
        }
        for (LevelDetailResp levelDetailResp : levelDetailList) {
            Integer level = levelDetailResp.getLevel();
            if (level == null) {
                return CommonResult.fail("活动赛事关卡等级不能为空!");
            }
            Long routeId = levelDetailResp.getRouteId();
            if (routeId == null) {
                return CommonResult.fail("第" + level + "关跑到路线ID不能为空!");
            }
            BigDecimal mileageTarget = levelDetailResp.getMileageTarget();
            if (mileageTarget == null) {
                return CommonResult.fail("第" + level + "关跑步里程不能为空!");
            }
        }
        List<ActivityEquipmentConfig> activityEquipmentConfigs = runActivitySaveOrUpdateReq.getActivityEquipmentConfigs();
        if (runActivitySaveOrUpdateReq.getId() == null && CollectionUtils.isEmpty(activityEquipmentConfigs)) {
            return CommonResult.fail("新增活动支持的设备列表不能为空!");
        }
        return null;
    }

    /**
     * 自动创建活动
     *
     * @param activityTaskConfig
     * @param currentDate
     */
    public void autoCreateActivity(ActivityTaskConfig activityTaskConfig, ZonedDateTime currentDate) {
        if (Objects.equals(activityTaskConfig.getType(), 0)) {
            ZnsRunActivityEntity activity = runActivityService.selectActivityById(activityTaskConfig.getActivityId());
            ZonedDateTime activityStartTime = DateUtil.getSameDay(activity.getActivityStartTime());
            ZonedDateTime activityEndTime = DateUtil.getSameDay(activity.getActivityEndTime());
            // 如果活动开始时间小于当前，则活动开始时间+1天
            if (activityStartTime.toInstant().toEpochMilli() <= currentDate.toInstant().toEpochMilli()) {
                activityStartTime = DateUtil.addDays(activityStartTime, 1);
                activityEndTime = DateUtil.addDays(activityEndTime, 1);
            }

            // 如果活动结束时间小于活动开始时间 ， 则活动结束时间 + 1 天
            if (activityEndTime.toInstant().toEpochMilli() <= activityStartTime.toInstant().toEpochMilli()) {
                activityEndTime = DateUtil.addDays(activityEndTime, 1);
            }
            doCreate0(activityTaskConfig, currentDate, activityStartTime, activityEndTime, null);
        } else if (Objects.equals(activityTaskConfig.getType(), 1)) {
            doCreate1(activityTaskConfig, currentDate);
        } else if (Objects.equals(activityTaskConfig.getType(), 2)) {
            doCreate2(activityTaskConfig, currentDate);
        } else if (Objects.equals(activityTaskConfig.getType(), 3)) {
            doCreate3(activityTaskConfig, currentDate);
        }
    }


    public void doCreate1(ActivityTaskConfig activityTaskConfig, ZonedDateTime currentDate) {
        ZnsRunActivityEntity activity = runActivityService.selectActivityById(activityTaskConfig.getActivityId());
        activity.setId(null);
        activity.setCreateTime(currentDate);
        activity.setModifieTime(currentDate);
        String batchNo = OrderUtil.getBatchNo();
        int i = 1;
        for (; ; ) {
            OrderUtil.addLogNo();
            try {
                ZonedDateTime tomorrow = DateUtil.addDays(currentDate, 1);
                ZonedDateTime activityStartTime = currentDate.plusMinutes(MapUtil.getInteger(activityTaskConfig.getConfig(), 10) * i);
                if (activityStartTime.toInstant().toEpochMilli() > tomorrow.toInstant().toEpochMilli()) {
                    log.info("生成时间已经到了 i=" + i
                            + ", tomorrow = " + DateUtil.formateDateStr(tomorrow, DateUtil.YYYY_MM_DD_HH_MM_SS)
                            + ", activityStartTime=" + DateUtil.formateDateStr(activityStartTime, DateUtil.YYYY_MM_DD_HH_MM_SS)
                            + ",activityTaskConfigId=" + activityTaskConfig.getId());
                    break;
                }

                log.info("生成时间中 i=" + i
                        + ", tomorrow = " + DateUtil.formateDateStr(tomorrow, DateUtil.YYYY_MM_DD_HH_MM_SS)
                        + ", activityStartTime=" + DateUtil.formateDateStr(activityStartTime, DateUtil.YYYY_MM_DD_HH_MM_SS)
                        + ",activityTaskConfigId=" + activityTaskConfig.getId());

                int startBetw = DateUtil.betweenSecond(activity.getActivityStartTime(), activity.getActivityEndTime());
                ZonedDateTime activityEndTime = DateUtil.addSeconds(activityStartTime, startBetw);
                doCreate0(activityTaskConfig, currentDate, activityStartTime, activityEndTime, batchNo);

                i++;
            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                OrderUtil.removeLogNo();
            }
        }
    }

    public void doCreate2(ActivityTaskConfig activityTaskConfig, ZonedDateTime currentDate) {
        String[] configs = activityTaskConfig.getConfig().split(",");
        ZnsRunActivityEntity activity = runActivityService.selectActivityById(activityTaskConfig.getActivityId());
        String dateStrXXX = DateUtil.formateDateStr(currentDate, DateUtil.YYYY_MM_DD);
        String batchNo = OrderUtil.getBatchNo();
        for (String config : configs) {
            try {
                OrderUtil.addLogNo();
                String date = dateStrXXX + " " + config + ":00";
                ZonedDateTime activityStartTime = DateUtil.formateDate(date, DateUtil.YYYY_MM_DD_HH_MM_SS);
                // 如果活动开始时间小于当前，则活动开始时间+1天
                if (activityStartTime.toInstant().toEpochMilli() <= currentDate.toInstant().toEpochMilli()) {
                    activityStartTime = DateUtil.addDays(activityStartTime, 1);
                }
                int startBetw = DateUtil.betweenSecond(activity.getActivityStartTime(), activity.getActivityEndTime());
                ZonedDateTime activityEndTime = DateUtil.addSeconds(activityStartTime, startBetw);
                doCreate0(activityTaskConfig, currentDate, activityStartTime, activityEndTime, batchNo);
            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                OrderUtil.removeLogNo();
            }
        }
    }


    public void doCreate3(ActivityTaskConfig activityTaskConfig, ZonedDateTime currentDate) {
        ZnsRunActivityEntity activity = runActivityService.selectActivityById(activityTaskConfig.getActivityId());
        activity.setId(null);
        activity.setCreateTime(currentDate);
        activity.setModifieTime(currentDate);
        String oldLogNo = OrderUtil.getLogNo();
        String batchNo = OrderUtil.getBatchNo();
        int i = 1;
        for (; ; ) {
            OrderUtil.addLogNo();
            try {
                log.info("旧的日志编号为 " + oldLogNo);
                ZonedDateTime startTime = DateUtil.getCurrentHHMMSS(activityTaskConfig.getTaskStartTime());
                ZonedDateTime tomorrow = DateUtil.getCurrentHHMMSS(activityTaskConfig.getTaskEndTime());

                if (tomorrow.toInstant().toEpochMilli() <= startTime.toInstant().toEpochMilli()) {
                    tomorrow = DateUtil.addDays(tomorrow, 1);
                }

                ZonedDateTime activityStartTime = startTime.plusMinutes(MapUtil.getInteger(activityTaskConfig.getConfig(), 10) * i);
                if (activityStartTime.toInstant().toEpochMilli() > tomorrow.toInstant().toEpochMilli()) {
                    log.info("生成时间已经到了 i=" + i
                            + ", tomorrow = " + DateUtil.formateDateStr(tomorrow, DateUtil.YYYY_MM_DD_HH_MM_SS)
                            + ", activityStartTime=" + DateUtil.formateDateStr(activityStartTime, DateUtil.YYYY_MM_DD_HH_MM_SS)
                            + ",activityTaskConfigId=" + activityTaskConfig.getId());
                    break;
                }

                log.info("生成时间中 i=" + i
                        + ", tomorrow = " + DateUtil.formateDateStr(tomorrow, DateUtil.YYYY_MM_DD_HH_MM_SS)
                        + ", activityStartTime=" + DateUtil.formateDateStr(activityStartTime, DateUtil.YYYY_MM_DD_HH_MM_SS)
                        + ",activityTaskConfigId=" + activityTaskConfig.getId());

                int startBetw = DateUtil.betweenSecond(activity.getActivityStartTime(), activity.getActivityEndTime());
                ZonedDateTime activityEndTime = DateUtil.addSeconds(activityStartTime, startBetw);
                doCreate0(activityTaskConfig, currentDate, activityStartTime, activityEndTime, batchNo);
                i++;
            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                OrderUtil.removeLogNo();
            }
        }
    }


    public void doCreate0(ActivityTaskConfig activityTaskConfig, ZonedDateTime currentDate, ZonedDateTime activityStartTime, ZonedDateTime activityEndTime, String batchNo) {
        ZnsRunActivityEntity activity = runActivityService.selectActivityById(activityTaskConfig.getActivityId());
        Long oldId = activity.getId();
        Long activityID = activityIDGenerateService.generateActivityID();
        activity.setId(activityID);
        activity.setCreateTime(currentDate);
        activity.setModifieTime(currentDate);
        activity.setParentId(activityTaskConfig.getActivityId());
        activity.setTaskConfigId(activityTaskConfig.getId());
        activity.setBatchNo(batchNo);
        log.info("旧的活动id为" + oldId + " activityStartTime="
                + DateUtil.formateDateStr(activity.getActivityStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS) +
                "，applicationStartTime= " + DateUtil.formateDateStr(activity.getApplicationEndTime(), DateUtil.YYYY_MM_DD_HH_MM_SS));

        log.info("doCreate 旧的活动id为" + oldId + ", activityStartTime=" + DateUtil.formateDateStr(activityStartTime, DateUtil.YYYY_MM_DD_HH_MM_SS));
        if (Objects.equals(activity.getApplicationStartLimit(), 0)) {
            activity.setApplicationStartTime(DateUtil.addSeconds(currentDate, 3 * 60));
            activity.setApplicationEndTime(DateUtil.addSeconds(activityStartTime, 30 * 60));
            log.info("applicationStartLimit为0时 "
                    + ", applicationStartTime=" + DateUtil.formateDateStr(activity.getApplicationStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS)
                    + ", applicationEndTime=" + DateUtil.formateDateStr(activity.getApplicationEndTime(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        } else {
            int endBetw = DateUtil.betweenSecond(activity.getActivityStartTime(), activity.getApplicationEndTime());
            int startBetw = DateUtil.betweenSecond(activity.getActivityStartTime(), activity.getApplicationStartTime());
            ZonedDateTime applicationStartTime = DateUtil.addSeconds(activityStartTime, startBetw);
            activity.setApplicationStartTime(applicationStartTime);
            activity.setApplicationEndTime(DateUtil.addSeconds(activityStartTime, endBetw));
            log.info(" 正常情况下2 startBetw = " + startBetw + ", endBetw = " + endBetw
                    + ", applicationStartTime=" + DateUtil.formateDateStr(activity.getApplicationStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS)
                    + ", applicationEndTime=" + DateUtil.formateDateStr(activity.getApplicationEndTime(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        }

        activity.setActivityStartTime(activityStartTime);
        activity.setActivityEndTime(activityEndTime);
        // 格式化活动开始时间和报名开始时间为整分钟
        activity.setApplicationStartTime(DateUtil.formatMinites(activity.getApplicationStartTime()));
        activity.setActivityStartTime(DateUtil.formatMinites(activity.getActivityStartTime()));
        log.info(" 旧的活动id为 " + oldId
                + ", activityStartTime=" + DateUtil.formateDateStr(activity.getActivityStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS)
                + ", activityEndTime=" + DateUtil.formateDateStr(activity.getActivityEndTime(), DateUtil.YYYY_MM_DD_HH_MM_SS)
                + ", applicationStartTime=" + DateUtil.formateDateStr(activity.getApplicationStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS)
                + ", applicationEndTime=" + DateUtil.formateDateStr(activity.getApplicationEndTime(), DateUtil.YYYY_MM_DD_HH_MM_SS));

        activity.setUserCount(0);
        activity.setStatus(1);
        activity.setIsTemplate(0);              // 设置不为模板,为活动id
        activity.setActivityState(0);           // 将活动状态设置为未开始
        activity.setIsAddRobot(0);
        // 3.0及以后版本isNew为1
        activity.setIsNew(YesNoStatus.YES.getCode());
        runActivityService.insert(activity);

        //写入活动道具配置
        List<ActivityPropConfig> list = activityPropConfigService.selectActivityPropConfigByActivityId(oldId);
        if (!CollectionUtils.isEmpty(list)) {
            for (ActivityPropConfig activityPropConfig : list) {
                activityPropConfig.setActivityId(activity.getId());
                activityPropConfig.setGmtCreate(ZonedDateTime.now());
                activityPropConfig.setGmtModified(null);
                activityPropConfig.setId(null);
                activityPropConfigService.insertActivityPropConfig(activityPropConfig);
            }
        }
        // 官方组队跑的限速也需要进行复制
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activity.getActivityType()) && activity.getRateLimitType() == 2) {
            List<ActivityRateLimit> activityRateLimits = activityRateLimitService.findList(
                    ActivityRateLimitQuery.builder()
                            .activityId(oldId)
                            .build()
            );
            log.info("旧活动id：{} 对应的限速数据有：{}", oldId, activityRateLimits.toString());
            activityRateLimits.stream().forEach(item -> {
                item.setId(null);
                item.setActivityId(activity.getId()); // 新生成的活动id
                activityRateLimitService.save(item);
            });

        }

        List<ActivityEquipmentConfig> activityTaskConfigs = activityEquipmentConfigService.selectActivityEquipmentConfigByActivityId(oldId);
        for (ActivityEquipmentConfig activityEquipmentConfig : activityTaskConfigs) {
            activityEquipmentConfig.setActivityId(activity.getId());
            activityEquipmentConfig.setId(null);
            activityEquipmentConfigService.insertOrUpdateActivityEquipmentConfig(activityEquipmentConfig);
        }

        List<ActivityBrandRightsInterests> activityBrandRightsInterests = activityBrandRightsInterestsService.findActivityBrandRightsInterestsByActivityId(oldId);
        for (ActivityBrandRightsInterests activityBrandRightsInterest : activityBrandRightsInterests) {
            activityBrandRightsInterest.setActivityId(activity.getId());
            activityBrandRightsInterest.setId(null);
            activityBrandRightsInterestsService.insertActivityBrandRightsInterests(activityBrandRightsInterest);
        }

        List<RunActivityShowUser> runActivityShowUsers = runActivityShowUserService.selectRunActivityShowUserByActivityId(oldId);
        for (RunActivityShowUser runActivityShowUser : runActivityShowUsers) {
            runActivityShowUser.setActivityId(activity.getId());
            runActivityShowUser.setId(null);
            runActivityShowUserService.insertRunActivityShowUser(runActivityShowUser);
        }


        runActivityRuleUserService.bindingRuleUser(activity.getId(), "", 1, oldId);
        log.info(" doCreate0 新生成的活动id = " + activity.getId());
        pushActivity(activity, activityTaskConfig, currentDate);
        // 获取配速员配置并插入新的
        List<PacerDto> pacerDtos = pacerConfigService.selectPacerConfigByActivityId(oldId);
        if (!CollectionUtils.isEmpty(pacerDtos)) {
            pacerConfigService.addPacerConfig(activity.getId(), pacerDtos);
        }
    }


    public void pushActivity(ZnsRunActivityEntity activity, ActivityTaskConfig activityTaskConfig, ZonedDateTime currentDate) {
        Long oldId = activityTaskConfig.getActivityId();
        log.info("autoCreateActivityTask activityId = " + activity.getId() + ",templateId = " + activityTaskConfig.getId() + " , templateActivityId = " + oldId);
        activity.setActivityNo("HD" + activity.getId());

        activity.setRemark((!StringUtils.hasText(activity.getRemark()) ? "" : activity.getRemark())
                + "，由模板ID为：" + activityTaskConfig.getId() + " ，活动模板ID为："
                + oldId + ",配置的时区为 ：" + activityTaskConfig.getTimeZone()
                + ", applicationStartLimit=" + activity.getApplicationStartLimit());

        runActivityService.updateById(activity);
        if (activity.getActivityType() == 4) {
            int betweenSecond = DateUtil.betweenSecond(currentDate, activity.getActivityEndTime());
            Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activity.getActivityConfig());
            List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
            for (Integer runningGoal : runningGoals) {
                Long activityGoalId = NumberUtils.getGoalImNumber(activity.getId(), runningGoal, activity.getCompleteRuleType());
                //推送到游戏服务器端
                try {
                    GamePushUtils.addRoom(gameDomain, activityGoalId, 2, null);
                } catch (Exception e) {
                    log.info("请求游戏服务器异常:{}", e.getMessage());
                }
            }
        } else if (activity.getActivityType() == 3) {
            //推送到游戏服务器端
            try {
                GamePushUtils.addRoom(gameDomain, activity.getId(), 2, null);
            } catch (Exception e) {
                log.info("请求游戏服务器异常:{}", e.getMessage());
            }
        }
        log.info("官方活动新增成功");
        eggActivityBizService.doActivityEggPush(activity.getId());
    }


    /**
     * 开始有机器人的活动
     *
     * @param logNo
     * @param runDate
     * @param znsRunActivityEntity
     */
    public void startHaveRobotActivity(String logNo, ZonedDateTime runDate, ZnsRunActivityEntity znsRunActivityEntity) {
        RLock lock = redissonClient.getLock(RedisConstants.OLD_ACTIVITY_START + znsRunActivityEntity.getId());
        LockHolder.tryLock(lock, 5, () -> {
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
            OrderUtil.append(znsRunActivityEntity.getId());
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                    try {
                        String newLogNo = logNo + "_" + OrderUtil.getUniqueCode();
                        OrderUtil.addLogNo(newLogNo, runDate.toInstant().toEpochMilli());
                        log.info("startHaveRobotActivity当前时间{}", ZonedDateTime.now());
                        log.info(" RotBotPushRunDataTask handleActivityStart 活动id = " + znsRunActivityEntity.getId());
                        if (Objects.equals(znsRunActivityEntity.getActivityState(), 1)
                                && DateUtil.betweenSecond(znsRunActivityEntity.getActivityStartTime(), runDate) > 30) {
                            log.info("活动已经开始 ：" + znsRunActivityEntity.getId());
                            return;
                        }
                        List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchByActivityIdStatusIsRobot(znsRunActivityEntity.getId(), 1, 1);
                        // 将所有zns_run_activity_user 中已经接受但是还没有参加跑步的用户更新为跑步中。
                        for (MindUserMatch mindUserMatch : mindUserMatches) {
                            ZnsRunActivityUserEntity znsRunActivityUserEntity = runActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), mindUserMatch.getUserId());
                            log.info("znsRunActivityUserEntity.getUserState(), znsRunActivityUserId=" + znsRunActivityUserEntity.getId() + "," + znsRunActivityUserEntity.getUserState());
                            if (Objects.equals(znsRunActivityUserEntity.getUserState(), 1)) {
                                log.info("ZnsRunActivityUserEntity的UserState 更新为 3 " + znsRunActivityUserEntity.getId());
                                runActivityUserService.updateUserStateById(3, ZonedDateTime.now(), znsRunActivityUserEntity.getId());
                            }
                        }

                        // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
                        Integer status = handleActivityStart(znsRunActivityEntity, 1);
                        if (ActivityStateEnum.CANCELED.getState().equals(status)) {
                            if (!CollectionUtils.isEmpty(mindUserMatches)) {
                                for (MindUserMatch mindUserMatch : mindUserMatches) {
                                    mindUserMatch.setStatus(2);
                                    mindUserMatchBizService.updateMindUserMatchById(mindUserMatch);

                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("handleActivityStart 异常", e);
                    } finally {
                        OrderUtil.addLogNo(logNo);
                    }
                }
            });
            log.info("RotBotPushRunDataTask 活动id = " + znsRunActivityEntity.getId());
            // 如果活动状态为未开始，并且活动开始时间大于当前时间，开启活动
            ZonedDateTime now = ZonedDateTime.now();
            log.info("now time {},runDate {}", now, runDate);
            log.info("当前活动状态{}，开始时间{}", znsRunActivityEntity.getActivityState(), znsRunActivityEntity.getActivityStartTime());
            if (Objects.equals(znsRunActivityEntity.getActivityState(), 0) && DateUtil.le(znsRunActivityEntity.getActivityStartTime(), now)) {
                log.info(" 如果活动状态为未开始，并且活动开始时间大于当前时间，开启活动 activityId = " + znsRunActivityEntity.getId());
                this.updateActivityState(znsRunActivityEntity, ActivityStateEnum.IN_PROGRESS, ActivitySubstateEnum.TIMED_TASK_START);
                activityUserScoreBizService.sendPkScore(znsRunActivityEntity.getId());
            }
            handlerActivity(znsRunActivityEntity);
        });
    }

    // 查询出所有活动对应的zns_mind_user_match，发送延迟消息队列，延迟时间为0 ，让所有的机器人跑起来 。
    public void handlerActivity(ZnsRunActivityEntity znsRunActivityEntity) {
        //官方不走这里
        if (znsRunActivityEntity.getActivityType() == 4) {
            return;
        }
        List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchByActivityIdStatusIsRobot(znsRunActivityEntity.getId(), 1, 1);
        for (MindUserMatch mindUserMatch : mindUserMatches) {
            AutomaticAdmissionDealDto dto = new AutomaticAdmissionDealDto(mindUserMatch.getId(), DateUtil.getDateYYYY_MM_DD_HH_MM_SS(), "定时任务自动添加");
            DelayDto delayDto = new DelayDto(Constants.ROBOT_ENTRANCE, JsonUtil.writeString(dto));
            log.info(" RotBotPushRunDataTask 延迟队列发送消息 mindMatchId=" + mindUserMatch.getId() + ",dto=" + delayDto);
            // 通过广播模式发布延时消息 延时30分钟 持久化消息 消费后销毁 这里无需指定路由，会广播至每个绑定此交换机的队列
            rabbitTemplate.convertAndSend(delay_exchange_name, "", JsonUtil.writeString(delayDto), message -> {
                message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                message.getMessageProperties().setDelay(0);   // 立即执行
                return message;
            });
        }
    }

    /**
     * 活动开始定时任务处理
     */
    public void handleActivityStart() {
        ZonedDateTime startTime = DateUtil.addDays(ZonedDateTime.now(), -1);
        List<ZnsRunActivityEntity> activityEntities = runActivityService.selectActivityByActivityStateActivityStartHasRobot(ActivityStateEnum.NOT_START.getState(), ZonedDateTime.now(), startTime, 0);
        String logNo = OrderUtil.getLogNo();
        long time = OrderUtil.getExeTime();
        log.info("handleActivityStart 开始处理" + logNo + ",time = " + time);
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        if (!CollectionUtils.isEmpty(activityEntities)) {
            for (ZnsRunActivityEntity runActivityEntity : activityEntities) {
                if (RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType().equals(runActivityEntity.getActivityType())) {
                    // 新里程碑活动的start由RunActivityTask.battlePassMilestoneStartTask处理
                    continue;
                }
                executor.execute(() -> {
                    Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
//                    String newLogNo = logNo + "_" + runActivityEntity.getId();
//                    OrderUtil.addLogNo("child_" + newLogNo, time);
                    handleActivityStart(runActivityEntity, 0);
                });
            }
        }
    }

    /**
     * 活动开始
     *
     * @param runActivityEntity
     * @param hasRobot
     * @return
     */
    public Integer handleActivityStart(ZnsRunActivityEntity runActivityEntity, int hasRobot) {
        try {
            int activityUserCount = 2;
            if (RunActivityTypeEnum.TEAM_RUN.getType().equals(runActivityEntity.getActivityType())) {
                // 组队跑:如果活动参与的人数>=2,则活动变成进行中,否则活动变成取消状态
                if (runActivityEntity.getUserCount() >= activityUserCount) {
                    runActivityEntity.setActivityState(ActivityStateEnum.IN_PROGRESS.getState());
                    log.info("修改活动状态，活动id：{},活动状态：{}", runActivityEntity.getId(), ActivityStateEnum.IN_PROGRESS.getState());
                } else {
                    runActivityEntity.setActivityState(ActivityStateEnum.CANCELED.getState());
                    runActivityEntity.setSubState(ActivitySubstateEnum.TIMED_TASK_CANCEL.getState());
                    // 取消活动退款
                    this.cancelActivityRefund(runActivityEntity, AccountDetailTypeEnum.SECURITY_FUND);
                    log.info("handleActivityStart取消活动，活动id：" + runActivityEntity.getId());
                }
            } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(runActivityEntity.getActivityType())) {
                // 挑战跑：活动开始时间+10秒的时候判断参与活动的用户是否处于跑步中的状态
                RunActivityUserQuery query = RunActivityUserQuery.builder()
                        .isDelete(0).activityId(runActivityEntity.getId())
                        .userStateIn(Arrays.asList(ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState()))
                        .build();
                long userCount = runActivityUserService.findCount(query);
                log.info("handleActivityStart userCount = " + userCount + "，activityUserCount=" + activityUserCount + ",runActivityEntityId=" + runActivityEntity.getId());
                if (userCount >= activityUserCount) {
                    runActivityEntity.setActivityState(ActivityStateEnum.IN_PROGRESS.getState());
                    activityUserScoreBizService.sendPkScore(runActivityEntity.getId());
                } else {
                    ZonedDateTime addSeconds = DateUtil.addSeconds(runActivityEntity.getActivityStartTime(), 10);
                    if (addSeconds.isAfter(ZonedDateTime.now())) {
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    userCount = runActivityUserService.findCount(query);
                    log.info("handleActivityStart 再次 userCount = " + userCount + "，activityUserCount=" + activityUserCount + ",runActivityEntityId=" + runActivityEntity.getId());
                    if (userCount >= activityUserCount || Objects.equals(runActivityEntity.getActivityTypeSub(), 3)) {
                        runActivityEntity.setActivityState(ActivityStateEnum.IN_PROGRESS.getState());
                        log.info("修改活动状态，活动id：{},活动状态：{}", runActivityEntity.getId(), ActivityStateEnum.IN_PROGRESS.getState());
                    } else {
                        runActivityEntity.setActivityState(ActivityStateEnum.CANCELED.getState());
                        runActivityEntity.setSubState(ActivitySubstateEnum.TIMED_TASK_CANCEL.getState());
                        //取消活动退款
                        this.cancelActivityRefund(runActivityEntity, AccountDetailTypeEnum.SECURITY_FUND);
                        log.info("handleActivityStart取消活动，活动id：" + runActivityEntity.getId());
                        //取消情况下处理已经跑步中的机器人
                        if (hasRobot == 1) {
                            //查询跑步中的机器人
                            ZnsRunActivityUserEntity activityUser = runActivityUserService.findOne(query);
                            if (Objects.nonNull(activityUser)) {
                                ZnsUserEntity user = userService.findById(activityUser.getUserId());
                                if (user.getIsRobot() == 1) {
                                    activityUser.setUserState(4);
                                    runActivityUserService.updateById(activityUser);
                                }
                            }

                        }
                    }
                }
            } else {
                if (runActivityEntity.getStatus() == 1) {
                    // 官方赛事
                    runActivityEntity.setActivityState(ActivityStateEnum.IN_PROGRESS.getState());
                }
            }
            runActivityEntity.setModifieTime(ZonedDateTime.now());
            runActivityService.updateByIdWithStatus1(runActivityEntity);
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            OrderUtil.removeLogNo();
        }
        return runActivityEntity.getActivityState();
    }

    /**
     * 取消任务关联的前一场活动
     *
     * @param userId
     * @param taskId
     * @param activityId
     */
    public void cancelLastActivityTask(Long userId, Long taskId, Long activityId) {
        if (Objects.isNull(taskId) || Objects.isNull(activityId)) {
            return;
        }
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(RunActivityUserQuery.builder().taskId(taskId).maxActivityIdLt(activityId).build());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ZnsRunActivityUserEntity runActivityUser : list) {
            log.info("cancelLastActivityTask start,activityId=" + runActivityUser.getActivityId());
            ZnsRunActivityEntity activity = runActivityService.findById(runActivityUser.getActivityId());
            if (Objects.isNull(activity)) {
                continue;
            }
            if (activity.getActivityState() != 0) {
                continue;
            }
            List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(runActivityUser.getActivityId());
            cancelActivity(activity, allActivityUser);
        }
    }

    public void cancelActivity(ZnsRunActivityEntity activityEntity, List<ZnsRunActivityUserEntity> activityUserEntities) {
        // 变更挑战跑活动为已取消
        updateActivityState(activityEntity, ActivityStateEnum.CANCELED, ActivitySubstateEnum.TIMED_TASK_CANCEL);
        // 取消活动退款
        cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
    }

    /**
     * 活动取消如果活动需要支付保证金，那么要给用户退回去
     */
    public void cancelActivityRefund(ZnsRunActivityEntity activityEntity, AccountDetailTypeEnum accountDetailTypeEnum) {
        if (null == activityEntity || null == accountDetailTypeEnum) {
            return;
        }
        // 如果挑战赛要付保证金，那么要给发起挑战者退保证金并且添加对应账号明细
        if (null != activityEntity.getBonusRuleType() && 2 == activityEntity.getBonusRuleType()) {
            List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityEntity.getId());
            if (CollectionUtils.isEmpty(allActivityUser)) {
                return;
            }
            for (ZnsRunActivityUserEntity runActivityUser : allActivityUser) {
                cancelActivityRefund(activityEntity, accountDetailTypeEnum, runActivityUser.getUserId(), I18nMsgUtils.getMessage("userAccount.refund.remark"));
                // 取消退还优惠券
                userCouponService.cancelActivityRefund(activityEntity.getId(), runActivityUser.getUserId(), runActivityUser.getUserState());
            }
        }
    }

    public void cancelActivityRefund(ZnsRunActivityEntity activityEntity, AccountDetailTypeEnum accountDetailTypeEnum, Long userId, String refundRemark) {
        if (null == activityEntity || null == accountDetailTypeEnum || null == userId) {
            return;
        }
        ZnsUserAccountDetailEntity userAccountDetailEntity = userAccountDetailService.selectAccountDetail(2, accountDetailTypeEnum, userId, activityEntity.getId());
        if (null != userAccountDetailEntity && userAccountDetailEntity.getRefundStatus() == 0) {
            userAccountService.refundBalance(userAccountDetailEntity, refundRemark);
        }
    }

    public void cancelActivityFee(ZnsRunActivityEntity activityEntity, ZnsUserEntity user, AccountDetailTypeEnum accountDetailTypeEnum) {
        if (null == activityEntity || null == accountDetailTypeEnum) {
            return;
        }
        // 如果支付了费用，那么要给发起挑战者退费用并且添加对应账号明细
        if (null != activityEntity.getBonusRuleType() && Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType())) {
            List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityEntity.getId());
            if (CollectionUtils.isEmpty(allActivityUser)) {
                return;
            }
            cancelActivityRefund(activityEntity, accountDetailTypeEnum, user.getId(),
                    "Activity cancelled,refund to the original way");
        }
    }

    /**
     * 变更活动状态
     *
     * @param activityEntity    活动
     * @param activityStateEnum 需要变更的状态
     */
    public void updateActivityState(ZnsRunActivityEntity activityEntity, ActivityStateEnum activityStateEnum, ActivitySubstateEnum activitySubstateEnum) {
        if (null != activityEntity) {
            if (null != activityEntity) {
                activityEntity.setActivityState(activityStateEnum.getState());
            }
            if (null != activitySubstateEnum) {
                activityEntity.setSubState(activitySubstateEnum.getState());
            }
            // 如果是取消状态 ,将机器人释放成可用
            if (ActivityStateEnum.CANCELED.equals(activityStateEnum)) {
                List<MindUserMatch> list = mindUserMatchService.selectMindUserMatchByActivityIdStatusIsRobot(activityEntity.getId(), 1, 1);
                for (MindUserMatch mindUserMatch : list) {
                    mindUserMatch.setStatus(2);
                    mindUserMatchBizService.updateMindUserMatchById(mindUserMatch);
                }
            }
            runActivityService.updateById(activityEntity);
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public Result updateActivityStatus(RunActivityShelfUpdateRequest runActivityShelfUpdateRequest) {
        Long id = runActivityShelfUpdateRequest.getId();
        if (id == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.validate.notExist")); //"活动赛事编号不能为空!"
        }
        Integer status = runActivityShelfUpdateRequest.getStatus();
        if (status == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.validate.status.empty")); //"活动赛事需要更新的状态为空!"
        }
        ZnsRunActivityEntity runActivity = runActivityService.findById(id);
        if (runActivity == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.validate.notExist")); //"指定的活动赛事不存在或者已经被删除!"
        }
        // 更新状态
        ZnsRunActivityEntity runActivityEntity = new ZnsRunActivityEntity();
        runActivityEntity.setStatus(status);
        if (ActivityShelfEnum.OFF_SHELF.getStatusCode().equals(status)) {
            ZonedDateTime tradeTime = ZonedDateTime.now();
            runActivityEntity.setActualOffTime(tradeTime);
            // 活动下架处理逻辑:https://lanhuapp.com/web/#/item/project/product?tid=1f178df1-fb24-4079-89a6-f300f4f15697&pid=4e4a79e1-4227-4ff7-a332-95c90b97f810&versionId=a4a7cc3c-b8c2-43fe-8ee1-8730de785ed6&docId=5fafec32-02d4-43fa-afe3-98f80c865030&docType=axure&pageId=fd2c58b1ed3c4762bf6e80e3f424bba3&image_id=5fafec32-02d4-43fa-afe3-98f80c865030&parentId=6277d1fb2c13467a9461db671e40752c
            // 查询所有报名成功用户
            // TODO:报名成功的判断逻辑需要进一步确认,目前表里面有对应的数据就算成功
            RunActivityUserQuery query = RunActivityUserQuery.builder()
                    .activityId(id).isDelete(0)
                    .build();
            List<ZnsRunActivityUserEntity> runActivityUserEntityList = runActivityUserService.findList(query);
            List<Long> userIdList;
            if (!CollectionUtils.isEmpty(runActivityUserEntityList)) {
                //（2）站内信通知所有报名成功用户活动已下架：
                userIdList = runActivityUserEntityList.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
                String activityTitle = runActivity.getActivityTitle();
                String activityConfig = runActivity.getActivityConfig();
                ActivityConfigResp activityConfigResp = JsonUtil.readValue(activityConfig, ActivityConfigResp.class);
                String pushThumbnail = activityConfigResp.getPushThumbnail();
                // 获取banner的H5链接
                OperationalActivity operationalActivity = operationalActivityService.findOne(OperationalActivityQuery.builder().runActivityId(id).isDelete(0).runActivityId(id).build());
                String activityUrl = "";
                if (operationalActivity != null) {
                    activityUrl = operationalActivity.getActivityUrl();
                }
                String tencentImConstant = TencentImConstant.TIM_CUSTOM_ELEM;

                String content = I18nMsgUtils.getMessage("activity.removed", activityTitle);// activityTitle + " event has been removed, we are sorry for the inconvenience.";
                ImMessageBo imMessageBo = new ImMessageBo();
                imMessageBo.setJumpType("0");
                //imMessageBo.setParams(paramMap);
                imMessageBo.setJumpValue(activityUrl);
                imMessageBo.setImageUrl(pushThumbnail);
                imMessageBo.setMsg(content);
                // 这里使用线程池,异步发送
                executor.execute(() -> {
                    // 助力成功发送一次站内信
                    appMessageService.sendIm("administrator", userIdList, JsonUtil.writeString(imMessageBo), tencentImConstant, "", 0, Boolean.FALSE);
                });

                // （3）下架成功后立即返还报名费用（如有）（保证金或费用）
                for (ZnsRunActivityUserEntity activityUser : runActivityUserEntityList) {
                    if (activityUser.getIsComplete() == 1) {
                        // TODO:（4）用户在活动下架前进入的比赛，然后完成了比赛，则奖金照旧发放（也就是说只要完成了就发放奖金）
                        // TODO:（5）异常情况：用户在下架之前完成比赛，但是系统还没上传，等到要处理这个异常的时候发现活动下架了，
                        //  此时奖励依旧发放，不考虑活动下架状态
                    }

                    if (runActivity.getBonusRuleType() != 1 && runActivity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                        // 保证金、费用退回
                        this.cancelActivityRefund(runActivity, AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_ENTRY_FEE, activityUser.getUserId(), "Deposit return");
                    }
                }
            }
        } else if (ActivityShelfEnum.ON_SHELF.getStatusCode().equals(status)) {
            Integer prevStatus = runActivity.getStatus();
            if (ActivityShelfEnum.OFF_SHELF.getStatusCode().equals(prevStatus)) {
                return CommonResult.fail("已经下架的主题活动不能再上架!");
            }
            //活动国家不能为空
            if (!StringUtils.hasText(runActivity.getCountry())) {
                log.info("活动上架失败，活动id：{}，请配置可见范围中的国家范围", runActivity.getId());
                return CommonResult.fail("请配置可见范围中的国家范围");
            }
        }
        runActivityEntity.setId(id);
        runActivityService.updateById(runActivityEntity);
        return CommonResult.success();
    }
}
