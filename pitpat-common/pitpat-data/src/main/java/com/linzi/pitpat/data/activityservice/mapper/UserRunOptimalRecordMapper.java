package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunOptimalRecordDo;
import com.linzi.pitpat.data.activityservice.model.query.UserRunOptimalRecordPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 用户运动最佳记录表 数据访问对象
 *
 * @since 2024年7月9日
 */
@Mapper
public interface UserRunOptimalRecordMapper extends BaseMapper<UserRunOptimalRecordDo> {

    List<UserRunOptimalRecordDo> findRandomList(@Param("query") UserRunOptimalRecordPageQuery query);

    List<UserRunOptimalRecordDo> findListByFriends(@Param("query") UserRunOptimalRecordPageQuery query);
}
