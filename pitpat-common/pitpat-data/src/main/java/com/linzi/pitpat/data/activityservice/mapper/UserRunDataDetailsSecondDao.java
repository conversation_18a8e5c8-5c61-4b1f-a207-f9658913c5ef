package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * null 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-20
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.LT;
import com.lz.mybatis.plugin.annotations.Realy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;

@Mapper
public interface UserRunDataDetailsSecondDao extends BaseMapper<ZnsUserRunDataDetailsSecondEntity> {

    ZnsUserRunDataDetailsSecondEntity selectLastSecond(@Param("runDataDetailId") Long runDataDetailId);

    @Realy
    void deleteUserRunDataDetailsSecond7Days(@LT @DateFormat ZonedDateTime createTime);
}
