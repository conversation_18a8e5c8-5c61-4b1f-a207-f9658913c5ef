package com.linzi.pitpat.data.activityservice.dto.console.response;

import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 赛事卡记录响应DTO
 *
 * @since 2025年6月19日
 */
@Data
public class ProActivityCardRecordResponseDto implements Serializable {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户code
     */
    private String userCode;
    
    /**
     * 赛事卡ID
     */
    private Long cardId;
    
    /**
     * 活动ID
     */
    private Long activityId;
    
    /**
     * 操作人
     */
    private String creator;
    
    /**
     * 用户昵称
     */
    private String userNickname;
    
    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;
    
    /**
     * 更新时间
     */
    private ZonedDateTime gmtModified;
    
    // 关联的赛事卡信息
    /**
     * 赛事卡年份
     */
    private Integer cardYear;
    
    /**
     * 赛事卡标题
     */
    private String cardTitle;
    
    /**
     * 赛事卡类型
     */
    private String cardType;
    
    /**
     * 赛事类型
     */
    private String competitiveType;

    /**
     * 等级
     */
    private String levelName;
} 