package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 优惠券 服务类
 * </p>
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>o
 * @since 2022-12-26
 */

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCategory;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryService;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponExchangeFailEnum;
import com.linzi.pitpat.data.awardservice.mapper.CouponCurrencyMapper;
import com.linzi.pitpat.data.awardservice.mapper.CouponDao;
import com.linzi.pitpat.data.awardservice.mapper.UserCouponDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityUserScoreDto;
import com.linzi.pitpat.data.awardservice.model.dto.CouponI8nDto;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.CouponExchangeFailRecord;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.CouponQuery;
import com.linzi.pitpat.data.awardservice.model.query.CouponsPageQuery;
import com.linzi.pitpat.data.awardservice.model.request.CouponReq;
import com.linzi.pitpat.data.awardservice.model.request.CouponSaveReq;
import com.linzi.pitpat.data.awardservice.model.request.CouponUpdateReq;
import com.linzi.pitpat.data.awardservice.model.request.CouponUpdateStatusReq;
import com.linzi.pitpat.data.awardservice.model.vo.CanadaCouponVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponDetailVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponUpdateDetailVo;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.CouponValidityTypeEnum;
import com.linzi.pitpat.data.enums.ExpirationRemindMethodEnum;
import com.linzi.pitpat.data.enums.NumberOfTimesEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.model.entity.MessageTaskMsg;
import com.linzi.pitpat.data.resp.WeekTaskConfigType;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.vo.useractive.UserActiveCouponVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CouponServiceImpl implements CouponService {


    @Autowired
    private CouponDao couponDao;
    @Autowired
    private UserCouponDao userCouponDao;

    @Autowired
    private ActivityCouponConfigService activityCouponConfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CouponCurrencyService couponCurrencyService;
    @Autowired
    private CouponCurrencyMapper couponCurrencyMapper;
    @Autowired
    private CouponI18nService couponI18nService;
    @Autowired
    private ZnsUserAccountService znsUserAccountService;
    @Autowired
    private ActivityCategoryService activityCategoryService;


    @Override
    public Coupon selectCouponById(Long id) {
        return couponDao.selectCouponById(id);
    }


    @Override
    public Result checkCouponExchange(Coupon coupon, ZnsUserEquipmentEntity userEquipment, Long userId, CouponExchangeFailRecord record) {
        if (coupon.getQuota() - coupon.getQuotaSend() <= 0) {
            record.setFailCode(CouponExchangeFailEnum.INSUFFICIENT_COUPON_STOCK.getCode());
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.count.redeemed"));
        }
        if (Objects.nonNull(coupon.getGmtEnd()) && ZonedDateTime.now().toInstant().toEpochMilli() > coupon.getGmtEnd().toInstant().toEpochMilli()) {
            record.setFailCode(CouponExchangeFailEnum.COUPON_EXPIRED.getCode());
            return CommonResult.fail(I18nMsgUtils.getMessage("common.operate.reenter"));
        }
        if (Objects.isNull(userEquipment)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.connect.redeem"));
        }

        Long count1 = userCouponDao.selectCount(Wrappers.<UserCoupon>lambdaQuery().eq(UserCoupon::getEquipmentNo, userEquipment.getEquipmentNo()).eq(UserCoupon::getCouponId, coupon.getId()).eq(UserCoupon::getIsDelete, 0));
        if (count1 >= coupon.getEquipmentLimitCount()) {
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.status.token.exceed", NumberOfTimesEnum.findByType(coupon.getEquipmentLimitCount())));
        }
        Long count2 = userCouponDao.selectCount(Wrappers.<UserCoupon>lambdaQuery().eq(UserCoupon::getUserId, userId).eq(UserCoupon::getCouponId, coupon.getId()).eq(UserCoupon::getIsDelete, 0));
        if (count2 >= coupon.getLimitCount()) {
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.status.token.exceed", NumberOfTimesEnum.findByType(coupon.getLimitCount())));
        }
        return null;
    }


    @Override
    public Page<Coupon> listAll(CouponReq pageQuery) {
        LambdaQueryWrapper<Coupon> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Coupon::getIsDelete, YesNoStatus.NO.getCode());
        wrapper.eq(Objects.nonNull(pageQuery.getStatus()), Coupon::getStatus, pageQuery.getStatus());
        wrapper.eq(Objects.nonNull(pageQuery.getCouponType()), Coupon::getCouponType, pageQuery.getCouponType());
        wrapper.eq(Objects.nonNull(pageQuery.getId()), Coupon::getId, pageQuery.getId());
        wrapper.eq(Objects.nonNull(pageQuery.getCouponMainType()), Coupon::getCouponMainType, pageQuery.getCouponMainType());
        wrapper.like(StringUtils.hasText(pageQuery.getCountryCode()), Coupon::getCountryCode, pageQuery.getCountryCode());
        wrapper.le(Objects.nonNull(pageQuery.getGmtEndTime()), Coupon::getGmtCreate, pageQuery.getGmtEndTime());
        wrapper.ge(Objects.nonNull(pageQuery.getGmtStartTime()), Coupon::getGmtCreate, pageQuery.getGmtStartTime());
        wrapper.like(StringUtils.hasText(pageQuery.getCouponName()), Coupon::getTitle, pageQuery.getCouponName());
        return couponDao.selectPage(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()), wrapper);
    }

    /**
     * 已废弃 4.4.3之后的版本可以删除
     */
    @Deprecated(since = "4.4.3")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveNew(CouponSaveReq couponSaveReq) {
        if (couponSaveReq.getExpiryType() == 2) {
            if (DateUtil.compare(couponSaveReq.getGmtEnd(), ZonedDateTime.now()) < 0) {
                return CommonResult.fail("券的有效时间需在当前时间之后");
            }
        }
        Coupon coupon = new Coupon();
        //如果是复制过来，新增时忽略id
        BeanUtils.copyProperties(couponSaveReq, coupon, "id");
        if (!CollectionUtils.isEmpty(couponSaveReq.getValues())) {
            //使用默认语言标题填充优惠券标题
            CouponI8nDto couponI8nDto = couponSaveReq.getValues().stream().filter(e -> e.getLangCode().equals(couponSaveReq.getDefaultLangCode())).findFirst().orElse(couponSaveReq.getValues().get(0));
            coupon.setDescription(couponI8nDto.getCanUseDescription());
            coupon.setTitle(couponI8nDto.getTitle());
        }
        if (Objects.nonNull(couponSaveReq.getDiscount())) {
            coupon.setDiscount(couponSaveReq.getDiscount().divide(new BigDecimal(100)).setScale(2));
        }
        coupon.setMinPicUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202304/iiJ13qGWf6FU6183.png");
        boolean save = couponDao.insert(coupon) > 0;
        //保存多币种价格
        couponCurrencyService.saveOrModifyCouponCurrency(couponSaveReq.getOperateName(), coupon.getId(), couponSaveReq.getCurrencyList());
        if (!CollectionUtils.isEmpty(couponSaveReq.getActivityIds())) {
            couponSaveReq.getActivityIds().forEach(i -> saveNewCouponConfig(2, String.valueOf(i), coupon));
        }
        if (Objects.nonNull(couponSaveReq.getActivityType())) {
            saveNewCouponConfig(1, String.valueOf(couponSaveReq.getActivityType()), coupon);
        }
        if (!CollectionUtils.isEmpty(couponSaveReq.getTaskIds())) {
            couponSaveReq.getTaskIds().forEach(i -> saveNewCouponConfig(3, String.valueOf(i), coupon));
        }
        if (CollectionUtils.isEmpty(couponSaveReq.getActivityIds()) && Objects.isNull(couponSaveReq.getActivityType()) && CollectionUtils.isEmpty(couponSaveReq.getTaskIds())
                && couponSaveReq.getNewActivityType().equals(MainActivityTypeEnum.OLD.getType())) {
            saveNewCouponConfig(0, null, coupon);
        }
        //新活动类型排除old
        if (Objects.nonNull(couponSaveReq.getNewActivityType()) && !couponSaveReq.getNewActivityType().equals(MainActivityTypeEnum.OLD.getType())) {
            if (Objects.nonNull(couponSaveReq.getNewActivityId())) {
                if (couponSaveReq.getNewActivityType().equals(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType())) {
                    couponSaveReq.getNewActivityId().forEach(i -> saveNewCouponConfig(6, i + "", coupon));
                } else {
                    couponSaveReq.getNewActivityId().forEach(i -> saveNewCouponConfig(2, i + "", coupon));
                }
            } else {
                saveNewCouponConfig(5, couponSaveReq.getNewActivityType(), coupon);
            }
        }
        //加拿大临时支付方案
        String name = couponSaveReq.getName();
        if (name != null && name.equals("JIANADALINSHIZHIFUFANGAN")) {
            redisTemplate.opsForList().rightPush(RedisConstants.CANADA_TEMP_COUPONS, coupon.getId().toString());
        }
        // 保存国际化数据
        List<CouponI8nDto> values = couponSaveReq.getValues();
        if (!CollectionUtils.isEmpty(values)) {
            couponI18nService.saveBatch(coupon.getId(), values);
        }

        return CommonResult.success(save);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateCouponConfig(CouponUpdateReq couponUpdateReq) {
        if (couponUpdateReq.getExpiryType() == 2) {
            if (DateUtil.compare(couponUpdateReq.getGmtEnd(), ZonedDateTime.now()) < 0) {
                return CommonResult.fail("券的有效时间需在当前时间之后");
            }
        }
        Coupon coupon = couponDao.selectById(couponUpdateReq.getId());
        BeanUtils.copyProperties(couponUpdateReq, coupon);
        if (Objects.nonNull(couponUpdateReq.getDiscount())) {
            coupon.setDiscount(couponUpdateReq.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        if (!CollectionUtils.isEmpty(couponUpdateReq.getValues())) {
            //使用默认语言标题填充优惠券标题
            CouponI8nDto couponI8nDto = couponUpdateReq.getValues().stream().filter(e -> e.getLangCode().equals(couponUpdateReq.getDefaultLangCode())).findFirst().orElse(couponUpdateReq.getValues().get(0));
            coupon.setDescription(couponI8nDto.getCanUseDescription());
            coupon.setTitle(couponI8nDto.getTitle());
        }
        boolean save = couponDao.updateById(coupon) > 0;
        //更新多币种价格
        couponCurrencyService.saveOrModifyCouponCurrency(couponUpdateReq.getOperateName(), coupon.getId(), couponUpdateReq.getCurrencyList());
        activityCouponConfigService.deleteByCouponId( coupon.getId());
        if (!CollectionUtils.isEmpty(couponUpdateReq.getActivityIds())) {
            couponUpdateReq.getActivityIds().forEach(i -> saveNewCouponConfig(2, String.valueOf(i), coupon));
        }
        if (Objects.nonNull(couponUpdateReq.getActivityType())) {
            ActivityCouponConfig typeConfig = activityCouponConfigService.getOne(new QueryWrapper<ActivityCouponConfig>().eq("is_delete", YesNoStatus.NO.getCode())
                    .eq("coupon_id", coupon.getId()).eq("type", 1));
            if (Objects.isNull(typeConfig)) {
                saveNewCouponConfig(1, String.valueOf(couponUpdateReq.getActivityType()), coupon);
            } else {
                if (!Objects.equals(typeConfig.getType(), couponUpdateReq.getActivityType())) {
                    typeConfig.setType(couponUpdateReq.getActivityType());
                    activityCouponConfigService.updateById(typeConfig);
                }
            }
        }
        if (!CollectionUtils.isEmpty(couponUpdateReq.getTaskIds())) {
            couponUpdateReq.getTaskIds().forEach(i -> saveNewCouponConfig(3, String.valueOf(i), coupon));
        }
        String newActivityType = couponUpdateReq.getNewActivityType();
        if (CollectionUtils.isEmpty(couponUpdateReq.getActivityIds()) && Objects.isNull(couponUpdateReq.getActivityType()) && CollectionUtils.isEmpty(couponUpdateReq.getTaskIds())
                && MainActivityTypeEnum.OLD.getType().equals(newActivityType)) {
            saveNewCouponConfig(0, null, coupon);
        }
        //新活动类型排除old
        if (Objects.nonNull(newActivityType) && !newActivityType.equals(MainActivityTypeEnum.OLD.getType())) {
            if (Objects.nonNull(couponUpdateReq.getNewActivityId())) {
                if (newActivityType.equals(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType())) {
                    couponUpdateReq.getNewActivityId().forEach(i -> saveNewCouponConfig(6, i + "", coupon));
                } else {
                    couponUpdateReq.getNewActivityId().forEach(i -> saveNewCouponConfig(2, i + "", coupon));
                }
            } else {
                saveNewCouponConfig(5, newActivityType, coupon);
            }
        }
        // 修改国际化数据
        List<CouponI8nDto> values = couponUpdateReq.getValues();
        if (!CollectionUtils.isEmpty(values)) {
            couponI18nService.saveBatch(coupon.getId(), values);
        }

        return CommonResult.success(save);
    }

    /**
     * 保存卷限制明细
     * @param type
     * @param i
     * @param coupon
     */
    private void saveNewCouponConfig(int type, String i, Coupon coupon) {
        ActivityCouponConfig activityCouponConfig = new ActivityCouponConfig();
        activityCouponConfig.setType(type);
        activityCouponConfig.setCouponConfig(i);
        activityCouponConfig.setCouponId(coupon.getId());
        activityCouponConfigService.save(activityCouponConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(CouponUpdateStatusReq couponUpdateStatusReq) {
        Coupon coupon = couponDao.selectById(couponUpdateStatusReq.getId());
        if (CouponValidityTypeEnum.RANGE.getCode() == coupon.getExpiryType()) {
            if (DateUtil.compare(ZonedDateTime.now(), coupon.getGmtEnd()) > 0) {
                throw new BaseException("卷生效结束时间需要更改", 500);
            }
        }
        if (Objects.nonNull(coupon.getReceiveEnd()) && coupon.getReceiveEnd().isBefore(ZonedDateTime.now())) {
            //已超过领取时间
            throw new BaseException("不在可领取范围内");
        }
        coupon.setStatus(couponUpdateStatusReq.getStatus());
        coupon.setGmtModified(ZonedDateTime.now());
        return couponDao.updateById(coupon) > 0;
    }

    @Autowired
    private AppRouteConfigService appRouteConfigService;

    @Override
    public CouponDetailVo couponDetail(Long id) {
        CouponDetailVo couponDetailVo = new CouponDetailVo();
        Coupon coupon = couponDao.selectById(id);
        BeanUtils.copyProperties(coupon, couponDetailVo);
        AppRouteConfig appRouteConfig = appRouteConfigService.findById(coupon.getRouteId());
        if (Objects.nonNull(appRouteConfig)) {
            couponDetailVo.setRouteName(appRouteConfig.getTwoLevel());
        }
        ActivityCategory category = activityCategoryService.findById(coupon.getRouteId());
        if (Objects.nonNull(category)) {
            couponDetailVo.setRouteName("官方赛事列表-" + category.getTitle());
        }
        couponDetailVo.setCouponContent(getCouponContent(coupon));
        couponDetailVo.setCouponValidityPeriod(getCouponValidityPeriod(coupon));
        couponDetailVo.setActivityCouponConfig(getActivityCouponConfig(coupon));
        couponDetailVo.setExpirationRemindConfig(geExpirationRemindConfig(coupon));

        //封装币种金额
        couponDetailVo.setCurrencyList(getCurrencyList(id));
        // 国际化数据
        List<CouponI18n> couponI18ns = couponI18nService.selectCouponId(id);
        if (!CollectionUtils.isEmpty(couponI18ns)) {
            couponDetailVo.setValues(couponI18ns);
        }
        return couponDetailVo;
    }

    /**
     * 获取多币种金额
     *
     * @param couponId
     * @return
     */
    private List<CurrencyAmount> getCurrencyList(Long couponId) {
        List<CouponCurrencyEntity> list = couponCurrencyMapper.selectByCouponIdAndIgnoreCurrency(couponId, null);
        if (CollectionUtils.isEmpty(list) || list.get(0) == null) {
            return new ArrayList<>();
        }
        return list.stream().map(o -> I18nConstant.buildCurrencyAmount(o.getCurrencyCode(), o.getAmount())).toList();
    }

    public String geExpirationRemindConfig(Coupon coupon) {
        return "卷过期前" + coupon.getExpirationRemindDay() + "天，" + coupon.getExpirationRemindTime() + "发送" + ExpirationRemindMethodEnum.findByType(coupon.getExpirationRemindMethod()).getName() + " 提醒用户";
    }

    @Autowired
    private ZnsRunActivityService runActivityService;

    @Autowired
    private ActivityTaskConfigService activityTaskConfigService;

    public String getActivityCouponConfig(Coupon coupon) {
        List<ActivityCouponConfig> rules = activityCouponConfigService.getConfigByCouponIdList(coupon);
        if (!CollectionUtils.isEmpty(rules)) {
            if (rules.size() == 1) {
                ActivityCouponConfig activityCouponConfig = rules.get(0);
                if (activityCouponConfig.getType() == 1) {
                    return RunActivityTypeEnum.findByType(Integer.valueOf(activityCouponConfig.getCouponConfig())).getName();
                } else if (activityCouponConfig.getType() == 2) {
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    if (activityNew.getMainType().equals(MainActivityTypeEnum.OLD.getType())) {
                        return RunActivityTypeEnum.findByType(activityNew.getActivityType()).getName() + "-" + activityNew.getId() + "/" + activityNew.getActivityTitle();
                    } else {
                        return MainActivityTypeEnum.findByType(activityNew.getMainType()).getRemark() + "-" + activityNew.getId() + "/" + activityNew.getActivityTitle();
                    }
                } else if (activityCouponConfig.getType() == 3) {
                    ActivityTaskConfig taskConfigServiceById = activityTaskConfigService.findById(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    return "聚合活动" + "-" + taskConfigServiceById.getId() + "/" + taskConfigServiceById.getTaskName();
                } else if (activityCouponConfig.getType() == 5) {
                    return MainActivityTypeEnum.findByType(activityCouponConfig.getCouponConfig()).getRemark();
                } else if (activityCouponConfig.getType() == 6) {
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    return MainActivityTypeEnum.findByType(activityNew.getMainType()).getRemark() + "-" + activityNew.getId() + "/" + activityNew.getActivityTitle();
                } else {
                    return "";
                }
            } else {
                ActivityCouponConfig activityCouponConfig = rules.get(0);
                if (activityCouponConfig.getType() == 1) {
                    return RunActivityTypeEnum.findByType(Integer.valueOf(activityCouponConfig.getCouponConfig())).getName();
                } else if (activityCouponConfig.getType() == 2) {
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    if (activityNew.getMainType().equals(MainActivityTypeEnum.OLD.getType())) {
                        return RunActivityTypeEnum.findByType(activityNew.getActivityType()).getName() + "-" + activityNew.getId() + "/" + activityNew.getActivityTitle();
                    } else {
                        return MainActivityTypeEnum.findByType(activityNew.getMainType()).getRemark() + "-" + activityNew.getId() + "/" + activityNew.getActivityTitle();
                    }
                } else if (activityCouponConfig.getType() == 3) {
                    return "聚合活动" + "-" + getAllTaskStr(rules);
                } else if (activityCouponConfig.getType() == 5) {
                    return MainActivityTypeEnum.findByType(activityCouponConfig.getCouponConfig()).getRemark();
                } else if (activityCouponConfig.getType() == 6) {
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    return MainActivityTypeEnum.findByType(activityNew.getMainType()).getRemark() + "-" + activityNew.getId() + "/" + activityNew.getActivityTitle();
                } else {
                    return "";
                }
            }
        } else {
            return "";
        }
    }

    private String getAllTaskStr(List<ActivityCouponConfig> rules) {
        List<String> result = Lists.newArrayList();
        rules.forEach(i -> {
            ActivityTaskConfig taskConfigServiceById = activityTaskConfigService.findById(Long.valueOf(i.getCouponConfig()));
            String detail = taskConfigServiceById.getId() + "/" + taskConfigServiceById.getTaskName();
            result.add(detail);
        });
        return String.join("", result);
    }

    private String getAllActivityStr(List<ActivityCouponConfig> rules) {
        List<String> result = Lists.newArrayList();
        rules.forEach(i -> {
            ZnsRunActivityEntity runActivityEntity = runActivityService.findById(Long.valueOf(i.getCouponConfig()));
            String detail = runActivityEntity.getActivityNo() + "/" + runActivityEntity.getActivityTitle();
            result.add(detail);
        });
        return String.join("", result);
    }


    /**
     * 卷内容获取
     * @param i
     * @return
     */
    public String
    getCouponContent(Coupon i) {
        List<Integer> couponList = Arrays.asList(3, 5, 6, 7);
        if (couponList.contains(i.getCouponType())) {
            if (CouponTypeEnum.LUCKY_CASH_COUPON.getCode().equals(i.getCouponType())) {
                return String.valueOf(i.getAmount());
            } else {
                return "抵扣金额$" + i.getAmount();
            }
        } else if (CouponTypeEnum.AMAZON_COUPON.getCode().equals(i.getCouponType())) {
            BigDecimal multiply = i.getDiscount().multiply(new BigDecimal(100)).setScale(0, RoundingMode.UP);
            if (multiply.toString().endsWith("0")) {
                return multiply.toString().replace("0", "") + "折";
            }
            if (multiply.compareTo(new BigDecimal(10)) < 0) {
                return multiply.divide(new BigDecimal(10)).setScale(1, RoundingMode.UP) + "折";
            } else {
                return multiply + "折";
            }
        } else if (CouponTypeEnum.MALL_COUPON.getCode().equals(i.getCouponType())) {
            //商城券
            BigDecimal minTotalAmount = Optional.ofNullable(i.getMinTotalAmount()).orElse(BigDecimal.ZERO);
            if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(i.getDiscountMethod())) {
                //金额
                String content = "抵扣金额$" + i.getAmount();
                return minTotalAmount.compareTo(BigDecimal.ZERO) > 0 ? "满" + minTotalAmount + content : content;
            } else {
                //折扣
                BigDecimal disct = new BigDecimal(100).subtract(i.getDiscount().multiply(new BigDecimal(100)).setScale(0, RoundingMode.UP));
                String content = "打";
                if (disct.toString().endsWith("0")) {
                    content = content + disct.toString().replace("0", "") + "折";
                } else if (disct.compareTo(new BigDecimal(10)) < 0) {
                    content = content + disct.divide(new BigDecimal(10), 1, RoundingMode.UP) + "折";
                } else {
                    content = content + disct + "折";
                }
                return minTotalAmount.compareTo(BigDecimal.ZERO) > 0 ? "满" + minTotalAmount + content : content;
            }
        } else if (CouponTypeEnum.WIN_COUPON.getCode().equals(i.getCouponType())) {
            return CouponTypeEnum.WIN_COUPON.getDescription();
        } else if (CouponTypeEnum.DOUBLE_REWARD_COUPON.getCode().equals(i.getCouponType())) {
            return CouponTypeEnum.DOUBLE_REWARD_COUPON.getDescription();
        }
        return "未知类型卷内容";
    }

    /**
     * 已废弃，4.4.3之后可以删除
     */
    @Deprecated(since = "4.4.3")
    @Override
    public CouponUpdateDetailVo couponUpdateDetail(Long id) {
        CouponUpdateDetailVo couponUpdateDetailVo = new CouponUpdateDetailVo();
        Coupon coupon = couponDao.selectById(id);
        BeanUtils.copyProperties(coupon, couponUpdateDetailVo);
        if (CouponTypeEnum.AMAZON_COUPON.getCode().equals(coupon.getCouponType())) {
            couponUpdateDetailVo.setDiscount(coupon.getDiscount().multiply(new BigDecimal(100)));
        }
        List<ActivityCouponConfig> typeConfigs = activityCouponConfigService.list(new QueryWrapper<ActivityCouponConfig>()
                .eq("is_delete", YesNoStatus.NO.getCode())
                .eq("coupon_id", coupon.getId()));
        if (!CollectionUtils.isEmpty(typeConfigs)) {
            if (typeConfigs.size() == 1) {
                ActivityCouponConfig activityCouponConfig = typeConfigs.get(0);
                if (activityCouponConfig.getType() == 1) {
                    couponUpdateDetailVo.setActivityType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                }
                if (activityCouponConfig.getType() == 2) {
                    couponUpdateDetailVo.setActivityIds(Collections.singletonList(Long.valueOf(activityCouponConfig.getCouponConfig())));
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(activityNew.getActivityType());
                    couponUpdateDetailVo.setNewActivityType(activityNew.getMainType() == null ? MainActivityTypeEnum.OLD.getType() : activityNew.getMainType());
                    if (!MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
                        couponUpdateDetailVo.setNewActivityId(Collections.singletonList(Long.valueOf(activityCouponConfig.getCouponConfig())));
                    }
                }
                if (activityCouponConfig.getType() == 3) {
                    couponUpdateDetailVo.setTaskIds(Collections.singletonList(Long.valueOf(activityCouponConfig.getCouponConfig())));
                    couponUpdateDetailVo.setCouponLimitationType(-4);
                }
                if (activityCouponConfig.getType() == 5) {
                    couponUpdateDetailVo.setNewActivityType(activityCouponConfig.getCouponConfig());
                }
                if (activityCouponConfig.getType() == 6) {
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setNewActivityId(collect);
//					ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setNewActivityType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
                }
            } else {
                // 使用范围 list ids
                ActivityCouponConfig activityCouponConfig = typeConfigs.get(0);
                if (activityCouponConfig.getType() == 1) {
                    couponUpdateDetailVo.setActivityType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                }
                if (activityCouponConfig.getType() == 2) {
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setActivityIds(collect);
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(activityNew.getActivityType());
                    couponUpdateDetailVo.setNewActivityType(activityNew.getMainType() == null ? MainActivityTypeEnum.OLD.getType() : activityNew.getMainType());
                    if (!MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
                        couponUpdateDetailVo.setNewActivityId(collect);
                    }
                }
                if (activityCouponConfig.getType() == 3) {
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setTaskIds(collect);
                    couponUpdateDetailVo.setCouponLimitationType(-4);
                }
                if (activityCouponConfig.getType() == 5) {
                    couponUpdateDetailVo.setNewActivityType(activityCouponConfig.getCouponConfig());
                }
                if (activityCouponConfig.getType() == 6) {
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setNewActivityId(collect);
//					ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setNewActivityType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
                }
            }
        }
        //封装币种金额
//        couponUpdateDetailVo.setCurrencyList(getCurrencyList(id));
        // 国际化数据
        List<CouponI18n> couponI18ns = couponI18nService.selectCouponId(id);
        if (!CollectionUtils.isEmpty(couponI18ns)) {
            couponUpdateDetailVo.setValues(couponI18ns);
        }
        return couponUpdateDetailVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean quotaAdd(CouponUpdateStatusReq couponUpdateStatusReq) {
        Coupon coupon = couponDao.selectById(couponUpdateStatusReq.getId());
        if (CouponValidityTypeEnum.RANGE.getCode() == coupon.getExpiryType()) {
            if (DateUtil.compare(ZonedDateTime.now(), coupon.getGmtEnd()) > 0) {
                throw new BaseException("卷生效结束时间需要更改", 500);
            }
        }
        coupon.setQuota(coupon.getQuota() + couponUpdateStatusReq.getQuotaAdd());
        coupon.setGmtModified(ZonedDateTime.now());
        return couponDao.updateById(coupon) > 0;
    }

    @Override
    public String getCouponValidityPeriod(Coupon i) {
        if (CouponValidityTypeEnum.DAYS.getCode() == i.getExpiryType()) {
            return "领取后" + i.getValidDays() + "天";
        }
        if (CouponValidityTypeEnum.RANGE.getCode() == i.getExpiryType()) {
            return DateUtil.formateDateStr(i.getGmtStart(), DateUtil.YYYY_MM_DD_HH_MM_SS) + "~" + DateUtil.formateDateStr(i.getGmtEnd(), DateUtil.YYYY_MM_DD_HH_MM_SS);
        }
        return "未定义有效期类型";
    }

    @Override
    public void getUserActiveCouponFiledByCouponId(Long couponId, UserActiveCouponVo userActiveCouponVo) {
        Coupon coupon = couponDao.selectById(couponId);
        userActiveCouponVo.setName(coupon.getName());
        userActiveCouponVo.setCouponType(coupon.getCouponType());
        userActiveCouponVo.setCouponValidityPeriod(this.getCouponValidityPeriod(coupon));
        userActiveCouponVo.setCouponContent(this.getCouponContent(coupon));
        userActiveCouponVo.setTitle(coupon.getTitle());
        userActiveCouponVo.setLimitCount(coupon.getLimitCount());
    }

    @Override
    public void addQuotaSend(Long id) {
        couponDao.addQuotaSend(id);
    }

    @Override
    public List<Long> getLoseEfficacyCouponIds(List<Long> couponIds) {
        return couponDao.selectLoseEfficacyCouponIds(couponIds);
    }

    @Override
    public List<CanadaCouponVo> getCanadaTempCoupons(String languageCode, Long userId) {
        List<String> ids = redisTemplate.opsForList().range(RedisConstants.CANADA_TEMP_COUPONS, 0, -1);
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList();
        }

        ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(userId);

        //查询指定优惠券多币种价格和语言i18n
        List<Coupon> list = couponDao.selectCurrencyCouponI18nByIds(ids, languageCode, accountEntity.getCurrencyCode());

        List<CanadaCouponVo> collect = list.stream().map(k -> {
            CanadaCouponVo vo = new CanadaCouponVo();
            vo.setCouponId(k.getId());
            vo.setTitle(k.getTitle());
            vo.setAmount(k.getAmount());
            vo.setPic("https://pitpat-oss.s3.us-east-2.amazonaws.com/202311/isw13CZToJPq5847.jpg");
            return vo;
        }).collect(Collectors.toList());


        return collect;
    }

    @Override
    public void updateQuota(Long id, Long count) {
        couponDao.updateQuota(id, count);
    }

    @Override
    public List<Coupon> findCouponList() {
        return couponDao.selectList(
                new QueryWrapper<Coupon>().lambda()
                        .select(Coupon::getId, Coupon::getExchangeCode, Coupon::getName)
                        .eq(Coupon::getIsDelete, 0)
                        .eq(Coupon::getStatus, 1)
        );
    }

    @Override
    public List<Coupon> selectCouponByIdRegisterAfterConfig(List<MessageTaskMsg> msgList) {
        boolean notEmpty = !CollectionUtils.isEmpty(msgList);
        if (notEmpty) {
            return couponDao.selectList(new QueryWrapper<Coupon>().in(!CollectionUtils.isEmpty(msgList), "id", msgList.stream().map(MessageTaskMsg::getContent).collect(Collectors.toList())));
        }
        return Lists.newArrayList();
    }

    @Override
    public List<Coupon> selectCouponByType(Integer type) {
        return couponDao.selectCouponByType(type);

    }

    @Override
    public Map<Long, Coupon> selectCouponScoreDto(List<ActivityUserScoreDto> couponList) {
        return couponDao.selectCouponScoreDto(couponList);
    }

    @Override
    public Map<Long, Coupon> selectCouponByWeekTaskConfigTypeId(List<WeekTaskConfigType> all) {
        return couponDao.selectCouponByWeekTaskConfigTypeId(all);
    }

    @Override
    public Coupon findOneByStatus(Long couponId, int status) {
        return couponDao.selectOne(
                new QueryWrapper<Coupon>().lambda()
                        .eq(Coupon::getId, couponId)
                        .eq(Coupon::getIsDelete, 0)
                        .eq(Coupon::getStatus, 1)
                        .last("limit 1"));
    }

    @Override
    public void updateBatch(Set<Long> couponIdSet, Coupon coupon) {
        couponDao.update(coupon, new QueryWrapper<Coupon>().lambda()
                .in(Coupon::getId, couponIdSet));
    }

    @Override
    public List<Coupon> findEndListByStatusAndExpiryType(int expiryType, int status) {
        return couponDao.selectList(new QueryWrapper<Coupon>().lambda()
                .select(Coupon::getId, Coupon::getGmtEnd)
                .eq(Coupon::getExpiryType, 2)
                .eq(Coupon::getIsDelete, 0)
                .eq(Coupon::getStatus, 1));
    }

    @Override
    public List<Coupon> findListByStatus(int status) {
        return couponDao.selectList(new QueryWrapper<Coupon>().lambda()
                .eq(Coupon::getIsDelete, 0)
                .eq(Coupon::getStatus, 1)
        );
    }

    @Override
    public List<Coupon> findListByIds(Collection<Long> couponIds) {
        return couponDao.selectBatchIds(couponIds);
    }

    @Override
    public Coupon findOneByQueryWrapper(LambdaQueryWrapper<Coupon> queryWrapper) {
        return couponDao.selectOne(queryWrapper);
    }

    @Override
    public List<Coupon> findList(CouponQuery query) {
        Wrapper<Coupon> wrapper = buildQueryWrapper(query);
        return couponDao.selectList(wrapper);
    }

    @Override
    public Coupon findOneByQuery(CouponQuery query) {
        Wrapper<Coupon> wrapper = buildQueryWrapper(query);
        return couponDao.selectOne(wrapper, false);
    }

    @Override
    public Long create(Coupon coupon) {
        //必要的业务检查
        int affectedRow = couponDao.insert(coupon);
        log.info("创建优惠券模板,coupon={}, affected row={}", coupon, affectedRow);
        return coupon.getId();
    }

    @Override
    public Long update(Coupon coupon) {
        Coupon existed = couponDao.selectById(coupon.getId());
        if (Objects.isNull(existed)) {
            throw new RuntimeException("优惠券模板不存在");
        }
        int affectedRow = couponDao.updateById(coupon);
        log.info("更新优惠券模板,coupon={}, affected row={}", coupon, affectedRow);
        return coupon.getId();
    }

    @Override
    public Coupon findById(Long id) {
        return couponDao.selectById(id);
    }

    @Override
	public Coupon findDeletedById(Long couponId) {
		return couponDao.findDeletedById(couponId);
	}

	@Override
    public Page<Coupon> findPage(CouponsPageQuery pageQuery) {
        Wrapper<Coupon> queryWrapper = buildQueryWrapper(pageQuery);
        Page<Coupon> result = couponDao.selectPage(PageHelper.ofPage(pageQuery), queryWrapper);
        log.info("查询优惠券模板， pageQuery={}", pageQuery);
        return result;
    }

    /**
     * 批量失效券配置
     */
    @Override
    public void expireByCouponIds(List<Long> ids) {
        couponDao.expireByCouponIds(ids);
    }

    private Wrapper<Coupon> buildQueryWrapper(CouponQuery query) {
        return Wrappers.<Coupon>lambdaQuery()
                .eq(Objects.nonNull(query.getIsDelete()), Coupon::getIsDelete, query.getIsDelete())
                .eq(Objects.nonNull(query.getType()), Coupon::getType, query.getType())
                .eq(Objects.nonNull(query.getStatus()), Coupon::getStatus, query.getStatus())
                .eq(Objects.nonNull(query.getCouponId()), Coupon::getId, query.getCouponId())
                .eq(Objects.nonNull(query.getUseScope()), Coupon::getUseScope, query.getUseScope())
                .eq(Objects.nonNull(query.getCouponMainType()), Coupon::getCouponMainType, query.getCouponMainType())
                .eq(Objects.nonNull(query.getMallShowDetail()), Coupon::getMallShowDetail, query.getMallShowDetail())
                .le(Objects.nonNull(query.getReceiveStartLe()), Coupon::getReceiveStart, query.getReceiveStartLe())
                .gt(Objects.nonNull(query.getReceiveEndGt()), Coupon::getReceiveEnd, query.getReceiveEndGt())
                .le(Objects.nonNull(query.getGmtStartLe()), Coupon::getGmtStart, query.getGmtStartLe())
                .gt(Objects.nonNull(query.getGmtEndGt()), Coupon::getGmtEnd, query.getGmtEndGt())
                .in(!org.springframework.util.CollectionUtils.isEmpty(query.getExchangeCodes()), Coupon::getExchangeCode, query.getExchangeCodes())
                .in(!org.springframework.util.CollectionUtils.isEmpty(query.getCouponIds()), Coupon::getId, query.getCouponIds())
                .like(StringUtils.hasText(query.getCouponName()), Coupon::getTitle, query.getCouponName())
                .last(!org.springframework.util.CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }

    private static Wrapper<Coupon> buildQueryWrapper(CouponsPageQuery query) {
        LambdaQueryWrapper<Coupon> wrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(query.getIsRetainPop()) && query.getIsRetainPop()) {
            wrapper.apply("quota_send < quota");
            if (Boolean.TRUE.equals(query.getIsAllGoodsCoupon())) {
                wrapper.eq(Coupon::getUseScope, CouponConstant.UseScopeEnum.USE_SCOPE_1.getType());
            } else {
                wrapper.in(Coupon::getUseScope, List.of(CouponConstant.UseScopeEnum.USE_SCOPE_2.getType(), CouponConstant.UseScopeEnum.USE_SCOPE_3.getType()));
            }
        }
        wrapper.eq(Coupon::getIsDelete, 0)
                .eq(StringUtils.hasText(query.getExchangeCode()), Coupon::getExchangeCode, query.getExchangeCode())
                .in(!org.springframework.util.CollectionUtils.isEmpty(query.getExchangeCodes()), Coupon::getExchangeCode, query.getExchangeCodes())
                .le(Objects.nonNull(query.getReceiveStartLe()), Coupon::getReceiveStart, query.getReceiveStartLe())
                .le(Objects.nonNull(query.getReceiveEndLe()), Coupon::getReceiveEnd, query.getReceiveEndLe())
                .gt(Objects.nonNull(query.getReceiveEndGt()), Coupon::getReceiveEnd, query.getReceiveEndGt())
                .eq(Objects.nonNull(query.getStatus()), Coupon::getStatus, query.getStatus())
                .eq(Objects.nonNull(query.getCouponMainType()), Coupon::getCouponMainType, query.getCouponMainType())
                .like(StringUtils.hasText(query.getCountryCode()), Coupon::getCountryCode, query.getCountryCode())
                .last(!org.springframework.util.CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
        return wrapper;
    }
}
