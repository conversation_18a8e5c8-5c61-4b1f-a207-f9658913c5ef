package com.linzi.pitpat.data.activityservice.manager.console;

import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.request.SwitchAssistActivityRequest;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;

/**
 * 助力活动处理类
 *
 * <AUTHOR>
 * @date 2024/6/3 18:40
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AssistActivitityConsoleManager {

    private final AssistActivitityService assistActivitityService;
    private final ZnsRunActivityService runActivityService;

    /**
     * 助力活动上下架操作
     *
     * @param switchAssistActivityRequest
     * @return
     */
    public Result switchStatus(SwitchAssistActivityRequest switchAssistActivityRequest) {
        Long id = switchAssistActivityRequest.getId();
        if (id == null) {
            return CommonResult.fail("助力活动编码不能为空!");
        }
        Integer targetStatus = switchAssistActivityRequest.getTargetStatus();
        if (targetStatus == null) {
            return CommonResult.fail("助力活动上下架目标状态不能为空!");
        }
        AssistActivitity assistActivitity = assistActivitityService.selectAssistActivitityById(id);
        if (assistActivitity == null) {
            return CommonResult.fail("指定的助力活动不存在!");
        }
        ZonedDateTime startTime = assistActivitity.getStartTime();
        ZonedDateTime endTime = assistActivitity.getEndTime();
        // 状态，默认0,1：上架，-1下架
        ZonedDateTime now = ZonedDateTime.now();
        if (targetStatus == 1) {
            if (now.isBefore(startTime)) {
                return CommonResult.fail("助力活动未生效");
            }
            if (now.isAfter(endTime)) {
                return CommonResult.fail("助力活动已结束");
            }
        } else if (targetStatus == -1) {

            RunActivityQuery runActivityQuery = RunActivityQuery.builder()
                    .assistActivityId(id)
                    .isDelete(0).status(1).build();
            long count = runActivityService.findCount(runActivityQuery);
            if (count > 0) {
                return CommonResult.fail("下架前请将与此活动关联的官方活动下架");
            }
        }
        AssistActivitity updateAssistActivitity = new AssistActivitity();
        updateAssistActivitity.setId(id);
        updateAssistActivitity.setStatus(targetStatus);
        assistActivitityService.updateAssistActivitityById(updateAssistActivitity);
        return CommonResult.success();
    }

}
