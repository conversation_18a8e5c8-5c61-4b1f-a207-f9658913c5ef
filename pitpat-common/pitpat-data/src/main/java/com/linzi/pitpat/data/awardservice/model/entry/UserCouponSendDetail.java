package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 发放奖励批次用户关系表
 *
 * <AUTHOR>
 * @since 2023-04-20
 */

@Data
@NoArgsConstructor
@TableName("zns_user_coupon_send_detail")
public class UserCouponSendDetail implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendDetail:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                         // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";            // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";          // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";      // 最后修改时间
    public final static String coupon_id = CLASS_NAME + "coupon_id";            // 发放优惠券id
    public final static String user_id = CLASS_NAME + "user_id";                // 用户id
    public final static String batch_id = CLASS_NAME + "batch_id";              // 任务id
    public final static String gmt_send = CLASS_NAME + "gmt_send";              // 发送时间
    public final static String status_ = CLASS_NAME + "status";                 // 状态 【 0: 未发送 1 已发送 -1 发送失败】
    public final static String remarks_ = CLASS_NAME + "remarks";               // 标记备注
    public final static String version_ = CLASS_NAME + "version";               // 批次版本
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //发放优惠券id
    private Long couponId;
    //用户id
    private Long userId;
    //任务id
    private Long batchId;
    //发送时间
    private ZonedDateTime gmtSend;
    //状态 【 0: 未发送 1 已发送 -1 发送失败】
    private Integer status;
    //标记备注
    private String remarks;
    //用户邮箱
    private String emailAddress;
    //批次版本
    private Integer version;
    //发放金额
    private BigDecimal sendAmount;

    @Override
    public String toString() {
        return "UserCouponSendDetail{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",couponId=" + couponId +
                ",userId=" + userId +
                ",batchId=" + batchId +
                ",gmtSend=" + gmtSend +
                ",status=" + status +
                ",remarks=" + remarks +
                ",version=" + version +
                "}";
    }
}
