package com.linzi.pitpat.data.awardservice.dto.api;

import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class PaypalRequest {
    private Long userId;
    /**
     * 购买类型 类型："coupon":优惠券,"vip":会员,"scoreExchange":积分商城,"exchangeWear": 我的形象皮肤,"battlePass":新里程碑 ,"mallOrder":商城订单 ,"refundOrder":退款单,h5MallOrder:投流H5商城订单
     *
     * @see PayConstant.BuyTypeEnum
     */
    private String type;

    /**
     * 关联id
     * 当type=coupon优惠劵 refId=劵id
     * 当type=scoreExchange refId=兑换规则id
     * 当type=vip refId=会员商品id
     */
    private Long refId;

    /**
     * 金额
     */
    private String amount;


    /**
     * 相关信息 json形式
     * 当type=scoreExchange 将 app/my/score/couponExchange 请求参数转为json格式
     * 当type=exchangeWear /app/my/wear/exchange 同上
     */
    private String info;

}
