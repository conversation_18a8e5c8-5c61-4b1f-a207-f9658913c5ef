package com.linzi.pitpat.data.courseservice.model.query;

import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Builder;
import lombok.Getter;

import java.time.ZonedDateTime;


@Builder
@Getter
public class CourseQuery extends PageQuery {

    private Long isDelete;

    private Long id;

    private Integer status;

    private String courseName;
    private Long categoryId;
    private Integer courseType;
    private Integer recommended;
    private Integer difficulty;
    private Integer isPlusCourse;
    //最小的分类id
    private Integer minCategoryId;

    private ZonedDateTime minModifyStartTime;
    private ZonedDateTime maxModifyStartTime;

    private Integer isTest;
    /**
     * 设备类型
     *
     * @see EquipmentDeviceTypeEnum
     * @since 4.6.0
     */
    private Integer deviceType;
}
