package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 智能用户匹配 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.Count;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.GT;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LT;
import com.lz.mybatis.plugin.annotations.NE;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface MindUserMatchDao extends BaseMapper<MindUserMatch> {


    MindUserMatch selectMindUserMatchById(@Param("id") Long id);


    Long insertMindUserMatch(MindUserMatch mindUserMatch);


    int updateMindUserMatchById(MindUserMatch mindUserMatch);


    int deleteMindUserMatchById(@Param("id") Long id);


    @LIMIT
    MindUserMatch selectMindUserMatchByUserIdStatus(Long userId, Integer status);

    List<MindUserMatch> selectMindUserMatchStatusTargetMileageUserId(@GT Long id, Integer status, @IF Integer targetMileage, @NE Long userId, @IF Integer targetTime);

    @OrderByIdDescLimit_1
    MindUserMatch selectMindUserMatchByUserIdUniqueCode(Long userId, @IF String uniqueCode);


    List<MindUserMatch> selectMindUserMatchRuleByStatusIsRoot(Integer status, Integer isRobot);

    List<MindUserMatch> selectMindUserMatchByUserIdMatchUserIdStartTimeEndTimeStatus(Long userId, Long matchUserId,
                                                                                     @Column("gmt_create") @DateFormat @GE ZonedDateTime startTime, @DateFormat @Column("gmt_create") @LE ZonedDateTime endTime, @IN List<Integer> status);

    List<MindUserMatch> selectMindUserMatchByActivityId(Long activityId);

    List<MindUserMatch> selectMindUserMatchByCondition(@By @Column("status") Integer newStatus, @GE @By @DateFormat @Column("gmt_create") ZonedDateTime activityStartTime,
                                                       @LT @By @DateFormat @Column("gmt_create") ZonedDateTime activityEndTime);

    @LIMIT
    MindUserMatch selectMindUserMatchByActivityIdUserId(Long activityId, Long userId);

    List<MindUserMatch> selectMindUserMatchByActivityIdStatusIsRobot(Long activityId, Integer status, Integer isRobot);

    @LIMIT
    MindUserMatch selectMindUserMatchByUniqueCodeUserIdStatus(String uniqueCode, Long userId, Integer status);

    Integer countRunRobCountByActivityIdStatusRountIdRountType(Long activityId, Integer status, @IF Long routeId, Integer routeType);

    List<MindUserMatch> selectRunRobCountByActivityIdStatusRountIdRountType(Long activityId, Integer status, @IF Long routeId, Integer routeType);


    @OrderByIdDescLimit_1
    MindUserMatch selectMindUserMatchByActivityIdLast3Minites(Long activityId, @DateFormat @GT ZonedDateTime gmtCreate);

    @OrderByIdDescLimit_1
    MindUserMatch selectMindUserMatchByActivityIdUserIdOrderByIdDesc(Long activityId, Long userId);

    @Count
    int countMindUserMatchByActivityIdStatus(Long activityId, @IN List<Integer> status);

    @OrderByIdDescLimit_1
    MindUserMatch selectMindUserMatchByUserIdStatusList(Long userId, @IN List<Integer> status);


    MindUserMatch selectMindUserMatchByIdStatusList(@Param("id") Long id, @Param("userId") Long userId, @Param("status") List<Integer> status);


    MindUserMatch selectMindUserMatchByIdContainsIsDelete(@Param("id") Long id);

    int updateMindUserMatchIsDeleteById(Integer isDelete, Long id);

    MindUserMatch selectMindUserMatchByActivityIdUserIdContainsIsDelete(@Param("activityId") Long activityId, @Param("userId") Long userId);

    Integer updateMindUserMatchStatusById(ZonedDateTime gmtModified, Integer status, Long id);

    int updateMindUserMatchByOn(ZonedDateTime gmtModified, Integer status, String remark, @LT ZonedDateTime gmtCreate);

    List<String> selectListStringExeSql(@Param("selectAllTable") String selectAllTable);

    Integer exeSql(@Param("createTable") String createTable);

    List<MindUserMatch> selectMindUserMatchByMatchId(Long mindUserMatchId);

    MindUserMatch getLastByUserIdContainsDelete(Long userId);
}
