package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 积分配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.data.awardservice.mapper.ScoreConfigDao;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig;
import com.linzi.pitpat.data.awardservice.model.resp.ScoreConfigResp;
import com.linzi.pitpat.data.awardservice.service.ScoreConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;

@Service
public class ScoreConfigServiceImpl extends ServiceImpl<ScoreConfigDao, ScoreConfig> implements ScoreConfigService {


    @Autowired
    private ScoreConfigDao scoreConfigDao;

    @Override
    public ScoreConfigResp selectScoreConfigByUserId(Long id, String calStyle, Long userId, ZonedDateTime startTime, ZonedDateTime endTime) {
        return scoreConfigDao.selectScoreConfigByUserId(id, calStyle, userId, startTime, endTime);
    }

    @Override
    public ScoreConfig selectScoreConfigById(Long id) {
        return scoreConfigDao.selectScoreConfigById(id);
    }

}
