package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 奖励配置金额表
 *
 * <AUTHOR>
 * @since 2023-07-14
 */

@Data
@NoArgsConstructor
@TableName("zns_award_config_amount")
public class AwardConfigAmount implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                             // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";                //
    public final static String gmt_create = CLASS_NAME + "gmt_create";              //
    public final static String award_config_id = CLASS_NAME + "award_config_id";    // 配置ID，关联zns_award_config
    public final static String amount_ = CLASS_NAME + "amount";                     // 金额
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    @TableLogic
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //配置ID，关联zns_award_config
    private Long awardConfigId;
    //金额
    private BigDecimal amount;
    /**
     * 金额比例
     */
    private BigDecimal proportion;

    @Override
    public String toString() {
        return "AwardConfigAmount{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",awardConfigId=" + awardConfigId +
                ",amount=" + amount +
                "}";
    }
}
