package com.linzi.pitpat.data.courseservice.service;
/**
 * <p>
 * 用户ai课程计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-25
 */

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linzi.pitpat.data.courseservice.mapper.UserAiCourseDao;
import com.linzi.pitpat.data.courseservice.model.entity.UserAiCourse;
import com.linzi.pitpat.data.courseservice.model.query.UserAiCourseQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class UserAiCourseServiceImpl implements UserAiCourseService {


    @Autowired
    private UserAiCourseDao userAiCourseDao;


    @Override
    public UserAiCourse selectUserAiCourseById(Long id) {
        return userAiCourseDao.selectUserAiCourseById(id);
    }


    @Override
    public Long insertUserAiCourse(UserAiCourse userAiCourse) {
        return userAiCourseDao.insertUserAiCourse(userAiCourse);
    }


    @Override
    public Long insertOrUpdateUserAiCourse(UserAiCourse userAiCourse) {
        return userAiCourseDao.insertOrUpdateUserAiCourse(userAiCourse);
    }


    @Override
    public int updateUserAiCourseById(UserAiCourse userAiCourse) {
        return userAiCourseDao.updateUserAiCourseById(userAiCourse);
    }


    @Override
    public int updateCoverUserAiCourseById(UserAiCourse userAiCourse) {
        return userAiCourseDao.updateCoverUserAiCourseById(userAiCourse);
    }


    @Override
    public int deleteUserAiCourseById(Long id) {
        return userAiCourseDao.deleteUserAiCourseById(id);
    }

    @Override
    public void completeCourse(Long userId, Long courseId) {
        if (Objects.isNull(courseId) || courseId <= 0) {
            return;
        }
        UserAiCourse userAiCourse = userAiCourseDao.selectByCourseId(userId, courseId);
        if (Objects.isNull(userAiCourse)) {
            log.info("completeCourse end, no join UserAiCourse");
            return;
        }
        if (userAiCourse.getStatus() == 1) {
            log.info("completeCourse end, UserAiCourse has completed");
            return;
        }
        userAiCourseDao.updateStatus(1, ZonedDateTime.now(), userAiCourse.getId());
    }

    @Override
    public List<UserAiCourse> findList(UserAiCourseQuery build) {
        return userAiCourseDao.selectList(buildQueryWrapper(build));
    }

    @Override
    public void updateBatchById(List<UserAiCourse> aiCoursesOld) {
        userAiCourseDao.updateById(aiCoursesOld);
    }

    @Override
    public boolean saveBatch(List<UserAiCourse> lists) {
        userAiCourseDao.insert(lists);
        return true;
    }

    @Override
    public void delectByQuery(UserAiCourseQuery query) {
        userAiCourseDao.delete(buildQueryWrapper(query));
    }

    private static Wrapper<UserAiCourse> buildQueryWrapper(UserAiCourseQuery query) {
        return Wrappers.<UserAiCourse>lambdaQuery()
                .eq(Objects.nonNull(query.getUserId()), UserAiCourse::getUserId, query.getUserId())
                .eq(Objects.nonNull(query.getIsDelete()), UserAiCourse::getIsDelete, query.getIsDelete())
                .eq(Objects.nonNull(query.getBaseInfoId()), UserAiCourse::getBaseinfoId, query.getBaseInfoId())
                ;
    }

}
