package com.linzi.pitpat.data.courseservice.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 课程表
 *
 * <AUTHOR>
 * @date 2021-09-30 16:42:14
 */
@TableName("zns_course")
@Data
@NoArgsConstructor
public class ZnsCourseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    public final static String CLASS_NAME = "com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                                     // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";                                        // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";                                    // 创建时间
    public final static String modify_time = CLASS_NAME + "modify_time";                                    // 最后修改时间
    public final static String creator_ = CLASS_NAME + "creator";                                           // 创建人
    public final static String modified_ = CLASS_NAME + "modified";                                         // 修改人
    public final static String course_name = CLASS_NAME + "course_name";                                    // 课程名称
    public final static String category_id = CLASS_NAME + "category_id";                                    // 类目id
    public final static String category_name = CLASS_NAME + "category_name";                                // 类目名称
    public final static String recommended_ = CLASS_NAME + "recommended";                                   // 是否推荐，默认：0，0：不推荐，1：热门课程，2：更多课程
    public final static String difficulty_ = CLASS_NAME + "difficulty";                                     // 难度
    public final static String course_duration = CLASS_NAME + "course_duration";                            // 课程时长（s）
    public final static String estimated_heat_consumption = CLASS_NAME + "estimated_heat_consumption";      // 预计消耗热量（kcal）
    public final static String participants_number = CLASS_NAME + "participants_number";                    // 参与人数
    public final static String course_desc = CLASS_NAME + "course_desc";                                    // 课程说明
    public final static String training_suggestions = CLASS_NAME + "training_suggestions";                  // 训练建议
    public final static String intended_for = CLASS_NAME + "intended_for";                                  // 适用人群
    public final static String taboo_population = CLASS_NAME + "taboo_population";                          // 禁忌人群
    public final static String default_participants_number = CLASS_NAME + "default_participants_number";    // 默认参与人数
    public final static String background_picture = CLASS_NAME + "background_picture";                      // 背景图
    public final static String status_ = CLASS_NAME + "status";                                             // 状态,0：关闭，1：开启
    public final static String route_id = CLASS_NAME + "route_id";                                          // 路线id
    public final static String is_test = CLASS_NAME + "is_test";                                            // 是否测试课程，1：是，0：否
    public final static String course_type = CLASS_NAME + "course_type";                                    // 课程类型，默认0，0：变速跑，1：视频
    public final static String equipment_type = CLASS_NAME + "equipment_type";                              // 设备类型，逗号隔开
    public final static String recommend_place_sort = CLASS_NAME + "recommend_place_sort";                  // 推荐位置排序
    public final static String sort_ = CLASS_NAME + "sort";                                                 // 排序
    public final static String recommend_poster = CLASS_NAME + "recommend_poster";                          // 推荐宣传图
    public final static String age_desc = CLASS_NAME + "age_desc";                                          // 年龄人群描述
    public final static String equipment_type_name = CLASS_NAME + "equipment_type_name";                    // 设备类型名称，逗号隔开
    public final static String actual_training_duration = CLASS_NAME + "actual_training_duration";          // 实际训练时长
    public final static String cover_mark = CLASS_NAME + "cover_mark";          // 封面角标
    public final static String show_participants_number = CLASS_NAME + "show_participants_number";          // 展示参与人数


    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 最后修改时间
     */
    private ZonedDateTime modifyTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人
     */
    private String modified;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 类目名称
     */
    private String categoryName;
    /**
     * 是否推荐，1：热门课程，2：更多课程，0：不推荐，默认：0
     */
    private Integer recommended;
    /**
     * 状态,0：关闭，1：开启
     */
    private Integer status;
    /**
     * 难度
     */
    private Integer difficulty;
    /**
     * 课程时长（s）
     */
    private Integer courseDuration;
    /**
     * 预计消耗热量（kcal）
     */
    private Integer estimatedHeatConsumption;
    /**
     * 参与人数
     */
    private Integer participantsNumber;
    /**
     * 课程说明
     */
    private String courseDesc;
    /**
     * 训练建议
     */
    private String trainingSuggestions;
    /**
     * 适用人群
     */
    private String intendedFor;
    /**
     * 禁忌人群
     */
    private String tabooPopulation;
    /**
     * 背景图/封面
     */
    private String backgroundPicture;
    /**
     * 背景视频/封面
     */
    private String backgroundVideo;
    /**
     * 默认参与人数
     */
    private Integer defaultParticipantsNumber;
    /**
     * 路线id
     */
    private Long routeId;
    /**
     * 路线类型，1:2D路线，2:3D路线
     */
    @TableField(exist = false)
    private Integer routeType;
    /**
     * 路线名称
     */
    @TableField(exist = false)
    private String routeTitle;
    /**
     * 路线缩略图
     */
    @TableField(exist = false)
    private String routeThumbnail;
    /**
     * 路线长度
     */
    @TableField(exist = false)
    private BigDecimal routeDistance;

    /**
     * 路线爬坡(m)
     */
    @TableField(exist = false)
    private BigDecimal routeClimb;

    /**
     * 是否测试课程
     */
    private Integer isTest;
    /**
     * 课程类型，默认0，0：变速跑，1：视频
     */
    private Integer courseType;
    /**
     * 设备类型id，逗号隔开
     * 0：无器械，1：跑步机，2：走步机，3：瑜伽垫
     */
    private String equipmentType;
    /**
     * 设备类型名称，逗号隔开
     */
    private String equipmentTypeName;
    /**
     * 推荐位置排序
     */
    private Integer recommendPlaceSort;
    /**
     * 推荐宣传图
     */
    private String recommendPoster;

    private Integer sort;
    /**
     * 年龄描述
     */
    private String ageDesc;
    /**
     * 课程视频
     */
    private String courseVideo;
    /**
     * 实际训练时长
     */
    private Integer actualTrainingDuration;

    /**
     * 封面角标
     */
    private String coverMark;
    /**
     * 是否是vip课程：0：不是；1：是
     */
    private Integer isPlusCourse;
    //展示参与人数
    private Integer showParticipantsNumber;

    /**
     * 课程默认语言
     *
     * @see I18nConstant.LanguageCodeEnum
     */
    private String defaultLangCode;
}
