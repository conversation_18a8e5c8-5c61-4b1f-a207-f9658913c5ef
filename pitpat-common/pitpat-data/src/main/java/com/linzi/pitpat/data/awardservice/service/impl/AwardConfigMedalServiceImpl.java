package com.linzi.pitpat.data.awardservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.awardservice.mapper.AwardConfigMedalMapper;
import com.linzi.pitpat.data.awardservice.model.entity.AwardConfigMedalDo;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigMedalPageQuery;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigMedalQuery;
import com.linzi.pitpat.data.awardservice.service.AwardConfigMedalService;
import java.util.List;
import java.util.Objects;

import com.linzi.pitpat.lang.Query;
import com.linzi.pitpat.framework.db.mybatis.wrapper.QueryWrapperBuilder;
import com.linzi.pitpat.framework.db.mybatis.wrapper.UpdateWrapperBuilder;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 *
 * 奖励配置勋章表 服务实现类
 *
 * @since 2024-04-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AwardConfigMedalServiceImpl  implements AwardConfigMedalService {
    //使用构造器初始化依赖
    private final AwardConfigMedalMapper awardConfigMedalMapper;

    @Override
    public Long create(AwardConfigMedalDo awardConfigMedal) {
        //必要的业务检查
        int affectedRow = awardConfigMedalMapper.insert(awardConfigMedal);
        log.info("创建奖励配置勋章表,user={}, affected row={}", awardConfigMedal, affectedRow);
        return awardConfigMedal.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    @Caching(evict = {
            //@CacheEvict(value = UserCacheName.USER_KEY, key = "#awardConfigMedal.id"),
    })
    public Long update(AwardConfigMedalDo awardConfigMedal) {
        AwardConfigMedalDo existed = findById(awardConfigMedal.getId());
        if (Objects.isNull(existed)) {
            throw new RuntimeException("奖励配置勋章表不存在");
        }
        int affectedRow = awardConfigMedalMapper.updateById(awardConfigMedal);
        log.info("更新奖励配置勋章表,user={}, affected row={}", awardConfigMedal, affectedRow);
        return awardConfigMedal.getId();
    }

    /**
     * @see  com.linzi.pitpat.framework.db.mybatis.wrapper.annotion.UpdateColumn
     */
    @Override
    public Long updateSelective(AwardConfigMedalDo awardConfigMedal){
        UpdateWrapper<AwardConfigMedalDo> wrapper = UpdateWrapperBuilder.build(awardConfigMedal);

        int affectedRow = awardConfigMedalMapper.update(wrapper);
        log.info("部分更新奖励配置勋章表,user={}, affected row={}", awardConfigMedal, affectedRow);
        return awardConfigMedal.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@CacheEvict(value = UserCacheName.USER_KEY, key = "#id")
    public boolean deleteById(Long id) {
        AwardConfigMedalDo existed = findById(id);
        if (Objects.isNull(existed)) {
            throw new RuntimeException("奖励配置勋章表不存在");
        }
        //awardConfigMedalMapper.deleteById(id);

        LambdaUpdateWrapper<AwardConfigMedalDo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq( AwardConfigMedalDo::getId, id);
        updateWrapper.set( AwardConfigMedalDo::getIsDelete,1L);
        awardConfigMedalMapper.update(updateWrapper);

        log.info("删除奖励配置勋章表，id={}", id);
        return true;
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@Cacheable(value = UserCacheName.USER_KEY, key = "#id")
    public AwardConfigMedalDo findById(Long id) {
        return awardConfigMedalMapper.selectById(id);
    }

    @Override
    public AwardConfigMedalDo findByQuery(AwardConfigMedalQuery query) {
        //TODO 不建议第二个参数设置为 false, 否则如果数据异常，难以发现
        return awardConfigMedalMapper.selectOne(buildQueryWrapper(query), false);
    }

    @Override
    public List<AwardConfigMedalDo> findList(AwardConfigMedalQuery query) {
        Wrapper<AwardConfigMedalDo> wrapper = buildQueryWrapper(query);
        List<AwardConfigMedalDo> AwardConfigMedals = awardConfigMedalMapper.selectList(wrapper);
        log.info("查询奖励配置勋章表列表， query={}", query);
        return AwardConfigMedals;
    }

    @Override
    public Page<AwardConfigMedalDo> findPage(AwardConfigMedalPageQuery pageQuery) {
        Wrapper<AwardConfigMedalDo> queryWrapper = buildQueryWrapper(pageQuery);
        Page<AwardConfigMedalDo> result = awardConfigMedalMapper.selectPage(PageHelper.ofPage(pageQuery), queryWrapper);
        log.info("查询奖励配置勋章表列表， pageQuery={}", pageQuery);
        return result;
    }

    @Override
    public boolean batchCreate(List<AwardConfigMedalDo> awardConfigMedalList) {
        //TODO 不用删掉此方法
        List<BatchResult> affectList = awardConfigMedalMapper.insert(awardConfigMedalList);
        log.info("批量新增奖励配置勋章表列表， pageQuery={}， affect Row={}", awardConfigMedalList, affectList.size());
        return true;
    }

    @Override
    public boolean batchUpdate(List<AwardConfigMedalDo> awardConfigMedalList) {
        //TODO 不用删掉此方法
         List<BatchResult> affectList = awardConfigMedalMapper.updateById(awardConfigMedalList);
        log.info("批量更新奖励配置勋章表列表， pageQuery={}， affect Row={}", awardConfigMedalList, affectList.size());
        return true;
    }

    @Override
    public AwardConfigMedalDo findByAwardConfigId(Long awardConfigId) {
        LambdaQueryWrapper<AwardConfigMedalDo> wrapper = Wrappers.<AwardConfigMedalDo>lambdaQuery()
                .eq(AwardConfigMedalDo::getAwardConfigId, awardConfigId).eq(AwardConfigMedalDo::getIsDelete, 0)
                .last("limit 1");
        return awardConfigMedalMapper.selectOne(wrapper);
    }

    /**
     * 通用的条件构造器
     * if using this method ,you should delete  buildQueryWrapper(AwardConfigMedalQuery query) and buildQueryWrapper(AwardConfigMedalPageQuery query)
     * @param query query condition
     * @return Wrapper
     * @param <Q> Q extends Query
     */
    private static <Q extends Query> Wrapper<AwardConfigMedalDo> buildQueryWrapper(Q query) {
        QueryWrapper<AwardConfigMedalDo> wrapper = QueryWrapperBuilder.build(query, AwardConfigMedalDo.class);
        //wrapper.last("");
        //wrapper.groupBy("");
        return wrapper;
    }

    private static Wrapper<AwardConfigMedalDo> buildQueryWrapper(AwardConfigMedalQuery query) {
        return Wrappers.<AwardConfigMedalDo>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), AwardConfigMedalDo::getId, query.getId())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }

    private static Wrapper<AwardConfigMedalDo> buildQueryWrapper(AwardConfigMedalPageQuery query) {
        return Wrappers.<AwardConfigMedalDo>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), AwardConfigMedalDo::getId, query.getId())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }
}
