package com.linzi.pitpat.data.communityservice.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.communityservice.dto.CommunityContentVo;
import com.linzi.pitpat.data.communityservice.dto.CommunityI18nDto;
import com.linzi.pitpat.data.communityservice.dto.ContentPublishBot;
import com.linzi.pitpat.data.communityservice.dto.RedirectVo;
import com.linzi.pitpat.data.communityservice.dto.console.ContentBindTopicInfo;
import com.linzi.pitpat.data.communityservice.dto.console.request.CommunityRobotPostChangeStatusRequestDto;
import com.linzi.pitpat.data.communityservice.dto.console.request.CommunityRobotPostDeleteRequestDto;
import com.linzi.pitpat.data.communityservice.model.entity.CommunityTopicDo;
import com.linzi.pitpat.data.communityservice.model.query.CommunityContentRobotPostPageQuery;
import com.linzi.pitpat.data.communityservice.service.CommunityCommentService;
import com.linzi.pitpat.data.communityservice.service.CommunityTopicContentRelationService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.CommunityUserTypeEnum;
import com.linzi.pitpat.data.listener.QueueConstants;
import com.linzi.pitpat.data.systemservice.dto.request.CommunityContentPo;
import com.linzi.pitpat.data.systemservice.dto.request.CommunityContentRobotPostRequestDto;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.mapper.CustomH5Dao;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.model.entity.CustomH5;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.response.CommunityContentPicResponseDto;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContent;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContentI18nEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.CommunityQuery;
import com.linzi.pitpat.data.userservice.service.CommunityContentI18nService;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.file.FileUtils;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class CommunityContentBusinessImpl implements CommunityContentBusiness {

    private final static Integer PUBLISHED = 2;
    private final static Integer HIDDEN = 3;
    @Value("${zns.config.rabbitQueue.delayed_content_publish_exchange_name}")
    private String delay_exchange_name;

    @Autowired
    private CommunityContentService communityContentService;
    @Autowired
    private CommunityContentI18nService communityContentI18nService;

    @Autowired
    private ZnsCourseService znsCourseService;

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private CustomH5Dao customH5Dao;

    @Resource
    private AppRouteConfigService appRouteConfigService;

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CommunityTopicContentRelationService communityTopicContentRelationService;

    @Autowired
    private CommunityCommentService communityCommentService;

    @Autowired
    private ClubService clubService;

    @Autowired
    private QueueMessageService queueMessageService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdate(CommunityContentVo vo) {
        CommunityContent content = contentVoToDo(vo);
        if (!CollectionUtils.isEmpty(vo.getI18nList())) {
            CommunityI18nDto i18nDto = vo.getI18nList().stream().filter(item -> vo.getDefaultLangCode().equals(item.getLanguageCode())).findFirst().orElse(vo.getI18nList().get(0));
            Long runActivityId = i18nDto.getRunActivityId();
            if (Objects.nonNull(runActivityId) && runActivityId == 0) {
                throw new BaseException("活动不可用，请重新选择");
            }
            BeanUtil.copyPropertiesIgnoreNull(i18nDto, content);
        }
        communityContentService.insertOrUpdateCommunityContent(content);
        //保存新增社区多语言内容
        addOrUpdateI18nList(content.getId(), vo.getI18nList());
        //修改话题关系
        List<ContentBindTopicInfo> topicInfos = vo.getTopicInfos();
        communityTopicContentRelationService.reFixTopicRelation(content.getId(), null);
        if (!CollectionUtils.isEmpty(topicInfos)) {
            List<Long> topicIds = topicInfos.stream().map(ContentBindTopicInfo::getTopicId).collect(Collectors.toList());
            communityTopicContentRelationService.reFixTopicRelation(content.getId(), topicIds);
        }
        //定时发布
        if (Objects.isNull(vo.getId()) && vo.getPublishType() == 1) {
            ZonedDateTime publishDate = content.getPublishExpectTime();

            //计算预计发布时间还剩ms
            long millis = publishDate.toInstant().toEpochMilli() - ZonedDateTime.now().toInstant().toEpochMilli();
            log.info("延时时间-》{}", millis);
            //发布时间在3s内直接发布，避免最后一步消息先消费风险
            if (millis <= 3 * 1000) {
                //立即发布
                CommunityContent queryContent = communityContentService.selectCommunityContentById(content.getId());
                queryContent.setPublishStatus(PUBLISHED);
                queryContent.setPublishRealTime(ZonedDateTime.now());
                communityContentService.update(queryContent);
                log.info("立即发布的contentId:{}", content.getId());
            }
            //距离发布时间小于6h发入延时交换机
            else if (millis <= 6 * 60 * 60 * 1000) {
                log.info("内容发布延时ms:{}", millis);
                log.info("contentId:{}", content.getId());

                queueMessageService.sendDelayMessage(QueueConstants.contentDelayExchange, QueueConstants.contentPublishKey, content.getId(), millis);

                //缓存记录已发入交换机的content，避免消息重复发送
                String key = RedisConstants.COMMUNITY_CONTENT_PUBLISHED + content.getId();
                redisTemplate.opsForValue().set(key, "default", 6, TimeUnit.HOURS);
            }

        }
        return content.getId();
    }

    /**
     * 保存or新增社区多语言内容
     *
     * @param contentId 内容id
     * @param i18nList  多语言内容
     */
    private void addOrUpdateI18nList(Long contentId, List<CommunityI18nDto> i18nList) {
        if (CollectionUtils.isEmpty(i18nList)) {
            return;
        }
        //物理删除多语言内容
        communityContentI18nService.deleteByContentId(contentId);

        //保存内容
        for (CommunityI18nDto communityI18nDto : i18nList) {
            CommunityContentI18nEntity contentI18nEntity = BeanUtil.copyBean(communityI18nDto, CommunityContentI18nEntity.class);
            contentI18nEntity.setContentId(contentId);
            I18nConstant.LanguageCodeEnum languageCodeEnum = I18nConstant.LanguageCodeEnum.findByCode(communityI18nDto.getLanguageCode());
            if (languageCodeEnum != null) {
                contentI18nEntity.setLanguageName(languageCodeEnum.getName());
            }
            contentI18nEntity.setPics(JsonUtil.writeString(communityI18nDto.getPics()));
            communityContentI18nService.insert(contentI18nEntity);
        }
    }

    @Override
    public Page<CommunityContentVo> list(CommunityContentPo po) {
        if (StringUtils.hasText(po.getUserCode())) {
            ZnsUserEntity user = znsUserService.findByUserCode(po.getUserCode());
            po.setUserId(Objects.nonNull(user) ? user.getId() : null);
        }
        LambdaQueryWrapper<CommunityContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommunityContent::getUserType, po.getUserType())
                .eq(po.getUserId() != null, CommunityContent::getUserId, po.getUserId())
                .eq(po.getContentType() != null, CommunityContent::getContentType, po.getContentType())
                .eq(po.getPublishType() != null, CommunityContent::getPublishType, po.getPublishType())
                .eq(po.getRedirectType() != null, CommunityContent::getRedirectType, po.getRedirectType())
                .eq(po.getHideStatus() != null, CommunityContent::getPublishStatus, po.getHideStatus())
                .like(StringUtils.hasText(po.getUserEmail()), CommunityContent::getUserEmail, po.getUserEmail())
                .like(StringUtils.hasText(po.getTitle()), CommunityContent::getTitle, po.getTitle())
                .orderByDesc(CommunityContent::getGmtCreate)
                .orderByDesc(CommunityContent::getTopSort);
        if (PUBLISHED.equals(po.getPublishStatus())) {
            wrapper.and(w -> {
                w.eq(CommunityContent::getPublishStatus, PUBLISHED)
                        .or().eq(CommunityContent::getPublishStatus, HIDDEN);
            });
        } else {
            wrapper.eq(po.getPublishStatus() != null, CommunityContent::getPublishStatus, po.getPublishStatus());
        }
        if (StringUtils.hasText(po.getUserCode())) {
            ZnsUserEntity user = znsUserService.findByUserCode(po.getUserCode());
            if (Objects.nonNull(user)) {
                wrapper.eq(CommunityContent::getUserId, user.getId());
            } else {
                wrapper.eq(CommunityContent::getUserId, -1);
            }
        }
        if (po.getTopicId() != null) {
            List<Long> contentIds = communityTopicContentRelationService.findByTopicId(po.getTopicId());
            if (!CollectionUtils.isEmpty(contentIds)) {
                wrapper.in(CommunityContent::getId, contentIds);
            }
        }
        if (StringUtils.hasText(po.getTopicName())) {
            List<Long> contentIds = communityTopicContentRelationService.findByTopicName(po.getTopicName());
            if (!CollectionUtils.isEmpty(contentIds)) {
                wrapper.in(CommunityContent::getId, contentIds);
            }
        }

        Page<CommunityContent> page = new Page<>(po.getPageNum(), po.getPageSize());
        Page<CommunityContent> pageList = communityContentService.pageList(page, wrapper);

        //重新组装page，保证do不会出现在controller
        Page<CommunityContentVo> pageVo = new Page<>(po.getPageNum(), po.getPageSize());
        pageVo.setTotal(pageList.getTotal());
        pageVo.setRecords(pageList.getRecords().stream().map(k -> contentDoToVO(k, false)).collect(Collectors.toList()));
        return pageVo;
    }

    @Override
    public CommunityContentVo detail(Long id) {
        return contentDoToVO(communityContentService.findById(id), true);
    }

    @Override
    public List<ContentPublishBot> queryBot() {

        SysConfig sysConfig = sysConfigService.selectSysConfigByKey("community.content.rot");
        String configValue = sysConfig.getConfigValue();
        List<Long> botIds = JsonUtil.readList(configValue, Long.class);
        List<ZnsUserEntity> userEntities = znsUserService.findByIds(botIds);
        List<ContentPublishBot> list = userEntities.stream().map(k -> {
            ContentPublishBot bot = new ContentPublishBot();
            bot.setUserId(k.getId());
            bot.setUserName(k.getFirstName());
            bot.setLastName(k.getLastName());
            bot.setHeadPortrait(k.getHeadPortrait());
            return bot;
        }).collect(Collectors.toList());
        return list;
    }

    @Override
    public void statusChange(Long id, Integer status) {
        CommunityContent communityContent = communityContentService.selectCommunityContentById(id);
        communityContent.setPublishStatus(status);

        if (status == 2) {
            //显示
            communityContent.setIsShow(1);
        } else if (status == 3) {
            //隐藏
            communityContent.setIsShow(0);
        }
        communityContentService.updateCommunityContentById(communityContent);
    }

    @Override
    public void top(Long id, Integer topStatus) {
        CommunityContent communityContent = communityContentService.selectCommunityContentById(id);
        if (Objects.isNull(communityContent)) return;
        // 更新置顶状态
        if (topStatus == 0) {
            communityContent.setTopSort(0); // 非置顶
        } else {
            // 计算置顶顺序
            CommunityQuery query = CommunityQuery.builder().lastStr("ORDER BY top_sort DESC limit 1").build();
            CommunityContent topOne = communityContentService.findByQuery(query);
            if (Objects.nonNull(topOne)) {
                Integer topSort = topOne.getTopSort();
                communityContent.setTopSort(++topSort);
            }
        }
        communityContentService.updateCommunityContentById(communityContent);
    }

    @Override
    public void enableCommentStatus(Long id, Integer status) {
        CommunityContent communityContent = communityContentService.selectCommunityContentById(id);
        if (status == 0 || status == 1) {
            if (!communityContent.getIsEnableComment().equals(status)) {
                communityContent.setIsEnableComment(status);
                communityContentService.updateCommunityContentById(communityContent);
            }
        }
    }

    @Override
    public CommunityContentVo robotPostDetail(Long id) {
        return contentDoToVO(communityContentService.findById(id), true);
    }

    @Override
    public void changeStatus(CommunityRobotPostChangeStatusRequestDto requestDto) {
        CommunityContent communityContent = new CommunityContent();
        communityContent.setId(requestDto.getContentId());
        communityContent.setIsShow(requestDto.getIsShow());
        communityContentService.update(communityContent);
    }

    @Override
    public void delete(CommunityRobotPostDeleteRequestDto requestDto) {
        communityContentService.deleteContent(requestDto.getContentId());
    }

    @Override
    public Page<CommunityContentVo> listRobotPost(CommunityContentRobotPostRequestDto requestDto) {
        CommunityContentRobotPostPageQuery pageQuery = BeanUtil.copyBean(requestDto, CommunityContentRobotPostPageQuery.class);
        Page<CommunityContent> page = new Page<>(requestDto.getPageNum(), requestDto.getPageSize());
        Page<CommunityContent> pageDo = communityContentService.findRobotPostPage(page, pageQuery);
        Page<CommunityContentVo> pageVo = new Page<>(requestDto.getPageNum(), requestDto.getPageSize());
        pageVo.setTotal(pageDo.getTotal());
        pageVo.setRecords(pageDo.getRecords().stream().map(k -> contentDoToVO(k, false)).collect(Collectors.toList()));
        return pageVo;
    }

    private CommunityContentVo contentDoToVO(CommunityContent content, Boolean needDetail) {
        CommunityContentVo vo = new CommunityContentVo();
        BeanUtil.copyPropertiesIgnoreNull(content, vo);
        try {
            vo.setPics(JsonUtil.readList(content.getPics(), CommunityContentPicResponseDto.class));
        } catch (Exception e) {
            List<String> list = JsonUtil.readList(content.getPics(), String.class);
            vo.setPics(list.stream().map(FileUtils::realPic).collect(Collectors.toList()));
        }
        if (content.getTopSort() > 0) {
            vo.setTopStatus(1);
        }

        //组装用户信息
        ZnsUserEntity znsUser = znsUserService.findById(content.getUserId());
        if (znsUser != null) {
            vo.setUserName(znsUser.getFirstName());
            vo.setHeadPortrait(znsUser.getHeadPortrait());
            vo.setUserCode(znsUser.getUserCode());
        }

        List<CommunityI18nDto> i18nList = new ArrayList<>();
        List<CommunityContentI18nEntity> list = communityContentI18nService.selectByContentId(content.getId());
        if (needDetail && (CommunityUserTypeEnum.AUTHORITY.getCode().equals(content.getUserType()) || CommunityUserTypeEnum.ROBOT_TASK.getCode().equals(content.getUserType()))) {
            //设置跳转内容
            setRedirectVo(vo);
            //设置多语言跳转内容
            if (!CollectionUtils.isEmpty(list)) {
                for (CommunityContentI18nEntity contentI18nEntity : list) {
                    CommunityI18nDto communityI18nDto = BeanUtil.copyBean(contentI18nEntity, CommunityI18nDto.class);
                    List<CommunityContentPicResponseDto> pics = JsonUtil.readList(contentI18nEntity.getPics(), CommunityContentPicResponseDto.class);
                    if (!CollectionUtils.isEmpty(pics)) {
                        communityI18nDto.setPics(pics);
                    }
                    setRedirectVo(communityI18nDto);
                    i18nList.add(communityI18nDto);
                }
            } else {
                //老数据默认填充英文内容
                CommunityI18nDto communityI18nDto = BeanUtil.copyBean(content, CommunityI18nDto.class);
                communityI18nDto.setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
                communityI18nDto.setLanguageName(I18nConstant.LanguageCodeEnum.en_US.getName());
                List<CommunityContentPicResponseDto> pics = JsonUtil.readList(content.getPics(), CommunityContentPicResponseDto.class);
                if (!CollectionUtils.isEmpty(pics)) {
                    communityI18nDto.setPics(pics);
                }
                setRedirectVo(communityI18nDto);
                i18nList.add(communityI18nDto);
            }
        } else {
            if (!CollectionUtils.isEmpty(list)) {
                CommunityContentI18nEntity communityContentI18nEntity = list.stream().filter(s -> Objects.equals(s.getLanguageCode(), I18nConstant.LanguageCodeEnum.en_US.getCode())).findFirst().get();
                vo.setVideoUrl(communityContentI18nEntity.getVideoUrl());
                vo.setVideoCoverUrl(communityContentI18nEntity.getVideoCoverUrl());
            }
        }
        vo.setI18nList(i18nList);

        //填充话题
        List<CommunityTopicDo> topicDoList = communityTopicContentRelationService.findByTopicByContentId(vo.getId());
        vo.setTopicInfos(topicDoList.stream().map(k -> {
            ContentBindTopicInfo contentBindTopicInfo = new ContentBindTopicInfo();
            contentBindTopicInfo.setTopicName(k.getName());
            contentBindTopicInfo.setTopicId(k.getId());
            return contentBindTopicInfo;
        }).collect(Collectors.toList()));
        vo.setClubId(content.getClubId());
        if (content.getClubId() != null) {
            Club club = clubService.findById(content.getClubId());
            vo.setClubName(club.getName());

        }
        vo.setIsEnableComment(content.getIsEnableComment());

        vo.setCommentNum(communityCommentService.getCommentNum(content.getId(), false));
        vo.setPostType(content.getIsSelect());
        return vo;
    }

    /**
     * 设置跳转内容
     *
     * @param vo
     */
    private void setRedirectVo(RedirectVo vo) {
        if (vo == null) {
            return;
        }
        //根据跳转类型补充相关信息
        Integer redirectType = vo.getRedirectType();
        BannerJumpTypeEnum jumpEnum = BannerJumpTypeEnum.getJumpEnumByType(redirectType);
        if (jumpEnum == null) {
            return;
        }

        switch (jumpEnum) {
            //赛事
            case RUN_ACTIVITY:
                ZnsRunActivityEntity activity = znsRunActivityService.selectActivityById(vo.getRunActivityId());
                if (activity != null) {
                    vo.setActivityName(activity.getActivityTitle());
                }
                break;
            //组装h5页面信息
            case H5_URL:
                String h5Url = vo.getActivityUrl();
                String[] split = h5Url.split("/");
                String h5id = split[split.length - 1];
                CustomH5 customH5 = customH5Dao.selectById(Long.valueOf(h5id));
                if (customH5 != null) {
                    vo.setH5title(customH5.getTitle());

                }
                break;
            //查页面信息
            case PAGE_URL:
                Long routeId = vo.getRouteId();
                AppRouteConfig appRouteConfig = appRouteConfigService.selectAppRouteConfigById(routeId);
                if (appRouteConfig != null) {
                    vo.setPageName(appRouteConfig.getTwoLevel());
                }
                break;
            //查课程信息
            case COURSE_URL:
                ZnsCourseEntity courseEntity = znsCourseService.selectById(vo.getCourseId());
                if (courseEntity != null) {
                    vo.setCourseName(courseEntity.getCourseName());
                }
                break;
            default:
                break;
        }
    }

    private CommunityContent contentVoToDo(CommunityContentVo vo) {
        CommunityContent content = new CommunityContent();
        BeanUtils.copyProperties(vo, content);
        if (content.getPublishType() == 0) {
            content.setPublishRealTime(ZonedDateTime.now());
            content.setPublishExpectTime(ZonedDateTime.now());
        }
        content.setPics(JsonUtil.writeString(vo.getPics()));
        return content;
    }
}
