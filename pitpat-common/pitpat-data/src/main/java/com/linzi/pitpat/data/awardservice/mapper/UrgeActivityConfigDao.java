package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 激历配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.awardservice.model.dto.EggActivityConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeActivityConfig;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IsEmpty;
import com.lz.mybatis.plugin.annotations.LE;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface UrgeActivityConfigDao extends BaseMapper<UrgeActivityConfig> {


    UrgeActivityConfig selectUrgeActivityConfigById(@Param("id") Long id);

    List<UrgeActivityConfig> selectUrgeActivityConfigByActivityId(Long activityId, @IF Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);

    List<UrgeActivityConfig> selectUrgeActivityConfigByActivityIdRouteIdDateDate(Long activityId, @IsEmpty Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);

    List<UrgeActivityConfig> selectUrgeActivityConfigByTypeGmtStartTimeGmtEndTime(Integer activityType, @IF Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);


    List<UrgeActivityConfig> selectUrgeActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(Integer activityType, @IsEmpty Long routeId, @DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime);


    Page<EggActivityConfigDto> selectPageByCondition(IPage page, @Param("tradeType") Integer tradeType,
                                                     @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime, @IF @Param("urgeType") List<Integer> urgeType);


}
