package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeReportVo;
import com.linzi.pitpat.data.entity.dto.AppUserActivityInfo;
import com.linzi.pitpat.data.vo.UserRunRecordDto;
import com.lz.mybatis.plugin.annotations.AS;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.Count;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.GT;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.Min;
import com.lz.mybatis.plugin.annotations.NE;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.Sum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户跑步挑战记录表
 *
 * <AUTHOR>
 * @date 2022-02-15 17:28:46
 */
@Mapper
public interface ZnsUserRunRecordDao extends BaseMapper<ZnsUserRunRecordEntity> {

    List<UserRunRecordDto> getRunRecords(@Param("activityId") Long activityId, @Param("userId") Long userId);


    /**
     * 统计官方赛事参与数据
     */
    AppUserActivityInfo userOfficialEventStatistics(@Param("userId") Long userId);

    List<ChallengeReportVo> getChallengeReport(@Param("activityId") Long activityId, @Param("isRealUser") Integer isRealUser);

    AppUserActivityInfo userOfficialEventStatistics1(@Param("userId") Long userId);

    @AS("uad")
    @Mapping("ifnull(count(*),0)")
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, as = "u", on = " uad.activity_id = u.id "),
    })
    Integer selectCountByUserIdChallengedUserIdIsChallengeSuccess(@Column("uad.user_id") Long userId, @Column("uad.challenged_user_id") @NE Long challengedUserId,
                                                                  @Column("uad.challenged_user_id") @NE Long challengedUserId0
            , @Column("uad.is_challenge_success") @IF Integer isChallengeSuccess, @Column("u.activity_type") Integer activityType);


    @AS("uad")
    @Mapping("ifnull(count(*),0)")
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, as = "u", on = " uad.activity_id = u.id "),
    })
    Integer selectCountByChallengedUserIdIsChallengeSuccess(@Column("uad.challenged_user_id") Long challengedUserId, @Column("uad.is_challenge_success") @IF Integer isChallengeSuccess
            , @Column("u.activity_type") Integer activityType
    );


    @OrderByIdDescLimit_1
    ZnsUserRunRecordEntity selectByRunDetailsId(Long runDataDetailsId);


    @Count(ZnsUserRunRecordEntity.id_)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunRecordEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    Integer selectGuanfangRankUpPositionCount(@Column(ZnsUserRunRecordEntity.create_time) @GE ZonedDateTime monthStart,
                                              @Column(ZnsUserRunRecordEntity.create_time) @LE ZonedDateTime monthEnd,
                                              @Column(ZnsUserRunRecordEntity.user_id) Long userId,
                                              @Column(ZnsRunActivityEntity.activity_type) Integer activityType,
                                              @Column(ZnsUserRunRecordEntity.rank_) @GE Integer rankStart,
                                              @Column(ZnsUserRunRecordEntity.rank_) @IF @LE Integer rankEnd
    );


    @Count(ZnsUserRunRecordEntity.id_)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunRecordEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    Integer selectGuanfangRankCompleteCount(@Column(ZnsUserRunRecordEntity.create_time) @GE ZonedDateTime monthStart,
                                            @Column(ZnsUserRunRecordEntity.create_time) @LE ZonedDateTime monthEnd,
                                            @Column(ZnsUserRunRecordEntity.user_id) Long userId,
                                            @Column(ZnsRunActivityEntity.activity_type) Integer activityType,
                                            @Column(ZnsUserRunRecordEntity.is_complete) Integer isComplete);


    @Sum(ZnsUserRunRecordEntity.rank_)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunRecordEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    Integer selectGuanfangRankPositionCount(@Column(ZnsUserRunRecordEntity.create_time) @GE ZonedDateTime monthStart,
                                            @Column(ZnsUserRunRecordEntity.create_time) @LE ZonedDateTime monthEnd,
                                            @Column(ZnsUserRunRecordEntity.user_id) Long userId,
                                            @Column(ZnsRunActivityEntity.activity_type) Integer activityType,
                                            @Column(ZnsUserRunRecordEntity.rank_) @GT Integer rank);

    @Min(ZnsUserRunRecordEntity.rank_)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunRecordEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    Integer selectGuanfangRankBestPosition(@Column(ZnsUserRunRecordEntity.create_time) @GE ZonedDateTime monthStart,
                                           @Column(ZnsUserRunRecordEntity.create_time) @LE ZonedDateTime monthEnd,
                                           @Column(ZnsUserRunRecordEntity.user_id) Long userId,
                                           @Column(ZnsRunActivityEntity.activity_type) Integer activityType,
                                           @Column(ZnsUserRunRecordEntity.rank_) @GE Integer rank);


    Map<String, Object> getChallengeSuccessAndFail(@Param("activityId") Long activityId, @Param("isRealUser") Integer isRealUser);

    Integer getChallengeUserSuccessAndFail(@Param("activityId") Long activityId, @Param("isRealUser") Integer isRealUser, @Param("isChallengeSuccess") Integer isChallengeSuccess);

    Integer selectRankCompleteNum(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);
}
