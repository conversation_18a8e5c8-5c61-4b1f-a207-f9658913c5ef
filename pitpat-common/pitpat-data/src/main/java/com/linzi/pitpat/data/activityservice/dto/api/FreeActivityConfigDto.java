package com.linzi.pitpat.data.activityservice.dto.api;

import com.linzi.pitpat.data.activityservice.dto.console.RouteDto;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import lombok.Data;

@Data
public class FreeActivityConfigDto {
    /**
     * 活动模式
     *
     * @see FreeActivityModeEnum
     */
    private String mode;


    private Long routeId;

    private Long activityId;

    private Integer targetMileage;

    private String pic;

    private FreeActivityChallengeDto challengeDto;



}
