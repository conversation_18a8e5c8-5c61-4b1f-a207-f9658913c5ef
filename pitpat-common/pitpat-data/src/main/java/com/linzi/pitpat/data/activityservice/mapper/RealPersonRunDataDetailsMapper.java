package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentRunDataVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentRunSumCountVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentRunDataVo;
import com.linzi.pitpat.data.userservice.dto.response.UserRunTotalDto;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.vo.UserAvgDataDto;
import com.linzi.pitpat.data.userservice.model.vo.UserEventPrefDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 真人用户跑步详情表 服务类
 *
 * @since 2024-04-25
 */
@Mapper
public interface RealPersonRunDataDetailsMapper extends BaseMapper<RealPersonRunDataDetails> {

    int batchInsert(@Param("list") List<RealPersonRunDataDetails> list);

    BigDecimal sumAllRunMileage(@Param("userId") Long userId, @Param("endTime") ZonedDateTime endTime, @Param("deviceType") Integer deviceType);

    Long sumAllRunTime(@Param("userId") Long userId);

    Long continuousRunDays(@Param("userId") Long userId, @Param("endTime") ZonedDateTime endTime, @Param("zoneId") String zoneId, @Param("deviceType") Integer deviceType);

    UserRunTotalDto sumAllRunTimeAndMileage(@Param("userId") Long userId, @Param("deviceTypes") List<Integer> deviceTypes);


    /**
     * 查询设备历史跑步数据
     */
    EquipmentRunDataVo findEquipmentRunDataVoByTreadmillId(@Param("treadmillId") Long treadmillId, @Param("createTime") ZonedDateTime createTime);

    /**
     * 批量查询设备历史跑步数据
     */
    EquipmentRunDataVo findEquipmentRunDataVoByTreadmillIds(@Param("treadmillIds") List<Long> treadmillId, @Param("createTime") ZonedDateTime createTime);

    /**
     * 获取用户运动数据
     */
    List<UserEquipmentRunDataVo> findUserEquipmentRunDataByTime(@Param("startDate") ZonedDateTime startDate);

    EquipmentRunSumCountVo findSumCountTotalByEquipmentId(@Param("treadmillId") Long treadmillId);

    RealPersonRunDataDetails selectByUserIdAverageVelocityActivityType(@Param("userId") Long userId, @Param("activityType") Integer activityType);

    UserAvgDataDto findUserAverageDurationAndAveragePace(@Param("userId") Long userId);

    Integer findSumCountByUserIdLastWeek(@Param("userId") Long userId);

    List<UserEventPrefDto> findUserLastWeekActivityTypeCount(@Param("userId") Long userId);

    List<Integer> findUserLastTenPkData(@Param("userId") Long userId);

    /**
     * 查询用户设备跑步数据
     */
    List<EquipmentRunDataVo> findUsersEquipmentRunDataVoByTreadmillIds(@Param("treadmillIds")List<Long> treadmillIds, @Param("userId")Long userId);
    List<RealPersonRunDataDetails> findListWithZeroCalorie(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime,
                                                            @Param("deviceTypes") List<Integer> deviceTypes, @Param("neProductCodeList") List<String> neProductCodeList);
}
