package com.linzi.pitpat.data.awardservice.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.awardservice.model.entity.AwardConfigMedalDo;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigMedalPageQuery;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigMedalQuery;

import java.util.List;


/**
 * 奖励配置勋章表 服务类
 *
 * @since 2025年7月3日
 */
public interface AwardConfigMedalService  {

    /**
     * 新增奖励配置勋章表
     * @param awardConfigMedal
     * @return 新增数量
     */
    Long create(AwardConfigMedalDo awardConfigMedal);

    /**
     * 更新奖励配置勋章表
     * @param awardConfigMedal
     * @return 更新数量
     */
    Long update(AwardConfigMedalDo awardConfigMedal);

    /**
     * 可更新空值
     * @param awardConfigMedal AwardConfigMedal
     * @return 更新数量
     */
    Long updateSelective(AwardConfigMedalDo awardConfigMedal);

    /**
     * 删除奖励配置勋章表
     * @param id
     * @return 影响数量
     */
    boolean deleteById(Long id);

    /**
     * 根据ID 查询奖励配置勋章表，返回单条数据
     * @param id
     * @return
     */
    AwardConfigMedalDo findById(Long id);

    /**
     * 根据条件查询聚合活动，返回单条数据
     * @param query
     * @return
     */
    AwardConfigMedalDo findByQuery(AwardConfigMedalQuery query);

    /**
     * 查询奖励配置勋章表列表
     * @param query
     * @return
     */
    List<AwardConfigMedalDo> findList(AwardConfigMedalQuery query);

    /**
     * 分页查询奖励配置勋章表
     * @param query
     * @return
     */
    Page<AwardConfigMedalDo> findPage(AwardConfigMedalPageQuery query);

    /**
     * 批量新增
     * @param awardConfigMedalList
     * @return
     */
    boolean batchCreate(List<AwardConfigMedalDo> awardConfigMedalList);

    /**
     * 批量更新
     * @param awardConfigMedalList
     * @return
     */
    public boolean batchUpdate(List<AwardConfigMedalDo> awardConfigMedalList);

    AwardConfigMedalDo findByAwardConfigId(Long awardConfigId);
}