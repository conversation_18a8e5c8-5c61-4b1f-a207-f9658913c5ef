package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 用户活动任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.awardservice.model.vo.FestivalActivityWalletVo;
import com.linzi.pitpat.data.resp.OneWeekHomeDetailDto;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.NE;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface RunActivityUserTaskDao extends BaseMapper<RunActivityUserTask> {


    RunActivityUserTask selectRunActivityUserTaskById(@Param("id") Long id);

    Long insertOrUpdateRunActivityUserTask(RunActivityUserTask runActivityUserTask);

    int getNewUserRunCount(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<RunActivityUserTask> selectRunActivityUserTaskByActivityIdUserId(Long activityId, Long userId);

    List<OneWeekHomeDetailDto> selectRunDataByActivityIdUserId(@Param("activityId") Long activityId, @Param("userId") Long userId);

    @LIMIT
    RunActivityUserTask selectRunActivityUserTaskByActivityIdUserIdLevel(Long activityId, Long userId, Integer level);

    void updateRunActivityUserTaskIsUnlockById(Integer isUnlock, @By Long id);


    //award_status  金额状态，0：默认，未获得，1：已获得，2：提现中，3：已提现
    @Mapping("ifnull(sum(award),0) totalAward," +
            " ifnull(sum(CASE WHEN activity_id = #{activityId} THEN award ELSE 0 END ),0) totalActivityAward," +//当前活动获得的钱
            " ifnull(sum(CASE WHEN award_status = 1 THEN award ELSE 0 END ),0) balance, " +     // 没有提现总金额
            " ifnull(sum(CASE WHEN award_status = 2 THEN award ELSE 0 END ),0) cashAmount," +    // 正在提现的金额
            " ifnull(sum(CASE WHEN award_status = 3 THEN award ELSE 0 END ),0) totalCashAmount," +    // 已经提现的总金额
            " ifnull(sum(CASE WHEN award_status = 2 and activity_id = #{activityId} THEN award ELSE 0 END ),0) totalActivityCashAmount")
    //当前活动提现中的钱
    FestivalActivityWalletVo selectFestivalActivityWallet(Long userId, Long activityId, Integer activityType, @NE Integer awardStatus);

    List<RunActivityUserTask> selectRunActivityUserTaskByUserIdAndTypeStatus(Long userId, Integer activityType, Integer status, Integer awardStatus);

    /**
     * 根据跑步记录获取关卡数据
     *
     * @param runDataDetailsId
     * @return
     */
    @LIMIT
    RunActivityUserTask selectByDetailId(Long runDataDetailsId);

    List<Long> selectActivityIds(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<Long> selectNotCompletedIds(@Param("userId") Long userId, @Param("activityIds") List<Long> activityIds, @Param("endTime") ZonedDateTime endTime);
}
