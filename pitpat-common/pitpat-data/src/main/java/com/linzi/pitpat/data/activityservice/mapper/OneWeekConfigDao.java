package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 一周快乐跑用户配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-01
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.entity.activity.OneWeekConfig;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LT;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;

@Mapper
public interface OneWeekConfigDao extends BaseMapper<OneWeekConfig> {


    OneWeekConfig selectOneWeekConfigById(@Param("id") Long id);


    Long insertOneWeekConfig(OneWeekConfig oneWeekConfig);


    Long insertOrUpdateOneWeekConfig(OneWeekConfig oneWeekConfig);


    int updateOneWeekConfigById(OneWeekConfig oneWeekConfig);


    int updateCoverOneWeekConfigById(OneWeekConfig oneWeekConfig);


    int deleteOneWeekConfigById(@Param("id") Long id);


    @LIMIT
    OneWeekConfig selectOneWeekConfigByActivityIdUserId(Long activityId, Long userId);


    @LIMIT
    OneWeekConfig selectOneWeekConfigByActivityUserIdType(Long activityId, @LT ZonedDateTime gmtCreate, Long userId, Integer type);

    @LIMIT
    OneWeekConfig selectOneWeekConfigByActivityIdUserIdType(Long activityId, Long userId, Integer type);

}
