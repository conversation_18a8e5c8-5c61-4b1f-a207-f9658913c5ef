package com.linzi.pitpat.data.awardservice.strategy;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityBrandInterestsBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserAwardBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityMedal;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardDoProcessResultDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendProcessDto;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.biz.UserWearsBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.RunDetailDataDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.model.entry.UserRunCertificate;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.UserRunCertificateService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.CertificateBrandEnum;
import com.linzi.pitpat.data.enums.TreadmillBrandEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public abstract class AbstractAwardProcessStrategy implements AwardProcessStrategy {
    @Autowired
    private MainActivityService mainActivityService;
    @Autowired
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Autowired
    protected ZnsTreadmillService znsTreadmillService;
    @Autowired
    protected ZnsUserService znsUserService;
    @Autowired
    protected UserRunCertificateService userRunCertificateService;
    @Resource
    protected AppMessageService appMessageService;
    @Autowired
    protected ActivityDisseminateBizService activityDisseminateBizService;

    @Autowired
    private RunActivityMedalService activityMedalService;
    @Autowired
    private UserMedalService userMedalService;
    @Autowired
    private MedalConfigService medalConfigService;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    protected ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    @Autowired
    protected ActivityBrandInterestsBizService activityBrandInterestsBizService;
    @Autowired
    protected SeriesActivityRelService seriesActivityRelService;
    @Autowired
    protected ActivityUserAwardBizService activityUserAwardBizService;
    @Autowired
    protected UserWearsBizService userWearsBizService;
    @Resource
    protected UserCouponService userCouponService;
    protected final List<Integer> brandRightsListType = Arrays.asList(AwardSentTypeEnum.COMPLETING_THE_GAME.getType()
            , AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), AwardSentTypeEnum.BEING_CHALLENGED.getType(),
            AwardSentTypeEnum.CHALLENGE_SUCCESS.getType(), AwardSentTypeEnum.CHALLENGE_FAIL.getType());

    @Autowired
    protected ISysConfigService sysConfigService;

    @Override
    public void awardSendProcess(List<Long> list, AwardSendDto dto, String batchNo) {
        log.info("AwardSendDto {}", dto);
        //检查是否有发送过
        List<Long> filterList = checkSendRecord(dto, list);
        // 执行逻辑
        doProcess(dto, filterList, batchNo);
        // 补充逻辑 check 证书
        doAfterProcess(dto);
    }

    @Override
    public AwardDoProcessResultDto awardSendProcess(AwardSendProcessDto awardSendProcessDto) {
        log.info("awardSendProcess start AwardSendProcessDto {}", awardSendProcessDto);
        boolean canSent = checkAwardSend(awardSendProcessDto);
        if (!canSent) {
            return new AwardDoProcessResultDto();
        }
        // 执行逻辑
        return doProcess(awardSendProcessDto);
    }


    /**
     * 检查是否可以发奖励
     *
     * @param awardSendProcessDto
     * @return
     */
    private boolean checkAwardSend(AwardSendProcessDto awardSendProcessDto) {
        if (awardSendProcessDto == null || !StringUtils.hasText(awardSendProcessDto.getAwardValue())) {
            return false;
        }
        String onlyKey = awardSendProcessDto.getOnlyKey();
        if (!StringUtils.hasText(onlyKey)) {
            return true;
        }
        if (redissonClient.getBucket(onlyKey).isExists()) {
            return false;
        }
        redissonClient.getBucket(onlyKey).set("1", 1, TimeUnit.HOURS);
        return true;
    }

    public abstract void doProcess(AwardSendDto dto, List<Long> list, String batchNo);

    protected abstract AwardDoProcessResultDto doProcess(AwardSendProcessDto awardSendProcessDto);

    /**
     * 设置奖励发送标志到缓存
     *
     * @param dto
     * @param i
     */
    public void putAwardSendFlagIntoCache(AwardSendDto dto, Long i) {
        String key = RedisConstants.USERAWARD_SEND + dto.getActivityId() + "_" + dto.getUserId() + "_" + i;
        MainActivity mainActivity = mainActivityService.findById(dto.getActivityId());
        ZonedDateTime activityEndTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        // 延迟时间get
        String reviewTime = sysConfigService.selectConfigByKey("activity.award.review.time");
        int reviewTimeIntHours;
        if (StringUtils.hasText(reviewTime)) {
            reviewTimeIntHours = Integer.parseInt(reviewTime);
        } else {
            reviewTimeIntHours = 72;
        }
        int surplusTime = DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.addHours(activityEndTime, reviewTimeIntHours + 48));
        try {
            redissonClient.getBucket(key).set("1", surplusTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("putAwardSendFlagIntoCache e", e);
        }
    }

    public List<Long> checkSendRecord(AwardSendDto dto, List<Long> list) {
        // 排除挑战奖励
        if (dto.getType().equals(AwardSentTypeEnum.CHALLENGE_SUCCESS.getType()) || dto.getType().equals(AwardSentTypeEnum.CHALLENGE_FAIL.getType())) {
            return list;
        }
        List<Long> filterList = new ArrayList<>();
        // 奖励发送缓存标志
        list.forEach(i -> {
            String key = RedisConstants.USERAWARD_SEND + dto.getActivityId() + "_" + dto.getUserId() + "_" + i;
            if (!redissonClient.getBucket(key).isExists()) {
                filterList.add(i);
            } else {
                log.info("checkSendRecord send activity:{},user:{},awardConfigId:{}", dto.getActivityId(), dto.getUserId(), i);
            }
        });
        return filterList;
    }

    /**
     * 通用奖励发放完成后置处理
     *
     * @param dto
     */
    public void doAfterProcess(AwardSendDto dto) {
        // 完赛,排名 check 证书配置
        if (dto.getType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType()) ||
                dto.getType().equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())) {
            String key = RedisConstants.CERTIFICATE_EXECUTION + dto.getActivityId() + "_" + dto.getUserId() + "_" + dto.getType();
            boolean exists = redissonClient.getBucket(key).isExists();
            if (exists) {
                return;
            }
            MainActivity service = mainActivityService.findById(dto.getActivityId());
            if (service.getFinishedCertificate() != -1 || service.getFirstCertificate() != -1) {
                // 证书
                RunDetailDataDto runDetailDataDto = new RunDetailDataDto();
                List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetailsEntities = new ArrayList<>();
                if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(service.getMainType())) {
                    //系列赛查询子活动
                    List<Long> subActivityIds = seriesActivityRelService.findSubActivityId(service.getId());
                    for (Long subActivityId : subActivityIds) {
                        List<ZnsUserRunDataDetailsEntity> detailList = userRunDataDetailsService.getUserDetailByActivityId(dto.getUserId(), subActivityId);
                        if (!CollectionUtils.isEmpty(detailList)) {
                            znsUserRunDataDetailsEntities.addAll(detailList);
                        }
                    }
                } else {
                    znsUserRunDataDetailsEntities = userRunDataDetailsService.getUserDetailByActivityId(dto.getUserId(), dto.getActivityId());
                }

                if (CollectionUtils.isEmpty(znsUserRunDataDetailsEntities)) {
                    // 无运动明细直接退出
                    return;
                }
                for (ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity : znsUserRunDataDetailsEntities) {
                    runDetailDataDto.setRunTime(runDetailDataDto.getRunTime() + znsUserRunDataDetailsEntity.getRunTime());
                    runDetailDataDto.setRunMileage(runDetailDataDto.getRunMileage().add(znsUserRunDataDetailsEntity.getRunMileage()));
                }
                ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsEntities.get(znsUserRunDataDetailsEntities.size() - 1);
                Long treadmillId = znsUserRunDataDetailsEntity.getTreadmillId();
                ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findById(treadmillId);
                // 设备判定null
                if (Objects.nonNull(treadmillEntity)) {
                    TreadmillBrandEnum treadmillBrandEnum = TreadmillBrandEnum.findByName(treadmillEntity.getBrand());
                    // 设备品牌不匹配证书配置
                    if (service.getFinishedCertificate() > 0 && !CertificateBrandEnum.findByName(treadmillEntity.getBrand()).getType().equals(service.getFinishedCertificate())) {
                        return;
                    }
                    if (service.getFirstCertificate() > 0 && !CertificateBrandEnum.findByName(treadmillEntity.getBrand()).getType().equals(service.getFirstCertificate())) {
                        return;
                    }
                    log.info("doAfterProcess 发放证书 userId:{},activityId:{},brand:{}", dto.getUserId(), dto.getActivityId(), treadmillEntity.getBrand());
                    runDetailDataDto.setType(CertificateBrandEnum.findByName(treadmillEntity.getBrand()).getType());
                    runDetailDataDto.setBrand(CertificateBrandEnum.findByName(treadmillEntity.getBrand()).getName());
                    Double averagePace = znsUserRunDataDetailsEntities.stream().mapToInt(ZnsUserRunDataDetailsEntity::getAveragePace).average().orElse(0d);
                    BigDecimal averageVelocity = znsUserRunDataDetailsEntities.stream().map(ZnsUserRunDataDetailsEntity::getAverageVelocity).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(znsUserRunDataDetailsEntities.size()), 2, BigDecimal.ROUND_HALF_UP);
                    runDetailDataDto.setAveragePace(averagePace.intValue());
                    runDetailDataDto.setAverageVelocity(averageVelocity);
                    ZonedDateTime lastTime = znsUserRunDataDetailsEntities.get(znsUserRunDataDetailsEntities.size() - 1).getLastTime();
                    runDetailDataDto.setActivityDate(lastTime);
                    genUserCertificate(runDetailDataDto, dto, service);
                    redissonClient.getBucket(key).set("1", 3, TimeUnit.DAYS);
                }
            }

        }
        //完赛勋章发放
        if (dto.getType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
            String key = RedisConstants.USERMEDAL_EXECUTION + dto.getActivityId() + "_" + dto.getUserId();
            boolean exists = redissonClient.getBucket(key).isExists();
            if (exists) {
                return;
            }
            RunActivityMedal runActivityMedal = activityMedalService.findByActivityIdOne(dto.getActivityId());
            if (Objects.nonNull(runActivityMedal)) {
                MedalConfig medalConfig = medalConfigService.selectMedalConfigById(runActivityMedal.getMedalConfigId());
                if (medalConfig != null) {
                    sendUserMedal(dto.getUserId(), medalConfig);
                }
                redissonClient.getBucket(key).set("1", 3, TimeUnit.DAYS);
            }
        }
    }

    private void sendUserMedal(Long userId, MedalConfig medalConfig) {
        UserMedal userMedal = userMedalService.findByUserAndMedalId(userId, medalConfig.getId());
        if (userMedal == null) {
            //勋章不存在就新增一个勋章
            userMedal = new UserMedal();
            userMedal.setUserId(userId);
            userMedal.setIsValid(0);
            userMedal.setParam1(medalConfig.getParam1());
            userMedal.setParam2(medalConfig.getParam2());
            userMedal.setParam3(medalConfig.getParam3());
            userMedal.setParam4(medalConfig.getParam4());
            userMedal.setParam5(medalConfig.getParam5());
            userMedal.setRemark(medalConfig.getRemark());
            userMedal.setType(medalConfig.getType());
            userMedal.setName(medalConfig.getName());
            userMedal.setLevel(medalConfig.getLevel());
            userMedal.setHandler(medalConfig.getHandler());
            userMedal.setMedalConfigId(medalConfig.getId());
        }
        userMedal.setObtain(1);
        userMedal.setObtainTime(ZonedDateTime.now());
        userMedalService.saveOrUpdate(userMedal);
    }


    public void genUserCertificate(RunDetailDataDto runDetailDataDto, AwardSendDto dto, MainActivity service) {
        if (StringUtil.isEmpty(runDetailDataDto.getBrand())) {
            return;
        }
        if (MainActivityTypeEnum.SERIES_SUB.getType().equals(service.getMainType())) {
            log.info("genUserCertificate 系列赛阶段赛事 无品牌证书");
            return;
        }
        UserRunCertificate userRunCertificate = null;
        ZnsUserEntity userEntity = znsUserService.findById(dto.getUserId());
        ActivityDisseminate disseminates = activityDisseminateBizService.findByActivityIdAndLanguage(service.getId(), null);
        if (dto.getType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
            // 品牌证书明细生成
            if (service.getFinishedCertificate() != -1) {
                if (service.getFinishedCertificate().equals(runDetailDataDto.getType()) || service.getFinishedCertificate() == 0) {
                    userRunCertificate = new UserRunCertificate();
                    BeanUtils.copyProperties(runDetailDataDto, userRunCertificate);
                    userRunCertificate.setActivityId(service.getId());
                    userRunCertificate.setUserId(userEntity.getId());
                    userRunCertificate.setActivityDate(service.getTimeStyle() == 0 ? DateTimeUtil.parse(service.getActivityEndTime()) :
                            DateTimeUtil.parse(service.getActivityEndTime(), userEntity.getZoneId()));
                    userRunCertificate.setCertificateType(2);
                    userRunCertificate.setCertificateName(disseminates.getTitle());

                    userRunCertificate.setCertificateNo(runDetailDataDto.getBrand().charAt(0) + DateTimeUtil.formatWithPattern(DateTimeUtil.parse(service.getActivityEndTime()), DateUtil.YYYYMMDD) + userEntity.getUserCode() + "");
                    userRunCertificateService.save(userRunCertificate);
//                    sendCertificationImMsg(userEntity.getId(), userRunCertificate);
                }
            }
        }
        if (dto.getType().equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())) {
            // 第一名证书
            if (service.getFirstCertificate() != -1 && dto.getRank() == 1) {
                if (service.getFirstCertificate().equals(runDetailDataDto.getType()) || service.getFirstCertificate() == 0) {
                    userRunCertificate = new UserRunCertificate();
                    BeanUtils.copyProperties(runDetailDataDto, userRunCertificate);
                    userRunCertificate.setActivityId(service.getId());
                    userRunCertificate.setCertificateType(1);
                    userRunCertificate.setUserId(userEntity.getId());
                    userRunCertificate.setActivityDate(service.getTimeStyle() == 0 ? DateTimeUtil.parse(service.getActivityEndTime()) : DateTimeUtil.parse(
                            DateUtil.getStampByZone(service.getActivityEndTime(), userEntity.getZoneId())));
                    userRunCertificate.setCertificateName(disseminates.getTitle());
                    userRunCertificate.setCertificateNo(runDetailDataDto.getBrand().charAt(0) + DateTimeUtil.formatWithPattern(DateTimeUtil.parse(service.getActivityEndTime()), DateUtil.YYYYMMDD) + userEntity.getUserCode() + "");
                    userRunCertificateService.save(userRunCertificate);
//                    sendCertificationImMsg(userEntity.getId(), userRunCertificate);
                }
            }
        }
    }
//
//    private void sendCertificationImMsg(Long userId, UserRunCertificate userRunCertificate) {
//        ImMessageBo imMessageBo = new ImMessageBo();
//        String msg = "Congratulation, you have received the Certificate of " + (userRunCertificate.getCertificateType() == 1 ? "Winner" : "Completion") + " for " + userRunCertificate.getCertificateName();
//        imMessageBo.setJumpType("6");
//        Map<String, Object> params = new HashMap<>();
//        params.put("certification_id", userRunCertificate.getId());
//        imMessageBo.setParams(params);
//        imMessageBo.setMsg(msg);
//        imMessageBo.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202305/iHR13soPkmkk0092.png");
//        imMessageBo.setJumpValue("lznative://main/myCertificationsDetail");
//        appMessageService.sendIm("administrator", Collections.singletonList(userId), JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
//    }

    @Override
    public void amountSendProcess(BigDecimal amount, Integer tradeType, Integer tradeSubtype, AwardSendDto awardSendDto) {

    }


    protected Integer getBrandRightsInterestEnumBySendType(Integer type) {
        if (type.equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
            return BrandRightsInterestEnum.COMPLETION_REWARD.getStatusCode();
        }
        if (type.equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())) {
            return BrandRightsInterestEnum.RANK_REWARD.getStatusCode();
        }
        if (type.equals(AwardSentTypeEnum.BEING_CHALLENGED.getType())) {
            return BrandRightsInterestEnum.BE_CHALLENGED_REWARD.getStatusCode();
        }
        if (type.equals(AwardSentTypeEnum.CHALLENGE_SUCCESS.getType())) {
            return BrandRightsInterestEnum.CHALLENGE_SUCCESS_REWARD.getStatusCode();
        }
        if (type.equals(AwardSentTypeEnum.CHALLENGE_FAIL.getType())) {
            return BrandRightsInterestEnum.CHALLENGE_FAIL_REWARD.getStatusCode();
        }
        return 0;
    }

    @Override
    public void deleteAwardConfig(List<Long> longList) {

    }
}
