package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 活动任务配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LLIKE;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface ActivityTaskConfigDao extends BaseMapper<ActivityTaskConfig> {

    ActivityTaskConfig selectActivityTaskConfigById(@Param("id") Long id);


    Long insertOrUpdateActivityTaskConfig(ActivityTaskConfig activityTaskConfig);

    @OrderByIdDescLimit_1
    ActivityTaskConfig selectActivityTaskConfigByActivityId(Long activityId);


    List<ActivityTaskConfig> selectActivityTaskConfigByGmtStartTimeGmtEndTimeUsefulHh(@DateFormat @LE ZonedDateTime gmtStartTime, @DateFormat @GE ZonedDateTime gmtEndTime, Integer useful, @LLIKE @IF String hh);


    List<ActivityTaskConfig> findList(Long activityId, String country);
}
