package com.linzi.pitpat.data.engine.medal;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.mapper.ZnsUserRunDataDetailsDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 3天内完成3km及以上的竞技型比赛3次，平均速度达6kph
 * 5天内完成3km及以上的竞技型比赛5次，平均速度达8kph
 * 30天内完成5km及以上的竞技型比赛15次，平均速度达9kph
 * 30天内完成5km及以上的竞技型比赛15次，平均速度达10kph
 * 60天内完成8km及以上的竞技型比赛15次，平均速度达10.5kph
 * 60天内完成8km及以上的竞技型比赛15次，平均速度达11kph
 * 60天内完成12km及以上的竞技型比赛15次，平均速度达11.5kph
 */

@Component("abilityMedalHandler")
@Slf4j
public class AbilityMedalHandler extends BaseMedalHandler {


    @Autowired
    private ZnsUserRunDataDetailsDao znsUserRunDataDetailsDao;


    @Override
    public Pair<Boolean, UserMedal> deliverMedal(MedalParamDto medalParamDto) {
        ZonedDateTime currentDate = ZonedDateTime.now();
        boolean flag = false;
        MedalConfig medalConfig = medalParamDto.getMedalConfig();
        UserMedal userMedal = medalParamDto.getUserMedal();

        if (!activityTypeCheck(medalParamDto.getActivityNew())) {
            return Pair.of(false, userMedal);
        }

        Integer count = znsUserRunDataDetailsDao.selectByUserRunDataDetail(medalParamDto.getUserId(), DateUtil.addDays(currentDate,
                        -MapUtil.getInteger(medalConfig.getParam1())),
                MapUtil.getInteger(medalConfig.getParam2()),
                MapUtil.getBigDecimal(medalConfig.getParam4(), new BigDecimal(6)));
        log.info("wwwwwww" + "abilityMedalHandler  " + medalConfig.getName() + " 竞技型比赛次数 count = " + count + ", 配置表次数 param3= " + medalConfig.getParam3() + ",userId=" + medalParamDto.getUserId());
        if (count >= MapUtil.getInteger(medalConfig.getParam3())) {
            //有效天数
            Integer validHours = medalConfig.getValidHours();
            if (Objects.nonNull(validHours) && validHours > 0) {
                userMedal.setValidDays(validHours);
                userMedal.setValidStartTime(currentDate);
                userMedal.setValidEndTime(DateUtil.addHours(currentDate, MapUtil.getInteger(validHours)));
            }
            flag = handlerSucessMedal(medalConfig, userMedal);
        }
        userMedal.setCurrentProcess1(new BigDecimal(count));
        return Pair.of(flag, userMedal);
    }

    @Override
    public boolean activityTypeCheck(ActivityTypeDto activityNew) {
        if (Objects.isNull(activityNew)) {
            return false;
        }
        if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityNew.getActivityType()) || RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityNew.getActivityType())) {
            return true;
        }
        return false;
    }
}
