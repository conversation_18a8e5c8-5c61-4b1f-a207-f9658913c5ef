package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.activityservice.filler.MarathonInviteAwardDataFiller;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.base.group.GroupRelationBind;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@NoArgsConstructor
@FieldNameConstants
public class MyMarathonTeamConfigDto extends MarathonTeamConfigDto {
    /**
     * 是否是队长
     */
    private Integer isTeamLeader = 0;

    private Long userId;

    private Long activityId;
    /**
     * 待审核数量
     */
    private Long waitJudgeReviewCount;


    /**
     * @since 4.7.4
     * 新用户奖励
     */
    private BigDecimal newUserAwardAmount;

    /**
     * @Since 4.7.4 队长奖励说明
     *
     */
    private String newEquipmentUserAwardDesc;
    /**
     * 成功邀请加入的新成员数量
     */
    @Filler(filler = MarathonInviteAwardDataFiller.class, relationFieldName = "successJoinNewMember", groupMode = true, groupRelationBind = {
            @GroupRelationBind(relationField = Fields.userId, dataFieldName = MarathonInviteAwardDataFiller.LoadData.Fields.userId),
            @GroupRelationBind(relationField = Fields.activityId, dataFieldName = MarathonInviteAwardDataFiller.LoadData.Fields.activityId),
            @GroupRelationBind(relationField = MarathonTeamConfigDto.Fields.teamId, dataFieldName = MarathonInviteAwardDataFiller.LoadData.Fields.teamId),
    }, fetchFiledName = MarathonInviteAwardDataFiller.FillData.Fields.successJoinNewMemberCount)
    private Long successJoinNewMemberCount;
    /**
     * 成功邀请加入的新成员 金钱奖励
     */
    @Filler(filler = MarathonInviteAwardDataFiller.class, relationFieldName = "successJoinNewMember", groupMode = true, fetchFiledName = MarathonInviteAwardDataFiller.FillData.Fields.successJoinNewMemberAmountAward)
    private BigDecimal successJoinNewMemberAmountAward;

    private Currency currency = I18nConstant.CurrencyCodeEnum.USD.getCurrency();


    public static MyMarathonTeamConfigDto from(MarathonTeamConfigDto team) {
        MyMarathonTeamConfigDto t = new MyMarathonTeamConfigDto();
        t.setTeamId(team.getTeamId());
        t.setTeamManagerId(team.getTeamManagerId());
        t.setTeamUserDtos(team.getTeamUserDtos());
        t.setTeamMileage(team.getTeamMileage());
        t.setMyMileage(team.getMyMileage());
        return t;
    }

    public boolean isTeamLeader() {
        return Objects.equals(isTeamLeader, 1);
    }
}
