package com.linzi.pitpat.data.activityservice.converter.api;

import com.linzi.pitpat.core.converter.BigDecimalConverter;
import com.linzi.pitpat.data.activityservice.dto.api.response.TopPlayerResponse;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/3
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {BigDecimalConverter.class})
public interface RunActivityUserConverter {

    TopPlayerResponse toTopPlayerDto(ZnsRunActivityUserEntity runActivityUser);

    TopPlayerResponse toTopPlayerDto(PropRunRankedActivityUser propRunRankedActivityUser);
}
