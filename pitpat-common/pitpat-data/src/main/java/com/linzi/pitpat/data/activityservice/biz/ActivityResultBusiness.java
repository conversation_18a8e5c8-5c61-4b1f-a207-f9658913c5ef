package com.linzi.pitpat.data.activityservice.biz;

import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityPropEffectEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserDto;
import com.linzi.pitpat.data.activityservice.manager.console.UserPropRecordQuery;
import com.linzi.pitpat.data.activityservice.model.dto.CheatRiskLogDto;
import com.linzi.pitpat.data.activityservice.model.dto.UserPropRecordDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SeriesGameplayService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.constants.FetchRuleTypeEnum;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/01/14
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ActivityResultBusiness {
    private final EntryGameplayService entryGameplayService;
    private final AwardConfigService awardConfigService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final SeriesGameplayService seriesGameplayService;
    private final ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final AwardConfigBizService awardConfigBizService;
    private final ActivityParamsBizService activityParamsBizService;
    private final ActivityUserBizService activityUserBizService;
    private final UserPropRecordService userPropRecordService;

    private final MongoTemplate mongoTemplate;

    public Map<Long, UserPropRecordDto> getPropRecordMap(Long activityId) {
        return getPropRecordMap(buildActivityContext(activityId));
    }

    /**
     * 获取道具记录
     */
    public Map<Long, UserPropRecordDto> getPropRecordMap(ActivityContext context) {
        if (Objects.isNull(context.getEntryGameplay()) || context.getEntryGameplay().getTargetType() == 0) {
            return Collections.emptyMap();
        }

        List<Long> activityIdList = context.getActivityIdList();

        List<UserPropRecordDto> records = userPropRecordService.findUserPropRecordList(
                new UserPropRecordQuery()
                        .setActivityIds(activityIdList)
                        .setEffectCode(ActivityPropEffectEnum.REDUCE_FINISH_TIME.getCode()));

        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyMap();
        }

        return records.stream()
                .collect(Collectors.toMap(UserPropRecordDto::getUseRunDataDetailsId,
                        Function.identity(),
                        (x, y) -> x));
    }

    /**
     * 获取用户跑步数据map
     */
    public Map<Long, List<ZnsUserRunDataDetailsEntity>> getUserRunDataMap(ActivityResultBusiness.ActivityContext context, List<Long> userIds) {
        List<ZnsUserRunDataDetailsEntity> userRunDataDetailsList = getUserRunData(context, userIds, null);
        return userRunDataDetailsList.stream().collect(Collectors.groupingBy(ZnsUserRunDataDetailsEntity::getUserId));
    }

    /**
     * 获取用户跑步数据
     */
    public List<ZnsUserRunDataDetailsEntity> getUserRunData(ActivityContext context, List<Long> userIds, Integer isCheat) {
        UserRunDataDetailsQuery userRunDataDetailsQuery = new UserRunDataDetailsQuery().setActivityIds(context.getActivityIdList()).setUserIds(userIds).setIsCheat(isCheat);
        if (Objects.nonNull(context.getEntryGameplay()) && context.getEntryGameplay().getDataSource() == 2) {
            MainActivity mainActivity = context.getMainActivity();
            UserRunDataDetailsQuery userRunDataDetailsQuery2 = new UserRunDataDetailsQuery().setUserIds(userIds).setGeLastTime(DateTimeUtil.parse(mainActivity.getActivityStartTime()))
                    .setLeLastTime(DateTimeUtil.parse(mainActivity.getActivityEndTime())).setIsCheat(isCheat);
            List<ZnsUserRunDataDetailsEntity> list = znsUserRunDataDetailsService.findListByQuery(userRunDataDetailsQuery2);
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            }
        }
        //查询所有成绩
        return znsUserRunDataDetailsService.findListByQuery(userRunDataDetailsQuery);
    }

    /**
     * 重置用户跑步结果
     */
    public void resetUserEntryResult(EntryGameplay entryGameplay, ZnsRunActivityUserEntity runActivityUser, List<ZnsUserRunDataDetailsEntity> userRunDataDetailsEntities, Map<Long, UserPropRecordDto> userPropRecordMap) {
        if (CollectionUtils.isEmpty(userRunDataDetailsEntities)) {
            resetActivityUserResult(runActivityUser, null, null);
            return;
        }
        if (FetchRuleTypeEnum.FIRST_TIME_COMPLETION.getType().equals(entryGameplay.getFetchRule())) {
            //首次完赛
            ZnsUserRunDataDetailsEntity userRunDataDetailsEntity = userRunDataDetailsEntities.stream()
                    .filter(runDataDetails -> completeTarget(runDataDetails, runActivityUser))
                    .sorted(Comparator.comparing(ZnsUserRunDataDetailsEntity::getCreateTime))
                    .findFirst().orElse(null);
            if (Objects.isNull(userRunDataDetailsEntity)) {
                resetActivityUserResult(runActivityUser, null, null);
                return;
            }
            resetActivityUserResult(runActivityUser, userRunDataDetailsEntity, userPropRecordMap.get(userRunDataDetailsEntity.getId()));
        } else if (FetchRuleTypeEnum.BEST_PERFORMANCE_COMPLETION.getType().equals(entryGameplay.getFetchRule())) {
            ZnsUserRunDataDetailsEntity userRunDataDetailsEntity = userRunDataDetailsEntities.stream()
                    .filter(runDataDetails -> completeTarget(runDataDetails, runActivityUser))
                    .sorted((o1, o2) -> {
                        return bestCompare(o1, o2, entryGameplay, userPropRecordMap);
                    }).findFirst().orElse(null);
            if (Objects.isNull(userRunDataDetailsEntity)) {
                resetActivityUserResult(runActivityUser, null, null);
                return;
            }
            resetActivityUserResult(runActivityUser, userRunDataDetailsEntity, userPropRecordMap.get(userRunDataDetailsEntity.getId()));
        } else if (FetchRuleTypeEnum.ACCUMULATED_PARTICIPATION_DATA.getType().equals(entryGameplay.getFetchRule())) {
            resetActivityUserResult(runActivityUser, userRunDataDetailsEntities, userPropRecordMap, false);
        } else if (FetchRuleTypeEnum.ACCUMULATED_COMPLETION_DATA.getType().equals(entryGameplay.getFetchRule())) {
            resetActivityUserResult(runActivityUser, userRunDataDetailsEntities, userPropRecordMap, true);
        }
    }

    public void resetActivityUserResult(ZnsRunActivityUserEntity runActivityUser, List<ZnsUserRunDataDetailsEntity> userRunDataDetailsEntities, Map<Long, UserPropRecordDto> userPropRecordMap, boolean isComplete) {
        runActivityUser.setRunTime(0);
        runActivityUser.setRunTimeMillisecond(0);
        runActivityUser.setRunMileage(BigDecimal.ZERO);
        runActivityUser.setPropRunTime(0);
        runActivityUser.setIsComplete(0);
        runActivityUser.setCompleteTime(null);

        userRunDataDetailsEntities.stream().filter(d -> !isComplete || completeTarget(d, runActivityUser))
                .forEach(userRunDataDetailsEntity -> {
                    runActivityUser.setRunMileage(runActivityUser.getRunMileage().add(userRunDataDetailsEntity.getRunMileage()));
                    runActivityUser.setRunTime(runActivityUser.getRunTime() + userRunDataDetailsEntity.getRunTime());
                    UserPropRecordDto userPropRecordDto = userPropRecordMap.get(userRunDataDetailsEntity.getId());
                    if (Objects.nonNull(userPropRecordDto)) {
                        int propTime = userRunDataDetailsEntity.getRunTimeMillisecond() < userPropRecordDto.getPropRunTime() ? 0 : userRunDataDetailsEntity.getRunTimeMillisecond() - userPropRecordDto.getPropRunTime();
                        runActivityUser.setPropRunTime(runActivityUser.getPropRunTime() + propTime);
                        runActivityUser.setRunTimeMillisecond(runActivityUser.getPropRunTime());
                    } else {
                        runActivityUser.setPropRunTime(runActivityUser.getPropRunTime() + userRunDataDetailsEntity.getRunTimeMillisecond());
                        runActivityUser.setRunTimeMillisecond(runActivityUser.getPropRunTime());
                    }
                    try {
                        if (Objects.isNull(runActivityUser.getCompleteTime()) && runActivityUser.getTargetRunMileage() > 0 && runActivityUser.getRunMileage().compareTo(new BigDecimal(runActivityUser.getTargetRunMileage())) >= 0) {
                            runActivityUser.setIsComplete(1);
                            runActivityUser.setCompleteTime(userRunDataDetailsEntity.getLastTime());
                        } else if (Objects.isNull(runActivityUser.getCompleteTime()) && runActivityUser.getTargetRunTime() > 0 && runActivityUser.getRunTime() >= runActivityUser.getTargetRunTime()) {
                            runActivityUser.setIsComplete(1);
                            runActivityUser.setCompleteTime(userRunDataDetailsEntity.getLastTime());
                        }
                    } catch (Exception e) {
                        log.error("resetActivityUserResult error", e);
                    }
                });
    }

    private void resetActivityUserResult(ZnsRunActivityUserEntity runActivityUser, ZnsUserRunDataDetailsEntity d, UserPropRecordDto userPropRecordDto) {
        if (Objects.isNull(d)) {
            runActivityUser.setRunTime(0);
            runActivityUser.setRunTimeMillisecond(0);
            runActivityUser.setRunMileage(BigDecimal.ZERO);
            runActivityUser.setRunDataDetailsId(0l);
            runActivityUser.setIsComplete(0);
            runActivityUser.setCompleteTime(null);
            runActivityUser.setRank(-1);
            runActivityUser.setPropRunTime(0);
            return;
        }
        runActivityUser.setRunTime(d.getRunTime());
        runActivityUser.setRunTimeMillisecond(d.getRunTimeMillisecond());
        runActivityUser.setRunMileage(d.getRunMileage());
        runActivityUser.setRunDataDetailsId(d.getId());
        runActivityUser.setIsComplete(1);
        runActivityUser.setCompleteTime(d.getLastTime());
        runActivityUser.setRunDataDetailsId(d.getId());
        if (Objects.nonNull(userPropRecordDto)) {
            runActivityUser.setPropRunTime(d.getRunTimeMillisecond() < userPropRecordDto.getPropRunTime() ? 0 : d.getRunTimeMillisecond() - userPropRecordDto.getPropRunTime());
        } else {
            runActivityUser.setPropRunTime(d.getRunTimeMillisecond());
        }
    }

    private int bestCompare(ZnsUserRunDataDetailsEntity o1, ZnsUserRunDataDetailsEntity o2, EntryGameplay entryGameplay, Map<Long, UserPropRecordDto> userPropRecordMap) {
        if (o1.getRunTimeMillisecond() <= 0 || o2.getRunTimeMillisecond() <= 0) {
            return -1;
        }

        if (ActivityConstants.TargetTypeEnum.TARGETTYPE_1.getCode().equals(entryGameplay.getTargetType())) {
            if (o1.getRunMileage().compareTo(o1.getDistanceTarget()) >= 0) {
                if (o2.getRunMileage().compareTo(o2.getDistanceTarget()) < 0) {
                    return -1;
                } else {
                    UserPropRecordDto userPropRecordDto1 = userPropRecordMap.get(o1.getId());
                    UserPropRecordDto userPropRecordDto2 = userPropRecordMap.get(o2.getId());

                    Integer propRunTime1 = 0;
                    Integer propRunTime2 = 0;
                    if (Objects.isNull(userPropRecordDto1) || Objects.isNull(propRunTime1)) {
                        propRunTime1 = o1.getRunTimeMillisecond();
                    } else {
                        propRunTime1 = userPropRecordDto1.getPropRunTime();
                    }
                    if (Objects.isNull(userPropRecordDto2) || Objects.isNull(propRunTime2)) {
                        propRunTime2 = o2.getRunTimeMillisecond();
                    } else {
                        propRunTime2 = userPropRecordDto2.getPropRunTime();
                    }
                    return propRunTime1 - propRunTime2;
                }
            } else {
                return o1.getRunMileage().compareTo(o2.getRunMileage());
            }
        } else if (ActivityConstants.TargetTypeEnum.TARGETTYPE_2.getCode().equals(entryGameplay.getTargetType())) {
            return o1.getRunMileage().compareTo(o2.getRunMileage());
        }

        return 0;
    }

    /**
     * 是否完成目标
     *
     * @param runDataDetails
     * @param runActivityUser
     * @return
     */
    public boolean completeTarget(ZnsUserRunDataDetailsEntity runDataDetails, ZnsRunActivityUserEntity runActivityUser) {
        if (Objects.isNull(runDataDetails)) {
            return false;
        }
        try {
            if (runActivityUser.getTargetRunTime() > 0 && runDataDetails.getRunTime() >= runActivityUser.getTargetRunTime()) {
                return true;
            }
            if (runActivityUser.getTargetRunMileage() > 0 && runDataDetails.getRunMileage().intValue() >= runActivityUser.getTargetRunMileage()) {
                return true;
            }
        } catch (Exception e) {
            log.info("completeTarget error", e);
        }

        return false;
    }
// ----------------------------------------------------


    public Map<Long, Long> getUserCheatHitMap(Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap) {
        Map<Long, Long> userCheatHitMap = new HashMap<>();
        List<Long> runIdList = userRunDataDetailsMap.values().stream().flatMap(Collection::stream).map(ZnsUserRunDataDetailsEntity::getId).collect(Collectors.toList());
        List<List<Long>> partition = ListUtils.partition(runIdList, 1000);
        for (List<Long> list : partition) {
            Query query = Query.query(Criteria.where("run_id").in(list));
            List<CheatRiskLogDto> cheatRiskLogDtos = mongoTemplate.find(query, CheatRiskLogDto.class, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
            Map<Long, Long> userCheatHitMapPartition = cheatRiskLogDtos.stream().filter(c -> c.getMsg().contains("空载持续时间大于") || "用户空跑".equals(c.getMsg()))
                    .collect(Collectors.groupingBy(CheatRiskLogDto::getUser_id, Collectors.counting()));
            userCheatHitMapPartition.forEach((key, value) -> userCheatHitMap.merge(key, value, Long::sum));
        }

        return userCheatHitMap;
    }

    public List<AwardConfigDto> getAwardConfigs(Long activityId) {
        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(activityId);
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        List<AwardConfigDto> awardConfigDtos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collect)) {
            awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
        }
        return awardConfigDtos;
    }


    public List<AwardReviewUserDto> buildReviewResults(List<ZnsRunActivityUserEntity> allActivityUser, ActivityContext context, ActivityResultContext resultContext, List<AwardConfigDto> awardConfigs, Map<Long, ZnsUserEntity> userMap, Map<Long, Long> userCheatHitMap, Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap) {
        List<AwardReviewUserDto> records = new ArrayList<>();

        for (ZnsRunActivityUserEntity runActivityUser : allActivityUser) {
            AwardReviewUserDto awardReviewUserDto = getAwardReviewUserDto(runActivityUser, userMap.get(runActivityUser.getUserId()), context.getRankingBy(), userCheatHitMap, userRunDataDetailsMap);
            List<AwardConfigDto> configDtos = awardConfigBizService.getAwardConfigDtos(runActivityUser, awardConfigs);
            awardReviewUserDto.setRewardAmounts(awardConfigBizService.getRewardAmount(configDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), awardReviewUserDto.getRewardCurrency(), runActivityUser.getRank(), null));
            awardReviewUserDto.setRewardPoints(awardConfigBizService.getRewardPoints(configDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), runActivityUser.getRank(), resultContext.getCompleteCount()));
            if (runActivityUser.getIsComplete() == 1) {
                awardReviewUserDto.setCompleteRewardAmounts(awardConfigBizService.getRewardAmount(awardConfigs, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), awardReviewUserDto.getRewardCurrency(), null, resultContext.getCompleteCount()));
                awardReviewUserDto.setCompleteRewardPoints(awardConfigBizService.getRewardPoints(awardConfigs, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), null, resultContext.getCompleteCount()));
            }
            if (activityUserBizService.isSurpass(resultContext.getSurpassRunActivityUser(), runActivityUser)) {
                awardReviewUserDto.setSurpassRewardAmounts(awardConfigBizService.getRewardAmount(awardConfigs, AwardSentTypeEnum.SURPASS_AWARD.getType(), awardReviewUserDto.getRewardCurrency(), null, resultContext.getSurpassCount()));
                awardReviewUserDto.setSurpassRewardPoints(awardConfigBizService.getRewardPoints(awardConfigs, AwardSentTypeEnum.SURPASS_AWARD.getType(), null, resultContext.getSurpassCount()));
            }
            awardReviewUserDto.setCheatingState(YesNoStatus.NO.getCode());
            if (awardReviewUserDto.getRank() == -1) {
                if (context.getMainActivity().getAwardSendStatus().equals(YesNoStatus.YES.getCode())) {
                    awardReviewUserDto.setStatus(-1);
                }
                awardReviewUserDto.setCheatingState(YesNoStatus.YES.getCode());
            }


            records.add(awardReviewUserDto);

        }
        return records;
    }

    public AwardReviewUserDto getAwardReviewUserDto(ZnsRunActivityUserEntity runActivityUser, ZnsUserEntity user, String rankingBy, Map<Long, Long> userCheatHitMap, Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap) {
        AwardReviewUserDto awardReviewUserDto = new AwardReviewUserDto();
        awardReviewUserDto.setId(runActivityUser.getId());
        awardReviewUserDto.setUserId(runActivityUser.getUserId());
        awardReviewUserDto.setUserCode(user.getUserCode());
        awardReviewUserDto.setUserCountry(user.getCountry());
        awardReviewUserDto.setUserNickname(user.getFirstName());
        awardReviewUserDto.setRank(runActivityUser.getRank());
        awardReviewUserDto.setRaceResult(runActivityUserService.getRaceResult(rankingBy, runActivityUser));
        awardReviewUserDto.setRaceResultType(rankingBy);
        awardReviewUserDto.setRewardCurrency(I18nConstant.CurrencyCodeEnum.USD.getCurrency());
        awardReviewUserDto.setStatus(0);
        awardReviewUserDto.setCheatCheatHitCount(userCheatHitMap.get(awardReviewUserDto.getUserId()));

        List<ZnsUserRunDataDetailsEntity> userRunDataDetailsEntities = userRunDataDetailsMap.get(runActivityUser.getUserId());
        if (CollectionUtils.isEmpty(userRunDataDetailsEntities)) {
            awardReviewUserDto.setCheatRunCount(0);
            return awardReviewUserDto;
        }
        Long cheatCount = userRunDataDetailsEntities.stream().filter(d -> d.getLastTime().isAfter(runActivityUser.getCreateTime()) && d.getIsCheat() == 1).count();
        awardReviewUserDto.setCheatRunCount(cheatCount.intValue());
        return awardReviewUserDto;
    }

    public void resetSeriesActivityUserResult(ZnsRunActivityUserEntity runActivityUser, List<ZnsRunActivityUserEntity> allActivityUser, List<Long> activityIds) {
        runActivityUser.setRunTime(0);
        runActivityUser.setRunTimeMillisecond(0);
        runActivityUser.setRunMileage(BigDecimal.ZERO);
        runActivityUser.setPropRunTime(0);
        runActivityUser.setIsComplete(0);
        runActivityUser.setCompleteTime(null);

        allActivityUser.forEach(a -> {
            runActivityUser.setRunMileage(runActivityUser.getRunMileage().add(a.getRunMileage()));
            runActivityUser.setRunTime(runActivityUser.getRunTime() + a.getRunTime());
            runActivityUser.setRunTimeMillisecond(runActivityUser.getRunTimeMillisecond() + a.getRunTimeMillisecond());
            runActivityUser.setPropRunTime(runActivityUser.getPropRunTime() + a.getPropRunTime());
        });

        long noCompleteCount = allActivityUser.stream().filter(a -> a.getIsComplete() == 0).count();
        if (noCompleteCount == 0 && allActivityUser.size() == activityIds.size()) {
            runActivityUser.setIsComplete(1);
            ZnsRunActivityUserEntity lastActivityUser = allActivityUser.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getId).reversed()).findFirst().get();
            runActivityUser.setCompleteTime(lastActivityUser.getCompleteTime());
        }

        //计算平均配速
        runActivityUser.setAverageVelocity(SportsDataUnit.getVelocity(runActivityUser.getRunTimeMillisecond() / 1000, runActivityUser.getRunMileage()));
    }

    /**
     * 构建活动上下文
     */
    @Data
    @Builder
    public static class ActivityContext {
        private MainActivity mainActivity;
        private SeriesGameplay seriesGameplay;
        private EntryGameplay entryGameplay;
        private List<Long> activityIdList;
        private String rankingBy;

    }

    public ActivityContext buildActivityContext(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        List<Long> activityIdList;
        String rankingBy;
        SeriesGameplay seriesGameplay = null;
        EntryGameplay entryGameplay = null;

        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
            activityIdList = seriesActivityRelService.findSubActivityId(mainActivity.getId());
            entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            rankingBy = seriesGameplay.getRankingBy();
        } else {
            activityIdList = Lists.newArrayList(activityId);
            entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            rankingBy = entryGameplay.getRankingBy();
        }

        return ActivityContext.builder()
                .mainActivity(mainActivity)
                .seriesGameplay(seriesGameplay)
                .entryGameplay(entryGameplay)
                .activityIdList(activityIdList)
                .rankingBy(rankingBy)
                .build();
    }


    /**
     * 构建活动成绩上下文
     */
    @Data
    @Builder
    public static class ActivityResultContext {
        private ZnsRunActivityUserEntity surpassRunActivityUser;
        private Long surpassCount;
        private Long completeCount;
    }

    public ActivityResultContext buildActivityResultContext(Long activityId, List<ZnsRunActivityUserEntity> allActivityUser) {
        ZnsUserEntity surpassUser = activityParamsBizService.findSurpassUser(activityId);
        ZnsRunActivityUserEntity surpassRunActivityUser = null;
        Long surpassCount = 0l;
        if (Objects.nonNull(surpassUser)) {
            surpassRunActivityUser = allActivityUser.stream().filter(a -> a.getUserId().equals(surpassUser.getId())).findFirst().orElse(null);
            ZnsRunActivityUserEntity finalSurpassRunActivityUser = surpassRunActivityUser;
            surpassCount = allActivityUser.stream().filter(a -> activityUserBizService.isSurpass(finalSurpassRunActivityUser, a)).count();
        }
        return ActivityResultContext.builder()
                .surpassRunActivityUser(surpassRunActivityUser)
                .surpassCount(surpassCount)
                .completeCount(allActivityUser.stream().filter(a -> a.getIsComplete() == 1).count())
                .build();
    }
}
