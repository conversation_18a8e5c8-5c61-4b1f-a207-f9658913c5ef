package com.linzi.pitpat.data.awardservice.biz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.query.award.SendUserCouponResultDto;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.query.CouponQuery;
import com.linzi.pitpat.data.awardservice.model.resp.UserCouponResp;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.courseservice.model.request.CourseUpdateRequest;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.NumberOfTimesEnum;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.request.CouponPageQuery;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/17 7:50
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class UserCouponBizService {

    private final CouponService couponService;
    private final UserCouponService userCouponService;
    private final ZnsUserService userService;
    private final CouponI18nService couponI18nService;
    private final AppMessageService appMessageService;
    private final CurrencyBizService currencyBizService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ZnsUserEquipmentService userEquipmentService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    private final RedissonClient redissonClient;

    /**
     * 新用户引导发放的优惠券，多个用逗号隔开
     */
    @Value("${zns.config.newUser.couponIds:374}")
    private String newUserCouponIds;

    /**
     * 发放用户优惠券
     *
     * @param couponId
     * @param userId
     * @param activityId
     * @return
     */
    public Result sendUserCoupon(Long couponId, Long userId, Long activityId) {
        return sendUserCouponMilepost(couponId, userId, activityId, null);
    }

    public Result sendUserCouponMilepost(Long couponId, Long userId, Long activityId, String milepost) {
        Coupon coupon = couponService.selectCouponById(couponId);
        ZnsUserEntity znsUserEntity = userService.findById(userId);
        return exchangeUserCouponMilepost(coupon, null, znsUserEntity, 1, activityId, milepost);
    }

    /**
     * @param coupon
     * @param userEquipment
     * @param loginUser
     * @param awardType     1  表示领取， 2 表示兑换 ， 3 表示后台发放
     * @return
     */
    public Result exchangeUserCouponMilepost(Coupon coupon, ZnsUserEquipmentEntity userEquipment, ZnsUserEntity loginUser, Integer awardType, Long activityId, String milepost) {
        String key = RedisConstants.EXCHANGE_COUPON + loginUser.getId() + ":" + coupon.getId();
        RLock lock = redissonClient.getLock(key);
        CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, loginUser.getId());
        CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                .couponId(coupon.getId()).langCode(LocaleContextHolder.getLocale().toString()).defaultLangCode(coupon.getDefaultLangCode()).build());
        String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
            UserCoupon userCoupon = new UserCoupon();
            userCoupon.setCouponId(coupon.getId());
            userCoupon.setCouponMainType(coupon.getCouponMainType());
            userCoupon.setUserId(loginUser.getId());
            if (userEquipment != null) {
                userCoupon.setEquipmentNo(userEquipment.getEquipmentNo());
            }

            ZonedDateTime now = ZonedDateTime.now();
            if (coupon.getExpiryType() == 1) {
                userCoupon.setGmtStart(now);
                userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
            } else if (coupon.getExpiryType() == 2) {
                userCoupon.setGmtStart(coupon.getGmtStart());
                userCoupon.setGmtEnd(coupon.getGmtEnd());
            }

            userCoupon.setSourceType(1);
            userCoupon.setAmount(currencyAmount.getAmount());
            userCoupon.setCurrencyCode(currencyAmount.getCurrencyCode());
            userCoupon.setCurrencyName(currencyAmount.getCurrencyName());
            userCoupon.setCurrencySymbol(currencyAmount.getCurrencySymbol());
            userCoupon.setDiscount(coupon.getDiscount());
            userCoupon.setIsNew(YesNoStatus.YES.getCode());
            userCoupon.setActivityId(activityId);
            userCoupon.setMilepost(milepost);
            userCouponService.insert(userCoupon);
//            String msg;
//            if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType().equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
//                String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
//                String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", loginUser.getZoneId());
//                String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
//                if (!StringUtils.isEmpty(loginUser.getLanguageCode())) {
//                    msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg2", couponTypeName, expiryString);
//                } else {
//                    msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//                }
//
//            } else {
//                if (!StringUtils.isEmpty(loginUser.getLanguageCode())) {
//                    msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg1", couponTitle);
//                } else {
//                    msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg1", couponTitle);
//                }
//            }
//            appMessageService.sendImTextWithNoJump(loginUser, coupon, msg, userCoupon);
            couponService.addQuotaSend(coupon.getId());
        } catch (Exception e) {
            log.error("exchangeUserCoupon 失败，e", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("title", coupon.getTitle());
        return CommonResult.success(map);
    }

    /**
     * 使用券
     *
     * @param userCouponId
     * @param activityId
     * @param remarks
     * @return
     */
    @Transactional
    public Result useCoupon(Long userCouponId, Long activityId, String remarks) {
        String key = RedisConstants.USE_COUPON + userCouponId;
        RLock lock = redissonClient.getLock(key);

        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
            UserCoupon userCoupon = userCouponService.selectUserCouponById(userCouponId);
            if (Objects.isNull(userCoupon)) {
                return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
            }
            ZonedDateTime now = ZonedDateTime.now();
            if (now.compareTo(userCoupon.getGmtEnd()) > 0) {
                userCoupon.setStatus(3);
                userCoupon.setGmtModified(now);
                userCouponService.update(userCoupon);
                log.info("useCoupon 失败，券过期");
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.expired"));
            }
            if (userCoupon.getStatus() != 0) {
                log.info("useCoupon 失败，券不属于未使用状态");
                return CommonResult.fail("Coupon expired");
            }

            Coupon coupon = couponService.selectCouponById(userCoupon.getCouponId());
            if (Objects.isNull(coupon)) {
                return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
            }
            if (coupon.getCouponType() == 1 || coupon.getCouponType() == 2) {
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.appVersionLow"));
            } else if (coupon.getCouponType() == 3) {
                userAccountService.increaseAmount(userCoupon.getAmount(), userCoupon.getUserId(), false);
                userAccountDetailService.addAccountDetail(userCoupon.getUserId(), 1, AccountDetailTypeEnum.COUPON_REWARD, AccountDetailSubtypeEnum.COUPON3, userCoupon.getAmount(), null, null);
            }
            StringBuffer sb = new StringBuffer();
            sb.append(userCoupon.getRemarks() + "");
            sb.append(remarks);
            userCoupon.setRemarks(sb.toString());
            userCoupon.setStatus(2);
            userCoupon.setGmtUse(now);
            userCoupon.setGmtModified(now);
            userCoupon.setUseActivityId(activityId);
            userCouponService.update(userCoupon);
        } catch (Exception e) {
            log.error("useCoupon 失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return CommonResult.success();
    }

    /**
     * @param couponId
     * @param userId
     * @param activityId
     * @param sourceType
     * @param isManual
     * @return
     */
    public Result<SendUserCouponResultDto> sendUserCouponSource(Long couponId, Long userId, Long activityId, int sourceType, Boolean isManual) {
        return sendUserCouponSource(couponId, userId, activityId, sourceType, false, isManual);
    }

    /**
     * 发放优惠券是否唯一
     *
     * @param couponId
     * @param userId
     * @param activityId
     * @param sourceType
     * @param isUnique   true：唯一
     * @param isManual
     * @return
     */
    public Result<SendUserCouponResultDto> sendUserCouponSource(Long couponId, Long userId, Long activityId, int sourceType, boolean isUnique, Boolean isManual) {
        Coupon coupon = couponService.selectCouponById(couponId);
        ZnsUserEntity loginUser = userService.findById(userId);
        if (Objects.isNull(coupon)) {
            log.error("sendUserCouponSource error,coupon is null");
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                .couponId(coupon.getId()).langCode(loginUser.getLanguageCode()).defaultLangCode(coupon.getDefaultLangCode()).build());
        String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
        SendUserCouponResultDto resultDto = new SendUserCouponResultDto();

        String key = RedisConstants.EXCHANGE_COUPON + coupon.getId() + ":" + userId;
        RLock lock = redissonClient.getLock(key);
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemBusy"));
            }

            if (isUnique) {
                //用户已获得优惠券-无需再发
                UserCoupon userCoupon = userCouponService.selectUserCouponByCouponId(userId, couponId);
                if (userCoupon != null) {
                    log.info("用户已获得该优惠券，无需再发放优惠券，userId:{},couponId:{}， activityId={}, sourceType={}", userId, couponId, activityId, sourceType);
                    resultDto.setTitle(coupon.getTitle());
                    resultDto.setUserCouponId(userCoupon.getId());
                    resultDto.setGmtEnd(userCoupon.getGmtEnd());
                    resultDto.setAmount(userCoupon.getAmount());
                    resultDto.setDiscount(userCoupon.getDiscount());
                    return CommonResult.success(resultDto);
                }
            }

            CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, loginUser.getId());
            UserCoupon userCoupon = BeanUtil.copyBean(currencyAmount, UserCoupon.class);
            userCoupon.setCouponId(coupon.getId());
            userCoupon.setCouponMainType(coupon.getCouponMainType());
            userCoupon.setUserId(loginUser.getId());

            ZonedDateTime now = ZonedDateTime.now();
            if (coupon.getExpiryType() == 1) {
                userCoupon.setGmtStart(now);
                userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
            } else if (coupon.getExpiryType() == 2) {
                userCoupon.setGmtStart(coupon.getGmtStart());
                userCoupon.setGmtEnd(coupon.getGmtEnd());
            }

            userCoupon.setSourceType(sourceType);
            userCoupon.setAmount(currencyAmount.getAmount());
            userCoupon.setDiscount(coupon.getDiscount());
            userCoupon.setIsNew(YesNoStatus.YES.getCode());
            userCoupon.setActivityId(activityId);
            userCouponService.insert(userCoupon);

            resultDto.setTitle(coupon.getTitle());
            resultDto.setUserCouponId(userCoupon.getId());
            resultDto.setGmtEnd(userCoupon.getGmtEnd());
            resultDto.setAmount(userCoupon.getAmount());
            resultDto.setDiscount(userCoupon.getDiscount());

//            String msg;
//            if (CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type.equals(coupon.getCouponMainType())) {
//                //商城券
//                if (!StringUtil.isEmpty(loginUser.getLanguageCode())) {
//                    if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_2.type.equals(coupon.getDiscountMethod())) {
//                        //折扣
//                        msg = I18nMsgUtils.getMessage("push.content.user.mall.coupon.discount", Optional.of(coupon.getDiscount()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
//                    } else {
//                        //金额
//                        msg = I18nMsgUtils.getMessage("push.content.user.mall.coupon.amount", Optional.of(coupon.getAmount()).orElse(BigDecimal.ZERO));
//                    }
//                } else {
//                    if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_2.type.equals(coupon.getDiscountMethod())) {
//                        //折扣
//                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.mall.coupon.discount", Optional.of(coupon.getDiscount()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
//                    } else {
//                        //金额
//                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.mall.coupon.amount", Optional.of(coupon.getAmount()).orElse(BigDecimal.ZERO));
//                    }
//                }
//            } else if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType().equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
//                String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
//                String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", loginUser.getZoneId());
//                String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
//                if (StringUtils.hasText(loginUser.getLanguageCode())) {
//                    msg = I18nMsgUtils.getLangMessage(loginUser.getLanguageCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//                } else {
//                    msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//                }
//
//            } else {
//                if (StringUtils.hasText(loginUser.getLanguageCode())) {
//                    msg = I18nMsgUtils.getLangMessage(loginUser.getLanguageCode(), "coupon.im.congratulation", couponTitle);
//                } else {
//                    msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "coupon.im.congratulation", couponTitle);
//                }
//            }// "Congratulations! You have received the " + coupon.getTitle() + " . You can check it on \"Mine\" - \"My coupons\".";
//            appMessageService.sendImTextWithNoJump(loginUser, coupon, msg, userCoupon);
            if (!isManual) {
                couponService.addQuotaSend(coupon.getId());
            }
        } catch (Exception e) {
            log.error("exchangeUserCoupon 失败，e", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success(resultDto);
    }

    /**
     * 发放新手引导优惠券
     *
     * @param po
     * @param userId
     * @return
     */
    public Result sendUserCoupon(CourseUpdateRequest po, Long userId) {
        if (po == null) {
            po = new CourseUpdateRequest();
        }
        if (CollectionUtils.isEmpty(po.getCouponIds())) {
            ArrayList<Long> list = Lists.newArrayList();
            //未传优惠券使用默认的优惠券
            String[] split = newUserCouponIds.split(",");
            for (String couponIdStr : split) {
                if (StringUtils.hasText(couponIdStr)) {
                    list.add(Long.valueOf(couponIdStr));
                }
            }
            po.setCouponIds(list);
        }
        if (CollectionUtils.isEmpty(po.getCouponIds())) {
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.notConfigured"));
        }
        //发放优惠券
        for (Long couponId : po.getCouponIds()) {
            sendUserCouponSource(couponId, userId, null, CouponConstant.SourceTypeEnum.source_type_7.getType(), true);
        }
        return CommonResult.success(true);
    }

    /**
     * 发放券给用户
     *
     * @param userId
     * @param couponId
     * @param isSendAppMessage
     * @return
     */
    public Result<Void> sendCoupon(Long userId, Long couponId, Boolean isSendAppMessage) {
        // 获取优惠券信息
        ZonedDateTime now = ZonedDateTime.now();
        Coupon coupon = couponService.findOneByStatus(couponId, 1);

        CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                .couponId(coupon.getId()).langCode(LocaleContextHolder.getLocale().toString()).defaultLangCode(coupon.getDefaultLangCode()).build());
        String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
        if (coupon == null) {
            log.error("The coupon does not exist or has expired!");
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
        }
        Integer couponQuota = coupon.getQuota();
        if (couponQuota != null && couponQuota != -1) {
            if (couponQuota - coupon.getQuotaSend() <= 0) {
                log.error("This coupon is no longer available!");
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
        }
        // 判断是否领取过优惠券

        // 判断优惠券领取是否超过每个人限制领取张数
        Integer limitCount = coupon.getLimitCount();
        if (limitCount != null && limitCount != -1) {
            long count2 = userCouponService.findCount(userId, coupon.getId());
            if (count2 >= limitCount) {
                return CommonResult.fail("Redemption failed, each user can only redeem " + NumberOfTimesEnum.findByType(limitCount) + " for this coupon");
            }
        }

        Integer expiryType = coupon.getExpiryType();
        if (expiryType == 2) {
            ZonedDateTime gmtEnd = coupon.getGmtEnd();
            if (now.isAfter(gmtEnd)) {
                log.error("The coupon does not exist or has expired!");
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
        }
        // 获取当前用户的设备信息
        ZnsUserEquipmentEntity userEquipment = userEquipmentService.getUserEquipmentOne(userId);

        // 免费领取优惠券

        CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, userId);
        UserCoupon userCoupon = BeanUtil.copyBean(currencyAmount, UserCoupon.class);
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(couponId);
        if (coupon.getExpiryType() == 1) {
            userCoupon.setGmtStart(now);
            userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
        } else if (coupon.getExpiryType() == 2) {
            userCoupon.setGmtStart(coupon.getGmtStart());
            userCoupon.setGmtEnd(coupon.getGmtEnd());
        }
        userCoupon.setSourceType(5);
        userCoupon.setAmount(currencyAmount.getAmount());
        userCoupon.setDiscount(coupon.getDiscount());
        userCoupon.setEquipmentNo(userEquipment != null ? userEquipment.getEquipmentNo() : null);
        userCouponService.insert(userCoupon);

        // 增加领取数量
        couponService.addQuotaSend(coupon.getId());

        if (isSendAppMessage) {
            ZnsUserEntity znsUserEntity = new ZnsUserEntity();
            znsUserEntity.setId(userId);
            // 这里使用线程池,异步发送
//            taskExecutor.execute(() -> {
//                //String msg = "With a coupon, join Arbor Day Challenge for free. However, NO refund after using it.";
////                String msg;
////                if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType()
////                        .equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
////                    String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
////                    String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", znsUserEntity.getZoneId());
////                    String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
////                    if (!StringUtil.isEmpty(znsUserEntity.getLanguageCode())) {
////                        msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg2", couponTypeName, expiryString);
////                    } else {
////                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
////                    }
////                } else {
////                    if (!StringUtil.isEmpty(znsUserEntity.getLanguageCode())) {
////                        msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg1", couponTitle);
////                    } else {
////                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg1", couponTitle);
////                    }
////                }
////                appMessageService.sendImTextWithNoJump(znsUserEntity, coupon, msg, userCoupon);
//            });
        }

        return null;
    }

    public Page<UserCouponResp> findPageByCouponPageQuery(CouponPageQuery query) {
        if (query.getGmtStartTime() == null && query.getGmtEndTime() == null) {
            ZonedDateTime endTime = ZonedDateTime.now();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            calendar.add(Calendar.DATE, -30);
            ZonedDateTime startTime = calendar.toInstant().toEpochMilli();
            query.setGmtStartTime(startTime);
            query.setGmtEndTime(endTime);
        }
        if (Objects.nonNull(query.getCouponId()) || Objects.nonNull(query.getType()) || StringUtils.hasText(query.getCouponName())) {
            CouponQuery couponQuery = new CouponQuery().setCouponId(query.getCouponId()).setCouponName(query.getCouponName()).setType(query.getType()).setIsDelete(0);
            List<Coupon> couponList = couponService.findList(couponQuery);
            if (CollectionUtils.isEmpty(couponList)) {
                return new Page<>();
            }
            List<Long> couponIds = Optional.ofNullable(couponList)
                    .orElse(Collections.emptyList())  // 如果list为null，返回一个空列表
                    .stream()
                    .map(Coupon::getId)
                    .toList();
            query.setCouponIds(couponIds);
        }

        Page<UserCouponResp> page = userCouponService.findPage(query);

        List<UserCouponResp> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            for (UserCouponResp userCouponResp : records) {
                Coupon coupon = couponService.selectCouponById(userCouponResp.getCouponId());
                if (Objects.nonNull(coupon)) {
                    userCouponResp.setType(coupon.getType());
                    userCouponResp.setCouponName(coupon.getTitle());
                    userCouponResp.setCouponType(coupon.getCouponType());
                    userCouponResp.setExchangeCode(coupon.getExchangeCode());
                }
            }
        }
        return page;
    }
}
