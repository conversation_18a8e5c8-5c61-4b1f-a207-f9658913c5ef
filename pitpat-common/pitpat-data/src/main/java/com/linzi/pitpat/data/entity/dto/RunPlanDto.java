package com.linzi.pitpat.data.entity.dto;


import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.mapper.SysConfigMapper;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;

@Data
@NoArgsConstructor
public class RunPlanDto {

    // 已经跑了多少秒
    private int runSecond;
    // 0 未结束 ， 1 需要结束,如果是1 ，本次推送之后，将不再推送
    private int isFinished;
    // 还剩下多少秒
    private int remainSecond;
    // 已经跑了多少米
    private int runMileage;
    // 还剩下多少米
    private int remainMileage;
    // 目标多少米
    private int targetMileage;
    // 当前机器人的速度  ， km / h
    private BigDecimal speed;

    private String email;

    private Long userId;
    // 活动结束时间
    private ZonedDateTime activityEndTime;
    /**
     * 头像
     */
    private String headPortrait;

    // 昵称
    private String nickName;
    // 唯一码
    private String uniqueCode;

    private Long activityId;
    // 匹配id
    private Long mindUserMatchId;

    private int runMillSec; // 推送增加这个字段。这个字段代表机器人最终应该跑步的毫秒数

    public RunPlanDto(int runSecond, int isFinished, int remainSecond, int runMileage, int remainMileage, int targetMileage,
                      BigDecimal speed, String email, Long userId, ZonedDateTime runDate, ZonedDateTime activityEndTime, String headPortrait, String nickName,
                      Long activityId, Long mindUserMatchId
    ) {
        this.runSecond = runSecond;
        this.isFinished = isFinished;
        this.remainSecond = remainSecond;
        this.runMileage = runMileage;
        this.remainMileage = remainMileage;
        this.targetMileage = targetMileage;
        if (isFinished == 1) {
            String randomMillisecond = "0~500";
            try {
                SysConfigMapper sysConfigMapper = SpringContextUtils.getBean(SysConfigMapper.class);
                SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.s_plus_config.getCode());
                Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
                Object value = data.get("randomMillisecond");
                if (value != null) {
                    randomMillisecond = value.toString();
                }
            } catch (Exception e) {
            }
            runMillSec = (runSecond + remainSecond) * 1000 + NumberUtils.getRandomStep(randomMillisecond, new BigDecimal(1), 1).intValue();
        }
        this.speed = speed;
        this.email = email;
        this.userId = userId;
        this.headPortrait = headPortrait;
        this.nickName = nickName;
        this.uniqueCode = mindUserMatchId + "";
        this.activityId = activityId;
        this.mindUserMatchId = mindUserMatchId;
        if (activityEndTime == null) {
            this.activityEndTime = DateUtil.addSeconds(runDate == null ? ZonedDateTime.now() : runDate, remainSecond);
        } else {
            this.activityEndTime = activityEndTime;
        }
    }


}
