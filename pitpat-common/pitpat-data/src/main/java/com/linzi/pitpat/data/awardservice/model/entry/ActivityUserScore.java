package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */

@Data
@NoArgsConstructor
@TableName("zns_activity_user_score")
public class ActivityUserScore implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                           // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";                              //
    public final static String gmt_create = CLASS_NAME + "gmt_create";                            //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                        //
    public final static String activity_id = CLASS_NAME + "activity_id";                          // 活动id
    public final static String user_id = CLASS_NAME + "user_id";                                  // 用户id
    public final static String score_ = CLASS_NAME + "score";                                     // 分数
    public final static String rank_ = CLASS_NAME + "rank";                                       // 排名
    public final static String source_ = CLASS_NAME + "source";                                   // -1 过期 ，-2 积分兑换券， -3.积分兑换商品 ,-4.排行赛挑战花费，-5.积分兑换其他，1. 签到 ， 2.设备连接 , 3.每运动1英里 , 4.完成一次课程 , 5.参与官方比赛 6. 参加PK赛，7.完成新人任务 8-完成评价 9-调查问卷,10-里程碑积分,11、活动奖励  12、活动进阶奖励，14.挑战跑离线pk，15.数据pk，16.团队赛，17、积分墙奖励 18、激励奖励(鼓掌) 、19激励奖励(加油)、20激励奖励(击掌)
    public final static String status_ = CLASS_NAME + "status";                                   // 0.待领取，1 领取成功， 2 已经使用，3 部分使用， -1 已经过期
    public final static String course_id = CLASS_NAME + "course_id";                              // 课程id
    public final static String coupon_id = CLASS_NAME + "coupon_id";                              // 优惠券id
    public final static String expire_time = CLASS_NAME + "expire_time";                          // 过期时间
    public final static String exchange_time = CLASS_NAME + "exchange_time";                      // 兑换时间
    public final static String use_score = CLASS_NAME + "use_score";                              // 已经使用了多少积分， 或兑换商品使用的积分
    public final static String income_ = CLASS_NAME + "income";                                   // 1 表示收入，-1 表示支出
    public final static String type_ = CLASS_NAME + "type";                                       //1 表示兑换的是优惠券， 2 表示兑换的是服装 3:标识补卡,4活动使用,5音乐,6道具,7实物,8其他, 9活动报名
    public final static String exchange_order_no = CLASS_NAME + "exchange_order_no";              // 兑换流水号
    public final static String exchange_score_rule_id = CLASS_NAME + "exchange_score_rule_id";    // 兑换的规则id
    public final static String send_time = CLASS_NAME + "send_time";                              // 发放时间
    public final static String award_time = CLASS_NAME + "award_time";                            // 领取时间
    public final static String score_config_id = CLASS_NAME + "score_config_id";                  // 积分领取配置id
    public final static String milepost_ = CLASS_NAME + "milepost";                                // 里程目标id
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //活动id
    private Long activityId;
    //用户id
    private Long userId;
    //分数
    private Integer score;

    //排名
    @TableField("`rank`")
    private Integer rank;
    //-1 过期 ，-2 积分兑换券， -3.积分兑换商品 ,-4.排行赛挑战花费，-5.积分兑换其他，1. 签到 ， 2.设备连接 , 3.每运动1英里 , 4.完成一次课程 , 5.参与官方比赛 6. 参加PK赛，7.完成新人任务 8-完成评价 9-调查问卷,10-里程碑积分,11、活动奖励  12、活动进阶奖励，14.挑战跑离线pk，15.数据pk，16.团队赛，17、积分墙奖励 18、激励奖励(鼓掌) 、19激励奖励(加油)、20激励奖励(击掌)、21活动报名, 22必胜券（补名次差额）
    /**
     * @see ScoreConstant.SourceTypeEnum
     */
    private Integer source;
    //0.待领取，1 领取成功， 2 已经使用，3 部分使用， -1 已经过期
    private Integer status;
    //课程id
    private Long courseId;
    //优惠券id
    private Long couponId;
    /**
     * 过期时间
     * /awardScore 也会修改
     */
    private ZonedDateTime expireTime;
    //兑换时间
    private ZonedDateTime exchangeTime;
    //已经使用了多少积分， 或兑换商品使用的积分
    private Integer useScore;
    // 兑换花费金额
    private BigDecimal amount;
    //1 表示收入，-1 表示支出
    private Integer income;
    //1 表示兑换的是优惠券， 2 表示兑换的是服装 3:标识补卡,4活动使用,5音乐,6道具,7实物,8其他, 9活动报名
    private Integer type;
    //兑换流水号
    private String exchangeOrderNo;
    //兑换的规则id
    private Long exchangeScoreRuleId;
    //发放时间
    private ZonedDateTime sendTime;
    //领取时间
    private ZonedDateTime awardTime;
    //积分领取配置id
    private Long scoreConfigId;
    /**
     * 里程目标id
     */
    private Integer milepost;
    /**
     * 额外分数，已包含在分数字段中
     */
    private Integer extraScore;
    /**
     * 14天新人任务明细id
     */
    private Long newPersonActivityTaskId;

    /**
     * 兑换物品id
     */
    private Long exchangeId;

    @Override
    public String toString() {
        return "ActivityUserScore{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",activityId=" + activityId +
                ",userId=" + userId +
                ",score=" + score +
                ",rank=" + rank +
                ",source=" + source +
                ",status=" + status +
                ",courseId=" + courseId +
                ",couponId=" + couponId +
                ",expireTime=" + expireTime +
                ",exchangeTime=" + exchangeTime +
                ",useScore=" + useScore +
                ",income=" + income +
                ",type=" + type +
                ",exchangeOrderNo=" + exchangeOrderNo +
                ",exchangeScoreRuleId=" + exchangeScoreRuleId +
                ",sendTime=" + sendTime +
                ",awardTime=" + awardTime +
                ",scoreConfigId=" + scoreConfigId +
                "}";
    }
}
