package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 奖励配置表
 *
 * <AUTHOR>
 * @since 2023-07-14
 */

@Data
@NoArgsConstructor
@TableName("zns_award_config")
public class AwardConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.AwardConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";          //
    public final static String gmt_create = CLASS_NAME + "gmt_create";        //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    //
    public final static String send_type = CLASS_NAME + "send_type";          // 发放类型，1：排名基础奖励，2：完赛，3：被挑战奖励 4：挑战成功奖励，5：挑战失败奖励 6：排名人头奖励 7:发起奖励 8：参与奖励 9:胜者奖励 10奖金池基础 11：奖金池占比
    public final static String award_type = CLASS_NAME + "award_type";        // 奖励类型，1：金额，2：券，3：积分 , 4:皮肤, 5：会员奖励, 6：经验奖励, 7：勋章奖励
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    @TableLogic
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //发放类型，1：排名基础奖励，2：完赛，3：被挑战奖励 4：挑战成功奖励，5：挑战失败奖励 6：排名人头奖励 7:发起奖励 8：参与奖励 9:胜者奖励 10奖金池基础 11：奖金池占比'
    private Integer sendType;
    /**
     * 奖励类型  1：金额，2：券，3：积分 4:皮肤 5:会员奖励 6:经验奖励 7：勋章奖励
     *
     * @see AwardTypeEnum
     */
    private Integer awardType;
    /**
     * 奖励条件类型，0：无，1：付费
     */
    private Integer awardCondition;

    @Override
    public String toString() {
        return "AwardConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",sendType=" + sendType +
                ",awardType=" + awardType +
                "}";
    }
}
