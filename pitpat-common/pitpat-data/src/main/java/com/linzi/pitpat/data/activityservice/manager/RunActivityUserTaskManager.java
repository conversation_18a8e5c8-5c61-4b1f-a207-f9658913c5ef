package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.OfficialCumulativeRunDto;
import com.linzi.pitpat.data.activityservice.model.dto.PropAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.entity.PropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.UserPropDetail;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.PropConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityConfigResp;
import com.linzi.pitpat.data.activityservice.model.resp.TaskResp;
import com.linzi.pitpat.data.activityservice.service.PropConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.biz.UserWearsBizService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.service.prop.UserPropDetailService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户任务处理类
 *
 * <AUTHOR>
 * @date 2024/6/19 14:03
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunActivityUserTaskManager {
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final ZnsCourseService courseService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final AppMessageService appMessageService;
    private final ISysConfigService sysConfigService;
    private final RedisTemplate redisTemplate;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ActivityUserScoreService activityUserScoreService;
    private final OperationalActivityService operationalActivityService;
    private final ZnsUserEquipmentService userEquipmentService;
    private final PropConfigService propConfigService;
    private final UserPropDetailService userPropDetailService;
    private final RabbitTemplate rabbitTemplate;
    private final UserPropRecordService userPropRecordService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final UserWearsBizService userWearsBizService;
    private final UserCouponBizService userCouponBizService;
    private final RedissonClient redissonClient;

    @Value("${zns.config.rabbitQueue.userPropBagQueue}")
    private String userPropBagQueue;
    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    /**
     * 用户任务处理
     *
     * @param runData
     * @param userRunDataDetail
     * @param activityEntity
     */
    public void dealUserTask(RunDataRequest runData, ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsRunActivityEntity activityEntity) {
        log.info("dealUserTask=====================");
        //作弊不算完成，不处理
        if (userRunDataDetail.getIsCheat() == 1) {
            log.info("dealUserTask 结束，作弊不处理");
            return;
        }
        RunActivityUserTask task = null;
        if (Objects.isNull(runData.getTaskId()) || runData.getTaskId() == 0) {
            //客户端新人挑战没有立即进入的时候会缺失taskId
            if (Objects.isNull(userRunDataDetail.getActivityId()) || userRunDataDetail.getActivityId() == 0) {
                return;
            }
            task = runActivityUserTaskService.selectRunActivityUserTaskOneByActivityId(userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
        } else {
            task = runActivityUserTaskService.findById(runData.getTaskId());
        }

        if (Objects.isNull(task)) {
            log.info("dealUserTask 结束，任务不存在");
            return;
        }
        if (task.getStatus() != 0) {
            return;
        }
        userRunDataDetail.setTaskId(task.getId());
        runData.setTaskId(task.getId());
        task.setTaskTime(ZonedDateTime.now());

        //挑战任务处理放到挑战结束处理
        Integer taskType = task.getTaskType();
        if (taskType == 1) {
            if (userRunDataDetail.getRunTime() >= 60 && userRunDataDetail.getRunMileage().compareTo(userRunDataDetail.getDistanceTarget()) >= 0) {
                task.setStatus(3);
                task.setTaskTime(null);
                runActivityUserTaskService.update(task);
            }
            return;
        } else if (taskType == 2) {
            task.setRunDataDetailsId(userRunDataDetail.getId());
            if (!task.getCourseId().equals(runData.getCourseId())) {
                log.warn("不是指定课程");
                return;
            }
            //判断完成情况
            ZnsCourseEntity course = courseService.selectById(task.getCourseId());
            if (course.getCourseDuration() <= userRunDataDetail.getRunTime()) {
                task.setStatus(1);
                task.setAward(task.getWinReward());
            } else {
                return;
            }
        } else if (taskType == 3) {
            task.setRunDataDetailsId(userRunDataDetail.getId());
            task.setRunMileage(userRunDataDetail.getRunMileage().intValue());
            task.setRunTime(userRunDataDetail.getRunTime());
            if (YesNoStatus.YES.getCode().equals(activityEntity.getPropSupport())) {
                //马拉松活动
                Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(task.getUserId(), activityEntity.getId(), userRunDataDetail.getId(), task.getLevel());
                if (effectValue > 0) {
                    task.setPropEffectTime(effectValue);
                }
            }
            if (task.getMileageTarget() > 0 && userRunDataDetail.getRunMileage().intValue() >= task.getMileageTarget()) {
                task.setStatus(1);
                // 关卡奖励
                ActivityConfigResp configResp = JsonUtil.readValue(activityEntity.getActivityConfig(), ActivityConfigResp.class);
                List<TaskResp> tasks = configResp.getTasks();
                if (!CollectionUtils.isEmpty(tasks)) {
                    ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(task.getActivityId(), task.getUserId());
                    Map<Integer, TaskResp> collect = tasks.stream().collect(Collectors.toMap(TaskResp::getLevel, Function.identity()));
                    TaskResp taskResp = collect.get(task.getLevel());
                    if (Objects.nonNull(taskResp)) {
                        if (sendUserLevelAward(activityUser, taskResp)) {
                            // im 通知
//                            sendUserLevelAwardIm(activityEntity, task.getLevel(), activityUser);
                        }
                        ;
                    }
                    TaskResp last = tasks.get(tasks.size() - 1);
                    //最后一关卡 发送完赛奖励
                    if (last.getLevel().equals(task.getLevel())) {
                        BigDecimal finishAward = configResp.getFinishAward();
                        // 1.用户完赛奖励(钱)
                        if (!Objects.isNull(finishAward)) {
                            Currency userCurrency = userAccountService.getUserCurrency(activityUser.getUserId());
                            // 官当活动兼容汇率计算
                            if (!userCurrency.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
                                ExchangeRateConfigEntity exchangeRateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(userCurrency.getCurrencyCode());
                                if (Objects.nonNull(exchangeRateConfigEntity)) {
                                    // 处理货币汇率问题
                                    finishAward = finishAward.multiply(exchangeRateConfigEntity.getExchangeRate()).setScale(2, RoundingMode.HALF_UP);
                                }
                            }
                            finishAward = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), finishAward);
                            //修改货架币种切换处理
                            List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                            if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
                                finishAward = BigDecimal.ZERO;
                                log.info("币种切换不发放");
                            }
                            activityUser.setRunAward(finishAward);
                            if (finishAward.compareTo(BigDecimal.ZERO) > 0) {
                                sendUserFinishedAwardAmount(activityEntity, activityUser, userRunDataDetail.getId());
                                sendUserFinishedAwardImMessage(activityEntity, activityUser);
                            }
                        }
                        //完成时间
                        activityUser.setCompleteTime(ZonedDateTime.now());
                        activityUser.setIsComplete(YesNoStatus.YES.getCode());
                        activityUser.setSubState(YesNoStatus.YES.getCode());
                        runActivityUserService.updateById(activityUser);
                    }
                }
                task.setAward(task.getWinReward());
            } else {
                return;
            }
        } else if (taskType == 4) {
            // 完成一次官方多人同跑
			/*Integer runStatus = userRunDataDetail.getRunStatus();
			if (runStatus == 1) {
				task.setStatus(2);
			}*/
            task.setRunDataDetailsId(userRunDataDetail.getId());
            BigDecimal runMileage = userRunDataDetail.getRunMileage();
            task.setRunMileage(runMileage.intValue());
            task.setRunTime(userRunDataDetail.getRunTime());
            Long activityId = task.getActivityId();

            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .select(List.of(ZnsRunActivityUserEntity::getId,
                            ZnsRunActivityUserEntity::getTargetRunMileage))
                    .activityId(activityId).userId(userRunDataDetail.getUserId()).isDelete(0).source(1).build();
            userQuery.setOrders(List.of(OrderItem.desc("id")));

            ZnsRunActivityUserEntity runActivityUser = runActivityUserService.findOne(userQuery);
            if (runActivityUser != null) {
                Integer targetRunMileage = runActivityUser.getTargetRunMileage();
                if (userRunDataDetail.getRunMileage().compareTo(new BigDecimal(targetRunMileage)) >= 0) {
                    task.setStatus(1);
                    task.setAward(task.getWinReward());
                } else {
                    runActivityUserTaskService.update(task);
                    return;
                }
            } else {
                runActivityUserTaskService.update(task);
                return;
            }
        }
        //一周快乐跑不在这儿处理
        if (Objects.nonNull(activityEntity) && activityEntity.getActivityType() == 7) {
            return;
        }
        runActivityUserTaskService.update(task);

        if (task.getActivityType() == 6) {
            addTaskAward(task.getAward(), task, userRunDataDetail, AccountDetailTypeEnum.NEW_USER_AWARD, AccountDetailTypeEnum.NEW_USER_AWARD.getName());
        } else if (task.getActivityType() == 8) {
            //马拉松最后一关统一发奖励
            RunActivityUserTask lastLevel = runActivityUserTaskService.selectRunActivityUserTaskByLevel(userRunDataDetail.getUserId(), task.getLevel() + 1, task.getActivityId());
            if (Objects.isNull(lastLevel)) {
                runData.setActivityUserEnd(1);
            }
        }

        //解锁下一级任务
        unlockNextLevelTask(task.getId(), task.getUserId(), task.getLevel(), task.getAward(), task.getActivityType());
    }

    /**
     * 解锁下一级任务
     *
     * @param id
     * @param userId
     * @param level
     * @param award
     * @param activityType
     */
    public void unlockNextLevelTask(Long id, Long userId, Integer level, BigDecimal award, Integer activityType) {
        //查询下一级任务
        RunActivityUserTask task = runActivityUserTaskService.findNextTask(userId, activityType, id);


        if (activityType == 6) {
            if (Objects.isNull(task)) {
                //完成所有，通知用户
//                ActivityNotificationEnum activityNotification = ActivityNotificationEnum.NEW_USER_STORM_PASS_ALL;
//                String content = I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.NEW_USER_STORM_PASS_ALL");
//                MessageBo message = appMessageService.assembleMessage("4", "", content, NoticeTypeEnum.NEW_PERSON_CHALLENGE.getType());
//                appMessageService.sendImAndPushUserIds(Arrays.asList(userId), message, content);

//                String config = sysConfigService.selectConfigByKey("task.last.push.config");
//                if (StringUtil.isEmpty(config)) {
//                    return;
//                }
//                Map<String, Object> jsonObject = JsonUtil.readValue(config);
//                String imageUrl = MapUtil.getString(jsonObject.get("imageUrl"));
//                String initiatorUserId = MapUtil.getString(jsonObject.get("initiatorUserId"));
//                appMessageService.sendIm(initiatorUserId, Arrays.asList(userId), imageUrl, TencentImConstant.TIM_IMAGE_ELEM, "", 0, Boolean.FALSE);
                return;
            } else {
                //文案推送
//                ActivityNotificationEnum activityNotification = ActivityNotificationEnum.NEW_USER_STORM_PASS_SUCCESS;
//                String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.NEW_USER_STORM_PASS_SUCCESS"), level, award, task.getLevel(), task.getWinReward());
//                MessageBo message = appMessageService.assembleMessage("4", "", content, NoticeTypeEnum.NEW_PERSON_CHALLENGE.getType());
//                message.setActivityId(task.getActivityId());
//                appMessageService.sendImAndPushUserIds(Arrays.asList(task.getUserId()), message, content);

                //活动推送
//                String config = sysConfigService.selectConfigByKey("new.activity.push.config");
//                Map<String, Object> jsonObject = JsonUtil.readValue(config);
//                String imageUrl = MapUtil.getString(jsonObject.get("imageUrl"));
//                String activityContent = MapUtil.getString(jsonObject.get("content"));
//                ImMessageBo imMessageBo = new ImMessageBo();
//                imMessageBo.setJumpType("0");
//                imMessageBo.setJumpValue(mallH5Url + "/newPeople");
//                imMessageBo.setImageUrl(imageUrl);
//                imMessageBo.setMsg(activityContent);
//                appMessageService.sendIm("", Arrays.asList(task.getUserId()), JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
            }
        } else {
            if (Objects.isNull(task)) {
                return;
            } else {
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(task.getActivityId(), task.getUserId());
                ZonedDateTime now = ZonedDateTime.now();
                if (Objects.nonNull(activityUser) && Objects.nonNull(activityUser.getUserActivityEndTime()) && now.compareTo(activityUser.getUserActivityEndTime()) > 0) {
                    return;
                }
            }
        }

        task.setIsUnlock(1);
        runActivityUserTaskService.update(task);
    }

    /**
     * 奖励领取
     *
     * @param userTasks
     */
    public void awardReceived(List<RunActivityUserTask> userTasks) {
        if (CollectionUtils.isEmpty(userTasks)) {
            return;
        }
        Long userId = userTasks.get(0).getUserId();
        RLock lock = redissonClient.getLock(RedisConstants.AWARD_RECEIVED + userId);
        try {
            if (lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                for (RunActivityUserTask userTask : userTasks) {
                    //查询奖励是否发放
                    ZnsUserAccountDetailEntity accountDetail = userAccountDetailService.getAccountDetail(userTask.getUserId(), userTask.getActivityId(), AccountDetailTypeEnum.FESTIVAL_ACTIVITIES_REWARD.getType(), userTask.getLevel());
                    if (Objects.nonNull(accountDetail)) {
                        return;
                    }
                    // 给用户余额发送奖励
                    userAccountService.increaseAmount(userTask.getAward(), userTask.getUserId(), true);
                    // 新增用户奖励余额明细
                    String billNo = NanoId.randomNanoId();
                    ;
                    ZonedDateTime tradeTime = ZonedDateTime.now();

                    userAccountDetailService.addRunActivityAccountDetail0131(userTask.getUserId(), AccountDetailTypeEnum.FESTIVAL_ACTIVITIES_REWARD,
                            userTask.getLevel(), 1, userTask.getAward(), billNo, tradeTime,
                            userTask.getActivityId(), userTask.getActivityId(), null, 7, userTask.getId(),
                            "", null, null, null, BigDecimal.ZERO);
                    userTask.setAwardStatus(2);
                }
            }
        } catch (Exception e) {
            log.error("提现异常，e={}", e.getMessage(), e);
        }
    }


    /**
     * 发送用户关卡奖励 积分/卷
     *
     * @param activityUser [ZnsRunActivityUserEntity]
     * @param task         [TaskResp]
     */
    private boolean sendUserLevelAward(ZnsRunActivityUserEntity activityUser, TaskResp task) {
        boolean sendSuccess = false;
        log.info("sendUserLevelAward userId:{},level:{},score:{},couponId:{}", activityUser.getUserId(), task.getLevel(), task.getScore(), task.getCouponId());
        if (task.getScore() > 0) {
            activityUserScoreService.increaseAmount(task.getScore(), activityUser.getActivityId(), activityUser.getUserId(), activityUser.getRank(), 0, null);
            sendSuccess = true;
        }
        if (task.getCouponId() != null) {
            for (int num = 0; num < task.getNum(); num++) {
                userCouponBizService.sendUserCoupon(task.getCouponId(), activityUser.getUserId(), activityUser.getActivityId());
            }
            sendSuccess = true;
        }
        PropAwardDto propAwardDto = task.getPropAwardDto();
        if (Objects.nonNull(propAwardDto)) {
            PropConfig one = propConfigService.findOne(PropConfigQuery.builder()
                    .isDelete(YesNoStatus.NO.getCode()).propType(propAwardDto.getPropType())
                    .build());
            if (Objects.isNull(one)) {
                log.error("道具奖励类型不存在");
                return false;
            }
            UserPropDetail userPropDetail = new UserPropDetail();
            userPropDetail.setActivityId(activityUser.getActivityId());
            userPropDetail.setUserId(activityUser.getUserId());
            userPropDetail.setPropNum(propAwardDto.getPropNum());
            userPropDetail.setPropId(one.getPropId());
            userPropDetail.setSource(userPropDetail.getSource());
            userPropDetailService.insert(userPropDetail);
            rabbitTemplate.convertAndSend(userPropBagQueue, JsonUtil.writeString(userPropDetail));
            sendSuccess = true;
        }
        WearAwardDto wearAwardDto = task.getWearAwardDto();
        if (Objects.nonNull(wearAwardDto)) {
            try {
                MilepostWearAwardDto milepostWearsAwardDto = new MilepostWearAwardDto();
                BeanUtils.copyProperties(wearAwardDto, milepostWearsAwardDto);
                milepostWearsAwardDto.setMilepost(BigDecimal.ZERO);
                OfficialCumulativeRunDto officialCumulativeRunDto = new OfficialCumulativeRunDto();
                BeanUtils.copyProperties(activityUser, officialCumulativeRunDto);
                userWearsBizService.sendUserWearMilepost(milepostWearsAwardDto, officialCumulativeRunDto);
            } catch (Exception e) {
                log.error("发送服装奖励error:", e);
            }
            sendSuccess = true;
        }
        return sendSuccess;
    }

//    /**
//     * 发送关卡等级奖励
//     *
//     * @param activityEntity
//     * @param level
//     * @param activityUser
//     */
//    private void sendUserLevelAwardIm(ZnsRunActivityEntity activityEntity, Integer level, ZnsRunActivityUserEntity activityUser) {
//        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.TASK_ACTIVITY_LEVEL;
//        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.TASK_ACTIVITY_LEVEL"), level, activityEntity.getActivityTitle());
//        ImMessageBo bo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
//        bo.setJumpType("0");
//        bo.setJumpValue(mallH5Url + "/operational/runActivity/" + activityEntity.getId() + "/0");
//        appMessageService.sendIm("", Arrays.asList(activityUser.getUserId()), JsonUtil.writeString(bo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
//    }

    /**
     * 发送用户完赛奖金
     *
     * @param activityEntity [ZnsRunActivityEntity]
     * @param activityUser   [ZnsRunActivityUserEntity]
     */
    private void sendUserFinishedAwardAmount(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, Long detailsId) {
        boolean brandUser = userEquipmentService.isBrandUser(activityUser.getUserId(), activityEntity.getPrivilegeBrand());
        // 品牌权益处理
        boolean brandRightsInterestsAward = dealBrandRightsInterestsAward(activityEntity, activityUser, brandUser);
        runActivityUserService.updateById(activityUser);
        // 给用户余额发送奖励
        BigDecimal runAward = activityUser.getRunAward();
        userAccountService.increaseAmount(runAward, activityUser.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_AWARD,
                0, 1, runAward, billNo, tradeTime, 0L,
                activityEntity.getId(), detailsId, RunActivityTypeEnum.TASK_ACTIVITY.getType(), 0L, activityEntity.getAwardRemark(),
                brandRightsInterestsAward ? activityEntity.getPrivilegeBrand() : null, brandRightsInterestsAward ? activityEntity.getBrandRightsInterestsAward() : null, BigDecimal.ZERO, BigDecimal.ZERO);
    }

    /**
     * 发送用户完赛奖金后im通知
     *
     * @param activityEntity [ZnsRunActivityEntity]
     * @param activityUser   [ZnsRunActivityUserEntity]
     */
    private void sendUserFinishedAwardImMessage(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser) {
        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.TASK_ACTIVITY_AWARD;
        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.TASK_ACTIVITY_AWARD"), activityEntity.getActivityTitle(), activityUser.getRunAward());
        ImMessageBo bo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
        bo.setJumpType("0");
        OperationalActivity operationalActivity = operationalActivityService.selectByRunActivityId(activityEntity.getId());
        if (!Objects.isNull(operationalActivity)) {
            bo.setJumpValue(operationalActivity.getActivityUrl());
            appMessageService.sendIm("", Collections.singletonList(activityUser.getUserId()), JsonUtil.writeString(bo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
        }
    }


    /**
     * 处理品牌权益
     *
     * @param activityEntity
     * @param activityUserEntity
     * @param brandUser
     * @return
     */
    private boolean dealBrandRightsInterestsAward(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUserEntity, boolean brandUser) {
        if (activityEntity.getPrivilegeBrand() == -1) {
            log.info("dealBrandRightsInterestsAward activityEntity.getPrivilegeBrand() = -1");
            return false;
        }
        if (activityEntity.getBrandRightsInterestsAward() == -1) {
            log.info("dealBrandRightsInterestsAward activityEntity.getBrandRightsInterestsAward() = -1");
            return false;
        }
        if (!brandUser) {
            log.info("dealBrandRightsInterestsAward brandUser= false");
            return false;
        }
        if (activityEntity.getBrandRightsInterestsAward() == 1) {
            //奖励翻倍
            BigDecimal award = activityUserEntity.getRunAward().multiply(new BigDecimal(2));
            ZnsUserAccountEntity accountEntity = userAccountService.getUserAccount(activityUserEntity.getUserId());
            if (Objects.nonNull(accountEntity)) {
                award = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), award);
            }
            activityUserEntity.setRunAward(award);
        }
        return true;
    }

    /**
     * 发放任务奖励
     *
     * @param award
     * @param task
     * @param userRunDataDetail
     * @param awardEnum
     * @param remark
     */
    private void addTaskAward(BigDecimal award, RunActivityUserTask task, ZnsUserRunDataDetailsEntity userRunDataDetail, AccountDetailTypeEnum awardEnum, String remark) {
        if (award.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        // 给用户余额发送奖励
        userAccountService.increaseAmount(award, task.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(task.getUserId(), awardEnum,
                task.getLevel(), 1, task.getAward(), billNo, tradeTime, task.getRunDataDetailsId(),
                userRunDataDetail.getActivityId(), userRunDataDetail.getId(), userRunDataDetail.getActivityType(), 0L, remark, null, null, award, BigDecimal.ZERO);
    }
}
