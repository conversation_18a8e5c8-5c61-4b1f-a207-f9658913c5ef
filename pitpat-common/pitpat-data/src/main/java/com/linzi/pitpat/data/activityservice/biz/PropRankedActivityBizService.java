package com.linzi.pitpat.data.activityservice.biz;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/22 6:38
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PropRankedActivityBizService {
    private final PropRunRankedActivityUserService runRankedActivityUserService;
    private final PropUserRankedLevelService propUserRankedLevelService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;

    public PropRunRankedActivityUser updateRunRankedActivityUser(ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {
        PropRunRankedActivityUser runRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
        if (Objects.isNull(runRankedActivityUser)) {
            log.error("用户段位赛数据不存在，无法更新状态， userRunDataDetailId={} activityId={}, userId={}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
            return runRankedActivityUser;
        }

        runRankedActivityUser.setIsComplete(1);
        runRankedActivityUser.setCompleteTime(ZonedDateTime.now());
        //runRankedActivityUser.setRank(userRank);
        //runRankedActivityUser.setRunTime(userRunDataDetail.getRunTime());
        //runRankedActivityUser.setRunMileage(userRunDataDetail.getRunMileage());

        runRankedActivityUserService.update(runRankedActivityUser);
        return runRankedActivityUser;
    }

    /**
     * 计算用户隐藏分
     *
     * @param userId 用户id
     *               隐藏分
     *               * 1、小于30场比赛，隐藏分=(140*30+实际比赛隐藏分总和)/(实际比赛场次+30）
     *               * 2、大于等于30场比赛，隐藏分=(60场隐藏分总和)/(比赛场次)
     *               * 3、运动记录>500才能有效
     * @return 隐藏分
     */
    public BigDecimal calcUserHideScore(Long userId) {

        //用户出定位的隐藏分
        PropUserRankedLevel userRankedLevel = propUserRankedLevelService.findByUserId(userId);
        Integer initScore = 160;
        if (userRankedLevel != null && userRankedLevel.getPlacementScore() > 0) {
            initScore = userRankedLevel.getPlacementScore();
        }

        List<ZnsUserRunDataDetailsEntity> runDataDetails = userRunDataDetailsService.findRankScoreList(userId, EquipmentDeviceTypeEnum.TREADMILL.getCode());

        if (!org.springframework.util.CollectionUtils.isEmpty(runDataDetails) && runDataDetails.get(0) == null) {
            //防止数组中只有一个空对象
            runDataDetails = new ArrayList<>();
        }
        int size = runDataDetails.size();
        log.info("ZnsUserRunDataDetailsServiceImpl#calcUserHideScore-------->计算用户隐藏分,user={}, runDataDetails size={}", userId, size);
        //初始值（140*30）-> （用户初始定位成功的隐藏分数值*30）
        BigDecimal totalScore = BigDecimal.valueOf(initScore).multiply(BigDecimal.valueOf(30));
        int totalSize = 30;
        if (!CollectionUtils.isEmpty(runDataDetails)) {
            int realSize = runDataDetails.size();
            BigDecimal realTotalPace = calcDetailTotalHideScore(runDataDetails);
            if (realSize >= 30) {
                //大于等于30场比赛，隐藏分=(60场隐藏分总和)/(比赛场次)
                totalScore = realTotalPace;
                totalSize = realSize;
            } else {
                //小于30场比赛，隐藏分=(140*30+实际比赛隐藏分总和)/(实际比赛场次+30）
                totalScore = totalScore.add(realTotalPace);
                totalSize = totalSize + realSize;
            }
        }
        // 隐藏分=比赛隐藏分总和/比赛场次
        BigDecimal hideScore = totalScore.divide(BigDecimal.valueOf(totalSize), 2, RoundingMode.HALF_UP);
        if (hideScore.compareTo(BigDecimal.ZERO) < 0) {
            hideScore = BigDecimal.ZERO;
            log.warn("ZnsUserRunDataDetailsServiceImpl#calcUserHideScore-------->计算用户隐藏分,user={},实际隐藏分小于0，重置隐藏分，{}", userId, hideScore);
        }
        log.info("ZnsUserRunDataDetailsServiceImpl#calcUserHideScore-------->计算用户隐藏分,user=={}, count={},  hideScore={}", userId, size, hideScore);
        return hideScore;
    }

    /**
     * 计算明细总隐藏分
     *
     * @param detailEntities
     * @return
     */
    private BigDecimal calcDetailTotalHideScore(List<ZnsUserRunDataDetailsEntity> detailEntities) {
        BigDecimal totalHideScore = BigDecimal.ZERO;
        for (ZnsUserRunDataDetailsEntity detailEntity : detailEntities) {
            totalHideScore = totalHideScore.add(calcDetailHideScore(detailEntity));
        }
        return totalHideScore;
    }

    /**
     * 计算跑步明细隐藏分
     *
     * @param detailEntity 跑步明细
     *                     隐藏分
     *                     * 1、隐藏分=(设备理论最低配速-当前配速)/15
     *                     * 2、假设用户有效跑步数据平均配速为1000秒/1000米，用户隐藏分为:3600-1000/15=173.33·隐藏分保留小数点后2位，仅作截断
     *                     * 3、运动记录>500才能有效
     * @return 隐藏分
     */
    private BigDecimal calcDetailHideScore(ZnsUserRunDataDetailsEntity detailEntity) {
        if (detailEntity == null) {
            return BigDecimal.ZERO;
        }
        int pace = 3600 - detailEntity.getAveragePace();
        BigDecimal hideScore = BigDecimal.valueOf(pace).divide(BigDecimal.valueOf(15), 2, RoundingMode.DOWN);
        if (hideScore.compareTo(BigDecimal.ZERO) < 0) {
            hideScore = BigDecimal.ZERO;
        }
        return hideScore;
    }
}
