package com.linzi.pitpat.data.awardservice.model.request;



import java.time.ZonedDateTime;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class UserCouponSendBatchAddReq extends PageQuery {
    /**
     * 任务标题
     */
    @Size(max = 20, message = "标题过长")
    private String title;
    /**
     * 开始时间
     */
//    @NotNull(message = "执行时间缺失")
//    private ZonedDateTime gmtSend;
    /**
     * 券id
     *
     * @see UserCouponSendBatchAddReq#couponIds
     */
    @Deprecated
    private Long couponId;
    /**
     * 券ids
     */
    private String couponIds;
    /**
     * 删除 1 删除 0 不删除
     */
    private Integer isDelete;
    /**
     * 状态 【 0: 未生效 1 生效 -1 失效】
     */
    private Integer status;
    //推送用户(，分割)
    private String pushUserEmails;
    //发放积分数
    private Integer score;
    //发放服装
    private String wears;
    //发放勋章
    private String medalIds;
    //会员发放类型，0:有期限，1：永久
    private Integer vipSendType;
    //会员天数
    private Integer vipDays = 0;

}
