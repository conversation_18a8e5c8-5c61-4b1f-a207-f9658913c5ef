package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*激历配置表
 *
 * <AUTHOR>
 * @since 2022-10-17
 */

@Data
@NoArgsConstructor
@TableName("zns_urge_activity_config")
public class UrgeActivityConfig implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //活动id
    private Long activityId;
    //房间号
    private Long roomId;
    //状态
    private Integer status;
    //活动状态
    private Integer activityType;
    //活动时间
    private ZonedDateTime gmtStartTime;
    //活动结束时间
    private ZonedDateTime gmtEndTime;
    //模式名称
    private String modeName;
    //路由类型
    private Integer routeType;
    //配置id
    private Long urgeConfigId;
    //路线id
    private Long routeId;

    @Override
    public String toString() {
        return "UrgeActivityConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",activityId=" + activityId +
                ",roomId=" + roomId +
                ",status=" + status +
                ",activityType=" + activityType +
                ",gmtStartTime=" + gmtStartTime +
                ",gmtEndTime=" + gmtEndTime +
                ",modeName=" + modeName +
                ",routeType=" + routeType +
                ",urgeConfigId=" + urgeConfigId +
                ",routeId=" + routeId +
                "}";
    }
}
