package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 积分配置表
 *
 * <AUTHOR>
 * @since 2023-06-19
 */

@Data
@NoArgsConstructor
@TableName("zns_score_config")
public class ScoreConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";        // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    // 最后修改时间
    public final static String type_ = CLASS_NAME + "type";                   // 1. 签到 ， 2.设备连接 , 3.每运动1英里 , 4.完成一次课程 , 5.参与官方比赛 6. 参加PK赛
    public final static String cal_style = CLASS_NAME + "cal_style";          // day 表示按天， life 表示按一生计算
    public final static String score_ = CLASS_NAME + "score";                 // 每次多少积分
    public final static String limit_count = CLASS_NAME + "limit_count";      // 限制次数
    public final static String zh_title = CLASS_NAME + "zh_title";            // 中文标题
    public final static String en_title = CLASS_NAME + "en_title";            // 英文标题
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //1. 签到 ， 2.设备连接 , 3.每运动1英里 , 4.完成一次课程 , 5.参与官方比赛 6. 参加PK赛
    private Integer type;
    //day 表示按天， life 表示按一生计算
    private String calStyle;
    //每次多少积分
    private Integer score;
    //限制次数
    private Integer limitCount;
    //中文标题
    private String zhTitle;
    //英文标题
    private String enTitle;

    @Override
    public String toString() {
        return "ScoreConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",type=" + type +
                ",calStyle=" + calStyle +
                ",score=" + score +
                ",limitCount=" + limitCount +
                ",zhTitle=" + zhTitle +
                ",enTitle=" + enTitle +
                "}";
    }
}
