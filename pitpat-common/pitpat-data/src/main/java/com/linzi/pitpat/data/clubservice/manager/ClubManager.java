package com.linzi.pitpat.data.clubservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomStatusEnum;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RoomDo;
import com.linzi.pitpat.data.activityservice.model.query.MainActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomQuery;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RoomService;
import com.linzi.pitpat.data.clubservice.autoconfigure.OpenAiProperties;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubImHandleScenesEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubLevelBenefitEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubLevelConditionEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubManagerInviteEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberApplyStateEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberRoleEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberStateEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubModificationTypeEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubStateEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubTaskTypeEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.UserKolClubAuthorizationEnum;
import com.linzi.pitpat.data.clubservice.convert.ClubConvert;
import com.linzi.pitpat.data.clubservice.convert.NewUserClubTaskConfigI8nConverter;
import com.linzi.pitpat.data.clubservice.model.ClubDisbandEvent;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityRoomRelationDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.model.entity.ClubLevelBenefitDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubLevelConditionDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubLevelConfig;
import com.linzi.pitpat.data.clubservice.model.entity.ClubManagerApplyDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMember;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMemberApply;
import com.linzi.pitpat.data.clubservice.model.entity.ClubReviewDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubRunDataDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskConfigDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskConfigI8nDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskI8nDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskRecordDo;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityRoomRelationQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityTeamQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubLevelBenefitQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubManagerApplyQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubMemberQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubRunDataQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskConfigI8nQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskConfigQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskI8nQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskRecordQuery;
import com.linzi.pitpat.data.clubservice.model.request.AppClubMemberInviteStateReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubCreateReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubDisbandReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubIdReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubLevelSetReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubListReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberApplyQuery;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberImportDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberListReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubNewUserTaskDaysReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubNewUserTaskFinishReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubSearchQuery;
import com.linzi.pitpat.data.clubservice.model.request.ClubUpdateReqDto;
import com.linzi.pitpat.data.clubservice.model.request.OpenAIRequestDto;
import com.linzi.pitpat.data.clubservice.model.request.clubMemberSwitchReqDto;
import com.linzi.pitpat.data.clubservice.model.response.AppClubInfoRespDto;
import com.linzi.pitpat.data.clubservice.model.response.AppClubMemberInviteStateRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubLevelBenefitDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubLevelCondition;
import com.linzi.pitpat.data.clubservice.model.response.ClubDetailResponseDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubLevelConfigRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubLevelInfoRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubListRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubMemberDetailResponseDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubNewUserTaskLevelRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubNewUserTaskRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubUserBaseInfoDto;
import com.linzi.pitpat.data.clubservice.model.response.UserClubJoinListRespDto;
import com.linzi.pitpat.data.clubservice.model.response.UserClubJoinListSearchReqDto;
import com.linzi.pitpat.data.clubservice.service.ClubActivityRoomRelationService;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubImService;
import com.linzi.pitpat.data.clubservice.service.ClubLevelBenefitService;
import com.linzi.pitpat.data.clubservice.service.ClubLevelConditionService;
import com.linzi.pitpat.data.clubservice.service.ClubLevelConfigService;
import com.linzi.pitpat.data.clubservice.service.ClubManagerApplyService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberApplyService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubReviewService;
import com.linzi.pitpat.data.clubservice.service.ClubRunDataService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskConfigI8nService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskConfigService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskI8nService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskRecordService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.ClubPageQueryDto;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserBenefitTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserBenefitConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserKol;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserBenefitConfigQuery;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.UserBenefitConfigService;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.UserLevelBenefitRelService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.util.page.PageConvert;
import com.linzi.pitpat.data.vo.im.GroupResultDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.exception.ExceptionFactory;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.linzi.pitpat.core.web.CommonError.BUSINESS_ERROR;
import static com.linzi.pitpat.core.web.CommonError.SYSTEM_ERROR;

@Service
@Slf4j
@RequiredArgsConstructor
@RefreshScope
public class ClubManager {
    private final ClubLevelConfigService clubLevelConfigService;
    private final ClubService clubService;
    private final ZnsUserService userService;
    private final ClubMemberService clubMemberService;
    private final ClubMemberApplyService clubMemberApplyService;

    private final QueueMessageService queueMessageService;
    private final UserKolService userKolService;

    private final UserLevelService userLevelService;

    private final UserBenefitConfigService userBenefitConfigService;

    private final UserLevelBenefitRelService userLevelBenefitRelService;

    private final TencentImUtil tencentImUtil;

    private final ISysConfigService sysConfigService;
    private final RedissonClient redissonClient;
    private final ClubImService clubImService;
    @Value("${spring.profiles.active}")
    private String envProfile;
    @Resource
    private ISysConfigService iSysConfigService;
    private final ClubMemberManager clubMemberManager;
    private final ClubActivityManager clubActivityManager;
    private final ClubActivityTeamService clubActivityTeamService;
    private final ClubActivityRoomRelationService clubActivityRoomRelationService;
    private final RoomService roomService;
    private static final String COUNTER_KEY = "new_user_club_count";
    private final OpenAiProperties openAiProperties;
    private final MainActivityService mainActivityService;
    private final ZnsUserService znsUserService;
    private final ClubRunDataService clubRunDataService;
    private final ClubLevelConditionService clubLevelConditionService;
    private final ClubLevelBenefitService clubLevelBenefitService;
    private final ClubManagerApplyService clubManagerApplyService;
    private final ClubReviewService clubReviewService;
    private final UserTaskBizService userTaskBizService;
    private final NewUserClubTaskConfigService newUserClubTaskConfigService;
    private final NewUserClubTaskService newUserClubTaskService;
    private final NewUserClubTaskConfigI8nService newUserClubTaskConfigI8nService;
    private final NewUserClubTaskI8nService newUserClubTaskI8nService;
    private final NewUserClubTaskConfigI8nConverter newUserClubTaskConfigI8nConverter;
    private final NewUserClubTaskRecordService newUserClubTaskRecordService;
    private final AppRouteService appRouteService;

    public Boolean canCreateClub(ZnsUserEntity user) {
        boolean levelClubFlag = checkUserLevelClubFlag(user.getId());
        //检查kol权限
        UserKol byUserIdSigned = userKolService.findByUserIdSigned(user.getId());
        boolean kolFlag = byUserIdSigned == null || UserKolClubAuthorizationEnum.YES.getCode() != byUserIdSigned.getClubAuthorization();
        if (!levelClubFlag && kolFlag && Objects.equals(user.getMemberType(), 0)) {
            return false;
        }
        //检查当前用户是否存在有效的club
        if (Objects.equals(user.getMemberType(), 0)) {
            Club club = clubService.findByQuery(new ClubSearchQuery().setOwnerUserid(user.getId()).setIsVipCreate(0));
            return !Objects.nonNull(club);
        } else {
            Club club = clubService.findByQuery(new ClubSearchQuery().setOwnerUserid(user.getId()).setIsVipCreate(1));
            if (Objects.isNull(club)) return true;
            if (levelClubFlag || !kolFlag) {
                Club byQuery = clubService.findByQuery(new ClubSearchQuery().setOwnerUserid(user.getId()).setIsVipCreate(0));
                return Objects.isNull(byQuery);
            }
        }
        return true;
    }

    /**
     * 用户等级的俱乐部创建权限
     *
     * @param userId
     * @return
     */
    public Boolean checkUserLevelClubFlag(Long userId) {
        UserBenefitConfig benefitConfig = userBenefitConfigService
                .findByQuery(UserBenefitConfigQuery.builder().benefitTypeList(List.of(UserBenefitTypeEnum.CLUB_CREATION.getCode())).build());
        if (benefitConfig == null) {
            return false;
        }
        Integer level = userLevelBenefitRelService.findByBenefitId(benefitConfig.getId()).getLevel();
        UserLevel userLevel = userLevelService.findByUserId(userId);
        return userLevel.getLevel() >= level;
    }


    /**
     * 设置俱乐部等级
     *
     * @param req
     * @return
     */
    public Boolean setClubLevel(ClubLevelSetReqDto req) {
        Club byId = clubService.findById(req.getClubId());
        if (byId == null || !ClubStateEnum.NORMAL.getCode().equals(byId.getState())) {
            throw new BaseException("俱乐部不存在或状态异常");
        }
        ClubLevelConfig byClubCode = clubLevelConfigService.findByClubCode(req.getLevelCode())
                .orElseThrow(() -> new BaseException("俱乐部等级设置错误"));
        return clubService.setClubLevel(req.getClubId(), req.getLevelCode(), req.getModifier());

    }

    @FillerMethod
    public Page<ClubListRespDto> queryClubPage(ClubListReqDto req) {
        ClubPageQueryDto query = ClubConvert.INSTANCE.clubListReqToQuery(req);
        if (StringUtils.hasText(req.getEmailAddress())) {
            ZnsUserEntity userByEmail = userService.findByEmail(req.getEmailAddress());
            if (userByEmail == null) {
                return new Page<>();
            }
            query.setUserId(userByEmail.getId());
        }
        Page<Club> clubPage = clubService.queryClubPage(query);
        Page<ClubListRespDto> resultClubPage = PageConvert.dataConvert(clubPage, ClubConvert.INSTANCE::clubToResp);
        return resultClubPage;
    }

    @FillerMethod
    public Page<UserClubJoinListRespDto> userClubListWithStatePage(UserClubJoinListSearchReqDto req) {
        Page<Club> clubPage = new Page<>();
        ClubSearchQuery query = new ClubSearchQuery();
        query.setSearchName(req.getClubName());
        query.setUserId(req.getUserId());
        query.setPageNum(req.getPageNum());
        query.setPageSize(req.getPageSize());
        ZnsUserEntity user = znsUserService.findById(req.getUserId());
        //增加一个俱乐部测试列表的过滤
        if (Objects.nonNull(user) && !Objects.equals(user.getIsTest(), 1)) {
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.TEST_CLUB_LIST.getCode());
            String configValue = sysConfig.getConfigValue();
            List<Long> testClubList = JsonUtil.readList(configValue, Long.class);
            query.setNeClubIds(testClubList);
        }
        if (Objects.nonNull(req.getOrderType())) {
            switch (req.getOrderType()) {
                case 0:
                    // 默认列表
//                    List<Club> clubList = new ArrayList<>();
//                    // 我的新人俱乐部
//                    List<Club> myNewClubList = clubService.findMyNewClub(req.getUserId());
//                    List<Long> newClubIds = myNewClubList.stream().map(Club::getId).collect(Collectors.toList());
//                    if (req.getPageNum() == 1) {
//                        // 第一页新人俱乐部要置顶
//                        clubList.addAll(myNewClubList);
//                    }
//                    if (!CollectionUtils.isEmpty(query.getNeClubIds())) {
//                        query.getNeClubIds().addAll(newClubIds);
//                    } else {
//                        query.setNeClubIds(newClubIds);
//                    }
                    clubPage = clubService.selectPageSortByQuery(query);
//                    if (!CollectionUtils.isEmpty(clubPage.getRecords())) {
//                        clubList.addAll(clubPage.getRecords());
//                    }
//                    clubPage.setRecords(clubList);
                    break;
                case 1:
                    //人数降序
                    query.setType(0);
                    query.addOrderByDesc("member_count");
                    query.addOrderByAsc("name");
                    clubPage = clubService.findPageByQuery(query);
                    break;
                case 2:
                    //人数升序
                    query.setType(0);
                    query.addOrderByAsc("member_count");
                    query.addOrderByAsc("name");
                    clubPage = clubService.findPageByQuery(query);
                    break;
                case 3:
                    //创建时间降序
                    query.setType(0);
                    query.addOrderByDesc("gmt_create");
                    query.addOrderByDesc("club_level");
                    query.addOrderByDesc("member_count");
                    query.addOrderByAsc("name");
                    clubPage = clubService.findPageByQuery(query);
                    break;
                case 4:
                    //创建时间升序
                    query.setType(0);
                    query.addOrderByAsc("gmt_create");
                    query.addOrderByDesc("club_level");
                    query.addOrderByDesc("member_count");
                    query.addOrderByAsc("name");
                    clubPage = clubService.findPageByQuery(query);
                    break;
            }
        } else {
            clubPage = clubService.selectPageSortByQuery(query);
        }
        if (CollectionUtils.isEmpty(clubPage.getRecords())) {
            return PageConvert.emptyConvert(clubPage);
        }
        List<Long> clubIds = clubPage.getRecords().stream().map(Club::getId).toList();
        Map<Long, ClubMemberStateEnum> joinState = getJoinState(clubIds, req.getUserId());
        return PageConvert.dataConvert(clubPage, club -> {
            UserClubJoinListRespDto userClubJoinListRespDto = ClubConvert.INSTANCE.clubToAppJoinClubRespDto(club);
            userClubJoinListRespDto.setUserJoinState(joinState.get(club.getId()).getCode());
            userClubJoinListRespDto.setOwner(userClubJoinListRespDto.getOwnerUserId().equals(req.getUserId()));
            userClubJoinListRespDto.setInviteCode(club.getRequiredInviteCode() ? club.getInviteCode() : "");
            if (userClubJoinListRespDto.getOwnerUserId().equals(req.getUserId())) {
                List<ClubMemberApply> clubMemberApplies = clubMemberApplyService.findListByQuery(ClubMemberApplyQuery.builder().clubId(club.getId()).state(ClubMemberStateEnum.APPLYING.getCode()).build());
                userClubJoinListRespDto.setPendingReviewNum(clubMemberApplies.size());
            }
            return userClubJoinListRespDto;
        });

    }

    /**
     * 新建俱乐部
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public Long createClub(@Valid ClubCreateReqDto req) {
        boolean levelClubFlag = checkUserLevelClubFlag(req.getOwnerId());
        //检查kol权限
        UserKol byUserIdSigned = userKolService.findByUserIdSigned(req.getOwnerId());
        ZnsUserEntity userEntity = userService.findById(req.getOwnerId());
        boolean kolFlag = byUserIdSigned == null || UserKolClubAuthorizationEnum.YES.getCode() != byUserIdSigned.getClubAuthorization();
        if (!levelClubFlag && kolFlag && req.getRoleCheck() && Objects.equals(userEntity.getMemberType(), 0)) {
            throw new BizI18nException("club.op.fail");
        }
        //检查当前用户是否存在有效的club
        if (req.getRoleCheck()) {
            if (Objects.equals(userEntity.getMemberType(), 0)) {
                Club club = clubService.findByQuery(new ClubSearchQuery().setOwnerUserid(userEntity.getId()).setIsVipCreate(0));
                if (Objects.nonNull(club)) throw new BaseException(I18nMsgUtils.getMessage("club.op.fail", req.getLanguageCode()));
            } else {
                Club club = clubService.findByQuery(new ClubSearchQuery().setOwnerUserid(userEntity.getId()).setIsVipCreate(1));
                if (Objects.nonNull(club)) {
                    if (!levelClubFlag && kolFlag) {
                        throw new BaseException(I18nMsgUtils.getMessage("club.op.fail", req.getLanguageCode()));
                    }
                    Club normalClub = clubService.findByQuery(new ClubSearchQuery()
                            .setOwnerUserid(userEntity.getId())
                            .setIsVipCreate(0));
                    if (Objects.nonNull(normalClub)) {
                        throw new BaseException(I18nMsgUtils.getMessage("club.op.fail", req.getLanguageCode()));
                    }
                }
            }
        }

        //检查名称是否重复
        boolean flag = clubService.checkNameCanUse(null, req.getName());
        if (flag) {
            Club club = new Club();
            club.setName(req.getName());
            club.setLowerName(req.getName().toLowerCase());
            club.setDescription(req.getDescription());
            club.setLogo(req.getLogo());
            club.setOwnerUserId(req.getOwnerId());
            //默认有负责人一个人
            club.setMemberCount(1);
            club.setMatchCount(0);
            club.setType(req.getType());
            club.setClubLevel(StringUtils.hasText(req.getClubLevel()) ? req.getClubLevel() : clubLevelConfigService.loadDefaultLevel());
            club.setState(ClubStateEnum.NORMAL.getCode());
            club.setInviteCode(createInviteCode());
            club.setRequiredInviteCode(false);
            club.setRequiresApproval(false);
            club.setGmtModified(ZonedDateTime.now());
            if (Objects.equals(userEntity.getMemberType(), YesNoStatus.YES.getCode())) {
                club.setIsVipCreate(YesNoStatus.YES.getCode());
            }
            clubService.insert(club);
            Club club1 = new Club();
            String env = (envProfile.equals("prod") || envProfile.equals("pre")) ? "prod" : envProfile;//由于预发和线上使用同数据库
            String clubGroupId = ApiConstants.clubGroupId + club.getId() + env;
            club1.setClubGroupId(clubGroupId);
            club1.setId(club.getId());
            club1.setDescription(req.getDescription());
            clubService.update(club1);
            if (sysConfigService.enableClubGroup(req.getOwnerId())) {
                GroupResultDto resultDto = tencentImUtil.createGroup(req.getOwnerId(), clubGroupId, req.getName());
                if (!Objects.equals(resultDto.getErrorCode(), 0)) {
                    log.error("创建俱乐部群聊失败,club:{},userId:{},result:{}", club.getId(), req.getOwnerId(), JsonUtil.writeString(resultDto));
                    throw new BizI18nException("club.op.fail");
                }
            }
            //新人俱乐部任务处理,保存任务配置
            if (Objects.equals(club.getType(), 1)) {
                List<NewUserClubTaskConfigDo> configDoList = newUserClubTaskConfigService.findList(new NewUserClubTaskConfigQuery().setState(0));
                if (!CollectionUtils.isEmpty(configDoList)) {
                    configDoList.forEach(config -> {
                        NewUserClubTaskDo newUserClubTaskDo = new NewUserClubTaskDo();
                        newUserClubTaskDo.setClubId(club.getId());
                        newUserClubTaskDo.setDays(config.getDays());
                        newUserClubTaskDo.setType(config.getType());
                        newUserClubTaskDo.setConfigId(config.getId());
                        newUserClubTaskDo.setDefaultLanguageCode(config.getDefaultLanguageCode());
                        newUserClubTaskService.create(newUserClubTaskDo);
                        List<NewUserClubTaskConfigI8nDo> list = newUserClubTaskConfigI8nService.findList(new NewUserClubTaskConfigI8nQuery().setConfigId(config.getId()));
                        List<NewUserClubTaskI8nDo> collect = list.stream().map(newUserClubTaskConfigI8nConverter::toTaskDo).collect(Collectors.toList());
                        collect.forEach(s -> s.setClubTaskId(newUserClubTaskDo.getId()));
                        newUserClubTaskI8nService.batchCreate(collect);
                    });
                }
            }
            //写入俱乐部成员
            clubMemberService.addMember(req.getOwnerId(), club.getId(), ClubMemberRoleEnum.OWNER, clubGroupId);
            userTaskBizService.completeEvent(new EventTriggerDto().setUser(userEntity).setEventSubType(TaskConstant.TakEventSubTypeEnum.CLUB_JOIN.getCode()));
            return club.getId();
        }
        throw ExceptionFactory.biz118nException("club.op.name_already");
    }

    public Boolean updateClub(ClubUpdateReqDto req, Integer appVersion) {
        Club byId = clubService.findById(req.getClubId());
        if (byId == null) {
            throw ExceptionFactory.biz118nException("club.op.fail");
        }
        if (clubService.checkNameCanUse(req.getClubId(), req.getName())) {
            if (appVersion >= 40600) {
                ClubReviewDo clubReviewDo = new ClubReviewDo();
                boolean nameChanged = !Objects.equals(byId.getName(), req.getName());
                boolean descriptionChanged = !Objects.equals(byId.getDescription(), req.getDescription());
                boolean logoChanged = !Objects.equals(byId.getLogo(), req.getLogo());
                if (nameChanged && !descriptionChanged && !logoChanged) {
                    clubReviewDo.setName(req.getName());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_NAME_ONLY.getCode());
                } else if (nameChanged && descriptionChanged && !logoChanged) {
                    clubReviewDo.setName(req.getName());
                    clubReviewDo.setDescription(req.getDescription());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_NAME_AND_DESCRIPTION.getCode());
                } else if (!nameChanged && descriptionChanged && !logoChanged) {
                    clubReviewDo.setDescription(req.getDescription());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_DESCRIPTION_ONLY.getCode());
                } else if (!nameChanged && !descriptionChanged && logoChanged) {
                    clubReviewDo.setLogo(req.getLogo());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_AVATAR_ONLY.getCode());
                } else if (nameChanged && !descriptionChanged && logoChanged) {
                    clubReviewDo.setName(req.getName());
                    clubReviewDo.setLogo(req.getLogo());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_NAME_AND_AVATAR.getCode());
                } else if (!nameChanged && descriptionChanged && logoChanged) {
                    clubReviewDo.setDescription(req.getDescription());
                    clubReviewDo.setLogo(req.getLogo());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_DESCRIPTION_AND_AVATAR.getCode());
                } else if (nameChanged && descriptionChanged && logoChanged) {
                    clubReviewDo.setName(req.getName());
                    clubReviewDo.setDescription(req.getDescription());
                    clubReviewDo.setLogo(req.getLogo());
                    clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_NAME_DESCRIPTION_AND_AVATAR.getCode());
                } else {
                    return true;
                }
                clubReviewDo.setClubId(byId.getId());
                clubReviewDo.setApplyUserId(req.getOwnerId());
                clubReviewDo.setApplyTime(ZonedDateTime.now());
                clubReviewService.create(clubReviewDo);
            } else {
                byId.setName(req.getName());
                byId.setDescription(req.getDescription());
                byId.setLogo(req.getLogo());
                clubService.update(byId);
            }
            return true;
        } else {
            throw ExceptionFactory.biz118nException("club.op.name_already");
        }
    }

    private String createInviteCode() {
        String inviteCode = "";
        do {
            inviteCode = NanoId.randomNanoId(7);
        } while (clubService.checkInviteCodeUsed(inviteCode));
        return inviteCode;
    }


    /**
     * 解散俱乐部
     *
     * @param req
     * @return
     */
    public Boolean disbandClub(ClubDisbandReqDto req) {
        Club byId = clubService.findById(req.getClubId());
        //判断是否有进行中的活动&&用户赛房间
        List<ClubActivityTeam> clubActivityTeams = clubActivityTeamService.findListByQuery(ClubActivityTeamQuery.builder().clubId(req.getClubId()).build());
        if (CollectionUtils.isNotEmpty(clubActivityTeams)) {
            List<Long> activityIds = clubActivityTeams.stream().map(ClubActivityTeam::getMainActivityId).collect(Collectors.toList());
            List<MainActivity> activities = mainActivityService.findList(MainActivityQuery.builder().activityIds(activityIds).activityStateList(List.of(MainActivityStateEnum.STARTED.getCode())).build());
            if (CollectionUtils.isNotEmpty(activities)) throw ExceptionFactory.biz118nException("club.disable.disband");
        }
        List<ClubActivityRoomRelationDo> roomList = clubActivityRoomRelationService.findList(ClubActivityRoomRelationQuery.builder().clubId(req.getClubId()).build());
        if (CollectionUtils.isNotEmpty(roomList)) {
            List<Long> roomIdList = roomList.stream().map(ClubActivityRoomRelationDo::getRoomId).collect(Collectors.toList());
            List<RoomDo> roomDos = roomService.findList(RoomQuery.builder().roomStatusLists(List.of(RoomStatusEnum.NOT_STARTED.getCode(), RoomStatusEnum.IN_PROGRESS.getCode())).roomIds(roomIdList).build());
            if (!CollectionUtils.isEmpty(roomDos)) throw ExceptionFactory.biz118nException("club.disable.disband");
        }
        if (byId == null || ClubStateEnum.DISBANDED.getCode().equals(byId.getState()) || !byId.getOwnerUserId().equals(req.getOwnerId())) {
            throw ExceptionFactory.biz118nException("club.op.fail");
        }
        Boolean b = clubService.disbandClub(req);
        //发送通知，进行数据删除
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.ClubDisbandEvent.getEventType(),new ClubDisbandEvent(this, req.getClubId()));
        return b;
    }

    /**
     * 后台解散俱乐部
     *
     * @param req
     * @return
     */
    public Boolean disbandClubAdmin(ClubDisbandReqDto req) {
        Boolean b = clubService.disbandClub(req);
        //发送通知，进行数据删除
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.ClubDisbandEvent.getEventType(),new ClubDisbandEvent(this, req.getClubId()));
        return b;
    }

    /**
     * 俱乐部推荐状态修改
     *
     * @param reqDto
     * @return
     */
    public void clubRecommendChange(ClubIdReqDto reqDto) {
        Club byId = clubService.findById(reqDto.getClubId());
        if (byId == null) {
            throw new BaseException("俱乐部不存在");
        }
        byId.setIsRecommend(reqDto.getIsRecommend());
        clubService.update(byId);
    }

    /**
     * 加载俱乐部信息
     *
     * @param clubId
     * @return
     */
    @FillerMethod
    public AppClubInfoRespDto loadClubInfo(Long clubId, Long userId) {
        Club byId = clubService.findById(clubId);
        if (byId != null) {
            AppClubInfoRespDto clubbedToAppClubInfoResp = ClubConvert.INSTANCE.clubToAppClubInfoResp(byId);
            clubbedToAppClubInfoResp.setClubGroupId(byId.getClubGroupId());
            clubbedToAppClubInfoResp.setJoinState(getJoinState(Lists.newArrayList(clubId), userId).get(clubId).getCode());
            clubbedToAppClubInfoResp.setClubOwner(byId.getOwnerUserId().equals(userId));
            List<ClubMemberApply> clubMemberApplies = clubMemberApplyService.findListByQuery(ClubMemberApplyQuery.builder().clubId(clubId).state(ClubMemberStateEnum.APPLYING.getCode()).build());
            clubbedToAppClubInfoResp.setPendingReviewNum(!CollectionUtils.isEmpty(clubMemberApplies) ? clubMemberApplies.size() : 0);
            //最近加入俱乐部的用户信息
            List<ClubMember> clubMembers = clubMemberService.findListByQuery(ClubMemberQuery.builder().clubId(clubId).build());
            List<ClubUserBaseInfoDto> userBaseInfoDtos = clubMembers.stream().limit(5).map(s -> {
                ZnsUserEntity user = userService.findById(s.getUserId());
                if (Objects.nonNull(user)) {
                    ClubUserBaseInfoDto clubUserBaseInfoDto = new ClubUserBaseInfoDto();
                    clubUserBaseInfoDto.setUserName(user.getFirstName());
                    clubUserBaseInfoDto.setUserId(user.getId());
                    clubUserBaseInfoDto.setHeadPortrait(user.getHeadPortrait());
                    return clubUserBaseInfoDto;
                }
                return null;
            }).collect(Collectors.toList());
            clubbedToAppClubInfoResp.setHeadPortraitList(userBaseInfoDtos);
            //活动通知开关
            clubMemberService.findByClubAndUserId(clubId, userId).ifPresent(i -> {
                clubbedToAppClubInfoResp.setActivityNoticeSwitch(i.getActivityNoticeSwitch());
                clubbedToAppClubInfoResp.setClubManager(Objects.equals(i.getRole(), ClubMemberRoleEnum.MANAGER.getCode()));
            });
            clubbedToAppClubInfoResp.setClubEventNotJoinInfoDto(clubActivityManager.getClubEventNotJoinInfoDto(clubId));
            clubbedToAppClubInfoResp.setClubEventTotalDto(clubActivityManager.getClubEventTotal(clubId));
            ClubLevelBenefitDo benefitDo = clubLevelBenefitService.findByQuery(new ClubLevelBenefitQuery().setType(ClubLevelBenefitEnum.MANAGER_NUM.getCode()).setLevelValue(Integer.parseInt(byId.getClubLevel().replaceAll("\\D+", ""))));
            clubbedToAppClubInfoResp.setManagerMaxNum(Objects.nonNull(benefitDo) ? benefitDo.getValue() : null);
            clubbedToAppClubInfoResp.setGmtCreate(DateUtil.toZonedDateTime(byId.getGmtCreate()));
            clubbedToAppClubInfoResp.setCurrentManagerNum(byId.getManagerNum());
            ZnsUserEntity owner = znsUserService.findById(byId.getOwnerUserId());
            clubbedToAppClubInfoResp.setOwnerUserName(Objects.nonNull(owner) ? owner.getFirstName() : "");
            clubbedToAppClubInfoResp.setBackgroundChangeNum(redissonClient.getBucket(RedisConstants.CLUB_BACKGROUND_CHANGE + clubId).isExists() ? 0 : 1);
            ClubManagerApplyDo applyDo = clubManagerApplyService.findByQuery(new ClubManagerApplyQuery().setClubId(clubId).setUserId(userId).setState(ClubManagerInviteEnum.PENDING.getCode()));
            clubbedToAppClubInfoResp.setIsShowManagerInvite(Objects.nonNull(applyDo));
            if (Objects.equals(byId.getType(), 1)) {
                ZonedDateTime now = ZonedDateTime.now();
                ZonedDateTime gmtCreateZoned = byId.getGmtCreate().toInstant().atZone(now.getZone());
                Duration duration = Duration.between(gmtCreateZoned, now);
                long secondsDifference = duration.getSeconds();
                //计算天数
                int groupNumber = (Objects.equals(secondsDifference, 0L)) ? 1 : (int) Math.ceil((double) secondsDifference / 86400);
                clubbedToAppClubInfoResp.setNewUserClubDays(groupNumber);
            }
            return clubbedToAppClubInfoResp;
        }
        throw ExceptionFactory.biz118nException("club.op.fail");
    }

    /**
     * 查询用户的俱乐部加入状态
     *
     * @param
     * @return
     */
    public Map<Long, ClubMemberStateEnum> getJoinState(List<Long> clubIdsList, Long userId) {
        List<Long> clubIds = new ArrayList<>(clubIdsList);
        Map<Long, ClubMemberStateEnum> result = new HashMap<>(clubIds.size());

        //1. 查询成员表
        List<ClubMember> byBatchClubIdsAndUserId = clubMemberService.findByBatchClubIdsAndUserId(clubIds, userId);
        Iterator<Long> iterator = clubIds.iterator();
        List<Long> joinedClubIds = new ArrayList<>();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            if (byBatchClubIdsAndUserId.stream().anyMatch(i -> i.getClubId().equals(next))) {
                //找到了
                result.put(next, ClubMemberStateEnum.JOINED);
                joinedClubIds.add(next);
            }
        }
        clubIds.removeAll(joinedClubIds);
        //2. 查询审批表
        List<ClubMemberApply> clubApply = clubMemberApplyService.findWaitAuditByBatchClubIdsAndUserId(clubIds, userId);
        iterator = clubIds.iterator();
        List<Long> applyingClubIds = new ArrayList<>();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            if (clubApply.stream().anyMatch(i -> i.getClubId().equals(next))) {
                //找到了
                result.put(next, ClubMemberStateEnum.APPLYING);
                applyingClubIds.add(next);
            }
        }
        //剩下的都是未加入
        clubIds.removeAll(applyingClubIds);
        result.putAll(clubIds.stream().collect(Collectors.toMap(Function.identity(), i -> ClubMemberStateEnum.WAITING)));
        return result;
    }

    /**
     * 用户已经加入的俱乐部列表
     *
     * @param page
     * @return
     */
    @FillerMethod
    public Page<UserClubJoinListRespDto> joinedClubList(PageQuery page, Long userId) {
        ClubSearchQuery query = new ClubSearchQuery();
        query.setPageNum(page.getPageNum());
        query.setPageSize(page.getPageSize());
        query.setUserId(userId);
        Page<Club> pageDataByQuery = clubService.findPageDataByQuery(query);
        if (CollectionUtils.isEmpty(pageDataByQuery.getRecords())) {
            return new Page<>();
        }

        return PageConvert.dataConvert(pageDataByQuery, club -> {
            UserClubJoinListRespDto dto = ClubConvert.INSTANCE.clubToAppJoinClubRespDto(club);
            dto.setClubGroupId(club.getClubGroupId());
            dto.setUserJoinState(ClubMemberStateEnum.JOINED.getCode());
            dto.setOwner(dto.getOwnerUserId().equals(userId));
            if (dto.getOwnerUserId().equals(userId)) {
                List<ClubMemberApply> clubMemberApplies = clubMemberApplyService.findListByQuery(ClubMemberApplyQuery.builder().clubId(club.getId()).state(ClubMemberStateEnum.APPLYING.getCode()).build());
                dto.setPendingReviewNum(clubMemberApplies.size());
            }
            return dto;
        });
//
//        Page<ClubMember> clubMemberPage = clubMemberService.findClubIdPageByUserId(Page.of(page.getPageNum(), page.getPageSize()), userId);
//        if (CollectionUtils.isEmpty(clubMemberPage.getRecords())) {
//            return new Page<>();
//        }
//
//        List<Club> byIds = clubService.findByIds(clubMemberPage.getRecords().stream().map(ClubMember::getClubId).toList());
//        Map<Long, Club> collect = byIds.stream().collect(Collectors.toMap(Club::getId, Function.identity()));
//        return PageConvert.dataConvert(clubMemberPage, clubMember -> {
//            Club userClub = collect.get(clubMember.getClubId());
//            UserClubJoinListRespDto dto = ClubConvert.INSTANCE.clubToAppJoinClubRespDto(userClub);
//            dto.setClubGroupId(userClub.getClubGroupId());
//            dto.setUserJoinState(ClubMemberStateEnum.JOINED.getCode());
//            dto.setOwner(dto.getOwnerUserId().equals(userId));
//            if (dto.getOwnerUserId().equals(userId)) {
//                List<ClubMemberApply> clubMemberApplies = clubMemberApplyService.findListByQuery(ClubMemberApplyQuery.builder().clubId(userClub.getId()).state(ClubMemberStateEnum.APPLYING.getCode()).build());
//                dto.setPendingReviewNum(clubMemberApplies.size());
//            }
//            return dto;
//        });
    }


    public AppClubMemberInviteStateRespDto checkInviteState(AppClubMemberInviteStateReqDto req, Long userId) {
        AppClubMemberInviteStateRespDto resp = new AppClubMemberInviteStateRespDto();
        resp.setExpired(DateUtil.addDays(req.getSendDate(), ClubMemberManager.IM_INVITE_EXPIRY_TIME_DAYS).compareTo(ZonedDateTime.now()) < 0);
        //检查邀请状态
        Map<Long, ClubMemberStateEnum> joinState = getJoinState(Lists.newArrayList(req.getClubId()), req.getInviteeUserId());
        resp.setMemberState(joinState.get(req.getClubId()).getCode());
        if (resp.isExpired()) {
            //过期了
            resp.setHandleScenes(ClubImHandleScenesEnum.TOAST);
            resp.setToastMsg(I18nMsgUtils.getMessage("club.invite.link_timeout"));
        } else {
            if (userId.equals(req.getInviteeUserId())) {
                if (ClubMemberStateEnum.APPLYING.getCode().equals(resp.getMemberState())) {
                    //存在待审批数据，toast提示
                    resp.setHandleScenes(ClubImHandleScenesEnum.TOAST);
                    resp.setToastMsg(I18nMsgUtils.getMessage("club.apply.submit.member_exist"));
                } else if (ClubMemberStateEnum.WAITING.getCode().equals(resp.getMemberState())) {
                    //待加入
                    resp.setHandleScenes(ClubImHandleScenesEnum.DIALOG);
                    List<String> joinReason = new ArrayList<>();
                    joinReason.add(I18nMsgUtils.getMessage("club.apply.reason_1"));
                    joinReason.add(I18nMsgUtils.getMessage("club.apply.reason_2"));
                    joinReason.add(I18nMsgUtils.getMessage("club.apply.reason_3"));
                    resp.setJoinReason(joinReason);
                    resp.setInviteCode(req.getInviteCode());
                } else {
                    resp.setHandleScenes(ClubImHandleScenesEnum.JUMP);
                    resp.setJumpUrl("lznative://lzrace/ClubHome");
                    Map<String, Long> jsonObject = Map.of("clubId", req.getClubId());
                    resp.setJumpParam(JsonUtil.writeString(jsonObject));
                }
            } else {
                //邀请人
                resp.setHandleScenes(ClubImHandleScenesEnum.JUMP);
                resp.setJumpUrl("lznative://lzrace/ClubHome");
                Map<String, Long> jsonObject = Map.of("clubId", req.getClubId());
                resp.setJumpParam(JsonUtil.writeString(jsonObject));
            }
        }
        return resp;
    }

    public void newUserJoin(Long userId) {
        String lockKey = ApiConstants.NEW_USER_JOIN_CLUB + userId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!LockHolder.tryLock(lock, 60, 60)) {
                log.info("新用户加入俱乐部 没有获得锁，锁失败 " + lockKey);
                throw new BaseException(SYSTEM_ERROR.getMsg());
            }
            Club club = findAvailableClubForNewUser(userId);
            Optional<ClubMember> byClubAndUserId = clubMemberService.findByClubAndUserId(club.getId(), userId);
            if (byClubAndUserId.isEmpty()) {
                clubMemberManager.addMember(club.getId(), userId);
                clubImService.sendImNewUserJoinClub(club, userId);
                log.info("新人加入新人俱乐部,userId:{},clubId:{}", userId, club.getId());
            }
        } catch (Exception e) {
            log.error("新用户加入俱乐部", e);
            throw new BaseException(BUSINESS_ERROR.getMsg());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("新用户加入俱乐部 删除锁 " + lockKey);
                lock.unlock();
            }
        }
    }

    private Club findAvailableClubForNewUser(Long userId) {
        // 查询现有俱乐部
        Club existingClub = clubService.findByNewUser(userId);
        if (existingClub != null) {
            ClubLevelConfig byClubCode = clubLevelConfigService.findByClubCode(existingClub.getClubLevel())
                    .orElseThrow(() -> new BaseException(BUSINESS_ERROR.getMsg()));
            if (existingClub.getMemberCount().compareTo(byClubCode.getMemberUpperLimit()) < 0) {
                return existingClub;
            }
        }
        // 创建新俱乐部
        log.info("当前新人俱乐部已满或者不存在，创建新俱乐部");

        ClubCreateReqDto clubCreateReqDto = new ClubCreateReqDto();
        clubCreateReqDto.setOwnerId(Long.valueOf(iSysConfigService.selectConfigByKey(ConfigKeyEnums.NEW_USER_CLUB_OWNER.getCode())));
        RAtomicLong atomicLong = redissonClient.getAtomicLong(COUNTER_KEY);
        clubCreateReqDto.setName("Newcomer Club" + atomicLong.get());
        clubCreateReqDto.setClubLevel("LV4");
        clubCreateReqDto.setLogo("https://pitpat-oss.s3.us-east-2.amazonaws.com/1733217229168.png");
        clubCreateReqDto.setDescription("Welcome to the Newcomer Club! Over the next 7 days, we'll guide you through all the exciting features and fun of PitPat. If you ever have questions, don't hesitate to reach out. After your week with us, you'll be ready to jump into the full PitPat experience and enjoy everything it has to offer!");
        clubCreateReqDto.setRoleCheck(Boolean.FALSE);
        clubCreateReqDto.setType(1);
        Long clubId = createClub(clubCreateReqDto);
        atomicLong.incrementAndGet();
        if (clubId == null) {
            throw new BaseException("创建俱乐部失败，请稍后重试");
        }
        Club club = clubService.findById(clubId);
        //默认人加入到俱乐部
        SysConfig sysConfig = iSysConfigService.selectSysConfigByKey(ConfigKeyEnums.NEW_USER_CLUB_MEMBER.getCode());
        if (Objects.nonNull(sysConfig) && StringUtils.hasText(sysConfig.getConfigValue())) {
            String configValue = sysConfig.getConfigValue();
            List<Long> userIds = NumberUtils.stringToLong(configValue.split(","));
            for (Long id : userIds) {
                clubMemberManager.addMember(clubId, id);
            }
            log.info("默认人员加入俱乐部成功");
        }
        return club;
    }

    public boolean userClubSwitch(clubMemberSwitchReqDto clubMemberSwitchReqDto, ZnsUserEntity user) {
        Optional<ClubMember> byClubAndUserId = clubMemberService.findByClubAndUserId(clubMemberSwitchReqDto.getClubId(), user.getId());
        if (byClubAndUserId.isEmpty()) {
            return false;
        }
        ClubMember clubMember = byClubAndUserId.get();
        clubMember.setChatSwitch(clubMemberSwitchReqDto.getChatSwitch());
        clubMember.setActivityNoticeSwitch(clubMemberSwitchReqDto.getActivityNoticeSwitch());
        return clubMemberService.update(clubMember) > 0;
    }

    public int getReviewMessageCount(Long userId) {
        ClubMemberApplyQuery build = ClubMemberApplyQuery.builder().auditUserId(userId).state(ClubMemberApplyStateEnum.APPLYING.getCode()).isDelete(0).build();
        List<ClubMemberApply> list = clubMemberApplyService.findListByQuery(build);
        return CollectionUtils.isEmpty(list) ? 0 : list.size();
    }

    public List<String> getClubGroupList(Long userId) {
        List<ClubMember> clubMembers = clubMemberService.findListByQuery(ClubMemberQuery.builder().userId(userId).build());
        if (CollectionUtils.isEmpty(clubMembers)) {
            return new ArrayList<>();
        }
        List<Long> clubList = clubMembers.stream().map(ClubMember::getClubId).collect(Collectors.toList());
        List<Club> byIds = clubService.findByIds(clubList);
        return byIds.stream().map(Club::getClubGroupId).collect(Collectors.toList());
    }

    public String getClubIntroduce(String clubName) {
        if (!StringUtils.hasText(clubName)) {
            return sysConfigService.selectConfigByKey(ConfigKeyEnums.CLUB_DEFAULT_INTRODUCE.getCode());
        }
        String content = generateWithAI(buildRequestBody(clubName));
        if (!StringUtils.hasText(content)) {
            content = sysConfigService.selectConfigByKey(ConfigKeyEnums.CLUB_DEFAULT_INTRODUCE.getCode());
        }
        return content;
    }

    @Nullable
    private String generateWithAI(OpenAIRequestDto openAIRequestDto) {
        // demo 数据格式
        //String json = """
        //         {
        //              "messages": [
        //                {
        //                  "role": "system",
        //                  "content": "You are a creative writer skilled in crafting engaging and unique introductions for sports clubs."
        //                },
        //                {
        //                  "role": "user",
        //                  "content": "Given the name of a sports club, create a compelling, dynamic, and uplifting introduction. Capture the club's energy, passion, and dedication to fostering growth through sports. Highlight its welcoming spirit, celebrating members from all backgrounds and skill levels, emphasizing connection, self-improvement, and friendly competition. Ensure the tone is inspiring, unique, and encourages participation. Keep the response under 300 characters."
        //                }
        //              ],
        //              "model": "gpt-4",
        //              "max_tokens": 75,
        //              "temperature": 1
        //            }
        //
        //        """;
        try {
            String response = RestTemplateUtil.post(openAiProperties.getBaseUrl(), openAIRequestDto, Map.of(
                    "Authorization", "Bearer " + openAiProperties.getApiKey(),
                    "Accept", "application/json"
            ));

            if (StringUtils.hasText(response)) {
                Map<String, Object> objectObjectMap = JsonUtil.readValue(response);
                log.info("getClubIntroduce response:{}", JsonUtil.writePrettyString(objectObjectMap));

                List<Map<String, Object>> choices = JsonUtil.readValue(objectObjectMap.get("choices"), new TypeReference<>() {
                });

                Map<String, Object> message = JsonUtil.readValue(choices.get(0).get("message"));
                String content = MapUtil.getString(message.get("content"));
                if (StringUtils.hasText(content)) {
                    return content;
                }
            }
        } catch (Exception e) {
            log.error("请求 Open AI 报错， msg:{}", e.getMessage(), e);
        }
        return null;
    }

    @NotNull
    private OpenAIRequestDto buildRequestBody(String clubName) {
        OpenAIRequestDto.Message systemMessage = new OpenAIRequestDto.Message(openAiProperties.getOptions().getRoleSystem(), "system");
        OpenAIRequestDto.Message userMessage = new OpenAIRequestDto.Message(String.format(openAiProperties.getOptions().getRoleUser(), clubName), "user");

        return new OpenAIRequestDto(
                List.of(systemMessage, userMessage),  // messages
                openAiProperties.getOptions().getModel(),                        // model
                openAiProperties.getOptions().getTemperature(),                                     // temperature
                70                                   // maxTokens
        );
    }

    @FillerMethod
    public ClubDetailResponseDto getClubDetail(Long clubId) {
        Club club = clubService.findById(clubId);
        if (Objects.isNull(club)) {
            throw new BizI18nException("club.not.found");
        }

        ClubDetailResponseDto respDto = ClubConvert.INSTANCE.toClubDetailResponseDto(club);
        respDto.setClubId(club.getId());
        return respDto;
    }

    public ClubLevelInfoRespDto getClubLevelInfo(Long clubId, ZnsUserEntity user) {
        Club club = clubService.findById(clubId);
        if (Objects.isNull(club) || !StringUtils.hasText(club.getClubLevel())) return null;
        ClubLevelInfoRespDto clubLevelInfoRespDto = new ClubLevelInfoRespDto();
        clubLevelInfoRespDto.setClubLevelName(club.getClubLevel());
        clubLevelInfoRespDto.setLogo(club.getLogo());
        clubLevelInfoRespDto.setName(club.getName());
        clubLevelInfoRespDto.setCreateTime(DateUtil.toZonedDateTime(club.getGmtCreate()));
        ZnsUserEntity owner = userService.findById(club.getOwnerUserId());
        clubLevelInfoRespDto.setOwnerName(Objects.nonNull(owner) ? owner.getFirstName() : null);
        int clubLevel = Integer.parseInt(club.getClubLevel().replaceAll("\\D+", ""));

        clubLevelInfoRespDto.setClubLevelValue(clubLevel);
        List<ClubLevelBenefitDo> benefitDoList = clubLevelBenefitService.findList(new ClubLevelBenefitQuery().setLevelValue(clubLevel));

        List<ClubLevelBenefitDto> benefitDtos = benefitDoList.stream().map(s -> {
            ClubLevelBenefitDto clubLevelBenefitDto = new ClubLevelBenefitDto();
            clubLevelBenefitDto.setIcon(s.getIcon());
            switch (ClubLevelBenefitEnum.findByType(s.getType())) {
                case TITLE -> clubLevelBenefitDto.setName(I18nMsgUtils.getMessage("club.rights.title"));
                case MANAGER_NUM -> clubLevelBenefitDto.setName(I18nMsgUtils.getMessage("club.rights.manager.num", s.getValue()));
            }
            return clubLevelBenefitDto;
        }).collect(Collectors.toList());
        //俱乐部权益
        clubLevelInfoRespDto.setBenefitDtos(benefitDtos);

        //超过满级不返回升级条件了
        if (clubLevel >= clubLevelConfigService.findHighestLevel()) {
            return clubLevelInfoRespDto;
        }
        List<ClubLevelConditionDo> conditionDoList = clubLevelConditionService.findListByLevel(clubLevel + 1);

        ClubRunDataDo clubRunDataDo = clubRunDataService.findByQuery(ClubRunDataQuery.builder().clubId(clubId).build());
        Long memberCount = clubMemberService.findMemberCount(clubId);
        List<ClubLevelCondition> conditions = conditionDoList.stream().map(conditionDo -> {
            ClubLevelCondition clubLevelCondition = new ClubLevelCondition();
            switch (ClubLevelConditionEnum.findByType(conditionDo.getType())) {
                case MEMBER_COUNT -> {
                    clubLevelCondition.setConditionName(I18nMsgUtils.getMessage("club.condition.member.count", conditionDo.getValue()));
                    clubLevelCondition.setConditionValue(conditionDo.getValue());
                    if (conditionDo.getValue() > memberCount) {
                        clubLevelCondition.setIsComplete(Boolean.FALSE);
                        clubLevelCondition.setCompleteNeed(I18nMsgUtils.getMessage("club.condition.count.need", (conditionDo.getValue() - memberCount)));
                    } else {
                        clubLevelCondition.setIsComplete(Boolean.TRUE);
                    }
                    clubLevelCondition.setCurrentValue(new BigDecimal(memberCount));
                }
                case RUN_MILEAGE -> {
                    clubLevelCondition.setConditionName(I18nMsgUtils.getMessage("club.condition.run.mileage", conditionDo.getValue()));
                    clubLevelCondition.setConditionValue(conditionDo.getValue());
                    BigDecimal runMileage = Objects.nonNull(clubRunDataDo) ? clubRunDataDo.getRunMileage() : BigDecimal.ZERO;
                    if (runMileage.compareTo(new BigDecimal(conditionDo.getValue()).multiply(new BigDecimal(1600))) >= 0) {
                        clubLevelCondition.setIsComplete(Boolean.TRUE);
                    } else {
                        clubLevelCondition.setIsComplete(Boolean.FALSE);
                        clubLevelCondition.setCompleteNeed(I18nMsgUtils.getMessage("club.condition.mileage.need", conditionDo.getValue() - runMileage.divide(new BigDecimal(1600)).intValue()));
                    }
                    clubLevelCondition.setCurrentValue(runMileage.divide(new BigDecimal(1600), 2, RoundingMode.HALF_DOWN));
                }
                case RUN_COUNT -> {
                    clubLevelCondition.setConditionName(I18nMsgUtils.getMessage("club.condition.run.count", conditionDo.getValue()));
                    clubLevelCondition.setConditionValue(conditionDo.getValue());
                    int runCount = Objects.nonNull(clubRunDataDo) ? clubRunDataDo.getRunCount() : 0;
                    if (runCount >= conditionDo.getValue()) {
                        clubLevelCondition.setIsComplete(Boolean.TRUE);
                    } else {
                        clubLevelCondition.setIsComplete(Boolean.FALSE);
                        clubLevelCondition.setCompleteNeed(I18nMsgUtils.getMessage("club.condition.count.need", (conditionDo.getValue() - runCount)));
                    }
                    clubLevelCondition.setCurrentValue(new BigDecimal(runCount));
                }
            }
            return clubLevelCondition;
        }).collect(Collectors.toList());
        clubLevelInfoRespDto.setConditions(conditions);

        //创建者
        if (Objects.equals(club.getOwnerUserId(), user.getId())) {
            Club creatClub = clubService.findByQuery(new ClubSearchQuery().setOwnerUserid(user.getId()).setIsVipCreate(1));
            if (Objects.equals(user.getMemberType(), 1)) {
                clubLevelInfoRespDto.setVipCreateClubType(Objects.isNull(creatClub) ? 2 : 3);
            } else {
                clubLevelInfoRespDto.setVipCreateClubType(1);
            }
        }
        return clubLevelInfoRespDto;
    }

    public List<ClubLevelConfigRespDto> getClubLevelConfig() {

        List<ClubLevelConfigRespDto> respDtos = new ArrayList<>();
        for (ClubLevelConfig clubLevelConfig : clubLevelConfigService.loadAll()) {
            ClubLevelConfigRespDto clubLevelConfigRespDto = new ClubLevelConfigRespDto();
            clubLevelConfigRespDto.setClubLevelName(clubLevelConfig.getLevelCode());
            List<ClubLevelConditionDo> conditionDoList = clubLevelConditionService.findListByLevel(Integer.valueOf(clubLevelConfig.getLevelName()));
            List<ClubLevelCondition> conditions = conditionDoList.stream().map(conditionDo -> {
                ClubLevelCondition clubLevelCondition = new ClubLevelCondition();
                switch (ClubLevelConditionEnum.findByType(conditionDo.getType())) {
                    case MEMBER_COUNT -> clubLevelCondition.setConditionName(I18nMsgUtils.getMessage("club.condition.member.count", conditionDo.getValue()));
                    case RUN_MILEAGE -> clubLevelCondition.setConditionName(I18nMsgUtils.getMessage("club.condition.run.mileage", conditionDo.getValue()));
                    case RUN_COUNT -> clubLevelCondition.setConditionName(I18nMsgUtils.getMessage("club.condition.run.count", conditionDo.getValue()));
                }
                return clubLevelCondition;
            }).collect(Collectors.toList());
            clubLevelConfigRespDto.setConditions(conditions);
            List<ClubLevelBenefitDo> benefitDoList = clubLevelBenefitService.findList(new ClubLevelBenefitQuery().setLevelValue(Integer.valueOf(clubLevelConfig.getLevelName())));
            List<ClubLevelBenefitDto> benefitDtos = benefitDoList.stream().map(s -> {
                ClubLevelBenefitDto clubLevelBenefitDto = new ClubLevelBenefitDto();
                clubLevelBenefitDto.setIcon(s.getIcon());
                switch (ClubLevelBenefitEnum.findByType(s.getType())) {
                    case TITLE -> clubLevelBenefitDto.setName(I18nMsgUtils.getMessage("club.rights.title"));
                    case MANAGER_NUM -> clubLevelBenefitDto.setName(I18nMsgUtils.getMessage("club.rights.manager.num", s.getValue()));
                }
                return clubLevelBenefitDto;
            }).collect(Collectors.toList());
            clubLevelConfigRespDto.setBenefitDtos(benefitDtos);
            respDtos.add(clubLevelConfigRespDto);
        }
        return respDtos;
    }

    /**
     * 检查俱乐部是否达到升级条件
     *
     * @param clubId
     */
    public void checkClubLevelUpgrade(Long clubId, ClubRunDataDo clubRunDataDo) {
        Club club = clubService.findById(clubId);
        if (Objects.isNull(clubRunDataDo)) {
            clubRunDataDo = clubRunDataService.findByQuery(ClubRunDataQuery.builder().clubId(clubId).build());
        }
        if (Objects.isNull(club) || !StringUtils.hasText(club.getClubLevel())) return;
        if (Objects.isNull(clubRunDataDo)) return;
        int level = Integer.parseInt(club.getClubLevel().replaceAll("\\D+", ""));
        clubLevelConfigService.findByClubCode(club.getClubLevel()).orElseThrow(() -> new BaseException("俱乐部等级设置错误"));

        if (level >= clubLevelConfigService.findHighestLevel()) {
            return;
        }
        List<ClubLevelConditionDo> conditionDoList = clubLevelConditionService.findListByLevel(level + 1);
        int finishLevel = 0;
        Long memberCount = clubMemberService.findMemberCount(clubId);

        for (ClubLevelConditionDo clubLevelConditionDo : conditionDoList) {

            switch (ClubLevelConditionEnum.findByType(clubLevelConditionDo.getType())) {
                case MEMBER_COUNT -> {
                    if (memberCount >= clubLevelConditionDo.getValue()) {
                        finishLevel++;
                    }
                }
                case RUN_MILEAGE -> {
                    if (Objects.nonNull(clubRunDataDo.getRunMileage()) && clubRunDataDo.getRunMileage().compareTo(new BigDecimal(clubLevelConditionDo.getValue() * 1600)) >= 0) {
                        finishLevel++;
                    }
                }
                case RUN_COUNT -> {
                    if (Objects.nonNull(clubRunDataDo.getRunCount()) && clubRunDataDo.getRunCount() >= clubLevelConditionDo.getValue()) {
                        finishLevel++;
                    }
                }
            }
        }
        if (finishLevel >= 2) {
            String nextLevel = "LV" + (Integer.parseInt(club.getClubLevel().replaceAll("\\D+", "")) + 1);
            club.setClubLevel(nextLevel);
            log.info("俱乐部等级升级，clubId:{},level:{}", clubId, nextLevel);
            clubService.update(club);
        }

    }

    public Boolean backgroundChange(ZnsUserEntity user, Long clubId, String backgroundUrl) {
        ClubReviewDo clubReviewDo = new ClubReviewDo();
        clubReviewDo.setClubId(clubId);
        clubReviewDo.setBackgroundUrl(backgroundUrl);
        clubReviewDo.setApplyUserId(user.getId());
        clubReviewDo.setApplyTime(ZonedDateTime.now());
        clubReviewDo.setModifyType(ClubModificationTypeEnum.CHANGE_BACKGROUND_IMG.getCode());
        clubReviewService.create(clubReviewDo);
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(user.getZoneId()));
        ZonedDateTime nextMonday = now.with(TemporalAdjusters.next(DayOfWeek.MONDAY))
                .withHour(0).withMinute(0).withSecond(0).withNano(0);
        long seconds = java.time.Duration.between(now, nextMonday).getSeconds();
        redissonClient.getBucket(RedisConstants.CLUB_BACKGROUND_CHANGE + clubId).set("1", seconds, TimeUnit.SECONDS);
        return true;
    }

    public List<ClubNewUserTaskRespDto> clubTaskList(ClubNewUserTaskDaysReqDto reqDto, ZnsUserEntity user) {
        Club club = clubService.findById(reqDto.getClubId());
        if (Objects.isNull(club)) {
            throw new BizI18nException("club.op.fail");
        }
        List<NewUserClubTaskRecordDo> list = newUserClubTaskRecordService.findList(new NewUserClubTaskRecordQuery().setClubId(club.getId()).setUserId(user.getId()).setDays(reqDto.getDays()));
        List<ClubNewUserTaskRespDto> respDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) return respDtoList;
        Map<Integer, List<NewUserClubTaskRecordDo>> daysMap = list.stream().collect(Collectors.groupingBy(NewUserClubTaskRecordDo::getDays));
        // 处理1-7天的数据
        for (int day = 1; day <= 7; day++) {
            ClubNewUserTaskRespDto clubNewUserTaskRespDto = new ClubNewUserTaskRespDto();
            clubNewUserTaskRespDto.setDays(day);

            if (daysMap.containsKey(day)) {
                // 有数据的天处理逻辑
                List<ClubNewUserTaskLevelRespDto> collect = daysMap.get(day).stream().map(record -> {
                    ClubNewUserTaskLevelRespDto clubNewUserTaskLevelRespDto = new ClubNewUserTaskLevelRespDto();
                    NewUserClubTaskDo clubTaskDo = newUserClubTaskService.findById(record.getClubTaskId());
                    List<NewUserClubTaskI8nDo> i8nDoList = newUserClubTaskI8nService.findList(new NewUserClubTaskI8nQuery().setClubTaskId(record.getClubTaskId()));
                    NewUserClubTaskI8nDo newUserClubTaskI8nDo = i8nDoList.stream().filter(s -> Objects.equals(s.getLanguageCode(), user.getLanguageCode())).findFirst().orElseGet(() -> i8nDoList.stream()
                            .filter(s -> Objects.equals(s.getLanguageCode(), clubTaskDo.getDefaultLanguageCode()))
                            .findFirst()
                            .orElse(new NewUserClubTaskI8nDo()));
                    clubNewUserTaskLevelRespDto.setUserTaskId(record.getId());
                    clubNewUserTaskLevelRespDto.setType(record.getType());
                    clubNewUserTaskLevelRespDto.setContent(newUserClubTaskI8nDo.getContent());
                    clubNewUserTaskLevelRespDto.setTitle(newUserClubTaskI8nDo.getTitle());
                    clubNewUserTaskLevelRespDto.setOptions(StringUtils.hasText(newUserClubTaskI8nDo.getOptions()) ? JsonUtil.readList(newUserClubTaskI8nDo.getOptions(), String.class) : null);
                    clubNewUserTaskLevelRespDto.setJumpImgUrl(newUserClubTaskI8nDo.getJumpImgUrl());
                    clubNewUserTaskLevelRespDto.setFeedBack(record.getOptions());
                    clubNewUserTaskLevelRespDto.setIsComplete(Objects.equals(record.getIsComplete(), 1));
                    PrimitiveForest forest = new PrimitiveForest();
                    if (Objects.equals(record.getType(), ClubTaskTypeEnum.JUMP.getCode())) {
                        forest.setJumpType(newUserClubTaskI8nDo.getJumpType());
                        forest.setRouteId(newUserClubTaskI8nDo.getRouteId());
                        forest.setMainActivityType(newUserClubTaskI8nDo.getMainActivityType());
                        forest.setRunActivityId(newUserClubTaskI8nDo.getActivityId());
                        forest.setCourseId(newUserClubTaskI8nDo.getCourseId());
                        forest.setJumpUrl(newUserClubTaskI8nDo.getUrl());
                        forest.setUserId(user.getId());
                        AppRoute route = appRouteService.findRoute(forest);
                        if (Objects.nonNull(route)) {
                            clubNewUserTaskLevelRespDto.setJumpValue(route.getJumpUrl());
                            clubNewUserTaskLevelRespDto.setJumpParam(route.getJumpParam());
                        }
                    }
                    return clubNewUserTaskLevelRespDto;
                }).collect(Collectors.toList());
                clubNewUserTaskRespDto.setTaskList(collect);
            } else {
                // 没有数据的天，设置空列表
                clubNewUserTaskRespDto.setTaskList(new ArrayList<>());
            }

            respDtoList.add(clubNewUserTaskRespDto);
        }

        return respDtoList;
    }

    public void taskFeedBack(ClubNewUserTaskFinishReqDto reqDto) {
        NewUserClubTaskRecordDo recordDo = newUserClubTaskRecordService.findById(reqDto.getUserTaskId());
        if (Objects.isNull(recordDo)) throw new BizI18nException("club.op.fail");
        switch (ClubTaskTypeEnum.findByType(recordDo.getType())) {
            case SINGLE_CHOICE, MULTIPLE_CHOICE -> recordDo.setOptions(reqDto.getFeedBack());
            case SCORE -> recordDo.setScore(Integer.parseInt(reqDto.getFeedBack()));
        }
        recordDo.setIsComplete(YesNoStatus.YES.getCode());
        recordDo.setSubmitTime(ZonedDateTime.now());
        newUserClubTaskRecordService.update(recordDo);
        //是否全部完赛 todo 新人任务后续处理
        Boolean finishTask = newUserClubTaskRecordService.isFinishTask(null, recordDo.getClubId(), recordDo.getUserId());
        if (finishTask) {
            userTaskBizService.completeEvent(new EventTriggerDto().setUserId(recordDo.getUserId()).setEventSubType(TaskConstant.TakEventSubTypeEnum.CLUE_NEW_USER_TASK.getCode()));
        }
    }

    /**
     * 俱乐部成员列表分页查询
     *
     * @param req 查询请求参数
     * @return 分页结果
     * @since 474
     */
    public Page<ClubMemberDetailResponseDto> getClubMemberPage(ClubMemberListReqDto req) {
        Page<ClubMemberDetailResponseDto> result = clubMemberService.findClubMemberPage(req.getClubId(), req.getPageSize(), req.getPageNum());
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public String importClubMembers(Long clubId, List<ClubMemberImportDto> memberList) {
        // 检查俱乐部是否存在
        Club club = clubService.findById(clubId);
        if (Objects.isNull(club)) {
            throw new BizI18nException("club.op.fail");
        }
        
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMsg = new StringBuilder();
        
        for (ClubMemberImportDto memberDto : memberList) {
            try {
                // 根据邮箱查找用户
                ZnsUserEntity user = userService.findByEmail(memberDto.getEmailAddress());
                if (Objects.isNull(user)) {
                    failCount++;
                    errorMsg.append("邮箱 ").append(memberDto.getEmailAddress()).append(" 对应的用户不存在；");
                    continue;
                }
                
                // 检查用户是否已经是俱乐部成员
                Optional<ClubMember> existingMember = clubMemberService.findByClubAndUserId(clubId, user.getId());
                if (existingMember.isPresent()) {
                    failCount++;
                    errorMsg.append("用户 ").append(memberDto.getEmailAddress()).append(" 已经是俱乐部成员；");
                    continue;
                }
                
                // 添加成员
                clubMemberService.addMember(user.getId(), clubId, ClubMemberRoleEnum.MEMBER, club.getClubGroupId());
                successCount++;
                
            } catch (Exception e) {
                failCount++;
                errorMsg.append("用户 ").append(memberDto.getEmailAddress()).append(" 导入失败：其他异常原因").append("；");
            }
        }
        
        // 更新俱乐部成员数量
        clubService.updateMemberCount(clubId);
        
        String result = String.format("导入成功，成功 %d 条，失败 %d 条", successCount, failCount);
        if (errorMsg.length() > 0) {
            result += "。失败原因：" + errorMsg.toString();
        }
        
        return result;
    }

    public static void main(String[] args) {
        int i = Integer.parseInt("LV1".replaceAll("\\D+", ""));
        BigDecimal divide = new BigDecimal("330.00").divide(new BigDecimal(1600), 2, RoundingMode.HALF_DOWN);

        System.out.println(divide);
    }

    @Transactional(rollbackFor = Exception.class)
    public void initActivityClub(Long userId) {
        Map<String,Long> map = new HashMap<>();
        Club newClub1 = new Club();
        newClub1.setName("PitPat Pro League Elite Club");
        newClub1.setDescription("With the new competition system——PitPat Pro League，PitPat Pro League Elite Club has gathered players who have passed the 2025 PitPat Pro League Open Trials. Membership is by invitation only. As a club member, you'll enjoy exclusive member benefits. These players will now advance to compete in the next round of the 2025 PitPat Pro League Elite Showdown!");
        newClub1.setLogo("https://pitpat-oss.s3.us-east-2.amazonaws.com/1750734255465.jpg");
        newClub1.setType(2);
        newClub1.setClubLevel("LV4");
        newClub1.setOwnerUserId(userId);
        Long newClub1Id = createActivityClub(newClub1.getName(),newClub1);
        map.put("monthlyClub", newClub1Id);
        Club newClub2 = new Club();
        newClub2.setName("PitPat Pro League Championship Club");
        newClub2.setDescription("With the new competition system——PitPat Pro League，PitPat Pro League Championship Club has gathered players who have passed the 2025 PitPat Pro League Elite Showdown. Membership is by invitation only. As a club member, you'll enjoy exclusive member benefits. These players will now advance to compete in the next round of the 2025 PitPat Pro League Championship Clash!");
        newClub2.setLogo("https://pitpat-oss.s3.us-east-2.amazonaws.com/1750734259708.jpg");
        newClub2.setType(2);
        newClub2.setClubLevel("LV4");
        newClub2.setOwnerUserId(userId);
        Long newClub2Id = createActivityClub(newClub2.getName(),newClub2);
        map.put("seasonalClub", newClub2Id);
        Club newClub3 = new Club();
        newClub3.setName("PitPat Pro League Grand Final Club");
        newClub3.setDescription("With the new competition system——PitPat Pro League，PitPat Pro League Grand Final Club has gathered players who have passed the 2025 PitPat Pro League Championship Clash. Membership is by invitation only. As a club member, you'll enjoy exclusive member benefits. These players will now advance to compete in the next round of the 2025 PitPat Pro League Grand Final!");
        newClub3.setLogo("https://pitpat-oss.s3.us-east-2.amazonaws.com/1750734264645.jpg");
        newClub3.setType(2);
        newClub3.setClubLevel("LV4");
        newClub3.setOwnerUserId(userId);
        Long newClub3Id = createActivityClub(newClub3.getName(),newClub3);
        map.put("annualClub", newClub3Id);
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigName(ConfigKeyEnums.USER_JOIN_COMPETITIVE_CLUB.getDesc());
        sysConfig.setConfigKey(ConfigKeyEnums.USER_JOIN_COMPETITIVE_CLUB.getCode());
        sysConfig.setConfigValue(JsonUtil.writeString(map));
        sysConfig.setRemark("monthlyClub：用户当前该加入的月赛俱乐部id,nextMonthlyClub：下一个月赛俱乐部id");
        iSysConfigService.insertConfig(sysConfig);
    }

    public Long createActivityClub(String newClubName, Club originalClub) {
        Long clubId = createNewClub(newClubName, originalClub);
        //写入俱乐部成员
        String env = (envProfile.equals("prod") || envProfile.equals("pre")) ? "prod" : envProfile;//由于预发和线上使用同数据库
        String clubGroupId = ApiConstants.clubGroupId + clubId + env;
        clubMemberService.addMember(originalClub.getOwnerUserId(), clubId, ClubMemberRoleEnum.OWNER, clubGroupId);
        clubService.updateMemberCount(clubId);
        return clubId;
    }


    public Long createNewClub(String newClubName, Club originalClub) {
        Club newClub = new Club();
        newClub.setName(newClubName);
        newClub.setLowerName(newClubName.toLowerCase());
        newClub.setDescription(originalClub.getDescription());
        newClub.setLogo(originalClub.getLogo());
        newClub.setOwnerUserId(originalClub.getOwnerUserId());
        newClub.setMemberCount(1);
        newClub.setMatchCount(0);
        newClub.setType(originalClub.getType());
        newClub.setClubLevel(originalClub.getClubLevel());
        newClub.setState(ClubStateEnum.NORMAL.getCode());
        newClub.setInviteCode(createInviteCode());
        newClub.setRequiredInviteCode(false);
        newClub.setRequiresApproval(false);
        newClub.setIsVipCreate(originalClub.getIsVipCreate());
        clubService.insert(newClub);
        Long clubId = newClub.getId();
        Club club1 = new Club();
        String env = (envProfile.equals("prod") || envProfile.equals("pre")) ? "prod" : envProfile;//由于预发和线上使用同数据库
        String clubGroupId = ApiConstants.clubGroupId + clubId + env;
        club1.setClubGroupId(clubGroupId);
        club1.setId(clubId);
        club1.setDescription(newClub.getDescription());
        clubService.update(club1);
        GroupResultDto resultDto = tencentImUtil.createGroup(newClub.getOwnerUserId(), clubGroupId, newClubName);
        if (!Objects.equals(resultDto.getErrorCode(), 0)) {
            log.error("创建俱乐部群聊失败,club:{},userId:{},result:{}", clubId, newClub.getOwnerUserId(), JsonUtil.writeString(resultDto));
            throw new BizI18nException("club.op.fail");
        }
        log.info("创建新的俱乐部成功, clubId: {}, clubName: {}", newClub.getId(), newClubName);
        return newClub.getId();
    }

}
