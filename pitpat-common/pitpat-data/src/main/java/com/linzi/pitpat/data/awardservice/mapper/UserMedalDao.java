package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 用户勋章 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.entity.dto.UserMedalDto;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.LIKE;
import com.lz.mybatis.plugin.annotations.OrderBy;
import com.lz.mybatis.plugin.annotations.OrderType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface UserMedalDao extends BaseMapper<UserMedal> {


    UserMedal selectUserMedalById(@Param("id") Long id);


    Long insertUserMedal(UserMedal userMedal);

    int updateUserMedalById(UserMedal userMedal);

    List<UserMedal> selectUserMedalByUserId(Long userId, Integer isValid);

    List<UserMedal> selectUserMedalByUserIdIsValidObtainEndTrigger(Long userId, Integer isValid, Integer obtain, @IF Integer endTrigger);


    List<UserMedal> selectUserMedalByUserIdObtainIsPop(Long userId, Integer obtain, @IF Integer isPop, Integer isValid);

    void updateUserMedalIsPopByUserId(Integer isPop, Long id);

    /**
     * 查询用户的勋章，赛事勋章只有拥有的时候才展示，没有不展示（因为赛事勋章错过了就不在拥有了，所以没必要把未获得的展示出来）
     *
     * @param userId
     * @param obtain
     * @param isValid
     * @return
     */

    @OrderBy(value = "obtain_time", type = {OrderType.DESC})
    List<UserMedal> selectUserMedalByUserIdObtain(Long userId, Integer obtain, Integer isValid);

    List<UserMedalDto> selectUserMedalByUserIdList(IPage page, @Param("userId") Long userId, @Param("obtain") Integer obtain);


    List<UserMedal> selectUserMedalValid(@LIKE @Param("isValid") Integer isValid, @Param("validStartTime") ZonedDateTime validStartTime, @Param("validEndTime") ZonedDateTime validEndTime);

    void updateUserMedalIsHomePagePosByUserId(ZonedDateTime gmtModified, Integer isShowHomePage, Integer pos, Long userId);

    List<MedalConfig> selectMonthDataMedalImgs(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<UserMedal> findRepeatAll();

    List<UserMedal> findRepeatAll1();

    /**
     * 获取用户已获得的勋章图片
     *
     * @param userId
     * @return
     */
    List<String> findUserObtainMedal(@Param("userId") Long userId, @Param("limitNum") Integer limitNum);
}
