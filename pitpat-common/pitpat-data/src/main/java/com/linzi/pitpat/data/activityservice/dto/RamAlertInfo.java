package com.linzi.pitpat.data.activityservice.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 内存告警信息对象
 */
@Data
public class RamAlertInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 告警总次数
     */
    private Integer alertCount;
    
    /**
     * 首次告警时间
     */
    private ZonedDateTime firstAlertTime;
    
    /**
     * 最后一次告警时间
     */
    private ZonedDateTime lastAlertTime;
    
    /**
     * 最大内存占用率
     */
    private BigDecimal maxRamUsagePercentage;
    
    /**
     * 设备型号
     */
    private String deviceModel;
    
    /**
     * 构造函数
     */
    public RamAlertInfo() {
        this.alertCount = 0;
        this.maxRamUsagePercentage = BigDecimal.ZERO;
    }
    
    /**
     * 构造函数
     */
    public RamAlertInfo(Long userId, String deviceModel) {
        this();
        this.userId = userId;
        this.deviceModel = deviceModel;
        this.firstAlertTime = ZonedDateTime.now();
        this.lastAlertTime = ZonedDateTime.now();
    }
    
    /**
     * 增加告警次数
     */
    public void incrementAlertCount() {
        this.alertCount++;
        this.lastAlertTime = ZonedDateTime.now();
    }
    
    /**
     * 更新最大内存占用率
     */
    public void updateMaxRamUsage(BigDecimal ramUsagePercentage) {
        if (ramUsagePercentage != null && 
            (this.maxRamUsagePercentage == null || 
             ramUsagePercentage.compareTo(this.maxRamUsagePercentage) > 0)) {
            this.maxRamUsagePercentage = ramUsagePercentage;
        }
    }
    
    /**
     * 检查是否达到告警阈值
     */
    public boolean isAlertThresholdReached() {
        return this.alertCount >= 3;
    }
}
