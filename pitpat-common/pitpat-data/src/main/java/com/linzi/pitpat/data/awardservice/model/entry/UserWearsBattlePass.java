package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.WearConstant;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 用户新里程碑服装待领取记录
 *
 * <AUTHOR>
 * @since 2023-10-20
 */

@Data
@TableName("zns_user_wears_battle_pass")
@Builder
public class UserWearsBattlePass implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserWearsBattlePass:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除（0否 1是）
    public final static String gmt_create = CLASS_NAME + "gmt_create";        //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    //
    public final static String wear_type = CLASS_NAME + "wear_type";          // 获取的服装类型: 1:发色（头发、帽子）、2:肤色、3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子
    public final static String wear_value = CLASS_NAME + "wear_value";        // 服装ID
    public final static String wear_name = CLASS_NAME + "wear_name";          // 服装名称
    public final static String wear_image_url = CLASS_NAME + "wear_image_url";    // 服装图片URL
    public final static String expired_time = CLASS_NAME + "expired_time";    // 失效时间
    public final static String status_ = CLASS_NAME + "status";               // 状态： 0.下发待领取 1已领取
    public final static String is_new = CLASS_NAME + "is_new";                // 新标识 0: 否, 1: 是
    public final static String user_id = CLASS_NAME + "user_id";              // 用户id
    public final static String activity_id = CLASS_NAME + "activity_id";      // 发放奖励的时候对应的活动id
    public final static String milepost_id = CLASS_NAME + "milepost_id";      // 里程碑id
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    /**
     * 获取的服装类型: 1:发色（头发、帽子）, 2:肤色, 3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子, 8:套装，9:背部服饰，10：动作
     *
     * @see WearConstant.WearTypeEnum
     */
    private Integer wearType;
    //服装ID
    private Integer wearValue;
    //服装名称
    private String wearName;
    //服装图片URL
    private String wearImageUrl;
    //失效时间
    private Integer expiredTime;
    //状态： 0.下发待领取 1已领取
    private Integer status;
    //新标识 0: 否, 1: 是
    private Integer isNew;
    //用户id
    private Long userId;
    //发放奖励的时候对应的活动id
    private Long activityId;
    //里程碑id
    private Integer milepostId;
    /**
     * 奖励来源，0：活动奖励，1：活动进阶奖励
     */
    private Integer source;

    @Override
    public String toString() {
        return "UserWearsBattlePass{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",wearType=" + wearType +
                ",wearValue=" + wearValue +
                ",wearName=" + wearName +
                ",expiredTime=" + expiredTime +
                ",status=" + status +
                ",isNew=" + isNew +
                ",userId=" + userId +
                ",activityId=" + activityId +
                ",milepostId=" + milepostId +
                "}";
    }
}
