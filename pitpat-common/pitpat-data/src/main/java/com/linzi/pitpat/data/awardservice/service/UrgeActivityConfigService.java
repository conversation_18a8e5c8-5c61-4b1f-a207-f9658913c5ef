package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 激历配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linzi.pitpat.data.awardservice.model.dto.EggActivityConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeActivityConfig;

import java.time.ZonedDateTime;
import java.util.List;

public interface UrgeActivityConfigService extends IService<UrgeActivityConfig> {

    Page<EggActivityConfigDto> selectPageByCondition(IPage page, int i, ZonedDateTime gmtStartTime, ZonedDateTime gmtEndTime, List<Integer> urgeTypes);

    List<UrgeActivityConfig> selectUrgeActivityConfigByActivityId(Long activityId, Long routeId, ZonedDateTime date, ZonedDateTime date1);

    List<UrgeActivityConfig> selectUrgeActivityConfigByActivityIdRouteIdDateDate(Long activityId, Long routeId, ZonedDateTime date, ZonedDateTime date1);

    List<UrgeActivityConfig> selectUrgeActivityConfigByTypeGmtStartTimeGmtEndTime(Integer activityType, Long routeId, ZonedDateTime date, ZonedDateTime date1);

    List<UrgeActivityConfig> selectUrgeActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(Integer activityType, Long routeId, ZonedDateTime date, ZonedDateTime date1);

    UrgeActivityConfig selectUrgeActivityConfigById(Long urgeActivityConfigId);
}
