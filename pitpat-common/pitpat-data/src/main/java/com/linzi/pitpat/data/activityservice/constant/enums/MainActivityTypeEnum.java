package com.linzi.pitpat.data.activityservice.constant.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum MainActivityTypeEnum {
    RANK("rank", "段位赛", 15),
    SINGLE("single", "单赛事", 13),
    SERIES_MAIN("series-main", "系列赛主赛事", 14),
    SERIES_SUB("series-sub", "系列赛阶段赛事", null),
    OLD("old", "老赛事", null),
    PROP("prop", "道具赛", 18),
    SINGLE_POLYMERIZATION("single-polymerization", "单赛事聚合", 13),
    SINGLE_POLYMERIZATION_RUNNING("single-polymerization-running", "单赛事同一批次最新的聚合", 13),
    PLACEMENT("placement", "定级赛", 21),
    FREE_CHALLENGE_MAIN("FREE_CHALLENGE_MAIN", "自由挑战赛主", 22),
    FREE_CHALLENGE_SUB("FREE_CHALLENGE_SUB", "自由挑战赛从", 22),
    ;

    private final String type;
    private final String remark;

    private final Integer oldType;

    private static final MainActivityTypeEnum[] VALUE;

    static {
        VALUE = values();
    }

    public static boolean IsNewActivity(String type){
        if (type != null){
            MainActivityTypeEnum typeEnum = findByType(type);
            if (typeEnum != null){
                ArrayList<MainActivityTypeEnum> list = Lists.newArrayList(SINGLE, SINGLE_POLYMERIZATION, SINGLE_POLYMERIZATION_RUNNING, SERIES_MAIN, SERIES_SUB);
                return list.contains(typeEnum);
            }
        }
        return false;
    }

    public static MainActivityTypeEnum findByType(String type) {
        return Arrays.stream(VALUE).filter(e -> e.type.equals(type)).findFirst().orElse(null);
    }

    public static List<String> singleTypes() {
        return Lists.newArrayList(SINGLE.type, SINGLE_POLYMERIZATION.type, SINGLE_POLYMERIZATION_RUNNING.type);
    }

    public static List<String> awardPopActivityTypes() {
        return Lists.newArrayList(SINGLE.type, SINGLE_POLYMERIZATION.type, SINGLE_POLYMERIZATION_RUNNING.type, SERIES_SUB.type,FREE_CHALLENGE_MAIN.type);
    }


    public static boolean isFreeChallengeActivity(String mainType) {
        return Lists.newArrayList(FREE_CHALLENGE_MAIN.type, FREE_CHALLENGE_SUB.type).contains(mainType);
    }
}
