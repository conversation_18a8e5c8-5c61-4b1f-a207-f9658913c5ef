package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.RankActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 赛事结果
 *
 * <AUTHOR>
 * @date 2023/12/4 10:40
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RankedActivityResultManager {

    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RedissonClient redissonClient;
    private final RunRankedActivityUserService runRankedActivityUserService;
    private final ISysConfigService sysConfigService;
    private final UserRankedLevelService userRankedLevelService;
    private final MainActivityService mainActivityService;
    private final RankActivityBizService rankActivityBizService;

    @Transactional
    public void handleFinishedData(MainActivity mainActivity, ZnsUserRunDataDetailsEntity userRunDataDetail) {
        log.info("开始处理用户段位赛数据， userId={}, activityId={}, averagePace={}", userRunDataDetail.getUserId(), mainActivity.getId(), userRunDataDetail.getAveragePace());
        try {
            List<ZnsUserRunDataDetailsEntity> userRunDataDetailList = userRunDataDetailsService.findListByQuery(new UserRunDataDetailsQuery().setActivityId(mainActivity.getId()));
            //跑步完成的用户运动下详情
            userRunDataDetailList = userRunDataDetailList.stream()
                    .filter(item -> item.getRunMileage().intValue() >= item.getDistanceTarget().intValue())
                    .sorted(Comparator.comparingInt(ZnsUserRunDataDetailsEntity::getRunTimeMillisecond)).toList();

            String sysConfig = sysConfigService.selectConfigByKey("new_ranked_activity_config");

            //TODO 读取配置
            if (userRunDataDetailList.size() >= 8) {
                List<RunRankedActivityUser> runRankedActivityUserList = runRankedActivityUserService.findListByActivityId(mainActivity.getId());
                log.info("段位赛当前参赛用户数量, count={}", runRankedActivityUserList.size());
                if (!CollectionUtils.isEmpty(runRankedActivityUserList) && userRunDataDetailList.size() >= 8) {
                    if (Objects.nonNull(mainActivity.getActivityEndTime())) {
                        log.info("活动结束时间={}, 当前时间={}", mainActivity.getActivityEndTime(), ZonedDateTime.now());
                        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
                        if (endTime.isBefore(ZonedDateTime.now())) {
                            log.info("活动结束时间小于当前时间，不发放奖励，activityId={}", mainActivity.getId());
                            return;
                        }
                    }
                    log.info("段位赛排所有用户已经完成跑步，主动发放所有段奖励和排名奖励");
                    activityEnd(mainActivity, true);
                    mainActivity.setActivityState(MainActivityStateEnum.ENDED.getCode());
                    mainActivityService.update(mainActivity);
                    log.info("手动结束段位赛，防止奖励重复发放，activityId={}", mainActivity.getId());
                }
            }
        } catch (Exception e) {
            log.info("手动发放段位赛奖励失败，msg={}", e.getMessage(), e);
        }
    }

    /**
     * 活动结束
     *
     * @param mainActivity
     */
    public void activityEnd(MainActivity mainActivity) {
        try {
            activityEnd(mainActivity, true);
        } catch (Exception e) {
            log.info("发放段位赛奖励失败，msg={}", e.getMessage(), e);
        }
    }

    public void activityEnd(MainActivity mainActivity, Boolean needLock) {
        log.info("开始处理用户段位赛数据，activityId={}", mainActivity.getId());

        Runnable runnable = () -> {
            MainActivity queryMainActivity = mainActivityService.findById(mainActivity.getId());
            if (Objects.equals(queryMainActivity.getActivityState(), MainActivityStateEnum.ENDED.getCode())) {
                log.warn("段位赛已经结束，无需手动发放奖励， activityId={}", mainActivity.getId());
                throw new BaseException("段位赛已经结束，无需手动发放奖励");
            }
            List<ZnsUserRunDataDetailsEntity> userRunDataDetailList = userRunDataDetailsService.findListByQuery(new UserRunDataDetailsQuery().setActivityId(mainActivity.getId()));
            userRunDataDetailList.forEach(item -> {
                log.info("段位赛完赛信息，detailId={}, runStatus={},runMileage={},runTimeMillisecond={},averagePace={},userId={}", item.getId(), item.getRunStatus(), item.getRunMileage(), item.getRunTimeMillisecond(), item.getAveragePace(), item.getUserId());
            });
            //跑步完成的用户运动下详情
            userRunDataDetailList = userRunDataDetailList.stream()
                    .filter(item -> item.getRunMileage().intValue() >= item.getDistanceTarget().intValue())
                    .sorted(Comparator.comparingInt(ZnsUserRunDataDetailsEntity::getRunTimeMillisecond)).toList();
            userRunDataDetailList.forEach(item -> {
                log.info("完赛段位赛排名信息，detailId={}, runStatus={},runMileage={},runTimeMillisecond={},averagePace={},userId={}", item.getId(), item.getRunStatus(), item.getRunMileage(), item.getRunTimeMillisecond(), item.getAveragePace(), item.getUserId());
            });
            for (int index = 0; index < userRunDataDetailList.size(); index++) {
                int rank = index + 1;
                ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailList.get(index);
                log.info("段位赛数据activityId={}, detailId={},userId={}", mainActivity.getId(), userRunDataDetail.getId(), userRunDataDetail.getUserId());

                RunRankedActivityUser runRankedActivityUser = rankActivityBizService.updateRunRankedActivityUser(userRunDataDetail, rank);
                UserRankedLevel userRankedLevel = rankActivityBizService.updateUserRankedLevel(userRunDataDetail, rank);
                rankActivityBizService.sendRankedActivityAward(mainActivity, userRunDataDetail, rank, runRankedActivityUser, userRankedLevel);
            }
        };
        if (needLock) {
            String lockKey = RedisConstants.RANKED_ACTIVITY_DATA_END_ACTIVITY_RESULT + mainActivity.getId();
            RLock lock = redissonClient.getLock(lockKey);
            LockHolder.tryLock(lock, 10, 180, runnable);
        } else {
            runnable.run();
        }
    }


    public void createRunRankedActivityUser(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        log.info("znsUserRunDataDetailsEntity ,getRunMileage={}, getDistanceTarget={}", userRunDataDetail.getRunMileage(), userRunDataDetail.getDistanceTarget());
        RunRankedActivityUser runRankedActivityUser = new RunRankedActivityUser();
        runRankedActivityUser.setActivityId(userRunDataDetail.getActivityId());
        runRankedActivityUser.setUserId(userRunDataDetail.getUserId());
        runRankedActivityUser.setRunDataDetailsId(userRunDataDetail.getId());
        runRankedActivityUser.setRunTime(userRunDataDetail.getRunTime());
        runRankedActivityUser.setRunMileage(userRunDataDetail.getRunMileage());

        runRankedActivityUser.setRank(-1);
        runRankedActivityUser.setTargetRunMileage(userRunDataDetail.getRunTimeMillisecond());
        runRankedActivityUser.setIsTest(userRunDataDetail.getIsTest());
        runRankedActivityUser.setIsRobot(userRunDataDetail.getIsRobot());
        runRankedActivityUser.setIsComplete(0);
        if (userRunDataDetail.getIsRobot() == 0) {
            runRankedActivityUser.setIsPlacement(0);
        }
        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(userRunDataDetail.getUserId());
        if (userRankedLevel.getIsInPlacement() == 1) {
            Integer segment = runRankedActivityUserService.getCurrentRankSegment(userRankedLevel.getUserId());
            if (segment < 3) {
                runRankedActivityUser.setIsPlacement(1);
            }
        }
        log.info("创建段位赛的用户数据：userId={},userRunDataDetailId={}", userRunDataDetail.getUserId(), userRunDataDetail.getId());
        runRankedActivityUserService.insert(runRankedActivityUser);
    }

}
