package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-12-02
 */

@Data
@NoArgsConstructor
@TableName("zns_coupon_i18n")
public class CouponI18n implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //是否删除
    private Integer isDelete;
    //优惠券id
    private Long couponId;
    //默认语言code
    private String langCode;
    //默认语言名称
    private String langName;
    //券名称
    private String title;
    //可用券描述
    private String canUseDescription;
    //券描述
    private String description;
}
