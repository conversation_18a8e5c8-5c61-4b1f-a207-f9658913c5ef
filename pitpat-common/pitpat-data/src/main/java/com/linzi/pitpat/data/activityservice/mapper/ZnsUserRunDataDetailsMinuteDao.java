package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsMinuteEntity;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.LT;
import com.lz.mybatis.plugin.annotations.Realy;
import org.apache.ibatis.annotations.Mapper;

import java.time.ZonedDateTime;

/**
 * 用户跑步详情表-分钟
 *
 * <AUTHOR>
 * @date 2021-10-11 14:24:40
 */
@Mapper
public interface ZnsUserRunDataDetailsMinuteDao extends BaseMapper<ZnsUserRunDataDetailsMinuteEntity> {
    @Realy
    void deleteUserRunDataDetailsMinute7Days(@LT @DateFormat ZonedDateTime createTime);
}
