package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 彩蛋配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-20
 */

import com.baomidou.mybatisplus.extension.service.IService;
import com.linzi.pitpat.data.awardservice.model.entry.ColorEggConfig;
import com.linzi.pitpat.data.awardservice.model.vo.ColorEggConfigVo;

import java.time.ZonedDateTime;
import java.util.List;

public interface ColorEggConfigService extends IService<ColorEggConfig> {


    ColorEggConfig selectColorEggConfigById(Long id);

    List<ColorEggConfigVo> selectColorEggConfigByStatusGmtStartGmtEnd(Integer status, ZonedDateTime gmtStart, ZonedDateTime gmtEnd);
}