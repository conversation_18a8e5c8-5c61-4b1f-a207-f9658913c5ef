package com.linzi.pitpat.data.activityservice.strategy;


import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityEntryFeeQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialTeamActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.activityservice.model.vo.TeamRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.dto.RunActivityRewardConfigDetails;
import com.linzi.pitpat.data.awardservice.model.entry.AwardLimitRule;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserAccountDetailSub;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivitySubstateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunMode;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserFriendEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.data.vo.report.MultipleRunReportVo;
import com.linzi.pitpat.data.vo.report.MyGradesDto;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class TeamRunActivityStrategy extends BaseActivityStrategy {

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Resource
    private ActivityAwardCurrencyBizService activityAwardCurrencyBizService;

    @Resource
    private ActivityEntryFeeService activityEntryFeeService;

    @Resource
    private ExchangeRateConfigService exchangeRateConfigService;

    @Resource
    private ZnsUserService znsUserService;

    private final RedissonClient redissonClient;


    @Override
    public void wrapperRunActivityBasicData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity) {
        super.wrapperRunActivityBasicData(activityEntity, activityDetailVO, userEntity);
        setActivityLaunchUser(activityDetailVO, activityEntity.getId());
        // 设置对应currency的entryFee
        activityDetailVO.setActivityEntryFee(activityEntity.getActivityEntryFee());
        ActivityEntryFeeQuery query = ActivityEntryFeeQuery.builder().activityId(activityEntity.getId()).build();
        List<ActivityEntryFee> list = activityEntryFeeService.findList(query);
        String currencyCode = userAccountService.getUserAccount(userEntity.getId()).getCurrencyCode();
        if (!CollectionUtils.isEmpty(list)) {
            ActivityEntryFee activityEntryFee = list.stream().filter(e -> e.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
            ActivityEntryFee activityUSDEntryFee = list.stream().filter(e -> e.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())).findFirst().orElse(null);
            if (runActivityService.checkNewActivity(activityEntity.getId()) && Objects.nonNull(activityEntryFee)) {
                BigDecimal entryFee = activityEntryFee.getEntryFee();
                entryFee = I18nConstant.currencyFormat(currencyCode, entryFee);
                activityDetailVO.setActivityEntryFee(entryFee);
                activityEntity.setActivityEntryFee(entryFee);
            } else if (Objects.nonNull(activityUSDEntryFee)) {
                BigDecimal entryFee = activityUSDEntryFee.getEntryFee();
                entryFee = I18nConstant.currencyFormat(currencyCode, entryFee);
                activityDetailVO.setActivityEntryFee(entryFee);
                activityEntity.setActivityEntryFee(entryFee);
            }
        }
        Integer limitPeopleSize = getLimitPeopleSize();
        // 规则语句全球化
        String rankAwardRule = I18nMsgUtils.getMessage("activity.basic.rank.award.rule", limitPeopleSize);
        activityDetailVO.setRankAwardRule(rankAwardRule);
        // 国际化数据
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.findRunActivityConfig(activityEntity.getActivityConfigId());
        Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
        wrapperRunActivityRules(activityEntity, jsonObject, null, 3000);
    }

    @Override
    public void wrapperRunActivityDetailData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        super.wrapperRunActivityDetailData(activityEntity, activityDetailVO, userEntity, oneself);
        // 活动奖励金额
        List<AwardConfigDto> awardConfigDtoListActivity = awardConfigBizService.selectAwardConfigDtoListByActivityId(activityEntity.getId(), null, null);
        boolean isNewConfig = !CollectionUtils.isEmpty(awardConfigDtoListActivity);
        String currencyCode = userAccountService.getUserCurrency(userEntity.getId()).getCurrencyCode();

        String participateAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD));
        String completeAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.COMPLETE_AWARD));
        //新奖励配置/页面兼容
        //新奖励配置
        Map<String, Object> poolConfig = awardConfigBizService.awardPoolConfig(activityEntity.getUserCount(), awardConfigDtoListActivity, false, isNewConfig, userEntity);
        BigDecimal firstAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.FIRST_AWARD)), 1, AwardTypeEnum.AMOUNT.getType());
        BigDecimal secondAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.SECOND_AWARD)), 2, AwardTypeEnum.AMOUNT.getType());
        BigDecimal thirdAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.THIRD_AWARD)), 3, AwardTypeEnum.AMOUNT.getType());
        if (isNewConfig) {
            participateAward = awardActivityManager.selectAwardAmount(awardConfigDtoListActivity, AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD)), userEntity).toString();
            completeAward = awardActivityManager.selectAwardAmount(awardConfigDtoListActivity, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.COMPLETE_AWARD)), userEntity).toString();
        }

        // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
        Integer lastEnterMinutes = MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
        if (null == lastEnterMinutes) {
            lastEnterMinutes = 30;
        }
        activityDetailVO.setParticipateAward(participateAward);
        activityDetailVO.setCompleteAward(completeAward);
        long teamMillisecond = lastEnterMinutes.intValue() * 60000;
        activityDetailVO.setLastEnterTeamRunTime(Long.valueOf(teamMillisecond));
        // 组队跑开始跑前多少分钟可入场(配置分钟)
        Integer beforeEnterMinutes = MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
        if (null == beforeEnterMinutes) {
            beforeEnterMinutes = 5;
        }
        long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
        activityDetailVO.setRunBeforeEnter(challengeMillisecond);
        //在前面步骤已经处理了activityEntity中的i18n数据，所以这直接赋值即可
        activityDetailVO.setAwardRule(activityEntity.getAwardRule());
        //被邀请人的奖励金额
        BigDecimal awardAmount = calculateTotalBonus(activityEntity, activityDetailVO.getActivityUsers().size() + 1, activityEntity.getActivityEntryFee(), null, userEntity, true);
        activityDetailVO.setInviteTotalBonus(awardAmount);
        //名次奖励
//        BigDecimal firstAward = jsonObjectConfig.get(ApiConstants.FIRST_AWARD);
        firstAward = I18nConstant.currencyFormat(currencyCode, firstAward);
        activityDetailVO.setFirstAward(firstAward);
//        BigDecimal secondAward = jsonObjectConfig.get(ApiConstants.SECOND_AWARD);
        secondAward = I18nConstant.currencyFormat(currencyCode, secondAward);
        activityDetailVO.setSecondAward(secondAward);
//        BigDecimal thirdAward = jsonObjectConfig.get(ApiConstants.THIRD_AWARD);
        thirdAward = I18nConstant.currencyFormat(currencyCode, thirdAward);
        activityDetailVO.setThirdAward(thirdAward);
    }

    @Override
    protected void wrapperRunActivityRules(ZnsRunActivityEntity runActivityEntity, Map<String, Object> jsonObject, Integer challengeRunType, Integer appVersion) {
        BigDecimal priceProportion = MapUtil.getBigDecimal(jsonObject.get("priceProportion"));
        String awardRule = I18nMsgUtils.getMessage("activity.team.run.award.rule");
        if (Objects.nonNull(priceProportion)) {
            String priceProportionStr = BigDecimalUtil.removeEndOfZero(BigDecimalUtil.multiply(priceProportion, new BigDecimal(100))).toString();
            awardRule = awardRule + "\n" + I18nMsgUtils.getMessage("activity.team.run.award.rule.append", priceProportionStr);
        }
        runActivityEntity.setAwardRule(awardRule);
    }

    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {
        List<RunActivityUserVO> runActivityUsers = activityUserManager.findRunActivityUsers(activityEntity, null, userEntity.getId(), activityDetailVO, null, null, activityUserStatus);
        activityDetailVO.setActivityUsers(runActivityUsers);
    }

    @Override
    public Map<String, Object> getActivityConfig(RunActivityRequest runActivity, String activityConfig, Integer measureUnit, Long userId, boolean isAdd) {
        super.getActivityConfig(runActivity, activityConfig, measureUnit, userId, isAdd);
        if (Objects.isNull(runActivity.getAcceptedUserCount())) {
            runActivity.setAcceptedUserCount(1);
        }
        // TODO: 2023/7/18 2.6.0以后版本删掉
        BigDecimal completeAward = BigDecimal.ZERO;

        if (runActivity.getIsRobotStart() == 1) {
            jsonObjectConfig.put("completeAward", runActivity.getCompleteAward());
            jsonObjectConfig.put("firstAward", runActivity.getFirstAward());
            jsonObjectConfig.put("secondAward", runActivity.getSecondAward());
            jsonObjectConfig.put("thirdAward", runActivity.getThirdAward());
        } else {
//            String currencyCode = userAccountService.getUserAccount(userId).getCurrencyCode();
//            BigDecimal nowRate = BigDecimal.ONE;
//            if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
//                nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
//            }
//            BigDecimal acceptedUserCount = new BigDecimal(runActivity.getAcceptedUserCount());
//            //计算完赛奖励
//            if (Integer.valueOf(0).equals(measureUnit)) {
//                BigDecimal kms = runActivity.getRunMileage().divide(new BigDecimal(1000));
//
//                BigDecimal completeAwardPerKm = BigDecimalUtil.get(jsonObjectConfig,ApiConstants.COMPLETE_AWARD_PER_KM);
//                completeAward = completeAwardPerKm.multiply(kms).multiply(nowRate);
//            } else {
//                BigDecimal miles = runActivity.getRunMileage().divide(new BigDecimal(1600));
//                BigDecimal completeAwardPerMiles = BigDecimalUtil.get(jsonObjectConfig,ApiConstants.COMPLETE_AWARD_PER_MILES);
//                completeAward = completeAwardPerMiles.multiply(miles).multiply(nowRate);
//            }
//            BigDecimal baseExtraAward = BigDecimalUtil.get(jsonObjectConfig,ApiConstants.BASE_EXTRA_AWARD);
//            if (Objects.isNull(baseExtraAward) || baseExtraAward.compareTo(BigDecimal.ZERO) <= 0) {
//                baseExtraAward = BigDecimal.ONE;
//            }
            if (runActivity.getAcceptedUserCount() >= getLimitPeopleSize()) {
                //相当于新建时不会走该方法

//                BigDecimal extraAward = baseExtraAward.multiply(acceptedUserCount);
//                BigDecimal maxExtraAwardLimit = BigDecimalUtil.get(jsonObjectConfig,ApiConstants.MAX_EXTRA_AWARD_LIMIT);
//                if (maxExtraAwardLimit.compareTo(BigDecimal.ZERO) > 0 && extraAward.compareTo(maxExtraAwardLimit) > 0) {
//                    extraAward = maxExtraAwardLimit;
//                }
//
//                jsonObjectConfig.put("firstAward",extraAward.multiply(getAwardRate("firstAwardRate",jsonObjectConfig)).setScale(2,BigDecimal.ROUND_HALF_DOWN));
//                jsonObjectConfig.put("secondAward",extraAward.multiply(getAwardRate("secondAwardRate",jsonObjectConfig)).setScale(2,BigDecimal.ROUND_HALF_DOWN));
//                jsonObjectConfig.put("thirdAward",extraAward.multiply(getAwardRate("thirdAwardRate",jsonObjectConfig)).setScale(2,BigDecimal.ROUND_HALF_DOWN));

                //新奖励配置
                List<AwardConfigDto> awardConfigDtoListActivity = awardConfigBizService.selectAwardConfigDtoListByActivityId(runActivity.getActivityId(), null, null);
                boolean isNewConfig = !CollectionUtils.isEmpty(awardConfigDtoListActivity);

                //新奖励配置/页面兼容
                //新奖励配置
                ZnsUserEntity userEntity = userService.findById(userId);
                Map<String, Object> poolConfig = awardConfigBizService.awardPoolConfig(runActivity.getAcceptedUserCount(), awardConfigDtoListActivity, isAdd, isNewConfig, userEntity);
                BigDecimal firstAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.FIRST_AWARD)), 1, AwardTypeEnum.AMOUNT.getType());
                BigDecimal secondAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.SECOND_AWARD)), 2, AwardTypeEnum.AMOUNT.getType());
                BigDecimal thirdAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.THIRD_AWARD)), 3, AwardTypeEnum.AMOUNT.getType());

                jsonObjectConfig.put("firstAward", firstAward);
                jsonObjectConfig.put("secondAward", secondAward);
                jsonObjectConfig.put("thirdAward", thirdAward);
            }
            if (!isAdd) {
                return jsonObjectConfig;
            }
            // 现在版本完赛奖励为运营手动设置，无需该逻辑
//            BigDecimal maxAwardLimit = BigDecimalUtil.get(jsonObjectConfig,ApiConstants.MAX_AWARD_LIMIT);
//            maxAwardLimit = maxAwardLimit.multiply(nowRate);
//            if (maxAwardLimit.compareTo(BigDecimal.ZERO) > 0 && completeAward.compareTo(maxAwardLimit) > 0) {
//                completeAward = maxAwardLimit;
//            }
//            completeAward = completeAward.setScale(2,BigDecimal.ROUND_HALF_UP);
//            jsonObjectConfig.put("completeAward",completeAward);
        }

        //设置机器人上限
        Integer robotLimit = 5;
        Random random = new Random();
        int i = random.nextInt(6);
        robotLimit = robotLimit + i;
        jsonObjectConfig.put(ApiConstants.TEAM_USER_LIMIT, robotLimit);
        Integer runBeforeEnter = MapUtil.getInteger(jsonObjectConfig.get("runBeforeEnter"));
        if (Objects.isNull(runBeforeEnter)) {
            runBeforeEnter = 5;
        }
        jsonObjectConfig.put("automaticAdmissionTime", runBeforeEnter);
        return jsonObjectConfig;
    }

    private BigDecimal getAwardRate(String key, Map<String, Object> jsonObjectConfig) {
        BigDecimal awardRate = MapUtil.getBigDecimal(jsonObjectConfig.get(key));
        if (Objects.isNull(awardRate)) {
            if ("firstAwardRate".equals(key)) {
                return new BigDecimal(0.5);
            } else if ("secondAwardRate".equals(key)) {
                return new BigDecimal(0.3);
            } else if ("thirdAwardRate".equals(key)) {
                return new BigDecimal(0.2);
            }
        }
        return BigDecimalUtil.divide(awardRate, new BigDecimal(100));
    }

    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        Integer minute = 30;
        String activityConfig = activityEntity.getActivityConfig();
        if (StringUtils.hasText(activityConfig)) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig);
            Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER)); // 最晚可进入时间
            if (Objects.nonNull(lastEnterMinutes)) {
                minute = lastEnterMinutes;
            }
        }
        //活动开始30分钟后不可操作
        ZonedDateTime date = activityEntity.getActivityStartTime().plusMinutes(minute);
        if (ZonedDateTime.now().compareTo(date) > 0) {
            String message = I18nMsgUtils.getMessage("activity.enter.timeOverDue", minute);
            log.info("checkRunActivityTime fail,error msg:{},activityStartTime:{},currentDate:{}", message, activityEntity.getActivityStartTime(), ZonedDateTime.now());
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enter.timeOverDue", minute));
        }
        return null;
    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals,
                                          boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {

//        String logNo = Logger.inheritableThreadLocalNo.get();
//        Long time = ch.qos.logback.classic.Logger.inheritableThreadLocalTime.get();
        // 接受组队活动
        Result result = canRefuseOrAcceptActivity(activityEntity, user);
        if (Objects.nonNull(result)) {
            return result;
        }
        if (1 == userStatus.intValue()) {

            //校验活动国家跟用户国家是否相同
            if (notContainsCountry(activityEntity, user)) {
                return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.mismatch.region")); //ActivityError.COUNTRY_ERROR.getMsg()
            }

            ZnsRunActivityEntity activity = new ZnsRunActivityEntity();

            if (user.getIsRobot() == 1) {
                activity.setHasRobot(1);
                //添加机器人策略
                ZonedDateTime endTime = DateUtil.addHours(activityEntity.getActivityStartTime(), 5);
                String runMode = getRunMode(activityEntity.getId());
                if (StringUtil.isEmpty(runMode)) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("activity.status.teamRun.robotFailed")); //"机器人跑步模式获取失败"
                } else {
                    Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
                    executor.execute(new Runnable() {
                        @SneakyThrows
                        @Override
                        public void run() {
                            Thread.sleep(3000);
                            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                            log.info("handleUserActivityState 消息回执处理");
                            if (immediatelyAdmission) {
                                mindUserMatchBizService.addRobotMindUserMatch(user.getId(), activityEntity.getId(), activityEntity.getRunMileage(), runMode, endTime);
                            }
                            Integer runBeforeEnter = runActivityConfigService.getIntegerConfig(RunActivityTypeEnum.TEAM_RUN.getType(), ApiConstants.ACTIVITY_BEFORE_ENTER, 5);
                            //到了候场时间自动入场
                            if (immediatelyAdmission || (ZonedDateTime.now().compareTo(activityEntity.getActivityStartTime().minusMinutes(runBeforeEnter)) >= 0 && ZonedDateTime.now().compareTo(activityEntity.getActivityStartTime()) < 0)) {
                                MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByActivityIdUserId(activityEntity.getId(), user.getId());
                                userOnlineBussiness.robotUpOnline(activityEntity, user, mindUserMatch, null);
                            }
                        }
                    });
                }
            }
            // 支付保证金逻辑
            Result payResult = runActivityPayManager.handlePayRunActivity(activityEntity, user, password, request, false);
            if (null != payResult) {
                return payResult;
            }
            //变更活动奖金池金额
            Integer userCount = runActivityUserService.queryActivityUserCount(activityEntity.getId(), true);

            // 变更用户状态为已接受
            ZnsRunActivityUserEntity activityUserEntity = runActivityUserService.findActivityUser(activityEntity.getId(), user.getId());
            if (null != activityUserEntity) {
                activityUserEntity.setUserState(ActivityUserStateEnum.ACCEPT.getState());
                runActivityUserService.updateById(activityUserEntity);
                log.info("修改活动用户状态，活动id：{}，用户id：{},用户状态：{}", activityEntity.getId(), user.getId(), ActivityUserStateEnum.ACCEPT.getState());
            } else {
                ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
                activityUser.setActivityId(activityEntity.getId());
                activityUser.setUserId(user.getId());
                activityUser.setIsRobot(user.getIsRobot());
                activityUser.setIsTest(user.getIsTest());

                activityUser.setNickname(user.getFirstName());
                activityUser.setUserState(ActivityUserStateEnum.ACCEPT.getState());
                activityUser.setActivityType(activityEntity.getActivityType());
                // 活动参与者
                activityUser.setUserType(2);
                if (activityEntity.getCompleteRuleType() == 1) {
                    activityUser.setTargetRunMileage(activityEntity.getRunMileage().intValue());
                } else if (activityEntity.getCompleteRuleType() == 2) {
                    activityUser.setTargetRunTime(activityEntity.getRunTime());
                }
                runActivityUserService.save(activityUser);
            }

            userCount = userCount + 1;

            activity.setId(activityEntity.getId());
            activity.setUserCount(userCount);
            activity.setRunMileage(activityEntity.getRunMileage());
            activity.setActivityConfig(activityEntity.getActivityConfig());
            activity.setBonusRuleType(activityEntity.getBonusRuleType());

            updateActivityConfig(activity, user, request.getAppVersion());

            BigDecimal totalBonus = calculateTotalBonus(activity, userCount, activityEntity.getActivityEntryFee(), null, user, false);
            activity.setActivityTotalBonus(totalBonus);

            runActivityService.updateById(activity);

            //赛前通知 2.11版本去除老通知
//            ZnsRunActivityUserEntity activityLaunchUser = runActivityUserService.findActivityLaunchUser(activity.getId());
//            runActivityUserService.preCompetitionNotification(activityEntity,Arrays.asList(user.getId()), activityLaunchUser.getNickname());
            return CommonResult.success();
        } else if (2 == userStatus.intValue()) {
            return userRefuse(activityEntity, user);
        }
        return CommonResult.success();
    }

    public Result checkHandleUserActivityState(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, Integer userStatus, ZnsUserEntity user) {
        Result result = super.checkHandleUserActivityState(activityEntity, activityUser, userStatus, user);
        if (Objects.nonNull(result)) {
            return result;
        }

        return canRefuseOrAcceptActivity(activityEntity, user);
    }

    @Override
    public List<? extends SimpleRunActivityVO> getActivityList(ZnsUserEntity user, boolean isTest, boolean checkVersion, boolean isHomepage, Integer source, Integer completeRuleType, List<Integer> runWalkStatus, Integer rateLimitType) {
        //查询所有赛事
        Long userId = user.getId();
        List<ZnsRunActivityEntity> list = runActivityService.getIndividualTeamRunActivity(userId, isTest, checkVersion, user.getCountry());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, null);
        List<Long> userIds = activityUsers.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        Map<Long, ZnsUserEntity> userEntityMap = null;
        if (!CollectionUtils.isEmpty(userIds)) {
            List<ZnsUserEntity> userList = userService.findByIds(userIds);
            userEntityMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        }

        Map<Long, List<ZnsRunActivityUserEntity>> listMap = activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getActivityId));

        List<OfficialTeamActivityListVO> activityList = new ArrayList<>();
        for (ZnsRunActivityEntity activity : list) {
            OfficialTeamActivityListVO vo = new OfficialTeamActivityListVO(activity);
            Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
            Currency currency = new Currency();
            currency.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
            currency.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
            boolean flag = runActivityService.checkNewActivity(activity.getId());
            if (flag) {
                ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(userId);
                String currencyCode = userAccount.getCurrencyCode();
                currency.setCurrencyCode(currencyCode);
                currency.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.findByCode(currencyCode).getSymbol());
                ActivityEntryFeeQuery activityEntryFeeQuery = ActivityEntryFeeQuery.builder().activityId(activity.getId()).currencyCode(currency.getCurrencyCode()).build();
                ActivityEntryFee activityEntryFee = activityEntryFeeService.findList(activityEntryFeeQuery).get(0);
                BigDecimal entryFee = I18nConstant.currencyFormat(currency.getCurrencyCode(), activityEntryFee.getEntryFee());
                vo.setActivityEntryFee(entryFee);
            }
            vo.setCurrency(currency);
            vo.setCoverImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
            vo.setTargetRunMileage(activity.getRunMileage().intValue());
            vo.setTargetRunTime(activity.getRunTime());

            List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = listMap.get(activity.getId());
            if (CollectionUtils.isEmpty(znsRunActivityUserEntities)) {
                activityList.add(vo);
                continue;
            }

            Long count = znsRunActivityUserEntities.stream().count();
            vo.setAcceptedNum(count.intValue());
            //获取参与用户
            List<UserSimpleVo> participantList = new ArrayList<>();
            for (ZnsRunActivityUserEntity znsRunActivityUserEntity : znsRunActivityUserEntities) {
                ZnsUserEntity znsUserEntity = userEntityMap.get(znsRunActivityUserEntity.getUserId());
                if (Objects.nonNull(znsUserEntity)) {
                    UserSimpleVo userSimpleVo = new UserSimpleVo(znsUserEntity.getId(), znsUserEntity.getFirstName(), znsUserEntity.getHeadPortrait());
                    participantList.add(userSimpleVo);
                }
            }
            if (participantList.size() > 3) {
                vo.setParticipantList(participantList.subList(0, 3));
            } else {
                vo.setParticipantList(participantList);
            }
            //个人参数设置
            ZnsRunActivityUserEntity oneSelf = znsRunActivityUserEntities.stream().filter(u -> u.getUserId().equals(userId)).findFirst().orElse(null);
            if (Objects.nonNull(oneSelf)) {
                if (Arrays.asList(1, 3, 4).contains(oneSelf.getUserState())) {
                    vo.setIsEnroll(1);
                }
                vo.setUserState(oneSelf.getUserState());
                vo.setUserType(oneSelf.getUserType());
            }
            ZnsRunActivityUserEntity ownerUser = znsRunActivityUserEntities.stream().filter(u -> u.getUserType() == 1).findFirst().orElse(null);
            if (Objects.nonNull(ownerUser)) {
                ZnsUserEntity znsUserEntity = userEntityMap.get(ownerUser.getUserId());
                if (!ownerUser.getUserId().equals(userId)) {
                    if (Objects.nonNull(znsUserEntity)) {
                        vo.setActivityTitle(I18nMsgUtils.getMessage("activity.title.initiatedByUser", znsUserEntity.getFirstName()));
                    } else {
                        vo.setActivityTitle(I18nMsgUtils.getMessage("activity.title.basic"));
                    }
                } else {
                    vo.setActivityTitle(I18nMsgUtils.getMessage("activity.title.initiatedByYou"));
                }
            } else {
                vo.setActivityTitle(I18nMsgUtils.getMessage("activity.title.basic"));
            }
            activityList.add(vo);
        }
        return activityList;
    }

    private Result userRefuse(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        // 变更用户状态为已拒绝
        boolean updateResult = runActivityUserService.updateActivityUserState(activityEntity.getId(), user.getId(), ActivityUserStateEnum.REFUSED);
        if (false == updateResult) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.teamRun.updatedFailed")); //"Failed to update the activity status of the user's rejection of team running"
        }
        // 判断活动是否可以取消
        boolean cancelActivity = runActivityUserService.judgeActivityCanCancel(activityEntity.getId());
        if (cancelActivity) {
            // 变更组队跑活动状态为已取消
            runActivityProcessManager.updateActivityState(activityEntity, ActivityStateEnum.CANCELED, ActivitySubstateEnum.PARTICIPANT_CANCEL);
            // 取消活动退款
            runActivityProcessManager.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
        }
        //判断活动用户是否都结束，如果都结跑步活动状态变更为活动结束
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findAnyActivityUserByState(activityEntity.getId(), Arrays.asList(ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ACCEPT.getState(), ActivityUserStateEnum.NO_REPLY.getState()));
        if (Objects.isNull(activityUser)) {
            // 防止重复提交
            String lockKey = ApiConstants.RUN_ACTIVITY_END + activityEntity.getId();
            RLock lock = redissonClient.getLock(lockKey);
            boolean locked = LockHolder.tryLock(lock, 0, 360);
            if (!locked) {
                log.info("handleActivityFinished 获取锁失败");
            }
            handleRunActivityEnd(activityEntity);
        }
        return CommonResult.success();
    }

    private String getRunMode(Long activityId) {
        //查询已经占用的机器人策略
        List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchByActivityId(activityId);
        if (CollectionUtils.isEmpty(mindUserMatches)) {
            return robotRunModeService.getRobotRunMode().getMode();
        }
        List<String> modeList = mindUserMatches.stream().map(MindUserMatch::getRunMode).collect(Collectors.toList());
        List<RobotRunMode> runModeList = robotRunModeService.findByMode();

//        List<RobotRunMode> runModeList = robotRunModeService.list(new QueryWrapper<RobotRunMode>().select("mode").eq("is_delete", 0).groupBy("mode"));
        Iterator<RobotRunMode> iterator = runModeList.iterator();
        while (iterator.hasNext()) {
            RobotRunMode next = iterator.next();
            if (modeList.contains(next.getMode())) {
                iterator.remove();
            }
        }

        if (CollectionUtils.isEmpty(modeList)) {
            return "";
        }

        Random random = new Random();
        return modeList.get(random.nextInt(modeList.size()));
    }

    private void updateActivityConfig(ZnsRunActivityEntity activity, ZnsUserEntity user, Integer appVersion) {
        //更新奖励规则
        ZnsRunActivityUserEntity activityLaunchUser = runActivityUserService.findActivityLaunchUser(activity.getId());
        ZnsUserEntity launchUser = userService.findById(activityLaunchUser.getUserId());
        //非机器人发起的组队跑奖励规则需要修改,1：机器人，2：聊天机器人
        if (launchUser.getIsRobot() == 0) {
            RunActivityRequest runActivity = new RunActivityRequest();
            runActivity.setRunMileage(activity.getRunMileage());
            if (Objects.nonNull(activity.getRunTime())) {
                runActivity.setRunTime(new BigDecimal(activity.getRunTime()));
            }
            runActivity.setIsRobotStart(0);
            runActivity.setAcceptedUserCount(activity.getUserCount());
            runActivity.setRunningGoalsUnit(activity.getCompleteRuleType());
            runActivity.setAppVersion(appVersion);
            runActivity.setActivityId(activity.getId());

            Map<String, Object> jsonObject = getActivityConfig(runActivity, activity.getActivityConfig(), user.getMeasureUnit(), user.getId(), false);
            //TODO fixme ,确认参数
            activity.setActivityConfig(JsonUtil.writeString(jsonObject));

            // 设置活动规则
            wrapperRunActivityRules(activity, jsonObject, null, runActivity.getAppVersion());
        }
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        if (ActivityStateEnum.FINISHED.equals(activityEntity.getActivityState())
                || ActivityStateEnum.CANCELED.equals(activityEntity.getActivityState())) {
            log.error("组队活动已经结束或者取消,不能接受或者拒绝活动");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.signup.teamRun.misCondition")); //"Does not meet the conditions for participating in team running"
        }
        // 活动状态=未开始 and 被挑战者参与状态=未答复 这两个条件都满足 被邀请者才能接受/拒绝
        // 活动状态=进行中,开跑30分钟内 and 被挑战者参与状态=未答复 这两个条件。被邀请者才能接受/拒绝

        ZonedDateTime activityStartTime = activityEntity.getActivityStartTime();
        ZonedDateTime activityTime = activityStartTime.plusMinutes(30);

        return isNoReply(activityTime, activityEntity.getId(), user.getId());
    }

    @Override
    public BigDecimal calculateTotalBonus(ZnsRunActivityEntity activity, Integer acceptCount, BigDecimal entryFee, Integer challengeRunType, ZnsUserEntity userEntity, Boolean view) {
        BigDecimal totalBonus = super.calculateTotalBonus(activity, acceptCount, entryFee, challengeRunType, userEntity, view);
        if (Objects.nonNull(totalBonus)) {
            return totalBonus;
        }
        if (!view) {
            // 非查看按发起人币种计算总金额
            ZnsRunActivityUserEntity ownerActivityUser = runActivityUserService.findActivityLaunchUser(activity.getId());
            userEntity = znsUserService.findById(ownerActivityUser.getUserId());
        }
        String currencyCode = userAccountService.getUserCurrency(userEntity.getId()).getCurrencyCode();
        BigDecimal totalAward = BigDecimal.ZERO;
        //新奖励配置
        List<AwardConfigDto> awardConfigDtoList = awardConfigBizService.selectAwardConfigDtoListByActivityId(activity.getId(), null, null);
        //奖励规则积分排名
        // 计算公式：发起奖励+参与奖励*(接受人数-1)+完赛奖励*接受人数+第1、2、3名的额外奖励 + 单人保证金 * 已接受人数

        // 获取对应的奖励配置
        BigDecimal participateAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD)), userEntity);
        BigDecimal completeAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.COMPLETE_AWARD)), userEntity);
        BigDecimal launchAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.LAUNCH_AWARD.getType(), getLaunchAward(participateAward, jsonObjectConfig), userEntity);
        boolean isNewConfig = !CollectionUtils.isEmpty(awardConfigDtoList);
        if (isNewConfig) {
            // 2.6版本后都是true
            totalAward = totalAward.add(launchAward);
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
            if (acceptCount < 1) {
                acceptCount = 1;
            }
            totalAward = totalAward.add(participateAward.multiply(new BigDecimal(acceptCount - 1)));
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        } else {
            String INITIATE_REWARDS_MULTIPLE = MapUtil.getString(jsonObjectConfig.get(ApiConstants.INITIATE_REWARDS_MULTIPLE));
            BigDecimal initiateRewardsMultiple = BigDecimal.ONE;
            if (StringUtils.hasText(INITIATE_REWARDS_MULTIPLE)) {
                initiateRewardsMultiple = new BigDecimal(INITIATE_REWARDS_MULTIPLE);
            }
            totalAward = totalAward.add(participateAward.multiply(BigDecimalUtil.add(new BigDecimal(acceptCount), initiateRewardsMultiple)));
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }

        if (Objects.nonNull(completeAward)) {
            totalAward = totalAward.add(completeAward.multiply(acceptCountDecimal));
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }
        Map<String, Object> poolConfig = awardConfigBizService.awardPoolConfig(acceptCount, awardConfigDtoList, false, isNewConfig, userEntity);
        BigDecimal firstAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.FIRST_AWARD)), 1, AwardTypeEnum.AMOUNT.getType());
        BigDecimal secondAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.SECOND_AWARD)), 2, AwardTypeEnum.AMOUNT.getType());
        BigDecimal thirdAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.THIRD_AWARD)), 3, AwardTypeEnum.AMOUNT.getType());
        if (Objects.nonNull(firstAward)) {
            totalAward = totalAward.add(firstAward);
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }
        if (Objects.nonNull(secondAward)) {
            totalAward = totalAward.add(secondAward);
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }
        if (Objects.nonNull(thirdAward)) {
            totalAward = totalAward.add(thirdAward);
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }
        if (activity.getBonusRuleType() != 1) {
            totalAward = totalAward.add(entryFee.multiply(acceptCountDecimal));
            totalAward = I18nConstant.currencyFormat(currencyCode, totalAward);
        }
        return totalAward;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        // 组队跑:如果活动参与的人数>=2,则活动变成进行中,否则活动变成取消状态
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId())
                .userStateIn(List.of(ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState(), ActivityUserStateEnum.ACCEPT.getState()))
                .build();
        long userCount = runActivityUserService.findCount(userQuery);

        if (userCount < 2) {
            activityEntity.setActivityState(ActivityStateEnum.CANCELED.getState());
            activityEntity.setSubState(ActivitySubstateEnum.TIMED_TASK_CANCEL.getState());
            // 取消活动退款
            runActivityProcessManager.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.SECURITY_FUND);
            activityEntity.setModifieTime(ZonedDateTime.now());
            runActivityService.updateById(activityEntity);
            log.info("checkReportUserRun取消活动，活动id：" + activityEntity.getId());
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.cancelled"));
        }

        //活动开始30分钟后不可操作
        // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
        if (null == lastEnterMinutes) {
            lastEnterMinutes = 30;
        }

        ZonedDateTime date = activityEntity.getActivityStartTime().plusMinutes(lastEnterMinutes);
        if (ZonedDateTime.now().compareTo(date) > 0) {
            log.info("checkReportUserRun fail,error msg:{},activityStartTime:{},currentDate:{}", "You are not allowed to enter after " + lastEnterMinutes + " min of running", activityEntity.getActivityStartTime(), ZonedDateTime.now());
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enter.timeOverDue", lastEnterMinutes)); //"You are not allowed to enter after "+lastEnterMinutes+" min of running"
        }
        return CommonResult.success();
    }

    @Override
    @Transactional
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        super.handleRunActivityEnd(activityEntity);

        //查询所有已接受、结束、进行中的用户
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).userStateIn(Arrays.asList(1, 3, 4)).activityId(activityEntity.getId())
                .build();
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> userIds = list.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        List<ZnsUserEntity> userEntities = userService.findByIds(userIds);
        Map<Long, String> userMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, ZnsUserEntity::getFirstName));
        for (ZnsRunActivityUserEntity znsRunActivityUserEntity : list) {
            String firstName = userMap.get(znsRunActivityUserEntity.getUserId());
            if (StringUtils.hasText(firstName)) {
                znsRunActivityUserEntity.setNickname(firstName);
            }
            if (Objects.isNull(znsRunActivityUserEntity.getRunTimeMillisecond()) || znsRunActivityUserEntity.getRunTimeMillisecond() == 0) {
                znsRunActivityUserEntity.setRunTimeMillisecond(znsRunActivityUserEntity.getRunTime() * 1000 + 999);
            }
        }

        List<ZnsRunActivityUserEntity> completeUsers = new ArrayList<>();
        List<ZnsRunActivityUserEntity> noCompleteUsers = new ArrayList<>();
        for (ZnsRunActivityUserEntity userEntity : list) {
            if (userEntity.getTargetRunMileage() > 0 && userEntity.getRunMileage().intValue() >= activityEntity.getRunMileage().intValue()) {
                userEntity.setIsComplete(1);
                userEntity.setCompleteTime(ZonedDateTime.now());
                userEntity.setSubState(1);
                completeUsers.add(userEntity);
            } else if (userEntity.getTargetRunTime() > 0 && userEntity.getRunTime().intValue() >= activityEntity.getRunTime().intValue()) {
                userEntity.setIsComplete(1);
                userEntity.setCompleteTime(ZonedDateTime.now());
                userEntity.setSubState(1);
                completeUsers.add(userEntity);
            } else {
                userEntity.setSubState(2);
                noCompleteUsers.add(userEntity);
            }
        }

        if (StringUtil.isEmpty(activityEntity.getActivityConfig())) {
            log.warn("ActivityConfig为空奖励发放失败");
            return;
        }
        Map<String, Object> jsonObject = new HashMap<>();
        try {
            jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        } catch (Exception e) {
            log.info("json解析异常 activityId = " + activityEntity.getId() + ", actvityconfig = " + activityEntity.getActivityConfig());
        }
        //新奖励配置
        List<AwardConfigDto> awardConfigDtoList = awardConfigBizService.selectAwardConfigDtoListByActivityId(activityEntity.getId(), null, null);
        boolean isNewConfig = !CollectionUtils.isEmpty(awardConfigDtoList);
        //奖励规则积分排名
        // 获取对应的奖励配置

        Integer limitPeopleSize = getLimitPeopleSize();
        //抽佣比例
        BigDecimal priceProportion = BigDecimal.ZERO;
        if (Objects.nonNull(jsonObject.get("priceProportion")) && StringUtils.hasText(jsonObject.get("priceProportion").toString())) {
            priceProportion = new BigDecimal(jsonObject.get("priceProportion").toString());
            log.info("组队跑活动={}设置佣金比例={}", activityEntity.getId(), priceProportion);
        }

        //可给用户分配的剩余比例
        BigDecimal surplus = BigDecimal.ONE.subtract(priceProportion);
        //费用参与不退回(补充：抽佣)
        BigDecimal userFees = activityEntity.getBonusRuleType() == 2 ?
                BigDecimalUtil.mul(new BigDecimal(list.size()), activityEntity.getActivityEntryFee(), surplus) :
                BigDecimal.ZERO;
        List<ActivityEntryFee> entryFees = activityEntryFeeService.findByActivityId(activityEntity.getId());
        if (!CollectionUtils.isEmpty(entryFees)) {
            BigDecimal entryFeeUSD = entryFees.stream().filter(s -> s.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())).findFirst().get().getEntryFee();
            userFees = entryFeeUSD.multiply(new BigDecimal(list.size())).multiply(surplus).setScale(4, RoundingMode.UP);
        }

        //瓜分的金额，先转成美元
        BigDecimal avePartitionAmount = completeUsers.size() > 0 ? userFees.divide(new BigDecimal(completeUsers.size()), 4, RoundingMode.UP) : BigDecimal.ZERO;
        log.info("组队跑活动={},平均瓜分美元金额={}", activityEntity.getId(), avePartitionAmount);
        if (!CollectionUtils.isEmpty(completeUsers)) {
            if (activityEntity.getCompleteRuleType() == 1) {
                completeUsers = completeUsers.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond)
                        .thenComparing(ZnsRunActivityUserEntity::getCompleteTime)).collect(Collectors.toList());
            } else {
                completeUsers = completeUsers.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()
                        .thenComparing(ZnsRunActivityUserEntity::getCompleteTime)).collect(Collectors.toList());
            }

            //排名并列处理
            Integer previousRank = 1;
            Integer previousGoal = 0;
            ZonedDateTime previousCompleteTime = completeUsers.get(0).getCompleteTime();
            for (int i = 0; i < completeUsers.size(); i++) {
                ZnsRunActivityUserEntity activityUser = completeUsers.get(i);
                ZnsUserEntity znsUserEntity = userService.findById(activityUser.getUserId());
                String currencyCode = userAccountService.getUserAccount(znsUserEntity.getId()).getCurrencyCode();
                Map<String, Object> poolConfig = awardConfigBizService.awardPoolConfig(list.size(), awardConfigDtoList, true, isNewConfig, znsUserEntity);
                BigDecimal participateAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), MapUtil.getBigDecimal(jsonObject.get(ApiConstants.PARTICIPATE_AWARD)), znsUserEntity);
                BigDecimal completeAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), MapUtil.getBigDecimal(jsonObject.get(ApiConstants.COMPLETE_AWARD)), znsUserEntity);
                BigDecimal launchAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.LAUNCH_AWARD.getType(), getLaunchAward(participateAward, jsonObject), znsUserEntity);
                if (null == participateAward || null == completeAward) {
                    log.error("组队跑活动配置参与奖励和完成奖励不存在:activityId=" + activityEntity.getId());
                    return;
                }
                BigDecimal rankAward = BigDecimal.ZERO;
                BigDecimal totalBonus = BigDecimal.ZERO;
                activityUser.setRank(i + 1);
                BigDecimal divideAmount = activityAwardCurrencyBizService.exchangeNewAmountByUser(activityUser.getUserId(), avePartitionAmount, activityEntity.getId());
                divideAmount = I18nConstant.currencyFormat(currencyCode, divideAmount);
                totalBonus = totalBonus.add(divideAmount);
                // 发起者可获得N参与补贴
                if (1 == activityUser.getUserType()) {
                    launchAward = I18nConstant.currencyFormat(currencyCode, launchAward);
                    totalBonus = totalBonus.add(launchAward);
                } else {
                    participateAward = I18nConstant.currencyFormat(currencyCode, participateAward);
                    totalBonus = totalBonus.add(participateAward);

                }

                if (activityUser.getRunTimeMillisecond().equals(previousGoal) && activityUser.getCompleteTime().compareTo(previousCompleteTime) == 0) {
                    activityUser.setRank(previousRank);
                }
                previousGoal = activityUser.getRunTimeMillisecond();
                previousRank = activityUser.getRank();
                previousCompleteTime = activityUser.getCompleteTime();

                // 给每个完成目标这发放奖金
                if (activityUser.getIsCheat() == 0) {
                    completeAward = I18nConstant.currencyFormat(currencyCode, completeAward);
                    totalBonus = totalBonus.add(completeAward);
                    log.info("非官方同跑id:{},divideAmount:{},launchAward:{},participateAward:{},completeAward:{}", activityEntity.getId(), divideAmount, launchAward, participateAward, completeAward);
                    //排名奖励
                    if (list.size() >= limitPeopleSize) {
                        if (activityUser.getRank() == 1) {
                            BigDecimal firstAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObject.get(ApiConstants.FIRST_AWARD)), 1, AwardTypeEnum.AMOUNT.getType());
                            log.info("非官方同跑id:{},firstAward:{}", activityEntity.getId(), firstAward);
                            firstAward = Objects.isNull(firstAward) ? BigDecimal.ZERO : firstAward;
                            firstAward = I18nConstant.currencyFormat(currencyCode, firstAward);
                            totalBonus = totalBonus.add(firstAward);
                            rankAward = firstAward;
                        } else if (activityUser.getRank() == 2) {
                            BigDecimal secondAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObject.get(ApiConstants.SECOND_AWARD)), 2, AwardTypeEnum.AMOUNT.getType());
                            log.info("非官方同跑id:{},secondAward:{}", activityEntity.getId(), secondAward);
                            secondAward = Objects.isNull(secondAward) ? BigDecimal.ZERO : secondAward;
                            secondAward = I18nConstant.currencyFormat(currencyCode, secondAward);
                            totalBonus = totalBonus.add(secondAward);
                            rankAward = secondAward;
                        } else if (activityUser.getRank() == 3) {
                            BigDecimal thirdAward = awardConfigBizService.selectAwardRankAmount(poolConfig, MapUtil.getBigDecimal(jsonObject.get(ApiConstants.THIRD_AWARD)), 3, AwardTypeEnum.AMOUNT.getType());
                            log.info("非官方同跑id:{},thirdAward:{}", activityEntity.getId(), thirdAward);
                            thirdAward = Objects.isNull(thirdAward) ? BigDecimal.ZERO : thirdAward;
                            thirdAward = I18nConstant.currencyFormat(currencyCode, thirdAward);
                            totalBonus = totalBonus.add(thirdAward);
                            rankAward = thirdAward;
                        }
                    }
                }

                // 计算好奖励金额给未完成用户发放奖励，并且添加对应账户明细
                if (!runActivityService.checkNewActivity(activityEntity.getId()) && !currencyCode.equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
                    // 老活动有加拿大人参加 或者 老活动参与人在发奖之前有币种转换
                    totalBonus = I18nConstant.currencyFormat(currencyCode, totalBonus);
                    activityUser.setRunAward(totalBonus);
                    BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
                    totalBonus = totalBonus.multiply(nowRate).setScale(2, RoundingMode.UP);
                } else {
                    totalBonus = I18nConstant.currencyFormat(currencyCode, totalBonus);
                    activityUser.setRunAward(totalBonus);
                }
                // 更新活动用户信息
                activityUser.setSubState(1);
                activityUser.setIsComplete(1);
                activityUser.setCompleteTime(ZonedDateTime.now());

                //修改币种切换处理
                List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityUser.getActivityId().toString())) {
                    log.info("币种切换不发放");
                } else {
                    Long detailId = handleRunAward(totalBonus, activityUser, activityEntity, BigDecimal.ZERO, null);
                    activityUser.setRewardTime(ZonedDateTime.now());

                    //奖励明细分类处理
                    List<UserAccountDetailSub> accountDetailSubs = new ArrayList<>();
                    userAccountDetailSubService.addDetailSubToList(accountDetailSubs, activityUser.getUserId(), detailId, activityUser.getActivityId(), 1, divideAmount, BigDecimal.ZERO);
                    userAccountDetailSubService.addDetailSubToList(accountDetailSubs, activityUser.getUserId(), detailId, activityUser.getActivityId(), 2, participateAward, BigDecimal.ZERO);
                    if (1 == activityUser.getUserType()) {
                        userAccountDetailSubService.addDetailSubToList(accountDetailSubs, activityUser.getUserId(), detailId, activityUser.getActivityId(), 3, participateAward, BigDecimal.ZERO);
                    }
                    ZnsUserEntity user = znsUserService.findById(activityUser.getUserId());
                    userAccountDetailSubService.addDetailSubToList(accountDetailSubs, user, detailId, activityUser.getActivityId(), 4, rankAward, activityUser.getRank(), BigDecimal.ZERO);
                    userAccountDetailSubService.addDetailSubToList(accountDetailSubs, activityUser.getUserId(), detailId, activityUser.getActivityId(), 5, completeAward, BigDecimal.ZERO);
                    if (!CollectionUtils.isEmpty(accountDetailSubs)) {
                        userAccountDetailSubService.saveBatch(accountDetailSubs);
                    }
                }


                if (activityUser.getUserType() == 1) {
                    rewardDistribution(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.LAUNCH_AWARD.getType()));
                    //积分一起发放
                    rewardDistributionScore(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.LAUNCH_AWARD.getType(), AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType()), poolConfig);
                } else {
                    rewardDistribution(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType()));
                    //积分一起发放
                    rewardDistributionScore(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), AwardSentTypeEnum.PERCENTAGE_OF_BONUS_POOL.getType()), poolConfig);
                }
                rewardDistribution(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.COMPLETING_THE_GAME.getType()));
                if (activityUser.getRank() <= 3 && 1 <= activityUser.getRank()) {
                    handleSendRankPush(activityUser, activityEntity);
                }
            }
            runActivityUserService.updateBatchById(completeUsers);
        }

        if (!CollectionUtils.isEmpty(noCompleteUsers)) {
            for (ZnsRunActivityUserEntity activityUser : noCompleteUsers) {
                ZnsUserEntity znsUserEntity = userService.findById(activityUser.getUserId());
                String currencyCode = userAccountService.getUserAccount(znsUserEntity.getId()).getCurrencyCode();
                BigDecimal participateAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), MapUtil.getBigDecimal(jsonObject.get(ApiConstants.PARTICIPATE_AWARD)), znsUserEntity);
                BigDecimal launchAward = awardActivityManager.selectAwardAmount(awardConfigDtoList, AwardSentTypeEnum.LAUNCH_AWARD.getType(), getLaunchAward(participateAward, jsonObject), znsUserEntity);
                Map<String, Object> poolConfig = awardConfigBizService.awardPoolConfig(list.size(), awardConfigDtoList, true, isNewConfig, znsUserEntity);
                // 给每个未完成目标这发放奖金
                BigDecimal totalBonus = BigDecimal.ZERO;
                // 判断用户是否跑过
                boolean userDidRun = userRunDataDetailsService.verifyUserDidRun(activityUser.getRunDataDetailsId(), activityUser.getUserId());
                // 发起者可获得参与参与补贴
                if (userDidRun) {
                    // 发起者可获得双倍参与补贴
                    if (1 == activityUser.getUserType()) {
                        launchAward = I18nConstant.currencyFormat(currencyCode, launchAward);
                        totalBonus = totalBonus.add(launchAward);
                    } else {
                        participateAward = I18nConstant.currencyFormat(currencyCode, participateAward);
                        totalBonus = totalBonus.add(participateAward);
                    }
                }
                // 计算好奖励金额给未完成用户发放奖励，并且添加对应账户明细
                if (!runActivityService.checkNewActivity(activityEntity.getId()) && !currencyCode.equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
                    // 老活动有加拿大人参加 或者 老活动参与人在发奖之前有币种转换
                    if (Integer.valueOf(0).equals(activityUser.getIsCheat())) {
                        totalBonus = I18nConstant.currencyFormat(currencyCode, totalBonus);
                        activityUser.setRunAward(totalBonus);
                    }
                    BigDecimal nowRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currencyCode).getExchangeRate();
                    totalBonus = totalBonus.multiply(nowRate).setScale(2, RoundingMode.UP);
                } else {
                    if (Integer.valueOf(0).equals(activityUser.getIsCheat())) {
                        totalBonus = I18nConstant.currencyFormat(currencyCode, totalBonus);
                        activityUser.setRunAward(totalBonus);
                    }
                }
                //修改币种切换处理
                List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityUser.getActivityId().toString())) {
                    log.info("币种切换不发放");
                } else {
                    Long detailId = handleRunAward(totalBonus, activityUser, activityEntity, BigDecimal.ZERO, null);
                    activityUser.setRewardTime(ZonedDateTime.now());
                    ZnsUserEntity user = znsUserService.findById(activityUser.getUserId());
                    //奖励明细分类处理
                    userAccountDetailSubService.addDetailSub(user, detailId, activityUser.getActivityId(), 1, avePartitionAmount);
                    if (userDidRun) {
                        userAccountDetailSubService.addDetailSub(user, detailId, activityUser.getActivityId(), 2, participateAward);
                        // 发起者可获得双倍参与补贴
                        if (1 == activityUser.getUserType()) {
                            userAccountDetailSubService.addDetailSub(user, detailId, activityUser.getActivityId(), 3, participateAward);
                        }
                        if (activityUser.getUserType() == 1) {
                            rewardDistribution(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.LAUNCH_AWARD.getType()));
                            //积分一起发放
                            rewardDistributionScore(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.LAUNCH_AWARD.getType()), poolConfig);
                        } else {
                            //积分一起发放
                            rewardDistributionScore(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType()), poolConfig);
                            rewardDistribution(awardConfigDtoList, activityUser, Arrays.asList(AwardSentTypeEnum.PARTICIPATION_AWARD.getType()));
                        }
                    }
                }
                // 更新活动用户信息
                boolean verifyUserDidRun = userRunDataDetailsService.verifyUserDidRun(activityUser.getRunDataDetailsId(), activityUser.getUserId());
                if (verifyUserDidRun) {
                    activityUser.setSubState(2);
                }
                activityUser.setIsComplete(0);
            }
            runActivityUserService.updateBatchById(noCompleteUsers);
        }
        super.handleRunActivityData(activityEntity);
    }

    private BigDecimal getLaunchAward(BigDecimal participateAward, Map<String, Object> jsonObject) {
        // 3.0版本兜底值为0
        return BigDecimal.ZERO;
    }


    public Integer getLimitPeopleSize() {
        //金额处理
        AwardLimitRule awardLimitRule = awardLimitRuleService.selectAwardLimitRule(1, null, 1, AwardTypeEnum.AMOUNT.getType());
        Integer limitPeople = null;
        if (Objects.nonNull(awardLimitRule) && Objects.nonNull(awardLimitRule.getLimitPeople())) {
            limitPeople = awardLimitRule.getLimitPeople();
        }
        if (Objects.nonNull(limitPeople)) {
            return limitPeople;
        }

        String limitCount = sysConfigService.selectConfigByKey("team.activity.people.num.limit");
        Integer limitPeopleSize = 5;
        if (StringUtils.hasText(limitCount)) {
            limitPeopleSize = Integer.parseInt(limitCount);
        }
        return limitPeopleSize;
    }

    /**
     * 奖励发放
     *
     * @param awardAmount
     * @param activityUser
     * @param activityEntity
     * @param extraAward
     * @param subType
     */
    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        if (null == awardAmount || awardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("组队跑奖励金额为零");
            return null;
        }
        // 给用户余额发送奖励
        userAccountService.increaseAmount(awardAmount, activityUser.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        Long detail = userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.TEAM_RUN_AWARD,
                AccountDetailSubtypeEnum.TEAM_RUM.getType(), 1, awardAmount, billNo, tradeTime, activityUser.getActivityId(),
                activityEntity.getId(), null, activityEntity.getActivityType(), 0L, "", null, null, null, BigDecimal.ZERO);
        if (!Objects.equals(activityUser.getIsCheat(), 1)) {
            //通知
            ActivityNotificationEnum activityNotification = ActivityNotificationEnum.WARD_TEAM_RUN;
            MessageBo message = appMessageService.assembleMessage(activityEntity.getId(), I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.WARD_TEAM_RUN"), billNo,
                    NoticeTypeEnum.REWARD_NOTICE.getType());
            message.setActivityId(activityEntity.getId());
            ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, activityNotification.getNotificationContent());
            appMessageService.sendImAndPushUserIds(Arrays.asList(activityUser.getUserId()), imMessageBo, message);
        }

        return detail;
    }

    /**
     * 多人同跑运动报告
     *
     * @param detail
     * @param loginUser
     * @param activityEntity
     * @param zoneId
     * @return
     */
    @Override
    public MultipleRunReportVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        MultipleRunReportVo multipleRunReportVo = new MultipleRunReportVo();
        Integer activityState = null;
        ZonedDateTime date = DateTimeUtil.parse("2022-11-28 04:00:00");
        if (Objects.nonNull(activityEntity.getActivityEndTime()) && activityEntity.getActivityEndTime().compareTo(date) > 0) {
            activityState = activityEntity.getActivityState();
        }
        List<TeamRunRunningReportListVO> teamRunRunningReport = userRunDataDetailsDao.getTeamRunRunningReport(detail.getId(), null, null, activityState);
        if (CollectionUtils.isEmpty(teamRunRunningReport)) {
            return multipleRunReportVo;
        }

        //查询自己
        TeamRunRunningReportListVO teamRunRunningReportListVO = teamRunRunningReport.stream().filter(r -> r.getUserId().equals(loginUser.getId())).findFirst().orElse(null);
        Currency currency = new Currency();
        currency.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
        currency.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
        MyGradesDto myGradesDto = new MyGradesDto();
        if (Objects.nonNull(teamRunRunningReportListVO)) {
            fillNewCurrency(loginUser, teamRunRunningReportListVO, currency);
            myGradesDto.setRank(teamRunRunningReportListVO.getRank());
            myGradesDto.setRunAward(teamRunRunningReportListVO.getRunAward());
        } else {
            myGradesDto.setRank(-1);
            myGradesDto.setRunAward(BigDecimal.ZERO);
        }
        teamRunRunningReportListVO.setCurrency(currency);
        myGradesDto.setCurrency(currency);
        multipleRunReportVo.setMyGrades(myGradesDto);


        //查询好友关系
        List<ZnsUserFriendEntity> friendList = userFriendService.getFriendList(loginUser.getId(), teamRunRunningReport.stream().map(TeamRunRunningReportListVO::getUserId).collect(Collectors.toList()));
        Map<Long, ZnsUserFriendEntity> friendEntityMap = friendList.stream().collect(Collectors.toMap(ZnsUserFriendEntity::getFriendId, Function.identity()));
        for (int i = 0; i < teamRunRunningReport.size(); i++) {
            TeamRunRunningReportListVO vo = teamRunRunningReport.get(i);
            if (vo.getRank() <= 0) {
                vo.setRank(i + 1);
            }
            if (Objects.nonNull(friendEntityMap.get(vo.getUserId()))) {
                vo.setIsFriend(1);
            } else {
                vo.setIsFriend(0);
            }
        }
        multipleRunReportVo.setList(teamRunRunningReport);

        // 是否可以一键关注
        List<ZnsUserFriendEntity> myfriendList = userFriendService.getMyFriendList(loginUser.getId(), teamRunRunningReport.stream().map(TeamRunRunningReportListVO::getUserId).collect(Collectors.toList()));
        Map<Long, ZnsUserFriendEntity> myfriendEntityMap = myfriendList.stream().collect(Collectors.toMap(ZnsUserFriendEntity::getFriendId, Function.identity(), (x, y) -> x));
        int canNoticeAll = 0;
        for (int i = 0; i < teamRunRunningReport.size(); i++) {
            TeamRunRunningReportListVO vo = teamRunRunningReport.get(i);
            //排除自己
            if (vo.getUserId().equals(loginUser.getId())) {
                continue;
            }
            if (Objects.nonNull(myfriendEntityMap.get(vo.getUserId()))) {
            } else {
                canNoticeAll = 1;
            }
        }
        multipleRunReportVo.setCanNoticeAll(canNoticeAll);

        //最佳配速
        TeamRunRunningReportListVO pace = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getAveragePace()) && r.getAveragePace() > 0)
                .min(Comparator.comparing(TeamRunRunningReportListVO::getAveragePace)).orElse(new TeamRunRunningReportListVO());
        multipleRunReportVo.setBestPaceUser(new UserSimpleVo(pace.getUserId(), pace.getNickname(), pace.getHeadPortrait()));

        //心率控制最佳
        TeamRunRunningReportListVO heartRate = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getAverageHeartRate()) && r.getAverageHeartRate() > 0)
                .max((x, y) -> {
                    BigDecimal r1 = BigDecimalUtil.divide(new BigDecimal(x.getAverageHeartRate()), new BigDecimal(SportsDataUnit.getUserMaxHeart(x.getBirthday())));
                    BigDecimal r2 = BigDecimalUtil.divide(new BigDecimal(y.getAverageHeartRate()), new BigDecimal(SportsDataUnit.getUserMaxHeart(y.getBirthday())));
                    return r1.compareTo(r2);
                }).orElse(new TeamRunRunningReportListVO());
        multipleRunReportVo.setBestHeartRateUser(new UserSimpleVo(heartRate.getUserId(), heartRate.getNickname(), heartRate.getHeadPortrait()));

        //卡路里消耗最佳
        TeamRunRunningReportListVO calorie = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getKilocalorie()) && r.getKilocalorie().compareTo(BigDecimal.ZERO) > 0)
                .max(Comparator.comparing(TeamRunRunningReportListVO::getKilocalorie, BigDecimal::compareTo)).orElse(new TeamRunRunningReportListVO());
        multipleRunReportVo.setBestCalorieUser(new UserSimpleVo(calorie.getUserId(), calorie.getNickname(), calorie.getHeadPortrait()));

        //跑力值最佳
        TeamRunRunningReportListVO capabilityValue = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getCapabilityValue()) && r.getCapabilityValue().compareTo(BigDecimal.ZERO) > 0)
                .max(Comparator.comparing(TeamRunRunningReportListVO::getCapabilityValue, BigDecimal::compareTo)).orElse(new TeamRunRunningReportListVO());
        multipleRunReportVo.setBestCapabilityValueUser(new UserSimpleVo(capabilityValue.getUserId(), capabilityValue.getNickname(), capabilityValue.getHeadPortrait()));

        //人气最佳
        Map<Long, Long> inviteMap = teamRunRunningReport.stream().filter(r -> Objects.nonNull(r.getInviterUserId()) && r.getInviterUserId() > 0).collect(Collectors.groupingBy(TeamRunRunningReportListVO::getInviterUserId, Collectors.counting()));
        Optional<Map.Entry<Long, Long>> popularityUserMap = inviteMap.entrySet().stream().filter(e -> e.getValue() > 0).max(Map.Entry.comparingByValue());
        if (popularityUserMap.isPresent()) {
            Long userId = popularityUserMap.get().getKey();
            TeamRunRunningReportListVO popularity = teamRunRunningReport.stream().filter(r -> r.getUserId().equals(userId)).findFirst().orElse(null);
            if (Objects.nonNull(popularity)) {
                multipleRunReportVo.setBestPopularityUser(new UserSimpleVo(popularity.getUserId(), popularity.getNickname(), popularity.getHeadPortrait()));
            }
        }

        //多人同跑运动明细数据
        dealTeamRunDetail(teamRunRunningReport, activityEntity.getActivityType(), activityEntity.getId());

        return multipleRunReportVo;
    }

    private void fillNewCurrency(ZnsUserEntity loginUser, TeamRunRunningReportListVO teamRunRunningReportListVO, Currency currency) {
        Long activityId = teamRunRunningReportListVO.getActivityId();
        Long userId = loginUser.getId();
        activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, activityId, userId);
    }


    @Override
    public void wrapperActivityRewardDetailByActivityType(ZnsRunActivityEntity activityEntity, Map<String, Object> jsonObjectConfig, RunActivityRewardDetailVO runActivityRewardDetailVO, ZnsUserEntity loginUser) {
        List<AwardConfigDto> awardConfigDtoList = awardConfigBizService.selectAwardConfigDtoListByActivityId(activityEntity.getId(), null, null);
        Integer acceptedUserCount = runActivityUserService.countRunUser(activityEntity.getId(), Arrays.asList(1, 3, 4), null, null);
        Currency currency = userAccountService.getCurrency(loginUser.getId(), activityEntity.getId(), true);
        String currencyCode = currency.getCurrencyCode();
        //没有数据表示历史数据，数据兼容
        if (CollectionUtils.isEmpty(awardConfigDtoList)) {
            String participateAwardStr = MapUtil.getString(jsonObjectConfig.get(ApiConstants.PARTICIPATE_AWARD));          // 参与金额
            String completeAwardStr = MapUtil.getString(jsonObjectConfig.get(ApiConstants.COMPLETE_AWARD));
            BigDecimal participateAward = new BigDecimal(participateAwardStr);
            participateAward = I18nConstant.currencyFormat(currencyCode, participateAward);
            try {
                BigDecimal completeAward = new BigDecimal(completeAwardStr);
                completeAward = I18nConstant.currencyFormat(currencyCode, completeAward);
                completeAwardStr = completeAward.toString();
            } catch (Exception e) {
                log.error("金额转换异常", e);
            }

            List<RunActivityRewardConfigDetails> runActivityRewardConfigDetails = new ArrayList<>();
            RunActivityRewardConfigDetails participateConfig = new RunActivityRewardConfigDetails();
            participateConfig.setAmount(participateAward);
            participateConfig.setSendType(AwardSentTypeEnum.PARTICIPATION_AWARD.getType());
            runActivityRewardConfigDetails.add(participateConfig);

            RunActivityRewardConfigDetails launchConfig = new RunActivityRewardConfigDetails();
            launchConfig.setAmount(BigDecimalUtil.multiply(participateAward, new BigDecimal(2)));
            launchConfig.setSendType(AwardSentTypeEnum.LAUNCH_AWARD.getType());
            runActivityRewardConfigDetails.add(launchConfig);
            runActivityRewardDetailVO.setAwardConfigDetailsDtoList(runActivityRewardConfigDetails);


            //奖励规则明细-完赛/名次 这种有目标的
            List<Map> goalDetailVOList = new ArrayList<>();
            Map<String, Object> vo = new HashMap<>();

            if (activityEntity.getCompleteRuleType() == 1) {
                vo.put("goal", activityEntity.getRunMileage());
            } else {
                vo.put("goal", new BigDecimal(activityEntity.getRunTime()));
            }
            vo.put("award", completeAwardStr);

            BigDecimal firstAward = I18nConstant.currencyFormat(currencyCode, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.FIRST_AWARD)));
            BigDecimal secondAward = I18nConstant.currencyFormat(currencyCode, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.SECOND_AWARD)));
            BigDecimal thirdAward = I18nConstant.currencyFormat(currencyCode, MapUtil.getBigDecimal(jsonObjectConfig.get(ApiConstants.THIRD_AWARD)));
            if (Objects.nonNull(firstAward)) {
                vo.put("firstAward", firstAward.toString());
            }
            if (Objects.nonNull(secondAward)) {
                vo.put("secondAward", secondAward.toString());
            }
            if (Objects.nonNull(thirdAward)) {
                vo.put("thirdAward", thirdAward.toString());
            }

            goalDetailVOList.add(vo);
            runActivityRewardDetailVO.setRunningGoalsAward(goalDetailVOList);
        } else {
            // 奖励规则明细
            List<AwardConfigDto> awardConfigDetailsDtoList1 = awardConfigDtoList.stream().filter(a -> a.getSendType().equals(AwardSentTypeEnum.PARTICIPATION_AWARD.getType()) || a.getSendType().equals(AwardSentTypeEnum.LAUNCH_AWARD.getType())).collect(Collectors.toList());
            List<RunActivityRewardConfigDetails> runActivityRewardConfigDetails = awardConfigBizService.assembleRunActivityRewardConfigDetails(awardConfigDetailsDtoList1, loginUser);
            runActivityRewardDetailVO.setAwardConfigDetailsDtoList(runActivityRewardConfigDetails);

            //奖励规则金额排名
            Map<String, Object> poolConfig = awardConfigBizService.awardPoolConfig(acceptedUserCount, awardConfigDtoList, false, true, loginUser);
            runActivityRewardDetailVO.setRunningGoalsAward(getNewRunningGoalsAward(activityEntity, AwardTypeEnum.AMOUNT.getType(), poolConfig, awardConfigDtoList, loginUser));
            runActivityRewardDetailVO.setCurrency(I18nConstant.buildDefaultCurrency());
            if (Objects.nonNull(loginUser)) {
                runActivityRewardDetailVO.setCurrency(I18nConstant.buildCurrency(currency.getCurrencyCode()));
            }
            runActivityRewardDetailVO.setRunningGoalScoreAward(getNewRunningGoalsAward(activityEntity, AwardTypeEnum.SCORE.getType(), poolConfig, awardConfigDtoList, loginUser));
            runActivityRewardDetailVO.setRunningGoalCouponAward(getNewRunningGoalsAward(activityEntity, AwardTypeEnum.COUPON.getType(), poolConfig, awardConfigDtoList, loginUser));
        }
    }

    private List<Map> getNewRunningGoalsAward(ZnsRunActivityEntity activityEntity, Integer awardType, Map<String, Object> poolConfig, List<AwardConfigDto> awardConfigDtoList, ZnsUserEntity loginUser) {
        List<Map> runningGoalsAward = new ArrayList<>();
        Map<String, Object> vo = new HashMap<>();
        if (activityEntity.getCompleteRuleType() == 1) {
            vo.put("goal", activityEntity.getRunMileage());
        } else {
            vo.put("goal", new BigDecimal(activityEntity.getRunTime()));
        }
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(loginUser.getId());
        //完赛奖励
        AwardConfigDto completeAward = awardConfigDtoList.stream().filter(a -> a.getAwardType().equals(awardType) && a.getSendType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())).findFirst().orElse(null);
        if (Objects.nonNull(completeAward)) {
            if (awardType == 1) {
                CurrencyAmount currencyAmount = awardConfigBizService.assembleCurrencyAmount(loginUser, completeAward);
                vo.put("award", toAwardStr(currencyAmount.getAmount()));
            } else if (awardType == 3) {
                vo.put("award", toAwardStr(completeAward.getScore()));
            } else if (awardType == 2) {
                String couponIds = completeAward.getCouponIds();
                if (StringUtils.hasText(couponIds)) {
                    List<Long> ids = NumberUtils.stringToLong2(couponIds);
                    Coupon coupon = couponService.selectCouponById(ids.get(0));
                    Map<String, Object> map = new HashMap<>();
                    map.put("couponId", coupon.getId());
                    map.put("num", 1);
                    map.put("couponName", coupon.getTitle());
                    vo.put("award", map);
                }
            }
        }
        if (awardType.equals(AwardTypeEnum.AMOUNT.getType()) && !poolConfig.isEmpty()) {
            String amountRank1 = poolConfig.get("amountRank1").toString();
            String amountRank2 = poolConfig.get("amountRank2").toString();
            String amountRank3 = poolConfig.get("amountRank3").toString();
            try {
                BigDecimal amount1 = new BigDecimal(amountRank1);
                amountRank1 = Objects.requireNonNull(I18nConstant.currencyFormat(userAccount.getCurrencyCode(), amount1)).toString();
                BigDecimal amount2 = new BigDecimal(amountRank2);
                amountRank2 = Objects.requireNonNull(I18nConstant.currencyFormat(userAccount.getCurrencyCode(), amount2)).toString();
                BigDecimal amount3 = new BigDecimal(amountRank3);
                amountRank3 = Objects.requireNonNull(I18nConstant.currencyFormat(userAccount.getCurrencyCode(), amount3)).toString();
            } catch (Exception e) {
                log.error("金额转换异常", e);
            }
            vo.put("firstAward", toAwardStr(amountRank1));
            vo.put("secondAward", toAwardStr(amountRank2));
            vo.put("thirdAward", toAwardStr(amountRank3));
        } else if (awardType.equals(AwardTypeEnum.SCORE.getType()) && !poolConfig.isEmpty()) {
            vo.put("firstAward", toAwardStr(poolConfig.get("scoreRank1")));
            vo.put("secondAward", toAwardStr(poolConfig.get("scoreRank2")));
            vo.put("thirdAward", toAwardStr(poolConfig.get("scoreRank3")));
        }
        runningGoalsAward.add(vo);

        return runningGoalsAward;
    }

    private String toAwardStr(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }

        return value.toString();
    }
}
