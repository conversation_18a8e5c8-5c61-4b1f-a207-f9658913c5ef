package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*每天日期表
 *
 * <AUTHOR>
 * @since 2022-11-09
 */

@Data
@NoArgsConstructor
@TableName("zns_every_day")
public class EveryDay implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //每天日期，便 于统计
    private ZonedDateTime gmtDay;
    //第天结束时间
    private ZonedDateTime gmtEndDay;

    @Override
    public String toString() {
        return "EveryDay{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",gmtDay=" + gmtDay +
                ",gmtEndDay=" + gmtEndDay +
                "}";
    }
}
