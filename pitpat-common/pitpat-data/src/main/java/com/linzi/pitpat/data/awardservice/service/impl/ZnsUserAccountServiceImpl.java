package com.linzi.pitpat.data.awardservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MD5Util;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.mapper.OneWeekConfigDao;
import com.linzi.pitpat.data.activityservice.mapper.RunActivityUserTaskDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityUserDao;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.awardservice.constant.enums.AccountConstant;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDao;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDetailDao;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.request.PayRequest;
import com.linzi.pitpat.data.awardservice.model.vo.AccountWithdrawalVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserAccountSimpleVo;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.activity.OneWeekConfig;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.request.OneWeekJudgeStatusDto;
import com.linzi.pitpat.data.request.ParticipateInDto;
import com.linzi.pitpat.data.service.pay.PaymentService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.mapper.AreaDao;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.AreaI18nEntity;
import com.linzi.pitpat.data.systemservice.service.AreaI18nService;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import com.paypal.http.HttpResponse;
import com.paypal.orders.Capture;
import com.paypal.orders.Order;
import com.paypal.orders.PurchaseUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Service("znsUserAccountService")
@Slf4j
public class ZnsUserAccountServiceImpl implements ZnsUserAccountService {
    private static final String CANCEL_URL = "/app/pay/failPayment";
    private static final String RETURN_URL = "/app/pay/successPayment";

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private PaymentService paymentService;
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ZnsUserAccountDao userAccountDao;
    @Lazy
    @Resource
    private ZnsUserService userService;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private ZnsRunActivityDao znsRunActivityDao;
    @Autowired
    private ZnsUserDao znsUserDao;
    @Autowired
    private AreaDao areaDao;

    @Autowired
    private RunActivityUserTaskDao runActivityUserTaskDao;

    @Autowired
    private OneWeekConfigDao oneWeekConfigDao;

    @Autowired
    private ZnsRunActivityUserDao znsRunActivityUserDao;

    @Autowired
    private ZnsUserAccountDetailDao znsUserAccountDetailDao;
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private AreaService areaService;

    @Resource
    private AreaI18nService areaI18nService;


    @Override
    @Deprecated
    public ZnsUserAccountEntity getOrCreateAccountByUserId(Long userId, Integer appVersion) {
        ZnsUserAccountEntity accountEntity = userAccountDao.selectOne(Wrappers.<ZnsUserAccountEntity>lambdaQuery()
                .eq(ZnsUserAccountEntity::getUserId, userId).eq(ZnsUserAccountEntity::getIsDelete, 0).last("limit 1"));
        if (Objects.isNull(accountEntity)) {
            return addNewUserAccount(userId, appVersion);
        }
        return accountEntity;
    }


    @Override
    @Deprecated
    public ZnsUserAccountEntity getOrCreateAccountByUserId(Long userId) {
        return getOrCreateAccountByUserId(userId, null);
    }

    private ZnsUserAccountEntity addNewUserAccount(Long userId, Integer appVersion) {
        ZnsUserEntity user = userService.findById(userId);
        ZnsUserAccountEntity userAccountEntity = getNewAccountEntity(user, appVersion);
        userAccountDao.insert(userAccountEntity);
        log.info("add new user account success");
        return userAccountEntity;
    }

    @Override
    public ZnsUserAccountEntity addUserAccount(Long userId, Integer appVersion) {
        return addNewUserAccount(userId, appVersion);
    }

    @Override
    public Result checkPassword(Long userId, String payPassword, boolean checkVersion) {
        // 机器人支付不需要校验密码
        ZnsUserEntity user = userService.findById(userId);
        if (null != user && 1 == user.getIsRobot()) {
            return null;
        }

        if (StringUtil.isEmpty(payPassword)) {
            //ios版本审核兼容
            if (checkVersion) {
                return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.validate.notMatchCondition")); //"You do not meet the signup requirements"
            } else {
                return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.validate.emptyPassword")); //"Password is empty"
            }
        }

        ZnsUserAccountEntity account = getByUserId(userId);

        String password = MD5Util.md5(payPassword + account.getSalt());
        //校验失败次数
        String countKey = RedisConstants.USER_PAY_ERROR_PASSWORD_COUNT + userId;
        RedisAtomicInteger rai = new RedisAtomicInteger(countKey, redisTemplate.getConnectionFactory());
        if (rai.get() >= 5) {
            return CommonResult.fail(UserError.PAY_PASSWORD_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.validate.passwordLockIn24Hour"), 0);
        }

        boolean r = password.equals(account.getPassword());
        if (!r) {
            //设置1天的有效期，过期释放key
            redisTemplate.expire(countKey, 1L, TimeUnit.DAYS);
            int count = rai.incrementAndGet();

            if (count > 5) {
                return CommonResult.fail(UserError.PAY_PASSWORD_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.validate.passwordLockIn24Hour"), 0);
            } else {
                return CommonResult.fail(UserError.PAY_PASSWORD_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.validate.payPassword.countLimit", 5 - count), 5 - count);
            }
        }
        redisTemplate.delete(countKey);
        return null;
    }

    @Override
    public boolean increaseAmount(BigDecimal amount, Long userId, boolean isBonus) {
        return increaseAmount(amount, userId, isBonus, false);
    }

    /**
     * 增加用户金额
     *
     * @param amount
     * @param userId
     * @param isBonus
     * @param ignoreScale 忽略小数处理,ture:忽略,false:不忽略
     * @return
     */
    @Override
    public boolean increaseAmount(BigDecimal amount, Long userId, boolean isBonus, Boolean ignoreScale) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        if (!ignoreScale) {
            Currency userCurrency = getUserCurrency(userId);
            amount = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), amount);
        }
        RLock lock = redissonClient.getLock(RedisConstants.USER_ACCOUNT_ADD + userId);
        boolean res = false;
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return false;
            }
            res = userAccountDao.increaseAmount(amount, userId, isBonus);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return res;
    }

    @Override
    public boolean decreaseAmount(BigDecimal amount, Long userId) {
        return decreaseAmount(amount, userId, false);
    }

    /**
     * 扣减金额
     *
     * @param amount
     * @param userId
     * @param ignoreScale 是否忽略小数处理,true:忽略,false:不忽略
     * @return
     */
    @Override
    public boolean decreaseAmount(BigDecimal amount, Long userId, boolean ignoreScale) {
        if (!ignoreScale) {
            ZnsUserAccountEntity userAccount = getByUserId(userId);
            amount = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), amount);
        }
        return userAccountDao.decreaseAmount(amount, userId);
    }

    @Override
    public boolean setUpPassword(Long userId, String password) {
        ZnsUserAccountEntity account = getByUserId(userId);
        if (!StringUtils.hasText(account.getSalt())) {
            String salt = NanoId.randomNanoId();
            account.setSalt(salt);
        }
        String passwordMd5 = MD5Util.md5(password + account.getSalt());
        ZnsUserAccountEntity update = new ZnsUserAccountEntity();
        update.setId(account.getId());
        update.setModifieTime(ZonedDateTime.now());
        update.setPassword(passwordMd5);
        update.setSalt(account.getSalt());
        return userAccountDao.updateById(update) > 0;
    }

    @Override
    public Result capturePayment(String orderId) {

        ZnsUserAccountDetailEntity accountDetailEntity = userAccountDetailService.getAccountDetailByTradeNo(orderId);


        if (Objects.isNull(accountDetailEntity)) {
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), "ZnsUserAccountDetailEntity不存在，billNo=" + orderId);
        }

        String lockKey = RedisConstants.USER_ACCOUNT + accountDetailEntity.getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean accountLock = lock.tryLock();

        try {
            //扣款
            HttpResponse<Order> response = paymentService.captureOrder(orderId, false);

            if (accountLock) {

                for (PurchaseUnit purchaseUnit : response.result().purchaseUnits()) {
                    for (Capture capture : purchaseUnit.payments().captures()) {
                        log.info("Capture id: {}", capture.id());
                        log.info("status: {}", capture.status());
                        log.info("invoice_id: {}", capture.invoiceId());

                        ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByBillNo(capture.invoiceId());
                        if (Objects.isNull(detail)) {
                            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), "ZnsUserAccountDetailEntity不存在，billNo=" + capture.invoiceId());
                        }
                        if (detail.getTradeStatus() == 2) {
                            log.info("paypal转账付款成功,重复调用");
                            return CommonResult.success();
                        }
                        if ("COMPLETED".equals(capture.status())) {
                            userAccountDetailService.updateAccountDetail(detail.getId(), 2, orderId, DateUtil.addHours(
                                    DateTimeUtil.parseWithPattern(capture.createTime(), "yyyy-MM-dd'T'HH:mm:ss'Z'"), 8), ZonedDateTime.now(), null, "", "", "", null);
                            //充值不处理金额小数问题
                            increaseAmount(detail.getAmount(), accountDetailEntity.getUserId(), false, true);
                            log.info("paypal转账付款成功");
                        } else if ("PENDING".equals(capture.status())) {
                            String reason = "PENDING";
                            if (capture.captureStatusDetails() != null && capture.captureStatusDetails().reason() != null) {
                                reason = capture.captureStatusDetails().reason();
                            }
                            userAccountDetailService.updateAccountDetail(detail.getId(), 1, orderId, DateUtil.addHours(
                                    DateTimeUtil.parseWithPattern(capture.createTime(), "yyyy-MM-dd'T'HH:mm:ss'Z'"), 8), null, null, "", "", "", null);
                            log.info("支付成功,状态为=PENDING : {}", reason);
                        } else {
                            userAccountDetailService.updateAccountDetail(detail.getId(), null, orderId, DateUtil.addHours(
                                    DateTimeUtil.parseWithPattern(capture.createTime(), "yyyy-MM-dd'T'HH:mm:ss'Z'"), 8), null, null, "", "", "", null);
                        }
                    }
                }

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("handleActivityFinished获取锁 后删除锁" + lockKey);
                lock.unlock();
            }
        }

        return CommonResult.success();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result capturePaymentByBillNo(String billNo) {
        log.info("ZnsUserAccountServiceImpl.capturePaymentByBillNo------ 同步支付，billNo：{}，开始", billNo);
        ZnsUserAccountDetailEntity accountDetailEntity = znsUserAccountDetailDao.selectAccountDetailByBillNo(billNo);
        if (Objects.isNull(accountDetailEntity)) {
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("user.account.no.entity", billNo));
        }
        String lockKey = RedisConstants.USER_ACCOUNT + accountDetailEntity.getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean accountLock = lock.tryLock();
        try {
            String orderId = accountDetailEntity.getTradeNo();
            //扣款
            HttpResponse<Order> response = paymentService.captureOrder(orderId, false);
            if (accountLock) {
                for (PurchaseUnit purchaseUnit : response.result().purchaseUnits()) {
                    for (Capture capture : purchaseUnit.payments().captures()) {
                        log.info("Capture id: {}", capture.id());
                        log.info("status: {}", capture.status());
                        log.info("invoice_id: {}", capture.invoiceId());
                        ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByBillNo(capture.invoiceId());
                        if (Objects.isNull(detail)) {
                            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error")); //"ZnsUserAccountDetailEntity不存在，billNo=" + capture.invoiceId()
                        }
                        if ("COMPLETED".equals(capture.status())) {
                            log.info("ZnsUserAccountServiceImpl.capturePaymentByBillNo------ 同步支付，billNo：{}，palpay扣款成功", billNo);
                            //更新记录状态、交易时间
                            ZonedDateTime tradeTime = DateUtil.addHours(DateTimeUtil.parseWithPattern(capture.createTime(), "yyyy-MM-dd' T 'HH:mm:ss'Z'"), 8);
                            int num = znsUserAccountDetailDao.successPayment(detail.getId(), AccountConstant.TradeStatusEnum.TRADE_STATUS_2.getCode(), tradeTime, ZonedDateTime.now());
                            if (num > 0) {
                                log.info("ZnsUserAccountServiceImpl.capturePaymentByBillNo------ 同步支付成功，增加余额，billNo：{}，资金明细更新成功，paypal转账付款成功", billNo);
                                increaseAmount(detail.getAmount(), detail.getUserId(), false, true);
                                return CommonResult.success();
                            }
                        } else if ("PENDING".equals(capture.status())) {
                            String reason = "PENDING";
                            if (capture.captureStatusDetails() != null && capture.captureStatusDetails().reason() != null) {
                                reason = capture.captureStatusDetails().reason();
                            }
                            userAccountDetailService.updateAccountDetail(detail.getId(), 1, orderId, DateUtil.addHours(
                                    DateTimeUtil.parseWithPattern(capture.createTime(), "yyyy-MM-dd'T'HH:mm:ss'Z'"), 8), null, null, "", "", "", null);
                            log.info("支付成功,状态为=PENDING : {}", reason);
                        } else {
                            userAccountDetailService.updateAccountDetail(detail.getId(), null, orderId, DateUtil.addHours(
                                    DateTimeUtil.parseWithPattern(capture.createTime(), "yyyy-MM-dd'T'HH:mm:ss'Z'"), 8), null, null, "", "", "", null);
                        }
                    }
                }

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("ZnsUserAccountServiceImpl.capturePaymentByBillNo获取锁 后删除锁" + lockKey);
                lock.unlock();
            }
        }

        return CommonResult.success();
    }


    /**
     * 支付成功，增加余额
     *
     * @param billNo
     * @param update_time
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result successPayment(String billNo, String update_time) {
        ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByBillNo(billNo);
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error")); //"ZnsUserAccountDetailEntity不存在，billNo=" + billNo
        }
        if (AccountConstant.TradeStatusEnum.TRADE_STATUS_2.getCode().equals(detail.getTradeStatus())) {
            log.info("ZnsUserAccountServiceImpl.successPayment------ 支付成功，增加余额，billNo：{}，订单已完成1", billNo);
            return CommonResult.success();
        }
        String lockKey = RedisConstants.USER_ACCOUNT + detail.getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean accountLock = lock.tryLock();

        try {
            if (accountLock) {
                detail = userAccountDetailService.getAccountDetailByBillNo(billNo);
                log.info("ZnsUserAccountServiceImpl.successPayment------ 支付成功，增加余额，billNo：{}，detail：{}", billNo, detail);
                if (AccountConstant.TradeStatusEnum.TRADE_STATUS_2.getCode().equals(detail.getTradeStatus())) {
                    log.info("ZnsUserAccountServiceImpl.successPayment------ 支付成功，增加余额，billNo：{}，订单已完成2", billNo);
                    return CommonResult.success();
                }

                //更新记录状态、交易时间
                ZonedDateTime tradeTime = DateUtil.addHours(DateTimeUtil.parseWithPattern(update_time, "yyyy-MM-dd'T'HH:mm:ss'Z'"), 8);
                int num = znsUserAccountDetailDao.successPayment(detail.getId(), AccountConstant.TradeStatusEnum.TRADE_STATUS_2.getCode(), tradeTime, ZonedDateTime.now());
                if (num > 0) {
                    log.info("ZnsUserAccountServiceImpl.successPayment------ 支付成功，增加余额，billNo：{}，资金明细更新成功，paypal转账付款成功", billNo);
                    increaseAmount(detail.getAmount(), detail.getUserId(), false, true);
                    return CommonResult.success();
                }
                log.info("ZnsUserAccountServiceImpl.successPayment------ 支付成功，增加余额，billNo：{}，资金明细更新失败，paypal转账付款失败", billNo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("ZnsUserAccountServiceImpl.successPayment获取锁 后删除锁" + lockKey);
                lock.unlock();
            }
        }

        return CommonResult.success();
    }

    @Override
    public Result failPayment(String orderId) {

        try {
            //查询支付信息
            HttpResponse<Order> response = paymentService.ordersGet(orderId, false);
            for (PurchaseUnit purchaseUnit : response.result().purchaseUnits()) {
                //取消记录
                ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByBillNo(purchaseUnit.invoiceId());
                userAccountDetailService.delete(detail.getId(), "取消支付");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return CommonResult.success();
    }


    @Override
    public Result failPaymentByBillNo(String billNo) {
        try {
            ZnsUserAccountDetailEntity accountDetailEntity = znsUserAccountDetailDao.selectAccountDetailByBillNo(billNo);
            //查询支付信息
            HttpResponse<Order> response = paymentService.ordersGet(accountDetailEntity.getTradeNo(), false);
            for (PurchaseUnit purchaseUnit : response.result().purchaseUnits()) {
                //取消记录
                ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByBillNo(purchaseUnit.invoiceId());
                userAccountDetailService.delete(detail.getId(), "取消支付");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return CommonResult.success();
    }


    @Override
    @Transactional
    public Result payByBalance(RunActivityPayRequest request) {
        Long userId = request.getUserId();
        String billNo = NanoId.randomNanoId();
        ;
        RLock lock = redissonClient.getLock(RedisConstants.USER_ACCOUNT + userId);
        BigDecimal amount = request.getAmount();
        Map<String, Object> map = new HashMap<>();
        map.put("billNo", billNo);
        map.put("activityId", request.getActivityId());
        try {
            if (LockHolder.tryLock(lock, 1, 3)) {
                //查询当前账户
                ZnsUserAccountEntity account = this.getByUserId(userId);
                //查询当前冻结状态的金额，例如提现中，支付中
                BigDecimal allAmountByStatus = userAccountDetailService.getAllAmountByStatus(userId, Arrays.asList(0, 1), 2, null, account.getId());
                //检查当前可用金额
                BigDecimal validAmount = account.getAmount().subtract(allAmountByStatus);
                if (amount.compareTo(validAmount) > 0) {
                    map.put("amount", validAmount);
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("userAccount.balance.insufficient"), map); // "余额不足"
                }
                //扣余额
                boolean res = this.decreaseAmount(request.getAmount(), request.getUserId());
                if (res) {
                    Long accountDetailAddActivityId = userAccountDetailService.addAccountDetailAddActivityId(userId, 2, request.getAccountDetailTypeEnum(), null,
                            amount, billNo, "", 2, "", request.getOperationalActivityId(), null, null,
                            request.getActivityId(), request.getActivityType(), request.getUserCouponId(), request.getPrivilegeBrand(), request.getBrandRightsInterests());
                    map.put("detailId", accountDetailAddActivityId);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success(map);
    }

    @Override
    @Transactional
    public boolean refundBalance(ZnsUserAccountDetailEntity detail, String remark) {
        //查询支付记录
        if (detail.getRefundStatus() == 1) {
            return true;
        }
        RLock lock = redissonClient.getLock(RedisConstants.USER_ACCOUNT + detail.getUserId());
        LockHolder.tryLock(lock, 1, 3, () -> {
            boolean res = userAccountDetailService.refundAccountDetail(detail.getId(), remark);
            if (res) {
                userAccountDao.refundBalance(detail.getAmount(), detail.getUserId());
            }
        });
        return true;
    }

    @Override
    public String callBack(String billNo, String paymentStatus, String paymentId, String parentPaymentId, String pendingReason) {
        //查询流水
        ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByBillNo(billNo);
        if (Objects.isNull(detail)) {
            log.error("paypal callBack 处理失败，流水详情不存在");
            return "failure";
        }
        if (detail.getRefundStatus() == 1) {
            return "success";
        }
        if (detail.getTradeStatus() == 2) {
            return "success";
        }
        RLock lock = redissonClient.getLock(RedisConstants.USER_ACCOUNT + detail.getUserId());
        try {
            if (LockHolder.tryLock(lock, 1, 3)) {
                if ("COMPLETED".equals(paymentStatus)) {
                    //进行数据库操作，修改订单状态为已支付成功，尽快发货（配合回调和CapturesGet查询确定成功）
                    //查询支付流水
                    increaseAmount(detail.getAmount(), detail.getUserId(), false, true);
                    userAccountDetailService.updateAccountDetail(detail.getId(), 2, null, null, ZonedDateTime.now(), null, "", "", "", null);
                    log.info("paypal转账付款成功");
                } else if ("PENDING".equals(paymentStatus)) {
                    log.info("订单支付成功,状态为=PENDING，产生此状态的原因是 : {}", pendingReason);
                    //进行数据库操作，修改订单状态为已支付成功，但触发了人工审核，请审核通过后再发货（配合回调和CapturesGet查询确定成功）
                    userAccountDetailService.updateAccountDetail(detail.getId(), 1, null, null, null, null, "", "", "", null);
                } else {
                    // TODO: 2022/2/28 看产品逻辑
                    userAccountDetailService.updateAccountDetail(detail.getId(), null, null, null, null, null, "", "", "", null);
                }
                return "success";
            }
            return "failure";
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result cancelPayment(String tradeNo, Integer userId) throws Exception {
        if (!StringUtils.hasText(tradeNo)) {
            return CommonResult.success();
        }
        HttpResponse<Order> response = paymentService.ordersGet(tradeNo, false);
        String status = "";
        if (Objects.nonNull(response)) {
            status = response.result().status();
        }
        //APPROVED. 客户通过 PayPal 钱包或其他形式的客人或无品牌付款方式批准了付款。例如，卡、银行帐户等
        //COMPLETED. 付款已授权或已为订单获取授权付款
        if (!"COMPLETED".equals(status) || !"APPROVED".equals(status)) {
            //取消付款
            RLock lock = redissonClient.getLock(RedisConstants.USER_ACCOUNT + userId);
            return LockHolder.tryLock(lock, 1, 3, () -> {
                try {
                    //查询流水
                    ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByTradeNo(tradeNo);
                    if (Objects.isNull(detail)) {
                        log.error("cancelPayment 处理失败，流水详情不存在");
                        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "流水详情不存在");
                    }
                    if (detail.getTradeStatus() == 2 || detail.getTradeStatus() == 1) {
                        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "交易当前状态不可取消");
                    }
                    userAccountDetailService.delete(detail.getId(), "定时取消未付款流水");
                } catch (Exception e) {
                    log.error("交易中数据处理失，错误原因：", e);
                }
                return CommonResult.success();
            });
        }

        return CommonResult.success();
    }

    @Override
    public Result paymentResult(String orderId) {
        //查询流水
        ZnsUserAccountDetailEntity detail = userAccountDetailService.getAccountDetailByTradeNo(orderId);
        if (Objects.isNull(detail)) {
            return CommonResult.success(2);
        }
        if (detail.getTradeStatus() == 0) {
            return CommonResult.success(0);
        }
        if (detail.getTradeStatus() == 2 && detail.getTradeStatus() == 1) {
            return CommonResult.success(1);
        }
        return CommonResult.success(0);
    }

    @Override
    public ZnsUserAccountEntity getByUserId(Long userId) {
        return userAccountDao.selectOne(Wrappers.<ZnsUserAccountEntity>lambdaQuery()
                .eq(ZnsUserAccountEntity::getUserId, userId)
                .eq(ZnsUserAccountEntity::getIsDelete, 0)
                .last("limit 1"));
    }

    @Override
    public Currency getUserCurrency(Long userId) {
        ZnsUserAccountEntity accountEntity = getUserAccount(userId);

        if (accountEntity == null) {
            //默认使用美元
            return I18nConstant.CurrencyCodeEnum.USD.getCurrency();
        }
        return I18nConstant.buildCurrency(accountEntity.getCurrencyCode());
    }

    @Override
    public ZnsUserAccountEntity getUserAccount(Long userId) {
        ZnsUserAccountEntity userAccount = userAccountDao.selectOne(Wrappers.<ZnsUserAccountEntity>lambdaQuery()
                .eq(ZnsUserAccountEntity::getUserId, userId)
                .eq(ZnsUserAccountEntity::getIsDelete, 0)
                .orderByDesc(ZnsUserAccountEntity::getId)
                .last("limit 1"));
        if (Objects.isNull(userAccount)) {
            userAccount = new ZnsUserAccountEntity();
            String languageCode = LocaleContextHolder.getLocale().toString();
            AreaI18nEntity areaI18nEntity = areaI18nService.getOneByLanguageCode(languageCode);
            if (Objects.isNull(areaI18nEntity)) {
                areaI18nEntity = areaI18nService.getOneByLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
            }
            AreaEntity areaEntity = areaService.findById(areaI18nEntity.getAreaId());
            userAccount.setCurrencyCode(areaEntity.getCurrencyCode());
            userAccount.setCurrencySymbol(areaEntity.getCurrencySymbol());
        }
        return userAccount;
    }

    @Override
    public List<UserAccountSimpleVo> getFriendRankingList(Long userId) {
        return userAccountDao.getFriendRankingList(userId);
    }

    @Override
    public List<UserAccountSimpleVo> getAllUserRankingList(Long userId) {
        return userAccountDao.getAllUserRankingList(userId);
    }

    @Override
    public Integer getFriendRanking(Long userId) {
        String key = RedisConstants.AWARD_ISSUED_FRIEND_RANKING + userId;
        Object o = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(o)) {
            return Integer.valueOf(o.toString());
        }
        Integer friendRanking = userAccountDao.getFriendRanking(userId);
        if (Objects.isNull(friendRanking)) {
            friendRanking = -1;
        }
        redisTemplate.opsForValue().set(key, friendRanking.toString(), 60, TimeUnit.SECONDS);
        return friendRanking;
    }

    @Override
    public Integer getAllUserRanking(Long userId) {
        String key = RedisConstants.AWARD_ISSUED_ALL_USER_RANKING + userId;
        Object o = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(o)) {
            return Integer.valueOf(o.toString());
        }
        Integer allUserRanking = userAccountDao.getAllUserRanking(userId);
        if (Objects.isNull(allUserRanking)) {
            allUserRanking = -1;
        }
        redisTemplate.opsForValue().set(key, allUserRanking.toString(), 60, TimeUnit.SECONDS);
        return allUserRanking;
    }

    @Override
    public Result withdrawalApply(BigDecimal amount, Long userId, String payAccount, PayRequest request) {
        Map<String, Object> data = new HashMap<>();
        String billNo = NanoId.randomNanoId();
        ;
        RLock lock = redissonClient.getLock(RedisConstants.USER_ACCOUNT + userId);
        Long accountDetailId = null;
        try {
            if (LockHolder.tryLock(lock, 1, 3)) {
                //查询当前账户
                ZnsUserAccountEntity account = this.getByUserId(userId);
                //查询当前冻结状态的金额，例如提现中，支付中
                BigDecimal allAmountByStatus = userAccountDetailService.getAllAmountByStatus(userId, Arrays.asList(0, 1), 2, null, account.getId());
                //检查当前可提现金额
                BigDecimal validAmount = account.getAmount().subtract(allAmountByStatus);
                if (amount.compareTo(validAmount) > 0) {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.amount.insufficient")); //"提现金额不足"
                }
                //税率
                String config = sysConfigService.selectConfigByKey("paypal.cash.config");
                Map<String, Object> object = JsonUtil.readValue(config);
                BigDecimal serviceRate = MapUtil.getBigDecimal(object.get("serviceRate"));
                BigDecimal taxRate = MapUtil.getBigDecimal(object.get("taxRate"));
                if (Objects.nonNull(request.getServiceRate()) && Objects.nonNull(request.getTaxRate())) {
                    if (request.getServiceRate().compareTo(serviceRate) != 0 || request.getTaxRate().compareTo(taxRate) != 0) {
                        log.info("税率或服务利率发生变化，需重新提交");
                        data.put("serviceRate", serviceRate.toString());
                        data.put("taxRate", taxRate.toString());
                        data.put("currency", I18nConstant.buildCurrency(account.getCurrencyCode()));
                        return CommonResult.fail(UserError.CASH_FAIL_RATE_CHANGE.getCode(), UserError.CASH_FAIL_RATE_CHANGE.getMsg(), data);
                    }
                }
                BigDecimal taxAmount = amount.multiply(taxRate);
                taxAmount = taxAmount.setScale(2, BigDecimal.ROUND_DOWN);
                BigDecimal serviceAmount = amount.multiply(serviceRate);
                serviceAmount = serviceAmount.setScale(2, BigDecimal.ROUND_DOWN);
                accountDetailId = userAccountDetailService.addAccountDetailAddActivityId(userId, 2, AccountDetailTypeEnum.WITHDRAW, null, amount,
                        billNo, payAccount, 1, "", request.getActivityId(), taxAmount, serviceAmount, request.getActivityId(), 7, null, null, null);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        data.put("accountDetailId", accountDetailId);
        return CommonResult.success(data);
    }

    @Override
    @Transactional
    public void withdrawalArrival(Long userId, BigDecimal amount, AccountWithdrawalVo po, Long operatorId) {
        boolean res = this.decreaseAmount(amount, userId, true);
        if (res) {
            userAccountDetailService.updateAccountDetail(po.getId(), 2, "", ZonedDateTime.now(), ZonedDateTime.now(), po.getActualAmount(), po.getActualPaypalAccount(), po.getOurSidePaypalAccount(), po.getTransferAccountsRemark(), operatorId);
        } else {
            throw new RuntimeException("提现失败");
        }
    }

    @Override
    public Result judgetStatus(ParticipateInDto dto,ZnsUserEntity znsUserEntity) {
        log.info(" judgetStatus 请求参数的值为={} " + dto);
        List<String> keys = Lists.newArrayList(ConfigKeyEnums.one_week_run_activity_id.getCode(), ConfigKeyEnums.one_week_serven_day_tips.getCode());
        Map<String, String> stringMap = sysConfigService.selectConfigValueListByKeys(keys, true);
        Long activityId = MapUtil.getLong(stringMap.get(ConfigKeyEnums.one_week_run_activity_id.getCode()), 0l);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityDao.selectActivityById(activityId);
        OneWeekConfig oneWeekConfig = oneWeekConfigDao.selectOneWeekConfigByActivityIdUserId(activityId, znsUserEntity.getId());
        Map<String, Object> data = JsonUtil.readValue(stringMap.get(ConfigKeyEnums.one_week_serven_day_tips.getCode()));
        String modalBgImage = data.get("modalBgImage") + "";
        String modalIconImage = data.get("modalIconImage") + "";

        OneWeekJudgeStatusDto oneWeekJudgeStatusDto = new OneWeekJudgeStatusDto();
        oneWeekJudgeStatusDto.setModalBgImage(modalBgImage);
        oneWeekJudgeStatusDto.setModalIconImage(modalIconImage);
        if (oneWeekConfig == null) {                                                                                      // 没有资格参加快乐跑，则不展示
            log.info("没有报名或没有参赛资格，zns_one_week_config 表没有数据");
            oneWeekJudgeStatusDto.setStatus(0);
            return CommonResult.success(oneWeekJudgeStatusDto);
        }
        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserDao.selectByActivityIdUserId(activityId, dto.getUserId());
        if (znsRunActivityUserEntity == null) {
            log.info("没有报名或没有参赛资格，zns_one_week_config 表没有数据");
            oneWeekJudgeStatusDto.setStatus(0);
            return CommonResult.success(oneWeekJudgeStatusDto);
        }
        if (znsRunActivityUserEntity != null) {
            if (Objects.equals(znsRunActivityUserEntity.getIsComplete(), 1)) {                        //提现成功，肯定示不展示
                if (Objects.equals(znsRunActivityUserEntity.getRank(), 2)) {
                    log.info("任务完成， zns_run_activity_user 的 rank字段为2 ，表示已经弹过 【提现已经到账弹窗】，不需要弹窗，直接进入app  ");
                    oneWeekJudgeStatusDto.setStatus(0);
                    return CommonResult.success(oneWeekJudgeStatusDto);
                } else {
                    if (Objects.equals(dto.getRequestType(), 0)) {
                        znsRunActivityUserEntity.setRank(2);
                        znsRunActivityUserDao.updateById(znsRunActivityUserEntity);
                    }
                    log.info("任务完成，表示没有弹过 【提现已经到账弹窗】，需要弹窗一次【提现已经到账弹窗】，才能进入app ");
                    oneWeekJudgeStatusDto.setStatus(2);
                    oneWeekJudgeStatusDto.setPopDesc(data.get("endActivityPopDesc2") + "");
                    return CommonResult.success(oneWeekJudgeStatusDto);
                }
            } else if (Objects.equals(znsRunActivityUserEntity.getIsComplete(), 2)) {                  // 提现中，一直展示
                log.info("任务完成，提现中，zns_run_activity_user 的 is_complete 为2，表示正在提现中，后台没有给 payAcount 账号打钱");
                oneWeekJudgeStatusDto.setStatus(1);
                return CommonResult.success(oneWeekJudgeStatusDto);
            }
        }
        if (ZonedDateTime.now().toInstant().toEpochMilli() < znsRunActivityEntity.getApplicationEndTime().toInstant().toEpochMilli()) {                            // 报名时间未结束，一直展示
            log.info("报名时间未结束，需要展示一周快乐跑，当前时间小于zns_run_activity 表 application_end_time 的时间 ");
            oneWeekJudgeStatusDto.setStatus(1);
            return CommonResult.success(oneWeekJudgeStatusDto);
        } else {
            List<RunActivityUserTask> userTasks = runActivityUserTaskDao.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
            if (userTasks == null || userTasks.size() == 0) {
                log.info("用户没有报名，直接进入app ，zns_run_activity_user_task 表没有数据");
                // 用户没有报名，不展示
                oneWeekJudgeStatusDto.setStatus(0);
                return CommonResult.success(oneWeekJudgeStatusDto);
            } else {
                RunActivityUserTask userTask = userTasks.get(6);
                if (ZonedDateTime.now().toInstant().toEpochMilli() <= DateUtil.endOfDate(userTask.getGmtCreate()).toInstant().toEpochMilli()) {                    // 活动未结束，则一直展示
                    log.info("当前时间小于 zns_run_activity_user_task 表的第7天的结束时间 ，则一直需要展示一周快乐跑");
                    oneWeekJudgeStatusDto.setStatus(1);
                    return CommonResult.success(oneWeekJudgeStatusDto);
                } else {
                    if (isFinished(userTasks)) {
                        if (DateUtil.addDays(DateUtil.endOfDate(userTask.getGmtCreate()), 15).toInstant().toEpochMilli() > ZonedDateTime.now().toInstant().toEpochMilli()) {  // 在15天之内可提现时间
                            log.info("任务完成，当前时间小于 zns_run_activity_user_task 表的第7天的结束时间 + 15 天  ");
                            oneWeekJudgeStatusDto.setStatus(1);
                            return CommonResult.success(oneWeekJudgeStatusDto);
                        } else {
                            if (Objects.equals(znsRunActivityUserEntity.getRank(), 3)) {         //超过15天没有提现
                                log.info("任务完成， 当前时间大于 zns_run_activity_user_task 表的第7天的结束时间 + 15 天 ，用户没有提现，【未按照提现弹窗】 已经弹过了，不需要弹窗了，直接进入app  ");
                                oneWeekJudgeStatusDto.setStatus(0);
                                return CommonResult.success(oneWeekJudgeStatusDto);
                            } else {                                        //超过15天没有提现
                                if (Objects.equals(dto.getRequestType(), 0)) {
                                    znsRunActivityUserEntity.setRank(3);
                                    znsRunActivityUserDao.updateById(znsRunActivityUserEntity);
                                }
                                log.info("任务完成， 当前时间大于 zns_run_activity_user_task 表的第7天的结束时间 + 15 天 ，用户没有提现，【未按照提现弹窗】 未弹，需要弹窗一次  ");
                                oneWeekJudgeStatusDto.setStatus(3);
                                oneWeekJudgeStatusDto.setPopDesc(data.get("endActivityPopDesc3") + "");
                                return CommonResult.success(oneWeekJudgeStatusDto);
                            }
                        }
                    } else {
                        if (Objects.equals(znsRunActivityUserEntity.getRank(), 4)) {  // 活动结束，没有跑完不展示
                            log.info("任务未完成，但是【未达成解锁弹窗】 已经弹过了,不再需要弹 未达成解锁弹窗】 ，直接进入app  ");
                            oneWeekJudgeStatusDto.setStatus(0);
                            return CommonResult.success(oneWeekJudgeStatusDto);
                        } else {
                            if (Objects.equals(dto.getRequestType(), 0)) {
                                znsRunActivityUserEntity.setRank(4);                             //超过15天没有提现
                                znsRunActivityUserDao.updateById(znsRunActivityUserEntity);
                            }
                            log.info("任务未完成，但是【未达成解锁弹窗】 没有弹过，需要弹窗一次,才能进入app  ");
                            oneWeekJudgeStatusDto.setStatus(4);
                            oneWeekJudgeStatusDto.setPopDesc(data.get("endActivityPopDesc4") + "");
                            return CommonResult.success(oneWeekJudgeStatusDto);
                        }
                    }
                }
            }
        }
    }


    public boolean isFinished(List<RunActivityUserTask> userTasks) {
        int finished = 0;
        int notfinish = 0;
        for (RunActivityUserTask userTask : userTasks) {
            if (Objects.equals(userTask.getStatus(), 1)) {
                finished = finished + 1;
            } else {
                notfinish = notfinish + 1;
            }
        }
        if (notfinish <= 0) {
            return true;
        }
        return false;
    }

    /**
     * 升级资金账户
     *
     * @param exchangeRate           汇率
     * @param userId                 用户id
     * @param totalIncomeAmount      总收入
     * @param totalExpenditureAmount 总支出
     * @param amount                 余额
     */
    @Override
    public void upgradeAccount(BigDecimal exchangeRate, Long userId, Currency currency, BigDecimal totalIncomeAmount, BigDecimal totalExpenditureAmount, BigDecimal amount) {
        userAccountDao.upgradeAccount(exchangeRate, userId, currency, totalIncomeAmount, totalExpenditureAmount, amount);
    }

    /**
     * 合并账户
     *
     * @param id                     账户Id
     * @param totalIncomeAmount      总收入
     * @param totalExpenditureAmount 总支出
     * @param amount                 账户余额
     */
    @Override
    public Integer mergeAccount(Long id, BigDecimal amount, BigDecimal totalIncomeAmount, BigDecimal totalExpenditureAmount, BigDecimal totalBonus) {
        return userAccountDao.mergeAccount(id, amount, totalIncomeAmount, totalExpenditureAmount, totalBonus);
    }

    @Override
    public List<ZnsUserAccountEntity> selectByUserIdList(List<Long> userIds) {
        LambdaQueryWrapper<ZnsUserAccountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ZnsUserAccountEntity::getIsDelete, 0);
        wrapper.in(ZnsUserAccountEntity::getUserId, userIds);
        return userAccountDao.selectList(wrapper);
    }

    @Override
    public void deleteById(Long id) {
        userAccountDao.deleteById(id);
        log.info("删除资金账户成功");
    }

    @Override
    public List<ZnsUserAccountEntity> getUserAccounts(Long userId) {
        return userAccountDao.getUserAccounts(userId);
    }

    @Override
    public Currency getCurrency(Long userId, Long activityId, boolean hasAward) {
        if (Objects.isNull(userId)) {
            return I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        }
        ZnsUserAccountEntity userAccount = this.getByUserId(userId);
        List<ZnsUserAccountEntity> userAccounts = this.getUserAccounts(userId);
        return getCurrency(userAccount, userId, activityId, userAccounts, hasAward);
    }

    @Override
    public Currency getCurrency(ZnsUserAccountEntity userAccount, Long userId, Long activityId, List<ZnsUserAccountEntity> userAccounts, boolean hasAwardJudge) {
        if (Objects.isNull(userAccount)) {
            return I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        }
        Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        //userAccounts == 1 表示从来没有切换过账户
        if (CollectionUtils.isEmpty(userAccounts) || userAccounts.size() == 1) {
            return currency;
        }
        if (!hasAwardJudge) {
            return currency;
        }
        if (Objects.isNull(activityId)) {
            return currency;
        }
        // 查询奖励时币种
        ZnsUserAccountDetailEntity accountDetail = userAccountDetailService.getAccountIncomeDetail(userId, activityId);
        if (Objects.nonNull(accountDetail)) {
            //账户id不为空
            //查询对应时间时的账户
            ZnsUserAccountEntity userAccountHis = null;
            if (accountDetail.getUserAccountId() != null && accountDetail.getUserAccountId() > 0) {
                userAccountHis = userAccounts.stream().filter(a -> accountDetail.getUserAccountId().equals(a.getId())).findFirst().orElse(userAccount);
            } else {
                userAccountHis = userAccounts.stream().filter(a -> accountDetail.getCreateTime().isAfter(a.getCreateTime())).findFirst().orElse(userAccount);
            }
            currency = I18nConstant.buildCurrency(userAccountHis.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        }
        return currency;
    }

    @Override
    public ZnsUserAccountEntity addUserAccountByOld(ZnsUserEntity user, ZnsUserAccountEntity oldAccount, Integer appVersion) {

        ZnsUserAccountEntity userAccountEntity = getNewAccountEntity(user, appVersion);
        //账户密码迁移
        userAccountEntity.setPassword(oldAccount.getPassword());
        userAccountEntity.setSalt(oldAccount.getSalt());
        //查询是否有对应account
        ZnsUserAccountEntity userAccount = userAccountDao.getUserAccountByCurrencyCode(user.getId(), userAccountEntity.getCurrencyCode());
        if (userAccount != null) {
            userAccountEntity.setId(userAccount.getId());
            userAccountDao.updateById(userAccountEntity);
            userAccountDao.updateNotDelete(userAccount.getId());
        } else {
            userAccountDao.insert(userAccountEntity);
        }

        log.info("add new user account success");
        return userAccountEntity;
    }

    /**
     * 查询N个需要币种升级的账户（不是美元的账户）
     *
     * @param startUserId 开始用户id(不包含)
     * @param num         查询数量
     */
    @Override
    public List<ZnsUserAccountEntity> selectUpgradeAccountByStartUserId(Long startUserId, int num) {
        return userAccountDao.selectUpgradeAccountByStartUserId(startUserId, num);
    }

    private ZnsUserAccountEntity getNewAccountEntity(ZnsUserEntity user, Integer appVersion) {
        ZnsUserAccountEntity userAccountEntity = new ZnsUserAccountEntity();
        if (appVersion < 3080) {
            AreaEntity areaEntity = areaDao.selectAreaByCode(user.getStateCode());
            if (areaEntity == null) {
                throw new BaseException("state code is not exit");
            }
            userAccountEntity.setCurrencyCode(areaEntity.getCurrencyCode());
            userAccountEntity.setCurrencyName(areaEntity.getCurrencyName());
            userAccountEntity.setCurrencySymbol(areaEntity.getCurrencySymbol());
        } else {
            userAccountEntity.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
            userAccountEntity.setCurrencyName(I18nConstant.CurrencyCodeEnum.USD.getName());
            userAccountEntity.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
        }
        userAccountEntity.setUserId(user.getId());
        userAccountEntity.setIsRobot(user.getIsRobot());
        userAccountEntity.setIsTest(user.getIsTest());
        userAccountEntity.setAmount(BigDecimal.ZERO);
        userAccountEntity.setTotalIncomeAmount(BigDecimal.ZERO);
        userAccountEntity.setTotalExpenditureAmount(BigDecimal.ZERO);
        userAccountEntity.setTotalBonus(BigDecimal.ZERO);
        return userAccountEntity;
    }

    @Override
    public ZnsUserAccountEntity selectUserAccountByUserId(Long userId) {
        return userAccountDao.selectUserAccountByUserId(userId);
    }

    @Override
    public Integer selectUserAccountByUserIdNotIsRobotGT(BigDecimal totalBonus) {
        return userAccountDao.selectUserAccountByUserIdNotIsRobotGT(totalBonus);
    }

    @Override
    public Integer selectUserAccountByUserIdNotIsRobotLT(BigDecimal totalBonus) {
        return userAccountDao.selectUserAccountByUserIdNotIsRobotLT(totalBonus);
    }

    @Override
    public ZnsUserAccountEntity findById(Long accountId) {
        return userAccountDao.selectById(accountId);
    }
}
