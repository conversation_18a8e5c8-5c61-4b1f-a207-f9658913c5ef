package com.linzi.pitpat.data.clubservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_club_member")
public class ClubMember implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否逻辑删除，0：未删除
    @TableLogic(delval = "UNIX_TIMESTAMP()")
    private Long isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;
    //加入的俱乐部
    private Long clubId;
    //用户
    private Long userId;
    //俱乐部中的身份 owner，manager，member
    private String role;
    //聊天通知开关(0:开,1：关)
    private Integer chatSwitch;
    //活动通知开关(0:开,1:关)
    private Integer activityNoticeSwitch;

    // 身份排序值（3owner，2manager，1member）
    private Integer roleOrder;

}
