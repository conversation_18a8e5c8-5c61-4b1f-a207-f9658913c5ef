package com.linzi.pitpat.data.clubservice.model.response;

import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberApplyStateEnum;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.user.rundata.UserRunDaysDataFiller;
import com.linzi.pitpat.data.filler.user.rundata.UserRunMileageDataFiller;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 申请审批列表
 */
@Data
@NoArgsConstructor
public class AppClubMemberApplyListRespDto {
    /**
     * 审批id
     */
    private Long applyId;
    /**
     * 用户id
     */
    private Long applyUserId;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String headPortrait;

    /**
     * 运动天数
     */
    @Filler(relationFieldName = "applyUserId", filler = UserRunDaysDataFiller.class)
    private Integer runDayCount;
    /**
     * 运动总里程(m)
     */
    @Filler(relationFieldName = "applyUserId", filler = UserRunMileageDataFiller.class)
    private Integer runMileage;

    /**
     * 是否最近同赛 0 非同赛 1 同赛
     * 不处理了
     */
    private Integer isRecentMatches = 0;

    //加入原因编码
    private String applyReason;
    //邀请码
    private String applyCode;
    //申请状态
    /**
     * 申请状态
     *
     * @see ClubMemberApplyStateEnum
     */
    private String state;
    /**
     * 过期时间
     */
    private ZonedDateTime expiryDate;


}
