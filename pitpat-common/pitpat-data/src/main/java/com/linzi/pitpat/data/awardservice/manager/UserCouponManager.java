package com.linzi.pitpat.data.awardservice.manager;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ISelect;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.biz.CurrencyBizService;
import com.linzi.pitpat.data.awardservice.biz.MallCouponComponent;
import com.linzi.pitpat.data.awardservice.biz.MallCouponConvertComponent;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import com.linzi.pitpat.data.awardservice.model.dto.SendUserCouponDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserAccountDetailSub;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendDetail;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.request.ClaimCouponReq;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponListRequestDto;
import com.linzi.pitpat.data.awardservice.model.resp.ClaimUserCouponResp;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageV2Vo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.model.vo.UserMilestoneCoupon;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserAccountDetailSubService;
import com.linzi.pitpat.data.awardservice.service.UserCouponSendDetailService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.courseservice.model.request.CourseListRequest;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.NumberOfTimesEnum;
import com.linzi.pitpat.data.enums.RouteConfigEnum;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderPickCouponDto;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.request.course.UseCouponRequest;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.model.vo.HalfScreenCouponClaimResp;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.annotation.RedisLock;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/19 14:45
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserCouponManager {
    /**
     * 运营计划优惠券锁前缀
     */
    private final static String PLAN_COUPON_KEY = "plan_coupon_key:";
    private final CouponService couponService;
    private final UserCouponService userCouponService;
    private final ZnsUserAccountService userAccountService;
    private final MainActivityService mainActivityService;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;
    private final ZnsRunActivityService runActivityService;
    private final UserCouponSendDetailService userCouponSendDetailService;
    private final ZnsUserService userService;
    private final CouponI18nService couponI18nService;
    private final AppMessageService appMessageService;
    private final AppRouteConfigService appRouteConfigService;
    private final ActivityCouponConfigService activityCouponConfigService;
    private final ActivityTaskConfigService activityTaskConfigService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ISysConfigService sysConfigService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserAccountDetailSubService userAccountDetailSubService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ZnsUserEquipmentService userEquipmentService;
    private final CurrencyBizService currencyBizService;
    private final MallCouponComponent mallCouponComponent;
    private final MallCouponConvertComponent mallCouponConvertComponent;
    private final ActivityParamsService activityParamsService;
    private final UserExtraBizService userExtraBizService;
    @Resource
    private RedissonClient redissonClient;

    @Resource(name = "asyncExecutor")
    private ThreadPoolTaskExecutor executor;

    /**
     * 查询用户券列表
     *
     * @param request
     * @return
     */
    public PPageUtils selectUserCouponByUseCouponRequest(UseCouponRequest request) {
        UserCouponActivityQuery activityQuery = new UserCouponActivityQuery(0L, request.getActivityType(), 0L, "", MainActivityTypeEnum.OLD.getType());
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(request.getUserId());

        if (Objects.nonNull(request.getActivityId())) {
            MainActivity mainActivity = mainActivityService.findById(request.getActivityId());
            if (Objects.nonNull(mainActivity) && !MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
                activityQuery = new UserCouponActivityQuery(mainActivity.getId(), mainActivity.getOldType(), 1L, "", mainActivity.getMainType());
                // 兼容聚合正在跑步活动卷限制类型
                if (mainActivity.getMainType().equals(MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType())) {
                    activityQuery.setMainType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
                }
                // 聚合活动查询
                ActivityPolymerizationRecord activityId = activityPolymerizationRecordService.findByActivityId(mainActivity.getId());
                if (Objects.nonNull(activityId)) {
                    activityQuery.setNewActivityTaskId(activityId.getParentActivityId());
                }
                if (mainActivity.getAllowCoupon() == 0) {
                    return new PPageUtils();
                }
            } else {
                ZnsRunActivityEntity runActivity = runActivityService.findById(request.getActivityId());
                activityQuery = new UserCouponActivityQuery(runActivity.getId(), runActivity.getActivityType(), runActivity.getTaskConfigId(), runActivity.getBatchNo(), MainActivityTypeEnum.OLD.getType());
            }
        }

        UserCouponActivityQuery finalActivityQuery = activityQuery;
        PPageUtils pageUtils = PPageUtils.startPage(request.getPageNum(), request.getPageSize()).doSelect(new ISelect() {
            @Override
            public List doSelect(IPage page) {
                return userCouponService.selectUserCouponByCondition(page, request.getUserId(), 5, 0, finalActivityQuery.getId(), finalActivityQuery.getActivityType(),
                        finalActivityQuery.getTaskConfigId(), finalActivityQuery.getBatchNo(), request.getUserCouponId()
                        , userAccount.getCurrencyCode(), finalActivityQuery.getOnlyCouponId(), finalActivityQuery.getMainType(), finalActivityQuery.getNewActivityTaskId(), CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_1.type);
            }
        });
        List<UserCouponDiKou> rows = pageUtils.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return pageUtils;
        }
        for (UserCouponDiKou row : rows) {
            Currency currency = new Currency();
            currency.setCurrencyCode(row.getCurrencyCode());
            currency.setCurrencyName(row.getCurrencyName());
            currency.setCurrencySymbol(row.getCurrencySymbol());
            row.setCurrency(currency);
        }
        pageUtils.setRows(rows);
        return pageUtils;
    }

    /**
     * 有数量限制的发货优惠券
     *
     * @param sendDetailList
     */
    public void sendUserCouponByQuota(List<UserCouponSendDetail> sendDetailList) {
        log.error("UserCouponServiceImpl#sendUserCouponByQuota------有数量限制的发货优惠券,数量： " + sendDetailList.size());
        for (UserCouponSendDetail sendDetail : sendDetailList) {
            Coupon coupon = couponService.selectCouponById(sendDetail.getCouponId());
            SendUserCouponDto sendUserCouponDto = new SendUserCouponDto(sendDetail.getCouponId(), sendDetail.getUserId(), 0L, sendDetail.getId());
            String lockKey = PLAN_COUPON_KEY + sendDetail.getCouponId();
            RLock lock = redissonClient.getLock(lockKey);
            boolean b = false;
            try {
                b = lock.tryLock(60L, 60L, TimeUnit.SECONDS); //等60秒，锁60秒
                if (b) {
                    if (coupon.getQuota() != null && coupon.getQuota() != -1) {
                        //有数量限制
                        List<UserCouponStatisticsDto> dtoList = userCouponService.selectCouponStatisticsDto(List.of(sendDetail.getCouponId()));
                        // key->优惠券Id，val->优惠券发放数量
                        Map<Long, Integer> map = dtoList.stream().collect(Collectors.toMap(UserCouponStatisticsDto::getCouponId, UserCouponStatisticsDto::getCouponNum));
                        Integer sendQuota = map.getOrDefault(sendDetail.getCouponId(), 0);  //已发放数量
                        if (sendQuota >= coupon.getQuota()) {
                            //优惠券已发数量大于可发数量
                            sendDetail.setStatus(-1);
                            sendDetail.setRemarks("优惠券已发数量大于可发数量");
                            userCouponSendDetailService.updateById(sendDetail);
                            continue;
                        }
                    }
                    sendUserCoupon(sendUserCouponDto, sendDetail, coupon);
                } else {
                    //获取锁失败
                    sendDetail.setStatus(-1);
                    sendDetail.setRemarks("获取锁失败");
                    userCouponSendDetailService.updateById(sendDetail);
                }
            } catch (Exception e) {
                log.error("UserCouponServiceImpl#sendUserCouponByQuota------有数量限制的发货优惠券,sendDetailId： " + sendDetail.getId(), e);
                sendDetail.setStatus(-1);
                sendDetail.setRemarks("发送失败异常:" + e.getMessage());
                userCouponSendDetailService.updateById(sendDetail);
            } finally {
                if (b && lock.isLocked()) {
                    log.info("UserCouponServiceImpl#sendUserCouponByQuota--------有数量限制的发货优惠券获取锁 后删除锁" + lockKey);
                    if (lock.isHeldByCurrentThread()) {
                        //判断锁是否存在，和是否当前线程加的锁。
                        lock.unlock();
                    }
                }
            }
        }
    }


    /**
     * 用户可用优惠券数量
     *
     * @param activityId
     * @param userId
     * @return
     */
    public Integer countUserCanUseCouponNum(Long activityId, Long userId) {
        UseCouponRequest request = new UseCouponRequest();
        request.setPageNum(1);
        request.setPageSize(1000000);
        request.setActivityId(activityId);
        request.setUserId(userId);
        int userCouponCount = 0;                                //用户的可用券
        PPageUtils pageUtils = selectUserCouponByUseCouponRequest(request);
        List<UserCouponDiKou> userCouponDiKous = pageUtils.getRows();
        if (!CollectionUtils.isEmpty(userCouponDiKous)) {
            for (UserCouponDiKou userCouponDiKou : userCouponDiKous) {
                if (userCouponDiKou.getGmtEnd().isBefore(ZonedDateTime.now())) {
                    //过滤已过期的优惠券
                    continue;
                }
                if (Objects.equals(userCouponDiKou.getIsCanUse(), 1)) {
                    userCouponCount++;
                }
            }
        }
        return userCouponCount;
    }

    /**
     * 发放用户优惠券
     *
     * @param couponId
     * @param userId
     * @param activityId
     * @return
     */
    public Result sendUserCoupon(Long couponId, Long userId, Long activityId) {
        return sendUserCouponMilepost(couponId, userId, activityId, null);
    }

    /**
     * 查询用户里程碑优惠券
     *
     * @param userId
     * @return
     */
    public List<UserMilestoneCoupon> findUserMilestoneList(Long userId) {
        String currencyCode = userAccountService.getUserAccount(userId).getCurrencyCode();
        I18nConstant.CurrencyCodeEnum currencyCodeEnum = I18nConstant.CurrencyCodeEnum.findByCode(currencyCode);
        List<UserMilestoneCoupon> userMilestoneCouponList = userCouponService.selectUserMilestoneCoupon(userId);
        if (!CollectionUtils.isEmpty(userMilestoneCouponList)) {
            userMilestoneCouponList.forEach(e -> {
                Currency currency = new Currency();
                currency.setCurrencyName(currencyCodeEnum.getName());
                currency.setCurrencyCode(currencyCode);
                currency.setCurrencySymbol(currencyCodeEnum.getSymbol());
                e.setCurrency(currency);
            });
        }
        return userMilestoneCouponList;
    }


    /**
     * 领取优惠券
     *
     * @param claimCouponReq
     * @return
     */
    @Transactional
    public ClaimUserCouponResp claimCoupons(ClaimCouponReq claimCouponReq) {
        long before = System.currentTimeMillis();
        Long runActivityId = claimCouponReq.getRunActivityId();
        if (runActivityId == null) {
            throw new RuntimeException(I18nMsgUtils.getMessage("common.params.systemError"));
        }
        // 参数验证
        Long couponId = claimCouponReq.getCouponId();
        if (couponId == null) {
            throw new RuntimeException(I18nMsgUtils.getMessage("common.params.systemError"));
        }
        Long userId = claimCouponReq.getUserId();
        String key = RedisConstants.CLAIM_COUPON_USER_ID + userId + "#" + RedisConstants.CLAIM_COUPON_ACTIVITY_ID + "#" + RedisConstants.CLAIM_COUPON_ID + couponId;
        RLock lock = redissonClient.getLock(key);
        ClaimUserCouponResp claimUserCouponResp = new ClaimUserCouponResp();
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                throw new RuntimeException(I18nMsgUtils.getMessage("common.params.systemError"));
            }
            // 获取优惠券信息
            ZonedDateTime now = ZonedDateTime.now();
            Coupon coupon = couponService.findOneByStatus(couponId, 1);
            CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                    .couponId(coupon.getId()).langCode(LocaleContextHolder.getLocale().toString()).defaultLangCode(coupon.getDefaultLangCode()).build());
            String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
            if (coupon == null) {
                throw new RuntimeException(I18nMsgUtils.getMessage("coupon.notExist"));
            }
            Integer couponQuota = coupon.getQuota();
            if (couponQuota != null && couponQuota != -1) {
                if (couponQuota - coupon.getQuotaSend() <= 0) {
                    throw new RuntimeException(I18nMsgUtils.getMessage("coupon.status.notAvailable"));
                }
            }


            Integer expiryType = coupon.getExpiryType();
            if (expiryType == 2) {
                ZonedDateTime gmtEnd = coupon.getGmtEnd();
                if (now.isAfter(gmtEnd)) {
                    throw new RuntimeException(I18nMsgUtils.getMessage("coupon.notExist"));
                }
            } else if (expiryType == 1) {

            }

            // 判断是否领取过优惠券
            UserCoupon claimedUserCoupon = userCouponService.findByQuery(new UserCouponQuery().setCouponId(couponId).setUserId(userId).setSourceActivityId(runActivityId));
            if (claimedUserCoupon != null) {
                throw new RuntimeException(I18nMsgUtils.getMessage("coupon.status.taken"));
            }

            // 判断优惠券领取是否超过每个人限制领取张数
            Integer limitCount = coupon.getLimitCount();
            if (limitCount != null && limitCount != -1) {
                long count2 = userCouponService.findCount(userId, coupon.getId());
                if (count2 >= limitCount) {
                    throw new RuntimeException(I18nMsgUtils.getMessage("coupon.status.token.exceed", NumberOfTimesEnum.findByType(limitCount)));
                }
            }

            // 获取当前用户的设备信息
            ZnsUserEquipmentEntity userEquipment = userEquipmentService.getUserEquipmentOne(userId);

            // 免费领取优惠券
            CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, userId);
            UserCoupon userCoupon = BeanUtil.copyBean(currencyAmount, UserCoupon.class);
            userCoupon.setUserId(userId);
            userCoupon.setCouponId(couponId);
            userCoupon.setCouponMainType(coupon.getCouponMainType());
            if (coupon.getExpiryType() == 1) {
                userCoupon.setGmtStart(now);
                userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
            } else if (coupon.getExpiryType() == 2) {
                userCoupon.setGmtStart(coupon.getGmtStart());
                userCoupon.setGmtEnd(coupon.getGmtEnd());
            }
            userCoupon.setSourceType(2);
            userCoupon.setAmount(currencyAmount.getAmount());
            userCoupon.setDiscount(coupon.getDiscount());
            //userCoupon.setActivityId(runActivityId);
            userCoupon.setSourceActivityId(runActivityId);
            userCoupon.setEquipmentNo(userEquipment != null ? userEquipment.getEquipmentNo() : null);
            userCouponService.insert(userCoupon);

            // 增加领取数量
            couponService.addQuotaSend(coupon.getId());

            claimUserCouponResp.setUserCouponId(userCoupon.getId());
            claimUserCouponResp.setTitle(coupon.getTitle());
            claimUserCouponResp.setCouponAmount(currencyAmount.getAmount());
            long sendImBefore = System.currentTimeMillis();
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();

            ZnsUserEntity znsUser = userService.findById(userId);

//            // 这里使用线程池,异步发送
//            executor.execute(() -> {
//                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
//                //String msg = "With a coupon, join Arbor Day Challenge for free. However, NO refund after using it.";
//                // "Congratulations! You have received the " + coupon.getTitle() + " . You can check it on \"Mine\" - \"My coupons\".";
//                String msg;
//                if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType().equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
//                    String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
//                    //如果没获取到就默认返回UTC
//                    String zoneId = znsUser.getZoneId() != null ? znsUser.getZoneId() : "UTC";
//                    String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", zoneId);
//                    String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
//                    if (!StringUtil.isEmpty(znsUser.getLanguageCode())) {
//                        msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg2", couponTypeName, expiryString);
//                    } else {
//                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//                    }
//
//                } else {
//                    if (!StringUtil.isEmpty(znsUser.getLanguageCode())) {
//                        msg = I18nMsgUtils.getMessage("coupon.im.congratulation", couponTitle);
//                    } else {
//                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "coupon.im.congratulation", couponTitle);
//                    }
//
//                }
//
////                appMessageService.sendImTextWithNoJump(claimCouponReq.getZnsUserEntity(), coupon, msg, userCoupon);
//            });
            long sendImAfter = System.currentTimeMillis();
            long after = System.currentTimeMillis();
            log.info("执行这个接口使用的时间:{}秒,发送IM消息需要使用的时间为:{}秒", (after - before) / 1000, (sendImAfter - sendImBefore) / 1000);
            return claimUserCouponResp;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public UserCoupon sendUserCoupon(SendUserCouponDto sendUserCouponDto, UserCouponSendDetail sendDetail, Coupon coupon) {
        couponService.addQuotaSend(coupon.getId());
        ZnsUserEntity userByEmail = userService.findById(sendUserCouponDto.getUserId());
        if (Objects.isNull(userByEmail)) {
            log.info("UserCouponServiceImpl#sendUserCoupon --------> 获取不到该userId :{},无需发送券", sendUserCouponDto.getUserId());
            return null;
        }
        UserCoupon userCoupon = sendUserCouponByCouponConfig(sendUserCouponDto.getCouponId(), coupon, userByEmail.getId());
        sendDetail.setStatus(1);
        sendDetail.setUserId(userByEmail.getId());
        userCouponSendDetailService.updateById(sendDetail);
//        String langCode = userByEmail.getLanguageCode();
//        CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder().couponId(coupon.getId()).langCode(langCode).defaultLangCode(coupon.getDefaultLangCode()).build());
//        String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
//        String msg;
//        if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType().equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
//            String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
//            String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", userByEmail.getZoneId());
//            String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
//            if (!StringUtil.isEmpty(userByEmail.getLanguageCode())) {
//                msg = I18nMsgUtils.getLangMessage(userByEmail.getLanguageCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//            } else {
//                msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//            }

//        } else {
//            if (!StringUtil.isEmpty(userByEmail.getLanguageCode())) {
//                msg = I18nMsgUtils.getLangMessage(userByEmail.getLanguageCode(), "coupon.im.congratulation", couponTitle);
//            } else {
//                msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "coupon.im.congratulation", couponTitle);
//            }
//        }// "Congratulations! You have received the "+ coupon.getTitle() + " . You can check it on \"Mine\" - \"My coupons\".";
//        log.info("UserCouponServiceImpl#sendUserCoupon --------> 语言为：{}, 发送消息为：{}", langCode, msg);
//        appMessageService.sendImTextWithNoJump(userByEmail, coupon, msg, userCoupon);
        return userCoupon;
    }

    public Result sendUserCouponMilepost(Long couponId, Long userId, Long activityId, String milepost) {
        Coupon coupon = couponService.selectCouponById(couponId);
        ZnsUserEntity znsUserEntity = userService.findById(userId);
        return exchangeUserCouponMilepost(coupon, null, znsUserEntity, 1, activityId, milepost);
    }

    public Result exchangeUserCoupon(Coupon coupon, ZnsUserEquipmentEntity userEquipment, ZnsUserEntity loginUser, Integer sourceType, Long activityId) {
        return exchangeUserCouponMilepost(coupon, userEquipment, loginUser, sourceType, activityId, null);
    }

    /**
     * @param sourceType 获取来源
     * @see CouponConstant.SourceTypeEnum
     */
    public Result exchangeUserCouponMilepost(Coupon coupon, ZnsUserEquipmentEntity userEquipment, ZnsUserEntity loginUser, Integer sourceType, Long activityId, String milepost) {
        String key = RedisConstants.EXCHANGE_COUPON + loginUser.getId() + ":" + coupon.getId();
        RLock lock = redissonClient.getLock(key);

        CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, loginUser.getId());
        CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                .couponId(coupon.getId()).langCode(LocaleContextHolder.getLocale().toString()).defaultLangCode(coupon.getDefaultLangCode()).build());
        String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
        UserCoupon userCoupon = new UserCoupon();
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
            userCoupon.setCouponId(coupon.getId());
            userCoupon.setUserId(loginUser.getId());
            userCoupon.setCouponMainType(coupon.getCouponMainType());
            if (userEquipment != null) {
                userCoupon.setEquipmentNo(userEquipment.getEquipmentNo());
            }

            ZonedDateTime now = ZonedDateTime.now();
            if (coupon.getExpiryType() == 1) {
                userCoupon.setGmtStart(now);
                userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
            } else if (coupon.getExpiryType() == 2) {
                userCoupon.setGmtStart(coupon.getGmtStart());
                userCoupon.setGmtEnd(coupon.getGmtEnd());
            }

            userCoupon.setSourceType(Optional.ofNullable(sourceType).orElse(CouponConstant.SourceTypeEnum.source_type_1.type));
            userCoupon.setAmount(currencyAmount.getAmount());
            userCoupon.setCurrencyCode(currencyAmount.getCurrencyCode());
            userCoupon.setCurrencyName(currencyAmount.getCurrencyName());
            userCoupon.setCurrencySymbol(currencyAmount.getCurrencySymbol());
            userCoupon.setDiscount(coupon.getDiscount());
            userCoupon.setIsNew(YesNoStatus.YES.getCode());
            userCoupon.setActivityId(activityId);
            userCoupon.setMilepost(milepost);
            userCouponService.insert(userCoupon);
            String msg;
            if (CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type.equals(coupon.getCouponMainType())) {
                //商城券
                if (!StringUtil.isEmpty(loginUser.getLanguageCode())) {
                    if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_2.type.equals(coupon.getDiscountMethod())) {
                        //折扣
                        msg = I18nMsgUtils.getMessage("push.content.user.mall.coupon.discount", Optional.of(coupon.getDiscount()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                    } else {
                        //金额
                        msg = I18nMsgUtils.getMessage("push.content.user.mall.coupon.amount", Optional.of(coupon.getAmount()).orElse(BigDecimal.ZERO));
                    }
                } else {
                    if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_2.type.equals(coupon.getDiscountMethod())) {
                        //折扣
                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.mall.coupon.discount", Optional.of(coupon.getDiscount()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                    } else {
                        //金额
                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.mall.coupon.amount", Optional.of(coupon.getAmount()).orElse(BigDecimal.ZERO));
                    }
                }
            } else if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType().equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
                String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
                String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", loginUser.getZoneId());
                String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
                if (!StringUtil.isEmpty(loginUser.getLanguageCode())) {
                    msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg2", couponTypeName, expiryString);
                } else {
                    msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
                }

            } else {
                if (!StringUtil.isEmpty(loginUser.getLanguageCode())) {
                    msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg1", couponTitle);
                } else {
                    msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg1", couponTitle);
                }
            }
            appMessageService.sendImTextWithNoJump(loginUser, coupon, msg, userCoupon);
            couponService.addQuotaSend(coupon.getId());
        } catch (Exception e) {
            log.error("exchangeUserCoupon 失败，e", e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("title", coupon.getTitle());
        map.put("couponId", coupon.getId());
        map.put("userCouponId", userCoupon.getId());
        return CommonResult.success(map);
    }


    /**
     * 预使用券
     *
     * @param userCouponId
     * @param activityId
     * @param remarks
     * @return
     */
    public Result preUseCoupon(Long userCouponId, Long activityId, String remarks) {
        String key = RedisConstants.USE_COUPON_PRE + userCouponId;
        RLock lock = redissonClient.getLock(key);
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemBusy"));
            }
            UserCoupon userCoupon = userCouponService.selectUserCouponById(userCouponId);
            if (Objects.isNull(userCoupon)) {
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error"));
            }
            ZonedDateTime now = ZonedDateTime.now();
            if (now.compareTo(userCoupon.getGmtEnd()) > 0) {
                userCoupon.setStatus(3);
                userCoupon.setGmtModified(now);
                userCouponService.update(userCoupon);
                log.info("useCoupon 失败，券过期");
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.expired"));

            }
            if (userCoupon.getStatus() != 0) {
                log.info("useCoupon 失败，券不属于未使用状态");
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.status.notAvailable"));
            }

            ZnsUserEntity user = userService.findById(userCoupon.getUserId());
            Coupon coupon = couponService.selectCouponById(userCoupon.getCouponId());
            if (Objects.isNull(coupon)) {
                return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
            }
            if (coupon.getCouponType() == 1 || coupon.getCouponType() == 2) {
                ZnsRunActivityUserEntity activityUser = runActivityUserService.selectByActivityIdUserId(activityId, userCoupon.getUserId());

                MainActivity mainActivity = mainActivityService.findById(activityId);
                if (Objects.isNull(mainActivity) || MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
                    ZnsRunActivityEntity activity = runActivityService.findById(activityId);
                    Result result = checkUseCouponToActivity(coupon.getCouponType(), userCoupon, activity, activityUser);
                    if (Objects.nonNull(result)) {
                        return result;
                    }
                    Result result1 = checkActivityTime(activity);
                    if (Objects.nonNull(result1)) {
                        return result1;
                    }
                } else {
                    Result result = checkUseCouponToNewActivity(user, mainActivity, activityUser);
                    if (Objects.nonNull(result)) {
                        return result;
                    }
                }
                //check 使用中得卷
                UserCoupon one = userCouponService.findByQuery(new UserCouponQuery().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId()).setStatus(Arrays.asList(1, 2)));
                log.info("UserCoupon exist:{}", Objects.nonNull(one));
                if (Objects.nonNull(one)) {
                    log.info("一场比赛只能使用一次");
                    return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.count.exceed"));
                }

                userCoupon.setUseActivityId(activityUser.getActivityId());
            }
            String sb = userCoupon.getRemarks() + remarks;
            userCoupon.setRemarks(sb);
            userCoupon.setStatus(1);
            userCoupon.setGmtUse(now);
            userCoupon.setGmtModified(now);
            userCouponService.update(userCoupon);
        } catch (Exception e) {
            log.error("preUseCoupon 失败，e", e);
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemError"));
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success(I18nMsgUtils.getMessage("coupon.notification.success"));
    }

    /**
     * 券使用
     *
     * @param userCoupon
     * @param amount
     */
    public void endUseCoupon(UserCoupon userCoupon, BigDecimal amount) {
        Coupon coupon = couponService.selectCouponById(userCoupon.getCouponId());
        if (Objects.isNull(coupon)) {
            return;
        }
        if (coupon.getCouponType() != 1 && coupon.getCouponType() != 2) {
            log.warn("useCoupon 失败，券类型不符");
            return;
        }

        String currencyCode = userAccountService.getUserCurrency(userCoupon.getUserId()).getCurrencyCode();
        if (coupon.getCouponType() == 2) {
            // 奖励翻倍券
            BigDecimal addAmount = userCoupon.getAmount().add(amount);
            addAmount = I18nConstant.currencyFormat(currencyCode, addAmount);
            userCoupon.setAmount(addAmount);
        }
        userCoupon.setStatus(2);
        userCoupon.setGmtUse(DateUtil.getNowDate());
        userCoupon.setGmtModified(DateUtil.getNowDate());
        userCouponService.update(userCoupon);
    }

    /**
     * @param userCouponId
     * @param activityId
     * @param remarks
     * @return
     */
    @Transactional
    public Result endUseCoupon(Long userCouponId, Long activityId, String remarks) {
        String key = RedisConstants.USE_COUPON + userCouponId;
        RLock lock = redissonClient.getLock(key);
        try {
            if (!LockHolder.tryLock(lock, 1, 3)) {
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
            UserCoupon userCoupon = userCouponService.selectUserCouponById(userCouponId);
            if (Objects.isNull(userCoupon)) {
                return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
            }
            Coupon coupon = couponService.selectCouponById(userCoupon.getCouponId());
            if (Objects.isNull(coupon)) {
                return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
            }
            if (coupon.getCouponType() != 1 && coupon.getCouponType() != 2) {
                log.warn("useCoupon 失败，券类型不符");
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.type.error"));
            }
            if (userCoupon.getStatus() != 1) {
                log.info("useCoupon 失败，券不属于未使用状态");
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.status.used"));
            }
            String currencyCode = userAccountService.getUserCurrency(userCoupon.getUserId()).getCurrencyCode();
            if (coupon.getCouponType() == 1) {
                //参赛必胜券
                ZnsRunActivityEntity activity = runActivityService.findById(activityId);
                ZnsRunActivityUserEntity activityUser = runActivityUserService.selectByActivityIdUserId(activityId, userCoupon.getUserId());
                Result result = checkUseCouponToActivity(coupon.getCouponType(), userCoupon, activity, activityUser);
                if (Objects.nonNull(result)) {
                    return result;
                }
                //查询已获得的名次奖励
                UserAccountDetailSub rankDetails = userAccountDetailSubService.selectUserAccountDetailSubBy(userCoupon.getUserId(), activity.getId(), 4);
                BigDecimal rankAward = BigDecimal.ZERO;
                if (Objects.nonNull(rankDetails)) {
                    rankAward = rankDetails.getAmount();
                }
                Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
                List<Map> runningGoalsAwardList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
                Map<Integer, Map> runningGoalsAwardMap = runningGoalsAwardList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
                Map awardMap = new HashMap();
                if (activity.getCompleteRuleType() == 1) {
                    awardMap = runningGoalsAwardMap.get(activityUser.getTargetRunMileage());
                } else if (activity.getCompleteRuleType() == 2) {
                    awardMap = runningGoalsAwardMap.get(activityUser.getTargetRunTime());
                }
                Double firstAward = MapUtils.getDouble(awardMap, ApiConstants.FIRST_AWARD);
                BigDecimal subAmount = new BigDecimal(firstAward).subtract(rankAward);
                subAmount = I18nConstant.currencyFormat(currencyCode, subAmount);
                userCoupon.setAmount(subAmount);
                BigDecimal amount = userCoupon.getAmount();
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    userCoupon.setAmount(BigDecimal.ZERO);
                }
                if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
                    //用户币种不是美元，需要转换汇率
                    ExchangeRateConfigEntity rateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(currencyCode);
                    if (rateConfigEntity != null) {
                        BigDecimal rate = Optional.ofNullable(rateConfigEntity.getExchangeRate()).orElse(BigDecimal.ONE);
                        amount = amount.multiply(rate);
                    }
                }
                amount = I18nConstant.currencyFormat(currencyCode, amount);
                userAccountService.increaseAmount(amount, userCoupon.getUserId(), false);
                userAccountDetailService.addAccountDetail(userCoupon.getUserId(), 1, AccountDetailTypeEnum.COUPON_REWARD, AccountDetailSubtypeEnum.COUPON1, amount, activity.getId(), null);
                userCoupon.setUseActivityId(activityUser.getActivityId());
                //积分补齐
                ActivityUserScore activityUserScore = activityUserScoreService.selectActivityUserScoreByActivityIdUserIdRank(activityId, userCoupon.getUserId(), activityUser.getRank());
                Integer score = 0;
                if (Objects.nonNull(activityUserScore)) {
                    score = activityUserScore.getScore();
                }
                if (jsonObject.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD) != null) {
                    List<Map> runningGoalsAwardScoreList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD), Map.class);
                    Map<Integer, Map> runningGoalsAwardScoreMap = runningGoalsAwardScoreList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
                    Map awardScoreMap = null;
                    if (activity.getCompleteRuleType() == 1) {
                        awardScoreMap = runningGoalsAwardScoreMap.get(activityUser.getTargetRunMileage());
                    } else if (activity.getCompleteRuleType() == 2) {
                        awardScoreMap = runningGoalsAwardScoreMap.get(activityUser.getTargetRunTime());
                    }
                    Double firstScoreAward = MapUtils.getDouble(awardScoreMap, ApiConstants.FIRST_AWARD);
                    Integer firstScore = 0;
                    if (Objects.nonNull(firstScoreAward)) {
                        firstScore = firstScoreAward.intValue();
                    }
                    Integer awardScoreInt = firstScore - score;
                    if (awardScoreInt <= 0) {
                        awardScoreInt = 0;
                    }
                    activityUserScoreService.increaseAmount(awardScoreInt, activityUser.getActivityId(), activityUser.getUserId(), 1, 0, ScoreConstant.SourceTypeEnum.source_type_22.getType());
                }
            } else if (coupon.getCouponType() == 2) {
                // 奖励翻倍券
                ZnsRunActivityUserEntity activityUser = runActivityUserService.selectByActivityIdUserId(activityId, userCoupon.getUserId());
                //ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userCoupon.getUserId());
                Result result = checkUseCouponToActivity(coupon.getCouponType(), userCoupon, null, activityUser);
                if (Objects.nonNull(result)) {
                    return result;
                }
                BigDecimal amount = activityUser.getRunAward();
                if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
                    //用户币种不是美元，需要转换汇率
                    ExchangeRateConfigEntity rateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(currencyCode);
                    if (rateConfigEntity != null) {
                        BigDecimal rate = Optional.ofNullable(rateConfigEntity.getExchangeRate()).orElse(BigDecimal.ONE);
                        amount = amount.multiply(rate);
                    }
                }
                amount = I18nConstant.currencyFormat(currencyCode, amount);
                userAccountService.increaseAmount(amount, userCoupon.getUserId(), false);
                userAccountDetailService.addAccountDetail(userCoupon.getUserId(), 1, AccountDetailTypeEnum.COUPON_REWARD, AccountDetailSubtypeEnum.COUPON2, amount, activityUser.getActivityId(), null);
                userCoupon.setAmount(amount);
                userCoupon.setUseActivityId(activityUser.getActivityId());
            }
            StringBuffer sb = new StringBuffer();
            sb.append(userCoupon.getRemarks() + "");
            sb.append(remarks);
            userCoupon.setRemarks(sb.toString());
            userCoupon.setStatus(2);
            userCoupon.setGmtUse(DateUtil.getNowDate());
            userCoupon.setGmtModified(DateUtil.getNowDate());
            userCouponService.update(userCoupon);
        } catch (Exception e) {
            log.error("endUseCoupon 失败:", e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        return CommonResult.success();
    }


    /**
     * 获取用户可用优惠券列表
     *
     * @param po
     * @param user
     * @param appType
     * @return
     */
    public Page<CouponPageVo> getUserCouponList(CourseListRequest po, ZnsUserEntity user, Integer appType) {
        Page<CouponPageVo> page = new Page<>(po.getPageNum(), po.getPageSize());
        po.setUserId(user.getId());
        Page<CouponPageVo> couponPage = userCouponService.getUserCouponPage(page, po);
        String langCode = I18nMsgUtils.getLangCode();
        List<CouponPageVo> records = couponPage.getRecords();
        records.stream().forEach(item -> {
            CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder().couponId(item.getCouponId()).langCode(langCode).defaultLangCode(item.getDefaultLangCode()).build());
            if (Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle())) {
                item.setTitle(couponI18n.getTitle());
                String description = StringUtils.hasText(couponI18n.getCanUseDescription()) ? couponI18n.getCanUseDescription() : couponI18n.getDescription();
                item.setDescription(description);
            }
        });
        wrapperRouteInfo(couponPage, appType, user);
        return couponPage;
    }

    public Integer getCouponCount(UseCouponRequest request) {
        Long activityId = request.getActivityId();
        if (activityId == null) {
            throw new RuntimeException(I18nMsgUtils.getMessage("common.params.systemError"));
        }
        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.findById(activityId);
        return userCouponService.selectUserCouponCountByCondition(request.getUserId(), 5, 0, znsRunActivityEntity.getId(), znsRunActivityEntity.getActivityType(), znsRunActivityEntity.getTaskConfigId(), znsRunActivityEntity.getBatchNo(), "", null);
    }

    /**
     * 发送半屏弹窗优惠券
     *
     * @param userId
     * @param couponId
     * @return
     */
    @RedisLock(value = {"userId", "couponId"}, isTry = true)
    public HalfScreenCouponClaimResp sendUserHalfScreenPopCouponClaim(Long userId, Long couponId) {
        HalfScreenCouponClaimResp halfScreenCouponClaimResp = new HalfScreenCouponClaimResp();
        Coupon coupon = couponService.selectCouponById(couponId);
        log.info("coupon 库存:quota:{},已经发送quotaSend:{},limit:{}", coupon.getQuota(), coupon.getQuotaSend(), (coupon.getQuota() - coupon.getQuotaSend()));
        if (coupon.getQuota() != -1 && (coupon.getQuota() - coupon.getQuotaSend()) <= 0) {
            throw new BaseException("Failed to collect coupon ", 500);
        }
        if (Objects.nonNull(coupon.getLimitCount()) && coupon.getLimitCount() != -1) {
            long count2 = userCouponService.findCount(userId, coupon.getId());
            if (count2 >= coupon.getLimitCount()) {
                throw new BaseException("Failed to collect coupon", 500);
            }
        }
        UserCoupon userCoupon = sendUserCouponByCouponConfig(coupon.getId(), coupon, userId);
        halfScreenCouponClaimResp.setCouponAmount(userCoupon.getAmount());
        halfScreenCouponClaimResp.setCouponType(coupon.getCouponType());
//		halfScreenCouponClaimResp.setCouponTypeDesc(CouponTypeEnum.findByType(coupon.getCouponType()).getName());
        halfScreenCouponClaimResp.setCouponTypeDesc(coupon.getTitle());
        return halfScreenCouponClaimResp;
    }

    /**
     * 发送用户优惠券
     *
     * @param couponId
     * @param coupon
     * @param userId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public UserCoupon sendUserCouponByCouponConfig(Long couponId, Coupon coupon, Long userId) {
        CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, userId);
        UserCoupon userCoupon = BeanUtil.copyBean(currencyAmount, UserCoupon.class);
        userCoupon.setCouponId(couponId);
        userCoupon.setSourceType(coupon.getType());
        userCoupon.setUserId(userId);
        userCoupon.setCouponMainType(coupon.getCouponMainType());
        userCoupon.setGmtStart(coupon.getGmtStart());
        ZonedDateTime now = ZonedDateTime.now();
        if (coupon.getExpiryType() == 1) {
            userCoupon.setGmtStart(now);
            userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
        } else if (coupon.getExpiryType() == 2) {
            userCoupon.setGmtStart(coupon.getGmtStart());
            userCoupon.setGmtEnd(coupon.getGmtEnd());
        }
        userCoupon.setStatus(0);
        userCoupon.setAmount(currencyAmount.getAmount());
        userCoupon.setDiscount(coupon.getDiscount());
        userCouponService.insert(userCoupon);
        return userCoupon;
    }


    /**
     * 路由信息补充
     *
     * @param userCouponList
     * @param appType
     * @param user
     */
    private void wrapperRouteInfo(Page userCouponList, Integer appType, ZnsUserEntity user) {
        List<CouponPageVo> records = userCouponList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        try {
            List<Long> routeIds = records.stream().filter(s -> Objects.equals(s.getIsActivityCategory(), 0))
                    .filter(item -> Objects.equals(item.getCouponMainType(), CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_1.type)).map(CouponPageVo::getRouteId).toList();
            if (CollectionUtils.isEmpty(routeIds)) {
                return;
            }
            List<AppRouteConfig> appRouteConfigs = appRouteConfigService.findListByIds(routeIds);
            Map<Long, AppRouteConfig> routeConfigMap = appRouteConfigs.stream().collect(Collectors.toMap(AppRouteConfig::getId, Function.identity()));
            records.forEach(i -> {
                AppRouteConfig appRouteConfig = routeConfigMap.get(i.getRouteId());
                List<ActivityCouponConfig> listed = activityCouponConfigService.list(new QueryWrapper<ActivityCouponConfig>()
                        .eq("is_delete", YesNoStatus.NO.getCode()).eq("coupon_id", i.getCouponId()));
                getUserCouponRouteAndRouteParam(i, listed, appType, user, appRouteConfig);
                if (Objects.equals(i.getIsActivityCategory(), 1)) {
                    i.setRouteParam("{\"activityCategoryId\":" + i.getRouteId() + ",\"subType \":" + 0 + "}");
                    i.setRoute("lznative://home/<USER>");
                }
            });
        } catch (Exception e) {
            log.error("wrapperRouteInfo error,e:", e);
        }

    }


    /**
     * 获取用户券路由信息
     *
     * @param i
     * @param listed
     * @param appType
     * @param userEntity
     * @param appRouteConfig
     */
    private void getUserCouponRouteAndRouteParam(CouponPageVo i, List<ActivityCouponConfig> listed, Integer appType, ZnsUserEntity userEntity, AppRouteConfig appRouteConfig) {
        if (Objects.isNull(appRouteConfig)) {
            return;
        }
        if (Arrays.asList(1, 2, 5, 6).contains(i.getCouponType())) {
            if (CollectionUtils.isEmpty(listed)) {
                i.setRouteParam("");
                // 无限制 多人统一跑 跳转无参数
                i.setRoute(appRouteConfig.getMainRoute());
            } else if (CollectionUtils.size(listed) == 1) {
                ActivityCouponConfig activityCouponConfig = listed.get(0);
                if (activityCouponConfig.getType() == 0) {
                    // 无限制 多人统一跑
                    i.setRoute(appRouteConfig.getMainRoute());
                    i.setRouteParam(appRouteConfig.getMainParam());
                }
                if (activityCouponConfig.getType() == 2) {
                    // 单场id
                    Long activityId = Long.valueOf(activityCouponConfig.getCouponConfig());
                    i.setRoute(appRouteConfig.getMainRoute());
                    i.setRouteParam(getRouteParamReplaceActivityId(appRouteConfig, activityId));
                }
                if (activityCouponConfig.getType() == 1) {
                    // 活动类型 跳转列表
                    String activityType = activityCouponConfig.getCouponConfig();
                    Integer type = Integer.valueOf(activityType);
                    if (Arrays.asList(4, 5).contains(type)) {
                        // Race/OfficialRaceList 无参数
                        i.setRoute(appRouteConfig.getMainRoute());
                    } else if (3 == type) {
                        //首页
                        i.setRoute(appRouteConfig.getMainRoute());
                        i.setRouteParam(appRouteConfig.getMainParam());
                    } else if (8 == type) {
                        // 跳转h5
                        i.setRoute(appRouteConfig.getMainRoute());
                    } else if (11 == type) {
                        i.setRoute(appRouteConfig.getMainRoute());
                        i.setRouteParam(appRouteConfig.getMainParam());
                    } else {
                        i.setRoute(appRouteConfig.getMainRoute());
                        i.setRouteParam(appRouteConfig.getMainParam());
                    }
                }
                if (activityCouponConfig.getType() == 3) {
                    ActivityTaskConfig taskConfig = activityTaskConfigService.findById(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityByTaskConfigId(taskConfig.getId());
                    i.setRoute(appRouteConfig.getMainRoute());
                    i.setRouteParam(getRouteParamReplaceActivityId(appRouteConfig, znsRunActivityEntity.getId()));
                }
            } else {
                //多条限制 多活动明细
                List<Long> activities = new ArrayList<>();
                // 活动类型
                if (listed.get(0).getType() == 1) {
                    i.setRoute(appRouteConfig.getMainRoute());
                    i.setRouteParam(appRouteConfig.getMainParam());
                }
                if (listed.get(0).getType() == 2) {
                    listed.forEach(k -> activities.add(Long.valueOf(k.getCouponConfig())));
                    List<ZnsRunActivityEntity> znsRunActivityEntities = runActivityService.findByIds(activities);
                    // 所有未开始活动
                    List<ZnsRunActivityEntity> collect = znsRunActivityEntities.stream().filter(k -> k.getActivityState() == 0 || k.getActivityState() == 1).collect(Collectors.toList());
                    List<ZnsRunActivityEntity> userNoRunActivity = new ArrayList<>();
                    ZonedDateTime activityTime = null;
                    ZnsRunActivityEntity znsRunActivityEntityFinal = null;
                    for (ZnsRunActivityEntity znsRunActivityEntity : collect) {
                        //查询报名状态
                        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(znsRunActivityEntity.getId(), userEntity.getId());
                        // 已报名状态
                        if (Objects.nonNull(activityUser)) {
                            if (activityTime != null) {
                                if (activityTime.compareTo(znsRunActivityEntity.getActivityStartTime()) > 0) {
                                    activityTime = znsRunActivityEntity.getActivityStartTime();
                                    znsRunActivityEntityFinal = znsRunActivityEntity;
                                }
                            } else {
                                activityTime = znsRunActivityEntity.getActivityStartTime();
                                znsRunActivityEntityFinal = znsRunActivityEntity;
                            }
                        } else {
                            userNoRunActivity.add(znsRunActivityEntity);
                        }
                    }
                    // 有报名
                    if (Objects.nonNull(znsRunActivityEntityFinal)) {
                        if (CollectionUtils.isEmpty(userNoRunActivity)) {
                            i.setRoute(appRouteConfig.getMainRoute());
                            i.setRouteParam(getRouteParamReplaceActivityId(appRouteConfig, znsRunActivityEntityFinal.getId()));
                        } else {
                            // 有报名/未报名 取未时间最近
                            userNoRunActivity.stream().min(Comparator.comparing(ZnsRunActivityEntity::getActivityStartTime)).ifPresent(ac -> {
                                i.setRoute(appRouteConfig.getMainRoute());
                                i.setRouteParam(getRouteParamReplaceActivityId(appRouteConfig, ac.getId()));
                            });
                        }
                    } else {
                        // 无报名时间最近
                        userNoRunActivity.stream().min(Comparator.comparing(ZnsRunActivityEntity::getActivityStartTime)).ifPresent(ac -> {
                            i.setRoute(appRouteConfig.getMainRoute());
                            i.setRouteParam(getRouteParamReplaceActivityId(appRouteConfig, ac.getId()));
                        });
                    }
                }
                // 聚合活动路由
                if (listed.get(0).getType() == 3) {
                    List<Long> taskIds = Lists.newArrayList();
                    listed.forEach(k -> taskIds.add(Long.valueOf(k.getCouponConfig())));
                    List<ActivityTaskConfig> taskConfigs = activityTaskConfigService.findByIds(taskIds);
                    Collections.shuffle(taskConfigs);
                    ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityByTaskConfigId(taskConfigs.get(0).getId());
                    i.setRoute(appRouteConfig.getMainRoute());
                    i.setRouteParam(getRouteParamReplaceActivityId(appRouteConfig, znsRunActivityEntity.getId()));
                }
            }
        } else {
            // 客服跳转/钱包跳转
            // 判断用户是否是plus会员
            i.setRoute(appRouteConfig.getMainRoute());
            i.setRouteParam(getRouteParamCashCoupon(appType, appRouteConfig, userEntity, i.getCouponType()));
        }
        //兼容钱包路径ios/安卓不一致
        if (RouteConfigEnum.COUPON_WALLET.getOneLevel().equals(appRouteConfig.getOneLevel()) && RouteConfigEnum.COUPON_WALLET.getTwoLevel().equals(appRouteConfig.getTwoLevel())) {
            i.setRoute(appType == 1 ? appRouteConfig.getSecondaryRoute() : appRouteConfig.getMainRoute());
        }

    }


    private static String getRouteParamReplaceActivityId(AppRouteConfig couponRouteConfig, Long activityId) {
        Map<String, Object> json = JsonUtil.readValue(couponRouteConfig.getMainParam());
        if (Objects.isNull(json)) {
            return couponRouteConfig.getMainParam();
        } else {
            json.put("activityId", activityId);
            return JsonUtil.writeString(json);
        }
    }

    /**
     * 跳转钱包参数替换
     *
     * @param appType
     * @param couponRouteConfig
     * @param userEntity
     * @param couponType
     * @return
     */
    private String getRouteParamCashCoupon(Integer appType, AppRouteConfig couponRouteConfig, ZnsUserEntity userEntity, Integer couponType) {
        Map<String, Object> json = new HashMap<>();
        if (couponType == 3) {
            if (appType == 2) {
                json = JsonUtil.readValue(couponRouteConfig.getMainParam());
            }
        } else {
            String chat_robot_user_ids = sysConfigService.selectConfigByKey("chat_robot_user_ids");
            //独立站抵扣券只挑普通客服
            if (!Objects.equals(couponType, CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
                if (Objects.equals(userEntity.getMemberType(), 1)) {
                    chat_robot_user_ids = sysConfigService.selectConfigByKey(ConfigKeyEnums.chat_robot_vip_user_ids.getCode());
                }
            }
            if (StringUtils.hasText(chat_robot_user_ids)) {
                String[] split = chat_robot_user_ids.split(",");
                String chatRobotUserId = split[0];
                ZnsUserEntity znsUser = userService.findById(Long.valueOf(chatRobotUserId));
                if (Objects.nonNull(znsUser)) {
                    String mainParam = couponRouteConfig.getMainParam();
                    if (StringUtils.hasText(mainParam)) {
                        mainParam = "{}";
                    }
                    json = JsonUtil.readValue(mainParam);
                    json.put("chatName", znsUser.getFirstName());
                    json.put("chatId", String.valueOf(znsUser.getId()));
                    json.put("showName", znsUser.getFirstName());
                    json.put("userId", String.valueOf(znsUser.getId()));
                }
            }

        }
        return json == null ? "" : JsonUtil.writeString(json);
    }

    /**
     * 检测活动使用券
     *
     * @param couponType
     * @param userCoupon
     * @param activity
     * @param activityUser
     * @return
     */
    private Result checkUseCouponToActivity(Integer couponType, UserCoupon userCoupon, ZnsRunActivityEntity activity, ZnsRunActivityUserEntity activityUser) {
        if (Objects.isNull(activityUser)) {
            log.info("未参加活动，活动用户不存在");
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.race.first"));
        }
        //一场比赛只能用一次券
        UserCoupon one = userCouponService.findByQuery(new UserCouponQuery().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId()).setStatus(List.of(2)));
        if (Objects.nonNull(one)) {
            log.info("一场比赛只能使用一次");
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.count.exceed"));
        }
        if (couponType == 1) {
            if (activity.getActivityType() != 4) {
                // 参赛必胜券 + 官方
                log.info("只能在官方多人同跑使用");
                return CommonResult.fail(I18nMsgUtils.getMessage("coupon.not.available"));
            }
        } else if (couponType == 2) {
        }
        return null;
    }

    private Result checkActivityTime(ZnsRunActivityEntity activity) {
        if (DateUtil.betweenMinutes(activity.getActivityStartTime(), DateUtil.getNowDate()) > 30 || activity.getActivityState() == 2) {
            log.info("超过30分钟/活动已经结束");
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.time.overDue"));
        }
        return null;
    }

    /**
     * @param user
     * @param mainActivity
     * @param activityUser
     * @return
     */
    private Result checkUseCouponToNewActivity(ZnsUserEntity user, MainActivity mainActivity, ZnsRunActivityUserEntity activityUser) {
        if (Objects.isNull(activityUser)) {
            log.info("未参加活动，活动用户不存在");
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.race.first"));
        }
        //一场比赛只能用一次券
        UserCoupon one = userCouponService.findByQuery(new UserCouponQuery().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId()).setStatus(List.of(2)));
        if (Objects.nonNull(one)) {
            log.info("一场比赛只能使用一次");
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.count.exceed"));
        }
        //检查是否能用券
        Integer allowCouponSwitch = activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(),
                        ActivitySettingConfigEnum.ALLOW_AWARD_COUPON_SWITCH)
                .map(ap -> {
                    try {
                        return Integer.parseInt(ap.getParamValue());
                    } catch (NumberFormatException e) {
                        log.error("ALLOW_AWARD_COUPON_SWITCH参数格式错误: {}", ap.getParamValue());
                        return 1; // 格式错误时默认允许
                    }
                })
                .orElse(0); // 配置不存在时默认不允许使用券

        if (allowCouponSwitch == 0) {
            log.info("活动[id={}]禁止使用优惠券", mainActivity.getId());
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.coupon.not.available"));
        }

        Long entByZone = DateUtil.getStampByZone(mainActivity.getActivityEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId());

        if (System.currentTimeMillis() > entByZone) {
            return CommonResult.fail(I18nMsgUtils.getMessage("coupon.use.time.overDue"));
        }

        return null;
    }

    /**
     * 用户卷列表第二版
     */
    public Page<CouponPageV2Vo> getUserCouponListV2(UserCouponListRequestDto req, ZnsUserEntity user, Integer appType) {
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(user, null);
        //查询用户券
        CourseListRequest po = new CourseListRequest();
        if (user.getAppVersion() < VersionConstant.V4_6_4){
            //老版本只展示美国券
            po.setUserCouponCountryCode(I18nConstant.CountryCodeEnum.US.getCode());
        }
        po.setPageNum(req.getPageNum());
        po.setPageSize(req.getPageSize());
        po.setCouponListStatus(req.getCouponListStatus());
        po.setGmtCreateGt(ZonedDateTime.now().plusMonths(-6));
        po.setSortStr(" uc.gmt_create DESC");
        po.setMallCountryCode(mallCountryCode);
        Page<CouponPageVo> pageVo = getUserCouponList(po, user, appType);
        Page<CouponPageV2Vo> result = new Page<>(pageVo.getCurrent(), pageVo.getSize(), pageVo.getTotal());
        List<CouponPageVo> records = pageVo.getRecords();
        if (org.springframework.util.CollectionUtils.isEmpty(records)) {
            return result;
        }

        //查询商城优惠券Dto
        Map<Long, OrderPickCouponDto> couponDtoMap = new HashMap<>();
        List<Long> userCouponIds = records.stream().filter(item -> CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type.equals(item.getCouponMainType())).map(CouponPageVo::getUserCouponId).toList();
        if (!org.springframework.util.CollectionUtils.isEmpty(userCouponIds)) {
            List<UserCoupon> userCoupons = userCouponService.findListByIds(userCouponIds);
            List<Long> couponIds = userCoupons.stream().map(UserCoupon::getCouponId).distinct().toList();
            List<Coupon> coupons = couponService.findListByIds(couponIds);
            Map<Long, Coupon> couponMap = coupons.stream().collect(Collectors.toMap(Coupon::getId, Function.identity(), (k1, k2) -> k2));
            for (UserCoupon userCoupon : userCoupons) {
                Coupon coupon = couponMap.get(userCoupon.getCouponId());
                if (coupon == null) {
                    continue;
                }
                //获取商城券详情
                OrderPickCouponDto mallCouponDto = getUserMallCouponDto(coupon, userCoupon, user);
                mallCouponDto.setGmtStart(ZonedDateTimeUtil.convertFrom(userCoupon.getGmtStart()));
                mallCouponDto.setGmtEnd(ZonedDateTimeUtil.convertFrom(userCoupon.getGmtEnd()));
                couponDtoMap.put(userCoupon.getId(), mallCouponDto);
            }
        }

        //转换格式
        List<CouponPageV2Vo> recordsV2List = new ArrayList<>();
        for (CouponPageVo record : records) {
            OrderPickCouponDto mallCouponDto = couponDtoMap.get(record.getUserCouponId());
            CouponPageV2Vo couponPageV2Vo = new CouponPageV2Vo(record);
            couponPageV2Vo.setMallCouponDto(mallCouponDto);
            recordsV2List.add(couponPageV2Vo);
            //状态转换
            convertStatus(couponPageV2Vo);
        }
        result.setRecords(recordsV2List);
        return result;
    }

    /**
     * 获取商城券详情
     */
    public OrderPickCouponDto getUserMallCouponDto(Coupon coupon, UserCoupon userCoupon, ZnsUserEntity user) {
        MallCouponDto mallCouponDto = mallCouponConvertComponent.appConvertDto(coupon, user,true, null);
        mallCouponDto.setUserCouponId(userCoupon.getId());
        //获取用户券列表不可用原因
        Set<UserCouponConstant.UnAvailableReasonEnum> reasonEnumList = mallCouponComponent.checkUserCoupon(coupon,userCoupon);
        StringJoiner remarkJoiner =  new StringJoiner(";");
        int isAvailable = 1;
        if (!org.springframework.util.CollectionUtils.isEmpty(reasonEnumList)) {
            for (UserCouponConstant.UnAvailableReasonEnum reasonEnum : reasonEnumList) {
                if (reasonEnum == UserCouponConstant.UnAvailableReasonEnum.REASON_7){
                    //国家不可用需要特殊转换
                    String countryNames = coupon.getCountryCodes().stream().map(countryCode -> I18nConstant.CountryCodeEnum.findByCode(countryCode).enName).collect(Collectors.joining(","));
                    remarkJoiner.add(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "coupon.check.reason.REASON_7",countryNames));
                }else {
                    remarkJoiner.add(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "coupon.check.reason." + reasonEnum)) ;
                }
            }
            isAvailable = 0;
        }
        return new OrderPickCouponDto(mallCouponDto, isAvailable, remarkJoiner.toString());
    }

    /**
     * 新老状态修改
     * 老状态的available=新状态的available
     * 老状态的using+used=新状态的used
     * 老状态的expired+invalid=新状态的unavailable
     * 老：0:未使用 1:使用中 2:已使 3:过期 4:已失效
     * 新：优惠券列表状态，1：可用、2：不可用、3：已用
     */
    private void convertStatus(CouponPageV2Vo couponPageV2Vo) {
        if (couponPageV2Vo.getMallCouponDto() != null && couponPageV2Vo.getMallCouponDto().getIsAvailable() == 0){
            couponPageV2Vo.setCouponListStatus(2);
            return;
        }
        if (Objects.equals(couponPageV2Vo.getStatus(), 0)) {
            couponPageV2Vo.setCouponListStatus(1);
            return;
        }
        if (List.of(1, 2).contains(couponPageV2Vo.getStatus())) {
            couponPageV2Vo.setCouponListStatus(3);
            return;
        }
        couponPageV2Vo.setCouponListStatus(2);
    }
}
