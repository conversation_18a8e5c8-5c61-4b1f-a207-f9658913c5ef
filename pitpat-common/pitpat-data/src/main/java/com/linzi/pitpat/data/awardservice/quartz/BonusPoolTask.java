package com.linzi.pitpat.data.awardservice.quartz;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.awardservice.service.BonusPoolLogService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 奖金池任务
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20  dsw
 **/
@Component("bonusPoolTask")
@Slf4j
public class BonusPoolTask {
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private BonusPoolLogService bonusPoolLogService;
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;

    /**
     * 定时增加奖金池累计金额
     */
    public void addBonusPool() {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey("bonus.pool.data");

        String bonusData = sysConfig.getConfigValue();
        Map<String, Object> bonusDataObject = JsonUtil.readValue(bonusData);
        BigDecimal growthRange = MapUtil.getBigDecimal(bonusDataObject.get("growthRange"));

        BigDecimal bonusPoolAmount = MapUtil.getBigDecimal(bonusDataObject.get("bonusPoolAmount"));
        if (Objects.isNull(bonusPoolAmount)) {
            bonusPoolAmount = BigDecimal.ZERO;
        }
        BigDecimal add = bonusPoolAmount.add(growthRange);
        bonusDataObject.put("bonusPoolAmount", add);

        sysConfig.setConfigValue(JsonUtil.writeString(bonusDataObject));
        sysConfigService.updateConfig(sysConfig);
        //添加记录
        bonusPoolLogService.addLog(add, 1);
    }

    /**
     * 更新奖金池数据
     */
    //@Scheduled(cron = "0 0 0 * * ?")
    public void updateBonusPoolData() {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey("bonus.pool.data");

        String bonusData = sysConfig.getConfigValue();
        Map<String, Object> bonusDataObject = JsonUtil.readValue(bonusData);
        //已发放奖励
        ZonedDateTime startDate = DateTimeUtil.parse("2022-08-01 09:00:00");
        BigDecimal issuedAward = userAccountDetailService.getIssuedAwardNoCache(startDate);
        bonusDataObject.put("lastIssuedAward", issuedAward);
        Integer issuedUserCount = userAccountDetailService.getIssuedUserCountNoCache(startDate);
        bonusDataObject.put("lastIssuedUserCount", issuedUserCount);
        sysConfig.setConfigValue(JsonUtil.writeString(bonusDataObject));
        sysConfigService.updateConfig(sysConfig);
    }

    /**
     * 更新奖金池数据
     */
    //@Scheduled(cron = "0 0 0 * * ?")
    public void updateIssuedAwardAndUserCount() {
        //更新奖金池数据
        ZonedDateTime startDate = DateTimeUtil.parse("2022-08-01 09:00:00");
        userAccountDetailService.updateIssuedAwardAndUserCount(startDate);
    }
}
