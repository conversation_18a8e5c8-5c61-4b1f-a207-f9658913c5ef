package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 活动主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.dto.api.request.pro.RaceProActivityPageQueryRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProModuleCount;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityConditionQuery;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivityDto;
import com.linzi.pitpat.data.activityservice.model.dto.RaceProModuleActivityDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCompetitiveListQuery;
import com.linzi.pitpat.data.activityservice.model.query.MainActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.MyRaceCalendarActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.MyRecordActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.ToBeInvolvedActivityQuery;
import com.linzi.pitpat.data.activityservice.model.vo.EquipmentActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MainActivityVO;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRaceCalendarActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRecordActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ToBeInvolvedActListVo;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.ZonedDateTime;
import java.util.List;


@Mapper
public interface MainActivityMapper extends BaseMapper<MainActivity> {

    @Select("SELECT IFNULL((select max(id) from zns_main_activity),0)")
    Long getMaxId();

    List<MyRaceCalendarActivityListVo> findMyRaceCalendarActivity(@Param("query") MyRaceCalendarActivityQuery query);

    List<MyRecordActivityListVo> myRecordActList(Page page, @Param("query") MyRecordActivityQuery query);

    List<ToBeInvolvedActListVo> toBeInvolvedActList(@Param("query") ToBeInvolvedActivityQuery query);

    List<ActivityTypeDto> findProceedActivity(@Param("userId") Long userId, @Param("currentTime") String currentTime, @Param("dataSource") Integer dataSource, @Param("deviceType") Integer deviceType);

    List<ActivityTypeDto> findProceedActivityByCompetitionFormat(@Param("competitionFormat") Integer competitionFormat);

    List<MainActivity> findAllNoEndActivity(Page<MainActivity> page, @Param("currentTime") String currentTime, @Param("activityIds") List<Long> activityIds,
                                            @Param("mainTypes") List<String> mainTypes, @Param("stateCode") String stateCode, @Param("countryCode") String countryCode, @Param("groupsByUserId") List<Long> groupsByUserId
            , @Param("flag") Boolean flag, @Param("checkUser") boolean checkUser);

    /**
     * 查询可以报名的活动,越早开赛越靠前
     *
     * @param currentTime      用户当前时区时间，
     * @param mainTypes        活动类型，
     * @param stateCode        用户州code，
     * @param groupsByUserId   用户所在分组id，
     * @param checkFee         免费活动是否无版本号限制，true：无限制，false：有限制，
     * @param equipmentVersion 用户设备版本号，不能为空
     * @param applyStartTime   报名最早开始时间 ，不能为空
     * @param startEndTime     活动最晚开始时间
     * @return
     */
    List<MainActivityVO> findCanApplyActivityId(@Param("currentTime") String currentTime,
                                                @Param("mainTypes") List<String> mainTypes,
                                                @Param("stateCode") String stateCode,
                                                @Param("countryCode") String countryCode,
                                                @Param("groupsByUserId") List<Long> groupsByUserId,
                                                @Param("checkFee") boolean checkFee,
                                                @Param("equipmentVersion") Integer equipmentVersion,
                                                @Param("applyStartTime") String applyStartTime,
                                                @Param("startEndTime") String startEndTime,
                                                @Param("equipmentModels") List<String> equipmentModels
    );

    @DS("slave")
    List<MainActivity> findBeginActivity(@Param("startTime") ZonedDateTime startTime);

    @DS("slave")
    List<MainActivity> findNotFinishActivity(ZonedDateTime startTime);

    @DS("slave")
    List<MainActivity> findToEndActivity(@Param("endTime") ZonedDateTime endTime);

    List<MainActivity> findEndActivityByIds(Page<MainActivity> page, @Param("currentTime") String currentTime, @Param("activityIds") List<Long> activityIds, @Param("mainTypes") List<String> mainTypes);

    Page<ActivityDto> findActivityByCondition(Page<ActivityDto> page, @Param("query") ActivityConditionQuery query);

    List<MainActivity> findListByRotSetting(@Param("query") MainActivityQuery query);

    List<MainActivity> findListByIdsAndState(Page<MainActivity> page, @Param("activityIds") List<Long> activityIds, @Param("areaCode") String areaCode, @Param("countryCode") String countryCode,
                                             @Param("groupsByUserId") List<Long> groupsByUserId, @Param("currentTime") String currentTime, @Param("checkUser") boolean checkUser);

    MainActivity findOneNoEndActivityUnlimited(@Param("userTime") String userTime, @Param("nowTime") String nowTime, @Param("dataTime") ZonedDateTime dataTime, @Param("countryCode") String countryCode, @Param("stateCode") String stateCode);


    List<MainActivity> findNoEndRateLimitActivity(@Param("userTime") String userTime, @Param("nowTime") String nowTime, @Param("dataTime") ZonedDateTime dataTime);

    List<MainActivity> findAreaActivity(@Param("list") List<Long> actIdList, @Param("stateCode") String stateCode, @Param("countryCode") String countryCode);


    List<MainActivity> selectBeforeSingleBegin(@Param("currentTime") ZonedDateTime now);

    List<MainActivity> findTargetTypeActivity(String userTime, String nowTime, Integer targetType, String stateCode, @Param("countryCode") String countryCode, ZonedDateTime dataTime);


    /**
     * 显示了在更多中的系列赛活动和
     *
     * @param count 支持上架的最大数量
     * @return
     */
    int countShowInMoreActivity(@Param("maxCount") int count, @Param("equipmentMainType") Integer equipmentMainType);

    /**
     * 获取竞技赛查询列表
     *
     * @param query
     * @return
     */
    List<MainActivity> findCompetitiveActivityList(@Param("query") ActivityCompetitiveListQuery query);

    List<MainActivity> findClubEventActivity();


    List<MainActivity> findTeamActivity();

    List<MainActivity> findEndNoSendAwardActivity();

    /**
     * 查询设备进行中的的活动
     *
     * @param equipmentModel 设备型号
     * @param num            查询数量,为空查询所有的
     */
    List<EquipmentActivityVo> findActivityByEquipmentModel(@Param("equipmentModel") String equipmentModel, @Param("languageCode") String languageCode,
                                                           @Param("num") Integer num, @Param("groupsByUserId") List<Long> groupsByUserId
            , @Param("stateCode") String stateCode, @Param("countryCode") String countryCode, @Param("checkUser") boolean checkUser, @Param("currentTime") String currentTime);

    /**
     * 获取社区推荐的活动
     *
     * @param limit
     * @return
     */
    List<MainActivity> findRecommendCommunityActivityList(@Param("limit") int limit);

    @Select("""
            select * from zns_main_activity where activity_state in (0,1) and is_delete = 0 and time_style = 0
            and is_show_in_more = 1 and application_start_time between #{minus} and #{now}
            """)
    List<MainActivity> findShowInMoreStartApplicationByDate(@Param("now") ZonedDateTime now, @Param("minus") ZonedDateTime minus);

    @Select("SELECT * FROM zns_main_activity WHERE play_id = #{playId} AND activity_state IN (-1,0,1) AND is_delete = 0")
    @DS("slave")
    List<MainActivity> findUseActivityByPlayId(Long playId);

    List<MainActivity> findMarathonActivityEndApplication();

    /**
     * 查询职业赛的活动列凑
     *
     * @param query
     * @return
     */
    List<RaceProModuleActivityDto> findProActivityRace(@Param("query") RaceProActivityPageQueryRequest query);

    Long proActivityRaceCount(@Param("query") RaceProActivityPageQueryRequest query);

    List<RaceProModuleCount> findProActivityTypeCountMinStartTime(@Param("query") RaceProActivityPageQueryRequest query);

    /**
     * 查询在指定时间范围内的职业赛活动ID列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 职业赛活动ID列表
     */
    List<Long> findProActivityIdsByTimeRange(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<MainActivity> findEndNoSendAwardFeeChallengeActivity();

    List<Long> findProActivityIdsByTime(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);
}
