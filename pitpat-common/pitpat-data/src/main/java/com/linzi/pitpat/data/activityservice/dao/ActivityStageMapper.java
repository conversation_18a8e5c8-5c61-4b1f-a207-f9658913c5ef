package com.linzi.pitpat.data.activityservice.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import org.apache.ibatis.annotations.Mapper;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 活动阶段表 服务类
 *
 * @since 2024-11-22
 */
@Mapper
public interface ActivityStageMapper extends BaseMapper<ActivityStage> {


    List<ActivityStage> findListNeedWait(ZonedDateTime now);

    List<ActivityStage> findListWillStart(int beforeMin);
}
