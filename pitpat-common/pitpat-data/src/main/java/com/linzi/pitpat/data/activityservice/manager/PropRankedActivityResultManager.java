package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.PropRankedActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.PropRankActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.PropRankedLevelEnums;
import com.linzi.pitpat.data.activityservice.constant.enums.SourceType;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.PropActivityUserGradeReportDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PropRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevelLog;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.query.PropRankedUserQuery;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PropRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelLogService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.PropRankedConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillWhiteListService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventSourceEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.biz.UserExpLevelBizService;
import com.linzi.pitpat.data.userservice.dto.event.UserExpSendEvent;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.bo.UserExpIncreDto;
import com.linzi.pitpat.data.userservice.model.query.UserExpConfigQuery;
import com.linzi.pitpat.data.userservice.service.UserExpConfigService;
import com.linzi.pitpat.data.userservice.service.UserExpDetailService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 赛事结果
 *
 * <AUTHOR>
 * @date 2023/12/4 10:40
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PropRankedActivityResultManager {

    private final ZnsUserService znsUserService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RedissonClient redissonClient;
    private final PropRankedLevelService propRankedLevelService;
    private final PropRunRankedActivityUserService runRankedActivityUserService;
    private final ISysConfigService sysConfigService;
    private final PropUserRankedLevelService userRankedLevelService;
    private final PropUserRankedLevelLogService userRankedLevelLogService;
    private final MainActivityService mainActivityService;
    private final AwardActivityBizService awardActivityBizService;
    private final PropRankedActivityBizService propRankedActivityBizService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private static final String RANKED_AWARD_SETTLE_PREFIX = "prop_ranked:award:settle:";
    private final UserExpConfigService userExpConfigService;
    private final UserExpDetailService userExpDetailService;
    private final UserTaskDetailService userTaskDetailService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final UserExpLevelBizService userExpLevelBizService;
    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(5);
    private final ApplicationEventPublisher applicationEventPublisher;
    private final TreadmillWhiteListService treadmillWhiteListService;
    private final SeriesActivityRelService seriesActivityRelService;

    private final QueueMessageService queueMessageService;
    /**
     * 活动结束后更新用户段位隐藏分
     *
     * @param userRunDataDetail
     */
    @Transactional
    public void updateRankedActivityUserHideScore(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        PropUserRankedLevel userRankedLevel = userRankedLevelService.findByUserIdForLock(userRunDataDetail.getUserId());
        if (Objects.isNull(userRankedLevel)) {
            log.info("用户段位赛未初始化，或者是机器人，无需更新，userRunDataDetailId={}, userId={}，hideScore={}, isRobot={}", userRunDataDetail.getId(), userRunDataDetail.getUserId());
            return;
        }
        //计算用户当前隐藏分
        BigDecimal hideScore = propRankedActivityBizService.calcUserHideScore(userRunDataDetail.getUserId());
        hideScore = Optional.ofNullable(hideScore).orElse(BigDecimal.ZERO);

        //更新段位隐藏分
        userRankedLevel.setScore(hideScore);
        userRankedLevelService.updateScore(userRankedLevel.getUserId(), userRankedLevel.getScore());
        log.info("用户活动结束，更新用户段位赛隐藏分userRunDataDetailId={}, userId={}", userRunDataDetail.getId(), userRunDataDetail.getUserId());
    }


    private void sendRankedActivityAward(MainActivity mainActivity, ZnsUserRunDataDetailsEntity userRunDataDetail, int rank, PropRunRankedActivityUser runRankedActivityUser, PropUserRankedLevel userRankedLevel) {
        log.info("道具赛发放排名奖励，activityId={},userId={},ranking={}", userRunDataDetail.getActivityId(), userRunDataDetail.getUserId(), rank);
        //排名奖励
        sendAward(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), userRunDataDetail.getDistanceTarget().intValue(), userRunDataDetail.getTimeTarget(), rank, userRunDataDetail.getId(), null);

        log.info("道具赛发放完赛奖励，activityId={},userId={}", mainActivity.getId(), runRankedActivityUser.getId());
        //完赛奖励
        sendAward(AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), runRankedActivityUser.getTargetRunMileage(), runRankedActivityUser.getTargetRunTime(), 0, userRunDataDetail.getId(), OrderUtil.getBatchNo());
        //段位奖励
        if (Objects.equals(userRankedLevel.getIsNewLevel(), Boolean.FALSE)) {
            log.info("用户未获未获得新的段位，userId={}, rankedLevelId={},highRankedLevelId={}", runRankedActivityUser.getId(), userRankedLevel.getRankedLevelId(), userRankedLevel.getHighRankedLevelId());
            return;
        }
        PropRankedLevelEnums rankedLevelEnum = PropRankedLevelEnums.resolve(userRankedLevel.getLevel(), userRankedLevel.getRank());
        if (runRankedActivityUser.getIsPlacement() == 1) {
            //定位赛结束，需要发放定位赛奖励
            if (runRankedActivityUserService.getCurrentRankSegment(userRunDataDetail.getUserId()) == 3) {
                //创建无解线程池
                ExecutorService executorService = Executors.newCachedThreadPool();
                while (Objects.nonNull(rankedLevelEnum)) {
                    PropRankedLevelEnums finalRankedLevelEnum = rankedLevelEnum;
                    Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
                    executorService.execute(() -> {
                        Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                        log.info("道具赛定位赛发放段位奖励 子线程id {}，activityId={},userId={},ranking={}", Thread.currentThread().getId(), mainActivity.getId(), runRankedActivityUser.getId(), rank);
                        sendAward(AwardSentTypeEnum.RANK_AWARD.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), userRunDataDetail.getDistanceTarget().intValue(), userRunDataDetail.getTimeTarget(), finalRankedLevelEnum.getIndex(), userRunDataDetail.getId(), null);
                    });
                    rankedLevelEnum = rankedLevelEnum.resolvePreviousRank(rankedLevelEnum.getLevel(), rankedLevelEnum.getRank());
                }
                //关闭线程池
                executorService.shutdown();
                try {
                    executorService.awaitTermination(20, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } else {
            log.info("道具赛发放段位奖励，activityId={},userId={},ranking={}", mainActivity.getId(), runRankedActivityUser.getId(), rank);
            sendAward(AwardSentTypeEnum.RANK_AWARD.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), userRunDataDetail.getDistanceTarget().intValue(), userRunDataDetail.getTimeTarget(), rankedLevelEnum.getIndex(), userRunDataDetail.getId(), null);

        }

    }


    private PropUserRankedLevel updateUserRankedLevel(ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank, PropUserRankedLevel userRankedLevel) {


        //处于定位中
        if (userRankedLevel.getIsInPlacement() == 1) {
            //定位赛处理
            userRankedLevel = placementUpdateUserRankedLevel(userRankedLevel, userRunDataDetail, userRank);

        } else {
            //普通段位赛处理
            userRankedLevel = rankUpdateUserRankedLevel(userRankedLevel, userRunDataDetail, userRank);
        }

        return userRankedLevel;
    }

    private PropUserRankedLevel rankUpdateUserRankedLevel(PropUserRankedLevel userRankedLevel, ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {
        Long userId = userRankedLevel.getUserId();
//        String sysConfig = sysConfigService.selectConfigByKey("new_ranked_activity_config");
//        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);
//        List<HashMap<String, Object>> rankedProgressList = (List<HashMap<String, Object>>) rankedSysConfig.get("rankedProgressList");
//        //获取可能的段位升级、或回落的进度
//        HashMap<String, Object> rankedProcessMap = rankedProgressList.stream().filter(item -> Objects.equals(MapUtils.getInteger(item, "ranking"), userRank)).findFirst().orElse(null);
//
//        if (Objects.isNull(rankedProcessMap)) {
//            log.error("无法获取该段位的奖励分值，userId={}, rank={}", userRunDataDetail.getActivityId(), userRank);
//            return null;
//        }
        BigDecimal growLevelProgress = sysConfigService.getGrowLevelProgress(userRunDataDetail.getDistanceTarget(), userRank);

        PropUserRankedLevel updateUserRanked = new PropUserRankedLevel();
        BeanUtils.copyProperties(userRankedLevel, updateUserRanked);
        log.info("当前用户段位信息， level={}, rank={}, levelId={}", userRankedLevel.getLevel(), userRankedLevel.getRank(), userRankedLevel.getRankedLevelId());
        //当场活动中获取新的段位进度
        PropRankedLevel rankedLevel = propRankedLevelService.findById(userRankedLevel.getRankedLevelId());
//        BigDecimal growLevelProgress = BigDecimal.valueOf(MapUtils.getDoubleValue(rankedProcessMap, "progress"));

        //根据当前用户的隐藏分，再次计算用户段位进度
        if (userRankedLevel.getScore().compareTo(rankedLevel.getMinScore()) < 0 || userRankedLevel.getScore().compareTo(rankedLevel.getMaxScore()) > 0) {
            BigDecimal averageLevelScore = (rankedLevel.getMinScore().add(rankedLevel.getMaxScore())).divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
            BigDecimal averageScore = userRankedLevel.getScore().divide(averageLevelScore, 2, RoundingMode.HALF_UP);
            log.info("用户的隐藏分不在段位区间，需要增加权重，userId={},userScore={}, minScore={}, maxScore={},growLevelProgress={}, averageScore={}", userId, userRankedLevel.getScore(), rankedLevel.getMinScore(), rankedLevel.getMaxScore(), growLevelProgress, averageScore);
            if (userRank <= 4) {
                if (averageScore.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("计算后的隐藏分为0，重置为1");
                    averageScore = BigDecimal.ONE;
                }
                growLevelProgress = growLevelProgress.multiply(averageScore).setScale(2, RoundingMode.HALF_UP);
            } else {
                if (averageScore.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("计算后的隐藏分为0，重置为0.01");
                    averageScore = BigDecimal.valueOf(0.01);
                }
                growLevelProgress = growLevelProgress.divide(averageScore, 2, RoundingMode.HALF_UP);
            }
            log.info("重新计算隐藏分后,growLevelProgress={}", growLevelProgress);
        }

        //是否发放段位奖励
        PropRankedLevelEnums newRankedLevel = null;
        BigDecimal levelProgress = userRankedLevel.getLevelProgress();
        BigDecimal newLevelProgress = BigDecimal.ZERO;

        if (growLevelProgress.compareTo(BigDecimal.ZERO) > 0) {
            newLevelProgress = levelProgress.add(growLevelProgress);
            //用户的新段位
            if (newLevelProgress.compareTo(BigDecimal.ONE) > -1) {
                //段位升级
                newRankedLevel = PropRankedLevelEnums.resolveNextRank(userRankedLevel.getLevel(), userRankedLevel.getRank());
                if (Objects.nonNull(newRankedLevel)) {
                    newLevelProgress = BigDecimal.ZERO; //大于1， 升段位，重新重制
                    log.info("段位升级，新段位");
                } else {
                    newLevelProgress = BigDecimal.ONE; //最大段位，保持进度100%
                    log.info("已经升到最高段位");
                }
            }
        } else {
            if (levelProgress.compareTo(BigDecimal.ZERO) == 0) {
                //段位降级
                newRankedLevel = PropRankedLevelEnums.resolvePreviousRank(userRankedLevel.getLevel(), userRankedLevel.getRank());
                log.info("降级从{}到段位：{},进度={}", userRankedLevel, newRankedLevel, newLevelProgress);
                if (Objects.nonNull(newRankedLevel)) {
                    newLevelProgress = BigDecimal.ONE.add(growLevelProgress);
                }
            } else {
                newLevelProgress = levelProgress.add(growLevelProgress);
            }
            //小于0
            if (newLevelProgress.compareTo(BigDecimal.ZERO) < 0) {
                newLevelProgress = BigDecimal.ZERO;
            }
        }
        //段位四舍五入
        newLevelProgress = newLevelProgress.setScale(2, RoundingMode.HALF_UP);
        log.info("用户段位赛中的获得段位进度，userId={}, rank={},newLevelProgress={}, levelProgress={},growLevelProgress={},activityId={}", userId, userRank, newLevelProgress, levelProgress, growLevelProgress, userRunDataDetail.getActivityId());

        if (Objects.nonNull(newRankedLevel)) {
            rankedLevel = propRankedLevelService.findByLevelAndRank(newRankedLevel.getLevel(), newRankedLevel.getRank());
            log.info("新段位信息={}", newRankedLevel.getIndex());
        }

        log.info("计算后，用户当前的段位进度，userId={}, levelId={}, newLevelId={},", userId, userRankedLevel.getId(), rankedLevel.getId());

        if (Objects.nonNull(newRankedLevel)) {
            PropRankedLevel newRankedLevelEntity = propRankedLevelService.findByLevelAndRank(newRankedLevel.getLevel(), newRankedLevel.getRank());
            if (newRankedLevelEntity.getId() > userRankedLevel.getHighRankedLevelId()) {
                updateUserRanked.setIsNewLevel(true);
                updateUserRanked.setActivityId(userRunDataDetail.getActivityId());
                updateUserRanked.setHighRankedLevelId(newRankedLevelEntity.getId());
            } else {
                //已获取最新的段位信息
                log.info("用户已获取该段位的奖励，不再重复发放，userId={}, newRankedLevelId={},highRankedLevelId={} bonus", userId, newRankedLevelEntity.getId(), userRankedLevel.getHighRankedLevelId());
            }
        }
        //更新段位信息
        //如果不展示新段位奖励，就展示段位奖励Toast
        if (Objects.equals(userRankedLevel.getIsNewLevel(), Boolean.FALSE)) {
            updateUserRanked.setShowAwardPop(true);
        }

        updateUserRanked.setLevelProgress(newLevelProgress);
        updateUserRanked.setRank(rankedLevel.getRank());
        updateUserRanked.setLevel(rankedLevel.getLevel());
        updateUserRanked.setName(rankedLevel.getName());
        updateUserRanked.setRankedLevelId(rankedLevel.getId());

        userRankedLevelService.update(updateUserRanked);
        createUserRankedLevelLog(updateUserRanked, levelProgress, growLevelProgress, userRank, userRunDataDetail);

        return updateUserRanked;
    }

    private PropUserRankedLevel placementUpdateUserRankedLevel(PropUserRankedLevel userRankedLevel, ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {
        Long userId = userRankedLevel.getUserId();
        Integer segment = runRankedActivityUserService.getCurrentRankSegment(userId);
        log.info("当前用户阶段为{}", segment);

        //最后一场定位赛 已经完成了两场 这是最后一场
        if (segment == 3) {
            Integer level = runRankedActivityUserService.getCurrentPlacementRankLevel(userId);

//            if (userRank == 1){
//                level = level + 1;
//            }
            log.info("当前用户level为{}", level);

            //根据level初始化段位等级
            PropRankedLevelEnums rankedLevelEnum = PropRankActivityConstants.PLACEMENT_SCORE_MAP.get(Math.min(level, 3));

            PropRankedLevel rankedLevel = propRankedLevelService.findByLevelAndRank(rankedLevelEnum.getLevel(), rankedLevelEnum.getRank());
            userRankedLevel.setIsNewLevel(true);
            userRankedLevel.setActivityId(userRunDataDetail.getActivityId());
            userRankedLevel.setHighRankedLevelId(rankedLevel.getId());
            userRankedLevel.setLevelProgress(BigDecimal.ZERO.setScale(2));
            userRankedLevel.setRank(rankedLevel.getRank());
            userRankedLevel.setLevel(rankedLevel.getLevel());
            userRankedLevel.setName(rankedLevel.getName());
            userRankedLevel.setRankedLevelId(rankedLevel.getId());
            userRankedLevel.setIsInPlacement(0);
            //更新用户定位隐藏分

            userRankedLevel.setPlacementScore(PropRankActivityConstants.SEGMENT_SCORE_MAP.get(Math.min(level, 3)));
            userRankedLevelService.update(userRankedLevel);


        }
        return userRankedLevel;
    }

    private void createUserRankedLevelLog(PropUserRankedLevel userRankedLevel, BigDecimal levelProgress, BigDecimal growLevelProgress, Integer userRank, ZnsUserRunDataDetailsEntity userRunDataDetail) {
        if (userRunDataDetail.getIsRobot() == 0) {
            PropUserRankedLevelLog userRankedLevelLog = new PropUserRankedLevelLog();
            BeanUtils.copyProperties(userRankedLevel, userRankedLevelLog, "id", "gmtCreate", "gmtModified");
            userRankedLevelLog.setLevelProgressGrow(growLevelProgress);
            userRankedLevelLog.setLevelProgressPreview(levelProgress);
            userRankedLevelLog.setRanking(userRank);
            userRankedLevelLog.setRunDataDetailId(userRunDataDetail.getId());
            userRankedLevelLog.setActivityId(userRunDataDetail.getActivityId());
            userRankedLevelLogService.insert(userRankedLevelLog);
            log.info("保存用户段位赛数据日志，userId={}, userRank={}, isRobot={}", userRankedLevelLog.getUserId(), userRank, userRunDataDetail.getIsRobot());
        } else {
            log.info("机器人不记录段位赛用户数据日志userId={}，userRunDataDetail={}", userRankedLevel.getUserId(), userRunDataDetail.getId());
        }
    }

    private void sendAward(Integer type, Integer targetType, Long userId, Long activityId, Integer targetRunMileage, Integer targetRunTime, Integer rank, Long runDataDetailsId, String batchNo) {
        log.info("sendAward,type={},targetType={}", type, targetType);
        //奖励发放
        AwardSendDto awardSendDto = new AwardSendDto();
        awardSendDto.setType(type);
        if (targetType == 1) {
            awardSendDto.setTarget(targetRunMileage);
        } else if (targetType == 2) {
            awardSendDto.setTarget(targetRunTime);
        }
        awardSendDto.setUserId(userId);
        awardSendDto.setActivityId(activityId);
        awardSendDto.setRank(rank);
        awardSendDto.setDetailId(runDataDetailsId);
        awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);
    }


    public void deleteRunRankedActivityUser(Long runDataDetailId) {
        PropRunRankedActivityUser runRankedActivityUser = runRankedActivityUserService.findByRunDataDetailsId(runDataDetailId);
        if (Objects.nonNull(runRankedActivityUser)) {
            log.info("运动数据小于1分钟，或速度异常，删除段位赛的用户数据：userId={},id={}", runRankedActivityUser.getUserId(), runRankedActivityUser.getId());
            runRankedActivityUserService.deleteById(runRankedActivityUser.getId());
        }
    }

    public void createRunRankedActivityUser(ZnsUserRunDataDetailsEntity userRunDataDetail, int type) {
        log.info("znsUserRunDataDetailsEntity ,getRunMileage={}, getDistanceTarget={}", userRunDataDetail.getRunMileage(), userRunDataDetail.getDistanceTarget());
        PropRunRankedActivityUser propRunRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
        if(Objects.nonNull(propRunRankedActivityUser)){
            log.info("用户已存在报名数据，不重复创建");
            return;
        }
        PropRunRankedActivityUser runRankedActivityUser = new PropRunRankedActivityUser();
        runRankedActivityUser.setActivityId(userRunDataDetail.getActivityId());
        runRankedActivityUser.setUserId(userRunDataDetail.getUserId());
        runRankedActivityUser.setRunDataDetailsId(userRunDataDetail.getId());

        runRankedActivityUser.setRank(-1);
        if (userRunDataDetail.getDistanceTarget() != null) {
            runRankedActivityUser.setTargetRunMileage(userRunDataDetail.getDistanceTarget().intValue());
        } else {
            //更新target
            PropRunRankedActivityUser realUser = runRankedActivityUserService.findListByActivityId(userRunDataDetail.getActivityId()).stream()
                    .filter(k -> k.getIsRobot() == 0).findFirst().orElse(null);
            if (realUser != null) {
                runRankedActivityUser.setTargetRunMileage(realUser.getTargetRunMileage());
                userRunDataDetail.setDistanceTarget(BigDecimal.valueOf(realUser.getTargetRunMileage()));
                userRunDataDetailsService.update(userRunDataDetail);
            }
        }

        runRankedActivityUser.setTargetRunTime(userRunDataDetail.getTimeTarget());

        runRankedActivityUser.setIsTest(userRunDataDetail.getIsTest());
        runRankedActivityUser.setIsRobot(userRunDataDetail.getIsRobot());
        runRankedActivityUser.setIsComplete(0);
        if (userRunDataDetail.getIsRobot() == 1) {
            runRankedActivityUser.setIsPlacement(0);
        }
        PropUserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(userRunDataDetail.getUserId());
        if (Objects.nonNull(userRankedLevel)) {
            if (userRankedLevel.getIsInPlacement() == 1) {
                Integer segment = runRankedActivityUserService.getCurrentRankSegment(userRankedLevel.getUserId());
                if (segment < 3) {
                    runRankedActivityUser.setIsPlacement(1);
                }
            }
        }
        runRankedActivityUser.setSourceType(type);
        log.info("创建道具赛的用户数据：userId={},userRunDataDetailId={}", userRunDataDetail.getUserId(), userRunDataDetail.getId());
        runRankedActivityUserService.insert(runRankedActivityUser);
    }

    /**
     * 用户道具赛奖励结算
     *
     * @param rankedUserQuery
     * @param source          ,处理来源，queue，job，api
     */
    @Transactional(rollbackFor = Exception.class)
    public void rankAwardSettle(PropRankedUserQuery rankedUserQuery, String source) {
        log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，来源：{}", rankedUserQuery, source);

        //加锁
        String lockKey = RANKED_AWARD_SETTLE_PREFIX + rankedUserQuery.getUserId() + ":" + rankedUserQuery.getActivityId();
        RLock lock = redissonClient.getLock(lockKey);
        LockHolder.tryLock(lock, 2, () -> {
            PropRunRankedActivityUser runRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(rankedUserQuery.getActivityId(), rankedUserQuery.getUserId());
            if (runRankedActivityUser == null) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，道具赛报名记录不存在", rankedUserQuery);
                return null;
            }
            ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.getFirstUserActivityRunDataDetails(rankedUserQuery.getUserId(), rankedUserQuery.getActivityId());
            if (userRunDataDetail == null) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，道具赛运动记录不存在", rankedUserQuery);
                return null;
            }
            if (runRankedActivityUser.getIsComplete() == 0) {
                log.info("用户道具赛奖励结算,入参：{}，游戏状态未上报结束", rankedUserQuery);
                return null;
            }
            Integer riskReview = userRunDataDetailsCheatService.isRiskReview(Collections.singletonList(rankedUserQuery.getActivityId()));
            if (riskReview == 1) {
                log.info("用户道具赛奖励结算,入参：{}，作弊审核中", rankedUserQuery);
                return null;
            }
            if (Objects.equals(userRunDataDetail.getIsCheat(), 1)) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，跑步作弊,不发奖励", rankedUserQuery);
                //跑步作弊,不发奖励
                runRankedActivityUser.setRank(-1);
                runRankedActivityUser.setAwardSendStatus(PropRankedConstant.AwardSendEnum.AWARD_SEND_2.code);
                runRankedActivityUser.setRemark(source + "__跑步作弊");
                runRankedActivityUser.setRankedLevelChange(PropRankedConstant.LevelChangeEnum.LEVEL_CHANGE_0.code);//段位不变
                runRankedActivityUser.setRankedScoreChange(BigDecimal.ZERO);//升级分数 0
                runRankedActivityUserService.update(runRankedActivityUser);
                return null;
            }
            if (!Objects.equals(userRunDataDetail.getRunStatus(), 1)) {
                log.info("用户道具赛奖励结算,入参：{}，跑步未完成,不发奖励", rankedUserQuery);

                return null;
            }

            //更新成绩
            if (runRankedActivityUser.getRank() < 0) {
                //获取排名
                Integer rank = getRankedActivityRank(rankedUserQuery);
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，排名：{}", rankedUserQuery, rank);
                if (rank == null) {
                    return null;
                }

                if (rank == 1) {
                    userTaskDetailService.initUserDailyTask(rankedUserQuery.getUserId());
                    // 每日任务，道具赛第一名
                    userTaskDetailService.completeLevelTask(rankedUserQuery.getUserId(), UserExpObtainSubTypeEnum.WIN_RACE_ONCE.getCode(), true);
                }


                //查询老段位
                PropUserRankedLevel oldLevel = userRankedLevelService.findByUserIdForLock(rankedUserQuery.getUserId());

                //更新段位
                PropUserRankedLevel newLevel = updateUserRankedLevel(userRunDataDetail, rank, oldLevel);
                if (newLevel == null) {
                    log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，更新段位失败", rankedUserQuery);
                    return null;
                }

                //更新道具赛排名 、段位升降信息
                updateRankedActivityUserRankInfo(runRankedActivityUser, oldLevel, newLevel, rank, source);
            } else {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，排名已更新", rankedUserQuery);
            }

            //奖励发放
            runRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(rankedUserQuery.getActivityId(), rankedUserQuery.getUserId());
            if (PropRankedConstant.AwardSendEnum.AWARD_SEND_2.code.equals(runRankedActivityUser.getAwardSendStatus())) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，奖励已发放", rankedUserQuery);
                return null;
            }
            if (runRankedActivityUser.getRank() < 0) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，用户未完赛", rankedUserQuery);
                return null;
            }
            try {
                //发奖励
                MainActivity mainActivity = mainActivityService.findById(rankedUserQuery.getActivityId());
                PropUserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(rankedUserQuery.getUserId());
                sendRankedActivityAward(mainActivity, userRunDataDetail, runRankedActivityUser.getRank(), runRankedActivityUser, userRankedLevel);
                runRankedActivityUser.setAwardSendStatus(PropRankedConstant.AwardSendEnum.AWARD_SEND_2.code);
                runRankedActivityUser.setRemark(source);
            } catch (Exception e) {
                log.error("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，奖励发放异常", rankedUserQuery, e);
                String message = e.getMessage();
                if (message.length() > 950) {
                    message = message.substring(0, 950);
                }
                message = source + "__" + message;
                runRankedActivityUser.setAwardSendStatus(PropRankedConstant.AwardSendEnum.AWARD_SEND_3.code);
                runRankedActivityUser.setRemark(message);
            }

            //更新奖励发放状态
            runRankedActivityUserService.update(runRankedActivityUser);
            log.info("RankedActivityResultManager#rankAwardSettle-----用户道具赛奖励结算,入参：{}，用户道具赛奖励结算结束", rankedUserQuery);
            return null;
        });
    }

    /**
     * 更新道具赛排名 、段位升降信息
     *
     * @param runRankedActivityUser
     * @param oldLevel
     * @param newLevel
     * @param rank
     */
    private void updateRankedActivityUserRankInfo(PropRunRankedActivityUser runRankedActivityUser, PropUserRankedLevel oldLevel, PropUserRankedLevel newLevel, Integer rank, String source) {
        //段位变化（1:不变，2:升级，3:降级）
        Integer rankedLevelChange = PropRankedConstant.LevelChangeEnum.LEVEL_CHANGE_0.code;
        if (newLevel.getLevel() > oldLevel.getLevel() || (newLevel.getLevel().equals(oldLevel.getLevel()) && newLevel.getRank() > oldLevel.getRank())) {
            //升级
            rankedLevelChange = PropRankedConstant.LevelChangeEnum.LEVEL_CHANGE_1.code;
        } else if (newLevel.getLevel() < oldLevel.getLevel() || (oldLevel.getLevel().equals(newLevel.getLevel()) && newLevel.getRank() < oldLevel.getRank())) {
            //降级
            rankedLevelChange = PropRankedConstant.LevelChangeEnum.LEVEL_CHANGE_2.code;
        }

        // 段位分数变化(分正负)
        BigDecimal rankedScoreChange = BigDecimal.ZERO;
        if (PropRankedConstant.LevelChangeEnum.LEVEL_CHANGE_0.code.equals(rankedLevelChange)) {
            //没有段位变化，变化分数 = 新段位进度 - 老段位进度
            rankedScoreChange = (newLevel.getLevelProgress().subtract(oldLevel.getLevelProgress())).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        } else if (PropRankedConstant.LevelChangeEnum.LEVEL_CHANGE_1.code.equals(rankedLevelChange)) {
            //升级，变化分数 = 100 - 老段位进度
            rankedScoreChange = (BigDecimal.ONE.subtract(oldLevel.getLevelProgress())).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        } else {
            //降级，变化分数 = -(100 - 新段位进度)
            rankedScoreChange = (BigDecimal.ONE.subtract(newLevel.getLevelProgress())).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).multiply(new BigDecimal("-1"));
        }

        //更新排名+进度
        runRankedActivityUser.setRank(rank);
        runRankedActivityUser.setRemark(source);
        runRankedActivityUser.setRankedLevelChange(rankedLevelChange);
        runRankedActivityUser.setRankedScoreChange(rankedScoreChange);
        runRankedActivityUserService.update(runRankedActivityUser);
    }

    /**
     * 获取道具赛排名
     *
     * @param rankedUserQuery
     */
    public Integer getRankedActivityRank(PropRankedUserQuery rankedUserQuery) {
        RLock lock = redissonClient.getLock(RedisConstants.PROP_RANK_CAL + rankedUserQuery.getActivityId());
        return LockHolder.tryLock(lock, 2, () -> {
            Long activityId = rankedUserQuery.getActivityId();
            //已经有序
            List<PropRunRankedActivityUser> activityUsers = runRankedActivityUserService.findListByActivityIdAndNoCheat(activityId);
            for (int i = 0; i < activityUsers.size(); i++) {
                PropRunRankedActivityUser propRunRankedActivityUser = activityUsers.get(i);
                if (propRunRankedActivityUser.getUserId().equals(rankedUserQuery.getUserId())) {
                    propRunRankedActivityUser.setRank(i + 1);
                    runRankedActivityUserService.update(propRunRankedActivityUser);
                    return i + 1;
                }
            }
            log.info("用户跑步数据不存在");
            return null;
        });
    }

    public void gradeReport(PropActivityUserGradeReportDto reportDto) {
        ZnsUserEntity user = znsUserService.findById(reportDto.getUserId());
        PropRunRankedActivityUser activityUser = runRankedActivityUserService
                .findByActivityIdAndUserId(reportDto.getActivityId(), reportDto.getUserId());
        if (activityUser == null) {
            log.info("用户{}在活动{}没有数据", reportDto.getUserId(), reportDto.getActivityId());
            return;
        }
        Integer rank = reportDto.getRank();
        activityUser.setRunMileage(reportDto.getRunMileage());
        Integer lastRunTime = activityUser.getRunTime();
        activityUser.setRunTime(reportDto.getRunTime() / 1000);
        activityUser.setRunTimeMils(reportDto.getRunTime());
        boolean isComplete = false;
        if (rank != null && rank > 0) {
            activityUser.setIsComplete(1);
            activityUser.setCompleteTime(ZonedDateTime.now());
            isComplete = true;
        }
        activityUser.setGameReportStatus(1);
        MainActivity mainActivity = mainActivityService.findById(reportDto.getActivityId());

        if (MainActivityTypeEnum.isFreeChallengeActivity(mainActivity.getMainType())){
            if((lastRunTime == 0 || lastRunTime > activityUser.getRunTime()) && isComplete){
                ZnsUserRunDataDetailsEntity lastByActivityId = userRunDataDetailsService.findLastByActIds(reportDto.getUserId(), List.of(reportDto.getActivityId()));
                if(Objects.nonNull(lastByActivityId)){
                    activityUser.setRunDataDetailsId(lastByActivityId.getId());
                    reportDto.setDetailId(lastByActivityId.getId());
                    // 道具模式上榜结果记录
                    lastByActivityId.setRunMileageRanking(0);
                    userRunDataDetailsService.update(lastByActivityId);
                }
                runRankedActivityUserService.update(activityUser);
            }
        }else {
            runRankedActivityUserService.update(activityUser);
        }

        ZnsUserRunDataDetailsEntity userRunDataDetails = userRunDataDetailsService.findById(activityUser.getRunDataDetailsId());
        if(MainActivityTypeEnum.isFreeChallengeActivity(mainActivity.getMainType()) && reportDto.getDetailId() != 0){
            userRunDataDetails = userRunDataDetailsService.findById(reportDto.getDetailId());
        }
        if (MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
            log.info("活动old 用户赛活动 不进行后续逻辑处理,只是保存道具游戏数据activityId:{}", mainActivity.getId());
            syncActivityUserNow(activityUser);
            return;
        }else if (MainActivityTypeEnum.isFreeChallengeActivity(mainActivity.getMainType())) {
            log.info("活动free_challenge 用户赛活动 不进行后续逻辑处理,只是保存道具游戏数据activityId:{}", mainActivity.getId());
            if((lastRunTime == 0 || lastRunTime > activityUser.getRunTime()) && isComplete){
                log.info("用户{}在活动{}成绩有更好的更新", reportDto.getUserId(), mainActivity.getId());
                activityUser.setRank(0);
                syncActivityUserNow(activityUser);
                // LA 设备判定 同步数据
                if(treadmillWhiteListService.checkEquipmentWhiteList(userRunDataDetails.getTreadmillId())){
                    List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
                    MainActivity subMainMainActivity = allMainActivity.get(0);
                    PropRunRankedActivityUser laActivityUser = runRankedActivityUserService
                            .findByActivityIdAndUserId(subMainMainActivity.getId(), reportDto.getUserId());
                    if (laActivityUser == null) {
                        log.info("用户{}在活动{}没有数据", reportDto.getUserId(), reportDto.getActivityId());
                        return;
                    }
                    laActivityUser.setRunMileage(reportDto.getRunMileage());
                    laActivityUser.setRunTime(reportDto.getRunTime() / 1000);
                    laActivityUser.setRunTimeMils(reportDto.getRunTime());
                    // 完赛 = 上榜
                    laActivityUser.setRank(0);
                    laActivityUser.setIsComplete(1);
                    laActivityUser.setCompleteTime(ZonedDateTime.now());
                    laActivityUser.setGameReportStatus(1);
                    if(Objects.nonNull(reportDto.getDetailId())){
                        laActivityUser.setRunDataDetailsId(reportDto.getDetailId());
                    }
                    runRankedActivityUserService.update(laActivityUser);
                    syncActivityUserNow(laActivityUser);
                }
            }else {
                log.info("用户{}在活动{}成绩没有更好的不做更新", reportDto.getUserId(), mainActivity.getId());
            }
            return;
        } else {
            //同步activityUser表
            syncActivityUser(activityUser, MDC.getCopyOfContextMap());
        }
        SpringContextUtils.getBean(PropRankedActivityResultManager.class).rankAwardSettle(new PropRankedUserQuery(reportDto.getActivityId(), reportDto.getUserId()), "game");

        //发布turbolink事件
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(),new TurbolinkApplicationEvent(TurbolinkEventEnum.GLOBAL_EVENT, user.getId(), Map.of("complete_lucky_dash", Boolean.TRUE.toString()), TurbolinkEventSourceEnum.GAME));
        if (Objects.isNull(userRunDataDetails)) {
            return;
        }
        UserExpConfigQuery userExpConfigQuery = UserExpConfigQuery.builder().obtainTypeList(List.of(UserExpObtainTypeEnum.EVENT_EXPERIENCE.getCode())).build();
        List<UserExpConfig> list = userExpConfigService.findList(userExpConfigQuery);
        UserExpConfig eventExpConfig;
        eventExpConfig = list.stream().filter(e -> UserExpObtainSubTypeEnum.DAILY_RACE.getCode().equals(e.getObtainSubType())).findFirst().get();
        if (!Objects.equals(activityUser.getIsComplete(), 1)) {
            eventExpConfig = null;
        }
        UserExpIncreDto exp = userExpDetailService.calculateExpAndInsertDetail(activityUser.getRunDataDetailsId(), activityUser.getUserId(), user.getMemberType(), new UserExpIncreDto(), eventExpConfig);
        if (exp.getNewExperience() > 0) {
            userExpLevelBizService.sendUserExpAndAward(activityUser.getUserId(), exp.getNewExperience());
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserExpSendEvent.getEventType(),UserExpSendEvent.of(activityUser.getUserId(), exp));
        }
    }

    private void syncActivityUserNow(PropRunRankedActivityUser propRunRankedActivityUser) {
        if (propRunRankedActivityUser.getIsComplete() == 1) {
            ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(propRunRankedActivityUser.getActivityId(), propRunRankedActivityUser.getUserId());
            if(propRunRankedActivityUser.getSourceType().equals(SourceType.USER_FREE_CHALLENGE_RACE.getType())){
                // 完赛 = 上榜
                activityUser.setRank(0);
            }
            activityUser.setIsComplete(1);
            activityUser.setCompleteTime(propRunRankedActivityUser.getCompleteTime());
            znsRunActivityUserService.updateById(activityUser);
            log.info("end update syncActivityUserNow:{}", activityUser);
        }
    }

    @Transactional
    public void test() {
        userRankedLevelService.findByUserId(94552l);
        userRankedLevelService.findByUserId(94552l);
        userRankedLevelService.findByUserIdForLock(94552l);
    }

    public void syncActivityUser(PropRunRankedActivityUser propRunRankedActivityUser, Map<String, String> copyOfContextMap) {
        log.info("come syncActivityUser");
        if (propRunRankedActivityUser.getIsComplete() == 1) {
            log.info("start syncActivityUser:{}", propRunRankedActivityUser);

            executorService.schedule(() -> {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(propRunRankedActivityUser.getActivityId(), propRunRankedActivityUser.getUserId());
                activityUser.setIsComplete(1);
                activityUser.setCompleteTime(propRunRankedActivityUser.getCompleteTime());
                znsRunActivityUserService.updateById(activityUser);
                log.info("end update ActivityUser:{}", activityUser);
            }, 30, TimeUnit.SECONDS);

        }
    }

}
