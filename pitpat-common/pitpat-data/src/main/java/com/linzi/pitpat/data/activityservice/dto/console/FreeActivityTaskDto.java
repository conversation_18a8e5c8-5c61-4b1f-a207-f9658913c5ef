package com.linzi.pitpat.data.activityservice.dto.console;

import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class FreeActivityTaskDto {

    private Long activityId;

    //任务名称
    private String taskName;
    /**
    *  状态
     * @see com.linzi.pitpat.data.enums.ActivityStateEnum
    *
    * */
    private Integer activityStatus;
    //开始时间
    private ZonedDateTime startTime;
    //结束时间
    private ZonedDateTime endTime;

}
