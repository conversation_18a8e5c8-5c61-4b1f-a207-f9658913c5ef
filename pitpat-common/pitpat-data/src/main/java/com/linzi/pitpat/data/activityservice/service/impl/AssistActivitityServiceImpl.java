package com.linzi.pitpat.data.activityservice.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.activityservice.constant.enums.AssistActivityStatusEnum;
import com.linzi.pitpat.data.activityservice.mapper.AssistActivitityDao;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.query.AssistActivitityQuery;
import com.linzi.pitpat.data.activityservice.model.request.AssistActivitySearchRequest;
import com.linzi.pitpat.data.activityservice.model.vo.AssistActivityDropListVo;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class AssistActivitityServiceImpl implements AssistActivitityService {


    @Autowired
    private AssistActivitityDao assistActivitityDao;


    @Override
    public AssistActivitity selectAssistActivitityById(Long id) {
        return assistActivitityDao.selectAssistActivitityById(id);
    }

    @Override
    public Page<AssistActivitity> searchAssistActivityByConditon(AssistActivitySearchRequest assistActivitySearchRequest) {
        String name = assistActivitySearchRequest.getName();
        ZonedDateTime startTime = assistActivitySearchRequest.getStartTime();
        ZonedDateTime endTime = assistActivitySearchRequest.getEndTime();
        //start_time<=startTime<=endTime<=end_time
        Integer progressStatus = assistActivitySearchRequest.getProgressStatus();
        //全部:不限制
        //未开始:now<startTime
        //进行中startTime<=now<endTime
        //已结束:now<endTime
        ZonedDateTime now = ZonedDateTime.now();

        Page<AssistActivitity> page = new Page<>(assistActivitySearchRequest.getPageNum(), assistActivitySearchRequest.getPageSize());
        page = assistActivitityDao.selectPage(page, new QueryWrapper<AssistActivitity>().lambda()
                .select(AssistActivitity::getId, AssistActivitity::getName,
                        AssistActivitity::getStartTime, AssistActivitity::getEndTime,
                        AssistActivitity::getCreateTime, AssistActivitity::getStatus,
                        AssistActivitity::getActivityUrl)
                .like(StringUtils.hasText(name), AssistActivitity::getName, name)
                .ge(Objects.nonNull(startTime), AssistActivitity::getStartTime, startTime)
                .le(Objects.nonNull(endTime), AssistActivitity::getEndTime, endTime)
                .gt(Objects.equals(progressStatus, AssistActivityStatusEnum.NOT_STARTED.getStatusCode()), AssistActivitity::getStartTime, now)
                .le(Objects.equals(progressStatus, AssistActivityStatusEnum.IN_PROGRESS.getStatusCode()), AssistActivitity::getStartTime, now)
                .ge(Objects.equals(progressStatus, AssistActivityStatusEnum.IN_PROGRESS.getStatusCode()), AssistActivitity::getEndTime, now)
                .gt(Objects.equals(progressStatus, AssistActivityStatusEnum.ENDED.getStatusCode()), AssistActivitity::getEndTime, now)
                .eq(AssistActivitity::getIsDelete, 0)
                .orderByDesc(AssistActivitity::getId)
        );
        List<AssistActivitity> assistActivitityList = page.getRecords();
        if (CollectionUtils.isEmpty(assistActivitityList)) {
            return page;
        }

        // 填充活动进度状态字段
        fillProgressStatus(now, assistActivitityList);
        return page;
    }

    private static void fillProgressStatus(ZonedDateTime now, List<AssistActivitity> assistActivitityList) {
        assistActivitityList.forEach(assistActivitity -> {
            ZonedDateTime assistActivitityStartTime = assistActivitity.getStartTime();
            ZonedDateTime assistActivitityEndTime = assistActivitity.getEndTime();
            if (assistActivitityStartTime == null) {
                return;
            }
            if (assistActivitityEndTime == null) {
                return;
            }
            if (now.isBefore(assistActivitityStartTime)) {
                assistActivitity.setProgressStatus(AssistActivityStatusEnum.NOT_STARTED.getStatusCode());
            } else if (assistActivitityStartTime.isBefore(now) && now.isBefore(assistActivitityEndTime)) {
                assistActivitity.setProgressStatus(AssistActivityStatusEnum.IN_PROGRESS.getStatusCode());
            } else if (now.isAfter(assistActivitityEndTime)) {
                assistActivitity.setProgressStatus(AssistActivityStatusEnum.ENDED.getStatusCode());
            }
        });
    }


    @Override
    public List<AssistActivityDropListVo> dropList() {
        return assistActivitityDao.selectByStatusAndGmtEnd(1, ZonedDateTime.now());
    }


    @Override
    public void updateAssistActivitityById(AssistActivitity updateAssistActivitity) {
        assistActivitityDao.updateAssistActivitityById(updateAssistActivitity);
    }

    @Override
    public void assistActivityAutoDownShelfTask() {
        List<AssistActivitity> activitityList = this.assistActivitityDao.selectList(
                new QueryWrapper<AssistActivitity>().lambda()
                        .eq(AssistActivitity::getIsDelete, 0)
                        .eq(AssistActivitity::getStatus, 1)
        );
        if (CollectionUtils.isEmpty(activitityList)) {
            log.info("执行结束 AssistActivityAutoDownShelfTask...");
            return;
        }
        ZonedDateTime now = ZonedDateTime.now();
        Set<Long> idSet = new HashSet<>(activitityList.size());
        for (AssistActivitity assistActivitity : activitityList) {
            ZonedDateTime endTime = assistActivitity.getEndTime();
            if (now.isAfter(endTime)) {
                idSet.add(assistActivitity.getId());
            }
        }
        if (CollectionUtils.isEmpty(idSet)) {
            log.info("执行结束 AssistActivityAutoDownShelfTask...");
            return;
        }
        AssistActivitity assistActivitity = new AssistActivitity();
        assistActivitity.setStatus(-1);
        this.assistActivitityDao.update(assistActivitity, new QueryWrapper<AssistActivitity>().lambda()
                .in(AssistActivitity::getId, idSet));
    }

    @Override
    public AssistActivitity findOne(AssistActivitityQuery build) {
        return this.assistActivitityDao.selectOne(buildWrapper(build), false);
    }

    @Override
    public long findCount(AssistActivitityQuery build) {
        return this.assistActivitityDao.selectCount(buildWrapper(build));
    }

    @Override
    public void save(AssistActivitity assistActivitity) {
        assistActivitityDao.insert(assistActivitity);
    }

    @Override
    public void updateById(AssistActivitity newAssistActivity) {
        assistActivitityDao.updateById(newAssistActivity);
    }


    private LambdaQueryWrapper<AssistActivitity> buildWrapper(AssistActivitityQuery query) {
        return Wrappers.lambdaQuery(AssistActivitity.class)
                .select(!CollectionUtils.isEmpty(query.getSelect()), query.getSelect())
                .eq(Objects.nonNull(query.getId()), AssistActivitity::getId, query.getId())
                .eq(Objects.nonNull(query.getIsDelete()), AssistActivitity::getIsDelete, query.getIsDelete())
                .eq(Objects.nonNull(query.getStatus()), AssistActivitity::getStatus, query.getStatus())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query))
                ;
    }
}
