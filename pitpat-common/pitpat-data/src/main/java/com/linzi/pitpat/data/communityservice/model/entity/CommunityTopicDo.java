package com.linzi.pitpat.data.communityservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.lang.BaseDo;
import lombok.Data;
import lombok.experimental.Accessors;

import static com.baomidou.mybatisplus.annotation.FieldStrategy.ALWAYS;


/**
 * 社区话题表 DO对象
 *
 * @since 2025年2月14日
 */
@Data
@Accessors(chain = true)
@TableName("zns_community_topic")
public class CommunityTopicDo extends BaseDo {

    //
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    // 话题名称
    private String name;
    // 话题图片
    private String pic;
    // 话题描述
    private String description;
    // 话题类型
    private String type;
    // 创建人
    private Long creator;
    // 来源0后台，1app
    private Integer source;
    // 排序
    private Integer sort;
    // 是否展示0否1是
    private Integer isShow;
    // 参与人数
    private Integer participantNum;
    // 关联帖子数
    private Long contentNum;
    // 点赞数
    private Integer likeNum;
    // 评论数
    private Integer commentNum;
    // 热度
    private Integer heat;
    // 创建人code
    private String creatorCode;

    // 默认语言
    private String defaultLangCode;

    // 话题分类（1商城，2赛事，3游戏，4健身，5平台活动）
    @TableField(updateStrategy = ALWAYS)
    private Integer classify;


}
