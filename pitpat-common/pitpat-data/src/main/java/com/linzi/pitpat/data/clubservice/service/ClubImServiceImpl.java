package com.linzi.pitpat.data.clubservice.service;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.linzi.pitpat.core.constants.enums.DingTalkTokenEnum;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubImScenesEnum;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.vo.ApplyAuditApproveImParam;
import com.linzi.pitpat.data.clubservice.model.vo.ApplyAuditRejectImParam;
import com.linzi.pitpat.data.clubservice.model.vo.ClubImMessageBo;
import com.linzi.pitpat.data.clubservice.model.vo.ExitClubImParam;
import com.linzi.pitpat.data.clubservice.model.vo.InviteClubMemberActivityImParam;
import com.linzi.pitpat.data.clubservice.model.vo.InviteJoinClubImParam;
import com.linzi.pitpat.data.clubservice.model.vo.RemoveClubMemberImParam;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClubImServiceImpl implements ClubImService {

    private final ISysConfigService sysConfigService;

    @Resource
    @Lazy
    private AppMessageService appMessageService;
    @Value("${spring.profiles.active}")
    private String profile;
    /**
     * 批量消息数量
     */
    private final String BATCH_NUMBER = "200";
    /**
     * 真实用户消息
     */
    private final int IS_TEST_TAG = 0;


    @Async(value = "asyncExecutor")
    @Override
    public void sendImMessageInviteUserJoinClub(Club club, List<ZnsUserEntity> receiveUsers, Long userId, Locale locale) {
        //qpm 12000 https://www.tencentcloud.com/zh/document/product/1047/34920#.E6.9C.80.E9.AB.98.E8.B0.83.E7.94.A8.E9.A2.91.E7.8E.87
        RateLimiter rateLimiter = RateLimiter.create(50.0);


        for (ZnsUserEntity user : receiveUsers) {
            boolean b = rateLimiter.tryAcquire(1, 1, TimeUnit.SECONDS);
            if (!b) {
                log.info("超过RateLimiter。终止发送");
                DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
                DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【服务通知】：邀请加入俱乐部im发送异常\n club = " + club.getId() + "\n用户数：" + receiveUsers.size(), "***********,***********"), profile);

                return;
            }
            InviteJoinClubImParam im = new InviteJoinClubImParam();
            im.setClubId(club.getId());
            String message = I18nMsgUtils.getLangMessage(locale.toString(), "club.im.invite.content", user.getFirstName(), club.getName());
            im.setMsg(message);
            im.setImageUrl(club.getLogo());
            im.setInviteCode(club.getInviteCode());
            im.setInviteUserId(userId);
            im.setInviteeUserId(user.getId());
            im.setSendDate(System.currentTimeMillis());
            im.setScenes(ClubImScenesEnum.CHECK_INVITE_CLUB);

            appMessageService.sendIm(String.valueOf(userId),
                    Lists.newArrayList(user.getId()),
                    JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
        }


    }

    @Override
    public void sendImMessageInviteUserJoinActivity(Club club, Long activityId, Long teamId, Long sendUserId, List<Long> userIds) {
        InviteClubMemberActivityImParam im = new InviteClubMemberActivityImParam();
        im.setScenes(ClubImScenesEnum.JUMP);
        //前端活动页地址
        im.setJumpValue("lznative://lzrace/EventDetails");
        Map<String, Object> params = new HashMap<>();
        params.put("activityId", activityId);
        params.put("teamId", teamId);
        im.setJumpParam(JsonUtil.writeString(params));
        im.setActivityId(activityId);
        String message = I18nMsgUtils.getMessage("club.im.invite.activity", club.getName());
        im.setMsg(message);
        im.setImageUrl(club.getLogo());
        appMessageService.sendIm("" + sendUserId,
                userIds,
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }

    @Override
    public void sendImMessageExitClub(Club club, ZnsUserEntity user) {
        ExitClubImParam im = new ExitClubImParam();
        im.setScenes(ClubImScenesEnum.VIEW);
        String message = I18nMsgUtils.getMessage("club.im.exit", user.getFirstName());
        im.setMsg(message);
        im.setImageUrl(club.getLogo());
        appMessageService.sendIm(getClubImRobotUserId(),
                Lists.newArrayList(club.getOwnerUserId()),
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }

    @Override
    public void sendImMessageGetClubPermission(Long userId) {
        ClubImMessageBo im = new ClubImMessageBo();
        im.setScenes(ClubImScenesEnum.CAN_CREATE_CLUB);
        im.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/1737960508082.png");
        String message = I18nMsgUtils.getMessage("club.permissions.obtain");
        im.setMsg(message);
        appMessageService.sendIm(getClubImRobotUserId(),
                Lists.newArrayList(userId),
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }


    @Override
    public void sendImMessageRemoveClubMember(Club club, List<Long> userIds) {
        RemoveClubMemberImParam im = new RemoveClubMemberImParam();
        String message = I18nMsgUtils.getMessage("club.im.remove_member", club.getName());
        im.setMsg(message);
        im.setScenes(ClubImScenesEnum.VIEW);
        im.setImageUrl(club.getLogo());
        appMessageService.sendIm(getClubImRobotUserId(),
                userIds,
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }

    @Override
    public void sendImApplyAuditApprove(Club club, Long userId) {
        ApplyAuditApproveImParam im = new ApplyAuditApproveImParam();
        String message = I18nMsgUtils.getMessage("club.im.apply.audit.approved", club.getName());
        im.setMsg(message);
        im.setImageUrl(club.getLogo());
        im.setScenes(ClubImScenesEnum.VIEW);
        appMessageService.sendIm(getClubImRobotUserId(),
                Lists.newArrayList(userId),
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }

    @Override
    public void sendImApplyAuditReject(Club club, Long userId) {
        ApplyAuditRejectImParam im = new ApplyAuditRejectImParam();
        String message = I18nMsgUtils.getMessage("club.im.apply.audit.rejected", club.getName());
        im.setMsg(message);
        im.setImageUrl(club.getLogo());
        im.setScenes(ClubImScenesEnum.VIEW);
        appMessageService.sendIm(getClubImRobotUserId(),
                Lists.newArrayList(userId),
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }

    @Override
    public void sendImNewUserJoinClub(Club club, Long userId) {
        InviteClubMemberActivityImParam im = new InviteClubMemberActivityImParam();
        im.setScenes(ClubImScenesEnum.JUMP);
        im.setJumpValue("lznative://lzrace/ClubHome");
        Map<String, Object> params = new HashMap<>();
        params.put("clubId", club.getId());
        im.setJumpParam(JsonUtil.writeString(params));
        String message = "Welcome to the PitPat Newcomer Club! We’ll be sharing daily tips to help you get the most out of PitPat. If you have any questions or feedback, just drop a message in the club section of the app, and we’ll be happy to help!";
        im.setMsg(message);
        im.setImageUrl(club.getLogo());
        appMessageService.sendIm(getClubImRobotUserId(),
                Lists.newArrayList(userId),
                JsonUtil.writeString(im), TencentImConstant.TIM_CUSTOM_ELEM, BATCH_NUMBER, IS_TEST_TAG, Boolean.FALSE);
    }

    /**
     * 获取俱乐部机器人id
     *
     * @return
     */
    private String getClubImRobotUserId() {
        String s = sysConfigService.selectConfigByKey("club.im.robot.userId");
        if (StringUtils.hasText(s)) {
            return s;
        }
        throw new BaseException("not found club robot userId");
    }

}
