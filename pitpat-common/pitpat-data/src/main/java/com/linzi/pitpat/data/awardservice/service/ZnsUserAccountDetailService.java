package com.linzi.pitpat.data.awardservice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.awardservice.dto.ActivityUserPairDto;
import com.linzi.pitpat.data.awardservice.dto.UserAmountPair;
import com.linzi.pitpat.data.awardservice.model.dto.ActivitySumAwardBySubTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.query.AccountDetailPo;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.model.vo.AccountTotalVo;
import com.linzi.pitpat.data.awardservice.model.vo.AccountWithdrawalVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserAccountSimpleVo;
import com.linzi.pitpat.data.entity.dto.AppUserConsumeInfo;
import com.linzi.pitpat.data.entity.dto.AppUserDto;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentModelAwardDataVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentAwardDataVo;
import com.linzi.pitpat.data.resp.LoopsResp;
import com.linzi.pitpat.data.resp.RunAwardDto;
import com.linzi.pitpat.data.vo.useractive.RunDataYearVo;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户账户明细表
 *
 * <AUTHOR>
 * @date 2021-12-29 09:58:56
 */
public interface ZnsUserAccountDetailService {

    /**
     * 获取账户明细
     *
     * @param pageNum
     * @param pageSize
     * @param userId
     * @param type
     * @param startTime
     * @param userAccountId
     * @return
     */
    Page getAccountDetail(Integer pageNum, Integer pageSize, Long userId, Integer type, ZonedDateTime startTime, Long userAccountId);

    /**
     * 添加跑步活动奖励账户明细
     *
     * @param subtype
     * @param type                    收支类型：1表示收入，2表示支出
     * @param activityId
     * @param taskId
     * @param remark
     * @param privilegeBrand
     * @param brandRightsInterests
     * @param rightsInterestsMultiple
     * @param extraAward
     */
    Long addRunActivityAccountDetail0131(Long userId, AccountDetailTypeEnum accountDetailTypeEnum, Integer subtype,
                                         Integer type, BigDecimal amount, String billNo, ZonedDateTime tradeTime, Long refId, Long activityId
            , Long detailsId, Integer activityType, Long taskId, String remark, Integer privilegeBrand, Integer brandRightsInterests, BigDecimal rightsInterestsMultiple, BigDecimal extraAward);

    /**
     * 通过状态获取金额大小
     *
     * @param userId
     * @param tradeStatus
     * @param type
     * @param refund_status
     * @param userAccountId
     * @return
     */
    BigDecimal getAllAmountByStatus(Long userId, List<Integer> tradeStatus, Integer type, Integer refund_status, Long userAccountId);

    @Deprecated
    Long addAccountDetail(Long userId, int type, AccountDetailTypeEnum detailTypeEnum, BigDecimal amount, String billNo,
                          String payAccount, Integer tradeStatus, String tradeNo, Long refId, BigDecimal taxAmount, BigDecimal serviceAmount, Long userCouponId);


    Long addAccountDetailAddActivityId(Long userId, int type, AccountDetailTypeEnum detailTypeEnum, AccountDetailSubtypeEnum subtypeEnum, BigDecimal amount, String billNo,
                                       String payAccount, Integer tradeStatus, String tradeNo, Long refId, BigDecimal taxAmount, BigDecimal serviceAmount,
                                       Long activityId, Integer activityType, Long userCouponId, Integer privilegeBrand, Integer brandRightsInterests);

    void updateAccountDetail(Long accountDetailId, Integer tradeStatus, String tradeNo, ZonedDateTime tradeTime, ZonedDateTime tradeSuccessTime, BigDecimal actualAmount, String actualPaypalAccount, String ourSidePaypalAccount, String transferAccountsRemark, Long operatorId);

    /**
     * 查询流水
     *
     * @param billNo
     * @return
     */
    ZnsUserAccountDetailEntity getAccountDetailByBillNo(String billNo);

    /**
     * 查询流水
     *
     * @param type                  收入/支出:1表示收入，2表示支出
     * @param accountDetailTypeEnum 交易类型
     * @param userId                用户id
     * @param refId                 关联id
     */
    ZnsUserAccountDetailEntity selectAccountDetail(Integer type, AccountDetailTypeEnum accountDetailTypeEnum, Long userId, Long refId);

    /**
     * 退款
     *
     * @param id
     * @param refundRemark
     * @return
     */
    boolean refundAccountDetail(Long id, String refundRemark);

    /**
     * 查询流水
     *
     * @param tradeNo
     * @return
     */
    ZnsUserAccountDetailEntity getAccountDetailByTradeNo(String tradeNo);

    void delete(Long id, String remark);

    ZonedDateTime getStartTime(Long userId, Integer type, Integer year, Integer month);

    Map<String, Object> getMonthData(Long userId, String timeStr, Long userAccountId);

    /**
     * 获取首次时间
     *
     * @param userId
     * @return
     */
    ZonedDateTime getFirstTime(Long userId);

    /**
     * 通过关联id查询明细
     *
     * @param userId
     * @param activityId
     * @param trade_type
     * @param trade_subtype
     * @return
     */
    ZnsUserAccountDetailEntity getAccountDetail(Long userId, Long activityId, Integer trade_type, Integer trade_subtype);

    /**
     * 通过关联id查询明细
     *
     * @param rid
     * @param type
     * @param subType
     * @return
     */
    ZnsUserAccountDetailEntity getAccountDetail(Long rid, Integer type, Integer subType);

    BigDecimal selectAccountDetailByTradeTypeRef(Long userId, Integer tradeType, Long refId, ZonedDateTime createTime, String dateType);

    /**
     * 查询用户消费信息
     *
     * @param userId
     */
    AppUserConsumeInfo userConsumeInfo(Long userId);


    /**
     * 添加账户明细
     *
     * @param userId
     * @param subtype
     * @param type    收支类型：1表示收入，2表示支出
     * @param refId
     */
    void addRunActivityAccountDetail(Integer userId, AccountDetailTypeEnum accountDetailTypeEnum, Integer subtype,
                                     Integer type, BigDecimal amount, String billNo, ZonedDateTime tradeTime, Integer refId);

    Page<AccountWithdrawalVo> userAccountPage(AccountDetailPo po);

    List<AccountWithdrawalVo> userAccountList(AccountDetailPo po);


    /**
     * 获取总的奖励金额
     *
     * @param activityType
     * @param refId
     * @param isRealUser
     * @return
     */
    BigDecimal sumOfficialActivityAward(Integer activityType, Long refId, Integer isRealUser);


    /**
     * 通过关联id查询明细
     *
     * @param rid
     * @param type
     * @param subType
     * @return
     */
    ZnsUserAccountDetailEntity getAccountDetail(Integer rid, Integer type, Integer subType);

    /**
     * 获取已发放奖励
     *
     * @param startDate
     * @return
     */
    BigDecimal getIssuedAward(ZonedDateTime startDate);

    /**
     * 已发放奖励人数
     *
     * @param startDate
     * @return
     */
    Integer getIssuedUserCount(ZonedDateTime startDate);

    /**
     * 获取最近获得奖励的数据
     *
     * @param startDate
     * @return
     */
    List<UserAccountSimpleVo> getLastUserAccountDetail(ZonedDateTime startDate);

    ZnsUserAccountDetailEntity getLastOneUserAccountDetail(Long userId);

    BigDecimal sumAward(Long userId, Integer tradeType, List<Integer> subTypes);

    Map<String, Object> getMilepostAwardReport(Long refId, Integer type, int subType, Integer isRealUser);

    BigDecimal selectAccountDetailByUserIdTypeRefIdsDateRule(Long userId, Integer type, ZonedDateTime date, String awardRule);

    Integer selectAccountDetailByUserIdTypeRefIdsDateRuleCount(Long userId, Integer type, ZonedDateTime date, String awardRule);

    void addRunActivityAccountDetail3D(Long userId, AccountDetailTypeEnum accountDetailTypeEnum, Integer subtype, Integer type, BigDecimal amount, String billNo, ZonedDateTime tradeTime, Long refId, Long activityId);

    BigDecimal sumAward(Long userId, ZonedDateTime startDate, ZonedDateTime endDate);

    void addAccountDetail(Long userId, int type, AccountDetailTypeEnum typeEnum, AccountDetailSubtypeEnum subtypeEnum, BigDecimal amount, Long activityId, String remark);

    List<AppUserDto> getWithdrawal(List<Long> userIds);

    List<ZnsUserAccountDetailEntity> getAccountDetails(List<Long> userIds, Long activityId, Integer tradeType, Integer tradeSubtype);

    List<ZnsUserAccountDetailEntity> getChallengeAmount(List<Long> userIds, Long activityId);

    BigDecimal getIssuedAwardNoCache(ZonedDateTime startDate);

    Integer getIssuedUserCountNoCache(ZonedDateTime startDate);

    void updateIssuedAwardAndUserCount(ZonedDateTime startDate);

    List<ActivitySumAwardBySubTypeDto> selectSumByActivityIdTradeType(Long activityId, Integer tradeType, Integer isRealUser);

    ZnsUserAccountDetailEntity selectById(Long id);

    BigDecimal sumAward(Long activityId, Long userId, List<Integer> tradeType, Integer tradeStatus);

    void updateAccountRemark(Long activityId, Long userId, Integer tradeType, String remark);

    BigDecimal selectSumAmountWithUserIdAndActivityIdAndTime(Long id, ZnsUserRunDataDetailsEntity entity);

    /**
     * 获取月报奖金数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal getMonthReportAccountData(Long userId, ZonedDateTime startTime, ZonedDateTime endTime);

    /**
     * 初始化用户资金明细金额
     *
     * @param userId
     */
    void initUserAccountDetail(Long userId);

    /**
     * 初始化单个用户资金明细
     *
     * @param userId
     * @param currencyCode
     */
    void initSingleUserAccountDetail(Long userId, String currencyCode);

    List<ZnsUserAccountDetailEntity> selectByUserIdsAndActivityId(List<Long> userIds, Long mainActivityId);

    List<ZnsUserAccountDetailEntity> getAccountDetailsByActivityIds(List<Long> activityIds, List<Integer> tradeTypes);

    BigDecimal sumAward(List<Long> activityIds, Long userId, List<Integer> tradeType, int tradeStatus);

    List<ZnsUserAccountDetailEntity> selectByAndActivityIds(List<Long> mainActIds);

    RunDataYearVo selectMostAmountDay(Long userId, ZonedDateTime startTime, ZonedDateTime endTime);

    /**
     * 查询活动指定类型的交易金额
     *
     * @param activityId
     * @param tradeTypes
     * @param tradeStatus
     * @return
     */
    List<CurrencyAmount> findActCurrencyAmountByTradeType(Long activityId, List<Integer> tradeTypes, Integer tradeStatus);

    void bindUserAccountId(Long userAccountId, Long userId);

    ZnsUserAccountDetailEntity getAccountIncomeDetail(Long userId, Long activityId);

    List<ZnsUserAccountDetailEntity> getUserAccountDetailByQuery(UserAccountDetailByQuery userAccountDetailByQuery);

    /**
     * 更新资金记录
     *
     * @param exchangeRate 汇率
     * @param userId       用户
     * @return 更新数量
     */
    Integer updateUserAccountDetailByCurrency(BigDecimal exchangeRate, Long userId);

    /**
     * 通过资金明细计算 总支出、总收入
     *
     * @param userId
     */
    List<AccountTotalVo> getUserAccountTotalVo(Long userId);

    Page<RunAwardDto> selectPageByCondition(IPage page, ZonedDateTime gmtStartTime, ZonedDateTime gmtEndTime, String activityNo);

    BigDecimal selectAccountByGmtCreateIsRobotTradeType(ZonedDateTime start, ZonedDateTime end, Integer isRobot, Integer type);

    BigDecimal selectAccountByTradeType(Long userId, Integer type);

    Integer countAccountByTradeType(Long userId, Integer type);

    BigDecimal selectAccountByCreateTimeTradeType(Long userId, ZonedDateTime befor30Date, Integer type);

    Integer countAccountByCreateTimeTradeType(Long userId, ZonedDateTime befor30Date, Integer type);

    BigDecimal selectAccountByTradeTypeSubType(ZonedDateTime gmtStartTime, ZonedDateTime gmtEndTime, Integer type, String activityType);

    List<LoopsResp> selectAccountByBatchNo(String batchNo);

    BigDecimal selectAccountByActivityId(Long id, Integer tradeType);

    BigDecimal selectSumAward(Long activityId, Long userId, List<Integer> tradeType, int i);

    BigDecimal selectAccountByActivityIdUserIdTradeType(Long aLong, Long userId, List<Integer> list);

    BigDecimal selectAccountDetailByUserIdTradeType(Long userId, List<Integer> list, Long id);

    BigDecimal selectSumByUserIdActivityType(Integer type, Long userId, int i, Integer refundStatus);

    ZnsUserAccountDetailEntity selectAccountDetailByBillNo(String billNo);

    Integer countAccountByTradeTypeTradeSubtype(Integer type, List<Integer> list, Long userId);

    /**
     * 合并资金记录
     *
     * @param newAccountId
     * @param newUserId
     * @param oldUserIds
     */
    void mergeAccountDetail(Long newAccountId, Long newUserId, List<Long> oldUserIds);

    List<ZnsUserAccountDetailEntity> listByIds(List<Long> userAccountDetailIdList);

    List<ZnsUserAccountDetailEntity> findList(UserAccountDetailByQuery userAccountDetailByQuery);

    void updateById(ZnsUserAccountDetailEntity update);

    List<ZnsUserAccountDetailEntity> findListForNonPayment(ZonedDateTime start, ZonedDateTime end);

    boolean save(ZnsUserAccountDetailEntity userAccountDetailEntity);

    boolean update(ZnsUserAccountDetailEntity znsUserAccountDetailEntity);

    /**
     * 查询设备奖励数据
     */
    List<EquipmentModelAwardDataVo> findEquipmentModelAwardData(Long userId);

    /**
     * 获取前一天的设备奖励数据
     */
    List<UserEquipmentAwardDataVo> findUserEquipmentAwardVo(ZonedDateTime startDate);

    /**
     * 用户在活动中获取的奖励总额
     *
     * @param loadData
     * @param accountDetailTypeEnum
     * @return
     */
    List<UserAmountPair> sumByActivityIdTradeTypeAndUserIds(List<ActivityUserPairDto> loadData, AccountDetailTypeEnum accountDetailTypeEnum);
}

