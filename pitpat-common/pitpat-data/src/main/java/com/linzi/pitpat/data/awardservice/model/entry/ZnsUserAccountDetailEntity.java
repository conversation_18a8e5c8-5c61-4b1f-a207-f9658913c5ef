package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.awardservice.constant.enums.AccountConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.lz.mybatis.plugin.annotations.GmtCreate;
import com.lz.mybatis.plugin.annotations.GmtModified;
import com.lz.mybatis.plugin.annotations.IsDelete;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 用户账户明细表
 *
 * <AUTHOR>
 * @date 2021-12-29 09:58:56
 */
@TableName("zns_user_account_detail")
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ZnsUserAccountDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                               // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";                                  // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";                              // 创建时间
    public final static String modifie_time = CLASS_NAME + "modifie_time";                            // 最后修改时间
    public final static String user_id = CLASS_NAME + "user_id";                                      // 用户id
    public final static String type_ = CLASS_NAME + "type";                                           // 收支类型：1表示收入，2表示支出
    public final static String title_ = CLASS_NAME + "title";                                         // 明细title:保证金、提现、充值、组队跑奖励、挑战跑奖励、排行赛奖励
    public final static String amount_ = CLASS_NAME + "amount";                                       // 收入/支出金额
    public final static String bill_no = CLASS_NAME + "bill_no";                                      // 流水单号
    public final static String trade_no = CLASS_NAME + "trade_no";                                    // 三方交易流水
    public final static String trade_type = CLASS_NAME + "trade_type";                                // 交易类型: 比如1:保证金,2:提现,3:提现服务费,4:提现税费,5:充值,6:组队跑奖励,7:挑战跑奖励,8:排行赛奖励 ,9 官方组队奖励，10 官方累计奖励 ,11 2D跑步彩蛋奖励 12：新人福利 13：分销佣金 14 费用,15 3D跑步彩蛋奖励 16  3D鼓掌奖励 17 调查问卷奖励 18邀请好友 19一周快乐跑 20优惠券 21世界杯打卡
    public final static String trade_subtype = CLASS_NAME + "trade_subtype";                          // 子交易类型，交易类型8：0：排行赛奖励-排名奖励，1：（必胜券）排行赛奖励-挑战成功奖励，2：（翻倍券）排行赛奖励-挑战失败奖励，3：（幸运券）排行赛奖励-被挑战奖励,  如果trade_type = 16  ，则6. 发起鼓掌 ,7 发起加油， 8 发起击掌 ， 9 收到鼓掌，10，收到加油，11 收到击掌
    public final static String trade_time = CLASS_NAME + "trade_time";                                // 交易时间
    public final static String trade_success_time = CLASS_NAME + "trade_success_time";                // 交易成功时间
    public final static String trade_status = CLASS_NAME + "trade_status";                            // 交易状态：0表示交易发起申请，1表示交易处理中，2表示交易结束，-1表示交易失败
    public final static String refund_status = CLASS_NAME + "refund_status";                          // 退款状态:0表示无退款，1表示全额退款
    public final static String refund_remark = CLASS_NAME + "refund_remark";                          // 退款说明
    public final static String refund_time = CLASS_NAME + "refund_time";                              // 退款时间
    public final static String service_fee = CLASS_NAME + "service_fee";                              // 服务费
    public final static String taxes_fee = CLASS_NAME + "taxes_fee";                                  // 税费
    public final static String paypal_account = CLASS_NAME + "paypal_account";                        // 交易paypal账号
    public final static String ref_id = CLASS_NAME + "ref_id";                                        // 关联id,如果trade_type =16,则ref_id存储的是活动类型，如果trade_type是15，则为 zns_egg_activity_config表的id
    public final static String remark_ = CLASS_NAME + "remark";                                       // 备注
    public final static String actual_amount = CLASS_NAME + "actual_amount";                          // 实际转账金额
    public final static String actual_paypal_account = CLASS_NAME + "actual_paypal_account";          // 实际到账PayPal账号
    public final static String our_side_paypal_account = CLASS_NAME + "our_side_paypal_account";      // 我方PayPal账号
    public final static String transfer_accounts_remark = CLASS_NAME + "transfer_accounts_remark";    // 转账备注
    public final static String activity_id = CLASS_NAME + "activity_id";                              // 活动id
    public final static String other_remark = CLASS_NAME + "other_remark";                            // 其他备注
    public final static String is_test = CLASS_NAME + "is_test";                                      // 是否是测试用户
    public final static String is_robot = CLASS_NAME + "is_robot";                                    // 是否是机器人
    public final static String details_id = CLASS_NAME + "details_id";                                // zns_user_run_data_details 表id
    public final static String activity_type = CLASS_NAME + "activity_type";                          // 活动类型
    public final static String user_coupon_id = CLASS_NAME + "user_coupon_id";                        // 抵扣券用户优惠券id
    public final static String audit_status = CLASS_NAME + "audit_status";                            // 审核状态: 0未审核，1已拒绝，2已同意
    public final static String abnormal_status = CLASS_NAME + "abnormal_status";                      // 异常状态：0正常，1异常
    public final static String audit_remark = CLASS_NAME + "audit_remark";                            // 审批备注说明
    public final static String abnormal_remark = CLASS_NAME + "abnormal_remark";                      // 异常备注说明
    public final static String audit_id = CLASS_NAME + "audit_id";                                    // 审核人的id
    public final static String transfer_accounts_id = CLASS_NAME + "transfer_accounts_id";            // 转账人的id
    public final static String abnormal_markers_id = CLASS_NAME + "abnormal_markers_id";              // 异常标记人的id
    public final static String audit_time = CLASS_NAME + "audit_time";                                // 审核同意或拒绝的操作时间
    public final static String abnormal_remark_time = CLASS_NAME + "abnormal_remark_time";            // 异常标记的时间


    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    @IsDelete
    private Integer isDelete;
    /**
     * 创建时间
     */
    @GmtCreate
    private ZonedDateTime createTime;
    /**
     * 最后修改时间
     */
    @GmtModified
    private ZonedDateTime modifieTime;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 收支类型：1表示收入，2表示支出
     */
    private Integer type;
    /**
     * 明细title:保证金、提现、充值、组队跑奖励、挑战跑奖励、排行赛奖励
     */
    private String title;
    /**
     * 收入/支出金额
     */
    private BigDecimal amount;
    /**
     * 流水单号
     */
    private String billNo;
    /**
     * 三方流水
     */
    private String tradeNo;
    /**
     * 交易类型: 比如1表示跑步活动奖励
     *
     * @see AccountDetailTypeEnum
     */
    private Integer tradeType;
    /**
     * 子交易类型 子交易类型，交易类型8：0：排行赛奖励-排名奖励，1：（必胜券）排行赛奖励-挑战成功奖励，2：（翻倍券）排行赛奖励-挑战失败奖励，3：（幸运券）排行赛奖励-被挑战奖励,  如果trade_type = 16  ，则6. 发起鼓掌 ,7 发起加油， 8 发起击掌 ， 9 收到鼓掌，10，收到加油，11 收到击掌
     *
     * @see AwardSentTypeEnum tradeType = 100 时候
     */
    private Integer tradeSubtype;
    /**
     * 交易时间
     */
    private ZonedDateTime tradeTime;
    /**
     * 交易成功时间
     */
    private ZonedDateTime tradeSuccessTime;
    /**
     * 交易状态：0表示交易发起申请，1表示交易处理中，2表示交易结束，-1表示交易失败 3:待领取
     *
     * @see AccountConstant.TradeStatusEnum
     */
    private Integer tradeStatus;
    /**
     * 退款状态,0表示无退款，1表示全额退款
     */
    private Integer refundStatus;
    /**
     * 退款说明
     */
    private String refundRemark;
    /**
     * 退款时间
     */
    private ZonedDateTime refundTime;
    /**
     * 服务费
     */
    private BigDecimal serviceFee;
    /**
     * 税费
     */
    private BigDecimal taxesFee;
    /**
     * paypal账号
     */
    private String paypalAccount;

    /**
     * paypal账号
     */
    @TableField(exist = false)
    private String ppzfAccount;


    /**
     * 关联id
     */
    private Long refId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 支付类型，0：余额，1：paypal
     */
    @TableField(exist = false)
    private Integer payType;
    /**
     * 实际到账金额($)
     */
    private BigDecimal actualAmount;
    /**
     * 实际到账PayPal账号
     */
    private String actualPaypalAccount;
    /**
     * 我方PayPal账号
     */
    private String ourSidePaypalAccount;
    /**
     * 转账备注
     */
    private String transferAccountsRemark;
    /**
     * 审核状态: 0未审核，1已拒绝，2已同意
     */
    private Integer auditStatus;
    /**
     * 异常状态：0正常，1异常
     */
    private Integer abnormalStatus;
    /**
     * 审批备注
     */
    private String auditRemark;
    /**
     * 异常备注
     */
    private String abnormalRemark;
    /**
     * 审批人的id
     */
    private Long auditId;
    /**
     * 转账人的id
     */
    private Long transferAccountsId;
    /**
     * 异常标记人的id
     */
    private Long abnormalMarkersId;
    /**
     * 提现审核的时间
     */
    private ZonedDateTime auditTime;
    /**
     * 异常标记的时间
     */
    private ZonedDateTime abnormalRemarkTime;

    private Long activityId;    //活动id

    private String otherRemark;     // 其他备注


    //是否是测试用户，1 是测试用户，0 不是测试用户
    private Integer isTest;
    //是否是机器人， 1 是机器人， 0 不是机器人
    private Integer isRobot;


    //zns_user_run_data_details 表id
    private Long detailsId;
    //活动类型
    private Integer activityType;

    private Long taskId;

    private Long userCouponId;//抵扣券用户优惠券id

    /**
     * 特权品牌 -1 无 1：Superun，2：Deerrun, 3：JLL
     */
    private Integer privilegeBrand;
    /**
     * 品牌权益 -1 无 0：免费 1:奖励翻倍(原)
     * 权益类型 -1 无 1：报名费，2：完赛奖励，3：名次奖励，4：积分，5：挑战他人成功奖励，6：挑战他人失败奖励，7：被挑战奖励，8：里程碑奖励
     */
    private Integer brandRightsInterests;
    /**
     * 权益倍数
     */
    private BigDecimal brandRightsInterestsMultiple;
    /**
     * 额外奖励金额，已包含在amount中
     */
    private BigDecimal extraAmount;

    @TableField(exist = false)
    private Currency currency;
    /**
     * 账户id
     */
    private Long userAccountId;
}

