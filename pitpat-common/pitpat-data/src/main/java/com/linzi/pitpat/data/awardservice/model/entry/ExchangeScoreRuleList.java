package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 兑换券规则列表
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

@Data
@NoArgsConstructor
@TableName("zns_exchange_score_rule_list")
public class ExchangeScoreRuleList implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleList:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                           // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                              // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";                            // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                        // 最后修改时间
    public final static String image_url = CLASS_NAME + "image_url";                              // 图片
    public final static String exchange_score_rule_id = CLASS_NAME + "exchange_score_rule_id";    // 规则id
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //图片
    private String imageUrl;
    //规则id
    private Long exchangeScoreRuleId;
    // 语言code
    private String langCode;

    @Override
    public String toString() {
        return "ExchangeScoreRuleList{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",imageUrl=" + imageUrl +
                ",exchangeScoreRuleId=" + exchangeScoreRuleId +
                "}";
    }
}
