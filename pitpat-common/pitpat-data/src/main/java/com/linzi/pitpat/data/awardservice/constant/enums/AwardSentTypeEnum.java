package com.linzi.pitpat.data.awardservice.constant.enums;


import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;

import java.util.Arrays;
import java.util.List;

public enum AwardSentTypeEnum {
    /*1：排名基础奖励，2：完赛，3：被挑战奖励 4：挑战成功奖励，
    5：挑战失败奖励 6：排名人头奖励 7：达到目标时间奖励 8：达到目标里程奖励,
    9,"发起奖励"10,"参与奖励"11,"胜者奖励"12,"奖金池基础",
    13,"奖金池占比"14, "段位奖励"15,"时间奖励"16,"里程奖励"17,"瓜分金额",
    18,"报名奖励")*/
    RANKING_BASED_REWARDS(1, "Ranking based rewards", "排名基础奖励"),
    COMPLETING_THE_GAME(2, "Completing the game", "完赛"),
    BEING_CHALLENGED(3, "Reward for being challenged", "被挑战奖励"),
    CHALLENGE_SUCCESS(4, "Challenge Success Reward", "挑战成功奖励"),
    CHALLENGE_FAIL(5, "Challenge Fail Reward", "挑战失败奖励"),
    RANKING_HEAD_REWARD(6, "Ranking head reward", "排名人头奖励"),
    COMPLETE_TARGET_TIME(7, "Reward for achieving target time", "达到目标时间奖励"),
    COMPLETE_TARGET_MILLAGE(8, "Reward for achieving target mileage", "达到目标里程奖励"),
    LAUNCH_AWARD(9, "launch award", "发起奖励"),
    PARTICIPATION_AWARD(10, "participation award", "参与奖励"),
    WINNER_AWARD(11, "winner award", "胜者奖励"),
    FOUNDATION_OF_BONUS_POOL(12, "Foundation of bonus pool", "奖金池基础"),
    PERCENTAGE_OF_BONUS_POOL(13, "Percentage of bonus pool", "奖金池占比"),
    RANK_AWARD(14, "rank_award", "段位奖励"),

    TIME_AWARD(15, "time_award", "时间奖励"),

    MILEAGE_AWARD(16, "mileage_award", "里程奖励"),
    AVE_PARTITION_AWARD(17, "ave_partition_award", "瓜分金额"),
    APPLICATION_AWARD(18, "application_award", "报名奖励"),

    SURPASS_AWARD(19, "surpass_award", "超越奖励"),

    PERSONAL_STAGE_AWARD(20, "personal_stage_award", "个人阶段奖励--场次奖励"),
    RECORD_BREAKING_AWARD(21, "record_breaking_award", "破纪录奖励"),
    RANKING_PERSONAL_AWARD(22, "ranking_personal_award", "团赛-个人排名奖励"),
    PRESIDENT_AWARD(23, "president_award", "团赛-会长奖励"),
    ONLINE_RANK(24, "online_rank", "上榜奖励"),

    ;

    private Integer type;
    private String name;
    private String znName;

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getZnName() {
        return znName;
    }

    AwardSentTypeEnum(Integer type, String name, String znName) {
        this.type = type;
        this.name = name;
        this.znName = znName;
    }

    public static AwardSentTypeEnum findByType(Integer type) {
        return Arrays.stream(AwardSentTypeEnum.values()).filter(e -> e.type.equals(type)).findFirst().orElse(null);
    }

    public static List<Integer> copyTypes() {
        return Arrays.asList(RANKING_BASED_REWARDS.getType(), COMPLETING_THE_GAME.getType(), LAUNCH_AWARD.getType(), PARTICIPATION_AWARD.getType(), WINNER_AWARD.getType(), FOUNDATION_OF_BONUS_POOL.getType(), PERCENTAGE_OF_BONUS_POOL.getType());
    }

    public static List<Integer> examineAwardTypes() {
        return Arrays.asList(RANKING_BASED_REWARDS.getType(), RANKING_HEAD_REWARD.getType(), COMPLETING_THE_GAME.getType(), SURPASS_AWARD.getType());
    }
}
