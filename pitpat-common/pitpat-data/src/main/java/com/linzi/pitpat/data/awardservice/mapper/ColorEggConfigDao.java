package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 彩蛋配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-20
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.awardservice.model.entry.ColorEggConfig;
import com.linzi.pitpat.data.awardservice.model.vo.ColorEggConfigVo;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.LE;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface ColorEggConfigDao extends BaseMapper<ColorEggConfig> {


    ColorEggConfig selectColorEggConfigById(@Param("id") Long id);

    List<ColorEggConfigVo> selectColorEggConfigByStatusGmtStartGmtEnd(Integer status, @LE @DateFormat ZonedDateTime gmtStart, @DateFormat @GE ZonedDateTime gmtEnd);
}