package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 奖励配置服装表
 *
 * <AUTHOR>
 * @since 2023-10-09
 */

@Data
@NoArgsConstructor
@TableName("zns_award_config_wear")
public class AwardConfigWear implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.AwardConfigWear:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                             // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";                //
    public final static String gmt_create = CLASS_NAME + "gmt_create";              //
    public final static String award_config_id = CLASS_NAME + "award_config_id";    // 配置ID，关联zns_award_config
    public final static String wear_type = CLASS_NAME + "wear_type";                // 衣服属性，值为wears表字段
    public final static String wear_value = CLASS_NAME + "wear_value";              // 衣服属性值，例：1
    public final static String wear_name = CLASS_NAME + "wear_name";                // 服装名称
    public final static String wear_image_url = CLASS_NAME + "wear_image_url";      // 服装图片 URL
    public final static String expired_time = CLASS_NAME + "expired_time";          // 道具配置过期时间 null 是永久道具
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //配置ID，关联zns_award_config
    private Long awardConfigId;
    //衣服属性，值为wears表字段
    private String wearType;
    //衣服属性值，例：1
    private Integer wearValue;
    //服装名称
    private String wearName;
    //服装图片URL
    private String wearImageUrl;
    //道具配置过期时间 null 是永久道具
    private Integer expiredTime;

    @Override
    public String toString() {
        return "AwardConfigWear{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",awardConfigId=" + awardConfigId +
                ",wearType=" + wearType +
                ",wearValue=" + wearValue +
                ",wearName=" + wearName +
                ",wearImageUrl=" + wearImageUrl +
                ",expiredTime=" + expiredTime +
                "}";
    }
}
