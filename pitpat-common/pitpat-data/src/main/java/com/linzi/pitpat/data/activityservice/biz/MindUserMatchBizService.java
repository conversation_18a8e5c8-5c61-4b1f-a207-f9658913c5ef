package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunMode;
import com.linzi.pitpat.data.robotservice.model.entity.RotPond;
import com.linzi.pitpat.data.robotservice.service.RobotRunModeService;
import com.linzi.pitpat.data.robotservice.service.RotPondService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/20 6:40
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MindUserMatchBizService {
    private final MindUserMatchService mindUserMatchService;
    private final ZnsUserService userService;
    private final RobotRunModeService robotRunModeService;
    private final MainActivityService mainActivityService;
    private final RotPondService rotPondService;
    private final ZnsRunActivityService runActivityService;

    /**
     * 插入用户匹配信息
     *
     * @param mindUserMatch
     * @return
     */
    public Long insertMindUserMatch(MindUserMatch mindUserMatch) {
        ZnsUserEntity znsUserEntity = userService.findById(mindUserMatch.getUserId());
        if (!Objects.equals(znsUserEntity.getIsRobot(), 1)) {
            return mindUserMatchService.insertMindUserMatch(mindUserMatch);
        }                    //如果是机器人
        // status 0 , 1 表示机器人被使用， -1 ， 2 表示机器人恢复使用
        userService.updateZnsUserRobotStatus(1, mindUserMatch.getUserId()); // 插入即被使用
        return mindUserMatchService.insertMindUserMatch(mindUserMatch);
    }

    /**
     * 修改用户匹配信息
     *
     * @param mindUserMatch
     * @return
     */
    public int updateMindUserMatchById(MindUserMatch mindUserMatch) {
        // status 0 , 1 表示机器人被使用， -1 ， 2 表示机器人恢复使用
        int status = Arrays.asList(0, 1).contains(mindUserMatch.getStatus()) ? 1 : 2;
        // 2 , 1
        ZnsUserEntity znsUserEntity = userService.findById(mindUserMatch.getUserId());
        if (znsUserEntity != null && !Objects.equals(znsUserEntity.getRobotCurrStatus(), status)) {     //如果状态不相等，则更新，如果相等，则不更新
            boolean flag = true;
            if (Objects.equals(status, 2)) {    //如果将状态为2，则看当前有没有在跑的数据，如果有，则不更新用户当前跑步状态
                MindUserMatch mindUserMatchNew = mindUserMatchService.selectMindUserMatchByIdStatusList(mindUserMatch.getId(), znsUserEntity.getId(), Arrays.asList(0, 1));
                if (mindUserMatchNew != null) {
                    log.info(" 正在跑步的mindUserMatchNewId = " + mindUserMatchNew.getId() + "，当前mindmatchId = " + mindUserMatch.getId() + ",userId=" + mindUserMatch.getUserId());
                    flag = false;
                }
            }
            if (flag) {
                userService.updateZnsUserRobotStatus(status, mindUserMatch.getUserId());
            }
        }
        return mindUserMatchService.updateMindUserMatchById(mindUserMatch);
    }

    /**
     * 新增机器人匹配信息，添加机器人策略
     *
     * @param userId
     * @param activityId
     * @param runMileage
     * @param runMode
     * @param activityEndTime
     * @return
     */
    public MindUserMatch addRobotMindUserMatch(Long userId, Long activityId, BigDecimal runMileage, String runMode, ZonedDateTime activityEndTime) {
        MindUserMatch mindUserMatch = new MindUserMatch();
        mindUserMatch.setUserId(userId);
        mindUserMatch.setStatus(1);

        String uniqueCode = OrderUtil.getUserPoolOrder();
        mindUserMatch.setTargetMileage(runMileage.intValue());
        mindUserMatch.setUniqueCode(uniqueCode);
        mindUserMatch.setIsRobot(1);
        mindUserMatch.setUniqueCode(OrderUtil.getUserPoolOrder());
        if (StringUtil.isEmpty(runMode)) {
            RobotRunMode robotRunMode = robotRunModeService.getRobotRunMode();
            mindUserMatch.setRunMode(robotRunMode.getMode());
        } else {
            mindUserMatch.setRunMode(runMode);
        }
        mindUserMatch.setActivityId(activityId);
        mindUserMatch.setActivityEndTime(activityEndTime);
        insertMindUserMatch(mindUserMatch);

        updateRotPond(userId, activityId, mindUserMatch.getId());

        return mindUserMatch;
    }

    /**
     * 更新机器人池信息
     *
     * @param userId
     * @param activityId
     * @param matchId
     */
    private void updateRotPond(Long userId, Long activityId, Long matchId) {
        List<RotPond> rotPonds = rotPondService.findListByUserId(userId);
        if (!CollectionUtils.isEmpty(rotPonds)) {
            RotPond rotPond = rotPonds.get(0);
            rotPond.setMatchId(matchId);
            rotPond.setActivityId(activityId);
            MainActivity mainActivity = mainActivityService.findById(activityId);
            if (Objects.isNull(mainActivity)) {
                ZnsRunActivityEntity activityEntity = runActivityService.selectActivityById(activityId);
                rotPond.setActivityType(activityEntity.getActivityType());
            } else {
                rotPond.setActivityType(mainActivity.getOldType());
            }

            rotPondService.updateRotPondById(rotPond);
        }

    }
}
