package com.linzi.pitpat.data.activityservice.manager.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.NewPersonPkBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ToBeInvolvedActRespDto;
import com.linzi.pitpat.data.activityservice.manager.RunRouteManager;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostAwardDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityEntryFeeQuery;
import com.linzi.pitpat.data.activityservice.model.query.MyRaceCalendarActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.MyRecordActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.ToBeInvolvedActivityQuery;
import com.linzi.pitpat.data.activityservice.model.request.ActivityIdRequest;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityConfigResp;
import com.linzi.pitpat.data.activityservice.model.resp.TaskResp;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityPageOptionListVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityPageVo;
import com.linzi.pitpat.data.activityservice.model.vo.FriendPkAward;
import com.linzi.pitpat.data.activityservice.model.vo.MyActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.MyActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.MyRecordsActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewerPKVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.RaceTabVo;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityIntroduceVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityTeamVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MaxAwardVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRaceCalendarActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRaceCalendarSubActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRecordActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.RecordActivityTypeVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.SubActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.SurplusActivityLevelVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ToBeInvolvedActListVo;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.BaseActivityStrategy;
import com.linzi.pitpat.data.activityservice.strategy.RunActivityTypeStrategy;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.dto.VisualizationAwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityClassifyTypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.MarginCurrencyTypeEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.resp.UserGameRankAwardDto;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.enums.RouteShowLocationEnum;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.query.OperationalActivityQuery;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserFriendEntity;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PkFriendAwardUtil;
import com.linzi.pitpat.data.vo.RobotBean;
import com.linzi.pitpat.data.vo.RobotSpeed;
import com.linzi.pitpat.data.vo.RunRouteVO;
import com.linzi.pitpat.data.vo.RunnersSimpleVo;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动页面业务类
 *
 * <AUTHOR>
 * @date 2024/7/19 6:40
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AppActivityPageManager {
    private final UserExtraService userExtraService;
    private final RunRouteManager runRouteManager;
    private final ZnsRunRouteService runRouteService;
    private final ZnsUserService userService;
    private final ZnsUserFriendService userFriendService;
    private final UserFriendMatchService userFriendMatchService;
    private final ZnsUserAccountService userAccountService;
    private final AwardConfigBizService awardConfigBizService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ActivityDisseminateService activityDisseminateService;
    private final ActivityEntryFeeService activityEntryFeeService;
    private final ActivityFeeService activityFeeService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ISysConfigService sysConfigService;
    private final EntryGameplayService entryGameplayService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsUserAccountDetailService accountDetailService;
    private final OperationalActivityService operationalActivityService;
    private final PkChallengeRecordService pkChallengeRecordService;
    private final ExchangeRateConfigService exchangeRateConfigService;

    private final RotationAreaBizService rotationAreaBizService;
    private final NewPersonPkBizService newPersonPkBizService;
    private final ActivityAwardCurrencyBizService activityAwardCurrencyBizService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final AwardActivityBizService awardActivityBizService;
    private final ActivityUserBizService activityUserBizService;
    private final RunActivityBizService runActivityBizService;

    private final RedissonClient redissonClient;
    private final ApplicationContext springContext;

    /**
     * 活动页面
     *
     * @param activityConfig
     * @param userEntity
     * @param checkVersion
     * @param appVersion
     * @return
     */
    public ActivityPageVo runActivityPageV1(ZnsRunActivityConfigEntity activityConfig, ZnsUserEntity userEntity, boolean checkVersion, Integer appVersion) {
        ActivityPageVo vo = new ActivityPageVo();
        // 路线列表
        List<RunRouteVO> routeList = runRouteManager.runRouteListVo(activityConfig.getActivityType(), activityConfig.getSubType(), appVersion);
        if (CollectionUtils.isEmpty(routeList)) {
            routeList = runRouteService.runRouteListVo(activityConfig.getActivityType(), RouteShowLocationEnum.getAppList(), appVersion);
        }
        vo.setRouteList(routeList);

        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(userEntity.getId());
        Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
        // 奖金池保证金列表
        if (checkVersion) {
            vo.setMarginList(new ArrayList<>());
        } else {
            MarginCurrencyTypeEnum marginCurrencyTypeEnum = MarginCurrencyTypeEnum.findByCurrencyCode(userAccount.getCurrencyCode());
            String margins = MapUtil.getString(jsonObject.get(marginCurrencyTypeEnum.getType()));
            if (StringUtils.hasText(margins)) {
                List<String> marginList = Arrays.asList(margins.split(","));
                vo.setMarginList(marginList);
            }
        }
        vo.setCurrency(I18nConstant.buildCurrency(userAccount.getCurrencyCode()));
        //选项查询
        List<VisualizationAwardConfigDto> visualizationAwardConfigDtoList = awardConfigBizService.selectAwardConfigDtoListByActivityType(activityConfig.getActivityType(), activityConfig.getSubType(), Arrays.asList(AwardSentTypeEnum.COMPLETING_THE_GAME.getType(), AwardSentTypeEnum.WINNER_AWARD.getType(), AwardSentTypeEnum.LAUNCH_AWARD.getType(), AwardSentTypeEnum.PARTICIPATION_AWARD.getType()));
        List<ActivityPageOptionListVo> mileageList = awardConfigBizService.assembleActivityPageOptionListVo(visualizationAwardConfigDtoList, 0, activityConfig.getActivityType(), userEntity);
        vo.setMileageList(mileageList);
        List<ActivityPageOptionListVo> mileList = awardConfigBizService.assembleActivityPageOptionListVo(visualizationAwardConfigDtoList, 1, activityConfig.getActivityType(), userEntity);
        vo.setMileList(mileList);
        List<ActivityPageOptionListVo> runTimeList = awardConfigBizService.assembleActivityPageOptionListVo(visualizationAwardConfigDtoList, 2, activityConfig.getActivityType(), userEntity);
        vo.setRunTimeList(runTimeList);

        //如果需要抽佣
        if (Objects.nonNull(jsonObject.get("priceProportion"))) {
            BigDecimal priceProportion = MapUtil.getBigDecimal(jsonObject.get("priceProportion"));
            vo.setPriceProportion(BigDecimalUtil.multiply(new BigDecimal(100), priceProportion).intValue());
        }
        return vo;
    }

    /**
     * 活动页相关活动配置数据
     */
    public Map<String, Object> runActivityPage(Long activityConfigId, ZnsUserEntity userEntity, boolean checkVersion, Integer appVersion, ZnsRunActivityConfigEntity activityConfig, Integer version) {
        Map<String, Object> data = new HashMap<>();

        // 路线列表
        List<RunRouteVO> routeList = runRouteService.runRouteListVo(activityConfig.getActivityType(), RouteShowLocationEnum.getAppList(), appVersion);
        data.put("routeList", routeList);

        if (null != activityConfig && StringUtils.hasText(activityConfig.getActivityConfig())) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
            if (null != jsonObject) {
                // 跑步里程列表，滑动列表
                String mileageList = MapUtil.getString(jsonObject.get(ApiConstants.MILEAGE_LIST));
                if (StringUtils.hasText(mileageList)) {
                    data.put("mileageList", Arrays.asList(mileageList.split(",")));
                }
                String mileList = MapUtil.getString(jsonObject.get(ApiConstants.MILE_LIST));
                if (StringUtils.hasText(mileList)) {
                    List<String> mileStringList = Arrays.asList(mileList.split(","));

                    data.put("mileList", mileStringList);
                }
                // 跑步里程列表,选择列表
                String mileages = MapUtil.getString(jsonObject.get(ApiConstants.MILEAGE));
                if (StringUtils.hasText(mileages)) {
                    data.put("mileages", Arrays.asList(mileages.split(",")));
                }
                //跑步里程(英里)列表,选择列表
                String miles = MapUtil.getString(jsonObject.get(ApiConstants.MILES));
                if (StringUtils.hasText(miles)) {
                    List<String> milesStringList = Arrays.asList(miles.split(","));
                    data.put("miles", milesStringList);
                }
                // 跑步时长列表
                String runTimes = MapUtil.getString(jsonObject.get(ApiConstants.RUN_TIME));
                if (StringUtils.hasText(runTimes)) {
                    List<String> runTimeList = Arrays.asList(runTimes.split(","));
                    data.put("runTimeList", runTimeList);
                }
                // 奖金池保证金列表
                if (checkVersion) {
                    data.put("marginList", new ArrayList<>());
                } else {
                    String margins = MapUtil.getString(jsonObject.get(ApiConstants.MARGIN));
                    if (StringUtils.hasText(margins)) {
                        List<String> marginList = Arrays.asList(margins.split(","));
                        data.put("marginList", marginList);
                    }
                }

                // 完赛奖励-每公里
                BigDecimal completeAwardPerKm = MapUtil.getBigDecimal(jsonObject.get(ApiConstants.COMPLETE_AWARD_PER_KM));
                data.put("completeAwardPerKm", completeAwardPerKm);
                // 完赛奖励-每英里
                BigDecimal completeAwardPerMiles = MapUtil.getBigDecimal(jsonObject.get(ApiConstants.COMPLETE_AWARD_PER_MILES));
                data.put("completeAwardPerMiles", completeAwardPerMiles);
                // 活动奖励
                if (RunActivityTypeEnum.TEAM_RUN.getType().intValue() == activityConfig.getActivityType().intValue()) {
                    String participateAward = MapUtil.getString(jsonObject.get(ApiConstants.PARTICIPATE_AWARD));
                    String completeAward = MapUtil.getString(jsonObject.get(ApiConstants.COMPLETE_AWARD));
                    data.put("participateAward", participateAward);
                    data.put("completeAward", completeAward);
                    Integer limitPeopleSize = getLimitPeopleSize();
                    data.put("placingAwardPeopleLimit", limitPeopleSize);
                } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().intValue() == activityConfig.getActivityType().intValue()) {
                    String participateAward = MapUtil.getString(jsonObject.get(ApiConstants.PARTICIPATE_AWARD));
                    String winnerAward = MapUtil.getString(jsonObject.get(ApiConstants.WINNER_AWARD));
                    data.put("participateAward", participateAward);
                    data.put("winnerAward", winnerAward);
                }
                BigDecimal maxAwardLimit = MapUtil.getBigDecimal(jsonObject.get(ApiConstants.MAX_AWARD_LIMIT));
                data.put("maxAwardLimit", maxAwardLimit);
            }
        }
        return data;
    }

    /**
     * 获取活动人数限制
     *
     * @return
     */
    private Integer getLimitPeopleSize() {
        String limitCount = sysConfigService.selectConfigByKey("team.activity.people.num.limit");
        Integer limitPeopleSize = 5;
        if (StringUtils.hasText(limitCount)) {
            limitPeopleSize = Integer.parseInt(limitCount);
        }
        return limitPeopleSize;
    }


    /**
     * 查询活动介绍页
     *
     * @param activityConfigId 活动配置ID
     */
    public RunActivityIntroduceVO runActivityIntroduce(Long activityConfigId, ZnsUserEntity userEntity, Integer activityType) {
        RunActivityIntroduceVO activityIntroduceVO = new RunActivityIntroduceVO();
        // 活动配置相关数据
        wrapperRunActivityIntroducePageData(activityConfigId, activityIntroduceVO);
        // 当前可参加活动队伍
        List<RunActivityTeamVO> activitys = getEffectiveRunActivity(userEntity, activityType, 1, 3);
        activityIntroduceVO.setActivitys(activitys);
        // 活动用户
        List<RunActivityUserVO> activityIntroduceUsers = runActivityUserService.findRunActivityIntroduceUsers(activityType);
        activityIntroduceVO.setIntroducePageUsers(activityIntroduceUsers);
        return activityIntroduceVO;
    }

    /**
     * 设置活动介绍页活动配置相关数据
     */
    private void wrapperRunActivityIntroducePageData(Long activityConfigId, RunActivityIntroduceVO activityIntroduceVO) {
        // 查询活动配置
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.findRunActivityConfig(activityConfigId);
        if (null != activityConfig && StringUtils.hasText(activityConfig.getActivityConfig())) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
            if (null != jsonObject) {
                // 活动介绍
                String activityIntroduce = MapUtil.getString(jsonObject.get(ApiConstants.ACTIVITY_INTRODUCE));
                activityIntroduceVO.setActivityIntroduce(activityIntroduce);
                // 活动规则
                String activityRule = MapUtil.getString(jsonObject.get(ApiConstants.ACTIVITY_RULE));
                activityIntroduceVO.setActivityRule(activityRule);
                // 活动要求
                String activityRequire = MapUtil.getString(jsonObject.get(ApiConstants.ACTIVITY_REQUIRE));
                activityIntroduceVO.setActivityRequire(activityRequire);
                // 活动宣传图
                activityIntroduceVO.setActivityImages(Arrays.asList(MapUtil.getString(jsonObject.get(ApiConstants.ADVERTISING_IMAGE))));
            }
        }
        if (null == activityIntroduceVO.getActivityIntroduce()) {
            activityIntroduceVO.setActivityIntroduce("");
        }
        if (null == activityIntroduceVO.getActivityRule()) {
            activityIntroduceVO.setActivityRule("");
        }
        if (null == activityIntroduceVO.getActivityRequire()) {
            activityIntroduceVO.setActivityRequire("");
        }
        if (null == activityIntroduceVO.getActivityImages()) {
            activityIntroduceVO.setActivityImages(new LinkedList<>());
        }
    }

    /**
     * 获取有效可参加、正在参加的活动
     *
     * @param user
     * @param activityType
     * @param pageNum
     * @param pageSize
     * @return
     */
    public List<RunActivityTeamVO> getEffectiveRunActivity(ZnsUserEntity user, Integer activityType, Integer pageNum, Integer pageSize) {
        Page page = new Page<>(pageNum, pageSize);
        Long userId = user.getId();
        List<RunActivityTeamVO> list = new ArrayList<>();
        //全部或非官方活动
        if (Objects.isNull(activityType) || RunActivityTypeEnum.noOfficialTypes().contains(activityType)) {
            List<RunActivityTeamVO> activityEntityList = runActivityService.getNoOverActivityByUserId(page, userId, activityType);
            if (CollectionUtils.isEmpty(activityEntityList)) {
                return new ArrayList<>();
            }
            List<Long> ids = activityEntityList.stream().map(RunActivityTeamVO::getId).collect(Collectors.toList());
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .activityIds(ids).isDelete(0)
                    .build();
            List<ZnsRunActivityUserEntity> activityUserEntities = runActivityUserService.findList(userQuery);
            Map<Long, List<ZnsRunActivityUserEntity>> listMap = activityUserEntities.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getActivityId));
            list = activityEntityList.stream().map(a -> {
                List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = listMap.get(a.getId());
                RunActivityTeamVO activityTeamVO = wrapperActivityTeamVo(a, znsRunActivityUserEntities, user, null, null);
                return activityTeamVO;
            }).collect(Collectors.toList());

        }
        //全部或官方活动
        if (Objects.isNull(activityType)) {
            if (CollectionUtils.isEmpty(list) || list.size() < pageSize) {
                pageSize = pageSize - list.size();
                if (pageSize < 1) {
                    return list;
                }
                page.setSize(pageSize);
                List<RunActivityTeamVO> officialTeam = getOfficialRunActivity(page, user, null, null, null, Arrays.asList(1, 0));
                if (!CollectionUtils.isEmpty(officialTeam)) {
                    list.addAll(officialTeam);
                }
            }
        }

        return list;
    }

    /**
     * 获取首页此用户参加的官方活动，排行赛和累计跑
     *
     * @param page
     * @param loginUser
     * @param activityType
     * @param startTime
     * @param endTime
     * @param activityState
     * @return
     */
    public List<RunActivityTeamVO> getOfficialRunActivity(Page page, ZnsUserEntity loginUser, Integer activityType, ZonedDateTime startTime, ZonedDateTime endTime, List<Integer> activityState) {
        List<RunActivityTeamVO> list = runActivityService.getOfficialRunActivity(page, loginUser.getId(), activityType, startTime, endTime, activityState);
        for (RunActivityTeamVO activityEntity : list) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());

            if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityEntity.getActivityType())) {
                List<Integer> runningGoals = JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class);
                activityEntity.setRunningGoals(runningGoals);
            } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                activityEntity.setRunningGoals(JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class));
            } else if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(activityEntity.getActivityType())) {
                List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
                if (Objects.nonNull(milepostAward)) {
                    activityEntity.setRunningGoals(Arrays.asList(milepostAward.get(milepostAward.size() - 1).getMilepost().intValue()));
                }
            }
            activityEntity.setMaxReward(runActivityBizService.getPreMaxReward(jsonObject, activityEntity.getActivityType(), activityEntity.getUserCount(), BigDecimal.ZERO, null, activityEntity.getId(), null));

            //设置状态
            if (activityEntity.getActivityState() == 0 || activityEntity.getActivityState() == 1) {
                if (activityEntity.getUserState() == 0) {
                    activityEntity.setActivityState(0);
                } else if (activityEntity.getUserState() == 3) {
                    if (activityEntity.getActivityType() == 3) {
                        activityEntity.setActivityState(3);
                    } else if (activityEntity.getActivityType() == 5) {
                        activityEntity.setActivityState(1);
                    } else {
                        activityEntity.setActivityState(2);
                    }
                } else if (activityEntity.getUserState() == 1) {
                    activityEntity.setActivityState(1);
                }
            } else if (activityEntity.getActivityState() == 2) {
                activityEntity.setActivityState(4);
            } else {
                activityEntity.setActivityState(activityEntity.getActivityState());
            }
        }
        return list;
    }

    /**
     * 获取我的参赛-未进行或进行中的
     *
     * @param user
     * @param zoneId
     * @param request
     * @param checkVersion
     * @param appVersion
     * @param appType
     * @param languageCode
     * @return
     */
    public MyActivityVo getMyActivity(ZnsUserEntity user, String zoneId, ActivityIdRequest request, boolean checkVersion, Integer appVersion, Integer appType, String languageCode) {
        MyActivityVo map = getMyActivity(user, zoneId, request.getActivityId(), checkVersion, appVersion, appType);
        //老版本直接返回
        if (appVersion < 3000) {
            return map;
        }
        //新人Pk 数据
        NewerPKVo newerPKVo = getNewerPKVo(user, request.getEquipmentType());
        map.setNewerPKVo(newerPKVo);
        String currencyCode = userAccountService.getUserAccount(user.getId()).getCurrencyCode();
        ToBeInvolvedActivityQuery query = new ToBeInvolvedActivityQuery(TimeZone.getTimeZone(zoneId), user.getId()).setLanguage(languageCode);
        List<ToBeInvolvedActRespDto> list = toBeInvolvedActivityList(query);
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            List<MyActivityListVO> todayList1 = map.getTodayList();
            List<MyActivityListVO> moreList1 = map.getMoreList();
            if (!CollectionUtils.isEmpty(todayList1)) {
                todayList1.forEach(e -> fillActivityEntryFee(currencyCode, e));
            }
            if (!CollectionUtils.isEmpty(moreList1)) {
                moreList1.forEach(e -> fillActivityEntryFee(currencyCode, e));
            }
            return map;
        }
        //今日数据获取
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId));

        ZonedDateTime dayStartTime = DateUtil.getStartOfDate(now, TimeZone.getTimeZone(zoneId));
        ZonedDateTime dayEndTime = DateUtil.getEndOfDate(now, TimeZone.getTimeZone(zoneId));

        //数据分组
        List<MyActivityListVO> todayList = map.getTodayList();
        List<MyActivityListVO> moreList = map.getMoreList();
        for (ToBeInvolvedActRespDto toBeInvolvedActRespDto : list) {
            MyActivityListVO myActivityListVO = new MyActivityListVO();
            BeanUtils.copyProperties(toBeInvolvedActRespDto, myActivityListVO);
            ZonedDateTime activityStartTime = myActivityListVO.getActivityStartTime();
            ZonedDateTime activityEndTime = myActivityListVO.getActivityEndTime();
            activityStartTime = DateUtil.getDate2ByTimeZone(activityStartTime, TimeZone.getTimeZone(zoneId));
            activityEndTime = DateUtil.getDate2ByTimeZone(activityEndTime, TimeZone.getTimeZone(zoneId));
            SurplusActivityLevelVo surplusActivity = findSurplusActivity(myActivityListVO.getActivityId(), user.getId());
            myActivityListVO.setSurplusLevelCount(surplusActivity.getSurplusLevelCount());

            //路由获取
            RotationArea route = rotationAreaBizService.getNewActivityRoute(myActivityListVO.getActivityId(), myActivityListVO.getMainType(), null);
            myActivityListVO.setRouteArea(route);

            if ((Objects.nonNull(activityStartTime) && DateUtil.isBetweenE(activityStartTime, dayStartTime, dayEndTime))
                    || (Objects.nonNull(activityEndTime) && DateUtil.isBetweenE(activityEndTime, dayStartTime, dayEndTime))) {
                todayList.add(myActivityListVO);
            } else {
                if (myActivityListVO.getActivityType() == RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType()
                        || myActivityListVO.getActivityType() == RunActivityTypeEnum.OFFICIAL_ENENT.getType()
                        || myActivityListVO.getActivityType() == RunActivityTypeEnum.TASK_ACTIVITY.getType()
                        || !MainActivityTypeEnum.OLD.getType().equals(myActivityListVO.getMainType())) {
                    if (DateUtil.isBetweenE(now, activityStartTime, activityEndTime)) {
                        todayList.add(myActivityListVO);
                    } else {
                        moreList.add(myActivityListVO);
                    }
                } else {
                    moreList.add(myActivityListVO);
                }
            }
        }
        if (!CollectionUtils.isEmpty(todayList)) {
            todayList.forEach(e -> fillActivityEntryFee(currencyCode, e));
        }
        if (!CollectionUtils.isEmpty(moreList)) {
            moreList.forEach(e -> fillActivityEntryFee(currencyCode, e));
        }
        map.setTodayList(todayList);
        map.setMoreList(moreList);
        return map;
    }

    /**
     * 获取用户活动记录
     *
     * @param user
     * @param zoneId
     * @param activityId
     * @param checkUser
     * @param appVersion
     * @param appType
     * @return
     */
    public MyActivityVo getMyActivity(ZnsUserEntity user, String zoneId, Long activityId, boolean checkUser, Integer appVersion, Integer appType) {
        MyActivityVo map = new MyActivityVo();
        List<MyActivityListVO> todayList = new ArrayList();
        List<MyActivityListVO> moreList = new ArrayList();
        boolean testUser = sysConfigService.isTestUser(user.getEmailAddressEn());

        //今日数据获取
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId));

        ZonedDateTime dayStartTime = DateUtil.startOfDate(now);
        ZonedDateTime dayEndTime = DateUtil.endOfDate(now);
        log.info("getMyActivity now = " + now + ",dayStartTime=" + dayStartTime + ",dayEndTime=" + dayEndTime);
        List<ZnsRunActivityConfigEntity> configEntityList = runActivityConfigService.findAll();

        Map<Integer, ZnsRunActivityConfigEntity> configEntityMap = configEntityList.stream().collect(Collectors.toMap(ZnsRunActivityConfigEntity::getActivityType, Function.identity(), (x, y) -> x));

        List<Long> taskIds = new ArrayList<>();
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.foo_activity_config.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        Long fooActivityId = MapUtil.getLong(data.get("activityId"), 0l);       // 愚人节活动id
        Long skipActivityId = MapUtil.getLong(data.get("skipActivityId"), 0l);  // 排行赛事id
        ZnsRunActivityEntity skipActivity = runActivityService.selectActivityById(skipActivityId);

        log.info("activityId = " + activityId);
        log.info("skipActivity = " + skipActivity);
        log.info("!Objects.equals(skipActivity.getActivityType(), 3) = " + !Objects.equals(skipActivity.getActivityType(), 3));
        // 如果是非官方排行赛事，并且有activityId时，则需要通过taskId 去查询官方多人同跑的活动
        if (Objects.nonNull(activityId) && skipActivity != null && !Objects.equals(skipActivity.getActivityType(), 3)) {
            ZnsRunActivityEntity activity = runActivityService.findById(activityId);
            if (Objects.nonNull(activity) && activity.getActivityType() == 7) {
                log.info("============= activity id = " + activity.getId());
                List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(activityId, user.getId(), null);
                if (!CollectionUtils.isEmpty(runActivityUserTasks)) {
                    taskIds = runActivityUserTasks.stream().map(RunActivityUserTask::getId).collect(Collectors.toList());
                }
            }
        }

        List<MyActivityListVO> myActivity = runActivityService.getMyActivity(user.getId(), testUser, taskIds, checkUser);

        //过滤掉已经超过最迟入场时间的非官方和官方多人同跑
        ZonedDateTime nowDate = ZonedDateTime.now();
        myActivity = myActivity.stream().filter(k -> {
//            nowDate = DateUtil.addHours(nowDate,-8);
            //官方非官方检查是否能入场
            if (RunActivityTypeEnum.TEAM_RUN.getType().equals(k.getActivityType())
                    || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(k.getActivityType())) {

                //检查配置
                Map<String, Object> jsonObject = JsonUtil.readValue(k.getActivityConfig());
                Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
                if (null == lastEnterMinutes) {
                    lastEnterMinutes = 30;
                }
                // 这里的end是用数据库的0时区转换出来的，所以比较的当前时间也要ZonedDateTime.now()
                ZonedDateTime end = k.getActivityStartTime().plusMinutes(lastEnterMinutes);
                if (nowDate.compareTo(end) > 0) {
                    return false;
                }
            }

            return true;

        }).collect(Collectors.toList());

        // 过滤新人引导pk
        List<Integer> newPersonPkType = RunActivitySubTypeEnum.getNewPersonPkType();
        myActivity = myActivity
                .stream()
                .filter(cur -> !newPersonPkType.contains(cur.getActivityTypeSub()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(myActivity)) {
            return map;
        }
        List<Long> activityIds = myActivity.stream().map(MyActivityListVO::getActivityId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityOwnerUsers(activityIds);
        Map<Long, ZnsRunActivityUserEntity> ownerUserMap = activityUsers.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (x, y) -> x));
        for (MyActivityListVO myActivityListVO : myActivity) {

            if (fooActivityId.equals(activityId)) {               //如果是愚人节活动
                if (Objects.equals(skipActivity.getActivityType(), 3)) {
                    List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, user.getId());
                    if (!CollectionUtils.isEmpty(userTasks)) {
                        Integer days = DateUtil.daysBetween(DateUtil.endOfDate(userTasks.get(0).getGmtCreate()), DateUtil.endOfDate(ZonedDateTime.now())) + 1;
                        // 如果是第4天 && 如果愚人节配置的是官方排行赛事，则将非此活动的官方赛事过虑掉
                        if (Objects.equals(days, 4) && !Objects.equals(myActivityListVO.getActivityId(), skipActivityId)) {
                            log.info("当前配置的是官网排行赛事，非官方排行赛事 activityId = " + myActivityListVO.getActivityId() + " 过滤掉");
                            continue;
                        }
                    }
                } else if (Objects.equals(skipActivity.getActivityType(), 4)     // 2.如果配置的是官方多人同跑，则将排行赛事过滤掉
                        && Objects.equals(myActivityListVO.getActivityType(), 3)) {
                    log.info("当前配置的是官方多人同跑， 官方排行赛事 activityId = " + myActivityListVO.getActivityId() + "过滤掉");
                    continue;
                }
            }

            Map<String, Object> jsonObject = JsonUtil.readValue(myActivityListVO.getActivityConfig());
            myActivityListVO.setCoverImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
            myActivityListVO.setRoomNumber(myActivityListVO.getActivityId());
            ZnsRunActivityConfigEntity activityConfig = configEntityMap.get(myActivityListVO.getActivityType());
            if (activityConfig != null) {
                Map<String, Object> object = JsonUtil.readValue(activityConfig.getActivityConfig());
                myActivityListVO.setMainTitle(MapUtil.getString(object.get("mainTitle")));
                myActivityListVO.setNextTitle(MapUtil.getString(object.get("nextTitle")));
                myActivityListVO.setSubTitle(MapUtil.getString(object.get("subTitle")));
                myActivityListVO.setRoomNumber(myActivityListVO.getActivityId());
            }
            if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(myActivityListVO.getActivityType())) {
                //查询排名
                Integer currentRank = runActivityUserService.getCurrentRank(myActivityListVO.getActivityId(), myActivityListVO.getCompleteRuleType(), user.getId());
                myActivityListVO.setRank(Objects.isNull(currentRank) ? 0 : currentRank);
            } else if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(myActivityListVO.getActivityType())) {
                List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
                setNextMilepost(myActivityListVO, milepostAward);
            } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(myActivityListVO.getActivityType())) {
                //查询参与用户
                List<UserSimpleVo> activitySimpleUser = runActivityBizService.findActivitySimpleUser(myActivityListVO.getActivityId(), 999);
                myActivityListVO.setParticipantList(activitySimpleUser);
            }
            if (RunActivityTypeEnum.TEAM_RUN.getType().equals(myActivityListVO.getActivityType())) {
                // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
                Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
                if (null == lastEnterMinutes) {
                    lastEnterMinutes = 30;
                }
                long teamMillisecond = lastEnterMinutes.intValue() * 60000;
                myActivityListVO.setLastEnterTeamRunTime(Long.valueOf(teamMillisecond));
                // 挑战跑开始跑前多少分钟可入场(配置分钟)
                Integer beforeEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
                if (null == beforeEnterMinutes) {
                    beforeEnterMinutes = 5;
                }
                long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
                myActivityListVO.setRunBeforeEnter(challengeMillisecond);
                ZnsRunActivityUserEntity ownerUser = ownerUserMap.get(myActivityListVO.getActivityId());
                if (Objects.nonNull(ownerUser) && !ownerUser.getUserId().equals(user.getId())) {
                    myActivityListVO.setActivityTitle(I18nMsgUtils.getMessage("activity.title.initiatedByUser", ownerUser.getNickname())); // "Multi-Player Run initiated by " + ownerUser.getNickname()
                } else {
                    myActivityListVO.setActivityTitle(I18nMsgUtils.getMessage("activity.title.initiatedByYou"));
                }
                List<ActivityEntryFee> activityEntryFeeList = activityEntryFeeService.findByActivityId(myActivityListVO.getActivityId());
                if (!CollectionUtils.isEmpty(activityEntryFeeList)) {
                    String currencyCode = userAccountService.getUserAccount(user.getId()).getCurrencyCode();
                    ActivityEntryFee activityEntryFee = activityEntryFeeList.stream().filter(e -> e.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
                    BigDecimal entryFee = I18nConstant.currencyFormat(currencyCode, activityEntryFee.getEntryFee());
                    myActivityListVO.setActivityEntryFee(entryFee);
                }
            } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(myActivityListVO.getActivityType())) {
                // 挑战跑开始跑前多少分钟可入场(配置分钟)
                Integer beforeEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.CHALLENGE_ACTIVITY_BEFORE_ENTER));
                if (null == beforeEnterMinutes) {
                    beforeEnterMinutes = 5;
                }
                long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
                ZnsRunActivityEntity activity = runActivityService.findById(myActivityListVO.getActivityId());
                myActivityListVO.setRaceSetType(1);
                if (Objects.nonNull(activity)) {
                    if (Objects.nonNull(activity.getAppointmentStartTime())) {
                        challengeMillisecond = 30 * 60000;
                        myActivityListVO.setAppointmentStartTime(activity.getAppointmentStartTime());
                        myActivityListVO.setRaceSetType(2);
                    }
                }
                myActivityListVO.setBeforeEnterChallengeRunTime(challengeMillisecond);
                UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(myActivityListVO.getActivityId());
                myActivityListVO.setPkType(Objects.nonNull(userFriendMatch) ? 2 : 1);

                if (Objects.equals(myActivityListVO.getActivityTypeSub(), 3)) {
                    PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(myActivityListVO.getActivityId(), 1);
                    PkChallengeRecord wasPkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(myActivityListVO.getActivityId(), 0);
                    if (pkChallengeRecord != null && wasPkChallengeRecord != null) {
                        myActivityListVO.setChallengeUserId(pkChallengeRecord.getUserId());
                        myActivityListVO.setWasChallengeUserId(wasPkChallengeRecord.getUserId());
                        myActivityListVO.setPkBatchNo(pkChallengeRecord.getBatchNo());
                    }
                }

            } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(myActivityListVO.getActivityType())) {
                // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
                Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
                if (null == lastEnterMinutes) {
                    lastEnterMinutes = 30;
                }
                long teamMillisecond = lastEnterMinutes.intValue() * 60000;
                myActivityListVO.setLastEnterTeamRunTime(Long.valueOf(teamMillisecond));
                // 挑战跑开始跑前多少分钟可入场(配置分钟)
                Integer beforeEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
                if (null == beforeEnterMinutes) {
                    beforeEnterMinutes = 5;
                }
                long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
                myActivityListVO.setRunBeforeEnter(challengeMillisecond);
                //房间号处理
                Integer goal = myActivityListVO.getCompleteRuleType() == 1 ? myActivityListVO.getTargetRunMileage() : myActivityListVO.getTargetRunTime();
                Long roomNumber = NumberUtils.getGoalImNumber(myActivityListVO.getActivityId(), goal, myActivityListVO.getCompleteRuleType());
                myActivityListVO.setRoomNumber(roomNumber);
            }

            if (myActivityListVO.getActivityType() == 1 || myActivityListVO.getActivityType() == 2) {
                myActivityListVO.setAcceptedNum(runActivityUserService.queryActivityUserCount(myActivityListVO.getActivityId(), true));
            } else {
                myActivityListVO.setAcceptedNum(runActivityUserService.queryActivityUserCount(myActivityListVO.getActivityId(), true));
            }
            ZonedDateTime activityStartTime = DateUtil.getDate2ByTimeZone(myActivityListVO.getActivityStartTime(), TimeZone.getTimeZone(zoneId));
            ZonedDateTime activityEndTime = DateUtil.getDate2ByTimeZone(myActivityListVO.getActivityEndTime(), TimeZone.getTimeZone(zoneId));
            if ((Objects.nonNull(activityStartTime) && DateUtil.isBetweenE(activityStartTime, dayStartTime, dayEndTime))
                    || (Objects.nonNull(activityEndTime) && DateUtil.isBetweenE(activityEndTime, dayStartTime, dayEndTime))) {
                todayList.add(myActivityListVO);
            } else {
                if (myActivityListVO.getActivityType() == RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType()
                        || myActivityListVO.getActivityType() == RunActivityTypeEnum.OFFICIAL_ENENT.getType()
                        || myActivityListVO.getActivityType() == RunActivityTypeEnum.TASK_ACTIVITY.getType()) {
                    if (DateUtil.isBetweenE(now, activityStartTime, activityEndTime)) {
                        todayList.add(myActivityListVO);
                    } else {
                        moreList.add(myActivityListVO);
                    }
                } else {
                    moreList.add(myActivityListVO);
                }
            }

        }

        // 补充团队赛相关信息
        fillupTeamActivityInfo(user, todayList, moreList);
        // 构建主题活动的路由信息.
        buildThemeActivityRoute(appType, todayList, user.getId());
        buildThemeActivityRoute(appType, moreList, user.getId());
        map.setTodayList(todayList);
        map.setMoreList(moreList);
        return map;
    }

    /**
     * 设置下一个里程碑
     *
     * @param myActivityListVO
     * @param milepostAward
     */
    private void setNextMilepost(MyActivityListVO myActivityListVO, List<MilepostAwardDto> milepostAward) {
        for (int i = 0; i < milepostAward.size(); i++) {
            MilepostAwardDto milepostAwardDto = milepostAward.get(i);

            if (myActivityListVO.getCompleteRuleType() == 1) {
                if (myActivityListVO.getRunMileage().compareTo(milepostAwardDto.getMilepost()) < 0) {
                    myActivityListVO.setNextMilepost(milepostAwardDto.getMilepost().intValue());
                    return;
                }
                myActivityListVO.setNextMilepost(milepostAwardDto.getMilepost().intValue());
            } else if (myActivityListVO.getCompleteRuleType() == 2) {
                if (myActivityListVO.getRunTime() < milepostAwardDto.getMilepost().intValue()) {
                    myActivityListVO.setNextMilepost(milepostAwardDto.getMilepost().intValue());
                    return;
                }
                myActivityListVO.setNextMilepost(milepostAwardDto.getMilepost().intValue());
            }
        }
    }

    /**
     * 填充团队赛信息
     *
     * @param user
     * @param todayList
     * @param moreList
     */
    private void fillupTeamActivityInfo(ZnsUserEntity user, List<MyActivityListVO> todayList, List<MyActivityListVO> moreList) {
        Long userId = user.getId();
        for (MyActivityListVO myActivityListVO : todayList) {
            if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(myActivityListVO.getActivityType())) {
                Long activityId = myActivityListVO.getActivityId();

                //查找活动
                ZnsRunActivityEntity entity = runActivityService.findOne(
                        RunActivityQuery.builder().id(activityId).isDelete(0).build());
                if (entity.getActivityState() == 0) {
                    myActivityListVO.setActivityState(1);
                } else if (entity.getActivityState() == 1) {
                    myActivityListVO.setActivityState(2);

                } else if (entity.getActivityState() == 2) {
                    myActivityListVO.setActivityState(4);

                }
                if (entity.getActivityState() == -1 || myActivityListVO.getUserState() == 5) {
                    myActivityListVO.setActivityState(-1);

                }


                ActivityTeam team = activityUserBizService.getUserCurrentTeam(activityId, userId);
                if (team != null) {
                    myActivityListVO.setRank(team.getRank());
                    myActivityListVO.setRunMileage(BigDecimal.valueOf(team.getMillage()));
                }
            }
        }
        for (MyActivityListVO myActivityListVO : moreList) {
            if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(myActivityListVO.getActivityType())) {
                Long activityId = myActivityListVO.getActivityId();

                //查找活动
                ZnsRunActivityEntity entity = runActivityService.findOne(
                        RunActivityQuery.builder().id(activityId).isDelete(0).build());

                if (entity.getActivityState() == 0) {
                    myActivityListVO.setActivityState(1);
                } else if (entity.getActivityState() == 1) {
                    myActivityListVO.setActivityState(2);

                } else if (entity.getActivityState() == 2) {
                    myActivityListVO.setActivityState(4);

                } else if (entity.getActivityState() == -1 || myActivityListVO.getUserState() == 5) {
                    myActivityListVO.setActivityState(-1);

                }

                ActivityTeam team = activityUserBizService.getUserCurrentTeam(activityId, userId);
                if (team != null) {
                    myActivityListVO.setRank(team.getRank());
                }
            }
        }
    }


    /**
     * 构建主题活动的路由信息
     *
     * @param appType      APP类型
     * @param activityList
     */
    private void buildThemeActivityRoute(Integer appType, List<MyActivityListVO> activityList, Long userId) {
        if (!CollectionUtils.isEmpty(activityList)) {
            Set<Long> runActivityIdSet = activityList.stream()
                    .filter(today -> RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(today.getActivityType()))
                    .map(MyActivityListVO::getActivityId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(runActivityIdSet)) {
                return;
            }
            List<OperationalActivity> operationalActivityList = operationalActivityService.findList(OperationalActivityQuery.builder().runActivityIds(runActivityIdSet).isDelete(0).build());
            Map<Long, OperationalActivity> runActivityId2OpMap = new HashMap<>();
            for (OperationalActivity activity : operationalActivityList) {
                runActivityId2OpMap.put(activity.getRunActivityId(), activity);
            }

            activityList.forEach(today -> {
                Integer activityType = today.getActivityType();
                if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(activityType)) {
                    Long Id = today.getActivityId();
                    OperationalActivity operationalActivity = runActivityId2OpMap.get(Id);
                    if (operationalActivity == null) {
                        RotationArea rotationArea = runActivityService.getDefaultRotationArea(today, activityType);
                        today.setRouteArea(rotationArea);
                        return;
                    }
                    RotationArea rotationArea = operationalActivityService.getRotationArea(appType, operationalActivity, userId);
                    if (rotationArea == null) {
                        rotationArea = runActivityService.getDefaultRotationArea(today, activityType);
                    }
                    today.setRouteArea(rotationArea);
                }
            });
        }
    }


    /**
     * 获取是否展示新人Pk状态
     *
     * @param user
     * @param equipmentType
     * @return true:展示，false：不展示
     */
    public NewerPKVo getNewerPKVo(ZnsUserEntity user, Integer equipmentType) {
        Long userId = user.getId();
        NewerPKVo result = new NewerPKVo();
        if (userExtraService.isPutChannelUser(userId)) {
            result.setIsShowNewerPK(false);
            return result;
        }
        int day = DateUtil.betweenDay(user.getCreateTime(), ZonedDateTime.now());
        if (day > 7) {
            log.info("RunActivityManager#getShowNewerPKStatus-----是否展示新人Pk状态,不是新人，userId:{}", userId);
            result.setIsShowNewerPK(false);
            return result;
        }
        String key = "show:newerpk:status:" + userId;
        boolean exists = redissonClient.getBucket(key).isExists();
        if (exists) {
            log.info("RunActivityManager#getShowNewerPKStatus-----是否展示新人Pk状态,已完成新人pk，userId:{}", userId);
            result.setIsShowNewerPK(false);
            return result;
        }
        //新人道具赛判断
        List<ZnsRunActivityUserEntity> propList = runActivityUserService.findUserCompleteActivityByType(userId,RunActivityTypeEnum.NEW_USER_PROP.getType(), null);
        if (!CollectionUtils.isEmpty(propList)){
            //已完成新人赛事，保存7天
            log.info("RunActivityManager#getShowNewerPKStatus-----是否展示新人Pk状态,已完成新人赛事，userId:{}",userId);
            redissonClient.getBucket(key).set("1",7L, TimeUnit.DAYS);
            result.setIsShowNewerPK(false);
            return result;
        }

        //查询用户新人pk记录
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findUserCompleteActivityByType(userId, RunActivityTypeEnum.CHALLENGE_RUN.getType(), RunActivitySubTypeEnum.getNewPersonPkType());
        if (!CollectionUtils.isEmpty(list)) {
            //已完成新人赛事，保存7天
            log.info("RunActivityManager#getShowNewerPKStatus-----是否展示新人Pk状态,已完成新人赛事，userId:{}", userId);
            redissonClient.getBucket(key).set("1", 7L, TimeUnit.DAYS);
            result.setIsShowNewerPK(false);
            return result;
        }

        //可以发起新人pk
        /*String dataKey = "show:newerpk:data:"+userId;
        NewerPKVo cacheDate = (NewerPKVo)redissonClient.getBucket(dataKey).get();
        if (cacheDate != null){
            return cacheDate;
        }*/
        result.setIsShowNewerPK(true);
        if (Objects.isNull(equipmentType)) {
            return result;
        }
        NewPersonPkVo newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(1L);
        if (Objects.nonNull(newPersonPkVo)) {
            RobotSpeed robotSpeed = newPersonPkVo.getRobotSpeed();
            List<RobotBean> robotLists = robotSpeed.getRobotLists();
            List<RobotBean> walkRobotLists = robotSpeed.getWalkRobotLists();
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(robotLists)) {
                int index = new Random().nextInt(robotLists.size());
                result.setRunDetailsId(robotLists.get(index).getDetailId());
            }
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(walkRobotLists)) {
                int index = new Random().nextInt(walkRobotLists.size());
                result.setWalkDetailsId(walkRobotLists.get(index).getDetailId());
            }
        }
        if (DeviceConstant.EquipmentTypeEnum.TYPE_20.getCode().equals(equipmentType)) {
            NewPersonPkVo newPersonPkBikeVo = newPersonPkBizService.selectNewPersonPkVo(3L);
            RobotSpeed bikeRobotSpeed = newPersonPkBikeVo.getRobotSpeed();
            List<RobotBean> bikeRobotLists = bikeRobotSpeed.getRobotLists();
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(bikeRobotLists)) {
                int index = new Random().nextInt(bikeRobotLists.size());
                result.setChallengeDataDetailsId(bikeRobotLists.get(index).getDetailId());
            }
        }
        if (DeviceConstant.EquipmentTypeEnum.TYPE_40.getCode().equals(equipmentType)) {
            NewPersonPkVo newPersonPkRowingVo = newPersonPkBizService.selectNewPersonPkVo(4L);
            RobotSpeed rowingRobotSpeed = newPersonPkRowingVo.getRobotSpeed();
            List<RobotBean> rowingRobotLists = rowingRobotSpeed.getRobotLists();
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(rowingRobotLists)) {
                int index = new Random().nextInt(rowingRobotLists.size());
                result.setChallengeDataDetailsId(rowingRobotLists.get(index).getDetailId());
            }
        }
        // redissonClient.getBucket(dataKey).set(result,7L,TimeUnit.DAYS);
        return result;
    }


    /**
     * 待参与活动/Today's Race
     *
     * @param query
     * @return
     */
    private List<ToBeInvolvedActRespDto> toBeInvolvedActivityList(ToBeInvolvedActivityQuery query) {
        String currentTime = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), query.getTimeZone(), DateUtil.DATE_TIME_SHORT);
        query.setCurrentTime(currentTime);
        List<ToBeInvolvedActListVo> list = mainActivityService.toBeInvolvedActivityList(query);
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            log.info("toBeInvolvedActivityList end,查询为空");
            return null;
        }
        List<Long> activityIds = list.stream().map(ToBeInvolvedActListVo::getId).collect(Collectors.toList());
        List<MyRaceCalendarSubActivityListVo> subActivityListVos = subActivityService.findListByActivityIds(activityIds);
        Map<Long, MyRaceCalendarSubActivityListVo> subActivityListVoMap = subActivityListVos.stream().collect(Collectors.toMap(MyRaceCalendarSubActivityListVo::getMainActivityId, Function.identity(), (x, y) -> x));
        Map<Long, String> languageTitleMap = activityDisseminateService.findTitleMapByActivityIdAndLanguage(activityIds, query.getLanguage());
        //查询默认语言
        Map<Long, String> defaultTitleMap = activityDisseminateService.findTitleMapByActivityIdAndDefault(activityIds);

        List<ToBeInvolvedActRespDto> dtoList = list.stream().map(a -> {
            MyRaceCalendarSubActivityListVo myRaceCalendarSubActivityListVo = subActivityListVoMap.get(a.getId());
            String title = activityDisseminateService.getActivityTitle(a.getId(), languageTitleMap, defaultTitleMap);
            //todo convert
            ToBeInvolvedActRespDto dto = new ToBeInvolvedActRespDto();
            dto.setActivityId(a.getId());
            Long start = DateUtil.getStampByZone(a.getStartTime(), a.getTimeStyle() == 0 ? "UTC" : query.getTimeZone().getID());
            Long end = DateUtil.getStampByZone(a.getEndTime(), a.getTimeStyle() == 0 ? "UTC" : query.getTimeZone().getID());
            dto.setActivityStartTime(new Date(start));
            dto.setActivityEndTime(new Date(end));
            dto.setActivityState(a.getActivityState());
            dto.setMainType(a.getMainType());
            dto.setActivityType(a.getOldType());
            dto.setRunMileage(a.getRunMileage());
            dto.setRunTime(a.getRunTime());
            dto.setTargetRunMileage(a.getTargetRunMileage());
            dto.setTargetRunTime(a.getTargetRunTime());
            dto.setUserState(a.getUserState());
            if (Objects.nonNull(myRaceCalendarSubActivityListVo)) {
                dto.setActivityRouteId(myRaceCalendarSubActivityListVo.getRouteId());
                dto.setRouteType(myRaceCalendarSubActivityListVo.getRouteType());
                dto.setCompleteRuleType(myRaceCalendarSubActivityListVo.getCompleteRuleType());
                if (Objects.isNull(dto.getRunMileage()) && Objects.isNull(dto.getRunTime())) {
                    if (Objects.equals(dto.getCompleteRuleType(), 1)) {
                        dto.setTargetRunMileage(myRaceCalendarSubActivityListVo.getTarget());
                    }
                    if (Objects.equals(dto.getCompleteRuleType(), 2)) {
                        dto.setTargetRunTime(myRaceCalendarSubActivityListVo.getTarget());
                    }
                }
            }
            dto.setActivityTitle(title);
            return dto;
        }).collect(Collectors.toList());
        return dtoList;
    }

    private void fillActivityEntryFee(String currencyCode, MyActivityListVO myActivityListVO) {
        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(myActivityListVO.getActivityType())
                || RunActivitySubTypeEnum.FRIENDS_MATCHING.getType().equals(myActivityListVO.getActivityTypeSub())) {
            myActivityListVO.setCurrency(I18nConstant.buildDefaultCurrency());
            if (runActivityService.checkNewActivity(myActivityListVO.getActivityId())) {
                myActivityListVO.setCurrency(I18nConstant.buildCurrency(currencyCode));
            }
            List<ActivityEntryFee> activityEntryFeeList = activityEntryFeeService.findByActivityId(myActivityListVO.getActivityId());
            if (!CollectionUtils.isEmpty(activityEntryFeeList)) {
                ActivityEntryFee activityEntryFee = activityEntryFeeList.stream().filter(e -> e.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
                BigDecimal entryFee = I18nConstant.currencyFormat(currencyCode, activityEntryFee.getEntryFee());
                myActivityListVO.setActivityEntryFee(entryFee);
            }
        }
    }

    public SurplusActivityLevelVo findSurplusActivity(Long activityId, Long userId) {
        SurplusActivityLevelVo vo = new SurplusActivityLevelVo();
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userId);
        //表示全部完成
        if (Objects.nonNull(activityUser) && activityUser.getIsComplete() == 1) {
            return vo;
        }

        List<SeriesActivityRel> subActivityList = seriesActivityRelService.findSubActivity(activityId);
        if (org.springframework.util.CollectionUtils.isEmpty(subActivityList)) {
            return vo;
        }

        //没报名返回第一关
        if (Objects.isNull(activityUser)) {
            return surplusActivityVo(subActivityList, 1);
        }

        List<Long> subActivityIds = subActivityList.stream().map(SeriesActivityRel::getSegmentActivityId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(subActivityIds, userId);
        if (org.springframework.util.CollectionUtils.isEmpty(activityUsers)) {
            return surplusActivityVo(subActivityList, 1);
        }
        List<ZnsRunActivityUserEntity> completeUsers = activityUsers.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(completeUsers)) {
            return surplusActivityVo(subActivityList, 1);
        }
        List<Long> completeActivityIds = completeUsers.stream().map(ZnsRunActivityUserEntity::getActivityId).collect(Collectors.toList());
        subActivityList = subActivityList.stream().sorted(Comparator.comparing(SeriesActivityRel::getLevel)).collect(Collectors.toList());

        vo.setSurplusLevelCount(subActivityList.size() - completeActivityIds.size());

        for (SeriesActivityRel seriesActivityRel : subActivityList) {
            if (!completeActivityIds.contains(seriesActivityRel.getSegmentActivityId())) {
                SubActivityVo subActivity = subActivityService.findSubActivity(seriesActivityRel.getSegmentActivityId());
                vo.setNextLevel(subActivity);
                return vo;
            }
        }

        return vo;
    }

    private SurplusActivityLevelVo surplusActivityVo(List<SeriesActivityRel> subActivityList, int level) {
        SurplusActivityLevelVo vo = new SurplusActivityLevelVo();
        vo.setSurplusLevelCount(subActivityList.size());
        SeriesActivityRel seriesActivityRel = subActivityList.stream().filter(s -> s.getLevel() == level).findFirst().orElse(null);
        SubActivityVo subActivity = subActivityService.findSubActivity(seriesActivityRel.getSegmentActivityId());
        vo.setNextLevel(subActivity);
        return vo;
    }

    public Page<RunActivityTeamVO> getPkActivity(Page page, ZnsUserEntity user, boolean testUser, boolean checkVersion) {
        Page<RunActivityTeamVO> activityListPage = runActivityService.getPkActivityList(page, user.getId(), testUser, checkVersion);
        List<RunActivityTeamVO> activityList = activityListPage.getRecords();
        if (CollectionUtils.isEmpty(activityList)) {
            return page;
        }
        // 过滤新人引导pk && 4.0后离线PK
        List<Integer> newPersonPkType = RunActivitySubTypeEnum.getNewPersonPkType();
        activityList = activityList
                .stream()
                .filter(cur -> !newPersonPkType.contains(cur.getActivityTypeSub()))
                .collect(Collectors.toList());
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(user.getId());
        //所有账户
        List<ZnsUserAccountEntity> userAccounts = userAccountService.getUserAccounts(user.getId());

        for (RunActivityTeamVO runActivityTeamVO : activityList) {
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .activityId(runActivityTeamVO.getId()).isDelete(0)
                    .build();
            userQuery.setOrders(List.of(OrderItem.asc("user_type")));
            List<ZnsRunActivityUserEntity> activityUserEntities = runActivityUserService.findList(userQuery);

            wrapperActivityTeamVo(runActivityTeamVO, activityUserEntities, user, null, 3);
            Currency currency = new Currency();
            if (!Objects.equals(runActivityTeamVO.getActivityTypeSub(), RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType())) {
                activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, runActivityTeamVO.getId(), user.getId());
            } else {
                activityAwardCurrencyBizService.fillPkAwardAmountCurrency(currency, runActivityTeamVO.getId(), user.getId(), userAccount, userAccounts);
            }
            runActivityTeamVO.setCurrency(currency);
        }

        activityListPage.setRecords(activityList);
        return activityListPage;
    }

    /**
     * myrace 日历
     *
     * @param date
     * @param user
     * @param timeZone
     * @param checkVersion
     * @param appVersion
     * @param appType
     * @return
     */
    public List<RunActivityTeamVO> myDayRecords(ZonedDateTime date, ZnsUserEntity user, TimeZone timeZone, boolean checkVersion, Integer appVersion, Integer appType, String languageCode) {
        List<RunActivityTeamVO> list = myDayRecords(date, user, timeZone, checkVersion, appVersion, appType);

        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        ZonedDateTime startTime = DateUtil.getStartOfDate(date, timeZone);
        ZonedDateTime endTime = DateUtil.getEndOfDate(date, timeZone);
        //新活动获取
        MyRaceCalendarActivityQuery myRaceCalendarActivityQuery = new MyRaceCalendarActivityQuery(DateUtil.getDate3ByTimeZone(startTime, timeZone, DateUtil.DATE_TIME_SHORT),
                DateUtil.getDate3ByTimeZone(endTime, timeZone, DateUtil.DATE_TIME_SHORT), timeZone, checkVersion, user.getId(), languageCode);
        myRaceCalendarActivityQuery.setZoneId(user.getZoneId());
        myRaceCalendarActivityQuery.setUserStartTime(startTime).setUserEndTime(endTime);
        List<RunActivityTeamVO> dataList = myRaceCalendarActivityList(myRaceCalendarActivityQuery);
        if (!org.springframework.util.CollectionUtils.isEmpty(dataList)) {
            List<RunActivityTeamVO> newActivityList = dataList.stream().map(a -> {
                RunActivityTeamVO vo = new RunActivityTeamVO();
                BeanUtils.copyProperties(a, vo);
                vo.setStatus(1);
                //兼容老活动状态
                if (vo.getActivityState() == 0) {
                    vo.setActivityState(1);
                } else if (vo.getActivityState() == 1) {
                    vo.setActivityState(2);
                } else if (vo.getActivityState() == 2) {
                    vo.setActivityState(4);
                } else if (vo.getActivityState() == 3) {
                    vo.setStatus(-1);
                }
                //路由获取
                RotationArea route = rotationAreaBizService.getNewActivityRoute(vo.getId(), vo.getMainType(), null);
                vo.setRouteArea(route);
                //获取最新跑步明细id
                //系列赛处理
                if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(vo.getMainType())) {
                    List<Long> subActivityId = seriesActivityRelService.findSubActivityId(vo.getId());
                    ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.getLastUserDetailByActivityIds(user.getId(), subActivityId);
                    if (Objects.nonNull(entity)) {
                        vo.setRunDataDetailsId(entity.getId());
                    }
                } else {
                    List<ZnsUserRunDataDetailsEntity> entityList = userRunDataDetailsService.getUserDetailByActivityId(new Page(1, 1), user.getId(), vo.getId());
                    if (!org.springframework.util.CollectionUtils.isEmpty(entityList)) {
                        vo.setRunDataDetailsId(entityList.get(0).getId());
                    }
                }
                return vo;
            }).collect(Collectors.toList());
            list.addAll(newActivityList);
        }
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(user.getId());

        String lastEnterTeamRunTime = sysConfigService.selectConfigByKey(ConfigKeyEnums.TEAM_ACTIVITY_LAST_ENTER.getCode());
        if (!StringUtils.hasText(lastEnterTeamRunTime)) {
            lastEnterTeamRunTime = "30";
        }
        long teamMillisecond = Integer.valueOf(lastEnterTeamRunTime) * 60000;
        //所有账户
        List<ZnsUserAccountEntity> userAccounts = userAccountService.getUserAccounts(user.getId());

        for (RunActivityTeamVO runActivityTeamVO : list) {
            runActivityTeamVO.setLastEnterTeamRunTime(teamMillisecond);
            Currency currency = new Currency();
            boolean hasAward = !"".equals(runActivityTeamVO.getPrizePool()) && !"0".equals(runActivityTeamVO.getPrizePool()) && !"?".equals(runActivityTeamVO.getPrizePool());
            if (!Objects.equals(runActivityTeamVO.getActivityTypeSub(), RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType())) {
                activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, runActivityTeamVO.getId(), user.getId(), userAccount, userAccounts, hasAward);
            } else {
                activityAwardCurrencyBizService.fillPkAwardAmountCurrency(currency, runActivityTeamVO.getId(), user.getId(), userAccount, userAccounts);
            }
            Boolean i18nActivity = isI18nActivity(runActivityTeamVO.getActivityType(), runActivityTeamVO.getId());
            if (i18nActivity || !MainActivityTypeEnum.OLD.getType().equals(runActivityTeamVO.getMainType())) {
                runActivityTeamVO.setCurrency(currency);
            } else {
                runActivityTeamVO.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode()));
            }

            //报名费多币种
            List<ActivityEntryFee> entryFees = activityEntryFeeService.findByActivityId(runActivityTeamVO.getId());
            if (!org.springframework.util.CollectionUtils.isEmpty(entryFees)) {
                BigDecimal entryFee = entryFees.stream().filter(s -> s.getCurrencyCode().equals(userAccount.getCurrencyCode())).findFirst().get().getEntryFee();
                runActivityTeamVO.setEarnestMoney(entryFee.toString());
            }
            SurplusActivityLevelVo surplusActivity = findSurplusActivity(runActivityTeamVO.getId(), user.getId());
            runActivityTeamVO.setSurplusLevelCount(surplusActivity.getSurplusLevelCount());
            //新pk活动标题处理
            if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(runActivityTeamVO.getActivityType())) {
                //设置路由信息
                runActivityTeamVO.setRouteArea(rotationAreaBizService.getNewActivityRoute(runActivityTeamVO.getId(), runActivityTeamVO.getMainType(), null));
                ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(runActivityTeamVO.getId(), user.getLanguageCode());
                if (Objects.nonNull(activityDisseminate)) {
                    runActivityTeamVO.setActivityTitle(activityDisseminate.getTitle());
                    runActivityTeamVO.setTeamName(activityDisseminate.getTitle());
                }
            }
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            Collections.sort(list, (x, y) -> {
                return compareActivityState(x, y);
            });

        }
        return list;
    }


    /**
     * 获取用户当天活动记录
     *
     * @param date
     * @param user
     * @param timeZone
     * @param checkVersion
     * @param appVersion
     * @param appType
     * @return
     */
    private List<RunActivityTeamVO> myDayRecords(ZonedDateTime date, ZnsUserEntity user, TimeZone timeZone, boolean checkVersion, Integer appVersion, Integer appType) {
        ZonedDateTime startTime = DateUtil.getStartOfDate(date, timeZone);
        ZonedDateTime endTime = DateUtil.getEndOfDate(date, timeZone);
        boolean testUser = sysConfigService.isTestUser(user.getEmailAddressEn());

        List<RunActivityTeamVO> activityList = runActivityService.myDayRecords(startTime, endTime, user.getId(), testUser, checkVersion);
        if (!CollectionUtils.isEmpty(activityList)) {
            dealRunActivityTeamVO(activityList, user, null);
            // 构建主题活动路由信息
            operationalActivityService.buildThemeActivityRouteArea(appType, activityList, user.getId());
            fillupTeamActivityRecords(user, activityList);
        }

        return activityList;
    }

    /**
     * 处理活动信息
     *
     * @param activityList
     * @param user
     * @param isHomePage
     */
    private void dealRunActivityTeamVO(List<RunActivityTeamVO> activityList, ZnsUserEntity user, Integer isHomePage) {
        for (RunActivityTeamVO vo : activityList) {
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .activityId(vo.getId())
                    .isDelete(0)
                    .build();
            userQuery.setOrders(List.of(OrderItem.asc("user_type")));


            List<ZnsRunActivityUserEntity> activityUserEntities = runActivityUserService.findList(userQuery);

            wrapperActivityTeamVo(vo, activityUserEntities, user, isHomePage, 3);
        }
    }

    /**
     * 封装活动信息
     */
    private RunActivityTeamVO wrapperActivityTeamVo(RunActivityTeamVO vo, List<ZnsRunActivityUserEntity> activityUserEntities, ZnsUserEntity user, Integer isHomePage, Integer participantListLimit) {
        Map<Long, List<ZnsRunActivityUserEntity>> activityUserMap = activityUserEntities.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getActivityId));
        //发起人
        ZnsRunActivityUserEntity ownerActivityUser = activityUserEntities.stream().filter(a -> a.getUserType() == 1).findFirst().orElse(null);
        //查询所有队伍用户
        List<ZnsUserEntity> userEntityList = userService.findByIds(activityUserEntities.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList()));
        Map<Long, ZnsUserEntity> userEntityMap = userEntityList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        //用户账户信息
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
        //查询用户关系
        List<ZnsUserFriendEntity> friendList = userFriendService.getFriendList(user.getId(), userEntityList.stream().map(ZnsUserEntity::getId).collect(Collectors.toList()));
        Map<Long, ZnsUserFriendEntity> friendEntityMap = friendList.stream().collect(Collectors.toMap(ZnsUserFriendEntity::getFriendId, Function.identity()));

        List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = activityUserMap.get(vo.getId());
        if (CollectionUtils.isEmpty(znsRunActivityUserEntities)) {
            znsRunActivityUserEntities = new ArrayList<>();
        }
        Long ownerUserId = 0L;
        if (null != ownerActivityUser) {
            vo.setIsOwner(ownerActivityUser.getUserId().equals(user.getId()) ? 1 : 2);
        } else {
            vo.setIsOwner(2);
        }
        String activityConfig = vo.getActivityConfig();
        Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig);
        // 根据用户币种设置保证金&参赛费
        Currency currency = I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, vo.getId(), user.getId());
        ActivityEntryFeeQuery query = ActivityEntryFeeQuery.builder().activityId(vo.getId()).currencyCode(currency.getCurrencyCode()).build();
        List<ActivityEntryFee> list = activityEntryFeeService.findList(query);
        vo.setEarnestMoney(vo.getActivityEntryFee().toString());
        if (!CollectionUtils.isEmpty(list)) {
            vo.setEarnestMoney(list.get(0).getEntryFee().toString());
            vo.setActivityEntryFee(list.get(0).getEntryFee());
        }
        vo.setTeamName(vo.getActivityTitle());
        vo.setMainType(MainActivityTypeEnum.OLD.getType());

        if (Objects.equals(vo.getActivityTypeSub(), 3)) {
            PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(vo.getId(), 1);
            PkChallengeRecord wasPkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(vo.getId(), 0);
            if (wasPkChallengeRecord != null && pkChallengeRecord != null) {
                PkChallengeRecord myChallengeRecord = pkChallengeRecord;
                if (wasPkChallengeRecord.getUserId().equals(user.getId())) {
                    myChallengeRecord = wasPkChallengeRecord;
                }
                vo.setChallengeUserId(pkChallengeRecord.getUserId());
                vo.setWasChallengeUserId(wasPkChallengeRecord.getUserId());
                vo.setPkBatchNo(pkChallengeRecord.getBatchNo());
                vo.setScore(pkChallengeRecord.getScore());
                vo.setCouponNum(pkChallengeRecord.getCouponNum());
                if (Objects.nonNull(myChallengeRecord.getCurrencyCode())) {
                    // 新活动
                    currency = I18nConstant.buildCurrency(myChallengeRecord.getCurrencyCode());
                }
                vo.setCurrency(currency);
            }
        }

        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(vo.getActivityType())) {
            vo.setCurrency(currency);
            if (vo.getUserType() == 1) {
                vo.setTeamName(I18nMsgUtils.getMessage("activity.wrapper.teamName"));
            } else {
                ZnsUserEntity ownerUser = userEntityMap.get(ownerUserId);
                vo.setTeamName(I18nMsgUtils.getMessage("activity.wrapper.prefixTeamName", (Objects.nonNull(ownerUser) ? ownerUser.getFirstName() : ""))); // (Objects.nonNull(ownerUser) ? ownerUser.getFirstName() : "") + " Called you to Run Together"
                //奖金池
                if (vo.getActivityTotalBonus().compareTo(BigDecimal.ZERO) > 0) {
                    //未答复用户查询
                    ZnsRunActivityUserEntity noReplyUser = znsRunActivityUserEntities.stream().filter(u -> 0 == u.getUserState()).findFirst().orElse(null);
                    if (Objects.isNull(noReplyUser)) {
                        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(vo.getActivityType());
                        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
                        ZnsRunActivityEntity runActivityEntity = runActivityService.selectActivityById(vo.getId());
                        Integer userCount = runActivityUserService.queryActivityUserCount(vo.getId(), true);
                        ActivityEntryFee activityEntryFee = activityAwardCurrencyBizService.getActivityEntryFeeCurrency(vo.getId(), user.getId());
                        BigDecimal totalBonus = activityStrategy.calculateTotalBonus(runActivityEntity, userCount, activityEntryFee.getEntryFee(), null, user, true);
                        totalBonus = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), totalBonus);
                        vo.setPrizePool(totalBonus.toString() + "+");
                    } else {
                        vo.setPrizePool("?");
                    }
                } else {
                    vo.setPrizePool("");
                    //查询组队次数
                    Integer organizeTeamNum = runActivityUserService.getOrganizeTeamNum(ownerUserId, user.getId());
                    vo.setOrganizeTeamNum(organizeTeamNum);
                }
            }
            // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
            Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
            if (null == lastEnterMinutes) {
                lastEnterMinutes = 30;
            }
            long teamMillisecond = lastEnterMinutes.intValue() * 60000;
            vo.setLastEnterTeamRunTime(Long.valueOf(teamMillisecond));
            // 挑战跑开始跑前多少分钟可入场(配置分钟)
            Integer beforeEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
            if (null == beforeEnterMinutes) {
                beforeEnterMinutes = 5;
            }
            long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
            vo.setRunBeforeEnter(challengeMillisecond);
            if (vo.getCompleteRuleType() == 1) {
                vo.setRunningGoals(Arrays.asList(vo.getRunMileage().intValue()));
            } else {
                vo.setRunningGoals(Arrays.asList(vo.getRunTime()));
            }
        } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(vo.getActivityType())) {
            //TODO i18n 修复
            if (vo.getCompleteRuleType() == 1) {
                String mil = vo.getRunMileage().divide(new BigDecimal(1000)).setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + I18nMsgUtils.getMessage("activity.wrapper.km");
                if (user.getMeasureUnit() == 1) {
                    mil = vo.getRunMileage().divide(new BigDecimal(1600)).setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + I18nMsgUtils.getMessage("activity.wrapper.miles");
                }
                vo.setTeamName(I18nMsgUtils.getMessage("activity.wrapper.prefixTitle", mil));
                vo.setRunningGoals(Arrays.asList(vo.getRunMileage().intValue()));
            } else {
                String mins = vo.getRunTime() / 60 + I18nMsgUtils.getMessage("activity.wrapper.mins");

                vo.setTeamName(I18nMsgUtils.getMessage("activity.wrapper.prefixTitle", mins));
                vo.setRunningGoals(Arrays.asList(vo.getRunTime()));
            }
            // 挑战跑开始跑前多少分钟可入场(配置分钟)
            Integer beforeEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.CHALLENGE_ACTIVITY_BEFORE_ENTER));
            if (null == beforeEnterMinutes) {
                beforeEnterMinutes = 5;
            }
            long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;

            ZnsRunActivityEntity runActivityEntity = runActivityService.selectActivityById(vo.getId());
            if (Objects.nonNull(runActivityEntity.getAppointmentStartTime())) {
                challengeMillisecond = 30 * 60000;
                vo.setAppointmentStartTime(runActivityEntity.getAppointmentStartTime());
                vo.setRaceSetType(2);
            }
            vo.setBeforeEnterChallengeRunTime(challengeMillisecond);

            if (Objects.equals(vo.getActivityTypeSub(), 3)) {
                PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(vo.getId(), 1);
                PkChallengeRecord wasPkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(vo.getId(), 0);
                if (pkChallengeRecord != null && wasPkChallengeRecord != null) {
                    vo.setChallengeUserId(pkChallengeRecord.getUserId());
                    vo.setWasChallengeUserId(wasPkChallengeRecord.getUserId());
                    vo.setPkBatchNo(pkChallengeRecord.getBatchNo());
                    vo.setScore(pkChallengeRecord.getScore());
                    vo.setCouponNum(pkChallengeRecord.getCouponNum());
                }
            }
            List<Integer> newPersonPkType = RunActivitySubTypeEnum.getNewPersonPkType();
            if (newPersonPkType.contains(vo.getActivityTypeSub())) {
                Integer score = 0;
                NewPersonPkVo newPkMultipleConfigVo = JsonUtil.readValue(activityConfig, NewPersonPkVo.class);
                if (YesNoStatus.YES.getCode().equals(newPkMultipleConfigVo.getIsFinishAward())) {
                    UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
                    if (Objects.nonNull(finishAward)) {
                        if (Objects.nonNull(finishAward.getScore())) {
                            score = finishAward.getScore();
                        }
                    }
                }
                vo.setScore(score);
            }


        } else if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(vo.getActivityType())) {
            List<Integer> runningGoals = JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class);
            vo.setRunningGoals(runningGoals);
            vo.setRank(runActivityUserService.getCurrentRank(vo.getId(), vo.getCompleteRuleType(), user.getId()));
        } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(vo.getActivityType())) {
            //判断用户所选目标
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(vo.getId(), user.getId());
            if (Objects.nonNull(activityUser)) {
                if (vo.getCompleteRuleType() == 2) {
                    vo.setRunningGoals(Arrays.asList(activityUser.getTargetRunTime()));
                } else {
                    vo.setRunningGoals(Arrays.asList(activityUser.getTargetRunMileage()));
                }
            } else {
                List<Integer> runningGoals = JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class);
                vo.setRunningGoals(runningGoals);

            }
            // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
            Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
            if (null == lastEnterMinutes) {
                lastEnterMinutes = 30;
            }
            long teamMillisecond = lastEnterMinutes.intValue() * 60000;
            vo.setLastEnterTeamRunTime(Long.valueOf(teamMillisecond));
            // 挑战跑开始跑前多少分钟可入场(配置分钟)
            Integer beforeEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
            if (null == beforeEnterMinutes) {
                beforeEnterMinutes = 5;
            }
            long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
            vo.setRunBeforeEnter(challengeMillisecond);
            //房间号处理
            Long roomNumber = NumberUtils.getGoalImNumber(vo.getId(), vo.getRunningGoals().get(0), vo.getCompleteRuleType());
            vo.setRoomNumber(roomNumber);
        } else if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(vo.getActivityType())) {
            List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);

            if (Objects.nonNull(milepostAward)) {
                vo.setRunningGoals(Arrays.asList(milepostAward.get(milepostAward.size() - 1).getMilepost().intValue()));
            }
            if (vo.getCompleteRuleType() == 1) {
                vo.setTeamName(vo.getActivityTitle());
            }
        } else if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(vo.getActivityType())) {
            // 计算目标总里程
            vo.setTargetMileSum(runActivityBizService.calTargetMile(activityConfig));
            // 计算最高可得
            BigDecimal rewardSum = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), calRewardSum(activityConfig));
            vo.setRewardSum(rewardSum);// 日历列表页上展示的金额
        }

        vo.setMaxReward(runActivityBizService.getPreMaxReward(jsonObject, vo.getActivityType(), vo.getUserCount(), vo.getActivityEntryFee(), vo.getRunningGoals(), vo.getId(), user));
        if (Integer.valueOf(1).equals(isHomePage) && RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(vo.getActivityType())) {
            BigDecimal maxReward = I18nConstant.currencyFormat(currency.getCurrencyCode(), runActivityService.getFirstReward(jsonObject));
            vo.setMaxReward(maxReward);
        }

        if (null != ownerActivityUser) {
            ownerUserId = ownerActivityUser.getUserId();
        }
        if (vo.getActivityType() == 1 || vo.getActivityType() == 2) {
            vo.setInviteesNum(runActivityUserService.queryActivityUserCount(vo.getId(), false));
            vo.setAcceptedNum(runActivityUserService.queryActivityUserCount(vo.getId(), true));
            vo.setParticipantList(getParticipantLis(vo.getActivityType(), userEntityMap, znsRunActivityUserEntities, user.getId(), ownerUserId, friendEntityMap, participantListLimit));
        } else {
            vo.setParticipantList(getParticipantLis(vo.getActivityType(), userEntityMap, znsRunActivityUserEntities, user.getId(), ownerUserId, friendEntityMap, participantListLimit));
            vo.setAcceptedNum(runActivityUserService.queryActivityUserCount(vo.getId(), true));
        }
        // 新用户多人PK 增加返回字段
        if (RunActivityTypeEnum.NEW_USER_PK_MANY.getType().equals(vo.getActivityType())) {
            BigDecimal maxReward = BigDecimal.ZERO;
            Integer score = 0;

            NewPkMultipleConfigVo newPkMultipleConfigVo = JsonUtil.readValue(activityConfig, NewPkMultipleConfigVo.class);
            if (newPkMultipleConfigVo.getIsFinishAward().equals(YesNoStatus.YES.getCode())) {
                UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
                if (Objects.nonNull(finishAward)) {
                    if (!CollectionUtils.isEmpty(finishAward.getAmountList())) {
                        CurrencyAmount currencyAmount = finishAward.getAmountList().stream().filter(s -> s.getCurrencyCode().equals(userAccount.getCurrencyCode())).findFirst().orElse(null);
                        if (Objects.nonNull(currencyAmount)) {
                            maxReward = maxReward.add(currencyAmount.getAmount());
                        }
                    } else if (Objects.nonNull(finishAward.getAmount()) && finishAward.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        maxReward = maxReward.add(finishAward.getAmount());
                    }
                    if (Objects.nonNull(finishAward.getScore())) {
                        score = score + finishAward.getScore();
                    }
                }
            }
            if (newPkMultipleConfigVo.getIsUserRankAward().equals(YesNoStatus.YES.getCode())) {
                List<UserGameRankAwardDto> userRankAward = newPkMultipleConfigVo.getUserRankAward();
                if (!CollectionUtils.isEmpty(userRankAward)) {
                    UserGameRankAwardDto userGameRankAwardDto = userRankAward.get(0);
                    UserGameAwardDto awardDto = userGameRankAwardDto.getAwardDto();
                    if (Objects.nonNull(awardDto)) {
                        if (!CollectionUtils.isEmpty(awardDto.getAmountList())) {
                            CurrencyAmount currencyAmount = awardDto.getAmountList().stream().filter(s -> s.getCurrencyCode().equals(userAccount.getCurrencyCode())).findFirst().orElse(null);
                            if (Objects.nonNull(currencyAmount)) {
                                maxReward = maxReward.add(currencyAmount.getAmount());
                            }
                        } else if (Objects.nonNull(awardDto.getAmount())) {
                            maxReward = maxReward.add(awardDto.getAmount());
                        }
                        if (Objects.nonNull(awardDto.getScore())) {
                            score = score + awardDto.getScore();
                        }
                    }
                }
            }
            maxReward = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), maxReward);
            vo.setMaxReward(maxReward);
            vo.setScore(score);
        }
        //当日赛事列表返回奖金池金额，总的
        if (!Integer.valueOf(1).equals(isHomePage) && RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(vo.getActivityType())) {
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .activityId(vo.getId()).isDelete(0)
                    .build();
            userQuery.setOrders(List.of(OrderItem.asc("user_type")));
            List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findList(userQuery);
            BigDecimal prizePool = getPrizePool(activityUsers, vo.getCompleteRuleType(), vo.getActivityEntryFee(), jsonObject);
            prizePool = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), prizePool);
            vo.setMaxReward(prizePool);
        }
        if (RunActivityTypeEnum.oldI18nOfficialActivity().contains(vo.getActivityType())) {
            vo.setCurrency(I18nConstant.buildDefaultCurrency());
            if (YesNoStatus.YES.getCode().equals(vo.getIsNew()) && !I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currency.getCurrencyCode())) {
                vo.setCurrency(currency);
                try {
                    BigDecimal exchangeRate = exchangeRateConfigService.selectByUsd2TargetCurrency(currency.getCurrencyCode()).getExchangeRate();
                    if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(vo.getActivityType())) {
                        BigDecimal rewardSum = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), vo.getRewardSum().multiply(exchangeRate));
                        vo.setRewardSum(rewardSum);
                    } else {
                        BigDecimal maxReward = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), vo.getMaxReward().multiply(exchangeRate));
                        vo.setMaxReward(maxReward);
                    }
                } catch (Exception e) {
                    log.error("setMaxReward error", e);
                }
            }
        }
        if (Objects.isNull(vo.getMaxReward())) {
            vo.setMaxReward(BigDecimal.ZERO);
        }
        BigDecimal maxReward = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), vo.getMaxReward().setScale(2, RoundingMode.HALF_DOWN));
        vo.setMaxReward(maxReward);

        if (Objects.isNull(vo.getActivityType()) || vo.getActivityType() != 1) {
            BigDecimal prizePool = I18nConstant.currencyFormat(userAccount.getCurrencyCode(), vo.getMaxReward());
            vo.setPrizePool(prizePool.toString());
        }


        vo.setActivityTitle(vo.getTeamName());


        if (RunActivityTypeEnum.officialTypes().contains(vo.getActivityType()) && !CollectionUtils.isEmpty(vo.getRunningGoals())) {
            if (vo.getCompleteRuleType() == 2) {
                vo.setRunTime(vo.getRunningGoals().get(0));
            } else {
                vo.setRunMileage(new BigDecimal(vo.getRunningGoals().get(0)));
            }
        }
        vo.setActivityStateNew(getActivityStateNew(vo.getActivityState(), vo.getStatus()));
        //设置状态
        Integer activityState = vo.getActivityState();
        if (!RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(vo.getActivityType())) {
            if (activityState == 0 || activityState == 1) {
                if (vo.getUserState() == 0) {
                    vo.setActivityState(0);
                } else if (vo.getUserState() == 3) {
                    if (vo.getActivityType() == 3) {
                        vo.setActivityState(3);
                    } else if (vo.getActivityType() == 5) {
                        vo.setActivityState(3);
                    } else {
                        vo.setActivityState(2);
                    }
                } else if (vo.getUserState() == 1) {
                    vo.setActivityState(1);
                } else if (vo.getUserState() == 4) {
                    vo.setActivityState(6);
                    if (vo.getActivityType().equals(RunActivityTypeEnum.TASK_ACTIVITY.getType())) {
                        vo.setActivityState(2);
                    }
                } else if (vo.getUserState() == 2) {
                    vo.setActivityState(5);
                }
            } else if (activityState == 2) {
                vo.setActivityState(4);
            } else {
                vo.setActivityState(activityState);
            }
        } else {
            if (activityState == 0) {
                vo.setActivityState(1);
            } else if (activityState == 1) {
                vo.setActivityState(2);
            } else if (activityState == 2) {
                vo.setActivityState(4);
            }
        }
        if (vo.getUserState() == 5) {
            vo.setActivityState(-1);
        }
        fillRunActivityTeamVO(vo, user.getId());
        return vo;
    }

    /**
     * 获取用户昵称
     *
     * @param activityType
     * @param userEntityMap
     * @param znsRunActivityUserEntities
     * @param userId
     * @param ownerUserId
     * @param friendEntityMap
     * @param participantListLimit
     * @return
     */
    private List<RunnersSimpleVo> getParticipantLis(Integer activityType, Map<Long, ZnsUserEntity> userEntityMap, List<ZnsRunActivityUserEntity> znsRunActivityUserEntities, Long userId, Long ownerUserId, Map<Long, ZnsUserFriendEntity> friendEntityMap, Integer participantListLimit) {
        List<Long> userIds = null;
        if (activityType == 2) {
            userIds = znsRunActivityUserEntities.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        } else {
            userIds = znsRunActivityUserEntities.stream().filter(u -> Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(), ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState()).contains(u.getUserState())).map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        if (Objects.nonNull(ownerUserId)) {
            userIds.remove(ownerUserId);
            userIds.add(0, ownerUserId);
        }
        Map<Long, ZnsRunActivityUserEntity> activityUserEntityMap = znsRunActivityUserEntities.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, Function.identity(), (x, y) -> x));
        List<RunnersSimpleVo> list = new ArrayList<>();
        for (Long u : userIds) {
            ZnsUserEntity znsUserEntity = userEntityMap.get(u);
            if (Objects.isNull(znsUserEntity)) {
                continue;
            }
            RunnersSimpleVo vo = new RunnersSimpleVo();
            String nickName = znsUserEntity.getFirstName();
            if (znsUserEntity.getId().equals(userId)) {
                nickName = nickName + "(me)";
            }
            vo.setUserId(znsUserEntity.getId());
            vo.setNickname(nickName);
            vo.setHeadPortrait(znsUserEntity.getHeadPortrait());
            vo.setIsFriend(0);
            //用户状态
            ZnsRunActivityUserEntity runActivityUser = activityUserEntityMap.get(znsUserEntity.getId());
            if (Objects.nonNull(runActivityUser)) {
                vo.setUserState(runActivityUser.getUserState());
            } else {
                vo.setUserState(0);
            }
            if (Objects.nonNull(friendEntityMap)) {
                ZnsUserFriendEntity friendEntity = friendEntityMap.get(u);
                if (Objects.nonNull(friendEntity) && friendEntity.getRelationType() == 1) {
                    vo.setIsFriend(1);
                }
            }

            list.add(vo);
        }

        if (Objects.nonNull(participantListLimit) && list.size() > participantListLimit) {
            list = list.subList(0, participantListLimit);
        }
        return list;
    }


    /**
     * 获取排行赛奖金池
     *
     * @param activityUsers
     * @param completeRuleType
     * @param activityEntryFee
     * @param jsonObject
     * @return
     */
    private BigDecimal getPrizePool(List<ZnsRunActivityUserEntity> activityUsers, Integer completeRuleType, BigDecimal activityEntryFee, Map<String, Object> jsonObject) {
        if (CollectionUtils.isEmpty(activityUsers)) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalBonus = BigDecimal.ZERO;
        //最高奖金池=保证金金额*报名人数+sum（各个里程的完赛奖励*报名人数+各个里程的3个名次奖励）
        totalBonus = totalBonus.add(new BigDecimal(activityUsers.size()).multiply(activityEntryFee));
        //用户目标分组
        Map<Integer, List<ZnsRunActivityUserEntity>> goalUserMap = new HashMap<>();
        if (completeRuleType == 1) {
            goalUserMap = activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTargetRunMileage));
        } else {
            goalUserMap = activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTargetRunTime));
        }
        if (Objects.isNull(jsonObject)) {
            return totalBonus;
        }
        try {
            List<Map> runningGoalsAward = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);

            for (Map map : runningGoalsAward) {
                Double award = MapUtils.getDouble(map, "award", 0D);
                Integer goal = MapUtils.getInteger(map, "goal");
                List<ZnsRunActivityUserEntity> runActivityUserVOS = goalUserMap.get(goal);
                if (!CollectionUtils.isEmpty(runActivityUserVOS) && award != null) {
                    totalBonus = totalBonus.add(new BigDecimal(award).multiply(new BigDecimal(runActivityUserVOS.size())));
                }
                totalBonus = totalBonus.add(new BigDecimal(MapUtils.getDouble(map, ApiConstants.FIRST_AWARD, 0D))).add(new BigDecimal(MapUtils.getDouble(map, ApiConstants.SECOND_AWARD, 0D))).add(new BigDecimal(MapUtils.getDouble(map, ApiConstants.THIRD_AWARD, 0D)));
            }
            return totalBonus;
        } catch (Exception e) {
            log.error("getPrizePool error,e:{}", e);
        }
        return totalBonus;
    }


    /**
     * 活动状态
     *
     * @param activityState
     * @param status
     * @return
     */
    private Integer getActivityStateNew(Integer activityState, Integer status) {
        //  活动状态：0表示未接受，1表示未开始，2表示跑步中，可以继续跑，-1：取消，3：再次挑战 4：已结束 5：已拒绝
        //  `activity_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '活动状态：0表示未开始，1表示进行中，2表示已结束，-1表示活动已取消',
        if (activityState == 0) {
            return 1;
        } else if (activityState == 1) {
            return 2;
        } else if (activityState == 2) {
            return 4;
        } else if (activityState == -1) {
            return -1;
        }
        return 0;
    }


    /**
     * 填充活动信息
     *
     * @param vo
     * @return
     */
    private void fillRunActivityTeamVO(RunActivityTeamVO vo, Long userId) {
        ZnsRunActivityEntity activity = runActivityService.selectActivityById(vo.getId());
        if (Objects.equals(activity.getActivityType(), 2) && Objects.equals(activity.getActivityTypeSub(), 2)) {
            String activityConfig = activity.getActivityConfig();
            Map<String, String> map = JsonUtil.readValue(activityConfig);
            String str = map.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
            if (StringUtils.hasText(str)) {
                FriendPkAward newFriendPkAward = PkFriendAwardUtil.getNewFriendPkAward(activity);
                BigDecimal activityEntryFee = vo.getActivityEntryFee();
                if (Objects.isNull(activityEntryFee)) {
                    activityEntryFee = BigDecimal.ZERO;
                }
                activityEntryFee = activityEntryFee.multiply(new BigDecimal(2)).add(newFriendPkAward.getParticipateAmount()).add(newFriendPkAward.getWinnerAmount());
                if (Objects.equals(activity.getActivityState(), 2)) {
                    ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(vo.getId(), userId);
                    vo.setPrizePool(Objects.nonNull(activityUser) ? activityUser.getRunAward().toString() : new BigDecimal(0).toString());
                } else {
                    vo.setPrizePool(activityEntryFee.toString());
                }
            }
        }

    }

    /**
     * 计算活动奖励
     *
     * @param activityConfig
     * @return
     */
    private BigDecimal calRewardSum(String activityConfig) {
        if (!StringUtils.hasText(activityConfig)) {
            return null;
        }
        ActivityConfigResp activityConfigResp = JsonUtil.readValue(activityConfig, ActivityConfigResp.class);
        if (activityConfigResp == null) {
            return null;
        }
        List<TaskResp> tasks = activityConfigResp.getTasks();
        if (CollectionUtils.isEmpty(tasks)) {
            return null;
        }
        List<BigDecimal> winRewardList = tasks.stream().map(taskResp -> {
            BigDecimal winReward = taskResp.getWinReward();
            if (winReward == null) {
                return BigDecimal.ZERO;
            }
            return winReward;
        }).collect(Collectors.toList());
        BigDecimal rewardSum = BigDecimal.ZERO;
        for (BigDecimal targetMile : winRewardList) {
            rewardSum = rewardSum.add(targetMile);
        }
        if (Objects.nonNull(activityConfigResp.getFinishAward())) {
            rewardSum = activityConfigResp.getFinishAward();
        }
        return rewardSum;
    }

    /**
     * 填充团队活动信息
     *
     * @param user
     * @param activityList
     */
    private void fillupTeamActivityRecords(ZnsUserEntity user, List<RunActivityTeamVO> activityList) {
        Long userId = user.getId();
        for (RunActivityTeamVO runActivityTeamVO : activityList) {
            if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(runActivityTeamVO.getActivityType())) {
                Long activityId = runActivityTeamVO.getId();
                //查找活动
                LambdaQueryWrapper<ZnsRunActivityEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ZnsRunActivityEntity::getId, activityId)
                        .eq(ZnsRunActivityEntity::getIsDelete, 0);
                ZnsRunActivityEntity entity = runActivityService.findOne(
                        RunActivityQuery.builder().id(activityId).isDelete(0).build());
                if (entity.getActivityState() == 0) {
                    runActivityTeamVO.setActivityState(1);
                } else if (entity.getActivityState() == 1) {
                    runActivityTeamVO.setActivityState(2);

                } else if (entity.getActivityState() == 4) {
                    runActivityTeamVO.setActivityState(4);

                }
                if (runActivityTeamVO.getActivityState() == -1 || runActivityTeamVO.getUserState() == 5) {
                    runActivityTeamVO.setActivityState(-1);

                }


                ActivityTeam team = activityUserBizService.getUserCurrentTeam(activityId, userId);
                if (team != null) {
                    runActivityTeamVO.setRank(team.getRank());
                    runActivityTeamVO.setTeamMaxNum(team.getMaxNum());
                    runActivityTeamVO.setTeamCurrentNum(team.getCurrentNum());
                }
            }
        }
    }

    /**
     * My Race 日历
     *
     * @param query
     * @return
     */
    private List<RunActivityTeamVO> myRaceCalendarActivityList(MyRaceCalendarActivityQuery query) {
        String currentTime = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), query.getTimeZone(), DateUtil.DATE_TIME_SHORT);
        query.setCurrentTime(currentTime);
        List<MyRaceCalendarActivityListVo> list = mainActivityService.findMyRaceCalendarActivity(query);
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            log.info("myRaceCalendarActivityList end,查询为空");
            return null;
        }
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(query.getUserId());
        List<ZnsUserAccountEntity> userAccounts = userAccountService.getUserAccounts(query.getUserId());
        List<Long> activityIds = list.stream().map(MyRaceCalendarActivityListVo::getId).collect(Collectors.toList());
        List<MyRaceCalendarSubActivityListVo> subActivityListVos = subActivityService.findListByActivityIds(activityIds);
        Map<Long, MyRaceCalendarSubActivityListVo> subActivityListVoMap = subActivityListVos.stream().collect(Collectors.toMap(MyRaceCalendarSubActivityListVo::getMainActivityId, Function.identity(), (x, y) -> x));
        Map<Long, String> languageTitleMap = activityDisseminateService.findTitleMapByActivityIdAndLanguage(activityIds, query.getLanguage());
        //查询默认语言
        Map<Long, String> defaultTitleMap = activityDisseminateService.findTitleMapByActivityIdAndDefault(activityIds);
        //玩法
        List<Integer> playIds = list.stream().map(MyRaceCalendarActivityListVo::getPlayId).collect(Collectors.toList());
        Map<Long, EntryGameplay> gameplayMap = entryGameplayService.findGameplayMap(playIds);
        List<RunActivityTeamVO> dtoList = new ArrayList<>();
        for (MyRaceCalendarActivityListVo a : list) {
            Currency currency = userAccountService.getCurrency(userAccount, query.getUserId(), a.getId(), userAccounts, true);
            //审核情况考虑
            if (query.isAuditing()) {
                //查询费用
                ActivityFee feeEntry = activityFeeService.findFeeEntry(a.getId(), currency.getCurrencyCode());
                if (Objects.nonNull(feeEntry) && !"free".equals(feeEntry.getType()) && !"score".equals(feeEntry.getType())) {
                    continue;
                }
            }
            MyRaceCalendarSubActivityListVo myRaceCalendarSubActivityListVo = subActivityListVoMap.get(a.getId());
            String title = activityDisseminateService.getActivityTitle(a.getId(), languageTitleMap, defaultTitleMap);
            RunActivityTeamVO dto = new RunActivityTeamVO();
            dto.setId(a.getId());
            Long start = DateUtil.getStampByZone(a.getStartTime(), a.getTimeStyle() == 0 ? "UTC" : query.getZoneId());
            Long end = DateUtil.getStampByZone(a.getEndTime(), a.getTimeStyle() == 0 ? "UTC" : query.getZoneId());
            dto.setStartTime(new Date(start));
            dto.setEndTime(new Date(end));
            dto.setMainType(a.getMainType());
            dto.setActivityType(a.getOldType());

            dto.setActivityState(a.getActivityState());
            dto.setGmtCreate(a.getGmtCreate());
            dto.setAlreadyRunMileage(a.getAlreadyRunMileage());
            dto.setAlreadyRunTime(a.getAlreadyRunTime());
            dto.setUserState(a.getUserState());
            dto.setRunTime(a.getRunTime());
            dto.setRunMileage(a.getRunMileage());
            dto.setRunDataDetailsId(a.getRunDataDetailsId());

            if (Objects.nonNull(myRaceCalendarSubActivityListVo)) {
                dto.setRouteId(myRaceCalendarSubActivityListVo.getRouteId());
                dto.setRouteType(myRaceCalendarSubActivityListVo.getRouteType());
                dto.setCompleteRuleType(myRaceCalendarSubActivityListVo.getCompleteRuleType());
                if (Objects.isNull(dto.getRunMileage()) && Objects.isNull(dto.getRunTime())) {
                    if (Objects.equals(dto.getCompleteRuleType(), 1)) {
                        dto.setRunMileage(new BigDecimal(myRaceCalendarSubActivityListVo.getTarget()));
                    }
                    if (Objects.equals(dto.getCompleteRuleType(), 2)) {
                        dto.setRunTime(myRaceCalendarSubActivityListVo.getTarget());
                    }
                }
            }
            if (StringUtils.hasText(title)) {
                dto.setTeamName(title);
            }
            //奖励添加
            if (a.getActivityState() == 2 || a.getActivityState() == 3) {
                dto.setActivityIsFinish(YesNoStatus.YES.getCode());
                setAward(dto, query.getUserId());
                //奖励发放状态
                boolean activityAwardSendStatus = mainActivityService.getActivityAwardSendStatus(dto.getId());
                if (activityAwardSendStatus) {
                    //发放完成 显示 setAward
                    dto.setAwardSendFinish(1);
                } else {
                    //发放未完成 显示max;
                    dto.setAwardSendFinish(0);
                    setMaxAward(dto, currency.getCurrencyCode(), dto.getCompleteRuleType(), dto.getRunMileage(), dto.getRunTime(), query.getUserId());
                }
            } else {
                dto.setActivityIsFinish(YesNoStatus.NO.getCode());
                setMaxAward(dto, currency.getCurrencyCode(), dto.getCompleteRuleType(), dto.getRunMileage(), dto.getRunTime(), query.getUserId());
            }
            //房间号处理
            if (!MainActivityTypeEnum.OLD.getType().equals(dto.getMainType())) {
                //目标
                List<Integer> goals = subActivityService.getAllSingleActByMain(dto.getId()).stream().map(SubActivity::getTarget)
                        .sorted().toList();
                Integer roomNumber = NumberUtils.getGoalImNumber(dto.getId(), goals, dto.getCompleteRuleType(), a.getTimeStyle(), a.getWaitTime());
                dto.setRoomNumber(Long.valueOf(roomNumber));
            }

            EntryGameplay entryGameplay = gameplayMap.get(a.getPlayId());
            if (Objects.nonNull(entryGameplay) && entryGameplay.getCompetitionFormat() == 1) {
                ActivityTeam team = activityUserBizService.getUserCurrentTeam(dto.getId(), query.getUserId());
                if (team != null) {
                    dto.setRank(team.getRank());
                    dto.setTeamMaxNum(team.getMaxNum());
                    dto.setTeamCurrentNum(team.getCurrentNum());
                }
            }

            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 是否国际化活动
     *
     * @param activityType
     * @param activityId
     * @return
     */
    private Boolean isI18nActivity(Integer activityType, Long activityId) {
        if (RunActivityTypeEnum.newGameplayActivity().contains(activityType)) {
            return true;
        }
        if (RunActivityTypeEnum.oldI18nActivity().contains(activityType)) {
            //查询是否有国际化配置
            return runActivityService.checkNewActivity(activityId);
        }
        ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
        if (RunActivityTypeEnum.oldI18nOfficialActivity().contains(activityType) && Objects.nonNull(runActivity)) {
            return YesNoStatus.YES.getCode().equals(runActivity.getIsNew());
        }
        return false;
    }

    private Integer compareActivityState(RunActivityTeamVO x, RunActivityTeamVO y) {
        //进行中->未开始->已结束->下架->取消
        //按1、进行中->2、未开始->3、已结束->4、下架->5、取消重新获取状态值，方便排序
        Integer xState = getCompareActivityState(x);
        Integer yState = getCompareActivityState(y);
        if (xState == yState) {
            //据开始时间由小到大排序
            if (y.getStartTime().compareTo(x.getStartTime()) < 0) {
                return 1;
            } else if (y.getStartTime().compareTo(x.getStartTime()) == 0) {
                // 相同时间开始的比赛，按创建时间最新时间排序
                if (y.getGmtCreate().compareTo(x.getGmtCreate()) > 0) {
                    return 1;
                }
            }
        } else {
            return xState - yState;
        }
        return -1;
    }

    /**
     * 按1、进行中->2、未开始->3、已结束->4、下架->5、取消重新获取状态值，方便排序
     *
     * @param vo
     * @return
     */
    private Integer getCompareActivityState(RunActivityTeamVO vo) {
        if (vo.getStatus() == -1) {
            return 4;
        } else {
            Integer activityState = vo.getActivityState();
            if (Objects.nonNull(vo.getActivityStateNew())) {
                activityState = vo.getActivityStateNew();
            }
            if (Arrays.asList(2, 3, 6).contains(activityState)) {
                return 1;
            } else if (activityState == 1 || activityState == 0) {
                return 2;
            } else if (activityState == 4) {
                return 3;
            } else if (activityState == -1) {
                return 5;
            } else {
                return 6;
            }
        }
    }

    private Integer getCompareActivityState2(MyRecordsActivityListVo vo) {
        //原活动状态：0表示未开始，1表示进行中，2表示已结束，-1表示活动已取消
        if (vo.getActivityState() == 3) {
            return 4;
        } else if (vo.getActivityState() == 1) {
            return 1;
        } else if (vo.getActivityState() == 0) {
            return 2;
        } else if (vo.getActivityState() == 2) {
            return 3;
        } else if (vo.getActivityState() == -1) {
            return 5;
        } else {
            return 6;
        }
    }

    /**
     * 设置RunActivityTeamVO最大奖励
     *
     * @param vo
     * @param currencyCode
     * @param completeRuleType
     * @param runMileage
     * @param userId
     */
    private void setMaxAward(RunActivityTeamVO vo, String currencyCode, Integer completeRuleType, BigDecimal runMileage, Integer runTime, Long userId) {
        Integer target = null;
        if (Objects.nonNull(completeRuleType) && completeRuleType == 1) {
            target = runMileage.intValue();
        } else if (Objects.nonNull(completeRuleType) && completeRuleType == 2) {
            target = runTime;
        }
        //获取累计的最大奖励
        MaxAwardVo maxAward = awardActivityBizService.findMaxAward(vo.getId(), currencyCode, target, vo.getMainType(), userId);
        BigDecimal maxReward = I18nConstant.currencyFormat(currencyCode, maxAward.getMaxReward());
        vo.setMaxReward(maxReward);
        vo.setPrizePool(maxReward.toString());
        vo.setScore(maxAward.getMaxScore());
        if (maxAward.getDistributeReward() != null) {
            BigDecimal currencyBigDecimal = I18nConstant.currencyFormat(currencyCode, maxAward.getDistributeReward());
            if (Objects.nonNull(currencyBigDecimal)) {
                vo.setDistributePrizePool(currencyBigDecimal.toString());
            }
        }
        vo.setDistributeScore(maxAward.getDistributeScore());
    }


    /**
     * 设置RunActivityTeamVO中奖励
     *
     * @param vo
     * @param userId
     */
    private void setAward(RunActivityTeamVO vo, Long userId) {
        List<Long> activityIds = new ArrayList<>();
        activityIds.add(vo.getId());
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(vo.getMainType())) {
            List<Long> subActivityId = seriesActivityRelService.findSubActivityId(vo.getId());
            if (!org.springframework.util.CollectionUtils.isEmpty(subActivityId)) {
                activityIds.addAll(subActivityId);
            }
        }
        //查询活动以获得奖励
        Integer sumScore = activityUserScoreService.sumScore(activityIds, userId, ScoreConstant.SourceTypeEnum.mainActivitySourceList(), null);
        vo.setScore(sumScore);
        //获取已获得奖励
        BigDecimal sumAward = accountDetailService.sumAward(activityIds, userId, Arrays.asList(AccountDetailTypeEnum.NEW_ACTIVITY_100.getType()), 2);
        if (Objects.nonNull(sumAward)) {
            String currencyCode = userAccountService.getUserCurrency(userId).getCurrencyCode();
            sumAward = I18nConstant.currencyFormat(currencyCode, sumAward);
            vo.setPrizePool(sumAward.toString());
        } else {
            vo.setPrizePool("0");
        }
    }


    /**
     * 我参加的活动赛事纪录
     *
     * @param pageNum
     * @param pageSize
     * @param activityType
     * @param user
     * @param appType
     * @param timeZone
     * @param appVersion
     * @return
     */
    public List<MyRecordsActivityListVo> getMyRecords(Integer pageNum, Integer pageSize, Integer activityType, ZnsUserEntity user, Integer appType, TimeZone timeZone, Integer appVersion) {
        if (Objects.isNull(activityType)) {
            activityType = 1;
        }

        List<MyRecordsActivityListVo> list = new ArrayList<>();
        //新活动查询
        MyRecordActivityQuery query = new MyRecordActivityQuery(timeZone, user.getId());
        query.setActivityType(activityType);
        query.setLanguage(user.getLanguageCode());
        query.setZoneId(user.getZoneId());

        List<MyRecordsActivityListVo> dataList = myRecordActList(new Page<>(pageNum, pageSize), query);
        if (!org.springframework.util.CollectionUtils.isEmpty(dataList)) {
            List<MyRecordsActivityListVo> newActivityList = dataList.stream().map(a -> {
                MyRecordsActivityListVo vo = new MyRecordsActivityListVo();
                BeanUtils.copyProperties(a, vo);
                if (a.getActivityState() == 3) {
                    vo.setStatus(-1);
                }
                //路由获取
                RotationArea route = rotationAreaBizService.getNewActivityRoute(vo.getId(), vo.getMainType(), null);
                vo.setRouteArea(route);
                return vo;
            }).collect(Collectors.toList());
            list.addAll(newActivityList);
        }
        return list;
    }

    /**
     * 我的参赛记录/My Race 列表
     *
     * @param page
     * @param query
     * @return
     */
    private List<MyRecordsActivityListVo> myRecordActList(Page page, MyRecordActivityQuery query) {
        String currentTime = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), query.getTimeZone(), DateUtil.DATE_TIME_SHORT);
        query.setCurrentTime(currentTime);

        List<MyRecordActivityListVo> list = mainActivityService.myRecordActList(page, query);
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            log.info("myRecordActList end,查询为空");
            return null;
        }

        List<Long> activityIds = list.stream().map(MyRecordActivityListVo::getId).collect(Collectors.toList());
        Map<Long, String> languageTitleMap = activityDisseminateService.findTitleMapByActivityIdAndLanguage(activityIds, query.getLanguage());
        //查询默认语言
        Map<Long, String> defaultTitleMap = activityDisseminateService.findTitleMapByActivityIdAndDefault(activityIds);

        List<MyRecordsActivityListVo> dtoList = list.stream().map(a -> {
            MyRecordsActivityListVo vo = new MyRecordsActivityListVo();
            BeanUtils.copyProperties(a, vo);
            Long start = DateUtil.getStampByZone(a.getStartTime(), a.getTimeStyle() == 0 ? "UTC" : query.getZoneId());
            if (StringUtils.hasText(a.getEndTime())) {
                Long end = DateUtil.getStampByZone(a.getEndTime(), a.getTimeStyle() == 0 ? "UTC" : query.getZoneId());
                vo.setEndTime(new Date(end));
            }
            vo.setStartTime(new Date(start));
            vo.setActivityType(a.getOldType());
            if (!StringUtils.hasText(a.getTeamName())) {
                String title = activityDisseminateService.getActivityTitle(a.getId(), languageTitleMap, defaultTitleMap);
                if (StringUtils.hasText(title)) {
                    vo.setTeamName(title);
                }
            }

            return vo;
        }).collect(Collectors.toList());
        return dtoList;
    }


    /**
     * 我参加的活动赛事纪录-活动类型
     *
     * @param languageCode
     * @return
     */
    public List<RecordActivityTypeVo> myRecordsActivityTypes(String languageCode) {
        String key = ConfigKeyEnums.RECORD_NEW_ACTIVITY_TYPE.getCode() + languageCode;
        String config = sysConfigService.selectConfigByKey(key);
        if (!StringUtils.hasText(config)) {
            config = sysConfigService.selectConfigByKey(ConfigKeyEnums.RECORD_NEW_ACTIVITY_TYPE.getCode() + I18nConstant.LanguageCodeEnum.en_US.getCode());
        }
        if (!StringUtils.hasText(config)) {
            return new ArrayList<>();
        }
        List<RecordActivityTypeVo> recordActivityTypeVos = JsonUtil.readList(config, RecordActivityTypeVo.class);
        return recordActivityTypeVos;
    }

    /**
     * 查询活动当前信息
     */
    public RunActivityTeamVO queryActivityInfo(Long activityId, ZnsUserEntity user) {
        if (null == activityId) {
            return null;
        }
        RunActivityTeamVO activityTeamVO = runActivityService.getActivityTeamVOById(activityId, user.getId());
        if (null == activityTeamVO) {
            return null;
        }
        // 查询所有的获得用户
        List<ZnsRunActivityUserEntity> activityUserEntities = runActivityUserService.findAllActivityUser(activityId);
        if (CollectionUtils.isEmpty(activityUserEntities)) {
            activityUserEntities = new LinkedList<>();
        }

        wrapperActivityTeamVo(activityTeamVO, activityUserEntities, user, null, null);

        return activityTeamVO;
    }

    /**
     * 获取个人赛事列表
     *
     * @param loginUser
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Page getIndividualTeamRunActivity(ZnsUserEntity loginUser, Integer pageNum, Integer pageSize) {
        boolean isTest = sysConfigService.isTestUser(loginUser.getEmailAddressEn());
        Page page = runActivityService.getIndividualTeamRunActivity(new Page<>(pageNum, pageSize), loginUser.getId(), isTest);
        List<ZnsRunActivityEntity> list = page.getRecords();
        if (list.isEmpty()) {
            return page;
        }
        //查询本人参加情况
        List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityOwnerUsers(activityIds);
        Map<Long, ZnsRunActivityUserEntity> ownerUserMap = activityUsers.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (x, y) -> x));

        List<OfficialActivityListVO> listVOS = new ArrayList<>();

        for (ZnsRunActivityEntity activityEntity : list) {
            OfficialActivityListVO vo = new OfficialActivityListVO();
            BeanUtils.copyProperties(activityEntity, vo);
            vo.setClassifyName(ActivityClassifyTypeEnum.getByType(vo.getClassifyType()).getEnName());
            if (vo.getCompleteRuleType() == 1) {
                vo.setRunningGoals(Arrays.asList(activityEntity.getRunMileage().intValue()));
            } else {
                vo.setRunningGoals(Arrays.asList(activityEntity.getRunTime()));
            }

            Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());

            vo.setAdvertisingImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
            vo.setPrizePool(activityEntity.getActivityTotalBonus().toString());
            vo.setMaxReward(new BigDecimal(vo.getPrizePool()));

            ZnsRunActivityUserEntity ownerUser = ownerUserMap.get(vo.getId());
            if (Objects.nonNull(ownerUser) && !ownerUser.getUserId().equals(loginUser.getId())) {
                vo.setActivityTitle("Multi-Player Run initiated by " + ownerUser.getNickname());
            } else {
                vo.setActivityTitle("Multi-Player Run initiated by you");
            }
            listVOS.add(vo);
        }

        page.setRecords(listVOS);

        return page;
    }

    /**
     * 获取首页官方活动
     *
     * @param loginUser
     * @param isHomePage
     * @param classifyType
     * @param activityType
     * @return
     */
    public List<OfficialActivityListVO> getHomePageOfficialActivity(ZnsUserEntity loginUser, int isHomePage, Integer classifyType, Integer activityType) {
        boolean testUser = sysConfigService.isTestUser(loginUser.getEmailAddressEn());
        //查询首页展示的官方活动
        RunActivityQuery.RunActivityQueryBuilder builder = RunActivityQuery.builder();
        if (isHomePage == 1) {
            builder.isHomepage(1);
        }
        if (!testUser) {
            builder.isTest(0);
        }
        RunActivityQuery query = builder.isDelete(0).status(1).classifyType(classifyType).activityType(activityType)
                .activityTypeIn(RunActivityTypeEnum.officialTypes())
                .activityStateIn(Arrays.asList(ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.IN_PROGRESS.getState())).build();

        if (isHomePage != 1) {
            query.setOrders(List.of(
                    OrderItem.asc("classify_type"),
                    OrderItem.asc("activity_start_time"),
                    OrderItem.desc("modifie_time")
            ));
        }
        List<ZnsRunActivityEntity> list = runActivityService.findList(query);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        //查询本人参加情况
        Map<Long, ZnsRunActivityUserEntity> userEntityMap = new HashMap<>();
        if (isHomePage == 1) {
            List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
            List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, loginUser.getId());
            userEntityMap = activityUsers.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (x, y) -> x));
        }
        List<OfficialActivityListVO> listVOS = getOfficialActivityList(list, isHomePage, userEntityMap);
        return listVOS;
    }

    /**
     * 获取官方活动列表
     *
     * @param list
     * @param isHomePage
     * @param userEntityMap
     * @return
     */
    private List<OfficialActivityListVO> getOfficialActivityList(List<ZnsRunActivityEntity> list, int isHomePage, Map<Long, ZnsRunActivityUserEntity> userEntityMap) {
        List<OfficialActivityListVO> listVOS = new ArrayList<>();
        for (ZnsRunActivityEntity activityEntity : list) {
            OfficialActivityListVO vo = new OfficialActivityListVO();
            vo.setId(activityEntity.getId());
            vo.setActivityStartTime(activityEntity.getActivityStartTime());
            vo.setActivityEndTime(activityEntity.getActivityEndTime());
            vo.setApplicationStartTime(activityEntity.getApplicationStartTime());
            vo.setApplicationEndTime(activityEntity.getApplicationEndTime());
            vo.setActivityTitle(activityEntity.getActivityTitle());
            vo.setActivityEntryFee(activityEntity.getActivityEntryFee());
            vo.setUserCount(activityEntity.getUserCount());
            vo.setCompleteRuleType(activityEntity.getCompleteRuleType());
            vo.setClassifyType(activityEntity.getClassifyType());
            vo.setClassifyName(ActivityClassifyTypeEnum.getByType(vo.getClassifyType()).getEnName());
            vo.setActivityType(activityEntity.getActivityType());

            Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());

            try {
                if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityEntity.getActivityType())) {
                    vo.setRunningGoals(JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class));
                } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                    vo.setRunningGoals(JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class));
                } else if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(activityEntity.getActivityType())) {
                    List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
                    if (Objects.nonNull(milepostAward)) {
                        vo.setRunningGoals(Arrays.asList(milepostAward.get(milepostAward.size() - 1).getMilepost().intValue()));
                    }
                }
                vo.setAdvertisingImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
                if (activityEntity.getActivityType() == 3) {
                    vo.setMaxReward(runActivityService.getFirstReward(jsonObject));
                } else {
                    vo.setMaxReward(runActivityBizService.getPreMaxReward(jsonObject, activityEntity.getActivityType(), vo.getUserCount(), BigDecimal.ZERO, null, activityEntity.getId(), null));
                }
                vo.setPrizePool(vo.getMaxReward().toString());
            } catch (Exception e) {
                e.printStackTrace();
            }

            //判断是否已报名
            if (isHomePage == 0) {
                vo.setIsSignUp(1);
            } else {
                if (Objects.nonNull(userEntityMap.get(activityEntity.getId()))) {
                    vo.setIsSignUp(1);
                } else {
                    vo.setIsSignUp(0);
                }
            }

            listVOS.add(vo);
        }
        return listVOS;
    }

    /**
     * 获取tab悬浮窗信息
     *
     * @param userId
     * @param zoneId
     * @return
     */
    public RaceTabVo getRaceTab(Long userId, String zoneId) {
        RaceTabVo raceTabVo = new RaceTabVo();
        //今日数据获取
        ZonedDateTime now = ZonedDateTime.now();

        // 查询用户参与的所有赛事
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).userId(userId).userStateIn(Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(), ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.NO_REPLY.getState()))
                .build();

        List<ZnsRunActivityUserEntity> znsRunActivityUserList = runActivityUserService.findList(userQuery);

        if (CollectionUtils.isEmpty(znsRunActivityUserList)) {
            return null;
        }

        List<Long> activityIdList = znsRunActivityUserList.stream().map(ZnsRunActivityUserEntity::getActivityId).collect(Collectors.toList());
        List<ZnsRunActivityEntity> todayActivityList = runActivityService.findList(RunActivityQuery.builder()
                .isDelete(0).status(1).activityStateIn(Arrays.asList(ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.IN_PROGRESS.getState()))
                .idIn(activityIdList)
                .build());
        todayActivityList = todayActivityList.stream().filter(k -> {
            // 倒计时本身考虑PK赛、非官方多人同跑、官方多人同跑
            if (!(RunActivityTypeEnum.TEAM_RUN.getType().equals(k.getActivityType())
                    || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(k.getActivityType())
                    || RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(k.getActivityType()))) {
                return false;
            }

            //官方非官方检查是否能入场 过滤超过最晚允许入场时间的比赛
            if (RunActivityTypeEnum.TEAM_RUN.getType().equals(k.getActivityType())
                    || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(k.getActivityType())) {

                //检查配置
                Map<String, Object> jsonObject = JsonUtil.readValue(k.getActivityConfig());
                Integer runBeforeEnter = MapUtil.getInteger(jsonObject.get(ApiConstants.ACTIVITY_BEFORE_ENTER)); // 可候场时间
                Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER)); // 最晚可进入时间
                if (null != runBeforeEnter) {
                    ZonedDateTime start = k.getActivityStartTime().minusMinutes(runBeforeEnter);
                    if (now.isBefore(start)) {
                        log.info("活动：{} ,可候场时间：{},当前时间{}，还未到时间", k.getId(), start, now);
                        // 不可候场
                        return false;
                    }
                }
                if (null == lastEnterMinutes) {
                    lastEnterMinutes = 30;
                }
                ZonedDateTime end = k.getActivityStartTime().plusMinutes(lastEnterMinutes);
                if (now.compareTo(end) > 0) {
                    log.info("活动：{}超过最晚可入场时间,系统时间为：{},活动最晚允许入场时间为：{}", k.getId(), now, end);
                    return false;
                }
            }
            return true;
        }).sorted(Comparator.comparing(ZnsRunActivityEntity::getActivityStartTime)).collect(Collectors.toList());

        ZonedDateTime endDate = null;
        if (CollectionUtils.isEmpty(todayActivityList)) {
            return null;
        }
        ZnsRunActivityEntity result = todayActivityList.get(0);
        raceTabVo.setId(result.getId());
        raceTabVo.setActivityTitle(result.getActivityTitle());
        raceTabVo.setActivityState(result.getActivityState());
        // 报名记录
        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserList.stream()
                .filter(k -> result.getId().equals(k.getActivityId())).findFirst().orElse(null);

        if (Objects.isNull(znsRunActivityUserEntity)) {
            return null;
        }
        raceTabVo.setRunMileage(znsRunActivityUserEntity.getTargetRunMileage()); // 目标距离
        raceTabVo.setRunTime(znsRunActivityUserEntity.getTargetRunTime()); // 目标时间

        if (ActivityStateEnum.IN_PROGRESS.getState().equals(result.getActivityState())) {
            // 除了pk赛
            if (!RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(result.getActivityType())) {
                //检查配置
                Map<String, Object> jsonObject = JsonUtil.readValue(result.getActivityConfig());
                Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
                if (null == lastEnterMinutes) {
                    lastEnterMinutes = 30;
                }
                // 进行中取最晚允许入场时间
                endDate = result.getActivityStartTime().plusMinutes(lastEnterMinutes);
            }
        } else {
            // 未开始取开始时间
            endDate = result.getActivityStartTime();
        }
        if (Objects.nonNull(endDate)) {
            raceTabVo.setTimestamp(endDate.toInstant().toEpochMilli());
        }
        return raceTabVo;
    }

}
