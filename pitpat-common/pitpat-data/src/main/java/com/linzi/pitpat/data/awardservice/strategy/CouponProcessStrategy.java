package com.linzi.pitpat.data.awardservice.strategy;

import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardDoProcessResultDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendProcessDto;
import com.linzi.pitpat.data.activityservice.model.query.award.SendUserCouponResultDto;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponSendDetailVo;
import com.linzi.pitpat.data.awardservice.service.AwardConfigCouponService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component("couponProcessStrategy")
@Slf4j
public class CouponProcessStrategy extends AbstractAwardProcessStrategy implements InitializingBean {
    @Autowired
    private CouponService couponService;
    @Autowired
    private AwardConfigCouponService awardConfigCouponService;
    @Autowired
    private UserCouponBizService userCouponBizService;
    @Autowired
    private ZnsUserService userService;

    @Override
    public void doProcess(AwardSendDto dto, List<Long> list, String batchNo) {
        list.forEach(i -> {
            try {
                AwardConfigCoupon awardConfigCoupon = awardConfigCouponService.findByAwardConfigId(i);
                Coupon coupon = couponService.selectCouponById(Long.valueOf(awardConfigCoupon.getCouponIds()));
                Result<SendUserCouponResultDto> result = userCouponBizService.sendUserCouponSource(coupon.getId(), dto.getUserId(), dto.getActivityId(), CouponConstant.SourceTypeEnum.source_type_100.getType() + dto.getType(), false, false);
                putAwardSendFlagIntoCache(dto, i);

                Long userCouponId = result.getData().getUserCouponId();
                //添加记录
                activityUserAwardBizService.saveNew(dto, userCouponId, AwardTypeEnum.COUPON.getType(), batchNo, dto.getTotalBatchNo());
            } catch (Exception e) {
                log.error("sendCoupon doProcess error award_config_id:{}", i, e);
            }
        });
    }

    @Override
    protected AwardDoProcessResultDto doProcess(AwardSendProcessDto awardSendProcessDto) {
        List<Long> refIdList = new ArrayList<>();
        AwardDoProcessResultDto awardDoProcessResultDto = new AwardDoProcessResultDto();
        awardDoProcessResultDto.setCouponAmount(BigDecimal.ZERO);
        awardDoProcessResultDto.setDiscount(BigDecimal.ZERO);
        List<UserCouponSendDetailVo> userCouponSendDetailVos = JsonUtil.readList(awardSendProcessDto.getAwardValue(), UserCouponSendDetailVo.class);
        for (UserCouponSendDetailVo userCouponSendDetailVo : userCouponSendDetailVos) {
            int num = NumberUtils.geZero(userCouponSendDetailVo.getNum()) ? userCouponSendDetailVo.getNum() : 1;
            for (int i = 0; i < num; i++) {
                Coupon coupon = couponService.selectCouponById(userCouponSendDetailVo.getCouponId());
                if (coupon == null) {
                    continue;
                }
                if (!CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type.equals(coupon.getCouponMainType())
                        && Objects.isNull(awardSendProcessDto.getSourceType())) {
                    continue;
                }
                Result<SendUserCouponResultDto> result = userCouponBizService.sendUserCouponSource(coupon.getId(), awardSendProcessDto.getUserId(), -1L, CouponConstant.SourceTypeEnum.source_type_5.getType(), false, false);
                SendUserCouponResultDto data = result.getData();
                if (data != null) {
                    refIdList.add(userCouponSendDetailVo.getCouponId());
                    awardDoProcessResultDto.setCouponAmount(awardDoProcessResultDto.getCouponAmount().add(data.getAmount()));
                    if (Objects.isNull(awardDoProcessResultDto.getCouponExpireTime())) {
                        awardDoProcessResultDto.setCouponExpireTime(DateUtil.toZonedDateTime(data.getGmtEnd()));
                    } else if (awardDoProcessResultDto.getCouponExpireTime().isAfter(DateUtil.toZonedDateTime(data.getGmtEnd()))) {
                        awardDoProcessResultDto.setCouponExpireTime(DateUtil.toZonedDateTime(data.getGmtEnd()));
                    }
                    awardDoProcessResultDto.setDiscount(BigDecimalUtil.smallValueGeZero(awardDoProcessResultDto.getDiscount(), data.getDiscount()));
                }
            }
        }
        awardDoProcessResultDto.setRefIdList(refIdList);
        return awardDoProcessResultDto;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        AwardProcessStrategyFactory.register(2, this);
    }

    @Override
    public void deleteAwardConfig(List<Long> longList) {
        longList.forEach(i -> {
            AwardConfigCoupon awardConfigCoupon = awardConfigCouponService.findByAwardConfigId(i);
            awardConfigCouponService.deleteAwardConfigCouponById(awardConfigCoupon.getId());
        });
    }
}
