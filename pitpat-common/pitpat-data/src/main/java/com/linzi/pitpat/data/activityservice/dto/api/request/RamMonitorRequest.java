package com.linzi.pitpat.data.activityservice.dto.api.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RamMonitorRequest {
    // 跑步id
    @NotNull
    private Long runDetailId;
    // 用户id
    private Long userId;
    // 移动设备型号 拿不到传 -
    private String deviceModel;
    // 设备CPU型号 拿不到传 -
    private String deviceCpu;
    // RAM总大小 拿不到传 0
    private BigDecimal deviceRam;

    // 蓝牙版本
    private String bluetoothVersion;

    private List<RamLogItem> ramLogItems;

    //正常退出。0：异常退出，1：正常退出。默认是0，用户正常退出，再最后一条给0
    private Integer normalExit;

    @Data
    public static class RamLogItem {
        // 采集时候的时间戳
        private Long time;
        // RAM使用率(%)
        private BigDecimal ramUsagePercentage;
        // RAM使用量
        private BigDecimal ramUsage;
        // pitpat Ram 使用率
        private BigDecimal pitpatRamPercentage;

        // CPU使用率（%）
        private BigDecimal cpuUsagePercentage;


    }
}
