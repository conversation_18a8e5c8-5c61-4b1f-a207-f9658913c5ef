package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreIdDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.query.ActivityUserScoreQuery;
import com.linzi.pitpat.data.awardservice.model.query.AddColorEggAward;
import com.linzi.pitpat.data.awardservice.model.query.UserScorePageQuery;
import com.linzi.pitpat.lang.Result;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

public interface ActivityUserScoreService extends IService<ActivityUserScore> {
    boolean insertOrUpdateActivityUserScore(ActivityUserScore activityUserScore);

    int update(ActivityUserScore activityUserScore);

    Long increaseAmount(int score, Long activityId, Long userId, Integer rank, Integer extraScore, Integer source);

    void beChallengeIncreaseAmount(int score, Long activityId, Long userId);

    void useActivityUserScore(ExchangeScoreRule exchangeScoreRule, ScoreIdDto dto);

    void useActivityUserScore(int score, Long activityId, Long userId, Integer rank);

    Result sendScore(Long userId, Integer type, Integer compareCount, Long activityId, Long courseId);

    List<ActivityUserScore> selectActivityUserScoreByActivityId(Long activityId);

    ActivityUserScore selectUserScoreByActivityIdUserIdMilepost(Long userId, Long activityId, int milepost);

    List<ActivityUserScore> selectActivityUserScoreByActivityIdUserId(Long activityId, Long userId);

    Integer sumScore(Long activityId, Long userId, List<Integer> source, Integer status);

    Integer sumScoreWithTime(Long activityId, Long userId, Integer status, ZonedDateTime createTime);

    Integer countByUserIdTradeTypeSubType(Long userId, ZonedDateTime startOfDate, ZonedDateTime date, List<Integer> list);

    BigDecimal selectScoreByUserIdSourceType(Long userId, ZonedDateTime startOfDate, ZonedDateTime date, List<Integer> list);

    void handleRunScore(AddColorEggAward request);

    Integer sumScore(List<Long> activityIds, Long userId, List<Integer> source, Integer status);

    //用户修改国家，洗积分兑换余额数据
    void changeScoreExchangeAmount(Long userId, BigDecimal exchangeRate);

    Integer selectByExchangeScoreRuleIdSource(Long exchangeScoreRuleId, List<Integer> source);

    ActivityUserScore selectActivityUserScoreByActivityIdUserIdRank(Long activityId, Long userId, Integer rank);

    /**
     * 合并积分账户
     * @param newUserId
     * @param oldUserIds
     */
    void mergeUserScore(Long newUserId, List<Long> oldUserIds);

    ActivityUserScore selectExchangePersonCountByScoreConfigIdUserId(Long scoreConfigId, Long userId);

    int selectActivityUserScoreByUserIdMonthExpire(Long userId, ZonedDateTime startTime, ZonedDateTime endTime, List<Integer> status);

    List<ActivityUserScore> selectActivityUserScoreByScoreConfigIdUserId(ZonedDateTime startTime, ZonedDateTime endTime, Long scoreConfigId, Long userId, Integer status);

    void updateScoreStatusExpireTime(ZonedDateTime gmtModified, ZonedDateTime expireTime, ZonedDateTime awardTime, Integer status, Long id);

    List selectActivityUserScoreByUserIdMonthApp(IPage page, Long userId, String queryMonth, Integer income, List<Integer> source, Integer status);

    ActivityUserScore selectFirstActivityUserScore(Long userId, Integer income, List<Integer> source, Integer status);

    int selectActivityUserScoreByUserIdMonth(Long userId, ZonedDateTime firstOfMonth, ZonedDateTime endOfDate, Integer income, Integer status);

    /**
     * 获得用户总积分
     * @param userId
     * @return
     */
    Integer getAllUserScore(Long userId);

    /**
     * 检查用户积分是否大于活动报名所需积分
     *
     * @param fee    活动积分
     * @param userId 用户
     * @return true 用户积分足够， false 用户积分不足
     */
    boolean checkUserActivityEntryScore(ActivityFee fee, Long userId);

    ActivityUserScore selectActivityUserScoreByActivityUserId(Long activityId, Long userId, Integer source);

    ActivityUserScore getRefundActivityUserScore(MainActivity activityEntity, Long userId, Integer score, boolean refund);

    Page<ActivityUserScore> findPage(UserScorePageQuery pageQuery);


    ActivityUserScore findSumScore(ActivityUserScoreQuery query);

    void deleteByQuery(ActivityUserScoreQuery query);

    void syncUseScore(ActivityUserScoreQuery query);

    List<ActivityUserScore> findList(ActivityUserScoreQuery query);
}
