package com.linzi.pitpat.data.userservice.service.impl.shopify.strategy;


import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.third.shopify.ShopifyUtil;
import com.linzi.pitpat.data.third.shopify.req.ShopifyCustomerVO;
import com.linzi.pitpat.data.third.shopify.resp.ShopifyCustomerResp;
import com.linzi.pitpat.data.third.shopify.resp.ShopifyOrderResp;
import com.linzi.pitpat.data.third.shopify.resp.ShopifyResp;
import com.linzi.pitpat.data.userservice.enums.ShopifyConstant;
import com.linzi.pitpat.data.userservice.model.entity.shopify.ShopifyRefundOrderEntity;
import com.linzi.pitpat.data.userservice.model.entity.shopify.ShopifySyncRecordEntity;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyOrderService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyRefundOrderService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifySyncRecordService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyUserService;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * shopify同步抽象类
 */
@Slf4j
public abstract class AbstractSycnStrategy implements InitializingBean {

    @Resource
    private ShopifySyncRecordService shopifySyncRecordService;
    @Resource
    private ShopifyUserService shopifyUserService;
    @Resource
    private ShopifyOrderService shopifyOrderService;
    @Resource
    private ShopifyRefundOrderService shopifyRefundOrderService;


    /**
     * shopify同步处理类map
     * key：品牌
     * val 处理类
     */
    public static Map<String, AbstractSycnStrategy> SYCN_MAP = new HashMap<>();

    /**
     * 同步shopify数据到pitpat
     *
     * @param typeEnum
     */
    @Transactional
    public void sycnShopifyToPitpat(ShopifyConstant.SyncTypeEnum typeEnum) {
        log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify数据到pitpat,处理开始，品牌：" + typeEnum);
        if (isTest()) {
            //shopify没有测试环境，所以不处理
            log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify数据到pitpat,测试环境不处理DeerRun");
            return;
        }

        ShopifyConstant.BrandEnum brandEnum = getBrandEnum();
        //获取品牌同步记录最大id
        ShopifySyncRecordEntity recordEntity = shopifySyncRecordService.selectLastRecord(brandEnum.getCode(), typeEnum.getCode());
        Long sinceId = 1L;
        if (recordEntity != null) {
            sinceId = recordEntity.getShopfiyBusinessId();
        }

        //同步状态
        Integer syncState = ShopifyConstant.SyncStateEnum.STATE_1.getCode();
        //同步结果
        String syncResult = "";
        ShopifyResp resp = null;
        try {
            //获取同步数据
            resp = getShopifyData(sinceId, typeEnum);
            if (resp == null) {
                return;
            }
            //保存同步数据
            sinceId = saveShopifyData(resp);
            if (sinceId == null) {
                log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify数据到pitpat,没有可以同步的数据，品牌：" + typeEnum);
                return;
            }
        } catch (Exception e) {
            log.error("AbstractSycnStrategy#sycnShopifyToPitpat--同步shopify数据到pitpat异常：", e);
            syncState = ShopifyConstant.SyncStateEnum.STATE_0.getCode();
            syncResult = e.getMessage().length() > 1000 ? e.getMessage().substring(0, 1000) : e.getMessage();
        }

        //保存同步记录
        ShopifySyncRecordEntity syncRecordEntity = new ShopifySyncRecordEntity(sinceId, brandEnum.getCode(), typeEnum.getCode(), syncState, syncResult);
        shopifySyncRecordService.insert(syncRecordEntity);
        log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify数据到pitpat,处理结束，品牌：" + typeEnum);
    }

    /**
     * 同步shopify退款到pitpat
     */
    @Transactional(rollbackFor = Exception.class)
    public void sycnShopifyRefundOrder() {
        ShopifyConstant.BrandEnum brandEnum = getBrandEnum();
        log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify退款到pitpat,处理开始，品牌：" + brandEnum);
        if (isTest()) {
            //shopify没有测试环境，所以不处理
            log.info("AbstractSycnStrategy#sycnShopifyRefundOrder------同步shopify退款到pitpat,测试环境不处理DeerRun");
            return;
        }

        //获取品牌退款订单最大退款时间
        ShopifyRefundOrderEntity refundOrderEntity = shopifyRefundOrderService.selectLastUpdatedRecord(brandEnum.getCode());

        //0时区
        ZonedDateTime updatedAt = DateTimeUtil.parse("2023-09-20 00:00:00");
        if (refundOrderEntity != null) {
            updatedAt = refundOrderEntity.getUpdatedAt();
        }
        //时间减一天（转西7区在-24小时）
        updatedAt = DateUtil.addHours(updatedAt, -31);

        HashMap<String, Object> param = new HashMap<>();
        param.put("limit", 50);
        param.put("order", "updated_at_min ASC");
        param.put("updated_at_min", DateUtil.formateDateStr(updatedAt, DateUtil.YYYY_MM_DD_HH_MM_SS_T));
        param.put("financial_status", "refunded");
        param.put("status", "any");
        List<ShopifyOrderResp> orderRespList = ShopifyUtil.getOrderList(brandEnum, param);
        if (CollectionUtils.isEmpty(orderRespList)) {
            return;
        }
        //保存退款订单
        List<ShopifyRefundOrderEntity> saveList = new ArrayList<>(orderRespList.size());
        List<Long> shopifyOrderIds = new ArrayList<>(orderRespList.size());
        for (ShopifyOrderResp shopifyOrderResp : orderRespList) {
            ShopifyRefundOrderEntity entity = new ShopifyRefundOrderEntity();
            entity.setBrand(brandEnum.getCode());
            entity.setUpdatedAt(shopifyOrderResp.getUpdated_at());
            entity.setClosedAt(shopifyOrderResp.getClosed_at());
            entity.setOrderNumber(shopifyOrderResp.getOrder_number() + "");
            entity.setShopfiyOrderId(shopifyOrderResp.getId());
            entity.setShopfiyUserId(shopifyOrderResp.getCustomer().getId());
            saveList.add(entity);
            shopifyOrderIds.add(shopifyOrderResp.getId());
        }

        if (!CollectionUtils.isEmpty(saveList)) {
            //批量保存退款订单
            shopifyRefundOrderService.saveBatch(saveList);
            //批量更新shopfiy订单状态
            shopifyOrderService.updateStatusByShopfiyOrderIds(ShopifyConstant.OrderStatusEnum.REFUNDED.getCode(), shopifyOrderIds);
        }
        log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify退款到pitpat,处理结束，品牌：" + brandEnum);
    }

    /**
     * 注销用户-更新用户标签
     *
     * @param shopfiyUserId
     */
    public void deleteUser(Long shopfiyUserId) {
        ShopifyUtil.updateCustomer(getBrandEnum(), new ShopifyCustomerVO(shopfiyUserId));
    }

    /**
     * 获取订单数据
     *
     * @param sinceId
     * @return
     */
    private ShopifyResp getOrderData(Long sinceId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("limit", 50);
        param.put("order", "id ASC");
        param.put("since_id", sinceId);
        param.put("status", "any");
        param.put("financial_status", "paid,refunded");
        List<ShopifyOrderResp> orderResps = ShopifyUtil.getOrderList(getBrandEnum(), param);
        ShopifyResp shopifyResp = new ShopifyResp();
        shopifyResp.setOrderRespList(orderResps);
        return shopifyResp;
    }

    /**
     * 获取用户数据
     *
     * @param sinceId
     * @return
     */
    private ShopifyResp getUserData(Long sinceId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("limit", 50);
        param.put("order", "id ASC");
        param.put("status", "any");
        param.put("since_id", sinceId);
        List<ShopifyCustomerResp> customerRespList = ShopifyUtil.getCustomerList(getBrandEnum(), param);
        ShopifyResp shopifyResp = new ShopifyResp();
        shopifyResp.setCustomerRespList(customerRespList);
        return shopifyResp;
    }

    /**
     * 获取用户数据
     *
     * @param email
     * @return
     */
    public ShopifyCustomerResp getShopifyUser(String email) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("limit", 1);
        param.put("email", email);
        param.put("status", "any");
        List<ShopifyCustomerResp> customerRespList = ShopifyUtil.getCustomerList(getBrandEnum(), param);
        if (CollectionUtils.isEmpty(customerRespList)) {
            return null;
        }
        return customerRespList.get(0);
    }

    /**
     * 获取同步数据
     *
     * @param sinceId
     * @param typeEnum
     * @return
     */
    private ShopifyResp getShopifyData(Long sinceId, ShopifyConstant.SyncTypeEnum typeEnum) {
        if (ShopifyConstant.SyncTypeEnum.TYPE_1 == typeEnum) {
            //获取用户数据
            return getUserData(sinceId);
        } else if (ShopifyConstant.SyncTypeEnum.TYPE_2 == typeEnum) {
            //获取订单数据
            return getOrderData(sinceId);
        }
        return null;
    }

    /**
     * 保存同步数据，生成同步记录
     *
     * @param resp
     * @return shopify业务最大id
     */
    private Long saveShopifyData(ShopifyResp resp) {
        if (!CollectionUtils.isEmpty(resp.getOrderRespList())) {
            List<ShopifyOrderResp> list = resp.getOrderRespList();
            shopifyOrderService.saveShopifyOrder(list, getBrandEnum());
            return list.get(list.size() - 1).getId();
        } else if (!CollectionUtils.isEmpty(resp.getCustomerRespList())) {
            List<ShopifyCustomerResp> list = resp.getCustomerRespList();
            shopifyUserService.saveShopifyUser(list, getBrandEnum());
            return list.get(list.size() - 1).getId();
        }
        return null;
    }

    protected boolean isTest() {
        String profile = SpringContextUtils.getActiveProfile();
        if (!EnvUtils.isOnline(profile)) {
            //shopify没有测试环境
            log.info("AbstractSycnStrategy#sycnShopifyToPitpat------同步shopify数据到pitpat,测试环境不处理");
            return true;
        }
        return false;
    }

    /**
     * 获取品牌类型
     *
     * @return
     */
    protected abstract ShopifyConstant.BrandEnum getBrandEnum();
}
