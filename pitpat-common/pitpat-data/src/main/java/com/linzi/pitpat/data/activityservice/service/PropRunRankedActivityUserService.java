package com.linzi.pitpat.data.activityservice.service;
/**
 * <p>
 * 段位赛跑步活动用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-05
 */

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankDataDto;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankedLevelStatisticsVo;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityPageQuery;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityUserQuery;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;

import java.time.ZonedDateTime;
import java.util.List;

public interface PropRunRankedActivityUserService {


    PropRunRankedActivityUser findById(Long id);


    int insert(PropRunRankedActivityUser runRankedActivityUser);


    int update(PropRunRankedActivityUser runRankedActivityUser);

    void updateBatchById(List<PropRunRankedActivityUser> list);


    int deleteById(Long id);


    List<PropRunRankedActivityUser> findListByActivityId(Long activityId);

    /**
     * 获取用户最近10场完赛段位赛数据
     *
     * @param userId
     * @return
     */
    List<PropRunRankedActivityUser> findListByUserId(Long userId);

    /**
     * 获取用户段位赛的统计数据
     *
     * @param loginUser
     * @return
     */
    PropUserRankedLevelStatisticsVo statisticsUserRankedData(ZnsUserEntity loginUser);

    /**
     * 获取用户最近一场完成的段位赛信息
     *
     * @param userId
     * @return
     */
    PropRunRankedActivityUser findLastByUserId(Long userId);

    /**
     * 根据用户活动ID查找段位赛用户数据
     *
     * @param runDataDetailsId
     * @return
     */
    PropRunRankedActivityUser findByRunDataDetailsId(Long runDataDetailsId);

    /**
     * 根据活动和用户ID 获取段位赛用户数据
     *
     * @param userId
     * @return
     */
    PropRunRankedActivityUser findByActivityIdAndUserId(Long activityId, Long userId);

    PropRunRankedActivityUser findByQuery(PropRankedActivityPageQuery query);

    List<PropRunRankedActivityUser> findListByUserId(Long userId, Integer isPlacement);

    Integer getCurrentRankSegment(Long userId);

    Integer getNextRankSegment(Long userId);

    Integer getCurrentPlacementRankLevel(Long userId);


    List<ZonedDateTime> getUserRankFirstDay(Long userId);

    Page<PropUserRankDataDto> findPage(Page<PropUserRankDataDto> page, PropRankedActivityPageQuery queryDto);

    List<PropRunRankedActivityUser> findListByActivityIdAndNoCheat(Long activityId);

    Page<PropRunRankedActivityUser> findPageByQuery(PropRankedActivityPageQuery pageQuery);

    Integer findCurrentRank(Long activityId, Long userId, String orderStr);

    List<PropRunRankedActivityUser> findList(PropRankedActivityUserQuery query);

}
