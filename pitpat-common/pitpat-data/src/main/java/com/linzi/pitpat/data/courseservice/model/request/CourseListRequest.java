package com.linzi.pitpat.data.courseservice.model.request;

import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/29 19:59
 */
@Data
@NoArgsConstructor
public class CourseListRequest extends PageQuery {

    private Integer status;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 课程id
     */
    private Long userId;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 课程类型，默认0，0：变速跑/阻力训练 ，1：视频
     */
    private Integer courseType;
    /**
     * 是否推荐，1：热门课程，2：更多课程，0：不推荐，默认：0
     */
    private Integer recommended;
    /**
     * 难度
     */
    private Integer difficulty;
    /**
     * 最后修改时间
     */
    private ZonedDateTime modifyStartTime;
    /**
     * 最后修改时间
     */
    private ZonedDateTime modifyEndTime;
    /**
     * 排序字段，sort
     */
    private String orderField;
    /**
     * 排序类型，asc，desc
     */
    private String orderType;

    /**
     * 是否详情页 1 是 0 /null 否
     */
    private Integer isDetailPage;
    /**
     * 是否是vip课程：0：不是；1：是
     */
    private Integer isPlusCourse;

    /**
     * 【4.4.3新增】优惠券列表状态，1：可用、2：不可用、3：已用
     */
    private Integer couponListStatus;

    /**
     * 【4.4.3新增】创建时间 >=
     */
    private ZonedDateTime gmtCreateGt;

    /**
     * 【4.4.3新增】排序方式
     */
    private String sortStr;

    /**
     * 【4.4.3新增】优惠券主类型  1：赛事券，2：商城券
     *
     * @see CouponConstant.CouponMainTypeEnum
     */
    private Integer couponMainType;

    /**
     * 设备类型
     *
     * @see EquipmentDeviceTypeEnum
     * @since 4.6.0
     */
    private Integer deviceType;

    /**
     * 商城国家code
     */
    private String mallCountryCode;

    /**
     * 用户券国家code
     */
    private String userCouponCountryCode;

}
