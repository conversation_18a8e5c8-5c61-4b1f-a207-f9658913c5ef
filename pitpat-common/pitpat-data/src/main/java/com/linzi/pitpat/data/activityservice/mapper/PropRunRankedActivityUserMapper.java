package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankDataDto;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface PropRunRankedActivityUserMapper extends BaseMapper<PropRunRankedActivityUser> {

    List<PropRunRankedActivityUser> selectListByActivityId(@Param("activityId") Long activityId);

    Page<PropUserRankDataDto> findPage(Page<PropUserRankDataDto> page, @Param("query") PropRankedActivityPageQuery query);


    /**
     * 统计用户排名汇总书记
     *
     * @param userId
     */
    List<PropRunRankedActivityUser> statisticsUserRankedData(Long userId);

    List<ZonedDateTime> getUserRankFirstDay(Long userId);

    List<PropRunRankedActivityUser> findListByActivityIdAndNoCheat(Long activityId);

    Integer findCurrentRank(@Param("activityId") Long activityId, @Param("userId") Long userId, @Param("orderStr") String orderStr);
}
