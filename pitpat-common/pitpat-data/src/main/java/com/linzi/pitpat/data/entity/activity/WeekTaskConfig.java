package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 任务配置表
 *
 * <AUTHOR>
 * @since 2023-02-28
 */

@Data
@NoArgsConstructor
@TableName("zns_week_task_config")
public class WeekTaskConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.activity.WeekTaskConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                     // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                        // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";                      // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                  // 最后修改时间
    public final static String task_name = CLASS_NAME + "task_name";                        // 任务名称
    public final static String week_challenge_num = CLASS_NAME + "week_challenge_num";      // 周挑战领取人数
    public final static String week_award_num = CLASS_NAME + "week_award_num";              // 周奖励领取人数
    public final static String activity_start_time = CLASS_NAME + "activity_start_time";    // 活动开始时间
    public final static String activity_end_time = CLASS_NAME + "activity_end_time";        // 活动结束时间
    public final static String year_month_n = CLASS_NAME + "year_month_n";                  // 年月
    public final static String week_n = CLASS_NAME + "week_n";                              // 第几周
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //任务名称
    private String taskName;
    //周挑战领取人数
    private Integer weekChallengeNum;
    //周奖励领取人数
    private Integer weekAwardNum;
    //活动开始时间
    private ZonedDateTime activityStartTime;
    //活动结束时间
    private ZonedDateTime activityEndTime;
    //年月
    private String yearMonthN;
    //第几周
    private Integer weekN;

    @Override
    public String toString() {
        return "WeekTaskConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",taskName=" + taskName +
                ",weekChallengeNum=" + weekChallengeNum +
                ",weekAwardNum=" + weekAwardNum +
                ",activityStartTime=" + activityStartTime +
                ",activityEndTime=" + activityEndTime +
                ",yearMonthN=" + yearMonthN +
                ",weekN=" + weekN +
                "}";
    }
}
