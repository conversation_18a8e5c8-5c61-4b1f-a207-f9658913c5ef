package com.linzi.pitpat.data.activityservice.dto.console.request;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;


@Data
@NoArgsConstructor
public class ActivityTaskRequestDto extends PageQuery {
    private Long id;
    //任务名称
    private String taskName;
    //模板名称
    private String templateName;
    //状态
    private Integer status;
    //活动id
    private Long activityId;
    //1 按日重复， 2 按星期重复， 3 按月重复， 4 按年重复
    private Integer repeatDateType = 1;

    //配置开始时间
    private ZonedDateTime gmtStartTime;
    //配置结束时间
    private ZonedDateTime gmtEndTime;
    //1 启用，0禁用
    private Integer useful;
    // 是否删除
    private Integer isDelete;
    // 1是模板，0不是模板
    private Integer isTemplate;

    //时区，东区为正数 ，如东8区 为8 ， 西区为负数 ， 如西2区， 为-2
    private Integer timeZone;

    private String hh; //执行任务的时间

    //类型， 0 ，只生成一场， 1  生成多场， 2 指定时间点(或八区) ，3  根据任务时间
    private Integer type;
    //配置
    private String config;

    //任务图片
    private String taskPic;
    //任务排序
    private Integer taskSort;

    private String activityTitle;


    private ZonedDateTime taskStartTime;                    // 任务启动时间

    private ZonedDateTime taskEndTime;                      // 任务结束时间

    /**
     * 活动标签
     *
     * @see com.linzi.pitpat.data.enums.TaskLabelEnums
     */
    private Integer label;

    /**
     * 是否展示倒计时,1-展示,0-不展示
     */
    private Integer isShowCountDown;
    /**
     * 是否显示跑步目标 ，1-展示，0-不展示
     */
    private Integer isShowRunTarget;
    /**
     * 是否显示获取跑马灯 1-展示，0-不展示
     */
    private Integer isShowMarquee;
    /**
     * 是否显示配速限制 1-展示，0-不展示
     */
    private Integer isShowSpeedLimit;
    //参赛限制 -1 无限制 > 1 具体限制次数
    private Integer entryLimit;
}
