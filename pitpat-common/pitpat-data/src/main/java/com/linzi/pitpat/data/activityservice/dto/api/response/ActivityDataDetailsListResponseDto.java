package com.linzi.pitpat.data.activityservice.dto.api.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2024/12/27 14:33
 */
@Data
public class ActivityDataDetailsListResponseDto {
    /**
     * id
     */
    private Long id;
    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;
    /**
     * 运动里程
     */
    private BigDecimal runMileage;
    /**
     * 运动时间(s)
     */
    private Integer runTime;
    /**
     * 平均配速(秒/公里)
     */
    private Integer averagePace;
    /**
     * 运动数据状态:0未完成 dnf，1表示完成(展示成绩) 2标识退赛（特殊用） 3:作弊 4：进行中 5：表示完成(不代表比赛完成，只是展示完成标识)
     */
    private Integer resultDataState;
    /**
     * 没有返回表示未申诉， 0：申诉中 1：维持原判 2：改为正常
     */
    private Integer appealState;
    /**
     * 是否强制显示公制， 1：是
     */
    private Integer isForceMetric;
    /**
     * 风控审核状态 1:审核中，0：无
     *
     * @version 4.4.8
     */
    private Integer isRiskReview;
}
