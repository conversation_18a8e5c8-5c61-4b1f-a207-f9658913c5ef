package com.linzi.pitpat.data.activityservice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免费活动模式枚举
 * 
 * @since 2025年1月
 */
@Getter
@AllArgsConstructor
public enum FreeActivityModeEnum {
    
    /**
     * 道具模式
     */
    PROP("PROP", "道具模式"),
    
    /**
     * 竞速模式
     */
    COMPETE("compete", "竞速模式");
    
    /**
     * 模式码
     */
    private final String code;
    
    /**
     * 模式描述
     */
    private final String desc;
    
    /**
     * 根据模式码获取枚举
     * 
     * @param code 模式码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FreeActivityModeEnum getByCode(String code) {
        for (FreeActivityModeEnum mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        return null;
    }
} 