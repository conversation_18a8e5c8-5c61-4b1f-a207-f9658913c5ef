package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 任务配置表列表
 *
 * <AUTHOR>
 * @since 2023-03-01
 */

@Data
@NoArgsConstructor
@TableName("zns_week_task_config_list")
public class WeekTaskConfigList implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.activity.WeekTaskConfigList:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                     // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                        // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";                      // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                  // 最后修改时间
    public final static String year_month_n = CLASS_NAME + "year_month_n";                  // 年月
    public final static String week_n = CLASS_NAME + "week_n";                              // 第几周
    public final static String title_ = CLASS_NAME + "title";                               // 标题
    public final static String run_distance = CLASS_NAME + "run_distance";                  // 跑步里程，米
    public final static String coupon_id = CLASS_NAME + "coupon_id";                        // 优惠券id
    public final static String type_ = CLASS_NAME + "type";                                 // 1. 按里程，2 按天数 ，3 按天数和里程
    public final static String day_n = CLASS_NAME + "day_n";                                // 第几天
    public final static String week_task_config_id = CLASS_NAME + "week_task_config_id";    // 配置id
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //年月
    private String yearMonthN;
    //第几周
    private Integer weekN;
    //标题
    private String title;
    //跑步里程，米
    private BigDecimal runDistance;
    //优惠券id
    private Long couponId;
    //1. 按里程，2 按天数 ，3 按天数和里程
    private Integer type;
    //第几天
    private Integer dayN;
    //配置id
    private Long weekTaskConfigId;

    @Override
    public String toString() {
        return "WeekTaskConfigList{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",yearMonthN=" + yearMonthN +
                ",weekN=" + weekN +
                ",title=" + title +
                ",runDistance=" + runDistance +
                ",couponId=" + couponId +
                ",type=" + type +
                ",dayN=" + dayN +
                ",weekTaskConfigId=" + weekTaskConfigId +
                "}";
    }
}
