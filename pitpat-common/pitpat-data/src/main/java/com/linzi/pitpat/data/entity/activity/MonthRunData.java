package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-03-14
 */

@Data
@NoArgsConstructor
@TableName("zns_month_run_data")
public class MonthRunData implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.activity.MonthRunData:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                                             // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";                                                //
    public final static String gmt_create = CLASS_NAME + "gmt_create";                                              //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                                          //
    public final static String activity_type_count = CLASS_NAME + "activity_type_count";                            // 跑步类型数
    public final static String run_distance = CLASS_NAME + "run_distance";                                          // 跑步里程
    public final static String run_time = CLASS_NAME + "run_time";                                                  // 跑步时长
    public final static String run_count = CLASS_NAME + "run_count";                                                // 跑步次数
    public final static String month_ = CLASS_NAME + "month";                                                       // 月
    public final static String user_id = CLASS_NAME + "user_id";                                                    // 用户id
    public final static String max_velocity = CLASS_NAME + "max_velocity";                                          // 最大速度
    public final static String max_velocity_distance = CLASS_NAME + "max_velocity_distance";                        // 平均速度最快对应的里程
    public final static String guanfang_mul_compile_count = CLASS_NAME + "guanfang_mul_compile_count";              // 官方多人同跑完赛事最多的用户
    public final static String guanfang_mul_run_distance = CLASS_NAME + "guanfang_mul_run_distance";                // 官方多人同跑中总里程
    public final static String guanfang_mul_run_time = CLASS_NAME + "guanfang_mul_run_time";                        // 官方多人同跑总时间
    public final static String guanfang_rank_up_position_count = CLASS_NAME + "guanfang_rank_up_position_count";    // 官方排行赛上榜次数
    public final static String guanfang_rank_complete_count = CLASS_NAME + "guanfang_rank_complete_count";          // 官方排行赛中完赛次数
    public final static String guanfang_rank_position_count = CLASS_NAME + "guanfang_rank_position_count";          // 官方排行赛总名次
    public final static String guanfang_rank_best_position = CLASS_NAME + "guanfang_rank_best_position";            // 官方排行赛中最好的名次
    public final static String work_day_run_count = CLASS_NAME + "work_day_run_count";                              // 工作日跑步次数
    public final static String work_day_run_distance = CLASS_NAME + "work_day_run_distance";                        // 工作日跑步里程
    public final static String work_day_run_time = CLASS_NAME + "work_day_run_time";                                // 工作日跑步时间
    public final static String weekend_run_count = CLASS_NAME + "weekend_run_count";                                // 周末跑步次数
    public final static String weekend_run_distance = CLASS_NAME + "weekend_run_distance";                          // 周未跑步距离
    public final static String weekend_run_time = CLASS_NAME + "weekend_run_time";                                  // 周未跑步时间
    public final static String pop1_ = CLASS_NAME + "pop1";                                                         // 最佳跑者是否弹窗
    public final static String pop2_ = CLASS_NAME + "pop2";                                                         // 城际巴士是否弹窗
    public final static String pop3_ = CLASS_NAME + "pop3";                                                         // 时间长河是否弹窗
    public final static String pop4_ = CLASS_NAME + "pop4";                                                         // 幻影脚是否弹窗
    public final static String pop5_ = CLASS_NAME + "pop5";                                                         // 赛事王是否弹窗
    public final static String pop6_ = CLASS_NAME + "pop6";                                                         // 1vs100是否弹窗
    public final static String pop7_ = CLASS_NAME + "pop7";                                                         // 工作自由是否弹窗
    public final static String pop8_ = CLASS_NAME + "pop8";                                                         // 本月是否已经弹窗，1 已经弹窗，还没有弹窗
    public final static String month_honour_pop = CLASS_NAME + "month_honour_pop";                                  // 本月是否已经弹窗
    public final static String is_add_rank = CLASS_NAME + "is_add_rank";                                            //
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //跑步类型数
    private Integer activityTypeCount;
    //跑步里程
    private BigDecimal runDistance;
    //跑步时长
    private BigDecimal runTime;
    //跑步次数
    private Integer runCount;
    //月
    private String month;
    //用户id
    private Long userId;
    //最大速度
    private BigDecimal maxVelocity;
    //平均速度最快对应的里程
    private BigDecimal maxVelocityDistance;
    //官方多人同跑完赛事最多的用户
    private Integer guanfangMulCompileCount;
    //官方多人同跑中总里程
    private BigDecimal guanfangMulRunDistance;
    //官方多人同跑总时间
    private BigDecimal guanfangMulRunTime;
    //官方排行赛上榜次数
    private Integer guanfangRankUpPositionCount;
    //官方排行赛中完赛次数
    private Integer guanfangRankCompleteCount;
    //官方排行赛总名次
    private Integer guanfangRankPositionCount;
    //官方排行赛中最好的名次
    private Integer guanfangRankBestPosition;
    //工作日跑步次数
    private Integer workDayRunCount;
    //工作日跑步里程
    private BigDecimal workDayRunDistance;
    //工作日跑步时间
    private BigDecimal workDayRunTime;
    //周末跑步次数
    private Integer weekendRunCount;
    //周未跑步距离
    private BigDecimal weekendRunDistance;
    //周未跑步时间
    private BigDecimal weekendRunTime;
    //最佳跑者是否弹窗
    private Integer pop1;
    //城际巴士是否弹窗
    private Integer pop2;
    //时间长河是否弹窗
    private Integer pop3;
    //幻影脚是否弹窗
    private Integer pop4;
    //赛事王是否弹窗
    private Integer pop5;
    //1vs100是否弹窗
    private Integer pop6;
    //工作自由是否弹窗
    private Integer pop7;
    //本月是否已经弹窗，1 已经弹窗，还没有弹窗
    private Integer pop8;
    //本月是否已经弹窗
    private Integer monthHonourPop;
    //
    private Integer isAddRank;

    @Override
    public String toString() {
        return "MonthRunData{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",activityTypeCount=" + activityTypeCount +
                ",runDistance=" + runDistance +
                ",runTime=" + runTime +
                ",runCount=" + runCount +
                ",month=" + month +
                ",userId=" + userId +
                ",maxVelocity=" + maxVelocity +
                ",maxVelocityDistance=" + maxVelocityDistance +
                ",guanfangMulCompileCount=" + guanfangMulCompileCount +
                ",guanfangMulRunDistance=" + guanfangMulRunDistance +
                ",guanfangMulRunTime=" + guanfangMulRunTime +
                ",guanfangRankUpPositionCount=" + guanfangRankUpPositionCount +
                ",guanfangRankCompleteCount=" + guanfangRankCompleteCount +
                ",guanfangRankPositionCount=" + guanfangRankPositionCount +
                ",guanfangRankBestPosition=" + guanfangRankBestPosition +
                ",workDayRunCount=" + workDayRunCount +
                ",workDayRunDistance=" + workDayRunDistance +
                ",workDayRunTime=" + workDayRunTime +
                ",weekendRunCount=" + weekendRunCount +
                ",weekendRunDistance=" + weekendRunDistance +
                ",weekendRunTime=" + weekendRunTime +
                ",pop1=" + pop1 +
                ",pop2=" + pop2 +
                ",pop3=" + pop3 +
                ",pop4=" + pop4 +
                ",pop5=" + pop5 +
                ",pop6=" + pop6 +
                ",pop7=" + pop7 +
                ",pop8=" + pop8 +
                ",monthHonourPop=" + monthHonourPop +
                ",isAddRank=" + isAddRank +
                "}";
    }
}
