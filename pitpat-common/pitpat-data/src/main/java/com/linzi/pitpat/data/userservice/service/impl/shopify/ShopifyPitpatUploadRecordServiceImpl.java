package com.linzi.pitpat.data.userservice.service.impl.shopify;
/**
 * <p>
 * pitpat用户同步到shopfiy记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.equipmentservice.model.vo.ZnsUserEquipmentVo;
import com.linzi.pitpat.data.third.shopify.ShopifyUtil;
import com.linzi.pitpat.data.third.shopify.req.ShopifyCustomerVO;
import com.linzi.pitpat.data.userservice.enums.ShopifyConstant;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserEquipmentDao;
import com.linzi.pitpat.data.userservice.mapper.shopify.ShopifyPitpatUploadRecordDao;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.shopify.ShopifyPitpatUploadRecordEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyPitpatUploadRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
public class ShopifyPitpatUploadRecordServiceImpl implements ShopifyPitpatUploadRecordService {

    private final ShopifyPitpatUploadRecordDao shopifyPitpatUploadRecordDao;
    private final ZnsUserEquipmentDao znsUserEquipmentDao;
    private final ZnsUserService znsUserService;

    @Value("${spring.profiles.active}")
    private String profile;


    /**
     * 同步pitpat用户到shopify
     */
    public void sycnPitpatToShopify() {
        //查询最近一条上传记录
        log.info("ShopfiyPitpatUploadRecordServiceImpl#sycnPitpatToShopify------同步pitpat用户到shopify,开始");
        if (!EnvUtils.isOnline(profile)) {
            log.info("ShopfiyPitpatUploadRecordServiceImpl#sycnPitpatToShopify------同步pitpat用户到shopify,测试环境不处理shopify");
            return;
        }
        ShopifyPitpatUploadRecordEntity recordEntity = shopifyPitpatUploadRecordDao.selectLastRecord();
        ZonedDateTime firstConnectTime = DateTimeUtil.parse("2022-01-01 00:00:00");
        if (recordEntity != null) {
            firstConnectTime = recordEntity.getFirstConnectTime();
        }
        //查询最近一条用户设备记录
        ZnsUserEquipmentVo znsUserEquipmentDto = znsUserEquipmentDao.selectNextUserEquipment(firstConnectTime);
        if (znsUserEquipmentDto == null) {
            log.info("ShopfiyPitpatUploadRecordServiceImpl#sycnPitpatToShopify------同步pitpat用户到shopify,没有可以同步到跑步机用户");
            return;
        }
        ZnsUserEntity znsUserEntity = znsUserService.findById(znsUserEquipmentDto.getUserId());
        if (znsUserEntity == null) {
            log.info("ShopfiyPitpatUploadRecordServiceImpl#sycnPitpatToShopify------同步pitpat用户到shopify,用户不存在");
            return;
        }
        //上传用户
        String reason = "";
        ShopifyConstant.BrandEnum brandEnum = ShopifyConstant.getBrandEnumByCode(znsUserEquipmentDto.getBrand());
        if (brandEnum != null) {
            ShopifyCustomerVO customerVO = new ShopifyCustomerVO(znsUserEntity.getFirstName(), znsUserEntity.getLastName(), znsUserEntity.getEmailAddressEn());
            reason = ShopifyUtil.uploadOneCustomer(brandEnum, customerVO);
        }

        //保存同步记录
        ShopifyPitpatUploadRecordEntity entity = new ShopifyPitpatUploadRecordEntity();
        entity.setBrand(znsUserEquipmentDto.getBrand());
        //todo emailAddressEn 需要修改业务待确定
//		entity.setEmailAddress(znsUserEntity.getEmailAddressEn());
        entity.setFirstName(znsUserEntity.getFirstName());
        entity.setLastName(znsUserEntity.getLastName());
        entity.setUserId(znsUserEntity.getId());
        entity.setFirstConnectTime(znsUserEquipmentDto.getFirstConnectTime());
        Integer uploadState = StringUtils.hasText(reason) ? ShopifyConstant.UploadStateEnum.UPLOADSTATE_0.getCode() : ShopifyConstant.UploadStateEnum.UPLOADSTATE_1.getCode();
        entity.setUploadState(uploadState);
        entity.setUploadResult(reason);
        shopifyPitpatUploadRecordDao.insert(entity);
        log.info("ShopfiyPitpatUploadRecordServiceImpl#sycnPitpatToShopify------同步pitpat用户到shopify,结束");
    }

}
