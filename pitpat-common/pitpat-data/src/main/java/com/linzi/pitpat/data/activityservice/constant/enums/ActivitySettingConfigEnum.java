package com.linzi.pitpat.data.activityservice.constant.enums;

import com.linzi.pitpat.data.activityservice.dto.VideoViewDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveCountDownDto;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ActivitySettingConfigEnum {
    ALLOW_JOIN_TEAM("allowJoinTeam", "团队赛允许参赛队伍", ActivityTeamSettingEnum.class),
    ALLOW_JOIN_TEAM_LIMIT("allowJoinTeamLimit", "团队赛允许参赛队伍队伍数量上线", Integer.class),
    ALLOW_JOIN_TEAM_MEMBER_LIMIT("allowJoinTeamMemberLimit", "团队赛每个参赛队伍成员数量上限，-1不限制", Integer.class),
    AWARD_SEND_TYPE("awardSendType", "排名奖励发送类型 0 默认发放 1 手工发放", Integer.class),
    COMPLETE_AWARD_SEND_TYPE("completeAwardSendType", "完赛奖励发送类型 0 默认发放 1 手工发放", Integer.class),
    SURPASS_AWARD_SEND_TYPE("surpassAwardSendType", "超越奖励发送类型 0 默认发放 1 手工发放", Integer.class),
    //    recordBreakingAwardSendType 破记录奖励
    RECORD_BREAKING_AWARD_SEND_TYPE("recordBreakingAwardSendType", "破记录奖励发送类型 0 默认发放 1 手工发放", Integer.class),
    COMPLETE_AMOUNT_AWARD_TYPE("completeAmountAwardType", "完赛金额奖励0：默认，固定奖励 ，1：瓜分", Integer.class),
    COMPLETE_SCORE_AWARD_TYPE("completeScoreAwardType", "完赛积分奖励0：默认，固定奖励 ，1：瓜分", Integer.class),
    SURPASS_AMOUNT_AWARD_TYPE("surpassAmountAwardType", "超越金额奖励0：默认，固定奖励 ，1：瓜分", Integer.class),
    SURPASS_SCORE_AWARD_TYPE("surpassScoreAwardType", "超越积分奖励0：默认，固定奖励 ，1：瓜分", Integer.class),
    SURPASS_USER_CODE("surpassUserCode", "超越用户编码", Integer.class),

    AWARD_SEND_STATUS("awardSendStatus", "排名奖励发送状态 0:待审核 1:审核通过 2 审核下发中(中间状态)", Integer.class),
    @Deprecated
    COMPETITIVE_SEASON_ACTIVITY_RANKING_THRESHOLD("competitiveSeasonActivityRankingThreshold", "竞技赛门槛", String.class),
    COMPETITIVE_SEASON_ACTIVITY_RANKING_THRESHOLD_CUSTOM_SEASON("competitiveSeasonActivityRankingThresholdCustomSeason", "竞技赛门槛", String.class),
    COMPETITIVE_SEASON_ACTIVITY_RANGE("competitiveSeasonActivityRange", "关联活动Id", String.class),
    COMPETITIVE_COUNT_DOWN_SETTING("competitiveCountDownSetting", "竞技赛倒计时配置", CompetitiveCountDownDto.class),
    AFTER_START_LIMIT("afterStartLimit", "开赛时间限制", Integer.class),
    //达标人数
    TARGET_USER_COUNT("targetUserCount", "达标人数", Integer.class),
    ACTIVITY_END_AWARD_SEND_STATUS("activityEndAwardSendStatus", "活动结束奖励发送状态 0:待发放，风控审核中 1:已发放，2：待发放，自由挑战跑榜单", Integer.class),

    //    ramLimit RAM限制
    RAM_LIMIT("ramLimit", "ram限制", Integer.class),
    //    isAiCommentary ai解说
    IS_AI_COMMENTARY("isAiCommentary", "是否开启ai解说", Integer.class),
    ACTIVITY_RECOMMEND_POSITION_HOME("activityRecommendPosition_HOME", "首页推荐", Integer.class),
    ACTIVITY_RECOMMEND_POSITION_COMMUNITY("activityRecommendPosition_COMMUNITY", "社区推荐", Integer.class),
    ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE("activityRecommendCommunityLanguageCode", "社区默认语言", String.class),
    ACTIVITY_MAX_AWARD("activity_max_award", "活动最大奖励", String.class),
    ALLOW_AWARD_COUPON_SWITCH("allowAwardCouponSwitch", "允许使用奖励券开关", Integer.class),
    VIDEO_VIEW_CONFIG("video_view_config", "视频审核配置", VideoViewDto.class),
    GENDER_LIMIT("gender_limit", "性别限制", Integer.class),

    DESK_BIKE_EQUIPMENT_VERSION("desk_bike_equipment_version", "脚踏机版本", Integer.class),
    ALlOW_USER_RANK_CHANGE_PUSH("ALlOW_RANK_CHANGE_PUSH", "是否符合允许完赛后成绩变化推送", Boolean.class),
    FREE_ACTIVITY_CONFIG("FREE_ACTIVITY_CONFIG", "自由挑战赛配置", FreeActivityConfig.class),

    ;
    private final String code;
    private final String msg;
    private final Class<?> valueClass;

}
