package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.console.FreeActivityDto;
import com.linzi.pitpat.data.activityservice.dto.console.FreeActivityTaskDto;
import com.linzi.pitpat.data.activityservice.dto.console.RouteDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunTemplateDo;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.query.MainActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RunTemplateService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.vo.RunRouteVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 免费活动管理器
 * 负责免费活动的创建、编辑和清理等核心业务逻辑
 *
 * @since 2025年1月
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FreeActivityManager {

    /**
     * 玩法ID，默认为1
     */
    @Value("${activity.free.playId:1}")
    private Long playId;

    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ActivityParamsService activityParamsService;
    private final AwardActivityBizService awardActivityBizService;
    private final ZnsRunRouteService runRouteService;
    private final RunTemplateService runTemplateService;
    /**
     * 根据活动ID获取免费活动信息
     * 用于编辑时的数据回显
     *
     * @param activityId 活动ID
     * @return 免费活动数据传输对象，如果不存在则返回null
     */
    public FreeActivityDto getByActivityId(Long activityId) {

        return getByActivityId(activityId,true);
    }

    /**
     * 根据活动ID获取免费活动信息
     * 用于编辑时的数据回显
     *
     * @param activityId 活动ID
     * @return 免费活动数据传输对象，如果不存在则返回null
     */
    public FreeActivityDto getByActivityId(Long activityId,Boolean containAward) {
        // 步骤1: 获取主活动信息
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (mainActivity == null) {
            log.warn("活动不存在，activityId: {}", activityId);
            return null;
        }

        // 步骤2: 检查是否为免费挑战赛主活动
        if (!MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(mainActivity.getMainType())) {
            log.warn("活动类型不是免费挑战赛主活动，activityId: {}, mainType: {}", activityId, mainActivity.getMainType());
            return null;
        }

        // 步骤3: 构建FreeActivityDto对象
        FreeActivityDto dto = new FreeActivityDto();
        dto.setId(activityId);

        // 步骤4: 获取子活动信息
        List<SubActivity> subActivities = subActivityService.getAllSingleActByMain(activityId);
        if (!CollectionUtils.isEmpty(subActivities)) {
            SubActivity subActivity = subActivities.get(0);
            dto.setTargetMileage(subActivity.getTarget());

            // 构建路线信息
            RouteDto routeDto = new RouteDto();
            routeDto.setId(subActivity.getRouteId());
            RunRouteVO runRouteVO = runRouteService.wrapperRunRouteVO(runRouteService.selectRunRouteById(subActivity.getRouteId()));
            dto.setRoute(runRouteVO);
        }

        // 步骤5: 获取活动配置
        Optional<ActivityParams> paramsOptional = activityParamsService.findOneByMainActivityAndParamType(
                activityId,
                ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG
        );


        if (paramsOptional.isPresent()) {
            FreeActivityConfig config = JsonUtil.readValue(paramsOptional.get().getParamValue(), FreeActivityConfig.class);

            dto.setMode(config.getMode());

            // 如果有挑战模板ID，设置挑战模板信息
            if (config.getRubTemplateId() != null) {
                RunTemplateDo runTemplateDo = runTemplateService.findById(config.getRubTemplateId());
                dto.setRunTemplateDo(runTemplateDo);
            }
        }

        // 步骤6: 获取奖励配置
        if (containAward){
            loadAwardConfig(dto, mainActivity);

        }

        return dto;
    }

    /**
     * 加载奖励配置信息
     *
     * @param dto          免费活动数据传输对象
     * @param mainActivity 活动
     */
    private void loadAwardConfig(FreeActivityDto dto, MainActivity mainActivity) {
        Long activityId = mainActivity.getId();
        try {
            // 获取主活动的奖励配置
            List<ActivityTargetAwardDto> mainAwards = awardActivityBizService.getAwardByAct(mainActivity);
            if (!CollectionUtils.isEmpty(mainAwards)) {
                // 设置线上奖励（主活动的奖励）
                ActivityTargetAwardDto activityTargetAwardDto = mainAwards.get(0);

                Optional<ActivityAwardConfigDto> first = activityTargetAwardDto.getAwardDtos().stream().filter(item -> AwardSentTypeEnum.ONLINE_RANK.getType().equals(item.getType())).findFirst();
                first.ifPresent(item -> dto.setOnRankAwardDto(item.getAwardLists().get(0)));

                activityTargetAwardDto.getAwardDtos().removeIf(item -> AwardSentTypeEnum.ONLINE_RANK.getType().equals(item.getType()));
                dto.setOnlineAwardDto(activityTargetAwardDto);
            }

            // 获取子活动的奖励配置
            List<MainActivity> subActivities = seriesActivityRelService.getAllMainActivity(activityId);
            if (!CollectionUtils.isEmpty(subActivities)) {
                MainActivity slaveActivity = subActivities.get(0);
                List<ActivityTargetAwardDto> subAwards = awardActivityBizService.getAwardByAct(slaveActivity);
                if (!CollectionUtils.isEmpty(subAwards)) {
                    // 设置线下奖励（子活动的奖励）
                    ActivityTargetAwardDto subTargetAwardDto = subAwards.get(0);
                    subTargetAwardDto.getAwardDtos().removeIf(item -> AwardSentTypeEnum.ONLINE_RANK.getType().equals(item.getType()));

                    dto.setOfflineAwardDto(subAwards.get(0));
                }
            }

        } catch (Exception e) {
            log.error("加载奖励配置失败，activityId: {}", activityId, e);
        }
    }

    /**
     * 编辑免费活动
     * 先清理现有活动，然后重新创建
     *
     * @param dto 免费活动数据传输对象
     */
    @Transactional
    public void edit(FreeActivityDto dto) {
        // 步骤1: 重新创建活动
        create(dto);
        // 步骤12 清理现老活动及其相关数据
        cleanActivity(dto.getId());


    }

    /**
     * 清理活动及其相关数据
     * 包括奖励配置、活动参数、主活动、子活动等
     *
     * @param activityId 活动ID
     */
    private void cleanActivity(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        // 步骤1: 删除奖励配置
        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(activityId);
        awardActivityBizService.deleteAwardConfig(awardQuery);

        // 步骤2: 删除活动参数配置
        activityParamsService.deleteByActId(activityId);

        // 步骤3: 删除主活动
        mainActivityService.deleteById(activityId);

        // 步骤4: 删除子活动
        subActivityService.deleteByActId(activityId);

        // 步骤5: 如果是主活动，递归清理关联的子活动

        if (MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(mainActivity.getMainType())) {
            for (MainActivity activity : seriesActivityRelService.getAllMainActivity(activityId)) {
                cleanActivity(activity.getId());
            }
        }
    }

    /**
     * 创建免费活动
     * 创建主活动和子活动，并建立关联关系
     *
     * @param dto 免费活动数据传输对象
     */
    public void create(FreeActivityDto dto) {

        // 步骤1: 计算本周二作为活动开始时间
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime tuesdayStart = null;
        if (dto.getId() == null){
            ZonedDateTime thisTuesday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.TUESDAY));
            tuesdayStart = thisTuesday.toLocalDate().atStartOfDay(now.getZone());

            log.info("本周二: {}", thisTuesday);
            log.info("本周二0点: {}", tuesdayStart);

        }else {
            MainActivity mainActivity = mainActivityService.findById(dto.getId());
            tuesdayStart = LocalDateTime.parse(mainActivity.getActivityStartTime(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault());
        }

        create(dto, tuesdayStart);
    }


    /**
     * 创建免费活动
     * 创建主活动和子活动，并建立关联关系
     *
     * @param dto 免费活动数据传输对象
     */
    public void create(FreeActivityDto dto, ZonedDateTime startTime) {

        // 步骤1: 创建主活动（FREE_CHALLENGE_MAIN）
        MainActivity masterActivity = createActivity(dto, MainActivityTypeEnum.FREE_CHALLENGE_MAIN, startTime);

        // 步骤2: 创建子活动（FREE_CHALLENGE_SUB）
        MainActivity slaveActivity = createActivity(dto, MainActivityTypeEnum.FREE_CHALLENGE_SUB, startTime);

        // 步骤3: 建立主活动和子活动的关联关系
        SeriesActivityRel rel = new SeriesActivityRel();
        rel.setParentActivityId(masterActivity.getId());
        rel.setSegmentActivityId(slaveActivity.getId());
        seriesActivityRelService.insert(rel);
    }

    /**
     * 创建单个活动
     * 包括主活动、子活动和相关配置的创建
     *
     * @param dto                  免费活动数据传输对象
     * @param mainActivityTypeEnum 活动类型枚举
     * @param startTime            活动开始时间
     * @return 创建的主活动对象
     */
    private MainActivity createActivity(FreeActivityDto dto, MainActivityTypeEnum mainActivityTypeEnum, ZonedDateTime startTime) {
        // 步骤1: 创建主活动基本信息
        MainActivity mainActivity = new MainActivity();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startString = startTime.format(formatter);
        ZonedDateTime endTime = startTime.plusDays(7); // 活动持续7天
        String endString = endTime.format(formatter);

        // 设置主活动的基本属性
        mainActivity.setActivityStartTime(startString);
        mainActivity.setActivityEndTime(endString);
        mainActivity.setApplicationStartTime(startString);
        mainActivity.setApplicationEndTime(endString);
        mainActivity.setOldType(RunActivityTypeEnum.FREE_CHALLENGE.getType());
        mainActivity.setPlayId(playId);
        mainActivity.setActivityState(ActivityStateEnum.NOT_START.getState());
        mainActivity.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType());
        mainActivity.setMainType(mainActivityTypeEnum.getType());
        mainActivity.setTargetType(ActivityConstants.TargetTypeEnum.TARGETTYPE_1.code);
        mainActivity.setCheatSwitch(2);

        // 保存主活动
        mainActivityService.insert(mainActivity);

        // 步骤2: 创建子活动
        SubActivity subActivity = new SubActivity();
        subActivity.setMainActivityId(mainActivity.getId());
        subActivity.setTarget(dto.getTargetMileage());
        if (dto.getRoute() != null){
            subActivity.setRouteId(dto.getRoute().getRouteId());
        }
        subActivity.setType(mainActivityTypeEnum.getType());
        subActivityService.insert(subActivity);

        // 步骤3: 创建免费活动配置
        FreeActivityConfig freeActivityConfig = new FreeActivityConfig();
        freeActivityConfig.setMode(dto.getMode());
        if (dto.getRunTemplateDo() != null) {
            freeActivityConfig.setRubTemplateId(dto.getRunTemplateDo().getId());
        }
        activityParamsService.saveConfigSingleValue(mainActivity.getId(), ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, JsonUtil.writeString(freeActivityConfig));

        // 步骤4: 配置奖励信息
        ArrayList<ActivityTargetAwardDto> awardDtos = new ArrayList<>();



        // 根据活动类型配置不同的奖励
        if (mainActivityTypeEnum == MainActivityTypeEnum.FREE_CHALLENGE_MAIN) {
            // 配置上榜奖励
            ActivityAwardDto onRankAwardDto = dto.getOnRankAwardDto();
            if (onRankAwardDto != null) {
                ActivityTargetAwardDto activityTargetAwardDto = new ActivityTargetAwardDto();
                activityTargetAwardDto.setTarget(dto.getTargetMileage());
                ActivityAwardConfigDto activityAwardConfigDto = new ActivityAwardConfigDto();
                activityAwardConfigDto.setAwardLists(List.of(onRankAwardDto));
                activityAwardConfigDto.setType(AwardSentTypeEnum.ONLINE_RANK.getType());
                activityTargetAwardDto.setAwardDtos(List.of(activityAwardConfigDto));
                awardDtos.add(activityTargetAwardDto);
            }
            // 主活动配置线上奖励
            if (dto.getOnlineAwardDto() != null){
                awardDtos.add(dto.getOnlineAwardDto());
            }
        } else {
            // 子活动配置线下奖励
            if (dto.getOfflineAwardDto() != null){
                awardDtos.add(dto.getOfflineAwardDto());
            }
        }

        // 保存奖励配置
        awardActivityBizService.saveTargetAward(awardDtos, mainActivity, List.of(subActivity));

        return mainActivity;
    }

    /**
     * 获取免费活动任务列表
     * 查询FREE_CHALLENGE_MAIN类型的活动，按活动开始时间倒序排列
     *
     * @return 免费活动任务列表
     */
    public List<FreeActivityTaskDto> taskList() {
        // 步骤1: 查询FREE_CHALLENGE_MAIN类型的活动
        List<MainActivity> activities = mainActivityService.findList(
                MainActivityQuery.builder()
                        .mainType(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType())
                        .build()
        );

        if (CollectionUtils.isEmpty(activities)) {
            return new ArrayList<>();
        }

        // 步骤2: 按活动开始时间倒序排序
        activities.sort((a1, a2) -> {
            ZonedDateTime startTime1 = parseActivityStartTime(a1.getActivityStartTime());
            ZonedDateTime startTime2 = parseActivityStartTime(a2.getActivityStartTime());
            return startTime2.compareTo(startTime1); // 倒序
        });

        // 步骤3: 转换为FreeActivityTaskDto列表
        List<FreeActivityTaskDto> taskList = new ArrayList<>();
        for (MainActivity activity : activities) {
            FreeActivityTaskDto taskDto = new FreeActivityTaskDto();
            taskDto.setActivityId(activity.getId());
            taskDto.setActivityStatus(activity.getActivityState());

            // 解析活动开始时间
            ZonedDateTime startTime = parseActivityStartTime(activity.getActivityStartTime());
            taskDto.setStartTime(startTime);

            // 解析活动结束时间
            ZonedDateTime endTime = parseActivityStartTime(activity.getActivityEndTime());
            taskDto.setEndTime(endTime);

            // 生成任务名称：月份第几周格式
            taskDto.setTaskName(generateTaskName(startTime));

            taskList.add(taskDto);
        }

        return taskList;
    }

    /**
     * 解析活动时间字符串为ZonedDateTime
     *
     * @param timeStr 时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return ZonedDateTime对象
     */
    private ZonedDateTime parseActivityStartTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return ZonedDateTime.parse(timeStr, formatter.withZone(java.time.ZoneId.systemDefault()));
        } catch (Exception e) {
            log.error("解析活动时间失败，timeStr: {}", timeStr, e);
            return null;
        }
    }

    /**
     * 生成任务名称
     * 格式：MM月第N周
     *
     * @param startTime 活动开始时间
     * @return 任务名称
     */
    private String generateTaskName(ZonedDateTime startTime) {
        if (startTime == null) {
            return "未知时间";
        }

        // 获取月份
        int month = startTime.getMonthValue();
        String monthStr = String.format("%02d", month);

        // 计算是当月的第几周
        int weekOfMonth = calculateWeekOfMonth(startTime);

        return monthStr + "月第" + weekOfMonth + "周";
    }

    /**
     * 计算指定日期是当月的第几周
     *
     * @param dateTime 指定日期
     * @return 第几周
     */
    private int calculateWeekOfMonth(ZonedDateTime dateTime) {
        // 获取当月第一天
        ZonedDateTime firstDayOfMonth = dateTime.withDayOfMonth(1);

        // 获取当月第一天是周几（1-7，1代表周一）
        int firstDayOfWeek = firstDayOfMonth.getDayOfWeek().getValue();

        // 获取当前日期是当月的第几天
        int dayOfMonth = dateTime.getDayOfMonth();

        // 计算是第几周
        // 如果第一天是周一，那么第一周就是1-7号
        // 如果第一天是周二，那么第一周就是1-6号，第二周从7号开始
        int weekOfMonth = (dayOfMonth + firstDayOfWeek - 2) / 7 + 1;

        return weekOfMonth;
    }

    /**
     * 生成下一个活动
     * 当所有现有活动都已完成时，自动生成下一个周期的活动
     * 基于当前进行中的活动配置，创建下一个7天周期的活动
     */
    @Transactional
    public void generateNextActivity() {
        // 步骤1: 查询所有FREE_CHALLENGE_MAIN类型的活动
        List<MainActivity> activities = mainActivityService.findList(
                MainActivityQuery.builder()
                        .mainType(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType())
                        .build()
        );

        // 如果没有活动，直接返回
        if (CollectionUtils.isEmpty(activities)) {
            log.info("没有找到挑战赛活动，无需生成下一个活动");
            return;
        }

        // 步骤2: 检查是否有未开始的活动
        // 如果存在未开始的活动，说明当前周期还未开始，不需要生成下一个活动
        for (MainActivity activity : activities) {
            if (MainActivityStateEnum.NOT_STARTED.getCode().equals(activity.getActivityState())) {
                log.info("存在未开始的活动，无需生成下一个活动，activityId: {}", activity.getId());
                return;
            }
        }

        // 步骤3: 查找当前进行中的活动
        // 如果找到进行中的活动，基于其配置生成下一个周期的活动
        activities.stream().sorted(Comparator.comparing(MainActivity::getActivityStartTime).reversed())
                .filter(s -> Objects.equals(s.getActivityState(), MainActivityStateEnum.STARTED.getCode()))
                .findFirst()
                .ifPresent(currentActivity -> {
                    log.info("找到进行中的活动，开始生成下一个周期活动，currentActivityId: {}", currentActivity.getId());
                    
                    // 步骤4: 获取当前活动的完整配置信息
                    FreeActivityDto activityDto = getByActivityId(currentActivity.getId());
                    if (activityDto == null) {
                        log.error("获取活动配置失败，无法生成下一个活动，activityId: {}", currentActivity.getId());
                        return;
                    }

                    // 步骤5: 计算下一个活动的开始时间
                    // 解析当前活动的开始时间，并加上7天作为下一个活动的开始时间
                    ZonedDateTime orgStart = LocalDateTime.parse(currentActivity.getActivityStartTime(),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault());

                    // 下一个活动开始时间 = 当前活动开始时间 + 7天
                    ZonedDateTime start = orgStart.plusDays(7);
                    log.info("下一个活动开始时间: {}", start);

                    // 步骤6: 创建下一个周期的活动
                    // 使用相同的配置，但开始时间延后7天
                    create(activityDto, start);
                    
                    log.info("成功生成下一个周期活动，开始时间: {}", start);
                });
    }

    @Transactional
    public void init(){
        FreeActivityDto dto = new FreeActivityDto();
        create(dto);
    }
}
