package com.linzi.pitpat.data.clubservice.manager;

/**
 * club 发送推送相关功能
 */

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMember;
import com.linzi.pitpat.data.clubservice.model.query.ClubMemberQuery;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class ClubPushManager {

    private final AppMessageService appMessageService;

    private final ZnsUserService znsUserService;

    private final ClubService clubService;


    private final ClubMemberService clubMemberService;

//    public void sendClubActivityNotice(final Long clubId) {
//        CompletableFuture.runAsync(() -> {
//                    Club club = clubService.findById(clubId);
//                    if (Objects.isNull(club)) return;
//                    List<ClubMember> clubMembers = clubMemberService.findListByQuery(ClubMemberQuery.builder().clubId(clubId).activityNoticeSwitch(1).build());
//                    if (CollectionUtils.isEmpty(clubMembers)) return;
//                    List<Long> userIds = clubMembers.stream().map(ClubMember::getUserId).collect(Collectors.toList());
//
//                    //俱乐部活动通知bo
//                    MessageBo messageBoEN = new MessageBo();
//                    messageBoEN.setTitle("Pitpat");
//                    messageBoEN.setContent("Join the fun with our new Club events!");
//                    messageBoEN.setJumpType("5");
//                    messageBoEN.setCollapseKey("Activity notification");
//                    messageBoEN.setJumpImage(club.getLogo());
//                    messageBoEN.setRouteType(1);
//                    messageBoEN.setRouteValue("lznative://lzrace/ClubHome");
//                    messageBoEN.setData((Map.of("clubId", clubId)));
//                    messageBoEN.setNotificationType(NoticeTypeEnum.ACTIVITY_INVITATION.getType());
//
//                    MessageBo messageBoFr = new MessageBo();
//                    BeanUtils.copyProperties(messageBoEN, messageBoFr);
//                    messageBoFr.setContent("Participez à l'événement de Club!");
//
//                    List<ZnsUserEntity> userEntityList = znsUserService.findByIds(userIds);
//                    List<Long> enUserIds = userEntityList.stream().filter(s -> Objects.equals(s.getLanguageCode(), I18nConstant.LanguageCodeEnum.en_US.getCode())).map(ZnsUserEntity::getId).collect(Collectors.toList());
//                    userIds.removeAll(enUserIds);
//
//                    try {
//                        String batchNumber = OrderUtil.getBatchNo() + "clubActivityNotice";
//                        if (!CollectionUtils.isEmpty(enUserIds)) {
//                            appMessageService.push(enUserIds, messageBoEN, batchNumber, 0);
//                        }
//                        if (!CollectionUtils.isEmpty(userIds)) {
//                            appMessageService.push(userIds, messageBoFr, batchNumber, 0);
//                        }
//                    } catch (Exception e) {
//                        log.info("推送PUSH异常", e);
//                    }
//                })
//                .exceptionally((e) -> {
//                    log.error("发送俱乐部通知失败", e);
//                    return null;
//                });
//
//
//    }

}
