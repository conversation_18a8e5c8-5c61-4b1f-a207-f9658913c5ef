package com.linzi.pitpat.data.clubservice.model.response;


import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class ClubMemberDetailResponseDto {

    /**
     * 用户ID
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 用户邮箱
     */
    @ExcelProperty(value = "email")
    private String emailAddress;

    /**
     * 用户名称
     */
    @ExcelIgnore
    private String firstName;

    /**
     * 角色：OWNER（创建者）, MANAGER（管理员）, MEMBER（普通成员）
     */
    @ExcelIgnore
    private String role;

    /**
     * 加入时间
     */
    @ExcelIgnore
    private ZonedDateTime gmtCreate;;

}
