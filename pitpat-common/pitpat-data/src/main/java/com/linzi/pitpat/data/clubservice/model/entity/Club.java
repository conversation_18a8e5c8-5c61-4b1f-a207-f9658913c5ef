package com.linzi.pitpat.data.clubservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubStateEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.ZonedDateTime;

import static com.baomidou.mybatisplus.annotation.FieldStrategy.ALWAYS;

@Data
@NoArgsConstructor
@TableName("zns_club")
@FieldNameConstants
public class Club implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    // 俱乐部群组id
    /**
     * @see ApiConstants#clubGroupId + id + env
     */
    private String clubGroupId;
    //是否逻辑删除，0：未删除
    @TableLogic(delval = "UNIX_TIMESTAMP()")
    private Long isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;
    //俱乐部名称
    private String name;
    //小写名称
    private String lowerName;
    //俱乐部描述
    @TableField(updateStrategy = ALWAYS)
    private String description;
    //俱乐部LOGO
    private String logo;
    //俱乐部负责人
    private Long ownerUserId;
    //俱乐部会员人数
    private Integer memberCount;
    //俱乐部参赛次数
    private Integer matchCount;
    //俱乐部等级
    private String clubLevel;
    //normal（正常）, frozen（冻结）, disbanded（解散）
    /**
     * @see ClubStateEnum
     */
    private String state;
    //邀请码
    private String inviteCode;
    //申请是否需要邀请码
    private Boolean requiredInviteCode;
    //申请是否需要审核
    private Boolean requiresApproval;
    //俱乐部操作备注
    private String operateNote;

    //终止时间
    private ZonedDateTime terminatedDate;

    //是否推荐(0:不推荐，1：推荐)
    private Integer isRecommend;

    //俱乐部类型（0：默认，1：新用户参加一次，2月季年赛）
    private Integer type;
    //是否会员权益创建的俱乐部（0:不是，1:是）
    private Integer isVipCreate;

    //俱乐部管理员数量
    private Integer managerNum;

    //背景图
    private String backgroundImg;

    public boolean isNormal() {
        return ClubStateEnum.NORMAL.getCode().equals(state);
    }

    /**
     * 俱乐部还没有解散 俱乐部数据有效
     *
     * @return
     */
    public boolean isValid() {
        return !ClubStateEnum.DISBANDED.getCode().equals(state);
    }

    public void setDescription(String description) {
        if (description == null) {
            description = "";
        }
        this.description = description;
    }

    public String getDescription() {
        if (description == null) {
            return "";
        }
        return description;
    }
}
