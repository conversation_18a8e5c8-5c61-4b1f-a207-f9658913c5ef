package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;

import static com.baomidou.mybatisplus.annotation.FieldStrategy.ALWAYS;

/**
 * 用户批次发放优惠券表
 *
 * <AUTHOR>
 * @since 2023-06-12
 */

@Data
@NoArgsConstructor
@TableName("zns_user_coupon_send_batch")
public class UserCouponSendBatch implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendBatch:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";        // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    // 最后修改时间
    public final static String coupon_id = CLASS_NAME + "coupon_id";          // 发放优惠券id
    public final static String status_ = CLASS_NAME + "status";               // 状态 【 0: 未生效 1 生效 -1 失效】
    public final static String send_status = CLASS_NAME + "send_status";      // 状态 【 0: 未发送 1 发送成功 -1 发送失败 】
    public final static String gmt_send = CLASS_NAME + "gmt_send";            // 发送时间
    public final static String remarks_ = CLASS_NAME + "remarks";             // 标记备注
    public final static String file_url = CLASS_NAME + "file_url";            // excel文件地址
    public final static String title_ = CLASS_NAME + "title";                 // 标题
    public final static String target_count = CLASS_NAME + "target_count";    // 目标人数
    public final static String send_count = CLASS_NAME + "send_count";        // 发送人数
    public final static String creator_ = CLASS_NAME + "creator";             // 创建者
    public final static String modifier_ = CLASS_NAME + "modifier";           // 最后修改者
    public final static String send_type = CLASS_NAME + "send_type";          // 发送类型 0 单张 1 卷包(多张)
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //发放优惠券id
    private String couponIds;
    //状态 【 0: 未生效 1 生效 -1 失效】
    private Integer status;
    //状态 【 0: 未发送 1 发送成功 -1 发送失败 】
    private Integer sendStatus;
    //发送时间
    private ZonedDateTime gmtSend;
    //标记备注
    private String remarks;
    //excel文件地址
    private String fileUrl;
    //标题
    private String title;
    //目标人数
    private Integer targetCount;
    //发送人数
    private Integer sendCount;
    //创建者
    private String creator;
    //最后修改者
    private String modifier;
    //发送类型 0 单张 1 卷包(多张)
    private Integer sendType;
    //发放积分
    private Integer score;
    //发放服装
    private String wears;
    //发放勋章
    private String medalIds;

    private Long activityId;

    //会员发放类型，0:有期限，1：永久
    private Integer vipSendType;
    //会员天数
    @TableField(updateStrategy = ALWAYS)
    private Integer vipDays;
    /**
     * 奖励类型  运营手动发放类型100
     */
    private Integer tradeType;
    /**
     * 奖励子类型 运营手动发放类型 24
     */
    private Integer tradeSubype;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 审核人
     */
    private String reviewer;
    /**
     * 审核时间
     */
    private ZonedDateTime gmtReview;

    @Override
    public String toString() {
        return "UserCouponSendBatch{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",couponId=" + couponIds +
                ",status=" + status +
                ",sendStatus=" + sendStatus +
                ",gmtSend=" + gmtSend +
                ",remarks=" + remarks +
                ",fileUrl=" + fileUrl +
                ",title=" + title +
                ",targetCount=" + targetCount +
                ",sendCount=" + sendCount +
                ",creator=" + creator +
                ",modifier=" + modifier +
                ",sendType=" + sendType +
                "}";
    }
}
