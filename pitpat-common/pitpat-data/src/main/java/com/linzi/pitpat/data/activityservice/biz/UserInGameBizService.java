package com.linzi.pitpat.data.activityservice.biz;

import com.google.common.collect.Maps;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomStatusEnum;
import com.linzi.pitpat.data.activityservice.model.dto.UserInGameDto;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityRoomUserDo;
import com.linzi.pitpat.data.activityservice.model.entity.FreeRoomDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.query.FreeActivityRoomUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.FreeRoomQuery;
import com.linzi.pitpat.data.activityservice.service.FreeActivityRoomUserService;
import com.linzi.pitpat.data.activityservice.service.FreeRoomService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@Component
@RequiredArgsConstructor
public class UserInGameBizService {

    private static final String USER_IN_GAME_STATUS_KEY = "GAME:USER:Status";

    private final RedissonClient redissonClient;

    private final FreeActivityRoomUserService freeActivityRoomUserService;
    private final FreeRoomService freeRoomService;
    private final MainActivityService mainActivityService;

    /**
     * 更新用户在游戏状态
     *
     * @param userId
     * @param roomId
     * @param isInGame
     * @param routeId
     */
    public void userStateChanged(Long userId, Long roomId, boolean isInGame, Long activityId, Long routeId) {
        RMap<Long, UserInGameDto> map = redissonClient.getMap(USER_IN_GAME_STATUS_KEY);
        map.expire(3, TimeUnit.DAYS);
        if (isInGame) {
            UserInGameDto dto = new UserInGameDto();
            dto.setRoomId(roomId);
            dto.setActivityId(activityId);
            dto.setRouteId(routeId);
            dto.setInGame(true);
            dto.setUpdateTime(ZonedDateTime.now().toInstant().toEpochMilli());
            map.put(userId, dto);
        } else {
            map.remove(userId);
        }

        //只处理自由挑战跑活动数据, 非自由挑战跑活动不处理,如果以后新增类型需要考虑定时任务的维护
        MainActivity activity = mainActivityService.findCacheById(activityId);
        if (Objects.nonNull(activity) && MainActivityTypeEnum.isFreeChallengeActivity(activity.getMainType())) {
            //维护房间玩家状态
            freeActivityRoomUserService.changeStatus(new FreeActivityRoomUserDo().setUserId(userId).setActivityId(activityId).setRoomNum(roomId).setStatus(isInGame ? "in" : "out"));
            //判断是否没有玩家在房间
            long count = freeActivityRoomUserService.count(new FreeActivityRoomUserQuery().setActivityId(activityId).setRoomNum(roomId).setStatus("in"));
            if (count == 0) {
                freeRoomService.changeStatus(new FreeRoomDo().setActivityId(activityId).setRoomStatus(RoomStatusEnum.DISMISSED.getCode()).setRoomNumber(roomId));
            }
            //如果房间未开赛,房主退出关闭房间
            FreeRoomDo freeRoomDo = freeRoomService.findByQuery(new FreeRoomQuery().setActivityId(activityId).setRoomNumber(roomId));
            if (Objects.nonNull(freeRoomDo) && Objects.equals(freeRoomDo.getOwnerUserId(), userId)) {
                //房主退出
                if (RoomStatusEnum.NOT_STARTED.getCode().equals(freeRoomDo.getRoomStatus()) && !isInGame) {
                    freeRoomService.changeStatus(new FreeRoomDo().setActivityId(activityId).setRoomStatus(RoomStatusEnum.DISMISSED.getCode()).setRoomNumber(roomId));
                } else if (isInGame) {
                    //房主进入
                    freeRoomService.changeStatus(new FreeRoomDo().setActivityId(activityId).setRoomStatus(RoomStatusEnum.NOT_STARTED.getCode()).setRoomNumber(roomId));
                }
            }
        }
    }

    /**
     * 判断用户是否在游戏
     *
     * @param userId
     * @return
     */
    public UserInGameDto isInGame(Long userId) {
        RMap<Long, UserInGameDto> map = redissonClient.getMap(USER_IN_GAME_STATUS_KEY);
        UserInGameDto userInGameDto = map.get(userId);
        if (Objects.isNull(userInGameDto)) {
            return UserInGameDto.notIn(userId);
        }
        ZonedDateTime zonedDateTime = ZonedDateTime.now().minusDays(1);
        boolean b = zonedDateTime.toInstant().toEpochMilli() < userInGameDto.getUpdateTime();
        if (!b) {
            map.remove(userId);
            return UserInGameDto.notIn(userId);
        }
        return userInGameDto;
    }

    /**
     * 用户是否再游戏中
     *
     * @param userIds
     * @return
     */
    public Map<Long, UserInGameDto> isInGames(List<Long> userIds) {
        Map<Long, UserInGameDto> result = Maps.newHashMap();
        userIds.forEach(userId -> {
            result.put(userId, isInGame(userId));
        });
        return result;
    }
}
