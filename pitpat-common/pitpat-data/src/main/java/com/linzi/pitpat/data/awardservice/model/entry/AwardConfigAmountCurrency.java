package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 奖励配置金币种明细表
 *
 * <AUTHOR>
 * @since 2023-11-29
 */

@Data
@NoArgsConstructor
@TableName("zns_award_config_amount_currency")
public class AwardConfigAmountCurrency implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //配置ID，关联zns_award_config_amount
    private Long awardAmountId;
    //金额
    private BigDecimal amount;
    //币种/字段值枚举/缩写待定
    private String currencyCode;
}
