package com.linzi.pitpat.data.awardservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserWearsDao;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserWearsEntity;
import com.linzi.pitpat.data.awardservice.model.query.ZnsUserWearQuery;
import com.linzi.pitpat.data.awardservice.service.ZnsUserWearsService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service("znsUserWearsService")
public class ZnsUserWearsServiceImpl implements ZnsUserWearsService {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ZnsUserWearsDao znsUserWearsDao;

    @Override
    public ZnsUserWearsEntity getByUserId(Long userId) {
        return znsUserWearsDao.selectOne(Wrappers.<ZnsUserWearsEntity>lambdaQuery()
                .eq(ZnsUserWearsEntity::getUserId, userId)
                .eq(ZnsUserWearsEntity::getIsDelete, 0)
                .last("limit 1")
        );
    }


    @Override
    public void update(ZnsUserWearsEntity userWear) {
        userWear.setCreateTime(ZonedDateTime.now());
        userWear.setModifieTime(ZonedDateTime.now());
        znsUserWearsDao.updateById(userWear);
    }

    @Override
    public ZnsUserWearsEntity initUserWear(Long userId) {
        ZnsUserWearsEntity znsUserWearsEntity = this.getByUserId(userId);
        if (Objects.isNull(znsUserWearsEntity)) {
            znsUserWearsEntity = new ZnsUserWearsEntity();
            znsUserWearsEntity.setUserId(userId);
            init(znsUserWearsEntity);
            znsUserWearsDao.insert(znsUserWearsEntity);
        } else {
            init(znsUserWearsEntity);
            znsUserWearsDao.updateById(znsUserWearsEntity);
        }
        return znsUserWearsEntity;
    }

    private void init(ZnsUserWearsEntity znsUserWearsEntity) {
        znsUserWearsEntity.setShoes(0);
        znsUserWearsEntity.setJacket(0);
        znsUserWearsEntity.setHead(0);
        znsUserWearsEntity.setTrousers(0);
        znsUserWearsEntity.setSkinColour(0);
        znsUserWearsEntity.setHairColor(0);
        znsUserWearsEntity.setFaceDecoration(0);
        znsUserWearsEntity.setSuit(-1);
        znsUserWearsEntity.setBackDecoration(-1);
    }


    protected Result checkTokenAndEmail(HttpServletRequest httpServletRequest, String emailAddress) {
        String token = httpServletRequest.getHeader("token");

        if (!StringUtils.hasText(token)) {
            return CommonResult.fail(CommonError.TOKEN_LACK.getCode(), I18nMsgUtils.getMessage("common.invalid.token"));
        }

        if (StringUtils.isEmpty(emailAddress)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }

        Object redisToken = redisUtil.get(ApiConstants.APP_LOGIN_TOKEN_KEY + emailAddress);
        if (Objects.isNull(redisToken) || !redisToken.toString().equals(token)) {
            return CommonResult.fail(CommonError.INVALID_TOKEN.getCode(), I18nMsgUtils.getMessage("common.invalid.token"));
        }
        return null;
    }


    @Override
    public ZnsUserWearsEntity findByQuery(ZnsUserWearQuery query) {
        return znsUserWearsDao.selectOne(buildQueryWrapper(query));
    }

    @Override
    public void updateById(ZnsUserWearsEntity znsUserWearsEntity) {
        znsUserWearsDao.updateById(znsUserWearsEntity);
    }

    @Override
    public void insert(ZnsUserWearsEntity userWearsEntity) {
        znsUserWearsDao.insert(userWearsEntity);
    }

    @Override
    public void updateBatch(List<ZnsUserWearsEntity> list) {
        znsUserWearsDao.updateById(list);
    }

    @Override
    public List<ZnsUserWearsEntity> findList(ZnsUserWearQuery query) {
        return znsUserWearsDao.selectList(new QueryWrapper<ZnsUserWearsEntity>().lambda()
                .eq(ZnsUserWearsEntity::getIsDelete, 0)
                .in(Objects.nonNull(query.getUserIds()), ZnsUserWearsEntity::getUserId, query.getUserIds())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)));
    }

    private static Wrapper<ZnsUserWearsEntity> buildQueryWrapper(ZnsUserWearQuery query) {
        return Wrappers.<ZnsUserWearsEntity>lambdaQuery()
                .eq(ZnsUserWearsEntity::getIsDelete, 0)
                .eq(Objects.nonNull(query.getUserId()), ZnsUserWearsEntity::getUserId, query.getUserId())
                ;
    }
}
