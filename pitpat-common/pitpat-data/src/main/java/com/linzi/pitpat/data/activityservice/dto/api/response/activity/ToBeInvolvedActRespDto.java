package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:03
 */
@Data
@NoArgsConstructor
public class ToBeInvolvedActRespDto {

    /**
     * 活动队伍id
     */
    private Long activityId;
    /**
     * 完成规则类型，1：里程，2：时间
     */
    private Integer completeRuleType;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;
    /**
     * 报名开始时间
     */
    private ZonedDateTime applicationStartTime;
    /**
     * 报名结束时间
     */
    private ZonedDateTime applicationEndTime;
    /**
     * 活动状态：0表示未开始，1 表示进行中，2表示已结束，-1表示活动已取消
     */
    private Integer activityState;
    private Integer userState;
    /**
     * 活动名称
     */
    private String activityTitle;
    /**
     * 活动路线id
     */
    private Long activityRouteId;
    /**
     * 路线类型，1:2D,2:3D
     */
    private Integer routeType;
    /**
     * 是否已报名，1：是，0：否
     */
    private Integer isEnroll;
    /**
     * 奖金规则类型：1表示免费参加，2表示保证金参加 3:费用，4:纯积分， 5: 积分 + 费用
     */
    private Integer bonusRuleType;
    /**
     * 跑步距离，km/mi
     */
    private BigDecimal runMileage;
    /**
     * 跑步时间 s
     */
    private Integer runTime;
    /**
     * 已选跑步距离，m，completeRuleType = 1有值
     */
    private Integer targetRunMileage;
    /**
     * 已选跑步时间，s，completeRuleType = 2有值
     */
    private Integer targetRunTime;
    /**
     * 类型
     */
    private String mainType;
    /**
     * 活动类型，老
     */
    private Integer activityType;
}
