package com.linzi.pitpat.data.communityservice.manager;


import com.linzi.pitpat.data.communityservice.dto.api.response.ApiTopicInfo;
import com.linzi.pitpat.data.communityservice.dto.api.response.TopicHomepageDto;
import com.linzi.pitpat.data.communityservice.enums.CommunityTopicClassifyEnum;
import com.linzi.pitpat.data.communityservice.model.query.CommunityApiTopicQuery;
import com.linzi.pitpat.data.communityservice.service.CommunityTopicService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class CommunityTopicManager {

    private final CommunityTopicService communityTopicService;

    private final RedissonClient redissonClient;


    public List<ApiTopicInfo> getMyList(ZnsUserEntity loginUser) {
        CommunityApiTopicQuery communityApiTopicQuery = new CommunityApiTopicQuery();
        communityApiTopicQuery.setLanguageCode(loginUser.getLanguageCode());
        communityApiTopicQuery.setCreator(loginUser.getId());
        communityApiTopicQuery.setPageNum(1);
        communityApiTopicQuery.setPageSize(1000);
        List<ApiTopicInfo> myList = communityTopicService.findApiListByQuery(communityApiTopicQuery);
        RScoredSortedSet<Long> dailySet = redissonClient.getScoredSortedSet(RedisKeyConstant.DAY_KEY);
        Collection<Long> dailyIds = dailySet.valueRangeReversed(0, 4);
        RScoredSortedSet<Long> weekSet = redissonClient.getScoredSortedSet(RedisKeyConstant.WEEK_KEY);
        Collection<Long> weekIds = weekSet.valueRangeReversed(0, 9);
        List<Long> list = new ArrayList<>();
        list.addAll(dailyIds);
        list.addAll(weekIds);
        myList.stream().forEach(e -> {
            if (list.contains(e.getId())) {
                e.setIsHeat(true);
            }
        });
        return myList;
    }

    public List<TopicHomepageDto> getTopicHomepageList(String languageCode) {
        List<TopicHomepageDto> result = new ArrayList<>();
        
        // 今日热榜5
        TopicHomepageDto todaysHot = createTopicHomepageDto(CommunityTopicClassifyEnum.TODAYS_HOT, 0);
        RScoredSortedSet<Long> dailySet = redissonClient.getScoredSortedSet(RedisKeyConstant.DAY_KEY);
        Collection<Long> dailyIds = dailySet.valueRangeReversed(0, 29); // 先获取30个，再在sql中查询后获取前5个
        if (!dailyIds.isEmpty()) {
            List<ApiTopicInfo> dailyTopics = getHotTopicsByIds(new ArrayList<>(dailyIds), languageCode, 5);
            todaysHot.setTopicList(dailyTopics);
            result.add(todaysHot);
        }

        // 本周热榜10
        TopicHomepageDto weeksHot = createTopicHomepageDto(CommunityTopicClassifyEnum.WEEKS_HOT, 0);
        RScoredSortedSet<Long> weekSet = redissonClient.getScoredSortedSet(RedisKeyConstant.WEEK_KEY);
        Collection<Long> weekIds = weekSet.valueRangeReversed(0, 29); // 先获取30个，再在sql中查询后获取前10个
        if (!weekIds.isEmpty()) {
            List<ApiTopicInfo> weeklyTopics = getHotTopicsByIds(new ArrayList<>(weekIds), languageCode, 10);
            weeksHot.setTopicList(weeklyTopics);
            result.add(weeksHot);
        }

        // 新话题 - 按创建时间最新的前10个
        TopicHomepageDto newTopics = createTopicHomepageDto(CommunityTopicClassifyEnum.NEW_TOPICS, 1);
        CommunityApiTopicQuery latestTopicsQuery = new CommunityApiTopicQuery();
        latestTopicsQuery.setLanguageCode(languageCode);
        latestTopicsQuery.setPageNum(1);
        latestTopicsQuery.setPageSize(10);
        List<ApiTopicInfo> latestTopics = communityTopicService.findApiListByQuery(latestTopicsQuery);
        newTopics.setTopicList(latestTopics);
        result.add(newTopics);
        
        // 分类的数据
        CommunityApiTopicQuery query = new CommunityApiTopicQuery();
        query.setClassifyList(CommunityTopicClassifyEnum.getAdminCodeList());
        query.setLanguageCode(languageCode);
        query.setPageNum(1);
        query.setPageSize(1000);
        List<ApiTopicInfo> classifyTopics = communityTopicService.findApiListByQuery(query);
        if (CollectionUtils.isEmpty(classifyTopics)) {
            return result;
        }
        Map<Integer, List<ApiTopicInfo>> classifyGroupMap = classifyTopics.stream()
                .collect(Collectors.groupingBy(ApiTopicInfo::getClassify));
        // 按classify从小到大排序，为每个分类创建TopicHomepageDto
        classifyGroupMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    Integer classify = entry.getKey();
                    List<ApiTopicInfo> topics = entry.getValue();
                    
                    // 根据classify获取对应的枚举
                    CommunityTopicClassifyEnum classifyEnum = CommunityTopicClassifyEnum.getByCode(classify);
                    if (Objects.nonNull(classifyEnum)) {
                        TopicHomepageDto classifyDto = createTopicHomepageDto(classifyEnum, 0);
                        classifyDto.setTopicList(topics);
                        result.add(classifyDto);
                    }
                });
        
        return result;
    }

    
    /**
     * 创建TopicHomepageDto基础信息
     */
    private TopicHomepageDto createTopicHomepageDto(CommunityTopicClassifyEnum classifyEnum, Integer allFlag) {
        TopicHomepageDto dto = new TopicHomepageDto();
        dto.setTypeCode(classifyEnum.getCode());
        dto.setTypeDescription(classifyEnum.getName());
        dto.setTypeIcon(classifyEnum.getImage());
        dto.setAllFlag(allFlag);
        return dto;
    }
    
    /**
     * 根据ID列表获取热度话题信息
     */
    private List<ApiTopicInfo> getHotTopicsByIds(List<Long> ids, String languageCode, Integer pageSize) {
        CommunityApiTopicQuery query = new CommunityApiTopicQuery();
        query.setPageNum(1);
        query.setPageSize(pageSize);
        query.setIds(ids);
        query.setLanguageCode(languageCode);
        List<ApiTopicInfo> list = communityTopicService.findApiListByQuery(query);
        list.sort(Comparator.comparingLong(x -> ids.indexOf(x.getId())));
        list.stream().forEach(i -> i.setIsHeat(true));
        return list;
    }

}
