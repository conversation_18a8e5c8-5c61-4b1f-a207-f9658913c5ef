package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityOfficialRankingListDto;
import com.linzi.pitpat.data.activityservice.model.dto.OfficialCumulativeRunDto;
import com.linzi.pitpat.data.activityservice.model.dto.RunActivityUserDto;
import com.linzi.pitpat.data.activityservice.model.dto.UserJoinActivityIdDto;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.vo.PropRankedUserListVo;
import com.linzi.pitpat.data.activityservice.model.vo.RankedUserListVo;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.UnfinishedActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.UserRecentActivityVo;
import com.linzi.pitpat.data.awardservice.model.dto.LastActivityUserDto;
import com.linzi.pitpat.data.entity.dto.ActivityUserStatisticsDto;
import com.linzi.pitpat.data.entity.dto.AppUserActivityInfo;
import com.linzi.pitpat.data.entity.dto.CompeteReportDto;
import com.linzi.pitpat.data.entity.dto.activity.AmountCompensationDto;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.lz.mybatis.plugin.annotations.AS;
import com.lz.mybatis.plugin.annotations.Avg;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.Count;
import com.lz.mybatis.plugin.annotations.CountDistinct;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.IsEmpty;
import com.lz.mybatis.plugin.annotations.IsNotEmpty;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LBracket;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.NE;
import com.lz.mybatis.plugin.annotations.OR;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.RBracket;
import com.lz.mybatis.plugin.annotations.Row;
import com.lz.mybatis.plugin.annotations.Sum;
import com.lz.mybatis.plugin.annotations.Where;
import com.lz.mybatis.plugins.interceptor.annotation.Bean2Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 跑步活动用户表
 *
 * <AUTHOR>
 * @date 2021-12-29 09:58:42
 */
@Mapper
public interface ZnsRunActivityUserDao extends BaseMapper<ZnsRunActivityUserEntity> {

    /**
     * 查询共同组队次数
     *
     * @param userId1
     * @param userId2
     * @return
     */
    Integer getOrganizeTeamNum(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    /**
     * 查询活动用户
     *
     * @param activityId
     * @param targetRunMileage
     * @param targetRunTime
     * @param userId
     * @param activityState
     * @param activityUserStatus
     * @return
     */
    List<RunActivityUserVO> findRunActivityUsers(Page page, @Param("activityId") Long activityId, @Param("activityType") Integer activityType,
                                                 @Param("completeRuleType") Integer completeRuleType, @Param("targetRunMileage") Integer targetRunMileage
            , @Param("targetRunTime") Integer targetRunTime, @Param("userId") Long userId, @Param("activityState") Integer activityState, @Param("activityUserStatus") Integer activityUserStatus);

    /**
     * 已经结束组队跑活动的发起用户
     *
     * @param startDate
     * @return
     */
    List<RunActivityUserVO> findTeamRunActivityUsers(ZonedDateTime startDate);

    /**
     * 已经结束挑战跑活动的胜利用户
     *
     * @param startDate
     * @return
     */
    List<RunActivityUserVO> findChanllengeRunActivityWinnerUsers(ZonedDateTime startDate);

    List<ZnsRunActivityEntity> getMyRecords(Page page, @Param("activityType") Integer activityType, @Param("userId") Long userId);

    Integer getMyRecordCount(@Param("activityType") Integer activityType, @Param("userId") Long userId);

    /**
     * 获取活动队长信息
     *
     * @param activityIds
     * @return
     */
    List<ZnsRunActivityUserEntity> getRecordsCaptainUsers(@Param("activityIds") List<Long> activityIds);

    /**
     * 查询用户历史活动
     *
     * @param userId 用户id
     * @return
     */
    List<RunActivityUserVO> findUserHistoryActivitys(Page page, @Param("userId") Long userId, @Param("activityConfigId") Long activityConfigId);

    List<ZnsRunActivityUserEntity> getInProgressActivityUser(@Param("ids") List<Integer> ids, @Param("selectStartTime") ZonedDateTime selectStartTime, @Param("selectEndTime") ZonedDateTime selectEndTime);

    Integer getActivityType(Long detailId);

    List<Map<String, Object>> getRunActivityType(List<Long> activityIds);

    List<Map<String, Object>> findCompleteActivityUser(Page page, @Param("activityId") Long activityId, @Param("completeRuleType") Integer completeRuleType, @Param("runTime") Integer runTime, @Param("runMileage") BigDecimal runMileage, @Param("neUserId") Long neUserId, @Param("activityType") Integer activityType, @Param("runningGoal") Integer runningGoal);

    ZnsRunActivityUserEntity findNoFinish(@Param("activityId") Long activityId);

    /**
     * 获取参与的官方累计跑活动，下架后24小时内有效
     *
     * @param userId
     * @return
     */
    List<OfficialCumulativeRunDto> getOfficialCumulativeRun(@Param("userId") Long userId);

    void addRunData(@Param("id") Long id, @Param("completeRuleType") Integer completeRuleType, @Param("runTime") Integer runTime, @Param("runMileage") BigDecimal runMileage, @Param("award") BigDecimal award);

    int getParticipationOfficialActivityCount(@Param("userId") Long userId);


    @DataCache({"activityType", "isComplete"})
    List<Map<String, Object>> getRunActivityHistoryUsers(@Param("activityType") Integer activityType, @Param("startTime") String startTime, @Param("isComplete") Integer isComplete);


    //=========================


    /**
     * 查询用户参赛活动信息统计
     */
    AppUserActivityInfo userActivityInfoStatistic(@Param("userId") Long userId);

    /**
     * 查询用户参与的最近一次官方赛事活动
     */
    ZnsRunActivityEntity queryLatestOfficialActivity(@Param("userId") Long userId);

    /**
     * 查询活动用户统计数据
     */
    List<ActivityUserStatisticsDto> activityUserStatistics(@Param("activityIdList") List<Long> activityIdList);


    List<CompeteReportDto> getCompeteReport(@Param("activityId") Long activityId, @Param("completeRuleType") Integer completeRuleType,
                                            @Param("activityType") Integer activityType, @Param("isRealUser") Integer isRealUser);

    List<ActivityOfficialRankingListDto> rankingList(@Param("activityId") Long activityId, @Param("completeRuleType") Integer completeRuleType, @Param("activityType") Integer activityType, @Param("activityState") Integer activityState);


    @Avg("run_time")
    BigDecimal selectByUserIdTargetRunMileage(Long userId, Integer targetRunMileage);


    List<ZnsRunActivityUserEntity> selectByActivityId(Long activityId);


    List<ZnsRunActivityUserEntity> selectByActivityIdIsComplete(Long activityId, Integer isComplete);


    Integer countInvite30ByUserId(Long userId, Integer userType, @IsNotEmpty Long inviterUserId);


    Integer selectByUserIdMulActivityType(@Param("userId") Long userId, @Param("isComplete") Integer isComplete,
                                          @Param("activityType") List<Integer> activityType);


    List<ZnsRunActivityUserEntity> selectByUserIdMulActivityTypeUserType(@Param("userId") Long userId, @Param("isComplete") Integer isComplete,
                                                                         @Param("activityType") List<Integer> activityType, @Param("userType") Integer userType);


    Integer selectByUserIdMulActivityTypeRank(@Param("userId") Long userId, @Param("isComplete") Integer isComplete,
                                              @Param("activityType") List<Integer> activityType, @Param("rank") Integer rank);


    ZnsRunActivityUserEntity selectByUseridActivityId(Long userId, Long activityId);

    Integer getTakeTartInMatchCount(@Param("userId") Long userId);


    List<ZnsRunActivityUserEntity> selectUserByActivityId(Long activityId);

    Integer getCurrentRank(@Param("activityId") Long activityId, @Param("completeRuleType") Integer completeRuleType, @Param("userId") Long userId);

    @LIMIT
    ZnsRunActivityUserEntity selectByActivityIdUserId(Long activityId, Long userId);

    Integer countRunUser(Long activityId, @IN List<Integer> userState, @IF Integer isComplete, @IF @GE Integer runTime);

    @Count
    Integer countByActivityIdIsComplete(Long activityId, Integer isComplete);

    List<UserSimpleVo> findActivitySimpleUser(Page page, @Param("activityId") Long activityId, @Param("activityType") Integer activityType);

    Integer getCurrentRankRealTime(@Param("activityId") Long activityId, @Param("completeRuleType") Integer completeRuleType, @Param("userId") Long userId, @Param("runTime") Integer runTime, @Param("runMileage") BigDecimal runMileage, @Param("modifieTime") ZonedDateTime modifieTime);


    @AS("uad")
    @Mapping(" ifnull(count(*), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    Integer selectByActivityIdTargetRunMileage(@IF @Column("u.is_test") Integer isTest, @IF @Column("u.is_robot") Integer isRobot, @Column("uad.activity_id") Long activityId
            , @IF @Column("uad.target_run_mileage") BigDecimal targetRunMileage, @IF @Column("uad.is_complete") Integer isComplete);


    @AS("uad")
    @Mapping(" uad.* ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    List<ZnsRunActivityUserEntity> selectByActivityIdRankIsTestIsRobot(@Column("uad.activity_id") Long activityId, @IF @Column("uad.target_run_mileage") BigDecimal targetRunMileage,
                                                                       @GE @Column("uad.rank") Integer minRank, @LE @Column("uad.rank") Integer maxRank, @IF @Column("u.is_test") Integer isTest
            , @IF @Column("u.is_robot") Integer isRobot);


    @AS("uad")
    @Mapping(" ifnull(count(*),0)")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    Integer selectByActivityIdUserType(@IF @Column("u.is_test") Integer isTest, @IF @Column("u.is_robot") Integer isRobot, @Column("uad.activity_id") Long activityId, @Column("uad.user_type") Integer userType);


    @Count
    Integer selectCountByUserIdActivityType(Long userId, @IF @IN List<Integer> activityType);

    @Count
    Integer selectCountByUserIdActivityTypeRank(Long userId, @IN List<Integer> activityType, Integer rank);

    @Sum("run_mileage")
    BigDecimal selectSumCountByUserIdActivityType(Long userId, @IN List<Integer> activityType);

    List<ZnsRunActivityUserEntity> findActivityOwnerUsers(List<Long> activityIds);

    List<ZnsRunActivityUserEntity> selectByActivityIdCreateTime(@Param("activityId") Long activityId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<ZnsRunActivityUserEntity> selectRunActivityUser(@IsEmpty Integer activityType);


    @AS("uad")
    @Mapping("ifnull(count(*),0)")
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, as = "u", on = " uad.activity_id = u.id "),
    })
    @Where(" u.activity_state = 2  and ")
    Integer selectByUserIdRankActivityType(@Column("uad.user_id") Long userId, @IF @Column("uad.rank") Integer rank, @Column("u.activity_type") Integer activityType);


    @AS("uad")
    @Mapping("ifnull(count(*),0)")
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, as = "u", on = " uad.activity_id = u.id "),
    })
    @Where(" u.activity_state = 2  and ")
    Integer selectByUserIdRankActivityTypeGt(@Column("uad.user_id") Long userId, @IF @GE @Column("uad.rank") Integer rank, @Column("u.activity_type") Integer activityType);


    @Bean2Map(key = {"activityId", "_", "userId"}, value = "this")
    Map<String, ZnsRunActivityUserEntity> selectByZnsRunActivityEntity(@IN @Row("id") List<ZnsRunActivityEntity> activityId);


    Integer countAwardUser(@Param("activityId") Long activityId, @Param("isRealUser") Integer isRealUser);

    ZnsRunActivityUserEntity getEnrollPayActivityCount(@Param("userId") Long userId, @Param("activityType") Integer activityType, @Param("taskId") Long taskId, @Param("isTest") Integer isTest);


    @LIMIT
    ZnsRunActivityUserEntity selectByActivityIdIdUserTypePerson(Long activityId, Integer userType);


    @AS("uad")
    @Mapping(" uad.* ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @LIMIT
    ZnsRunActivityUserEntity selectByActivityHasRotbotReposrt(@Column("uad.activity_id") Long activityId, @Column("u.is_robot") Integer isRobot);


    @Bean2Map(key = {"activityId"}, value = "this")
    Map<Long, List<ZnsRunActivityUserEntity>> selectByZnsRunActivityEntityList(@IN @Row("id") List<ZnsRunActivityEntity> activityId);


    @Bean2Map(key = {"activityId"}, value = "this")
    Map<Long, List<ZnsRunActivityUserEntity>> selectByZnsRunActivityEntityXX(@IN List<Long> activityId);


    @Bean2Map(key = {"activityId"}, value = "this")
    @DataCache("activityId")
    Map<Long, ZnsRunActivityUserEntity> selectByZnsRunActivityEntityXXCC(@IN List<Long> activityId);


    @CountDistinct(ZnsRunActivityUserEntity.activity_type)
    Integer selectActivityTypeCount(@Column(ZnsRunActivityUserEntity.create_time) @GE ZonedDateTime monthStart,
                                    @Column(ZnsRunActivityUserEntity.create_time) @LE ZonedDateTime monthEnd,
                                    Long userId, @NE Integer activityType);


    @Count(ZnsRunActivityUserEntity.all)
    Integer selectGuanfangMulCompileCount(@Column(ZnsRunActivityUserEntity.create_time) @GE ZonedDateTime monthStart,
                                          @Column(ZnsRunActivityUserEntity.create_time) @LE ZonedDateTime monthEnd,
                                          Long userId,
                                          Integer activityType,
                                          Integer isComplete
    );

    int updateUserStateById(Integer userState, ZonedDateTime modifieTime, Long id);


    @Mapping(ZnsRunActivityUserEntity.all)
    @LeftJoinOns(
            @Item(value = ZnsRunActivityEntity.class, left = ZnsRunActivityUserEntity.activity_id, right = ZnsRunActivityEntity.id_)
    )
    List<ZnsRunActivityUserEntity> selectByTaskIds(@Column(ZnsRunActivityUserEntity.user_id) Long userId,
                                                   @Column(ZnsRunActivityUserEntity.task_id) @IN @Row("id") List<RunActivityUserTask> taskId,
                                                   @Column(ZnsRunActivityUserEntity.user_state) @IN List<Integer> userState,
                                                   @Column(ZnsRunActivityEntity.activity_state) @IN List<Integer> activityState,
                                                   @Column(ZnsRunActivityEntity.status_) Integer status,
                                                   @Column(ZnsRunActivityEntity.activity_type) @LBracket @IN List<Integer> activityType,
                                                   @Column(ZnsRunActivityEntity.activity_type) @OR @LBracket @IN List<Integer> activityType1,
                                                   @Column(ZnsRunActivityEntity.activity_start_time) @RBracket(2) @GE ZonedDateTime startTime);


    ZnsRunActivityUserEntity selectRunActivityUserByNQActivityTypeRank(@Param("userId") Long userId, @Param("activityType") Integer activityType, @Param("rank") Integer rank);

    @OrderByIdDescLimit_1
    ZnsRunActivityUserEntity selectRunActivityUserByNQActivityTypeRankOld(Long userId, Integer activityType, Integer rank, @IsEmpty Long taskId);

    List<Long> queryActivityByUserId30Days(@Param("userId") Long userId);


    Integer findActivityUserCountByUserId(@Param("userId") Long userId, @Param("isComplete") Integer isComplete, @Param("userState") List<Integer> userState, @Param("activityTypes") List<Integer> activityTypes, @Param("startOfDate") ZonedDateTime startOfDate, @Param("endOfDate") ZonedDateTime endOfDate, @Param("runWalkStatus") Integer runWalkStatus);

    List<LastActivityUserDto> selectLastActivityUser(@Param("userId") Long userId,
                                                     @Param("activityType") Integer activityType,
                                                     @Param("subType") Integer subType, @Param("limit") Integer limit,
                                                     @Param("activityId") Long activityId);


    @Select("SELECT u.id from (" +
            "SELECT * from zns_run_activity_user WHERE activity_id=" +
            "           (SELECT activity_id from zns_run_activity_user WHERE user_id = #{userId} and is_delete = 0 order by create_time desc limit 1)and is_delete = 0 ) \n" +
            "as au" +
            " left join" +
            " zns_user as u on au.user_id = u.id  WHERE u.is_robot = 1 and u.is_delete =0")
    List<Long> getAllActivityBotUserByUserId(Long userId);


    List<AmountCompensationDto> amountCompensation(Page page);

    Integer selectCompleteNum(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    Integer selectTeamCompleteNum(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    Integer selectGoalCompleteNum(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);


    Integer updateUserRunAwardAmountById(@Param("id") Long id, @Param("sendAmount") BigDecimal sendAmount);

    Integer findNewActivityUserCountByUserId(@Param("userId") Long userId, @Param("startOfDate") ZonedDateTime startOfDate, @Param("endOfDate") ZonedDateTime endOfDate, @Param("activityTypes") List<Integer> activityTypes);


    List<RunActivityUserDto> findActivityUserByQuery(@Param("param") RunActivityUserQuery param);

    //查询用户该赛事玩法报名次数
    Integer findUserGameSignUpCount(@Param("userId") Long userId, @Param("gameId") Long gameId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    //查询用户当日参赛次数，活动有人完成才算参赛
    Integer findDailyCountByUserAndActivityType(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("activityType") Integer activityType, @Param("activityTypeSub") Integer activityTypeSub);

    Integer findDailyPartnerCountByUserAndActivityType(@Param("userId") Long userId, @Param("matchUserId") Long matchUserId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("activityType") Integer activityType, @Param("activityTypeSub") Integer activityTypeSub);

    /**
     * 获取段位赛排名
     *
     * @param activityId
     * @return
     */
    List<RankedUserListVo> findRankedUserListVoList(@Param("activityId") Long activityId);

    List<UnfinishedActivityVo> getUnfinishedActivities(@Param("userId") Long userId);

    ZnsRunActivityUserEntity getUserLatestRateLimitActivity(Long userId, ZonedDateTime dataTime);


    ZnsRunActivityUserEntity getUserLatestActivityByTargetType(@Param("userId") Long userId, @Param("targetType") Integer targetType, @Param("dataTime") ZonedDateTime dataTime);

    /**
     * 最近同赛
     *
     * @param userId
     * @return
     */
    List<Long> queryPlayedTogetherRecentlyStranger(@Param("userId") Long userId, @Param("limit") int limit);


    /**
     * 获取用户官方赛完赛次数
     *
     * @param userId
     */
    Integer getUserOfficialActivityNum(Long userId);

    /**
     * 获取用户主题赛完赛次数
     *
     * @param userId
     */
    Integer getUserThemeActivityNum(Long userId);

    /**
     * 获取用户团队赛完赛次数
     *
     * @param userId
     */
    Integer getUserTeamActivityNum(Long userId);

    /**
     * PK赛 完赛次数
     *
     * @param userId
     * @param activityType    赛事类型
     * @param activityTypeSub 子赛事类型
     * @return
     */
    Integer getUserPkActivityNum(Long userId, Integer activityType, Integer activityTypeSub);

    /**
     * 获取用户指定赛事完赛次数
     *
     * @param userId
     * @param activityType 赛事类型
     * @see RunActivityTypeEnum
     */
    Integer getUserActivityNum(Long userId, Integer activityType);

    /**
     * 根据活动分类查询 3.0新赛事完赛次数
     *
     * @param userId
     * @param categoryType 分类
     * @see com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants.CategoryTypeEnum
     */
    Integer getUserNewActivityNumByCategoryType(Long userId, Integer categoryType);


    UserJoinActivityIdDto findUserLastJoinActivity(@Param("userId") Long userId,
                                                   @Param("categoryType") Integer categoryType,
                                                   @Param("mainTypes") List<String> mainTypes);


    List<UserRecentActivityVo> queryEnrollActivity(Long userId, int limit);

    List<UserRecentActivityVo> queryObtainedRankActivity(Long userId, int limit);

    List<Long> selectSubActivityIdsByUserId(@Param("userId") Long userId);

    ZnsRunActivityEntity getUserLatestPkActivityByUserId(@Param("userId") Long userId);

    List<PropRankedUserListVo> findPropUserListVoList(@Param("activityId") Long activityId);

    /**
     * 根据类型查询用户已完成的活动
     *
     * @param userId
     * @param activityType
     * @param activitySubTyp
     */
    List<ZnsRunActivityUserEntity> findUserCompleteActivityByType(@Param("userId") Long userId, @Param("activityType") Integer activityType, @Param("activitySubType") List<Integer> activitySubType);

    Long findFirstCrossCheckId(@Param("date") ZonedDateTime date);

    List<Long> loadCrossCheckId(@Param("id") Serializable lastId, @Param("limit") Integer limit);

    ZnsRunActivityUserEntity findRawById(@Param("id") Long id, @Param("userId") Long userId);

    Integer findCompanionCount(@Param("userId") Long userId, @Param("minCompleteTime") ZonedDateTime minCompleteTime, @Param("taskType") Integer taskType);

    Long countActivityUserWithOutRobot(@Param("activityIds") Set<Long> calActivityIds);

    Integer countServiceUserNum(@Param("userId") Long userId, @Param("beginTime") ZonedDateTime beginTime);


    List<Long> findTopInRankByActivityIdAndSeasonId(@Param("mainActivityId") Long mainActivityId, @Param("seasonId") Long seasonId, @Param("size") Integer limit);

    List<Long> findTopInRankByActivityId(@Param("mainActivityId") Long mainActivityId, @Param("size") Integer limit);

    Integer findCurrentRank(@Param("activityId") Long activityId, @Param("userId") Long userId, @Param("orderStr") String orderStr);

    @Select("select user_id from zns_run_activity_user where activity_id = #{mainActivityId} and is_delete = 0")
    List<Long> findAllEnrollUserId(Long mainActivityId);
}
