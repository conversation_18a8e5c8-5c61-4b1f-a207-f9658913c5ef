package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * <p>
 * 菜单权限表
 * </p>*用户勋章
 *
 * <AUTHOR>
 * @since 2022-06-23
 */

@Data
@NoArgsConstructor
@TableName("zns_user_medal")
public class UserMedal implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //用户id
    private Long userId;
    //枚举:ABILITY,1,实力勋章:TAKE,2,参加经历:GOLD,3,冠军之路:SUM_MILEAGE,4,累计里程:SUM_REWARD,5,累计奖金:SURPRISED,6,惊喜
    private Integer type;
    //勋章名称
    private String name;
    //等级
    private Integer level;
    //处理器
    private String handler;
    //第一个参数
    private String param1;
    //第二个参数
    private String param2;
    //第三个参数
    private String param3;
    //第四个参数
    private String param4;
    //第五个参数
    private String param5;
    //描述
    private String remark;
    //有效天数
    private Integer validDays;
    //目标进度1
    private BigDecimal targetProcess1;
    //当前进度1
    private BigDecimal currentProcess1;
    //历史进度1
    private BigDecimal historyProcess1;
    //目标进度2
    private BigDecimal targetProcess2;
    //当前进度2
    private BigDecimal currentProcess2;
    //历史进度2
    private BigDecimal historyProcess2;
    //进度条数
    private Integer processNum;
    //是否已经获得，0 未获得，1 已经获得
    private Integer obtain;
    //开始有效时间
    private ZonedDateTime validStartTime;
    //结束有效时间
    private ZonedDateTime validEndTime;
    //获得时间
    private ZonedDateTime obtainTime;
    //是否失效 ,1 已经失效 ， 0 没有失效
    private Integer isValid;
    //配置id
    private Long medalConfigId;
    //是否弹窗
    private Integer isPop;
    //是否结束时触发 0 不是， 1 是
    private Integer endTrigger;
    //是否已经展示，0 未展示 ， 1 已经展示动画
    private Integer isShow;
    //是否展示在首页 0 不在首页展示， 1 在首页展示
    private Integer isShowHomePage;
    //位置
    private Integer pos;

    /**
     * 枚举:ABILITY,1,实力勋章:TAKE,2,参加经历:GOLD,3,冠军之路:SUM_MILEAGE,4,累计里程:SUM_REWARD,5,累计奖金:SURPRISED,6,惊喜
     */
    public enum TYPE_ENUM {
        ABILITY(1, "实力勋章"),
        TAKE(2, "参加经历"),
        GOLD(3, "冠军之路"),
        SUM_MILEAGE(4, "累计里程"),
        SUM_REWARD(5, "累计奖金"),
        SURPRISED(6, "惊喜");


        private Integer code;

        private String desc;

        TYPE_ENUM(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserMedal userMedal = (UserMedal) o;
        return Objects.equals(targetProcess1, userMedal.targetProcess1) && Objects.equals(currentProcess1, userMedal.currentProcess1) && Objects.equals(historyProcess1, userMedal.historyProcess1) && Objects.equals(targetProcess2, userMedal.targetProcess2) && Objects.equals(currentProcess2, userMedal.currentProcess2) && Objects.equals(historyProcess2, userMedal.historyProcess2) && Objects.equals(processNum, userMedal.processNum) && Objects.equals(obtain, userMedal.obtain) && Objects.equals(validStartTime, userMedal.validStartTime) && Objects.equals(validEndTime, userMedal.validEndTime) && Objects.equals(obtainTime, userMedal.obtainTime) && Objects.equals(isValid, userMedal.isValid) && Objects.equals(medalConfigId, userMedal.medalConfigId) && Objects.equals(isPop, userMedal.isPop) && Objects.equals(endTrigger, userMedal.endTrigger) && Objects.equals(isShow, userMedal.isShow) && Objects.equals(isShowHomePage, userMedal.isShowHomePage) && Objects.equals(pos, userMedal.pos);
    }

    @Override
    public int hashCode() {
        return Objects.hash(targetProcess1, currentProcess1, historyProcess1, targetProcess2, currentProcess2, historyProcess2, processNum, obtain, validStartTime, validEndTime, obtainTime, isValid, medalConfigId, isPop, endTrigger, isShow, isShowHomePage, pos);
    }

    @Override
    public String toString() {
        return "UserMedal{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",userId=" + userId +
                ",type=" + type +
                ",name=" + name +
                ",level=" + level +
                ",handler=" + handler +
                ",validDays=" + validDays +
                ",processNum=" + processNum +
                ",obtain=" + obtain +
                ",obtainTime=" + obtainTime +
                ",isValid=" + isValid +
                ",medalConfigId=" + medalConfigId +
                ",isPop=" + isPop +
                ",endTrigger=" + endTrigger +
                ",isShow=" + isShow +
                ",isShowHomePage=" + isShowHomePage +
                ",pos=" + pos +
                "}";
    }
}
