package com.linzi.pitpat.data.activityservice.service;
/**
 * <p>
 * 用户活动任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */

import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.awardservice.model.vo.FestivalActivityWalletVo;
import com.linzi.pitpat.data.resp.OneWeekHomeDetailDto;
import com.lz.mybatis.plugin.annotations.NE;

import java.time.ZonedDateTime;
import java.util.List;

public interface RunActivityUserTaskService {


    RunActivityUserTask selectRunActivityUserTaskById(Long id);

    List<RunActivityUserTask> getUserTasks(Long userId, Integer activityType);

    void addUserTasks(Long userId, String activityConfig, Long activityId, Integer activityType);

    RunActivityUserTask selectRunActivityUserTaskByLevel(Long userId, Integer level, Integer activityType);

    RunActivityUserTask selectRunActivityUserTaskByTime(Long userId, ZonedDateTime date, Integer activityType);

    String getRunningMode(Integer robotRunningMode);

    /**
     * 重置任务状态
     * @param id
     */
    void resetStatus(Long id);


    List<RunActivityUserTask> selectRunActivityUserTaskByActivityId(Long activityId, Long userId, Integer awardStatus);

    /**
     * 处理运营节目活动
     * @param userTask
     * @param taskActivityUser
     */
    void dealUserTask(RunActivityUserTask userTask, ZnsRunActivityUserEntity taskActivityUser);


    List<RunActivityUserTask> selectRunActivityUserTaskByActivityType(Integer activityType, Long userId);

    RunActivityUserTask selectRunActivityUserTaskByLevel(Long userId, int level, Long activityId);

    List<RunActivityUserTask> selectRunActivityUserTaskByActivityIdUserId(Long activityId, Long userId);

    RunActivityUserTask selectRunActivityUserTaskByActivityIdNextLevel(Long id, Long id1);

    /**
     * 获取马拉松完赛次数
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    Integer getThemeCompleteNum(Long userId, ZonedDateTime startTime, ZonedDateTime endTime);

    RunActivityUserTask selectByDetailId(Long runDataDetailsId);

    RunActivityUserTask selectRunActivityUserTaskOneByActivityId(Long activityId, Long userId);

    Long insertOrUpdateRunActivityUserTask(RunActivityUserTask runActivityUserTask);

    List<OneWeekHomeDetailDto> selectRunDataByActivityIdUserId(Long activityId, Long userId);

    RunActivityUserTask selectRunActivityUserTaskByActivityIdUserIdLevel(Long activityId, Long userId, Integer level);

    void updateRunActivityUserTaskIsUnlockById(Integer isUnlock, Long id);

    FestivalActivityWalletVo selectFestivalActivityWallet(Long userId, Long activityId, Integer activityType, @NE Integer awardStatus);

    List<RunActivityUserTask> selectRunActivityUserTaskByUserIdAndTypeStatus(Long userId, Integer activityType, Integer status, Integer awardStatus);


    void update(RunActivityUserTask runActivityUserTask);

    RunActivityUserTask findById(Long taskId);

    RunActivityUserTask getNextTask(RunActivityUserTask task);

    List<RunActivityUserTask> findStartListByTime(ZonedDateTime startTime, ZonedDateTime endTime);

    void save(RunActivityUserTask task);

    RunActivityUserTask findNextTask(Long userId, Integer activityType, Long id);

    void cancle(Long id);

    long countByIdAndActivityType(Long taskId, Integer type);
}
