package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ActivityAwardReviewAdminDto extends ActivityAwardReviewDto {
    //审核人
    private String reviewUserName;
    // 审核时间
    private ZonedDateTime reviewTime;

    private String surpassUserCode;
    // 比赛成绩m/s/时间戳/m/s
    private String raceResult;
    // 成绩类型 1：里程，2：时长，3：完赛时间 4：平均配速
    private String raceResultType;
    /**
     * 排名奖励发送类型 0 默认发放 1 手工发放，人工审核
     */
    private Integer awardSendType = 0;
    /**
     * 完赛金额发送类型 0 默认发放 1 手工发放，人工审核
     */
    private Integer completeAwardSendType = 0;
    /**
     * 超越金额发送类型 0 默认发放 1 手工发放，人工审核
     */
    private Integer surpassAwardSendType = 0;
    /**
     * 是否赛事有阶段 0 不是 1是
     */
    private Integer isStageActivity = 0;

    //视频审核开启1 不开启0
    private Integer enableVideo = 0;


}
