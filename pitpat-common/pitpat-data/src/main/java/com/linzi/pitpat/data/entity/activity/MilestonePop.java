package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-07-14
 */

@Data
@NoArgsConstructor
@TableName("zns_milestone_pop")
public class MilestonePop implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.activity.MilestonePop:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                        // 主键id
    public final static String user_id = CLASS_NAME + "user_id";               // 用户id
    public final static String activity_id = CLASS_NAME + "activity_id";       // 活动id
    public final static String mileage_node_ = CLASS_NAME + "mileage_node";    // 里程碑节点
    public final static String is_delete = CLASS_NAME + "is_delete";           // 是否删除1 -删除 0-未失效
    public final static String gmt_create = CLASS_NAME + "gmt_create";         // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";     // 修改时间
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    //用户id
    private Long userId;
    //活动id
    private Long activityId;
    //里程碑节点
    private Integer mileageNode;
    //是否删除1 -删除 0-未失效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;

    @Override
    public String toString() {
        return "MilestonePop{" +
                ",id=" + id +
                ",userId=" + userId +
                ",activityId=" + activityId +
                ",mileage node=" + mileageNode +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                "}";
    }
}
