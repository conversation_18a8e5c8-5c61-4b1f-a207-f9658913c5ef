package com.linzi.pitpat.data.activityservice.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@Data
public class RoomStatusDto {
    public long RoomID;

    /// <summary>
    /// 游戏模式
    /// </summary>
    public int GameMode ;

    /// <summary>
    /// 房主
    /// </summary>
    public long Master ;

    /// <summary>
    /// 0 待机 1开始
    /// </summary>
    public int State;

    /// <summary>
    /// 房间内玩家id列表
    /// </summary>
    public List<Long> Users ;
}
