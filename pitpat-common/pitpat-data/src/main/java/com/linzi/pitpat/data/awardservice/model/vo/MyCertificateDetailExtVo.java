package com.linzi.pitpat.data.awardservice.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 证书明细页面数据
 */
@Data
@NoArgsConstructor
public class MyCertificateDetailExtVo extends MyCertificateDetailVo {

    /**
     * 昵称
     */
    private String nickName;
    /**
     * 运动时间(s)
     */
    private Integer runTime;
    /**
     * 运动里程
     */
    private BigDecimal runMileage;
    /**
     * 平均速度(公里/小时)
     */
    private BigDecimal averageVelocity;
    /**
     * 平均速度(m/小时)
     */
    private Integer averageVelocityInt;
    /**
     * 平均配速(秒/公里)
     */
    private Integer averagePace;
    /**
     * 比赛日
     */
    private ZonedDateTime activityDate;
    /**
     * 证书编号
     */
    private String certificateNo;
    /**
     * 品牌 deerRun / superRun
     */
    private String brand;
    /**
     * 品牌logo url 图片
     */
    private String brandLogoUrl;
    /**
     * 证书固定文案
     */
    private String certificateContent;
    /**
     * 证书类型 1名次证书 2 品牌完赛证书
     */
    private Integer certificateType;


}
