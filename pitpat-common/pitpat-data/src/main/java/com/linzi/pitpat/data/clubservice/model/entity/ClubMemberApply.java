package com.linzi.pitpat.data.clubservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_club_member_apply")
public class ClubMemberApply implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否逻辑删除，0：未删除
    @TableLogic(delval = "UNIX_TIMESTAMP()")
    private Long isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;
    //加入的俱乐部
    private Long clubId;
    //申请人id
    private Long applyUserId;
    //审批人id
    private Long auditUserId;
    //申请时间
    private ZonedDateTime applyDate;
    //加入原因编码
    private String applyReason;
    //邀请码
    private String applyCode;
    //申请状态
    private String state;
    /**
     * 过期时间
     */
    private ZonedDateTime expiryDate;
}
