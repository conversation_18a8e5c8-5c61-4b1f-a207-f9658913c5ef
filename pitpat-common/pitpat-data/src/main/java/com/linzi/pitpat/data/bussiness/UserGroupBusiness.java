package com.linzi.pitpat.data.bussiness;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.constants.LabelConstant;
import com.linzi.pitpat.data.entity.dto.GroupUserDto;
import com.linzi.pitpat.data.systemservice.model.entity.OperationalPlanEntity;
import com.linzi.pitpat.data.systemservice.model.entity.OperationalPlanUserGroupRel;
import com.linzi.pitpat.data.systemservice.service.OperationalPlanService;
import com.linzi.pitpat.data.systemservice.service.OperationalPlanUserGroupRelService;
import com.linzi.pitpat.data.userservice.dto.request.GroupPlanListReq;
import com.linzi.pitpat.data.userservice.dto.response.PlanResponseDto;
import com.linzi.pitpat.data.userservice.dto.response.UserGroupPlanRelPage;
import com.linzi.pitpat.data.userservice.enums.OperationalConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.label.LabelUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.label.UserGroupEntity;
import com.linzi.pitpat.data.userservice.model.entity.label.UserGroupRelEntity;
import com.linzi.pitpat.data.userservice.model.entity.label.UserGroupRelLogEntity;
import com.linzi.pitpat.data.userservice.model.query.UserGroupRelQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.LabelUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelLogService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserGroupBusiness {
    @Autowired
    private UserGroupService userGroupService;
    @Autowired
    private UserGroupRelService userGroupRelService;
    @Autowired
    private UserGroupRelLogService logService;
    @Autowired
    private ZnsUserService userService;
    @Autowired
    private LabelUserService labelUserService;
    @Autowired
    private OperationalPlanService operationalPlanService;
    @Autowired
    private OperationalPlanUserGroupRelService operationalPlanUserGroupRelService;


    public List<GroupUserDto> getUserByGroupId(Long id) {
        return userGroupService.getUserByGroupId(id);
    }


    public void importGroupUser(List<GroupUserDto> list, Long groupId) {
        UserGroupEntity group = userGroupService.findById(groupId);
        List<ZnsUserEntity> users = getUserByDtos(list);

    }

    public Integer updateUserGroup(Long groupId) {
        UserGroupEntity group = userGroupService.findById(groupId);
        group.setGmtCreate(null);
        //更新group计算状态
        group.setComputeStatus(1);
        userGroupService.update(group);

        List<UserGroupRelEntity> groupRels = new ArrayList<>();

        //开始统计数据
        try {
            String relLables = group.getRelLables();
            String[] lineIds = relLables.split(",");
            if (lineIds.length == 0) {
                return 2;
            }

            //获取分群用户
            Set<Long> labelUsers = getGroupUserList(List.of(lineIds), group.getLineRel());

            //模型转换
            groupRels = labelUsers.stream().distinct().map(k -> {
                UserGroupRelEntity groupRel = new UserGroupRelEntity();
                groupRel.setGroupId(groupId);
                groupRel.setUserId(k);
                groupRel.setGroupName(group.getGroupName());
                return groupRel;
            }).collect(Collectors.toList());

            //开启自动删除
            if (group.getAutoDelete() == 0) {
                userGroupRelService.deleteByGroup(groupId);
                if (groupRels.size() > 0) {
                    userGroupRelService.saveBatch(groupRels);
                }

            } else {
                for (UserGroupRelEntity groupRel : groupRels) {
                    List<UserGroupRelEntity> userAndGroup = userGroupRelService.getByUserAndGroup(groupRel.getUserId(), groupRel.getGroupId());
                    if (!CollectionUtils.isEmpty(userAndGroup)) {
                        continue;
                    }
                    userGroupRelService.insert(groupRel);
                }
            }
            //更新group标识
            group.setComputeStatus(2);
            //兼容版本升级
            Long num = userGroupRelService.findCountByGroupId(group.getId());
            group.setNum(Math.toIntExact(num));
            group.setUpdateTime(ZonedDateTime.now());
            userGroupService.update(group);
        } catch (Exception e) {
            log.error("groupId{}计算失败", groupId, e);
            //更新group标识
            group.setComputeStatus(3);
            userGroupService.update(group);
        }
        if (group.getComputeStatus() == 2) {
            //更新log
            int version = logService.getMaxVersionByGroup(groupId) != null ? logService.getMaxVersionByGroup(groupId) : 0;
            List<UserGroupRelEntity> userAndGroupRels = userGroupRelService.getByUserAndGroup(null, groupId);

            List<UserGroupRelLogEntity> relLogs = userAndGroupRels.stream().map(k -> {
                UserGroupRelLogEntity relLog = new UserGroupRelLogEntity();
                BeanUtils.copyProperties(k, relLog);
                relLog.setVersion(version + 1);
                relLog.setId(null);
                return relLog;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(relLogs)) {
                //保存记录
                logService.saveBatch(relLogs);
                //删除3个版本以前的记录
                int delVersion = version - 2;
                if (delVersion > 0) {
                    logService.deleteByVersionAndGroupId(groupId, delVersion);
                }
            }

        }
        return group.getComputeStatus();
    }

    /**
     * 获取分群用户
     *
     * @param lineIds 分层id
     * @param lineRel 分层关系：and、or
     * @return
     */
    private Set<Long> getGroupUserList(List<String> lineIds, String lineRel) {
        List<LabelUserEntity> allLabelUsers = labelUserService.findListByLineIds(lineIds);
        if (CollectionUtils.isEmpty(allLabelUsers)) {
            return new HashSet<>();
        }
        Map<Long, List<LabelUserEntity>> labelUserMap = allLabelUsers.stream().collect(Collectors.groupingBy(LabelUserEntity::getLineId));
        if (labelUserMap.size() < lineIds.size() && LabelConstant.RULE_REL_AND.equals(lineRel)) {
            //and的关系 并且 存在没有用户的分层，直接返回没有用户（ 1 and 0 = 0）
            return new HashSet<>();
        }
        Set<Long> labelUsers = null;
        for (Map.Entry<Long, List<LabelUserEntity>> entry : labelUserMap.entrySet()) {
            List<LabelUserEntity> list = entry.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            if (labelUsers == null) {
                labelUsers = list.stream().map(LabelUserEntity::getUserId).collect(Collectors.toSet());
            } else {
                Set<Long> userIds = list.stream().map(LabelUserEntity::getUserId).collect(Collectors.toSet());
                if (LabelConstant.RULE_REL_OR.equals(lineRel)) {
                    //合集
                    labelUsers.addAll(userIds);
                } else {
                    //交集
                    labelUsers.retainAll(userIds);
                }
            }
        }
        return Optional.ofNullable(labelUsers).orElse(new HashSet<>());
    }

    private List<ZnsUserEntity> getUserByDtos(List<GroupUserDto> list) {
        List<String> emails = list.stream().map(GroupUserDto::getEmailAddress).collect(Collectors.toList());
        List<ZnsUserEntity> users = userService.selectUserByEmails(emails);
        return users;
    }

    public UserGroupRelEntity userToRel(ZnsUserEntity user, UserGroupEntity group) {
        UserGroupRelEntity rel = new UserGroupRelEntity();
        rel.setGroupId(group.getId());
        rel.setUserId(user.getId());
        rel.setGroupName(group.getGroupName());

        return rel;

    }

    public List<UserGroupRelEntity> getUserGroupRel(Long userId, List<Long> groupIds) {
        UserGroupRelQuery query = UserGroupRelQuery.builder().userId(userId).groupIds(groupIds).build();
        return userGroupRelService.findList(query);
    }

    /**
     * 用户分群下的运营计划列表
     *
     * @param req
     */
    public UserGroupPlanRelPage<PlanResponseDto> groupPlanList(GroupPlanListReq req) {
        UserGroupPlanRelPage<PlanResponseDto> result = new UserGroupPlanRelPage<>(req.getPageNum(), req.getPageSize());
        //查询分群下的所有运营计划
        List<OperationalPlanUserGroupRel> list = operationalPlanUserGroupRelService.findListByUserGroupIds(List.of(req.getUserGroupId()));
        if (CollectionUtils.isEmpty(list)) {
            result.setGoingNum(0L);
            result.setRecords(new ArrayList<>());
            return result;
        }
        List<Long> planIds = list.stream().map(OperationalPlanUserGroupRel::getOperationalPlanId).distinct().toList();
        result = (UserGroupPlanRelPage<PlanResponseDto>) operationalPlanService.findPageByPlanIds(result, planIds);

        //计算计划状态
        for (PlanResponseDto record : result.getRecords()) {
            OperationalPlanEntity planEntity = new OperationalPlanEntity();
            planEntity.setEndTime(record.getEndTime());
            planEntity.setState(record.getState());
            planEntity.setExecuteType(record.getExecuteType());
            if (OperationalConstant.ExecuteTypeEnum.MANUAL.getCode().equals(record.getExecuteType())) {
                //手动计划的开始时间要加上 时分
                ZonedDateTime startTime = DateUtil.addHours(record.getStartTime(), record.getHour());
                startTime = startTime.plusMinutes(record.getMinute());
                planEntity.setStartTime(startTime);
                record.setStartTime(startTime);
            } else {
                planEntity.setStartTime(record.getStartTime());
            }
            //计算状态
            Integer listState = operationalPlanService.getListState(planEntity);
            record.setListState(listState);
        }

        //查询进行中运营计划数量
        Long goingNum = operationalPlanService.findGoingPlanByIds(planIds);
        result.setGoingNum(goingNum); //进行中的数量
        result.setUseNum(planIds.size()); //被引用的数量
        return result;
    }
}
