package com.linzi.pitpat.data.awardservice.constant.enums;

import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

public enum AwardTypeEnum {

    //1：金额，2：券，3：积分 4:皮肤 5:会员奖励 6:经验奖励 4里面:游戏动作
    //金额奖励
    AMOUNT(1, "amount", "amount award", "金额奖励"),
    //劵奖励
    COUPON(2, "coupon", "coupon award", "劵奖励"),
    SCORE(3, "score", "score award", "积分奖励"),
    WEAR(4, "wear", "wear award", "皮肤奖励"),
    MEMBER(5, "member", "wear award", "会员奖励"),
    EXP(6, "exp", "wear award", "经验奖励"),
    MEDAL(7, "medal", "medal award", "勋章奖励"),
    //抵扣金抽奖
    DEDUCTION(8, "deduction", "deduction award", "抵扣金抽奖"),
    //turbolink 抽奖
    TURBOLINK_DRAW(9, "turbolinkdraw", "turbolink draw", "turbolink抽奖"),

    VIRTUAL_DEVICE(10, "virtual_device", "virtual_device award", "虚拟设备"),
//    ACTIVITY_TYPE(11, "activity_type", "activity type", "活动类型解锁"),
    ;
    private Integer type;

    private String code;
    private String name;
    private String znName;

    public static String getValueStr(AwardConfigDto awardConfigDto) {
        String valueStr = "";
        if (awardConfigDto == null) {
            return valueStr;
        }
        AwardTypeEnum awardTypeEnum = findByType(awardConfigDto.getAwardType());
        switch (awardTypeEnum) {
            case AMOUNT:
                valueStr = Objects.nonNull(awardConfigDto.getAmount())&&awardConfigDto.getAmount().compareTo(BigDecimal.ZERO)>0? "$" + awardConfigDto.getAmount():"";
                break;
            case SCORE:
                valueStr = Objects.nonNull(awardConfigDto.getScore())&&awardConfigDto.getScore()>0?awardConfigDto.getScore() + "Point":"";
                break;
            case WEAR:
                valueStr = awardConfigDto.getWearName();
                break;
            case MEDAL:
                valueStr = awardConfigDto.getMedalName();
                break;
            default:
                valueStr = "";
        }
        return valueStr;
    }

    public Integer getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getZnName() {
        return znName;
    }

    AwardTypeEnum(Integer type, String code, String name, String znName) {
        this.type = type;
        this.code = code;
        this.name = name;
        this.znName = znName;
    }

    public static AwardTypeEnum findByType(Integer type) {
        return Arrays.stream(AwardTypeEnum.values()).filter(e -> e.type.equals(type)).findFirst().orElse(null);
    }

    public static AwardTypeEnum findByCode(String code) {
        return Arrays.stream(AwardTypeEnum.values()).filter(e -> e.code.equals(code)).findFirst().orElse(null);
    }
}
