package com.linzi.pitpat.data.awardservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.linzi.pitpat.lang.BaseDo;




/**
 * 奖励配置勋章表 DO对象
 *
 * @since 2025年7月3日
 */
@Data
@Accessors(chain = true)
@TableName("zns_award_config_medal")
public class AwardConfigMedalDo extends BaseDo   {

     // 主键id
     @TableId(value ="id", type = IdType.AUTO)
     private Long id;
     // 配置ID，关联zns_award_config
     private Long awardConfigId;
     // 勋章id
     private Long medalId;
}