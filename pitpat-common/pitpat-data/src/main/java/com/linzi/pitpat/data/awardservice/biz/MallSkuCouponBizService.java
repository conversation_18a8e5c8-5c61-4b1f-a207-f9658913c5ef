package com.linzi.pitpat.data.awardservice.biz;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCouponConfigQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.query.CouponQuery;
import com.linzi.pitpat.data.awardservice.model.vo.SkuCouponAmountVo;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderPickCouponDto;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsAmountVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商城sku优惠券Biz
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MallSkuCouponBizService {

    private final CouponService couponService;
    private final CouponCurrencyService couponCurrencyService;
    private final UserCouponService userCouponService;
    private final ActivityCouponConfigService couponConfigService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final MallCouponComponent mallCouponComponent;
    private final MallCouponConvertComponent mallCouponConvertComponent;

    /**
     * 查询sku优惠券列表
     */
    public List<OrderPickCouponDto> findSkuCouponList(Long skuId, Integer count, ZnsUserEntity user) {
        List<OrderPickCouponDto> result = new ArrayList<>();
        if (user.getAppVersion() < Constant.appVersion_4044) {
            return result;
        }
        ZnsGoodsSkuEntity znsGoodsSkuEntity = znsGoodsSkuService.findById(skuId);
        ZnsGoodsEntity goodsEntity = znsGoodsService.findById(znsGoodsSkuEntity.getGoodsId());
        List<Coupon> couponList = findAllCouponBySku(List.of(skuId), false, goodsEntity.getCountryCode());
        if (CollectionUtils.isEmpty(couponList)) {
            return result;
        }

        //按门槛排序（门槛较低的在前；相同门槛，折扣在前，优惠金额大的在前（折扣顺序，金额倒序），最后按id倒序）
        couponList = mallCouponComponent.couponSort(couponList);
        Map<Long, Coupon> couponMap = couponList.stream().collect(Collectors.toMap(Coupon::getId, Function.identity()));

        //转换格式
        List<MallCouponDto> mallCouponDtos = mallCouponConvertComponent.appConvertDto(couponList, user, false, znsGoodsSkuEntity.getCurrencyCode(), null);
        if (!CollectionUtils.isEmpty(mallCouponDtos)) {
            for (MallCouponDto mallCouponDto : mallCouponDtos) {
                Coupon coupon = couponMap.get(mallCouponDto.getCouponId());
                //获取不可用原因
                Set<UserCouponConstant.UnAvailableReasonEnum> reasonEnumList = mallCouponComponent.checkCouponBySku(coupon, List.of(new OrderSkuVo(skuId, count)));
                StringJoiner remarkJoiner = new StringJoiner(";");
                int isAvailable = 1;
                if (!CollectionUtils.isEmpty(reasonEnumList)) {
                    for (UserCouponConstant.UnAvailableReasonEnum reasonEnum : reasonEnumList) {
                        if (reasonEnum == UserCouponConstant.UnAvailableReasonEnum.REASON_7) {
                            //国家不可用需要特殊转换
                            String countryNames = coupon.getCountryCodes().stream().map(countryCode -> I18nConstant.CountryCodeEnum.findByCode(countryCode).enName).collect(Collectors.joining(","));
                            remarkJoiner.add(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "coupon.check.reason.REASON_7", countryNames));
                        } else {
                            remarkJoiner.add(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "coupon.check.reason." + reasonEnum));
                        }
                    }
                    isAvailable = 0;
                }
                OrderPickCouponDto orderPickCouponDto = new OrderPickCouponDto(mallCouponDto, isAvailable, remarkJoiner.toString());
                result.add(orderPickCouponDto);
            }
        }
        return result;
    }

    /**
     * 指定优惠券计算价格
     */
    public SkuCouponAmountVo calSkuCouponAmount(Long skuId, Integer count, Long couponId) {
        ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(skuId);
        if (skuEntity == null) {
            return null;
        }

        //获取最佳优惠券
        List<OrderSkuVo> skuVos = List.of(new OrderSkuVo(skuId, count));
        Coupon coupon = couponService.findById(couponId);

        //计算优惠金额
        Map<Long, Integer> skuCountMap = skuVos.stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, OrderSkuVo::getCount, (k1, k2) -> k2));
        BigDecimal couponAmount = calCouponTotalAmount(coupon, skuCountMap);
        SkuCouponAmountVo skuCouponAmountVo = new SkuCouponAmountVo(skuId, count, couponId, skuEntity.getOriginalPrice(), skuEntity.getSalePrice(), couponAmount);
        if (!Objects.equals(count, 1)) {
            //不是一件（说明是商品详情页），会在上层计算总金额，总金额兼容最小值，所以直接返回
            return skuCouponAmountVo;
        }
        //只有一件（说明是商品列表），这里需要兼容总金额最小值（就是销售价）
        BigDecimal minTotalAmount = mallCouponComponent.getMinTotalAmount();
        BigDecimal couponSalePrice = skuCouponAmountVo.getCouponSalePrice();
        couponSalePrice = couponSalePrice.compareTo(minTotalAmount) < 0 ? minTotalAmount : couponSalePrice;
        skuCouponAmountVo.setCouponSalePrice(couponSalePrice);
        return skuCouponAmountVo;
    }

    /**
     * 计算商品券后单价
     */
    public SkuCouponAmountVo calSkuCouponAmount(Long skuId, Integer count, Integer appVersion) {
        ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(skuId);
        if (skuEntity == null) {
            return null;
        }
        ZnsGoodsEntity goodsEntity = znsGoodsService.findById(skuEntity.getGoodsId());

        //获取最佳优惠券
        List<OrderSkuVo> skuVos = List.of(new OrderSkuVo(skuId, count));
        Coupon coupon = null;
        if (appVersion >= Constant.appVersion_4044) {
            coupon = findSkuBestCoupon(skuVos, goodsEntity.getCountryCode());
        }

        //计算优惠金额
        Map<Long, Integer> skuCountMap = skuVos.stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, OrderSkuVo::getCount, (k1, k2) -> k2));
        BigDecimal couponAmount = calCouponTotalAmount(coupon, skuCountMap);
        Long couponId = Optional.ofNullable(coupon).map(Coupon::getId).orElse(null);
        SkuCouponAmountVo skuCouponAmountVo = new SkuCouponAmountVo(skuId, count, couponId, skuEntity.getOriginalPrice(), skuEntity.getSalePrice(), couponAmount);
        if (Objects.equals(count, 1)) {
            //只有一件（说明是商品列表），这里需要兼容总金额最小值（就是销售价）,如果有多件用总价兼容最小值，所以在上层做兼容
            BigDecimal minTotalAmount = mallCouponComponent.getMinTotalAmount();
            BigDecimal couponSalePrice = skuCouponAmountVo.getCouponSalePrice();
            couponSalePrice = couponSalePrice.compareTo(minTotalAmount) < 0 ? minTotalAmount : couponSalePrice;
            skuCouponAmountVo.setCouponSalePrice(couponSalePrice);
        }
        //填充币种+折扣
        skuCouponAmountVo.setCurrencySymbol(I18nConstant.buildCurrency(skuEntity.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode()).getCurrencySymbol());
        skuCouponAmountVo.setDiscount(new GoodsAmountVo(skuCouponAmountVo).calDiscount());
        return skuCouponAmountVo;
    }

    /**
     * 计算商品优惠券优惠总金额
     *
     * @param skuCountMap : sku数量，key -> skuId, val -> 数量
     */
    private BigDecimal calCouponTotalAmount(Coupon coupon, Map<Long, Integer> skuCountMap) {
        if (coupon == null) {
            return BigDecimal.ZERO;
        }
        if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(coupon.getDiscountMethod())) {
            //金额券
            return coupon.getAmount();
        }

        //查询优惠券适用spu
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuCountMap.keySet());
        List<Long> spuIds = mallCouponComponent.getCouponSpuIds(coupon, skuEntities);
        if (CollectionUtils.isEmpty(spuIds)) {
            return BigDecimal.ZERO;
        }

        //计算优惠金额
        BigDecimal spuTotalAmount = BigDecimal.ZERO;
        for (ZnsGoodsSkuEntity skuEntity : skuEntities) {
            if (!spuIds.contains(skuEntity.getGoodsId())) {
                //不是指定的spu
                continue;
            }
            BigDecimal skuTotalAmount = skuEntity.getSalePrice().multiply(new BigDecimal(skuCountMap.getOrDefault(skuEntity.getId(), 0)));
            spuTotalAmount = spuTotalAmount.add(skuTotalAmount);
        }
        //这里计算的优惠金额，直接用折扣（打9折 -> 0.1 ） 如果商品 100，优惠金额 = 100 * 0.1 = 10
        return coupon.getDiscount().multiply(spuTotalAmount).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取商品最佳优惠券
     */
    private Coupon findSkuBestCoupon(List<OrderSkuVo> skuVos, String countryCode) {
        List<Long> skuIds = skuVos.stream().map(OrderSkuVo::getSkuId).distinct().toList();
        List<Coupon> couponList = findAllCouponBySku(skuIds, true, countryCode); //获取sku所有可用的优惠券
        if (CollectionUtils.isEmpty(couponList)) {
            return null;
        }
        //计算最佳优惠券
        return mallCouponComponent.calBestCoupon(skuVos, couponList, null);
    }

    /**
     * 获取sku所有可用的优惠券
     *
     * @param isNowUse ：当前是否可用
     */
    public List<Coupon> findAllCouponBySku(List<Long> skuIds, boolean isNowUse, String mallCountryCode) {
        List<Coupon> couponList = new ArrayList<>();
        //查询全品券
        CouponQuery couponQuery = new CouponQuery().setReceiveStartLe(ZonedDateTime.now()).setReceiveEndGt(ZonedDateTime.now())
                .setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type).setUseScope(CouponConstant.UseScopeEnum.USE_SCOPE_1.type)
                .setStatus(CouponConstant.CouponStatusEnum.STATUS_1.getType()).setMallShowDetail(CouponConstant.ShowDetailEnum.SHOW_DETAIL_1.type);
        List<Coupon> allCouponList = couponService.findList(couponQuery);
        if (!CollectionUtils.isEmpty(allCouponList)) {
            couponList.addAll(allCouponList);
        }
        if (isNowUse) {
            //当前可以使用
            couponList = allCouponList.stream().filter(item -> CouponConstant.ExpiryTypeEnum.EXPIRY_TYPE_1.type.equals(item.getExpiryType()) || (
                    item.getGmtStart().isBefore(ZonedDateTime.now()) && item.getGmtEnd().isAfter(ZonedDateTime.now())
            )).collect(Collectors.toList());
        }

        //查询spuIds
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        List<Long> spuIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        //查询 单品券/指定商品券
        ActivityCouponConfigQuery query = ActivityCouponConfigQuery.builder().couponConfigIds(spuIds).type(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type).build();
        List<ActivityCouponConfig> couponConfigs = couponConfigService.findListByQuery(query);
        if (!CollectionUtils.isEmpty(couponConfigs)) {
            //查询当前可用券
            List<Long> couponIds = couponConfigs.stream().map(ActivityCouponConfig::getCouponId).distinct().toList();
            couponQuery = new CouponQuery().setReceiveStartLe(ZonedDateTime.now()).setReceiveEndGt(ZonedDateTime.now()).setMallShowDetail(CouponConstant.ShowDetailEnum.SHOW_DETAIL_1.type)
                    .setCouponIds(couponIds).setStatus(CouponConstant.CouponStatusEnum.STATUS_1.getType());
            List<Coupon> spuCouponList = couponService.findList(couponQuery);
            if (!CollectionUtils.isEmpty(spuCouponList)) {
                if (isNowUse) {
                    //当前可以使用
                    spuCouponList = spuCouponList.stream().filter(item -> CouponConstant.ExpiryTypeEnum.EXPIRY_TYPE_1.type.equals(item.getExpiryType()) || (
                            item.getGmtStart().isBefore(ZonedDateTime.now()) && item.getGmtEnd().isAfter(ZonedDateTime.now())
                    )).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(spuCouponList)) {
                    couponList.addAll(spuCouponList);
                }
            }
        }
        if (CollectionUtils.isEmpty(couponList)) {
            return null;
        }

        //判断领取限额
        List<Long> couponIds = couponList.stream().map(Coupon::getId).toList();
        Map<Long, Integer> statisticsMap = new HashMap<>(); //优惠券已发总数
        List<UserCouponStatisticsDto> statisticsDtos = userCouponService.selectCouponStatisticsDto(couponIds);
        if (!CollectionUtils.isEmpty(statisticsDtos)) {
            statisticsDtos.forEach(item -> statisticsMap.put(item.getCouponId(), item.getCouponNum()));
        }

        //查询优惠券多币种金额
        List<CouponCurrencyEntity> couponCurrencyEntities = couponCurrencyService.findCouponCurrencyListOrDefault(couponList);
        if (CollectionUtils.isEmpty(couponCurrencyEntities)) {
            return null;
        }
        Map<Long, CouponCurrencyEntity> currencyMap = couponCurrencyEntities.stream().filter(item -> Objects.equals(item.getCurrencyCode(), skuEntities.get(0).getCurrencyCode()))
                .collect(Collectors.toMap(CouponCurrencyEntity::getCouponId, Function.identity(), (k1, k2) -> k2));

        //封装结果
        List<Coupon> result = new ArrayList<>();
        for (Coupon coupon : couponList) {
            Integer quota = coupon.getQuota(); //发放总数 -1:不限制
            Integer totalCount = statisticsMap.getOrDefault(coupon.getId(), 0); //已发总数量
            if (!Objects.equals(quota, -1) && totalCount >= quota) {
                //已发完
                continue;
            }
            if (!coupon.getCountryCodes().contains(mallCountryCode)) {
                //国家不匹配
                continue;
            }
            CouponCurrencyEntity couponCurrency = currencyMap.get(coupon.getId());
            if (couponCurrency == null) {
                //币种不匹配
                continue;
            }
            coupon.fillCurrencyAmount(couponCurrency);
            result.add(coupon);
        }
        return result;
    }

}
