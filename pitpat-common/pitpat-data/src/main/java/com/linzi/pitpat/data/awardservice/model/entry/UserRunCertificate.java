package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 用户活动证书表
 *
 * <AUTHOR>
 * @since 2023-05-16
 */

@Data
@NoArgsConstructor
@TableName("zns_user_run_certificate")
public class UserRunCertificate implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserRunCertificate:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                               // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";                  // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";              // 创建时间
    public final static String modifie_time = CLASS_NAME + "modifie_time";            // 最后修改时间
    public final static String user_id = CLASS_NAME + "user_id";                      // 用户id
    public final static String activity_id = CLASS_NAME + "activity_id";              // 活动id
    public final static String certificate_no = CLASS_NAME + "certificate_no";        // 证书编号
    public final static String certificate_name = CLASS_NAME + "certificate_name";    // 证书名称
    public final static String certificate_type = CLASS_NAME + "certificate_type";    // 证书类型 1名次证书 2 品牌完赛证书
    public final static String run_time = CLASS_NAME + "run_time";                    // 运动时间(s)
    public final static String run_mileage = CLASS_NAME + "run_mileage";              // 运动里程
    public final static String average_velocity = CLASS_NAME + "average_velocity";    // 平均速度(公里/小时)
    public final static String average_pace = CLASS_NAME + "average_pace";            // 平均配速(秒/公里)
    public final static String activity_date = CLASS_NAME + "activity_date";          // 比赛日
    public final static String brand_ = CLASS_NAME + "brand";                         // 品牌
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //最后修改时间
    private ZonedDateTime modifieTime;
    //用户id
    private Long userId;
    //活动id
    private Long activityId;
    //证书编号
    private String certificateNo;
    //证书名称
    private String certificateName;
    //证书类型 1名次证书 2 品牌完赛证书
    private Integer certificateType;
    //运动时间(s)
    private Integer runTime;
    //运动里程
    private BigDecimal runMileage;
    //平均速度(公里/小时)
    private BigDecimal averageVelocity;
    //平均配速(秒/公里)
    private Integer averagePace;
    //比赛日
    private ZonedDateTime activityDate;
    //品牌
    private String brand;

    @Override
    public String toString() {
        return "UserRunCertificate{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",createTime=" + createTime +
                ",modifieTime=" + modifieTime +
                ",userId=" + userId +
                ",activityId=" + activityId +
                ",certificateNo=" + certificateNo +
                ",certificateName=" + certificateName +
                ",certificateType=" + certificateType +
                ",runTime=" + runTime +
                ",runMileage=" + runMileage +
                ",averageVelocity=" + averageVelocity +
                ",averagePace=" + averagePace +
                ",activityDate=" + activityDate +
                ",brand=" + brand +
                "}";
    }
}
