package com.linzi.pitpat.data.awardservice.model.request;


import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CouponReq extends PageQuery {
    /**
     * 卷状态 1：已发布(开) -1：已失效(关)
     */
    private Integer status;
    /**
     * 开始时间
     */
    private ZonedDateTime gmtStartTime;
    /**
     * 结束时间
     */
    private ZonedDateTime gmtEndTime;
    /**
     * 1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券,6 进阶里程碑券,7独立站抵扣券，100：商城优惠券
     *
     * @see CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 券名称(对title)
     */
    private String couponName;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 【4.4.3新增】优惠券主类型  1：赛事券，2：商城券
     *
     * @see CouponConstant.CouponMainTypeEnum
     */
    private Integer couponMainType;

    /**
     * 国家码
     * @since  4.6.4
     */
    private String countryCode;

}
