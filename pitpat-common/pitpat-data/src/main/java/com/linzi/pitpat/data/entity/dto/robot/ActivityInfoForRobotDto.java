package com.linzi.pitpat.data.entity.dto.robot;

import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class ActivityInfoForRobotDto {
    /**
     * 活动类型：1 表示组队跑，2表示挑战跑，3表示官方赛事 4；官方组队跑 5：累计跑 7：一周快乐跑 10 团队赛
     *
     * @see RunActivityTypeEnum
     */
    private Integer activityType;
    // 活动id 通用
    private Long activityId;
    /**
     * 完成规则类型：1表示完成跑步里程，2表示完成跑步时长
     * 当前只有排行赛和累计跑有跑步时长
     *
     * @see ActivityConstants.CompleteRuleTypeEnum
     */
    private Integer completeRuleType;
    // 赛道id
    private Long activityRouteId;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;
    /**
     * 跑步时长(s)
     */
    private Integer runTime;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
}
