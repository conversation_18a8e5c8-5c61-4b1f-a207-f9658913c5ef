package com.linzi.pitpat.data.clubservice.model.response;

import com.linzi.pitpat.data.annotation.Excel;
import com.linzi.pitpat.data.util.file.BaseExcel;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 返回对象有各种定义，BO,VO,DTO 等等
 * 用户响应对象，Response 不要简写
 */
@Data
public class NewUserClubTaskRecordResponseDto extends BaseExcel implements Serializable {

    private Long id;
    // 天数，第几天
    @Excel(name = "天数", defaultValue = "——", prompt = "天数", sort = 1)
    private Integer days;
    @Excel(name = "俱乐部ID", defaultValue = "——", prompt = "俱乐部ID", sort = 2)
    private Long clubId;

    @Excel(name = "用户", defaultValue = "——", prompt = "用户", sort = 3)
    private String userName;

    @Excel(name = "用户ID", defaultValue = "——", prompt = "用户ID", sort = 4)
    private String userCode;

    // 步骤类型,1:文本，2单选，3：多选，4:评分，5：跳转
    @Excel(name = "步骤类型", defaultValue = "——", prompt = "步骤类型", sort = 5)
    private Integer type;

    //选项，平分，是否完成
    @Excel(name = "步骤反馈", defaultValue = "——", prompt = "步骤反馈", sort = 6)
    private String feedBack;

    //提交时间
    private ZonedDateTime applyTime;

    @Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss", defaultValue = "——", prompt = "提交时间", sort = 7)
    private ZonedDateTime submitTime;
    // 是否完成 0：未完成，1：完成
    private Integer isComplete;
}
