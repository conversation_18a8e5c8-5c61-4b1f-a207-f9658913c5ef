package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 勋章配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.MedalConstant;
import com.linzi.pitpat.data.awardservice.dto.consloe.MedalI18nDto;
import com.linzi.pitpat.data.awardservice.dto.consloe.MedalSaveOrModifyReqDto;
import com.linzi.pitpat.data.awardservice.mapper.MedalConfigDao;
import com.linzi.pitpat.data.awardservice.mapper.MedalI18nMapper;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.MedalI18n;
import com.linzi.pitpat.data.awardservice.model.query.MedalConfigQuery;
import com.linzi.pitpat.data.awardservice.model.query.MedalListPo;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

//枚举:ABILITY,1实力勋章: TAKE,2,参加经历:GOLD,3,冠军之路:SUM_MILEAGE,4,累计里程:SUM_REWARD,5,累计奖金:SURPRISED,6,惊喜
//枚举:RANDOM_DISTANCE,random_distance,随机多少（米）: RANDOM_TIME,random_time,随机时间 (秒) : FIX_TIME,fix_time,固定时间 （秒）:FIX_DISTANCE, fix_distance,（米） 固定距离: SUPER_PERSON,super_person, 超过（人）
@Service
public class MedalConfigServiceImpl extends ServiceImpl<MedalConfigDao, MedalConfig> implements MedalConfigService {


    @Autowired
    private MedalConfigDao medalConfigDao;
    @Autowired
    private MedalI18nMapper medalI18nMapper;


    @Override
    public MedalConfig selectMedalConfigById(Long id) {
        return medalConfigDao.selectMedalConfigById(id);
    }

    @Override
    public List<MedalConfig> selectAllMedalConfig(Integer isHide) {
        return medalConfigDao.selectAllMedalConfigList(isHide);
    }

    @Override
    public List<MedalConfig> selectMedalConfigListByIds(List<Long> medalConfigIds) {
        if (CollectionUtils.isEmpty(medalConfigIds)) {
            return new ArrayList<>();
        }
        return medalConfigDao.selectMedalConfigListByIds(medalConfigIds);
    }

    /**
     * 更新修改勋章
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrModify(MedalSaveOrModifyReqDto req, String userName) {
        MedalI18nDto defaultI18nDto = req.getI18nDtos().stream().filter(item -> req.getDefaultLangCode().equals(item.getLangCode())).findFirst().orElse(req.getI18nDtos().get(0));
        MedalConfig medalConfig = new MedalConfig();
        medalConfig.setType(req.getType());
        medalConfig.setValidHours(req.getValidHours());
        medalConfig.setName(defaultI18nDto.getName());
        medalConfig.setRemark(defaultI18nDto.getRemark());
        medalConfig.setZhName(defaultI18nDto.getName());
        medalConfig.setRemark(defaultI18nDto.getRemark());
        medalConfig.setParamNum(req.getParamNum());
        medalConfig.setParam1(req.getParam1());
        medalConfig.setParam2(req.getParam2());
        medalConfig.setParam3(req.getParam3());
        medalConfig.setParam4(req.getParam4());
        medalConfig.setParam5(req.getParam5());
        medalConfig.setConditionType(req.getConditionType());
        medalConfig.setIsHide(req.getIsHide());
        medalConfig.setUrl(req.getUrl());
        medalConfig.setDefaultLangCode(req.getDefaultLangCode());
        Long id = req.getId();
        if (Objects.nonNull(id)) {
            //更新勋章
            medalConfig.setId(id);
            medalConfig.setGmtModified(ZonedDateTime.now());
            medalConfig.setModifier(userName);
            medalConfigDao.updateById(medalConfig);
        } else {
            //新增勋章
            medalConfig.setGmtCreate(ZonedDateTime.now());
            medalConfig.setCreator(userName);
            medalConfig.setParamNum(1);
            medalConfig.setParam1("1");
            medalConfig.setProcess("1");
            medalConfig.setPos(100);
            medalConfig.setHandler(MedalConstant.MEDAL_TYPE_HANDLER_MAP.get(req.getType()));
            medalConfigDao.insert(medalConfig);
            id = medalConfig.getId();
        }

        //删除勋章已有国际化数据
        medalI18nMapper.deleteByMedalConfigId(id);

        //保存新的国际化数据
        for (MedalI18nDto i18nDto : req.getI18nDtos()) {
            I18nConstant.LanguageCodeEnum languageEnum = Optional.ofNullable(I18nConstant.LanguageCodeEnum.findByCode(i18nDto.getLangCode())).orElse(I18nConstant.LanguageCodeEnum.en_US);
            MedalI18n medalI18n = new MedalI18n(id, languageEnum.getCode(), languageEnum.getName(), i18nDto.getName(), i18nDto.getRemark());
            medalI18nMapper.insert(medalI18n);
        }
    }

    /**
     * @param query
     * @return
     */
    @Override
    public List<MedalConfig> findByQuery(MedalConfigQuery query) {
        LambdaQueryWrapper<MedalConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getType() != null, MedalConfig::getType, query.getType());
        queryWrapper.eq(MedalConfig::getIsDelete, 0);
        return medalConfigDao.selectList(queryWrapper);
    }

    @Override
    public Page<MedalConfig> findPage(MedalListPo pageQuery) {
        return medalConfigDao.selectMedalConfigList(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()), pageQuery);
    }

    @Override
    public void updateMedalConfigById(MedalConfig medalConfig) {
        medalConfigDao.updateMedalConfigById(medalConfig);
    }

    @Override
    public List<MedalConfig> selectMedalConfigByType(Integer type) {
        return medalConfigDao.selectMedalConfigByType(type);
    }

    @Override
    public Map<Long, MedalConfig> selectAllMedalConfig() {
        return medalConfigDao.selectAllMedalConfig();
    }
}
