package com.linzi.pitpat.data.awardservice.mapper;
/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityUserScoreDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.query.UserScorePageQuery;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRecordResp;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.Count;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.IsNull;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.NE;
import com.lz.mybatis.plugin.annotations.OrderBy;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.OrderType;
import com.lz.mybatis.plugin.annotations.Sum;
import com.lz.mybatis.plugin.utils.DF_Const;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface ActivityUserScoreDao extends BaseMapper<ActivityUserScore> {

    @OrderBy(ActivityUserScore.send_time)
    List<ActivityUserScoreDto> selectActivityUserScoreByUserId(IPage page, Long userId,
                                                               @Column(ActivityUserScore.gmt_create) @IF @GE ZonedDateTime gmtCreate,
                                                               @Column(ActivityUserScore.gmt_create) @IF @DateFormat(DF_Const.y_m) String queryMonth,
                                                               @Column(ActivityUserScore.income_) @IF Integer income,
                                                               @Column(ActivityUserScore.source_) @IF List<Integer> source);


    @OrderBy(value = {ActivityUserScore.gmt_create}, type = {OrderType.DESC})
    List<ActivityUserScoreDto> selectActivityUserScoreByUserIdMonthApp(IPage page, Long userId,
                                                                       @Column(ActivityUserScore.gmt_create) @IF @LE String queryMonth,
                                                                       @Column(ActivityUserScore.income_) @IF Integer income,
                                                                       @Column(ActivityUserScore.source_) @IF List<Integer> source,
                                                                       @Column(ActivityUserScore.status_) @NE Integer status
    );


    @OrderBy(value = {ActivityUserScore.gmt_create}, type = {OrderType.ASC})
    @LIMIT(1)
    ActivityUserScore selectFirstActivityUserScore(Long userId,
                                                   @Column(ActivityUserScore.income_) @IF Integer income,
                                                   @Column(ActivityUserScore.source_) @IF List<Integer> source,
                                                   @Column(ActivityUserScore.status_) @NE Integer status
    );


    @OrderBy(ActivityUserScore.id_)
    @Mapping({ActivityUserScore.all, ZnsRunActivityEntity.activity_title, Coupon.title_, Coupon.coupon_type, Coupon.amount_, Coupon.picture_,
            Coupon.min_pic_url})
    @LeftJoinOns(
            {@Item(value = ZnsRunActivityEntity.class, left = ActivityUserScore.activity_id, right = ZnsRunActivityEntity.id_),
                    @Item(value = Coupon.class, left = ActivityUserScore.coupon_id, right = Coupon.id_)
            }
    )
    ActivityUserScoreDto selectActivityUserScoreDtoById(@Column(ActivityUserScore.id_) Long id);


    Page<ExchangeScoreRecordResp> selectExchangeScoreRecordByCondition(IPage page,
                                                                       @Param("emailAddress") String emailAddress,
                                                                       @Param("title") String title,
                                                                       @Param("income") Integer income);

    @Sum("score -use_score")
    int selectActivityUserScoreByUserIdMonthExpire(Long userId,
                                                   @Column(ActivityUserScore.expire_time) @GE ZonedDateTime firstOfMonth,
                                                   @Column(ActivityUserScore.expire_time) @LE ZonedDateTime endOfDate,
                                                   List<Integer> status
    );


    @Sum("score")
    int selectActivityUserScoreByUserIdMonth(Long userId,
                                             @Column(ActivityUserScore.gmt_create) @GE ZonedDateTime firstOfMonth,
                                             @Column(ActivityUserScore.gmt_create) @LE ZonedDateTime endOfDate,
                                             Integer income,
                                             @NE Integer status
    );


    @Count(ActivityUserScore.id_)
    int selectExchangePersonCountByUserIdExchangeScoreRuleId(Long userId, Long exchangeScoreRuleId, Integer income);


    @OrderBy(value = {ActivityUserScore.status_, ActivityUserScore.expire_time}, type = {OrderType.DESC, OrderType.ASC})
    List<ActivityUserScore> selectActivityUserScoreByUserIdStatusExpireTime(IPage page, Long userId, List<Integer> status, Integer income);


    @Count(ActivityUserScore.user_id)
    Integer selectByExchangeScoreRuleIdSource(Long exchangeScoreRuleId, @IN List<Integer> source);


    @Count(ActivityUserScore.all)
    int selectActivityUserScoreByUserIdStartTimeEndTime(Long userId,
                                                        @Column(ActivityUserScore.gmt_create) @IF @GE ZonedDateTime startTime,
                                                        @Column(ActivityUserScore.gmt_create) @IF @LE ZonedDateTime endTime,
                                                        Integer source);


    int updateScoreStatusExpireTime(ZonedDateTime gmtModified, ZonedDateTime expireTime, ZonedDateTime awardTime, Integer status, @By Long id);

    @OrderByIdDescLimit_1
    ActivityUserScore selectActivityUserScoreByActivityUserId(Long activityId, Long userId, Integer source);


    @Sum(ActivityUserScore.score_)
    Integer selectAllAwardScore(Long userId, Integer income, @NE Integer status);


    List<ActivityUserScore> selectActivityUserScoreByScoreConfigIdUserId(
            @Column(ActivityUserScore.gmt_create) @IF @GE ZonedDateTime startTime,
            @Column(ActivityUserScore.gmt_create) @IF @LE ZonedDateTime endTime,
            Long scoreConfigId,
            Long userId,
            Integer status);


    // status , 0.待领取，1 领取成功， 2 已经使用，3 部分使用， -1 已经过期
    // expireTime
    List<ActivityUserScore> selectActivityUserScoreByExpireTimeStatus(@Column("expire_time") @GE ZonedDateTime startTime, @Column("expire_time") @LE ZonedDateTime endTime, List<Integer> status);


    @OrderByIdDescLimit_1
    ActivityUserScore selectActivityUserScoreByCourseIdUserId(Long courseId, Long userId, Integer source);


    @OrderByIdDescLimit_1
    ActivityUserScore selectExchangePersonCountByScoreConfigIdUserId(Long scoreConfigId, Long userId);


    @LIMIT
    ActivityUserScore selectActivityUserScoreByActivityIdUserIdRank(Long activityId, Long userId, Integer rank);

    List<ActivityUserScore> selectActivityUserScoreByActivityIdExpireTime(@IsNull ZonedDateTime expireTime);

    @Mapping({ActivityUserScore.activity_id, ActivityUserScore.user_id, ActivityUserScore.score_, ActivityUserScore.source_})
    List<ActivityUserScore> selectActivityUserScoreByActivityId(Long activityId);

    ActivityUserScore selectOneByMilepost(@Param("activity_id") Long activityId, @Param("user_id") Long userId, @Param("milepost") int milepost);

    @Mapping("IFNULL(sum(score),0)")
    Integer selectSumScore(Long activityId, Long userId, @IN @IF List<Integer> source, @IF Integer status);

    @Mapping("IFNULL(sum(score),0)")
    Integer selectSumScoreWithTime(Long activityId, Long userId, @IF Integer status, @GE ZonedDateTime gmtCreate);

    @Sum("score")
    BigDecimal selectScoreByUserIdSourceType(Long userId, @IF @DateFormat @GE @Column("gmt_create") ZonedDateTime gmtStartTime, @LE @IF @DateFormat @Column("gmt_create") ZonedDateTime gmtEndTime, @IF @IN List<Integer> source);

    Integer countByUserIdTradeTypeSubType(Long userId, @IF @DateFormat @GE @Column("gmt_create") ZonedDateTime gmtStartTime, @LE @IF @DateFormat @Column("gmt_create") ZonedDateTime gmtEndTime, @IF @IN List<Integer> source);

    BigDecimal selectMonthReportScoreData(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    @Mapping("IFNULL(sum(score),0)")
    Integer selectSumScoreByActivityIds(@IN @IF List<Long> activityId, Long userId, @IN @IF List<Integer> source, @IF Integer status);

    /**
     * 合并积分账户
     * @param newUserId
     * @param oldUserIds
     */
    void mergeUserScore(@Param("newUserId") Long newUserId, @Param("oldUserIds") List<Long> oldUserIds);

    Page<ActivityUserScore> findPage(Page page, @Param("query") UserScorePageQuery query);

    List<Long> getFixUserId();

    List<ActivityUserScore> getFixDo(@Param("userIds") List<Long> userIds);
}
