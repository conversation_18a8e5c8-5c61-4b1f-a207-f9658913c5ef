package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 用户勋章 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.entity.dto.UserMedalDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

public interface UserMedalService {


    UserMedal selectUserMedalById(Long id);

    int updateUserMedalById(UserMedal userMedal);


    void initMedal(Long userId);


    void deliverMedal(ZnsUserEntity user, Long activityId, Integer isFinished, ActivityTypeDto activityNew);


    void setTarget(MedalConfig medalConfig, UserMedal userMedal);

    List<UserMedal> selectUserMedalByUserIdObtainIsPop(Long userId, Integer obtain, Integer isPop, Integer isValid);

    /**
     * 查询指定弹窗的勋章列表
     *
     * @param userId
     * @param obtain
     * @param isPop
     * @param isValid
     * @param medalConfigIds
     * @return
     */
    List<UserMedal> selectUserMedalByUserIdObtainIsPop(Long userId, Integer obtain, Integer isPop, Integer isValid, List<Long> medalConfigIds);

    void updateUserMedalIsPopByUserId(Integer isPop, Long id);

    List<Map<String, Object>> selectHomePageList(Long userId);

    UserMedal selectMostUserMedalByType(Long userId, Integer type);

    /**
     * 勋章未展示改为已展示
     *
     * @param userId
     */
    void updateIsShow(Long userId);

    void updateUserMedalValid();

    void updateUserMedalIsHomePagePosByUserId(Integer isShowHomePage, Integer pos, Long userId);

    UserMedal selectUserMedalByConfigId(Long userId, Long demandMedalConfigId);

    void convert(UserMedal userMedal);

    void robotWearMedals(boolean robotWearingNewMedal, ZnsUserEntity user);

    /**
     * 获取月报指定用户的五张徽章图案
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    List<MedalConfig> getMonthReportMedalImgs(Long userId, ZonedDateTime startTime, ZonedDateTime endTime);

    //手动发放勋章奖励
    void sendUserMedal(Long userId, Long medalId);

    UserMedal findByUserAndMedalId(Long userId, Long MedalConfigId);

    List<UserMedal> findRepeatAll();

    List<UserMedal> findRepeatAllV2();

    /**
     * 获取用户已获得的勋章图片
     *
     * @param userId
     * @return
     */
    List<String> findUserObtainMedal(Long userId, Integer limitNum);

    List<UserMedalDto> selectUserMedalByUserIdList(IPage page, Long userId, Integer obtain);

    boolean saveOrUpdate(UserMedal userMedal);

    void updateBatchById(List<UserMedal> updateList);

    long findCount(QueryWrapper<UserMedal> wrapper);

    List<UserMedal> findList(QueryWrapper<UserMedal> wrapper);
}
