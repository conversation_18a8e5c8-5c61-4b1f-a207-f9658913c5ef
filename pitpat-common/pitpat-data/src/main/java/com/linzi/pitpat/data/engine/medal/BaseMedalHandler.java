package com.linzi.pitpat.data.engine.medal;

import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.ZonedDateTime;

public abstract class BaseMedalHandler {


    public abstract Pair<Boolean, UserMedal> deliverMedal(MedalParamDto medalParamDto);

    /**
     * 检查勋章涉及活动类型
     *
     * @param activityNew
     * @return
     */
    public boolean activityTypeCheck(ActivityTypeDto activityNew) {
        return true;
    }

    public boolean handlerSucessMedal(MedalConfig medalConfig, UserMedal userMedal) {
        userMedal.setObtain(1);
        userMedal.setIsValid(0);
        userMedal.setParam1(medalConfig.getParam1());
        userMedal.setParam2(medalConfig.getParam2());
        userMedal.setParam3(medalConfig.getParam3());
        userMedal.setParam4(medalConfig.getParam4());
        userMedal.setParam5(medalConfig.getParam5());
        userMedal.setObtainTime(ZonedDateTime.now());
        userMedal.setRemark(medalConfig.getRemark());
        UserMedalService userMedalService = SpringContextUtils.getBean(UserMedalService.class);
        userMedalService.setTarget(medalConfig, userMedal);
        return true;
    }


    public Integer getInt(String value) {
        return MapUtil.getInteger(value);
    }
}
