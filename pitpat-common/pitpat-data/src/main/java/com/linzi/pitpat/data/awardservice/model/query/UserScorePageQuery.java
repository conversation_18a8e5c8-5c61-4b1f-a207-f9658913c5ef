package com.linzi.pitpat.data.awardservice.model.query;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 16:06
 */
@Data
@Accessors(chain = true)
public class UserScorePageQuery extends PageQuery {

    private ZonedDateTime geExpireTime;
    private ZonedDateTime ltExpireTime;
    private List<Integer> statusList;
    private Integer isRobot;
}
