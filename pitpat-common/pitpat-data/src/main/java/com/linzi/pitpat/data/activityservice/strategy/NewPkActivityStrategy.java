package com.linzi.pitpat.data.activityservice.strategy;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.CompanionTaskBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityMode;
import com.linzi.pitpat.data.activityservice.constant.enums.CompanionTaskType;
import com.linzi.pitpat.data.activityservice.constant.enums.CompanionUserAccountDetailsTradeType;
import com.linzi.pitpat.data.activityservice.constant.enums.CompanionUserAccountDetailsTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomStatusEnum;
import com.linzi.pitpat.data.activityservice.manager.api.CompanionUserAccountManager;
import com.linzi.pitpat.data.activityservice.model.dto.CompanionUserAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.NewPkActivityConfigDto;
import com.linzi.pitpat.data.activityservice.model.dto.RankAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.dto.SendUserCompanionAccountDto;
import com.linzi.pitpat.data.activityservice.model.entity.CompanionTaskDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompanionUserDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompanionUserTaskDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.RoomActivityExtInfoDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomParticipantsDo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.CompanionUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.CompanionUserTaskQuery;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomActivityExtInfoQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomParticipantsQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.service.CompanionUserService;
import com.linzi.pitpat.data.activityservice.service.CompanionUserTaskService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.RoomActivityExtInfoService;
import com.linzi.pitpat.data.activityservice.service.RoomParticipantsService;
import com.linzi.pitpat.data.activityservice.service.RoomService;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventSourceEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/7/10 11:00
 */
@Service
@Slf4j
public class NewPkActivityStrategy extends BaseActivityStrategy {
    @Resource
    private UserTaskDetailService userTaskDetailService;
    @Resource
    private RoomParticipantsService roomParticipantsService;
    @Resource
    private RoomService roomService;
    @Autowired
    private CompanionUserService companionUserService;
    @Autowired
    private CompanionUserAccountManager companionUserAccountManager;
    @Autowired
    private CompanionTaskBizService companionTaskBizService;
    @Autowired
    private CompanionUserTaskService companionTaskService;
    @Autowired
    private RoomActivityExtInfoService roomActivityExtInfoService;
    @Autowired
    private PropRunRankedActivityUserService runRankedActivityUserService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 投放机器人延迟队列
     */
    @Value("${" + RabbitQueueConstants.ROOM_PROPMODE_ENDACTIVITY_DELAY_EXCHANGE + "}")
    private String room_propmode_endactivity_delay_exchange;
    @Value("${" + RabbitQueueConstants.ROOM_PROPMODE_ENDACTIVITY_DELAY_QUEUE + "}")
    private String room_propmode_endactivity_delay_queue;

    @Autowired
    private MainRunActivityRelationService mainRunActivityRelationService;
    @Autowired
    private QueueMessageService queueMessageService;

    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {

    }

    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        return null;
    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {
        return null;
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return null;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return CommonResult.success();
    }

    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        return null;
    }

    @Override
    public ActivityRunningReportBaseVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        return null;
    }

    @Override
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        log.info("NewPkActivityStrategy:handleRunActivityEnd 处理开始，活动id:{}", activityEntity.getId());
        RoomActivityExtInfoDo roomActivityExtInfoDo = roomActivityExtInfoService.findByQuery(new RoomActivityExtInfoQuery().setActivityId(activityEntity.getId()));
        //模式普通模式.历史数据没有模式信息存储
        Integer activityMode = ActivityMode.NORMAL_MODE.getValue();
        if (Objects.nonNull(roomActivityExtInfoDo)) {
            //有模式数据
            activityMode = roomActivityExtInfoDo.getActivityMode();
        }
        // 道具模式activityEnd 根据游戏上报数据结果 队列重新触发结束
        if (ActivityMode.PROP_MODE.getValue().equals(activityMode)) {
            List<PropRunRankedActivityUser> activityUsers = runRankedActivityUserService
                    .findListByActivityId(activityEntity.getId());
            String incrKey = "propActivityEnd:" + activityEntity.getId();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(incrKey);
            //存在未上报结束的游戏成绩 超过3秒 10 次数就直接去做结算
            log.info("propActivityUsers:{}", JsonUtil.writeString(activityUsers));
            if (activityUsers.stream().anyMatch(user -> user.getGameReportStatus() == 0) && atomicLong.get() < 10L) {
                log.info("还有未上报游戏成绩的用户:重新发送队列结束activity:{}", activityEntity.getId());
                rabbitTemplate.convertAndSend(room_propmode_endactivity_delay_exchange,
                        room_propmode_endactivity_delay_queue,
                        activityEntity.getId(), message -> {
                            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                            message.getMessageProperties().setDelay(3000);
                            return message;
                        });
                // 原子递增操作
                long value = atomicLong.incrementAndGet();
                log.info("等待游戏成绩后续在结束activity_id:{} 递增后的次数value: {}", activityEntity.getId(), value);
                return;
            }
        }
        super.handleRunActivityEnd(activityEntity);

        //查询完赛用户
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).userStateIn(Arrays.asList(1, 3, 4)).activityId(activityEntity.getId())
                .build();
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
        log.info("ZnsRunActivityUserEntity list:{}", JsonUtil.writeString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.info("NewPkActivityStrategy:handleRunActivityEnd 处理结束，无用户，活动id:{}", activityEntity.getId());
            return;
        }

        //修改房间状态
        updateRoomStatus(list.get(0));

        BigDecimal userCount = new BigDecimal(list.stream().filter(i -> Arrays.asList(3, 4).contains(i.getUserState())).count());
        //筛选用户完赛状态
        list = list.stream().filter(u -> u.getIsComplete() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list) && ActivityMode.PROP_MODE.getValue().equals(activityMode)) {
            log.info("NewPkActivityStrategy:handleRunActivityEnd 处理结束报名表完赛状态未完成同步，无完赛用户，活动id:{}", activityEntity.getId());
            String incrKey = "propActivityEnd:" + activityEntity.getId();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(incrKey);
            //存在未上报结束的游戏成绩 超过3秒 10 次数就直接去做结算
            if (atomicLong.get() < 10L) {
                log.info("还有未同步游戏成绩的用户:重新发送队列结束activity:{}", activityEntity.getId());
                rabbitTemplate.convertAndSend(room_propmode_endactivity_delay_exchange,
                        room_propmode_endactivity_delay_queue,
                        activityEntity.getId(), message -> {
                            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                            message.getMessageProperties().setDelay(3000);
                            return message;
                        });
                // 原子递增操作
                long value = atomicLong.incrementAndGet();
                log.info("等待游戏成绩后续在结束activity_id:{} 递增后的次数value: {}", activityEntity.getId(), value);
                return;
            }
        }
        //筛选用户状态
        list = list.stream().filter(u -> u.getIsComplete() == 1 && u.getIsCheat() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            log.info("NewPkActivityStrategy:handleRunActivityEnd 处理结束，无完赛用户，活动id:{}", activityEntity.getId());
            return;
        }

        NewPkActivityConfigDto newPkActivityConfigDto = JsonUtil.readValue(activityEntity.getActivityConfig(), NewPkActivityConfigDto.class);
        //只有两个人的都按1:0，重置newPkActivityConfigDto
        resettingNewPkActivityConfigDto(newPkActivityConfigDto, activityEntity.getId());

        BigDecimal priceProportion = newPkActivityConfigDto.getPriceProportion();
        Map<Integer, BigDecimal> rankAwardConfig = newPkActivityConfigDto.getRankAwardConfigList().stream().collect(Collectors.toMap(RankAwardConfigDto::getRank, RankAwardConfigDto::getRatio));

        //可给用户分配的剩余比例
        BigDecimal surplus = BigDecimal.ONE.subtract(priceProportion).add(newPkActivityConfigDto.getBonusProportion());
        //总奖励计算,已抽成
        BigDecimal totalAward = BigDecimal.ZERO;
        if (ActivityConstants.BonusRuleTypeEnum.BOND.getCode().equals(activityEntity.getBonusRuleType())) {
            totalAward = activityEntity.getActivityEntryFee().multiply(userCount).multiply(surplus).setScale(2, RoundingMode.HALF_UP);
        } else if (ActivityConstants.BonusRuleTypeEnum.SCORE.getCode().equals(activityEntity.getBonusRuleType())) {
            totalAward = activityEntity.getActivityEntryScore().multiply(userCount).multiply(surplus).setScale(2, RoundingMode.HALF_UP);
        }
        log.info("NewPkActivityStrategy:handleRunActivityEnd 处理中，活动id:{},活动总奖励:{}", activityEntity.getId(), totalAward);
        //排名并列处理
        Map<Integer, List<ZnsRunActivityUserEntity>> userGradeMap = null;
        List<Map.Entry<Integer, List<ZnsRunActivityUserEntity>>> userMapList = null;

        // 传统模式
        if (ActivityMode.NORMAL_MODE.getValue().equals(activityMode)) {
            if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode().equals(activityEntity.getCompleteRuleType())) {
                userGradeMap = list.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getRunTimeMillisecond));
                userMapList = userGradeMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey)).collect(Collectors.toList());
            } else {
                userGradeMap = list.stream().collect(Collectors.groupingBy(u -> u.getRunMileage().intValue()));
                userMapList = userGradeMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } else {
            // 道具模式
            list.forEach(activityUser -> {
                PropRunRankedActivityUser propActivityUser = runRankedActivityUserService
                        .findByActivityIdAndUserId(activityUser.getActivityId(), activityUser.getUserId());
                activityUser.setPropRunMileage(propActivityUser.getRunMileage());
                activityUser.setPropRunTime(propActivityUser.getRunTimeMils());
            });
            if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode().equals(activityEntity.getCompleteRuleType())) {
                userGradeMap = list.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getPropRunTime));
                userMapList = userGradeMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey)).collect(Collectors.toList());
            } else {
                userGradeMap = list.stream().collect(Collectors.groupingBy(u -> u.getPropRunMileage().intValue()));
                userMapList = userGradeMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        }
        Integer rank = 0;
        for (int i = 0; i < userMapList.size(); i++) {
            Map.Entry<Integer, List<ZnsRunActivityUserEntity>> listEntry = userMapList.get(i);
            List<ZnsRunActivityUserEntity> activityUserEntities = listEntry.getValue();
            //并列排名处理
            BigDecimal totalRatio = BigDecimal.ZERO;
            Integer preRank = i;
            for (int j = 0; j < activityUserEntities.size(); j++) {
                ZnsRunActivityUserEntity activityUser = activityUserEntities.get(j);
                rank += 1;
                if (j == 0) {
                    preRank = rank;
                }
                activityUser.setRank(preRank);
                BigDecimal ratio = rankAwardConfig.getOrDefault(rank, BigDecimal.ZERO);
                totalRatio = totalRatio.add(ratio);
                log.info("handleRunActivityEnd 处理中，活动id:{},用户id:{},非并列排名:{},比例:{}", activityEntity.getId(), activityUser.getUserId(), rank, ratio);
            }

            //计算奖励,同一名次瓜分,并列第一瓜分不并列下比例,即并列第一名两人瓜分1,2名;并列第一名三人瓜分1,2,3名
            BigDecimal totalRankAward = totalAward.multiply(totalRatio);
            BigDecimal award = totalRankAward.divide(new BigDecimal(listEntry.getValue().size()), 2, RoundingMode.UP);

            for (ZnsRunActivityUserEntity activityUser : activityUserEntities) {
                if (activityUser.getRank() == 1) {
                    userTaskDetailService.initUserDailyTask(activityUser.getUserId());
                    //新pk赛第一名
                    userTaskDetailService.completeLevelTask(activityUser.getUserId(), UserExpObtainSubTypeEnum.WIN_RACE_ONCE.getCode(), true);
                }
                if (ActivityConstants.BonusRuleTypeEnum.SCORE.getCode().equals(activityEntity.getBonusRuleType())) {
                    award = award.setScale(0, RoundingMode.UP);
                }
                activityUser.setRunAward(award);
                activityUser.setRewardTime(ZonedDateTime.now());
                //奖励发放
                if (award.compareTo(BigDecimal.ZERO) > 0) {
                    sendNewPkActivityAward(activityUser, activityEntity, award);
                }
                log.info("handleRunActivityEnd 处理中，活动id:{},用户id:{},实际排名:{},获得奖励:{}", activityEntity.getId(),
                        activityUser.getUserId(), activityUser.getRank(), award);
                if (ActivityMode.PROP_MODE.getValue().equals(activityMode)) {
                    PropRunRankedActivityUser propActivityUser = runRankedActivityUserService
                            .findByActivityIdAndUserId(activityUser.getActivityId(), activityUser.getUserId());
                    propActivityUser.setRank(rank);
                    runRankedActivityUserService.update(propActivityUser);
                }
            }
            runActivityUserService.updateBatchById(activityUserEntities);
        }
    }

    private void updateRoomStatus(ZnsRunActivityUserEntity activityEntity) {
        //查询房间用户关系
        RoomParticipantsDo participantsDo = roomParticipantsService.findByQuery(RoomParticipantsQuery.builder().activityId(activityEntity.getActivityId()).userId(activityEntity.getUserId()).build());
        if (participantsDo == null) {
            return;
        }
        RoomDo roomDo = roomService.findById(participantsDo.getRoomId());
        if (Objects.nonNull(roomDo) && roomDo.getRoomStatus().equals(RoomStatusEnum.IN_PROGRESS.getCode())) {
            roomService.updateStatus(roomDo.getId(), RoomStatusEnum.NOT_STARTED.getCode());
        }
    }

    private void resettingNewPkActivityConfigDto(NewPkActivityConfigDto newPkActivityConfigDto, Long activityId) {
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).userStateIn(Arrays.asList(1, 3, 4)).activityId(activityId)
                .build();
        Long count = runActivityUserService.findCount(userQuery);
        if (count > 2) {
            return;
        }
        List<RankAwardConfigDto> rankAwardConfigList = new ArrayList<>();
        RankAwardConfigDto rankAwardConfigDto = new RankAwardConfigDto().setRank(1).setRatio(BigDecimal.ONE);
        rankAwardConfigList.add(rankAwardConfigDto);
        newPkActivityConfigDto.setRankAwardConfigList(rankAwardConfigList);
    }

    private void sendNewPkActivityAward(ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal award) {

        if (ActivityConstants.BonusRuleTypeEnum.BOND.getCode().equals(activityEntity.getBonusRuleType())) {
            userAccountService.increaseAmount(award, activityUser.getUserId(), true);
            // 新增用户奖励余额明细
            String billNo = NanoId.randomNanoId();
            ;
            ZonedDateTime tradeTime = ZonedDateTime.now();
            userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.NEW_ACTIVITY_100,
                    1, 1, award, billNo, tradeTime,
                    activityUser.getActivityId(), activityUser.getActivityId(), null, activityUser.getActivityType(),
                    0L, activityEntity.getActivityTitle(), null, null, null, BigDecimal.ZERO);

        } else if (ActivityConstants.BonusRuleTypeEnum.SCORE.getCode().equals(activityEntity.getBonusRuleType())) {
            // 发放积分
            activityUserScoreService.increaseAmount(award.intValue(), activityUser.getActivityId(), activityUser.getUserId(),
                    activityUser.getRank(), 0, ScoreConstant.SourceTypeEnum.source_type_100.getType() + 1);
        }
    }

    public void runEnd(ZnsUserRunDataDetailsEntity detailsEntity, ZnsRunActivityEntity activityEntity) {
        //完赛奖励发放，涉及陪跑奖励/任务奖励
        try {
            MainRunActivityRelationDo relationDo = mainRunActivityRelationService.findByQuery(MainRunActivityRelationQuery.builder().runActivityId(activityEntity.getId()).build());
            CompanionUserDo userDo = companionUserService.findByQuery(new CompanionUserQuery().setUserId(detailsEntity.getUserId()));
            //查询活动用户，是否完赛
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), detailsEntity.getUserId());
            if (Objects.isNull(activityUser)) {
                log.info("runEnd end,用户未参与活动，userId:{}", detailsEntity.getUserId());
                return;
            }
            if (activityUser.getIsComplete() != 1) {
                log.info("runEnd end,用户未完赛，userId:{}", detailsEntity.getUserId());
                return;
            }
            RoomParticipantsDo participantsDo = roomParticipantsService.findByQuery(RoomParticipantsQuery.builder().activityId(activityEntity.getId()).userId(detailsEntity.getUserId()).build());
            if (Objects.isNull(participantsDo)) {
                log.info("runEnd end,用户未参与房间，userId:{}", detailsEntity.getUserId());
                return;
            }
            //发布turbolink事件
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(), new TurbolinkApplicationEvent(TurbolinkEventEnum.GLOBAL_EVENT, detailsEntity.getUserId(), Map.of("complete_user_race", Boolean.TRUE.toString()), TurbolinkEventSourceEnum.RUNEND));

            if (Objects.isNull(userDo) || userDo.getCompanionLevel() < 1) {
                log.info("runEnd end,用户不是陪跑员，userId:{}", detailsEntity.getUserId());
                return;
            }
            if (userDo.getCompanionStatus() != 0) {
                log.info("runEnd end,用户陪跑员陪跑员状态被冻结，userId:{}", detailsEntity.getUserId());
                return;
            }
            participantsDo.setIsCompanionUser(1);
            roomParticipantsService.update(participantsDo);

            RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(participantsDo.getRoomId()).build());

            //表示俱乐部用户赛，不发陪跑奖励
            if (Objects.isNull(relationDo)) {
                //陪跑奖励
                String config = sysConfigService.selectConfigByKey("companion.user.award");
                List<CompanionUserAwardDto> companionUserAwardDtos = JsonUtil.readList(config, CompanionUserAwardDto.class);
                companionUserAwardDtos.stream().filter(a -> a.getCompanionLevel().equals(userDo.getCompanionLevel())).findFirst().ifPresent(a -> {
                    companionUserAccountManager.sendUserCompanionAward(new SendUserCompanionAccountDto().setUserId(detailsEntity.getUserId()).setType(CompanionUserAccountDetailsTypeEnum.INCOME.getType())
                            .setTradeType(CompanionUserAccountDetailsTradeType.COMPANION_AWARD.getType()).setAmount(a.getAward()).setRoomNumber(roomDo.getRoomNumber()));
                });

                //任务奖励
                sendTaskAward(detailsEntity.getUserId(), roomDo);
            }

            //更新陪跑员表
            companionUserService.addServiceNum(userDo.getId());
        } catch (Exception e) {
            log.error("NewPkActivityStrategy runEnd error", e);
        }
    }

    private void sendTaskAward(Long userId, RoomDo roomDo) {
        List<CompanionTaskDo> validList = companionTaskBizService.findUserUndoneTaskList(userId);
        if (CollectionUtils.isEmpty(validList)) {
            log.info("runEnd end,无任务，userId:{}", userId);
            return;
        }
        Integer companionRoomCount = runActivityUserService.findCompanionCount(userId, ZonedDateTime.now().truncatedTo(ChronoUnit.DAYS), CompanionTaskType.OPER_ROOM.getType());
        Integer companionCompleteCount = runActivityUserService.findCompanionCount(userId, ZonedDateTime.now().truncatedTo(ChronoUnit.DAYS), CompanionTaskType.COMPLETE_ACCOMPANYING_RUN.getType());

        for (CompanionTaskDo companionTaskDo : validList) {
            Boolean isComplete = false;
            //开房间
            if (CompanionTaskType.OPER_ROOM.getType().equals(companionTaskDo.getTaskType())) {
                if (!roomDo.getOwnerUserId().equals(userId)) {
                    log.info("runEnd end,用户不是房间创建者，userId:{}", userId);
                    continue;
                }
                if (companionRoomCount >= companionTaskDo.getFinshNum()) {
                    isComplete = true;
                }
            } else if (CompanionTaskType.COMPLETE_ACCOMPANYING_RUN.getType().equals(companionTaskDo.getTaskType())) {
                if (companionCompleteCount >= companionTaskDo.getFinshNum()) {
                    isComplete = true;
                }
            }
            if (isComplete) {
                CompanionUserTaskDo taskDo = companionTaskService.findByQuery(new CompanionUserTaskQuery().setUserId(userId).setTaskId(companionTaskDo.getId()).setMinTaskCompletionTime(ZonedDateTime.now().truncatedTo(ChronoUnit.DAYS)));
                if (Objects.nonNull(taskDo)) {
                    log.info("runEnd end,用户任务已领取，userId:{}", userId);
                    continue;
                }
                //添加任务完成
                companionTaskService.create(new CompanionUserTaskDo().setUserId(userId).setTaskId(companionTaskDo.getId())
                        .setTaskCompletionTime(ZonedDateTime.now()).setAmount(companionTaskDo.getAmount()));

                companionUserAccountManager.sendUserCompanionAward(new SendUserCompanionAccountDto().setUserId(userId).setType(CompanionUserAccountDetailsTypeEnum.INCOME.getType())
                        .setTradeType(CompanionUserAccountDetailsTradeType.TASK_AWARD.getType()).setAmount(companionTaskDo.getAmount())
                        .setRoomNumber(roomDo.getRoomNumber()).setCompanionTaskId(companionTaskDo.getId()));
            }
        }
    }
}
