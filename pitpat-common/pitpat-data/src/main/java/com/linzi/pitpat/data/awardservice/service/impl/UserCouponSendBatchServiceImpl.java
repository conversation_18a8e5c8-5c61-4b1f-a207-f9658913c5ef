package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 用户批次发放优惠券表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.awardservice.mapper.UserCouponSendBatchDao;
import com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendBatch;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponSendBatchAddReq;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponSendBatchReq;
import com.linzi.pitpat.data.awardservice.model.resp.UserCouponSendBatchResp;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponSendDetailVo;
import com.linzi.pitpat.data.awardservice.service.UserCouponSendBatchService;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class UserCouponSendBatchServiceImpl extends ServiceImpl<UserCouponSendBatchDao, UserCouponSendBatch> implements UserCouponSendBatchService {


    @Autowired
    private UserCouponSendBatchDao userCouponSendBatchDao;


    @Override
    public Page<UserCouponSendBatchResp> listAll(UserCouponSendBatchReq basePagePo) {
        Page<UserCouponSendBatchResp> page = userCouponSendBatchDao.listAll(new Page<>(basePagePo.getPageNum(), basePagePo.getPageSize()), basePagePo);
        List<UserCouponSendBatchResp> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(s -> {
                s.setIsCashAmountSend(Objects.nonNull(s.getActivityId()));
            });
        }
        return page;
    }

    @Override
    public boolean deleteUserCouponSendBatchById(Long id) {
        return userCouponSendBatchDao.deleteById(id) > 1;
    }

    @Override
    public void fillField(UserCouponSendBatchAddReq userCouponSendBatchAddReq, UserCouponSendBatch userCouponSendBatch) {
        List<UserCouponSendDetailVo> userCouponSendDetailVos = JsonUtil.readList(userCouponSendBatchAddReq.getCouponIds(), UserCouponSendDetailVo.class);
        if (!CollectionUtils.isEmpty(userCouponSendDetailVos)) {
            String couponIds = userCouponSendDetailVos.stream().map(UserCouponSendDetailVo::getCouponId).toList().toString();
            userCouponSendBatch.setCouponIds(couponIds);
        }
        if (StringUtils.hasText(userCouponSendBatchAddReq.getMedalIds())) {
            userCouponSendBatch.setMedalIds(userCouponSendBatchAddReq.getMedalIds());
        }
        if (StringUtils.hasText(userCouponSendBatchAddReq.getWears())) {
            userCouponSendBatch.setWears(userCouponSendBatchAddReq.getWears());
        }
        if (CollectionUtils.size(userCouponSendDetailVos) == 1) {
            userCouponSendBatch.setSendType(0);
        } else {
            userCouponSendBatch.setSendType(1);
        }
    }


    @Override
    public String uploadFile(MultipartFile file) {
        try {
            String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());
            Map<String, Object> map = AwsUtil.putS3Object(file, yyyyMM);
            return (String) map.get("url");
        } catch (Exception e) {
            log.error("file upload error", e);
            throw e;
        }
    }
}
