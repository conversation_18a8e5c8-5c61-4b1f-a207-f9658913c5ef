package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/22 10:27
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PolymerizationActivityBizService {
    private final PolymerizationActivityPoleService polymerizationActivityPoleService;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;

    /**
     * 获得聚合任务主活动id集合
     *
     * @return
     */
    public List<Long> findAllParentActivityId() {
        List<PolymerizationActivityPole> polymerizationActivityPoles = polymerizationActivityPoleService.findAllParentActivityIdByCreateRole();

        if (!CollectionUtils.isEmpty(polymerizationActivityPoles)) {
            List<Long> result = polymerizationActivityPoles.stream()
                    .map(PolymerizationActivityPole::getMainActivityId)
                    .collect(Collectors.toList());

            List<Long> elementsToRemove = new ArrayList<>();

            for (PolymerizationActivityPole pole : polymerizationActivityPoles) {
                List<ActivityPolymerizationRecord> records = activityPolymerizationRecordService.findByPole(pole.getId());
                if (CollectionUtils.isEmpty(records)) {
                    elementsToRemove.add(pole.getMainActivityId());
                }
                ActivityPolymerizationRecord activityPolymerizationRecord = records.stream()
                        .max(Comparator.comparing(ActivityPolymerizationRecord::getEndTime))
                        .orElse(null);
                if (activityPolymerizationRecord != null && activityPolymerizationRecord.getEndTime().toInstant().toEpochMilli() < ZonedDateTime.now().toInstant().toEpochMilli()) {
                    elementsToRemove.add(pole.getMainActivityId());
                }
            }

            result.removeAll(elementsToRemove);
            return result;
        }

        return new ArrayList<>();
    }
}
