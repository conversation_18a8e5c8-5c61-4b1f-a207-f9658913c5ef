package com.linzi.pitpat.data.bussiness;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.mapper.EggActivityConfigDao;
import com.linzi.pitpat.data.activityservice.model.entity.EggActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.awardservice.model.entry.ColorEggConfig;
import com.linzi.pitpat.data.awardservice.service.ColorEggConfigService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.robot.ActivityInfoForRobotDto;
import com.linzi.pitpat.data.robotservice.model.entity.Robot3dRoom;
import com.linzi.pitpat.data.robotservice.service.Robot3dRoomService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class UserOnlineBussiness {
    @Autowired
    private SocketPushUtils socketPushUtils;
    @Autowired
    private ZnsRunRouteService znsRunRouteService;
    @Value("${admin.server.gamepush}")
    private String gamepush;
    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private Robot3dRoomService robot3dRoomService;
    @Autowired
    private EggActivityConfigDao eggActivityConfigDao;
    @Autowired
    private ColorEggConfigService colorEggConfigService;
    @Autowired
    @Lazy
    private RoomIdBizService roomIdBizService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * @param znsRunActivityEntity 活动表
     * @param znsUserEntity        用户表
     * @param mindUserMatch        打日志使用
     * @param targetMileage        目标里程，主要是activity_type = 4 时，官方组队跑房间号需要使用targetMileage来构建房间号
     */

    public void robotUpOnline(ZnsRunActivityEntity znsRunActivityEntity, ZnsUserEntity znsUserEntity, MindUserMatch mindUserMatch
            , Integer targetMileage) {
        String key = RedisConstants.robot_online_lock_key + "_" + mindUserMatch.getId();
        RLock lock = redissonClient.getLock(key);

        try {
            if (LockHolder.tryLock(lock, 60, 60)) {
                Robot3dRoom robot3dRoomOld = robot3dRoomService.selectRobot3dRoomByMindUserMatchId(mindUserMatch.getId());
                if (robot3dRoomOld != null) {
                    log.info("robotUpOnline机器人已经上线了过了 " + mindUserMatch.getId());
                    return;
                }
                log.info("robotUpOnline 排行赛机器人开始添加到房间" + znsRunActivityEntity.getId() + ",userId = " + znsUserEntity.getId() + "，targetMileage=" + targetMileage);

                long roomId = mindUserMatch.getActivityId();
                if (Objects.equals(znsRunActivityEntity.getActivityType(), 3)) {  // 如果是排行赛事，3D 则默认房间号为-2
                    roomId = -2;
                } else if (Objects.equals(znsRunActivityEntity.getActivityType(), 4)) {
                    if (znsRunActivityEntity.getCompleteRuleType() == 1) {
                        roomId = targetMileage != null ? NumberUtils.getGoalImNumber(mindUserMatch.getActivityId(), targetMileage,
                                znsRunActivityEntity.getCompleteRuleType()) : mindUserMatch.getActivityId();
                    } else {
                        if (mindUserMatch.getTargetTime() != null && mindUserMatch.getTargetTime() > 0) {
                            roomId = NumberUtils.getGoalImNumber(mindUserMatch.getActivityId(), mindUserMatch.getTargetTime(),
                                    znsRunActivityEntity.getCompleteRuleType());
                        } else if (targetMileage != null && targetMileage > 0) {
                            roomId = NumberUtils.getGoalImNumber(mindUserMatch.getActivityId(), targetMileage,
                                    znsRunActivityEntity.getCompleteRuleType());
                        } else {
                            roomId = mindUserMatch.getActivityId();
                        }
                    }

                    //创建游戏房间(防止机器人进入房间时房间不存在)
                    String roomKey = RedisConstants.ACTIVITY_ROOM_FLAG + roomId;
                    String cacheVal = redisUtil.get(roomKey);
                    if (StringUtil.isEmpty(cacheVal)) {
                        //半小时内未创建过
                        try {
                            GamePushUtils.addRoom(gamepush, roomId, 2, null);

                            //缓存30分钟
                            redisUtil.set(roomKey, roomId, 30, TimeUnit.MINUTES);
                        } catch (Exception e) {
                            log.info("robotUpOnline 请求游戏服务器异常:{}", e.getMessage());
                        }
                    }

                }
                log.info("robotUpOnline addRobotToRankActivity 房间号为 " + roomId + ",userId = " + mindUserMatch.getUserId());
                // 如果是官方排行赛，2D 上线数据的房间号为 activityId ,但是3D 的房间号为-2
                socketPushUtils.onlinePush(Objects.equals(znsRunActivityEntity.getActivityType(), 3) ? mindUserMatch.getActivityId() + "" : roomId, znsUserEntity, 1);

                Robot3dRoom robot3dRoom = new Robot3dRoom();
                robot3dRoom.setRoomId(MapUtil.getLong(roomId, -3L));
                robot3dRoom.setUserId(znsUserEntity.getId());
                robot3dRoom.setStatus(1);
                robot3dRoom.setActivityId(znsRunActivityEntity.getId());
                robot3dRoom.setMatchId(mindUserMatch.getId());
                robot3dRoom.setLogNo(MDC.get("traceId"));
                robot3dRoom.setRoadId(znsRunActivityEntity.getActivityRouteId());
                robot3dRoomService.insertRobot3dRoom(robot3dRoom);

                ZnsRunRouteEntity znsRunRouteEntity = znsRunRouteService.selectRunRouteById(znsRunActivityEntity.getActivityRouteId());
                if (Objects.equals(znsRunRouteEntity.getRouteType(), 2)) {   // 表示3D路线
                    log.info("robotUpOnline 将机器人添加到3D房间 " + mindUserMatch.getId() + ", activityId = " + znsRunActivityEntity.getId());
                    // 排行赛3D游戏 ， addRobot 方法内部已经对Robot3dRoom添加了
                    GamePushUtils.addRobot(gamepush, roomId, znsRunActivityEntity.getActivityRouteId(), znsUserEntity, znsRunActivityEntity.getId(), mindUserMatch.getId());
                }
            } else {
                log.info("robotUpOnline 没有获得锁" + znsRunActivityEntity.getId() + ",userId = " + znsUserEntity.getId() + "，targetMileage=" + targetMileage);
            }
        } catch (Exception e) {
            log.error("robotUpOnline 异常", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void robotUpOnlineByActivityBean(ActivityInfoForRobotDto znsRunActivityEntity, ZnsUserEntity znsUserEntity, MindUserMatch mindUserMatch
            , Integer targetMileage) {
        String key = RedisConstants.robot_online_lock_key + "_" + mindUserMatch.getId();
        RLock lock = redissonClient.getLock(key);

        try {
            if (LockHolder.tryLock(lock, 60, 60)) {
                Robot3dRoom robot3dRoomOld = robot3dRoomService.selectRobot3dRoomByMindUserMatchId(mindUserMatch.getId());
                if (robot3dRoomOld != null) {
                    log.info("robotUpOnlineByActivityBean 机器人已经上线了过了 " + mindUserMatch.getId());
                    return;
                }
                log.info("robotUpOnlineByActivityBean 排行赛机器人开始添加到房间" + znsRunActivityEntity.getActivityId() + ",userId = " + znsUserEntity.getId() + "，targetMileage="
                        + targetMileage);

                Integer roomId = roomIdBizService.getRoomIdByActIdGoal(znsRunActivityEntity.getActivityId(), targetMileage);
                // -2 房间创建兼容逻辑
                if (!Objects.equals(-2, roomId)) {
                    log.info("robotUpOnlineByActivityBean roomId:{}", roomId);
                    //创建游戏房间(防止机器人进入房间时房间不存在)
                    String roomKey = RedisConstants.ACTIVITY_ROOM_FLAG + roomId;
                    String cacheVal = redisUtil.get(roomKey);
                    if (StringUtil.isEmpty(cacheVal)) {
                        //半小时内未创建过
                        try {
                            GamePushUtils.addRoom(gamepush, roomId, 2, null);
                            //缓存30分钟
                            redisUtil.set(roomKey, roomId, 30, TimeUnit.MINUTES);
                        } catch (Exception e) {
                            log.info("robotUpOnlineByActivityBean 请求游戏服务器异常:{}", e.getMessage());
                        }
                    }
                }
                log.info("robotUpOnlineByActivityBean addRobotToRankActivity 房间号为 " + roomId + ",userId = " + mindUserMatch.getUserId());
                // 如果是官方排行赛，2D 上线数据的房间号为 activityId ,但是3D 的房间号为-2
                socketPushUtils.onlinePush(Objects.equals(znsRunActivityEntity.getActivityType(), 3) ? mindUserMatch.getActivityId() + "" : roomId, znsUserEntity, 1);
                Robot3dRoom robot3dRoom = new Robot3dRoom();
                robot3dRoom.setRoomId(MapUtil.getLong(roomId, -3L));
                robot3dRoom.setUserId(znsUserEntity.getId());
                robot3dRoom.setStatus(1);
                robot3dRoom.setActivityId(znsRunActivityEntity.getActivityId());
                robot3dRoom.setMatchId(mindUserMatch.getId());
                robot3dRoom.setLogNo(MDC.get("traceId"));
                robot3dRoom.setRoadId(znsRunActivityEntity.getActivityRouteId());
                robot3dRoomService.insertRobot3dRoom(robot3dRoom);

                ZnsRunRouteEntity znsRunRouteEntity = znsRunRouteService.selectRunRouteById(znsRunActivityEntity.getActivityRouteId());
                if (Objects.equals(znsRunRouteEntity.getRouteType(), 2)) {   // 表示3D路线
                    log.info("robotUpOnlineByActivityBean 将机器人添加到3D房间 " + mindUserMatch.getId() + ", activityId = " + znsRunActivityEntity.getActivityId());
                    // 排行赛3D游戏 ， addRobot 方法内部已经对Robot3dRoom添加了
                    GamePushUtils.addRobot(gamepush, roomId, znsRunActivityEntity.getActivityRouteId(), znsUserEntity, znsRunActivityEntity.getActivityId(), mindUserMatch.getId());
                }
            } else {
                log.info(
                        "robotUpOnlineByActivityBean 没有获得锁" + znsRunActivityEntity.getActivityId() + ",userId = " + znsUserEntity.getId() + "，targetMileage=" + targetMileage);
            }
        } catch (Exception e) {
            log.error("robotUpOnlineByActivityBean 异常", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public void eggActivityPushDearRunSuperRun(Long activityId) {
        ZonedDateTime date = ZonedDateTime.now();
        List<EggActivityConfig> list = eggActivityConfigDao.selectEggActivityConfigListByActivityId(activityId, date, date);
        for (EggActivityConfig eggActivityConfig : list) {
            Long routeId = eggActivityConfig.getRouteId();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("eggActivityPushDearRunSuperRun 异常", e);
            }
            ZonedDateTime activityStartTime = eggActivityConfig.getGmtStartTime();
            ColorEggConfig colorEggConfig = colorEggConfigService.selectColorEggConfigById(eggActivityConfig.getColorEggConfigId());
            GamePushUtils.popColorEgg(gamepush, colorEggConfig.getRule(), colorEggConfig.getType(), colorEggConfig.getAwardValue(),
                    colorEggConfig.getRPosition(), colorEggConfig.getRAwardCount(), colorEggConfig.getSingleCount(),
                    colorEggConfig.getFirstAppearSecond(), eggActivityConfig.getRoomId() + "", activityId, routeId,
                    eggActivityConfig.getId(), colorEggConfig.getIntervalAppearSecond(), colorEggConfig.getValidTime(),
                    DateUtil.getTime(activityStartTime),
                    eggActivityConfig.getColorEggConfigId(),
                    DateUtil.getTime(eggActivityConfig.getGmtEndTime()), colorEggConfig.getPointValue(), colorEggConfig.getUseType());
        }
    }


}
