package com.linzi.pitpat.data.awardservice.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.data.awardservice.dto.ActivityUserPairDto;
import com.linzi.pitpat.data.awardservice.dto.UserAmountPair;
import com.linzi.pitpat.data.awardservice.model.dto.ActivitySumAwardBySubTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.query.AccountDetailPo;
import com.linzi.pitpat.data.awardservice.model.vo.AccountTotalVo;
import com.linzi.pitpat.data.awardservice.model.vo.AccountWithdrawalVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserAccountSimpleVo;
import com.linzi.pitpat.data.entity.dto.AppUserDto;
import com.linzi.pitpat.data.entity.vo.UserConsumeStatisticVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentModelAwardDataVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentAwardDataVo;
import com.linzi.pitpat.data.resp.LoopsResp;
import com.linzi.pitpat.data.resp.RunAwardDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.vo.useractive.RunDataYearVo;
import com.lz.mybatis.plugin.annotations.AS;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.Sum;
import com.lz.mybatis.plugin.annotations.Where;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户账户明细表
 *
 * <AUTHOR>
 * @date 2021-12-29 09:58:56
 */
@Mapper
public interface ZnsUserAccountDetailDao extends BaseMapper<ZnsUserAccountDetailEntity> {


    BigDecimal selectAccountDetailByTradeTypeRef(@Param("userId") Long userId, @Param("tradeType") Integer tradeType, @Param("refId") Long refId,
                                                 @Param("createTime") ZonedDateTime createTime, @Param("dateType") String dateType);


    /**
     * 用户消费统计
     */
    UserConsumeStatisticVo userConsumptionStatistic(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime);

    List<AccountWithdrawalVo> userAccountList(@Param("po") AccountDetailPo po);

    @Sum("amount")
    BigDecimal selectAccountDetailByUserIdTradeType(Long userId, @IN List<Integer> tradeType, @IF Long userAccountId);

    List<UserAccountSimpleVo> getLastUserAccountDetail(@Param("startTime") ZonedDateTime startTime);

    Page<AccountWithdrawalVo> userAccountPage(Page<AccountWithdrawalVo> page, @Param("po") AccountDetailPo po);

    Map<String, Object> getMilepostAwardReport(@Param("refId") Long refId, @Param("type") Integer type, @Param("subType") int subType, @Param("isRealUser") Integer isRealUser);

    BigDecimal selectAccountDetailByUserIdTypeRefIdsDateRule(@Param("userId") Long userId, @Param("tradeType") Integer tradeType, @Param("createTime") ZonedDateTime createTime, @Param("dateType") String dateType);

    Integer selectAccountDetailByUserIdTypeRefIdsDateRuleCount(@Param("userId") Long userId, @Param("tradeType") Integer tradeType, @Param("createTime") ZonedDateTime createTime, @Param("dateType") String dateType);

    @DS("slave")
    BigDecimal selectAccountByGmtCreateIsRobotTradeType(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("isRobot") Integer isRobot, @Param("tradeType") Integer tradeType);

    @AS("uad")
    @Mapping(" ifnull(sum(uad.amount), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @Where(" uad.is_delete = 0 and u.is_robot=0 and   ")
    @DS("slave")
    BigDecimal selectAccountByTradeType(@IF Long userId, Integer tradeType);


    @AS("uad")
    @Mapping(" ifnull(count(1), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @Where(" uad.is_delete = 0 and u.is_robot=0 and   ")
    @DS("slave")
    Integer countAccountByTradeType(@IF Long userId, Integer tradeType);


    @AS("uad")
    @Mapping(" ifnull(sum(uad.amount), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @Where(" uad.is_delete = 0 and u.is_robot=0 and   ")
    @DS("slave")
    BigDecimal selectAccountByCreateTimeTradeType(@IF Long userId, @DateFormat @GE ZonedDateTime createTime, Integer tradeType);


    @AS("uad")
    @Mapping(" ifnull(count(1), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @Where(" uad.is_delete = 0 and u.is_robot=0 and   ")
    @DS("slave")
    Integer countAccountByCreateTimeTradeType(@IF Long userId, @DateFormat @GE ZonedDateTime createTime, Integer tradeType);


    @AS("uad")
    @Mapping(" ifnull(sum(uad.amount), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @Where(" uad.is_delete = 0 and u.is_robot=0 and   ")
    @DS("slave")
    BigDecimal selectAccountByTradeTypeSubType(@IF @GE @Column("uad.create_time") ZonedDateTime gmtStartTime, @LE @IF @Column("uad.create_time") ZonedDateTime gmtEndTime, Integer tradeType, @IF String tradeSubtype);


    @AS("uad")
    @Mapping(" ifnull(count(1), 0) ")
    @LeftJoinOns({
            @Item(value = ZnsUserEntity.class, as = "u", on = " uad.user_id = u.id "),
    })
    @Where(" uad.is_delete = 0 and u.is_robot=0 and  ")
    @DS("slave")
    Integer countAccountByTradeTypeTradeSubtype(Integer tradeType, List<Integer> tradeSubtype, @IF Long userId);


    Page<RunAwardDto> selectPageByCondition(IPage page, @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime, @Param("activityNo") String activityNo);

    List<LoopsResp> selectAccountByBatchNo(@Param("batchNo") String batchNo);

    @Sum("amount")
    BigDecimal selectAccountByActivityId(Long activityId, Integer tradeType);

    BigDecimal sumOfficialActivityAward(@Param("tradeType") Integer tradeType, @Param("refId") Long refId, @Param("isRealUser") Integer isRealUser);

    List<AppUserDto> getWithdrawal(@Param("userIds") List<Long> userIds);

    List<ZnsUserAccountDetailEntity> getChallengeAmount(@Param("userIds") List<Long> userIds, @Param("activityId") Long activityId);

    @Sum(ZnsUserAccountDetailEntity.amount_)
    BigDecimal selectAccountByActivityIdUserIdTradeType(Long refId, Long userId, @IN List<Integer> tradeType);


    @OrderByIdDescLimit_1
    ZnsUserAccountDetailEntity selectAccountDetailByBillNo(String billNo);

    List<ActivitySumAwardBySubTypeDto> selectSumByActivityIdTradeType(@Param("activityId") Long activityId, @Param("tradeType") Integer tradeType, @Param("isRealUser") Integer isRealUser);

    @Sum(ZnsUserAccountDetailEntity.amount_)
    BigDecimal selectSumByUserIdActivityType(Integer activityType, Long userId, Integer type, @IF Integer refundStatus);

    @Mapping(" ifnull(sum(amount),0)")
    BigDecimal selectSumAward(Long activityId, @IF Long userId, @IN @IF List<Integer> tradeType, @IF Integer tradeStatus);

    /**
     * 支付成功
     *
     * @param id
     * @param tradeStatus
     * @param tradeTime
     * @param tradeSuccessTime
     * @return
     */
    int successPayment(@Param("id") Long id, @Param("tradeStatus") Integer tradeStatus, @Param("tradeTime") ZonedDateTime tradeTime, @Param("tradeSuccessTime") ZonedDateTime tradeSuccessTime);

    BigDecimal selectMonthReportAccountData(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    /**
     * 多币种初始化-更新资金记录
     *
     * @param exchangeRate
     * @param userId
     * @return
     */
    int updateUserAccountDetailByCurrency(@Param("exchangeRate") BigDecimal exchangeRate, @Param("userId") Long userId);

    @Mapping("sum(amount)")
    BigDecimal selectSumAwardByActivityIds(@IN List<Long> activityId, Long userId, @IN @IF List<Integer> tradeType, @IF Integer tradeStatus);

    RunDataYearVo selectMostAmountDay(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    /**
     * 查询活动指定类型的交易金额
     *
     * @param activityId
     * @param tradeTypes
     * @param tradeStatus
     * @return
     */
    List<CurrencyAmount> findActCurrencyAmountByTradeType(@Param("activityId") Long activityId, @Param("tradeTypes") List<Integer> tradeTypes, @Param("tradeStatus") Integer tradeStatus);

    /**
     * 通过资金明细计算 总支出、总收入
     *
     * @param userId
     * @return
     */
    List<AccountTotalVo> getUserAccountTotalVo(@Param("userId") Long userId);

    /**
     * 合并资金记录
     *
     * @param newAccountId
     * @param newUserId
     * @param oldUserIds
     */
    void mergeAccountDetail(Long newAccountId, Long newUserId, List<Long> oldUserIds);

    /**
     * 查询设备奖励数据
     */
    List<EquipmentModelAwardDataVo> findEquipmentModelAwardData(@Param("userId") Long userId);

    /**
     * 获取前一天的设备奖励数据
     */
    List<UserEquipmentAwardDataVo> findUserEquipmentAwardVo(@Param("startDate") ZonedDateTime startDate);

    List<UserAmountPair> sumByActivityIdTradeTypeAndUserIds(@Param("list") List<ActivityUserPairDto> loadData, @Param("type") Integer type);
}
