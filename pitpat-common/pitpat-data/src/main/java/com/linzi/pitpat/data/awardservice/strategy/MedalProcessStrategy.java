package com.linzi.pitpat.data.awardservice.strategy;

import com.linzi.pitpat.data.activityservice.model.query.award.AwardDoProcessResultDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendProcessDto;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entity.AwardConfigMedalDo;
import com.linzi.pitpat.data.awardservice.service.AwardConfigMedalService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("medalProcessStrategy")
@Slf4j
@RequiredArgsConstructor
public class MedalProcessStrategy extends AbstractAwardProcessStrategy implements InitializingBean {
    public final UserMedalService userMedalService;
    private final AwardConfigMedalService awardConfigMedalService;
    @Override
    public void doProcess(AwardSendDto dto, List<Long> list, String batchNo) {
        Long userId = dto.getUserId();
        list.forEach(i -> {
            AwardConfigMedalDo configMedalDo = awardConfigMedalService.findByAwardConfigId(i);
            if (configMedalDo != null && configMedalDo.getMedalId() != null && configMedalDo.getMedalId() > 0) {
                userMedalService.sendUserMedal(userId, configMedalDo.getMedalId());
            }
        });
    }

    @Override
    protected AwardDoProcessResultDto doProcess(AwardSendProcessDto awardSendProcessDto) {
        Long userId = awardSendProcessDto.getUserId();
        AwardDoProcessResultDto awardDoProcessResultDto = new AwardDoProcessResultDto();
        Long medalId = Long.parseLong(awardSendProcessDto.getAwardValue());
        userMedalService.sendUserMedal(userId, medalId);
        // 奖励金抽奖
        return awardDoProcessResultDto;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        AwardProcessStrategyFactory.register(AwardTypeEnum.MEDAL.getType(), this);
    }

}
