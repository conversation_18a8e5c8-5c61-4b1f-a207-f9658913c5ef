package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 用户跑每秒跑步数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataEveryEverySecond;
import com.lz.mybatis.plugin.annotations.Avg;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LT;
import com.lz.mybatis.plugin.annotations.NE;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.Realy;
import org.apache.ibatis.annotations.Mapper;

import java.time.ZonedDateTime;

@Mapper
public interface UserRunDataEveryEverySecondDao extends BaseMapper<UserRunDataEveryEverySecond> {

    Long insertUserRunDataEveryEverySecond(UserRunDataEveryEverySecond userRunDataEveryEverySecond);

    @Avg("v")
    int selectUserRunDataEveryEverySecondByGmtCreateActivityIdUserIdAvg(@GE @DateFormat ZonedDateTime gmtCreate, Long activityId, @NE Long userId);

    @OrderByIdDescLimit_1
    UserRunDataEveryEverySecond selectUserRunDataEveryEverySecondByActivityIdUserIdGmtCreateOrderByIdDesc(Long activityId, Long userId, @DateFormat @LE ZonedDateTime gmtCreate);


    @Realy
    void deleteUserRunDataEveryEverySecond7Days(@LT @DateFormat ZonedDateTime gmtCreate);


}
