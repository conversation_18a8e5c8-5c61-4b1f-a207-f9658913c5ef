package com.linzi.pitpat.data.awardservice.manager.console;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponSendBatchConstant;
import com.linzi.pitpat.data.awardservice.dto.consloe.request.ArtificialSendAwardRequestDto;
import com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendBatch;
import com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendBatchConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendDetail;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponSendBatchAddReq;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponSendBatchChangeStatusReq;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponSendBatchUpdateReq;
import com.linzi.pitpat.data.awardservice.model.vo.ManualSendCashAwardVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponSendDetailVo;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponSendBatchConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponSendBatchService;
import com.linzi.pitpat.data.awardservice.service.UserCouponSendDetailService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.DingTalkTokenEnum;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserBizService;
import com.linzi.pitpat.data.util.file.FileUtils;
import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.service.ExcelService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/19 17:50
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserCouponSendBatchManager {
    private final UserCouponSendBatchService userCouponSendBatchService;
    private final ZnsUserService znsUserService;
    private final UserCouponSendDetailService userCouponSendDetailService;
    private final UserCouponSendBatchConfigService userCouponSendBatchConfigService;
    private final UserWearsBagService userWearsBagService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserMedalService userMedalService;
    private final CouponService couponService;
    private final UserCouponBizService userCouponBizService;
    private final VipUserBizService vipUserBizService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ExcelService excelService;
    private final RedissonClient redissonClient;

    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(UserCouponSendBatchChangeStatusReq req, SysUser user) {
        String lockKey = RedisConstants.MANUAL_SEND_AWARD + req.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean flag = false;
        try {
            flag = lock.tryLock(1L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BaseException("正在生效中，请勿重复点击");
            }
            UserCouponSendBatch userCouponSendBatch = userCouponSendBatchService.getById(req.getId());
            if (Objects.isNull(userCouponSendBatch)) {
                throw new BaseException("主键错误,发放实体不存在");
            }
            if (Objects.equals(userCouponSendBatch.getStatus(), req.getStatus())) {
                throw new BaseException("状态已经修改，请勿重复操作");
            }
            userCouponSendBatch.setStatus(req.getStatus());
            userCouponSendBatch.setReviewer(user.getUserName());
            userCouponSendBatch.setGmtReview(ZonedDateTime.now());
            if (req.getStatus() == 1) {
                sendAward(userCouponSendBatch);
            }
            if (Objects.nonNull(userCouponSendBatch.getActivityId())) {
                dingTalk(userCouponSendBatch);
            }
            return userCouponSendBatchService.updateById(userCouponSendBatch);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        } finally {
            if (flag) {
                lock.unlock();
            }
        }
    }

    private void sendAward(UserCouponSendBatch userCouponSendBatch) {
        List<UserCouponSendDetail> oldList = userCouponSendDetailService.list(new QueryWrapper<UserCouponSendDetail>().eq("is_delete", 0).eq("batch_id", userCouponSendBatch.getId()));
        if (CollectionUtils.isEmpty(oldList)) {
            log.info("发放记录为空");
            return;
        }
        if (Objects.nonNull(userCouponSendBatch.getActivityId())) {
            userCouponSendBatch.setSendCount(oldList.size());
            for (UserCouponSendDetail userCouponSendDetail : oldList) {
                userAccountService.increaseAmount(userCouponSendDetail.getSendAmount(), userCouponSendDetail.getUserId(), true);
                addAccountDetail(userCouponSendDetail.getUserId(), 1,
                        AccountDetailTypeEnum.findByType(userCouponSendBatch.getTradeType()), userCouponSendBatch.getTradeSubype(),
                        userCouponSendDetail.getSendAmount(), userCouponSendBatch.getActivityId(), userCouponSendBatch.getTitle());
            }

        } else {
            List<Long> userList = oldList.stream().map(UserCouponSendDetail::getUserId).toList();
            List<UserCouponSendBatchConfig> configList = userCouponSendBatchConfigService.findByBatchId(userCouponSendBatch.getId());
            userList.parallelStream().forEach(userId -> {
                //发放服装奖励
                List<WearAwardDto> wearAwardDtos = JsonUtil.readList(userCouponSendBatch.getWears(), WearAwardDto.class);
                if (!CollectionUtils.isEmpty(wearAwardDtos)) {
                    wearAwardDtos.forEach(wearAwardDto -> userWearsBagService.sendUserWear(userId, wearAwardDto, null));
                }
                //发放积分奖励
                if (Objects.nonNull(userCouponSendBatch.getScore()) && userCouponSendBatch.getScore() > 0) {
                    activityUserScoreService.increaseAmount(userCouponSendBatch.getScore(), null, userId, null, null, ScoreConstant.SourceTypeEnum.source_type_30.getType());
                }
                //发放勋章奖励
                List<Long> medals = JsonUtil.readList(userCouponSendBatch.getMedalIds(), Long.class);
                if (!CollectionUtils.isEmpty(medals)) {
                    medals.forEach(medalId -> userMedalService.sendUserMedal(userId, medalId));
                }
                //发放券奖励
                if (!CollectionUtils.isEmpty(configList)) {
                    configList.forEach(config -> {
                        long couponId = config.getCouponId();
                        int num = config.getNum();
                        for (int i = 0; i < num; i++) {
                            userCouponBizService.sendUserCouponSource(couponId, userId, null, CouponConstant.SourceTypeEnum.source_type_30.getType(), true);
                        }
                    });
                }
                //发放会员
                if (Objects.equals(userCouponSendBatch.getVipSendType(), 0) && userCouponSendBatch.getVipDays() > 0) {
                    vipUserBizService.addSpecifiedDayMember(userId, userCouponSendBatch.getVipDays());
                } else if (Objects.equals(userCouponSendBatch.getVipSendType(), 1)) {
                    vipUserBizService.addSpecifiedDayMember(userId, null);
                }


            });
            if (!CollectionUtils.isEmpty(configList)) {
                configList.forEach(s ->
                        couponService.updateQuota(s.getCouponId(), (long) userList.size() * s.getNum()));
            }
            userCouponSendBatch.setSendCount(userList.size());
        }

        userCouponSendBatch.setGmtSend(ZonedDateTime.now());
        userCouponSendBatch.setSendStatus(1);
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchSendById(UserCouponSendBatchUpdateReq req, MultipartFile file, SysUser user) {
        UserCouponSendBatch userCouponSendBatch = userCouponSendBatchService.getById(req.getId());
        if (Objects.equals(req.getIsDelete(), YesNoStatus.YES.getCode())) {
            return userCouponSendBatchService.deleteUserCouponSendBatchById(req.getId());
        }
        String[] ignoreFields = {"couponIds", "wears", "medalIds"};
        BeanUtils.copyProperties(req, userCouponSendBatch, ignoreFields);
        userCouponSendBatchService.fillField(req, userCouponSendBatch);
        userCouponSendBatch.setModifier(user.getUserName());
        List<String> list = new ArrayList<>();
        if (Objects.nonNull(file)) {
            list.addAll(FileUtils.readFirstColumn(file));
            String uploaded = userCouponSendBatchService.uploadFile(file);
            userCouponSendBatch.setFileUrl(uploaded);
        }
        if (StringUtils.hasText(req.getPushUserEmails())) {
            List<String> pushEmails = Arrays.asList(req.getPushUserEmails().split(","));
            list.addAll(pushEmails);
        }
        list = list.stream().distinct().collect(Collectors.toList());
        List<UserCouponSendDetail> detailList = userCouponSendDetailService.findByBatchId(userCouponSendBatch.getId());
        List<UserCouponSendBatchConfig> configList = userCouponSendBatchConfigService.findByBatchId(userCouponSendBatch.getId());
        List<Long> detailCollect = (!CollectionUtils.isEmpty(detailList)) ? detailList.stream().map(UserCouponSendDetail::getId).collect(Collectors.toList()) : new ArrayList<>();
        List<Long> configCollect = (!CollectionUtils.isEmpty(configList)) ? configList.stream().map(UserCouponSendBatchConfig::getId).collect(Collectors.toList()) : new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailCollect)) {
            userCouponSendDetailService.removeBatchByIds(detailCollect);
        }
        if (CollectionUtils.isNotEmpty(configCollect)) {
            userCouponSendBatchConfigService.removeBatchByIds(configCollect);
        }
        //用户信息保存
        list.forEach(i -> saveSendDetailToDb(userCouponSendBatch, i, 1));
        userCouponSendBatch.setSendCount(list.size());
        //券信息保存
        saveSendCouponConfig(req, userCouponSendBatch);
        return userCouponSendBatchService.updateById(userCouponSendBatch);
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean addNew(UserCouponSendBatchAddReq userCouponSendBatchAddReq, MultipartFile file, SysUser user) {
        UserCouponSendBatch userCouponSendBatch = new UserCouponSendBatch();
        String[] ignoreFields = {"couponIds", "wears", "medalIds"};
        BeanUtils.copyProperties(userCouponSendBatchAddReq, userCouponSendBatch, ignoreFields);
        userCouponSendBatch.setCreator(user.getUserName());
        userCouponSendBatch.setModifier(user.getUserName());
        userCouponSendBatch.setStatus(0);
        userCouponSendBatchService.fillField(userCouponSendBatchAddReq, userCouponSendBatch);
        List<String> list = new ArrayList<>();
        if (StringUtils.hasText(userCouponSendBatchAddReq.getPushUserEmails())) {
            List<String> pushEmails = Arrays.asList(userCouponSendBatchAddReq.getPushUserEmails().split(","));
            list.addAll(pushEmails);
        }
        if (Objects.nonNull(file)) {
            String uploaded = userCouponSendBatchService.uploadFile(file);
            userCouponSendBatch.setFileUrl(uploaded);
            list.addAll(FileUtils.readFirstColumn(file));
        }
        list = list.stream().distinct().collect(Collectors.toList());
        userCouponSendBatch.setSendCount(list.size());
        boolean save = userCouponSendBatchService.save(userCouponSendBatch);
        list.forEach(i -> saveSendDetailToDb(userCouponSendBatch, i, 1));
        saveSendCouponConfig(userCouponSendBatchAddReq, userCouponSendBatch);
        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    public String artificialSendAward(MultipartFile file, ArtificialSendAwardRequestDto requestDto, String operator) {
        List<ManualSendCashAwardVo> artificialSendAwardVos = getArtificialSendAwardVoList(file, requestDto);

        if (org.springframework.util.CollectionUtils.isEmpty(artificialSendAwardVos)) {
            return "未解析到数据";
        }
        UserCouponSendBatch userCouponSendBatch = new UserCouponSendBatch();
        userCouponSendBatch.setCreator(operator);
        userCouponSendBatch.setModifier(operator);
        userCouponSendBatch.setStatus(0);
        userCouponSendBatch.setSendStatus(0);
        userCouponSendBatch.setTitle(requestDto.getAwardTitle());
        userCouponSendBatch.setActivityId(requestDto.getActivityId());
        userCouponSendBatch.setTradeType(requestDto.getTradeType());
        userCouponSendBatch.setTradeSubype(requestDto.getTradeSubype());
        userCouponSendBatch.setTotalAmount(requestDto.getTotalAmount());
        userCouponSendBatchService.save(userCouponSendBatch);

        List<UserCouponSendDetail> detailList = new ArrayList<>();
        for (ManualSendCashAwardVo artificialSendAwardVo : artificialSendAwardVos) {
            if (Objects.isNull(artificialSendAwardVo.getUserId())) {
                continue;
            }
            UserCouponSendDetail userCouponSendDetail = new UserCouponSendDetail();
            userCouponSendDetail.setBatchId(userCouponSendBatch.getId());
            userCouponSendDetail.setUserId(artificialSendAwardVo.getUserId());
            userCouponSendDetail.setEmailAddress(artificialSendAwardVo.getEmailAddress());
            userCouponSendDetail.setSendAmount(artificialSendAwardVo.getSendAmount());
            detailList.add(userCouponSendDetail);
        }
        userCouponSendDetailService.saveBatch(detailList);
        log.info("奖励发放添加成功,任务ID：{},数量：{}", userCouponSendBatch.getId(), artificialSendAwardVos.size());

        dingTalk(userCouponSendBatch);

        return "奖励发放添加成功，数量：" + artificialSendAwardVos.size();
    }

    private static void dingTalk(UserCouponSendBatch userCouponSendBatch) {
        String token = DingTalkTokenEnum.AMOUNT_SEND_ONLINE.getToken();
        String secret = DingTalkTokenEnum.AMOUNT_SEND_ONLINE.getSecret();
        if (!EnvUtils.isReallyOnline(SpringContextUtils.getActiveProfile())) {
            log.warn("当前环境不是线上，忽略提醒,env={}", SpringContextUtils.getActiveProfile());
            token = DingTalkTokenEnum.AMOUNT_SEND_TEST.getToken();
            secret = DingTalkTokenEnum.AMOUNT_SEND_TEST.getSecret();
        }

        String msg = null;
        if (userCouponSendBatch.getStatus() == 0) {
            String msgTpl = """
                    ### 现金任务-%s
                    #### 任务标题: %s
                    #### 发放金额: %s
                    #### 创建人:%s
                    """;

            msg = String.format(msgTpl, UserCouponSendBatchConstant.Status.getStatus(userCouponSendBatch.getStatus()).getNoticeName(),
                    userCouponSendBatch.getTitle(), userCouponSendBatch.getTotalAmount(), userCouponSendBatch.getCreator());
        } else {
            String msgTpl = """
                    ### 现金任务-%s
                    #### 任务标题: %s
                    #### 发放金额: %s
                    #### 创建人:%s
                    #### 审批人:%s
                    """;
            msg = String.format(msgTpl, UserCouponSendBatchConstant.Status.getStatus(userCouponSendBatch.getStatus()).getNoticeName(),
                    userCouponSendBatch.getTitle(), userCouponSendBatch.getTotalAmount(), userCouponSendBatch.getCreator(), userCouponSendBatch.getReviewer());
        }

        DingTalkUtils.sendMsg(DingTalkRequestDto.ofMarkdown(token, secret, JsonUtil.writeString(msg)));

    }

    public static void main(String[] args) {
        UserCouponSendBatch userCouponSendBatch = new UserCouponSendBatch();
        userCouponSendBatch.setTitle("测试");
        userCouponSendBatch.setStatus(0);
        userCouponSendBatch.setTotalAmount(new BigDecimal(100));
        userCouponSendBatch.setCreator("xiaop");
        userCouponSendBatch.setReviewer("xiaop");
        dingTalk(userCouponSendBatch);
        userCouponSendBatch.setStatus(1);
        dingTalk(userCouponSendBatch);
        userCouponSendBatch.setStatus(2);
        userCouponSendBatch.setTotalAmount(new BigDecimal("100.02"));
        dingTalk(userCouponSendBatch);
    }

    /**
     * 保存发送明细
     *
     * @param userCouponSendBatch
     * @param userEmail
     * @param version             //	 * @param u
     */
    private void saveSendDetailToDb(UserCouponSendBatch userCouponSendBatch, String userEmail, int version) {
        UserCouponSendDetail userCouponSendDetail = new UserCouponSendDetail();
        userCouponSendDetail.setUserId(znsUserService.findByEmail(userEmail).getId());
//		userCouponSendDetail.setCouponId(u.getCouponId());
        userCouponSendDetail.setStatus(0);
        userCouponSendDetail.setBatchId(userCouponSendBatch.getId());
        userCouponSendDetail.setGmtSend(userCouponSendBatch.getGmtSend());
        userCouponSendDetail.setVersion(version);
        userCouponSendDetailService.save(userCouponSendDetail);
    }

    private void saveSendCouponConfig(UserCouponSendBatchAddReq userCouponSendBatchAddReq, UserCouponSendBatch userCouponSendBatch) {
        List<UserCouponSendDetailVo> userCouponSendDetailVos = JsonUtil.readList(userCouponSendBatchAddReq.getCouponIds(), UserCouponSendDetailVo.class);
        userCouponSendDetailVos.forEach(s -> {
            UserCouponSendBatchConfig userCouponSendBatchConfig = new UserCouponSendBatchConfig();
            userCouponSendBatchConfig.setBatchId(userCouponSendBatch.getId());
            userCouponSendBatchConfig.setCouponId(s.getCouponId());
            userCouponSendBatchConfig.setNum(s.getNum());
            userCouponSendBatchConfigService.insertUserCouponSendBatchConfig(userCouponSendBatchConfig);
        });
    }

    public void addAccountDetail(Long userId, int type, AccountDetailTypeEnum typeEnum, Integer tradeSubype, BigDecimal amount, Long activityId, String remark) {
        ZnsUserEntity user = znsUserService.findById(userId);
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime now = ZonedDateTime.now();
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        ZnsUserAccountEntity accountEntity = userAccountService.selectUserAccountByUserId(userId);
        if (Objects.nonNull(accountEntity)) {
            amount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), amount);
            userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        }
        userAccountDetailEntity.setCreateTime(now);
        userAccountDetailEntity.setModifieTime(now);
        userAccountDetailEntity.setUserId(userId);
        userAccountDetailEntity.setType(type);
        userAccountDetailEntity.setTitle(remark);
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setBillNo(billNo);
        userAccountDetailEntity.setTradeType(typeEnum.getType());
        userAccountDetailEntity.setTradeSubtype(tradeSubype);
        userAccountDetailEntity.setRemark(remark);
        userAccountDetailEntity.setPaypalAccount("");
        userAccountDetailEntity.setTradeStatus(2);
        userAccountDetailEntity.setTradeTime(ZonedDateTime.now());
        userAccountDetailEntity.setActivityId(activityId);
        userAccountDetailEntity.setIsTest(user.getIsTest());
        userAccountDetailEntity.setIsRobot(user.getIsRobot());
        userAccountDetailService.save(userAccountDetailEntity);
    }


    private List<ManualSendCashAwardVo> getArtificialSendAwardVoList(MultipartFile file, ArtificialSendAwardRequestDto requestDto) {

        if (!Objects.equals(requestDto.getUserFiledType(), 2)) {
            throw new BaseException("仅支持邮箱导入");
        }

        ExcelImportResult<ManualSendCashAwardVo> response = excelService.importExcel(file, ManualSendCashAwardVo.class);

        List<ManualSendCashAwardVo> artificialSendAwardEmailVos = validateResponse(response);

        List<String> emailList = artificialSendAwardEmailVos.stream().map(ManualSendCashAwardVo::getEmailAddress).collect(Collectors.toList());
        Map<String, Long> idMap = znsUserService.findList(UserQuery.builder().emailAddresses(emailList).build()).stream().collect(Collectors.toMap(ZnsUserEntity::getEmailAddress, ZnsUserEntity::getId));

        return artificialSendAwardEmailVos.stream().map(a -> {
            a.setUserId(idMap.get(a.getEmailAddress()));
            return a;
        }).collect(Collectors.toList());
    }

    private static <T> List<T> validateResponse(ExcelImportResult<T> response) {
        if (CollectionUtils.isEmpty(response.getAllData())) {
            throw new BizException("未解析到数据");
        }
        if (!CollectionUtils.isEmpty(response.getInvalidData())) {
            throw new BizException(response.getErrorMessages().toString());
        }
        return response.getValidData();
    }
}
