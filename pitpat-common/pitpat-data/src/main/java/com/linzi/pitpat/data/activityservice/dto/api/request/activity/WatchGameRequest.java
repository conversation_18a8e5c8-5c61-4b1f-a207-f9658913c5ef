package com.linzi.pitpat.data.activityservice.dto.api.request.activity;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/11 10:02
 */
@Data
@NoArgsConstructor
public class WatchGameRequest extends PageQuery {
    //活动id
    private Long activityId;
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 观赛进入时间
     */
    private ZonedDateTime joinTime;
    // 观赛退出时间
    private ZonedDateTime exitTime;
    /**
     * 进入跟跑模式时间
     */
    private ZonedDateTime joinFollowTime;
    /**
     * 跟跑模式 0 ：不跟跑，1：跟跑
     */
    private Integer followType;
}
