package com.linzi.pitpat.data.awardservice.model.vo;

import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2024/1/28 13:28
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ActivityWearAward extends WearAward {
    /**
     * 唯一表示，wearType + "_" + wearId
     */
    private String key;
    /**
     * 活动id
     */
    private Long activityId;
    //活动开始时间
    private ZonedDateTime startTime;
    //活动创建时间
    private ZonedDateTime creatTime;
    //rank排位赛 single单赛事 series-main系列赛主赛事 series-sub系列赛阶段赛事old老赛事
    private String mainType;
    /**
     * 活动类型：1 表示组队跑，2表示挑战跑，3表示官方赛事 4；官方组队跑 5：累计跑 7：一周快乐跑 10 团队赛
     *
     * @see RunActivityTypeEnum
     */
    private Integer activityType;
}
