package com.linzi.pitpat.data.config;

import com.linzi.pitpat.framework.web.task.TransmittableDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.instrument.async.LazyTraceAsyncCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;


@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {
    @Autowired
    private BeanFactory beanFactory;

    @Bean("asyncExecutor")
    public ThreadPoolTaskExecutor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("async-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(500);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("createRoomExecutor")
    public ThreadPoolTaskExecutor createRoomExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("create-room-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(500);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("roomTagExecutor")
    public ThreadPoolTaskExecutor roomTagExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("room-tag-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(500);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 钉钉专用
     *
     * @return
     */
    @Bean("dingtalkExecutor")
    public ThreadPoolTaskExecutor dingtalkExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("dingtalk-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(1000);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());//超时丢弃最老的请求
        executor.initialize();
        return executor;
    }

    /**
     * 用户标签专用线程池
     *
     * @return
     */
    @Bean("labelExecutor")
    public ThreadPoolTaskExecutor labelExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("label-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() + 2);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() + 2);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(500);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("mail-executor")
    public ThreadPoolTaskExecutor mailExecutors() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("mail-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setKeepAliveSeconds(60);
        //不可设置过大，暂时先设置为10
        executor.setQueueCapacity(100);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("taskExecutor-");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 4 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(10000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TransmittableDecorator());

        executor.initialize();
        return executor;
    }

    @Bean("slowSqlExecutor")
    public ThreadPoolTaskExecutor slowSqlExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("slowSqlExecutor-");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() + 1);
        executor.setQueueCapacity(1000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.initialize();
        return executor;
    }

    @Bean("trackerMaiDianExecutor")
    public ThreadPoolTaskExecutor trackerMaiDianExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("trackerExecutor-maiDian-");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 4 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(10000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TransmittableDecorator());
        return executor;
    }

    @Bean("completeFutureExecutor")
    public ThreadPoolTaskExecutor completeFutureExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //executor.set
        // 核心线程数：线程池创建时候初始化的线程数
        executor.setCorePoolSize(20);
        // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(20);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(0);
        // 允许线程的空闲时间10秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
        executor.setKeepAliveSeconds(10);
        // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        executor.setThreadNamePrefix("completeFutureExecutor-");
        // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TransmittableDecorator());
        //初始化
        executor.initialize();
        return executor;
    }

    @Bean("turbolinkEventProcessExecutor")
    public ThreadPoolTaskExecutor turbolinkEventExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //executor.set
        // 核心线程数：线程池创建时候初始化的线程数
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 5 + 1);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(10 * 1000);
        // 允许线程的空闲时间10秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
        executor.setKeepAliveSeconds(10);
        // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        executor.setThreadNamePrefix("turbolinkEventExecutor-");
        // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TransmittableDecorator());
        //初始化
        executor.initialize();
        return executor;
    }


    @Bean("robotAddExecutor")
    public ThreadPoolTaskExecutor robotAddExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("robot-add-");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 4 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(10000);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 解决 sleuth 冲突
     *
     * @return
     */
    @Bean
    public AsyncConfigurer asyncConfigurer() {

        //  在这里返回一个 LazyTraceAsyncCustomizer 类型的AsyncConfigurer
        return new LazyTraceAsyncCustomizer(this.beanFactory, new AsyncConfigurer() {
            @Override
            public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
                return (throwable, method, params) -> log.error("方法名：={},参数={},异常信息：", method, params, throwable);
            }
        });
    }
}
