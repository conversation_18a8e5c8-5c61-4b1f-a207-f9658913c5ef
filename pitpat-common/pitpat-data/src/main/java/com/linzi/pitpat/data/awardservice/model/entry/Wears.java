package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.WearConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 服装表
 *
 * <AUTHOR>
 * @since 2023-11-09
 */

@Data
@NoArgsConstructor
@TableName("zns_wears")
public class Wears implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.Wears:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                       // 主键ID
    public final static String wear_type = CLASS_NAME + "wear_type";                          // 获取的服装类型:0:全部,1:发色（头发、帽子）,2:肤色,3.头型,4:脸部服饰（眼镜）,5:上衣,6:裤子,7:鞋子,8:套装
    public final static String attire_type_included = CLASS_NAME + "attire_type_included";    // 套装包含服装类型,[5,6]
    public final static String wear_id = CLASS_NAME + "wear_id";                              // 服装id
    public final static String wear_name = CLASS_NAME + "wear_name";                          // 服装名称
    public final static String wear_image_url = CLASS_NAME + "wear_image_url";                // 服装图片url
    public final static String men_wear_url = CLASS_NAME + "men_wear_url";                    // 男性服装图片
    public final static String women_wear_url = CLASS_NAME + "women_wear_url";                // 女性服装图片
    public final static String status_ = CLASS_NAME + "status";                               // 服装状态(0:可用，1：不可用)
    public final static String is_delete = CLASS_NAME + "is_delete";                          // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";                      // 创建时间
    public final static String modifie_time = CLASS_NAME + "modifie_time";                    // 最后修改时间
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 获取的服装类型: 1:发色（头发、帽子）, 2:肤色, 3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子, 8:套装，9:背部服饰，10：动作
     *
     * @see WearConstant.WearTypeEnum
     */
    private Integer wearType;
    //套装包含服装类型,[5,6]
    private String attireTypeIncluded;
    //服装id
    private Integer wearId;
    //服装名称
    private String wearName;
    //服装图片url
    private String wearImageUrl;
    //男性服装图片
    private String menWearUrl;
    //女性服装图片
    private String womenWearUrl;
    //服装状态(0:可用，1：不可用)
    private Integer status;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //最后修改时间
    private ZonedDateTime modifieTime;
    // 默认语言
    private String defaultLangCode;

}
