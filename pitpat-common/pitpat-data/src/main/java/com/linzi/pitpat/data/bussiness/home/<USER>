package com.linzi.pitpat.data.bussiness.home;

import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityPolymerizationBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.DailyRaceDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.vo.activity.DailyRaceVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MaxAwardVo;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryItemService;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.constant.RouteConstant;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.entity.dto.user.LoginUserDto;
import com.linzi.pitpat.data.entity.home.HomeItem;
import com.linzi.pitpat.data.entity.home.HomeModule;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.enums.RouteConfigEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceModelEnum;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.vo.AppRouteVersionCompatibility;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderItemQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderQuery;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.query.HomeItemQuery;
import com.linzi.pitpat.data.service.home.HomeItemService;
import com.linzi.pitpat.data.service.home.HomeModuleService;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.systemservice.dto.response.HomeComponentConfigurationAdminDto;
import com.linzi.pitpat.data.systemservice.dto.response.HomeComponentConfigurationAppDto;
import com.linzi.pitpat.data.systemservice.dto.response.HomeI8nDto;
import com.linzi.pitpat.data.systemservice.dto.response.HomePageCustomizeAdminResponseDto;
import com.linzi.pitpat.data.systemservice.dto.response.HomePageCustomizeAppResponseDto;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.enums.MallJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.PopRecordConstant;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.model.entity.HomePageCustomizeDo;
import com.linzi.pitpat.data.systemservice.model.entity.PopRecord;
import com.linzi.pitpat.data.systemservice.model.query.HomePageCustomizeQuery;
import com.linzi.pitpat.data.systemservice.model.query.PopRecordQuery;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.HomePageCustomizeService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.userservice.biz.UserExpLevelBizService;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelAwardRecord;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserExtraQuery;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserLevelAwardRecordService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.vo.home.HomePageUserInfo;
import com.linzi.pitpat.data.vo.home.HomePageUserInfoRespDto;
import com.linzi.pitpat.data.vo.home.HomePageV3Vo;
import com.linzi.pitpat.data.vo.home.HomePageV4Vo;
import com.linzi.pitpat.data.vo.home.HomepageActivityVo;
import com.linzi.pitpat.data.vo.home.HomepageItemListVo;
import com.linzi.pitpat.data.vo.home.UserDataInfoSimpleVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/26 10:21
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HomepageBussiness {
    @Resource
    private OperationalActivityService operationalActivityService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private HomeItemService homeItemService;
    @Resource
    private HomeModuleService homeModuleService;
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;
    @Resource
    private ISysConfigService sysConfigService;
    @Value("${spring.profiles.active}")
    private String profile;
    @Resource
    private ZnsUserService userService;
    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private ActivityCategoryItemService activityCategoryItemService;
    @Resource
    private ActivityDisseminateService activityDisseminateService;
    @Resource
    private SubActivityService subActivityService;
    @Resource
    private ActivityEquipmentConfigService activityEquipmentConfigService;
    @Resource
    private RotationAreaBizService rotationAreaBizService;
    @Resource
    private UserGroupRelService userGroupRelService;
    @Resource
    private ActivityPolymerizationBizService activityPolymerizationBizService;
    @Resource
    private MainActivityService mainActivityService;
    @Resource
    private ActivityFeeService activityFeeService;
    @Resource
    private UserRankedLevelService userRankedLevelService;
    @Resource
    private RunRankedActivityUserService runRankedActivityUserservice;
    @Resource
    private UserLevelService userLevelService;
    @Resource
    private AwardActivityBizService awardActivityBizService;
    @Resource
    private RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    @Resource
    private UserLevelAwardRecordService userLevelAwardRecordService;
    @Resource
    private HomePageCustomizeService homePageCustomizeService;
    @Resource
    private UserExpLevelBizService userExpLevelBizService;
    @Resource
    private AppRouteConfigService appRouteConfigService;
    @Resource
    private ZnsUserEquipmentService userEquipmentService;

    private final UserExtraService userExtraService;
    private final ZnsOrderService orderService;
    private final ZnsOrderItemService orderItemService;
    private final PopRecordService popRecordService;
    private final String GOODS_URL_VALUE = "lznative://shop/productDetail";//商城商品详情
    private final String CATEGORY_URL_VALUE = "lznative://shop/categoryList";//类目页

    public HomePageV3Vo getHomepage(ZnsUserEntity loginUser, boolean testUser, Integer appType, boolean checkUser, Integer appVersion, Integer showLocation, String zoneId) {
        HomePageV3Vo vo = new HomePageV3Vo();
        //用户信息
        HomePageUserInfo homepageUserInfo = userService.getHomepageUserInfo(loginUser);
        vo.setUserInfo(homepageUserInfo);
        List<RotationArea> rotationArea = getHomeRotationArea(loginUser, appType, appVersion, checkUser, showLocation, testUser);

        //里程跑和时长跑和速度跑
        List<HomepageItemListVo> homepageItemRunMileage = homeItemService.homepageItemList(1L, appVersion);
        vo.setRunMileages(homepageItemRunMileage);
        List<HomepageItemListVo> homepageItemRunTime = homeItemService.homepageItemList(2L, appVersion);
        vo.setRunTime(homepageItemRunTime);
        List<HomepageItemListVo> homepageItemRunRate = homeItemService.homepageItemList(6L, appVersion);
        vo.setRunRate(homepageItemRunRate);


        List<ZnsRunActivityConfigEntity> configEntityList = runActivityConfigService.findAll();
        Map<Integer, ZnsRunActivityConfigEntity> configEntityMap = configEntityList.stream().collect(Collectors.toMap(ZnsRunActivityConfigEntity::getActivityType, Function.identity(), (x, y) -> x));

        LoginUserDto loginUserDto = new LoginUserDto(loginUser, testUser, appType, checkUser, appVersion, showLocation, zoneId, loginUser.getIsPrivacy());
        //里程碑赛
        HomepageActivityVo officialCumulativeRun = activityStrategyContext.getActivityMap(configEntityMap.get(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType()));
        ZnsRunActivityEntity officialCumulativeActivity = runActivityService.selectHomeActivityOne(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(), testUser, checkUser, loginUser.getId());
        activityStrategyContext.homePageActivityMap(officialCumulativeRun, officialCumulativeActivity, loginUser.getId(), zoneId);
        vo.setOfficialCumulativeRun(officialCumulativeRun);

        List<HomepageActivityVo> homepageActivityVoList = getHomepageActivityVoList(configEntityMap, loginUserDto);
        if (!CollectionUtils.isEmpty(homepageActivityVoList)) {
            vo.setHomepageActivityVoList(homepageActivityVoList);
        }
        vo.setRotationArea(rotationArea);
        return vo;
    }

    private List<HomepageActivityVo> getHomepageActivityVoList(Map<Integer, ZnsRunActivityConfigEntity> configEntityMap, LoginUserDto userDto) {
        List<HomepageActivityVo> homepageActivityVoList = new ArrayList<>();
        //新里程碑组装
        HomeModule homeModule = homeModuleService.findListByModelType(2, userDto.getTestUser());
        if (Objects.isNull(homeModule)) {
            return homepageActivityVoList;
        }
        List<HomeItem> list = homeItemService.findList(HomeItemQuery.builder().moduleId(homeModule.getId()).isDelete(0).status(1).endTime(ZonedDateTime.now()).startTime(ZonedDateTime.now()).activityType(0).build());
        if (CollectionUtils.isEmpty(list)) {
            return homepageActivityVoList;
        }

        return list.stream().map(i -> {
            HomepageActivityVo homepageActivityVo = activityStrategyContext.getActivityMap(configEntityMap.get(i.getActivityType()));
            ZnsRunActivityEntity activity = null;
            if (RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType().equals(i.getActivityType())) {
                //查询当前时间内的新里程碑

                RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                        .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                        .activityStateIn(Arrays.asList(0, 1))
                        .isDelete(0)
                        .maxActivityStartTime(ZonedDateTime.now())
                        .minActivityEndTime(ZonedDateTime.now());
                if (Objects.equals(0, userDto.getLoginUser().getIsTest())) {
                    runActivityQueryBuilder.isTest(0);
                }


                activity = runActivityService.findOne(runActivityQueryBuilder.build());
            } else {
                activity = runActivityService.selectHomeActivityOne(i.getActivityType(), userDto.getTestUser(), userDto.getCheckUser(), userDto.getLoginUser().getId());
            }
            activityStrategyContext.homePageActivityMap(homepageActivityVo, activity, userDto.getLoginUser().getId(), userDto.getZoneId());
            return homepageActivityVo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取首页用户信息
     *
     * @param user
     * @param zoneId
     * @return
     */
    public HomePageUserInfoRespDto getHomepageUserInfo(ZnsUserEntity user, String zoneId) {
        HomePageUserInfoRespDto info = new HomePageUserInfoRespDto();
        info.setNickname(user.getFirstName());
        info.setUserId(user.getId());
        info.setHeadPortrait(user.getHeadPortrait());
        info.setUserLevel(user.getUserLevel());
        UserLevel userLevel = userLevelService.findByUserId(user.getId());
        info.setUserLevel(userLevel.getLevel());
        List<UserLevelAwardRecord> levelAwardRecordList = userLevelAwardRecordService.findByUserId(user.getId());
        Boolean levelAwardFlag = userExpLevelBizService.getNewLevelAwardFlag(user) || !org.springframework.util.CollectionUtils.isEmpty(levelAwardRecordList);
        info.setLevelAwardFlag(levelAwardFlag);
        info.setVipType(user.getVipType());
        //奖金、积分
        Integer score = userService.getAllUserScore(user.getId());
        info.setScore(score);
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
        info.setBonus(userAccount.getTotalBonus());
        info.setCurrency(I18nConstant.buildCurrency(userAccount.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode()));

        //运动数据
        //当日
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime todayStart = DateUtil.getDayStartByZone(now, zoneId);
        ZonedDateTime todayEnd = DateUtil.getDayEndByZone(now, zoneId);
        UserDataInfoSimpleVo todayUserData = getUserDataInfoSimpleVo(user.getId(), todayEnd, todayStart);
        info.setTodayData(todayUserData);
        //本月
        ZonedDateTime monthStart = DateUtil.getStartOfMonth(now, TimeZone.getTimeZone(zoneId));
        UserDataInfoSimpleVo month = getUserDataInfoSimpleVo(user.getId(), now, monthStart);
        info.setMonthData(month);
        //检查用户渠道
        info.setIsShowNewbieUniverse(0);
        //设置投流用户标识
        info.setIsPutChannel(userExtraService.isPutChannelUser(user.getId()) ? 1 : 0);
        return info;
    }

    private String getChannelUserAwardPop(ZnsUserEntity user, UserExtraDo profilesDo) {
        if (Objects.isNull(profilesDo) || !StringUtils.hasText(profilesDo.getChannelCode())) {
            return "";
        }
        //查询是否弹窗过
        PopRecordQuery popRecordQuery = PopRecordQuery.builder()
                .userId(user.getId()).pos(PopRecordConstant.PopPosEnum.HOME.getPos())
                .type(PopRecordConstant.PopRecordTypeEnum.CHANNEL_NEW_USER_AWARD_POP.getType()).build();
        PopRecord popRecord = popRecordService.findByQuery(popRecordQuery);
        if (Objects.nonNull(popRecord)) {
            return "";
        }

        popRecordService.addPop(user.getId(), PopRecordConstant.PopRecordTypeEnum.CHANNEL_NEW_USER_AWARD_POP.getType(), PopRecordConstant.PopPosEnum.HOME.getPos(), 0L);
        return I18nMsgUtils.getMessage("home.channel.newUser.award.tip");
    }

    private Integer isShowNewbieUniverse(ZnsUserEntity user, UserExtraDo profilesDo) {
        if (Objects.isNull(profilesDo) || !StringUtils.hasText(profilesDo.getChannelCode())) {
            return 0;
        }
        List<Long> goodsIds = sysConfigService.selectConfigListByKey(ConfigKeyEnums.BRACELET_GOODS_ID_LIST.code, Long.class);
        if (CollectionUtils.isEmpty(goodsIds)) {
            return 1;
        }
        //判断是否购买手环，通过商品id判断
        List<ZnsOrderItemEntity> list = orderItemService.findList(new OrderItemQuery().setGoodsIdList(goodsIds).setUserId(user.getId()));
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> orderList = list.stream().map(ZnsOrderItemEntity::getOrderId).toList();
            ZnsOrderEntity order = orderService.findByQuery(new OrderQuery().setIds(orderList).setStatusList(OrderStatusEnum.purchasedStatus()));
            if (Objects.nonNull(order)) {
                return 0;
            }
        }

        return 1;
    }

    private UserDataInfoSimpleVo getUserDataInfoSimpleVo(Long userId, ZonedDateTime end, ZonedDateTime start) {
        List<RealPersonRunDataDetails> monthUserData = realPersonRunDataDetailsService.findListByTime(userId, start, end, EquipmentDeviceTypeEnum.getAllDeviceTypList());
        UserDataInfoSimpleVo month = new UserDataInfoSimpleVo();
        if (CollectionUtils.isNotEmpty(monthUserData)) {
            month.setTotalRunMileage(monthUserData.stream().map(RealPersonRunDataDetails::getRunMileage).reduce(BigDecimal.ZERO, BigDecimal::add).intValue());
            month.setTotalRunTime(monthUserData.stream().mapToInt(RealPersonRunDataDetails::getRunTime).sum());
            month.setTotalRunCalorie(monthUserData.stream().map(RealPersonRunDataDetails::getKilocalorie).reduce(BigDecimal.ZERO, BigDecimal::add).intValue());
            month.setTotalRunCount(monthUserData.size());
        }
        return month;
    }

    public HomePageV4Vo getHomepageV4(ZnsUserEntity loginUser, boolean testUser, Integer appType, boolean checkUser, Integer appVersion, Integer showLocation, String zoneId) {
        HomePageV4Vo vo = new HomePageV4Vo();
        List<RotationArea> rotationArea = getHomeRotationArea(loginUser, appType, appVersion, checkUser, showLocation, testUser);
        String currentTime = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId), DateUtil.DATE_TIME_SHORT);

        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(loginUser.getId());
        List<ZnsRunActivityConfigEntity> configEntityList = runActivityConfigService.findAll();
        Map<Integer, ZnsRunActivityConfigEntity> configEntityMap = configEntityList.stream().collect(Collectors.toMap(ZnsRunActivityConfigEntity::getActivityType, Function.identity(), (x, y) -> x));

        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());

        List<DailyRaceVo> homeActivity = activityCategoryItemService.findHomeActivity(currentTime, loginUser.getId(), groupsByUserId);
        if (!CollectionUtils.isEmpty(homeActivity)) {
            vo.setDailyRaceList(processHomeActivity(homeActivity, loginUser, userAccount, configEntityMap, checkUser));
        }

        vo.setRotationArea(rotationArea);

        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(loginUser.getId());
        if (userRankedLevel != null) {
            vo.setIsInPlacement(userRankedLevel.getIsInPlacement());
            vo.setSegment(runRankedActivityUserservice.getCurrentRankSegment(loginUser.getId()));
        }
        return vo;
    }

    private List<DailyRaceDto> processHomeActivity(List<DailyRaceVo> homeActivity, ZnsUserEntity loginUser, ZnsUserAccountEntity userAccount, Map<Integer, ZnsRunActivityConfigEntity> configEntityMap, boolean checkUser) {
        List<DailyRaceDto> dtoList = new ArrayList<>();
        List<Long> activityIds = homeActivity.stream().map(DailyRaceVo::getActivityId).collect(Collectors.toList());
        Map<Long, ActivityDisseminate> disseminateMap = activityDisseminateService.findMapByActIdsAndLanguage(activityIds, loginUser.getLanguageCode());
        Map<Long, List<Integer>> equipmentMap = activityEquipmentConfigService.findMapByActIds(activityIds);
        //活动状态
        for (DailyRaceVo a : homeActivity) {
            DailyRaceDto dto = new DailyRaceDto();
            dto.setActivityId(a.getActivityId());
            //活动状态同时单赛事聚合id修改
            if (!isValidActivityState(a, dto, loginUser)) {
                continue;
            }

            //审核期间过滤付费活动
            if (checkUser) {
                ActivityFee feeEntry = activityFeeService.findFeeEntry(a.getActivityId(), userAccount.getCurrencyCode());
                if (Objects.nonNull(feeEntry) && (feeEntry.getAmount().compareTo(BigDecimal.ZERO) > 0 || feeEntry.getScore() > 0)) {
                    continue;
                }
                if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(a.getItemMainType())) {
                    continue;
                }
            }
            ActivityDisseminate activityDisseminate = disseminateMap.get(a.getActivityId());
            populateDtoWithData(a, dto, loginUser, activityDisseminate);

            //奖励金额
            MaxAwardVo maxAward = awardActivityBizService.findMaxAward(dto.getActivityId(), userAccount.getCurrencyCode(), null, a.getMainType(), loginUser.getId());
            CurrencyAmount amount = new CurrencyAmount(I18nConstant.buildCurrency(userAccount.getCurrencyCode()), maxAward.getMaxReward());
            dto.setAward(amount);

            //可用设备
            if (Objects.nonNull(equipmentMap)) {
                dto.setDeviceType(equipmentMap.get(a.getActivityId()));
            }
            dtoList.add(dto);
        }
        return dtoList;
    }


    private void populateDtoWithData(DailyRaceVo a, DailyRaceDto dto, ZnsUserEntity loginUser, ActivityDisseminate disseminate) {
        dto.setActivityCoverImage(a.getHomepageCover());
        dto.setActivityTitle(disseminate.getTitle());
        if (!StringUtils.hasText(dto.getActivityCoverImage())) {
            dto.setActivityCoverImage(disseminate.getDisseminatePics());
        }
        if (MainActivityTypeEnum.SINGLE.getType().equals(a.getMainType()) || MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(a.getMainType())) {
            List<SubActivity> activityList = subActivityService.getAllSingleActByMain(a.getActivityId());
            dto.setCompleteType(a.getTargetType());
            if (CollectionUtils.isEmpty(activityList) || activityList.size() > 1) {
                dto.setTarget(null);
            } else {
                dto.setTarget(activityList.get(0).getTarget());
            }
        }

        RotationArea route = rotationAreaBizService.getNewActivityRoute(dto.getActivityId(), a.getMainType(), 2);
        dto.setUrl(route.getUrl());
        dto.setJumpParam(route.getJumpParam());
    }

    /**
     * 判断活动状态是否有效
     *
     * @param vo
     * @param dto
     * @param loginUser
     * @return
     */
    private boolean isValidActivityState(DailyRaceVo vo, DailyRaceDto dto, ZnsUserEntity loginUser) {
        Integer activityState = 1;
        if (MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(vo.getItemMainType())) {
            vo.setMainType(vo.getItemMainType());
            Long activityId = activityPolymerizationBizService.findRunningActivityIdByParentId(dto.getActivityId());
            if (Objects.nonNull(activityId)) {
                MainActivity activity = mainActivityService.findById(activityId);
                activityState = mainActivityService.getActivityState(activity.getId(), loginUser.getZoneId());
                dto.setActivityId(activityId);
            }
        } else {
            activityState = mainActivityService.getActivityState(vo.getActivityId(), loginUser.getZoneId());
        }

        if (activityState == 2 || activityState == -1 || activityState == 3) {
            return false;
        }
        return true;
    }

    private List<RotationArea> getHomeRotationArea(ZnsUserEntity loginUser, Integer appType, Integer appVersion, boolean checkUser, Integer showLocation, boolean testUser) {
        //组装轮播专区
        List<RotationArea> rotationArea = new ArrayList<>();
        //banner
        List<RotationArea> homeMapList = operationalActivityService.selectHomeMapList(loginUser, appVersion, checkUser, appType, showLocation);
        List<Long> homeActivityIds = null;
        log.info("--------------------------> 首页banner列表为：{}", homeMapList);
        // 如果当前用户是显示2.12版本的老用户 那么如果该banner跳转3.0赛事的 则进行过滤
        Integer needVersionControl = loginUser.getNeedVersionControl();
        if (needVersionControl == 1) {
            homeMapList = homeMapList.stream().filter(item -> {
                if (BannerJumpTypeEnum.NEW_ACTIVITY.getJumpType().equals(item.getBannerJumpType())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        log.info("--------------------------> 过滤后首页banner列表为：{}", homeMapList);
        if (!CollectionUtils.isEmpty(homeMapList)) {
            rotationArea.addAll(homeMapList);
            homeActivityIds = rotationArea.stream().map(RotationArea::getId).collect(Collectors.toList());
        }
        //查询首页赛事相关
        List<ZnsRunActivityEntity> homeList = runActivityService.getHomeActivityByTypeAndState(RunActivityTypeEnum.officialTypes(), testUser, checkUser, loginUser.getId(), loginUser.getCountry());
        if (checkUser) {
            return rotationArea;
        }
        for (ZnsRunActivityEntity znsRunActivityEntity : homeList) {
            Map<String, Object> object = JsonUtil.readValue(znsRunActivityEntity.getActivityConfig());
            RotationArea activity = new RotationArea();
            String advertisingImage = MapUtil.getString(object.get(ApiConstants.ADVERTISING_IMAGE));
            String homeBanner = MapUtil.getString(object.get("homeBanner"));
            if (StringUtils.hasText(homeBanner)) {
                advertisingImage = homeBanner;
            }

            activity.setAdvertisingImage(advertisingImage);
            activity.setActivityType(znsRunActivityEntity.getActivityType());
            activity.setActivityTitle(znsRunActivityEntity.getActivityTitle());
            activity.setId(znsRunActivityEntity.getId());
            if (!CollectionUtils.isEmpty(homeActivityIds) && homeActivityIds.contains(znsRunActivityEntity.getId())) {
                continue;
            }
            rotationArea.add(activity);
        }
        return rotationArea;
    }

    /**
     * @param user
     * @param appVersion app 版本
     * @param appType
     * @return
     */
    public HomePageCustomizeAppResponseDto getHomeCustomize(ZnsUserEntity user, Integer appVersion, Integer appType) {
        HomePageCustomizeAppResponseDto responseDto = new HomePageCustomizeAppResponseDto();

        // 判断用户第一个设备是否为儿童款
        boolean isChildDevice = userEquipmentService.isFirstDeviceChildModel(user.getId());

        // 根据设备类型选择配置model
        String configModel = isChildDevice && appVersion >= VersionConstant.V4_5_2 ? DeviceModelEnum.CHILD.getCode() : DeviceModelEnum.STANDARD.getCode();

        HomePageCustomizeDo homePageCustomizeDo = homePageCustomizeService.findByQuery(
                HomePageCustomizeQuery.builder()
                        .status(1)
                        .model(configModel)
                        .build()
        );

        if (Objects.isNull(homePageCustomizeDo)) {
            log.error("首页自定义不存在上架的配置");
            return null;
        }

        HomePageCustomizeAdminResponseDto homePageCustomizeAdminResponseDto = JsonUtil.readValue(homePageCustomizeDo.getContent(), HomePageCustomizeAdminResponseDto.class);
        if (Objects.isNull(homePageCustomizeAdminResponseDto)) {
            log.error("首页自定义配置数据错误");
            return null;
        }

        String languageCode = user.getLanguageCode();
        List<HomeComponentConfigurationAdminDto> items = homePageCustomizeAdminResponseDto.getItems();
        List<HomeComponentConfigurationAppDto> appDtoList = new ArrayList<>();
        items.forEach(s -> {
            HomeComponentConfigurationAppDto homeComponentConfigurationAppDto = new HomeComponentConfigurationAppDto();
            AppRouteConfig appRouteConfig = appRouteConfigService.selectAppRouteConfigById(s.getPage());
            if (Objects.nonNull(appRouteConfig)) {
                homeComponentConfigurationAppDto.setRouteValue(appRouteConfig.getMainRoute());
                homeComponentConfigurationAppDto.setRouteParams(JsonUtil.writeString(appRouteConfig.getMainParam()));
                //route 版本兼容替换
                checkRouteVersionReplace(appRouteConfig, homeComponentConfigurationAppDto, appVersion);
                //会员额外处理
                if (Objects.equals(RouteConfigEnum.MY_MEMBER.getOneLevel(), appRouteConfig.getOneLevel())
                        && Objects.equals(RouteConfigEnum.MY_MEMBER.getTwoLevel(), appRouteConfig.getTwoLevel())
                        && Objects.equals(user.getMemberType(), 0)) {
                    homeComponentConfigurationAppDto.setRouteValue(appRouteConfig.getSecondaryRoute());
                }
            }
            if (Objects.equals(s.getMallJumpType(), MallJumpTypeEnum.GOODS_URL.getJumpType())) {
                homeComponentConfigurationAppDto.setRouteValue(GOODS_URL_VALUE);
                homeComponentConfigurationAppDto.setGoodsId(s.getGoodsId());
                homeComponentConfigurationAppDto.setRouteParams(JsonUtil.writeString(Map.of("goodsId", s.getGoodsId())));
            } else if (Objects.equals(s.getMallJumpType(), MallJumpTypeEnum.CATEGORY_URL.getJumpType())) {
                homeComponentConfigurationAppDto.setRouteValue(CATEGORY_URL_VALUE);
                homeComponentConfigurationAppDto.setCategoryCode(s.getCategoryCode());
                homeComponentConfigurationAppDto.setRouteParams(JsonUtil.writeString(Map.of("categoryCode", s.getCategoryCode())));
            }
            HomeI8nDto homeI8nDto = s.getLanguageFormData().get(languageCode);
            if (Objects.isNull(homeI8nDto) || !StringUtils.hasText(homeI8nDto.getImg())) {
                homeI8nDto = s.getLanguageFormData().get(s.getDefaultLangCode());
            }
            if (Objects.nonNull(s.getPointsGoodsId())) {
                homeComponentConfigurationAppDto.setRouteValue(RouteConstant.POINTS_GOODS_URL);
                homeComponentConfigurationAppDto.setRouteParams(JsonUtil.writeString(Map.of("id", s.getPointsGoodsId())));
                if (Objects.nonNull(appType) && appType == 1 && appVersion < 40700) {
                    //安卓且版本小于4.7.0
                    homeComponentConfigurationAppDto.setRouteValue("lznative://main/pointDetail");
                    homeComponentConfigurationAppDto.setRouteParams(JsonUtil.writeString(Map.of("redeem_award_id", s.getPointsGoodsId())));
                }
            }
            homeComponentConfigurationAppDto.setImg(homeI8nDto.getImg());
            homeComponentConfigurationAppDto.setModule(s.getModule());
            homeComponentConfigurationAppDto.setPage(s.getPage());
            appDtoList.add(homeComponentConfigurationAppDto);
        });
        responseDto.setType(homePageCustomizeAdminResponseDto.getType());
        responseDto.setItems(appDtoList);
        return responseDto;
    }

    /**
     * 需要替换的route value 版本兼容替换
     *
     * @param appRouteConfig
     * @param homeComponentConfigurationAppDto
     * @param appVersion
     */
    private void checkRouteVersionReplace(AppRouteConfig appRouteConfig, HomeComponentConfigurationAppDto homeComponentConfigurationAppDto, Integer appVersion) {
        // 获取系统配置中的route_id list
        List<AppRouteVersionCompatibility> routeReplaceCheckList = sysConfigService.selectConfigListByKey(ConfigKeyEnums.APP_ROUTE_VERSION_REPLACE.getCode()
                , AppRouteVersionCompatibility.class);
        if (!CollectionUtils.isEmpty(routeReplaceCheckList)) {
            routeReplaceCheckList.forEach(item -> {
                // 如果匹配版本小于目标版本，则替换 mainRoute 和 mainParam为 旧route + param
                if (appRouteConfig.getId().equals(item.getRouteId()) && appVersion < item.getAppVersion()) {
                    if (StringUtils.hasText(appRouteConfig.getOldMainRoute())) {
                        homeComponentConfigurationAppDto.setRouteValue(appRouteConfig.getOldMainRoute());
                        homeComponentConfigurationAppDto.setRouteParams(JsonUtil.writeString(appRouteConfig.getOldMainParam()));
                    }
                }
            });
        }
    }

}
