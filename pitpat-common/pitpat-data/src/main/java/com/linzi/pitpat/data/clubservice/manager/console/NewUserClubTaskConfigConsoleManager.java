package com.linzi.pitpat.data.clubservice.manager.console;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubTaskTypeEnum;
import com.linzi.pitpat.data.clubservice.convert.NewUserClubTaskConfigConsoleConverter;
import com.linzi.pitpat.data.clubservice.convert.NewUserClubTaskConfigI8nConverter;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskConfigDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskConfigI8nDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskRecordDo;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskConfigI8nQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskRecordPageQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskRecordQuery;
import com.linzi.pitpat.data.clubservice.model.request.ClubTaskConfigI8nDto;
import com.linzi.pitpat.data.clubservice.model.request.NewUserClubTaskConfigCreateRequestDto;
import com.linzi.pitpat.data.clubservice.model.request.NewUserClubTaskRecordPageQueryDto;
import com.linzi.pitpat.data.clubservice.model.response.NewUserClubTaskConfigResponseDto;
import com.linzi.pitpat.data.clubservice.model.response.NewUserClubTaskRecordResponseDto;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskConfigI8nService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskConfigService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskRecordService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 不是所有的业务都需要通过 manager 层去透传给 service ,如果是service 提供的能力已经满足 controller 的需求，可以直接通过 service 提供能力，
 * 这样尽在需要何必要的情况下 使用 manager 确保 合理的分层和代码维护
 * <p>
 * manager 类里面的依赖全部使用构造方法，为什么？ 因为官方推荐这么做，同时也可以避免依赖循环和 依赖膨胀，当该类的依赖太多，你可能需要考虑重构了
 *
 * @since 2025年2月25日
 */
@Component
@AllArgsConstructor
public class NewUserClubTaskConfigConsoleManager {
    //强制使用构造器传参
    private final NewUserClubTaskConfigService newUserClubTaskConfigService;
    private final NewUserClubTaskConfigConsoleConverter newUserClubTaskConfigConsoleConverter;
    private final NewUserClubTaskConfigI8nConverter newUserClubTaskConfigI8nConverter;
    private final NewUserClubTaskConfigI8nService newUserClubTaskConfigI8nService;
    private final NewUserClubTaskRecordService newUserClubTaskRecordService;
    private final NewUserClubTaskService newUserClubTaskService;
    private final ZnsUserService znsUserService;

    /**
     * 创建新人俱乐部任务配置表
     *
     * @param requestDto
     * @param modifier
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(NewUserClubTaskConfigCreateRequestDto requestDto, String modifier) {
        NewUserClubTaskConfigDo newUserClubTaskConfig = newUserClubTaskConfigConsoleConverter.toDo(requestDto);
        newUserClubTaskConfig.setModifier(modifier);
        newUserClubTaskConfig.setTitle(requestDto.getI8nList().stream().filter(s -> Objects.equals(s.getLanguageCode(), requestDto.getDefaultLanguageCode())).findFirst().orElse(new ClubTaskConfigI8nDto()).getTitle());
        Long l = newUserClubTaskConfigService.create(newUserClubTaskConfig);
        requestDto.getI8nList().stream().filter(s -> StringUtils.hasText(s.getTitle())).forEach(s -> {
            NewUserClubTaskConfigI8nDo i8nDo = newUserClubTaskConfigI8nConverter.toDo(s);
            i8nDo.setConfigId(newUserClubTaskConfig.getId());
            newUserClubTaskConfigI8nService.create(i8nDo);
        });
        return l > 0;
    }

    /**
     * 更新新人俱乐部任务配置表
     *
     * @param requestDto
     * @param modifier
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(NewUserClubTaskConfigCreateRequestDto requestDto, String modifier) {
        NewUserClubTaskConfigDo newUserClubTaskConfig = newUserClubTaskConfigConsoleConverter.toDo(requestDto);
        newUserClubTaskConfig.setModifier(modifier);
        newUserClubTaskConfig.setTitle(requestDto.getI8nList().stream().filter(s -> Objects.equals(s.getLanguageCode(), requestDto.getDefaultLanguageCode())).findFirst().orElse(new ClubTaskConfigI8nDto()).getTitle());
        Long l = newUserClubTaskConfigService.update(newUserClubTaskConfig);
        newUserClubTaskConfigI8nService.deleteByConfigId(newUserClubTaskConfig.getId());
        requestDto.getI8nList().stream().filter(s -> StringUtils.hasText(s.getTitle())).forEach(s -> {
            NewUserClubTaskConfigI8nDo i8nDo = newUserClubTaskConfigI8nConverter.toDo(s);
            i8nDo.setConfigId(newUserClubTaskConfig.getId());
            newUserClubTaskConfigI8nService.create(i8nDo);
        });
        return l > 0;
    }

    public NewUserClubTaskConfigResponseDto detail(Long id) {
        NewUserClubTaskConfigDo newUserClubTaskConfig = newUserClubTaskConfigService.findById(id);
        NewUserClubTaskConfigResponseDto responseDto = newUserClubTaskConfigConsoleConverter.toDto(newUserClubTaskConfig);
        List<NewUserClubTaskConfigI8nDo> list = newUserClubTaskConfigI8nService.findList(new NewUserClubTaskConfigI8nQuery().setConfigId(id));
        responseDto.setI8nList(list.stream().map(newUserClubTaskConfigI8nConverter::toDto).collect(Collectors.toList()));
        responseDto.setJoinNum(newUserClubTaskRecordService.participateNums(responseDto.getId()));
        List<NewUserClubTaskDo> serviceList = newUserClubTaskService.findList(new NewUserClubTaskQuery().setConfigId(id));
        if (!CollectionUtils.isEmpty(serviceList)) {
            long clubCount = serviceList.stream().map(NewUserClubTaskDo::getClubId).filter(Objects::nonNull).distinct().count();
            responseDto.setJoinClubNum(clubCount);
        }
        return responseDto;
    }

    public Page<NewUserClubTaskRecordResponseDto> page(NewUserClubTaskRecordPageQueryDto pageQueryDto) {
        NewUserClubTaskRecordPageQuery newUserClubTaskRecordPageQuery = new NewUserClubTaskRecordPageQuery();
        ZnsUserEntity userEntity = znsUserService.findByUserCode(pageQueryDto.getUserCode());
        BeanUtils.copyProperties(pageQueryDto, newUserClubTaskRecordPageQuery);
        newUserClubTaskRecordPageQuery.setUserId(Objects.nonNull(userEntity) ? userEntity.getId() : null);
        newUserClubTaskRecordPageQuery.setIsComplete(1);
        Page<NewUserClubTaskRecordDo> page = newUserClubTaskRecordService.findPage(newUserClubTaskRecordPageQuery);
        Page<NewUserClubTaskRecordResponseDto> resultPage = new Page<>();
        List<NewUserClubTaskRecordDo> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return resultPage;
        }
        List<NewUserClubTaskRecordResponseDto> collect = getNewUserClubTaskRecordResponseDtos(records);
        resultPage.setRecords(collect);
        resultPage.setCurrent(page.getCurrent());
        resultPage.setTotal(page.getTotal());

        return resultPage;

    }

    public List<NewUserClubTaskRecordResponseDto> recordList(NewUserClubTaskRecordPageQueryDto pageQueryDto) {
        NewUserClubTaskRecordQuery newUserClubTaskRecordQuery = new NewUserClubTaskRecordQuery();
        ZnsUserEntity userEntity = znsUserService.findByUserCode(pageQueryDto.getUserCode());
        BeanUtils.copyProperties(pageQueryDto, newUserClubTaskRecordQuery);
        newUserClubTaskRecordQuery.setUserId(Objects.nonNull(userEntity) ? userEntity.getId() : null);
        newUserClubTaskRecordQuery.setIsComplete(1);
        List<NewUserClubTaskRecordDo> list = newUserClubTaskRecordService.findList(newUserClubTaskRecordQuery);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return getNewUserClubTaskRecordResponseDtos(list);
    }

    private List<NewUserClubTaskRecordResponseDto> getNewUserClubTaskRecordResponseDtos(List<NewUserClubTaskRecordDo> records) {
        return records.stream().map(dto -> {
            NewUserClubTaskRecordResponseDto newUserClubTaskRecordResponseDto = new NewUserClubTaskRecordResponseDto();
            BeanUtils.copyProperties(dto, newUserClubTaskRecordResponseDto);
            newUserClubTaskRecordResponseDto.setApplyTime(dto.getSubmitTime());
            newUserClubTaskRecordResponseDto.setSubmitTime(Objects.nonNull(dto.getSubmitTime()) ? Date.from(dto.getSubmitTime().toInstant()) : null);
            ZnsUserEntity byId = znsUserService.findById(dto.getUserId());
            if (Objects.nonNull(byId)) {
                newUserClubTaskRecordResponseDto.setUserCode(byId.getUserCode());
                newUserClubTaskRecordResponseDto.setUserName(byId.getFirstName());
            }
            switch (ClubTaskTypeEnum.findByType(dto.getType())) {
                case SCORE -> newUserClubTaskRecordResponseDto.setFeedBack(dto.getScore().toString());
                case SINGLE_CHOICE, MULTIPLE_CHOICE -> newUserClubTaskRecordResponseDto.setFeedBack(dto.getOptions());
            }
            return newUserClubTaskRecordResponseDto;
        }).collect(Collectors.toList());
    }
}
