package com.linzi.pitpat.data.activityservice.listener;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.SourceType;
import com.linzi.pitpat.data.activityservice.listener.event.RunStartEvent;
import com.linzi.pitpat.data.activityservice.listener.event.RunningEvent;
import com.linzi.pitpat.data.activityservice.manager.ActivityUserManager;
import com.linzi.pitpat.data.activityservice.manager.PropRankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.RankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.courseservice.model.entity.UserCourseRecord;
import com.linzi.pitpat.data.courseservice.service.UserCourseRecordService;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.request.RunDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/22 17:12
 */
@Component
@Slf4j
public class RunDataListener {

    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private RankedActivityResultManager rankedActivityResultManager;
    @Resource
    private PropRankedActivityResultManager propRankedActivityResultManager;
    @Resource
    private UserCourseRecordService userCourseRecordService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private ActivityUserManager activityUserManager;
    @Resource
    private SeriesActivityRelService seriesActivityRelService;

    /**
     * 数据初始化
     *
     * @param event
     */
    @Async
    @EventListener(classes = {RunStartEvent.class})
    public void initData(RunStartEvent event) {
        log.info("RunDataListener initData start,event:{}", event);
        ZnsUserRunDataDetailsEntity userRunDataDetail = event.getUserRunDataDetails();
        RunDataRequest runData = event.getRunData();
        ActivityTypeDto activityTypeDto = runActivityService.getActivityNew(userRunDataDetail.getActivityId());
        if (EquipmentDeviceTypeEnum.TREADMILL.getCode().equals(event.getUserRunDataDetails().getDeviceType())) {
            //段位赛用户数据初始化
            rankedActivityInit(userRunDataDetail, activityTypeDto);
            //道具赛用户数据初始化
            propActivityInit(userRunDataDetail, activityTypeDto);
            //课程跑数据记录保存
            userCourseRecordInit(userRunDataDetail);
            // 马拉松任务倒计时添加
            marathonDeal(runData, userRunDataDetail);
        }
        //绑定活动
        activityUserManager.bindingRunDataDetailId(activityTypeDto, userRunDataDetail, runData.getTaskId());
    }

    /**
     * 数据初始化
     *
     * @param event
     */
    @Async
    @EventListener(classes = {RunningEvent.class})
    public void initRunningData(RunningEvent event) {
        log.info("RunDataListener initRunningData start");
        ZnsUserRunDataDetailsEntity userRunDataDetail = event.getUserRunDataDetails();
        RunDataRequest runData = event.getRunData();

        // 马拉松任务倒计时添加
        marathonDeal(runData, userRunDataDetail);
    }

    private void userCourseRecordInit(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        // 课程跑数据记录保存SQL优化 2.8 版本
        if (Objects.isNull(userRunDataDetail.getCourseId()) || userRunDataDetail.getCourseId() <= 0) {
            log.info("userCourseRecordInit end,无需初始化");
            return;
        }
        UserCourseRecord userCourseRecord = new UserCourseRecord();
        userCourseRecord.setCourseId(userRunDataDetail.getCourseId());
        userCourseRecord.setUserId(userRunDataDetail.getUserId());
        userCourseRecord.setDetailId(userRunDataDetail.getId());
        userCourseRecordService.save(userCourseRecord);
    }

    private void rankedActivityInit(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityTypeDto) {
        if (Objects.isNull(userRunDataDetail.getActivityId()) || userRunDataDetail.getActivityId() <= 0) {
            log.info("rankedActivityInit end,无需初始化");
            return;
        }
        log.info("活动信息 mainType={}, activityId={}, detailId{}", activityTypeDto.getMainType(), activityTypeDto.getId(), userRunDataDetail.getId());
        if (!Objects.equals(activityTypeDto.getMainType(), MainActivityTypeEnum.RANK.getType())) {
            log.info("rankedActivityInit end,不是段位赛");
            return;
        }
        rankedActivityResultManager.createRunRankedActivityUser(userRunDataDetail);
        log.info("初始化用户段位赛数据,detailId={}, activityId={},userId={}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
    }

    private void propActivityInit(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityTypeDto) {
        if (Objects.isNull(userRunDataDetail.getActivityId()) || userRunDataDetail.getActivityId() <= 0) {
            log.info("propActivityInit end,无需初始化");
            return;
        }
        log.info("活动信息 mainType={}, activityId={}, detailId{}", activityTypeDto.getMainType(), activityTypeDto.getId(), userRunDataDetail.getId());
        if (!Objects.equals(activityTypeDto.getMainType(), MainActivityTypeEnum.PROP.getType())) {
            log.info("propActivityInit end,不是道具赛");
            if (MainActivityTypeEnum.OLD.getType().equals(activityTypeDto.getMainType())) {
                //老数据
                ZnsRunActivityEntity runActivity = runActivityService.findById(activityTypeDto.getId());
                if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(runActivity.getActivityType())) {
                    propRankedActivityResultManager.createRunRankedActivityUser(userRunDataDetail, SourceType.USER_PROP_RACE.getType());
                    log.info("初始化用户道具赛数据,detailId={}, activityId={},userId={}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
                }
            }
            if (MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(activityTypeDto.getMainType())) {
                // 主报名信息
                propRankedActivityResultManager.createRunRankedActivityUser(userRunDataDetail, SourceType.USER_FREE_CHALLENGE_RACE.getType());
                List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(activityTypeDto.getId());
                MainActivity subMainMainActivity = allMainActivity.get(0);
                ZnsUserRunDataDetailsEntity subMainRunDataDetail = new ZnsUserRunDataDetailsEntity();
                BeanUtils.copyProperties(userRunDataDetail, subMainRunDataDetail);
                subMainRunDataDetail.setActivityId(subMainMainActivity.getId());
                // 子报名信息
                propRankedActivityResultManager.createRunRankedActivityUser(subMainRunDataDetail, SourceType.USER_FREE_CHALLENGE_RACE.getType());
                log.info("初始化free挑战跑用户道具赛数据,detailId={}, activityId={},userId={}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
            }
            return;
        }
        propRankedActivityResultManager.createRunRankedActivityUser(userRunDataDetail, SourceType.TRADITIONAL_PROP_RACE.getType());
        log.info("初始化用户道具赛数据,detailId={}, activityId={},userId={}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
    }


    private void marathonDeal(RunDataRequest runData, ZnsUserRunDataDetailsEntity userRunDataDetail) {
        try {
            if (Objects.isNull(runData.getTaskId()) || runData.getTaskId() <= 0) {
                log.info("marathonDeal end runData taskId 为空");
                return;
            }
            //时长大于60秒
            if (userRunDataDetail.getRunTime() < 60) {
                log.info("marathonDeal end runData 时长必须大于60秒");
                return;
            }
            RunActivityUserTask task = runActivityUserTaskService.findById(runData.getTaskId());
            if (Objects.isNull(task)) {
                log.info("marathonDeal end runData task 为空");
                return;
            }

            if (Objects.isNull(userRunDataDetail.getActivityId()) || userRunDataDetail.getActivityId() == -1) {
                userRunDataDetail.setActivityId(task.getActivityId());
                userRunDataDetailsService.update(userRunDataDetail);
            }

            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(task.getActivityId(), userRunDataDetail.getUserId());
            if (Objects.isNull(activityUser)) {
                log.info("marathonDeal end runData activityUser 为空");
                return;
            }

            if (activityUser.getActivityType() != 8) {
                log.info("marathonDeal end runData 活动类型不是马拉松");
                return;
            }

            if (activityUser.getUserState() != 1) {
                log.info("marathonDeal end runData 用户状态不可修改");
                return;
            }
            ZnsRunActivityEntity activity = runActivityService.findById(task.getActivityId());
            if (!DateUtil.isBetweenE(ZonedDateTime.now(), activity.getActivityStartTime(), activity.getActivityEndTime())) {
                log.info("marathonDeal end 当前时间不在活动时间内");
                return;
            }
            Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
            Integer countdownDuration = MapUtil.getInteger(jsonObject.get("countdownDuration"));
            ZonedDateTime endTime = DateUtil.addSeconds(ZonedDateTime.now(), countdownDuration);
            activityUser.setUserActivityEndTime(endTime);           // 用户活动结束时间
            activityUser.setUserState(3);
            runActivityUserService.updateById(activityUser);
            ;
        } catch (Exception e) {
            log.error("marathonDeal error,e:{}", e);
        }

    }
}
