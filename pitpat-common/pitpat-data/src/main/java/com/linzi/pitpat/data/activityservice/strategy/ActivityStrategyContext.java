package com.linzi.pitpat.data.activityservice.strategy;

import com.google.common.base.Splitter;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.AwardConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.EggActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.manager.ActivityUserManager;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityPayManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityProcessManager;
import com.linzi.pitpat.data.activityservice.manager.api.RunActivityUserManager;
import com.linzi.pitpat.data.activityservice.mapper.ActivityEquipmentConfigDao;
import com.linzi.pitpat.data.activityservice.mapper.MainActivityMapper;
import com.linzi.pitpat.data.activityservice.mapper.PkChallengeRecordDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityConfigDao;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityCheat;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.ExpRewardVo;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityThemeConfigService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.RunActivityCheatService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.ActivityMarkTypeEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.MarginCurrencyTypeEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.robotservice.listener.event.UserCreateActivityEvent;
import com.linzi.pitpat.data.robotservice.listener.event.UserReportActivityEvent;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserLoginLogEntity;
import com.linzi.pitpat.data.userservice.model.query.UserExpConfigQuery;
import com.linzi.pitpat.data.userservice.service.UserExpConfigService;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.vo.home.HomepageActivityVo;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.RedisCache;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityStrategyContext {
    private final UserIdentityService userIdentityService;
    @Resource
    private ApplicationContext springContext;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    @Lazy
    protected MindUserMatchService mindUserMatchService;
    @Value("${admin.server.gamepush}")
    private String gameDomain;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RedisCache redisCache;
    @Resource
    private UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    @Resource
    private ZnsUserService userService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    @Autowired
    private ActivityEquipmentConfigService activityEquipmentConfigService;
    @Autowired
    private ActivityEquipmentConfigDao activityEquipmentConfigDao;
    @Autowired
    private ZnsRunActivityConfigDao znsRunActivityConfigDao;
    @Autowired
    private UserFriendMatchService userFriendMatchService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private PkChallengeRecordDao pkChallengeRecordDao;
    @Resource
    private AwardConfigBizService awardConfigBizService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ActivityTaskConfigService activityTaskConfigService;
    @Autowired
    private ActivityThemeConfigService activityThemeConfigService;
    @Autowired
    private RunActivityCheatService runActivityCheatService;
    @Autowired
    @Lazy
    private AppMessageService appMessageService;
    @Resource
    private ActivityEntryFeeService activityEntryFeeService;
    @Resource
    private ZnsUserAccountService znsUserAccountService;
    @Resource
    private ActivityIDGenerateService activityIDGenerateService;
    @Resource
    private MainActivityMapper mainActivityMapper;

    @Resource
    private ZnsUserLoginLogService znsUserLoginLogService;
    @Resource
    private ActivityUserManager activityUserManager;
    @Resource
    private EggActivityBizService eggActivityBizService;
    @Resource
    private UserLevelService userLevelService;
    @Resource
    private UserLevelRuleService userLevelRuleService;
    @Resource
    private RunActivityPayManager runActivityPayManager;
    @Resource
    private RunActivityProcessManager runActivityProcessManager;
    @Resource
    private AwardActivityManager awardActivityManager;
    @Resource
    private UserExpConfigService userExpConfigService;
    @Autowired
    @Lazy
    private RunActivityUserManager runActivityUserManager;
    private final QueueMessageService queueMessageService;
    /**
     * 活动详情
     *
     * @param activityId
     * @param userEntity
     * @param activityUserStatus
     * @return
     */
    public RunActivityDetailVO activityDetail(Long activityId, ZnsUserEntity userEntity, Integer activityUserStatus) {
        RunActivityDetailVO activityDetailVO = new RunActivityDetailVO();
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
        if (null == activityEntity) {
            log.error("活动详情:未查到对应的活动.活动id:" + activityId);
            return activityDetailVO;
        }
        activityDetailVO.setEquipmentMainType(activityEntity.getEquipmentMainType());
        if (activityEntity.getActivityType() == 1) {
            activityEntity.setActivityTypeSub(null);
        }
        // 官方多人同跑需要返回活动简介字段
        if (activityEntity.getActivityType().equals(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType())) {
            if (ActivityMarkTypeEnum.NOT_INTERVIEW.getType().equals(userEntity.getActivityMark())) {
                // 随机分配页面类型 获取页面
                Integer randomPageType = getRandomPageType();
                log.info("获取随机数为：" + randomPageType);
                userEntity.setActivityMark(randomPageType); // 页面类型
                userService.update(userEntity);
            }
            if (ActivityMarkTypeEnum.PAGE_A.getType().equals(userEntity.getActivityMark())) {
                activityDetailVO.setActivityMark(ActivityMarkTypeEnum.PAGE_A.getType());
            } else {
                // B页
                activityDetailVO.setActivityMark(ActivityMarkTypeEnum.PAGE_B.getType());
                activityDetailVO.setActivitySynopsis(activityEntity.getActivitySynopsis());
            }
        }

        // 限速类型
        activityDetailVO.setRateLimitType(activityEntity.getRateLimitType());

        // 新增助力活动
        Long assistActivityId = activityEntity.getAssistActivityId();
        activityDetailVO.setAssistActivityId(assistActivityId);
        Integer assistActivityLinkPeriod = activityEntity.getAssistActivityLinkPeriod();
        activityDetailVO.setAssistActivityLinkPeriod(assistActivityLinkPeriod);


        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        // 新增竞技分字段
        if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())
                || RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(activityEntity.getActivityType())) {
            activityDetailVO.setOfficialEventScoreAward(JsonUtil.readValue(jsonObject.get("officialEventScoreAward")));
        }

        //排行赛设置挑战消耗积分
        if (activityEntity.getActivityType() == 3) {
            int challengeScoreConsumer = MapUtil.getInteger(jsonObject.get(ApiConstants.CHALLENGE_SCORE_CONSUME), 0);
            activityDetailVO.setChallengeScoreConsumer(challengeScoreConsumer);
        }
        try {
            activityDetailVO.setOfficialEventCouponAward(JsonUtil.readValue(jsonObject.get(ApiConstants.OFFICIAL_EVENT_COUPON_AWARD)));
        } catch (Exception e) {
            log.error("setOfficialEventScoreAward异常", e);
        }


        //防止活动开始定时任务未及时处理
        if (activityEntity.getActivityState() == 0 && activityEntity.getActivityStartTime().isBefore(ZonedDateTime.now())) {
            runActivityProcessManager.handleActivityStart(activityEntity, 0);
            activityEntity = runActivityService.findById(activityId);
        }

        if (Objects.equals(activityEntity.getActivityTypeSub(), 3)) {
            PkChallengeRecord pkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 1);
            PkChallengeRecord wasPkChallengeRecord = pkChallengeRecordDao.selectPkChallengeRecordByActivityIdChallengeType(activityEntity.getId(), 0);
            if (pkChallengeRecord != null && wasPkChallengeRecord != null) {
                activityDetailVO.setActivityTypeSub(activityEntity.getActivityTypeSub());
                activityDetailVO.setChallengeUserId(pkChallengeRecord.getUserId());
                activityDetailVO.setWasChallengeUserId(wasPkChallengeRecord.getUserId());
                activityDetailVO.setPkBatchNo(pkChallengeRecord.getBatchNo());
            }
        }

        List<ActivityEquipmentConfig> activityEquipmentConfigs = activityEquipmentConfigDao.selectActivityEquipmentConfigByActivityId(activityId);
        if (CollectionUtils.isEmpty(activityEquipmentConfigs) && Arrays.asList(1, 2).contains(activityEntity.getActivityType())) {
            UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(activityEntity.getId());
            if (userFriendMatch == null) {
                // 获取ZnsRunActivityConfigEntity中的activityEquipmentConfigs
                ZnsRunActivityConfigEntity znsRunActivityConfigEntity = znsRunActivityConfigDao.selectByActivityType(activityEntity.getActivityType(), activityEntity.getActivityTypeSub());
                Map<String, Object> data = JsonUtil.readValue(znsRunActivityConfigEntity.getActivityConfig());
                if (data != null && data.get("activityEquipmentConfigs") != null) {
                    activityEquipmentConfigs = JsonUtil.readList(data.get("activityEquipmentConfigs"), ActivityEquipmentConfig.class);// 设备配置
                }
            }
        }

        for (ActivityEquipmentConfig activityEquipmentConfig : activityEquipmentConfigs) {
            if (Objects.equals(activityEquipmentConfig.getEquipmentType(), 3)) {
                // 跑步形态 , 1 走步形态 , 2. 跑步形态
                if (Objects.equals(activityEquipmentConfig.getSubType(), 1)) {
                    activityEquipmentConfig.setEquipmentInfo(activityEquipmentConfig.getEquipmentInfo() + " (Walking Mode)");
                } else if (Objects.equals(activityEquipmentConfig.getSubType(), 2)) {
                    activityEquipmentConfig.setEquipmentInfo(activityEquipmentConfig.getEquipmentInfo() + " (Running Mode)");
                }
            }
        }
        activityDetailVO.setActivityEquipmentConfigs(activityEquipmentConfigs);
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        activityStrategy.wrapperRunActivityBasicData(activityEntity, activityDetailVO, userEntity);
        activityStrategy.wrapperRunActivityRouteData(activityEntity.getActivityRouteId(), activityDetailVO);
        activityStrategy.wrapperRunActivityUsers(activityEntity, activityDetailVO, userEntity, activityUserStatus);
        ZnsRunActivityUserEntity oneself = runActivityUserService.findActivityUser(activityId, userEntity.getId());
        activityStrategy.wrapperRunActivityDetailData(activityEntity, activityDetailVO, userEntity, oneself);
        activityStrategy.wrapperRunActivityHistoryUsers(activityEntity, activityDetailVO);
        activityStrategy.wrapperRunActivityDataDetail(activityEntity, activityDetailVO, userEntity.getId());
        activityStrategy.wrapperRunActivityVisitor(activityEntity, activityDetailVO, userEntity, oneself);
        getCouponPopSwitchResult(activityId, userEntity, activityDetailVO);
        activityStrategy.wrapperCouponDetail(activityEntity, activityDetailVO, userEntity.getId());
        activityStrategy.wrapperStageImageUrl(activityEntity, activityDetailVO, userEntity.getId());
        activityStrategy.wrapperEntryLimitInfo(activityEntity, userEntity, activityDetailVO);
        return activityDetailVO;
    }

    /**
     * 获取用户活动卷弹窗标志
     *
     * @param activityId
     * @param userEntity
     * @param activityDetailVO
     */
    private void getCouponPopSwitchResult(Long activityId, ZnsUserEntity userEntity, RunActivityDetailVO activityDetailVO) {
        // 活动结束状态不做弹出
        if (activityDetailVO.getActivityState() == -1 || activityDetailVO.getActivityState() == 2) {
            activityDetailVO.setCouponSwitch(YesNoStatus.NO.getCode());
            return;
        }
        String couponSwitch = sysConfigService.selectConfigByKey("user.coupon.switch");
        if (StringUtils.hasText(couponSwitch) && ActivityUserStateEnum.ACCEPT.getState().equals(activityDetailVO.getUserState())) {
            int i = Integer.parseInt(couponSwitch);
            if (i == 1) {
                String key = RedisConstants.ACTIVITY_POP_FLAG + ":" + userEntity.getId() + ":" + activityId;
                String s = redisCache.getCacheObject(key);
                if (StringUtils.hasText(s)) {
                    activityDetailVO.setCouponSwitch(YesNoStatus.NO.getCode());
                } else {
                    redisTemplate.opsForValue().setIfAbsent(key, "1");
                    activityDetailVO.setCouponSwitch(Integer.parseInt(couponSwitch));
                }

            }
        }
    }


    /**
     * 创建跑步活动
     *
     * @param runActivity
     * @param userEntity
     * @param activityConfig
     * @param activityStrategy
     * @return
     */
    public ZnsRunActivityEntity createRunActivity(RunActivityRequest runActivity, ZnsUserEntity userEntity, ZnsRunActivityConfigEntity activityConfig, BaseActivityStrategy activityStrategy) {
        // 1. 创建组队活动
        ZnsRunActivityEntity runActivityEntity = new ZnsRunActivityEntity();
        BeanUtils.copyProperties(runActivity, runActivityEntity);
        if (Objects.nonNull(runActivity.getActivityStartTime())) {
            ZonedDateTime startDate = new Date(runActivity.getActivityStartTime());
            runActivityEntity.setActivityStartTime(startDate);
        }
        if (Objects.nonNull(runActivity.getRunTime())) {
            runActivityEntity.setRunTime(runActivity.getRunTime().intValue());
        }
        runActivityEntity.setActivityType(activityConfig.getActivityType());
        if (runActivity.getIsRobotStart() == 1) {
            runActivityEntity.setHasRobot(1);
        }
        runActivityEntity.setAppointmentStartTime(runActivity.getAppointmentStartTime());

        Map<String, Object> jsonObject = activityStrategy.getActivityConfig(runActivity, activityConfig.getActivityConfig(), userEntity.getMeasureUnit(), userEntity.getId(), true);
        runActivityEntity.setActivityConfig(JsonUtil.writeString(jsonObject));

        // 设置活动规则
        activityStrategy.wrapperRunActivityRules(runActivityEntity, jsonObject, runActivity.getChallengeRunType(), runActivity.getAppVersion());
        runActivityEntity.setStatus(1);
        if (StringUtil.isEmpty(runActivityEntity.getActivityTitle())) {
            if (runActivityEntity.getActivityType() == 1) {
                runActivityEntity.setActivityTitle("Multi-Player Run initiated by " + userEntity.getFirstName());
            }
        }
        //id获取
        Long activityID = activityIDGenerateService.generateActivityID();
        runActivityEntity.setId(activityID);
        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(runActivityEntity.getActivityType())) {
            runActivityEntity.setActivityTypeSub(null);
        }
        if (Objects.nonNull(runActivity.getAppVersion()) && runActivity.getAppVersion() >= 3000) {
            runActivityEntity.setIsNew(YesNoStatus.YES.getCode());
        }
        boolean save = runActivityService.insert(runActivityEntity) == 1;
        if (save) {
            //保存新表
            MainActivity mainActivity = new MainActivity().setOldActivityId(activityID).setId(activityID).setMainType(MainActivityTypeEnum.OLD.getType()).setOldType(runActivityEntity.getActivityType());

            mainActivityMapper.insert(mainActivity);
        }
        ZnsUserAccountEntity userAccount = znsUserAccountService.getUserAccount(userEntity.getId());
        //zns_award_config_amount_currency 添加活动货币信息
        if (runActivityEntity.getActivityType().equals(RunActivityTypeEnum.TEAM_RUN.getType()) || (runActivityEntity.getActivityType().equals(RunActivityTypeEnum.CHALLENGE_RUN.getType()) && Objects.equals(runActivity.getActivityTypeSub(), 2))) {
            insertActivityEntryFee(runActivity, runActivityEntity, jsonObject, userAccount);
        }
        if (runActivityEntity.getActivityType() == 1 || runActivityEntity.getActivityType() == 2 || runActivityEntity.getActivityType() == 12) {
            //新奖励设置
            if (Objects.nonNull(runActivity.getAppVersion()) && runActivity.getAppVersion() >= 260) {
                //1v1 离线PK 不走这里 好友PK也不走
                if (!Arrays.asList(2, 3).contains(runActivityEntity.getActivityTypeSub())) {
                    awardActivityManager.copyAwardConfig(runActivity.getActivityConfigId(), runActivity.getRunningGoalsUnit(), runActivity.getRunMileage(), runActivity.getRunTime(), runActivityEntity.getId(), jsonObject, runActivity.getAppVersion());
                }
                runActivityEntity.setActivityConfig(JsonUtil.writeString(jsonObject));
                // 设置活动规则
                activityStrategy.wrapperRunActivityRules(runActivityEntity, jsonObject, runActivity.getChallengeRunType(), runActivity.getAppVersion());
                runActivityService.updateById(runActivityEntity);
            }
        }

        //查询活动id是否重复
        hasRepeatActivityId(runActivityEntity, 1);

        //1v1 没有发起人
        if (Objects.nonNull(runActivity.getChallengeRunType()) && runActivity.getChallengeRunType() == 1) {
            List<Long> activityUserIds = new ArrayList<>();
            activityUserIds.addAll(runActivity.getActivityUserIds());
            activityUserIds.add(userEntity.getId());
            runActivity.setActivityUserIds(activityUserIds);
        }
        // 3. 添加活动用户-发起者
        runActivityUserService.addRunActivityLaunchUser(runActivityEntity, userEntity, runActivity.getChallengeRunType());
        // 2. 添加活动参与者
        runActivityUserManager.addRunActivityUsers(runActivityEntity.getId(), runActivity.getActivityUserIds(), userEntity.getId(), userEntity.getFirstName(), runActivityEntity, runActivity.getChallengeRunType(), runActivity);

        // 4. 更新活动参与人数
        Integer userCount = runActivityUserService.queryActivityUserCount(runActivityEntity.getId(), runActivityEntity.getActivityType() == 1 ? true : false);
        BigDecimal totalBonus = activityStrategy.calculateTotalBonus(runActivityEntity, userCount, runActivity.getActivityEntryFee(), runActivity.getChallengeRunType(), userEntity, false);
        // 设置奖金池总额
        runActivityEntity.setActivityTotalBonus(totalBonus);

        runActivityService.updateActivityUser(runActivityEntity.getId(), userCount, totalBonus);
        return runActivityEntity;
    }

    /**
     * 创建活动费用
     *
     * @param runActivity
     * @param runActivityEntity
     * @param jsonObject
     * @param userAccount
     * @param userAccount
     */
    private void insertActivityEntryFee(RunActivityRequest runActivity, ZnsRunActivityEntity runActivityEntity, Map<String, Object> jsonObject, ZnsUserAccountEntity userAccount) {
        //保证金集合
        List<MarginCurrencyTypeEnum> marginList = MarginCurrencyTypeEnum.getMarginList();

        //获取保证金对应索引值
        MarginCurrencyTypeEnum userMargin = marginList.stream().filter(s -> s.getCurrencyCode().equals(userAccount.getCurrencyCode())).findFirst().get();
        List<BigDecimal> userMarginList = convertToBigDecimal(MapUtil.getString(jsonObject.get(userMargin.getType())));
        int index = -1;
        if (runActivity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
            index = userMarginList.indexOf(runActivity.getActivityEntryFee());
        }
        //把每个币种插入到报名费用表
        for (MarginCurrencyTypeEnum marginCurrencyTypeEnum : marginList) {
            String marginStr = MapUtil.getString(jsonObject.get(marginCurrencyTypeEnum.getType()));
            List<BigDecimal> oneMarginList = convertToBigDecimal(marginStr);
            ActivityEntryFee activityEntryFee = new ActivityEntryFee();
            if (index < 0) {
                activityEntryFee.setEntryFee(BigDecimal.ZERO);
            } else {
                activityEntryFee.setEntryFee(oneMarginList.get(index));
            }
            activityEntryFee.setActivityId(runActivityEntity.getId());
            activityEntryFee.setCurrencyCode(marginCurrencyTypeEnum.getCurrencyCode());
            activityEntryFee.setCurrencySymbol(I18nConstant.buildCurrency(marginCurrencyTypeEnum.getCurrencyCode()).getCurrencySymbol());
            activityEntryFeeService.insert(activityEntryFee);
        }
    }

    private static List<BigDecimal> convertToBigDecimal(String marginStr) {
        List<String> marginStrList = Splitter.on(",").trimResults().splitToList(marginStr);
        List<BigDecimal> marginList = marginStrList.stream().filter(org.springframework.util.StringUtils::hasText).map(BigDecimal::new).collect(Collectors.toList());
        return marginList;
    }

    private void hasRepeatActivityId(ZnsRunActivityEntity runActivityEntity, int i) {
        String key = RedisConstants.ACTIVITY_ID + runActivityEntity.getId().toString();
        Object activityId = redisTemplate.opsForValue().get(key);
        if (Objects.isNull(activityId)) {
            return;
        }
        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
        activityEntity.setId(runActivityEntity.getId() + 1);
        runActivityService.updateId(runActivityEntity.getId(), runActivityEntity.getId() + 1);
        runActivityEntity.setId(runActivityEntity.getId() + 1);
        i++;
        //防止进入死循环
        if (i > 100) {
            log.error("重置活动id失败，请处理");
            return;
        }
        hasRepeatActivityId(runActivityEntity, i);
    }

    public Result checkHandleActivity(Long activityId, Integer userStatus, ZnsUserEntity user) {
        if (null == activityId || null == userStatus) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.notExit"));
        }
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, user.getId());
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        Result result = activityStrategy.checkHandleUserActivityState(activityEntity, activityUser, userStatus, user);
        if (Objects.nonNull(result)) {
            return result;
        }

        return CommonResult.success();
    }

    public Map<String, Object> isPopPreventionCheat(Long userId, ActivityTypeDto activity) {
        Map<String, Object> data = new HashMap<>();
        data.put("checkCheatSwitch", 0);
        String checkCheatSwitch = sysConfigService.selectConfigByKey("check.cheat.switch");

        if (RunActivityTypeEnum.officialTypes().contains(activity.getActivityType())) {
            List<RunActivityCheat> list = runActivityCheatService.findList(activity.getId());
            if (CollectionUtils.isEmpty(list)) {
                log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                return data;
            }
            RunActivityCheat cheat = list.stream().filter(c -> c.getCheatSwitch() == 1).findFirst().orElse(null);
            if (Objects.isNull(cheat)) {
                log.info("preventionCheatDeal 处理结束，活动心率防作弊开关为关");
                return data;
            }
        } else {
            if ("0".equals(checkCheatSwitch)) {
                log.info("preventionCheatDeal 处理结束，防作弊开关为关");
                return data;
            }
            //新人活动不防作弊
            List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(activity.getId(), userId, null);
            if (!CollectionUtils.isEmpty(runActivityUserTasks)) {
                log.info("preventionCheatDeal 处理结束，新人活动不防作弊");
                return data;
            }
            String checkCheatActivityType = sysConfigService.selectConfigByKey("check.cheat.activity.type");
            if (StringUtils.hasText(checkCheatActivityType)) {
                List<Integer> activityTypes = NumberUtils.stringToInt(checkCheatActivityType.split(","));
                data.put("checkCheatActivityType", activityTypes);
                if (!activityTypes.contains(activity.getActivityType())) {
                    return data;
                }
            }
        }

        //检查人群
        String userStr = sysConfigService.selectConfigByKey("check.cheat.user");
        if (StringUtils.hasText(userStr)) {
            List<Long> userIds = NumberUtils.stringToLong(userStr.split(","));
            if (!userIds.contains(userIds)) {
                return data;
            }
        }

        data.put("checkCheatSwitch", 1);

        String config = sysConfigService.selectConfigByKey("check.cheat.rule");
        if (StringUtils.hasText(config)) {
            Map map = JsonUtil.readValue(config);
            BigDecimal cheatCountRate = BigDecimalUtil.divide(new BigDecimal(MapUtils.getDouble(map, "cheatCountRate")), new BigDecimal(100));
            map.put("cheatCountRate", cheatCountRate);
            data.putAll(map);
        }
        return data;
    }

    /**
     * 用户取消/接受/拒绝 跑步活动
     *
     * @param activityId           活动ID
     * @param userStatus           : -1表示取消，1表示接受，2表示拒绝
     * @param runningGoals
     * @param immediatelyAdmission
     * @param taskId
     * @param request
     * @param checkVersion
     */
    public Result<Void> handleUserActivityState(Long activityId, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission,
                                                Long taskId, HandleActivityRequest request, boolean checkVersion) {
        Boolean accept = Objects.equals(userStatus, 1) || (Objects.nonNull(request) && request.getUserStatus() == 1);


        if (null == activityId || null == userStatus) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }

        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);

        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.notExit")); //"Activity does not exist"
        }
        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
            if (YesNoStatus.NO.getCode().equals(activityEntity.getIsNew())) {
                ZnsUserLoginLogEntity loginVersion = znsUserLoginLogService.getLoginVersion(user.getId(), 3000);
                if (user.getAppVersion() >= 3000 || Objects.nonNull(loginVersion)) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("activity.invitation.failed"));
                }
            }
        }
        if ((RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType()) && Objects.equals(activityEntity.getActivityTypeSub(), 2)) || RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
            List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityId);
            if (!CollectionUtils.isEmpty(allActivityUser)) {
                List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).toList();
                for (Long activityUserId : userIds) {
                    if (activityUserId.equals(user.getId())) {// 排除本人
                        continue;
                    }
                    Result error = runActivityUserManager.checkEnrollCount(user.getId(), activityUserId, activityEntity.getActivityType(), activityEntity.getActivityTypeSub(), false);
                    if (error != null) return error;
                }
            }
        }

        if (Objects.equals(user.getIsRobot(), 0)) {                     //如果是真实用户，则做限制
            String activityConfigstr = activityEntity.getActivityConfig();
            Map<String, Object> activityConfigMap = JsonUtil.readValue(activityConfigstr);
            Integer maxRunScore = MapUtil.getInteger(activityConfigMap.get("maxRunScore"), -1);
            Integer minRunScore = MapUtil.getInteger(activityConfigMap.get("minRunScore"), -1);
            log.info("handleUserActivityState最大竞技分和最小竞技分判断  maxRunScore  = " + maxRunScore + ",minRunScore = " + minRunScore + " ,activityId = " + activityEntity.getId() + ",userId = " + user.getId());
            if (!Objects.equals(maxRunScore, -1) || !Objects.equals(minRunScore, -1) || Arrays.asList(4, 5).contains(activityEntity.getBonusRuleType())) {
                Integer userScore = userService.getAllUserScore(user.getId());
                if (maxRunScore != null && maxRunScore > 0 && userScore < maxRunScore) {
                    log.info(user.getId() + ",handleUserActivityState用户竟技分为 " + userScore + ",  配置的maxRunScore = " + maxRunScore + ", userScore > maxRunScore");
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.racePoint.ge", maxRunScore)); //"Race Point must be equal to or greater than " + maxRunScore
                }
                if (minRunScore != null && minRunScore > 0 && userScore >= minRunScore) {
                    log.info(user.getId() + ", handleUserActivityState用户竟技分为 " + userScore + ",  配置的minRunScore= " + minRunScore + ", userScore < minRunScore");
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.racePoint.le", minRunScore)); //"Race Point must be less than " + (minRunScore) + " in order to participate"
                }
                //若报名费包含积分，则判断用户积分是否足够
                if (!checkUserActivityEntryScore(activityEntity, userScore)) {
                    return CommonResult.fail(ActivityError.ACTIVITY_LACK_SCORE.getCode(), I18nMsgUtils.getMessage("activity.racePoint.insufficient")); //"Insufficient points to register
                    // for
                    // the competition"
                }
            }

        } else {
            log.info("用户是机器人 userId = " + user.getId());
        }
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());

        //校验活动国家跟用户国家是否相同
        if (activityStrategy.notContainsCountry(activityEntity, user)) {
            return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.mismatch.region")); // ActivityError.COUNTRY_ERROR.getMsg()
        }

        Result result = activityStrategy.canUpdateUserActivityState(activityEntity);
        if (Objects.nonNull(result)) {
            return result;
        }
        // check task limit count
        Result checkLimit = checkUserTaskEntryLimit(user, activityEntity);
        if (Objects.nonNull(checkLimit)) {
            return checkLimit;
        }
        log.info("activityStrategy.handleUserActivityState = {}", activityEntity);
        Result handleUserActivityState = activityStrategy.handleUserActivityState(activityEntity, userStatus, user, password, runningGoals, immediatelyAdmission, taskId, request, checkVersion);
        //发布报名事件
        if (accept) {
            HandleActivityRequest request1 = new HandleActivityRequest();
            request1.setUserId(user.getId());
            request1.setActivityId(activityId);
            log.info("发布报名事件，活动{}，用户{}", activityId, request1.getUserId());
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserReportActivityEvent.getEventType(),new UserReportActivityEvent(this, request1));
        }
        return handleUserActivityState;
    }

    /**
     * 检查用户积分是否大于活动报名所需积分
     *
     * @param activityEntity 活动
     * @param userScore      用户积分
     * @return true 用户积分足够， false 用户积分不足
     */
    private boolean checkUserActivityEntryScore(ZnsRunActivityEntity activityEntity, Integer userScore) {
        boolean containsEntryScore = Arrays.asList(4, 5).contains(activityEntity.getBonusRuleType());
        boolean legalEntryScore = Objects.nonNull(activityEntity.getActivityEntryScore()) && activityEntity.getActivityEntryScore().compareTo(BigDecimal.ZERO) > 0;
        if (containsEntryScore && legalEntryScore) {
            return BigDecimal.valueOf(userScore).compareTo(activityEntity.getActivityEntryScore()) >= 0;
        }
        return true;
    }


    /**
     * 上报用户活动状态前检查
     *
     * @param activityEntity
     * @param user
     * @return
     */
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), user.getId());
        if (Objects.nonNull(activityEntity.getActivityEndTime()) && activityEntity.getActivityEndTime().compareTo(ZonedDateTime.now()) < 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
        }
        if (activityEntity.getActivityState() == 2 || activityEntity.getActivityState() == -1) {
            if (activityEntity.getActivityType() == 1 || activityEntity.getActivityType() == 4) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
            } else {
                if (Objects.nonNull(activityUser) && Arrays.asList(1, 3, 4).contains(activityUser.getUserState())) {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
                } else {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.offlinePK.rule.reward.cancelled"));
                }
            }
        }
        if (activityEntity.getActivityStartTime().isAfter(ZonedDateTime.now())) {
            return CommonResult.success();
        }

        if (Objects.isNull(activityUser) || activityUser.getUserState() == 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.offlinePK.rule.reward.notenter"));
        }
        if (Objects.nonNull(activityUser) && activityUser.getUserState() == 2) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.offlinePK.rule.reward.invitation"));
        }
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());

        return activityStrategy.checkReportUserRun(activityEntity, user);
    }


    /**
     * 上报用户活动状态
     *
     * @param activityEntity
     * @param userEntity
     * @param userStatus
     * @return
     */
    public Result handleReportUserRunStatus(ZnsRunActivityEntity activityEntity, ZnsUserEntity userEntity, Integer userStatus) {
        if (Objects.isNull(activityEntity)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "活动不存在");
        }
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());


        //团队赛
        if (activityEntity.getActivityType() == 10) {
            List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(
                    RunActivityUserQuery.builder()
                            .isDelete(0)
                            .activityId(activityEntity.getId())
                            .userId(userEntity.getId())
                            // 0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束 5表示以退出
                            .userStateIn(Arrays.asList(1, 3, 4))
                            .build()
            );
            if (CollectionUtils.isEmpty(list)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "pls report first");
            }
        }

        //时间判断
        if (ActivityUserStateEnum.RUNING.getState().equals(userStatus)) {
            Result result = activityStrategy.checkRunActivityTime(activityEntity);
            if (Objects.nonNull(result)) {
                return result;
            }

            // 处理跑步中
            ActivityUserStateEnum activityUserStateEnum = ActivityUserStateEnum.findByType(userStatus);
            runActivityUserService.updateActivityUserState(activityEntity.getId(), userEntity.getId(), activityUserStateEnum);
        } else if (ActivityUserStateEnum.ENDED.getState().equals(userStatus)) {
            if (Arrays.asList(RunActivityTypeEnum.TEAM_RUN.getType(), RunActivityTypeEnum.CHALLENGE_RUN.getType(),
                    RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType(), RunActivityTypeEnum.NEW_PK_ACTIVITY.getType()).contains(activityEntity.getActivityType())) {
                // 处理跑步结束
                ActivityUserStateEnum activityUserStateEnum = ActivityUserStateEnum.findByType(userStatus);
                runActivityUserService.updateActivityUserState(activityEntity.getId(), userEntity.getId(), activityUserStateEnum);
            }

        }
        return CommonResult.success();
    }

    /**
     * 活动-检查是否可以跑步
     *
     * @param activityEntity
     * @return
     */
    public Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        if (activityEntity.getActivityState() == -1 || activityEntity.getActivityState() == 2) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Activity ended / cancelled");
        }

        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());

        return activityStrategy.checkRunActivityTime(activityEntity);
    }

    public void handleActivityFinished(ZnsRunActivityEntity activityEntity) {
//        String no = ch.qos.logback.classic.Logger.inheritableThreadLocalNo.get();
        log.info("handleActivityFinished 之前的活动id 及日志编号 ,活动id：" + activityEntity.getId());
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());

        try {
            OrderUtil.addLogNo();
            log.info("handleActivityFinished start,活动id：" + activityEntity.getId());
            // 防止重复提交
            String lockKey = ApiConstants.RUN_ACTIVITY_END + activityEntity.getId();
            RLock lock = redissonClient.getLock(lockKey);
            try {
                if (!lock.tryLock()) {
                    log.info("handleActivityFinished 获取锁失败");
                    return;
                }
                //重新查询活动，防止重复
                ZnsRunActivityEntity activity = runActivityService.findById(activityEntity.getId());
                if (activity.getActivityState() == 2) {                   // 活动状态：0表示未开始，1 表示进行中，2表示已结束，-1表示活动已取消
                    return;
                }
                log.info("handleActivityFinished获取锁 成功 " + lockKey);
                activityStrategy.handleRunActivityEnd(activityEntity);
            } catch (Exception e) {
                log.error("handleActivityFinished 处理失败，e:" + e, e);
                activityStrategy.exceptionNotification("Abnormality in automatic sending of reward system after completing the game \n activityId="
                        + activityEntity.getId());
            } finally {
                if (lock.isHeldByCurrentThread()) { //判断锁是否存在，和是否当前线程加的锁。
                    log.info("handleActivityFinished获取锁 后删除锁" + lockKey);
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("handleActivityFinished 异常 ", e);
            activityStrategy.exceptionNotification("Abnormality in automatic sending of reward system after completing the game \n activityId="
                    + activityEntity.getId());
        } finally {
            OrderUtil.removeLogNo();
        }
    }

    public void handleCumulativeRunEnd(ZnsUserRunDataDetailsEntity detailsEntity, Integer activityUserEnd) {
        String lockKey = ApiConstants.HANDLE_CUMULATIVE_RUN_END + detailsEntity.getId();
        //缓存一小时防止重复调用
        Object res = redisTemplate.opsForValue().get(lockKey);
        try {
            if (Objects.isNull(res)) {
                redisTemplate.opsForValue().set(lockKey, "1", 1, TimeUnit.HOURS);
                taskExecutor.execute(() -> {
                    log.info("开始处理里程碑和累计跑");
                    //累计跑处理
                    handleOfficialCumulativeRun(detailsEntity);
                    //里程碑活动处理
                    handleBattlePassCumulativeRunActivity(detailsEntity);
                });
            }
        } catch (Exception e) {
            log.error("handleActivityRunDateEnd ", e);

        }
    }

    /**
     * 跑步数据结束活动处理
     */
    public void handleActivityRunDateEnd(ZnsUserRunDataDetailsEntity detailsEntity, ZnsRunActivityEntity activityEntity, Integer activityUserEnd) {
        String lockKey = ApiConstants.RUN_ACTIVITY_USER_END + detailsEntity.getId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (LockHolder.tryLock(lock, 0, 3600L)) {
                log.info("handleActivityRunDateEnd 获取锁成功，key=" + lockKey);
                if (Objects.isNull(detailsEntity.getActivityId()) || detailsEntity.getActivityId() == -1) {
                    log.info("handleActivityRunDateEnd 活动id不存在");
                    return;
                }
                if (Objects.isNull(activityEntity) || activityEntity.getIsDelete() == 1) {
                    log.info("handleActivityRunDateEnd 活动不存在");
                    return;
                }
                //组队跑和挑战跑更新运动数据
                if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityEntity.getActivityType()) //  activityType = 1
                        || RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType())
                        || RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityEntity.getActivityType())) { // activityType = 2
                    log.info("handleActivityRunDateEnd 活动用户处理");
                    //更新活动用户表
                    activityUserManager.updateRunDataDetail(detailsEntity.getId(), activityEntity, detailsEntity, activityEntity.getCompleteRuleType());
                    NewPkActivityStrategy activityStrategy = springContext.getBean(NewPkActivityStrategy.class);
                    activityStrategy.runEnd(detailsEntity, activityEntity);
                    //判断活动用户是否都结束，如果都结跑步活动状态变更为活动结束
                    ZnsRunActivityUserEntity activityUser = runActivityUserService.findNoFinish(activityEntity.getId());
                    //考虑全部风控结束
                    Integer riskReview = userRunDataDetailsCheatService.isRiskReview(Collections.singletonList(activityEntity.getId()));
                    if (Objects.isNull(activityUser) && Objects.equals(riskReview, 0)) {
                        handleActivityFinished(activityEntity);
                    } else {
                        log.info("handleActivityRunDateEnd ");
                    }
                    // activityType = 3
                } else if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityEntity.getActivityType())) {
                    OfficialRankingActivityStrategy activityStrategy = springContext.getBean(OfficialRankingActivityStrategy.class);
                    activityStrategy.updateOfficialEventUserRunData(activityEntity, detailsEntity.getUserId(), detailsEntity);
                    // activityType = 4
                } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                    //更新活动用户表
                    activityUserManager.updateRunDataDetail(detailsEntity.getId(), activityEntity, detailsEntity, activityEntity.getCompleteRuleType());
                    OfficialTeamRunActivityStrategy activityStrategy = springContext.getBean(OfficialTeamRunActivityStrategy.class);
                    activityStrategy.runEnd(detailsEntity, activityEntity);
                } else if (RunActivityTypeEnum.WEEK_HAPPY_RUNNING.getType().equals(activityEntity.getActivityType())) {                     // activityType = 7
                    WeekHappyRunningActivityStrategy activityStrategy = springContext.getBean(WeekHappyRunningActivityStrategy.class);
                    activityStrategy.runDataEnd(activityEntity, detailsEntity);
                } else if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(activityEntity.getActivityType())) {                     // activityType = 8
                    TaskActivityStrategy activityStrategy = springContext.getBean(TaskActivityStrategy.class);
                    activityStrategy.runDataEnd(activityEntity, detailsEntity, activityUserEnd);
                }
            } else {
                log.info("handleActivityRunDateEnd 获取锁失败，key={}", lockKey);
            }
        } catch (Exception e) {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.error("handleActivityRunDateEnd", e);
        }
    }

    private void handleBattlePassCumulativeRunActivity(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        BattlePassCumulativeRunActivityStrategy activityStrategy = springContext.getBean(BattlePassCumulativeRunActivityStrategy.class);
        ZnsUserRunDataDetailsEntity runData = new ZnsUserRunDataDetailsEntity();
        BeanUtils.copyProperties(userRunDataDetail, runData);
        activityStrategy.handleBattlePassCumulativeRunActivity(runData);
    }

    /**
     * 获取活动跑步报告
     * ds
     *
     * @param detailId
     * @param loginUser
     * @param type
     * @param zoneId
     * @return
     */
    public ActivityRunningReportBaseVo getActivityRunningReport(Long detailId, ZnsUserEntity loginUser, Integer type, String zoneId, Long activityId) {
        ZnsUserRunDataDetailsEntity detail = userRunDataDetailsService.findById(detailId);
        userRunDataDetailsService.waitingProcessing(detail, 500);
        //兼容之前老的版本
        if (activityId == null || activityId == 0) {
            //查询活动
            if (Objects.isNull(detail.getActivityId()) || detail.getActivityId() <= 0) {
                return new ActivityRunningReportBaseVo();
            }
        }

        ZnsRunActivityEntity activityEntity = null;
        if (activityId != null && activityId > 0) {
            log.info("活动id不为空 ,activityId = " + activityId);
            activityEntity = runActivityService.findById(activityId);
        } else {
            activityEntity = runActivityService.findById(detail.getActivityId());
        }
        if (Objects.isNull(activityEntity)) {
            return new ActivityRunningReportBaseVo();
        }
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        //查询活动
        return activityStrategy.getActivityRunningReport(detail, loginUser, activityEntity, zoneId);
    }

    /**
     * 检查是否可以参加新人活动
     *
     * @param runActivityConfig
     * @param user
     * @param equipmentNo
     * @return
     */
    public Result checkEnrollingNewUserActivity(ZnsRunActivityConfigEntity runActivityConfig, ZnsUserEntity user, String equipmentNo) {
        Map<String, Object> object = JsonUtil.readValue(runActivityConfig.getActivityConfig());
        Result result = checkEnrollingNewUserActivityTime(object, user);
        if (Objects.nonNull(result)) {
            return result;
        }
        //查询该设备已参与活动数量
        long count = runActivityUserService.findCount(
                RunActivityUserQuery.builder()
                        .equipmentNo(equipmentNo)
                        .isDelete(0)
                        .activityType(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType())
                        .build()
        );

        Integer deviceConnectionNum = MapUtil.getInteger(object.get("deviceConnectionNum"));

        if (count >= deviceConnectionNum) {
            log.warn("参加新用户活动失败，一台跑步机仅限绑定{}个用户，用户id：{}", deviceConnectionNum, user.getId());
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("treadmill.connect.limit", deviceConnectionNum));
        }
        return null;
    }

    public Result checkNewUserActivityTime(Map<String, Object> object, ZnsUserEntity user, ZnsRunActivityEntity newUserActivityInfo) {
        if (Objects.isNull(user)) {
            return null;
        }
        ZonedDateTime now = ZonedDateTime.now();
        if (Objects.isNull(newUserActivityInfo)) {
            newUserActivityInfo = runActivityService.getNewUserActivityInfo(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType(), user.getId());
        }
        if (Objects.nonNull(newUserActivityInfo)) {
            if (now.isAfter(newUserActivityInfo.getActivityEndTime())) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "This event is only for new users");
            } else {
                return null;
            }
        }

        Integer deadline = MapUtil.getInteger(object.get("deadline"));
        ZonedDateTime newUserExclusiveDate = DateUtil.addDays(user.getCreateTime(), deadline);

        if (now.isAfter(newUserExclusiveDate)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.for.new.users"));
        }
        return null;
    }

    public Result checkEnrollingNewUserActivityTime(Map<String, Object> object, ZnsUserEntity user) {
        //查询是否已报名
        ZnsRunActivityEntity newUserActivityInfo = runActivityService.getNewUserActivityInfo(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType(), user.getId());
        if (Objects.nonNull(newUserActivityInfo)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.newcomer.activity"));
        }

        Result result = checkNewUserActivityTime(object, user, newUserActivityInfo);
        if (Objects.nonNull(result)) {
            return result;
        }

        return null;
    }

    //添加事务，防止活动创建成功，后续业务报错，导致产生脏数据
    @Transactional(rollbackFor = Exception.class)
    public Result doLaunchActivity(RunActivityRequest runActivity, ZnsUserEntity currentUser) {
        // 防止重复提交
        String lockKey = ApiConstants.RUN_ACTIVITY_LAUNCH + currentUser.getId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock()) {
                return CommonResult.success();
            }
            //目标类型runningGoalsUnit
            setRunningGoalUnit(runActivity);
            ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.findRunActivityConfig(runActivity.getActivityConfigId());
            if (null == activityConfig) {
                log.error("新增跑步活动:活动配置不存在");
                return null;
            }
            RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityConfig.getActivityType());
            BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());

            // 不允许3.0以下邀请3.0及以上用户
            if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityConfig.getActivityType())) {
                if (Objects.nonNull(runActivity.getAppVersion()) && runActivity.getAppVersion() < 3000) {
                    List<Long> activityUserIds = runActivity.getActivityUserIds();
                    List<ZnsUserEntity> userList = userService.findByIds(activityUserIds);
                    for (ZnsUserEntity znsUserEntity : userList) {
                        if (znsUserEntity.getId().equals(currentUser.getId())) {
                            // 排除本人
                            continue;
                        }
                        ZnsUserLoginLogEntity loginVersion = znsUserLoginLogService.getLoginVersion(znsUserEntity.getId(), 3000);
                        if (znsUserEntity.getAppVersion() >= 3000 || Objects.nonNull(loginVersion)) {
                            return CommonResult.fail(I18nMsgUtils.getMessage("activity.invitation.failed"));
                        }
                    }
                }
            }
            // 创建活动
            ZnsRunActivityEntity activityEntity = createRunActivity(runActivity, currentUser, activityConfig, activityStrategy);

            activityEquipmentConfigService.updateActivityEquipmentConfig(activityEntity.getId(), runActivity.getActivityEquipmentConfigs());

            if (null == activityEntity) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.create.fail"));
            }

            // 支付保证金逻辑
            HandleActivityRequest handleActivityRequest = new HandleActivityRequest();
            handleActivityRequest.setUserCouponId(runActivity.getUserCouponId());
            Result payResult = runActivityPayManager.handlePayRunActivity(activityEntity, currentUser, runActivity.getPassword(), handleActivityRequest, true);
            if (null != payResult) {
                // 支付失败,删除创建的活动
                runActivityService.deleteActivity(activityEntity.getId());
                return payResult;
            }

            Map<String, Object> data = new HashMap<>();
            data.put("activityId", activityEntity.getId());
            //被邀请人的奖励金额
            if (activityEntity.getActivityType() == 1) {
                BigDecimal awardAmount = activityStrategy.calculateTotalBonus(activityEntity, runActivity.getActivityUserIds().size() + 1, runActivity.getActivityEntryFee(), null, currentUser, false);
                data.put("awardAmount", awardAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
            } else if (activityEntity.getActivityType() == 2) {
                BigDecimal awardAmount = activityStrategy.calculatePersonalBonus(activityEntity, 1, runActivity.getActivityEntryFee());
                String currencyCode = znsUserAccountService.getUserCurrency(currentUser.getId()).getCurrencyCode();
                awardAmount = I18nConstant.currencyFormat(currencyCode, awardAmount);
                data.put("awardAmount", awardAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (runActivity.getRobotAcceptSuccess() == 0) {
                return CommonResult.success(data);
            }

            String userIds = null;
            int mode = 2;
            if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType())) {//挑战跑必须传两个用户id
                userIds = currentUser.getId() + "," + runActivity.getActivityUserIds().get(0);
                mode = 3;
            }

            for (int i = 0; i < 3; i++) {
                try {
                    GamePushUtils.addRoom(gameDomain, activityEntity.getId(), mode, userIds);
                    break;              // 如果没有抛出异常，直接返回 ，如果抛出异常，则再次循环
                } catch (Exception e) {
                    log.info("请求游戏服务器异常:{}", e.getMessage());
                }
            }

            eggActivityBizService.doActivityEggPush(activityEntity.getId());
            //发布活动创建事件
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserCreateActivityEvent.getEventType(),new UserCreateActivityEvent(this, currentUser, activityEntity));

            return CommonResult.success(data);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void setRunningGoalUnit(RunActivityRequest runActivity) {
        if (Objects.nonNull(runActivity.getRunTime()) && runActivity.getRunTime().compareTo(BigDecimal.ZERO) > 0) {
            runActivity.setRunningGoalsUnit(2);
            return;
        }
        if (Objects.nonNull(runActivity.getRunningGoalsUnit())) {
            return;
        }
        runActivity.setRunningGoalsUnit(1);
    }

    public void handleOfficialCumulativeRun(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        OfficialCumulativeRunActivityStrategy activityStrategy = springContext.getBean(OfficialCumulativeRunActivityStrategy.class);
        ZnsUserRunDataDetailsEntity runData = new ZnsUserRunDataDetailsEntity();
        BeanUtils.copyProperties(userRunDataDetail, runData);
        activityStrategy.handleOfficialCumulativeRun(runData);
    }


    /**
     * 首页活动数据处理
     *
     * @param map
     * @param activity
     * @param userId
     * @param zoneId
     */
    public void homePageActivityMap(HomepageActivityVo map, ZnsRunActivityEntity activity, Long userId, String zoneId) {
        if (Objects.isNull(activity)) {
            return;
        }
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        activityStrategy.homePageActivityMap(map, activity, userId, zoneId);
    }

    public HomepageActivityVo getActivityMap(ZnsRunActivityConfigEntity configEntity) {
        HomepageActivityVo map = new HomepageActivityVo();
        if (Objects.isNull(configEntity)) {
            return map;
        }
        Map<String, Object> object = JsonUtil.readValue(configEntity.getActivityConfig());

        map.setMainTitle(MapUtil.getString(object.get("mainTitle")));
        map.setNextTitle(MapUtil.getString(object.get("nextTitle")));
        map.setSubTitle(MapUtil.getString(object.get("subTitle")));
        map.setAdvertisingImage(MapUtil.getString(object.get(ApiConstants.BACKGROUND_IMAGE)));
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(configEntity.getActivityType())) {
            map.setAdvertisingImage(MapUtil.getString(object.get(ApiConstants.ADVERTISING_CONFIG_IMAGE)));
        }
        //标签
        map.setTagText(MapUtil.getString(object.get(ApiConstants.TAG_TEXT)));
        map.setActivityType(configEntity.getActivityType());
        return map;
    }

    public List<? extends SimpleRunActivityVO> homePageActivityList(List<ZnsRunActivityEntity> activityEntityList, Integer type, Long userId) {
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(type);
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        // 2.9.0 过滤新主题赛已经参加的活动
        List<? extends SimpleRunActivityVO> activityList = activityStrategy.homePageActivityList(activityEntityList, userId);
        List<Long> lists = activityThemeConfigService.getThemeConfigIds();
        return activityList.stream().filter(i -> !lists.contains(i.getActivityId())).collect(Collectors.toList());
    }

    public List<? extends SimpleRunActivityVO> getActivityList(Integer type, ZnsUserEntity user, boolean checkVersion, Integer source, Integer completeRuleType, List<Integer> runWalkStatus, Integer rateLimitType) {
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(type);
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        boolean testUser = sysConfigService.isTestUser(user.getEmailAddressEn());

        //兼容老版本
        List<Integer> defaultRunWalkStatus = Arrays.asList(0, 1);
        //官方多人同跑区分走步、跑步
        if (Objects.equals(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType(), type)) {
            //兼容2.6.0之前的版本,如果是组队跑，未传值，则置空走跑类型
            defaultRunWalkStatus = CollectionUtils.isEmpty(runWalkStatus) ? null : runWalkStatus;
        }
        // 2.9.0 过滤新主题赛已经参加的活动
        List<? extends SimpleRunActivityVO> activityList = activityStrategy.getActivityList(user, testUser, checkVersion, false, source, completeRuleType, defaultRunWalkStatus, rateLimitType);
        List<Long> lists = activityThemeConfigService.getThemeConfigIds();
        return activityList.stream().filter(i -> !lists.contains(i.getActivityId())).collect(Collectors.toList());
    }

    public Integer getLimitPeopleSize(Integer type) {
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(type);
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        return activityStrategy.getLimitPeopleSize();
    }


    /**
     * 获取活动奖励明细
     *
     * @param activityId 活动id
     * @return
     */
    public RunActivityRewardDetailVO activityRewardDetail(Long activityId, ZnsUserEntity loginUser) {
        RunActivityRewardDetailVO runActivityRewardDetailVO = new RunActivityRewardDetailVO();
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
        if (Objects.isNull(activityEntity)) {
            return runActivityRewardDetailVO;
        }
        Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activityEntity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());
        activityStrategy.wrapperActivityRewardDetailByActivityType(activityEntity, jsonObjectConfig, runActivityRewardDetailVO, loginUser);
        //查询用户经验奖励
        ExpRewardVo expRewardVo = getUserExpRewardVo(loginUser, activityEntity.getRunMileage());
        runActivityRewardDetailVO.setExpRewardVo(expRewardVo);
        return runActivityRewardDetailVO;
    }

    /**
     * 获取用户经验奖励
     *
     * @param loginUser
     * @return
     */
    private ExpRewardVo getUserExpRewardVo(ZnsUserEntity loginUser, BigDecimal runMileage) {
        ExpRewardVo result = new ExpRewardVo();
        UserLevel userLevel = userLevelService.findByUserId(loginUser.getId());
        if (userLevel == null) {
            return result;
        }
        Integer beforeExp = userLevel.getHistoryExperience();
        Integer currentExp = userLevel.getExperience();

        //计算预计经验
        if (runMileage != null) {
            Integer incrExperience = calculateExperience(runMileage, loginUser);
            if (incrExperience > 0) {
                beforeExp = currentExp;
                currentExp = currentExp + incrExperience;
            }
        }
        result.setBeforeExp(beforeExp);
        result.setBeforeLevel(userLevel.getHistoryLevel());
        result.setCurrentExp(currentExp);
        result.setCurrentLevel(userLevel.getLevel());
        UserLevelRule beforeLevel = userLevelRuleService.findByLevel(userLevel.getHistoryLevel());
        if (beforeLevel != null) {
            result.setBeforeMaxExp(beforeLevel.getExpHigh());
        }
        UserLevelRule currentLevel = userLevelRuleService.findByLevel(userLevel.getLevel());
        if (currentLevel != null) {
            result.setCurrentMaxExp(currentLevel.getExpHigh());
        }
        return result;
    }

    /**
     * 计算距离经验
     *
     * @param runMileage
     * @param user
     * @return
     */
    private Integer calculateExperience(BigDecimal runMileage, ZnsUserEntity user) {
        int incrExperience = 0;
        //距离经验
        UserExpConfig userExpConfig = userExpConfigService.findByObtainTypAndValue(UserExpObtainTypeEnum.RUNNING_MILEAGE.getCode(), runMileage.intValue());
        if (Objects.nonNull(userExpConfig)) {
            incrExperience = Objects.equals(user.getMemberType(), 1) ? userExpConfig.getBasicExp() + userExpConfig.getMemberBenefit() : userExpConfig.getBasicExp();
        }
        //赛事经验
        UserExpConfigQuery userExpConfigQuery = UserExpConfigQuery.builder().obtainSubType(UserExpObtainSubTypeEnum.DAILY_RACE.getCode())
                .obtainTypeList(List.of(UserExpObtainTypeEnum.EVENT_EXPERIENCE.getCode())).build();
        List<UserExpConfig> list = userExpConfigService.findList(userExpConfigQuery);
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            UserExpConfig expConfig = list.get(0);
            int gameExperience = Objects.equals(user.getMemberType(), 1) ? expConfig.getBasicExp() + expConfig.getMemberBenefit() : expConfig.getBasicExp();
            incrExperience = incrExperience + gameExperience;
        }
        return incrExperience;
    }

    public void updateActivityUser(ZnsRunActivityEntity activity, ZnsUserEntity user) {
        RunActivityTypeStrategy strategy = RunActivityTypeStrategy.findByType(activity.getActivityType());
        BaseActivityStrategy activityStrategy = springContext.getBean(strategy.getStrategy());

        Integer userCount = runActivityUserService.queryActivityUserCount(activity.getId(), true);

        BigDecimal totalBonus = activityStrategy.calculateTotalBonus(activity, userCount, activity.getActivityEntryFee(), null, user, false);

        ZnsRunActivityEntity update = new ZnsRunActivityEntity();
        update.setId(activity.getId());
        update.setUserCount(userCount);
        update.setActivityTotalBonus(totalBonus);
        runActivityService.updateById(update);
    }

    /**
     * 根据概率获取1/2的随机数
     *
     * @return
     */
    private Integer getRandomPageType() {
        String rate = sysConfigService.selectConfigByKey(ConfigKeyEnums.ACTIVITY_DETAIL_AB_RATE.getCode());
        if (!StringUtils.hasText(rate)) return 1;
        String[] split = rate.split(",");
        if (split.length != 2) return 1;
        // 计算比重
        BigDecimal a = new BigDecimal(split[0]);
        BigDecimal b = new BigDecimal(split[1]);
        if (a.add(b).compareTo(new BigDecimal(0)) == 0) return 1;

        BigDecimal aRate = a.divide(a.add(b), 4, RoundingMode.HALF_UP);
        // 生成随机数
        double random = Math.random();
        // B页类型
        if (BigDecimal.valueOf(random).compareTo(aRate) > 0) {
            return 2;
        }
        return 1;
    }

    private Result checkUserTaskEntryLimit(ZnsUserEntity user, ZnsRunActivityEntity activity) {
        Long taskConfigId = activity.getTaskConfigId();
        //npc 不需要检查参赛次数限制

        if (Objects.nonNull(taskConfigId) && !userIdentityService.isNpc(user.getId())) {
            ActivityTaskConfig taskConfigServiceById = activityTaskConfigService.findById(taskConfigId);
            List<ZnsRunActivityEntity> znsRunActivityEntities = runActivityService.selectActivityByBatchNoActivityStateDesc(activity.getBatchNo(), null);
            List<Long> activityIds = znsRunActivityEntities.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
            int entryNum = runActivityUserService.countUserEntryNum(user.getId(), activityIds);
            if (taskConfigServiceById.getEntryLimit() > 0 && entryNum >= taskConfigServiceById.getEntryLimit()) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.participate.count.exceed", taskConfigServiceById.getEntryLimit())); // "You can participate in the current race up to "+ taskConfigServiceById.getEntryLimit() + "times, and you have no permission to participate. Check out other races!"
            }
            return null;
        }
        return null;
    }

    /**
     * 3.0的加拿大用户不能参加老官方活动，下个版本可以去掉
     *
     * @param user
     * @param activityId
     * @param appVersion
     * @return
     */
    public Result checkCanJoin(ZnsUserEntity user, Long activityId, Integer appVersion) {
        String country = user.getCountry();
        if (!"United States".equals(country)) {
            ZnsRunActivityEntity activity = runActivityService.findById(activityId);
            Integer activityType = activity.getActivityType();
            if (RunActivityTypeEnum.officialTypes().contains(activityType)) {
                log.info("Can't join user {},activityId:{},appVersion:{} ", user.getId(), activityId, activityType);
                return CommonResult.fail(999, I18nMsgUtils.getMessage("activity.not.in.scope"));
            }
        }
        return null;

    }
}
