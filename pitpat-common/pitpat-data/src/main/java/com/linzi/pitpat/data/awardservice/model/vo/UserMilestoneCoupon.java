package com.linzi.pitpat.data.awardservice.model.vo;

import com.linzi.pitpat.core.entity.Currency;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class UserMilestoneCoupon {
    //券标题
    private String title;
    //券id
    private Long couponId;
    /**
     * 券金额
     */
    private BigDecimal couponAmount;

    /**
     * 汇率类型
     */
    private Currency currency;
    /**
     * 券过期时间
     */
    private ZonedDateTime expireTime;
    /**
     * 用户券id
     */
    private Long userCouponId;
    /**
     * 用户券获取时间
     */
    private ZonedDateTime gmtCreate;
}
