package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.UserRankDataDto;
import com.linzi.pitpat.data.activityservice.model.entity.RunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.query.RankedActivityPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface RunRankedActivityUserMapper extends BaseMapper<RunRankedActivityUser> {

    List<RunRankedActivityUser> selectListByActivityId(@Param("activityId") Long activityId);

    Page<UserRankDataDto> findPage(Page<UserRankDataDto> page, @Param("query") RankedActivityPageQuery query);


    /**
     * 统计用户排名汇总书记
     *
     * @param userId
     */
    List<RunRankedActivityUser> statisticsUserRankedData(Long userId);

    List<ZonedDateTime> getUserRankFirstDay(Long userId);
}
