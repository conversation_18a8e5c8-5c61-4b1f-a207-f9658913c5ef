package com.linzi.pitpat.data.activityservice.biz;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.linzi.pitpat.data.activityservice.constant.enums.RankActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.RankedLevelEnums;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.RunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevelLog;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RankedUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RankedLevelService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelLogService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.RankedConstant;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/7/22 6:32
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RankActivityBizService {
    private final UserRankedLevelService userRankedLevelService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RunRankedActivityUserService runRankedActivityUserService;
    private final RankedLevelService rankedLevelService;
    private final ISysConfigService sysConfigService;
    private final UserRankedLevelLogService userRankedLevelLogService;
    private final MainActivityService mainActivityService;

    private final UserTaskDetailService userTaskDetailService;
    private final AwardActivityBizService awardActivityBizService;
    private final RedissonClient redissonClient;
    private static final String RANKED_AWARD_SETTLE_PREFIX = "ranked:award:settle:";

    /**
     * 活动结束后更新用户段位隐藏分
     *
     * @param userRunDataDetail
     */
    public void updateRankedActivityUserHideScore(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(userRunDataDetail.getUserId());
        if (Objects.isNull(userRankedLevel)) {
            log.info("用户段位赛未初始化，或者是机器人，无需更新，userRunDataDetailId={}, userId={}", userRunDataDetail.getId(), userRunDataDetail.getUserId());
            return;
        }
        //计算用户当前隐藏分
        BigDecimal hideScore = calcUserHideScore(userRunDataDetail.getUserId());
        hideScore = Optional.ofNullable(hideScore).orElse(BigDecimal.ZERO);

        //更新段位隐藏分
        userRankedLevel.setScore(hideScore);
        userRankedLevelService.update(userRankedLevel);
        log.info("用户活动结束，更新用户段位赛隐藏分userRunDataDetailId={}, userId={}", userRunDataDetail.getId(), userRunDataDetail.getUserId());
    }


    /**
     * 计算用户隐藏分
     *
     * @param userId 用户id
     *               隐藏分
     *               * 1、小于30场比赛，隐藏分=(140*30+实际比赛隐藏分总和)/(实际比赛场次+30）
     *               * 2、大于等于30场比赛，隐藏分=(60场隐藏分总和)/(比赛场次)
     *               * 3、运动记录>500才能有效
     * @return 隐藏分
     */
    public BigDecimal calcUserHideScore(Long userId) {

        //用户出定位的隐藏分
        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(userId);
        Integer initScore = 160;
        if (userRankedLevel != null && userRankedLevel.getPlacementScore() > 0) {
            initScore = userRankedLevel.getPlacementScore();
        }


        List<ZnsUserRunDataDetailsEntity> runDataDetails = userRunDataDetailsService.findRankScoreList(userId, EquipmentDeviceTypeEnum.TREADMILL.getCode());
        if (!org.springframework.util.CollectionUtils.isEmpty(runDataDetails) && runDataDetails.get(0) == null) {
            //防止数组中只有一个空对象
            runDataDetails = new ArrayList<>();
        }
        int size = runDataDetails.size();
        log.info("ZnsUserRunDataDetailsServiceImpl#calcUserHideScore-------->计算用户隐藏分,user={}, runDataDetails size={}", userId, size);
        //初始值（140*30）-> （用户初始定位成功的隐藏分数值*30）
        BigDecimal totalScore = BigDecimal.valueOf(initScore).multiply(BigDecimal.valueOf(30));
        int totalSize = 30;
        if (!CollectionUtils.isEmpty(runDataDetails)) {
            int realSize = runDataDetails.size();
            BigDecimal realTotalPace = calcDetailTotalHideScore(runDataDetails);
            if (realSize >= 30) {
                //大于等于30场比赛，隐藏分=(60场隐藏分总和)/(比赛场次)
                totalScore = realTotalPace;
                totalSize = realSize;
            } else {
                //小于30场比赛，隐藏分=(140*30+实际比赛隐藏分总和)/(实际比赛场次+30）
                totalScore = totalScore.add(realTotalPace);
                totalSize = totalSize + realSize;
            }
        }
        // 隐藏分=比赛隐藏分总和/比赛场次
        BigDecimal hideScore = totalScore.divide(BigDecimal.valueOf(totalSize), 2, RoundingMode.HALF_UP);
        if (hideScore.compareTo(BigDecimal.ZERO) < 0) {
            hideScore = BigDecimal.ZERO;
            log.warn("ZnsUserRunDataDetailsServiceImpl#calcUserHideScore-------->计算用户隐藏分,user={},实际隐藏分小于0，重置隐藏分，{}", userId, hideScore);
        }
        log.info("ZnsUserRunDataDetailsServiceImpl#calcUserHideScore-------->计算用户隐藏分,user=={}, count={},  hideScore={}", userId, size, hideScore);
        return hideScore;
    }

    /**
     * 计算明细总隐藏分
     *
     * @param detailEntities
     * @return
     */
    private BigDecimal calcDetailTotalHideScore(List<ZnsUserRunDataDetailsEntity> detailEntities) {
        BigDecimal totalHideScore = BigDecimal.ZERO;
        for (ZnsUserRunDataDetailsEntity detailEntity : detailEntities) {
            totalHideScore = totalHideScore.add(calcDetailHideScore(detailEntity));
        }
        return totalHideScore;
    }

    /**
     * 计算跑步明细隐藏分
     *
     * @param detailEntity 跑步明细
     *                     隐藏分
     *                     * 1、隐藏分=(设备理论最低配速-当前配速)/15
     *                     * 2、假设用户有效跑步数据平均配速为1000秒/1000米，用户隐藏分为:3600-1000/15=173.33·隐藏分保留小数点后2位，仅作截断
     *                     * 3、运动记录>500才能有效
     * @return 隐藏分
     */
    private BigDecimal calcDetailHideScore(ZnsUserRunDataDetailsEntity detailEntity) {
        if (detailEntity == null) {
            return BigDecimal.ZERO;
        }
        int pace = 3600 - detailEntity.getAveragePace();
        BigDecimal hideScore = BigDecimal.valueOf(pace).divide(BigDecimal.valueOf(15), 2, RoundingMode.DOWN);
        if (hideScore.compareTo(BigDecimal.ZERO) < 0) {
            hideScore = BigDecimal.ZERO;
        }
        return hideScore;
    }

    public RunRankedActivityUser updateRunRankedActivityUser(ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {
        RunRankedActivityUser runRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
        if (Objects.isNull(runRankedActivityUser)) {
            log.error("用户段位赛数据不存在，无法更新状态， userRunDataDetailId={} activityId={}, userId={}", userRunDataDetail.getId(), userRunDataDetail.getActivityId(), userRunDataDetail.getUserId());
            return runRankedActivityUser;
        }

        runRankedActivityUser.setIsComplete(1);
        runRankedActivityUser.setCompleteTime(ZonedDateTime.now());
        //runRankedActivityUser.setRank(userRank);
        runRankedActivityUser.setRunTime(userRunDataDetail.getRunTime());
        runRankedActivityUser.setRunMileage(userRunDataDetail.getRunMileage());

        runRankedActivityUserService.update(runRankedActivityUser);
        return runRankedActivityUser;
    }


    /**
     * 用户排位赛奖励结算
     *
     * @param rankedUserQuery
     * @param source          ,处理来源，queue，job，api
     */
    @Transactional(rollbackFor = Exception.class)
    public void rankAwardSettle(RankedUserQuery rankedUserQuery, String source) {
        log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，来源：{}", rankedUserQuery, source);

        //加锁
        String lockKey = RANKED_AWARD_SETTLE_PREFIX + rankedUserQuery.getUserId() + ":" + rankedUserQuery.getActivityId();
        RLock lock = redissonClient.getLock(lockKey);
        LockHolder.tryLock(lock, 10, 180, () -> {
            RunRankedActivityUser runRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(rankedUserQuery.getActivityId(), rankedUserQuery.getUserId());
            if (runRankedActivityUser == null) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，排位赛报名记录不存在", rankedUserQuery);
                return null;
            }
            ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.getFirstUserActivityRunDataDetails(rankedUserQuery.getUserId(), rankedUserQuery.getActivityId());
            if (userRunDataDetail == null) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，排位赛运动记录不存在", rankedUserQuery);
                return null;
            }

            if (Objects.equals(userRunDataDetail.getIsCheat(), 1)) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，跑步作弊,不发奖励", rankedUserQuery);
                //跑步作弊,不发奖励
                runRankedActivityUser.setRank(-1);
                runRankedActivityUser.setAwardSendStatus(RankedConstant.AwardSendEnum.AWARD_SEND_2.code);
                runRankedActivityUser.setRemark(source + "__跑步作弊");
                runRankedActivityUser.setRankedLevelChange(RankedConstant.LevelChangeEnum.LEVEL_CHANGE_0.code);//段位不变
                runRankedActivityUser.setRankedScoreChange(BigDecimal.ZERO);//升级分数 0
                runRankedActivityUserService.update(runRankedActivityUser);
                return null;
            }

            //更新成绩
            if (runRankedActivityUser.getRank() < 0) {
                //获取排名
                Integer rank = getRankedActivityRank(rankedUserQuery);
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，排名：{}", rankedUserQuery, rank);
                if (rank == null) {
                    return null;
                }

                if (rank == 1) {
                    userTaskDetailService.initUserDailyTask(rankedUserQuery.getUserId());
                    // 每日任务，段位赛第一名
                    userTaskDetailService.completeLevelTask(rankedUserQuery.getUserId(), UserExpObtainSubTypeEnum.WIN_RACE_ONCE.getCode(), true);
                }


                //查询老段位
                UserRankedLevel oldLevel = userRankedLevelService.findByUserId(rankedUserQuery.getUserId());

                //更新段位
                UserRankedLevel newLevel = updateUserRankedLevel(userRunDataDetail, rank);
                if (newLevel == null) {
                    log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，更新段位失败", rankedUserQuery);
                    return null;
                }

                //更新排位赛排名 、段位升降信息
                updateRankedActivityUserRankInfo(runRankedActivityUser, oldLevel, newLevel, rank, source);
            } else {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，排名已更新", rankedUserQuery);
            }

            //奖励发放
            runRankedActivityUser = runRankedActivityUserService.findByActivityIdAndUserId(rankedUserQuery.getActivityId(), rankedUserQuery.getUserId());
            if (RankedConstant.AwardSendEnum.AWARD_SEND_2.code.equals(runRankedActivityUser.getAwardSendStatus())) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，奖励已发放", rankedUserQuery);
                return null;
            }
            if (runRankedActivityUser.getRank() < 0) {
                log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，用户未完赛", rankedUserQuery);
                return null;
            }
            try {
                //发奖励
                MainActivity mainActivity = mainActivityService.findById(rankedUserQuery.getActivityId());
                UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(rankedUserQuery.getUserId());
                sendRankedActivityAward(mainActivity, userRunDataDetail, runRankedActivityUser.getRank(), runRankedActivityUser, userRankedLevel);
                runRankedActivityUser.setAwardSendStatus(RankedConstant.AwardSendEnum.AWARD_SEND_2.code);
                runRankedActivityUser.setRemark(source);
            } catch (Exception e) {
                log.error("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，奖励发放异常", rankedUserQuery, e);
                String message = e.getMessage();
                if (message.length() > 950) {
                    message = message.substring(0, 950);
                }
                message = source + "__" + message;
                runRankedActivityUser.setAwardSendStatus(RankedConstant.AwardSendEnum.AWARD_SEND_3.code);
                runRankedActivityUser.setRemark(message);
            }

            //更新奖励发放状态
            runRankedActivityUserService.update(runRankedActivityUser);
            log.info("RankedActivityResultManager#rankAwardSettle-----用户排位赛奖励结算,入参：{}，用户排位赛奖励结算结束", rankedUserQuery);
            return null;
        });
    }

    /**
     * 更新排位赛排名 、段位升降信息
     *
     * @param runRankedActivityUser
     * @param oldLevel
     * @param newLevel
     * @param rank
     */
    private void updateRankedActivityUserRankInfo(RunRankedActivityUser runRankedActivityUser, UserRankedLevel oldLevel, UserRankedLevel newLevel, Integer rank, String source) {
        //段位变化（1:不变，2:升级，3:降级）
        Integer rankedLevelChange = RankedConstant.LevelChangeEnum.LEVEL_CHANGE_0.code;
        if (newLevel.getLevel() > oldLevel.getLevel() || (newLevel.getLevel().equals(oldLevel.getLevel()) && newLevel.getRank() > oldLevel.getRank())) {
            //升级
            rankedLevelChange = RankedConstant.LevelChangeEnum.LEVEL_CHANGE_1.code;
        } else if (newLevel.getLevel() < oldLevel.getLevel() || (oldLevel.getLevel().equals(newLevel.getLevel()) && newLevel.getRank() < oldLevel.getRank())) {
            //降级
            rankedLevelChange = RankedConstant.LevelChangeEnum.LEVEL_CHANGE_2.code;
        }

        // 段位分数变化(分正负)
        BigDecimal rankedScoreChange = BigDecimal.ZERO;
        if (RankedConstant.LevelChangeEnum.LEVEL_CHANGE_0.code.equals(rankedLevelChange)) {
            //没有段位变化，变化分数 = 新段位进度 - 老段位进度
            rankedScoreChange = (newLevel.getLevelProgress().subtract(oldLevel.getLevelProgress())).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        } else if (RankedConstant.LevelChangeEnum.LEVEL_CHANGE_1.code.equals(rankedLevelChange)) {
            //升级，变化分数 = 100 - 老段位进度
            rankedScoreChange = (BigDecimal.ONE.subtract(oldLevel.getLevelProgress())).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        } else {
            //降级，变化分数 = -(100 - 新段位进度)
            rankedScoreChange = (BigDecimal.ONE.subtract(newLevel.getLevelProgress())).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).multiply(new BigDecimal("-1"));
        }

        //更新排名+进度
        runRankedActivityUser.setRank(rank);
        runRankedActivityUser.setRemark(source);
        runRankedActivityUser.setRankedLevelChange(rankedLevelChange);
        runRankedActivityUser.setRankedScoreChange(rankedScoreChange);
        runRankedActivityUserService.update(runRankedActivityUser);
    }

    public void sendRankedActivityAward(MainActivity mainActivity, ZnsUserRunDataDetailsEntity userRunDataDetail, int rank, RunRankedActivityUser runRankedActivityUser, UserRankedLevel userRankedLevel) {
        log.info("段位赛发放排名奖励，activityId={},userId={},ranking={}", userRunDataDetail.getActivityId(), userRunDataDetail.getUserId(), rank);
        //排名奖励
        sendAward(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), userRunDataDetail.getDistanceTarget().intValue(), userRunDataDetail.getTimeTarget(), rank, userRunDataDetail.getId());

        //段位奖励
        if (Objects.equals(userRankedLevel.getIsNewLevel(), Boolean.FALSE)) {
            log.info("用户未获未获得新的段位，userId={}, rankedLevelId={},highRankedLevelId={}", runRankedActivityUser.getId(), userRankedLevel.getRankedLevelId(), userRankedLevel.getHighRankedLevelId());
            return;
        }
        RankedLevelEnums rankedLevelEnum = RankedLevelEnums.resolve(userRankedLevel.getLevel(), userRankedLevel.getRank());
        if (runRankedActivityUser.getIsPlacement() == 1) {
            //定位赛结束，需要发放定位赛奖励
            if (runRankedActivityUserService.getCurrentRankSegment(userRunDataDetail.getUserId()) == 3) {
                //创建无解线程池
                ExecutorService executorService = Executors.newCachedThreadPool();
                while (Objects.nonNull(rankedLevelEnum)) {
                    RankedLevelEnums finalRankedLevelEnum = rankedLevelEnum;
                    Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
                    executorService.execute(() -> {
                        Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                        log.info("定位赛发放段位奖励 子线程id {}，activityId={},userId={},ranking={}", Thread.currentThread().getId(), mainActivity.getId(), runRankedActivityUser.getId(), rank);
                        sendAward(AwardSentTypeEnum.RANK_AWARD.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), userRunDataDetail.getDistanceTarget().intValue(), userRunDataDetail.getTimeTarget(), finalRankedLevelEnum.getIndex(), userRunDataDetail.getId());
                    });
                    rankedLevelEnum = rankedLevelEnum.resolvePreviousRank(rankedLevelEnum.getLevel(), rankedLevelEnum.getRank());
                }
                //关闭线程池
                executorService.shutdown();
                try {
                    executorService.awaitTermination(20, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } else {
            log.info("段位赛发放段位奖励，activityId={},userId={},ranking={}", mainActivity.getId(), runRankedActivityUser.getId(), rank);
            sendAward(AwardSentTypeEnum.RANK_AWARD.getType(), mainActivity.getTargetType(), runRankedActivityUser.getUserId(), runRankedActivityUser.getActivityId(), userRunDataDetail.getDistanceTarget().intValue(), userRunDataDetail.getTimeTarget(), rankedLevelEnum.getIndex(), userRunDataDetail.getId());

        }
    }


    public UserRankedLevel updateUserRankedLevel(ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {

        Long userId = userRunDataDetail.getUserId();
        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(userId);
        //处于定位中
        if (userRankedLevel.getIsInPlacement() == 1) {
            //定位赛处理
            userRankedLevel = placementUpdateUserRankedLevel(userRankedLevel, userRunDataDetail, userRank);

        } else {
            //普通段位赛处理
            userRankedLevel = rankUpdateUserRankedLevel(userRankedLevel, userRunDataDetail, userRank);
        }

        return userRankedLevel;
    }

    private UserRankedLevel rankUpdateUserRankedLevel(UserRankedLevel userRankedLevel, ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {
        Long userId = userRankedLevel.getUserId();
//        String sysConfig = sysConfigService.selectConfigByKey("new_ranked_activity_config");
//        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);
//        List<HashMap<String, Object>> rankedProgressList = (List<HashMap<String, Object>>) rankedSysConfig.get("rankedProgressList");
//        //获取可能的段位升级、或回落的进度
//        HashMap<String, Object> rankedProcessMap = rankedProgressList.stream().filter(item -> Objects.equals(MapUtils.getInteger(item, "ranking"), userRank)).findFirst().orElse(null);
//
//        if (Objects.isNull(rankedProcessMap)) {
//            log.error("无法获取该段位的奖励分值，userId={}, rank={}", userRunDataDetail.getActivityId(), userRank);
//            return null;
//        }


        UserRankedLevel updateUserRanked = new UserRankedLevel();
        BeanUtils.copyProperties(userRankedLevel, updateUserRanked);
        log.info("当前用户段位信息， level={}, rank={}, levelId={}", userRankedLevel.getLevel(), userRankedLevel.getRank(), userRankedLevel.getRankedLevelId());
        //当场活动中获取新的段位进度
        RankedLevel rankedLevel = rankedLevelService.findById(userRankedLevel.getRankedLevelId());
//        BigDecimal growLevelProgress = BigDecimal.valueOf(MapUtils.getDoubleValue(rankedProcessMap, "progress"));
        BigDecimal growLevelProgress = sysConfigService.getGrowLevelProgress(userRunDataDetail.getDistanceTarget(), userRank);

        //根据当前用户的隐藏分，再次计算用户段位进度
        if (userRankedLevel.getScore().compareTo(rankedLevel.getMinScore()) < 0 || userRankedLevel.getScore().compareTo(rankedLevel.getMaxScore()) > 0) {
            BigDecimal averageLevelScore = (rankedLevel.getMinScore().add(rankedLevel.getMaxScore())).divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
            BigDecimal averageScore = userRankedLevel.getScore().divide(averageLevelScore, 2, RoundingMode.HALF_UP);
            log.info("用户的隐藏分不在段位区间，需要增加权重，userId={},userScore={}, minScore={}, maxScore={},growLevelProgress={}, averageScore={}", userId, userRankedLevel.getScore(), rankedLevel.getMinScore(), rankedLevel.getMaxScore(), growLevelProgress, averageScore);
            if (userRank <= 4) {
                if (averageScore.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("计算后的隐藏分为0，重置为1");
                    averageScore = BigDecimal.ONE;
                }
                growLevelProgress = growLevelProgress.multiply(averageScore).setScale(2, RoundingMode.HALF_UP);
            } else {
                if (averageScore.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("计算后的隐藏分为0，重置为0.01");
                    averageScore = BigDecimal.valueOf(0.01);
                }
                growLevelProgress = growLevelProgress.divide(averageScore, 2, RoundingMode.HALF_UP);
            }
            log.info("重新计算隐藏分后,growLevelProgress={}", growLevelProgress);
        }

        //是否发放段位奖励
        RankedLevelEnums newRankedLevel = null;
        BigDecimal levelProgress = userRankedLevel.getLevelProgress();
        BigDecimal newLevelProgress = BigDecimal.ZERO;

        if (growLevelProgress.compareTo(BigDecimal.ZERO) > 0) {
            newLevelProgress = levelProgress.add(growLevelProgress);
            //用户的新段位
            if (newLevelProgress.compareTo(BigDecimal.ONE) > -1) {
                //段位升级
                newRankedLevel = RankedLevelEnums.resolveNextRank(userRankedLevel.getLevel(), userRankedLevel.getRank());
                if (Objects.nonNull(newRankedLevel)) {
                    newLevelProgress = BigDecimal.ZERO; //大于1， 升段位，重新重制
                    log.info("段位升级，新段位");
                } else {
                    newLevelProgress = BigDecimal.ONE; //最大段位，保持进度100%
                    log.info("已经升到最高段位");
                }
            }
        } else {
            if (levelProgress.compareTo(BigDecimal.ZERO) == 0) {
                //段位降级
                newRankedLevel = RankedLevelEnums.resolvePreviousRank(userRankedLevel.getLevel(), userRankedLevel.getRank());
                log.info("降级从{}到段位：{},进度={}", userRankedLevel, newRankedLevel, newLevelProgress);
                if (Objects.nonNull(newRankedLevel)) {
                    newLevelProgress = BigDecimal.ONE.add(growLevelProgress);
                }
            } else {
                newLevelProgress = levelProgress.add(growLevelProgress);
            }
            //小于0
            if (newLevelProgress.compareTo(BigDecimal.ZERO) < 0) {
                newLevelProgress = BigDecimal.ZERO;
            }
        }
        //段位四舍五入
        newLevelProgress = newLevelProgress.setScale(2, RoundingMode.HALF_UP);
        log.info("用户段位赛中的获得段位进度，userId={}, rank={},newLevelProgress={}, levelProgress={},growLevelProgress={},activityId={}", userId, userRank, newLevelProgress, levelProgress, growLevelProgress, userRunDataDetail.getActivityId());

        if (Objects.nonNull(newRankedLevel)) {
            rankedLevel = rankedLevelService.findByLevelAndRank(newRankedLevel.getLevel(), newRankedLevel.getRank());
            log.info("新段位信息={}", newRankedLevel.getIndex());
        }

        log.info("计算后，用户当前的段位进度，userId={}, levelId={}, newLevelId={},", userId, userRankedLevel.getId(), rankedLevel.getId());

        if (Objects.nonNull(newRankedLevel)) {
            RankedLevel newRankedLevelEntity = rankedLevelService.findByLevelAndRank(newRankedLevel.getLevel(), newRankedLevel.getRank());
            if (newRankedLevelEntity.getId() > userRankedLevel.getHighRankedLevelId()) {
                updateUserRanked.setIsNewLevel(true);
                updateUserRanked.setActivityId(userRunDataDetail.getActivityId());
                updateUserRanked.setHighRankedLevelId(newRankedLevelEntity.getId());
            } else {
                //已获取最新的段位信息
                log.info("用户已获取该段位的奖励，不再重复发放，userId={}, newRankedLevelId={},highRankedLevelId={} bonus", userId, newRankedLevelEntity.getId(), userRankedLevel.getHighRankedLevelId());
            }
        }
        //更新段位信息
        //如果不展示新段位奖励，就展示段位奖励Toast
        if (Objects.equals(userRankedLevel.getIsNewLevel(), Boolean.FALSE)) {
            updateUserRanked.setShowAwardPop(true);
        }

        updateUserRanked.setLevelProgress(newLevelProgress);
        updateUserRanked.setRank(rankedLevel.getRank());
        updateUserRanked.setLevel(rankedLevel.getLevel());
        updateUserRanked.setName(rankedLevel.getName());
        updateUserRanked.setRankedLevelId(rankedLevel.getId());

        userRankedLevelService.update(updateUserRanked);
        createUserRankedLevelLog(updateUserRanked, levelProgress, growLevelProgress, userRank, userRunDataDetail);

        return updateUserRanked;
    }

    private void createUserRankedLevelLog(UserRankedLevel userRankedLevel, BigDecimal levelProgress, BigDecimal growLevelProgress, Integer userRank, ZnsUserRunDataDetailsEntity userRunDataDetail) {
        if (userRunDataDetail.getIsRobot() == 0) {
            UserRankedLevelLog userRankedLevelLog = new UserRankedLevelLog();
            BeanUtils.copyProperties(userRankedLevel, userRankedLevelLog, "id", "gmtCreate", "gmtModified");
            userRankedLevelLog.setLevelProgressGrow(growLevelProgress);
            userRankedLevelLog.setLevelProgressPreview(levelProgress);
            userRankedLevelLog.setRanking(userRank);
            userRankedLevelLog.setRunDataDetailId(userRunDataDetail.getId());
            userRankedLevelLog.setActivityId(userRunDataDetail.getActivityId());
            userRankedLevelLogService.insert(userRankedLevelLog);
            log.info("保存用户段位赛数据日志，userId={}, userRank={}, isRobot={}", userRankedLevelLog.getUserId(), userRank, userRunDataDetail.getIsRobot());
        } else {
            log.info("机器人不记录段位赛用户数据日志userId={}，userRunDataDetail={}", userRankedLevel.getUserId(), userRunDataDetail.getId());
        }
    }

    private UserRankedLevel placementUpdateUserRankedLevel(UserRankedLevel userRankedLevel, ZnsUserRunDataDetailsEntity userRunDataDetail, Integer userRank) {
        Long userId = userRankedLevel.getUserId();
        Integer segment = runRankedActivityUserService.getCurrentRankSegment(userId);

        //最后一场定位赛 已经完成了两场 这是最后一场
        if (segment == 2) {
            Integer level = runRankedActivityUserService.getCurrentPlacementRankLevel(userId);

            if (userRank == 1) {
                level = level + 1;
            }
            //根据level初始化段位等级
            RankedLevelEnums rankedLevelEnum = RankActivityConstants.PLACEMENT_SCORE_MAP.get(Math.min(level, 3));

            RankedLevel rankedLevel = rankedLevelService.findByLevelAndRank(rankedLevelEnum.getLevel(), rankedLevelEnum.getRank());
            userRankedLevel.setIsNewLevel(true);
            userRankedLevel.setActivityId(userRunDataDetail.getActivityId());
            userRankedLevel.setHighRankedLevelId(rankedLevel.getId());
            userRankedLevel.setLevelProgress(BigDecimal.ZERO.setScale(2));
            userRankedLevel.setRank(rankedLevel.getRank());
            userRankedLevel.setLevel(rankedLevel.getLevel());
            userRankedLevel.setName(rankedLevel.getName());
            userRankedLevel.setRankedLevelId(rankedLevel.getId());
            userRankedLevel.setIsInPlacement(0);
            //更新用户定位隐藏分

            userRankedLevel.setPlacementScore(RankActivityConstants.SEGMENT_SCORE_MAP.get(Math.min(level, 3)));
            userRankedLevelService.update(userRankedLevel);


        }
        return userRankedLevel;
    }

    /**
     * 获取排位赛排名
     *
     * @param rankedUserQuery
     */
    public Integer getRankedActivityRank(RankedUserQuery rankedUserQuery) {
        Long activityId = rankedUserQuery.getActivityId();
        List<ZnsUserRunDataDetailsEntity> userRunDataDetailList = userRunDataDetailsService.findEndByActivityId(activityId);
        if (org.springframework.util.CollectionUtils.isEmpty(userRunDataDetailList)) {
            log.info("RankedActivityResultManager#rankAwardSettle-----更新排位赛排名,入参：{}，排位赛跑步记录不存在", rankedUserQuery);
        }
        //跑步完成的用户运动排名(排除作弊的)
        userRunDataDetailList = userRunDataDetailList.stream()
                .filter(item -> item.getRunMileage().intValue() >= item.getDistanceTarget().intValue())
                .filter(item -> Objects.equals(item.getIsCheat(), 0))
                .sorted(Comparator.comparingInt(ZnsUserRunDataDetailsEntity::getRunTimeMillisecond)).toList();
        userRunDataDetailList.forEach(item -> {
            log.info("RankedActivityResultManager#rankAwardSettle-----更新排位赛排名,userId={},activityId={}，detailId={}, runStatus={},runMileage={},runTimeMillisecond={},averagePace={},", item.getUserId(), activityId, item.getId(), item.getRunStatus(), item.getRunMileage(), item.getRunTimeMillisecond(), item.getAveragePace());
        });
        for (int i = 0; i < userRunDataDetailList.size(); i++) {
            ZnsUserRunDataDetailsEntity runDataDetails = userRunDataDetailList.get(i);
            if (runDataDetails.getUserId().equals(rankedUserQuery.getUserId())) {
                return i + 1;
            }
        }
        return null;
    }

    private void sendAward(Integer type, Integer targetType, Long userId, Long activityId, Integer targetRunMileage, Integer targetRunTime, Integer rank, Long runDataDetailsId) {
        log.info("sendAward,type={},targetType={}", type, targetType);
        //奖励发放
        AwardSendDto awardSendDto = new AwardSendDto();
        awardSendDto.setType(type);
        if (targetType == 1) {
            awardSendDto.setTarget(targetRunMileage);
        } else if (targetType == 2) {
            awardSendDto.setTarget(targetRunTime);
        }
        awardSendDto.setUserId(userId);
        awardSendDto.setActivityId(activityId);
        awardSendDto.setRank(rank);
        awardSendDto.setDetailId(runDataDetailsId);
        awardActivityBizService.sendActivityAwardByConfigAndStage(awardSendDto);
    }
}
