package com.linzi.pitpat.data.activityservice.manager.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.RunDataBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ResultDataStateEnum;
import com.linzi.pitpat.data.activityservice.converter.api.RealUserRunDataDetailsConverter;
import com.linzi.pitpat.data.activityservice.dto.api.request.ActivityDataDetailsListRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.ActivityDataDetailsListResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.ActivityDataDetailsResponseDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.StatisticsTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.request.RunDataStatisticsRequest;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.trainingplanservice.biz.TrainingPlanBizService;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanDetailService;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanService;
import com.linzi.pitpat.data.userservice.dto.response.UserRunTotalDto;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.UserFeedbackDataDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserFeedbackDataQuery;
import com.linzi.pitpat.data.userservice.service.UserFeedbackDataService;
import com.linzi.pitpat.data.vo.runData.RunHighlightsVo;
import com.linzi.pitpat.data.vo.runData.RunRecordDetailsVo;
import com.linzi.pitpat.data.vo.runData.TotalRunRecordVo;
import com.linzi.pitpat.data.vo.runData.UserRunningRecordVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class RealPersonRunDataDetailsManager {
    private static final Set<String> restrictedCodes = Set.of("R1", "S1");

    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final UserTrainingPlanDetailService userTrainingPlanDetailService;
    private final UserTrainingPlanService userTrainingPlanService;
    private final TrainingPlanBizService trainingPlanBizService;
    private final ZnsUserRunDataService znsUserRunDataService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ZnsCourseService znsCourseService;
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final UserFeedbackDataService userFeedbackDataService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final RunDataBizService runDataBizService;
    private final RealUserRunDataDetailsConverter realUserRunDataDetailsConverter;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final ISysConfigService sysConfigService;
    private final ZnsTreadmillService treadmillService;

    public List<TotalRunRecordVo> findRunningStatistics(RunDataStatisticsRequest request, ZnsUserEntity loginUser, String zoneId) {
        Integer appVersion = loginUser.getAppVersion();
        boolean flag = appVersion >= VersionConstant.V4_7_1;// 脚踏车和单车运动数据合并标志
        //设备类型判断
        List<Integer> deviceTypes = new ArrayList<>();
        if (Objects.equals(request.getDeviceType(), -1)) {
            deviceTypes = EquipmentDeviceTypeEnum.getAllDeviceTypList();
        } else {
            if (flag && EquipmentDeviceTypeEnum.BIKE.getCode().equals(request.getDeviceType())) {
                deviceTypes.add(EquipmentDeviceTypeEnum.BICYCLE.getCode());
            }
            deviceTypes.add(request.getDeviceType());
        }
        UserRunTotalDto treadmill = realPersonRunDataDetailsService.sumAllRunTimeAndMileage(loginUser.getId(), List.of(EquipmentDeviceTypeEnum.TREADMILL.getCode()));
        UserRunTotalDto rowing = realPersonRunDataDetailsService.sumAllRunTimeAndMileage(loginUser.getId(), List.of(EquipmentDeviceTypeEnum.ROWING.getCode()));
        UserRunTotalDto deskBike;
        UserRunTotalDto bicycle;
        if (flag) {
            deskBike = new UserRunTotalDto();
            deskBike.setRunMileage(BigDecimal.ZERO);
            deskBike.setRunTime(0);
            deskBike.setRunDay(0);
            bicycle = realPersonRunDataDetailsService.sumAllRunTimeAndMileage(loginUser.getId(), List.of(EquipmentDeviceTypeEnum.BIKE.getCode(), EquipmentDeviceTypeEnum.BICYCLE.getCode()));
        } else {
            deskBike = realPersonRunDataDetailsService.sumAllRunTimeAndMileage(loginUser.getId(), List.of(EquipmentDeviceTypeEnum.BICYCLE.getCode()));
            bicycle = realPersonRunDataDetailsService.sumAllRunTimeAndMileage(loginUser.getId(), List.of(EquipmentDeviceTypeEnum.BIKE.getCode()));
        }

        List<TotalRunRecordVo> totalRunRecordVos = new ArrayList<>();
        //总数据是要按周做增量处理
        if (Objects.equals(request.getStatisticsType(), StatisticsTypeEnum.ALL.getCode())) {
            //注册时间
            ZonedDateTime createTime = loginUser.getCreateTime();
            List<RealPersonRunDataDetails> list = realPersonRunDataDetailsService.findListWithZeroCalorie(loginUser.getId(), createTime, ZonedDateTime.now(), deviceTypes, List.of("R1", "S1"));
            //数据划分成周
            Map<ZonedDateTime, List<RealPersonRunDataDetails>> weeklyData = getWeeklyData(createTime, list, zoneId);

            for (Map.Entry<ZonedDateTime, List<RealPersonRunDataDetails>> entry : weeklyData.entrySet()) {
                ZonedDateTime weekTime = entry.getKey();
                List<RealPersonRunDataDetails> weekRecords = entry.getValue();
                UserRunningRecordVo userRunningRecordVo = new UserRunningRecordVo();
                //数据填充
                getStatistics(weekRecords, userRunningRecordVo, zoneId);
                TotalRunRecordVo totalRunRecordVo = new TotalRunRecordVo();
                totalRunRecordVo.setDateTime(weekTime);
                totalRunRecordVo.setUserRunningRecordVo(userRunningRecordVo);
                totalRunRecordVo.setRegisterTime(loginUser.getCreateTime());
                totalRunRecordVo.setTreadmillRunMileage(treadmill.getRunMileage().setScale(0, RoundingMode.HALF_UP));
                totalRunRecordVo.setBicycleRunMileage(bicycle.getRunMileage().setScale(0, RoundingMode.HALF_UP));
                totalRunRecordVo.setRowingRunMileage(rowing.getRunMileage().setScale(0, RoundingMode.HALF_UP));
                totalRunRecordVo.setDeskBikeRunMileage(deskBike.getRunMileage().setScale(0, RoundingMode.HALF_UP));
                totalRunRecordVo.setRunTimeTotal(treadmill.getRunTime() + bicycle.getRunTime() + rowing.getRunTime() + deskBike.getRunTime());
                totalRunRecordVos.add(totalRunRecordVo);
            }
            return totalRunRecordVos;
        } else {
            //解决冬夏令时的时间差
            List<RealPersonRunDataDetails> records = realPersonRunDataDetailsService.findListByTime(loginUser.getId(), request.getStartTime(), request.getEndTime(), deviceTypes);
            request.setStartTime(DateUtil.addHours(request.getStartTime(), 3));
            //前端请求携带查询时间，按日期类型分为十条数据返回
            for (int i = 0; i < 10; i++) {
                int finalI = i;
                fillStatistics(request, records, finalI, totalRunRecordVos, zoneId, loginUser, treadmill, bicycle, rowing, deskBike);
            }
        }
        return totalRunRecordVos;
    }

    //填充用户运动总数据
    private void fillStatistics(RunDataStatisticsRequest request, List<RealPersonRunDataDetails> records, int finalI, List<TotalRunRecordVo> totalRunRecordVos, String zoneId, ZnsUserEntity loginUser, UserRunTotalDto treadmill, UserRunTotalDto bicycle, UserRunTotalDto rowing, UserRunTotalDto deskBike) {
        StatisticsTypeEnum statisticsTypeEnum = StatisticsTypeEnum.findTypeByCode(request.getStatisticsType());
        TotalRunRecordVo totalRunRecordVo = new TotalRunRecordVo();
        totalRunRecordVo.setTreadmillRunMileage(treadmill.getRunMileage());
        totalRunRecordVo.setBicycleRunMileage(bicycle.getRunMileage());
        totalRunRecordVo.setRowingRunMileage(rowing.getRunMileage());
        totalRunRecordVo.setDeskBikeRunMileage(deskBike.getRunMileage());
        totalRunRecordVo.setRunTimeTotal(treadmill.getRunTime() + bicycle.getRunTime() + rowing.getRunTime() + deskBike.getRunTime());
        ZonedDateTime startTime = null;
        ZonedDateTime endTime = null;
        switch (statisticsTypeEnum) {
            case DAY -> {
                totalRunRecordVo.setDateTime(DateUtil.addDays(request.getStartTime(), finalI));
                startTime = DateUtil.getStartOfDayByZoneId(DateUtil.addDays(request.getStartTime(), finalI), zoneId);
                endTime = DateUtil.getStartOfDayByZoneId(DateUtil.addDays(request.getStartTime(), 1 + finalI), zoneId);
            }
            case WEEK -> {
                totalRunRecordVo.setDateTime(DateUtil.addWeeks(request.getStartTime(), finalI));
                startTime = DateUtil.getStartOfWeekByZoneId(DateUtil.addWeeks(request.getStartTime(), finalI), zoneId);
                endTime = DateUtil.getStartOfWeekByZoneId(DateUtil.addWeeks(request.getStartTime(), 1 + finalI), zoneId);
            }
            case MONTH -> {
                totalRunRecordVo.setDateTime(DateUtil.addMonthsByZoneId(request.getStartTime(), finalI, zoneId));
                startTime = DateUtil.getStartOfMonth(DateUtil.addMonthsByZoneId(request.getStartTime(), finalI, zoneId), TimeZone.getTimeZone(zoneId));
                endTime = DateUtil.addMonthsByZoneId(startTime, 1, zoneId);
            }
            case YEAR -> {
                totalRunRecordVo.setDateTime(DateUtil.addYearsByZoneId(request.getStartTime(), finalI, zoneId));
                startTime = DateUtil.addYears(request.getStartTime(), finalI);
                endTime = DateUtil.addYears(request.getStartTime(), 1 + finalI);
            }
        }
        ZonedDateTime finalStartTime = startTime;
        ZonedDateTime finalEndTime = endTime;
        totalRunRecordVo.setRegisterTime(loginUser.getCreateTime());
        List<RealPersonRunDataDetails> collect = records.stream().filter(s -> s.getCreateTime().compareTo(finalStartTime) >= 0 && s.getCreateTime().compareTo(finalEndTime) < 0).toList();
        //空数据时也要返回时间
        if (CollectionUtils.isEmpty(collect) && IsAddDetail(request, loginUser, totalRunRecordVo)) {
            totalRunRecordVos.add(totalRunRecordVo);
            return;
        }
        UserRunningRecordVo userRunningRecordVo = new UserRunningRecordVo();
        //运动数据填充
        getStatistics(collect, userRunningRecordVo, zoneId);
        totalRunRecordVo.setUserRunningRecordVo(userRunningRecordVo);
        //过滤超过当前时间数据
        if (IsAddDetail(request, loginUser, totalRunRecordVo)) {
            totalRunRecordVos.add(totalRunRecordVo);
        }
    }

    private static boolean IsAddDetail(RunDataStatisticsRequest request, ZnsUserEntity loginUser, TotalRunRecordVo totalRunRecordVo) {
        StatisticsTypeEnum statisticsTypeEnum = StatisticsTypeEnum.findTypeByCode(request.getStatisticsType());
        ZonedDateTime date = totalRunRecordVo.getDateTime();
        switch (statisticsTypeEnum) {
            case DAY -> date = DateUtil.addDays(totalRunRecordVo.getDateTime(), 1);
            case WEEK -> date = DateUtil.addWeeks(totalRunRecordVo.getDateTime(), 1);
            case MONTH -> date = DateUtil.addMonths(totalRunRecordVo.getDateTime(), 1);
            case YEAR -> date = DateUtil.addYears(totalRunRecordVo.getDateTime(), 1);
        }
        return totalRunRecordVo.getDateTime().isBefore(request.getEndTime()) && date.isAfter(loginUser.getCreateTime());
    }

    //用户运动统计数据填充
    private void getStatistics(List<RealPersonRunDataDetails> collect, UserRunningRecordVo userRunningRecordVo, String zoneId) {
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        userRunningRecordVo.setRunDays((int) collect.stream().map(record -> record.getCreateTime().toInstant().atZone(ZoneId.of(zoneId)).toLocalDate()).distinct().count());
        userRunningRecordVo.setTimes(collect.size());
        userRunningRecordVo.setTotalRunMileage(collect.stream().map(RealPersonRunDataDetails::getRunMileage).reduce(BigDecimal.ZERO, BigDecimal::add));
        userRunningRecordVo.setTotalStepNum(collect.stream().mapToInt(RealPersonRunDataDetails::getStepNum).sum());
        userRunningRecordVo.setTotalTime(collect.stream().mapToInt(RealPersonRunDataDetails::getRunTime).sum());
        userRunningRecordVo.setRotateNum(collect.stream().filter(e -> Objects.nonNull(e.getRotateNum())).mapToInt(RealPersonRunDataDetails::getRotateNum).sum());
        userRunningRecordVo.setFatConsumption(collect.stream().map(RealPersonRunDataDetails::getKilocalorie).reduce(BigDecimal.ZERO, BigDecimal::add).intValue());
        userRunningRecordVo.setAveragePace((int) collect.stream().mapToInt(RealPersonRunDataDetails::getAveragePace).average().orElse(0.0));
        userRunningRecordVo.setAverageVelocity(collect.stream().map(RealPersonRunDataDetails::getAverageVelocity).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(collect.size()), 2, RoundingMode.HALF_UP));
        List<RunHighlightsVo> highlightsVos = new ArrayList<>();
        //高光里程值
        RunHighlightsVo highMileage = new RunHighlightsVo();
        RealPersonRunDataDetails mileage = collect.stream().max(Comparator.comparing(RealPersonRunDataDetails::getRunMileage)).get();
        highMileage.setRunMileage(mileage.getRunMileage());
        highMileage.setCreateTime(DateUtil.convertTimeZone(mileage.getCreateTime(), "UTC", zoneId));
        highlightsVos.add(highMileage);
        //高光消耗量
        RunHighlightsVo kilocalorie = new RunHighlightsVo();
        RealPersonRunDataDetails highKilocalorie = collect.stream().max(Comparator.comparing(RealPersonRunDataDetails::getKilocalorie)).get();
        kilocalorie.setCreateTime(DateUtil.convertTimeZone(highKilocalorie.getCreateTime(), "UTC", zoneId));
        kilocalorie.setKilocalorie(highKilocalorie.getKilocalorie());
        highlightsVos.add(kilocalorie);
        //高光配速
        RunHighlightsVo highPace = new RunHighlightsVo();
        RealPersonRunDataDetails pace = collect.stream().min(Comparator.comparing(RealPersonRunDataDetails::getAveragePace)).get();
        highPace.setCreateTime(DateUtil.convertTimeZone(pace.getCreateTime(), "UTC", zoneId));
        highPace.setPace(pace.getAveragePace());
        highlightsVos.add(highPace);
        //高光能力值
        RealPersonRunDataDetails highCapabilityValue = collect.stream().max(Comparator.comparing(RealPersonRunDataDetails::getCapabilityValue)).get();
        RunHighlightsVo capabilityValue = new RunHighlightsVo();
        capabilityValue.setCreateTime(DateUtil.convertTimeZone(highCapabilityValue.getCreateTime(), "UTC", zoneId));
        capabilityValue.setCapabilityValue(highCapabilityValue.getCapabilityValue());
        highlightsVos.add(capabilityValue);
        userRunningRecordVo.setHighlights(highlightsVos);
    }

    public Map<ZonedDateTime, List<RealPersonRunDataDetails>> getWeeklyData(ZonedDateTime registerTime, List<RealPersonRunDataDetails> list, String zoneId) {
        Map<ZonedDateTime, List<RealPersonRunDataDetails>> weeklyDataMap = new TreeMap<>();

        ZoneId zone = ZoneId.of(zoneId);
        // 初始化起始周时间为注册时间所在周的星期一（00:00）
        ZonedDateTime startOfWeek = ZonedDateTime.ofInstant(registerTime.toInstant(), zone)
                .with(DayOfWeek.MONDAY) // 设置为星期一
                .truncatedTo(ChronoUnit.DAYS); // 去除时间部分，保留日期


        // 设置初始结束日期为第一周的最后一天（周日的 23:59:59.999）
        ZonedDateTime endOfWeek = startOfWeek.plusDays(6)
                .withHour(23)
                .withMinute(59)
                .withSecond(59)
                .withNano(999_999_999);

        List<RealPersonRunDataDetails> cumulativeRecords = new ArrayList<>();
        while (Date.from(startOfWeek.toInstant()).isBefore(ZonedDateTime.now())) {
            List<RealPersonRunDataDetails> weeklyRecords = new ArrayList<>();

            for (RealPersonRunDataDetails record : list) {
                // 将 ZonedDateTime 转换为 ZonedDateTime 对象，并获取时间戳
                long createTimeMillis = record.getCreateTime().toInstant().toEpochMilli();

                // 比较 record 的创建时间是否在 startOfWeek 和 endOfWeek 之间
                if (createTimeMillis >= Date.from(startOfWeek.toInstant()).toInstant().toEpochMilli() &&
                        createTimeMillis <= Date.from(endOfWeek.toInstant()).toInstant().toEpochMilli()) {
                    weeklyRecords.add(record);
                }
            }            // 将累积的数据加入本周数据列表
            cumulativeRecords.addAll(weeklyRecords);
            weeklyDataMap.put(Date.from(startOfWeek.toInstant()), new ArrayList<>(cumulativeRecords));

            // 准备下一周的统计
            startOfWeek = startOfWeek.plusWeeks(1);
            endOfWeek = endOfWeek.plusWeeks(1);

            // 如果结束时间超过当前时间，设置为当天的最后一刻
            ZonedDateTime now = ZonedDateTime.now(ZoneId.of(zoneId));
            if (endOfWeek.isAfter(now)) {
                endOfWeek = now
                        .withHour(23)
                        .withMinute(59)
                        .withSecond(59)
                        .withNano(999_999_999);
            }
        }
        return weeklyDataMap;
    }


    //查询范围内运动记录条数
    public Map<String, Object> getRunningRecordV2(RunDataStatisticsRequest request, ZnsUserEntity loginUser, String zoneId) throws ParseException {
        //设备类型判断
        Integer appVersion = loginUser.getAppVersion();
        boolean flag = appVersion >= VersionConstant.V4_7_1;// 脚踏车和单车运动数据合并标志
        //设备类型判断
        List<Integer> deviceTypes = new ArrayList<>();
        if (Objects.equals(request.getDeviceType(), -1)) {
            deviceTypes = EquipmentDeviceTypeEnum.getAllDeviceTypList();
        } else {
            if (flag && EquipmentDeviceTypeEnum.BIKE.getCode().equals(request.getDeviceType())) {
                deviceTypes.add(EquipmentDeviceTypeEnum.BICYCLE.getCode());
            }
            deviceTypes.add(request.getDeviceType());
        }
        Page<TotalRunRecordVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        Map<String, Object> data = new HashMap<>();
        if (Objects.equals(request.getStatisticsType(), StatisticsTypeEnum.ALL.getCode())) {
            request.setStartTime(loginUser.getCreateTime());
            request.setEndTime(ZonedDateTime.now());
        } else {
            fixFindTime(request, zoneId);
        }
        Page<RealPersonRunDataDetails> detailsPage = realPersonRunDataDetailsService.findRecordByTime(page, loginUser.getId(), request.getStartTime(), request.getEndTime(), deviceTypes);

        List<RealPersonRunDataDetails> records = detailsPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return data;
        }
        //排序
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM", Locale.ENGLISH);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //按月分组

        Map<String, List<RealPersonRunDataDetails>> listMap = records.stream().collect(Collectors.groupingBy(d -> simpleDateFormat.format(
                DateUtil.getDateByStrAndZone(formatter.format(d.getCreateTime()), zoneId))));

        List<Map<String, Object>> list = new ArrayList<>();
        List<Map.Entry<String, List<RealPersonRunDataDetails>>> entryList = listMap.entrySet().stream().sorted((x, y) -> {
            try {
                ZonedDateTime xDate = simpleDateFormat.parse(x.getKey().toString());
                ZonedDateTime yDate = simpleDateFormat.parse(y.getKey().toString());
                return yDate.compareTo(xDate);
            } catch (ParseException e) {
                e.printStackTrace();
                return 0;
            }
        }).collect(Collectors.toList());
        //设备卡路里处理
        List<Long> treadmilIdList = records.stream().map(RealPersonRunDataDetails::getTreadmillId).toList();
        List<ZnsTreadmillEntity> treadmillEntities = treadmillService.findByIds(treadmilIdList);
        Map<Long, ZnsTreadmillEntity> treadmillMap = treadmillEntities.stream().collect(Collectors.toMap(ZnsTreadmillEntity::getId, z -> z));
        for (Map.Entry<String, List<RealPersonRunDataDetails>> stringListEntry : entryList) {
            Map<String, Object> map = new HashMap<>();
            map.put("years", stringListEntry.getKey());
            ZonedDateTime date = DateTimeUtil.parseYearMonthWithPattern(stringListEntry.getKey(), "yyyy-MM");
            ZonedDateTime date2ByTimeZone = DateUtil.getTimeZoneDate(DateUtil.addDays1(date, 2), TimeZone.getTimeZone(zoneId));
            map.put("createTime", date2ByTimeZone);
            map.put("dataType", 1);
            List<RealPersonRunDataDetails> detailList = stringListEntry.getValue().stream().sorted(Comparator.comparing(RealPersonRunDataDetails::getCreateTime).reversed()).toList();

            List<Map<String, Object>> detailListMap = new ArrayList<>();
            for (RealPersonRunDataDetails realPersonRunDataDetails : detailList) {
                Map<String, Object> detailMap = getDetailMap(realPersonRunDataDetails, loginUser, zoneId, treadmillMap.get(realPersonRunDataDetails.getTreadmillId()));
                detailListMap.add(detailMap);
            }
            list.add(map);
            list.addAll(detailListMap);
        }
        data.put("list", list);
        data.put("total", page.getTotal());
        data.put("current", page.getCurrent());
        data.put("size", page.getSize());
        return data;
    }

    public Page<RunRecordDetailsVo> getDetailsVoList(PageQuery pageQuery, ZnsUserEntity user, ZonedDateTime startDate, ZonedDateTime endDate) {
        Page<RealPersonRunDataDetails> detailsPage = realPersonRunDataDetailsService.findRecordByTime(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()), user.getId(), startDate, endDate, new ArrayList<>());
        Page<RunRecordDetailsVo> reslutPage = new Page<>();
        List<RealPersonRunDataDetails> listByTime = detailsPage.getRecords();
        if (CollectionUtils.isEmpty(listByTime)) {
            return reslutPage;
        }
        List<RunRecordDetailsVo> collect = listByTime.stream().map(s -> {
            RunRecordDetailsVo runRecordDetailsVo = new RunRecordDetailsVo();
            runRecordDetailsVo.setDetailsId(s.getRunDataDetailsId());
            runRecordDetailsVo.setTitle(fillTitle(s, user));
            runRecordDetailsVo.setRunDate(s.getCreateTime());
            runRecordDetailsVo.setTotalTime(s.getRunTime());
            runRecordDetailsVo.setKilocalorie(s.getKilocalorie());
            runRecordDetailsVo.setRunMileage(s.getRunMileage());
            //设置虚拟设备
            runRecordDetailsVo.setVirtual(sysConfigService.isVirtualRecord(s.getRunDataDetailsId()) ? 1 : 0);
            return runRecordDetailsVo;
        }).collect(Collectors.toList());
        reslutPage.setRecords(collect);
        reslutPage.setTotal(detailsPage.getTotal());
        reslutPage.setCurrent(detailsPage.getCurrent());
        reslutPage.setSize(detailsPage.getSize());
        return reslutPage;
    }


    private Map<String, Object> getDetailMap(RealPersonRunDataDetails realPersonRunDataDetails, ZnsUserEntity loginUser, String zoneId, ZnsTreadmillEntity treadmill) {
        Map<String, Object> map = new HashMap<>();
        map.put("dataType", 2);
        map.put("dataSource", realPersonRunDataDetails.getDataSource());
        map.put("title", fillTitle(realPersonRunDataDetails, loginUser));
        map.put("totalTime", realPersonRunDataDetails.getRunTime());
        map.put("runMileage", realPersonRunDataDetails.getRunMileage());
        map.put("kilocalorie", realPersonRunDataDetails.getKilocalorie());
        map.put("runDate", DateUtil.convertTimeZone(realPersonRunDataDetails.getCreateTime(), "UTC", zoneId));
        map.put("detailId", realPersonRunDataDetails.getRunDataDetailsId());
        map.put("activityType", Objects.equals(realPersonRunDataDetails.getUnActivityType(), 0) ? realPersonRunDataDetails.getActivityType() : realPersonRunDataDetails.getUnActivityType());
        map.put("virtual", sysConfigService.isVirtualEquipment(realPersonRunDataDetails.getTreadmillId()) ? 1 : 0);
        if (Objects.nonNull(treadmill)) {
            map.put("equipmentMainType", treadmill.getEquipmentMainType());
        }
        map.put("virtual", sysConfigService.isVirtualEquipment(realPersonRunDataDetails.getTreadmillId()) ? 1 : 0);
        if (Objects.nonNull(realPersonRunDataDetails.getCourseId()) && realPersonRunDataDetails.getCourseId() > 0) {
            ZnsCourseEntity courseEntity = znsCourseService.selectCourseById(realPersonRunDataDetails.getCourseId());
            if (Objects.nonNull(courseEntity)) {
                map.put("courseType", courseEntity.getCourseType());
            }
        }
        if (Objects.nonNull(treadmill) && restrictedCodes.contains(treadmill.getProductCode())) {
            map.put("showCalorie", 0);
        } else {
            map.put("showCalorie", 1);
        }
        return map;
    }

    //查询时间转时区
    private void fixFindTime(RunDataStatisticsRequest request, String zoneId) {
        StatisticsTypeEnum statisticsTypeEnum = StatisticsTypeEnum.findTypeByCode(request.getStatisticsType());
        switch (statisticsTypeEnum) {
            case DAY -> {
                request.setStartTime(DateUtil.getDayStartByZone(request.getStartTime(), zoneId));
                request.setEndTime(DateUtil.getDayEndByZone(request.getStartTime(), zoneId));
            }
            case WEEK -> {
                request.setStartTime(request.getStartTime());
                request.setEndTime(request.getEndTime());
            }
            case MONTH -> {
                request.setStartTime(DateUtil.getMonthStartByZone(request.getYear(), request.getMonth(), zoneId));
                request.setEndTime(DateUtil.getMonthEndByZone(request.getYear(), request.getMonth(), zoneId));
            }
            case YEAR -> {
                request.setStartTime(DateUtil.getYearStartByZone(request.getYear(), zoneId));
                request.setEndTime(DateUtil.getYearEndByZone(request.getYear(), zoneId));
            }
        }
    }

    private String fillTitle(RealPersonRunDataDetails realPersonRunDataDetails, ZnsUserEntity user) {
        String title = null;
        //自由跑或目标跑
        MainActivity mainActivity = mainActivityService.findById(realPersonRunDataDetails.getActivityId());
        if (Objects.equals(realPersonRunDataDetails.getUnActivityType(), -1) || Objects.equals(realPersonRunDataDetails.getUnActivityType(), -3)) {
            //自由跑和目标跑
            title = I18nMsgUtils.getMessage("free.run.title");
        } else if (Objects.equals(realPersonRunDataDetails.getUnActivityType(), -2)) {
            //课程跑
            ZnsCourseEntity i18nCourseById = znsCourseService.getI18nCourseById(realPersonRunDataDetails.getCourseId(), Objects.nonNull(user) ? user.getLanguageCode() : "en_US");
            title = Optional.ofNullable(i18nCourseById)
                    .map(i18n -> i18n.getCourseName())
                    .orElseGet(() -> realPersonRunDataDetails.getRunMileage()
                            .divide(new BigDecimal("1600"), 2, RoundingMode.DOWN) + "miles");

            Long trainingId = realPersonRunDataDetails.getTrainingId();
            String finalTitle = title;
            title = Optional.ofNullable(trainingId)
                    .filter(id -> Objects.nonNull(i18nCourseById) && "Training program".equals(i18nCourseById.getCategoryName()))
                    .map(id -> userTrainingPlanDetailService.findById(id))
                    .map(userTrainingPlanDetail -> userTrainingPlanService.findById(userTrainingPlanDetail.getUserPlanId()))
                    .map(userTrainingPlan -> trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(userTrainingPlan.getPlanId()), user.getLanguageCode()))
                    .filter(trainingPlanInfos -> !CollectionUtils.isEmpty(trainingPlanInfos))
                    .map(trainingPlanInfos -> trainingPlanInfos.get(0).getTitle() + "-" + finalTitle)
                    .orElse(title);
        } else if (Objects.equals(realPersonRunDataDetails.getActivityType(), 2)) {
            //用户赛1v1
            title = "1V1 PK";
        } else if (Objects.equals(realPersonRunDataDetails.getActivityType(), 1)) {
            //非官方多人同跑
            title = I18nMsgUtils.getMessage("Unofficial.multiplayer.run");
        } else if (Objects.equals(realPersonRunDataDetails.getActivityType(), 17)) {
            //新PK活动
            title = mainActivity.getRemark();
        } else if (Objects.equals(realPersonRunDataDetails.getActivityType(), 22)) {
            //free 挑战赛
            title =  I18nMsgUtils.getMessage("free.challenge.title");
            return title;
        } else if (Objects.nonNull(mainActivity) && (!Objects.equals(mainActivity.getMainType(), "old") && !Objects.equals(mainActivity.getMainType(), "rank"))){
            //新官方赛
            ActivityDisseminate language = activityDisseminateBizService.findByActivityIdAndLanguage(realPersonRunDataDetails.getActivityId(), Objects.nonNull(user) ? user.getLanguageCode() : "en_US");
            title = Objects.nonNull(language) ? language.getTitle() : realPersonRunDataDetails.getRunMileage().divide(new BigDecimal("1600"), 2, RoundingMode.DOWN) + "miles";
        } else {
            //老活动记录
            title = realPersonRunDataDetails.getRunMileage().divide(new BigDecimal("1600"), 2, RoundingMode.DOWN) + "miles";
        }

        //新活动处理
        if (RunActivityTypeEnum.hasNewUserActivity(realPersonRunDataDetails.getActivityType())) {
            if (RunActivityTypeEnum.isNewUserActivity(realPersonRunDataDetails.getActivityType())) {
                title = I18nMsgUtils.getMessage("activity.title.AccountDetailTypeEnum.NEW_USER_FIRST_ACTIVITY");
            } else {
                ZnsRunActivityEntity runActivity = runActivityService.findById(realPersonRunDataDetails.getActivityId());
                if (Objects.nonNull(runActivity) && RunActivityTypeEnum.isNewUserActivity(runActivity.getActivityType(), runActivity.getActivityTypeSub())) {
                    title = I18nMsgUtils.getMessage("activity.title.AccountDetailTypeEnum.NEW_USER_FIRST_ACTIVITY");
                }
            }
        }
        return title;
    }

    public ActivityDataDetailsResponseDto getActivityDataDetailsList(ActivityDataDetailsListRequestDto requestDto, ZnsUserEntity loginUser) {
        if (NumberUtils.geZero(requestDto.getDetailId())) {
            ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.findById(requestDto.getDetailId());
            requestDto.setActivityId(details.getActivityId());
        }
        ActivityDataDetailsResponseDto responseDto = new ActivityDataDetailsResponseDto();
        MainActivity activity = mainActivityService.findById(requestDto.getActivityId());
        List<Long> activityIdList;
        //是否俱乐部用户赛
        Boolean isClubActivity = false;
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(activity.getMainType())) {
            activityIdList = seriesActivityRelService.findSubActivityId(activity.getId());
        } else {
            List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(activity.getId()).build());
            if (CollectionUtils.isEmpty(list)) {
                activityIdList = Lists.newArrayList(activity.getId());
            } else {
                activityIdList = list.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
                isClubActivity = true;
            }
        }

        UserRunDataDetailsQuery userRunDataDetailsQuery = new UserRunDataDetailsQuery().setActivityIds(activityIdList).setUserId(loginUser.getId()).setRunStatus(1);
        userRunDataDetailsQuery.setPageNum(requestDto.getPageNum());
        userRunDataDetailsQuery.setPageSize(requestDto.getPageSize());
        userRunDataDetailsQuery.setOrders(Lists.newArrayList(OrderItem.desc("create_time")));

        Page<ZnsUserRunDataDetailsEntity> page = userRunDataDetailsService.findPage(userRunDataDetailsQuery);
        responseDto.setTotal(page.getTotal()).setCurrent(page.getCurrent()).setSize(page.getSize()).setPages(page.getPages());
        List<ZnsUserRunDataDetailsEntity> list = page.getRecords();

        if (activity.getActivityState() == 2 && (activity.getAwardSendStatus() == 2 || activity.getAwardSendStatus() == 0)) {
            responseDto.setReviewState(1);
        }
        List<ActivityDataDetailsListResponseDto> dtoList = new ArrayList<>();
        for (ZnsUserRunDataDetailsEntity item : list) {
            ActivityDataDetailsListResponseDto activityDataDetailsListResponseDto = realUserRunDataDetailsConverter.toDto(item);
            //查询成绩反馈
            UserFeedbackDataDo userFeedbackDataDo = userFeedbackDataService.findByQuery(new UserFeedbackDataQuery().setDetailId(item.getId()));
            //新状态设置
            if (Objects.nonNull(userFeedbackDataDo)) {
                activityDataDetailsListResponseDto.setAppealState(userFeedbackDataDo.getStatus());
            }

            Integer isComplete = runDataBizService.getComplete(item, activity, isClubActivity);

            if (EquipmentDeviceTypeEnum.ROWING.getCode().equals(item.getDeviceType())) {
                activityDataDetailsListResponseDto.setIsForceMetric(1);
            }
            Integer resultDataState = ResultDataStateEnum.stateConvert(isComplete, item.getIsCheat(), item.getRunStatus(), activityDataDetailsListResponseDto.getAppealState());
            activityDataDetailsListResponseDto.setResultDataState(resultDataState);

            if (Objects.equals(requestDto.getDataType(), 1)) {
                if (ResultDataStateEnum.abnormalState().contains(resultDataState) && Objects.isNull(activityDataDetailsListResponseDto.getAppealState())) {
                    dtoList.add(activityDataDetailsListResponseDto);
                }
                continue;
            }
            //查询是否有风控检测中
            activityDataDetailsListResponseDto.setIsRiskReview(userRunDataDetailsCheatService.isDetailsRiskReview(item.getId()));
            dtoList.add(activityDataDetailsListResponseDto);
        }
        //查询是否有风控检测中
        Integer riskReview = userRunDataDetailsCheatService.isRiskReview(activityIdList);
        responseDto.setIsRiskReview(riskReview);
        responseDto.setList(dtoList);

        responseDto.setHasFeedbackEntrance(getHasFeedbackEntrance(activity, activityIdList, loginUser.getId()));
        return responseDto;
    }

    private Boolean getHasFeedbackEntrance(MainActivity activity, List<Long> activityIdList, Long userId) {
        if (!StringUtils.hasText(activity.getActivityEndTime())) {
            log.info("活动不是结束，不能申诉");
            return false;
        }
        ZonedDateTime startTime = DateTimeUtil.parse(activity.getActivityEndTime());
        if (MainActivityTypeEnum.OLD.getType().equals(activity.getMainType())) {
            ZnsRunActivityEntity runActivity = runActivityService.findById(activity.getId());
            if (Objects.nonNull(runActivity)) {
                startTime = runActivity.getActivityEndTime();
            }
        }
        if (Objects.isNull(startTime)) {
            log.info("活动不是结束，不能申诉");
            return false;
        }
        if (DateUtil.betweenDay(startTime, ZonedDateTime.now()) > 15) {
            log.info("活动结束时间超过15天，不能申诉");
            return false;
        }
        if (!ActivityStateEnum.FINISHED.getState().equals(activity.getActivityState())) {
            log.info("活动不是结束，不能申诉");
            return false;
        }
        if (!activity.isNotReview()) {
            log.info("活动申诉状态，不能申诉");
            return false;
        }
        return userRunDataDetailsService.findWaitFeedbackCount(activityIdList, userId) > 0;
    }
}
