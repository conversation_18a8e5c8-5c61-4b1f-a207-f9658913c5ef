package com.linzi.pitpat.data.activityservice.manager.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.api.enums.EquipmentTypeEnum;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityBrandInterestsBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityPolymerizationBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserAwardBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityVerificationBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.PayActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.ProActivityRedisEnhanceService;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.CompetitiveShortlistTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.marathon.MarathonInviteeTypeStatus;
import com.linzi.pitpat.data.activityservice.constant.enums.pro.ProActivityType;
import com.linzi.pitpat.data.activityservice.dto.ActivityStageUserInfo;
import com.linzi.pitpat.data.activityservice.dto.VideoViewDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.ActivityRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.MarathonTeamCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.EnrollActivityRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.ReportUsersRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SeriesActivityRankRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SingleActivityIdRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.EnrollActivityResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.SignupFirstCheckResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityAwardReviewDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityBaseInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityRuleDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivitySeriesLevelInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityStageApiDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityVideoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ApiActivityEquipmentInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CardUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CompetitionScheduleDetailDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CompetitionScheduleDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.EquipmentCategoryDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.EquipmentInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.GradeViewDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.HistoryRewardDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.LightCityPicsDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MarathonConfigDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MarathonTeamConfigDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MarathonTeamUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyActRecordDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyMarathonTeamConfigDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyRaceDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyTeamActRecordDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ProActivityInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.RankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.RateLimitDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ReportInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ReportUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityRankResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesAwardRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SingleActivityDetailDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SingleStageRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SpeedRateLimitDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SurpassDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.TeamPersonalRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.TeamPersonalRankResultDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.TeamUserGradeDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.marathon.MarathonTeamJoinPopDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ApplicationRewardDto;
import com.linzi.pitpat.data.activityservice.enums.ActivityViewStatusEnum;
import com.linzi.pitpat.data.activityservice.enums.ProActivityCardTypeEnum;
import com.linzi.pitpat.data.activityservice.manager.ActivityStageBusiness;
import com.linzi.pitpat.data.activityservice.manager.ActivityTeamManager;
import com.linzi.pitpat.data.activityservice.manager.ActivityVideoViewManger;
import com.linzi.pitpat.data.activityservice.manager.AppProActivityCardManager;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.mapper.MilestonePopDao;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityTeamJoinSettingDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityWatchUserNumRespDto;
import com.linzi.pitpat.data.activityservice.model.dto.AddMainActivityUserDto;
import com.linzi.pitpat.data.activityservice.model.dto.AppActivityRateLimitDto;
import com.linzi.pitpat.data.activityservice.model.dto.PayActivityDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveListDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityArea;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEnterThreshold;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityImpracticalAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRateLimit;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveShortlistDo;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.Gameplay;
import com.linzi.pitpat.data.activityservice.model.entity.InvitationNewUserPopDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.MarathonInvitationRecordsDo;
import com.linzi.pitpat.data.activityservice.model.entity.PlacementLevelConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityCardDo;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityCardRecordDo;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityStageUser;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.TeamApplyDo;
import com.linzi.pitpat.data.activityservice.model.entity.TeamCallRecord;
import com.linzi.pitpat.data.activityservice.model.entity.TeamEffectiveGrade;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.enums.TeamApplyStatusEnum;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRateLimitQuery;
import com.linzi.pitpat.data.activityservice.model.query.ActivityTeamQuery;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveShortlistQuery;
import com.linzi.pitpat.data.activityservice.model.query.InvitationNewUserPopQuery;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.MarathonInvitationRecordsQuery;
import com.linzi.pitpat.data.activityservice.model.query.ProActivityCardRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardTargetConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiActivityAwardListDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiAmountAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQueryUser;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityAwardPopRespDto;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityWatchInfoRespDto;
import com.linzi.pitpat.data.activityservice.model.resp.AwardPopDto;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityUserAwardVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MaxAwardVo;
import com.linzi.pitpat.data.activityservice.query.SeriesActivityRelQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAreaService;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityEnterThresholdService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityImpracticalAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsLoaderService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveScoreConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveShortlistService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayService;
import com.linzi.pitpat.data.activityservice.service.InvitationNewUserPopService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.MarathonInvitationRecordsService;
import com.linzi.pitpat.data.activityservice.service.PlacementLevelConfigService;
import com.linzi.pitpat.data.activityservice.service.ProActivityCardRecordService;
import com.linzi.pitpat.data.activityservice.service.RunActivityStageUserService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserExtraService;
import com.linzi.pitpat.data.activityservice.service.SeasonBonusPoolService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SeriesGameplayService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.TeamApplyService;
import com.linzi.pitpat.data.activityservice.service.TeamCallRecordService;
import com.linzi.pitpat.data.activityservice.service.TeamEffectiveGradeService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.clubservice.enums.CompetitiveActivityClubEnum;
import com.linzi.pitpat.data.clubservice.manager.ClubActivityManager;
import com.linzi.pitpat.data.clubservice.manager.ClubManager;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMember;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberRoleEnum;
import com.linzi.pitpat.data.userservice.enums.UserExtraParamsKeyEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraParamsDo;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelDo;
import com.linzi.pitpat.data.userservice.model.query.UserExtraParamsQuery;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.FetchRuleTypeEnum;
import com.linzi.pitpat.data.constants.RankingByEnum;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.activity.MilestonePop;
import com.linzi.pitpat.data.entity.award.ActivityUserAwardPre;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.entity.vo.SocketRoomUserVo;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityFeeTypeEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsEquipmentProductionBatchEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentVersionService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mallservice.biz.MallCategoryEquiModelBizService;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderModelCountDto;
import com.linzi.pitpat.data.mallservice.enums.OrderRefundConstant;
import com.linzi.pitpat.data.mallservice.manager.api.GoodsManager;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderRefundQuery;
import com.linzi.pitpat.data.mallservice.service.OrderRefundService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.mallservice.service.impl.ZnsOrderServiceImpl;
import com.linzi.pitpat.data.model.sys.ActivityWatchUserModel;
import com.linzi.pitpat.data.request.CommonShareDto;
import com.linzi.pitpat.data.resp.CommonShareRespDto;
import com.linzi.pitpat.data.service.award.ActivityUserAwardPreService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.enums.RegionConstants;
import com.linzi.pitpat.data.systemservice.model.entity.AreaI18nEntity;
import com.linzi.pitpat.data.systemservice.model.entity.CountryI18nEntity;
import com.linzi.pitpat.data.systemservice.model.query.CountryI18nQuery;
import com.linzi.pitpat.data.systemservice.service.AreaI18nService;
import com.linzi.pitpat.data.systemservice.service.CountryI18nService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.biz.TrafficInvestmentUserAwardChecker;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelQuery;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.UserExtraParamsService;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.RunRouteVO;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.entity.MockMultipartFile;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.linzi.pitpat.data.activityservice.service.ProActivityService;
import com.linzi.pitpat.data.activityservice.service.ProActivityTimelineService;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityDo;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityTimelineDo;

@Component
@RequiredArgsConstructor
@Slf4j
public class AppActivityManager {
    private final MarathonJoinReviewBizService marathonJoinReviewManager;
    private final RunActivityUserExtraService runActivityUserExtraService;
    private final SeasonBonusPoolService seasonBonusPoolService;
    private final ActivityUserAwardPreService activityUserAwardPreService;
    private final ActivityAreaService activityAreaService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final ActivityRateLimitService activityRateLimitService;
    private final ActivityFeeService activityFeeService;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final ActivityTeamService activityTeamService;
    private final ZnsUserAccountService accountService;
    private final ActivityEquipmentConfigService equipmentConfigService;
    private final ZnsRunRouteService runRouteService;
    private final AreaI18nService areaI18nService;
    private final CountryI18nService countryI18nService;
    private final ZnsRunActivityUserService activityUserService;
    private final ZnsUserService userService;
    private final ZnsUserFriendService friendService;
    private final ZnsUserAccountDetailService accountDetailService;
    private final ZnsUserRunDataDetailsService runDataDetailsService;
    private final EntryGameplayService entryGameplayService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ISysConfigService sysConfigService;
    private final UserCouponService userCouponService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final ActivityEnterThresholdService activityEnterThresholdService;
    private final ActivityBrandRightsInterestsService brandRightsInterestsService;
    private final ActivityBrandInterestsBizService activityBrandInterestsBizService;
    private final ActivityUserScoreService activityUserScoreService;
    private final TeamEffectiveGradeService teamEffectiveGradeService;
    private final TeamCallRecordService teamCallRecordService;
    private final ActivityImpracticalAwardConfigService impracticalAwardConfigService;
    private final UserWearsBagService userWearsBagService;
    private final ActivityUserAwardBizService activityUserAwardBizService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;
    private final ActivityPolymerizationBizService activityPolymerizationBizService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ZnsUserAccountService userAccountService;
    private final AwardActivityManager awardActivityManager;
    private final ApiCompetitiveActivityManager apiCompetitiveActivityManager;
    private final AwardActivityBizService awardActivityBizService;
    private final MilestonePopDao milestonePopDao;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RoomIdBizService roomIdBizService;
    private final CouponService couponService;
    private final CouponI18nService couponI18nService;
    private final CouponCurrencyService couponCurrencyService;
    private final RedissonClient redissonClient;
    private final GameplayService gameplayService;
    private final SeriesGameplayService seriesGameplayService;
    private final ActivityParamsLoaderService activityParamsLoaderService;
    private final ClubActivityTeamService clubActivityTeamService;
    private final ClubActivityManager clubActivityManager;
    private final ActivityVerificationBizService activityVerificationBizService;
    private final SocketPushUtils socketPushUtils;
    private final MainActivityBizService mainActivityBizService;
    private final PayActivityBizService payActivityBizService;
    private final ActivityUserBizService activityUserBizService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final CompetitiveScoreConfigService competitiveScoreConfigService;
    private final ActivityParamsService activityParamsService;
    private final ZnsTreadmillService treadmillService;
    private final ZnsEquipmentProductionBatchService znsEquipmentProductionBatchService;
    private final EquipmentVersionService equipmentVersionservice;
    private final ActivityStageService activityStageService;
    private final ActivityStageBusiness activityStageBusiness;
    private final RunActivityStageUserService runActivityStageUserService;
    private final ActivityTeamManager activityTeamManager;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final CompetitiveSeasonService competitiveSeasonService;
    private final CompetitiveShortlistService competitiveShortlistService;
    private final UserTaskBizService userTaskBizService;
    private final MallCategoryEquiModelBizService mallCategoryEquiModelBizService;
    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final GoodsManager goodsManager;
    private final ActivityTeamService teamService;
    private final TeamApplyService teamApplyService;
    private final MarathonInvitationRecordsService marathonInvitationRecordsService;
    private final InvitationNewUserPopService invitationNewUserPopService;
    private final ZnsUserAccountDetailService znsUserAccountDetailService;
    private final TrafficInvestmentUserAwardChecker trafficInvestmentUserAwardChecker;
    private final ActivityVideoViewManger activityVideoViewManger;
    private final ProActivityService proActivityService;
    private final ProActivityTimelineService proActivityTimelineService;
    private final AppProActivityCardManager appProActivityCardManager;
    private final ProActivityCardRecordService proActivityCardRecordService;

    private final ClubService clubService;
    private final ClubMemberService clubMemberService;
    private final UserExtraParamsService userExtraParamsService;
    private final TencentImUtil tencentImUtil;
    private final ClubManager clubManager;
    private final PlacementLevelConfigService placementLevelConfigService;
    private final UserPlacementLevelService userPlacementLevelService;


    @Value("${spring.profiles.active}")
    private String envProfile;

    private final OrderRefundService orderRefundService;

    private final static Long DAY_MILS = 24 * 60 * 60 * 1000l;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ZnsOrderService znsOrderService;
    @Autowired
    private ProActivityRedisEnhanceService proActivityRedisEnhanceService;
    //@Autowired
    //private RedisUtil redisUtil;
    private final QueueMessageService queueMessageService;
    /**
     * 单赛事详情
     *
     * @param mainActivityId
     * @param user
     * @param isPolyList
     * @param appVersion
     * @return
     */
    @FillerMethod
    public SingleActivityDetailDto singleActivityDetail(Long mainActivityId, ZnsUserEntity user, Integer isPolyList, Integer appVersion) {
        log.info("ActivityDetail come");

        String languageCode = user.getLanguageCode(); // 本来应该传系统语言，但是由于H5只传了固定语言，所以这里只能用用户语言了
        //聚合活动处理
        mainActivityId = getPolymerizationRunningId(mainActivityId, isPolyList);

        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        SingleActivityDetailDto detailDto = new SingleActivityDetailDto();
        String currencyCode = "USD";
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
        if (userAccount != null) {
            currencyCode = userAccount.getCurrencyCode();
        }
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());

        //报名相关信息
        ReportInfoDto reportInfoDto = generateReportInfoDto(mainActivity, user, isPolyList, languageCode, appVersion);
        //活动规则
        ActivityRuleDto activityRuleDto = generateActivityRuleDto(mainActivity, user, subActivity, isPolyList, languageCode);
        //赛事限速规则
        List<SpeedRateLimitDto> speedRateLimitDtos = generateSpeedRateLimitDtos(mainActivity, user);
        //赛事基础信息
        ActivityBaseInfoDto activityBaseInfoDto = generateActivityBaseInfoDto(mainActivity, user, reportInfoDto, currencyCode, subActivity, isPolyList, languageCode);
        //系列赛事信息
        SeriesActivityInfoDto seriesActivityInfoDto = generateSeriesActivityInfoDto(mainActivity, user, languageCode);

        detailDto.setReportInfoDto(reportInfoDto);
        detailDto.setActivityRuleDto(activityRuleDto);
        detailDto.setSpeedRateLimitDto(speedRateLimitDtos);
        detailDto.setBaseInfoDto(activityBaseInfoDto);
        detailDto.setSeriesActivityInfoDto(seriesActivityInfoDto);
        detailDto.setIsBatchSignUpActivity(apiCompetitiveActivityManager.canBatchSignUp(user, user.getZoneId(), false, mainActivity.getEquipmentMainType(), mainActivityId));

        //马拉松额外信息扩展
        extendMarathon(mainActivity, detailDto, user);

        activityParamsService.findOneByMainActivityAndParamType(mainActivityId, ActivitySettingConfigEnum.VIDEO_VIEW_CONFIG)
                .ifPresent(k -> {
                    VideoViewDto videoViewDto = JsonUtil.readValue(k.getParamValue(), VideoViewDto.class);
                    if (videoViewDto != null) {
                        detailDto.setAllowVideoPrivacy(videoViewDto.getEnable());
                        GradeViewDto gradeViewDto = new GradeViewDto();
                        gradeViewDto.setUploadStartTime(videoViewDto.getStartTime());
                        gradeViewDto.setUploadEndTime(videoViewDto.getEndTime());
                        List<ActivityVideoDto> myVideoList = activityVideoViewManger.getMyVideoList(mainActivity.getId(), user);
                        gradeViewDto.setWaitUploadCount(myVideoList.stream().filter(v -> ActivityViewStatusEnum.TOBE_UPLOAD.getCode().equals(v.getViewStatus())).count());
                        gradeViewDto.setInPassingCount(myVideoList.stream().filter(v -> ActivityViewStatusEnum.WAIT.getCode().equals(v.getViewStatus())).count());
                        gradeViewDto.setRefuseCount(myVideoList.stream().filter(v -> ActivityViewStatusEnum.REFUSE.getCode().equals(v.getViewStatus())).count());
                        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivity.getId(), user.getId());
                        if (activityUser != null && activityUser.getIsComplete() == 1) {
                            gradeViewDto.setHasParticipated(1);
                        } else {
                            gradeViewDto.setHasParticipated(0);
                        }
                        detailDto.setGradeViewDto(gradeViewDto);
                    }
                });
        //是否开始活动推送
        detailDto.setNeedAllowPush(activityParamsService.allowUserRankChangePush(mainActivity.getId()) ? 1 : 0);

        //职业赛信息
        detailDto.setProActivityInfoDto(generateProActivityInfoDto(mainActivity, languageCode, user));

        return detailDto;
    }

    private List<CardUserDto> generateCardUserDto(Long activityId) {
        List<ProActivityCardRecordDo> list = proActivityCardRecordService.findList(new ProActivityCardRecordQuery().setActivityId(activityId));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ZnsUserEntity> users = userService.findByIds(list.stream().map(ProActivityCardRecordDo::getUserId).collect(Collectors.toList()));
        return users.stream().map(k -> {
            CardUserDto cardUserDto = new CardUserDto();
            cardUserDto.setUserId(k.getId());
            cardUserDto.setUserName(k.getFirstName());
            cardUserDto.setHeadPic(k.getHeadPortrait());
            return cardUserDto;
        }).collect(Collectors.toList());

    }

    private ProActivityInfoDto generateProActivityInfoDto(MainActivity mainActivity, String languageCode, ZnsUserEntity user) {
        log.info("开始生成职业赛信息，活动ID: {}", mainActivity.getId());

        ProActivityInfoDto proActivityInfoDto = new ProActivityInfoDto();

        // 查询职业赛配置信息
        Optional<ProActivityDo> proActivityOpt = proActivityService.findByMainActivityId(mainActivity.getId());
        if (proActivityOpt.isEmpty()) {
            log.info("活动 {} 不是职业赛，返回空信息", mainActivity.getId());
            return proActivityInfoDto;
        }

        ProActivityDo proActivity = proActivityOpt.get();
        log.info("查询到职业赛配置，类型: {}, 主活动ID: {}", proActivity.getProActivityType(), proActivity.getMainActivityId());

        // 设置职业赛类型
        proActivityInfoDto.setProActivityType(proActivity.getProActivityType());


        Integer maleSortOrder = Integer.MAX_VALUE;
        Integer femaleSortOrder = Integer.MAX_VALUE;
        // 设置等级要求
        if (StringUtils.hasText(proActivity.getMalePlacementLevelCode())) {
            PlacementLevelConfigDo maleLevelConfig = placementLevelConfigService.findByCode(proActivity.getMalePlacementLevelCode());
            proActivityInfoDto.setMalePlacementLevelCode(maleLevelConfig.getLevelName());
            maleSortOrder = maleLevelConfig.getSortOrder();
        }
        if (StringUtils.hasText(proActivity.getFemalePlacementLevelCode())) {
            PlacementLevelConfigDo femaleLevelConfig = placementLevelConfigService.findByCode(proActivity.getFemalePlacementLevelCode());
            proActivityInfoDto.setFemalePlacementLevelCode(femaleLevelConfig.getLevelName());
            femaleSortOrder = femaleLevelConfig.getSortOrder();
        }
        UserPlacementLevelDo userPlacementLevelDo = userPlacementLevelService.findByQuery(new UserPlacementLevelQuery().setUserId(user.getId()));
        if (userPlacementLevelDo != null) {
            PlacementLevelConfigDo code = placementLevelConfigService.findByCode(userPlacementLevelDo.getLevelCode());
            Integer gender = user.getGender();
            Integer userSort = code.getSortOrder();
            Integer targetSort = gender == 2 ? femaleSortOrder : maleSortOrder;
            if (userSort > targetSort) {
                proActivityInfoDto.setUseReachedLevel(0);
            }
        } else {
            if(user.getGender() == 1 && maleSortOrder < Integer.MAX_VALUE){
                proActivityInfoDto.setUseReachedLevel(0);

            }else if(user.getGender() == 2 && femaleSortOrder < Integer.MAX_VALUE){
                proActivityInfoDto.setUseReachedLevel(0);

            }
        }
        // 设置特权相关
        proActivityInfoDto.setIsOfferProActivityCardPrivilege(proActivity.getIsOfferProActivityCardPrivilege());
        proActivityInfoDto.setIsSendProActivityCard(proActivity.getIsSendProActivityCard());

        ProActivityTimelineDo timeline = proActivityTimelineService.findYearByActivityStartTime(mainActivity.getActivityStartTime());


        if (timeline != null) {
            log.info("查询到赛期信息，ID: {}, 年份: {}, 类型: {}",
                    timeline.getId(), timeline.getYear(), timeline.getTimelineType());

            // 设置赛年信息
            proActivityInfoDto.setYear(timeline.getYear());
            proActivityInfoDto.setYearStartTime(timeline.getStartTime());
            proActivityInfoDto.setYearEndTime(timeline.getEndTime());
            Optional<ProActivityCardDo> canUseCard = appProActivityCardManager.getCanUseCard(user.getId(), proActivity.getProActivityType(), ProActivityCardTypeEnum.QUALIFICATION, timeline.getYear());
            canUseCard.ifPresent(card -> {
                proActivityInfoDto.setQualificationCardTitle(card.getTitleByLanguageCode(languageCode));
                proActivityInfoDto.setIsQualificationCardSignUp(1);
            });
            Optional<ProActivityCardDo> canUseDirectCard = appProActivityCardManager.getCanUseCard(user.getId(), proActivity.getProActivityType(), ProActivityCardTypeEnum.DIRECT_PASS, timeline.getYear());
            canUseDirectCard.ifPresent(card -> {
                proActivityInfoDto.setDirectCardTitle(card.getTitleByLanguageCode(languageCode));
                proActivityInfoDto.setIsDirectCardSignUp(1);
            });
            Optional<ProActivityCardDo> currentCard = appProActivityCardManager.getCurrentCard(proActivity.getProActivityType(), timeline.getYear(), ProActivityCardTypeEnum.QUALIFICATION);
            currentCard.ifPresent(card -> {
                proActivityInfoDto.setSendQualificationCardRemark(card.getRemarkByLanguageCode(languageCode));
            });
        } else {
            log.warn("未找到对应的赛期信息，职业赛类型: {}",
                    proActivity.getProActivityType());
        }

        log.info("职业赛信息生成完成，活动ID: {}", mainActivity.getId());
        return proActivityInfoDto;
    }

    private void extendMarathon(MainActivity mainActivity, SingleActivityDetailDto detailDto, ZnsUserEntity user) {
        Long activityId = mainActivity.getId();
        Boolean marathon = sysConfigService.isMarathon(activityId);
        if (!marathon) {
            return;
        }
        detailDto.setIsMarathonActivity(1);
        //马拉松队伍配置
        detailDto.setMarathonConfig(buildMarathonConfig(mainActivity, user));
        //审核通过的弹窗
        List<TeamApplyDo> teamApplyDos = teamApplyService.popApplyResult(activityId, user);
        if (!CollectionUtils.isEmpty(teamApplyDos)) {
            MarathonTeamJoinPopDto joinPopDto = new MarathonTeamJoinPopDto();
            joinPopDto.setShow(true);
            List<Long> teamIds = teamApplyDos.stream().map(TeamApplyDo::getTeamId).distinct().toList();
            ActivityTeamQuery query = ActivityTeamQuery.builder().ids(teamIds).build();
            List<ActivityTeam> teams = activityTeamService.findList(query);
            teamApplyDos.stream().filter(a -> TeamApplyStatusEnum.APPLY.getCode().equals(a.getStatus())).findFirst().ifPresent(b -> {
                joinPopDto.setApplyTeamName(teams.stream().filter(c -> c.getId().equals(b.getTeamId())).findFirst().orElse(new ActivityTeam()).getTeamName());
            });
            List<Long> refundIds = teamApplyDos.stream().filter(a -> TeamApplyStatusEnum.REFUSE.getCode().equals(a.getStatus())).map(TeamApplyDo::getTeamId).distinct().toList();
            if (!CollectionUtils.isEmpty(refundIds)) {
                List<String> rejectTeamName = teams.stream().filter(a -> refundIds.contains(a.getId())).distinct().map(b -> b.getTeamName()).toList();
                joinPopDto.setRejectTeamName(rejectTeamName);
            }
            detailDto.setMarathonTeamJoinPopDto(joinPopDto);
        }

    }

    private MarathonConfigDto buildMarathonConfig(MainActivity mainActivity, ZnsUserEntity currentUser) {
        Long activityId = mainActivity.getId();
        MarathonConfigDto marathonConfigDto = new MarathonConfigDto();

        List<ActivityTeam> teams = teamService.getTeamsByActivityId(activityId);
        List<MarathonTeamConfigDto> dtoList = teams.stream().map(t -> {
            MarathonTeamConfigDto teamConfigDto = new MarathonTeamConfigDto();
            teamConfigDto.setTeamId(t.getId());
            teamConfigDto.setTeamLogo(t.getTeamLogo());
            teamConfigDto.setTeamManagerId(t.getTeamManagerId());
            teamConfigDto.setTeamName(t.getTeamName());
            teamConfigDto.setMaxTeamNum(t.getMaxNum());
            teamConfigDto.setTeamMileage(t.getMillage());
            teamConfigDto.setGmtCreate(t.getGmtCreate());
            List<ZnsRunActivityUserEntity> activityUserEntities = activityUserService.getMemberGradeByTeamId(t.getId(), activityId);
            List<MarathonTeamUserDto> userDtos = activityUserEntities.stream().map(a -> {
                        ZnsUserEntity user = userService.findByIdWithoutLogicDelete(a.getUserId());
                        MarathonTeamUserDto userDto = new MarathonTeamUserDto();
                        userDto.setUserId(a.getUserId());
                        userDto.setUsername(user.getFirstName());
                        userDto.setAvatar(user.getHeadPortrait());
                        userDto.setRunMileage(a.getRunMileage().intValue());
                        userDto.setRuntimeMils(a.getRunTimeMillisecond());
                        userDto.setTeamName(t.getTeamName());
                        userDto.setIsTeamLeader(t.getTeamManagerId().equals(a.getUserId()) ? 1 : 0);
                        userDto.setCreateTime(a.getCreateTime());
                        return userDto;
                    }).sorted(Comparator.comparing(MarathonTeamUserDto::getIsTeamLeader).reversed().thenComparing(MarathonTeamUserDto::getCreateTime))
                    .collect(Collectors.toList());
            teamConfigDto.setTeamUserDtos(userDtos);
            return teamConfigDto;
        }).sorted(Comparator.comparing(MarathonTeamConfigDto::notFull).reversed().thenComparing(MarathonTeamConfigDto::getGmtCreate)).collect(Collectors.toList());

        Map<Long, Boolean> s1UserMap = queryUserOrderNewTagMap(mainActivity, dtoList);
        dtoList.forEach(a -> {
            a.getTeamUserDtos().forEach(b -> {
                if (s1UserMap.get(b.getUserId()) != null) {
                    b.setIsNew(1);
                }else{
                    b.setIsNew(0);
                }
            });
        });
        marathonConfigDto.setMarathonTeamConfigDtos(dtoList);

//        if ((mainActivity.getActivityState().equals(MainActivityStateEnum.ENDED.getCode())
//                && AwardSendStatusEnum.NO_VIEW_CONFIG.getCode().equals(mainActivity.getAwardSendStatus()))
//                || AwardSendStatusEnum.VIEW_PASS.getCode().equals(mainActivity.getAwardSendStatus())) {
//            List<ActivityTeam> teamList = teamService.findByActId(activityId);
//            Optional<ActivityTeam> first = teamList.stream().filter(t -> t.getRank().equals(1) && t.getCompleteTime() != null).findFirst();
//            Integer target = subActivityService.getAllSingleActByMain(activityId).get(0).getTarget();
//
//            first.ifPresent(activityTeam -> {
////                ActivityTeam team = first.get();
////                Long teamId = team.getId();
////                List<TeamEffectiveGrade> effectiveGradeList = teamEffectiveGradeService.findByTeam(teamId);
////                List<TeamEffectiveGrade> gradeList = effectiveGradeList.stream().filter(k -> k.getRunMileage() >= target / 4)
////                        .sorted(Comparator.comparing(TeamEffectiveGrade::getMvpGrade).reversed()).collect(Collectors.toList());
//
//
////                List<MarathonTeamUserDto> mvpList = buildMvpList(gradeList);
////                marathonConfigDto.setMvpList(mvpList);
//
//            });
//
//        }

        if (currentUser != null && !CollectionUtils.isEmpty(dtoList)) {
            Optional.ofNullable(runActivityUserService.findActivityUser(activityId, currentUser.getId())).ifPresent(a -> {
                //用户已经报名，填充myTeam
                final Long teamId = a.getTeamId();
                dtoList.stream().filter(tt -> tt.getTeamId().equals(teamId)).findFirst().ifPresent(team -> {
                    MyMarathonTeamConfigDto myTeam = MyMarathonTeamConfigDto.from(team);
                    myTeam.setUserId(currentUser.getId());
                    myTeam.setActivityId(activityId);
                    marathonConfigDto.setMyTeam(myTeam);
                    myTeam.setIsTeamLeader(myTeam.getTeamManagerId().equals(currentUser.getId()) ? 1 : 0);
                    if (myTeam.isTeamLeader()) {
                        myTeam.setWaitJudgeReviewCount(teamApplyService.getWaitJudgeReviewCount(teamId));
                        long count = myTeam.getTeamUserDtos().stream().filter(b->b.getIsTeamLeader()==0).filter(u -> u.getIsNew() == 1).count();
                        int[] awardList = new int[]{2,5,8,12};

                        if (count>0 && count <= awardList.length) {
                            myTeam.setNewUserAwardAmount(new BigDecimal(awardList[Math.toIntExact(count-1)]));
                            String msg = I18nMsgUtils.getMessage("marathon.474.new.equipment.user.award.desc",count,"S1");
                            myTeam.setNewEquipmentUserAwardDesc(msg);
                        }else{

                            myTeam.setNewUserAwardAmount(new BigDecimal(0));
                            String msg = I18nMsgUtils.getMessage("marathon.474.new.equipment.user.award.zero.desc","S1");
                            myTeam.setNewEquipmentUserAwardDesc(msg);
                        }
                    }
                });
            });
        }
        return marathonConfigDto;
    }

    @NotNull
    private Map<Long, Boolean> queryUserOrderNewTagMap(MainActivity mainActivity, List<MarathonTeamConfigDto> dtoList) {

        List<Long> userIds  = dtoList.stream().flatMap(a -> a.getTeamUserDtos().stream().map(b -> b.getUserId())).distinct().toList();

        //填充isNew标签。 在活动开始到活动结束 购买s1，且30天，成功购买S1的用户，isNew=1
        ZonedDateTime start = mainActivity.activityStartDateZonedDateTime();
        ZonedDateTime end = mainActivity.activityEndDateZonedDateTime();
        ZonedDateTime afterEnd = end.plusDays(30);
        //查询所有S1,在比赛期间下单，且没有全部退款的
        List<OrderModelCountDto> allOrders = znsOrderService.getOrderByUserIdAndGoodsType(userIds, "S1", start, end);
        if(CollectionUtils.isEmpty(allOrders)){
            return Collections.emptyMap();
        }
        //用户所有购买的商品
        Map<Long,Integer> userEquipmentCount = new HashMap<>();
        allOrders.forEach(a ->{
            Integer orDefault = userEquipmentCount.getOrDefault(a.getUserId(), 0);
            userEquipmentCount.put(a.getUserId(),orDefault+a.getGoodsCount());
        });

        //查询关联的退款单
        List<Long> orderItemIds = allOrders.stream().map(OrderModelCountDto::getOrderItemId).distinct().toList();
        OrderRefundQuery query = new OrderRefundQuery();
        query.setOrderItemIdList(orderItemIds);
        //注意反复申请退款，被拒绝的状态。
        query.setStatusList(List.of(OrderRefundConstant.STATUS_ENUM.REFUND_SUCCESS.getCode()));
        List<OrderRefund> refundList = orderRefundService.findList(query);
        if(!CollectionUtils.isEmpty(refundList)){
            refundList.forEach(a ->{
                Integer i = userEquipmentCount.get(a.getUserId());
                if (i == null || a.getGmtRefund()==null) {
                    return;
                }
                if(a.getGmtRefund().toInstant().toEpochMilli()<(afterEnd.toInstant().toEpochMilli())){
                    i=i-a.getCount();
                    userEquipmentCount.put(a.getUserId(),i);
                }

            });
        }
        Map<Long,Boolean> s1UserMap = new HashMap<>();
        userEquipmentCount.forEach((k,v)->{
            if (v > 0) {
                s1UserMap.put(k,true);
            }
        });


        return s1UserMap;
    }

    public List<MarathonTeamUserDto> buildMvpList(List<TeamEffectiveGrade> gradeList) {
        if (CollectionUtils.isEmpty(gradeList)) {
            return Collections.emptyList();
        }

        List<MarathonTeamUserDto> mvpList = new ArrayList<>();

        // 获取第一名的MVP得分作为基准
        BigDecimal highestScore = gradeList.get(0).getMvpGrade();

        // 遍历找出所有得分相同的用户
        for (TeamEffectiveGrade grade : gradeList) {
            if (grade.getMvpGrade().compareTo(highestScore) == 0) {
                ZnsUserEntity user = userService.findByIdWithoutLogicDelete(grade.getUserId());
                if (user != null) {
                    MarathonTeamUserDto userDto = new MarathonTeamUserDto();
                    userDto.setUserId(grade.getUserId());
                    userDto.setUsername(user.getFirstName());
                    userDto.setAvatar(user.getHeadPortrait());
                    userDto.setRunMileage(grade.getRunMileage());
                    userDto.setRuntimeMils(grade.getRunTime() * 1000);
                    // 获取并设置队伍名称
                    ActivityTeam team = teamService.findById(grade.getTeamId());
                    if (team != null) {
                        userDto.setTeamName(team.getTeamName());
                    }

                    mvpList.add(userDto);
                }
            } else {
                // 一旦遇到分数不同的，就可以退出循环了
                break;
            }
        }

        return mvpList;
    }

    /**
     * 聚合活动id处理
     *
     * @param mainActivityId
     * @param isPolyList
     * @return
     */
    private Long getPolymerizationRunningId(Long mainActivityId, Integer isPolyList) {
        if (Objects.isNull(isPolyList) || isPolyList == 0) {
            return mainActivityId;
        } else if (isPolyList == 1) {
            Long runningActivityId = activityPolymerizationBizService.findRunningActivityIdByParentId(mainActivityId);
            if (Objects.nonNull(runningActivityId)) {
                return runningActivityId;
            }
        } else if (isPolyList == 2) {
            Long runningActivityId = activityPolymerizationRecordService.findRunningActivityId(mainActivityId);
            if (Objects.nonNull(runningActivityId)) {
                return runningActivityId;
            }
        }

        return mainActivityId;
    }

    /**
     * 系列赛事信息
     *
     * @param mainActivity
     * @param user
     * @param languageCode
     * @return
     */
    private SeriesActivityInfoDto generateSeriesActivityInfoDto(MainActivity mainActivity, ZnsUserEntity user, String languageCode) {
        if (!MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            return null;

        }
        SeriesActivityInfoDto seriesActivityInfoDto = new SeriesActivityInfoDto();

        //包装阶段活动
        List<MainActivity> segmentMainActs = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
        List<ActivitySeriesLevelInfoDto> levelInfoDtos = segmentMainActs.stream().map(k -> convertToLevelInfo(k, user)).toList();
        seriesActivityInfoDto.setLevelInfos(levelInfoDtos);

        //查找用户关卡信息
        if (user.getId() != null) {
            Integer completeLevel = 0;
            for (MainActivity segmentMainAct : segmentMainActs) {
                ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(segmentMainAct.getId(), user.getId());
                if (activityUser != null) {
                    completeLevel++;
                } else {
                    break;
                }
            }
            seriesActivityInfoDto.setCompleteLevel(completeLevel);
        }

        //查找页面配置
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
        seriesActivityInfoDto.setPageSettings(disseminate.getPageSettings());

        //关门时间
        if (mainActivity.getCloseDoor() != null) {
            seriesActivityInfoDto.setCloseDoorSeconds(mainActivity.getCloseDoor() * 60);
        }
        //用户第一次参赛时间
        if (user.getId() != null) {
            Long firstJoin = null;
            for (MainActivity segmentMainAct : segmentMainActs) {
                ZnsUserRunDataDetailsEntity dataDetailsEntity = runDataDetailsService.getFirstUserActivityRunDataDetails(user.getId(), segmentMainAct.getId());
                if (dataDetailsEntity != null) {
                    if (firstJoin == null) {
                        firstJoin = dataDetailsEntity.getCreateTime().toInstant().toEpochMilli();
                    }
                    if (firstJoin > dataDetailsEntity.getCreateTime().toInstant().toEpochMilli()) {
                        firstJoin = dataDetailsEntity.getCreateTime().toInstant().toEpochMilli();
                    }
                }
            }
            seriesActivityInfoDto.setFirstJoin(firstJoin);

        }
        //组装奖励排行榜
        seriesActivityInfoDto.setAwardRankDtos(assemblySeriesRewardRanking(mainActivity, user));

        //报告页id todo 最好放activityUser里
        if (user.getId() != null) {
            List<Long> actIds = segmentMainActs.stream().map(MainActivity::getId).toList();
            ZnsUserRunDataDetailsEntity lastRunDetail = runDataDetailsService.findLastByActIds(user.getId(), actIds);

            if (lastRunDetail != null) {
                seriesActivityInfoDto.setDetailId(lastRunDetail.getId());
            }
        }

        return seriesActivityInfoDto;
    }

    /**
     * 组装奖励排行榜
     *
     * @param mainActivity
     * @param user
     * @return
     */
    private List<SeriesAwardRankDto> assemblySeriesRewardRanking(MainActivity mainActivity, ZnsUserEntity user) {
        AwardQueryUser query = new AwardQueryUser();
        query.setActivityId(mainActivity.getId());
        query.setUserId(user.getId());
        query.setHasAwardJudge(true);
        //比赛没有结束，展示总体奖励
        List<SeriesAwardRankDto> list = new ArrayList<>();

        //比赛结束展示获奖用户
        List<ActivityAwardTargetConfigDto> activityAwardTargetConfigDtos = awardActivityBizService.queryAwardByActivityId(query);
        if (!CollectionUtils.isEmpty(activityAwardTargetConfigDtos)) {
            ActivityAwardTargetConfigDto targetConfigDto = activityAwardTargetConfigDtos.get(0);
            List<ApiActivityAwardListDto> targetAwards = targetConfigDto.getTargetAwards();
            ApiActivityAwardListDto apiActivityAwardListDto = targetAwards.stream().filter(k -> AwardSentTypeEnum.RANKING_BASED_REWARDS.getType().equals(k.getType())).findFirst().orElse(null);
            if (apiActivityAwardListDto != null) {
                List<ApiActivityAwardDto> awards = apiActivityAwardListDto.getAwards();
                if (awards.size() < 3) {
                    return null;
                }
                List<SeriesAwardRankDto> rankDtos = awards.stream().sorted(Comparator.comparing(ApiActivityAwardDto::getRankMin))
                        .filter(k -> k.getAmountAwardDto() != null)
                        .limit(3).map(m -> {
                            SeriesAwardRankDto rankDto = new SeriesAwardRankDto();
                            ApiAmountAwardDto amountAwardDto = m.getAmountAwardDto();
                            rankDto.setAmount(I18nConstant.buildCurrencyAmount(amountAwardDto.getCurrency().getCurrencyCode(), amountAwardDto.getAmount()));
                            //完赛设置用户头像名称
                            if (MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())) {
                                List<ActivityUserDto> userDtos = activityUserService.findRankUser(mainActivity.getId(), m.getRankMin(), m.getRankMax(), 2)
                                        .stream().map(r -> {
                                            ActivityUserDto userDto = new ActivityUserDto();
                                            ZnsUserEntity znsUser = userService.findByIdWithoutLogicDelete(r.getUserId());
                                            if (znsUser != null) {
                                                userDto.setAvatar(znsUser.getHeadPortrait());
                                                userDto.setName(znsUser.getFirstName());
                                                userDto.setRank(r.getRank());
                                                userDto.setUserId(znsUser.getId());
                                            }
                                            return userDto;
                                        }).toList();
                                rankDto.setUsers(userDtos);
                            }

                            return rankDto;
                        }).toList();
                if (rankDtos.size() < 3) {
                    return null;
                } else {
                    return rankDtos;
                }
            }
        }


        return null;
    }

    private Map<Integer, ApiActivityAwardDto> getAwardByRank(List<ApiActivityAwardDto> awards, int num) {
        Map<Integer, ApiActivityAwardDto> map = new HashMap();
        for (int i = 1; i <= num; i++) {
            final Integer j = i;
            awards.forEach(k -> {
                if (k.getRankMin() <= j && k.getRankMax() >= j) {
                    map.put(j, k);
                }
            });
        }
        return map;

    }

    private ActivitySeriesLevelInfoDto convertToLevelInfo(MainActivity mainActivity, ZnsUserEntity user) {
        ActivitySeriesLevelInfoDto levelInfoDto = new ActivitySeriesLevelInfoDto();
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        //属性填充
        BeanUtil.copyPropertiesIgnoreNull(subActivity, levelInfoDto);
        levelInfoDto.setSegmentActivityId(mainActivity.getId());


        //判断是否有奖励
        List<ActivityAwardConfig> awardConfigs = activityAwardConfigService.lambdaQuery().eq(ActivityAwardConfig::getActivityId, mainActivity.getId())
                .eq(ActivityAwardConfig::getIsDelete, 0).list();
        levelInfoDto.setHaveAward(CollectionUtils.isEmpty(awardConfigs) ? 0 : 1);


        //每一关条件
        ActivityEnterThreshold threshold = activityEnterThresholdService.findByActId(mainActivity.getId());
        levelInfoDto.setConditionState(threshold == null ? 0 : threshold.getConditionState());

        //是否完成当前关卡 跑步详情id
        if (user.getId() != null) {
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivity.getId(), user.getId());
            if (activityUser != null) {
                levelInfoDto.setIsCompleted(activityUser.getIsComplete());
                levelInfoDto.setDetailId(activityUser.getRunDataDetailsId());
            }
        }
        //路线类型
        ZnsRunRouteEntity routeEntity = runRouteService.selectRunRouteById(subActivity.getRouteId());
        levelInfoDto.setRouteType(routeEntity.getRouteType());
        //房间号
        Integer roomId = roomIdBizService.getRoomIdByActIdGoal(mainActivity.getId(), subActivity.getTarget());
        levelInfoDto.setRoomId(roomId);

        //限速
        List<SpeedRateLimitDto> speedRateLimitDtos = generateSpeedRateLimitDtos(mainActivity, null);
        levelInfoDto.setSpeedRateLimitDto(speedRateLimitDtos);

        //关卡奖励
        AwardQueryUser query = new AwardQueryUser();
        query.setActivityId(mainActivity.getId());
        query.setUserId(user.getId());
        query.setHasAwardJudge(true);
        List<ActivityAwardTargetConfigDto> awards = awardActivityBizService.queryAwardByActivityId(query);
        levelInfoDto.setAwards(awards);

        //目标类型
        levelInfoDto.setTargetType(mainActivity.getTargetType());

        //关卡成绩
        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivity.getId(), user.getId());
        if (activityUser != null) {
            if (mainActivity.getTargetType() == 1) {
                levelInfoDto.setGrade(activityUser.getRunTime());
            } else {
                levelInfoDto.setGrade(activityUser.getRunMileage().intValue());

            }
        }
        //用户剩余的跑步次数
        //跑步次数限制
        if (subActivity.getUserEnterLimit() == -1) {
            levelInfoDto.setSurplusEntryCount(-1);
        } else {
            //获取用户已经跑步次数
            if (user.getId() != null) {
                Integer count = userRunDataDetailsService.getCountByActivityId(user.getId(), mainActivity.getId());
                levelInfoDto.setSurplusEntryCount(subActivity.getUserEnterLimit() - count > 0 ? subActivity.getUserEnterLimit() - count : 0);
            } else {
                levelInfoDto.setSurplusEntryCount(subActivity.getUserEnterLimit());
            }
        }
        //如果重复参赛条件是未完赛，完赛后不可参赛
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (entryGameplay.getRepeatedEntryLimit() == 1) {
            if (levelInfoDto.getIsCompleted() == 1) {
                levelInfoDto.setSurplusEntryCount(0);
            }
        }

        return levelInfoDto;
    }

    /**
     * 赛事基础信息
     *
     * @param mainActivity
     * @param user
     * @param reportInfoDto
     * @param currencyCode
     * @param subActivity
     * @param isPolyList
     * @param languageCode
     * @return
     */
    private ActivityBaseInfoDto generateActivityBaseInfoDto(MainActivity mainActivity, ZnsUserEntity user, ReportInfoDto reportInfoDto,
                                                            String currencyCode, SubActivity subActivity, Integer isPolyList, String languageCode) {
        ActivityBaseInfoDto dto = new ActivityBaseInfoDto();
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        dto.setCompetitionFormat(entryGameplay.getCompetitionFormat());
        dto.setIsAllowChanllenge(entryGameplay.getIsAllowChanllenge());
        if (MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType()) && Arrays.asList(1, 2).contains(isPolyList)) {
            dto.setPolymerization(1);
        } else {
            dto.setPolymerization(0);
        }

        String rankingBy;
        Integer dataSource;
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            SeriesGameplay seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
            rankingBy = seriesGameplay.getRankingBy();
            dataSource = seriesGameplay.getDataSource();
        } else {
            rankingBy = entryGameplay.getRankingBy();
            dataSource = entryGameplay.getDataSource();
        }

        dto.setRankingBy(Integer.valueOf(rankingBy));
        dto.setFetchRule(entryGameplay.getFetchRule());
        dto.setDataSource(dataSource);
        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivity.getId(), user.getId());

        //默认显示活动详情页 可能后面被修改
        ActivityDisseminate disseminates = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
        if (dto.getPolymerization() == 0) {
            dto.setLevelPic(disseminates.getActivityDetailPic());
        }
        dto.setSharePic(disseminates.getSharePic());
        dto.setShareContent(disseminates.getShareContent());
        dto.setEnableTotalRankBeforeEnd(disseminates.getEnableTotalRankBeforeEnd());
        Integer target = 0;

        //活动阶段
        List<ActivityStage> stageList = activityStageService.findByActId(mainActivity.getId());
        activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.TARGET_USER_COUNT).ifPresent(s -> dto.setReachNumsTarget(Integer.valueOf(s.getParamValue())));
        if (activityUser != null) {
            dto.setUserState(activityUser.getUserState());
            //获取最新跑步明细id
            //系列赛处理
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                List<Long> subActivityId = seriesActivityRelService.findSubActivityId(mainActivity.getId());
                ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.getLastUserDetailByActivityIds(user.getId(), subActivityId);
                //阶段赛事
                if (!CollectionUtils.isEmpty(stageList)) {
                    ZnsRunActivityUserEntity segmentActivityUser = runActivityUserService.findActivityUser(subActivityId.get(0), user.getId());
                    if (segmentActivityUser != null) {
                        dto.setDetailId(segmentActivityUser.getRunDataDetailsId());
                    }
                } else {
                    if (Objects.nonNull(entity)) {
                        dto.setDetailId(entity.getId());
                    }
                }
                if (Objects.nonNull(entity)) {
                    dto.setHasRunRecord(1);
                }
            } else {
                //阶段赛事
                if (!CollectionUtils.isEmpty(stageList)) {
                    dto.setDetailId(activityUser.getRunDataDetailsId());
                } else {
                    ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.getLastRunDetail(user.getId(), mainActivity.getId(), FetchRuleTypeEnum.ACCUMULATED_COMPLETION_DATA.getType().equals(entryGameplay.getFetchRule()), entryGameplay.getTargetType());
                    if (Objects.nonNull(entity)) {
                        dto.setDetailId(entity.getId());
                    }
                }
                ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.getLastRunDetail(user.getId(), mainActivity.getId(), false, null);
                if (Objects.nonNull(entity)) {
                    dto.setHasRunRecord(1);
                }
            }
            if (activityUser.getTargetRunTime() > 0) {
                target = activityUser.getTargetRunTime();
            } else if (activityUser.getTargetRunMileage() > 0) {
                target = activityUser.getTargetRunMileage();
            }
            //是否完赛
            dto.setIsComplete(activityUser.getIsComplete());

            if (user.getId() != null) {
                Integer targetType = mainActivity.getTargetType();

                if (targetType == 2) {
                    dto.setGoal(activityUser.getTargetRunTime());
                } else {
                    dto.setGoal(activityUser.getTargetRunMileage());
                }

                dto.setTargetType(targetType);
            }

            //房间号处理
            Integer roomNumber = roomIdBizService.getRoomId(mainActivity, Arrays.asList(target));
            dto.setRoomNumber(roomNumber);

            try {
                //非聚合
                if (dto.getPolymerization() == 0) {
                    //关卡完成图
                    if (Objects.isNull(activityUser.getCompletedLevel()) || activityUser.getCompletedLevel() == 0) {
                        dto.setLevelPic(disseminates.getAllNotCompletedPic());
                    } else {
                        List<String> list = JsonUtil.readList(disseminates.getLevelPics(), String.class);
                        dto.setLevelPic(list.get(activityUser.getCompletedLevel() - 1));
                    }
                }

            } catch (Exception e) {
                log.error("关卡完成图 设置失败");
            }

        }
        dto.setActivityType(mainActivity.getOldType());
        //最大奖励设置

        MaxAwardVo maxAward = awardActivityBizService.findMaxAward(mainActivity.getId(), currencyCode, target, mainActivity.getMainType(), user.getId());
        dto.setMaxScore(maxAward.getMaxScore());
        dto.setMaxAmount(maxAward.getMaxReward());
        dto.setDistributeAmount(maxAward.getDistributeReward());
        dto.setDistributeScore(maxAward.getDistributeScore());

        //币种
        if (user.getId() != null) {
            dto.setCurrency(accountService.getCurrency(user.getId(), mainActivity.getId(), true));
        }
        //玩法判断
        if (entryGameplay.getRepeatedEntryLimit() == 1 && Objects.nonNull(activityUser) && activityUser.getIsComplete() == 1) {
            dto.setSurplusEntryCount(0);
        } else {
            //跑步次数限制
            if (subActivity.getUserEnterLimit() == -1) {
                dto.setSurplusEntryCount(-1);
            } else {
                //获取用户已经跑步次数
                Integer count = userRunDataDetailsService.getCountByActivityId(user.getId(), mainActivity.getId());
                dto.setSurplusEntryCount(subActivity.getUserEnterLimit() - count > 0 ? subActivity.getUserEnterLimit() - count : 0);
            }
        }

        //团队id
        ActivityTeam team = activityUserBizService.getUserCurrentTeam(mainActivity.getId(), user.getId());
        if (team != null) {
            dto.setTeamId(team.getId());
            if (ActivityTeamTypeEnum.CLUB.getCode().equals(team.getTeamType())) {
                clubActivityTeamService.findByTeamId(team.getId()).ifPresent(i -> {
                    dto.setClubId(i.getClubId());
                });
            }
        }
        ///有问题 1显示 0不显示,
        Long endTime = DateUtil.getStampByZone(mainActivity.getActivityEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId());

        if (MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())) {
            if (System.currentTimeMillis() - endTime <= 15 * DAY_MILS) {
                dto.setIsHasProblem(1);
            }
        }


        SysConfig chatConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.chat_robot_user_ids.getCode());
        //填充客服
        if (Objects.nonNull(chatConfig)) {
            String configValue = chatConfig.getConfigValue();
            String[] split = configValue.split(",");
            String chatRobotUserId = split[0];
            ZnsUserEntity customer = userService.findById(Long.parseLong(chatRobotUserId));
            dto.setCustomerId(Long.parseLong(chatRobotUserId));
            dto.setCustomerName(customer.getFirstName());
        }
        dto.setIsCompetitive(mainActivity.getIsCompetitive());
        ActivityCompetitiveListDto activityCompetitive = competitiveSeasonBizService.getActivityCompetitive(mainActivity.getId());
        dto.setCompetitiveSeasonIconUrl(activityCompetitive.getCompetitiveSeasonIconUrl());
        if (activityCompetitive.getCompetitiveSeasonType() != null) {
            dto.setCompetitiveSeasonType(activityCompetitive.getCompetitiveSeasonType());
        }
        //组装超越人信息
        Optional<ActivityParams> optional = activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.SURPASS_USER_CODE);
        optional.ifPresent(k -> {
            ZnsUserEntity byUserCode = userService.findByUserCode(k.getParamValue());
            if (byUserCode != null) {
                SurpassDto surpassDto = new SurpassDto();
                surpassDto.setUserCode(byUserCode.getUserCode());
                surpassDto.setUserId(byUserCode.getId());
                surpassDto.setUserName(byUserCode.getFirstName());
                dto.setSurpassDto(surpassDto);
            }
        });
        //奖励是否已经发放
        dto.setAwardSendFinish(mainActivityService.getActivityAwardSendStatus(mainActivity.getId()) ? 1 : 0);


        //阶段活动组装
        List<ActivityStageApiDto> activityStageApiDtos = stageList.stream().map(k -> {
            ActivityStageApiDto stage = new ActivityStageApiDto();
            //确保有用户信息
            if (user.getId() != null) {
                RunActivityStageUser userAndStage = runActivityStageUserService.findByUserAndStage(user.getId(), k.getId());
                if (userAndStage != null && userAndStage.getRunDataDetailsId() != null) {
                    stage.setIsRun(1);
                } else {
                    stage.setIsRun(0);
                }
            }
            stage.setStageId(k.getId());
            stage.setEndTime(k.getEndTime());
            stage.setStartTime(k.getStartTime());
            return stage;
        }).collect(Collectors.toList());
        dto.setStageApiDtos(activityStageApiDtos);
        dto.setEquipmentMainType(mainActivity.getEquipmentMainType());
        dto.setIsShowInMore(mainActivity.getIsShowInMore());
        // 奖励券开关
        if (dto.getCompetitionFormat() == 0 && !List.of(MainActivityStateEnum.ENDED.getCode(), MainActivityStateEnum.OFF_SHELF.getCode()).contains(reportInfoDto.getActivityState())) {
            activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.ALLOW_AWARD_COUPON_SWITCH).ifPresent(s -> dto.setAllowAwardCouponSwitch(Integer.valueOf(s.getParamValue())));
        }
        return dto;
    }

    /**
     * 活动规则
     *
     * @param mainActivity
     * @param user
     * @param subActivity
     * @param isPolyList
     * @param languageCode
     * @return
     */
    private ActivityRuleDto generateActivityRuleDto(MainActivity mainActivity, ZnsUserEntity user, SubActivity subActivity, Integer isPolyList, String languageCode) {
        ActivityRuleDto activityRuleDto = new ActivityRuleDto();
        //报名开始时间
        activityRuleDto.setApplicationStartTime(DateUtil.getStampByZone(mainActivity.getApplicationStartTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));

        //报名结束时间
        activityRuleDto.setApplicationEndTime(DateUtil.getStampByZone(mainActivity.getApplicationEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));

        polyTimeCovert(mainActivity, user, new ReportInfoDto(), activityRuleDto, isPolyList);
        //跑道
        if (mainActivity.isSeriesMain()) {
            //系列赛使用第一个子赛事的id
            List<Long> refActivityIds = seriesActivityRelService.findSubActivityId(mainActivity.getId());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(refActivityIds)) {
                Optional<Long> first1 = refActivityIds.stream().sorted().findFirst();
                if (first1.isPresent()) {
                    SubActivity byId = subActivityService.getSingleActByMain(first1.get());
                    if (byId != null) {
                        setRouteVo(byId, activityRuleDto);
                    }
                }
            }
        } else {
            setRouteVo(subActivity, activityRuleDto);
        }

        //格式化观赛预览数据
        RunRouteVO route = activityRuleDto.getRoute();
        if (Objects.nonNull(route) && StringUtils.hasText(route.getWatchPreviewResJson())) {
            Optional<RunRouteVO.WatchPreviewResDto> watchPreviewResDto = route.findWatchPreviewResDto(mainActivity.getEquipmentMainType());
            if (watchPreviewResDto.isPresent()) {
                route.setWatchPreviewRes(watchPreviewResDto.get().getRes());
                route.setWatchPreviewResType(watchPreviewResDto.get().getResType());
            }
        }

        //参赛人员地区
        List<String> areaNames = getParticipants(mainActivity, user, languageCode);
        if (!CollectionUtils.isEmpty(areaNames)) {
            activityRuleDto.setParticipants(areaNames);
        }

//        List<ActivityArea> activityAreas = activityAreaService.findByActId(mainActivity.getId());
//        if (!CollectionUtils.isEmpty(activityAreas)) {
//            List<String> areaNames = areaI18nService.lambdaQuery().in(AreaI18nEntity::getAreaId, activityAreas.stream().map(ActivityArea::getAreaId).toList())
//                    .list().stream().map(AreaI18nEntity::getAreaName).toList();
//
//            activityRuleDto.setParticipants(areaNames);
//        }

        //反作弊
        activityRuleDto.setCheatSwitch(mainActivity.getCheatSwitch());

        //人数限制
        activityRuleDto.setNumberLimit(mainActivity.getEnterLimit());

        //完赛证书
        activityRuleDto.setFinishedCertificate(mainActivity.getFinishedCertificate());
        activityRuleDto.setFirstCertificate(mainActivity.getFirstCertificate());

        //品牌权益
        //SupeRun deerRun
        ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(1, mainActivity.getId(), user.getId());
        if (brandRightsInterests != null) {
            if (brandRightsInterests.getBrand() == 1) {
                activityRuleDto.setBrandRights(I18nMsgUtils.getMessage("superun.free.right"));
            } else if (brandRightsInterests.getBrand() == 2) {
                activityRuleDto.setBrandRights(I18nMsgUtils.getMessage("deerrun.free.right"));
            } else if (brandRightsInterests.getBrand() == 3) {
                activityRuleDto.setBrandRights(I18nMsgUtils.getMessage("jll.free.right"));
            }
        }
        List<ActivityBrandRightsInterests> brandRightsInterestsList = brandRightsInterestsService.getBrandRightsInterestsByActId(mainActivity.getId());
        if (!CollectionUtils.isEmpty(brandRightsInterestsList)) {
            ActivityBrandRightsInterests interests = brandRightsInterestsList.get(0);
            if (interests.getBrand() == 1) {
                activityRuleDto.setBrandRightsContent(I18nMsgUtils.getMessage("superun.free.right"));
            } else if (interests.getBrand() == 2) {
                activityRuleDto.setBrandRightsContent(I18nMsgUtils.getMessage("deerrun.free.right"));
            } else if (interests.getBrand() == 3) {
                activityRuleDto.setBrandRightsContent(I18nMsgUtils.getMessage("jll.free.right"));
            }
        }

        //报名参赛包
        ActivityImpracticalAwardConfig impracticalAwardConfig = impracticalAwardConfigService.findByActId(mainActivity.getId());
        activityRuleDto.setIsMustWear(Objects.nonNull(impracticalAwardConfig) ? impracticalAwardConfig.getIsMustWear() : null);
        String key = ApiConstants.ACTIVITY_APPLICATION_AWARD_DRESS + mainActivity.getId() + user.getId();
        Object o = redisTemplate.opsForValue().get(key);
        activityRuleDto.setIsDress(Objects.nonNull(o) ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
        //描述
        ActivityDisseminate disseminates =
                activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
        //标题
        activityRuleDto.setDesc(disseminates.getActivityDesc());
        activityRuleDto.setRule(disseminates.getRule());
        activityRuleDto.setDetailPic(disseminates.getDetailPic());
        activityRuleDto.setRunwayPic(disseminates.getRunwayPic());
        //团队赛参赛队伍属性
        //回显团队赛字段数据
        ActivityTeamJoinSettingDto activityTeamSetting = activityParamsLoaderService.getActivityTeamSetting(mainActivity.getId());
        if (activityTeamSetting != null && activityTeamSetting.getAllowJoinTeamType() != null) {
            activityRuleDto.setTeamType(activityTeamSetting.getAllowJoinTeamType().getCode());
            activityRuleDto.setClubTeamConfig(activityTeamSetting.getClubTeamConfig());
        }
        //回显开赛时间限制
        Optional<ActivityParams> optional = activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.AFTER_START_LIMIT);
        optional.ifPresent(k -> activityRuleDto.setAfterStartLimit(Integer.parseInt(k.getParamValue())));
        return activityRuleDto;
    }

    private void setRouteVo(SubActivity subActivity, ActivityRuleDto activityRuleDto) {
        ZnsRunRouteEntity routeEntity = runRouteService.selectRunRouteById(subActivity.getRouteId());
        if (null != routeEntity) {
            // 设置路线相关数据
            RunRouteVO routeVO = runRouteService.wrapperRunRouteVO(routeEntity);
            activityRuleDto.setRoute(routeVO);
        }
    }

    private List<String> getParticipants(MainActivity mainActivity, ZnsUserEntity user, String languageCode) {
        List<ActivityArea> activityAreas = activityAreaService.findByActId(mainActivity.getId());
        List<String> showCountryList = new ArrayList<>();
        if (CollectionUtils.isEmpty(activityAreas)) {
            //全球
            String global = I18nMsgUtils.getLangMessage(languageCode, "accountDetail.area.global");
            showCountryList.add(global);
        }

        //获取国家类型区域
        List<Long> countryIds = activityAreas.stream().filter(item -> RegionConstants.RegionTypeEnum.REGION_TYPE_2.getCode().equals(item.getRegionType()))
                .map(ActivityArea::getAreaId).filter(Objects::nonNull).distinct().toList();
        if (!CollectionUtils.isEmpty(countryIds)) {
            showCountryList = countryI18nService.findList(CountryI18nQuery.builder().countryCode(languageCode).countryIds(countryIds).build()).stream().map(CountryI18nEntity::getName).collect(Collectors.toList());
        }

        //获取州类型区域
        List<Long> areaIds = activityAreas.stream().filter(item -> RegionConstants.RegionTypeEnum.REGION_TYPE_1.getCode().equals(item.getRegionType()))
                .map(ActivityArea::getAreaId).filter(Objects::nonNull).distinct().toList();
        if (!CollectionUtils.isEmpty(areaIds)) {
            List<String> partAreaList = areaI18nService.findByLanguageCodeAndIds(languageCode, areaIds).stream().map(AreaI18nEntity::getAreaName).toList();
            showCountryList.addAll(partAreaList);
        }
        return showCountryList;
    }

    /**
     * 赛事限速规则
     *
     * @param mainActivity
     * @param user
     * @return
     */
    private List<SpeedRateLimitDto> generateSpeedRateLimitDtos(MainActivity mainActivity, ZnsUserEntity user) {
        Integer targetType = getSingleActivityTargetType(mainActivity.getId());

        ArrayList<SpeedRateLimitDto> list = new ArrayList<>();
        List<ActivityRateLimit> activityRateLimitList = activityRateLimitService.findList(
                ActivityRateLimitQuery.builder()
                        .activityId(mainActivity.getId())
                        .build()
        );

        Map<BigDecimal, List<ActivityRateLimit>> goalRateLimitMap = activityRateLimitList.stream().collect(Collectors.groupingBy(ActivityRateLimit::getRunningGoal));
        for (Map.Entry<BigDecimal, List<ActivityRateLimit>> entry : goalRateLimitMap.entrySet()) {
            SpeedRateLimitDto speedRateLimitDto = new SpeedRateLimitDto();
            BigDecimal goal = entry.getKey();
            List<ActivityRateLimit> rateLimits = entry.getValue();
            List<RateLimitDto> limitDtos = rateLimits.stream().map(k -> {
                RateLimitDto rateLimitDto = new RateLimitDto();
                rateLimitDto.setStart(k.getStart());
                rateLimitDto.setEnd(k.getEnd());
                rateLimitDto.setRate(k.getRateLimit());
                return rateLimitDto;
            }).sorted(Comparator.comparing(RateLimitDto::getStart)).toList();

            speedRateLimitDto.setGoal(goal);
            speedRateLimitDto.setRates(limitDtos);
            speedRateLimitDto.setTargetType(targetType);
            list.add(speedRateLimitDto);
        }

        return list.stream().sorted(Comparator.comparing(SpeedRateLimitDto::getGoal))
                .toList();


    }

    /**
     * 报名相关信息
     *
     * @param mainActivity
     * @param user
     * @param isPolyList
     * @param languageCode
     * @param appVersion
     * @return
     */
    private ReportInfoDto generateReportInfoDto(MainActivity mainActivity, ZnsUserEntity user, Integer isPolyList, String languageCode, Integer appVersion) {
        Long mainActivityId = mainActivity.getId();
        ReportInfoDto reportInfoDto = new ReportInfoDto();
        ActivityDisseminate disseminates = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivityId, languageCode);
        //标题
        reportInfoDto.setActivityTitle(disseminates.getTitle());
        //图片
        reportInfoDto.setPicture(disseminates.getCoverPic());
        if (!StringUtils.hasText(disseminates.getDetailTopPromoPic())) {
            reportInfoDto.setDetailTopPromoPic(disseminates.getCoverPic());
        } else {
            reportInfoDto.setDetailTopPromoPic(disseminates.getDetailTopPromoPic());
        }

        //活动状态
        Integer activityState = mainActivityService.getActivityState(mainActivityId, user);

        reportInfoDto.setActivityState(activityState);
        //币种
        Currency currency = accountService.getCurrency(user.getId(), mainActivityId, true);
        if (currency != null) {
            reportInfoDto.setCurrency(currency);
        }
        //报名金额 积分
        activityFeeService.findByActId(mainActivityId).stream()
                .filter(c -> c.getCurrency().equals(currency.getCurrencyCode())).findFirst().ifPresent(k -> {
                    reportInfoDto.setAmount(k.getAmount());
                    reportInfoDto.setScore(k.getScore());
                    reportInfoDto.setType(k.getType());
                    // 版本兼容 旧版本 钱/分报名只需要钱
                    if (appVersion < 40700 && k.getType().equals(ActivityFeeTypeEnum.SCORE_OR_FEE.getType())) {
                        reportInfoDto.setScore(0);
                    }
                });
        //会员用户，报名金额和积分是0
        if (Objects.equals(user.getMemberType(), 1)) {
            if (appVersion < 4043) {
                reportInfoDto.setAmount(new BigDecimal(0));
                reportInfoDto.setScore(0);
            } else {
                Optional<CompetitiveSeasonDo> optional = competitiveSeasonService.findByActivityId(mainActivity.getId());
                optional.ifPresentOrElse(k -> {
                    ActivityCompetitiveSeasonType competitiveSeasonType = k.getCompetitiveSeasonType();
                    if (!(ActivityCompetitiveSeasonType.ANNUAL.equals(competitiveSeasonType) || ActivityCompetitiveSeasonType.SEASONAL.equals(competitiveSeasonType))) {
                        reportInfoDto.setAmount(new BigDecimal(0));
                        reportInfoDto.setScore(0);
                    }
                }, () -> {
                    reportInfoDto.setAmount(new BigDecimal(0));
                    reportInfoDto.setScore(0);
                });
            }
            Optional<ProActivityDo> optionalProActivity = proActivityService.findByMainActivityId(mainActivityId);
            optionalProActivity.ifPresent(k -> {
                if (!ProActivityType.isPro(k.getProActivityType()) || ProActivityType.WEEK.equals(k.getProActivityType())) {
                    reportInfoDto.setAmount(new BigDecimal(0));
                    reportInfoDto.setScore(0);
                }
            });

        }


        //目标
        List<Integer> goals = subActivityService.getAllSingleActByMain(mainActivityId).stream().map(SubActivity::getTarget)
                .sorted().toList();
        reportInfoDto.setGoals(goals);

        //开始时间
        reportInfoDto.setStartTime(DateUtil.getStampByZone(mainActivity.getActivityStartTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
        //结束时间
        reportInfoDto.setEndTime(DateUtil.getStampByZone(mainActivity.getActivityEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));

        //报名开始时间
        reportInfoDto.setApplicationStartTime(DateUtil.getStampByZone(mainActivity.getApplicationStartTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
        //报名结束时间
        reportInfoDto.setApplicationEndTime(DateUtil.getStampByZone(mainActivity.getApplicationEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
        if (activityParamsService.checkAwardSendType(mainActivityId)) {
            String reviewTime = sysConfigService.selectConfigByKey("activity.award.review.time");
            if (!StringUtils.hasText(reviewTime)) {
                //默认72小时
                reviewTime = "72";
            }
            reportInfoDto.setPlanSendAwardTime(reportInfoDto.getEndTime() + Long.parseLong(reviewTime) * 1000 * 60 * 60);
        } else {
            reportInfoDto.setPlanSendAwardTime(reportInfoDto.getEndTime());
        }
        //聚合活动时间处理
        polyTimeCovert(mainActivity, user, reportInfoDto, new ActivityRuleDto(), isPolyList);
        //0：无，1：里程，2：时长',
        Integer targetType = getSingleActivityTargetType(mainActivityId);
        reportInfoDto.setTargetType(targetType);

        //可用设备
        List<ActivityEquipmentConfig> equipments = equipmentConfigService.findByActId(mainActivityId);

        //设备类型分类关系
        List<EquipmentCategoryDto> equipmentCategories = mallCategoryEquiModelBizService.findActEquipmentCategory(equipments);
        reportInfoDto.setEquipmentCategories(equipmentCategories);
        List<String> equipmentInfos = equipments.stream().map(item -> {
            if (Objects.equals(item.getEquipmentType(), 3)) {
                // 跑步形态 , 1 走步形态 , 2. 跑步形态
                if (Objects.equals(item.getSubType(), 1)) {
                    item.setEquipmentInfo(item.getEquipmentInfo() + " (Walking Mode)");
                } else if (Objects.equals(item.getSubType(), 2)) {
                    item.setEquipmentInfo(item.getEquipmentInfo() + " (Running Mode)");
                }
            }
            return item.getEquipmentInfo();
        }).toList();
        reportInfoDto.setEquipments(equipmentInfos);

        //房间号处理
        reportInfoDto.setWatchRoomId(roomIdBizService.getRoomId(mainActivity, goals));

        //观战信息
        if (targetType == 1) {
            reportInfoDto.setWatchRoomMileageGoal(goals.get(0));
        } else {
            reportInfoDto.setWatchRoomTimeGoal(goals.get(0));
        }

        reportInfoDto.setSurplusContestCount(getSurplusContestCount(user));
        reportInfoDto.setSignUpLimit(getSignUpLimit(user, mainActivityId));

        //可选券
        if (user.getId() != null) {
            setUserCouponDiKou(mainActivity, user, reportInfoDto.getAmount(), reportInfoDto);
        }
        reportInfoDto.setEquipmentVersion(mainActivity.getEquipmentVersion());
        if (mainActivity.isBikeActivity()) {
            Optional<ActivityParams> oneByMainActivityAndParamType = activityParamsService.findOneByMainActivityAndParamType(mainActivityId, ActivitySettingConfigEnum.DESK_BIKE_EQUIPMENT_VERSION);
            if (oneByMainActivityAndParamType.isPresent()) {
                reportInfoDto.setDeskBikeEquipmentVersion(Integer.valueOf(oneByMainActivityAndParamType.get().getParamValue()));
            }
        }
        reportInfoDto.setWaitTime(mainActivity.getWaitTime());
        reportInfoDto.setIsMemberActivity(mainActivity.getIsMemberActivity());
        //竞技分限制 是否外卡
        if (mainActivity.isCompetitive()) {
            reportInfoDto.setNotReachCompetitionScoreThreshold(
                    competitiveSeasonBizService.checkReachCompetitionScoreThreshold(mainActivity.getId(), user.getId()) ?
                            0 : 1);
            reportInfoDto.setIsForeign(CompetitiveShortlistTypeEnum.WILD_CARD.equals(
                    competitiveShortlistService.getUserIdentity(mainActivity.getId(), user.getId())) ? 1 : 0);

            reportInfoDto.setCompetitionEntryCondition(competitiveSeasonBizService.getCompetitionEntryCondition(mainActivity.getId(), user));

        } else {
            reportInfoDto.setNotReachCompetitionScoreThreshold(0);
        }
        Boolean marathon = sysConfigService.isMarathon(mainActivity.getId());
        //判断商城开关
        SysConfig mallConfig = sysConfigService.selectSysConfigByKey("mall_home_config_switch");
        Integer mallSwitch = (mallConfig == null || !StringUtils.hasText(mallConfig.getConfigValue())) ? 0 : Integer.parseInt(mallConfig.getConfigValue());
        if (marathon && Objects.equals(mallSwitch, 1)) {
            //商品推荐
            ZnsUserEquipmentEntity znsUserEquipmentEntity = znsUserEquipmentService.selectRecentNoDelete(user.getId());
            String activityCategoryCode = sysConfigService.selectConfigByKey("activity_categoryCode");
            Map<String, String> map = JsonUtil.readValue(activityCategoryCode, Map.class);
            String defaultCode = map.get("default");
            String categoryCode = defaultCode;
            if (Objects.nonNull(znsUserEquipmentEntity)) {
                String equipmentModel = znsUserEquipmentEntity.getEquipmentModel();
                String prefix = equipmentModel.substring(0, 2).toUpperCase();
                categoryCode = map.getOrDefault(prefix, defaultCode);
            }
            reportInfoDto.setRecommendGoodsRespDtoList(goodsManager.goodsRespByCategoryCode(categoryCode, user));
        }
        activityParamsService.findOneByMainActivityAndParamType(mainActivityId, ActivitySettingConfigEnum.GENDER_LIMIT).ifPresent(k -> reportInfoDto.setGender(Integer.valueOf(k.getParamValue())));
        reportInfoDto.setUserGender(user.getGender());
        //是否修改过性别
        UserExtraParamsDo userExtraParams = userExtraParamsService.findUserExtraParams(user.getId(), UserExtraParamsKeyEnum.UPDATE_GENDER_RECORD);
        reportInfoDto.setIsUpdateGendered(Objects.isNull(userExtraParams) ? 0 : 1);
        return reportInfoDto;
    }

    /**
     * 获得活动目标类型：0：无，1：里程，2：时长
     *
     * @param mainActivityId 活动id
     * @return
     */
    public Integer getSingleActivityTargetType(Long mainActivityId) {
        Long playId = mainActivityService.findById(mainActivityId).getPlayId();
        return entryGameplayService.findByGameplayId(playId).getTargetType();
    }

    private void polyTimeCovert(MainActivity mainActivity, ZnsUserEntity user, ReportInfoDto reportInfoDto, ActivityRuleDto activityRuleDto, Integer isPolyList) {
        //聚合活动时间处理
        if (mainActivity.getMainType().equals(MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()) && (isPolyList == 1 || isPolyList == 2)) {
            List<ActivityPolymerizationRecord> batchRecord = activityPolymerizationRecordService.getBatchRecord(mainActivity.getId());
            ZonedDateTime now = ZonedDateTime.now();
            ZonedDateTime currentTime = DateTimeUtil.parse(DateUtil.getCurrentTime(TimeZone.getTimeZone(user.getZoneId())));

            //通过时间过滤已经结束的
            List<ActivityPolymerizationRecord> newBatchRecord = batchRecord.stream().filter(item -> {
                if (mainActivity.getTimeStyle() == 0) {
                    return item.getEndTime().compareTo(now) >= 0;
                } else {
                    return item.getEndTime().compareTo(currentTime) >= 0;
                }
            }).toList();
            if (!CollectionUtils.isEmpty(newBatchRecord)) {
                batchRecord = newBatchRecord;
            }
            ActivityPolymerizationRecord minStartTime = batchRecord.stream().min(Comparator.comparing(ActivityPolymerizationRecord::getStartTime)).get();
            ActivityPolymerizationRecord maxEndTime = batchRecord.stream().max(Comparator.comparing(ActivityPolymerizationRecord::getEndTime)).get();
            ActivityPolymerizationRecord minStartApplicationTime = batchRecord.stream().min(Comparator.comparing(ActivityPolymerizationRecord::getApplicationStartTime)).get();
            ActivityPolymerizationRecord maxEndApplicationTime = batchRecord.stream().max(Comparator.comparing(ActivityPolymerizationRecord::getApplicationEndTime)).get();
            Long parentActivityId = minStartTime.getParentActivityId();
            MainActivity main = mainActivityService.findById(parentActivityId);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (Objects.nonNull(reportInfoDto)) {
                reportInfoDto.setStartTime(DateUtil.getStampByZone(dateFormat.format(minStartTime.getStartTime()), main.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
                reportInfoDto.setEndTime(DateUtil.getStampByZone(dateFormat.format(maxEndTime.getEndTime()), main.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
                reportInfoDto.setApplicationStartTime(
                        DateUtil.getStampByZone(dateFormat.format(minStartApplicationTime.getApplicationStartTime()), main.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
                reportInfoDto.setApplicationEndTime(DateUtil.getStampByZone(dateFormat.format(maxEndApplicationTime.getApplicationEndTime()), main.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
            }
            if (Objects.nonNull(activityRuleDto)) {
                activityRuleDto.setApplicationStartTime(
                        DateUtil.getStampByZone(dateFormat.format(minStartApplicationTime.getApplicationStartTime()), main.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
                activityRuleDto.setApplicationEndTime(DateUtil.getStampByZone(dateFormat.format(maxEndApplicationTime.getApplicationEndTime()), main.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
            }
        }
    }

    /**
     * 获取用户抵扣券
     *
     * @param activity
     * @param user/
     * @param fee
     * @param reportInfoDto
     * @return
     */
    private void setUserCouponDiKou(MainActivity activity, ZnsUserEntity user, BigDecimal fee, ReportInfoDto reportInfoDto) {
        Long userId = user.getId();

        if (activity.getAllowCoupon() == 0) {
            return;
        }
        if (Objects.isNull(fee) || fee.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(userId);
        List<UserCouponDiKou> userCoupons = userCouponService.selectCanUseConponList(activity.getId(), userId, activity.getOldType(), userAccount.getCurrencyCode(), null);
        if (CollectionUtils.isEmpty(userCoupons)) {
            return;
        } else {
            if (Objects.nonNull(fee) && fee.compareTo(BigDecimal.ZERO) > 0) {
                userCoupons = userCoupons.stream().filter(e -> e.getGmtEnd().isAfter(ZonedDateTime.now())).collect(Collectors.toList());
                // 先计算绝对值
                List<UserCouponDiKou> couponDiKous1 = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(fee.subtract(coupon.getAmount()).abs())).collect(Collectors.toList());
                couponDiKous1.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                    // free 0 值 完全匹配
                    if (i.getAbsAmount().compareTo(BigDecimal.ZERO) == 0) {
                        reportInfoDto.setDeCoupon(i);
                    }
                });
                if (Objects.isNull(reportInfoDto.getDeCoupon())) {
                    List<UserCouponDiKou> couponDiKous = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(fee.subtract(coupon.getAmount()))).collect(Collectors.toList());
                    // 筛选 插值为 负数值
                    List<UserCouponDiKou> couponDiKous2 = couponDiKous.stream().filter(coupon -> coupon.getAbsAmount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(couponDiKous2)) {
                        couponDiKous2.stream().max(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                            reportInfoDto.setDeCoupon(i);
                        });
                    }
                    if (Objects.isNull(reportInfoDto.getDeCoupon())) {
                        {
                            couponDiKous.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                reportInfoDto.setDeCoupon(i);
                            });
                        }
                    }
                }
            }
        }

        if (Objects.nonNull(reportInfoDto.getDeCoupon())) {
            UserCouponDiKou diKou = reportInfoDto.getDeCoupon();
            Currency currency = new Currency(diKou.getCurrencyName(), diKou.getCurrencyCode(), diKou.getCurrencySymbol());
            diKou.setCurrency(currency);
            reportInfoDto.setDeCoupon(diKou);
        }
    }

    private UserCouponDiKou toUserCouponDiKou(Coupon coupon, ZnsUserEntity user) {
        try {
            UserCouponDiKou couponDiKou = new UserCouponDiKou();
            BeanUtils.copyProperties(coupon, couponDiKou);
            couponDiKou.setCouponId(coupon.getId());
            couponDiKou.setCouponMainType(coupon.getCouponMainType());
            couponDiKou.setId(null);
            CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                    .couponId(coupon.getId()).langCode(user.getLanguageCode()).defaultLangCode(coupon.getDefaultLangCode()).build());
            if (Objects.nonNull(couponI18n)) {
                couponDiKou.setTitle(couponI18n.getTitle());
                couponDiKou.setVerifyDesc(couponI18n.getCanUseDescription());
            } else {
                couponDiKou.setTitle(coupon.getTitle());
                couponDiKou.setVerifyDesc(coupon.getCanUseDescription());
            }

            ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
            CouponCurrencyEntity couponCurrencyEntity = couponCurrencyService.selectByCouponIdAndCurrencyCode(coupon.getId(), userAccount.getCurrencyCode());
            if (Objects.nonNull(couponCurrencyEntity)) {
                Currency currency = new Currency(couponCurrencyEntity.getCurrencyName(), couponCurrencyEntity.getCurrencyCode(), couponCurrencyEntity.getCurrencySymbol());
                couponDiKou.setCurrency(currency);
            } else {
                couponDiKou.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD));
            }

            return couponDiKou;
        } catch (Exception e) {
            log.error("toUserCouponDiKou error", e);
            return null;
        }
    }

    private Integer getSurplusContestCount(ZnsUserEntity user) {
        //查询参赛限制
        String contestCountLimit = sysConfigService.selectConfigByKey("activity.contest.count.limit");
        if (StringUtils.isEmpty(contestCountLimit) || "-1".equals(contestCountLimit)) {
            return null;
        }
        //查询用户今日参赛次数
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(user.getZoneId()));
        ZonedDateTime startOfDate = DateUtil.getStartOfDate(now, TimeZone.getTimeZone(user.getZoneId()));
        ZonedDateTime endOfDate = DateUtil.getEndOfDate(now, TimeZone.getTimeZone(user.getZoneId()));

        //剩余参赛次数
        Integer activityUserCount = runActivityUserService.findNewActivityUserCountByUserId(user.getId(), startOfDate, endOfDate, Arrays.asList(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType()));
        Integer surplusContestCount = Integer.valueOf(contestCountLimit) - activityUserCount;
        if (surplusContestCount < 0) {
            surplusContestCount = 0;
        }
        return surplusContestCount;
    }

    private Integer getSignUpLimit(ZnsUserEntity user, Long mainActivityId) {
        //查询赛事玩法报名限制
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        Long gameId = mainActivity.getPlayId();
        if (gameId == null) {
            return -1;
        }
        Gameplay gameplay = gameplayService.findById(gameId);
        if (Objects.isNull(gameplay) || gameplay.getSignUpLimit() == null) {
            return -1;
        }
        //赛事玩法报名次数
        Integer signUpLimit = gameplay.getSignUpLimit();
        //查询用户已经报名次数次数
        Integer gameSignUpCount = runActivityUserService.findGameSignUpCount(user, gameId);
        //剩余参赛次数
        int count = signUpLimit - gameSignUpCount;
        if (count < 0) {
            count = 0;
        }
        return count;
    }

    @FillerMethod
    public MyRaceDto myRace(Long mainActivityId, ZnsUserEntity loginUser) {
        if (!NumberUtils.geZero(mainActivityId)) {
            return null;
        }
        MyRaceDto myRaceDto = new MyRaceDto();
        Long userId = loginUser.getId();
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        Integer competitionFormat = entryGameplay.getCompetitionFormat();
        Integer targetType = entryGameplay.getTargetType();
        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivityId, userId);
        if (Objects.isNull(activityUser)) {
            return null;
        }
        //获取最新跑步记录
        List<ZnsUserRunDataDetailsEntity> detailByActivityId = runDataDetailsService.getUserDetailByActivityId(new Page(1, 1), loginUser.getId(), mainActivityId);
        if (!CollectionUtils.isEmpty(detailByActivityId)) {
            myRaceDto.setIsCheat(detailByActivityId.get(0).getIsCheat());
        }

        //增加里程碑弹窗判断
        myRaceDto.setIsLightenCityPopup(0);
        if (activityUser.getCompletedLevel() > 0) {
            //查询配置
            ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivityId, loginUser.getLanguageCode());
            myRaceDto.setIsLightenCityPopup(getIsLightenCityPopup(disseminate, activityUser, loginUser));
        }

        //todo:现在只有单选
        String rankingBy = entryGameplay.getRankingBy();
        Integer rankType = Integer.valueOf(rankingBy);
        myRaceDto.setRankType(rankType);

        //个人赛
        if (competitionFormat == 0) {
            myRaceDto.setRank(activityUser.getRank());
            if (rankType == 2) {
                myRaceDto.setGrade(activityUser.getRunTime());
            } else {
                myRaceDto.setGrade(activityUser.getRunMileage().intValue());


            }
            if (targetType == 2) {
                myRaceDto.setGoal(activityUser.getTargetRunTime());
            } else {
                myRaceDto.setGoal(activityUser.getTargetRunMileage());
            }

            myRaceDto.setTargetType(targetType);

        } else {

            MyTeamActRecordDto teamRecordDto = getMyTeamActRecordDto(mainActivity, userId);
            //只保留前20条
            teamRecordDto.setUserGrades(teamRecordDto.getUserGrades().stream().limit(20).toList());
            myRaceDto.setMyTeamActRecordDto(teamRecordDto);
        }

        //奖励
        List<ApiActivityAwardListDto> userAward = awardActivityManager.findUserAward(mainActivityId, loginUser, myRaceDto.getGoal(), targetType);
        myRaceDto.setAwardList(userAward);
        if (!CollectionUtils.isEmpty(myRaceDto.getAwardList())) {
            //聚合type分类中的awards类型。将均分金额和积分合并到一个对象中
            for (ApiActivityAwardListDto awardListDto : myRaceDto.getAwardList()) {
                List<ApiActivityAwardDto> awards = awardListDto.getAwards();
                //合并完赛奖励 ,外赛奖励可能会分多次方法，前端上需要合并为一个对象。需要对其信息合并。
                if (AwardSentTypeEnum.COMPLETING_THE_GAME.getType().equals(awardListDto.getType()) && !CollectionUtils.isEmpty(awards) && awards.size() > 1) {
                    List<ApiActivityAwardDto> newLists = new ArrayList<>();
                    ApiActivityAwardDto finalAward = awards.get(0);
                    newLists.add(finalAward);
                    for (int i = 1; i < awards.size(); i++) {
                        ApiActivityAwardDto indexAwards = awards.get(i);
                        if (indexAwards.getScore() != null) {
                            finalAward.addScore(indexAwards.getScore());
                            indexAwards.setScore(null);
                        }
                        if (indexAwards.getAmountAwardDto() != null) {
                            finalAward.addAmountAwardDto(indexAwards.getAmountAwardDto());
                            indexAwards.setAmountAwardDto(null);
                        }
                        boolean saveItem = false;
                        if (indexAwards.getCouponAwardDto() != null) {
                            if (finalAward.getCouponAwardDto() == null) {
                                finalAward.setCouponAwardDto(indexAwards.getCouponAwardDto());
                                indexAwards.setCouponAwardDto(null);
                            } else {
                                //两个奖励都有券，无法合并
                                saveItem = true;
                            }
                        }
                        if (indexAwards.getWearAwardDto() != null) {
                            if (finalAward.getWearAwardDto() == null) {
                                finalAward.setWearAwardDto(indexAwards.getWearAwardDto());
                                indexAwards.setWearAwardDto(null);
                            } else {
                                //两个奖励都有衣服，无法合并
                                saveItem = true;
                            }
                        }
                        if (saveItem) {
                            newLists.add(indexAwards);
                        }
                    }
                    awardListDto.getAwards().clear();
                    awardListDto.getAwards().addAll(newLists);
                }
            }
        }

        //是否有跑步记录
        myRaceDto.setHasRunRecord(0);
        List<ZnsUserRunDataDetailsEntity> entityList = userRunDataDetailsService.getUserDetailByActivityId(new Page(1, 1), loginUser.getId(), mainActivityId);
        if (!CollectionUtils.isEmpty(entityList)) {
            myRaceDto.setHasRunRecord(1);
        }
        //判断是否是俱乐部用户赛
        List<MainRunActivityRelationDo> relationDoList = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(mainActivityId).build());
        if (!CollectionUtils.isEmpty(relationDoList)) {
            List<Long> activityIds = relationDoList.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
            List<ZnsUserRunDataDetailsEntity> detailsEntityList = userRunDataDetailsService.findListByQuery(new UserRunDataDetailsQuery().setActivityIds(activityIds).setUserId(loginUser.getId()));
            if (!CollectionUtils.isEmpty(detailsEntityList)) myRaceDto.setHasRunRecord(1);
        }
        //获得奖励
        myRaceDto.setCurrency(accountService.getCurrency(loginUser.getId(), mainActivityId, true));
        BigDecimal acquireDAmount = accountDetailService.sumAward(mainActivityId, loginUser.getId(),
                Arrays.asList(AccountDetailTypeEnum.NEW_ACTIVITY_100.getType()), 2);
        myRaceDto.setAcquiredAmount(acquireDAmount);
        Integer score = activityUserScoreService.sumScore(mainActivityId, loginUser.getId(), ScoreConstant.SourceTypeEnum.mainActivitySourceList(), 1);
        myRaceDto.setAcquiredScore(score);
        myRaceDto.setIsSendAward(mainActivity.isFinishAndAwardSend() ? 1 : 0);
        myRaceDto.setExpectSendAwardDate(
                AwardSendStatusEnum.NO_VIEW_CONFIG.getCode().equals(mainActivity.getAwardSendStatus()) ?
                        mainActivity.activityEndDateZonedDateTime() :
                        mainActivity.activityEndDateZonedDateTime().plusDays(3));
        return myRaceDto;
    }

    private MyTeamActRecordDto getMyTeamActRecordDto(MainActivity mainActivity, Long userId) {
        MyTeamActRecordDto teamRecordDto = new MyTeamActRecordDto();
        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivity.getId(), userId);

        ActivityTeam activityTeam = activityTeamService.selectActivityTeamById(activityUser.getTeamId());

        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());        //0：否，1：是

        TeamEffectiveGrade effectiveGrade = teamEffectiveGradeService.findByActivityAndUser(mainActivity.getId(), userId);

        //todo:现在只有单选
        RankingByEnum rankingByEnum = RankingByEnum.findByType(entryGameplay.getRankingBy());
        teamRecordDto.setRankType(Integer.valueOf(entryGameplay.getRankingBy()));

        Integer targetType = mainActivity.getTargetType();
        teamRecordDto.setTargetType(targetType);
        if (targetType == 0) {
            switch (rankingByEnum) {
                case RUN_TIME:
                    teamRecordDto.setTeamGrade(activityTeam.getRunTime());
                    teamRecordDto.setMyGrade(effectiveGrade.getRunTime());
                    break;
                case RUN_MILEAGE:
                    teamRecordDto.setTeamGrade(activityTeam.getMillage());
                    teamRecordDto.setMyGrade(effectiveGrade.getRunMileage());
                    break;
                case RUN_COUNT:
                    teamRecordDto.setMyGrade((Objects.nonNull(effectiveGrade) && Objects.nonNull(effectiveGrade.getRunCount())) ? effectiveGrade.getRunCount() : 0);
                    teamRecordDto.setTeamGrade(activityTeam.getRunCount());
                    break;
                case REACH_NUMS:
                    teamRecordDto.setMyGrade((Objects.nonNull(effectiveGrade) && Objects.equals(effectiveGrade.getIsReach(), 1)) ? 1 : 0);
                    teamRecordDto.setTeamGrade(activityTeam.getReachNums());
                    break;
            }
        } else if (targetType == 1) {
            teamRecordDto.setTeamGrade(activityTeam.getMillage());
            teamRecordDto.setMyGrade(effectiveGrade.getRunMileage());
        } else if (targetType == 2) {
            teamRecordDto.setTeamGrade(activityTeam.getRunTime());
            teamRecordDto.setMyGrade(effectiveGrade.getRunTime());
        } else if (targetType == 3) {
            teamRecordDto.setTeamGrade(activityTeam.getRunCount());
            teamRecordDto.setMyGrade(effectiveGrade.getRunCount());
        }
        if (ActivityTeamTypeEnum.CLUB.getCode().equals(activityTeam.getTeamType())) {
            clubActivityTeamService.findByTeamId(activityTeam.getId()).ifPresent(i -> {
                teamRecordDto.setClubId(i.getClubId());
            });
        }
        teamRecordDto.setTeamId(activityTeam.getId());
        teamRecordDto.setTeamRank(activityTeam.getRank());
        teamRecordDto.setTeamName(activityTeam.getTeamName());
        teamRecordDto.setTeamLogo(activityTeam.getTeamLogo());
        teamRecordDto.setMaxNum(activityTeam.getMaxNum());
        teamRecordDto.setCurrentNum(activityTeam.getCurrentNum());
        teamRecordDto.setRunTime(activityTeam.getRunTime());
        teamRecordDto.setMillage(activityTeam.getMillage());
        teamRecordDto.setRunCount(activityTeam.getRunCount());
        if (mainActivity.getTargetType() != 0) {
            teamRecordDto.setTeamTarget(subActivityService.getSingleActivityTargets(mainActivity.getId()).get(0));
        }
        if (Objects.equals(rankingByEnum, RankingByEnum.REACH_NUMS)) {
            teamRecordDto.setReachNums(activityTeamManager.getTeamReachNums(teamRecordDto.getTeamId()));
        }
        //团队赛的用户成绩
        List<ZnsRunActivityUserEntity> actUsers = activityUserBizService.getActUsersByTeamId(activityTeam.getId());
        // 审核成绩(活动已经结束)失败用户 再次剔除后进行排序
        removeViewCheatActivityUser(mainActivity, actUsers);

        Map<Long, TeamEffectiveGrade> gradeMap = new HashMap<>();
        //用户召唤状态map
        Map<Long, Integer> userCallStatusMap = getUserCallStatus(activityTeam, userId);
        List<TeamEffectiveGrade> teamEffectiveGrades = teamEffectiveGradeService.findByTeam(activityTeam.getId());
        gradeMap = teamEffectiveGrades.stream().collect(Collectors.toMap(TeamEffectiveGrade::getUserId, Function.identity(), (x, y) -> x));

        final Map<Long, TeamEffectiveGrade> finalGradeMap = gradeMap;


        List<TeamUserGradeDto> gradeDtos = actUsers.stream()
                .map(k -> {
                    TeamUserGradeDto teamUserGradeDto = new TeamUserGradeDto();
                    ZnsUserEntity user = userService.findById(k.getUserId());
                    teamUserGradeDto.setUserId(k.getUserId());
                    teamUserGradeDto.setUsername(user.getFirstName());
                    teamUserGradeDto.setAvatar(user.getHeadPortrait());
                    TeamEffectiveGrade grade = finalGradeMap.get(k.getUserId());
                    if (targetType == 0) {
                        teamUserGradeDto.setRunTime(k.getRunTime());
                        teamUserGradeDto.setMillage(k.getRunMileage().intValue());
                        teamUserGradeDto.setRunCount((Objects.nonNull(grade) && Objects.nonNull(grade.getRunCount())) ? grade.getRunCount() : 0);
                        teamUserGradeDto.setIsReach(grade != null ? Objects.equals(grade.getIsReach(), 1) : Boolean.FALSE);
                    } else if (Objects.equals(targetType, 1)) {
                        teamUserGradeDto.setRunTime(grade != null ? grade.getRunTime() : 0);
                        teamUserGradeDto.setMillage(grade != null ? grade.getRunMileage() : 0);
                    } else {
                        teamUserGradeDto.setRunTime(grade != null ? grade.getRunTime() : 0);
                        teamUserGradeDto.setMillage(grade != null ? grade.getRunMileage() : 0);
                        teamUserGradeDto.setRunCount((Objects.nonNull(grade) && Objects.nonNull(grade.getRunCount())) ? grade.getRunCount() : 0);
                    }
                    //召唤状态
                    teamUserGradeDto.setCallStatus(userCallStatusMap.get(k.getUserId()));
                    return teamUserGradeDto;

                }).toList();
        //排序
        Stream<TeamUserGradeDto> stream = gradeDtos.stream();

        //0：无，1：里程，2：时长
        if (targetType == 0) {
            //1：里程，2：时长
            stream = switch (rankingByEnum) {
                case RUN_MILEAGE -> stream.sorted(Comparator.comparing(TeamUserGradeDto::getMillage).reversed());
                case RUN_TIME -> stream.sorted(Comparator.comparing(TeamUserGradeDto::getRunTime).reversed());
                case RUN_COUNT -> stream.sorted(Comparator.comparing(TeamUserGradeDto::getRunCount).reversed());
                case REACH_NUMS -> stream.sorted(Comparator.comparing(TeamUserGradeDto::getIsReach).reversed());
                default -> stream;
            };
        } else if (targetType == 1) {
            stream = stream.sorted(Comparator.comparing(TeamUserGradeDto::getMillage).reversed());
        } else if (targetType == 2) {
            stream = stream.sorted(Comparator.comparing(TeamUserGradeDto::getRunTime).reversed());
        } else if (targetType == 3) {
            stream = stream.sorted(Comparator.comparing(TeamUserGradeDto::getRunCount).reversed());
        }
        teamRecordDto.setUserGrades(stream.collect(Collectors.toList()));
        return teamRecordDto;
    }

    private Map<Long, Integer> getUserCallStatus(ActivityTeam team, Long userId) {

        ZonedDateTime now = ZonedDateTime.now();
        List<Long> activityUsers = activityUserBizService.getActUsersByTeamId(team.getId())
                .stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        Map<Long, Integer> userStatusMap = activityUsers.stream().collect(Collectors.toMap(Long::longValue, k -> 1, (x, y) -> y));

        //排除自己
        userStatusMap.put(userId, 1);

        MainActivity mainActivity = mainActivityService.findById(team.getActivityId());
        if (MainActivityStateEnum.STARTED.getCode().equals(mainActivity.getActivityState())) {
            if (team.getCompleteTime() == null) {
                activityUsers.remove(userId);
                //今日已召唤
                List<Long> todayCallRecord = teamCallRecordService.getTodayCallRecord(team.getId(), null, null)
                        .stream().map(TeamCallRecord::getSendUserId).collect(Collectors.toList());
                for (Long uid : todayCallRecord) {
                    userStatusMap.put(uid, 2);
                    activityUsers.remove(uid);
                }
                for (Long uid : activityUsers) {
                    ZnsUserRunDataDetailsEntity runDataDetail = runDataDetailsService.getLatestUserActivityRunDataDetails(uid, team.getActivityId());
                    if (runDataDetail == null || runDataDetail.getModifieTime().isBefore(DateUtil.startOfDate(now))) {
                        userStatusMap.put(uid, 0);
                    }
                }
            }
        }

        log.info("userStatusMap {}", userStatusMap);
        return userStatusMap;
    }

    private Integer getIsLightenCityPopup(ActivityDisseminate disseminate, ZnsRunActivityUserEntity activityUser, ZnsUserEntity loginUser) {
        if (Objects.isNull(disseminate) || !StringUtils.hasText(disseminate.getLightCityPics())) {
            return 0;
        }
        List<LightCityPicsDto> lightCityPicsDtos = JsonUtil.readList(disseminate.getLightCityPics(), LightCityPicsDto.class);
        if (activityUser.getCompletedLevel() > lightCityPicsDtos.size()) {
            return 0;
        }
        LightCityPicsDto dto = lightCityPicsDtos.get(activityUser.getCompletedLevel() - 1);
        if (Objects.isNull(dto) || !StringUtils.hasText(dto.getPic())) {
            return 0;
        }
        MilestonePop milestonePop = milestonePopDao.selectOne(new QueryWrapper<MilestonePop>().lambda().eq(MilestonePop::getActivityId, activityUser.getActivityId()).eq(MilestonePop::getUserId, loginUser.getId()).eq(MilestonePop::getMileageNode, activityUser.getCompletedLevel()).last("limit 1"));
        if (Objects.isNull(milestonePop)) {
            return 1;
        }
        return 0;
    }

    public Page<ReportUserDto> reportUsers(ReportUsersRequest request, ZnsUserEntity loginUser) {
        //返回当前活动的参赛用户，可以缓存
        Page<ZnsRunActivityUserEntity> page = new Page<>(request.getPageNum(), request.getPageSize());
        if (Objects.equals(request.getPageNum(), 1)) {
            String cacheKey = String.format("reportUsers_activityId:%s_status:%s_pageSize:%s", request.getMainActivityId(), 5, request.getPageSize());
            String cacheTotalKey = String.format("total_reportUsers_activityId:%s_status:%s_pageSize:%s", request.getMainActivityId(), 5, request.getPageSize());
            //缓存1分钟，分页
            RList<ZnsRunActivityUserEntity> cachedList = redissonClient.getList(cacheKey);
            RBucket<Long> cachedCount = redissonClient.getBucket(cacheTotalKey);
            if (CollectionUtils.isEmpty(cachedList)) {
                String lockKey = String.format("reportUsers_activityId:%s_status:%s_lock", request.getMainActivityId(), 5);
                //防止缓存击穿，造成高并发下大量请求落库
                RLock lock = redissonClient.getLock(lockKey);
                try {
                    if (lock.tryLock(10, TimeUnit.SECONDS)) {
                        if (CollectionUtils.isEmpty(cachedList)) {

                            RunActivityUserPageQuery userPageQuery = RunActivityUserPageQuery.builder()
                                    .activityId(request.getMainActivityId())
                                    .userStateNotIn(Arrays.asList(5))
                                    .build();
                            userPageQuery.setPageNum((int) page.getCurrent());
                            userPageQuery.setPageSize((int) page.getSize());
                            Page<ZnsRunActivityUserEntity> pageResult = activityUserService.findPage(userPageQuery);
                            page = pageResult;
                            cachedList.addAll(page.getRecords());
                            cachedList.expire(1, TimeUnit.MINUTES);

                            cachedCount.set(pageResult.getTotal());
                            cachedCount.expire(1, TimeUnit.MINUTES);
                        } else {
                            log.info("已命中缓存");
                        }
                    }
                } catch (InterruptedException e) {
                    log.info("获取锁中断，lockKey={}", lockKey);
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        log.info("释放命中缓存，lockKey={}", lockKey);
                        lock.unlock();
                    } else {
                        log.info("未获得锁，lockKey={}", lockKey);
                    }
                }
            } else {
                log.info("命中缓存，cacheKey={}", cacheKey);
            }
            page.setRecords(cachedList);
            //fix NPE
            page.setTotal(Objects.isNull(cachedCount.get()) ? 0 : cachedCount.get());
        } else {
            //写法不规范
            RunActivityUserPageQuery userPageQuery = RunActivityUserPageQuery.builder()
                    .activityId(request.getMainActivityId())
                    .userStateNotIn(Arrays.asList(5))
                    .build();
            userPageQuery.setPageSize((int) page.getSize());
            userPageQuery.setPageNum((int) page.getCurrent());
            page = activityUserService.findPage(userPageQuery);
        }

        List<ReportUserDto> reportUserDtos = page.getRecords().stream().filter(Objects::nonNull).map(k -> {
            ReportUserDto reportUserDto = new ReportUserDto();
            ZnsUserEntity user = userService.findById(k.getUserId());
            if (user == null) {
                user = new ZnsUserEntity();
            }
            reportUserDto.setUserId(user.getId());
            reportUserDto.setName(user.getFirstName());
            reportUserDto.setAvatar(user.getHeadPortrait());
            reportUserDto.setIsPrivacy(user.getIsPrivacy());
            Integer relationType = friendService.getRelationType(loginUser.getId(), k.getUserId());
            reportUserDto.setIsFriend(relationType == 1 ? 1 : 0);
            reportUserDto.setIsFollow(relationType > 0);
            if (Objects.equals(loginUser.getId(), user.getId())) {
                // 本人不展示关注按钮
                reportUserDto.setIsFollow(null);
            }
            return reportUserDto;
        }).toList();

        Page<ReportUserDto> resPage = new Page<>();
        resPage.setTotal(page.getTotal());
        resPage.setCurrent(page.getCurrent());
        resPage.setSize(page.getSize());
        resPage.setRecords(reportUserDtos);
        return resPage;

    }

    public List<HistoryRewardDto> historyReward(Long mainActivityId, ZnsUserEntity loginUser) {
        List<HistoryRewardDto> historyRewardDtos = new ArrayList<>();
        List<Long> activityIdList = new ArrayList<>();
        activityIdList.add(mainActivityId);
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            List<Long> subActivityId = seriesActivityRelService.findSubActivityId(mainActivityId);
            if (!CollectionUtils.isEmpty(subActivityId)) {
                activityIdList.addAll(subActivityId);
            }
        }

        List<ZnsRunActivityUserEntity> allActivityUser = activityUserService.findActivityUsers(activityIdList, null);
        if (CollectionUtils.isEmpty(allActivityUser)) {
            return historyRewardDtos;
        }
        List<MainRunActivityRelationDo> clubActivityList = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(mainActivityId).build());
        Map<Long, List<ZnsRunActivityUserEntity>> activityUserMap = allActivityUser.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getUserId));
        List<ActivityUserAwardVo> userAwardList = activityUserAwardBizService.findUserAward(activityIdList);
        for (ActivityUserAwardVo vo : userAwardList) {
            HistoryRewardDto rewardDto = new HistoryRewardDto();
            rewardDto.setUserId(vo.getUserId()).setAvatar(vo.getHeadPortrait()).setName(vo.getNickname()).setAmount(vo.getAward()).setCurrency(vo.getCurrency());
            List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = activityUserMap.get(vo.getUserId());
            if (!CollectionUtils.isEmpty(znsRunActivityUserEntities)) {
                ZnsRunActivityUserEntity activityUser = null;
                if (znsRunActivityUserEntities.size() == 1) {
                    activityUser = znsRunActivityUserEntities.get(0);
                } else {
                    activityUser = znsRunActivityUserEntities.stream().filter(a -> a.getActivityId().equals(vo.getActivityId())).findFirst().orElse(null);
                }

                if (Objects.nonNull(activityUser)) {
                    if (Objects.nonNull(vo.getRunDataDetailsId()) && vo.getRunDataDetailsId() > 0) {
                        ZnsUserRunDataDetailsEntity dataDetails = userRunDataDetailsService.findById(vo.getRunDataDetailsId());
                        rewardDto.setDistance(dataDetails.getRunMileage().intValue());
                        rewardDto.setRunTime(dataDetails.getRunTime());
                    } else {
                        rewardDto.setDistance(activityUser.getRunMileage().intValue());
                        rewardDto.setRunTime(activityUser.getRunTime());
                    }
                    rewardDto.setTargetType(mainActivity.getTargetType());
                }
            }
            if (!CollectionUtils.isEmpty(clubActivityList)) {
                //表示俱乐部用户赛
                List<Long> runActivityList = clubActivityList.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
                Long playId = mainActivity.getPlayId();
                EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(playId);
                RunActivityUserQuery.RunActivityUserQueryBuilder builder = RunActivityUserQuery.builder().activityIds(runActivityList).userId(vo.getUserId()).isCheat(0);
                if (Objects.nonNull(entryGameplay) && (Objects.equals(entryGameplay.getRankingBy(), RankingByEnum.RUN_COUNT.getType()) || Objects.equals(entryGameplay.getRankingBy(), RankingByEnum.REACH_NUMS.getType()))) {
                    builder.isComplete(1);
                }
                List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUserByQuery(builder.build());
                rewardDto.setDistance(activityUsers.stream().map(ZnsRunActivityUserEntity::getRunMileage).reduce(BigDecimal.ZERO, BigDecimal::add).intValue());
                rewardDto.setRunTime(activityUsers.stream().mapToInt(ZnsRunActivityUserEntity::getRunTime).sum());
                rewardDto.setRunCount(activityUsers.size());
            }
            rewardDto.setDate(vo.getCreateTime().toInstant().toEpochMilli());
            historyRewardDtos.add(rewardDto);
        }
        historyRewardDtos.sort(Comparator.comparing(HistoryRewardDto::getDate).reversed());
        return historyRewardDtos;
    }

    @FillerMethod
    public List<RankDto> singleRank(Long mainActivityId, ZnsUserEntity loginUser) {
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);

        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivityId, loginUser.getLanguageCode());

        //活动未结束开启排行榜 0否 1是 默认0
        if (Objects.nonNull(disseminate) && Objects.equals(disseminate.getEnableRankBeforeEnd(), 0)
                && !MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())) {
            return null;
        }
        Map<Long, BigDecimal> userGainAmount = activityUserAwardBizService.sumUserActivityGainsAmount(mainActivityId);

        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        Integer competitionFormat = entryGameplay.getCompetitionFormat();
        //todo:现在只有单选
        String rankingBy = entryGameplay.getRankingBy();
        Integer rankType = Integer.valueOf(rankingBy);
        if (rankType == 0) {
            return null;
        }
        if (competitionFormat == 0) {
            Integer isAllowChanllenge = entryGameplay.getIsAllowChanllenge();

            List<ZnsRunActivityUserEntity> allActivityUser = activityUserService.findAllActivityUser(mainActivityId)
                    .stream().filter(k -> k.getIsComplete() == 1).collect(Collectors.toList());
            //审核成绩(活动已经结束)失败用户 再次剔除后进行排序
            removeViewCheatActivityUser(mainActivity, allActivityUser);
            // 里程排
            if (rankType == 1) {
                allActivityUser = allActivityUser.stream().
                        sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed())
                        .limit(20)
                        .toList();

            } else {
                //处理使用道具
                for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
                    if (activityUser.getPropRunTime() == null) {
                        activityUser.setPropRunTime(activityUser.getRunTimeMillisecond());
                    }
                }
                allActivityUser = allActivityUser.stream()
                        .sorted(Comparator.comparing(ZnsRunActivityUserEntity::getPropRunTime))
                        .limit(20)
                        .toList();

            }
            //排名并处理是否可挑战
            Boolean canChanllenge = true;
            List<RankDto> rankDtos = allActivityUser.stream().map(k -> convertToRankDto(k, loginUser, mainActivity.getTargetType())).toList();
            Integer activityState = mainActivity.getActivityState();
            if (mainActivity.getTimeStyle() == 1) {
                activityState = mainActivityService.getActivityState(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), loginUser.getZoneId()),
                        DateUtil.getStampByZone(mainActivity.getActivityEndTime(), loginUser.getZoneId()));
            }
            if (activityState == 2) {
                canChanllenge = false;
            }

            List<Long> followingIdList = new ArrayList<>();
            Map<Long, ZnsUserEntity> userMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(rankDtos)) {
                List<Long> friendIds = rankDtos.stream().map(RankDto::getUserId).collect(Collectors.toList());
                followingIdList = friendService.getFollowingIdList(loginUser.getId(), friendIds);
                userMap = userService.findByIds(friendIds).stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
            }
            Integer rank = 1;
            for (int i = 0; i < allActivityUser.size(); i++) {

                RankDto rankDto = rankDtos.get(i);
                rankDto.setRankType(rankType);
                ZnsUserEntity user = userMap.get(rankDto.getUserId());
                if (Objects.nonNull(user)) {
                    rankDto.setIsPrivacy(user.getIsPrivacy());
                }
                rankDto.setIsFollow(followingIdList.contains(rankDto.getUserId()));
                if (rankDto.getUserId().equals(loginUser.getId())) {
                    canChanllenge = false;
                    rankDto.setIsFollow(null);
                }
                if (i > 0 && !isSameGrade(allActivityUser.get(i), allActivityUser.get(i - 1), rankType)) {
                    rank = i + 1;
                }
                rankDto.setRank(rank);
                if (isAllowChanllenge == 1 && canChanllenge) {
                    rankDto.setIsCanChallenged(1);
                }
            }
            //包装用户设备
            packageEquipment(mainActivityId, rankDtos);
            return rankDtos.stream()
                    .peek(item -> {
                        item.setBonus(userGainAmount.getOrDefault(item.getUserId(), BigDecimal.ZERO));
                    })
                    .toList();
        } else if (competitionFormat == 1) {
            //团队赛
            List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivityId);
            List<RankDto> list = teams.stream().filter(t -> {
                        if (t.getMillage() <= 0) return false;
                        if (rankType == 5) return t.getRunCount() > 0;
                        if (rankType == 6) return t.getReachNums() > 0;
                        return true;
                    })
                    .sorted(Comparator.comparing(ActivityTeam::getRank)).map(t -> {
                        RankDto rankDto = new RankDto();
                        rankDto.setTeamId(t.getId());
                        rankDto.setTeamType(t.getTeamType());
                        rankDto.setName(t.getTeamName());
                        rankDto.setAvatar(t.getTeamLogo());
                        rankDto.setDistance(t.getMillage());
                        rankDto.setConsumeTime(t.getRunTime());
                        rankDto.setCurrentNum(t.getCurrentNum());
                        rankDto.setMaxNum(t.getMaxNum());
                        rankDto.setRank(t.getRank());
                        rankDto.setRankType(rankType);
                        rankDto.setRunCount(t.getRunCount());
                        rankDto.setReachNums(t.getReachNums());
                        return rankDto;
                    }).toList();
            List<Long> clubTeamIds = list.stream().filter(a -> ActivityTeamTypeEnum.CLUB.getCode().equals(a.getTeamType())).map(RankDto::getTeamId).toList();
            //club id 查询
            if (!CollectionUtils.isEmpty(clubTeamIds)) {
                List<ClubActivityTeam> byTeamIds = clubActivityTeamService.findByTeamIds(clubTeamIds);
                if (!CollectionUtils.isEmpty(byTeamIds)) {
                    list.stream().filter(a -> ActivityTeamTypeEnum.CLUB.getCode().equals(a.getTeamType()))
                            .forEach(b -> {
                                byTeamIds.stream().filter(c -> c.getActivityTeamId().equals(b.getTeamId())).findAny().ifPresent(c -> {
                                    b.setClubId(c.getClubId());
                                });
                            });
                }

            }

            //马拉松扩展
            if (sysConfigService.isMarathon(mainActivityId)) {
                extendMarathonRankDto(list, mainActivity);

            }
            return list;

        }

        return null;

    }

    private void extendMarathonRankDto(List<RankDto> list, MainActivity mainActivity) {
        if (CollectionUtils.isEmpty(list) || mainActivity == null) {
            return;
        }

        // 获取目标里程
        Integer targetMileage = subActivityService.getAllSingleActByMain(mainActivity.getId()).get(0).getTarget();
        if (targetMileage == null || targetMileage == 0) {
            return;
        }

        BigDecimal target = new BigDecimal(targetMileage);

        // 计算每个用户的进度
        for (RankDto rankDto : list) {
            if (rankDto.getDistance() != null) {
                // 计算进度百分比 = (用户里程 / 目标里程) * 100
                BigDecimal progress = new BigDecimal(rankDto.getDistance())
                        .multiply(new BigDecimal("100"))
                        .divide(target, 0, BigDecimal.ROUND_DOWN); // 向下取整

                // 确保进度不超过100%
                progress = progress.min(new BigDecimal("100"));
                rankDto.setProgress(progress.toString());
            } else {
                rankDto.setProgress(BigDecimal.ZERO.toString());
            }
        }
    }

    private void packageEquipment(Long mainActivityId, List<RankDto> rankDtos) {
        for (RankDto rankDto : rankDtos) {
            EquipmentInfoDto equipmentInfoDto = queryEquipmentInfo(mainActivityId, rankDto.getUserId());
            if (equipmentInfoDto != null) {
                rankDto.setEquipmentModel(equipmentInfoDto.getEquipmentModel());
            }
        }
    }

    private void packageEquipment(Long mainActivityId, SeriesActivityRankResponse response) {
        Page<SeriesActivityRankDto> ranks = response.getRanks();
        for (SeriesActivityRankDto record : ranks.getRecords()) {
            EquipmentInfoDto equipmentInfoDto = queryEquipmentInfo(mainActivityId, record.getUserId());
            if (equipmentInfoDto != null) {
                record.setEquipmentModel(equipmentInfoDto.getEquipmentModel());
            }
        }
        SeriesActivityRankDto myRank = response.getMyRank();
        if (myRank != null) {
            EquipmentInfoDto equipmentInfoDto = queryEquipmentInfo(mainActivityId, myRank.getUserId());
            if (equipmentInfoDto != null) {
                myRank.setEquipmentModel(equipmentInfoDto.getEquipmentModel());
            }
        }

    }


    private boolean isSameGrade(ZnsRunActivityUserEntity activityUserA, ZnsRunActivityUserEntity activityUserB, Integer rankType) {
        // 里程排
        if (rankType == 1) {
            if (activityUserA.getRunMileage().compareTo(activityUserB.getRunMileage()) == 0) {
                return true;
            }
        } else {
            if (activityUserA.getPropRunTime() - activityUserB.getPropRunTime() == 0) {
                return true;
            }
            return false;

        }


        return false;
    }

    private RankDto convertToRankDto(ZnsRunActivityUserEntity activityUser, ZnsUserEntity loginUser, Integer targetType) {
        RankDto rankDto = new RankDto();
        ZnsUserEntity user = userService.findByIdWithoutLogicDelete(activityUser.getUserId());
        rankDto.setName(user.getFirstName());
        rankDto.setAvatar(user.getHeadPortrait());
        rankDto.setDistance(activityUser.getRunMileage().intValue());
        rankDto.setUserId(user.getId());
        if (activityUser.getPropRunTime() != null) {
            rankDto.setConsumeTime(activityUser.getPropRunTime() / 1000);
        } else {
            rankDto.setConsumeTime(activityUser.getRunTime());
        }

        Integer relationType = friendService.getRelationType(loginUser.getId(), activityUser.getUserId());
        rankDto.setIsFriend(relationType == 1 ? 1 : 0);
        rankDto.setTargetType(targetType);
        return rankDto;


    }

    public List<MyActRecordDto> MyActRecordDto(Page page, Long mainActivityId, ZnsUserEntity loginUser) {
        Long userId = loginUser.getId();
        List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(mainActivityId).build());
        List<ZnsUserRunDataDetailsEntity> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            result = runDataDetailsService.getUserDetailByActivityId(page, userId, mainActivityId);
        } else {
            UserRunDataDetailsQuery userRunDataDetailsQuery = new UserRunDataDetailsQuery();
            userRunDataDetailsQuery.setActivityIds(list.stream().map(MainRunActivityRelationDo::getRunActivityId).toList()).setUserId(userId);
            result = runDataDetailsService.findListByQuery(userRunDataDetailsQuery);

        }
        return result.stream().sorted(Comparator.comparing(ZnsUserRunDataDetailsEntity::getId).reversed()).map(k -> {
            MyActRecordDto dto = new MyActRecordDto();
            dto.setDetailId(k.getId());
            dto.setMillage(k.getRunMileage().intValue());
            dto.setRunTime(k.getRunTime());
            dto.setPace(k.getAveragePace());
            dto.setDate(k.getCreateTime().toInstant().toEpochMilli());
            dto.setIsCheat(k.getIsCheat());
            return dto;
        }).toList();
    }

    public void signupPreCheck(Long mainActivityId, Long teamId, ZnsUserEntity loginUser, Integer appVersion) {
        Boolean marathon = sysConfigService.isMarathon(mainActivityId);
        Long userId = loginUser.getId();
        log.info("user:{},signupPreCheck ,activityId:{}", userId, mainActivityId);
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);

        //活动是否下架
        if (MainActivityStateEnum.OFF_SHELF.getCode().equals(mainActivity.getActivityState())) {
            throw new BaseException(I18nMsgUtils.getMessage("activity.offline"));
        }

        //是否已报名
        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivityId, userId);
        if (activityUser != null) {
            throw new BaseException(I18nMsgUtils.getMessage("activity.signup.misCondition"));
        }
        // todo 活动参赛人数限制 缺少团队赛

        //判断是否参与互斥活动
        String mutexActivityIds = mainActivity.getMutexActivityIds();
        if (StringUtils.hasText(mutexActivityIds)) {
            List<Long> mutexIds = JsonUtil.readList(mutexActivityIds, Long.class);
            for (Long mutexActId : mutexIds) {
                ZnsRunActivityUserEntity mutexActivityUser = activityUserService.findActivityUser(mutexActId, userId);
                if (mutexActivityUser != null) {
                    ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mutexActivityUser.getActivityId(), loginUser.getLanguageCode());
                    throw new BaseException(I18nMsgUtils.getMessage("activity.signup.count.exceed", disseminate.getTitle()));
                }
            }
        }


        if (loginUser.getIsRobot() == 0) {
            ActivityBrandRightsInterests enjoyBenefits = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.REGISTRATION_FEE.getStatusCode(), mainActivityId, loginUser.getId());

            activityVerificationBizService.areaAndGroupCheck(loginUser, mainActivityId);

            //外卡竞技分不校验积分
            CompetitiveShortlistQuery query = new CompetitiveShortlistQuery();
            query.setActivityId(mainActivityId).setUserId(loginUser.getId());
            CompetitiveShortlistDo shortlistDo = competitiveShortlistService.findByQuery(query);
            if (shortlistDo == null) {
                //积分校验
                boolean checkPayActivity = payActivityBizService.checkPayActivity(mainActivity, loginUser);
                if (!checkPayActivity && Objects.isNull(enjoyBenefits)) {
                    throw new BaseException(I18nMsgUtils.getMessage("activity.racePoint.insufficient"));
                }
            }

        }


        //是否在报名时间
        Long startByZone = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : loginUser.getZoneId());
        Long entByZone = DateUtil.getStampByZone(mainActivity.getApplicationEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : loginUser.getZoneId());

        if (System.currentTimeMillis() < startByZone || System.currentTimeMillis() > entByZone) {
            throw new BaseException(I18nMsgUtils.getMessage("activity.signup.timeNoStart"));
        }
        if (Objects.nonNull(teamId) && teamId > 0) {
            //团队赛检查队伍是否已满
            ActivityTeam team = activityTeamService.findById(teamId);
            if (Objects.isNull(team)) {
                throw new BaseException(I18nMsgUtils.getMessage("activity.join.failed"));
            }

            if (team.getMaxNum() > 0 && team.getMaxNum() <= team.getCurrentNum()) {
                throw new BaseException(I18nMsgUtils.getMessage("activity.apply.num.exceed"));
            }
        } else {
            if (!marathon) {
                //是否达到人数上线
                Integer enterLimit = mainActivity.getEnterLimit();
                long count = activityUserService.findAllActivityUser(mainActivityId).stream().count();
                if (enterLimit != -1 && count >= enterLimit) {
                    throw new BaseException(I18nMsgUtils.getMessage("activity.enroll.count.full"));
                }
            }

        }


        //剩余报名次数
        Integer signUpLimit = getSignUpLimit(loginUser, mainActivityId);
        if (Objects.nonNull(signUpLimit) && signUpLimit == 0) {
            throw new BaseException(I18nMsgUtils.getMessage(I18nMsgUtils.getMessage("activity.sign.up.limit")));
        }
        activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.GENDER_LIMIT).ifPresent(
                k -> {
                    String limitGender = k.getParamValue();
                    if (!limitGender.equals("0")) {
                        if (!limitGender.equals(loginUser.getGender().toString())) {
                            if (limitGender.equals("1")) {
                                throw new BizI18nException(I18nMsgUtils.getMessage("activity_gender_limit_male"));
                            } else {
                                throw new BizI18nException(I18nMsgUtils.getMessage("activity_gender_limit_female"));

                            }
                        }
                    }
                });
        //用户的积分是否足够 特殊品牌权益

        //考虑系列赛是否达到一下关开启条件

        //鲸鱼需求


    }


    public List<CompetitionScheduleDto> competitionSchedule(SingleActivityIdRequest request, ZnsUserEntity loginUser) {
        List<CompetitionScheduleDto> dtoList = new ArrayList<>();
        //目标
        List<Integer> goals = subActivityService.getAllSingleActByMain(request.getMainActivityId()).stream().map(SubActivity::getTarget)
                .sorted().toList();
        MainActivity activity = mainActivityService.findById(request.getMainActivityId());
        //查询聚合活动
        List<MainActivity> mainActivityList = new ArrayList<>();
        //查询聚合关系
        List<Long> sameBatchAct = activityPolymerizationRecordService.getSameBatchAct(request.getMainActivityId());
        if (Objects.equals(request.getIsPolyList(), 1)) {
            //模版id捞活动
            sameBatchAct = activityPolymerizationRecordService.getSameBatchActByParentId(request.getMainActivityId());
        }
        if (CollectionUtils.isEmpty(sameBatchAct)) {
            return dtoList;
        }

        //已结束两场DateUtils.getCurrentTime(timeZone)
        List<MainActivity> endActivityList = mainActivityService.findEndActivityByIds(new Page<>(1, 2), DateUtil.getCurrentTime(TimeZone.getTimeZone(loginUser.getZoneId())), sameBatchAct, Arrays.asList(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()));
        if (!CollectionUtils.isEmpty(endActivityList)) {
            endActivityList = endActivityList.stream().sorted(Comparator.comparing(MainActivity::getActivityStartTime)).collect(Collectors.toList());
            mainActivityList.addAll(endActivityList);
        }
        //查询进行中或未开始
        List<MainActivity> noEndActivityList = mainActivityService.findAllNoEndActivity(new Page<>(1, 999), DateUtil.getCurrentTime(TimeZone.getTimeZone(loginUser.getZoneId())), sameBatchAct, Arrays.asList(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()), "", "", null, false, false);
        if (!CollectionUtils.isEmpty(noEndActivityList)) {
            mainActivityList.addAll(noEndActivityList);
        }
        if (CollectionUtils.isEmpty(mainActivityList)) {
            return dtoList;
        }
        mainActivityList.sort(Comparator.comparing(MainActivity::getActivityStartTime));

        List<Long> activityIds = mainActivityList.stream().map(MainActivity::getId).collect(Collectors.toList());
        mainActivityList = mainActivityList.stream().sorted(Comparator.comparing(MainActivity::getActivityStartTime)).toList();
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, null);
        Map<Long, List<ZnsRunActivityUserEntity>> activityUserMap = null;
        Map<Long, ZnsUserAccountEntity> accountMap = null;
        if (!CollectionUtils.isEmpty(activityUsers)) {
            activityUserMap = activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getActivityId));
            List<Long> userIds = activityUsers.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
            List<ZnsUserAccountEntity> userAccountList = accountService.selectByUserIdList(userIds);
            accountMap = userAccountList.stream().collect(Collectors.toMap(ZnsUserAccountEntity::getUserId, Function.identity(), (x, y) -> x));
        }
        CompetitionScheduleDto competitionScheduleDto = new CompetitionScheduleDto();
        List<CompetitionScheduleDetailDto> detailDtoList = new ArrayList<>();
        for (int i = 0; i < mainActivityList.size(); i++) {
            MainActivity mainActivity = mainActivityList.get(i);

            CompetitionScheduleDetailDto detailDto = new CompetitionScheduleDetailDto();
            detailDto.setActivityId(mainActivity.getId());
            if (Objects.equals(mainActivity.getTimeStyle(), 0)) {
                detailDto.setStartTime(DateTimeUtil.parse(mainActivity.getActivityStartTime()).toInstant().toEpochMilli());
                detailDto.setEndTime(DateTimeUtil.parse(mainActivity.getActivityEndTime()).toInstant().toEpochMilli());
                detailDto.setApplicationStartTime(DateTimeUtil.parse(mainActivity.getApplicationStartTime()).toInstant().toEpochMilli());
                detailDto.setApplicationEndTime(DateTimeUtil.parse(mainActivity.getApplicationEndTime()).toInstant().toEpochMilli());
            } else {
                detailDto.setStartTime(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), loginUser.getZoneId()));
                detailDto.setEndTime(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), loginUser.getZoneId()));
                detailDto.setApplicationStartTime(DateUtil.getStampByZone(mainActivity.getApplicationStartTime(), loginUser.getZoneId()));
                detailDto.setApplicationEndTime(DateUtil.getStampByZone(mainActivity.getApplicationEndTime(), loginUser.getZoneId()));
            }
            //活动状态
            Integer activityState = mainActivityService.getActivityState(detailDto.getStartTime(), detailDto.getEndTime());
            detailDto.setActivityState(activityState);
            detailDto.setWatchRoomId(NumberUtils.getGoalImNumber(request.getMainActivityId(), goals, mainActivity.getTargetType(), mainActivity.getTimeStyle(), mainActivity.getWaitTime()));

            if (Objects.nonNull(activityUserMap)) {
                List<ZnsRunActivityUserEntity> list = activityUserMap.get(mainActivity.getId());
                BigDecimal sumAmount = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(list)) {
                    detailDto.setPartNum(list.size());
                    ZnsRunActivityUserEntity activityUser = list.stream().filter(a -> a.getUserId().equals(loginUser.getId())).findFirst().orElse(null);
                    if (Objects.nonNull(activityUser)) {
                        detailDto.setUserState(activityUser.getUserState());
                        //结束统计金额
                        if (activityState == 2 && Objects.nonNull(accountMap)) {
                            ZnsUserAccountEntity userAccount = accountMap.get(detailDto.getActivityId());
                            if (Objects.nonNull(userAccount)) {
                                sumAmount = sumAmount.add(getUsdAmount(userAccount, activityUser.getRunAward()));
                            }
                        }
                    }
                }
                detailDto.setAmount(sumAmount);
                detailDto.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD));
            }

            //如果空设置第一条
            if (Objects.isNull(competitionScheduleDto.getDayTime())) {
                Long startTime = detailDto.getStartTime();
                competitionScheduleDto.setDayTime(startTime);
                detailDtoList = new ArrayList<>();
            }

            if (DateUtil.isSameDay(detailDto.getStartTime(), competitionScheduleDto.getDayTime(), TimeZone.getTimeZone(loginUser.getZoneId()))) {
                detailDtoList.add(detailDto);
            } else {
                competitionScheduleDto.setDetailDtoList(detailDtoList);
                dtoList.add(competitionScheduleDto);
                competitionScheduleDto = new CompetitionScheduleDto();
                Long startTime = detailDto.getStartTime();
                competitionScheduleDto.setDayTime(startTime);
                detailDtoList = new ArrayList<>();
                detailDtoList.add(detailDto);
            }

            if (i == mainActivityList.size() - 1) {
                //活动开始时间排序
                competitionScheduleDto.setDetailDtoList(detailDtoList);
                dtoList.add(competitionScheduleDto);
            }
        }

        return dtoList;
    }

    /**
     * 获取美元
     *
     * @param userAccount
     * @param runAward
     * @return
     */
    private BigDecimal getUsdAmount(ZnsUserAccountEntity userAccount, BigDecimal runAward) {
        if (Objects.isNull(runAward) || runAward.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(userAccount.getCurrencyCode())) {
            return BigDecimal.ZERO;
        }
        ExchangeRateConfigEntity exchangeRateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(userAccount.getCurrencyCode());
        if (Objects.isNull(exchangeRateConfigEntity)) {
            return runAward;
        }
        return runAward.divide(runAward).setScale(2, RoundingMode.HALF_UP);
    }

    @Transactional
    public Result<EnrollActivityResponseDto> enrollActivity(EnrollActivityRequest enrollActivityRequest, ZnsUserEntity loginUser, Integer appVersion, boolean isBatch) {

        if (sysConfigService.isMarathon(enrollActivityRequest.getMainActivityId())) {
            if (appVersion < VersionConstant.V4_6_1) {
                throw new BaseException(I18nMsgUtils.getMessage("marathon_enroll_error"));
            }
        }


        log.info("开始报名：{}，{}，{}", enrollActivityRequest.getMainActivityId(), loginUser.getId(), appVersion);
        try {
            signupPreCheck(enrollActivityRequest.getMainActivityId(), enrollActivityRequest.getTeamId(), loginUser, loginUser.getAppVersion());
            MainActivity mainActivity = mainActivityService.findById(enrollActivityRequest.getMainActivityId());


            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(enrollActivityRequest.getMainActivityId(), loginUser.getId());
            if (Objects.nonNull(activityUser)) {
                return CommonResult.success();
            }
            AtomicReference<Boolean> annualOrSeasonal = new AtomicReference<>(false);
            Optional<CompetitiveSeasonDo> optional = competitiveSeasonService.findByActivityId(enrollActivityRequest.getMainActivityId());
            optional.ifPresent(k -> {
                ActivityCompetitiveSeasonType competitiveSeasonType = k.getCompetitiveSeasonType();
                if (ActivityCompetitiveSeasonType.ANNUAL.equals(competitiveSeasonType) || ActivityCompetitiveSeasonType.SEASONAL.equals(competitiveSeasonType)) {
                    annualOrSeasonal.set(true);
                }
            });
            Result payActivity = payActivityBizService.payActivity(new PayActivityDto().setActivity(mainActivity).setUser(loginUser).setPassword(enrollActivityRequest.getPassword()).setUserCouponId(enrollActivityRequest.getUserCouponId()), appVersion, annualOrSeasonal.get(), isBatch);
            if (Objects.nonNull(payActivity)) {
                return payActivity;
            }

            Integer teamRole = 0;
            //团队赛报名处理
            if (entryGameplay.getCompetitionFormat() == 1) {
                teamRole = enrollActivityTeam(enrollActivityRequest, loginUser);
            }
            Integer target = null;
            Integer targetType = mainActivity.getTargetType();
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                List<SubActivity> subActivityList = seriesActivityRelService.getAllSubActivity(mainActivity.getId());

                target = subActivityList.stream().map(SubActivity::getTarget).reduce(0, Integer::sum);
                //计算系列赛的targetType
                List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
                if (!CollectionUtils.isEmpty(allMainActivity)) {
                    targetType = allMainActivity.get(0).getTargetType();
                }
            } else {
                target = enrollActivityRequest.getRunningGoals();
            }
            //添加用户
            runActivityUserService.addMainActivityUser(new AddMainActivityUserDto().setUser(loginUser)
                    .setActivityId(enrollActivityRequest.getMainActivityId())
                    .setActivityType(MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType()) ? RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType() : RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType())
                    .setTeamId(enrollActivityRequest.getTeamId())
                    .setIsVipFree((Objects.equals(loginUser.getMemberType(), 1)) ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode())
                    .setTarget(target).setTeamRole(teamRole)
                    .setTargetType(targetType).setTaskId(enrollActivityRequest.getTaskId())
            );
            //记录报名相关的额外信息
            runActivityUserExtraService.saveActivityUserState(loginUser, enrollActivityRequest.getMainActivityId(), enrollActivityRequest.getAllowActivityPush());
            if (annualOrSeasonal.get()) {
                competitiveShortlistService.report(enrollActivityRequest.getMainActivityId(), loginUser);
            }
            //给有参赛包的活动发放奖励
            sendApplicationAward(loginUser, mainActivity);
            //报名成功，处理俱乐部相关操作
            if (enrollActivityRequest.getTeamId() != null) {
                ActivityTeam activityTeam = activityTeamService.findById(enrollActivityRequest.getTeamId());
                if (activityTeam != null && ActivityTeamTypeEnum.CLUB.getCode().equals(activityTeam.getTeamType())) {
                    clubActivityManager.enrollActivity(loginUser.getId(), mainActivity.getId(), activityTeam.getId());
                }
            }
            if (loginUser.getIsRobot().equals(UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode())
                    && loginUser.getIsTest().equals(UserConstant.TestUserEnum.IS_TEST_0.getCode()) && !annualOrSeasonal.get()) {
                //报名成功，处理竞技赛奖金池逻辑 增加机器人判定逻辑
                competitiveSeasonBizService.getByActivityId(mainActivity.getId())
                        //            competitiveScoreConfigId config的快照id，修改为总id
                        .flatMap(i -> competitiveScoreConfigService.findById(i.getScoreConfigId()))
                        .ifPresent(item -> {
                            seasonBonusPoolService.activityEnroll(mainActivity, loginUser.getId(), item.getConfigId());
                        });

            } else {
                log.info("loginUser 报名奖金池不做计算当前用户为 robot:{},test:{}", loginUser.getIsRobot(), loginUser.getIsTest());
            }
            activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.GENDER_LIMIT).ifPresent(
                    k -> {
                        String limitGender = k.getParamValue();
                        if (!limitGender.equals("0")) {
                            if (!limitGender.equals(loginUser.getGender().toString())) {
                                if (limitGender.equals("1")) {
                                    throw new BizI18nException(I18nMsgUtils.getMessage("activity_gender_limit_male"));
                                } else {
                                    throw new BizI18nException(I18nMsgUtils.getMessage("activity_gender_limit_female"));
                                }
                            }
                            if (appVersion < VersionConstant.V4_8_0){
                                log.info("用户：{}报名活动：{}开启性别限制", loginUser.getId(), mainActivity.getId());
                                RBucket<String> bucket = redissonClient.getBucket(RedisConstants.GENDER_LIMIT_KEY + loginUser.getId());
                                bucket.set("1");
                                bucket.expire(7, TimeUnit.DAYS);
                            }
                        }
                    });

            //发布tb报名成功事件
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(),new TurbolinkApplicationEvent(TurbolinkEventEnum.SIGN_UP, loginUser.getId(), Map.of("isSignUp", Boolean.TRUE.toString())));
            userTaskBizService.completeEvent(new EventTriggerDto().setUser(loginUser).setEventSubType(TaskConstant.TakEventSubTypeEnum.ACTIVITY_JOIN_COMPETITIVE.getCode()));
            //写入数据统计
            proActivityRedisEnhanceService.addEnroll(mainActivity.getId(), loginUser.getId());

            ProActivityType proActivityType = mainActivity.getProActivityType();
            if (appVersion >= VersionConstant.V4_8_0 && ProActivityType.activityTypeClubList().contains(proActivityType)) {
                // 加入月季年赛俱乐部
                try {
                    Result<EnrollActivityResponseDto> responseDto = joinCompetitiveClub(loginUser, proActivityType);
                    if (responseDto != null) return responseDto;
                } catch (Exception e) {
                    log.error("加入月季年赛俱乐部失败，userId:{}，proActivityType:{}，异常:{}", loginUser.getId(), proActivityType.getCode(), e);
                }
            }
        } catch (BizI18nException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return CommonResult.success();
    }


    private Result<EnrollActivityResponseDto> joinCompetitiveClub(ZnsUserEntity loginUser, ProActivityType proActivityType) {
        // 查询用户当前应加入的月赛俱乐部ID
        String clubMapStr = sysConfigService.selectConfigByKey(ConfigKeyEnums.USER_JOIN_COMPETITIVE_CLUB.getCode());
        if (!StringUtils.hasText(clubMapStr)) {
            return null;
        }
        Map<String, Long> userJoinCompetitiveClubMap = JsonUtil.readValue(clubMapStr,new TypeReference<>() {});
        UserExtraParamsQuery query = new UserExtraParamsQuery();
        query.setUserId(loginUser.getId());
        CompetitiveActivityClubEnum clubEnum = CompetitiveActivityClubEnum.getByType(proActivityType);
        query.setParamKey(clubEnum.getJoinedClub());
        UserExtraParamsDo userExtraParams = userExtraParamsService.findByQuery(query);
        if (Objects.isNull(userExtraParams)) {
            Long currentClubId = userJoinCompetitiveClubMap.get(clubEnum.getClub());
            Long nextClubId = userJoinCompetitiveClubMap.get(clubEnum.getNextClub());

            Club club = clubService.findById(currentClubId);
            if (Objects.nonNull(club)) {
                Integer memberCount = club.getMemberCount();
                if (memberCount < 1500) {
                    // 人数 < 1500：加入当前俱乐部
                    addUserToClub(loginUser.getId(), currentClubId, clubEnum.getJoinedClub());
                } else if (memberCount >= 1500 && memberCount < 1990) {
                    // 人数 >= 1500 且 < 1990：加入当前俱乐部
                    addUserToClub(loginUser.getId(), currentClubId, clubEnum.getJoinedClub());

                    // 如果下一个俱乐部为空：创建下一个俱乐部（原名字序号+1）
                    if (Objects.isNull(nextClubId)) {
                        String newClubName = generateNextClubName(club.getName());
                        Long newClubId = clubManager.createNewClub(newClubName, club);

                        // 设置下一个月赛俱乐部的值
                        userJoinCompetitiveClubMap.put(clubEnum.getNextClub(), newClubId);
                        updateCompetitiveClubConfig(userJoinCompetitiveClubMap);
                    }
                } else {
                    // 人数 ≥ 1990：加入下一个俱乐部
                    if (Objects.nonNull(nextClubId)) {
                        Club nextClub = clubService.findById(nextClubId);
                        if (Objects.nonNull(nextClub)) {
                            addUserToClub(loginUser.getId(), nextClubId, clubEnum.getJoinedClub());
                            club = nextClub;

                            // 重置用户当前应加入的俱乐部及下一个俱乐部
                            userJoinCompetitiveClubMap.put(clubEnum.getClub(), nextClubId);
                            userJoinCompetitiveClubMap.put(clubEnum.getNextClub(), null);
                            updateCompetitiveClubConfig(userJoinCompetitiveClubMap);
                        }
                    } else {
                        // 如果下一个俱乐部不存在，先创建再加入
                        String newClubName = generateNextClubName(club.getName());
                        Long newClubId = clubManager.createNewClub(newClubName, club);
                        addUserToClub(loginUser.getId(), newClubId, clubEnum.getJoinedClub());

                        // 重置俱乐部配置
                        userJoinCompetitiveClubMap.put(clubEnum.getClub(), newClubId);
                        userJoinCompetitiveClubMap.put(clubEnum.getNextClub(), null);
                        updateCompetitiveClubConfig(userJoinCompetitiveClubMap);

                        club = clubService.findById(newClubId);
                    }
                }
                if (Objects.nonNull(club)) {
                    EnrollActivityResponseDto responseDto = new EnrollActivityResponseDto();
                    responseDto.setClubId(club.getId());
                    responseDto.setClubName(club.getName());
                    return CommonResult.success(responseDto);
                }
            }
        }
        return null;
    }

    public void sendApplicationAward(ZnsUserEntity loginUser, MainActivity mainActivity) {
        ApplicationRewardDto applicationRewardDto = awardActivityBizService.queryApplicationAward(mainActivity.getId());
        if (Objects.nonNull(applicationRewardDto)) {
            List<WearAwardDto> wearAwards = applicationRewardDto.getWearAwards();
            wearAwards.forEach(s -> {
                Long bagLogId = userWearsBagService.sendUserWear(loginUser.getId(), s, mainActivity.getId());
                String batchNo = OrderUtil.getBatchNo();
                AwardSendDto dto = new AwardSendDto().setActivityId(mainActivity.getId())
                        .setUserId(loginUser.getId()).setType(AwardSentTypeEnum.APPLICATION_AWARD.getType())
                        .setTotalBatchNo(batchNo);
                activityUserAwardBizService.saveNew(dto, bagLogId, AwardTypeEnum.WEAR.getType(), batchNo, batchNo);
            });
        }
    }

    private Integer enrollActivityTeam(EnrollActivityRequest enrollActivityRequest, ZnsUserEntity loginUser) {

        Integer teamRole = 0;
        String lockKey = RedisConstants.ENROLL_TEAM + enrollActivityRequest.getTeamId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean teamLock = false;
        try {
            teamLock = lock.tryLock(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        try {
            if (teamLock) {
                log.info("获取锁团队赛报名锁成功" + lockKey);

                //团队赛检查队伍是否已满
                ActivityTeam team = activityTeamService.getByIdLock(enrollActivityRequest.getTeamId());
                if (Objects.isNull(team)) {
                    throw new BaseException(I18nMsgUtils.getMessage("activity.join.failed"));
                }
                if (team.getMaxNum() > 0 && team.getMaxNum() <= team.getCurrentNum()) {
                    throw new BaseException(I18nMsgUtils.getMessage("activity.apply.num.exceed"));
                }
                if (team.getCurrentNum() == 0 && team.getIsOfficial() == 0) {
                    teamRole = 1;
                }
                //增加队伍用户
                activityTeamService.addUser(loginUser, enrollActivityRequest.getTeamId(), teamRole);
            }

        } catch (BaseException ce) {
            log.info("团队赛报名失败:{},{}", enrollActivityRequest.getMainActivityId(), loginUser.getId(), ce);
            throw ce;
        } catch (Exception e) {
            log.error("团队赛成绩计算error", e);
            throw e;
        } finally {
            log.info("获取锁 后删除锁" + lockKey);
            if (lock.isHeldByCurrentThread()) { //判断锁是否存在，和是否当前线程加的锁。
                lock.unlock();
            }
        }

        return teamRole;
    }

    public void seriesLevelEnroll(Long mainActivityId, ZnsUserEntity loginUser) {
        String lockKey = RedisConstants.SERIES_LEVEL_ENROLL + mainActivityId + "#" + loginUser.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean seriesEnrollLock = lock.tryLock();
        if (seriesEnrollLock) {
            try {
                ZnsRunActivityUserEntity currentActUser = activityUserService.findActivityUser(mainActivityId, loginUser.getId());
                if (currentActUser != null) {
                    return;
                }

                MainActivity mainActivity = mainActivityService.findById(mainActivityId);

                //判断用户是否已经报过主赛事
                MainActivity masterActivity = seriesActivityRelService.getMainActivityBySegmentActId(mainActivityId);
                ZnsRunActivityUserEntity masterAcUser = activityUserService.findActivityUser(masterActivity.getId(), loginUser.getId());
                if (masterAcUser == null) {
                    throw new BaseException(I18nMsgUtils.getMessage("series.activity.not.report"));
                }
                //判断用户是否可以进入当前关卡
                checkEnter(mainActivity, masterActivity, loginUser);

                //添加用户
                SubActivity subActivity = subActivityService.getSingleActByMain(mainActivityId);
                runActivityUserService.addMainActivityUser(new AddMainActivityUserDto().setUser(loginUser)
                        .setActivityId(mainActivityId)
                        .setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType())
                        .setTarget(subActivity.getTarget())
                        .setTargetType(mainActivity.getTargetType()));
            } catch (Exception e) {
                log.error("系列赛报名异常,actId :" + mainActivityId + ",userId：" + loginUser.getId(), e);
                throw new BaseException(e.getMessage());
            } finally {
                if (lock.isHeldByCurrentThread()) { //判断锁是否存在，和是否当前线程加的锁。
                    lock.unlock();
                }
            }
        }


    }

    private void checkEnter(MainActivity currentActivity, MainActivity masterActivity, ZnsUserEntity loginUser) {
        //不允许进入状态  -1 未上架 2已结束 3下架
        List<Integer> noAllowStateList = List.of(MainActivityStateEnum.NOT_PUBLISHED.getCode(), MainActivityStateEnum.ENDED.getCode(), MainActivityStateEnum.OFF_SHELF.getCode());
        if (noAllowStateList.contains(masterActivity.getActivityState()) || noAllowStateList.contains(currentActivity.getActivityState())) {
            //主活动或者子活动状态 不允许进入
            throw new BaseException(I18nMsgUtils.getMessage("activity.status.invalid"));
        }
        ActivityEnterThreshold threshold = activityEnterThresholdService.findByActId(currentActivity.getId());
        if (threshold == null || threshold.getConditionState() == 0) {
            return;
        }

        MainActivity preActivity = seriesActivityRelService.getPreActivity(currentActivity.getId());
        if (preActivity != null) {
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(preActivity.getId(), loginUser.getId());
            if (activityUser.getIsComplete() != 1) {
                throw new BaseException(I18nMsgUtils.getMessage("not.meet.activity.require"));
            }
        }
    }

    public MyTeamActRecordDto teamMyRace(SingleActivityIdRequest request, ZnsUserEntity loginUser) {

        Long mainActivityId = request.getMainActivityId();
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        MyTeamActRecordDto teamRecordDto = getMyTeamActRecordDto(mainActivity, loginUser.getId());

        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());


        //todo:现在只有单选
        String rankingBy = entryGameplay.getRankingBy();
        Integer rankType = Integer.valueOf(rankingBy);
        teamRecordDto.setRankType(rankType);

        //分页
        Page<TeamUserGradeDto> page = new Page<>(request.getPageNum(), request.getPageSize());
        page.setTotal(teamRecordDto.getUserGrades().size());

        List<TeamUserGradeDto> pageGradeDtos = teamRecordDto.getUserGrades().stream()
                .skip((request.getPageNum() - 1) * request.getPageSize())
                .limit(request.getPageSize())
                .toList();
        page.setRecords(pageGradeDtos);
        teamRecordDto.setUserGrades(null);
        teamRecordDto.setPageUserGrades(page);
        Integer teamRole = activityUserService.findActivityUser(mainActivityId, loginUser.getId()).getTeamRole();
        teamRecordDto.setTeamRole(teamRole);
        teamRecordDto.setUserId(loginUser.getId());

        //召唤记录
        List<TeamCallRecord> todayCallRecord = teamCallRecordService.getTodayCallRecord(teamRecordDto.getTeamId(), loginUser.getId(), null);
        if (teamRole == 1) {
            if (MainActivityStateEnum.STARTED.getCode().equals(mainActivity.getActivityState())) {
                teamRecordDto.setOneClickStatus(0);
            }
        } else {
            int count = 5;
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.TEAM_ACTIVITY_CALL_COUNT.getCode());
            if (sysConfig != null) {
                count = Integer.parseInt(sysConfig.getConfigValue());
            }
            teamRecordDto.setRemainClickCount(count - todayCallRecord.size());
        }
        //马拉松增强
        if (sysConfigService.isMarathon(mainActivityId)) {
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivityId, loginUser.getId());
            if (activityUser != null) {
                ActivityTeam activityTeam = activityTeamService.findById(activityUser.getTeamId());
                marathonExtendTeamRecordDto(teamRecordDto.getUserGrades(), activityTeam);
                marathonExtendTeamRecordDto(teamRecordDto.getPageUserGrades().getRecords(), activityTeam);
            }

        }


        return teamRecordDto;
    }

    private void marathonExtendTeamRecordDto(List<TeamUserGradeDto> userGrades, ActivityTeam activityTeam) {
        if (CollectionUtils.isEmpty(userGrades) || activityTeam == null || activityTeam.getMillage() == null || activityTeam.getMillage() == 0) {
            return;
        }

        // 获取团队总里程
        BigDecimal teamTotalMileage = new BigDecimal(activityTeam.getMillage());

        // 计算每个用户的进度百分比
        for (TeamUserGradeDto userGrade : userGrades) {
            if (userGrade.getMillage() != null) {
                // 计算用户贡献百分比 = (用户里程 / 团队总里程) * 100
                BigDecimal userMileage = new BigDecimal(userGrade.getMillage());
                BigDecimal progress = userMileage
                        .divide(teamTotalMileage, 6, BigDecimal.ROUND_HALF_UP)  // 除法保留6位小数
                        .multiply(new BigDecimal("100"))  // 转换为百分比
                        .setScale(2, BigDecimal.ROUND_HALF_UP);  // 最终结果保留4位小数

                userGrade.setProgress(progress.toString());
            } else {
                userGrade.setProgress(BigDecimal.ZERO.toString());
            }
        }
    }

    public void activityDress(ActivityRequestDto request, ZnsUserEntity user) {
        MainActivity mainActivity = mainActivityService.findById(request.getId());
        Long applicationStartTime = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId());
        Long activityEndTime = DateUtil.getStampByZone(mainActivity.getActivityEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId());
        long expireTime = activityEndTime - applicationStartTime;
        String key = ApiConstants.ACTIVITY_APPLICATION_AWARD_DRESS + request.getId() + user.getId();
        redisTemplate.opsForValue().set(key, "1", expireTime, TimeUnit.SECONDS);
    }

    public void shareSuccessCallback(Long mainActivityId, ZnsUserEntity currentUser) {
        RLock lock = redissonClient.getLock(RedisConstants.SHARE_SUCCESS_CALLBACK + mainActivityId + "#" + currentUser.getId());
        LockHolder.tryLock(lock, 5, () -> {
            Long finalMainActivityId = mainActivityId;
            MainActivity mainActivity = mainActivityService.findById(mainActivityId);
            //系列赛定位到主赛事
            if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
                mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(mainActivityId);
                finalMainActivityId = mainActivity.getId();
            }
            ActivityImpracticalAwardConfig impracticalAwardConfig = impracticalAwardConfigService.findByActId(finalMainActivityId);
            if (Objects.nonNull(impracticalAwardConfig) && !Objects.equals(impracticalAwardConfig.getShareScore(), -1)) {
                //判断用户是否报过名
                ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(finalMainActivityId, currentUser.getId());
                if (Objects.nonNull(activityUser)) {
                    //查用户是否获得过积分奖励
                    List<ActivityUserScore> activityUserScores = activityUserScoreService.selectActivityUserScoreByActivityIdUserId(finalMainActivityId, currentUser.getId());
                    long count = activityUserScores.stream().filter(k -> Objects.equals(k.getSource(), ScoreConstant.SourceTypeEnum.source_type_31.getType())).count();
                    if (count < 1) {
                        Integer shareScore = impracticalAwardConfig.getShareScore();
                        //投流用户奖励
                        shareScore = trafficInvestmentUserAwardChecker.scoreUserLimitCheck(currentUser.getId(), shareScore);
                        if (shareScore > 0) {
                            activityUserScoreService.increaseAmount(shareScore, finalMainActivityId, currentUser.getId(), null, 0,
                                    ScoreConstant.SourceTypeEnum.source_type_31.getType());
                        }

                    }
                }
            }
        });
    }

    public Integer queryShareAward(Long mainActivityId, ZnsUserEntity currentUser) {

        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        //系列赛定位到主赛事
        if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
            mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(mainActivityId);
            mainActivityId = mainActivity.getId();
        }

        ActivityImpracticalAwardConfig impracticalAwardConfig = impracticalAwardConfigService.findByActId(mainActivityId);
        if (Objects.nonNull(impracticalAwardConfig) && !Objects.equals(impracticalAwardConfig.getShareScore(), -1)) {
            //查用户是否获得过积分奖励
            List<ActivityUserScore> activityUserScores = activityUserScoreService.selectActivityUserScoreByActivityIdUserId(mainActivityId, currentUser.getId());
            long count = activityUserScores.stream().filter(k -> Objects.equals(k.getSource(), ScoreConstant.SourceTypeEnum.source_type_31.getType())).count();
            if (count < 1) {
                return impracticalAwardConfig.getShareScore();
            }
        }
        return null;
    }


    public SeriesActivityRankResponse seriesActivityRank(SeriesActivityRankRequest request, ZnsUserEntity loginUser) {

        SeriesActivityRankResponse response = new SeriesActivityRankResponse();
        Long mainActivityId = request.getMainActivityId();
        Long userId = request.getUserId();
        Integer source = request.getSource();
        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();

        Page<SeriesActivityRankDto> page = new Page<>(pageNum, pageSize);


        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
            //如果是阶段活动，重定位到主活动
            mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(mainActivityId);
            mainActivityId = mainActivity.getId();
        }

        SeriesGameplay seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
        log.info("活动{}排名依据为{}", mainActivityId, seriesGameplay);
        if (!Arrays.asList("1", "2", "3", "4").contains(seriesGameplay.getRankingBy())) {
            return null;
        }

        List<ZnsRunActivityUserEntity> allActivityUserList = runActivityUserService.findAllActivityUser(mainActivity.getId());

        List<ZnsUserEntity> users = userService.findByIds(allActivityUserList.stream().map(ZnsRunActivityUserEntity::getUserId).toList());
        Map<Long, List<ZnsUserEntity>> userMap = users.stream().collect(Collectors.groupingBy(ZnsUserEntity::getId));

        for (ZnsRunActivityUserEntity activityUser : allActivityUserList) {
            if (!CollectionUtils.isEmpty(userMap.get(activityUser.getUserId()))) {
                //更新名字
                activityUser.setNickname(userMap.get(activityUser.getUserId()).get(0).getFirstName());
            }
            //配速
            if (Objects.isNull(activityUser.getPropRunTime())) {
                activityUser.setPropRunTime(activityUser.getRunTimeMillisecond());
            }
            activityUser.setAverageVelocity(SportsDataUnit.getVelocity(activityUser.getPropRunTime() / 1000, activityUser.getRunMileage()));

        }
        //预排序
        allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getCreateTime))
                .sorted(Comparator.comparing(ZnsRunActivityUserEntity::getNickname)).collect(Collectors.toList());
        // 审核成绩(活动已经结束)失败用户 再次剔除后进行排序
        removeViewCheatActivityUser(mainActivity, allActivityUserList);
        List<ZnsRunActivityUserEntity> filterActivityUserList = new ArrayList<>();

        log.info("用户刷选前{}", allActivityUserList);
        //用户刷选
        if (2 == seriesGameplay.getRankingUser()) {
            Long finalMainActivityId = mainActivityId;
            filterActivityUserList = allActivityUserList.stream().filter(a -> activityUserBizService.joinSeriesAllSegment(finalMainActivityId, a.getUserId())).collect(Collectors.toList());
        } else if (3 == seriesGameplay.getRankingUser()) {
            filterActivityUserList = allActivityUserList.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
        } else if (1 == seriesGameplay.getRankingUser()) {
            filterActivityUserList = new ArrayList<>(allActivityUserList);
        }
        log.info("用户刷选后{}", filterActivityUserList);
        //剩余
        allActivityUserList.removeAll(filterActivityUserList);
        log.info("用户剩余{}", allActivityUserList);

        Map<Long, BigDecimal> userGainsAmount = activityUserAwardBizService.sumUserActivityGainsAmount(mainActivityId);
        //排序
        List<SeriesActivityRankDto> filterList = seriesSortAndConvert(filterActivityUserList, seriesGameplay, 0, userGainsAmount);
        List<SeriesActivityRankDto> finalList = filterList;

        //代表app
        if (source == 1) {
            //对剩余的用户进行设置rank。
            List<SeriesActivityRankDto> remainList = seriesSortAndConvert(allActivityUserList, seriesGameplay, 1, userGainsAmount);
            int startRank = filterList.size();
            for (int i = 0; i < remainList.size(); i++) {
                SeriesActivityRankDto rankDto = remainList.get(i);
                rankDto.setRank(startRank + i + 1);
                finalList.add(rankDto);
            }

            if (Objects.nonNull(loginUser)) {
                Optional<SeriesActivityRankDto> first = finalList.stream().filter(k -> Objects.equals(k.getUserId(), loginUser.getId())).findFirst();
                //不在排名范围情况排行榜
                first.ifPresent(response::setMyRank);
            }
        } else if (source == 2 && Objects.nonNull(userId)) {
            Optional<SeriesActivityRankDto> first = filterList.stream().filter(k -> Objects.equals(k.getUserId(), userId)).findFirst();
            //不在排名范围情况排行榜
            if (first.isPresent()) {
                response.setMyRank(first.get());
            } else {
                finalList.clear();
            }
        }
        long startIndex = (pageNum - 1) * pageSize;
        //不分页，全部返回
        if (request.isAll()) {
            pageSize = finalList.size();
            startIndex = 0;
        }
        int size = finalList.size();
        finalList = finalList.stream().skip(startIndex).limit(pageSize).collect(Collectors.toList());
        page.setTotal(size);
        page.setCurrent(pageNum);
        if (pageSize != 0) {
            page.setPages(size / pageSize);
        } else {
            page.setPages(0);
        }
        page.setRecords(finalList);
        //写入奖金
        if (!CollectionUtils.isEmpty(finalList)) {
            finalList.forEach(item -> {
                item.setBonus(userGainsAmount.getOrDefault(item.getUserId(), BigDecimal.ZERO));
            });
        }
        response.setRanks(page);
        response.setRankingBy(seriesGameplay.getRankingBy());
        packageEquipment(mainActivityId, response);
        return response;
    }


    /**
     * 剔除掉得 allActivityUserList 如果是stream 过滤之后 不能使用toList 收集。建议使用collect(Collectors.toList())
     *
     * @param mainActivity
     * @param allActivityUserList
     */
    public void removeViewCheatActivityUser(MainActivity mainActivity, List<ZnsRunActivityUserEntity> allActivityUserList) {
        if (MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState()) && mainActivity.getAwardSendStatus() == 1) {
            List<ActivityUserAwardPre> activityUserAwardPres = activityUserAwardPreService.cheatUserIdListList(mainActivity.getId());
            List<Long> userIdlist = activityUserAwardPres.stream().map(ActivityUserAwardPre::getUserId).toList();
            if (!CollectionUtils.isEmpty(userIdlist)) {
                List<ZnsRunActivityUserEntity> filterActivityUserList2 = runActivityUserService.findActivityUsersList(mainActivity.getId(), userIdlist);
                List<Long> longs = filterActivityUserList2.stream().map(ZnsRunActivityUserEntity::getId).toList();
                allActivityUserList.removeIf(a -> longs.contains(a.getId()));
            }
        }
    }

    /**
     * 根据定义的规则进行排序，时间/里程/完赛时间/速度
     * DNF 用户
     * 作弊用户
     *
     * @param activityUserList
     * @param seriesGameplay
     * @param source
     * @return
     */
    private List<SeriesActivityRankDto> seriesSortAndConvert(List<ZnsRunActivityUserEntity> activityUserList, SeriesGameplay seriesGameplay, int source, Map<Long, BigDecimal> userGainsAmount) {

        ZonedDateTime now = ZonedDateTime.now();
        List<SeriesActivityRankDto> list = new ArrayList<>();
        if (source == 0) {
            //用户排名当前单选
            if ("1".equals(seriesGameplay.getRankingBy())) {
                activityUserList = activityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()
                        .thenComparing(ActivitySeriesUserComparator.defaultComparator(userGainsAmount))
                ).collect(Collectors.toList());
            } else if ("2".equals(seriesGameplay.getRankingBy())) {
                if (2 == seriesGameplay.getRankingUser() || 1 == seriesGameplay.getRankingUser()) {
                    activityUserList = activityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getPropRunTime).reversed()
                            .thenComparing(ActivitySeriesUserComparator.defaultComparator(userGainsAmount))
                    ).collect(Collectors.toList());
                } else {
                    activityUserList = activityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getPropRunTime)
                            .thenComparing(ActivitySeriesUserComparator.defaultComparator(userGainsAmount))
                    ).collect(Collectors.toList());
                }
            } else if ("3".equals(seriesGameplay.getRankingBy())) {
                Comparator<ZnsRunActivityUserEntity> completeTimeComparator = (o1, o2) -> {
                    ZonedDateTime o1CompleteTime = o1.getCompleteTime();
                    ZonedDateTime o2CompleteTime = o2.getCompleteTime();
                    if (Objects.isNull(o1CompleteTime)) {
                        o1CompleteTime = DateUtil.addDays(now, 1);
                    }
                    if (Objects.isNull(o2CompleteTime)) {
                        o2CompleteTime = DateUtil.addDays(now, 1);
                    }
                    return o1CompleteTime.compareTo(o2CompleteTime);
                };
                activityUserList = activityUserList.stream().sorted(completeTimeComparator
                        .thenComparing(ActivitySeriesUserComparator.defaultComparator(userGainsAmount))
                ).collect(Collectors.toList());

            } else if ("4".equals(seriesGameplay.getRankingBy())) {
                activityUserList = activityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getAverageVelocity).reversed()
                        .thenComparing(ActivitySeriesUserComparator.defaultComparator(userGainsAmount))
                ).collect(Collectors.toList());
            }
            log.info("seriesActivityEnd end，rankingUser={}，rankingBy={}", seriesGameplay.getRankingUser(), seriesGameplay.getRankingBy());
            for (int i = 0; i < activityUserList.size(); i++) {
                ZnsRunActivityUserEntity runActivityUser = activityUserList.get(i);
                runActivityUser.setRank(i + 1);
            }
        }

        list = activityUserList.stream().map(k -> {
            //根据前面的排名进行重新赋值。
            SeriesActivityRankDto rankDto = new SeriesActivityRankDto();
            rankDto.setNickname(k.getNickname());
            rankDto.setRank(k.getRank());
            rankDto.setUserId(k.getUserId());
            rankDto.setRunTime(k.getPropRunTime());
            rankDto.setAverageVelocity(k.getAverageVelocity());
            rankDto.setRunMileage(k.getRunMileage().intValue());
            rankDto.setCompleteTime(k.getCompleteTime());
            rankDto.setCheatingState(YesNoStatus.NO.getCode());
            rankDto.setEntryActivityTime(k.getEntryActivityTime());
            if (source == 0) {
                //source = 0 会先调用一次。所有都会设置为改状态。
                rankDto.setCompleteState(1);
            } else if (source == 1) {
                rankDto.setCompleteState(0);
                if (k.getRunMileage().compareTo(BigDecimal.ZERO) == 0) {
                    //退赛
                    rankDto.setCompleteState(2);
                }
            }
            if (rankDto.getCompleteState() == 1 && k.getRank() == -1) {
                //作弊
                rankDto.setCheatingState(YesNoStatus.YES.getCode());
            }
            return rankDto;
        }).collect(Collectors.toList());

        if (source == 1) {
            //只有0  和 2 状态，才会走这里。
            list = list.stream().sorted(Comparator.comparingInt(SeriesActivityRankDto::getCompleteState)
                    .thenComparing(ActivitySeriesUserComparator.defaultRankComparator(userGainsAmount))).toList();
        }
        return list;
    }

    public List<SingleStageRankDto> singleStageRank(Long mainActivityId) {
        return activityStageBusiness.singleStageRank(mainActivityId);
    }

    public List<SingleStageRankDto> seriesStageActivityRank(Long mainActivityId) {
        return activityStageBusiness.singleStageRank(mainActivityId);
    }

    public void stageActivityEnter(Long mainActivityId, ZnsUserEntity loginUser) {
        RLock lock = redissonClient.getLock(RedisConstants.STAGE_ACTIVITY_ENTER + mainActivityId + "#" + loginUser.getId());
        LockHolder.tryLock(lock, 0, () -> {
            ActivityStageUserInfo stageUserInfo = activityStageBusiness.findStageUserInfo(mainActivityId, ZonedDateTime.now(), loginUser.getId());
            if (stageUserInfo.getCurrentStage() != null && stageUserInfo.getCurrentRunActivityStageUser() == null) {

                RunActivityStageUser userAndStage = runActivityStageUserService.findByUserAndStage(loginUser.getId(), stageUserInfo.getCurrentStage().getId());
                if (userAndStage != null) {
                    log.info("Stage user :{} has entered:{} ", loginUser.getId(), mainActivityId);
                    return;
                }
                log.info("Stage user :{} entered:{} ", loginUser.getId(), mainActivityId);
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(mainActivityId, loginUser.getId());
                RunActivityStageUser stageUser = new RunActivityStageUser();
                stageUser.setActivityId(mainActivityId);
                stageUser.setStageId(stageUserInfo.getCurrentStage().getId());
                stageUser.setStage(stageUserInfo.getCurrentStage().getLevel());
                stageUser.setUserId(loginUser.getId());
                stageUser.setIsRobot(loginUser.getIsRobot());
                stageUser.setIsTest(loginUser.getIsTest());
                stageUser.setReportTime(activityUser.getCreateTime());
                runActivityStageUserService.insert(stageUser);

                log.info("用户：{}加入阶段活动：{}成功", loginUser.getId(), mainActivityId);
            }
        });

    }

    public CommonShareRespDto getShareCommonUrl(CommonShareDto dto) {
        CommonShareRespDto commonShareRespDto = new CommonShareRespDto();
        String url = "";
        String img = dto.getImageUrl();
        String content = dto.getContent();
        String jumpUrl = dto.getJumpUrl();
        String html = sysConfigService.selectConfigByKey("activity_share_template");
        String message = MessageFormat.format(html, content, img, jumpUrl, jumpUrl);
        log.info("生成的html为{}", message);
        MultipartFile multipartFile = new com.linzi.pitpat.framework.web.entity.MockMultipartFile("share.html", "share.html", "application/html", message.getBytes());
        Map<String, Object> map = AwsUtil.putS3Object(multipartFile, "html");
        url = map.get("url").toString();
        log.info("文件访问地址" + map.get("url"));
        commonShareRespDto.setShareUrl(url);
        return commonShareRespDto;
    }

    public TeamPersonalRankResultDto teamSinglePersonalRank(Long mainActivityId, ZnsUserEntity currentUser) {
        var teamPersonalRankResultDto = new TeamPersonalRankResultDto();
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivityId, currentUser.getLanguageCode());
        //活动未结束开启排行榜 0否 1是 默认0
        if (Objects.nonNull(disseminate) && Objects.equals(disseminate.getEnableRankBeforeEnd(), 0)
                && !MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())) {
            return teamPersonalRankResultDto;
        }
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        Integer competitionFormat = entryGameplay.getCompetitionFormat();
        Integer dataSource = entryGameplay.getDataSource();
        List<TeamPersonalRankDto> rankList;
        teamPersonalRankResultDto.setIsSignup(YesNoStatus.NO.getCode());
        if (Objects.nonNull(currentUser.getId())) {
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(mainActivityId, currentUser.getId());
            if (Objects.nonNull(activityUser)) {
                teamPersonalRankResultDto.setIsSignup(YesNoStatus.YES.getCode());
            }
        }
        if (competitionFormat == 1 && dataSource == 6) {
            List<TeamEffectiveGrade> allTeamUser = new ArrayList<>();
            List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivityId);
            teams.forEach(team -> {
                List<TeamEffectiveGrade> teamEffectiveGradeServiceByTeam = teamEffectiveGradeService.findByTeam(team.getId());
                allTeamUser.addAll(teamEffectiveGradeServiceByTeam);
            });
            //团队赛所有参与人数计入次数排序
            if (mainActivity.getActivityState().equals(MainActivityStateEnum.ENDED.getCode())) {
                rankList = allTeamUser.stream().sorted(Comparator.comparingInt(TeamEffectiveGrade::getPersonalRank)).map(teamEffectiveGrade -> {
                    TeamPersonalRankDto personalRankDto = new TeamPersonalRankDto();
                    ZnsUserEntity znsUser = userService.findByIdWithoutLogicDelete(teamEffectiveGrade.getUserId());
                    if (znsUser != null) {
                        personalRankDto.setAvatar(znsUser.getHeadPortrait());
                        personalRankDto.setName(znsUser.getFirstName());
                        personalRankDto.setRank(teamEffectiveGrade.getPersonalRank());
                        personalRankDto.setUserId(znsUser.getId());
                        personalRankDto.setRunCount(teamEffectiveGrade.getRunCount());
                    }
                    if (Objects.nonNull(currentUser.getId())) {
                        if (teamEffectiveGrade.getUserId().equals(currentUser.getId())) {
                            teamPersonalRankResultDto.setSelfRank(teamEffectiveGrade.getPersonalRank());
                            if (teamEffectiveGrade.getPersonalRank() > 0) {
                                teamPersonalRankResultDto.setIsInRank(YesNoStatus.YES.getCode());
                            }
                        }
                    }
                    return personalRankDto;
                }).toList();
            } else {
                rankList = getTeamPersonalRankDtos(allTeamUser);
                rankList.forEach(item -> {
                    if (Objects.nonNull(currentUser.getId())) {
                        if (item.getUserId().equals(currentUser.getId())) {
                            teamPersonalRankResultDto.setSelfRank(item.getRank());
                            if (item.getRank() > 0) {
                                teamPersonalRankResultDto.setIsInRank(YesNoStatus.YES.getCode());
                            }
                        }
                    }
                });
            }
            if (Objects.isNull(teamPersonalRankResultDto.getIsSignup()) || Objects.isNull(teamPersonalRankResultDto.getIsInRank())) {
                teamPersonalRankResultDto.setIsInRank(YesNoStatus.NO.getCode());
            }
            teamPersonalRankResultDto.setRankList(rankList);

        }
        return teamPersonalRankResultDto;
    }

    private List<TeamPersonalRankDto> getTeamPersonalRankDtos(List<TeamEffectiveGrade> allTeamUser) {
        List<TeamPersonalRankDto> rankList;
        // 实时排序根据runCount排序 排除次数为0 的
        rankList = allTeamUser.stream().filter(t -> t.getRunCount() > 0).map(teamEffectiveGrade -> {
            TeamPersonalRankDto personalRankDto = new TeamPersonalRankDto();
            ZnsUserEntity znsUser = userService.findByIdWithoutLogicDelete(teamEffectiveGrade.getUserId());
            if (znsUser != null) {
                personalRankDto.setAvatar(znsUser.getHeadPortrait());
                personalRankDto.setName(znsUser.getFirstName());
                personalRankDto.setUserId(znsUser.getId());
                personalRankDto.setRunCount(teamEffectiveGrade.getRunCount());
                personalRankDto.setCompleteTime(teamEffectiveGrade.getGmtModified());
            }
            return personalRankDto;
        }).sorted((a, b) -> Integer.compare(b.getRunCount(), a.getRunCount())).sorted((a, b) -> {
            if (a.getRunCount().equals(b.getRunCount())) {
                return a.getCompleteTime().compareTo(b.getCompleteTime());
            }
            return b.getRunCount().compareTo(a.getRunCount());
        }).toList();
        // 为 rank 字段赋值
        int currentRank = 1;
        for (TeamPersonalRankDto dto : rankList) {
            dto.setRank(currentRank++);
        }
        return rankList;
    }

    @Transactional
    public void createMarathonTeam(MarathonTeamCreateRequest request, ZnsUserEntity loginUser, Integer appVersion) {

        Boolean is = sysConfigService.isMarathon(request.getActivityId());
        if (!is) {
            log.info("活动id不是马拉松活动");
            return;
        }
        Long activityId = request.getActivityId();

        RLock lock = redissonClient.getLock("createMarathonTeam:" + activityId);
        LockHolder.tryLock(lock, 120, () -> {
            ActivityTeamJoinSettingDto activityTeamSetting = activityParamsLoaderService.getActivityTeamSetting(activityId);
            Integer allowJoinTeamCount = activityTeamSetting.getAllowJoinTeamCount();
            List<ActivityTeam> teamsByActivityId = teamService.getTeamsByActivityId(activityId);

            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(activityId, loginUser.getId());

            if (teamsByActivityId.size() >= allowJoinTeamCount || activityUser != null) {
                String message = I18nMsgUtils.getMessage("marathon.limit.create.team", allowJoinTeamCount);
                throw new BaseException(message);
            }
            ActivityTeam team = new ActivityTeam();
            team.setActivityId(activityId);
            team.setTeamLogo(request.getTeamLogo());
            team.setTeamName(request.getTeamName());
            team.setMaxNum(activityTeamSetting.getAllowJoinTeamMemberLimit());
            team.setCurrentNum(0);
            team.setIsOfficial(0);
            team.setTeamManagerId(loginUser.getId());
            team.setTeamType(ActivityTeamTypeEnum.CUSTOMER.getCode());
            teamService.save(team);
            Integer target = subActivityService.getAllSingleActByMain(activityId).get(0).getTarget();
            EnrollActivityRequest enrollActivityRequest = new EnrollActivityRequest();
            enrollActivityRequest.setMainActivityId(activityId);
            enrollActivityRequest.setTeamId(team.getId());
            enrollActivityRequest.setRunningGoals(target);
            enrollActivity(enrollActivityRequest, loginUser, appVersion, false);
            marathonJoinReviewManager.teamApplyProcess(loginUser.getId(), activityId, team.getId());
        });

    }

    public void joinMarathonTeam(Long teamId, ZnsUserEntity loginUser) {

        RLock lock = redissonClient.getLock("joinMarathonTeam:" + teamId);

        LockHolder.tryLock(lock, 120, () -> {
            ActivityTeam team = teamService.findById(teamId);
            Long activityId = team.getActivityId();
            Boolean is = sysConfigService.isMarathon(team.getActivityId());
            if (!is) {
                log.info("活动id不是马拉松活动");
                return;
            }
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(activityId, loginUser.getId());
            if (activityUser != null || team.getCurrentNum() >= team.getMaxNum()) {
                throw new BaseException("not allow join other team");
            }

            TeamApplyDo teamApplyDo = new TeamApplyDo();
            teamApplyDo.setApplyUserId(loginUser.getId());
            teamApplyDo.setApplyUserCode(loginUser.getUserCode());
            teamApplyDo.setTeamId(teamId);
            teamApplyDo.setActivityId(activityId);
            teamApplyDo.setIsPop(0);
            teamApplyDo.setTeamLeader(team.getTeamManagerId());
            teamApplyDo.setStatus(TeamApplyStatusEnum.ACTIVITY.getCode());
            teamApplyService.create(teamApplyDo);

        });
    }

    public List<CardUserDto> getCardUserDto(Long mainActivityId) {

        //获得资格卡用户
        List<CardUserDto> cardUserDtos = generateCardUserDto(mainActivityId);
        return cardUserDtos;
    }

    private static class ActivitySeriesUserComparator {

        // 按金额降序
        public static Comparator<ZnsRunActivityUserEntity> byAmountDesc(Map<Long, BigDecimal> userGainsAmount) {
            return (user1, user2) -> {
                BigDecimal amount1 = userGainsAmount.getOrDefault(user1.getUserId(), BigDecimal.ZERO);
                BigDecimal amount2 = userGainsAmount.getOrDefault(user2.getUserId(), BigDecimal.ZERO);
                return amount2.compareTo(amount1);
            };
        }

        // 按金额降序
        public static Comparator<SeriesActivityRankDto> byAmountDescRank(Map<Long, BigDecimal> userGainsAmount) {
            return (user1, user2) -> {
                BigDecimal amount1 = userGainsAmount.getOrDefault(user1.getUserId(), BigDecimal.ZERO);
                BigDecimal amount2 = userGainsAmount.getOrDefault(user2.getUserId(), BigDecimal.ZERO);
                return amount2.compareTo(amount1);
            };
        }

        /// 名字/
        public static Comparator<ZnsRunActivityUserEntity> byNickName() {
            return (user1, user2) -> {
                String n1 = user1.getNickname() == null ? "" : user1.getNickname();
                String n2 = user2.getNickname() == null ? "" : user2.getNickname();
                return n1.compareTo(n2);
            };
        }

        public static Comparator<SeriesActivityRankDto> byNickNameRank() {
            return (user1, user2) -> {
                String n1 = user1.getNickname() == null ? "" : user1.getNickname();
                String n2 = user2.getNickname() == null ? "" : user2.getNickname();
                return n1.compareTo(n2);
            };
        }

        //        报名时间
        public static Comparator<ZnsRunActivityUserEntity> byEnrollTime() {
            return (user1, user2) -> {
                if (Objects.isNull(user1.getEntryActivityTime())) {
                    return -1;
                }
                if (Objects.isNull(user2.getEntryActivityTime())) {
                    return 1;
                }
                long time = user1.getEntryActivityTime().toInstant().toEpochMilli();
                long time2 = user2.getEntryActivityTime().toInstant().toEpochMilli();
                return Long.compare(time, time2);
            };
        }

        public static Comparator<SeriesActivityRankDto> byEnrollTimeRank() {
            return (user1, user2) -> {
                if (Objects.isNull(user1.getEntryActivityTime())) {
                    return -1;
                }
                if (Objects.isNull(user2.getEntryActivityTime())) {
                    return 1;
                }
                long time = user1.getEntryActivityTime().toInstant().toEpochMilli();
                long time2 = user2.getEntryActivityTime().toInstant().toEpochMilli();
                return Long.compare(time, time2);
            };
        }

        public static Comparator<ZnsRunActivityUserEntity> defaultComparator(Map<Long, BigDecimal> userGainsAmount) {
            return byAmountDesc(userGainsAmount).thenComparing(byNickName()).thenComparing(byEnrollTime());
        }

        public static Comparator<SeriesActivityRankDto> defaultRankComparator(Map<Long, BigDecimal> userGainsAmount) {
            return byAmountDescRank(userGainsAmount).thenComparing(byNickNameRank()).thenComparing(byEnrollTimeRank());
        }
    }


    /**
     * 进入跑道前检查
     *
     * @param activityEntity
     * @param user
     * @return
     */
    public Result checkReportUserRun(ActivityTypeDto activityEntity, ZnsUserEntity user) {
        Result result = checkRunActivityTime(activityEntity, user);
        if (Objects.nonNull(result)) {
            return result;
        }
        try {
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), user.getId());
            if (activityEntity.getActivityState() == 3 || activityEntity.getActivityState() == 2 || activityEntity.getActivityState() == -1) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
            }
            //子活动校验主活动状态
            if (MainActivityTypeEnum.SERIES_SUB.getType().equals(activityEntity.getMainType())) {
                SeriesActivityRel activityRel = seriesActivityRelService.findBySubId(activityEntity.getId());
                MainActivity mainActivity = mainActivityService.findById(activityRel.getParentActivityId());
                if (mainActivity.getActivityState() == 3 || mainActivity.getActivityState() == 2 || mainActivity.getActivityState() == -1) {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
                }
            }

            if (activityEntity.getActivityStartTime().isAfter(ZonedDateTime.now())) {
                return CommonResult.success();
            }

            if (Objects.isNull(activityUser) || activityUser.getUserState() == 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Cannot enter, please sign up first.");
            }
            if (Objects.nonNull(activityUser) && activityUser.getUserState() == 2) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Cannot enter. Invitation has been rejected.");
            }
            //段位赛没有其他逻辑判断
            if (activityEntity.getMainType().equals("rank")) {
                return CommonResult.success();
            }
            //阶段活动判断
            if (!activityStageBusiness.canRun(activityEntity.getId(), user.getId(), ZonedDateTime.now())) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
            }
        } catch (Exception e) {
            log.error("checkReportUserRun error,e=" + e);
        }
        return CommonResult.success();
    }

    /**
     * 上报用户状态
     *
     * @param activityEntity
     * @param user
     * @param userStatus
     * @return
     */
    public Result handleReportUserRunStatus(ActivityTypeDto activityEntity, ZnsUserEntity user, Integer userStatus) {
        //时间判断
        if (ActivityUserStateEnum.RUNING.getState().equals(userStatus)) {
            Result result = checkRunActivityTime(activityEntity, user);
            if (Objects.nonNull(result)) {
                return result;
            }

            // 处理跑步中
            ActivityUserStateEnum activityUserStateEnum = ActivityUserStateEnum.findByType(userStatus);


            if (MainActivityTypeEnum.SERIES_SUB.getType().equals(activityEntity.getMainType())) {
                //更新主赛事状态
                MainActivity mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(activityEntity.getId());
                runActivityUserService.updateActivityUserState(mainActivity.getId(), user.getId(), activityUserStateEnum);
            }
            runActivityUserService.updateActivityUserState(activityEntity.getId(), user.getId(), activityUserStateEnum);
        } else if (ActivityUserStateEnum.ENDED.getState().equals(userStatus)) {
            //新活动获取玩法
            MainActivity mainActivity = activityEntity.getMainActivity();
            SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
            if (subActivity.getUserEnterLimit() > 0) {
                //查询已跑步次数
                //获取用户已经跑步次数
                Integer count = userRunDataDetailsService.getCountByActivityId(user.getId(), mainActivity.getId());
                if (count < subActivity.getUserEnterLimit()) {
                    return CommonResult.success();
                } else {
                    // 处理跑步结束
                    ActivityUserStateEnum activityUserStateEnum = ActivityUserStateEnum.findByType(userStatus);
                    runActivityUserService.updateActivityUserState(activityEntity.getId(), user.getId(), activityUserStateEnum);
                }
            } else {
                return CommonResult.success();
            }
        }

        return CommonResult.success();
    }

    /**
     * 检查活动时间
     *
     * @param activityEntity
     * @param user
     * @return
     */
    public Result checkRunActivityTime(ActivityTypeDto activityEntity, ZnsUserEntity user) {
        //段位赛允许用户提前进去，等后机器人入场在比赛
        if (Objects.equals(activityEntity.getMainType(), MainActivityTypeEnum.RANK.getType()) ||
                Objects.equals(activityEntity.getMainType(), MainActivityTypeEnum.PROP.getType())) {
            return null;
        }
        if (activityEntity.getActivityState() == 0) {

            Long startStamp = DateUtil.getStampByZone(DateUtil.formateDateStr(activityEntity.getActivityStartTime(), DateUtil.YYYY_MM_DD_HH_MM_SS),
                    activityEntity.getTimeStyle() == 0 ? null : user.getZoneId());

            log.info("checkRunActivityTime startStamp{},waitTime{},System.currentTimeMillis{}",
                    startStamp, activityEntity.getWaitTime(), System.currentTimeMillis());
            if (startStamp - activityEntity.getWaitTime() * 60 * 1000 > System.currentTimeMillis()) {
                return CommonResult.fail(I18nMsgUtils.getMessage("activity.status.notAvailable"));
            }

        }

        if (activityEntity.getActivityState() == 2) {
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.status.ended"));
        }
        return null;
    }

    /**
     * 校验指定活动观赛人数
     *
     * @param req
     * @return
     */
    public ActivityWatchUserNumRespDto checkWatchUserNum(AppActivityRateLimitDto req) {
        log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{},开始", req.getActivityId());
        ActivityWatchUserNumRespDto defaultDto = new ActivityWatchUserNumRespDto(true, -1, -1);
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.WATCH_USER_NUM_AND_SWITCH.getCode());
        if (sysConfig == null || !StringUtils.hasText(sysConfig.getConfigValue())) {
            log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{},观赛人数未配置", req.getActivityId());
            return defaultDto;
        }
        MainActivity mainActivity = mainActivityService.findById(req.getActivityId());
        List<String> noLimitTypes = List.of(MainActivityTypeEnum.OLD.getType(), MainActivityTypeEnum.RANK.getType(), MainActivityTypeEnum.PROP.getType()); //无人数限制活动类型
        if (mainActivity == null || noLimitTypes.contains(mainActivity.getMainType())) {
            //老活动不做限制
            log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{},老活动不做限制", req.getActivityId());
            return defaultDto;
        }

        //最大人数限制
        ActivityWatchUserNumRespDto result = new ActivityWatchUserNumRespDto();
        ActivityWatchUserModel activityWatchUserModel = JsonUtil.readValue(sysConfig.getConfigValue(), ActivityWatchUserModel.class);
        if (!activityWatchUserModel.getEnable()) {
            //活动观赛人数限制未开启
            log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{},活动观赛人数限制未开启", req.getActivityId());
            return defaultDto;
        }
        Integer limitUserNum = activityWatchUserModel.getNum();
        if (limitUserNum == null || limitUserNum <= -1) {
            //活动观赛人数无限制
            log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{},活动观赛人数无限制", req.getActivityId());
            return defaultDto;
        }

        //活动参赛最大人数
        Integer activityUserNmb = 0;
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (1 == Optional.ofNullable(entryGameplay.getCompetitionFormat()).orElse(0)) {  //比赛形式：0：个人，1：团体
            //团队赛
            List<ActivityTeam> activityTeams = activityTeamService.getTeamsByActivityId(req.getActivityId());
            if (!CollectionUtils.isEmpty(activityTeams)) {
                activityUserNmb = activityTeams.stream().map(ActivityTeam::getMaxNum).reduce(0, Integer::sum);
            }
        } else {
            //个人赛
            activityUserNmb = mainActivity.getEnterLimit();
        }
        if (activityUserNmb <= 0) {
            //活动参赛最大人数无限制
            log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{},活动参赛最大人数无限制", req.getActivityId());
            return defaultDto;
        }
        result.setMaxUserNum(limitUserNum);

        //查询房间内用户数量
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        Integer roomId = roomIdBizService.getRoomIdByActIdGoal(mainActivity.getId(), subActivity.getTarget());
        List<SocketRoomUserVo.UserVo> roomUserVoList = socketPushUtils.getOnLineRobot(Long.valueOf(roomId));
        int onlineTotalUserNum = -1;
        int onlineWatchUserNum = 0;
        if (!CollectionUtils.isEmpty(roomUserVoList)) {
            onlineTotalUserNum = roomUserVoList.size();
            long count = roomUserVoList.stream().filter(itme -> Optional.ofNullable(itme.getIsWatch()).orElse(0) == 1).count();
            onlineWatchUserNum = Integer.parseInt(count + "");
        }
        result.setOnlineTotalUserNum(onlineTotalUserNum);
        result.setOnlineWatchUserNum(onlineWatchUserNum);

        //最大观赛人数 = 限制总人数 - 参赛最大人数
        int maxWatchUserNum = limitUserNum - activityUserNmb;
        result.setMaxWatchUserNum(Math.max(maxWatchUserNum, 0));

        //已占用人数 = 参赛最大人数 + 在线观赛人数
        int intoUserNum = activityUserNmb + onlineWatchUserNum;
        result.setCanIntoRoom(limitUserNum > intoUserNum);

        log.info("ConsloeActivityManager#checkWatchUserNum-----校验指定活动观赛人数,活动id:{}，返回结束：{} 结束", req.getActivityId(), result);
        return result;
    }


    public ActivityAwardReviewDto reviewStatusGet(Long mainActivityId) {
        return mainActivityBizService.reviewStatusGet(mainActivityId);
    }

    /**
     * 用户报名前第一步检查
     *
     * @param mainActivityId
     * @param user
     * @return
     */
    public SignupFirstCheckResponseDto signupFirstCheck(Long mainActivityId, ZnsUserEntity user) {
        SignupFirstCheckResponseDto result = new SignupFirstCheckResponseDto();
        //校验国家
        List<ActivityArea> activityAreas = activityAreaService.findByActId(mainActivityId);
        if (!CollectionUtils.isEmpty(activityAreas) &&
                !org.springframework.util.StringUtils.hasText(user.getCountryCode())) {
            //指定区域性活动 并且用户国家为空，弹框提示用户修改地区
            result.setPopType(1);
            result.setEquipmentPopMsg(I18nMsgUtils.getMessage("activity.enroll.country.isnull"));
        }
        return result;
    }

    public String getShareUrl(Long activityId, String jumpUrl, String languageCode) {

        return doGetShareUrl(activityId, jumpUrl, languageCode);
    }

    private String doGetShareUrl(Long activityId, String jumpUrl, String languageCode) {
        String url = "";
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, languageCode);
        if (disseminate != null) {
            String img = StringUtils.hasText(disseminate.getSharePic()) ? disseminate.getSharePic() : disseminate.getDisseminatePics();
            String content = StringUtils.hasText(disseminate.getShareContent()) ? disseminate.getShareContent() : I18nMsgUtils.getMessage("activity_share_title", disseminate.getTitle());
            String html = sysConfigService.selectConfigByKey("activity_share_template");

            String message = MessageFormat.format(html, content, img, jumpUrl, jumpUrl);
            log.info("生成的html为{}", message);

            MultipartFile multipartFile = new MockMultipartFile("share.html", "share.html", "application/html", message.getBytes());
            Map<String, Object> map = AwsUtil.putS3Object(multipartFile, "html");
            url = map.get("url").toString();
            log.info("文件访问地址" + map.get("url"));

        }
        return url;
    }

    public ApiActivityEquipmentInfoDto queryActivityEquipmentInfo(Long mainActivityId, Long userId, ZnsUserEntity loginUser) {
        ApiActivityEquipmentInfoDto apiActivityEquipmentInfoDto = new ApiActivityEquipmentInfoDto();
        if (userId != null) {
            apiActivityEquipmentInfoDto.setOtherEquipmentInfo(queryEquipmentInfo(mainActivityId, userId));
        }
        if (loginUser != null) {
            apiActivityEquipmentInfoDto.setMyEquipmentInfo(queryEquipmentInfo(mainActivityId, loginUser.getId()));
        }

        return apiActivityEquipmentInfoDto;
    }

    private EquipmentInfoDto queryEquipmentInfo(Long mainActivityId, Long userId) {
        EquipmentInfoDto equipmentInfoDto = null;
        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(mainActivityId, userId);
        if (activityUser != null && activityUser.getRunDataDetailsId() != null) {
            ZnsUserRunDataDetailsEntity userRunDataDetailsEntity = userRunDataDetailsService.findById(activityUser.getRunDataDetailsId());

            if (userRunDataDetailsEntity != null && userRunDataDetailsEntity.getIsRobot() == 0) {
                equipmentInfoDto = new EquipmentInfoDto();
                Long treadmillId = userRunDataDetailsEntity.getTreadmillId();
                ZnsTreadmillEntity treadmill = treadmillService.findById(treadmillId);

                ZnsUserEntity user = userService.findByIdWithoutLogicDelete(userId);
                if (user != null) {
                    equipmentInfoDto.setUsername(user.getFirstName());
                }
                equipmentInfoDto.setEquipmentNo(treadmill.getUniqueCode());
                if (!"unknown".equals(treadmill.getBrand())) {
                    equipmentInfoDto.setBrand(treadmill.getBrand());
                }

                ZnsEquipmentProductionBatchEntity productionBatchEntity = znsEquipmentProductionBatchService.selectByBatchNumber(treadmill.getBatchNumber());
                equipmentInfoDto.setEquipmentType(EquipmentTypeEnum.findByType(productionBatchEntity.getEquipmentType()).name);
                equipmentInfoDto.setEquipmentTypeV2(productionBatchEntity.getEquipmentType());
                if (Objects.nonNull(productionBatchEntity.getResistanceNum())) {
                    // 档位设置
                    equipmentInfoDto.setMinResistanceNum(1);
                    equipmentInfoDto.setMaxResistanceNum(productionBatchEntity.getResistanceNum());
                }

                equipmentInfoDto.setMaxSpeedMph(SportsDataUnit.kphToMph(productionBatchEntity.getMaxMaxSpeed()));
                equipmentInfoDto.setMaxSpeedKph(productionBatchEntity.getMaxMaxSpeed());

                equipmentInfoDto.setEquipmentModel(treadmill.getProductCode());
                equipmentInfoDto.setEquipmentVersion(userRunDataDetailsEntity.getFirmwareVersion());

            } else if (userRunDataDetailsEntity != null && userRunDataDetailsEntity.getIsRobot() == 1) {
//                //设置机器人的规则
                ZnsEquipmentProductionBatchEntity productionBatchEntity = znsEquipmentProductionBatchService.selectRobotEquipmentByActivity(mainActivityId);
                if (productionBatchEntity == null) {
                    return null;
                }
                equipmentInfoDto = new EquipmentInfoDto();
                equipmentInfoDto.setBrand("DeerRun");
                equipmentInfoDto.setUsername(activityUser.getNickname());
                equipmentInfoDto.setEquipmentModel(productionBatchEntity.getTreadmillModel());
                equipmentInfoDto.setMaxSpeedKph(productionBatchEntity.getMaxMaxSpeed());
                equipmentInfoDto.setMaxSpeedMph(SportsDataUnit.kphToMph(productionBatchEntity.getMaxMaxSpeed()));


                String treadmillModel = productionBatchEntity.getTreadmillModel();
                if (treadmillModel.startsWith("AS") || treadmillModel.startsWith("BA")) {
                    equipmentInfoDto.setEquipmentType("TREADMILL");
                } else if (treadmillModel.startsWith("CT")) {
                    equipmentInfoDto.setEquipmentType("W_2_in_1");
                } else {
                    equipmentInfoDto.setEquipmentType("TREADMILL");
                }
                equipmentInfoDto.setEquipmentVersion(equipmentVersionservice.findMaxVersion());

            }

        }

        return equipmentInfoDto;
    }

    public void cleanupAward(MyRaceDto myRaceDto, Integer appVersion) {
        if (Objects.isNull(myRaceDto) || CollectionUtils.isEmpty(myRaceDto.getAwardList())) {
            return;
        }
        if (appVersion < 4023) {
            if (myRaceDto != null) {
                myRaceDto.getAwardList().removeIf(targetAward -> AwardSentTypeEnum.SURPASS_AWARD.getType().equals(targetAward.getType()));
            }

        }
    }

    public ActivityWatchInfoRespDto getWatchInfoRespDto(Long activityId, ZnsUserEntity user) {
        ActivityWatchInfoRespDto activityWatchInfoRespDto = new ActivityWatchInfoRespDto();
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.isNull(mainActivity)) {
            return activityWatchInfoRespDto;
        }
        //目标
        List<Integer> goals = subActivityService.getAllSingleActByMain(activityId).stream().map(SubActivity::getTarget)
                .sorted().toList();
        //房间号
        activityWatchInfoRespDto.setWatchRoomId(roomIdBizService.getRoomId(mainActivity, goals));

        if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SERIES_MAIN.getType())) {
            //系列赛观赛只有单关卡
            SeriesActivityRel seriesActivityRel = seriesActivityRelService.findByQuery(new SeriesActivityRelQuery().setParentActivityId(activityId).setLevel(1));
            mainActivity = mainActivityService.findById(seriesActivityRel.getSegmentActivityId());
        }
        activityWatchInfoRespDto.setMainActivityId(mainActivity.getId());
        Boolean isStageActivity = Boolean.FALSE;
        List<ActivityStage> stageList = activityStageService.findByActId(activityId);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of(mainActivity.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
        ZonedDateTime start = ZonedDateTime.parse(mainActivity.getActivityStartTime(), formatter);
        ZonedDateTime end = ZonedDateTime.parse(mainActivity.getActivityEndTime(), formatter);

        activityWatchInfoRespDto.setSegmentActivityStartTime(start);
        if (!CollectionUtils.isEmpty(stageList)) {
            isStageActivity = Boolean.TRUE;
        }
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("MMM d 'at' HH:mm", Locale.ENGLISH).withZone(ZoneId.of(user.getZoneId()));

        //能否观赛判断
        if (Objects.equals(mainActivity.getActivityState(), MainActivityStateEnum.NOT_STARTED.getCode()) && ZonedDateTime.now().isBefore(start.plusMinutes(-mainActivity.getWaitTime()))) {
            String formattedTime = start.format(sdf);
            activityWatchInfoRespDto.setCanWatchGame(false);
            activityWatchInfoRespDto.setFailWatchToast(I18nMsgUtils.getMessage("activity.watch.game.not.start", formattedTime));
            return activityWatchInfoRespDto;
        }

        if (Objects.equals(mainActivity.getActivityState(), MainActivityStateEnum.ENDED.getCode()) || Objects.equals(mainActivity.getActivityState(), MainActivityStateEnum.OFF_SHELF.getCode()) || ZonedDateTime.now().isAfter(end)) {
            activityWatchInfoRespDto.setCanWatchGame(false);
            activityWatchInfoRespDto.setFailWatchToast(I18nMsgUtils.getMessage("activity.watch.game.expire"));
            return activityWatchInfoRespDto;
        }

        if (isStageActivity) {
            //找到第一个阶段
            Optional<ActivityStage> activityStage = stageList.stream().filter(s -> Objects.equals(s.getLevel(), 1)).findFirst();
            if (activityStage.isPresent() && ZonedDateTime.now().isBefore(activityStage.get().getStartTime().minusMinutes(mainActivity.getWaitTime()))) {
                activityWatchInfoRespDto.setFailWatchToast(I18nMsgUtils.getMessage("activity.watch.game.not.start", activityStage.get().getStartTime().toInstant().atZone(ZoneId.of(user.getZoneId())).format(sdf)));
                activityWatchInfoRespDto.setCanWatchGame(false);
                return activityWatchInfoRespDto;
            }
            //找到最后一阶段，判断时间是否结束
            Optional<ActivityStage> lastStage = stageList.stream().filter(s -> Objects.equals(s.getLevel(), stageList.size())).findFirst();
            if (lastStage.isPresent() && ZonedDateTime.now().isAfter(lastStage.get().getEndTime())) {
                activityWatchInfoRespDto.setCanWatchGame(false);
                activityWatchInfoRespDto.setFailWatchToast(I18nMsgUtils.getMessage("activity.watch.game.expire"));
                return activityWatchInfoRespDto;
            }
            //前面条件都不满足，说明当前时间在阶段内
            //找出当前阶段
            ActivityStage currentStage = getCurrentStage(stageList, mainActivity);

            if (Objects.nonNull(currentStage)) {
                activityWatchInfoRespDto.setLevel(currentStage.getLevel());
                activityWatchInfoRespDto.setSegmentActivityStartTime(ZonedDateTimeUtil.convertFrom(currentStage.getStartTime()));
            } else {
                //说明是在上阶段结束 -> 下一阶段候场时间内
                Optional<ActivityStage> min = stageList.stream().filter(s -> ZonedDateTime.now().isBefore(s.getStartTime())).min(Comparator.comparing(ActivityStage::getLevel));
                if (min.isPresent()) {
                    Integer currentLevel = min.get().getLevel();
                    Integer preLevel = currentLevel - 1;
                    activityWatchInfoRespDto.setFailWatchToast(I18nMsgUtils.getMessage("activity.watch.game.X.expire", preLevel, currentLevel, min.get().getStartTime().toInstant().atZone(ZoneId.of(user.getZoneId())).format(sdf)));
                }
                activityWatchInfoRespDto.setCanWatchGame(false);
                return activityWatchInfoRespDto;
            }
        }
        activityWatchInfoRespDto.setCanWatchGame(true);

        //排名
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            SeriesGameplay seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
        } else {
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            activityWatchInfoRespDto.setRankingBy(entryGameplay.getRankingBy());
        }

        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, user.getLanguageCode());
        activityWatchInfoRespDto.setActivityTitle(Objects.nonNull(disseminate) ? disseminate.getTitle() : "");

        //路线
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
        if (mainActivity.isSeriesMain()) {
            //系列赛使用第一个子赛事的id
            List<Long> refActivityIds = seriesActivityRelService.findSubActivityId(mainActivity.getId());
            if (!CollectionUtils.isEmpty(refActivityIds)) {
                Optional<Long> first1 = refActivityIds.stream().sorted().findFirst();
                SubActivity subActivityNew = subActivityService.getSingleActByMain(first1.get());
                if (Objects.nonNull(subActivityNew)) {
                    activityWatchInfoRespDto.setRouteId(subActivityNew.getRouteId());
                }
            }
        } else {
            activityWatchInfoRespDto.setRouteId(subActivity.getRouteId());
        }

        //观战目标
        if (Objects.equals(mainActivity.getTargetType(), 1)) {
            activityWatchInfoRespDto.setWatchRoomMileageGoal(goals.get(0));
        } else {
            activityWatchInfoRespDto.setWatchRoomTimeGoal(goals.get(0));
        }
        activityWatchInfoRespDto.setTargetType(mainActivity.getTargetType());
        activityWatchInfoRespDto.setMainType(mainActivity.getMainType());
        //回显开赛时间限制
        Optional<ActivityParams> optional = activityParamsService.findOneByMainActivityAndParamType(mainActivity.getId(), ActivitySettingConfigEnum.AFTER_START_LIMIT);
        optional.ifPresent(k -> activityWatchInfoRespDto.setAfterStartLimit(Integer.parseInt(k.getParamValue())));

        return activityWatchInfoRespDto;
    }

    public ActivityStage getCurrentStage(List<ActivityStage> stageList, MainActivity mainActivity) {
        for (ActivityStage stage : stageList) {
            if (ZonedDateTime.now().isAfter(stage.getStartTime().minusMinutes(mainActivity.getWaitTime())) && ZonedDateTime.now().isBefore(stage.getEndTime())) {
                log.info("当前阶段:{}", stage.getId());
                return stage;
            }
        }
        return null;
    }

    public ActivityAwardPopRespDto awardPopInfo(Long mainActivityId, ZnsUserEntity loginUser) {
        ActivityAwardPopRespDto activityAwardPopRespDto = new ActivityAwardPopRespDto();
//        Boolean marathon = sysConfigService.isMarathon(mainActivityId);
//        if (!marathon) {
//            return activityAwardPopRespDto;
//        }
//        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(mainActivityId, loginUser.getId());
//        if (Objects.nonNull(activityUser) && Objects.nonNull(activityUser.getTeamId())) {
//            String key = RedisConstants.ACTIVITY_TEAM_AWARD_POP + activityUser.getTeamId() + loginUser.getId();
//            boolean exists = redissonClient.getBucket(key).isExists();
//            //组队奖励弹窗
//            ActivityTeam activityTeam = activityTeamService.findByQuery(ActivityTeamQuery.builder().ids(List.of(activityUser.getTeamId())).build());
//            if (Objects.nonNull(activityTeam) && Objects.nonNull(activityTeam.getTeamFullTime()) && !exists) {
//                AwardPopDto teamAward = new AwardPopDto();
//                Integer sort = activityTeamService.findFullSort(activityTeam.getActivityId(), activityTeam.getTeamFullTime());
//                //队伍人数
//                if (1 <= sort && sort <= 10) {
//                    teamAward.setTitleCount(sort);
//                    teamAward.setScore((int) Math.ceil(10000.0 / activityTeam.getCurrentNum()));
//                    activityAwardPopRespDto.setTeamAwardPop(teamAward);
//                } else if (sort > 10 && sort <= 30) {
//                    teamAward.setTitleCount(sort);
//                    teamAward.setScore((int) Math.ceil(6000.0 / activityTeam.getCurrentNum()));
//                    activityAwardPopRespDto.setTeamAwardPop(teamAward);
//                }
//                if (Objects.nonNull(teamAward.getScore()) && teamAward.getScore() > 0) {
//                    //发积分
//                    Long detailId = activityUserScoreService.increaseAmount(teamAward.getScore(), activityUser.getActivityId(), activityUser.getUserId(), sort, 0, ScoreConstant.SourceTypeEnum.source_type_36.getType());
//                    redissonClient.getBucket(key).set(1, 90, TimeUnit.DAYS);
//                }
//            }
//        }
//
//        //邀请成功弹窗
//        List<MarathonInvitationRecordsDo> list = marathonInvitationRecordsService.findList(new MarathonInvitationRecordsQuery().setInviterUserId(loginUser.getId()).setInviteeType(MarathonInviteeTypeStatus.NEW.getCode()).setIsInviteeVerified(1).setActivityId(mainActivityId));
//        if (!CollectionUtils.isEmpty(list)) {
//            InvitationNewUserPopDo invitationNewUserPopDo = invitationNewUserPopService.findByQuery(new InvitationNewUserPopQuery().setUserId(loginUser.getId()).setActivityId(mainActivityId));
//
//            int currentTotal = list.size();
//            int alreadySent = (invitationNewUserPopDo != null) ? invitationNewUserPopDo.getNums() : 0;
//
//            // 计算新增人数（不超过上限4人）
//            int newUsers = Math.max(0, currentTotal - alreadySent);
//            int validNewUsers = Math.min(newUsers, 4 - alreadySent);
//
//            // 计算奖励金额
//            BigDecimal totalReward = BigDecimal.ZERO;
//
//            for (int i = 1; i <= validNewUsers; i++) {
//                int position = alreadySent + i;
//                BigDecimal currentReward = switch (position) {
//                    case 1 -> new BigDecimal("2");
//                    case 2 -> new BigDecimal("3");
//                    case 3, 4 -> new BigDecimal("5");
//                    default -> BigDecimal.ZERO;
//                };
//                totalReward = totalReward.add(currentReward); // 累加操作
//            }
//            // 更新奖励弹窗信息
//            if (totalReward.compareTo(BigDecimal.ZERO) > 0) {
//                AwardPopDto teamAward = new AwardPopDto();
//                teamAward.setTitleCount(validNewUsers);
//                teamAward.setAmount(totalReward);
//
//                // 更新已发放人数记录（例如：原已发放2人，本次新增2人 → 更新为4人）
//                if (invitationNewUserPopDo == null) {
//                    InvitationNewUserPopDo newUserPopDo = new InvitationNewUserPopDo();
//                    newUserPopDo.setUserId(loginUser.getId());
//                    newUserPopDo.setActivityId(mainActivityId);
//                    newUserPopDo.setNums(validNewUsers);
//                    invitationNewUserPopService.create(newUserPopDo);
//                } else {
//                    invitationNewUserPopDo.setNums(validNewUsers + invitationNewUserPopDo.getNums());
//                    invitationNewUserPopService.update(invitationNewUserPopDo);
//                }
//                activityAwardPopRespDto.setInviteAwardPop(teamAward);
//                String key = "invite_newUser_award" + mainActivityId + loginUser.getId();
//                RLock lock = redissonClient.getLock(key);
//                try {
//                    if (lock.tryLock(1, 5, TimeUnit.SECONDS)) {
//                        userAccountService.increaseAmount(totalReward, loginUser.getId(), true);
//                        znsUserAccountDetailService.addAccountDetail(loginUser.getId(), 1, AccountDetailTypeEnum.INVITE_NEWUSER_JOIN_TEAM, AccountDetailSubtypeEnum.NEW_ACTIVITY_AWARD, totalReward, mainActivityId, "Invitation Reward");
//                    }
//                } catch (Exception e) {
//                    log.error("获取邀请奖励发放锁失败", e);
//                    return activityAwardPopRespDto;
//                } finally {
//                    if (lock.isHeldByCurrentThread()) {
//                        lock.unlock();
//                    }
//                }
//            }
//        }
        return activityAwardPopRespDto;
    }

    /**
     * 将用户加入俱乐部
     */
    private void addUserToClub(Long userId, Long clubId, String joinedClubParamKey) {
        try {
            // 检查用户是否已经是俱乐部成员
            ClubMember existingMember = clubMemberService.findByClubAndUserId(clubId, userId).orElse(null);
            if (Objects.isNull(existingMember)) {
                // 保存用户加入的俱乐部ID到用户额外参数
                UserExtraParamsDo userExtraParams = new UserExtraParamsDo();
                userExtraParams.setUserId(userId);
                userExtraParams.setParamKey(joinedClubParamKey);
                userExtraParams.setParamValue(clubId.toString());
                userExtraParamsService.create(userExtraParams);
            }
            // 添加用户到俱乐部
            String env = (envProfile.equals("prod") || envProfile.equals("pre")) ? "prod" : envProfile;//由于预发和线上使用同数据库
            String clubGroupId = ApiConstants.clubGroupId + clubId + env;
            clubMemberService.addMember(userId, clubId, ClubMemberRoleEnum.MEMBER, clubGroupId);
            clubService.updateMemberCount(clubId);
        } catch (Exception e) {
            log.error("加入赛事俱乐部失败, userId: {}, clubId: {}", userId, clubId, e);
        }
    }

    /**
     * 生成下一个俱乐部名称（原名字序号+1）
     */
    private String generateNextClubName(String originalName) {
        char lastChar = originalName.charAt(originalName.length() - 1);
        if (Character.isDigit(lastChar)) {
            String baseName = originalName.substring(0, originalName.length() - 1);
            String numberStr = originalName.substring(originalName.length() - 1);
            int number = Integer.parseInt(numberStr);
            return baseName + (number + 1);
        } else {
            return originalName + "2";
        }
    }

    /**
     * 更新竞技俱乐部配置
     */
    private void updateCompetitiveClubConfig(Map<String, Long> userJoinCompetitiveClubMap) {
        try {
            SysConfig sysConfig = new SysConfig();
            sysConfig.setConfigKey(ConfigKeyEnums.USER_JOIN_COMPETITIVE_CLUB.getCode());
            sysConfig.setConfigValue(JsonUtil.writeString(userJoinCompetitiveClubMap));
            sysConfigService.updateByConfigKey(sysConfig);
            log.info("更新竞技俱乐部配置成功: {}", userJoinCompetitiveClubMap);
        } catch (Exception e) {
            log.error("更新竞技俱乐部配置失败: {}", userJoinCompetitiveClubMap, e);
        }
    }

}
