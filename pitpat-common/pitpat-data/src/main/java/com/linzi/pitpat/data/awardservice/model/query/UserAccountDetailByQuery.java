package com.linzi.pitpat.data.awardservice.model.query;

import com.linzi.pitpat.data.awardservice.constant.enums.AccountConstant;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3 14:57
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserAccountDetailByQuery {

    private Long userId;

    private Long activityId;

    private Long userAccountId;
    /**
     * 收支类型：1表示收入，2表示支出
     */
    private Integer type;
    /**
     * 交易类型: 比如1表示跑步活动奖励
     */
    private List<Integer> tradeType;
    /**
     * 子交易类型
     */
    private List<Integer> tradeSubtype;
    /**
     * 交易状态：0表示交易发起申请，1表示交易处理中，2表示交易结束，-1表示交易失败 3:待领取
     *
     * @see AccountConstant.TradeStatusEnum
     */
    private Integer tradeStatus;
    //活动类型
    private Integer activityType;

    private List<Long> userIds;

    private List<Long> activityIds;
    private ZonedDateTime startTime;
    private ZonedDateTime endTime;

}
