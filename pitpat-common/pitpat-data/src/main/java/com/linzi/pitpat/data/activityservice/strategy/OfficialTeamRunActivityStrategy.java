package com.linzi.pitpat.data.activityservice.strategy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityDao;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityCouponDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialTeamActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.activityservice.model.vo.TeamRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserAccountDetailSub;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserFriendEntity;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.data.vo.home.HomepageActivityVo;
import com.linzi.pitpat.data.vo.report.MyGradesDto;
import com.linzi.pitpat.data.vo.report.TeamRunReportVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 官方组队跑
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Service
@Slf4j
public class OfficialTeamRunActivityStrategy extends BaseOfficialActivityStrategy {
    @Resource
    private UserCouponService userCouponService;

    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;

    @Autowired
    private ZnsRunActivityDao znsRunActivityDao;

    @Autowired
    private ZnsUserAccountService znsUserAccountService;

    @Resource
    private UserPropRecordService userPropRecordService;

    @Override
    public void wrapperRunActivityBasicData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity) {
        super.wrapperRunActivityBasicData(activityEntity, activityDetailVO, userEntity);
        //查询参赛限制
        String contestCountLimit = sysConfigService.selectConfigByKey("activity.contest.count.limit");
        if (StringUtil.isEmpty(contestCountLimit) || "-1".equals(contestCountLimit)) {
            return;
        }
        //跑步类型不限的活动，不考虑报名活动次数
//        if(Objects.equals(activityEntity.getRunWalkStatus(), RunActivityRaceTypeEnum.UNLIMITED.getCode())){
//            return;
//        }
        //查询用户今日参赛次数
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(ZoneId.of("GMT-8")));
        ZonedDateTime startOfDate = DateUtil.getStartOfDate(now, TimeZone.getTimeZone(ZoneId.of("GMT-8")));
        ZonedDateTime endOfDate = DateUtil.getEndOfDate(now, TimeZone.getTimeZone(ZoneId.of("GMT-8")));


        Integer activityUserCount = runActivityUserService.findActivityUserCountByUserId(userEntity.getId(), startOfDate, endOfDate, Arrays.asList(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType()), null, activityEntity.getRunWalkStatus());
        Integer surplusContestCount = Integer.valueOf(contestCountLimit) - activityUserCount;
        if (surplusContestCount < 0) {
            surplusContestCount = 0;
        }
        activityDetailVO.setSurplusContestCount(surplusContestCount);
    }

    @Override
    public void wrapperRunActivityDetailData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        super.wrapperRunActivityDetailData(activityEntity, activityDetailVO, userEntity, oneself);
        // 活动要求
        String activityRequire = String.valueOf(jsonObjectConfig.get(ApiConstants.ACTIVITY_REQUIRE));
        activityDetailVO.setActivityRequire(activityRequire);
        List<Map> runningGoalsAward = getRunningGoalsAward(jsonObjectConfig);
        activityDetailVO.setRunningGoalsAward(runningGoalsAward);
        activityDetailVO.setRunningGoalScoreAward(getRunningGoalsScoreAward(jsonObjectConfig, ApiConstants.RUNNING_GOAL_SCORE_AWARD));
        activityDetailVO.setRunningGoalCouponAward(getRunningGoalsScoreAward(jsonObjectConfig, ApiConstants.RUNNING_GOAL_COUPON_AWARD));
        log.info("jsonObjectConfig. RunningGoalCouponAward = " + JsonUtil.writeString(activityDetailVO.getRunningGoalCouponAward()));
        activityDetailVO.setMaxRunScore(MapUtil.getInteger(jsonObjectConfig.get("maxRunScore"), -1));
        activityDetailVO.setMinRunScore(MapUtil.getInteger(jsonObjectConfig.get("minRunScore"), -1));
        //ZnsUserAccountEntity oneAccountEntity = userAccountService.getByUserId(oneself.getUserId());
        ZnsUserAccountEntity userAccountEntity = userAccountService.getByUserId(userEntity.getId());

        //奖励计算
        BigDecimal activityEntryFee = activityEntity.getBonusRuleType() == 2 ? activityEntity.getActivityEntryFee() : BigDecimal.ZERO;
        BigDecimal goalAward = BigDecimal.ZERO;
        BigDecimal maxReward = BigDecimal.ZERO;
        if (Objects.nonNull(oneself)) {
            for (Map map : activityDetailVO.getRunningGoalsAward()) {
                Integer goal = MapUtils.getInteger(map, "goal");
                if (activityEntity.getCompleteRuleType() == 1) {
                    if (goal.equals(oneself.getTargetRunMileage())) {
                        goalAward = new BigDecimal(MapUtils.getDouble(map, "award", 0d));
                        BigDecimal firstAward = new BigDecimal(MapUtils.getDouble(map, "firstAward", 0d));
                        maxReward = goalAward.add(firstAward);
                    }
                } else {
                    if (goal.equals(oneself.getTargetRunTime())) {
                        goalAward = new BigDecimal(MapUtils.getDouble(map, "award", 0d));
                        BigDecimal firstAward = new BigDecimal(MapUtils.getDouble(map, "firstAward", 0d));
                        maxReward = goalAward.add(firstAward);
                    }
                }
            }
            //房间号处理
            Integer roomNumber = 0;
            if (activityDetailVO.getCompleteRuleType() == 1) {
                activityDetailVO.setRunMileage(new BigDecimal(oneself.getTargetRunMileage()));
                roomNumber = NumberUtils.getGoalImNumber(activityEntity.getId(), oneself.getTargetRunMileage(), activityEntity.getCompleteRuleType()).intValue();
            } else {
                activityDetailVO.setRunTime(oneself.getTargetRunTime());
                roomNumber = NumberUtils.getGoalImNumber(activityEntity.getId(), oneself.getTargetRunTime(), activityEntity.getCompleteRuleType()).intValue();
            }
            activityDetailVO.setRoomNumber(roomNumber);

            //查询已获得的名次奖励
            UserAccountDetailSub rankDetails = userAccountDetailSubService.selectUserAccountDetailSubBy(userEntity.getId(), activityEntity.getId(), 4);
            BigDecimal rankAward = BigDecimal.ZERO;
            if (Objects.nonNull(rankDetails)) {
                rankAward = rankDetails.getAmount();
            }
            //券列表和升级金额处理
            List<Integer> couponTypes = Arrays.asList(CouponTypeEnum.WIN_COUPON.getCode(), CouponTypeEnum.DOUBLE_REWARD_COUPON.getCode());
            UserCoupon useCoupon = userCouponService.getUserCouponByActivityAndUserId(activityEntity.getId(), userEntity.getId(), couponTypes);
            if (Objects.nonNull(useCoupon)) {
                activityDetailVO.setExpandAward(useCoupon.getAmount());
                activityDetailVO.setCouponId(useCoupon.getId());
                activityDetailVO.setIsComplete(oneself.getIsComplete());
            } else if (Objects.nonNull(oneself.getCompleteTime())) {
                List<CouponPageVo> couponPageVos = userCouponService.selectUserCouponList(userEntity.getId(), 0, Arrays.asList(1, 2));
                couponPageVos = couponPageVos.stream().filter(c -> oneself.getCompleteTime().compareTo(c.getGmtCreate()) > 0).collect(Collectors.toList());
                for (CouponPageVo couponPageVo : couponPageVos) {
                    if (couponPageVo.getCouponType() == 1) {
                        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
                        List<Map> runningGoalsAwardList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
                        Map<Integer, Map> runningGoalsAwardMap = runningGoalsAwardList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
                        Map awardMap = runningGoalsAwardMap.get(oneself.getTargetRunMileage());
                        Double firstAward = MapUtils.getDouble(awardMap, ApiConstants.FIRST_AWARD, 0d);
                        couponPageVo.setAmount(new BigDecimal(firstAward).subtract(rankAward));
                        if (couponPageVo.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                            couponPageVo.setAmount(BigDecimal.ZERO);
                        }
                    } else if (couponPageVo.getCouponType() == 2 && oneself.getRunAward().compareTo(BigDecimal.ZERO) > 0) {
                        couponPageVo.setAmount(oneself.getRunAward());
                    }
                }
                activityDetailVO.setUserCouponVoList(couponPageVos);
            }
        }
        BigDecimal completeAward = activityEntryFee.add(goalAward);
        completeAward = completeAward.setScale(2, BigDecimal.ROUND_HALF_DOWN);
        completeAward = I18nConstant.currencyFormat(userAccountEntity.getCurrencyCode(), completeAward);
        activityDetailVO.setCompleteAward(completeAward.toString());
        maxReward = I18nConstant.currencyFormat(userAccountEntity.getCurrencyCode(), maxReward);
        activityDetailVO.setMaxReward(maxReward);
        //总奖金池计算
        calculateTotalBonus(activityEntity, activityDetailVO, activityEntryFee);
        activityDetailVO.setActivityTotalBonus(I18nConstant.currencyFormat(userAccountEntity.getCurrencyCode(), activityDetailVO.getActivityTotalBonus()));

        // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
        long teamMillisecond = getLastEnterRunTime(jsonObjectConfig);
        activityDetailVO.setLastEnterTeamRunTime(Long.valueOf(teamMillisecond));
        // 组队跑开始跑前多少分钟可入场(配置分钟)
        Integer beforeEnterMinutes = MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
        if (null == beforeEnterMinutes) {
            beforeEnterMinutes = 5;
        }
        long challengeMillisecond = beforeEnterMinutes.intValue() * 60000;
        activityDetailVO.setRunBeforeEnter(challengeMillisecond);

        Iterator<Map> iterator = runningGoalsAward.iterator();
        while (iterator.hasNext()) {
            Map map = iterator.next();
            BigDecimal award = new BigDecimal(MapUtils.getDoubleValue(map, "award", 0d));
            if (award.compareTo(BigDecimal.ZERO) == 0) {
                map.remove("award");
            }
            BigDecimal firstAward = new BigDecimal(MapUtils.getDouble(map, "firstAward", 0d));
            if (firstAward.compareTo(BigDecimal.ZERO) == 0) {
                map.remove("firstAward");
            }
            BigDecimal secondAward = new BigDecimal(MapUtils.getDouble(map, "secondAward", 0d));
            if (secondAward.compareTo(BigDecimal.ZERO) == 0) {
                map.remove("secondAward");
            }
            BigDecimal thirdAward = new BigDecimal(MapUtils.getDouble(map, "thirdAward", 0d));
            if (thirdAward.compareTo(BigDecimal.ZERO) == 0) {
                map.remove("thirdAward");
            }

            for (int i = 4; i <= 10; i++) {
                String key = i + "";
                Double awardDouble = MapUtils.getDouble(map, key, 0d);
                if (Objects.isNull(awardDouble)) {
                    continue;
                }
                BigDecimal rankAward = new BigDecimal(awardDouble);
                if (rankAward.compareTo(BigDecimal.ZERO) == 0) {
                    map.remove(key);
                }
            }

            if (map.size() == 1) {
                iterator.remove();
            }
        }

        activityDetailVO.setRunningGoalsAward(runningGoalsAward);
        activityDetailVO.setWarmPrompt(getWarmPrompt(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType()));
    }

    @Override
    public void wrapperRunActivityDataDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
        super.wrapperRunActivityDataDetail(activityEntity, activityDetailVO, userId);
        RunActivityUserVO oneself = activityDetailVO.getActivityUsers().stream().filter(u -> u.getUserId().equals(userId)).findFirst().orElse(null);

    }

    private void calculateTotalBonus(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, BigDecimal activityEntryFee) {
        List<RunActivityUserVO> activityUsers = activityDetailVO.getActivityUsers();
        if (CollectionUtils.isEmpty(activityUsers)) {
            return;
        }
        BigDecimal totalBonus = BigDecimal.ZERO;
        //最高奖金池=保证金金额*报名人数+sum（各个里程的完赛奖励*报名人数+各个里程的3个名次奖励）
        totalBonus = totalBonus.add(new BigDecimal(activityUsers.size()).multiply(activityEntryFee));
        //用户目标分组
        Map<Integer, List<RunActivityUserVO>> goalUserMap = new HashMap<>();
        if (activityEntity.getCompleteRuleType() == 1) {
            goalUserMap = activityUsers.stream().collect(Collectors.groupingBy(RunActivityUserVO::getTargetRunMileage));
        } else {
            goalUserMap = activityUsers.stream().collect(Collectors.groupingBy(RunActivityUserVO::getTargetRunTime));
        }

        for (Map map : activityDetailVO.getRunningGoalsAward()) {
            Double award = MapUtils.getDoubleValue(map, "award", 0.00);
            Integer goal = MapUtils.getInteger(map, "goal");
            List<RunActivityUserVO> runActivityUserVOS = goalUserMap.get(goal);
            if (!CollectionUtils.isEmpty(runActivityUserVOS)) {
                totalBonus = totalBonus.add(new BigDecimal(award).multiply(new BigDecimal(runActivityUserVOS.size())));
            }
            totalBonus = totalBonus.add(new BigDecimal(MapUtils.getDoubleValue(map, ApiConstants.FIRST_AWARD, 0.00)))
                    .add(new BigDecimal(MapUtils.getDoubleValue(map, ApiConstants.SECOND_AWARD, 0.00)))
                    .add(new BigDecimal(MapUtils.getDoubleValue(map, ApiConstants.THIRD_AWARD, 0.00)));
        }
        activityDetailVO.setActivityTotalBonus(totalBonus);
    }

    @Override
    protected BigDecimal getPreMaxReward(Integer activityType, Integer userCount) {
        BigDecimal maxReward = BigDecimal.ZERO;
        try {
            List<Map> runningGoalsAwardList = JsonUtil.readList(jsonObjectConfig.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
            if (CollectionUtils.isEmpty(runningGoalsAwardList)) {
                return maxReward;
            }
            maxReward = runningGoalsAwardList.stream().map(map -> {
                BigDecimal award = MapUtil.getBigDecimal(map.get("award"), BigDecimal.ZERO);
                BigDecimal firstAward = MapUtil.getBigDecimal(map.get("firstAward"), BigDecimal.ZERO);
                return award.add(firstAward);
            }).max((x1, x2) -> x1.compareTo(x2)).get();
        } catch (Exception e) {
            log.info("getPreMaxReward 异常，e：{}", e);
        }
        return maxReward;
    }

    /**
     * 官方多人同跑统计人数和头像
     *
     * @param activityEntity
     * @param activityDetailVO
     * @param userEntity
     * @param activityUserStatus
     */
    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {
        Long activityId = activityEntity.getId();
        // 报名人数（获取指定时长，公里数的报名人数）
        List<ZnsRunActivityEntity> sourceNotStartList = new ArrayList<>();
        ZnsRunActivityEntity recentInProgressActivity = null;
        List<ZnsRunActivityEntity> notStartCountList = znsRunActivityDao.selectActivityByBatchNoActivityStateDesc(activityEntity.getBatchNo(), Arrays.asList(0, 1));
        sourceNotStartList.addAll(notStartCountList);
        notStartCountList = notStartCountList.stream()
                .filter(item -> {
                    // 如果当前活动正在进行中，筛选掉超过最晚报名时间的活动
                    if (ActivityStateEnum.IN_PROGRESS.getState().equals(item.getActivityState())) {
                        ZonedDateTime end = item.getApplicationEndTime();
//                        ZonedDateTime testDate = DateUtil.addHours(ZonedDateTime.now(),-8);
                        if (ZonedDateTime.now().compareTo(end) > 0) {
                            log.info("活动：{}超过最晚可入场时间", item.getId());
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notStartCountList)) {
            for (ZnsRunActivityEntity ac : notStartCountList) {
                if (ActivityStateEnum.IN_PROGRESS.getState().equals(ac.getActivityState())) {
                    // 进行中的活动，取开始时间离当前最近的那场活动
                    if (recentInProgressActivity != null) {
                        if (ac.getActivityStartTime().isAfter(recentInProgressActivity.getActivityStartTime())) {
                            recentInProgressActivity = ac;
                        }
                    } else {
                        recentInProgressActivity = ac;
                    }

                }
            }
            if (Objects.nonNull(recentInProgressActivity)) {
                activityId = recentInProgressActivity.getId();
            } else {
                // 没有进行中活动，取第一个即可
                ZnsRunActivityEntity notStartActivityEntity = notStartCountList.get(0);
                activityId = notStartActivityEntity.getId();
            }

        } else {
            if (!CollectionUtils.isEmpty(sourceNotStartList)) {
                // 过滤前有数据，说明开始时间都已经过期了，取最后一个
                ZnsRunActivityEntity lastActivityEntity = sourceNotStartList.get(sourceNotStartList.size() - 1);
                activityId = lastActivityEntity.getId();
            }
        }
        activityEntity = znsRunActivityDao.selectById(activityId); // 重置为新的活动对象

        Page page = new Page(1, 20);
        List<RunActivityUserVO> runActivityUsers = activityUserManager.findRunActivityUsers(activityEntity, page, userEntity.getId(), activityDetailVO, activityDetailVO.getTargetRunMileage(), activityDetailVO.getTargetRunTime(), activityUserStatus);
        activityDetailVO.setActivityUsers(runActivityUsers);
        activityDetailVO.setActivityUsersTotal(page.getTotal());
        Integer enrollNum = runActivityUserService.countRunUser(activityEntity.getId(), Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(), ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState()), null, null);
        activityDetailVO.setEnrollNum(enrollNum);
        Integer partakeNum = runActivityUserService.countRunUser(activityEntity.getId(), Arrays.asList(ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState()), null, 60);
        activityDetailVO.setPartakeNum(partakeNum);
        Integer completeNum = runActivityUserService.countRunUser(activityEntity.getId(), Arrays.asList(ActivityUserStateEnum.ENDED.getState()), 1, null);
        activityDetailVO.setCompleteNum(completeNum);
    }

    @Override
    @Transactional
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        log.info("handleRunActivityEnd 开始执行 activityId=" + activityEntity.getId());
        super.handleRunActivityEnd(activityEntity);
        Integer completeRuleType = activityEntity.getCompleteRuleType();
        // 3. 开始给用户分奖励，官方活动都是已接受的
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId())
                .build());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> userIds = list.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        log.info("handleRunActivityEnd userId = {} ", userIds);
        List<ZnsUserEntity> userEntities = userService.findByIds(userIds);
        Map<Long, String> userMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, ZnsUserEntity::getFirstName));
        for (ZnsRunActivityUserEntity znsRunActivityUserEntity : list) {
            String firstName = userMap.get(znsRunActivityUserEntity.getUserId());
            if (StringUtils.hasText(firstName)) {
                znsRunActivityUserEntity.setNickname(firstName);
            }
            if (Objects.isNull(znsRunActivityUserEntity.getRunTimeMillisecond()) || znsRunActivityUserEntity.getRunTimeMillisecond() == 0) {
                znsRunActivityUserEntity.setRunTimeMillisecond(znsRunActivityUserEntity.getRunTime() * 1000 + 999);
            }
            // 减时间道具使用，活动状态结束，但用户未触发运动结束，则不使用道具
        }
        List<ZnsRunActivityUserEntity> tempList = BeanUtil.copyBeanList(list, ZnsRunActivityUserEntity.class);
        List<ZnsRunActivityUserEntity> completeUsers = new ArrayList<>();
        List<ZnsRunActivityUserEntity> noCompleteUsers = new ArrayList<>();
        for (ZnsRunActivityUserEntity userEntity : list) {
            if (completeRuleType == 1 && userEntity.getTargetRunMileage() > 0 && userEntity.getRunMileage().intValue() >= userEntity.getTargetRunMileage()) {
                userEntity.setIsComplete(1);
                userEntity.setCompleteTime(ZonedDateTime.now());
                userEntity.setSubState(1);
                completeUsers.add(userEntity);
            } else if (completeRuleType == 2 && userEntity.getTargetRunTime() > 0 && userEntity.getRunTime() >= userEntity.getTargetRunTime()) {
                userEntity.setIsComplete(1);
                userEntity.setCompleteTime(ZonedDateTime.now());
                userEntity.setSubState(1);
                completeUsers.add(userEntity);
            } else {
                userEntity.setSubState(2);
                noCompleteUsers.add(userEntity);
            }
        }
        //费用参与不退回
        BigDecimal userFees = activityEntity.getBonusRuleType() == 2 && activityEntity.getStatus() == 1 ? BigDecimalUtil.multiply(new BigDecimal(list.size()), activityEntity.getActivityEntryFee()) : BigDecimal.ZERO;
        log.info("费用参与 userFees = " + userFees);
        //跑步目标奖励
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        Map<String, Object> data = JsonUtil.readValue(activityEntity.getActivityConfig());
        log.info("当时的配置 activityConfig = " + activityEntity.getActivityConfig());
        List<Map> runningGoalsAwardList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
        Map<Integer, Map> runningGoalsAwardMap = runningGoalsAwardList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));

        List<Map> runningGoalsAwardScoreList = null;
        Map<Integer, Map> runningGoalsAwardScoreMap = null;
        if (data.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD) != null) {
            runningGoalsAwardScoreList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD), Map.class);
            runningGoalsAwardScoreMap = runningGoalsAwardScoreList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
        }
        List<Map> runningGoalsAwardCouponList = null;
        Map<Integer, Map> runningGoalsAwardCouponMap = null;
        if (data.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD) != null) {
            runningGoalsAwardCouponList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD), Map.class);
            runningGoalsAwardCouponMap = runningGoalsAwardCouponList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
        }

        BigDecimal avePartitionAmount = completeUsers.size() > 0 ? userFees.divide(new BigDecimal(completeUsers.size()), 2, BigDecimal.ROUND_DOWN) : BigDecimal.ZERO;
        log.info("avePartitionAmount = " + avePartitionAmount);
        //报名分组数量
        Map<Integer, Long> countMap = list.stream().collect(Collectors.groupingBy(completeRuleType == 1 ? ZnsRunActivityUserEntity::getTargetRunMileage : ZnsRunActivityUserEntity::getTargetRunTime, Collectors.counting()));

        //按目标分组排名
        Map<Integer, List<ZnsRunActivityUserEntity>> targetMap = completeUsers.stream().collect(Collectors.groupingBy(completeRuleType == 1 ? ZnsRunActivityUserEntity::getTargetRunMileage : ZnsRunActivityUserEntity::getTargetRunTime));
        for (Map.Entry<Integer, List<ZnsRunActivityUserEntity>> map : targetMap.entrySet()) {
            List<ZnsRunActivityUserEntity> activityUserEntityList = map.getValue();
            if (completeRuleType == 1) {
                activityUserEntityList = activityUserEntityList.stream().map(e -> {
                    Integer propRunTime = e.getPropRunTime();
                    if (Objects.nonNull(propRunTime)) {
                        //用于处理使用道具后的排名逻辑
                        e.setRunTimeMillisecond(propRunTime);
                    }
                    return e;
                }).sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond)
                        .thenComparing(ZnsRunActivityUserEntity::getCompleteTime)).collect(Collectors.toList());
            } else {
                activityUserEntityList = activityUserEntityList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()
                        .thenComparing(ZnsRunActivityUserEntity::getCompleteTime)).collect(Collectors.toList());
            }
            Long count = countMap.get(map.getKey());

            //排名并列处理
            Integer previousRank = 1;
            Integer previousGoal = 0;
            ZonedDateTime previousCompleteTime = activityUserEntityList.get(0).getCompleteTime();
            for (int i = 0; i < activityUserEntityList.size(); i++) {
                ZnsRunActivityUserEntity activityUser = activityUserEntityList.get(i);
                activityUser.setRank(i + 1);
                BigDecimal award = BigDecimal.ZERO;
                BigDecimal rankAward = BigDecimal.ZERO;
                Map awardMap = null;

                Map awardScoreMap = null;

                Map<String, ActivityCouponDto> awardCouponMap = null;
                ActivityCouponDto activityCouponDto = null;

                if (completeRuleType == 1) {
                    log.info("completeRuleType = 1 ");
                    awardMap = runningGoalsAwardMap.get(activityUser.getTargetRunMileage());
                    if (runningGoalsAwardScoreMap != null) {
                        awardScoreMap = runningGoalsAwardScoreMap.get(activityUser.getTargetRunMileage());
                    }
                    if (runningGoalsAwardCouponMap != null) {
                        awardCouponMap = runningGoalsAwardCouponMap.get(activityUser.getTargetRunMileage());
                    }
                    if (activityUser.getRunTimeMillisecond().equals(previousGoal) && activityUser.getCompleteTime().compareTo(previousCompleteTime) == 0) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunTimeMillisecond();
                    previousRank = activityUser.getRank();
                    previousCompleteTime = activityUser.getCompleteTime();
                } else {
                    log.info("completeRuleType = 2");
                    awardMap = runningGoalsAwardMap.get(activityUser.getTargetRunTime());

                    if (runningGoalsAwardScoreMap != null) {
                        awardScoreMap = runningGoalsAwardScoreMap.get(activityUser.getTargetRunTime());
                    }

                    if (runningGoalsAwardCouponMap != null) {
                        awardCouponMap = runningGoalsAwardCouponMap.get(activityUser.getTargetRunTime());
                    }

                    if (activityUser.getRunMileage().compareTo(new BigDecimal(previousGoal)) == 0 && activityUser.getCompleteTime().compareTo(previousCompleteTime) == 0) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunMileage().intValue();
                    previousRank = activityUser.getRank();
                    previousCompleteTime = activityUser.getCompleteTime();
                }
                //权益处理
                List<ActivityBrandRightsInterests> brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterestsList(activityEntity, activityUser.getUserId());

                //瓜分奖励
                award = award.add(avePartitionAmount);
                BigDecimal extraCompleteAward = BigDecimal.ZERO;
                BigDecimal extraRankAward = BigDecimal.ZERO;
                if (activityUser.getIsCheat() == 0) {

                    //排名奖励
                    log.info("分组人数count：" + count + "最少人数限制：" + getLimitPeopleSize());
                    if (count >= getLimitPeopleSize() && Objects.nonNull(awardMap)) {
                        BigDecimal officialTeamRankAward = getOfficialTeamRankAward(awardMap, activityUser.getRank());
                        log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ", 排名奖励 officialTeamRankAward = " + officialTeamRankAward);
                        award = award.add(officialTeamRankAward);
                        log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",排名奖励 award = " + award);
                        rankAward = officialTeamRankAward;
                        ActivityBrandRightsInterests rankRightsInterest = activityBrandRightsInterestsService.getBrandRightsInterest(brandRightsInterests, BrandRightsInterestEnum.RANK_REWARD.getStatusCode());
                        if (Objects.nonNull(rankRightsInterest)) {
                            BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(officialTeamRankAward, rankRightsInterest.getMultiple());
                            log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",排名奖励 brandAward = " + brandAward);
                            award = award.add(brandAward);
                            rankAward = rankAward.add(brandAward);
                            log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",排名奖励 award = " + award);
                            extraRankAward = brandAward;
                            log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",排名奖励 extraRankAward = " + award);
                        }
                    }

                    //排名积分
                    BigDecimal officialTeamRankAwardScore = getOfficialTeamRankAward(awardScoreMap, activityUser.getRank());
                    log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",officialTeamRankAwardScore = " + officialTeamRankAwardScore);
                    Integer awardScoreInt = officialTeamRankAwardScore.intValue();
                    log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",awardScoreInt = " + awardScoreInt);
                    ActivityBrandRightsInterests scoreRightsInterest = activityBrandRightsInterestsService.getBrandRightsInterest(brandRightsInterests, BrandRightsInterestEnum.INTEGRAL.getStatusCode());
                    Integer extraScore = 0;
                    if (Objects.nonNull(scoreRightsInterest)) {
                        BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(new BigDecimal(awardScoreInt), scoreRightsInterest.getMultiple());
                        extraScore = brandAward.intValue();
                        awardScoreInt = awardScoreInt + brandAward.intValue();
                        log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",brandAward = " + brandAward + ",extraScore=" + extraScore + ",awardScoreInt=" + awardScoreInt);
                    }

                    log.info("官方排行赛事分数 activityId = " + activityUser.getActivityId() + ",userId = " + activityUser.getUserId() + "awardScoreInt = " + awardScoreInt + ",排名 = " + activityUser.getRank());
                    if (awardScoreInt > 0) {
                        ActivityUserScore activityUserScore = activityUserScoreDao.selectActivityUserScoreByActivityIdUserIdRank(activityUser.getActivityId(),
                                activityUser.getUserId(), activityUser.getRank());
                        if (activityUserScore == null) {
                            activityUserScoreService.increaseAmount(awardScoreInt, activityUser.getActivityId(), activityUser.getUserId(), activityUser.getRank(), extraScore, null);
                        }
                    }

                    //排名券奖励
                    ActivityCouponDto officialTeamRankAwardCoupon = getOfficialTeamRankCouponAward(awardCouponMap, activityUser.getRank());
                    log.info("赛事  awardCouponMap 排名奖励 activityId = " + activityUser.getActivityId() + ",userId = " + activityUser.getUserId() + ",officialTeamRankAwardCoupon = {}", officialTeamRankAwardCoupon);
                    if (officialTeamRankAwardCoupon != null) {
                        for (int num = 0; num < officialTeamRankAwardCoupon.getNum(); num++) {
                            userCouponManager.sendUserCoupon(officialTeamRankAwardCoupon.getCouponId(), activityUser.getUserId(), activityEntity.getId());
                        }
                    }

                }


                log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",award = " + award);
                BigDecimal runAward = activityUser.getRunAward().add(award);
                //修改币种切换处理
                List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityUser.getActivityId().toString())) {
                    log.info("币种切换不发放");
                    runAward = BigDecimal.ZERO;
                } else {
                    String currencyCode = userAccountService.getUserCurrency(activityUser.getUserId()).getCurrencyCode();
                    runAward = I18nConstant.currencyFormat(currencyCode, runAward);
                    activityUser.setRunAward(runAward);
                    activityUser.setRewardTime(ZonedDateTime.now());
                    log.info("金额明细" + "extraRankAward :" + extraRankAward + "rankAward:" + rankAward + "avePartitionAmount:" + avePartitionAmount);
                    Long detailIdSplit = handleRunAward(avePartitionAmount, activityUser, activityEntity, extraCompleteAward.add(extraRankAward), AccountDetailSubtypeEnum.OFFICIAL_TEAM_RUM_SPLIT);
                    log.info("------跑步奖励处理完成------");
                    Long detailIdRank = handleRunAward(rankAward, activityUser, activityEntity, extraCompleteAward.add(extraRankAward), AccountDetailSubtypeEnum.OFFICIAL_TEAM_RUM_RANK);
                    log.info("------排名奖励处理完成------");
                    //奖励明细分类处理
                    List<UserAccountDetailSub> accountDetailSubs = new ArrayList<>();
                    userAccountDetailSubService.addDetailSubToList(accountDetailSubs, activityUser.getUserId(), detailIdSplit, activityUser.getActivityId(), 1, avePartitionAmount, BigDecimal.ZERO);
                    log.info("------accountdetailsub添加完成------");
                    if (activityUser.getIsCheat() == 0) {
                        ZnsUserEntity user = znsUserService.findById(activityUser.getUserId());
                        userAccountDetailSubService.addDetailSubToList(accountDetailSubs, user, detailIdRank, activityUser.getActivityId(), 4, rankAward, activityUser.getRank(), extraRankAward);
                    }
                    if (!CollectionUtils.isEmpty(accountDetailSubs)) {
                        userAccountDetailSubService.saveBatch(accountDetailSubs);
                    }
                }

                log.info("------证书处理完成------");
                handlerUserCertificate(activityEntity, activityUser);
                if (activityUser.getRank() <= 10 && 1 <= activityUser.getRank()) {
                    //排名push
                    handleSendRankPush(activityUser, activityEntity);
                }
            }
        }
        if (!CollectionUtils.isEmpty(completeUsers)) {
            completeUsers = completeUsers.stream().map(e -> {
                // 排名逻辑后修正回道具使用前的跑步时长
                ZnsRunActivityUserEntity entity = tempList.stream().filter(x -> x.getUserId().equals(e.getUserId())).findFirst().get();
                e.setRunTimeMillisecond(entity.getRunTimeMillisecond());
                return e;
            }).collect(Collectors.toList());
            runActivityUserService.updateBatchById(completeUsers);
            // 结束后卷的使用
            completeUsers.forEach(i -> {
                UserCoupon coupon = userCouponService.getUserCouponByActivityAndUserIdAndStatus(i.getActivityId(), i.getUserId(), 1);
                if (Objects.nonNull(coupon)) {
                    userCouponManager.endUseCoupon(coupon.getId(), i.getActivityId(), null);
                }
            });
        }
        // 活动结束退还卷逻辑
        noCompleteUsers.forEach(k -> {
            UserCoupon coupon = userCouponService.findByQuery(new UserCouponQuery().setActivityId(activityEntity.getId()).setUserId(k.getUserId()).setStatus(List.of(1)));
            if (Objects.nonNull(coupon)) {
                log.info("return coupon no user_id:{},coupon_id:{}", k.getId(), coupon.getId());
                if (k.getUserState() == 1) {
                    coupon.setStatus(0);
                } else {
                    coupon.setStatus(2);
                }
                userCouponService.update(coupon);
            }
        });

        //下架处理
        if (activityEntity.getStatus() == -1 && activityEntity.getBonusRuleType() != 1) {
            for (ZnsRunActivityUserEntity activityUser : list) {
                useUserScore(activityEntity, activityUser.getUserId(), true);

                if (activityEntity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                    // 保证金、费用退回
                    AccountDetailTypeEnum detailType = Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? AccountDetailTypeEnum.FEE : AccountDetailTypeEnum.SECURITY_FUND;
                    String refundRemark = Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? "fee return" : "Deposit return";
                    runActivityProcessManager.cancelActivityRefund(activityEntity, detailType, activityUser.getUserId(), refundRemark);
                }
            }
        }
        super.handleRunActivityData(activityEntity);
    }

    private BigDecimal getOfficialTeamRankAward(Map awardMap, Integer rank) {
        if (Objects.isNull(rank)) {
            return BigDecimal.ZERO;
        }
        try {
            if (awardMap == null || rank == -1) {
                return BigDecimal.ZERO;
            }
            log.info(" getOfficialTeamRankAward awardMap json数据={},rank= {}", JsonUtil.writeString(awardMap), rank);
            if (rank == 1) {
                Double firstAward = MapUtils.getDouble(awardMap, "firstAward", 0d);
                return new BigDecimal(firstAward);
            } else if (rank == 2) {
                Double secondAward = MapUtils.getDouble(awardMap, "secondAward", 0d);
                return new BigDecimal(secondAward);
            } else if (rank == 3) {
                Double thirdAward = MapUtils.getDouble(awardMap, "thirdAward", 0d);
                return new BigDecimal(thirdAward);
            } else {
                String key = rank + "";
                Double rankAward = MapUtils.getDouble(awardMap, key, 0d);
                if (Objects.isNull(rankAward)) {
                    return BigDecimal.ZERO;
                }
                return new BigDecimal(rankAward);
            }
        } catch (Exception e) {
            log.info("getOfficialTeamRankAward error,e:{}", e);
        }
        return BigDecimal.ZERO;
    }


    private ActivityCouponDto getOfficialTeamRankCouponAward(Map awardMap, Integer rank) {
        if (Objects.isNull(rank)) {
            return null;
        }
        try {
            if (awardMap == null || rank == -1) {
                return null;
            }
            log.info(" getOfficialTeamRankCouponAward awardMap json数据=" + JsonUtil.writeString(awardMap) + ", rank= " + rank);
            if (rank == 1) {
                String firstAward = MapUtils.getString(awardMap, "firstAward");
                return JsonUtil.readValue(firstAward, ActivityCouponDto.class);
            } else if (rank == 2) {
                String secondAward = MapUtils.getString(awardMap, "secondAward");
                return JsonUtil.readValue(secondAward, ActivityCouponDto.class);
            } else if (rank == 3) {
                String thirdAward = MapUtils.getString(awardMap, "thirdAward");
                return JsonUtil.readValue(thirdAward, ActivityCouponDto.class);
            } else {
                String key = rank + "";
                String rankAward = MapUtils.getString(awardMap, key);
                if (Objects.isNull(rankAward)) {
                    return null;
                }
                return JsonUtil.readValue(rankAward, ActivityCouponDto.class);
            }
        } catch (Exception e) {
            log.error("getOfficialTeamRankCouponAward error,e:{}", e);
        }
        return null;
    }


    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        if (awardAmount.compareTo(BigDecimal.ZERO) > 0) {
            awardAmount = getUserCurrencyAmount(activityUser.getUserId(), awardAmount);
            extraAward = getUserCurrencyAmount(activityUser.getUserId(), extraAward);
            // 给用户余额发送奖励
            userAccountService.increaseAmount(awardAmount, activityUser.getUserId(), true);
            // 新增用户奖励余额明细
            String billNo = NanoId.randomNanoId();
            ;
            ZonedDateTime tradeTime = ZonedDateTime.now();
            String remark = "";
            if (Objects.nonNull(subType)) {
                remark = subType.getName();
            }
            Long detail = userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.OFFICIAL_TEAM_AWARD, Objects.nonNull(subType) ? subType.getType() :
                            AccountDetailSubtypeEnum.TEAM_RUM.getType(), 1, awardAmount, billNo, tradeTime, activityUser.getActivityId(),
                    activityUser.getActivityId(), null, activityUser.getActivityType(), 0L, remark, null, null, null, extraAward);
            ActivityNotificationEnum activityNotification = ActivityNotificationEnum.AWARD_OFFICIAL_TEAM_RUN;

            List<Long> userIds = Arrays.asList(activityUser.getUserId());
            String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.AWARD_OFFICIAL_TEAM_RUN"), activityEntity.getActivityTitle());

            ImMessageBo bo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
            MessageBo messageBo = appMessageService.assembleMessage(activityEntity.getId(), content, "4", NoticeTypeEnum.REWARD_NOTICE.getType());
            messageBo.setActivityId(activityEntity.getId());
            appMessageService.sendImAndPushUserIds(userIds, bo, messageBo);
            return detail;
        }
        return null;
    }

    @Override
    public void wrapperRunActivityHistoryUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO) {
        // 计算当前时间往前推一个月的时间作为开始时间,此参数单独用于优化sql，并无实际意义
        String startTime = ZonedDateTime.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        List<Map<String, Object>> runActivityHistoryUsers = activityUserManager.getRunActivityHistoryUsers(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType(), 1, startTime);
        if (!runActivityHistoryUsers.isEmpty() && runActivityHistoryUsers.size() >= 6) {
            activityDetailVO.setRunActivityHistoryUsers(runActivityHistoryUsers);
        }
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        //活动开始30分钟后不可操作
        // 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
        if (null == lastEnterMinutes) {
            lastEnterMinutes = 30;
        }
        ZonedDateTime date = activityEntity.getActivityStartTime().plusMinutes(lastEnterMinutes);
        if (ZonedDateTime.now().compareTo(date) > 0) {
            log.info("checkReportUserRun fail,error msg:{},activityStartTime:{},currentDate:{}", "You are not allowed to enter after 30 min of running", activityEntity.getActivityStartTime(), ZonedDateTime.now());
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.run.check.user.running"));
        }
        return super.checkReportUserRun(activityEntity, user);
    }

    public Integer getLimitPeopleSize() {
        String limitCount = sysConfigService.selectConfigByKey("official.team.activity.people.num.limit");
        Integer limitPeopleSize = 5;
        if (StringUtils.hasText(limitCount)) {
            limitPeopleSize = Integer.parseInt(limitCount);
        }
        return limitPeopleSize;
    }

    @Override
    public TeamRunReportVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        TeamRunReportVo teamRunReportVo = new TeamRunReportVo();
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), loginUser.getId());
        Integer activityState = null;
        ZonedDateTime date = DateTimeUtil.parse("2022-11-28 04:00:00");
        if (Objects.nonNull(activityEntity.getActivityEndTime()) && activityEntity.getActivityEndTime().compareTo(date) > 0) {
            activityState = activityEntity.getActivityState();
        }
        List<TeamRunRunningReportListVO> teamRunRunningReport = userRunDataDetailsDao.getTeamRunRunningReport(detail.getId(), activityUser.getTargetRunMileage(), activityUser.getTargetRunTime(), activityState);
        if (CollectionUtils.isEmpty(teamRunRunningReport)) {
            return teamRunReportVo;
        }

        //处理团队赛明细数据
        dealTeamRunDetail(teamRunRunningReport, activityEntity.getActivityType(), activityEntity.getId());

        //活动进行中排序修改
        if (activityEntity.getActivityState() == 1) {
            List<TeamRunRunningReportListVO> newList = new ArrayList<>();
            Map<Integer, List<TeamRunRunningReportListVO>> listMap = teamRunRunningReport.stream().collect(Collectors.groupingBy(TeamRunRunningReportListVO::getIsComplete));
            List<TeamRunRunningReportListVO> teamRunRunningReportListVOS = listMap.get(1);
            if (!CollectionUtils.isEmpty(teamRunRunningReportListVOS)) {
                if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_2.getCode().equals(activityEntity.getCompleteRuleType())) {
                    //时长跑 按里程越大越在前
                    List<TeamRunRunningReportListVO> completeUsers = teamRunRunningReportListVOS.stream().sorted(Comparator.comparing(TeamRunRunningReportListVO::getRunMileage, Comparator.reverseOrder()).thenComparing(TeamRunRunningReportListVO::getCompleteTime)).collect(Collectors.toList());
                    newList.addAll(completeUsers);
                } else {
                    //里程跑 按时间越小越在前
                    List<TeamRunRunningReportListVO> completeUsers = teamRunRunningReportListVOS.stream().sorted(Comparator.comparing(TeamRunRunningReportListVO::getRunTimeMillisecond).thenComparing(TeamRunRunningReportListVO::getCompleteTime)).collect(Collectors.toList());
                    newList.addAll(completeUsers);
                }
            }
            List<TeamRunRunningReportListVO> noTeamRunRunningReportListVOS = listMap.get(0);
            if (!CollectionUtils.isEmpty(noTeamRunRunningReportListVOS)) {
                List<TeamRunRunningReportListVO> noCompleteUsers = noTeamRunRunningReportListVOS.stream().sorted((x, y) -> {
                    if (Objects.isNull(y.getRunTimeMillisecond()) || y.getRunTimeMillisecond() <= 0) {
                        return 0;
                    }
                    if (Objects.isNull(x.getRunTimeMillisecond()) || x.getRunTimeMillisecond() <= 0) {
                        return 0;
                    }
                    if (Objects.isNull(y.getCompleteTime()) || Objects.isNull(x.getCompleteTime())) {
                        return 0;
                    }
                    BigDecimal v1 = x.getRunMileage().divide(new BigDecimal(x.getRunTimeMillisecond()), 6, BigDecimal.ROUND_HALF_UP);
                    BigDecimal v2 = y.getRunMileage().divide(new BigDecimal(y.getRunTimeMillisecond()), 6, BigDecimal.ROUND_HALF_UP);
                    if (v2.compareTo(v1) > 0) {
                        return 1;
                    } else if (v2.compareTo(v1) == 0 && y.getCompleteTime().compareTo(x.getCompleteTime()) < 0) {
                        return 1;
                    } else {
                        return 0;
                    }
                }).collect(Collectors.toList());
                newList.addAll(noCompleteUsers);
            }
            teamRunRunningReport = newList;
        }

        //查询自己
        TeamRunRunningReportListVO teamRunRunningReportListVO = teamRunRunningReport.stream().filter(r -> loginUser.getId().equals(r.getUserId())).findFirst().orElse(null);
        if (Objects.nonNull(teamRunRunningReportListVO)) {
            teamRunReportVo.setMyGrades(new MyGradesDto(teamRunRunningReportListVO.getRank(), teamRunRunningReportListVO.getRunAward()));
        } else {
            teamRunReportVo.setMyGrades(new MyGradesDto(-1, BigDecimal.ZERO));
        }

        //查询好友关系
        List<ZnsUserFriendEntity> friendList = userFriendService.getFriendList(loginUser.getId(), teamRunRunningReport.stream().map(TeamRunRunningReportListVO::getUserId).collect(Collectors.toList()));
        Map<Long, ZnsUserFriendEntity> friendEntityMap = friendList.stream().collect(Collectors.toMap(ZnsUserFriendEntity::getFriendId, Function.identity(), (x, y) -> x));
        for (int i = 0; i < teamRunRunningReport.size(); i++) {
            TeamRunRunningReportListVO vo = teamRunRunningReport.get(i);
            if (vo.getRank() <= 0) {
                vo.setRank(i + 1);
            }
            if (Objects.nonNull(friendEntityMap) && Objects.nonNull(friendEntityMap.get(vo.getUserId()))) {
                vo.setIsFriend(1);
            } else {
                vo.setIsFriend(0);
            }
        }
        //是否可以一键关注
        List<ZnsUserFriendEntity> myfriendList = userFriendService.getMyFriendList(loginUser.getId(), teamRunRunningReport.stream().map(TeamRunRunningReportListVO::getUserId).collect(Collectors.toList()));
        Map<Long, ZnsUserFriendEntity> myfriendEntityMap = myfriendList.stream().collect(Collectors.toMap(ZnsUserFriendEntity::getFriendId, Function.identity(), (x, y) -> x));
        Integer canNoticeAll = 0;
        for (int i = 0; i < teamRunRunningReport.size(); i++) {
            TeamRunRunningReportListVO vo = teamRunRunningReport.get(i);
            //排除自己
            if (loginUser.getId().equals(vo.getUserId())) {
                continue;
            }
            if (Objects.nonNull(myfriendEntityMap) && Objects.nonNull(myfriendEntityMap.get(vo.getUserId()))) {
            } else {
                canNoticeAll = 1;
            }
        }
        teamRunReportVo.setCanNoticeAll(canNoticeAll);
        //团队跑步数据
        teamRunReportVo.setList(teamRunRunningReport);

        //最佳配速
        TeamRunRunningReportListVO pace = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getAveragePace()) && r.getAveragePace() > 0)
                .min(Comparator.comparing(TeamRunRunningReportListVO::getAveragePace)).orElse(new TeamRunRunningReportListVO());
        UserSimpleVo bestPaceVo = new UserSimpleVo();
        bestPaceVo.setUserId(pace.getUserId());
        bestPaceVo.setNickname(pace.getNickname());
        bestPaceVo.setHeadPortrait(pace.getHeadPortrait());
        bestPaceVo.setBestPace(pace.getAveragePace());
        teamRunReportVo.setBestPaceUser(bestPaceVo);

        //心率控制最佳
        TeamRunRunningReportListVO heartRate = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getAverageHeartRate()) && r.getAverageHeartRate() > 0)
                .max((x, y) -> {
                    BigDecimal r1 = BigDecimalUtil.divide(new BigDecimal(x.getAverageHeartRate()), new BigDecimal(SportsDataUnit.getUserMaxHeart(x.getBirthday())));
                    BigDecimal r2 = BigDecimalUtil.divide(new BigDecimal(y.getAverageHeartRate()), new BigDecimal(SportsDataUnit.getUserMaxHeart(y.getBirthday())));
                    return r1.compareTo(r2);
                }).orElse(new TeamRunRunningReportListVO());
        UserSimpleVo bestHeartRateVo = new UserSimpleVo();
        bestHeartRateVo.setUserId(heartRate.getUserId());
        bestHeartRateVo.setNickname(heartRate.getNickname());
        bestHeartRateVo.setHeadPortrait(heartRate.getHeadPortrait());
        bestHeartRateVo.setBestHeartRate(heartRate.getAverageHeartRate());
        teamRunReportVo.setBestHeartRateUser(bestHeartRateVo);

        //卡路里消耗最佳
        TeamRunRunningReportListVO calorie = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getKilocalorie()) && r.getKilocalorie().compareTo(BigDecimal.ZERO) > 0)
                .max(Comparator.comparing(TeamRunRunningReportListVO::getKilocalorie, BigDecimal::compareTo)).orElse(new TeamRunRunningReportListVO());
        teamRunReportVo.setBestCalorieUser(new UserSimpleVo(calorie.getUserId(), calorie.getNickname(), calorie.getHeadPortrait(), calorie.getKilocalorie()));

        //跑力值最佳
        TeamRunRunningReportListVO capabilityValue = teamRunRunningReport.stream()
                .filter(r -> Objects.nonNull(r.getCapabilityValue()) && r.getCapabilityValue().compareTo(BigDecimal.ZERO) > 0)
                .max(Comparator.comparing(TeamRunRunningReportListVO::getCapabilityValue, BigDecimal::compareTo)).orElse(new TeamRunRunningReportListVO());
        UserSimpleVo bestCapabilityValueVo = new UserSimpleVo();
        bestCapabilityValueVo.setUserId(capabilityValue.getUserId());
        bestCapabilityValueVo.setNickname(capabilityValue.getNickname());
        bestCapabilityValueVo.setHeadPortrait(capabilityValue.getHeadPortrait());
        bestCapabilityValueVo.setBestCapabilityValue(capabilityValue.getCapabilityValue());
        teamRunReportVo.setBestCapabilityValueUser(bestCapabilityValueVo);

        //人气最佳
        Map<Long, Long> inviteMap = teamRunRunningReport.stream().filter(r -> Objects.nonNull(r.getInviterUserId()) && r.getInviterUserId() > 0).collect(Collectors.groupingBy(TeamRunRunningReportListVO::getInviterUserId, Collectors.counting()));
        Optional<Map.Entry<Long, Long>> popularityUserMap = inviteMap.entrySet().stream().filter(e -> e.getValue() > 0).max(Map.Entry.comparingByValue());
        if (popularityUserMap.isPresent()) {
            Long userId = popularityUserMap.get().getKey();
            TeamRunRunningReportListVO popularity = teamRunRunningReport.stream().filter(r -> r.getUserId().equals(userId)).findFirst().orElse(null);
            if (Objects.nonNull(popularity)) {
                teamRunReportVo.setBestPopularityUser(new UserSimpleVo(popularity.getUserId(), popularity.getNickname(), popularity.getHeadPortrait(), popularityUserMap.get().getValue()));
            }
        }

        //活动进行中预估完成时间（分钟）
        if (activityEntity.getActivityState() == 1) {
            long seconds = DateUtil.between(ZonedDateTime.now(), activityEntity.getActivityEndTime());
            teamRunReportVo.setEstimatedFinishTime(TimeUnit.SECONDS.toMinutes(seconds));
        }
        return teamRunReportVo;
    }

    @Override
    public void wrapperRunActivityVisitor(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        List<RunActivityUserVO> runActivityUsers = activityDetailVO.getActivityUsers();
        //判断是否是游客
        if (Objects.isNull(oneself)) {
            //游客返回游客房间号
            activityDetailVO.setIsVisitor(1);
            Map<Integer, Long> countMap = runActivityUsers.stream().collect(Collectors.groupingBy(activityEntity.getCompleteRuleType() == 1 ? RunActivityUserVO::getTargetRunMileage : RunActivityUserVO::getTargetRunTime, Collectors.counting()));
            Map.Entry<Integer, Long> entry = countMap.entrySet().stream().sorted((e1, e2) -> {
                return e2.getValue().compareTo(e1.getValue());
            }).findFirst().orElse(null);
            if (Objects.nonNull(entry)) {
                Integer goal = entry.getKey();
                //房间号处理
                Long roomNumber = NumberUtils.getGoalImNumber(activityEntity.getId(), goal, activityEntity.getCompleteRuleType());
                activityDetailVO.setWatchRoomId(roomNumber.intValue());
                if (activityEntity.getCompleteRuleType() == 1) {
                    activityDetailVO.setWatchRoomMileageGoal(goal);
                } else {
                    activityDetailVO.setWatchRoomTimeGoal(goal);
                }
            } else {
                //设置默认房间号
                List<Integer> runningGoals = activityDetailVO.getRunningGoals();
                Integer goal = runningGoals.get(0);
                Long roomNumber = NumberUtils.getGoalImNumber(activityEntity.getId(), goal, activityEntity.getCompleteRuleType());
                activityDetailVO.setWatchRoomId(roomNumber.intValue());
                if (activityEntity.getCompleteRuleType() == 1) {
                    activityDetailVO.setWatchRoomMileageGoal(goal);
                } else {
                    activityDetailVO.setWatchRoomTimeGoal(goal);
                }
            }
        }
    }

    @Override
    public void homePageActivityMap(HomepageActivityVo map, ZnsRunActivityEntity activity, Long userId, String zoneId) {
        super.homePageActivityMap(map, activity, userId, zoneId);
        if (Objects.nonNull(jsonObjectConfig.get("runningGoals"))) {
            List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
            map.setRunningGoals(runningGoals);
            //房间号处理
            Long roomNumber = NumberUtils.getGoalImNumber(activity.getId(), runningGoals.get(0), activity.getCompleteRuleType());
            map.setRoomNumber(roomNumber.intValue());
        }

        map.setLastEnterTeamRunTime(getLastEnterRunTime(jsonObjectConfig));
        map.setRunBeforeEnter(getRunBeforeTime(jsonObjectConfig));

        //是否是但当日赛事判断
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId));
        ZonedDateTime activityStartTime = DateUtil.getDate2ByTimeZone(activity.getActivityStartTime(), TimeZone.getTimeZone(zoneId));
        ZonedDateTime activityEndTime = DateUtil.getDate2ByTimeZone(activity.getActivityEndTime(), TimeZone.getTimeZone(zoneId));
        if (DateUtil.isSameDay(now, activityStartTime) || DateUtil.isSameDay(now, activityEndTime)) {
            map.setIsSameDay(1);
            String advertisingConfigImage = MapUtil.getString(jsonObjectConfig.get(ApiConstants.ADVERTISING_CONFIG_IMAGE));
            if (StringUtil.isEmpty(advertisingConfigImage)) {
                advertisingConfigImage = MapUtil.getString(jsonObjectConfig.get(ApiConstants.ADVERTISING_IMAGE));
            }
            map.setAdvertisingImage(advertisingConfigImage);
        } else {
            map.setIsSameDay(0);
        }

        //查询活动用户
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activity.getId(), userId);
        if (Objects.isNull(activityUser)) {
            map.setIsEnroll(0);
            return;
        }

        Long roomNumber = NumberUtils.getGoalImNumber(activity.getId(), activityUser.getTargetRunMileage(), activity.getCompleteRuleType());
        map.setRoomNumber(roomNumber.intValue());
        map.setIsEnroll(1);
        map.setRunMileage(activityUser.getTargetRunMileage());
        map.setRunTime(activityUser.getTargetRunTime());
        map.setUserState(activityUser.getUserState());
    }

    @Override
    public List<? extends SimpleRunActivityVO> getActivityList(ZnsUserEntity user, boolean isTest, boolean checkVersion, boolean isHomepage, Integer source, Integer completeRuleType, List<Integer> runWalkStatus, Integer rateLimitType) {
        Long userId = user.getId();
        List<ZnsRunActivityEntity> list = runActivityService.getActivityByTypeAndState(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType(), isTest, checkVersion, false, userId, isHomepage, source, completeRuleType, runWalkStatus, user.getCountry(), rateLimitType);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, null);
        List<Long> userIds = activityUsers.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        Map<Long, ZnsUserEntity> userEntityMap = null;
        if (!CollectionUtils.isEmpty(userIds)) {
            List<ZnsUserEntity> userList = userService.findByIds(userIds);
            userEntityMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity(), (x, y) -> x));
        }

        Map<Long, List<ZnsRunActivityUserEntity>> listMap = activityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getActivityId));

        List<OfficialTeamActivityListVO> activityList = new ArrayList<>();
        for (ZnsRunActivityEntity activity : list) {
            OfficialTeamActivityListVO vo = new OfficialTeamActivityListVO(activity);
            Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
            List<Integer> runningGoals = JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class);
            String runningGoalUnit = MapUtil.getString(jsonObject.get("runningGoalsUnit"));
            vo.setRunningGoals(runningGoals);
            if (StringUtils.hasText(runningGoalUnit)) {
                vo.setRunningGoalUnit(Integer.valueOf(runningGoalUnit));
            }
            vo.setCoverImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
            List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = listMap.get(activity.getId());
            if (CollectionUtils.isEmpty(znsRunActivityUserEntities)) {
                activityList.add(vo);
                continue;
            }

            Long count = znsRunActivityUserEntities.stream().count();
            vo.setAcceptedNum(count.intValue());
            //获取参与用户
            List<UserSimpleVo> participantList = new ArrayList<>();
            for (ZnsRunActivityUserEntity znsRunActivityUserEntity : znsRunActivityUserEntities) {
                ZnsUserEntity znsUserEntity = userEntityMap.get(znsRunActivityUserEntity.getUserId());
                if (Objects.nonNull(znsUserEntity)) {
                    UserSimpleVo userSimpleVo = new UserSimpleVo(znsUserEntity.getId(), znsUserEntity.getFirstName(), znsUserEntity.getHeadPortrait());
                    participantList.add(userSimpleVo);
                }
            }
            if (participantList.size() > 3) {
                vo.setParticipantList(participantList.subList(0, 3));
            } else {
                vo.setParticipantList(participantList);
            }
            //个人参数设置
            ZnsRunActivityUserEntity oneSelf = znsRunActivityUserEntities.stream().filter(u -> u.getUserId().equals(userId)).findFirst().orElse(null);
            if (Objects.nonNull(oneSelf)) {
                vo.setIsEnroll(1);
                vo.setTargetRunMileage(oneSelf.getTargetRunMileage());
                vo.setTargetRunTime(oneSelf.getTargetRunTime());
                vo.setUserState(oneSelf.getUserState());
            }
            if (Objects.isNull(oneSelf) && activity.getStatus() == -1) {
                continue;
            }
            Integer integer = userCouponService.countUserCouponByCondition(userId, activity.getId(), activity.getActivityType(), activity.getTaskConfigId(), activity.getBatchNo());
            vo.setCanUserCoupon(integer > 0 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            activityList.add(vo);
        }
        return activityList;
    }

    @Override
    public Result checkHandleUserActivityState(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser, Integer userStatus, ZnsUserEntity user) {
        Result result = super.checkHandleUserActivityState(activityEntity, activityUser, userStatus, user);
        if (Objects.nonNull(result)) {
            return result;
        }
        return checkContestCountLimit(user, activityEntity);
    }

    private Result checkContestCountLimit(ZnsUserEntity user, ZnsRunActivityEntity activityEntity) {
        if (user.getIsRobot() == 1) {
            return null;
        }
        //查询参赛限制
        String contestCountLimit = sysConfigService.selectConfigByKey("activity.contest.count.limit");
        if (StringUtil.isEmpty(contestCountLimit) || "-1".equals(contestCountLimit)) {
            return null;
        }
        //跑步类型不限的活动，不考虑报名活动次数
//        if(Objects.equals(activityEntity.getRunWalkStatus(), RunActivityRaceTypeEnum.UNLIMITED.getCode())){
//            return null;
//        }
        //查询用户今日参赛次数
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(ZoneId.of("GMT-8")));
        ZonedDateTime startOfDate = DateUtil.getStartOfDate(now, TimeZone.getTimeZone(ZoneId.of("GMT-8")));
        ZonedDateTime endOfDate = DateUtil.getEndOfDate(now, TimeZone.getTimeZone(ZoneId.of("GMT-8")));
        Integer activityUserCount = runActivityUserService.findActivityUserCountByUserId(user.getId(), startOfDate, endOfDate, Arrays.asList(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType()), null, activityEntity.getRunWalkStatus());
        if (Objects.nonNull(activityUserCount) && activityUserCount >= Integer.valueOf(contestCountLimit)) {
            log.info("参赛次数达到限制");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.race.join.limit"));
        }
        return null;
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        Result result = checkContestCountLimit(user, activityEntity);
        if (Objects.nonNull(result)) {
            return result;
        }
        return super.canRefuseOrAcceptActivity(activityEntity, user);
    }

    @Override
    public void wrapperCouponDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
        UserCoupon userCoupon = userCouponService.getUserCouponByActivityAndUserIdAndCouponType(activityEntity.getId(), userId, 5, null);
        if (Objects.isNull(userCoupon)) {
            List<UserCouponDiKou> userCoupons = userCouponService.selectCanUseConponList(activityEntity, userId);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(userCoupons)) {
                activityDetailVO.setCouponAmount(BigDecimal.ZERO);
            } else {
                activityDetailVO.setCouponAmount(BigDecimal.ZERO);
                if (Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) && activityDetailVO.getUserState() == 0) {
                    // 先计算绝对值
                    List<UserCouponDiKou> couponDiKous1 = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(activityEntity.getActivityEntryFee().subtract(coupon.getAmount()).abs())).collect(Collectors.toList());
                    couponDiKous1.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                        // free 0 值 完全匹配
                        if (i.getAbsAmount().compareTo(BigDecimal.ZERO) == 0) {
                            activityDetailVO.setCouponAmount(i.getAmount());
                            activityDetailVO.setUserCouponDiKou(i);
                        }
                    });
                    if (Objects.isNull(activityDetailVO.getUserCouponDiKou())) {
                        List<UserCouponDiKou> couponDiKous = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(activityEntity.getActivityEntryFee().subtract(coupon.getAmount()))).collect(Collectors.toList());
                        // 筛选 插值为 负数值
                        List<UserCouponDiKou> couponDiKous2 = couponDiKous.stream().filter(coupon -> coupon.getAbsAmount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(couponDiKous2)) {
                            couponDiKous2.stream().max(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                activityDetailVO.setCouponAmount(i.getAmount());
                                activityDetailVO.setUserCouponDiKou(i);
                            });
                        }
                        if (Objects.isNull(activityDetailVO.getUserCouponDiKou())) {
                            {
                                couponDiKous.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                    activityDetailVO.setCouponAmount(i.getAmount());
                                    activityDetailVO.setUserCouponDiKou(i);
                                });
                            }
                        }
                    }
                }
            }
        } else {
            activityDetailVO.setCouponAmount(userCoupon.getAmount());
            UserCouponDiKou userCouponDiKou = new UserCouponDiKou();
            BeanUtils.copyProperties(userCoupon, userCouponDiKou);
            activityDetailVO.setUserCouponDiKou(userCouponDiKou);
        }
    }

    @Autowired
    @Lazy
    private CouponService couponService;

    @Override
    public void wrapperActivityRewardDetailByActivityType(ZnsRunActivityEntity activityEntity, Map<String, Object> jsonObjectConfig, RunActivityRewardDetailVO runActivityRewardDetailVO, ZnsUserEntity loginUser) {
        runActivityRewardDetailVO.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode()));
        List<Map> runningGoalsAward = getRunningGoalsAward(jsonObjectConfig);
        runActivityRewardDetailVO.setRunningGoalsAward(runningGoalsAward);
        runActivityRewardDetailVO.setRunningGoalScoreAward(getRunningGoalsScoreAward(jsonObjectConfig, ApiConstants.RUNNING_GOAL_SCORE_AWARD));
        List<Map<String, Object>> jsonArray = JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD), new TypeReference<List<Map<String, Object>>>() {
        });
        if (Objects.nonNull(jsonArray)) {
            List<Map> runningGoalCouponAward = JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD), new TypeReference<List<Map>>() {
            });
            List<Map> list = Lists.newArrayList();
            for (Map map : runningGoalCouponAward) {
                log.info(JsonUtil.writeString(map));
                for (Object key : map.keySet()) {
                    Object s = map.get(key);
                    if (String.valueOf(key).equals("goal")) {
                        continue;
                    }
                    if (StringUtils.hasText(String.valueOf(s))) {
                        Map<String, Object> jsonObject = JsonUtil.readValue(s);
                        Long couponId = MapUtil.getLong(jsonObject.get("couponId"));
                        if (Objects.nonNull(couponId)) {
                            Coupon coupon = couponService.selectCouponById(couponId);
                            jsonObject.put("couponType", coupon.getCouponType());
                            jsonObject.put("amount", coupon.getAmount());
                            jsonObject.put("couponName", coupon.getTitle());
                        }
                        map.put(key, jsonObject);
                    }
                    list.add(map);
                }
            }
            runActivityRewardDetailVO.setRunningGoalCouponAward(list);
        }
        //组装最大奖励 2023/11/7 补丁
        assemblyMaximumReward(runActivityRewardDetailVO);

    }

    private void assemblyMaximumReward(RunActivityRewardDetailVO activityDetailVO) {
        //最大奖励金额
        List<Map> runningGoalsAwards = activityDetailVO.getRunningGoalsAward();
        BigDecimal maxReward = BigDecimal.ZERO;
        BigDecimal goalAward = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(runningGoalsAwards)) {
            for (Map map : runningGoalsAwards) {
                goalAward = new BigDecimal(MapUtils.getDouble(map, "award", 0d));
                BigDecimal firstAward = new BigDecimal(MapUtils.getDouble(map, "firstAward", 0d));
                maxReward = goalAward.add(firstAward);
            }
        }
        activityDetailVO.setMaxReward(maxReward);

        //最大奖励积分
        List<Map> ScoreAward = activityDetailVO.getRunningGoalScoreAward();
        Long maxScore = 0l;
        Long goalScore = 0l;
        if (!CollectionUtils.isEmpty(ScoreAward)) {
            for (Map map : ScoreAward) {
                goalScore = MapUtils.getLong(map, "award", 0l);
                Long firstAward = MapUtils.getLong(map, "firstAward", 0l);
                maxScore = goalScore + firstAward;
            }
        }
        activityDetailVO.setMaxScore(maxScore);
    }

    /**
     * 跑步结束处理
     *
     * @param detailsEntity
     * @param activityEntity
     */
    public void runEnd(ZnsUserRunDataDetailsEntity detailsEntity, ZnsRunActivityEntity activityEntity) {
        log.info("runEnd 开始执行 activityId:{},detailsId:{}", activityEntity.getId(), detailsEntity.getId());
        //查询用户
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), detailsEntity.getUserId());
        Integer completeRuleType = activityEntity.getCompleteRuleType();

        if (completeRuleType == 1 && activityUser.getTargetRunMileage() > 0 && activityUser.getRunMileage().intValue() >= activityUser.getTargetRunMileage()) {
            activityUser.setIsComplete(1);
            activityUser.setCompleteTime(ZonedDateTime.now());
            activityUser.setSubState(1);
        } else if (completeRuleType == 2 && activityUser.getTargetRunTime() > 0 && activityUser.getRunTime() >= activityUser.getTargetRunTime()) {
            activityUser.setIsComplete(1);
            activityUser.setCompleteTime(ZonedDateTime.now());
            activityUser.setSubState(1);
        } else {
            activityUser.setSubState(2);
            runActivityUserService.updateById(activityUser);
            return;
        }

        completeAward(activityUser, detailsEntity, activityEntity);
    }

    public void completeAward(ZnsRunActivityUserEntity activityUser, ZnsUserRunDataDetailsEntity detailsEntity, ZnsRunActivityEntity activityEntity) {
        if (detailsEntity.getIsCheat() == 1) {
            log.info("完赛奖励 activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",完赛奖励 处理结束，用户有作弊行为，不发放完赛奖励");
            return;
        }
        //跑步目标奖励
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        Map<String, Object> data = JsonUtil.readValue(activityEntity.getActivityConfig());
        log.info("当时的配置 activityConfig = " + activityEntity.getActivityConfig());
        List<Map> runningGoalsAwardList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
        Map<Integer, Map> runningGoalsAwardMap = runningGoalsAwardList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));

        List<Map> runningGoalsAwardScoreList = null;
        Map<Integer, Map> runningGoalsAwardScoreMap = null;
        if (data.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD) != null) {
            runningGoalsAwardScoreList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD), Map.class);
            runningGoalsAwardScoreMap = runningGoalsAwardScoreList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
        }
        List<Map> runningGoalsAwardCouponList = null;
        Map<Integer, Map> runningGoalsAwardCouponMap = null;
        if (data.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD) != null) {
            runningGoalsAwardCouponList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD), Map.class);
            runningGoalsAwardCouponMap = runningGoalsAwardCouponList.stream().collect(Collectors.toMap(map -> MapUtils.getInteger(map, "goal"), Function.identity(), (x, y) -> x));
        }
        Integer target = activityUser.getTargetRunMileage();
        if (activityEntity.getCompleteRuleType() == 2) {
            target = activityUser.getTargetRunTime();
        }
        Map awardMap = runningGoalsAwardMap.get(target);
        Map awardScoreMap = runningGoalsAwardScoreMap.get(target);
        Map<String, ActivityCouponDto> awardCouponMap = runningGoalsAwardCouponMap.get(target);

        //修改币种切换处理
        List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
        if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
            log.info("币种切换不发放");
            awardMap = null;
        }
        //权益处理
        List<ActivityBrandRightsInterests> brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterestsList(activityEntity, activityUser.getUserId());
        //金额奖励发放
        if (Objects.nonNull(awardMap)) {
            Double awardDouble = MapUtils.getDouble(awardMap, "award", 0d);
            BigDecimal award = new BigDecimal(awardDouble);
            BigDecimal extraCompleteAward = BigDecimal.ZERO;
            log.info("完赛奖励 activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",完赛奖励 award = " + award);
            ActivityBrandRightsInterests completeRightsInterest = activityBrandRightsInterestsService.getBrandRightsInterest(brandRightsInterests, BrandRightsInterestEnum.COMPLETION_REWARD.getStatusCode());
            if (Objects.nonNull(completeRightsInterest)) {
                BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(new BigDecimal(awardDouble), completeRightsInterest.getMultiple());
                award = award.add(brandAward);
                extraCompleteAward = brandAward;
                log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",完赛奖励 brandAward = " + brandAward + ",award=" + award + ",extraCompleteAward=" + extraCompleteAward);
            }

            //奖励发放
            BigDecimal runAward = activityUser.getRunAward().add(award);
            ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(activityUser.getUserId());
            if (accountEntity != null) {
                runAward = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), runAward);
            }
            activityUser.setRunAward(runAward);
            activityUser.setRewardTime(ZonedDateTime.now());
            Long detailId = handleRunAward(award, activityUser, activityEntity, extraCompleteAward, AccountDetailSubtypeEnum.OFFICIAL_TEAM_RUM_FINISH);
            //奖励明细分类处理
            List<UserAccountDetailSub> accountDetailSubs = new ArrayList<>();
            if (activityUser.getIsCheat() == 0) {
                userAccountDetailSubService.addDetailSubToList(accountDetailSubs, activityUser.getUserId(), detailId, activityUser.getActivityId(), 5, new BigDecimal(awardDouble), extraCompleteAward);
            }
            if (!CollectionUtils.isEmpty(accountDetailSubs)) {
                userAccountDetailSubService.saveBatch(accountDetailSubs);
            }
            runActivityUserService.updateById(activityUser);
        }
        //积分
        if (runningGoalsAwardScoreMap != null) {
            if (Objects.nonNull(awardScoreMap)) {
                Integer awardScoreInt = MapUtil.getInteger(awardScoreMap.get("award"), 0);
                log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",awardScoreInt = " + awardScoreInt);
                ActivityBrandRightsInterests scoreRightsInterest = activityBrandRightsInterestsService.getBrandRightsInterest(brandRightsInterests, BrandRightsInterestEnum.INTEGRAL.getStatusCode());
                Integer extraScore = 0;
                if (Objects.nonNull(scoreRightsInterest)) {
                    BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(new BigDecimal(awardScoreInt), scoreRightsInterest.getMultiple());
                    extraScore = brandAward.intValue();
                    awardScoreInt = awardScoreInt + brandAward.intValue();
                    log.info("activityUserId" + activityUser.getId() + ",userId=" + activityUser.getUserId() + ",brandAward = " + brandAward + ",extraScore=" + extraScore + ",awardScoreInt=" + awardScoreInt);
                }

                log.info("官方组队跑赛事完赛分数 activityId = " + activityUser.getActivityId() + ",userId = " + activityUser.getUserId() + "awardScoreInt = " + awardScoreInt);
                if (awardScoreInt > 0) {
                    ActivityUserScore activityUserScore = activityUserScoreDao.selectActivityUserScoreByActivityIdUserIdRank(activityUser.getActivityId(),
                            activityUser.getUserId(), activityUser.getRank());
                    if (activityUserScore == null) {
                        activityUserScoreService.increaseAmount(awardScoreInt, activityUser.getActivityId(), activityUser.getUserId(), activityUser.getRank(), extraScore, null);
                    }
                }
            }
        }
        if (runningGoalsAwardCouponMap != null) {
            awardCouponMap = runningGoalsAwardCouponMap.get(target);
            if (Objects.nonNull(awardCouponMap)) {
                ActivityCouponDto activityCouponDto = JsonUtil.readValue(awardCouponMap.get("award"), ActivityCouponDto.class);
                log.info("赛事 award awardCouponMap activityId = " + activityUser.getActivityId() + ",userId = " + activityUser.getUserId() + ",activityCouponDto = " + JsonUtil.writeString(activityCouponDto));
                if (activityCouponDto != null) {
                    for (int num = 0; num < activityCouponDto.getNum(); num++) {
                        userCouponManager.sendUserCoupon(activityCouponDto.getCouponId(), activityUser.getUserId(), activityEntity.getId());
                    }
                }
            }
        }
    }
}
