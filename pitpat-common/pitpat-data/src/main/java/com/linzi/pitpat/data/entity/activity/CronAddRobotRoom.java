package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*机器人加入房间配置表
 *
 * <AUTHOR>
 * @since 2022-09-15
 */

@Data
@NoArgsConstructor
@TableName("zns_cron_add_robot_room")
public class CronAddRobotRoom implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //表达式
    private String cronExpression;
    //路线id
    private Long routeId;
    //路线类型，2 2d , 3 3D
    private Integer routeType;
    //房间号
    private Long roomId;
    //最大用户数
    private Integer maxUserCount;
    //跑步速度范围
    private String vRandomRange;
    //跑步时间范围
    private String tRandomRange;
    //投放概率
    private BigDecimal randomRate;

    @Override
    public String toString() {
        return "CronAddRobotRoom{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",cronExpression=" + cronExpression +
                ",routeId=" + routeId +
                ",routeType=" + routeType +
                ",roomId=" + roomId +
                ",maxUserCount=" + maxUserCount +
                ",vRandomRange=" + vRandomRange +
                ",tRandomRange=" + tRandomRange +
                ",randomRate=" + randomRate +
                "}";
    }
}
