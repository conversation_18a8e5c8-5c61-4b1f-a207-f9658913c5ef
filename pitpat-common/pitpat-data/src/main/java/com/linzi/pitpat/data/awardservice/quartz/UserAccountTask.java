package com.linzi.pitpat.data.awardservice.quartz;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 用户账户处理
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Component("userAccountTask")
@Slf4j
public class UserAccountTask {

    @Resource(name = "asyncExecutor")
    private ThreadPoolTaskExecutor executor;

    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ZnsUserAccountService userAccountService;

    /**
     * 定时未付款流水
     */
    //@Scheduled(cron = "0 0/1 * * * ?")
    public void nonPayment() throws InterruptedException {
        try {
            OrderUtil.addLogNo();
            //查询开始时间-11 到 -10 分钟
            ZonedDateTime now = ZonedDateTime.now();
            ZonedDateTime start = DateUtil.addDays(now, -1);
            ZonedDateTime end = now.minusMinutes(15);
            //查询交易未付款数据
            List<ZnsUserAccountDetailEntity> list = userAccountDetailService.findListForNonPayment(start, end);

            if (CollectionUtils.isEmpty(list)) {
                log.info("inTransactionDeal 定时任务处理结束，未查询到未付款数据");
                return;
            }
            for (ZnsUserAccountDetailEntity entity : list) {
                //睡眠500毫秒，防止调用频繁
                Thread.sleep(500);
                nonPaymentAccountDetailDeal(entity);
            }
        } finally {
            OrderUtil.removeLogNo();
        }
    }

    private void nonPaymentAccountDetailDeal(ZnsUserAccountDetailEntity entity) {
        //查询交易订单信息
        //无交易流水
        if (Objects.isNull(entity.getTradeNo()) || StringUtil.isEmpty(entity.getTradeNo())) {
            userAccountDetailService.delete(entity.getId(), "定时取消未付款流水");
            return;
        }

        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        executor.execute(() -> {
            try {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                userAccountService.cancelPayment(entity.getTradeNo(), MapUtil.getInteger(entity.getUserId()));
            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                OrderUtil.removeLogNo();
            }
        });
        log.info("定时取消未付款流水，三方流水：{}，billNo：{}", entity.getTradeNo(), entity.getBillNo());
    }
}
