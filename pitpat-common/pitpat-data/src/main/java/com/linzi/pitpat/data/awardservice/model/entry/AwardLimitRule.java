package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 奖励配置限制表
 *
 * <AUTHOR>
 * @since 2023-07-18
 */

@Data
@NoArgsConstructor
@TableName("zns_award_limit_rule")
public class AwardLimitRule implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.AwardLimitRule:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                 // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";                    //
    public final static String gmt_create = CLASS_NAME + "gmt_create";                  //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";              //
    public final static String limit_send_type = CLASS_NAME + "limit_send_type";        // 限制发放类型，1：排名基础奖励，2：完赛，3：被挑战奖励 4：挑战成功奖励，5：挑战失败奖励 6：排名人头奖励 7:发起奖励 8：参与奖励 9:胜者奖励 10奖金池基础 11：奖金池占比
    public final static String limit_award_type = CLASS_NAME + "limit_award_type";      // 奖励类型，1：金额，2：券，3：积分 4:皮肤
    public final static String limit_people = CLASS_NAME + "limit_people";              // 限制人数
    public final static String activity_type = CLASS_NAME + "activity_type";            // 活动类型，具体与activity_confg中类型一致
    public final static String activity_sub_type = CLASS_NAME + "activity_sub_type";    // 子类型，pk赛使用，1：随机PK，2：好友PK
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //限制发放类型，1：排名基础奖励，2：完赛，3：被挑战奖励 4：挑战成功奖励，5：挑战失败奖励 6：排名人头奖励 7:发起奖励 8：参与奖励 9:胜者奖励 10奖金池基础 11：奖金池占比
    private Integer limitSendType;
    //奖励类型，1：金额，2：券，3：积分 4:皮肤
    private Integer limitAwardType;
    //限制人数
    private Integer limitPeople;
    //活动类型，具体与activity_confg中类型一致
    private Integer activityType;
    //子类型，pk赛使用，1：随机PK，2：好友PK
    private Integer activitySubType;

    @Override
    public String toString() {
        return "AwardLimitRule{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",limitSendType=" + limitSendType +
                ",limitAwardType=" + limitAwardType +
                ",limitPeople=" + limitPeople +
                ",activityType=" + activityType +
                ",activitySubType=" + activitySubType +
                "}";
    }
}
