package com.linzi.pitpat.data.activityservice.strategy;

import com.google.api.client.util.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.dto.RankAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.ZnsRunActivityUserRankDto;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityConfigResp;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.awardservice.model.dto.RunDetailDataDto;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.TreadmillBrandEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/12 9:40
 */
@Service
@Slf4j
public class TaskActivityStrategy extends BaseActivityStrategy {
    @Resource
    private OperationalActivityService operationalActivityService;
    @Resource
    private ZnsUserEquipmentService userEquipmentService;
    @Resource
    private ZnsUserAccountService znsUserAccountService;

    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;


    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {

    }

    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        return null;
    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {
        if (Objects.nonNull(activity.getApplicationStartTime()) && Objects.nonNull(activity.getApplicationEndTime())) {
            ZonedDateTime now = ZonedDateTime.now();
            if (now.compareTo(activity.getApplicationStartTime()) < 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.notStart")); //"Registration has not started"
            }
            if (now.compareTo(activity.getApplicationEndTime()) > 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.ended")); //"Registration has ended"
            }
        }
        if (activity.getStatus() == -1) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.ended")); //"Registration has ended"
        }
        //校验活动国家跟用户国家是否相同
        if (notContainsCountry(activity, user)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.enroll.mismatch.region"));
        }

        //品牌权益处理
        boolean brandRightsInterestsUser = isBrandRightsInterestsUser(user.getId(), request, activity);
        UserCoupon userCoupon = getUserCoupon(request, user, activity);

        // 验证优惠券的合法性
        Long couponId = request.getUserCouponId();
        ZonedDateTime now = ZonedDateTime.now();

        // 支付保证金逻辑
        Result payResult = handlePayOperationalActivity(user, request, userCoupon != null ? userCoupon.getAmount() : BigDecimal.ZERO, brandRightsInterestsUser, activity);
        if (null != payResult) {
            return payResult;
        }

        //判断是否包含积分，如果包含积分，则需要积分抵扣
        useUserScore(activity, user.getId(), false);

        // 支付成功之后,将优惠券消费掉.
        if (couponId != null && couponId > 1) {
            UserCoupon newUserCoupon = new UserCoupon();
            newUserCoupon.setStatus(2);
            newUserCoupon.setGmtModified(now);
            newUserCoupon.setGmtUse(now);
            newUserCoupon.setId(couponId);
            newUserCoupon.setUseActivityId(activity.getId());
            userCouponService.update(newUserCoupon);
        }

        //任务处理
        List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activity.getId(), user.getId());
        BigDecimal award = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(runActivityUserTasks)) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
            List<RunActivityUserTask> tasks = JsonUtil.readList(jsonObject.get("tasks"), RunActivityUserTask.class);
            for (RunActivityUserTask task : tasks) {
                task.setUserId(user.getId());
                task.setActivityId(activity.getId());
                task.setActivityType(8);
                if (task.getLevel() == 1) {
                    task.setIsUnlock(1);
                }
                award = BigDecimalUtil.add(award, task.getWinReward());
                runActivityUserTaskService.save(task);
            }
        }
        //查询是否有有报名
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activity.getId(), user.getId());
        if (Objects.isNull(activityUser)) {
            ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(user.getId());
            if (accountEntity != null) {
                award = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), award);
            }
            ZnsRunActivityUserEntity runActivityUser = new ZnsRunActivityUserEntity();
            runActivityUser.setActivityId(activity.getId());
            runActivityUser.setUserId(user.getId());
            runActivityUser.setNickname(user.getFirstName());
            runActivityUser.setActivityType(8);
            runActivityUser.setRunAward(award);
            runActivityUser.setUserType(2);
            runActivityUser.setUserState(1);
            runActivityUserService.save(runActivityUser);

            runActivityService.addOfficialActivityUserCount(activity.getId());
        }
        return CommonResult.success();
    }


    private Result handlePayOperationalActivity(ZnsUserEntity user, HandleActivityRequest request, BigDecimal couponAmount, boolean brandRightsInterestsUser, ZnsRunActivityEntity activity) {
        if (activity.getBonusRuleType() == 0 || Arrays.asList(1, 4).contains(activity.getBonusRuleType())) {
            return null;
        }
        String password = request.getPassword();
        if (user.getIsRobot() != 1) {
            Result passwordResult = userAccountService.checkPassword(user.getId(), password, false);
            if (null != passwordResult) {
                passwordResult.setData(null);
                return passwordResult;
            }
        }
        // 开始支付
        RunActivityPayRequest payRequest = new RunActivityPayRequest();
        //当前品牌权益免费
        if (brandRightsInterestsUser) {
            couponAmount = activity.getActivityEntryFee();
            payRequest.setPrivilegeBrand(request.getPrivilegeBrand());
            payRequest.setBrandRightsInterests(request.getBrandRightsInterests());
        }

        payRequest.setUserId(user.getId());
        payRequest.setPayType(0);
        payRequest.setPayPassword(password);
        payRequest.setUserCouponId(request.getUserCouponId());
        BigDecimal entryFee = activity.getActivityEntryFee();
        BigDecimal discountedAmount = entryFee.subtract(couponAmount);
        if (discountedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            discountedAmount = BigDecimal.ZERO;
        }
        payRequest.setAmount(discountedAmount);
        log.info("报名费用:{},折扣金额:{},实际支付金额:{}", entryFee, couponAmount, discountedAmount);

        payRequest.setActivityId(activity.getId());
        payRequest.setOperationalActivityId(request.getOperationalActivityId());
        payRequest.setAccountDetailTypeEnum(AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_ENTRY_FEE);

        Result payResult = userAccountService.payByBalance(payRequest);
        if (null != payResult && !CommonError.SUCCESS.getCode().equals(payResult.getCode())) {
            // 支付失败
            return payResult;
        }

        return null;
    }

    private UserCoupon getUserCoupon(HandleActivityRequest request, ZnsUserEntity user, ZnsRunActivityEntity activity) {
        UserCoupon userCoupon = null;
        // 验证优惠券的合法性
        Long couponId = request.getUserCouponId();
        ZonedDateTime now = ZonedDateTime.now();
        if (couponId != null && couponId > 1) {
            log.info("请求参数为:{}", JsonUtil.writeString(request));
            userCoupon = userCouponService.findByQuery(new UserCouponQuery().setUserId(user.getId()).setStatus(List.of(0)).setTime(now).setCouponId(couponId));
            if (userCoupon == null) {
                throw new BaseException("The coupon does not exist or has expired!");
            }
            Result result = activityCouponConfigService.canUseCoupon(userCoupon, activity);
            if (Objects.nonNull(result)) {
                throw new BaseException(result.getMsg());
            }
            BigDecimal frontendAmount = request.getFrontendAmount();
            BigDecimal discountAmount = userCoupon.getAmount();
            BigDecimal entryFee = activity.getActivityEntryFee();
            BigDecimal actualPaymentAmount = entryFee.subtract(discountAmount);
            if (actualPaymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                actualPaymentAmount = BigDecimal.ZERO;
            }
            if (frontendAmount == null || frontendAmount.compareTo(actualPaymentAmount) != 0) {
                log.warn("前端传递的金额:{},,报名费用:{},优惠券折扣金额:{},实际支付金额:{}",
                        frontendAmount, entryFee, discountAmount, actualPaymentAmount
                );
                throw new BaseException(ActivityError.ACTUAL_PAYMENT_AMOUNT_IS_UNCORRECT.getMsg(), ActivityError.ACTUAL_PAYMENT_AMOUNT_IS_UNCORRECT.getCode());
            }
        }
        return userCoupon;
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return null;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return null;
    }

    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        if (awardAmount.compareTo(BigDecimal.ZERO) > 0) {
            awardAmount = getUserCurrencyAmount(activityUser.getUserId(), awardAmount);
            extraAward = getUserCurrencyAmount(activityUser.getUserId(), extraAward);
            // 给用户余额发送奖励
            userAccountService.increaseAmount(awardAmount, activityUser.getUserId(), true);
            // 新增用户奖励余额明细
            String billNo = NanoId.randomNanoId();
            ;
            ZonedDateTime tradeTime = ZonedDateTime.now();
            return userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.SPECIAL_EVENT_RANKING_AWARD, Objects.nonNull(subType) ? subType.getType() :
                            AccountDetailSubtypeEnum.SPECIAL_EVENT_RANKING_AWARD.getType(), 1, awardAmount, billNo, tradeTime, activityUser.getActivityId(),
                    activityUser.getActivityId(), null, activityUser.getActivityType(), 0L, "", null, null, null, extraAward);
        }
        return null;
    }

    @Override
    public ActivityRunningReportBaseVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        return null;
    }

    public void runDataEnd(ZnsRunActivityEntity activityEntity, ZnsUserRunDataDetailsEntity detailsEntity, Integer activityUserEnd) {
        //更新活动用户表
        if (Objects.isNull(activityUserEnd) || activityUserEnd != 1) {
            log.info("MarathonActivityStrategy end activityUserEnd ！= 1");
            return;
        }
        ZnsRunActivityUserEntity activityUserEntity = runActivityUserService.findActivityUser(activityEntity.getId(), detailsEntity.getUserId());
        if (Objects.isNull(activityUserEntity)) {
            log.info("MarathonActivityStrategy end activityUserEntity 为空");
            return;
        }
        if (Objects.isNull(activityUserEntity.getUserActivityEndTime())) {
            log.info("MarathonActivityStrategy end userActivityEndTime 为空");
            return;
        }
        if (activityUserEntity.getUserActivityEndTime().compareTo(ZonedDateTime.now()) < 0) {
            log.info("MarathonActivityStrategy end userActivityEndTime 结束");
            return;
        }
        // 证书获取
        dealUserCertificate(activityEntity, activityUserEntity, detailsEntity);

        if (activityUserEntity.getRunAward().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("MarathonActivityStrategy end 金额为0");
            return;
        }
        // 历史旧版本兼容
        ActivityConfigResp configResp = JsonUtil.readValue(activityEntity.getActivityConfig(), ActivityConfigResp.class);

        if (Objects.isNull(configResp.getFinishAward()) && activityUserEntity.getRunAward().compareTo(BigDecimal.ZERO) > 0) {
            boolean brandUser = userEquipmentService.isBrandUser(activityUserEntity.getUserId(), activityEntity.getPrivilegeBrand());
            //品牌权益处理
            boolean brandRightsInterestsAward = dealBrandRightsInterestsAward(activityEntity, activityUserEntity, brandUser);

            activityUserEntity.setUserState(ActivityUserStateEnum.ENDED.getState());
            runActivityUserService.updateById(activityUserEntity);
            BigDecimal finishAward = activityUserEntity.getRunAward();
            BigDecimal currencyAmount = getUserCurrencyAmount(activityUserEntity.getUserId(), finishAward);
            List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUserEntity.getUserId(), 0, -1);
            if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityUserEntity.getActivityId().toString())) {
                log.info("币种切换不发放");
            } else {
                // 给用户余额发送奖励
                userAccountService.increaseAmount(currencyAmount, detailsEntity.getUserId(), true);
                // 新增用户奖励余额明细
                String billNo = NanoId.randomNanoId();
                ;
                ZonedDateTime tradeTime = ZonedDateTime.now();
                userAccountDetailService.addRunActivityAccountDetail0131(detailsEntity.getUserId(), AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_AWARD,
                        0, 1, activityUserEntity.getRunAward(), billNo, tradeTime, 0L,
                        activityEntity.getId(), 0L, RunActivityTypeEnum.TASK_ACTIVITY.getType(), 0L, activityEntity.getAwardRemark(),
                        brandRightsInterestsAward ? activityEntity.getPrivilegeBrand() : null, brandRightsInterestsAward ? activityEntity.getBrandRightsInterestsAward() : null, acceptCountDecimal, BigDecimal.ZERO);
            }
//
//            String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.TASK_ACTIVITY_AWARD"), activityEntity.getActivityTitle(), activityUserEntity.getRunAward());
//            ImMessageBo bo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
//            bo.setJumpType("0");
//
//            OperationalActivity operationalActivity = operationalActivityService.selectByRunActivityId(activityEntity.getId());
//            if (Objects.isNull(operationalActivity)) {
//                return;
//            }
//            bo.setJumpValue(operationalActivity.getActivityUrl());

//            appMessageService.sendIm("", Arrays.asList(detailsEntity.getUserId()), JsonUtil.writeString(bo), TencentImConstant.TIM_CUSTOM_ELEM, "", activityUserEnd, Boolean.FALSE);
        } else {
            activityUserEntity.setUserState(ActivityUserStateEnum.ENDED.getState());
            runActivityUserService.updateById(activityUserEntity);
        }
    }

    private void dealUserCertificate(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUserEntity, ZnsUserRunDataDetailsEntity detailsEntity) {
        try {
            RunDetailDataDto runDetailDataDto = new RunDetailDataDto();
            Long treadmillId = detailsEntity.getTreadmillId();
            ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findById(treadmillId);
            // 品牌获取
            if (Objects.isNull(treadmillEntity)) {
                log.info("设备品牌获取失败 user_id:{}", activityUserEntity.getUserId());
                return;
            }
            String brand = treadmillEntity.getBrand();
            Integer type = TreadmillBrandEnum.findByName(brand).getType();
            runDetailDataDto.setType(type);
            runDetailDataDto.setBrand(brand);

            //查询所有任务数据
            List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(activityEntity.getId(), detailsEntity.getUserId(), null);
            List<Long> runDataDetailsIds = runActivityUserTasks.stream().map(RunActivityUserTask::getRunDataDetailsId).collect(Collectors.toList());
            List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetailsEntities = userRunDataDetailsService.findByIds(runDataDetailsIds);

            for (ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity : znsUserRunDataDetailsEntities) {
                runDetailDataDto.setRunTime(runDetailDataDto.getRunTime() + znsUserRunDataDetailsEntity.getRunTime());
                runDetailDataDto.setRunMileage(runDetailDataDto.getRunMileage().add(znsUserRunDataDetailsEntity.getRunMileage()));
            }

            Double averagePace = znsUserRunDataDetailsEntities.stream().mapToInt(ZnsUserRunDataDetailsEntity::getAveragePace).average().orElse(0d);
            BigDecimal averageVelocity = znsUserRunDataDetailsEntities.stream().map(ZnsUserRunDataDetailsEntity::getAverageVelocity).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(znsUserRunDataDetailsEntities.size()), 2, BigDecimal.ROUND_HALF_UP);
            runDetailDataDto.setAveragePace(averagePace.intValue());
            runDetailDataDto.setAverageVelocity(averageVelocity);
            runDetailDataDto.setActivityDate(detailsEntity.getLastTime());

            genUserCertificate(activityEntity, activityUserEntity, runDetailDataDto);
        } catch (Exception e) {
            log.error("dealUserCertificate error,e", e);
        }
    }

    private boolean dealBrandRightsInterestsAward(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUserEntity, boolean brandUser) {
        if (activityEntity.getPrivilegeBrand() == -1) {
            log.info("dealBrandRightsInterestsAward activityEntity.getPrivilegeBrand() = -1");
            return false;
        }
        if (activityEntity.getBrandRightsInterestsAward() == -1) {
            log.info("dealBrandRightsInterestsAward activityEntity.getBrandRightsInterestsAward() = -1");
            return false;
        }
        if (!brandUser) {
            log.info("dealBrandRightsInterestsAward brandUser= false");
            return false;
        }
        if (activityEntity.getBrandRightsInterestsAward() == 1) {
            //奖励翻倍
            BigDecimal award = activityUserEntity.getRunAward().multiply(new BigDecimal(2));
            ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(activityUserEntity.getUserId());
            if (accountEntity != null) {
                award = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), award);
            }
            activityUserEntity.setRunAward(award);
        }
        return true;
    }

    private boolean isBrandRightsInterestsUser(Long userId, HandleActivityRequest request, ZnsRunActivityEntity activity) {
        request.setPrivilegeBrand(activity.getPrivilegeBrand());
        request.setBrandRightsInterests(activity.getBrandRightsInterests());
        boolean brandUser = userEquipmentService.isBrandUser(userId, activity.getPrivilegeBrand());
        if (brandUser && activity.getBrandRightsInterests() == 0) {
            return true;
        }
        return false;
    }

    /**
     * @param activityEntity [ZnsRunActivityEntity]
     */
    @Override
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        super.handleRunActivityEnd(activityEntity);
        //完成比赛
        if (activityEntity.getStatus() == 1) {
            // 1.查询所有完赛参赛用户
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .isDelete(0).activityId(activityEntity.getId())
                    .subState(YesNoStatus.YES.getCode()).isComplete(YesNoStatus.YES.getCode())
                    .build();
            List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(list)) {
                log.info("查询所有完赛参赛用户 list size 0 无用户返回");
                return;
            }
            ActivityConfigResp configResp = JsonUtil.readValue(activityEntity.getActivityConfig(), ActivityConfigResp.class);
            List<RankAwardDto> rankAwardLists = configResp.getRankAwardLists();
            if (!CollectionUtils.isEmpty(rankAwardLists)) {
                Map<Integer, RankAwardDto> rankCollect = rankAwardLists.stream().collect(Collectors.toMap(RankAwardDto::getRank, Function.identity()));
                // 2.获取用户排名 并查询奖励发送
                List<ZnsRunActivityUserRankDto> ranks = Lists.newArrayList();
                for (ZnsRunActivityUserEntity activityUser : list) {
                    List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityUser.getActivityId(), activityUser.getUserId());
                    List<Long> detailIds = runActivityUserTasks.stream().map(RunActivityUserTask::getRunDataDetailsId).collect(Collectors.toList());
                    // 计算毫秒值比较
                    int sumTime = getUserAllTaskSumTime(activityEntity, runActivityUserTasks, detailIds);
                    ZnsRunActivityUserRankDto znsRunActivityUserRankDto = new ZnsRunActivityUserRankDto();
                    znsRunActivityUserRankDto.setAllTime(sumTime);
                    BeanUtils.copyProperties(activityUser, znsRunActivityUserRankDto);
                    ranks.add(znsRunActivityUserRankDto);
                }
                ranks = ranks.stream().sorted(Comparator.comparing(ZnsRunActivityUserRankDto::getAllTime)).collect(Collectors.toList());
                int realRank = 0;
                for (int i = 0; i < ranks.size(); i++) {
                    ZnsRunActivityUserRankDto activityUser = ranks.get(i);
                    if (i == 0) {
                        realRank = realRank + 1;
                        activityUser.setRank(realRank);
                    }
                    // 并列排名处理
                    if (i > 0) {
                        if (activityUser.getAllTime().equals(ranks.get(i - 1).getAllTime())) {
                            activityUser.setRank(realRank);
                        } else {
                            realRank = realRank + 1;
                            activityUser.setRank(realRank);
                        }
                    }
                    BigDecimal rankAward;
                    if (activityUser.getRank() >= 1 && activityUser.getRank() <= 3) {
                        RankAwardDto rankAwardDto = rankCollect.get(activityUser.getRank());
                        if (Objects.nonNull(rankAwardDto)) {
                            rankAward = rankAwardDto.getRankWinAward();
                            //修改币种切换处理
                            List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                            if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityUser.getActivityId().toString())) {
                                log.info("币种切换不发放");
                                rankAward = BigDecimal.ZERO;
                            }
                            if (Objects.nonNull(rankAward)) {
                                this.handleRunAward(rankAward, activityUser, activityEntity, BigDecimal.ZERO, null);
                                rankAward = getUserCurrencyAmount(activityUser.getUserId(), rankAward);
                                log.info("马拉松发排名奖id:{},userId:{},已获得奖励:{},rankAward:{}", activityEntity.getId(), activityUser.getUserId(), activityUser.getRunAward(), rankAward);
                                BigDecimal runAward = activityUser.getRunAward().add(rankAward);
                                ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(activityUser.getUserId());
                                if (accountEntity != null) {
                                    runAward = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), runAward);
                                }
                                activityUser.setRunAward(runAward);
                                activityUser.setRewardTime(ZonedDateTime.now());
                                // 3.排名im
                                if (rankAward.compareTo(BigDecimal.ZERO) > 0) {
                                    handleSendRankIm(activityUser, activityEntity);
                                }
                            }
                        }
                    }
                    // 更新排名
                    runActivityUserService.updateById(activityUser);
                }
            }
        }

        //下架处理
        if (activityEntity.getStatus() == -1 && activityEntity.getBonusRuleType() != 1) {
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .activityId(activityEntity.getId()).isDelete(0)
                    .build();
            List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
            for (ZnsRunActivityUserEntity activityUser : list) {
                //退回积分
                useUserScore(activityEntity, activityUser.getUserId(), true);

                if (activityEntity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                    // 保证金、费用退回
                    runActivityProcessManager.cancelActivityRefund(activityEntity, Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? AccountDetailTypeEnum.FEE : AccountDetailTypeEnum.SECURITY_FUND, activityUser.getUserId(), "Deposit return");
                }
            }
        }
    }

    public static void main(String[] args) {
        ArrayList<Object> objects = new ArrayList<>();
        objects.forEach(e -> {
            System.out.printf("hello");
        });
    }

    /**
     * 计算所有关卡总时长
     *
     * @param activityEntity
     * @param runActivityUserTasks
     * @param detailIds
     * @return
     */
    private int getUserAllTaskSumTime(ZnsRunActivityEntity activityEntity, List<RunActivityUserTask> runActivityUserTasks, List<Long> detailIds) {
        int sumTime = 0;
        if (YesNoStatus.YES.getCode().equals(activityEntity.getPropSupport())) {
            Map<Long, List<RunActivityUserTask>> collect = runActivityUserTasks.stream().collect(Collectors.groupingBy(RunActivityUserTask::getRunDataDetailsId));
            List<ZnsUserRunDataDetailsEntity> detailList = userRunDataDetailsService.findByIds(detailIds);
            for (ZnsUserRunDataDetailsEntity userRunDataDetailsEntity : detailList) {
                Integer propEffectTime = collect.get(userRunDataDetailsEntity.getId()).get(0).getPropEffectTime();
                if (Objects.nonNull(propEffectTime) && propEffectTime > 0) {
                    int time = userRunDataDetailsEntity.getRunTimeMillisecond() - propEffectTime * 1000 < 0 ? 0 : userRunDataDetailsEntity.getRunTimeMillisecond() - propEffectTime * 1000;
                    sumTime += time;
                } else {
                    sumTime += userRunDataDetailsEntity.getRunTimeMillisecond();
                }
            }
        } else {
            sumTime = userRunDataDetailsService.sumUserTaskTimeByIds(detailIds);
        }
        return sumTime;
    }

    /**
     * 发送im
     *
     * @param activityUser
     * @param activityEntity
     */
    private void handleSendRankIm(ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity) {
        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.TASK_ACTIVITY_RANK;
        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.TASK_ACTIVITY_RANK"), activityUser.getRank(), activityEntity.getActivityTitle());
        ImMessageBo bo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
        bo.setJumpType("0");
        bo.setJumpValue(mallH5Url + "/operational/runActivity/" + activityEntity.getId() + "/0");
        appMessageService.sendIm("", Arrays.asList(activityUser.getUserId()), JsonUtil.writeString(bo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
    }
}
