package com.linzi.pitpat.data.awardservice.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
public class RunDetailDataDto {
    /**
     * 运动时间(s)
     */
    private Integer runTime;
    /**
     * 运动里程
     */
    private BigDecimal runMileage;
    /**
     * 平均速度(公里/小时)
     */
    private BigDecimal averageVelocity;
    /**
     * 平均配速(秒/公里)
     */
    private Integer averagePace;
    /**
     * 比赛日
     */
    private ZonedDateTime activityDate;
    /**
     * 设备类型
     */
    private Integer type;
    /**
     * 品牌
     */
    private String brand;

    public RunDetailDataDto() {
        this.runTime = 0;
        this.runMileage = BigDecimal.ZERO;
        this.averageVelocity = BigDecimal.ZERO;
        this.averagePace = 0;
    }

    public RunDetailDataDto(Integer runTime, BigDecimal runMileage, BigDecimal averageVelocity, Integer averagePace, ZonedDateTime activityDate, Integer type, String brand) {
        this.runTime = runTime;
        this.runMileage = runMileage;
        this.averageVelocity = averageVelocity;
        this.averagePace = averagePace;
        this.activityDate = activityDate;
        this.type = type;
        this.brand = brand;
    }
}
