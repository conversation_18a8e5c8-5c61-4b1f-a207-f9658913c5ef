package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 用户优惠券表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-26
 */

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.resp.UserCouponResp;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.model.vo.UserMilestoneCoupon;
import com.linzi.pitpat.data.courseservice.model.request.CourseListRequest;
import com.linzi.pitpat.data.courseservice.model.request.CourseUpdateRequest;
import com.linzi.pitpat.data.entity.dto.message.CouponOverdueRemindListDto;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.request.CouponPageQuery;

import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;

public interface UserCouponService {


    UserCoupon selectUserCouponById(Long id);

    UserCoupon getUserCouponByActivityAndUserId(Long activityId, Long userId, List<Integer> couponTypes);

    List<CouponPageVo> selectUserCouponList(Long userId, int status, List<Integer> couponTypes);


    boolean updateIsNew(CourseUpdateRequest po, Long id);

    /**
     * 统计用户卷数量
     * @param id
     * @return
     */
    Integer countUserCanUserCoupon(Long id, Integer appVersion,String mallCountryCode);

    /**
     *
     * @return
     */
    Integer countUserCouponByCondition(Long userId, Long activityId, Integer activityType, Long taskConfigId, String batchNo);

    List<UserCouponDiKou> selectCanUseConponList(ZnsRunActivityEntity activityEntity, Long userId);

    UserCoupon getUserCouponByActivityAndUserIdAndCouponType(Long id, Long userId, int i, String currencyCode);

    List<CouponOverdueRemindListDto> overdueRemindList();

    List<CouponPageVo> getUserNewCouponByUserId(Long id, String languageCode);

    Integer selectHistoryUseCouponCount(Long id);

    void cancelActivityRefund(Long activityId, Long userId, Integer userState);

    List<UserCoupon> selectUserCouponByActivityIdUserId(Long activityId, Long userId);

    UserCoupon getUserCouponByActivityAndUserIdAndStatus(Long activityId, Long userId, Integer status);

    Integer getCountUserCouponByActivityIdAndUserId(Long activityId, Long userId, ZonedDateTime createTime, ZonedDateTime modifieTime);

    List<UserCouponDiKou> selectCanUseConponList(Long activityId, Long userId, Integer activityType, String currencyCode, Long couponId);

    UserCoupon findBySource(Long couponId, Long userId, int source, Long activityId);

    UserCoupon getUserCouponByActIdUserIdCouponType(Long activityId, Long userId, List<Integer> couponType);

    Page selectUserCouponPage(Page page, Long userId);

    Long getAllCouponCount(Long userId);

    List<UserCoupon> selectUserCouponByActivityIdAndSource(Long activityId, int sourceType);

    List<UserCouponDiKou> selectUserCouponByCondition(IPage page, Long userId, Integer couponType,
                                                      Integer status, Long activityId, Integer activityType,
                                                      Long taskConfigId, String batchNo, Long userCouponId,
                                                      String currencyCode, Long onlyCouponId, String newActivityType,
                                                      Long newActivityTaskId, Integer couponMainType);

    List<UserCouponStatisticsDto> selectCouponStatisticsDto(List<Long> couponId);

    List<UserMilestoneCoupon> selectUserMilestoneCoupon(Long userId);

    UserCoupon selectUserCouponByCouponId(Long userId, Long couponId);

    Page<CouponPageVo> getUserCouponPage(Page<CouponPageVo> page, CourseListRequest req);

    Integer selectUserCouponCountByCondition(Long userId, Integer couponType, Integer status, Long activityId,
                                             Integer activityType, Long taskConfigId, String batchNo,
                                             String currencyCode, Long onlyCouponId);

    List<UserCouponResp> findList(IPage page, CouponPageQuery query);

    Page<UserCouponResp> findPage(CouponPageQuery query);

    void insert(UserCoupon userCoupon);

    List<UserCoupon> findListByIds(List<Long> userCouponDetailIdList);

    void update(UserCoupon userCoupon);

    Long findCount(Long userId, Long id);

    void updateBatchByIds(List<UserCoupon> list);

    List<UserCoupon> findListByTimeAndStatus(int status, ZonedDateTime date, ZonedDateTime date1);

    List<UserCoupon> findListBySourceType(Long userId, Long activityId, List<Integer> sourceList, int status);

    List<UserCoupon> findListByActivityIdAndStatus(Long activityId, int status);


    UserCoupon findByQuery(UserCouponQuery userCouponQuery);

    List<UserCoupon> findListByQuery(UserCouponQuery query);

    /**
     * 查询用户指定类型可用的通用优惠券
     */
    List<Long> findUserWholeCouponId(Long userId, Integer couponMainType, Boolean isNowUse);

    /**
     * 查询用户指定类型可用的限定优惠券
     */
    List<Long> findUserSpecialCouponId(Long userId, List<Long> spuIds, Integer couponMainType, Boolean isNowUse);

    UserCoupon findById(Long id);
}
