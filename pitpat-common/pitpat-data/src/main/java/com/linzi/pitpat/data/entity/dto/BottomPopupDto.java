package com.linzi.pitpat.data.entity.dto;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @Date 2023-06-29
 */
@Data
@NoArgsConstructor
@Getter
@Setter
public class BottomPopupDto extends PageQuery implements Serializable {
    //弹窗标题
    private String title;
    //弹窗类型（0：活动引导，1：问卷，2：评分）',
    private Integer type;
    //页面类型（0：页面，1：活动，2:课程）,
    private Integer pageType;
    //有效开始时间
    private ZonedDateTime startTime;
    //有效结束时间
    private ZonedDateTime endTime;
}
