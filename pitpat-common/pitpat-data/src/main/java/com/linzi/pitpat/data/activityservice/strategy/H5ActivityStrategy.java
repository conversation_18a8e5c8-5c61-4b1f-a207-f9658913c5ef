package com.linzi.pitpat.data.activityservice.strategy;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.awardservice.model.dto.RunDetailDataDto;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.TreadmillBrandEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/12 9:40
 */
@Service
@Slf4j
public class H5ActivityStrategy extends BaseActivityStrategy {
    @Resource
    private OperationalActivityService operationalActivityService;
    @Resource
    private ZnsUserEquipmentService userEquipmentService;
    @Resource
    private ZnsUserAccountService userAccountService;


    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {

    }

    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        return null;
    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission,
                                          Long taskId, HandleActivityRequest request, boolean checkVersion) {
        if (Objects.nonNull(activity.getApplicationStartTime()) && Objects.nonNull(activity.getApplicationEndTime())) {
            ZonedDateTime now = ZonedDateTime.now();
            if (now.compareTo(activity.getApplicationStartTime()) < 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.notStart"));
            }
            if (now.compareTo(activity.getApplicationEndTime()) > 0) {
                //return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.ended"));
                throw new BizI18nException("activity.apply.time.ended");
            }
        }
        if (activity.getStatus() == -1) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.ended"));
        }

        //校验活动国家跟用户国家是否相同
        if (notContainsCountry(activity, user)) {
            return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), ActivityError.COUNTRY_ERROR.getMsg());
        }

        // 支付保证金逻辑
        Result payResult = handlePayOperationalActivity(user, request, BigDecimal.ZERO, false, activity);
        if (null != payResult) {
            return payResult;
        }

        //查询是否有有报名
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activity.getId(), user.getId());
        if (Objects.isNull(activityUser)) {
            ZnsRunActivityUserEntity runActivityUser = new ZnsRunActivityUserEntity();
            runActivityUser.setActivityId(activity.getId());
            runActivityUser.setUserId(user.getId());
            runActivityUser.setNickname(user.getFirstName());
            runActivityUser.setActivityType(8);
            runActivityUser.setRunAward(BigDecimal.ZERO);
            runActivityUser.setUserType(2);
            runActivityUser.setUserState(1);
            runActivityUserService.save(runActivityUser);
        }
        return CommonResult.success();
    }

    private Result handlePayOperationalActivity(ZnsUserEntity user, HandleActivityRequest request, BigDecimal couponAmount, boolean brandRightsInterestsUser, ZnsRunActivityEntity activity) {
        if (activity.getBonusRuleType() == 0) {
            return null;
        }
        String password = request.getPassword();
        if (user.getIsRobot() != 1) {
            Result passwordResult = userAccountService.checkPassword(user.getId(), password, false);
            if (null != passwordResult) {
                passwordResult.setData(null);
                return passwordResult;
            }
        }
        // 开始支付
        RunActivityPayRequest payRequest = new RunActivityPayRequest();
        //当前品牌权益免费
        if (brandRightsInterestsUser) {
            couponAmount = activity.getActivityEntryFee();
            payRequest.setPrivilegeBrand(request.getPrivilegeBrand());
            payRequest.setBrandRightsInterests(request.getBrandRightsInterests());
        }

        payRequest.setUserId(user.getId());
        payRequest.setPayType(0);
        payRequest.setPayPassword(password);
        payRequest.setUserCouponId(request.getUserCouponId());
        BigDecimal entryFee = activity.getActivityEntryFee();
        BigDecimal discountedAmount = entryFee.subtract(couponAmount);
        if (discountedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            discountedAmount = BigDecimal.ZERO;
        }
        payRequest.setAmount(discountedAmount);
        log.info("报名费用:{},折扣金额:{},实际支付金额:{}", entryFee, couponAmount, discountedAmount);

        payRequest.setActivityId(activity.getId());
        payRequest.setOperationalActivityId(request.getOperationalActivityId());
        payRequest.setAccountDetailTypeEnum(AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_ENTRY_FEE);

        Result payResult = userAccountService.payByBalance(payRequest);
        if (null != payResult && !CommonError.SUCCESS.getCode().equals(payResult.getCode())) {
            // 支付失败
            return payResult;
        }

        return null;
    }

    private UserCoupon getUserCoupon(HandleActivityRequest request, ZnsUserEntity user, ZnsRunActivityEntity activity) {
        UserCoupon userCoupon = null;
        // 验证优惠券的合法性
        Long couponId = request.getUserCouponId();
        ZonedDateTime now = ZonedDateTime.now();
        if (couponId != null && couponId > 1) {
            log.info("请求参数为:{}", request);
            userCoupon = userCouponService.findByQuery(new UserCouponQuery().setUserId(user.getId()).setStatus(List.of(0)).setTime(now).setCouponId(couponId));
            if (userCoupon == null) {
                throw new BaseException(I18nMsgUtils.getMessage("coupon.notExist"));
            }
            Result result = activityCouponConfigService.canUseCoupon(userCoupon, activity);
            if (Objects.nonNull(result)) {
                throw new BaseException(result.getMsg());
            }
            BigDecimal frontendAmount = request.getFrontendAmount();
            BigDecimal discountAmount = userCoupon.getAmount();
            BigDecimal entryFee = activity.getActivityEntryFee();
            BigDecimal actualPaymentAmount = entryFee.subtract(discountAmount);
            if (actualPaymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                actualPaymentAmount = BigDecimal.ZERO;
            }
            if (frontendAmount == null || frontendAmount.compareTo(actualPaymentAmount) != 0) {
                log.warn("前端传递的金额:{},,报名费用:{},优惠券折扣金额:{},实际支付金额:{}",
                        frontendAmount, entryFee, discountAmount, actualPaymentAmount
                );
                throw new BaseException(I18nMsgUtils.getMessage("payment.amount.incorrect"), ActivityError.ACTUAL_PAYMENT_AMOUNT_IS_UNCORRECT.getCode()); //actual payment amount
                // is incorrect
            }
        }
        return userCoupon;
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return null;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return null;
    }

    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        return null;
    }

    @Override
    public ActivityRunningReportBaseVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        return null;
    }

    public void runDataEnd(ZnsRunActivityEntity activityEntity, ZnsUserRunDataDetailsEntity detailsEntity, Integer activityUserEnd) {
        //更新活动用户表
        if (Objects.isNull(activityUserEnd) || activityUserEnd != 1) {
            log.info("MarathonActivityStrategy end activityUserEnd ！= 1");
            return;
        }
        ZnsRunActivityUserEntity activityUserEntity = runActivityUserService.findActivityUser(activityEntity.getId(), detailsEntity.getUserId());
        if (Objects.isNull(activityUserEntity)) {
            log.info("MarathonActivityStrategy end activityUserEntity 为空");
            return;
        }
        if (Objects.isNull(activityUserEntity.getUserActivityEndTime())) {
            log.info("MarathonActivityStrategy end userActivityEndTime 为空");
            return;
        }
        if (activityUserEntity.getUserActivityEndTime().compareTo(ZonedDateTime.now()) < 0) {
            log.info("MarathonActivityStrategy end userActivityEndTime 结束");
            return;
        }

        if (activityUserEntity.getRunAward().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("MarathonActivityStrategy end 金额为0");
            return;
        }

        dealUserCertificate(activityEntity, activityUserEntity, detailsEntity);

        boolean brandUser = userEquipmentService.isBrandUser(activityUserEntity.getUserId(), activityEntity.getPrivilegeBrand());
        //品牌权益处理
        boolean brandRightsInterestsAward = dealBrandRightsInterestsAward(activityEntity, activityUserEntity, brandUser);

        activityUserEntity.setUserState(ActivityUserStateEnum.ENDED.getState());
        runActivityUserService.updateById(activityUserEntity);

        // 给用户余额发送奖励
        userAccountService.increaseAmount(activityUserEntity.getRunAward(), detailsEntity.getUserId(), false);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(detailsEntity.getUserId(), AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_AWARD,
                0, 1, activityUserEntity.getRunAward(), billNo, tradeTime, 0L,
                activityEntity.getId(), 0L, RunActivityTypeEnum.TASK_ACTIVITY.getType(), 0L, activityEntity.getAwardRemark(),
                brandRightsInterestsAward ? activityEntity.getPrivilegeBrand() : null, brandRightsInterestsAward ? activityEntity.getBrandRightsInterestsAward() : null, acceptCountDecimal, BigDecimal.ZERO);

        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.TASK_ACTIVITY_AWARD;

        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.TASK_ACTIVITY_AWARD"), activityEntity.getActivityTitle(), activityUserEntity.getRunAward());
        ImMessageBo bo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
        bo.setJumpType("0");

        OperationalActivity operationalActivity = operationalActivityService.selectByRunActivityId(activityEntity.getId());
        if (Objects.isNull(operationalActivity)) {
            return;
        }
        bo.setJumpValue(operationalActivity.getActivityUrl());

        appMessageService.sendIm("", Arrays.asList(detailsEntity.getUserId()), JsonUtil.writeString(bo), TencentImConstant.TIM_CUSTOM_ELEM, "", activityUserEnd, Boolean.FALSE);
    }

    private void dealUserCertificate(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUserEntity, ZnsUserRunDataDetailsEntity detailsEntity) {
        try {
            RunDetailDataDto runDetailDataDto = new RunDetailDataDto();
            Long treadmillId = detailsEntity.getTreadmillId();
            ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findById(treadmillId);
            // 品牌获取
            if (Objects.isNull(treadmillEntity)) {
                log.info("设备品牌获取失败 user_id:{}", activityUserEntity.getUserId());
                return;
            }
            String brand = treadmillEntity.getBrand();
            Integer type = TreadmillBrandEnum.findByName(brand).getType();
            runDetailDataDto.setType(type);
            runDetailDataDto.setBrand(brand);

            //查询所有任务数据
            List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(activityEntity.getId(), detailsEntity.getUserId(), null);
            List<Long> runDataDetailsIds = runActivityUserTasks.stream().map(RunActivityUserTask::getRunDataDetailsId).collect(Collectors.toList());
            List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetailsEntities = userRunDataDetailsService.findByIds(runDataDetailsIds);

            for (ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity : znsUserRunDataDetailsEntities) {
                runDetailDataDto.setRunTime(runDetailDataDto.getRunTime() + znsUserRunDataDetailsEntity.getRunTime());
                runDetailDataDto.setRunMileage(runDetailDataDto.getRunMileage().add(znsUserRunDataDetailsEntity.getRunMileage()));
            }

            Double averagePace = znsUserRunDataDetailsEntities.stream().mapToInt(ZnsUserRunDataDetailsEntity::getAveragePace).average().orElse(0d);
            BigDecimal averageVelocity = znsUserRunDataDetailsEntities.stream().map(ZnsUserRunDataDetailsEntity::getAverageVelocity).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(znsUserRunDataDetailsEntities.size()), 2, BigDecimal.ROUND_HALF_UP);
            runDetailDataDto.setAveragePace(averagePace.intValue());
            runDetailDataDto.setAverageVelocity(averageVelocity);
            runDetailDataDto.setActivityDate(detailsEntity.getLastTime());

            genUserCertificate(activityEntity, activityUserEntity, runDetailDataDto);
        } catch (Exception e) {
            log.error("dealUserCertificate error,e:{}", e);
        }
    }

    private boolean dealBrandRightsInterestsAward(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUserEntity, boolean brandUser) {
        if (activityEntity.getPrivilegeBrand() == -1) {
            log.info("dealBrandRightsInterestsAward activityEntity.getPrivilegeBrand() = -1");
            return false;
        }
        if (activityEntity.getBrandRightsInterestsAward() == -1) {
            log.info("dealBrandRightsInterestsAward activityEntity.getBrandRightsInterestsAward() = -1");
            return false;
        }
        if (!brandUser) {
            log.info("dealBrandRightsInterestsAward brandUser= false");
            return false;
        }
        if (activityEntity.getBrandRightsInterestsAward() == 1) {
            //奖励翻倍
            BigDecimal award = activityUserEntity.getRunAward().multiply(new BigDecimal(2));
            ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(activityUserEntity.getUserId());
            if (accountEntity != null) {
                award = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), award);
            }
            activityUserEntity.setRunAward(award);
        }
        return true;
    }

    private boolean isBrandRightsInterestsUser(Long userId, HandleActivityRequest request, ZnsRunActivityEntity activity) {
        request.setPrivilegeBrand(activity.getPrivilegeBrand());
        request.setBrandRightsInterests(activity.getBrandRightsInterests());
        boolean brandUser = userEquipmentService.isBrandUser(userId, activity.getPrivilegeBrand());
        if (brandUser && activity.getBrandRightsInterests() == 0) {
            return true;
        }
        return false;
    }
}
