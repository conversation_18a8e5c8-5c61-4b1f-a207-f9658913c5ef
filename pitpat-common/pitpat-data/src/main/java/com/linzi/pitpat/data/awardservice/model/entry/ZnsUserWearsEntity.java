package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 用户穿戴表
 *
 * <AUTHOR>
 * @date 2022-04-22 16:07:45
 */
@TableName("zns_user_wears")
@Data
@NoArgsConstructor
public class ZnsUserWearsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    @JsonIgnore
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    @JsonIgnore
    private Integer isDelete;
    /**
     * 创建时间
     */
    @JsonIgnore
    private ZonedDateTime createTime;
    /**
     * 最后修改时间
     */
    @JsonIgnore
    private ZonedDateTime modifieTime;
    /**
     * 用户id
     */
    @JsonIgnore
    private Long userId;
    /**
     * 发色
     */
    private Integer hairColor;
    /**
     * 肤色
     */
    private Integer skinColour;
    /**
     * 上衣
     */
    private Integer jacket;
    /**
     * 下装
     */
    private Integer trousers;
    /**
     * 鞋子
     */
    private Integer shoes;
    /**
     * 头型
     */
    private Integer head;

    /**
     * 脸部服饰，如：眼镜
     */
    private Integer faceDecoration;
    /**
     * 性别，1：男，2：女，0：未知
     */
    @TableField(exist = false)
    private Integer gender;

    /**
     * 套装
     */
    private Integer suit;
    /**
     * 背部装饰
     */
    private Integer backDecoration;

    /**
     * 年龄类型 0：青年，1：中年，2：老年
     */
    private Integer ageType;

    public ZnsUserWearsEntity(Long userId) {
        this.userId = userId;
        this.hairColor = 0;
        this.skinColour = 0;
        this.jacket = 0;
        this.trousers = 0;
        this.shoes = 0;
        this.faceDecoration = 0;
        this.head = 0;
        this.suit = -1;
        this.backDecoration = -1;
        this.ageType = 0;
    }
}
