package com.linzi.pitpat.data.engine.medal;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.mapper.ZnsUserRunDataDetailsDao;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 连续7天，每天累计跑步3km，平均速度达到10kph
 */
@Slf4j
@Component("takeContinue7MedalHandler")
public class TakeContinue7MedalHandler extends BaseMedalHandler {

    @Autowired
    private ZnsUserRunDataDetailsDao znsUserRunDataDetailsDao;


    @Override
    public Pair<Boolean, UserMedal> deliverMedal(MedalParamDto medalParamDto) {
        boolean flag = false;
        MedalConfig medalConfig = medalParamDto.getMedalConfig();
        UserMedal userMedal = medalParamDto.getUserMedal();
        int count = getInt(medalConfig.getParam1());
        ZonedDateTime currentDate = ZonedDateTime.now();


        int process = 0;
        for (int i = 0; i < count; i++) {
            ZonedDateTime date = DateUtil.addDays(currentDate, -i); //
            BigDecimal sumMileage = znsUserRunDataDetailsDao.selectSumRunMileageByUserId(medalParamDto.getUserId(), DateUtil.getStartOfDate(date), DateUtil.getEndOfDate(date));
            BigDecimal avgV = znsUserRunDataDetailsDao.selectAvgVUserId(medalParamDto.getUserId(), DateUtil.getStartOfDate(date), DateUtil.getEndOfDate(date));
            log.info("wwwwwww" + " 当天向前推第" + i + "天," + medalConfig.getName() + ", handler = " + medalConfig.getHandler() + " ,userId = " + medalParamDto.getUserId()
                    + " 每天累计跑步 =" + sumMileage + ", 平均速度 = " + avgV + "，param1" + medalConfig.getParam1() + ",param2=" + medalConfig.getParam2());
            if (sumMileage.compareTo(MapUtil.getBigDecimal(medalConfig.getParam2(), new BigDecimal(3000))) >= 0
                    && avgV.compareTo(MapUtil.getBigDecimal(medalConfig.getParam3(), new BigDecimal(10))) >= 0) {
                process++;
            } else {                //直到某天就没有数据了
                if (i > 0) {
                    break;
                }
            }
        }
        // 如果今天没有数据，但是昨天有数据，可能今天用户会再来跑步，则不更新用户的进度
        // 连续7天有数据了
        if (process >= count) {
            flag = handlerSucessMedal(medalConfig, userMedal);
        }
        userMedal.setCurrentProcess1(new BigDecimal(process));
        return Pair.of(flag, userMedal);
    }


}
