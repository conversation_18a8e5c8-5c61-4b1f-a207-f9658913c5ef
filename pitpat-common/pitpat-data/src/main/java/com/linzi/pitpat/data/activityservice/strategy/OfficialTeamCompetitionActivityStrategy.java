package com.linzi.pitpat.data.activityservice.strategy;

import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSourceEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.vo.AwardRelation;
import com.linzi.pitpat.data.awardservice.service.AwardProcessService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OfficialTeamCompetitionActivityStrategy extends BaseOfficialActivityStrategy {


    @Autowired
    private ActivityTeamService activityTeamService;

    @Autowired
    private ZnsRunActivityUserService activityUserService;

    @Resource
    private ZnsRunActivityService activityService;

    @Resource
    private AwardProcessService awardProcessService;

    @Autowired
    private UserCouponService userCouponService;

    @Resource
    protected ActivityBrandRightsInterestsService activityBrandRightsInterestsService;

    @Resource
    protected ZnsUserAccountService userAccountService;
    @Resource
    protected ZnsUserAccountDetailService userAccountDetailService;


    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {

    }

    //: -1表示取消，1表示接受，2表示拒绝
    @Override
    @Transactional
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {

        log.info("handleUserActivityState official....................");
        Long teamId = request.getTeamId();
        // 官方赛事
        if (1 == userStatus.intValue()) {
            //加锁
            String key = RedisConstants.ACTIVITY_PARTICIPATION_KEY + user.getId();
            RLock lock = redissonClient.getLock(key);
            try {
                if (LockHolder.tryLock(lock, 3, 30)) {

                    //校验活动国家跟用户国家是否相同
                    if (notContainsCountry(activityEntity, user)) {
                        return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), ActivityError.COUNTRY_ERROR.getMsg());
                    }
                    // 接受官方赛事活动
                    Result result = canRefuseOrAcceptActivity(activityEntity, user);
                    //补充逻辑
                    //团队赛检查队伍是否已满
                    ActivityTeam team = activityTeamService.findById(teamId);
                    if (team.getMaxNum() <= team.getCurrentNum()) {
                        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.num.exceed")); //The team is full"
                    }
                    if (Objects.nonNull(result)) {
                        return result;
                    }

                    // 支付保证金逻辑
                    log.info("开始执行支付流程handlePayRunActivity activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                    Result payResult = runActivityPayManager.handlePayRunActivity(activityEntity, user, password, request, checkVersion);
                    if (null != payResult) {
                        return payResult;
                    }
                    //判断是否包含积分，如果包含积分，则需要积分抵扣
                    useUserScore(activityEntity, user.getId(), false);

                    // 添加官方赛事活动用户
                    log.info("添加官方赛事活动用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                    activityUserBizService.addOfficialActivityUser(activityEntity, user.getId(), runningGoals, taskId, request.getSource(), teamId, false);
                    //修改官方赛事活动参与用户
                    log.info("修改官方赛事活动参与用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                    //增加队伍用户
                    activityTeamService.addUser(user, request.getTeamId(), 0);
                    runActivityService.addOfficialActivityUserCount(activityEntity.getId());
                    if (user.getIsRobot() == 1) {
                        log.info("用户是机器人处理 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                        activityEntity.setHasRobot(1);
                        activityEntity.setUserCount(null);
                        runActivityService.updateById(activityEntity);

                    }
                } else {
                    return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("activity.join.failed"));
                }
            } catch (Exception e) {
                log.error("handleUserActivityState 异常，e:{}", e.getMessage(), e);
                exceptionNotification("Event registration operation failed with exception");
                throw new BaseException(I18nMsgUtils.getMessage("activity.join.failed"), e);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
            return CommonResult.success();
        }

        //如果用户取消报名
        if (-1 == userStatus.intValue()) {
            //加锁
            String key = RedisConstants.ACTIVITY_PARTICIPATION_KEY + user.getId();
            RLock lock = redissonClient.getLock(key);
            try {
                if (LockHolder.tryLock(lock, 3, 30)) {
                    // 判断用户是否可以退出
                    Result result = canCancel(activityEntity, user);
                    if (Objects.nonNull(result)) {
                        return result;
                    }

                    // 用户退队
                    activityTeamService.quitUser(teamId, user);
                    // 更改用户活动关联关系
                    activityUserManager.quitTeam(user, activityEntity, teamId);
                    // 取消活动退款
                    //runActivityService.cancelActivityRefund(activityEntity, AccountDetailTypeEnum.FEE);
                    runActivityProcessManager.cancelActivityFee(activityEntity, user, AccountDetailTypeEnum.FEE);

                    //判断是否包含积分，如果包含积分，则需要积分抵扣
                    if (checkActivityEntryScore(activityEntity)) {
                        // 增加积分兑换记录(相当于扣除积分)
                        useUserScore(activityEntity, user.getId(), true);
                        log.info("用户={}取消报名活动={}消耗了积分={}", user.getId(), activityEntity.getId(), activityEntity.getActivityEntryScore());
                    }
                } else {
                    return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("activity.join.failed"));
                }
            } catch (Exception e) {
                log.error("handleUserActivityState 异常", e);
                exceptionNotification("Event registration operation failed with exception");
                throw new BaseException(I18nMsgUtils.getMessage("activity.join.failed"), e);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
            return CommonResult.success();
        }

        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Processing failed");
    }

    @Override
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        Long activityId = activityEntity.getId();

        //查询奖励配置
        List<AwardRelation> rewards = awardProcessService.getRewards(AwardSourceEnum.ACTIVITY, activityId);
        Map<Integer, AwardRelation> relationMap = rewards.stream().collect(
                Collectors.toMap(AwardRelation::getRank, Function.identity()));
        //查询活动队伍
        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(activityId);

        for (ActivityTeam team : teams) {
            BigDecimal totalMillage = BigDecimal.valueOf(team.getMillage());
            BigDecimal currentNum = BigDecimal.valueOf(team.getCurrentNum());
            //查询队伍成员成绩
            List<ZnsRunActivityUserEntity> activityUsers = activityUserService.getMemberGradeByTeamId(team.getId(), team.getActivityId());
            for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                BigDecimal userMillage = activityUser.getRunMileage();
                BigDecimal awardRatio = BigDecimal.ZERO;
                // 计算奖励比例
                //为0不计算，避免0值异常
                if (!BigDecimal.ZERO.equals(totalMillage)) {
                    awardRatio = userMillage.divide(totalMillage, 8, RoundingMode.HALF_UP);
                }
                //计算奖金
                AwardRelation awardRelation = relationMap.get(team.getRank());
                BigDecimal awardAmount = BigDecimal.ZERO;
                BigDecimal baseReward = awardRelation.getBaseReward();
                BigDecimal headReward = awardRelation.getHeadReward();
                if (Objects.nonNull(baseReward) && baseReward.compareTo(BigDecimal.ZERO) > 0) {
                    awardAmount = awardAmount.add(baseReward);
                }
                if (Objects.nonNull(headReward) && headReward.compareTo(BigDecimal.ZERO) > 0) {
                    awardAmount = BigDecimalUtil.multiply(headReward, currentNum).add(awardAmount);
                }
                // 最终金额奖励
                awardAmount = awardRatio.multiply(awardAmount).setScale(2, RoundingMode.DOWN);
                //修改币种切换处理
                List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
                if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
                    log.info("币种切换不发放");
                    awardAmount = BigDecimal.ZERO;
                }
                // 计算积分
                Integer awardScore = 0;
                Integer baseScoreReward = awardRelation.getBaseScoreReward();
                Integer headScoreReward = awardRelation.getHeadScoreReward();
                if (Objects.nonNull(baseScoreReward) && baseScoreReward > 0) {
                    awardScore = awardScore + baseScoreReward;
                }
                if (Objects.nonNull(headScoreReward) && headScoreReward > 0) {
                    awardScore = headScoreReward * team.getCurrentNum() + awardScore;
                }
                // 最终积分奖励
                awardScore = awardRatio.multiply(new BigDecimal(awardScore)).setScale(0, RoundingMode.UP).intValue();

                List<Long> couponIds = awardRelation.getCouponIds();
                if (!CollectionUtils.isEmpty(couponIds)) {
                    Long couponId = couponIds.get(0);
                    userCouponManager.sendUserCoupon(couponId, activityUser.getUserId(), activityId);
                }
                //权益处理
                ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.BE_CHALLENGED_REWARD.getStatusCode(), activityEntity, activityUser.getUserId());
                Integer rightsInterestsType = null;
                Integer privilegeBrand = null;
                BigDecimal rightsInterestsMultiple = null;

                if (Objects.nonNull(brandRightsInterests)) {
                    rightsInterestsMultiple = brandRightsInterests.getMultiple();
                    rightsInterestsType = brandRightsInterests.getRightsInterestsType();
                    privilegeBrand = brandRightsInterests.getBrand();
                }

                //发放奖励
                // 给用户余额发送奖励
                if (BigDecimal.ZERO.compareTo(awardAmount) != 0 &&
                        !ActivityUserStateEnum.ACCEPT.getState().equals(activityUser.getUserState())) {
                    awardAmount = getUserCurrencyAmount(activityUser.getUserId(), awardAmount);
                    userAccountService.increaseAmount(awardAmount, activityUser.getUserId(), true);
                    // 新增用户奖励余额明细
                    String billNo = NanoId.randomNanoId();
                    ;
                    ZonedDateTime tradeTime = ZonedDateTime.now();
                    userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.OFFICIAL_TEAM_COMPLETION_AWARD,
                            AccountDetailSubtypeEnum.OFFICIAL_TEAM_AWARD.getType(), 1, awardAmount, billNo, tradeTime,
                            activityUser.getActivityId(), activityUser.getActivityId(), null, activityUser.getActivityType(),
                            0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, BigDecimal.ZERO);
                }
                if (awardScore > 0) {
                    // 发放积分
                    activityUserScoreService.increaseAmount(awardScore, activityUser.getActivityId(), activityUser.getUserId(), activityUser.getRank(), 0, ScoreConstant.SourceTypeEnum.source_type_16.getType());
                }

                //下架处理
                if (activityEntity.getStatus() == -1 && activityEntity.getBonusRuleType() != 1) {
                    //退回积分
                    useUserScore(activityEntity, activityUser.getUserId(), true);
                    if (activityEntity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                        // 保证金、费用退回
                        runActivityProcessManager.cancelActivityRefund(activityEntity, Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? AccountDetailTypeEnum.FEE : AccountDetailTypeEnum.SECURITY_FUND, activityUser.getUserId(), "Deposit return");
                    }
                }
            }


        }

        super.handleRunActivityEnd(activityEntity);
    }

    private Result canCancel(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {

        //判断报名时间
        if (Objects.nonNull(activityEntity.getActivityStartTime()) && ZonedDateTime.now().compareTo(activityEntity.getActivityStartTime()) > 0) {
            log.info("判断团队赛是否可以退出：不可以，活动开始不能退出");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.quit.failed"));//"Cannot exit at the beginning of the activity"
        }


        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityEntity.getId())
                .userId(user.getId()).userState(ActivityUserStateEnum.ACCEPT.getState())
                .build();


        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.quit.notJoin", user.getId(), activityEntity.getId())); //"The userId " + user.getId() + " is not Participate Activity" + activityEntity.getId()
        }
        return null;
    }


}
