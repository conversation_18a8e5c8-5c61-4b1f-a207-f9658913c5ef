package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRamRecordsDo;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRamRecordsQuery;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityMonitorAnalyticsItem;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityRamRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 比赛ram统计表 数据访问对象
 *
 * @since 2025年2月13日
 */
@Mapper
public interface ActivityRamRecordsMapper extends BaseMapper<ActivityRamRecordsDo> {

    List<ActivityMonitorAnalyticsItem> groupByErrorDevice(@Param("mainActivityId") Long mainActivityId, @Param("offset") Long offset, @Param("pageSize") Long pageSize);

    @Select("""
             select count(distinct device_model)     as count
                    from zns_activity_ram_records
                    where is_delete = 0
                      and main_activity_id = #{mainActivityId}
                      and normal_exit = 0
            """)
    Long countByErrorDevice(@Param("mainActivityId") Long mainActivityId);

    List<ActivityMonitorAnalyticsItem> groupByErrorCpu(@Param("mainActivityId") Long mainActivityId, @Param("offset") Long offset, @Param("pageSize") Long pageSize);

    @Select("""
             select count(distinct device_cpu)     as count
                    from zns_activity_ram_records
                    where is_delete = 0
                      and main_activity_id = #{mainActivityId}
                      and normal_exit = 0
            """)
    Long countByErrorCpu(@Param("mainActivityId") Long mainActivityId);


    List<ActivityMonitorAnalyticsItem> groupByErrorRam(@Param("mainActivityId") Long mainActivityId, @Param("offset") Long offset, @Param("pageSize") Long pageSize);

    @Select("""
             select count(distinct device_ram)     as count
                    from zns_activity_ram_records
                    where is_delete = 0
                      and main_activity_id = #{mainActivityId}
                      and normal_exit = 0
            """)
    Long countByErrorRam(@Param("mainActivityId") Long mainActivityId);

    List<ActivityMonitorAnalyticsItem> groupByErrorBt(@Param("mainActivityId") Long mainActivityId, @Param("offset") Long offset, @Param("pageSize") Long pageSize);

    @Select("""
             select count(distinct bluetooth_version)     as count
                    from zns_activity_ram_records
                    where is_delete = 0
                      and main_activity_id = #{mainActivityId}
                      and normal_exit = 0
            """)
    Long countByErrorBt(@Param("mainActivityId") Long mainActivityId);

    List<ActivityMonitorAnalyticsItem> groupByNormalExit(@Param("mainActivityId") Long mainActivityId, @Param("offset") Long offset, @Param("pageSize") Long pageSize);

    @Select("""
             select count(distinct normal_exit)     as count
                    from zns_activity_ram_records
                    where is_delete = 0
                      and main_activity_id = #{mainActivityId}
            """)
    Long countByNormal(@Param("mainActivityId") Long mainActivityId);

    List<ActivityRamRecordVo> selectAbnormalExitList(ActivityRamRecordsQuery query);
}
