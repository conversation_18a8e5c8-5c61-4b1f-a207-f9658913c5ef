package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.data.activityservice.model.entity.GameplayAwardStageConfig;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardStageDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardStageDto;
import com.linzi.pitpat.data.activityservice.service.GameplayAwardStageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 奖励相关服务
 */
@Component
@Slf4j
public class AwardStageBizService {
    @Autowired
    private GameplayAwardStageConfigService gameplayAwardStageConfigService;

    /**
     * 奖励阶段存储
     */
    public void saveAwardStageConfig(ActivityAwardStageDto dto) {
        List<AwardStageDto> stageLists = dto.getStageLists();
        //save stage config
        stageLists.forEach(i -> {
            var gameplayAwardStageConfig = new GameplayAwardStageConfig();
            BeanUtils.copyProperties(i, gameplayAwardStageConfig);
            gameplayAwardStageConfig.setGmtCreate(ZonedDateTime.now());
            gameplayAwardStageConfig.setGmtModified(ZonedDateTime.now());
            gameplayAwardStageConfig.setGameplayId(dto.getGameplayId());
            gameplayAwardStageConfigService.insert(gameplayAwardStageConfig);
        });
    }

    /**
     * 奖励阶段更新
     */
    public void updateAwardStageConfig(ActivityAwardStageDto dto) {
        List<GameplayAwardStageConfig> list = gameplayAwardStageConfigService.queryListByGamePlayId(dto.getGameplayId());
        list.forEach(i -> gameplayAwardStageConfigService.deleteById(i.getId()));
        //update stage config
        List<AwardStageDto> stageLists = dto.getStageLists();
        stageLists.forEach(i -> {
            var gameplayAwardStageConfig = new GameplayAwardStageConfig();
            BeanUtils.copyProperties(i, gameplayAwardStageConfig);
            gameplayAwardStageConfig.setGmtCreate(ZonedDateTime.now());
            gameplayAwardStageConfig.setGmtModified(ZonedDateTime.now());
            gameplayAwardStageConfig.setGameplayId(dto.getGameplayId());
            gameplayAwardStageConfigService.insert(gameplayAwardStageConfig);
        });
    }

}
