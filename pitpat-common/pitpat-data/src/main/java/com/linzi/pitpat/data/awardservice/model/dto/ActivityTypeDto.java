package com.linzi.pitpat.data.awardservice.model.dto;

import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/30 20:38
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Slf4j
public class ActivityTypeDto {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 活动类型：1 表示组队跑，2表示挑战跑，3表示官方赛事 4；官方组队跑 5：累计跑 7：一周快乐跑 10 团队赛
     *
     * @see RunActivityTypeEnum
     */
    private Integer activityType;

    // 子类型， 1 随机pk ,  2 好友对战，3 离线pk,4:新人引导pk
    private Integer activityTypeSub;
    /**
     * 赛事类型，single单赛事 series系列赛 old老赛事, rank 段位赛
     *
     * @See com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum
     */
    private String mainType;
    /**
     * 活动标题
     */
    private String activityTitle;

    //目标类型：0：无，1：里程，2：时长 3:场次
    private Integer targetType;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;

    private Integer activityState;

    private Long activityRouteId;

    private ZnsRunActivityEntity runActivity;

    private MainActivity mainActivity;

    private SubActivity subActivity;

    private Integer waitTime = 0;

    private Integer timeStyle = 0;
    /**
     * 脚踏车版本号
     *
     * @since 4.7.1
     */
    private Integer deskBikeEquipmentVersion;


    public ActivityTypeDto(Long id, Integer activityType, Integer activityTypeSub, String mainType) {
        this.id = id;
        this.activityType = activityType;
        this.activityTypeSub = activityTypeSub;
        this.mainType = mainType;
    }

    public ActivityTypeDto(Long id, Integer activityType, Integer activityTypeSub, String mainType, String activityTitle) {
        this.id = id;
        this.activityType = activityType;
        this.activityTypeSub = activityTypeSub;
        this.mainType = mainType;
        this.activityTitle = activityTitle;
    }

    public ActivityTypeDto(Long id, Integer activityType, Integer activityTypeSub, String mainType, String activityTitle, Integer targetType) {
        this.id = id;
        this.activityType = activityType;
        this.activityTypeSub = activityTypeSub;
        this.mainType = mainType;
        this.activityTitle = activityTitle;
        this.targetType = targetType;
    }

    public boolean isBikeActivity() {
        if (mainActivity == null) {
            return false;
        }
        return mainActivity.isBikeActivity();
    }

    /**
     * 检查用户设备是否可以参与当前比赛
     *
     * @since 4.7.1
     */
    public boolean checkUserEquipmentAllowJoinActivity(Integer equipmentVersion, Integer equipmentMainType) {
        if (mainActivity == null) {
            return false;
        }
        if (equipmentVersion == null) {
            log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，用户未绑定符合活动类型大类的设备", mainActivity.getId());
            return false;
        }
        DeviceConstant.EquipmentMainTypeEnum enumByType = DeviceConstant.EquipmentMainTypeEnum.findEnumByType(mainActivity.getEquipmentMainType());
        if (enumByType == null) {
            return false;
        }
        if (!enumByType.isAllowEquipmentTypeJoinActivity(equipmentMainType)) {
            log.info("设备类型不符合:ActivityType:{},UserType:{}", mainActivity.getEquipmentMainType(), equipmentMainType);
            return false;
        }
        boolean result = false;
        if (DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType().equals(equipmentMainType)) {
            //脚踏机逻辑
            Integer activityDeskBikeEquipmentVersion = getDeskBikeEquipmentVersion();
            if (Objects.isNull(activityDeskBikeEquipmentVersion)) {
                return true;
            }
            result = equipmentVersion >= activityDeskBikeEquipmentVersion;
        } else {
            if (Objects.isNull(mainActivity.getEquipmentVersion())) {
                return true;
            }
            result = equipmentVersion >= mainActivity.getEquipmentVersion();
        }
        if (!result) {
            log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，type:{},用户最大版本号 < 活动版本号 ", mainActivity.getId(), equipmentMainType);
        }
        return result;
    }
}
