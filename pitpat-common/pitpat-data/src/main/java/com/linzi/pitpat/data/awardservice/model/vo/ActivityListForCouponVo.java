package com.linzi.pitpat.data.awardservice.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZonedDateTime;

@ToString
@Data
@NoArgsConstructor
public class ActivityListForCouponVo {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动编号
     */
    private String activityNo;

    /**
     * 参与活动用户数
     */
    private Integer userCount;

    /**
     * 跑步里程(m) 范围
     */
    private String runTarget;
}
