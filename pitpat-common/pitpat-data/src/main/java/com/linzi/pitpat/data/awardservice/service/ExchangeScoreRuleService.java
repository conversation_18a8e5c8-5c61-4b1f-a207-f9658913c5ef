package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 兑换券规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleDto;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRuleResp;

import java.time.ZonedDateTime;
import java.util.List;

public interface ExchangeScoreRuleService extends IService<ExchangeScoreRule> {


    ExchangeScoreRule selectExchangeScoreRuleById(Long id);


    int deleteExchangeScoreRuleById(Long id);

    /**
     * 兑换商品
     * @param rule
     * @return
     */
    void updateExchangeScoreByIdAndExchangeReserve(ExchangeScoreRule rule);

    /**
     * 校验商品用户每日兑换量
     * @param userId
     * @param ruleId
     * @return
     */
    Boolean verifyUserScoreRuleDayExchange(Long userId, Long ruleId);


    /**
     * 设置商品用户每日兑换缓存
     * @param userId
     * @param ruleId
     */
    void setUserScoreRuleDayExchangeCache(Long userId, Long ruleId);

    void updateExchangeScoreRuleStatusById(ZonedDateTime date, Integer status, Long id);

    Page<ExchangeScoreRuleResp> findPage(ExchangeScoreRuleDto exchangeScoreRuleDto);

    List<ExchangeScoreRuleResp> selectAppNewPageList(IPage page, Integer inReview, Integer categoryCode, String currencyCode, Integer belongTo, Integer appVersion);

    /**
     * 可更新空值
     * @param rule
     */
    void updateSelective(ExchangeScoreRule rule);
}
