package com.linzi.pitpat.data.activityservice.constant.enums;

/**
 * 运动报告运动类型枚举
 */
public enum ReportMainTypeEnum {
    COMPETITIVE_TOURNAMENT("mainActivity", "竞技赛", "", 1),
    USER_TOURNAMENT("user", "用户赛", "old", 17),
    RANK("rank", "段位赛", "rank", null),
    PROP("prop", "道具赛", "prop", null),
    FREE_RUN("freeRun", "自由运动", "old", -1),
    PACE_PK("pacePk", "配速PK", "old", null),
    TARGET_RUN("targetRun", "目标跑", "old", null),
    COURSE_RUN("courseRun", "课程跑", "old", null),
    NORMAL_RUN("normalRun", "普通跑-无模式/硬件跑/半硬件跑", "old", null),
    OTHER("other", "其他v3.0之前历史数据，官方赛（排行赛、里程碑、主题活动等）和非官方赛（用户赛等）", "old", null),
    FREE_CHALLENGE("freeChallenge", "自由挑战跑", "old", null),
    ;

    private String code;

    private String name;

    private String mainType;

    private Integer activityType;

    ReportMainTypeEnum(String code, String name, String mainType, Integer activityType) {
        this.code = code;
        this.name = name;
        this.mainType = mainType;
        this.activityType = activityType;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getMainType() {
        return mainType;
    }

    public Integer getActivityType() {
        return activityType;
    }
}
