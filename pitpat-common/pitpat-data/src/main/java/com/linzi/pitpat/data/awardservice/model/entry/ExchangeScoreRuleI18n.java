package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */

@Data
@NoArgsConstructor
@TableName("zns_exchange_score_rule_i18n")
public class ExchangeScoreRuleI18n implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //是否删除
    private Integer isDelete;
    //规则id
    private Long ruleId;
    //语言code
    private String langCode;
    //语言名称
    private String langName;
    //兑换活动名称
    private String activityName;
    //兑换规则
    private String exchangeRule;
    //宣传图
    private String advertiseImage;

    /**
     * @since 4.7.0
     *富文本内容
     */
    private String textContent;
}
