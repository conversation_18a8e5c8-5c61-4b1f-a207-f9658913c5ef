package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.entity.app.AbTestConfig;
import com.linzi.pitpat.data.enums.AbTestTypeEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.query.AbTestConfigQuery;
import com.linzi.pitpat.data.service.app.AbTestConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/17 10:48
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ActivityUserBizService {
    private final ZnsRunActivityUserService activityUserService;
    private final ActivityTeamService activityTeamService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final UserPropRecordService userPropRecordService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final AbTestConfigService abTestConfigService;
    private final ISysConfigService sysConfigService;
    private final ZnsUserService userService;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final ActivityParamsService activityParamsService;

    /**
     * 获取用户当前队伍
     *
     * @param activityId
     * @param userId
     * @return
     */
    public ActivityTeam getUserCurrentTeam(Long activityId, Long userId) {
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .activityId(activityId).userId(userId).isDelete(0).userStateIn(Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                        ActivityUserStateEnum.RUNING.getState(),
                        ActivityUserStateEnum.ENDED.getState()))
                .build();


        List<ZnsRunActivityUserEntity> list = activityUserService.findList(userQuery);
        if (!CollectionUtils.isEmpty(list)) {
            ZnsRunActivityUserEntity runActivityUserEntity = list.get(0);
            return activityTeamService.selectActivityTeamById(runActivityUserEntity.getTeamId());
        }
        return null;
    }

    /**
     * 根据队伍id获取队伍成员
     *
     * @param teamId
     * @return
     */
    public List<ZnsRunActivityUserEntity> getActUsersByTeamId(Long teamId) {
        ActivityTeam activityTeam = activityTeamService.selectActivityTeamById(teamId);
        if (activityTeam != null) {
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .teamId(teamId).activityId(activityTeam.getActivityId())
                    .build();
            return activityUserService.findList(userQuery);
        }
        return new ArrayList<>();
    }

    /**
     * 是否报名所有子系列活动
     *
     * @param mainActivityId
     * @param userId
     * @return
     */
    public boolean joinSeriesAllSegment(Long mainActivityId, Long userId) {

        List<SeriesActivityRel> seriesActivityRels = seriesActivityRelService.findSubActivity(mainActivityId);

        List<ZnsRunActivityUserEntity> activityUsers = activityUserService.findActivityUsers(seriesActivityRels.stream().map(SeriesActivityRel::getSegmentActivityId).toList(), userId);
        activityUsers = activityUsers.stream().filter(k -> k.getRunTime() > 0).collect(Collectors.toList());

        return activityUsers.size() == seriesActivityRels.size();
    }


    /**
     * 修改活动用户跑步数据
     *
     * @param activityUser
     * @param userRunDataDetail
     * @param entryGameplay
     * @param allowProp
     */
    public void updateRunDataDetail(ZnsRunActivityUserEntity activityUser, ZnsUserRunDataDetailsEntity userRunDataDetail, EntryGameplay entryGameplay, Integer allowProp) {
        if (Objects.isNull(activityUser)) {
            log.error("修改跑步数据失败，活动用户不存在");
            return;
        }

        if (entryGameplay.getTargetType() == 1 && userRunDataDetail.getRunMileage().compareTo(activityUser.getRunMileage()) < 0) {
            return;
        }
        //进来的都是未完赛的，所以目标未时间时，时长必须大于记录时间才更新
        if (entryGameplay.getTargetType() == 2 && userRunDataDetail.getRunTime() < activityUser.getRunTime()) {
            return;
        }
        activityUser.setId(activityUser.getId());
        activityUser.setRunTime(userRunDataDetail.getRunTime());
        activityUser.setRunMileage(userRunDataDetail.getRunMileage());
        activityUser.setRunDataDetailsId(userRunDataDetail.getId());
        if (activityUser.getRunTime() >= 60) {
            activityUser.setSubState(2);
        }
        if (entryGameplay.getTargetType() == 1 && activityUser.getRunMileage().intValue() >= activityUser.getTargetRunMileage()) {
            activityUser.setIsComplete(1);
            activityUser.setSubState(1);
            activityUser.setCompleteTime(ZonedDateTime.now());
        }
        if (entryGameplay.getTargetType() == 2 && activityUser.getRunTime() >= activityUser.getTargetRunTime()) {
            activityUser.setIsComplete(1);
            activityUser.setSubState(1);
            activityUser.setCompleteTime(ZonedDateTime.now());
        }
        if (entryGameplay.getRepeatedEntryLimit() > 0 && activityUser.getIsComplete() == 1) {
            activityUser.setUserState(4);     // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
        }
        activityUser.setIsCheat(userRunDataDetail.getIsCheat());
        if (Objects.isNull(userRunDataDetail.getRunTimeMillisecond()) || userRunDataDetail.getRunTimeMillisecond() == 0) {
            activityUser.setRunTimeMillisecond(userRunDataDetail.getRunTime() * 1000 + 999);
        } else {
            activityUser.setRunTimeMillisecond(userRunDataDetail.getRunTimeMillisecond());
        }

        if (entryGameplay.getTargetType() == 1 && YesNoStatus.YES.getCode().equals(allowProp)) {
            Integer effectValue = userPropRecordService.countUsePropTimeEffectValue(activityUser.getUserId(), activityUser.getActivityId(), userRunDataDetail.getId(), null);
            if (effectValue > 0) {
                activityUser.setPropRunTime(activityUser.getRunTimeMillisecond() - effectValue * 1000 < 0 ? 0 : activityUser.getRunTimeMillisecond() - effectValue * 1000);
            }
        }
        activityUserService.updateById(activityUser);
    }


    /**
     * 新增官方赛事活动用户
     *
     * @param activity         活动id
     * @param userId           参与用户id
     * @param runningGoals
     * @param taskId
     * @param source
     * @param teamId
     * @param activityPackOpen
     */
    public void addOfficialActivityUser(ZnsRunActivityEntity activity, Long userId, Integer runningGoals, Long taskId, Integer source, Long teamId, Boolean activityPackOpen) {
        if (null == activity || null == userId) {
            log.error("官方赛事活动用户报名失败,参数缺失");
            return;
        }
        ZnsUserEntity userEntity = userService.findById(userId);
        if (null == userEntity) {
            log.error("官方赛事活动用户报名用户不存在,用户id=" + userId);
            return;
        }
        Long activityId = activity.getId();
        // 校验活动用户是否存在
        // 团队赛
        if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(activity.getActivityType())) {
            findTeamActivityUser(activityId, userId);
        } else {
            ZnsRunActivityUserEntity user = activityUserService.findActivityUser(activityId, userId);
            if (null != user) {
                log.info("官方赛事活动用户报名失败,活动用户已存在");
                return;
            }
        }


        ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
        activityUser.setActivityId(activityId);
        activityUser.setUserId(userId);
        ZnsUserEntity znsUser = userService.findById(userId);
        activityUser.setIsRobot(znsUser.getIsRobot());
        activityUser.setIsTest(znsUser.getIsTest());
        activityUser.setSource(source);
        activityUser.setNickname(userEntity.getFirstName());
        activityUser.setUserState(ActivityUserStateEnum.ACCEPT.getState());
        activityUser.setActivityType(activity.getActivityType());
        //H5活动包设置为付费
        if (activityPackOpen) {
            activityUser.setIsPay(YesNoStatus.YES.getCode());
        }
        if (teamId != null) {
            activityUser.setTeamId(teamId);
        }
        // 活动参与者
        activityUser.setUserType(2);
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activity.getActivityType())) {
            AbTestConfigQuery query = AbTestConfigQuery.builder().configKey(AbTestTypeEnum.OFFICIAL_TEAM_RUN_report.getCode()).userId(userId).build();
            AbTestConfig one = abTestConfigService.findOne(query);
            String count = sysConfigService.selectConfigByKey("official_team_run_report_proportion");
            if (Objects.isNull(one) && StringUtils.hasText(count)) {
                String[] split = count.split(",");
                String ACount = split[0];
                Random random = new Random();
                int randomNumber = random.nextInt(100); // 生成0到99之间的随机数
                AbTestConfig abTestConfig = new AbTestConfig();
                abTestConfig.setUserId(userId);
                abTestConfig.setConfigKey(AbTestTypeEnum.OFFICIAL_TEAM_RUN_report.getCode());
                if (randomNumber < Integer.parseInt(ACount)) {
                    abTestConfig.setValue(1);
                } else {
                    abTestConfig.setValue(2);
                }
                abTestConfigService.insert(abTestConfig);
            }
            if (activity.getCompleteRuleType() == 1) {
                activityUser.setTargetRunMileage(runningGoals);
            } else if (activity.getCompleteRuleType() == 2) {
                activityUser.setTargetRunTime(runningGoals);
            } else {
                // 限速跑 判断是根据里程还是时间限速
                String activityConfig = activity.getActivityConfig();
                Integer sourceUnit = null;
                if (activityConfig != null) {
                    Map<String, Object> activityConfigObj = JsonUtil.readValue(activityConfig);
                    String runningGoalsUnit = String.valueOf(activityConfigObj.get("runningGoalsUnit"));
                    if (StringUtils.hasText(runningGoalsUnit)) {
                        // 新建/修改活动原始目标单位 0：公里 ，1：英里，2：时间（min）
                        sourceUnit = Integer.valueOf(runningGoalsUnit);
                        if (sourceUnit == 0 || sourceUnit == 1) {
                            activityUser.setTargetRunMileage(runningGoals);
                        } else {
                            activityUser.setTargetRunTime(runningGoals);
                        }
                    }
                }

            }
        } else if (RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType().equals(activity.getActivityType()) && Objects.equals(userEntity.getMemberType(), 1)) {
            //进阶里程碑VIP免费解锁
            activityUser.setIsVipFree(1);
            activityUser.setIsPay(1);
        } else {
            if (activity.getCompleteRuleType() == 1) {
                activityUser.setTargetRunMileage(activity.getRunMileage().intValue());
            } else if (activity.getCompleteRuleType() == 2) {
                activityUser.setTargetRunTime(activity.getRunTime());
            }
        }
        if (Objects.nonNull(taskId)) {
            activityUser.setTaskId(taskId);
            // 将新人活动的对应的关卡的进行更新
            RunActivityUserTask runActivityUserTask = new RunActivityUserTask();
            runActivityUserTask.setId(taskId);
            runActivityUserTask.setActivityId(activityId);
            runActivityUserTaskService.update(runActivityUserTask);

        }
        activityUserService.save(activityUser);
    }

    /**
     * 查询用户是否已经报名
     *
     * @param activityId
     * @param userId
     */
    private void findTeamActivityUser(Long activityId, Long userId) {

        if (null == activityId || null == userId) {
            throw new BaseException("findTeamActivityUser");
        }
        List<Integer> list = Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                ActivityUserStateEnum.RUNING.getState(), ActivityUserStateEnum.ENDED.getState());

        RunActivityUserQuery query = RunActivityUserQuery.builder()
                .isDelete(0).activityId(activityId).userId(userId).userStateIn(list)
                .build();
        ZnsRunActivityUserEntity userEntity = activityUserService.findOne(query);
        if (userEntity != null) {
            throw new BaseException("this user has signed" + userId);
        }

    }

    public boolean isSurpass(ZnsRunActivityUserEntity surpassRunActivityUser, ZnsRunActivityUserEntity activityUser) {
        if (activityUser.getRank() <= 0) {
            return false;
        }
        if (Objects.isNull(surpassRunActivityUser)) {
            return true;
        }
        if (surpassRunActivityUser.getRank() < 1) {
            return true;
        }
        if (activityUser.getRank() < surpassRunActivityUser.getRank()) {
            return true;
        }
        return false;
    }
    public boolean isSurpass(Integer surpassRank,Integer userRank){
        if (userRank <= 0) {
            return false;
        }
        if(Objects.isNull(surpassRank)){
            return true;
        }
        if(surpassRank < 1){
            return true;
        }
        if(userRank < surpassRank){
            return true;
        }
        return false;
    }

    //用户是否达标俱乐部用户赛
    public Boolean isReach(Long activityId, Long userId) {
        Optional<ActivityParams> oneByMainActivityAndParamType = activityParamsService.findOneByMainActivityAndParamType(activityId, ActivitySettingConfigEnum.TARGET_USER_COUNT);
        if (oneByMainActivityAndParamType.isPresent()) {
            List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(activityId).build());
            if (!CollectionUtils.isEmpty(list)) {
                List<Long> runActivityIds = list.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
                List<ZnsRunActivityUserEntity> activityUsers = activityUserService.findList(RunActivityUserQuery.builder().activityIds(runActivityIds).userId(userId).isComplete(1).build());
                return !CollectionUtils.isEmpty(activityUsers) && activityUsers.size() >= Integer.parseInt(oneByMainActivityAndParamType.get().getParamValue());
            }
        }
        return false;
    }
}
