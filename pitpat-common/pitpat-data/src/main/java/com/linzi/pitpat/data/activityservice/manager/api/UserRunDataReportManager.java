package com.linzi.pitpat.data.activityservice.manager.api;


import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.HeartRateDeviceType;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.OfflineFriendTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.PropRankedLevelEnums;
import com.linzi.pitpat.data.activityservice.constant.enums.RankedLevelEnums;
import com.linzi.pitpat.data.activityservice.constant.enums.ReportMainTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.response.RunSpeedLimitNoticeResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.ShareReportResponseDto;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevelLog;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.RunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.RunTemplateDo;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevelLog;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsHeartRate;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.model.query.PkChallengeRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.SelfPkChallengeRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.resp.SeriesRunningReportResp;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelLogService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.RunTemplateService;
import com.linzi.pitpat.data.activityservice.service.SelfPkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelLogService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsMileageService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.FetchRuleTypeEnum;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.entity.dto.CalorieConsumptionFoodCalibrationDto;
import com.linzi.pitpat.data.enums.CalorieConsumptionFoodCalibration;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.RunDataRunTypeEnum;
import com.linzi.pitpat.data.enums.RunningEnduranceLevel;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentVO;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.request.RunDataStatisticsRequest;
import com.linzi.pitpat.data.resp.ShareConfigVo;
import com.linzi.pitpat.data.resp.SpeedLineDto;
import com.linzi.pitpat.data.resp.StageCapacityDto;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.UserPerformanceLevels;
import com.linzi.pitpat.data.systemservice.model.vo.DeviceSpeedLimitPushVo;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.UserPerformanceLevelsService;
import com.linzi.pitpat.data.trainingplanservice.biz.TrainingPlanBizService;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppTrainingPlanDetailResponseDto;
import com.linzi.pitpat.data.trainingplanservice.model.entity.UserTrainingPlan;
import com.linzi.pitpat.data.trainingplanservice.model.entity.UserTrainingPlanDetail;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanDetailService;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanService;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.vo.RunningRecordListDayVO;
import com.linzi.pitpat.data.vo.runData.AerobicCapacityVo;
import com.linzi.pitpat.data.vo.runData.ChallengeReportDto;
import com.linzi.pitpat.data.vo.runData.CourseReportVo;
import com.linzi.pitpat.data.vo.runData.HeartRateChartData;
import com.linzi.pitpat.data.vo.runData.HeartRateChartDataDetail;
import com.linzi.pitpat.data.vo.runData.LaActivityReportDto;
import com.linzi.pitpat.data.vo.runData.PaceRateChartData;
import com.linzi.pitpat.data.vo.runData.PerformanceLevelVo;
import com.linzi.pitpat.data.vo.runData.RunDataContrastVo;
import com.linzi.pitpat.data.vo.runData.RunRadarChartDetailVo;
import com.linzi.pitpat.data.vo.runData.RunRadarChartVo;
import com.linzi.pitpat.data.vo.runData.RunningLevelVo;
import com.linzi.pitpat.data.vo.runData.RunningRecordListVo;
import com.linzi.pitpat.data.vo.runData.RunningReportVo;
import com.linzi.pitpat.data.vo.runData.ShareDataVo;
import com.linzi.pitpat.data.vo.runData.StepFrequencyRateChartData;
import com.linzi.pitpat.data.vo.runData.StepFrequencyRateChartDataDetail;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 用户数据展示处理类
 *
 * <AUTHOR>
 * @date 2024/6/12 17:50
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserRunDataReportManager {
    private static final Set<String> restrictedCodes = Set.of("R1", "S1");

    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ISysConfigService sysConfigService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final ZnsUserRunDataService userRunDataService;
    private final UserPropRecordService userPropRecordService;
    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    private final ZnsCourseService courseService;
    private final ZnsUserRunDataDetailsMileageService userRunDataDetailsMileageService;
    private final ZnsUserRunRecordService userRunRecordService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final ZnsUserService userService;
    private final UserPerformanceLevelsService userPerformanceLevelsService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ZnsRunActivityUserService activityUserService;
    private final MongoTemplate mongoTemplate;
    private final ZnsUserEquipmentService userEquipmentService;
    private final AppMessageService appMessageService;
    private final RunRankedActivityUserService runRankedActivityUserService;
    private final UserRankedLevelLogService userRankedLevelService;
    private final PropRunRankedActivityUserService propRunRankedActivityUserService;
    private final PropUserRankedLevelLogService propUserRankedLevelLogService;
    private final VipUserService vipUserService;
    private final EntryGameplayService entryGameplayService;
    private final MainActivityBizService mainActivityBizService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final PkChallengeRecordService pkChallengeRecordService;
    private final MainActivityService mainActivityService;
    private final UserTrainingPlanDetailService userTrainingPlanDetailService;
    private final UserTrainingPlanService userTrainingPlanService;
    private final TrainingPlanBizService trainingPlanBizService;
    private final ZnsTreadmillService treadmillService;
    private final ZnsEquipmentProductionBatchService equipmentProductionBatchService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final SelfPkChallengeRecordService selfPkChallengeRecordService;
    private final ActivityParamsService activityParamsService;
    private final RunTemplateService runTemplateService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取跑步报告
     *
     * @param entity
     * @param loginUser
     * @param activityId
     * @param appVersion
     * @param activityNew
     * @return
     */
    public RunningReportVo getRunningReport(ZnsUserRunDataDetailsEntity entity, ZnsUserEntity loginUser, Long activityId, Integer appVersion, ActivityTypeDto activityNew) {
        RunningReportVo vo = new RunningReportVo();
        //设置虚拟设备
        vo.setVirtual(sysConfigService.isVirtualRecord(entity.getId()) ? 1 : 0);

        vo.setId(entity.getId());
        vo.setNickname(loginUser.getFirstName()).setHeadPortrait(loginUser.getHeadPortrait());
        //设置基础信息
        setBaseInfo(vo, entity, loginUser.getReportFoodType(), activityNew, loginUser.getLanguageCode());

        RunDataContrastVo runDataContrastVo = runDataContrast(entity, loginUser.getZoneId());
        vo.setRunDataContrast(runDataContrastVo);

        List<ZnsUserRunDataDetailsSecondEntity> seconds = null;


        if (RunDataRunTypeEnum.getAllRunTypeCode().contains(entity.getRunType())) {
            log.info("getRunningReport ---------------------1");
            List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(entity.getId());
            seconds = detailsSecondEntities;
            log.info("getRunningReport ---------------------2");

            List<ZnsUserRunDataDetailsSecondEntity> dataPoint = getPointData(entity.getId(), detailsSecondEntities);
            log.info("getRunningReport ---------------------3");

            //设置图表数据
            setChartData(vo, dataPoint, entity, detailsSecondEntities, loginUser);
        }
        // 速度曲线
        List<SpeedLineDto> speedLineDtos = setSpeedLineData(seconds);
        vo.setSpeedLines(speedLineDtos);
        if (!CollectionUtils.isEmpty(seconds)) {
            vo.setTopSpeed(seconds.stream().max(Comparator.comparing(ZnsUserRunDataDetailsSecondEntity::getVelocity)).map(ZnsUserRunDataDetailsSecondEntity::getVelocity).orElse(BigDecimal.ZERO));
        }

        //参与活动类型
        if (Objects.nonNull(activityNew)) {
            vo.setActivityType(userRunDataDetailsService.getActivityType(entity, activityNew));
        }
        //判断是否完成
        vo.setTargetStatus(userRunDataDetailsService.getTargetStatus(entity));

        //获取挑战记录-- 判断是否挑战他人
        List<ZnsUserRunRecordEntity> runRecords = userRunRecordService.getRunRecordByDetailIds(loginUser.getId(), Lists.newArrayList(entity.getId()));
        vo.setIsChallengeRun(false);
        vo.setUserCompetitionPopup(YesNoStatus.NO.getCode());
        if (!CollectionUtils.isEmpty(runRecords)) {
            Long challengedUserId = runRecords.get(0).getChallengedUserId();
            if (Optional.ofNullable(challengedUserId).orElse(0L) > 0) {
                vo.setIsChallengeRun(true);
            }
        }
        //真实跑步时间（毫秒）
        vo.setRunTimeMillisecond(entity.getRunTimeMillisecond());
        //道具扣减时间
        if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(entity.getActivityType())) {
            //马拉松（主题赛）-每个关卡都有道具-获取指定关卡扣减时间
            RunActivityUserTask runActivityUserTask = runActivityUserTaskService.selectByDetailId(entity.getId());
            if (runActivityUserTask != null) {
                vo.setPropEffectTime(runActivityUserTask.getPropEffectTime());
            }
        } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(entity.getActivityType())) {
            //官方多人同跑
            Integer propTimeEffectValue = userPropRecordService.countUsePropTimeEffectValue(entity.getUserId(), entity.getActivityId(), entity.getId(), null);
            vo.setPropEffectTime(propTimeEffectValue);
        } else if (RunActivityTypeEnum.NEW_USER_PK_MANY.getType().equals(entity.getActivityType())) {
            //新用户多人PK
            ZnsRunActivityEntity runActivity = runActivityService.findById(entity.getActivityId());
            String activityConfig = runActivity.getActivityConfig();
            var newPkMultipleConfigVo = JsonUtil.readValue(activityConfig, NewPkMultipleConfigVo.class);
            if (Objects.nonNull(newPkMultipleConfigVo.getUserRankAward())) {
                vo.setUserRankAward(newPkMultipleConfigVo.getUserRankAward());
            }
            if (Objects.nonNull(newPkMultipleConfigVo.getFinishAward())) {
                vo.setFinishAward(newPkMultipleConfigVo.getFinishAward());
            }
            if (Objects.nonNull(newPkMultipleConfigVo.getUnFinishAward())) {
                vo.setUnFinishAward(newPkMultipleConfigVo.getUnFinishAward());
            }
            var znsUserActivity = runActivityUserService.findActivityUser(entity.getActivityId(), loginUser.getId());
            vo.setRank(znsUserActivity.getRank());
            vo.setIsComplete(znsUserActivity.getIsComplete());
        } else if (RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType().equals(entity.getActivityType()) ||
                RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType().equals(entity.getActivityType())) {
            Integer propTimeEffectValue = userPropRecordService.countUsePropTimeEffectValue(entity.getUserId(), entity.getActivityId(), entity.getId(), null);
            vo.setPropEffectTime(propTimeEffectValue);
        } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(entity.getActivityType())
                && activityNew.getActivityTypeSub().equals(RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType())) {
            // 好友pk 判定
            PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(activityId).userId(loginUser.getId()).build();
            PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.findByQuery(query);
            List<Long> activityUserIds = runActivityUserService.findAllActivityUserIds(entity.getActivityId());
            Long otherUserId = activityUserIds.stream().filter(b -> !b.equals(loginUser.getId())).findFirst().orElse(null);
            var znsUserActivity = runActivityUserService.findActivityUser(entity.getActivityId(), loginUser.getId());
            vo.setRank(znsUserActivity.getRank());
            if (pkChallengeRecord.getOfflineFriendType().equals(OfflineFriendTypeEnum.FRIEND.getCode())) {
                if (vo.getRank() == 1) {
                    // 挑战成功提醒 无弹窗缓存标志
                    String key = RedisConstants.USER_COMPETITION_POP + loginUser.getId() + "_" + activityId;
                    if (!redissonClient.getBucket(key).isExists()) {
                        vo.setUserCompetitionPopup(YesNoStatus.YES.getCode());
                        vo.setUserCompetitionUserId(otherUserId);
                        redissonClient.getBucket(key).set(YesNoStatus.YES.getCode());
                    }
                }
            }
            //挑战跑增加数据对比
            getChallengeReportDto(vo, otherUserId, YesNoStatus.NO.getCode(), loginUser);
        } else if (Objects.isNull(entity.getActivityType())) {
            //判定是否挑战了自己
            var selfPkChallengeRecordQuery = new SelfPkChallengeRecordQuery().setUserId(loginUser.getId()).setRunDataDetailsId(entity.getId());
            var selfPkChallengeRecord = selfPkChallengeRecordService.findByQuery(selfPkChallengeRecordQuery);
            // 挑战自己的对比数据
            if (Objects.nonNull(selfPkChallengeRecord)) {
                RealPersonRunDataDetails otherUserIdRunDataDetailsEntity = realPersonRunDataDetailsService.findByDetailsId(selfPkChallengeRecord.getTargetRunDataDetailsId());
                ChallengeReportDto challengeReportDto = getChallengeReportDto(vo, otherUserIdRunDataDetailsEntity, YesNoStatus.YES.getCode(), loginUser);
                vo.setChallengePkResult(challengeReportDto);
            }
        } else if (RunActivityTypeEnum.FREE_CHALLENGE.getType().equals(entity.getActivityType())) {
            // 自由挑战上榜
            // 挑战自己的对比数据
            List<RealPersonRunDataDetails> otherUserIdRunDataDetailsList = realPersonRunDataDetailsService.findListByActivity(loginUser.getId(), activityId);
            FreeActivityConfig config = activityParamsService.findCacheOne(entity.getActivityId(), ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
            List<RealPersonRunDataDetails> personRunDataDetailsList = otherUserIdRunDataDetailsList.stream()
                    .filter(otherUserIdRunDataDetailsEntity -> otherUserIdRunDataDetailsEntity.getRunStatus() == 1)
                    .filter(otherUserIdRunDataDetailsEntity -> !otherUserIdRunDataDetailsEntity.getRunDataDetailsId().equals(entity.getId()))
                    .filter(otherUserIdRunDataDetailsEntity -> otherUserIdRunDataDetailsEntity.getRunDataDetailsId() < entity.getId())
                    .toList();
            if (!config.getMode().equals(FreeActivityModeEnum.PROP.getCode())) {
                personRunDataDetailsList = personRunDataDetailsList.stream().filter(otherUserIdRunDataDetailsEntity -> otherUserIdRunDataDetailsEntity.getRunMileage().compareTo(entity.getDistanceTarget()) >= 0).toList();
            }
            RealPersonRunDataDetails otherUserIdRunDataDetailsEntity = null;
            if(!CollectionUtils.isEmpty(personRunDataDetailsList)){
                otherUserIdRunDataDetailsEntity = personRunDataDetailsList.stream().sorted(Comparator.comparingLong(RealPersonRunDataDetails::getId).reversed()).toList().get(0);
            }
            var znsUserActivity = runActivityUserService.findActivityUser(entity.getActivityId(), loginUser.getId());
            vo.setRank(znsUserActivity.getRank());
            LaActivityReportDto laActivityReportDto = getLaActivityReportDto(vo, otherUserIdRunDataDetailsEntity,entity);
            vo.setLaResult(laActivityReportDto);
        }

        if (MainActivityTypeEnum.PLACEMENT.getType().equals(vo.getMainType())) {
            var znsUserActivity = runActivityUserService.findActivityUser(entity.getActivityId(), loginUser.getId());
            vo.setIsComplete(znsUserActivity.getIsComplete());
        }


        //排名时间减道具时间
        if (Optional.ofNullable(vo.getPropEffectTime()).orElse(0) > 0) {
            int runTime = vo.getRunTime() - vo.getPropEffectTime();
            runTime = Math.max(runTime, 0);
            vo.setRunTime(runTime);
        }

        //5、成长&训练分析
        if (entity.getRunType() == 2) {
            //获取报告图
            String sysConfig = sysConfigService.selectConfigByKey(ConfigKeyEnums.WALK_REPORT_BACKGROUND.getCode());
            vo.setBackgroundImage(sysConfig);
        }

        if (loginUser.getMemberType() == 1 || appVersion >= 4048) {
            log.info("getRunningReport vip功能模块处理开始");
            //雷达图
            setRunRadarChart(vo, entity, loginUser);
            //近7次跑步数据
            UserRunDataDetailsQuery userRunDataDetailsQuery = new UserRunDataDetailsQuery().setUserId(entity.getUserId()).setDeviceType(entity.getDeviceType()).setMinRunMileage(new BigDecimal(500));
            userRunDataDetailsQuery.setOrders(List.of(OrderItem.desc("id")));
            userRunDataDetailsQuery.setPageSize(7);
            List<ZnsUserRunDataDetailsEntity> lastRunDetails = userRunDataDetailsService.findListByPageQuery(userRunDataDetailsQuery);
            if (!CollectionUtils.isEmpty(lastRunDetails)) {
                lastRunDetails.sort(Comparator.comparing(ZnsUserRunDataDetailsEntity::getId));
                vo.setLastRunPaceList(lastRunDetails.stream().map(ZnsUserRunDataDetailsEntity::getAveragePace).collect(Collectors.toList()));
            }

            // 3.8.0 单赛事 个人类型非团队赛
            if (MainActivityTypeEnum.SINGLE.getType().equals(activityNew.getMainType())
                    || MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(activityNew.getMainType())
                    || MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(activityNew.getMainType())) {
                EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(activityNew.getMainActivity().getPlayId());
                vo.setCompetitionFormat(entryGameplay.getCompetitionFormat());
                // TODO: 2024/9/24  版本兼容,4.2 版本上线后可删除
                if (appVersion < 4020 && vo.getTargetStatus() == 1 && entryGameplay.getTargetType() != 0) {
                    //有目标展示能力值
                    setUserLevelData(vo);
                }
            }

            if (entity.getRunMileage().compareTo(new BigDecimal(500)) >= 0) {
                if (appVersion >= 4020) {
                    //有目标展示能力值
                    setUserLevelData(vo);
                }
            }
        }

        //上次数据
        ZnsUserRunDataDetailsEntity lastDetails = userRunDataDetailsService.findLastDetail(loginUser.getId(), entity.getId(), entity.getDeviceType());

        //设置竞技水平
        RunningLevelVo runningLevelVo = getRunningLevelVo(lastDetails, entity, appVersion);
        vo.setRunningLevelVo(runningLevelVo);
        // 设备base 坡度数据
        ZnsTreadmillEntity treadmillEntity = treadmillService.findById(entity.getTreadmillId());
        if (Objects.nonNull(treadmillEntity)) {
            vo.setEquipmentInfo(treadmillEntity.getProductCode());
            //查询设备信息
            UserEquipmentVO equipmentVo = equipmentProductionBatchService.selectEquipmentVo(treadmillEntity.getBatchNumber());
            if (Objects.nonNull(equipmentVo)) {
                vo.setMaximumLiftingGradient(equipmentVo.getMaximumLiftingGradient());
            }
            vo.setShowCalorie(restrictedCodes.contains(treadmillEntity.getProductCode())?0:1);
        }

        //查询是否有风控检测中
        vo.setIsRiskReview(userRunDataDetailsCheatService.isDetailsRiskReview(entity.getId()));
        return vo;
    }

    /**
     * la 活动 报告数据
     *
     * @param vo
     * @param lastTimeRunDataDetailsEntity 上次比赛记录
     * @param entity
     * @return
     */
    private LaActivityReportDto getLaActivityReportDto(RunningReportVo vo, RealPersonRunDataDetails lastTimeRunDataDetailsEntity, ZnsUserRunDataDetailsEntity entity) {
        var laActivityReportDto = new LaActivityReportDto();
        laActivityReportDto.setThisTimeAveragePace(vo.getAveragePace());
        laActivityReportDto.setThisRunTime(vo.getRunTime());
        laActivityReportDto.setIsFinishTarget(vo.getIsComplete());
        //判定上榜
        FreeActivityConfig config = activityParamsService.findCacheOne(vo.getActivityId(), ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
        laActivityReportDto.setIsMakeTheList(makeTheList(entity,config));
        if(Objects.nonNull(lastTimeRunDataDetailsEntity)){
            laActivityReportDto.setLastRunTime(lastTimeRunDataDetailsEntity.getRunTime());
            laActivityReportDto.setLastTimeAveragePace(lastTimeRunDataDetailsEntity.getAveragePace());
            laActivityReportDto.setIsMakeTheListUpdate(YesNoStatus.NO.getCode());
            if (vo.getRunTimeMillisecond() < lastTimeRunDataDetailsEntity.getRunTimeMillisecond()) {
                laActivityReportDto.setIsMakeTheListUpdate(YesNoStatus.YES.getCode());
            }
        }else {
            laActivityReportDto.setIsMakeTheListUpdate(YesNoStatus.NO.getCode());
            laActivityReportDto.setIsFirstMakeTheList(YesNoStatus.NO.getCode());
            if(laActivityReportDto.getIsMakeTheList().equals(YesNoStatus.YES.getCode())){
                laActivityReportDto.setIsFirstMakeTheList(YesNoStatus.YES.getCode());
            }
        }
        return laActivityReportDto;
    }

    /**
     * 是否上榜
     *
     * @param userRunDataDetail
     * @param config
     * @return
     */
    private Integer makeTheList(ZnsUserRunDataDetailsEntity userRunDataDetail, FreeActivityConfig config) {
        if (Objects.nonNull(config)) {
            if (config.getMode().equals(FreeActivityModeEnum.PROP.getCode())) {
                // 道具模式不进行目标比较
                return userRunDataDetail.getRunMileageRanking() >=0?YesNoStatus.YES.getCode():YesNoStatus.NO.getCode();
            }
            Long rubTemplateId = config.getRubTemplateId();
            RunTemplateDo runTemplateDo = runTemplateService.findById(rubTemplateId);
            // 超越目标数据
            if(Objects.nonNull(runTemplateDo)){
                ZnsUserRunDataDetailsEntity target = userRunDataDetailsService.findById(runTemplateDo.getDetailId());
                return (userRunDataDetail.getRunMileage().compareTo(userRunDataDetail.getDistanceTarget()) >= 0 &&
                        userRunDataDetail.getRunTime() < target.getRunTime()) ?YesNoStatus.YES.getCode():YesNoStatus.NO.getCode();
            }
        }
        return YesNoStatus.NO.getCode();
    }

    private void getChallengeReportDto(RunningReportVo vo, Long otherUserId, Integer isPkSelf, ZnsUserEntity loginUser) {
        PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(vo.getActivityId()).userId(otherUserId).build();
        PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.findByQuery(query);
        Long runDataDetailsId = pkChallengeRecord.getRunDataDetailsId();
        RealPersonRunDataDetails otherUserIdRunDataDetailsEntity = realPersonRunDataDetailsService.findByDetailsId(runDataDetailsId);
        if (Objects.isNull(otherUserIdRunDataDetailsEntity)) {
            ZnsUserRunDataDetailsEntity userRunDataDetailsEntity = userRunDataDetailsService.findById(runDataDetailsId);
            if (Objects.nonNull(userRunDataDetailsEntity)) {
                otherUserIdRunDataDetailsEntity = new RealPersonRunDataDetails();
                otherUserIdRunDataDetailsEntity.setAveragePace(userRunDataDetailsEntity.getAveragePace());
                otherUserIdRunDataDetailsEntity.setRunTime(userRunDataDetailsEntity.getRunTime());
                otherUserIdRunDataDetailsEntity.setDistanceTarget(userRunDataDetailsEntity.getDistanceTarget());
            }
        }
        ChallengeReportDto challengeReportDto = getChallengeReportDto(vo, otherUserIdRunDataDetailsEntity, isPkSelf, loginUser);
        vo.setChallengePkResult(challengeReportDto);
    }


    private ChallengeReportDto getChallengeReportDto(RunningReportVo vo, RealPersonRunDataDetails otherUserIdRunDataDetailsEntity, Integer isPkSelf, ZnsUserEntity loginUser) {
        ChallengeReportDto challengeReportDto = new ChallengeReportDto();
        challengeReportDto.setIsPkMySelf(isPkSelf);
        challengeReportDto.setThisTimeAveragePace(vo.getAveragePace());
        challengeReportDto.setLastTimeAveragePace(otherUserIdRunDataDetailsEntity.getAveragePace());
        challengeReportDto.setThisRunTime(vo.getRunTime());
        challengeReportDto.setLastRunTime(otherUserIdRunDataDetailsEntity.getRunTime());
        challengeReportDto.setIsFinishTarget(vo.getRunMileage() >= otherUserIdRunDataDetailsEntity.getDistanceTarget().intValue() ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
        if (isPkSelf.equals(YesNoStatus.YES.getCode())) {
            var selfPkChallengeRecordQuery = new SelfPkChallengeRecordQuery().setUserId(loginUser.getId()).setRunDataDetailsId(vo.getId());
            var selfPkChallengeRecord = selfPkChallengeRecordService.findByQuery(selfPkChallengeRecordQuery);
            challengeReportDto.setIsChallengeSuccess(selfPkChallengeRecord.getStatus() == 1 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
        } else {
            challengeReportDto.setIsChallengeSuccess(Objects.nonNull(vo.getRank()) && vo.getRank() == 1 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            String key = RedisConstants.REPORT_CHALLENGE_SUCCESS_POP + "_" + vo.getId();
            boolean exists = redissonClient.getBucket(key).isExists();
            if (!exists) {
                challengeReportDto.setIsPopChallengeSuccess(challengeReportDto.getIsChallengeSuccess());
                redissonClient.getBucket(key).set(1, 30, TimeUnit.DAYS);
            }
        }
        return challengeReportDto;
    }

    /**
     * @param entity
     * @param zoneId
     * @return
     */
    private RunDataContrastVo runDataContrast(ZnsUserRunDataDetailsEntity entity, String zoneId) {
        RunDataContrastVo vo = new RunDataContrastVo();
        Long runMileage = realPersonRunDataDetailsService.sumAllRunMileage(entity.getUserId(), entity.getCreateTime(), entity.getDeviceType());
        if (Objects.nonNull(runMileage)) {
            vo.setTotalRunMileage(runMileage.intValue());
        } else {
            vo.setTotalRunMileage(entity.getRunMileage().intValue());
        }
        Long runDays = realPersonRunDataDetailsService.continuousRunDays(entity.getUserId(), entity.getCreateTime(), zoneId, entity.getDeviceType());
        vo.setRunDays(runDays.intValue());
        ZnsUserRunDataDetailsEntity lastDetail = userRunDataDetailsService.findLastDetail(entity.getUserId(), entity.getId(), entity.getDeviceType());
        if (Objects.nonNull(lastDetail)) {
            if (lastDetail.getAveragePace() > entity.getAveragePace()) {
                vo.setImprovePace(lastDetail.getAveragePace() - entity.getAveragePace());
            }
            if (lastDetail.getRunMileage().compareTo(entity.getRunMileage()) < 0) {
                vo.setImproveRunMileage(entity.getRunMileage().subtract(lastDetail.getRunMileage()).intValue());
            }
            if (lastDetail.getRunTime() < entity.getRunTime()) {
                vo.setImproveRunTime(entity.getRunTime() - lastDetail.getRunTime());
            }
        }
        return vo;
    }

    /**
     * 获取每月运动简单记录
     *
     * @param userId
     * @param request
     * @param zoneId
     * @param languageCode
     * @return
     */
    public List<RunningRecordListVo> getRunningRecordList(Long userId, RunDataStatisticsRequest request, String zoneId, String languageCode) {
        //设置查询时间和横坐标
        userRunDataDetailsService.setSelectCondition(request, zoneId);
        //获取本月运动记录
        List<ZnsUserRunDataDetailsEntity> list = userRunDataDetailsService.findListByCompletedAndCreateTime(userId, request.getStartTime(), request.getEndTime());

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        //查询跑步对应的活动
        List<Map<String, Object>> runActivityType = runActivityUserService.getRunActivityType(list.stream().filter(d -> d.getActivityId() > 0).map(ZnsUserRunDataDetailsEntity::getActivityId).collect(Collectors.toList()));
        Map<Object, Object> runActivityTypeMap = runActivityType.stream().collect(Collectors.toMap(d -> d.get("id"), d -> d.get("activity_type"), (x, y) -> x));
        List<ZnsUserRunRecordEntity> runRecords = userRunRecordService.getRunRecordByDetailIds(userId, list.stream().filter(d -> d.getActivityId() > 0).map(ZnsUserRunDataDetailsEntity::getId).collect(Collectors.toList()));
        Map<Long, ZnsUserRunRecordEntity> runRecordsMap = runRecords.stream().collect(Collectors.toMap(ZnsUserRunRecordEntity::getRunDataDetailsId, Function.identity(), (x, y) -> x));
        //课程
        Map<Long, ZnsCourseEntity> courseMap = new HashMap<>();
        List<Long> courseIds = list.stream().filter(r -> Objects.nonNull(r.getCourseId()) && r.getCourseId() > 0).map(ZnsUserRunDataDetailsEntity::getCourseId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(courseIds)) {
            for (Long courseId : courseIds) {
                ZnsCourseEntity znsCourseEntity = courseService.getI18nCourseById(courseId, languageCode);
                courseMap.put(courseId, znsCourseEntity);
            }
        }

        /****** 按日分组 ******/
        Map<String, List<ZnsUserRunDataDetailsEntity>> collectMap = list.stream().collect(Collectors.groupingBy(d -> DateUtil.formateDateStr(
                DateUtil.getDate2ByTimeZone(d.getCreateTime(), TimeZone.getTimeZone(zoneId)), "yyyy.MM.dd")));
        Map<Long, ZnsCourseEntity> finalCourseMap = courseMap;
        return collectMap.entrySet().stream().map(m -> {
            List<ZnsUserRunDataDetailsEntity> dataDetailsEntityList = m.getValue();
            RunningRecordListVo map = new RunningRecordListVo();
            final Integer[] sumRunTime = {0};
            final Integer[] sumRunMileage = {0};
            final Integer[] sumKilocalorie = {0};
            List<RunningRecordListDayVO> voList = dataDetailsEntityList.stream().map(d -> {
                RunningRecordListDayVO vo = new RunningRecordListDayVO();
                map.setCreateTime(d.getCreateTime());
                vo.setDetailId(d.getId());
                vo.setCreateTime(d.getCreateTime());
                vo.setAveragePace(d.getAveragePace());
                vo.setKilocalorie(d.getKilocalorie().intValue());
                vo.setRunMileage(d.getRunMileage().intValue());
                vo.setRunTime(d.getRunTime());
                sumRunTime[0] = sumRunTime[0] + vo.getRunTime();
                sumRunMileage[0] = sumRunMileage[0] + vo.getRunMileage();
                sumKilocalorie[0] = sumKilocalorie[0] + vo.getKilocalorie();
                Object activity_type = runActivityTypeMap.get(d.getActivityId());
                if (Objects.nonNull(activity_type)) {
                    Long activityType = (Long) activity_type;
                    //排行赛判断是否刷榜，刷榜不返回类型（刷榜：没有挑战任何人，包括自己）
                    if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().intValue() == activityType.intValue()) {
                        ZnsUserRunRecordEntity znsUserRunRecordEntity = runRecordsMap.get(d.getId());
                        if (Objects.nonNull(znsUserRunRecordEntity)) {
                            vo.setChallengedUserId(znsUserRunRecordEntity.getChallengedUserId());
                            vo.setActivityType(activityType.intValue());
                        } else {
                            vo.setActivityType(-1);
                        }
                    } else {
                        vo.setActivityType(activityType.intValue());
                    }
                } else {
                    vo.setActivityType(-1);
                }
                //课程数据
                ZnsCourseEntity course = finalCourseMap.get(d.getCourseId());
                if (Objects.nonNull(course)) {
                    vo.setCourseName(course.getCourseName());
                    vo.setDifficulty(course.getDifficulty());
                    vo.setActivityType(-2);
                    vo.setCourseType(course.getCourseType());
                }
                vo.setRunType(d.getRunType());
                return vo;
            }).sorted(Comparator.comparing(RunningRecordListDayVO::getCreateTime).reversed()).collect(Collectors.toList());
            map.setRunTime(sumRunTime[0]);
            map.setRunMileage(sumRunMileage[0]);
            map.setKilocalorie(sumKilocalorie[0]);
            map.setRunData(voList);
            return map;
        }).sorted((x, y) -> {
            ZonedDateTime xDate = x.getCreateTime();
            ZonedDateTime yDate = y.getCreateTime();
            return yDate.compareTo(xDate);
        }).collect(Collectors.toList());
    }

    /**
     * 设置跑步雷达图数据
     *
     * @param data
     * @param entity
     * @param user
     */
    public void setRunRadarChart(RunningReportVo data, ZnsUserRunDataDetailsEntity entity, ZnsUserEntity user) {
        RunRadarChartVo runRadarChart = new RunRadarChartVo();

        //本次数据
        runRadarChart.setThisTime(getRunRadarChartData(entity.getRunTime(), entity.getRunMileage(), entity.getMaxStepFrequency(), entity.getMaxPace(), entity.getKilocalorie().intValue(), entity.getAverageHeartRate()));

        //最佳数据
        ZnsUserRunDataEntity runDataEntity = userRunDataService.findUserRunDataByUserId(user.getId(), entity.getDeviceType());
        if (Objects.isNull(runDataEntity)) {
            runRadarChart.setTheBest(getRunRadarChartData(entity.getRunTime(), entity.getRunMileage(), entity.getMaxStepFrequency(), entity.getMaxPace(), entity.getKilocalorie().intValue(), (int) (SportsDataUnit.getUserMaxHeart(user.getBirthday()) * 0.75)));
        } else {
            runRadarChart.setTheBest(getRunRadarChartData(runDataEntity.getMaxRunTime(), runDataEntity.getMaxMileageSingleDay(), runDataEntity.getMaxStepFrequency(), runDataEntity.getMaxPace(), runDataEntity.getMaxKilocalorie(), (int) (SportsDataUnit.getUserMaxHeart(user.getBirthday()) * 0.75)));
        }

        //上次数据
        ZnsUserRunDataDetailsEntity lastDetails = userRunDataDetailsService.findLastDetail(user.getId(), entity.getId(), entity.getDeviceType());

        if (Objects.isNull(lastDetails)) {
            runRadarChart.setLastTime(new RunRadarChartDetailVo());
        } else {
            runRadarChart.setLastTime(getRunRadarChartData(lastDetails.getRunTime(), lastDetails.getRunMileage(), lastDetails.getMaxStepFrequency(), lastDetails.getMaxPace(), lastDetails.getKilocalorie().intValue(), lastDetails.getAverageHeartRate()));
        }
        data.setRunRadarChart(runRadarChart);
    }

    /**
     * 获取竞技水平
     *
     * @param lastDetails
     * @param entity
     * @return
     */
    public RunningLevelVo getRunningLevelVo(ZnsUserRunDataDetailsEntity lastDetails, ZnsUserRunDataDetailsEntity entity, Integer appVersion) {
        RunningLevelVo runningLevelVo = new RunningLevelVo();
        //本次竞技值
        BigDecimal capabilityValue = SportsDataUnit.getCapabilityValue(entity.getRunMileage(), entity.getRunTime());
        runningLevelVo.setCapabilityValue(capabilityValue);
        if (Objects.nonNull(lastDetails)) {
            //上次跑力值
            BigDecimal beforeValue = SportsDataUnit.getCapabilityValue(lastDetails.getRunMileage(), lastDetails.getRunTime());
            if (beforeValue.compareTo(BigDecimal.ZERO) > 0) {
                runningLevelVo.setBeforeCapabilityValue(beforeValue);
                //（本次竞技值-上次竞技值）/上次竞技值
                BigDecimal subtract = capabilityValue.subtract(beforeValue);
                BigDecimal changeRate = subtract.divide(beforeValue, 4, BigDecimal.ROUND_HALF_UP);
                //变化比例（百分比四舍五入，取整数），水平提高了 3%，返回 0.03
                changeRate = changeRate.compareTo(new BigDecimal("1")) > 0 ? new BigDecimal("1") : changeRate;
                runningLevelVo.setChangeRate(changeRate);
            }
        }
        //超过比例，51.1% ,返回 0.551,eg: 用户当前竞技值*（100/理论最大竞技值）
        capabilityValue = capabilityValue.multiply(new BigDecimal("100")).divide(new BigDecimal("108"), 1, BigDecimal.ROUND_HALF_UP);
        BigDecimal capabilityRate = capabilityValue.divide(new BigDecimal("100"), 3, BigDecimal.ROUND_HALF_UP);
        capabilityRate = capabilityRate.compareTo(new BigDecimal("1")) > 0 ? new BigDecimal("1") : capabilityRate;
        runningLevelVo.setExceedRate(capabilityRate);
        return runningLevelVo;
    }

    /**
     * 获得分享数据
     *
     * @param userId
     * @param runType
     * @param loginUser
     * @return
     */
    public ShareDataVo getShareData(Long userId, Integer runType, ZnsUserEntity loginUser) {
        ShareDataVo data = new ShareDataVo();

        Integer deviceType = EquipmentDeviceTypeEnum.TREADMILL.getCode();
        if (Objects.equals(runType, RunDataRunTypeEnum.BIKE.getCode())) {
            deviceType = EquipmentDeviceTypeEnum.BIKE.getCode();
        } else if (Objects.equals(runType, RunDataRunTypeEnum.BICYCLE.getCode())) {
            deviceType = EquipmentDeviceTypeEnum.BICYCLE.getCode();
        } else if (Objects.equals(runType, RunDataRunTypeEnum.ROWING.getCode())) {
            deviceType = EquipmentDeviceTypeEnum.ROWING.getCode();
        }
        //总记录统计

        Map<String, Object> totalStatistics = userRunDataDetailsService.getShareDateTotalRecordMap(userId, runType);


        int run_mileage = new BigDecimal(totalStatistics.get("run_mileage").toString()).intValue();
        data.setTotalRunMileage(run_mileage);
        int kilocalorie = new BigDecimal(totalStatistics.get("kilocalorie").toString()).intValue();
        data.setCalories(kilocalorie);
        data.setTotalRunTime(MapUtils.getInteger(totalStatistics, "run_time"));
        data.setTotalRunCount(MapUtils.getInteger(totalStatistics, "totalRunCount"));
        data.setAverageVelocity(new BigDecimal(MapUtils.getDouble(totalStatistics, "averageVelocity")));
        //获取综合排名（里程）
        Integer mileageRank = userRunDataService.getMileageRank(userId, new BigDecimal(totalStatistics.get("run_mileage").toString()), runType);
        if (mileageRank > 10000) {
            data.setRunMileageRanking(10000);
        } else {
            data.setRunMileageRanking(mileageRank);
        }
        data.setRunMileageRanking(mileageRank);
        if (run_mileage > 0 && Objects.nonNull(mileageRank)) {
            long count = userRunDataService.count(deviceType);
            BigDecimal divide = BigDecimal.ONE.subtract(new BigDecimal(mileageRank).divide(new BigDecimal(count), 4, BigDecimal.ROUND_DOWN));
            BigDecimal mileageExceedPercentage = divide.multiply(new BigDecimal(100));
            if (mileageExceedPercentage.compareTo(new BigDecimal(100)) >= 0) {
                mileageExceedPercentage = new BigDecimal(99.99);
            }
            data.setMileageExceedPercentage(mileageExceedPercentage);
        }

        data.setTotalDayCount(MapUtils.getInteger(totalStatistics, "totalCount"));

        if (runType == 1) {
            CalorieConsumptionFoodCalibration calorieConsumptionFoodCalibration = CalorieConsumptionFoodCalibration.getCalorieConsumptionFoodCalibration(kilocalorie);
            data.setFoodNum(new BigDecimal(calorieConsumptionFoodCalibration.getNum()));
            data.setFoodName(calorieConsumptionFoodCalibration.getFoodName());
        } else {
            String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.CALORIE_CONSUMPTION_FOOD_CALIBRATION_WALK.getCode());

            Map<String, Object> jsonObject = JsonUtil.readValue(config);
            List<CalorieConsumptionFoodCalibrationDto> calorieConsumptionFoodCalibrationDtos = JsonUtil.readList(jsonObject.get("list"),
                    CalorieConsumptionFoodCalibrationDto.class);
            CalorieConsumptionFoodCalibrationDto calorieConsumptionFoodCalibrationDto = calorieConsumptionFoodCalibrationDtos.stream().filter(c -> c.getType().equals(loginUser.getReportFoodType())).findFirst().orElse(null);
            BigDecimal foodNum = BigDecimalUtil.divHalfUp(new BigDecimal(kilocalorie), new BigDecimal(calorieConsumptionFoodCalibrationDto.getCal()), 1);
            data.setFoodNum(foodNum);
            data.setFoodName(calorieConsumptionFoodCalibrationDto.getName());
            data.setPluralName(calorieConsumptionFoodCalibrationDto.getPluralName());
            data.setFoodIcon(calorieConsumptionFoodCalibrationDto.getIcon());
            return data;
        }


        //获取最快配速
        ZnsUserRunDataDetailsEntity pace = userRunDataDetailsService.findBestAveragePace(userId, runType);

        if (Objects.nonNull(pace)) {
            data.setBestPace(pace.getAveragePace());
            //获取配速排名
            Integer paceRanking = userRunDataDetailsService.getPaceRanking(userId, deviceType);
            long count = userRunDataService.count(deviceType);
            BigDecimal divide = new BigDecimal(paceRanking).divide(new BigDecimal(count), 5, BigDecimal.ROUND_DOWN);
            BigDecimal subtract = new BigDecimal(1).subtract(divide);
            BigDecimal paceExceedPercentage = subtract.setScale(4, BigDecimal.ROUND_DOWN);
            data.setPaceExceedPercentage(paceExceedPercentage);
        }
        return data;
    }

    /**
     * 获取数据2-游戏端获取历史总和数据
     *
     * @param userId
     * @return
     */
    public Map<String, Object> getRunData2(Long userId) {
        Map<String, Object> map = new HashMap<>();
        ZnsUserRunDataEntity runDataEntity = userRunDataService.findUserRunDataByUserId(userId, EquipmentDeviceTypeEnum.TREADMILL.getCode());

        if (Objects.isNull(runDataEntity)) {
            map.put("totalRunMileage", 0);
            map.put("totalRunTime", 0);
            map.put("totalCalorie", 0);
            map.put("totalClimbMileage", 0);
        } else {
            map.put("totalRunMileage", runDataEntity.getRunMileage().intValue());
            map.put("totalRunTime", runDataEntity.getRunTime() * 1000);
            map.put("totalCalorie", runDataEntity.getKilocalorie().intValue());
            map.put("totalClimbMileage", 0);
        }

        return map;
    }

    /**
     * 获取课程详情数据报告
     *
     * @param entity
     * @param loginUser
     * @param languageCode
     * @return
     */
    public CourseReportVo getCourseReport(ZnsUserRunDataDetailsEntity entity, ZnsUserEntity loginUser, String languageCode) {
        CourseReportVo vo = new CourseReportVo();
        vo.setDetailsId(entity.getId());
        vo.setReportCopy(entity.getReportCopywriting());
        vo.setCalorie(entity.getKilocalorie());
        vo.setRunStatus(entity.getRunStatus());
        vo.setAveragePace(entity.getAveragePace());
        vo.setAverageHeartRate(entity.getAverageHeartRate());
        vo.setReportCreateTime(entity.getLastTime());
        vo.setRunMileage(entity.getRunMileage().intValue());
        vo.setRunTime(entity.getRunTime());
        vo.setStepNum(entity.getStepNum());
        vo.setBackgroundImage(sysConfigService.getRandomData("course.report.backgroundImage"));
        ZnsCourseEntity course = courseService.getI18nCourseById(entity.getCourseId(), languageCode);
        String courseName = course.getCourseName();
        Integer courseDuration = course.getCourseDuration();
        vo.setCourseType(course.getCourseType());
        vo.setCourseName(courseName);
        vo.setCourseId(course.getId());
        vo.setCourseDuration(courseDuration);
        return vo;
    }


    /**
     * 设置基础信息
     *
     * @param data
     * @param entity
     * @param reportFoodType
     * @param activityNew
     * @param languageCode
     */
    private void setBaseInfo(RunningReportVo data, ZnsUserRunDataDetailsEntity entity, Integer reportFoodType, ActivityTypeDto activityNew, String languageCode) {
        data.setDeviceType(entity.getDeviceType());
        data.setReportDate(entity.getCreateTime().toInstant().toEpochMilli());
        data.setCapabilityValue(entity.getCapabilityValue().setScale(1, RoundingMode.HALF_UP));
        data.setChickenSoup(entity.getReportCopywriting());
        data.setCalorieTarget(entity.getCalorieTarget());
        data.setCalorie(entity.getKilocalorie());
        data.setRunMileage(entity.getRunMileage().intValue());
        data.setRunTime(entity.getRunTime());
        data.setClimbingMileage(entity.getClimbingMileage().divide(new BigDecimal(100), 0, RoundingMode.HALF_UP));
        data.setStepNum(entity.getStepNum());
        data.setFatConsumption(entity.getFatConsumption());
        data.setRunMileageRanking(entity.getRunMileageRanking());
        data.setMileageExceedPercentage(entity.getMileageExceedPercentage());
        data.setIsCheat(entity.getIsCheat());
        data.setMaxSpeed(entity.getMaxPace());
        data.setAveragePace(entity.getAveragePace());
        data.setTotalTime(entity.getTotalTime());
        data.setAverageTreadFrequency(entity.getAverageTreadFrequency());
        data.setRotateNum(entity.getRotateNum());

        SysConfig chatConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.chat_robot_user_ids.getCode());

        //填充客服
        if (Objects.nonNull(chatConfig)) {
            String configValue = chatConfig.getConfigValue();
            String[] split = configValue.split(",");
            String chatRobotUserId = split[0];
            ZnsUserEntity customer = userService.findById(Long.parseLong(chatRobotUserId));
            data.setCustomerId(Long.parseLong(chatRobotUserId));
            data.setCustomerName(customer.getFirstName());
        }

        if (Objects.nonNull(activityNew)) {
            data.setCompleteType(activityNew.getTargetType());
            if (activityNew.getTargetType() == 1) {
                data.setGoalMileage(entity.getDistanceTarget());
            } else {
                data.setGoalTime(entity.getTimeTarget());
            }
        }

        data.setActivityId(entity.getActivityId());

        data.setAverageVelocity(entity.getAverageVelocity());
        String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.CALORIE_CONSUMPTION_FOOD_CALIBRATION_WALK.getCode());

        Map<String, Object> jsonObject = JsonUtil.readValue(config);
        List<CalorieConsumptionFoodCalibrationDto> calorieConsumptionFoodCalibrationDtos = JsonUtil.readList(jsonObject.get("list"),
                CalorieConsumptionFoodCalibrationDto.class);
        CalorieConsumptionFoodCalibrationDto calorieConsumptionFoodCalibrationDto = calorieConsumptionFoodCalibrationDtos.stream().filter(c -> c.getType().equals(reportFoodType)).findFirst().orElse(null);
        CalorieConsumptionFoodCalibration calorieConsumptionFoodCalibration = CalorieConsumptionFoodCalibration.getCalorieConsumptionFoodCalibration(entity.getKilocalorie().intValue());
        if (entity.getRunType() == 1) {
            data.setFoodName(calorieConsumptionFoodCalibration.getFoodName());
        } else {
            data.setFoodName(calorieConsumptionFoodCalibrationDto.getName());
            data.setPluralName(calorieConsumptionFoodCalibrationDto.getPluralName());
            data.setPluralName(calorieConsumptionFoodCalibrationDto.getPluralName());
            data.setFoodIcon(calorieConsumptionFoodCalibrationDto.getIcon());
        }
        data.setFoodImgUrl(calorieConsumptionFoodCalibration.getImgUrl());
        data.setFoodNum(new BigDecimal(calorieConsumptionFoodCalibration.getNum()));

        data.setDataSource(entity.getDataSource());
        data.setRunType(entity.getRunType());
        if (Objects.nonNull(entity.getCourseId()) && entity.getCourseId() > 0) {
            ZnsCourseEntity course = courseService.selectById(entity.getCourseId());
            data.setCourseType(course.getCourseType());
        }

        String reportTitle = getReportTitle(entity, activityNew, languageCode);
        data.setReportTitle(reportTitle);
        data.setMainType(activityNew.getMainType());
    }

    /**
     * 设置图形数据吧
     *
     * @param data
     * @param dataPoint
     * @param entity
     * @param detailsSecondEntities
     * @param loginUser
     */
    private void setChartData(RunningReportVo data, List<ZnsUserRunDataDetailsSecondEntity> dataPoint, ZnsUserRunDataDetailsEntity entity, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities, ZnsUserEntity loginUser) {
        List<HeartRateChartDataDetail> heartRateDetails = new ArrayList<>();
        List<StepFrequencyRateChartDataDetail> stepFrequencyDetails = new ArrayList<>();

        for (ZnsUserRunDataDetailsSecondEntity secondEntity : dataPoint) {
            int second = secondEntity.getRunTime();
            HeartRateChartDataDetail heartRateDetail = new HeartRateChartDataDetail();
            heartRateDetail.setTime(second);
            heartRateDetail.setHeartRate(secondEntity.getHeartRate());
            heartRateDetails.add(heartRateDetail);

            StepFrequencyRateChartDataDetail stepFrequencyDetail = new StepFrequencyRateChartDataDetail();
            stepFrequencyDetail.setTime(second);
            stepFrequencyDetail.setStepFrequency(secondEntity.getStepFrequency());
            stepFrequencyDetails.add(stepFrequencyDetail);
        }

        /******心率图表数据******/
        data.setHeartRate(getHeartRateChartMap(entity, detailsSecondEntities));

        /******配速图表数据******/
        data.setPace(userRunDataDetailsMileageService.getPaceChartMap2(entity, loginUser.getMeasureUnit()));

        /******步频图表数据******/
        data.setStepFrequency(getStepFrequencyChartMap(entity, stepFrequencyDetails));
    }

    /**
     * 获取心率图表数据
     *
     * @param entity
     * @param heartRateDetails
     * @return
     */
    private HeartRateChartData getHeartRateChartMap(ZnsUserRunDataDetailsEntity entity, List<ZnsUserRunDataDetailsSecondEntity> heartRateDetails) {
        if (HeartRateDeviceType.thirdType().contains(entity.getHeartRateDeviceType())) {
            //获取三方心率表数据
            return getThirdHeartRateChartMap(entity);
        } else {
            return getOriginalHeartRateChartMap(entity, heartRateDetails);
        }
    }

    private HeartRateChartData getThirdHeartRateChartMap(ZnsUserRunDataDetailsEntity entity) {
        HeartRateChartData heartRate = new HeartRateChartData();

        Query query = new Query().addCriteria(new Criteria().and("runDataDetailsId").is(entity.getId()).and("heartRateDeviceType").is(entity.getHeartRateDeviceType()));
        ZnsUserRunDataDetailsHeartRate userRunDataDetailsHeartRate = mongoTemplate.findOne(query, ZnsUserRunDataDetailsHeartRate.class, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_HEART_RATE);
        if (Objects.isNull(userRunDataDetailsHeartRate)) {
            return heartRate;
        }
        heartRate.setAverageHeartRate(userRunDataDetailsHeartRate.getAverageHeartRate());
        heartRate.setMaxHeartRate(userRunDataDetailsHeartRate.getMaxHeartRate());
        heartRate.setDetail(userRunDataDetailsHeartRate.getDetail());
        heartRate.setHeartRateDeviceType(userRunDataDetailsHeartRate.getHeartRateDeviceType());
        return heartRate;
    }

    private HeartRateChartData getOriginalHeartRateChartMap(ZnsUserRunDataDetailsEntity entity, List<ZnsUserRunDataDetailsSecondEntity> heartRateDetails) {
        HeartRateChartData heartRate = new HeartRateChartData();
        heartRate.setAverageHeartRate(entity.getAverageHeartRate());
        heartRate.setMaxHeartRate(entity.getMaxHeartRate());
        heartRate.setWarmUpHeartRateTime(entity.getWarmUpHeartRateTime());
        heartRate.setFatBurningHeartRateTime(entity.getFatBurningHeartRateTime());
        heartRate.setAerobicHeartRateTime(entity.getAerobicHeartRateTime());
        heartRate.setAnaerobicHeartRateTime(entity.getAnaerobicHeartRateTime());
        heartRate.setLimitHeartRateTime(entity.getLimitHeartRateTime());
        heartRate.setHeartRateDeviceType(entity.getHeartRateDeviceType());

        //心率图
        if (CollectionUtils.isEmpty(heartRateDetails) || Optional.ofNullable(entity.getMaxHeartRate()).orElse(0) == 0) {
            heartRate.setDetail(null);
            return heartRate;
        }

        //每5秒取一个点
        heartRateDetails = heartRateDetails.stream().filter(item -> item.getRunTime() < 30000).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(heartRateDetails)){
            heartRate.setDetail(null);
            return heartRate;
        }

        List<HeartRateChartDataDetail> newHeartRateChart = new ArrayList<>();
        int intervalSecond = 5;
        ZnsUserRunDataDetailsSecondEntity firstHeartRate = heartRateDetails.get(0);
        newHeartRateChart.add(new HeartRateChartDataDetail(firstHeartRate.getRunTime(), firstHeartRate.getHeartRate())); //第一个直接取
        Map<Integer, ZnsUserRunDataDetailsSecondEntity> map = heartRateDetails.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsSecondEntity::getRunTime, Function.identity(), (k1, k2) -> k2));
        ZnsUserRunDataDetailsSecondEntity lastHeartRate = heartRateDetails.get(heartRateDetails.size() - 1);
        Integer totalTime = entity.getRunTime(); //总时间
        int num = (totalTime % intervalSecond == 0) ? (totalTime / intervalSecond - 1) : (totalTime / intervalSecond);
        for (int i = 1; i <= num; i++) {
            //每5秒取一个。空的用默认0
            ZnsUserRunDataDetailsSecondEntity heartRateDataEntity = map.get(i * intervalSecond);
            HeartRateChartDataDetail heartRateData = heartRateDataEntity == null ? new HeartRateChartDataDetail(i * intervalSecond, 0) : new HeartRateChartDataDetail(heartRateDataEntity.getRunTime(), heartRateDataEntity.getHeartRate());
            newHeartRateChart.add(heartRateData);
        }
        newHeartRateChart.add(new HeartRateChartDataDetail(lastHeartRate.getRunTime(), lastHeartRate.getHeartRate())); //最后一个直接取
        heartRate.setDetail(newHeartRateChart);
        return heartRate;
    }

    /**
     * 获取步频图表数据
     *
     * @param entity
     * @param stepFrequencyDetails
     * @return
     */
    private StepFrequencyRateChartData getStepFrequencyChartMap(ZnsUserRunDataDetailsEntity entity, List<StepFrequencyRateChartDataDetail> stepFrequencyDetails) {
        StepFrequencyRateChartData stepFrequency = new StepFrequencyRateChartData();
        stepFrequency.setAverageStepFrequency(entity.getAverageStepFrequency());
        stepFrequency.setMaxStepFrequency(entity.getMaxStepFrequency());
        stepFrequency.setDetail(stepFrequencyDetails);
        return stepFrequency;
    }

    /**
     * 获取雷达图数据
     *
     * @param runTime
     * @param runMileage
     * @param stepFrequency
     * @param pace
     * @param kilocalorie
     * @param heartRate
     * @return
     */
    private RunRadarChartDetailVo getRunRadarChartData(Integer runTime, BigDecimal runMileage, Integer stepFrequency, Integer pace, Integer kilocalorie, Integer heartRate) {
        RunRadarChartDetailVo runRadarChartData = new RunRadarChartDetailVo();
        runRadarChartData.setRunTime(runTime);
        runRadarChartData.setRunMileage(runMileage.intValue());
        runRadarChartData.setStepFrequency(stepFrequency);
        runRadarChartData.setPace(pace);
        runRadarChartData.setCalorie(kilocalorie);
        runRadarChartData.setHeartRate(heartRate);
        return runRadarChartData;
    }

    /**
     * 设置速度曲线
     *
     * @param seconds
     */
    private List<SpeedLineDto> setSpeedLineData(List<ZnsUserRunDataDetailsSecondEntity> seconds) {
        if (CollectionUtils.isEmpty(seconds)) {
            return null;
        }
        List<SpeedLineDto> speedLines = new ArrayList<>();
        // 排序
        List<ZnsUserRunDataDetailsSecondEntity> collect = seconds.stream().sorted(Comparator.comparing(ZnsUserRunDataDetailsSecondEntity::getRunTime)).toList();
        List<List<ZnsUserRunDataDetailsSecondEntity>> groupCollected = collect.stream().collect(Collectors.groupingBy(i -> (i.getRunTime() - 1) / 60)).values().stream().collect(Collectors.toList());
        for (int i = 0; i < groupCollected.size(); i++) {
            List<ZnsUserRunDataDetailsSecondEntity> minSecond = groupCollected.get(i);
            BigDecimal divide = minSecond.stream().map(ZnsUserRunDataDetailsSecondEntity::getVelocity).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(minSecond.size()), 2, RoundingMode.HALF_UP);
            speedLines.add(new SpeedLineDto(i, divide));
        }
        return speedLines;
    }

    /**
     * 获取数据端点
     *
     * @param detailId
     * @param detailsSecondEntities
     * @return
     */
    private List<ZnsUserRunDataDetailsSecondEntity> getPointData(Long detailId, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities) {
        //数据，20秒一个
        List<ZnsUserRunDataDetailsSecondEntity> dataPoint = detailsSecondEntities.stream().filter(s -> s.getRunTime() % 20 == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataPoint)) {
            return new ArrayList<>();
        }
        Map<Integer, ZnsUserRunDataDetailsSecondEntity> collect = dataPoint.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsSecondEntity::getRunTime, Function.identity(), (x, y) -> y));
        dataPoint = collect.values().stream().sorted(Comparator.comparing(ZnsUserRunDataDetailsSecondEntity::getRunTime)).collect(Collectors.toList());
        //断点数据补存
        int points = dataPoint.get(dataPoint.size() - 1).getRunTime() / 20;
        if (points > dataPoint.size() - 1) {
            for (int i = 0; i <= points; i++) {
                if (dataPoint.get(i).getRunTime() / 20 != i) {
                    dataPoint.add(i, defaultSecondEntity(i));
                }
            }
        }
        if (dataPoint.get(0).getRunTime() == 0) {
            dataPoint.remove(0);
        }
        return dataPoint;
    }

    /**
     * ZnsUserRunDataDetailsSecondEntity 默认值设置
     *
     * @param i
     * @return
     */
    private ZnsUserRunDataDetailsSecondEntity defaultSecondEntity(int i) {
        ZnsUserRunDataDetailsSecondEntity entity = new ZnsUserRunDataDetailsSecondEntity();
        entity.setCalories(BigDecimal.ZERO);
        entity.setGradient(0);
        entity.setStepNum(0);
        entity.setHeartRate(0);
        entity.setMileage(BigDecimal.ZERO);
        entity.setVelocity(BigDecimal.ZERO);
        entity.setRunTime(i * 20);
        entity.setPace(0);
        entity.setStepFrequency(0);
        return entity;
    }


    /**
     * 聚合报告
     *
     * @param loginUser
     * @param mainActivity
     * @return
     */
    public RunningReportVo runningReportSeries(ZnsUserEntity loginUser, MainActivity mainActivity) {
        RunningReportVo data = new RunningReportVo();
        data.setNickname(loginUser.getFirstName()).setHeadPortrait(loginUser.getHeadPortrait());

        List<StageCapacityDto> stageCapacityList = new ArrayList<>();
        List<SeriesActivityRel> seriesActivityRelServiceSubActivity = seriesActivityRelService.findSubActivity(mainActivity.getId());
        Integer runTime = 0;
        BigDecimal runMileage = BigDecimal.ZERO;
        BigDecimal kilocalorie = BigDecimal.ZERO;
        Integer fatConsumption = 0;
        Integer stepNum = 0;
        BigDecimal foodNum1 = BigDecimal.ZERO;
        for (int i = 0; i < seriesActivityRelServiceSubActivity.size(); i++) {
            SeriesActivityRel seriesActivityRel = seriesActivityRelServiceSubActivity.get(i);
            Long segmentActivityId = seriesActivityRel.getSegmentActivityId();
            StageCapacityDto stageCapacityDto = new StageCapacityDto();
            stageCapacityDto.setStage(seriesActivityRel.getLevel() + "");
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(segmentActivityId, loginUser.getId());
            if (Objects.nonNull(activityUser)) {
                Long runDataDetailsId = activityUser.getRunDataDetailsId();
                ZnsUserRunDataDetailsEntity runDataDetailsServiceById = userRunDataDetailsService.findById(runDataDetailsId);
                stageCapacityDto.setPace(runDataDetailsServiceById.getAveragePace());
                stageCapacityDto.setRunMileage(runDataDetailsServiceById.getRunMileage());
                runTime = runTime + runDataDetailsServiceById.getRunTime();
                stepNum = stepNum + runDataDetailsServiceById.getStepNum();
                runMileage = runMileage.add(runDataDetailsServiceById.getRunMileage());
                kilocalorie = kilocalorie.add(runDataDetailsServiceById.getKilocalorie());
                fatConsumption = fatConsumption + runDataDetailsServiceById.getFatConsumption();
                if (runDataDetailsServiceById.getRunType() == 1) {
                    CalorieConsumptionFoodCalibration calorieConsumptionFoodCalibration = CalorieConsumptionFoodCalibration.getCalorieConsumptionFoodCalibration(runDataDetailsServiceById.getKilocalorie().intValue());
                    data.setFoodNum(foodNum1.add(new BigDecimal(calorieConsumptionFoodCalibration.getNum())));
                    data.setFoodName(calorieConsumptionFoodCalibration.getFoodName());
                    data.setFoodImgUrl(calorieConsumptionFoodCalibration.getImgUrl());
                } else {
                    String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.CALORIE_CONSUMPTION_FOOD_CALIBRATION_WALK.getCode());
                    Map<String, Object> jsonObject = JsonUtil.readValue(config);
                    List<CalorieConsumptionFoodCalibrationDto> calorieConsumptionFoodCalibrationDtos = JsonUtil.readList(jsonObject.get("list"),
                            CalorieConsumptionFoodCalibrationDto.class);
                    CalorieConsumptionFoodCalibrationDto calorieConsumptionFoodCalibrationDto = calorieConsumptionFoodCalibrationDtos.stream().filter(c -> c.getType().equals(loginUser.getReportFoodType())).findFirst().orElse(null);
                    BigDecimal foodNum = BigDecimalUtil.divHalfUp(kilocalorie, new BigDecimal(calorieConsumptionFoodCalibrationDto.getCal()), 1);
                    data.setFoodNum(foodNum1.add(foodNum));
                    data.setFoodName(calorieConsumptionFoodCalibrationDto.getName());
                    data.setPluralName(calorieConsumptionFoodCalibrationDto.getPluralName());
                    data.setFoodIcon(calorieConsumptionFoodCalibrationDto.getIcon());
                }
                //最后一条数据
                if (i == seriesActivityRelServiceSubActivity.size() - 1) {
                    data.setPace(userRunDataDetailsMileageService.getPaceChartMap2(runDataDetailsServiceById, loginUser.getMeasureUnit()));
                    // 雷达
                    setRunRadarChart(data, runDataDetailsServiceById, loginUser);
                    // 超越比例
                    data.setMileageExceedPercentage(runDataDetailsServiceById.getMileageExceedPercentage());
                    //上次数据

                    ZnsUserRunDataDetailsEntity lastDetails = userRunDataDetailsService.findLastDetail(loginUser.getId(), runDataDetailsServiceById.getId(), runDataDetailsServiceById.getDeviceType());
                    //设置竞技水平
                    RunningLevelVo runningLevelVo = getRunningLevelVo(lastDetails, runDataDetailsServiceById, 3100);
                    data.setRunningLevelVo(runningLevelVo);
                }
            }
            stageCapacityList.add(stageCapacityDto);

        }

        data.setStageCapacityList(stageCapacityList);
        data.setCalorie(kilocalorie);
        data.setRunMileage(runMileage.intValue());
        data.setRunTime(runTime);
        data.setStepNum(stepNum);
        data.setFatConsumption(fatConsumption);
        return data;
    }

    public void setUserLevelData(RunningReportVo vo) {
        // 跑步时间
        RunningEnduranceLevel level = RunningEnduranceLevel.fromTime(vo.getRunTime());
        val aerobicCapacityVo = new AerobicCapacityVo();
        aerobicCapacityVo.setRunTime(vo.getRunTime());
        aerobicCapacityVo.setCapacity(level.getLevel());
        aerobicCapacityVo.setContent(level.getContent());
        aerobicCapacityVo.setLevelNum(level.getLevelNum());
        vo.setAerobicCapacity(aerobicCapacityVo);
        UserPerformanceLevels userPerformanceLevels = userPerformanceLevelsService.queryByUserSpeedAndType(vo.getRunType(), vo.getAverageVelocity());
        val performanceLevelVo = new PerformanceLevelVo();
        if (Objects.nonNull(userPerformanceLevels)) {
            setPerformanceLevel(performanceLevelVo, userPerformanceLevels);
        } else {
            //无匹配数据补偿逻辑超过最大边界
            userPerformanceLevels = userPerformanceLevelsService.getMaxSpeedConfigByRunType(vo.getRunType());
            if (vo.getRunType() == 1) {
                if (vo.getAverageVelocity().compareTo(userPerformanceLevels.getRunningMachineSpeedMax()) > 0) {
                    setPerformanceLevel(performanceLevelVo, userPerformanceLevels);
                }
            }
            if (vo.getRunType() == 2) {
                if (vo.getAverageVelocity().compareTo(userPerformanceLevels.getWalkingMachineSpeedMax()) > 0) {
                    setPerformanceLevel(performanceLevelVo, userPerformanceLevels);
                }
            }
        }
        vo.setPerformanceLevel(performanceLevelVo);
    }


    private void setPerformanceLevel(PerformanceLevelVo performanceLevelVo, UserPerformanceLevels userPerformanceLevels) {
        performanceLevelVo.setLevel(userPerformanceLevels.getPerformanceLevel());
        performanceLevelVo.setSpeedLevel(userPerformanceLevels.getLevelContent());
        performanceLevelVo.setContent(userPerformanceLevels.getNoticeTips());
        performanceLevelVo.setLevelNum(userPerformanceLevels.getLevelNum());
    }

    public RunSpeedLimitNoticeResponseDto speedLimitNotice(Long userId) {
        RunSpeedLimitNoticeResponseDto responseDto = new RunSpeedLimitNoticeResponseDto();
        ZnsUserEquipmentEntity userEquipment = userEquipmentService.getUserLastConnectedEquipment(userId);
        String model = "AS02";
        if (model.equals(userEquipment.getEquipmentModel())) {
            return responseDto;
        }
        DeviceSpeedLimitPushVo deviceSpeedLimitPushVo = JsonUtil.readValue(sysConfigService.selectConfigByKey(ConfigKeyEnums.DEVICE_SPEED_LIMIT_PUSH.getCode()), DeviceSpeedLimitPushVo.class);
        String discountCode = deviceSpeedLimitPushVo.getDiscountCode();
        String discountLink = deviceSpeedLimitPushVo.getDiscountLink();
        String popKey = RedisKeyConstant.RUN_SPEED_LIMIT_POP + userId;
        RBucket<Object> popBucket = redissonClient.getBucket(popKey);
        if (Objects.isNull(popBucket.get()) && YesNoStatus.YES.getCode().equals(deviceSpeedLimitPushVo.getPopPush())) {
            responseDto.setDiscountCode(discountCode);
            responseDto.setDiscountLink(discountLink);
            popBucket.set(1, 14, TimeUnit.DAYS);
        }
        String imKey = RedisKeyConstant.RUN_SPEED_LIMIT_IM + userId;
        RBucket<Object> imBucket = redissonClient.getBucket(imKey);
        if (Objects.isNull(imBucket.get()) && YesNoStatus.YES.getCode().equals(deviceSpeedLimitPushVo.getImPush())) {
            //消息1
            String message = I18nMsgUtils.getMessage("im.speed.limit.notice_1", discountCode);
            appMessageService.sendIm("administrator", List.of(userId), message, TencentImConstant.TIM_TEXT_ELEM, "", 0, Boolean.FALSE);
            //消息2
            ImMessageBo imMessageBo = new ImMessageBo();
            Map<String, Object> params = new HashMap<>();
            params.put("isDiscountLink", 1);
            imMessageBo.setParams(params);
            imMessageBo.setJumpType("0");
            imMessageBo.setJumpValue(discountLink);
            imMessageBo.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202408/ilWIY2Kd9_leqKfi.jpeg");
            imMessageBo.setMsg(I18nMsgUtils.getMessage("im.speed.limit.notice_2"));
            appMessageService.sendIm("administrator", List.of(userId), JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
            imBucket.set(1, 14, TimeUnit.DAYS);
        }
        return responseDto;
    }


    public ShareReportResponseDto shareReport(Long userRunDataDetailsId, Long activityId, String languageCode) {
        ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.findById(userRunDataDetailsId);
        if (Objects.isNull(entity)) {
            return null;
        }
        ZnsUserEntity user = userService.findById(entity.getUserId());

        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.share_report_data_config.getCode());
        ShareConfigVo vo = JsonUtil.readValue(sysConfig.getConfigValue(), ShareConfigVo.class);
        // 设备base 坡度数据
        ZnsTreadmillEntity treadmillEntity = treadmillService.findById(entity.getTreadmillId());
        ShareReportResponseDto dto = new ShareReportResponseDto();
        if (Objects.nonNull(treadmillEntity)) {
            //查询设备信息
            UserEquipmentVO equipmentVo = equipmentProductionBatchService.selectEquipmentVo(treadmillEntity.getBatchNumber());
            if (Objects.nonNull(equipmentVo)) {
                dto.setMaximumLiftingGradient(equipmentVo.getMaximumLiftingGradient());
            }
        }
        dto.setDeviceType(entity.getDeviceType()).setReportDate(entity.getCreateTime()).setRunTime(entity.getRunTime())
                .setAveragePace(entity.getAveragePace()).setRunMileage(entity.getRunMileage())
                .setRotateNum(entity.getRotateNum()).setAverageTreadFrequency(entity.getAverageTreadFrequency())
                .setKilocalorie(entity.getKilocalorie())
                .setNickname(user.getFirstName()).setImageList(vo.getImageList())
                .setHeadPortrait(user.getHeadPortrait())
                .setClimbingMileage(entity.getClimbingMileage().divide(new BigDecimal(100), 0, RoundingMode.HALF_UP));
        if (entity.getActivityId() <= 1) {
            String reportTitle = getReportTitle(entity, null, languageCode);
            dto.setTitle(reportTitle);
            return dto;
        }

        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(entity.getActivityId(), "");
        String reportTitle = getReportTitle(entity, activityNew, languageCode);
        dto.setTitle(reportTitle);
        String reportMainType = getReportMainType(entity, activityNew);
        dto.setReportMainType(reportMainType);
        //如果查看的是系列赛的总数据，activityId填写了，且类型是系列赛.对数据进行覆盖
        Long detailActivityId = entity.getActivityId();
        if (activityId != null && activityId > 1) {
            activityNew = mainActivityBizService.getActivityNew(activityId, "");
            detailActivityId = activityId;
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(detailActivityId, entity.getUserId());
        dto.setRank(activityUser.getRank());
        if (Objects.isNull(activityNew)) {
            return dto;
        }
        if (MainActivityTypeEnum.RANK.getType().equals(activityNew.getMainType())) {
            RunRankedActivityUser rankedActivityUser = runRankedActivityUserService.findByRunDataDetailsId(userRunDataDetailsId);
            dto.setRank(rankedActivityUser.getRank());
            UserRankedLevelLog rankedLevelLog = userRankedLevelService.findByUserIdAndActivityId(entity.getUserId(), entity.getActivityId());
            if (Objects.nonNull(rankedLevelLog)) {
                RankedLevelEnums rankedLevelEnum = RankedLevelEnums.resolve(rankedLevelLog.getLevel(), rankedLevelLog.getRank());
                dto.setLevelName(I18nMsgUtils.getMessage("rankedActivity.rank.RankedLevelEnums." + rankedLevelEnum));
            }
        } else if (MainActivityTypeEnum.PROP.getType().equals(activityNew.getMainType())) {
            PropRunRankedActivityUser rankedActivityUser = propRunRankedActivityUserService.findByRunDataDetailsId(userRunDataDetailsId);
            dto.setRank(rankedActivityUser.getRank());
            PropUserRankedLevelLog rankedLevelLog = propUserRankedLevelLogService.findByUserIdAndActivityId(entity.getUserId(), entity.getActivityId());
            if (Objects.nonNull(rankedLevelLog)) {
                PropRankedLevelEnums rankedLevelEnum = PropRankedLevelEnums.resolve(rankedLevelLog.getLevel(), rankedLevelLog.getRank());
                if (Objects.nonNull(rankedLevelEnum)) {
                    dto.setLevelName(I18nMsgUtils.getMessage("prop.rankedActivity.rank.RankedLevelEnums." + rankedLevelEnum));
                }
            }
        } else if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(activityNew.getMainType())) {
            user.setMeasureUnit(0);
            RunningReportVo detail = runningReportSeries(user, activityNew.getMainActivity());
            PaceRateChartData pace = detail.getPace();
            if (pace != null) {
                dto.setAveragePace(pace.getAveragePace());
            }
            dto.setRunMileage(BigDecimal.valueOf(detail.getRunMileage()));
            dto.setRunTime(detail.getRunTime());
            dto.setKilocalorie(detail.getCalorie());
        }

        return dto;
    }

    private String getReportMainType(ZnsUserRunDataDetailsEntity entity, ActivityTypeDto activityNew) {
        if (Objects.isNull(activityNew)) {
            if (entity.getCourseId() > 0) {
                return ReportMainTypeEnum.COURSE_RUN.getCode();
            } else if (entity.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0 || entity.getTimeTarget() > 0) {
                return ReportMainTypeEnum.TARGET_RUN.getCode();
            } else if (entity.getDataSource() == 1 || entity.getDataSource() == 2) {
                return ReportMainTypeEnum.NORMAL_RUN.getCode();
            } else {
                return ReportMainTypeEnum.FREE_RUN.getCode();
            }
        } else if (MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
            if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityNew.getActivityType())) {
                return ReportMainTypeEnum.USER_TOURNAMENT.getCode();
            } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityNew.getActivityType()) && activityNew.getActivityTypeSub() == RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType()) {
                return ReportMainTypeEnum.PACE_PK.getCode();
            }
        } else if (MainActivityTypeEnum.RANK.getType().equals(activityNew.getMainType())) {
            return ReportMainTypeEnum.RANK.getCode();
        } else if (MainActivityTypeEnum.PROP.getType().equals(activityNew.getMainType())) {
            return ReportMainTypeEnum.PROP.getCode();
        } else {
            return ReportMainTypeEnum.COMPETITIVE_TOURNAMENT.getCode();
        }

        return ReportMainTypeEnum.OTHER.getCode();
    }

    private String getReportTitle(ZnsUserRunDataDetailsEntity entity, ActivityTypeDto activityNew, String languageCode) {
        if (Objects.isNull(activityNew) || activityNew.getId() < 2) {
            if (entity.getCourseId() > 0) {
                ZnsCourseEntity course = courseService.getI18nCourseById(entity.getCourseId(), languageCode);
                String reportTitle = course.getCourseName();
                Long trainingId = entity.getTrainingId();
                if (Objects.nonNull(trainingId) && course.getCategoryName().equals("Training program")) {
                    UserTrainingPlanDetail userTrainingPlanDetail = userTrainingPlanDetailService.findById(trainingId);
                    if (Objects.nonNull(userTrainingPlanDetail)) {
                        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findById(userTrainingPlanDetail.getUserPlanId());
                        List<AppTrainingPlanDetailResponseDto> trainingPlanInfos = trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(userTrainingPlan.getPlanId()), languageCode);
                        if (!CollectionUtils.isEmpty(trainingPlanInfos)) {
                            reportTitle = trainingPlanInfos.get(0).getTitle() + "-" + reportTitle;
                        }
                    }
                }
                return reportTitle;
            } else if (entity.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0 || entity.getTimeTarget() > 0) {
                return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.TARGET_RUN.getCode());
            } else if (entity.getDataSource() == 1 || entity.getDataSource() == 2) {
                return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.NORMAL_RUN.getCode());
            } else {
                return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.FREE_RUN.getCode());
            }
        } else if (MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
            List<Integer> pkType = RunActivitySubTypeEnum.getPkType();
            if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityNew.getActivityType())) {
                return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.USER_TOURNAMENT.getCode());
            } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityNew.getActivityType())) {
                if (pkType.contains(activityNew.getActivityTypeSub())) {
                    return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.PACE_PK.getCode());
                }
            }
        } else if (MainActivityTypeEnum.RANK.getType().equals(activityNew.getMainType())) {
            return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.RANK.getCode());
        } else if (MainActivityTypeEnum.PROP.getType().equals(activityNew.getMainType())) {
            return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.PROP.getCode());
        } else if (MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(activityNew.getMainType()) || MainActivityTypeEnum.FREE_CHALLENGE_SUB.getType().equals(activityNew.getMainType())) {
            return I18nMsgUtils.getLangMessage(languageCode, "run.report.title." + ReportMainTypeEnum.FREE_CHALLENGE.getCode());
        } else {
            return activityDisseminateBizService.findByActivityIdAndLanguage(activityNew.getId(), languageCode).getTitle();
        }

        return "";
    }

    /**
     * 系列赛新报告
     *
     * @param loginUser
     * @param mainActivity
     * @return
     */
    public SeriesRunningReportResp runningReportSeriesV2(ZnsUserEntity loginUser, MainActivity mainActivity) {
        SeriesRunningReportResp data = new SeriesRunningReportResp();
        data.setIsFinishAll(YesNoStatus.NO.getCode());
        List<StageCapacityDto> stageCapacityList = new ArrayList<>();
        List<SeriesActivityRel> seriesActivityRelServiceSubActivity = seriesActivityRelService.findSubActivity(mainActivity.getId());
        // 总计数成绩
        int runTime = 0;
        int stepNum = 0;
        BigDecimal runMileage = BigDecimal.ZERO;
        BigDecimal kilocalorie = BigDecimal.ZERO;
        ZnsRunActivityUserEntity allUserActivity = activityUserService.findActivityUser(mainActivity.getId(), loginUser.getId());
        // 完成标志
        if (allUserActivity.getIsComplete().equals(YesNoStatus.YES.getCode())) {
            data.setIsFinishAll(YesNoStatus.YES.getCode());
        }
        //title
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), loginUser.getLanguageCode());
        if (Objects.nonNull(disseminate)) {
            data.setActivityTitle(disseminate.getTitle());
        }
        //平均速度(公里/小时)
        List<BigDecimal> averageVelocity = new ArrayList<>();
        //最高配速
        List<Integer> maxPace = new ArrayList<>();
        //最大速度
        List<BigDecimal> topSpeed = new ArrayList<>();
        for (SeriesActivityRel seriesActivityRel : seriesActivityRelServiceSubActivity) {
            // 阶段计数成绩
            int runTimeSer = 0;
            BigDecimal runMileageSer = BigDecimal.ZERO;
            Long segmentActivityId = seriesActivityRel.getSegmentActivityId();
            StageCapacityDto stageCapacityDto = new StageCapacityDto();
            stageCapacityDto.setStage(seriesActivityRel.getLevel() + "");
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(segmentActivityId, loginUser.getId());
            MainActivity seriesSubActivity = mainActivityService.findById(segmentActivityId);
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(seriesSubActivity.getPlayId());
            if (Objects.nonNull(activityUser)) {
                // 计算成绩的明细detail
                Long runDataDetailsId = activityUser.getRunDataDetailsId();
                ZnsUserRunDataDetailsEntity detail = userRunDataDetailsService.findById(runDataDetailsId);
                // 两个累计条件下
                if (FetchRuleTypeEnum.ACCUMULATED_PARTICIPATION_DATA.getType().equals(entryGameplay.getFetchRule())) {
                    // 所有有效成绩
                    List<ZnsUserRunDataDetailsEntity> details = userRunDataDetailsService.findByActivityId(seriesSubActivity.getId());
                    // 所有计算数值的detail
                    List<BigDecimal> subTopSpeeds = new ArrayList<>();
                    for (ZnsUserRunDataDetailsEntity detailsEntity : details) {
                        // 速度曲线秒级别数据 计算topSpeed
                        List<ZnsUserRunDataDetailsSecondEntity> seconds = userRunDataDetailsSecondService.getSecondsList(detailsEntity.getId());
                        List<SpeedLineDto> speedLineDtos = setSpeedLineData(seconds);
                        if (!CollectionUtils.isEmpty(speedLineDtos)) {
                            subTopSpeeds.add(speedLineDtos.stream().max(Comparator.comparing(SpeedLineDto::getSpeed)).map(SpeedLineDto::getSpeed).orElse(BigDecimal.ZERO));
                        }
                        runTime = runTime + detailsEntity.getRunTime();
                        stepNum = stepNum + detailsEntity.getStepNum();
                        runMileage = runMileage.add(detailsEntity.getRunMileage());
                        kilocalorie = kilocalorie.add(detailsEntity.getKilocalorie());
                        maxPace.add(detailsEntity.getMaxPace());
                        runTimeSer = runTimeSer + detailsEntity.getRunTime();
                        runMileageSer = runMileageSer.add(detailsEntity.getRunMileage());
                    }
                    topSpeed.add(subTopSpeeds.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                } else if (FetchRuleTypeEnum.ACCUMULATED_COMPLETION_DATA.getType().equals(entryGameplay.getFetchRule())) {
                    // 所有有效成绩
                    List<ZnsUserRunDataDetailsEntity> details = userRunDataDetailsService.findByActivityId(seriesSubActivity.getId());
                    // 所有计算数值的detail
                    List<BigDecimal> subTopSpeeds = new ArrayList<>();
                    for (ZnsUserRunDataDetailsEntity detailsEntity : details) {
                        Integer targetStatus = userRunDataDetailsService.getTargetStatus(detailsEntity);
                        if (targetStatus == 1) {
                            // 速度曲线秒级别数据 计算topSpeed
                            List<ZnsUserRunDataDetailsSecondEntity> seconds = userRunDataDetailsSecondService.getSecondsList(detailsEntity.getId());
                            List<SpeedLineDto> speedLineDtos = setSpeedLineData(seconds);
                            if (!CollectionUtils.isEmpty(speedLineDtos)) {
                                subTopSpeeds.add(speedLineDtos.stream().max(Comparator.comparing(SpeedLineDto::getSpeed)).map(SpeedLineDto::getSpeed).orElse(BigDecimal.ZERO));
                            }
                            runTime = runTime + detailsEntity.getRunTime();
                            stepNum = stepNum + detailsEntity.getStepNum();
                            runMileage = runMileage.add(detailsEntity.getRunMileage());
                            kilocalorie = kilocalorie.add(detailsEntity.getKilocalorie());
                            maxPace.add(detailsEntity.getMaxPace());
                            runTimeSer = runTimeSer + detailsEntity.getRunTime();
                            runMileageSer = runMileageSer.add(detailsEntity.getRunMileage());
                        }
                    }
                    topSpeed.add(subTopSpeeds.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                } else {
                    // 速度曲线秒级别数据 计算topSpeed
                    if (Objects.nonNull(detail)) {
                        List<ZnsUserRunDataDetailsSecondEntity> seconds = userRunDataDetailsSecondService.getSecondsList(detail.getId());
                        List<SpeedLineDto> speedLineDtos = setSpeedLineData(seconds);
                        if (!CollectionUtils.isEmpty(speedLineDtos)) {
                            topSpeed.add(speedLineDtos.stream().max(Comparator.comparing(SpeedLineDto::getSpeed)).map(SpeedLineDto::getSpeed).orElse(BigDecimal.ZERO));
                        }
                        runTime = runTime + detail.getRunTime();
                        stepNum = stepNum + detail.getStepNum();
                        runMileage = runMileage.add(detail.getRunMileage());
                        kilocalorie = kilocalorie.add(detail.getKilocalorie());
                        maxPace.add(detail.getMaxPace());
                        runTimeSer = runTimeSer + detail.getRunTime();
                        runMileageSer = runMileageSer.add(detail.getRunMileage());
                    }
                }
                stageCapacityDto.setPace(SportsDataUnit.getPace(runTimeSer, BigDecimalUtil.divHalfUp(runMileageSer, new BigDecimal(1000), 4)));
                stageCapacityDto.setRunMileage(activityUser.getRunMileage());
                if (Objects.nonNull(detail)) {
                    averageVelocity.add(detail.getAverageVelocity());
                }
            }
            stageCapacityList.add(stageCapacityDto);
        }
        data.setStageCapacityList(stageCapacityList);
        data.setCalorie(kilocalorie);
        data.setRunMileage(runMileage.intValue());
        data.setRunTime(runTime);
        data.setTopSpeed(topSpeed.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
        data.setAveragePace(SportsDataUnit.getPace(runTime, BigDecimalUtil.divHalfUp(runMileage, new BigDecimal(1000), 4)));
        data.setMaxPace(maxPace.stream().min(Integer::compareTo).orElse(0));
        data.setAverageVelocity(averageVelocity.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
        //判断是否显示卡路里
        List<Long> segmentActivityIdList = seriesActivityRelServiceSubActivity.stream().map(SeriesActivityRel::getSegmentActivityId).toList();
        List<String> productCodeList = userRunDataDetailsService.findProductCodeList(loginUser.getId(), segmentActivityIdList);
        if (!CollectionUtils.isEmpty(productCodeList)) {
            data.setShowCalorie(productCodeList.stream().anyMatch(restrictedCodes::contains)?0:1);
        }
        return data;
    }

    /**
     * 获取运动的最高速度
     * @param detailId
     * @return 速度 km/h
     *
     */
    public BigDecimal runDataDetailTopSpeed(Long detailId){
        List<ZnsUserRunDataDetailsSecondEntity> seconds = userRunDataDetailsSecondService.getSecondsList(detailId);
        BigDecimal bigDecimal1 = seconds.stream().max(Comparator.comparing(ZnsUserRunDataDetailsSecondEntity::getVelocity)).map(ZnsUserRunDataDetailsSecondEntity::getVelocity).orElse(BigDecimal.ZERO);

        return bigDecimal1.setScale(1,RoundingMode.HALF_UP);

    }
}
