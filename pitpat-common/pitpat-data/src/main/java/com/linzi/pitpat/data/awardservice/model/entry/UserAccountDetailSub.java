package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*用户账户明细分类表
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@NoArgsConstructor
@TableName("zns_user_account_detail_sub")
public class UserAccountDetailSub implements java.io.Serializable {


    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserAccountDetailSub:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                         // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";            // 是否删除（0否 1是）
    public final static String create_time = CLASS_NAME + "create_time";        // 创建时间
    public final static String modifie_time = CLASS_NAME + "modifie_time";      // 最后修改时间
    public final static String user_id = CLASS_NAME + "user_id";                // 用户id
    public final static String detail_id = CLASS_NAME + "detail_id";            // 明细id
    public final static String type_ = CLASS_NAME + "type";                     // 1：保证金 2：参与奖励 3：发起奖励 4：名次奖励 5：完赛奖励 6：胜者奖励
    public final static String rank_ = CLASS_NAME + "rank";                     // 名次
    public final static String amount_ = CLASS_NAME + "amount";                 // 金额
    public final static String activity_id = CLASS_NAME + "activity_id";        // 活动id
    public final static String is_test = CLASS_NAME + "is_test";                // 是否是测试用户
    public final static String is_robot = CLASS_NAME + "is_robot";              // 是否是机器人，1 是机器人，0 不是机器人
    public final static String activity_type = CLASS_NAME + "activity_type";    // 活动类型


    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //创建时间
    private ZonedDateTime createTime;
    //最后修改时间
    private ZonedDateTime modifieTime;
    //用户id
    private Long userId;
    //明细id
    private Long detailId;
    //1：保证金 2：参与奖励 3：发起奖励 4：名次奖励 5：完赛奖励
    private Integer type;
    //名次
    @TableField("`rank`")
    private Integer rank;
    //金额
    private BigDecimal amount;
    //金额
    private BigDecimal extraAmount;
    //活动id
    private Long activityId;
    //是否是测试用户
    private Integer isTest;
    //是否是机器人，1 是机器人，0 不是机器人
    private Integer isRobot;
    //活动类型
    private Integer activityType;

    @Override
    public String toString() {
        return "UserAccountDetailSub{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",createTime=" + createTime +
                ",modifieTime=" + modifieTime +
                ",userId=" + userId +
                ",detailId=" + detailId +
                ",type=" + type +
                ",rank=" + rank +
                ",amount=" + amount +
                ",activityId=" + activityId +
                "}";
    }
}
