package com.linzi.pitpat.data.awardservice.model.resp;

import com.linzi.pitpat.data.annotation.Excel;
import com.linzi.pitpat.data.util.file.BaseExcel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;


@Data
@NoArgsConstructor
public class UserCouponResp extends BaseExcel implements Serializable {

    private Long id;
    private Long userId;
    @Excel(name = "券名称", prompt = "券名称", sort = 1)
    private String couponName; //券名称
    @Excel(name = "券类型", defaultValue = "1", readConverterExp = "1=参赛必胜券,2=奖励翻倍券,3=幸运现金券,4=亚马逊优惠券", cellType = Excel.ColumnType.NUMERIC, sort = 2)
    private Integer couponType; //券类型
    @Excel(name = "获得者昵称", prompt = "获得者昵称", sort = 3)
    private String nickName; // 获得者昵称
    @Excel(name = "获得者邮箱", prompt = "获得者邮箱", sort = 4)
    private String emailAddress; //获得者邮箱;
    @Excel(name = "兑换时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 5)
    private ZonedDateTime gmtCreate; // 兑换时间
    @Excel(name = "到期时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 6)
    private ZonedDateTime gmtEnd; // 到期时间
    @Excel(name = "使用时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 7)
    private ZonedDateTime gmtUse; // 使用时间
    @Excel(name = "券状态", defaultValue = "0", readConverterExp = "0=未使用,1=使用中,2=已使用,3=已过期,4=已失效", cellType = Excel.ColumnType.NUMERIC, sort = 8)
    private Integer status; // 券状态
    @Excel(name = "标记备注", prompt = "标记备注", sort = 9)
    private String remarks; //  标记备注
    @Excel(name = "测试用户", defaultValue = "0", readConverterExp = "0=非测试用户,1=测试用户", cellType = Excel.ColumnType.NUMERIC, sort = 10)
    private Integer isTest; //  标记备注
    @Excel(name = "券类型", defaultValue = "1", readConverterExp = "1=刮刮乐,2=排行榜", cellType = Excel.ColumnType.NUMERIC, sort = 11)
    private Integer type; //  标记备注【1：刮刮卡券,2 排行榜 】

    @Excel(name = "获取方式", defaultValue = "1", readConverterExp = "1=兑换,2=植树节免费领取,3=活动领取,5=积分兑换", cellType = Excel.ColumnType.NUMERIC, sort = 12)
    private String sourceType; //获取来源【1：兑换,2:植树节免费领取】
    @Excel(name = "券id", prompt = "券id", sort = 13)
    private Long couponId;
    @Excel(name = "券code", prompt = "券code", sort = 14)
    private String exchangeCode;

}
