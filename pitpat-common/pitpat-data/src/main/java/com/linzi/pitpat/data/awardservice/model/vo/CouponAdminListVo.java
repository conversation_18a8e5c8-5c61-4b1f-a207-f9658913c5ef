package com.linzi.pitpat.data.awardservice.model.vo;

import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@ToString
@Data
@NoArgsConstructor
public class CouponAdminListVo {

    private Long id;

    //创建时间
    private ZonedDateTime gmtCreate;
    //标题(卷名称)
    private String title;
    //运营自己看名称(备注)
    private String name;
    //券来源类型【1：刮刮卡券,2 排行榜,3活动获取,4 后台发放 】
    private Integer type;
    //券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券】
    private Integer couponType;
    /**
     * 卷内容
     */
    private String couponContent;
    /**
     * 卷有效期
     */
    private String couponValidityPeriod;
    //优惠券发放总数(库存) -1:不限制
    private Integer quota;
    //已经领取数量(已发放)
    private Integer quotaSend;
    // 剩余数量
    private Integer remainingNum;

    //1：已发布(启用) -1：已失效(禁用)
    private Integer status;
    /**
     * 每个人限制领取张数，-1不限制
     */
    private Integer limitCount;
    /**
     * 券金额
     */
    private BigDecimal amount;

    /**
     * 【4.4.3新增】兑换码
     */
    private String exchangeCode;

    /**
     * 【4.4.3新增】优惠方式，1：金额，2：折扣
     */
    private Integer discountMethod;

    /**
     * 【4.4.3新增】使用范围：1：全场通用 2：部分活动/商品，3：单品券(指定商品)
     *
     * @see CouponConstant.UseScopeEnum
     */
    private Integer useScope;

    /**
     * 国家码列表
     * @since 4.6.4
     */
    private List<String> countryCodeList;
}
