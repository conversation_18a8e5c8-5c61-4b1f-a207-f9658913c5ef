package com.linzi.pitpat.data.activityservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.activityservice.constant.enums.CompanionTaskType;
import com.linzi.pitpat.data.activityservice.mapper.CompanionTaskMapper;
import com.linzi.pitpat.data.activityservice.model.entity.CompanionTaskDo;
import com.linzi.pitpat.data.activityservice.model.query.CompanionTaskPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.CompanionTaskQuery;
import com.linzi.pitpat.data.activityservice.service.CompanionTaskService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 陪跑员任务表 服务实现类
 *
 * @since 2024-04-16
 */
@Slf4j
@Service
public class CompanionTaskServiceImpl implements CompanionTaskService {
    //使用构造器初始化依赖
    private CompanionTaskMapper companionTaskMapper;

    public CompanionTaskServiceImpl(CompanionTaskMapper companionTaskMapper) {
        this.companionTaskMapper = companionTaskMapper;
    }

    @Override
    public Long create(CompanionTaskDo companionTask) {
        //必要的业务检查
        int affectedRow = companionTaskMapper.insert(companionTask);
        log.info("创建陪跑员任务表,user={}, affected row={}", companionTask, affectedRow);
        return companionTask.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    @Caching(evict = {
            //@CacheEvict(value = UserCacheName.USER_KEY, key = "#companionTask.id"),
    })
    public Long update(CompanionTaskDo companionTask) {
        CompanionTaskDo existed = findById(companionTask.getId());
        if (Objects.isNull(existed)) {
            throw new BaseException("陪跑员任务表不存在");
        }
        int affectedRow = companionTaskMapper.updateById(companionTask);
        log.info("更新陪跑员任务表,user={}, affected row={}", companionTask, affectedRow);
        return companionTask.getId();
    }

    @Override
    public Long updateSelective(CompanionTaskQuery query, CompanionTaskDo companionTask) {
        LambdaUpdateWrapper<CompanionTaskDo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(Objects.nonNull(query.getId()), CompanionTaskDo::getId, query.getId())
                .setEntity(companionTask);
        int affectedRow = companionTaskMapper.update(updateWrapper);
        log.info("部分更新陪跑员任务表,user={}, affected row={}", companionTask, affectedRow);
        return companionTask.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@CacheEvict(value = UserCacheName.USER_KEY, key = "#id")
    public boolean deleteById(Long id) {
        CompanionTaskDo existed = findById(id);
        if (Objects.isNull(existed)) {
            throw new RuntimeException("陪跑员任务表不存在");
        }
        companionTaskMapper.deleteById(id);
        log.info("删除陪跑员任务表，id={}", id);
        return true;
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    public CompanionTaskDo findById(Long id) {
        return companionTaskMapper.selectById(id);
    }

    @Override
    public CompanionTaskDo findByQuery(CompanionTaskQuery query) {
        //TODO 不建议第二个参数设置为 false, 否则如果数据异常，难以发现
        return companionTaskMapper.selectOne(buildQueryWrapper(query), false);
    }

    @Override
    public List<CompanionTaskDo> findList(CompanionTaskQuery query) {
        Wrapper<CompanionTaskDo> wrapper = buildQueryWrapper(query);
        List<CompanionTaskDo> CompanionTasks = companionTaskMapper.selectList(wrapper);
        log.info("查询陪跑员任务表列表， query={}", query);
        return CompanionTasks;
    }

    @Override
    public Page<CompanionTaskDo> findPage(CompanionTaskPageQuery pageQuery) {
        Wrapper<CompanionTaskDo> queryWrapper = buildQueryWrapper(pageQuery);
        Page<CompanionTaskDo> result = companionTaskMapper.selectPage(PageHelper.ofPage(pageQuery), queryWrapper);
        log.info("查询陪跑员任务表列表， pageQuery={}", pageQuery);
        return result;
    }

    @Override
    public boolean batchCreate(List<CompanionTaskDo> companionTaskList) {
        //防止有多个未生效数据，先执行删除
        companionTaskMapper.delete(Wrappers.<CompanionTaskDo>lambdaQuery()
                .eq(CompanionTaskDo::getIsDelete, 0).ge(CompanionTaskDo::getGmtTakeEffect, ZonedDateTime.now()));
        String batchNumber = NanoId.randomNanoId();
        //生效时间，明天0点
        ZonedDateTime gmtTakeEffect = ZonedDateTime.now().plusDays(1).with(LocalTime.MIN);
        for (CompanionTaskDo companionTaskDo : companionTaskList) {
            companionTaskDo.setBatchNumber(batchNumber);
            String taskName = CompanionTaskType.getTaskName(companionTaskDo.getTaskType(), companionTaskDo.getFinshNum());
            companionTaskDo.setTaskName(taskName);
            companionTaskDo.setGmtTakeEffect(gmtTakeEffect);
            companionTaskMapper.insert(companionTaskDo);
        }
        log.info("批量更新陪跑员任务表列表， batchCreate={}", companionTaskList);
        return true;
    }

    @Override
    public List<CompanionTaskDo> findListByLastBatchNum() {
        CompanionTaskQuery companionTaskQuery = new CompanionTaskQuery();
        companionTaskQuery.addOrderByDesc("id");
        CompanionTaskDo taskDo = findByQuery(companionTaskQuery);
        if (Objects.isNull(taskDo) || taskDo.getIsDelete() == 1 || CompanionTaskType.NO_TASK.getType().equals(taskDo.getTaskType())) {
            return null;
        }

        return findList(new CompanionTaskQuery().setBatchNumber(taskDo.getBatchNumber()).setIsDelete(0));
    }

    @Override
    public List<CompanionTaskDo> findValidList() {
        CompanionTaskQuery companionTaskQuery = new CompanionTaskQuery().setMaxGmtTakeEffect(ZonedDateTime.now());
        companionTaskQuery.setIsDelete(0);
        companionTaskQuery.addOrderByDesc("id");
        CompanionTaskDo taskDo = findByQuery(companionTaskQuery);
        if (Objects.isNull(taskDo) || CompanionTaskType.NO_TASK.getType().equals(taskDo.getTaskType())) {
            return null;
        }

        return findList(new CompanionTaskQuery().setBatchNumber(taskDo.getBatchNumber()).setIsDelete(0));
    }

    @Override
    public String findTitle(CompanionTaskDo task, String languageCode) {
        String key = "companion.task.title.taskType" + task.getTaskType();
        return I18nMsgUtils.getLangMessage(languageCode, key, task.getFinshNum());
    }

    private static Wrapper<CompanionTaskDo> buildQueryWrapper(CompanionTaskQuery query) {
        return Wrappers.<CompanionTaskDo>lambdaQuery()
                .eq(Objects.nonNull(query.getIsDelete()), CompanionTaskDo::getIsDelete, query.getIsDelete())
                .eq(Objects.nonNull(query.getId()), CompanionTaskDo::getId, query.getId())
                .ge(Objects.nonNull(query.getMinGmtTakeEffect()), CompanionTaskDo::getGmtTakeEffect, query.getMinGmtTakeEffect())
                .le(Objects.nonNull(query.getMaxGmtTakeEffect()), CompanionTaskDo::getGmtTakeEffect, query.getMaxGmtTakeEffect())
                .eq(StringUtils.hasText(query.getBatchNumber()), CompanionTaskDo::getBatchNumber, query.getBatchNumber())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }

    private static Wrapper<CompanionTaskDo> buildQueryWrapper(CompanionTaskPageQuery query) {
        return Wrappers.<CompanionTaskDo>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), CompanionTaskDo::getId, query.getId())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }
}
