package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:03
 */
@Data
@NoArgsConstructor
public class MyRecordActRespDto {
    /**
     * 活动id
     */
    private Long id;
    /**
     * 队伍名称/活动名称
     */
    private String teamName;
    /**
     * 开始时间
     */
    private ZonedDateTime startTime;
    /**
     * 结束时间
     */
    private ZonedDateTime endTime;
    /**
     * 活动状态
     */
    private Integer activityState;
    /**
     * 活动类型，1：组队跑，2：挑战跑 3:排行赛 4：官方组队 5：累计跑 8主题赛事
     *
     * @see RunActivityTypeEnum
     */
    private Integer activityType;
}
