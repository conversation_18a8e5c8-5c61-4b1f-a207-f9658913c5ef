package com.linzi.pitpat.data.activityservice.manager.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonRankLikeCountBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.CompetitiveTabType;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.CompetitiveShortlistTypeEnum;
import com.linzi.pitpat.data.activityservice.converter.api.CompetitiveSeasonRankConverter;
import com.linzi.pitpat.data.activityservice.dto.BizCacheDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.CompetitiveSeasonActivityListReq;
import com.linzi.pitpat.data.activityservice.dto.api.response.BatchSignUpActivityResponseDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.CompetitiveDisseminatePromotionPicDto;
import com.linzi.pitpat.data.activityservice.model.dto.SeasonBonusPoolBonusDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveGameUserRankListDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveGameUserRankListItemDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveListDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveUserRankDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveUserRankListDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveBonusPoolDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveCountDownDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveDisseminateDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CoverButtonEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsCoverDetailDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserGroup;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveDisseminateDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveScoreConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonRankDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCompetitiveListQuery;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCompetitiveUserRankListQuery;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveSeasonRankPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.CompetitiveSeasonSystemDefaultConfig;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityHighlightsService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserGroupService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveDisseminateService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveScoreConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonRankLikeCountService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonRankService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveShortlistService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.SeasonBonusPoolService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.UserActivityClickRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.strategy.activity.ApiCompetitiveActivityDeviceManagerFactory;
import com.linzi.pitpat.data.activityservice.strategy.activity.ApiCompetitiveActivityDeviceManagerStrategy;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.VipStatusUserEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.systemservice.enums.ComponentParamKey;
import com.linzi.pitpat.data.systemservice.model.entity.CustomH5;
import com.linzi.pitpat.data.systemservice.service.CustomH5Service;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUser;
import com.linzi.pitpat.data.userservice.model.query.VipUserQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ApiCompetitiveActivityManager implements ApiCompetitiveActivityDeviceManagerStrategy, InitializingBean {

    private final UserGroupRelService userGroupRelService;

    private final MainActivityService mainActivityService;
    private final ActivityFeeService activityFeeService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ActivityDisseminateBizService activityDisseminateBizService;

    private final RotationAreaBizService rotationAreaBizService;

    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;

    private final PolymerizationActivityPoleService polymerizationActivityPoleService;

    private final ActivityParamsService activityParamsService;

    private final CompetitiveSeasonService competitiveSeasonService;

    private final CompetitiveScoreConfigService competitiveScoreConfigService;

    private final CompetitiveSeasonRankService competitiveSeasonRankService;

    private final CompetitiveSeasonRankConverter competitiveSeasonRankConverter;

    private final CompetitiveDisseminateService competitiveDisseminateService;

    private final ActivityUserGroupService activityUserGroupService;

    private final CustomH5Service customH5Service;

    private final SeasonBonusPoolService seasonBonusPoolService;

    private final ActivityHighlightsService activityHighlightsService;
    private final CompetitiveSeasonRankLikeCountService competitiveSeasonRankLikeCountService;
    private final CompetitiveSeasonRankLikeCountBizService competitiveSeasonRankLikeCountBizService;
    private final RedissonClient redissonClient;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserService userService;
    private final CompetitiveShortlistService competitiveShortlistService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final CompetitiveSeasonConfigService competitiveSeasonConfigService;
    private final CompetitiveSeasonConfigBizService competitiveSeasonConfigBizService;
    private final UserActivityClickRecordService userActivityClickRecordService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final SubActivityService subActivityService;
    private final EntryGameplayService entryPlayerService;
    private final GameplayService gamePlayService;

    private final ZnsUserAccountService userAccountService;
    private final VipUserService vipUserService;

    @Value("${pitpat.api.competitive.maxrank:110}")
    private Integer maxRank;

    private final static String IN_REPORT = "IN_REPORT";
    private final static String NOT_START = "NOT_START";
    private final static String IN_PROCESS_AND_CAN_REPORT = "IN_PROCESS_AND_CAN_REPORT";
    private final static String IN_PROCESS_AND_NOT_REPORT = "IN_PROCESS_AND_NOT_REPORT";
    private final static String END = "END";


    /**
     * 获取竞技赛的 月赛
     *
     * @return
     */
    public List<ActivityCompetitiveListDto> queryMonthlyCompetitiveActivity(ZnsUserEntity loginUser, String zoneId, String languageCode, boolean checkVersion, Integer appVersion) {
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());

        ZonedDateTime now = ZonedDateTime.now(timeZone.toZoneId());
        ZonedDateTime[] firstTwoMonth = ZonedDateTimeUtil.rangeOfMonth(now.minusMonths(2));
        ZonedDateTime[] firstMonth = ZonedDateTimeUtil.rangeOfMonth(now.minusMonths(1));
        ZonedDateTime[] currentMonth = ZonedDateTimeUtil.rangeOfMonth(now);
        ZonedDateTime[] nextMonth = ZonedDateTimeUtil.rangeOfMonth(now.plusMonths(1));
        ZonedDateTime[] nextTwoMonth = ZonedDateTimeUtil.rangeOfMonth(now.plusMonths(2));


        ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder = ActivityCompetitiveListQuery.builder()
                .competitiveSeasonType(ActivityCompetitiveSeasonType.MONTHLY.getCode())
                .isCompetitive(1)
                .isShowInMore(0)
                .activityType(List.of(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType()))
                .userGroups(groupsByUserId)
                .countryCode(loginUser.getCountryCode())
                .stateCode(loginUser.getStateCode())
                .activityStates(List.of(ActivityStateEnum.IN_PROGRESS.getState(), ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.FINISHED.getState()))
                .equipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType())
                .limit(20);
        List<MainActivity> activities;
        if (appVersion >= 4044) {
            queryBuilder.startDate(firstTwoMonth[0]).endDate(nextTwoMonth[1]).limit(1000);
            activities = mainActivityService.findCompetitiveActivityList(queryBuilder.build());
        } else {
            ActivityCompetitiveListQuery currentQuery = queryBuilder.startDate(currentMonth[0]).endDate(currentMonth[1]).build();
            ActivityCompetitiveListQuery preQuery = queryBuilder.startDate(firstMonth[0]).endDate(firstMonth[1]).build();
            ActivityCompetitiveListQuery nextQuery = queryBuilder.startDate(nextMonth[0]).endDate(nextMonth[1]).build();
            activities = mainActivityService.findCompetitiveActivityList(preQuery);
            activities.addAll(mainActivityService.findCompetitiveActivityList(currentQuery));
            activities.addAll(mainActivityService.findCompetitiveActivityList(nextQuery));
        }
        ArrayList<ActivityCompetitiveListDto> activityCompetitiveListDtos = fillAndSortActivity(loginUser, zoneId, languageCode, activities, checkVersion);
        activityCompetitiveListDtos.sort(ActivityCompetitiveListDto.getSeasonComparator(appVersion));
        return activityCompetitiveListDtos;
    }

    /**
     * 填充活动相关信息
     *
     * @param zoneId
     * @param languageCode
     * @param activities
     * @param checkVersion
     * @return
     */
    private ArrayList<ActivityCompetitiveListDto> fillAndSortActivity(ZnsUserEntity user, String zoneId, String languageCode, List<MainActivity> activities, boolean checkVersion) {
        if (CollectionUtils.isEmpty(activities)) {
            return Lists.newArrayList();
        }

        final List<Long> activityIds = activities.stream().map(MainActivity::getId).toList();
        activities = mainActivityService.findListByIds(activityIds);
        final List<CompetitiveSeasonDo> competitiveSeasonList = competitiveSeasonService.findByActivityIds(activityIds);
        final List<CompetitiveScoreConfigDo> competitiveScoreConfigDos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(competitiveSeasonList)) {
            List<Long> competitiveSeasonConfigIds = competitiveSeasonList.stream().map(CompetitiveSeasonDo::getScoreConfigId).distinct().toList();
            competitiveScoreConfigDos.clear();
            competitiveScoreConfigDos.addAll(competitiveScoreConfigService.findByIds(competitiveSeasonConfigIds));
        }

        List<ActivityHighlightsDo> validHighlightList = activityHighlightsService.getValidHighlightByActivityIds(activityIds);

        List<ActivityCompetitiveListDto> list = activities.parallelStream().map(mainActivity -> {
            //审核过滤
            if (filterPayActivity(mainActivity, checkVersion)) {
                return null;
            }
            ActivityCompetitiveListDto dto = fillBaseInfo(user, zoneId, languageCode, mainActivity);
            try {
                // 设置竞技赛信息
                if (mainActivity.getIsCompetitive() == 1) {
                    Optional<CompetitiveSeasonDo> byActivityId = competitiveSeasonList.stream().filter(item -> item.getActivityId().equals(dto.getActivityId())).findFirst();
                    CompetitiveSeasonDo competitiveSeasonDo = byActivityId.orElseThrow(() -> new BaseException("Not Found Config"));
                    dto.setCompetitiveSeasonType(competitiveSeasonDo.getCompetitiveSeasonType().getCode());
                    dto.setCompetitiveSeasonSubType(competitiveSeasonDo.getCompetitiveSeasonSubType() != null ? competitiveSeasonDo.getCompetitiveSeasonSubType().getCode() : null);
                    dto.setCompetitiveSeasonYear(competitiveSeasonDo.getCompetitiveSeasonYear());
                    Optional<CompetitiveScoreConfigDo> byId = competitiveScoreConfigDos.stream().filter(competitiveScoreConfigDo -> competitiveScoreConfigDo.getId().equals(competitiveSeasonDo.getScoreConfigId())).findFirst();
                    CompetitiveScoreConfigDo configDo = byId.orElse(new CompetitiveScoreConfigDo());
                    dto.setCompetitiveSeasonIconUrl(configDo.getScoreIcon());
                    //填充倒计时
                    activityParamsService.findOneByMainActivityAndParamType(dto.getActivityId(),
                                    ActivitySettingConfigEnum.COMPETITIVE_COUNT_DOWN_SETTING)
                            .ifPresent(k -> dto.setCompetitiveCountDownDto(JsonUtil.readValue(k.getParamValue(), CompetitiveCountDownDto.class)));

                    //填充前三位用户
                    dto.setUsernames(getPreUserName(mainActivity, competitiveSeasonDo, 3));
                    //填充是否is_new
                    Long start = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(), "UTC");
                    Long end = DateUtil.getStampByZone(mainActivity.getActivityEndTime(), "UTC");
                    if (start <= System.currentTimeMillis() && System.currentTimeMillis() <= end && user != null) {
                        dto.setIsNew(userActivityClickRecordService.hasClick(user.getId(), mainActivity.getId()) ? 0 : 1);
                    }

                }
            } catch (Exception e) {
                log.error("加载活动信息出错", e);
            }
            //设置发奖状态
            boolean activityAwardSendStatus = mainActivityService.getActivityAwardSendStatus(mainActivity);
            dto.setAwardSendFinish(activityAwardSendStatus ? 1 : 0);

            //设置集锦配置
            validHighlightList.stream().filter(a -> a.getActivityId().equals(mainActivity.getId())).findFirst()
                    .ifPresent(a -> {
                        if (activityAwardSendStatus) {
                            if (a.getState() == 1) {
                                dto.setHighlightsId(a.getId());
                            }
                        }
                        if (a.getState() == 1) {
                            dto.setCategoryDetails(a.getCategoryDetails());
                        }
                        dto.setHighlightState(a.getState());
                        ActivityHighlightsCoverDetailDo coverDetails = a.getCoverDetails();
                        if (coverDetails != null && a.getCoverState() == 1) {

                            Integer activityState = mainActivity.getActivityState();
                            MainActivityStateEnum stateEnum = MainActivityStateEnum.findByCode(activityState);
                            switch (stateEnum) {
                                case NOT_STARTED:
                                    Long appStartTimeStamp = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(), "UTC");
                                    if (System.currentTimeMillis() - appStartTimeStamp < 0) {
                                        dto.setHighlightsCoverDetail(coverDetails.getNotStartUrl());
                                    } else {
                                        dto.setHighlightsCoverDetail(coverDetails.getInReportUrl());
                                    }
                                    break;
                                case STARTED:
                                    dto.setHighlightsCoverDetail(coverDetails.getInProcessUrl());
                                    break;
                                case ENDED:
                                    dto.setHighlightsCoverDetail(coverDetails.getFinishUrl());
                                    break;
                            }
                        }
                    });

            //路由获取
            RotationArea route = rotationAreaBizService.getNewActivityRoute(dto.getActivityId(), mainActivity.getMainType(), 2);
            dto.setUrl(route.getUrl());
            dto.setJumpParam(route.getJumpParam());

            if (user != null) {
                dto.setNotReachCompetitionScoreThreshold(
                        competitiveSeasonBizService.checkReachCompetitionScoreThreshold(mainActivity.getId(), user.getId()) ?
                                0 : 1);
            }

            //设置按钮状态
            setButtonEnum(dto);
            return dto;
        }).filter(Objects::nonNull).toList();
        log.info("过滤后，list={}", CollectionUtils.isEmpty(list) ? 0 : list.size());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        ArrayList<ActivityCompetitiveListDto> result = new ArrayList<>(list);
        //result 中的元素工具activityIds 的位置进行排序
        result.sort((o1, o2) -> {
            int i = activityIds.indexOf(o1.getActivityId());
            int j = activityIds.indexOf(o2.getActivityId());
            return i - j;
        });
        if (user != null) {
            List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, user.getId());
            Map<Long, CompetitiveShortlistTypeEnum> userIdentity = competitiveShortlistService.getUserIdentity(activityIds, user.getId());
            result.stream().forEach(dto -> {
                //报名状态处理
                dto.setUserState(activityUsers.stream().anyMatch(activityUser -> activityUser.getActivityId().equals(dto.getActivityId())) ? 1 : 0);
                //用户身份
                dto.setIsForeign(CompetitiveShortlistTypeEnum.WILD_CARD.equals(
                        userIdentity.get(dto.getActivityId())) ? 1 : 0);

            });
        }
        return result;
    }

    @NotNull
    private ActivityCompetitiveListDto fillBaseInfo(ZnsUserEntity user, String zoneId, String languageCode, MainActivity mainActivity) {
        ActivityCompetitiveListDto dto = new ActivityCompetitiveListDto();
        dto.setMainActivity(mainActivity);
        dto.setGmtCreate(mainActivity.getGmtCreate());
        dto.setEquipmentMainType(mainActivity.getEquipmentMainType());
        dto.setActivityId(mainActivity.getId());
        dto.setMainType(mainActivity.getMainType());
        ZonedDateTime startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        dto.setActivityState(mainActivity.getActivityState());
        dto.setIsMemberActivity(mainActivity.getIsMemberActivity());
        //设置活动时间
        if (mainActivity.getTimeStyle() == 0) {
            dto.setActivityStartTime(startTime);
            dto.setActivityEndTime(endTime);
        } else {
            dto.setActivityStartTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), zoneId)));
            dto.setActivityEndTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), zoneId)));
            //各时区判断
            dto.setActivityState(mainActivityService.getActivityState(mainActivity, zoneId));
        }
        if (user == null) {
            ZnsUserEntity newUser = new ZnsUserEntity();
            newUser.setZoneId(zoneId);
            dto.setActivityState(mainActivityService.getActivityState(mainActivity, newUser));
        } else {
            dto.setActivityState(mainActivityService.getActivityState(mainActivity, user));
        }

        dto.setApplicationStartTime(DateTimeUtil.parse(mainActivity.getApplicationStartTime()));
        dto.setApplicationEndTime(DateTimeUtil.parse(mainActivity.getApplicationEndTime()));
        //聚合活动处理
        if (MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(mainActivity.getMainType())
                || MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType())) {
            ActivityPolymerizationRecord polymerizationRecord = activityPolymerizationRecordService.findByActivityId(mainActivity.getId());
            if (Objects.nonNull(polymerizationRecord)) {
                PolymerizationActivityPole pole = polymerizationActivityPoleService.findById(polymerizationRecord.getPolyId());
                if (Objects.nonNull(pole)) {
                    dto.setActivityStartTime(pole.getTaskStartTime());
                    dto.setActivityEndTime(pole.getTaskEndTime());
                }
            }
        }

        //设置图片、标题
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
        if (Objects.nonNull(disseminate)) {
            dto.setActivityCoverImage(disseminate.getDisseminatePics());
            if (I18nConstant.LanguageCodeEnum.en_US.getCode().equals(disseminate.getLanguageCode())) {
                //英语显示大写
                dto.setActivityTitle(disseminate.getTitle().toUpperCase());
            } else {
                dto.setActivityTitle(disseminate.getTitle());
            }
        }
        return dto;
    }

    private void setButtonEnum(ActivityCompetitiveListDto dto) {
        ZonedDateTime now = ZonedDateTime.now();
        String competitiveSeasonType = dto.getCompetitiveSeasonType();
        Boolean enableHighlight = dto.getHighlightState() == 1;

        Integer activityState = dto.getMainActivity().getActivityState();
        String finalActivityState = NOT_START;
        if (activityState == 0) {
            if (now.compareTo(dto.getApplicationStartTime()) < 0 || now.compareTo(dto.getApplicationEndTime()) > 0) {
                //未开始
                finalActivityState = NOT_START;
            } else {
                //报名中
                finalActivityState = IN_REPORT;

            }

        } else if (activityState == 1) {
            if (now.compareTo(dto.getApplicationEndTime()) < 0) {
                //进行中，未过报名时间段
                finalActivityState = IN_PROCESS_AND_CAN_REPORT;
            } else {
                //进行中，已过报名时间段
                finalActivityState = IN_PROCESS_AND_NOT_REPORT;
            }
        } else if (activityState == 2) {
            finalActivityState = END;
        }
        Integer userState = dto.getUserState();


        //赛事     是否配置赛事集锦（并生效）    赛事状态      用户报名状态  封面按钮（小：则为封面上按钮）

        //月/季/年赛   是    未开始   未报名  未报名集锦（小），赛事详情
        if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && enableHighlight
                && NOT_START.equals(finalActivityState)
                && userState == 0) {
            dto.setButton(Arrays.asList(CoverButtonEnum.SMALL_HIGHLIGHTS, CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛   是    报名中 未报名 未报名集锦（小），立即报名
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && enableHighlight
                && IN_REPORT.equals(finalActivityState)
                && userState == 0) {
            dto.setButton(Arrays.asList(CoverButtonEnum.SMALL_HIGHLIGHTS, CoverButtonEnum.REGISTER));
            //月/季/年赛   是 报名中 已报名 未报名集锦（小），赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && enableHighlight
                && IN_REPORT.equals(finalActivityState)
                && userState == 1) {
            dto.setButton(Arrays.asList(CoverButtonEnum.SMALL_HIGHLIGHTS, CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛   是    未报名    进行中，已过报名时间段 赛事集锦
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && enableHighlight
                && IN_PROCESS_AND_NOT_REPORT.equals(finalActivityState)
                && userState == 0) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_HIGHLIGHTS));
            //月/季/年赛   是    进行中，未过报名时间段   未报名    集锦（小），立即报名
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && enableHighlight
                && IN_PROCESS_AND_CAN_REPORT.equals(finalActivityState)
                && userState == 0) {
            dto.setButton(Arrays.asList(CoverButtonEnum.SMALL_HIGHLIGHTS, CoverButtonEnum.REGISTER));
            //月/季/年赛   是    进行中   已报名    集锦（小），赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && enableHighlight
                && (IN_PROCESS_AND_CAN_REPORT.equals(finalActivityState) || IN_PROCESS_AND_NOT_REPORT.equals(finalActivityState))
                && userState == 1) {
            dto.setButton(Arrays.asList(CoverButtonEnum.SMALL_HIGHLIGHTS, CoverButtonEnum.ACTIVITY_DETAIL));
            //月赛   是    已结束   未报名    赛事集锦
        } else if (ActivityCompetitiveSeasonType.MONTHLY.getCode().equals(competitiveSeasonType)
                && enableHighlight
                && END.equals(finalActivityState)
                && userState == 0) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_HIGHLIGHTS));
            //月赛   是    已结束   已报名    赛事集锦
        } else if (ActivityCompetitiveSeasonType.MONTHLY.getCode().equals(competitiveSeasonType)
                && enableHighlight
                && END.equals(finalActivityState)
                && userState == 1) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_HIGHLIGHTS));
            //季/年赛  是    已结束   未报名/已报名    “集锦”字段，以及集锦下的类别
        } else if (ActivityCompetitiveSeasonType.isAnnualOrSeasonal(competitiveSeasonType)
                && enableHighlight
                && END.equals(finalActivityState)
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.HIGHLIGHTS_ALL_CATEGORY));
            //月/季/年赛  否   未开始   未报名    赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && NOT_START.equals(finalActivityState)
                && userState == 0
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛  否   报名中  未报名    立即报名
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && IN_REPORT.equals(finalActivityState)
                && userState == 0
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.REGISTER));
            //月/季/年赛  否   报名中  已报名    赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && IN_REPORT.equals(finalActivityState)
                && userState == 1
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛  否   进行中，已过报名时间段  未报名    赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && IN_PROCESS_AND_NOT_REPORT.equals(finalActivityState)
                && userState == 0
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛  否   进行中，未过报名时间段  未报名    立即报名
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && IN_PROCESS_AND_CAN_REPORT.equals(finalActivityState)
                && userState == 0
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.REGISTER));
            //月/季/年赛  否   进行中   已报名    赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && (IN_PROCESS_AND_CAN_REPORT.equals(finalActivityState) || IN_PROCESS_AND_NOT_REPORT.equals(finalActivityState))
                && userState == 1
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛  否   已结束   未报名    赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && (END.equals(finalActivityState))
                && userState == 0
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_DETAIL));
            //月/季/年赛  否   已结束   已报名    赛事详情
        } else if (ActivityCompetitiveSeasonType.isCompetitive(competitiveSeasonType)
                && !enableHighlight
                && (END.equals(finalActivityState))
                && userState == 1
        ) {
            dto.setButton(Arrays.asList(CoverButtonEnum.ACTIVITY_DETAIL));
        }


    }

    private List<String> getPreUserName(MainActivity mainActivity, CompetitiveSeasonDo competitiveSeasonDo, int num) {
        Long activityId = mainActivity.getId();
        if (!((mainActivity.getAwardSendStatus() == -1 || mainActivity.getAwardSendStatus() == 1)
                && MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState()))) {
            return null;
        }

        if (competitiveSeasonDo == null) {
            return null;
        }
        RBucket<BizCacheDto<List<String>>> cacheBucket = redissonClient.getBucket("ApiCompetitiveActivityManager:getPreUserName:" + mainActivity.getId() + ":" + competitiveSeasonDo.getId() + ":" + num);
        if (cacheBucket.isExists()) {
            BizCacheDto<List<String>> cacheDto = cacheBucket.get();
            if (cacheDto != null) {
                return cacheDto.getData();
            }
        }
        RunActivityUserQuery query = RunActivityUserQuery.builder().activityId(mainActivity.getId()).minRank(1).maxRank(num).build();
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findActivityUserByQuery(query);
        List<String> collect = list.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRank)).limit(num).
                map(k -> {
                    ZnsUserEntity user = userService.findById(k.getUserId());
                    if (user != null) {
                        return user.getFirstName();
                    } else {
                        return null;
                    }
                }).collect(Collectors.toList());
        cacheBucket.set(BizCacheDto.create(collect));
        cacheBucket.expire(5, TimeUnit.MINUTES);
        return collect;

    }

    private boolean filterPayActivity(MainActivity mainActivity, boolean checkVersion) {
        if (!checkVersion) {
            return false;
        }
        List<ActivityFee> feeList = activityFeeService.findByActId(mainActivity.getId());
        if (CollectionUtils.isEmpty(feeList)) {
            return false;
        }

        for (ActivityFee activityFee : feeList) {
            if ("free".equals(activityFee.getType()) || "score".equals(activityFee.getType())) {
                continue;
            }
            if (activityFee.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
        }

        //todo 临时方案，过滤全量用户展示的活动
        List<ActivityUserGroup> groupList = activityUserGroupService.findByActId(mainActivity.getId());
        if (CollectionUtils.isEmpty(groupList)) {
            return true;
        }
        return false;
    }

    /**
     * 获取竞技赛的 季度
     *
     * @return
     */
    @Override
    public ActivityCompetitiveListDto querySeasonalCompetitiveActivity(ZnsUserEntity loginUser, String zoneId, String languageCode, Integer appVersion) {
        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());

        ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder = ActivityCompetitiveListQuery.builder()
                .competitiveSeasonType(ActivityCompetitiveSeasonType.SEASONAL.getCode())
                .isCompetitive(1)
                .countDownSwitch(true)
                .isShowInMore(0)
                .activityType(List.of(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType()))
                .userGroups(groupsByUserId)
                .countryCode(loginUser.getCountryCode())
                .stateCode(loginUser.getStateCode())
                .activityStates(List.of(ActivityStateEnum.NOT_START.getState()))
                .limit(1);
        if (appVersion < 4043) {
            setLess4043QueryCondition(queryBuilder);
        }
        List<MainActivity> activities = mainActivityService.findCompetitiveActivityList(queryBuilder.build());
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        return fillAndSortActivity(loginUser, zoneId, languageCode, activities, false).get(0);
    }

    @Override
    public List<ActivityCompetitiveListDto> querySeasonalCompetitiveActivityList(ZnsUserEntity loginUser, String zoneId, String languageCode, CompetitiveSeasonActivityListReq req) {
        List<Long> groupsByUserId = null;
        if (loginUser != null) {
            //查询用户所属人群
            groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());
        }

        ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder = ActivityCompetitiveListQuery.builder()
                .competitiveSeasonType(req.getSeasonType().getCode())
                .seasonId(req.getSeasonId())
                .competitiveSeasonType(Objects.nonNull(req.getSeasonType()) ? req.getSeasonType().getCode() : null)
                .isCompetitive(1)
                .isShowInMore(0)
                .activityType(List.of(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType()))
                .userGroups(groupsByUserId)
                .activityStates(List.of(ActivityStateEnum.IN_PROGRESS.getState(), ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.FINISHED.getState()));
        if (loginUser != null) {
            queryBuilder.countryCode(loginUser.getCountryCode())
                    .stateCode(loginUser.getStateCode());
        }

        List<MainActivity> activities = mainActivityService.findCompetitiveActivityList(queryBuilder.build());
        return fillAndSortActivity(loginUser, zoneId, languageCode, activities, false);
    }

    /**
     * 获取竞技赛的 年度
     *
     * @return
     */
    public ActivityCompetitiveListDto queryAnnualCompetitiveActivity(ZnsUserEntity loginUser, String zoneId, String languageCode, Integer appVersion) {
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());
        //配置了倒计时的，开始时间最早的未开始的活动。
        ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder = ActivityCompetitiveListQuery.builder()
                .competitiveSeasonType(ActivityCompetitiveSeasonType.ANNUAL.getCode())
                .isCompetitive(1)
                .countDownSwitch(true)
                .isShowInMore(0)
                .activityType(List.of(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType()))
                .userGroups(groupsByUserId)
                .countryCode(loginUser.getCountryCode())
                .stateCode(loginUser.getStateCode())
                .activityStates(List.of(ActivityStateEnum.NOT_START.getState()))
                .limit(1);
        if (appVersion < 4043) {
            setLess4043QueryCondition(queryBuilder);
        }
        List<MainActivity> activities = mainActivityService.findCompetitiveActivityList(queryBuilder.build());
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        return fillAndSortActivity(loginUser, zoneId, languageCode, activities, false).get(0);
    }

    private void setLess4043QueryCondition(ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder) {
        CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
        Long seasonActivitySeasonRankId = competitiveSeasonSystemDefaultConfig.getSeasonActivitySeasonRankId();
        queryBuilder.seasonId(seasonActivitySeasonRankId);
        queryBuilder.countDownSwitch(null);
        queryBuilder.activityStates(List.of(ActivityStateEnum.IN_PROGRESS.getState(), ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.FINISHED.getState()));
    }

    /**
     * 展示在更多中
     *
     * @return
     */
    public List<ActivityCompetitiveListDto> queryShowInMoreActivity(ZnsUserEntity loginUser, String zoneId, String languageCode, boolean checkVersion, Integer equipmentMainType, Integer appVersion) {
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        String minActivityEndTimeLocalDateStr = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId), DateUtil.DATE_TIME_SHORT);

        ZonedDateTime now = ZonedDateTime.now(timeZone.toZoneId());
        ZonedDateTime[] firstTwoMonth = ZonedDateTimeUtil.rangeOfMonth(now.minusMonths(2));
        ZonedDateTime[] nextTwoMonth = ZonedDateTimeUtil.rangeOfMonth(now.plusMonths(2));
        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());

        ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder = ActivityCompetitiveListQuery.builder()
                .isCompetitive(0)
                .isShowInMore(1)
                .activityType(List.of(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()))
                .userGroups(groupsByUserId)
                .countryCode(loginUser.getCountryCode())
                .stateCode(loginUser.getStateCode())
                .activityStates(List.of(ActivityStateEnum.IN_PROGRESS.getState(), ActivityStateEnum.NOT_START.getState()))
                .areaCheck(StringUtils.hasText(loginUser.getCountryCode()))
                .minActivityEndTimeLocalDateStr(minActivityEndTimeLocalDateStr)
                .minActivityEndTimeStr(DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), TimeZone.getDefault(), DateUtil.DATE_TIME_SHORT))
                .equipmentMainType(equipmentMainType)
                .limit(20);
        if (appVersion >= 4044) {
            queryBuilder.minActivityEndTimeLocalDateStr(null)
                    .minActivityEndTimeStr(null)
                    .activityStates(List.of(ActivityStateEnum.IN_PROGRESS.getState(), ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.FINISHED.getState()))
                    .startDate(firstTwoMonth[0]).endDate(nextTwoMonth[1])
                    .limit(1000);
        }
        List<MainActivity> activities = mainActivityService.findCompetitiveActivityList(queryBuilder.build());
        ArrayList<ActivityCompetitiveListDto> activityCompetitiveListDtos = fillAndSortActivity(loginUser, zoneId, languageCode, activities, checkVersion);
        activityCompetitiveListDtos.sort(ActivityCompetitiveListDto.getShowInMoreComparator(appVersion));
        return activityCompetitiveListDtos;
    }

    public Integer canBatchSignUp(ZnsUserEntity loginUser, String zoneId, boolean checkVersion, Integer equipmentMainType, Long mainActivityId) {
        //需求变更，不给这个标识了
//        BatchSignUpActivityResponseDto batchSignUpActivityResponseDto = queryBatchSignUpShowInfoMoreActivity(loginUser, zoneId, loginUser.getLanguageCode(), checkVersion, equipmentMainType,mainActivityId!=null?List.of(mainActivityId):null);
//        if (Objects.equals(equipmentMainType, DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType())) {
//            return (CollectionUtils.isEmpty(batchSignUpActivityResponseDto.getRunning()) ? 0 : 1);
//        } else if (Objects.equals(equipmentMainType, DeviceConstant.EquipmentMainTypeEnum.BIKE.getType())) {
//            return (CollectionUtils.isEmpty(batchSignUpActivityResponseDto.getCycling()) ? 0 : 1);
//        } else if (Objects.equals(equipmentMainType, DeviceConstant.EquipmentMainTypeEnum.ROWING.getType())) {
//            return (CollectionUtils.isEmpty(batchSignUpActivityResponseDto.getRowing()) ? 0 : 1);
//        }
        return 0;
    }

    /**
     * 查询可用批量报名的 显示在更多的活动。
     *
     * @param loginUser
     * @param zoneId
     * @param languageCode
     * @param equipmentMainType DeviceConstant.EquipmentMainTypeEnum
     * @return
     */
    @FillerMethod
    public BatchSignUpActivityResponseDto queryBatchSignUpShowInfoMoreActivity(ZnsUserEntity loginUser, String zoneId, String languageCode, boolean checkVersion, Integer equipmentMainType, List<Long> mainActivityIds) {
//        包含后台配置的更多赛事，且配置为全球，上架并处于报名中的赛事；不包含团赛，配置互斥的赛事，用户已报名的赛事，多目标的赛事，玩法报名配置了限制次数的赛事，该用户在赛事的黑名单的赛事，该用户不在赛事的白名单的赛事，配置非全球的赛事
        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(loginUser.getId());
        String minApplicationEndTimeLocalDateStr = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId), DateUtil.DATE_TIME_SHORT);
        ActivityCompetitiveListQuery.ActivityCompetitiveListQueryBuilder queryBuilder = ActivityCompetitiveListQuery.builder()
                .isCompetitive(0)
                // 包含后台配置的更多赛事，
                .isShowInMore(1)
                .activityType(List.of(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()))
                //全球
                .areaCheck(true)
                .countryCode(null).stateCode(null)
                .mainActivityIds(mainActivityIds)
                .equipmentMainType(equipmentMainType)
                //上架并处于报名中的赛事
                .activityStates(List.of(ActivityStateEnum.IN_PROGRESS.getState(), ActivityStateEnum.NOT_START.getState()))
                .minApplicationEndTimeLocalDateStr(minApplicationEndTimeLocalDateStr)
                .minApplicationEndTimeStr(DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), TimeZone.getDefault(), DateUtil.DATE_TIME_SHORT))
                //equipment Running，Cycling，Rowing）下进入
                .equipmentMainTypes(List.of(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType(),
                        DeviceConstant.EquipmentMainTypeEnum.BIKE.getType(),
                        DeviceConstant.EquipmentMainTypeEnum.ROWING.getType()))
                //该用户在赛事的黑名单的赛事，该用户不在赛事的白名单的赛事
                .userGroups(groupsByUserId)

                //配置互斥的赛事
                .noneMutexActivityIds(true)
                .limit(500);
        List<MainActivity> activities = mainActivityService.findCompetitiveActivityList(queryBuilder.build());
        log.info("[批量报名过滤]：{}", activities.stream().map(MainActivity::getId).toList());
        //过滤不能报名的活动
        activities = activities.stream().filter(a -> a.canSignUp(zoneId)).toList();
        log.info("[批量报名过滤]：报名时间。{}", activities.stream().map(MainActivity::getId).toList());
        //        不包含团赛，玩法报名配置了限制次数的赛事,
        List<Long> playIds = activities.stream().map(MainActivity::getPlayId).distinct().toList();
        List<Long> limitedPlayIds = gamePlayService.haveSignUpLimitGameplay(playIds);
        activities = activities.stream().filter(a -> !limitedPlayIds.contains(a.getPlayId())).toList();
        log.info("[批量报名过滤]：玩法。{}", activities.stream().map(MainActivity::getId).toList());
//       当前只有单赛事存 移除团赛
        List<Long> singlePlayId = activities.stream().filter(a -> !a.isSeriesMain()).map(MainActivity::getPlayId).distinct().toList();
        final List<Long> teamGameplay = entryPlayerService.teamGameplay(singlePlayId);
        activities = activities.stream().filter(a -> !teamGameplay.contains(a.getPlayId())).toList();
        log.info("[批量报名过滤]：团赛。{}", activities.stream().map(MainActivity::getId).toList());
        //       只有单赛事是才有多目标， 多目标的赛事，
        List<Long> singleActivityIds = activities.stream().filter(a -> !a.isSeriesMain()).map(MainActivity::getId).toList();
        List<Long> mulitTargetActivityids = subActivityService.haveMulitTargetActivity(singleActivityIds);
        activities = activities.stream().filter(a -> !mulitTargetActivityids.contains(a.getId())).toList();
        log.info("[批量报名过滤]：多目标。{}", activities.stream().map(MainActivity::getId).toList());
        // 过滤允许使用优惠券的活动
        activities = activities.stream().filter(a -> a.getAllowCoupon() != 1).toList();
        log.info("[批量报名过滤]：不允许使用优惠券。{}", activities.stream().map(MainActivity::getId).toList());
//        用户已报名的赛事，
        List<Long> signedUpActivity = znsRunActivityUserService.findActivityUsers(activities.stream().map(MainActivity::getId).toList(), loginUser.getId()).stream().map(ZnsRunActivityUserEntity::getActivityId).toList();
        List<MainActivity> finalActivity = activities.stream().filter(a -> !signedUpActivity.contains(a.getId())).toList();
        log.info("[批量报名过滤]：已报名。{}", activities.stream().map(MainActivity::getId).toList());
        BatchSignUpActivityResponseDto result = new BatchSignUpActivityResponseDto();
        VipUserQuery query = VipUserQuery.builder().userId(loginUser.getId())
                .memberType(1)
                .vipStatus(VipStatusUserEnum.ACTIVATED.getCode())
                .build();
        VipUser vipUser = vipUserService.findByQuery(query);
        if (vipUser != null) {
            result.setMemberType(vipUser.getMemberType());
            result.setMemberExpireTime(vipUser.getVipEndtime());
            result.setIsFreeUsedForMonth(vipUser.getIsTrialVip() != null);
        } else {
            result.setMemberType(0);
        }
        result.setUserId(loginUser.getId());
        for (MainActivity mainActivity : finalActivity) {
            //审核过滤
            if (filterPayActivity(mainActivity, checkVersion)) {
                continue;
            }
            result.addActivity(mainActivity, languageCode, zoneId, loginUser.getId());
        }
        return result;
    }

    public ActivityCompetitiveUserRankDto userRankScore(ZnsUserEntity loginUser) {
        ActivityCompetitiveUserRankDto dto = new ActivityCompetitiveUserRankDto();
        dto.setUserId(loginUser.getId());
        dto.setHeadPortrait(loginUser.getHeadPortrait());
        dto.setAnnualRank(0);
        dto.setAnnualScore(0);
        CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
        Long annualRankId = competitiveSeasonSystemDefaultConfig.getAnnualRankId();
        if (Objects.nonNull(annualRankId)) {
            CompetitiveSeasonRankDo annualRank = competitiveSeasonRankService.getUserCurrentRank(loginUser.getId(), annualRankId);
            if (annualRank != null) {
                dto.setAnnualRank(annualRank.getSeasonRank());
                dto.setAnnualScore(annualRank.getSeasonScore());
            }
        }
        Long seasonRankId = competitiveSeasonSystemDefaultConfig.getSeasonRankId();
        if (Objects.nonNull(seasonRankId)) {
            CompetitiveSeasonRankDo seasonRank = competitiveSeasonRankService.getUserCurrentRank(loginUser.getId(), seasonRankId);
            if (seasonRank != null) {
                dto.setBonusSeasonRank(seasonRank.getBonusSeasonRank());
                dto.setSeasonBonus(seasonRank.getSeasonBonus());
            }
        }
        return dto;
    }

    @FillerMethod
    public ActivityCompetitiveUserRankListDto userRank(ActivityCompetitiveUserRankListQuery query) {
        if (Objects.isNull(query.getSeasonId())) {
            CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
            if (ActivityCompetitiveSeasonType.ANNUAL.equals(query.getSeasonType())) {
                query.setSeasonId(competitiveSeasonSystemDefaultConfig.getAnnualRankId());
            } else {
                query.setSeasonId(competitiveSeasonSystemDefaultConfig.getSeasonRankId());
            }
        }
        if (Objects.isNull(query.getTabType())) {
            query.setTabType(CompetitiveTabType.SCORE);
        }
        Long seasonId = query.getSeasonId();
        CompetitiveSeasonRankPageQuery build = CompetitiveSeasonRankPageQuery.builder()
                .seasonId(seasonId).version(1)
                .build();
        build.setPageNum(query.getPageNum());
        build.setPageSize(query.getPageSize());
        if (CompetitiveTabType.SCORE.equals(query.getTabType())) {
            build.setSeasonScoreGt(0);
            build.setOrders(List.of(OrderItem.asc("season_rank"), OrderItem.desc("rank_top10_percent"), OrderItem.asc("id")));
        } else {
            build.setSeasonBonusGt(new BigDecimal(0));
            build.setOrders(List.of(OrderItem.asc("bonus_season_rank"), OrderItem.desc("average_season_bonus"), OrderItem.asc("id")));
        }
        Page<CompetitiveSeasonRankDo> page = competitiveSeasonRankService.findPage(build);
        ActivityCompetitiveUserRankListDto dto = new ActivityCompetitiveUserRankListDto();
        dto.setRankList(competitiveSeasonRankConverter.toRankPage(page));
        if (!CollectionUtils.isEmpty(dto.getRankList().getRecords()) && Objects.nonNull(query.getUserId())) {
            dto.getRankList().getRecords().stream().filter(item -> item.getUserId().equals(query.getUserId())).forEach(a -> {
                a.setIsMe(1);
            });
        }
        competitiveSeasonConfigService.findBySeasonId(query.getSeasonId()).ifPresent(dto::setSeasonConfigDo);
        return dto;
    }

    @Override
    public CompetitiveDisseminateDto queryCompetitiveDisseminate(ZnsUserEntity loginUser, String languageCode, ActivityCompetitiveSeasonType seasonType) {
        CompetitiveDisseminateDto dto = new CompetitiveDisseminateDto();
        //如果是季赛或者年赛，填充宣传图和倒计时信息
        CompetitiveDisseminateDo disseminateDo = competitiveDisseminateService.findBySeasonType(seasonType.getCode());
        if (disseminateDo != null) {
            List<CompetitiveDisseminatePromotionPicDto> picDtos = JsonUtil.readList(disseminateDo.getMultipleDisseminateSetting(), CompetitiveDisseminatePromotionPicDto.class);
            CompetitiveDisseminatePromotionPicDto competitiveDisseminatePromotionPicDto = picDtos.stream().filter(k -> k.getLanguageCode().equals(languageCode) && StringUtils.hasText(k.getPromotionalPic())).findFirst().orElse(
                    picDtos.stream().filter(k -> k.getLanguageCode().equals(disseminateDo.getDefaultLangCode())).findFirst().get()
            );
            dto.setPromotionalPic(competitiveDisseminatePromotionPicDto.getPromotionalPic());
            dto.setEventJumpTarget(disseminateDo.getJumpType());
            dto.setJumpUrl(disseminateDo.getJumpUrl());
            switch (dto.getEventJumpTarget()) {
                case EVENT_H5 -> {
                    //获取h5使用的奖金池信息
                    Long h5ConfigId = disseminateDo.getH5ConfigId();
                    queryH5BonusPoolsAmount(h5ConfigId, dto);
                }
                case EVENT_LIST -> {
                    CompetitiveSeasonActivityListReq req = new CompetitiveSeasonActivityListReq();
                    req.setSeasonType(seasonType);
                    List<ActivityCompetitiveListDto> activityCompetitiveListDtos = querySeasonalCompetitiveActivityList(loginUser, loginUser.getZoneId(), languageCode, req);
                    if (CollectionUtils.isEmpty(activityCompetitiveListDtos)) {
                        dto.setPendingSignUpActivityCount(0L);
                        dto.setRunningActivityCount(0L);
                    } else {
                        dto.setRunningActivityCount(activityCompetitiveListDtos.stream().filter(a -> ActivityStateEnum.IN_PROGRESS.getState().equals(a.getActivityState())).count());
                        dto.setPendingSignUpActivityCount(activityCompetitiveListDtos.stream()
                                .filter(ActivityCompetitiveListDto::isUnRegisterActivity)
                                .count());
                    }

                }
            }

        }
        return dto;
    }

    /**
     * 设置奖金池的金额
     *
     * @param h5ConfigId
     * @param dto
     */
    private void queryH5BonusPoolsAmount(Long h5ConfigId, CompetitiveDisseminateDto dto) {

        if (h5ConfigId != null) {
            CustomH5 customH5 = customH5Service.selectById(h5ConfigId);
            if (customH5 != null) {
                String componentParam = customH5.getComponentParam(ComponentParamKey.BONUS_POOL_ID);
                if (StringUtils.hasText(componentParam)) {
                    try {
                        SeasonBonusPoolBonusDto bonus = seasonBonusPoolService.getBonus(Long.parseLong(componentParam));
                        dto.setBonusPoolUpdateKey(bonus.getKey());
                        dto.setBonusPoolAmount(bonus.getBonusAmount());
                        dto.setHaveBonusPool(1);
                    } catch (Exception e) {
                        log.error("bonus pool id is not number;{},h5id:{}", componentParam, customH5.getId());
                        dto.setHaveBonusPool(0);
                    }

                } else {
                    dto.setHaveBonusPool(0);
                }
            }
        }
    }

    public CompetitiveBonusPoolDto getBonusPoolAmount() {
        CompetitiveBonusPoolDto result = new CompetitiveBonusPoolDto();
        //查询当前配置了奖金池的金额
        CompetitiveDisseminateDo seasonCode = competitiveDisseminateService.findBySeasonType(ActivityCompetitiveSeasonType.SEASONAL.getCode());
        if (seasonCode != null) {
            CompetitiveDisseminateDto seasonDTO = new CompetitiveDisseminateDto();
            queryH5BonusPoolsAmount(seasonCode.getH5ConfigId(), seasonDTO);
            if (Integer.valueOf(1).equals(seasonDTO.getHaveBonusPool())) {
                result.setSeasonBonusPoolAmount(seasonDTO.getBonusPoolAmount());
            }
        }
        //查询当前配置了奖金池的金额
        CompetitiveDisseminateDo annualCode = competitiveDisseminateService.findBySeasonType(ActivityCompetitiveSeasonType.ANNUAL.getCode());
        if (annualCode != null) {
            CompetitiveDisseminateDto seasonDTO = new CompetitiveDisseminateDto();
            queryH5BonusPoolsAmount(annualCode.getH5ConfigId(), seasonDTO);
            if (Integer.valueOf(1).equals(seasonDTO.getHaveBonusPool())) {
                result.setAnnualBonusPoolAmount(seasonDTO.getBonusPoolAmount());
            }
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ApiCompetitiveActivityDeviceManagerFactory.register(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType(), this);
    }

    @FillerMethod
    public ActivityCompetitiveGameUserRankListDto userRank(Long activityId, String languageCode, Long userId) {
        if (Objects.isNull(activityId) || activityId <= 1) {
            return null;
        }
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (mainActivity.getMainType().equals(MainActivityTypeEnum.SERIES_SUB.getType())) {
            activityId = seriesActivityRelService.getMainActivityBySegmentActId(activityId).getId();
        }
        Optional<CompetitiveSeasonConfigDo> seasonByActivityToShowInGame = competitiveSeasonRankLikeCountBizService.findSeasonByActivityToShowInGame(activityId);
        if (seasonByActivityToShowInGame.isEmpty()) {
            return null;
        }
        CompetitiveSeasonConfigDo competitiveSeasonConfigDo = seasonByActivityToShowInGame.get();
        Long seasonId = competitiveSeasonConfigDo.getSeasonId();
        CompetitiveSeasonRankPageQuery build = CompetitiveSeasonRankPageQuery.builder()
                .seasonId(seasonId).version(1)
                .maxRank(8)
                .build();
        build.setPageSize(8);
        build.setOrders(List.of(OrderItem.asc("season_rank"), OrderItem.asc("id")));
        Page<CompetitiveSeasonRankDo> page = competitiveSeasonRankService.findPage(build);
        ActivityCompetitiveGameUserRankListDto dto = new ActivityCompetitiveGameUserRankListDto();
        dto.setSeasonId(seasonId);
        List<ActivityCompetitiveGameUserRankListItemDto> activityCompetitiveGameUserRankListItemDtos = competitiveSeasonRankConverter.toGameRankList(page.getRecords());
        Map<Object, Object> map = redissonClient.getMap(RedisConstants.USER_SEASON_LIKES_COUNT + seasonId + ":" + activityId);
        if (map.isEmpty()) {
            map = competitiveSeasonRankLikeCountBizService.initLikesCount(seasonId, activityId);
        }
        for (ActivityCompetitiveGameUserRankListItemDto itemDto : activityCompetitiveGameUserRankListItemDtos) {
            Long count = MapUtil.getLong(map.get(itemDto.getUserId()), 0l);
            itemDto.setLikesCount(count);
        }
        dto.setRankList(activityCompetitiveGameUserRankListItemDtos);
        CompetitiveSeasonRankDo userCurrentRank = competitiveSeasonRankService.getUserCurrentRank(userId, seasonId);
        dto.setCurrentRank(competitiveSeasonRankConverter.toGameRank(userCurrentRank));
        dto.setSeasonName(competitiveSeasonConfigDo.queryI18nSeasonName(languageCode));
        dto.setEndTime(competitiveSeasonConfigDo.getEndTime());
        return dto;
    }
}
