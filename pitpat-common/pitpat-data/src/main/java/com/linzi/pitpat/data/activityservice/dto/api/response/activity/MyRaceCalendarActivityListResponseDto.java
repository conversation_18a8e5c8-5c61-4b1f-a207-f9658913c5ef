package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:03
 */
@Data
@NoArgsConstructor
public class MyRaceCalendarActivityListResponseDto {
    /**
     * 活动id
     */
    private Long id;
    /**
     * 活动类型，1：组队跑，2：挑战跑 3:排行赛 4：官方组队 5：累计跑 8主题赛事
     *
     * @see RunActivityTypeEnum
     */
    private Integer activityType;
    /**
     * 路线id
     */
    private Long routeId;
    /**
     * 路线类型，1:2D,2:3D
     */
    private Integer routeType;
    /**
     * 队伍名称/活动名称
     */
    private String teamName;
    /**
     * 开始时间
     */
    private ZonedDateTime startTime;
    /**
     * 结束时间
     */
    private ZonedDateTime endTime;
    /**
     * 完成规则类型，1：里程，2：时间 0：无目标
     */
    private Integer completeRuleType;
    /**
     * 参赛次数限制：1：一人一次，2：一人多次
     */
    private Integer entryCount;
    /**
     * 跑步距离，km/mi
     */
    private BigDecimal runMileage;
    /**
     * 跑步时间 s
     */
    private Integer runTime;
    /**
     * 活动状态
     */
    private Integer activityState;
    /**
     * 用户跑步距离，mi
     */
    private BigDecimal alreadyRunMileage;
    /**
     * 用户跑步时间 s
     */
    private Integer alreadyRunTime;
    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;
    /**
     * 用户状态
     */
    private Integer userState;
    /**
     * single单赛事 series-main系列赛主赛事 series-sub系列赛阶段赛事old老赛事
     */
    private String mainType;
}
