package com.linzi.pitpat.data.awardservice.quartz;


import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 更新优惠券状态的定时任务,当优惠券的时间过期之后,自动进行下架
 *
 * <AUTHOR> 李兴海
 * @date : 2023/5/9 15:32
 */
@Component("updateCouponStatusTask")
@Slf4j
public class UpdateCouponStatusTask {
    @Resource
    private CouponService couponService;

    public void run() {
        try {
            OrderUtil.addLogNo();
            log.info("updateCouponStatusTask 执行开始...");
            List<Coupon> couponList = couponService.findEndListByStatusAndExpiryType(2, 1);
            if (CollectionUtils.isEmpty(couponList)) {
                log.info("updateCouponStatusTask 执行结束...");
                return;
            }
            ZonedDateTime now = ZonedDateTime.now();
            Set<Long> couponIdSet = new HashSet<>(couponList.size());
            couponList.forEach(coupon -> {
                ZonedDateTime gmtEnd = coupon.getGmtEnd();
                if (gmtEnd != null && now.isAfter(gmtEnd)) {
                    couponIdSet.add(coupon.getId());
                }
            });
            if (CollectionUtils.isEmpty(couponIdSet)) {
                log.info("updateCouponStatusTask 执行结束...");
                return;
            }
            Coupon coupon = new Coupon();
            coupon.setStatus(-1);
            couponService.updateBatch(couponIdSet, coupon);
            log.info("updateCouponStatusTask 执行结束...");
        } catch (Exception e) {
            log.error("自动更新优惠券状态定时任务:当优惠券的时间过期之后,自动进行下架失败!,异常信息为:{}", e.getMessage());
        } finally {
            OrderUtil.removeLogNo();
        }
    }
}
