package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class MarathonTeamUserDto {

    private Long userId;

    //用户名
    private String username;

    //头像
    private String avatar;

    //0-普通成员，1-队长
    private Integer isTeamLeader = 0;

    /**
     * 跑步里程
     */
    private Integer runMileage;

    /**
     * 跑步里程 ms
     */
    private Integer runtimeMils;
    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 创建时间
     */
    private ZonedDateTime createTime;

    /**
     * @since 4.7.4
     * 是否在指定时间新购买S1
     */
    private Integer isNew;
}
