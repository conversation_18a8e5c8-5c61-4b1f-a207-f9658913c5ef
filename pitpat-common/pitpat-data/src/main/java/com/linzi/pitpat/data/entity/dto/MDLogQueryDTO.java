package com.linzi.pitpat.data.entity.dto;

import com.linzi.pitpat.data.enums.MDEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * @Description 埋点日志 - 查询条件封装
 * <AUTHOR>
 * @Date 2022/8/30 10:12:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MDLogQueryDTO {

    /**
     * 事件类型
     * {@link MDEventTypeEnum}
     */
    private String eventType;

    /**
     * 关联id集合
     */
    private Long refId;

    /**
     * 创建时间筛选
     */
    private ZonedDateTime floorDate;

    /**
     * 创建时间筛选
     */
    private ZonedDateTime ceilingDate;

}
