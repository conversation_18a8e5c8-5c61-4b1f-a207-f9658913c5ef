package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.model.entity.EggActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.EggActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.model.entry.ColorEggConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeActivityConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeConfig;
import com.linzi.pitpat.data.awardservice.service.ColorEggConfigService;
import com.linzi.pitpat.data.awardservice.service.UrgeActivityConfigService;
import com.linzi.pitpat.data.awardservice.service.UrgeConfigService;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.util.GamePushUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:52
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EggActivityBizService {
    private final ZnsRunActivityService runActivityService;
    private final EggActivityConfigService eggActivityConfigService;
    private final ColorEggConfigService colorEggConfigService;
    @Value("${admin.server.gamepush}")
    private String gamepush;


    public void doActivityEggPush(Long activityId) {
        try {
            ZnsRunActivityEntity activityEntity = runActivityService.selectActivityById(activityId);
            if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
                List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
                for (Integer runingGoal : runningGoals) {
                    eggActivityPush(activityEntity.getId(), activityEntity.getActivityRouteId(), runingGoal);
                }
            } else {
                eggActivityPush(activityEntity.getId(), activityEntity.getActivityRouteId(), null);
            }
            doActivityUrgePush(activityEntity.getId());
        } catch (Exception e) {
            log.error("doActivityEggPush 异常", e);
        }
    }

    /**
     * String rule ,String type  ,  String  award_value,Integer r_position ,Integer   r_award_count ,
     * Integer single_count,Integer  first_appear_second,String roomid, Long  activity_id,
     * ds                                 Long route_id,Long  config_id
     *
     * @param activityId
     */
    public void eggActivityPush(Long activityId, Long routeId, Integer targetMileage) {
        ZonedDateTime date = ZonedDateTime.now();
        List<EggActivityConfig> eggActivityConfigs = eggActivityConfigService.selectEggActivityConfigByActivityIdNew(activityId, routeId, date, date);
        if (CollectionUtils.isEmpty(eggActivityConfigs)) {
            eggActivityConfigs = eggActivityConfigService.selectEggActivityConfigByActivityIdRouteIdDateDateNew(activityId, null, date, date); // 如果通过活动和路径去取，取不到，则取默认的
        }
        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
        if (CollectionUtils.isEmpty(eggActivityConfigs) && Objects.nonNull(znsRunActivityEntity)) {
            eggActivityConfigs = eggActivityConfigService.selectEggActivityConfigByTypeGmtStartTimeGmtEndTime(znsRunActivityEntity.getActivityType(), routeId, date, date);
            if (CollectionUtils.isEmpty(eggActivityConfigs)) {
                eggActivityConfigs = eggActivityConfigService.selectEggActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(znsRunActivityEntity.getActivityType(), null, date, date);
            }
        }
        for (EggActivityConfig eggActivityConfig : eggActivityConfigs) {
            if (eggActivityConfig == null) {
                log.info("eggActivityConfig 不存在 activityId =  " + activityId);
                return;
            }
            routeId = eggActivityConfig.getRouteId();
            if (znsRunActivityEntity != null && !Arrays.asList(-1L, -2L, -3L, -4L, -5L).contains(activityId)) {
                routeId = znsRunActivityEntity.getActivityRouteId();
            }
            String roomId = NumberUtils.getGoalImNumber(activityId, targetMileage, znsRunActivityEntity.getCompleteRuleType()) + "";
            if (!Arrays.asList(-1L, -2L, -3L, -4L, -5L).contains(activityId) && Objects.equals(znsRunActivityEntity.getActivityType(), 3)) {  // 如果是排行赛事，3D 则默认房间号为-2
                roomId = "-2";
            }
            ZonedDateTime activityStartTime = eggActivityConfig.getGmtStartTime();
            if (znsRunActivityEntity.getActivityStartTime() != null) {
                activityStartTime = znsRunActivityEntity.getActivityStartTime();
            }
            ColorEggConfig colorEggConfig = colorEggConfigService.selectColorEggConfigById(eggActivityConfig.getColorEggConfigId());
            GamePushUtils.popColorEgg(gamepush, colorEggConfig.getRule(), colorEggConfig.getType(), colorEggConfig.getAwardValue(),
                    colorEggConfig.getRPosition(), colorEggConfig.getRAwardCount(), colorEggConfig.getSingleCount(),
                    colorEggConfig.getFirstAppearSecond(), roomId + "", activityId, routeId,
                    eggActivityConfig.getId(), colorEggConfig.getIntervalAppearSecond(), colorEggConfig.getValidTime(),
                    DateUtil.getTime(activityStartTime),
                    eggActivityConfig.getColorEggConfigId(),
                    DateUtil.getTime(eggActivityConfig.getGmtEndTime()),
                    colorEggConfig.getPointValue(), colorEggConfig.getUseType());
        }
    }

    public void doActivityUrgePush(Long activityId) {
        try {
            ZnsRunActivityEntity activityEntity = runActivityService.selectActivityById(activityId);
            if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
                List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
                for (Integer runingGoal : runningGoals) {
                    urgeActivityUrgePush(activityEntity.getId(), activityEntity.getActivityRouteId(), runingGoal);
                }
            } else {
                urgeActivityUrgePush(activityEntity.getId(), activityEntity.getActivityRouteId(), null);
            }
        } catch (Exception e) {
            log.error("doActivityEggPush 异常", e);
        }
    }

    private final UrgeActivityConfigService urgeActivityConfigService;
    private final UrgeConfigService urlConfigService;

    public void urgeActivityUrgePush(Long activityId, Long routeId, Integer targetMileage) {
        ZonedDateTime date = ZonedDateTime.now();
        List<UrgeActivityConfig> eggActivityConfigList = urgeActivityConfigService.selectUrgeActivityConfigByActivityId(activityId, routeId, date, date);
        if (eggActivityConfigList == null) {
            eggActivityConfigList = urgeActivityConfigService.selectUrgeActivityConfigByActivityIdRouteIdDateDate(activityId, null, date, date); // 如果通过活动和路径去取，取不到，则取默认的
        }

        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
        if (CollectionUtils.isEmpty(eggActivityConfigList) && znsRunActivityEntity != null) {
            eggActivityConfigList = urgeActivityConfigService.selectUrgeActivityConfigByTypeGmtStartTimeGmtEndTime(znsRunActivityEntity.getActivityType(), routeId, date, date);
            if (CollectionUtils.isEmpty(eggActivityConfigList)) {
                eggActivityConfigList = urgeActivityConfigService.selectUrgeActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(znsRunActivityEntity.getActivityType(), null, date, date);
            }
        }
        if (eggActivityConfigList == null) {
            log.info("eggActivityUrgePush 不存在 activityId =  " + activityId);
            return;
        }

        String roomId = NumberUtils.getGoalImNumber(activityId, targetMileage, null) + "";
        if (znsRunActivityEntity != null && Objects.equals(znsRunActivityEntity.getActivityType(), 3)
                && !Arrays.asList(-1L, -2L, -3L, -4L, -5L).contains(activityId)) {  // 如果是排行赛事，3D 则默认房间号为-2
            roomId = "-2";
        }

        for (UrgeActivityConfig urgeActivityConfig : eggActivityConfigList) {
            UrgeConfig urgeConfig = urlConfigService.selectUrgeConfigById(urgeActivityConfig.getUrgeConfigId());
            ZonedDateTime activityStartTime = urgeActivityConfig.getGmtStartTime();
            if (znsRunActivityEntity != null && znsRunActivityEntity.getActivityStartTime() != null) {
                activityStartTime = znsRunActivityEntity.getActivityStartTime();
            }

            GamePushUtils.urgePush(gamepush, urgeConfig.getType(), urgeConfig.getRemainDistance(), urgeConfig.getRemainTime(), urgeConfig.getRunDistance(),
                    urgeConfig.getRunTime(), urgeConfig.getMaxCount(), urgeConfig.getCoolTime(), urgeConfig.getIncOneGrid(), roomId,
                    activityId, urgeActivityConfig.getId(), urgeConfig.getId(), DateUtil.parseDateToStr(DateUtil.YYYYMMDDHHMMSS, activityStartTime),
                    DateUtil.parseDateToStr(DateUtil.YYYYMMDDHHMMSS, urgeActivityConfig.getGmtEndTime()), urgeConfig.getAwardValue());
        }
    }
}
