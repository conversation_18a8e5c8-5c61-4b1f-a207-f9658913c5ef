package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 积分配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.extension.service.IService;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig;
import com.linzi.pitpat.data.awardservice.model.resp.ScoreConfigResp;

import java.time.ZonedDateTime;

public interface ScoreConfigService extends IService<ScoreConfig> {

    ScoreConfigResp selectScoreConfigByUserId(Long id, String calStyle, Long userId, ZonedDateTime startTime, ZonedDateTime endTime);

    ScoreConfig selectScoreConfigById(Long id);
}
