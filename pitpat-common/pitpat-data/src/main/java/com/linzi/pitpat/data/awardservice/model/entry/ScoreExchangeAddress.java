package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_score_exchange_address")
public class ScoreExchangeAddress implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //自增主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //用户id
    private Long userId;
    //用户地址id
    private Long addressId;
    //兑换规则ID
    private Long ruleId;
    //用户积分兑换记录ID
    private Long userScoreId;
}
