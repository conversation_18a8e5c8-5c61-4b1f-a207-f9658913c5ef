package com.linzi.pitpat.data.activityservice.manager.api;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SeriesActivityRankRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityHighlightsResp;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.RankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityRankResponse;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveUserRankListItemDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonRankDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveUserScoreDo;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCompetitiveUserRankListQuery;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveUserScoreQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityHighlightsService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonRankService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveUserScoreService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ActivityHighlightsManager {
    private final AppActivityManager appActivityManager;
    private final ActivityHighlightsService activityHighlightsService;
    private final MainActivityService mainActivityService;
    private final ApiCompetitiveActivityManager competitiveActivityManager;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final EntryGameplayService entryGameplayService;
    private final CompetitiveSeasonRankService competitiveSeasonRankService;
    private final CompetitiveUserScoreService competitiveUserScoreService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final CompetitiveSeasonConfigService competitiveSeasonConfigService;

    /**
     * 获取赛事集锦的详情
     *
     * @return
     */
    @FillerMethod
    public ActivityHighlightsResp getByHighlightId(Long activityId, ZnsUserEntity user, Integer appVersion, Long seasonId) {
        ActivityHighlightsResp resp = new ActivityHighlightsResp();
        ActivityHighlightsDo highlightById = activityHighlightsService.getHighlightByActivityId(activityId);
        if (highlightById != null) {
            if (highlightById.getState() != 1) {
                throw new BaseException(I18nMsgUtils.getMessage("activity.highlights.state.message"), 40900);
            }
            resp.setHighlights(highlightById);
        }

        MainActivity byId = mainActivityService.findById(activityId);
        ActivityDisseminate disseminates = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, user.getLanguageCode());
        if(disseminates!=null){
            //标题
            resp.setActivityTitle(disseminates.getTitle());
        }

        //开始时间
        resp.setStartTime(DateUtil.getStampByZone(byId.getActivityStartTime(),
                byId.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));
        //结束时间
        resp.setEndTime(DateUtil.getStampByZone(byId.getActivityEndTime(),
                byId.getTimeStyle() == 0 ? "UTC" : user.getZoneId()));

        resp.setSeries(byId.isSeriesMain());
        if (byId.isFinishAndAwardSend()) {
            if (!byId.isSeriesMain()) {
                List<RankDto> rankDtos = appActivityManager.singleRank(activityId, user);
                EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(byId.getPlayId());
                Integer competitionFormat = entryGameplay.getCompetitionFormat();
                //todo:现在只有单选
                String rankingBy = entryGameplay.getRankingBy();
                wrapperCompetitiveScore(rankDtos, activityId);
                resp.setSingleRank(rankDtos);
                resp.setRankingBy(rankingBy);
            } else {
                SeriesActivityRankRequest request = new SeriesActivityRankRequest();
                request.setMainActivityId(activityId);
                request.setSource(1);
                request.setAll(true);
                request.setUserId(user.getId());
                SeriesActivityRankResponse seriesActivityRankResponse = appActivityManager.seriesActivityRank(request, user);
                List<SeriesActivityRankDto> records = seriesActivityRankResponse.getRanks().getRecords();
                wrapperSeriesCompetitiveScore(records, activityId);
                resp.setSeriesRank(records);
                resp.setRankingBy(seriesActivityRankResponse.getRankingBy());
            }
        }

        Optional<CompetitiveSeasonConfigDo> competitiveSeasonConfigDo = loadActivitySeasonId(byId.getId());
        if (competitiveSeasonConfigDo.isEmpty()) {
            return resp;
        }
        CompetitiveSeasonConfigDo seasonConfigDo = competitiveSeasonConfigDo.get();
        //设置排名
        ActivityCompetitiveUserRankListQuery query = new ActivityCompetitiveUserRankListQuery();
        query.setUserId(user.getId());
        query.setSeasonType(ActivityCompetitiveSeasonType.ANNUAL);
        query.setTargetRank(10);
        query.setSeasonId(seasonConfigDo.getYear().longValue());
        query.setPageNum(1);
        query.setPageSize(10);
        List<ActivityCompetitiveUserRankListItemDto> rankList = competitiveActivityManager.userRank(query).getRankList().getRecords();
        addSameRankUser(query, rankList);
        resp.setAnnualRank(rankList);
        query.setSeasonType(ActivityCompetitiveSeasonType.SEASONAL);
        if (seasonId == null) {
            query.setSeasonId(seasonConfigDo.getSeasonId());
        } else {
            query.setSeasonId(seasonId);
        }
        List<ActivityCompetitiveUserRankListItemDto> seasonRank = competitiveActivityManager.userRank(query).getRankList().getRecords();
        addSameRankUser(query, seasonRank);
        resp.setSeasonRank(seasonRank);
        competitiveSeasonConfigService.findBySeasonId(query.getSeasonId()).ifPresent(item -> {
            CompetitiveSeasonConfigDto configDto = new CompetitiveSeasonConfigDto();
            configDto.setSeasonId(item.getSeasonId());
            configDto.setSeasonName(item.queryI18nSeasonName(user.getLanguageCode()));
            configDto.setYear(item.getYear());
            configDto.setSeasonType(item.getSeasonType());
            configDto.setSubSeasonType(item.getSubSeasonType());
            configDto.setStartTime(item.getStartTime());
            configDto.setEndTime(item.getEndTime());
            resp.setSeasonConfigDto(configDto);
        });
        List<CompetitiveSeasonConfigDo> rangeActivity = competitiveSeasonBizService.findFirstRelateSeasonByActivityId(activityId);
        resp.setBelongSeasonIds(rangeActivity.stream().filter(k -> ActivityCompetitiveSeasonType.SEASONAL.equals(k.getSeasonType()))
                .map(CompetitiveSeasonConfigDo::getSeasonId).collect(Collectors.toList()));
        resp.setBelongAnnualIds(rangeActivity.stream().filter(k -> ActivityCompetitiveSeasonType.ANNUAL.equals(k.getSeasonType()))
                .map(CompetitiveSeasonConfigDo::getSeasonId).collect(Collectors.toList()));
        return resp;

    }

    private void wrapperSeriesCompetitiveScore(List<SeriesActivityRankDto> records, Long activityId) {
        for (SeriesActivityRankDto rankDto : records) {
            CompetitiveUserScoreQuery query = CompetitiveUserScoreQuery.builder().userId(rankDto.getUserId()).activityId(activityId).build();
            CompetitiveUserScoreDo scoreDo = competitiveUserScoreService.findByQuery(query);
            if (scoreDo != null) {
                rankDto.setCompetitiveScore(scoreDo.getScore());
            }
        }
    }


    private void wrapperCompetitiveScore(List<RankDto> rankDtos, Long activityId) {
        if (CollectionUtils.isEmpty(rankDtos)) {
            return;
        }
        for (RankDto rankDto : rankDtos) {
            CompetitiveUserScoreQuery query = CompetitiveUserScoreQuery.builder().userId(rankDto.getUserId()).activityId(activityId).build();
            CompetitiveUserScoreDo scoreDo = competitiveUserScoreService.findByQuery(query);
            if (scoreDo != null) {
                rankDto.setCompetitiveScore(scoreDo.getScore());
            }
        }
    }

    private Optional<CompetitiveSeasonConfigDo> loadActivitySeasonId(Long activityId) {
        List<CompetitiveSeasonConfigDo> rangeActivity = competitiveSeasonBizService.findFirstRelateSeasonByActivityId(activityId);
        if (CollectionUtils.isEmpty(rangeActivity)) {
            return Optional.empty();
        }
        return Optional.ofNullable(rangeActivity.get(0));
    }

    private void addSameRankUser(ActivityCompetitiveUserRankListQuery query, List<ActivityCompetitiveUserRankListItemDto> rankList) {
        if (CollectionUtils.isEmpty(rankList)) {
            return;
        }
        if (rankList.size() >= query.getPageSize()) {
            //列表是满的。查询最后一名的名次是否有重复的。
            ActivityCompetitiveUserRankListItemDto lastItem = rankList.get(rankList.size() - 1);
            List<Long> seasonIds = new ArrayList<>();
//            List<Long> seasonIds = competitiveSeasonRankService
//                    .getSeasonIds(query.getActivitySeasonDate()!=null?query.getActivitySeasonDate(): ZonedDateTime.now(), null);
            Long seasonId = query.getSeasonId();
//            if (ActivityCompetitiveSeasonType.ANNUAL.equals(query.getSeasonType())) {
//                seasonId = seasonIds.get(0);
//            } else if (ActivityCompetitiveSeasonType.SEASONAL.equals(query.getSeasonType())) {
//                seasonId = seasonIds.get(1);
//            } else {
//                return;
//            }
            List<CompetitiveSeasonRankDo> seasonRankUser = competitiveSeasonRankService.getSeasonRankUser(seasonId, lastItem.getSeasonRank());
            if (!CollectionUtils.isEmpty(seasonRankUser) && seasonRankUser.size() > 1) {
                List<ActivityCompetitiveUserRankListItemDto> list = rankList.stream().filter(item -> !item.getSeasonRank().equals(lastItem.getSeasonRank())).toList();
                List<ActivityCompetitiveUserRankListItemDto> list2 = seasonRankUser.stream().map(item -> {
                    ActivityCompetitiveUserRankListItemDto i = new ActivityCompetitiveUserRankListItemDto();
                    i.setSeasonRank(Long.valueOf(item.getSeasonRank()));
                    i.setSeasonRankChange(Long.valueOf(item.getSeasonRankChange()));
                    i.setEventCount(item.getEventCount());
                    i.setSeasonScore(item.getSeasonScore());
                    i.setUserId(item.getUserId());
                    i.setSeasonBonus(item.getSeasonBonus());
                    return i;
                }).toList();

                rankList.clear();
                rankList.addAll(list);
                rankList.addAll(list2);

            }
        }
    }
}
