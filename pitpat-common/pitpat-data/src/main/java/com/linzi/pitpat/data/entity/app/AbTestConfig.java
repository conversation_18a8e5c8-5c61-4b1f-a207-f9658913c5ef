package com.linzi.pitpat.data.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-18
 */

@Data
@NoArgsConstructor
@TableName("zns_ab_test_config")
public class AbTestConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.app.AbTestConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键
    public final static String key_ = CLASS_NAME + "config_key";                     // ABTest的类型key
    public final static String user_id = CLASS_NAME + "user_id";              // 用户id
    public final static String value_ = CLASS_NAME + "value";                 // 值(1:老版本A，2：新版本B )
    public final static String is_delete = CLASS_NAME + "is_delete";          //
    public final static String gmt_create = CLASS_NAME + "gmt_create";        //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    //
    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //ABTest的类型key
    private String configKey;
    //用户id
    private Long userId;
    //值(1:老版本A，2：新版本B )
    private Integer value;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;

    @Override
    public String toString() {
        return "AbTestConfig{" +
                ",id=" + id +
                ",configKey=" + configKey +
                ",userId=" + userId +
                ",value=" + value +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                "}";
    }
}
