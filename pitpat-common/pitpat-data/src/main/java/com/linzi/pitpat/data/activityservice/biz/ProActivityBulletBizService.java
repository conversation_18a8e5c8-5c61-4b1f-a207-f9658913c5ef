package com.linzi.pitpat.data.activityservice.biz;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProBullet;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProModuleActivityItemResponse;
import com.linzi.pitpat.data.activityservice.enums.ActivityTempRankBizEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityHighlightDo;
import com.linzi.pitpat.data.activityservice.service.ActivityRunRankTempService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ProActivityHighLightBulletEnum;
import com.linzi.pitpat.data.systemservice.model.entity.I18nKeyValueDo;
import com.linzi.pitpat.data.systemservice.model.query.I18nKeyValueQuery;
import com.linzi.pitpat.data.systemservice.service.I18nKeyValueService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProActivityBulletBizService {
    private final ProActivityRedisEnhanceService proActivityRedisEnhanceService;

    private final I18nKeyValueService i18nKeyValueService;

    private final ZnsUserService userService;

    private final ActivityStageService activityStageService;

    private final ActivityRunRankTempBizService activityRunRankTempBizService;

    private final ActivityRunRankTempService activityRunRankTempService;

    private final MainActivityService mainActivityService;


    private Cache<String, List<I18nKeyValueDo>> i18nKeyCache = CacheBuilder.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<I18nKeyValueDo>>() {
                @Override
                public List<I18nKeyValueDo> load(String langCode) throws Exception {
                    I18nKeyValueQuery i18nKeyValueQuery = new I18nKeyValueQuery();
                    i18nKeyValueQuery.setScene("PRO_ACTIVITY_BULLET");
                    i18nKeyValueQuery.setLangCode(langCode);

                    List<I18nKeyValueDo> i18n = i18nKeyValueService.findList(i18nKeyValueQuery);
                    return i18n;
                }
            });

    /**
     * 填充活动的弹幕信息
     *
     * @param response
     */
    public void fillBullet(RaceProModuleActivityItemResponse response, RaceProBullet proBullet) {
        Long userId = response.getUserId();
        List<I18nKeyValueDo> i18n = null;
        try {
            i18n = i18nKeyCache.get(response.getLanguageCode(), () -> getI18nKeyValueDos(response));
        } catch (ExecutionException e) {
            i18n = getI18nKeyValueDos(response);
        }
        if (CollectionUtils.isEmpty(i18n)) {
            return;
        }
        Map<String, String> i18nMap = new HashMap<>();
        for (I18nKeyValueDo i18nKeyValueDo : i18n) {
            i18nMap.put(i18nKeyValueDo.getKeyCode(), i18nKeyValueDo.getValue());
        }
        ProActivityHighlightDo highlight = proActivityRedisEnhanceService.getProActivityHighlight(response.getMainActivityId());

        switch (proBullet.getBulletType()) {
            case FRIENDS_VIEWED -> {
                Integer i = proActivityRedisEnhanceService.getFriendsViewCount(response.getMainActivityId(), userId);
                if (i > 0) {
                    String friendsViewed = i18nMap.get("FRIENDS_VIEWED").replace("{userCount}", i + "");
                    proBullet.setValue(friendsViewed);
                }
            }
            case USERS_VIEWED -> {
                long i = proActivityRedisEnhanceService.getPageViewCount(response.getMainActivityId());
                if (i > 0) {
                    String usersViewed = i18nMap.get("USERS_VIEWED").replace("{userCount}", i + "");
                    proBullet.setValue(usersViewed);
                }
            }
            case FRIENDS_REGISTERED -> {
                Integer i = proActivityRedisEnhanceService.getFriendEnrollCount(response.getMainActivityId(), userId);
                if (i > 0) {
                    String friendsRegistered = i18nMap.get("FRIENDS_REGISTERED").replace("{userCount}", "" + i);
                    proBullet.setValue(friendsRegistered);
                }

            }
            case CEO_REGISTERED -> {
                if (highlight.haveCeoEnroll()) {
                    proBullet.setValue(i18nMap.get("CEO_REGISTERED"));
                }
            }
            case KOL_REGISTERED -> {
                if (highlight.haveKolEnroll()) {
                    Long firstEnrollKolUserId = highlight.getFirstEnrollKolUserId();
                    ZnsUserEntity byId = userService.findById(firstEnrollKolUserId);
                    if (byId != null) {
                        String kolRegistered = i18nMap.get("KOL_REGISTERED").replace("{kolName}", byId.getFirstName());
                        proBullet.setValue(kolRegistered);
                    }
                }
            }
            case DROPPED_FROM_REWARDS -> {
                activityRunRankTempBizService.findTempRankUserRank(ActivityTempRankBizEnum.ProActivityBullet, response.getMainActivityId(), userId).ifPresent(rank -> {
                    Long activityRunRankTempId = rank.getActivityRunRankTempId();
                    ActivityRunRankTempDo byId = activityRunRankTempService.findById(activityRunRankTempId);
                    if (byId != null) {
                        Integer awardRankByTarget = byId.getAwardRankByTarget(rank.getTargetRunMileage());
                        //处于奖励取外
                        boolean isOutRewardRank = rank.getCurrentRank().compareTo(awardRankByTarget) > 0;
                        boolean isInUserRank = rank.getUserRunRank().compareTo(awardRankByTarget) <= 0;
                        if (isOutRewardRank && isInUserRank) {
                            proBullet.setValue(i18nMap.get("DROPPED_FROM_REWARDS"));
                        }
                    }
                });
            }
            case RANK_OVERTAKEN -> {
                activityRunRankTempBizService.findTempRankUserRank(ActivityTempRankBizEnum.ProActivityBullet, response.getMainActivityId(), userId).ifPresent(rank -> {
                    Integer userRunRank = rank.getUserRunRank();
                    Integer currentRank = rank.getCurrentRank();
                    if (userRunRank != null && userRunRank != -1 && currentRank != null && currentRank != -1) {
                        int minus = currentRank - userRunRank;
                        if (minus > 0) {
                            String rankOvertaken = i18nMap.get("RANK_OVERTAKEN").replace("{overTakenCount}", minus + "");
                            proBullet.setValue(rankOvertaken);
                        }
                    }
                });
            }
            case CURRENT_TOP_ONE -> {
                if (highlight.getRankNo1UserId() != null) {
                    ZnsUserEntity byId = userService.findById(highlight.getRankNo1UserId());
                    if (byId != null && StringUtils.hasText(byId.getFirstName())) {
                        String currentTopOne = i18nMap.get("CURRENT_TOP_ONE").replace("{firstName}", byId.getFirstName());
                        proBullet.setValue(currentTopOne);
                    }
                }

            }
            case REWARD_REVIEWING -> {
                MainActivity byId = mainActivityService.findById(response.getMainActivityId());
                if (byId != null) {
                    Integer activityStatus = mainActivityService.getActivityState(byId, response.getZoneId());
                    if (ActivityStateEnum.FINISHED.getState().equals(activityStatus)) {
                        if (mainActivityService.getActivityAwardSendStatus(byId)) {
                            proBullet.setValue(i18nMap.get("REWARD_REVIEWING"));
                        }
                    }
                }


            }
            //下面的是在底部的，没有文案。
            case REGISTRATION_COUNTDOWN -> {
                proBullet.setValue(String.valueOf(response.getApplicationStartTime()));
                proBullet.setMsg("Start");
            }
            case REGISTRATION_FINISH_COUNTDOWN -> {
                proBullet.setValue(String.valueOf(response.getApplicationEndTime()));
                proBullet.setMsg("End");
            }
            case ESTIMATED_REWARD -> {
                BigDecimal totalAmountAward = response.getTotalAmountAward();
                if (BigDecimal.ZERO.compareTo(totalAmountAward) < 0) {
                    proBullet.setValue(response.getCurrency().getCurrencySymbol() + "" + totalAmountAward.toString());
                }
            }
            case CURRENT_PARTICIPANTS -> {
                Integer enrollCount = proActivityRedisEnhanceService.getEnrollCount(response.getMainActivityId());
                if(enrollCount!=null&&enrollCount>0){
                    proBullet.setValue(enrollCount + "");
                }
            }
            case EVENT_COUNTDOWN -> {
                proBullet.setValue(String.valueOf(response.getActivityStartTime()));
                proBullet.setMsg("Start");
            }
            case EVENT_END_COUNTDOWN -> {
                if (activityStageService.isStageACtivity(response.getMainActivityId())) {
                    activityStageService.findNextStage(response.getMainActivityId(), ZonedDateTime.now()).ifPresentOrElse(s -> {
                        proBullet.setValue(String.valueOf(s.getStartTime().toInstant().toEpochMilli()));
                        proBullet.setIconUrl(ProActivityHighLightBulletEnum.EVENT_COUNTDOWN.getIconUrl());
                        proBullet.setMsg("Next");
                    }, () -> {
                        proBullet.setValue(String.valueOf(response.getActivityEndTime()));
                        proBullet.setMsg("End");
                    });
                } else {
                    proBullet.setValue(String.valueOf(response.getActivityEndTime()));
                    proBullet.setMsg("End");
                }

            }
            case PERSONAL_RESULT -> {
                activityRunRankTempBizService.findTempRankUserRank(ActivityTempRankBizEnum.ProActivityBullet, response.getMainActivityId(), userId).ifPresent(rank -> {
                    proBullet.setValue(rank.getGradeStr());
                });
            }
            case PERSONAL_RANK -> {
                activityRunRankTempBizService.findTempRankUserRank(ActivityTempRankBizEnum.ProActivityBullet, response.getMainActivityId(), userId).ifPresent(rank -> {
                    proBullet.setValue(rank.getCurrentRank() + "");
                });
            }
            case TOP_ONE_RESULT -> {
                if (StringUtils.hasText(highlight.getRankNo1Grade())) {
                    proBullet.setValue(highlight.getRankNo1Grade());
                }
            }
            case TOP_ONE_REWARD -> {
                if (StringUtils.hasText(highlight.getRankNo1Award())) {
                    proBullet.setValue(highlight.getRankNo1Award());
                }
            }
        }
    }

    @NotNull
    private List<I18nKeyValueDo> getI18nKeyValueDos(RaceProModuleActivityItemResponse response) {
        I18nKeyValueQuery i18nKeyValueQuery = new I18nKeyValueQuery();
        i18nKeyValueQuery.setScene("PRO_ACTIVITY_BULLET");
        i18nKeyValueQuery.setLangCode(response.getLanguageCode());

        List<I18nKeyValueDo> i18n = i18nKeyValueService.findList(i18nKeyValueQuery);
        if (CollectionUtils.isEmpty(i18n)) {
            return List.of();
        }
        return i18n;
    }

}
