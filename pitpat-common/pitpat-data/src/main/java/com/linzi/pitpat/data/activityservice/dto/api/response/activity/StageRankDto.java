package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
public class StageRankDto {
    //用户id
    private Long userId;
    //排名
    private Integer rank;
    //昵称
    private String nickname;
    //头像
    private String avatar;
    //平均速度
    private BigDecimal averageVelocity;
    //跑步用时(ms)
    private Integer runTime;
    //运动里程（m）
    private Integer runMileage;
    //完成时间
    private ZonedDateTime completeTime;
    //是否完成比赛:0未完成，1表示完成(不代表比赛完成，只是展示成绩) 2标识退赛 3作弊
    private Integer completeState;
    /**
     * 默认 0 非作弊 1 作弊
     *
     * @see com.linzi.pitpat.data.enums.YesNoStatus
     */
    private Integer cheatingState;

    //设备模型
    private String equipmentModel;
    //奖金
    private BigDecimal bonus;

}
