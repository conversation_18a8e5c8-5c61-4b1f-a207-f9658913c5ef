package com.linzi.pitpat.data.awardservice.dto.api;


import com.linzi.pitpat.data.awardservice.constant.enums.WearConstant;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
@Builder
public class UserWearsBagDto {
    private Long id;
    /**
     * 服装类型: 1:发色（头发、帽子）, 2:肤色, 3.头型, 4:脸部服饰（眼镜）, 5:上衣, 6:裤子, 7:鞋子, 8:套装，9:背部服饰，10：动作
     *
     * @see WearConstant.WearTypeEnum
     */
    private Integer wearType;
    /**
     * 服装名称
     */
    private String wearName;
    /**
     * 服装ID
     */
    private Integer wearValue;
    /**
     * 服装图片URL
     */
    private String wearImageUrl;
    /**
     * 状态： 0:未过期，1:已过期
     */
    private Integer status;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 失效时间
     *
     * @tag 2.10.0
     */
    private ZonedDateTime expiredTime;
}
