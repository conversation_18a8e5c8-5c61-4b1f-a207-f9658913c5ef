package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityListDto;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityManagePo;
import com.linzi.pitpat.data.activityservice.model.resp.FeiGuanFangZhuiDuiResp;
import com.linzi.pitpat.data.activityservice.model.resp.GuanFangZhuiDuiResp;
import com.linzi.pitpat.data.activityservice.model.resp.GuanPKResp;
import com.linzi.pitpat.data.activityservice.model.resp.RankDtoResp;
import com.linzi.pitpat.data.activityservice.model.vo.MyActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityTeamVO;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.resp.LichengbeiResp;
import com.linzi.pitpat.data.resp.XinRenFuliDto;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.IsEmpty;
import com.lz.mybatis.plugin.annotations.IsNull;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LIKE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LLIKE;
import com.lz.mybatis.plugin.annotations.Order;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.OrderType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 跑步活动表
 *
 * <AUTHOR>
 * @date 2021-12-29 09:58:11
 */
@Mapper
public interface ZnsRunActivityDao extends BaseMapper<ZnsRunActivityEntity> {

    /**
     * 查询未开始或进行中的活动
     * 当前用户可进入
     *
     * @param page
     * @param userId
     * @param activityType
     * @return
     */
    List<RunActivityTeamVO> getNoOverActivityByUserId(Page page, @Param("userId") Long userId, @Param("activityType") Integer activityType);

    Integer getParticipantsNumByType(@Param("activityType") Integer activityType, @Param("startOfDate") ZonedDateTime startOfDate, @Param("endOfDate") ZonedDateTime endOfDate);

    ZnsRunActivityEntity getActivityByDetailId(Long detailId);

    ZnsRunActivityEntity getActivityByDetailId2(Long detailId);

    List<RunActivityTeamVO> getOfficialRunActivity(Page page, @Param("userId") Long userId, @Param("activityType") Integer activityType, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("activityState") List<Integer> activityState);

    ZnsRunActivityEntity getOneUserActivityInfo(@Param("activityType") Integer activityType, @Param("userId") Long userId);

    /**
     * 获取包含用户的活动，除新人活动
     *
     * @param startTime
     * @param endTime
     * @param userId
     * @return
     */
    List<ZnsRunActivityEntity> getUserActivity(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("userId") Long userId);

    List<RunActivityTeamVO> myDayRecords(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("userId") Long userId, @Param("isTest") boolean isTest, @Param("checkVersion") boolean checkVersion);

    Page<ZnsRunActivityEntity> getIndividualTeamRunActivity(Page page, @Param("userId") Long userId, @Param("isTest") boolean isTest);


    /**
     * 非官方活动列表
     */
    Page<ActivityListDto> runActivityList(Page page, @Param("po") ActivityManagePo po);


    List<ZnsRunActivityEntity> selectActivityByStatus(Integer activityState);

    @LIMIT
    ZnsRunActivityEntity selectActivityById(Long id);

    List<ZnsRunActivityEntity> selectActivityByActivityStateActivityStartHasRobot(Integer activityState, @LE ZonedDateTime activityStartTime, @Column(ZnsRunActivityEntity.activity_start_time) @GE ZonedDateTime startTime, Integer hasRobot);

    void updateId(@Param("id") Long id, @Param("newId") Long newId);

    RunActivityTeamVO getActivityTeamVOById(@Param("activityId") Long activityId, @Param("userId") Long userId);

    List<ZnsRunActivityEntity> selectActivityByActivityStartTimeActivityStateActivityType(@LE ZonedDateTime activityStartTime, @GE ZonedDateTime activityEndTime, @IN List<Integer> activityState, Integer activityType, @IN List<Integer> status);


    List<ZnsRunActivityEntity> selectActivityByStatusHasRotBotActivityStartTimeActivityState(Integer status, Integer hasRobot, @LE ZonedDateTime activityStartTime, @IN List<Integer> activityState, @IN List<Integer> activityType);

    List<ZnsRunActivityEntity> getIndividualTeamRunActivity2(@Param("userId") Long userId, @Param("isTest") boolean isTest, @Param("checkVersion") boolean checkVersion, @Param("country") String country);


    List<ZnsRunActivityEntity> selectActivityByStatusActivityStartTimeActivityState(Integer status, @DateFormat @LE ZonedDateTime activityStartTime, @IN List<Integer> activityState);

    Page<ZnsRunActivityEntity> selectActivityByIsTemplate(IPage page, @Param("isTemplate") Integer isTemplate, @Param("activityTitle") String activityTitle);


    List<MyActivityListVO> getMyActivity(@Param("userId") Long userId, @Param("testUser") boolean testUser, @Param("taskIds") List<Long> taskIds, @Param("checkUser") boolean checkUser);


    @DataCache(value = {"page.current", "page.size", "activityType", "gmtStartTime", "gmtEndTime", "activityNo"})
    Page<GuanFangZhuiDuiResp> selectPageByGuanFangZhuDui(@Param("page") IPage page, @Param("activityType") String activityType,
                                                         @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime,
                                                         @Param("activityNo") String activityNo);


    @DataCache(value = {"page.current", "page.size", "activityType", "gmtStartTime", "gmtEndTime", "activityNo"})
    Page<FeiGuanFangZhuiDuiResp> selectPageByFeiGuanFangZhuDui(@Param("page") IPage page, @Param("activityType") String activityType,
                                                               @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime,
                                                               @Param("activityNo") String activityNo
    );

    @DataCache(value = {"page.current", "page.size", "activityType", "gmtStartTime", "gmtEndTime", "activityNo"})
    Page<GuanPKResp> selectPageByPk(@Param("page") IPage page, @Param("activityType") String activityType,
                                    @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime, @Param("activityNo") String activityNo);


    @DataCache(value = {"page.current", "page.size", "activityType", "gmtStartTime", "gmtEndTime", "activityNo"})
    Page<LichengbeiResp> selectPageByLiChengBei(@Param("page") IPage page, @Param("activityType") String activityType, @Param("gmtStartTime") ZonedDateTime gmtStartTime
            , @Param("gmtEndTime") ZonedDateTime gmtEndTime, @Param("activityNo") String activityNo);


    @DataCache(value = {"page.current", "page.size", "gmtStartTime", "gmtEndTime"})
    Page<XinRenFuliDto> getXinRenFuli(@Param("page") IPage page, @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime);


    @DataCache(value = {"page.current", "page.size", "activityType", "gmtStartTime", "gmtEndTime", "activityNo"})
    Page<RankDtoResp> selectPageByRank(@Param("page") IPage page, @Param("activityType") String activityType,
                                       @Param("gmtStartTime") ZonedDateTime gmtStartTime, @Param("gmtEndTime") ZonedDateTime gmtEndTime,
                                       @Param("activityNo") String activityNo
    );

    Integer getActivityCountByUserId(@Param("startDate") ZonedDateTime startDate, @Param("endDate") ZonedDateTime endDate, @Param("userId") Long userId);

    List<ZnsRunActivityEntity> getActualCount(List<Long> activityIds);


    List<ZnsRunActivityEntity> selectActivityByNotApplicationActivityEndTime(Integer activityType, Integer activityState, Integer status);

    List<ZnsRunActivityEntity> selectActivityByTask(@Param("isTest") boolean isTest, @Param("checkVersion") boolean checkVersion, @Param("source") Integer source,
                                                    @Param("userId") Long userId, @Param("completeRuleType") Integer completeRuleType,
                                                    @Param("runWalkStatus") List<Integer> runWalkStatus, @Param("country") String country,
                                                    @Param("activityType") Integer activityType, @Param("rateLimitType") Integer rateLimitType);

    @LIMIT
    ZnsRunActivityEntity selectActivityByBatchNo(String batchNo, @IN List<Integer> activityState);

    @Order({
            @By(value = {"activity_start_time"}, type = OrderType.ASC)
    })
    @LIMIT
    ZnsRunActivityEntity selectActivityByBatchNoActivityStateActivityStartTimeAsc(String batchNo, @IN List<Integer> activityState);


    List<ZnsRunActivityEntity> selectActivityByBatchNoActivityStateList(String batchNo, @IN List<Integer> activityState);

    @LIMIT
    ZnsRunActivityEntity selectActivityByTaskConfigId(Long taskConfigId);


    @OrderByIdDescLimit_1
    ZnsRunActivityEntity selectActivityByTaskConfigIdLast(Long taskConfigId);

    @Order({
            @By(value = {"activity_start_time"}, type = OrderType.DESC)
    })
    @LIMIT(2)
    List<ZnsRunActivityEntity> selectActivityByBatchNoActivityStateActivityStartTime(String batchNo, @IN List<Integer> activityState);


    @Order({
            @By(value = {"activity_state"}, type = OrderType.DESC),
            @By(value = {"activity_start_time"}, type = OrderType.ASC)
    })
    List<ZnsRunActivityEntity> selectActivityByBatchNoActivityStateDesc(String batchNo, @IF @IN List<Integer> activityState);


    @Order({
            @By(value = {"activity_end_time"}, type = OrderType.DESC)
    })
    @LIMIT(1)
    ZnsRunActivityEntity selectActivityByActivityEndTimeDesc(List<Long> id);


    List<ZnsRunActivityEntity> selectAutomaticEnrollingOfficialTeamBatchNoIsNull(Integer isAddRobot,
                                                                                 @Column(ZnsRunActivityEntity.application_start_time) @LE ZonedDateTime applicationStartTimeEnd,
                                                                                 @Column(ZnsRunActivityEntity.application_start_time) @GE ZonedDateTime applicationStartTime,
                                                                                 Integer activityState,
                                                                                 Integer status,
                                                                                 Integer activityType,
                                                                                 @IsNull String batchNo);

    List<ZnsRunActivityEntity> selectAutomaticEnrollingOfficialTeamBatchNOIsNOtNullGroupByBatchNo(@Param("isAddRobot") Integer isAddRobot, @Param("applicationStartTime") ZonedDateTime applicationStartTime, @Param("startTime") ZonedDateTime startTime, @Param("activityState") Integer activityState,
                                                                                                  @Param("status") Integer status, @Param("activityType") Integer activityType);

    @LIMIT(2)
    List<ZnsRunActivityEntity> selectAutomaticEnrollingOfficialTeamBatchNo(Integer isAddRobot, @LE ZonedDateTime applicationStartTime, Integer activityState, Integer status, Integer activityType, String batchNo);

    void addUserCount(@Param("activityId") Long activityId);

    @DataCache("id")
    List<ZnsRunActivityEntity> selectByIds(@IN List<Long> id);


    @Order({
            @By(ZnsRunActivityEntity.activity_start_time),
            @By(value = ZnsRunActivityEntity.id_, type = OrderType.DESC)
    })
    List<ZnsRunActivityEntity> selectActivityByCondition(
            Integer status,
            @IF Integer activityType,
            @IN List<Integer> activityState,
            @GE ZonedDateTime activityStartTime,
            @LIKE Integer isTest,
            @LLIKE BigDecimal runMileage,
            @IsEmpty String batchNo);


    @OrderByIdDescLimit_1
    ZnsRunActivityEntity selectActivityByBatchNoStatusActivityState(String batchNo, @IN List<Integer> status, @IN List<Integer> activityState);

    ZnsRunActivityEntity selectHomeActivityOne(@Param("activityType") Integer activityType, @Param("isTest") boolean isTest, @Param("checkUser") boolean checkUser, @Param("userId") Long userId);

    List<ZnsRunActivityEntity> selectActivityList(@Param("activityType") Integer activityType,
                                                  @Param("isTest") boolean isTest, @Param("checkUser") boolean checkUser,
                                                  @Param("runWalkStatus") List<Integer> runWalkStatus, @Param("batchNo") boolean batchNo,
                                                  @Param("userId") Long userId, @Param("isHomepage") boolean isHomepage, @Param("source") Integer source,
                                                  @Param("completeRuleType") Integer completeRuleType, @Param("country") String country, @Param("rateLimitType") Integer rateLimitType);


    List<ZnsRunActivityEntity> getHomeActivityByTypeAndState(@Param("activityType") List<Integer> activityType,
                                                             @Param("isTest") boolean isTest, @Param("checkUser") boolean checkUser,
                                                             @Param("runWalkStatus") List<Integer> runWalkStatus,
                                                             @Param("userId") Long userId,
                                                             @Param("country") String country);

    Page<RunActivityTeamVO> getPkActivityList(Page page, @Param("userId") Long userId, @Param("isTest") boolean testUser,
                                              @Param("checkVersion") boolean checkVersion);

    Page<ZnsRunActivityEntity> selectActivityPage(Page page, @Param("activityType") Integer activityType,
                                                  @Param("isTest") boolean isTest, @Param("checkUser") boolean checkUser,
                                                  @Param("runWalkStatus") List<Integer> runWalkStatus, @Param("batchNo") boolean batchNo,
                                                  @Param("userId") Long userId, @Param("isHomepage") boolean isHomepage, @Param("source") Integer source,
                                                  @Param("completeRuleType") Integer completeRuleType,
                                                  @Param("country") String country);


    void addOfficialActivityUserPayCount(Long id);

    ActivityTypeDto getActivityNew(Long activityId);

    @Select("SELECT IFNULL((select max(id) from zns_run_activity),0)")
    Long getMaxId();

    List<ZnsRunActivityEntity> getUserEnrollActivity(Long userId, Integer type, ZonedDateTime start, ZonedDateTime end);
}
