package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.core.constants.enums.DingTalkMsgTypeEnum;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.data.activityservice.model.entity.RoomDo;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.userservice.enums.UserGenderEnum;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserLoginLogEntity;
import com.linzi.pitpat.data.userservice.model.query.UserLoginLogQuery;
import com.linzi.pitpat.data.userservice.model.vo.UserAvgDataDto;
import com.linzi.pitpat.data.userservice.model.vo.UserEventPrefDto;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.ShiftCalculation;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
@RequiredArgsConstructor
public class RoomUserDataBiz {


    private final ZnsUserLoginLogService znsUserLoginLogService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final ZnsUserService userService;

    public void sendUserDataNotice(RoomDo room, ZnsUserEntity loginUser, String finalToken, String finalSecret, Integer type) {
        String msgTpl2 = "";
        String title = "";
        String activeProfile = SpringContextUtils.getActiveProfile();
        String userAvgPace = "/";
        String userAvgMin = "/";
        // 获取用户平均配速/平均时长
        UserAvgDataDto userAveragePace = queryUserAverageDurationAndAveragePace(loginUser.getId());
        if (Objects.nonNull(userAveragePace)) {
            BigDecimal multiplied = userAveragePace.getAvgPace().multiply(new BigDecimal("1.6"));
            userAvgPace = checkPaceInterval(multiplied.intValue());
            userAvgMin = userAveragePace.getAvgSecond() / 60 + "mins";
        }
        // 获取用户最近PK胜率
        String userRecentPkWinRate = queryUserRecentPkWinRate(loginUser.getId());
        // 获取用户赛事喜好
        String userEventPrefs = queryUserEventPrefs(loginUser.getId());
        // 获取用户登录时段
        String userLoginData = queryUserLogInInfoData(loginUser.getId());
        // 获取用户运动数据
        String userSportData = queryUserSportData(loginUser.getId());
        String msg2;
        if (type == 1) {
            msgTpl2 = """
                         ### User creates a room.
                         #### Room number: %s
                         #### Gender: %s
                         #### Avs. pace last 7 games: %s
                         #### Duration last 7 games: %s
                         #### Recent PK win rate: %s
                         #### Event prefs: %s
                         #### Login time slot: %s
                         #### Exercise sessions: %s
                    """;
            title = "有用户创建房间";
            msg2 = String.format(msgTpl2,
                    room.getRoomNumber(), UserGenderEnum.resolve(loginUser.getGender()).getEnName()
                    , userAvgPace, userAvgMin,
                    userRecentPkWinRate, userEventPrefs,
                    userLoginData, userSportData);
        } else {
            msgTpl2 = """
                         ### User joins the NPC room.
                         #### Room number: %s
                         #### Account: %s
                         #### Gender: %s
                         #### Avs. pace last 7 games: %s
                         #### Duration last 7 games: %s
                         #### Recent PK win rate: %s
                         #### Event prefs: %s
                         #### Login time slot: %s
                         #### Exercise sessions: %s
                    """;
            title = "有用户加入到 NPC房间";
            ZnsUserEntity owner = userService.findById(room.getOwnerUserId());
            msg2 = String.format(msgTpl2,
                    room.getRoomNumber(), owner.getEmailAddressEn(), UserGenderEnum.resolve(owner.getGender()).getEnName()
                    , userAvgPace, userAvgMin,
                    userRecentPkWinRate, userEventPrefs,
                    userLoginData, userSportData);
        }
        DingTalkUtils.sendMsg(DingTalkRequestDto.ofMarkdown( finalToken, finalSecret, msg2));
    }


    public static String checkPaceInterval(Integer paceInSeconds) {
        // 将 4 分 30 秒和 6 分 30 秒转换为秒
        int minPace = 4 * 60 + 30;
        int maxPace = 6 * 60 + 30;

        if (paceInSeconds <= minPace) {
            return "≤4'30''";
        } else if (paceInSeconds < maxPace) {
            return "4'30''-6'30'' ";
        } else {
            return "≥6'30''";
        }
    }

    private String queryUserEventPrefs(Long userId) {
        // 其他赛事 (用户赛,道具赛,段位赛/目标跑)
        List<UserEventPrefDto> preActivityTypeCount = realPersonRunDataDetailsService.findUserLastWeekActivityTypeCount(userId);
        Integer competitiveActivityCount = realPersonRunDataDetailsService.findSumCountByUserIdLastWeek(userId);
        // 职业赛次数
        if (CollectionUtils.isEmpty(preActivityTypeCount)) {
            if (Objects.isNull(competitiveActivityCount) || competitiveActivityCount == 0) {
                return "/";
            } else {
                return "Event";
            }
        } else {
            if (Objects.nonNull(competitiveActivityCount) && competitiveActivityCount != 0) {
                preActivityTypeCount.add(new UserEventPrefDto(13, competitiveActivityCount));
            }
            Integer activityType = preActivityTypeCount.stream().max(Comparator.comparingInt(UserEventPrefDto::getCount)).map(UserEventPrefDto::getActivityType).orElse(0);
            if (activityType != 0) {
                return switch (activityType) {
                    case 17 -> "PK";
                    case 18 -> "Lucky Dush";
                    case 15 -> "Match Ranking";
                    case 13 -> "Event";
                    case -1 -> "Target Workout";
                    default -> "/";
                };
            }
            return "/";
        }

    }

    private String queryUserRecentPkWinRate(Long userId) {
        List<Integer> rankList = realPersonRunDataDetailsService.findUserLastTenPkData(userId);
        if (CollectionUtils.isEmpty(rankList)) {
            return "/";
        }
        long rankOneCount = rankList.stream()
                .filter(rank -> rank == 1)
                .count();
        BigDecimal totalCount = new BigDecimal(rankList.size());
        BigDecimal oneCount = new BigDecimal(rankOneCount);
        BigDecimal percentage = oneCount.divide(totalCount, 2, RoundingMode.DOWN).multiply(new BigDecimal(100)).setScale(0, RoundingMode.DOWN);
        log.info("rank 为 1 的数据占比:{}", percentage);
        return percentage + "%";
    }

    private UserAvgDataDto queryUserAverageDurationAndAveragePace(Long userId) {
        return realPersonRunDataDetailsService.findUserAverageDurationAndAveragePace(userId);
    }

    /**
     * 用户运动数据
     *
     * @param userId
     * @return
     */
    private String queryUserSportData(Long userId) {
        List<RealPersonRunDataDetails> listByTime = realPersonRunDataDetailsService.findListByTime(userId, DateUtil.addDays1(ZonedDateTime.now(), -7), ZonedDateTime.now(), List.of(0));
        if (CollectionUtils.isEmpty(listByTime)) {
            return "/";
        } else {
            return listByTime.size() + "";
        }
    }

    public String queryUserLogInInfoData(Long userId) {
        ZonedDateTime now = ZonedDateTime.now();
        ZoneId zoneId = ZoneId.systemDefault();
        UserLoginLogQuery query = UserLoginLogQuery.builder().userId(userId).startTime(DateUtil.addDays1(now, -7)).endTime(now).build();
        List<ZnsUserLoginLogEntity> userLoginData = znsUserLoginLogService.findList(query);
        if (!CollectionUtils.isEmpty(userLoginData)) {
            List<ZonedDateTime> list = userLoginData.stream().map(ZnsUserLoginLogEntity::getCreateTime).map(date -> {
                Instant instant = date.toInstant();
                return ZonedDateTime.ofInstant(instant, zoneId);
            }).toList();
            return ShiftCalculation.findMostFrequentShift(list);
        }
        return "";
    }

}
