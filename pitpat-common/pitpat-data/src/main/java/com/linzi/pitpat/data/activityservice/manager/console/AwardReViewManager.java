package com.linzi.pitpat.data.activityservice.manager.console;

import com.google.common.collect.Lists;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityResultBusiness;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.DetailsCheatStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.converter.console.UserRunDataDetailsConverter;
import com.linzi.pitpat.data.activityservice.dto.console.response.AwardReviewUserDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.DataDetailsListResponseDto;
import com.linzi.pitpat.data.activityservice.model.dto.CheatRiskLogDto;
import com.linzi.pitpat.data.activityservice.model.dto.UserPropRecordDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataDetailsCheat;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsCheatQuery;
import com.linzi.pitpat.data.activityservice.model.request.ActivityIdRequest;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.entity.award.ActivityUserAwardPre;
import com.linzi.pitpat.data.service.award.ActivityUserAwardPreService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class AwardReViewManager {
    private final ActivityUserAwardPreService activityUserAwardPreService;
    private final ZnsUserService znsUserService;
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ActivityUserBizService activityUserBizService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final ActivityResultBusiness activityResultBusiness;
    private final MongoTemplate mongoTemplate;
    private final UserRunDataDetailsConverter userRunDataDetailsConverter;

    /**
     * 获取审核列表
     *
     * @param request
     * @return List<AwardReviewUserDto>
     */
    public List<AwardReviewUserDto> queryReviewList(ActivityIdRequest request) {
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(request.getActivityId());
        if (CollectionUtils.isEmpty(allActivityUser)) {
            return new ArrayList<>();
        }
        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());

        ActivityResultBusiness.ActivityContext context = activityResultBusiness.buildActivityContext(request.getActivityId());

        Map<Long, ZnsUserEntity> userMap = znsUserService.findUserMap(userIds);
        Map<Long, List<ZnsUserRunDataDetailsEntity>> userRunDataDetailsMap = activityResultBusiness.getUserRunDataMap(context, userIds);
        Map<Long, Long> userCheatHitMap = activityResultBusiness.getUserCheatHitMap(userRunDataDetailsMap);

        //排序处理
        if (context.getMainActivity().getAwardSendStatus() == 1) {
            allActivityUser = allActivityUser.stream().filter(znsRunActivityUserEntity -> znsRunActivityUserEntity.getRank() > 0).sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRank)).limit(request.getPageSize()).toList();
        } else {
            Map<Long, UserPropRecordDto> propMap = activityResultBusiness.getPropRecordMap(context);
            for (ZnsRunActivityUserEntity runActivityUser : allActivityUser) {
                resetUserResult(runActivityUser, context, userRunDataDetailsMap.get(runActivityUser.getUserId()), propMap);
            }
            allActivityUser = sortActivityUsers(allActivityUser, context);
        }

        ActivityResultBusiness.ActivityResultContext resultContext = activityResultBusiness.buildActivityResultContext(request.getActivityId(), allActivityUser);
        List<AwardConfigDto> awardConfigs = activityResultBusiness.getAwardConfigs(context.getMainActivity().getId());
        //构建处理结果
        List<AwardReviewUserDto> records = activityResultBusiness.buildReviewResults(allActivityUser, context, resultContext, awardConfigs, userMap, userCheatHitMap, userRunDataDetailsMap);

        records = records.stream().sorted(Comparator.comparing(AwardReviewUserDto::getCheatingState)
                .thenComparing(AwardReviewUserDto::getRank)
                .thenComparing(AwardReviewUserDto::getUserNickname)
                .thenComparing(AwardReviewUserDto::getRaceResult)).collect(Collectors.toList());

        // 废弃用户成绩展示
        if (context.getMainActivity().getAwardSendStatus() == 1) {
            List<ActivityUserAwardPre> activityUserAwardPres = activityUserAwardPreService.cheatUserIdListList(request.getActivityId());
            for (ActivityUserAwardPre activityUserAwardPre : activityUserAwardPres) {
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUserWithNoState(activityUserAwardPre.getUserId(), request.getActivityId());
                AwardReviewUserDto awardReviewUserDto = activityResultBusiness.getAwardReviewUserDto(activityUser, userMap.get(activityUserAwardPre.getUserId()), context.getRankingBy(), userCheatHitMap, userRunDataDetailsMap);
                awardReviewUserDto.setRank(null);
                awardReviewUserDto.setStatus(-1);
                records.add(awardReviewUserDto);
            }
        }
        return records;
    }

    /**
     * 用户列表排序
     *
     * @param allActivityUser
     * @param context
     * @return List<ZnsRunActivityUserEntity>
     */
    private List<ZnsRunActivityUserEntity> sortActivityUsers(List<ZnsRunActivityUserEntity> allActivityUser, ActivityResultBusiness.ActivityContext context) {
        if (context.getMainActivity().getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())) {
            SeriesGameplay seriesGameplay = context.getSeriesGameplay();
            //用户刷选
            if (2 == seriesGameplay.getRankingUser()) {
                allActivityUser = allActivityUser.stream().filter(ZnsRunActivityUserEntity::getIsJoinAll).collect(Collectors.toList());
            } else if (3 == seriesGameplay.getRankingUser()) {
                allActivityUser = allActivityUser.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
            }

            //用户排名当前单选
            if ("1".equals(seriesGameplay.getRankingBy())) {
                allActivityUser = allActivityUser.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
            } else if ("2".equals(seriesGameplay.getRankingBy())) {
                if (2 == seriesGameplay.getRankingUser() || 1 == seriesGameplay.getRankingUser()) {
                    allActivityUser = allActivityUser.stream().sorted((o1, o2) -> {
                        Integer propRunTime1 = o1.getPropRunTime();
                        Integer propRunTime2 = o2.getPropRunTime();
                        if (Objects.isNull(propRunTime1)) {
                            propRunTime1 = o1.getRunTimeMillisecond();
                        }
                        if (Objects.isNull(propRunTime2)) {
                            propRunTime2 = o2.getRunTimeMillisecond();
                        }
                        return propRunTime2 - propRunTime1;
                    }).collect(Collectors.toList());
                } else {
                    allActivityUser = allActivityUser.stream().sorted((o1, o2) -> {
                        Integer propRunTime1 = o1.getPropRunTime();
                        Integer propRunTime2 = o2.getPropRunTime();
                        if (Objects.isNull(propRunTime1)) {
                            propRunTime1 = o1.getRunTimeMillisecond();
                        }
                        if (Objects.isNull(propRunTime2)) {
                            propRunTime2 = o2.getRunTimeMillisecond();
                        }
                        return propRunTime1 - propRunTime2;
                    }).collect(Collectors.toList());
                }
            } else if ("3".equals(seriesGameplay.getRankingBy())) {
                ZonedDateTime now = ZonedDateTime.now();
                allActivityUser = allActivityUser.stream().sorted((o1, o2) -> {
                    ZonedDateTime o1CompleteTime = o1.getCompleteTime();
                    ZonedDateTime o2CompleteTime = o2.getCompleteTime();
                    if (Objects.isNull(o1CompleteTime)) {
                        o1CompleteTime = DateUtil.addDays(now, 1);
                    }
                    if (Objects.isNull(o2CompleteTime)) {
                        o2CompleteTime = DateUtil.addDays(now, 1);
                    }
                    return o1CompleteTime.compareTo(o2CompleteTime);
                }).collect(Collectors.toList());
            } else if ("4".equals(seriesGameplay.getRankingBy())) {
                allActivityUser = allActivityUser.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getAverageVelocity, Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
            }
            for (int i = 0; i < allActivityUser.size(); i++) {
                ZnsRunActivityUserEntity runActivityUser = allActivityUser.get(i);
                runActivityUser.setRank(i + 1);
            }
        } else {
            if (context.getEntryGameplay().getTargetType() != 0) {
                allActivityUser = allActivityUser.stream().filter(a -> a.getIsComplete() == 1).collect(Collectors.toList());
            }
            //排名数据更新
            if ("2".equals(context.getEntryGameplay().getRankingBy())) {
                if (context.getEntryGameplay().getTargetType() != 0) {
                    allActivityUser = allActivityUser.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond)).collect(Collectors.toList());
                } else {
                    allActivityUser = allActivityUser.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond).reversed()).collect(Collectors.toList());
                }
            } else {
                allActivityUser = allActivityUser.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
            }
            //排名并列处理
            Integer previousRank = 1;
            Integer previousGoal = 0;
            for (int i = 0; i < allActivityUser.size(); i++) {
                ZnsRunActivityUserEntity activityUser = allActivityUser.get(i);
                activityUser.setRank(i + 1);
                if ("2".equals(context.getEntryGameplay().getRankingBy())) {
                    if (activityUser.getRunTimeMillisecond().equals(previousGoal)) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunTimeMillisecond();
                    previousRank = activityUser.getRank();
                } else {
                    log.info("completeRuleType = 2");
                    if (activityUser.getRunMileage().compareTo(new BigDecimal(previousGoal)) == 0) {
                        activityUser.setRank(previousRank);
                    }
                    previousGoal = activityUser.getRunMileage().intValue();
                    previousRank = activityUser.getRank();
                }
            }
        }
        return allActivityUser;
    }


    /**
     * 重置用户成绩
     *
     * @param runActivityUser
     * @param userRunDataDetailsEntities
     * @param userPropRecordMap
     */
    private void resetUserResult(ZnsRunActivityUserEntity runActivityUser, ActivityResultBusiness.ActivityContext context,
                                 List<ZnsUserRunDataDetailsEntity> userRunDataDetailsEntities, Map<Long, UserPropRecordDto> userPropRecordMap) {
        runActivityUser.setIsJoinAll(false);
        if (CollectionUtils.isEmpty(userRunDataDetailsEntities)) {
            return;
        }
        ZonedDateTime startTime = runActivityUser.getCreateTime();
        if (!StringUtils.isEmpty(context.getMainActivity().getActivityStartTime()) && DateTimeUtil.parse(context.getMainActivity().getActivityStartTime()).isAfter(startTime)) {
            startTime = DateTimeUtil.parse(context.getMainActivity().getActivityStartTime());
        }
        ZonedDateTime finalStartTime = startTime;
        userRunDataDetailsEntities = userRunDataDetailsEntities.stream().filter(d -> d.getLastTime().isAfter(finalStartTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userRunDataDetailsEntities)) {
            return;
        }
        Long cheatCount = userRunDataDetailsEntities.stream().filter(d -> d.getIsCheat() == 1).count();
        if (context.getMainActivity().getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())) {
            List<ZnsRunActivityUserEntity> allActivityUser = new ArrayList<>();
            //计算子阶段成绩
            Map<Long, List<ZnsUserRunDataDetailsEntity>> activityDataMap = userRunDataDetailsEntities.stream().collect(Collectors.groupingBy(ZnsUserRunDataDetailsEntity::getActivityId));
            runActivityUser.setIsJoinAll(true);
            for (int i = 0; i < context.getActivityIdList().size(); i++) {
                ZnsRunActivityUserEntity entryRunActivityUser = runActivityUserService.findActivityUser(context.getActivityIdList().get(i), runActivityUser.getUserId());
                if (Objects.isNull(entryRunActivityUser)) {
                    runActivityUser.setIsJoinAll(false);
                    //未完成不需要继续执行
                    continue;
                }
                activityResultBusiness.resetUserEntryResult(context.getEntryGameplay(), entryRunActivityUser, activityDataMap.get(context.getActivityIdList().get(i)), userPropRecordMap);
                allActivityUser.add(entryRunActivityUser);
            }

            activityResultBusiness.resetSeriesActivityUserResult(runActivityUser, allActivityUser, context.getActivityIdList());
        } else {
            // 考虑风控检测中,风控检测中也可能需要重置成绩
            UserRunDataDetailsCheat detailsCheat = userRunDataDetailsCheatService.findOne(new UserRunDataDetailsCheatQuery().setRunDataDetailsIds(userRunDataDetailsEntities.stream().map(ZnsUserRunDataDetailsEntity::getId).collect(Collectors.toList())).setIsCheat(DetailsCheatStateEnum.RISK.getState()));
            if (cheatCount == 0 && Objects.isNull(detailsCheat)) {
                log.info("非作弊用户无需重置用户成绩");
                return;
            }
            activityResultBusiness.resetUserEntryResult(context.getEntryGameplay(), runActivityUser, userRunDataDetailsEntities, userPropRecordMap);
            runActivityUser.setRunTimeMillisecond(runActivityUser.getPropRunTime());
        }
    }


    public void exportList(ActivityIdRequest activityIdRequest, HttpServletResponse response, HttpServletRequest request) {
        List<AwardReviewUserDto> list = queryReviewList(activityIdRequest);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityIdRequest.getActivityId());
        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        ActivityResultBusiness.ActivityContext context = activityResultBusiness.buildActivityContext(activityIdRequest.getActivityId());
        List<ZnsUserRunDataDetailsEntity> userRunDataDetailsList = activityResultBusiness.getUserRunData(context, userIds, null);
        List<DataDetailsListResponseDto> dtoList = userRunDataDetailsConverter.toDtoList(userRunDataDetailsList);
        setUserCheatData(dtoList);

        ExcelUtil util = new ExcelUtil(AwardReviewUserDto.class);
        MainActivity mainActivity = mainActivityService.findById(activityIdRequest.getActivityId());
        String fileName = "奖励审核-" + mainActivity.getRemark() + "-" + AwardSendStatusEnum.findDesc(mainActivity.getAwardSendStatus());
        util.exportMultiExcel(Lists.newArrayList(new ExcelUtil.SheetData(fileName, list, AwardReviewUserDto.class), new ExcelUtil.SheetData("奖励审核-运动记录", dtoList, DataDetailsListResponseDto.class)),
                fileName, response, request);
    }

    private void setUserCheatData(List<DataDetailsListResponseDto> dtoList) {
        List<List<DataDetailsListResponseDto>> partitionList = ListUtils.partition(dtoList, 1000);
        for (List<DataDetailsListResponseDto> dataDetailsListResponseDtos : partitionList) {
            List<Long> runIdList = dataDetailsListResponseDtos.stream().map(DataDetailsListResponseDto::getId).collect(Collectors.toList());
            Query query = Query.query(Criteria.where("run_id").in(runIdList));
            List<CheatRiskLogDto> cheatRiskLogDtos = mongoTemplate.find(query, CheatRiskLogDto.class, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
            Map<Long, List<CheatRiskLogDto>> listMap = cheatRiskLogDtos.stream().collect(Collectors.groupingBy(CheatRiskLogDto::getRun_id));
            for (DataDetailsListResponseDto dataDetailsListResponseDto : dataDetailsListResponseDtos) {
                List<CheatRiskLogDto> riskLogDtos = listMap.get(dataDetailsListResponseDto.getId());
                if (org.apache.commons.collections.CollectionUtils.isEmpty(riskLogDtos)) {
                    continue;
                }
                Set<Integer> noLoadList = new HashSet<>();
                for (CheatRiskLogDto cheatRiskLogDto : riskLogDtos) {
                    if ("风控作弊判定结果".equals(cheatRiskLogDto.getMsg())) {
                        dataDetailsListResponseDto.setRiskCheatResult(1);
                    } else if ("用户空跑".equals(cheatRiskLogDto.getMsg())) {
                        noLoadList.add(cheatRiskLogDto.getRun_time());
                    } else if (cheatRiskLogDto.getMsg().contains("空载持续时间大于")) {
                        noLoadList.add(cheatRiskLogDto.getRun_time());
                    }
                }
                dataDetailsListResponseDto.setNoLoadList(noLoadList);
                dataDetailsListResponseDto.setCheatCheatHitCount(noLoadList.size());
            }
        }
    }

}
