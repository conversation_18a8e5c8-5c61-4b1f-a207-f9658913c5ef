package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 兑换券规则
 *
 * <AUTHOR>
 * @since 2023-11-13
 */

@Slf4j
@Data
@NoArgsConstructor
@TableName("zns_exchange_score_rule")
@Accessors(chain = true)
public class ExchangeScoreRule implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                                 // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                                    // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";                                  // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                              // 最后修改时间
    public final static String activity_name = CLASS_NAME + "activity_name";                            // 积分兑换活动名称
    public final static String coupon_id = CLASS_NAME + "coupon_id";                                    // 优惠券id
    public final static String rank_ = CLASS_NAME + "rank";                                             // 排序
    public final static String profit_supply = CLASS_NAME + "profit_supply";                            // 权益提供方
    public final static String exchange_score = CLASS_NAME + "exchange_score";                          // 兑换需积分
    public final static String exchange_amount = CLASS_NAME + "exchange_amount";                        // 兑换需金额
    public final static String exchange_count = CLASS_NAME + "exchange_count";                          // 兑换次数
    public final static String remain_num = CLASS_NAME + "remain_num";                                  // 剩余库存
    public final static String exchange_reserve = CLASS_NAME + "exchange_reserve";                      // 可兑换量,-1表示不限
    public final static String status_ = CLASS_NAME + "status";                                         // 上架状态 ， 1 已上架，0 下架， -1 新建
    public final static String exchange_person_count = CLASS_NAME + "exchange_person_count";            // 兑换人次数
    public final static String exchange_person_day_limit = CLASS_NAME + "exchange_person_day_limit";    // 用户每日兑换上限,-1表示不限
    public final static String exchange_rule = CLASS_NAME + "exchange_rule";                            // 兑换规则，图片url
    public final static String profit_icon = CLASS_NAME + "profit_icon";                                // 图片
    public final static String tag_image = CLASS_NAME + "tag_image";                                    // 商品角标
    public final static String advertise_image = CLASS_NAME + "advertise_image";                        // 物品宣传图
    public final static String exchange_goods_name = CLASS_NAME + "exchange_goods_name";                // 兑换物品名称
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //积分兑换活动名称
    private String activityName;
    //优惠券id
    private Long couponId;
    //排序
    @TableField("`rank`")
    private Integer rank;
    //权益提供方
    private String profitSupply;
    //兑换需金额
    @DecimalMin(value = "0.01", message = "兑换需金额不能小于0.01$")
    @DecimalMax(value = "9999.99", message = "兑换需金额不能大于9999.99$")
    //@JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal exchangeAmount;
    //兑换需积分
    private Integer exchangeScore;
    //兑换次数
    private Integer exchangeCount;
    //剩余库存
    private Integer remainNum;
    //可兑换量,-1表示不限
    private Integer exchangeReserve;
    //上架状态 ， 1 已上架，0 下架， -1 新建
    private Integer status;
    //兑换人次数
    private Integer exchangePersonCount;
    //用户每日兑换上限,-1表示不限
    private Integer exchangePersonDayLimit;
    //用户兑换等级限制
    private Integer userLevelLimit;
    //兑换规则,文字
    private String exchangeRule;
    //图片
    private String profitIcon;
    //商品角标
    private String tagImage;
    //物品宣传图
    private String advertiseImage;
    //兑换物品名称
    private String exchangeGoodsName;
    //兑换需金额（原价/划线价）
    private BigDecimal originalExchangeAmount;
    //兑换需积分（原价/划线价）
    private Integer originalExchangeScore;
    //有效期天数,-1表示永久
    private Integer validDays;
    /**
     * 归属于，1：普通，2：vip
     */
    private Integer belongTo;
    // 默认语言code
    private String defaultLangCode;
    //是否在app端展示：0不展示，1展示
    private Integer isShowApp;

    //平台sku编码
    private String skuNo;

    /**
     * 4.7.0 后创建商品，0：不是，1：是
     * @since 4.7.0
     */
    private Integer isNewGoods;
}
