package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 优惠券
 *
 * <AUTHOR>
 * @since 2023-06-08
 */

@Data
@NoArgsConstructor
@TableName("zns_coupon")
@Accessors(chain = true)
public class Coupon implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.Coupon:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                               // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                                  // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";                                // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                            // 最后修改时间
    public final static String creator_ = CLASS_NAME + "creator";                                     // 创建者
    public final static String modifier_ = CLASS_NAME + "modifier";                                   // 最后修改者
    public final static String title_ = CLASS_NAME + "title";                                         // 标题
    public final static String exchange_code = CLASS_NAME + "exchange_code";                          // 兑换码
    public final static String name_ = CLASS_NAME + "name";                                           // 运营自己看名称
    public final static String type_ = CLASS_NAME + "type";                                           // 券来源类型【1：刮刮卡券,2 排行榜,3活动获取,4 后台发放,5 积分兑换 】
    public final static String coupon_type = CLASS_NAME + "coupon_type";                              // 券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券,6 进阶里程碑券,7:独立站抵扣券】
    public final static String amount_ = CLASS_NAME + "amount";                                       // 券金额
    public final static String discount_ = CLASS_NAME + "discount";                                   // 券折扣
    public final static String can_use_description = CLASS_NAME + "can_use_description";              // 可用描述
    public final static String description_ = CLASS_NAME + "description";                             // 使用规则
    public final static String expiry_type = CLASS_NAME + "expiry_type";                              // 有效期类型【1:days固定天数，2:range固定时间范围】
    public final static String quota_ = CLASS_NAME + "quota";                                         // 优惠券发放总数 -1:不限制
    public final static String quota_send = CLASS_NAME + "quota_send";                                // 已经领取数量
    public final static String limit_count = CLASS_NAME + "limit_count";                              // 每个人限制领取张数，-1不限制
    public final static String equipment_limit_count = CLASS_NAME + "equipment_limit_count";          // 每台设备限制领取张数，-1不限制
    public final static String valid_days = CLASS_NAME + "valid_days";                                // 有效期天数
    public final static String gmt_start = CLASS_NAME + "gmt_start";                                  // 有效期开始时间
    public final static String gmt_end = CLASS_NAME + "gmt_end";                                      // 有效期结束时间
    public final static String receive_type = CLASS_NAME + "receive_type";                            // 领取类型【0:发放后即可，1限时】
    public final static String receive_start = CLASS_NAME + "receive_start";                          // 领取开始时间
    public final static String receive_end = CLASS_NAME + "receive_end";                              // 领取结束时间
    public final static String status_ = CLASS_NAME + "status";                                       // 1：已发布 -1：已失效
    public final static String picture_ = CLASS_NAME + "picture";                                     // 图片
    public final static String use_scope = CLASS_NAME + "use_scope";                                  // 使用范围：1：全场通用 2：部分活动类型
    public final static String use_scope_id = CLASS_NAME + "use_scope_id";                            // 使用范围具体参数，根据使用范围决定值的类型
    public final static String min_pic_url = CLASS_NAME + "min_pic_url";                              // 卷图片url 缩略
    public final static String route_id = CLASS_NAME + "route_id";                                    // 路由表id
    public final static String expiration_remind_type = CLASS_NAME + "expiration_remind_type";        // 过期提醒配置 0 默认 1 自定义
    public final static String expiration_remind_day = CLASS_NAME + "expiration_remind_day";          // 过期提醒天数配置
    public final static String expiration_remind_time = CLASS_NAME + "expiration_remind_time";        // 过期提醒时间配置
    public final static String expiration_remind_method = CLASS_NAME + "expiration_remind_method";    // 提醒方式 0 push 1 im 2 im + push
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //最后修改者
    private String modifier;
    //标题
    private String title;
    //兑换码
    private String exchangeCode;
    //运营自己看名称
    private String name;
    /**
     * 券来源类型【1：刮刮卡券,2 排行榜,3活动获取,4 后台发放,5 积分兑换，100：商城 】
     *
     * @see CouponConstant.TypeEnum
     */
    private Integer type;
    /**
     * 券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券,6 进阶里程碑券,7独立站抵扣券，100：商城优惠券】
     *
     * @see CouponTypeEnum
     */
    private Integer couponType;
    //券金额
    private BigDecimal amount;
    //券折扣
    private BigDecimal discount;
    //可用描述
    private String canUseDescription;
    //使用规则
    private String description;
    /**
     * 有效期类型【1:days固定天数，2:range固定时间范围】
     *
     * @see CouponConstant.ExpiryTypeEnum
     */
    private Integer expiryType;
    //优惠券发放总数 -1:不限制
    private Integer quota;
    //已经领取数量
    private Integer quotaSend;
    //每个人限制领取张数，-1不限制
    private Integer limitCount;
    //每台设备限制领取张数，-1不限制
    private Integer equipmentLimitCount;
    //有效期天数
    private Integer validDays;
    //有效期开始时间
    private ZonedDateTime gmtStart;
    //有效期结束时间
    private ZonedDateTime gmtEnd;
    //领取类型【0:发放后即可，1限时】
    private Integer receiveType;
    //领取开始时间
    private ZonedDateTime receiveStart;
    //领取结束时间
    private ZonedDateTime receiveEnd;

    /**
     * 1：已发布 -1：已失效
     *
     * @see CouponConstant.CouponStatusEnum
     */
    private Integer status;

    //图片
    private String picture;
    /**
     * 使用范围：1：全场通用 2：部分活动/商品，3：单品券(指定商品)
     *
     * @see CouponConstant.UseScopeEnum
     */
    private Integer useScope;
    //使用范围具体参数，根据使用范围决定值的类型，多个用 , 隔开
    private String useScopeId;
    //卷图片url 缩略
    private String minPicUrl;
    //路由表id
    private Long routeId;
    //过期提醒配置 0 默认 1 自定义
    private Integer expirationRemindType;
    //过期提醒天数配置
    private Integer expirationRemindDay;
    //过期提醒时间配置
    private String expirationRemindTime;
    //提醒方式 0 push 1 im 2 im + push
    private Integer expirationRemindMethod;
    // 默认语言code
    private String defaultLangCode;
    // 时间类型：1:同一时刻；2:各时区分别推送
    private Integer timeType;
    //是否3.0赛事分类（0：不是，1：是）
    private Integer isActivityCategory;

    /**
     * 优惠券主类型  1：赛事券，2：商城券
     *
     * @see CouponConstant.CouponMainTypeEnum
     * @since 4.4.3
     */
    private Integer couponMainType;

    /**
     * 使用最小总金额
     *
     * @since 4.4.3
     */
    private BigDecimal minTotalAmount;

    /**
     * 商城优惠券是否叠加其他营销，0：不叠加，1：叠加
     *
     * @see CouponConstant.AddMarketEnum
     * @since 4.4.3
     */
    private Integer mallAddMarket;

    /**
     * 商城优惠券是否在详情页展，0：不展示，1：展示, 【是】在商品模块计算预估价并展示在详情
     *
     * @see CouponConstant.ShowDetailEnum
     * @since 4.4.3
     */
    private Integer mallShowDetail;

    /**
     * 优惠方式，1：金额，2：折扣
     *
     * @see CouponConstant.DiscountMethodEnum
     * @since 4.4.3
     */
    private Integer discountMethod;

    /**
     * 国家code多选,"["US","UK"]"
     */
    private String countryCode;

    /**
     * 国家数组（非持久化字段）
     */
    @TableField(exist = false)
    private List<String> countryCodes;

    public List<String> getCountryCodes() {
        return StringUtils.hasText(countryCode) ? JsonUtil.readList(countryCode,String.class) : List.of(I18nConstant.CountryCodeEnum.US.code);
    }

    /**
     * 填充多币种金额
     */
    public void fillCurrencyAmount(CouponCurrencyEntity couponCurrencyEntity) {
        if (couponCurrencyEntity != null){
            if(CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(this.discountMethod)){
                this.amount = couponCurrencyEntity.getAmount();
            }
            this.minTotalAmount = Optional.ofNullable(couponCurrencyEntity.getMinTotalAmount()).orElse(this.minTotalAmount);
        }
    }
}
