package com.linzi.pitpat.data.activityservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.activityservice.constant.enums.CompetitiveDisseninateStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.CompetitiveDisseminateJumpType;
import com.linzi.pitpat.data.activityservice.mapper.CompetitiveDisseminateMapper;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveDisseminateDo;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveDisseminatePageQuery;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveDisseminateQuery;
import com.linzi.pitpat.data.activityservice.service.CompetitiveDisseminateService;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 竞技赛品宣表 服务实现类
 *
 * @since 2024-04-16
 */
@Slf4j
@Service
public class CompetitiveDisseminateServiceImpl implements CompetitiveDisseminateService {
    //使用构造器初始化依赖
    private CompetitiveDisseminateMapper competitiveDisseminateMapper;

    public CompetitiveDisseminateServiceImpl(CompetitiveDisseminateMapper competitiveDisseminateMapper) {
        this.competitiveDisseminateMapper = competitiveDisseminateMapper;
    }

    @Override
    public Long create(CompetitiveDisseminateDo competitiveDisseminate) {
        //必要的业务检查
        int affectedRow = competitiveDisseminateMapper.insert(competitiveDisseminate);
        log.info("创建竞技赛品宣表,user={}, affected row={}", competitiveDisseminate, affectedRow);
        return competitiveDisseminate.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    @Caching(evict = {
            //@CacheEvict(value = UserCacheName.USER_KEY, key = "#competitiveDisseminate.id"),
    })
    public Long update(CompetitiveDisseminateDo competitiveDisseminate) {
        CompetitiveDisseminateDo existed = findById(competitiveDisseminate.getId());
        if (Objects.isNull(existed)) {
            throw new RuntimeException("竞技赛品宣表不存在");
        }
        int affectedRow = competitiveDisseminateMapper.updateById(competitiveDisseminate);
        log.info("更新竞技赛品宣表,user={}, affected row={}", competitiveDisseminate, affectedRow);
        return competitiveDisseminate.getId();
    }

    @Override
    public Long updateSelective(CompetitiveDisseminateQuery query, CompetitiveDisseminateDo competitiveDisseminate) {
        LambdaUpdateWrapper<CompetitiveDisseminateDo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(Objects.nonNull(query.getId()), CompetitiveDisseminateDo::getId, query.getId())
                .setEntity(competitiveDisseminate);
        int affectedRow = competitiveDisseminateMapper.update(updateWrapper);
        log.info("部分更新竞技赛品宣表,user={}, affected row={}", competitiveDisseminate, affectedRow);
        return competitiveDisseminate.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@CacheEvict(value = UserCacheName.USER_KEY, key = "#id")
    public boolean deleteById(Long id) {
        CompetitiveDisseminateDo existed = findById(id);
        if (Objects.isNull(existed)) {
            throw new RuntimeException("竞技赛品宣表不存在");
        }
        competitiveDisseminateMapper.deleteById(id);
        log.info("删除竞技赛品宣表，id={}", id);
        return true;
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@Cacheable(value = UserCacheName.USER_KEY, key = "#id")
    public CompetitiveDisseminateDo findById(Long id) {
        return competitiveDisseminateMapper.selectById(id);
    }

    @Override
    public CompetitiveDisseminateDo findByQuery(CompetitiveDisseminateQuery query) {
        //TODO 不建议第二个参数设置为 false, 否则如果数据异常，难以发现
        return competitiveDisseminateMapper.selectOne(buildQueryWrapper(query), false);
    }

    @Override
    public List<CompetitiveDisseminateDo> findList(CompetitiveDisseminateQuery query) {
        Wrapper<CompetitiveDisseminateDo> wrapper = buildQueryWrapper(query);
        List<CompetitiveDisseminateDo> CompetitiveDisseminates = competitiveDisseminateMapper.selectList(wrapper);
        log.info("查询竞技赛品宣表列表， query={}", query);
        return CompetitiveDisseminates;
    }

    @Override
    public Page<CompetitiveDisseminateDo> findPage(CompetitiveDisseminatePageQuery pageQuery) {
        Wrapper<CompetitiveDisseminateDo> queryWrapper = buildQueryWrapper(pageQuery);

        Page<CompetitiveDisseminateDo> result = competitiveDisseminateMapper.selectPage(PageHelper.ofPage(pageQuery), queryWrapper);
        log.info("查询竞技赛品宣表列表， pageQuery={}", pageQuery);
        return result;
    }

    @Override
    public boolean batchCreate(List<CompetitiveDisseminateDo> competitiveDisseminateList) {
        competitiveDisseminateMapper.insert(competitiveDisseminateList);
        log.info("批量更新竞技赛品宣表列表， pageQuery={}", competitiveDisseminateList);
        return false;
    }

    @Override
    public Long createOrUpdate(CompetitiveDisseminateDo disseminateDo) {
        String jumpUrl = disseminateDo.getJumpUrl();
        if (CompetitiveDisseminateJumpType.EVENT_H5.equals(disseminateDo.getJumpType()) &&
                StringUtils.hasText(jumpUrl) && jumpUrl.contains("/")) {
            jumpUrl = jumpUrl.substring(jumpUrl.lastIndexOf("/") + 1);
            disseminateDo.setH5ConfigId(Long.parseLong(jumpUrl));
        }
        if (disseminateDo.getId() == null) {
            CompetitiveDisseminateQuery query = new CompetitiveDisseminateQuery();
            query.setTitle(disseminateDo.getTitle());
            //query.setShowLocation(disseminateDo.getShowLocation());
            CompetitiveDisseminateDo byQuery = findByQuery(query);
            if (byQuery != null) {
                throw new BaseException("已有该名称");
            }
            return create(disseminateDo);
        } else {
            return update(disseminateDo);
        }

    }

    @Override
    public void changStatus(Long id, Integer status) {
        CompetitiveDisseninateStatusEnum statusEnum = CompetitiveDisseninateStatusEnum.findByCode(status);
        CompetitiveDisseminateDo disseminateDo = findById(id);
        switch (statusEnum) {
            case ONLINE -> {
                CompetitiveDisseminateQuery query = new CompetitiveDisseminateQuery();
                query.setStatus(CompetitiveDisseninateStatusEnum.ONLINE.getCode());
                query.setShowLocation(disseminateDo.getShowLocation());
                CompetitiveDisseminateDo byQuery = findByQuery(query);
                if (byQuery != null) {
                    throw new BaseException("与已有宣传冲突");
                }
                doChangeStatus(id, status);
            }
            case OFFLINE -> doChangeStatus(id, status);
            default -> throw new IllegalArgumentException("Unknown status:" + status);
        }

    }

    @Override
    public CompetitiveDisseminateDo findBySeasonType(String competitiveSeasonType) {
        CompetitiveDisseminateQuery query = new CompetitiveDisseminateQuery();
        query.setShowLocation(competitiveSeasonType);
        query.setStatus(CompetitiveDisseninateStatusEnum.ONLINE.getCode());
        query.setShowStartTime(ZonedDateTime.now());
        query.setShowEndTime(ZonedDateTime.now());

        return findByQuery(query);
    }

    @Override
    public List<CompetitiveDisseminateDo> findExpierList() {
        return competitiveDisseminateMapper.selectList(Wrappers.<CompetitiveDisseminateDo>lambdaQuery()
                .eq(CompetitiveDisseminateDo::getStatus, CompetitiveDisseninateStatusEnum.ONLINE.getCode())
                .le(CompetitiveDisseminateDo::getShowEndTime, ZonedDateTime.now()));
    }

    private void doChangeStatus(Long id, Integer status) {
        CompetitiveDisseminateDo disseminateDo = findById(id);
        disseminateDo.setStatus(status);
        update(disseminateDo);
    }


    private static Wrapper<CompetitiveDisseminateDo> buildQueryWrapper(CompetitiveDisseminateQuery query) {
        return Wrappers.<CompetitiveDisseminateDo>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), CompetitiveDisseminateDo::getId, query.getId())
                .eq(Objects.nonNull(query.getTitle()), CompetitiveDisseminateDo::getTitle, query.getTitle())
                .le(Objects.nonNull(query.getShowStartTime()), CompetitiveDisseminateDo::getShowStartTime, query.getShowStartTime())
                .ge(Objects.nonNull(query.getShowEndTime()), CompetitiveDisseminateDo::getShowEndTime, query.getShowEndTime())
                .eq(Objects.nonNull(query.getStatus()), CompetitiveDisseminateDo::getStatus, query.getStatus())
                .eq(Objects.nonNull(query.getShowLocation()), CompetitiveDisseminateDo::getShowLocation, query.getShowLocation())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }

    private static Wrapper<CompetitiveDisseminateDo> buildQueryWrapper(CompetitiveDisseminatePageQuery query) {
        return Wrappers.<CompetitiveDisseminateDo>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), CompetitiveDisseminateDo::getId, query.getId())
                .eq(StringUtils.hasText(query.getTitle()), CompetitiveDisseminateDo::getTitle, query.getTitle())
                .le(Objects.nonNull(query.getShowStartTime()), CompetitiveDisseminateDo::getShowStartTime, query.getShowStartTime())
                .ge(Objects.nonNull(query.getShowEndTime()), CompetitiveDisseminateDo::getShowEndTime, query.getShowEndTime())
                .eq(StringUtils.hasText(query.getShowLocation()), CompetitiveDisseminateDo::getShowLocation, query.getShowLocation())
                .eq(Objects.nonNull(query.getStatus()), CompetitiveDisseminateDo::getStatus, query.getStatus())
                .last("ORDER BY FIELD(`status`, 1) desc, gmt_create desc "); //排序
    }
}
