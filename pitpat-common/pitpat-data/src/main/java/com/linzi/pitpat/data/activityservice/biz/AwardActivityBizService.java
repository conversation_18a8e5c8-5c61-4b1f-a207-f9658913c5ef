package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.LightCityPicsDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ApplicationRewardDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityImpracticalAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.CheatDataDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardTargetConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AmountCurrencyDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiActivityAwardListDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiAmountAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiCouponAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ApiWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQueryUser;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardSendDto;
import com.linzi.pitpat.data.activityservice.model.query.award.CouponAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.MedalAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityWearCacheInfo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MaxAwardVo;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityImpracticalAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entity.AwardConfigMedalDo;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfig;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigAmountCurrencyQuery;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigAmountQuery;
import com.linzi.pitpat.data.awardservice.model.vo.WearAward;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigCouponService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigMedalService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigWearService;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategy;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategyFactory;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.award.ActivityUserAwardPre;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsAddressEntity;
import com.linzi.pitpat.data.mallservice.service.ZnsAddressService;
import com.linzi.pitpat.data.service.award.ActivityUserAwardPreService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserLightCity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserLightCityService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/20 17:05
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AwardActivityBizService {
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final AwardConfigService awardConfigService;
    private final WearsService wearsService;
    private final AwardConfigAmountService awardConfigAmountService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyService;
    private final CouponService couponService;
    private final CouponCurrencyService couponCurrencyService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final MainActivityService mainActivityService;
    private final ZnsUserAccountService userAccountService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final SubActivityService subActivityService;
    private final ActivityParamsService activityParamsService;
    private final UserCouponService userCouponService;
    private final ZnsUserService znsUserService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final ActivityUserAwardPreService activityUserAwardPreService;
    private final UserLightCityService userLightCityService;
    private final ZnsAddressService znsAddressService;
    private final AwardConfigScoreService awardConfigScoreService;
    private final AwardConfigCouponService awardConfigCouponService;
    private final AwardConfigWearService awardConfigWearService;
    private final ActivityImpracticalAwardConfigService impracticalAwardConfigService;
    private final AwardConfigMedalService awardConfigMedalService;
    private final MedalConfigService medalConfigService;


    private final RedisTemplate redisTemplate;
    private final RedissonClient redissonClient;

    /**
     * 查询奖励接口
     *
     * @param query
     * @return
     */
    public List<ActivityAwardConfigDto> queryActivityAwardByActivityId(AwardQuery query) {
        List<ActivityAwardConfigDto> list = new ArrayList<>();
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(query);
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return list;
        }
        List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
        awardConfigDtos.forEach(s -> {
            if (Objects.nonNull(s.getWearType()) && Objects.nonNull(s.getWearValue())) {
                Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(s.getWearType()), s.getWearValue());
                s.setWearName(wear.getWearName());
                s.setWearImageUrl(wear.getWearImageUrl());
            }
        });
        Map<Integer, List<AwardConfigDto>> listMap = awardConfigDtos.stream().collect(Collectors.groupingBy(AwardConfigDto::getSendType));
        listMap.forEach((k, v) -> {
            var activityAwardConfigDto = new ActivityAwardConfigDto();
            activityAwardConfigDto.setType(k);
            List<ActivityAwardDto> awardLists = new ArrayList<>();
            // 里程分组
            if (k.equals(AwardSentTypeEnum.MILEAGE_AWARD.getType())) {
                Map<Integer, List<AwardConfigDto>> mileages = v.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetMileage));
                mileages.forEach((k1, v1) -> {
                    ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                    activityAwardDto.setTargetType(1);
                    activityAwardDto.setTarget(k1);
                    v1.forEach(i -> setAwardBeanField(i, activityAwardDto));
                    awardLists.add(activityAwardDto);
                    awardLists.sort(Comparator.comparing(ActivityAwardDto::getTarget));
                });
            }
            // 时间分组
            else if (k.equals(AwardSentTypeEnum.TIME_AWARD.getType())) {
                Map<Integer, List<AwardConfigDto>> mileages = v.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetTime));
                mileages.forEach((k1, v1) -> {
                    ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                    activityAwardDto.setTargetType(2);
                    activityAwardDto.setTarget(k1 / 60);
                    v1.forEach(i -> setAwardBeanField(i, activityAwardDto));
                    awardLists.add(activityAwardDto);
                    awardLists.sort(Comparator.comparing(ActivityAwardDto::getTarget));
                });
            }
            // 破纪录奖励
            else if (k.equals(AwardSentTypeEnum.RECORD_BREAKING_AWARD.getType()) || k.equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
                Map<Integer, List<AwardConfigDto>> mileages = v.stream().collect(Collectors.groupingBy(AwardConfigDto::getTarget));
                mileages.forEach((k1, v1) -> {
                    ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                    activityAwardDto.setTargetType(query.getActivityTargetType());
                    if (ActivityConstants.TargetTypeEnum.TARGETTYPE_2.getCode().equals(query.getActivityTargetType())) {
                        activityAwardDto.setTarget(k1 / 60);
                    } else {
                        activityAwardDto.setTarget(k1);
                    }
                    v1.forEach(i -> setAwardBeanField(i, activityAwardDto));
                    awardLists.add(activityAwardDto);
                    awardLists.sort(Comparator.comparing(ActivityAwardDto::getTarget));
                });
            }

            // 场次分组
            else if (k.equals(AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType())) {
                Map<Integer, List<AwardConfigDto>> mileages = v.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetCount));
                mileages.forEach((k1, v1) -> {
                    ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                    activityAwardDto.setTargetType(3);
                    activityAwardDto.setTarget(k1);
                    v1.forEach(i -> setAwardBeanField(i, activityAwardDto));
                    awardLists.add(activityAwardDto);
                    awardLists.sort(Comparator.comparing(ActivityAwardDto::getTarget));
                });
            }
            // 排行分组
            else if (k.equals(AwardSentTypeEnum.RANK_AWARD.getType())
                    || k.equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())
                    || k.equals(AwardSentTypeEnum.RANKING_HEAD_REWARD.getType())
                    || k.equals(AwardSentTypeEnum.BEING_CHALLENGED.getType())
                    || k.equals(AwardSentTypeEnum.CHALLENGE_SUCCESS.getType())
                    || k.equals(AwardSentTypeEnum.RANKING_PERSONAL_AWARD.getType())
                    || k.equals(AwardSentTypeEnum.PRESIDENT_AWARD.getType())
            ) {
                Map<String, List<AwardConfigDto>> mileages = v.stream().collect(Collectors.groupingBy(m -> m.getRank() + "-" + m.getRankMax() + ""));
                mileages.forEach((k1, v1) -> {
                    ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                    v1.forEach(i -> setAwardBeanField(i, activityAwardDto));
                    awardLists.add(activityAwardDto);
                    awardLists.sort(Comparator.comparing(ActivityAwardDto::getRankMin));
                });
            } else if (k.equals(AwardSentTypeEnum.APPLICATION_AWARD.getType())) {
                v.forEach(s -> {
                    ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                    WearAwardDto wearAwardDto = new WearAwardDto();
                    BeanUtils.copyProperties(s, wearAwardDto);
                    activityAwardDto.setWearAwardDto(wearAwardDto);
                    awardLists.add(activityAwardDto);
                });

            } else {
                ActivityAwardDto activityAwardDto = new ActivityAwardDto();
                v.forEach(i -> setAwardBeanField(i, activityAwardDto));
                awardLists.add(activityAwardDto);
            }
            activityAwardConfigDto.setAwardLists(awardLists);
            list.add(activityAwardConfigDto);
        });
        return list;
    }

    /**
     * app 查询奖励接口
     *
     * @param query
     * @return
     */
    @DataCache(value = {"query.userId", "query.activityId", "query.hasAwardJudge", "query.subActivityId", "query.isPolyList"})
    public List<ActivityAwardTargetConfigDto> queryAwardByActivityId(AwardQueryUser query) {
        List<ActivityAwardTargetConfigDto> list = new ArrayList<>();
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(query);
        MainActivity service = mainActivityService.findById(query.getActivityId());
        if (service == null) {
            return new ArrayList<>();
        }
        Currency currency = userAccountService.getCurrency(query.getUserId(), query.getActivityId(), query.isHasAwardJudge());

        // 按照目标分组
        Map<Integer, List<ActivityAwardConfig>> configGroupList = configs.stream().collect(Collectors.groupingBy(ActivityAwardConfig::getTarget));
        configGroupList.forEach((k, v) -> {
            var activityAwardTargetConfigDto = new ActivityAwardTargetConfigDto();
            activityAwardTargetConfigDto.setTarget(k);
            List<ApiActivityAwardListDto> awards = new ArrayList<>();
            activityAwardTargetConfigDto.setTarget(k);
            activityAwardTargetConfigDto.setTargetType(service.getTargetType());
            List<Long> collect = v.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                return;
            }
            List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
            awardConfigDtos.forEach(s -> {
                if (Objects.nonNull(s.getWearType()) && Objects.nonNull(s.getWearValue())) {
                    Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(s.getWearType()), s.getWearValue());
                    s.setWearName(wear.getWearName());
                    s.setWearImageUrl(wear.getWearImageUrl());
                }
            });
            Map<Integer, List<AwardConfigDto>> listMap = awardConfigDtos.stream().collect(Collectors.groupingBy(AwardConfigDto::getSendType));
            // 按照奖励类型分组
            listMap.forEach((kk, vv) -> {
                var activityAwardConfigDto = new ApiActivityAwardListDto();
                activityAwardConfigDto.setType(kk);
                List<ApiActivityAwardDto> awardLists = new ArrayList<>();
                // 里程分组
                if (kk.equals(AwardSentTypeEnum.MILEAGE_AWARD.getType())) {
                    Map<Integer, List<AwardConfigDto>> mileages = vv.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetMileage));
                    mileages.forEach((k1, v1) -> {
                        ApiActivityAwardDto activityAwardDto = new ApiActivityAwardDto();
                        activityAwardDto.setTargetType(1);
                        activityAwardDto.setTarget(k1);
                        v1.forEach(i -> setApiAwardBeanField(i, activityAwardDto, query.getUserId(), currency));
                        awardLists.add(activityAwardDto);
                        awardLists.sort(Comparator.comparing(ApiActivityAwardDto::getTarget));
                    });
                }
                // 时间分组
                else if (kk.equals(AwardSentTypeEnum.TIME_AWARD.getType())) {
                    Map<Integer, List<AwardConfigDto>> mileages = vv.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetTime));
                    mileages.forEach((k1, v1) -> {
                        ApiActivityAwardDto activityAwardDto = new ApiActivityAwardDto();
                        activityAwardDto.setTargetType(2);
                        activityAwardDto.setTarget(k1);
                        v1.forEach(i -> setApiAwardBeanField(i, activityAwardDto, query.getUserId(), currency));
                        awardLists.add(activityAwardDto);
                        awardLists.sort(Comparator.comparing(ApiActivityAwardDto::getTarget));
                    });
                }
                // 场次分组
                else if (kk.equals(AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType())) {
                    Map<Integer, List<AwardConfigDto>> count = vv.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetCount));
                    count.forEach((k1, v1) -> {
                        ApiActivityAwardDto activityAwardDto = new ApiActivityAwardDto();
                        activityAwardDto.setTargetType(3);
                        activityAwardDto.setTarget(k1);
                        v1.forEach(i -> setApiAwardBeanField(i, activityAwardDto, query.getUserId(), currency));
                        awardLists.add(activityAwardDto);
                        awardLists.sort(Comparator.comparing(ApiActivityAwardDto::getTarget));
                    });
                }
                // 排行分组
                else if (kk.equals(AwardSentTypeEnum.RANK_AWARD.getType())
                        || kk.equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())
                        || kk.equals(AwardSentTypeEnum.RANKING_HEAD_REWARD.getType())
                        || kk.equals(AwardSentTypeEnum.BEING_CHALLENGED.getType())
                        || kk.equals(AwardSentTypeEnum.CHALLENGE_SUCCESS.getType())
                        || kk.equals(AwardSentTypeEnum.RANKING_PERSONAL_AWARD.getType())
                        || kk.equals(AwardSentTypeEnum.PRESIDENT_AWARD.getType())
                ) {
                    Map<String, List<AwardConfigDto>> mileages = vv.stream().collect(Collectors.groupingBy(m -> m.getRank() + "-" + m.getRankMax() + ""));
                    mileages.forEach((k1, v1) -> {
                        ApiActivityAwardDto activityAwardDto = new ApiActivityAwardDto();
                        v1.forEach(i -> setApiAwardBeanField(i, activityAwardDto, query.getUserId(), currency));
                        awardLists.add(activityAwardDto);
                        awardLists.sort(Comparator.comparing(ApiActivityAwardDto::getRankMin));
                    });
                } else if (kk.equals(AwardSentTypeEnum.APPLICATION_AWARD.getType())) {
                    vv.forEach(s -> {
                        ApiActivityAwardDto activityAwardDto = new ApiActivityAwardDto();
                        ApiWearAwardDto apiWearAwardDto = new ApiWearAwardDto();
                        BeanUtils.copyProperties(s, apiWearAwardDto);
                        activityAwardDto.setWearAwardDto(apiWearAwardDto);
                        awardLists.add(activityAwardDto);
                    });

                } else {
                    ApiActivityAwardDto activityAwardDto = new ApiActivityAwardDto();
                    vv.forEach(i -> setApiAwardBeanField(i, activityAwardDto, query.getUserId(), currency));
                    awardLists.add(activityAwardDto);
                }
                activityAwardConfigDto.setAwards(awardLists);
                awards.add(activityAwardConfigDto);
            });
            //所有奖励统一为一样的数据结构
            activityAwardTargetConfigDto.setTargetAwards(awards);
            //一个target 一个award list
            list.add(activityAwardTargetConfigDto);
        });
        // 排序
        list.sort(Comparator.comparing(ActivityAwardTargetConfigDto::getTarget));
        return list;
    }

    private void setApiAwardBeanField(AwardConfigDto i, ApiActivityAwardDto activityAwardDto, Long userId, Currency currency) {
        BigDecimal amount = i.getAmount();
        if (i.getAwardType() == 1 && Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0) {
            ApiAmountAwardDto apiAmountAwardDto = new ApiAmountAwardDto();
            Long awardAmountId = awardConfigAmountService.findByAwardConfigId(i.getAwardConfigId()).getId();
            // 没有用户信息情况默认 $ 货币,当前currency 有默认值
            apiAmountAwardDto.setCurrency(currency);

            String currencyCode = currency.getCurrencyCode();

            AwardConfigAmountCurrency amountCurrency = awardConfigAmountCurrencyService.findByAmountIdAndCurrencyCode(awardAmountId, currencyCode);
            if (amountCurrency == null) {
                //奖励未配置，查询汇率进行转换
                ExchangeRateConfigEntity rateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(currencyCode);
                if (rateConfigEntity != null) {
                    BigDecimal currencyAmount = rateConfigEntity.getExchangeRate().multiply(amount).setScale(2, RoundingMode.HALF_UP);
                    apiAmountAwardDto.setAmount(currencyAmount);
                }
            } else {
                apiAmountAwardDto.setAmount(amountCurrency.getAmount());
            }
            activityAwardDto.setAmountAwardDto(apiAmountAwardDto);
            if (i.getIsDivide() == 1) {
                activityAwardDto.setAmountIsDivide(1);
            }
        }
        if (i.getAwardType() == 3) {
            if (i.getIsDivide() == 1) {
                activityAwardDto.setScoreIsDivide(1);
            }
            activityAwardDto.setScore(i.getScore());
        }
        if (i.getAwardType() == 2 && StringUtils.hasText(i.getCouponIds())) {
            ApiCouponAwardDto apiCouponAwardDto = new ApiCouponAwardDto();
            List<Long> couponIds = NumberUtils.stringToLong2(i.getCouponIds());
            Coupon coupon = couponService.selectCouponById(couponIds.get(0));
            apiCouponAwardDto.setCouponId(couponIds.get(0));
            apiCouponAwardDto.setCouponType(coupon.getCouponType());
            apiCouponAwardDto.setCouponTitle(coupon.getTitle());
            apiCouponAwardDto.setNum(1);
            String currencyCode = I18nConstant.CurrencyCodeEnum.USD.getCode();
            apiCouponAwardDto.setAmount(coupon.getAmount());
            apiCouponAwardDto.setCurrency(new Currency(I18nConstant.CurrencyCodeEnum.USD.getCode(), I18nConstant.CurrencyCodeEnum.USD.getName(), I18nConstant.CurrencyCodeEnum.USD.getSymbol()));
            if (Objects.nonNull(userId)) {
                currencyCode = currency.getCurrencyCode();
                CouponCurrencyEntity couponCurrencyEntity = couponCurrencyService.selectByCouponIdAndCurrencyCode(coupon.getId(), currencyCode);
                if (Objects.nonNull(couponCurrencyEntity)) {
                    apiCouponAwardDto.setCurrency(currency);
                    apiCouponAwardDto.setAmount(couponCurrencyEntity.getAmount());
                }
            }
            activityAwardDto.setCouponAwardDto(apiCouponAwardDto);
        }
        if (i.getAwardType() == 4 && Objects.nonNull(i.getWearValue())) {
            Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(i.getWearType()), i.getWearValue());
            ApiWearAwardDto apiWearAwardDto = new ApiWearAwardDto();
            apiWearAwardDto.setWearType(Integer.valueOf(i.getWearType()));
            apiWearAwardDto.setWearValue(i.getWearValue());
            apiWearAwardDto.setWearName(wear.getWearName());
            apiWearAwardDto.setWearImageUrl(wear.getWearImageUrl());
            apiWearAwardDto.setExpiredTime(i.getExpiredTime());
            activityAwardDto.setWearAwardDto(apiWearAwardDto);
        }
        activityAwardDto.setRankMax(i.getRankMax());
        activityAwardDto.setRankMin(i.getRank());

    }

    private void setAwardBeanField(AwardConfigDto i, ActivityAwardDto activityAwardDto) {
        if (i.getAwardType() == 1 && Objects.nonNull(i.getAmount()) && i.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            Long awardAmountId = awardConfigAmountService.findByAwardConfigId(i.getAwardConfigId()).getId();
            AwardConfigAmountCurrencyQuery awardConfigAmountCurrencyQuery = AwardConfigAmountCurrencyQuery.builder().awardAmountIdList(List.of(awardAmountId)).build();
            List<AwardConfigAmountCurrency> amountCurrencies = awardConfigAmountCurrencyService.findList(awardConfigAmountCurrencyQuery);
            List<AmountCurrencyDto> currencyDtos = amountCurrencies.stream().map(ac -> {
                Currency currency = I18nConstant.buildCurrency(ac.getCurrencyCode());
                return new AmountCurrencyDto(ac.getCurrencyCode(), currency.getCurrencyName(), currency.getCurrencySymbol(), ac.getAmount());
            }).collect(Collectors.toList());
            activityAwardDto.setAmountLists(currencyDtos);
        }
        if (i.getAwardType() == 3) {
            activityAwardDto.setScore(i.getScore());
        }
        if (i.getAwardType() == 2 && StringUtils.hasText(i.getCouponIds())) {
            Coupon coupon = couponService.selectCouponById(Long.valueOf(i.getCouponIds()));
            CouponAwardDto couponAwardDto = new CouponAwardDto();
            couponAwardDto.setCouponId(Long.valueOf(i.getCouponIds()));
            couponAwardDto.setCouponName(CouponTypeEnum.findNameByType(coupon.getCouponType()) + "：" + coupon.getName());
            couponAwardDto.setCouponTitle(coupon.getTitle());
            activityAwardDto.setCouponAwardDto(couponAwardDto);
        }
        if (i.getAwardType() == 4 && Objects.nonNull(i.getWearValue())) {
            Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(i.getWearType()), i.getWearValue());
            activityAwardDto.setWearAwardDto(new WearAwardDto(Integer.valueOf(i.getWearType()), i.getWearValue(), i.getExpiredTime(), wear.getWearName(), wear.getWearImageUrl()));
        }
        if (i.getAwardType() == 7 && Objects.nonNull(i.getMedalId())) {
            MedalConfig medalConfig = medalConfigService.selectMedalConfigById(i.getMedalId());
            MedalAwardDto medalAwardDto = new MedalAwardDto();
            if (Objects.nonNull(medalConfig)){
                medalAwardDto.setId(medalConfig.getId());
                medalAwardDto.setType(medalConfig.getType());
                medalAwardDto.setName(medalConfig.getName());
            }else {
                log.warn("勋章未找到");
            }

            activityAwardDto.setMedalAwardDto(medalAwardDto);
        }
        activityAwardDto.setRankMax(i.getRankMax());
        activityAwardDto.setRankMin(i.getRank());
    }

    public MaxAwardVo findMaxAwardWithDbCache(Long activityId, String currencyCode, String mainType) {
        MaxAwardVo maxAward = findMaxAward(activityId, currencyCode, null, mainType, null);
        activityParamsService.saveConfigSingleValue(activityId, ActivitySettingConfigEnum.ACTIVITY_MAX_AWARD, JsonUtil.writeString(maxAward));
        return maxAward;
    }

    @DataCache(value = {"activityId", "currencyCode", "mainType", "userId"})
    public MaxAwardVo findMaxAward(Long activityId, String currencyCode, Integer target, String mainType, Long userId) {
        MaxAwardVo vo = new MaxAwardVo();
        AwardQueryUser query = new AwardQueryUser();
        query.setActivityId(activityId);
        if (Objects.nonNull(userId)) {
            query.setUserId(userId);
        }
        query.setHasAwardJudge(true);
        List<ActivityAwardTargetConfigDto> activityAwardConfigDtos = queryAwardByActivityId(query);
        //当前不考虑用户目标
        boolean competitiveActivity = competitiveSeasonBizService.isCompetitiveActivity(activityId);
        setMaxAwardVo(vo, activityAwardConfigDtos, currencyCode, null, competitiveActivity);

        //聚合子活动
        if (StringUtils.hasText(mainType) && MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainType)) {
            //查询子活动
            List<MainActivity> subActivityList = seriesActivityRelService.getAllMainActivity(activityId);
            for (MainActivity mainActivity : subActivityList) {

                List<SubActivity> subActivities = subActivityService.getAllSingleActByMain(mainActivity.getId());
                for (SubActivity subActivity : subActivities) {
                    AwardQueryUser subQuery = new AwardQueryUser();
                    subQuery.setActivityId(mainActivity.getId());
                    subQuery.setSubActivityId(subActivity.getId());
                    subQuery.setUserId(userId);
                    subQuery.setHasAwardJudge(true);
                    List<ActivityAwardTargetConfigDto> subActivityAwardConfigDtos = queryAwardByActivityId(subQuery);
                    setMaxAwardVo(vo, subActivityAwardConfigDtos, currencyCode, null, competitiveActivity);
                }
                log.info("活动奖励为：{},actId :{}", vo.getMaxReward(), mainActivity.getId());

            }
        }

        return vo;
    }

    /**
     * 设置最大奖励
     *
     * @param vo
     * @param activityAwardConfigDtos
     * @param currencyCode
     * @param target
     * @param isCompetitiveActivity   是否是竞技赛
     */
    private void setMaxAwardVo(MaxAwardVo vo, List<ActivityAwardTargetConfigDto> activityAwardConfigDtos, String currencyCode, Integer target, boolean isCompetitiveActivity) {
        if (CollectionUtils.isEmpty(activityAwardConfigDtos)) {
            return;
        }
//        按照目标倒叙进行排序
        activityAwardConfigDtos = activityAwardConfigDtos.stream().sorted(Comparator.comparing(ActivityAwardTargetConfigDto::getTarget).reversed()).collect(Collectors.toList());
        ActivityAwardTargetConfigDto activityAwardTargetConfigDto = activityAwardConfigDtos.get(0);
        if (Objects.nonNull(target) && target > 0) {
            //指定了目标，获取对应的目标奖励
            activityAwardTargetConfigDto = activityAwardConfigDtos.stream().filter(e -> target.equals(e.getTarget())).findFirst().orElse(activityAwardTargetConfigDto);
        }
        List<ApiActivityAwardListDto> targetAwards = activityAwardTargetConfigDto.getTargetAwards();
        for (ApiActivityAwardListDto activityAwardConfigDto : targetAwards) {
            List<ApiActivityAwardDto> awardLists = activityAwardConfigDto.getAwards();
            if (activityAwardConfigDto.getType() == 1) {
                //是否有目标
                if (Objects.nonNull(target)) {
                    ApiActivityAwardDto awardDto = awardLists.stream().filter(a -> target.equals(a.getTarget()) && 1 >= a.getRankMin() && 1 <= a.getRankMax()).findFirst().orElse(null);
                    if (Objects.nonNull(awardDto)) {
                        setMacAward(vo, awardDto, currencyCode);
                        continue;
                    }
                }
                ApiActivityAwardDto awardDto = awardLists.stream().filter(a -> 1 >= a.getRankMin() && 1 <= a.getRankMax())
                        .sorted(Comparator.comparing(ApiActivityAwardDto::getTarget).reversed()).findFirst().orElse(null);

                if (Objects.nonNull(awardDto)) {
                    setMacAward(vo, awardDto, currencyCode);
                }
            } else if (activityAwardConfigDto.getType() == 2 || activityAwardConfigDto.getType() == 8) {
                //是否有目标
                if (Objects.nonNull(target)) {
                    ApiActivityAwardDto awardDto = awardLists.stream().filter(a -> target.equals(a.getTarget())).findFirst().orElse(null);
                    if (Objects.nonNull(awardDto)) {
                        if (activityAwardConfigDto.getType() == 2) {
                            //完赛奖励需要处理均分
                            setMaxDistributeAward(vo, awardDto, currencyCode);
                        } else {
                            setMacAward(vo, awardDto, currencyCode);
                        }
                        continue;
                    }
                }
                ApiActivityAwardDto awardDto = awardLists.stream().sorted(Comparator.comparing(ApiActivityAwardDto::getTarget)).findFirst().orElse(null);
                if (Objects.nonNull(awardDto)) {
                    if (activityAwardConfigDto.getType() == 2) {
                        //完赛奖励，需要处理均分
                        setMaxDistributeAward(vo, awardDto, currencyCode);
                    } else {
                        setMacAward(vo, awardDto, currencyCode);
                    }
                }
                log.info("奖励log，添加完赛奖励{}", vo.getMaxReward());
            } else if (activityAwardConfigDto.getType() == AwardSentTypeEnum.TIME_AWARD.getType() || activityAwardConfigDto.getType() == AwardSentTypeEnum.MILEAGE_AWARD.getType()) {
                for (ApiActivityAwardDto awardDto : awardLists) {
                    setMacAward(vo, awardDto, currencyCode);
                    log.info("奖励log，添加时间里程奖励{}", vo.getMaxReward());
                }
            } else if (AwardSentTypeEnum.SURPASS_AWARD.getType().equals(activityAwardConfigDto.getType())) {
                //增加超越奖励到累计奖金中
                for (ApiActivityAwardDto awardItem : awardLists) {
                    setMaxDistributeAward(vo, awardItem, currencyCode);
                }
            } else if (AwardSentTypeEnum.RECORD_BREAKING_AWARD.getType().equals(activityAwardConfigDto.getType()) && isCompetitiveActivity) {
                for (ApiActivityAwardDto awardList : awardLists) {
                    setMacAward(vo, awardList, currencyCode);
                    log.info("奖励log，破纪录奖励{}", vo.getMaxReward());
                }
            }
        }
    }

    /**
     * 需要被均价的奖励，进行累加
     *
     * @param vo
     * @param awardDto
     * @param currencyCode
     */
    private void setMaxDistributeAward(MaxAwardVo vo, ApiActivityAwardDto awardDto, String currencyCode) {
        if (Objects.isNull(awardDto)) {
            return;
        }
        if (Objects.nonNull(awardDto.getAmountAwardDto())) {
            if (Objects.equals(1, awardDto.getAmountIsDivide())) {
                vo.addDistributeReward(awardDto.getAmountAwardDto().getAmount());
            } else {
                vo.addMaxReward(awardDto.getAmountAwardDto().getAmount());
            }
        }
        if (Objects.nonNull(awardDto.getScoreIsDivide()) && Objects.nonNull(awardDto.getScore())) {
            if (Objects.equals(1, awardDto.getScoreIsDivide())) {
                vo.addDistributeScore(awardDto.getScore());
            } else {
                vo.addMaxScore(awardDto.getScore());
            }
        }
        //优惠券
        ApiCouponAwardDto couponAwardDto = awardDto.getCouponAwardDto();
        if (couponAwardDto != null && couponAwardDto.getNum() > 0) {
            for (int i = 0; i < couponAwardDto.getNum(); i++) {
                vo.addCouponId(couponAwardDto.getCouponId());
            }
        }
    }


    /**
     * 设置最大奖励金额、积分
     * 对奖励进行累加
     *
     * @param vo
     * @param awardDto
     * @param currencyCode
     */
    private void setMacAward(MaxAwardVo vo, ApiActivityAwardDto awardDto, String currencyCode) {
        BigDecimal maxReward = vo.getMaxReward();
        Integer maxScore = vo.getMaxScore();
        if (Objects.nonNull(awardDto.getAmountAwardDto())) {
            maxReward = maxReward.add(awardDto.getAmountAwardDto().getAmount());
        }
        if (NumberUtils.geZero(awardDto.getScore())) {
            maxScore += awardDto.getScore();
        }
        vo.setMaxScore(maxScore);
        vo.setMaxReward(maxReward);
        //优惠券
        ApiCouponAwardDto couponAwardDto = awardDto.getCouponAwardDto();
        if (couponAwardDto != null && couponAwardDto.getNum() > 0) {
            for (int i = 0; i < couponAwardDto.getNum(); i++) {
                vo.addCouponId(couponAwardDto.getCouponId());
            }
        }
    }

    /**
     * 复制保存奖励
     *
     * @param mainActivity
     * @return
     */
    public List<ActivityTargetAwardDto> getAwardByAct(MainActivity mainActivity) {
        Long mainActId = mainActivity.getId();
        List<SubActivity> subActivities = subActivityService.getAllSingleActByMain(mainActId);
        List<ActivityTargetAwardDto> targetAwardDtos = subActivities.stream().map(k -> {
            AwardQuery awardQuery = new AwardQuery();
            awardQuery.setActivityId(mainActId);
            awardQuery.setSubActivityId(k.getId());
            awardQuery.setActivityTargetType(mainActivity.getTargetType());
            List<ActivityAwardConfigDto> activityAwardConfigDtos = queryActivityAwardByActivityId(awardQuery);

            ActivityTargetAwardDto targetAwardDto = new ActivityTargetAwardDto();
            if (mainActivity.getTargetType() == 2) {
                targetAwardDto.setTarget(k.getTarget() / 60);
            } else {
                targetAwardDto.setTarget(k.getTarget());
            }

            targetAwardDto.setAwardDtos(activityAwardConfigDtos);
            return targetAwardDto;

        }).toList();


        return targetAwardDtos;
    }

    /**
     * 发送活动奖励
     *
     * @param dto
     */
    public void sendActivityAwardByConfigAndStage(AwardSendDto dto) {
        log.info("sendActivityAwardByConfigAndStage dto:{}", dto);
        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(dto.getActivityId());
        awardQuery.setSubActivityId(dto.getSubActivityId());
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        if (!CollectionUtils.isEmpty(configs)) {
            List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
            List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
            if (dto.getType().equals(AwardSentTypeEnum.MILEAGE_AWARD.getType())) {
                awardConfigDtos = awardConfigDtos.stream()
                        .filter(i -> i.getSendType().equals(dto.getType()))
                        .filter(j -> Objects.isNull(dto.getTarget()) || dto.getTarget() == 0 || j.getTarget().equals(dto.getTarget()))
                        .filter(k -> k.getTargetMileage().equals(dto.getTargetMileage())).toList();
            } else if (dto.getType().equals(AwardSentTypeEnum.TIME_AWARD.getType())) {
                awardConfigDtos = awardConfigDtos.stream()
                        .filter(i -> i.getSendType().equals(dto.getType()))
                        .filter(j -> Objects.isNull(dto.getTarget()) || dto.getTarget() == 0 || j.getTarget().equals(dto.getTarget()))
                        .filter(k -> k.getTargetTime().equals(dto.getTargetTime())).toList();
            } else if (dto.getType().equals(AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType())) {
                awardConfigDtos = awardConfigDtos.stream()
                        .filter(i -> i.getSendType().equals(dto.getType()))
                        .filter(k -> k.getTargetCount().equals(dto.getTargetCount())).toList();
            } else if (dto.getType().equals(AwardSentTypeEnum.PRESIDENT_AWARD.getType())
                    || dto.getType().equals(AwardSentTypeEnum.RANKING_PERSONAL_AWARD.getType())) {
                awardConfigDtos = awardConfigDtos.stream()
                        .filter(i -> i.getSendType().equals(dto.getType())).toList();
            } else {
                awardConfigDtos = awardConfigDtos.stream()
                        .filter(i -> i.getSendType().equals(dto.getType()))
                        .filter(k -> k.getTarget().equals(dto.getTarget())).toList();
            }

            if (Objects.nonNull(dto.getRank()) && (dto.getType().equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())
                    || dto.getType().equals(AwardSentTypeEnum.RANKING_HEAD_REWARD.getType())
                    || dto.getType().equals(AwardSentTypeEnum.RANK_AWARD.getType())
                    || dto.getType().equals(AwardSentTypeEnum.TIME_AWARD.getType())
                    || dto.getType().equals(AwardSentTypeEnum.MILEAGE_AWARD.getType())
                    || dto.getType().equals(AwardSentTypeEnum.CHALLENGE_SUCCESS.getType())
                    || dto.getType().equals(AwardSentTypeEnum.BEING_CHALLENGED.getType())
                    || dto.getType().equals(AwardSentTypeEnum.PRESIDENT_AWARD.getType())
                    || dto.getType().equals(AwardSentTypeEnum.RANKING_PERSONAL_AWARD.getType())
            )) {
                // 过滤之后的奖励
                awardConfigDtos = awardConfigDtos.stream().filter(i -> dto.getRank() >= i.getRank() && dto.getRank() <= i.getRankMax()).collect(Collectors.toList());
            } else if (dto.getType().equals(AwardSentTypeEnum.SURPASS_AWARD.getType()) || dto.getType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType())) {
                //非瓜分下通过人数过滤
                awardConfigDtos = awardConfigDtos.stream().filter(j -> j.getIsDivide() == 0 || (j.getIsDivide() == 1 && NumberUtils.geZero(dto.getDivideUserCount()))).collect(Collectors.toList());
            }

            // 排名奖励预存
            if (AwardSentTypeEnum.examineAwardTypes().contains(dto.getType())
                    && activityParamsService.checkAwardSendType(dto.getActivityId(), dto.getType())) {
                MainActivity mainActivity = mainActivityService.findById(dto.getActivityId());
                Integer awardSendStatus = mainActivity.getAwardSendStatus();
                log.info("开启人工审核奖开启：dto{},awardSendStatus:{}", JsonUtil.writeString(dto), awardSendStatus);
                if (awardSendStatus == 0) {
                    savePreAward(dto, awardConfigDtos);
                    return;
                }
                if (awardSendStatus == 1) {
                    log.info("奖励发放已经结束");
                    return;
                }
                if (awardSendStatus == 2) {
                    updatePreAward(dto.getUserId(), dto.getActivityId());
                }
            }

            // 切换币种不发放奖励判断,过滤金额奖励
            List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + dto.getUserId(), 0, -1);
            if (!CollectionUtils.isEmpty(list) && list.contains(dto.getActivityId().toString())) {
                awardConfigDtos = awardConfigDtos.stream().filter(a -> !AwardTypeEnum.AMOUNT.getType().equals(a.getAwardType())).collect(Collectors.toList());
            }

            //翻倍券判断
            UserCoupon userCoupon = userCouponService.getUserCouponByActIdUserIdCouponType(dto.getActivityId(), dto.getUserId(), Arrays.asList(CouponTypeEnum.DOUBLE_REWARD_COUPON.getCode()));
            if (Objects.nonNull(userCoupon)) {
                dto.setDoubleUserCoupon(userCoupon);
            }

            String batchNo = OrderUtil.getBatchNo();
            if (!CollectionUtils.isEmpty(awardConfigDtos)) {
                Map<Integer, List<AwardConfigDto>> listMap = awardConfigDtos.stream().collect(Collectors.groupingBy(AwardConfigDto::getAwardType));
                listMap.forEach((k, v) -> {
                    AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(k);
                    List<Long> longList = v.stream().map(AwardConfigDto::getAwardConfigId).collect(Collectors.toList());
                    awardProcessStrategy.awardSendProcess(longList, dto, batchNo);
                });
            } else if (dto.getType().equals(AwardSentTypeEnum.COMPLETING_THE_GAME.getType()) ||
                    dto.getType().equals(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType())) {
                // 无具体奖励发送完赛 勋章/证书
                AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(1);
                awardProcessStrategy.awardSendProcess(new ArrayList<>(), dto, null);
            }
        } else {
            // 无具体奖励发送完赛 勋章/证书
            AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(1);
            awardProcessStrategy.awardSendProcess(new ArrayList<>(), dto, null);
        }
    }

    /**
     * 人工审核奖励保存
     *
     * @param dto
     * @param awardConfigDtos
     */
    private void savePreAward(AwardSendDto dto, List<AwardConfigDto> awardConfigDtos) {
        var awardPre = activityUserAwardPreService.selectByActivityIdAndUserId(dto.getActivityId(), dto.getUserId());
        if (Objects.nonNull(awardPre)) {
            if (dto.getRank() != null && dto.getRank() > 0) {
                awardPre.setRank(dto.getRank());
                activityUserAwardPreService.update(awardPre);
            }
            return;
        }

        var activityUserAwardPre = new ActivityUserAwardPre();
        activityUserAwardPre.setActivityId(dto.getActivityId());
        activityUserAwardPre.setUserId(dto.getUserId());
        ZnsUserEntity user = znsUserService.findById(dto.getUserId());
        activityUserAwardPre.setUserCode(user.getUserCode());
        activityUserAwardPre.setStatus(0);
        activityUserAwardPre.setRank(dto.getRank());
        // 作弊数据query
        CheatDataDto cheatDataDto = userRunDataDetailsCheatService.getCheatData(dto.getDetailId());
        activityUserAwardPre.setCheatingTime(cheatDataDto.getCheatingTime());
        activityUserAwardPre.setCheatingDistance(cheatDataDto.getCheatingDistance());
        activityUserAwardPreService.insert(activityUserAwardPre);
    }

    private void updatePreAward(Long userId, Long activityId) {
        var awardPre = activityUserAwardPreService.selectByActivityIdAndUserId(activityId, userId);
        if (Objects.nonNull(awardPre)) {
            awardPre.setStatus(YesNoStatus.YES.getCode());
            activityUserAwardPreService.update(awardPre);
        }
    }

    public void sendActivityAwardByConfig(AwardConfigDto awardConfigType, AwardSendDto dto) {
        if (Objects.isNull(awardConfigType)) {
            log.info("sendActivityAwardByConfig end,awardConfigType is null");
            return;
        }
        AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(awardConfigType.getAwardType());
        awardProcessStrategy.awardSendProcess(Arrays.asList(awardConfigType.getAwardConfigId()), dto, dto.getTotalBatchNo());
    }

    public void sendAmount(ZnsRunActivityUserEntity activityUser, BigDecimal amount, Integer tradeType, Integer tradeSubtype) {
        AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(1);
        AwardSendDto dto = new AwardSendDto().setActivityId(activityUser.getActivityId()).setUserId(activityUser.getUserId()).setType(tradeSubtype);
        awardProcessStrategy.amountSendProcess(amount, tradeType, tradeSubtype, dto);
    }

    /**
     * 点亮城市
     *
     * @param disseminate
     * @param user
     * @param level
     */
    public void sendUserLightCity(ActivityDisseminate disseminate, ZnsUserEntity user, Integer level) {
        if (Objects.isNull(disseminate) || !StringUtils.hasText(disseminate.getLightCityPics()) || Objects.isNull(level)) {
            return;
        }
        List<LightCityPicsDto> lightCityPicsDtos = JsonUtil.readList(disseminate.getLightCityPics(), LightCityPicsDto.class);
        if (level < 1 || lightCityPicsDtos.size() < 1) {
            return;
        }
        if (level > lightCityPicsDtos.size()) {
            return;
        }
        LightCityPicsDto dto = lightCityPicsDtos.get(level - 1);
        if (Objects.isNull(dto) || Objects.isNull(dto.getCityId())) {
            return;
        }
        //点亮城市
        log.info("累计跑关卡完成，点亮城市记录  cityId = " + dto.getCityId());
        UserLightCity userLightCityRecord = userLightCityService.findByCityIdAndUserId(dto.getCityId(), user.getId());
        if (Objects.nonNull(userLightCityRecord)) {
            log.info("当前城市已经被点亮");
            return;
        }
        ZnsAddressEntity city = znsAddressService.findById(dto.getCityId());
        if (Objects.nonNull(city)) {
            UserLightCity userLightCity = new UserLightCity();
            userLightCity.setCityId(dto.getCityId());
            userLightCity.setUserId(user.getId());
            userLightCity.setCityName(city.getName());
            userLightCity.setStateId(city.getParentId());
            ZnsAddressEntity state = znsAddressService.findById(city.getParentId());
            userLightCity.setStateName(state.getName());
            userLightCity.setModifieTime(ZonedDateTime.now());
            log.info("点亮城市图片地址 url = " + city.getCityUrl());
            if (StringUtils.hasText(city.getCityUrl())) {
                userLightCity.setCityUrl(city.getCityUrl());
            } else {
                userLightCity.setCityUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202308/ilC13xE5VeCb1256.png");
            }
            userLightCityService.insert(userLightCity);
        }
    }


    /**
     * 保存目标奖励
     *
     * @param targetAwardDtos
     * @param mainActivity
     * @param subActivityList
     */
    //只有sub - main 1对1可用
    public void saveTargetAward(List<ActivityTargetAwardDto> targetAwardDtos, MainActivity mainActivity, List<SubActivity> subActivityList) {
        for (ActivityTargetAwardDto targetAwardDto : targetAwardDtos) {
            //注意 当目标类型是时间，target前端传的是min，数据库存的是秒
            Integer target = targetAwardDto.getTarget();
            SubActivity subActivity = subActivityList.stream().filter(k -> k.getTarget() == (mainActivity.getTargetType() == 2 ? target * 60 : target))
                    .findFirst().get();
            //获得奖励
            List<ActivityAwardConfigDto> awardDtos = targetAwardDto.getAwardDtos();
            for (ActivityAwardConfigDto awardDto : awardDtos) {
                for (ActivityAwardDto dto : awardDto.getAwardLists()) {
                    dto.setActivityId(subActivity.getMainActivityId());
                    dto.setSubActivityId(subActivity.getId());
                }
            }
            saveBatchAward(awardDtos, targetAwardDto.getTarget(), mainActivity.getTargetType());
            //服装奖励缓存
            wearCache(awardDtos, mainActivity);
        }
    }

    /**
     * 保存活动奖励 批量
     *
     * @param list
     * @param target
     * @param targetType
     */
    private void saveBatchAward(List<ActivityAwardConfigDto> list, Integer target, Integer targetType) {
        list.parallelStream().forEach(k -> k.getAwardLists().parallelStream().forEach(i -> this.saveActivityAwardConfig(i, k.getType(), target, targetType)));
    }

    /**
     * 保存活动奖励
     *
     * @param dto
     * @param type
     * @param target
     * @param targetType
     */
    private void saveActivityAwardConfig(ActivityAwardDto dto, Integer type, Integer target, Integer targetType) {
        // 保存勋章奖励
        if (dto.getMedalAwardDto() != null){
            AwardConfig config = saveAwardConfig(type, AwardTypeEnum.MEDAL.getType());
            AwardConfigMedalDo awardMedalDo = new AwardConfigMedalDo();
            awardMedalDo.setAwardConfigId(config.getId());
            awardMedalDo.setMedalId(dto.getMedalAwardDto().getId());
            awardConfigMedalService.create(awardMedalDo);
            saveActivityAwardConfig(dto, config, target, targetType, null);
        }
        if (!CollectionUtils.isEmpty(dto.getAmountLists())) {
            AwardConfig config = saveAwardConfig(type, AwardTypeEnum.AMOUNT.getType());
            //金额奖励配置
            AwardConfigAmount amount = new AwardConfigAmount();
            List<AmountCurrencyDto> amountLists = dto.getAmountLists();
            amountLists.forEach(i -> {
                if (i.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
                    amount.setAmount(i.getAmount());
                }
            });
            amount.setAwardConfigId(config.getId());
            awardConfigAmountService.insertAwardConfigAmount(amount);
            List<AwardConfigAmountCurrency> awardConfigAmountCurrencys = new ArrayList<>();
            amountLists.forEach(i -> {
                AwardConfigAmountCurrency awardConfigAmountCurrency = new AwardConfigAmountCurrency();
                awardConfigAmountCurrency.setAwardAmountId(amount.getId());
                String currencyCode = i.getCurrencyCode();
                BigDecimal amountCurrency = I18nConstant.currencyFormat(currencyCode, i.getAmount());
                awardConfigAmountCurrency.setAmount(amountCurrency);
                awardConfigAmountCurrency.setCurrencyCode(currencyCode);
                awardConfigAmountCurrencys.add(awardConfigAmountCurrency);
            });
            awardConfigAmountCurrencyService.saveBatch(awardConfigAmountCurrencys);
            //奖励活动配置关联
            saveActivityAwardConfig(dto, config, target, targetType, dto.getIsDivideAmount());
        }
        if (Objects.nonNull(dto.getScore()) && dto.getScore() > 0) {
            AwardConfig config = saveAwardConfig(type, AwardTypeEnum.SCORE.getType());
            AwardConfigScore awardConfigScore = new AwardConfigScore();
            awardConfigScore.setAwardConfigId(config.getId());
            awardConfigScore.setScore(dto.getScore());
            awardConfigScoreService.insertAwardConfigScore(awardConfigScore);
            //奖励活动配置关联
            saveActivityAwardConfig(dto, config, target, targetType, dto.getIsDivideScore());
        }
        if (Objects.nonNull(dto.getCouponAwardDto())) {
            AwardConfig config = saveAwardConfig(type, AwardTypeEnum.COUPON.getType());
            awardConfigCouponService.addAwardConfig(config.getId(), String.valueOf(dto.getCouponAwardDto().getCouponId()));
            //奖励活动配置关联
            saveActivityAwardConfig(dto, config, target, targetType, null);
        }
        if (Objects.nonNull(dto.getWearAwardDto())) {
            AwardConfig config = saveAwardConfig(type, AwardTypeEnum.WEAR.getType());
            WearAwardDto awardDto = dto.getWearAwardDto();
            awardConfigWearService.addAwardConfig(config.getId(), awardDto.getWearType().toString(), awardDto.getWearValue(), "", "", awardDto.getExpiredTime());
            saveActivityAwardConfig(dto, config, target, targetType, null);
        }
    }

    private AwardConfig saveAwardConfig(Integer sendType, Integer awardType) {
        AwardConfig config = new AwardConfig();
        config.setSendType(sendType);
        config.setAwardType(awardType);
        awardConfigService.insertAwardConfig(config);
        return config;
    }

    /**
     * 奖励活动配置关联
     *
     * @param dto
     * @param config
     * @param target
     * @param isDivide
     */
    private void saveActivityAwardConfig(ActivityAwardDto dto, AwardConfig config, Integer target, Integer targetType, Integer isDivide) {
        ActivityAwardConfig activityAwardConfig = new ActivityAwardConfig();
        activityAwardConfig.setActivityId(dto.getActivityId());
        activityAwardConfig.setAwardId(config.getId());
        activityAwardConfig.setSubActivityId(dto.getSubActivityId());
        activityAwardConfig.setPic(dto.getPic());
        activityAwardConfig.setTarget(target);
        if (targetType == 2) {
            activityAwardConfig.setTarget(target * 60);
        }
        activityAwardConfig.setRank(dto.getRankMin());
        activityAwardConfig.setRankMax(dto.getRankMax());
        if (dto.getRankMin() != null && dto.getRankMax() != null && dto.getRankMin() > 0 && dto.getRankMax() == 0) {
            // 最大值
            activityAwardConfig.setRankMax(99999);
        }
        if (config.getSendType().equals(AwardSentTypeEnum.TIME_AWARD.getType())) {
            activityAwardConfig.setTargetTime(dto.getTarget() * 60);
        }
        if (config.getSendType().equals(AwardSentTypeEnum.MILEAGE_AWARD.getType())) {
            activityAwardConfig.setTargetMileage(dto.getTarget());
        }
        if (config.getSendType().equals(AwardSentTypeEnum.PERSONAL_STAGE_AWARD.getType())) {
            activityAwardConfig.setTargetCount(dto.getTarget());
        }
        activityAwardConfig.setIsDivide(isDivide);
        activityAwardConfigService.insert(activityAwardConfig);
    }

    /**
     * 服装缓存
     *
     * @param awardConfigDtoList
     * @param mainActivity
     */
    private void wearCache(List<ActivityAwardConfigDto> awardConfigDtoList, MainActivity mainActivity) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(awardConfigDtoList)) {
            return;
        }
        //段位赛不保存
        if (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
            return;
        }
        //道具赛不保存
        if (MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
            return;
        }
        RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
        if (!CollectionUtils.isEmpty(list)) {
            list.iterator().forEachRemaining(info -> {
                if (info.getActivityId().equals(mainActivity.getId())) {
                    list.remove(info);
                }
            });
        }
        List<ActivityAwardDto> wearAwardList = awardConfigDtoList.stream().map(ActivityAwardConfigDto::getAwardLists).flatMap(Collection::stream).filter(a -> Objects.nonNull(a.getWearAwardDto())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(wearAwardList)) {
            return;
        }
        List<WearAward> wearList = new ArrayList<>();
        for (ActivityAwardDto awardDto : wearAwardList) {
            WearAwardDto wearAwardDto = awardDto.getWearAwardDto();
            WearAward wearAward = new WearAward();
            BeanUtils.copyProperties(wearAwardDto, wearAward);
            wearAward.setWearId(wearAwardDto.getWearValue());
            wearList.add(wearAward);
        }

        ActivityWearCacheInfo activityWearCacheInfo = new ActivityWearCacheInfo();
        activityWearCacheInfo.setActivityId(mainActivity.getId());
        activityWearCacheInfo.setExpireTime(DateTimeUtil.parse(mainActivity.getApplicationEndTime()));
        activityWearCacheInfo.setWearList(wearList);
        list.add(activityWearCacheInfo);
    }

    /**
     * 删除奖励配置
     *
     * @param awardQuery
     */
    public void deleteAwardConfig(AwardQuery awardQuery) {
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        if (!CollectionUtils.isEmpty(configs)) {
            List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
            List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
            Map<Integer, List<AwardConfigDto>> listMap = awardConfigDtos.stream().collect(Collectors.groupingBy(AwardConfigDto::getAwardType));
            // 删除明细配置表
            listMap.forEach((k, v) -> {
                AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(k);
                List<Long> longList = v.stream().map(AwardConfigDto::getAwardConfigId).collect(Collectors.toList());
                awardProcessStrategy.deleteAwardConfig(longList);
            });
            List<Long> longs = configs.stream().map(ActivityAwardConfig::getId).toList();
            //删除主配置表
            activityAwardConfigService.removeBatchByIds(longs);
        }
    }

    //查询报名奖励--服装参赛包
    public ApplicationRewardDto queryApplicationAward(Long activityId) {
        ApplicationRewardDto applicationRewardDto = new ApplicationRewardDto();
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.isNull(mainActivity)) {
            return applicationRewardDto;
        }
        //系列赛要获取主赛事活动
        if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SERIES_SUB.getType())) {
            MainActivity main = seriesActivityRelService.getMainActivityBySegmentActId(activityId);
            activityId = main.getId();
        }
        List<AwardConfigDto> awardConfigDtos = activityAwardConfigService.selectAwardConfigDtoListBySendTypes(activityId, Arrays.asList(AwardSentTypeEnum.APPLICATION_AWARD.getType()), AwardTypeEnum.WEAR.getType());
        if (CollectionUtils.isEmpty(awardConfigDtos)) {
            return null;
        }
        awardConfigDtos.forEach(s -> {
            if (Objects.nonNull(s.getWearType()) && Objects.nonNull(s.getWearValue())) {
                Wears wear = wearsService.getWearByWearIdAndType(Integer.valueOf(s.getWearType()), s.getWearValue());
                s.setWearName(wear.getWearName());
                s.setWearImageUrl(wear.getWearImageUrl());
            }
        });
        List<WearAwardDto> wearLists = new ArrayList<>();
        awardConfigDtos.forEach(s -> {
            WearAwardDto wearAwardDto = new WearAwardDto();
            BeanUtils.copyProperties(s, wearAwardDto);
            wearAwardDto.setWearType(Integer.valueOf(s.getWearType()));
            wearLists.add(wearAwardDto);
        });

        ActivityImpracticalAwardConfig impracticalAwardConfig = impracticalAwardConfigService.findByActId(activityId);
        applicationRewardDto.setWearAwards(wearLists);
        applicationRewardDto.setAwardSentType(AwardSentTypeEnum.APPLICATION_AWARD.getType());
        applicationRewardDto.setIsMustWear(Objects.nonNull(impracticalAwardConfig) ? impracticalAwardConfig.getIsMustWear() : null);
        return applicationRewardDto;
    }

    @Deprecated
    public void cleanupAward(List<ActivityAwardTargetConfigDto> awards, Integer appVersion) {
        if (appVersion < 4023) {
            for (ActivityAwardTargetConfigDto award : awards) {
                award.getTargetAwards().removeIf(targetAward -> AwardSentTypeEnum.SURPASS_AWARD.getType().equals(targetAward.getType()));
            }
        }
//            老版本，不显示破纪录奖励
        if (appVersion < VersionConstant.V4_5_0) {
            for (ActivityAwardTargetConfigDto award : awards) {
                award.getTargetAwards().removeIf(targetAward -> AwardSentTypeEnum.RECORD_BREAKING_AWARD.getType().equals(targetAward.getType()));
            }
        }
    }

    /**
     * 删除指定活动的指定类型奖励
     *
     * @param activityId
     * @param awardSentTypeEnum
     */
    public void removeAwardByType(Long activityId, AwardSentTypeEnum awardSentTypeEnum) {
        if (activityId == null || awardSentTypeEnum == null) {
            return;
        }
        List<AwardConfigDto> awardConfigDtos = activityAwardConfigService.selectAwardConfigDtoList(activityId, awardSentTypeEnum.getType(), null);
        if (!CollectionUtils.isEmpty(awardConfigDtos)) {
            activityAwardConfigService.deleteByIds(awardConfigDtos.stream().map(AwardConfigDto::getActivityAwardId).toList());
            List<Long> awardIds = awardConfigDtos.stream().map(AwardConfigDto::getAwardConfigId).toList();
            awardConfigService.deleteBatchIds(awardIds);
            awardConfigCouponService.deleteByAwardIds(awardIds);
            AwardConfigAmountQuery query = AwardConfigAmountQuery.builder()
                    .awardConfigIdList(awardIds)
                    .build();
            awardConfigAmountService.deleteByAwardIds(awardIds);
            List<AwardConfigAmount> awardConfigAmounts = awardConfigAmountService.findList(query);
            if (!CollectionUtils.isEmpty(awardConfigAmounts)) {
                awardConfigAmountCurrencyService.deleteByAwardAmountIds(awardConfigAmounts.stream().map(AwardConfigAmount::getId).toList());
            }
            awardConfigScoreService.deleteByAwardIds(awardIds);
            awardConfigWearService.deleteByAwardIds(awardIds);
        }
    }
}
