package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-07-18
 */

@Data
@NoArgsConstructor
@TableName("zns_account_withdrawal_user")
public class AccountWithdrawalUser implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.AccountWithdrawalUser:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                         // 主键
    public final static String gmt_create = CLASS_NAME + "gmt_create";          // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";      // 修改时间
    public final static String is_delete = CLASS_NAME + "is_delete";            // 是否有效0有效1-无效
    public final static String email_address = CLASS_NAME + "email_address";    // 有提现转账权限的人的邮箱
    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //是否有效0有效1-无效
    private Integer isDelete;
    //有提现转账权限的人的邮箱
    private String emailAddress;

    @Override
    public String toString() {
        return "AccountWithdrawalUser{" +
                ",id=" + id +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",isDelete=" + isDelete +
                ",emailAddress=" + emailAddress +
                "}";
    }
}
