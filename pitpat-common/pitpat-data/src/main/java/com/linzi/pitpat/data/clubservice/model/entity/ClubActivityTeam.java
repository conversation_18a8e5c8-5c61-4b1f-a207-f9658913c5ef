package com.linzi.pitpat.data.clubservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubActivityTeamEnrollStateEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_club_activity_team")
public class ClubActivityTeam implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否逻辑删除，0：未删除
    @TableLogic(delval = "UNIX_TIMESTAMP()")
    private Long isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;
    //俱乐部id
    private Long clubId;
    //队伍id
    private Long activityTeamId;
    //活动id
    private Long mainActivityId;
    /**
     * 俱乐部报名状态
     *
     * @see ClubActivityTeamEnrollStateEnum
     */
    private String state;
}
