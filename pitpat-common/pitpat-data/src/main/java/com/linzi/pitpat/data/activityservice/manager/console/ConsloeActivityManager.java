package com.linzi.pitpat.data.activityservice.manager.console;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityAreaBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonRuleValid;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.PolymerizationActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.constant.ActivityStringConstant;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityRecommendPositionEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.SubActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.TeamShareTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveTypeEnum;
import com.linzi.pitpat.data.activityservice.converter.ProActivityConvert;
import com.linzi.pitpat.data.activityservice.converter.api.CompetitiveSeasonConverter;
import com.linzi.pitpat.data.activityservice.dto.VideoViewDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityAwardReviewAdminDto;
import com.linzi.pitpat.data.activityservice.dto.console.ActivityRecommendCommunityDesc;
import com.linzi.pitpat.data.activityservice.dto.console.ActivityRecommendSetting;
import com.linzi.pitpat.data.activityservice.dto.console.ActivityStageDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityConditionQuery;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityListQuery;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityPaceSetting;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityPropConfigDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityRateLimitDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTimeRangeQuery;
import com.linzi.pitpat.data.activityservice.dto.console.request.ApplicationRewardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.BrandRightAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.BrandRightDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.EnableActStatusRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.Equipment;
import com.linzi.pitpat.data.activityservice.dto.console.request.PolymerizationActQuery;
import com.linzi.pitpat.data.activityservice.dto.console.request.RateLimitDetailDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.RotSetting;
import com.linzi.pitpat.data.activityservice.dto.console.request.SeriesActivityAllRulesCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SeriesActivityCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SeriesActivityDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SeriesActivitySegmentRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityFeeAndAwardCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.TeamConfigDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivityDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivityPropResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivitySimpleDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.ActivityTitleDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.CompetitiveSeasonSettingResponseDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.MusicPlayDetailDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.PolymerizationActResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityAllRulesResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityBaseInfoDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityCreateResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityDistributionResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityIncomeAndExpenditure;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityIncomeAndExpenditureAmount;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivityResultResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SeriesActivitySegmentResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityAchieveInfo;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityCreateResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityDistributionResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityFeeAndAwardResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityIncomeAndExpenditure;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityInfoResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityParticipantInfo;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityRank;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityReportResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityResultResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.SingleActivityRuleResponse;
import com.linzi.pitpat.data.activityservice.dto.console.response.UserGroupDto;
import com.linzi.pitpat.data.activityservice.mapper.MainActivityMapper;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityTeamJoinSettingDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonSettingDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityArea;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEnterThreshold;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityImpracticalAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPlaylistRel;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRateLimit;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRotSetting;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserGroup;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonActivityRangeDo;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.Gameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PacerConfig;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.PropManage;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityMedal;
import com.linzi.pitpat.data.activityservice.model.entity.RunPlaylist;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRateLimitQuery;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRecommendDisseminateQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardDto;
import com.linzi.pitpat.data.activityservice.service.ActivityAreaService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityEnterThresholdService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityHighlightsService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityImpracticalAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsLoaderService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.ActivityPropConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.ActivityRecommendDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityRecommendTimelineService;
import com.linzi.pitpat.data.activityservice.service.ActivityRotSettingService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserGroupService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonActivityRangeService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PacerConfigService;
import com.linzi.pitpat.data.activityservice.service.PlaylistMusicRelService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.ProActivityService;
import com.linzi.pitpat.data.activityservice.service.PropManageService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.RunPlaylistService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.awardservice.constant.enums.AccountConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.model.vo.CouponDetailVo;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.equipmentservice.model.query.RunPlaylistQuery;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mybatis.core.MpUtil;
import com.linzi.pitpat.data.service.activity.MaidianLogService;
import com.linzi.pitpat.data.systemservice.enums.RegionConstants;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.query.AreaQuery;
import com.linzi.pitpat.data.systemservice.model.vo.RegionDto;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.label.UserGroupEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.trace.api.dto.request.TraceLogQueryDto;
import com.linzi.pitpat.trace.api.interfaces.TraceLogApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@RequiredArgsConstructor
@Slf4j
public class ConsloeActivityManager {
    private final ProActivityConvert proActivityConvert;
    private final ProActivityService proActivityService;
    private final CompetitiveSeasonRuleValid competitiveSeasonRuleValid;
    private final ActivityIDGenerateService idGenerateService;
    private final RedissonClient redissonClient;
    private final ActivityAreaService activityAreaService;
    private final ActivityDisseminateService activityDisseminateService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final ActivityRateLimitService activityRateLimitService;
    private final ActivityFeeService activityFeeService;
    private final PacerConfigService pacerConfigService;
    private final ActivityPropConfigService activityPropConfigService;
    private final ActivityRotSettingService activityRotSettingService;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final AreaService areaService;
    private final ActivityBrandRightsInterestsService brandRightsInterestsService;
    private final RunActivityMedalService activityMedalService;
    private final ActivityTeamService activityTeamService;
    private final ActivityPlaylistRelService activityPlaylistRelService;
    private final RunPlaylistService runPlaylistService;
    private final PlaylistMusicRelService playlistMusicRelService;
    private final GameplayService gameplayService;
    private final EntryGameplayService entryGameplayService;
    private final ActivityUserGroupService activityUserGroupService;
    private final UserGroupService userGroupService;
    private final CouponService couponService;
    private final MedalConfigService medalConfigService;
    private final PropManageService propManageService;
    private final ZnsRunRouteService runRouteService;
    private final ActivityEquipmentConfigService activityEquipmentConfigService;
    private final PolymerizationActivityPoleService polymerizationActivityPoleService;
    private final ActivityEnterThresholdService activityEnterThresholdService;
    private final ZnsRunActivityUserService activityUserService;
    private final ZnsUserAccountDetailService accountDetailService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserService userService;
    private final MaidianLogService maidianLogService;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;
    private final ActivityAreaBizService activityAreaBizService;
    private final AwardActivityBizService awardActivityBizService;

    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final CompetitiveSeasonConverter competitiveSeasonConverter;
    private final CompetitiveSeasonActivityRangeService competitiveSeasonActivityRangeService;


    private final MainActivityMapper mainActivityMapper;
    private final ActivityImpracticalAwardConfigService impracticalAwardConfigService;
    private final ActivityParamsService activityParamsService;
    private final ActivityParamsLoaderService activityParamsLoaderService;
    private final ActivityStageService activityStageService;


    private final static String MAIN_ACTIVITY = "zns_main_activity";
    private final static String SUB_ACTIVITY = "zns_sub_activity";
    private final static String ACTIVITY_PROP_CONFIG = "zns_activity_prop_config";
    private final static String ACTIVITY_RATE_LIMIT = "zns_activity_rate_limit";
    private final static String ACTIVITY_AREA = "zns_activity_area";
    private final static String ACTIVITY_ROT_SETTING = "zns_activity_rot_setting";
    private final static String ACTIVITY_PACER_CONFIG = "zns_pacer_config";
    private final static String ACTIVITY_FEE = "zns_activity_fee";
    private final static String ACTIVITY_DISSEMINATE = "zns_activity_disseminate";
    private final RabbitTemplate rabbitTemplate;

    private final ZnsUserAccountDetailService userAccountDetailService;
    private final RoomIdBizService roomIdBizService;
    private final MainActivityBizService mainActivityBizService;
    private final PolymerizationActivityBizService polymerizationActivityBizService;
    private final ActivityHighlightsService activityHighlightsService;
    private final ActivityRecommendDisseminateService activityRecommendDisseminateService;
    private final ActivityRecommendTimelineService activityRecommendTimelineService;
    private final TraceLogApi traceLogApi;

    @Value("${spring.profiles.active}")
    private String profile;

    private void buildActCache(Long ActId, String cacheName, Object cache) {
        RMap<String, String> rmap = redissonClient.getMap(ActId.toString());
        if (!(cache instanceof List))
            cache = List.of(cache);
        rmap.put(cacheName, JsonUtil.writeString(cache));
        rmap.expire(1, TimeUnit.DAYS);
    }

    @Transactional
    public MainActivity createSeriesActivity(SeriesActivityCreateRequest request) {

        Long mainActId = request.getActivityId();
        MainActivity mainActivity = new MainActivity();
        if (mainActId != null) {
            MainActivity oldActivity = mainActivityService.findById(mainActId);
            //上架后只允许编辑宣发
            if (!MainActivityStateEnum.NOT_PUBLISHED.getCode().equals(oldActivity.getActivityState())) {
                activityDisseminateService.deleteByActId(mainActId);
                processSeriesActDisseminate(oldActivity, request.getActivityDistributionCreateRequest(), oldActivity.getIsShowInMore(), request.getActivityRecommendSetting());
                //保存是否ai解锁
                if (request.getRulesCreateRequest().getIsAiCommentary() == null) {
                    request.getRulesCreateRequest().setIsAiCommentary(ActivityStringConstant.KEYS.DEFAULT_IS_AI_COMMENTARY);
                }
                activityParamsService.saveConfigSingleValue
                        (mainActId, ActivitySettingConfigEnum.IS_AI_COMMENTARY, request.getRulesCreateRequest().getIsAiCommentary());
                if (request.getProActivitySettings() != null) {
                    proActivityService.updateHighlight(request.getActivityId(), request.getProActivitySettings().getHighlight());
                }
                return mainActivity;
            }
            //编辑 清理所有相关表
            mainActivityBizService.cleanSeriesActivity(mainActId);
            mainActivity.setId(mainActId);
        } else {
            mainActivity.setId(idGenerateService.generateActivityID());
        }
        mainActivity.setMainType(MainActivityTypeEnum.SINGLE.getType());

        //前置处理
        preProcessSeriesRequest(request);
        //处理整体规则奖励
        processSeriesActAllRules(mainActivity, request.getRulesCreateRequest());
        //处理活动说明，宣发
        processSeriesActDisseminate(mainActivity, request.getActivityDistributionCreateRequest(), request.getIsShowInMore(), request.getActivityRecommendSetting());
        //处理子阶段(活动阶段绑定在子赛事上)
        processSeriesActSegments(mainActivity, request.getSegmentRequests(), request.getRulesCreateRequest().getStages());
        //save
        processSeriesActivity(mainActivity);
        //职业赛信息保存
        mainActivityBizService.processProActivity(mainActivity, request.getProActivitySettings());
        //竞技赛相关配置
        mainActivityBizService.processCompetitiveSeason(mainActivity);

        return mainActivity;
    }


    private void preProcessSeriesRequest(SeriesActivityCreateRequest request) {
        List<SeriesActivitySegmentRequest> segmentRequests = request.getSegmentRequests();
        for (SeriesActivitySegmentRequest segmentRequest : segmentRequests) {
            //前段传min，需要转成s
            if (segmentRequest.getTargetType() == 2) {
                segmentRequest.setTargets(segmentRequest.getTargets().stream().map(t -> t * 60).toList());
            }

        }
        //确定时间
        SeriesActivityAllRulesCreateRequest rulesCreateRequest = request.getRulesCreateRequest();
        Integer timeStyle = rulesCreateRequest.getTimeStyle();
        if (timeStyle == 0) {
            rulesCreateRequest.setActivityStartTime(DateUtil.convertTimeStrByZone(rulesCreateRequest.getActivityStartTime(), "GMT+08:00", "UTC"));
            rulesCreateRequest.setActivityEndTime(DateUtil.convertTimeStrByZone(rulesCreateRequest.getActivityEndTime(), "GMT+08:00", "UTC"));
            if (StringUtils.hasText(rulesCreateRequest.getApplicationStartTime())) {
                rulesCreateRequest.setApplicationStartTime(
                        DateUtil.convertTimeStrByZone(rulesCreateRequest.getApplicationStartTime(), "GMT+08:00", "UTC")
                );
            }
            rulesCreateRequest.setApplicationEndTime(DateUtil.convertTimeStrByZone(rulesCreateRequest.getApplicationEndTime(), "GMT+08:00", "UTC"));
        } else if (timeStyle == 1) {
            if (request.getActivityRecommendSetting() != null) {
                throw new BizException("跟随用户时区，不可设置推荐。");
            }
        }
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(request.getRulesCreateRequest().getPlayId());
        if (entryGameplay != null && entryGameplay.getCompetitionFormat() == 1 && request.getActivityRecommendSetting() != null) {
            throw new BizException("团队赛，不可设置推荐。");
        }
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(request.getRulesCreateRequest().getAreas()) && !Objects.equals(request.getRulesCreateRequest().getAreas().get(0).getCountryId(), 0L) && request.getActivityRecommendSetting() != null) {
            throw new BizException("区域不是全球，不可设置推荐。");
        }
        if (EnvUtils.isReallyOnline(profile) && List.of(1, 0).contains(request.getRulesCreateRequest().getGroupType()) && request.getActivityRecommendSetting() != null) {
            throw new BizException("已设置用户范围，不可设置推荐。");
        }

    }

    private void processSeriesActivity(MainActivity mainActivity) {
        //重定位subActivity，使其指向自己的mainId
        for (SubActivity subActivity : subActivityService.getAllSingleActByMain(mainActivity.getId())) {
            MainActivity mainToSubActivity = new MainActivity();
            BeanUtil.copyPropertiesIgnoreNull(mainActivity, mainToSubActivity);
            mainToSubActivity.setId(idGenerateService.generateActivityID());
            subActivity.setId(mainToSubActivity.getId());
            //建立系列赛关系
        }
        mainActivity.setMainType(MainActivityTypeEnum.SERIES_MAIN.getType());
        Gameplay gameplay = gameplayService.findById(mainActivity.getPlayId());
        // 赛事保存玩法设备类型
        mainActivity.setEquipmentMainType(gameplay.getEquipmentMainType());
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (entryGameplay != null) {
            mainActivity.setCompetitionFormat(entryGameplay.getCompetitionFormat());
        }
        mainActivityService.insertOrUpdate(mainActivity);
        //手动置为空
        if (mainActivity.getApplicationStartTime() == null) {
            mainActivityMapper.update(mainActivity, Wrappers.<MainActivity>lambdaUpdate()
                    .set(MainActivity::getApplicationStartTime, null)
                    .eq(MainActivity::getId, mainActivity.getId()));
        }

    }

    private void processSeriesActDisseminate(MainActivity mainActivity, List<SeriesActivityDistributionCreateRequest> requests,
                                             Integer isShowInMore, ActivityRecommendSetting activityRecommendSetting) {
        for (SeriesActivityDistributionCreateRequest request : requests) {
            I18nConstant.LanguageCodeEnum codeEnum = I18nConstant.LanguageCodeEnum.findByCode(request.getLanguageCode());
            String languageName = codeEnum == null ? request.getLanguageCode() : codeEnum.getName();
            if (!StringUtils.hasText(request.getTitle())) {
                throw new BaseException(languageName + "宣发赛事标题不能为空");
            }
            if (!StringUtils.hasText(request.getDisseminatePics())) {
                throw new BaseException(languageName + "宣发列表页宣传图不能为空");
            }
            if (Objects.isNull(request.getMarquee())) {
                throw new BaseException(languageName + "宣发是否开启跑马灯不能为空");
            }
            if (Objects.isNull(request.getPageSettings())) {
                throw new BaseException(languageName + "宣发页面配置不能为空");
            }
            //{\"title\":\"ltest法语\",\"bgColor\":\"#00A6FF\",\"data\":[]}
            Map<String, Object> jsonObject = JsonUtil.readValue(request.getPageSettings());
            String bgColor = MapUtil.getString(jsonObject.get("bgColor"));
            if (!StringUtils.hasText(bgColor)) {
                throw new BaseException(languageName + "宣发背景颜色不能为空");
            }

            //保存宣发
            ActivityDisseminate disseminate = new ActivityDisseminate();
            BeanUtil.copyPropertiesIgnoreNull(request, disseminate);
            disseminate.setMainActivityId(mainActivity.getId());
            activityDisseminateService.insert(disseminate);
            mainActivity.setCategoryType(request.getCategoryType());

            //活动显示的位置
            mainActivity.setIsShowInMore(isShowInMore);
//            //页面配置
//            for (SeriesActivityPageSetting pageSetting : request.getPageSettings()) {
//                ActivityPageSetting activityPageSetting = new ActivityPageSetting();
//                BeanUtil.copyPropertiesIgnoreNull(pageSetting, activityPageSetting);
//                activityPageSetting.setMainActivityId(mainActivity.getId());
//                activityPageSetting.setDisseminateId(disseminate.getId());
//                activityPageSettingService.insert(activityPageSetting);
//            }
            Long mainActId = mainActivity.getId();
            if (activityRecommendSetting != null) {
                List<String> recommendPosition = activityRecommendSetting.getRecommendPosition();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(recommendPosition)) {
                    if (recommendPosition.contains(ActivityRecommendPositionEnum.HOME.getPosition())) {
                        activityParamsService.saveConfigSingleValue
                                (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME, 1);
                    } else {
                        activityParamsService.saveConfigSingleValue
                                (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME, 0);
                        activityRecommendTimelineService.deleteByActivityId(mainActId);
                    }
                    if (recommendPosition.contains(ActivityRecommendPositionEnum.COMMUNITY.getPosition())) {
                        activityParamsService.saveConfigSingleValue
                                (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, 1);
                    } else {
                        activityParamsService.saveConfigSingleValue
                                (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, 0);
                    }
                    if (activityRecommendSetting.getDefaultLanguageCode() != null) {
                        activityParamsService.saveConfigSingleValue
                                (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE, activityRecommendSetting.getDefaultLanguageCode());
                        List<ActivityRecommendCommunityDesc> communityDesc = activityRecommendSetting.getCommunityDesc();
                        activityRecommendDisseminateService.save(communityDesc, mainActId, ActivityRecommendPositionEnum.COMMUNITY.getPosition());
                    }
                    if (recommendPosition.contains(ActivityRecommendPositionEnum.HOME.getPosition())) {
                        //占领首页时间线
                        activityRecommendTimelineService.bindTimeline(mainActId, ActivityRecommendPositionEnum.HOME, mainActivity.activityStartDateZonedDateTime(), mainActivity.activityEndDateZonedDateTime());
                    }
                }
            } else {
                activityParamsService.saveConfigSingleValue
                        (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, 0);
                activityParamsService.saveConfigSingleValue
                        (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME, 0);
                activityRecommendTimelineService.deleteByActivityId(mainActId);
            }
        }
    }

    private void processSeriesActSegments(MainActivity mainActivity, List<SeriesActivitySegmentRequest> segmentRequests, List<ActivityStageDto> stages) {
        Long mainActivityId = mainActivity.getId();
//        List<SubActivity> subActivityList = new ArrayList<>();
        List<MainActivity> sub2mainActivityList = new ArrayList<>();

        for (SeriesActivitySegmentRequest segmentRequest : segmentRequests) {
            //初始化阶段子活动
            //sub生成对应main
            MainActivity sub2mainAct = new MainActivity();
            BeanUtil.copyPropertiesIgnoreNull(mainActivity, sub2mainAct);
            sub2mainAct.setMainType(MainActivityTypeEnum.SERIES_SUB.getType());
            sub2mainAct.setTargetType(segmentRequest.getTargetType());
            sub2mainAct.setId(idGenerateService.generateActivityID());
            sub2mainAct.setActivityNo(null);
            //此时playId为阶段id
            sub2mainAct.setPlayId(segmentRequest.getPlayId());

            //添加系列赛main 的oldType
            EntryGameplay entryGameplay = entryGameplayService.findById(sub2mainAct.getPlayId());
            if (entryGameplay != null) {
                sub2mainAct.setOldType(entryGameplay.getOldActivityType());
                sub2mainAct.setCompetitionFormat(entryGameplay.getCompetitionFormat());
            }
            //组装子活动
            List<SubActivity> subActivityList = segmentRequest.getTargets().stream().map(t -> {
                SubActivity subActivity = new SubActivity();
                BeanUtil.copyPropertiesIgnoreNull(segmentRequest, subActivity);
                subActivity.setType(SubActivityTypeEnum.SERIES_SUB.getType());
                subActivity.setSegmentPlayId(segmentRequest.getPlayId());
//                if (sub2mainAct.getTargetType() == 2) {
//                    subActivity.setTarget(t * 60);
//                } else {
//                    subActivity.setTarget(t);
//                }

                //前置处理过了
                subActivity.setTarget(t);
                subActivity.setId(idGenerateService.generateActivityID());
                subActivity.setMainActivityId(sub2mainAct.getId());
                //保存用户参赛限制
                Integer entryCount = entryGameplay.getEntryCount();
                if (entryCount == 1) {
                    subActivity.setUserEnterLimit(1);
                }
                return subActivity;
            }).toList();
            subActivityService.savaBatch(subActivityList);

//            SubActivity subActivity = new SubActivity();
//            subActivity.setId(idGenerateService.generateActivityID());
//
//            BeanUtil.copyPropertiesIgnoreNull(segmentRequest, subActivity);
//            subActivity.setType("series-sub");
//            subActivityList.add(subActivity);
//
//
//            //id反存
//            subActivity.setMainActivityId(sub2mainAct.getId());

            sub2mainActivityList.add(sub2mainAct);
            mainActivityService.insert(sub2mainAct);


            //保存配速员信息
            for (ActivityPaceSetting activityPaceSetting : segmentRequest.getPaceSetting()) {

                PacerConfig pacerConfig = new PacerConfig();
                pacerConfig.setActivityType(mainActivity.getOldType());
                BeanUtil.copyPropertiesIgnoreNull(activityPaceSetting, pacerConfig);
                pacerConfig.setConfigType(2);
                pacerConfig.setAverageMinute(activityPaceSetting.getAverageMinute());
                pacerConfig.setAverageSecond(activityPaceSetting.getAverageSecond());
                pacerConfig.setAveragePace(activityPaceSetting.getAveragePace());
                pacerConfig.setActivityId(sub2mainAct.getId());
                //系列赛sub
                pacerConfigService.save(pacerConfig);
            }

            //保存道具信息
            List<ActivityPropConfigDto> propConfigDtoList = segmentRequest.getActivityPropConfig();
            for (ActivityPropConfigDto activityPropConfigDto : propConfigDtoList) {
                ActivityPropConfig activityPropConfig = new ActivityPropConfig();
                activityPropConfig.setActivityId(sub2mainAct.getId());
                BeanUtil.copyPropertiesIgnoreNull(activityPropConfigDto, activityPropConfig);
                activityPropConfigService.save(activityPropConfig);
            }

            //保存子活动速率表
            for (ActivityRateLimitDto activityRateLimitDto : segmentRequest.getRateLimit()) {
                activityRateLimitDto.getRateLimitDetailDtoList().forEach(k -> {
                    ActivityRateLimit activityRateLimit = new ActivityRateLimit();
                    BeanUtil.copyPropertiesIgnoreNull(k, activityRateLimit);
                    activityRateLimit.setActivityId((sub2mainAct.getId()));
                    activityRateLimit.setRunningGoal(activityRateLimitDto.getRunningGoal());
                    activityRateLimit.setIntervalUnit(activityRateLimitDto.getRunningGoalUnit());
                    activityRateLimit.setRunningGoalUnit(activityRateLimitDto.getRunningGoalUnit());
                    activityRateLimitService.save(activityRateLimit);
                });

            }


            //保存团队赛配置
            List<TeamConfigDto> teamConfigDtos = segmentRequest.getTeamConfigDtos();
            if (!CollectionUtils.isEmpty(teamConfigDtos)) {
                for (TeamConfigDto teamConfigDto : teamConfigDtos) {
                    ActivityTeam activityTeam = new ActivityTeam();
                    BeanUtil.copyPropertiesIgnoreNull(teamConfigDto, activityTeam);
                    ///activityTeam.setMaxNum(segmentRequest.getUserEnterLimit());
                    activityTeam.setActivityId(sub2mainAct.getId());
                    activityTeam.setIsOfficial(1);
                    activityTeamService.save(activityTeam);
                }
            }
            //保存非官方队伍
            List<TeamConfigDto> nonOfficialTeamConfigDtos = segmentRequest.getNonOfficialTeamConfigDtos();
            if (!CollectionUtils.isEmpty(nonOfficialTeamConfigDtos)) {
                for (TeamConfigDto teamConfigDto : teamConfigDtos) {
                    ActivityTeam activityTeam = new ActivityTeam();
                    BeanUtil.copyPropertiesIgnoreNull(teamConfigDto, activityTeam);
                    ///activityTeam.setMaxNum(segmentRequest.getUserEnterLimit());
                    activityTeam.setActivityId(sub2mainAct.getId());
                    activityTeam.setIsOfficial(0);
                    activityTeamService.save(activityTeam);
                }
            }
            //保存歌单
            mainActivityBizService.saveMusicPlays(sub2mainAct.getId(), segmentRequest.getMusicListId());

            //保存子阶段奖励
            List<ActivityTargetAwardDto> targetAwardDtos = segmentRequest.getTargetAwardDtos();
            awardActivityBizService.saveTargetAward(targetAwardDtos, sub2mainAct, subActivityList);
            //保存本阶段参赛条件
            Integer condition = segmentRequest.getConditionState();
            ActivityEnterThreshold threshold = new ActivityEnterThreshold();
            threshold.setMainActivityId(sub2mainAct.getId());
            threshold.setConditionState(condition);
            activityEnterThresholdService.insert(threshold);

            //保存活动阶段
            if (!org.springframework.util.CollectionUtils.isEmpty(stages)) {
                stages.sort(Comparator.comparing(ActivityStageDto::getStartTime));
                List<ActivityStage> stageList = new ArrayList<>();
                for (int i = 0; i < stages.size(); i++) {
                    ActivityStage stage = new ActivityStage();
                    stage.setLevel(i + 1);
                    stage.setActivityId(sub2mainAct.getId());
                    stage.setStartTime(stages.get(i).getStartTime());
                    stage.setEndTime(stages.get(i).getEndTime());
                    stage.setWaitTime(mainActivity.getWaitTime());
                    stageList.add(stage);
                }
                activityStageService.saveBatch(stageList);
            }

            roomIdBizService.createGameRoom(mainActivity, subActivityList);

        }
        //构建系列赛关系
//        for (int i = 0; i < subActivityList.size(); i++) {
//            if (i > 0) {
//                subActivityList.get(i).setPreId(subActivityList.get(i - 1).getId());
//            }
//            if (i < subActivityList.size() - 1) {
//                subActivityList.get(i).setNextId(subActivityList.get(i + 1).getId());
//            }
//            subActivityService.update(subActivityList.get(i));
//        }
        Long preId = -1l;

        for (int i = 0; i < sub2mainActivityList.size(); i++) {
            SeriesActivityRel rel = new SeriesActivityRel();
            rel.setLevel(i + 1);
            rel.setParentActivityId(mainActivityId);
            rel.setSegmentActivityId(sub2mainActivityList.get(i).getId());
            rel.setPreId(preId);
            if (i == sub2mainActivityList.size() - 1) {
                rel.setNextId(-1l);
            } else {
                rel.setNextId(sub2mainActivityList.get(i + 1).getId());
            }
            preId = rel.getSegmentActivityId();
            seriesActivityRelService.insert(rel);
        }


    }


    private void processSeriesActAllRules(MainActivity mainActivity, SeriesActivityAllRulesCreateRequest rulesCreateRequest) {
        //主活动id
        Long mainActId = mainActivity.getId();

        //生成子活动
        SubActivity subActivity = new SubActivity();
        subActivity.setMainActivityId(mainActivity.getId());
        subActivity.setType(SubActivityTypeEnum.SERIES_MAIN.getType());
        subActivity.setSegmentPlayId(rulesCreateRequest.getPlayId());
        subActivity.setTarget(0);
        subActivityService.insert(subActivity);

        //报名时间类型 0自定义 1不限
        if (!StringUtils.hasText(rulesCreateRequest.getApplicationStartTime())) {
            mainActivity.setReportTimeType(1);
        } else {
            mainActivity.setReportTimeType(0);
        }

        //保存设备
        mainActivityBizService.saveActivityEquipments(rulesCreateRequest.getEquipments(), mainActivity.getId());

        //保存互斥活动
        List<Long> mutexActivityIds = rulesCreateRequest.getMutexActivityIds();
        if (!CollectionUtils.isEmpty(mutexActivityIds)) {
            mainActivity.setMutexActivityIds(JsonUtil.writeString(mutexActivityIds));
        } else {
            mainActivity.setMutexActivityIds(null);
            mainActivityService.clearMutexActivity(mainActId);
        }

        //保存活动地区
        activityAreaService.saveActivityArea(mainActId, rulesCreateRequest.getAreas());

        //保存人群信息
        for (Long groupId : rulesCreateRequest.getGroupIds()) {
            if (Objects.equals(rulesCreateRequest.getGroupType(), 0) && Objects.isNull(groupId)) {
                throw new BaseException("用户分群不能为空");
            }
            if (Objects.nonNull(groupId) && groupId > 0) {
                ActivityUserGroup userGroup = new ActivityUserGroup();
                userGroup.setMainActivityId(mainActId);
                userGroup.setGroupId(groupId);
                userGroup.setType(rulesCreateRequest.getGroupType());
                activityUserGroupService.insert(userGroup);
            }
        }

        //拼装主活动
        BeanUtil.copyPropertiesIgnoreNull(rulesCreateRequest, mainActivity);
        //报名开始时间需要特殊处理
        mainActivity.setApplicationStartTime(rulesCreateRequest.getApplicationStartTime());

        //保存参赛费用
        SingleActivityFeeAndAwardCreateRequest feeAndAwardCreateRequest = rulesCreateRequest.getFeeAndAwardCreateRequest();
        mainActivityBizService.preProcessSingleFeeRequest(feeAndAwardCreateRequest);

        List<ActivityFee> activityFeeList = feeAndAwardCreateRequest.getAmounts().stream().map(k -> {
            ActivityFee activityFee = new ActivityFee();
            BeanUtil.copyPropertiesIgnoreNull(feeAndAwardCreateRequest, activityFee);
            activityFee.setAmount(k.getAmount());
            activityFee.setCurrency(k.getCurrencyCode());
            activityFee.setMainActivityId(mainActId);
            return activityFee;
        }).toList();
        for (ActivityFee activityFee : activityFeeList) {
            activityFeeService.insert(activityFee);
        }
        //保存优惠劵信息
        mainActivity.setCouponId(feeAndAwardCreateRequest.getCouponId());
        mainActivity.setAllowCoupon(feeAndAwardCreateRequest.getAllowCoupon());

        //保存品牌勋章权益
        mainActivityBizService.saveBrandRightDto(feeAndAwardCreateRequest.getBrandRightDto(), mainActivity);

        //保存奖励
        List<ActivityTargetAwardDto> targetAwardDtos = rulesCreateRequest.getFeeAndAwardCreateRequest().getTargetAwardDtos();

        //报名奖励填充
        impracticalAwardConfigService.saveApplicationAward(mainActivity, rulesCreateRequest.getApplicationRewardDto(), null, targetAwardDtos, false, null);
        List<SubActivity> subActivities = subActivityService.getAllSingleActByMain(mainActivity.getId());
        awardActivityBizService.saveTargetAward(targetAwardDtos, mainActivity, subActivities);

        //保存分享奖励
        impracticalAwardConfigService.saveSharedAward(mainActivity, rulesCreateRequest.getFeeAndAwardCreateRequest().getShareScore());
        // 奖励人工审核配置
        mainActivityBizService.saveAwardSendType(mainActivity, rulesCreateRequest.getFeeAndAwardCreateRequest());

        //保存开赛时间限制
        if (rulesCreateRequest.getAfterStartLimit() != null) {
            activityParamsService.saveConfigSingleValue
                    (mainActId, ActivitySettingConfigEnum.AFTER_START_LIMIT, rulesCreateRequest.getAfterStartLimit());
        }
        //保存ram限制
        if (rulesCreateRequest.getRamLimit() == null) {
            rulesCreateRequest.setRamLimit(ActivityStringConstant.KEYS.DEFAULT_RAM_LIMIT);
        }
        activityParamsService.saveConfigSingleValue
                (mainActId, ActivitySettingConfigEnum.RAM_LIMIT, rulesCreateRequest.getRamLimit());
        //保存是否ai解锁
        if (rulesCreateRequest.getIsAiCommentary() == null) {
            rulesCreateRequest.setIsAiCommentary(ActivityStringConstant.KEYS.DEFAULT_IS_AI_COMMENTARY);
        }
        activityParamsService.saveConfigSingleValue
                (mainActId, ActivitySettingConfigEnum.IS_AI_COMMENTARY, rulesCreateRequest.getIsAiCommentary());
    }


    private BrandRightDto getBrandRightDto(MainActivity mainActivity) {
        List<ActivityBrandRightsInterests> interestsList = brandRightsInterestsService.findByActId(mainActivity.getId());
        if (CollectionUtils.isEmpty(interestsList)) {
            return null;
        }
        BrandRightDto brandRightDto = new BrandRightDto();
        BeanUtil.copyPropertiesIgnoreNull(mainActivity, brandRightDto);
        List<BrandRightAwardDto> rightAwardDtos = interestsList.stream().map(k -> {
            BrandRightAwardDto brandRightAwardDto = new BrandRightAwardDto();
            BeanUtil.copyPropertiesIgnoreNull(k, brandRightAwardDto);
            return brandRightAwardDto;
        }).toList();
        brandRightDto.setBrandRightAwardDtos(rightAwardDtos);
        RunActivityMedal medal = activityMedalService.findByActivityIdOne(mainActivity.getId());
        if (medal != null) {
            brandRightDto.setMedalId(medal.getMedalConfigId());
            MedalConfig medalConfig = medalConfigService.getById(medal.getMedalConfigId());
            if (medalConfig != null) {
                brandRightDto.setMedal(medalConfig.getName());
            }

        }
        return brandRightDto;
    }


    /**
     * @param activityId
     * @param isCopy     是否是复制 请求，复制请求需要补全币种
     * @return
     */
    @FillerMethod
    public SingleActivityCreateResponse singleActivityDetail(Long activityId, Boolean isCopy) {
        MainActivity mainActivity = mainActivityService.findById(activityId);

        SingleActivityCreateResponse response = new SingleActivityCreateResponse();
        response.setActivityId(mainActivity.getId());
        response.setActivityNo(mainActivity.getActivityNo());
        //赛事规则实体
        SingleActivityRuleResponse singleActivityRuleResponse =
                findSingleActivityRule(mainActivity);
        //赛事报名实体
        SingleActivityReportResponse singleActivityReportResponse =
                findSingleActivityReport(mainActivity);

        //赛事费用奖励实体
        SingleActivityFeeAndAwardResponse singleActivityFeeAndAwardResponse =
                findSingleActivityFeeAndAward(mainActivity, isCopy);
        //赛事说明宣发实体
        SingleActivityDistributionResponse singleActivityDistributionResponse =
                findSingleActivityDistribution(mainActivity);
        //赛事相关信息
        SingleActivityInfoResponse singleActivityInfoResponse =
                findSingleSingleActivityInfo(mainActivity);

        //赛事完成情况
//        SingleActivityResultResponse singleActivityResultResponse =
//                findSingleSingleActivityResult(mainActivity,0);


        response.setSingleActivityRuleResponse(singleActivityRuleResponse);
        response.setSingleActivityReportResponse(singleActivityReportResponse);
        response.setSingleActivityFeeAndAwardResponse(singleActivityFeeAndAwardResponse);
        response.setSingleActivityDistributionResponse(singleActivityDistributionResponse);
        response.setSingleActivityInfoResponse(singleActivityInfoResponse);
        //response.setSingleActivityResultResponse(singleActivityResultResponse);
        //添加竞技赛相关的配置
        response.setCompetitiveSeasonSettingDto(loadCompetitiveConfig(mainActivity));
        //后置处理
        postProcessSingleResponse(mainActivity, response);
        return response;
    }

    private CompetitiveSeasonSettingResponseDto loadCompetitiveConfig(MainActivity mainActivity) {
        if (mainActivity.isCompetitive()) {
            Optional<CompetitiveSeasonSettingDto> settingDto = competitiveSeasonBizService.getByActivityId(mainActivity.getId());
            if (settingDto.isPresent()) {
                CompetitiveSeasonSettingDto competitiveSeasonSettingDto = settingDto.get();
                CompetitiveSeasonSettingResponseDto responseDto = competitiveSeasonConverter.toResponseDto(competitiveSeasonSettingDto);
                if (!ActivityCompetitiveSeasonType.MONTHLY.equals(competitiveSeasonSettingDto.getCompetitiveSeasonType())) {
                    List<CompetitiveSeasonActivityRangeDo> add = competitiveSeasonActivityRangeService.findByCompetitiveSeasonActivityId(mainActivity.getId());

                    Map<Long, String> titleMapByActivityIdAndDefault = activityDisseminateService.findTitleMapByActivityIdAndDefault(add.stream().map(CompetitiveSeasonActivityRangeDo::getEventActivityId).toList());
                    responseDto.setSeasonActivityRange(titleMapByActivityIdAndDefault);
                }
                return responseDto;
            }
        }
        return null;
    }

    public SingleActivityResultResponse getSingleActivityResult(Long mainActivityId, Integer isAllUser) {

        MainActivity mainActivity = mainActivityService.findById(mainActivityId);

        SingleActivityResultResponse resultResponse = new SingleActivityResultResponse();

        //参与情况
        List<SingleActivityParticipantInfo> singleActivityParticipantInfos = getSingleActivityParticipantInfos(mainActivity, isAllUser);
        //费用奖励
        List<SingleActivityIncomeAndExpenditure> singleActivityIncomeAndExpenditures = getSingleActivityIncomeAndExpenditures(mainActivity, isAllUser);
        //排行榜
        List<SingleActivityRank> singleActivityRanks = getSingleActivityRank(mainActivity, isAllUser);
        //里程/时长奖励：
        List<SingleActivityAchieveInfo> singleActivityAchieveInfos = getSingleActivityAchieveInfo(mainActivity, isAllUser);

        resultResponse.setTargetType(mainActivity.getTargetType());
        resultResponse.setSingleActivityParticipantInfos(singleActivityParticipantInfos);
        resultResponse.setSingleActivityIncomeAndExpenditures(singleActivityIncomeAndExpenditures);
        resultResponse.setSingleActivityRanks(singleActivityRanks);
        resultResponse.setSingleActivityAchieveInfos(singleActivityAchieveInfos);

        return resultResponse;
    }

    private List<SingleActivityAchieveInfo> getSingleActivityAchieveInfo(MainActivity mainActivity, Integer isAllUser) {


        List<SingleActivityAchieveInfo> singleActivityAchieveInfos = new ArrayList<SingleActivityAchieveInfo>();

        //时间奖励信息
        SingleActivityAchieveInfo timeAchieveInfo = new SingleActivityAchieveInfo();
        timeAchieveInfo.setTargetType(2);
        //里程奖励信息
        SingleActivityAchieveInfo mileageAchieveInfo = new SingleActivityAchieveInfo();
        mileageAchieveInfo.setTargetType(1);

        singleActivityAchieveInfos.add(timeAchieveInfo);
        singleActivityAchieveInfos.add(mileageAchieveInfo);


        //时间金额奖励
        List<CurrencyAmount> timeAmountList = new ArrayList<>();
        //里程金额奖励
        List<CurrencyAmount> mileageAmountList = new ArrayList<>();

        timeAchieveInfo.setAmountList(timeAmountList);
        mileageAchieveInfo.setAmountList(mileageAmountList);


        //所有活动用户
        List<ZnsRunActivityUserEntity> allActivityUser = activityUserService.findAllActivityUser(mainActivity.getId());
        //只查真实用户
        if (isAllUser == 1) {
            allActivityUser = allActivityUser.stream().filter(k -> k.getIsRobot() == 0 && k.getIsTest() == 0).toList();
        }


        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).toList();
        if (CollectionUtils.isEmpty(userIds)) {
            return singleActivityAchieveInfos;
        }
        List<ZnsUserAccountEntity> accountList = userAccountService.selectByUserIdList(userIds);
        Map<String, List<Long>> currencyMap = accountList.stream().collect(Collectors.groupingBy(k -> k.getCurrencyCode(),
                Collectors.mapping(ZnsUserAccountEntity::getUserId, Collectors.toList())));
        //<币种，List<用户id>>
        currencyMap.forEach((currencyCode, currencyUserList) -> {
            if (CollectionUtils.isEmpty(currencyUserList)) {
                return;
            }
            List<ZnsUserAccountDetailEntity> accountDetails = userAccountDetailService.findList(new UserAccountDetailByQuery().setActivityId(mainActivity.getId()).setUserIds(currencyUserList));

            //时间奖励总现金
            BigDecimal totalTimeAward = accountDetails.stream().filter(
                            k -> Objects.equals(k.getTradeType(), AccountDetailTypeEnum.NEW_ACTIVITY_100.getType())
                                    && Objects.equals(k.getTradeSubtype(), AwardSentTypeEnum.TIME_AWARD))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            CurrencyAmount totalTimeAmount = new CurrencyAmount(I18nConstant.buildCurrency(currencyCode), totalTimeAward);
            timeAmountList.add(totalTimeAmount);

            //里程奖励总现金
            BigDecimal totalMileageAward = accountDetails.stream().filter(
                            k -> Objects.equals(k.getTradeType(), AccountDetailTypeEnum.NEW_ACTIVITY_100.getType())
                                    && Objects.equals(k.getTradeSubtype(), AwardSentTypeEnum.MILEAGE_AWARD))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            CurrencyAmount totalMileageAmount = new CurrencyAmount(I18nConstant.buildCurrency(currencyCode), totalMileageAward);
            mileageAmountList.add(totalMileageAmount);

        });

        //查询活动奖励配置
        List<ActivityTargetAwardDto> targetAwardDtos = awardActivityBizService.getAwardByAct(mainActivity);
        if (CollectionUtils.isEmpty(targetAwardDtos)) {
            return new ArrayList<SingleActivityAchieveInfo>();
        }
        ActivityTargetAwardDto targetAwardDto = targetAwardDtos.get(0);
        List<ActivityAwardConfigDto> awardDtos = targetAwardDto.getAwardDtos();


        //时长统计
        Integer totalTimeSendNum = 0;
        List<ActivityAwardConfigDto> timeAwardConfigDtos = awardDtos.stream().filter(k -> AwardSentTypeEnum.TIME_AWARD.getType().equals(k.getType())).toList();
        if (!CollectionUtils.isEmpty(timeAwardConfigDtos)) {
            List<Integer> timeTargets = timeAwardConfigDtos.get(0).getAwardLists().stream().map(ActivityAwardDto::getTarget).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(timeTargets)) {
                //计算时长奖励总发放次数
                for (Integer timeTarget : timeTargets) {
                    for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
                        if (activityUser.getRunTime() > timeTarget) {
                            totalTimeSendNum++;
                        }
                    }
                }
                timeAchieveInfo.setSendNum(totalTimeSendNum);
                //拿到奖励人数
                Integer minTimeTarget = timeTargets.stream().min(Integer::compareTo).get();
                Long timeCompleteNum = allActivityUser.stream().filter(k -> k.getRunTime() >= minTimeTarget).count();
                timeAchieveInfo.setCompleteNum(timeCompleteNum.intValue());

            }
        }

        //里程统计
        Integer totalMileageSendNum = 0;
        List<ActivityAwardConfigDto> mileageAwardConfigDtos = awardDtos.stream().filter(k -> AwardSentTypeEnum.MILEAGE_AWARD.getType().equals(k.getType())).toList();
        if (!CollectionUtils.isEmpty(mileageAwardConfigDtos)) {
            List<Integer> mileageTargets = mileageAwardConfigDtos.get(0).getAwardLists().stream().map(ActivityAwardDto::getTarget).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(mileageTargets)) {
                //计算里程奖励总发放次数
                for (Integer mileageTarget : mileageTargets) {
                    for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
                        if (activityUser.getRunMileage().intValue() > mileageTarget) {
                            totalMileageSendNum++;
                        }
                    }
                }
                mileageAchieveInfo.setSendNum(totalMileageSendNum);
                //拿到奖励人数
                Integer minMileageTarget = mileageTargets.stream().min(Integer::compareTo).get();
                Long mileageCompleteNum = allActivityUser.stream().filter(k -> k.getRunMileage().intValue() >= minMileageTarget).count();
                mileageAchieveInfo.setCompleteNum(mileageCompleteNum.intValue());
            }
        }

        return singleActivityAchieveInfos;
    }

    private List<SingleActivityRank> getSingleActivityRank(MainActivity mainActivity, Integer isAllUser) {
        if (MainActivityStateEnum.NOT_PUBLISHED.getCode().equals(mainActivity.getActivityState())
                || MainActivityStateEnum.NOT_STARTED.getCode().equals(mainActivity.getActivityState())) {
            //未开始、未上架不会有数据
            return new ArrayList<>();
        }
        List<ZnsRunActivityUserEntity> activityUserEntities = activityUserService.selectByActivityId(mainActivity.getId());
        //只查真实用户
        if (isAllUser == 1) {
            activityUserEntities = activityUserEntities.stream().filter(k -> k.getIsRobot() == 0 && k.getIsTest() == 0).toList();
        }
        if (CollectionUtils.isEmpty(activityUserEntities)) {
            return new ArrayList<>();
        }
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        //排名依据，1：里程，2：时长，3：完赛时间 4：平均配速
        String rankingBy = entryGameplay.getRankingBy();
        int rankType = Integer.parseInt(rankingBy);
        if (MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())) {
            //已结束按排名
            activityUserEntities = activityUserEntities.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRank)).collect(Collectors.toList());
        } else if (rankType == 2) {
            //按时间排行，越小越在前
            activityUserEntities = activityUserEntities.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunTime)).collect(Collectors.toList());
        } else {
            //按里程排行，越大越在前
            activityUserEntities = activityUserEntities.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
        }
        if (activityUserEntities.size() > 20) {
            //排行榜只展示20条数据
            activityUserEntities = activityUserEntities.subList(0, 20);
        }
        //封装返回数据
        List<Long> userIds = activityUserEntities.stream().map(ZnsRunActivityUserEntity::getUserId).toList();
        Map<Long, ZnsUserEntity> userMap = userService.findByIds(userIds).stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        Map<Long, String> userCurrencyMap = userAccountService.selectByUserIdList(userIds).stream().collect(Collectors.toMap(ZnsUserAccountEntity::getUserId, ZnsUserAccountEntity::getCurrencyCode));
        List<SingleActivityRank> rankList = new ArrayList<>(activityUserEntities.size());
        for (int i = 0; i < activityUserEntities.size(); i++) {
            ZnsRunActivityUserEntity k = activityUserEntities.get(i);
            SingleActivityRank rank = new SingleActivityRank();
            rank.setRank(i + 1);
            if (rankType == 2) {
                //时长
                rank.setGrade(k.getRunTime());
                rank.setTarget(k.getTargetRunMileage());
            } else {
                rank.setGrade(k.getRunMileage().intValue());
                rank.setTarget(k.getTargetRunTime());
            }
            rank.setEmail(userMap.get(k.getUserId()).getEmailAddressEn());
            rank.setNickname(userMap.get(k.getUserId()).getFirstName());
            String currencyCode = userCurrencyMap.get(k.getUserId());
            BigDecimal sumAward = userAccountDetailService.sumAward(mainActivity.getId(), k.getUserId(),
                    Collections.singletonList(AccountDetailTypeEnum.NEW_ACTIVITY_100.getType()), AccountConstant.TradeStatusEnum.TRADE_STATUS_2.getCode());
            CurrencyAmount currencyAmount = I18nConstant.buildCurrencyAmount(currencyCode, sumAward);
            rank.setAward(currencyAmount);
            rankList.add(rank);
        }
        return rankList;
    }

    private List<SingleActivityIncomeAndExpenditure> getSingleActivityIncomeAndExpenditures(MainActivity mainActivity, Integer isAllUser) {
        Long mainActivityId = mainActivity.getId();

        List<SingleActivityIncomeAndExpenditure> list = new ArrayList<>();
        List<ZnsRunActivityUserEntity> activityUserEntities = activityUserService.selectByActivityId(mainActivityId);

        //只查真实用户
        if (isAllUser == 1) {
            activityUserEntities = activityUserEntities.stream().filter(k -> k.getIsRobot() == 0 && k.getIsTest() == 0).toList();
        }

        List<Long> userIds = activityUserEntities.stream().map(ZnsRunActivityUserEntity::getUserId).toList();
        if (CollectionUtils.isEmpty(userIds)) {
            return list;
        }
        List<ZnsUserAccountEntity> accountList = userAccountService.selectByUserIdList(userIds);
        Map<String, List<Long>> currencyMap = accountList.stream().collect(Collectors.groupingBy(k -> k.getCurrencyCode(),
                Collectors.mapping(ZnsUserAccountEntity::getUserId, Collectors.toList())));
        //<币种，List<用户id>>
        currencyMap.forEach((currencyCode, currencyUserList) -> {
            if (CollectionUtils.isEmpty(currencyUserList)) {
                return;
            }
            List<ZnsUserAccountDetailEntity> accountDetails = userAccountDetailService.findList(new UserAccountDetailByQuery().setActivityId(mainActivityId).setUserIds(currencyUserList));

            SingleActivityIncomeAndExpenditure incomeAndExpenditure = new SingleActivityIncomeAndExpenditure();
            //设置币种
            incomeAndExpenditure.setCurrency(I18nConstant.buildCurrency(currencyCode));

            //报名现金
            BigDecimal reportAward = accountDetails.stream().filter(k -> Objects.equals(k.getTradeType(), AccountDetailTypeEnum.FEE.getType()))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            incomeAndExpenditure.setReportAward(reportAward);

            //总奖励
            BigDecimal totalAward = accountDetails.stream().filter(k -> Objects.equals(k.getTradeType(), AccountDetailTypeEnum.NEW_ACTIVITY_100.getType()))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            incomeAndExpenditure.setTotalAward(totalAward);

            //排名奖励
            BigDecimal rankAward = accountDetails.stream().filter(k -> Objects.equals(k.getTradeSubtype(), AwardSentTypeEnum.RANK_AWARD.getType()))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            incomeAndExpenditure.setRankAward(rankAward);


            //挑战成功奖励
            BigDecimal challengeSuccessAward = accountDetails.stream().filter(k -> Objects.equals(k.getTradeSubtype(), AwardSentTypeEnum.CHALLENGE_SUCCESS.getType()))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            incomeAndExpenditure.setChallengeSuccessAward(challengeSuccessAward);

            //挑战失败奖励
            BigDecimal challengeFailureAward = accountDetails.stream().filter(k -> Objects.equals(k.getTradeSubtype(), AwardSentTypeEnum.CHALLENGE_FAIL.getType()))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            incomeAndExpenditure.setChallengeFailureAward(challengeFailureAward);

            //被挑战奖励
            BigDecimal beChallengedAward = accountDetails.stream().filter(k -> Objects.equals(k.getTradeSubtype(), AwardSentTypeEnum.BEING_CHALLENGED.getType()))
                    .map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            incomeAndExpenditure.setBeChallengedAward(beChallengedAward);

            list.add(incomeAndExpenditure);
        });

        return list;

    }

    private List<SingleActivityParticipantInfo> getSingleActivityParticipantInfos(MainActivity mainActivity, Integer isAllUser) {


        Long mainActivityId = mainActivity.getId();
        Integer targetType = mainActivity.getTargetType();


        //参加这个活动的真实用户
        List<Integer> targets = subActivityService.getSingleActivityTargets(mainActivityId);
        List<ZnsRunActivityUserEntity> activityUserEntities = activityUserService.selectByActivityId(mainActivityId);

        //只查真实用户
        if (isAllUser == 1) {
            activityUserEntities = activityUserEntities.stream().filter(k -> k.getIsRobot() == 0 && k.getIsTest() == 0).toList();
        }

        List<ZnsRunActivityUserEntity> targetActUsers = activityUserEntities;


        //参与情况
        List<SingleActivityParticipantInfo> singleActivityParticipantInfos = new ArrayList<>();

        //为了能够正确计算数据，对于前后连续的分表跨月份之后，需要指定起止时间,
        ZonedDateTime startTime = ZonedDateTime.ofInstant(mainActivity.getGmtCreate().toInstant(), ZoneId.systemDefault());//.with(LocalTime.MIN);
        ZonedDateTime endTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), "UTC")), ZoneId.systemDefault());//.with(LocalTime.MAX); //回退 2 个月，确保覆盖到活动从开始到结束
        if (endTime.isAfter(ZonedDateTime.now())) {
            //回退时间在当前时间之后，则设置为当前时间
            endTime = ZonedDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
        }
        log.info("mainActivityId={}, startTime={}, endTime={}", mainActivity.getId(), startTime, endTime);
        //详情浏览人数
        Long detailPageViewNum =
                traceLogApi.count(new TraceLogQueryDto().setMainType(mainActivity.getMainType()).setEventTypeOne("st").setEventTypeTwo("ex").setActivityId(mainActivityId).setStartTime(Date.from(startTime.toInstant())).setEndTime(Date.from(endTime.toInstant()))).getData();
        if (detailPageViewNum == null) {
            detailPageViewNum = 0l;
        }

        for (Integer target : targets) {
            //目标类型：0：无，1：里程，2：时长
            if (ActivityConstants.TargetTypeEnum.TARGETTYPE_1.code.equals(targetType)) {
                targetActUsers = activityUserEntities.stream().filter(k -> Objects.equals(k.getTargetRunMileage(), target)).toList();
            } else if (ActivityConstants.TargetTypeEnum.TARGETTYPE_2.code.equals(targetType)) {
                targetActUsers = activityUserEntities.stream().filter(k -> Objects.equals(k.getTargetRunTime(), target)).toList();
            }
            SingleActivityParticipantInfo participantInfo = new SingleActivityParticipantInfo();
            //详情浏览人数
            participantInfo.setDetailPageViewNum(detailPageViewNum.intValue());
            //报名人数
            participantInfo.setReportNum(targetActUsers.size());
            //参赛人数
            Long participantCount = 0l;
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                List<Long> segmentIds = seriesActivityRelService.findSubActivity(mainActivityId).stream().map(SeriesActivityRel::getSegmentActivityId).toList();
                List<ZnsRunActivityUserEntity> segmentActivityUsers = activityUserService.findActivityUsers(segmentIds, null);
                //只查真实用户
                if (isAllUser == 1) {
                    segmentActivityUsers = segmentActivityUsers.stream().filter(k -> k.getIsRobot() == 0 && k.getIsTest() == 0).toList();
                }
                participantCount = segmentActivityUsers.stream().filter(k -> k.getRunTime() > 0).map(ZnsRunActivityUserEntity::getUserId).distinct().count();
            } else {
                participantCount = targetActUsers.stream().filter(k -> Objects.nonNull(k.getRunTime()) && k.getRunTime() > 0).count();
            }
            participantInfo.setParticipantNum(participantCount.intValue());
            //完成人数
            Long completeCount = targetActUsers.stream().filter(k -> k.getIsComplete() == 1).count();
            participantInfo.setCompleteNum(completeCount.intValue());

            List<ZnsUserAccountDetailEntity> accountDetails = new ArrayList<>();
            //奖励发放人数  系列赛需要算上所有阶段
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                //系列赛查所有阶段活动
                List<Long> allSegmentAndMainActIds = seriesActivityRelService.getAllMainActivity(mainActivityId).stream().map(MainActivity::getId).collect(Collectors.toList());
                allSegmentAndMainActIds.add(mainActivityId);
                accountDetails = accountDetailService.selectByAndActivityIds(allSegmentAndMainActIds);
            } else {
                accountDetails = accountDetailService.selectByUserIdsAndActivityId(targetActUsers.stream().map(ZnsRunActivityUserEntity::getUserId).toList(), mainActivityId);
            }

            //只查真实用户
            if (isAllUser == 1) {
                accountDetails = accountDetails.stream().filter(k -> k.getIsRobot() == 0 && k.getIsTest() == 0).toList();
            }
            if (CollectionUtils.isEmpty(accountDetails)) {
                participantInfo.setAwardSentNum(0);
            } else {
                Long count = accountDetails.stream().filter(k -> AccountDetailTypeEnum.NEW_ACTIVITY_100.getType().equals(k.getTradeType()))
                        .map(ZnsUserAccountDetailEntity::getUserId).distinct().count();
                participantInfo.setAwardSentNum(count.intValue());
            }

            //目标
            participantInfo.setTarget(target);
            singleActivityParticipantInfos.add(participantInfo);
        }
        return singleActivityParticipantInfos;

    }

    private void postProcessSingleResponse(MainActivity mainActivity, SingleActivityCreateResponse response) {
        if (mainActivity.getTargetType() == 2) {
            SingleActivityRuleResponse ruleResponse = response.getSingleActivityRuleResponse();
            ruleResponse.setTargets(ruleResponse.getTargets().stream().map(k -> k / 60).toList());
        }
        if (mainActivity.getTimeStyle() == 0) {
            SingleActivityReportResponse reportResponse = response.getSingleActivityReportResponse();
            reportResponse.setActivityStartTime(DateUtil.convertTimeStrByZone(mainActivity.getActivityStartTime(), "UTC", "GMT+08:00"));
            reportResponse.setActivityEndTime(DateUtil.convertTimeStrByZone(mainActivity.getActivityEndTime(), "UTC", "GMT+08:00"));
            if (StringUtils.hasText(mainActivity.getApplicationStartTime())) {
                reportResponse.setApplicationStartTime(
                        DateUtil.convertTimeStrByZone(mainActivity.getApplicationStartTime(), "UTC", "GMT+08:00"));
            }
            reportResponse.setApplicationEndTime(DateUtil.convertTimeStrByZone(mainActivity.getApplicationEndTime(), "UTC", "GMT+08:00"));

        }
    }

    private SingleActivityInfoResponse findSingleSingleActivityInfo(MainActivity mainActivity) {

        SingleActivityInfoResponse response = new SingleActivityInfoResponse();
        response.setActivityState(mainActivity.getActivityState());
        return response;

    }

    private SingleActivityDistributionResponse findSingleActivityDistribution(MainActivity mainActivity) {

        Long mainActId = mainActivity.getId();
        SingleActivityDistributionResponse response = new SingleActivityDistributionResponse();
        List<ActivityDisseminate> activityDisseminateList = activityDisseminateService.findByActId(mainActId);
        List<SingleDistributionCreateRequest> distributionRequests = activityDisseminateList.stream().map(k -> {
            SingleDistributionCreateRequest distributionRequest = new SingleDistributionCreateRequest();
            BeanUtil.copyPropertiesIgnoreNull(k, distributionRequest);
            distributionRequest.setCategoryType(mainActivity.getCategoryType());
            return distributionRequest;
        }).toList();
        response.setDistributionRequests(distributionRequests);
        response.setIsShowInMore(mainActivity.getIsShowInMore());

        List<ActivityParams> optional = activityParamsService.findListByMainActivityAndParamTypes(mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME);
        if (!CollectionUtils.isEmpty(optional)) {
            ActivityRecommendSetting activityRecommendSetting = new ActivityRecommendSetting();
            List<String> position = new ArrayList<>();
            optional.forEach(item -> {
                if (item.getParamKey().equals(ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY.getCode())
                        && "1".equals(item.getParamValue())) {
                    position.add(ActivityRecommendPositionEnum.COMMUNITY.getPosition());
                }
                if (item.getParamKey().equals(ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME.getCode())
                        && "1".equals(item.getParamValue())) {
                    position.add(ActivityRecommendPositionEnum.HOME.getPosition());
                }
            });
            activityRecommendSetting.setRecommendPosition(position);
            activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE).ifPresent(item -> {
                activityRecommendSetting.setDefaultLanguageCode(item.getParamValue());
            });
            ActivityRecommendDisseminateQuery q = new ActivityRecommendDisseminateQuery();
            q.setMainActivityId(mainActId);
            q.setPosition(ActivityRecommendPositionEnum.COMMUNITY.getPosition());
            List<ActivityRecommendCommunityDesc> descs = activityRecommendDisseminateService.findList(q).stream().map(item -> {
                ActivityRecommendCommunityDesc desc = new ActivityRecommendCommunityDesc();
                desc.setLanguageCode(item.getLanguageCode());
                desc.setDesc(item.getDescription());
                return desc;
            }).toList();
            activityRecommendSetting.setCommunityDesc(descs);
            response.setActivityRecommendSetting(activityRecommendSetting);
        }
        //填充职业赛信息
        response.setProActivitySettings(proActivityConvert.toSettings(proActivityService.findByMainActivityId(mainActId).orElse(null)));
        return response;


    }

    private SingleActivityFeeAndAwardResponse findSingleActivityFeeAndAward(MainActivity mainActivity, Boolean isCopy) {
        SingleActivityFeeAndAwardResponse response = new SingleActivityFeeAndAwardResponse();
        Long mainActId = mainActivity.getId();
        List<ActivityFee> activityFeeList = activityFeeService.findByActId(mainActId);
        List<CurrencyAmount> amountList = activityFeeList.stream().map(k -> {
//            CurrencyAmount currencyAmount = new CurrencyAmount();
//            currencyAmount.setCurrencyCode(k.getCurrency());
            CurrencyAmount currencyAmount = I18nConstant.CurrencyCodeEnum
                    .findByCode(k.getCurrency()).getCurrencyAmount(k.getAmount());
            BeanUtil.copyPropertiesIgnoreNull(k, currencyAmount);
            return currencyAmount;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(amountList)) {
            BeanUtil.copyPropertiesIgnoreNull(activityFeeList.get(0), response);
        } else {
            amountList = new ArrayList<>();
        }
        response.setAmounts(amountList);

        //优惠劵回显
        Long couponId = mainActivity.getCouponId();
        if (couponId != null) {
            CouponDetailVo couponDetail = couponService.couponDetail(couponId);
            response.setCoupon(couponDetail);
        }

        //品牌权益
        response.setBrandRightDto(getBrandRightDto(mainActivity));

        //奖励回显
        List<ActivityTargetAwardDto> targetAwardDtos = awardActivityBizService.getAwardByAct(mainActivity);

        //报名奖励回显
        ApplicationRewardDto applicationRewardDto = awardActivityBizService.queryApplicationAward(mainActivity.getId());

        response.setTargetAwardDtos(targetAwardDtos);
        response.setApplicationRewardDto(applicationRewardDto);
        response.setTeamAwardType(TeamShareTypeEnum.NORMAL.getType());
        ActivityImpracticalAwardConfig impracticalAwardConfig = impracticalAwardConfigService.findByActId(mainActivity.getId());
        if (Objects.nonNull(impracticalAwardConfig)) {
            //分享奖励回显
            response.setTeamAwardType(impracticalAwardConfig.getTeamAwardType());
            if (impracticalAwardConfig.getShareScore() == -1) {
                response.setShareScore(null);
            } else {
                response.setShareScore(impracticalAwardConfig.getShareScore());
            }
        }
        setActivityParams(response, mainActivity.getId());

        //回显视频审核
        activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.VIDEO_VIEW_CONFIG)
                .ifPresent(k -> response.setVideoViewDto(JsonUtil.readValue(k.getParamValue(), VideoViewDto.class)));
        return response;
    }


    private void setActivityParams(SingleActivityFeeAndAwardResponse response, Long activityId) {
        // 奖励发放方式审核
        List<ActivityParams> activityAndParamTypes = activityParamsService.findListByMainActivityAndParamTypes(activityId,
                ActivitySettingConfigEnum.AWARD_SEND_TYPE,
                ActivitySettingConfigEnum.COMPLETE_AWARD_SEND_TYPE,
                ActivitySettingConfigEnum.SURPASS_AWARD_SEND_TYPE,
                ActivitySettingConfigEnum.COMPLETE_AMOUNT_AWARD_TYPE,
                ActivitySettingConfigEnum.COMPLETE_SCORE_AWARD_TYPE,
                ActivitySettingConfigEnum.SURPASS_AMOUNT_AWARD_TYPE,
                ActivitySettingConfigEnum.SURPASS_SCORE_AWARD_TYPE,
                ActivitySettingConfigEnum.SURPASS_USER_CODE,
                ActivitySettingConfigEnum.RECORD_BREAKING_AWARD_SEND_TYPE,
                ActivitySettingConfigEnum.ALLOW_AWARD_COUPON_SWITCH);
        if (CollectionUtils.isEmpty(activityAndParamTypes)) {
            return;
        }

        activityAndParamTypes.forEach(k -> {
            if (k.getParamKey().equals(ActivitySettingConfigEnum.AWARD_SEND_TYPE.getCode())) {
                response.setAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.COMPLETE_AWARD_SEND_TYPE.getCode())) {
                response.setCompleteAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.SURPASS_AWARD_SEND_TYPE.getCode())) {
                response.setSurpassAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.COMPLETE_AMOUNT_AWARD_TYPE.getCode())) {
                response.setCompleteAmountAwardType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.COMPLETE_SCORE_AWARD_TYPE.getCode())) {
                response.setCompleteScoreAwardType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.SURPASS_AMOUNT_AWARD_TYPE.getCode())) {
                response.setSurpassAmountAwardType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.SURPASS_SCORE_AWARD_TYPE.getCode())) {
                response.setSurpassScoreAwardType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.SURPASS_USER_CODE.getCode())) {
                response.setSurpassUserCode(k.getParamValue());
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.RECORD_BREAKING_AWARD_SEND_TYPE.getCode())) {
                response.setRecordBreakingAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.ALLOW_AWARD_COUPON_SWITCH.getCode())) {
                response.setAllowAwardCouponSwitch(Integer.valueOf(k.getParamValue()));
            }
        });
    }


    private SingleActivityReportResponse findSingleActivityReport(MainActivity mainActivity) {
        SingleActivityReportResponse response = new SingleActivityReportResponse();
        Long mainActId = mainActivity.getId();

        BeanUtil.copyPropertiesIgnoreNull(mainActivity, response);
        SubActivity subActivity = subActivityService.getSingleActByMain(mainActId);
        BeanUtil.copyPropertiesIgnoreNull(subActivity, response);

        //回显活动地区
        List<RegionDto> activityAreas = activityAreaBizService.getRegionDtoList(mainActId);
        if (CollectionUtils.isEmpty(activityAreas)) {
            activityAreas = List.of(RegionDto.buildGlobal());
        }
        response.setAreas(activityAreas);

        //回显运营人群信息
        List<UserGroupDto> userGroupDtoList = getUserGroupsByActId(mainActId);
        response.setGroups(userGroupDtoList);
        if (CollectionUtils.isEmpty(userGroupDtoList)) {
            response.setGroupType(-1);
        } else {
            response.setGroupType(activityUserGroupService.findByActId(mainActId).get(0).getType());
        }

        //回显互斥活动
        String mutexActivityIds = mainActivity.getMutexActivityIds();
        if (StringUtils.hasText(mutexActivityIds)) {
            response.setMutexActivityIds(JsonUtil.readList(mutexActivityIds, Long.class));
        }
        //回显设备
        List<Equipment> equipments = getEquipmentsByActId(mainActId);
        response.setEquipments(equipments);


        //回显配速员信息
        List<PacerConfig> pacerConfigList = pacerConfigService.findByActId(mainActivity.getId());
        List<ActivityPaceSetting> paceSettings = pacerConfigList.stream().map(k -> {
            ActivityPaceSetting paceSetting = new ActivityPaceSetting();
            BeanUtil.copyPropertiesIgnoreNull(k, paceSetting);
            return paceSetting;
        }).toList();
        response.setPaceSetting(paceSettings);

        //回显机器人配置
        ActivityRotSetting activityRotSetting = activityRotSettingService.findByActId(mainActId);
        RotSetting rotSetting = new RotSetting();
        BeanUtil.copyPropertiesIgnoreNull(activityRotSetting, rotSetting);
        response.setRotSetting(rotSetting);
        response.setEquipmentVersion(mainActivity.getEquipmentVersion());
        //deskBike version版本号。
        Optional<ActivityParams> oneByMainActivityAndParamType = activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.DESK_BIKE_EQUIPMENT_VERSION);
        if (oneByMainActivityAndParamType.isPresent()) {
            response.setDeskBikeEquipmentVersion(Integer.valueOf(oneByMainActivityAndParamType.get().getParamValue()));
        }
        //回显活动阶段
        List<ActivityStage> stageList = activityStageService.findByActId(mainActId);
        if (!CollectionUtils.isEmpty(stageList)) {
            List<ActivityStageDto> stageDtoList = stageList.stream().sorted(Comparator.comparing(ActivityStage::getStartTime)).map(k -> {
                ActivityStageDto stageDto = new ActivityStageDto();
                stageDto.setStartTime(k.getStartTime());
                stageDto.setEndTime(k.getEndTime());
                return stageDto;
            }).collect(Collectors.toList());
            response.setStages(stageDtoList);
        }
        //回显开赛时间
        Optional<ActivityParams> optional = activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.AFTER_START_LIMIT);
        optional.ifPresent(k -> response.setAfterStartLimit(Integer.valueOf(k.getParamValue())));
        activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.RAM_LIMIT)
                .ifPresentOrElse(k -> response.setRamLimit(Integer.valueOf(k.getParamValue())), () -> response.setRamLimit(ActivityStringConstant.KEYS.DEFAULT_RAM_LIMIT));
        activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.IS_AI_COMMENTARY).ifPresentOrElse(k -> response.setIsAiCommentary(Integer.valueOf(k.getParamValue())), () -> response.setIsAiCommentary(0));
        activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.GENDER_LIMIT).ifPresent(k -> response.setGender(Integer.valueOf(k.getParamValue())));

        return response;
    }

    private List<UserGroupDto> getUserGroupsByActId(Long mainActId) {
        List<ActivityUserGroup> userGroupList = activityUserGroupService.findByActId(mainActId);
        if (CollectionUtils.isEmpty(userGroupList)) {
            return null;
        }

        return userGroupList.stream().map(k -> {
            UserGroupEntity userGroup = userGroupService.findById(k.getGroupId());
            if (userGroup == null) {
                return null;
            }
            UserGroupDto userGroupDto = new UserGroupDto();
            BeanUtil.copyPropertiesIgnoreNull(userGroup, userGroupDto);
            return userGroupDto;
        }).filter(Objects::nonNull).toList();
    }

    private List<Equipment> getEquipmentsByActId(Long mainActId) {
        List<ActivityEquipmentConfig> equipmentConfigs = activityEquipmentConfigService.findByActId(mainActId);
        List<Equipment> equipments = equipmentConfigs.stream().map(k -> {
            Equipment equipment = new Equipment();
            BeanUtil.copyPropertiesIgnoreNull(k, equipment);
            return equipment;
        }).toList();
        return equipments;
    }

    private SingleActivityRuleResponse findSingleActivityRule(MainActivity mainActivity) {

        SingleActivityRuleResponse response = new SingleActivityRuleResponse();

        BeanUtil.copyPropertiesIgnoreNull(mainActivity, response);

        List<SubActivity> subActivityList = subActivityService.getAllSingleActByMain(mainActivity.getId());

        //设置target
        response.setTargets(subActivityList.stream().map(SubActivity::getTarget).toList());
        BeanUtil.copyPropertiesIgnoreNull(subActivityList.get(0), response);


        //回显道具配置
        List<ActivityPropConfig> activityPropConfigs = activityPropConfigService.findByActId(mainActivity.getId());
        List<ActivityPropResponse> propResponses = activityPropConfigs.stream().map(t -> {
            ActivityPropResponse activityPropResponse = new ActivityPropResponse();
            BeanUtil.copyPropertiesIgnoreNull(t, activityPropResponse);
            PropManage propManage = propManageService.selectPropManageByPropId(t.getPropId());
            if (propManage != null) {
                activityPropResponse.setPropName(propManage.getTitle());
                activityPropResponse.setPropType(propManage.getPropType());
            }
            return activityPropResponse;
        }).toList();
        response.setActivityPropRes(propResponses);


        //回显活动速度限制
        List<ActivityRateLimit> activityRateLimits = activityRateLimitService.findList(ActivityRateLimitQuery.builder()
                .activityId(mainActivity.getId()).build());

        List<ActivityRateLimitDto> rateLimits = new ArrayList<>();
        Map<BigDecimal, List<ActivityRateLimit>> listMap = activityRateLimits.stream().collect(Collectors.groupingBy(ActivityRateLimit::getRunningGoal));
        for (Map.Entry<BigDecimal, List<ActivityRateLimit>> entry : listMap.entrySet()) {
            ActivityRateLimitDto rateLimitDto = new ActivityRateLimitDto();
            List<RateLimitDetailDto> detailDtos = entry.getValue().stream().map(k -> {
                RateLimitDetailDto limitDetailDto = new RateLimitDetailDto();
                BeanUtil.copyPropertiesIgnoreNull(k, limitDetailDto);
                return limitDetailDto;
            }).sorted(Comparator.comparing(RateLimitDetailDto::getStart)).toList();
            rateLimitDto.setRunningGoal(entry.getKey());
            rateLimitDto.setRateLimitDetailDtoList(detailDtos);
            rateLimits.add(rateLimitDto);
        }
        //排序
        rateLimits = rateLimits.stream().sorted(Comparator.comparing(ActivityRateLimitDto::getRunningGoal))
                .toList();


        //回显歌单详情
        List<MusicPlayDetailDto> musicList = findMusicList(mainActivity.getId());
        response.setMusicList(musicList);
        response.setRateLimit(rateLimits);

        //回显玩法
        Gameplay gameplay = gameplayService.findById(mainActivity.getPlayId());
        response.setPlayId(gameplay.getId());
        response.setPlayName(gameplay.getTitle());
        response.setEquipmentMainType(gameplay.getEquipmentMainType());

        //回显跑道名称
        response.setRouteId(subActivityList.get(0).getRouteId());
        response.setRouteName(runRouteService.getRouteTitle(subActivityList.get(0).getRouteId().intValue()));

        //回显团队赛
        List<TeamConfigDto> configDtos = getTeamConfigDtoByActId(mainActivity.getId());
        response.setNonOfficialTeamConfigDtos(configDtos.stream().filter(k -> k.getIsOfficial() == 0).collect(Collectors.toList()));
        response.setTeamConfigDtos(configDtos.stream().filter(k -> k.getIsOfficial() == 1).collect(Collectors.toList()));
        //回显团队赛字段数据
        ActivityTeamJoinSettingDto activityTeamSetting = activityParamsLoaderService.getActivityTeamSetting(mainActivity.getId());
        if (activityTeamSetting != null && activityTeamSetting.getAllowJoinTeamType() != null) {
            response.setTeamType(activityTeamSetting.getAllowJoinTeamType().getCode());
            response.setClubTeamConfig(activityTeamSetting.getClubTeamConfig());
            response.setMaxTeamNum(activityTeamSetting.getAllowJoinTeamCount());
        }
        return response;
    }

    private List<TeamConfigDto> getTeamConfigDtoByActId(Long mainActivityId) {
        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(mainActivityId);
        List<TeamConfigDto> configDtos = teams.stream().map(k -> {
            TeamConfigDto teamConfigDto = new TeamConfigDto();
            BeanUtil.copyPropertiesIgnoreNull(k, teamConfigDto);
            ZnsUserEntity znsUserEntity = userService.findById(k.getTeamManagerId());
            teamConfigDto.setCaptainEmail(Objects.nonNull(znsUserEntity) ? znsUserEntity.getEmailAddressEn() : null);
            return teamConfigDto;
        }).toList();
        return configDtos;
    }

    private List<MusicPlayDetailDto> findMusicList(Long mainActivityId) {
        List<ActivityPlaylistRel> activityPlaylistRels = activityPlaylistRelService.findByActId(mainActivityId);
        List<Long> playListIds = activityPlaylistRels.stream().map(ActivityPlaylistRel::getPlaylistId).toList();
        if (CollectionUtils.isEmpty(playListIds)) {
            return new ArrayList<>();
        }
        RunPlaylistQuery query = new RunPlaylistQuery();
        query.setPlaylistIds(playListIds);
        List<RunPlaylist> runPlaylists = runPlaylistService.findRunPlaylistByQuery(query);
        return runPlaylists.stream().map(k -> {
            MusicPlayDetailDto detailDto = new MusicPlayDetailDto();
            BeanUtil.copyPropertiesIgnoreNull(k, detailDto);
            detailDto.setMusicNum(playlistMusicRelService.countByPlaylistId(k.getId()));
            return detailDto;
        }).toList();

    }

    @FillerMethod
    public SeriesActivityCreateResponse seriesActivityDetail(Long mainActId, Boolean isCopy) {
        SeriesActivityCreateResponse response = new SeriesActivityCreateResponse();

        MainActivity mainActivity = mainActivityService.findById(mainActId);

        //回显基础信息
        SeriesActivityBaseInfoDto baseInfoDto = findSeriesActBaseInfoDto(mainActivity);
        //回显整体规则奖励
        SeriesActivityAllRulesResponse rulesResponse = findSeriesActAllRules(mainActivity, isCopy);
        //回显处理子阶段
        List<SeriesActivitySegmentResponse> segmentResponse = findSeriesActSegments(mainActivity);
        //回显活动说明，宣发
        List<SeriesActivityDistributionResponse> distributionResponse = findSeriesActDisseminate(mainActivity);

        response.setSeriesActivityAllRulesResponse(rulesResponse);
        response.setSegmentResponses(segmentResponse);
        response.setActivityDistributionResponses(distributionResponse);
        response.setIsShowInMore(mainActivity.getIsShowInMore());
        response.setBaseInfoDto(baseInfoDto);
        //设置活动推荐信息
        setActivityRecommentDisseminate(mainActId, response);
        //后置处理系列赛res
        postProcessSeriesResponse(mainActivity, response);

        response.setCompetitiveSeasonSettingDto(loadCompetitiveConfig(mainActivity));
        //写入职业赛信息
        response.setProActivitySettings(proActivityConvert.toSettings(proActivityService.findByMainActivityId(mainActId).orElse(null)));
        return response;
    }

    private void setActivityRecommentDisseminate(Long mainActId, SeriesActivityCreateResponse response) {
        List<ActivityParams> optional = activityParamsService.findListByMainActivityAndParamTypes(mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME);
        if (!CollectionUtils.isEmpty(optional)) {
            ActivityRecommendSetting activityRecommendSetting = new ActivityRecommendSetting();
            List<String> position = new ArrayList<>();
            optional.forEach(item -> {
                if (item.getParamKey().equals(ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY.getCode()) && "1".equals(item.getParamValue())) {
                    position.add(ActivityRecommendPositionEnum.COMMUNITY.getPosition());
                }
                if (item.getParamKey().equals(ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME.getCode()) && "1".equals(item.getParamValue())) {
                    position.add(ActivityRecommendPositionEnum.HOME.getPosition());
                }
            });
            activityRecommendSetting.setRecommendPosition(position);
            activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE).ifPresent(item -> {
                activityRecommendSetting.setDefaultLanguageCode(item.getParamValue());
            });
            ActivityRecommendDisseminateQuery q = new ActivityRecommendDisseminateQuery();
            q.setMainActivityId(mainActId);
            q.setPosition(ActivityRecommendPositionEnum.COMMUNITY.getPosition());
            List<ActivityRecommendCommunityDesc> descs = activityRecommendDisseminateService.findList(q).stream().map(item -> {
                ActivityRecommendCommunityDesc desc = new ActivityRecommendCommunityDesc();
                desc.setLanguageCode(item.getLanguageCode());
                desc.setDesc(item.getDescription());
                return desc;
            }).toList();
            activityRecommendSetting.setCommunityDesc(descs);
            response.setActivityRecommendSetting(activityRecommendSetting);
        }
    }

    private void postProcessSeriesResponse(MainActivity mainActivity, SeriesActivityCreateResponse response) {
        //目前系列赛各阶段统一
//        for (SeriesActivitySegmentResponse segmentResponse : response.getSegmentResponses()) {
//            if (segmentResponse.getTargetType() == 2){
//                segmentResponse.setTargets(segmentResponse.getTargets().stream().map(k->k/60).toList());
//            }
//        }

        if (mainActivity.getTimeStyle() == 0) {
            SeriesActivityAllRulesResponse allRulesResponse = response.getSeriesActivityAllRulesResponse();
            allRulesResponse.setActivityStartTime(DateUtil.convertTimeStrByZone(mainActivity.getActivityStartTime(), "UTC", "GMT+08:00"));
            allRulesResponse.setActivityEndTime(DateUtil.convertTimeStrByZone(mainActivity.getActivityEndTime(), "UTC", "GMT+08:00"));
            if (StringUtils.hasText(mainActivity.getApplicationStartTime())) {
                allRulesResponse.setApplicationStartTime(
                        DateUtil.convertTimeStrByZone(mainActivity.getApplicationStartTime(), "UTC", "GMT+08:00"));
            }
            allRulesResponse.setApplicationEndTime(DateUtil.convertTimeStrByZone(mainActivity.getApplicationEndTime(), "UTC", "GMT+08:00"));

        }
    }


    private SeriesActivityBaseInfoDto findSeriesActBaseInfoDto(MainActivity mainActivity) {
        SeriesActivityBaseInfoDto baseInfoDto = new SeriesActivityBaseInfoDto();
        baseInfoDto.setActivityId(mainActivity.getId());
        baseInfoDto.setActivityNo(mainActivity.getActivityNo());
        baseInfoDto.setActivityState(mainActivity.getActivityState());
        return baseInfoDto;
    }

    private List<SeriesActivityDistributionResponse> findSeriesActDisseminate(MainActivity mainActivity) {
        Long mainActId = mainActivity.getId();
        List<ActivityDisseminate> disseminateList = activityDisseminateService.findByActId(mainActId);
        //回显宣发
        List<SeriesActivityDistributionResponse> list = disseminateList.stream().map(k -> {
            SeriesActivityDistributionResponse distributionResponse = new SeriesActivityDistributionResponse();
            BeanUtil.copyPropertiesIgnoreNull(k, distributionResponse);
            distributionResponse.setCategoryType(mainActivity.getCategoryType());
//            //回显页面配置
//            List<ActivityPageSetting> pageSettingList = activityPageSettingService.getByDisseminateId(k.getId());
//            List<SeriesActivityPageSetting> pageSettingDtos = pageSettingList.stream().map(t -> {
//                SeriesActivityPageSetting pageSettingDto = new SeriesActivityPageSetting();
//                BeanUtil.copyPropertiesIgnoreNull(t, pageSettingDto);
//                return pageSettingDto;
//            }).toList();
//            distributionResponse.setPageSettings(pageSettingDtos);
            return distributionResponse;
        }).toList();

        return list;


    }

    private List<SeriesActivitySegmentResponse> findSeriesActSegments(MainActivity mainActivity) {
        Long mainActId = mainActivity.getId();

        List<MainActivity> segmentMainActs = seriesActivityRelService.getAllMainActivity(mainActivity.getId());

//        List<SubActivity> subActivityList = subActivityService.getAllSeriesActByMain(mainActId);

        List<SeriesActivitySegmentResponse> segmentResponses = segmentMainActs.stream().map(k -> {

            SeriesActivitySegmentResponse segmentResponse = new SeriesActivitySegmentResponse();
            BeanUtil.copyPropertiesIgnoreNull(k, segmentResponse);

            //查找子活动
            List<SubActivity> subActivityList = subActivityService.getAllSingleActByMain(k.getId());
            segmentResponse.setTargets(subActivityList.stream().map(SubActivity::getTarget).toList());
            //时长 * 60
            if (k.getTargetType() == 2) {
                segmentResponse.setTargets(segmentResponse.getTargets().stream().map(n -> n / 60).toList());
            }
            SubActivity subActivity = subActivityList.get(0);
            BeanUtil.copyPropertiesIgnoreNull(subActivity, segmentResponse);

            //
            segmentResponse.setTargetType(k.getTargetType());

            //回显配速员信息
            List<PacerConfig> pacerConfigs = pacerConfigService.findByActId(k.getId());
            List<ActivityPaceSetting> paceSettings = pacerConfigs.stream().map(t -> {
                ActivityPaceSetting paceSetting = new ActivityPaceSetting();
                BeanUtil.copyPropertiesIgnoreNull(t, paceSetting);
                return paceSetting;
            }).toList();
            segmentResponse.setPaceSetting(paceSettings);

            //回显道具信息
            List<ActivityPropConfig> activityPropConfigs = activityPropConfigService.findByActId(k.getId());
            List<ActivityPropResponse> propResponses = activityPropConfigs.stream().map(t -> {
                ActivityPropResponse activityPropResponse = new ActivityPropResponse();
                BeanUtil.copyPropertiesIgnoreNull(t, activityPropResponse);
                PropManage propManage = propManageService.selectPropManageByPropId(t.getPropId());
                activityPropResponse.setPropName(propManage.getTitle());
                activityPropResponse.setPropType(propManage.getPropType());
                return activityPropResponse;
            }).toList();
            segmentResponse.setActivityPropResponseList(propResponses);

            //奖励回显
            List<ActivityTargetAwardDto> targetAwardDtos = awardActivityBizService.getAwardByAct(k);
            segmentResponse.setTargetAwardDtos(targetAwardDtos);

            //回显歌单
            segmentResponse.setMusicList(findMusicList(k.getId()));

            //回显团队赛
            List<TeamConfigDto> configDtos = getTeamConfigDtoByActId(k.getId());
            segmentResponse.setTeamConfigDtos(configDtos);

            //回显跑道
            segmentResponse.setRouteName(runRouteService.getRouteTitle(subActivity.getRouteId().intValue()));

            //回显本阶段参赛条件
            ActivityEnterThreshold threshold = activityEnterThresholdService.findByActId(k.getId());
            if (threshold != null) {
                segmentResponse.setConditionState(threshold.getConditionState());
            }


            //回显活动速度限制
            List<ActivityRateLimit> activityRateLimits = activityRateLimitService.findList(
                    ActivityRateLimitQuery.builder()
                            .activityId(k.getId())
                            .build()
            );

            List<ActivityRateLimitDto> rateLimits = new ArrayList<>();
            Map<BigDecimal, List<ActivityRateLimit>> listMap = activityRateLimits.stream().collect(Collectors.groupingBy(ActivityRateLimit::getRunningGoal));
            for (Map.Entry<BigDecimal, List<ActivityRateLimit>> entry : listMap.entrySet()) {
                ActivityRateLimitDto rateLimitDto = new ActivityRateLimitDto();
                List<RateLimitDetailDto> detailDtos = entry.getValue().stream().map(m -> {
                    RateLimitDetailDto limitDetailDto = new RateLimitDetailDto();
                    BeanUtil.copyPropertiesIgnoreNull(m, limitDetailDto);
                    return limitDetailDto;
                }).sorted(Comparator.comparing(RateLimitDetailDto::getStart)).toList();
                rateLimitDto.setRunningGoal(entry.getKey());
                rateLimitDto.setRateLimitDetailDtoList(detailDtos);
                rateLimits.add(rateLimitDto);
            }
            //排序
            rateLimits = rateLimits.stream().sorted(Comparator.comparing(ActivityRateLimitDto::getRunningGoal))
                    .toList();

            segmentResponse.setRateLimit(rateLimits);

            return segmentResponse;
        }).toList();


        return segmentResponses;

    }

    private SeriesActivityAllRulesResponse findSeriesActAllRules(MainActivity mainActivity, Boolean isCopy) {
        SeriesActivityAllRulesResponse response = new SeriesActivityAllRulesResponse();
        Long mainActId = mainActivity.getId();

        //玩法回显
        Gameplay gameplay = gameplayService.findById(mainActivity.getPlayId());
        response.setPlayId(gameplay.getId());
        response.setPlayName(gameplay.getTitle());
        response.setEquipmentMainType(gameplay.getEquipmentMainType());

        //回显互斥活动
        String mutexActivityIds = mainActivity.getMutexActivityIds();
        if (StringUtils.hasText(mutexActivityIds)) {
            response.setMutexActivityIds(JsonUtil.readList(mutexActivityIds, Long.class));
        }
        //回显活动地区
        List<RegionDto> activityAreas = activityAreaBizService.getRegionDtoList(mainActId);
        if (CollectionUtils.isEmpty(activityAreas)) {
            activityAreas = List.of(RegionDto.buildGlobal());
        }
        response.setAreas(activityAreas);
        //回显运营人群信息
        List<UserGroupDto> groupDtoList = getUserGroupsByActId(mainActId);
        response.setGroups(groupDtoList);
        if (CollectionUtils.isEmpty(groupDtoList)) {
            response.setGroupType(-1);
        } else {
            response.setGroupType(activityUserGroupService.findByActId(mainActId).get(0).getType());
        }

        BeanUtil.copyPropertiesIgnoreNull(mainActivity, response);

        //回显设备
        List<Equipment> equipments = getEquipmentsByActId(mainActId);
        response.setEquipments(equipments);
        //报名奖励回显
        ApplicationRewardDto applicationRewardDto = awardActivityBizService.queryApplicationAward(mainActivity.getId());

        response.setApplicationRewardDto(applicationRewardDto);
        response.setFeeAndAwardResponse(findSingleActivityFeeAndAward(mainActivity, isCopy));
        response.setEquipmentVersion(mainActivity.getEquipmentVersion());
        List<ActivityParams> activityAndParamTypes = activityParamsService.findListByMainActivityAndParamTypes(mainActivity.getId(),
                ActivitySettingConfigEnum.AWARD_SEND_TYPE);
        if (!CollectionUtils.isEmpty(activityAndParamTypes)) {
            response.setAwardSendType(Integer.valueOf(activityAndParamTypes.get(0).getParamValue()));
        } else {
            response.setAwardSendType(0);
        }
        //系列赛回显活动阶段
        List<SeriesActivityRel> activityRels = seriesActivityRelService.findSubActivity(mainActId);
        if (activityRels.size() == 1) {
            List<ActivityStage> stageList = activityStageService.findByActId(activityRels.get(0).getSegmentActivityId());
            if (!CollectionUtils.isEmpty(stageList)) {
                List<ActivityStageDto> stageDtoList = stageList.stream().sorted(Comparator.comparing(ActivityStage::getStartTime)).map(k -> {
                    ActivityStageDto stageDto = new ActivityStageDto();
                    stageDto.setStartTime(k.getStartTime());
                    stageDto.setEndTime(k.getEndTime());
                    return stageDto;
                }).collect(Collectors.toList());
                response.setStages(stageDtoList);
            }
        }

        //回显开赛时间
        Optional<ActivityParams> optional = activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.AFTER_START_LIMIT);
        optional.ifPresent(k -> response.setAfterStartLimit(Integer.valueOf(k.getParamValue())));

        activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.RAM_LIMIT)
                .ifPresentOrElse(k -> response.setRamLimit(Integer.valueOf(k.getParamValue())), () -> response.setRamLimit(ActivityStringConstant.KEYS.DEFAULT_RAM_LIMIT));
        activityParamsService.findOneByMainActivityAndParamType(mainActId, ActivitySettingConfigEnum.IS_AI_COMMENTARY).ifPresentOrElse(k -> response.setIsAiCommentary(Integer.valueOf(k.getParamValue())), () -> response.setIsAiCommentary(0));
        return response;
    }

    @FillerMethod
    public Page<ActivityDto> activityList(ActivityListQuery query) {
        QueryWrapper<MainActivity> wrapper = MpUtil.generateWrapper(MainActivity.class, query);
        Page<ActivityDto> dtoPage = new Page<>();

        if (query.getIsAutoCreate() != null) {
            List<Long> actIds = polymerizationActivityPoleService.getActIdsByAutoStatus(1);
            if (CollectionUtils.isEmpty(actIds)) {
                return dtoPage;
            }
            if (Objects.equals(query.getIsAutoCreate(), 1)) {
                wrapper.in("id", actIds);
            }
            if (Objects.equals(query.getIsAutoCreate(), 0)) {
                wrapper.notIn("id", actIds);
            }
        }
        ZonedDateTime activityStartTime = query.getActivityStartTime();
        ZonedDateTime activityEndTime = query.getActivityEndTime();
        if (activityStartTime != null) {

            wrapper.and(k -> k.and(w -> w.ge("activity_start_time", DateUtil.getStrByDateAndZone(activityStartTime, TimeZone.getTimeZone("UTC")))
                            .eq("time_style", 0))
                    .or(w -> w.ge("activity_start_time", DateUtil.getStrByDateAndZone(activityStartTime, TimeZone.getTimeZone("GMT+08:00")))
                            .eq("time_style", 1)));

//            String formattedDate = DateUtil.getStrByDateAndZone(activityStartTime,TimeZone.getTimeZone("GMT+08:00"));
//            wrapper.ge("activity_start_time",formattedDate);
        }
        if (activityEndTime != null) {
            wrapper.and(k -> k.and(w -> w.le("activity_end_time", DateUtil.getStrByDateAndZone(activityEndTime, TimeZone.getTimeZone("UTC")))
                            .eq("time_style", 0))
                    .or(w -> w.le("activity_end_time", DateUtil.getStrByDateAndZone(activityEndTime, TimeZone.getTimeZone("GMT+08:00")))
                            .eq("time_style", 1)));
//            String formattedDate = DateUtil.getStrByDateAndZone(activityEndTime,TimeZone.getTimeZone("GMT+08:00"));
//            wrapper.le("activity_end_time",formattedDate);
        }
        //处理竞技赛赛事类型查询条件
        if (StringUtils.hasText(query.getCompetitiveType())) {
            Optional<ActivityCompetitiveTypeEnum> competitiveTypeEnumOptional = ActivityCompetitiveTypeEnum.of(query.getCompetitiveType());
            if (competitiveTypeEnumOptional.isPresent()) {
                switch (competitiveTypeEnumOptional.get()) {
                    case COMPETITIVE -> {
                        wrapper.eq("is_competitive", 1);
                        wrapper.eq("is_show_in_more", 0);
                    }
                    case SHOW_IN_MORE -> {
                        wrapper.eq("is_competitive", 0);
                        wrapper.eq("is_show_in_more", 1);
                    }
                    case OTHER -> {
                        wrapper.eq("is_competitive", 0);
                        wrapper.eq("is_show_in_more", 0);
                    }
                }
            }

        }
        //处理标题
        if (StringUtils.hasText(query.getActivityTitle())) {
            List<Long> actIds = activityDisseminateService.findByTitle(query.getActivityTitle(), true);
            if (!CollectionUtils.isEmpty(actIds)) {
                wrapper.in("id", actIds);
            } else {
                return dtoPage;
            }
        }
        //处理地区
        if (!CollectionUtils.isEmpty(query.getAreaId())) {
            List<Long> areaIds = query.getAreaId();
            AreaQuery areaQuery = AreaQuery.builder().ids(areaIds).build();
            List<AreaEntity> areaList = areaService.findByQuery(areaQuery);
            List<Long> actIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(areaList)) {
                //按国家查
                List<Long> countryIds = areaList.stream().map(AreaEntity::getCountryId).distinct().toList();
                List<ActivityArea> countryActivityList = activityAreaService.findByAreaIdsAndRegionType(countryIds, RegionConstants.RegionTypeEnum.REGION_TYPE_2.getCode());
                if (!CollectionUtils.isEmpty(countryActivityList)) {
                    actIds.addAll(countryActivityList.stream().map(ActivityArea::getMainActivityId).distinct().toList());
                }
                //按州查
                List<ActivityArea> stateActivityList = activityAreaService.findByAreaIdsAndRegionType(areaIds, RegionConstants.RegionTypeEnum.REGION_TYPE_1.getCode());
                if (!CollectionUtils.isEmpty(countryActivityList)) {
                    actIds.addAll(stateActivityList.stream().map(ActivityArea::getMainActivityId).distinct().toList());
                }
            }
            if (!CollectionUtils.isEmpty(actIds)) {
                wrapper.in("id", actIds);
            }
        }
        wrapper.in("main_type", Arrays.asList(MainActivityTypeEnum.SINGLE.getType(),
                MainActivityTypeEnum.SERIES_MAIN.getType(),
                MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType(),
                MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()));

        //sort
        wrapper.orderByAsc("activity_state");
        wrapper.orderByDesc("id");

        Page<MainActivity> page = new Page<>(query.getPageNum(), query.getPageSize());
        page = mainActivityService.findAllByWrapper(wrapper, page);

        List<ActivityDto> activityDtos = page.getRecords().stream().map(this::convertActivityDto).toList();

        //填充对应聚合活动是否创建状态是否有活动阶段
        for (ActivityDto activityDto : activityDtos) {
            PolymerizationActivityPole pole = polymerizationActivityPoleService.findByActivityId(activityDto.getActivityId());
            if (Objects.isNull(pole)) {
                activityDto.setIsCreatePolymerization(0);
                activityDto.setAutoStatus(0);
            } else {
                activityDto.setIsCreatePolymerization(pole.getIsCreateRole());
                activityDto.setAutoStatus(pole.getAutoStatus());
            }
            activityDto.setAwardSendType(activityParamsService.getAwardSendType(activityDto.getActivityId()));
            activityDto.setIsStageActivity(activityStageService.isStageACtivity(activityDto.getActivityId()) ? 1 : 0);
        }
//        //设置是否可以进行竞技赛,符合条件，且没有上架过
        activityDtos.stream()
                //活动没有上架过
                .filter(item -> ActivityStateEnum.CANCELED.getState().equals(item.getActivityState()))
                //没有自动创建
                .filter(item -> item.getAutoStatus() != 1)
                //没有聚合
                .filter(item -> item.getIsCreatePolymerization() != 1)
                .parallel().forEach(item -> {
                    item.setCanSettingCompetitive(competitiveSeasonRuleValid.checkActivityIsCompetitive(item.getActivityId()));
                });
        //无目标的玩法
        List<Long> playIds = activityDtos.stream().map(ActivityDto::getPlayId).distinct().toList();
        List<Gameplay> gameplay = gameplayService.findByIds(playIds);
        List<Long> noneTargetGameplayIds = gameplay.stream().filter(i -> ActivityConstants.TargetTypeEnum.TARGETTYPE_0.getCode().equals(i.getGameplayType())).map(Gameplay::getId).toList();
        //存在已经结结束的阶段
        List<Long> allActivity = activityDtos.stream().map(ActivityDto::getActivityId).toList();
        List<Long> haveFinishStageActivityIds = activityStageService.findIsFinishStageByMainActivityIds(allActivity).stream().map(ActivityStage::getActivityId).distinct().toList();
        activityDtos.stream()
                //不是无目标活动
                .filter(item -> !noneTargetGameplayIds.contains(item.getPlayId()))
                //活动已经结束
                .filter(item -> {
                    if (ActivityStateEnum.FINISHED.getState().equals(item.getActivityState())) {
                        return true;
                    } else if (ActivityStateEnum.IN_PROGRESS.getState().equals(item.getActivityState())) {
                        return haveFinishStageActivityIds.contains(item.getActivityId());
                    } else {
                        return false;
                    }
                })
                //设置显示设备监控
                .forEach(item -> {
                    item.setIsDeviceMonitor(1);
                });

        dtoPage.setTotal(page.getTotal());
        dtoPage.setCurrent(page.getCurrent());
        dtoPage.setSize(page.getSize());
        dtoPage.setRecords(activityDtos);
        return dtoPage;


    }

    private ActivityDto convertActivityDto(MainActivity mainActivity) {
        ActivityDto activityDto = new ActivityDto();
        BeanUtil.copyPropertiesIgnoreNull(mainActivity, activityDto);

        activityDto.setActivityId(mainActivity.getId());
        activityDto.setActivityNo(mainActivity.getActivityNo());
        //玩法回显 PlayGameTitleDataFiller
//        Gameplay gameplay = gameplayService.findById(mainActivity.getPlayId());
//        activityDto.setPlayName(gameplay.getTitle());
        activityDto.setPlayId(mainActivity.getPlayId());

        //比赛形式 交给PlayGameCompetitionFormatDataFiller
//        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
//        activityDto.setCompetitionFormat(entryGameplay.getCompetitionFormat());

        //活动名称回显 交给MainActivityDefaultTitleDataFiller 批量填充
        //类别回显
        activityDto.setCategoryType(mainActivity.getCategoryType());

        //状态回显
        activityDto.setActivityState(mainActivity.getActivityState());

        //时间回显 只处理0时区情况即可
        if (mainActivity.getTimeStyle() == 0) {
            String startTime = DateUtil.convertTimeStrByZone(mainActivity.getActivityStartTime(), "UTC", "GMT+08:00");
            activityDto.setActivityStartTime(startTime);
            String endTime = DateUtil.convertTimeStrByZone(mainActivity.getActivityEndTime(), "UTC", "GMT+08:00");
            activityDto.setActivityEndTime(endTime);
        }

        //地区回显 交给MainActivityAreaListSingleDataFiller 并行填充
//        List<String> showCountryList = activityAreaBizService.getActivityAreaName(mainActivity.getId());
//        activityDto.setAreas(showCountryList);
        //状态回显
        activityDto.setIsPublicDisplay(mainActivity.getIsPublicDisplay());
        activityDto.setIsCompetitive(mainActivity.getIsCompetitive());
        activityDto.setIsShowInMore(mainActivity.getIsShowInMore());
        activityDto.setProActivityType(mainActivity.getProActivityType());


        EntryGameplay byGameplayId = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (StringUtils.hasText(byGameplayId.getRankingBy())) {
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                List<MainActivity> allMainActivity = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
                if (allMainActivity.size() == 1) {
                    activityDto.setIsMeetOtherRecommendDevice(1);
                    MainActivity mainActivity1 = allMainActivity.get(0);
                    EntryGameplay byGameplayId1 = entryGameplayService.findByGameplayId(mainActivity1.getPlayId());
                    if (byGameplayId1.getRankingBy().equals("0")) {
                        activityDto.setIsMeetOtherRecommendDevice(0);
                    }
                }


            } else {
                activityDto.setIsMeetOtherRecommendDevice(1);
            }
        }

        return activityDto;
    }

    public Page<ActivitySimpleDto> activityNoEndList(ActivityConditionQuery pagePo) {
        //屏蔽掉阶段活动
        Page page = new Page<>(pagePo.getPageNum(), pagePo.getPageSize());
        Page activityPage = mainActivityService.findAllByWrapper(Wrappers.<MainActivity>lambdaQuery()
                .select(MainActivity::getId, MainActivity::getRemark)
                .in(MainActivity::getActivityState, Arrays.asList(0, 1))
                .eq(MainActivity::getStatus, 0)
                .eq(MainActivity::getIsDelete, 0)
                .eq(StringUtils.hasText(pagePo.getMainActivityType()), MainActivity::getMainType, pagePo.getMainActivityType())
                .in(!StringUtils.hasText(pagePo.getMainActivityType()), MainActivity::getMainType, Arrays.asList(MainActivityTypeEnum.SINGLE.getType(),
                        MainActivityTypeEnum.SERIES_MAIN.getType(),
                        MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType())), page);
        List<MainActivity> records = activityPage.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            List<ActivitySimpleDto> dtoList = records.stream().map(m -> {
                ActivitySimpleDto dto = new ActivitySimpleDto();
                dto.setActivityId(m.getId());
                //活动名称回显
                ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(m.getId(), null);
                //fix:npe
                String disseminateTitle = Objects.isNull(disseminate) ? "" : disseminate.getTitle();
                String activityTitle = disseminateTitle + " | " + m.getRemark();
                dto.setActivityTitle(activityTitle);
                return dto;
            }).collect(Collectors.toList());
            activityPage.setRecords(dtoList);
        }
        return activityPage;
    }

    public Boolean verifyActivityRemark(String remark, Long mainActivityId) {
        //如果没有改名
        if (mainActivityId != null) {
            MainActivity ac = mainActivityService.findById(mainActivityId);
            if (ac.getRemark().equals(remark)) {
                return false;
            }
        }
        MainActivity mainActivity = mainActivityService.findByRemark(remark);
        return mainActivity != null;


    }


    /**
     * 修改自动创建活动状态
     *
     * @param polymerizationActQuery
     * @return
     */
    public PolymerizationActResponse polymerizationAct(PolymerizationActQuery polymerizationActQuery) {
        return polymerizationActivityPoleService.findPolyResponseByActivityId(polymerizationActQuery.getMainActivityId());
    }

    /**
     * 查询赛事列表
     *
     * @param activityConditionQuery
     * @return
     */
    public Page<ActivityDto> findActivityByCondition(ActivityConditionQuery activityConditionQuery) {
        return findPageActivityByCondition(activityConditionQuery);
    }

    public SeriesActivityResultResponse getSeriesActivityResult(Long mainActivityId, Integer isAllUser) {

        MainActivity mainActivity = mainActivityService.findById(mainActivityId);

        SeriesActivityResultResponse resultResponse = new SeriesActivityResultResponse();

        //参与情况
        List<SingleActivityParticipantInfo> singleActivityParticipantInfos = getSingleActivityParticipantInfos(mainActivity, isAllUser);
        //费用&奖励 收支
        List<SeriesActivityIncomeAndExpenditure> seriesActivityIncomeAndExpenditures = getSeriesActivityParticipantInfos(mainActivity, isAllUser);
        ;

        resultResponse.setSingleActivityParticipantInfos(singleActivityParticipantInfos);
        resultResponse.setSeriesActivityIncomeAndExpenditures(seriesActivityIncomeAndExpenditures);

        return resultResponse;

    }

    private List<SeriesActivityIncomeAndExpenditure> getSeriesActivityParticipantInfos(MainActivity mainActivity, Integer isAllUser) {

        Long mainActivityId = mainActivity.getId();

        List<SeriesActivityIncomeAndExpenditure> seriesActivityIncomeAndExpenditureList = new ArrayList<>();
        List<Long> activityIds = seriesActivityRelService.findSubActivityId(mainActivityId);
        List<Long> allActivityIds = new ArrayList<>();
        allActivityIds.add(mainActivityId);
        allActivityIds.addAll(activityIds);

        List<ZnsUserAccountDetailEntity> detailEntityList = userAccountDetailService.getAccountDetailsByActivityIds(allActivityIds, Arrays.asList(AccountDetailTypeEnum.NEW_ACTIVITY_100.getType(), AccountDetailTypeEnum.SECURITY_FUND.getType(), AccountDetailTypeEnum.FEE.getType()));
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return seriesActivityIncomeAndExpenditureList;
        }
        //真实用户处理
        if (Objects.nonNull(isAllUser) && isAllUser == 1) {
            detailEntityList = detailEntityList.stream().filter(d -> d.getIsRobot() == 0 && d.getIsTest() == 0).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return seriesActivityIncomeAndExpenditureList;
        }
        List<Long> userIds = detailEntityList.stream().map(ZnsUserAccountDetailEntity::getUserId).collect(Collectors.toList());
        List<ZnsUserAccountEntity> znsUserAccountEntities = userAccountService.selectByUserIdList(userIds);
        Map<String, List<ZnsUserAccountEntity>> currencyCodeMap = znsUserAccountEntities.stream().collect(Collectors.groupingBy(ZnsUserAccountEntity::getCurrencyCode));
        for (Map.Entry<String, List<ZnsUserAccountEntity>> currencyCodeEntry : currencyCodeMap.entrySet()) {
            List<ZnsUserAccountEntity> accountEntityList = currencyCodeEntry.getValue();
            if (CollectionUtils.isEmpty(accountEntityList)) {
                continue;
            }
            SeriesActivityIncomeAndExpenditure seriesActivityIncomeAndExpenditure = new SeriesActivityIncomeAndExpenditure();
            seriesActivityIncomeAndExpenditure.setCurrency(I18nConstant.buildCurrency(currencyCodeEntry.getKey()));
            List<SeriesActivityIncomeAndExpenditureAmount> activityIncomeAndExpenditureAmounts = new ArrayList<>();

            List<Long> currencyUserIds = accountEntityList.stream().map(ZnsUserAccountEntity::getUserId).collect(Collectors.toList());
            //阶段奖励
            //当前只有一个阶段
            List<ZnsUserAccountDetailEntity> subActAccountDetailList = detailEntityList.stream().filter(a -> !a.getActivityId().equals(mainActivityId) && currencyUserIds.contains(a.getUserId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subActAccountDetailList)) {
                SeriesActivityIncomeAndExpenditureAmount amount = getSeriesActivityIncomeAndExpenditureAmount(subActAccountDetailList, 1);
                activityIncomeAndExpenditureAmounts.add(amount);
            }
            //整体奖励
            List<ZnsUserAccountDetailEntity> mainActAccountDetailList = detailEntityList.stream().filter(a -> a.getActivityId().equals(mainActivityId) && currencyUserIds.contains(a.getUserId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(mainActAccountDetailList)) {
                SeriesActivityIncomeAndExpenditureAmount amount = getSeriesActivityIncomeAndExpenditureAmount(mainActAccountDetailList, 0);
                activityIncomeAndExpenditureAmounts.add(amount);
            }
            seriesActivityIncomeAndExpenditure.setActivityIncomeAndExpenditureAmounts(activityIncomeAndExpenditureAmounts);
            seriesActivityIncomeAndExpenditureList.add(seriesActivityIncomeAndExpenditure);
        }

        return seriesActivityIncomeAndExpenditureList;

    }

    private SeriesActivityIncomeAndExpenditureAmount getSeriesActivityIncomeAndExpenditureAmount(List<ZnsUserAccountDetailEntity> accountDetailEntities, int level) {
        SeriesActivityIncomeAndExpenditureAmount amount = new SeriesActivityIncomeAndExpenditureAmount();
        amount.setLevel(level);

        BigDecimal totalFee = BigDecimal.ZERO;
        BigDecimal totalAward = BigDecimal.ZERO;
        BigDecimal totalCompleteAward = BigDecimal.ZERO;
        BigDecimal totalRankingAward = BigDecimal.ZERO;
        for (ZnsUserAccountDetailEntity userAccountDetailEntity : accountDetailEntities) {
            if (AccountDetailTypeEnum.FEE.getType().equals(userAccountDetailEntity.getTradeType()) || AccountDetailTypeEnum.SECURITY_FUND.getType().equals(userAccountDetailEntity.getTradeType())) {
                totalFee = totalFee.add(userAccountDetailEntity.getAmount());
            } else {
                totalAward = totalAward.add(userAccountDetailEntity.getAmount());
                if (AwardSentTypeEnum.COMPLETING_THE_GAME.getType().equals(userAccountDetailEntity.getTradeSubtype())) {
                    totalCompleteAward = totalCompleteAward.add(userAccountDetailEntity.getAmount());
                } else if (AwardSentTypeEnum.RANKING_BASED_REWARDS.getType().equals(userAccountDetailEntity.getTradeSubtype())) {
                    totalRankingAward = totalRankingAward.add(userAccountDetailEntity.getAmount());
                }
            }
        }
        amount.setRankAward(totalRankingAward);
        amount.setCompleteAward(totalCompleteAward);
        amount.setTotalAward(totalAward);
        amount.setReportAward(totalFee);
        return amount;
    }


    /**
     * 修改活动公开状态
     *
     * @param activityId
     * @param isPublicDisplay
     */
    public void enablePublicDisplay(Long activityId, Integer isPublicDisplay) {
        MainActivity activity = new MainActivity();
        activity.setId(activityId);
        activity.setIsPublicDisplay(isPublicDisplay);
        mainActivityService.update(activity);
    }

    public ActivityAwardReviewAdminDto reviewStatus(Long activityId) {
        return mainActivityBizService.reviewStatus(activityId);
    }

    /**
     * 查询活动
     *
     * @param query
     * @return
     */
    public Page<ActivityDto> findPageActivityByCondition(ActivityConditionQuery query) {
        Page<ActivityDto> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<ActivityDto> activityDtoPage = new Page<>();
        if (Objects.equals(query.getType(), 1)) {
            activityDtoPage = activityHighlightsService.findPageByCondition(page, query);
        } else if (Objects.equals(query.getType(), 2)) {
            activityDtoPage = mainActivityService.findActivityByCondition(page, query);
        } else {
            if (StringUtils.hasText(query.getMainActivityType()) && query.getMainActivityType().equals(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType())) {
                List<Long> allParentActivityId = polymerizationActivityBizService.findAllParentActivityId();
                if (org.apache.commons.collections.CollectionUtils.isEmpty(allParentActivityId)) {
                    return activityDtoPage;
                }
                query.setParentIds(allParentActivityId);
                activityDtoPage = mainActivityService.findActivityByCondition(page, query);
            } else {
                activityDtoPage = mainActivityService.findActivityByCondition(page, query);
            }
        }
        List<ActivityDto> records = activityDtoPage.getRecords();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(records)) {
            return page;
        }
        List<Long> activityIds = records.stream().map(ActivityDto::getActivityId).collect(Collectors.toList());
        Map<Long, List<ActivityDisseminate>> dissMap = activityDisseminateService.findByActIds(activityIds);
        List<ActivityPolymerizationRecord> polyRecords = activityPolymerizationRecordService.getBatchRecordByParentActIds(activityIds);
        List<Long> parentActivityIds = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(polyRecords)) {
            parentActivityIds.addAll(polyRecords.stream().map(ActivityPolymerizationRecord::getParentActivityId).distinct().toList());
        }
        records.forEach(s -> {
            Long activityId = s.getActivityId();
            List<ActivityDisseminate> disseminates = dissMap.get(activityId);
            if (!CollectionUtils.isEmpty(disseminates)) {
                ActivityDisseminate activityDisseminate = disseminates.stream().filter(a -> Objects.equals(a.getLanguageCode(), "en_US")).findFirst().orElse(null);
                //优先返回英语。不存在英语的情况下用另外随机的语言
                if (Objects.nonNull(activityDisseminate)) {
                    s.setActivityTitle(activityDisseminate.getTitle());
                    s.setCoverPic(activityDisseminate.getDisseminatePics());
                } else {
                    s.setActivityTitle(disseminates.get(0).getTitle());
                    s.setCoverPic(disseminates.get(0).getDisseminatePics());
                }
            }
            if (parentActivityIds.contains(activityId)) {
                //聚合活动
                s.setMainType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
            }
        });

        List<SubActivity> list = subActivityService.getAllSingleActByMainIds(activityIds);
        Map<Long, List<SubActivity>> subActivityMap = new HashMap<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            subActivityMap.putAll(list.stream().collect(Collectors.groupingBy(SubActivity::getMainActivityId)));
        }
//        List<ActivityParams> activityAndParamTypes = activityParamsService.findListByMainActivitysAndParamTypes(activityIds, ActivitySettingConfigEnum.AWARD_SEND_TYPE);
//        Map<Long,List<ActivityParams>> activityParamMap = new HashMap<>();
//        if (!org.springframework.util.CollectionUtils.isEmpty(activityAndParamTypes)){
//            activityParamMap.putAll(activityAndParamTypes.stream().collect(Collectors.groupingBy(ActivityParams::getMainActivityId)));
//        }
        for (ActivityDto record : records) {
            List<SubActivity> subActivityList = subActivityMap.get(record.getActivityId());
            if (!CollectionUtils.isEmpty(subActivityList)) {
                List<Integer> targetList = subActivityList.stream().map(SubActivity::getTarget).toList();
                record.setActivityTarget(targetList);
            }
            record.setAwardSendType(activityParamsService.getAwardSendType(record.getActivityId()));
        }
        activityDtoPage.setRecords(records);
        return activityDtoPage;
    }

    public void changeStatus(EnableActStatusRequest request) {
        mainActivityBizService.changeStatus(request);
    }

    public List<ActivityTitleDto> findActivityByTimeRange(ActivityTimeRangeQuery request) {
        if (CollectionUtils.isEmpty(request.getActivityStatus())) {
            request.setActivityStatus(List.of(
                    ActivityStateEnum.NOT_START.getState(),
                    ActivityStateEnum.IN_PROGRESS.getState(),
                    ActivityStateEnum.FINISHED.getState()
            ));
        }
        List<String> mainType = Arrays.asList(MainActivityTypeEnum.SINGLE.getType(),
                MainActivityTypeEnum.SERIES_MAIN.getType(),
                MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType(),
                MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType());
        request.setActivityMainType(mainType);
        return mainActivityService.findActivityByTimeRange(request);
    }
}
