package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 用户账户表
 *
 * <AUTHOR>
 * @date 2021-12-29 16:27:37
 */
@TableName("zns_user_account")
@Data
@NoArgsConstructor
public class ZnsUserAccountEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 最后修改时间
     */
    private ZonedDateTime modifieTime;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 当前金额
     */
    private BigDecimal amount;
    /**
     * 累计收入金额
     */
    private BigDecimal totalIncomeAmount;
    /**
     * 累计支出金额
     */
    private BigDecimal totalExpenditureAmount;
    /**
     * 累计奖金
     */
    private BigDecimal totalBonus;
    /**
     * 是否可用，1：可用，0：不可用
     */
    private Integer enabled;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 密码
     */
    @JsonIgnore
    private String password;
    /**
     * 盐值
     */
    @JsonIgnore
    private String salt;


    //是否是测试用户，1 是测试用户，0 不是测试用户
    private Integer isTest;
    //是否是机器人， 1 是机器人， 0 不是机器人
    private Integer isRobot;

    /**
     * 货币名称
     */
    private String currencyName;

    /**
     * 货币code
     *
     * @see I18nConstant.CurrencyCodeEnum
     */
    private String currencyCode;

    /**
     * 货币符号
     *
     * @see I18nConstant.CurrencyCodeEnum
     */
    private String currencySymbol;

    /**
     * 账户升级时间
     */
    private ZonedDateTime upgradeDate;
}

