package com.linzi.pitpat.data.awardservice.model.request;


import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class UserCouponSendBatchReq extends PageQuery {
    /**
     * 任务标题
     */
    private String title;
    /**
     * 开始时间
     */
    private ZonedDateTime gmtStartTime;
    /**
     * 结束时间
     */
    private ZonedDateTime gmtEndTime;
    /**
     * 券类型 【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券】
     */
    private Integer couponType;

    /**
     * 券名称
     */
    private Integer couponName;

    /**
     * 是否 卷包 1 是 类型展示卷包 0 否直接展示映射类型
     */
    private Integer isCouponPackage;

}
