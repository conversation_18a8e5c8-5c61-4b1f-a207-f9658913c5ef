package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 用户paypal账户表
 *
 * <AUTHOR>
 * @date 2022-02-15 10:12:44
 */
@TableName("zns_user_paypal_account")
@Data
@NoArgsConstructor
public class ZnsUserPaypalAccountEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 最后修改时间
     */
    private ZonedDateTime modifieTime;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * paypal账户
     */
    private String paypalAccount;
    /**
     * 手机号
     */
    private String phone;
    /**
     * paypal用户名
     */
    private String paypalUserName;
}
