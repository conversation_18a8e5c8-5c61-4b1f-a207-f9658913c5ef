package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*激历配置表
 *
 * <AUTHOR>
 * @since 2022-10-17
 */

@Data
@NoArgsConstructor
@TableName("zns_urge_config")
public class UrgeConfig implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //1-鼓掌，2-加油， 3-击掌 4-减少时间道具
    private Integer type;
    //剩余距离大于50米
    private String remainDistance;
    //剩余距离大于60秒
    private String remainTime;
    //已经跑步150米，单位为米
    private Integer runDistance;
    //已经跑步3分钟，单位为秒
    private Integer runTime;
    //鼓掌，加油，击掌 次数上限
    private String maxCount;
    //冷却时间最小值，2 分钟，单位为秒
    private String coolTime;
    //增加一格条件 , '3~8.9~60' 到  3-8.9速度60秒
    private String incOneGrid;
    //90%概率：鼓掌和被鼓掌两方，获得10-20经验值 ，随机精度1 （区间，精度可配置）， 10% 概率，鼓掌和被鼓掌两方，获得$0.1-0.4 间随机金钱奖励，随机精度0.1（区间可配置）
    private String awardValue;

    @Override
    public String toString() {
        return "UrgeConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",type=" + type +
                ",remainDistance=" + remainDistance +
                ",remainTime=" + remainTime +
                ",runDistance=" + runDistance +
                ",runTime=" + runTime +
                ",maxCount=" + maxCount +
                ",coolTime=" + coolTime +
                ",incOneGrid=" + incOneGrid +
                ",awardValue=" + awardValue +
                "}";
    }
}
