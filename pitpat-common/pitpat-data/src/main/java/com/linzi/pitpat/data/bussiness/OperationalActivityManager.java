package com.linzi.pitpat.data.bussiness;

import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.entity.operational.OperationalActivityUser;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.request.activity.OperationalActivityEnrollPo;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.service.operational.OperationalActivityUserService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/20 15:21
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OperationalActivityManager {
    @Resource
    private final RedissonClient redissonClient;
    private final ActivityStrategyContext activityStrategyContext;
    private final OperationalActivityService operationalActivityService;
    private final OperationalActivityUserService operationalActivityUserService;
    private final ZnsUserAccountService userAccountService;

    @Transactional
    public Result enrolling(ZnsUserEntity user, OperationalActivityEnrollPo request, Integer appVersion) {
        //加锁
        String key = RedisConstants.OPERATION_ACTIVITY_PARTICIPATION_KEY + user.getId();
        RLock lock = redissonClient.getLock(key);
        return LockHolder.tryLock(lock, 3, 30, () -> {
            boolean isNumber = NumberUtils.isNumber(request.getActivityNo());
            Long runActivityId = null;
            HandleActivityRequest handleActivityRequest = new HandleActivityRequest();
            handleActivityRequest.setFrontendAmount(request.getAmount());
            handleActivityRequest.setPassword(request.getPassword());
            handleActivityRequest.setUserCouponId(request.getUserCouponId());

            if (!isNumber) {
                OperationalActivity operationalActivity = operationalActivityService.selectByActivityNo(request.getActivityNo());
                if (Objects.isNull(operationalActivity)) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("activity.enroll.time.expired"));
                }

                //兼容历史
                if (operationalActivity.getOperationalActivityType() == 1) {
                    return compatibleHistoryOperationalActivity(user, request, operationalActivity);
                }
                runActivityId = operationalActivity.getRunActivityId();
                handleActivityRequest.setOperationalActivityId(operationalActivity.getId());
            } else {
                runActivityId = Long.valueOf(request.getActivityNo());
            }
            Result canJoin = activityStrategyContext.checkCanJoin(user, runActivityId, appVersion);
            if (Objects.nonNull(canJoin)) {
                return canJoin;
            }
            Result result = activityStrategyContext.handleUserActivityState(runActivityId, 1, user, request.getPassword(), null, false,
                    null, handleActivityRequest, false);
            return result;
        });

    }

    /**
     * 历史活动兼容处理
     *
     * @param user
     * @param request
     * @param operationalActivity
     */
    private Result compatibleHistoryOperationalActivity(ZnsUserEntity user, OperationalActivityEnrollPo request, OperationalActivity operationalActivity) {
        if (Objects.nonNull(operationalActivity.getApplicationStartTime()) && Objects.nonNull(operationalActivity.getApplicationEndTime())) {
            ZonedDateTime now = ZonedDateTime.now();
            if (now.compareTo(operationalActivity.getApplicationStartTime()) < 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.notStart"));
            }
            if (now.compareTo(operationalActivity.getApplicationEndTime()) > 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.ended"));
            }
        }
        if (operationalActivity.getStatus() == -1) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.time.ended"));
        }
        OperationalActivityUser operationalActivityUser = operationalActivityUserService.selectByUserIdAndOperationalId(operationalActivity.getId(), user.getId());
        if (operationalActivity.getIsAll() == 0) {
            if (Objects.isNull(operationalActivityUser)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.apply.condition.notMatch"));
            }
        }

        if (Objects.nonNull(operationalActivityUser) && operationalActivityUser.getUserState() == 1) {
            return CommonResult.success();
        }
        // 支付保证金逻辑
        Result payResult = handlePayOperationalActivity(operationalActivity, user, request, BigDecimal.ZERO, false);
        if (null != payResult) {
            return payResult;
        }
        if (Objects.nonNull(operationalActivityUser)) {
            operationalActivityUserService.enrolling(operationalActivityUser.getId(), user);
        } else {
            operationalActivityUserService.enrolling(user, operationalActivity);
        }
        return CommonResult.success();
    }


    private Result handlePayOperationalActivity(OperationalActivity operationalActivity, ZnsUserEntity user, OperationalActivityEnrollPo operationalActivityEnrollPo,
                                                BigDecimal couponAmount, boolean brandRightsInterestsUser) {
        if (Objects.isNull(operationalActivity.getEntryFee()) || operationalActivity.getEntryFee().compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        if (operationalActivity.getEntryRuleType() == 1) {
            return null;
        }
        String password = operationalActivityEnrollPo.getPassword();
        if (user.getIsRobot() != 1) {
            Result passwordResult = userAccountService.checkPassword(user.getId(), password, false);
            if (null != passwordResult) {
                passwordResult.setData(null);
                return passwordResult;
            }
        }
        // 开始支付
        RunActivityPayRequest request = new RunActivityPayRequest();
        //当前品牌权益免费
        if (brandRightsInterestsUser) {
            couponAmount = operationalActivity.getEntryFee();
            request.setPrivilegeBrand(operationalActivityEnrollPo.getPrivilegeBrand());
            request.setBrandRightsInterests(operationalActivityEnrollPo.getBrandRightsInterests());
        }

        request.setUserId(user.getId());
        request.setPayType(0);
        request.setPayPassword(password);
        request.setUserCouponId(operationalActivityEnrollPo.getUserCouponId());
        BigDecimal entryFee = operationalActivity.getEntryFee();
        BigDecimal discountedAmount = entryFee.subtract(couponAmount);
        if (discountedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            discountedAmount = BigDecimal.ZERO;
        }
        request.setAmount(discountedAmount);
        log.info("报名费用:{},折扣金额:{},实际支付金额:{}", entryFee, couponAmount, discountedAmount);

        request.setActivityId(operationalActivity.getRunActivityId());
        request.setOperationalActivityId(operationalActivity.getId());
        request.setAccountDetailTypeEnum(AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_ENTRY_FEE);

        Result payResult = userAccountService.payByBalance(request);
        if (null != payResult && !CommonError.SUCCESS.getCode().equals(payResult.getCode())) {
            // 支付失败
            return payResult;
        }

        return null;
    }
}
