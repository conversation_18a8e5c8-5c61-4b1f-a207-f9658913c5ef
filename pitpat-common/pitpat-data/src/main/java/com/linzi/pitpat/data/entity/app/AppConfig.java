package com.linzi.pitpat.data.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-13
 */

@Data
@NoArgsConstructor
@TableName("zns_app_config")
public class AppConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.app.AppConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                           // 主键
    public final static String status_ = CLASS_NAME + "status";                   // 是否启用（0-否，1-是）
    public final static String app_version = CLASS_NAME + "app_version";          // app下发版本号
    public final static String config_content = CLASS_NAME + "config_content";    // 配置内容
    public final static String config_desc = CLASS_NAME + "config_desc";          // 配置描述
    public final static String is_delete = CLASS_NAME + "is_delete";              // 是否删除
    public final static String gmt_create = CLASS_NAME + "gmt_create";            // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";        // 修改时间
    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否启用（0-否，1-是）
    private Integer status;
    //app下发版本号
    private String appVersion;
    //配置内容
    private String configContent;
    //配置描述
    private String configDesc;
    //是否删除
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;

    @Override
    public String toString() {
        return "AppConfig{" +
                ",id=" + id +
                ",status=" + status +
                ",appVersion=" + appVersion +
                ",configContent=" + configContent +
                ",configDesc=" + configDesc +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                "}";
    }
}
