package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.dto.UserPbTargetCountDto;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataEntity;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeUserResp;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityMonthReportDataVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityRunExportVo;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.model.vo.TeamRunRunningReportListVO;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.entity.dto.AppUserDto;
import com.linzi.pitpat.data.entity.vo.UserRunDataVo;
import com.linzi.pitpat.data.vo.UserRunDetailsExportVo;
import com.linzi.pitpat.data.vo.home.UserDataInfoSimpleVo;
import com.linzi.pitpat.data.vo.runData.DataOverviewVo;
import com.linzi.pitpat.data.vo.runData.UserRunDaysVo;
import com.linzi.pitpat.data.vo.useractive.PowerAchievementVo;
import com.linzi.pitpat.data.vo.useractive.RunDataYearVo;
import com.linzi.pitpat.data.vo.useractive.UserRunDataAchievementVo;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.lz.mybatis.plugin.annotations.AS;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.Count;
import com.lz.mybatis.plugin.annotations.DateFormat;
import com.lz.mybatis.plugin.annotations.GE;
import com.lz.mybatis.plugin.annotations.GT;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.IN;
import com.lz.mybatis.plugin.annotations.Item;
import com.lz.mybatis.plugin.annotations.LE;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.LeftJoinOns;
import com.lz.mybatis.plugin.annotations.Mapping;
import com.lz.mybatis.plugin.annotations.Max;
import com.lz.mybatis.plugin.annotations.Min;
import com.lz.mybatis.plugin.annotations.NE;
import com.lz.mybatis.plugin.annotations.NotIn;
import com.lz.mybatis.plugin.annotations.Order;
import com.lz.mybatis.plugin.annotations.OrderBy;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import com.lz.mybatis.plugin.annotations.OrderType;
import com.lz.mybatis.plugin.annotations.Sum;
import com.lz.mybatis.plugin.annotations.Where;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户跑步详情表
 *
 * <AUTHOR>
 * @date 2021-10-09 10:00:14
 */
@Mapper
public interface ZnsUserRunDataDetailsDao extends BaseMapper<ZnsUserRunDataDetailsEntity> {


    List<TeamRunRunningReportListVO> getTeamRunRunningReport(@Param("detailId") Long detailId, @Param("targetRunMileage") Integer targetRunMileage, @Param("targetRunTime") Integer targetRunTime,
                                                             @Param("activityState") Integer activityState);

    List<ChallengeRunRunningReportListVO> getChallengeRunRunningReport(@Param("detailId") Long detailId, @Param("userId") Long userId, @Param("activityId") Long activityId);

    ZnsUserRunDataDetailsEntity getSingleDaySumMax(@Param("column") String column, @Param("userId") Long userId);

    Integer getRunDays(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("runType") Integer runType);

    int getNoEnd(@Param("activityId") Long activityId);

    int getChallengeRunning(@Param("detailId") Long detailId);

    ZnsUserRunDataDetailsEntity getRunData1(@Param("userId") Long userId);


    /**
     * 统计用户一段时间跑步数据
     */
    UserRunDataVo userRunDataStatistics(@Param("startTime") ZonedDateTime startTime, @Param("userId") Long userId, @Param("runType") Integer runType, @Param("deviceType") Integer deviceType);


    ChallengeRunRunningReportListVO getMyRaceRunRunningReport(Long detailId);

    ChallengeRunRunningReportListVO getOtherRaceRunRunningReport(Long detailId);


    Integer selectByUserRunDataDetail(@Param("userId") Long userId, @Param("createTime") ZonedDateTime createTime, @Param("runMileage") Integer runMileage, @Param("averageVelocity") BigDecimal averageVelocity);


    ZnsUserRunDataDetailsEntity selectByUserIdUserId10_6_65(@Param("userId") Long userId, @Param("runMileage") Integer runMileage,
                                                            @Param("averageVelocityMin") BigDecimal averageVelocityMin,
                                                            @Param("averageVelocityMax") BigDecimal averageVelocityMax);

    @Sum("run_mileage")
    Integer selectByUserIdUserRunMileage(Long userId);


    BigDecimal selectByUserIdUseRaverageVelocity(@Param("userId") Long userId);


    @OrderByIdDescLimit_1
    ZnsUserRunDataDetailsEntity selectByUserIdActivityId(Long userId, Long activityId);


    @OrderByIdDescLimit_1
    ZnsUserRunDataDetailsEntity selectByActivityIdUserId(Long activityId, Long userId);

    Integer getPaceRanking(Long userId, Integer deviceType);


    @Sum("run_mileage")
    BigDecimal selectSumRunMileageByUserId(Long userId,
                                           @Column("create_time") @DateFormat @GE ZonedDateTime startOfDate,
                                           @Column("create_time") @DateFormat @LE ZonedDateTime endOfDate);

    BigDecimal selectAvgVUserId(@Param("userId") Long userId, @Param("startOfDate") ZonedDateTime startOfDate, @Param("endOfDate") ZonedDateTime endOfDate);


    @Count
    Integer countRunDataDetails(@IN List<Long> routeId, @IN List<Integer> runStatus);

    List<ZnsUserRunDataEntity> nearly30DayRunCount(Page page);

    List<UserRunDaysVo> getRunDaysByUserIds(@Param("userIds") List<Integer> userIds);

    Map<String, Object> runCount(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<UserRunDetailsExportVo> getUserRunData(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    List<ActivityRunExportVo> getActivityRunData(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);


    @AS("rdd")
    @Mapping(" * ")
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, as = "rae", on = " rdd.activity_id = rae.id "),
    })
    @Where(" rdd.is_delete = 0 and   ")
    @Order({
            @By(value = {"rdd.average_pace"}, type = OrderType.ASC)
    })
    @LIMIT
    ZnsUserRunDataDetailsEntity selectByUserIdAverageVelocityActivityType(@Column("rdd.user_id") Long userId, @Column("rae.activity_type") Integer activityType);

    List<ZnsUserRunDataDetailsEntity> selectByUserIdUserRunTimeRunMileage15(Long userId, @GT Integer runTime, @GT BigDecimal runMileage, @GT ZonedDateTime createTime);

    Map<String, Object> getUserDataBroadcastingDay7(@Param("startDate") ZonedDateTime startDate, @Param("endDate") ZonedDateTime endDate, @Param("userId") Long userId);

    BigDecimal getMilepost(@Param("userId") Long userId, @Param("activityStartTime") ZonedDateTime activityStartTime);

    List<AppUserDto> getAppRunCount(@Param("userIds") List<Long> userIds);

    List<AppUserDto> getLastEntryTime(@Param("userIds") List<Long> userIds);


    List<AppUserDto> getLastRunTime(@Param("userIds") List<Long> userIds);


    @Sum(ZnsUserRunDataDetailsEntity.run_mileage)
    BigDecimal selectRunDistance(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                 @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                 @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                 @Column(ZnsUserRunDataDetailsEntity.activity_type) @IF @NotIn List<Integer> activityType);


    @Sum(ZnsUserRunDataDetailsEntity.run_time_millisecond)
    BigDecimal selectRunTime(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                             @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                             Long userId);


    @Max(ZnsUserRunDataDetailsEntity.average_velocity)
    BigDecimal selectMaxVelocity(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                 @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                 Long userId);


    @Count(ZnsUserRunDataDetailsEntity.id_)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    Integer selectRunCount(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                           @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                           @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                           @Column(ZnsRunActivityEntity.activity_type) @IF @NotIn List<Integer> activityType);


    @Sum(ZnsUserRunDataDetailsEntity.run_mileage)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    BigDecimal selectGuanfangMulRunDistance(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                            @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                            @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                            @Column(ZnsRunActivityEntity.activity_type) Integer activityType);


    @Sum(ZnsUserRunDataDetailsEntity.run_time_millisecond)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    BigDecimal selectGuanfangMulRunRunTime(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                           @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                           @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                           @Column(ZnsRunActivityEntity.activity_type) Integer activityType);


    @Mapping(ZnsUserRunDataDetailsEntity.run_mileage)
    @LIMIT
    BigDecimal selectMaxVelocityDistance(BigDecimal averageVelocity, Long userId);


    @Sum(ZnsUserRunDataDetailsEntity.run_mileage)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    BigDecimal selectRunDistanceNotInActivityType(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                                  @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                                  @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                                  @Column(ZnsRunActivityEntity.activity_type) @IF @NotIn List<Integer> activityType);


    @Sum(ZnsUserRunDataDetailsEntity.run_mileage)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    BigDecimal selectRunDistanceNotInActivityTypeDataSource(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                                            @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                                            @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                                            @Column(ZnsRunActivityEntity.activity_type) @IF @NotIn List<Integer> activityType,
                                                            @Column(ZnsUserRunDataDetailsEntity.data_source) @IF @IN List<Integer> dataSource);


    @Sum(ZnsUserRunDataDetailsEntity.run_time_millisecond)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    BigDecimal selectRunTimeNotInActivityTypeDataSource(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                                        @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                                        @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                                        @Column(ZnsRunActivityEntity.activity_type) @IF @NotIn List<Integer> activityType,
                                                        @Column(ZnsUserRunDataDetailsEntity.data_source) @IF @IN List<Integer> dataSource);


    @Sum(ZnsUserRunDataDetailsEntity.run_time_millisecond)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    BigDecimal selectRunTimeNotInActivityType(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                              @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                              @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                              @Column(ZnsRunActivityEntity.activity_type) @IF @NotIn List<Integer> activityType);

    @Mapping(ZnsUserRunDataDetailsEntity.id_)
    @LeftJoinOns({
            @Item(value = ZnsCourseEntity.class, left = ZnsUserRunDataDetailsEntity.course_id, right = ZnsCourseEntity.id_),
    })
    List<ZnsUserRunDataDetailsEntity> selectNoEndCourseList(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime startTime,
                                                            @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime endTime,
                                                            @Column(ZnsCourseEntity.course_type) Integer courseType, @NE Integer runStatus);


    @Mapping(" ifnull(count(DISTINCT(DATE_FORMAT(create_time, '%Y-%m-%d'))), 0) ")
    Integer selectRunDayNotInActivityTypeDataSource(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                                    @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                                    @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                                    @Column(ZnsUserRunDataDetailsEntity.activity_type) @IF @NotIn List<Integer> activityType,
                                                    @Column(ZnsUserRunDataDetailsEntity.data_source) @IN @IF List<Integer> dataSource);


    @Mapping(ZnsUserRunDataDetailsEntity.all)
    @LeftJoinOns({
            @Item(value = ZnsRunActivityEntity.class, left = ZnsUserRunDataDetailsEntity.activity_id, right = ZnsRunActivityEntity.id_),
    })
    List<ZnsUserRunDataDetailsEntity> selectRunDistanceList(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                                            @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                                            @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                                            @Column(ZnsRunActivityEntity.activity_type) @IF @NotIn List<Integer> activityType);


    @Mapping(" ifnull(count(DISTINCT(un_activity_type)), 0) ")
    Integer selectRunDistanceNotInSubActivityType(@Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime monthStart,
                                                  @Column(ZnsUserRunDataDetailsEntity.create_time) @LE ZonedDateTime monthEnd,
                                                  @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId,
                                                  @Column(ZnsUserRunDataDetailsEntity.un_activity_type) @IF @NotIn List<Integer> unActivityType);


    Page<Long> selectUserIds(IPage page,
                             @Param("gmtStart") ZonedDateTime gmtStart,
                             @Param("gmtEnd") ZonedDateTime gmtEnd,
                             @Param("isRobot") Integer isRobot);


    @Mapping(" ifnull(sum(run_mileage),0) mileage," +
            "ifnull(sum(kilocalorie),0) calories," +
            "count(*) times,ifnull(sum(run_time),0) motionTime," +
            "ifnull(sum(step_num),0) step_num," +
            "ifnull(avg(average_velocity),0) averVelocity," +
            "sum(fat_consumption) fat_consumption")
    DataOverviewVo getDataStatisticsByRunType(Long userId, @IF Integer runType,
                                              @Column(ZnsUserRunDataDetailsEntity.create_time) @IF @GE ZonedDateTime startTime,
                                              @Column(ZnsUserRunDataDetailsEntity.create_time) @IF @LE ZonedDateTime endTime, Integer runStatus);

    ZnsUserRunDataEntity getUserRunStatisticsData(@Param("userId") Long userId, @Param("runTypes") List<Integer> runTypes);


    List<ZnsUserRunDataDetailsEntity> selectListByActivityIdUserId(@Column(ZnsUserRunDataDetailsEntity.activity_id) Long activityId, @Column(ZnsUserRunDataDetailsEntity.user_id) Long userId);


    @Min(ZnsUserRunDataDetailsEntity.average_pace)
    Integer selectMinRunTimeByUserIdDistanceTargetRunMileageMinRunTime(
            Long userId,
            Integer distanceTarget,
            @GE Integer runMileage,
            @GE ZonedDateTime createTime,
            Integer runStatus,
            @IF @GE Long activityId, Integer deviceType);

    @OrderByIdDescLimit_1
    ZnsUserRunDataDetailsEntity selectMinRunTimeByUserIdDistanceTargetRunMileage(Long userId,
                                                                                 Integer distanceTarget,
                                                                                 @GE Integer runMileage,
                                                                                 @GE ZonedDateTime createTime,
                                                                                 Integer averagePace,
                                                                                 @IF @GE Long activityId, Integer deviceType);

    @Mapping({
            ZnsUserRunDataDetailsEntity.user_id,
            ZnsUserRunDataDetailsEntity.run_time,
            ZnsUserRunDataDetailsEntity.average_pace,
            "id as runDataDetailsId"
    })
    @LIMIT(200)
    @OrderBy(value = {ZnsUserRunDataDetailsEntity.average_pace}, type = {OrderType.DESC})
    @DataCache(value = {"userId"})
    List<ChallengeUserResp> selectMinRunTimeByUserIdDistanceTargetRunMileageRuntime(@Column(ZnsUserRunDataDetailsEntity.user_id) @NE Long userId,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.distance_target) Integer distanceTarget,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.run_mileage) @GE Integer runMileage,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.run_status) @GE Integer runStatus,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.average_pace) @GE Integer minRunTime,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.average_pace) @LE Integer maxRunTime,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime createTime,
                                                                                    @Column(ZnsUserRunDataDetailsEntity.activity_id) @IF @GE Long activityId);

    @Mapping("count(1)")
    Integer selectCountByUserIdAndRouteId(Long userId, Long routeId);

    @Mapping("AVG(average_pace)")
    BigDecimal selectAveragePace(Long userId, @Column(ZnsUserRunDataDetailsEntity.create_time) @GE ZonedDateTime startTime);

    @Sum(ZnsUserRunDataDetailsEntity.run_time_millisecond)
    Integer selectSumUserTaskTimeByIds(@IN List<Long> id);

    List<Map<String, Object>> getAllUserMilageBytime(ZonedDateTime date);

    ActivityMonthReportDataVo selectMonthReportActivityData(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    UserDataInfoSimpleVo findUserData(@Param("userId") Long userId, @Param("start") ZonedDateTime start, @Param("end") ZonedDateTime end);

    RunDataYearVo selectMostMileDay(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    RunDataYearVo selectMostTimeMonth(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    RunDataYearVo selectMostEndDay(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);

    Integer countCourseTime(@Param("userId") Long userId, @Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime);


    List<Long> selectBestGradeExpiration(Integer distanceTarget, Integer runMileage, ZonedDateTime earlyTime, ZonedDateTime lateTime, Integer runStatus);

    List<ZnsUserRunDataDetailsEntity> findListByRealCondition();

    /**
     * 查询用户跑步成就数据汇总
     *
     * @param userId
     * @param divisor    米转公英制除数
     * @param createTime 用户注册时间
     * @return
     */
    UserRunDataAchievementVo selectUserRunDataVo(@Param("userId") Long userId, @Param("divisor") Integer divisor, @Param("createTime") ZonedDateTime createTime);

    /**
     * 获取用户指定里程的最佳配速
     *
     * @param userId
     * @param minDetailId 最小运动id
     * @param runMileage  里程
     * @param deviceType
     */
    BigDecimal getUserBestPaceByRunMileage(Long userId, Long minDetailId, Integer runMileage, Integer deviceType);

    /**
     * 查询最佳耐力成就数据
     *
     * @param userId
     * @param minDetailId 最小运动id
     * @param deviceType
     */
    PowerAchievementVo getUserPowerAchievementVo(Long userId, Long minDetailId, Integer deviceType);

    /**
     * 周跑量达到10mile/16km的次数
     *
     * @param userId
     * @param minDetailId
     * @param deviceType
     * @return
     */
    Integer getTenMileNum(Long userId, Long minDetailId, Integer deviceType);

    /**
     * 获取最佳配速
     *
     * @param userId
     * @return
     */
    Integer getBestMileTime(Long userId);

    List<UserRunDaysVo> getRunDaysByRealUserIds(@Param("userIds") List<Integer> userIds);


    ZnsUserRunDataDetailsEntity findByIdActually(Long id);

    /**
     * 查询一起运动的记录，时间范围存在冗余，所以可能有无效数据。
     *
     * @param activityId 活动id
     * @param createTime 开始时间
     * @param endTime    结束时间
     * @param runTime    运动时长
     * @return
     */
    List<ZnsUserRunDataDetailsEntity> findPlayUserData(Long activityId, ZonedDateTime createTime, ZonedDateTime endTime, int runTime);

    List<ZnsUserRunDataEntity> findUserAllMileage(@Param("userIds") List<Long> userIds);

    Long findWaitFeedbackCount(@Param("activityIdList") List<Long> activityIdList, @Param("userId") Long userId);

    ZnsUserRunDataDetailsEntity getRunDataDetailsPbLast(@Param("userId") Long userId, @Param("deviceTypes") List<Integer> deviceTypes, @Param("target") List<Integer> list);

    List<UserPbTargetCountDto> countUserPBMaxTarget(@Param("userId") Long userId, @Param("deviceTypes") List<Integer> deviceTypes, @Param("targetMileages") List<Integer> targetMileages);

    ZnsUserRunDataDetailsEntity findBestTargetRunTime(@Param("userId") Long userId, @Param("target") Integer targetRunTime);

    ZnsUserRunDataDetailsEntity findBestTargetRunMilleage(@Param("userId") Long userId, @Param("target") Integer targetRunMileage);

    List<String> findProductCodeList(@Param("userId") Long userId, @Param("activityIdList") List<Long> activityIdList);
}
