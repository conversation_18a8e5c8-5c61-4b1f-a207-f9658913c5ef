package com.linzi.pitpat.data.activityservice.biz;

import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.IpUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.listener.event.RunStartEvent;
import com.linzi.pitpat.data.activityservice.model.dto.UpdateUserRecordDto;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityDataEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserDataEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsMinuteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserDataQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.UserRunOptimalRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityDataService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserDataService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.courseservice.model.entity.UserCourseRecord;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.model.request.CourseDataDetailRequest;
import com.linzi.pitpat.data.courseservice.service.UserAiCourseService;
import com.linzi.pitpat.data.courseservice.service.UserCourseRecordService;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.RunDataRunTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsDeviceErrorRecordService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.mongo.Id.SnowflakeId;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventSourceEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserCourseEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserCourseService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.runData.DataProcessingVo;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 数据上传/结束/开始处理类
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunDataBizService {
    private final ZnsTreadmillService treadmillService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ZnsDeviceErrorRecordService znsDeviceErrorRecordService;
    private final ZnsRunActivityService runActivityService;
    private final RabbitTemplate rabbitTemplate;
    private final ZnsUserCourseService userCourseService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final QueueMessageService queueMessageService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final ISysConfigService sysConfigService;
    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    private final ZnsCourseService courseService;
    private final ZnsUserService userService;
    private final UserAiCourseService userAiCourseService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserCourseRecordService userCourseRecordService;
    private final AppMessageService appMessageService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserRunDataService userRunDataService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final MainActivityService mainActivityService;
    private final RunRankedActivityUserService runRankedActivityUserService;
    private final MongoTemplate mongoTemplate;
    private final RedissonClient redissonClient;
    private final ZnsRunActivityUserDataService runActivityUserDataService;
    private final ZnsRunActivityDataService runActivityDataService;
    private final UserRunOptimalRecordService userRunOptimalRecordService;
    private final EntryGameplayService entryGameplayService;
    private final UserRunOptimalBizService userRunOptimalBizService;
    @Value("${" + RabbitQueueConstants.RUN_DATA_QUEUE + "}")
    private String runDataQueue;
    @Value("${zns.config.rabbitQueue.saveDetailMinute}")
    private String saveDetailMinute;

    /**
     * 数据上传
     *
     * @param runData
     * @return
     */
    public Result dataProcessing(RunDataRequest runData) {
        if (Objects.isNull(runData.getDeviceType())) {
            // 兼容跑步机
            runData.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        DataProcessingVo vo = new DataProcessingVo();
        //参数处理
        Result result = checkParam(runData);
        if (Objects.nonNull(result)) {
            return result;
        }

        Long userId = runData.getId_no();
        try {
            ActivityTypeDto activityTypeDto = runActivityService.getActivityNew(runData.getActivityId());
            if (Objects.nonNull(activityTypeDto)) {
                runData.setMainType(activityTypeDto.getMainType());
            }

            // 获取或保存zns_user_run_data_details数据
            ZnsUserRunDataDetailsEntity userRunDataDetail = getRunDataDetail(runData, userId, activityTypeDto);
            if (Objects.isNull(userRunDataDetail)) {
                log.error("数据上传服务器异常，userRunDataDetail为空，activityId={},id_no={}", runData.getActivityId(), runData.getId_no());
                return CommonResult.success(vo);
            }

            setReturnData(userRunDataDetail, vo);
            runData.setRunDataDetailsId(userRunDataDetail.getId());

            rabbitTemplate.convertAndSend(runDataQueue, JsonUtil.writeString(runData));
        } catch (Exception e) {
            log.error("数据上传服务器异常", e);
            return CommonResult.success(vo);
        }


        return CommonResult.success(vo);
    }

    private Result checkParam(RunDataRequest runData) {// 兼容运动编号为null问题
        if (Objects.isNull(runData.getOrder_no()) || runData.getOrder_no() < 0) {
            log.warn("order_no 异常，忽略该数据， activity={}, user_id={}. order_no={}", runData.getActivityId(), runData.getId_no(), runData.getOrder_no());
            return CommonResult.success();
        }
        log.info("数据上传服务器activityId={},id_no={}", runData.getActivityId(), runData.getId_no());
        if (Objects.nonNull(runData.getActivityId()) && (runData.getActivityId() == 1 || runData.getActivityId() == 0)) {
            runData.setActivityId(null);
        }
        if (StringUtils.hasText(runData.getUnique_code())) {
            runData.setUnique_code(runData.getUnique_code().replaceAll("\\u0000", ""));
        }
        return null;
    }

    /**
     * 跑步结束
     *
     * @param entity
     * @return
     */
    public ZnsUserRunDataDetailsEntity endSport(ZnsUserRunDataDetailsEntity entity) {
        entity.setIsDelete(0);
        entity.setModifieTime(ZonedDateTime.now());
        entity.setRunStatus(1);
        //不满足一分钟的数据不展示
        if (entity.getRunTime() < 60) {
            entity.setIsDelete(1);
        }
        Integer paceMileage = EquipmentDeviceTypeEnum.ROWING.getCode().equals(entity.getDeviceType()) ? 500 : 1000;
        try {
            if (Objects.isNull(entity.getRunTimeMillisecond()) || entity.getRunTimeMillisecond() == 0) {
                entity.setRunTimeMillisecondLargeThenOneSecond(entity.getRunTime() * 1000 + 999);
            }
            //查询活动
            ZnsRunActivityEntity activityEntity = runActivityService.findById(entity.getActivityId());
            BigDecimal runMileage = Objects.nonNull(activityEntity) && Arrays.asList(1, 4).contains(activityEntity.getActivityType()) ? entity.getDistanceTarget() : entity.getRunMileage();
            if (runMileage.compareTo(BigDecimal.ZERO) <= 0 || entity.getRunMileage().compareTo(entity.getDistanceTarget()) < 0) {
                runMileage = entity.getRunMileage();
            }
            BigDecimal runTime = BigDecimalUtil.divHalfUp(new BigDecimal(entity.getRunTimeMillisecond()), new BigDecimal(1000), 2);
            entity.setAveragePace(SportsDataUnit.getPace(runTime, BigDecimalUtil.divHalfUp(runMileage, new BigDecimal(paceMileage), 4)));
            entity.setAverageVelocity(SportsDataUnit.getVelocity(runTime, runMileage));

            //计算平均值
            List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(entity.getId());

            ZnsUserEntity userEntity = userService.findById(entity.getUserId());
            if (Objects.isNull(userEntity)) {
                userEntity = new ZnsUserEntity();
                userEntity.setWeight(80d);
            }
            Double averFallingGradient = 0.0;
            if (!CollectionUtils.isEmpty(detailsSecondEntities)) {
                Double averHeartRate = detailsSecondEntities.stream().filter(d -> Objects.nonNull(d.getHeartRate())).mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().orElse(0D);
                Double averStepFrequency = detailsSecondEntities.stream().filter(d -> Objects.nonNull(d.getStepFrequency())).mapToInt(ZnsUserRunDataDetailsSecondEntity::getStepFrequency).average().orElse(0D);
                averFallingGradient = detailsSecondEntities.stream().filter(d -> Objects.nonNull(d.getGradient())).mapToInt(ZnsUserRunDataDetailsSecondEntity::getGradient).average().orElse(0D);
                entity.setAverageHeartRate(averHeartRate.intValue());
                entity.setAverageStepFrequency(averStepFrequency.intValue());
                //运动详情设置心率区间
                setDetailHeartRateTimeMap1(entity, detailsSecondEntities, userEntity);
                //爬坡相关计算
//                List<ZnsUserRunDataDetailsSecondEntity> climbingList = detailsSecondEntities.stream().filter(s -> Objects.nonNull(s.getGradient()) && s.getGradient() > 0).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(climbingList)) {
//                    averFallingGradient = climbingList.stream().filter(d -> Objects.nonNull(d.getGradient())).mapToInt(ZnsUserRunDataDetailsSecondEntity::getGradient).average().orElse(0D);
//                    BigDecimal airlineDistance = SportsDataUnit.getComputeMileage(climbingList.size(), entity.getAverageVelocity());
//                    entity.setClimbingMileage(SportsDataUnit.getClimbingMileage(averFallingGradient, airlineDistance));
//                }
            }
            //计算步幅
            Integer averageStride = SportsDataUnit.getStride(entity.getStepNum(), entity.getRunMileage());
            entity.setAverageStride(averageStride);
            //计算能力值
            entity.setCapabilityValue(SportsDataUnit.getCapabilityValue(entity.getRunMileage(), entity.getRunTime()));
            if (Objects.isNull(entity.getDistanceTarget())) {
                entity.setDistanceTarget(BigDecimal.ZERO);
            }
            //预计卡路里计算
            Integer calorieTarget = SportsDataUnit.getCalorieTarget(entity.getDistanceTarget(), entity.getTimeTarget(), entity.getAveragePace(), userEntity.getWeight());
            entity.setCalorieTarget(calorieTarget);
            entity.setFatConsumption(SportsDataUnit.getFatConsumption(entity.getKilocalorie()));
        } catch (Exception e) {
            log.error("endSport error,e", e);
        }

        userRunDataDetailsService.update(entity);
        UpdateUserRecordDto recordDto = UpdateUserRecordDto.builder()
                .deviceType(entity.getDeviceType()).runDataDetailsId(entity.getId())
                .distanceTarget(entity.getDistanceTarget()).averagePace(entity.getAveragePace())
                .runMileage(entity.getRunMileage()).userId(entity.getUserId())
                .isTest(entity.getIsTest()).isRobot(entity.getIsRobot())
                .isCheat(entity.getIsCheat()).runTime(entity.getRunTime())
                .build();
        userRunOptimalBizService.updateUserRecord(recordDto);
        realPersonRunDataDetailsService.updateRealPersonDetails(entity);
        return entity;
    }

    /**
     * 保存明细数据
     *
     * @param userRunDataDetail
     * @param runDataRequest
     * @param activityTypeDto
     * @param detailsSecondEntities
     * @return
     */
    public ZnsUserRunDataDetailsEntity saveUserRunDataDetail(ZnsUserRunDataDetailsEntity userRunDataDetail, RunDataRequest runDataRequest, ActivityTypeDto activityTypeDto, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities) {
        log.info("saveUserRunDataDetail start,detailsId:{}", userRunDataDetail.getId());
        Integer run_status = runDataRequest.getRun_status();
        Integer firmwareVersion = runDataRequest.getFirmwareVersion();
        Integer dataSource = runDataRequest.getDataSource();

        userRunDataDetail.setModifieTime(ZonedDateTime.now());
        userRunDataDetail.setLastTime(ZonedDateTime.now());
        // 如果runStatus状态的值为非1，【运动状态，默认0,0：运动中，1：结束，2：暂停】，则可以修改run_status状态
        if (userRunDataDetail.getRunStatus() != 1) {
            userRunDataDetail.setRunStatus(run_status);
        }
        userRunDataDetail.setIsDelete(0);
        userRunDataDetail.setFirmwareVersion(firmwareVersion);
        // 不满足一分钟的数据不展示，如果还没有跑一分钟，如果用户没有跑完一分钟就结束了，则需要将zns_user_run_data_details 表中的数据删除掉
        if (userRunDataDetail.getRunStatus() == 1 && userRunDataDetail.getRunTime() < 60) {
            userRunDataDetail.setIsDelete(1);
        }
        //只要有app上传数据就算app,半硬件跑不算
        if (dataSource != 1 && userRunDataDetail.getDataSource() == 1) {
            userRunDataDetail.setDataSource(dataSource);
        }
        ZnsUserEntity userEntity = userService.findById(userRunDataDetail.getUserId());

        //结束运动再计算平均值，跑步数据上传结束
        if (userRunDataDetail.getRunStatus() == 1) {
            // 设置运行的总毫秒值
            if (Objects.isNull(userRunDataDetail.getRunTimeMillisecond()) || userRunDataDetail.getRunTimeMillisecond() == 0) {
                userRunDataDetail.setRunTimeMillisecondLargeThenOneSecond(userRunDataDetail.getRunTime() * 1000 + 999);
            }
            // 计算跑步时间，单位为秒
            BigDecimal runTime = BigDecimalUtil.divHalfUp(new BigDecimal(userRunDataDetail.getRunTimeMillisecond()), new BigDecimal(1000), 2);
            // 获取目标跑步距离
            BigDecimal runMileage = Objects.nonNull(activityTypeDto) && Arrays.asList(1, 4).contains(activityTypeDto.getActivityType()) ? userRunDataDetail.getDistanceTarget() : userRunDataDetail.getRunMileage();
            // 获取用户跑步距离，
            // a 假如用户目标距离是 1600 米， 假如用户跑了 1500 米，则用 1500 来计算
            // b 假如用户目标距离是 1600 米，用户跑了1602米，则用1600 来计算
            if (runMileage.compareTo(BigDecimal.ZERO) <= 0 || userRunDataDetail.getRunMileage().compareTo(userRunDataDetail.getDistanceTarget()) < 0) {
                runMileage = userRunDataDetail.getRunMileage();
            }
            // 计算 平均配速(秒/公里)
            userRunDataDetail.setAveragePace(SportsDataUnit.getPace(runTime, BigDecimalUtil.divHalfUp(runMileage, new BigDecimal(1000), 4)));
            // 平均速度(公里/小时)
            userRunDataDetail.setAverageVelocity(SportsDataUnit.getVelocity(runTime, runMileage));
            //速度异常处理
            if (Objects.equals(userEntity.getIsRobot(), 1) && userRunDataDetail.getAverageVelocity().compareTo(Constants.MAX_ROBOT_AVERAGE_VELOCITY) > 0) {
                userRunDataDetail.setIsDelete(1);
            }
            // 计算平均值, 拿出用户的每秒跑步数据
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(detailsSecondEntities)) {
                detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(userRunDataDetail.getId());
            }
            Double averFallingGradient = 0.0;

            if (!CollectionUtils.isEmpty(detailsSecondEntities)) {
                Double averHeartRate = detailsSecondEntities.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().orElse(0D);
                // 平均心率(次/分钟)
                userRunDataDetail.setAverageHeartRate(averHeartRate.intValue());
                //运动详情设置心率区间
                userRunDataDetailsService.setDetailHeartRateTimeMap(userRunDataDetail, detailsSecondEntities, userEntity);
//                //爬坡相关计算 , 过滤出所有 坡度 大于 0 的 每秒数据
//                List<ZnsUserRunDataDetailsSecondEntity> climbingList = detailsSecondEntities.stream().filter(s -> Objects.nonNull(s.getGradient()) && s.getGradient() > 0).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(climbingList)) {
//                    // 获取平均坡度
//                    averFallingGradient = climbingList.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getGradient).average().orElse(0D);
//                    // 计算总跑步米数
//                    BigDecimal airlineDistance = SportsDataUnit.getComputeMileage(climbingList.size(), userRunDataDetail.getAverageVelocity());
//                    //爬坡距离（m） = 总跑步米数 *  平均坡度
//                    userRunDataDetail.setClimbingMileage(SportsDataUnit.getClimbingMileage(averFallingGradient, airlineDistance));
//                }
                //获取心率类型
                userRunDataDetail.setHeartRateDeviceType(detailsSecondEntities.get(0).getHeartRateDeviceType());
            }
            //计算步频，每分钟走多少步
            int stepFrequency = 0;
            if (Objects.nonNull(userRunDataDetail.getRunTime()) && userRunDataDetail.getRunTime() > 0) {
                stepFrequency = userRunDataDetail.getStepNum() * 60 / userRunDataDetail.getRunTime();
            }
            userRunDataDetail.setAverageStepFrequency(stepFrequency);
            // 平均步幅(cm)  =  距离(米) * 100  / 步数
            Integer averageStride = SportsDataUnit.getStride(userRunDataDetail.getStepNum(), userRunDataDetail.getRunMileage());
            userRunDataDetail.setAverageStride(averageStride);
            //计算能力值 =  0.2*速度(m/min)+0.9*速度(m/min)*坡度(%)-3.5
            userRunDataDetail.setCapabilityValue(SportsDataUnit.getCapabilityValue(userRunDataDetail.getRunMileage(), userRunDataDetail.getRunTime()));
            //预计卡路里计算 = (体重【默认为80】 * 米 / 1000) * 1.036
            Integer calorieTarget = SportsDataUnit.getCalorieTarget(userRunDataDetail.getDistanceTarget(), userRunDataDetail.getTimeTarget(), userRunDataDetail.getAveragePace(), userEntity.getWeight());
            userRunDataDetail.setCalorieTarget(calorieTarget);
            // 脂肪消耗 = Kilocalorie * 1000 / 7700
            userRunDataDetail.setFatConsumption(SportsDataUnit.getFatConsumption(userRunDataDetail.getKilocalorie()));
            // 走步文案保存 , runType 【1. 跑步， 2 走步】
            if (Integer.valueOf(2).equals(userRunDataDetail.getRunType())) {
                Integer mileageRank = userRunDataService.getMileageRank(userRunDataDetail.getUserId(), runMileage, userRunDataDetail.getRunType());
                // 设置当前的走步排名或跑步排名
                userRunDataDetail.setRunMileageRanking(mileageRank);
                if (Objects.nonNull(mileageRank)) {
                    // 查询总用户数
                    long count = userRunDataService.count(userRunDataDetail.getDeviceType());
                    BigDecimal divide = new BigDecimal(mileageRank).divide(new BigDecimal(count), 2, BigDecimal.ROUND_DOWN);
                    BigDecimal mileageExceedPercentage = divide.multiply(new BigDecimal(100));
                    // 里程超过百分比
                    userRunDataDetail.setMileageExceedPercentage(mileageExceedPercentage);
                }
            }
        }
        userRunDataDetail.setIsCountMilestone(null);
        // 毫秒处理，防止为0
        if (userRunDataDetail.getRunTimeMillisecond() == 0) {
            userRunDataDetail.setRunTimeMillisecondLargeThenOneSecond(userRunDataDetail.getRunTime() * 1000 + 999);
        }
        //强制更新,包括删除状态
        userRunDataDetailsService.updateByIdForce(userRunDataDetail);
        if (userRunDataDetail.getIsDelete() == 1) {
            if (Objects.nonNull(activityTypeDto) && Objects.equals(activityTypeDto.getMainType(), MainActivityTypeEnum.RANK.getType())) {
                //段位赛的活动也需要同步删除
                runRankedActivityUserService.deleteRunRankedActivityUser(userRunDataDetail.getId());
            }
        }
        return userRunDataDetail;
    }


    /**
     * 保存初始运动数据
     *
     * @param treadmillId
     * @param userId
     * @param order_no
     * @param routeId
     * @param activityId
     * @param courseId
     * @param runDataRequest
     * @param dataSource
     * @param zoneId
     * @param ipAddr
     * @param appVersion
     * @param firmwareVersion
     * @param runType
     * @param trainingId
     * @return
     */
    public ZnsUserRunDataDetailsEntity saveNewRunDataDetail(Long treadmillId, Long userId, Integer order_no, Long routeId, Long activityId, Long courseId,
                                                            RunDataRequest runDataRequest, Integer dataSource, String zoneId, String ipAddr, Integer appVersion,
                                                            Integer firmwareVersion, Integer runType, Integer heartRateDeviceType, Long trainingId) {
        ZnsUserRunDataDetailsEntity entity = new ZnsUserRunDataDetailsEntity();
        ZnsUserEntity user = userService.findById(userId);
        entity.setTrainingId(trainingId);
        entity.setCreateTime(ZonedDateTime.now());
        entity.setTreadmillId(treadmillId);
        // 加 IsRobot , IsTest , ActivityType 主要是为了统计数据时用
        if (Objects.nonNull(user)) {
            entity.setIsRobot(user.getIsRobot());
            entity.setIsTest(user.getIsTest());
        }
        entity.setDeviceType(runDataRequest.getDeviceType());
        entity.setActivityType(getActivityType(activityId));
        entity.setUserId(userId);               //用户id
        entity.setMaxHeartRate(0);              //最大心率(次/分钟)
        entity.setMaxStepFrequency(0);          //最大速度(公里/小时)
        entity.setMaxPace(0);                   //最大配速(秒/公里)
        entity.setStepNum(0);                   //步数(步)
        entity.setUniqueCode(order_no.toString());
        if (Objects.nonNull(runDataRequest.getRun_status()) && runDataRequest.getRun_status() == 3) {
            entity.setRunStatus(3); // 运动状态，默认0,0：运动中，1：结束，2：暂停
        }
        String config = sysConfigService.selectConfigByKey("share.data.config");
        Map<String, Object> object = JsonUtil.readValue(config);
        List<String> copyWritingList = JsonUtil.readList(object.get("copyWritingList"), String.class);
        entity.setReportCopywriting(ListUtils.random(copyWritingList));
        entity.setRouteId(routeId);
        entity.setActivityId(activityId);
        entity.setCourseId(courseId);
        if (Objects.nonNull(courseId) && courseId > 0) {
            ZnsCourseEntity course = courseService.selectById(courseId);
            if (Objects.nonNull(course)) {
                entity.setTimeTarget(course.getCourseDuration());
            }
            entity.setUnActivityType(-2);
        } else if ((NumberUtils.geZero(runDataRequest.getDistanceTarget()) || NumberUtils.geZero(runDataRequest.getTimeTarget())) && Objects.isNull(entity.getActivityType())) {
            entity.setUnActivityType(-3);       // 非活动类型，-1：自由跑，-2：课程跑，-3：目标跑
        } else if (NumberUtils.leZero(runDataRequest.getDistanceTarget()) && NumberUtils.leZero(runDataRequest.getTimeTarget()) && Objects.isNull(entity.getActivityType())) {
            entity.setUnActivityType(-1);
        }

        //目标
        if (Objects.nonNull(runDataRequest.getDistanceTarget())) {
            entity.setDistanceTarget(new BigDecimal(runDataRequest.getDistanceTarget()));
        }
        if (Objects.nonNull(runDataRequest.getTimeTarget())) {
            entity.setTimeTarget(runDataRequest.getTimeTarget());
        }

        if (Objects.nonNull(runDataRequest.getTaskId())) {
            entity.setTaskId(runDataRequest.getTaskId());           // 一周快乐跑 ，或者其他的新人活动动需要添加taskId
            //任务需要添加目标
            RunActivityUserTask task = runActivityUserTaskService.findById(runDataRequest.getTaskId());
            if (Objects.nonNull(task) && task.getMileageTarget() > 0) {
                entity.setDistanceTarget(new BigDecimal(task.getMileageTarget()));
            }
        }
        entity.setDataSource(dataSource);                           // 数据来源，0：app，1：跑步机 2：半硬件跑
        if (StringUtils.hasText(zoneId)) {
            entity.setZoneId(zoneId);                               //时区
        }
        if (StringUtils.hasText(ipAddr)) {
            entity.setIpAddr(ipAddr);                               //ip地址
        }
        if (Objects.nonNull(firmwareVersion)) {
            entity.setFirmwareVersion(firmwareVersion);             //固件版本号
        }
        entity.setAppVersion(appVersion);                   //app版本号
        entity.setRunType(runType);                         //1. 跑步， 2 走步
        entity.setHeartRateDeviceType(heartRateDeviceType);
        userRunDataDetailsService.save(entity);
        return entity;
    }


    /**
     * 用户跑步数据总表数据处理
     *
     * @param userId
     * @param user
     */
    public void userRunDataHandle(Long userId, ZnsUserEntity user) {
        if (Objects.isNull(userId) || Objects.isNull(user)) {
            return;
        }
        //跑步数据
        ZnsUserRunDataEntity userRunData = userRunDataDetailsService.getUserRunData(userId, Lists.newArrayList(RunDataRunTypeEnum.RUN.getCode(), RunDataRunTypeEnum.RUN_10.getCode(), RunDataRunTypeEnum.WALK.getCode()));

        //查询用户数据总表
        ZnsUserRunDataEntity runData = userRunDataService.findUserRunDataByUserId(userId, EquipmentDeviceTypeEnum.TREADMILL.getCode());
        BigDecimal oldTotalRunMileage = BigDecimal.ZERO;
        if (Objects.nonNull(runData)) {
            oldTotalRunMileage = runData.getRunMileage();

            setRunData(userRunData, runData);
        } else {
            runData = new ZnsUserRunDataEntity();
            runData.setUserId(userId);
            runData.setEmailAddress(user.getEmailAddressEn());
            runData.setNickname(user.getFirstName());
            if (userRunData != null && userRunData.getRunMileage() != null) {
                runData.setRunMileage(userRunData.getRunMileage());
            } else {
                runData.setRunMileage(BigDecimal.ZERO);
            }
            runData.setRunCount(1);
        }

        //走步数据
//        ZnsUserRunDataEntity userWalkData = userRunDataDetailsService.getUserRunData(userId, Lists.newArrayList(RunDataRunTypeEnum.WALK.getCode()));
//        if (Objects.isNull(userWalkData)) {
//            userWalkData = new ZnsUserRunDataEntity(0, 0, 0);
//        }
//        if (Objects.isNull(userWalkData.getRunMileage())) {
//            runData.setWalkMileage(0);
//        } else {
//            runData.setWalkMileage(userWalkData.getRunMileage().intValue());
//        }
//        if (Objects.isNull(userWalkData.getRunTime())) {
//            runData.setWalkTime(0);
//        } else {
//            runData.setWalkTime(userWalkData.getRunTime());
//        }
//        if (Objects.isNull(userWalkData.getMaxMileageSingleDay())) {
//            runData.setMaxWalkMileageSingleDay(0);
//        } else {
//            runData.setMaxWalkMileageSingleDay(userWalkData.getMaxMileageSingleDay().intValue());
//        }

        runData.setModifieTime(ZonedDateTime.now());

        Integer runTime = Objects.isNull(runData.getRunTime()) ? 0 : runData.getRunTime();
        BigDecimal totalRunMileage = Objects.isNull(runData.getRunMileage()) ? BigDecimal.ZERO : runData.getRunMileage();
        Integer totalStepNum = Objects.isNull(runData.getStepNum()) ? 0 : runData.getStepNum();
        Integer totalHeartBeat = Objects.isNull(runData.getTotalHeartBeat()) ? 0 : runData.getTotalHeartBeat();

        runData.setAveragePace(SportsDataUnit.getPace(runTime, BigDecimalUtil.divHalfUp(totalRunMileage, new BigDecimal(1000), 2)));
        runData.setAverageVelocity(SportsDataUnit.getVelocity(runTime, totalRunMileage));
        runData.setAverageStepFrequency(SportsDataUnit.getStepFrequency(totalStepNum, runTime));
        runData.setAverageStride(SportsDataUnit.getStride(totalStepNum, totalRunMileage));
        runData.setAverageHeartRate(runTime == 0 ? 0 : totalHeartBeat / runTime);
        if (Objects.isNull(runData.getId())) {
            userRunDataService.save(runData);
        } else {
            userRunDataService.update(runData);
        }
        //通知处理
//        if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(100 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(100 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 100);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(50 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(50 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 50);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(30 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(30 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 30);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(20 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(20 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 20);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(15 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(15 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 15);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(10 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(10 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 10);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(8 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(8 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 8);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(5 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(5 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 5);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(3 * 1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(3 * 1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 3);
//        } else if (oldTotalRunMileage.compareTo(BigDecimal.valueOf(1600)) < 0 && totalRunMileage.compareTo(BigDecimal.valueOf(1600)) >= 0) {
//            runMileageArriveGoalPush(userId, 1);
//        }
    }

    /**
     * 课程结束
     *
     * @param request
     * @param user
     */
    public void endCourseRun(CourseDataDetailRequest request, ZnsUserEntity user) {
        ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.findByIdActually(request.getDetailId());
        if (Objects.isNull(details)) {
            return;
        }
        if (Objects.isNull(details.getCourseId()) || details.getCourseId() <= 0) {
            return;
        }
        details.setRunStatus(1);
        if (request.getRunTime() < 60) {
            details.setIsDelete(1);
        }
        details.setRunTime(request.getRunTime());
        details.setRunTimeMillisecondLargeThenOneSecond(request.getRunTime() * 1000 + 999);
        ZnsCourseEntity course = courseService.selectById(details.getCourseId());
        BigDecimal kilocalorie = new BigDecimal(course.getEstimatedHeatConsumption() * request.getRunTime()).divide(new BigDecimal(course.getActualTrainingDuration()), 0, BigDecimal.ROUND_DOWN);
        details.setKilocalorie(kilocalorie);
        //课程处理
        userAiCourseService.completeCourse(user.getId(), details.getCourseId());

        activityUserScoreService.sendScore(user.getId(), 4, null, null, details.getCourseId());
        userRunDataDetailsService.update(details);
        UpdateUserRecordDto recordDto = UpdateUserRecordDto.builder()
                .deviceType(details.getDeviceType()).runDataDetailsId(details.getId())
                .distanceTarget(details.getDistanceTarget()).averagePace(details.getAveragePace())
                .runMileage(details.getRunMileage()).runTime(details.getRunTime())
                .userId(details.getUserId()).isTest(details.getIsTest())
                .isRobot(details.getIsRobot()).isCheat(details.getIsCheat()).build();
        userRunOptimalBizService.updateUserRecord(recordDto);
        realPersonRunDataDetailsService.updateRealPersonDetails(details);
        UserCourseRecord userCourseRecord = userCourseRecordService.selectCourseRecordByUserIdAndCourseId(details.getUserId(), course.getId(), details.getId());
        if (Objects.nonNull(userCourseRecord)) {
            userCourseRecord.setRunStatus(YesNoStatus.YES.getCode());
            userCourseRecordService.updateById(userCourseRecord);
        }
        //发布tb跑步结束增加运动里程时长事件
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(),new TurbolinkApplicationEvent(TurbolinkEventEnum.RUN, user.getId(),
                Map.of("time", details.getRunTime()), TurbolinkEventSourceEnum.COURSEEND));
    }

    private void setReturnData(ZnsUserRunDataDetailsEntity userRunDataDetail, DataProcessingVo vo) {
        if (Objects.isNull(userRunDataDetail)) {
            throw new RuntimeException("数据上传服务器失败,数据保存失败");
        }
        vo.setDetailId(userRunDataDetail.getId());

        //课程参与人数返回
        if (Objects.nonNull(userRunDataDetail.getCourseId()) && userRunDataDetail.getCourseId() > 0) {
            //数据走缓存
            ZnsUserCourseEntity userCourse = userCourseService.getUserCourse(userRunDataDetail.getUserId(), userRunDataDetail.getCourseId());
            // 如果是课程跑，则返回实际参与次数
            if (Objects.isNull(userCourse)) {
                vo.setUserPartakeNumber(1);
            } else {
                vo.setUserPartakeNumber(userCourse.getActualNumber());
            }
        }
    }

    /**
     * 保存数据详情
     *
     * @param runDataDetailId
     * @param list
     * @param userId
     * @param runStatus
     * @param activityTypeDto
     */
    public void saveUserRunDataDetailMinute(Long runDataDetailId, List<ZnsUserRunDataDetailsSecondEntity> list, Long userId, Integer runStatus, ActivityTypeDto activityTypeDto) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(list)) {
            return;
        }
        ZnsUserRunDataDetailsMinuteEntity entity = new ZnsUserRunDataDetailsMinuteEntity();
        entity.setIsDelete(0);
        entity.setCreateTime(ZonedDateTime.now());
        entity.setRunDataDetailsId(runDataDetailId);
        entity.setRunMileage(list.get(list.size() - 1).getMileage());
        entity.setRunTime(list.get(list.size() - 1).getRunTime());
        //计算平均值
        Double averHeartRate = list.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().orElse(0D);
        Double averPace = list.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getPace).average().orElse(0D);
        Double averStepFrequency = list.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getStepFrequency).average().orElse(0D);
        entity.setAverageHeartRate(averHeartRate.intValue());
        entity.setAveragePace(averPace.intValue());
        entity.setAverageStepFrequency(averStepFrequency.intValue());
        entity.setAverageVelocity(SportsDataUnit.paceToVelocity(entity.getAveragePace()));
        entity.setDataDetails(list);
        entity.setRunStatus(runStatus);
        entity.setId(SnowflakeId.getId());
        //保存到mongodb
        try {
            if (!isRobot(userId)) {
                userRunDataDetailsSecondService.saveSecondList(list, runDataDetailId, runStatus);
            } else {
                entity.setIsRobot(1);
                if (Objects.nonNull(activityTypeDto)) {
                    entity.setActivityType(activityTypeDto.getActivityType());
                }
                // 将每分钟的数据放到消息队列中
                rabbitTemplate.convertAndSend(saveDetailMinute, JsonUtil.writeString(entity));
            }
        } catch (Exception e) {
            log.error("saveUserRunDataDetailMinute error:{}", e);
        }
    }

    /**
     * 数据补录
     *
     * @param userRunDataDetail
     */
    public void dataCompensationSave(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        List<ZnsUserRunDataDetailsSecondEntity> data = new ArrayList<>();
        userRunDataDetailsSecondService.dataCompensation(data, 1, userRunDataDetail, false);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        try {
            ZnsUserRunDataDetailsSecondEntity znsUserRunDataDetailsSecondEntity = data.get(0);
            //保存每次上传的数据
            saveUserRunDataDetailMinute(userRunDataDetail.getId(), data, userRunDataDetail.getUserId(), 1, null);
            if (znsUserRunDataDetailsSecondEntity.getMileage().compareTo(userRunDataDetail.getRunMileage()) > 0) {
                userRunDataDetail.setRunMileage(znsUserRunDataDetailsSecondEntity.getMileage());
            }
            if (znsUserRunDataDetailsSecondEntity.getRunTime() > userRunDataDetail.getRunTime()) {
                userRunDataDetail.setRunTime(znsUserRunDataDetailsSecondEntity.getRunTime());
            }
            if (znsUserRunDataDetailsSecondEntity.getRunTimeMillisecond() > userRunDataDetail.getRunTimeMillisecond()) {
                userRunDataDetail.setRunTimeMillisecondLargeThenOneSecond(znsUserRunDataDetailsSecondEntity.getRunTimeMillisecond());
            }
        } catch (Exception e) {
            log.error("dataCompensationSave 异常，e:{}", e);
        }
    }

    /**
     * 活动结束后更新用户参与跑步活动相关数据
     */
    public void updateRunActivityUserData(ZnsRunActivityEntity runActivityEntity) {
        // 判断活动是否存在并且结束
        if (null == runActivityEntity || !ActivityStateEnum.FINISHED.getState().equals(runActivityEntity.getActivityState())) {
            log.error("活动不存在或者活动未结束");
            return;
        }
        // 查询所有的活动用户
        List<ZnsRunActivityUserEntity> activityUserList = runActivityUserService.findAllActivityUser(runActivityEntity.getId());
        // 遍历每个用户
        for (ZnsRunActivityUserEntity activityUser : activityUserList) {
            if (activityUser.getUserState() == 0 || activityUser.getUserState() == 2) {
                continue;
            }
            //上锁，否则会出现新增两条ZnsRunActivityUserDataEntity数据，同时各项数据也会不准确
            String lockKey = RedisConstants.USER_RUN_DATA_PROCESS + activityUser.getUserId();
            RLock lock = redissonClient.getLock(lockKey);
            try {
                if (lock.tryLock(5L, TimeUnit.SECONDS)) {
                    // 查询用户活动数据是否存在
                    ZnsRunActivityUserDataEntity userDataEntity = runActivityUserDataService.findOne(
                            RunActivityUserDataQuery.builder()
                                    .isDelete(0).userId(activityUser.getUserId())
                                    .build());
                    if (null == userDataEntity) {
                        // 新增
                        userDataEntity = new ZnsRunActivityUserDataEntity();
                        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(runActivityEntity.getActivityType()) || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(runActivityEntity.getActivityType())) {
                            // 组队跑
                            userDataEntity.setOrganizeTeamNum(1);                   // 组队跑次数
                        } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(runActivityEntity.getActivityType())) {
                            // 挑战跑
                            userDataEntity.setChallengeRunNum(1);               //挑战跑次数
                            if (1 == activityUser.getRank()) {
                                // 胜利
                                userDataEntity.setWinsNum(1);
                                userDataEntity.setWinningRatio(100);
                            } else if (0 == activityUser.getRank()) {
                                // 平局
                                userDataEntity.setWinningRatio(50);
                            } else {
                                // 失败
                                userDataEntity.setFailNum(1);
                            }
                        }
                        userDataEntity.setUserId(activityUser.getUserId());
                        runActivityUserDataService.save(userDataEntity);
                    } else {
                        // 更新
                        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(runActivityEntity.getActivityType()) || RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(runActivityEntity.getActivityType())) {
                            // 组队跑
                            Integer teamRunNum = userDataEntity.getOrganizeTeamNum() + 1;
                            userDataEntity.setOrganizeTeamNum(teamRunNum);
                        } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(runActivityEntity.getActivityType())) {
                            // 挑战跑
                            Integer challengeRunNum = userDataEntity.getChallengeRunNum() + 1;
                            userDataEntity.setChallengeRunNum(challengeRunNum);
                            if (1 == activityUser.getRank()) {
                                // 胜利
                                Integer winsNum = userDataEntity.getWinsNum() + 1;
                                userDataEntity.setWinsNum(winsNum);
                            } else if (-1 != activityUser.getRank() && 0 != activityUser.getRank()) {
                                // 失败
                                Integer failNum = userDataEntity.getFailNum() + 1;
                                userDataEntity.setFailNum(failNum);
                            }
                            userDataEntity.setWinningRatio(BigDecimalUtil.divHalfUp(new BigDecimal(userDataEntity.getWinsNum()), new BigDecimal(userDataEntity.getChallengeRunNum()), 2).multiply(new BigDecimal(100)).intValue());
                        }
                        runActivityUserDataService.updateById(userDataEntity);
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }


        }
    }

    /**
     * 修改活动数据表，主要用于统计用
     *
     * @param activityEntity
     */
    public void updateRunActivityData(ZnsRunActivityEntity activityEntity) {
        //查询用户状态为已接受及之后的用户
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder().activityId(activityEntity.getId()).isDelete(0).userStateIn(Arrays.asList(1, 3, 4)).build();

        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> ids = new ArrayList<>();
        Long winner = null;
        for (ZnsRunActivityUserEntity znsRunActivityUserEntity : list) {
            if (znsRunActivityUserEntity.getRank() == 1) {
                winner = znsRunActivityUserEntity.getUserId();
            }
            ids.add(znsRunActivityUserEntity.getUserId().toString());
        }

        //查询是否已存在
        ZnsRunActivityDataEntity dataEntity = runActivityDataService.findOneByActivityId(activityEntity.getId());
        if (Objects.nonNull(dataEntity)) {
            dataEntity.setAllUserId(String.join(",", ids));
            dataEntity.setWinner(winner);
            runActivityDataService.update(dataEntity);
            return;
        }

        ZnsRunActivityDataEntity activityDataEntity = new ZnsRunActivityDataEntity();
        activityDataEntity.setActivityId(activityEntity.getId());
        activityDataEntity.setActivityType(activityEntity.getActivityType());
        activityDataEntity.setAllUserId(String.join(",", ids));
        activityDataEntity.setWinner(winner);
        runActivityDataService.save(activityDataEntity);
    }

    private boolean isRobot(Long userId) {
        if (Objects.isNull(userId) || userId == 0) {
            return true;
        }
        ZnsUserEntity znsUserEntity = userService.findById(userId);
        if (Objects.isNull(znsUserEntity)) {
            return true;
        }
        if (znsUserEntity.getIsRobot() == 1) {
            return true;
        }
        return false;
    }


    private ZnsUserRunDataDetailsEntity getRunDataDetail(RunDataRequest runDataRequest, Long userId, ActivityTypeDto activityTypeDto) {
        //查询用户
        ZnsTreadmillEntity treadmillEntity = treadmillService.getTreadmillByUniqueCode(runDataRequest.getUnique_code());
        znsDeviceErrorRecordService.addErrorRecord(treadmillEntity, runDataRequest.getErrorCode());

        Long treadmillId = 0L;
        if (Objects.nonNull(treadmillEntity)) {
            treadmillId = treadmillEntity.getId();
        }
        //当前时间往前30分钟内，重复的order_no都算相同的运动
        ZonedDateTime date = ZonedDateTime.now().minusMinutes(30);

        // 大家需要注意，这个UniqueCode 非 ZnsTreadmillEntity 表的unique_code 字段，标识这个活动的唯一标识

        ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.findByTreadmillIdAndUniqueCode(date, userId, treadmillId, runDataRequest.getOrder_no().toString());
        //半硬件跑后app跑,即使orderno相同也为两条数据
        if (Objects.nonNull(detailsEntity)) {
            if (detailsEntity.getDataSource() == 2 && runDataRequest.getDataSource() == 0) {
            } else {
                return detailsEntity;
            }
        }

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String zoneId = "";
        String ipAddr = "127.0.0.1";
        Integer appVersion = 0;
        if (servletRequestAttributes != null) {
            HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
            // 获取时区
            zoneId = request.getHeader("zoneId");
            // 获取远程IP
            ipAddr = IpUtil.getRemoteIp(request);
            // 获取app 版本号
            String appVersionStr = request.getHeader("appVersion");
            try {
                if (StringUtils.hasText(appVersionStr)) {
                    appVersion = Integer.valueOf(appVersionStr);
                }
            } catch (Exception e) {
                log.error("getRunDataDetail error,e", e);
            }
        }
        // 如果传过来 run_status 为1 ，则不需要创建zns_user_run_data_details 表了
        if (runDataRequest.getRun_status() == 1 && CollectionUtils.isEmpty(runDataRequest.getData())) {
            return null;
        }

        //心率设备
        Integer heartRateDeviceType = null;
        if (!CollectionUtils.isEmpty(runDataRequest.getData())) {
            heartRateDeviceType = runDataRequest.getData().stream().filter(item -> Objects.nonNull(item.getHeartRateDeviceType())).map(ZnsUserRunDataDetailsSecondEntity::getHeartRateDeviceType).findFirst().orElse(null);
        }

        //保存记录
        ZnsUserRunDataDetailsEntity userRunDataDetail = saveNewRunDataDetail(treadmillId,
                userId, runDataRequest.getOrder_no(), runDataRequest.getRouteId(), runDataRequest.getActivityId(),
                runDataRequest.getCourseId(), runDataRequest, runDataRequest.getDataSource(), zoneId, ipAddr, appVersion,
                runDataRequest.getFirmwareVersion(), runDataRequest.getRunType(), heartRateDeviceType, runDataRequest.getTrainingId());

        applicationEventPublisher.publishEvent(new RunStartEvent(this, runDataRequest, userRunDataDetail, activityTypeDto, MDC.get("spanId")));
        return userRunDataDetail;
    }

    /**
     * 设置跑步数据
     *
     * @param sourceUserRunData
     * @param targetUserRunData
     */
    private void setRunData(ZnsUserRunDataEntity sourceUserRunData, ZnsUserRunDataEntity targetUserRunData) {
        if (Objects.isNull(sourceUserRunData) || Objects.isNull(targetUserRunData)) {
            return;
        }
        targetUserRunData.setRunCount(sourceUserRunData.getRunCount());
        targetUserRunData.setRunTime(sourceUserRunData.getRunTime());
        targetUserRunData.setRunMileage(sourceUserRunData.getRunMileage());
        targetUserRunData.setKilocalorie(sourceUserRunData.getKilocalorie());
        targetUserRunData.setStepNum(sourceUserRunData.getStepNum());
        targetUserRunData.setTotalHeartBeat(sourceUserRunData.getTotalHeartBeat());
        if (NumberUtils.geZero(sourceUserRunData.getMaxPace())) {
            targetUserRunData.setMaxPace(sourceUserRunData.getMaxPace());
        }
        if (NumberUtils.geZero(sourceUserRunData.getOptimumPace())) {
            targetUserRunData.setOptimumPace(sourceUserRunData.getOptimumPace());
        }
        targetUserRunData.setMaxCapabilityValue(sourceUserRunData.getMaxCapabilityValue());
        targetUserRunData.setMaxKilocalorie(sourceUserRunData.getMaxKilocalorie());
        targetUserRunData.setMaxMileageSingleDay(sourceUserRunData.getMaxMileageSingleDay());
        targetUserRunData.setMaxRunTime(sourceUserRunData.getMaxRunTime());
        targetUserRunData.setMaxStepFrequency(sourceUserRunData.getMaxStepFrequency());
    }

    /**
     * 设置详情心率时长
     *
     * @param entity
     * @param entityList
     * @param userEntity
     */
    private void setDetailHeartRateTimeMap1(ZnsUserRunDataDetailsEntity entity, List<ZnsUserRunDataDetailsSecondEntity> entityList, ZnsUserEntity userEntity) {
        //获取心率强度划分标准
        String config = sysConfigService.selectConfigByKey("heart_rate_intensity_division_standard");
        if (!StringUtils.hasText(config)) {
            log.warn("心率强度划分标准不存在，请注意");
            config = "{\"warmUp\":\"50-60\",\"fatBurning\":\"60-70\",\"aerobic\":\"70-80\",\"anaerobic\":\"80-90\",\"limit\":\"90-100\"}";
        }
        if (Objects.isNull(userEntity)) {
            userEntity = new ZnsUserEntity();
        }
        BigDecimal maxHeartRateDecimal = new BigDecimal(SportsDataUnit.getUserMaxHeart(userEntity.getBirthday()));

        Map<String, Object> standard = JsonUtil.readValue(config);
        /********热身心率时长********/
        String warmUp = com.linzi.pitpat.core.util.MapUtil.getString(standard.get("warmUp"));
        if (!StringUtils.hasText(warmUp)) {
            warmUp = "50-60";
        }
        Integer warmUpHeartRateTime = getProportion(warmUp, maxHeartRateDecimal, entityList);
        entity.setWarmUpHeartRateTime(warmUpHeartRateTime);

        /********燃脂心率时长********/
        String fatBurning = com.linzi.pitpat.core.util.MapUtil.getString(standard.get("fatBurning"));
        if (!StringUtils.hasText(fatBurning)) {
            fatBurning = "60-70";
        }
        Integer fatBurningHeartRateTime = getProportion(fatBurning, maxHeartRateDecimal, entityList);
        entity.setFatBurningHeartRateTime(fatBurningHeartRateTime);

        /********有氧心率时长********/
        String aerobic = com.linzi.pitpat.core.util.MapUtil.getString(standard.get("aerobic"));
        if (!StringUtils.hasText(aerobic)) {
            aerobic = "70-80";
        }
        Integer aerobicHeartRateTime = getProportion(aerobic, maxHeartRateDecimal, entityList);
        entity.setAerobicHeartRateTime(aerobicHeartRateTime);

        /********无氧心率时长********/
        String anaerobic = com.linzi.pitpat.core.util.MapUtil.getString(standard.get("anaerobic"));
        if (!StringUtils.hasText(anaerobic)) {
            anaerobic = "80-90";
        }
        Integer anaerobicHeartRateTime = getProportion(anaerobic, maxHeartRateDecimal, entityList);
        entity.setAnaerobicHeartRateTime(anaerobicHeartRateTime);

        /********极限心率时长********/
        String limit = com.linzi.pitpat.core.util.MapUtil.getString(standard.get("limit"));
        if (!StringUtils.hasText(limit)) {
            limit = "90-100";
        }
        Integer limitHeartRateTime = getProportion(limit, maxHeartRateDecimal, entityList);
        entity.setLimitHeartRateTime(limitHeartRateTime);
    }

    /**
     * 计算心率时长
     *
     * @param section
     * @param maxHeartRateDecimal
     * @param entityList
     * @return
     */
    private Integer getProportion(String section, BigDecimal maxHeartRateDecimal, List<ZnsUserRunDataDetailsSecondEntity> entityList) {
        String[] split = section.split("-");
        int startHeartRate = BigDecimalUtil.divHalfUp(BigDecimalUtil.multiply(new BigDecimal(split[0]), maxHeartRateDecimal), new BigDecimal(100), 0).intValue();
        int endHeartRate = BigDecimalUtil.divHalfUp(BigDecimalUtil.multiply(new BigDecimal(split[1]), maxHeartRateDecimal), new BigDecimal(100), 0).intValue();
        Long count = entityList.stream().filter(e -> Objects.nonNull(e.getHeartRate()) && e.getHeartRate().intValue() >= startHeartRate && e.getHeartRate().intValue() < endHeartRate).count();

        return count.intValue();
    }

    /**
     * 跑步里程到达目标
     *
     * @param userId
     * @param totalRunMileage
     */
    private void runMileageArriveGoalPush(Long userId, Integer totalRunMileage) {
        Integer activityUserCountByUserId = runActivityUserService.findActivityUserCountByUserId(userId, null, null, null, null, null);

        if (Objects.isNull(activityUserCountByUserId) || activityUserCountByUserId <= 0) {
//            //通知
//            ActivityNotificationEnum activityNotification = ActivityNotificationEnum.RUN_MILEAGE_CHANGE;
//            String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.RUN_MILEAGE_CHANGE"), totalRunMileage);
//            MessageBo message = appMessageService.assembleMessage("4", "", content, NoticeTypeEnum.ACTIVITY_INVITATION.getType());
//            appMessageService.sendImAndPushUserIds(Arrays.asList(userId), message, content);
            //查询符合条件的里程碑
            ZonedDateTime now = ZonedDateTime.now();

            RunActivityQuery query = RunActivityQuery.builder()
                    .activityType(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType())
                    .status(1)
                    .activityState(1)
                    .maxApplicationStartTime(ZonedDateTime.now())
                    .minApplicationEndTime(ZonedDateTime.now()).build();
            query.setOrders(Lists.newArrayList(OrderItem.desc("activity_end_time")));
            ZnsRunActivityEntity runActivity = runActivityService.findOne(query);
            if (Objects.isNull(runActivity)) {
                return;
            }
            String config = sysConfigService.selectConfigByKey("new.activity.push.config");
            Map<String, Object> jsonObject = JsonUtil.readValue(config);
            String activityContent = MapUtil.getString(jsonObject.get("content"));
            activityContent = activityContent.replace("{activityTitle}", runActivity.getActivityTitle());
            ImMessageBo imMessageBo = appMessageService.assembleImActivityMessage(runActivity, activityContent);
            appMessageService.sendIm("", Arrays.asList(userId), JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
        }
    }

    private Integer getActivityType(Long activityId) {
        if (Objects.isNull(activityId)) {
            return null;
        }
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.isNull(mainActivity)) {
            ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
            if (Objects.isNull(runActivity)) {
                return null;
            }
            return runActivity.getActivityType();
        }
        if (MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
            return mainActivity.getOldType();
        }
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType();
        } else if (MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.PROP_ACTIVITY.getType();
        } else if (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType();
        } else if (MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.FREE_CHALLENGE.getType();
        } else if (MainActivityTypeEnum.PLACEMENT.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.PLACEMENT_ACTIVITY.getType();
        } else {
            return RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType();
        }

    }


    public Integer getComplete(ZnsUserRunDataDetailsEntity userRunDataDetails, MainActivity activity, Boolean isClubActivity) {
        Integer isComplete = 0;
        if (MainActivityTypeEnum.PROP.getType().equals(activity.getMainType())) {
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(userRunDataDetails.getActivityId(), userRunDataDetails.getUserId());
            if (Objects.nonNull(activityUser)) {
                isComplete = activityUser.getIsComplete();
            }
        } else if (!MainActivityTypeEnum.OLD.getType().equals(activity.getMainType()) && !isClubActivity) {
            EntryGameplay gameplay = entryGameplayService.findByGameplayId(activity.getPlayId());
            if (Objects.nonNull(gameplay) && gameplay.getCompetitionFormat() == 1) {
                isComplete = 1;
            } else {
                if (userRunDataDetails.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0 && userRunDataDetails.getRunMileage().compareTo(userRunDataDetails.getDistanceTarget()) >= 0) {
                    isComplete = 1;
                } else if (userRunDataDetails.getTimeTarget() > 0 && userRunDataDetails.getRunTime() >= userRunDataDetails.getTimeTarget()) {
                    isComplete = 1;
                } else if (userRunDataDetails.getDistanceTarget().compareTo(BigDecimal.ZERO) <= 0 && userRunDataDetails.getTimeTarget() <= 0) {
                    isComplete = 1;
                }
            }
        } else {
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(userRunDataDetails.getActivityId(), userRunDataDetails.getUserId());
            if (Objects.nonNull(activityUser)) {
                isComplete = activityUser.getIsComplete();
            }
        }
        return isComplete;
    }
}
