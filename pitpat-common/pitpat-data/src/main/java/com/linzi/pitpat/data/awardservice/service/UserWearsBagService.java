package com.linzi.pitpat.data.awardservice.service;
/**
 * <p>
 * 用户服装背包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;

import java.time.ZonedDateTime;
import java.util.List;

public interface UserWearsBagService {

    UserWearsBag findByById(Long id);

    Integer insert(UserWearsBag userWearsBag);

    int update(UserWearsBag userWearsBag);

    List<UserWearsBag> findListByUserIdAndIsNew(Long userId, Integer isNew);

    /**
     * 软删除过期服装
     *
     * @param ids
     */
    void deleteByIds(List<Long> ids);

    UserWearsBag findByQuery(UserWearBagQuery query);

    ZonedDateTime getAddHours(Integer expiredTime);

    Long sendUserWear(Long userId, WearAwardDto wearAwardDto, Long activityId);

    /**
     * 一次性发放，服装没有就发，有了不发
     *
     * @param userId
     * @param wearAwardDto
     */
    void sendOnceWear(Long userId, WearAwardDto wearAwardDto);

    List<UserWearsBag> findListByQuery(UserWearBagQuery build);

    /**
     * 根据活动Id 和 用户ID 获取某个活动发放的奖励
     *
     * @param userId
     * @param activityId
     * @return
     */
    List<UserWearsBag> findList(Long userId, Long activityId);

    List<UserWearsBag> findList(Long userId, Long activityId, ZonedDateTime createTime);

    List<UserWearsBag> findListTimeNotNull();

    void updateBatchByIds(List<Long> expiredBagIds);

    List<UserWearsBag> findListByIds(List<Long> bagIds);

    Long countByQuery(Long userId, int status, ZonedDateTime startTime, ZonedDateTime endTime);

    List<UserWearsBag> findListWearType(Long userId, int status, Integer wearType);

    UserWearsBag findByByIdWithoutLogicDelete(Long id);
}
