package com.linzi.pitpat.data.courseservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 课程多语言表
 *
 * <AUTHOR>
 * @since 2024-01-23
 */

@Data
@NoArgsConstructor
@TableName("zns_course_i18n")
public class CourseI18nEntity implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //课程id
    private Long courseId;
    //语言名称
    private String languageName;
    /**
     * 语言code@LanguageCodeEnum，en_US：英语，fr_CA ：法语，de_DE：德语，it_IT：意大利语，es_ES：西班牙语，pt_PT：葡萄牙语
     *
     * @see I18nConstant.LanguageCodeEnum
     */
    private String languageCode;
    //课程名称
    private String courseName;
    //课程介绍
    private String courseDesc;
    //注意事项
    private String trainingSuggestions;
    //适用人群
    private String intendedFor;
    //禁忌人群
    private String tabooPopulation;
    //课程封面图
    private String backgroundPicture;
    //预览视频
    private String backgroundVideo;
    //课程视频
    private String courseVideo;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建者
    private String creator;
    //修改者
    private String modifier;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
}
