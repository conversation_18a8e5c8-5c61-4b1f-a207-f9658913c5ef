package com.linzi.pitpat.data.bussiness;

import com.linzi.pitpat.core.constants.enums.DingTalkTokenEnum;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.enums.email.AwsEmailBounceType;
import com.linzi.pitpat.data.enums.email.AwsEmailComplaintSubType;
import com.linzi.pitpat.data.enums.email.AwsEmailDelayType;
import com.linzi.pitpat.data.enums.email.AwsEmailEventType;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventBounce;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventClick;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventComplaint;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventDelivery;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventDeliveryDelay;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventDeliveryDelayRecipients;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventMailRequest;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventOpen;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventReject;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventRenderingFailure;
import com.linzi.pitpat.data.messageservice.dto.request.AwsEmailEventRequest;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2024/2/29 14:29
 */
@Component
@Slf4j
public class AwsEmailEventManager {
    @Resource
    private ZnsUserEmailSendingRecordService userEmailSendingRecordService;
    private final Map<String, Consumer<AwsEmailEventRequest>> eventHandlerMap = new HashMap<>();

    public AwsEmailEventManager() {
        initEventHandlerMap();
    }

    private void initEventHandlerMap() {
        eventHandlerMap.put(AwsEmailEventType.Delivery.getCode(), this::deliveryEvent);
        eventHandlerMap.put(AwsEmailEventType.Open.getCode(), this::openEvent);
        eventHandlerMap.put(AwsEmailEventType.Click.getCode(), this::clickEvent);
        eventHandlerMap.put(AwsEmailEventType.Bounce.getCode(), this::bounceEvent);
        eventHandlerMap.put(AwsEmailEventType.Complaint.getCode(), this::complaintEvent);
        eventHandlerMap.put(AwsEmailEventType.Rendering_Failure.getCode(), this::renderingFailureEvent);
        eventHandlerMap.put(AwsEmailEventType.DeliveryDelay.getCode(), this::deliveryDelayEvent);
        eventHandlerMap.put(AwsEmailEventType.Reject.getCode(), this::rejectEvent);
    }

    /**
     * 邮件事件处理
     *
     * @param awsEmailEventRequest
     * @return
     */
    public boolean handleAwsEmailEvent(AwsEmailEventRequest awsEmailEventRequest) {
        String eventType = awsEmailEventRequest.getEventType();
        try {
            if (StringUtil.isEmpty(eventType)) {
                log.info("aws email event deal error,eventType is null");
                return false;
            }

            // 使用策略模式动态选择事件处理逻辑
            Consumer<AwsEmailEventRequest> eventHandler = eventHandlerMap.get(eventType);
            if (eventHandler != null) {
                eventHandler.accept(awsEmailEventRequest);
            } else {
                log.info("Unsupported event type: " + eventType);
                return false;
            }
        } catch (BaseException ce) {
            log.info("event error,e=", ce);
        } catch (Exception e) {
            log.error("event error,e=", e);
            // 钉钉通知
            DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ONLINE_ABNORMAL_MONITORING;
            DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【异常】： \n"
                    + "AWS邮件事件回调处理失败：\n"
                    + "eventType=" + eventType + "\n"
                    + "日志id：" + MDC.get("spanId") + "\n"
                    + "错误信息：" + e), "online");
            return false;
        }

        return true;
    }

    /**
     * 投递延迟
     *
     * @param request
     */
    private void deliveryDelayEvent(AwsEmailEventRequest request) {
        AwsEmailEventDeliveryDelay deliveryDelay = request.getDeliveryDelay();
        if (Objects.isNull(deliveryDelay)) {
            throw new BaseException("AwsEmailEventDeliveryDelay is null");
        }
        List<AwsEmailEventDeliveryDelayRecipients> delayedRecipients = deliveryDelay.getDelayedRecipients();
        if (CollectionUtils.isEmpty(delayedRecipients)) {
            log.info("deliveryDelayEvent end,delayedRecipients is null");
            return;
        }
        List<String> emailList = delayedRecipients.stream().map(AwsEmailEventDeliveryDelayRecipients::getEmailAddress).toList();
        List<ZnsUserEmailSendingRecordEntity> userEmailSendingRecordList = findUserEmailSendingRecordList(request, emailList);
        if (CollectionUtils.isEmpty(userEmailSendingRecordList)) {
            log.info("deliveryDelayEvent end,userEmailSendingRecordList is null");
            return;
        }

        String remark = AwsEmailDelayType.findDesc(deliveryDelay.getDelayType());

        for (ZnsUserEmailSendingRecordEntity znsUserEmailSendingRecordEntity : userEmailSendingRecordList) {
            ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
            update.setId(znsUserEmailSendingRecordEntity.getId());
            update.setRemark(remark);
            update.setEmailStatus(AwsEmailEventType.DeliveryDelay.getCode());
            userEmailSendingRecordService.update(update);
        }

        log.info("deliveryDelayEvent 修改record记录成功");
        // 钉钉通知
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【注意】： \n"
                        + "用户邮箱：" + emailList + " AWS邮件服务邮件延迟发送\n"
                        + "日志id：" + MDC.get("spanId") + "\n"
                        + "延迟原因：" + remark + "\n"
                        + "发生延迟时间：" + deliveryDelay.getTimestamp() + "\n"), "online");
    }


    /**
     * 投诉事件
     *
     * @param request
     */
    private void complaintEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);
        AwsEmailEventComplaint complaint = request.getComplaint();
        if (Objects.isNull(complaint)) {
            throw new BaseException("AwsEmailEventComplaint is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        String remark = AwsEmailComplaintSubType.findDesc(complaint.getComplaintSubType());
        update.setRemark(remark);
        update.setEmailStatus(AwsEmailEventType.Complaint.getCode());
        userEmailSendingRecordService.update(update);
        log.info("complaintEvent 修改record记录成功");
        // 钉钉通知
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【注意】： \n"
                        + "用户邮箱：" + complaint.getComplainedRecipients() + " AWS邮件服务邮件投诉\n"
                        + "日志id：" + MDC.get("spanId") + "\n"
                        + "投诉原因：" + remark + "\n"
                        + "投诉时间：" + complaint.getTimestamp() + "\n"
                        + "投诉的唯一 ID：" + complaint.getFeedbackId() + "\n"), "online");
    }

    /**
     * 退信事件
     *
     * @param request
     */
    private void bounceEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);
        AwsEmailEventBounce bounce = request.getBounce();
        if (Objects.isNull(bounce)) {
            throw new BaseException("AwsEmailEventBounce is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        String remark = AwsEmailBounceType.BounceSubType.findByTypeAndSubType(bounce.getBounceType(), bounce.getBounceSubType());
        update.setRemark(remark);
        update.setEmailStatus(AwsEmailEventType.Bounce.getCode());
        userEmailSendingRecordService.update(update);
        log.info("bounceEvent 修改record记录成功");
        // 钉钉通知
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【注意】： \n"
                        + "用户邮箱：" + JsonUtil.writeString(bounce.getBouncedRecipients()) + " AWS邮件服务邮件退回\n"
                        + "日志id：" + MDC.get("spanId") + "\n"
                        + "退回原因：" + remark + "\n"
                        + "退回时间：" + bounce.getTimestamp() + "\n"
                        + "退信的唯一 ID：" + bounce.getFeedbackId() + "\n"), "online");
    }

    /**
     * 拒绝
     *
     * @param request
     */
    private void rejectEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);
        AwsEmailEventReject reject = request.getReject();
        if (Objects.isNull(reject)) {
            throw new BaseException("AwsEmailEventReject is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        update.setEmailStatus(AwsEmailEventType.Reject.getCode());
        userEmailSendingRecordService.update(update);
        log.info("rejectEvent 修改record记录成功");
        // 钉钉通知
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【注意】： \n"
                        + "用户邮箱：" + userEmailSendingRecord.getEmailAddressEn() + " AWS邮件服务邮件拒绝\n"
                        + "日志id：" + MDC.get("spanId") + "\n"
                        + "拒绝原因：" + reject.getReason() + "\n"), "online");
    }

    /**
     * 打开事件
     *
     * @param request
     */
    private void openEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);
        AwsEmailEventOpen open = request.getOpen();
        if (Objects.isNull(open)) {
            throw new BaseException("AwsEmailEventOpen is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        update.setEmailStatus(AwsEmailEventType.Open.getCode());
        userEmailSendingRecordService.update(update);
        log.info("openEvent 修改record记录成功");
    }

    /**
     * 呈现失败事件
     *
     * @param request
     */
    private void renderingFailureEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);
        AwsEmailEventRenderingFailure failure = request.getFailure();
        if (Objects.isNull(failure)) {
            throw new BaseException("AwsEmailEventRenderingFailure is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        update.setEmailStatus(AwsEmailEventType.Rendering_Failure.getCode());
        userEmailSendingRecordService.update(update);
        log.info("openEvent 修改record记录成功");
        // 钉钉通知
        DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ABNORMAL_MONITORING_ALL;
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【注意】： \n"
                        + "用户邮箱：" + userEmailSendingRecord.getEmailAddressEn() + " AWS邮件服务邮件呈现失败\n"
                        + "日志id：" + MDC.get("spanId") + "\n"
                        + "模板的名称：" + failure.getTemplateName() + "\n"
                        + "错误信息：" + failure.getErrorMessage() + "\n"), "online");
    }

    /**
     * 点击事件
     *
     * @param request
     */
    private void clickEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);
        AwsEmailEventClick click = request.getClick();
        if (Objects.isNull(click)) {
            throw new BaseException("AwsEmailEventClick is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        update.setEmailStatus(AwsEmailEventType.Click.getCode());
        userEmailSendingRecordService.update(update);
        log.info("clickEvent 修改record记录成功");
    }

    /**
     * 送达事件处理
     *
     * @param request
     */
    private void deliveryEvent(AwsEmailEventRequest request) {
        ZnsUserEmailSendingRecordEntity userEmailSendingRecord = findUserEmailSendingRecord(request);

        AwsEmailEventDelivery delivery = request.getDelivery();
        if (Objects.isNull(delivery)) {
            throw new BaseException("AwsEmailEventDelivery is null");
        }
        ZnsUserEmailSendingRecordEntity update = new ZnsUserEmailSendingRecordEntity();
        update.setId(userEmailSendingRecord.getId());
        update.setEmailStatus(AwsEmailEventType.Delivery.getCode());
        try {
            update.setDeliveryTime(DateTimeUtil.parseWithPattern(delivery.timestamp, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
        } catch (Exception e) {
            log.error("deliveryEvent 修改record记录失败", e);
        }
        userEmailSendingRecordService.update(update);
        log.info("deliveryEvent 修改record记录成功");
    }

    /**
     * 查询发送信息
     *
     * @param request
     * @return
     */
    private ZnsUserEmailSendingRecordEntity findUserEmailSendingRecord(AwsEmailEventRequest request) {
        AwsEmailEventMailRequest mail = request.getMail();
        if (Objects.isNull(mail)) {
            throw new BaseException("AwsEmailEventMailRequest is null");
        }
        String messageId = mail.getMessageId();
        ZnsUserEmailSendingRecordEntity sendingRecord = userEmailSendingRecordService.findByMessageId(messageId);
        if (Objects.isNull(sendingRecord)) {
            throw new BaseException("sendingRecord is null");
        }
        return sendingRecord;
    }

    private List<ZnsUserEmailSendingRecordEntity> findUserEmailSendingRecordList(AwsEmailEventRequest request, List<String> emailList) {
        AwsEmailEventMailRequest mail = request.getMail();
        if (Objects.isNull(mail)) {
            throw new BaseException("AwsEmailEventMailRequest is null");
        }
        String messageId = mail.getMessageId();
        List<ZnsUserEmailSendingRecordEntity> list = userEmailSendingRecordService.findListByMessageId(messageId, emailList);
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException("sendingRecord is null");
        }
        return list;
    }
}
