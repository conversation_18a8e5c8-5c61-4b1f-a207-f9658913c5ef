package com.linzi.pitpat.data.awardservice.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public class PayConstant {

    /**
     * 购买类型 类型："coupon":优惠券,"vip":会员,"scoreExchange":积分商城,"exchangeWear": 我的形象皮肤,"battlePass":新里程碑 ,"mallOrder":商场订单
     */
    @Getter
    @AllArgsConstructor
    public enum BuyTypeEnum {
        COUPON("coupon", "优惠券"),
        VIP("vip", "会员"),
        SCOREEX_CHANGE("scoreExchange", "积分商城"),
        EXCHANGE_WEAR("exchangeWear", "我的形象皮肤"),
        BATTLE_PASS("battlePass", "新里程碑"),
        MALL_ORDER("mallOrder", "商城订单"),
        REFUND_ORDER("refundOrder", "退款单"),
        H5_MALL_ORDER("h5MallOrder", "投流H5商城订单"),
        ;

        public final String type;
        public final String name;

        public static List<String> findMallType() {
            return List.of(MALL_ORDER.type,H5_MALL_ORDER.type);
        }
    }

    /**
     * 支付类型 0:余额支付；1:paypal支付；2：钱海支付
     */
    @Getter
    @AllArgsConstructor
    public enum PayTypeEnum {
        BALANCE(0, "余额支付", "balance", ""),
        PAYPAL(1, "paypal", "paypal", ""),
        OCEANPAY(2, "信用卡", "oceanpay", "Credit Card"),
        OCEANPAY_GOOGLE(3, "google pay", "oceanpay_google", "GooglePay"),
        OCEANPAY_APPLE(4, "Apple pay", "oceanpay_apple", "ApplePay"),
        OCEANPAY_AFTERPAY(5, "afterPay", "oceanpay_afterpay", "Afterpay"),
        OCEANPAY_KLARNA(6, "klarna", "oceanpay_klarna", "Klarna"),
        PAYPAL_PAY_LATER(7, "paypal pay later", "paypal_pay_later", ""),
        ZERO_PAY(8, "zero_pay", "zero_pay", ""),
        ;

        public static PayTypeEnum findByType(Integer type) {
            return Arrays.stream(PayTypeEnum.values()).filter(e -> e.getType().equals(type)).findFirst().orElse(BALANCE);
        }

        public static PayTypeEnum findByCode(String code) {
            return Arrays.stream(PayTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        }

        public static PayTypeEnum findByThirdCode(String code) {
            return Arrays.stream(PayTypeEnum.values()).filter(e -> e.getThirdCode().equals(code)).findFirst().orElse(OCEANPAY);
        }
        public static List<PayTypeEnum> findByTypeList(List<Integer> types) {
            return Arrays.stream(PayTypeEnum.values())
                    .filter(e -> types.contains(e.getType()))
                    .toList();
        }

        public final Integer type;
        public final String name;
        public final String code;
        public final String thirdCode;

        public static boolean canRefund(Integer type) {
            return !PayTypeEnum.BALANCE.getType().equals(type);
        }

        public static boolean isOceanPay(Integer type) {
            return OCEANPAY.type.equals(type)
                    || OCEANPAY_GOOGLE.type.equals(type)
                    || OCEANPAY_APPLE.type.equals(type)
                    || OCEANPAY_AFTERPAY.type.equals(type)
                    || OCEANPAY_KLARNA.type.equals(type);
        }
    }

    /**
     * 状态：0：发起支付；1:支付成功；2:支付中；-1:支付失败; 3已取消
     */
    @Getter
    @AllArgsConstructor
    public enum PayStatusEnum {
        PAY_STATUS_N1(-1, "支付失败"),
        PAY_STATUS_0(0, "发起支付"),
        PAY_STATUS_1(1, "支付成功"),
        PAY_STATUS_2(2, "支付中"),
        PAY_STATUS_3(3, "已取消"),
        ;

        public final Integer type;
        public final String name;
    }

    /**
     * 退款支付状态枚举
     * PENDING:处理中，COMPLETED：退款成功，FAILED ：退款失败
     */
    @Getter
    @AllArgsConstructor
    public enum RefundPayStatusEnum {
        PENDING("PENDING", "处理中"),
        COMPLETED("COMPLETED", "退款成功"),
        FAILED("FAILED", "退款失败"),
        ;

        public static RefundPayStatusEnum findByCode(String type) {
            return Arrays.stream(RefundPayStatusEnum.values()).filter(e -> e.getType().equals(type)).findFirst().orElse(PENDING);
        }

        public final String type;
        public final String name;
    }

    /**
     * 订单支付状态枚举
     * PENDING:处理中，COMPLETED：支付成功，FAILED ：支付失败
     */
    @Getter
    @AllArgsConstructor
    public enum OrderPayStatusEnum {
        PENDING("PENDING", "处理中"),
        COMPLETED("COMPLETED", "支付成功"),
        FAILED("FAILED", "支付失败"),
        ;

        public static OrderPayStatusEnum findByCode(String type) {
            return Arrays.stream(OrderPayStatusEnum.values()).filter(e -> e.getType().equals(type)).findFirst().orElse(PENDING);
        }

        public final String type;
        public final String name;
    }
}
