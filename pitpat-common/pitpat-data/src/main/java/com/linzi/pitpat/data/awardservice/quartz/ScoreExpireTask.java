package com.linzi.pitpat.data.awardservice.quartz;


import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.awardservice.manager.api.ScoreManager;
import com.linzi.pitpat.data.awardservice.model.query.UserScorePageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.time.ZonedDateTime;

@Component("scoreExpireTask")
@Slf4j
public class ScoreExpireTask {

    @Autowired
    private ScoreManager scoreManager;

    public void run() {
        ZonedDateTime startTime = DateUtil.startOfDate(ZonedDateTime.now());
        ZonedDateTime endTime = DateUtil.addDays(startTime, 1);

        UserScorePageQuery userScorePageQuery = new UserScorePageQuery().setGeExpireTime(startTime).setLtExpireTime(endTime)
                .setStatusList(Arrays.asList(1, 3)).setIsRobot(0);
        userScorePageQuery.setPageNum(1);
        userScorePageQuery.setPageSize(1000);
        userScorePageQuery.setSearchCount(false);
        scoreManager.scoreExpire(userScorePageQuery);
    }

    public void robotRun() {
        ZonedDateTime startTime = DateUtil.startOfDate(ZonedDateTime.now());
        ZonedDateTime endTime = DateUtil.addDays(startTime, 1);

        UserScorePageQuery userScorePageQuery = new UserScorePageQuery().setGeExpireTime(startTime).setLtExpireTime(endTime)
                .setStatusList(Arrays.asList(1, 3)).setIsRobot(1);
        userScorePageQuery.setPageNum(1);
        userScorePageQuery.setPageSize(1000);
        userScorePageQuery.setSearchCount(false);
        scoreManager.scoreExpire(userScorePageQuery);
    }
}
