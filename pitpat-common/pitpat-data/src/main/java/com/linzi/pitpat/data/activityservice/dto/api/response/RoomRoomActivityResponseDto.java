package com.linzi.pitpat.data.activityservice.dto.api.response;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 返回对象有各种定义，BO,VO,DTO 等等
 * 用户响应对象，Response 不要简写
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RoomRoomActivityResponseDto implements Serializable {
    // 开始活动id
    Long activityId;
    /**
     * 活动开始时间
     */
    ZonedDateTime activityStartTime;
}
