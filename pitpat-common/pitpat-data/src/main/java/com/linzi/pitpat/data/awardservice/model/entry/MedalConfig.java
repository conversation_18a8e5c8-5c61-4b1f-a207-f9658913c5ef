package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.MedalConstant;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*勋章配置表
 *
 * <AUTHOR>
 * @since 2022-06-23
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("zns_medal_config")
public class MedalConfig implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;

    /**
     * 勋章分类:1：实力勋章，2：参加经历，3：冠军之路，4：累计里程，5：累计奖金,6,惊喜，7: 赛事勋章
     *
     * @see MedalConstant.MedalTypeEnum
     */
    private Integer type;
    //勋章名称
    private String name;
    //描述
    private String remark;
    //中文勋章名称
    private String zhName;
    //中文描述
    private String zhRemark;
    //等级
    private Integer level;
    //处理器
    private String handler;
    //参数个数
    private Integer paramNum;
    //第一个参数
    private String param1;
    //第二个参数
    private String param2;
    //第三个参数
    private String param3;
    //第四个参数
    private String param4;
    //第五个参数
    private String param5;
    //条件类型
    private String conditionType;
    /**
     * 是否隐藏 ，0：不隐藏，1：影藏
     *
     * @see MedalConstant.MedalHideEnum
     */
    private Integer isHide;
    //图片url
    private String url;
    //是否跑步结束才触发，0 不是， 1 是
    private Integer endTrigger;
    //进度参数
    private String process;
    //位置
    private Integer pos;
    /**
     * 有效天数，（-1,null）：永久有效
     */
    private Integer validHours;
    // 默认语言
    private String defaultLangCode;

    /**
     * 创建者
     */
    private String creator;
    /**
     * 最后修改者
     */
    private String modifier;


    /**
     * 枚举:ABILITY,1,实力勋章:TAKE,2,参加经历:GOLD,3,冠军之路:SUM_MILEAGE,4,累计里程:SUM_REWARD,5,累计奖金:SURPRISED,6,惊喜
     */
    public enum TYPE_ENUM {
        ABILITY(1, "实力勋章"),
        TAKE(2, "参加经历"),
        GOLD(3, "冠军之路"),
        SUM_MILEAGE(4, "累计里程"),
        SUM_REWARD(5, "累计奖金"),
        SURPRISED(6, "惊喜");


        private Integer code;

        private String desc;

        TYPE_ENUM(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    @Override
    public String toString() {
        return "MedalConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",type=" + type +
                ",name=" + name +
                ",level=" + level +
                ",handler=" + handler +
                ",paramNum=" + paramNum +
                ",param1=" + param1 +
                ",param2=" + param2 +
                ",param3=" + param3 +
                ",param4=" + param4 +
                ",param5=" + param5 +
                ",remark=" + remark +
                ",conditionType=" + conditionType +
                ",isHide=" + isHide +
                ",url=" + url +
                ",endTrigger=" + endTrigger +
                ",process=" + process +
                ",pos=" + pos +
                ",validHours=" + validHours +
                "}";
    }
}
