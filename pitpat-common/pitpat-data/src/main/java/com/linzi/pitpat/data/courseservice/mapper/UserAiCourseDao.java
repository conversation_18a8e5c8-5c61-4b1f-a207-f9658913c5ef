package com.linzi.pitpat.data.courseservice.mapper;
/**
 * <p>
 * 用户ai课程计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-25
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.courseservice.model.entity.UserAiCourse;
import com.lz.mybatis.plugin.annotations.Column;
import com.lz.mybatis.plugin.annotations.OrderByIdDescLimit_1;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;

@Mapper
public interface UserAiCourseDao extends BaseMapper<UserAiCourse> {


    UserAiCourse selectUserAiCourseById(@Param("id") Long id);


    Long insertUserAiCourse(UserAiCourse userAiCourse);


    Long insertOrUpdateUserAiCourse(UserAiCourse userAiCourse);


    int updateUserAiCourseById(UserAiCourse userAiCourse);


    int updateCoverUserAiCourseById(UserAiCourse userAiCourse);


    int deleteUserAiCourseById(@Param("id") Long id);


    @OrderByIdDescLimit_1
    UserAiCourse selectByCourseId(@Column(UserAiCourse.user_id) Long userId, @Column(UserAiCourse.course_id) Long courseId);

    void updateStatus(@Column(UserAiCourse.status_) int status, @Column(UserAiCourse.gmt_modified) ZonedDateTime gmtModified, @Column(UserAiCourse.id_) Long id);
}
