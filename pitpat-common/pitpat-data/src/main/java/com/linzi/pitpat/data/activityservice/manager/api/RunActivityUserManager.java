package com.linzi.pitpat.data.activityservice.manager.api;


import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityCouponDto;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.resp.CanChallengeResp;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityCheckVo;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.activityservice.strategy.OfficialRankingActivityStrategy;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.dto.LastActivityUserDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.entity.dto.DelayReportDto;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.service.ZnsMessageService;
import com.linzi.pitpat.data.request.CanChallengeRequest;
import com.linzi.pitpat.data.robotservice.model.entity.CeoRobotIncreaseConfig;
import com.linzi.pitpat.data.robotservice.model.entity.DelayTimeSetting;
import com.linzi.pitpat.data.robotservice.service.RobotRunModeService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserMatchModeRecord;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.UserMatchModeRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.stream.Collectors;


/**
 * 活动用户业务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunActivityUserManager {
    private final UserIdentityService userIdentityService;
    private final ZnsUserService userService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ActivityStrategyContext activityStrategyContext;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final ISysConfigService sysConfigService;
    private final RobotRunModeService robotRunModeService;
    private final RabbitTemplate rabbitTemplate;
    private final UserMatchModeRecordService userMatchModeRecordService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final PkChallengeRecordService pkChallengeRecordService;
    private final CouponService couponService;
    private final ZnsMessageService messageService;
    private final MindUserMatchBizService mindUserMatchBizService;
    @Value("${zns.config.rabbitQueue.delayed_content_publish_exchange_name}")
    private String delay_exchange_name;

    /**
     * 添加邀请参与活动的用户
     *
     * @param activityId       活动ID
     * @param activityUserIds  活动用户id列表
     * @param inviterUserName
     * @param activity
     * @param challengeRunType
     * @param runActivity
     */
    public void addRunActivityUsers(Long activityId, List<Long> activityUserIds, Long inviterUserID, String inviterUserName, ZnsRunActivityEntity activity, Integer challengeRunType, RunActivityRequest runActivity) {
        if (CollectionUtils.isEmpty(activityUserIds) || null == activityId) {
            log.error("添加活动用户失败");
            return;
        }
        List<Integer> pkType = RunActivitySubTypeEnum.getPkType();
        List<ZnsUserEntity> userEntities = userService.findByIds(activityUserIds);
        Map<Long, ZnsUserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
        for (int i = 0; i < activityUserIds.size(); i++) {

            Long userId = activityUserIds.get(i);
            log.info("开始添加用户{}", userId);
            ZnsUserEntity userEntity = userEntityMap.get(userId);
            if (null != userEntity) {
                // 判断用户是否已经被邀请
                ZnsRunActivityUserEntity inviteeUser = znsRunActivityUserService.findActivityUser(activityId, userEntity.getId());
                if (null == inviteeUser) {
                    ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
                    activityUser.setActivityId(activityId);
                    activityUser.setUserId(userId);
                    activityUser.setIsRobot(userEntity.getIsRobot());
                    activityUser.setIsTest(userEntity.getIsTest());

                    activityUser.setNickname(userEntity.getFirstName());
                    activityUser.setActivityType(activity.getActivityType());
                    //1v1 匹配直接接受
                    if (Objects.nonNull(challengeRunType) && challengeRunType == 1) {
                        activityUser.setUserState(1);
                    }

                    // 活动参与者
                    activityUser.setUserType(2);
                    if (null != inviterUserID) {
                        activityUser.setInviterUserId(inviterUserID);
                    }

                    activityUser.setTargetRunMileage(activity.getRunMileage().intValue());
                    activityUser.setTargetRunTime(activity.getRunTime());

                    if (pkType.contains(activity.getActivityTypeSub())) {
                        ZnsUserRunDataDetailsEntity dataDetails = userRunDataDetailsService.findById(runActivity.getChallengeRunDataDetailsId());
                        activityUser.setUserState(4);
                        activityUser.setRunDataDetailsId(runActivity.getChallengeRunDataDetailsId());
                        activityUser.setCompleteTime(ZonedDateTime.now());
                        activityUser.setIsComplete(1);
                        activityUser.setRunTime(dataDetails.getRunTime());
                        activityUser.setRunMileage(dataDetails.getRunMileage());
                    }

                    znsRunActivityUserService.save(activityUser);

                    if (userEntity.getIsRobot() == 1) {
                        if (Objects.equals(activity.getActivityTypeSub(), 3)) {
                            return;
                        }
                        log.info("机器人自动接受开始执行,活动{}", activity);
                        //判断机器人是否自动接受
                        Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
                        Integer robotAcceptFeeInvite = MapUtil.getInteger(jsonObject.get(ApiConstants.ACCEPT_FEE_INVITE));
                        // 如果是离线pk，则不参与robotAcceptFeeInvite的判断
                        if (!Objects.equals(activity.getBonusRuleType(), 1) && Objects.nonNull(robotAcceptFeeInvite)
                                && robotAcceptFeeInvite == 0 && !Objects.equals(activity.getActivityTypeSub(), 3)
                                && !Objects.equals(activity.getActivityTypeSub(), 4)) {
                            runActivity.setRobotAcceptSuccess(0);
                            activityUser.setUserState(0);
                            znsRunActivityUserService.updateById(activityUser);
                            log.error("机器人自动接受失败1111111111");
                        } else if (Objects.equals(activity.getActivityType(), 1)) {
                            //非官方组队跑特殊逻辑判断
                            //延迟接收邀请
                            log.info("延迟接收邀请,rot {}", userEntity.getId());
                            DelayReportDto dto = new DelayReportDto();
                            dto.setType("teamRunRotReceive");
                            dto.setParam(
                                    Arrays.asList(JsonUtil.writeString(activity),
                                            JsonUtil.writeString(activityUser),
                                            JsonUtil.writeString(userEntity),
                                            JsonUtil.writeString(inviterUserName))
                            );
                            //设置随机时间 30min-24h
                            //Todo mock
                            int millis = getRandomtimeByType("teamRunRotReceive");
                            ;

                            rabbitTemplate.convertAndSend(delay_exchange_name, "delayRotReport", JsonUtil.writeString(dto), message -> {
                                message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                                message.getMessageProperties().setDelay(millis);
                                return message;
                            });
                            log.info("非官方多人同跑机器人延迟接收邀请消息发送,实体为{}，延时时间为{}", dto, millis);
                        } else {
                            // 如果是挑战赛事，并且报名失败，则抛出异常
                            boolean res = handleActivityRobotUser(activity, activityUser, userEntity, inviterUserName);
                            if (!res && activity.getActivityType() == 2) {
                                runActivity.setRobotAcceptSuccess(0);
                                log.error("机器人自动接受失败2222222");
                            }
                        }
                    }
                }
            } else {
                log.error("添加邀请参与活动的用户不存在,用户id=" + userId);
            }
        }
    }

    private int getRandomtimeByType(String type) {
        String setting = sysConfigService.selectConfigByKey("rotRandomSetting");
        List<DelayTimeSetting> list = JsonUtil.readList(setting, DelayTimeSetting.class);
        DelayTimeSetting delayTimeSetting = list.stream().filter(k -> k.getType().equals(type)).findFirst().get();

        return MapUtil.getInteger(getRandom(delayTimeSetting.getMin(), delayTimeSetting.getMax()), 0);
    }

    private Long getRandom(long x, long y) {
        long num = -1;
        //说明：两个数在合法范围内，并不限制输入的数哪个更大一些
        if (x < 0 || y < 0) {
            return num;
        } else {
            long max = Math.max(x, y);
            long min = Math.min(x, y);
            long mid = max - min;//求差
            //产生随机数
            num = (long) (Math.random() * (mid + 1)) + min;
        }
        return num;
    }

    /**
     * 机器人用户处理
     *
     * @param activity
     * @param activityUser
     * @param userEntity
     * @param inviterUserName
     */
    public boolean handleActivityRobotUser(ZnsRunActivityEntity activity, ZnsRunActivityUserEntity activityUser, ZnsUserEntity userEntity, String inviterUserName) {
        //查看机器人是否行程冲突
        ZonedDateTime selectStartTime = activity.getActivityStartTime().minusMinutes(30);
        ZonedDateTime selectEndTime = activity.getActivityStartTime().plusMinutes(30);
        List<ZnsRunActivityUserEntity> userEntityList = getInProgressActivityUser(activityUser.getUserId().intValue(), selectStartTime, selectEndTime);

        try {
            if (CollectionUtils.isEmpty(userEntityList)) {
                return handleUserActivityStateAndNotification(activity, 1, userEntity, null, null, inviterUserName, true);
            }
        } catch (Exception e) {
            log.error("handleActivityRobotUser 处理失败，e:{}", e);
        }

        return true;
    }


    /**
     * 机器人接受处理并通知管理员
     *
     * @param activity
     * @param userStatus
     * @param user
     * @param password
     * @param runningGoals
     * @param inviterUserName
     * @param immediatelyAdmission
     */
    public boolean handleUserActivityStateAndNotification(ZnsRunActivityEntity activity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, String inviterUserName, boolean immediatelyAdmission) {
        HandleActivityRequest handleActivityRequest = new HandleActivityRequest();
        handleActivityRequest.setAppVersion(260);
        Result result = activityStrategyContext.handleUserActivityState(activity.getId(), userStatus, user, password, runningGoals, immediatelyAdmission, null, handleActivityRequest, false);
        if (CommonError.SUCCESS.getCode().equals(result.getCode())) {
            messageService.successActivityNotification(user, inviterUserName, activity);
            return true;
        } else {
            log.info("机器人接受失败，result：" + result);
            // messageService.failActivityNotification(user, inviterUserName, activity, result.getMsg());
        }
        return false;
    }

    public List<ZnsRunActivityUserEntity> getInProgressActivityUser(Integer id, ZonedDateTime selectStartTime, ZonedDateTime selectEndTime) {
        List<Integer> ids = new ArrayList<>();
        ids.add(id);
        return runActivityUserService.getInProgressActivityUser(ids, selectStartTime, selectEndTime);
    }


    public String defeatPreventionMechanism(Long userId, Long activityId, UserMatchModeRecord matchModeRecord) {
        //判断是否触发
        String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.DEFEAT_CONFIG.getCode());
        if (!StringUtils.hasText(config)) {
            log.info("defeatPreventionMechanism ------2");
            return "";
        }
        //xx天内连续x次
        ZonedDateTime now = ZonedDateTime.now();
        Map<String, Object> jsonObject = JsonUtil.readValue(config);
        Integer day = MapUtil.getInteger(jsonObject.get("day"));
        Integer failSession = MapUtil.getInteger(jsonObject.get("failSession"));

        List<LastActivityUserDto> list = znsRunActivityUserService.selectLastActivityUser(userId, RunActivityTypeEnum.CHALLENGE_RUN.getType(), 1, failSession, activityId);
        if (CollectionUtils.isEmpty(list)) {
            log.info("defeatPreventionMechanism ------1");
            return "";
        }

        long failCount = list.stream().filter(a -> DateUtil.betweenDay(a.getActivityStartTime(), now) <= MapUtil.getInteger(jsonObject.get("day")) && a.getActivityState().equals(ActivityStateEnum.FINISHED.getState()) && a.getRank() == 2).count();
        if (failCount < failSession) {
            log.info("defeatPreventionMechanism ------failCount=" + failCount + "|failSession=" + failSession);
            return "";
        }
        //查询xx天内平均速度
        ZonedDateTime startTime = DateUtil.addDays(now, -day);
        Integer averagePace = userRunDataDetailsService.selectAveragePace(userId, startTime);
        if (Objects.isNull(averagePace)) {
            log.info("defeatPreventionMechanism ------4");
            return "C";
        }

        log.info("defeatPreventionMechanism ------5 averagePace=" + averagePace);
        String mode = robotRunModeService.selectRobotRunModeByPave(averagePace);
        if (StringUtil.isEmpty(mode)) {
            mode = "E1";
        }
        matchModeRecord.setTriggerSource(1);
        matchModeRecord.setRobotRunMode(mode);
        userMatchModeRecordService.insertUserMatchModeRecord(matchModeRecord);
        return mode;
    }

    public ActivityCheckVo checkWaitTime(Long activityId) {
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
        if (Objects.isNull(activityEntity)) {
            throw new BaseException(ActivityError.ACTIVITY_NOT_EXIST.getMsg(), ActivityError.ACTIVITY_NOT_EXIST.getCode());
        }
        ActivityCheckVo checkVo = new ActivityCheckVo();
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
            checkVo.setWithinWaitTime(true);
            // 官方多人同跑允许进入  该比赛在开始活动时间-候场时间 至 开始活动时间+最后入场时间内
            long nowTime = ZonedDateTime.now().toInstant().toEpochMilli();
            Map<String, Object> activityConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
            Integer teamRunLastEnter = MapUtil.getInteger(activityConfig.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
            Integer runBeforeEnter = MapUtil.getInteger(activityConfig.get(ApiConstants.ACTIVITY_BEFORE_ENTER));
            if ((Objects.nonNull(teamRunLastEnter) && nowTime > activityEntity.getActivityStartTime().toInstant().toEpochMilli() + teamRunLastEnter * 60000)
                    || (Objects.nonNull(runBeforeEnter) && nowTime < activityEntity.getActivityStartTime().toInstant().toEpochMilli() - runBeforeEnter * 60000)) {
                checkVo.setWithinWaitTime(false);
            }
        }
        return checkVo;
    }


    //检测用户参与类型活动的次数
    public Result checkEnrollCount(Long userId, Long matchUserId, Integer activityType, Integer activityTypeSub, Boolean isCheckMySelf) {

        Integer dailyCount = znsRunActivityUserService.findDailyCountByUserAndActivityType(userId, activityType, activityTypeSub);
        if (!Objects.equals(activityType, 2)) {
            activityTypeSub = null;
        }
        if ((RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityType)
                || RunActivityTypeEnum.TEAM_RUN.getType().equals(activityType)) && userIdentityService.isNpc(userId)) {
            return null;
        }
        ZnsRunActivityConfigEntity configEntity = runActivityConfigService.getByType(activityType, activityTypeSub);

        Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
        Integer maxDailyCount = MapUtil.getInteger(jsonObject.get("maxDailyParticipations"));
        Integer maxDailyPartnerCount = MapUtil.getInteger(jsonObject.get("maxDailyPartnerParticipations"));
        if (maxDailyCount - dailyCount <= 0) {
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.enroll.daily.failed"));
        }
        if (matchUserId != null) {
            Integer dailyOtherCount = znsRunActivityUserService.findDailyCountByUserAndActivityType(matchUserId, activityType, activityTypeSub);
            Integer dailyPartnerCount = znsRunActivityUserService.findDailyPartnerCountByUserAndActivityType(userId, matchUserId, activityType, activityTypeSub);
            if (isCheckMySelf) {
                if (maxDailyCount - dailyOtherCount <= 0) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("activity.enroll.daily.other.failed"));
                }
            }
            if (maxDailyPartnerCount - dailyPartnerCount <= 0) {
                return CommonResult.fail(I18nMsgUtils.getMessage("activity.enroll.daily.Partner.failed"));
            }
        }
        return null;
    }

    public Result challengeAndDecreaseScore(CanChallengeRequest request, ZnsUserEntity loginUser) {
        Long activityId = request.getActivityId();
        Long challengedUserId = request.getChallengedUserId();
        ZnsRunActivityUserEntity challengedUser = runActivityUserService.findActivityUser(activityId, challengedUserId);

        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
//        //排行赛当前用户名次
//        Integer currentRank = runActivityUserService.getCurrentRank(activityId, activityEntity.getCompleteRuleType(), loginUser.getId());

        //扣积分
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        int challengeScoreConsume = MapUtil.getInteger(jsonObject.get(ApiConstants.CHALLENGE_SCORE_CONSUME), 0);
        //校验积分是否充足
        Integer allUserScore = userService.getAllUserScore(loginUser.getId());
        if (challengeScoreConsume > allUserScore) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.challenged.points.expired"));
        }
        if (loginUser.getId().equals(request.getChallengedUserId())) {

            activityUserScoreService.useActivityUserScore(challengeScoreConsume, activityId, loginUser.getId(), -1);
        } else {
            activityUserScoreService.useActivityUserScore(challengeScoreConsume, activityId, loginUser.getId(), challengedUser.getRank());
        }
        // 记录积分扣减详情日志
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setUserId(loginUser.getId());
        activityUserScore.setScore(challengeScoreConsume);
        activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_13.getType());
        activityUserScore.setStatus(2);
        activityUserScore.setExpireTime(ZonedDateTime.now());
        activityUserScore.setExchangeTime(ZonedDateTime.now());
        activityUserScore.setUseScore(challengeScoreConsume);
        activityUserScore.setIncome(-1);
        activityUserScore.setType(4);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setAwardTime(ZonedDateTime.now());
        activityUserScore.setExtraScore(0);
        activityUserScoreService.save(activityUserScore);
        return CommonResult.success();

    }

    public void createShadowData(ZnsRunActivityEntity znsRunActivityEntity, String batchNo) {
        // 拿到被挑战者的id
        PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(batchNo, 0);
        List<PkChallengeRecord> pkChallengeRecords = pkChallengeRecordService.selectPkChallengeRecordByBatchNo(batchNo);
        for (PkChallengeRecord pk : pkChallengeRecords) {
            pk.setActivityId(znsRunActivityEntity.getId());
            pk.setStatus(1);
            pkChallengeRecordService.updatePkChallengeRecordById(pk);
        }
        log.info("开始复制和设置数据 ");
        ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = userRunDataDetailsService.findById(pkChallengeRecord.getRunDataDetailsId());
        ZnsRunActivityUserEntity znsRunActivityUserEntity = runActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), pkChallengeRecord.getUserId());
        znsRunActivityUserEntity.setRunDataDetailsId(znsUserRunDataDetailsEntity.getId());
        znsRunActivityUserEntity.setUserState(4);
        runActivityUserService.updateById(znsRunActivityUserEntity);        // 复制znsRunActivityUserEntity 数据
    }

    /**
     * 是否可挑战用户
     *
     * @param request
     * @param loginUser
     * @return
     */
    @Transactional
    public Result<CanChallengeResp> canChallenge(CanChallengeRequest request, ZnsUserEntity loginUser) {

        Long activityId = request.getActivityId();
        Long challengedUserId = request.getChallengedUserId();
        ZnsRunActivityUserEntity challengedUser = null;

        if (null != challengedUserId && challengedUserId > 0) {
            challengedUser = runActivityUserService.findActivityUser(activityId, challengedUserId);
            if (null == challengedUser) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.challenged.not.exist"));
            }
        }
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);

        //校验积分是否充足
        Integer allUserScore = userService.getAllUserScore(loginUser.getId());
        Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        int challengeScoreConsume = MapUtil.getInteger(new BigInteger(MapUtil.getString(jsonObject.get(ApiConstants.CHALLENGE_SCORE_CONSUME))), 0);
        if (challengeScoreConsume > allUserScore) {
            return CommonResult.fail(996, I18nMsgUtils.getMessage("activity.challenged.not.points"));
        } else {
            //构建返回值
            CanChallengeResp canChallengeResp = new CanChallengeResp();
            Map<String, Object> challengeAwardScoreObject = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_SCORE_AWARD);
            Map<String, Object> challengeAwardCouponObject = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_COUPON_AWARD);
            Map<String, Object> challengeAwardAmountObject = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_AWARD);

            if (challengeAwardScoreObject != null) {
                String scoreNum = OfficialRankingActivityStrategy.officialChallengeSuccessAwardScoreAmount(request.getChallengeRank(), challengeAwardScoreObject,
                        loginUser.getId().equals(challengedUserId));
                if (scoreNum != null) {
                    canChallengeResp.setScore(Integer.valueOf(scoreNum));
                }
            }
            if (challengeAwardAmountObject != null) {
                BigDecimal awardAmount = OfficialRankingActivityStrategy.officialChallengeSuccessAwardAmount(request.getChallengeRank(), challengeAwardAmountObject,
                        loginUser.getId().equals(challengedUserId));
                if (awardAmount != null) {
                    canChallengeResp.setAmount(awardAmount);
                }
            }
            if (challengeAwardCouponObject != null) {
                ActivityCouponDto activityCouponDto = OfficialRankingActivityStrategy.officialChallengeSuccessAwardCouponAmount(request.getChallengeRank(), challengeAwardCouponObject,
                        loginUser.getId().equals(challengedUserId));
                if (activityCouponDto != null) {
                    Coupon coupon = couponService.selectCouponById(activityCouponDto.getCouponId());
                    if (coupon != null) {
                        canChallengeResp.setCouponName(coupon.getTitle());
                    }

                }
            }


            return CommonResult.success(canChallengeResp);

        }
    }

    /**
     * check
     *
     * @param matchFriendId
     * @return
     */
    public boolean checkFriendMatchUserIds(Long matchFriendId) {
        // 社区官方账号
        String configValue = sysConfigService.selectConfigByKey("community.content.rot");
        List<Long> botIds = JsonUtil.readList(configValue, Long.class);
        // 剔除老板账号
        String s = sysConfigService.selectConfigByKey(CeoRobotIncreaseConfig.CONFIG_KEY_USER_ID);
        if (!matchFriendId.equals(Long.valueOf(s)) && botIds.contains(matchFriendId)) {
            return true;
        }
        // im 官方账号
        String systemAccountList = sysConfigService.selectConfigByKey("im.system.account");
        List<String> list = JsonUtil.readList(systemAccountList, String.class);
        if (list.contains(String.valueOf(matchFriendId))) {
            return true;
        }
        //获取特殊账号
        String config = sysConfigService.selectConfigByKey("pitpat.im.system");
        List<String> userIds = StringUtil.splitToList(config, ",");
        if (userIds.contains(matchFriendId)) {
            return true;
        }
        return false;
    }

    public void addRunActivityUsers(ZnsRunActivityEntity activity, List<Long> activityUserIds, Long initiatorUserId, Long activityTeamId) {
        List<ZnsUserEntity> userEntities = userService.findByIds(activityUserIds);
        Map<Long, ZnsUserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
        for (int i = 0; i < activityUserIds.size(); i++) {
            Long userId = activityUserIds.get(i);
            log.info("开始添加用户{}", userId);
            ZnsUserEntity userEntity = userEntityMap.get(userId);
            if (null == userEntity) {
                continue;
            }
            ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
            activityUser.setActivityId(activity.getId());
            activityUser.setUserId(userId);
            activityUser.setIsRobot(userEntity.getIsRobot());
            activityUser.setIsTest(userEntity.getIsTest());

            activityUser.setNickname(userEntity.getFirstName());
            activityUser.setActivityType(activity.getActivityType());
            //直接接受
            activityUser.setUserState(1);
            // 活动参与者
            activityUser.setUserType(2);
            if (null != initiatorUserId) {
                activityUser.setInviterUserId(initiatorUserId);
            }
            if (Objects.nonNull(activity.getRunMileage())) {
                activityUser.setTargetRunMileage(activity.getRunMileage().intValue());
            }
            if (Objects.nonNull(activity.getRunTime())) {
                activityUser.setTargetRunTime(activity.getRunTime());
            }
            activityUser.setTeamId(activityTeamId);
            znsRunActivityUserService.save(activityUser);
        }
    }

    public void addRobot(ZnsRunActivityEntity znsRunActivityEntity, ZonedDateTime runDate, String runMode) {
        ZnsUserEntity shortageRobotUser = userService.getShortageRobotUser(runMode, znsRunActivityEntity);
        log.info("排行赛添加机器人 activityId = " + znsRunActivityEntity.getId() + ",userId=" + shortageRobotUser.getId());
        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), shortageRobotUser.getId());
        // 如果run_activity_user 为空，则报名
        if (znsRunActivityUserEntity == null) {
            handleUserActivityStateAndNotification(znsRunActivityEntity, 1, shortageRobotUser, null, znsRunActivityEntity.getRunMileage().intValue(), "", false);
        }
        mindUserMatchBizService.addRobotMindUserMatch(shortageRobotUser.getId(), znsRunActivityEntity.getId(), znsRunActivityEntity.getRunMileage(), runMode, znsRunActivityEntity.getActivityEndTime());
    }
}
