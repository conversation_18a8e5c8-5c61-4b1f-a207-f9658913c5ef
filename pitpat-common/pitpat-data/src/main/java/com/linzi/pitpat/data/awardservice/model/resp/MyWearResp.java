package com.linzi.pitpat.data.awardservice.model.resp;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class MyWearResp extends Wears {


    //套装包含服装类型.1:发色（头发、帽子）,2:肤色,3.头型,4:脸部服饰（眼镜）,5:上衣,6:裤子,7:鞋子,8:套装
    private List<Integer> attireTypeIncludedList;

    /**
     * 服装获取状态 1：已获得，2：积分兑换，3：活动获取
     */
    private Integer wearSource;
    /**
     * 所需积分（wearSource = 2时存在）
     */
    private Long exchangeScore;
    /**
     * 所需金额（wearSource = 2 时存在）
     */
    private BigDecimal exchangeAmount;

    private Currency currency;

    //积分兑换的规则ID ruleId
    private Long ruleId;
    /**
     * 活动获取的ID(wearSource = 3时存在)
     */
    private Long obtainActivityId;
    /**
     * 活动跳转url
     */
    private String activityJumpUrl;
    /**
     * 活动跳转参数
     */
    private String activityJumpParam;
    //活动开始时间
    private ZonedDateTime startTime;
    //活动创建时间
    private ZonedDateTime creatTime;
    //过期类型 0:非永久，1：永久
    private Integer expireType;
    //过期天数(兑换时显示)
    private Integer expiredTime;
    //过期时间
    private ZonedDateTime expireDay;
    /**
     * 活动类型
     *
     * @see MainActivityTypeEnum
     */
    private String mainType;

    //服装兑换经验等级限制
    private Integer wearExpLevelLimit;

    @Override
    public boolean equals(Object o) {
        Wears wears = (Wears) o;
        return Objects.equals(getWearType(), wears.getWearType()) && Objects.equals(getWearId(), wears.getWearId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getWearType(), getWearId());
    }

}
