package com.linzi.pitpat.data.courseservice.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 课程动作表
 *
 * <AUTHOR>
 * @date 2021-10-08 16:19:22
 */
@TableName("zns_course_action")
@Data
@NoArgsConstructor
public class ZnsCourseActionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 最后修改时间
     */
    private ZonedDateTime modifyTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人
     */
    private String modified;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 动作名称
     */
    private String actionName;
    /**
     * 课程时长（s）
     */
    private Integer actionDuration;
    /**
     * 休息（s）
     */
    private Integer restDuration;
    /**
     * 阶段类型，1:热身阶段，2:锻炼阶段，3:调整阶段
     */
    private Integer stageType;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 坡度/阻力挡位
     */
    private Integer gradient;
    /**
     * 速度 米/小时
     */
    private BigDecimal velocity;
    /**
     * 加速缓存时间
     */
    private Integer speedUpTime;
    /**
     * 是否记录训练时长，1：是，0：否
     */
    private Integer isRecordTime;
    /**
     * 动作图
     */
    private String actionPicture;
    @TableField(exist = false)
    private Integer timeCoordinate;

    //语言名称
    private String languageName;
    /**
     * 语言code，en_US：英语，fr_CA ：法语，de_DE：德语，it_IT：意大利语，es_ES：西班牙语，pt_PT：葡萄牙语
     *
     * @see I18nConstant.LanguageCodeEnum
     */
    private String languageCode;
}
