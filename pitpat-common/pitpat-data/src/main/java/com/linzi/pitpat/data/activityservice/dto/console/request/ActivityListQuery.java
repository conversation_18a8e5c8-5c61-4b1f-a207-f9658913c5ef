package com.linzi.pitpat.data.activityservice.dto.console.request;

import com.linzi.pitpat.data.annotation.Compare;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class ActivityListQuery extends PageQuery {
    //开始时间
    private ZonedDateTime activityStartTime;
    //结束时间
    private ZonedDateTime activityEndTime;
    //活动id
    @Compare(compareType = "eq", columnName = "id")
    private Long activityId;
    //活动编号
    @Compare(compareType = "eq")
    private String activityNo;
    //活动名称
    private String activityTitle;
    //活动备注
    @Compare(compareType = "like")
    private String remark;
    //'活动类别1、官方活动2、主题活动 3、团队活动4、最新活动',
    @Compare(compareType = "eq")
    private Integer categoryType;
    //地区
    private List<Long> areaId;
    //活动状态  -1 未上架 0未开始 1一开始 2已结束 3下架
    @Compare(compareType = "eq")
    private Integer activityState;
    //玩法
    @Compare(compareType = "eq")
    private Long playId;
    //是否自动创建0不是1是
    private Integer isAutoCreate;

    /**
     * 竞技赛类型
     *
     * @see com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveTypeEnum
     */
    private String competitiveType;

}
