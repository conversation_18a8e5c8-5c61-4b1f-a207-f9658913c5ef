package com.linzi.pitpat.data.awardservice.dto.api;

import com.linzi.pitpat.data.awardservice.model.dto.ActivityUserScoreDto;
import com.linzi.pitpat.data.util.PPageUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class ScoreListByMonthDto {
    private PPageUtils<ActivityUserScoreDto> page;
    //总分数
    private int allScore;
    //积分规则
    private String runScoreRule;

    // 最后一条的时间
    private ZonedDateTime lastDateTime;


}
