package com.linzi.pitpat.data.activityservice.dto.api.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/19 11:36
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ShareReportResponseDto {

    /**
     * 设备类型（0跑步机，1单车，2脚踏车，3划船机）
     */
    private Integer deviceType;
    //图片列表
    private List<String> imageList;
    //排名
    private int rank;
    //报告标题
    private String title;
    /**
     * 报告生成日期
     *
     * @mock @timestamp
     */
    private ZonedDateTime reportDate;
    //跑步里程(m)
    private BigDecimal runMileage = BigDecimal.ZERO;
    //平均配速(秒/公里)
    private int averagePace;
    /**
     * 平均踏频，桨频
     */
    private BigDecimal averageTreadFrequency;

    /**
     * 圈数，桨次
     */
    private Integer rotateNum;
    //运动时长
    private int runTime;
    //千卡路里
    private BigDecimal kilocalorie = BigDecimal.ZERO;
    /**
     * 段位等级名称
     */
    private String levelName;
    /**
     * 新活动类型
     * 需要先区分新活动类型
     * rank排位赛 prop 道具赛 mainActivity竞技赛 user 用户赛 freeRun 自由运动 pacePk 配速PK targetRun 目标跑 courseRun 课程跑
     * normalRun 普通跑，无模式 other其他v3.0之前历史数据，官方赛（排行赛、里程碑、主题活动等）和非官方赛（用户赛等）
     */
    private String reportMainType;
    //昵称
    private String nickname;
    //头像
    private String headPortrait;
    /**
     * 爬坡距离（m） 修改精度 实际保存的 是cm 精度为了计算保留更准确的数值
     */
    private BigDecimal climbingMileage;
    /**
     * 扬升最大坡度
     */
    private Integer maximumLiftingGradient;
}
