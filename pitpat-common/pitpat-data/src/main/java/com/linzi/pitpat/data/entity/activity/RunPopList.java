package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*跑步弹幕列表
 *
 * <AUTHOR>
 * @since 2022-06-29
 */

@Data
@NoArgsConstructor
@TableName("zns_run_pop_list")
public class RunPopList implements java.io.Serializable {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //用户id
    private Long userId;
    //内容
    private String content;
    //昵称
    private String nickName;
    //头像
    private String headPortrait;
    //活动id
    private Long activityId;

    @Override
    public String toString() {
        return "RunPopList{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",userId=" + userId +
                ",content=" + content +
                ",nickName=" + nickName +
                ",headPortrait=" + headPortrait +
                ",activityId=" + activityId +
                "}";
    }
}
