package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.enums.MDEventTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>*埋点记录表
 *
 * <AUTHOR>
 * @since 2022-06-29
 */

@Data
@NoArgsConstructor
@TableName("zns_maidian_log")
public class MaidianLog implements java.io.Serializable {
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //app用户id
    private Long userId;
    /**
     * 事件类型 运动中打开app
     * {@link MDEventTypeEnum}
     */
    private String eventType;
    //关联id
    private Long refId;
    //关联类型，1：运动数据id
    private Integer refType;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 设备号，唯一标识
     */
    private String uuid;
    /**
     * 手机型号/名称
     */
    private String deviceName;
    /**
     * 页面地址
     */
    private String page;
    /**
     * 按钮
     */
    private String button;
    /**
     * 分享用户id
     */
    private Long invitationUserId;
    /**
     * 手机系统版本号
     */
    private String systemVersion;


    private String rmtIp;       //      远程ip


    private String appVersion;      //app版本


    private String appType;     //app类型


    private String keyValue;                //功能


    private String remark;                  //备注


    private Long activityId;        //活动id

    private Integer activityType; // 活动类型
    //
    private Integer activityState; //活动状态

    private Integer userState;

    private String runPersonType;//rchar(255) NULL COMMENT '选择保证金的业务类型：随机匹配、好友PK、非官方多人同跑' AFTER `user_state`,
    private Long routeId;//` int(11) NULL COMMENT '路线id' AFTER `run_person_type`,
    private ZonedDateTime activityStartTime;//` varchar(255) NULL COMMENT '非官方赛事开始时间' AFTER `route_id`,
    private Integer completeRuleType;
    /// ` varchar(64) NULL COMMENT '选择的目标类型 1：里程 2：时间' AFTER `activity_start_time`,
    private BigDecimal runMileage;//` varchar(64) NULL COMMENT '选择的跑步里程' AFTER `complete_rule_type`,
    private String bonusRuleType;//` varchar(32) NULL COMMENT '是否免费' AFTER `run_mileage`,
    private BigDecimal activityEntryFee;//` decimal(18, 6) NULL COMMENT '保证金金额' AFTER `bonus_rule_type`,
    private String activityUserIds;//` varchar(255) NULL COMMENT '邀请的好友ID' AFTER `activity_entry_fee`,
    private String isPublic;//` varchar(32) NULL COMMENT '是否公开' AFTER `activity_user_ids`,
    private String fromType;//` varchar(64) NULL COMMENT '连接设备页的入口' AFTER `is_public`;
    private String activityTitle; // 活动标题
    private String requestUrl; //请求api
    private String h5Url;//H5链接
    private Integer isPoly;//是否聚合活动
    private Integer isComplete;//是否完赛
    /**
     * 国家
     */
    private String country;


    @Override
    public String toString() {
        return "MaidianLog{" +
                ",id=" + id +
                ",gmtCreate=" + gmtCreate +
                ",userId=" + userId +
                ",eventType=" + eventType +
                ",refId=" + refId +
                ",refType=" + refType +
                "}";
    }
}
