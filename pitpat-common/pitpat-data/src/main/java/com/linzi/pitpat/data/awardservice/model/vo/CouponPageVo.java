package com.linzi.pitpat.data.awardservice.model.vo;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/26 15:54
 */
@Data
@NoArgsConstructor
public class CouponPageVo {
    /**
     * 用户卷id
     */
    protected Long userCouponId;
    //标题
    protected String title;

    protected ZonedDateTime gmtCreate;
    //有效截至时间
    protected ZonedDateTime gmtEnd;
    /**
     * 券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券,6 进阶里程碑券,7独立站抵扣券，100：商城优惠券】
     *
     * @see CouponTypeEnum
     */
    protected Integer couponType;
    //使用规则
    protected String description;
    //券金额
    protected BigDecimal amount;
    //券折扣
    protected BigDecimal discount;
    //状态，0:未使用 ， 1:使用中 2:已使，3:过期 4:已失效
    protected Integer status;
    // 新获取卷标识 1 是新的 0 不是新的
    protected Integer isNew;
    /**
     * 卷跳转路由
     */
    protected String route;
    /**
     * 路由參數 json
     */
    protected String routeParam;
    /**
     * 卷配置Id
     */
    protected Long couponId;
    /**
     * 路由id
     */
    protected Long routeId;

    //是否3.0赛事分类（0：不是，1：是）
    protected Integer isActivityCategory;

    /**
     * 快过期标志 1 快过期 0 非快过期 isNew = 1 以new 为准
     */
    protected Integer expiringSoon;

    /**
     * 币种
     */
    protected Currency currency;
    /**
     * 券默认语言
     */
    protected String defaultLangCode;

    /**
     * 【4.4.3新增】优惠券主类型  1：赛事券，2：商城券
     *
     * @see CouponConstant.CouponMainTypeEnum
     */
    protected Integer couponMainType;

}
