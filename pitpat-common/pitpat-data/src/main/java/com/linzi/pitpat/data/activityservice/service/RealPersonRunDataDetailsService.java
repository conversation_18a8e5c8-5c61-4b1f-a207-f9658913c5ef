package com.linzi.pitpat.data.activityservice.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RealUserRunDataDetailsQuery;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentRunDataVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentRunSumCountVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentRunDataVo;
import com.linzi.pitpat.data.userservice.dto.response.UserBestPaceDto;
import com.linzi.pitpat.data.userservice.dto.response.UserRunTotalDto;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.vo.UserAvgDataDto;
import com.linzi.pitpat.data.userservice.model.vo.UserEventPrefDto;
import com.linzi.pitpat.data.vo.UserSimpleVo;

import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 真人用户跑步详情表 服务类
 *
 * @since 2024-04-25
 */
public interface RealPersonRunDataDetailsService {

    RealPersonRunDataDetails findById(Long id);

    int insert(RealPersonRunDataDetails realPersonRunDataDetails);

    int update(RealPersonRunDataDetails realPersonRunDataDetails);

    int deleteById(Long id);

    void batchInsert(List<RealPersonRunDataDetails> list);

    //用户时间范围内运动记录
    Page<RealPersonRunDataDetails> findRecordByTime(Page page, Long userId, ZonedDateTime startTime, ZonedDateTime endTime, List<Integer> deviceTypes);

    List<RealPersonRunDataDetails> findListByTime(Long userId, ZonedDateTime startTime, ZonedDateTime endTime, List<Integer> deviceTypes);

    List<RealPersonRunDataDetails> findListByActivity(Long userId, Long activityId);

    RealPersonRunDataDetails findByDetailsId(Long detailsId);

    void updateRealPersonDetails(ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity);

    // 1英里&&1600m 最佳配速
    UserSimpleVo getUserBestPaceByRunMileage(Long userId, Integer runMileage, List<Integer> deviceTypeList);

    List<UserBestPaceDto> findBestPaceListByUser(Long userId, Integer deviceType);

    /**
     * 用户所有的运动里程
     *
     * @param userId
     * @param maxTime
     * @param deviceType
     * @return
     */
    Long sumAllRunMileage(Long userId, ZonedDateTime maxTime, Integer deviceType);

    /**
     * 用户所有的运动时间
     *
     * @param userId
     * @return
     */
    Long sumAllRunTime(Long userId);

    Long continuousRunDays(Long userId, ZonedDateTime createTime, String zoneId, Integer deviceType);

    UserRunTotalDto sumAllRunTimeAndMileage(Long userId, List<Integer> deviceTypes);


    /**
     * 查询设备历史跑步数据
     */
    EquipmentRunDataVo findEquipmentRunDataVoByTreadmillId(Long treadmillId, ZonedDateTime createTime);

    /**
     * 批量查询设备历史跑步数据
     */
    EquipmentRunDataVo findEquipmentRunDataVoByTreadmillIds(List<Long> treadmillIds, ZonedDateTime createTime);

    List<RealPersonRunDataDetails> findList(RealUserRunDataDetailsQuery query);

    List<RealPersonRunDataDetails> findByIdList(List<Long> idList);

    /**
     * 获取用户运动数据
     */
    List<UserEquipmentRunDataVo> findUserEquipmentRunDataByTime(ZonedDateTime startDate);

    EquipmentRunSumCountVo findSumCountTotalByEquipmentId(Long equipmentId);

    RealPersonRunDataDetails selectByUserIdAverageVelocityActivityType(Long userId, Integer activityType);

    UserAvgDataDto findUserAverageDurationAndAveragePace(Long userId);

    Integer findSumCountByUserIdLastWeek(Long userId);

    List<UserEventPrefDto> findUserLastWeekActivityTypeCount(Long userId);

    List<Integer> findUserLastTenPkData(Long userId);

    /**
     * 查询用户设备跑步数据
     */
    List<EquipmentRunDataVo> findUsersEquipmentRunDataVoByTreadmillIds(List<Long> treadmillIds, Long id);

	List<RealPersonRunDataDetails> findListWithZeroCalorie(Long userId, ZonedDateTime startTime, ZonedDateTime endTime,List<Integer> deviceTypes, List<String> neProductCodeList);
}
