package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 用户勋章 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.awardservice.constant.enums.MedalConstant;
import com.linzi.pitpat.data.awardservice.mapper.MedalConfigDao;
import com.linzi.pitpat.data.awardservice.mapper.UserMedalDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.constants.CacheConstants;
import com.linzi.pitpat.data.engine.medal.BaseMedalHandler;
import com.linzi.pitpat.data.engine.medal.MedalParamDto;
import com.linzi.pitpat.data.entity.dto.UserMedalDto;
import com.linzi.pitpat.data.enums.SocketEventEnums;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
public class UserMedalServiceImpl implements UserMedalService {


    @Autowired
    private UserMedalDao userMedalDao;

    @Autowired
    private MedalConfigDao medalConfigDao;
    @Autowired
    private SocketPushUtils socketPushUtils;
    @Autowired
    private RedissonClient redissonClient;

    @Override
    public UserMedal selectUserMedalById(Long id) {
        return userMedalDao.selectUserMedalById(id);
    }

    @Override
    public int updateUserMedalById(UserMedal userMedal) {
        return userMedalDao.updateUserMedalById(userMedal);
    }

    //同一用户5分钟内不重复触发
    @DataCache
    public void initMedal(Long userId) {
        List<UserMedal> userMedalList = userMedalDao.selectUserMedalByUserId(userId, 0);
        List<Long> configId = new ArrayList<>();
        configId.add(0l);
        for (UserMedal userMedal : userMedalList) {
            configId.add(userMedal.getMedalConfigId());
        }
        List<MedalConfig> medalConfigs = medalConfigDao.selectMedalConfigByUserMedalId(configId);
        for (MedalConfig medalConfig : medalConfigs) {
            if (MedalConstant.MedalTypeEnum.type_7.getType().equals(medalConfig.getType())) {
                //赛事勋章不初始化
                continue;
            }
            createUserMedal(userId, medalConfig);
        }
    }

    @Override
    public void deliverMedal(ZnsUserEntity user, Long activityId, Integer isFinished, ActivityTypeDto activityNew) {
        String key = CacheConstants.MEDAL_KEY_ + activityId + "_" + user.getId();
        RLock lock = redissonClient.getLock(key);

        try {
            if (LockHolder.tryLock(lock, 60, 60)) {
                if (Objects.equals(user.getIsRobot(), 1)) {
                    log.info("机器人: {}, 开始发放活动: {} 勋章， 是否完成活动: {}", user.getId(), activityId, isFinished);
                }
                initMedal(user.getId());
                Map<Long, MedalConfig> medalConfigMap = medalConfigDao.selectAllMedalConfig();
                List<UserMedal> userMedals = userMedalDao.selectUserMedalByUserIdIsValidObtainEndTrigger(user.getId(), 0, 0, isFinished == 1 ? null : isFinished);
                //判断机器人是否需要更换佩戴的勋章的标志，如果在本次活动中获得了新的勋章，就需要更换
                boolean robotWearingNewMedal = false;
                List<UserMedal> updateUserMedals = new ArrayList<>();
                for (UserMedal userMedal : userMedals) {
                    UserMedal oldUserMedal = new UserMedal();
                    BeanUtils.copyProperties(userMedal, oldUserMedal);

                    BaseMedalHandler baseMedalHandler = SpringContextUtils.getBean(userMedal.getHandler(), BaseMedalHandler.class);
                    MedalConfig medalConfig = medalConfigMap.get(userMedal.getMedalConfigId());
                    if (Objects.isNull(medalConfig)) {
                        continue;
                    }
                    Pair<Boolean, UserMedal> pair = baseMedalHandler.deliverMedal(new MedalParamDto(user.getId(), medalConfig.getId(), activityId, medalConfig, userMedal, activityNew));
                    userMedal = pair.getValue();
                    if (userMedal != null) {
                        setTarget(medalConfig, userMedal);
                        if (!oldUserMedal.equals(userMedal)) {
                            updateUserMedals.add(userMedal);
                        }

                        if (pair.getKey()) {
                            robotWearingNewMedal = true;
                        }

                        if (pair.getKey() && Objects.equals(isFinished, 0) && Objects.equals(medalConfig.getEndTrigger(), 0)) { // 如果已经达成目标
                            Map<String, Object> data = new HashMap<>();
                            data.put("name", medalConfig.getName());
                            data.put("remark", medalConfig.getRemark());
                            data.put("userId", userMedal.getUserId());
                            data.put("url", medalConfig.getUrl());
                            log.info(" 已经达到目标勋章在跑道中弹窗,{} ", data);
                            socketPushUtils.push(NumberUtils.convertActivityId(activityId), SocketEventEnums.PUSH_MEDAL_MESSAGE.getCode(), data);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(updateUserMedals)) {
                    userMedalDao.updateById(updateUserMedals);
                }
                //开始为机器人随机佩戴最新的勋章
                robotWearMedals(robotWearingNewMedal, user);
                //循环结束
            } else {
                log.info("没有获得锁,{}", key);
            }
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("删除锁 " + key);
                lock.unlock();
            }
        }

    }

    /**
     * 机器人可以获取勋章，获取后，需要随机佩戴1～3个最新的勋章
     *
     * @param user
     */
    private void robotWearMedals(ZnsUserEntity user) {
        if (Objects.equals(user.getIsRobot(), 1)) {
            log.info("机器人:{} 随机佩戴勋章", user.getId());
            //先取消所有显示
            updateUserMedalIsHomePagePosByUserId(0, 10000, user.getId());

            List<UserMedal> obtainUserMedals = userMedalDao.selectUserMedalByUserIdObtainIsPop(user.getId(), 1, null, 0);
            //按照时间倒叙排列，佩戴最新获取的奖牌（1～3）个
            List<UserMedal> subObtainUserMedals = obtainUserMedals.stream().sorted(Comparator.comparingLong(UserMedal::getId).reversed())
                    .collect(Collectors.toList())
                    .subList(0, ThreadLocalRandom.current().nextInt(1, Math.min(obtainUserMedals.size() + 1, 4))); //防止边界溢出
            log.info("机器人:{} 随机佩戴勋章明细: {}", user.getId(), subObtainUserMedals.stream().map(UserMedal::getId));
            IntStream.range(0, subObtainUserMedals.size()).forEach(index -> {
                subObtainUserMedals.get(index).setPos(index + 1);
                subObtainUserMedals.get(index).setIsShowHomePage(1);
                updateUserMedalById(subObtainUserMedals.get(index));
            });
            log.info("机器人:{} 随机佩戴勋章佩戴完成", user.getId());
        }
    }

    /**
     * 机器人可以获取勋章，获取后，需要随机佩戴1～3个最新的勋章
     *
     * @param robotWearingNewMedal 是否获得新勋章
     * @param user                 机器人信息
     */
    public void robotWearMedals(boolean robotWearingNewMedal, ZnsUserEntity user) {
        if (Objects.equals(user.getIsRobot(), 1) && Objects.equals(robotWearingNewMedal, true)) {
            log.info("机器人:{} 随机佩戴勋章", user.getId());
            //先取消所有显示
            updateUserMedalIsHomePagePosByUserId(0, 10000, user.getId());

            List<UserMedal> obtainUserMedals = userMedalDao.selectUserMedalByUserIdObtain(user.getId(), 1, 0);
            //按照时间倒叙排列，佩戴最新获取的奖牌（1～3）个
            List<UserMedal> subObtainUserMedals = obtainUserMedals.subList(0, ThreadLocalRandom.current().nextInt(1, Math.min(obtainUserMedals.size() + 1, 4))); //防止边界溢出
            log.info("机器人:{} 可用勋章明细: {}", user.getId(), obtainUserMedals.stream().map(UserMedal::getId).collect(Collectors.toList()));
            IntStream.range(0, subObtainUserMedals.size()).forEach(index -> {
                subObtainUserMedals.get(index).setPos(index + 1);
                subObtainUserMedals.get(index).setIsShowHomePage(1);
                updateUserMedalById(subObtainUserMedals.get(index));
            });
            log.info("机器人:{} 随机佩戴勋章佩戴完成: {}", user.getId(), subObtainUserMedals.stream().map(UserMedal::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public List<MedalConfig> getMonthReportMedalImgs(Long userId, ZonedDateTime startTime, ZonedDateTime endTime) {
        return userMedalDao.selectMonthDataMedalImgs(userId, startTime, endTime);
    }

    @Override
    public void sendUserMedal(Long userId, Long medalId) {
        List<UserMedal> userMedals = userMedalDao.selectList(new QueryWrapper<UserMedal>().lambda().eq(UserMedal::getUserId, userId).eq(UserMedal::getMedalConfigId, medalId).eq(UserMedal::getIsDelete, 0));
        if (CollectionUtils.isEmpty(userMedals)) {
            MedalConfig medalConfig = medalConfigDao.selectMedalConfigById(medalId);
            UserMedal userMedalNew = new UserMedal();
            BeanUtils.copyProperties(medalConfig, userMedalNew);
            userMedalNew.setId(null);
            userMedalNew.setObtain(YesNoStatus.YES.getCode());
            userMedalNew.setUserId(userId);
            userMedalNew.setMedalConfigId(medalId);
            userMedalNew.setObtainTime(ZonedDateTime.now());
            userMedalDao.insertUserMedal(userMedalNew);
        } else {
            List<UserMedal> list = userMedals.stream().filter(s -> Objects.equals(s.getObtain(), 1)).toList();
            if (!CollectionUtils.isEmpty(list)) {
                return;
            }
            UserMedal userMedal = userMedals.get(0);
            userMedal.setObtain(YesNoStatus.YES.getCode());
            userMedal.setObtainTime(ZonedDateTime.now());
            userMedalDao.updateById(userMedal);
        }
    }

    @Override
    public UserMedal findByUserAndMedalId(Long userId, Long medalConfigId) {
        return userMedalDao.selectOne(new QueryWrapper<UserMedal>().eq("user_id", userId)
                .eq("medal_config_id", medalConfigId).eq("is_delete", 0)
                .last("limit 1"));
    }

    @Override
    public List<UserMedal> findRepeatAll() {
        return userMedalDao.findRepeatAll();
    }

    @Override
    public List<UserMedal> findRepeatAllV2() {
        return userMedalDao.findRepeatAll1();
    }

    /**
     * 获取用户已获得的勋章图片
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> findUserObtainMedal(Long userId, Integer limitNum) {
        return userMedalDao.findUserObtainMedal(userId, limitNum);
    }

    private void createUserMedal(Long userId, MedalConfig medalConfig) {
        UserMedal userMedal = new UserMedal();
        userMedal.setUserId(userId);
        userMedal.setType(medalConfig.getType());
        userMedal.setName(medalConfig.getName());
        userMedal.setLevel(medalConfig.getLevel());
        userMedal.setHandler(medalConfig.getHandler());
        userMedal.setRemark(medalConfig.getRemark());
        setTarget(medalConfig, userMedal);
        userMedal.setObtain(0);
        userMedal.setIsValid(0);
        userMedal.setMedalConfigId(medalConfig.getId());
        userMedal.setIsPop(0);
        userMedal.setPos(medalConfig.getPos());
        userMedal.setEndTrigger(medalConfig.getEndTrigger());
        userMedalDao.insertUserMedal(userMedal);
    }


    @Override
    public void setTarget(MedalConfig medalConfig, UserMedal userMedal) {
        String keys[] = medalConfig.getProcess().split(",");
        for (int i = 0; i < keys.length; i++) {
            if (i == 0) {
                userMedal.setTargetProcess1(MapUtil.getBigDecimal(getValue(medalConfig, keys[i]), BigDecimal.ZERO));
            } else if (i == 1) {
                userMedal.setTargetProcess2(MapUtil.getBigDecimal(getValue(medalConfig, keys[i]), BigDecimal.ZERO));
            }
        }
        userMedal.setProcessNum(keys.length);
    }

    @Override
    public List<UserMedal> selectUserMedalByUserIdObtainIsPop(Long userId, Integer obtain, Integer isPop, Integer isValid) {
        return userMedalDao.selectUserMedalByUserIdObtainIsPop(userId, obtain, isPop, isValid);
    }

    @Override
    public List<UserMedal> selectUserMedalByUserIdObtainIsPop(Long userId, Integer obtain, Integer isPop, Integer isValid, List<Long> medalConfigIds) {
        if (userId == null || CollectionUtils.isEmpty(medalConfigIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserMedal> eq = Wrappers.lambdaQuery(UserMedal.class)
                .eq(UserMedal::getUserId, userId)
                .eq(UserMedal::getObtain, obtain)
                .eq(UserMedal::getIsPop, isPop)
                .eq(UserMedal::getIsValid, isValid)
                .in(UserMedal::getMedalConfigId, medalConfigIds);
        return userMedalDao.selectList(eq);
    }

    @Override
    public void updateUserMedalIsPopByUserId(Integer isPop, Long id) {
        userMedalDao.updateUserMedalIsPopByUserId(isPop, id);
    }

    @Override
    public List<Map<String, Object>> selectHomePageList(Long userId) {
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<UserMedal> list = userMedalDao.selectList(new QueryWrapper<UserMedal>()
                .eq("user_id", userId)
                .eq("is_delete", 0)
                .eq("obtain", 1)
                .eq("is_valid", 0)
                .eq("is_show_home_page", 1)
                .orderByAsc("pos"));
        if (CollectionUtils.isEmpty(list)) {
            return listMap;
        }
        List<Long> configIds = list.stream().map(UserMedal::getMedalConfigId).collect(Collectors.toList());
        List<MedalConfig> medalConfigs = medalConfigDao.selectMedalConfigListByIds(configIds);
        Map<Long, MedalConfig> configMap = medalConfigs.stream().collect(Collectors.toMap(MedalConfig::getId, Function.identity()));

        for (UserMedal userMedal : list) {
            Map<String, Object> map = new HashMap<>();
            MedalConfig medalConfig = configMap.get(userMedal.getMedalConfigId());
            if (Objects.nonNull(medalConfig)) {
                map.put("url", medalConfig.getUrl());
            }
            map.put("id", userMedal.getId());
            map.put("name", userMedal.getName());
            listMap.add(map);
        }
        return listMap;
    }

    @Override
    @DataCache(value={"userId","type"})
    public UserMedal selectMostUserMedalByType(Long userId, Integer type) {
        return userMedalDao.selectOne(new QueryWrapper<UserMedal>().eq("user_id", userId)
                .eq("is_delete", 0)
                .eq("obtain", 1)
                .eq("is_valid", 0)
                .eq("type", type)
                .orderByDesc("level")
                .last("limit 1"));
    }

    @Override
    public void updateIsShow(Long userId) {
        UserMedal update = new UserMedal();
        update.setIsShow(1);
        update.setGmtModified(ZonedDateTime.now());

        userMedalDao.update(update, new QueryWrapper<UserMedal>()
                .eq("user_id", userId)
                .eq("obtain", 1)
                .eq("is_valid", 0)
                .eq("is_show", 0));
    }

    @Override
    public void updateUserMedalValid() {
        List<UserMedal> userMedals = userMedalDao.selectUserMedalValid(0, DateUtil.getStartOfDate(DateUtil.addDays(ZonedDateTime.now(), -2)), ZonedDateTime.now());
        Set<Long> userIds = new HashSet<>();
        for (UserMedal userMedal : userMedals) {
            userMedal.setIsValid(1);
            userMedalDao.updateUserMedalById(userMedal);
            userIds.add(userMedal.getUserId());
        }
        for (Long userId : userIds) {
            initMedal(userId);
        }
    }

    @Override
    public void updateUserMedalIsHomePagePosByUserId(Integer isShowHomePage, Integer pos, Long userId) {
        userMedalDao.updateUserMedalIsHomePagePosByUserId(ZonedDateTime.now(), isShowHomePage, pos, userId);
    }

    @Override
    public UserMedal selectUserMedalByConfigId(Long userId, Long demandMedalConfigId) {
        return userMedalDao.selectOne(new QueryWrapper<UserMedal>().eq("user_id", userId)
                .eq("medal_config_id", demandMedalConfigId).eq("is_delete", 0)
                .eq("is_valid", 0).eq("obtain", 1).last("limit 1"));
    }

    @Override
    public void convert(UserMedal userMedal) {
        if (Objects.equals(userMedal.getObtain(), 1)) {
            userMedal.setCurrentProcess1(userMedal.getTargetProcess1());
            userMedal.setCurrentProcess2(userMedal.getTargetProcess2());
        }
        if ("sumMileageMedalHandler".equals(userMedal.getHandler()) || "takeTotal30Avg6MedalHandler".equals(userMedal.getHandler())) {
            BigDecimal target = BigDecimalUtil.convert_miles(userMedal.getTargetProcess1(), new BigDecimal(1600));
            BigDecimal history = BigDecimalUtil.convert_miles(userMedal.getHistoryProcess1(), new BigDecimal(1600));
            BigDecimal current = BigDecimalUtil.convert_miles(userMedal.getCurrentProcess1(), new BigDecimal(1600));
            userMedal.setTargetProcess1(target);
            if (current.compareTo(target) > 0) {
                userMedal.setCurrentProcess1(target);
            } else {
                userMedal.setCurrentProcess1(current);
            }
            if (history.compareTo(target) > 0) {
                userMedal.setHistoryProcess1(target);
            } else {
                userMedal.setHistoryProcess1(history);
            }
            if ("takeTotal30Avg6MedalHandler".equals(userMedal.getHandler())) {
                BigDecimal target2 = BigDecimalUtil.convert_miles(userMedal.getTargetProcess2(), new BigDecimal(1.6));
                BigDecimal history2 = BigDecimalUtil.convert_miles(userMedal.getHistoryProcess2(), new BigDecimal(1.6));
                BigDecimal current2 = BigDecimalUtil.convert_miles(userMedal.getCurrentProcess2(), new BigDecimal(1.6));
                userMedal.setTargetProcess2(target2);
                if (current2.compareTo(target2) > 0) {
                    userMedal.setCurrentProcess2(target2);
                } else {
                    userMedal.setCurrentProcess2(current2);
                }
                if (history2.compareTo(target2) > 0) {
                    userMedal.setHistoryProcess2(target2);
                } else {
                    userMedal.setHistoryProcess2(history2);
                }
            }
        }
    }

    public String getValue(MedalConfig medalConfig, String key) {
        if ("1".equals(key)) {
            return medalConfig.getParam1();
        } else if ("2".equals(key)) {
            return medalConfig.getParam2();
        } else if ("3".equals(key)) {
            return medalConfig.getParam3();
        } else if ("4".equals(key)) {
            return medalConfig.getParam4();
        }
        return medalConfig.getParam5();
    }


    @Override
    public List<UserMedalDto> selectUserMedalByUserIdList(IPage page, Long userId, Integer obtain) {
        return userMedalDao.selectUserMedalByUserIdList(page, userId, obtain);
    }

    @Override
    public boolean saveOrUpdate(UserMedal userMedal) {
        return userMedalDao.insertOrUpdate(userMedal);
    }

    @Override
    public void updateBatchById(List<UserMedal> userMedalList) {
        userMedalDao.updateById(userMedalList);
    }

    @Override
    public long findCount(QueryWrapper<UserMedal> wrapper) {
        return userMedalDao.selectCount(wrapper);
    }

    @Override
    public List<UserMedal> findList(QueryWrapper<UserMedal> wrapper) {
        return userMedalDao.selectList(wrapper);
    }
}
