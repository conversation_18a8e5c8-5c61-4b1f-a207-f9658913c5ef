package com.linzi.pitpat.data.activityservice.biz;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityPropEffectEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityPropTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityCouponDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityMedalDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityOfficialDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityOfficialRankingListDto;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.PacerDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPlaylistRel;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRateLimit;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.PropManage;
import com.linzi.pitpat.data.activityservice.model.entity.RunPlaylist;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRateLimitQuery;
import com.linzi.pitpat.data.activityservice.model.query.AssistActivitityQuery;
import com.linzi.pitpat.data.activityservice.model.query.PkChallengeRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityConfigResp;
import com.linzi.pitpat.data.activityservice.model.resp.TaskResp;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeReportVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkVo;
import com.linzi.pitpat.data.activityservice.model.vo.RewardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.TeamAndRewardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.TeamConfig;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.ActivityPropConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import com.linzi.pitpat.data.activityservice.service.PacerConfigService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.PlaylistMusicRelService;
import com.linzi.pitpat.data.activityservice.service.PropManageService;
import com.linzi.pitpat.data.activityservice.service.RunActivityCheatService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.RunActivityShowUserService;
import com.linzi.pitpat.data.activityservice.service.RunMilestoneStageConfigService;
import com.linzi.pitpat.data.activityservice.service.RunPlaylistService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSourceEnum;
import com.linzi.pitpat.data.awardservice.model.dto.ActivitySumAwardBySubTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.BrandRightsInterestListDto;
import com.linzi.pitpat.data.awardservice.model.dto.RewardSituationDto;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.AwardRelation;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardProcessService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.entity.dto.ActivityPropDto;
import com.linzi.pitpat.data.entity.dto.CompeteReportDto;
import com.linzi.pitpat.data.entity.dto.RunPlaylistDto;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityMusicSupportEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.query.RunPlaylistQuery;
import com.linzi.pitpat.data.request.InviteRunnerRequest;
import com.linzi.pitpat.data.request.activity.GroupRunRateLimitDetailDto;
import com.linzi.pitpat.data.request.activity.GroupRunRateLimitDto;
import com.linzi.pitpat.data.request.activity.VisibleRangePo;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.vo.FriendSimpleInfoVO;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 原活动创建类
 *
 * <AUTHOR>
 * @date 2024/6/17 18:36
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunActivityBizService {
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserService userService;
    private final ZnsUserAccountService userAccountService;
    private final ISysConfigService sysConfigService;
    private final PkChallengeRecordService pkChallengeRecordService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    private final ZnsUserRunRecordService userRunRecordService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final RunActivityCheatService runActivityCheatService;
    private final ActivityPropConfigService activityPropConfigService;
    private final RunMilestoneStageConfigService runMilestoneStageConfigService;
    private final RunActivityMedalService runActivityMedalService;
    private final PropManageService propManageService;
    private final ActivityRateLimitService activityRateLimitService;
    private final ActivityEquipmentConfigService activityEquipmentConfigService;
    private final ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    private final PacerConfigService pacerConfigService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityPlaylistRelService activityPlaylistRelService;
    private final RunPlaylistService runPlaylistService;
    private final PlaylistMusicRelService playlistMusicRelService;
    private final ZnsRunRouteService runRouteService;
    private final MedalConfigService medalConfigService;
    private final AssistActivitityService assistActivitityService;
    private final ActivityTeamService activityTeamService;
    private final AwardProcessService awardProcessService;
    private final CouponService couponService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final RunActivityShowUserService runActivityShowUserService;


    /**
     * 处理活动行程冲突，活动是否参与
     *
     * @param list
     * @param startTime
     */
    public void dealActivityConflict(List<FriendSimpleInfoVO> list, ZonedDateTime startTime) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (Objects.isNull(startTime)) {
            return;
        }
        ZonedDateTime selectStartTime = startTime.minusMinutes(30);
        ZonedDateTime selectEndTime = startTime.plusMinutes(30);
        List<Integer> ids = list.stream().map(FriendSimpleInfoVO::getFriendId).collect(Collectors.toList());

        List<ZnsRunActivityUserEntity> userEntityList = runActivityUserService.getInProgressActivityUser(ids, selectStartTime, selectEndTime);

        if (CollectionUtils.isEmpty(userEntityList)) {
            return;
        }
        Map<Long, ZnsRunActivityUserEntity> userEntityMap = userEntityList.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, Function.identity(), (x, y) -> y));

        list.forEach(v -> {
            ZnsRunActivityUserEntity userEntity = userEntityMap.get(Long.valueOf(v.getFriendId()));
            if (Objects.nonNull(userEntity)) {
                v.setIsAlreadyActivity(1);
            }
        });
    }


    /**
     * 获得预计最大奖励
     *
     * @param jsonObject
     * @param activityType
     * @param userCount
     * @param activityEntryFee
     * @param runningGoals
     * @return
     */
    public BigDecimal getPreMaxReward(Map<String, Object> jsonObject, Integer activityType, Integer userCount, BigDecimal activityEntryFee, List<Integer> runningGoals
            , Long activityId, ZnsUserEntity loginUser) {
        String currencyCode = userAccountService.getUserAccount(loginUser.getId()).getCurrencyCode();
        BigDecimal maxReward = BigDecimal.ZERO;
        try {
            if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityType)) {
                Map<String, Object> officialEventAward = JsonUtil.readValue(jsonObject.get("officialEventAward"));
                if (userCount < 20) {
                    userCount = 20;
                }
                for (int i = 0; i < userCount; i++) {
                    Integer rank = i + 1;
                    BigDecimal award = runActivityService.officialEventRankAward(rank, officialEventAward);
                    if (award != null) {
                        maxReward = maxReward.add(award);
                    }
                }
            } else if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityType)) {
                List<Map> runningGoalsAwardList = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
                //用户目标不为空，返回所选目标奖励
                if (!CollectionUtils.isEmpty(runningGoals)) {
                    Integer goal = runningGoals.get(0);
                    Map map = runningGoalsAwardList.stream().filter(m -> goal.equals(MapUtils.getInteger(m, "goal"))).findFirst().orElse(null);
                    if (Objects.nonNull(map)) {
                        maxReward = MapUtil.getBigDecimal(map.get("award"), BigDecimal.ZERO).add(MapUtil.getBigDecimal(map.get("firstAward"), BigDecimal.ZERO));
                    }
                } else {
                    maxReward = runningGoalsAwardList.stream().map(map -> {
                        BigDecimal award = MapUtil.getBigDecimal(map.get("award"), BigDecimal.ZERO);
                        BigDecimal firstAward = MapUtil.getBigDecimal(map.get("firstAward"), BigDecimal.ZERO);
                        return award.add(firstAward);
                    }).max((x1, x2) -> x1.compareTo(x2)).get();
                }
            } else if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(activityType)) {
                List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
                maxReward = milepostAward.get(milepostAward.size() - 1).getCumulativeAward();
            } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityType)) {
                // 计算公式：平台补贴金额 * (已接受人数) + 单人保证金 * 已接受人数 + 胜者奖励金额
                String participateAward = MapUtil.getString(jsonObject.get(ApiConstants.PARTICIPATE_AWARD));
                String winnerAward = MapUtil.getString(jsonObject.get(ApiConstants.WINNER_AWARD));
                BigDecimal participateDecimal = new BigDecimal(participateAward);
                BigDecimal winnerDecimal = new BigDecimal(winnerAward);
                maxReward = maxReward.add(participateDecimal).add(winnerDecimal);
                if (Objects.nonNull(activityEntryFee)) {
                    maxReward = maxReward.add(activityEntryFee.multiply(new BigDecimal(userCount)));
                }
                ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
                if (znsRunActivityEntity != null && Arrays.asList(1, 2).contains(znsRunActivityEntity.getActivityTypeSub())) {
                    BigDecimal winnerAmountCurrency = awardConfigAmountCurrencyDataService.findAmountByActivityAndSendType(activityId, AwardSentTypeEnum.WINNER_AWARD.getType(), currencyCode);
                    BigDecimal participationCurrency = awardConfigAmountCurrencyDataService.findAmountByActivityAndSendType(activityId, AwardSentTypeEnum.PARTICIPATION_AWARD.getType(), currencyCode);
                    BigDecimal max = BigDecimal.ZERO;
                    if (Objects.nonNull(winnerAmountCurrency) && winnerAmountCurrency.compareTo(BigDecimal.ZERO) > 0) {
                        max = max.add(winnerAmountCurrency);
                    }
                    if (Objects.nonNull(participationCurrency) && participationCurrency.compareTo(BigDecimal.ZERO) > 0) {
                        max = max.add(participationCurrency);
                    }
                    if (activityEntryFee.compareTo(BigDecimal.ZERO) > 0) {
                        maxReward = max.add(activityEntryFee.multiply(new BigDecimal(userCount)));
                    } else {
                        maxReward = max;
                    }
                }
                if (znsRunActivityEntity != null && Objects.equals(znsRunActivityEntity.getActivityTypeSub(), 3)) {
                    log.info("pk 赛事，离线pk计算金额开始");
                    PkChallengeRecordQuery query = PkChallengeRecordQuery.builder().activityId(znsRunActivityEntity.getId()).userId(loginUser.getId()).build();
                    PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.findByQuery(query);
                    if (pkChallengeRecord != null) {
                        maxReward = pkChallengeRecord.getAward();
                    }
                }
                if (znsRunActivityEntity != null && Objects.equals(znsRunActivityEntity.getActivityTypeSub(), 4)) {
                    NewPersonPkVo newPkMultipleConfigVo = JsonUtil.readValue(jsonObject, NewPersonPkVo.class);

                    if (newPkMultipleConfigVo.getIsFinishAward().equals(YesNoStatus.YES.getCode())) {
                        UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
                        if (Objects.nonNull(finishAward)) {
                            if (!CollectionUtils.isEmpty(finishAward.getAmountList())) {
                                List<CurrencyAmount> amountList = finishAward.getAmountList();
                                CurrencyAmount currencyAmount = amountList.stream().filter(s -> s.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
                                if (Objects.nonNull(currencyAmount) && Objects.nonNull(currencyAmount.getAmount())) {
                                    maxReward = currencyAmount.getAmount();
                                }
                                //兼容老活动
                            } else if (Objects.nonNull(finishAward.getAmount()) && finishAward.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                                maxReward = finishAward.getAmount();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("jsonObject:{}", JsonUtil.writeString(jsonObject));
            log.error("getPreMaxReward 失败，e:{}", e);
        }
        maxReward = I18nConstant.currencyFormat(currencyCode, maxReward);
        return maxReward;
    }


    public Map<String, Object> getRankingResults(ActivityTypeDto activity, Integer type, ZnsUserEntity user) {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> activityUsers = new ArrayList<>();

        //查询用户的跑步记录id
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activity.getId(), user.getId());
        if (Objects.isNull(activityUser)) {
            log.warn("getRankingResults失败，无用户活动数据");
            return data;
        }
        data.put("activityTitle", activity.getActivityTitle());
        BigDecimal runMileage = BigDecimal.ZERO;
        Integer runTime = 0;
        if (MainActivityTypeEnum.OLD.getType().equals(activity.getMainType())) {
            runMileage = activity.getRunActivity().getRunMileage();
            runTime = activity.getRunActivity().getRunTime();
        } else {
            runMileage = new BigDecimal(activity.getSubActivity().getTarget());
            runTime = activity.getSubActivity().getTarget();
        }

        if (type == 1) {
            //返回排行榜所有数据
            List<ZnsRunActivityUserEntity> activityUserList = runActivityUserService.findAllCompleteActivityUser(activity.getId());
            if (!CollectionUtils.isEmpty(activityUserList)) {
                List<Long> userIds = activityUserList.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
                List<ZnsUserEntity> userEntityList = userService.findByIds(userIds);
                Map<Long, ZnsUserEntity> userMap = userEntityList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
                if (activity.getTargetType() == 1) {
                    activityUserList.sort(Comparator.comparing(ZnsRunActivityUserEntity::getRunTimeMillisecond).thenComparing(ZnsRunActivityUserEntity::getModifieTime, Comparator.reverseOrder()));
                } else if (activity.getTargetType() == 2) {
                    activityUserList.sort(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed().thenComparing(ZnsRunActivityUserEntity::getModifieTime, Comparator.reverseOrder()));
                }
                for (int i = 0; i < activityUserList.size(); i++) {
                    ZnsRunActivityUserEntity znsRunActivityUserEntity = activityUserList.get(i);

                    ZnsUserEntity znsUserEntity = userMap.get(znsRunActivityUserEntity.getUserId());
                    Map<String, Object> map = new HashMap<>();
                    map.put("firstName", znsUserEntity.getFirstName());
                    map.put("lastName", znsUserEntity.getLastName());
                    map.put("headPortrait", znsUserEntity.getHeadPortrait());
                    map.put("gender", znsUserEntity.getGender());
                    map.put("rank", i + 1);
                    map.put("userId", znsUserEntity.getId());
                    if (znsRunActivityUserEntity.getUserId().equals(user.getId())) {
                        data.put("rank", i + 1);
                        map.put("isMe", 1);
                    }
                    activityUsers.add(map);
                }
                data.put("totalCount", activityUserList.size());
                data.put("activityUsers", activityUsers);
            }
            return data;
        }

        // 查找用户官方赛事最新跑步记录

        ZnsUserRunRecordEntity runRecordEntity = userRunRecordService.findOneByUserAndActAndNoDetailId(user.getId(), activity.getId());
        if (null == runRecordEntity || runRecordEntity.getRunDataDetailsId().intValue() <= 0) {
            log.warn("未查到用户官方赛事最新跑步记录");
            return data;
        }

        ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.findById(runRecordEntity.getRunDataDetailsId());


        //跑步是否结束
        if (detailsEntity.getRunStatus() != 1) {
            //防止数据未处理完
            detailsEntity = userRunDataDetailsService.waitingProcessing(detailsEntity, 500);
            if (Objects.isNull(detailsEntity)) {
                log.warn("未查到用户官方赛事最新跑步记录");
                return data;
            }
            if (detailsEntity.getRunStatus() != 1) {
                log.warn("Run report not generated");
            }
        }
        data.put("runDataDetailsId", detailsEntity.getId());
        boolean isComplete = false;
        if (activity.getTargetType() == 1 && detailsEntity.getRunMileage().compareTo(runMileage) >= 0) {
            isComplete = true;
        } else if (activity.getTargetType() == 2 && detailsEntity.getRunTime().compareTo(runTime) >= 0) {
            isComplete = true;
        }
        data.put("isComplete", isComplete ? 1 : 0);
        if (!isComplete) {
            return data;
        }

        List<ZnsRunActivityUserEntity> activityUserList = runActivityUserService.findAllCompleteActivityUserNeOneSelf(activity.getId(), user.getId());
        if (CollectionUtils.isEmpty(activityUserList)) {
            data.put("rank", 1);
            data.put("totalCount", 1);
            Map<String, Object> map = new HashMap<>();
            map.put("firstName", user.getFirstName());
            map.put("lastName", user.getLastName());
            map.put("headPortrait", user.getHeadPortrait());
            map.put("rank", 1);
            map.put("userId", user.getId());
            map.put("gender", user.getGender());
            map.put("isMe", 1);
            activityUsers.add(map);
        } else {
            ZnsRunActivityUserEntity oneSelf = new ZnsRunActivityUserEntity();
            oneSelf.setUserId(activityUser.getUserId());
            oneSelf.setRunMileage(detailsEntity.getRunMileage());
            oneSelf.setRunTime(detailsEntity.getRunTime());
            oneSelf.setModifieTime(ZonedDateTime.now());
            activityUserList.add(oneSelf);
            data.put("totalCount", activityUserList.size());

            List<Long> userIds = activityUserList.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
            List<ZnsUserEntity> userEntityList = userService.findByIds(userIds);
            Map<Long, ZnsUserEntity> userMap = userEntityList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
            if (activity.getTargetType() == 1 && detailsEntity.getRunMileage().compareTo(runMileage) >= 0) {
                activityUserList.sort(Comparator.comparing(ZnsRunActivityUserEntity::getRunTime).thenComparing(ZnsRunActivityUserEntity::getModifieTime, Comparator.reverseOrder()));
            } else if (activity.getTargetType() == 2 && detailsEntity.getRunTime().compareTo(runTime) >= 0) {
                activityUserList.sort(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed().thenComparing(ZnsRunActivityUserEntity::getModifieTime, Comparator.reverseOrder()));
            }
            Integer oneSelfIndex = 0;
            for (int i = 0; i < activityUserList.size(); i++) {
                ZnsRunActivityUserEntity znsRunActivityUserEntity = activityUserList.get(i);

                ZnsUserEntity znsUserEntity = userMap.get(znsRunActivityUserEntity.getUserId());
                Map<String, Object> map = new HashMap<>();
                map.put("firstName", znsUserEntity.getFirstName());
                map.put("lastName", znsUserEntity.getLastName());
                map.put("headPortrait", znsUserEntity.getHeadPortrait());
                map.put("gender", znsUserEntity.getGender());
                map.put("rank", i + 1);
                map.put("userId", znsUserEntity.getId());
                if (znsRunActivityUserEntity.getUserId().equals(user.getId())) {
                    oneSelfIndex = i;
                    data.put("rank", i + 1);
                    map.put("isMe", 1);
                }
                activityUsers.add(map);
            }

            //用户取舍，最多7位用户
            Integer minIndex = 0;
            Integer maxIndex = activityUserList.size();
            if (maxIndex > 7) {
                if (oneSelfIndex < 7) {
                    maxIndex = 7;
                } else {
                    if (activityUserList.size() - oneSelfIndex > 3) {
                        minIndex = oneSelfIndex - 3;
                        maxIndex = oneSelfIndex + 3;
                    } else {
                        minIndex = oneSelfIndex - 6;
                        maxIndex = oneSelfIndex + 1;
                    }
                }
            }
            activityUsers = activityUsers.subList(minIndex, maxIndex);
        }

        data.put("activityUsers", activityUsers);
        return data;
    }


    /**
     * 获得官方活动详情
     *
     * @param activityId
     * @param isRealUser
     * @return
     */
    public ActivityOfficialDto getOfficialActivityDetail(Long activityId, Integer isRealUser) {
        ActivityOfficialDto officialDto = new ActivityOfficialDto();
        ZnsRunActivityEntity activity = runActivityService.findById(activityId);
        if (Objects.isNull(activity)) {
            return officialDto;
        }
        BeanUtils.copyProperties(activity, officialDto);
        if (ActivityMusicSupportEnum.SUPPORT.getCode().equals(activity.getMusicSupport())) {
            // 活动中歌单数据
            List<ActivityPlaylistRel> activityPlaylistRels = activityPlaylistRelService.findByActId(activityId);
            List<Long> playlistIds = activityPlaylistRels.stream().map(ActivityPlaylistRel::getPlaylistId).collect(Collectors.toList());
            RunPlaylistQuery queryPo = new RunPlaylistQuery();
            queryPo.setPlaylistIds(playlistIds);
            List<RunPlaylist> runPlaylists = runPlaylistService.findRunPlaylistByQuery(queryPo);
            List<RunPlaylistDto> playlistDtoList = runPlaylists.stream().map(e -> {
                RunPlaylistDto playlistDto = BeanUtil.copyBean(e, RunPlaylistDto.class);
                long count = playlistMusicRelService.countByPlaylistId(e.getId());
                playlistDto.setMusicNum((int) count);
                return playlistDto;
            }).collect(Collectors.toList());
            officialDto.setPlaylistDtoList(playlistDtoList);
        }
        if (YesNoStatus.YES.getCode().equals(activity.getPropSupport())) {
            // 活动中配置的道具数据
            List<ActivityPropDto> activityPropDtoList = getActivityPropDtoList(activityId);
            officialDto.setActivityPropDtoList(activityPropDtoList);
        }
        Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
        analysis(jsonObject, officialDto);
        //参赛情况处理
        competitionSituation(officialDto, isRealUser);

        List<ActivityEquipmentConfig> activityEquipmentConfigs = activityEquipmentConfigService.selectActivityEquipmentConfigByActivityId(activityId);
        officialDto.setActivityEquipmentConfigs(activityEquipmentConfigs);

        //互斥活动处理
        String mutexActivityIds = officialDto.getMutexActivityIds().replace("-1", "");
        if (StringUtils.hasText(mutexActivityIds)) {
            officialDto.setMutexActivityIdList(NumberUtils.stringToInt(mutexActivityIds.split(",")));
            List<ZnsRunActivityEntity> znsRunActivityEntities = runActivityService.findByIds(officialDto.getMutexActivityIdListLong());
            List<Map<String, Object>> mutexActivityList = new ArrayList<>();
            for (ZnsRunActivityEntity znsRunActivityEntity : znsRunActivityEntities) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", znsRunActivityEntity.getId());
                map.put("activityTitle", znsRunActivityEntity.getActivityTitle());
                mutexActivityList.add(map);
            }
            officialDto.setMutexActivityList(mutexActivityList);
        }
        officialDto.setRouteTitle(runRouteService.getRouteTitle(officialDto.getActivityRouteId().intValue()));
        //活动对象
        if (officialDto.getActivityObjectType() == 1) {
            MedalConfig medalConfig = medalConfigService.getById(officialDto.getDemandMedalConfigId());
            if (Objects.nonNull(medalConfig)) {
                officialDto.setDemandMedalName(medalConfig.getName());
                officialDto.setDemandMedalUrl(medalConfig.getUrl());
            }
        }
        // 获取助力活动详情信息
        Long assistActivityId = activity.getAssistActivityId();
        if (assistActivityId != null && assistActivityId > 0) {
            AssistActivitity activitity = assistActivitityService.findOne(
                    AssistActivitityQuery.builder()
                            .select(List.of(AssistActivitity::getStartTime, AssistActivitity::getEndTime, AssistActivitity::getName))
                            .id(assistActivityId)
                            .build());
            officialDto.setAssistActivityTitle(activitity.getName());
            officialDto.setAssistStartTime(activitity.getStartTime().toInstant().toEpochMilli());
            officialDto.setAssistEndTime(activitity.getEndTime().toInstant().toEpochMilli());
        }
        //如果是团队赛
        if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(activity.getActivityType())) {
            //组装队伍详情
            List<ActivityTeam> teams = activityTeamService.findByActId(activityId);

            TeamAndRewardConfig teamAndRewardConfig = new TeamAndRewardConfig();
            //队伍属性回显
            List teamConfigs = new ArrayList<TeamConfig>();
            for (ActivityTeam team : teams) {
                TeamConfig teamConfig = new TeamConfig();
                teamConfig.setTeamLogo(team.getTeamLogo());
                teamConfig.setTeamName(team.getTeamName());
                teamConfig.setRank(team.getRank());
                teamConfigs.add(teamConfig);
                //实际上这里会被重复多次赋一样的值，但是下面可以避免出现空指针判断
                teamAndRewardConfig.setTeamMaxNum(team.getMaxNum());
            }

            //奖励回显
            List<AwardRelation> relationList = awardProcessService.getRewards(AwardSourceEnum.ACTIVITY, activityId);
            List<RewardConfig> rewardConfigList = relationList.stream().map(k -> {
                RewardConfig rewardConfig = new RewardConfig();
                rewardConfig.setRank(k.getRank());
                rewardConfig.setBaseReward(k.getBaseReward());
                rewardConfig.setBaseScoreReward(k.getBaseScoreReward());
                rewardConfig.setHeadReward(k.getHeadReward());
                rewardConfig.setHeadScoreReward(k.getHeadScoreReward());
                if (!CollectionUtils.isEmpty(k.getCouponIds())) {
                    rewardConfig.setCouponId(k.getCouponIds().get(0));
                    Coupon coupon = couponService.selectCouponById(k.getCouponIds().get(0));
                    rewardConfig.setCouponName(coupon.getName());
                    rewardConfig.setCouponNum(k.getCouponIds().size());
                    rewardConfig.setCoupon(k.getCouponIds().size());
                }
                return rewardConfig;
            }).collect(Collectors.toList());

            //计算应发奖励
            BigDecimal rewardAmount = calculateRewardAmount(relationList, teams);
            officialDto.setRewardAmount(rewardAmount);
            Integer rewardScore = calculateRewardScore(relationList, teams);
            officialDto.setRewardScore(rewardScore);

            //参赛人数/获得奖励人数
            Integer num = getNumberOfParticipants(activity);
            officialDto.setNumberOfParticipants(num);

            //组装队伍与奖励
            teamAndRewardConfig.setTeamConfig(teamConfigs);
            teamAndRewardConfig.setRewardConfig(rewardConfigList);
            officialDto.setTeamAndRewardConfig(teamAndRewardConfig);
        }

        // 官方组队跑
        // 限速回显
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activity.getActivityType())) {
            String activityConfig = activity.getActivityConfig();
            Integer sourceUnit = null;
            if (activityConfig != null) {
                Map<String, Object> activityConfigObj = JsonUtil.readValue(activityConfig);
                String runningGoalsUnit = String.valueOf(activityConfigObj.get("runningGoalsUnit"));
                if (StringUtils.hasText(runningGoalsUnit)) {
                    // 新建/修改活动原始目标单位 0：公里 ，1：英里，2：时间（min）
                    sourceUnit = Integer.valueOf(runningGoalsUnit);
                }
            }
            if (activity.getRateLimitType() == 2) {
                // 有限制最高速
                List<GroupRunRateLimitDto> groupRunRateLimitDtoList = new ArrayList<>();
                List<ActivityRateLimit> activityRateLimits = activityRateLimitService.findList(
                        ActivityRateLimitQuery.builder()
                                .activityId(activityId)
                                .build()
                );
                if (!CollectionUtils.isEmpty(activityRateLimits)) {
                    Map<BigDecimal, List<ActivityRateLimit>> collect = activityRateLimits.stream().collect(Collectors.groupingBy(ActivityRateLimit::getRunningGoal));
                    for (BigDecimal runningGoal : collect.keySet()) {
                        GroupRunRateLimitDto groupRunRateLimitDto = new GroupRunRateLimitDto();
                        BigDecimal returnRunningGoal = null;
                        if (sourceUnit == 0) {
                            // m——>km
                            returnRunningGoal = BigDecimalUtil.divHalfDown(runningGoal, new BigDecimal(1000), 4);
                        } else if (sourceUnit == 1) {
                            // m——>mile
                            returnRunningGoal = BigDecimalUtil.divHalfDown(runningGoal, new BigDecimal(1600), 4);
                        } else {
                            // s——>min
                            returnRunningGoal = BigDecimalUtil.divHalfDown(runningGoal, new BigDecimal(60), 0);
                        }
                        groupRunRateLimitDto.setRunningGoal(returnRunningGoal); // m——>km m——>mile s——>min

                        List<GroupRunRateLimitDetailDto> details = new ArrayList<>();
                        List<ActivityRateLimit> rateLimitDetils = collect.get(runningGoal);
                        for (ActivityRateLimit item : rateLimitDetils) {
                            GroupRunRateLimitDetailDto detailDto = new GroupRunRateLimitDetailDto();
                            detailDto.setRateLimit(SportsDataUnit.conversionVelocity(item.getRateLimit(), 1, 4)); // m/h ——> mile/h
                            if (sourceUnit == 0) {
                                // 距离
                                detailDto.setStart(BigDecimalUtil.divHalfUp(item.getStart(), new BigDecimal(1600), 4)); // m——>mile
                                detailDto.setEnd(BigDecimalUtil.divHalfUp(item.getEnd(), new BigDecimal(1600), 4)); // m——>mile
                            } else if (sourceUnit == 1) {
                                // 距离
                                detailDto.setStart(BigDecimalUtil.divHalfUp(item.getStart(), new BigDecimal(1600), 4)); // m——>mile
                                detailDto.setEnd(BigDecimalUtil.divHalfUp(item.getEnd(), new BigDecimal(1600), 4)); // m——>mile
                            } else {
                                // 时间
                                detailDto.setStart(item.getStart()); // s——>s
                                detailDto.setEnd(item.getEnd()); // s——>s
                            }
                            details.add(detailDto);
                        }
                        groupRunRateLimitDto.setDetails(details);
                        groupRunRateLimitDtoList.add(groupRunRateLimitDto);
                    }
                    officialDto.setGroupRunRateLimitDto(groupRunRateLimitDtoList);
                }


            }

        }


        //获取活动勋章
        ActivityMedalDto activityMedalDto = runActivityMedalService.findByActivityId(activityId);
        officialDto.setActivityMedalDto(activityMedalDto);

        runMilestoneStageConfigService.getActivityRunMilestoneStageConfigList(officialDto, activity);
        //作弊开关处理
        List<Integer> cheatList = runActivityCheatService.selectCheatSwitchList(activity.getId());

        officialDto.setCheatSwitchList(cheatList);
        return officialDto;
    }


    /**
     * 获取参赛人数
     *
     * @param activity
     * @return
     */
    private Integer getNumberOfParticipants(ZnsRunActivityEntity activity) {
        RunActivityUserQuery query = RunActivityUserQuery.builder()
                .isDelete(0)
                .activityId(activity.getId())
                .userStateIn(Arrays.asList(ActivityUserStateEnum.RUNING.getState(),
                        ActivityUserStateEnum.ENDED.getState()))
                .build();

        return runActivityUserService.findCount(query).intValue();
    }


    private BigDecimal calculateRewardAmount(List<AwardRelation> relationList, List<ActivityTeam> teams) {
        Map<Integer, AwardRelation> relationMap = relationList.stream().collect(Collectors.toMap(
                AwardRelation::getRank, Function.identity()
        ));
        BigDecimal total = BigDecimal.ZERO;
        for (ActivityTeam team : teams) {
            if (team.getCurrentNum() > 0) {
                AwardRelation relation = relationMap.get(team.getRank());
                BigDecimal amount = calculateAmount(relation, team.getCurrentNum());
                total = total.add(amount);
            }
        }
        return total;

    }

    private BigDecimal calculateAmount(AwardRelation awardRelation, Integer currentNum) {
        if (Objects.isNull(awardRelation)) {
            return BigDecimal.ZERO;
        }
        BigDecimal awardAmount = BigDecimal.ZERO;
        BigDecimal baseReward = awardRelation.getBaseReward();
        BigDecimal headReward = awardRelation.getHeadReward();
        if (Objects.nonNull(baseReward) && baseReward.compareTo(BigDecimal.ZERO) > 0) {
            awardAmount = awardAmount.add(baseReward);
        }
        if (Objects.nonNull(headReward) && headReward.compareTo(BigDecimal.ZERO) > 0) {
            awardAmount = BigDecimalUtil.multiply(headReward, BigDecimal.valueOf(currentNum)).add(awardAmount);
        }
        return awardAmount;
    }

    private Integer calculateRewardScore(List<AwardRelation> relationList, List<ActivityTeam> teams) {
        Map<Integer, AwardRelation> relationMap = relationList.stream().collect(Collectors.toMap(
                AwardRelation::getRank, Function.identity()
        ));
        Integer total = 0;
        for (ActivityTeam team : teams) {
            if (team.getCurrentNum() > 0) {
                AwardRelation relation = relationMap.get(team.getRank());
                Integer score = calculateScore(relation, team.getCurrentNum());
                total = total + score;
            }
        }
        return total;
    }

    private Integer calculateScore(AwardRelation awardRelation, Integer currentNum) {
        Integer awardScore = 0;
        Integer baseScoreReward = awardRelation.getBaseScoreReward();
        Integer headScoreReward = awardRelation.getHeadScoreReward();
        if (Objects.nonNull(baseScoreReward) && baseScoreReward > 0) {
            awardScore = awardScore + baseScoreReward;
        }
        if (Objects.nonNull(headScoreReward) && headScoreReward > 0) {
            awardScore = headScoreReward * currentNum + awardScore;
        }
        return awardScore;
    }


    /**
     * 参赛情况
     *
     * @param officialDto
     * @param isRealUser
     */
    private void competitionSituation(ActivityOfficialDto officialDto, Integer isRealUser) {
        //奖励情况
        RewardSituationDto rewardSituation = new RewardSituationDto();

        //参赛人数
        List<CompeteReportDto> competeReport = new ArrayList<>();
        if (officialDto.getActivityType() == 3) {
            competeReport = runActivityUserService.getCompeteReport(officialDto.getId(), officialDto.getCompleteRuleType(), officialDto.getActivityType(), isRealUser);
            //被挑战情况
            List<ChallengeReportVo> challengeReport = userRunRecordService.getChallengeReport(officialDto.getId(), isRealUser);
            Integer beChallengedCount = 0;
            BigDecimal beChallengedAmount = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(challengeReport)) {
                //被挑战奖励
                List<Long> challengedUserIds = challengeReport.stream().map(ChallengeReportVo::getChallengedUserId).collect(Collectors.toList());
                List<ZnsUserAccountDetailEntity> accountDetails1 = userAccountDetailService.getAccountDetails(challengedUserIds, officialDto.getId(), AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType(), AccountDetailSubtypeEnum.OFFICIAL_BECHALLENGED.getType());
                Map<Long, ZnsUserAccountDetailEntity> beChallenge = accountDetails1.stream().collect(Collectors.toMap(ZnsUserAccountDetailEntity::getUserId, Function.identity(), (x, y) -> x));
                //查询用户币种
                List<ZnsUserAccountEntity> accountEntities = userAccountService.selectByUserIdList(challengedUserIds);
                Map<Long, String> userCurrencyMap = accountEntities.stream().collect(Collectors.toMap(ZnsUserAccountEntity::getUserId, ZnsUserAccountEntity::getCurrencyCode, (k1, k2) -> k2));
                for (ChallengeReportVo challengeReportVo : challengeReport) {
                    ZnsUserAccountDetailEntity accountDetail = beChallenge.get(challengeReportVo.getChallengedUserId());
                    beChallengedCount = beChallengedCount + challengeReportVo.getChallengedNum();
                    if (Objects.nonNull(accountDetail)) {
                        //非美元转换成美元
                        BigDecimal challengeAward = exchangeRateConfigService.convertUSDCurrency(accountDetail.getAmount(), userCurrencyMap.get(challengeReportVo.getChallengedUserId()));
                        challengeReportVo.setChallengeAward(challengeAward);
                    }
                    if (Objects.nonNull(challengeReportVo.getChallengeAward())) {
                        beChallengedAmount = beChallengedAmount.add(challengeReportVo.getChallengeAward());
                    }
                }
                challengeReport = challengeReport.stream().sorted(Comparator.comparing(ChallengeReportVo::getChallengedNum).reversed()).collect(Collectors.toList());
            }
            officialDto.setChallengeReport(challengeReport);
            //查询奖励情况
            List<ActivitySumAwardBySubTypeDto> activitySumAwardBySubTypeDtos = userAccountDetailService.selectSumByActivityIdTradeType(officialDto.getId(), AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType(), isRealUser);
            Map<String, Object> challengeSuccessAndFail = userRunRecordService.getChallengeSuccessAndFail(officialDto.getId(), isRealUser);
            Integer totalCount = MapUtils.getInteger(challengeSuccessAndFail, "totalCount");
            Integer successCount = MapUtils.getInteger(challengeSuccessAndFail, "successCount");
            Integer failCount = totalCount - successCount;
            //查询人数
            Integer challengeUserFail = userRunRecordService.getChallengeUserSuccessAndFail(officialDto.getId(), isRealUser, 0);
            boolean hasChallengedFail = false;
            for (ActivitySumAwardBySubTypeDto activitySumAwardBySubTypeDto : activitySumAwardBySubTypeDtos) {
                if (activitySumAwardBySubTypeDto.getTradeSubType() == 0) {
                    activitySumAwardBySubTypeDto.setAwardName("名次奖励");
                    activitySumAwardBySubTypeDto.setChallengedCount(activitySumAwardBySubTypeDto.getAwardCount());
                } else if (activitySumAwardBySubTypeDto.getTradeSubType() == 1) {
                    activitySumAwardBySubTypeDto.setAwardName("挑战成功奖励");
                    activitySumAwardBySubTypeDto.setChallengedCount(successCount);
                } else if (activitySumAwardBySubTypeDto.getTradeSubType() == 2) {
                    activitySumAwardBySubTypeDto.setAwardName("挑战失败奖励");
                    activitySumAwardBySubTypeDto.setChallengedCount(failCount);
                    activitySumAwardBySubTypeDto.setUserCount(challengeUserFail);
                    hasChallengedFail = true;
                } else if (activitySumAwardBySubTypeDto.getTradeSubType() == 3) {
                    activitySumAwardBySubTypeDto.setAwardName("被挑战奖励");
                    activitySumAwardBySubTypeDto.setChallengedCount(beChallengedCount);
                    activitySumAwardBySubTypeDto.setAmount(beChallengedAmount);
                }
            }

            //挑战失败人数：挑战他人失败不重复用户数,特殊处理
            if (!hasChallengedFail && failCount > 0) {
                ActivitySumAwardBySubTypeDto dto = new ActivitySumAwardBySubTypeDto();
                dto.setChallengedCount(failCount);
                dto.setAwardName("挑战失败奖励");

                dto.setUserCount(challengeUserFail);
                activitySumAwardBySubTypeDtos.add(dto);
            }

            rewardSituation.setAwardDetails(activitySumAwardBySubTypeDtos);
        } else if (officialDto.getActivityType() == 4) {
            List<CompeteReportDto> reportDtos = runActivityUserService.getCompeteReport(officialDto.getId(), officialDto.getCompleteRuleType(), officialDto.getActivityType(), isRealUser);
            Map<Double, CompeteReportDto> reportDtoMap = null;
            if (!CollectionUtils.isEmpty(reportDtos)) {
                for (CompeteReportDto dto : reportDtos) {
                    dto.setTargetAndInt(officialDto.getRunningGoalsUnit());
                }
                reportDtoMap = reportDtos.stream().collect(Collectors.toMap(CompeteReportDto::getTargetInt, Function.identity(), (x, y) -> x));
            }

            for (Double runningGoal : officialDto.getRunningGoals()) {
                if (Objects.isNull(reportDtoMap) || Objects.isNull(reportDtoMap.get(runningGoal))) {
                    competeReport.add(new CompeteReportDto(officialDto.getRunningGoalsUnit(), runningGoal));
                } else {
                    competeReport.add(reportDtoMap.get(runningGoal));
                }
            }
        } else if (officialDto.getActivityType() == 5) {
            competeReport = runActivityUserService.getCompeteReport(officialDto.getId(), officialDto.getCompleteRuleType(), officialDto.getActivityType(), isRealUser);
            //里程碑人数获取
            List<Integer> mileposts = officialDto.getMilepostAward().stream().map(m -> {
                if (officialDto.getRunningGoalsUnit() == 0) {
                    return m.getMilepost().multiply(new BigDecimal(1000)).intValue();
                } else if (officialDto.getRunningGoalsUnit() == 1) {
                    return m.getMilepost().multiply(new BigDecimal(1600)).intValue();
                } else {
                    return m.getMilepost().multiply(new BigDecimal(60)).intValue();
                }
            }).collect(Collectors.toList());
            List<Map<String, Object>> milepostReports = new ArrayList<>();
            for (int i = 0; i < mileposts.size(); i++) {
                Map<String, Object> milepostAwardReport = userAccountDetailService.getMilepostAwardReport(officialDto.getId(), AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD.getType(), i + 1, isRealUser);
                milepostReports.add(milepostAwardReport);
            }

            officialDto.setMilepostReports(milepostReports);
        } else if (officialDto.getActivityType() == 10) {
            //团队赛
            competeReport = runActivityUserService.getCompeteReport(officialDto.getId(), officialDto.getCompleteRuleType(), officialDto.getActivityType(), isRealUser);
        }
        if (!CollectionUtils.isEmpty(competeReport)) {
            for (CompeteReportDto dto : competeReport) {
                dto.setTargetAndInt(officialDto.getRunningGoalsUnit());
            }
        }
        officialDto.setCompeteReport(competeReport);


        Integer count = runActivityUserService.countAwardUser(officialDto.getId(), isRealUser);
        rewardSituation.setReceiveNum(count);
        BigDecimal activityAward = userAccountDetailService.sumOfficialActivityAward(officialDto.getActivityType(), officialDto.getId(), isRealUser);
        rewardSituation.setTotalAmount(Objects.isNull(activityAward) ? BigDecimal.ZERO : activityAward);
        officialDto.setRewardSituation(rewardSituation);

        //排名情况
        officialDto.setRankingList(getRankingList(officialDto.getId(), officialDto.getCompleteRuleType(), officialDto.getActivityType(), officialDto.getRunningGoalsUnit(), officialDto.getActivityState(), isRealUser));
    }


    /**
     * 获得排名情况
     *
     * @param activityId
     * @param completeRuleType
     * @param activityType
     * @param runningGoalsUnit
     * @param activityState
     * @param isRealUser
     * @return
     */
    private List<ActivityOfficialRankingListDto> getRankingList(Long activityId, Integer completeRuleType, Integer activityType, Integer runningGoalsUnit, Integer activityState, Integer isRealUser) {
        List<ActivityOfficialRankingListDto> rankingList = new ArrayList<>();
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityType)) {
            List<ActivityOfficialRankingListDto> userEntityList = runActivityUserService.rankingList(activityId, completeRuleType, activityType, activityState);
            if (!CollectionUtils.isEmpty(userEntityList)) {
                Map<Integer, List<ActivityOfficialRankingListDto>> map = new HashMap<>();
                if (completeRuleType == 1) {
                    map = userEntityList.stream().collect(Collectors.groupingBy(ActivityOfficialRankingListDto::getTargetRunMileage));
                } else {
                    map = userEntityList.stream().collect(Collectors.groupingBy(ActivityOfficialRankingListDto::getTargetRunTime));
                }
                Map<Integer, List<ActivityOfficialRankingListDto>> finalMap = map;
                // 相同成绩用户排序
                map.keySet().stream().sorted().forEach(m -> {
                    List<ActivityOfficialRankingListDto> list = finalMap.get(m);
                    List<ActivityOfficialRankingListDto> smallRankingList = new ArrayList<>();
                    for (int i = 0; i < list.size(); i++) {
                        ActivityOfficialRankingListDto dto = list.get(i);
                        if (dto.getSort() == -1) {
                            dto.setSort(smallRankingList.size() + 1);
                        }
                        dto.setCompleteRuleType(completeRuleType);
                        dto.setNameAndResult(dto.getTargetRunTime(), dto.getTargetRunMileage(), runningGoalsUnit, dto.getRunTime(), dto.getRunMileage(), dto.getRunTimeMillisecond(), completeRuleType);
                        if (Objects.nonNull(isRealUser) && isRealUser == 1) {
                            if (dto.getIsRobot() == 0 && dto.getIsTest() == 0) {
                                smallRankingList.add(dto);
                            }
                        } else {
                            smallRankingList.add(dto);
                        }
                    }
                    rankingList.addAll(smallRankingList);
                });
            }
        } else if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().equals(activityType)) {
            List<ActivityOfficialRankingListDto> userEntityList = runActivityUserService.rankingList(activityId, completeRuleType, activityType, activityState);
            if (!CollectionUtils.isEmpty(userEntityList)) {
                List<Long> userIds = userEntityList.stream().map(ActivityOfficialRankingListDto::getUserId).collect(Collectors.toList());
                //被挑战奖励
                List<ZnsUserAccountDetailEntity> accountDetails1 = userAccountDetailService.getAccountDetails(userIds, activityId, AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType(), AccountDetailSubtypeEnum.OFFICIAL_BECHALLENGED.getType());
                Map<Long, ZnsUserAccountDetailEntity> beChallenge = accountDetails1.stream().collect(Collectors.toMap(ZnsUserAccountDetailEntity::getUserId, Function.identity(), (x, y) -> x));
                //挑战奖励
                List<ZnsUserAccountDetailEntity> accountDetails2 = userAccountDetailService.getChallengeAmount(userIds, activityId);
                Map<Long, ZnsUserAccountDetailEntity> challenge = accountDetails2.stream().collect(Collectors.toMap(ZnsUserAccountDetailEntity::getUserId, Function.identity(), (x, y) -> x));
                //排名奖励重复问题处理，99469/99470/99471
                Map<Long, ZnsUserAccountDetailEntity> rankDetails = new HashMap<>();
                if (Arrays.asList(99469L, 99470L, 99471L).contains(activityId)) {
                    List<ZnsUserAccountDetailEntity> accountDetails3 = userAccountDetailService.getAccountDetails(userIds, activityId, AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType(), AccountDetailSubtypeEnum.OFFICIAL_RANK.getType());
                    rankDetails = accountDetails3.stream().collect(Collectors.toMap(ZnsUserAccountDetailEntity::getUserId, Function.identity(), (x, y) -> x));
                }
                //查询用户币种
                List<ZnsUserAccountEntity> accountEntities = userAccountService.selectByUserIdList(userIds);
                Map<Long, String> userCurrencyMap = accountEntities.stream().collect(Collectors.toMap(ZnsUserAccountEntity::getUserId, ZnsUserAccountEntity::getCurrencyCode, (k1, k2) -> k2));

                for (int i = 0; i < userEntityList.size(); i++) {
                    ActivityOfficialRankingListDto dto = userEntityList.get(i);
                    dto.setSort(i + 1);
                    dto.setCompleteRuleType(completeRuleType);
                    dto.setNameAndResult(dto.getTargetRunTime(), dto.getTargetRunMileage(), runningGoalsUnit, dto.getRunTime(), dto.getRunMileage(), dto.getRunTimeMillisecond(), completeRuleType);

                    ZnsUserAccountDetailEntity userAccountDetail1 = beChallenge.get(dto.getUserId());
                    if (Objects.nonNull(userAccountDetail1) && Objects.nonNull(userAccountDetail1.getAmount())) {
                        dto.setRunAward(dto.getRunAward().add(userAccountDetail1.getAmount()));
                    }
                    ZnsUserAccountDetailEntity userAccountDetail2 = challenge.get(dto.getUserId());
                    if (Objects.nonNull(userAccountDetail2) && Objects.nonNull(userAccountDetail2.getAmount())) {
                        dto.setRunAward(dto.getRunAward().add(userAccountDetail2.getAmount()));
                    }
                    ZnsUserAccountDetailEntity userAccountDetail3 = rankDetails.get(dto.getUserId());
                    if (Objects.nonNull(userAccountDetail3) && Objects.nonNull(userAccountDetail3.getAmount())) {
                        dto.setRunAward(dto.getRunAward().add(userAccountDetail3.getAmount()));
                    }
                    //非美元奖励转成美元
                    BigDecimal usdAward = exchangeRateConfigService.convertUSDCurrency(dto.getRunAward(), userCurrencyMap.get(dto.getUserId()));
                    dto.setRunAward(usdAward);
                    if (Objects.nonNull(isRealUser) && isRealUser == 1) {
                        if (dto.getIsRobot() == 0 && dto.getIsTest() == 0) {
                            rankingList.add(dto);
                        }
                    } else {
                        rankingList.add(dto);
                    }
                }
            }
        }

        return rankingList;
    }

    /**
     * 查询活动配置的道具
     *
     * @param runActivityId
     * @return
     */
    public List<ActivityPropDto> getActivityPropDtoList(Long runActivityId) {
        List<ActivityPropDto> activityPropDtoList = new ArrayList<>();
        List<ActivityPropConfig> activityPropConfigList = activityPropConfigService.selectActivityPropConfigByActivityId(runActivityId);
        if (!CollectionUtils.isEmpty(activityPropConfigList)) {
            List<Long> propIds = activityPropConfigList.stream().map(ActivityPropConfig::getPropId).collect(Collectors.toList());
            List<PropManage> propManageList = propManageService.selectPropManageByPropIds(propIds);
            Map<Long, PropManage> collect = propManageList.stream().collect(Collectors.toMap(PropManage::getPropId, Function.identity()));
            activityPropDtoList = activityPropConfigList.stream().map(e -> {
                PropManage propManage = collect.get(e.getPropId());
                ActivityPropDto activityPropDto = BeanUtil.copyBean(propManage, ActivityPropDto.class);
                activityPropDto.setTriggerLimit(e.getTriggerLimit());
                activityPropDto.setPropTypeDesc(ActivityPropTypeEnum.getDescByCode(propManage.getPropType()));
                activityPropDto.setEffectCodeDesc(ActivityPropEffectEnum.getDescByCode(propManage.getEffectCode()));
                return activityPropDto;
            }).collect(Collectors.toList());
        }
        return activityPropDtoList;
    }


    /**
     * 解析config
     *
     * @param jsonObject
     * @param officialDto
     */
    private void analysis(Map<String, Object> jsonObject, ActivityOfficialDto officialDto) {
        officialDto.setAdvertisingImage(MapUtil.getString(jsonObject.get(ApiConstants.ADVERTISING_IMAGE)));
        officialDto.setCoverImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
        officialDto.setActivityRule(MapUtil.getString(jsonObject.get("activityRule")));
        if (jsonObject.get("detailsImage") != null) {
            officialDto.setDetailsImage(JsonUtil.readList(jsonObject.get("detailsImage"), String.class));
        }
        officialDto.setRunningGoalsUnit(MapUtil.getInteger(jsonObject.get("runningGoalsUnit")));
        officialDto.setRateLimitingUnit(MapUtil.getInteger(jsonObject.get("rateLimitingUnit")));
        // 将限速数据根据单位转回原始数据

        officialDto.setHomeBanner(MapUtil.getString(jsonObject.get("homeBanner")));
        officialDto.setMaxRunScore(MapUtil.getInteger(jsonObject.get("maxRunScore"), -1));
        officialDto.setMinRunScore(MapUtil.getInteger(jsonObject.get("minRunScore"), -1));
        officialDto.setAdvertisingConfigImage(MapUtil.getString(jsonObject.get("advertisingConfigImage")));
        officialDto.setActivityObjectErrorDesc(MapUtil.getString(jsonObject.get("activityObjectErrorDesc")));
        officialDto.setRankRobotMode(MapUtil.getString(jsonObject.get("rankRobotMode")));
        officialDto.setChallengeScoreConsumer(MapUtil.getInteger(jsonObject.get("challengeScoreConsumer")));
        officialDto.setThemeActivityListBackGroundImageUrl(MapUtil.getString(jsonObject.get("themeActivityListBackGroundImageUrl")));
        //团队赛不需要
        if (!RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(officialDto.getActivityType())) {
            officialDto.setRateLimitMile(SportsDataUnit.conversionVelocity(new BigDecimal(officialDto.getRateLimiting()), officialDto.getRateLimitingUnit(), 4));
        }
        //TODO fixme
        if (officialDto.getActivityType() == 3) {
            officialDto.setOfficialChallengeAward(JsonUtil.readValue(jsonObject.get("officialChallengeAward")));
            officialDto.setOfficialChallengeCouponAward(JsonUtil.readValue(jsonObject.get("officialChallengeCouponAward")));
            officialDto.setOfficialChallengeScoreAward(JsonUtil.readValue(jsonObject.get("officialChallengeScoreAward")));
            officialDto.setOfficialEventAward(JsonUtil.readValue(jsonObject.get("officialEventAward")));
            officialDto.setOfficialEventScoreAward(JsonUtil.readValue(jsonObject.get("officialEventScoreAward")));
            officialDto.setOfficialEventCouponAward(JsonUtil.readValue(jsonObject.get("officialEventCouponAward")));
            officialDto.setBeChallengedAward(JsonUtil.readValue(jsonObject.get("beChallengedAward")));
            officialDto.setBeChallengedScoreAward(JsonUtil.readValue(jsonObject.get("beChallengedScoreAward")));
            officialDto.setBeChallengedCouponAward(JsonUtil.readValue(jsonObject.get("beChallengedCouponAward")));
            officialDto.setChallengeFailureAward(MapUtil.getBigDecimal(jsonObject.get("challengeFailureAward")));
            officialDto.setChallengeFailureScoreAward(MapUtil.getInteger(jsonObject.get("challengeFailureScoreAward")));
            officialDto.setChallengeFailureCouponAward(JsonUtil.readValue(jsonObject.get("challengeFailureCouponAward"), ActivityCouponDto.class));
            runningGoalToBigUnit(jsonObject, officialDto);
            officialDto.setMapRobotLimit(MapUtil.getInteger(jsonObject.get("mapRobotLimit")));
            setRobot(officialDto, jsonObject);
        } else if (officialDto.getActivityType() == 4) {
            runningGoalToBigUnit(jsonObject, officialDto);

            List<Map> list = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
            List<Map<String, Object>> runningGoalsAward = getRunningGoals(list, officialDto);
            officialDto.setRunningGoalsAward(runningGoalsAward);

            Object scoreAwardMapObj = jsonObject.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD);
            if (Objects.nonNull(scoreAwardMapObj)) {
                List<Map> runningGoalScoreAwardMap = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_SCORE_AWARD), Map.class);
                List<Map<String, Object>> runningGoalScoreAward = getRunningGoals(runningGoalScoreAwardMap, officialDto);
                officialDto.setRunningGoalScoreAward(runningGoalScoreAward);
            }

            Object couponAwardMapObj = jsonObject.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD);
            if (Objects.nonNull(couponAwardMapObj)) {
                List<Map> runningGoalScoreAwardMap = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOAL_COUPON_AWARD), Map.class);
                List<Map<String, Object>> runningGoalScoreAward = getRunningGoals(runningGoalScoreAwardMap, officialDto);
                officialDto.setRunningGoalCouponAward(runningGoalScoreAward);
            }
            setRobot(officialDto, jsonObject);
        } else if (officialDto.getActivityType() == 5) {
            List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
            for (MilepostAwardDto awardDto : milepostAward) {
                if (officialDto.getRunningGoalsUnit() == 0) {
                    awardDto.setMilepost(awardDto.getMilepost().divide(new BigDecimal(1000)));
                } else if (officialDto.getRunningGoalsUnit() == 1) {
                    awardDto.setMilepost(awardDto.getMilepost().divide(new BigDecimal(1600)));
                } else if (officialDto.getRunningGoalsUnit() == 2) {
                    awardDto.setMilepost(awardDto.getMilepost().divide(new BigDecimal(60)));
                }
            }
            officialDto.setMilepostAward(milepostAward);

            if (Objects.nonNull(jsonObject) && JsonUtil.writeString(jsonObject).contains(ApiConstants.milepostCouponAward)) {
                List<MilepostAwardDto> milepostCouponAward = JsonUtil.readList(jsonObject.get(ApiConstants.milepostCouponAward), MilepostAwardDto.class);
                if (milepostCouponAward != null && milepostCouponAward.size() > 0) {
                    for (MilepostAwardDto awardDto : milepostCouponAward) {
                        if (officialDto.getRunningGoalsUnit() == 0) {
                            awardDto.setMilepost(awardDto.getMilepost().divide(new BigDecimal(1000)));
                        } else if (officialDto.getRunningGoalsUnit() == 1) {
                            awardDto.setMilepost(awardDto.getMilepost().divide(new BigDecimal(1600)));
                        } else if (officialDto.getRunningGoalsUnit() == 2) {
                            awardDto.setMilepost(awardDto.getMilepost().divide(new BigDecimal(60)));
                        }
                    }
                    officialDto.setMilepostCouponAward(milepostCouponAward);
                }
            }

            if (Objects.nonNull(jsonObject) && JsonUtil.writeString(jsonObject).contains(ApiConstants.milepostWearsAward)) {
                List<MilepostWearAwardDto> milepostWearsAwards = JsonUtil.readList(jsonObject.get(ApiConstants.milepostWearsAward), MilepostWearAwardDto.class);
                if (milepostWearsAwards != null && milepostWearsAwards.size() > 0) {
                    milepostWearsAwards.forEach(wearsAward -> {
                        if (officialDto.getRunningGoalsUnit() == 0) {
                            wearsAward.setMilepost(wearsAward.getMilepost().divide(new BigDecimal(1000)));
                        } else if (officialDto.getRunningGoalsUnit() == 1) {
                            wearsAward.setMilepost(wearsAward.getMilepost().divide(new BigDecimal(1600)));
                        } else if (officialDto.getRunningGoalsUnit() == 2) {
                            wearsAward.setMilepost(wearsAward.getMilepost().divide(new BigDecimal(60)));
                        }
                    });
                    officialDto.setMilepostWearsAward(milepostWearsAwards);
                }

            }


        }
        //品牌权益处理
        List<BrandRightsInterestListDto> brandRightsInterestListDtos = activityBrandRightsInterestsService.selectActivityBrandRightsInterestsByActivityId(officialDto.getId());
        officialDto.setBrandRightsInterestList(brandRightsInterestListDtos);
        //配速员处理
        List<PacerDto> pacerDtos = pacerConfigService.selectPacerConfigByActivityId(officialDto.getId());
        officialDto.setPacerDtos(pacerDtos);
    }

    private List<Map<String, Object>> getRunningGoals(List<Map> list, ActivityOfficialDto officialDto) {
        List<Map<String, Object>> runningGoalsAward = new ArrayList<>();
        for (Map<String, Object> map : list) {
            //转换大单位
            Double goal = org.apache.commons.collections4.MapUtils.getDouble(map, "goal");
            if (officialDto.getRunningGoalsUnit() == 0) {
                Double key = BigDecimalUtil.divHalfUp(new BigDecimal(goal), new BigDecimal(1000), 4).doubleValue();
                map.put("goal", key);
            } else if (officialDto.getRunningGoalsUnit() == 1) {
                Double key = BigDecimalUtil.divHalfUp(new BigDecimal(goal), new BigDecimal(1600), 4).doubleValue();
                map.put("goal", key);
            } else if (officialDto.getRunningGoalsUnit() == 2) {
                Double key = goal / 60;
                map.put("goal", key);
            }
            MapUtils.getDouble(map, "goal");
            runningGoalsAward.add(map);
        }
        return runningGoalsAward;
    }

    private void setRobot(ActivityOfficialDto officialDto, Map<String, Object> jsonObject) {
        officialDto.setRobotSCount(MapUtil.getInteger(jsonObject.get("SRobotCount")));
        officialDto.setRobotSCount(MapUtil.getInteger(jsonObject.get("SRobotCount")));
        officialDto.setRobotACount(MapUtil.getInteger(jsonObject.get("ARobotCount")));
        officialDto.setRobotBCount(MapUtil.getInteger(jsonObject.get("BRobotCount")));
        officialDto.setRobotCCount(MapUtil.getInteger(jsonObject.get("CRobotCount")));
        officialDto.setRobotDCount(MapUtil.getInteger(jsonObject.get("DRobotCount")));
        officialDto.setRobotSPlusCount(MapUtil.getInteger(jsonObject.get("SPlusRobotCount")));

        officialDto.setRobotSCountStart(MapUtil.getInteger(jsonObject.get("SRobotCountStart")));
        officialDto.setRobotACountStart(MapUtil.getInteger(jsonObject.get("ARobotCountStart")));
        officialDto.setRobotBCountStart(MapUtil.getInteger(jsonObject.get("BRobotCountStart")));
        officialDto.setRobotCCountStart(MapUtil.getInteger(jsonObject.get("CRobotCountStart")));
        officialDto.setRobotDCountStart(MapUtil.getInteger(jsonObject.get("DRobotCountStart")));
        officialDto.setRobotSPlusCountStart(MapUtil.getInteger(jsonObject.get("SPlusRobotCountStart")));

        officialDto.setRobotSCountEnd(MapUtil.getInteger(jsonObject.get("SRobotCountEnd")));
        officialDto.setRobotACountEnd(MapUtil.getInteger(jsonObject.get("ARobotCountEnd")));
        officialDto.setRobotBCountEnd(MapUtil.getInteger(jsonObject.get("BRobotCountEnd")));
        officialDto.setRobotCCountEnd(MapUtil.getInteger(jsonObject.get("CRobotCountEnd")));
        officialDto.setRobotDCountEnd(MapUtil.getInteger(jsonObject.get("DRobotCountEnd")));
        officialDto.setRobotSPlusCountEnd(MapUtil.getInteger(jsonObject.get("SPlusRobotCountEnd")));

        officialDto.setRobotE1Count(MapUtil.getInteger(jsonObject.get("E1RobotCount")));
        officialDto.setRobotE2Count(MapUtil.getInteger(jsonObject.get("E2RobotCount")));
        officialDto.setRobotE3Count(MapUtil.getInteger(jsonObject.get("E3RobotCount")));
        officialDto.setRobotE4Count(MapUtil.getInteger(jsonObject.get("E4RobotCount")));
        officialDto.setRobotE5Count(MapUtil.getInteger(jsonObject.get("E5RobotCount")));
        officialDto.setRobotE6Count(MapUtil.getInteger(jsonObject.get("E6RobotCount")));

        officialDto.setRobotE1CountStart(MapUtil.getInteger(jsonObject.get("E1RobotCountStart")));
        officialDto.setRobotE2CountStart(MapUtil.getInteger(jsonObject.get("E2RobotCountStart")));
        officialDto.setRobotE3CountStart(MapUtil.getInteger(jsonObject.get("E3RobotCountStart")));
        officialDto.setRobotE4CountStart(MapUtil.getInteger(jsonObject.get("E4RobotCountStart")));
        officialDto.setRobotE5CountStart(MapUtil.getInteger(jsonObject.get("E5RobotCountStart")));
        officialDto.setRobotE6CountStart(MapUtil.getInteger(jsonObject.get("E6RobotCountStart")));

        officialDto.setRobotE1CountEnd(MapUtil.getInteger(jsonObject.get("E1RobotCountEnd")));
        officialDto.setRobotE2CountEnd(MapUtil.getInteger(jsonObject.get("E2RobotCountEnd")));
        officialDto.setRobotE3CountEnd(MapUtil.getInteger(jsonObject.get("E3RobotCountEnd")));
        officialDto.setRobotE4CountEnd(MapUtil.getInteger(jsonObject.get("E4RobotCountEnd")));
        officialDto.setRobotE5CountEnd(MapUtil.getInteger(jsonObject.get("E5RobotCountEnd")));
        officialDto.setRobotE6CountEnd(MapUtil.getInteger(jsonObject.get("E6RobotCountEnd")));
    }

    /**
     * 跑量目标转换成公里
     *
     * @param jsonObject
     * @param officialDto
     */
    private void runningGoalToBigUnit(Map<String, Object> jsonObject, ActivityOfficialDto officialDto) {
        List<Integer> list = JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class);
        List<Double> runningGoals = new ArrayList<>();
        for (Integer g : list) {
            Double intValue = 0.0;
            if (officialDto.getCompleteRuleType() == 1) {
                intValue = SportsDataUnit.conversionVelocity(new BigDecimal(g), officialDto.getRunningGoalsUnit(), 4).doubleValue();
            } else if (officialDto.getCompleteRuleType() == 2) {
                intValue = Double.valueOf(g) / Double.valueOf(60);
            } else {
                intValue = SportsDataUnit.conversionVelocity(new BigDecimal(g), officialDto.getRunningGoalsUnit(), 4).doubleValue();
            }
            runningGoals.add(intValue);
        }
        officialDto.setRunningGoals(runningGoals);
    }


    /**
     * 获得目标里程
     *
     * @param activityConfig
     * @return
     */
    public BigDecimal calTargetMile(String activityConfig) {
        if (!StringUtils.hasText(activityConfig)) {
            return null;
        }
        ActivityConfigResp activityConfigResp = JsonUtil.readValue(activityConfig, ActivityConfigResp.class);
        if (activityConfigResp == null) {
            return null;
        }
        List<TaskResp> tasks = activityConfigResp.getTasks();
        if (CollectionUtils.isEmpty(tasks)) {
            return null;
        }
        List<BigDecimal> targetMileList = tasks.stream().map(taskResp -> {
            BigDecimal mileageTarget = taskResp.getMileageTarget();
            if (mileageTarget == null) {
                return BigDecimal.ZERO;
            }
            return mileageTarget;
        }).collect(Collectors.toList());
        BigDecimal runMileage = BigDecimal.ZERO;
        for (BigDecimal targetMile : targetMileList) {
            runMileage = runMileage.add(targetMile);
        }
        return runMileage;
    }


    /**
     * 活动用户简单信息
     *
     * @param activityId
     * @param pageSize
     * @return
     */
    public List<UserSimpleVo> findActivitySimpleUser(Long activityId, int pageSize) {
        ZnsRunActivityEntity activity = runActivityService.findById(activityId);
        return runActivityUserService.findActivitySimpleUser(activityId, pageSize, activity.getActivityType());
    }

    //活动创建

    /**
     * 校验跑步活动参数
     */
    public Result checkRunActivityParams(RunActivityRequest runActivity, ZnsUserEntity user) {
        // 校验活动类型是否存在
        if (null == runActivity.getActivityConfigId()) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.configIdEmpty")); //The activity configuration ID parameter does not exist
        }
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.findRunActivityConfig(runActivity.getActivityConfigId());
        if (null == activityConfig) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.configEmpty")); //"Active configuration does not exist"
        }

        // 1. 校验路线图是否存在
        if (null == runActivity.getActivityRouteId()) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.roadMap.empty"));
        }
        ZnsRunRouteEntity runRouteEntity = runRouteService.selectRunRouteById(runActivity.getActivityRouteId());
        if (null == runRouteEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.roadMap.selectedEmpty"));
        }
        // 2. 校验跑步开始时间
        if (activityConfig.getActivityType() == 1) {
            if (null == runActivity.getActivityStartTime()) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.startTime.empty"));
            }
            ZonedDateTime activityStartTime = new Date(runActivity.getActivityStartTime());
            if (activityStartTime.isBefore(ZonedDateTime.now())) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.starTime.tooEarlier"));
            }
        }


        // 3. 组队跑完成规则
        if (null != runActivity.getCompleteRuleType() && 1 == runActivity.getCompleteRuleType()) {
            // 1表示完成跑步里程
            if (null == runActivity.getRunMileage()) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.mileage.empty"));
            }
        } else if (null != runActivity.getCompleteRuleType() && 2 == runActivity.getCompleteRuleType()) {
            // 2表示完成跑步时长
            if (null == runActivity.getRunTime()) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.duration.empty"));
            }
        } else {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.completion.rule.empty"));
        }
        // 4. 设置奖金池规则
        if (null != runActivity.getBonusRuleType() && 1 == runActivity.getBonusRuleType()) {
            // 4.1表示免费参加
        } else if (null != runActivity.getBonusRuleType() && 2 == runActivity.getBonusRuleType()) {
            // 4.2表示保证金参加
            if (null == runActivity.getActivityEntryFee()) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.margin.empty"));
            }
            // 4.3 校验密码是否正确（机器人发起的活动不需要校验密码）
            if (0 == runActivity.getIsRobotStart()) {
                // 校验密码是否正确
                Result passwordResult = userAccountService.checkPassword(user.getId(), runActivity.getPassword(), false);
                if (null != passwordResult) {
                    passwordResult.setData(null);
                    return passwordResult;
                }
            }
        } else {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.bonusPoolRule.empty"));
        }
        // 5. 是否选择跑友
        if (CollectionUtils.isEmpty(runActivity.getActivityUserIds())) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.runner.empty"));
        }
        // 6. 挑战跑对象只能选一个
        if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityConfig.getActivityType())) {
            if (1 != runActivity.getActivityUserIds().size()) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.challenge.exceed"));
            }
        }
        return null;
    }

    /**
     * 校验邀请用户
     */
    public Result checkInviteRunner(InviteRunnerRequest request) {
        if (null == request.getActivityId()) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        ZnsRunActivityEntity activityEntity = runActivityService.findById(request.getActivityId());
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        // 截止活动开始30分钟后不能再邀请
        ZonedDateTime deadline = activityEntity.getActivityStartTime().plusMinutes(30);
        if (deadline.isBefore(ZonedDateTime.now())) {
            log.info("checkInviteRunner end 当前时间不能再邀请用户");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.link.expired"));
        }
        if (CollectionUtils.isEmpty(request.getActivityUserIds())) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.user.empty"));
        }
        return null;
    }


    /**
     * 修改可见范围
     *
     * @param file
     * @param po
     * @return
     */
    public Result visibleRange(MultipartFile file, VisibleRangePo po) {
        if (Objects.isNull(po.getActivityId())) {
            return CommonResult.fail("活动id不能为空!");
        }
        if (Objects.isNull(po.getIsShowAll())) {
            return CommonResult.fail("是否展示全部不能为空!");
        }
        if (po.getIsShowAll() == 1 && StringUtils.isEmpty(po.getCountry())) {
            //全部可见-国家不能为空
            return CommonResult.fail("投放国家不能为空！");
        }
        if (po.getIsShowAll() == 0) {
            po.setCountry(UserConstant.ALL_COUNTRY);
        }


        ZnsRunActivityEntity updateRunActivity = new ZnsRunActivityEntity();
        updateRunActivity.setId(po.getActivityId());
        updateRunActivity.setIsShowAll(po.getIsShowAll());
        updateRunActivity.setCountry(Optional.ofNullable(po.getCountry()).orElse(UserConstant.ALL_COUNTRY));

        runActivityService.updateById(updateRunActivity);

        if (Objects.isNull(file)) {
            return CommonResult.success();
        }
        ZnsRunActivityEntity runActivity = runActivityService.selectActivityById(po.getActivityId());
        //可见范围修改
        //删除原配置
        runActivityShowUserService.deleteByActivityId(po.getActivityId());

        if (po.getIsShowAll() == 1) {
            return CommonResult.success();
        }

        runActivityShowUserService.addActivityShowUser(po.getActivityId(), file, runActivity.getBatchNo());
        return CommonResult.success();
    }
}
