package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.biz.RunCheatBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.CheatTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.DetailsCheatStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.NoLoadJudgeTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.PropRankedActivityConfigDto;
import com.linzi.pitpat.data.activityservice.model.dto.BraceletCheatRuleResponseDto;
import com.linzi.pitpat.data.activityservice.model.dto.CheatConfigResponseDto;
import com.linzi.pitpat.data.activityservice.model.dto.CheatElectricityRuleDto;
import com.linzi.pitpat.data.activityservice.model.dto.CheatRiskLogDto;
import com.linzi.pitpat.data.activityservice.model.dto.CheatUserWhiteSwitchDto;
import com.linzi.pitpat.data.activityservice.model.dto.ElectricityCheatDataDto;
import com.linzi.pitpat.data.activityservice.model.dto.ElectricityCheatSyncDataDto;
import com.linzi.pitpat.data.activityservice.model.dto.PreventionCheatRuleResponseDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityCheat;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataDetailsCheat;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsCheatQuery;
import com.linzi.pitpat.data.activityservice.model.request.RiskCheatRequestDto;
import com.linzi.pitpat.data.activityservice.model.resp.RiskCheatResponseDto;
import com.linzi.pitpat.data.activityservice.model.vo.RunEndActivityVo;
import com.linzi.pitpat.data.activityservice.service.CheatUserWhiteService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RunActivityCheatService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.mongo.DynamicCollection;
import com.linzi.pitpat.data.mongo.Id.SnowflakeId;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/3 11:32
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RunCheatManager {
    private static final int ABNORMAL_CURRENT_DURATION = 10;
    private static final int FIRST_PHASE_LIMIT = 16;  // 第一阶段计算次数限制,包括初始后台配置的那一次
    private static final int SECOND_PHASE_LIMIT = 5;   // 第二阶段计算次数限制
    private static final int FINAL_PHASE_LIMIT = 2;     // 最终阶段计算次数限制
    private static final int FIRST_PHASE_INTERVAL = 4; // 5秒
    private static final int SECOND_PHASE_INTERVAL = 10; // 10秒
    private static final int FINAL_PHASE_INTERVAL = 20; // 20秒
    private static final String CHEAT_RISK_URI_TEST = "http://172.16.157.240:5200/risk_api";
    private static final String CHEAT_RISK_URI_ONLINE = "http://172.31.16.62:5000/risk_api";

    private final ZnsUserService userService;
    private final ISysConfigService sysConfigService;
    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    private final RunActivityCheatService runActivityCheatService;
    private final MainActivityService mainActivityService;
    private final UserIdentityService userIdentityService;
    private final AppMessageService appMessageService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final CheatUserWhiteService cheatUserWhiteService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ZnsTreadmillService treadmillService;
    private final MongoTemplate mongoTemplate;
    private final RunCheatBizService runCheatBizService;
    @Value("${" + RabbitQueueConstants.RUN_DATA_CHEAT_SYNC_QUEUE + "}")
    private String cheatDataSync;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    @Qualifier("dateDynamicCollection")
    private DynamicCollection<ZonedDateTime> dynamicCollection;
    @Resource(name = "asyncExecutor")
    private ThreadPoolTaskExecutor executor;
    /**
     * 风控超时延迟队列
     */
    @Value("${" + RabbitQueueConstants.RUN_DATA_DELAY_EXCHANGE + "}")
    private String run_data_delay_exchange;
    /**
     * 风控超时延迟队列
     */
    @Value("${" + RabbitQueueConstants.RISK_TIMEOUT_DELAY_KEY + "}")
    private String risk_timeout_delay_key;

    public Integer preventionCheatDeal(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityEntity, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities) {
        userRunDataDetailsCheatService.saveAndCheckRepeat(new UserRunDataDetailsCheat().setCheatType(CheatTypeEnum.BUSINESS.getType()).setIsCheat(DetailsCheatStateEnum.RISK.getState())
                .setUserId(userRunDataDetail.getUserId()).setRunDataDetailsId(userRunDataDetail.getId()));
        Integer cheatDeal = cheatDeal(userRunDataDetail, activityEntity, detailsSecondEntities);
        userRunDataDetailsCheatService.updateSelect(new UserRunDataDetailsCheatQuery().setCheatType(CheatTypeEnum.BUSINESS.getType()).setIsCheat(DetailsCheatStateEnum.RISK.getState()).setRunDataDetailsId(userRunDataDetail.getId()),
                new UserRunDataDetailsCheat().setIsCheat(Objects.isNull(cheatDeal) ? 0 : cheatDeal));
        return cheatDeal;
    }

    private Integer cheatDeal(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityEntity, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities) {
        log.info("preventionCheatDeal start,detailsId:{}", userRunDataDetail.getId());
        if (sysConfigService.isVirtualRecord(userRunDataDetail.getId())){
            log.info("虚拟设备不处理作弊");
            return 0;
        }

        if (userRunDataDetail.getRunStatus() != 1) {
            log.info("preventionCheatDeal 处理结束，跑步未结束不处理");
            return 0;
        }
        ZnsUserEntity znsUser = userService.findById(userRunDataDetail.getUserId());
        if (znsUser.getIsRobot() == 1) {
            return 0;
        }
        if (CollectionUtils.isEmpty(detailsSecondEntities)) {
            detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(userRunDataDetail.getId());
        }

        if (CollectionUtils.isEmpty(detailsSecondEntities)) {
            log.info("preventionCheatDeal 处理结束，运动数据为空");
            return 0;
        }

        Map<Integer, ZnsUserRunDataDetailsSecondEntity> collect = detailsSecondEntities.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsSecondEntity::getRunTime, Function.identity(), (x, y) -> y));
        detailsSecondEntities = collect.values().stream().sorted(Comparator.comparing(ZnsUserRunDataDetailsSecondEntity::getRunTime)).collect(Collectors.toList());

        CheatElectricityRuleDto cheatElectricityRule = getCheatElectricityRule();
        Integer riskCheatDeal = riskCheatDeal(userRunDataDetail, cheatElectricityRule);
        Integer electricityCheatCount = preElectricityCheatDeal(userRunDataDetail, detailsSecondEntities, cheatElectricityRule);

        if (Objects.isNull(activityEntity)) {
            log.info("preventionCheatDeal 处理结束，活动为空");
            return 0;
        }

        //作弊白名单处理
        if (checkUserWhite(znsUser.getId())) {
            log.info("preventionCheatDeal 处理结束，作弊白名单");
            return 0;
        }

        List<RunActivityCheat> list = runActivityCheatService.findList(activityEntity.getId());
        //新活动兼容
        if (!MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType())) {
            MainActivity mainActivity = mainActivityService.findById(activityEntity.getId());
            if (mainActivity.getCheatSwitch() != -1) {
                RunActivityCheat cheat = new RunActivityCheat();
                cheat.setCheatSwitch(mainActivity.getCheatSwitch() + 1);
                cheat.setActivityId(activityEntity.getId());
                list.add(cheat);
            }
            activityEntity.setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType());
        }
        Integer newCheatState = null;
        //@since 4.0.0 NPC不进行作弊检查
        if (!userIdentityService.isNpc(userRunDataDetail.getUserId())) {
            preventionHeartCheatDeal(activityEntity, userRunDataDetail, detailsSecondEntities, list);
            preventionStepCheatDeal(activityEntity, userRunDataDetail, detailsSecondEntities, list);
            newCheatState = preventionElectricityCheatDeal(activityEntity, userRunDataDetail, cheatElectricityRule, list, electricityCheatCount, riskCheatDeal);
        } else {
            newCheatState = 0;
        }

        runCheatBizService.cheatPush(userRunDataDetail, activityEntity);

        //无作弊状态表示风控超时，需记录
        if (Objects.isNull(newCheatState)) {
            newCheatState = saveTimeoutRiskCheat(userRunDataDetail, activityEntity, newCheatState);
        }
        return newCheatState;
    }

    private Integer saveTimeoutRiskCheat(ZnsUserRunDataDetailsEntity userRunDataDetail, ActivityTypeDto activityEntity, Integer newCheatState) {
        if (Objects.isNull(activityEntity) && activityEntity.getId() < 2) {
            log.info("saveTimeoutRiskCheat end, 无活动无需记录超时");
            return newCheatState;
        }
        UserRunDataDetailsCheat save = new UserRunDataDetailsCheat().setCheatType(CheatTypeEnum.RISK.getType()).setIsCheat(DetailsCheatStateEnum.RISK.getState())
                .setUserId(userRunDataDetail.getUserId()).setRunDataDetailsId(userRunDataDetail.getId());

        UserRunDataDetailsCheat detailsCheat = userRunDataDetailsCheatService.findOne(new UserRunDataDetailsCheatQuery()
                .setRunDataDetailsId(save.getRunDataDetailsId())
                .setCheatType(save.getCheatType()));

        if (Objects.nonNull(detailsCheat)) {
            log.info("saveTimeoutRiskCheat end, detailsCheat已存在");
            if (DetailsCheatStateEnum.CHEAT.getState().equals(detailsCheat.getIsCheat()) ||
                    DetailsCheatStateEnum.NO_CHEAT.getState().equals(detailsCheat.getIsCheat())) {
                return detailsCheat.getIsCheat();
            } else {
                return newCheatState;
            }
        }
        userRunDataDetailsCheatService.save(save);

        RunEndActivityVo vo = new RunEndActivityVo();
        vo.setUserRunDataDetail(userRunDataDetail);
        vo.setActivityTypeDto(activityEntity);
        // 部分赛事超时延时处理
        if (MainActivityTypeEnum.PROP.getType().equals(activityEntity.getMainType())
                || MainActivityTypeEnum.RANK.getType().equals(activityEntity.getMainType())
                || (MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType()) && RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activityEntity.getActivityType()))
                || (MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType()) && RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType()) && RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType().equals(activityEntity.getActivityTypeSub()))) {
            rabbitTemplate.convertAndSend(run_data_delay_exchange, risk_timeout_delay_key, JsonUtil.writeString(vo), message -> {
                message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                message.getMessageProperties().setDelay(60 * 1000);
                return message;
            });
        }
        return newCheatState;
    }


    private Integer preventionElectricityCheatDeal(ActivityTypeDto activityEntity, ZnsUserRunDataDetailsEntity userRunDataDetail, CheatElectricityRuleDto cheatElectricityRule, List<RunActivityCheat> cheatList, Integer electricityCheatCount, Integer riskCheatDeal) {
        log.info("preventionElectricityCheatDeal start,userId:{},activityId:{},electricityCheatCount:{},riskCheatDeal:{}", userRunDataDetail.getUserId(), activityEntity.getId(), electricityCheatCount, riskCheatDeal);
        if (Objects.isNull(cheatElectricityRule)) {
            log.info("preventionElectricityCheatDeal end,未配置作弊规则");
            return 0;
        }

        if (Objects.nonNull(activityEntity) && activityEntity.getId() >= 2) {
            if (MainActivityTypeEnum.PROP.getType().equals(activityEntity.getMainType())) {
                activityEntity.setActivityType(RunActivityTypeEnum.PROP_ACTIVITY.getType());
            } else if (MainActivityTypeEnum.RANK.getType().equals(activityEntity.getMainType())) {
                activityEntity.setActivityType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType());
            } else if (MainActivityTypeEnum.isFreeChallengeActivity(activityEntity.getMainType())) {
                activityEntity.setActivityType(RunActivityTypeEnum.FREE_CHALLENGE.getType());
            }else if (MainActivityTypeEnum.PLACEMENT.getType().equals(activityEntity.getMainType())) {
                activityEntity.setActivityType(RunActivityTypeEnum.PLACEMENT_ACTIVITY.getType());
            }

            RunActivityCheat runActivityCheat = cheatList.stream().filter(c -> c.getCheatSwitch() == 3).findFirst().orElse(null);
            if (RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType().equals(activityEntity.getActivityType()) && Objects.isNull(runActivityCheat)) {
                log.info("preventionElectricityCheatDeal end,作弊结果活动开关为关");
                return 0;
            }
        }
        boolean isCheatNoload = false;
        if (cheatElectricityRule.getResultCheatSwitch() == 1 && cheatElectricityRule.isCheatScene(cheatElectricityRule.getResultScene(), activityEntity.getActivityType(), activityEntity.getActivityTypeSub())) {
            isCheatNoload = Objects.nonNull(electricityCheatCount) && electricityCheatCount >= cheatElectricityRule.getCheatCount();
        }

        boolean isCheatRisk = false;
        if (cheatElectricityRule.getRiskAbnormalCheatSwitch() == 1 && cheatElectricityRule.isCheatScene(cheatElectricityRule.getRiskAbnormalCheatScene(), activityEntity.getActivityType(), activityEntity.getActivityTypeSub())) {
            isCheatRisk = Objects.equals(riskCheatDeal, 1);
            //防止新人pk产生超时数据
            if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType()) && RunActivitySubTypeEnum.getNewPersonPkType().contains(activityEntity.getActivityTypeSub())) {
                riskCheatDeal = isCheatRisk ? 1 : 0;
            }
        } else {
            // 没有命中开关配置规则不考虑风控结果，当没作弊处理
            riskCheatDeal = 0;
        }

        if (isCheatNoload || isCheatRisk) {
            userRunDataDetail.setIsCheat(1);
            ZnsUserRunDataDetailsEntity cheatUpdate = new ZnsUserRunDataDetailsEntity();
            cheatUpdate.setId(userRunDataDetail.getId());
            cheatUpdate.setIsCheat(1);
            userRunDataDetailsService.update(cheatUpdate);
            return 1;
        }
        return riskCheatDeal;
    }

    private CheatElectricityRuleDto getCheatElectricityRule() {
        SysConfig cheatElectricityRule = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.CHEAT_ELECTRICITY_RULE.getCode());
        if (Objects.isNull(cheatElectricityRule)) {
            return null;
        }
        return JsonUtil.readValue(cheatElectricityRule.getConfigValue(), CheatElectricityRuleDto.class);
    }

    /**
     * 电流作弊检测
     *
     * @param userRunDataDetail
     * @param detailsSecondEntities
     * @param cheatElectricityRule
     * @return null 表示没有判定出结果，其他返回作弊次数
     */
    private Integer preElectricityCheatDeal(ZnsUserRunDataDetailsEntity userRunDataDetail, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities, CheatElectricityRuleDto cheatElectricityRule) {
        if (Objects.isNull(cheatElectricityRule)) {
            log.info("preventionElectricityCheatDeal end,未配置作弊规则");
            return null;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        detailsSecondEntities = detailsSecondEntities.stream().filter(s -> (s.getRunType() == 1 || s.getRunType() == 2) && s.getRealRotate() > 0).collect(Collectors.toList());
        if (detailsSecondEntities.size() <= 50) {
            syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                    .setMsg("有电流值数量过少").setData(detailsSecondEntities.size()), "1-有电流值数量过少");
            stopWatch.stop();
            return null;
        }
        try {
            //数据初始化
            Map<BigDecimal, List<Integer>> loadCurrentBuffer = new HashMap<>(); // 存储每个速度对应的负载电流值
            Map<String, Integer> loadCurrentAverage = new HashMap<>(); // 存储每个速度对应的负载电流平均值
            List<ElectricityCheatDataDto> noLoadCurrentBuffer = new ArrayList<>(); //空载连续数据
            Map<String, Integer> noLoadCurrentAverage = new HashMap<>(); // 存储每个速度对应的空载电流平均值
            Integer noLoadCurrentCheckTime = cheatElectricityRule.getAbnormalContinuousTime(); // 检测异常连续时间
            int calculationCount = 0; //同一段空载数据的计算次数
            int cheatCount = 0; // 累计作弊次数
            int noLoadContinuousCheatCount = 0; // 空载累计作弊次数
            int realNoLoadDuration = 0; // 空载连续时间
            int realabnormalCurrentLastTime = 0;
            Integer maxNoLoadTime = 0; //最大持续空载
            int realNoLoadCurrentZeroDuration = 0; // 非空载电流零值持续时间
            int realNoLoadCurrentZeroLastTime = 0; // 非空载电流零值上次时间
            int electricityLargeCount = 0; // 异常电流次数，电流异常大
            boolean lastNoLoad = false;

            for (int i = 0; i < detailsSecondEntities.size(); i++) {
                ZnsUserRunDataDetailsSecondEntity detailsSecondEntity = detailsSecondEntities.get(i);
                //数据异常检测，判断设备是否支持电流数据
                if (detailsSecondEntity.getRealElectricity() == 0) {
                    int timeDiff = detailsSecondEntity.getRunTime() - realNoLoadCurrentZeroLastTime;
                    realNoLoadCurrentZeroDuration += timeDiff;
                    if (realNoLoadCurrentZeroDuration >= ABNORMAL_CURRENT_DURATION) {
                        syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                                .setMsg("连续零值电流值数量到达阙值").setData(realNoLoadCurrentZeroDuration).setRun_time(detailsSecondEntity.getRunTime()), "1-连续零值电流值数量到达阙值，设备数据不支持作弊检测");
                        stopWatch.stop();
                        return null;
                    }
                } else {
                    realNoLoadCurrentZeroDuration = 0;
                }

                realNoLoadCurrentZeroLastTime = detailsSecondEntity.getRunTime();

                if (detailsSecondEntity.getRunTime() < cheatElectricityRule.getCheckStartTime()) {
                    realabnormalCurrentLastTime = detailsSecondEntity.getRunTime();
                    continue;
                }
                if (detailsSecondEntity.getNoLiveLoad() == 0) { //负载
                    if (detailsSecondEntity.getRealElectricity() <= 0) {
                        log.info("preventionElectricityCheatDeal 电流数据异常，不生效");
                        continue;
                    }
                    if (realNoLoadDuration > 0) {
                        syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                                .setMsg("空载持续时间小于" + noLoadCurrentCheckTime + "s").setData(realNoLoadDuration).setRun_time(detailsSecondEntity.getRunTime()), "5-空载持续时间小于" + noLoadCurrentCheckTime + "s");
                    }
                    //负载破坏空载连续，数据初始化
                    noLoadCurrentBuffer.clear();
                    noLoadCurrentCheckTime = cheatElectricityRule.getAbnormalContinuousTime();
                    calculationCount = 0;
                    realNoLoadDuration = 0;
                    maxNoLoadTime = 0;
                    lastNoLoad = false;

                    //空载前10s排除
                    long noLiveLoadCount = detailsSecondEntities.stream().filter(s -> s.getRunTime() > detailsSecondEntity.getRunTime() && s.getRunTime() - 10 <= detailsSecondEntity.getRunTime()).filter(s -> s.getNoLiveLoad() == 1).count();
                    if (noLiveLoadCount > 0) {
                        log.info("preventionElectricityCheatDeal 空载前10s负载数据不生效,runTime:{}", detailsSecondEntity.getRunTime());
                        realabnormalCurrentLastTime = detailsSecondEntity.getRunTime();
                        continue;
                    }
                    loadCurrentBuffer.putIfAbsent(detailsSecondEntity.getVelocity(), new ArrayList<>());
                    loadCurrentBuffer.get(detailsSecondEntity.getVelocity()).add(detailsSecondEntity.getRealElectricity());

                    // 仅保留最近30条数据
                    if (loadCurrentBuffer.get(detailsSecondEntity.getVelocity()).size() > 30) {
                        loadCurrentBuffer.get(detailsSecondEntity.getVelocity()).remove(0);
                    }
                    double v = loadCurrentBuffer.get(detailsSecondEntity.getVelocity()).stream().mapToInt(Integer::intValue).average().orElse(0);
                    loadCurrentAverage.put(detailsSecondEntity.getVelocity().toString(), (int) v);
                } else { //空载
                    if (detailsSecondEntity.getRealElectricity() > 0) {
                        //空载数据累加
                        noLoadCurrentBuffer.add(new ElectricityCheatDataDto(detailsSecondEntity.getVelocity(), detailsSecondEntity.getRealElectricity()));
                        double v = noLoadCurrentBuffer.stream().filter(l -> l.getVelocity().equals(detailsSecondEntity.getVelocity())).map(ElectricityCheatDataDto::getRealElectricity).mapToInt(Integer::intValue).average().orElse(0);
                        noLoadCurrentAverage.put(detailsSecondEntity.getVelocity().toString(), (int) v);
                    }

                    int timeDiff = detailsSecondEntity.getRunTime() - realabnormalCurrentLastTime;
                    if (!lastNoLoad) {
                        //如果上一条数据不是空载，timeDiff只计算为1，防止过量计算
                        timeDiff = 1;
                    }
                    realNoLoadDuration += timeDiff;
                    maxNoLoadTime += timeDiff;
                    //连续空载时间判定
                    if (realNoLoadDuration >= noLoadCurrentCheckTime) {
                        Integer judge = realNoLoadJudge(noLoadCurrentBuffer, loadCurrentAverage, loadCurrentBuffer,
                                userRunDataDetail, detailsSecondEntity, cheatElectricityRule, noLoadCurrentAverage);

                        realNoLoadDuration = 0;
                        calculationCount++;
                        if (NoLoadJudgeTypeEnum.NO_LOAD.getType().equals(judge)) {
                            cheatCount++;
                        } else if (NoLoadJudgeTypeEnum.ELEC_LARGE.getType().equals(judge)) {
                            electricityLargeCount++;
                        }
                        if (electricityLargeCount >= cheatElectricityRule.getAbnormalLargeElectricityCount()) {
                            break;
                        }
                        // 根据计算次数调整计算频率
                        if (calculationCount < FIRST_PHASE_LIMIT) {
                            noLoadCurrentCheckTime = FIRST_PHASE_INTERVAL;
                        } else {
                            noLoadCurrentCheckTime = SECOND_PHASE_INTERVAL;
                        }
                    } else if (i == detailsSecondEntities.size() - 1) {
                        syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                                .setMsg("空载持续时间小于" + noLoadCurrentCheckTime + "s").setData(realNoLoadDuration).setRun_time(detailsSecondEntity.getRunTime()), "5-空载持续时间小于" + noLoadCurrentCheckTime + "s");
                    }

                    //最大空载时间判定
                    if (NumberUtils.geZero(cheatElectricityRule.getMaxAbnormalContinuousTime()) && maxNoLoadTime >= cheatElectricityRule.getMaxAbnormalContinuousTime()) {
                        syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                                .setMsg("空载持续时间大于" + cheatElectricityRule.getMaxAbnormalContinuousTime() + "s").setData(maxNoLoadTime).setRun_time(detailsSecondEntity.getRunTime()), "空载持续时间大于" + cheatElectricityRule.getMaxAbnormalContinuousTime() + "s");
                        noLoadContinuousCheatCount++;
                        maxNoLoadTime = 0;
                    }
                    lastNoLoad = true;
                }
                realabnormalCurrentLastTime = detailsSecondEntity.getRunTime();
            }
            cheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                    .setMsg("本次跑步作弊判定次数记录").setData("ruleCheatCount=" + cheatElectricityRule.getCheatCount() + ", noLoadContinuousCheatCount=" + noLoadContinuousCheatCount + ", realCheatCount=" + cheatCount), "3-空跑作弊判定结果");
            stopWatch.stop();

            if (electricityLargeCount >= cheatElectricityRule.getAbnormalLargeElectricityCount()) {
                log.info("preElectricityCheatDeal------电流作弊计算结束,runId={},总耗时={}s,cheatCount={},noLoadContinuousCheatCount={},实际返回作弊次数0，原因：电流异常大次数{}大于配置{}",
                        userRunDataDetail.getId(), stopWatch.getTotalTimeSeconds(), cheatCount, noLoadContinuousCheatCount, electricityLargeCount, cheatElectricityRule.getAbnormalLargeElectricityCount());
                return 0;
            }
            log.info("preElectricityCheatDeal------电流作弊计算结束,runId={},总耗时={}s,cheatCount={},noLoadContinuousCheatCount={}",
                    userRunDataDetail.getId(), stopWatch.getTotalTimeSeconds(), cheatCount, noLoadContinuousCheatCount);
            return cheatCount > noLoadContinuousCheatCount ? cheatCount : noLoadContinuousCheatCount;
        } catch (Exception e) {
            log.info("preElectricityCheatDeal error", e);
            return 0;
        }
    }

    private Integer riskCheatDeal(ZnsUserRunDataDetailsEntity runDataDetail, CheatElectricityRuleDto cheatElectricityRule) {
        String collectionName = dynamicCollection.getTargetCollectionName(MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_SECOND, runDataDetail.getCreateTime());
        RiskCheatRequestDto requestDto = new RiskCheatRequestDto();
        requestDto.setRunId(runDataDetail.getId());
        requestDto.setCollectionName(collectionName);
        try {
            ZnsTreadmillEntity treadmill = treadmillService.findById(runDataDetail.getTreadmillId());
            if (Objects.isNull(treadmill)) {
                log.info("riskCheatDeal------风控作弊判定结束，设备不存在");
                return 0;
            }
            requestDto.setModel(treadmill.getProductCode());

            if (!Objects.equals(cheatElectricityRule.getRiskAbnormalCheatSwitch(), 1)) {
                log.info("riskCheatDeal------风控作弊判定结束，开关为关");
                return 0;
            }

            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();

            Future<RiskCheatResponseDto> submit = executor.submit(() -> {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                String uri = SpringContextUtils.isOnline() ? CHEAT_RISK_URI_ONLINE : CHEAT_RISK_URI_TEST;
                String res = RestTemplateUtil.post(uri, requestDto);
                return JsonUtil.readValue(res, RiskCheatResponseDto.class);
            });

            RiskCheatResponseDto response = submit.get(5, TimeUnit.SECONDS);
            syncCheatLog(new CheatRiskLogDto().setRun_id(runDataDetail.getId()).setUser_id(runDataDetail.getUserId())
                    .setMsg("风控作弊判定结果").setData(JsonUtil.writeString(response)), "3-作弊判定结果");

            if (response.is_cheat()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.info("riskCheatDeal------作弊判定异常", e);
            syncCheatLog(new CheatRiskLogDto().setRun_id(runDataDetail.getId()).setUser_id(runDataDetail.getUserId())
                    .setMsg("风控请求超时").setData(requestDto), "3-作弊判定结果");
        }
        return null;
    }

    /**
     * 空跑判定处理
     */
    private Integer realNoLoadJudge(List<ElectricityCheatDataDto> noLoadCurrentBuffer, Map<String, Integer> loadCurrentAverage, Map<BigDecimal, List<Integer>> loadCurrentBuffer, ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserRunDataDetailsSecondEntity detailsSecondEntity, CheatElectricityRuleDto cheatElectricityRule, Map<String, Integer> noLoadCurrentAverage) {
        Integer isNoLoadType = NoLoadJudgeTypeEnum.UNABLE_JUDGE.getType();
        ElectricityCheatSyncDataDto electricMapDto = new ElectricityCheatSyncDataDto();
        BigDecimal velocity = noLoadCurrentBuffer.stream()
                .map(ElectricityCheatDataDto::getVelocity)
                .max(BigDecimal::compareTo)
                .orElse(null);

        if (velocity == null) {
            // 处理 noLoadCurrentBuffer 为空的情况
            syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                    .setMsg("无法判断-无空载速度数据").setData(noLoadCurrentBuffer).setRun_time(detailsSecondEntity.getRunTime()), "2-无空载速度数据，无法计算");
            return isNoLoadType;
        }
        Integer loadRealElectricity = loadCurrentAverage.get(velocity.toString());
        electricMapDto.setNormalEleAvg(loadCurrentAverage);
        electricMapDto.setNoLiveEleAvg(noLoadCurrentAverage);
        //获取速度对于负载的平均电流
        List<Integer> realElectricityList = loadCurrentBuffer.get(velocity);
        if (CollectionUtils.isEmpty(realElectricityList)) {
            syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                    .setMsg("无法判断-无正常跑电流数据").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "2-正常负载电流数据为空，无法计算");
        } else if (realElectricityList.size() < 10) {
            syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                    .setMsg("无法判断-正常跑数据量较少").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "2-正常负载电流数据不充足，无法计算");
        } else {
            if (loadRealElectricity <= 0) {
                syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                        .setMsg("无法判断-正常跑负载电流为0").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "2-正常负载电流数据不充足，无法计算");
            }
            double noLoadRealElectricity = noLoadCurrentBuffer.stream().mapToInt(ElectricityCheatDataDto::getRealElectricity).average().orElse(0);
            if (noLoadRealElectricity > 0) {
                Integer noLoadMinElectricity = new BigDecimal(loadRealElectricity).multiply(cheatElectricityRule.getAbnormalElectricityRate()).intValue();
                Integer noLoadMinAbnormalElectricity = new BigDecimal(loadRealElectricity).multiply(cheatElectricityRule.getAbnormalLargeElectricityRate()).intValue();
                if (noLoadRealElectricity <= noLoadMinElectricity) {
                    isNoLoadType = NoLoadJudgeTypeEnum.NO_LOAD.getType();
                    log.info("preventionElectricityCheatDeal，日志3 检测到用户空跑，速度：{},当前空载电流：{},正常负载电流：{},作弊浮动电流比：{}", velocity, noLoadRealElectricity, loadRealElectricity, cheatElectricityRule.getAbnormalElectricityRate());
                    syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                            .setMsg("用户空跑").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "3-检测到用户空跑");
                } else {
                    syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                            .setMsg("空载电流比较大").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "4-空载电流较大");
                    if (noLoadRealElectricity >= noLoadMinAbnormalElectricity) {
                        isNoLoadType = NoLoadJudgeTypeEnum.ELEC_LARGE.getType();
                        syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                                .setMsg("设备空载异常").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "7-设备空载异常");
                    }
                }
            } else {
                syncCheatLog(new CheatRiskLogDto().setRun_id(userRunDataDetail.getId()).setUser_id(userRunDataDetail.getUserId())
                        .setMsg("无法判断-空载平均电流为0").setData(electricMapDto).setRun_time(detailsSecondEntity.getRunTime()), "2-空载电流数据小于等于0，无法计算");
            }
        }
        return isNoLoadType;
    }

    private void syncCheatLog(CheatRiskLogDto ratioLogDto, String message) {
        // 风控只有线上消费数据，只同步线上
        if (EnvUtils.isReallyOnline(SpringContextUtils.getActiveProfile())) {
            rabbitTemplate.convertAndSend(cheatDataSync, JsonUtil.writeString(ratioLogDto));
        }
        cheatLog(ratioLogDto, message);

    }

    private void cheatLog(CheatRiskLogDto ratioLogDto, String message) {
        RunCheatManager.log.info("preventionElectricityCheatDeal end 日志:{}", message);
        if (ratioLogDto.getData() instanceof ElectricityCheatSyncDataDto) {
            ElectricityCheatSyncDataDto data = (ElectricityCheatSyncDataDto) ratioLogDto.getData();
            Map map = new HashMap();
            map.put("normalEleAvg", getMongoElectricity(data.getNormalEleAvg()));
            map.put("noLiveEleAvg", getMongoElectricity(data.getNoLiveEleAvg()));
            ratioLogDto.setData(map);
        }

        ratioLogDto.setId(SnowflakeId.getId());
        mongoTemplate.save(ratioLogDto, MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_CHEAT);
    }

    private List<ElectricityCheatDataDto> getMongoElectricity(Map<String, Integer> map) {
        List<ElectricityCheatDataDto> list = new ArrayList<>();
        if (Objects.isNull(map)) {
            return list;
        }
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            ElectricityCheatDataDto dataDto = new ElectricityCheatDataDto();
            dataDto.setVelocity(new BigDecimal(entry.getKey()));
            dataDto.setRealElectricity(entry.getValue());
            list.add(dataDto);
        }
        return list;
    }

    /**
     * 加速传感器作弊检测
     *
     * @param activityEntity
     * @param userRunDataDetail
     * @param detailsSecondEntities
     * @param cheatList
     */
    private void preventionStepCheatDeal(ActivityTypeDto activityEntity, ZnsUserRunDataDetailsEntity userRunDataDetail, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities, List<RunActivityCheat> cheatList) {
        //加速传感器
        SysConfig cheatAccelerationSensorRule = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.CHEAT_ACCELERATION_SENSOR_RULE.getCode());
        if (Objects.isNull(cheatAccelerationSensorRule)) {
            return;
        }

        CheatConfigResponseDto cheatConfigResponseDto = JsonUtil.readValue(cheatAccelerationSensorRule.getConfigValue(), CheatConfigResponseDto.class);

        if (Objects.nonNull(activityEntity) && (RunActivityTypeEnum.officialTypes().contains(activityEntity.getActivityType()) || RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType().equals(activityEntity.getActivityType()))
        ) {
            if (!CollectionUtils.isEmpty(cheatList)) {
                RunActivityCheat runActivityCheat = cheatList.stream().filter(c -> c.getCheatSwitch() == 2).findFirst().orElse(null);
                if (Objects.nonNull(runActivityCheat)) {
                    cheatConfigResponseDto.setCheatSwitch(1);
                } else {
                    log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                    return;
                }
            } else {
                log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                return;
            }
        } else {
            if (cheatConfigResponseDto.getCheatSwitch() == 0) {
                log.info("preventionCheatDeal 处理结束，防作弊开关为关");
                return;
            }
            if (Objects.nonNull(activityEntity)) {
                //新人活动不防作弊
                ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.getFirstUserActivityRunDataDetails(userRunDataDetail.getUserId(), activityEntity.getId());

                if (Objects.nonNull(details) && details.getTaskId() > 0) {
                    cheatConfigResponseDto.setCheatSwitch(0);
                    log.info("preventionCheatDeal 处理结束，新人活动不防作弊");
                }
            }
        }
        int abnormalStepTimeUser = 0;
        Integer step = -1;
        List<UserRunDataDetailsCheat> cheatAccelerationSensorList = new ArrayList<>();
        UserRunDataDetailsCheat cheat = new UserRunDataDetailsCheat();
        //是否支持空载检测
        ZnsUserRunDataDetailsSecondEntity detailsSecond = detailsSecondEntities.stream().filter(d -> d.getNoLiveLoad() == 1).findFirst().orElse(null);
        boolean noLiveLoadStatus = false;
        if (Objects.nonNull(detailsSecond)) {
            noLiveLoadStatus = true;
        }

        for (int i = 0; i < detailsSecondEntities.size(); i++) {
            ZnsUserRunDataDetailsSecondEntity znsUserRunDataDetailsSecondEntity = detailsSecondEntities.get(i);
            //小于检查开始时间不计算
            if (znsUserRunDataDetailsSecondEntity.getRunTime() <= cheatConfigResponseDto.getCheckStartTime()) {
                continue;
            }
            //默认值 -1
            if (znsUserRunDataDetailsSecondEntity.getStepNum().equals(step)) {
                abnormalStepTimeUser += 1;
                if (abnormalStepTimeUser >= cheatConfigResponseDto.getAbnormalStepTime()) {
                    cheat.setCheatType(2);
                    cheat.setEndRunTime(znsUserRunDataDetailsSecondEntity.getRunTime());
                    cheat.setEndRunMileage(znsUserRunDataDetailsSecondEntity.getMileage().intValue());
                    cheat.setStepNum(znsUserRunDataDetailsSecondEntity.getStepNum());
                    cheat.setRunDataDetailsId(znsUserRunDataDetailsSecondEntity.getRunDataDetailsId());
                    cheat.setUserId(userRunDataDetail.getUserId());
                    cheat.setIsCheat(YesNoStatus.YES.getCode().equals(znsUserRunDataDetailsSecondEntity.getSensorStatus()) ||
                            noLiveLoadStatus ? 1 : 0);
                    cheat.setAverageVelocity(znsUserRunDataDetailsSecondEntity.getVelocity());
                    cheat.setAverageHeartRate(znsUserRunDataDetailsSecondEntity.getHeartRate());
                    cheatAccelerationSensorList.add(cheat);
                    cheat = new UserRunDataDetailsCheat();
                    step = znsUserRunDataDetailsSecondEntity.getStepNum();
                    cheat.setStartRunTime(znsUserRunDataDetailsSecondEntity.getRunTime());
                    cheat.setStartRunMileage(znsUserRunDataDetailsSecondEntity.getMileage().intValue());
                    abnormalStepTimeUser = 0;
                }
            } else {
                step = znsUserRunDataDetailsSecondEntity.getStepNum();
                cheat = new UserRunDataDetailsCheat();
                cheat.setStartRunTime(znsUserRunDataDetailsSecondEntity.getRunTime());
                cheat.setStartRunMileage(znsUserRunDataDetailsSecondEntity.getMileage().intValue());
                abnormalStepTimeUser = 0;
            }
        }
        if (CollectionUtils.isEmpty(cheatAccelerationSensorList)) {
            return;
        }

        userRunDataDetailsCheatService.saveBatch(cheatAccelerationSensorList);
        if (cheatAccelerationSensorList.stream().filter(k -> k.getIsCheat() == 1).count() >= cheatConfigResponseDto.getCheatCount()) {
            ZnsUserRunDataDetailsEntity cheatUpdate = new ZnsUserRunDataDetailsEntity();
            cheatUpdate.setId(userRunDataDetail.getId());
            cheatUpdate.setIsCheat(1);
            userRunDataDetailsService.update(cheatUpdate);
            userRunDataDetail.setIsCheat(1);
        }
    }

    /**
     * 心率作弊检测
     *
     * @param activityEntity
     * @param userRunDataDetail
     * @param detailsSecondEntities
     * @param cheatSwitchList
     */
    private void preventionHeartCheatDeal(ActivityTypeDto activityEntity, ZnsUserRunDataDetailsEntity userRunDataDetail, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities, List<RunActivityCheat> cheatSwitchList) {
        if (Objects.nonNull(activityEntity) && (RunActivityTypeEnum.officialTypes().contains(activityEntity.getActivityType()) || RunActivityTypeEnum.newGameplayActivity().contains(activityEntity.getActivityType()))) {
            if (CollectionUtils.isEmpty(cheatSwitchList)) {
                log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                return;
            }
            RunActivityCheat runActivityCheat = cheatSwitchList.stream().filter(c -> c.getCheatSwitch() == 1).findFirst().orElse(null);
            if (Objects.isNull(runActivityCheat)) {
                log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                return;
            }
        } else {
            // 0关；1开
            String checkCheatSwitch = sysConfigService.selectConfigByKey("check.cheat.switch");
            if ("0".equals(checkCheatSwitch)) {
                log.info("preventionCheatDeal 处理结束，防作弊开关为关");
                return;
            }

            String checkCheatActivityType = sysConfigService.selectConfigByKey("check.cheat.activity.type");
            if (StringUtil.isEmpty(checkCheatActivityType)) {
                log.info("preventionCheatDeal 处理结束，防作弊活动类型为空");
                return;
            }

            // activity_type 以逗号隔开
            List<Integer> activityTypes = NumberUtils.stringToInt(checkCheatActivityType.split(","));
            if (!activityTypes.contains(activityEntity.getActivityType())) {
                log.info("preventionCheatDeal 处理结束，活动类型不在防作弊活动类型中");
                return;
            }
        }
        String config = sysConfigService.selectConfigByKey("check.cheat.rule");
        if (StringUtil.isEmpty(config)) {
            log.info("preventionCheatDeal 处理结束，防作弊规则为空");
            return;
        }

        Map<String, Object> jsonObject = JsonUtil.readValue(config);
        Integer checkFrequency = MapUtil.getInteger(jsonObject.get("checkFrequency"));
        Integer checkStartTime = MapUtil.getInteger(jsonObject.get("checkStartTime")) - checkFrequency;
        List<Map> rules = JsonUtil.readList(jsonObject.get("rule"), Map.class);


        List<List<ZnsUserRunDataDetailsSecondEntity>> partition = new ArrayList<>();
        List<ZnsUserRunDataDetailsSecondEntity> smallList = new ArrayList<>();
        for (ZnsUserRunDataDetailsSecondEntity detailsSecondEntity : detailsSecondEntities) {
            smallList.add(detailsSecondEntity);
            if (detailsSecondEntity.getRunTime() % checkFrequency == 0) {
                partition.add(smallList);
                smallList = new ArrayList<>();
            }
        }
        partition.add(smallList);
        Integer runTime = 0;
        BigDecimal runMileage = BigDecimal.ZERO;

        List<UserRunDataDetailsCheat> cheatList = new ArrayList<>();
        int totalCheatCount = 0;
        int totalCount = 0;

        for (int i = 0; i < partition.size(); i++) {
            List<ZnsUserRunDataDetailsSecondEntity> znsUserRunDataDetailsSecondEntities = partition.get(i);
            ZnsUserRunDataDetailsSecondEntity last = znsUserRunDataDetailsSecondEntities.get(znsUserRunDataDetailsSecondEntities.size() - 1);
            //求平均速度
            BigDecimal velocity = SportsDataUnit.getVelocity(last.getRunTime() - runTime, last.getMileage().subtract(runMileage));
            //求平均心率
            int averageRate = (int) znsUserRunDataDetailsSecondEntities.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().getAsDouble();
            boolean checkCheat = checkCheat(rules, velocity, averageRate);
            UserRunDataDetailsCheat cheat = new UserRunDataDetailsCheat();
            cheat.setRunDataDetailsId(userRunDataDetail.getId());
            cheat.setUserId(userRunDataDetail.getUserId());
            cheat.setIsCheat(BooleanUtils.toInteger(checkCheat));
            cheat.setEndRunMileage(last.getMileage().intValue());
            cheat.setEndRunTime(last.getRunTime());
            cheat.setAverageHeartRate(averageRate);
            cheat.setAverageVelocity(velocity);
            cheat.setStartRunTime(runTime);
            cheat.setStartRunMileage(runMileage.intValue());
            cheat.setIsStatistics(1);
            cheat.setCheatType(1);
            if (last.getRunTime() <= checkStartTime) {
                cheat.setIsStatistics(0);
            }
            if (i == partition.size() - 1 && last.getRunTime() % checkFrequency != 0) {
                cheat.setIsStatistics(0); // 是否统计，1：是，0：否（前两分钟及最后不满一分钟不统计）
            }
            if (cheat.getIsStatistics() == 1) {
                totalCount++;
            }
            if (cheat.getIsCheat() == 1 && cheat.getIsStatistics() == 1) {
                totalCheatCount++;
            }
            runTime = last.getRunTime();
            runMileage = last.getMileage();
            cheatList.add(cheat);
        }
        //删除原数据，防止重复
        userRunDataDetailsCheatService.deleteByDetailsId(userRunDataDetail.getId());
        userRunDataDetailsCheatService.saveBatch(cheatList);
        BigDecimal cheatCountRate = BigDecimalUtil.divide(MapUtil.getBigDecimal(jsonObject.get("cheatCountRate")), new BigDecimal(100));
        BigDecimal cheatRate = BigDecimalUtil.divHalfDown(new BigDecimal(totalCheatCount), new BigDecimal(totalCount), 2);
        ZnsUserRunDataDetailsEntity cheatUpdate = new ZnsUserRunDataDetailsEntity();
        cheatUpdate.setId(userRunDataDetail.getId());
        cheatUpdate.setCheatRate(cheatRate);
        // 如果欺骗率大于 cheatCountRate ，则设置isCheat 为1
        if (cheatRate.compareTo(cheatCountRate) >= 0) {
            cheatUpdate.setIsCheat(1);
        } else {
            cheatUpdate.setIsCheat(0);
        }
        userRunDataDetailsService.update(cheatUpdate);
    }

    private boolean checkCheat(List<Map> rules, BigDecimal velocity, int averageRate) {
        for (Map rule : rules) {
            Integer minVelocity = MapUtils.getInteger(rule, "minVelocity");
            Integer minHeartRate = MapUtils.getInteger(rule, "minHeartRate");
            // 如果平均速度大于最小平均速度，但是平均心率小于最小心率，则认为他是作弊
            if (velocity.compareTo(new BigDecimal(minVelocity)) > 0) {
                if (averageRate <= minHeartRate) {
                    log.info("checkCheat true,velocity:" + velocity + ",minVelocity:" + minVelocity + ",averageRate:" + averageRate + ",minHeartRate:" + minHeartRate);
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 获取防作弊规则
     *
     * @param userId
     * @param activity
     * @param isCourse
     * @return
     */
    public PreventionCheatRuleResponseDto getPreventionCheatRule(Long userId, ActivityTypeDto activity, Integer isCourse) {
        PreventionCheatRuleResponseDto data = new PreventionCheatRuleResponseDto();
        //检查人群
        String userStr = sysConfigService.selectConfigByKey("check.cheat.user");
        if (StringUtils.hasText(userStr)) {
            List<Long> userIds = NumberUtils.stringToLong(userStr.split(","));
            if (!userIds.contains(userIds)) {
                return data;
            }
        }
        if (checkUserWhite(userId)) {
            return data;
        }
        List<RunActivityCheat> list = null;
        if (Objects.nonNull(activity)) {
            list = runActivityCheatService.findList(activity.getId());
            //新活动兼容
            if (!MainActivityTypeEnum.OLD.getType().equals(activity.getMainType())) {
                MainActivity mainActivity = activity.getMainActivity();
                if (mainActivity.getCheatSwitch() != -1) {
                    RunActivityCheat cheat = new RunActivityCheat();
                    cheat.setCheatSwitch(mainActivity.getCheatSwitch() + 1);
                    cheat.setActivityId(activity.getId());
                    list.add(cheat);
                }
                activity.setActivityType(13);
                if (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
                    //段位赛类型
                    activity.setActivityType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType());
                }
                if (MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
                    //段位赛类型
                    activity.setActivityType(RunActivityTypeEnum.PROP_ACTIVITY.getType());
                }
                if (MainActivityTypeEnum.isFreeChallengeActivity(mainActivity.getMainType())) {
                    //段位赛类型
                    activity.setActivityType(RunActivityTypeEnum.FREE_CHALLENGE.getType());
                }
            }

            if (!RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(activity.getActivityType())) {
                //手环作弊数据
                setBraceletCheatRule(data, userId, activity.getActivityType(), activity, list);
                //加速传感器
                setAccelerationSensorCheatRule(data, userId, activity.getActivityType(), activity, list);
            }
        }

        //电流作弊
        setCheatElectricityRule(data, activity, list, isCourse);
        return data;
    }

    private void setCheatElectricityRule(PreventionCheatRuleResponseDto data, ActivityTypeDto activity, List<RunActivityCheat> cheatList, Integer isCourse) {
        SysConfig cheatElectricityRule = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.CHEAT_ELECTRICITY_RULE.getCode());
        if (Objects.isNull(cheatElectricityRule)) {
            return;
        }
        CheatElectricityRuleDto checkElectricityRule = JsonUtil.readValue(cheatElectricityRule.getConfigValue(), CheatElectricityRuleDto.class);

        if (checkElectricityRule.getPopCheatSwitch() == 0) {
            return;
        }
        if (checkElectricityRule.isCheatScene(checkElectricityRule.getPopScene(), activity, isCourse, cheatList)) {
            data.setCheatElectricityRule(checkElectricityRule);
        }
        if (Objects.nonNull(activity) && (RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType().equals(activity.getActivityType())
                || RunActivityTypeEnum.PROP_ACTIVITY.getType().equals(activity.getActivityType()))) {
            data.setAccelerationSensorCheatRule(null);
        }
    }

    private boolean checkUserWhite(Long userId) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey("cheat.user.white.switch");
        CheatUserWhiteSwitchDto cheatUserWhiteSwitchDto = JsonUtil.readValue(sysConfig.getConfigValue(), CheatUserWhiteSwitchDto.class);
        if (cheatUserWhiteSwitchDto.getSwitchState() == 0) {
            return false;
        }
        return cheatUserWhiteService.isUserWhite(userId);
    }


    private void setBraceletCheatRule(PreventionCheatRuleResponseDto data, Long userId, Integer activityType, ActivityTypeDto activity, List<RunActivityCheat> cheatSwitchList) {
        data.setCheckCheatSwitch(0);
        String checkCheatSwitch = sysConfigService.selectConfigByKey("check.cheat.switch");

        if (Objects.nonNull(activity) && (RunActivityTypeEnum.officialTypes().contains(activityType) || RunActivityTypeEnum.newGameplayActivity().contains(activityType))) {
            if (CollectionUtils.isEmpty(cheatSwitchList)) {
                log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                return;
            }
            RunActivityCheat runActivityCheat = cheatSwitchList.stream().filter(c -> c.getCheatSwitch() == 1).findFirst().orElse(null);
            if (Objects.isNull(runActivityCheat)) {
                log.info("preventionCheatDeal 处理结束，活动防作弊开关为关");
                return;
            }
        } else {
            if ("0".equals(checkCheatSwitch)) {
                log.info("preventionCheatDeal 处理结束，防作弊开关为关");
                return;
            }
            if (Objects.nonNull(activity)) {
                //新人活动不防作弊
                ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.getUserDetailByActivityId(userId, activity.getId()).stream().findFirst().orElse(null);
                if (Objects.nonNull(details) && details.getTaskId() > 0) {
                    log.info("preventionCheatDeal 处理结束，新人活动不防作弊");
                    return;
                }
            }
            String checkCheatActivityType = sysConfigService.selectConfigByKey("check.cheat.activity.type");
            if (StringUtils.hasText(checkCheatActivityType)) {
                List<Integer> activityTypes = NumberUtils.stringToInt(checkCheatActivityType.split(","));
                data.setCheckCheatActivityType(activityTypes);
                if (!activityTypes.contains(activityType)) {
                    return;
                }
            }
        }


        data.setCheckCheatSwitch(1);
        String config = sysConfigService.selectConfigByKey("check.cheat.rule");
        if (StringUtils.hasText(config)) {
            BraceletCheatRuleResponseDto braceletCheatRuleResponseDto = JsonUtil.readValue(config, BraceletCheatRuleResponseDto.class); //JsonUtil.readValue(config, BraceletCheatRuleResponseDto.class);
            data.setRemindFrequency(braceletCheatRuleResponseDto.getRemindFrequency());
            data.setCheckStartTime(braceletCheatRuleResponseDto.getCheckStartTime());
            data.setRule(braceletCheatRuleResponseDto.getRule());
            data.setCheckFrequency(braceletCheatRuleResponseDto.getCheckFrequency());
            data.setCheatCountRate(braceletCheatRuleResponseDto.getCheatCountRate());
        }
    }

    private void setAccelerationSensorCheatRule(PreventionCheatRuleResponseDto data, Long userId, Integer activityType, ActivityTypeDto activity, List<RunActivityCheat> cheatList) {
        SysConfig cheatAccelerationSensorRule = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.CHEAT_ACCELERATION_SENSOR_RULE.getCode());
        if (Objects.isNull(cheatAccelerationSensorRule)) {
            return;
        }

        CheatConfigResponseDto cheatConfigResponseDto = JsonUtil.readValue(cheatAccelerationSensorRule.getConfigValue(), CheatConfigResponseDto.class);
        cheatConfigResponseDto.setCheatSwitch(0);

        //道具赛走自己的作弊规则
        if (MainActivityTypeEnum.PROP.getType().equals(activity.getMainType())) {
            ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.PROP_ACTIVITY.getType(), null);
            PropRankedActivityConfigDto runActivityTemplate = JsonUtil.readValue(runActivityConfig.getActivityConfig(), PropRankedActivityConfigDto.class);
            if (Objects.equals(runActivityTemplate.getCheatSwitch(), 0)) {
                cheatConfigResponseDto.setCheatSwitch(0);
            } else if (Objects.equals(runActivityTemplate.getCheatSwitch(), 1)) {
                cheatConfigResponseDto.setCheatSwitch(1);
            }
        } else if ((RunActivityTypeEnum.officialTypes().contains(activityType)
                || RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType().equals(activityType)
                || RunActivityTypeEnum.PROP_ACTIVITY.getType().equals(activityType))
                || RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType().equals(activityType)
                || RunActivityTypeEnum.FREE_CHALLENGE.getType().equals(activityType)) {
            if (!CollectionUtils.isEmpty(cheatList)) {
                RunActivityCheat runActivityCheat = cheatList.stream().filter(c -> c.getCheatSwitch() == 2).findFirst().orElse(null);
                if (Objects.nonNull(runActivityCheat)) {
                    cheatConfigResponseDto.setCheatSwitch(1);
                } else {
                    log.info("setAccelerationSensorCheatRule preventionCheatDeal 处理结束，活动防作弊开关为关");
                }
            } else {
                log.info("setAccelerationSensorCheatRule preventionCheatDeal 处理结束，活动防作弊开关为关");
            }

        } else {
            if (cheatConfigResponseDto.getCheatSwitch() == 0) {
                log.info("setAccelerationSensorCheatRule preventionCheatDeal 处理结束，防作弊开关为关");
                return;
            }
            if (Objects.nonNull(activity)) {
                //新人活动不防作弊
                ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.getUserDetailByActivityId(userId, activity.getId()).stream().findFirst().orElse(null);
                if (Objects.nonNull(details) && details.getTaskId() > 0) {
                    cheatConfigResponseDto.setCheatSwitch(0);
                    log.info("setAccelerationSensorCheatRule preventionCheatDeal 处理结束，新人活动不防作弊");
                }
            }
        }

        data.setAccelerationSensorCheatRule(cheatConfigResponseDto);
    }


}
