package com.linzi.pitpat.data.activityservice.service;

import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDo;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityHighlightDo;
import com.linzi.pitpat.data.activityservice.model.query.ProActivityHighlightQuery;

import java.util.List;


/**
 * 职业赛高亮数据 服务类
 *
 * @since 2025年6月23日
 */
public interface ProActivityHighlightService {

    /**
     * 新增职业赛高亮数据
     *
     * @param proActivityHighlight
     * @param lastAmountOrScoreAwardRank
     * @return 新增数量
     */
    Long create(Long mainActivityId, List<ActivityRunRankTempDo.AwardRank> lastAmountOrScoreAwardRank);


    /**
     * 根据ID 查询职业赛高亮数据，返回单条数据
     *
     * @param id
     * @return
     */
    ProActivityHighlightDo findById(Long id);

    ProActivityHighlightDo findByMainActivityId(Long mainActivityId);

    void addEnroll(Long mainActivityId);

    void setCeoEnroll(Long mainActivityId);

    void setFirstEnrollKolUserId(Long mainActivityId, Long userId);

    void updateNo1(Long mainActivityId, Long userId, String firstName, String gradeStr, String gameAwardAmountStr);

    /**
     * 写入当前highlight kol为空的活动，当前userId又报名了。设置为kol
     * @param userId
     */
    void fillNewKolUser(Long userId);

    List<Long> userFirstKolActivityId(Long userId);

    void resetKolUser(Long activityId);

    List<ProActivityHighlightDo> findListByQuery(ProActivityHighlightQuery query);
}