package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 激历配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linzi.pitpat.data.awardservice.mapper.UrgeActivityConfigDao;
import com.linzi.pitpat.data.awardservice.model.dto.EggActivityConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeActivityConfig;
import com.linzi.pitpat.data.awardservice.service.UrgeActivityConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;

@Service
public class UrgeActivityConfigServiceImpl extends ServiceImpl<UrgeActivityConfigDao, UrgeActivityConfig> implements UrgeActivityConfigService {


    @Autowired
    private UrgeActivityConfigDao urgeActivityConfigDao;

    @Override
    public Page<EggActivityConfigDto> selectPageByCondition(IPage page, int i, ZonedDateTime gmtStartTime, ZonedDateTime gmtEndTime, List<Integer> urgeTypes) {
        return urgeActivityConfigDao.selectPageByCondition(page, i, gmtStartTime, gmtEndTime, urgeTypes);
    }

    @Override
    public List<UrgeActivityConfig> selectUrgeActivityConfigByActivityId(Long activityId, Long routeId, ZonedDateTime date, ZonedDateTime date1) {
        return urgeActivityConfigDao.selectUrgeActivityConfigByActivityId(activityId, routeId, date, date1);
    }

    @Override
    public List<UrgeActivityConfig> selectUrgeActivityConfigByActivityIdRouteIdDateDate(Long activityId, Long routeId, ZonedDateTime date, ZonedDateTime date1) {
        return urgeActivityConfigDao.selectUrgeActivityConfigByActivityIdRouteIdDateDate(activityId, routeId, date, date1);
    }

    @Override
    public List<UrgeActivityConfig> selectUrgeActivityConfigByTypeGmtStartTimeGmtEndTime(Integer activityType, Long routeId, ZonedDateTime date, ZonedDateTime date1) {
        return urgeActivityConfigDao.selectUrgeActivityConfigByTypeGmtStartTimeGmtEndTime(activityType, routeId, date, date1);
    }

    @Override
    public List<UrgeActivityConfig> selectUrgeActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(Integer activityType, Long routeId, ZonedDateTime date, ZonedDateTime date1) {
        return urgeActivityConfigDao.selectUrgeActivityConfigByTypeRouteIdGmtStartTimeGmtEndTime(activityType, routeId, date, date1);
    }

    @Override
    public UrgeActivityConfig selectUrgeActivityConfigById(Long urgeActivityConfigId) {
        return urgeActivityConfigDao.selectUrgeActivityConfigById(urgeActivityConfigId);
    }
}
