package com.linzi.pitpat.data.courseservice.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.courseservice.mapper.ZnsCourseDao;
import com.linzi.pitpat.data.courseservice.model.entity.CourseI18nEntity;
import com.linzi.pitpat.data.courseservice.model.entity.UserAiBaseinfo;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseActionEntity;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.model.query.CourseQuery;
import com.linzi.pitpat.data.courseservice.model.request.CourseHomeRequest;
import com.linzi.pitpat.data.courseservice.model.response.CourseBaseInfoI18nDto;
import com.linzi.pitpat.data.courseservice.model.response.CourseDetailI18nDto;
import com.linzi.pitpat.data.courseservice.model.vo.AICourseConfigListVo;
import com.linzi.pitpat.data.courseservice.model.vo.AICourseConfigVo;
import com.linzi.pitpat.data.courseservice.model.vo.AiCourseConfigBeanVo;
import com.linzi.pitpat.data.courseservice.model.vo.CourseCategoryVo;
import com.linzi.pitpat.data.courseservice.model.vo.CourseDetailVoPo;
import com.linzi.pitpat.data.courseservice.model.vo.CourseHomeVo;
import com.linzi.pitpat.data.courseservice.model.vo.CourseListDetailVO;
import com.linzi.pitpat.data.courseservice.model.vo.SimpleCourseCategoryVo;
import com.linzi.pitpat.data.courseservice.model.vo.SimpleCourseListVo;
import com.linzi.pitpat.data.enums.CourseDifficultyEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service("znsCourseService")
public class ZnsCourseServiceImpl implements ZnsCourseService {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ZnsCourseDao courseDao;
    @Resource
    private CourseI18nService courseI18nService;
    @Resource
    private RedisTemplate redisTemplate;


    @Override
    public List<ZnsCourseEntity> getRecommendCourse(Integer isTest) {
        return courseDao.selectList(Wrappers.<ZnsCourseEntity>lambdaQuery()
                .eq(ZnsCourseEntity::getIsDelete, 0)
                .eq(ZnsCourseEntity::getStatus, 1)
                .eq(Objects.nonNull(isTest) && isTest == 0, ZnsCourseEntity::getIsTest, 0)
                .ne(ZnsCourseEntity::getRecommended, 0)
                .orderByAsc(ZnsCourseEntity::getRecommendPlaceSort)
                .orderByDesc(ZnsCourseEntity::getModifyTime)
        );
    }

    @Override
    public boolean addPartakeCount(Long courseId) {
        RLock lock = redissonClient.getLock(ApiConstants.PARTAKE_COURSE + courseId);
        boolean locked = LockHolder.tryLock(lock, 1, 30);
        if (locked) {
            courseDao.addPartakeCount(courseId);
        }
        return true;
    }

    /**
     * 保存课程基础信息多语言内容
     *
     * @param username
     * @param baseInfoI18nList
     * @param courseId
     */
    private void saveBaseInfoI18n(String username, List<CourseBaseInfoI18nDto> baseInfoI18nList, Long courseId) {
        //物理删除指定课程多语言基础内容
        courseI18nService.deleteByCourseId(courseId);
        //保存多语言内容
        for (CourseBaseInfoI18nDto courseBaseInfoI18nDto : baseInfoI18nList) {
            CourseI18nEntity courseI18nEntity = BeanUtil.copyBean(courseBaseInfoI18nDto, CourseI18nEntity.class);
            if (!StringUtils.hasText(courseI18nEntity.getCourseName())) {
                continue;
            }
            courseI18nEntity.setCourseId(courseId);
            courseI18nEntity.setCreator(username);
            I18nConstant.LanguageCodeEnum languageCodeEnum = I18nConstant.LanguageCodeEnum.findByCode(courseBaseInfoI18nDto.getLanguageCode());
            if (languageCodeEnum != null) {
                courseI18nEntity.setLanguageName(languageCodeEnum.getName());
            }
            courseI18nService.insert(courseI18nEntity);
        }
    }

    @Override
    public void setHomeCourseRecommend(List<ZnsCourseEntity> courseList, CourseHomeVo vo, Integer appVersion, List<SimpleCourseCategoryVo> homeCategory) {
        if (CollectionUtils.isEmpty(courseList)) {
            return;
        }
        List<Long> categoryIds = homeCategory.stream().map(SimpleCourseCategoryVo::getId).collect(Collectors.toList());
        String vipCourseImg = sysConfigService.selectConfigByKey(ConfigKeyEnums.VIP_COURSE_ICON.getCode());
        String courseIcon = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON.getCode());
        String courseIconRowing = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON_ROWING.getCode());
        String courseIconCycling = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON_CYCLING.getCode());
        List<SimpleCourseListVo> hotCourseList = new ArrayList<>();
        List<SimpleCourseListVo> moreCourseList = new ArrayList<>();
        for (ZnsCourseEntity c : courseList) {
            SimpleCourseListVo homeListVo = new SimpleCourseListVo();
            BeanUtils.copyProperties(c, homeListVo);
            CourseDifficultyEnum difficultyEnum = CourseDifficultyEnum.getNameByDifficulty(homeListVo.getDifficulty());
            String difficultyName = I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(I18nMsgUtils.getLangCode()) ? difficultyEnum.getNameFr() : difficultyEnum.getName();
            homeListVo.setDifficultyName(difficultyName);
            homeListVo.setParticipantsNumber(c.getParticipantsNumber() + c.getDefaultParticipantsNumber());
            // 会员课程标识
            if (c.getIsPlusCourse() == 1) {
                homeListVo.setPlusCourseImg(vipCourseImg);
            }
            // 变速跑
            homeListVo.setCoverMark(null); // 之前配置的都失效
            if (c.getCourseType() == 0) {
                homeListVo.setCoverMark(courseIcon);
                if (c.getEquipmentType().equals("6")) {
                    homeListVo.setCoverMark(courseIconCycling);
                }
                if (c.getEquipmentType().equals("7")) {
                    homeListVo.setCoverMark(courseIconRowing);
                }
            }

            if (c.getRecommended() == 1 && categoryIds.contains(c.getCategoryId())) {
                hotCourseList.add(homeListVo);
            } else if (c.getRecommended() == 2) {
                moreCourseList.add(homeListVo);
            }
        }
        if (hotCourseList.size() > 4) {
            hotCourseList = hotCourseList.subList(0, 4);
        }
        if (moreCourseList.size() > 4) {
            moreCourseList = moreCourseList.subList(0, 4);
        }
        vo.setHotCourseList(hotCourseList);
        vo.setMoreCourseList(moreCourseList);
    }

    @Override
    public List<CourseListDetailVO> listV1(Integer isTest) {
        List<CourseListDetailVO> categoryCourseList = new LinkedList<>();

        // 获取所有的课程列表
        QueryWrapper<ZnsCourseEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("is_delete", 0);
        wrapper.eq("status", 1);
        wrapper.eq(isTest == 0, "is_test", 0);
        List<ZnsCourseEntity> courseList = courseDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(courseList)) {
            return categoryCourseList;
        }

        Map<Long, List<ZnsCourseEntity>> courseCategoryMap = courseList.stream().collect(Collectors.groupingBy(ZnsCourseEntity::getCategoryId));
        Set<Long> categoryIds = courseCategoryMap.keySet();
        for (Long categoryId : categoryIds) {
            List<ZnsCourseEntity> courseEntityList = courseCategoryMap.get(categoryId);
            courseEntityList = courseEntityList.stream().sorted(Comparator.comparing(ZnsCourseEntity::getSort)).collect(Collectors.toList());
            List<SimpleCourseListVo> collect = courseEntityList.stream().map(course -> {
                SimpleCourseListVo listVo = new SimpleCourseListVo();
                BeanUtils.copyProperties(course, listVo);
                listVo.setParticipantsNumber(course.getParticipantsNumber() + course.getDefaultParticipantsNumber());
                return listVo;
            }).collect(Collectors.toList());

            CourseListDetailVO courseListVO = new CourseListDetailVO();
            courseListVO.setCategoryName(courseEntityList.get(0).getCategoryName());
            courseListVO.setCategoryId(categoryId);
            courseListVO.setCourseList(collect);
            categoryCourseList.add(courseListVO);
        }

        return categoryCourseList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editCourse(CourseDetailVoPo po, String username) {
        //默认内容
        CourseDetailI18nDto defaultContent = po.getContentI18nList().stream().filter(item -> po.getDefaultLangCode().equals(item.getLanguageCode())).findFirst().orElse(po.getContentI18nList().get(0));
        //默认基础信息
        CourseBaseInfoI18nDto defaultBaseInfo = po.getBaseInfoI18nList().stream().filter(item -> po.getDefaultLangCode().equals(item.getLanguageCode())).findFirst().orElse(po.getBaseInfoI18nList().get(0));

        ZnsCourseEntity courseEntity = BeanUtil.copyBean(po, ZnsCourseEntity.class);
        BeanUtil.copyPropertiesIgnoreNull(defaultBaseInfo, courseEntity);
        courseEntity.setModifyTime(ZonedDateTime.now());
        courseEntity.setModified(username);
        if (Objects.isNull(courseEntity.getId())) {
            courseEntity.setCreateTime(ZonedDateTime.now());
            courseEntity.setCreator(username);
        }
        //实际训练时长
        if (po.getCourseType() == 1) {
            Integer sum = defaultContent.getActionList().stream().filter(a -> a.getIsRecordTime() == 1).mapToInt(ZnsCourseActionEntity::getActionDuration).sum();
            courseEntity.setActualTrainingDuration(sum);
        }
        courseDao.insertOrUpdate(courseEntity);

        String coverMark = courseEntity.getCoverMark();
        if (!StringUtils.hasText(coverMark)) {
            LambdaUpdateWrapper<ZnsCourseEntity> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(ZnsCourseEntity::getCoverMark, null);
            updateWrapper.eq(ZnsCourseEntity::getId, courseEntity.getId());
            courseDao.update(updateWrapper);
        }
        po.setId(courseEntity.getId());

        //保存课程基础信息多语言内容
        saveBaseInfoI18n(username, po.getBaseInfoI18nList(), courseEntity.getId());
    }

    @Override
    public Page pageList(CourseHomeRequest request, Integer isTest, String languageCode) {
        Page page = courseDao.pageList(new Page(request.getPageNum(), request.getPageSize()), request.getCategoryId(), request.getRecommended(), isTest, languageCode);
        List<SimpleCourseListVo> records = page.getRecords();
        String vipCourseImg = sysConfigService.selectConfigByKey(ConfigKeyEnums.VIP_COURSE_ICON.getCode());
        String courseIcon = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON.getCode());
        String courseIconRowing = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON_ROWING.getCode());
        String courseIconCycling = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON_CYCLING.getCode());
        for (SimpleCourseListVo record : records) {
            // 课程跑
            record.setCoverMark(null); // 之前配置的都不生效
            if (Objects.nonNull(record.getCourseType()) && record.getCourseType() == 0) {
                record.setCoverMark(courseIcon);
                if (record.getEquipmentType().equals("6")) {
                    record.setCoverMark(courseIconCycling);
                }
                if (record.getEquipmentType().equals("7")) {
                    record.setCoverMark(courseIconRowing);
                }
            }
            // 会员
            if (Objects.nonNull(record.getIsPlusCourse()) && record.getIsPlusCourse() == 1) {
                record.setPlusCourseImg(vipCourseImg);
            }
            CourseDifficultyEnum difficultyEnum = CourseDifficultyEnum.getNameByDifficulty(record.getDifficulty());
            String difficultyName = I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(I18nMsgUtils.getLangCode()) ? difficultyEnum.getNameFr() : difficultyEnum.getName();
            record.setDifficultyName(difficultyName);
        }
        page.setRecords(records);
        return page;
    }

    @Override
    public void updateByCategoryId(Long categoryId, String categoryName) {
        ZnsCourseEntity update = new ZnsCourseEntity();
        update.setCategoryName(categoryName);
        courseDao.update(update, Wrappers.<ZnsCourseEntity>lambdaUpdate().eq(ZnsCourseEntity::getIsDelete, 0).eq(ZnsCourseEntity::getCategoryId, categoryId));
    }

    @Autowired
    private UserAiBaseinfoService userAiBaseinfoService;
    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public AICourseConfigListVo questionConfig(ZnsUserEntity loginUser, Integer appVersion) {
        //获取系统配置的
        String langCode = I18nMsgUtils.getLangCode();
        AICourseConfigListVo aiCourseConfigListVo = new AICourseConfigListVo();
        String config = sysConfigService.selectConfigByKey("ai.course.config.height");
        String config1 = sysConfigService.selectConfigByKey("ai.course.config.weight");

        String configCM = sysConfigService.selectConfigByKey("ai.course.config.height.cm");
        String config1KG = sysConfigService.selectConfigByKey("ai.course.config.weight.kg");

        //训练目的
        String configKey2 = "ai.course.config.fitnessGoals";
        I18nConstant.LanguageCodeEnum codeEnum2 = I18nConstant.LanguageCodeEnum.findByCode(langCode);
        if (codeEnum2 != null && codeEnum2 != I18nConstant.LanguageCodeEnum.en_US) {
            //不是英语，才需要加后缀查询
            configKey2 = configKey2 + "." + codeEnum2.getCode();
        }

        //ai训练课程运动类型
        String configKey3 = "ai.course.config.exerciseType";
        I18nConstant.LanguageCodeEnum codeEnum3 = I18nConstant.LanguageCodeEnum.findByCode(langCode);
        if (codeEnum3 != null && codeEnum3 != I18nConstant.LanguageCodeEnum.en_US) {
            //不是英语，才需要加后缀查询
            configKey3 = configKey3 + "." + codeEnum3.getCode();
        }

        String config2 = sysConfigService.selectConfigByKey(configKey2);
        String config3 = sysConfigService.selectConfigByKey(configKey3);
        AICourseConfigVo height = new AICourseConfigVo();
        AICourseConfigVo weight = new AICourseConfigVo();
        AICourseConfigVo defaultHeight = JsonUtil.readValue(configCM, AICourseConfigVo.class);
        AICourseConfigVo defaultWeight = JsonUtil.readValue(config1KG, AICourseConfigVo.class);
        if (Objects.equals(loginUser.getMeasureUnit(), 0)) {
            height = defaultHeight;
            weight = defaultWeight;
        } else {
            height = JsonUtil.readValue(config, AICourseConfigVo.class);
            height.setDefaultValue(defaultHeight.getDefaultValue());
            weight = JsonUtil.readValue(config1, AICourseConfigVo.class);
            weight.setDefaultValue(defaultWeight.getDefaultValue());
        }

        AiCourseConfigBeanVo fitnessGoals = JsonUtil.readValue(config2, AiCourseConfigBeanVo.class);
        AiCourseConfigBeanVo exerciseType = JsonUtil.readValue(config3, AiCourseConfigBeanVo.class);
        UserAiBaseinfo userAiBaseinfo = userAiBaseinfoService.selectUserAiBaseinfoByUserId(loginUser.getId());
        if (Objects.nonNull(userAiBaseinfo)) {
            if (appVersion >= 3050) {
                height.setUserValue(userAiBaseinfo.getHeight().toString());
                height.setDefaultValue(new BigDecimal(height.getDefaultValue()).multiply(new BigDecimal(1000)).toString());
                weight.setUserValue(userAiBaseinfo.getWeight().toString());
                weight.setDefaultValue(new BigDecimal(weight.getDefaultValue()).multiply(new BigDecimal(1000)).toString());
            } else {
                height.setUserValue(userAiBaseinfo.getHeightStr());
                weight.setUserValue(BigDecimalUtil.convertToPounds(new BigDecimal(userAiBaseinfo.getWeight())).toString());
            }
            fitnessGoals.setUserCode(userAiBaseinfo.getFitnessGoals());
            exerciseType.setUserCode(userAiBaseinfo.getExerciseType());

        }
        aiCourseConfigListVo.setHeight(height);
        aiCourseConfigListVo.setWeight(weight);
        aiCourseConfigListVo.setFitnessGoals(fitnessGoals);
        aiCourseConfigListVo.setExerciseType(exerciseType);
        return aiCourseConfigListVo;
    }

    @Override
    public void assemble(CourseHomeVo vo) {
        //重装热门课程
        List hotIds = redisTemplate.opsForList().range(RedisConstants.HOT_COURSE_LIST, 0, -1);
        vo.setHotCourseList(null);
        if (!CollectionUtils.isEmpty(hotIds)) {
            List<ZnsCourseEntity> hotCourses = courseDao.selectBatchIds(hotIds);
            List<SimpleCourseListVo> hotVos = hotCourses.stream().sorted(Comparator.comparingInt(ZnsCourseEntity::getShowParticipantsNumber).reversed())
                    .map(this::copy).collect(Collectors.toList());
            vo.setHotCourseList(hotVos);
        }
        //组装拉伸
        List strengthIds = redisTemplate.opsForList().range(RedisConstants.STRETCH_COURSE_LIST, 0, -1);
        if (!CollectionUtils.isEmpty(strengthIds)) {
            List<ZnsCourseEntity> strengthCourses = courseDao.selectBatchIds(strengthIds);
            List<SimpleCourseListVo> strengthCoursesVos = strengthCourses.stream()
                    .map(this::copy).collect(Collectors.toList());

            CourseCategoryVo strengthVo = new CourseCategoryVo();
            strengthVo.setId(13l);
            strengthVo.setCourseList(strengthCoursesVos);
            vo.setStretchCourseCategoryVo(strengthVo);
        }

        //组装有氧
        List aerobicIds = redisTemplate.opsForList().range(RedisConstants.AEROBIC_COURSE_LIST, 0, -1);
        if (!CollectionUtils.isEmpty(aerobicIds)) {
            List<ZnsCourseEntity> aerobicCourses = courseDao.selectBatchIds(aerobicIds);
            List<SimpleCourseListVo> aerobicCoursesVos = aerobicCourses.stream()
                    .map(this::copy).collect(Collectors.toList());

            CourseCategoryVo aerobicCourseCategoryVo = new CourseCategoryVo();
            aerobicCourseCategoryVo.setId(10l);
            aerobicCourseCategoryVo.setCourseList(aerobicCoursesVos);
            vo.setAerobicCourseCategoryVo(aerobicCourseCategoryVo);
        }


        //组装肌肉
        List muscleIds = redisTemplate.opsForList().range(RedisConstants.MUSCLE_COURSE_LIST, 0, -1);
        if (!CollectionUtils.isEmpty(muscleIds)) {
            List<ZnsCourseEntity> muscleCourses = courseDao.selectBatchIds(muscleIds);
            List<SimpleCourseListVo> muscleCoursesVos = muscleCourses.stream()
                    .map(this::copy).collect(Collectors.toList());

            CourseCategoryVo muscleCourseCategoryVo = new CourseCategoryVo();
            muscleCourseCategoryVo.setId(9l);
            muscleCourseCategoryVo.setCourseList(muscleCoursesVos);
            vo.setMuscleCourseCategoryVo(muscleCourseCategoryVo);
        }


    }

    /**
     * 获取课程多语言
     *
     * @param courseId
     * @param languageCode
     * @return
     */
    @Override
    public ZnsCourseEntity getI18nCourseById(Long courseId, String languageCode) {
        return courseDao.getI18nCourseById(courseId, languageCode);
    }

    private SimpleCourseListVo copy(ZnsCourseEntity c) {
        String vipCourseImg = sysConfigService.selectConfigByKey(ConfigKeyEnums.VIP_COURSE_ICON.getCode());
        String courseIcon = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON.getCode());
        String courseIconRowing = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON_ROWING.getCode());
        String courseIconCycling = sysConfigService.selectConfigByKey(ConfigKeyEnums.COURSE_ICON_CYCLING.getCode());
        SimpleCourseListVo homeListVo = new SimpleCourseListVo();
        BeanUtils.copyProperties(c, homeListVo);
        CourseDifficultyEnum difficultyEnum = CourseDifficultyEnum.getNameByDifficulty(homeListVo.getDifficulty());
        String difficultyName = I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(I18nMsgUtils.getLangCode()) ? difficultyEnum.getNameFr() : difficultyEnum.getName();
        homeListVo.setDifficultyName(difficultyName);
        homeListVo.setParticipantsNumber(c.getParticipantsNumber() + c.getDefaultParticipantsNumber());
        // 会员课程标识
        if (c.getIsPlusCourse() == 1) {
            homeListVo.setPlusCourseImg(vipCourseImg);
        }
        // 变速跑
        homeListVo.setCoverMark(null); // 之前配置的都失效
        if (c.getCourseType() == 0) {
            homeListVo.setCoverMark(courseIcon);
            if (c.getEquipmentType().equals("6")) {
                homeListVo.setCoverMark(courseIconCycling);
            }
            if (c.getEquipmentType().equals("7")) {
                homeListVo.setCoverMark(courseIconRowing);
            }
        }

        return homeListVo;

    }

    @Override
    public ZnsCourseEntity selectById(Long courseId) {
        return courseDao.selectById(courseId);
    }

    @Override
    public List<ZnsCourseEntity> selectHotCourseList() {
        LambdaQueryWrapper<ZnsCourseEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(ZnsCourseEntity::getParticipantsNumber)
                .eq(ZnsCourseEntity::getIsDelete, 0)
                .eq(ZnsCourseEntity::getCourseType, 0)
                .eq(ZnsCourseEntity::getStatus, 1)
                .eq(ZnsCourseEntity::getIsTest, 0)
                .last("limit 20");
        return courseDao.selectList(wrapper);
    }

    @Override
    public List<ZnsCourseEntity> getRandomByCategoryId(int categoryId, int limit) {
        return courseDao.getRandomByCategoryId(categoryId, limit);
    }

    @Override
    public Page<ZnsCourseEntity> findPage(CourseQuery query) {
        return courseDao.selectPage(Page.of(query.getPageNum(), query.getPageSize()), buildQueryWrapper(query));

    }

    @Override
    public List<ZnsCourseEntity> findList(CourseQuery query) {
        return courseDao.selectList(buildQueryWrapper(query));
    }

    @Override
    public void updateById(ZnsCourseEntity delete) {
        courseDao.updateById(delete);
    }

    @Override
    public ZnsCourseEntity selectCourseById(Long courseId) {
        return courseDao.selectCourseById(courseId);
    }

    @Override
    public List<ZnsCourseEntity> findByIds(List<Long> ids) {
        return courseDao.selectBatchIds(ids);
    }

    @Override
    public void delete(Long id, String username) {
        courseDao.update(new UpdateWrapper<ZnsCourseEntity>().lambda().set(ZnsCourseEntity::getIsDelete, 1).set(ZnsCourseEntity::getModified, username)
                .eq(ZnsCourseEntity::getId, id));
    }

    private LambdaQueryWrapper<ZnsCourseEntity> buildQueryWrapper(CourseQuery query) {
        LambdaQueryWrapper<ZnsCourseEntity> wrapper = Wrappers.lambdaQuery();
        if (Objects.nonNull(query.getDeviceType())) {
            switch (query.getDeviceType()) {
                case 0:
                    wrapper.and(wp -> wp
                            .apply("FIND_IN_SET(1, equipment_type) > 0")
                            .or()
                            .apply("FIND_IN_SET(2, equipment_type) > 0")
                            .or()
                            .apply("FIND_IN_SET(4, equipment_type) > 0")
                            .or()
                            .apply("FIND_IN_SET(5, equipment_type) > 0"));
                    break;
                case 1:
                    wrapper.apply("FIND_IN_SET(6, equipment_type) > 0");
                    break;
                case 3:
                    wrapper.apply("FIND_IN_SET(7, equipment_type) > 0");
                    break;
            }
        }
        wrapper.eq(Objects.nonNull(query.getIsDelete()), ZnsCourseEntity::getIsDelete, query.getIsDelete());
        wrapper.eq(Objects.nonNull(query.getId()), ZnsCourseEntity::getId, query.getId());
        wrapper.eq(Objects.nonNull(query.getStatus()), ZnsCourseEntity::getStatus, query.getStatus());
        wrapper.eq(StringUtils.hasText(query.getCourseName()), ZnsCourseEntity::getCourseName, query.getCourseName());
        wrapper.eq(Objects.nonNull(query.getCategoryId()), ZnsCourseEntity::getCategoryId, query.getCategoryId());
        wrapper.ge(Objects.nonNull(query.getMinCategoryId()), ZnsCourseEntity::getCategoryId, query.getMinCategoryId());
        wrapper.eq(Objects.nonNull(query.getCourseType()), ZnsCourseEntity::getCourseType, query.getCourseType());
        wrapper.eq(Objects.nonNull(query.getRecommended()), ZnsCourseEntity::getRecommended, query.getRecommended());
        wrapper.eq(Objects.nonNull(query.getDifficulty()), ZnsCourseEntity::getDifficulty, query.getDifficulty());
        wrapper.eq(Objects.nonNull(query.getIsPlusCourse()), ZnsCourseEntity::getIsPlusCourse, query.getIsPlusCourse());
        wrapper.ge(Objects.nonNull(query.getMinModifyStartTime()), ZnsCourseEntity::getModifyTime, query.getMinModifyStartTime());
        wrapper.le(Objects.nonNull(query.getMaxModifyStartTime()), ZnsCourseEntity::getModifyTime, query.getMaxModifyStartTime());
        wrapper.eq(Objects.nonNull(query.getIsTest()), ZnsCourseEntity::getIsTest, query.getIsTest());
        wrapper.last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序

        return wrapper;
    }
}
