package com.linzi.pitpat.data.dao.activity;
/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.entity.activity.MonthRunData;
import com.lz.mybatis.plugin.annotations.By;
import com.lz.mybatis.plugin.annotations.IF;
import com.lz.mybatis.plugin.annotations.LIMIT;
import com.lz.mybatis.plugin.annotations.OrderBy;
import com.lz.mybatis.plugin.annotations.OrderType;
import org.apache.ibatis.annotations.Mapper;

import java.time.ZonedDateTime;

@Mapper
public interface MonthRunDataDao extends BaseMapper<MonthRunData> {


    int updateMonthRunDataById(MonthRunData monthRunData);


    @LIMIT
    MonthRunData selectMonthRunDataByUserIdMonth(Long userId, String month);

    @OrderBy({MonthRunData.activity_type_count, MonthRunData.run_count, MonthRunData.run_distance})
    @LIMIT
    MonthRunData selectMonthRunDataByMonthBestRun(String month, @IF Integer isAddRank);

    @LIMIT
    @OrderBy({MonthRunData.run_distance, MonthRunData.run_time})
    MonthRunData selectMonthRunDataByChengjiBashi(String month, @IF Integer isAddRank);

    @LIMIT
    @OrderBy({MonthRunData.run_time, MonthRunData.run_distance})
    MonthRunData selectMonthRunDataByShiJianChangHe(String month, @IF Integer isAddRank);


    @LIMIT
    @OrderBy({MonthRunData.max_velocity, MonthRunData.run_distance})
    MonthRunData selectMonthRunDataByHuanYinJiao(String month, @IF Integer isAddRank);


    @LIMIT
    @OrderBy(
            value = {MonthRunData.guanfang_mul_compile_count, MonthRunData.guanfang_mul_run_distance, MonthRunData.guanfang_mul_run_time},
            type = {OrderType.DESC, OrderType.DESC, OrderType.ASC})
    MonthRunData selectMonthRunDataBySaiShiWang(String month, @IF Integer isAddRank);


    @LIMIT
    @OrderBy(
            value = {MonthRunData.guanfang_rank_up_position_count, MonthRunData.guanfang_rank_complete_count,
                    MonthRunData.guanfang_rank_position_count},
            type = {OrderType.DESC, OrderType.DESC, OrderType.ASC})
    MonthRunData selectMonthRunDataBy1vs100(String month, @IF Integer isAddRank);


    @LIMIT
    @OrderBy({MonthRunData.work_day_run_count, MonthRunData.run_distance, MonthRunData.run_time})
    MonthRunData selectMonthRunDataByGongzhuoZhiYou(String month, @IF Integer isAddRank);

    /// iewioewoiewioewioewoiwiooeiwoieweoiw
    @LIMIT
    @OrderBy({MonthRunData.weekend_run_count, MonthRunData.run_distance, MonthRunData.run_time})
    MonthRunData selectMonthRunDataByJiangKuangJiaQi(String month, @IF Integer isAddRank);


    Integer updateMonthRunDataMonthHonourPopById(ZonedDateTime gmtModified, Integer monthHonourPop, @By Long id);
}
