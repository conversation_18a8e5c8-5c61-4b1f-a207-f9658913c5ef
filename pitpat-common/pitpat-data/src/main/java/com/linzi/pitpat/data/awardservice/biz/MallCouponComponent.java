package com.linzi.pitpat.data.awardservice.biz;

import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCouponConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.query.CouponQuery;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsDiscountAmountDetailVo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商城券组件
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MallCouponComponent {

    private final CouponService couponService;
    private final ISysConfigService iSysConfigService;
    private final UserCouponService userCouponService;
    private final ActivityCouponConfigService couponConfigService;
    private final CouponCurrencyService couponCurrencyService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final UserExtraBizService userExtraBizService;
    private final RedissonClient redissonClient;


    /**
     * 通过券id判断不可用原因，默认使用最早过期的券
     */
    public Set<UserCouponConstant.UnAvailableReasonEnum> checkUserCouponByCouponIdAndSku(ZnsUserEntity user, Long couponId, List<OrderSkuVo> skuVos) {
        UserCouponQuery userCouponQuery = new UserCouponQuery().setUserId(user.getId()).setCouponId(couponId).setTime(ZonedDateTime.now()).setCouponStatus(UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type);
        List<UserCoupon> userCoupons = userCouponService.findListByQuery(userCouponQuery);
        if (CollectionUtils.isEmpty(userCoupons)) {
            return Set.of(UserCouponConstant.UnAvailableReasonEnum.REASON_6);
        }
        UserCoupon userCoupon = userCoupons.stream().min(Comparator.comparing(UserCoupon::getGmtEnd)).orElse(null);
        return checkUserCouponBySku(userCoupon, skuVos);
    }

    /**
     * 通过商品判断不用户券可用原因
     */
    public Set<UserCouponConstant.UnAvailableReasonEnum> checkUserCouponBySku(UserCoupon userCoupon, List<OrderSkuVo> skuVos) {
        //获取券配置
        Set<UserCouponConstant.UnAvailableReasonEnum> result = new HashSet<>();
        CouponQuery query = new CouponQuery().setCouponId(userCoupon.getCouponId());
        Coupon coupon = couponService.findOneByQuery(query);
        if (coupon == null) {
            //优惠券不存在
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_1);
            return result;
        }
        //校验用户券状态
        result.addAll(checkUserCoupon(coupon,userCoupon));
        if (!CollectionUtils.isEmpty(result)) {
            //用户券不可用，直接返回
            return result;
        }
        //校验券状态
        result.addAll(checkCouponBySku(coupon, skuVos));
        return result;
    }

    /**
     * 通过商品判断券不可用原因
     */
    public Set<UserCouponConstant.UnAvailableReasonEnum> checkCouponBySku(Coupon coupon, List<OrderSkuVo> skuVos) {
        Set<UserCouponConstant.UnAvailableReasonEnum> result = new HashSet<>();
        if (Objects.nonNull(coupon.getGmtStart()) && ZonedDateTime.now().isBefore(ZonedDateTimeUtil.convertFrom(coupon.getGmtStart()))) {
            //未到使用时间
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_3);
        }

        //优先使用缓存
        String key = coupon.getId() + ":" + skuVos.stream().map(OrderSkuVo::toString).collect(Collectors.joining(":"));
        RBucket<Set<UserCouponConstant.UnAvailableReasonEnum>> bucket = redissonClient.getBucket(key);
        if (Objects.nonNull(bucket.get())) {
            return bucket.get();
        }
        Map<Long, Integer> skuCountMap = skuVos.stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, OrderSkuVo::getCount, (k1, k2) -> k2));
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuCountMap.keySet());
        List<Long> goodIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).toList();
        List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(goodIds);

        //判断优惠券国家是否一致
        if (!CollectionUtils.isEmpty(goodsEntities)) {
            //校验国家
            goodsEntities.stream().filter(item -> !coupon.getCountryCodes().contains(item.getCountryCode())).findFirst().ifPresent(znsGoodsEntity -> result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_7));
            //校验币种
            List<CouponCurrencyEntity> couponCurrencyEntities = couponCurrencyService.findCouponCurrencyListOrDefault(List.of(coupon));
            CouponCurrencyEntity couponCurrencyEntity = couponCurrencyEntities.stream().filter(item -> Objects.equals(item.getCurrencyCode(), skuEntities.get(0).getCurrencyCode())).findFirst().orElse(null);
            if (Objects.isNull(couponCurrencyEntity)) {
                result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_7);
            }
            coupon.fillCurrencyAmount(couponCurrencyEntity);
        }

        //计算券适用的sku
        List<ZnsGoodsSkuEntity> couponSkuEntities = new ArrayList<>(); //分担券的sku
        if (CouponConstant.UseScopeEnum.USE_SCOPE_3.type.equals(coupon.getUseScope())) {
            //单品券，只能作用于一个sku,所以这里取最高价sku
            List<Long> spuIds = getCouponSpuIds(coupon, skuEntities);
            skuEntities.stream().filter(item -> spuIds.contains(item.getGoodsId())).max(Comparator.comparing(ZnsGoodsSkuEntity::getSalePrice)).ifPresent(couponSkuEntities::add);
        } else if (CouponConstant.UseScopeEnum.USE_SCOPE_2.type.equals(coupon.getUseScope())) {
            //部分商品券
            List<Long> spuIds = getCouponSpuIds(coupon, skuEntities);
            List<ZnsGoodsSkuEntity> skuList = skuEntities.stream().filter(item -> spuIds.contains(item.getGoodsId())).toList();
            couponSkuEntities.addAll(skuList);
        } else {
            //全品券，计算所有sku金额
            couponSkuEntities = skuEntities;
        }
        if (CollectionUtils.isEmpty(couponSkuEntities)) {
            //无可用商品
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_2);
        } else {
            //计算商品总金额
            BigDecimal salePriceTotalAmount = BigDecimal.ZERO;
            for (ZnsGoodsSkuEntity couponSkuEntity : couponSkuEntities) {
                BigDecimal skuTotalAmount = couponSkuEntity.getSalePrice().multiply(new BigDecimal(skuCountMap.getOrDefault(couponSkuEntity.getId(), 0)));
                salePriceTotalAmount = skuTotalAmount.add(skuTotalAmount);
            }
            if (salePriceTotalAmount.compareTo(Optional.ofNullable(coupon.getMinTotalAmount()).orElse(BigDecimal.ZERO)) < 0) {
                //未满足门槛
                result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_4);
            }
        }
        long timeToLive = SpringContextUtils.isOnline() ? 30 * 60 : 30; //线上缓存 30分钟，测试缓存30秒
        bucket.set(result, timeToLive, TimeUnit.SECONDS);
        return result;
    }

    /**
     * 校验用户券状态
     */
    public Set<UserCouponConstant.UnAvailableReasonEnum> checkUserCoupon(Coupon coupon,UserCoupon userCoupon) {
        Set<UserCouponConstant.UnAvailableReasonEnum> result = new HashSet<>();
        if (Objects.nonNull(userCoupon.getGmtStart()) && ZonedDateTime.now().isBefore(userCoupon.getGmtStart())) {
            //未到使用时间
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_3);
        }
        //失效状态
        List<Integer> expireStatus = List.of(UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type
                , UserCouponConstant.UserCouponStatusEnum.USE_STATUS_3.type, UserCouponConstant.UserCouponStatusEnum.USE_STATUS_4.type);
        if (Objects.nonNull(userCoupon.getGmtEnd()) && ZonedDateTime.now().isAfter(userCoupon.getGmtEnd())) {
            //优惠券失效(过期)
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_1);
        }
        if (UserCouponConstant.UserCouponStatusEnum.USE_STATUS_2.type.equals(userCoupon.getStatus())) {
            //优惠券已使用
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_6);
        }

        //校验国家
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(userCoupon.getUserId(), null);
        if (!coupon.getCountryCodes().contains(mallCountryCode)){
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_7);
        }

        //校验币种
        String currencyCode = userExtraBizService.findUserCurrencyCode(userCoupon.getUserId(), null);
        List<CouponCurrencyEntity> couponCurrencyEntities = couponCurrencyService.findCouponCurrencyListOrDefault(List.of(coupon));
        CouponCurrencyEntity couponCurrencyEntity = couponCurrencyEntities.stream().filter(item -> Objects.equals(item.getCurrencyCode(), currencyCode)).findFirst().orElse(null);
        if (Objects.isNull(couponCurrencyEntity)) {
            result.add(UserCouponConstant.UnAvailableReasonEnum.REASON_7);
        }
        coupon.fillCurrencyAmount(couponCurrencyEntity);

        return result;
    }

    /**
     * 商城销售价最小总金额
     */
    public BigDecimal getMinTotalAmount() {
        BigDecimal minTotalAmount = new BigDecimal("0.01");
        try {
            String minTotalAmountStr = iSysConfigService.selectConfigByKey(ConfigKeyEnums.MALL_SALE_PRICE_MIN_TOTAL_AMOUNT.getCode());
            if (StringUtils.hasText(minTotalAmountStr)) {
                minTotalAmount = new BigDecimal(minTotalAmountStr);
            }
        } catch (Exception e) {
            log.error("商城销售价最小总金额 异常：", e);
        }
        return minTotalAmount;
    }

    /**
     * 优惠券按门槛排序（门槛较低的在前；相同门槛，折扣在前，优惠金额大的在前（折扣顺序，金额倒序），最后按id倒序）
     */
    public List<Coupon> couponSort(List<Coupon> couponList) {
        couponList = couponList.stream()
                .sorted(Comparator.comparing((Coupon coupon) ->
                                Optional.ofNullable(coupon.getMinTotalAmount()).orElse(BigDecimal.ZERO))
                        .thenComparing(Coupon::getDiscountMethod, Comparator.reverseOrder())
                        .thenComparing((Coupon coupon) -> {
                            if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_2.type.equals(coupon.getDiscountMethod())) {
                                return coupon.getDiscount();
                            } else if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(coupon.getDiscountMethod())) {
                                return coupon.getAmount().negate(); // 为了使amount大的在前，我们取其负值
                            } else {
                                return BigDecimal.ZERO; // 如果discountMethod既不是1也不是2，我们用0作为比较值
                            }
                        })
                        .thenComparing(Coupon::getId, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        return couponList;
    }

    /**
     * 查询优惠券适用的spu
     */
    public List<Long> getCouponSpuIds(Coupon coupon, List<ZnsGoodsSkuEntity> skuEntities) {
        if (coupon == null) {
            return List.of();
        }
        List<Long> spuIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        if (CouponConstant.UseScopeEnum.USE_SCOPE_1.type.equals(coupon.getUseScope())) {
            //全场通用
            return spuIds;
        }
        //指定spu
        ActivityCouponConfigQuery query = ActivityCouponConfigQuery.builder().couponConfigIds(spuIds).couponId(coupon.getId()).type(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type).build();
        List<ActivityCouponConfig> couponConfigs = couponConfigService.findListByQuery(query);
        return couponConfigs.stream().map(item -> Long.valueOf(item.getCouponConfig())).toList();
    }

    /**
     * 计算最佳优惠券
     */
    public Coupon calBestCoupon(List<OrderSkuVo> skuVos, List<Coupon> couponList, ZnsUserEntity user) {
        //商品销售价总金额
        BigDecimal totalSaleAmount = calTotalSaleAmount(skuVos);

        //查询spuId
        List<Long> skuIds = skuVos.stream().map(OrderSkuVo::getSkuId).distinct().toList();
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        List<Long> spuIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        Map<Long, Long> spuIdMap = skuEntities.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, ZnsGoodsSkuEntity::getGoodsId, (k1, k2) -> k2));

        //查询优惠券可用的spu, key -> couponId，val -> spuIdList
        ActivityCouponConfigQuery query = ActivityCouponConfigQuery.builder().couponConfigIds(spuIds).type(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type).build();
        List<ActivityCouponConfig> couponConfigs = couponConfigService.findListByQuery(query);
        Map<Long, List<Long>> couponMap = couponConfigs.stream().collect(Collectors.groupingBy(ActivityCouponConfig::getCouponId, Collectors.mapping(item -> Long.valueOf(item.getCouponConfig()), Collectors.toList())));

        //查询用户券的最早失效时间 key -> couponId
        Map<Long, UserCoupon> expireMap = new HashMap<>();
        if (user != null) {
            List<Long> couponIds = couponList.stream().map(Coupon::getId).toList();
            UserCouponQuery userCouponQuery = new UserCouponQuery().setUserId(user.getId()).setCouponIds(couponIds).setTime(ZonedDateTime.now()).setCouponStatus(UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type);
            List<UserCoupon> userCoupons = userCouponService.findListByQuery(userCouponQuery);
            if (!CollectionUtils.isEmpty(userCoupons)) {
                expireMap = userCoupons.stream().collect(Collectors.toMap(UserCoupon::getCouponId, Function.identity(), (k1, k2) -> k1.getGmtEnd().isBefore(k2.getGmtEnd()) ? k1 : k2));
            }
        }

        //计算最佳优惠券
        Coupon bestCoupon = null;
        BigDecimal bestAmount = BigDecimal.ZERO;
        for (Coupon coupon : couponList) {
            //计算优惠券可用的sku销售价总金额
            BigDecimal saleAmount = totalSaleAmount;
            if (!CouponConstant.UseScopeEnum.USE_SCOPE_1.type.equals(coupon.getUseScope())) {
                //不是通用券，过滤指定sku
                List<Long> couponSpuIds = Optional.ofNullable(couponMap.get(coupon.getId())).orElse(new ArrayList<>());
                List<OrderSkuVo> skuList = skuVos.stream().filter(item -> couponSpuIds.contains(spuIdMap.getOrDefault(item.getSkuId(), 0L))).toList();
                if (CollectionUtils.isEmpty(skuList)) {
                    continue;
                }
                saleAmount = calTotalSaleAmount(skuList);
            }
            //校验优惠券门槛
            BigDecimal minTotalAmount = Optional.ofNullable(coupon.getMinTotalAmount()).orElse(BigDecimal.ZERO);
            if (minTotalAmount.compareTo(BigDecimal.ZERO) > 0 && minTotalAmount.compareTo(saleAmount) > 0) {
                //未达可用门槛(门槛不为0)
                continue;
            }
            //计算优惠券金额
            BigDecimal amount;
            if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(coupon.getDiscountMethod())) {
                //按金额计算优惠
                amount = Optional.ofNullable(coupon.getAmount()).orElse(BigDecimal.ZERO);
            } else {
                //这里计算的优惠金额，直接用折扣（打9折 -> 0.1 ） 如果商品 100，优惠金额 = 100 * 0.1 = 10
                amount = coupon.getDiscount().multiply(saleAmount).setScale(2, RoundingMode.HALF_UP);
            }
            if (amount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            //计算最佳优惠券
            if (bestCoupon == null) {
                bestCoupon = coupon;
                bestAmount = amount;
            } else if (amount.compareTo(bestAmount) > 0) {
                //当前最优惠,替换最佳
                bestAmount = amount;
                bestCoupon = coupon;
            } else if (bestAmount.compareTo(amount) == 0) {
                //最大优惠金额相同，比较 结束时间(用户券比较使用时间，商品券比较领取时间)
                ZonedDateTime endDate = Optional.ofNullable(expireMap.get(coupon.getId())).map(UserCoupon::getGmtEnd).orElse(coupon.getReceiveEnd());
                ZonedDateTime bestEndDate = Optional.ofNullable(expireMap.get(bestCoupon.getId())).map(UserCoupon::getGmtEnd).orElse(bestCoupon.getReceiveEnd());
                if (endDate.isBefore(bestEndDate)) {
                    //当前最优惠先结束，使用当前优惠券
                    bestAmount = amount;
                    bestCoupon = coupon;
                }
            }
        }
        return bestCoupon;
    }

    public BigDecimal calTotalSaleAmount(List<OrderSkuVo> skuVos) {
        BigDecimal totalSaleAmount = BigDecimal.ZERO;
        List<Long> skuIds = skuVos.stream().map(OrderSkuVo::getSkuId).toList();
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        Map<Long, ZnsGoodsSkuEntity> skuMap = skuEntities.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, Function.identity(), (k1, k2) -> k1));
        for (OrderSkuVo skuVo : skuVos) {
            ZnsGoodsSkuEntity skuEntity = skuMap.get(skuVo.getSkuId());
            //销售价金额
            BigDecimal skuTotalAmount = skuEntity.getSalePrice().multiply(new BigDecimal(skuVo.getCount())).setScale(2, RoundingMode.HALF_UP);
            totalSaleAmount = totalSaleAmount.add(skuTotalAmount);
        }
        return totalSaleAmount;
    }

    /**
     * 计算商品优惠券明细
     *
     * @param skuCountMap : sku数量，key -> skuId, val -> 数量
     */
    public List<GoodsDiscountAmountDetailVo> calCouponAmountDetails(Coupon coupon, Map<Long, Integer> skuCountMap, List<ZnsGoodsSkuEntity> skuEntities) {
        String currencyCode = skuEntities.get(0).getCurrencyCode();
        if (coupon != null) {
            //校验币种,填充多币种金额
            List<CouponCurrencyEntity> couponCurrencyEntities = couponCurrencyService.findCouponCurrencyListOrDefault(List.of(coupon));
            CouponCurrencyEntity couponCurrencyEntity = couponCurrencyEntities.stream().filter(item -> Objects.equals(item.getCurrencyCode(), currencyCode)).findFirst().orElse(null);
            if (Objects.isNull(couponCurrencyEntity)) {
                //币种不匹配
                coupon = null;
            } else {
                coupon.fillCurrencyAmount(couponCurrencyEntity);
            }
        }
        //优惠总金额
        BigDecimal couponTotalAmount = calCouponTotalAmount(coupon, skuCountMap);

        //适用spuId
        List<Long> spuIds = getCouponSpuIds(coupon, skuEntities);

        //sku分组
        List<ZnsGoodsSkuEntity> couponSkuEntities = new ArrayList<>(); //分担券的sku
        List<ZnsGoodsSkuEntity> otherSkuEntities = new ArrayList<>(); //不能分担券的sku
        if (coupon == null) {
            //没有券
            otherSkuEntities = skuEntities;
        } else if (CouponConstant.UseScopeEnum.USE_SCOPE_2.type.equals(coupon.getUseScope())) {
            //部分商品
            for (ZnsGoodsSkuEntity skuEntity : skuEntities) {
                if (spuIds.contains(skuEntity.getGoodsId())) {
                    couponSkuEntities.add(skuEntity);
                } else {
                    otherSkuEntities.add(skuEntity);
                }
            }
        } else if (CouponConstant.UseScopeEnum.USE_SCOPE_3.type.equals(coupon.getUseScope())) {
            //单品券(只能作用于一个sku，所以这里取第一个)
            ZnsGoodsSkuEntity skuEntity = skuEntities.stream().filter(item -> spuIds.contains(item.getGoodsId())).findFirst().orElse(null);
            if (skuEntity != null) {
                couponSkuEntities.add(skuEntity);
                skuEntities.stream().filter(item -> !skuEntity.getId().equals(item.getId())).forEach(otherSkuEntities::add);
            } else {
                //没有适用的sku
                otherSkuEntities = skuEntities;
            }
        } else {
            //通用券
            couponSkuEntities = skuEntities;
        }

        //优惠销售价总金额
        BigDecimal couponSalePriceTotalAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(couponSkuEntities)) {
            for (ZnsGoodsSkuEntity couponSkuEntity : couponSkuEntities) {
                if (couponSkuEntity != null) {
                    BigDecimal skuTotalAmount = couponSkuEntity.getSalePrice().multiply(new BigDecimal(skuCountMap.getOrDefault(couponSkuEntity.getId(), 0)));
                    couponSalePriceTotalAmount = couponSalePriceTotalAmount.add(skuTotalAmount);
                }
            }
        }

        //商城销售价最小总金额
        BigDecimal minTotalAmount = getMinTotalAmount();

        //组装数据
        List<GoodsDiscountAmountDetailVo> result = new ArrayList<>();
        BigDecimal addCouponAmount = BigDecimal.ZERO; // 优惠累计总金额
        for (int i = 0; i < couponSkuEntities.size(); i++) {
            BigDecimal skuCouponAmount;
            GoodsDiscountAmountDetailVo detailVo;
            ZnsGoodsSkuEntity skuEntity = couponSkuEntities.get(i);
            Integer count = skuCountMap.getOrDefault(skuEntity.getId(), 0);
            if (couponSkuEntities.size() - 1 == i) {
                //最后一个用减法
                skuCouponAmount = couponTotalAmount.subtract(addCouponAmount);
                skuCouponAmount = skuCouponAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : skuCouponAmount;
                detailVo = new GoodsDiscountAmountDetailVo(currencyCode, skuEntity.getId(), count, skuEntity.getOriginalPrice(), skuEntity.getSalePrice(), skuCouponAmount, minTotalAmount);
            } else {
                //其他的用按比例计算
                BigDecimal skuTotalAmount = skuEntity.getSalePrice().multiply(new BigDecimal(count));
                skuCouponAmount = skuTotalAmount.divide(couponSalePriceTotalAmount, 8, RoundingMode.HALF_UP).multiply(couponTotalAmount).setScale(2, RoundingMode.HALF_UP);
                detailVo = new GoodsDiscountAmountDetailVo(currencyCode, skuEntity.getId(), count, skuEntity.getOriginalPrice(), skuEntity.getSalePrice(), skuCouponAmount, BigDecimal.ZERO);
            }
            addCouponAmount = addCouponAmount.add(skuCouponAmount);
            result.add(detailVo);
        }
        if (!CollectionUtils.isEmpty(otherSkuEntities)) {
            for (ZnsGoodsSkuEntity otherSkuEntity : otherSkuEntities) {
                Integer count = skuCountMap.getOrDefault(otherSkuEntity.getId(), 0);
                result.add(new GoodsDiscountAmountDetailVo(currencyCode, otherSkuEntity.getId(), count, otherSkuEntity.getOriginalPrice(), otherSkuEntity.getSalePrice(), BigDecimal.ZERO, BigDecimal.ZERO));
            }
        }
        return result;
    }

    /**
     * 计算商品优惠券优惠总金额
     *
     * @param skuCountMap : sku数量，key -> skuId, val -> 数量
     */
    public BigDecimal calCouponTotalAmount(Coupon coupon, Map<Long, Integer> skuCountMap) {
        if (coupon == null) {
            return BigDecimal.ZERO;
        }
        if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(coupon.getDiscountMethod())) {
            //金额券
            return coupon.getAmount();
        }

        //查询优惠券适用spu
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuCountMap.keySet());
        List<Long> spuIds = getCouponSpuIds(coupon, skuEntities);
        if (CollectionUtils.isEmpty(spuIds)) {
            return BigDecimal.ZERO;
        }

        //计算优惠金额
        BigDecimal spuTotalAmount = BigDecimal.ZERO;
        for (ZnsGoodsSkuEntity skuEntity : skuEntities) {
            if (!spuIds.contains(skuEntity.getGoodsId())) {
                //不是指定的spu
                continue;
            }
            BigDecimal skuTotalAmount = skuEntity.getSalePrice().multiply(new BigDecimal(skuCountMap.getOrDefault(skuEntity.getId(), 0)));
            spuTotalAmount = spuTotalAmount.add(skuTotalAmount);
        }
        //这里计算的优惠金额，直接用折扣（打9折 -> 0.1 ） 如果商品 100，优惠金额 = 100 * 0.1 = 10
        return coupon.getDiscount().multiply(spuTotalAmount).setScale(2, RoundingMode.HALF_UP);
    }

}
