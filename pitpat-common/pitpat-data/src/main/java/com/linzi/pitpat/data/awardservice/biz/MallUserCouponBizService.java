package com.linzi.pitpat.data.awardservice.biz;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponExchangeFailEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsDiscountAmountDetailVo;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderPickCouponDto;
import com.linzi.pitpat.data.mallservice.dto.response.OrderSkuAmountRespDto;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商城用户优惠券Biz
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MallUserCouponBizService {

    private final CouponService couponService;
    private final CouponCurrencyService couponCurrencyService;
    private final UserCouponService userCouponService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final MallCouponComponent mallCouponComponent;
    private final MallCouponConvertComponent mallCouponConvertComponent;
    private final UserExtraBizService userExtraBizService;


    /**
     * 计算订单券后价格
     */
    public OrderSkuAmountRespDto calOrderCouponAmount(List<OrderSkuVo> skuVos, ZnsUserEntity user, Long couponId) {
        //获取最佳优惠券
        Coupon coupon = null;
        if (user.getAppVersion() >= Constant.appVersion_4044) {
            coupon = Objects.isNull(couponId) ? findUserBestCoupon(skuVos, user) : couponService.findById(couponId);
        }

        //计算券后明细
        Map<Long, Integer> skuCountMap = skuVos.stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, OrderSkuVo::getCount, (k1, k2) -> k2));
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuCountMap.keySet());
        List<GoodsDiscountAmountDetailVo> detailVos = mallCouponComponent.calCouponAmountDetails(coupon, skuCountMap, skuEntities);

        //优惠券总金额
        BigDecimal couponAmount = detailVos.stream().map(item -> Optional.ofNullable(item.getCouponAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //原价总金额
        BigDecimal originalTotalAmount = skuEntities.stream().map(item -> Optional.ofNullable(item.getOriginalPrice()).orElse(item.getSalePrice()).multiply(new BigDecimal(skuCountMap.get(item.getId())))).reduce(BigDecimal.ZERO, BigDecimal::add);
        //销售价总金额
        BigDecimal totalSaleAmount = skuEntities.stream().map(item -> item.getSalePrice().multiply(new BigDecimal(skuCountMap.get(item.getId())))).reduce(BigDecimal.ZERO, BigDecimal::add);

        //组装结果
        OrderSkuAmountRespDto respDto = new OrderSkuAmountRespDto(originalTotalAmount, totalSaleAmount, couponAmount);
        respDto.setMallCouponDto(Optional.ofNullable(coupon).map(item -> mallCouponConvertComponent.appConvertDto(item, user, true, null)).orElse(null));
        respDto.setSkuDiscountAmountDetails(detailVos);
        return respDto;
    }

    /**
     * 获取用户已领取的适合优惠券
     */
    private Coupon findUserBestCoupon(List<OrderSkuVo> skuVos, ZnsUserEntity user) {
        List<Long> skuIds = skuVos.stream().map(OrderSkuVo::getSkuId).distinct().toList();
        List<Coupon> couponList;
        //获取用户已领取的适合优惠券
        couponList = findAllCouponByUser(skuIds, user.getId());
        if (CollectionUtils.isEmpty(couponList)) {
            return null;
        }
        //计算最佳优惠券
        return mallCouponComponent.calBestCoupon(skuVos, couponList, user);
    }

    /**
     * 获取用户已领取的适合优惠券
     */
    private List<Coupon> findAllCouponByUser(List<Long> skuIds, Long userId) {
        List<Coupon> result = new ArrayList<>();
        List<Long> couponIds = new ArrayList<>();
        //查询用户已领取的全品类优惠券
        List<Long> wholeCouponIds = userCouponService.findUserWholeCouponId(userId, CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type, true);
        if (!CollectionUtils.isEmpty(wholeCouponIds)) {
            couponIds.addAll(wholeCouponIds);
        }
        //查询spuIds
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        List<Long> spuIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();

        //查询用户已领取的特定商品优惠券
        List<Long> specialCouponIds = userCouponService.findUserSpecialCouponId(userId, spuIds, CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type, true);
        if (!CollectionUtils.isEmpty(specialCouponIds)) {
            couponIds.addAll(specialCouponIds);
        }
        if (CollectionUtils.isEmpty(couponIds)) {
            return result;
        }
        List<Coupon> coupons = couponService.findListByIds(couponIds);
        ZnsGoodsEntity goodsEntity = znsGoodsService.findById(spuIds.get(0));
        coupons = coupons.stream().filter(item -> item.getCountryCodes().contains(goodsEntity.getCountryCode())).collect(Collectors.toList());

        //校验币种,填充多币种金额
        List<CouponCurrencyEntity> couponCurrencyEntities = couponCurrencyService.findCouponCurrencyListOrDefault(coupons);
        if (CollectionUtils.isEmpty(couponCurrencyEntities)) {
            return result;
        }
        Map<Long, CouponCurrencyEntity> currencyMap = couponCurrencyEntities.stream().filter(item -> Objects.equals(item.getCurrencyCode(), skuEntities.get(0).getCurrencyCode()))
                .collect(Collectors.toMap(CouponCurrencyEntity::getCouponId, Function.identity(), (k1, k2) -> k2));
        //填充优惠券多币种金额
        for (Coupon coupon : coupons) {
            CouponCurrencyEntity couponCurrencyEntity = currencyMap.get(coupon.getId());
            if (couponCurrencyEntity != null) {
                coupon.fillCurrencyAmount(couponCurrencyEntity);
                result.add(coupon);
            }
        }
        return coupons;
    }

    /**
     * 根据商品查询用户优惠券列表
     */
    public List<OrderPickCouponDto> findUserCouponBySkus(List<OrderSkuVo> skuList, ZnsUserEntity user) {
        List<OrderPickCouponDto> result = new ArrayList<>();
        if (user.getAppVersion() < Constant.appVersion_4044) {
            return result;
        }

        //查询用户未使用+未过期的优惠券
        UserCouponQuery userCouponQuery = new UserCouponQuery().setUserId(user.getId()).setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type)
                .setCouponStatus(UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type).setTime(ZonedDateTime.now());
        List<UserCoupon> userCoupons = userCouponService.findListByQuery(userCouponQuery);

        //查询已过期的优惠券
        UserCouponQuery userCouponQuery2 = new UserCouponQuery().setUserId(user.getId())
                .setStatus(List.of(UserCouponConstant.UserCouponStatusEnum.USE_STATUS_3.type, UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type))
                .setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type).setGmtEndLe(ZonedDateTime.now());
        List<UserCoupon> userCoupons2 = userCouponService.findListByQuery(userCouponQuery2);
        if (!CollectionUtils.isEmpty(userCoupons2)) {
            userCoupons.addAll(userCoupons2);
        }
        if (CollectionUtils.isEmpty(userCoupons)) {
            return result;
        }
        Map<Long, List<UserCoupon>> userCouponMap = userCoupons.stream().collect(Collectors.groupingBy(UserCoupon::getCouponId));

        //按门槛排序（门槛较低的在前；相同门槛，折扣在前，优惠金额大的在前（折扣顺序，金额倒序），最后按id倒序）
        List<Coupon> couponList = couponService.findListByIds(userCouponMap.keySet());
        couponList = mallCouponComponent.couponSort(couponList);
        String currencyCode = userExtraBizService.findUserCurrencyCode(user.getId(), null);
        List<MallCouponDto> mallCouponDtos = mallCouponConvertComponent.appConvertDto(couponList, user, true, currencyCode, null);//转换格式
        if (CollectionUtils.isEmpty(mallCouponDtos)) {
            return result;
        }

        //添加用户券+不可用原因
        Map<Long, List<String>> map = couponList.stream().collect(Collectors.toMap(Coupon::getId, Coupon::getCountryCodes));
        for (MallCouponDto mallCouponDto : mallCouponDtos) {
            List<UserCoupon> list = userCouponMap.get(mallCouponDto.getCouponId());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            //每个券都要展示
            list.sort(Comparator.comparing(UserCoupon::getGmtEnd));//按结束时间升序
            for (UserCoupon userCoupon : list) {
                //校验用户券状态
                Set<UserCouponConstant.UnAvailableReasonEnum> reasonEnums = mallCouponComponent.checkUserCouponBySku(userCoupon, skuList);
                int isAvailable = 1;
                StringJoiner remarkJoiner =  new StringJoiner(";");
                if (!CollectionUtils.isEmpty(reasonEnums)) {
                    for (UserCouponConstant.UnAvailableReasonEnum reasonEnum : reasonEnums) {
                        if (reasonEnum == UserCouponConstant.UnAvailableReasonEnum.REASON_7){
                            //国家不可用需要特殊转换
                            String countryNames = map.get(userCoupon.getCouponId()).stream().map(countryCode -> I18nConstant.CountryCodeEnum.findByCode(countryCode).enName).collect(Collectors.joining(","));
                            remarkJoiner.add(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "coupon.check.reason.REASON_7",countryNames));
                        }else {
                            remarkJoiner.add(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "coupon.check.reason." + reasonEnum)) ;
                        }
                    }
                    isAvailable = 0;
                }
                OrderPickCouponDto orderPickCouponDto = new OrderPickCouponDto(mallCouponDto, isAvailable, remarkJoiner.toString());
                orderPickCouponDto.setGmtStart(ZonedDateTimeUtil.convertFrom(userCoupon.getGmtStart()));
                orderPickCouponDto.setGmtEnd(ZonedDateTimeUtil.convertFrom(userCoupon.getGmtEnd()));
                orderPickCouponDto.setUserCouponId(userCoupon.getId());
                result.add(orderPickCouponDto);
            }
        }
        return result;
    }

    /**
     * 给用户发券
     *
     * @param isAdmin : 是否运营后台，true：是-不校验发放数量，false：不是-校验发放数量
     * @see CouponExchangeFailEnum 失败原因
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> sendCouponToUser(Coupon coupon, ZnsUserEntity user, Boolean isAdmin, Boolean isRetainPop) {
        //校验
        CouponExchangeFailEnum exchangeFailEnum = mallCouponConvertComponent.checkSendCoupon(coupon, user, isAdmin, isRetainPop);
        if (exchangeFailEnum != null) {
            log.info("[receive],领取失败,couponId={},userId={},reason={}", coupon.getId(), user.getId(), exchangeFailEnum.getDesc());
            if (exchangeFailEnum == CouponExchangeFailEnum.COUPON_INVALID) {
                //优惠券开关关闭 || 优惠券code错误 => 优惠券不存在，请检查code
                throw new BaseException(I18nMsgUtils.getMessage("common.operate.reenter"));
            } else if (exchangeFailEnum == CouponExchangeFailEnum.COUPON_EXPIRED || exchangeFailEnum == CouponExchangeFailEnum.INSUFFICIENT_COUPON_STOCK) {
                //达到发放上 || 超过领取时间 =>  优惠券发完
                throw new BaseException(I18nMsgUtils.getMessage("coupon.count.redeemed"));
            } else if (exchangeFailEnum == CouponExchangeFailEnum.EXCEED_USER_MAX) {
                //超过单人领取上限 => 领取达到上限，请在结算时使用
                throw new BaseException(I18nMsgUtils.getMessage("coupon.user.max"), ActivityError.MALL_ORDER_COUPON_USER_MAX_FAIL.getCode());
            } else if (exchangeFailEnum == CouponExchangeFailEnum.NOT_START) {
                throw new BaseException(I18nMsgUtils.getMessage("coupon.not.start"));
            }
            throw new BaseException(CommonError.SYSTEM_ERROR.getMsg());
        }

        //校验用户国家
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(user, null);
        if (!coupon.getCountryCodes().contains(mallCountryCode)) {
            throw new BaseException(I18nMsgUtils.getMessage("coupon.cannot.support.country", I18nConstant.CountryCodeEnum.findByCode(mallCountryCode).enName));
        }

        //发券
        UserCouponManager userCouponManager = SpringContextUtils.getBean(UserCouponManager.class);
        Result<Map<String, Object>> result = userCouponManager.exchangeUserCoupon(coupon, null, user, CouponConstant.SourceTypeEnum.source_type_200.type, null);
        return result.getData();
    }


}
