package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-11-10
 */

@Data
@NoArgsConstructor
@TableName("zns_paypal_pay")
public class PaypalPay implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.PaypalPay:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";        // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    // 最后修改时间
    public final static String pay_no = CLASS_NAME + "pay_no";                // 支付流水号
    public final static String pay_type = CLASS_NAME + "pay_type";            // 支付类型：1：paypal
    public final static String trade_no = CLASS_NAME + "trade_no";            // 第三方交易流水号
    public final static String amount_ = CLASS_NAME + "amount";               // 支付金额
    public final static String type_ = CLASS_NAME + "type";                   // 购买类型 类型：优惠劵 里程碑 ，形象皮肤，会员购买',
    public final static String ref_id = CLASS_NAME + "ref_id";                // 关联类型主键id
    public final static String info_ = CLASS_NAME + "info";                   // 其他信息 json{}-》与类型相关
    public final static String status_ = CLASS_NAME + "status";               // 状态：0：发起支付；1:支付成功；2:支付中；-1:支付失败
    public final static String user_id = CLASS_NAME + "user_id";              // 用户id
    public final static String fail_reason = CLASS_NAME + "fail_reason";      // 失败原因
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;
    //创建时间
    @TableField(select = false)
    private ZonedDateTime gmtCreate;
    //最后修改时间
    @TableField(select = false)
    private ZonedDateTime gmtModified;
    //支付流水号
    private String payNo;
    /**
     * 支付类型 1：paypal，2：钱海
     *
     * @see PayConstant.PayTypeEnum
     */
    private Integer payType;

    //第三方交易流水号
    private String tradeNo;
    //支付金额
    private BigDecimal amount;

    /**
     * 购买类型 类型："coupon":优惠券,"vip":会员,"scoreExchange":积分商城,"exchangeWear": 我的形象皮肤,"battlePass":新里程碑 ,"mallOrder":商城订单 ,"refundOrder":退款单
     *
     * @see PayConstant.BuyTypeEnum
     */
    private String type;

    //关联类型主键id
    private Long refId;

    //其他信息 json{}-》与类型相关
    private String info;

    /**
     * 状态：0：发起支付；1:支付成功；2:支付中；-1:支付失败
     *
     * @see PayConstant.PayStatusEnum
     */
    private Integer status;

    //用户id
    private Long userId;
    //失败原因
    private String failReason;
    //新表流水号
    private String newTradeNo;

    /**
     * 货币code，USD：美元，CAD：加币，EUR：欧元，GBP：英镑
     *
     * @see I18nConstant.CurrencyCodeEnum
     * @since 4.7.0
     */
    private String currencyCode;

    /**
     * 币种符号
     *  @since 4.7.0
     */
    private String currencySymbol;

    /**
     * 创建订单支付订单
     */
    public PaypalPay(ZnsOrderEntity orderEntity, String tradeNo, String param, String type, Long userId, Integer payType) {
        this.amount = orderEntity.getActualAmount();
        this.type = type;
        this.refId = orderEntity.getId();
        this.info = param;
        this.payNo = orderEntity.getOrderNo();
        this.currencyCode = orderEntity.getCurrencyCode();
        this.currencySymbol = orderEntity.getCurrencySymbol();
        this.tradeNo = tradeNo;
        this.userId = userId;
        this.status = PayConstant.PayStatusEnum.PAY_STATUS_0.type;
        this.payType = payType;
    }

    /**
     * 创建退款支付单
     */
    public PaypalPay(OrderRefund orderRefund, Integer payType, String tradeNo, String type) {
        this.amount = orderRefund.getApplyAmount();
        this.userId = orderRefund.getUserId();
        this.refId = orderRefund.getId();
        this.payNo = orderRefund.getRefundNo();
        this.currencyCode = orderRefund.getCurrencyCode();
        this.currencySymbol = orderRefund.getCurrencySymbol();
        this.payType = payType;
        this.type = type;
        this.tradeNo = tradeNo;
        this.status = PayConstant.PayStatusEnum.PAY_STATUS_0.type;
    }

    /**
     * 更新付款单
     */
    public PaypalPay(Long id) {
        this.id = id;
    }


    public boolean isMallOrder() {
        return Objects.equals(this.type, PayConstant.BuyTypeEnum.MALL_ORDER.type) || Objects.equals(this.type, PayConstant.BuyTypeEnum.H5_MALL_ORDER.type)
                || Objects.equals(this.type, PayConstant.BuyTypeEnum.REFUND_ORDER.type);
    }
}
