package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import com.linzi.pitpat.core.entity.CurrencyAmount;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class OfficialActivityDto {

    private String activityTitle;

    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;

    //目标类型 1 距离 2 时间
    private Integer completeType;

    /**
     * 距离 completeType = 1
     */
    private BigDecimal runMileage;
    /**
     * 时间 completeType = 2
     */
    private Integer runTime;
    /**
     * 赛事标签（0:最新，1：最热）
     */
    private Integer eventTags;

    //报名金额
    private CurrencyAmount applicationAmount;
    //报名积分
    private Integer applicationScore;


}
