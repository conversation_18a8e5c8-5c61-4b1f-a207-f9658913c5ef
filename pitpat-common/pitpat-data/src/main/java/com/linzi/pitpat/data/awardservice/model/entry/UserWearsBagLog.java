package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.data.awardservice.constant.enums.WearConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户服装背包log
 *
 * <AUTHOR>
 * @since 2023-10-12
 */

@Data
@NoArgsConstructor
@TableName("zns_user_wears_bag_log")
public class UserWearsBagLog implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键ID
    public final static String is_delete = CLASS_NAME + "is_delete";          // 是否删除（0否 1是）
    public final static String gmt_create = CLASS_NAME + "gmt_create";        //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    //
    public final static String bag_id = CLASS_NAME + "bag_id";                // wear_bag_id 关联
    public final static String expired_time = CLASS_NAME + "expired_time";    // 失效时间 0 代表永久
    public final static String user_id = CLASS_NAME + "user_id";              // 用户id
    public final static String activity_id = CLASS_NAME + "activity_id";      // 发放奖励的时候对应的活动id
    public final static String sort_num = CLASS_NAME + "sort_num";            // 关卡奖励里程碑用
    public final static String is_new = CLASS_NAME + "is_new";                    // 新标识 0: 否, 1: 是
    //主键ID
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除（0否 1是）
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //wear_bag_id 关联
    private Long bagId;
    //失效时间 0 代表永久
    private Integer expiredTime;
    //用户id
    private Long userId;
    //发放奖励的时候对应的活动id
    private Long activityId;
    //关卡奖励里程碑用
    private Integer sortNum;
    //新标识 0: 否, 1: 是
    private Integer isNew;

    /**
     * 奖励来源，0：活动奖励，1：活动进阶奖励, 2：积分兑换 40 衍生品绑定
     *
     * @see WearConstant.SourceTypeEnum
     */
    private Integer source;

    /**
     * 来源业务key（主键id或者业务code）
     *
     * @since 4.6.0
     */
    private String sourceKey;

}
