package com.linzi.pitpat.data.activityservice.manager.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityPaceSetting;
import com.linzi.pitpat.data.activityservice.dto.console.request.BrandRightDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.EnableActStatusRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.RotSetting;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityFeeAndAwardCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityReportCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityRuleCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.model.dto.EffectMileageDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.Gameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.service.GameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.request.UserPlacementLevelChartRequest;
import com.linzi.pitpat.data.userservice.dto.response.PlacementLevelResponse;
import com.linzi.pitpat.data.userservice.dto.response.SpeedLineDto;
import com.linzi.pitpat.data.userservice.dto.response.UserPlacementLevelActivityResponse;
import com.linzi.pitpat.data.userservice.dto.response.UserPlacementLevelActivityResultResp;
import com.linzi.pitpat.data.userservice.dto.response.UserPlacementLevelActivityStartResp;
import com.linzi.pitpat.data.userservice.dto.response.UserPlacementLevelDto;
import com.linzi.pitpat.data.userservice.dto.response.UserPlacementLevelHistoryResponse;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelDo;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelLogDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelLogPageQuery;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelLogQuery;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelQuery;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelLogService;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
@RequiredArgsConstructor
@Slf4j
public class PlacementLevelApiManager {
    private final ISysConfigService sysConfigService;
    private final MainActivityBizService mainActivityBizService;
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityConfigService znsRunActivityConfigService;
    private final GameplayService gameplayService;
    private final RoomIdBizService roomIdBizService;
    private final UserPlacementLevelService userPlacementLevelService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final UserPlacementLevelLogService userPlacementLevelLogService;
    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ActivityDisseminateBizService activityDisseminateBizService;


    public PlacementLevelResponse getLevelConfig(Integer scenario) {
        var placementLevelResponse = new PlacementLevelResponse();
        List<EffectMileageDto> effectMileageDtos = sysConfigService.selectConfigListByKey(ConfigKeyEnums.EFFECT_MILEAGES.getCode(), EffectMileageDto.class);
        Map<Integer, List<Integer>> mileageByScenario = effectMileageDtos.stream()
                .filter(dto -> dto.getEffectMileages() != null) // 过滤掉 null 列表
                .collect(Collectors.groupingBy(
                        EffectMileageDto::getScenario, // 按性别分组
                        Collectors.flatMapping(
                                dto -> dto.getEffectMileages().stream(),
                                Collectors.toList() // 收集为列表
                        )
                ));
        mileageByScenario.values().forEach(Collections::sort);
        placementLevelResponse.setMileageConfigList(mileageByScenario.get(scenario));
        UserPlacementLevelDo userPlacementLevel = userPlacementLevelService.findByQuery(new UserPlacementLevelQuery());
        if(Objects.nonNull(userPlacementLevel)){
            placementLevelResponse.setLevelCode(userPlacementLevel.getLevelCode());
        }
        return placementLevelResponse;
    }

    /**
     * 用户创建定级赛
     *
     * @param loginUser
     * @param targetMileage
     * @return
     */
    public UserPlacementLevelActivityStartResp startPlacementActivity(ZnsUserEntity loginUser, Integer targetMileage) {
        var userPlacementLevelActivityStartResp = new UserPlacementLevelActivityStartResp();
        MainActivity mainActivity = createPlacementActivity(targetMileage);
        ZnsRunActivityUserEntity activityUser = createActivityUser(loginUser, mainActivity.getId(), targetMileage);
        runActivityUserService.save(activityUser);
        userPlacementLevelActivityStartResp.setActivityId(mainActivity.getId());
        userPlacementLevelActivityStartResp.setTargetType(1);
        userPlacementLevelActivityStartResp.setEntryCount(1);
        userPlacementLevelActivityStartResp.setRankingBy("2");
        Integer roomNumber = roomIdBizService.getRoomId(mainActivity, Collections.singletonList(targetMileage));
        userPlacementLevelActivityStartResp.setRealRoomId(Long.valueOf(roomNumber));
        userPlacementLevelActivityStartResp.setActivityType(mainActivity.getOldType());
        userPlacementLevelActivityStartResp.setRouteId(1401L);
        userPlacementLevelActivityStartResp.setStartTime(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), "UTC" ));
        return userPlacementLevelActivityStartResp;
    }

    /**
     * 创建定级赛活动报名用户
     * @param loginUser
     * @param activityId
     * @param targetMileage
     * @return
     */
    private ZnsRunActivityUserEntity createActivityUser(ZnsUserEntity loginUser, Long activityId, Integer targetMileage) {
        ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
        activityUser.setActivityId(activityId);
        activityUser.setUserId(loginUser.getId());
        activityUser.setIsRobot(loginUser.getIsRobot());
        activityUser.setIsTest(loginUser.getIsTest());
        activityUser.setNickname(loginUser.getFirstName());
        activityUser.setActivityType(RunActivityTypeEnum.PLACEMENT_ACTIVITY.getType());
        activityUser.setUserState(1);
        // 活动参与者
        activityUser.setInviterUserId(loginUser.getId());
        activityUser.setTargetRunMileage(targetMileage);
        activityUser.setTargetRunTime(null);
        return activityUser;
    }

    /**
     * 创建定级赛活动
     * @param targetMileage
     * @return
     */
    private MainActivity createPlacementActivity(Integer targetMileage) {
        SingleActivityCreateRequest rankActivityRequest = buildSingleActivityCreateRequest(targetMileage);
        MainActivity rankActivity = mainActivityBizService.createSingleActivity(rankActivityRequest, MainActivityTypeEnum.PLACEMENT);
        //发布活动
        EnableActStatusRequest request = new EnableActStatusRequest();
        request.setStatus(0);
        request.setActivityIds(List.of(rankActivity.getId()));
        mainActivityBizService.changeStatus(request);
        //开始活动
        rankActivity.setActivityState(MainActivityStateEnum.STARTED.getCode());
        mainActivityService.update(rankActivity);
        return rankActivity;
    }

    private SingleActivityCreateRequest buildSingleActivityCreateRequest(Integer targetMileage) {
        SingleActivityCreateRequest request = new SingleActivityCreateRequest();
        ZnsRunActivityConfigEntity runActivityConfig = znsRunActivityConfigService.getByType(RunActivityTypeEnum.PLACEMENT_ACTIVITY.getType(), null);
        //赛事规则实体
        request.setSingleActivityRuleCreateRequest(buildSingleActivityRuleCreateRequest(targetMileage));
        //赛事报名实体
        request.setSingleActivityReportCreateRequest(buildSingleActivityReportCreateRequest());
        //赛事费用奖励实体
        request.setSingleActivityFeeAndAwardCreateRequest(buildSingleActivityFeeAndAwardCreateRequest());
        //赛事说明宣发实体
        request.setSingleActivityDistributionCreateRequest(buildSingleActivityDistributionCreateRequest());
        log.info("奖励 json={}", request);
        return request;
    }

    private SingleActivityFeeAndAwardCreateRequest buildSingleActivityFeeAndAwardCreateRequest() {
        //开始构建request
        SingleActivityFeeAndAwardCreateRequest request = new SingleActivityFeeAndAwardCreateRequest();
        request.setLunchChallengeScore(null);
        request.setCouponId(null);
        request.setAllowCoupon(0);
        request.setType("free");
        request.setAmounts(List.of());
        request.setScore(0);
        BrandRightDto brandRightDto = new BrandRightDto();
        brandRightDto.setBrandRightAwardDtos(List.of());
        request.setBrandRightDto(brandRightDto);
        //构建奖励
        request.setTargetAwardDtos(List.of());
        return request;
    }

    private SingleActivityDistributionCreateRequest buildSingleActivityDistributionCreateRequest() {
        SingleActivityDistributionCreateRequest request = new SingleActivityDistributionCreateRequest();

        List<SingleDistributionCreateRequest> createRequestList = Arrays.stream(I18nConstant.LanguageCodeEnum.VALUES).map(item -> {
            SingleDistributionCreateRequest createRequest = new SingleDistributionCreateRequest();
            createRequest.setShowMode(0);
            createRequest.setIsDefault(Objects.equals(item.getCode(), I18nConstant.LanguageCodeEnum.en_US.getCode()) ? 1 : 0);
            createRequest.setLanguageCode(item.getCode());
            String title = I18nMsgUtils.getLangMessage(item.getCode(), "placementLevel.title");
            createRequest.setTitle(title);
            createRequest.setCategoryType(0);
            createRequest.setMarquee(0);
            createRequest.setEnableRankBeforeEnd(0);
            createRequest.setShowUserSameAct(0);
            return createRequest;
        }).toList();

        request.setDistributionRequests(createRequestList);
        return request;
    }

    /**
     * 赛事规则实体
     *
     * @param targetMileage
     * @return
     */
    private SingleActivityRuleCreateRequest buildSingleActivityRuleCreateRequest(Integer targetMileage) {
        String sysConfig = sysConfigService.selectConfigByKey("placement_activity_config");
        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);
        Gameplay gameplay = gameplayService.findById(MapUtils.getLong(rankedSysConfig, "gamePlayId"));
        SingleActivityRuleCreateRequest request = new SingleActivityRuleCreateRequest();
        List empty = List.of();
        request.setPlayId(gameplay.getId());
        request.setRemark("用户定级赛");
        request.setTargets(List.of(targetMileage));//目标
        request.setSpeedLimit(-1);
        request.setRateLimit(empty);
        request.setAllowProp(0);
        request.setActivityPropConfig(empty);
        request.setCheatSwitch(-1);
        request.setEnterLimit(1);
        request.setUserEnterLimit(-1);
        request.setRouteId(1401L);
        request.setSpecialEffectTheme(0);
        request.setRunwaySty("0");
        request.setMusicListId(empty);
        request.setRateLimit(empty);
        request.setTargetType(1);
        //开启反作弊
        request.setCheatSwitch(2);
        return request;
    }

    private SingleActivityReportCreateRequest buildSingleActivityReportCreateRequest() {
        SingleActivityReportCreateRequest request = new SingleActivityReportCreateRequest();
        List empty = List.of();
        request.setEquipments(empty);
        request.setTimeStyle(0);
        //这里先随便设置下时间，后面会另外更新（只是防止创建活动报错）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime now = ZonedDateTime.now().withSecond(0).plusHours(8L);//+8个小时是为了兼容现在的时间是用字符串存储的，
        request.setApplicationStartTime(formatter.format(now)); //报名开始时间 = 现在
        ZonedDateTime applicationEndTime = now.plusMinutes(5); //报名结束时间 = 报名开始时间+5min
        ZonedDateTime activityStartTime = now.plusSeconds(30); //活动开始时间 = 报名开始时间+30s
        request.setApplicationEndTime(formatter.format(applicationEndTime));
        request.setActivityStartTime(formatter.format(activityStartTime));//活动开始时间 = 报名结束时间
        ZonedDateTime activityEndTime = applicationEndTime.plusMinutes(10);
        request.setActivityEndTime(formatter.format(activityEndTime));//活动结束时间 = 活动开始时间 + 10分钟
        request.setGroupIds(empty);
        request.setRotSetting(new RotSetting());
        request.setHasPacer(0);
        request.setClotheType("0");
        request.setMutexActivityIdList(empty);
        request.setAreas(empty);
        request.setPaceSetting(List.of(new ActivityPaceSetting()));
        request.setWaitTime(0);
        return request;
    }


    public Page<UserPlacementLevelActivityResponse> speedChartPage(ZnsUserEntity loginUser, UserPlacementLevelChartRequest pageQuery) {
        UserPlacementLevelLogPageQuery userPlacementLevelLogPageQuery = new UserPlacementLevelLogPageQuery();
        userPlacementLevelLogPageQuery.setPageNum(pageQuery.getPageNum());
        userPlacementLevelLogPageQuery.setPageSize(pageQuery.getPageSize());
        userPlacementLevelLogPageQuery.setUserId(loginUser.getId());
        userPlacementLevelLogPageQuery.setScenario(loginUser.getGender());
        Page<UserPlacementLevelLogDo> page = userPlacementLevelLogService.findPage(userPlacementLevelLogPageQuery);
        Page<UserPlacementLevelActivityResponse> responsePage = new Page<>();
        responsePage.setTotal(page.getTotal());
        responsePage.setCurrent(page.getCurrent());
        responsePage.setSize(page.getSize());
        responsePage.setRecords(page.getRecords().stream().map(item -> {
            var userPlacementLevelActivityResponse = new UserPlacementLevelActivityResponse();
            List<ZnsUserRunDataDetailsSecondEntity> secondsList = userRunDataDetailsSecondService.getSecondsList(item.getDetailId());
            // 转换 70 点数据
            List<ZnsUserRunDataDetailsSecondEntity> secondSplitsEntities = resampleTo70Points(secondsList);
            userPlacementLevelActivityResponse.setHistoryList(secondSplitsEntities.stream()
                    .map(entity -> {
                        SpeedLineDto dto = new SpeedLineDto();
                        dto.setSpeed(entity.getVelocity());
                        dto.setRunTime(entity.getRunTime());
                        return dto;
                    })
                    .collect(Collectors.toList()));
            userPlacementLevelActivityResponse.setScenario(loginUser.getGender());
            userPlacementLevelActivityResponse.setCurrentLevelCode(item.getCurrentLevelCode());
            userPlacementLevelActivityResponse.setCurrentLevelScore(item.getCurrentLevelScore());
            userPlacementLevelActivityResponse.setCurrentLevelName(item.getCurrentLevelName());
            userPlacementLevelActivityResponse.setScenario(loginUser.getGender());
            RealPersonRunDataDetails realPersonRunDataDetails = realPersonRunDataDetailsService.findByDetailsId(item.getDetailId());
            MainActivity mainActivity = mainActivityService.findById(realPersonRunDataDetails.getActivityId());
            ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(realPersonRunDataDetails.getActivityId(), loginUser.getLanguageCode());
            if (Objects.nonNull(disseminate)) {
                userPlacementLevelActivityResponse.setActivityTitle(disseminate.getTitle());
            }
            userPlacementLevelActivityResponse.setMainType(mainActivity.getMainType());
            userPlacementLevelActivityResponse.setGmtCreate(item.getGmtCreate());
            userPlacementLevelActivityResponse.setTargetMillage(item.getTargetMileage());
            return userPlacementLevelActivityResponse;
        }).toList());
        return responsePage;
    }


    /**
     * 使用Stream API将每秒数据列表等分为70个点
     * @param secondsList 原始每秒数据列表
     * @return 重采样后的70个数据点
     */
    public <T> List<T> resampleTo70Points(List<T> secondsList) {
        int originalSize = secondsList.size();

        // 处理空列表
        if (originalSize == 0) {
            return new ArrayList<>();
        }

        // 使用Stream生成70个采样点
        return IntStream.range(0, 70)
                .mapToObj(i -> {
                    if (originalSize <= 70) {
                        // 原始数据少于等于70个，直接取索引或重复最后一个
                        int index = Math.min(i, originalSize - 1);
                        return secondsList.get(index);
                    } else {
                        // 原始数据多于70个，均匀采样
                        double position = (double) i / 69 * (originalSize - 1);
                        int index = (int) Math.floor(position);
                        index = Math.min(index, originalSize - 1);
                        return secondsList.get(index);
                    }
                })
                .collect(Collectors.toList());
    }


    public UserPlacementLevelActivityResultResp activityResult(ZnsUserEntity loginUser, Long detailId) {
        var data = new UserPlacementLevelActivityResultResp();
        RealPersonRunDataDetails entity = realPersonRunDataDetailsService.findByDetailsId(detailId);
        if(Objects.isNull(entity)){
            data.setIsComplete(YesNoStatus.NO.getCode());
            return data;
        }
        ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(entity.getActivityId(), loginUser.getId());
        data.setIsCheat(entity.getIsCheat());
        data.setIsComplete(activityUser.getIsComplete());
        return data;
    }

    public UserPlacementLevelHistoryResponse historyInfo(ZnsUserEntity loginUser) {
        var userPlacementLevelHistoryResponse = new UserPlacementLevelHistoryResponse();
        UserPlacementLevelDo userPlacementLevelDo = userPlacementLevelService.findByQuery(new UserPlacementLevelQuery().setUserId(loginUser.getId()));
        if(Objects.isNull(userPlacementLevelDo)){
            return userPlacementLevelHistoryResponse;
        }
        userPlacementLevelHistoryResponse.setCurrentLevelCode(userPlacementLevelDo.getLevelCode());
        userPlacementLevelHistoryResponse.setCurrentLevelScore(userPlacementLevelDo.getLevelScore());
        userPlacementLevelHistoryResponse.setScenario(userPlacementLevelDo.getScenario());
        List<UserPlacementLevelLogDo> list = userPlacementLevelLogService.findList(new UserPlacementLevelLogQuery().setUserId(loginUser.getId()).setScenario(loginUser.getGender()));
        if(!CollectionUtils.isEmpty(list)){
            userPlacementLevelHistoryResponse.setHistoryList(list.stream().map(item -> {
                UserPlacementLevelDto userPlacementLevelDto = new UserPlacementLevelDto();
                userPlacementLevelDto.setLevelCode(item.getCurrentLevelCode());
                userPlacementLevelDto.setLevelName(item.getCurrentLevelName());
                userPlacementLevelDto.setLevelScore(item.getCurrentLevelScore());
                return userPlacementLevelDto;
            }).toList());
        }
        return userPlacementLevelHistoryResponse;
    }
}
