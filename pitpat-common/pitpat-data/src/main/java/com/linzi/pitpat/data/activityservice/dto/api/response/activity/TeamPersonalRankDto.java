package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class TeamPersonalRankDto {
    private Long userId;
    //排名
    private Integer rank;
    //头像
    private String avatar;
    //名字
    private String name;
    //运动场次
    private Integer runCount;
    // 完成最后一场用户赛时间
    private ZonedDateTime completeTime;

}
