package com.linzi.pitpat.data.activityservice.dto.console.response;

import com.linzi.pitpat.data.activityservice.model.entity.ProActivityCardI18n;
import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 资格卡响应DTO
 *
 * @since 2025年6月19日
 */
@Data
public class ProActivityCardResponseDto implements Serializable {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 年份
     */
    private Integer year;

    /**
     * 赛事类型
     *
     * @see com.linzi.pitpat.data.activityservice.constant.enums.pro.ProActivityType
     */
    private String competitiveType;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 卡类型
     * 
     * @see com.linzi.pitpat.data.activityservice.enums.ProActivityCardTypeEnum
     */
    private String cardType;
    
    /**
     * 默认语言
     */
    private String defaultLanguageCode;
    
    /**
     * 多语言信息
     */
    private List<ProActivityCardI18n> i18nInfo;
    
    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;
    
    /**
     * 更新时间
     */
    private ZonedDateTime gmtModified;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 修改人
     */
    private String modifier;
} 