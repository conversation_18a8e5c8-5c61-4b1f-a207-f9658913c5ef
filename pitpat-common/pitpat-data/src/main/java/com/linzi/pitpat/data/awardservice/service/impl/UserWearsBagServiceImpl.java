package com.linzi.pitpat.data.awardservice.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.awardservice.constant.enums.WearConstant;
import com.linzi.pitpat.data.awardservice.mapper.UserWearsBagDao;
import com.linzi.pitpat.data.awardservice.mapper.UserWearsBagLogDao;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 用户服装背包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */

@Slf4j
@Service
public class UserWearsBagServiceImpl implements UserWearsBagService {


    @Autowired
    private UserWearsBagDao userWearsBagDao;

    @Autowired
    private UserWearsBagLogDao userWearsBagLogDao;

    @Override
    public UserWearsBag findByById(Long id) {
        return userWearsBagDao.selectById(id);
    }


    @Override
    public Integer insert(UserWearsBag userWearsBag) {
        return userWearsBagDao.insert(userWearsBag);
    }

    @Override
    public int update(UserWearsBag userWearsBag) {
        return userWearsBagDao.updateById(userWearsBag);
    }

    @Override
    public List<UserWearsBag> findListByUserIdAndIsNew(Long userId, Integer isNew) {
        return userWearsBagDao.getUserWearsByUserIdAndIsNew(userId, isNew);
    }

    @Override
    public List<UserWearsBag> findList(Long userId, Long activityId) {
        return userWearsBagDao.selectListByUserIdAndActivityId(userId, activityId);
    }

    @Override
    public UserWearsBag findByQuery(UserWearBagQuery query) {
        return userWearsBagDao.getByQuery(query);
    }


    //当前时间续期
    public ZonedDateTime getAddHours(Integer expiredTime) {
        ZonedDateTime date = ZonedDateTime.now();
        if (date.getMinutes() > 0) {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(DateUtil.addHours(ZonedDateTime.now(), 1), 0), 0), expiredTime * 24);
        } else {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(ZonedDateTime.now(), 0), 0), expiredTime * 24);
        }
    }


    @Override
    public List<UserWearsBag> findList(Long userId, Long activityId, ZonedDateTime createTime) {
        return userWearsBagDao.queryList(userId, activityId, createTime);
    }

    @Override
    public Long sendUserWear(Long userId, WearAwardDto wearAwardDto, Long activityId) {
        UserWearBagQuery userWearBagQuery = UserWearBagQuery.builder()
                .userId(userId)
                .wearType(wearAwardDto.getWearType())
                .wearValue(wearAwardDto.getWearValue())
                .build();
        UserWearsBag existUserWearsBag = findByQuery(userWearBagQuery);
        UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
        //不存在就插入
        if (Objects.isNull(existUserWearsBag)) {
            UserWearsBag userWearsBag = UserWearsBag.builder()
                    .wearType(wearAwardDto.getWearType())
                    .wearName(wearAwardDto.getWearName())
                    .wearValue(wearAwardDto.getWearValue())
                    .wearImageUrl(wearAwardDto.getWearImageUrl())
                    .userId(userId)
                    .source(WearConstant.SourceTypeEnum.source_type_30.getType())
                    .expiredTime(wearAwardDto.getExpiredTime() == null ? null : getAddHours(wearAwardDto.getExpiredTime()))
                    .build();
            userWearsBagDao.insert(userWearsBag);
            userWearsBagLog.setBagId(userWearsBag.getId());
        } else {
            //背包服装永久
            if (Objects.nonNull(existUserWearsBag.getExpiredTime())) {
                //存在，就判断过期时间是续期还是直接修改
                if (existUserWearsBag.getExpiredTime().isAfter(ZonedDateTime.now())) {
                    existUserWearsBag.setExpiredTime(wearAwardDto.getExpiredTime() == null ? null : DateUtil.addDays(existUserWearsBag.getExpiredTime(), wearAwardDto.getExpiredTime()));
                } else {
                    existUserWearsBag.setExpiredTime(wearAwardDto.getExpiredTime() == null ? null : DateUtil.addDays(ZonedDateTime.now(), wearAwardDto.getExpiredTime()));
                }
            }
            userWearsBagDao.updateById(existUserWearsBag);
            userWearsBagLog.setBagId(existUserWearsBag.getId());
        }
        userWearsBagLog.setActivityId(activityId);
        userWearsBagLog.setUserId(userId);
        userWearsBagLog.setExpiredTime(wearAwardDto.getExpiredTime());
        userWearsBagLogDao.insert(userWearsBagLog);
        return userWearsBagLog.getId();
    }

    @Override
    public void sendOnceWear(Long userId, WearAwardDto wearAwardDto) {
        UserWearsBag userWearsBag = UserWearsBag.builder()
                .wearType(wearAwardDto.getWearType())
                .wearValue(wearAwardDto.getWearValue())
                .wearImageUrl(wearAwardDto.getWearImageUrl())
                .userId(userId)
                .source(WearConstant.SourceTypeEnum.source_type_30.getType())
                .expiredTime(Objects.isNull(wearAwardDto.getExpiredTime()) ? null : getAddHours(wearAwardDto.getExpiredTime()))
                .build();
        userWearsBagDao.insert(userWearsBag);
        UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
        userWearsBagLog.setBagId(userWearsBag.getId());
        userWearsBagLog.setActivityId(-1L);
        userWearsBagLog.setUserId(userId);
        userWearsBagLog.setExpiredTime(wearAwardDto.getExpiredTime());
        userWearsBagLogDao.insert(userWearsBagLog);
    }


    @Override
    public void deleteByIds(List<Long> ids) {
        userWearsBagDao.deleteByIds(ids);
    }

    @Override
    public List<UserWearsBag> findListByQuery(UserWearBagQuery query) {
        return userWearsBagDao.selectList(buildQueryWrapper(query));
    }

    @Override
    public List<UserWearsBag> findListTimeNotNull() {
        return userWearsBagDao.selectList(Wrappers.<UserWearsBag>lambdaQuery()
                .eq(UserWearsBag::getIsDelete, 0)
                .isNotNull(UserWearsBag::getExpiredTime));
    }

    @Override
    public void updateBatchByIds(List<Long> expiredBagIds) {
        userWearsBagDao.update(Wrappers.<UserWearsBag>lambdaUpdate()
                .in(UserWearsBag::getId, expiredBagIds)
                .set(UserWearsBag::getIsDelete, 1));
    }

    @Override
    public List<UserWearsBag> findListByIds(List<Long> bagIds) {
        return userWearsBagDao.selectBatchIds(bagIds);
    }

    @Override
    public Long countByQuery(Long userId, int status, ZonedDateTime startTime, ZonedDateTime endTime) {
        return userWearsBagDao.selectCount(Wrappers.<UserWearsBag>lambdaQuery()
                .eq(UserWearsBag::getIsDelete, 0)
                .eq(UserWearsBag::getUserId, userId)
                .ne(UserWearsBag::getStatus, status)
                .ge(UserWearsBag::getGmtCreate, startTime)
                .le(UserWearsBag::getGmtCreate, endTime));
    }

    @Override
    public List<UserWearsBag> findListWearType(Long userId, int status, Integer wearType) {
        return userWearsBagDao.selectList(new QueryWrapper<UserWearsBag>().lambda()
                .eq(UserWearsBag::getUserId, userId)
                .eq(!Objects.equals(wearType, 0), UserWearsBag::getWearType, wearType)
                .eq(UserWearsBag::getIsDelete, 0)
                .eq(UserWearsBag::getStatus, status)
                .last("ORDER BY ISNULL(expired_time),expired_time ASC"));
    }

    @Override
    public UserWearsBag findByByIdWithoutLogicDelete(Long id) {
        return userWearsBagDao.findById(id);
    }

    private static Wrapper<UserWearsBag> buildQueryWrapper(UserWearBagQuery query) {
        return Wrappers.<UserWearsBag>lambdaQuery()
                .eq(UserWearsBag::getIsDelete, 0)
                .eq(Objects.nonNull(query.getUserId()), UserWearsBag::getUserId, query.getUserId())
                .eq(Objects.nonNull(query.getStatus()), UserWearsBag::getStatus, query.getStatus())
                .eq(Objects.nonNull(query.getWearValue()), UserWearsBag::getWearValue, query.getWearValue())
                .eq(Objects.nonNull(query.getWearType()), UserWearsBag::getWearType, query.getWearType())
                .le(Objects.nonNull(query.getExpiredTimeLe()), UserWearsBag::getExpiredTime, query.getExpiredTimeLe())
                .ge(Objects.nonNull(query.getExpiredTimeGe()), UserWearsBag::getExpiredTime, query.getExpiredTimeGe());
    }
}
