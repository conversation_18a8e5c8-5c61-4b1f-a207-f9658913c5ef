package com.linzi.pitpat.data.activityservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityHighlightDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


/**
 * 职业赛高亮数据 数据访问对象
 *
 * @since 2025年6月23日
 */
@Mapper
public interface ProActivityHighlightMapper extends BaseMapper<ProActivityHighlightDo> {
    @Select("update zns_pro_activity_highlight set page_view = page_view + 1 where main_activity_id = #{mainActivityId}")
    void addPageView(Long mainActivityId);

    @Select("update zns_pro_activity_highlight set enroll_count = enroll_count + 1 where main_activity_id = #{mainActivityId}")
    void addEnroll(Long mainActivityId);

    @Select("update zns_pro_activity_highlight set ceo_enroll = 1 where main_activity_id = #{mainActivityId}")
    void setCeoEnroll(Long mainActivityId);

    @Update("""
            
            UPDATE zns_pro_activity_highlight
            SET first_enroll_kol_user_id = #{userId}
            WHERE first_enroll_kol_user_id = -1
              AND main_activity_id IN (
            
                SELECT DISTINCT zns_run_activity_user.activity_id
                FROM zns_run_activity_user
                join zns_main_activity on zns_run_activity_user.activity_id = zns_main_activity.id
                WHERE zns_run_activity_user.user_id = #{userId}
                  AND zns_run_activity_user.is_delete = 0 
                  and zns_main_activity.is_delete = 0
                and zns_main_activity.activity_state in (0,1)
            
              );
            """)
    void fillNewKolUser(@Param("userId") Long userId);

    @Select("""
    
select zns_pro_activity_highlight.main_activity_id from zns_pro_activity_highlight 
         join zns_main_activity on zns_pro_activity_highlight.main_activity_id = zns_main_activity.id and zns_main_activity.activity_state in (0,1)
where zns_pro_activity_highlight.first_enroll_kol_user_id = #{userId} 
""")
    List<Long> userFirstKolActivityId(@Param("userId") Long userId);

    @Select("""

select zns_run_activity_user.user_id from zns_run_activity_user
join zns_user_kol on zns_user_kol.user_id = zns_run_activity_user.user_id and zns_user_kol.state='signed' and zns_user_kol.type='2'
where zns_run_activity_user.is_delete = 0 and zns_user_kol.is_delete = 0 and zns_run_activity_user.activity_id = #{activityId}
order by zns_run_activity_user.id limit 1;
""")
    Long findFirstEnrollKolUserId(@Param("activityId") Long activityId);

}
