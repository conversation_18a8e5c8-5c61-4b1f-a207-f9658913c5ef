package com.linzi.pitpat.data.activityservice.constant.enums;

import com.linzi.pitpat.lang.OrderItem;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 活动相关常量类
 */
public class ActivityConstants {
    public static final String FREE_CHALLENGE_PUSH_IMAGE = "https://pitpat-oss.s3.us-east-2.amazonaws.com/1752464102187.png";
    public static final List<OrderItem> FREE_CHALLENGE_RANK_COMPETE = List.of(OrderItem.asc("run_time_millisecond"),OrderItem.asc("complete_time"),OrderItem.asc("id"));
    public static final List<OrderItem> FREE_CHALLENGE_RANK_PROP = List.of(OrderItem.asc("run_time_mils"),OrderItem.asc("complete_time"),OrderItem.asc("id"));
    /**
     * 完成规则类型：
     * 1表示完成跑步里程，2表示完成跑步时长
     */
    @Getter
    @AllArgsConstructor
    public enum CompleteRuleTypeEnum {
        COMPLETERULETYPE_1(1, "完成跑步里程"),
        COMPLETERULETYPE_2(2, "完成跑步时长"),
        ;

        private Integer code;
        private String name;
    }

    /**
     * 主活动完成规则类型：
     * 目标类型：0：无，1：里程，2：时长
     */
    @Getter
    @AllArgsConstructor
    public enum TargetTypeEnum {
        TARGETTYPE_0(0, "无目标"),
        TARGETTYPE_1(1, "完成跑步里程"),
        TARGETTYPE_2(2, "完成跑步时长"),
        TARGETTYPE_3(3, "完成跑步场次"),
        ;

        public final Integer code;
        public final String name;

        public static TargetTypeEnum of(Integer code) {
            for (TargetTypeEnum targetTypeEnum : TargetTypeEnum.values()) {
                if (targetTypeEnum.code.equals(code)) {
                    return targetTypeEnum;
                }
            }
            return null;
        }
    }

    /**
     * 是否完成比赛:
     * 0表示未完成，1表示完成
     */
    @Getter
    @AllArgsConstructor
    public enum IsCompleteEnum {
        ISCOMPLETE_0(0, "未完成"),
        ISCOMPLETE_1(1, "完成"),
        ;

        private Integer code;
        private String name;
    }

    /**
     * 挑战双方完赛状态:
     * 1：都完成，2：都未完成，3：部分完赛
     */
    @Getter
    @AllArgsConstructor
    public enum FinishStatusEnum {
        FINISH_STATUS_1(1, "都完成"),
        FINISH_STATUS_2(2, "都未完成"),
        FINISH_STATUS_3(3, "部分完赛"),
        ;

        private Integer code;
        private String name;
    }

    /**
     * 活动类别 1、官方活动 2、主题活动 3、团队活动 4、最新活动 5、段位赛
     */
    @Getter
    @AllArgsConstructor
    public enum CategoryTypeEnum {
        CATEGORY_TYPE_1(1, "官方活动"),
        CATEGORY_TYPE_2(2, "主题活动"),
        CATEGORY_TYPE_3(3, "团队活动"),
        CATEGORY_TYPE_4(4, "最新活动"),
        CATEGORY_TYPE_5(5, "段位赛"),
        ;

        private final Integer code;
        private final String name;
    }

    /**
     * 奖金规则类型：1表示免费参加，2表示保证金参加，3：费用参与(完赛不退回)，4:纯积分， 5: 积分 + 费用
     */
    @Getter
    @AllArgsConstructor
    public enum BonusRuleTypeEnum {
        FREE(1, "免费"),
        BOND(2, "保证金"),
        FEE(3, "费用"),
        SCORE(4, "积分"),
        SCORE_FEE(5, "积分 + 费用"),
        ;

        private final Integer code;
        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum NewPersonActivityTypeEnum {
        PK(1, "新人pk"),
        PROP(2, "新人道具赛");

        private final Integer type;
        private final String name;
    }

    @Getter
    @AllArgsConstructor
    public enum ActivityEndAwardSendStatusEnum {
        REVIEW_PENDING(0, "未发放，风控审核中"),
        GIVE_OUT(1, "已发放"),
        FREE_ACTIVITY_NO_RANKING_END(2, "未发放,自由挑战跑排名未结束"),
        ;

        private final Integer status;
        private final String name;
    }
}
