package com.linzi.pitpat.data.entity.dto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023/6/12 20:19
 */
@Data
@NoArgsConstructor
public class CouponOverdueRemindListDto {
    // 用户卷id
    private Long id;
    //用户id
    private Long userId;
    //优惠券id
    private Long couponId;
    //标题
    private String title;
    //过期提醒配置 0 默认 1 自定义
    private Integer expirationRemindType;
    //过期提醒天数配置
    private Integer expirationRemindDay;
    //提醒方式 0 push 1 im 2 im + push
    private Integer expirationRemindMethod;
    //卷图片url 缩略
    private String minPicUrl;
    //券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券】
    private Integer couponType;
    //有效期类型【1:days固定天数，2:range固定时间范围】
    private Integer expiryType;
    //有效期天数
    private Integer validDays;
    //有效期结束时间
    private ZonedDateTime gmtEnd;
    //券金额
    private BigDecimal amount;

    //币类型
    private String currencyCode;
}
