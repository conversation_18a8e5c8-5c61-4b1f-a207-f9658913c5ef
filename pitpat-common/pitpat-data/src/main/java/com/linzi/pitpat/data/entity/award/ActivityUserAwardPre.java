package com.linzi.pitpat.data.entity.award;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_activity_user_award_pre")
public class ActivityUserAwardPre implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //活动id，关联zns_main_activity
    private Long activityId;
    //用户id
    private Long userId;
    //用户编码
    private String userCode;
    //作弊时常作弊距离
    private Integer cheatingDistance;
    //作弊时常
    private Integer cheatingTime;
    //排名/挑战排名
    @TableField("`rank`")
    private Integer rank;
    //明细状态 0:待审核,1:审核通过 -1 审核作废
    private Integer status;
}
