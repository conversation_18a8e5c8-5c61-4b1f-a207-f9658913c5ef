package com.linzi.pitpat.data.awardservice.service.impl;
/**
 * <p>
 * 用户优惠券表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-26
 */

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.awardservice.mapper.UserCouponDao;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.resp.UserCouponResp;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.model.vo.UserMilestoneCoupon;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.courseservice.model.request.CourseListRequest;
import com.linzi.pitpat.data.courseservice.model.request.CourseUpdateRequest;
import com.linzi.pitpat.data.entity.dto.message.CouponOverdueRemindListDto;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.request.CouponPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RefreshScope
@RequiredArgsConstructor
public class UserCouponServiceImpl implements UserCouponService {


    @Autowired
    private UserCouponDao userCouponDao;


    @Override
    public UserCoupon selectUserCouponById(Long id) {
        return userCouponDao.selectUserCouponById(id);
    }


    @Override
    public UserCoupon getUserCouponByActivityAndUserId(Long activityId, Long userId, List<Integer> couponTypes) {
        return userCouponDao.selectUserCouponOne(activityId, userId, couponTypes);

    }

    @Override
    public List<CouponPageVo> selectUserCouponList(Long userId, int status, List<Integer> couponTypes) {
        return userCouponDao.selectUserCouponList(userId, status, couponTypes);
    }


    @Override
    public boolean updateIsNew(CourseUpdateRequest po, Long userId) {
        if (!CollectionUtils.isEmpty(po.getCouponIds())) {
            List<UserCoupon> updateBatch = userCouponDao.selectList(new QueryWrapper<UserCoupon>().in("id", po.getCouponIds()));
            updateBatch.forEach(i -> {
                if (i.getIsNew().equals(YesNoStatus.YES.getCode()) && userId.equals(i.getUserId())) {
                    i.setIsNew(YesNoStatus.NO.getCode());
                }
            });
            if (!CollectionUtils.isEmpty(updateBatch)) {
                return !userCouponDao.updateById(updateBatch).isEmpty();
            }
        }
        return false;
    }


    @Override
    public Integer countUserCanUserCoupon(Long userId, Integer appVersion, String mallCountryCode) {
        ZonedDateTime startDate = appVersion > Constant.appVersion_4044 ? ZonedDateTime.now().plusMonths(-6) : null;
        CourseListRequest req = new CourseListRequest();
        req.setUserId(userId);
        req.setCouponListStatus(1);
        req.setGmtCreateGt(startDate);
        if (appVersion < VersionConstant.V4_6_4) {
            //老版本只展示美国券
            req.setUserCouponCountryCode(I18nConstant.CountryCodeEnum.US.getCode());
        }
        req.setMallCountryCode(mallCountryCode);

        return userCouponDao.getUserCouponCount(req).intValue();
    }

    @Override
    public Integer countUserCouponByCondition(Long userId, Long activityId, Integer activityType, Long taskConfigId, String batchNo) {
        return userCouponDao.selectUserCouponCountByCondition(userId, 5, 0, activityId, activityType, taskConfigId, batchNo, "", null);
    }

    @Override
    public List<UserCouponDiKou> selectCanUseConponList(ZnsRunActivityEntity znsRunActivityEntity, Long userId) {
        return userCouponDao.selectUserCouponCountByConditionList(userId, 5, 0, znsRunActivityEntity.getId(), znsRunActivityEntity.getActivityType(), znsRunActivityEntity.getTaskConfigId(), znsRunActivityEntity.getBatchNo(), null, null);
    }

    @Override
    public UserCoupon getUserCouponByActivityAndUserIdAndCouponType(Long activityId, Long userId, int i, String currencyCode) {
        return userCouponDao.getUserCouponByActivityAndUserIdAndCouponType(activityId, 2, userId, 5, currencyCode);
    }

    @Override
    public List<CouponOverdueRemindListDto> overdueRemindList() {
        return userCouponDao.overdueRemindList();
    }

    @Override
    public List<CouponPageVo> getUserNewCouponByUserId(Long userId, String languageCode) {
        return userCouponDao.getUserNewCouponByUserId(userId, languageCode);
    }

    @Override
    public Integer selectHistoryUseCouponCount(Long userId) {
        return userCouponDao.selectHistoryUseCouponCount(userId);
    }


    @Override
    public void cancelActivityRefund(Long activityId, Long userId, Integer userState) {
        UserCoupon coupon = userCouponDao.selectOne(new QueryWrapper<UserCoupon>().eq("use_activity_id", activityId).in("status", Arrays.asList(1, 2)).eq("is_delete", 0).eq("user_id", userId));
        if (Objects.nonNull(coupon)) {
            log.info("return coupon no user_id:{},coupon_id:{}", userId, coupon.getId());
            if (userState == 1) {
                coupon.setStatus(0);
            } else {
                coupon.setStatus(2);
            }
            userCouponDao.updateById(coupon);
        }
    }


    @Override
    public List<UserCoupon> selectUserCouponByActivityIdUserId(Long activityId, Long userId) {
        return userCouponDao.selectList(Wrappers.<UserCoupon>lambdaQuery().eq(UserCoupon::getActivityId, activityId).eq(UserCoupon::getUserId, userId).eq(UserCoupon::getIsDelete, 0));
    }

    @Override
    public UserCoupon getUserCouponByActivityAndUserIdAndStatus(Long activityId, Long userId, Integer status) {
        return userCouponDao.selectOne(Wrappers.<UserCoupon>lambdaQuery().eq(UserCoupon::getUseActivityId, activityId).eq(UserCoupon::getUserId, userId).eq(UserCoupon::getIsDelete, 0).eq(Objects.nonNull(status), UserCoupon::getStatus, status).last("limit 1"));
    }

    @Override
    public Integer getCountUserCouponByActivityIdAndUserId(Long activityId, Long userId, ZonedDateTime createTime, ZonedDateTime modifieTime) {
        return userCouponDao.getCountUserCouponByActivityIdAndUserId(activityId, userId, createTime);
    }


    @Override
    public List<UserCouponDiKou> selectCanUseConponList(Long activityId, Long userId, Integer activityType, String currencyCode, Long couponId) {
        return userCouponDao.selectUserCouponCountByConditionList(userId, 5, 0, activityId, activityType, 0L,
                "", currencyCode, couponId);

    }

    @Override
    public UserCoupon findBySource(Long couponId, Long userId, int source, Long activityId) {
        return userCouponDao.selectOne(Wrappers.<UserCoupon>lambdaQuery()
                .eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getCouponId, couponId)
                .eq(UserCoupon::getSourceType, source)
                .eq(UserCoupon::getSourceActivityId, activityId)
                .eq(UserCoupon::getIsDelete, 0).last("limit 1"));
    }


    @Override
    public UserCoupon getUserCouponByActIdUserIdCouponType(Long activityId, Long userId, List<Integer> couponTypes) {
        return userCouponDao.getUserCouponByActIdUserIdCouponType(activityId, userId, couponTypes);
    }

    @Override
    public Page selectUserCouponPage(Page page, Long userId) {
        return userCouponDao.selectPage(page, Wrappers.lambdaQuery(UserCoupon.class).eq(UserCoupon::getUserId, userId));
    }

    @Override
    public Long getAllCouponCount(Long userId) {
        return userCouponDao.selectCount(Wrappers.lambdaQuery(UserCoupon.class).eq(UserCoupon::getUserId, userId));
    }

    @Override
    public List<UserCoupon> selectUserCouponByActivityIdAndSource(Long activityId, int sourceType) {
        return userCouponDao.selectUserCouponByActivityIdAndSource(activityId, sourceType);
    }

    @Override
    public List<UserCouponDiKou> selectUserCouponByCondition(IPage page, Long userId, Integer couponType, Integer status, Long activityId, Integer activityType, Long taskConfigId, String batchNo, Long userCouponId,
                                                             String currencyCode, Long onlyCouponId, String newActivityType, Long newActivityTaskId, Integer couponMainType) {
        return userCouponDao.selectUserCouponByCondition(page, userId, couponType, status, activityId, activityType, taskConfigId, batchNo, userCouponId, currencyCode, onlyCouponId, newActivityType, newActivityTaskId, couponMainType);
    }

    @Override
    public List<UserCouponStatisticsDto> selectCouponStatisticsDto(List<Long> couponId) {
        return userCouponDao.selectCouponStatisticsDto(couponId);
    }

    @Override
    public List<UserMilestoneCoupon> selectUserMilestoneCoupon(Long userId) {
        return userCouponDao.selectUserMilestoneCoupon(userId);
    }

    @Override
    public UserCoupon selectUserCouponByCouponId(Long userId, Long couponId) {
        return userCouponDao.selectUserCouponByCouponId(userId, couponId);
    }

    @Override
    public Page<CouponPageVo> getUserCouponPage(Page<CouponPageVo> page, CourseListRequest req) {
        return userCouponDao.getUserCouponPage(page, req);
    }

    @Override
    public Integer selectUserCouponCountByCondition(Long userId, Integer couponType, Integer status, Long activityId, Integer activityType, Long taskConfigId, String batchNo, String currencyCode, Long onlyCouponId) {
        return userCouponDao.selectUserCouponCountByCondition(userId, couponType, status, activityId, activityType, taskConfigId, batchNo, currencyCode, onlyCouponId);
    }

    @Override
    public List<UserCouponResp> findList(IPage page, CouponPageQuery query) {
        return userCouponDao.findList(page, query);
    }

    @Override
    public Page<UserCouponResp> findPage(CouponPageQuery query) {
        return userCouponDao.findPage(PageHelper.ofPage(query), query);
    }

    @Override
    public void insert(UserCoupon userCoupon) {
        userCouponDao.insert(userCoupon);
    }

    @Override
    public List<UserCoupon> findListByIds(List<Long> userCouponDetailIdList) {
        return userCouponDao.selectBatchIds(userCouponDetailIdList);
    }

    @Override
    public void update(UserCoupon userCoupon) {
        userCouponDao.updateById(userCoupon);
    }

    @Override
    public Long findCount(Long userId, Long couponId) {
        return userCouponDao.selectCount(Wrappers.<UserCoupon>lambdaQuery()
                .eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getCouponId, couponId)
                .eq(UserCoupon::getIsDelete, 0));
    }

    @Override
    public void updateBatchByIds(List<UserCoupon> list) {
        userCouponDao.updateById(list);
    }

    @Override
    public List<UserCoupon> findListByTimeAndStatus(int status, ZonedDateTime date, ZonedDateTime date1) {
        return userCouponDao.selectList(Wrappers.<UserCoupon>lambdaQuery()
                .eq(UserCoupon::getStatus, status)
                .eq(UserCoupon::getIsDelete, 0)
                .ge(UserCoupon::getGmtEnd, date)
                .le(UserCoupon::getGmtEnd, date1));
    }

    @Override
    public List<UserCoupon> findListBySourceType(Long userId, Long activityId, List<Integer> sourceList, int status) {
        return userCouponDao.selectList(Wrappers.<UserCoupon>lambdaQuery()
                .eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getActivityId, activityId)
                .in(UserCoupon::getSourceType, sourceList)
                .eq(UserCoupon::getStatus, status)
                .eq(UserCoupon::getIsDelete, 0));
    }

    @Override
    public List<UserCoupon> findListByActivityIdAndStatus(Long activityId, int status) {
        return userCouponDao.selectList((Wrappers.<UserCoupon>lambdaQuery()
                .eq(UserCoupon::getActivityId, activityId)
                .ne(UserCoupon::getStatus, status)
                .eq(UserCoupon::getIsDelete, 0)));
    }

    @Override
    public UserCoupon findByQuery(UserCouponQuery query) {
        return userCouponDao.selectOne(buildQueryWrapper(query), false);
    }

    @Override
    public List<UserCoupon> findListByQuery(UserCouponQuery query) {
        return userCouponDao.selectList(buildQueryWrapper(query));
    }

    /**
     * 查询用户指定类型可用的通用优惠券
     */
    @Override
    public List<Long> findUserWholeCouponId(Long userId, Integer couponMainType, Boolean isNowUse) {
        return userCouponDao.findUserWholeCouponId(userId, couponMainType, isNowUse);
    }

    /**
     * 查询用户指定类型可用的限定优惠券
     */
    @Override
    public List<Long> findUserSpecialCouponId(Long userId, List<Long> spuIds, Integer couponMainType, Boolean isNowUse) {
        return userCouponDao.findUserSpecialCouponId(userId, spuIds, couponMainType, isNowUse);
    }

    @Override
    public UserCoupon findById(Long id) {
        return userCouponDao.selectById(id);
    }

    private static Wrapper<UserCoupon> buildQueryWrapper(UserCouponQuery query) {
        return Wrappers.<UserCoupon>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), UserCoupon::getId, query.getId())
                .eq(UserCoupon::getIsDelete, 0)
                .eq(Objects.nonNull(query.getActivityId()), UserCoupon::getUseActivityId, query.getActivityId())
                .eq(Objects.nonNull(query.getSourceActivityId()), UserCoupon::getSourceActivityId, query.getSourceActivityId())
                .eq(Objects.nonNull(query.getUserId()), UserCoupon::getUserId, query.getUserId())
                .eq(Objects.nonNull(query.getCouponId()), UserCoupon::getCouponId, query.getCouponId())
                .eq(Objects.nonNull(query.getCouponStatus()), UserCoupon::getStatus, query.getCouponStatus())
                .eq(Objects.nonNull(query.getCouponMainType()), UserCoupon::getCouponMainType, query.getCouponMainType())
                .in(Objects.nonNull(query.getStatus()), UserCoupon::getStatus, query.getStatus())
                .in(Objects.nonNull(query.getSourceList()), UserCoupon::getSourceType, query.getSourceList())
                .in(Objects.nonNull(query.getCouponIds()), UserCoupon::getCouponId, query.getCouponIds())
                .ge(Objects.nonNull(query.getTime()), UserCoupon::getGmtEnd, query.getTime())
                .le(Objects.nonNull(query.getTime()), UserCoupon::getGmtStart, query.getTime())
                .le(Objects.nonNull(query.getGmtEndLe()), UserCoupon::getGmtEnd, query.getGmtEndLe())
                .last(!org.springframework.util.CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }
}
