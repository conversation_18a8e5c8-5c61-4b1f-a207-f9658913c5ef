package com.linzi.pitpat.data.activityservice.biz;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.award.match.ActivityAwardMatchService;
import com.linzi.pitpat.data.activityservice.constant.ActivityStringConstant;
import com.linzi.pitpat.data.activityservice.constant.ActivityStringConstant.RedisLock;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityRecommendPositionEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamSettingEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.VideoViewDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeChallengeActivityResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.UserHeadNameDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityAwardReviewAdminDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityAwardReviewDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CommunityRecommendActivityDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.HomeRecommendActivityDto;
import com.linzi.pitpat.data.activityservice.dto.console.ActivityRecommendCommunityDesc;
import com.linzi.pitpat.data.activityservice.dto.console.ActivityRecommendSetting;
import com.linzi.pitpat.data.activityservice.dto.console.ActivityStageDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityRateLimitDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.BrandRightAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.BrandRightDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.EnableActStatusRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.Equipment;
import com.linzi.pitpat.data.activityservice.dto.console.request.ProActivitySettings;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityFeeAndAwardCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityReportCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityRuleCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.TeamConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsCategoryVideoDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPlaylistRel;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRateLimit;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRecommendDisseminateDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRecommendTimelineDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRotSetting;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserGroup;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.Gameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.PacerConfig;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityMedal;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRecommendDisseminateQuery;
import com.linzi.pitpat.data.activityservice.model.query.MainRunActivityRelationQuery;
import com.linzi.pitpat.data.activityservice.model.query.NoEndRunDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.model.request.CompetitiveSeasonSystemDefaultConfig;
import com.linzi.pitpat.data.activityservice.model.vo.EquipmentActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityTypeRuleVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MaxAwardVo;
import com.linzi.pitpat.data.activityservice.service.ActivityAreaService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityEnterThresholdService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityHighlightsService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityImpracticalAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.ActivityPropConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.ActivityRecommendDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityRecommendTimelineService;
import com.linzi.pitpat.data.activityservice.service.ActivityRotSettingService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserGroupService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.PacerConfigService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.ProActivityHighlightService;
import com.linzi.pitpat.data.activityservice.service.ProActivityService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SeriesGameplayService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.award.ActivityUserAwardReviewLog;
import com.linzi.pitpat.data.entity.dto.AutoEnrollDto;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityFeeTypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.service.award.ActivityUserAwardReviewLogService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.transaction.MultiplyThreadTransactionManager;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;

import static com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class MainActivityBizService {

    private final MainActivityService mainActivityService;
    private final ZnsRunActivityService runActivityService;

    private final SeriesActivityRelService seriesActivityRelService;
    private final SeriesGameplayService seriesGameplayService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ActivityUserAwardReviewLogService activityUserAwardReviewLogService;
    private final ISysConfigService sysConfigService;
    private final ActivityParamsService activityParamsService;
    private final ActivityTeamService activityTeamService;
    private final ActivityFeeService activityFeeService;
    private final ZnsRunActivityUserService activityUserService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ZnsUserAccountService userAccountService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserCouponService userCouponService;
    private final RedisTemplate redisTemplate;
    private final ActivityDisseminateService activityDisseminateService;
    private final CompetitiveSeasonRuleValid competitiveSeasonRuleValid;
    private final ActivityIDGenerateService idGenerateService;
    private final ActivityAreaService activityAreaService;
    private final RotationAreaBizService rotationAreaBizService;
    private final ActivityRateLimitService activityRateLimitService;
    private final PacerConfigService pacerConfigService;
    private final ActivityPropConfigService activityPropConfigService;
    private final ActivityRotSettingService activityRotSettingService;
    private final SubActivityService subActivityService;
    private final ActivityBrandRightsInterestsService brandRightsInterestsService;
    private final RunActivityMedalService activityMedalService;
    private final ActivityPlaylistRelService activityPlaylistRelService;
    private final GameplayService gameplayService;
    private final EntryGameplayService entryGameplayService;
    private final ActivityUserGroupService activityUserGroupService;
    private final ActivityEquipmentConfigService activityEquipmentConfigService;
    private final PolymerizationActivityPoleService polymerizationActivityPoleService;
    private final ActivityEnterThresholdService activityEnterThresholdService;
    private final ZnsUserService userService;
    private final AwardActivityBizService awardActivityBizService;
    private final MultiplyThreadTransactionManager multiplyThreadTransactionManager;

    private final ActivityImpracticalAwardConfigService impracticalAwardConfigService;
    private final RoomIdBizService roomIdBizService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final UserCouponBizService userCouponBizService;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final RabbitTemplate rabbitTemplate;
    private final RedissonClient redissonClient;
    private final ActivityStageService activityStageService;
    private final CompetitiveSeasonConfigBizService competitiveSeasonConfigBizService;
    private final CompetitiveSeasonService competitiveSeasonService;
    private final UserGroupRelService userGroupRelService;

    private final CompetitiveActivityStateChangeRunner competitiveActivityStateChangeRunner;
    private final ActivityRecommendDisseminateService activityRecommendDisseminateService;
    private final ActivityRecommendTimelineService activityRecommendTimelineService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final ProActivityService proActivityService;
    private final ProActivityHighlightService proActivityHighlightService;
    private final ActivityAwardMatchService activityAwardMatchService;
    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${activity.recommendCheckUserGroup:1}")
    private String recommendCheckUserGroup;
    /**
     * 显示在更多中的活动
     */
    @Value("${activity.competitive.showInfoMore.maxCount:1000}")
    public int showInMoreActivityOnlineMaxCount;
    @Value("${zns.config.rabbitQueue.delayed_content_publish_exchange_name}")
    private String delay_exchange_name_rot;

    public ActivityTypeDto getActivityNew(Long activityId, String zoneId) {
        log.info("ConsloeActivityManager#getActivityNew------入参activityId：{},zoneId：{}", activityId, zoneId);
        if (Objects.isNull(activityId)) {
            return null;
        }
        MainActivity activity = mainActivityService.findById(activityId);
        ActivityTypeDto activityTypeDto = new ActivityTypeDto();
        ZonedDateTime now = ZonedDateTime.now();
        if (Objects.nonNull(activity) && !MainActivityTypeEnum.OLD.getType().equals(activity.getMainType())) {
            activityTypeDto.setMainActivity(activity);
            activityTypeDto.setId(activityId).setActivityType(activity.getOldType()).setMainType(activity.getMainType()).setTargetType(activity.getTargetType());
            activityTypeDto.setActivityStartTime(DateTimeUtil.parse(activity.getActivityStartTime()));
            activityTypeDto.setActivityEndTime(DateTimeUtil.parse(activity.getActivityEndTime()));
            activityTypeDto.setActivityState(activity.getActivityState());
            activityTypeDto.setWaitTime(activity.getWaitTime());
            activityTypeDto.setTimeStyle(activity.getTimeStyle());
            if (Objects.isNull(activityTypeDto.getActivityType())) {
                activityTypeDto.setActivityType(13);
            }
            if (activity.getTimeStyle() == 1 && StringUtils.hasText(zoneId)) {
                TimeZone timeZone = TimeZone.getTimeZone(zoneId);
                Long startTime = DateUtil.getStampByZone(activity.getActivityStartTime(), zoneId);
                Long endTime = DateUtil.getStampByZone(activity.getActivityEndTime(), zoneId);

                //修改活动状态
                if (activity.getActivityState() == 1) {
                    long time = now.toInstant().toEpochMilli();
                    if (time > endTime) {
                        activityTypeDto.setActivityState(2);
                    } else if (time < startTime) {
                        activityTypeDto.setActivityState(0);
                    }
                    log.info("ConsloeActivityManager#getActivityNew------修改活动状态,time：{},startTime:{},endTime：{},activityState:{}", time, startTime, endTime, activityTypeDto.getActivityState());
                }
                activityTypeDto.setActivityStartTime(new Date(startTime));
                activityTypeDto.setActivityEndTime(new Date(endTime));
            }
            if (activity.isBikeActivity()) {
                Optional<ActivityParams> oneByMainActivityAndParamType = activityParamsService.findOneByMainActivityAndParamType(activityId, ActivitySettingConfigEnum.DESK_BIKE_EQUIPMENT_VERSION);
                if (oneByMainActivityAndParamType.isPresent()) {
                    activityTypeDto.setDeskBikeEquipmentVersion(Integer.valueOf(oneByMainActivityAndParamType.get().getParamValue()));
                }
            }
            return activityTypeDto;
        } else {
            //没查到表示老数据
            ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
            if (Objects.isNull(runActivity)) {
                return null;
            }
            BeanUtils.copyProperties(runActivity, activityTypeDto);
            activityTypeDto.setMainType(MainActivityTypeEnum.OLD.getType());
            activityTypeDto.setTargetType(runActivity.getCompleteRuleType());
            activityTypeDto.setRunActivity(runActivity);
            return activityTypeDto;
        }

    }


    public ActivityAwardReviewAdminDto reviewStatus(Long activityId) {
        ActivityAwardReviewAdminDto adminDto = new ActivityAwardReviewAdminDto();
        if (Objects.nonNull(activityId)) {
            ActivityAwardReviewDto reviewDto = reviewStatusGet(activityId);
            adminDto.setCountDownTime(Objects.nonNull(reviewDto.getCountDownTime()) && reviewDto.getCountDownTime() > 0 ? reviewDto.getCountDownTime() - 3600 : 0);
            adminDto.setStatus(reviewDto.getStatus());
            if (reviewDto.getStatus().equals(AwardSendStatusEnum.VIEW_PASS.getCode())) {
                ActivityUserAwardReviewLog activityUserAwardReviewLog = activityUserAwardReviewLogService.findByActivityId(activityId);
                adminDto.setReviewUserName(activityUserAwardReviewLog.getReviewName());
                adminDto.setReviewTime(activityUserAwardReviewLog.getGmtCreate());
            }
        }

        // 奖励发放方式审核
        List<ActivityParams> activityAndParamTypes = activityParamsService.findListByMainActivityAndParamTypes(activityId,
                ActivitySettingConfigEnum.AWARD_SEND_TYPE, ActivitySettingConfigEnum.COMPLETE_AWARD_SEND_TYPE, ActivitySettingConfigEnum.SURPASS_AWARD_SEND_TYPE,
                ActivitySettingConfigEnum.SURPASS_USER_CODE);
        if (CollectionUtils.isEmpty(activityAndParamTypes)) {
            return adminDto;
        }

        activityAndParamTypes.forEach(k -> {
            if (k.getParamKey().equals(ActivitySettingConfigEnum.AWARD_SEND_TYPE.getCode())) {
                adminDto.setAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.COMPLETE_AWARD_SEND_TYPE.getCode())) {
                adminDto.setCompleteAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.SURPASS_AWARD_SEND_TYPE.getCode())) {
                adminDto.setSurpassAwardSendType(Integer.valueOf(k.getParamValue()));
            } else if (k.getParamKey().equals(ActivitySettingConfigEnum.SURPASS_USER_CODE.getCode())) {
                adminDto.setSurpassUserCode(k.getParamValue());
                ZnsUserEntity user = userService.findByUserCode(k.getParamValue());
                if (Objects.nonNull(user)) {
                    MainActivity mainActivity = mainActivityService.findById(activityId);
                    String rankingBy = findRankingBy(mainActivity);
                    ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUserWithNoState(user.getId(), activityId);
                    if (Objects.nonNull(activityUser)) {
                        String raceResult = activityUserService.getRaceResult(rankingBy, activityUser);
                        adminDto.setRaceResult(raceResult);
                        adminDto.setRaceResultType(rankingBy);
                    }
                }
            }
        });

        List<ActivityStage> stageList = activityStageService.findByActId(activityId);
        adminDto.setIsStageActivity(CollectionUtils.isEmpty(stageList) ? 0 : 1);
        activityParamsService.findOneByMainActivityAndParamType(activityId, ActivitySettingConfigEnum.VIDEO_VIEW_CONFIG)
                .ifPresent(k -> {
                    VideoViewDto videoViewDto = JsonUtil.readValue(k.getParamValue(), VideoViewDto.class);
                    adminDto.setEnableVideo(videoViewDto.getEnable());
                });


        return adminDto;
    }

    private String findRankingBy(MainActivity mainActivity) {
        String rankingBy;
        List<Long> subActivityList;
        if (mainActivity.getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())) {
            SeriesGameplay seriesGameplay = seriesGameplayService.findOneByGameplayId(mainActivity.getPlayId());
            rankingBy = seriesGameplay.getRankingBy();
            subActivityList = seriesActivityRelService.findSubActivityId(mainActivity.getId());
        } else {
            subActivityList = new ArrayList<>();
            EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
            rankingBy = entryGameplay.getRankingBy();
        }
        return rankingBy;
    }

    /**
     * 奖励审核状态
     *
     * @param mainActivityId
     * @return
     */
    public ActivityAwardReviewDto reviewStatusGet(Long mainActivityId) {
        ActivityAwardReviewDto activityAwardReviewDto = new ActivityAwardReviewDto();
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        activityAwardReviewDto.setStatus(mainActivity.getAwardSendStatus());
        if (!activityParamsService.checkAwardSendType(mainActivity.getId())) {
            // 活动非结束状态 或者未开启审核状态 审核状态都为结束
            activityAwardReviewDto.setStatus(AwardSendStatusEnum.VIEW_PASS.getCode());
            activityAwardReviewDto.setEnableExamine(0);
        } else {
            activityAwardReviewDto.setEnableExamine(1);
            if (!MainActivityStateEnum.ENDED.getCode().equals(mainActivity.getActivityState())) {
                // 活动非结束状态 或者未开启审核状态 审核状态都为结束
                activityAwardReviewDto.setStatus(AwardSendStatusEnum.VIEW_PASS.getCode());
            }
        }
        if (!mainActivity.getAwardSendStatus().equals(AwardSendStatusEnum.VIEW_PASS.getCode())) {
            String reviewTime = sysConfigService.selectConfigByKey("activity.award.review.time");
            if (StringUtils.hasText(reviewTime)) {
                activityAwardReviewDto.setCountDownTime(DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.addHours(DateTimeUtil.parse(mainActivity.getActivityEndTime()), Integer.parseInt(reviewTime) + 1)));
            } else {
                activityAwardReviewDto.setCountDownTime(DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.addHours(DateTimeUtil.parse(mainActivity.getActivityEndTime()), 72)));
            }
        }
        return activityAwardReviewDto;
    }

    /**
     * 段位赛-所有人都结束了则关闭活动
     *
     * @param activityId
     * @return
     */

    public Integer closedRankActivity(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (mainActivity == null) {
            //比赛不存在
            return null;
        }
        Integer activityState = mainActivity.getActivityState();
        if (!MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
            //不是段位赛
            return activityState;
        }
        if (!MainActivityStateEnum.STARTED.getCode().equals(activityState)) {
            //活动不是运动中状态
            return activityState;
        }

        //查询未完赛的记录
        NoEndRunDetailsQuery query = new NoEndRunDetailsQuery();
        query.setActivityId(activityId);
        List<ZnsUserRunDataDetailsEntity> detailsEntities = userRunDataDetailsService.findNoEndDetails(query);
        if (!CollectionUtils.isEmpty(detailsEntities)) {
            //还有用户未完赛
            return activityState;
        }
        //更新活动为已结束
        MainActivity updateMainActivity = new MainActivity();
        updateMainActivity.setId(activityId);
        updateMainActivity.setActivityState(MainActivityStateEnum.ENDED.getCode());
        mainActivityService.update(updateMainActivity);
        return MainActivityStateEnum.ENDED.getCode();
    }

    /**
     * 修改活动状态
     *
     * @param request
     */
    @Transactional
    public void changeStatus(EnableActStatusRequest request) {
        Integer status = request.getStatus();
        for (Long activityId : request.getActivityIds()) {
            MainActivity mainActivity = mainActivityService.findById(activityId);
            if (status == 0) {
                RLock lock = redissonClient.getLock(RedisLock.LOCK_ACTIVITY_LISTING_COUNT);
                LockHolder.tryLock(lock, 100, () -> {
                    enableActivity(mainActivity);
                });
            } else {
                //活动下架
                removalActivity(mainActivity, request.getOfflineReason());
            }
        }
    }

    private void enableActivity(MainActivity mainActivity) {
        Long appEndStamp = DateUtil.getStampByZone(mainActivity.getApplicationEndTime(), null);
        if (mainActivity.getTimeStyle() == 1) {
            appEndStamp = DateUtil.getStampByZone(mainActivity.getApplicationEndTime(), "UTC+14:00");
        }

        if (System.currentTimeMillis() >= appEndStamp) {
            throw new BaseException("当前时间>=活动报名时间，不可上架");
        }


        mainActivity.setStatus(0);
        mainActivity.setOnlineTime(ZonedDateTime.now());
        //上架后，通知竞技赛模块

        //上架后开始报名，只生效一次  上架只能上架一次，下架后不能再上架
        if (mainActivity.getActivityState() != -1) {
            return;
        }
        //报名时间
        if (Objects.isNull(mainActivity.getApplicationStartTime())) {
            if (mainActivity.getTimeStyle() == 0) {
                mainActivity.setApplicationStartTime(DateUtil.getNowStrBYZoneId("UTC"));
            } else {
                mainActivity.setApplicationStartTime(DateUtil.getNowStrBYZoneId("GMT+08:00"));
            }

        }
        //计算活动状态
        Integer activityState = 0;
        Long startTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityStartTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : "UTC+14:00");
        Long endTimeStamp = DateUtil.getStampByZone(mainActivity.getActivityEndTime(),
                mainActivity.getTimeStyle() == 0 ? "UTC" : "UTC-12:00");
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis < startTimeStamp) {
            activityState = 0;
        } else if (currentTimeMillis > startTimeStamp && currentTimeMillis < endTimeStamp) {
            activityState = 1;
        } else if (currentTimeMillis >= endTimeStamp) {
            activityState = 2;
        }
        mainActivity.setActivityState(activityState);
        if (Integer.valueOf(1).equals(mainActivity.getIsCompetitive())) {
            competitiveActivityStateChangeRunner.enableActivity(mainActivity);
        }
        if (mainActivity.isShowInMore()) {
            //显示了在更多中的活动数量
            if (mainActivityService.checkCountShowInMoreActivity(showInMoreActivityOnlineMaxCount, mainActivity.getEquipmentMainType())) {
                DeviceConstant.EquipmentMainTypeEnum enumByType = DeviceConstant.EquipmentMainTypeEnum.findEnumByType(mainActivity.getEquipmentMainType());
                if (Objects.nonNull(enumByType)) {
                    throw new BaseException(String.format("当前已有%d场" + enumByType.getName() + "赛事上线中", showInMoreActivityOnlineMaxCount));
                } else {
                    throw new BaseException(String.format("当前已有%d场" + DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getName() + "赛事上线中", showInMoreActivityOnlineMaxCount));
                }
            }
        }
        //处理系列赛各子活动
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            List<MainActivity> allSegmentMainActivityList = seriesActivityRelService.getAllMainActivity(mainActivity.getId());
            for (MainActivity segmentActivity : allSegmentMainActivityList) {
                segmentActivity.setActivityState(mainActivity.getActivityState());
                segmentActivity.setOnlineTime(mainActivity.getOnlineTime());
                segmentActivity.setApplicationStartTime(mainActivity.getApplicationStartTime());
                segmentActivity.setStatus(mainActivity.getStatus());
            }
            mainActivityService.updateBatch(allSegmentMainActivityList);

        }
        mainActivityService.update(mainActivity);
        //如果不是竞技赛，删除破纪录奖励
        if (!competitiveSeasonBizService.isCompetitiveActivity(mainActivity.getId())) {
            awardActivityBizService.removeAwardByType(mainActivity.getId(), AwardSentTypeEnum.RECORD_BREAKING_AWARD);
        }
        //预设队长自动报名
        autoEnroll(mainActivity);
        //将算好的最大奖励，直接写入activityParam
        MaxAwardVo maxAward = awardActivityBizService.findMaxAwardWithDbCache(mainActivity.getId(),
                I18nConstant.CurrencyCodeEnum.USD.getCode(), mainActivity.getMainType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(), ActivitySettingConfigEnum.ACTIVITY_MAX_AWARD, JsonUtil.writeString(maxAward));
        //写入亮点数据
        List<ActivityRunRankTempDo.AwardRank> lastAmountOrScoreAwardRank = activityAwardMatchService.findLastAmountOrScoreAwardRank(mainActivity.getId());
        proActivityHighlightService.create(mainActivity.getId(), lastAmountOrScoreAwardRank);
    }


    private void removalActivity(MainActivity mainActivity, String offlineReason) {
        if (Arrays.asList(0, 1, 2).contains(mainActivity.getActivityState())) {
            //如果时竞技赛，在活动已结束后，且奖励已下方的情况下，不允许下架活动
            Optional<CompetitiveSeasonDo> competitiveSeasonOp = competitiveSeasonService.findByActivityId(mainActivity.getId());
            if (competitiveSeasonOp.isPresent()) {
                if (Integer.valueOf(2).equals(mainActivity.getActivityState())) {
                    if (AwardSendStatusEnum.NO_VIEW_CONFIG.getCode().equals(mainActivity.getAwardSendStatus())
                            || AwardSendStatusEnum.VIEW_PASS.getCode().equals(mainActivity.getAwardSendStatus())
                            || AwardSendStatusEnum.VIEW_SENDING.getCode().equals(mainActivity.getAwardSendStatus())) {
//                        认为竞技分已经发放，不允许下架
                        throw new BaseException("竞技分已经下发，不允许下架活动");
                    }
                }
                CompetitiveSeasonDo competitiveSeasonDo = competitiveSeasonOp.get();
                competitiveSeasonConfigBizService.updateActivityCount(competitiveSeasonDo.getSeasonId(), mainActivity.activityStartDateZonedDateTime(), false);
            }
            mainActivity.setStatus(1);
            mainActivity.setActivityState(3);
            mainActivity.setOfflineReason(offlineReason);
            mainActivityService.update(mainActivity);
            //下架所有阶段活动
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                for (MainActivity segmentActivity : seriesActivityRelService.getAllMainActivity(mainActivity.getId())) {
                    segmentActivity.setStatus(1);
                    segmentActivity.setActivityState(3);
                    segmentActivity.setOfflineReason(offlineReason);
                    mainActivityService.update(segmentActivity);

                }
            }

            //活动下架退款
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
            new Thread(() -> {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                refundActivity(mainActivity);
            }).start();

        }
    }

    public void refundActivity(MainActivity mainActivity) {
        ActivityFee fee = activityFeeService.findFeeEntry(mainActivity.getId(), null);
        if (Objects.isNull(fee)) {
            log.info("payActivity end，费用配置为空");
            return;
        }
        //免费
        if ("free".equals(fee.getType())) {
            return;
        }
        AccountDetailTypeEnum accountDetailTypeEnum = null;
        if (Arrays.asList("fee", "scoreFee", ActivityFeeTypeEnum.SCORE_OR_FEE.getType()).contains(fee.getType())) {
            accountDetailTypeEnum = AccountDetailTypeEnum.FEE;
        } else {
            accountDetailTypeEnum = AccountDetailTypeEnum.SECURITY_FUND;
        }
        List<ZnsRunActivityUserEntity> allActivityUser = activityUserService.findAllActivityUser(mainActivity.getId());
        log.info("allActivityUser size:{}", CollectionUtils.isNotEmpty(allActivityUser) ? allActivityUser.size() : 0);
        for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
            //退钱
            ZnsUserAccountDetailEntity userAccountDetailEntity = userAccountDetailService.selectAccountDetail(2, accountDetailTypeEnum, activityUser.getUserId(), mainActivity.getId());
            if (null != userAccountDetailEntity && userAccountDetailEntity.getRefundStatus() == 0) {
                userAccountService.refundBalance(userAccountDetailEntity, "activity offline refundBalance");
                log.info("用户活动下架退款{}", userAccountDetailEntity);
            }
            //退积分
            if ("score".equals(fee.getType())
                    || "scoreFee".equals(fee.getType())
                    || ActivityFeeTypeEnum.SCORE_OR_FEE.getType().equals(fee.getType())) {
                if (fee.getScore() <= 0) {
                    //未使用积分报名，无需抵扣或回退积分
                    log.info("活动={}发起的退款没有查询的使用积分记录，不需要抵扣或回退积分", mainActivity.getId());
                }

                //如果是取消比赛，需要确定是否之前使用过积分
                ActivityUserScore existedActivityUserScore = activityUserScoreService.selectActivityUserScoreByActivityUserId(mainActivity.getId(), activityUser.getUserId(), ScoreConstant.SourceTypeEnum.source_type_21.getType());
                //没有扣除或已经退回过积分，则不要退回积分
                if (Objects.isNull(existedActivityUserScore) || Objects.equals(existedActivityUserScore.getIncome(), 1)) {
                    //如果是用户发起的退款，但是没有查到积分，则抛不做任何处理
                    log.info("用户={}对活动={}发起的退款没有查询的使用积分记录，不需要返还积分", activityUser.getUserId(), mainActivity.getId());
                    continue;
                }

                ActivityUserScore activityUserScore = activityUserScoreService.getRefundActivityUserScore(mainActivity, activityUser.getUserId(), fee.getScore(), true);
                activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);
                log.info("用户={}报名活动={}退回积分={}", activityUser.getUserId(), mainActivity.getId(), fee.getScore());

            }
            //劵退回
            UserCoupon coupon = userCouponService.getUserCouponByActivityAndUserIdAndStatus(mainActivity.getId(), activityUser.getUserId(), 2);
            if (coupon != null) {
                userCouponBizService.sendUserCouponSource(coupon.getCouponId(), activityUser.getUserId(), -1l, CouponConstant.SourceTypeEnum.source_type_10.getType(), false);
                log.info("活动下架用户{}退劵{}", activityUser.getUserId(), coupon.getCouponId());
            }

        }

    }

    /**
     * 团队赛预设队长自动报名
     */
    public void autoEnroll(MainActivity mainActivity) {
        List<ActivityTeam> activityTeams = activityTeamService.findByActId(mainActivity.getId());
        if (org.springframework.util.CollectionUtils.isEmpty(activityTeams)) {
            return;
        }
        List<ActivityTeam> collect = activityTeams.stream().filter(s -> s.getIsOfficial() == 0).toList();
        if (org.springframework.util.CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<Long> userId = collect.stream().map(ActivityTeam::getTeamManagerId).filter(Objects::nonNull).toList();
        if (org.springframework.util.CollectionUtils.isEmpty(userId)) {
            return;
        }
        AutoEnrollDto autoEnrollDto = new AutoEnrollDto();
        autoEnrollDto.setActivityId(mainActivity.getId());
        autoEnrollDto.setIds(userId);
        log.info("ConsloeActivityManager#autoEnrollActivity-----团队赛预设队长自动延时报名，ids:{},activityId:{}", userId, mainActivity.getId());

        Long time = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(), "UTC");

        long nowTime = ZonedDateTime.now().toInstant().toEpochMilli();

        long waitMillisecond = time - nowTime; //延迟时间，报名时间和当前时间差
        rabbitTemplate.convertAndSend(delay_exchange_name_rot, "autoEnrollActivity", JsonUtil.writeString(autoEnrollDto), message -> {
            message.getMessageProperties().setDelay(waitMillisecond <= 0 ? 10000 : (int) waitMillisecond);// 毫秒为单位，指定此消息的延时时长,
            return message;
        });
    }


    @Transactional
    public MainActivity createSingleActivity(SingleActivityCreateRequest request) {
        return createSingleActivity(request, MainActivityTypeEnum.SINGLE);
    }

    public MainActivity createSingleActivity(SingleActivityCreateRequest request, MainActivityTypeEnum type) {
        boolean enableMultiply = request.getMultiplySwitch() == 1;
        List<Runnable> tasks = new ArrayList<>();

        MainActivity mainActivity = new MainActivity();
        Long mainActId = request.getActivityId();


        if (mainActId != null) {
            String excutingKey = RedisConstants.AGGREGATION_TASK_EXECUTION + mainActId;
            if (redisTemplate.hasKey(excutingKey)) {
                throw new BaseException("正在生成聚合活动，请勿编辑");
            }

            MainActivity oldActivity = mainActivityService.findById(mainActId);
            //上架后只允许编辑宣发
            if (!MainActivityStateEnum.NOT_PUBLISHED.getCode().equals(oldActivity.getActivityState())) {
                activityDisseminateService.deleteByActId(mainActId);
                request.getSingleActivityDistributionCreateRequest().setIsShowInMore(oldActivity.getIsShowInMore());
                processSingleActDistribution(oldActivity, request.getSingleActivityDistributionCreateRequest());
                //保存是否ai解说
                if (request.getSingleActivityReportCreateRequest().getIsAiCommentary() == null) {
                    request.getSingleActivityReportCreateRequest().setIsAiCommentary(ActivityStringConstant.KEYS.DEFAULT_IS_AI_COMMENTARY);
                }
                activityParamsService.saveConfigSingleValue
                        (mainActId, ActivitySettingConfigEnum.IS_AI_COMMENTARY, request.getSingleActivityReportCreateRequest().getIsAiCommentary());
//                processProActivity(mainActivity, request.getSingleActivityDistributionCreateRequest().getProActivitySettings());
                if (request.getSingleActivityDistributionCreateRequest().getProActivitySettings() != null) {
                    proActivityService.updateHighlight(request.getActivityId(), request.getSingleActivityDistributionCreateRequest().getProActivitySettings().getHighlight());
                }
                return oldActivity;
            }
            //编辑 清理所有相关表
            cleanSingleActivity(mainActId);
            mainActivity.setId(mainActId);
        } else {
            mainActivity.setId(idGenerateService.generateActivityID());
        }
        mainActivity.setMainType(type.getType());

        //前置处理
        preProcessSingleRequest(request);
        //保存赛事规则
        List<SubActivity> subActivityList = processSingleActRules(mainActivity, request.getSingleActivityRuleCreateRequest(), request.getSingleActivityReportCreateRequest());

        if (enableMultiply) {
            //保存赛事报名信息
            tasks.add(() -> processSingleActReport(mainActivity, subActivityList, request.getSingleActivityReportCreateRequest()));
            //保存赛事费用奖励信息
            tasks.add(() -> processSingleActFeeAndAward(mainActivity, subActivityList, request.getSingleActivityFeeAndAwardCreateRequest(), request.getSingleActivityRuleCreateRequest()));
            //保存赛事宣发信息
            tasks.add(() -> processSingleActDistribution(mainActivity, request.getSingleActivityDistributionCreateRequest()));
            //保存并进行后置处理
            tasks.add(() -> processSingleActPost(mainActivity, subActivityList, request));
            //处理复制聚合
            tasks.add(() -> processCopyPoly(request.getTemplateId(), mainActivity));
            multiplyThreadTransactionManager.runAsyncButWaitUntilAllDown(tasks, true);
        } else {
            //保存赛事报名信息
            processSingleActReport(mainActivity, subActivityList, request.getSingleActivityReportCreateRequest());
            //保存赛事费用奖励信息
            processSingleActFeeAndAward(mainActivity, subActivityList, request.getSingleActivityFeeAndAwardCreateRequest(), request.getSingleActivityRuleCreateRequest());
            //保存赛事宣发信息
            processSingleActDistribution(mainActivity, request.getSingleActivityDistributionCreateRequest());
            //保存并进行后置处理
            processSingleActPost(mainActivity, subActivityList, request);
            //处理复制聚合
            processCopyPoly(request.getTemplateId(), mainActivity);

        }
        //竞技赛配置信息，如果活动配置修改未不符合配置了。则清空竞技赛配置
        processCompetitiveSeason(mainActivity);
        //处理ProActivity配置
        if (request.getSingleActivityDistributionCreateRequest() != null) {
            processProActivity(mainActivity, request.getSingleActivityDistributionCreateRequest().getProActivitySettings());
        }
        return mainActivity;
    }

    public void processProActivity(MainActivity mainActivity, ProActivitySettings proActivityType) {
        if (proActivityType == null || mainActivity == null || null == mainActivity.getId()) {
            return;
        }
        MainActivity mainActivityNew = new MainActivity();
        mainActivityNew.setProActivityType(proActivityType.getProActivityType());
        mainActivityNew.setId(mainActivity.getId());
        mainActivityService.update(mainActivityNew);

        proActivityService.saveProActivity(mainActivity.getId(), proActivityType);

    }

    public void processCompetitiveSeason(MainActivity mainActivity) {
        if (competitiveSeasonBizService.isCompetitiveActivity(mainActivity.getId())) {
            if (competitiveSeasonRuleValid.checkActivityIsCompetitive(mainActivity.getId())) {
                //判断当前是否是竞技赛，如果不符合条件需要移除相关配置
                if (!competitiveSeasonRuleValid.checkActivityIsCompetitive(mainActivity.getId()))
                    competitiveSeasonBizService.cancelCompetitiveSeason(mainActivity.getId());
            }
        }

    }

    private void processCopyPoly(Long templateId, MainActivity mainActivity) {
        if (templateId == null) {
            return;
        }
        PolymerizationActivityPole pole = polymerizationActivityPoleService.findByActivityId(templateId);
        if (pole == null) {
            return;
        }
        Long activityId = mainActivity.getId();
        //关闭老配置
        pole.setAutoStatus(0);
        polymerizationActivityPoleService.update(pole);
        //开启新配置
        PolymerizationActivityPole newPole = new PolymerizationActivityPole();
        BeanUtils.copyProperties(pole, newPole);
        newPole.setAutoStatus(1);
        newPole.setMainActivityId(activityId);
        newPole.setId(null);
        newPole.setGmtModify(null);
        newPole.setGmtCreate(null);
        newPole.setTemplateId(templateId);
        polymerizationActivityPoleService.insert(newPole);

    }


    private void processSingleActReport(MainActivity mainActivity, List<SubActivity> subActivityList, SingleActivityReportCreateRequest request) {

        BeanUtil.copyPropertiesIgnoreNull(request, mainActivity);
        //报名开始时间需要特殊处理
        mainActivity.setApplicationStartTime(request.getApplicationStartTime());
        Long mainActId = mainActivity.getId();


        //报名时间类型 0自定义 1不限
        if (!StringUtils.hasText(request.getApplicationStartTime())) {
            mainActivity.setReportTimeType(1);
        } else {
            mainActivity.setReportTimeType(0);
        }

        //保存设备
        saveActivityEquipments(request.getEquipments(), mainActivity.getId());

        //保存互斥活动
        List<Long> mutexActivityIds = request.getMutexActivityIdList();
        if (!org.springframework.util.CollectionUtils.isEmpty(mutexActivityIds)) {
            mainActivity.setMutexActivityIds(JsonUtil.writeString(mutexActivityIds));
        } else {
            mainActivity.setMutexActivityIds(null);
            mainActivityService.clearMutexActivity(mainActId);
        }


        //保存活动地区
        activityAreaService.saveActivityArea(mainActId, request.getAreas());

        //保存人群信息
        List<ActivityUserGroup> userGroups = new ArrayList<>();
        request.getGroupIds().forEach(groupId -> {
            ActivityUserGroup userGroup = new ActivityUserGroup();
            userGroup.setMainActivityId(mainActId);
            userGroup.setGroupId(groupId);
            userGroup.setType(request.getGroupType());
            userGroups.add(userGroup);
        });
        if (!org.springframework.util.CollectionUtils.isEmpty(userGroups)) {
            activityUserGroupService.saveBatch(userGroups);
        }


        //保存配速员信息
        List<PacerConfig> pacerConfigList = request.getPaceSetting().stream().map(k -> {
            PacerConfig pacerConfig = new PacerConfig();
            pacerConfig.setActivityId(mainActId);
            pacerConfig.setActivityType(mainActivity.getOldType());
            pacerConfig.setConfigType(2);
            pacerConfig.setAverageMinute(k.getAverageMinute());
            pacerConfig.setAverageSecond(k.getAverageSecond());
            pacerConfig.setAveragePace(k.getAveragePace());
            //todo subActivityId could be added but not only one
            //pacerConfig.setSubActivityId(subActivityService.getSingleActByMain(mainActId).getId());
            return pacerConfig;
        }).toList();
        pacerConfigService.saveBatch(pacerConfigList);

        //机器人配置
        ActivityRotSetting activityRotSetting = new ActivityRotSetting();
        BeanUtil.copyPropertiesIgnoreNull(request.getRotSetting(), activityRotSetting);
        activityRotSetting.setMainActivityId(mainActId);
        activityRotSettingService.insert(activityRotSetting);

        //保存活动阶段
        List<ActivityStageDto> stages = request.getStages();
        if (!org.springframework.util.CollectionUtils.isEmpty(stages)) {
            stages.sort(Comparator.comparing(ActivityStageDto::getStartTime));
            List<ActivityStage> stageList = new ArrayList<>();
            for (int i = 0; i < stages.size(); i++) {
                ActivityStage stage = new ActivityStage();
                stage.setLevel(i + 1);
                stage.setActivityId(mainActId);
                stage.setStartTime(stages.get(i).getStartTime());
                stage.setEndTime(stages.get(i).getEndTime());
                stage.setWaitTime(request.getWaitTime());
                stageList.add(stage);
            }
            activityStageService.saveBatch(stageList);
        }
        //保存开赛时间限制
        if (request.getAfterStartLimit() != null) {
            activityParamsService.saveConfigSingleValue
                    (mainActId, ActivitySettingConfigEnum.AFTER_START_LIMIT, request.getAfterStartLimit());
        }
        //保存ram限制
        if (request.getRamLimit() == null) {
            request.setRamLimit(ActivityStringConstant.KEYS.DEFAULT_RAM_LIMIT);
        }
        activityParamsService.saveConfigSingleValue
                (mainActId, ActivitySettingConfigEnum.RAM_LIMIT, request.getRamLimit());
        //保存是否ai解锁
        if (request.getIsAiCommentary() == null) {
            request.setIsAiCommentary(ActivityStringConstant.KEYS.DEFAULT_IS_AI_COMMENTARY);
        }
        activityParamsService.saveConfigSingleValue
                (mainActId, ActivitySettingConfigEnum.IS_AI_COMMENTARY, request.getIsAiCommentary());
        //deskBike version版本号。
        if(Objects.isNull(request.getDeskBikeEquipmentVersion())){
           activityParamsService.deleteConfigSingleValues(mainActId,List.of(ActivitySettingConfigEnum.DESK_BIKE_EQUIPMENT_VERSION));
        }else{
            activityParamsService.saveConfigSingleValue
                    (mainActId, ActivitySettingConfigEnum.DESK_BIKE_EQUIPMENT_VERSION, request.getDeskBikeEquipmentVersion());
        }


        activityParamsService.saveConfigSingleValue
                (mainActId, ActivitySettingConfigEnum.GENDER_LIMIT, request.getGender());
        //保存是否支持名次变化推送 4.7.0 需求10
        activityParamsService.saveConfigSingleValue
                (mainActId, ActivitySettingConfigEnum.ALlOW_USER_RANK_CHANGE_PUSH, checkActivityAllowRankChangePushRule(mainActivity));

    }

    /**
     * 检查活动是否符合支持名次变化推送
     *
     * @return
     */
    private boolean checkActivityAllowRankChangePushRule(MainActivity mainActivity) {
//        单赛事
        if (!MainActivityTypeEnum.SINGLE.getType().equals(mainActivity.getMainType())) {
            return false;
        }

        //        参赛方式：个人d
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (entryGameplay == null || entryGameplay.getCompetitionFormat() != 0) {
            return false;
        }
        //限制设备为跑步机
        Gameplay byId = gameplayService.findById(mainActivity.getPlayId());
        if (byId == null || byId.getEquipmentMainType() != DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType()) {
            return false;
        }
//        目标类型：选择时长或里程
        if (!List.of(1, 2).contains(entryGameplay.getTargetType())) {
            return false;
        }
//        排名依据：选择时长或里程
        if (StringUtils.hasText(entryGameplay.getRankingBy())) {
            String[] split = entryGameplay.getRankingBy().split(",");
            if (split.length > 0) {
                for (String s : split) {
                    if (!List.of("1", "2").contains(s)) {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }
        //一人多次。
        if (entryGameplay.getEntryCount() != 2 && entryGameplay.getEntryCount() != 3) {
            return false;
        }
        //最佳成绩完赛
        return 2 == entryGameplay.getFetchRule();
    }

    public void saveActivityEquipments(List<Equipment> equipments, Long mainActivityId) {
        List<ActivityEquipmentConfig> equipmentList = equipments.stream().map(k -> {
            ActivityEquipmentConfig equipmentConfig = new ActivityEquipmentConfig();
            BeanUtil.copyPropertiesIgnoreNull(k, equipmentConfig);
            equipmentConfig.setActivityId(mainActivityId);
            return equipmentConfig;
        }).toList();

        activityEquipmentConfigService.save(equipmentList);
    }


    private List<SubActivity> processSingleActRules(MainActivity mainActivity, SingleActivityRuleCreateRequest request, SingleActivityReportCreateRequest singleActivityReportCreateRequest) {
        Long mainActId = mainActivity.getId();
        BeanUtil.copyPropertiesIgnoreNull(request, mainActivity);

        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(request.getPlayId());
        //组装子活动
        List<SubActivity> subActivityList = request.getTargets().stream().map(t -> {
            SubActivity subActivity = new SubActivity();
            BeanUtil.copyPropertiesIgnoreNull(singleActivityReportCreateRequest, subActivity);

            subActivity.setTarget(t);
            BeanUtil.copyPropertiesIgnoreNull(request, subActivity);
            subActivity.setType(mainActivity.getMainType());
            subActivity.setSegmentPlayId(request.getPlayId());
            subActivity.setTarget(t);
            subActivity.setId(idGenerateService.generateActivityID());
            subActivity.setMainActivityId(mainActId);
            //保存用户参赛限制
            Integer entryCount = entryGameplay.getEntryCount();
            if (entryCount == 1) {
                subActivity.setUserEnterLimit(1);
            }
            return subActivity;
        }).toList();
        subActivityService.savaBatch(subActivityList);


        //组装子活动活动道具配置表
        List<ActivityPropConfig> activityPropConfigs = new ArrayList<>();
        request.getActivityPropConfig().forEach(activityPropConfigDto -> {
            ActivityPropConfig activityPropConfig = new ActivityPropConfig();
            BeanUtil.copyPropertiesIgnoreNull(activityPropConfigDto, activityPropConfig);
            activityPropConfig.setActivityId(mainActId);
            //single activity not need sub_activity_id
            activityPropConfigs.add(activityPropConfig);
        });
        activityPropConfigService.saveBatch(activityPropConfigs);

        //组装子活动速率表
        List<ActivityRateLimit> activityRateLimits = new ArrayList<>();
        for (ActivityRateLimitDto activityRateLimitDto : request.getRateLimit()) {
            activityRateLimitDto.getRateLimitDetailDtoList().forEach(k -> {
                ActivityRateLimit activityRateLimit = new ActivityRateLimit();
                BeanUtil.copyPropertiesIgnoreNull(k, activityRateLimit);
                activityRateLimit.setActivityId(mainActId);
                activityRateLimit.setRunningGoal(activityRateLimitDto.getRunningGoal());
                activityRateLimit.setIntervalUnit(activityRateLimitDto.getRunningGoalUnit());
                activityRateLimit.setRunningGoalUnit(activityRateLimitDto.getRunningGoalUnit());
                activityRateLimits.add(activityRateLimit);
            });
        }
        activityRateLimitService.save(activityRateLimits);

        //保存团队赛配置
        if (request.getTeamType() != null) {
            activityParamsService.saveConfigSingleValue(mainActId,
                    ActivitySettingConfigEnum.ALLOW_JOIN_TEAM, request.getTeamType());
            if (ActivityTeamSettingEnum.ONLY_CLUB.getCode().equals(request.getTeamType())) {
                Integer maxTeamNum = request.getClubTeamConfig().getMaxTeamNum();
                if (maxTeamNum == null || maxTeamNum < 0) {
                    throw new BaseException("俱乐部允许参赛队伍数量填写错误");
                }
                activityParamsService.saveConfigSingleValue(mainActId,
                        ActivitySettingConfigEnum.ALLOW_JOIN_TEAM_LIMIT, maxTeamNum);
                activityParamsService.saveConfigSingleValue(mainActId, ActivitySettingConfigEnum.ALLOW_JOIN_TEAM_MEMBER_LIMIT,
                        request.getEnterLimit());
                //俱乐部团赛达标人数
                if (Objects.nonNull(request.getClubTeamConfig()) && Objects.nonNull(request.getClubTeamConfig().getTargetNum())) {
                    activityParamsService.saveConfigSingleValue(mainActId, ActivitySettingConfigEnum.TARGET_USER_COUNT, request.getClubTeamConfig().getTargetNum());
                }
            } else if (ActivityTeamSettingEnum.ONLY_CUSTOMER.getCode().equals(request.getTeamType())) {
                Integer maxTeamNum = request.getMaxTeamNum();
                if (maxTeamNum == null || maxTeamNum < 0) {
                    throw new BaseException("允许用户创队伍允许参赛队伍数量填写错误");
                }
                activityParamsService.saveConfigSingleValue(mainActId,
                        ActivitySettingConfigEnum.ALLOW_JOIN_TEAM_LIMIT, maxTeamNum);
                activityParamsService.saveConfigSingleValue(mainActId, ActivitySettingConfigEnum.ALLOW_JOIN_TEAM_MEMBER_LIMIT,
                        request.getEnterLimit());

            }
        }
        //保存官方团队配置
        List<TeamConfigDto> teamConfigDtos = request.getTeamConfigDtos();
        if (!org.springframework.util.CollectionUtils.isEmpty(teamConfigDtos)) {
            List<ActivityTeam> activityTeams = new ArrayList<>();
            teamConfigDtos.forEach(teamConfigDto -> {
                ActivityTeam activityTeam = new ActivityTeam();
                BeanUtil.copyPropertiesIgnoreNull(teamConfigDto, activityTeam);
                activityTeam.setMaxNum(request.getEnterLimit());
                activityTeam.setActivityId(mainActId);
                activityTeam.setIsOfficial(1);
                activityTeam.setTeamType(ActivityTeamTypeEnum.OFFICIAL.getCode());
                activityTeams.add(activityTeam);
            });
            activityTeamService.saveBatch(activityTeams);
        }
        //保存非官方队伍
        List<TeamConfigDto> nonOfficialTeamConfigDtos = request.getNonOfficialTeamConfigDtos();
        if (!org.springframework.util.CollectionUtils.isEmpty(nonOfficialTeamConfigDtos)) {
            List<ActivityTeam> activityTeams = new ArrayList<>();
            nonOfficialTeamConfigDtos.forEach(teamConfigDto -> {
                ActivityTeam activityTeam = new ActivityTeam();
                BeanUtil.copyPropertiesIgnoreNull(teamConfigDto, activityTeam);
                activityTeam.setMaxNum(request.getEnterLimit());
                activityTeam.setActivityId(mainActId);
                activityTeam.setIsOfficial(0);
                activityTeam.setTeamType(ActivityTeamTypeEnum.CUSTOMER.getCode());
                ZnsUserEntity user = userService.findByEmail(teamConfigDto.getCaptainEmail());
                activityTeam.setTeamManagerId(Objects.nonNull(user) ? user.getId() : null);
                activityTeam.setCurrentNum(Objects.nonNull(user) ? 1 : 0);
                activityTeams.add(activityTeam);
            });
            activityTeamService.saveBatch(activityTeams);
        }

        //保存歌单
        saveMusicPlays(mainActId, request.getMusicListId());

        return subActivityList;

    }

    public void saveMusicPlays(Long mainActId, List<Long> musicListId) {
        List<ActivityPlaylistRel> activityPlaylistRels = new ArrayList<>();
        musicListId.forEach(playId -> {
            ActivityPlaylistRel activityPlaylistRel = new ActivityPlaylistRel();
            activityPlaylistRel.setActivityId(mainActId);
            activityPlaylistRel.setPlaylistId(playId);
            activityPlaylistRels.add(activityPlaylistRel);
        });
        activityPlaylistRelService.save(activityPlaylistRels);
    }

    private void processSingleActDistribution(MainActivity mainActivity, SingleActivityDistributionCreateRequest request) {
        Long mainActId = mainActivity.getId();
        List<ActivityDisseminate> activityDisseminates = new ArrayList<>();
        request.getDistributionRequests().forEach(k -> {
            ActivityDisseminate activityDisseminate = new ActivityDisseminate();
            BeanUtil.copyPropertiesIgnoreNull(k, activityDisseminate);
            activityDisseminate.setMainActivityId(mainActId);
            activityDisseminates.add(activityDisseminate);
            if (k.getCategoryType() != null) {
                mainActivity.setCategoryType(k.getCategoryType());
            }
        });
        activityDisseminateService.saveBatch(activityDisseminates);
        mainActivity.setIsShowInMore(request.getIsShowInMore());
        ActivityRecommendSetting activityRecommendSetting = request.getActivityRecommendSetting();
        if (activityRecommendSetting != null) {
            List<String> recommendPosition = activityRecommendSetting.getRecommendPosition();
            if (CollectionUtils.isNotEmpty(recommendPosition)) {
                if (recommendPosition.contains(ActivityRecommendPositionEnum.HOME.getPosition())) {
                    activityParamsService.saveConfigSingleValue
                            (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME, 1);
                } else {
                    activityParamsService.saveConfigSingleValue
                            (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME, 0);
                    activityRecommendTimelineService.deleteByActivityId(mainActId);
                }
                if (recommendPosition.contains(ActivityRecommendPositionEnum.COMMUNITY.getPosition())) {
                    activityParamsService.saveConfigSingleValue
                            (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, 1);
                } else {
                    activityParamsService.saveConfigSingleValue
                            (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, 0);
                }

                if (activityRecommendSetting.getDefaultLanguageCode() != null) {
                    activityParamsService.saveConfigSingleValue
                            (mainActId, ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE, activityRecommendSetting.getDefaultLanguageCode());
                    List<ActivityRecommendCommunityDesc> communityDesc = activityRecommendSetting.getCommunityDesc();
                    activityRecommendDisseminateService.save(communityDesc, mainActId, ActivityRecommendPositionEnum.COMMUNITY.getPosition());
                }
                if (recommendPosition.contains(ActivityRecommendPositionEnum.HOME.getPosition())) {
                    //占领首页时间线
                    activityRecommendTimelineService.bindTimeline(mainActId, ActivityRecommendPositionEnum.HOME, mainActivity.activityStartDateZonedDateTime(), mainActivity.activityEndDateZonedDateTime());
                }
            }
        } else {
            activityParamsService.saveConfigSingleValue
                    (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_COMMUNITY, 0);
            activityParamsService.saveConfigSingleValue
                    (mainActId, ActivitySettingConfigEnum.ACTIVITY_RECOMMEND_POSITION_HOME, 0);
            activityRecommendTimelineService.deleteByActivityId(mainActId);
        }
    }

    public void cleanSeriesActivity(Long mainActId) {
        cleanSingleActivity(mainActId);
        //删除关联表
        seriesActivityRelService.deleteByActId(mainActId);
        //处理子阶段
        List<MainActivity> SegmentMainActivityList = seriesActivityRelService.getAllMainActivity(mainActId);
        for (MainActivity segmentActivity : SegmentMainActivityList) {
            cleanSingleActivity(segmentActivity.getId());
            mainActivityService.deleteById(segmentActivity.getId());
            activityEnterThresholdService.deleteByActId(segmentActivity.getId());
        }
    }

    public void cleanSingleActivity(Long mainActId) {
        //清理
        subActivityService.deleteByActId(mainActId);
        activityPropConfigService.deleteByActId(mainActId);
        activityRateLimitService.deleteByActId(mainActId);
        activityTeamService.deleteByActId(mainActId);
        activityPlaylistRelService.deleteByActId(mainActId);
        activityEquipmentConfigService.deleteByActId(mainActId);
        activityAreaService.deleteByActId(mainActId);
        activityUserGroupService.deleteByActId(mainActId);
        pacerConfigService.deleteByActId(mainActId);
        activityRotSettingService.deleteByActId(mainActId);
        activityFeeService.deleteByActId(mainActId);
        brandRightsInterestsService.deleteByActId(mainActId);
        activityMedalService.deleteByActId(mainActId);
        activityDisseminateService.deleteByActId(mainActId);
        //奖励删除
        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(mainActId);
        awardActivityBizService.deleteAwardConfig(awardQuery);
        impracticalAwardConfigService.deleteByActId(mainActId);
        //不移除指定的配置
        activityParamsService.deleteByActId(mainActId,
                List.of(ActivitySettingConfigEnum.COMPETITIVE_SEASON_ACTIVITY_RANKING_THRESHOLD_CUSTOM_SEASON,
                        ActivitySettingConfigEnum.COMPETITIVE_COUNT_DOWN_SETTING));
        activityStageService.deleteByActId(mainActId);
    }


    private void preProcessSingleRequest(SingleActivityCreateRequest request) {
        SingleActivityRuleCreateRequest ruleCreateRequest = request.getSingleActivityRuleCreateRequest();
        //前段传min，需要转成s
        if (ruleCreateRequest.getTargetType() == 2) {
            ruleCreateRequest.setTargets(ruleCreateRequest.getTargets().stream().map(t -> t * 60).toList());
        }
        //确定时间
        SingleActivityReportCreateRequest reportCreateRequest = request.getSingleActivityReportCreateRequest();
        Integer timeStyle = reportCreateRequest.getTimeStyle();

        //是否是推荐活动。
        boolean settingRecommend = request.getSingleActivityRuleCreateRequest() != null && request.getSingleActivityDistributionCreateRequest().getActivityRecommendSetting() != null
                && CollectionUtils.isNotEmpty(request.getSingleActivityDistributionCreateRequest().getActivityRecommendSetting().getRecommendPosition());


        if (timeStyle == 0) {
            reportCreateRequest.setActivityStartTime(DateUtil.convertTimeStrByZone(reportCreateRequest.getActivityStartTime(), "GMT+08:00", "UTC"));
            reportCreateRequest.setActivityEndTime(DateUtil.convertTimeStrByZone(reportCreateRequest.getActivityEndTime(), "GMT+08:00", "UTC"));
            if (StringUtils.hasText(reportCreateRequest.getApplicationStartTime())) {
                reportCreateRequest.setApplicationStartTime(
                        DateUtil.convertTimeStrByZone(reportCreateRequest.getApplicationStartTime(), "GMT+08:00", "UTC")
                );
            }
            reportCreateRequest.setApplicationEndTime(DateUtil.convertTimeStrByZone(reportCreateRequest.getApplicationEndTime(), "GMT+08:00", "UTC"));
        } else if (timeStyle == 1) {
            if (settingRecommend) {
                throw new BizException("跟随用户时区，不可设置推荐。");
            }
        }
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(request.getSingleActivityRuleCreateRequest().getPlayId());
        if (entryGameplay != null && entryGameplay.getCompetitionFormat() == 1 && settingRecommend) {
            throw new BizException("团队赛，不可设置推荐。");
        }
        if (!CollectionUtils.isEmpty(request.getSingleActivityReportCreateRequest().getAreas())
                && !Objects.equals(request.getSingleActivityReportCreateRequest().getAreas().get(0).getCountryId(), 0L) && settingRecommend) {
            throw new BizException("区域不是全球，不可设置推荐。");
        }
        if ("1".equals(recommendCheckUserGroup) && settingRecommend && request.getSingleActivityReportCreateRequest() != null && request.getSingleActivityReportCreateRequest().getGroupType() != null && List.of(1, 0).contains(request.getSingleActivityReportCreateRequest().getGroupType())) {
            throw new BizException("已设置用户范围，不可设置推荐。");
        }
    }

    private void processSingleActPost(MainActivity mainActivity, List<SubActivity> subActivityList, SingleActivityCreateRequest request) {

        BeanUtil.copyPropertiesIgnoreNull(request.getSingleActivityReportCreateRequest(), mainActivity);
        BeanUtil.copyPropertiesIgnoreNull(request.getSingleActivityFeeAndAwardCreateRequest(), mainActivity);
        //保存互斥活动
        List<Long> mutexActivityIds = request.getSingleActivityReportCreateRequest().getMutexActivityIdList();
        if (!org.springframework.util.CollectionUtils.isEmpty(mutexActivityIds)) {
            mainActivity.setMutexActivityIds(JsonUtil.writeString(mutexActivityIds));
        } else {
            mainActivity.setMutexActivityIds(null);
            mainActivityService.clearMutexActivity(mainActivity.getId());
        }


        Gameplay gameplay = gameplayService.findById(mainActivity.getPlayId());
        mainActivity.setOldType(gameplay.getOldActivityType());
        // 赛事保存玩法设备类型
        mainActivity.setEquipmentMainType(gameplay.getEquipmentMainType());
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        if (entryGameplay != null) {
            mainActivity.setCompetitionFormat(entryGameplay.getCompetitionFormat());
        }
        mainActivityService.insertOrUpdate(mainActivity);
        //手动置为空
        if (mainActivity.getApplicationStartTime() == null) {
            mainActivityService.update(mainActivity, Wrappers.<MainActivity>lambdaUpdate()
                    .set(MainActivity::getApplicationStartTime, null)
                    .eq(MainActivity::getId, mainActivity.getId()));
        }
        if(mainActivity.getEquipmentVersion()==null){
            mainActivityService.update(mainActivity, Wrappers.<MainActivity>lambdaUpdate()
                    .set(MainActivity::getEquipmentVersion, null)
                    .eq(MainActivity::getId, mainActivity.getId()));
        }
        roomIdBizService.createGameRoom(mainActivity, subActivityList);

    }


    private void processSingleActFeeAndAward(MainActivity mainActivity, List<SubActivity> subActivityList, SingleActivityFeeAndAwardCreateRequest request, SingleActivityRuleCreateRequest ruleCreateRequest) {
        //前置处理
        preProcessSingleFeeRequest(request);

        Long mainActId = mainActivity.getId();
        BeanUtil.copyPropertiesIgnoreNull(request, mainActivity);
        List<ActivityFee> activityFeeList = request.getAmounts().stream().map(k -> {
            ActivityFee activityFee = new ActivityFee();
            BeanUtil.copyPropertiesIgnoreNull(request, activityFee);
            activityFee.setAmount(k.getAmount());
            activityFee.setCurrency(k.getCurrencyCode());
            activityFee.setMainActivityId(mainActId);
            return activityFee;
        }).toList();
        activityFeeService.saveBatch(activityFeeList);

        //保存品牌证书勋章相关信息
        saveBrandRightDto(request.getBrandRightDto(), mainActivity);
        //保存奖励
        List<ActivityTargetAwardDto> targetAwardDtos = request.getTargetAwardDtos();
        //报名奖励填充
        impracticalAwardConfigService.saveApplicationAward(mainActivity, request.getApplicationRewardDto(), ruleCreateRequest, targetAwardDtos, true, request.getTeamAwardType());
        //各目标奖励
        awardActivityBizService.saveTargetAward(targetAwardDtos, mainActivity, subActivityList);
        //保存分享奖励
        impracticalAwardConfigService.saveSharedAward(mainActivity, request.getShareScore());
        // 奖励人工审核配置
        if (!MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType()) && !MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
            saveAwardSendType(mainActivity, request);
        }
        //保存奖励发送类型
        if (Objects.nonNull(request.getAllowAwardCouponSwitch())) {
            activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                    ActivitySettingConfigEnum.ALLOW_AWARD_COUPON_SWITCH, request.getAllowAwardCouponSwitch());
        }
        //保存视频审核
        if (Objects.nonNull(request.getVideoViewDto())) {
            activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                    ActivitySettingConfigEnum.VIDEO_VIEW_CONFIG, JsonUtil.writeString(request.getVideoViewDto()));
        }

    }

    public void preProcessSingleFeeRequest(SingleActivityFeeAndAwardCreateRequest request) {
        request.getTargetAwardDtos().stream().forEach(k -> {
            k.getAwardDtos().stream().forEach(j -> {
                if (AwardSentTypeEnum.COMPLETING_THE_GAME.getType().equals(j.getType())) {
                    j.getAwardLists().stream().forEach(i -> {
                        setAwardType(i, request.getCompleteAmountAwardType(), request.getCompleteScoreAwardType());
                    });
                } else if (AwardSentTypeEnum.SURPASS_AWARD.getType().equals(j.getType())) {
                    j.getAwardLists().stream().forEach(i -> {
                        setAwardType(i, request.getSurpassAmountAwardType(), request.getSurpassScoreAwardType());
                    });
                }
            });
        });
    }

    private void setAwardType(ActivityAwardDto dto, Integer completeAmountAwardType, Integer completeScoreAwardType) {
        if (!CollectionUtils.isEmpty(dto.getAmountLists())) {
            dto.setIsDivideAmount(completeAmountAwardType);
        }
        if (Objects.nonNull(dto.getScore()) && dto.getScore() > 0) {
            dto.setIsDivideScore(completeScoreAwardType);
        }
    }

    public void saveBrandRightDto(BrandRightDto brandRightDto, MainActivity mainActivity) {
        if (Objects.isNull(brandRightDto)) {
            return;
        }
        //品牌勋章
        BeanUtil.copyPropertiesIgnoreNull(brandRightDto, mainActivity);

        //品牌权益
        List<BrandRightAwardDto> rightAwardDtos = brandRightDto.getBrandRightAwardDtos();
        if (!org.springframework.util.CollectionUtils.isEmpty(rightAwardDtos)) {
            List<ActivityBrandRightsInterests> rightsInterestsList = new ArrayList<>();
            for (BrandRightAwardDto brandRightAwardDto : rightAwardDtos) {
                ActivityBrandRightsInterests rightsInterests = new ActivityBrandRightsInterests();
                BeanUtil.copyPropertiesIgnoreNull(brandRightAwardDto, rightsInterests);
                rightsInterests.setActivityId(mainActivity.getId());
                rightsInterestsList.add(rightsInterests);
            }
            brandRightsInterestsService.save(rightsInterestsList);
        }

        //勋章
        if (brandRightDto.getMedalId() != null) {
            RunActivityMedal activityMedal = new RunActivityMedal();
            activityMedal.setActivityId(mainActivity.getId());
            activityMedal.setMedalConfigId(brandRightDto.getMedalId());
            activityMedalService.save(activityMedal);
        }
    }


    public void saveAwardSendType(MainActivity mainActivity, SingleActivityFeeAndAwardCreateRequest feeAwardRequest) {
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.AWARD_SEND_TYPE, feeAwardRequest.getAwardSendType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.COMPLETE_AWARD_SEND_TYPE, feeAwardRequest.getCompleteAwardSendType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.SURPASS_AWARD_SEND_TYPE, feeAwardRequest.getSurpassAwardSendType());
        //冗余保存，方便展示
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.COMPLETE_AMOUNT_AWARD_TYPE, feeAwardRequest.getCompleteAmountAwardType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.COMPLETE_SCORE_AWARD_TYPE, feeAwardRequest.getCompleteScoreAwardType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.SURPASS_AMOUNT_AWARD_TYPE, feeAwardRequest.getSurpassAmountAwardType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.SURPASS_SCORE_AWARD_TYPE, feeAwardRequest.getSurpassScoreAwardType());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.SURPASS_USER_CODE, feeAwardRequest.getSurpassUserCode());
        activityParamsService.saveConfigSingleValue(mainActivity.getId(),
                ActivitySettingConfigEnum.RECORD_BREAKING_AWARD_SEND_TYPE, feeAwardRequest.getRecordBreakingAwardSendType());
        Integer awardSendStatus = AwardSendStatusEnum.NO_VIEW_CONFIG.getCode();
        if (Integer.valueOf(1).equals(feeAwardRequest.getAwardSendType())) {
            awardSendStatus = AwardSendStatusEnum.NO_VIEW.getCode();
        } else if (Integer.valueOf(1).equals(feeAwardRequest.getCompleteAwardSendType())) {
            awardSendStatus = AwardSendStatusEnum.NO_VIEW.getCode();
        } else if (Integer.valueOf(1).equals(feeAwardRequest.getSurpassAwardSendType())) {
            awardSendStatus = AwardSendStatusEnum.NO_VIEW.getCode();
        }
        mainActivity.setAwardSendStatus(awardSendStatus);
        mainActivityService.update(mainActivity);
    }

    /**
     * 兼容新活动返回旧活动bean 相关参数
     *
     * @param target
     * @param activityId
     * @param znsRunActivityEntity
     * @return
     */
    public ZnsRunActivityEntity genNewActivityBean(Integer target, Long activityId, ZnsRunActivityEntity znsRunActivityEntity) {
        if (Objects.isNull(znsRunActivityEntity)) {
            MainActivity mainActivity = mainActivityService.findById(activityId);
            SubActivity subActivity = subActivityService.findByMainActIdAndTarget(mainActivity.getId(), target);
            znsRunActivityEntity = new ZnsRunActivityEntity();
            znsRunActivityEntity.setId(mainActivity.getId());
            znsRunActivityEntity.setActivityRouteId(subActivity.getRouteId());
            znsRunActivityEntity.setCompleteRuleType(mainActivity.getTargetType());
            znsRunActivityEntity.setActivityType(mainActivity.getOldType());
        }
        return znsRunActivityEntity;
    }

    public List<Long> getActivityIdList(MainActivity mainActivity) {
        List<Long> activityIdList;
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            activityIdList = seriesActivityRelService.findSubActivityId(mainActivity.getId());
        } else {
            List<MainRunActivityRelationDo> list = mainRunActivityRelationService.findList(MainRunActivityRelationQuery.builder().mainActivityId(mainActivity.getId()).build());
            if (CollectionUtils.isEmpty(list)) {
                activityIdList = Lists.newArrayList(mainActivity.getId());
            } else {
                activityIdList = list.stream().map(MainRunActivityRelationDo::getRunActivityId).collect(Collectors.toList());
            }
        }
        return activityIdList;
    }

    /**
     * 设备是否有可用活动
     */
    @DataCache(value = {"equipmentModel", "userId", "num", "checkUser", "stateCode", "countryCode", "zoneId"}, timeout = 60)
    public List<EquipmentActivityVo> equipmentActivityList(String equipmentModel, String zoneId, Long userId, String stateCode, String countryCode, Integer num, String languageCode, boolean checkUser) {
        List<Long> groupsByUserId = new ArrayList<>();
        if (checkUser) {
            groupsByUserId = userGroupRelService.getGroupsByUserId(userId); //用户所在分组id
            if (org.springframework.util.CollectionUtils.isEmpty(groupsByUserId)) {
                groupsByUserId = List.of(0L);
            }
        }
        if (CollectionUtils.isEmpty(groupsByUserId)) {
            groupsByUserId.add(0L);
        }

        //根据时区校验活动
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(zoneId));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = now.format(formatter);
        List<EquipmentActivityVo> result = mainActivityService.findActivityByEquipmentModel(equipmentModel, languageCode, num, groupsByUserId, stateCode, countryCode, checkUser, currentTime);

        //转换时区
        for (EquipmentActivityVo activityVo : result) {
            String activityStartTimeStr = activityVo.getActivityStartTimeStr();
            if (StringUtils.hasText(activityStartTimeStr)) {
                Long start = DateUtil.getStampByZone(activityStartTimeStr, Objects.equals(0, activityVo.getTimeStyle()) ? "UTC" : zoneId);
                activityVo.setActivityStartTime(new Date(start));
            }
            String activityEndTimeStr = activityVo.getActivityEndTimeStr();
            if (StringUtils.hasText(activityEndTimeStr)) {
                Long end = DateUtil.getStampByZone(activityEndTimeStr, Objects.equals(0, activityVo.getTimeStyle()) ? "UTC" : zoneId);
                activityVo.setActivityEndTime(new Date(end));
            }
        }
        return result;

    }

    /**
     * 获取主活动，如何传入的是系列赛子活动id，会替换为主活动
     *
     * @param activityId
     * @return
     */
    public Optional<MainActivity> getMainActivity(Long activityId) {
        MainActivity byId = mainActivityService.findById(activityId);
        if (!Objects.isNull(byId) && Objects.equals(MainActivityTypeEnum.SERIES_SUB.getType(), byId.getMainType())) {
            return Optional.ofNullable(seriesActivityRelService.getMainActivityBySegmentActId(activityId));
        } else {
            return Optional.ofNullable(byId);
        }
    }

    /**
     * 获取首页推荐信息
     *
     * @return
     */
    public Optional<HomeRecommendActivityDto> getHomeRecommendActivityDto(String languageCode) {
        Optional<ActivityRecommendTimelineDo> activityByTime = activityRecommendTimelineService.findActivityByTime(ActivityRecommendPositionEnum.HOME, ZonedDateTime.now());
        if (activityByTime.isEmpty()) {
            return Optional.empty();
        }

        MainActivity activity = mainActivityService.findById(activityByTime.get().getMainActivityId());
        if (Objects.isNull(activity) || !List.of(ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.IN_PROGRESS.getState()).contains(activity.getActivityState())) {
            return Optional.empty();
        }

        //。5名头像取已报名该赛事，且在报名的用户中的竞技分为前5名（如果有并列，则按照排行榜前五位截取），竞技分榜单取默认年度排行榜；
        ActivityRecommendTimelineDo timeline = activityByTime.get();
        List<Long> userIds = null;
        CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
        int userLimit = 5;
        if (competitiveSeasonSystemDefaultConfig != null && competitiveSeasonSystemDefaultConfig.getAnnualRankId() != null) {
            userIds = activityUserService.findTopInRankByActivityIdAndSeasonId(timeline.getMainActivityId(), competitiveSeasonSystemDefaultConfig.getAnnualRankId(), userLimit);
        }
        if (CollectionUtils.isEmpty(userIds) || userIds.size() < userLimit) {
            userIds = activityUserService.findTopInRankByActivityId(timeline.getMainActivityId(), userLimit);
        }
        HomeRecommendActivityDto homeRecommendActivityDto = new HomeRecommendActivityDto();
        homeRecommendActivityDto.setMainActivityId(timeline.getMainActivityId());
        homeRecommendActivityDto.setActivityStatus(activity.getActivityState());
        homeRecommendActivityDto.setStartDate(activity.activityStartDateZonedDateTime());
        homeRecommendActivityDto.setEndDate(activity.activityEndDateZonedDateTime());
        if (!CollectionUtils.isEmpty(userIds)) {
            homeRecommendActivityDto.setUsers(userIds.stream().map(UserHeadNameDto::new).toList());
        }
        List<ActivityStage> byActId = activityStageService.findByActId(activity.getId());
        if (!CollectionUtils.isEmpty(byActId)) {
//            //有阶段信息
//            Integer level = 0;
//            for (ActivityStage k : byActId) {
//                if (k.getStartTime().toInstant().toEpochMilli() <= (ZonedDateTime.now().toInstant().toEpochMilli())
//                        && k.getEndTime().toInstant().toEpochMilli() >= (ZonedDateTime.now().toInstant().toEpochMilli())
//                ) {
//                    //当前阶段已开始
//                    level = Math.max(level, k.getLevel());
//                }
//            }
//            homeRecommendActivityDto.setLevel(level);
            homeRecommendActivityDto.setActivityStages(byActId);
        }
        //设置图片、标题
        ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(homeRecommendActivityDto.getMainActivityId(), languageCode);
        if (Objects.nonNull(disseminate)) {
            homeRecommendActivityDto.setActivityCoverImage(disseminate.getDisseminatePics());
            if (I18nConstant.LanguageCodeEnum.en_US.getCode().equals(disseminate.getLanguageCode())) {
                //英语显示大写
                homeRecommendActivityDto.setActivityTitle(disseminate.getTitle().toUpperCase());
            } else {
                homeRecommendActivityDto.setActivityTitle(disseminate.getTitle());
            }
        }
        RotationArea route = rotationAreaBizService.getNewActivityRoute(homeRecommendActivityDto.getMainActivityId(), activity.getMainType(), null);
        homeRecommendActivityDto.setRouteUrl(route.getUrl());
        homeRecommendActivityDto.setJumpParam(route.getJumpParam());
        return Optional.of(homeRecommendActivityDto);
    }

    /**
     * 返回指定数量的社区推荐活动
     *
     * @return
     */
    @FillerMethod
    public List<CommunityRecommendActivityDto> getCommunityRecommendActivityDtoList(String languageCode, int size) {
        List<MainActivity> recommendActivity = mainActivityService.findRecommendCommunityActivityIdList(size);
        if (CollectionUtils.isEmpty(recommendActivity)) {
            return new ArrayList<>();
        }
        List<Long> recommendActivityIds = recommendActivity.stream().map(MainActivity::getId).toList();
        List<ActivityHighlightsDo> highlights = activityHighlightsService.getHighlightByActivityIds(recommendActivityIds);
        List<CommunityRecommendActivityDto> result = new ArrayList<>();
        ;
        recommendActivityIds.forEach(activityId -> {
            final CommunityRecommendActivityDto item = new CommunityRecommendActivityDto();
            item.setMainActivityId(activityId);
            Optional<ActivityHighlightsDo> first = highlights.stream()
                    .filter(highlight -> highlight.getActivityId().equals(activityId))
                    .findFirst();
            MainActivity byId = mainActivityService.findById(item.getMainActivityId());
            if (first.isPresent() && Objects.equals(first.get().getState(), 1) && !CollectionUtils.isEmpty(first.get().getCategoryDetails()) && !CollectionUtils.isEmpty(first.get().getCategoryDetails().get(0).getVideos())) {
                ActivityHighlightsCategoryVideoDo videoDo = first.get().getCategoryDetails().get(0).getVideos().get(0);
                item.setActivityCoverImage(videoDo.getCover());
                item.setVideoUrl(videoDo.getVideo());
                item.setDescription(videoDo.getTitle());
                item.setActivityHighlight(true);
            } else {
                item.setActivityHighlight(false);
                //设置图片、标题
                ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(item.getMainActivityId(), languageCode);
                if (Objects.nonNull(disseminate)) {
                    item.setActivityCoverImage(disseminate.getDisseminatePics());
                }
                ActivityRecommendDisseminateQuery query = new ActivityRecommendDisseminateQuery();
                query.setMainActivityId(item.getMainActivityId());
                query.setPosition(ActivityRecommendPositionEnum.COMMUNITY.getPosition());
                query.setLanguageCode(languageCode);
                ActivityRecommendDisseminateDo byQuery = activityRecommendDisseminateService.findByQuery(query);
                if (Objects.nonNull(byQuery) && StringUtils.hasText(byQuery.getDescription())) {
                    item.setDescription(byQuery.getDescription());
                } else {
                    Optional<ActivityParams> defaultOp = activityParamsService.findOneByMainActivityAndParamType(item.getMainActivityId(), ACTIVITY_RECOMMEND_COMMUNITY_DEFAULT_LANGUAGE_CODE);
                    if (defaultOp.isPresent()) {
                        ActivityRecommendDisseminateQuery defaultQuery = new ActivityRecommendDisseminateQuery();
                        defaultQuery.setMainActivityId(item.getMainActivityId());
                        defaultQuery.setPosition(ActivityRecommendPositionEnum.COMMUNITY.getPosition());
                        defaultQuery.setLanguageCode(defaultOp.get().getParamValue());
                        ActivityRecommendDisseminateDo defaultQueryDo = activityRecommendDisseminateService.findByQuery(defaultQuery);
                        if (defaultQueryDo != null) {
                            item.setDescription(defaultQueryDo.getDescription());
                        }
                    }
                }
                item.setMainActivityId(item.getMainActivityId());

            }
            if (DeviceConstant.EquipmentMainTypeEnum.ROWING.getType().equals(byId.getEquipmentMainType())) {
                item.setWatchButton(false);
            } else {
                List<ActivityStage> stages = activityStageService.findByActId(activityId);
                if (CollectionUtils.isEmpty(stages)) {
                    item.setWatchButton(true);
                } else {
                    item.setWatchButton(false);
                    for (ActivityStage stage : stages) {
                        if (stage.getStartTime().toInstant().toEpochMilli() <= ZonedDateTime.now().toInstant().toEpochMilli() && stage.getEndTime().toInstant().toEpochMilli() >= ZonedDateTime.now().toInstant().toEpochMilli()) {
                            item.setWatchButton(true);
                            break;
                        }
                    }
                }
            }
            if (item.isWatchButton()) {
                recommendActivity.stream().filter(a -> a.getId().equals(item.getMainActivityId())).filter(b -> b.getMainType().equals(MainActivityTypeEnum.SERIES_MAIN.getType())).findFirst().ifPresent(c -> {
                    //系列赛使用第一个子赛事的id
                    List<Long> subActivityId = seriesActivityRelService.findSubActivityId(c.getId());
                    if (CollectionUtils.isNotEmpty(subActivityId)) {
                        Optional<Long> first1 = subActivityId.stream().sorted().findFirst();
                        first1.ifPresent(item::setSeriesSubActivityId);
                        fillRouteId(item, mainActivityService.findById(item.getSeriesSubActivityId()));
                    }
                });
                if (MainActivityTypeEnum.SINGLE.getType().equals(byId.getMainType()) || MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(byId.getMainType())) {
                    fillRouteId(item, byId);
                }
            }
            result.add(item);
        });
        return result;
    }

    /**
     * @param item
     * @param byId 主活动 系列赛就是子活动
     */
    private void fillRouteId(CommunityRecommendActivityDto item, MainActivity byId) {
        List<SubActivity> subActivityList = subActivityService.getAllSingleActByMain(byId.getId());
        if (!CollectionUtils.isEmpty(subActivityList)) {
            item.setRouteId(subActivityList.get(0).getRouteId());
            List<SubActivity> allSingleActByMain = subActivityService.getAllSingleActByMain(item.getMainActivityId());
            if (!CollectionUtils.isEmpty(allSingleActByMain)) {
                item.setRoomId(roomIdBizService.getRoomId(byId, subActivityList.stream().map(SubActivity::getTarget).toList()));
            } else {
                item.setRoomId(roomIdBizService.getRoomId(byId, new ArrayList<>()));
            }
        }
    }

    private final ActivityHighlightsService activityHighlightsService;

    /**
     * 获取活动类型规则
     *
     * @param activityId
     * @return
     */
    public ActivityTypeRuleVo getActivityTypeRule(Long activityId) {
        ActivityTypeRuleVo vo = new ActivityTypeRuleVo();
        vo.setActivityId(activityId);
        if (Objects.isNull(activityId)) {
            return vo;
        }

        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.nonNull(mainActivity)) {
            //排位赛进入游戏使用官方多人同跑能力
            if (Objects.equals(mainActivity.getMainType(), "rank")) {
                vo.setActivityType(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType());
            } else if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.PROP.getType())) {
                vo.setActivityType(RunActivityTypeEnum.PROP_ACTIVITY.getType());
            } else {
                vo.setActivityType(mainActivity.getOldType());
            }
            vo.setTargetType(mainActivity.getTargetType());
        } else {
            return vo;
        }
        //获取规则
        EntryGameplay entryGameplay = entryGameplayService.findById(mainActivity.getPlayId());
        if (Objects.nonNull(entryGameplay)) {
            vo.setEntryCount(entryGameplay.getEntryCount());
            vo.setCompetitionFormat(entryGameplay.getCompetitionFormat());
            vo.setIsAllowChanllenge(entryGameplay.getIsAllowChanllenge());
            vo.setRepeatedEntryLimit(entryGameplay.getRepeatedEntryLimit());
            if (StringUtils.hasText(entryGameplay.getRankingBy())) {
                vo.setRankingBy(entryGameplay.getRankingBy().replaceAll("0", ""));
            }
        }
        //是否有奖励
        List<ActivityAwardConfigDto> activityAwardConfigDtos = awardActivityBizService.queryActivityAwardByActivityId(new AwardQuery(activityId));
        if (!org.springframework.util.CollectionUtils.isEmpty(activityAwardConfigDtos)) {
            vo.setHasAward(1);
        } else {
            vo.setHasAward(0);
        }

        if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
            //定位到主活动
            MainActivity parentActivity = seriesActivityRelService.getMainActivityBySegmentActId(mainActivity.getId());
            Integer target = 0;

            Integer roomNumber = roomIdBizService.getRoomId(parentActivity, Arrays.asList(target));
            vo.setRealRoomId(Long.valueOf(roomNumber));

            if (parentActivity.getWaitTime() == 0) {
                vo.setActivityType(null);
            }
        } else if (MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
            //新PK活动处理
            if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(mainActivity.getOldType())) {
                if (vo.getTargetType() == 1) {
                    vo.setRankingBy("2");
                } else {
                    vo.setRankingBy("1");
                }
                vo.setIsAllowChanllenge(0);
            }
        }

        return vo;
    }

    @Cacheable(value = "Activity:findFreeChallengeActivityList", key = "#activity.id")
    public List<FreeChallengeActivityResponse> findFreeChallengeActivityList(MainActivity activity) {
        Long activityId = activity.getId();
        // 判断是否主榜
        if (!MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(activity.getMainType())) {
            MainActivity mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(activityId);
            if (mainActivity != null) {
                activityId = mainActivity.getId();
            }
        }
        List<FreeChallengeActivityResponse> result = new ArrayList<>();
        FreeChallengeActivityResponse itemOnline = new FreeChallengeActivityResponse();
        itemOnline.setActivityId(activityId);
        itemOnline.setDeviceLocation("ALL");
        result.add(itemOnline);
        // 子活动查询,当前只有一个子活动，固定为la
        List<MainActivity> subActivityList = seriesActivityRelService.getAllMainActivity(activityId);
        subActivityList.forEach(item -> {
            FreeChallengeActivityResponse itemSub = new FreeChallengeActivityResponse();
            itemSub.setActivityId(item.getId());
            itemSub.setDeviceLocation("LA");
            result.add(itemSub);
        });
        return result;
    }
}
