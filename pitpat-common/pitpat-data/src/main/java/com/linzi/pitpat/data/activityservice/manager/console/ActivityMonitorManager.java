package com.linzi.pitpat.data.activityservice.manager.console;

import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.dto.api.request.RamMonitorRequest;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRamLogDo;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRamRecordsDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityRamRecordsQuery;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityRamAlarmConfigVo;
import com.linzi.pitpat.data.activityservice.service.ActivityRamLogService;
import com.linzi.pitpat.data.activityservice.service.ActivityRamRecordsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Service
@Slf4j
public class ActivityMonitorManager {

    private final ActivityRamRecordsService activityRamRecordService;

    private final ActivityRamLogService activityRamLogService;

    private final RedissonClient redissonClient;

    private final ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    private final MainActivityBizService mainActivityBizService;
    private final ISysConfigService sysConfigService;

    /**
     * 保存数据，并更新统计
     *
     * @param request
     */
    public void saveRecords(RamMonitorRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        String lockKey = "RAM_LOCK:" + request.getRunDetailId();
        RLock lock = redissonClient.getLock(lockKey);
        LockHolder.tryLock(lock, 5, () -> {
            ActivityRamRecordsDo record = activityRamRecordService.findOne(
                            new ActivityRamRecordsQuery().setRunDetailId(request.getRunDetailId()))
                    .orElseGet(() -> {
                        ZnsUserRunDataDetailsEntity runDataDetails = znsUserRunDataDetailsService.findById(request.getRunDetailId());
                        if (Objects.isNull(runDataDetails)) {
                            log.info("[ramRecord],无法找到跑步记录：{}", request.getRunDetailId());
                            return null;
                        }
                        Optional<MainActivity> mainActivity = mainActivityBizService.getMainActivity(runDataDetails.getActivityId());
                        if (mainActivity.isEmpty()) {
                            log.info("[ramRecord],无法找到主活动：{},{}", request.getRunDetailId(), runDataDetails.getActivityId());
                            return null;
                        }
                        //初始化
                        return createActivityRamRecord(request, mainActivity);
                    });
            if (Objects.isNull(record)) {
                log.info("[ramRecord], record 无法建立");
                return;
            }
            //存储行数据,并统计最大最小
            if (CollectionUtils.isNotEmpty(request.getRamLogItems())) {

                Optional<RamMonitorRequest.RamLogItem> minRamOptional = request.getRamLogItems().stream().min(Comparator.comparing(RamMonitorRequest.RamLogItem::getRamUsage));
                Optional<RamMonitorRequest.RamLogItem> maxRamOptional = request.getRamLogItems().stream().max(Comparator.comparing(RamMonitorRequest.RamLogItem::getRamUsage));

                Optional<RamMonitorRequest.RamLogItem> minCpuOptional = request.getRamLogItems().stream().min(Comparator.comparing(RamMonitorRequest.RamLogItem::getCpuUsagePercentage));
                Optional<RamMonitorRequest.RamLogItem> maxCpuOptional = request.getRamLogItems().stream().max(Comparator.comparing(RamMonitorRequest.RamLogItem::getCpuUsagePercentage));

                Optional<RamMonitorRequest.RamLogItem> minPitpatRamOptional = request.getRamLogItems().stream()
                        .filter(a -> Objects.nonNull(a.getPitpatRamPercentage()))
                        .min(Comparator.comparing(RamMonitorRequest.RamLogItem::getPitpatRamPercentage));
                Optional<RamMonitorRequest.RamLogItem> maxPitpatRamOptional = request.getRamLogItems().stream()
                        .filter(a -> Objects.nonNull(a.getPitpatRamPercentage()))
                        .max(Comparator.comparing(RamMonitorRequest.RamLogItem::getPitpatRamPercentage));
                log.info("maxRam={}, pitpatMaxRam={}", maxRamOptional, maxPitpatRamOptional);
                //过滤掉无意义数据，数据库膨胀太快
                List<ActivityRamLogDo> logs = Stream.of(minRamOptional, maxRamOptional, minCpuOptional, maxCpuOptional, minPitpatRamOptional, maxPitpatRamOptional).filter(Optional::isPresent).map(Optional::get).map(item -> {
                            ActivityRamLogDo logItem = new ActivityRamLogDo();
                            logItem.setRecordId(record.getId());
                            logItem.setRunDetailId(record.getRunDetailId());
                            logItem.setUserId(record.getUserId());
                            logItem.setTime(item.getTime());
                            logItem.setRamUsage(item.getRamUsage());
                            logItem.setRamUsagePercentage(item.getRamUsagePercentage());
                            logItem.setCpuUsagePercentage(item.getCpuUsagePercentage());
                            logItem.setPitpatRamPercentage(item.getPitpatRamPercentage());

                            if (logItem.getCpuUsagePercentage().compareTo(record.getCpuUsageMax()) > 0) {
                                record.setCpuUsageMax(logItem.getCpuUsagePercentage());
                            }
                            if (logItem.getCpuUsagePercentage().compareTo(record.getCpuUsageMin()) < 0) {
                                record.setCpuUsageMin(logItem.getCpuUsagePercentage());
                            }
                            if (logItem.getRamUsagePercentage().compareTo(record.getRamUsageMax()) > 0) {
                                record.setRamUsageMax(logItem.getRamUsagePercentage());
                            }
                            if (logItem.getRamUsagePercentage().compareTo(record.getRamUsageMin()) < 0) {
                                record.setRamUsageMin(logItem.getRamUsagePercentage());
                            }
                            if (Objects.nonNull(logItem.getPitpatRamPercentage())) {
                                if (Objects.isNull(record.getPitpatRamUsageMin()) || logItem.getPitpatRamPercentage().compareTo(record.getPitpatRamUsageMin()) < 0) {
                                    record.setPitpatRamUsageMin(logItem.getPitpatRamPercentage());
                                }
                                if (Objects.isNull(record.getPitpatRamUsageMax()) || logItem.getPitpatRamPercentage().compareTo(record.getPitpatRamUsageMax()) > 0) {
                                    record.setPitpatRamUsageMax(logItem.getPitpatRamPercentage());
                                }
                            }
                            if (logItem.getTime() < record.getFirstFetchTime().toEpochSecond()) {
                                record.setFirstFetchTime(ZonedDateTime.ofInstant(Instant.ofEpochSecond(logItem.getTime()), ZoneId.systemDefault()));
                            }
                            return logItem;
                        }
                ).toList();
                List<ActivityRamLogDo> sortLogs = logs.stream().sorted(Comparator.comparing(ActivityRamLogDo::getTime)).toList();
                activityRamLogService.batchCreate(sortLogs);
                if (request.getNormalExit() == 1) {
                    record.setNormalExit(1);
                }
                activityRamRecordService.update(record);

                // 内存告警逻辑：检查非iPhone设备且内存使用率>85%的情况
                checkRamAlert(request);
            }
        });
    }

    @NotNull
    private ActivityRamRecordsDo createActivityRamRecord(RamMonitorRequest request, Optional<MainActivity> mainActivity) {
        ActivityRamRecordsDo activityRamRecordsDo = new ActivityRamRecordsDo();
        activityRamRecordsDo.setUserId(request.getUserId());
        activityRamRecordsDo.setMainActivityId(mainActivity.get().getId());
        activityRamRecordsDo.setRunDetailId(request.getRunDetailId());
        activityRamRecordsDo.setDeviceModel(request.getDeviceModel());
        activityRamRecordsDo.setDeviceRam(request.getDeviceRam());
        activityRamRecordsDo.setRamUsageMin(BigDecimal.ZERO);
        activityRamRecordsDo.setRamUsageMax(BigDecimal.ZERO);
        activityRamRecordsDo.setPitpatRamUsageMin(BigDecimal.ZERO);
        activityRamRecordsDo.setPitpatRamUsageMax(BigDecimal.ZERO);
        activityRamRecordsDo.setDeviceCpu(request.getDeviceCpu());
        activityRamRecordsDo.setCpuUsageMin(BigDecimal.ZERO);
        activityRamRecordsDo.setCpuUsageMax(BigDecimal.ZERO);
        activityRamRecordsDo.setBluetoothVersion(request.getBluetoothVersion());
        activityRamRecordsDo.setNormalExit(request.getNormalExit());
        activityRamRecordsDo.setFirstFetchTime(ZonedDateTime.now());
        activityRamRecordService.create(activityRamRecordsDo);
        return activityRamRecordsDo;
    }

    /**
     * 检查内存告警逻辑
     * 优化版本：使用配置化的短期缓存计数，达到阈值后转移到长期缓存存储详细信息
     *
     * @param request 内存监控请求
     */
    private void checkRamAlert(RamMonitorRequest request) {

        // 检查设备类型是否应该被排除
        if (Objects.isNull(request.getDeviceModel())) {
            return;
        }
        String osName = getOsName(request.getDeviceModel());

        String json = sysConfigService.selectConfigByKey("ramUsageThreshold");
        if (Objects.isNull(json)) {
            log.error("未找到内存监控配置，key={}", "ramUsageThreshold");
            return;
        }
        Map<String, ActivityRamAlarmConfigVo> ramUsageThreshold = JsonUtil.readValue(json, new TypeReference<>() {
        });


        // 获取超过内存请求比例的次数
        ActivityRamAlarmConfigVo ramAlarmConfig = ramUsageThreshold.get(osName);
        log.info("key={}, json={}, config={}", "ramUsageThreshold", json, ramAlarmConfig);

        if (Objects.equals(osName, "iOS")) {
            //ios 只判断pitpatRam
            long pitpatRamCount = request.getRamLogItems().stream()
                    .map(RamMonitorRequest.RamLogItem::getPitpatRamPercentage)
                    .filter(Objects::nonNull)
                    .filter(item -> item.compareTo(ramAlarmConfig.getRamUsagePercentThreshold()) >= 0)
                    .count();
            log.info("ios maxRamUsage threshold count= {}, userid={}", pitpatRamCount, request.getUserId());

            if (pitpatRamCount > 5 && request.getUserId() != null) {
                processRamAlert(request.getUserId(), request.getDeviceModel());
            }
        } else {
            //android
            long pitpatRamCount = request.getRamLogItems().stream()
                    .filter(ramLogItem -> Objects.nonNull(ramLogItem.getPitpatRamPercentage()))
                    .filter(item -> {
                        //ram超过比例
                        if (item.getRamUsagePercentage().compareTo(ramAlarmConfig.getRamUsagePercentThreshold()) >= 0) {
                            //Pitpat ram 超过比例
                            if (item.getPitpatRamPercentage().compareTo(ramAlarmConfig.getPitpatRamUsagePercentThreshold()) >= 0) {
                                //该比例超过最小内存大小
                                return item.getPitpatRamPercentage().divide(BigDecimal.valueOf(100), RoundingMode.FLOOR).multiply(request.getDeviceRam()).compareTo(ramAlarmConfig.getPitpatRamUsageThreshold()) >= 0;
                            }
                        }
                        return false;
                    })
                    .count();
            log.info("android maxRamUsage threshold count= {}, userid={}", pitpatRamCount, request.getUserId());

            //ios： 单次有5次超过指定内存比例
            if (pitpatRamCount > 5 && request.getUserId() != null) {
                processRamAlert(request.getUserId(), request.getDeviceModel());
            }
        }
    }

    private static String getOsName(String deviceModel) {
        if (deviceModel.contains("iPhone") || deviceModel.contains("iPad")) {
            return "iOS";
        }
        return "android";
    }

    /**
     * 处理内存告警
     * 简化版本：仅使用计数器，超过3次后每次满足条件则更新处罚次数
     *
     * @param userId      用户ID
     * @param deviceModel 设备型号
     */
    private void processRamAlert(Long userId, String deviceModel) {
        String countKey = RedisConstants.RAM_ALERT_COUNT_KEY + userId;

        // 获取或创建计数器
        RAtomicLong counter = redissonClient.getAtomicLong(countKey);
        long currentCount = counter.incrementAndGet();

        counter.expire(30, TimeUnit.MINUTES);

        log.info("[RAM_ALERT] 用户 {} 设备 {}当前计数: {}", userId, deviceModel, currentCount);

        // 如果达到或超过阈值，记录告警
        if (currentCount >= 3) {
            // 使用配置的过期时间，确保能够持续计数
            counter.expire(24, TimeUnit.HOURS);
            log.warn("[RAM_ALERT] 用户 {} 设备 {} 内存使用率超过90%，累计 {} 次",
                    userId, deviceModel, currentCount);
        }
    }

}
