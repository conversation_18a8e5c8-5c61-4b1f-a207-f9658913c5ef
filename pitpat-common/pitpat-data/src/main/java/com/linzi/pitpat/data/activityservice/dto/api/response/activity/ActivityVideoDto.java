package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
public class ActivityVideoDto {
    //运动id
    private Long detailId;
    // 视频上传时间
    private ZonedDateTime completeTime;
    private BigDecimal runMileage;
    private Integer runTime;
    // 审核状态  pass：通过  refuse :未通过  wait：待处理  tobeUpload：待上传
    private String viewStatus;
    //阶段
    private Integer level;
    // 拒绝理由
    private String refuseContent;

}
