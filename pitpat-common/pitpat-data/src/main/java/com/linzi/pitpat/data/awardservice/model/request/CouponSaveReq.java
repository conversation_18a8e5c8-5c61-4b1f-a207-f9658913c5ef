package com.linzi.pitpat.data.awardservice.model.request;


import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.dto.CouponI8nDto;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.request.CouponCurrencyRequestDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 新增修改优惠券请求参数
 */
@Data
@NoArgsConstructor
public class CouponSaveReq {

    /**
     * 主键id，修改时必传
     */
    private Long id;

    /**
     * 券优惠类型【1：参赛必胜券，2：奖励翻倍券，3：幸运现金券，4：亚马逊优惠券,5 抵扣券,6 进阶里程碑券,7独立站抵扣券，100：商城优惠券】
     *
     * @see CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 券来源类型【1：刮刮卡券,2 排行榜,3活动获取,4 后台发放,5 积分兑换，100：商城 】
     *
     * @see CouponConstant.TypeEnum
     */
    private Integer type;

    /**
     * 使用范围：1：全场通用 2：部分活动/商品，3：单品券(指定商品)
     *
     * @see CouponConstant.UseScopeEnum
     */
    private Integer useScope;

    /**
     * 赛事券备注/商城券名称，运营自己看
     */
    private String name;

    //优惠券发放总数(库存) -1:不限制
    private Integer quota;
    //每个人限制领取张数，-1不限制
    private Integer limitCount;

    //兑换码
    private String exchangeCode;

    //券金额 couponType = 3,5
    private BigDecimal amount;

    //券折扣 couponType = 4
    private BigDecimal discount;

    //使用规则
    private String description;


    /**
     * 有效期类型【1:days固定天数，2:range固定时间范围】
     *
     * @see CouponConstant.ExpiryTypeEnum
     */
    private Integer expiryType;
    //有效期天数 expiryType = 1
    private Integer validDays;
    //有效期开始时间 expiryType = 2
    private ZonedDateTime gmtStart;
    //有效期结束时间 expiryType = 2
    private ZonedDateTime gmtEnd;

    //1：已发布 -1：已失效
    private Integer status;
    //图片
    private String picture;
    //卷图片url 缩略
    private String minPicUrl;
    //路由表id(跳转路径)
    private Long routeId;
    //赛事分类(0:不是，1：是)
    private Integer isActivityCategory;
    // 使用范围 list ids
    private List<Long> activityIds;
    // 使用范围 赛事类别
    private Integer activityType;
    // 使用范围 聚合活动类型 list ids
    private List<Long> taskIds;

    //过期提醒配置 0 默认 1 自定义
    private Integer expirationRemindType;
    //过期提醒天数配置
    private Integer expirationRemindDay;
    //过期提醒时间配置
    private String expirationRemindTime;
    //提醒方式 0 push 1 im 2 im + push
    private Integer expirationRemindMethod;

    //【废弃，放到国际化了】标题(卷名称)
    @Deprecated
    private String title;

    //【废弃，放到国际化了】可用描述(卷说明)
    @Deprecated
    private String canUseDescription;

    /**
     * 优惠券多币种参数
     */
    private List<CouponCurrencyRequestDto> currencyList;
    //操作人
    private String operateName;


    // 默认语言code
    private String defaultLangCode;
    // 时间类型：1:同一时刻；2:各时区分别推送
    private Integer timeType;
    // 国际化数据
    private List<CouponI8nDto> values;

    /**
     * 使用范围 3.0赛事类别 排除old 类型
     *
     * @see MainActivityTypeEnum
     */
    private String newActivityType;

    /**
     * 使用范围 3.0赛事id/聚合id
     */
    private List<Long> newActivityId;

    /**
     * 【4.4.3新增】优惠方式，1：金额，2：折扣
     *
     * @see CouponConstant.DiscountMethodEnum
     */
    private Integer discountMethod;

    /**
     * 【4.4.3新增】使用最小总金额
     */
    private BigDecimal minTotalAmount;

    /**
     * 【4.4.3新增】商城优惠券是否叠加其他营销，0：不叠加，1：叠加
     *
     * @see CouponConstant.AddMarketEnum
     */
    private Integer mallAddMarket;

    /**
     * 【4.4.3新增】使用范围 goodsId
     */
    private List<Long> goodsIds;

    /**
     * 【4.4.3新增】商城优惠券是否在详情页展，0：不展示，1：展示
     *
     * @see CouponConstant.ShowDetailEnum
     */
    private Integer mallShowDetail;

    /**
     * 【4.4.3新增】领取开始时间
     */
    private ZonedDateTime receiveStart;

    /**
     * 【4.4.3新增】领取结束时间
     */
    private ZonedDateTime receiveEnd;

    /**
     * 国家码列表
     * @since 4.6.4
     */
    private List<String> countryCodeList;

}
