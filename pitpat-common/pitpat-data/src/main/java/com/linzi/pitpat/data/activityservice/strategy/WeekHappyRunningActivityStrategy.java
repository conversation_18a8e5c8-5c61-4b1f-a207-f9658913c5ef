package com.linzi.pitpat.data.activityservice.strategy;

import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/23 14:44
 */
@Service
@Slf4j
public class WeekHappyRunningActivityStrategy extends BaseActivityStrategy {

    @Override
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        super.handleRunActivityEnd(activityEntity);
    }

    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {

    }

    @Override
    protected Result checkRunActivityTime(ZnsRunActivityEntity activityEntity) {
        //活动结束后不可操作
        if (Objects.nonNull(activityEntity.getActivityEndTime()) && ZonedDateTime.now().compareTo(activityEntity.getActivityEndTime()) > 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "The activity has ended and cannot be attended");
        }
        return null;
    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {
        activityUserBizService.addOfficialActivityUser(activityEntity, user.getId(), runningGoals, taskId, request.getSource(), null, false);
        return CommonResult.success();
    }

    @Override
    public Result canRefuseOrAcceptActivity(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return null;
    }

    @Override
    public Result checkReportUserRun(ZnsRunActivityEntity activityEntity, ZnsUserEntity user) {
        return CommonResult.success();
    }

    @Override
    public Long handleRunAward(BigDecimal awardAmount, ZnsRunActivityUserEntity activityUser, ZnsRunActivityEntity activityEntity, BigDecimal extraAward, AccountDetailSubtypeEnum subType) {
        return null;
    }

    @Override
    public ActivityRunningReportBaseVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        return null;
    }

    public void runDataEnd(ZnsRunActivityEntity activityEntity, ZnsUserRunDataDetailsEntity detailsEntity) {
        //查询任务
        List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(activityEntity.getId(), detailsEntity.getUserId(), null);
        if (CollectionUtils.isEmpty(runActivityUserTasks)) {
            return;
        }
        if (ZonedDateTime.now().compareTo(activityEntity.getActivityEndTime()) > 0) {
            return;
        }
        //查询活动用户
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), detailsEntity.getUserId());
        if (Objects.isNull(activityUser)) {
            return;
        }

        activityUser.setRunMileage(activityUser.getRunMileage().add(detailsEntity.getRunMileage()));
        activityUser.setRunTime(activityUser.getRunTime() + detailsEntity.getRunTime());
        runActivityUserService.updateById(activityUser);

        int mileage = detailsEntity.getRunMileage().intValue();
        for (int i = 0; i < runActivityUserTasks.size(); i++) {
            RunActivityUserTask task = runActivityUserTasks.get(i);
            if (mileage <= 0) {
                return;
            }
            if (i == runActivityUserTasks.size() - 1 || task.getId().equals(detailsEntity.getTaskId())) {
                int runMileage = task.getRunMileage() + mileage;
                task.setRunMileage(runMileage);
                task.setIsUnlock(1);
                task.setTaskTime(ZonedDateTime.now());
                if (runMileage >= task.getMileageTarget()) {
                    task.setStatus(1);
                }
                runActivityUserTaskService.update(task);
                return;
            }

            if (task.getStatus() == 1) {
                continue;
            }
            int runMileage = task.getRunMileage() + mileage;
            if (runMileage >= task.getMileageTarget()) {
                task.setStatus(1);
                int surplusMil = task.getMileageTarget() - task.getRunMileage();
                mileage = mileage - surplusMil;
                task.setRunMileage(task.getMileageTarget());
            } else {
                mileage = 0;
                task.setRunMileage(runMileage);
            }
            task.setIsUnlock(1);
            task.setTaskTime(ZonedDateTime.now());
            runActivityUserTaskService.update(task);
        }
    }
}
