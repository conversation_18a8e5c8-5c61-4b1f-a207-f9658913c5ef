package com.linzi.pitpat.data.activityservice.dto.api.response;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.filler.activity.VipUserSavedAmountDataFiller;
import com.linzi.pitpat.data.filler.base.Filler;
import com.linzi.pitpat.data.filler.base.InnerFiller;
import com.linzi.pitpat.data.filler.base.group.GroupRelationBind;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;

@Data
public class BatchSignUpActivityResponseDto {
    @InnerFiller
    private List<BatchSignUpActivityItemResponseDto> running;
    @InnerFiller
    private List<BatchSignUpActivityItemResponseDto> cycling;
    @InnerFiller
    private List<BatchSignUpActivityItemResponseDto> rowing;


    private Long userId;

    /**
     * 会员类型：0:非付费用户，1：付费会员
     */
    private Integer memberType;
    /**
     * 会员过期时间
     */
    private ZonedDateTime memberExpireTime;

    //是否已免费试用过
    private Boolean isFreeUsedForMonth = false;


    /**
     * 已节约的费用
     */
    @Filler(filler = VipUserSavedAmountDataFiller.class, relationFieldName = "saveAmount", groupMode = true, groupRelationBind = {
            @GroupRelationBind(relationField = BatchSignUpActivityItemResponseDto.Fields.userId, dataFieldName = VipUserSavedAmountDataFiller.Load.Fields.userId),
    }, order = 2, fetchFiledName = VipUserSavedAmountDataFiller.ItemData.Fields.feeAmount)
    private BigDecimal savedAmount = new BigDecimal(0);
    /**
     * 已节约的积分
     */
    @Filler(filler = VipUserSavedAmountDataFiller.class, relationFieldName = "saveAmount", groupMode = true,
            fetchFiledName = VipUserSavedAmountDataFiller.ItemData.Fields.feeScore)
    private Integer savedScore = 0;
    /**
     * 是否使用过批量报名
     */
    @Filler(filler = VipUserSavedAmountDataFiller.class, relationFieldName = "saveAmount", groupMode = true,
            fetchFiledName = VipUserSavedAmountDataFiller.ItemData.Fields.usedBatchMode)
    private Integer usedBatchMode;

    private Currency currency = I18nConstant.CurrencyCodeEnum.USD.getCurrency();

    public void addActivity(MainActivity activityDto, String languageCode, String zoneId, Long userId) {
        if (running == null) {
            this.running = new ArrayList<>();
        }
        if (cycling == null) {
            this.cycling = new ArrayList<>();
        }
        if (rowing == null) {
            this.rowing = new ArrayList<>();
        }
        BatchSignUpActivityItemResponseDto item = new BatchSignUpActivityItemResponseDto();
        item.setLanguageCode(languageCode);
        item.setZoneId(zoneId);
        item.setUserId(userId);
        item.setMainActivityId(activityDto.getId());
        if (DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType().equals(activityDto.getEquipmentMainType())) {
            running.add(item);
        } else if (DeviceConstant.EquipmentMainTypeEnum.BIKE.getType().equals(activityDto.getEquipmentMainType())) {
            this.cycling.add(item);
        } else if (DeviceConstant.EquipmentMainTypeEnum.ROWING.getType().equals(activityDto.getEquipmentMainType())) {
            this.rowing.add(item);
        }
    }
}
