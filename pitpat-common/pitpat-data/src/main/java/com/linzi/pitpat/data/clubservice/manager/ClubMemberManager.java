package com.linzi.pitpat.data.clubservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubLevelBenefitEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubManagerInviteEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberApplyStateEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberRoleEnum;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubStateEnum;
import com.linzi.pitpat.data.clubservice.convert.ClubMemberConvert;
import com.linzi.pitpat.data.clubservice.model.ClubDisbandEvent;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.model.entity.ClubLevelBenefitDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubLevelConfig;
import com.linzi.pitpat.data.clubservice.model.entity.ClubManagerApplyDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMember;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMemberApply;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskDo;
import com.linzi.pitpat.data.clubservice.model.entity.NewUserClubTaskRecordDo;
import com.linzi.pitpat.data.clubservice.model.query.ClubLevelBenefitQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubManagerApplyQuery;
import com.linzi.pitpat.data.clubservice.model.query.NewUserClubTaskQuery;
import com.linzi.pitpat.data.clubservice.model.request.AppClubMemberCheckUserClubReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberApplyAuditReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberApplyReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberExitReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberListReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberRemoveReqDto;
import com.linzi.pitpat.data.clubservice.model.request.ManagerInviteReqDto;
import com.linzi.pitpat.data.clubservice.model.response.AppClubMemberApplyListRespDto;
import com.linzi.pitpat.data.clubservice.model.response.AppClubMemberInviteActivityListRespDto;
import com.linzi.pitpat.data.clubservice.model.response.AppClubMemberListRespDto;
import com.linzi.pitpat.data.clubservice.model.response.ClubManagerInviteInfoRespDto;
import com.linzi.pitpat.data.clubservice.model.response.InvitedPersonInfoRespDto;
import com.linzi.pitpat.data.clubservice.service.ClubActivityInviteService;
import com.linzi.pitpat.data.clubservice.service.ClubImService;
import com.linzi.pitpat.data.clubservice.service.ClubLevelBenefitService;
import com.linzi.pitpat.data.clubservice.service.ClubLevelConfigService;
import com.linzi.pitpat.data.clubservice.service.ClubManagerApplyService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberApplyService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskRecordService;
import com.linzi.pitpat.data.clubservice.service.NewUserClubTaskService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.filler.base.FillContext;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.UserExtraParamsService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.util.page.PageConvert;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.exception.ExceptionFactory;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClubMemberManager {
    /**
     * 审批过期时间 7天
     */
    public static final int APPLY_EXPIRY_TIME_DAYS = 7;
    /**
     * IM 发送的信息过期天数
     */
    public static final int IM_INVITE_EXPIRY_TIME_DAYS = 7;

    private final ClubMemberApplyService clubMemberApplyService;

    private final ClubService clubService;

    private final ClubMemberService clubMemberService;

    private final ClubLevelConfigService clubLevelConfigService;

    private final TransactionTemplate transactionTemplate;

    private final ZnsUserService znsUserService;

    private final ClubImService clubImService;

    private final RedissonClient redissonClient;

    private final ClubActivityInviteService clubActivityInviteService;

    private final ZnsRunActivityUserService runActivityUserService;

    private final TencentImUtil tencentImUtil;

    private final QueueMessageService queueMessageService;
    private final ClubManagerApplyService clubManagerApplyService;
    private final NewUserClubTaskService newUserClubTaskService;
    private final NewUserClubTaskRecordService newUserClubTaskRecordService;
    private final UserLevelService userLevelService;

    @Value("${spring.profiles.active}")
    private String envProfile;
    private final UserTaskBizService userTaskBizService;

    private final ClubManager clubManager;
    private final ClubLevelBenefitService clubLevelBenefitService;

    private final UserExtraParamsService userExtraParamsService;

    /**
     * 提交加入俱乐部审批
     *
     * @param req
     * @return true 立即加入俱乐部成功 false 审批已经成功提交，还未加入俱乐部
     */
    public Boolean submitJoinApply(ClubMemberApplyReqDto req) {
        Club byId = clubService.findById(req.getClubId());
        if (!ClubStateEnum.NORMAL.getCode().equals(byId.getState())) {
            throw ExceptionFactory.biz118nException("club.apply.submit.state_fail");
        }
        if (byId.getRequiredInviteCode() && !byId.getInviteCode().equals(req.getApplyCode())) {
            throw new BizI18nException("club.apply.submit.invite_code_fail");
        }
        //邀请码不必填，填了但是不对，设置为空
        if (!byId.getRequiredInviteCode() && !byId.getInviteCode().equals(req.getApplyCode())) {
            req.setApplyCode("");
        }
        ClubLevelConfig clubLevelConfig = clubLevelConfigService.findByClubCode(byId.getClubLevel()).get();
        if (clubLevelConfig.getMemberUpperLimit() <= byId.getMemberCount()) {
            throw new BizI18nException("club.apply.submit.member_limit");
        }
        if (clubMemberService.findByClubAndUserId(req.getClubId(), req.getUserId()).isPresent()) {
            throw new BizI18nException("club.apply.submit.member_exist");
        }
        if (clubMemberApplyService.findWaitAuditByClubAndUserId(req.getClubId(), req.getUserId()).isPresent()) {
            throw new BizI18nException("club.apply.submit.member_exist");
        }
        //是否成功加入俱乐部
        AtomicReference<Boolean> joinState = new AtomicReference<>(false);
        transactionTemplate.executeWithoutResult(status -> {
            ClubMemberApply apply = new ClubMemberApply();
            apply.setApplyCode(req.getApplyCode());
            apply.setApplyDate(ZonedDateTime.now());
            apply.setClubId(req.getClubId());
            apply.setApplyUserId(req.getUserId());
            //默认7天过期
            apply.setExpiryDate(DateUtil.addDays(ZonedDateTime.now(), APPLY_EXPIRY_TIME_DAYS));
            apply.setApplyReason(req.getApplyReason());
            apply.setState(byId.getRequiresApproval() ?
                    ClubMemberApplyStateEnum.APPLYING.getCode() :
                    ClubMemberApplyStateEnum.JOINED.getCode());
            clubMemberApplyService.insert(apply);
            if (ClubMemberApplyStateEnum.JOINED.getCode().equals(apply.getState())) {
                //不需要审核，直接加入
                addMember(req.getClubId(), req.getUserId());
                joinState.set(true);
            }
        });
        return joinState.get();

    }

    public Boolean auditApply(ClubMemberApplyAuditReqDto req, Long userId) {
        ClubMemberApply clubMemberApply = clubMemberApplyService.findById(req.getApplyId());
        if (!ClubMemberApplyStateEnum.APPLYING.getCode().equals(clubMemberApply.getState())) {
            throw new BizI18nException("club.op.fail");
        }
        Club club = clubService.findById(clubMemberApply.getClubId());

        if (club == null) {
            throw new BizI18nException("club.op.fail");
        }
        if (!club.getOwnerUserId().equals(userId)) {
            throw new BizI18nException("club.op.fail");
        }
        if (1 == req.getAuditStatus()) {
            if (!ClubStateEnum.NORMAL.getCode().equals(club.getState())) {
                throw new BizI18nException("club.apply.club_state_freeze");
            }
            transactionTemplate.executeWithoutResult(status -> {
                clubMemberApplyService.joinApply(clubMemberApply.getId(), userId);
                addMember(clubMemberApply.getClubId(), clubMemberApply.getApplyUserId());

            });
            clubImService.sendImApplyAuditApprove(club, clubMemberApply.getApplyUserId());
        } else {
            clubMemberApplyService.rejectApply(clubMemberApply.getId(), userId);
            clubImService.sendImApplyAuditReject(club, clubMemberApply.getApplyUserId());

        }
        return true;
    }


    public Boolean removeMembers(ClubMemberRemoveReqDto req) {
        boolean isManager = false;
        Optional<ClubMember> byClubAndUserId = clubMemberService.findByClubAndUserId(req.getClubId(), req.getUserId());
        if (byClubAndUserId.isPresent() && ClubMemberRoleEnum.MANAGER.getCode().equals(byClubAndUserId.get().getRole())) {
            isManager = true;
        }
        //移除人群中是否有管理员
        List<ClubMember> clubMembers = clubMemberService.findByMemberIdsAndClubId(req.getClubMemberIds(), req.getClubId());
        if (CollectionUtils.isEmpty(clubMembers) || req.getClubMemberIds().size() > clubMembers.size()) {
            throw new BaseException(I18nMsgUtils.getMessage("club.remove.member.error.notice"));
        }
        boolean hasManager = clubMembers.stream().anyMatch(d -> ClubMemberRoleEnum.MANAGER.getCode().equals(d.getRole()));
        if ((!clubService.checkIsOwner(req.getClubId(), req.getUserId()) && !isManager)
                || req.getClubMemberIds().contains(req.getUserId())) {
            throw new BizI18nException("club.op.fail");
        }
        //管理员不能移除管理员
        if (isManager && hasManager) {
            throw new BizI18nException("club.op.fail");
        }
        Club byId = clubService.findById(req.getClubId());
        subMember(req.getClubId(), req.getClubMemberIds(), false);

        clubImService.sendImMessageRemoveClubMember(byId, req.getClubMemberIds());
        // 删除加入的俱乐部标志
        req.getClubMemberIds().forEach(x -> userExtraParamsService.deleteByUserIdAndParamValue(x, byId.getId().toString()));
        return true;
    }


    /**
     * 获取俱乐部待审核的列表
     *
     * @param clubId
     * @return
     */
    @FillerMethod
    public Page<AppClubMemberApplyListRespDto> queryWaitAuditApply(Integer pageNum, Integer pageSize, Long clubId, Long userId) {
        Club club = clubService.findById(clubId);
        if (club == null || !club.getOwnerUserId().equals(userId)) {
            return new Page<>();
        }
        Page<ClubMemberApply> pageWaitAuditByClubId = clubMemberApplyService.findPageWaitAuditByClubId(pageNum, pageSize, clubId);
        if (pageWaitAuditByClubId.getRecords().isEmpty()) {
            return new Page<>();
        }
        List<Long> list = pageWaitAuditByClubId.getRecords().stream().map(ClubMemberApply::getApplyUserId).toList();
        List<ZnsUserEntity> userInfo = znsUserService.findByIds(list);
        FillContext.setUserId(userId);
        return PageConvert.dataConvert(pageWaitAuditByClubId, item -> {
            AppClubMemberApplyListRespDto resp = new AppClubMemberApplyListRespDto();
            resp.setApplyId(item.getId());
            resp.setApplyUserId(item.getApplyUserId());
            userInfo.stream().filter(d -> d.getId().equals(item.getApplyUserId())).findFirst().ifPresent(d -> {
                resp.setNickname(d.getFirstName());
                resp.setHeadPortrait(d.getHeadPortrait());
            });
            resp.setApplyReason(item.getApplyReason());
            resp.setApplyCode(item.getApplyCode());
            resp.setState(item.getState());
            resp.setExpiryDate(item.getExpiryDate());
            return resp;
        });

    }

    /**
     * 自己退出俱乐部
     *
     * @param req
     * @return
     */
    public Boolean exitClub(ClubMemberExitReqDto req) {
        Club club = clubService.findById(req.getClubId());
        if (club.getOwnerUserId().equals(req.getUserId())) {
            throw new BizI18nException("club.op.fail");
        }
        ZnsUserEntity user = znsUserService.findById(req.getUserId());
        if (user == null) {
            throw new BizI18nException("club.op.fail");
        }
        Optional<ClubMember> clubMemberOp = clubMemberService.findByClubAndUserId(club.getId(), req.getUserId());
        if (clubMemberOp.isEmpty()) {
            throw new BizI18nException("club.op.fail");
        }
        ArrayList<Long> clubMemberIds = Lists.newArrayList();
        clubMemberIds.add(clubMemberOp.get().getUserId());
        subMember(req.getClubId(), clubMemberIds, true);
        clubImService.sendImMessageExitClub(club, user);
        // 删除加入的俱乐部标志
        userExtraParamsService.deleteByUserIdAndParamValue(req.getUserId(), club.getId().toString());
        return true;
    }

    /**
     * 俱乐部会员列表
     *
     * @param req
     * @param userId 当前用户
     * @return
     */
    @FillerMethod
    public Page<AppClubMemberListRespDto> queryClubMember(ClubMemberListReqDto req, Long userId) {
        Club byId = clubService.findById(req.getClubId());
        if (byId == null) {
            return new Page<>();
        }
        Page<ClubMember> clubMemberPage = clubMemberService.queryClubMember(req.getMemberName(), req.getClubId(), req.getPageSize(), req.getPageNum());
        return PageConvert.dataConvert(clubMemberPage, item -> {
            AppClubMemberListRespDto resp = new AppClubMemberListRespDto();
            resp.setId(item.getId());
            resp.setUserId(item.getUserId());
            resp.setRole(item.getRole());
            ClubManagerApplyDo applyDo = clubManagerApplyService.findByQuery(new ClubManagerApplyQuery().setClubId(req.getClubId()).setUserId(item.getUserId()).setState(ClubManagerInviteEnum.PENDING.getCode()));
            if (Objects.nonNull(applyDo)) {
                resp.setManagerInviteStatus(ClubManagerInviteEnum.PENDING.getCode());
            }
            return resp;
        });
    }

    public void addMember(Long clubId, Long userId) {
        ArrayList<Long> userIds = Lists.newArrayList();
        userIds.add(userId);
        changeMember(clubId, userIds, () -> {
            Club byId = clubService.findById(clubId);
            Optional<ClubLevelConfig> byClubCode = clubLevelConfigService.findByClubCode(byId.getClubLevel());
            if (byClubCode.isPresent()) {
                if (byId.getMemberCount() >= byClubCode.get().getMemberUpperLimit()) {
                    throw new BizI18nException("club.audit.club_full");
                }
                ZnsUserEntity user = znsUserService.findById(userId);
                if (user == null) {
                    throw new BizI18nException("club.op.fail");
                }
                clubMemberService.addMember(userId, clubId, ClubMemberRoleEnum.MEMBER, byId.getClubGroupId());
                clubService.updateMemberCount(clubId);
                clubManager.checkClubLevelUpgrade(clubId, null);
                userTaskBizService.completeEvent(new EventTriggerDto().setUserId(userId).setEventSubType(TaskConstant.TakEventSubTypeEnum.CLUB_JOIN.getCode()));
                //发布加入俱乐部成功tb事件
                queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(), new TurbolinkApplicationEvent(TurbolinkEventEnum.ENTER_CLUB, userId, Map.of("isEnter", Boolean.TRUE.toString())));
                //新人俱乐部任务处理
                if (Objects.equals(byId.getType(), 1)) {
                    List<NewUserClubTaskDo> list = newUserClubTaskService.findList(new NewUserClubTaskQuery().setClubId(clubId));
                    if (!CollectionUtils.isEmpty(list)) {
                        List<NewUserClubTaskRecordDo> recordDoList = list.stream().map(s -> {
                            NewUserClubTaskRecordDo newUserClubTaskRecordDo = new NewUserClubTaskRecordDo();
                            BeanUtils.copyProperties(s, newUserClubTaskRecordDo);
                            newUserClubTaskRecordDo.setId(null);
                            newUserClubTaskRecordDo.setClubTaskId(s.getId());
                            newUserClubTaskRecordDo.setUserId(userId);
                            newUserClubTaskRecordDo.setIsComplete(0);
                            return newUserClubTaskRecordDo;
                        }).collect(Collectors.toList());
                        newUserClubTaskRecordService.batchCreate(recordDoList);
                    }
                }
            } else {
                throw new BizI18nException("club.op.fail");
            }
        });
    }

    private void subMember(Long clubId, List<Long> clubMemberIds, boolean isExit) {
        changeMember(clubId, clubMemberIds, () -> {
            Club club = clubService.findById(clubId);
            List<ClubMember> members = clubMemberService.findByMemberIdsAndClubId(clubMemberIds, clubId);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(members) || members.size() != clubMemberIds.size()) {
                throw new BizI18nException("club.op.fail");
            }
            long managerCount = members.stream().filter(member -> ClubMemberRoleEnum.MANAGER.getCode().equals(member.getRole())).count();
            List<Long> userIdList = members.stream().map(ClubMember::getUserId).collect(Collectors.toList());
            int deleteMemberCount = clubMemberService.removeMembers(clubId, members.stream().map(ClubMember::getId).toList(), isExit, club.getClubGroupId(), userIdList);
            //管理员请求失效
            List<ClubManagerApplyDo> applyServiceList = clubManagerApplyService.findList(new ClubManagerApplyQuery().setClubId(clubId).setUserIdList(clubMemberIds).setState(ClubManagerInviteEnum.PENDING.getCode()));
            if (!CollectionUtils.isEmpty(applyServiceList)) {
                applyServiceList.forEach(apply -> {
                    clubManagerApplyService.deleteById(apply.getId());
                });
            }
            //管理员数量修改
            if (managerCount > 0 || !CollectionUtils.isEmpty(applyServiceList)) {
                club.setManagerNum(Math.max(((club.getManagerNum() - (int) managerCount) - applyServiceList.size()), 0));
                clubService.update(club);
            }
            clubService.updateMemberCount(clubId);
        });
    }

    private void changeMember(Long clubId, List<Long> userIds, Runnable runnable) {
        RLock lock = redissonClient.getLock(RedisConstants.LOCK_CLUB_MEMBER_CHANGE + clubId);
        try {
            boolean b = lock.tryLock(2, TimeUnit.SECONDS);
            if (b) {
                transactionTemplate.executeWithoutResult(state -> {
                    runnable.run();
                });
            } else {
                log.error("俱乐部成员变更记录:{},{}", clubId, userIds);
                throw new BizI18nException("club.op.fail");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @EventListener
    @Async(value = "asyncExecutor")
    public void handleDisbandEvent(ClubDisbandEvent event) {
        log.info("清除member数据 start club {} member", event.getClubId());
        clubMemberService.removeAll(event.getClubId());
        log.info("清除member数据 end club {} member", event.getClubId());
        log.info("清除申请加入数据 start club {} member", event.getClubId());
        clubMemberApplyService.removeAll(event.getClubId());
        log.info("清除申请加入数据 end club {} member", event.getClubId());
        log.info("清理邀请记录 start club {} member", event.getClubId());
        clubActivityInviteService.removeAll(event.getClubId());
        log.info("清理邀请记录 end club {} member", event.getClubId());
        String env = (envProfile.equals("prod") || envProfile.equals("pre")) ? "prod" : envProfile;//由于预发和线上使用同数据库
        tencentImUtil.destroyGroup(ApiConstants.clubGroupId + event.getClubId() + env);
        // 清除该俱乐部的用户加入标志
        userExtraParamsService.deleteByParamValue(event.getClubId().toString());
    }

    @FillerMethod
    public Page<AppClubMemberInviteActivityListRespDto> queryClubMemberWithActivityList(ClubMemberListReqDto req, Long id) {
        ClubMemberListReqDto clubReq = new ClubMemberListReqDto();
        clubReq.setClubId(req.getClubId());
        clubReq.setPageNum(req.getPageNum());
        clubReq.setPageSize(req.getPageSize());
        Page<AppClubMemberListRespDto> appClubMemberListRespDtoPage = queryClubMember(clubReq, id);
        if (appClubMemberListRespDtoPage == null || CollectionUtils.isEmpty(appClubMemberListRespDtoPage.getRecords())) {
            return new Page<>();
        }
        List<Long> userIds = appClubMemberListRespDtoPage.getRecords().stream().map(AppClubMemberListRespDto::getUserId).toList();
        Map<Long, ZnsRunActivityUserEntity> activityUsersMap = runActivityUserService.findActivityUsersMap(req.getActivityId(), userIds);
        return PageConvert.dataConvert(appClubMemberListRespDtoPage, item -> {
            AppClubMemberInviteActivityListRespDto dto = ClubMemberConvert.INSTANCE.toActivityListResp(item);
            dto.setIsJoinedActivity(activityUsersMap.get(item.getUserId()) != null ? 1 : 0);
            return dto;
        });
    }


    public Boolean checkUserClub(AppClubMemberCheckUserClubReqDto req, Long userId) {
        Club club = clubService.findByClubGroupId(req.getClubGroupId());
        if (Objects.nonNull(club)) {
            Optional<ClubMember> clubMemberOp = clubMemberService.findByClubAndUserId(club.getId(), userId);
            if (!clubMemberOp.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    //查询用户是否加入新人俱乐部,并且达到7天时间
    public Boolean checkNewUerClub(Long userId) {
        Long newUserClub = clubMemberService.findNewUserClub(userId);
        Club club = clubService.findById(newUserClub);
        if (Objects.isNull(club)) {
            return false;
        }
        ZoneId zoneId = ZoneId.of("UTC-8");
        ZonedDateTime joinTime = club.getGmtCreate().toInstant().atZone(zoneId);
        ZonedDateTime now = ZonedDateTime.now(zoneId);
        ZonedDateTime targetTime = joinTime.plusDays(7).toLocalDate().atStartOfDay(zoneId);
        // 判断是否达到目标时间
        return !now.isBefore(targetTime);
    }

    /**
     * 通过队伍ID判断，用户是否在这个队伍的俱乐部
     *
     * @return
     */
    public List<Long> checkUserTeamClub(List<ClubActivityTeam> clubActivityTeams, Long userId) {

        List<Long> result = new ArrayList<>();
        for (ClubActivityTeam clubActivityTeam : clubActivityTeams) {
            Long clubId = clubActivityTeam.getClubId();
            Optional<ClubMember> byClubAndUserId = clubMemberService.findByClubAndUserId(clubId, userId);
            if (byClubAndUserId.isPresent()) {
                result.add(clubActivityTeam.getActivityTeamId());
            }
        }
        return result;
    }

    /**
     * 管理员被邀请人信息
     *
     * @param userId
     * @param clubId
     * @return
     */
    public InvitedPersonInfoRespDto managerPersonInfo(Long userId, Long clubId) {
        ZnsUserEntity user = znsUserService.findById(userId);
        UserLevel userLevel = userLevelService.findByUserId(userId);
        InvitedPersonInfoRespDto invitedPersonInfoRespDto = new InvitedPersonInfoRespDto();
        if (Objects.nonNull(user)) {
            invitedPersonInfoRespDto.setMemberType(user.getMemberType());
            invitedPersonInfoRespDto.setUserExpLevel(Objects.nonNull(userLevel) ? userLevel.getLevel() : user.getUserLevel());
            invitedPersonInfoRespDto.setNickname(user.getFirstName());
            invitedPersonInfoRespDto.setHeadPortrait(user.getHeadPortrait());
            invitedPersonInfoRespDto.setUserId(userId);
        }
        Club club = clubService.findById(clubId);
        invitedPersonInfoRespDto.setType(Objects.nonNull(club) ? club.getType() : null);
        clubMemberService.findByClubAndUserId(clubId, userId).ifPresent(clubMember -> invitedPersonInfoRespDto.setJoinClubTime(DateUtil.toZonedDateTime(clubMember.getGmtCreate())));
        return invitedPersonInfoRespDto;
    }

    /**
     * 管理员邀请详情
     *
     * @param clubId
     * @param userId
     * @return
     */
    @FillerMethod
    public ClubManagerInviteInfoRespDto managerInviteDetail(Long clubId, Long userId) {
        Club club = clubService.findById(clubId);
        if (Objects.isNull(club)) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.error"));
        }
        ClubManagerInviteInfoRespDto resp = new ClubManagerInviteInfoRespDto();
        BeanUtils.copyProperties(club, resp);
        clubMemberService.findByClubAndUserId(clubId, userId).ifPresent(clubMember -> resp.setJoinClubTime(DateUtil.toZonedDateTime(clubMember.getGmtCreate())));
        ClubManagerApplyDo applyDo = clubManagerApplyService.findByQuery(new ClubManagerApplyQuery().setClubId(clubId).setUserId(userId).setState(ClubManagerInviteEnum.PENDING.getCode()));
        resp.setInviteReason(Objects.nonNull(applyDo) ? applyDo.getInviteReason() : null);
        return resp;
    }

    /**
     * 管理员邀请
     */
    public Boolean managerJoinApply(ManagerInviteReqDto req) {
        Club club = clubService.findById(req.getClubId());
        if (Objects.isNull(club)) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.error"));
        }
        boolean empty = clubMemberService.findByClubAndUserId(req.getClubId(), req.getInvitedUserId()).isEmpty();
        if (empty) {
            throw new BaseException(I18nMsgUtils.getMessage("club.invite.error"));
        }
        ClubManagerApplyDo clubManagerApplyDo = clubManagerApplyService.findByQuery(new ClubManagerApplyQuery().setClubId(req.getClubId()).setUserId(req.getInvitedUserId()).setState(ClubManagerInviteEnum.PENDING.getCode()));
        if (Objects.nonNull(clubManagerApplyDo)) {
            throw new BaseException(I18nMsgUtils.getMessage("club.already.invite"));
        }
        int clubLevel = Integer.parseInt(club.getClubLevel().replaceAll("\\D+", ""));
        ClubLevelBenefitDo benefitDo = clubLevelBenefitService.findByQuery(new ClubLevelBenefitQuery().setLevelValue(clubLevel).setType(ClubLevelBenefitEnum.MANAGER_NUM.getCode()));
        if (club.getManagerNum() >= benefitDo.getValue()) {
            throw new BizI18nException("club.op.fail");
        }
        ClubManagerApplyDo applyDo = new ClubManagerApplyDo();
        applyDo.setInviteReason(req.getInviteReason());
        applyDo.setClubId(req.getClubId());
        applyDo.setInvitedUserId(req.getInvitedUserId());
        applyDo.setState(ClubManagerInviteEnum.PENDING.getCode());
        clubManagerApplyService.create(applyDo);
        club.setManagerNum(club.getManagerNum() + 1);
        return clubService.update(club) > 0;
    }


    public Boolean managerApplyAgree(ZnsUserEntity loginUser, Long clubId) {
        ClubManagerApplyDo applyDo = clubManagerApplyService.findByQuery(new ClubManagerApplyQuery().setClubId(clubId).setUserId(loginUser.getId()).setState(ClubManagerInviteEnum.PENDING.getCode()));
        if (Objects.nonNull(applyDo)) {
            applyDo.setState(ClubManagerInviteEnum.REJECT.getCode());
            clubManagerApplyService.update(applyDo);

            clubMemberService.findByClubAndUserId(clubId, loginUser.getId()).ifPresent(clubMember -> {
                clubMember.setRole(ClubMemberRoleEnum.MANAGER.getCode());
                clubMember.setRoleOrder(ClubMemberRoleEnum.MANAGER.getOrder());
                clubMemberService.update(clubMember);
            });
        }
        return true;
    }

    public Boolean managerApplyReject(ZnsUserEntity loginUser, Long clubId) {
        Club club = clubService.findById(clubId);
        if (Objects.isNull(club)) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.error"));
        }
        ClubManagerApplyDo applyDo = clubManagerApplyService.findByQuery(new ClubManagerApplyQuery().setClubId(clubId).setUserId(loginUser.getId()).setState(ClubManagerInviteEnum.PENDING.getCode()));
        if (Objects.nonNull(applyDo)) {
            applyDo.setState(ClubManagerInviteEnum.REJECT.getCode());
            clubManagerApplyService.update(applyDo);
            club.setManagerNum(club.getManagerNum() - 1);
            return clubService.update(club) > 0;
        }
        return true;
    }

}
