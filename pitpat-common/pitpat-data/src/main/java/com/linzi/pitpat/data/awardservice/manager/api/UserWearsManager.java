package com.linzi.pitpat.data.awardservice.manager.api;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.biz.ActivityVerificationBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityWearCacheInfo;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.biz.UserWearsBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.query.WearQuery;
import com.linzi.pitpat.data.awardservice.model.resp.MyWearResp;
import com.linzi.pitpat.data.awardservice.model.vo.ActivityWearAward;
import com.linzi.pitpat.data.awardservice.model.vo.WearAward;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户服装处理类
 *
 * <AUTHOR>
 * @date 2024/7/22 3:18
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserWearsManager {
    private final WearsService wearsService;
    private final UserWearsBagService userWearsBagService;
    private final ExchangeScoreAwardService exchangeScoreAwardService;
    private final ISysConfigService sysConfigService;
    private final ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    private final ZnsUserAccountService userAccountService;
    private final MainActivityService mainActivityService;
    private final ZnsRunActivityService runActivityService;
    private final AppRouteService appRouteService;
    private final SeriesActivityRelService seriesActivityRelService;

    private final UserWearsBizService userWearsBizService;
    private final ActivityVerificationBizService activityVerificationBizService;
    private final RotationAreaBizService rotationAreaBizService;

    private final RedissonClient redissonClient;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    /**
     * 查询用户服装
     *
     * @param loginUser
     * @param query
     * @param appVersion
     * @return
     */
    public List<MyWearResp> findMyWear(ZnsUserEntity loginUser, WearQuery query, Integer appVersion) {
        Integer gender = loginUser.getGender();
        //用户服装背包
        List<UserWearsBag> userWearsBags = userWearsBagService.findListWearType(loginUser.getId(), 0, query.getWearType());


        //拿到积分兑换服装列表
        List<MyWearResp> result = exchangeScoreAwardService.findAwardIdsByType(ExchangeScoreTypeEnum.CLOTHES.getCode(), query.getWearType());
        Map<List<Integer>, MyWearResp> result1 = result.stream()
                .collect(Collectors.toMap(
                        p -> Arrays.asList(p.getWearType(), p.getWearId()),
                        p -> p,
                        (p1, p2) -> p1.getExchangeScore() > p2.getExchangeScore() ? p1 : p2,
                        LinkedHashMap::new
                ));
        log.info("findMyWear exchangeWearList有");
        List<MyWearResp> exchangeWearList = new LinkedList<>(result1.values());
        List<Integer> suitList = Lists.newArrayList(18, 11, 33, 36, 37, 38);//套装id列表
        log.info("findMyWear 积分兑换剔除背包已拥有");
        if (!CollectionUtils.isEmpty(userWearsBags)) {
            for (UserWearsBag userWearsBag : userWearsBags) {
                List<MyWearResp> scoreList = exchangeWearList.stream().filter(s -> Objects.equals(s.getWearType(), userWearsBag.getWearType()) && Objects.equals(s.getWearId(), userWearsBag.getWearValue())).collect(Collectors.toList());
                //积分兑换剔除背包已拥有
                exchangeWearList.removeAll(scoreList);
            }
        }
        log.info("findMyWear 积分兑换剔除背包已拥有结束");
        List<MyWearResp> myWearList = new LinkedList<>();
        RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
        if (!CollectionUtils.isEmpty(list)) {
            List<ActivityWearCacheInfo> finalList = list.stream().toList();
            //执行时间较长，异步处理
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    list.iterator().forEachRemaining(info -> {
                        if (Objects.isNull(info.getExpireTime()) || info.getExpireTime().isBefore(ZonedDateTime.now())) {
                            list.remove(info);
                        }
                    });
                }
            });

            finalList = finalList.stream().filter(info -> Objects.nonNull(info) && Objects.nonNull(info.getExpireTime()) && info.getExpireTime().isAfter(ZonedDateTime.now())).collect(Collectors.toList());
            List<MyWearResp> activityList = dealActivityWear(finalList, userWearsBags, exchangeWearList, appVersion, loginUser, query);

            List<MyWearResp> ActivityCollect = activityList.stream().filter(w -> Objects.nonNull(w.getStartTime()) && Objects.nonNull(w.getCreatTime())).sorted(Comparator.comparing(MyWearResp::getStartTime).thenComparing(MyWearResp::getCreatTime)).collect(Collectors.toList());
            myWearList.addAll(ActivityCollect);
        }
        //填充积分兑换服装
        for (MyWearResp myWearResp : exchangeWearList) {
            myWearResp.setWearSource(2);
            Wears wear = wearsService.findWearByTypeId(myWearResp.getWearType(), myWearResp.getWearId());
            userWearsBizService.fillWearResp(gender, myWearResp, wear);
            if (myWearResp.getExpiredTime() != null) {
                myWearResp.setExpireType(0);
            } else {
                myWearResp.setExpireType(1);
            }
            myWearList.add(myWearResp);
        }
        //填充背包服装
        if (!CollectionUtils.isEmpty(userWearsBags)) {
            for (UserWearsBag userWearsBag : userWearsBags) {
                MyWearResp myWearResp = new MyWearResp();
                Wears wearsBag = wearsService.findWearByTypeId(userWearsBag.getWearType(), userWearsBag.getWearValue());
                BeanUtils.copyProperties(userWearsBag, myWearResp);
                myWearResp.setWearId(userWearsBag.getWearValue());
                myWearResp.setWearSource(1);
                userWearsBizService.fillWearResp(gender, myWearResp, wearsBag);
                if (userWearsBag.getExpiredTime() != null) {
                    myWearResp.setExpireType(0);
                    myWearResp.setExpireDay(userWearsBag.getExpiredTime());
                } else {
                    myWearResp.setExpireType(1);
                }

                myWearList.add(myWearResp);
            }
        }
        //去重
        Map<List<Integer>, MyWearResp> collect1 = myWearList.stream()
                .collect(Collectors.toMap(
                        p -> Arrays.asList(p.getWearType(), p.getWearId()),
                        p -> p,
                        (p1, p2) -> p1,
                        LinkedHashMap::new
                ));
        List<MyWearResp> resultList = new LinkedList<>(collect1.values());
        resultList = new ArrayList<>(resultList);
        if (Objects.equals(query.getWearType(), 0)) {
            //不展示的服装表主键ID
            String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.NO_DISPLAY_IDS.getCode());
            List<Integer> noDisplayIds = NumberUtils.stringToInt(config.split(","));
            List<Wears> wears = wearsService.listByIds(noDisplayIds);
            for (Wears wear : wears) {
                List<MyWearResp> removeList = resultList.stream().filter(s -> Objects.equals(wear.getWearType(), s.getWearType()) && Objects.equals(wear.getWearId(), s.getWearId())).collect(Collectors.toList());
                resultList.removeAll(removeList);
            }
            //过滤后不展示服装的all拥有服装
        }
        Set<Long> ruleIds = resultList.stream().map(MyWearResp::getRuleId).collect(Collectors.toSet());

        //根据用户信息设置对应的兑换现金和货币信息
        if (!CollectionUtils.isEmpty(ruleIds)) {
            Currency currency = userAccountService.getUserCurrency(loginUser.getId());
            List<ExchangeScoreRuleCurrencyEntity> exchangeScoreRuleCurrencies = exchangeScoreRuleCurrencyService.findAllByRuleIdsAndCurrencyCode(ruleIds, currency.getCurrencyCode());
            if (!org.springframework.util.CollectionUtils.isEmpty(exchangeScoreRuleCurrencies)) {
                //不为空，处理积分兑换
                Map<Long, ExchangeScoreRuleCurrencyEntity> exchangeScoreRuleCurrencyMap = exchangeScoreRuleCurrencies.stream().collect(Collectors.toMap(ExchangeScoreRuleCurrencyEntity::getExchangeScoreRuleId, Function.identity(), (x, y) -> y));
                resultList = resultList.stream().map(item -> {
                    if (Objects.nonNull(exchangeScoreRuleCurrencyMap.get(item.getRuleId()))) {
                        item.setCurrency(currency);
                        item.setExchangeAmount(exchangeScoreRuleCurrencyMap.get(item.getRuleId()).getExchangeAmount());
                    }
                    return item;
                }).toList();
            }
        }
        return resultList;
    }

    /**
     * 获取用户服装信息
     *
     * @param loginUser
     * @param query
     * @param appVersion
     * @return
     */
    private List<MyWearResp> dealActivityWear(List<ActivityWearCacheInfo> list, List<UserWearsBag> userWearsBags, List<MyWearResp> exchangeWearList, Integer appVersion, ZnsUserEntity loginUser, WearQuery query) {
        log.info("dealActivityWear start");
        List<MyWearResp> wearRespList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            log.info("dealActivityWear list isEmpty");
            return wearRespList;
        }
        //重新封装，每个服装放单独的
        List<ActivityWearAward> newList = new ArrayList<>();
        Set<Long> activityIds = new HashSet<>();
        for (ActivityWearCacheInfo activityWearCacheInfo : list) {
            for (WearAward wearAward : activityWearCacheInfo.getWearList()) {
                ActivityWearAward info = new ActivityWearAward();
                info.setActivityId(activityWearCacheInfo.getActivityId()).setWearId(wearAward.getWearId());
                info.setWearName(wearAward.getWearName()).setWearType(wearAward.getWearType()).setWearImageUrl(wearAward.getWearImageUrl());
                info.setKey(wearAward.getWearType() + "_" + wearAward.getWearId());
                newList.add(info);
                activityIds.add(activityWearCacheInfo.getActivityId());
            }
        }
        if (CollectionUtils.isEmpty(newList)) {
            log.info("dealActivityWear newList isEmpty");
            return wearRespList;
        }
        //查询活动
        List<MainActivity> activityList = mainActivityService.findListByIds(new ArrayList<>(activityIds));
        Map<Long, MainActivity> mainActivityMap = activityList.stream().collect(Collectors.toMap(MainActivity::getId, Function.identity(), (x, y) -> x));
        List<ZnsRunActivityEntity> runActivityEntities = runActivityService.findByIds(Lists.newArrayList(activityIds));
        Map<Long, ZnsRunActivityEntity> runActivityEntityMap = null;
        if (!CollectionUtils.isEmpty(runActivityEntities)) {
            runActivityEntityMap = runActivityEntities.stream().collect(Collectors.toMap(ZnsRunActivityEntity::getId, Function.identity(), (x, y) -> x));
        }

        //分组处理
        Map<String, List<ActivityWearAward>> listMap = newList.stream().collect(Collectors.groupingBy(ActivityWearAward::getKey));
        for (Map.Entry<String, List<ActivityWearAward>> entry : listMap.entrySet()) {
            List<ActivityWearAward> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }

            ActivityWearAward activityWearAward = value.get(0);
            if (!Objects.equals(query.getWearType(), 0) && !Objects.equals(activityWearAward.getWearType(), query.getWearType())) {
                continue;
            }

            //活动剔除背包已拥有
            if (!CollectionUtils.isEmpty(userWearsBags)) {
                boolean userWearsBagHas = false;
                for (UserWearsBag userWearsBag : userWearsBags) {
                    if (Objects.equals(activityWearAward.getWearType(), userWearsBag.getWearType())
                            && Objects.equals(activityWearAward.getWearId(), userWearsBag.getWearValue())) {
                        userWearsBagHas = true;
                    }
                }
                if (userWearsBagHas) {
                    continue;
                }
            }

            //活动数据判断处理
            for (ActivityWearAward wearAward : value) {
                MainActivity mainActivity = mainActivityMap.get(wearAward.getActivityId());
                if (Objects.isNull(mainActivity) || MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
                    if (Objects.isNull(runActivityEntityMap)) {
                        continue;
                    }
                    ZnsRunActivityEntity runActivity = runActivityEntityMap.get(wearAward.getActivityId());
                    if (Objects.nonNull(runActivity) && Objects.equals(runActivity.getStatus(), 1)
                            && Objects.equals(runActivity.getIsShowAll(), 1)
                            && runActivity.getApplicationEndTime().isAfter(ZonedDateTime.now())
                            && runActivity.getActivityEndTime().isAfter(ZonedDateTime.now())
                            && (runActivity.getCountry().equals("all") || runActivity.getCountry().equals(loginUser.getCountry()))) {
                        wearAward.setStartTime(runActivity.getActivityStartTime());
                        wearAward.setCreatTime(runActivity.getCreateTime());
                        wearAward.setMainType(MainActivityTypeEnum.OLD.getType());
                        wearAward.setActivityType(runActivity.getActivityType());

                        //积分兑换剔除活动可以获取
                        if (!CollectionUtils.isEmpty(exchangeWearList)) {
                            List<MyWearResp> collect = exchangeWearList.stream().filter(s -> Objects.equals(s.getWearType(), wearAward.getWearType()) && Objects.equals(s.getWearId(), wearAward.getWearId())).collect(Collectors.toList());
                            exchangeWearList.removeAll(collect);
                        }
                    }
                } else {
                    if (Objects.isNull(mainActivity.getApplicationStartTime()) || Objects.isNull(mainActivity.getApplicationEndTime())) {
                        continue;
                    }
                    try {
                        //是否在报名时间
                        Long startByZone = DateUtil.getStampByZone(mainActivity.getApplicationStartTime(),
                                mainActivity.getTimeStyle() == 0 ? "UTC" : loginUser.getZoneId());
                        Long entByZone = DateUtil.getStampByZone(mainActivity.getApplicationEndTime(),
                                mainActivity.getTimeStyle() == 0 ? "UTC" : loginUser.getZoneId());

                        if (System.currentTimeMillis() < startByZone || System.currentTimeMillis() > entByZone) {
                            log.info("fillNewActivityWear end,不在报名期间");
                            continue;
                        }
                        if (Arrays.asList(-1, 2, 3).contains(mainActivity.getActivityState())) {
                            log.info("fillNewActivityWear end,活动已结束或下架");
                            continue;
                        }
                        wearAward.setMainType(mainActivity.getMainType());
                        wearAward.setStartTime(new Date(startByZone));
                        wearAward.setCreatTime(mainActivity.getGmtCreate());
                        //积分兑换剔除活动可以获取
                        if (!CollectionUtils.isEmpty(exchangeWearList)) {
                            List<MyWearResp> collect = exchangeWearList.stream().filter(s -> Objects.equals(s.getWearType(), wearAward.getWearType()) && Objects.equals(s.getWearId(), wearAward.getWearId())).collect(Collectors.toList());
                            exchangeWearList.removeAll(collect);
                        }
                    } catch (Exception e) {
                        log.info("活动数据判断处理 失败，活动id=" + mainActivity.getId());
                        continue;
                    }

                }
            }
            //过滤无时间并排序
            value = value.stream().filter(w -> Objects.nonNull(w.getStartTime()) && Objects.nonNull(w.getCreatTime())).sorted(Comparator.comparing(ActivityWearAward::getStartTime).thenComparing(ActivityWearAward::getCreatTime)).collect(Collectors.toList());
            if (value.isEmpty()) {
                continue;
            }

            //条件判断
            ActivityWearAward activityWearAwardOne = null;
            for (ActivityWearAward wearAward : value) {
                if (Objects.isNull(activityWearAwardOne)) {
                    if (!MainActivityTypeEnum.OLD.getType().equals(wearAward.getMainType())) {
                        try {
                            activityVerificationBizService.areaAndGroupCheck(loginUser, wearAward.getActivityId());
                            activityWearAwardOne = wearAward;
                        } catch (Exception e) {
                            log.info("fillNewActivityWear end,", e);
                            continue;
                        }
                    } else {
                        activityWearAwardOne = wearAward;
                    }
                }
            }
            if (Objects.isNull(activityWearAwardOne)) {
                continue;
            }
            MyWearResp myWearResp = null;
            if (MainActivityTypeEnum.OLD.getType().equals(activityWearAwardOne.getMainType())) {
                myWearResp = fillOldActivityWear(activityWearAwardOne, loginUser);
            } else {
                myWearResp = fillNewActivityWear(activityWearAwardOne, loginUser);
            }
            wearRespList.add(myWearResp);
        }

        return wearRespList;
    }

    /**
     * 填充旧活动服装
     *
     * @param activityWearAward
     * @param loginUser
     * @return
     */
    private MyWearResp fillOldActivityWear(ActivityWearAward activityWearAward, ZnsUserEntity loginUser) {
        MyWearResp myWearResp = new MyWearResp();
        BeanUtils.copyProperties(activityWearAward, myWearResp);
        myWearResp.setWearSource(3);
        myWearResp.setObtainActivityId(activityWearAward.getActivityId());
        Wears wear = wearsService.findWearByTypeId(activityWearAward.getWearType(), activityWearAward.getWearId());
        userWearsBizService.fillWearResp(loginUser.getGender(), myWearResp, wear);
        //填充跳转地址
        PrimitiveForest primitiveForest = new PrimitiveForest();
        primitiveForest.setJumpType(2);
        primitiveForest.setRunActivityId(activityWearAward.getActivityId());
        primitiveForest.setRunActivityType(activityWearAward.getActivityType());
        AppRoute route = appRouteService.findRoute(primitiveForest);
        myWearResp.setActivityJumpUrl(route.getJumpUrl());
        if (StringUtils.hasText(route.getJumpParam())) {
            myWearResp.setActivityJumpParam(route.getJumpParam());
        }
        return myWearResp;
    }

    /**
     * 填充新活动服装信息
     *
     * @param wearAward
     * @param loginUser
     * @return
     */
    private MyWearResp fillNewActivityWear(ActivityWearAward wearAward, ZnsUserEntity loginUser) {
        MyWearResp myWearResp = new MyWearResp();
        BeanUtils.copyProperties(wearAward, myWearResp);
        myWearResp.setWearSource(3);
        myWearResp.setObtainActivityId(wearAward.getActivityId());
        myWearResp.setMainType(wearAward.getMainType());
        Wears wear = wearsService.findWearByTypeId(wearAward.getWearType(), wearAward.getWearId());
        userWearsBizService.fillWearResp(loginUser.getGender(), myWearResp, wear);
        //填充跳转地址,系列赛子赛事需要跳转上级
        if (MainActivityTypeEnum.SERIES_SUB.getType().equals(wearAward.getMainType())) {
            MainActivity mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(wearAward.getActivityId());
            wearAward.setActivityId(mainActivity.getId()).setMainType(mainActivity.getMainType());
        }
        RotationArea route = rotationAreaBizService.getNewActivityRoute(wearAward.getActivityId(), wearAward.getMainType(), null);
        myWearResp.setActivityJumpUrl(route.getUrl());
        if (StringUtils.hasText(route.getJumpParam())) {
            myWearResp.setActivityJumpParam(route.getJumpParam());
        }
        return myWearResp;
    }
}
