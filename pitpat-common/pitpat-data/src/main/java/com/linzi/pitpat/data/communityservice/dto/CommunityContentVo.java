package com.linzi.pitpat.data.communityservice.dto;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.annotation.Desensitized;
import com.linzi.pitpat.data.communityservice.dto.console.ContentBindTopicInfo;
import com.linzi.pitpat.data.enums.SensitiveTypeEnum;
import com.linzi.pitpat.data.userservice.dto.response.CommunityContentPicResponseDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class CommunityContentVo extends RedirectVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    //用户id
    private Long userId;
    //注册邮箱
    @Desensitized(type = SensitiveTypeEnum.EMAIL)
    private String userEmail;
    //用户类型：0-官方，1-真实用户，2-机器人任务
    private Integer userType;
    //用户名
    private String userName;
    //TODO 昵称 弃用
    private String lastName;
    //头像
    private String headPortrait;
    //标题
    private String title;
    //内容类型  1：纯文字 2：纯图片 3：文本加图片 4:视频 5：文字+视频
    private Integer contentType;
    //内容
    private String content;
    //图片
    private List<CommunityContentPicResponseDto> pics;
    //曝光量
    private Integer exposureNum;
    //显示点赞量
    private Long displayedLikedNum;
    //真实点赞量
    private Long realLikedNum;
    //发布类型 0：即时 1：定时
    private Integer publishType;
    //发布状态：0：未发布 1：已取消 2：已发布
    private Integer publishStatus;
    //预计发布时间
    private ZonedDateTime publishExpectTime;
    //真实发布时间
    private ZonedDateTime publishRealTime;
    //活动赛事的类型
    private Integer runActivityType;
    //活动地址/l路由/路由id
    private String activityUrl;
    //关联的赛事ID
    private Long runActivityId;
    //页面/路由id
    private Long routeId;
    //课程id
    private Long courseId;
    //课程名称
    private String courseName;
    //h5页面名称
    private String h5title;
    //页面名称
    private String pageName;
    //赛事名称
    private String activityName;
    // 跳转配置中的图片
    private String jumpImg;
    // 置顶状态：0-非置顶；1-置顶
    private Integer topStatus;

    /**
     * 课程默认语言
     *
     * @see I18nConstant.LanguageCodeEnum
     */
    private String defaultLangCode;

    /**
     * 社区内容多语言参数
     */
    private List<CommunityI18nDto> i18nList;
    /**
     * 用户编码
     */
    private String userCode;

    //视频地址
    private String videoUrl;

    //视频封面地址
    private String videoCoverUrl;
    //视频封面宽度
    private Integer videoCoverWidth;
    //视频封面高度
    private Integer videoCoverHeight;
    /**
     * 数据来源
     */
    private String dataSource;
    /**
     * 数据来源id
     */
    private String dataSourceId;


    /**
     * 话题信息
     */
    private List<ContentBindTopicInfo> topicInfos;
    /**
     * /**
     * 俱乐部id
     */
    private Long clubId;

    /**
     * 俱乐部name
     */
    private String clubName;


    //评论数
    private Long commentNum;

    //是否开启评论0不是 1是
    private Integer isEnableComment;
    //帖子类型 0普通 1精选
    private Integer postType;

    // 显示方式（0不显示，1显示）
    private Integer isShow;

}
