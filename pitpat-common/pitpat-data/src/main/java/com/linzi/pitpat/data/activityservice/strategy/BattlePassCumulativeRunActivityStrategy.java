package com.linzi.pitpat.data.activityservice.strategy;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDao;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBitSet;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/8/7 20:53
 */
@Service
@Slf4j
public class BattlePassCumulativeRunActivityStrategy extends BaseOfficialActivityStrategy {

    @Resource
    private ActivityAwardCurrencyBizService activityAwardCurrencyBizService;
    @Autowired
    private ZnsUserAccountDao znsUserAccountDao;
    @Autowired
    private RedissonClient redissonClient;

    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {

    }

    @Override
    public Result handleUserActivityState(ZnsRunActivityEntity activityEntity, Integer userStatus, ZnsUserEntity user, String password, Integer runningGoals, boolean immediatelyAdmission, Long taskId, HandleActivityRequest request, boolean checkVersion) {
//        String logNo = Logger.inheritableThreadLocalNo.get();
//        Long time = ch.qos.logback.classic.Logger.inheritableThreadLocalTime.get();
        log.info("handleUserActivityState BattlePassCumulativeRunActivityStrategy....................");
        // 官方赛事
        if (1 == userStatus.intValue()) {
            //加锁
            String key = RedisConstants.ACTIVITY_PARTICIPATION_KEY + user.getId();
            RLock lock = redissonClient.getLock(key);
            try {
                if (LockHolder.tryLock(lock, 3, 30)) {
                    //校验活动国家跟用户国家是否相同
                    if (notContainsCountry(activityEntity, user)) {
                        return CommonResult.fail(ActivityError.COUNTRY_ERROR.getCode(), ActivityError.COUNTRY_ERROR.getMsg());
                    }

                    // 查询是否已报名
                    ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), user.getId());
                    if (Objects.equals(1, request.getIsPay())) {
                        // 支付保证金逻辑
                        log.info("开始执行支付流程handlePayRunActivity activityId = " + activityEntity.getId() + ",userId=" + user.getId() + ",discountPayType=" + request.getDiscountPayType() + ",couponId=" + request.getUserCouponId());
                        Result payResult = runActivityPayManager.handlePayRunActivity(activityEntity, user, password, request, checkVersion);
                        if (null != payResult) {
                            return payResult;
                        }
                        if (Objects.nonNull(activityUser)) {
                            if (request.getDiscountPayType() != null) {
                                activityUser.setDiscount(request.getDiscountPayType());
                            }
                            activityUser.setIsPay(1);
                            runActivityUserService.updateById(activityUser);
                            runActivityService.addOfficialActivityUserPayCount(activityEntity.getId());
                        }
                    }
                    if (Objects.isNull(activityUser)) {
                        // 添加官方赛事活动用户
                        log.info("添加官方赛事活动用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                        activityUserBizService.addOfficialActivityUser(activityEntity, user.getId(), runningGoals, taskId, request.getSource(), null, false);
                        //修改官方赛事活动参与用户
                        log.info("修改官方赛事活动参与用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
                        runActivityService.addOfficialActivityUserCount(activityEntity.getId());
                    }

                } else {
                    return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), "Processing failed");
                }
            } catch (Exception e) {
                log.error("handleUserActivityState 异常", e);
                exceptionNotification("Event registration operation failed with exception");
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }

            return CommonResult.success();
        }
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Processing failed");
    }

    public void handleBattlePassCumulativeRunActivity(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        ZnsUserEntity user = userService.findById(userRunDataDetail.getUserId());
        if (Objects.isNull(user)) {
            return;
        }
        if (Objects.equals(user.getIsRobot(), 1)) {
            return;
        }
        //自动报名新累计跑活动处理
        //查询当月活动
        ZonedDateTime date = ZonedDateTime.now();
        //国家注册不一定会有值，所以默认给一个西八区的州code
        AreaEntity area = areaService.selectAreaByCode(StringUtils.hasText(user.getStateCode()) ? user.getStateCode() : "US_AFA");
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(date, TimeZone.getTimeZone(area.getZoneId()));
        //查询当前时间内的新里程碑
        RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                .isDelete(0)
                .activityStateIn(Arrays.asList(0, 1))
                .maxActivityStartTime(now)
                .minActivityEndTime(now);
        if (Objects.equals(0, user.getIsTest())) {
            runActivityQueryBuilder.isTest(0);
        }
        ZnsRunActivityEntity runActivity = runActivityService.findOne(runActivityQueryBuilder
                .build()
        );
        if (Objects.isNull(runActivity)) {
            log.info("handleBattlePassCumulativeRunActivity end 不存在");
            return;
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(runActivity.getId(), user.getId());
        if (Objects.isNull(activityUser)) {
            this.handleUserActivityState(runActivity, 1, user, "", null, true, null, new HandleActivityRequest(), false);
            activityUser = runActivityUserService.findActivityUser(runActivity.getId(), user.getId());
        }

        RBitSet bitSet = redissonClient.getBitSet(RedisConstants.CANCEL_BATTLE_PASS);
        if (bitSet.get(userRunDataDetail.getUserId())) {
            log.info("升级后用户不再处理里程碑：{}", userRunDataDetail.getUserId());
            return;
        }
        //非跑步机上传数据不处理
        if (userRunDataDetail.getDataSource() != 0 || !Objects.equals(userRunDataDetail.getUnActivityType(), 0)) {
            log.info("handleOfficialCumulativeRun结束，数据上传非app");
            //修改为待上传数据
            userRunDataDetailsService.toBeUploadStatus(userRunDataDetail.getId());
            return;
        }

        //奖励发放
        BigDecimal award = handleOfficialCumulativeRunAward(userRunDataDetail, activityUser, runActivity);
        runActivityUserService.addRunData(activityUser.getId(), runActivity.getCompleteRuleType(), userRunDataDetail.getRunTime(), userRunDataDetail.getRunMileage(), award);
        log.info("里程碑处理结束,,活动id：{}，用户id：{},增加时间：{}，增加距离：{}，增加奖励：{}", activityUser.getActivityId(), userRunDataDetail.getUserId(), userRunDataDetail.getRunTime(), userRunDataDetail.getRunMileage(), award);
    }

    private BigDecimal handleOfficialCumulativeRunAward(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsRunActivityUserEntity activityUserEntity, ZnsRunActivityEntity runActivity) {
        log.info("新累计跑奖励开始发放，用户id：{}，活动id：{}", userRunDataDetail.getUserId(), activityUserEntity.getActivityId());
        //查询所有奖励
        List<AwardConfigDto> activityTypeAwardConfigs = activityAwardConfigService.selectAwardConfigDtoList(activityUserEntity.getActivityId(), null, null);
        if (CollectionUtils.isEmpty(activityTypeAwardConfigs)) {
            log.info("新累计跑奖励结束发放，用户id：{}，活动id：{}，无奖励配置", userRunDataDetail.getUserId(), activityUserEntity.getActivityId());
            return BigDecimal.ZERO;
        }
        //当前只有里程
        Map<Integer, List<AwardConfigDto>> listMap = activityTypeAwardConfigs.stream().collect(Collectors.groupingBy(AwardConfigDto::getTargetMileage));
        List<Map.Entry<Integer, List<AwardConfigDto>>> list = listMap.entrySet().stream().collect(Collectors.toList());
        Collections.sort(list, Comparator.comparingInt(Map.Entry<Integer, List<AwardConfigDto>>::getKey));

        if (activityUserEntity.getCompletedLevel() > list.size()) {
            log.info("新累计跑奖励结束发放，用户id：{}，活动id：{}，无奖励配置", userRunDataDetail.getUserId(), activityUserEntity.getActivityId());
            return BigDecimal.ZERO;
        }
        ZnsUserEntity user = userService.findById(activityUserEntity.getUserId());

        BigDecimal award = BigDecimal.ZERO;
        try {
            //计算结束剩余时间
            ZonedDateTime date = ZonedDateTime.now();
            ZonedDateTime now = DateUtil.getDate2ByTimeZone(date, TimeZone.getTimeZone(user.getZoneId()));
            int surplusTime = DateUtil.betweenSecond(now, runActivity.getActivityEndTime());
            for (int i = activityUserEntity.getCompletedLevel(); i < list.size(); i++) {
                Integer mil = list.get(i).getKey();
                boolean isComplete = false;
                if (runActivity.getCompleteRuleType() == 1) {
                    BigDecimal runMileage = activityUserEntity.getRunMileage().add(userRunDataDetail.getRunMileage());
                    log.info("用户跑步里程 runMileage: {}， activityUserEntity: {}, userRunDataDetail: {}", runMileage, activityUserEntity.getRunMileage(), userRunDataDetail.getRunMileage());
                    if (runMileage.compareTo(new BigDecimal(mil)) >= 0) {
                        isComplete = true;
                    }
                } else if (runActivity.getCompleteRuleType() == 2) {
                    int runTime = activityUserEntity.getRunTime() + userRunDataDetail.getRunTime();
                    log.info("用户跑步时间 runTime: {}, activityUserEntity{}: ,userRunDataDetail: {} ", runTime, activityUserEntity.getRunTime(), userRunDataDetail.getRunTime());
                    if (runTime >= mil.intValue()) {
                        isComplete = true;
                    }
                }

                log.info("累计跑阶段目标：{}，完成规则类型：{}，是否完成：{}", mil, runActivity.getCompleteRuleType(), isComplete);
                if (isComplete) {
                    //防止重复奖励
                    String key = RedisConstants.BATTLE_PASS_CUMULATIVE_RUN_ACTIVITY + mil + activityUserEntity.getUserId();
                    Object o = redisTemplate.opsForValue().get(key);
                    if (Objects.nonNull(o)) {
                        continue;
                    }
                    List<AwardConfigDto> dtoList = list.get(i).getValue();
                    List<AwardConfigDto> ordinaryList = dtoList.stream().filter(d -> d.getAwardCondition() == 0).collect(Collectors.toList());
                    List<AwardConfigDto> advancedList = dtoList.stream().filter(d -> d.getAwardCondition() == 1).collect(Collectors.toList());
                    Boolean hashUnclaimedAward = false;
                    if (!CollectionUtils.isEmpty(ordinaryList)) {
                        for (AwardConfigDto awardConfigDto : ordinaryList) {
                            Boolean hasAward = sendAward(awardConfigDto, user, i, userRunDataDetail.getId(), runActivity.getId(), AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ORDINARY_AWARD);
                            log.info("里程碑普通是否有奖励 {} ", hasAward);
                            if (hasAward) {
                                hashUnclaimedAward = true;
                            }
                        }
                    }

                    if (!CollectionUtils.isEmpty(advancedList)) {
                        for (AwardConfigDto awardConfigDto : advancedList) {
                            Boolean hasAward = sendAward(awardConfigDto, user, i, userRunDataDetail.getId(), runActivity.getId(), AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ADVANCED_AWARD);
                            log.info("里程碑进阶是否有奖励 {} ", hasAward);
                            if (hasAward && activityUserEntity.getIsPay() == 1) {
                                hashUnclaimedAward = true;
                            }
                        }
                    }

                    //保存关卡
                    ZnsRunActivityUserEntity update = new ZnsRunActivityUserEntity();
                    update.setId(activityUserEntity.getId());
                    update.setCompletedLevel(i + 1);
                    if (hashUnclaimedAward) {
                        update.setHashUnclaimedAward(1);
                    }
                    runActivityUserService.updateById(update);
                    redisTemplate.opsForValue().set(key, "1", surplusTime, TimeUnit.SECONDS);
                }

            }

            log.info("新累计跑奖励结束发放，用户id：{}，活动id：{}", userRunDataDetail.getUserId(), activityUserEntity.getActivityId());
        } catch (Exception e) {
            log.error("handleOfficialCumulativeRunAward error: {}", e.getMessage(), e);
        }
        return award;
    }

    private Boolean sendAward(AwardConfigDto awardConfigDto, ZnsUserEntity user, int sort, Long detailId, Long runActivityId, AccountDetailTypeEnum officialCumulativeAward) {
        if (Objects.isNull(awardConfigDto)) {
            return false;
        }
        Integer awardType = awardConfigDto.getAwardType();
        switch (awardType) {
            case 1:
                sendAmountAward(awardConfigDto, user, sort, detailId, runActivityId, officialCumulativeAward);
                break;
            case 2:
                sendCouponAward(awardConfigDto, user, runActivityId);
                break;
            case 3:
                sendScoreAward(awardConfigDto, user, runActivityId);
                break;
            case 4:
                sendWearAward(awardConfigDto, user, runActivityId, sort);
                break;
            default:
                break;
        }
        return true;
    }

    private void sendWearAward(AwardConfigDto awardConfigDto, ZnsUserEntity user, Long runActivityId, int sort) {
        log.info("里程碑开始发放服装励");
        awardConfigDto.setMilepost(awardConfigDto.getTargetMileage());
        userWearsBattlePassService.sendUserWearMilepost(awardConfigDto, user, runActivityId, 0, sort + 1);
    }

    private void sendScoreAward(AwardConfigDto awardConfigDto, ZnsUserEntity user, Long runActivityId) {
        log.info("里程碑开始发放积分奖励");
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setUserId(user.getId());
        activityUserScore.setScore(awardConfigDto.getScore());
        if (awardConfigDto.getAwardCondition() == 0) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_11.getType());
        } else {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_12.getType());
        }
        activityUserScore.setActivityId(runActivityId);
        activityUserScore.setStatus(0);
        activityUserScore.setIncome(1);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setMilepost(awardConfigDto.getTargetMileage().intValue());
        activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
        activityUserScoreService.save(activityUserScore);
    }

    private void sendCouponAward(AwardConfigDto awardConfigDto, ZnsUserEntity user, Long runActivityId) {
        List<Long> couponIds = NumberUtils.stringToLong2(awardConfigDto.getCouponIds());
        if (CollectionUtils.isEmpty(couponIds)) {
            return;
        }
        for (Long couponId : couponIds) {
            log.info(" 累计跑奖励发送券 activityId =  " + runActivityId + ",userId= " + user.getId() + ",couponId = " + couponId);
            Coupon coupon = couponService.selectCouponById(couponId);
            CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, user.getId());
            UserCoupon userCoupon = BeanUtil.copyBean(currencyAmount, UserCoupon.class);
            userCoupon.setCouponId(coupon.getId());
            userCoupon.setCouponMainType(coupon.getCouponMainType());
            userCoupon.setUserId(user.getId());
            userCoupon.setStatus(5);
            ZonedDateTime now = ZonedDateTime.now();
            if (coupon.getExpiryType() == 1) {
                userCoupon.setGmtStart(now);
                userCoupon.setGmtEnd(DateUtil.addDays(now, coupon.getValidDays()));
            } else if (coupon.getExpiryType() == 2) {
                userCoupon.setGmtStart(coupon.getGmtStart());
                userCoupon.setGmtEnd(coupon.getGmtEnd());
            }
            if (awardConfigDto.getAwardCondition() == 0) {
                userCoupon.setSourceType(6);
            } else {
                userCoupon.setSourceType(8);
            }
            userCoupon.setAmount(currencyAmount.getAmount());
            userCoupon.setDiscount(coupon.getDiscount());
            userCoupon.setIsNew(YesNoStatus.YES.getCode());
            userCoupon.setActivityId(runActivityId);
            userCoupon.setMilepost(awardConfigDto.getTargetMileage() + "");
            userCouponService.insert(userCoupon);
            log.info("券发放完成");
        }


    }

    /**
     * 发放待领取奖励
     *
     * @param awardConfigDto
     * @param user
     * @param sort
     * @param detailId
     * @param runActivityId
     * @param officialCumulativeAward
     */
    private void sendAmountAward(AwardConfigDto awardConfigDto, ZnsUserEntity user, int sort, Long detailId, Long runActivityId, AccountDetailTypeEnum officialCumulativeAward) {
        log.info("里程碑开始发放金额奖励");
        Long userId = user.getId();
        ZnsUserAccountEntity accountEntity = znsUserAccountDao.selectUserAccountByUserId(userId);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        AwardConfigAmountCurrency awardConfigAmountCurrency = activityAwardCurrencyBizService.getAwardConfigAmountCurrency(userId, awardConfigDto);
        BigDecimal amount = awardConfigAmountCurrency.getAmount();
        if (Objects.nonNull(accountEntity)) {
            amount = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), amount);
        }
        ZnsUserAccountDetailEntity userAccountDetailEntity = new ZnsUserAccountDetailEntity();
        userAccountDetailEntity.setAmount(amount);
        userAccountDetailEntity.setActivityId(runActivityId).setRefId(runActivityId).setUserId(userId).setTradeType(officialCumulativeAward.getType())
                .setTradeSubtype(sort + 1).setType(1).setBillNo(billNo).setTradeTime(tradeTime).setDetailsId(detailId)
                .setActivityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType()).setTradeStatus(3);
        userAccountDetailEntity.setTitle(officialCumulativeAward.getName());
        userAccountDetailEntity.setRemark(officialCumulativeAward.getName());
        userAccountDetailEntity.setOtherRemark(officialCumulativeAward.getName());
        userAccountDetailEntity.setIsRobot(user.getIsRobot());
        userAccountDetailEntity.setIsTest(user.getIsTest());
        userAccountDetailEntity.setUserAccountId(accountEntity.getId());
        userAccountDetailService.save(userAccountDetailEntity);
    }

    /**
     * 奖励上传
     *
     * @param userRunDataDetail
     * @param loginUser
     * @param activityId
     */
    public BigDecimal handleBattlePassCumulativeRunActivity(ZnsUserRunDataDetailsEntity userRunDataDetail, ZnsUserEntity loginUser, Long activityId) {
        log.info("累计跑奖励开始发放，用户id：{}，活动id：{}", loginUser.getId(), activityId);
        ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, loginUser.getId());
        if (Objects.isNull(activityUser)) {
            handleUserActivityState(runActivity, 1, loginUser, "", null, true, null, new HandleActivityRequest(), false);
            activityUser = runActivityUserService.findActivityUser(activityId, loginUser.getId());
        }

        //奖励发放
        BigDecimal award = handleOfficialCumulativeRunAward(userRunDataDetail, activityUser, runActivity);
        runActivityUserService.addRunData(activityUser.getId(), runActivity.getCompleteRuleType(), userRunDataDetail.getRunTime(), userRunDataDetail.getRunMileage(), award);
        log.info("里程碑处理结束,,活动id：{}，用户id：{},增加时间：{}，增加距离：{}，增加奖励：{}", activityUser.getActivityId(), userRunDataDetail.getUserId(), userRunDataDetail.getRunTime(), userRunDataDetail.getRunMileage(), award);
        return award;
    }
}
