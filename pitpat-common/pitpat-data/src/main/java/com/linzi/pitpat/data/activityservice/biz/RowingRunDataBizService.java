package com.linzi.pitpat.data.activityservice.biz;


import com.google.common.collect.Lists;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.IpUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.listener.event.RunStartEvent;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsMinuteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataEntity;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.RunDataRunTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsDeviceErrorRecordService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.mongo.Id.SnowflakeId;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserCourseEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserCourseService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.runData.DataProcessingVo;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 划船机运动数据处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RowingRunDataBizService {

    private final ZnsRunActivityService runActivityService;

    private final ZnsTreadmillService treadmillService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ZnsDeviceErrorRecordService znsDeviceErrorRecordService;
    private final RabbitTemplate rabbitTemplate;
    private final ZnsUserService userService;
    private final ZnsCourseService courseService;
    private final MainActivityService mainActivityService;
    private final ISysConfigService sysConfigService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final ZnsUserCourseService userCourseService;
    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    private final RunRankedActivityUserService runRankedActivityUserService;
    private final ZnsUserRunDataService userRunDataService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final MongoTemplate mongoTemplate;

    @Value("${" + RabbitQueueConstants.RUN_DATA_QUEUE + "}")
    private String runDataQueue;
    @Value("${zns.config.rabbitQueue.saveDetailMinute}")
    private String saveDetailMinute;


    public Result<DataProcessingVo> dataProcessing(RunDataRequest runData) {
        DataProcessingVo vo = new DataProcessingVo();
        //参数处理
        Result result = checkParam(runData);
        if (Objects.nonNull(result)) {
            return result;
        }

        Long userId = runData.getId_no();
        try {
            ActivityTypeDto activityTypeDto = runActivityService.getActivityNew(runData.getActivityId());
            if (Objects.nonNull(activityTypeDto)) {
                runData.setMainType(activityTypeDto.getMainType());
            }

            // 获取或保存zns_user_run_data_details数据
            ZnsUserRunDataDetailsEntity userRunDataDetail = getRunDataDetail(runData, userId, activityTypeDto);
            if (Objects.isNull(userRunDataDetail)) {
                log.error("划船机数据上传服务器异常，userRunDataDetail为空，activityId={},id_no={}", runData.getActivityId(), runData.getId_no());
                return CommonResult.success(vo);
            }

            setReturnData(userRunDataDetail, vo);
            runData.setRunDataDetailsId(userRunDataDetail.getId());

            rabbitTemplate.convertAndSend(runDataQueue, JsonUtil.writeString(runData));
        } catch (Exception e) {
            log.error("单车数据上传服务器异常", e);
            return CommonResult.success(vo);
        }
        return CommonResult.success(vo);
    }


    private Result checkParam(RunDataRequest runData) {
        if (Objects.isNull(runData.getOrder_no()) || runData.getOrder_no() <= 0) {
            log.warn("划船机order_no 异常，忽略该数据， activity={}, user_id={}. order_no={}", runData.getActivityId(), runData.getId_no(), runData.getOrder_no());
            return CommonResult.success();
        }
        log.info("划船机数据上传服务器activityId={},id_no={}", runData.getActivityId(), runData.getId_no());
        if (Objects.nonNull(runData.getActivityId()) && (runData.getActivityId() == 1 || runData.getActivityId() == 0)) {
            runData.setActivityId(null);
        }
        if (StringUtils.hasText(runData.getUnique_code())) {
            runData.setUnique_code(runData.getUnique_code().replaceAll("\\u0000", ""));
        }
        return null;
    }


    private ZnsUserRunDataDetailsEntity getRunDataDetail(RunDataRequest runDataRequest, Long userId, ActivityTypeDto activityTypeDto) {
        //查询用户
        ZnsTreadmillEntity treadmillEntity = treadmillService.getTreadmillByUniqueCode(runDataRequest.getUnique_code());
        znsDeviceErrorRecordService.addErrorRecord(treadmillEntity, runDataRequest.getErrorCode());

        Long treadmillId = 0L;
        if (Objects.nonNull(treadmillEntity)) {
            treadmillId = treadmillEntity.getId();
        }
        //当前时间往前30分钟内，重复的order_no都算相同的运动
        ZonedDateTime date = ZonedDateTime.now().minusMinutes(30);

        ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.findByTreadmillIdAndUniqueCode(date, userId, treadmillId, runDataRequest.getOrder_no().toString());
        //半硬件跑后app跑,即使orderno相同也为两条数据
        if (Objects.nonNull(detailsEntity)) {
            if (detailsEntity.getDataSource() == 2 && runDataRequest.getDataSource() == 0) {
            } else {
                return detailsEntity;
            }
        }

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String zoneId = "";
        String ipAddr = "127.0.0.1";
        Integer appVersion = 0;
        if (servletRequestAttributes != null) {
            HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
            // 获取时区
            zoneId = request.getHeader("zoneId");
            // 获取远程IP
            ipAddr = IpUtil.getRemoteIp(request);
            // 获取app 版本号
            String appVersionStr = request.getHeader("appVersion");
            try {
                if (StringUtils.hasText(appVersionStr)) {
                    appVersion = Integer.valueOf(appVersionStr);
                }
            } catch (Exception e) {
                log.error("getRunDataDetail error,e", e);
            }
        }
        // 如果传过来 run_status 为1 ，则不需要创建zns_user_run_data_details 表了
        if (runDataRequest.getRun_status() == 1 && CollectionUtils.isEmpty(runDataRequest.getBikeData())) {
            return null;
        }

        //心率设备
        Integer heartRateDeviceType = null;
        if (!CollectionUtils.isEmpty(runDataRequest.getBikeData())) {
            heartRateDeviceType = runDataRequest.getBikeData().stream().filter(item -> Objects.nonNull(item.getHeartRateDeviceType())).map(ZnsUserRunDataDetailsSecondEntity::getHeartRateDeviceType).findFirst().orElse(null);
        }

        //保存记录
        ZnsUserRunDataDetailsEntity userRunDataDetail = saveNewRunDataDetail(treadmillId,
                userId, runDataRequest.getOrder_no(), runDataRequest.getRouteId(), runDataRequest.getActivityId(),
                runDataRequest.getCourseId(), runDataRequest, runDataRequest.getDataSource(), zoneId, ipAddr, appVersion,
                runDataRequest.getFirmwareVersion(), runDataRequest.getRunType(), heartRateDeviceType, runDataRequest.getTrainingId());

        applicationEventPublisher.publishEvent(new RunStartEvent(this, runDataRequest, userRunDataDetail, activityTypeDto, MDC.get("spanId")));
        return userRunDataDetail;
    }

    // 保存初始运动数据
    public ZnsUserRunDataDetailsEntity saveNewRunDataDetail(Long treadmillId, Long userId, Integer order_no, Long routeId, Long activityId, Long courseId,
                                                            RunDataRequest runDataRequest, Integer dataSource, String zoneId, String ipAddr, Integer appVersion,
                                                            Integer firmwareVersion, Integer runType, Integer heartRateDeviceType, Long trainingId) {
        ZnsUserRunDataDetailsEntity entity = new ZnsUserRunDataDetailsEntity();
        ZnsUserEntity user = userService.findById(userId);
        entity.setTreadmillId(treadmillId);
        entity.setTrainingId(trainingId);
        // 加 IsRobot , IsTest , ActivityType 主要是为了统计数据时用
        if (Objects.nonNull(user)) {
            entity.setIsRobot(user.getIsRobot());
            entity.setIsTest(user.getIsTest());
        }
        entity.setDeviceType(runDataRequest.getDeviceType());
        entity.setActivityType(getActivityType(activityId));
        entity.setUserId(userId);
        entity.setMaxHeartRate(0);              //最大心率(次/分钟)
        entity.setRotateNum(0);
        entity.setTotalTime(0);
        entity.setUniqueCode(order_no.toString());
        String config = sysConfigService.selectConfigByKey("share.data.config");
        Map<String, Object> object = JsonUtil.readValue(config);
        List<String> copyWritingList = JsonUtil.readList(object.get("copyWritingList"), String.class);
        entity.setReportCopywriting(ListUtils.random(copyWritingList));
        entity.setRouteId(routeId);
        entity.setActivityId(activityId);
        entity.setCourseId(courseId);
        if (Objects.nonNull(courseId) && courseId > 0) {
            ZnsCourseEntity course = courseService.selectById(courseId);
            if (Objects.nonNull(course)) {
                entity.setTimeTarget(course.getCourseDuration());
            }
            entity.setUnActivityType(-2);
        } else if ((NumberUtils.geZero(runDataRequest.getDistanceTarget()) || NumberUtils.geZero(runDataRequest.getTimeTarget())) && Objects.isNull(entity.getActivityType())) {
            entity.setUnActivityType(-3);       // 非活动类型，-1：自由跑，-2：课程跑，-3：目标跑
        } else if (NumberUtils.leZero(runDataRequest.getDistanceTarget()) && NumberUtils.leZero(runDataRequest.getTimeTarget()) && Objects.isNull(entity.getActivityType())) {
            entity.setUnActivityType(-1);
        }

        //目标
        if (Objects.nonNull(runDataRequest.getDistanceTarget())) {
            entity.setDistanceTarget(new BigDecimal(runDataRequest.getDistanceTarget()));
        }
        if (Objects.nonNull(runDataRequest.getTimeTarget())) {
            entity.setTimeTarget(runDataRequest.getTimeTarget());
        }

        if (Objects.nonNull(runDataRequest.getTaskId())) {
            entity.setTaskId(runDataRequest.getTaskId());           // 其他活动需要添加taskId
            //任务需要添加目标
            RunActivityUserTask task = runActivityUserTaskService.findById(runDataRequest.getTaskId());
            if (Objects.nonNull(task) && task.getMileageTarget() > 0) {
                entity.setDistanceTarget(new BigDecimal(task.getMileageTarget()));
            }
        }
        entity.setDataSource(dataSource);                           // 数据来源，0：app，1：跑步机 2：半硬件跑
        if (StringUtils.hasText(zoneId)) {
            entity.setZoneId(zoneId);                               //时区
        }
        if (StringUtils.hasText(ipAddr)) {
            entity.setIpAddr(ipAddr);                               //ip地址
        }
        if (Objects.nonNull(firmwareVersion)) {
            entity.setFirmwareVersion(firmwareVersion);             //固件版本号
        }
        entity.setAppVersion(appVersion);                   //app版本号
        entity.setRunType(runType);
        entity.setHeartRateDeviceType(heartRateDeviceType);
        userRunDataDetailsService.save(entity);
        return entity;
    }

    private Integer getActivityType(Long activityId) {
        if (Objects.isNull(activityId)) {
            return null;
        }
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.isNull(mainActivity)) {
            ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
            if (Objects.isNull(runActivity)) {
                return null;
            }
            return runActivity.getActivityType();
        }
        if (MainActivityTypeEnum.OLD.getType().equals(mainActivity.getMainType())) {
            return mainActivity.getOldType();
        }
        if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType();
        } else if (MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.PROP_ACTIVITY.getType();
        } else if (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
            return RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType();
        } else {
            return RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType();
        }

    }

    private void setReturnData(ZnsUserRunDataDetailsEntity userRunDataDetail, DataProcessingVo vo) {
        if (Objects.isNull(userRunDataDetail)) {
            throw new RuntimeException("数据上传服务器失败,数据保存失败");
        }
        vo.setDetailId(userRunDataDetail.getId());

        //课程参与人数返回
        if (Objects.nonNull(userRunDataDetail.getCourseId()) && userRunDataDetail.getCourseId() > 0) {
            //数据走缓存
            ZnsUserCourseEntity userCourse = userCourseService.getUserCourse(userRunDataDetail.getUserId(), userRunDataDetail.getCourseId());
            // 如果是课程跑，则返回实际参与次数
            if (Objects.isNull(userCourse)) {
                vo.setUserPartakeNumber(1);
            } else {
                vo.setUserPartakeNumber(userCourse.getActualNumber());
            }
        }
    }

    /**
     * 保存分钟数据详情
     *
     * @param runDataDetailId
     * @param list
     * @param userId
     * @param runStatus
     * @param activityTypeDto
     */
    public void saveUserRunDataDetailMinute(Long runDataDetailId, List<ZnsUserRunDataDetailsSecondEntity> list, Long userId, Integer runStatus, ActivityTypeDto activityTypeDto) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        ZnsUserRunDataDetailsMinuteEntity entity = new ZnsUserRunDataDetailsMinuteEntity();
        entity.setIsDelete(0);
        entity.setCreateTime(ZonedDateTime.now());
        entity.setRunDataDetailsId(runDataDetailId);
        ZnsUserRunDataDetailsSecondEntity secondEntity = list.get(list.size() - 1);
        entity.setRunMileage(secondEntity.getMileage());
        entity.setRotateNum(secondEntity.getRotateNum());
        entity.setTotalTime(secondEntity.getTotalTime());
        entity.setRunTime(secondEntity.getRunTime());
        //计算平均值
        Double averHeartRate = list.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().orElse(0D);
        Double averPace = list.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getPace).average().orElse(0D);
        Double averTreadFrequency = list.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getTreadFrequency).average().orElse(0D);
        entity.setAverageHeartRate(averHeartRate.intValue());
        entity.setAveragePace(averPace.intValue());
        entity.setAverageTreadFrequency(new BigDecimal(averTreadFrequency.toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
        entity.setAverageVelocity(SportsDataUnit.paceToVelocity(entity.getAveragePace()));
        entity.setDataDetails(list);
        entity.setRunStatus(runStatus);
        entity.setId(SnowflakeId.getId());
        //保存到mongodb
        try {
            if (!isRobot(userId)) {
                userRunDataDetailsSecondService.saveSecondList(list, runDataDetailId, runStatus);
            } else {
                entity.setIsRobot(1);
                if (Objects.nonNull(activityTypeDto)) {
                    entity.setActivityType(activityTypeDto.getActivityType());
                }
                // 将每分钟的数据放到消息队列中
                rabbitTemplate.convertAndSend(saveDetailMinute, JsonUtil.writeString(entity));
            }
        } catch (Exception e) {
            log.error("saveUserRunDataDetailMinute error:{}", e);
        }
    }

    private boolean isRobot(Long userId) {
        if (Objects.isNull(userId) || userId == 0) {
            return true;
        }
        ZnsUserEntity znsUserEntity = userService.findById(userId);
        if (Objects.isNull(znsUserEntity)) {
            return true;
        }
        if (znsUserEntity.getIsRobot() == 1) {
            return true;
        }
        return false;
    }

    /**
     * 保存userRunDataDetail数据
     *
     * @param userRunDataDetail
     * @param runDataRequest
     * @param activityTypeDto
     * @param detailsSecondEntities
     * @return
     */
    public ZnsUserRunDataDetailsEntity saveUserRunDataDetail(ZnsUserRunDataDetailsEntity userRunDataDetail, RunDataRequest runDataRequest, ActivityTypeDto activityTypeDto, List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities) {
        log.info("rowing saveUserRunDataDetail start,detailsId:{}", userRunDataDetail.getId());
        Integer run_status = runDataRequest.getRun_status();
        Integer firmwareVersion = runDataRequest.getFirmwareVersion();
        Integer dataSource = runDataRequest.getDataSource();

        userRunDataDetail.setModifieTime(ZonedDateTime.now());
        userRunDataDetail.setLastTime(ZonedDateTime.now());
        // 如果runStatus状态的值为非1，【运动状态，默认0,0：运动中，1：结束，2：暂停】，则可以修改run_status状态
        if (userRunDataDetail.getRunStatus() != 1) {
            userRunDataDetail.setRunStatus(run_status);
        }
        userRunDataDetail.setIsDelete(0);
        userRunDataDetail.setFirmwareVersion(firmwareVersion);
        // 不满足一分钟的数据不展示，如果还没有跑一分钟，如果用户没有跑完一分钟就结束了，则需要将zns_user_run_data_details 表中的数据删除掉
        if (userRunDataDetail.getRunStatus() == 1 && userRunDataDetail.getRunTime() < 60) {
            userRunDataDetail.setIsDelete(1);
        }
        //只要有app上传数据就算app,半硬件跑不算
        if (dataSource != 1 && userRunDataDetail.getDataSource() == 1) {
            userRunDataDetail.setDataSource(dataSource);
        }
        ZnsUserEntity userEntity = userService.findById(userRunDataDetail.getUserId());

        //结束运动再计算平均值，跑步数据上传结束
        if (userRunDataDetail.getRunStatus() == 1) {
            // 设置运行的总毫秒值
            if (Objects.isNull(userRunDataDetail.getRunTimeMillisecond()) || userRunDataDetail.getRunTimeMillisecond() == 0) {
                userRunDataDetail.setRunTimeMillisecondLargeThenOneSecond(userRunDataDetail.getRunTime() * 1000 + 999);
            }
            // 计算运动时间，单位为秒
            BigDecimal runTime = BigDecimalUtil.divHalfUp(new BigDecimal(userRunDataDetail.getRunTimeMillisecond()), new BigDecimal(1000), 2);
            // 获取目标跑步距离
            BigDecimal runMileage = Objects.nonNull(activityTypeDto) && Arrays.asList(1, 4).contains(activityTypeDto.getActivityType()) ? userRunDataDetail.getDistanceTarget() : userRunDataDetail.getRunMileage();
            // 获取用户跑步距离，
            // a 假如用户目标距离是 1600 米， 假如用户跑了 1500 米，则用 1500 来计算
            // b 假如用户目标距离是 1600 米，用户跑了1602米，则用1600 来计算
            if (runMileage.compareTo(BigDecimal.ZERO) <= 0 || userRunDataDetail.getRunMileage().compareTo(userRunDataDetail.getDistanceTarget()) < 0) {
                runMileage = userRunDataDetail.getRunMileage();
            }
            // 计算 平均配速(秒/公里)，500米配速
            userRunDataDetail.setAveragePace(SportsDataUnit.getPace(runTime, BigDecimalUtil.divHalfUp(runMileage, new BigDecimal(500), 4)));
            // 平均速度(公里/小时)
            userRunDataDetail.setAverageVelocity(SportsDataUnit.getVelocity(runTime, runMileage));
            // 平均桨频（圈/分钟）
            userRunDataDetail.setAverageTreadFrequency(SportsDataUnit.getTreadFrequency(runTime, userRunDataDetail.getRotateNum()));
            //速度异常处理
            if (Objects.equals(userEntity.getIsRobot(), 1) && userRunDataDetail.getAverageVelocity().compareTo(Constants.MAX_ROBOT_AVERAGE_VELOCITY) > 0) {
                userRunDataDetail.setIsDelete(1);
            }
            // 计算平均值, 拿出用户的每秒跑步数据
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(detailsSecondEntities)) {
                detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(userRunDataDetail.getId());
            }
            Double averFallingGradient = 0.0;

            if (!CollectionUtils.isEmpty(detailsSecondEntities)) {
                Double averHeartRate = detailsSecondEntities.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().orElse(0D);
                // 平均心率(次/分钟)
                userRunDataDetail.setAverageHeartRate(averHeartRate.intValue());
                //运动详情设置心率区间
                userRunDataDetailsService.setDetailHeartRateTimeMap(userRunDataDetail, detailsSecondEntities, userEntity);
                //爬坡相关计算 , 过滤出所有 坡度 大于 0 的 每秒数据
//                List<ZnsUserRunDataDetailsSecondEntity> climbingList = detailsSecondEntities.stream().filter(s -> Objects.nonNull(s.getGradient()) && s.getGradient() > 0).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(climbingList)) {
//                    // 获取平均坡度
//                    averFallingGradient = climbingList.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getGradient).average().orElse(0D);
//                    // 计算总跑步米数
//                    BigDecimal airlineDistance = SportsDataUnit.getComputeMileage(climbingList.size(), userRunDataDetail.getAverageVelocity());
//                    //爬坡距离（m） = 总跑步米数 *  平均坡度
//                    userRunDataDetail.setClimbingMileage(SportsDataUnit.getClimbingMileage(averFallingGradient, airlineDistance));
//                }
                //获取心率类型
                userRunDataDetail.setHeartRateDeviceType(detailsSecondEntities.get(0).getHeartRateDeviceType());
            }
            //计算步频，每分钟走多少步
//            int stepFrequency = 0;
//            if (Objects.nonNull(userRunDataDetail.getRunTime()) && userRunDataDetail.getRunTime() > 0) {
//                stepFrequency = userRunDataDetail.getStepNum() * 60 / userRunDataDetail.getRunTime();
//            }
//            userRunDataDetail.setAverageStepFrequency(stepFrequency);
            // 平均步幅(cm)  =  距离(米) * 100  / 步数
//            Integer averageStride = SportsDataUnit.getStride(userRunDataDetail.getStepNum(), userRunDataDetail.getRunMileage());
//            userRunDataDetail.setAverageStride(averageStride);
            //计算能力值 =  0.2*速度(m/min)+0.9*速度(m/min)*坡度(%)-3.5
            userRunDataDetail.setCapabilityValue(SportsDataUnit.getCapabilityValue(userRunDataDetail.getRunMileage(), userRunDataDetail.getRunTime()));
            //预计卡路里计算 = (体重【默认为80】 * 米 / 1000) * 1.036
            Integer calorieTarget = SportsDataUnit.getCalorieTarget(userRunDataDetail.getDistanceTarget(), userRunDataDetail.getTimeTarget(), userRunDataDetail.getAveragePace(), userEntity.getWeight());
            userRunDataDetail.setCalorieTarget(calorieTarget);
            // 脂肪消耗 = Kilocalorie * 1000 / 7700
            userRunDataDetail.setFatConsumption(SportsDataUnit.getFatConsumption(userRunDataDetail.getKilocalorie()));
            // 走步文案保存 , runType 【1. 跑步， 2 走步】
            if (Integer.valueOf(2).equals(userRunDataDetail.getRunType())) {
                // todo 划船机不处理
//                Integer mileageRank = userRunDataService.getMileageRank(userRunDataDetail.getUserId(), runMileage, userRunDataDetail.getRunType());
//                // 设置当前的走步排名或跑步排名
//                userRunDataDetail.setRunMileageRanking(mileageRank);
//                if (Objects.nonNull(mileageRank)) {
//                    // 查询总用户数
//                    long count = userRunDataService.count();
//                    BigDecimal divide = new BigDecimal(mileageRank).divide(new BigDecimal(count), 2, BigDecimal.ROUND_DOWN);
//                    BigDecimal mileageExceedPercentage = divide.multiply(new BigDecimal(100));
//                    // 里程超过百分比
//                    userRunDataDetail.setMileageExceedPercentage(mileageExceedPercentage);
//                }
            }
        }
        userRunDataDetail.setIsCountMilestone(null);
        // 毫秒处理，防止为0
        if (userRunDataDetail.getRunTimeMillisecond() == 0) {
            userRunDataDetail.setRunTimeMillisecondLargeThenOneSecond(userRunDataDetail.getRunTime() * 1000 + 999);
        }
        userRunDataDetailsService.updateByIdForce(userRunDataDetail);
        if (userRunDataDetail.getIsDelete() == 1) {
            if (Objects.nonNull(activityTypeDto) && Objects.equals(activityTypeDto.getMainType(), MainActivityTypeEnum.RANK.getType())) {
                //段位赛的活动也需要同步删除
                runRankedActivityUserService.deleteRunRankedActivityUser(userRunDataDetail.getId());
            }
        }
        return userRunDataDetail;
    }


    /**
     * 用户跑步数据总表数据处理
     *
     * @param userId
     * @param user
     */
    public void userRunDataHandle(Long userId, ZnsUserEntity user) {
        if (Objects.isNull(userId) || Objects.isNull(user)) {
            return;
        }
        //单车数据
        ZnsUserRunDataEntity userRunData = userRunDataDetailsService.getUserRunData(userId, Lists.newArrayList(RunDataRunTypeEnum.ROWING.getCode()));
        ZnsUserRunDataEntity runData = userRunDataService.findUserRunDataByUserId(userId, EquipmentDeviceTypeEnum.ROWING.getCode());
        if (Objects.isNull(runData)) {
            runData = new ZnsUserRunDataEntity();
            runData.setDeviceType(EquipmentDeviceTypeEnum.ROWING.getCode());
            runData.setUserId(userId);
            runData.setEmailAddress(user.getEmailAddressEn());
            runData.setNickname(user.getFirstName());
            runData.setRunMileage(BigDecimal.ZERO);
            runData.setRunCount(1);
        }
        setRunData(userRunData, runData);

        runData.setModifieTime(ZonedDateTime.now());

        Integer runTime = Objects.isNull(runData.getRunTime()) ? 0 : runData.getRunTime();
        BigDecimal totalRunMileage = Objects.isNull(runData.getRunMileage()) ? BigDecimal.ZERO : runData.getRunMileage();
        Integer totalHeartBeat = Objects.isNull(runData.getTotalHeartBeat()) ? 0 : runData.getTotalHeartBeat();

        runData.setAveragePace(SportsDataUnit.getPace(runTime, BigDecimalUtil.divHalfUp(totalRunMileage, new BigDecimal(500), 2)));
        runData.setAverageVelocity(SportsDataUnit.getVelocity(runTime, totalRunMileage));

        runData.setAverageHeartRate(runTime == 0 ? 0 : totalHeartBeat / runTime);
        if (Objects.isNull(runData.getId())) {
            userRunDataService.save(runData);
        } else {
            userRunDataService.update(runData);
        }
    }

    /**
     * 设置跑步数据
     *
     * @param sourceUserRunData
     * @param targetUserRunData
     */
    private void setRunData(ZnsUserRunDataEntity sourceUserRunData, ZnsUserRunDataEntity targetUserRunData) {
        if (Objects.isNull(sourceUserRunData) || Objects.isNull(targetUserRunData)) {
            return;
        }
        targetUserRunData.setRunCount(sourceUserRunData.getRunCount());
        targetUserRunData.setRunTime(sourceUserRunData.getRunTime());
        targetUserRunData.setRunMileage(sourceUserRunData.getRunMileage());
        targetUserRunData.setKilocalorie(sourceUserRunData.getKilocalorie());
        targetUserRunData.setTotalHeartBeat(sourceUserRunData.getTotalHeartBeat());
        if (NumberUtils.geZero(sourceUserRunData.getMaxPace())) {
            targetUserRunData.setMaxPace(sourceUserRunData.getMaxPace());
        }
        if (NumberUtils.geZero(sourceUserRunData.getOptimumPace())) {
            targetUserRunData.setOptimumPace(sourceUserRunData.getOptimumPace());
        }
        targetUserRunData.setMaxCapabilityValue(sourceUserRunData.getMaxCapabilityValue());
        targetUserRunData.setMaxKilocalorie(sourceUserRunData.getMaxKilocalorie());
        targetUserRunData.setMaxMileageSingleDay(sourceUserRunData.getMaxMileageSingleDay());
        targetUserRunData.setMaxRunTime(sourceUserRunData.getMaxRunTime());
    }



}
