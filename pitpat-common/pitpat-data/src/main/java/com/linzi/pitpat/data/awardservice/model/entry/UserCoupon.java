package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * 用户优惠券表
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

@Data
@NoArgsConstructor
@TableName("zns_user_coupon")
@Accessors(chain = true)
public class UserCoupon implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserCoupon:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                             // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";              // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";          // 最后修改时间
    public final static String user_id = CLASS_NAME + "user_id";                    // 用户id
    public final static String coupon_id = CLASS_NAME + "coupon_id";                // 优惠券id
    public final static String gmt_start = CLASS_NAME + "gmt_start";                // 有效开始时间
    public final static String gmt_end = CLASS_NAME + "gmt_end";                    // 有效截至时间
    public final static String status_ = CLASS_NAME + "status";                     // 状态 【 0:未使用 ， 1:使用中 2:已使，3:过期 4:已失效，5: 待领取 】
    public final static String is_new = CLASS_NAME + "is_new";                      // 新卷标识 1 是 0 否
    public final static String gmt_use = CLASS_NAME + "gmt_use";                    // 使用的时间
    public final static String source_type = CLASS_NAME + "source_type";            // 获取来源【1：兑换,2:植树节免费领取3.后台发放 4.助力活动,5新人任务 6:活动获得 7:新手引导 8：活动进阶获得】
    public final static String amount_ = CLASS_NAME + "amount";                     // 券金额
    public final static String discount_ = CLASS_NAME + "discount";                 // 券折扣
    public final static String remarks_ = CLASS_NAME + "remarks";                   // 标记备注
    public final static String equipment_no = CLASS_NAME + "equipment_no";          // 设备号
    public final static String activity_id = CLASS_NAME + "activity_id";            // 使用优惠券的时候对应的活动id
    public final static String milepost_ = CLASS_NAME + "milepost";                 // 里程id
    public final static String use_activity_id = CLASS_NAME + "use_activity_id";    // 哪个活动id 使用
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //用户id
    private Long userId;
    //优惠券id
    private Long couponId;
    //有效开始时间
    private ZonedDateTime gmtStart;
    //有效截至时间
    private ZonedDateTime gmtEnd;
    /**
     * 状态 【 0:未使用 ， 1:使用中 2:已使，3:过期 4:已失效 5:待领取】
     *
     * @see UserCouponConstant.UserCouponStatusEnum
     */
    private Integer status;

    //新卷标识 1 是 0 否
    private Integer isNew;
    //使用的时间
    private ZonedDateTime gmtUse;

    /**
     * 优惠券主类型  1：赛事券，2：商城券
     *
     * @see CouponConstant.CouponMainTypeEnum
     */
    private Integer couponMainType;

    /**
     * 获取来源：
     * 1：兑换,2:植树节免费领取3.后台发放 4.助力活动,5新人任务 6:活动获得 7:新手引导
     * 8：活动进阶获得,9：购买 ,30 平台发放,100 新活动奖励起始位置,101：排名基础奖励,102：完赛,103：被挑战奖励,
     * 104：挑战成功奖励,105:挑战失败奖励,106：排名人头奖励,107：达到目标时间奖励,108：达到目标里程奖励,109:发起奖励,
     * 110:参与奖励,111:胜者奖励,112:奖金池基础,113:奖金池占比,114:段位奖励,115:时间奖励,116:里程奖励,117:瓜分金额,118:报名奖励,
     * 200：商城手动领取
     *
     * @see CouponConstant.SourceTypeEnum
     */
    private Integer sourceType;

    //券金额
    private BigDecimal amount;
    //货币名称
    private String currencyName;
    /**
     * 货币code，USD：美元，CAD：加币，EUR：欧元，GBP：英镑
     *
     * @see I18nConstant.CurrencyCodeEnum
     */
    private String currencyCode;
    //币种符号
    private String currencySymbol;
    //券折扣
    private BigDecimal discount;
    //标记备注
    private String remarks;
    //设备号
    private String equipmentNo;
    //使用优惠券的时候对应的活动id
    private Long activityId;
    //里程id
    private String milepost;
    //哪个活动id 使用
    private Long useActivityId;
    /**
     * 来源活动id
     */
    private Long sourceActivityId;

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    @Override
    public String toString() {
        return "UserCoupon{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",userId=" + userId +
                ",couponId=" + couponId +
                ",gmtStart=" + gmtStart +
                ",gmtEnd=" + gmtEnd +
                ",status=" + status +
                ",isNew=" + isNew +
                ",gmtUse=" + gmtUse +
                ",sourceType=" + sourceType +
                ",amount=" + amount +
                ",discount=" + discount +
                ",remarks=" + remarks +
                ",equipmentNo=" + equipmentNo +
                ",activityId=" + activityId +
                ",milepost=" + milepost +
                ",useActivityId=" + useActivityId +
                "}";
    }

    public UserCoupon(Long id) {
        this.id = id;
    }
}
