package com.linzi.pitpat.data.activityservice.dto.api.response.activity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class SeriesActivityRankDto {

    //用户id
    private Long userId;
    //排名
    private Integer rank;
    //昵称
    private String nickname;
    //平均速度
    private BigDecimal averageVelocity;
    //跑步用时(ms)
    private Integer runTime;
    //运动里程（m）
    private Integer runMileage;
    //完成时间
    private ZonedDateTime completeTime;
    //是否完成比赛:0未完成，1表示完成(不代表比赛完成，只是展示成绩) 2标识退赛
    private Integer completeState;
    /**
     * 默认 0 非作弊 1 作弊
     *
     * @see com.linzi.pitpat.data.enums.YesNoStatus
     */
    private Integer cheatingState;

    //设备模型
    private String equipmentModel;
    /**
     * 在这场活动得到的金额
     */
    private BigDecimal bonus;
    /**
     * 参赛时间
     */
    private ZonedDateTime entryActivityTime;
    //竞技分
    private Integer competitiveScore = 0;

}
