package com.linzi.pitpat.data.bussiness;

import com.linzi.pitpat.data.messageservice.model.entity.ReachPushEmail;
import com.linzi.pitpat.data.messageservice.model.query.ReachPushQuery;
import com.linzi.pitpat.data.messageservice.service.ReachPushEmailService;
import com.linzi.pitpat.data.userservice.model.entity.UserRegisterFailRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserLoginLogEntity;
import com.linzi.pitpat.data.userservice.model.entity.shopify.ShopifyUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUser;
import com.linzi.pitpat.data.userservice.model.query.EmailSendingRecordQuery;
import com.linzi.pitpat.data.userservice.model.query.ShopifyUserQuery;
import com.linzi.pitpat.data.userservice.model.query.UserLoginLogQuery;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserQuery;
import com.linzi.pitpat.data.userservice.service.UserRegisterFailRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component("batchEncryptTask")
public class BatchEncryptTask {

    @Autowired
    private ZnsUserService znsUserService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ShopifyUserService shopifyUserService;
    @Autowired
    private VipUserService vipUserService;
    @Autowired
    private ZnsUserLoginLogService znsUserLoginLogService;
    @Autowired
    private ZnsUserEmailSendingRecordService znsUserEmailSendingRecordService;
    @Autowired
    private UserRegisterFailRecordService userRegisterFailRecordService;

    @Autowired
    private ReachPushEmailService reachPushEmailService;

    private static final int PAGE_SIZE = 500;

    public void run(int startId, int endId) {
        // 按id进行范围查询
        UserQuery query = UserQuery.builder().startUserId((long) startId).endUserId((long) endId).build();
        Long userInfoCount = znsUserService.findCount(query);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            query.setLastStr(" limit " + finalI * PAGE_SIZE + "," + PAGE_SIZE);
            List<ZnsUserEntity> znsUserEntities = znsUserService.findList(query);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (ZnsUserEntity znsUserEntity : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(znsUserEntity.getEmailAddressEn())) {
                            continue;
                        }
                        znsUserEntity.setEmailAddressEn(znsUserEntity.getEmailAddress());
                        znsUserService.update(znsUserEntity);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", znsUserEntity.getEmailAddressEn(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }


    public void run1(int startId, int endId) {
        // 按id进行范围查询
        ShopifyUserQuery query = ShopifyUserQuery.builder().startId((long) startId).endId((long) endId).build();
        long userInfoCount = shopifyUserService.findCount(query);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            query.setLastStr(" limit " + finalI * PAGE_SIZE + "," + PAGE_SIZE);
            List<ShopifyUserEntity> znsUserEntities = shopifyUserService.findList(query);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (ShopifyUserEntity shopifyUserEntity : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(shopifyUserEntity.getEmailAddressEn())) {
                            continue;
                        }
                        shopifyUserEntity.setEmailAddressEn(shopifyUserEntity.getEmail().toLowerCase());
                        shopifyUserService.update(shopifyUserEntity);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", shopifyUserEntity.getEmail(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }

    public void run2(int startId, int endId) {
        // 按id进行范围查询
        VipUserQuery query = VipUserQuery.builder().geStartId((long) startId).leEndId((long) endId).build();
        long userInfoCount = vipUserService.findCount(query);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            query.setLastStr(" limit " + finalI * PAGE_SIZE + "," + PAGE_SIZE);
            List<VipUser> znsUserEntities = vipUserService.finList(query);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (VipUser vipUser : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(vipUser.getEmailAddressEn())) {
                            continue;
                        }
                        vipUser.setEmailAddressEn(vipUser.getEmailAddress().toLowerCase());
                        vipUserService.update(vipUser);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", vipUser.getEmailAddress(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }

    public void run3(Integer startId, Integer endId) {
        // 按id进行范围查询
        UserQuery query = UserQuery.builder().startUserId((long) startId).endUserId((long) endId).build();
        Long userInfoCount = znsUserService.findCount(query);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            query.setLastStr(" limit " + finalI * PAGE_SIZE + "," + PAGE_SIZE);
            List<ZnsUserEntity> znsUserEntities = znsUserService.findList(query);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (ZnsUserEntity znsUserEntity : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(znsUserEntity.getUserCode())) {
                            continue;
                        }
                        znsUserService.setUserCode(znsUserEntity, 1);
                        znsUserService.update(znsUserEntity);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", znsUserEntity.getEmailAddressEn(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }

    public void run4(Integer startId, Integer endId) {
        // 按id进行范围查询
        UserLoginLogQuery query = UserLoginLogQuery.builder().startId((long) startId).endId((long) endId).build();
        long userInfoCount = znsUserLoginLogService.findCount(query);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            query.setLastStr(" limit " + finalI * PAGE_SIZE + "," + PAGE_SIZE);
            List<ZnsUserLoginLogEntity> znsUserEntities = znsUserLoginLogService.findList(query);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (ZnsUserLoginLogEntity znsUserLoginLog : znsUserEntities) {
                    try {
                        if (znsUserLoginLog.getUserId() != 0) {
                            continue;
                        }
                        ZnsUserEntity userByEmail = znsUserService.findByEmail(znsUserLoginLog.getEmailAddress());
                        if (Objects.isNull(userByEmail)) {
                            continue;
                        }
                        znsUserLoginLog.setUserId(userByEmail.getId());
                        znsUserLoginLogService.update(znsUserLoginLog);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", znsUserLoginLog.getEmailAddress(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }


    public void run5(int startId, int endId) {
        // 按id进行范围查询
        EmailSendingRecordQuery query = EmailSendingRecordQuery.builder().startId((long) startId).endId((long) endId).build();
        long userInfoCount = znsUserEmailSendingRecordService.findCountByQuery(query);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            query.setLastStr(" limit " + finalI * PAGE_SIZE + "," + PAGE_SIZE);
            List<ZnsUserEmailSendingRecordEntity> znsUserEntities = znsUserEmailSendingRecordService.findList(query);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (ZnsUserEmailSendingRecordEntity shopifyUserEntity : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(shopifyUserEntity.getEmailAddressEn())) {
                            continue;
                        }
                        shopifyUserEntity.setEmailAddressEn(shopifyUserEntity.getEmailAddress());
                        znsUserEmailSendingRecordService.update(shopifyUserEntity);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", shopifyUserEntity.getEmailAddress(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }

    public void run6(int startId, int endId) {
        // 按id进行范围查询
        long userInfoCount = userRegisterFailRecordService.findCountByStartIdAndEndId(startId, endId);
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            int pageStart = finalI * PAGE_SIZE;
            String lastStr = " limit " + pageStart + "," + PAGE_SIZE;
            List<UserRegisterFailRecordEntity> znsUserEntities = userRegisterFailRecordService.findListByStartIdAndEndId(startId, endId, lastStr);
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", pageStart, PAGE_SIZE);
                for (UserRegisterFailRecordEntity shopifyUserEntity : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(shopifyUserEntity.getEmailAddressEn())) {
                            continue;
                        }
                        shopifyUserEntity.setEmailAddressEn(shopifyUserEntity.getEmailAddress());
                        userRegisterFailRecordService.update(shopifyUserEntity);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", shopifyUserEntity.getEmailAddress(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }

    public void run7(Integer startId, Integer endId) {
        // 按id进行范围查询

        long userInfoCount = reachPushEmailService.countById(ReachPushQuery.builder().startId(startId).endId(endId).build());
        long totalPageNum = (userInfoCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("BatchEncryptTask run start totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
        if (userInfoCount == 0) {
            return;
        }
        for (int i = 0; i < totalPageNum; i++) {
            // 分页分批获取用户信息
            int finalI = i;
            // 查询库信息
            List<ReachPushEmail> znsUserEntities = reachPushEmailService.findList(ReachPushQuery.builder().startId(startId).endId(endId).finalI(finalI).build());
            taskExecutor.execute(() -> {
                log.info("taskExecutor 执行业务数据更新start:{},num:{}", finalI * PAGE_SIZE, PAGE_SIZE);
                for (ReachPushEmail shopifyUserEntity : znsUserEntities) {
                    try {
                        if (StringUtils.hasText(shopifyUserEntity.getEmailAddressEn())) {
                            continue;
                        }
                        shopifyUserEntity.setEmailAddressEn(shopifyUserEntity.getEmail());
                        reachPushEmailService.update(shopifyUserEntity);
                    } catch (Exception e) {
                        log.error("更新用户en emailAddress :{} 信息错误", shopifyUserEntity.getEmailAddressEn(), e);
                    }
                }
                log.info("taskExecutor 执行业务数据同步 end time:{}", ZonedDateTime.now());
            });
        }
        log.info("taskExecutor run end totalRecord:{} ,totalPageNum:{} ,time:{}", userInfoCount, totalPageNum, ZonedDateTime.now());
    }
}
