package com.linzi.pitpat.data.clubservice.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_club_activity_invite")
public class ClubActivityInvite implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    //是否逻辑删除，0：未删除
    @TableLogic(delval = "UNIX_TIMESTAMP()")
    private Long isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;
    //加入的俱乐部
    private Long clubId;
    //申请人id
    private Long inviteUserId;
    //受邀人
    private Long inviteeUserId;
    //活动主id
    private Long mainActivityId;
    //活动子表id
    private Long subActivityId;

    private Long activityTeamId;

}
