package com.linzi.pitpat.data.awardservice.biz;

import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponExchangeFailEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商城券转换组件
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MallCouponConvertComponent {

    private final UserExtraBizService userExtraBizService;
    private final UserCouponService userCouponService;
    private final CouponI18nService couponI18nService;
    private final CouponCurrencyService couponCurrencyService;


    /**
     * app批量转换优惠券dto
     */
    public List<MallCouponDto> appConvertDto(List<Coupon> couponList, ZnsUserEntity user, Boolean isOrderConvert, String currencyCode, String mallCountryCode) {
        return convertMallCouponDto(currencyCode, couponList, user, isOrderConvert, mallCountryCode);
    }

    /**
     * app单个转换优惠券dto
     */
    public MallCouponDto appConvertDto(Coupon coupon, ZnsUserEntity user, Boolean isUserCouponConvert, String mallCountryCode) {
        String currencyCode = userExtraBizService.findUserCurrencyCode(user.getId(), mallCountryCode);
        List<MallCouponDto> dtos = convertMallCouponDto(currencyCode, List.of(coupon), user, isUserCouponConvert, mallCountryCode);
        if (CollectionUtils.isEmpty(dtos)) {
            return null;
        }
        return dtos.get(0);
    }

    /**
     * 转换优惠券Dto
     *
     * @param isUserCouponConvert : 是否用户券转换，true：是-不校验领取状态（不符合不会跳过），false：不是-校验领取状态（不符合的会跳过）
     */
    private List<MallCouponDto> convertMallCouponDto(String currencyCode, List<Coupon> couponList, ZnsUserEntity user, Boolean isUserCouponConvert, String mallCountryCode) {
        //查询用户优惠券、语言
        List<Long> couponIds = couponList.stream().map(Coupon::getId).distinct().toList();
        String languageCode = null;
        List<UserCoupon> userCoupons = null;
        if (user != null) {
            languageCode = user.getLanguageCode();
            UserCouponQuery query = new UserCouponQuery().setCouponIds(couponIds).setUserId(user.getId());
            userCoupons = userCouponService.findListByQuery(query);
        }

        //新券标识map，val > 1 就是新券
        Map<Long, Integer> newMap = new HashMap<>();
        //最早失效时间的券 key -> couponId,val -> UserCoupon
        Map<Long, UserCoupon> userCouponMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userCoupons)) {
            // newMap = userCoupons.stream().collect(Collectors.groupingBy(UserCoupon::getCouponId, Collectors.summingInt(UserCoupon::getIsNew))); （新券前端实现，暂时不管）
            userCouponMap = userCoupons.stream().filter(item -> UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type.equals(item.getStatus()))
                    .filter(item -> item.getGmtStart().isBefore(ZonedDateTime.now()) && item.getGmtEnd().isAfter(ZonedDateTime.now()))
                    .collect(Collectors.toMap(UserCoupon::getCouponId, Function.identity(), (k1, k2) -> k1.getGmtEnd().isBefore(k2.getGmtEnd()) ? k1 : k2));
        }

        //查询优惠券I18N
        Map<Long, CouponI18n> i18nMap = new HashMap<>();
        if (StringUtils.hasText(languageCode)) {
            CouponI18nQuery query = CouponI18nQuery.builder().couponIds(couponIds).langCode(languageCode).build();
            List<CouponI18n> list = couponI18nService.findListByQuery(query);
            if (!CollectionUtils.isEmpty(list)) {
                i18nMap = list.stream().collect(Collectors.toMap(CouponI18n::getCouponId, Function.identity(), (k1, k2) -> k1));
            }
        }

        //获取用户优惠券领取状态
        Map<Long, Integer> receiveMap = new HashMap<>();
        if (!isUserCouponConvert) {
            //非用户券转换,需要校验领取状态（订单是直接取用户的券）
            receiveMap = batchReceiveStatus(couponList, userCoupons, false, false);
        }

        //查询券币种
        Map<Long, CouponCurrencyEntity> couponCurrencyMap;
        if (!isUserCouponConvert){
            //非用户券转换,使用默认币种
            couponCurrencyMap = couponCurrencyService.findCouponCurrencyMapOrDefault( couponList, currencyCode);
        }else {
            //用户券转换,没有币种金额使用第一个
            couponCurrencyMap = couponCurrencyService.findUserCouponCurrencyMap( couponList, currencyCode);
        }

        //组装返回数据
        List<MallCouponDto> result = new ArrayList<>();
        mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(user, mallCountryCode);
        for (Coupon coupon : couponList) {
            Long couponId = coupon.getId();
            if (!coupon.getCountryCodes().contains(mallCountryCode) && !isUserCouponConvert) {
                //用户国家不支持
                continue;
            }
            CouponCurrencyEntity couponCurrency = couponCurrencyMap.get(couponId);
            if (Objects.isNull(couponCurrency) && !isUserCouponConvert) {
                //币种不支持
                continue;
            }
            //领取状态，1：可领取，2：已领取，3：已发完
            Integer receiveStatus = receiveMap.getOrDefault(couponId, UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_2.type);
            Integer isNew = newMap.getOrDefault(couponId, 0) > 1 ? 1 : 0;
            CouponI18n couponI18n = i18nMap.get(couponId);
            MallCouponDto mallCouponDto = new MallCouponDto(receiveStatus, isNew, couponI18n, coupon, couponCurrency);
            Long userCouponId = Optional.ofNullable(userCouponMap.get(couponId)).map(UserCoupon::getId).orElse(null);
            mallCouponDto.setUserCouponId(userCouponId);
            result.add(mallCouponDto);
        }
        return result;
    }


    /**
     * 用户发券校验
     */
    public CouponExchangeFailEnum checkSendCoupon(Coupon coupon, ZnsUserEntity user, Boolean isAdmin, Boolean isRetainPop) {
        Long userId = user.getId();
        Long couponId = coupon.getId();
        if (!CouponConstant.CouponStatusEnum.STATUS_1.type.equals(coupon.getStatus())) {
            log.info("[sendCouponToUser],给用户发券,优惠券状态已失效,couponId={},userId={}", couponId, userId);
            return CouponExchangeFailEnum.COUPON_INVALID;
        }
        Integer receiveStatus = getReceiveStatus(coupon, user, isAdmin, isRetainPop);
        if (UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_3.type.equals(receiveStatus)) {
            //券不可领取（优惠劵库存不足）
            log.info("[sendCouponToUser],给用户发券,优惠劵库存不足,couponId={},userId={},isAdmin={}", couponId, userId, isAdmin);
            return CouponExchangeFailEnum.INSUFFICIENT_COUPON_STOCK;
        }
        if (UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_2.type.equals(receiveStatus)) {
            //券不可领取（达到单人上限）
            log.info("[sendCouponToUser],给用户发券,超过单人领取上限,couponId={},userId={},isAdmin={}", couponId, userId, isAdmin);
            return CouponExchangeFailEnum.EXCEED_USER_MAX;
        }
        if (Objects.nonNull(coupon.getReceiveStart()) && ZonedDateTime.now().toInstant().toEpochMilli() < coupon.getReceiveStart().toInstant().toEpochMilli()) {
            log.info("[sendCouponToUser],给用户发券,未到领取时间,couponId={},userId={}", couponId, userId);
            return CouponExchangeFailEnum.NOT_START;
        }
        if (Objects.nonNull(coupon.getReceiveEnd()) && ZonedDateTime.now().toInstant().toEpochMilli() >= coupon.getReceiveEnd().toInstant().toEpochMilli()) {
            log.info("[sendCouponToUser],给用户发券,领取已过期,couponId={},userId={}", couponId, userId);
            return CouponExchangeFailEnum.COUPON_EXPIRED;
        }
        return null;
    }

    /**
     * 获取用户优惠券领取状态
     *
     * @param isAdmin     : 是否运营后台，true：是-不校验发放数量，false：不是-校验发放数量
     * @param isRetainPop : 是否挽留弹窗领取，true：是-不校验单人领取上限，false：不是-校验单人领取上限
     * @see UserCouponConstant.ReceiveStatusEnum
     */
    private Integer getReceiveStatus(Coupon coupon, ZnsUserEntity user, Boolean isAdmin, Boolean isRetainPop) {
        UserCouponQuery query = new UserCouponQuery().setCouponId(coupon.getId()).setUserId(user.getId());
        List<UserCoupon> userCoupons = userCouponService.findListByQuery(query);
        Map<Long, Integer> receiveMap = batchReceiveStatus(List.of(coupon), userCoupons, isAdmin, isRetainPop);
        return receiveMap.getOrDefault(coupon.getId(), UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_2.type);
    }

    /**
     * 批量获取用户优惠券领取状态
     *
     * @param isAdmin     : 是否运营后台，true：是-不校验发放数量，false：不是-校验发放数量
     * @param isRetainPop : 是否挽留弹窗领取，true：是-不校验单人领取上限，false：不是-校验单人领取上限
     * @see UserCouponConstant.ReceiveStatusEnum
     */
    public Map<Long, Integer> batchReceiveStatus(List<Coupon> couponList, List<UserCoupon> userCoupons, Boolean isAdmin, Boolean isRetainPop) {
        List<Long> couponIds = couponList.stream().map(Coupon::getId).distinct().toList();

        Map<Long, Long> userCountMap = new HashMap<>();//用户优惠券数量map
        if (!CollectionUtils.isEmpty(userCoupons)) {
            userCountMap = userCoupons.stream().collect(Collectors.groupingBy(UserCoupon::getCouponId, Collectors.counting()));
        }

        //优惠券已发总数
        Map<Long, Integer> statisticsMap = new HashMap<>();
        if (!isAdmin) {
            //不是运营后台需要查询发放总数
            List<UserCouponStatisticsDto> statisticsDtos = userCouponService.selectCouponStatisticsDto(couponIds);
            if (!CollectionUtils.isEmpty(statisticsDtos)) {
                statisticsDtos.forEach(item -> statisticsMap.put(item.getCouponId(), item.getCouponNum()));
            }
        }

        Map<Long, Integer> result = new HashMap<>();
        for (Coupon coupon : couponList) {
            Long couponId = coupon.getId();
            //领取状态，1：可领取，2：已领取，3：已发完
            Integer receiveStatus = UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_1.type;
            Integer limitCount = coupon.getLimitCount();//每个人限制领取张数，-1不限制
            Integer quota = coupon.getQuota(); //发放总数 -1:不限制
            Long userCount = userCountMap.getOrDefault(couponId, 0L); //用户已发数量
            Integer totalCount = statisticsMap.getOrDefault(couponId, 0); //已发总数量
            if (!Objects.equals(limitCount, -1) && userCount >= limitCount && !isRetainPop) {
                //已领取(用户达到单人领取上限)
                receiveStatus = UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_2.type;
            } else if (!Objects.equals(quota, -1) && totalCount >= quota) {
                //已发完(优惠券超过最大发放上限)
                receiveStatus = UserCouponConstant.ReceiveStatusEnum.RECEIVE_STATUS_3.type;
            }
            result.put(couponId, receiveStatus);
        }
        return result;
    }

}
