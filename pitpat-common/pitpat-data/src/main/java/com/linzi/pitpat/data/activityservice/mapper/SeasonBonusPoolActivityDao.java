package com.linzi.pitpat.data.activityservice.mapper;
/**
 * <p>
 * 活动权益配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.data.activityservice.model.entity.SeasonBonusPoolActivityDo;
import org.apache.ibatis.annotations.Mapper;

import java.time.ZonedDateTime;
import java.util.Set;

@Mapper
public interface SeasonBonusPoolActivityDao extends BaseMapper<SeasonBonusPoolActivityDo> {


    Set<Long> findSyncPoolByConfigId(Long competitiveScoreConfigId, ZonedDateTime date);

    Set<Long> findSyncPoolByActivityId(Long activityId);
}
