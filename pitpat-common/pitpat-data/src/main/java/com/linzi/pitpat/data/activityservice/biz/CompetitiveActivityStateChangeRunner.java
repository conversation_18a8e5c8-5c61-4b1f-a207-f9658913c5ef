package com.linzi.pitpat.data.activityservice.biz;

import com.google.common.collect.Lists;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.AwardSendStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.CompetitiveScoreConfigStatus;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.CompetitiveShortlistTypeEnum;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonActivityRangeDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonRankingThreshold;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonSettingDto;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveScoreConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonRankDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveShortlistDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.query.CompetitiveSeasonRankQuery;
import com.linzi.pitpat.data.activityservice.model.request.CompetitiveSeasonSystemDefaultConfig;
import com.linzi.pitpat.data.activityservice.service.CompetitiveScoreConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonActivityRangeService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonConfigService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonRankService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveShortlistService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 竞技活动上架检测
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CompetitiveActivityStateChangeRunner {

    private final CompetitiveSeasonConfigBizService competitiveSeasonConfigBizService;
    private final CompetitiveSeasonConfigService competitiveSeasonConfigService;
    private final CompetitiveSeasonBizService competitiveSeasonBizService;
    private final CompetitiveSeasonService competitiveSeasonService;
    private final CompetitiveSeasonRankService competitiveSeasonRankService;
    private final CompetitiveSeasonActivityRangeService competitiveSeasonActivityRangeService;
    private final ZnsUserService znsUserService;
    private final CompetitiveShortlistService competitiveShortlistService;
    private final MainActivityService mainActivityService;
    private final CompetitiveScoreConfigService competitiveScoreConfigService;

    /**
     * 活动上架
     *
     * @param mainActivity
     */
    public void enableActivity(MainActivity mainActivity) {
        //处理竞技赛状态
        checkRuleOfCompetitiveActivity(mainActivity);
        //保存竞技赛关联的活动列表
        checkAndSaveActivityRange(mainActivity);
        //处理入围用户名单
        checkAndAddShortList(mainActivity);
        //上架成功，修改seasonConfig活动上架状态
        CompetitiveSeasonDo competitiveSeasonDo = competitiveSeasonService.findByActivityId(mainActivity.getId()).orElseThrow(() -> new BaseException("竞技赛配置不存在"));
        //赛事等级根状态检查
        checkScoreConfigStatus(competitiveSeasonDo);

        competitiveSeasonConfigBizService.updateActivityCount(competitiveSeasonDo.getSeasonId(), mainActivity.activityStartDateZonedDateTime(), true);
    }

    /**
     * 检查赛事等级是否在线
     *
     * @param competitiveSeasonDo
     */
    private void checkScoreConfigStatus(CompetitiveSeasonDo competitiveSeasonDo) {
        Long scoreConfigId = competitiveSeasonDo.getScoreConfigId();
        CompetitiveScoreConfigDo competitiveScoreConfigDo = competitiveScoreConfigService.findById(scoreConfigId).orElseThrow(() -> new BaseException("赛事等级不存在"));
        CompetitiveScoreConfigDo competitiveScoreConfigRoot = competitiveScoreConfigService.findByConfigId(competitiveScoreConfigDo.getConfigId()).orElseThrow(() -> new BaseException("赛事等级根不存在"));
        if (!CompetitiveScoreConfigStatus.ENABLE.equals(competitiveScoreConfigRoot.getStatus())) {
            throw new BaseException("赛事等级已被下架请重新标记赛事竞技后再上架");
        }
    }

    public void checkAndAddShortList(MainActivity mainActivity) {

        Optional<CompetitiveSeasonDo> optional = competitiveSeasonService.findByActivityId(mainActivity.getId());
        optional.ifPresent(k -> {
            ActivityCompetitiveSeasonType competitiveSeasonType = k.getCompetitiveSeasonType();
            if (!ActivityCompetitiveSeasonType.ANNUAL.equals(competitiveSeasonType) && !ActivityCompetitiveSeasonType.SEASONAL.equals(competitiveSeasonType)) {
                return;
            }
        });

        //todo 查找排行榜
        ZonedDateTime now = ZonedDateTime.now();

        List<CompetitiveShortlistDo> competitiveShortlistList = new ArrayList<>();
        Optional<CompetitiveSeasonSettingDto> settingDtoOptional = competitiveSeasonBizService.getByActivityId(mainActivity.getId());
        settingDtoOptional.ifPresent(k -> {
            List<CompetitiveSeasonRankingThreshold> competitiveSeasonThresholds = k.getCompetitiveSeasonThresholds();
            if (!CollectionUtils.isEmpty(competitiveSeasonThresholds)) {
                for (CompetitiveSeasonRankingThreshold competitiveSeasonThreshold : competitiveSeasonThresholds) {
                    CompetitiveSeasonRankQuery query = new CompetitiveSeasonRankQuery();
                    query.setSeasonId(competitiveSeasonThreshold.getSeasonId());
                    query.setVersion(1);
                    query.setMaxRank(competitiveSeasonThreshold.getRank());
                    List<CompetitiveSeasonRankDo> list = competitiveSeasonRankService.findList(query);
                    List<CompetitiveShortlistDo> doList = list.stream().map(m -> {
                        CompetitiveShortlistDo shortlistDo = new CompetitiveShortlistDo();
                        ZnsUserEntity user = znsUserService.findById(m.getUserId());
                        shortlistDo.setActivityId(mainActivity.getId());
                        if (user != null) {
                            shortlistDo.setUserCode(user.getUserCode());
                            shortlistDo.setUserId(user.getId());
                            shortlistDo.setName(user.getFirstName());
                        }
                        shortlistDo.setIdentity(CompetitiveShortlistTypeEnum.COMPETITIVE_SCORE.getCode());
                        shortlistDo.setStatus(0);
                        shortlistDo.setJoinTime(now);
                        shortlistDo.setScore(m.getSeasonScore());
                        return shortlistDo;
                    }).collect(Collectors.toList());
                    competitiveShortlistList.addAll(doList);
                }
            }

        });
        competitiveShortlistService.competitiveScoreJoin(competitiveShortlistList);
    }


    /**
     * @param mainActivity
     */
    public void checkRuleOfCompetitiveActivity(MainActivity mainActivity) {
        if (Integer.valueOf(1).equals(mainActivity.getIsCompetitive())) {
            CompetitiveSeasonDo competitiveSeasonDo = competitiveSeasonService.findByActivityId(mainActivity.getId()).orElseThrow(() -> new BaseException("竞技赛配置错误"));
            if (ActivityCompetitiveSeasonType.MONTHLY.equals(competitiveSeasonDo.getCompetitiveSeasonType())) {

                //@since 4.0.0 竞技赛只有统一时间，不需要转化时区
                ZonedDateTime zonedDateTime = ZonedDateTimeUtil.convertFrom(DateTimeUtil.parse(mainActivity.getActivityStartTime()));
                if (zonedDateTime == null) {
                    throw new BaseException("时间解析失败");
                }
                //检查是否存在已经创建的季赛或年赛
                //获取活动归属的赛季
                List<CompetitiveSeasonConfigDo> seasons = competitiveSeasonConfigBizService.getSeasonIds(mainActivity.getId(), mainActivity.activityStartDateZonedDateTime());
//                if (CollectionUtils.isEmpty(seasons)) {
//                    throw new BaseException("未找到对应的季赛配置");
//                }
                //已经上架过对应的季赛，或者年赛
                seasons.stream().filter(CompetitiveSeasonConfigDo::haveSeasonalActivity).findFirst().ifPresent(season -> {
                    throw new BaseException("该赛事时间与已有季赛或年度赛有冲突");
                });
            } else if (ActivityCompetitiveSeasonType.SEASONAL.equals(competitiveSeasonDo.getCompetitiveSeasonType())) {
                List<CompetitiveSeasonActivityRangeDto> activityIds = competitiveSeasonBizService.findRelateCompetitiveSeasonActivity(mainActivity.getId());
                if (org.springframework.util.CollectionUtils.isEmpty(activityIds)) {
                    throw new BaseException("无月赛，不可上架该季赛");
                }
                //赛季最迟时间判断
                CompetitiveSeasonConfigDo bySeasonId = competitiveSeasonConfigService.findBySeasonId(competitiveSeasonDo.getSeasonId())
                        .orElseThrow(() -> new BaseException("对应的赛季不存在"));
                checkCompetitiveEndTime(mainActivity, bySeasonId);
                //结束时间判断
                if (competitiveSeasonService.checkExistAnnualActivity(competitiveSeasonDo.getCompetitiveSeasonYear(),
                        Lists.newArrayList(ActivityStateEnum.IN_PROGRESS, ActivityStateEnum.IN_PROGRESS, ActivityStateEnum.FINISHED))) {
                    throw new BaseException("该赛事时间与已有年度赛有冲突。");
                }

            } else if (ActivityCompetitiveSeasonType.ANNUAL.equals(competitiveSeasonDo.getCompetitiveSeasonType())) {
                List<CompetitiveSeasonActivityRangeDto> activityIds = competitiveSeasonBizService.findRelateCompetitiveSeasonActivity(mainActivity.getId());
                if (CollectionUtils.isEmpty(activityIds)) {
                    throw new BaseException("无月赛，不可上架该年度赛");
                }
                //赛季最迟时间判断
                CompetitiveSeasonConfigDo bySeasonId = competitiveSeasonConfigService.findBySeasonId(competitiveSeasonDo.getSeasonId())
                        .orElseThrow(() -> new BaseException("对应的赛季不存在"));
                checkCompetitiveEndTime(mainActivity, bySeasonId);

            } else {
                throw new BaseException("未知竞技赛类型");
            }
        }

    }


    private void checkCompetitiveEndTime(MainActivity mainActivity, CompetitiveSeasonConfigDo bySeasonId) {
        ZonedDateTime latestActivityAwardSendDate = getLatestActivityAwardSendDate(mainActivity);
        CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
        if (ActivityCompetitiveSeasonType.SEASONAL.equals(bySeasonId.getSeasonType())) {
            ZonedDateTime seasonLastedEndTime = bySeasonId.getEndTime().plusDays(competitiveSeasonSystemDefaultConfig.publishSeasonDayCount());
            if (latestActivityAwardSendDate.isAfter(seasonLastedEndTime)) {
                throw new BaseException("季赛比赛结束时间不可晚于本季赛的竞技分范围的截止时间+%d个月".formatted(competitiveSeasonSystemDefaultConfig.getPublishSeasonMonthCount()));
            }
        } else if (ActivityCompetitiveSeasonType.ANNUAL.equals(bySeasonId.getSeasonType())) {
            ZonedDateTime annualLastedEndTime = ZonedDateTime.now().withYear(bySeasonId.getYear()).with(TemporalAdjusters.firstDayOfNextYear())
                    .truncatedTo(ChronoUnit.DAYS).plusDays(competitiveSeasonSystemDefaultConfig.publishAnnualDayCount());
            if (!annualLastedEndTime.isAfter(latestActivityAwardSendDate)) {
                throw new BaseException("年赛比赛结束时间不可晚于年赛所属年份+%d个月".formatted(competitiveSeasonSystemDefaultConfig.getPublishAnnualMonthCount()));
            }
        }
    }

    public void checkAndSaveActivityRange(MainActivity mainActivity) {
        Optional<CompetitiveSeasonDo> byActivityId = competitiveSeasonService.findByActivityId(mainActivity.getId());
        if (byActivityId.isEmpty()) {
            throw new BaseException("竞技赛配置不存在");
        }
        CompetitiveSeasonDo competitiveSeason = byActivityId.get();
        if (ActivityCompetitiveSeasonType.MONTHLY.equals(competitiveSeason.getCompetitiveSeasonType())) {
            return;
        }
        List<CompetitiveSeasonActivityRangeDto> rangeActivity = competitiveSeasonBizService.findRelateCompetitiveSeasonActivity(mainActivity.getId());
        //排掉自己
        rangeActivity.removeIf(k -> mainActivity.getId().equals(k.getActivityId()));
        //检查所有的活动是否已经完成了奖励发放
        if (!CollectionUtils.isEmpty(rangeActivity)) {
            List<MainActivity> listByIds = mainActivityService.findListByIds(rangeActivity.stream().map(CompetitiveSeasonActivityRangeDto::getActivityId).toList());
            if (CollectionUtils.isEmpty(listByIds)) {
                throw new BaseException("该活动不存在");
            }
            ZonedDateTime mainActivityStartTime = mainActivity.applicationStartTimeZoneDateTime();
            for (MainActivity listById : listByIds) {
                Integer activityState = listById.getActivityState();
                ZonedDateTime currentActivityEndTime = getLatestActivityAwardSendDate(listById);
                if (!ActivityStateEnum.FINISHED.getState().equals(activityState)) {
                    if (currentActivityEndTime.isAfter(mainActivityStartTime)) {
                        if (competitiveSeason.getCompetitiveSeasonType().equals(ActivityCompetitiveSeasonType.ANNUAL)) {
                            throw new BaseException("季赛与月赛有冲突。");
                        }
                        throw new BaseException("月赛有冲突");
                    }
                }
                if (AwardSendStatusEnum.NO_VIEW.getCode().equals(listById.getAwardSendStatus())) {
                    //-1 无审核意义未开启审核  2 下发中 1 下发完成 所有只要等于0 ，就需要等待审核完成
                    if (currentActivityEndTime.isAfter(mainActivityStartTime)) {
                        if (competitiveSeason.getCompetitiveSeasonType().equals(ActivityCompetitiveSeasonType.ANNUAL)) {
                            throw new BaseException("年赛与月赛有冲突。");
                        }
                        throw new BaseException("月赛有冲突");
                    }
                }
            }
            //活动奖励条件检查完成。 进行数据保存
            competitiveSeasonActivityRangeService.saveActivityRange(mainActivity.getId(),
                    listByIds.stream().map(MainActivity::getId).toList());
        }

    }

    private ZonedDateTime getLatestActivityAwardSendDate(MainActivity activity) {
        ZonedDateTime zonedDateTime = activity.activityEndDateZonedDateTime();
        if (AwardSendStatusEnum.NO_VIEW.getCode().equals(activity.getAwardSendStatus())) {
            return zonedDateTime.plusDays(3);
        }
        return zonedDateTime;
    }


}
