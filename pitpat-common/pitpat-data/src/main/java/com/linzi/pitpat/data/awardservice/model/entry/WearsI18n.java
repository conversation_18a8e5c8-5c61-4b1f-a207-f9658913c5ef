package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @since 2023-12-19
 */

@Data
@NoArgsConstructor
@TableName("zns_wears_i18n")
public class WearsI18n implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //是否删除
    private Integer isDelete;
    //服装表主键id
    private Long clothId;
    //默认语言code
    private String langCode;
    //默认语言名称
    private String langName;
    //服装名称
    private String name;
}
