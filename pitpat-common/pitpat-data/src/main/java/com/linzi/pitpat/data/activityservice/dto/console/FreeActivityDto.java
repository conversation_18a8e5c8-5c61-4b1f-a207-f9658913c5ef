package com.linzi.pitpat.data.activityservice.dto.console;

import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.RunTemplateDo;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardDto;
import com.linzi.pitpat.data.vo.RunRouteVO;
import lombok.Data;

import java.util.List;

/**
 * 免费活动数据传输对象
 * 
 * @since 2025年1月
 */
@Data
public class FreeActivityDto {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动模式
     * 
     * @see FreeActivityModeEnum
     */
    private String mode;

    /**
     * 路线信息
     */
    private RunRouteVO route;

    /**
     * 目标里程
     */
    private Integer targetMileage;
    
    /**
     * 挑战模板
     */
    private RunTemplateDo runTemplateDo;

    /**
     * 上榜奖励
     */
    private ActivityAwardDto onRankAwardDto;

    /**
     * 线上奖励
     */
    private ActivityTargetAwardDto onlineAwardDto;

    /**
     * 线下奖励
     */
    private ActivityTargetAwardDto offlineAwardDto;
}
