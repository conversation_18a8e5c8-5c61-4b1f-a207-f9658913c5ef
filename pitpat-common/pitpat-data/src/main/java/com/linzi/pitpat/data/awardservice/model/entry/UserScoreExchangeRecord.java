package com.linzi.pitpat.data.awardservice.model.entry;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 用户积分表和兑换记录关联表
 *
 * <AUTHOR>
 * @since 2023-06-06
 */

@Data
@NoArgsConstructor
@TableName("zns_user_score_exchange_record")
public class UserScoreExchangeRecord implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.awardservice.model.entry.UserScoreExchangeRecord:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                                               // 主键，自增id
    public final static String is_delete = CLASS_NAME + "is_delete";                                  // 是否删除状态，1：删除，0：有效
    public final static String gmt_create = CLASS_NAME + "gmt_create";                                // 创建时间
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";                            // 最后修改时间
    public final static String user_score_id = CLASS_NAME + "user_score_id";                          // zns_activity_user_score表id
    public final static String exchange_score_record_id = CLASS_NAME + "exchange_score_record_id";    // zns_exchange_score_record 表id
    //主键，自增id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //zns_activity_user_score表id
    private Long userScoreId;
    //zns_exchange_score_record 表id
    private Long exchangeScoreRecordId;

    @Override
    public String toString() {
        return "UserScoreExchangeRecord{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",userScoreId=" + userScoreId +
                ",exchangeScoreRecordId=" + exchangeScoreRecordId +
                "}";
    }
}
