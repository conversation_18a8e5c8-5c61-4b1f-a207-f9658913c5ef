package com.linzi.pitpat.data.activityservice.strategy;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityDao;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityCouponDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeFailureCoupon;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.model.vo.HomePageOfficialRankingRun;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.vo.UserRunRecordDto;
import com.linzi.pitpat.data.vo.UserRunRecordDto1;
import com.linzi.pitpat.data.vo.report.RankingRunReportVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 官方排行赛
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class OfficialRankingActivityStrategy extends BaseOfficialActivityStrategy {

    private final ZnsRunActivityDao znsRunActivityDao;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserCouponService userCouponService;
    private final RedissonClient redissonClient;


    @Override
    public void wrapperRunActivityBasicData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity) {
        super.wrapperRunActivityBasicData(activityEntity, activityDetailVO, userEntity);
        ZonedDateTime lastUpdateTime = runActivityUserService.officalActivityLastUpdateTime(activityEntity.getId());
        activityDetailVO.setLastUpdateTime(lastUpdateTime);
    }


    @Override
    public void wrapperRunActivityDetailData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        super.wrapperRunActivityDetailData(activityEntity, activityDetailVO, userEntity, oneself);
        // 官方赛事活动完赛奖励
        String activityCompleteAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.ACTIVITY_COMPLETE_AWARD));
        activityDetailVO.setActivityCompleteAward(activityCompleteAward);
        // 官方赛事活动挑战榜中人奖励
        String activityRankAward = MapUtil.getString(jsonObjectConfig.get(ApiConstants.ACTIVITY_RANK_AWARD));
        activityDetailVO.setActivityRankAward(activityRankAward);
        // 活动要求
        String activityRequire = MapUtil.getString(jsonObjectConfig.get(ApiConstants.ACTIVITY_REQUIRE));
        activityDetailVO.setActivityRequire(activityRequire);
        //跑步目标奖励（m或s）
        activityDetailVO.setRunningGoalsAward(getRunningGoalsAward(jsonObjectConfig));
        BigDecimal maxReward = activityDetailVO.getMaxReward();
        if (userEntity != null) {
            String currencyCode = userAccountService.getUserCurrency(userEntity.getId()).getCurrencyCode();
            maxReward = I18nConstant.currencyFormat(currencyCode, maxReward);
        }
        activityDetailVO.setActivityTotalBonus(maxReward);

        activityDetailVO.setOfficialChallengeAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_CHALLENGE_AWARD)));
        activityDetailVO.setOfficialChallengeCouponAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_CHALLENGE_COUPON_AWARD)));
        // TODO i18n
        StringBuffer awardBuffer = new StringBuffer();
        awardBuffer.append("Completion Reward:\n");
        awardBuffer.append(activityCompleteAward);
        awardBuffer.append("\nRanking Challenge Rewards Rules:\n");
        awardBuffer.append(activityRankAward);
        activityDetailVO.setAwardRule(awardBuffer.toString());
        if (!activityDetailVO.getRunningGoals().isEmpty()) {
            if (activityDetailVO.getCompleteRuleType() == 1) {
                activityDetailVO.setRunMileage(new BigDecimal(activityDetailVO.getRunningGoals().get(0)));
            } else {
                activityDetailVO.setRunTime(activityDetailVO.getRunningGoals().get(0));
            }
        }
        List<Map<String, Object>> officialEventAward = getOfficialEventAward(jsonObjectConfig);
        activityDetailVO.setOfficialEventAward(officialEventAward);
    }

    private List<Map<String, Object>> getOfficialEventAward(Map<String, Object> jsonObjectConfig) {
        List<Map<String, Object>> list = new ArrayList<>();

        Map<String, Object> officialEventAward = JsonUtil.readValue(jsonObjectConfig.get("officialEventAward"));
        for (int i = 1; i <= 7; i++) {
            BigDecimal awardBigDecimal = MapUtil.getBigDecimal(officialEventAward.get(String.valueOf(i)));
            Map<String, Object> map = new HashMap<>();
            map.put("award", awardBigDecimal);
            if (i == 1) {
                map.put("startRank", 1);
                map.put("endRank", 1);
            } else if (i == 2) {
                map.put("startRank", 2);
                map.put("endRank", 2);
            } else if (i == 3) {
                map.put("startRank", 3);
                map.put("endRank", 3);
            } else if (i == 4) {
                map.put("startRank", 4);
                map.put("endRank", 6);
            } else if (i == 5) {
                map.put("startRank", 7);
                map.put("endRank", 10);
            } else if (i == 6) {
                map.put("startRank", 11);
                map.put("endRank", 20);
            } else if (i == 7) {
                map.put("startRank", 21);
                map.put("endRank", -1);
            }
            list.add(map);
        }
        return list;
    }

    @Override
    protected BigDecimal getPreMaxReward(Integer activityType, Integer userCount) {
        BigDecimal maxReward = super.getPreMaxReward(activityType, userCount);
        try {
            Map<String, Object> officialEventAward = JsonUtil.readValue(jsonObjectConfig.get("officialEventAward"));
            if (userCount < 20) {
                userCount = 20;
            }
            for (int i = 0; i < userCount; i++) {
                Integer rank = i + 1;
                BigDecimal award = runActivityService.officialEventRankAward(rank, officialEventAward);
                if (Objects.nonNull(award)) {
                    maxReward = maxReward.add(award);
                }
            }
        } catch (Exception e) {
            log.error("getPreMaxReward 失败，e:{}", e);
        }
        return maxReward;
    }


    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {
        Page page = new Page(1, 20);

        List<RunActivityUserVO> runActivityUsers = activityUserManager.findRunActivityUsers(activityEntity, null, userEntity.getId(), activityDetailVO, null, null, activityUserStatus);
        //个人数据处理
        // TODO: 2022/12/19 优化
        RunActivityUserVO oneself = runActivityUsers.stream().filter(u -> u.getUserId().equals(userEntity.getId())).findFirst().orElse(null);
        if (!CollectionUtils.isEmpty(runActivityUsers) && runActivityUsers.size() > 20) {
            runActivityUsers = runActivityUsers.subList(0, 20);
        }

        for (RunActivityUserVO runActivityUser : runActivityUsers) {
            //组装我的奖励
            Integer sumScore = activityUserScoreService.sumScore(activityEntity.getId(), runActivityUser.getUserId(),
                    null, 1);
            Map<String, Object> officialEventAward = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_AWARD);
            //int amount = runActivityService.officialEventRankScoreAward(runActivityUser.getRank(), officialEventScoreAward);
            BigDecimal amount = runActivityService.officialEventRankAward(runActivityUser.getRank(), officialEventAward);
            runActivityUser.setMyRewardScore(sumScore != null ? sumScore : 0);
            ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(runActivityUser.getUserId());
            if (accountEntity != null) {

            }
            runActivityUser.setMyRewardAmount(amount);
        }
        activityDetailVO.setActivityUsers(runActivityUsers);

        //判断是否是游客
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), userEntity.getId());
        if (Objects.isNull(activityUser)) {
            //游客返回游客房间号
            activityDetailVO.setIsVisitor(1);
            //房间号处理
            activityDetailVO.setWatchRoomId(activityEntity.getId().intValue());
            if (activityEntity.getCompleteRuleType() == 1) {
                activityDetailVO.setWatchRoomMileageGoal(activityEntity.getRunMileage().intValue());
            } else {
                activityDetailVO.setWatchRoomTimeGoal(activityEntity.getRunTime());
            }
        } else {
            if (Objects.nonNull(oneself)) {
                Map map = JsonUtil.readValue(oneself);
                map.put("name", userEntity.getFirstName());

                //组装我的奖励
                Integer sumScore = activityUserScoreService.sumScore(activityEntity.getId(), userEntity.getId(),
                        null, 1);
                Map<String, Object> officialEventAward = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_AWARD);
//                int amount = runActivityService.officialEventRankScoreAward(oneself.getRank(), officialEventScoreAward);
                BigDecimal amount = runActivityService.officialEventRankAward(oneself.getRank(), officialEventAward);
                if (Objects.isNull(amount)) {
                    amount = BigDecimal.ZERO;
                }
                map.put("myRewardScore", sumScore != null ? sumScore : 0);
                map.put("myRewardAmount", BigDecimalUtil.valueOf(String.valueOf(amount)));
                activityDetailVO.setMyGrades(map);
            }
        }

    }


    @Override
    @Transactional
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        if (activityEntity.getStatus() == 1) {
            // 2. 查找获得奖励的用户
            List<ZnsRunActivityUserEntity> activityUserEntities = runActivityUserService.awardUsersOfOfficialEvent(activityEntity);
            // 3. 开始发放活动奖励
            if (!CollectionUtils.isEmpty(activityUserEntities)) {
                for (ZnsRunActivityUserEntity activityUser : activityUserEntities) {
                    // 活动结束发放排行赛奖励
                    handleOfficialEventRankAward(activityEntity, activityUser);
                    // 活动结束被挑战者次数奖励发放
                    handleBeChallengedCountAward(activityEntity, activityUser);
                    // 保证金退回
                    handleOfficialEarnestMoney(activityEntity, activityUser);
                    log.info("before handlerUserCertificate user_id:{}", activityUser.getUserId());
                    // 证书
                    handlerUserCertificate(activityEntity, activityUser);
                }
            }
        } else {
            runActivityUserService.awardUsersOfOfficialEvent(activityEntity);
            List<ZnsRunActivityUserEntity> entityList = runActivityUserService.findAllActivityUser(activityEntity.getId());
            for (ZnsRunActivityUserEntity activityUser : entityList) {
                if (activityUser.getIsComplete() == 1) {
                    // 活动结束发放排行赛奖励
                    handleOfficialEventRankAward(activityEntity, activityUser);
                    // 活动结束被挑战者次数奖励发放
                    handleBeChallengedCountAward(activityEntity, activityUser);
                    log.info("before handlerUserCertificate user_id:{}", activityUser.getUserId());
                    // 证书
                    handlerUserCertificate(activityEntity, activityUser);
                }

                if (activityEntity.getBonusRuleType() != 1) {
                    //退回积分
                    useUserScore(activityEntity, activityUser.getUserId(), true);
                    if (activityEntity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                        // 保证金、费用退回
                        runActivityProcessManager.cancelActivityRefund(activityEntity, Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? AccountDetailTypeEnum.FEE : AccountDetailTypeEnum.SECURITY_FUND, activityUser.getUserId(), "Deposit return");
                    }
                }
            }
        }

        super.handleRunActivityEnd(activityEntity);
    }


    @Override
    protected void handleOfficialEarnestMoney(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser) {
        if (activityUser.getIsComplete() != 1) {
            return;
        }
        super.handleOfficialEarnestMoney(activityEntity, activityUser);
    }

    @Override
    public void wrapperRunActivityDataDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
        Map<String, Object> personalActivityData = new HashMap<>();
        List<UserRunRecordDto> list = userRunRecordService.getRunRecords(activityEntity.getId(), userId);

        if (list.isEmpty()) {
            activityDetailVO.setPersonalActivityData(personalActivityData);
            return;
        }

        personalActivityData.put("rank", runActivityUserService.getCurrentRank(activityEntity.getId(), activityEntity.getCompleteRuleType(), userId));

        long completeCount = list.stream().filter(r -> r.getIsComplete() == 1).count();
        personalActivityData.put("completeCount", completeCount);

        Map<String, Object> officialAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_AWARD);
        if (Objects.nonNull(officialAwardJson)) {
            BigDecimal award = runActivityService.officialEventRankAward((Integer) personalActivityData.get("rank"), officialAwardJson);
            personalActivityData.put("completeAward", award);
        } else {
            log.error("官方赛事活动排名发送奖励:奖励配置不存在");
            activityDetailVO.setPersonalActivityData(personalActivityData);
        }

        long challengeSuccessCount = list.stream().filter(r -> r.getIsChallengeSuccess() == 1).count();
        long challengeFailCount = list.stream().filter(r -> r.getIsChallengeSuccess() == 0 && r.getChallengedUserId() > 0).count();
        personalActivityData.put("challengeSuccessCount", challengeSuccessCount);
        personalActivityData.put("challengeFailCount", challengeFailCount);

        int challengedCount = userRunRecordService.officialEventChallengedCount(userId, activityEntity.getId());
        personalActivityData.put("challengedCount", challengedCount);
        if (challengedCount > 0) {
            Map<String, Object> beChallengedAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.BE_CHALLENGED_AWARD);
            if (Objects.nonNull(beChallengedAwardJson)) {
                BigDecimal beChallengedAward = officialBeChallengedCountAward(challengedCount, beChallengedAwardJson);
                personalActivityData.put("beChallengedAward", Objects.isNull(beChallengedAward) ? BigDecimal.ZERO : beChallengedAward);
            } else {
                log.error("官方赛事活动被挑战次数发送奖励:奖励配置不存在");
            }

        }
        //挑战获得奖励
        BigDecimal sumAward = userRunRecordService.sumAward(userId, activityEntity.getId());
        personalActivityData.put("challengedAward", sumAward);

        List<UserRunRecordDto> collect = list.stream().filter(r -> Objects.nonNull(r.getRunTime()) && r.getRunTime() >= 60).sorted(Comparator.comparing(UserRunRecordDto::getCreateTime).reversed()).collect(Collectors.toList());
        // TODO: 2023/1/17 临时
        List<UserRunRecordDto1> collect1 = collect.stream().map(m -> {
            UserRunRecordDto1 dto1 = new UserRunRecordDto1();
            dto1.setDtoData(m);
            if (m.getLastTime().compareTo(activityEntity.getActivityEndTime()) > 0) {
                dto1.setRank("-");
            } else {
                dto1.setRank(m.getRank().toString());
            }
            return dto1;
        }).collect(Collectors.toList());
        personalActivityData.put("list", collect1);

        activityDetailVO.setPersonalActivityData(personalActivityData);
    }

    @Override
    public List<? extends SimpleRunActivityVO> homePageActivityList(List<ZnsRunActivityEntity> activityEntityList, Long userId) {
        if (CollectionUtils.isEmpty(activityEntityList)) {
            return new ArrayList<>();
        }
        List<Long> routeIds = activityEntityList.stream().map(ZnsRunActivityEntity::getActivityRouteId).collect(Collectors.toList());
        List<Long> activityIds = activityEntityList.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());

        List<ZnsRunRouteEntity> znsRunRouteEntities = runRouteService.findListByIds(routeIds);
        Map<Long, ZnsRunRouteEntity> routeEntityMap = znsRunRouteEntities.stream().collect(Collectors.toMap(ZnsRunRouteEntity::getId, Function.identity(), (x, y) -> x));

        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, userId);
        Map<Long, ZnsRunActivityUserEntity> activityUserMap = activityUsers.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (x, y) -> x));

        List<HomePageOfficialRankingRun> list = new ArrayList<>();
        for (ZnsRunActivityEntity activity : activityEntityList) {
            HomePageOfficialRankingRun rankingRun = new HomePageOfficialRankingRun(activity);
            Integer currentRank = runActivityUserService.getCurrentRank(activity.getId(), activity.getCompleteRuleType(), userId);
            rankingRun.setRank(currentRank);
            ZnsRunRouteEntity znsRunRouteEntity = routeEntityMap.get(activity.getActivityRouteId());
            if (Objects.nonNull(znsRunRouteEntity)) {
                rankingRun.setRouteType(znsRunRouteEntity.getRouteType());
            } else {
                rankingRun.setRouteType(0);
            }
            ZnsRunActivityUserEntity znsRunActivityUserEntity = activityUserMap.get(activity.getId());
            if (Objects.nonNull(znsRunActivityUserEntity)) {
                rankingRun.setIsEnroll(1);
            }
            Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
            rankingRun.setCoverImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
            Integer integer = userCouponService.countUserCouponByCondition(userId, activity.getId(), activity.getActivityType(), activity.getTaskConfigId(), activity.getBatchNo());
            rankingRun.setCanUserCoupon(integer > 0 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());

            //填充活动开始结束时间 from 2.8.0
            rankingRun.setActivityStartTime(activity.getActivityStartTime());
            rankingRun.setActivityEndTime(activity.getActivityEndTime());

            // 限速
            rankingRun.setRateLimitType(activity.getRateLimitType());
            if (activity.getRateLimitType() > 0) {
                Integer rateLimiting = activity.getRateLimiting();
                rankingRun.setRateLimitMile(SportsDataUnit.conversionVelocity(new BigDecimal(rateLimiting), 1)); // 米->英里
            } else {
                rankingRun.setRateLimitMile(new BigDecimal(-1));
            }

            list.add(rankingRun);
        }
        return list;
    }

    /**
     * 官方赛事排行奖励发送
     */
    void handleOfficialEventRankAward(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser) {
        Integer rank = activityUser.getRank();
        if (null == rank) {
            rank = -1;
        }
        log.info("activityId = " + activityEntity.getId() + "，userId=" + activityUser.getUserId() + " 开始发送handleOfficialEventRankAward奖励 ");

        Map<String, Object> officialEventScoreAward = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_SCORE_AWARD);
        if (officialEventScoreAward != null) {
            int socre = runActivityService.officialEventRankScoreAward(rank, officialEventScoreAward);
            if (socre > 0) {
                //积分权益
                ActivityBrandRightsInterests scoreRightsInterest = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.INTEGRAL.getStatusCode(), activityEntity, activityUser.getUserId());
                Integer extraScore = 0;
                if (Objects.nonNull(scoreRightsInterest)) {
                    BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(new BigDecimal(socre), scoreRightsInterest.getMultiple());
                    extraScore = brandAward.intValue();
                    socre = socre + brandAward.intValue();
                }

                activityUserScoreService.increaseAmount(socre, activityUser.getActivityId(), activityUser.getUserId(), rank, extraScore, null);
            } else {
                log.info("官方赛事不给用户奖励分数 ，用户id:" + activityUser.getUserId() + "分数为 " + socre);
            }
        } else {
            log.info("官方赛事活动排行赛事不发奖励:没有配置");
        }
        log.info("优惠券发送 activityId = " + activityEntity.getId() + "，userId=" + activityUser.getUserId() + " 开始发送handleCouponRankAward奖励 ");
        Map<String, Object> officialEventCouponAward = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_COUPON_AWARD);
        if (officialEventCouponAward != null) {
            ActivityCouponDto activityCouponDto = runActivityService.officialEventRankCouponAward(rank, officialEventCouponAward);
            if (activityCouponDto != null) {
                for (int num = 0; num < activityCouponDto.getNum(); num++) {
                    userCouponManager.sendUserCoupon(activityCouponDto.getCouponId(), activityUser.getUserId(), activityEntity.getId());
                }
            } else {
                log.info("官方赛事不给用户奖励优惠券 ，用户id:" + activityUser.getUserId() + "优惠券配置为 " + JsonUtil.writeString(activityCouponDto));
            }
        } else {
            log.info("官方赛事活动排行赛事不发优惠券:没有配置");
        }

        Map<String, Object> officialAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_EVENT_AWARD);
        if (null == officialAwardJson) {
            log.info("官方赛事活动排名发送奖励:奖励配置不存在");
            return;
        }
        BigDecimal award = runActivityService.officialEventRankAward(rank, officialAwardJson);
        if (null == award) {
            log.info("官方赛事给用户id:" + activityUser.getUserId() + "发送奖励失败,奖励配置找不到");
            return;
        }
        //修改币种切换处理
        List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
        if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
            log.info("币种切换不发放");
            award = BigDecimal.ZERO;
        }
        if (award.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("官方赛事活动排名发送奖励:奖励金额为0");
            return;
        }
        //权益处理
        ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.RANK_REWARD.getStatusCode(), activityEntity, activityUser.getUserId());
        Integer rightsInterestsType = null;
        Integer privilegeBrand = null;
        BigDecimal rightsInterestsMultiple = null;

        if (Objects.nonNull(brandRightsInterests)) {
            rightsInterestsMultiple = brandRightsInterests.getMultiple();
            BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(award, rightsInterestsMultiple);
            award = award.add(brandAward);
            rightsInterestsType = brandRightsInterests.getRightsInterestsType();
            privilegeBrand = brandRightsInterests.getBrand();
        }
        award = getUserCurrencyAmount(activityUser.getUserId(), award);
        // 给用户余额发送奖励
        userAccountService.increaseAmount(award, activityUser.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD,
                AccountDetailSubtypeEnum.OFFICIAL_RANK.getType(), 1, award, billNo, tradeTime, activityUser.getActivityId(),
                activityUser.getActivityId(), null, activityUser.getActivityType(), 0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, BigDecimal.ZERO);
        if (Integer.valueOf(0).equals(activityUser.getIsCheat())) {
            // 更新活动用户奖励金额
            activityUser.setRunAward(award);
            activityUser.setRewardTime(ZonedDateTime.now());
            runActivityUserService.updateById(activityUser);

            //通知
            ActivityNotificationEnum activityNotification = ActivityNotificationEnum.AWARD_OFFICIAL_RANKING_RUN;
            String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.AWARD_OFFICIAL_RANKING_RUN"), activityEntity.getActivityTitle());
            MessageBo message = appMessageService.assembleMessage(activityEntity.getId(), content, "4", NoticeTypeEnum.REWARD_NOTICE.getType());
            message.setActivityId(activityEntity.getId());
            ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
            appMessageService.sendImAndPushUserIds(Arrays.asList(activityUser.getUserId()), imMessageBo, message);
        }
    }

    /**
     * 官方赛事被挑战次数发放奖励
     */
    void handleBeChallengedCountAward(ZnsRunActivityEntity activityEntity, ZnsRunActivityUserEntity activityUser) {
        long challengedCount = userRunRecordService.officialEventChallengedCount(activityUser.getUserId(), activityUser.getActivityId());
        if (challengedCount <= 0) {
            log.info("被挑战次数小于等于0");
            return;
        }
        log.info("官方赛事被挑战积分奖励发送" + "activityId = " + activityEntity.getId() + "，userId=" + activityUser.getUserId() + " 开始发送beChallengedScoreAward奖励 ");
        Map<String, Object> beChallengedScoreAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.BE_CHALLENGED_SCORE_AWARD);
        if (beChallengedScoreAwardJson != null) {
            String scoreLevel = officialBeChallengedLevel(challengedCount, beChallengedScoreAwardJson);
            Integer beChallengedScore = MapUtil.getInteger(beChallengedScoreAwardJson.get(scoreLevel));
            if (beChallengedScore != null && beChallengedScore > 0) {
                activityUserScoreService.beChallengeIncreaseAmount(beChallengedScore, activityUser.getActivityId(), activityUser.getUserId());
            } else {
                log.info("官方赛事活动被挑战不给用户奖励分数 ，用户id:" + activityUser.getUserId() + "分数为 " + beChallengedScore);
            }
        } else {
            log.info("官方赛事活动被挑战次数发送奖励:奖励配置不存在");
        }
        log.info("官方赛事被挑战优惠券奖励发送 activityId = " + activityEntity.getId() + "，userId=" + activityUser.getUserId() + " 开始发送beChallengedCouponAward奖励 ");
        Map<String, Object> beChallengedCouponAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.BE_CHALLENGED_COUPON_AWARD);
        if (beChallengedCouponAwardJson != null) {
            String couponLevel = officialBeChallengedLevel(challengedCount, beChallengedCouponAwardJson);
            String award = String.valueOf(beChallengedCouponAwardJson.get(couponLevel));
            if (StringUtils.hasText(award)) {
                ActivityCouponDto activityCouponDto = JsonUtil.readValue(award, ActivityCouponDto.class);
                if (Objects.nonNull(activityCouponDto)) {
                    for (int num = 0; num < activityCouponDto.getNum(); num++) {
                        userCouponManager.sendUserCoupon(activityCouponDto.getCouponId(), activityUser.getUserId(), activityEntity.getId());
                    }
                } else {
                    log.info("官方排行赛事被挑战不给用户奖励优惠券 ，用户id:" + activityUser.getUserId() + "优惠券配置为 " + JsonUtil.writeString(activityCouponDto));
                }
            }
        } else {
            log.info("官方排行赛事被挑战不发优惠券:没有配置");
        }
        Map<String, Object> beChallengedAwardJson = runActivityService.officialAwardConfig(activityEntity, ApiConstants.BE_CHALLENGED_AWARD);
        if (null == beChallengedAwardJson) {
            log.info("官方赛事活动被挑战次数发送现金奖励:奖励配置不存在");
            return;
        }
        BigDecimal beChallengedAward = officialBeChallengedCountAward((int) challengedCount, beChallengedAwardJson);
        if (null == beChallengedAward) {
            log.info("官方赛事活动被挑战次数发送现金奖励:奖励金额未查到");
            return;
        }
        //修改货架币种切换处理
        List<String> list = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUser.getUserId(), 0, -1);
        if (!CollectionUtils.isEmpty(list) && list.contains(activityUser.getActivityId().toString())) {
            log.info("币种切换不发放");
            beChallengedAward = BigDecimal.ZERO;
        }

        if (beChallengedAward.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("官方赛事活动被挑战次数发送现金奖励:奖励金额为0");
            return;
        }

        //权益处理
        ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.BE_CHALLENGED_REWARD.getStatusCode(), activityEntity, activityUser.getUserId());
        Integer rightsInterestsType = null;
        Integer privilegeBrand = null;
        BigDecimal rightsInterestsMultiple = null;

        if (Objects.nonNull(brandRightsInterests)) {
            rightsInterestsMultiple = brandRightsInterests.getMultiple();
            BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(beChallengedAward, rightsInterestsMultiple);
            beChallengedAward = beChallengedAward.add(brandAward);
            rightsInterestsType = brandRightsInterests.getRightsInterestsType();
            privilegeBrand = brandRightsInterests.getBrand();
        }
        beChallengedAward = getUserCurrencyAmount(activityUser.getUserId(), beChallengedAward);
        // 给用户余额发送奖励
        userAccountService.increaseAmount(beChallengedAward, activityUser.getUserId(), true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(activityUser.getUserId(), AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD,
                AccountDetailSubtypeEnum.OFFICIAL_BECHALLENGED.getType(), 1, beChallengedAward, billNo, tradeTime,
                activityUser.getActivityId(), activityUser.getActivityId(), null, activityUser.getActivityType(), 0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, BigDecimal.ZERO);
        if (Integer.valueOf(0).equals(activityUser.getIsCheat())) {
            //通知
            ActivityNotificationEnum activityNotification = ActivityNotificationEnum.OFFICIAL_RANKING_RUN;
            String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.OFFICIAL_RANKING_RUN"), challengedCount, activityEntity.getActivityTitle());
            MessageBo message = appMessageService.assembleMessage(activityEntity.getId(), content, "4", NoticeTypeEnum.REWARD_NOTICE.getType());
            message.setActivityId(activityEntity.getId());
            ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
            appMessageService.sendImAndPushUserIds(Arrays.asList(activityUser.getUserId()), imMessageBo, message);
        }
    }

    /**
     * 返回官方赛事活动奖励档位
     */
    static BigDecimal officialBeChallengedCountAward(int challengedCount, Map<String, Object> beChallengedAwardJson) {
        if (challengedCount < 0 || null == beChallengedAwardJson) {
            return null;
        }
        String levelKey = "";
        if (challengedCount >= 1 && challengedCount <= 5) {
            levelKey = "1";
        } else if (challengedCount >= 6 && challengedCount <= 10) {
            levelKey = "2";
        } else if (challengedCount >= 11) {
            levelKey = "3";
        }
        if (StringUtil.isEmpty(levelKey)) {
            return null;
        }
        BigDecimal award = MapUtil.getBigDecimal(beChallengedAwardJson.get(levelKey));
        if (null == award) {
            return null;
        }
        return award;
    }

    /**
     * 返回官方赛事活动奖励档位
     */
    static String officialBeChallengedLevel(long challengedCount, Map<String, Object> beChallengedAwardJson) {
        if (challengedCount < 0 || null == beChallengedAwardJson) {
            return null;
        }
        String levelKey = "";
        if (challengedCount >= 1 && challengedCount <= 5) {
            levelKey = "1";
        } else if (challengedCount >= 6 && challengedCount <= 10) {
            levelKey = "2";
        } else if (challengedCount >= 11) {
            levelKey = "3";
        }
        if (StringUtil.isEmpty(levelKey)) {
            return null;
        }
        return levelKey;
    }

    @Override
    public RankingRunReportVo getActivityRunningReport(ZnsUserRunDataDetailsEntity detail, ZnsUserEntity loginUser, ZnsRunActivityEntity activityEntity, String zoneId) {
        RankingRunReportVo rankingRunReportVo = new RankingRunReportVo();
        ZnsRunActivityEntity activity = runActivityService.getActivityByDetailId2(detail.getId());
        if (Objects.nonNull(activity)) {
            rankingRunReportVo.setCompleteRuleType(activity.getCompleteRuleType());
            rankingRunReportVo.setRunMileage(activity.getRunMileage());
            rankingRunReportVo.setRunTime(activity.getRunTime());
            rankingRunReportVo.setActivityStartTime(detail.getCreateTime());
            rankingRunReportVo.setActivityEndTime(DateUtil.convertZonedDateTime2Date(detail.getLastTime()));
        }
        List<ChallengeRunRunningReportListVO> list = new ArrayList<>();
        ChallengeRunRunningReportListVO myRaceRunRunningReport = userRunDataDetailsDao.getMyRaceRunRunningReport(detail.getId());
        ChallengeRunRunningReportListVO otherRaceRunRunningReport = userRunDataDetailsDao.getOtherRaceRunRunningReport(detail.getId());
        if (Objects.nonNull(myRaceRunRunningReport) && myRaceRunRunningReport.getIsChallengeSuccess() == 1) {
            myRaceRunRunningReport.setRank(1);
            otherRaceRunRunningReport.setRank(2);
        } else {
            myRaceRunRunningReport.setRank(2);
            otherRaceRunRunningReport.setRank(1);
        }
        //设置真实时间
        otherRaceRunRunningReport.setRealRunTimeMillisecond(myRaceRunRunningReport.getRunTimeMillisecond());
        myRaceRunRunningReport.setRealRunTimeMillisecond(otherRaceRunRunningReport.getRunTimeMillisecond());

        list.add(myRaceRunRunningReport);
        list.add(otherRaceRunRunningReport);

        //处理挑战跑 数据明细
        Integer finishStatus = dealChallengeDetail(list, activityEntity.getCompleteRuleType(), activityEntity.getActivityType());
        rankingRunReportVo.setFinishStatus(finishStatus);

        rankingRunReportVo.setList(list);
        return rankingRunReportVo;
    }


    public void updateOfficialEventUserRunData(ZnsRunActivityEntity activityEntity, Long userId, ZnsUserRunDataDetailsEntity runDataDetailsEntity) {
        if (null == activityEntity || null == userId) {
            log.info("活动或者用户不存在");
            return;
        }
        if (null == runDataDetailsEntity) {
            log.info("未查到对应的跑步报告记录");
            return;
        }
        // 查找用户官方赛事最新跑步记录
        //这里的锁，是否有必要，因为上层已经加锁过了
        ZnsUserRunRecordEntity runRecordEntity = userRunRecordService.findLastRecord(activityEntity.getId(), userId, runDataDetailsEntity.getId());

        if (null == runRecordEntity) {
            log.info("未查到用户官方赛事最新跑步记录");
            return;
        }

        // 完成比赛并比较成绩之后, 更新用户跑步成功。
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityEntity.getId(), userId);
        if (null == activityUser) {
            log.info("未查到参与的官方赛事用户");
            return;
        }
        if (activityUser.getUserState() == 1 && runDataDetailsEntity.getRunTime() >= 60) {
            activityUser.setUserState(3);                       // 用户状态：0表示未答复，1表示已接受，2表示已拒绝，3表示跑步中，4表示已结束
        }
        // 完成比赛并比较成绩之后, 更新用户跑步成功。
        ZnsRunActivityUserEntity challengedUser = runRecordEntity.getChallengedUserId() > 0 ? runActivityUserService.findActivityUser(activityEntity.getId(), runRecordEntity.getChallengedUserId()) : null;

        // 完成规则类型：1表示完成跑步里程，2表示完成跑步时长
        boolean isComplete = false;
        boolean needUpdate = false;
        if (1 == activityEntity.getCompleteRuleType()) {
            // 先判断用户是否完成比赛
            if (runDataDetailsEntity.getRunMileage().compareTo(activityEntity.getRunMileage()) >= 0) {
                // 完成比赛
                isComplete = true;
                // activity_user 中只保留最好的成绩
                if (activityUser.getRunTimeMillisecond() == 0 || activityUser.getRunTimeMillisecond().compareTo(runDataDetailsEntity.getRunTimeMillisecond()) >= 0) {
                    needUpdate = true;
                }
            }
        } else if (2 == activityEntity.getCompleteRuleType()) {
            // 先判断用户是否完成比赛
            if (runDataDetailsEntity.getRunTime().compareTo(activityEntity.getRunTime()) >= 0) {
                // 完成比赛
                isComplete = true;
                if (activityUser.getRunMileage().compareTo(BigDecimal.ZERO) == 0 || activityUser.getRunMileage().compareTo(runDataDetailsEntity.getRunMileage()) <= 0) {
                    needUpdate = true;
                }
            }
        }
        if (needUpdate && Integer.valueOf(0).equals(runDataDetailsEntity.getIsCheat())
                && ZonedDateTime.now().compareTo(activityEntity.getActivityEndTime()) <= 0) {
            // 需要更新用户官方赛事排行信息
            activityUser.setRunTime(runDataDetailsEntity.getRunTime());
            activityUser.setRunTimeMillisecond(runDataDetailsEntity.getRunTimeMillisecond());
            activityUser.setRunMileage(runDataDetailsEntity.getRunMileage());
            activityUser.setModifieTime(ZonedDateTime.now());
            activityUser.setRunDataDetailsId(runDataDetailsEntity.getId());
            if (isComplete) {
                activityUser.setIsComplete(1);
                if (Objects.isNull(activityUser.getCompleteTime())) {
                    activityUser.setCompleteTime(ZonedDateTime.now());
                }
            }
        }
        runActivityUserService.updateById(activityUser);
        if (isComplete) {
            //查询当前排名
            Integer currentRank = runActivityUserService.getCurrentRank(activityUser.getActivityId(), activityEntity.getCompleteRuleType(), userId, runDataDetailsEntity.getRunTimeMillisecond(), runDataDetailsEntity.getRunMileage());
            runRecordEntity.setRank(currentRank);
            // 更新用户跑步记录的完成状态
            runRecordEntity.setIsComplete(1);
            runRecordEntity.setModifieTime(ZonedDateTime.now());
            runRecordEntity.setCompleteTime(ZonedDateTime.now());
            userRunRecordService.update(runRecordEntity);
            log.info("排行赛事已经跑完 runRecordEntityId= " + runRecordEntity.getId());
            if (Objects.nonNull(runRecordEntity.getTaskId()) && runRecordEntity.getTaskId() != 0) {
                RunActivityUserTask userTask = runActivityUserTaskService.findById(runRecordEntity.getTaskId());
                ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityDao.selectActivityById(userTask.getActivityId());
                ZonedDateTime runTime = runDataDetailsEntity.getCreateTime() == null ? ZonedDateTime.now() : runDataDetailsEntity.getCreateTime();
                if (znsRunActivityEntity != null && runTime.toInstant().toEpochMilli() < znsRunActivityEntity.getActivityEndTime().toInstant().toEpochMilli()) {
                    activityUser.setIsComplete(1);
                    activityUser.setCompleteTime(ZonedDateTime.now());
                    runActivityUserTaskService.dealUserTask(userTask, activityUser);
                } else {
                    log.info("排行赛事已经跑完 activityId = " + userTask.getActivityId() + "，活动已经结束，不需要发奖励 ,userId = " + userTask.getUserId());
                }
            }
        }
        // 查询是否有被挑战者
        boolean challengeSuccess = false;
        if (runRecordEntity.getChallengedUserId() > 0) {
            //查询奖励是否已发送
            ZnsUserAccountDetailEntity accountDetail = userAccountDetailService.getAccountDetail(userId, runDataDetailsEntity.getId(), AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType(), null);
            if (Objects.nonNull(accountDetail)) {
                log.info("奖励已发送");
                return;
            }
            if (null == challengedUser) {
                log.info("未查到官方赛事的被挑战用户");
                return;
            }
            if (1 == activityEntity.getCompleteRuleType()) {
                if (challengedUser.getRunTimeMillisecond() == 0) {
                    challengedUser.setRunTimeMillisecond(challengedUser.getRunTime() * 1000 + 999);
                }
                // 先判断用户是否挑战成功
                if (challengedUser.getRunTimeMillisecond().compareTo(runDataDetailsEntity.getRunTimeMillisecond()) >= 0 && isComplete) {
                    challengeSuccess = true;
                }
            } else if (2 == activityEntity.getCompleteRuleType()) {
                // 先判断用户是否挑战成功
                if (challengedUser.getRunMileage().compareTo(runDataDetailsEntity.getRunMileage()) <= 0 && isComplete) {
                    challengeSuccess = true;
                }
            }
            if (isComplete && challengeSuccess) {
                // 处理挑战成功奖励逻辑
                BigDecimal award = handleOfficialEventChallengeSuccess(activityEntity, userId, runRecordEntity.getChallengeRank(), runDataDetailsEntity.getId(), runRecordEntity.getChallengedUserId(), runDataDetailsEntity.getIsCheat(), runDataDetailsEntity);
                // 更新用户跑步记录的完成状态
                runRecordEntity.setIsChallengeSuccess(1);
                runRecordEntity.setModifieTime(ZonedDateTime.now());
                runRecordEntity.setChallengeAward(award);
                userRunRecordService.update(runRecordEntity);
            }
            if (false == challengeSuccess && runRecordEntity.getChallengedUserId() > 0 && runDataDetailsEntity.getRunTime() >= 60) {
                // 挑战失败奖励发放
                BigDecimal award = handleOfficialEventChallengeFailure(activityEntity, userId, runDataDetailsEntity.getId(), runDataDetailsEntity.getIsCheat(), runDataDetailsEntity);
                runRecordEntity.setModifieTime(ZonedDateTime.now());
                runRecordEntity.setChallengeAward(award);
                userRunRecordService.update(runRecordEntity);
            }
        }
    }

    /**
     * 官方赛事挑战成功奖励
     *
     * @param activityEntity       活动
     * @param userId               用户id
     * @param challengeRank        被挑战者用户排名
     * @param rid
     * @param challengedUserId
     * @param isCheat
     * @param runDataDetailsEntity
     */
    BigDecimal handleOfficialEventChallengeSuccess(ZnsRunActivityEntity activityEntity, Long userId, Integer challengeRank, Long rid, Long challengedUserId, Integer isCheat, ZnsUserRunDataDetailsEntity runDataDetailsEntity) {
        if (Integer.valueOf(1).equals(isCheat)) {
            log.info("handleOfficialEventChallengeSuccess 失败，用户作弊结束处理");
            return BigDecimal.ZERO;
        }
        if (null == challengeRank) {
            challengeRank = 0;
        }
        Map<String, Object> challengeAwardCouponObject = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_COUPON_AWARD);
        if (challengeAwardCouponObject != null) {
            ActivityCouponDto activityCouponDto = officialChallengeSuccessAwardCouponAmount(challengeRank, challengeAwardCouponObject, userId.equals(challengedUserId));
            if (activityCouponDto != null) {
                for (int num = 0; num < activityCouponDto.getNum(); num++) {
                    userCouponManager.sendUserCoupon(activityCouponDto.getCouponId(), userId, activityEntity.getId());
                }
            }
        } else {
            log.info("没有配置challengeAwardCouponObject");
        }
        Map<String, Object> challengeAwardScoreObject = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_SCORE_AWARD);
        if (challengeAwardScoreObject != null) {
            String scoreNum = officialChallengeSuccessAwardScoreAmount(challengeRank, challengeAwardScoreObject, userId.equals(challengedUserId));
            if (StringUtils.hasText(scoreNum)) {
                int ChallengedScore = Integer.parseInt(scoreNum);
                if (ChallengedScore > 0) {
                    activityUserScoreService.beChallengeIncreaseAmount(ChallengedScore, activityEntity.getId(), userId);
                } else {
                    log.info("官方赛事活动挑战成功不给用户奖励分数 ，用户id:" + userId + "分数为 " + ChallengedScore);
                }
            }
        } else {
            log.info("没有配置challengeAwardScoreObject");
        }
        Map<String, Object> challengeAwardObject = runActivityService.officialAwardConfig(activityEntity, ApiConstants.OFFICIAL_CHALLENGE_AWARD);
        if (null == challengeAwardObject) {
            log.error("官方赛事挑战成功奖励配置不存在");
            return BigDecimal.ZERO;
        }
        BigDecimal awardAmount = officialChallengeSuccessAwardAmount(challengeRank, challengeAwardObject, userId.equals(challengedUserId));
        if (null == awardAmount) {
            log.info("挑战用户不存在或者不在挑战排名奖励中");
            return BigDecimal.ZERO;
        }
        //修改币种切换处理
        List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + userId, 0, -1);
        if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityEntity.getId().toString())) {
            log.info("币种切换不发放");
            awardAmount = BigDecimal.ZERO;
        }
        if (awardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("挑战排名成功奖励为0");
            return BigDecimal.ZERO;
        }
        //权益处理
        ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(runDataDetailsEntity.getTreadmillId(), BrandRightsInterestEnum.CHALLENGE_SUCCESS_REWARD.getStatusCode(), activityEntity);
        Integer rightsInterestsType = null;
        Integer privilegeBrand = null;
        BigDecimal rightsInterestsMultiple = null;

        if (Objects.nonNull(brandRightsInterests)) {
            rightsInterestsMultiple = brandRightsInterests.getMultiple();
            BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(awardAmount, rightsInterestsMultiple);
            awardAmount = awardAmount.add(brandAward);
            rightsInterestsType = brandRightsInterests.getRightsInterestsType();
            privilegeBrand = brandRightsInterests.getBrand();
        }

        awardAmount = getUserCurrencyAmount(userId, awardAmount);
        // 给用户余额发送奖励
        userAccountService.increaseAmount(awardAmount, userId, true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(userId, AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD,
                AccountDetailSubtypeEnum.OFFICIAL_CHALLENGE_SUCCESS.getType(), 1, awardAmount, billNo, tradeTime,
                rid, activityEntity.getId(), null, activityEntity.getActivityType(), 0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, BigDecimal.ZERO);

        //通知
        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.CHALLENGE_SUCCESS_OFFICIAL_RANKING_RUN;
        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.CHALLENGE_SUCCESS_OFFICIAL_RANKING_RUN"), activityEntity.getActivityTitle());
        MessageBo message = appMessageService.assembleMessage(activityEntity.getId(), content, "4", NoticeTypeEnum.REWARD_NOTICE.getType());
        message.setActivityId(activityEntity.getId());
        ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
        appMessageService.sendImAndPushUserIds(Arrays.asList(userId), imMessageBo, message);
        return awardAmount;
    }

    /**
     * 官方赛事挑战失败奖励
     */
    BigDecimal handleOfficialEventChallengeFailure(ZnsRunActivityEntity activityEntity, Long userId, Long rid, Integer isCheat, ZnsUserRunDataDetailsEntity runDataDetailsEntity) {
        if (Integer.valueOf(1).equals(isCheat)) {
            return BigDecimal.ZERO;
        }
        if (null != activityEntity && StringUtils.hasText(activityEntity.getActivityConfig())) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
            if (null != jsonObject) {
                // 挑战失败劵奖励
                ActivityCouponDto activityCouponDto = JsonUtil.readValue(jsonObject.get(ApiConstants.CHALLENGE_FAILURE_COUPON_AWARD), ActivityCouponDto.class);
                if (activityCouponDto != null) {
                    for (int num = 0; num < activityCouponDto.getNum(); num++) {
                        userCouponManager.sendUserCoupon(activityCouponDto.getCouponId(), userId, activityEntity.getId());
                    }
                } else {
                    log.info("没有配置challengeAwardCouponObject");
                }
                // 挑战失败积分奖励
                Integer failureScore = MapUtil.getInteger(jsonObject.get(ApiConstants.CHALLENGE_FAILURE_SCORE_AWARD));
                if (failureScore != null && failureScore > 0) {
                    activityUserScoreService.beChallengeIncreaseAmount(failureScore, activityEntity.getId(), userId);
                } else {
                    log.info("官方赛事活动挑战失败不给用户奖励分数 ，用户id:" + userId + "分数为 " + failureScore);
                }
            }
        }
        BigDecimal awardAmount = null;
        if (null != activityEntity && StringUtils.hasText(activityEntity.getActivityConfig())) {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
            if (null != jsonObject) {
                // 挑战失败奖励金额
                awardAmount = MapUtil.getBigDecimal(jsonObject.get(ApiConstants.CHALLENGE_FAILURE_AWARD));
            }
        }
        if (null == awardAmount) {
            log.error("官方赛事挑战失败奖励配置不存在");
            return awardAmount;
        }
        //修改币种切换处理
        List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + userId, 0, -1);
        if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityEntity.getId().toString())) {
            log.info("币种切换不发放");
            awardAmount = BigDecimal.ZERO;
        }
        if (awardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("挑战排名失败奖励为0");
            return BigDecimal.ZERO;
        }

        //权益处理
        ActivityBrandRightsInterests brandRightsInterests = activityBrandInterestsBizService.getBrandRightsInterests(runDataDetailsEntity.getTreadmillId(), BrandRightsInterestEnum.CHALLENGE_FAIL_REWARD.getStatusCode(), activityEntity);
        Integer rightsInterestsType = null;
        Integer privilegeBrand = null;
        BigDecimal rightsInterestsMultiple = null;

        if (Objects.nonNull(brandRightsInterests)) {
            rightsInterestsMultiple = brandRightsInterests.getMultiple();
            BigDecimal brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(awardAmount, rightsInterestsMultiple);
            awardAmount = awardAmount.add(brandAward);
            rightsInterestsType = brandRightsInterests.getRightsInterestsType();
            privilegeBrand = brandRightsInterests.getBrand();
        }
        awardAmount = getUserCurrencyAmount(userId, awardAmount);
        // 给用户余额发送奖励
        userAccountService.increaseAmount(awardAmount, userId, true);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail0131(userId, AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD,
                AccountDetailSubtypeEnum.OFFICIAL_CHALLENGE_FAIL.getType(), 1, awardAmount, billNo, tradeTime,
                rid, activityEntity.getId(), rid, activityEntity.getActivityType(), 0L, "", privilegeBrand, rightsInterestsType, rightsInterestsMultiple, BigDecimal.ZERO);

        //完成通知
        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.CHALLENGE_COMPLETE_OFFICIAL_RANKING_RUN;
        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.CHALLENGE_COMPLETE_OFFICIAL_RANKING_RUN"), activityEntity.getActivityTitle());
        MessageBo message = appMessageService.assembleMessage(activityEntity.getId(), content, "4", NoticeTypeEnum.REWARD_NOTICE.getType());
        message.setActivityId(activityEntity.getId());
        ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
        appMessageService.sendImAndPushUserIds(Arrays.asList(userId), imMessageBo, message);
        return awardAmount;
    }


    /**
     * 返回官方赛事活动奖励档位
     */
    public static BigDecimal officialChallengeSuccessAwardAmount(int beChallengedRank, Map<String, Object> challengeAwardObject, boolean isChallengeOneSelf) {
        if (null == challengeAwardObject) {
            return null;
        }
        String levelKey = String.valueOf(beChallengedRank);
        //优先挑战自己的奖励
        if (isChallengeOneSelf) {
            levelKey = "-1";
        }
        if (StringUtil.isEmpty(levelKey)) {
            return null;
        }
        BigDecimal award = MapUtil.getBigDecimal(challengeAwardObject.get(levelKey));
        if (null == award) {
            return null;
        }
        return award;
    }


    /**
     * 返回官方赛事活动奖励档位
     */
    public static ActivityCouponDto officialChallengeSuccessAwardCouponAmount(int beChallengedRank, Map<String, Object> challengeAwardObject, boolean isChallengeOneSelf) {
        if (null == challengeAwardObject) {
            log.info("challengeAwardObject 为空");
            return null;
        }
        String levelKey = String.valueOf(beChallengedRank);
        //优先挑战自己的奖励
        if (isChallengeOneSelf) {
            log.info("levelKey 为 -1 ");
            levelKey = "-1";
        }
        if (StringUtil.isEmpty(levelKey)) {
            log.info("levelKey 为空");
            return null;
        }

        String award = String.valueOf(challengeAwardObject.get(levelKey));
        if (!StringUtils.hasText(award)) {
            log.info("没有配置challengeAwardObject");
            return null;
        }
        return JsonUtil.readValue(award, ActivityCouponDto.class);
    }

    /**
     * 返回官方赛事活动奖励档位
     */
    public static String officialChallengeSuccessAwardScoreAmount(int beChallengedRank, Map<String, Object> challengeAwardObject, boolean isChallengeOneSelf) {
        if (null == challengeAwardObject) {
            log.info("challengeAwardObject 为空");
            return null;
        }
        String levelKey = String.valueOf(beChallengedRank);
        //优先挑战自己的奖励
        if (isChallengeOneSelf) {
            log.info("levelKey 为 -1 ");
            levelKey = "-1";
        }
        if (StringUtil.isEmpty(levelKey)) {
            log.info("levelKey 为空");
            return null;
        }

        String award = String.valueOf(challengeAwardObject.get(levelKey));
        if (!StringUtils.hasText(award)) {
            log.info("没有配置challengeAwardObject");
            return null;
        }
        return award;
    }

    @Override
    public void wrapperCouponDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
        UserCoupon userCoupon = userCouponService.getUserCouponByActivityAndUserIdAndCouponType(activityEntity.getId(), userId, 5, null);
        if (Objects.isNull(userCoupon)) {
            List<UserCouponDiKou> userCoupons = userCouponService.selectCanUseConponList(activityEntity, userId);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(userCoupons)) {
                activityDetailVO.setCouponAmount(BigDecimal.ZERO);
            } else {
                activityDetailVO.setCouponAmount(BigDecimal.ZERO);
                if (Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) && activityDetailVO.getUserState() == 0) {
                    // 先计算绝对值
                    List<UserCouponDiKou> couponDiKous1 = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(activityEntity.getActivityEntryFee().subtract(coupon.getAmount()).abs())).collect(Collectors.toList());
                    couponDiKous1.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                        // free 0 值 完全匹配
                        if (i.getAbsAmount().compareTo(BigDecimal.ZERO) == 0) {
                            activityDetailVO.setCouponAmount(i.getAmount());
                            activityDetailVO.setUserCouponDiKou(i);
                        }
                    });
                    if (Objects.isNull(activityDetailVO.getUserCouponDiKou())) {
                        List<UserCouponDiKou> couponDiKous = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(activityEntity.getActivityEntryFee().subtract(coupon.getAmount()))).collect(Collectors.toList());
                        // 筛选 插值为 负数值
                        List<UserCouponDiKou> couponDiKous2 = couponDiKous.stream().filter(coupon -> coupon.getAbsAmount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(couponDiKous2)) {
                            couponDiKous2.stream().max(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                activityDetailVO.setCouponAmount(i.getAmount());
                                activityDetailVO.setUserCouponDiKou(i);
                            });
                        }
                        if (Objects.isNull(activityDetailVO.getUserCouponDiKou())) {
                            {
                                couponDiKous.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                    activityDetailVO.setCouponAmount(i.getAmount());
                                    activityDetailVO.setUserCouponDiKou(i);
                                });
                            }
                        }
                    }
                }
            }
        } else {
            activityDetailVO.setCouponAmount(userCoupon.getAmount());
            UserCouponDiKou userCouponDiKou = new UserCouponDiKou();
            BeanUtils.copyProperties(userCoupon, userCouponDiKou);
            activityDetailVO.setUserCouponDiKou(userCouponDiKou);
        }
    }

    @Override
    public void wrapperActivityRewardDetailByActivityType(ZnsRunActivityEntity activityEntity, Map<String, Object> jsonObjectConfig, RunActivityRewardDetailVO runActivityRewardDetailVO, ZnsUserEntity loginUser) {
        List<Map<String, Object>> officialEventAward = getOfficialEventAward(jsonObjectConfig);
        runActivityRewardDetailVO.setOfficialEventAward(officialEventAward);
        runActivityRewardDetailVO.setOfficialEventScoreAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_EVENT_SCORE_AWARD)));
        runActivityRewardDetailVO.setOfficialChallengeAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_CHALLENGE_AWARD)));
        runActivityRewardDetailVO.setOfficialChallengeScoreAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_CHALLENGE_SCORE_AWARD)));
        runActivityRewardDetailVO.setBeChallengedAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.BE_CHALLENGED_AWARD)));
        runActivityRewardDetailVO.setBeChallengedScoreAward(JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.BE_CHALLENGED_SCORE_AWARD)));
        runActivityRewardDetailVO.setChallengeFailureAward(MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.CHALLENGE_FAILURE_AWARD)));
        runActivityRewardDetailVO.setChallengeFailureScoreAward(MapUtil.getInteger(jsonObjectConfig.get(ApiConstants.CHALLENGE_FAILURE_SCORE_AWARD)));
        ActivityCouponDto activityCouponDto = JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.CHALLENGE_FAILURE_COUPON_AWARD), ActivityCouponDto.class);
        if (Objects.nonNull(activityCouponDto)) {
            ChallengeFailureCoupon challengeFailureCoupon = new ChallengeFailureCoupon();
            BeanUtils.copyProperties(activityCouponDto, challengeFailureCoupon);
            Long couponId = activityCouponDto.getCouponId();
            if (couponId != null) {
                Coupon coupon = couponService.selectCouponById(couponId);
                challengeFailureCoupon.setAmount(coupon.getAmount());
                challengeFailureCoupon.setCouponType(coupon.getCouponType());
            }
            runActivityRewardDetailVO.setChallengeFailureCouponAward(challengeFailureCoupon);
        }
        Map<String, Object> object = JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_EVENT_COUPON_AWARD));
        queryCouponAwardColumn(object);
        runActivityRewardDetailVO.setOfficialEventCouponAward(object);
        Map<String, Object> map = JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.OFFICIAL_CHALLENGE_COUPON_AWARD));
        queryCouponAwardColumn(map);
        runActivityRewardDetailVO.setOfficialChallengeCouponAward(map);
        Map<String, Object> beChallengedMap = JsonUtil.readValue(jsonObjectConfig.get(ApiConstants.BE_CHALLENGED_COUPON_AWARD));
        queryCouponAwardColumn(beChallengedMap);
        runActivityRewardDetailVO.setBeChallengedCouponAward(beChallengedMap);

    }

}
