package com.linzi.pitpat.data.clubservice.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class ClubActivityVo {
    //活动ID
    private Long activityId;
    //赛事封面
    private String activityCoverImage;
    //赛事标题
    private String activityTitle;

    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;

    //	string	跳转路径/路由
    private String url;
    //n	string	跳转参数，json字符串
    private String jumpParam;
    /**
     * 设备类型，1：跑步机，2：走步机 3：走跑一体 40：划船机  20:动感单车 30：脚踏车 ,101：心率臂带、102：心率肩带
     *
     * @see com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant.EquipmentTypeEnum
     */
    private List<Integer> deviceType;
}
