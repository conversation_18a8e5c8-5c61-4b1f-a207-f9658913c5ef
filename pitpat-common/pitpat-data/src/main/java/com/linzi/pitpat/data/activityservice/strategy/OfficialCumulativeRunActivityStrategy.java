package com.linzi.pitpat.data.activityservice.strategy;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.constant.enums.BrandRightsInterestEnum;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.OfficialCumulativeRunDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityBrandRightsInterests;
import com.linzi.pitpat.data.activityservice.model.entity.RunMilestoneStageConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunMilestoneStageConfigQuery;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialCumulativeActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.activityservice.service.RunMilestoneStageConfigService;
import com.linzi.pitpat.data.awardservice.biz.UserWearsBizService;
import com.linzi.pitpat.data.awardservice.mapper.UserCouponDao;
import com.linzi.pitpat.data.awardservice.model.dto.RunDetailDataDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.activity.MilestonePop;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.TreadmillBrandEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsAddressEntity;
import com.linzi.pitpat.data.mallservice.service.ZnsAddressService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.UserLightCity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserLightCityService;
import com.linzi.pitpat.data.vo.home.HomepageActivityVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 官方累计跑(里程碑)
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Service
@Slf4j
public class OfficialCumulativeRunActivityStrategy extends BaseOfficialActivityStrategy {


    @Autowired
    private UserCouponService userCouponService;

    @Autowired
    private UserCouponDao userCouponDao;

    @Autowired
    private ActivityUserScoreService activityUserScoreService;

    @Autowired
    private UserLightCityService userLightCityService;

    @Autowired
    private ZnsAddressService znsAddressService;

    @Resource
    private ExchangeRateConfigService exchangeRateConfigService;
    @Resource
    private UserWearsBizService userWearsBizService;

    @Override
    public void wrapperRunActivityBasicData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity) {
        super.wrapperRunActivityBasicData(activityEntity, activityDetailVO, userEntity);
    }

    @Override
    public void wrapperRunActivityDetailData(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, ZnsRunActivityUserEntity oneself) {
        super.wrapperRunActivityDetailData(activityEntity, activityDetailVO, userEntity, oneself);
        if (Objects.nonNull(oneself)) {
            // 因前端未修改，所以老里程碑详情页只能展示成美元
            ZnsUserAccountEntity userAccountEntity = userAccountService.getByUserId(oneself.getUserId());
            String currencyCode = I18nConstant.CurrencyCodeEnum.USD.getCode();
            if (Objects.nonNull(userAccountEntity)) {
                currencyCode = userAccountEntity.getCurrencyCode();
            }
            if (YesNoStatus.YES.getCode().equals(activityEntity.getIsNew()) && !I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyCode)) {
                BigDecimal exchangeRate = exchangeRateConfigService.selectByUsd2TargetCurrency(currencyCode).getExchangeRate();
                BigDecimal hasAward = activityDetailVO.getHasAward().divide(exchangeRate, 2, RoundingMode.HALF_UP);
                hasAward = I18nConstant.currencyFormat(currencyCode, hasAward);
                activityDetailVO.setHasAward(hasAward);
            }
        }
        //里程碑奖励规则
        List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObjectConfig.get("milepostAward"), MilepostAwardDto.class);
        //增加里程碑弹窗判断
        activityDetailVO.setIsPopupEntryMilestone(0);
        if (Objects.nonNull(oneself) && oneself.getCompletedLevel() > 0) {
            if (StringUtils.hasText(milepostAward.get(oneself.getCompletedLevel() - 1).getFinishRacePopUrl())) {
                MilestonePop milestonePop = milestonePopDao.selectOne(new QueryWrapper<MilestonePop>().lambda().eq(MilestonePop::getActivityId, activityEntity.getId()).eq(MilestonePop::getUserId, userEntity.getId()).eq(MilestonePop::getMileageNode, oneself.getCompletedLevel()).last("limit 1"));
                if (Objects.isNull(milestonePop)) {
                    activityDetailVO.setIsPopupEntryMilestone(1);
                }
            }
        }
        if (Objects.nonNull(oneself)) {
            if (oneself.getRunMileage() != null && oneself.getRunMileage().compareTo(BigDecimal.ZERO) != 0) {
                activityDetailVO.setRunMileageTotal(oneself.getRunMileage());
            }
            if (oneself.getRunTime() != null && oneself.getRunTime().intValue() != 0) {
                activityDetailVO.setRunTimeTotal(oneself.getRunTime());
            }
        }
        List<MilepostAwardDto> milepostCouponAward = new ArrayList<>();
        if (jsonObjectConfig.get("milepostCouponAward") != null) {
            milepostCouponAward = JsonUtil.readList(jsonObjectConfig.get("milepostCouponAward"), MilepostAwardDto.class);
        }
        //查看个人完成情况
        Integer completedLevel = 0;
        if (Objects.nonNull(oneself)) {
            completedLevel = oneself.getCompletedLevel();
        }
        for (int i = 0; i < milepostAward.size(); i++) {
            MilepostAwardDto milepostAwardDto = milepostAward.get(i);
            ZnsUserAccountDetailEntity accountDetail = userAccountDetailService.getAccountDetail(userEntity.getId(), activityEntity.getId(), AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD.getType(), i + 1);
            if (Objects.nonNull(accountDetail)) {
                milepostAwardDto.setIsComplete(1);
            } else {
                milepostAwardDto.setIsComplete(0);
            }
            if (i == milepostAward.size() - 1) {
                if (activityDetailVO.getCompleteRuleType() == 1) {
                    activityDetailVO.setRunMileage(milepostAwardDto.getMilepost());
                } else {
                    activityDetailVO.setRunTime(milepostAwardDto.getMilepost().intValue());
                }
                activityDetailVO.setRunningGoals(Arrays.asList(milepostAwardDto.getMilepost().intValue()));
            }
            if (completedLevel > i) {
                milepostAwardDto.setIsComplete(1);
            }
        }
        BigDecimal maxReward = activityDetailVO.getMaxReward();
        if (userEntity != null) {
            String currencyCode = userAccountService.getUserCurrency(userEntity.getId()).getCurrencyCode();
            maxReward = I18nConstant.currencyFormat(currencyCode, maxReward);
        }
        activityDetailVO.setMilepostAward(milepostAward);
        activityDetailVO.setMilepostCouponAward(milepostCouponAward);
        activityDetailVO.setActivityTotalBonus(maxReward);
        activityDetailVO.setWarmPrompt(getWarmPrompt(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType()));
    }

    @Override
    protected BigDecimal getPreMaxReward(Integer activityType, Integer userCount) {
        BigDecimal maxReward = super.getPreMaxReward(activityType, userCount);

        try {
            List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObjectConfig.get("milepostAward"), MilepostAwardDto.class);
            maxReward = milepostAward.get(milepostAward.size() - 1).getCumulativeAward();
        } catch (Exception e) {
            log.error("getPreMaxReward 失败，e:{}", e);
        }

        return maxReward;
    }

    @Override
    public void wrapperRunActivityUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, ZnsUserEntity userEntity, Integer activityUserStatus) {
        List<RunActivityUserVO> runActivityUsers = activityUserManager.findRunActivityUsers(activityEntity, new Page(1, 20), userEntity.getId(), activityDetailVO, null, null, activityUserStatus);
        activityDetailVO.setActivityUsers(runActivityUsers);
    }

    @Override
    public void wrapperRunActivityHistoryUsers(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO) {
        // 计算当前时间往前推三个月的时间作为开始时间,此参数单独用于优化sql，并无实际意义
        String startTime = ZonedDateTime.now().minusMonths(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        List<Map<String, Object>> runActivityHistoryUsers = activityUserManager.getRunActivityHistoryUsers(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(), null, startTime);
        if (!CollectionUtils.isEmpty(runActivityHistoryUsers) && runActivityHistoryUsers.size() >= 6) {
            activityDetailVO.setRunActivityHistoryUsers(runActivityHistoryUsers);
        }
    }

    @Override
    @Transactional
    public void handleRunActivityEnd(ZnsRunActivityEntity activityEntity) {
        super.handleRunActivityEnd(activityEntity);
        if (activityEntity.getStatus() == 1) {
            //查询所有完赛用户（完成所有目标）
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .activityId(activityEntity.getId()).isDelete(0)
                    .build();
            switch (activityEntity.getCompleteRuleType()) {
                case 1:
                    userQuery.setApply("run_mileage>=target_run_mileage");
                    break;
                case 2:
                    userQuery.setApply("run_time>=target_run_time");
                    break;
                default:
                    break;
            }

            List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
            for (ZnsRunActivityUserEntity activityUser : list) {
                // 保证金退回
                handleOfficialEarnestMoney(activityEntity, activityUser);
                // 证书
                RunDetailDataDto runDetailDataDto = new RunDetailDataDto();
                List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetailsEntities = userRunDataDetailsService.getUserDetailByActivityId(activityUser.getUserId(), activityUser.getActivityId());
                if (CollectionUtils.isEmpty(znsUserRunDataDetailsEntities)) {
                    continue;
                }
                for (ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity : znsUserRunDataDetailsEntities) {
                    runDetailDataDto.setRunTime(runDetailDataDto.getRunTime() + znsUserRunDataDetailsEntity.getRunTime());
                    runDetailDataDto.setRunMileage(runDetailDataDto.getRunMileage().add(znsUserRunDataDetailsEntity.getRunMileage()));
                }
                ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsEntities.get(znsUserRunDataDetailsEntities.size() - 1);
                Long treadmillId = znsUserRunDataDetailsEntity.getTreadmillId();
                ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findById(treadmillId);
                // 设备判定null
                if (Objects.nonNull(treadmillEntity)) {
                    TreadmillBrandEnum treadmillBrandEnum = TreadmillBrandEnum.findByName(treadmillEntity.getBrand());
                    runDetailDataDto.setType(treadmillBrandEnum.getType());
                    runDetailDataDto.setBrand(treadmillBrandEnum.getName());
                    Double averagePace = znsUserRunDataDetailsEntities.stream().mapToInt(ZnsUserRunDataDetailsEntity::getAveragePace).average().orElse(0d);
                    BigDecimal averageVelocity = znsUserRunDataDetailsEntities.stream().map(ZnsUserRunDataDetailsEntity::getAverageVelocity).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(znsUserRunDataDetailsEntities.size()), 2, BigDecimal.ROUND_HALF_UP);
                    runDetailDataDto.setAveragePace(averagePace.intValue());
                    runDetailDataDto.setAverageVelocity(averageVelocity);
                    ZonedDateTime lastTime = znsUserRunDataDetailsEntities.get(znsUserRunDataDetailsEntities.size() - 1).getLastTime();
                    runDetailDataDto.setActivityDate(lastTime);
                    super.genUserCertificate(activityEntity, activityUser, runDetailDataDto);
                }
            }
        } else {
            if (activityEntity.getBonusRuleType() != 1) {
                RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                        .activityId(activityEntity.getId()).isDelete(0)
                        .build();

                List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(userQuery);
                for (ZnsRunActivityUserEntity activityUser : list) {
                    //积分退回
                    useUserScore(activityEntity, activityUser.getUserId(), true);
                    if (activityEntity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                        // 取消活动退款
                        AccountDetailTypeEnum detailType = Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? AccountDetailTypeEnum.FEE : AccountDetailTypeEnum.SECURITY_FUND;
                        String refundRemark = Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) ? "fee return" : "Deposit return";
                        runActivityProcessManager.cancelActivityRefund(activityEntity, detailType, activityUser.getUserId(), refundRemark);
                    }
                }
            }
        }

    }

    public void handleOfficialCumulativeRun(ZnsUserRunDataDetailsEntity userRunDataDetail) {
        //非跑步机上传数据不处理
        if (userRunDataDetail.getDataSource() != 0) {
            log.info("handleOfficialCumulativeRun结束，数据上传非app");
            return;
        }
        //自动报名累计跑活动处理
        automaticEnrollment(userRunDataDetail.getUserId());
        //查询参加的累计跑活动
        List<OfficialCumulativeRunDto> officialCumulativeRun = runActivityUserService.getOfficialCumulativeRun(userRunDataDetail.getUserId());
        if (CollectionUtils.isEmpty(officialCumulativeRun)) {
            log.info("当前用户未参与累计跑活动");
            return;
        }
        // activityType = 5
        ZnsRunActivityConfigEntity configEntity = runActivityConfigService.getByType(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(), null);
        String config = configEntity.getActivityConfig();
        Map<String, Object> object = JsonUtil.readValue(config);
        Integer dailyMileageLimit = MapUtil.getInteger(object.get(ApiConstants.DAILY_MILEAGE_LIMIT));        // 官方累计跑赛事活动每日里程限制
        Integer dailyRunTimeLimit = MapUtil.getInteger(object.get(ApiConstants.DAILY_RUN_TIME_LIMIT));

        int averageVelocity = userRunDataDetail.getRunMileage().multiply(new BigDecimal(3600)).divide(new BigDecimal(userRunDataDetail.getRunTime()), 2, BigDecimal.ROUND_HALF_DOWN).intValue();

        for (OfficialCumulativeRunDto activityUserEntity : officialCumulativeRun) {
            //速度限制
            Integer rateLimiting = activityUserEntity.getRateLimiting();
            Integer rateLimitType = activityUserEntity.getRateLimitType();
            // 限速类型
            BigDecimal velocityLimiting = null;
            if (rateLimiting != -1) {
                // 限制了最低速
                if (rateLimitType == 1 && averageVelocity < rateLimiting) {
                    log.info("里程碑处理结束，速度不达标，需求速度：{},活动id：{}，用户id：{}，本次跑步id：{}，速度：{}", rateLimiting, activityUserEntity.getActivityId(), userRunDataDetail.getUserId(), userRunDataDetail.getId(), averageVelocity);
                    continue;
                }
                // 限制了最高速
                if (rateLimitType == 2 && averageVelocity > rateLimiting) {
                    log.info("里程碑处理结束，速度超过限速，需求速度：{},活动id：{}，用户id：{}，本次跑步id：{}，速度：{}", rateLimiting, activityUserEntity.getActivityId(), userRunDataDetail.getUserId(), userRunDataDetail.getId(), averageVelocity);
                    continue;
                }
                velocityLimiting = new BigDecimal(rateLimiting).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_DOWN); // 单位 km/h
            }
            //当日里程处理
            ZnsUserRunDataDetailsEntity userRunDataDay = userRunDataDetailsService.getUserRunMileageDay(userRunDataDetail.getUserId(), ZonedDateTime.now(), userRunDataDetail.getId(), activityUserEntity.getActivityStartTime(), activityUserEntity.getCreateTime(), velocityLimiting, rateLimitType);
            int runMileage = userRunDataDetail.getRunMileage().intValue();      // 本次跑的里程
            Integer runTime = userRunDataDetail.getRunTime();
            if (activityUserEntity.getCompleteRuleType() == 1) {            // 完成规则类型：1表示完成跑步里程，2表示完成跑步时长
                int userRunMileageDay = userRunDataDay.getRunMileage().intValue();             // 当天的里程
                int sumMil = runMileage + userRunMileageDay;                                    // 总里程
                if (userRunMileageDay >= dailyMileageLimit) {     // 如果用户当天跑步里程达到当天里程限制
                    log.info("里程碑处理结束,用户达到统计里程限制,活动id：{}，用户id：{}", activityUserEntity.getActivityId(), userRunDataDetail.getUserId());
                    continue;
                }
                // 当用户当天跑过的距离 大于 当日限制时 。
                // 假如，每天5000米。 之前用户 跑了 4000米，此时用户跑了1600米。
                // 则需要将本次跑步的 RunMileage 改成 1000米 ， RunTime 改成1000米 所用的时间 ， 多余跑的时间不算。
                if (sumMil > dailyMileageLimit) {
                    int surplusMileage = dailyMileageLimit - userRunMileageDay;
                    if (surplusMileage <= 0) {
                        log.info("里程碑处理结束,用户达到统计时长限制,剩余距离小于0,活动id：{}，用户id：{}", activityUserEntity.getActivityId(), userRunDataDetail.getUserId());
                        continue;
                    }
                    if (surplusMileage < runMileage) {
                        userRunDataDetail.setRunMileage(new BigDecimal(surplusMileage));
                        //重新计算剩余距离需要的时间
                        try {
                            int newRunTime = surplusMileage * 3600 / averageVelocity;
                            userRunDataDetail.setRunTime(newRunTime);
                        } catch (Exception e) {
                            log.info("handleOfficialCumulativeRun setRunTime error,e:{}", e);
                        }
                    }
                }
            } else {
                int userRunTimeDay = userRunDataDay.getRunTime();
                int sumTime = runTime + userRunTimeDay;
                if (userRunTimeDay >= dailyMileageLimit) {
                    log.info("里程碑处理结束,用户达到统计时长限制,活动id：{}，用户id：{}", activityUserEntity.getActivityId(), userRunDataDetail.getUserId());
                    continue;
                }
                if (sumTime > dailyRunTimeLimit) {
                    int surplusRunTime = dailyRunTimeLimit - userRunTimeDay;
                    if (surplusRunTime <= 0) {
                        log.info("里程碑处理结束,用户达到统计时长限制,剩余时长小于0,活动id：{}，用户id：{}", activityUserEntity.getActivityId(), userRunDataDetail.getUserId());
                        continue;
                    }
                    if (surplusRunTime < runTime) {
                        userRunDataDetail.setRunTime(surplusRunTime);
                    }
                }
            }
            //奖励发放
            BigDecimal award = handleOfficialCumulativeRunAward(userRunDataDetail, activityUserEntity);
            runActivityUserService.addRunData(activityUserEntity.getId(), activityUserEntity.getCompleteRuleType(), userRunDataDetail.getRunTime(), userRunDataDetail.getRunMileage(), award);
            // 奖励发放标识
            log.info("里程碑处理结束,,活动id：{}，用户id：{},增加时间：{}，增加距离：{}，增加奖励：{}", activityUserEntity.getActivityId(), userRunDataDetail.getUserId(), userRunDataDetail.getRunTime(), userRunDataDetail.getRunMileage(), award);
        }
    }

    @Override
    public void homePageActivityMap(HomepageActivityVo map, ZnsRunActivityEntity activity, Long userId, String zoneId) {
        super.homePageActivityMap(map, activity, userId, zoneId);
        List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObjectConfig.get("milepostAward"), MilepostAwardDto.class);
        //如果有里程碑。标题显示下一个
        String nextTitle = map.getNextTitle();
        if (StringUtils.hasText(nextTitle)) {
            map.setMainTitle(nextTitle);
        }
        //查询活动用户
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activity.getId(), userId);
        if (Objects.isNull(activityUser)) {
            map.setIsEnroll(0);
            map.setRunMileage(0);
            map.setRunTime(0);
            MilepostAwardDto milepostAwardDto = milepostAward.get(0);
            map.setMilepost(milepostAwardDto.getMilepost().intValue());
            map.setAward(milepostAwardDto.getAward());
            return;
        }
        map.setIsEnroll(1);

        map.setRunMileage(activityUser.getRunMileage().intValue());
        map.setRunTime(activityUser.getRunTime());
        map.setUserState(activityUser.getUserState());
        map.setAward(activityUser.getRunAward());
        BigDecimal runMileage = activityUser.getRunMileage();
        int runTime = activityUser.getRunTime();

        if (activity.getCompleteRuleType() == 1) {
            map.setProgressRte(activityUser.getRunMileage().divide(new BigDecimal(activityUser.getTargetRunMileage()), 2, BigDecimal.ROUND_DOWN));
            if (runMileage.intValue() >= activityUser.getTargetRunMileage()) {
                map.setMilepost(activityUser.getTargetRunMileage());
                map.setProgressRte(BigDecimal.ONE);
                map.setUserState(4);
                map.setMainTitle(activity.getActivityTitle());
                return;
            }
        } else if (activity.getCompleteRuleType() == 2) {
            map.setProgressRte(new BigDecimal(activityUser.getRunTime()).divide(new BigDecimal(activityUser.getTargetRunTime()), 2, BigDecimal.ROUND_DOWN));
            if (runTime >= activityUser.getTargetRunTime()) {
                map.setMilepost(activityUser.getTargetRunTime());
                map.setProgressRte(BigDecimal.ONE);
                map.setUserState(4);
                map.setMainTitle(activity.getActivityTitle());
                return;
            }
        }

        for (int i = 0; i < milepostAward.size(); i++) {
            MilepostAwardDto milepostAwardDto = milepostAward.get(i);
            map.setAward(milepostAwardDto.getAward());

            if (activity.getCompleteRuleType() == 1) {
                if (runMileage.compareTo(milepostAwardDto.getMilepost()) < 0) {
                    map.setMilepost(milepostAwardDto.getMilepost().intValue());
                    return;
                }
            } else if (activity.getCompleteRuleType() == 2) {
                if (runTime < milepostAwardDto.getMilepost().intValue()) {
                    map.setMilepost(milepostAwardDto.getMilepost().intValue());
                    return;
                }
            }
        }
    }

    @Override
    public List<? extends SimpleRunActivityVO> getActivityList(ZnsUserEntity user, boolean isTest, boolean checkVersion, boolean isHomepage, Integer source, Integer completeRuleType, List<Integer> runWalkStatus, Integer rateLimitType) {
        Long userId = user.getId();
        List<ZnsRunActivityEntity> list = runActivityService.getActivityByTypeAndState(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(), isTest, checkVersion, false, userId, isHomepage, source, null, runWalkStatus, user.getCountry(), null);

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, userId);
        Map<Long, ZnsRunActivityUserEntity> activityUserEntityMap = activityUsers.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (x, y) -> x));
        List<OfficialCumulativeActivityListVO> activityList = new ArrayList<>();
        for (ZnsRunActivityEntity activity : list) {
            OfficialCumulativeActivityListVO vo = new OfficialCumulativeActivityListVO(activity);
            Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
            List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
            MilepostAwardDto milepostAwardDto = milepostAward.get(milepostAward.size() - 1);
            BigDecimal maxReward = milepostAwardDto.getCumulativeAward();
            if (Objects.isNull(maxReward)) {
                maxReward = BigDecimal.ZERO;
            }
            vo.setMaxReward(maxReward);
            vo.setMilepost(milepostAwardDto.getMilepost().intValue());
            vo.setCoverImage(MapUtil.getString(jsonObject.get(ApiConstants.COVER_IMAGE)));
            ZnsRunActivityUserEntity znsRunActivityUserEntity = activityUserEntityMap.get(activity.getId());
            if (Objects.nonNull(znsRunActivityUserEntity)) {
                vo.setIsEnroll(1);
                vo.setAward(znsRunActivityUserEntity.getRunAward());
                vo.setRunMileage(znsRunActivityUserEntity.getRunMileage().intValue());
                vo.setRunTime(znsRunActivityUserEntity.getRunTime());
            }
            Integer integer = userCouponService.countUserCouponByCondition(userId, activity.getId(), activity.getActivityType(), activity.getTaskConfigId(), activity.getBatchNo());
            vo.setCanUserCoupon(integer > 0 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            activityList.add(vo);
        }
        return activityList;
    }

    /**
     * 自动报名
     *
     * @param userId
     */
    private void automaticEnrollment(Long userId) {
        ZnsUserEntity znsUser = userService.findById(userId);
        if (znsUser.getIsRobot() == 1) {
            return;
        }
        List<String> countries = Lists.newArrayList();
        if (UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode().equals(znsUser.getIsRobot())) {
            //真实用户要按国家过滤
            countries.add(znsUser.getCountry());
            countries.add(UserConstant.ALL_COUNTRY);
        }


        //查询当前自动报名的活动
        RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                .isDelete(0)
                .isTest(znsUser.getIsTest())
                .automaticEnrollment(1)
                .status(1)
                .maxApplicationStartTime(ZonedDateTime.now())
                .minActivityEndTime(ZonedDateTime.now())
                .activityStateIn(Arrays.asList(0, 1))
                .activityType(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType());
        if (!CollectionUtils.isEmpty(countries)) {
            runActivityQueryBuilder.countryIn(countries);
        }
        List<ZnsRunActivityEntity> list = runActivityService.findList(runActivityQueryBuilder.build());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> activityIds = list.stream().map(ZnsRunActivityEntity::getId).collect(Collectors.toList());
        List<ZnsRunActivityUserEntity> activityUsers = runActivityUserService.findActivityUsers(activityIds, userId);

        if (!CollectionUtils.isEmpty(activityUsers) && activityUsers.size() == list.size()) {
            return;
        }

        Map<Long, ZnsRunActivityUserEntity> userEntityMap = activityUsers.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (x, y) -> x));

        for (ZnsRunActivityEntity activity : list) {
            ZnsRunActivityUserEntity znsRunActivityUserEntity = userEntityMap.get(activity.getId());
            if (Objects.nonNull(znsRunActivityUserEntity)) {
                continue;
            }
            //只处理免费的累计跑活动 , bonusRuleType 奖金规则类型：1表示免费参加，2表示保证金参加 3:费用
            if (activity.getBonusRuleType() != 1 || activity.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0) {
                continue;
            }
            handleUserActivityState(activity, 1, znsUser, "", null, true, null, null, false);
        }
    }


    /**
     * 累计跑奖励
     *
     * @param userRunDataDetail
     * @param activityUserEntity
     * @return
     */
    private BigDecimal handleOfficialCumulativeRunAward(ZnsUserRunDataDetailsEntity userRunDataDetail, OfficialCumulativeRunDto activityUserEntity) {
        log.info("累计跑奖励开始发放，用户id：{}，活动id：{}", userRunDataDetail.getUserId(), activityUserEntity.getActivityId());
        BigDecimal award = BigDecimal.ZERO;
        try {
            Map<String, Object> jsonObject = JsonUtil.readValue(activityUserEntity.getActivityConfig());
            //  "milepostAward":[
            //      {"award":1,"cumulativeAward":1,"milepost":1000,"sort":1},
            //      {"award":1,"cumulativeAward":2,"milepost":2000,"sort":2}
            //  ]
            List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);
            if (milepostAward.isEmpty()) {
                return award;
            }
            //计算结束剩余时间
            int surplusTime = DateUtil.betweenSecond(ZonedDateTime.now(), activityUserEntity.getActivityEndTime());
            Integer maxGoal = 0;
            for (int i = 0; i < milepostAward.size(); i++) {
                MilepostAwardDto milepostAwardDto = milepostAward.get(i);
                boolean isComplete = false;
                if (activityUserEntity.getCompleteRuleType() == 1) {
                    BigDecimal runMileage = activityUserEntity.getRunMileage().add(userRunDataDetail.getRunMileage());
                    log.info("用户跑步里程 runMileage: {}， activityUserEntity: {}, userRunDataDetail: {}", runMileage, activityUserEntity.getRunMileage(), userRunDataDetail.getRunMileage());
                    if (runMileage.compareTo(milepostAwardDto.getMilepost()) >= 0) {
                        isComplete = true;
                    }
                } else if (activityUserEntity.getCompleteRuleType() == 2) {
                    int runTime = activityUserEntity.getRunTime() + userRunDataDetail.getRunTime();
                    log.info("用户跑步时间 runTime: {}, activityUserEntity{}: ,userRunDataDetail: {} ", runTime, activityUserEntity.getRunTime(), userRunDataDetail.getRunTime());
                    if (runTime >= milepostAwardDto.getMilepost().intValue()) {
                        isComplete = true;
                    }
                }

                log.info("累计跑阶段目标：{}，完成规则类型：{}，是否完成：{}", milepostAwardDto.getMilepost(), activityUserEntity.getCompleteRuleType(), isComplete);
                if (isComplete) {
                    if (jsonObject.get("milepostCouponAward") != null) {
                        List<MilepostAwardDto> milepostCouponAward = JsonUtil.readList(jsonObject.get("milepostCouponAward"), MilepostAwardDto.class);
                        log.info("handleOfficialCumulativeRunAward milepostCouponAward当前地数据 ：" + JsonUtil.writeString(milepostCouponAward));
                        if (milepostCouponAward != null && milepostCouponAward.size() > 0) {
                            MilepostAwardDto milepostCouponAwardDto = milepostCouponAward.get(i);
                            UserCoupon userCoupon = userCouponDao.selectUserCouponByActivityIdCouponIdUserIdMilepost(activityUserEntity.getActivityId(), milepostCouponAwardDto.getCouponId(), activityUserEntity.getUserId(), milepostCouponAwardDto.getMilepost() + "");
                            if (userCoupon == null) {
                                // TODO 目前每个里程碑职能发放一张奖励，这里循环逻辑是否有必要
                                for (int num = 0; num < milepostCouponAwardDto.getNum(); num++) {
                                    log.info(" 累计跑奖励发送券 activityId =  " + activityUserEntity.getActivityId() + ",userId= " + activityUserEntity.getUserId() + ",couponId = " + milepostCouponAwardDto.getCouponId());
                                    userCouponManager.sendUserCouponMilepost(milepostCouponAwardDto.getCouponId(), activityUserEntity.getUserId(), activityUserEntity.getActivityId(), milepostCouponAwardDto.getMilepost() + "");
                                    log.info("券发放完成");
                                }
                            }
                        }
                    }
                    //发放累计跑服装奖励
                    if (Objects.nonNull(jsonObject.get("milepostWearsAward"))) {
                        List<MilepostWearAwardDto> milepostWearsAward = JsonUtil.readList(jsonObject.get("milepostWearsAward"), MilepostWearAwardDto.class);
                        log.info("handleOfficialCumulativeRunAward milepostCouponAward当前地数据 ：" + JsonUtil.writeString(milepostWearsAward));
                        if (!CollectionUtils.isEmpty(milepostWearsAward)) {
                            MilepostWearAwardDto milepostWearsAwardDto = getMilepostWearAwardDto(milepostWearsAward, i);
                            if (Objects.nonNull(milepostWearsAwardDto) && Objects.nonNull(milepostWearsAwardDto.getWearType())) {
                                userWearsBizService.sendUserWearMilepost(milepostWearsAwardDto, activityUserEntity);
                            } else {
                                log.warn("第{}几个里程碑未配置里程碑的服装奖励", i + 1);
                            }
                        }
                    }
                    if (!Integer.valueOf(0).equals(milepostAwardDto.getScoreNum())) {
                        //发放积分
                        ActivityUserScore activityUserScore = activityUserScoreService.selectUserScoreByActivityIdUserIdMilepost(activityUserEntity.getActivityId(), activityUserEntity.getUserId(), milepostAwardDto.getMilepost().intValue());
                        if (activityUserScore == null) {
                            log.info(" 累计跑奖励发送积分 activityId =  " + activityUserEntity.getActivityId() + ",userId= " + activityUserEntity.getUserId() + ",score = " + milepostAwardDto.getScore());
                            sendScore(activityUserEntity, milepostAwardDto);
                        }
                    }
                    if (milepostAwardDto.getLightCityId() != null && !Long.valueOf(0).equals(milepostAwardDto.getLightCityId())) {
                        //点亮城市
                        log.info("累计跑关卡完成，点亮城市记录  cityId = " + milepostAwardDto.getLightCityId());
                        UserLightCity userLightCityRecord = userLightCityService.findByCityIdAndUserId(milepostAwardDto.getLightCityId(), activityUserEntity.getUserId());
                        if (Objects.isNull(userLightCityRecord)) {
                            ZnsAddressEntity city = znsAddressService.findById(milepostAwardDto.getLightCityId());
                            if (Objects.nonNull(city)) {
                                UserLightCity userLightCity = new UserLightCity();
                                userLightCity.setCityId(milepostAwardDto.getLightCityId());
                                userLightCity.setUserId(activityUserEntity.getUserId());
                                userLightCity.setCityName(city.getName());
                                userLightCity.setStateId(city.getParentId());
                                ZnsAddressEntity state = znsAddressService.findById(city.getParentId());
                                userLightCity.setStateName(state.getName());
                                userLightCity.setModifieTime(ZonedDateTime.now());
                                log.info("点亮城市图片地址 url = " + city.getCityUrl());
                                if (StringUtils.hasText(city.getCityUrl())) {
                                    userLightCity.setCityUrl(city.getCityUrl());
                                } else {
                                    userLightCity.setCityUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202308/ilC13xE5VeCb1256.png");
                                }
                                userLightCityService.insert(userLightCity);
                            }
                            log.error("查询zns_address为空，参数出错！");
                        }
                        log.info("当前城市已经被点亮");
                    }
                    ZnsUserAccountDetailEntity accountDetail = userAccountDetailService.getAccountDetail(activityUserEntity.getUserId(), activityUserEntity.getActivityId(), AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD.getType(), i + 1);
                    if (Objects.nonNull(accountDetail)) {
                        log.info("累计跑奖励处理结束，奖励重复");
                        continue;
                    }
                    //保存关卡
                    ZnsRunActivityUserEntity update = new ZnsRunActivityUserEntity();
                    update.setId(activityUserEntity.getId());
                    update.setCompletedLevel(i + 1);
                    runActivityUserService.updateById(update);

                    //权益
                    ActivityBrandRightsInterests scoreRightsInterest = activityBrandInterestsBizService.getBrandRightsInterests(BrandRightsInterestEnum.MILESTONE_REWARD.getStatusCode(), activityUserEntity.getActivityId(), activityUserEntity.getUserId());
                    BigDecimal milepostAwardDtoAward = milepostAwardDto.getAward();
                    BigDecimal brandAward = BigDecimal.ZERO;
                    Integer brand = null;
                    Integer rightsInterestsType = null;
                    BigDecimal multiple = null;
                    if (Objects.nonNull(scoreRightsInterest)) {
                        brandAward = activityBrandRightsInterestsService.getRightsInterestsAward(milepostAwardDto.getAward(), scoreRightsInterest.getMultiple());
                        milepostAwardDtoAward = milepostAwardDtoAward.add(brandAward);
                        brand = scoreRightsInterest.getBrand();
                        rightsInterestsType = scoreRightsInterest.getRightsInterestsType();
                        multiple = scoreRightsInterest.getMultiple();
                    }
                    //
                    milepostAwardDtoAward = getUserCurrencyAmount(userRunDataDetail.getUserId(), milepostAwardDtoAward);
                    //修改币种切换处理
                    List<String> activityIds = redisTemplate.opsForList().range(RedisConstants.USER_NO_FINISH_ACTIVITY + activityUserEntity.getUserId(), 0, -1);
                    if (!CollectionUtils.isEmpty(activityIds) && activityIds.contains(activityUserEntity.getActivityId().toString())) {
                        log.info("币种切换不发放");
                        milepostAwardDtoAward = BigDecimal.ZERO;
                    }
                    // 给用户余额发送奖励
                    userAccountService.increaseAmount(milepostAwardDtoAward, userRunDataDetail.getUserId(), true);
                    // 更新活动用户奖励金额
                    award = award.add(milepostAwardDtoAward);
                    log.info("奖励金额判断前");
                    if (award.compareTo(BigDecimal.ZERO) <= 0 || milepostAwardDtoAward.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    log.info("奖励金额判断后");
                    // 新增用户奖励余额明细
                    String billNo = NanoId.randomNanoId();
                    ;
                    ZonedDateTime tradeTime = ZonedDateTime.now();
                    userAccountDetailService.addRunActivityAccountDetail0131(userRunDataDetail.getUserId(), AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD,
                            i + 1, 1, milepostAwardDtoAward, billNo, tradeTime, activityUserEntity.getActivityId()
                            , activityUserEntity.getActivityId(), userRunDataDetail.getId(), activityUserEntity.getActivityType(), 0L, "", brand, rightsInterestsType, multiple, brandAward);


                    if (Integer.valueOf(0).equals(userRunDataDetail.getIsCheat())) {
                        //通知
                        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.AWARD_OFFICIAL_CUMULATIVE_RUN;
                        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.AWARD_OFFICIAL_CUMULATIVE_RUN"), activityUserEntity.getActivityTitle());
                        MessageBo message = appMessageService.assembleMessage(activityUserEntity.getActivityId(), content, "4", NoticeTypeEnum.REWARD_NOTICE.getType());
                        message.setActivityId(activityUserEntity.getActivityId());
                        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
                        activityEntity.setId(activityUserEntity.getActivityId());
                        activityEntity.setActivityConfig(activityUserEntity.getActivityConfig());
                        activityEntity.setActivityType(activityUserEntity.getActivityType());
                        ImMessageBo imMessageBo = appMessageService.assembleImActivityMessageAward(activityEntity, content);
                        appMessageService.sendImAndPushUserIds(Arrays.asList(userRunDataDetail.getUserId()), imMessageBo, message);
                    }
                } else {
                    //判断是否剩余20%以内
                    Integer surplus = 0;
                    if (activityUserEntity.getCompleteRuleType() == 1) {
                        BigDecimal runMileage = activityUserEntity.getRunMileage().add(userRunDataDetail.getRunMileage());
                        surplus = milepostAwardDto.getMilepost().subtract(runMileage).intValue();
                    } else if (activityUserEntity.getCompleteRuleType() == 2) {
                        int runTime = activityUserEntity.getRunTime() + userRunDataDetail.getRunTime();
                        surplus = milepostAwardDto.getMilepost().intValue() - runTime;
                    }

                    log.info("surplus = " + surplus);
                    BigDecimal surplusRatio = new BigDecimal(surplus).divide(milepostAwardDto.getMilepost(), 2, BigDecimal.ROUND_DOWN);
                    log.info("surplusRatio = " + surplusRatio);
                    if (surplusRatio.compareTo(new BigDecimal(0.2)) < 0) {
                        //防止重复通知
                        String key = RedisConstants.SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN_NOTIFICATION + activityUserEntity.getActivityId() + "_" + activityUserEntity.getUserId() + "_" + i;
                        Object value = redisTemplate.opsForValue().get(key);
                        if (Objects.nonNull(value)) {
                            continue;
                        }
                        String surplusGoal = "";
                        if (activityUserEntity.getCompleteRuleType() == 1) {
                            surplusGoal = new BigDecimal(surplus).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_DOWN).toString() + " km";
                            if (activityUserEntity.getMeasureUnit() == 1) {
                                surplusGoal = new BigDecimal(surplus).divide(new BigDecimal(1600), 2, BigDecimal.ROUND_HALF_DOWN).toString() + " miles";
                            }
                        } else if (activityUserEntity.getCompleteRuleType() == 2) {
                            surplusGoal = surplus / 60 + " min";
                        }

                        //通知【Only %s left to the next milestone! Come on! Bonus is waiting for you!】
                        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN;
                        String content = String.format(I18nMsgUtils.getMessage("activity.notify.remark.ActivityNotificationEnum.SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN"), surplusGoal);
                        MessageBo message = appMessageService.assembleMessage(activityUserEntity.getActivityId(), content, "4", NoticeTypeEnum.ACTIVITY_INVITATION.getType());
                        message.setActivityId(activityUserEntity.getActivityId());
                        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
                        activityEntity.setId(activityUserEntity.getActivityId());
                        activityEntity.setActivityConfig(activityUserEntity.getActivityConfig());
                        activityEntity.setActivityType(activityUserEntity.getActivityType());
                        ImMessageBo imMessageBo = appMessageService.assembleImActivityMessage(activityEntity, content);
                        appMessageService.sendImAndPushUserIds(Arrays.asList(userRunDataDetail.getUserId()), imMessageBo, message);
                        if (surplusTime < 0) {
                            surplusTime = 24 * 60 * 60;
                        }
                        redisTemplate.opsForValue().set(key, "1", surplusTime, TimeUnit.SECONDS);
                        return award;
                    }
                }
            }
            log.info("累计跑奖励结束发放，用户id：{}，活动id：{}", userRunDataDetail.getUserId(), activityUserEntity.getActivityId());
        } catch (Exception e) {
            log.error("handleOfficialCumulativeRunAward error: {}", e.getMessage(), e);
        }
        return award;
    }

    private static MilepostWearAwardDto getMilepostWearAwardDto(List<MilepostWearAwardDto> milepostWearsAward, int i) {
        //这里需要按照 sort id 对应 index 获取该里程碑的奖励
        MilepostWearAwardDto milepostWearsAwardDto = null;
        for (MilepostWearAwardDto milepostWearAwardDto : milepostWearsAward) {
            if (Objects.equals(milepostWearAwardDto.getSort(), i + 1)) {
                milepostWearsAwardDto = milepostWearAwardDto;
                break;
            }
        }
        return milepostWearsAwardDto;
    }

    private void sendScore(OfficialCumulativeRunDto activityUserEntity, MilepostAwardDto milepostAwardDto) {
        if (NumberUtils.leZero(milepostAwardDto.getScore())) {
            log.info("sendScore end, score <= 0");
            return;
        }
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setUserId(activityUserEntity.getUserId());
        activityUserScore.setScore(milepostAwardDto.getScore());
        activityUserScore.setSource(10);
        activityUserScore.setActivityId(activityUserEntity.getActivityId());
        activityUserScore.setStatus(1);
        activityUserScore.setExchangeTime(ZonedDateTime.now());
        activityUserScore.setIncome(1);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setAwardTime(ZonedDateTime.now());
        activityUserScore.setMilepost(milepostAwardDto.getMilepost().intValue());
        activityUserScoreService.save(activityUserScore);
    }

    @Override
    public void wrapperCouponDetail(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
        UserCoupon userCoupon = userCouponService.getUserCouponByActivityAndUserIdAndCouponType(activityEntity.getId(), userId, 5, null);
        if (Objects.isNull(userCoupon)) {
            List<UserCouponDiKou> userCoupons = userCouponService.selectCanUseConponList(activityEntity, userId);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(userCoupons)) {
                activityDetailVO.setCouponAmount(BigDecimal.ZERO);
            } else {
                activityDetailVO.setCouponAmount(BigDecimal.ZERO);
                if (Arrays.asList(3, 5).contains(activityEntity.getBonusRuleType()) && activityDetailVO.getUserState() == 0) {
                    // 先计算绝对值
                    List<UserCouponDiKou> couponDiKous1 = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(activityEntity.getActivityEntryFee().subtract(coupon.getAmount()).abs())).collect(Collectors.toList());
                    couponDiKous1.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                        // free 0 值 完全匹配
                        if (i.getAbsAmount().compareTo(BigDecimal.ZERO) == 0) {
                            activityDetailVO.setCouponAmount(i.getAmount());
                            activityDetailVO.setUserCouponDiKou(i);
                        }
                    });
                    if (Objects.isNull(activityDetailVO.getUserCouponDiKou())) {
                        List<UserCouponDiKou> couponDiKous = userCoupons.stream().peek(coupon -> coupon.setAbsAmount(activityEntity.getActivityEntryFee().subtract(coupon.getAmount()))).collect(Collectors.toList());
                        // 筛选 插值为 负数值
                        List<UserCouponDiKou> couponDiKous2 = couponDiKous.stream().filter(coupon -> coupon.getAbsAmount().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(couponDiKous2)) {
                            couponDiKous2.stream().max(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                activityDetailVO.setCouponAmount(i.getAmount());
                                activityDetailVO.setUserCouponDiKou(i);
                            });
                        }
                        if (Objects.isNull(activityDetailVO.getUserCouponDiKou())) {
                            {
                                couponDiKous.stream().min(Comparator.comparing(UserCouponDiKou::getAbsAmount)).ifPresent(i -> {
                                    activityDetailVO.setCouponAmount(i.getAmount());
                                    activityDetailVO.setUserCouponDiKou(i);
                                });
                            }
                        }
                    }
                }
            }
        } else {
            activityDetailVO.setCouponAmount(userCoupon.getAmount());
            UserCouponDiKou userCouponDiKou = new UserCouponDiKou();
            BeanUtils.copyProperties(userCoupon, userCouponDiKou);
            activityDetailVO.setUserCouponDiKou(userCouponDiKou);
        }
    }

    @Autowired
    private RunMilestoneStageConfigService runMilestoneStageConfigService;

    @Override
    public void wrapperStageImageUrl(ZnsRunActivityEntity activityEntity, RunActivityDetailVO activityDetailVO, Long userId) {
        activityDetailVO.setShowMode(activityEntity.getShowMode());
        if (activityEntity.getShowMode() == 1) {
            ZnsRunActivityUserEntity znsRunActivityUserEntity = runActivityUserService.findActivityUser(activityEntity.getId(), userId);
            RunMilestoneStageConfigQuery build = RunMilestoneStageConfigQuery.builder()
                    .isDelete(YesNoStatus.NO.getCode())
                    .activityId(activityEntity.getId())
                    .build();
            build.addOrderByDesc("stage");
            RunMilestoneStageConfig end = runMilestoneStageConfigService.findOne(
                    build);

            activityDetailVO.setStageImageEndUrl(end.getStageImageUrl());
            if (Objects.nonNull(znsRunActivityUserEntity)) {
                RunMilestoneStageConfigQuery oneQuery = RunMilestoneStageConfigQuery.builder()
                        .isDelete(YesNoStatus.NO.getCode())
                        .activityId(activityEntity.getId())
                        .stage(znsRunActivityUserEntity.getCompletedLevel())
                        .build();
                RunMilestoneStageConfig one = runMilestoneStageConfigService.findOne(oneQuery);
                activityDetailVO.setStageImageUrl(one.getStageImageUrl());
            } else {
                activityDetailVO.setStageImageUrl(end.getStageImageUrl());
            }

        }
    }

    @Override
    public void wrapperActivityRewardDetailByActivityType(ZnsRunActivityEntity activityEntity, Map<String, Object> jsonObjectConfig, RunActivityRewardDetailVO runActivityRewardDetailVO, ZnsUserEntity loginUser) {
        List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObjectConfig.get("milepostAward"), MilepostAwardDto.class);
        MilepostAwardDto maxDto = milepostAward.stream()
                .max(Comparator.comparing(MilepostAwardDto::getSort))
                .orElse(null);
        if (maxDto != null) {
            String currencyCode = userAccountService.getUserCurrency(loginUser.getId()).getCurrencyCode();
            BigDecimal maxReward = I18nConstant.currencyFormat(currencyCode, maxDto.getCumulativeAward());
            runActivityRewardDetailVO.setMaxReward(maxReward);
            if (maxDto.getScoreNum() != null) {
                runActivityRewardDetailVO.setMaxScore(Long.valueOf(maxDto.getScoreNum()));
            }
        }


    }
}
