package com.linzi.pitpat.data.entity.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 一周快乐跑用户配置表
 *
 * <AUTHOR>
 * @since 2023-03-07
 */

@Data
@NoArgsConstructor
@TableName("zns_one_week_config")
public class OneWeekConfig implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    public final static String CLASS_NAME = "com.linzi.pitpat.data.entity.activity.OneWeekConfig:";

    public final static String all = CLASS_NAME + "*";
    public final static String id_ = CLASS_NAME + "id";                       // 主键id
    public final static String is_delete = CLASS_NAME + "is_delete";          //
    public final static String gmt_create = CLASS_NAME + "gmt_create";        //
    public final static String gmt_modified = CLASS_NAME + "gmt_modified";    //
    public final static String activity_id = CLASS_NAME + "activity_id";      // 活动id
    public final static String user_id = CLASS_NAME + "user_id";              // 用户id
    public final static String status_ = CLASS_NAME + "status";               // 状态
    public final static String type_ = CLASS_NAME + "type";                   // 1 ，一周快乐跑，2 绿帽子活动
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //
    private Integer isDelete;
    //
    private ZonedDateTime gmtCreate;
    //
    private ZonedDateTime gmtModified;
    //活动id
    private Long activityId;
    //用户id
    private Long userId;
    //状态
    private Integer status;
    //1 ，一周快乐跑，2 绿帽子活动 3， 愚人节活动
    private Integer type;

    @Override
    public String toString() {
        return "OneWeekConfig{" +
                ",id=" + id +
                ",isDelete=" + isDelete +
                ",gmtCreate=" + gmtCreate +
                ",gmtModified=" + gmtModified +
                ",activityId=" + activityId +
                ",userId=" + userId +
                ",status=" + status +
                ",type=" + type +
                "}";
    }
}
