package com.linzi.pitpat.data.entity.award;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@TableName("zns_activity_user_award_review_log")
public class ActivityUserAwardReviewLog implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    //主键
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除 0：不删除 1：删除
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //审核人
    private String reviewName;
    //1审核通过
    private Integer status;
    //main id
    private Long activityId;
}
