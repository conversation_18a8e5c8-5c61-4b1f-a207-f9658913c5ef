ALTER TABLE `zns_treadmill`
MODIFY COLUMN `firmware_type` varchar(50)  NOT NULL DEFAULT 'lx' COMMENT '蓝牙芯片类型，lx：乐芯蓝牙固件，xzx_self：芯中芯自研蓝牙固件,xzx_purchase：芯中芯选品蓝牙固件' ,algorithm=inplace, lock=none,
ADD COLUMN `is_new_ota` int(0) NOT NULL DEFAULT 0 COMMENT '是否新版ota，0：老板本，1：新版本' ,algorithm=inplace, lock=none ;

ALTER TABLE `zns_treadmill_group`
ADD COLUMN `equipment_main_type` varchar(64) NOT NULL DEFAULT '-' COMMENT '设备类型：treadmill-跑步机，bicycle-脚踏，rowing-划船机，bike-单车' ,
DROP INDEX `idx_group_name`,
ADD INDEX `idx_group_name`(`group_name`, `is_delete`) USING BTREE COMMENT '分组名索引',
ADD INDEX `idx_main_type`(`equipment_main_type`, `is_delete`) USING BTREE COMMENT '设备类型索引';

ALTER TABLE `zns_device_version`
ADD COLUMN `default_language_code` varchar(50)  NULL DEFAULT NULL COMMENT '默认语言code' ,
ADD COLUMN `default_upgrade_context` text  NULL DEFAULT NULL COMMENT '默认语言更新内容' ;

-- 新版OAT升级记录表
CREATE TABLE `zns_ota_v2_upgrade_log`
(
    `id`                     bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`              int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`             datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`           datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `ota_v2_upgrade_rule_id` bigint unsigned                                                       DEFAULT NULL COMMENT '新版升级规则id @zns_ota_v2_upgrade_rule',
    `type`                   int                                                          NOT NULL DEFAULT '0' COMMENT '固件类型（十进制）同 zns_ota_v2_addr表type，0默认蓝牙固件',
    `bluetooth_mac`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '蓝牙mac',
    `upgrade_desc`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '升级描述',
    `state`                  int                                                          NOT NULL DEFAULT '0' COMMENT ' 升级状态 -1：失败，0: 开始下载 99: 下载完成等待重启； 100：成功',
    `fail_reason`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '失败原因',
    `up_way`                 int                                                          NOT NULL DEFAULT '0' COMMENT '升级方式，1：WiFi升级，2：蓝牙升级',
    `current_version`        varchar(10) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '升级前的版本（可读，eg:V36.2）',
    `upgrade_version`        varchar(10) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '升级到的版本（可读，eg:V36.2）',
    `current_version_ten`    int                                                                   DEFAULT NULL COMMENT '升级前的版本(十进制)',
    `upgrade_version_ten`    int                                                                   DEFAULT NULL COMMENT '升级到的版本(十进制)',
    PRIMARY KEY (`id`),
    KEY `idx_ble_type` (`bluetooth_mac`, `type`, `is_delete`) USING BTREE COMMENT '蓝牙类型索引',
    KEY `idx_ble_upver` (`bluetooth_mac`, `upgrade_version`, `is_delete`) USING BTREE COMMENT '蓝牙升级版本索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='新版OAT升级记录表';

-- 设备详情表
CREATE TABLE `zns_treadmill_detail`
(
    `id`                       bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`                int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`               datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`             datetime                                                              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`                  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改者',
    `bluetooth_mac`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '蓝牙mac',
    `bluetooth_version`        varchar(10) COLLATE utf8mb4_general_ci                                DEFAULT NULL COMMENT '蓝牙版固件本号（可读eg：V36.2）',
    `bluetooth_version_ten`    int                                                                   DEFAULT NULL COMMENT '蓝牙版固件本号(十进制，eg：9218)',
    `bluetooth_type`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '蓝牙版固件子类型，lx：乐芯蓝牙固件，xzx_self：芯中芯自研蓝牙固件,xzx_purchase：芯中芯选品蓝牙固件',
    `bluetooth_addr_type`      int                                                                   DEFAULT NULL COMMENT '蓝牙版固件目标地址类型',
    `up_control_version`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '上控固件本号（可读eg：V4.0.0.2）',
    `up_control_version_ten`   int                                                                   DEFAULT NULL COMMENT '上控版本版本号(十进制)',
    `up_control_type`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '上控芯片类型',
    `up_control_addr_type`     int                                                                   DEFAULT NULL COMMENT '上控固件目标地址类型',
    `down_control_version`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '下控固件本号（可读eg：V4.0.0.2）',
    `down_control_version_ten` int                                                                   DEFAULT NULL COMMENT '下控版本版本号(十进制)',
    `down_control_type`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '下控芯片类型，huada：华大',
    `down_control_addr_type`   int                                                                   DEFAULT NULL COMMENT '下控固件目标地址类型',
    `keyboard_version`         varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '按键板固件本号（可读eg：V4.0.0.2）',
    `keyboard_version_ten`     int                                                                   DEFAULT NULL COMMENT '按键板版本号(十进制)',
    `keyboard_type`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '按键板芯片类型，syct06：赛元CT06，syct09r：赛元CT09R',
    `keyboard_addr_type`       int                                                                   DEFAULT NULL COMMENT '按键板目标地址类型',
    `is_set_upgrade`           int                                                          NOT NULL DEFAULT '0' COMMENT '是否设置过自动更新，0：未设置，1：已设置',
    `is_auto_upgrade`          int                                                          NOT NULL DEFAULT '0' COMMENT '是否自动更新，0：不更新，1：已更新',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_bluetooth_mac` (`bluetooth_mac`, `is_delete`) USING BTREE COMMENT '设备蓝牙唯一索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='设备详情表';

-- 设备蓝牙固件I18n内容
CREATE TABLE `zns_device_version_i18n`
(
    `id`                bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`         int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`        datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '修改者',
    `device_version_id` bigint unsigned                                               NOT NULL DEFAULT '0' COMMENT '老版oat固件id（zns_device_version）',
    `language_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '语言名称',
    `language_code`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '语言code',
    `upgrade_context`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新内容',
    PRIMARY KEY (`id`),
    KEY `idx_device_version_id` (`device_version_id`, `language_code`, `is_delete`) USING BTREE COMMENT '固件id索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='设备蓝牙固件I18n内容';

-- 新版OTA目标地址配置
CREATE TABLE `zns_ota_v2_addr`
(
    `id`                  bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`           int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`          datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '修改者',
    `title`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
    `target_addr`         varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '目标地址（0x0100）',
    `type`                int                                                           NOT NULL COMMENT '固件类型（十进制）',
    `type_desc`           varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '固件类型描述',
    `equipment_main_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT 'treadmill' COMMENT '设备类型：treadmill-跑步机，bicycle-脚踏，rowing-划船机，bike-单车',
    `chip_main_type`      int                                                           NOT NULL DEFAULT '0' COMMENT '芯片主类型, 1:蓝牙,2:按键板,3:下控,4:上控',
    `chip_sub_type`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '芯片子类型, 【蓝牙】xzx_self：芯中芯自研,xzx_purchase：芯中芯选品；【按键板】syct06：赛元CT06，syct09r：赛元CT09R；【下控】huada：华大',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='新版OTA目标地址配置';

CREATE TABLE `zns_ota_v2_firmware`
(
    `id`                      bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`               int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`              datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`            datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '修改者',
    `firmware_package_id`     bigint                                                        NOT NULL COMMENT '固件包ID',
    `addr_id`                 bigint                                                        NOT NULL COMMENT '固件目标地址配置id@zns_ota_v2_addr#id',
    `file_url`                text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NOT NULL COMMENT '固件url',
    `file_md5`                varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '镜像md5',
    `file_size`               int unsigned                                                  NOT NULL DEFAULT '0' COMMENT '镜像大小(b)',
    `name`                    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '固件名称',
    `ver`                     varchar(10) COLLATE utf8mb4_general_ci                        NOT NULL COMMENT '固件版本号（可读，eg:V36.2）',
    `ver_ten`                 int                                                           NOT NULL COMMENT '固件版本号(十进制，eg：9218)',
    `type`                    int                                                           NOT NULL COMMENT '固件类型@zns_ota_v2_addr#type',
    `type_desc`               varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '固件类型描述@ zns_ota_v2_addr#type_desc',
    `equipment_main_type`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT 'treadmill' COMMENT '设备类型：treadmill-跑步机，bicycle-脚踏，rowing-划船机，bike-单车',
    `upgrade_desc`            text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新说明',
    `default_language_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci           DEFAULT NULL COMMENT '默认语言code',
    `default_upgrade_context` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '默认语言更新内容',
    `chip_main_type`          int                                                           NOT NULL DEFAULT '0' COMMENT '芯片主类型, 1:蓝牙,2:按键板,3:下控,4:上控',
    `chip_sub_type`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT 'lx' COMMENT '芯片子类型, 【蓝牙】  xzx_self：芯中芯自研,xzx_purchase：芯中芯选品；【按键板】  syct06：赛元CT06，syct09r：赛元CT09R；【下控】  huada：华大',
    PRIMARY KEY (`id`),
    KEY `idx_package_id` (`firmware_package_id`, `is_delete`) USING BTREE COMMENT '升级包id索引',
    KEY `idx_addr_id` (`addr_id`, `is_delete`) USING BTREE COMMENT '目标地址id索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='新版ota固件';

-- 固件包配置I18n内容
CREATE TABLE `zns_ota_v2_firmware_i18n`
(
    `id`                 bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`          int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`       datetime                                                              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改者',
    `ota_v2_firmware_id` bigint unsigned                                              NOT NULL DEFAULT '0' COMMENT '新版oat固件id',
    `language_name`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语言名称',
    `language_code`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语言code',
    `upgrade_context`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新内容',
    PRIMARY KEY (`id`),
    KEY `idx_firmware_id` (`ota_v2_firmware_id`, `language_code`, `is_delete`) USING BTREE COMMENT '固件id索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='固件包配置I18n内容';

-- 新版OTA固件包
CREATE TABLE `zns_ota_v2_firmware_package`
(
    `id`           bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`    int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '修改者',
    `title`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '固件包名称',
    `state`        tinyint unsigned                                              NOT NULL DEFAULT '1' COMMENT '固件包状态，0：停用，1：启用',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='新版OTA固件包';

-- 新版ota升级规则
CREATE TABLE `zns_ota_v2_upgrade_rule`
(
    `id`                  bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`           int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`          datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        datetime                                                               DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '修改者',
    `state`               tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '上架状态，0：停用，1：启用',
    `name`                varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '升级批次名称',
    `firmware_package_id` bigint                                                        NOT NULL COMMENT '固件包ID',
    `equipment_main_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT 'treadmill' COMMENT '设备类型：treadmill-跑步机，bicycle-脚踏，rowing-划船机，bike-单车',
    `product_code`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '设备型号,多个用,隔开, all 是通用',
    `up_type`             int unsigned                                                  NOT NULL DEFAULT '0' COMMENT '升级类型，1:用户主动升级，2:强制升级',
    `up_dev_group_id`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '升级设备组id，多个用，隔开（zns_treadmill_group表id）',
    `up_by_bluetooth`     int                                                           NOT NULL DEFAULT '0' COMMENT '是否支持蓝牙升级：0-不支持，1：支持',
    `up_by_wifi`          int                                                           NOT NULL DEFAULT '0' COMMENT '是否支持wifi升级：0-不支持，1：支持',
    `reboot`              int                                                           NOT NULL DEFAULT '-1' COMMENT '重启状态：0: 立即重启，-1: 无需重启，N: 重启时间间隔（单位：秒）',
    PRIMARY KEY (`id`),
    KEY `idx_main_type` (`equipment_main_type`, `state`, `is_delete`) USING BTREE COMMENT '设备类型索引'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='新版ota升级规则';

-- 固件名称I18n
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '1', 'OTA_V2_CHIP_MAIN_TYPE', 'en_US', 'Communication Module', 'OTA蓝牙芯片（通讯模组）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '2', 'OTA_V2_CHIP_MAIN_TYPE', 'en_US', 'Keypad Panel', 'OTA按键板（按键面板）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '3', 'OTA_V2_CHIP_MAIN_TYPE', 'en_US', 'Motor Controller', 'OTA下控（电机控制器）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '4', 'OTA_V2_CHIP_MAIN_TYPE', 'en_US', 'Display System', 'OTA上控（显示系统）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '1', 'OTA_V2_CHIP_MAIN_TYPE', 'fr_CA', 'Module de Communication', 'OTA蓝牙芯片（通讯模组）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '2', 'OTA_V2_CHIP_MAIN_TYPE', 'fr_CA', 'Panneau de Clavier', 'OTA按键板（按键面板）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '3', 'OTA_V2_CHIP_MAIN_TYPE', 'fr_CA', 'Contrôleur de Moteur', 'OTA下控（电机控制器）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '4', 'OTA_V2_CHIP_MAIN_TYPE', 'fr_CA', 'Système d\'Affichage', 'OTA上控（显示系统）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '1', 'OTA_V2_CHIP_MAIN_TYPE', 'de_DE', 'Kommunikationsmodul', 'OTA蓝牙芯片（通讯模组）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '2', 'OTA_V2_CHIP_MAIN_TYPE', 'de_DE', 'Tastaturpanel', 'OTA按键板（按键面板）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '3', 'OTA_V2_CHIP_MAIN_TYPE', 'de_DE', 'Motorsteuerung', 'OTA下控（电机控制器）');
INSERT INTO `zns_i18n_key_value`(`gmt_create`, `gmt_modified`, `is_delete`, `key_code`, `scene`, `lang_code`, `value`, `description`) VALUES (now(), now(), 0, '4', 'OTA_V2_CHIP_MAIN_TYPE', 'de_DE', 'Displaysystem', 'OTA上控（显示系统）');

-- 新版OTA版本上报时间间隔
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `app_version`, `is_visualization`, `keyword`, `parameter_type`) VALUES ( '新版OTA版本上报时间间隔(秒)', 'ota_v2_report_interval_sec', '600', 'N', 'admin', NULL, 'admin', NULL, '新版OTA版本上报时间间隔(秒)', NULL, 0, NULL, NULL);

-- 新版OTA版本24小时升级次数
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `app_version`, `is_visualization`, `keyword`, `parameter_type`) VALUES ( '新版OTA版本24小时升级次数', 'ota_v2_upgrade_count', '10', 'N', 'admin', NULL, 'admin', NULL, '新版OTA版本24小时升级次数', NULL, 0, NULL, NULL);