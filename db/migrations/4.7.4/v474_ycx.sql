CREATE TABLE `zns_traffic_investment_package_activity` (
         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
         `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
         `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
         `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
         `package_id` bigint NOT NULL DEFAULT '-1' COMMENT '投流包ID',
         `material_token` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材token',
         `h5_url` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'H5连接',
         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='投流包渠道活动表';

ALTER TABLE `zns_user_extra`
    ADD COLUMN `material_token` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材token',
    ADD COLUMN `h5_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'H5连接';


ALTER TABLE `zns_user_detail`
    ADD COLUMN `traffic_investment_activity_flag` int DEFAULT '0' COMMENT '投流渠道活动标识 0：未参与，1：参与过';

INSERT INTO `pitpat`.`zns_app_route_config` (`one_level`, `two_level`, `ios_route`, `ios_param`, `android_route`, `android_param`, `route_type`, `sort`, `main_route`, `secondary_route`, `main_param`, `is_home_customize`, `old_main_route`, `old_main_param`) VALUES ('PitPat赛事', '自由挑战赛', '', '', '', '', 0, 0, 'lzrn://Race/LAListPage', NULL, '', 0, '', '');
