CREATE TABLE `zns_treadmill_white_list` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `equipment_no` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '设备标号',
  `sence` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT 'LA/待扩展 ...',
  `equipment_id` bigint DEFAULT '0' COMMENT '设备id',
  PRIMARY KEY (`id`),
  KEY `idx_equipment_no` (`equipment_no`),
  KEY `idx_equipment_id` (`equipment_id`)
) ENGINE=InnoDB COMMENT='跑步机设备管理白名单表';