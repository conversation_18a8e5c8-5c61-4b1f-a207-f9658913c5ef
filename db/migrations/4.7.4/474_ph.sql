INSERT INTO `schedule_job` (`create_time`,`bean_name`,`method_name`,`params`,`cron_expression`,`status`,`remark`,`test_params`) VALUES ('2025-07-02 10:33:04','roomStatusTask','freeChallengeActivity',NULL,'3 * * * * ?','2','freeRoom房间状态处理',NULL);
INSERT INTO `schedule_job` (`create_time`,`bean_name`,`method_name`,`params`,`cron_expression`,`status`,`remark`,`test_params`) VALUES ('2025-07-03 08:19:43','mainActivityTask','endNoSendAwardFeeChallengeActivity',NULL,'1 0 2 * * ?','2','自由挑战跑活动最终排行及奖励',NULL);
ALTER TABLE `zns_pop_record`
	MODIFY COLUMN `type` INT   NULL    COMMENT '类型,1：新人弹窗 2:会员 3:新券弹窗4:新人引导离线pk弹窗5:14天新人任务 6:阶段里程完成弹窗 7:新人任务已领取全部奖励 8:新人任务结束-未领取全部奖励 9:新人任务结束-未完成所有奖励 10:渠道新人奖励弹窗 11:自由挑战跑奖励弹窗' ;
