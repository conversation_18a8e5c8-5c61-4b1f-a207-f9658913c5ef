INSERT INTO `pitpat`.`schedule_job`( `create_time`, `bean_name`, `method_name`, `params`, `cron_expression`, `status`, `remark`, `test_params`) VALUES ( '2025-07-03 08:19:43', 'freeActivityTask', 'autoGenerateNextActivity', NULL, '7 0/10 * * * ?', 2, '自由挑战跑活动自动生成任务', NULL);

CREATE TABLE `zns_free_room` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除 0：不删除 1：删除',
                                 `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                 `activity_id` int DEFAULT NULL COMMENT '活动id',
                                 `room_number` bigint NOT NULL DEFAULT '0' COMMENT '房间编号',
                                 `owner_user_id` bigint NOT NULL DEFAULT '0' COMMENT '房主id',
                                 `room_status` tinyint NOT NULL DEFAULT '0' COMMENT '房间状态 0 等待中 1 进行中 2 已结束',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_activity_id` (`activity_id`),
                                 KEY `idx_room_num` (`room_number`),
                                 KEY `idx_uid` (`owner_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='房间表';

CREATE TABLE `zns_free_activity_room_user` (
                                               `id` bigint NOT NULL AUTO_INCREMENT,
                                               `activity_id` int DEFAULT NULL COMMENT '活动id',
                                               `user_id` int DEFAULT NULL COMMENT '用户id',
                                               `room_num` bigint DEFAULT NULL COMMENT '房间号',
                                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户状态，in在房间/out退出',
                                               `detail_id` int DEFAULT NULL COMMENT '跑步id',
                                               `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                               `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                               `is_delete` int DEFAULT '0',
                                               PRIMARY KEY (`id`),
                                               KEY `idx_activity_id` (`activity_id`),
                                               KEY `idx_uid` (`user_id`),
                                               KEY `idx_room_num` (`room_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户自由活动房间状态表';

CREATE TABLE `zns_award_config_medal` (
                                           `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                           `is_delete` tinyint DEFAULT '0',
                                           `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP,
                                           `award_config_id` int NOT NULL DEFAULT '-1' COMMENT '配置ID，关联zns_award_config',
                                           `medal_id` bigint NOT NULL DEFAULT '-1' COMMENT '勋章id',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_award_id` (`award_config_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='奖励配置勋章表';



###复制一个线上自由跑的配置

##地图配置
