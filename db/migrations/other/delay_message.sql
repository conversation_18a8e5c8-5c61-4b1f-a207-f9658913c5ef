CREATE TABLE `zns_delay_queue_message` (
       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       `message_id` varchar(64)  NOT NULL COMMENT '消息ID（UUID）',
       `batch_id` varchar(128)  NOT NULL COMMENT '消息处理 ID（exchange + routing_key + sent_time）',
       `message_type` varchar(50)  NOT NULL COMMENT '消息类型 payload class name',
       `exchange` varchar(100)  NOT NULL COMMENT '交换机名称',
       `routing_key` varchar(100)  NOT NULL COMMENT '路由键',
       `payload` text  NOT NULL COMMENT '消息内容（JSON格式）',
       `scheduled_time` datetime NOT NULL COMMENT '计划发送时间',
       `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待发送，1-已发送，2-已取消',
       `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `sent_time` datetime DEFAULT NULL COMMENT '实际发送时间',
       PRIMARY KEY (`id`),
       UNIQUE KEY `uk_message_id` (`message_id`),
       KEY `idx_scheduled_time_status` (`scheduled_time`,`status`),
       KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT  COMMENT='简化的延迟消息表';


CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250701` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250702` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250703` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250704` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250705` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250706` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250707` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250708` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250709` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250710` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250711` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250712` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250713` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250714` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250715` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250716` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250717` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250718` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250719` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250720` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250721` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250722` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250723` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250724` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250725` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250726` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250727` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250728` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250729` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250730` LIKE `zns_delay_queue_message`;
CREATE TABLE IF NOT EXISTS `zns_delay_queue_message_20250731` LIKE `zns_delay_queue_message`;



INSERT INTO `schedule_job` ( `bean_name`, `method_name`, `params`, `cron_expression`, `status`, `remark`, `test_params`)
VALUES
    ( 'delayMessageTask', 'doScanFutureMessages', NULL, '5 */3 * * * ?', 2, '延迟消息扫描未来 3~5 分钟的消息', NULL),
    ( 'delayMessageTask', 'doScanMessages', NULL, '12 * * * * ?', 2, '延迟消息扫描未来 -5 ～5分钟的消息', NULL),
    ( 'delayMessageTask', 'doScanPastMessages', NULL, '9 */10 * * * ?', 2, '延迟消息扫描补偿过去 1小时的消息', NULL),
    ( 'delayMessageTask', 'doCreateTable', NULL, '6 3 * 10,20 * ?',2, '延迟消息创建下个月的分表', NULL);
