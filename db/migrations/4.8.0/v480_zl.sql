ALTER TABLE `zns_order_refund`
    ADD COLUMN `source` varchar(10) NOT NULL DEFAULT 'app' COMMENT '售后来源，app：app提交，admin：后台提交' ,
    MODIFY COLUMN `refund_type` tinyint(0) NOT NULL DEFAULT 0  COMMENT '退款类型  0：仅退款， 1：退货退款， 2：换货 ' ,
    ADD COLUMN `creator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者' AFTER `gmt_modified`;

ALTER TABLE `zns_refund_remark`
    MODIFY COLUMN `refund_type` tinyint(0) NOT NULL DEFAULT 0  COMMENT '退款类型 : 0：仅退款，1：退货退款，2：换货';

ALTER TABLE `zns_user_kol`
    ADD COLUMN `type` int(0) NOT NULL DEFAULT 1 COMMENT 'kol类型，1：常规KOL，2：赛事KOL';

CREATE TABLE `zns_goods_label`
(
    `id`                    bigint           NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`             int              NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`            datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`          datetime                  DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`               varchar(32)      NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`              varchar(32)      NOT NULL DEFAULT '' COMMENT '修改者',
    `title`                 varchar(100)              DEFAULT NULL COMMENT '名称',
    `state`                 tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态，0：停用，1：启用',
    `img_ratio`             decimal(8, 2)    NOT NULL COMMENT '图片比率',
    `start_time`            datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效开始时间',
    `end_time`              datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效结束时间',
    `sort`                  bigint           NOT NULL DEFAULT '0' COMMENT '排序，越大越在前面',
    `default_language_code` varchar(50)      NOT NULL COMMENT '默认语言code',
    `default_label_img`     text             NOT NULL COMMENT '默认语言标签图片',
    PRIMARY KEY (`id`),
    KEY `idx_title` (`title`, `is_delete`) USING BTREE COMMENT '名称索引',
    KEY `idx_enable_time` (`start_time`, `end_time`, `state`, `is_delete`) USING BTREE COMMENT '起止时间索引',
    KEY `idx_sort` (`sort`, `is_delete`) USING BTREE COMMENT '排序索引'
) ENGINE = InnoDB COMMENT ='商品标签配置';


CREATE TABLE `zns_goods_label_i18n`
(
    `id`             bigint          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`      int             NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`   datetime                 DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`        varchar(32)     NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`       varchar(32)     NOT NULL DEFAULT '' COMMENT '修改者',
    `goods_label_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品标签配置id',
    `language_name`  varchar(50)     NOT NULL COMMENT '语言名称',
    `language_code`  varchar(50)     NOT NULL COMMENT '语言code',
    `label_img`      varchar(100)             DEFAULT NULL COMMENT '标签原因',
    PRIMARY KEY (`id`),
    KEY `idx_goods_label_id` (`goods_label_id`) USING BTREE COMMENT '商品标签配置id'
) ENGINE = InnoDB COMMENT ='商品标签配置I18n';


CREATE TABLE `zns_goods_label_rel`
(
    `id`             bigint          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `is_delete`      int             NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
    `gmt_create`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`   datetime                 DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    `creator`        varchar(32)     NOT NULL DEFAULT '' COMMENT '创建者',
    `modifier`       varchar(32)     NOT NULL DEFAULT '' COMMENT '修改者',
    `goods_id`       bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `goods_label_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品标签配置id',
    PRIMARY KEY (`id`),
    KEY `idx_goods_id` (`goods_id`, `goods_label_id`, `is_delete`) USING BTREE COMMENT '商品id索引',
    KEY `idx_goods_label_id` (`goods_label_id`, `goods_id`, `is_delete`) USING BTREE COMMENT '商品标签配置id索引'
) ENGINE = InnoDB COMMENT ='商品标签关系';