spring:
  cloud:
    gcp:
#      预发    pitpat-sub-sub-preview
      subscription-name: pitpat-sub-sub-release
      project-id: pitpat-447010
      credentials:
        location: classpath:google-pay/pitpat-447010-2025-01-06.json
      pubsub:
        # 可选的 PubSub 特定配置
        subscriber:
          executor-threads: 2
# 日志配置
logging:
  level:
    com.linzi.pitpat: info
    org.springframework: warn
pitpat:
  api:
    h5Url: https://pitpat.pitpatfitness.com/pc/resetpassword
    mailIcon: http://api-market.oss-cn-hangzhou.aliyuncs.com/file/2021/10/*****************.png
    mailHost: imap.exmail.qq.com
    mailAccount: <EMAIL>
    mailPassword: gPuhxH6QLvgFKQev
    url: https://pitpat.pitpatfitness.com
    mallH5Url: https://h5.pitpatfitness.com/#
    profile: ./pitpat/uploadPath
tencent:
  im:
    sdkAppId: ********
    key: 62fcee54ccc9ad643a3717f5881d65cd9012666e874120992fec64ea953c302e
    video:
      thumbUrl: https://pitpat-oss.s3.us-east-2.amazonaws.com/202306/iZ313u6D75g48607.png
paypal:
  mode: live
  checkout:
    id: AZSTRqgShnRx4OG2SJxtoE5Zdzi0VKB7OYA5btrfv77vVWAHvShU2BRx8NV3JWdoAHewVJmAfzCs-H2b
    secret: EI8BqXnPnyb-iCNZZOtCjlz9d5aUlslzuFwzmLJxsmAwq-I1f_c5VJx6qh7Ck6gHVTedcwceC8zdmoSX
    payment:
      cancelUrl: https://api.pitpatfitness.com/app/pay/failPayment
      returnUrl: https://api.pitpatfitness.com/app/pay/successPayment
    WebhookId: 18273040C0013574V
  billing:
    id: AT9x8ROU868seAbtoHErjimjva3lWW6sdD7YwduuGmK70kOj-XAu2breew-7CVTXYCsvzFVpY8TETec_
    secret: EPVjtKrVnE9aoQUSQgrZ48F3TsqCYonOoEj8tr4ocOc6cVPVH3nnzDzLFOpdXNqu9qT-Qt1RDn5LGDlF
    WebhookId: 5LB54013MM2601223

rainmaker:
  url:
    baseUrl: https://p51zc9nzn3.execute-api.us-east-1.amazonaws.com/dev
  admin:
    userName: <EMAIL>
    password: Pw{xMweT6n
aws:
  s3:
    accessKey: ********************
    secretKey: hu4dlrs24L5E/anpW3TH5g2BLeneWiiDwiCO2sfz
    region: us-east-2
    bucket: pitpat-oss
    baseUrl: https://pitpat-oss.pitpatfitness.com/
user:
  password:
    aes: CWzQ6UjGDC#xQ+pC

fegin:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 60000

# 苹果支付凭证验证地址
apple:
  environment: Production
  server:
    init: true
google:
  payment:
    init: true