spring:
  redis:
    database: 0
    host: rzredis.vm4d62.clustercfg.memorydb.us-west-2.amazonaws.com
    port: 6379
    timeout: 10s
    pool:
      max-active: 500
      max-wait: -1
      max-idle: 10
      min-idle: 5
    redisson:
      cacheGroup:
        # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
        - groupId: redissonCacheMap
          # 组过期时间ms
          ttl: 310000
          # 组最大空闲时间ms
          maxIdleTime: 365000
          maxSize: 550
          defaultGroup: true
      config: |
        #cpu数
        threads: 8
        #cpu数*2+1
        nettyThreads: 16
        codec: !<org.redisson.codec.JsonJacksonCodec> {}
        transportMode: "NIO"
        #2-1 单机模式配置
        singleServerConfig:
          address: "redis://rzredis.vm4d62.clustercfg.memorydb.us-west-2.amazonaws.com:6379"
          database: 0
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 6000
          retryAttempts: 3
          retryInterval: 1500
          clientName: ${spring.application.name}
          connectionMinimumIdleSize: 10
          connectionPoolSize: 40
          dnsMonitoringInterval: 5000
          subscriptionsPerConnection: 8
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 25

  rabbitmq:
    host: ************
    port: 5672
    username: guest
    password: guest

  data:
    mongodb:
      host: docdb-2022-01-18-06-15-01.cluster-cocs8caufzfp.us-west-2.docdb.amazonaws.com
      port: 27017
      username: root
      password: qEJMAwIjY45F
      database: pitpat

# SocketIO配置
socket:
  # SocketIO端口
  port: 8991
  # 连接数大小
  workCount: 100
  # 允许客户请求
  allowCustomRequests: true
  # 协议升级超时时间(毫秒)，默认10秒，HTTP握手升级为ws协议超时时间
  upgradeTimeout: 10000
  # Ping消息超时时间(毫秒)，默认60秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
  pingTimeout: 60000
  # Ping消息间隔(毫秒)，默认25秒。客户端向服务器发送一条心跳消息间隔
  pingInterval: 25000
  # 设置HTTP交互最大内容长度
  maxHttpContentLength: 1048576
  # 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
  maxFramePayloadLength: 1048576
redisson:
  # 线程池数量
  threads: 8
  # Netty线程池数量
  nettyThreads: 16
  # 传输模式
  transportMode: "NIO"
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${ruoyi.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 16
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 6000
    # 如果尝试在此限制之内发送成功，则开始启用 timeout 计时。
    retryAttempts: 3
    # 命令重试发送时间间隔，单位：毫秒
    retryInterval: 1500
    # 发布和订阅连接的最小空闲连接数
    subscriptionConnectionMinimumIdleSize: 1
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 单个连接最大订阅数量
    subscriptionsPerConnection: 5
    # DNS监测时间间隔，单位：毫秒
    dnsMonitoringInterval: 5000
# 日志配置
logging:
  level:
    com.linzi.pitpat: debug
    org.springframework: warn
pitpat:
  api:
    h5Url: https://tkjh5.ldxinyong.com/pc/resetpassword
    mailIcon: http://api-market.oss-cn-hangzhou.aliyuncs.com/file/2021/10/*****************.png
    mailHost: smtp.qq.com
    mailAccount: <EMAIL>
    mailPassword: vtbifgonorghcajg
    socketRequestHost: http://*************:7770
tencent:
  im:
    sdkAppId: **********
    key: d244ae86e3a1aed30366a44610fc30978268402c7dd96717f6e16d7165d2bcef
rainmaker:
  url:
    baseUrl: https://p51zc9nzn3.execute-api.us-east-1.amazonaws.com/dev
    login: /v1/custom_login
    nodeMapping: /v1/user/nodes/mapping
paypal:
  mode: sandbox
  client:
    id: AT9x8ROU868seAbtoHErjimjva3lWW6sdD7YwduuGmK70kOj-XAu2breew-7CVTXYCsvzFVpY8TETec_
    secret: EPVjtKrVnE9aoQUSQgrZ48F3TsqCYonOoEj8tr4ocOc6cVPVH3nnzDzLFOpdXNqu9qT-Qt1RDn5LGDlF
  payment:
    cancelUrl: https://tkjapi2.ldxinyong.com/app/pay/failPayment
    returnUrl: https://tkjapi2.ldxinyong.com/app/pay/successPayment
aws:
  s3:
    accessKey: ********************
    secretKey: hu4dlrs24L5E/anpW3TH5g2BLeneWiiDwiCO2sfz
    region: us-east-2
    bucket: pitpat-oss
    baseUrl: https://pitpat-oss.pitpatfitness.com/


zns:
  config:
    rabbitQueue:
      maidian: QUEUE_USER_RUN_DATA_ONLINE
      run:  QUEUE_RUN_ONLINE_1
      runData: QUEUE_RUN_DATA_ONLINE_1
      closeConnection: QUEUE_CLOSE_CONNECTION_ONLINE_1
      delay_exchange_name: delay_exchange_name_ONLINE_1
      delay_queue_name: delay_queue_name_ONLINE_1
      dataEnd: run_data_end_rabbit_queue_ONLINE_1
      saveDetailMinute: save_user_data_detail_minute_ONLINE_1
      pushDelivery: QUEUE_PUSH_DELIVER_ONLINE_1
      delay_send_coupon_queue_name: delay_send_coupon_queue_name_ONLINE_1
      delayed_coupon_exchange_name: delayed_coupon_exchange_name_ONLINE_1
      sql_consumer: sql_consumer_online
      closeRoom: QUEUE_CLOSE_ROOM_online
      joinRoom: queue_join_room_online
