spring:
  redis:
    database: 0
    host: rzredis.vm4d62.clustercfg.memorydb.us-west-2.amazonaws.com
    port: 6379
    pool:
      max-active: 1000
      max-wait: -1
      max-idle: 10
      min-idle: 5

pitpat:
  gateway:
    noDesUriList: >
      /device/treadmill/registerDevice,
      /device/treadmill/register,
      /device/treadmill/getPrintCode,
      /device/data/synchronize,
      /device/treadmill/getEquipmentBrandByBlueMac,
      /device/treadmill/treadmillPrint,
      /app/user/login,
      /app/user/logout,
      /app/im/callback,
      /app/apple/appstore/notification,
      /h5/user/resetPassword,
      /app/pay/webhook,
      /app/pay/billingWebhook,
      /app/user/changeHeadPortrait,
      /app/user/sendVerificationCodeFail,
      /app/user/change/HeadPortrait/noLogin,
      /app/user/uploadCommunityContentImage,
      /app/awsEmail/event,
      /app/treadmill/audio/queryLatest,
      /device/treadmill/start,
      /device/treadmill/startV2,
      /device/treadmill/reportSchedule,
      /device/treadmill/reportScheduleV2,
      /app/treadmill/audio/notifyUpdateStatus,
      /app/facebook/webhook,
      /device/data/report,
      /app/turbolink/awardCallback,
      /app/buttonClicks/create,
      /app/oceanpay/notify,
      /app/runData/risk/result,
      /api/dev/appearance/configList,
      /api/dev/appearance/markAppearance,
      /api/dev/appearance/getAppearance,
      /api/dev/treadmill/getDveInfo,
      /app/act/activity/getCardUserDto
    appVisitorUriList: >
      /app/user/login,
      /app/user/loginNew,
      /app/user/register,
      /app/user/forgetPassword,
      /app/user/getCountryList,
      /app/agreement/list,
      /app/user/onlineStatus,
      /app/apple/appstore/notification,
      /app/system/agreement/list,
      /app/system/version,
      /app/system/addressLib,
      /app/im/onlineStatus,
      /app/user/endpoint/login,
      /app/user/endpoint/consent,
      /app/system/maidian/log,
      /app/task/datail,
      /app/push/readPush,
      /app/system/getRnZipConfig,
      /app/user/third/registerAndLogin,
      /app/user/third/emailAdd,
      /app/user/third/emailAddV2,
      /app/user/third/thirdIdCheck,
      /app/user/game/getProp,
      /app/log/trace/create,
      /app/user/registerV2,
      /app/user/third/registerAndLoginV2,
      /app/oceanpay/notify,
      /app/system/getLoginPageData,
      /app/mall/home/<USER>
      /app/mall/home/<USER>/list,
      /app/mall/home/<USER>/list,
      /app/act/activity/findRankAppRoute,
      /app/nfc/equipment/nfcProductDetail,
      /app/mall/home/<USER>/cart,
      /app/mall/goods/info,
      /app/mall/home/<USER>
      /app/mall/goods/sku/list,
      /app/operational/activity/searchByShowLocation,
      /app/buttonClicks/create,
      /app/oceanpay/notify,
      /app/activity/competitive/seasonActivityList,
      /app/activity/competitive/competitiveSeasonList,
      /app/competitive/activity/record/breaking/recordByActivityId,
      /app/competitive/activity/record/breaking/breakingRatingUserList,
      /app/equipment/install/url,
      /app/pay/getPaymentConfig,
      /app/mall/middlePage/info,
      /app/mall/home/<USER>/policy,
      /app/mall/retainPop/getInfoByPage,
      /app/payment/trade/paypal/result,
      /app/act/activity/getCardUserDto
    appNoSigVerifyUriList: >
      /app/user/login,
      /app/user/loginNew,
      /app/user/rLogout,
      /app/user/getUserInfo,
      /app/user/saveWearsInformation,
      /app/user/getWearsInformation,
      /app/pay/successPayment,
      /app/pay/failPayment,
      /app/pay/callback,
      /app/pay/webhook,
      /app/pay/billingWebhook,
      /app/apple/appstore/notification,
      /app/im/callback,
      /app/runData/getRunData1,
      /app/runData/getRunData2,
      /app/account/test16,
      /app/runActivity/rankingResults,
      /app/user/changeHeadPortrait,
      /app/account/log,
      /app/user/login,
      /app/user/logout,
      /app/system/config,
      /app/system/getResourceConfig,
      /app/account/addAward,
      /app/account/remainAward,
      /app/system/maidian/log,
      /app/user/sendVerificationCode,
      /app/runActivity/getPreventionCheatRule,
      /app/task/datail,
      /app/user/sendVerificationCodeFail,
      /app/push/readPush,
      /app/system/getRnZipConfig,
      /app/getUserGuidePageRouteList,
      /app/user/change/HeadPortrait/noLogin,
      /app/user/getWearsBag,
      /app/user/uploadCommunityContentImage,
      /app/region/allList,
      /app/user/game/getProp,
      /app/config/getByVersion,
      /app/user/game/addScore,
      /app/user/game/remainAward,
      /app/runActivity/rateLimit,
      /app/user/game/getUserInfo,
      /app/game/queryActivityProp,
      /app/game/obtainProp,
      /app/user/game/getDetailIds,
      /app/user/game/getReward,
      /app/user/game/reportRank,
      /app/region/areaList,
      /app/user/game/rateDetail,
      /app/ack/rankedActivity/getUserRankedLevel,
      /app/ack/rankedActivity/getRankedAwardConfig,
      /app/runData/dataConversion,
      /app/ack/rankedActivity/getUserRankedSettle,
      /app/awsEmail/event,
      /app/teamActivity/queryTeamInfoByActivityId,
      /app/treadmill/audio/queryLatest,
      /app/treadmill/audio/notifyUpdateStatus,
      /app/act/activity/seriesActivityRank,
      /app/log/trace/create,
      /app/facebook/webhook,
      /game/activity/user/gradeReport,
      /app/turbolink/awardCallback,
      /app/sysconfig/getByConfigKey,
      /app/user/getOrRegisterTempUser,
      /app/buttonClicks/create,
      /app/oceanpay/notify,
      /app/system/getLoginPageData,
      /app/mall/home/<USER>
      /app/mall/home/<USER>/list,
      /app/mall/home/<USER>/list,
      /app/act/activity/findRankAppRoute,
      /app/nfc/equipment/nfcProductDetail,
      /app/mall/home/<USER>/cart,
      /app/mall/goods/info,
      /app/mall/home/<USER>
      /app/mall/goods/sku/list,
      /app/operational/activity/searchByShowLocation,
      /app/activity/competitive/seasonActivityList,
      /app/competitive/activity/record/breaking/recordByActivityId,
      /app/competitive/activity/record/breaking/breakingRatingUserList,
      /app/activity/competitive/competitiveSeasonList,
      /app/pay/getPaymentConfig,
      /app/mall/home/<USER>/policy
      /app/equipment/install/url,
      /app/runData/risk/result,
      /app/mall/middlePage/info,
      /app/mall/home/<USER>/policy,
      /app/mall/retainPop/getInfoByPage,
      /game/activity/user/gameStatus,
      /app/payment/trade/paypal/result,
      /app/investment/prize/generateToken,
      /app/investment/prize/prizeDraw,
      /app/payment/trade/paypal/result,
      /app/user/findByEmail,
      /app/mall/home/<USER>/policy,
      /app/mallOrder/settleInfo/surrender,
      /app/mall/goods/info/surrender,
      /app/mall/goods/sku/list/surrender,
      /app/mallOrder/calculateOrderTotalAmount/surrender,
      /app/user/verificationCodeCheck,
      /app/mall/goods/surrender/list,
      /app/mallOrder/h5/surrender/OrderList,
      /h5/address/checkZipCode,
      /h5/customh5/personalized/detail/surrender,
      /app/act/activity/getCardUserDto
    h5VisitorUriList: >
      /h5/user/login,
      /h5/home/<USER>
      /h5/user/register,
      /h5/goods/goodsInfo,
      /h5/goods/list,
      /h5/address/addressLib,
      /h5/user/forgetPassword,
      /h5/mind/run/end,
      /h5/visitor/register,
      /h5/system/maidian/log,
      /h5/user/sendVerificationCode,
      /h5/task/datail,
      /h5/activity/team/voteInfo/visitor,
      /h5/file/uploadHtml,
      /app/log/trace/create,
      /h5/user/registerV2,
      /h5/activity/highlights/get,
      /h5/activity/competitive/seasonActivityList,
      /app/activity/competitive/competitiveSeasonList,
      /app/equipment/install/video/categories,
      /h5/act/activity/singleStageRank
      /h5/customh5/personalized/detail
    h5NoSigVerifyUriList: >
      /h5/user/resetPassword,
      /h5/activity/activityDetail,
      /h5/activity/activityUser,
      /h5/system/maidian/log,
      /h5/system/config,
      /h5/task/datail,
      /h5/operational/activity/info,
      /h5/data/dataScreen,
      /h5/teamActivity/queryActivityIdDetail,
      /h5/teamActivity/jumpTeamRank,
      /h5/teamActivity/queryTeamDetail,
      /h5/activity/activityRewardDetail,
      /h5/region/allList,
      /h5/region/areaList,
      /h5/act/activity/signupPreCheck,
      /h5/act/activity/singleMyRace,
      /h5/act/activity/singleHistoryReward,
      /h5/act/activity/singleReportUsers,
      /h5/act/activity/singleRank,
      /h5/act/activity/singleActivityDetail,
      /h5/act/activity/myTeamActRecord,
      /h5/act/activity/myActRecord,
      /h5/act/activity/competitionSchedule,
      /h5/act/activity/enrollActivity,
      /h5/act/activity/award/query,
      /h5/act/activity/seriesActivityRank,
      /h5/act/activity/singleActivityDetail/reviewStatus,
      /h5/act/activity/singleActivityDetail/singleStageRank,
      /h5/act/activity/singleActivityDetail/seriesStageActivityRank,
      /app/log/trace/create,
      /h5/activity/highlights/get,
      /h5/activity/competitive/seasonActivityList,
      /app/activity/competitive/competitiveSeasonList,
      /app/equipment/install/video/categories,
      /h5/act/activity/singleStageRank,
      /h5/customh5/personalized/detail,
      /app/act/activity/findRankAppRoute,
      /app/nfc/equipment/nfcProductDetail,
      /h5/act/activity/team/single/personal/rank,
      /app/act/activity/team/single/personal/rank,
      /app/investment/prize/generateToken,
      /app/investment/prize/prizeDraw,
      /app/payment/trade/paypal/result,
      /app/user/findByEmail,
      /app/mall/home/<USER>/policy,
      /app/mallOrder/settleInfo/surrender,
      /app/mall/goods/info/surrender,
      /app/mall/goods/sku/list/surrender,
      /app/mallOrder/calculateOrderTotalAmount/surrender,
      /app/user/verificationCodeCheck,
      /app/mall/goods/surrender/list,
      /app/mallOrder/h5/surrender/OrderList,
      /app/region/areaList,
      /h5/address/checkZipCode,
      /h5/customh5/personalized/detail/surrender,
      /app/activity/free/topList/local
aws:
  s3:
    accessKey: ********************
    secretKey: hu4dlrs24L5E/anpW3TH5g2BLeneWiiDwiCO2sfz
    region: us-east-2
    bucket: pitpat-oss
    baseUrl: https://pitpat-oss.pitpatfitness.com/
