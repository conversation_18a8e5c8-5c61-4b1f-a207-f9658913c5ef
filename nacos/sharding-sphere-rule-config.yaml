
rules:
  - !SINGLE
    tables:
      - "*.*"
    defaultDataSource: ds_0
  - !SHARDING
    tables:
      zns_run_activity_user:
        actualDataNodes: ds_0.zns_run_activity_user_sharding_${0..15}
        tableStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: zns_run_activity_user_inline
      zns_trace_log:
        actualDataNodes: ds_0.zns_trace_log_${2025..2029}${['01','02','03','04','05','06','07','08','09','10','11','12']}
        tableStrategy:
          standard:
            shardingColumn: gmt_create
            shardingAlgorithmName: zns_trace_log_inline
      zns_delay_queue_message:
        actualDataNodes: ds_0.zns_delay_queue_message_2025${(7..12).collect{t -> String.format('%02d', t)}}${(1..31).collect{t -> String.format('%02d', t)}},ds_0.zns_delay_queue_message_$->{2026..2029}${(1..12).collect{t -> String.format('%02d', t)}}${(1..31).collect{t -> String.format('%02d', t)}}
        tableStrategy:
          standard:
            shardingColumn: scheduled_time
            shardingAlgorithmName: delay_queue_message_inline

    shardingAlgorithms:
      zns_run_activity_user_inline:
        type: INLINE
        props:
          algorithm-expression: zns_run_activity_user_sharding_${user_id % 16}
      zns_trace_log_inline:
        type: INTERVAL
        props:
          datetime-pattern: yyyy-MM-dd HH:mm:ss
          datetime-interval-unit: MONTHS # 时间间隔单位，这里设置为月
          datetime-interval-amount: 1 # 时间间隔数量，这里设置为每个月
          sharding-suffix-pattern: yyyyMM
          datetime-lower: '2025-01-01 00:00:00' # 开始时间
          datetime-upper: '2099-01-01 00:00:00' # 开始时间
      delay_queue_message_inline:
        type: CLASS_BASED
        props:
          strategy: STANDARD
          algorithmClassName: com.linzi.pitpat.framework.db.algorithm.CachedIntervalShardingAlgorithm
          datetime-pattern: yyyy-MM-dd HH:mm:ss
          datetime-interval-unit: DAYS
          datetime-interval-amount: 1
          sharding-suffix-pattern: yyyyMMdd
          datetime-lower: '2025-07-01 00:00:00'
          datetime-upper: '2029-01-01 00:00:00'
props:
  sql-show: false


