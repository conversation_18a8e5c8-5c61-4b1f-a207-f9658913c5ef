spring:
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: -1
  ai:
    openai:
      base-url: https://api.openai.com/v1/chat/completions
      api-key: ********************************************************************************************************************************************************************
      options:
        model: gpt-4o-mini
        temperature: 1.5
        role-user: Write a compelling introduction for a club name '%s'. Highlight its energy, inclusivity, and dedication to connection, self-improvement, and friendly competition. Make it inspiring and under 280 characters. Do not include the club's name.
        role-system: You are a creative writer skilled in crafting engaging and unique introductions for clubs.
  datasource:
    hikari:
      maximum-pool-size: 150
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: **************************************************************************************************************************************************************************************************************************************
          username: lz_pitpat
          password: qEJMAwIjY45F
          driver-class-name: com.mysql.cj.jdbc.Driver
        slave:
          url: *******************************************************************************************************************************************************************************************************************************************
          username: pitpat_readonly
          password: Yv9@y2Pbt5s&eoZn
          driver-class-name: com.mysql.cj.jdbc.Driver
        sharding:
          url: jdbc:shardingsphere:nacos:sharding-sphere-config.yaml?group=DEFAULT_GROUP&ruleConfig=sharding-sphere-rule-config.yaml
          driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
        sharding-read:
          url: jdbc:shardingsphere:nacos:sharding-sphere-read-config.yaml?group=DEFAULT_GROUP&ruleConfig=sharding-sphere-rule-config.yaml
          driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
      hikari:
        maximum-pool-size: 200

  redis:
    database: 0
    host: rzredis.vm4d62.clustercfg.memorydb.us-west-2.amazonaws.com
    port: 6379
    timeout: 10s
    pool:
      max-active: 1000
      max-wait: -1
      max-idle: 10
      min-idle: 5
    redisson:
      cacheGroup:
        # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
        - groupId: 1minCacheMap
          ttl: 60000   # 组过期时间ms
          maxIdleTime: 60000 # 组最大空闲时间ms,过期后直接删除
          maxSize: 1000
        - groupId: 5minCacheMap
          ttl: 300000
          maxIdleTime: 300000
          maxSize: 1000
          defaultGroup: true
        - groupId: 30minCacheMap
          ttl: 1800000
          maxIdleTime: 360000
          maxSize: 1000
        - groupId: 60minCacheMap
          ttl: 3600000
          maxIdleTime: 360000
          maxSize: 1000
      config: |
        #cpu数
        threads: 8
        #cpu数*2+1
        nettyThreads: 16
        codec: !<com.linzi.pitpat.framework.redis.codec.JsonJacksonCodec> {}
        transportMode: "NIO"
        #2-1 单机模式配置
        singleServerConfig:
          address: "redis://rzredis.vm4d62.clustercfg.memorydb.us-west-2.amazonaws.com:6379"
          database: 0
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 6000
          retryAttempts: 3
          retryInterval: 1500
          clientName: ${spring.application.name}
          connectionMinimumIdleSize: 10
          connectionPoolSize: 40
          dnsMonitoringInterval: 5000
          subscriptionsPerConnection: 8
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 25

  rabbitmq:
    host: rabbitmq-0d1d4a10011a3a86.elb.us-west-2.amazonaws.com
    port: 5672
    username: guest
    password: guest
    custom:
      exchanges:
        - name: content.delay.exchange
          type: direct
          delayed: true
        - name: transfer.direct.exchange #死信交换机
          type: direct
      queues:
        - name: 1min.queue.dlx
          ttl: 60000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 2min.queue.dlx
          ttl: 120000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 3min.queue.dlx
          ttl: 180000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 4min.queue.dlx
          ttl: 240000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 5min.queue.dlx
          ttl: 300000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 10min.queue.dlx
          ttl: 600000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: content.publish.queue
        - name: transfer.queue
      bindings:
        - queue: transfer.queue
          exchange: transfer.direct.exchange
          routingKey: transfer
        - queue: 1min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.1min
        - queue: 2min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.2min
        - queue: 3min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.3min
        - queue: 4min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.4min
        - queue: 5min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.5min
        - queue: 10min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.10min
        - queue: content.publish.queue
          exchange: content.delay.exchange
          routingKey: publish
      enable-environment-prefix: true

  data:
    mongodb:
      host: docdb-2022-01-18-06-15-01.cluster-cocs8caufzfp.us-west-2.docdb.amazonaws.com
      port: 27017
      username: root
      password: qEJMAwIjY45F
      database: pitpat

game:
  server:
    domain: ************

#paypal:
#  pitpat:
#    id: AeC11x_3F7K1CVaesHkhGg-P7bVhb3Rl1DW2K9LIvWGCN4nfzjsb08R1RApTrsK5GzDFM6mH_LRX5DLT
#    secret: ECHH4xJJVp5QKrxTxuJL_QMiBgemBScRUR4KgcbPxmE8cZV3PRriagJ276Td5_GZIMlMsZ1rk2fQaVK1
#    webhookId: 4SM11976JL569205B
# 这是测试的，线上的还不知道
paypal:
  mode: live
  checkout:
    id: AZSTRqgShnRx4OG2SJxtoE5Zdzi0VKB7OYA5btrfv77vVWAHvShU2BRx8NV3JWdoAHewVJmAfzCs-H2b
    secret: EI8BqXnPnyb-iCNZZOtCjlz9d5aUlslzuFwzmLJxsmAwq-I1f_c5VJx6qh7Ck6gHVTedcwceC8zdmoSX
    payment:
      cancelUrl: https://ypitpat.pitpatfitness.com/app/pay/failPayment
      returnUrl: https://ypitpat.pitpatfitness.com/app/pay/successPayment
    WebhookId: 18273040C0013574V
  billing:
    id: AbzlZvYYAwiCF3b75-e9K0sbxQBON0q3kitWaxx1kAsXj_sZueqJEYQCNglbGkFAZferGuvLLCBYZGZB
    secret: EDN-pyeo1Dd-r0wp9N2rKHck6XFU8eGGDa5C8SbKDAv4vZFU-WpegkvnzWfTVkAYprrEnN_qv1ZdaHSF
    WebhookId: 5LB54013MM2601223
  pitpat:
    id: AbzlZvYYAwiCF3b75-e9K0sbxQBON0q3kitWaxx1kAsXj_sZueqJEYQCNglbGkFAZferGuvLLCBYZGZB
    secret: EDN-pyeo1Dd-r0wp9N2rKHck6XFU8eGGDa5C8SbKDAv4vZFU-WpegkvnzWfTVkAYprrEnN_qv1ZdaHSF
    webhookId: 5LB54013MM2601223
    targetUrl: https://yh5.pitpatfitness.com/#/member/paypalResult
    url: https://ypitpat.pitpatfitness.com/app/payment/trade/paypal/result?tradeNo=
  # 商城的
  mall:
    id: AZSTRqgShnRx4OG2SJxtoE5Zdzi0VKB7OYA5btrfv77vVWAHvShU2BRx8NV3JWdoAHewVJmAfzCs-H2b
    secret: EI8BqXnPnyb-iCNZZOtCjlz9d5aUlslzuFwzmLJxsmAwq-I1f_c5VJx6qh7Ck6gHVTedcwceC8zdmoSX
    webhookId: 55B7688167643012X

admin:
  server:
    name: http://api.pitpatfitness.com
    push: http://*************:7773
    gamepush: http://*************:9002

zns:
  config:
    vipMember:
      discountExpireIn: 1
    rabbitQueue:
      maidian: QUEUE_USER_RUN_DATA_ONLINE
      run: QUEUE_RUN_ONLINE_1
      runData: QUEUE_RUN_DATA_ONLINE_1
      closeConnection: QUEUE_CLOSE_CONNECTION_ONLINE_1
      delay_exchange_name: delay_exchange_name_ONLINE_1
      delay_queue_name: delay_queue_name_ONLINE_1
      dataEnd: run_data_end_rabbit_queue_ONLINE_1
      saveDetailMinute: save_user_data_detail_minute_ONLINE_1
      pushDelivery: QUEUE_PUSH_DELIVER_ONLINE_1
      delay_send_coupon_queue_name: delay_send_coupon_queue_name_ONLINE_1
      delayed_coupon_exchange_name: delayed_coupon_exchange_name_ONLINE_1
      sql_consumer: sql_consumer_online
      new_user_send_coupon_queue: new_user_send_coupon_queue_online
      old_user_send_coupon_queue: old_user_send_coupon_queue_online
      delayed_content_publish_exchange_name: delayed_content_publish_exchange_name_online
      delay_content_publish_queue_name: delay_content_publish_queue_name_online
      delay_robot_follow_queue_name: delay_robot_follow_queue_name_online
      delay_robot_report_queue_name: delay_robot_report_queue_name_online
      delay_push_new_user_activity_message_queue_name: delay_push_new_user_activity_message_queue_name_online
      delay_push_new_user_activity_message_exchange_name: delay_push_new_user_activity_message_exchange_name_online
      userPropBagQueue: user_propBag_queue_online
      emailSend: email_send_queue_online
      treadmill_upgrade_delay_exchange: treadmill_upgrade_delay_exchange
      treadmill_upgrade_delay_queue: treadmill_upgrade_delay_queue
      rank_match_put_in_robot_delay_exchange: rank_match_put_in_robot_delay_exchange_online
      rank_match_put_in_robot_delay_queue: rank_match_put_in_robot_delay_queue_online
      user_ranked_level_init_exchange: user_ranked_level_init_exchange_online
      user_ranked_level_init_queue: user_ranked_level_init_queue_online
      delay_auto_enroll_queue_name: delay_auto_enroll_queue_name_online
      ranked_award_settle_delay_queue: ranked_award_settle_delay_queue_online
      ranked_award_settle_delay_exchange: ranked_award_settle_delay_exchange_online
      new_user_send_coupon_delay_exchange: new_user_send_coupon_delay_exchange_online
      new_user_send_coupon_delay_queue: new_user_send_coupon_delay_queue_online
      delay_email_reach_queue_name: delay_email_reach_queue_name_online
      delay_club_notice_queue_name: delay_club_notice_queue_name_online
      run_data_exchange: run_data_exchange_online
      run_data_queue: run_data_queue_online
      delay_trace_log_queue_name: trace_log_queue_name_online
      delay_push_im_queue_name: delay_push_im_queue_name_online
      user_account_upgrade_queue: user_account_upgrade_queue_online
      user_account_upgrade_exchange: user_account_upgrade_exchange_online
      activity_area_upgrade_queue: activity_area_upgrade_queue_online
      activity_area_upgrade_exchange: activity_area_upgrade_exchange_online
      prop_rank_match_put_in_robot_delay_exchange: prop_rank_match_put_in_robot_delay_exchange_online
      prop_rank_match_put_in_robot_delay_queue: prop_rank_match_put_in_robot_delay_queue_online
      prop_user_ranked_level_init_exchange: prop_user_ranked_level_init_exchange_online
      prop_user_ranked_level_init_queue: prop_user_ranked_level_init_queue_online
      prop_ranked_award_settle_delay_exchange: prop_ranked_award_settle_delay_exchange_online
      prop_ranked_award_settle_delay_queue: prop_ranked_award_settle_delay_queue_online
      closeRoom: QUEUE_CLOSE_ROOM_online
      joinRoom: queue_join_room_online
      sync_sharding_queue: sync_sharding_queue_online
      sync_sharding_exchange: sync_sharding_exchange_online
      auto_mark_single_user_exchange: auto_mark_single_user_exchange_online
      auto_mark_single_user_queue: auto_mark_single_user_queue_online
      auto_mark_single_user_key: auto_mark_single_user_key_online
      manual_mark_exchange: manual_mark_exchange_online
      manual_mark_queue: manual_mark_queue_online
      auto_mark_exchange: auto_mark_exchange_online
      auto_mark_queue: auto_mark_queue_online
      plan_im_exchange: plan_im_exchange_online
      plan_im_queue: plan_im_queue_online
      email_lower_case_exchange: email_lower_case_exchange_online
      email_lower_case_queue: email_lower_case_queue_online
      send_message_exchange: send_message_exchange_online
      send_message_queue: send_message_queue_online
      plan_push_exchange: plan_push_exchange_online
      plan_push_queue: plan_push_queue_online
      plan_score_exchange: plan_score_exchange_online
      plan_score_queue: plan_score_queue_online
      plan_email_exchange: plan_email_exchange_online
      plan_email_queue: plan_email_queue_online
      plan_coupon_exchange: plan_coupon_exchange_online
      plan_coupon_queue: plan_coupon_queue_online
      currency_init_exchange: currency_init_exchange_online
      currency_init_queue: currency_init_queue_online
      send_award_exchange: send_award_exchange_online
      send_award_queue: send_award_queue_online
      sync_sharding_default_routing_key: sync_sharding_default_routing_key_online
      message_task_prepare: message_task_prepare_online
      message_task_slow_prepare: message_task_slow_prepare_online
      msg_center_exchange: msg_center_exchange_online
      msg_center_send_queue: msg_center_send_queue_online
      msg_center_result_queue: msg_center_result_queue_online
      msg_center_skip_queue: msg_center_skip_queue_online
      data_sync_queue: data_sync_queue_online
      cheat_data_sync_queue: cheat_data_sync_queue_online
      delay_turbolink_queue_name: delay_turbolink_queue_name_online
      mall_order_close_delay_exchange: mall_order_close_delay_exchange_online
      mall_order_close_delay_queue: mall_order_close_delay_queue_online
      mall_order_close_delay_key: mall_order_close_delay_key_online
      common_init_exchange: common_init_exchange_online
      equipment_exchange: equipment_exchange_online
      equipment_care_queue: equipment_care_queue_online
      equipment_care_key: equipment_care_key_online
      rankMatch.delay.exchange: rankMatch.delay.exchange.v2.online
      rankMatch.deliveryRobot.delay.queue: rankMatch.deliveryRobot.delay.queue.v2.online
      propRankMatch.delay.exchange: propRankMatch.delay.exchange.v2.online
      propRankMatch.deliveryRobot.delay.queue: propRankMatch.deliveryRobot.delay.queue.v2.online
      traceLog.exchange: traceLog.exchange.v2.online
      traceLog.transform.queue: traceLog.transform.queue.v2.online
      room_propmode_endactivity_delay_exchange: room_propmode_endactivity_delay_exchange_online
      room_propmode_endactivity_delay_queue: room_propmode_endactivity_delay_queue_online
      activity.monitor.exchange: activity.monitor.exchange.online
      activity.monitor.ram.queue: activity.monitor.ram.queue.online

      runData.delay.exchange: runData.delay.exchange.online
      risk.timeout.queue: risk.timeout.queue.online
      risk.timeout.delay.key: risk.timeout.delay.key.online
      mall.order.push.delay.exchange: mall.order.push.delay.exchange.online
      mall.order.push.delay.key: mall.order.push.delay.key.online
      mall.order.push.delay.queue: mall.order.push.delay.queue.online
      user.equipment.statistics.key: user.equipment.statistics.key.online
      user.equipment.statistics.queue: user.equipment.statistics.queue.online
      user.task.event.queue: user.task.event.queue.online
      user.task.event.key: user.task.event.key.online
# TODO: 线上配置要改
Turbolink:
  config:
    ProjectId: csq7460gpf6n20ekl170
    appKey: c3203329ced78a2c9bf3408aee511bc4
    appSecret: 63fd874fcebef7a2a1348c7ca25a98c776b3edb1
    openSecret: 7f304559967c60ddbfc8bf8d9e102f8ed27aa797
    notifyToken: eb98cea257bd753a1842bc1255eb19d7e34670ba

community:
  reportCustomer: 107866
user:
  d3StyleImage: https://pitpat-oss.s3.us-east-2.amazonaws.com/communityContentImage/iW2S3VQLRweIlK53.png
aliyun:
  imageseg:
    appId: LTAI5tQzAopcBBXM7avef6wq
    appSecret: ******************************

#待产品提供
activity:
  free:
    playId: 1
