# 日志配置
logging:
  level:
    com.linzi.pitpat: info
    org.springframework: warn
pitpat:
  api:
    h5Url: https://api.pitpatfitness.com/pc/resetpassword
    mailIcon: http://api-market.oss-cn-hangzhou.aliyuncs.com/file/2021/10/20211028165917073.png
    url: https://api.pitpatfitness.com
    mallH5Url: https://h5.pitpatfitness.com/#
    surrenderH5Url: http://pitpatshop.com
    profile: ./pitpat/uploadPath
    singleMallH5Url: https://www.superuntreadmill.com
    h5Host: https://yh5.pitpatfitness.com/#
  device:
    url: https://kjreg.yijiesudai.com
tencent:
  im:
    sdkAppId: 20000490
    key: 62fcee54ccc9ad643a3717f5881d65cd9012666e874120992fec64ea953c302e
    video:
      thumbUrl: https://pitpat-oss.s3.us-east-2.amazonaws.com/202306/iZ313u6D75g48607.png

paypal:
  mode: live
  checkout:
    id: AZSTRqgShnRx4OG2SJxtoE5Zdzi0VKB7OYA5btrfv77vVWAHvShU2BRx8NV3JWdoAHewVJmAfzCs-H2b
    secret: EI8BqXnPnyb-iCNZZOtCjlz9d5aUlslzuFwzmLJxsmAwq-I1f_c5VJx6qh7Ck6gHVTedcwceC8zdmoSX
    payment:
      cancelUrl: https://api.pitpatfitness.com/app/pay/failPayment
      returnUrl: https://api.pitpatfitness.com/app/pay/successPayment
    WebhookId: 18273040C0013574V
  billing:
    id: AT9x8ROU868seAbtoHErjimjva3lWW6sdD7YwduuGmK70kOj-XAu2breew-7CVTXYCsvzFVpY8TETec_
    secret: EPVjtKrVnE9aoQUSQgrZ48F3TsqCYonOoEj8tr4ocOc6cVPVH3nnzDzLFOpdXNqu9qT-Qt1RDn5LGDlF
    WebhookId: 5LB54013MM2601223

#钱海支付配置
# 下面配置都是测试账户，上线需要替换为正式账户
oceanpay:
  defaultChannel:
    creditCard:
      account: 241291
      terminal: ********
      secureCode: hH08t266L20l00n88bX4f0jjRb0n60h0fv06l0h6x20DB462N224pv40pZrlh40b
    klarna:
      account: 241291
      terminal: ********
      secureCode: rjNb80Hl62b444h04804H8R6dvhbRh00n4JZzz68d8f4b62hd2DV886dd66lp8nf
    afterPay:
      account: 241291
      terminal: ********
      secureCode: pdz4rL04l0648B06xF8Tzvzt486X4ftz488260dN2008pJf460b8XV0ZdR0NpL6j
    googlePay:
      account: 241291
      terminal: ********
      secureCode: z6rd20bz8286n8bl46j6j8RflDb0lh2d44ZfH8f228vJjjXLfp0xj62vZ46662D8
    applePay:
      account: 241291
      terminal: ********
      secureCode: 8T68nB24dFpz6t04d02h4PrB08x468h4ldj8x40P440F2PZp62260jL220840b4t
  h5Channel:
    creditCard:
      account: 241291
      terminal: ********
      secureCode: z8zV2480p646r46jlt4pt44820z840fvr60D4vjlrz040nv028f08P46r6xP8448
    klarna:
      account: 241291
      terminal: ********
      secureCode: 222866DL0Pd8b6rz464dD040jN88l0zj00ld6066Z2l6lnVl64246dh0l8Z6d426
    afterPay:
      account: 241291
      terminal: ********
      secureCode: 86h8b0z88X406dz6xTf02r22Tb4F2244vX8Ftj6Drz4b800880L8jNh0j2b6nfBf
    googlePay:
      account: 241291
      terminal: ********
      secureCode: Db806fx80Vlrd4vv642rLdzf2vT4V88L284V6Brb6F8rp62VnD80zn0d2nr6x226
    applePay:
      account: 241291
      terminal: ********
      secureCode: 2T0V2f4dR80662fFf242ptf6d426j2b4l06406x88F84hRD8Xb8r8ft0d04b4d28


rainmaker:
  url:
    baseUrl: https://p51zc9nzn3.execute-api.us-east-1.amazonaws.com/dev
  admin:
    userName: <EMAIL>
    password: Pw{xMweT6n
aws:
  s3:
    accessKey: ********************
    secretKey: hu4dlrs24L5E/anpW3TH5g2BLeneWiiDwiCO2sfz
    region: us-east-2
    bucket: pitpat-oss
    baseUrl: https://pitpat-oss.pitpatfitness.com/
user:
  password:
    aes: CWzQ6UjGDC#xQ+pC
  newLevel:
    date: 2024-07-31 00:00:00

fegin:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 60000

communityContent:
  defaultLinkPic: https://pitpat-oss.s3.us-east-2.amazonaws.com/202311/ior13CMNfFHA2255.png

im:
  customerService:
    idStr: 100428
  plusService:
    idStr: 343530

# 苹果支付凭证验证地址
apple:
  environment: Production
  server:
    init: true
google:
  payment:
    init: true

