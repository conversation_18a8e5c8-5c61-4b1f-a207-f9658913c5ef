aws:
  s3:
    accessKey: ********************
    secretKey: hu4dlrs24L5E/anpW3TH5g2BLeneWiiDwiCO2sfz
    region: us-east-2
    bucket: pitpat-oss
    baseUrl: https://pitpat-oss.pitpatfitness.com/



  #ribbon配置
ribbon:
  #请求处理的超时时间
  ReadTimeout: 10000
  #请求连接的超时时间
  ConnectTimeout: 10000
user:
  password:
    aes: CWzQ6UjGDC#xQ+pC
tencent:
  im:
    sdkAppId: 20000490
    key: 62fcee54ccc9ad643a3717f5881d65cd9012666e874120992fec64ea953c302e
    video:
      thumbUrl: https://pitpat-oss.s3.us-east-2.amazonaws.com/202306/iZ313u6D75g48607.png


paypal:
  mode: live
  client:
    id: AT9x8ROU868seAbtoHErjimjva3lWW6sdD7YwduuGmK70kOj-XAu2breew-7CVTXYCsvzFVpY8TETec_
    secret: EPVjtKrVnE9aoQUSQgrZ48F3TsqCYonOoEj8tr4ocOc6cVPVH3nnzDzLFOpdXNqu9qT-Qt1RDn5LGDlF
  WebhookId: 55B7688167643012X
  payment:
    cancelUrl: https://api.pitpatfitness.com/app/pay/failPayment
    returnUrl: https://api.pitpatfitness.com/app/pay/successPayment


rainmaker:
  url:
    baseUrl: https://p51zc9nzn3.execute-api.us-east-1.amazonaws.com/dev
  admin:
    userName: <EMAIL>
    password: Pw{xMweT6n

pitpat:
  api:
    h5Url: https://tkjapi2.ldxinyong.com/pc/resetpassword
    mailIcon: http://api-market.oss-cn-hangzhou.aliyuncs.com/file/2021/10/*****************.png
    mailHost: smtp.exmail.qq.com
    mailAccount: <EMAIL>
    mailPassword: gPuhxH6QLvgFKQev
    url: https://tkjapi2.ldxinyong.com
    mallH5Url: https://h5.pitpatfitness.com/#
    profile: ./pitpat/uploadPath
# 苹果支付凭证验证地址
apple:
  payment:
    certificateUrl: https://buy.itunes.apple.com/verifyReceipt