{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "Bash(find ~ -name \"*claude*\" -type f)", "Bash(find /Users/<USER>/Projects/profressional/linzikg/pitpat-server-dev -name \"*.jar\")", "Bash(find /Users/<USER>/Projects/profressional/linzikg/pitpat-server-dev -name \"*.jar\" -exec grep -l \"DingTalkAppender\" {} ;)", "Bash(mvn dependency:tree -pl pitpat-version-pro)", "Bash(mvn dependency:tree -Dverbose -pl pitpat-version-pro)", "Bash(find /Users/<USER>/Projects/profressional/linzikg/pitpat-server-dev -name \"*.jar\" -exec jar tf {})", "Bash(jar tf /Users/<USER>/Projects/profressional/linzikg/pitpat-server-dev/pitpat-api/target/pitpat-api.jar)", "Bash(jar tf /Users/<USER>/Projects/profressional/linzikg/pitpat-server-dev/pitpat-version-pro/target/pitpat-version-pro.jar)", "Bash(find ~/.m2/repository -name \"*web-spring-boot-starter*\" -name \"*.jar\")", "Bash(jar tf /Users/<USER>/.m2/repository/com/linzi/pitpat/web-spring-boot-starter/0.1.3-SNAPSHOT/web-spring-boot-starter-0.1.3-SNAPSHOT.jar)", "Bash(find ~/.m2/repository -name \"*web-spring-boot-starter*0.1.11-SNAPSHOT*\" -name \"*.jar\")", "Bash(jar tf /Users/<USER>/.m2/repository/com/linzi/pitpat/web-spring-boot-starter/0.1.11-SNAPSHOT/web-spring-boot-starter-0.1.11-SNAPSHOT.jar)"], "deny": []}}