package com.linzi.pitpat.data.quartz;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.robotservice.quartz.UserRobotFollowTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.ZonedDateTime;
import java.util.TimeZone;

/**
 * 机器人任务
 */
@Slf4j
@SpringBootTest
public class UserRobotFollowTaskTest  extends PitpatAdminApplicationTests {

    @Test
    public void dateTest(){
        ZonedDateTime now = DateUtil.formateDate("20241210112323", "yyyyMMddHHmmss");
        ZonedDateTime usDate = DateUtil.getDate2ByTimeZone(now, TimeZone.getTimeZone("GMT-8"));
        ZonedDateTime startDate = DateUtil.getStartOfDate(usDate);
        log.info("now:{},usDate:{},startDate{}",now,usDate,startDate);
    }
    @Autowired
    private UserRobotFollowTask userRobotFollowTask;
    @Test
    public void testTaskRun(){
        userRobotFollowTask.ceoIncreaseFans();
    }
}
