package com.linzi.pitpat.data.activityservice.manager;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.data.activityservice.dto.SeasonBonusPoolResetEventDto;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SeasonBonusPoolManagerTest extends PitpatAdminApplicationTests {
    @Resource
    private QueueMessageService queueMessageService;

    @Test
    void resetBonus() {
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.SeasonBonusPoolResetEventDto.getEventType(), new SeasonBonusPoolResetEventDto(6L, 6L));
    }
}
