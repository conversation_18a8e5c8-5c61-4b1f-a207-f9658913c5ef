package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;

public class ZnsUserServiceTest extends PitpatAdminApplicationTests{

    @Resource
    private ZnsUserService znsUserService;

    @Test
    public void findByQueryTest(){
        UserQuery query = UserQuery.builder().isTest(1)
                .startUserId(25L).userIds(List.of(456L)).createTimeEnd(ZonedDateTime.now())
                .endUserId(45L).isRobot(1).userId(45L).lastStr("limit 1").country("US")
                .createTimeStart(ZonedDateTime.now()).emailAddress("<EMAIL>").emailAddressEn("<EMAIL>")
                .geAppVersion(3083).gender(1).isPrivacy(1).stateCodes(List.of("US_UFO")).build();
        znsUserService.findByQuery(query);
    }
}
