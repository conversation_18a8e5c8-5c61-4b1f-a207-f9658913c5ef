package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.data.activityservice.mapper.ActivityRecommendDisseminateMapper;
import com.linzi.pitpat.data.activityservice.mapper.RoomMsgMapper;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryI18nService;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryService;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.PlaylistMusicRelService;
import com.linzi.pitpat.data.activityservice.service.RunMusicService;
import com.linzi.pitpat.data.activityservice.service.RunPlaylistService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.courseservice.service.UserAiCourseService;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.equipmentservice.mapper.UserEquipmentShareMapper;
import com.linzi.pitpat.data.equipmentservice.service.DeviceVersionService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioSoundpackRelService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackHistoryService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackService;
import com.linzi.pitpat.data.mallservice.mapper.ZnsGoodsDao;
import com.linzi.pitpat.data.mallservice.service.GoodsSkuI18nService;
import com.linzi.pitpat.data.mallservice.service.HomeModuleGoodService;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleI8nService;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleService;
import com.linzi.pitpat.data.mallservice.service.MallRetainPopGoodsRelService;
import com.linzi.pitpat.data.mallservice.service.RefundRemarkI18nService;
import com.linzi.pitpat.data.mallservice.service.ZnsBrandService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsCategoryService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsFileService;
import com.linzi.pitpat.data.mallservice.service.ZnsPropertyService;
import com.linzi.pitpat.data.messageservice.service.MessageTaskUserService;
import com.linzi.pitpat.data.service.operational.OperationalActivityUserService;
import com.linzi.pitpat.data.systemservice.mapper.AppRnUpgradeDao;
import com.linzi.pitpat.data.systemservice.mapper.OperationalPlanDao;
import com.linzi.pitpat.data.systemservice.mapper.PopUserDao;
import com.linzi.pitpat.data.systemservice.service.CustomerAnswerDetailService;
import com.linzi.pitpat.data.systemservice.service.PagePopupRelationService;
import com.linzi.pitpat.data.systemservice.service.PopUserService;
import com.linzi.pitpat.data.trainingplanservice.service.TrainingPlanI18nService;
import com.linzi.pitpat.data.trainingplanservice.service.TrainingPlanService;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.service.TaskAwardService;
import com.linzi.pitpat.data.userservice.service.TaskDetailService;
import com.linzi.pitpat.data.userservice.service.TaskPackageService;
import com.linzi.pitpat.data.userservice.service.UserThirdOauthService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/4/24
 */
public class DeleteServiceTest extends PitpatAdminApplicationTests {

    @Autowired
    private OperationalActivityUserService operationalActivityUserService;
    @Autowired
    private PopUserDao popUserDao;
    @Autowired
    private PagePopupRelationService pagePopupRelationService;
    @Autowired
    private MallHomeModuleService mallHomeModuleService;
    @Autowired
    private MallHomeModuleI8nService mallHomeModuleI8nService;
    @Autowired
    private HomeModuleGoodService homeModuleGoodService;
    @Autowired
    private ZnsUserService userService;
    @Autowired
    private UserThirdOauthService userThirdOauthService;
    @Autowired
    private UserEquipmentShareMapper userEquipmentShareMapper;
    @Autowired
    private RoomMsgMapper roomMsgMapper;
    @Autowired
    private PopUserService popUserService;
    @Autowired
    private TrainingPlanI18nService trainingPlanI18nService;
    @Autowired
    private TrainingPlanService trainingPlanService;
    @Autowired
    private ZnsGoodsFileService goodsFileService;
    @Autowired
    private GoodsSkuI18nService goodsSkuI18nService;
    @Autowired
    private AppRnUpgradeDao appRnUpgradeDao;
    @Autowired
    private OperationalPlanDao operationalPlanDao;
    @Autowired
    private ActivityRecommendDisseminateMapper activityRecommendDisseminateMapper;
    @Autowired
    private ActivityCategoryService activityCategoryService;
    @Autowired
    private RunMusicService runMusicService;
    @Autowired
    private RunPlaylistService runPlaylistService;
    @Autowired
    private ActivityPlaylistRelService activityPlaylistRelService;
    @Autowired
    private ExchangeScoreRuleService exchangeScoreRuleService;
    @Autowired
    private ZnsCourseService courseService;
    @Autowired
    private DeviceVersionService deviceVersionService;
    @Autowired
    private TreadmillSoundpackService treadmillSoundpackService;

    @Autowired
    private TreadmillAudioService treadmillAudioService;

    @Autowired
    private TreadmillAudioSoundpackRelService treadmillAudioSoundpackRelService;

    @Autowired
    private TreadmillSoundpackHistoryService treadmillSoundpackHistoryService;
    // @Autowired
    // private MsgNonDisturbUserMapper mapper;
    @Autowired
    private CustomerAnswerDetailService customerAnswerDetailService;
    @Autowired
    private TaskPackageService taskPackageService;
    @Autowired
    private ZnsUserDao userDao;
    @Autowired
    private ZnsUserAccountDetailService userAccountDetailService;
    @Autowired
    private ZnsRunActivityService runActivityService;
    @Autowired
    private ZnsPropertyService propertyService;
    @Autowired
    private ZnsGoodsDao goodsDao;
    @Autowired
    private ZnsGoodsCategoryService goodsCategoryService;
    @Autowired
    private ZnsBrandService brandService;
    @Autowired
    private TaskDetailService taskDetailService;
    @Autowired
    private TaskAwardService taskAwardService;
    @Autowired
    private RefundRemarkI18nService refundRemarkI18nService;
    @Autowired
    private PlaylistMusicRelService playlistMusicRelService;
    @Autowired
    private MessageTaskUserService messageTaskUserService;
    @Autowired
    private MallRetainPopGoodsRelService mallRetainPopGoodsRelService;
    @Autowired
    private ActivityCategoryI18nService activityCategoryI18nService;
    @Autowired
    private UserAiCourseService userAiCourseService;

    @Test
    public void deleteOperationalActivityByActivityId() {
//        userAiCourseService.delectByQuery(UserAiCourseQuery.builder()
//                .isDelete(YesNoStatus.NO.getCode())
//                .userId(-1l)
//                .baseInfoId(-1l)
//                .build());
        userService.deleteById(-1l);

        //恢复机器人
//        userDao.recovery(-1l);
//        userAccountDetailService.delete(-1l, "定时取消未付款流水");
//        runActivityService.deleteActivity(-1l);
//        propertyService.delete(-1l, "sss");
//        goodsDao.update(new LambdaUpdateWrapper<ZnsGoodsEntity>().set(ZnsGoodsEntity::getIsDelete,1)
//                .set(ZnsGoodsEntity::getModifier,"sss").eq(ZnsGoodsEntity::getId,-1l));
//        goodsCategoryService.delete(-1l, "sss");
//        brandService.delete(-1l, "sss");
//        taskDetailService.deleteByQuery(new TaskDetailQuery().setTaskPackageId(-1l));
//        taskAwardService.deleteByQuery(new TaskAwardQuery().setTaskPackageId(-1l));
//        refundRemarkI18nService.deleteByQuery(new RefundRemarkI18nQuery().setRefundRemarkId(-1l));
//        propertyService.delete(-1l, "sss");
//        PlaylistMusicRel playlistMusicRel = new PlaylistMusicRel();
//        playlistMusicRel.setModifier("sss");
//        playlistMusicRel.setIsDelete(1);
//        playlistMusicRelService.deletePlaylistMusicRelByPlaylistId(-1l, "sss");
//        messageTaskUserService.deleteByTaskId(-1l);
//        mallRetainPopGoodsRelService.deleteByPopId(-1l);
//        runActivityService.deleteActivity(-1l);
//        activityPlaylistRelService.deleteActivityPlaylistRelByActivityId(-1l, "sss");
//        activityCategoryI18nService.deleteByCategory(-1l);
////        deleteActivityBrandRightsInterestsByActivityId(1l);
//
//        taskPackageService.delete(-1l, "sss");

//        customerAnswerDetailService.delectByConfigId(-1l);

//        LambdaUpdateWrapper<MsgNonDisturbUserDo> update = Wrappers.lambdaUpdate(MsgNonDisturbUserDo.class)
//                .set(MsgNonDisturbUserDo::getIsDelete, System.currentTimeMillis())
//                .set(MsgNonDisturbUserDo::getModifier, "sss")
//                .eq(MsgNonDisturbUserDo::getId, -1l);
//        boolean b = mapper.update(update) == 1;

//        treadmillSoundpackService.delete(-1l, "sss");
//        treadmillAudioSoundpackRelService.deleteByPackageIdAndVersion(-1l,12);
//        treadmillSoundpackHistoryService.deleteById(-1l);
//
//        deviceVersionService.delete(-1l, "SecurityUtils.getUsername()");

//        courseService.delete(-1l, "SecurityUtils.getUsername()");

//        exchangeScoreRuleService.deleteExchangeScoreRuleById(-1l);
//        runPlaylistService.delete(1l, "SecurityUtils.getUsername()");
//        activityPlaylistRelService.deleteActivityPlaylistRelByPlaylistId(1l,"SecurityUtils.getUsername()");
//        operationalActivityUserService.deleteOperationalActivityByActivityId(1L);
//        popUserDao.delete(Wrappers.<PopUser>lambdaUpdate().eq(PopUser::getPopupId, 1l).eq(PopUser::getPopType, 22).eq(PopUser::getIsDelete, 0));
//        pagePopupRelationService.deleteByPopupId(1l);
//        mallHomeModuleService.deleteByHomeId(1l);
//        mallHomeModuleI8nService.deleteByModuleId(1l);
//        homeModuleGoodService.deleteByModuleId(1l);
//        userService.deleteById(1l);
//        userThirdOauthService.deleteUserThirdOauth(1l);
//        userEquipmentShareMapper.deleteById(1l);
//        roomMsgMapper.deleteById(1l);
//        popUserService.deleteInvalidConfigPopUser(1l, PopPositionEnum.HOME_PAGE_POP.getCode());
//        trainingPlanI18nService.deleteByPlanId(1l);
//        trainingPlanService.deleteById(1l);
//        goodsFileService.deleteByGoodsId(1l,"US","");
//        goodsSkuI18nService.deleteByGoodsId(1l);
//        appRnUpgradeDao.deleteById(1l);
//        LambdaUpdateWrapper<OperationalPlanEntity> updateWrapper = new UpdateWrapper<OperationalPlanEntity>().lambda();
//        updateWrapper.set(OperationalPlanEntity::getModifier, "ces");
//        updateWrapper.eq(OperationalPlanEntity::getId, 1l);
//        updateWrapper.set(OperationalPlanEntity::getState, OperationalConstant.StateEnum.END.getCode());
//        operationalPlanDao.update(null, updateWrapper);
//        operationalPlanDao.update(updateWrapper);
//        updateWrapper.set(OperationalPlanEntity::getIsDelete, 1);
//        operationalPlanDao.update(null, updateWrapper);
//        operationalPlanDao.update(updateWrapper);

//        Wrapper<ActivityRecommendDisseminateDo> updateWrapper = Wrappers.<ActivityRecommendDisseminateDo>lambdaUpdate()
//                .eq(ActivityRecommendDisseminateDo::getMainActivityId, 1l);
//        activityRecommendDisseminateMapper.delete(updateWrapper);
//        activityCategoryService.deleteById(1l,"sss");
//        runMusicService.deleteRunMusicById(1l);
    }

}
