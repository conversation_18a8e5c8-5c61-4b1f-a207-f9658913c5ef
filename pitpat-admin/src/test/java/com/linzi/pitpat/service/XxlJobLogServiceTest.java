package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.systemservice.service.XxlJobLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;

class XxlJobLogServiceTest extends PitpatAdminApplicationTests {

    @Autowired
    private XxlJobLogService xxlJobLogService;

    // 测试：当传入有效日期时应调用DAO方法
    @Test
    void testDeleteByNDays_WithValidDate_CallsDaoDelete() {
        // 准备测试数据
        ZonedDateTime testDate = ZonedDateTime.now();
        testDate = DateUtil.addDays(testDate, -30);
        // 执行被测方法
        xxlJobLogService.deleteByNDays(testDate);
    }
}
