package com.linzi.pitpat.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.admin.manager.message.EmailReachManager;
import com.linzi.pitpat.admin.manager.region.RegionManager;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.dto.api.request.BasicPageDto;
import com.linzi.pitpat.data.activityservice.service.PkChallengeBizService;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.dto.SendUserCouponDto;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCouponSendDetail;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponSendDetailService;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.messageservice.dto.response.EmailReachResp;
import com.linzi.pitpat.data.messageservice.model.entity.EmailTemplate;
import com.linzi.pitpat.data.messageservice.service.EmailTemplateService;
import com.linzi.pitpat.data.systemservice.dto.request.RegisterCountryRequestDto;
import com.linzi.pitpat.data.systemservice.dto.response.HomePageCustomizeAppResponseDto;
import com.linzi.pitpat.data.systemservice.manager.ConfigPopManager;
import com.linzi.pitpat.data.systemservice.model.entity.HomePageCustomizeDo;
import com.linzi.pitpat.data.systemservice.model.query.BottomPopInsertRequest;
import com.linzi.pitpat.data.systemservice.model.query.BottomPopQuery;
import com.linzi.pitpat.data.systemservice.service.BottomPopService;
import com.linzi.pitpat.data.systemservice.service.HomePageCustomizeService;
import com.linzi.pitpat.data.userservice.quartz.VipTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/7 11:57
 * @Description TODO
 */
public class PopTest extends PitpatAdminApplicationTests {

    @Resource
    private ConfigPopManager configPopManager;
    @Resource
    private BottomPopService bottomPopService;
    @Resource
    private UserCouponSendDetailService userCouponSendDetailService;
    @Resource
    private CouponService couponService;
    @Resource
    private UserCouponManager userCouponManager;
    @Resource
    private RegionManager regionManager;
    @Resource
    private EmailTemplateService emailTemplateService;
    @Resource
    private EmailReachManager emailReachManager;
    @Resource
    private VipTask vipTask;
    @Resource
    private PkChallengeBizService pkChallengeBizService;
    @Resource
    private ZnsCourseService znsCourseService;

    @Test
    void testYc() {
        String heightStr = "5.9";
        String[] split = heightStr.split("\\.");
        int feet = Integer.parseInt(split[0]);
        int inches = Integer.parseInt(split[1]);
        double v = (feet * 12 * 2.54) + (inches * 2.54);
        BigDecimal height = MapUtil.getBigDecimal(v).multiply(new BigDecimal(1000));
        System.out.println(height);
        System.out.println(v);

    }

    @Test
    void testCourseSelect() {
        ZnsCourseEntity znsCourseEntity = znsCourseService.selectCourseById(6L);
        System.out.println(znsCourseEntity);
    }

    @Autowired
    private HomePageCustomizeService homePageCustomizeService;

    @Test
    void testVipRemind() {
        HomePageCustomizeDo byId = homePageCustomizeService.findById(2L);
        HomePageCustomizeAppResponseDto homePageCustomizeResponseDto1 = JsonUtil.readValue(byId.getContent(), HomePageCustomizeAppResponseDto.class);
        System.out.println(homePageCustomizeResponseDto1);
//        vipTask.memberExpireRemind();
    }

    @Test
    void testCountryUpdate() {
        RegisterCountryRequestDto registerCountryRequestDto = new RegisterCountryRequestDto();
        registerCountryRequestDto.setCountry("中国");
        registerCountryRequestDto.setOpenRegister(1);
        regionManager.updateOpenRegisterStatus(registerCountryRequestDto);
    }

    @Test
    void testPopUpList() {
        BottomPopQuery query = BottomPopQuery.builder().popIds(List.of(1L, 2L, 3L)).limit(5).build();
//        List<PopUp> initPopUps = popUpService.findList(query);
        Page<EmailTemplate> page = emailTemplateService.page(new Page<EmailTemplate>(1, 10));
        System.out.println(page);
    }

    @Test
    void testReachList() {
        BasicPageDto basicPageDto = new BasicPageDto();
        basicPageDto.setPageNum(1);
        basicPageDto.setPageSize(10);
        Page<EmailReachResp> list = emailReachManager.list(basicPageDto);
        System.out.println(list);
    }

    @Test
    void testBottomPop() {
        String json = "{\"activityGuidanceType\":0,\"areaIds\":\"[1,58,59]\",\"contextList\":\"[{\\\"langCode\\\":\\\"en_US\\\",\\\"guidanceCodeContext\\\":{\\\"buttonContent\\\":\\\"1\\\",\\\"jumpId\\\":1,\\\"jumpType\\\":2,\\\"pushTitle\\\":\\\"1\\\"}},{\\\"langCode\\\":\\\"fr_CA\\\",\\\"id\\\":371,\\\"gmtCreate\\\":\\\"2023-12-07 16:08:43\\\",\\\"gmtModified\\\":\\\"2023-12-07 17:35:55\\\",\\\"validStartTime\\\":\\\"2023-12-07 02:49:59\\\",\\\"validEndTime\\\":\\\"2023-12-07 02:59:59\\\",\\\"isDelete\\\":0,\\\"title\\\":\\\"sun123\\\",\\\"priority\\\":1,\\\"type\\\":0,\\\"activityGuidanceType\\\":0,\\\"validType\\\":0,\\\"status\\\":0,\\\"isAll\\\":-1,\\\"triggerPageType\\\":0,\\\"activityType\\\":null,\\\"triggerNodeType\\\":0,\\\"delayTime\\\":1,\\\"couponType\\\":null,\\\"couponId\\\":null,\\\"couponName\\\":null,\\\"stimulateType\\\":0,\\\"triggerFrequency\\\":3,\\\"questionnaireContext\\\":null,\\\"guidanceCodeContext\\\":{\\\"buttonContent\\\":\\\"2\\\",\\\"jumpId\\\":1,\\\"jumpType\\\":2,\\\"linkUrl\\\":\\\"lzrn://Course/CourseDetailPage\\\",\\\"pushTitle\\\":\\\"2\\\"},\\\"guidanceImageContext\\\":null,\\\"jumpParam\\\":\\\"{\\\\\\\"courseId\\\\\\\":1}\\\",\\\"jumpUrl\\\":\\\"lzrn://Course/CourseDetailPage\\\",\\\"grandeContext\\\":null,\\\"stimulateScore\\\":null,\\\"questionnaireContextDto\\\":null,\\\"guidanceCodeContextDto\\\":null,\\\"guidanceImageContextDto\\\":null,\\\"grandeContextDto\\\":null,\\\"defaultLangCode\\\":\\\"en_US\\\",\\\"defaultLangName\\\":\\\"英语\\\",\\\"timeType\\\":2,\\\"adminZoneOffset\\\":8,\\\"pageNameList\\\":null,\\\"pageIdList\\\":[35],\\\"users\\\":null,\\\"areas\\\":[1,58,59],\\\"contextList\\\":[{\\\"langCode\\\":\\\"en_US\\\",\\\"questionnaireContext\\\":null,\\\"guidanceCodeContext\\\":\\\"{\\\\\\\"buttonContent\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"jumpId\\\\\\\":1,\\\\\\\"jumpType\\\\\\\":2,\\\\\\\"linkUrl\\\\\\\":\\\\\\\"lzrn://Course/CourseDetailPage\\\\\\\",\\\\\\\"pushTitle\\\\\\\":\\\\\\\"1\\\\\\\"}\\\",\\\"guidanceImageContext\\\":null,\\\"grandeContext\\\":null},{\\\"langCode\\\":\\\"fr_CA\\\",\\\"questionnaireContext\\\":null,\\\"guidanceCodeContext\\\":\\\"{\\\\\\\"buttonContent\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"jumpId\\\\\\\":1,\\\\\\\"jumpType\\\\\\\":2,\\\\\\\"linkUrl\\\\\\\":\\\\\\\"lzrn://Course/CourseDetailPage\\\\\\\",\\\\\\\"pushTitle\\\\\\\":\\\\\\\"2\\\\\\\"}\\\",\\\"guidanceImageContext\\\":null,\\\"grandeContext\\\":null}],\\\"validTime\\\":[\\\"2023-12-07 02:49:59\\\",\\\"2023-12-07 02:59:59\\\"],\\\"couponAward\\\":{\\\"couponId\\\":null,\\\"num\\\":1,\\\"couponName\\\":null},\\\"routeId\\\":35}]\",\"defaultLangCode\":\"en_US\",\"delayTime\":1,\"guidanceCodeContext\":\"{\\\"buttonContent\\\":\\\"1\\\",\\\"jumpId\\\":1,\\\"jumpType\\\":2,\\\"pushTitle\\\":\\\"1\\\"}\",\"id\":371,\"priority\":1,\"stimulateType\":0,\"timeType\":2,\"title\":\"sun123\",\"triggerFrequency\":3,\"triggerNodeType\":0,\"triggerPageIds\":\"35\",\"triggerPageType\":0,\"type\":0,\"validEndTime\":1701889199000,\"validStartTime\":1701888599000,\"validType\":0}";
        BottomPopInsertRequest bottomPopupInfo = JsonUtil.readValue(json, BottomPopInsertRequest.class);
        bottomPopService.bottomPopInsertOrUpdate(null, bottomPopupInfo, 8);

    }

    @Test
    void testUserCoupon() {
        UserCouponSendDetail sendDetail = userCouponSendDetailService.getById(5409L);
        SendUserCouponDto sendUserCouponDto = new SendUserCouponDto(sendDetail.getCouponId(), sendDetail.getUserId(), 0L, sendDetail.getId());
        Coupon coupon = couponService.selectCouponById(sendUserCouponDto.getCouponId());
        userCouponManager.sendUserCoupon(sendUserCouponDto, sendDetail, coupon);

    }
}
