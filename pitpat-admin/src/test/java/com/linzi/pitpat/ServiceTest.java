package com.linzi.pitpat;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.command.RobotInitAvatarCommandRunner;
import com.linzi.pitpat.admin.manager.exchangeRate.ExchangeRateConfigManager;
import com.linzi.pitpat.admin.model.Dto.response.exchangeRate.ExchangeRateResponseDto;
import com.linzi.pitpat.admin.model.ExerciseMuscleVo;
import com.linzi.pitpat.admin.model.MovementVo;
import com.linzi.pitpat.admin.service.movement.MovementLibraryService;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.PayActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.converter.api.UserGamePropRecordConverter;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.PropActivityUserRecordDto;
import com.linzi.pitpat.data.activityservice.listener.event.RunEndEvent;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.PropRankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.api.RankActivityManager;
import com.linzi.pitpat.data.activityservice.manager.api.UserGamePropManager;
import com.linzi.pitpat.data.activityservice.manager.console.ConsloeActivityManager;
import com.linzi.pitpat.data.activityservice.manager.console.PolymerizationActivityManager;
import com.linzi.pitpat.data.activityservice.mapper.ActivityTeamDao;
import com.linzi.pitpat.data.activityservice.mapper.MainActivityMapper;
import com.linzi.pitpat.data.activityservice.mapper.MindUserMatchDao;
import com.linzi.pitpat.data.activityservice.mapper.SeriesActivityRelMapper;
import com.linzi.pitpat.data.activityservice.mapper.TrainingOfflinePkRecordDao;
import com.linzi.pitpat.data.activityservice.mapper.UserRankedMatchMapper;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsUserRunDataDetailsDao;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityOfficialDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserGroup;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.PropRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.UserGamePropOperationLogDo;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRankedLevelQuery;
import com.linzi.pitpat.data.activityservice.quartz.MainActivityTask;
import com.linzi.pitpat.data.activityservice.quartz.PolymerizationActTask;
import com.linzi.pitpat.data.activityservice.quartz.UpdateRunActivityStatusTask;
import com.linzi.pitpat.data.activityservice.quartz.UpdateTeamGradeTask;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserGroupService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.UserRankedMatchService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataEveryEverySecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.activityservice.strategy.OfficialTeamCompetitionActivityStrategy;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.mapper.AwardConfigAmountDao;
import com.linzi.pitpat.data.awardservice.mapper.AwardConfigDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.model.vo.AwardRelation;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.DelayReportDto;
import com.linzi.pitpat.data.entity.dto.RobotUsedInfo158DTO;
import com.linzi.pitpat.data.entity.po.MovementPo;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsReviewGoodsService;
import com.linzi.pitpat.data.messageservice.mapper.MessageTaskMsgDao;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.messageservice.util.IosPushyUtils;
import com.linzi.pitpat.data.movementservice.model.entity.Movement;
import com.linzi.pitpat.data.movementservice.service.MovementService;
import com.linzi.pitpat.data.paymentservice.biz.PayPalPaymentBizService;
import com.linzi.pitpat.data.paymentservice.biz.PaymentTradeBizService;
import com.linzi.pitpat.data.paymentservice.biz.client.PayPalExecutor;
import com.linzi.pitpat.data.paymentservice.biz.client.paypalmodel.SubscriptionInfo;
import com.linzi.pitpat.data.paymentservice.biz.client.paypalmodel.SubscriptionsTransactionRecord;
import com.linzi.pitpat.data.paymentservice.enums.PaymentGoodsTypeEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentMethodEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentRefTypeEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentTradeSourceEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentTradeStateEnum;
import com.linzi.pitpat.data.paymentservice.manager.PaymentWebhookManager;
import com.linzi.pitpat.data.paymentservice.model.bo.PaymentTradeBo;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentTradeDo;
import com.linzi.pitpat.data.paymentservice.script.PayPalDataMigrationScript;
import com.linzi.pitpat.data.paymentservice.service.PaymentTradeService;
import com.linzi.pitpat.data.quartz.CourseTask;
import com.linzi.pitpat.data.quartz.RotTest;
import com.linzi.pitpat.data.quartz.script.FriendPKCleanScript;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.robotservice.biz.RobotBizService;
import com.linzi.pitpat.data.robotservice.listener.RotFollowListener;
import com.linzi.pitpat.data.robotservice.mapper.RotPicDao;
import com.linzi.pitpat.data.robotservice.mapper.RotPondDao;
import com.linzi.pitpat.data.robotservice.model.domain.RobotCache;
import com.linzi.pitpat.data.robotservice.model.domain.RobotQuery;
import com.linzi.pitpat.data.robotservice.model.entity.DelayTimeSetting;
import com.linzi.pitpat.data.robotservice.model.entity.RotPic;
import com.linzi.pitpat.data.robotservice.model.entity.RotPondAdd;
import com.linzi.pitpat.data.robotservice.model.entity.RotPondAddMetaInfo;
import com.linzi.pitpat.data.robotservice.quartz.AutoRotPondTask;
import com.linzi.pitpat.data.robotservice.quartz.RobotManageTask;
import com.linzi.pitpat.data.robotservice.quartz.RobotTask;
import com.linzi.pitpat.data.robotservice.quartz.RotFollowCountReduceTask;
import com.linzi.pitpat.data.robotservice.quartz.RotPondTask;
import com.linzi.pitpat.data.robotservice.quartz.script.RotInternationalizationScript;
import com.linzi.pitpat.data.robotservice.quartz.script.RotScript;
import com.linzi.pitpat.data.robotservice.service.RobotManageControlService;
import com.linzi.pitpat.data.robotservice.service.RobotRunRecordLimitService;
import com.linzi.pitpat.data.robotservice.service.RotNickService;
import com.linzi.pitpat.data.robotservice.service.RotPondService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkUserTypeEnum;
import com.linzi.pitpat.data.turbolink.executor.TurbolinkCampaignExecutor;
import com.linzi.pitpat.data.turbolink.executor.TurbolinkEventExecutor;
import com.linzi.pitpat.data.turbolink.executor.TurbolinkMarketExecutor;
import com.linzi.pitpat.data.turbolink.model.dto.api.CampaignOutline;
import com.linzi.pitpat.data.turbolink.model.dto.event.TurbolinkEvent;
import com.linzi.pitpat.data.turbolink.quartz.TurbolinkTask;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.mapper.label.LabelManageSqlTemplateDao;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContent;
import com.linzi.pitpat.data.userservice.model.entity.UserPushToken;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.quartz.CommunityContentPublishTask;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserPushTokenService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.impl.ZnsUserServiceImpl;
import com.linzi.pitpat.data.userservice.service.label.LabelManageAttrService;
import com.linzi.pitpat.data.userservice.service.label.LabelManageLineService;
import com.linzi.pitpat.data.userservice.service.label.LabelUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.Test;
import org.redisson.api.RBitSet;
import org.redisson.api.RBoundedBlockingQueue;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest(classes = PitpatAdminApplication.class)
public class ServiceTest  {

    @Resource
    private CommunityContentService communityContentService;
    @Resource
    private UserRankedMatchService userRankedMatchService;
    @Resource
    private CommunityContentPublishTask communityContentPublishTask;

    @Autowired
    private MovementLibraryService movementLibraryService;

    @Autowired
    private MovementService movementService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ActivityTeamService activityTeamService;

    @Autowired
    private AwardConfigDao awardConfigDao;

    @Autowired
    private ZnsRunActivityService activityService;

    @Autowired
    private UpdateTeamGradeTask updateTeamGradeTask;
    @Autowired
    private ZnsRunActivityDao runActivityDao;

    @Autowired
    private ZnsRunActivityUserService activityUserService;

    @Autowired
    private OfficialTeamCompetitionActivityStrategy teamCompetitionActivityStrategy;

    @Autowired
    private MessageTaskMsgDao messageTaskMsgDao;

    @Autowired
    private ZnsReviewGoodsService reviewGoodsService;

    @Autowired
    private ZnsUserFriendService znsUserFriendService;
    @Value("${zns.config.rabbitQueue.delayed_content_publish_exchange_name}")
    private String delay_exchange_name_rot;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;

    @Resource
    private ZnsUserFriendService friendService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RobotTask robotTask;

    @Autowired
    private RotFollowListener rotFollowListener;

    @Autowired
    private RotFollowCountReduceTask reduceTask;

    @Autowired
    private ZnsRunActivityDao znsRunActivityDao;

    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;

    @Resource
    private UpdateRunActivityStatusTask updateRunActivityStatusTask;

    @Resource
    private MindUserMatchDao mindUserMatchDao;
    @Resource
    private ActivityUserScoreDao activityUserScoreDao;
    @Resource
    private ActivityUserScoreService activityUserScoreService;
    @Resource
    private ZnsUserService userService;
    @Resource
    private ZnsUserDao userDao;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private RotPondTask rotPondTask;
    @Resource
    private RobotInitAvatarCommandRunner commandRunner;
    @Resource
    private RotPondService rotPondService;
    @Resource
    private RotPondDao rotPondDao;

    @Resource
    private ZnsUserServiceImpl userServiceImpl;

    @Resource
    private RotPicDao rotPicDao;
    @Resource
    private AutoRotPondTask autoRotPondTask;

    @Resource
    private RotTest rotTest;

    @Resource
    private LabelUserService labelUserService;

    @Resource
    private LabelManageAttrService labelManageAttrService;

    @Resource
    private LabelManageLineService labelManageLineService;

    @Resource
    private CourseTask courseTask;
    @Resource
    private TrainingOfflinePkRecordDao trainingOfflinePkRecordDao;


    @Resource
    private RotScript rotScript;

    @Resource
    private RotNickService rotNickService;
    @Resource
    private ZnsUserRunDataDetailsDao znsUserRunDataDetailsDao;
    @Resource
    private RotInternationalizationScript rotInternationalizationScript;

    @Resource
    private ActivityStrategyContext activityStrategyContext;
    @Resource
    private AppMessageService appMessageService;
    @Resource
    private AppRouteConfigService appRouteConfigService;
    @Resource
    private ExchangeRateConfigManager exchangeRateConfigManager;

    @Resource
    private UserPushTokenService userPushTokenService;
    @Resource
    private LabelManageSqlTemplateDao labelManageSqlTemplateDao;

    @Resource
    private WearsService wearsService;
    @Resource
    private ZnsGoodsService znsGoodsService;
    @Resource
    private AwardConfigAmountDao  awardConfigAmountDao;

    @Resource
    private PolymerizationActTask polymerizationActTask;

    @Resource
    private ActivityPolymerizationRecordService polymerizationRecordService;

    @Resource
    private MainActivityTask mainActivityTask;
    @Resource
    private MainActivityMapper mainActivityMapper;

    @Resource
    private MainActivityService mainActivityService;
    @Resource
    private ConsloeActivityManager consloeActivityManager;
    @Resource
    private ActivityResultManager activityResultManager;
    @Resource
    private PayActivityBizService payActivityBizService;

    @Resource
    private SeriesActivityRelService seriesActivityRelService;

    @Resource
    private SeriesActivityRelMapper seriesActivityRelMapper;
    @Resource
    private PolymerizationActivityPoleService polymerizationActivityPoleService;

    @Autowired
    private ActivityUserGroupService activityUserGroupService;
    @Autowired
    private UserGroupRelService userGroupRelService;
    @Autowired
    private UserRankedLevelService userRankedLevelService;
    @Autowired
    private UserRankedMatchMapper userRankedMatchMapper;

//    @Resource
//    private RotNickScript rotNickScript;
    @Resource
    private ActivityTeamDao activityTeamDao;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RobotManageTask robotManageTask;
    @Autowired
    private RobotRunRecordLimitService runRecordLimitService;

    @Autowired
    private RobotManageControlService robotManageControlService;

    @Autowired
    private FriendPKCleanScript friendPKCleanScript;

    @Autowired
    private RobotBizService robotBizService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private PolymerizationActivityManager polymerizationActivityManager;
    @Autowired
    private RunActivityBizService runActivityBizService;
    @Autowired
    private RankActivityManager rankActivityManager;

    @Autowired
    private PayPalExecutor payPalExecutor;

    @Autowired
    private PaymentWebhookManager paymentWebhookManager;


    @Autowired
    private PaymentTradeService paymentTradeService;

    @Value("${zns.config.rabbitQueue.pushDelivery}")
    private String pushDelivery;
    @Value("${spring.profiles.active}")
    private String profile;

    @Autowired
    private UserGamePropRecordConverter userGamePropRecordConverter;

    @Autowired
    private UserGamePropManager propManager;
    @Autowired
    private MainActivityBizService mainActivityBizService;
    @Autowired
    private PropRankedActivityResultManager propRankedActivityResultManager;
    @Autowired
    private PayPalDataMigrationScript payPalDataMigrationScript;

    @Autowired
    private PaypalPayService paypalPayService;


    @Autowired
    private  PayPalPaymentBizService payPalPaymentBizService;

    @Autowired
    private  PaymentTradeBizService paymentTradeBizService;

    @Autowired
    private TurbolinkMarketExecutor turbolinkMarketExecutor;

    @Autowired
    private TurbolinkCampaignExecutor turbolinkCampaignExecutor;

    @Autowired
    private TurbolinkEventExecutor turbolinkEventExecutor;

    @Autowired
    private TurbolinkTask turbolinkTask;

    @Test
    public void test102(){
       // AwsUtil.deleteS3Object("community/video/20250522/1747905912video-2025-05-22-17-25-10-167-942365.mp4");
        AwsUtil.deleteS3Object("community/video/20250522/1747905912video-2025-05-22-17-25-10-167-942365.mp4");


    }

    @Test
    public void test101(){
        turbolinkCampaignExecutor.syncTurbolinkUserType(8322066l, TurbolinkUserTypeEnum.VIP);
    }

    @Test
    public void test100(){
        turbolinkTask.syncLocalCampaign();
    }


    @Test
    public void test99(){
        TurbolinkEvent turbolinkEvent = new TurbolinkEvent("free_run", 83066l, Map.of("mile", "100"));

        turbolinkEventExecutor.eventReport(turbolinkEvent);


    }
    @Test
    public void test98(){
        List<CampaignOutline> allCampaign = turbolinkCampaignExecutor.getAllCampaign();
        System.out.println(allCampaign);
    }
    @Test
    public void test97(){
        turbolinkMarketExecutor.acquireCampaignInfo("csavvida5kla03h1v6dg");
    }

    @Test
    public void test96(){
//        appMessageService.sendIm(null, List.of(94198l), "CONTENT", TencentImConstant.TIM_TEXT_ELEM, OrderUtil.getBatchNo(), 0);

    }
    @Test
    public void test95(){
        RBitSet bT = redissonClient.getBitSet("bT");
        bT.set(123l,true);
        bT.set(461l,true);
        bT.set(711l,true);
        for (int i = 0; i < 1000; i++) {
            System.out.println(i+"--"+bT.get(i));
        }
        bT.delete();
    }
    @Test
    public void test94(){
        payPalPaymentBizService.scanNullRec(ZonedDateTime.now());
    }
    @Test
    public void test93(){
        payPalPaymentBizService.checkRefund("I-FAVP6MT6K8M0");
    }
    @Test
    public void test92(){
        PaymentTradeBo bo = paymentTradeBizService.findPaymentTradeBo("83348.68712920240902085232");
        payPalPaymentBizService.paypalRenewSubscription(bo);
    }
    @Test
    public void test91(){
        PaypalPay pay = paypalPayService.getById(488);
        PaymentTradeDo tradeDo = new PaymentTradeDo();
        tradeDo.setTradeNo(OrderUtil.getUniqueCode(pay.getUserId() + "."));
        tradeDo.setUserId(pay.getUserId());
        tradeDo.setPaymentMethod(PaymentMethodEnum.PAYPAL_WEB.getCode());
        tradeDo.setPaymentTradeState(PaymentTradeStateEnum.PAY_SUCCESS.getCode());
        tradeDo.setGoodsId(pay.getRefId());
        tradeDo.setGoodsType(PaymentGoodsTypeEnum.SINGLE.getCode());
        tradeDo.setPaymentTradeSource( PaymentTradeSourceEnum.PEOPLE_APP.getCode());
        tradeDo.setPurchaseDate(ZonedDateTime.now());
        tradeDo.setDealDate(ZonedDateTime.now());
        tradeDo.setStatusChangeDate(ZonedDateTime.now());
        tradeDo.setRefId("11");
        tradeDo.setRefType(PaymentRefTypeEnum.PREMIER_MEMBER.getCode());
        paymentTradeService.create(tradeDo);

        pay.setNewTradeNo(tradeDo.getTradeNo());
        paypalPayService.updateById(pay);
    }
    @Test
    public void test90(){
        payPalDataMigrationScript.run();
    }
    @Test
    public void test89(){
        BigDecimal growLevelProgress = sysConfigService.getGrowLevelProgress(BigDecimal.valueOf(2000), 5);
        System.out.println(growLevelProgress);
    }
    @Test
    public void test88(){
        propRankedActivityResultManager.test();
    }
    @Test
    public void test87(){
        List<String> list = new ArrayList<>();

        list.add("<EMAIL>");
        list.add("<EMAIL>");
        list.add("<EMAIL>");

        List<Long> longs = userService.selectUserIdByEmails(list);
        System.out.println(longs);
    }


    @Test
    public void test86(){
        Map<String, Object> stringObjectMap = znsUserRunDataDetailsDao.selectMaps(new QueryWrapper<ZnsUserRunDataDetailsEntity>()
                .select("ifnull(sum(run_mileage),0) run_mileage,ifnull(sum(kilocalorie),0) kilocalorie,count(*) totalCount,ifnull(sum(run_time),0) run_time,ifnull(sum(step_num),0) step_num,ifnull(avg(average_pace),0) average_pace,sum(fat_consumption) fat_consumption")
                .eq("user_id", 1)
                .eq("is_delete", 0)
                .eq("run_status", 1)
                .eq("run_type", 1)
                .ge(Objects.nonNull(ZonedDateTime.now()), "create_time", ZonedDateTime.now())
                .le(Objects.nonNull(ZonedDateTime.now()), "create_time", ZonedDateTime.now())
        ).get(0);
        System.out.println(stringObjectMap
        );
        System.out.println(userRunDataDetailsService.getTotalRecordMap(1l));
        System.out.println(userRunDataDetailsService.getShareDateTotalRecordMap(1l,2));
        System.out.println(userRunDataDetailsService.getAllTotalRecordMap(1l));

    }
    @Test
    public void test85(){
        RList<PropRankedLevel> list = redissonClient.getList(RedisConstants.PROP_RANKED_LEVEL_RESULT);
        System.out.println(list);
        redissonClient.getList(RedisConstants.RANKED_LEVEL_RESULT).delete();
        redissonClient.getList(RedisConstants.PROP_RANKED_LEVEL_RESULT).delete();
    }
    @Test
    public void test84(){
        List<UserGamePropOperationLogDo> list = new ArrayList<>();
        UserGamePropOperationLogDo logDo = new UserGamePropOperationLogDo();
        logDo.setSourceUserId(1l);
        logDo.setAction(2);
        logDo.setTargetUserId(2l);
        logDo.setActivityId(123l);
        logDo.setPropId(1l);
        list.add(logDo);

        UserGamePropOperationLogDo logDo1 = new UserGamePropOperationLogDo();
        logDo1.setSourceUserId(1l);
        logDo1.setAction(2);
        logDo1.setActivityId(123l);
        logDo1.setTargetUserId(3l);
        logDo1.setPropId(1l);

        list.add(logDo1);



        propManager.saveGamePropOperationLog(list);
    }

    @Test
    public void test83(){

        PropActivityUserRecordDto dto = new PropActivityUserRecordDto();
        dto.setActivityId(1l);
        dto.setSourceUserId(2l);
        dto.setTargetUserIds(List.of(3l,4l));
        System.out.println(userGamePropRecordConverter.toDos(dto));
    }

    @Test
    public void test82() {
        PaymentTradeDo paymentTradeDo = paymentTradeService.findById(1803l).get();
        ZonedDateTime subscriptionStartDate = paymentTradeDo.getSubscriptionExpireDate();
        ZonedDateTime finalSubscriptionStartDate= subscriptionStartDate;

        System.out.println(subscriptionStartDate);
        LocalTime subscriptionTime = subscriptionStartDate.toLocalTime();
        LocalTime nowTime = LocalTime.now();
        if (nowTime.isAfter(subscriptionTime)){
            finalSubscriptionStartDate = finalSubscriptionStartDate.with(nowTime);
            log.info("进过计算给的过期时间为{}", finalSubscriptionStartDate);
        }

    }
    @Test
    public void test81() {
      String body = """
              {
                "id": "WH-8RP8019509131735L-5RE48436GK718525S",
                "create_time": "2024-06-26T14:09:22.810Z",
                "resource_type": "subscription",
                "event_type": "BILLING.SUBSCRIPTION.ACTIVATED",
                "summary": "Subscription activated",
                "resource": {
                  "quantity": "1",
                  "subscriber": {
                    "email_address": "<EMAIL>",
                    "payer_id": "8LW4N4UGTX9U8",
                    "name": {
                      "given_name": "杰",
                      "surname": "张"
                    },
                    "shipping_address": {
                      "address": {
                        "address_line_1": "71566176 Sky E137 S",
                        "admin_area_2": "Posta",
                        "admin_area_1": "Libisia",
                        "postal_code": "W185744",
                        "country_code": "HK"
                      }
                    }
                  },
                  "create_time": "2024-06-26T14:09:17Z",
                  "plan_overridden": false,
                  "shipping_amount": {
                    "currency_code": "USD",
                    "value": "0.0"
                  },
                  "start_time": "2024-06-26T14:08:25Z",
                  "update_time": "2024-06-26T14:09:18Z",
                  "billing_info": {
                    "outstanding_balance": {
                      "currency_code": "USD",
                      "value": "0.0"
                    },
                    "cycle_executions": [
                      {
                        "tenure_type": "REGULAR",
                        "sequence": 1,
                        "cycles_completed": 1,
                        "cycles_remaining": 0,
                        "current_pricing_scheme_version": 1,
                        "total_cycles": 0
                      }
                    ],
                    "last_payment": {
                      "amount": {
                        "currency_code": "USD",
                        "value": "300.0"
                      },
                      "time": "2024-06-26T14:09:17Z"
                    },
                    "next_billing_time": "2025-06-26T10:00:00Z",
                    "failed_payments_count": 0
                  },
                  "links": [
                    {
                      "href": "https://api.sandbox.paypal.com/v1/billing/subscriptions/I-S02YY59EBRYE/cancel",
                      "rel": "cancel",
                      "method": "POST",
                      "encType": "application/json"
                    },
                    {
                      "href": "https://api.sandbox.paypal.com/v1/billing/subscriptions/I-S02YY59EBRYE",
                      "rel": "edit",
                      "method": "PATCH",
                      "encType": "application/json"
                    },
                    {
                      "href": "https://api.sandbox.paypal.com/v1/billing/subscriptions/I-S02YY59EBRYE",
                      "rel": "self",
                      "method": "GET",
                      "encType": "application/json"
                    },
                    {
                      "href": "https://api.sandbox.paypal.com/v1/billing/subscriptions/I-S02YY59EBRYE/suspend",
                      "rel": "suspend",
                      "method": "POST",
                      "encType": "application/json"
                    },
                    {
                      "href": "https://api.sandbox.paypal.com/v1/billing/subscriptions/I-S02YY59EBRYE/capture",
                      "rel": "capture",
                      "method": "POST",
                      "encType": "application/json"
                    }
                  ],
                  "id": "I-S02YY59EBRYE",
                  "plan_id": "P-0BE06975AP0491412MZYUT6I",
                  "status": "ACTIVE",
                  "status_update_time": "2024-06-26T14:09:18Z"
                },
                "status": "SUCCESS",
                "transmissions": [
                  {
                    "webhook_url": "https://tkjapi.yijiesudai.com/app/pay/webhook1",
                    "http_status": 200,
                    "reason_phrase": "HTTP/1.1 200 Connection established",
                    "response_headers": {
                      "Server": "nginx/1.13.8",
                      "Connection": "keep-alive",
                      "Vary": "Access-Control-Request-Headers",
                      "Content-Length": "34",
                      "Date": "Wed, 26 Jun 2024 14:09:38 GMT",
                      "Content-Type": "application/json;charset=UTF-8"
                    },
                    "transmission_id": "b6bc43a0-33c5-11ef-b238-5bf20dc0a33e",
                    "status": "SUCCESS",
                    "timestamp": "2024-06-26T14:09:33Z"
                  }
                ],
                "links": [
                  {
                    "href": "https://api.sandbox.paypal.com/v1/notifications/webhooks-events/WH-8RP8019509131735L-5RE48436GK718525S",
                    "rel": "self",
                    "method": "GET",
                    "encType": "application/json"
                  },
                  {
                    "href": "https://api.sandbox.paypal.com/v1/notifications/webhooks-events/WH-8RP8019509131735L-5RE48436GK718525S/resend",
                    "rel": "resend",
                    "method": "POST",
                    "encType": "application/json"
                  }
                ],
                "event_version": "1.0",
                "resource_version": "2.0"
              }
              """;
        paymentWebhookManager.billingWebhookTest(body);
    }

    @Test
    public void test80() {
        SubscriptionInfo subscriptionsInfo = payPalExecutor.getSubscriptionsInfo("I-TG2VKT4XU8YS");
        log.info("Subscriptions I-TG2VKT4XU8YS-- {}", subscriptionsInfo);
        SubscriptionInfo subscriptionsInfo1 = payPalExecutor.getSubscriptionsInfo("I-VG39SVMRS6CL");
        log.info("Subscriptions I-VG39SVMRS6CL-- {}", subscriptionsInfo1);
        //Object billingInfo = subscriptionsInfo1.getBillingInfo();
        //Map<String, Object> map = JsonUtil.readValue(billingInfo);
        //Object o = map.get("next_billing_time");
        //System.out.println(o);
    }
    @Test
    public void test79() {
        SubscriptionsTransactionRecord subscriptionsInfo1 = payPalExecutor.subscriptionRecord("I-V1599RH9J9PE");
        System.out.println(subscriptionsInfo1);
    }

    @Test
    public void test78() {
        UserLevel byUserId = userLevelService.findByUserId(122223l);
        System.out.println(byUserId);
    }
    @Test
    public void test77() {
        RobotQuery query = RobotQuery.builder().countrys(List.of("CA"))
                .mode("SS").activityId(-1l).startTime(ZonedDateTime.now()).build();
        ZnsUserEntity rot = robotBizService.acquireRot(query);
        System.out.println(rot);}
    @Test
    public void test76() {
        friendPKCleanScript.clean();
    }
    @Test
    public void test75() throws Exception{
        RLock lock = redissonClient.getLock("1112");
        Boolean tryLock = lock.tryLock(1, 3600, TimeUnit.SECONDS);
        log.info("第一次{}",tryLock);
        new Thread(()->{
            Boolean tryLock1 = null;
            try {
                tryLock1 = lock.tryLock(1, 3600, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("第2次{}",tryLock1);
        }).start();
        new Thread(()->{
            RLock lock1 = redissonClient.getLock("1112");
            lock1.unlock();

            Boolean tryLock2 = null;
            try {
                tryLock2 = lock.tryLock(1, 3600, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("第3次{}",tryLock2);
        }).start();






    }

    @Test
    public void test74() throws InterruptedException {

        RBoundedBlockingQueue<Integer> queue = redissonClient.getBoundedBlockingQueue("t3");
        System.out.println(queue.isExists());
        queue.trySetCapacity(10);
        queue.put(1);
        System.out.println(queue.size()+"|"+queue.remainingCapacity());
        List<Integer> read = queue.readAll();
        System.out.println();
        queue.delete();
        queue.trySetCapacity(20);
        System.out.println(queue.size()+"|"+queue.remainingCapacity());
        for (Integer integer : read) {
            queue.put(integer);
        }
        System.out.println(queue.readAll());
        System.out.println(queue.size()+"|"+queue.remainingCapacity());

    }

    @Test
    public void test73() {
        List<Map> maps = rotPondDao.selectErrorRots();
        for (Map map : maps) {
//            robotManageControlService.lambdaUpdate().eq(RobotManageControl::getUserId,map.get("uid"))
//                    .set(RobotManageControl::getRunMode,map.get("pr")).update();
        }
    }
    @Test
    public void test72() {
//        List<RobotManageControl> list = robotManageControlService.list();
//        Map<Long, List<RobotManageControl>> map = list.stream().collect(Collectors.groupingBy(RobotManageControl::getUserId));
//        ArrayList<RobotManageControl> dels = new ArrayList<>();
//        map.forEach((userId, controls) ->{
//            if (controls.size() >1){
//                RobotManageControl control = controls.stream().sorted(Comparator.comparing(RobotManageControl::getIsInQueue)).findFirst().get();
//                dels.add(control);
//            }
//        });
//        robotManageControlService.removeBatchByIds(dels);
    }
    @Test
    public void test71() {
//        List<RobotRunRecordLimit> list = runRecordLimitService.list();
//        Map<Long, List<RobotRunRecordLimit>> map = list.stream().collect(Collectors.groupingBy(RobotRunRecordLimit::getUserId));
//        map.forEach((uid,records) ->{
//            if (records.size() >1){
//                RobotRunRecordLimit robotRunRecordLimit = records.stream().sorted(Comparator.comparing(RobotRunRecordLimit::getId)).findFirst().get();
//                runRecordLimitService.deleteById(robotRunRecordLimit.getId());
//            }
//        });
    }
    @Test
    public void test70() {

        List<MindUserMatch> mindUserMatches = mindUserMatchDao.selectList(Wrappers.<MindUserMatch>lambdaQuery().lt(true, MindUserMatch::getActivityEndTime, ZonedDateTime.now()).eq(MindUserMatch::getStatus, 1));
        System.out.println(mindUserMatches);

    }

    @Test
    public void test69() throws InterruptedException {

        RBoundedBlockingQueue<RobotCache> queue = redissonClient.getBoundedBlockingQueue("t1");
        System.out.println(queue.isExists());
        System.out.println(redissonClient.getBoundedBlockingQueue("t2").isExists());
        System.out.println(queue.size());
        //queue.trySetCapacity(10);
        RobotCache rotCache = new RobotCache();
        rotCache.setUserId(1l);
        queue.clear();
        queue.offer(rotCache);
        List<RobotCache> rotCaches = queue.readAll();
        System.out.println(rotCaches);
        System.out.println(rotCaches.size());

        RobotCache poll = queue.poll();
        System.out.println(poll);
        System.out.println(queue.size());

        System.out.println(queue.readAll().size());

        System.out.println(queue.remainingCapacity());



    }
    @Test
    public void test68() {

        activityTeamDao.addUser(115l,11l,1);

    }

    @Test
    public void test67() {

        ActivityTypeDto activityNew = runActivityService.getActivityNew(287005l);
        System.out.println(activityNew);

    }

    @Test
    public void test65() {

        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(286850l);
        if (!CollectionUtils.isEmpty(teams)) {
            for (int i = 0; i < teams.size(); i++) {
                //重置排名
                teams.get(i).setRank(1);
                //确认排名
                for (int j = 0; j < teams.size(); j++) {
                    if (teams.get(i).lowThan(teams.get(j),"2")) {
                        teams.get(i).setRank(teams.get(i).getRank() + 1);
                    }
                }
            }
        }
        System.out.println(teams);

    }

    @Test
    public void test64() {
        List<ActivityUserGroup> groupByActId = activityUserGroupService.findByActId(10l);

        List<Long> activityAllowGroups = groupByActId.stream().map(ActivityUserGroup::getGroupId).toList();
        if (!org.springframework.util.CollectionUtils.isEmpty(activityAllowGroups)) {
            Integer type = groupByActId.get(0).getType();
            List<Long> activityAllowGroupsCopy = new ArrayList<>(activityAllowGroups);
            List<Long> groups = userGroupRelService.getGroupsByUserId(123l);
            activityAllowGroupsCopy.retainAll(groups);
            System.out.println(activityAllowGroupsCopy);
            if ((activityAllowGroupsCopy.isEmpty() && type == 0) ||
                    (!activityAllowGroupsCopy.isEmpty() && type == 1)) {
                throw new BaseException(I18nMsgUtils.getMessage("activity.enroll.specialUser.commonError"));
            }
        }



    }
    @Test
    public void test631() {
        List<ZnsUserEntity> allRots = rotPondDao.selectAllRotWithoutHeadPortrait("US");
        log.info(allRots.size()+"");



    }
    @Test
    public void test6312() {
        Page<UserRankedLevel> page = new Page<>(1, 10);
        page.addOrder(OrderItem.asc("gmt_modified"));
        UserRankedLevelQuery query = UserRankedLevelQuery.builder().isRobot(YesNoStatus.YES.getCode()).countryCode("US").build();
        List<UserRankedLevel> allRots = userRankedLevelService.findList(page,query);
        log.info(allRots.size()+"");



    }
    @Test
    public void test62() {
        List<SeriesActivityRel> seriesActivityRels = seriesActivityRelMapper.selectList(null);
        Map<Long, List<SeriesActivityRel>> map = seriesActivityRels.stream().collect(Collectors.groupingBy(SeriesActivityRel::getParentActivityId));
        map.forEach((parentActId,list)->{
            for (int i = 0; i < list.size(); i++) {
                if (i>0){
                    list.get(i).setPreId(list.get(i-1).getSegmentActivityId());
                    seriesActivityRelMapper.updateById(list.get(i));
                }
            }
        });


    }
    @Test
    public void test61() {
        mainActivityService.removePolymerizationMark(Arrays.asList(564545455l));


    }
    @Test
    public void test60() {
        mainActivityBizService.cleanSingleActivity(150571l);
    }
    @Test
    public void test59() {
        //List<MainActivity> az = mainActivityMapper.findListByIdsAndState(Arrays.asList(133842l, 133843l), "AZ");
        //System.out.println(az);
    }
    @Test
    public void test58() {
        mainActivityTask.startActivity();
    }
    @Test
    public void test57() {
        mainActivityService.findToEndActivity();
    }
    @Test
    public void test56() {
        PolymerizationActivityPole pole = polymerizationActivityPoleService.findById(128l);
        //polymerizationActTask.executePolymerization(pole, true);
    }
    @Test
    public void test55() {
        RunDataRequest runData = new RunDataRequest();
        runData.setId_no(9913l);
        runData.setRunType(1);
        runData.setDataSource(1);
        runData.setRunDataDetailsId(1450162l);

        RunEndEvent runEndEvent = new RunEndEvent(new Object(), runData);
        List<ExchangeRateResponseDto> list = exchangeRateConfigManager.palPayExchangeRateList();
        System.out.println(list);
    }
    @Test
    public void test54() {
        //执行sql
        String sql = "SELECT if(IFNULL(count(*),0) >= 1 AND IFNULL(count(*),0) <= 2,1,null) FROM zns_link_bluetooth_record connect WHERE connect.is_delete=0 AND connect.user_id = 90146 AND connect.gmt_create >= '2023-10-30 08:00:00' AND connect.gmt_create <= '2023-11-08 08:00:00'";
        List<Map<String, Object>> list = labelManageSqlTemplateDao.executeSql(sql);
        boolean result = list.size()>0 && list.get(0) != null;
        System.out.println(result);
    }
    @Test
    public void test53() {

        MessageBo messageBo = new MessageBo();
        messageBo.setTitle("title");
        messageBo.setContent("content");
        messageBo.setRouteType(5);
        Long userId = 9913l;

        UserPushToken userToken = userPushTokenService.findByUserId(userId);
        //ios
        if (StringUtils.hasText(userToken.getIosPushToken()) && !"-1".equals(userToken.getIosPushToken())) {

                messageBo.setRouteValue("lznative://main/newrunningreport");


        }

        HashMap<String, Object> map = new HashMap<>();
        map.put("detailId",1450162);
        messageBo.setData(map);

        appMessageService.push(Arrays.asList(userId), messageBo, null, 0);
    }
    @Test
    public void test52() {
        Long userId = 94160l;
        UserPushToken userToken = userPushTokenService.findByUserId(userId);
        Map<String, String> map = new HashMap<>();

            map.put("routeValue","lzrn://Walk/ReportWalkSingle");
            map.put("jumpData","{\"detailId\":1450162}");


        String iosPushToken = userToken.getIosPushToken();
        IosPushyUtils.push(Arrays.asList(iosPushToken), "message.getTitle()","message.getContent()", EnvUtils.isOnline(profile)?true:false,map,1,profile,null,pushDelivery);
    }
    @Test
    public void test51() {
        MessageBo messageBo = new MessageBo();

//        messageBo.setJumpType("5");
//        messageBo.setRouteType(1);
        Map<String, Object> extras = new HashMap<>();

        Long userId = 94160l;

        UserPushToken userToken = userPushTokenService.findByUserId(userId);
        if (StringUtils.hasText(userToken.getIosPushToken()) && !"-1".equals(userToken.getIosPushToken())) {
//            messageBo.setRouteValue("lzrn://Walk/ReportWalkSingle");

            if (StringUtils.hasText(userToken.getAndroidPushToken()) && !"-1".equals(userToken.getAndroidPushToken())) {
//                messageBo.setRouteValue("/main/newrunningreport");
            }
//            extras.put("detailId", 1450162);
//            messageBo.setData(extras);
            //appMessageService.push(Arrays.asList(userId), messageBo, "");

            appMessageService.push(Arrays.asList(userId), messageBo, null, 0);

    }}
    @Test
    public void test50() {
        ZnsRunActivityEntity znsRunActivityEntity = activityService.selectActivityById(119378L);
        activityStrategyContext.handleActivityFinished(znsRunActivityEntity);

    }
    @Test
    public void test49() throws Exception{
                String name = "Pacôme";
        String getUrl = null;
        try {
            getUrl = "http://47.110.167.249:8001" + "?french="+ org.springframework.util.StringUtils.trimAllWhitespace(name);
//            getUrl = URLEncoder.encode(getUrl,"UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        //String result = HttpUtil.doGet(getUrl, 10000);
        String response = RestTemplateUtil.get(getUrl);
        System.out.println(response);
    }

    @Test
    public void test48() throws Exception{
//        List<RotNick> list = rotNickService.lambdaQuery().eq(RotNick::getLanguage, "French").list();
//        for (RotNick nick : list) {
//            String s = org.springframework.util.StringUtils.trimAllWhitespace(nick.getFirstName());
//            System.out.println(s);
//
//        }
    }

    @Test
    public void test47() throws Exception{
        String s = rotInternationalizationScript.uploadPic("https://images.unsplash.com/photo-1581557991964-125469da3b8a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0OTkyMTh8MHwxfHNlYXJjaHw0fHx0b3l8ZW58MHx8fHwxNjk3Njg0NDAxfDA&ixlib=rb-4.0.3&q=80&w=200");
        System.out.println(s);
    }
    @Test
    public void test46(){
        String activities = sysConfigService.selectConfigByKey("force.create.robot.activities");
        if (StringUtils.hasText(activities)){
            List<Long> testActivities = JsonUtil.readList(activities,Long.class);
            System.out.println(testActivities);}
    }
    @Test
    public void test45(){
        rotInternationalizationScript.batchAddRotsProxy("Germany#1#1");
    }
    @Test
    public void test44() {
        Long maxUserId ;

        List<String> list = Arrays.asList("all", "United States", null);
        for (String country : list) {
            if (Objects.isNull(country)){
                country = "all";
            }
            HashOperations operations = redisTemplate.opsForHash();
            if (operations.get(RedisConstants.COUNTRY_MAX_USER_ID, country) != null){
                maxUserId = Long.parseLong(operations.get(RedisConstants.COUNTRY_MAX_USER_ID, country).toString());

            } else {
                maxUserId = userService.selectUserMaxUser(country,null);
                operations.put(RedisConstants.COUNTRY_MAX_USER_ID, country, maxUserId.toString());
                redisTemplate.expire(RedisConstants.COUNTRY_MAX_USER_ID, 2, TimeUnit.MINUTES);
            }
            System.out.println(country +"  " + maxUserId);
        }
        System.out.println("--------------------");
        for (String country : list) {
            if (Objects.isNull(country)){
                country = "all";
            }
            HashOperations operations = redisTemplate.opsForHash();
            if (operations.get(RedisConstants.COUNTRY_MAX_USER_ID, country) != null){
                maxUserId = Long.parseLong(operations.get(RedisConstants.COUNTRY_MAX_USER_ID, country).toString());

            } else {
                maxUserId = userService.selectUserMaxUser(country,null);
                operations.put(RedisConstants.COUNTRY_MAX_USER_ID, country, maxUserId.toString());
                redisTemplate.expire(RedisConstants.COUNTRY_MAX_USER_ID, 2, TimeUnit.MINUTES);
            }
            System.out.println(country +"  " + maxUserId);
        }




    }

    @Test
    public void test43() {

        rotInternationalizationScript.cleanRepeatRotNick("United States","English");

    }

    @Test
    public void test42() {
        String filePath = "D:\\nick\\02.text";
        rotPondTask.updateTotNickByQmsjmfb(filePath,2,"United States","English");

    }

    @Test
    public void test41() {
        rotInternationalizationScript.updatePicRandom();
    }

    @Test
    public void test40() {
        //加拿大
        //rotPondTask.initRotNickDB("C:\\Users\\<USER>\\Desktop\\fre3000.txt","Canada","French");
        //美国
        rotPondTask.initRotNickDB("D:\\nick\\02.text","Canada","French");

    }
    @Test
    public void test39() {
        RotInternationalizationScript.AutoUpdatePicDto dto = new RotInternationalizationScript.AutoUpdatePicDto();

        dto.setGender(1);
        dto.setParam("car");
        rotInternationalizationScript.updatePic(JsonUtil.writeString(dto));
    }

    @Test
    public void test36() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        HashSet<Long> set = new HashSet<>();
        for (int i = 0; i < 100; i++) {
            ZnsUserEntity shortageRobotUser = userService.getShortageRobotUser(false);
            set.add(shortageRobotUser.getId());
            System.out.println(shortageRobotUser.getId());
        }
        System.out.println(set.size());
        stopWatch.stop();
        System.out.println(stopWatch.getTotalTimeSeconds());

        for (Long aLong : set) {
            userService.updateZnsUserRobotStatus(2,aLong);
        }
    }

    @Test
    public void test35() {
        ZonedDateTime startOfDate = DateUtil.getStartOfDate(ZonedDateTime.now());
        List<Map<String,Object>> todayRunMilages = znsUserRunDataDetailsDao.getAllUserMilageBytime(startOfDate);
        System.out.println(todayRunMilages);
    }

    @Test
    public void test34() {
        RotPondAddMetaInfo rotPondAddMetaInfo = new RotPondAddMetaInfo().setKm(15).setCountry("Canada").setLanguage("French");
        List<RotPondAdd> rotPondAdds = Arrays.asList(
                new RotPondAdd().setFemaleNum(10).setMaleNum(20).setMode("A9"),
                new RotPondAdd().setMaleNum(10).setMode("A8")
        );
        autoRotPondTask.autoAddRotPondProxy(JsonUtil.writeString(rotPondAddMetaInfo), JsonUtil.writeString(rotPondAdds));
    }

    @Test
    public void test33() {
        int km = 150;
        List<RotPondAdd> rotPondAdds = Arrays.asList(
                new RotPondAdd().setFemaleNum(100).setMaleNum(200).setMode("A3"),
                new RotPondAdd().setMaleNum(200).setMode("A4")

        );
        autoRotPondTask.autoAddRotPond(null,rotPondAdds);
    }

    @Test
    public void test32() {
        List<Long> allRotRunMillegeLtXXkm = userDao.getAllRotRunMillegeLtXXm(150 * 1000,true,10,"Canada");
        List<Long> allRotRunMillegeLtXXkm1 = userDao.getAllRotRunMillegeLtXXm(150 * 1000,false,20,"Canada");
    }

    @Test
    public void test31() {
        System.out.println(trainingOfflinePkRecordDao.getPkRankList());
    }

    @Test
    public void test25() {
        List<Long> allRotRunMillegeLtXXkm = userDao.getAllRotRunMillegeLtXXm(150 * 1000,true,null,null);
        System.out.println(allRotRunMillegeLtXXkm.size());

        List<Long> SRots = allRotRunMillegeLtXXkm.subList(0, 5000);
        List<Long> ARots = allRotRunMillegeLtXXkm.subList(5000, 10000);
        System.out.println(SRots.size());
        System.out.println(ARots.size());
        System.out.println(SRots);
        System.out.println(ARots);

    }
    @Test
    public void test24() {
        rotPondDao.updateStatusByUserIds(Arrays.asList(111111111l),123);
    }

    @Test
    public void test23() {

        CountDownLatch countDownLatch = new CountDownLatch(2);
        new Thread(()->{
            log.info("ss");
            countDownLatch.countDown();
        }).start();
        new Thread(()->{
            log.info("aa");
            countDownLatch.countDown();
        }).start();
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.info("ok");

    }

    @Test
    public void test22() {
        String runMode = "A";
        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
        activityEntity.setId(81395l);
        activityEntity.setCountry("Canada");
        activityEntity.setActivityType(3);
        for (int i = 0; i < 100; i++) {
            Long rotId = rotPondService.getFreeRotByRunModeAndActivity(runMode, activityEntity);
            System.out.println(rotId);
        }
        Long rotId = rotPondService.getFreeRotByRunModeAndActivity(runMode, activityEntity);
    }

    @Test
    public void test20() {
//        List<ZnsUserEntity> pond = userDao.selectFreeRotWithoutPond(null, 10, null);
//        log.info(pond.toString());
    }
    @Test
    public void test19() {
        Integer allUserScore = userService.getAllUserScore(9913l);
        System.out.println(allUserScore);
        activityUserScoreService.useActivityUserScore(3293,1l,9913l,1);
        // 记录积分扣减详情日志
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setUserId(9913l);
        activityUserScore.setScore(3293);
        activityUserScore.setSource(-4);
        activityUserScore.setStatus(2);
        activityUserScore.setExpireTime(ZonedDateTime.now());
        activityUserScore.setExchangeTime(ZonedDateTime.now());
        activityUserScore.setUseScore(10);
        activityUserScore.setIncome(-1);
        activityUserScore.setType(4);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setAwardTime(ZonedDateTime.now());
        activityUserScore.setExtraScore(0);
        activityUserScoreService.save(activityUserScore);
        Integer score = userService.getAllUserScore(9913l);
        System.out.println(score);

    }

    @Test
    public void test18() {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime startTime = DateUtil.addDays(now, -1);
        znsRunActivityDao.selectAutomaticEnrollingOfficialTeamBatchNoIsNull(0, ZonedDateTime.now(), startTime, 0, 1, 4, null);
        znsRunActivityDao.selectAutomaticEnrollingOfficialTeamBatchNo(0, ZonedDateTime.now(), 0, 1, 4, null);

    }

    @Test
    public void test17() {
        Integer sumScore = activityUserScoreService.sumScore(14l, 12l, null, 1);
    }



    @Test
    public void test30() {
        List<RotPic> randomPics = rotPicDao.getRandomPics(100);
        System.out.println(randomPics);
    }
    @Test
    public void test29() {
        String value = sysConfigService.selectConfigByKey("robot.attention.list");
        Map<String, Object> jsonObject = JsonUtil.readValue(value);
        String available = MapUtil.getString(jsonObject.get("available"));
        List<String> rots = JsonUtil.readList(available,String.class);
        List<Long> resRots = rots.stream().map(Long::parseLong).collect(Collectors.toList());

        //Todo 158

        String key = ConfigKeyEnums.ROBOT_158_USED_INFO.getCode();
        SysConfig config = sysConfigService.selectSysConfigByKey(key);
        System.out.println(config);
        RobotUsedInfo158DTO dto = getRobotUsedInfo158DTO(config);
        resRots.addAll(dto.getUsedIds());
        resRots.addAll(dto.getIds());
        System.out.println(resRots);
    }

    private RobotUsedInfo158DTO getRobotUsedInfo158DTO(SysConfig config) {
        if (config == null) {
            return null;
        }
        if (StringUtil.isEmpty(config.getConfigValue())) {
            log.info("robotFollowUser158Task 结束，无配置数据");
            return null;
        }
        RobotUsedInfo158DTO dto = JsonUtil.readValue(config.getConfigValue(), RobotUsedInfo158DTO.class);
        List<Integer> times = dto.getTimes();

        return dto;

    }


    @Test
    public void test28() {
        String key = "k2";
        System.out.println(redisTemplate.hasKey(key));
        ListOperations listOperations = redisTemplate.opsForList();
        listOperations.rightPush(key, "123");
        redisTemplate.expire(key, 10, TimeUnit.MINUTES);
        List<Long> k1 = listOperations.range(key, 0, -1);
        System.out.println(k1);
        System.out.println(redisTemplate.hasKey(key));

    }

    @Test
    public void test27() {

//        rotPondDao.getFreeRotByRunModeAndActivity("A", 3, Arrays.asList(1l, 2l));
//        rotPondDao.getFreeRotByRunModeAndActivity("A", 3, null);

    }

    @Test
    public void test26() {
        // rotPondTask.initRotNickDB();
        // rotPondTask.initRotPondDB();
        //rotPondTask.updateRotPondNick();
        rotPondTask.initRotPicDB();

    }

    @Test
    public void test16() {

        ZnsRunActivityEntity activityEntity = activityService.findById(87781L);
        Map<String, Object> jsonObject = new HashMap<>();

        jsonObject = JsonUtil.readValue(activityEntity.getActivityConfig());
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).userStateIn( Arrays.asList(1, 3, 4)).activityId(activityEntity.getId())
                .build();


        List<ZnsRunActivityUserEntity> list = activityUserService.findList(userQuery);

        List<ZnsRunActivityUserEntity> completeUsers = new ArrayList<>();
        List<ZnsRunActivityUserEntity> noCompleteUsers = new ArrayList<>();
        for (ZnsRunActivityUserEntity userEntity : list) {
            if (userEntity.getTargetRunMileage() > 0 && userEntity.getRunMileage().intValue() >= activityEntity.getRunMileage().intValue()) {
                userEntity.setIsComplete(1);
                userEntity.setSubState(1);
                completeUsers.add(userEntity);
            } else if (userEntity.getTargetRunTime() > 0 && userEntity.getRunTime().intValue() >= activityEntity.getRunTime().intValue()) {
                userEntity.setIsComplete(1);
                userEntity.setSubState(1);
                completeUsers.add(userEntity);
            } else {
                userEntity.setSubState(2);
                noCompleteUsers.add(userEntity);
            }
        }
        //抽佣比例
        BigDecimal priceProportion = BigDecimal.ZERO;
        if (Objects.nonNull(jsonObject.get("priceProportion")) && StringUtils.hasText(jsonObject.get("priceProportion").toString())) {
            priceProportion = new BigDecimal(jsonObject.get("priceProportion").toString());
            log.info("组队跑活动={}设置佣金比例={}", activityEntity.getId(), priceProportion);
        }

        //可给用户分配的剩余比例
        BigDecimal surplus = BigDecimal.ONE.subtract(priceProportion);
        //费用参与不退回(补充：抽佣)
        BigDecimal userFees = activityEntity.getBonusRuleType() == 2 ?
                BigDecimalUtil.mul(new BigDecimal(list.size()), activityEntity.getActivityEntryFee(), surplus) :
                BigDecimal.ZERO;

        //瓜分金额
        BigDecimal avePartitionAmount = completeUsers.size() > 0 ? userFees.divide(new BigDecimal(completeUsers.size()), 2, RoundingMode.DOWN) : BigDecimal.ZERO;
        log.info("组队跑活动={},抽佣后总参与费用={},平均瓜分费用={}", activityEntity.getId(), userFees, avePartitionAmount);
    }


    @Test
    public void test14() {
        ZnsRunActivityEntity activity = activityService.findById(86674l);
        Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
        List<Map> maps = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD),Map.class);
        for (Map map : maps) {
            Integer goal = MapUtils.getInteger(map, "goal");

            System.out.println(goal + "-----");
            System.out.println(activity);
            List<ZnsRunActivityUserEntity> allActivityUser = activityUserService.findAllActivityUser(activity.getId());
            //机器人补丁，确认都是机器人
            allActivityUser = allActivityUser.stream().filter(k -> k.getIsRobot() != 0).collect(Collectors.toList());
            //确认目标 完成规则类型：1表示完成跑步里程，2表示完成跑步时长
            if (activity.getCompleteRuleType() == 1) {
                System.out.println(11);
                allActivityUser = allActivityUser.stream().filter(k -> goal.equals(k.getTargetRunMileage())).collect(Collectors.toList());
                System.out.println(allActivityUser.size());
            } else if (activity.getCompleteRuleType() == 2) {
                System.out.println(222);
                allActivityUser = allActivityUser.stream().filter(k -> goal.equals(k.getTargetRunTime())).collect(Collectors.toList());
                System.out.println(allActivityUser.size());
            }
        }

    }


    @Test
    public void test13() {
        reduceTask.run();
    }

    @Test
    public void test12() {
        List<Long> longs = rotFollowListener.chooseRobot(83381l, 2l, 4l);
        System.out.println(longs);

    }

    @Test
    public void test11() {
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .isDelete(0).activityId(1515515151L)
                .build();

        ZnsRunActivityUserEntity activityUser = activityUserService.findOne(userQuery);
        System.out.println(activityUser == null);
        List<ZnsRunActivityUserEntity> activityUserEntities = Arrays.asList(activityUser);
        System.out.println(activityUserEntities);
        System.out.println(!CollectionUtils.isEmpty(activityUserEntities));
    }

    @Test
    public void test10() {
        znsUserFriendService.add(92271l, 92128l);
    }

    @Test
    public void tsests() {

        activityUserService.getAllActivityBotUserByUserId(84l);

    }

    @Test
    public void tsest() {
        String value = sysConfigService.selectConfigByKey("robot.attention.list");
        Map<String, Object> jsonObject = JsonUtil.readValue(value);
        String available = MapUtil.getString(jsonObject.get("available"));
        List<String> rots = JsonUtil.readList(available,String.class);
        System.out.println(rots);
    }


    @Test
    public void sys() {

        SysConfig config = new SysConfig();
        config.setConfigKey("rotRandomSetting");
        Map<String, Object> json = new HashMap<>();

        DelayTimeSetting s = new DelayTimeSetting();
        s.setType("login").setMax(72 * 60 * 60 * 1000l).setMin(3 * 60 * 60 * 1000l);
        DelayTimeSetting s1 = new DelayTimeSetting();
        s1.setType("achieveMileage").setMax(0l).setMin(0l);
        DelayTimeSetting s2 = new DelayTimeSetting();
        s2.setType("firstRun").setMax(5 * 60 * 1000l).setMin(0l);
        DelayTimeSetting s3 = new DelayTimeSetting();
        s3.setType("firstReport").setMax(5 * 60 * 1000l).setMin(0l);
        DelayTimeSetting s4 = new DelayTimeSetting();
        s4.setType("rotFollowBack").setMax(72 * 60 * 60 * 1000l).setMin(0l);
        DelayTimeSetting s5 = new DelayTimeSetting();
        s5.setType("delayRotReport").setMax(4 * 60 * 60 * 1000l).setMin(30 * 60 * 1000l);

        config.setConfigValue(JsonUtil.writeString(Arrays.asList(s, s1, s2, s3, s4, s5)));
        sysConfigService.insertConfig(config);
    }

    @Test
    public void test8() {

        //发布消息 officeTeamRotReport
        DelayReportDto dto = new DelayReportDto();
        dto.setType("1234");

        rabbitTemplate.convertAndSend(delay_exchange_name_rot, "delayRotReport",
                JsonUtil.writeString(dto), message -> {
                    message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    message.getMessageProperties().setDelay(10);
                    return message;
                });
    }

    @Test
    public void test9() {

        List<Long> allActivityBotUserByUserId = activityUserService.getAllActivityBotUserByUserId(83381l);
        System.out.println(allActivityBotUserByUserId);

    }

    @Test
    public void test7() {

        String setting = sysConfigService.selectConfigByKey("rotRandomSetting");
        List<String> list = JsonUtil.readList(setting,String.class);
        System.out.println(list);
    }


    @Test
    public void testAward() {
        ZnsRunActivityEntity activityEntity = runActivityDao.selectById(82299);
        teamCompetitionActivityStrategy.handleRunActivityEnd(activityEntity);
    }


    @Test
    public void testUpdate() {
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .activityId(81756L)
                .userId(9913L).isDelete(0).taskId(243L).userState( ActivityUserStateEnum.ACCEPT.getState())
                .build();
        activityUserService.updateState(ActivityUserStateEnum.QUIT.getState(),userQuery);
    }


    @Test
    public void testcache() {
        List<AwardRelation> rewards = new ArrayList<>();
        AwardRelation relation = new AwardRelation();
        relation.setRank(1);
        relation.setHeadReward(BigDecimal.ONE);
        relation.setCouponIds(Arrays.asList(1l, 5l, 6l));
        rewards.add(relation);
        String jsonString = JsonUtil.writeString(rewards);
        redisTemplate.opsForValue().set("key1", jsonString, 1, TimeUnit.HOURS);
        Object key1 = redisTemplate.opsForValue().get("key1");
        System.out.println(key1);
        List<AwardRelation> k = JsonUtil.readList(key1,AwardRelation.class);
        System.out.println(k);
        for (AwardRelation awardRelation : k) {
            System.out.println(awardRelation.decideAwardType());
            System.out.println(awardRelation.decideAwardSentType());
        }

        Object o = redisTemplate.opsForValue().get("125");
        System.out.println(o);
    }

    @Test
    public void test21() {
        ZonedDateTime startTime = DateUtil.addDays(ZonedDateTime.now(), -1);
        List<ZnsRunActivityEntity> activityEntities = runActivityDao.
                selectActivityByActivityStateActivityStartHasRobot(
                        ActivityStateEnum.NOT_START.getState(), ZonedDateTime.now(), startTime, 0);
        System.out.println(activityEntities);

    }

    @Test
    public void jobTest1() {
        updateTeamGradeTask.updateTeamGrade();

    }

    @Test
    public void insertTest() {
        ActivityOfficialDto dto = runActivityBizService.getOfficialActivityDetail(79672l, 0);
        System.out.println(dto);

    }


    @Test
    public void redisTest() {


        UserRunDataEveryEverySecondService userRunDataEveryEverySecondService = SpringContextUtils.getBean(UserRunDataEveryEverySecondService.class);
        System.out.println(userRunDataEveryEverySecondService);

    }


    @Test
    public void keyTrst() {
        CommunityContent content = new CommunityContent();
        Long aLong = communityContentService.insertOrUpdateCommunityContent(content);
        System.out.println(aLong);
        System.out.println(content.getId());
    }


    @Test
    public void mqTest1() {
        long id = 56;
        CommunityContent content = communityContentService.findById(id);
        //0：未发布 1：已取消 2：已发布'
        if (content != null && 0 == content.getPublishStatus()) {
            content.setPublishStatus(2);
            content.setPublishRealTime(ZonedDateTime.now());
            communityContentService.update(content);
        }
    }

    @Test
    public void mqTest() {
        System.out.println(ZonedDateTime.now());
        rabbitTemplate.convertAndSend("delayed_coupon_exchange_name_test", "con", "conTesrttt", message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay(10000);
            return message;
        });
    }

    @Test
    public void jobTest() throws Exception {
        communityContentPublishTask.publish();

    }

    @Test
    public void insertCom() throws Exception {
        CommunityContent content = new CommunityContent();

    }

    @Test
    public void testTime() throws Exception {
        MovementPo po = new MovementPo();

        ZonedDateTime start = new Date(1687708800000L);
        ZonedDateTime end = new Date(1687881600000L);
        System.out.println(start);
        System.out.println(end);
        po.setStartTime(start);
        po.setEndTime(end);
        Page<MovementVo> page = movementLibraryService.list(po);
        page.getRecords().forEach(k -> {
            System.out.println(k.getCreateTime());
        });
    }


    @Test
    public void update() throws Exception {
        Movement movement = movementService.findById(35L);
        System.out.println(movement.getRemark());
        movement.setRemark("AAAAA");
        movementService.insertOrUpdateMovement(movement);
        Movement newDo = movementService.findById(35L);
        System.out.println(newDo.getRemark());
        movement.setRemark("");
        movementService.insertOrUpdateMovement(movement);
        System.out.println(movementService.findById(35L).getRemark());
    }

    @Test
    public void dataFac() throws Exception {
        CommunityContent content = new CommunityContent();
        communityContentService.insertCommunityContent(content);
    }


    @Test
    public void test6() throws Exception {
        MovementPo po = new MovementPo();
        Page<MovementVo> list = movementLibraryService.list(po);
        for (MovementVo record : list.getRecords()) {

            System.out.println(record);
        }


    }
//
//    @Test
//    public void test5() throws Exception {
//        //构造条件
//        MovementPo po = new MovementPo();
//        po.setMovementName("高抬腿");
//        po.setApplicableGender(0);
//        po.setEndTime(ZonedDateTime.now());
//        po.setMovementTypes(List.of());
//        //忽略比较的字段，可不传
//        ArrayList<String> ignores = new ArrayList<>();
//        ignores.add("applicable_gender");
//
//
//        //生成wrapper
//        QueryWrapper<Movement> wrapper = MpUtil.generateWrapper(Movement.class, po, ignores);
//        List<Movement> list = movementService.list(wrapper);
//        list.forEach(System.out::println);
//
//    }

    @Test
    public void test4() {
        MovementPo po = new MovementPo();
        List<ExerciseMuscleVo> list = movementLibraryService.muscleList();
        System.out.println(list);
    }


    @Test
    public void test3() {
        MovementPo po = new MovementPo();
        po.setMovementName("labore velit dolor fugiat");

        Page<MovementVo> page = movementLibraryService.list(po);
        System.out.println(page.getRecords());
        System.out.println(page.getTotal());
    }

    @Test
    public void test1() {
        Movement movement = new Movement();
        movement.setCover("111");

    }

//    @Test
//    public void test2() {
//        Movement movement = new Movement();
//        movement.setCover("111");
//
//
//        // movementService.getBaseMapper().insert(movement);
//        Movement movement1 = movementService.getById(1);
//        System.out.println(movement1);
//        Movement movement2 = movementService.getById(2);
//        System.out.println(movement2);
//        Movement movement3 = movementService.getById(3);
//        System.out.println(movement3);
//        movementService.deleteMovementById(1l);
//    }

    @Test
    public void test0() {
        AwardConfigAmount awardConfigAmount = new AwardConfigAmount();
        awardConfigAmount.setAwardConfigId(1000l);
        awardConfigAmount.setAmount(new BigDecimal("2.00"));
        awardConfigAmountDao.insert(awardConfigAmount);
        System.out.println("11111");
        awardConfigAmountDao.deleteById(awardConfigAmount.getId());
        System.out.println("222");
    }

    @Test
    public void test01() {
        rankActivityManager.updateMatchLineStateBySocket(userRankedMatchMapper.selectById(1L),"test");

    }


}
