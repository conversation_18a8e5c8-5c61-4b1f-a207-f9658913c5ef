package com.linzi.pitpat.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.admin.activityservice.controller.ProActivityCardController;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.dto.console.request.ProActivityCardCreateRequestDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ProActivityCardPageQueryDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ProActivityCardQueryDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.ProActivityCardRecordPageQueryDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.ProActivityCardResponseDto;
import com.linzi.pitpat.data.activityservice.dto.console.response.ProActivityCardRecordResponseDto;
import com.linzi.pitpat.data.activityservice.enums.ProActivityCardTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ProActivityCardI18n;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 资格卡控制器测试
 *
 * @since 2025年6月19日
 */
@Slf4j
public class ProActivityCardControllerTest extends PitpatAdminApplicationTests {

    @Autowired
    private ProActivityCardController proActivityCardController;

    @Test
    public void testCreate() {
        // 创建测试数据
        ProActivityCardCreateRequestDto requestDto = new ProActivityCardCreateRequestDto();
        requestDto.setYear(2025);
        requestDto.setCompetitiveType("PRO");
        requestDto.setTitle("2025年专业赛事资格卡");
        // 使用枚举值
        requestDto.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto.setDefaultLanguageCode("zh_CN");
        
        // 设置多语言信息
        List<ProActivityCardI18n> i18nInfo = new ArrayList<>();
        ProActivityCardI18n zhI18n = new ProActivityCardI18n();
        zhI18n.setLangCode("zh_CN");
        zhI18n.setLangName("中文");
        zhI18n.setTitle("2025年专业赛事资格卡");
        zhI18n.setRemark("用于参加2025年专业赛事的资格卡");
        i18nInfo.add(zhI18n);
        
        ProActivityCardI18n enI18n = new ProActivityCardI18n();
        enI18n.setLangCode("en_US");
        enI18n.setLangName("English");
        enI18n.setTitle("2025 Professional Event Qualification Card");
        enI18n.setRemark("Qualification card for participating in 2025 professional events");
        i18nInfo.add(enI18n);
        
        requestDto.setI18nInfo(i18nInfo);
        
        // 调用创建接口
        Result<Long> result = proActivityCardController.create(requestDto);
        
        log.info("创建资格卡结果：{}", JsonUtil.writeString(result));
    }

    @Test
    public void testCreateDirectPassCard() {
        // 创建直通卡测试数据
        ProActivityCardCreateRequestDto requestDto = new ProActivityCardCreateRequestDto();
        requestDto.setYear(2025);
        requestDto.setCompetitiveType("PRO");
        requestDto.setTitle("2025年专业赛事直通卡");
        // 使用直通卡枚举值
        requestDto.setCardType(ProActivityCardTypeEnum.DIRECT_PASS.getCode());
        requestDto.setDefaultLanguageCode("zh_CN");
        
        // 设置多语言信息
        List<ProActivityCardI18n> i18nInfo = new ArrayList<>();
        ProActivityCardI18n zhI18n = new ProActivityCardI18n();
        zhI18n.setLangCode("zh_CN");
        zhI18n.setLangName("中文");
        zhI18n.setTitle("2025年专业赛事直通卡");
        zhI18n.setRemark("直接获得2025年专业赛事参赛资格");
        i18nInfo.add(zhI18n);
        
        ProActivityCardI18n enI18n = new ProActivityCardI18n();
        enI18n.setLangCode("en_US");
        enI18n.setLangName("English");
        enI18n.setTitle("2025 Professional Event Direct Pass Card");
        enI18n.setRemark("Direct access to 2025 professional event");
        i18nInfo.add(enI18n);
        
        requestDto.setI18nInfo(i18nInfo);
        
        // 调用创建接口
        Result<Long> result = proActivityCardController.create(requestDto);
        
        log.info("创建直通卡结果：{}", JsonUtil.writeString(result));
    }

    @Test
    public void testGet() {
        // 创建查询条件
        ProActivityCardQueryDto queryDto = new ProActivityCardQueryDto();
        queryDto.setId(1L); // 假设ID为1
        
        // 调用查询接口
        Result<com.linzi.pitpat.data.activityservice.dto.console.response.ProActivityCardResponseDto> result = proActivityCardController.get(queryDto);
        
        log.info("查询资格卡结果：{}", JsonUtil.writeString(result));
    }

    @Test
    public void testPageQueryWithSorting() {
        // 创建分页查询条件
        ProActivityCardPageQueryDto pageQueryDto = new ProActivityCardPageQueryDto();
        pageQueryDto.setPageNum(1);
        pageQueryDto.setPageSize(10);
        pageQueryDto.setYear(2025);
        pageQueryDto.setCompetitiveType("PRO");
        
        // 调用分页查询接口
        Result<Page<ProActivityCardResponseDto>> result = proActivityCardController.findPage(pageQueryDto);
        
        log.info("分页查询结果：{}", JsonUtil.writeString(result));
        
        // 验证排序结果
        if (result.getData() != null && result.getData().getRecords() != null) {
            List<ProActivityCardResponseDto> records = result.getData().getRecords();
            log.info("分页查询返回 {} 条记录", records.size());
            
            // 验证排序：年份降序 > 赛事类型优先级排序 > 资格卡优先
            for (int i = 0; i < records.size(); i++) {
                ProActivityCardResponseDto record = records.get(i);
                log.info("第{}条记录 - 年份: {}, 赛事类型: {}, 卡类型: {}", 
                    i + 1, record.getYear(), record.getCompetitiveType(), record.getCardType());
            }
            
            // 验证排序逻辑
            if (records.size() > 1) {
                for (int i = 0; i < records.size() - 1; i++) {
                    ProActivityCardResponseDto current = records.get(i);
                    ProActivityCardResponseDto next = records.get(i + 1);
                    
                    // 验证年份降序
                    if (current.getYear() != null && next.getYear() != null) {
                        if (current.getYear() < next.getYear()) {
                            log.warn("年份排序错误：第{}条记录年份({})小于第{}条记录年份({})", 
                                i + 1, current.getYear(), i + 2, next.getYear());
                        }
                    }
                    
                    // 验证同一年份内的赛事类型优先级
                    if (current.getYear() != null && next.getYear() != null && 
                        current.getYear().equals(next.getYear())) {
                        int currentPriority = getCompetitiveTypePriority(current.getCompetitiveType());
                        int nextPriority = getCompetitiveTypePriority(next.getCompetitiveType());
                        
                        if (currentPriority > nextPriority) {
                            log.warn("同一年份内赛事类型排序错误：第{}条记录类型({})优先级({})低于第{}条记录类型({})优先级({})", 
                                i + 1, current.getCompetitiveType(), currentPriority, 
                                i + 2, next.getCompetitiveType(), nextPriority);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 获取赛事类型优先级
     * 年赛(ANNUAL) > 季赛(SEASONAL) > 月赛(MONTHLY) > 周赛(WEEK) > 娱乐赛(ENTERTAINMENT)
     */
    private int getCompetitiveTypePriority(String competitiveType) {
        if (competitiveType == null) return 999;
        
        switch (competitiveType) {
            case "ANNUAL": return 0;        // 年赛优先级最高
            case "SEASONAL": return 1;      // 季赛优先级次之
            case "MONTHLY": return 2;       // 月赛优先级再次之
            case "WEEK": return 3;          // 周赛优先级较低
            case "ENTERTAINMENT": return 4; // 娱乐赛优先级最低
            default: return 999;            // 其他类型优先级最低
        }
    }

    @Test
    public void testDuplicateCompetitiveTypeCheck() {
        // 创建第一个资格卡
        ProActivityCardCreateRequestDto requestDto1 = new ProActivityCardCreateRequestDto();
        requestDto1.setYear(2025);
        requestDto1.setCompetitiveType("ANNUAL");
        requestDto1.setTitle("2025年年赛资格卡");
        requestDto1.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto1.setDefaultLanguageCode("zh_CN");
        
        // 设置多语言信息
        List<ProActivityCardI18n> i18nInfo = new ArrayList<>();
        ProActivityCardI18n zhI18n = new ProActivityCardI18n();
        zhI18n.setLangCode("zh_CN");
        zhI18n.setLangName("中文");
        zhI18n.setTitle("2025年年赛资格卡");
        zhI18n.setRemark("2025年年赛资格卡");
        i18nInfo.add(zhI18n);
        requestDto1.setI18nInfo(i18nInfo);
        
        // 创建第一个资格卡
        Result<Long> result1 = proActivityCardController.create(requestDto1);
        log.info("创建第一个资格卡结果：{}", JsonUtil.writeString(result1));
        
        // 尝试创建相同年份、相同卡类型、相同赛事类型的资格卡（应该失败）
        ProActivityCardCreateRequestDto requestDto2 = new ProActivityCardCreateRequestDto();
        requestDto2.setYear(2025);
        requestDto2.setCompetitiveType("ANNUAL");
        requestDto2.setTitle("2025年年赛资格卡（重复）");
        requestDto2.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto2.setDefaultLanguageCode("zh_CN");
        requestDto2.setI18nInfo(i18nInfo);
        
        try {
            Result<Long> result2 = proActivityCardController.create(requestDto2);
            log.error("应该失败但成功了：{}", JsonUtil.writeString(result2));
        } catch (Exception e) {
            log.info("正确捕获到重复创建异常：{}", e.getMessage());
        }
        
        // 尝试创建相同年份、相同卡类型、不同赛事类型的资格卡（应该成功）
        ProActivityCardCreateRequestDto requestDto3 = new ProActivityCardCreateRequestDto();
        requestDto3.setYear(2025);
        requestDto3.setCompetitiveType("SEASONAL");
        requestDto3.setTitle("2025年季赛资格卡");
        requestDto3.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto3.setDefaultLanguageCode("zh_CN");
        requestDto3.setI18nInfo(i18nInfo);
        
        Result<Long> result3 = proActivityCardController.create(requestDto3);
        log.info("创建不同赛事类型资格卡结果：{}", JsonUtil.writeString(result3));
        
        // 尝试创建相同年份、不同卡类型、相同赛事类型的资格卡（应该成功）
        ProActivityCardCreateRequestDto requestDto4 = new ProActivityCardCreateRequestDto();
        requestDto4.setYear(2025);
        requestDto4.setCompetitiveType("ANNUAL");
        requestDto4.setTitle("2025年年赛直通卡");
        requestDto4.setCardType(ProActivityCardTypeEnum.DIRECT_PASS.getCode());
        requestDto4.setDefaultLanguageCode("zh_CN");
        requestDto4.setI18nInfo(i18nInfo);
        
        Result<Long> result4 = proActivityCardController.create(requestDto4);
        log.info("创建不同卡类型资格卡结果：{}", JsonUtil.writeString(result4));
    }

    @Test
    public void testTitleAutoSetFromDefaultLanguage() {
        // 创建测试数据，不设置title字段
        ProActivityCardCreateRequestDto requestDto = new ProActivityCardCreateRequestDto();
        requestDto.setYear(2025);
        requestDto.setCompetitiveType("ANNUAL");
        // 不设置title，让系统自动设置
        // requestDto.setTitle("2025年年赛资格卡");
        requestDto.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto.setDefaultLanguageCode("zh_CN");
        
        // 设置多语言信息
        List<ProActivityCardI18n> i18nInfo = new ArrayList<>();
        ProActivityCardI18n zhI18n = new ProActivityCardI18n();
        zhI18n.setLangCode("zh_CN");
        zhI18n.setLangName("中文");
        zhI18n.setTitle("2025年年赛资格卡（自动设置）");
        zhI18n.setRemark("2025年年赛资格卡");
        i18nInfo.add(zhI18n);
        
        ProActivityCardI18n enI18n = new ProActivityCardI18n();
        enI18n.setLangCode("en_US");
        enI18n.setLangName("English");
        enI18n.setTitle("2025 Annual Qualification Card (Auto Set)");
        enI18n.setRemark("2025 Annual Qualification Card");
        i18nInfo.add(enI18n);
        
        requestDto.setI18nInfo(i18nInfo);
        
        // 调用创建接口
        Result<Long> result = proActivityCardController.create(requestDto);
        log.info("创建资格卡结果：{}", JsonUtil.writeString(result));
        
        // 查询创建的记录，验证title是否正确设置
        if (result.getData() != null) {
            ProActivityCardQueryDto queryDto = new ProActivityCardQueryDto();
            queryDto.setId(result.getData());
            
            Result<com.linzi.pitpat.data.activityservice.dto.console.response.ProActivityCardResponseDto> getResult = proActivityCardController.get(queryDto);
            log.info("查询创建的资格卡：{}", JsonUtil.writeString(getResult));
            
            if (getResult.getData() != null) {
                String expectedTitle = "2025年年赛资格卡（自动设置）";
                String actualTitle = getResult.getData().getTitle();
                log.info("期望的title: {}, 实际的title: {}", expectedTitle, actualTitle);
                
                if (expectedTitle.equals(actualTitle)) {
                    log.info("✅ title自动设置功能正常");
                } else {
                    log.error("❌ title自动设置功能异常");
                }
            }
        }
    }

    @Test
    public void testDuplicateCardNameCheck() {
        // 创建第一个资格卡
        ProActivityCardCreateRequestDto requestDto1 = new ProActivityCardCreateRequestDto();
        requestDto1.setYear(2025);
        requestDto1.setCompetitiveType("ANNUAL");
        requestDto1.setTitle("2025年年赛资格卡");
        requestDto1.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto1.setDefaultLanguageCode("zh_CN");
        
        // 设置多语言信息
        List<ProActivityCardI18n> i18nInfo1 = new ArrayList<>();
        ProActivityCardI18n zhI18n1 = new ProActivityCardI18n();
        zhI18n1.setLangCode("zh_CN");
        zhI18n1.setLangName("中文");
        zhI18n1.setTitle("2025年年赛资格卡");
        zhI18n1.setRemark("2025年年赛资格卡");
        i18nInfo1.add(zhI18n1);
        
        ProActivityCardI18n enI18n1 = new ProActivityCardI18n();
        enI18n1.setLangCode("en_US");
        enI18n1.setLangName("English");
        enI18n1.setTitle("2025 Annual Qualification Card");
        enI18n1.setRemark("2025 Annual Qualification Card");
        i18nInfo1.add(enI18n1);
        requestDto1.setI18nInfo(i18nInfo1);
        
        // 创建第一个资格卡
        Result<Long> result1 = proActivityCardController.create(requestDto1);
        log.info("创建第一个资格卡结果：{}", JsonUtil.writeString(result1));
        
        // 尝试创建相同年份、相同语言、相同名称的资格卡（应该失败）
        ProActivityCardCreateRequestDto requestDto2 = new ProActivityCardCreateRequestDto();
        requestDto2.setYear(2025);
        requestDto2.setCompetitiveType("SEASONAL"); // 不同赛事类型
        requestDto2.setTitle("2025年季赛资格卡");
        requestDto2.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto2.setDefaultLanguageCode("zh_CN"); // 相同默认语言
        
        List<ProActivityCardI18n> i18nInfo2 = new ArrayList<>();
        ProActivityCardI18n zhI18n2 = new ProActivityCardI18n();
        zhI18n2.setLangCode("zh_CN");
        zhI18n2.setLangName("中文");
        zhI18n2.setTitle("2025年年赛资格卡"); // 相同中文名称
        zhI18n2.setRemark("2025年季赛资格卡");
        i18nInfo2.add(zhI18n2);
        
        ProActivityCardI18n enI18n2 = new ProActivityCardI18n();
        enI18n2.setLangCode("en_US");
        enI18n2.setLangName("English");
        enI18n2.setTitle("2025 Seasonal Qualification Card"); // 不同英文名称
        enI18n2.setRemark("2025 Seasonal Qualification Card");
        i18nInfo2.add(enI18n2);
        requestDto2.setI18nInfo(i18nInfo2);
        
        try {
            Result<Long> result2 = proActivityCardController.create(requestDto2);
            log.error("应该失败但成功了：{}", JsonUtil.writeString(result2));
        } catch (Exception e) {
            log.info("正确捕获到重复名称异常：{}", e.getMessage());
        }
        
        // 尝试创建相同年份、不同语言、相同名称的资格卡（应该失败）
        ProActivityCardCreateRequestDto requestDto3 = new ProActivityCardCreateRequestDto();
        requestDto3.setYear(2025);
        requestDto3.setCompetitiveType("MONTHLY");
        requestDto3.setTitle("2025年月赛资格卡");
        requestDto3.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto3.setDefaultLanguageCode("en_US"); // 不同默认语言
        
        List<ProActivityCardI18n> i18nInfo3 = new ArrayList<>();
        ProActivityCardI18n enI18n3 = new ProActivityCardI18n();
        enI18n3.setLangCode("en_US");
        enI18n3.setLangName("English");
        enI18n3.setTitle("2025 Annual Qualification Card"); // 相同英文名称
        enI18n3.setRemark("2025 Monthly Qualification Card");
        i18nInfo3.add(enI18n3);
        requestDto3.setI18nInfo(i18nInfo3);
        
        try {
            Result<Long> result3 = proActivityCardController.create(requestDto3);
            log.error("应该失败但成功了：{}", JsonUtil.writeString(result3));
        } catch (Exception e) {
            log.info("正确捕获到重复名称异常：{}", e.getMessage());
        }
        
        // 尝试创建不同年份、相同语言、相同名称的资格卡（应该成功）
        ProActivityCardCreateRequestDto requestDto4 = new ProActivityCardCreateRequestDto();
        requestDto4.setYear(2024); // 不同年份
        requestDto4.setCompetitiveType("ANNUAL");
        requestDto4.setTitle("2024年年赛资格卡");
        requestDto4.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto4.setDefaultLanguageCode("zh_CN"); // 相同默认语言
        
        List<ProActivityCardI18n> i18nInfo4 = new ArrayList<>();
        ProActivityCardI18n zhI18n4 = new ProActivityCardI18n();
        zhI18n4.setLangCode("zh_CN");
        zhI18n4.setLangName("中文");
        zhI18n4.setTitle("2025年年赛资格卡"); // 相同中文名称
        zhI18n4.setRemark("2024年年赛资格卡");
        i18nInfo4.add(zhI18n4);
        requestDto4.setI18nInfo(i18nInfo4);
        
        Result<Long> result4 = proActivityCardController.create(requestDto4);
        log.info("创建不同年份资格卡结果：{}", JsonUtil.writeString(result4));
        
        // 尝试创建相同年份、所有语言都不同的资格卡（应该成功）
        ProActivityCardCreateRequestDto requestDto5 = new ProActivityCardCreateRequestDto();
        requestDto5.setYear(2025);
        requestDto5.setCompetitiveType("WEEK");
        requestDto5.setTitle("2025年周赛资格卡");
        requestDto5.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto5.setDefaultLanguageCode("zh_CN");
        
        List<ProActivityCardI18n> i18nInfo5 = new ArrayList<>();
        ProActivityCardI18n zhI18n5 = new ProActivityCardI18n();
        zhI18n5.setLangCode("zh_CN");
        zhI18n5.setLangName("中文");
        zhI18n5.setTitle("2025年周赛资格卡"); // 不同中文名称
        zhI18n5.setRemark("2025年周赛资格卡");
        i18nInfo5.add(zhI18n5);
        
        ProActivityCardI18n enI18n5 = new ProActivityCardI18n();
        enI18n5.setLangCode("en_US");
        enI18n5.setLangName("English");
        enI18n5.setTitle("2025 Weekly Qualification Card"); // 不同英文名称
        enI18n5.setRemark("2025 Weekly Qualification Card");
        i18nInfo5.add(enI18n5);
        requestDto5.setI18nInfo(i18nInfo5);
        
        Result<Long> result5 = proActivityCardController.create(requestDto5);
        log.info("创建所有语言都不同的资格卡结果：{}", JsonUtil.writeString(result5));
        
        // 测试空字符串过滤功能
        ProActivityCardCreateRequestDto requestDto6 = new ProActivityCardCreateRequestDto();
        requestDto6.setYear(2025);
        requestDto6.setCompetitiveType("ENTERTAINMENT");
        requestDto6.setTitle("2025年娱乐赛资格卡");
        requestDto6.setCardType(ProActivityCardTypeEnum.QUALIFICATION.getCode());
        requestDto6.setDefaultLanguageCode("zh_CN");
        
        List<ProActivityCardI18n> i18nInfo6 = new ArrayList<>();
        ProActivityCardI18n zhI18n6 = new ProActivityCardI18n();
        zhI18n6.setLangCode("zh_CN");
        zhI18n6.setLangName("中文");
        zhI18n6.setTitle("2025年娱乐赛资格卡");
        zhI18n6.setRemark("2025年娱乐赛资格卡");
        i18nInfo6.add(zhI18n6);
        
        ProActivityCardI18n enI18n6 = new ProActivityCardI18n();
        enI18n6.setLangCode("en_US");
        enI18n6.setLangName("English");
        enI18n6.setTitle(""); // 空字符串，应该被过滤
        enI18n6.setRemark("2025 Entertainment Qualification Card");
        i18nInfo6.add(enI18n6);
        
        ProActivityCardI18n frI18n6 = new ProActivityCardI18n();
        frI18n6.setLangCode("fr_FR");
        frI18n6.setLangName("Français");
        frI18n6.setTitle("   "); // 只有空格的字符串，应该被过滤
        frI18n6.setRemark("2025 Entertainment Qualification Card");
        i18nInfo6.add(frI18n6);
        requestDto6.setI18nInfo(i18nInfo6);
        
        Result<Long> result6 = proActivityCardController.create(requestDto6);
        log.info("创建包含空字符串的资格卡结果：{}", JsonUtil.writeString(result6));
    }

    @Test
    public void testRecordPageQuery() {
        // 创建分页查询请求
        ProActivityCardRecordPageQueryDto pageQueryDto = new ProActivityCardRecordPageQueryDto();
        pageQueryDto.setPageNum(1);
        pageQueryDto.setPageSize(10);
        pageQueryDto.setUserCode("USER001");
        pageQueryDto.setYear(2025);
        pageQueryDto.setCardType("QUALIFICATION");
        pageQueryDto.setCompetitiveType("ANNUAL");
        
        // 执行分页查询
        Result<Page<ProActivityCardRecordResponseDto>> result = proActivityCardController.findRecordPage(pageQueryDto);
        
        // 验证查询结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
        
        Page<ProActivityCardRecordResponseDto> page = result.getData();
        log.info("赛事卡记录分页查询结果：总记录数={}, 当前页记录数={}", page.getTotal(), page.getRecords().size());
        
        // 验证分页信息
        assertNotNull(page.getRecords());
        assertTrue(page.getCurrent() >= 1);
        assertTrue(page.getSize() >= 0);
        assertTrue(page.getTotal() >= 0);
        
        // 验证排序规则（按创建时间倒序）
        if (page.getRecords().size() > 1) {
            for (int i = 0; i < page.getRecords().size() - 1; i++) {
                ZonedDateTime currentTime = page.getRecords().get(i).getGmtCreate();
                ZonedDateTime nextTime = page.getRecords().get(i + 1).getGmtCreate();
                if (currentTime != null && nextTime != null) {
                    assertTrue( currentTime.compareTo(nextTime) >= 0);
                }
            }
        }
        
        // 验证关联查询结果
        for (int i = 0; i < page.getRecords().size(); i++) {
            ProActivityCardRecordResponseDto record = page.getRecords().get(i);
            log.info("第{}条记录 - 用户code: {}, 用户昵称: {}, 创建时间: {}, 赛事卡标题: {}", 
                i + 1, record.getUserCode(), record.getUserNickname(), record.getGmtCreate(), record.getCardTitle());
            
            // 验证关联查询字段不为空
            if (record.getCardYear() != null) {
                log.info("关联查询到赛事卡年份：{}", record.getCardYear());
            }
            if (record.getCardTitle() != null) {
                log.info("关联查询到赛事卡标题：{}", record.getCardTitle());
            }
            if (record.getCardType() != null) {
                log.info("关联查询到赛事卡类型：{}", record.getCardType());
            }
            if (record.getCompetitiveType() != null) {
                log.info("关联查询到赛事类型：{}", record.getCompetitiveType());
            }
        }
    }

    @Test
    public void testCheckNeedPopup() {
        // 测试查询用户是否需要弹框
        String userCode = "USER001";
        
        // 这里需要注入AppProActivityCardManager，但由于这是admin模块的测试，我们需要在api模块中创建测试
        // 或者创建一个集成测试
        log.info("测试弹框功能，userCode={}", userCode);
        log.info("请在实际使用时调用 POST /app/pro/activity/card/checkPopup 接口");
        log.info("无需请求参数，用户信息从父类BaseAppController获取");
        
        // 验证逻辑：
        // 1. 查询 zns_pro_activity_card_record 表中 userCode=USER001 且 is_pop=0 的记录
        // 2. 如果有记录，返回弹框数据列表
        // 3. 如果没有记录，返回空列表
        // 4. 弹框数据按创建时间排序，时间相同则资格卡在前
        // 5. 卡标题从i18n信息中获取，优先使用中文
    }

    /**
     * 测试查询当前赛事卡信息接口
     */
    @Test
    public void testGetCurrentCards() {
        log.info("=== 测试查询当前赛事卡信息接口 ===");
        
        // 调用示例：
        // POST /app/pro/activity/card/currentCards
        // 无需请求参数，用户信息从父类BaseAppController获取
        
        log.info("接口地址: POST /app/pro/activity/card/currentCards");
        log.info("请求参数: 无（用户信息从父类获取）");
        log.info("响应格式: List<ProActivityCardInfoResponseDto>");
        log.info("功能说明: 查询当前年份的所有赛事卡信息，包括卡类型、标题、年份、赛事类型、时间线等");
        
        // 验证逻辑：
        // 1. 如果当前年份有赛事卡，返回赛事卡信息列表
        // 2. 如果当前年份没有赛事卡，返回空列表
        // 3. 每个赛事卡包含：cardId、cardType、cardTitle、year、competitiveType、startTime、endTime、gmtCreate
    }
} 