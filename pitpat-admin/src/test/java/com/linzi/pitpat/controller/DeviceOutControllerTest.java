package com.linzi.pitpat.controller;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.admin.outservice.controller.DeviceOutController;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUpgradeRequestDto;
import com.linzi.pitpat.lang.Result;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class DeviceOutControllerTest extends PitpatAdminApplicationTests {

    @Resource
    private DeviceOutController deviceOutController;

    @Test
    public void testSyncData() {
        Result<?> result = deviceOutController.syncData();
        System.out.println(JsonUtil.writeString(result));
    }

    @Test
    public void testDelUpgradeRecord() {
        DelUpgradeRequestDto req = new DelUpgradeRequestDto();
        req.setEquipmentNo("TEST_EQUIPMENT_NO");
        req.setType(256);
        
        Result<?> result = deviceOutController.delUpgradeRecord(req);
        System.out.println(JsonUtil.writeString(result));
    }
}