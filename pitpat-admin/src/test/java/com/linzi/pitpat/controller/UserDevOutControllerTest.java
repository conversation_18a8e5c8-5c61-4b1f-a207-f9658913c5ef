package com.linzi.pitpat.controller;

import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.admin.outservice.controller.UserDevOutController;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUserDevRequestDto;
import com.linzi.pitpat.lang.Result;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class UserDevOutControllerTest extends PitpatAdminApplicationTests {

    @Resource
    private UserDevOutController userDevOutController;

    @Test
    public void testDelBindRecord() {
        DelUserDevRequestDto req = new DelUserDevRequestDto();
        req.setEquipmentNo("t8gG10viRy0Gg046");
        
        Result<?> result = userDevOutController.delBindRecord(req);
        System.out.println(JsonUtil.writeString(result));
    }
}