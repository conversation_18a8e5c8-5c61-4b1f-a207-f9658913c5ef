package com.linzi.pitpat.manager;


import com.google.common.collect.Sets;
import com.linzi.pitpat.PitpatAdminApplicationTests;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.NewPersonPkPageConfigDetail;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.NewPersonPkPageConfigDetailService;
import com.linzi.pitpat.data.activityservice.service.NewPersonPkPageConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmount;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigAmountQuery;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.query.exchangeRate.ExchangeRateQuery;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.manager.ConfigPopManager;
import com.linzi.pitpat.data.userservice.dto.response.label.LabelUserMarkDto;
import com.linzi.pitpat.data.userservice.model.entity.UserRunRoute;
import com.linzi.pitpat.data.userservice.service.UserRegisterFailRecordService;
import com.linzi.pitpat.data.userservice.service.UserRunRouteService;
import com.linzi.pitpat.data.userservice.service.label.LabelManageService;
import com.linzi.pitpat.data.userservice.service.label.LabelUserService;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


class ConfigPopManagerTest extends PitpatAdminApplicationTests {

    @Resource
    private ConfigPopManager configPopManager;
    @Resource
    private LabelUserService labelUserService;
    @Resource
    private LabelManageService labelManageService;
    @Resource
    private ZnsUserAccountService znsUserAccountService;
    @Resource
    private UserRegisterFailRecordService userRegisterFailRecordService;

    @Resource
    private NewPersonPkPageConfigService newPersonPkPageConfigService;


    @Resource
    private NewPersonPkPageConfigDetailService newPersonPkPageConfigDetailService;

    @Test
    void testSql() {
        String dtoJson = "{\"labelId\":19,\"recordId\":20,\"userId\":90130}";
        String sql = "SELECT 1 FROM zns_user users WHERE users.is_delete =0 AND users.gender=1 AND users.id=19697";
        Boolean sqlResult = labelUserService.getSqlResult(JsonUtil.readValue(dtoJson, LabelUserMarkDto.class), sql, 1L, 19697L);
        System.out.println("------------- sqlResult：" + sqlResult);

    }

    @Test
    void testMonitorAutoMark() {
        userRegisterFailRecordService.saveFailUser();
    }


    @Test
    void getConfigPopById() {
//        znsUserAccountService.successPayment("paymentrWPm13zjRehoM895",DateUtil.formateDateStr(ZonedDateTime.now(),"yyyy-MM-dd'T'HH:mm:ss'Z'"));
//        NewPersonPkPageConfig newPersonPkPageConfig = new NewPersonPkPageConfig();
//        newPersonPkPageConfig.setDescription("test33");
//        int insert = newPersonPkPageConfigService.insert(newPersonPkPageConfig);
//        Long id = newPersonPkPageConfig.getId();
//        System.out.println();
//        NewPersonPkPageConfig newPersonPkPageConfig = newPersonPkPageConfigService.findById(2L);
//        newPersonPkPageConfig.setIsDelete(YesNoStatus.YES.getCode());
//        newPersonPkPageConfigService.deleteById(newPersonPkPageConfig);
//        System.out.println();

        List<NewPersonPkPageConfigDetail> detailList = new ArrayList<>();
        NewPersonPkPageConfigDetail detail1 = new NewPersonPkPageConfigDetail();
        detail1.setId(1L);
        detail1.setIllustratedText("test22");
        detailList.add(detail1);
        NewPersonPkPageConfigDetail detail2 = new NewPersonPkPageConfigDetail();
        detail2.setIllustratedText("test33");
        detailList.add(detail2);
        newPersonPkPageConfigDetailService.saveOrUpdateBatch(detailList);


    }


    @Resource
    private ActivityAwardConfigService activityAwardConfigService;

    @Resource
    private AwardConfigAmountService awardConfigAmountService;

    @Resource
    private AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;

    @Resource
    private ExchangeRateConfigService exchangeRateConfigService;

    @Resource
    private ActivityEntryFeeService activityEntryFeeService;

    @Resource
    private ZnsRunActivityService runActivityService;

    @Test
    void listConfigPop() {
        // 奖励金额
        Long activityId = 86693L;
        ExchangeRateQuery build = ExchangeRateQuery.builder().sourceCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode())
                .targetCurrencyCode(I18nConstant.CurrencyCodeEnum.CAD.getCode()).build();
        BigDecimal exchangeRate = exchangeRateConfigService.findList(build).get(0).getExchangeRate();
        List<AwardConfigDto> awardConfigDtoList = activityAwardConfigService.selectAwardConfigDtoList(activityId, null, 1);
        List<Long> awardConfigIds = awardConfigDtoList.stream().map(AwardConfigDto::getAwardConfigId).collect(Collectors.toList());
        AwardConfigAmountQuery query = AwardConfigAmountQuery.builder().awardConfigIdList(awardConfigIds).build();
        List<AwardConfigAmount> list = awardConfigAmountService.findList(query);
        for (AwardConfigAmount awardConfigAmount : list) {
            AwardConfigAmountCurrency awardConfigAmountCurrency1 = new AwardConfigAmountCurrency();
            awardConfigAmountCurrency1.setAwardAmountId(awardConfigAmount.getId());
            awardConfigAmountCurrency1.setAmount(awardConfigAmount.getAmount());
            awardConfigAmountCurrency1.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
            awardConfigAmountCurrencyDataService.insert(awardConfigAmountCurrency1);
            AwardConfigAmountCurrency awardConfigAmountCurrency2 = new AwardConfigAmountCurrency();
            awardConfigAmountCurrency2.setAwardAmountId(awardConfigAmount.getId());

            BigDecimal amountCAD = awardConfigAmount.getAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
            awardConfigAmountCurrency2.setAmount(amountCAD);
            awardConfigAmountCurrency2.setCurrencyCode(I18nConstant.CurrencyCodeEnum.CAD.getCode());
            awardConfigAmountCurrencyDataService.insert(awardConfigAmountCurrency2);
        }

        // 入参费用
        ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
        BigDecimal activityEntryFeeCAD = runActivity.getActivityEntryFee().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
        ActivityEntryFee activityEntryFee = new ActivityEntryFee();
        activityEntryFee.setActivityId(activityId);
        activityEntryFee.setEntryFee(runActivity.getActivityEntryFee());
        activityEntryFee.setCurrencyCode("USD");
        activityEntryFee.setCurrencySymbol("$");
        activityEntryFeeService.insert(activityEntryFee);

        ActivityEntryFee activityEntryFee1 = new ActivityEntryFee();
        activityEntryFee1.setActivityId(activityId);
        activityEntryFee1.setEntryFee(activityEntryFeeCAD);
        activityEntryFee1.setCurrencyCode("CAD");
        activityEntryFee1.setCurrencySymbol("C$");
        activityEntryFeeService.insert(activityEntryFee1);


    }

    @Resource
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Resource
    private UserRunRouteService userRunRouteService;

    @Test
    void testMigrate() {

        Long id = 23469786L;
        int limit = 5;
        List<ZnsUserRunDataDetailsEntity> runDataDetailsList = new ArrayList<>();
        while (true) {
            runDataDetailsList = znsUserRunDataDetailsService.listByIdLimit(id, limit);
            if (CollectionUtils.isEmpty(runDataDetailsList)) {
                return;
            }
            Set<String> runDataRouteList = runDataDetailsList.stream().map(e -> e.getUserId() + "_" + e.getRouteId()).collect(Collectors.toSet());
            List<UserRunRoute> oldList = userRunRouteService.findListByUserRoute(runDataRouteList);
            Set<String> runRouteList = oldList.stream().map(UserRunRoute::getUserRouteStr).collect(Collectors.toSet());
            Set<String> difference = Sets.difference(runDataRouteList, runRouteList);

            List<UserRunRoute> collect = difference.stream().map(e -> {
                UserRunRoute userRunRoute = new UserRunRoute();
                userRunRoute.setUserRouteStr(e);
                return userRunRoute;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                userRunRouteService.batchInsert(collect);
            }
            id = runDataDetailsList.get(runDataDetailsList.size() - 1).getId();
        }


    }


}
