package com.linzi.pitpat;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.activityservice.biz.RankActivityBizService;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.RankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.api.RankActivityManager;
import com.linzi.pitpat.data.activityservice.mapper.ZnsUserRunDataDetailsDao;
import com.linzi.pitpat.data.activityservice.quartz.PolymerizationActTask;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.strategy.ChallengeRunActivityStrategy;
import com.linzi.pitpat.data.systemservice.service.PopRegionService;
import com.linzi.pitpat.data.userservice.biz.LabelBizService;
import com.linzi.pitpat.data.userservice.dto.response.label.LabelUserMarkDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.LabelUserService;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;

@Slf4j
@SpringBootTest(classes = PitpatAdminApplication.class)
public class ZhengLiTest {

    @Resource
    private RankedActivityResultManager rankedActivityResultManager;
    @Resource
    private ZnsUserService znsUserService;
    @Resource
    private ActivityResultManager activityResultManager;
    @Resource
    private MainActivityService mainActivityService;
    @Resource
    private EntryGameplayService entryGameplayService;
    @Resource
    private ChallengeRunActivityStrategy challengeRunActivityStrategy;
    @Resource
    private ZnsRunActivityService znsRunActivityService;
    @Resource
    private PopRegionService popRegionService;
    @Resource
    private RankActivityManager rankedActivityManager;
    @Resource
    private PolymerizationActTask polymerizationActTask;
    @Resource
    private PolymerizationActivityPoleService polymerizationActivityPoleService;
    @Resource
    private LabelUserService labelUserService;
    @Resource
    private ZnsUserRunDataDetailsDao userRunDataDetailsDao;
    @Resource
    private RankActivityBizService rankActivityBizService;
    @Resource
    private LabelBizService labelBizService;
    /**
     * 自动打标测试
     */
    @Test
    public void test8() {
        ZonedDateTime day7 = DateUtil.addDays(DateUtil.startOfDate(ZonedDateTime.now()), -7);
//        userRunDataDetailsDao.selectMinRunTimeByUserIdDistanceTargetRunMileageMinRunTime(336533L, Constants.target_1_Mile, Constants.target_1_Mile, day7,1,null, deviceType);
    }


    /**
     * 自动打标测试
     */
    @Test
    public void test7() {
        String traceId = String.format("%s_%s_%s", SpringContextUtils.getApplicationName(), OrderUtil.host, MDC.get("traceId"));
        LabelUserMarkDto labelUserMarkDto = new LabelUserMarkDto();
        labelUserMarkDto.setUserId(83118L);
        labelUserMarkDto.setLabelId(289L);
        labelUserMarkDto.setRecordId(4129L);
        labelBizService.autoMark(labelUserMarkDto);
    }

    /**
     * 段位赛机器人匹配测试
     */
    @Test
    public void test5() {
        rankedActivityManager.dealPutInRobot(665L);
    }


    /**
     * banner 区域过滤查询
     */
    @Test
    public void test4() {
        popRegionService.findAreaMap(4, List.of(1L,2L),"HZ","US");
    }



    /**
     * 查找机器人测试
     */
    @Test
    public void test2() {
        ZnsUserEntity shortageRobotUser = znsUserService.getShortageRobotUser(false, "IT");
        System.out.println(shortageRobotUser);
    }

    /**
     * 获取排位赛排名
     */
    @Test
    public void test1() {
//        Integer rank = rankActivityBizService.getRankedActivityRank(new RankedUserQuery(286880L, 95071L));
//        System.out.println(rank);
    }

}
