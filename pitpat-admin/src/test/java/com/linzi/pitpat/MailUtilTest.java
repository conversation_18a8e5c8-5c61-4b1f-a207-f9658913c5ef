package com.linzi.pitpat;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.messageservice.util.MailUtils;
import com.linzi.pitpat.data.systemservice.model.entity.EmailConfig;
import com.linzi.pitpat.data.systemservice.service.EmailConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.query.EmailSendingRecordQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;

public class MailUtilTest  extends PitpatAdminApplicationTests{

    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private EmailConfigService emailConfigService;
    @Autowired
    private ZnsUserEmailSendingRecordService znsUserEmailSendingRecordService;
    @Test
    public void sendEmail() throws Exception {
//        mailUtils.sendMail(email, emailConfig, emailTemplate.getContent(), emailTemplate.getTitle(),emailSend);
        mailUtils.sendMail("<EMAIL>", "ttttttttt", "title", new ZnsUserEmailSendingRecordEntity());
    }

    @Test
    public void sendEmailWithTemplate() throws Exception {
        EmailConfig emailConfig = emailConfigService.findById(8L);
        ZnsUserEmailSendingRecordEntity recordEntity = znsUserEmailSendingRecordService.finByQuery(EmailSendingRecordQuery.builder().id(1L).build());

//        MailDto mailDto = new MailDto("<EMAIL>", "emailTemplate.getContent()", "emailTemplate.getTitle()", new ZnsUserEmailSendingRecordEntity(),emailConfig);
        mailUtils.sendMail("<EMAIL>", emailConfig,"content666", "title", recordEntity);
    }

}
