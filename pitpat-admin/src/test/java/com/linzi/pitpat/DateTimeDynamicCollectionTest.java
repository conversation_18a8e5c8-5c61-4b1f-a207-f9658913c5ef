package com.linzi.pitpat;

import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecond;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.mongo.DateTimeDynamicCollection;
import com.linzi.pitpat.data.mongo.DynamicCollection;
import com.linzi.pitpat.data.mongo.Id.SnowflakeId;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;


@Slf4j
public class DateTimeDynamicCollectionTest extends PitpatAdminApplicationTests {

    @Resource
    private MongoTemplate mongoTemplate;

    @Autowired
    private UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    @Resource
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Autowired
    @Qualifier("dateDynamicCollection")
    private DynamicCollection<ZonedDateTime> dynamicCollection;

    @Test
    public void printWeek() {
        DateTimeDynamicCollection dateTimeDynamicCollection = new DateTimeDynamicCollection();
        dates().forEach(i -> {
            //正确返回当前日期所属的第几周
            System.out.println(i + "=>" + dateTimeDynamicCollection.getTargetCollectionName(MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_SECOND, StringToDate(String.format(i))));
        });
    }

    @Test
    public void testMongoTest() {
        Long runDataDetailId = 23082656L;
        int runTime = 10;
        ZnsUserRunDataDetailsEntity runDataDetail = znsUserRunDataDetailsService.findById(runDataDetailId);
        String collectionName = dynamicCollection.getTargetCollectionName(MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_SECOND, runDataDetail.getCreateTime());

        Query query = Query.query(Criteria.where("runDataDetailsId").is(runDataDetailId).and("runTime").is(runTime).and("id").is(169094397114257408L));//.and("runTime").is(runTime).and("isDelete").is(0)
        List<ZnsUserRunDataDetailsSecond> list = mongoTemplate.find(query, ZnsUserRunDataDetailsSecond.class, collectionName);
        log.info("{}", list);


    }

    @Test
    public void testQueryFromMinutes() {
        ZnsUserRunDataDetailsSecondEntity lastSecond = userRunDataDetailsSecondService.getLastSecond(23082656L);
        List<ZnsUserRunDataDetailsSecondEntity> lastSeconds = userRunDataDetailsSecondService.getSecondsList(23082656L);
        List<ZnsUserRunDataDetailsSecondEntity> lastMinute = userRunDataDetailsSecondService.getLastMinute(23082656L);
        Assertions.assertNotNull(lastSecond);
        Assertions.assertNotNull(lastSeconds);
        Assertions.assertNotNull(lastMinute);
    }

    @Test
    public void testFindOne() {
        ZnsUserRunDataDetailsSecondEntity lastSecond = userRunDataDetailsSecondService.getLastSecond(23129609L);
        ZnsUserRunDataDetailsSecondEntity lastSecond1 = userRunDataDetailsSecondService.getLastSecond(23113910L);

        ZnsUserRunDataDetailsEntity runDataDetail = znsUserRunDataDetailsService.findById(lastSecond.getRunDataDetailsId());
        String collectionName = dynamicCollection.getTargetCollectionName(MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_SECOND, runDataDetail.getCreateTime());

        Update update = new Update().set("isDelete", 0).set("createTime", ZonedDateTime.now()).set("gradient", 0).set("calories", BigDecimal.ZERO).set("runTimeMillisecond", 0).set("noLiveLoad", 0).set("sensorStatus", 0).set("sensorMax", 0).set("sensorTrends", 0).set("sensorSteps", 0).set("realElectricity", 0).set("realRotate", 0);

        // 必须要指定 Document: ZnsUserRunDataDetailsSecond, 否则 id 无法正确映射 { "_id" : 169609229689221120}
        mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(lastSecond.getId())), update, ZnsUserRunDataDetailsSecond.class, collectionName);
    }

    @Test
    public void testFindOneMinute() {
        List<ZnsUserRunDataDetailsSecondEntity> lastMinute = userRunDataDetailsSecondService.getLastMinute(23129609L);
        List<ZnsUserRunDataDetailsSecondEntity> lastMinute1 = userRunDataDetailsSecondService.getLastMinute(23113910L);
        log.info("lastMinute:{}", lastMinute);
        log.info("lastMinute1:{}", lastMinute1);

    }

    @Test
    public void testInsertToMongodb() {
        String str = getString();

        List<ZnsUserRunDataDetailsSecondEntity> list = JsonUtil.readList(str, ZnsUserRunDataDetailsSecondEntity.class);
        list.forEach(System.out::println);

        List<ZnsUserRunDataDetailsSecondEntity> list1 = list.stream().map(item -> {
            item.setRunDataDetailsId(23082656L); //1450453
            item.setIsDelete(0);
            return item;
        }).toList();
        userRunDataDetailsSecondService.saveToMongodb(list1, 23082656L, 1);

        list1 = list.stream().map(item -> {
            item.setRunDataDetailsId(23106815L);
            item.setIsDelete(0);
            return item;
        }).toList();
        userRunDataDetailsSecondService.saveToMongodb(list1, 23106815L, 1);


    }

    private static String getString() {
        String str = """
                [{"pace":3600,"velocity":"1.00","stepNum":0,"heartRate":0,"stepFrequency":0,"calories":"0","runTime":2,"gradient":0,"mileage":"0"}]
                """;

        List<Map<String, Object>> list = JsonUtil.readValue(str, new TypeReference<List<Map<String, Object>>>() {
        });
        list.forEach(item -> item.keySet().forEach(key -> {
            log.info("key={}, value={}", key, item.get(key));
        }));
        return str;
    }

    @Test
    public void testWeek() {
        LocalDate startDate1 = LocalDate.of(2022, 1, 1); // 开始日期
        LocalDate startDate2 = LocalDate.of(2023, 1, 1); // 开始日期

        LocalDate middleDate1 = LocalDate.of(2022, 12, 22); // 中间日期
        LocalDate middleDate2 = LocalDate.of(2023, 12, 25); // 中间日期

        LocalDate endDate1 = LocalDate.of(2022, 12, 31); // 结束日期
        LocalDate endDate2 = LocalDate.of(2023, 12, 31); // 结束日期

        WeekFields weekFields = WeekFields.of(Locale.CHINA);

        Assertions.assertEquals(1, startDate1.get(weekFields.weekOfYear()));
        Assertions.assertEquals(1, startDate2.get(weekFields.weekOfYear()));

        Assertions.assertEquals(52, middleDate1.get(weekFields.weekOfYear()));
        Assertions.assertEquals(52, middleDate2.get(weekFields.weekOfYear()));

        Assertions.assertEquals(53, endDate1.get(weekFields.weekOfYear()));
        Assertions.assertEquals(53, endDate2.get(weekFields.weekOfYear()));
    }


    public ZonedDateTime StringToDate(String dateStr) {
//        DateTimeFormatter formatter  = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd"); // 定义你的日期格式
        try {
            return formatter.parse(dateStr); // 将字符串转换为日期
        } catch (ParseException e) {
            e.printStackTrace(); // 打印解析异常信息
        }
        return null;
    }

    public List<String> dates() {
        LocalDate startDate = LocalDate.of(2022, 1, 1); // 开始日期
        LocalDate endDate = LocalDate.of(2023, 12, 31); // 结束日期
        List<LocalDate> allDates = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        while (!startDate.isAfter(endDate)) {
            allDates.add(startDate);
            startDate = startDate.plusDays(1); // 将日期向前移动一天
        }

        return allDates.stream().map(date -> formatter.format(date)).toList();
    }

    public static void main(String[] args) throws InterruptedException {
//        Set<Object> objects = new HashSet<>();
//        List<Object> objects = new ArrayList<>();
        final Set<Object> objects = new ConcurrentSkipListSet<>();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        ConcurrentHashMap<Long, Integer> objectObjectConcurrentHashMap = new ConcurrentHashMap<>();
        Runnable runnable = () -> {
            IntStream.range(0, 500000).mapToLong(i -> SnowflakeId.getId()).forEach(id -> {
                objects.add(id);
                atomicInteger.incrementAndGet();
                log.info("{}", id);
            });
        };

        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();
        new Thread(runnable).start();

        log.info("{}", "生成结束");
        TimeUnit.SECONDS.sleep(50L);
        log.info("{}-{}", atomicInteger.get(), objects.size());


    }

    @Test
    public void testId() {
        SnowflakeId.getId();
    }
}
