package com.linzi.pitpat.util;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Locale;
import java.util.Map;
import java.util.stream.IntStream;


@Slf4j
public class NumberUtilTest {

    @Test
    public void testRandomConfig() {
        log.info("{}- {} - {}", ZoneId.systemDefault(), ZoneId.getAvailableZoneIds(), ZonedDateTime.ofInstant(Instant.now(), ZoneOffset.UTC));

        //随机值的范围在5.5-12之间
        IntStream.range(0, 12)
                .mapToObj(i -> NumberUtils.getConfigRandom("5,12,0.5"))
                .forEach(random -> {
                    log.info("random value is : {}", random);
                });
    }


    @Test
    public void testBigDecimalDown() {
        BigDecimal sum = new BigDecimal(3);
        BigDecimal total = new BigDecimal(7);
        BigDecimal average = sum.divide(total, 2, RoundingMode.DOWN);
        BigDecimal average1 = sum.divide(total, 2, RoundingMode.HALF_DOWN);
        Assertions.assertEquals(average, new BigDecimal("0.42")); // 抹去多余尾数
        Assertions.assertEquals(average1, new BigDecimal("0.43")); //四舍五入
        log.info("{}", average);


    }

    @Test
    public void testJsonObject() {
        Map<String, Object> jsonObject = JsonUtil.readValue("{\"secondPrize\":2,\"dRate\":0,\"completeAward\":\"1.4\",\"backgroundImage\":\"https://tkjh5.ldxinyong.com/admin/profile/upload/2022/01/12/78feee06-bda6-4473-a52f-0f83178635ca.jpg\",\"sRate\":0,\"baseExtraAward\":0.05,\"completeAwardPerMiles\":1,\"miles\":\"0.5,0.8125,1,3,5,7,10,16\",\"advertisingImage\":\"https://pitpat-oss.s3.us-east-2.amazonaws.com/202212/iIM10OJOdj5D0544.png\",\"activityTitle\":\"call XXX to win the bonus\",\"firstPrize\":3,\"coverImage\":\"https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iGg10FSR891L9185.png\",\"activityEquipmentConfigs\":[{\"equipmentName\":\"Treadmill1\",\"subType\":0,\"type\":2,\"equipmentInfo\":\"AS01\",\"equipmentType\":1},{\"equipmentName\":\"阿鲁克跑步机\",\"subType\":0,\"type\":2,\"equipmentInfo\":\"AS02\",\"equipmentType\":1},{\"equipmentName\":\"Treadmill8\",\"subType\":0,\"type\":2,\"equipmentInfo\":\"BA02\",\"equipmentType\":2},{\"equipmentName\":\"CT04(Walking Model)\",\"subType\":1,\"type\":2,\"equipmentInfo\":\"CT04\",\"equipmentType\":3},{\"equipmentName\":\"CT04(Running Model)\",\"subType\":2,\"type\":2,\"equipmentInfo\":\"CT04\",\"equipmentType\":3},{\"equipmentName\":\"CT05(Walking Model)\",\"subType\":1,\"type\":2,\"equipmentInfo\":\"CT05\",\"equipmentType\":3},{\"equipmentName\":\"CT05(Running Model)\",\"subType\":2,\"type\":2,\"equipmentInfo\":\"CT05\",\"equipmentType\":3}],\"runTime\":\"5,10,15,20,30,40,50,60,90,120\",\"thirdPrize\":1,\"aRate\":30,\"mileList\":\"0.5,0.8125,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26\",\"winnerAward\":\"2.4\",\"mileage\":\"1,3,5,7,10,16\",\"mileageList\":\"1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42\",\"participateAward\":\"0\",\"thirdAwardRate\":20,\"margin\":\"1,3,5,48\",\"maxExtraAwardLimit\":10,\"robotAcceptFeeInvite\":0,\"bRate\":60,\"completeAwardPerKm\":0.62,\"teamRunLastEnter\":30,\"activityRequire\":\"Participants should be in a good health condition and behavior properly\",\"secondAwardRate\":30,\"firstAwardRate\":50,\"cRate\":10,\"maxAwardLimit\":10,\"activityIntroduce\":\"You can invite any registered users to Team Run, and all participants will be running on the same track.\",\"activityRule\":\"1. If no one accepts the invitation before the start time, the event will be automatically cancelled.\\n2. If users are late more than 30 minutes, users will not be able to participate in the game anymore. No reward will be granted and the deposit will not be returned.\\n3. The race needs to be completed at one time. Please do not quit halfway.\",\"runBeforeEnter\":5}\t");
        log.info("{}-{}", jsonObject, jsonObject.get("secondPrize"));

        BigDecimal bigDecimal = BigDecimal.valueOf(Double.parseDouble("0.1"));
        BigDecimal bigDecimal1 = new BigDecimal("0.1");
        log.info("{} - {}", bigDecimal1, bigDecimal1);
        Assertions.assertEquals(bigDecimal1, bigDecimal);
    }

    @Test
    public void testaa() {
        DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
        System.out.println(formatter.format(ZonedDateTime.now()));
    }

}
