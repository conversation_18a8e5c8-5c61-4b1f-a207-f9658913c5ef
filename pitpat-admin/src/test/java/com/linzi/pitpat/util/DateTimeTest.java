package com.linzi.pitpat.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@Slf4j
public class DateTimeTest {

    @Test
    public void testZoneId() {
        log.info("{}", ZonedDateTime.now().withZoneSameInstant(ZoneOffset.UTC));
        log.info("{}", ZonedDateTime.now().withZoneSameInstant(ZoneId.of("UTC+8")));
        log.info("{}", ZonedDateTime.now().withZoneSameInstant(ZoneId.of("GMT+8")));
        ZonedDateTime dateTime = ZonedDateTime.now().withZoneSameInstant(ZoneId.of("GMT+8"));
        ZonedDateTime zonedDateTime = ZonedDateTime.now().withZoneSameInstant(ZoneOffset.UTC);

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //.withZone(ZoneId.of("GMT+8"));
        log.info("{}", dateTimeFormatter.format(zonedDateTime));
        log.info("{}", dateTimeFormatter.format(dateTime));

        //log.info("{}", ZoneId.getAvailableZoneIds());
    }

    @Test
    public void testaa() throws ParseException {
        DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.SHORT, DateFormat.SHORT, Locale.SIMPLIFIED_CHINESE);
        log.info(formatter.format(ZonedDateTime.now()));

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        ZonedDateTime date1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-09-21 00:00:00");

        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(date1.toInstant(), ZoneId.of("Asia/Shanghai"));
        String format = dateTimeFormatter.format(zonedDateTime);

        log.info(format);
    }
}
