package com.linzi.pitpat.util;

import com.linzi.pitpat.admin.model.MovementVo;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.awardservice.model.vo.AwardRelation;
import com.linzi.pitpat.data.movementservice.model.entity.Movement;
import com.linzi.pitpat.data.robotservice.model.entity.RotNick;
import com.linzi.pitpat.data.robotservice.model.entity.RotPondAdd;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.web.util.HtmlUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class JsonTest {
    @Test
    public void testUtf() throws Exception{

        String parameter = "Pacôme";

            String encodedParameter = URLEncoder.encode(parameter, "utf-8");
        byte[] bytes1 = parameter.getBytes();
        String s1 = new String(bytes1, StandardCharsets.UTF_8);

        //Pacôme
        String name = "Pacôme";
        String urlnew = null;
        byte[] bytes = name.getBytes(StandardCharsets.UTF_8);
        String s = new String(bytes, StandardCharsets.UTF_8);
        String getUrl = null;
        try {
//            getUrl = "http://127.0.0.1:8080/hello/test1" + "?french="+encodedParameter;
            getUrl = "http://127.0.0.1:8080/hello/test1" ;
             //urlnew = URLEncoder.encode(parameter, "utf-8");
//            getUrl = URLEncoder.encode(getUrl,"UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Map<String, Object> jsonObject = new HashMap<>();
        jsonObject.put("test","Pacôme");
        System.out.println(jsonObject);
        //String result = HttpUtil.doGet(getUrl,1000);
        String response = RestTemplateUtil.get(getUrl);
        System.out.println(response);
    }

    @Test
    public void parseHtml(){
        String content = "<li>eden frederick</li><li>bard francis</li>";
        String s = HtmlUtils.htmlEscape(content);
        System.out.println(s);
        String replace = content.replace("</li><li>", " ");
        String[] s1 = replace.split(" ");
        for (String s2 : s1) {
            System.out.println(s2);
        }

        String part = "(<li>){1}[a-z]*\\s*[a-z]*(</li>){1}";
        // 创建模式对象
        Pattern p = Pattern.compile(part);

        // 创建匹配器对象
        Matcher m = p.matcher(content);
        boolean b = m.find();
        String group = m.group();
        System.out.println(group);
        String group1 = m.group();
        System.out.println(group1);
//
        System.out.println(b);

    }

    @Test
    public void distinct(){
        ArrayList<RotNick> list = new ArrayList<>();
        for (int i = 0; i < 100000; i++) {
            RotNick rotNick = new RotNick();
            Random random = new Random();
            rotNick.setUseTime(random.nextInt(100));
            rotNick.setFirstName("hik");
            list.add(rotNick);
        }
        System.out.println(list.size());
        ArrayList<RotNick> collect = list.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RotNick::getFirstName))),
                        ArrayList::new));

        System.out.println(collect.size());


    }

    @Test
    public void parTest() {
        List<RotPondAdd> rotPondAdds = Arrays.asList(
                new RotPondAdd().setFemaleNum(10).setMaleNum(20).setMode("A5"),
                new RotPondAdd().setMaleNum(0).setMode("A6")

        );
        System.out.println(rotPondAdds);
    }

    @Test
    public void strSqlTest() {
        Long id =23l;
        String sql = "\'{\"id\":" +id + "}\'";
        Map<String, Object> json = new HashMap<>();
        json.put("id",id);
        System.out.println(json);

        System.out.println(sql);
       // SELECT * FROM zns_review_goods WHERE JSON_CONTAINS(goods_spec,'{"id":23}')
    }
    @Test
    public void moneyTest() {
        //计算奖池
        BigDecimal currentNum = BigDecimal.valueOf(2);
        BigDecimal baseReward = BigDecimalUtil.valueOf("2.6");
        BigDecimal headReward = BigDecimalUtil.valueOf("0.15");
        BigDecimal totalAmount = BigDecimalUtil.multiply(headReward, currentNum).add(baseReward);
        BigDecimal A1 = BigDecimalUtil.valueOf("379");
        BigDecimal A2 = BigDecimalUtil.valueOf("22");
        BigDecimal totalMillage = BigDecimalUtil.valueOf("401");

        BigDecimal score = BigDecimal.ZERO;
        //为0不计算，避免0值异常
        if (!BigDecimal.ZERO.equals(totalMillage)) {
            score = A1.divide(totalMillage,8,RoundingMode.HALF_UP);
            BigDecimal awardAmount = score.multiply(totalAmount).setScale(2,BigDecimal.ROUND_DOWN);
            System.out.println(awardAmount);
        }
        if (!BigDecimal.ZERO.equals(totalMillage)) {
            score = A2.divide(totalMillage, 8, RoundingMode.HALF_UP);
            BigDecimal awardAmount = score.multiply(totalAmount).setScale(2,BigDecimal.ROUND_DOWN);
            System.out.println(awardAmount);
        }
        //最终奖励
    }

    @Test
    public void list() {
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("12");
        System.out.println(list);
        list = new LinkedList<>();
        System.out.println(list);
    }



    @Test
    public void de() {
        BigDecimal a = BigDecimal.valueOf(22);
        BigDecimal q = BigDecimal.valueOf(3);
       // System.out.println(q.divide(a));
        System.out.println(q.divide(a,2, RoundingMode.HALF_UP));
    }

    @Test
    public void sort() {
        ArrayList<Object> list = new ArrayList<>();
        list.add(1);
        list.add("223");
        System.out.println(list);
        Collections.sort(list,(Comparator) (o1, o2) -> -1);
        System.out.println(list);
        Collections.sort(list,(Comparator) (o1, o2) -> -1);
        System.out.println(list);

    }
    @Test
    public void compain() {
        AwardRelation s = new AwardRelation();
        s.setRank(1);
        AwardRelation t = new AwardRelation();
        t.setScore(2);
    BeanUtil.copyPropertiesIgnoreNull(s,t);
        System.out.println(s);
        System.out.println(t);

    }
    @Test
    public void date() {

        LocalDate now = LocalDate.now();
        System.out.println(now);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(ZonedDateTime.now());//设置起时间
        calendar.add(Calendar.YEAR, 1);//增加一年
//calendar.add(Calendar.DATE, 1);//增加1天
//calendar.add(Calendar.DATE, -1);//减1天
//calendar.add(Calendar.MONTH, 1);//增加一个月
        System.out.println("输出::" + calendar.toInstant().toEpochMilli());

    }

    @Test
    public void test1() {

        ArrayList<String> list = new ArrayList<>();
        list.add("a");
        list.add("s");
        System.out.println(list);
        String[] strs = {"a", "c"};
        System.out.println(strs[1]);
        String listSTR = JsonUtil.writeString(list);
        String arrStr = JsonUtil.writeString(strs);
        System.out.println(listSTR);
        System.out.println(arrStr);

        List<String> strings = JsonUtil.readList(listSTR,String.class);
        System.out.println(strings);


    }

    @Test
    public void copy() {

        MovementVo vo = new MovementVo();
        vo.setCover("222");
        vo.setApplicableGender(1);

        Movement movement = new Movement();
        movement.setId(11);
        movement.setGmtCreate(ZonedDateTime.now());
        BeanUtil.copyPropertiesIgnoreNull(vo, movement);

        System.out.println(movement);


    }

    @Test
    public void testPost(){
        HashMap<String, String> map = new HashMap<>();
        map.put("name","Pacôme");
        try {
            //HttpUtil.doPost JsonUtil.writeString(map)
            String post = post("http://127.0.0.1:8080/hello/test1", map);
            System.out.println(post);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    private String post(String purl,Map map) throws Exception{
        URL url = new URL(purl);
        String res= null;
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setDoOutput(true);

        String jsonInputString = JsonUtil.writeString(map);

        try(OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonInputString.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        try(BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String responseLine = null;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            res = response.toString();
            System.out.println(response.toString());
        }
        return res;
    }

}
