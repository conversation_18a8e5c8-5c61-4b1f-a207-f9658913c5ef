package com.linzi.pitpat.admin.service.music.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.exception.AdminCommonError;
import com.linzi.pitpat.admin.model.RunMusicVo;
import com.linzi.pitpat.admin.model.RunPlaylistVo;
import com.linzi.pitpat.admin.service.music.RunPlaylistManager;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPlaylistRel;
import com.linzi.pitpat.data.activityservice.model.entity.PlaylistMusicRel;
import com.linzi.pitpat.data.activityservice.model.entity.RunMusic;
import com.linzi.pitpat.data.activityservice.model.entity.RunPlaylist;
import com.linzi.pitpat.data.activityservice.model.query.ActivityPlaylistRelQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunPlaylistPageQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.PlaylistMusicRelService;
import com.linzi.pitpat.data.activityservice.service.RunMusicService;
import com.linzi.pitpat.data.activityservice.service.RunPlaylistService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.MusicUsageStateEnum;
import com.linzi.pitpat.data.enums.PlaylistUsageStateEnum;
import com.linzi.pitpat.data.equipmentservice.model.query.RunMusicQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.RunPlaylistQuery;
import com.linzi.pitpat.data.request.music.DeletePlaylistReq;
import com.linzi.pitpat.data.request.music.PlaylistUsageStateReq;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RunPlaylistManagerImpl implements RunPlaylistManager {

    @Resource
    private RunPlaylistService runPlaylistService;

    @Resource
    private RunMusicService runMusicService;

    @Resource
    private PlaylistMusicRelService playlistMusicRelService;

    @Resource
    private ActivityPlaylistRelService activityPlaylistRelService;

    @Resource
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private RedisUtil redisUtil;


    @Override
    public Page<RunPlaylistVo> listPlaylist(RunPlaylistQuery queryPo) {
        RunPlaylistPageQuery build = RunPlaylistPageQuery.builder()
                .isDelete(0).usageState(queryPo.getUsageState())
                .titleSearchKey(queryPo.getPlaylistTitle())
                .build();
        if (Objects.nonNull(queryPo.getStepRateOrder())) {
            if (queryPo.getStepRateOrder() == 0) {
                build.addOrderByDesc("step_rate");
            } else {
                build.addOrderByAsc("step_rate");
            }
        }
        build.addOrderByDesc("id");
        build.setPageNum(queryPo.getPageNum());
        build.setPageSize(queryPo.getPageSize());
        Page<RunPlaylist> page = runPlaylistService.findPage(build);

        Page<RunPlaylistVo> pageVo = new Page<>(queryPo.getPageNum(), queryPo.getPageSize());
        pageVo.setTotal(page.getTotal());
        List<RunPlaylistVo> records = BeanUtil.copyBeanList(page.getRecords(), RunPlaylistVo.class);
        if (!CollectionUtils.isEmpty(records)) {
            records.stream().map(e -> {
                List<RunMusic> musicList = getMusicList(e.getId(), MusicUsageStateEnum.PUBLISHED.getCode());
                e.setMusicNum(musicList.size());
                return e;
            }).collect(Collectors.toList());
        }
        pageVo.setRecords(records);
        return pageVo;
    }


    /**
     * 查询歌单下的歌曲
     *
     * @param playlistId
     * @param usageState
     * @return
     */
    private List<RunMusic> getMusicList(Long playlistId, Integer usageState) {
        List<RunMusic> musicList = new ArrayList<>();
        List<PlaylistMusicRel> list = playlistMusicRelService.findListByPlaylistId(List.of(playlistId));
        if (!CollectionUtils.isEmpty(list)) {
            RunMusicQuery query = new RunMusicQuery();
            query.setSelect(List.of(RunMusic::getId, RunMusic::getTitle, RunMusic::getStepRate, RunMusic::getUsageState));
            query.setIsDelete(0);
            query.setUsageState(usageState);
            query.setIdIn(list.stream().map(PlaylistMusicRel::getMusicId).collect(Collectors.toList()));
            musicList = runMusicService.findList(query);
        }
        return musicList;
    }

    @Override
    public RunPlaylistVo getPlaylistById(Long id) {
        RunPlaylistVo vo = BeanUtil.copyBean(runPlaylistService.selectRunPlaylistById(id), RunPlaylistVo.class);
        List<RunMusicVo> musicVoList = BeanUtil.copyBeanList(getMusicList(id, null), RunMusicVo.class);
        vo.setMusicVoList(musicVoList);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlaylist(RunPlaylistVo vo) {
        RunPlaylist entity = BeanUtil.copyBean(vo, RunPlaylist.class);
        entity.setModifier(SecurityUtils.getUsername());
        entity.setGmtModified(ZonedDateTime.now());
        runPlaylistService.save(entity);
        Long playlistId = vo.getId();
        if (Objects.nonNull(playlistId)) {
            deletePlaylistMusicRel(playlistId);
            List<Long> activityIds = getPlaylistRelActivityIds(playlistId);
            if (!CollectionUtils.isEmpty(activityIds)) {
                activityIds.forEach(e -> {
                    redisUtil.delete(String.format(RedisKeyConstant.ACTIVITY_MUSIC_280, e));
                });
            }
        }
        List<RunMusicVo> musicVoList = vo.getMusicVoList();
        if (!CollectionUtils.isEmpty(musicVoList)) {
            //插入新歌曲列表
            List<PlaylistMusicRel> collect = musicVoList.stream().map(e -> {
                PlaylistMusicRel rel = new PlaylistMusicRel();
                rel.setPlaylistId(entity.getId());
                rel.setMusicId(e.getId());
                rel.setModifier(SecurityUtils.getUsername());
                return rel;
            }).collect(Collectors.toList());
            playlistMusicRelService.saveBatch(collect);
        }
    }

    /**
     * 查询歌单关联的活动id
     *
     * @param playlistId
     * @return
     */
    private List<Long> getPlaylistRelActivityIds(Long playlistId) {
        ActivityPlaylistRelQuery build = ActivityPlaylistRelQuery.builder()
                .playlistId(playlistId).isDelete(0)
                .select(List.of(ActivityPlaylistRel::getActivityId))
                .build();
        List<Long> activityIds = activityPlaylistRelService.findList(build).stream().map(ActivityPlaylistRel::getActivityId).collect(Collectors.toList());
        return activityIds;
    }

    /**
     * 删除歌单下的歌曲列表
     *
     * @param playlistId
     */
    private void deletePlaylistMusicRel(Long playlistId) {
        playlistMusicRelService.deletePlaylistMusicRelByPlaylistId(playlistId, SecurityUtils.getUsername());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlaylist(DeletePlaylistReq req) {
        Long playlistId = req.getId();
        RunPlaylist playlist = runPlaylistService.selectRunPlaylistById(playlistId);
        if (PlaylistUsageStateEnum.PUBLISHED.getCode().equals(playlist.getUsageState())) {
            throw new BaseException(AdminCommonError.OPERATION_FAILED.getMsg(), AdminCommonError.OPERATION_FAILED.getCode());
        }
        runPlaylistService.delete(playlistId, SecurityUtils.getUsername());
        activityPlaylistRelService.deleteActivityPlaylistRelByPlaylistId(playlistId, SecurityUtils.getUsername());
        deletePlaylistMusicRel(playlistId);
    }


    @Override
    public void updatePlaylistState(PlaylistUsageStateReq req) {
        Long playlistId = req.getId();
        if (PlaylistUsageStateEnum.NOT_PUBLISHED.getCode().equals(req.getUsageState())) {
            // 歌单下架限制
            List<Long> activityIds = getPlaylistRelActivityIds(playlistId);
            if (!CollectionUtils.isEmpty(activityIds)) {

                RunActivityQuery runActivityQuery = RunActivityQuery.builder()
                        .isDelete(0).activityStateIn(List.of(ActivityStateEnum.NOT_START.getState(), ActivityStateEnum.IN_PROGRESS.getState()))
                        .idIn(activityIds)
                        .build();
                if (znsRunActivityService.findCount(runActivityQuery) > 0) {
                    throw new BaseException(AdminCommonError.PLAYLIST_USED_IN_ACTIVITY.getMsg(), AdminCommonError.PLAYLIST_USED_IN_ACTIVITY.getCode());
                }
                // 歌单相关的活动缓存删除
                activityIds.forEach(e -> {
                    redisUtil.delete(String.format(RedisKeyConstant.ACTIVITY_MUSIC_280, e));
                });
            }
        }
        RunPlaylist playlist = runPlaylistService.selectRunPlaylistById(playlistId);
        if (PlaylistUsageStateEnum.PUBLISHED.getCode().equals(req.getUsageState())) {
            // 歌单上架限制
            if (playlist.getIsDelete() == 1) {
                throw new BaseException(AdminCommonError.OPERATION_FAILED.getMsg(), AdminCommonError.OPERATION_FAILED.getCode());
            }

            if (playlistMusicRelService.countByPlaylistId(playlistId) < 1) {
                throw new BaseException(AdminCommonError.NO_MUSIC_IN_PLAYLIST.getMsg(), AdminCommonError.NO_MUSIC_IN_PLAYLIST.getCode());
            }
        }
        RunPlaylist entity = new RunPlaylist();
        entity.setId(playlistId);
        entity.setUsageState(req.getUsageState());
        entity.setModifier(SecurityUtils.getUsername());
        runPlaylistService.updateRunPlaylistById(entity);
    }


}
