package com.linzi.pitpat.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.dao.sys.ScheduleJobMapper;
import com.linzi.pitpat.data.entity.sys.ScheduleJobEntity;
import com.linzi.pitpat.data.util.CronFrequencyAnalyzer;
import com.linzi.pitpat.data.request.JobDto;
import com.linzi.pitpat.data.service.job.JobService;
import com.linzi.pitpat.data.util.dto.RecommendedCronDensityDto;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/*

        DELETE FROM QRTZ_LOCKS;
        DELETE FROM QRTZ_CALENDARS;
        DELETE FROM QRTZ_FIRED_TRIGGERS;
		DELETE FROM QRTZ_PAUSED_TRIGGER_GRPS;
		DELETE FROM QRTZ_SCHEDULER_STATE;
    	DELETE FROM QRTZ_BLOB_TRIGGERS;
    	DELETE FROM QRTZ_CRON_TRIGGERS;
    	DELETE FROM QRTZ_SIMPLE_TRIGGERS;
    	DELETE FROM QRTZ_SIMPROP_TRIGGERS;
    	DELETE FROM QRTZ_TRIGGERS;
    	DELETE FROM QRTZ_JOB_DETAILS;



 */
@RestController
@RequestMapping({"/job", "/test/job"})
@Slf4j
public class JobContoller {

    @Autowired
    private ScheduleJobMapper scheduleJobMapper;

    @Autowired
    private JobService jobService;

    @PostMapping("/list")
    public Result<Page<ScheduleJobEntity>> selectEveryDayType(@RequestBody JobDto query) {
        Page<ScheduleJobEntity> page = scheduleJobMapper.selectListJob(PageHelper.ofPage(query), query);
        return CommonResult.success(page);
    }

    @PostMapping("/getById")
    public Result getById(@RequestBody ScheduleJobEntity scheduleJob) {
        ScheduleJobEntity scheduleJobEntity = scheduleJobMapper.selectByJobId(scheduleJob.getJobId());
        return CommonResult.success(scheduleJobEntity);
    }

    @PostMapping("/edit")
    public Result edit(@RequestBody ScheduleJobEntity scheduleJob) {
        boolean validExpression = CronExpression.isValidExpression(scheduleJob.getCronExpression());
        if (!validExpression) {
            return CommonResult.fail("cron表达式格式错误");
        }
        List<ScheduleJobEntity> scheduleJobEntities = scheduleJobMapper.findListByQuery(new JobDto().setStatus(2));
        List<String> list = scheduleJobEntities.stream().map(ScheduleJobEntity::getCronExpression).toList();
        RecommendedCronDensityDto recommendedCronDensityDto = CronFrequencyAnalyzer.recommendedCronDensity(scheduleJob.getCronExpression(), list, 6, 10);
        //提示是否高频
        if (!recommendedCronDensityDto.getIsSafe()) {
            //根据cron执行频率，推荐修改的时段
            return CommonResult.fail("该时段的监控数已满，请调整一下时段重新保存,推荐执行时间"+recommendedCronDensityDto.getRecommendedCron());
        }

        // 状态类型转换
        scheduleJob.setStatus(scheduleJob.getStatus() == 0 ? 2 : scheduleJob.getStatus());
        if (scheduleJob.getJobId() != null) {
            scheduleJobMapper.updateById(scheduleJob);
        } else {
            scheduleJobMapper.insert(scheduleJob);
        }

        return CommonResult.success();
    }

    @PostMapping("/invoke")
    public Result invoke(@RequestBody ScheduleJobEntity scheduleJob) {
        try {
            ScheduleJobEntity scheduleJobEntity = scheduleJobMapper.selectByJobId(scheduleJob.getJobId());
            Object object = SpringContextUtils.getBean(scheduleJobEntity.getBeanName());
            Method methods[] = object.getClass().getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(scheduleJobEntity.getMethodName())) {
                    jobService.invoke(object, method, scheduleJobEntity.getParams(), scheduleJobEntity.getTestParams());
                    break;
                }
            }
        } catch (Exception e) {
            log.error("invoke 定时任务 异常", e);
            return CommonResult.fail("invoke 定时任务异常");
        }
        return CommonResult.success();
    }

    /**
     * 获取cron表达式的频率
     * @return
     */
    @PostMapping("/getCronFrequency")
    public Result getCronFrequency(@RequestParam("ignoreHighFrequency") Boolean ignoreHighFrequency) {
        List<ScheduleJobEntity> scheduleJobEntities = scheduleJobMapper.findListByQuery(new JobDto().setStatus(2));
        List<String> list = scheduleJobEntities.stream().map(ScheduleJobEntity::getCronExpression).toList();
        Map<String, Object> cronFrequency = CronFrequencyAnalyzer.getCronFrequency(list, ignoreHighFrequency);
        return CommonResult.success(cronFrequency);
    }

    /**
     * 获取秒
     * @return
     */
    @PostMapping("/getSecondEntityMap")
    public Result getSecondEntityMap(@RequestParam("second") Integer second) {
        List<ScheduleJobEntity> scheduleJobEntities = scheduleJobMapper.findListByQuery(new JobDto().setStatus(2));
        Map<Integer, Set<ScheduleJobEntity>> secondEntityMap = jobService.getSecondEntityMap(scheduleJobEntities);
        if (Objects.isNull(second)) {
            return CommonResult.success(secondEntityMap);
        }
        Set<ScheduleJobEntity> scheduleJobEntitiesSecond = secondEntityMap.get(second);
        if (!scheduleJobEntitiesSecond.isEmpty()) {
            List<Long> list = scheduleJobEntitiesSecond.stream().map(ScheduleJobEntity::getJobId).toList();
            log.info("list:{}",list);
        }
        return CommonResult.success(scheduleJobEntitiesSecond);
    }
}
