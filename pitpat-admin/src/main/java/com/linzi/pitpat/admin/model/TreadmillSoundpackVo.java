package com.linzi.pitpat.admin.model;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class TreadmillSoundpackVo {


    private Long id;
    //音频包名称
    private String packageName;
    //最新音频版本
    private Integer latestVersion;
    //语言code，en_US：英语，fr_CA ：法语
    private String languageCode;
    //语言名称
    private String languageName;
    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;

    //音频数量
    private Integer audioNum;

    //版本日志
    private String remark;

    //音频列表
    private List<TreadmillAudioVo> audioVoList;

}
