package com.linzi.pitpat.admin.activityservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.manager.console.AssistActivitityConsoleManager;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.entity.AssistInitiatorReward;
import com.linzi.pitpat.data.activityservice.model.query.AssistActivitityQuery;
import com.linzi.pitpat.data.activityservice.model.request.AssistActivitySearchRequest;
import com.linzi.pitpat.data.activityservice.model.request.SaveOrUpdateAssistRequest;
import com.linzi.pitpat.data.activityservice.model.request.SwitchAssistActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.AssistActivityDropListVo;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import com.linzi.pitpat.data.activityservice.service.AssistInitiatorRewardService;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 助力活动管理
 *
 * <AUTHOR> 李兴海
 * @date : 2023/5/5 18:44
 */
@RestController
@RequestMapping("/assist/activity")
@Slf4j
public class AssistActivityController {

    @Resource
    private AssistActivitityService assistActivitityService;
    @Resource
    private AssistActivitityConsoleManager assistActivitityConsoleManager;
    @Autowired
    private AssistInitiatorRewardService assistInitiatorRewardService;

    @Autowired
    private CouponService couponService;


    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    private static final String ASSIST_ACTIVITY_H5_URI = "/activity/assist/";


    /**
     * 通过条件查询助力活动
     *
     * @param assistActivitySearchRequest
     * @return
     */
    @PostMapping("/searchAssistActivityByConditon")
    public Result<Page<AssistActivitity>> searchAssistActivityByConditon(@RequestBody final AssistActivitySearchRequest assistActivitySearchRequest) {
        return CommonResult.success(assistActivitityService.searchAssistActivityByConditon(assistActivitySearchRequest));
    }


    /**
     * 查询助力活动详情
     *
     * @param id 助力活动ID
     * @return
     */
    @GetMapping("/getById")
    public Result getById(@RequestParam("id") final Long id) {
        return CommonResult.fail("not support");
    }

    /**
     * 新增/更新助力活动
     *
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public Result saveOrUpdate(@RequestBody final SaveOrUpdateAssistRequest saveOrUpdateAssistRequest) {
        final AssistActivitity assistActivitity = saveOrUpdateAssistRequest.getAssistActivitity();
        final Long singleLuckyDrawId = assistActivitity.getSingleLuckyDrawId();

        final Long id = assistActivitity.getId();
        if (id != null) {

            final long count = assistActivitityService.findCount(
                    AssistActivitityQuery.builder()
                            .isDelete(0).id(id).status(1)
                            .build());
            if (count > 0) {
                return CommonResult.fail("上架的助力活动不可编辑");
            }
        }
        final String name = assistActivitity.getName();
        if (!StringUtils.hasText(name)) {
            return CommonResult.fail("助力活动名称不能为空!");
        }
        final ZonedDateTime startTime = assistActivitity.getStartTime();
        if (startTime == null) {
            return CommonResult.fail("助力活动开始时间不能为空!");
        }
        final ZonedDateTime now = ZonedDateTime.now();
        if (startTime.isBefore(now)) {
            assistActivitity.setStartTime(now);
        }
        final ZonedDateTime endTime = assistActivitity.getEndTime();
        if (endTime == null) {
            return CommonResult.fail("助力活动结束时间不能为空!");
        }
        if (endTime.isBefore(startTime)) {
            return CommonResult.fail("助力活动结束时间不能早于开始时间!");
        }
        if (singleLuckyDrawId == null) {
            return CommonResult.fail("独立站抽奖活动模板编号不能为空!");
        }

        final String mainImage = assistActivitity.getMainImage();
        if (!StringUtils.hasText(mainImage)) {
            return CommonResult.fail("助力活动主图不能为空!");
        }
        final String subImage = assistActivitity.getSubImage();
        if (!StringUtils.hasText(subImage)) {
            return CommonResult.fail("助力活动副图不能为空");
        }

        // 保存发起助力者奖励规则
        final List<AssistInitiatorReward> assistInitiatorRewardList = saveOrUpdateAssistRequest.getAssistInitiatorRewardList();
        if (CollectionUtils.isEmpty(assistInitiatorRewardList)) {
            return CommonResult.fail("发起助力者奖励规则列表不能为空!");
        }
        final int size = assistInitiatorRewardList.size();
        if (size < 1 || size > 4) {
            return CommonResult.fail("发起助力者奖励规则列表最少一个,最多4个");
        }
        // 校验活动助力参数
        // 固定选择，最多5次；单个数字仅能选择一次，且上排选择的次数，需要小于下排次数。
        // 1、pitpat现在已有的、固定的券：参赛必胜券、奖励翻倍券、幸运现金券1、幸运现金券2、亚马逊优惠券、抵扣券2、单个券仅能选择一次
        final Set<Integer> assistTimesSet = new HashSet<>(assistInitiatorRewardList.size());
        final Set<Long> couponIdSet = new HashSet<>(assistInitiatorRewardList.size());
        for (int i = 0; i < assistInitiatorRewardList.size(); i++) {
            final AssistInitiatorReward currentAssistInitiatorReward = assistInitiatorRewardList.get(i);
            final Integer assistTimes = currentAssistInitiatorReward.getAssistTimes();
            final int rowNumber = i + 1;
            if (assistTimes == null) {
                return CommonResult.fail("第" + rowNumber + "行发起助力者助力次数不能为空!");
            }
            if (assistTimes <= 0) {
                return CommonResult.fail("第" + rowNumber + "行发起助力者助力次数最少1次!");
            }
            if (assistTimes > 5) {
                return CommonResult.fail("第" + rowNumber + "行发起助力者助力次数最多5次!");
            }
            if (assistTimesSet.contains(assistTimes)) {
                return CommonResult.fail("第" + rowNumber + "行发起助力者助力次数不能重复!");
            }
            final Long couponId = currentAssistInitiatorReward.getCouponId();
            if (couponId == null) {
                return CommonResult.fail("第" + rowNumber + "行发起助力者获取的优惠券ID不能为空!");
            }
            if (couponIdSet.contains(couponId)) {
                return CommonResult.fail("第" + rowNumber + "行发起助力者获取的优惠券ID不能重复!");
            }
            assistTimesSet.add(assistTimes);
            couponIdSet.add(couponId);
        }
        if (id == null) {
            assistActivitity.setStatus(0);
            assistActivitityService.save(assistActivitity);
            final AssistActivitity newAssistActivity = new AssistActivitity();
            newAssistActivity.setId(assistActivitity.getId());
            newAssistActivity.setActivityUrl(mallH5Url + ASSIST_ACTIVITY_H5_URI + assistActivitity.getId());
            assistActivitityService.updateById(newAssistActivity);
        } else {
            assistActivitityService.updateById(assistActivitity);
        }

        // 保存助力发起人奖励信息
        if (id != null) {
            assistInitiatorRewardService.deleteById(id);
        }
        assistInitiatorRewardList.forEach(assistInitiatorReward -> {
            assistInitiatorReward.setAssistActivitityId(assistActivitity.getId());
        });
        assistInitiatorRewardService.saveBatch(assistInitiatorRewardList);
        return CommonResult.success();
    }

    /**
     * 获取独立站抽奖活动列表
     *
     * @return
     */
    @GetMapping("/getSingleLuckyDraw")
    public Result getLuckyDraw() {
        return CommonResult.fail("not support");
    }


    /**
     * 助力活动上下架操作
     *
     * @param switchAssistActivityRequest
     * @return
     */
    @PostMapping("/switchStatus")
    public Result switchStatus(@RequestBody final SwitchAssistActivityRequest switchAssistActivityRequest) {
        return assistActivitityConsoleManager.switchStatus(switchAssistActivityRequest);
    }


    /**
     * 助力活动下拉列表
     *
     * @return
     */
    @GetMapping("/dropList")
    public Result<List<AssistActivityDropListVo>> dropList() {
        return CommonResult.success(assistActivitityService.dropList());
    }

    /**
     * 获取优惠券列表
     *
     * @return
     */
    @GetMapping("/getCouponList")
    public Result getCouponList() {
        final List<Coupon> couponList = couponService.findCouponList();
        return CommonResult.success(couponList);
    }

    /**
     * 获取优惠券列表
     *
     * @return
     */
    @GetMapping("/assistActivityAutoDownShelfTask")
    public Result assistActivityAutoDownShelfTask() {
        assistActivitityService.assistActivityAutoDownShelfTask();
        return CommonResult.success();
    }


}
