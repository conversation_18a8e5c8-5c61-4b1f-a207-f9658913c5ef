package com.linzi.pitpat.admin.bussiness;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.linzi.pitpat.admin.model.CustomerAnswerConfigDetailVo;
import com.linzi.pitpat.admin.model.CustomerAnswerConfigVo;
import com.linzi.pitpat.admin.model.CustomerAnswerDetailVo;
import com.linzi.pitpat.admin.model.Dto.request.CustomerAnswerDeleteReqDto;
import com.linzi.pitpat.admin.model.Dto.request.CustomerAnswerReqDto;
import com.linzi.pitpat.admin.model.Dto.response.CustomerAnswerRespDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.systemservice.model.query.CustomerAnswerConfigQuery;
import com.linzi.pitpat.data.systemservice.model.query.CustomerAnswerConfigQueryPo;
import com.linzi.pitpat.data.systemservice.model.query.CustomerAnswerDetailQuery;
import com.linzi.pitpat.data.systemservice.service.CustomerAnswerConfigService;
import com.linzi.pitpat.data.systemservice.service.CustomerAnswerDetailService;
import com.linzi.pitpat.data.userservice.model.entity.CustomerAnswerConfig;
import com.linzi.pitpat.data.userservice.model.entity.CustomerAnswerDetail;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerAnswerManager {

    @Resource
    private CustomerAnswerConfigService customerAnswerConfigService;

    @Resource
    private CustomerAnswerDetailService customerAnswerDetailService;

    @Resource
    private RedissonClient redissonClient;

    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;


    public Page<CustomerAnswerConfigVo> listCustomerAnswerConfig(CustomerAnswerConfigQueryPo queryPo) {
        CustomerAnswerConfigQuery query = BeanUtil.copyBean(queryPo, CustomerAnswerConfigQuery.class);
        Page<CustomerAnswerConfig> page = customerAnswerConfigService.findPage(query);
        Page<CustomerAnswerConfigVo> pageVo = new Page<>();
        pageVo.setTotal(page.getTotal());
        pageVo.setRecords(BeanUtil.copyBeanList(page.getRecords(), CustomerAnswerConfigVo.class));
        return pageVo;
    }

    public CustomerAnswerConfigDetailVo getById(Long id) {
        CustomerAnswerConfigDetailVo vo = new CustomerAnswerConfigDetailVo();
        CustomerAnswerConfig config = customerAnswerConfigService.getById(id);
        vo.setId(id);
        vo.setTitle(config.getTitle());
        vo.setOrderNumber(config.getOrderNumber());
        vo.setOpenStatus(config.getOpenStatus());
        vo.setDefaultLangCode(config.getDefaultLangCode());
        vo.setVersionLimit(config.getVersionLimit());
        vo.setJumpType(config.getJumpType());
        vo.setJumpUrl(config.getJumpUrl());
        CustomerAnswerDetailQuery detailQuery = CustomerAnswerDetailQuery.builder().configIdList(List.of(id)).build();
        List<CustomerAnswerDetail> detailList = customerAnswerDetailService.findList(detailQuery);
        List<CustomerAnswerDetailVo> list = BeanUtil.copyBeanList(detailList, CustomerAnswerDetailVo.class);
        vo.setList(list);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(CustomerAnswerConfigDetailVo vo) {
        String title = vo.getTitle();
        Integer orderNumber = vo.getOrderNumber();
        List<CustomerAnswerDetailVo> list = vo.getList();
        list = list.stream().filter(e -> StringUtils.hasText(e.getTypeTitle())).collect(Collectors.toList());
        // 检查配置是否存在
        CustomerAnswerConfigQuery configQuery1 = new CustomerAnswerConfigQuery();
        configQuery1.setTitle(title);
        CustomerAnswerConfig config1 = customerAnswerConfigService.getByQuery(configQuery1);
        if (Objects.nonNull(config1) && !config1.getId().equals(vo.getId())) {
            throw new BaseException("Title already exists.");
        }
        CustomerAnswerConfigQuery configQuery2 = new CustomerAnswerConfigQuery();
        configQuery2.setOrderNumber(orderNumber);
        CustomerAnswerConfig config2 = customerAnswerConfigService.getByQuery(configQuery2);
        if (Objects.nonNull(config2) && !config2.getId().equals(vo.getId())) {
            throw new BaseException("Order number already exists.");
        }
        CustomerAnswerConfig customerAnswerConfig = BeanUtil.copyBean(vo, CustomerAnswerConfig.class);
        Integer preJumpType = null;
        if (Objects.nonNull(vo.getId())) {
            // 更新
            CustomerAnswerConfig config = customerAnswerConfigService.getById(vo.getId());
            preJumpType = config.getJumpType();
            customerAnswerConfig.setGmtModified(ZonedDateTime.now());
            customerAnswerConfigService.update(customerAnswerConfig);
            deleteOldDetail(list, customerAnswerConfig);
        } else {
            // 新增
            customerAnswerConfigService.insert(customerAnswerConfig);
        }
        // 处理答案详情
        for (CustomerAnswerDetailVo detailVo : list) {
            handleAnswerDetail(detailVo, customerAnswerConfig.getId(), vo.getJumpUrl(), preJumpType, vo.getJumpType());
        }
        // 删除缓存
        deleteCache();
    }

    /**
     * 删除新的不存在的语言数据
     *
     * @param list
     * @param customerAnswerConfig
     */
    public void deleteOldDetail(List<CustomerAnswerDetailVo> list, CustomerAnswerConfig customerAnswerConfig) {
        CustomerAnswerDetailQuery detailQuery = CustomerAnswerDetailQuery.builder().configIdList(List.of(customerAnswerConfig.getId())).build();
        List<CustomerAnswerDetail> oldList = customerAnswerDetailService.findList(detailQuery);
        Set<String> oldLangCodeList = oldList.stream().map(CustomerAnswerDetail::getLangCode).collect(Collectors.toSet());
        Set<String> collect = list.stream().map(CustomerAnswerDetailVo::getLangCode).collect(Collectors.toSet());
        Set<String> difference = Sets.difference(oldLangCodeList, collect);
        if (!CollectionUtils.isEmpty(difference)) {
            Set<String> finalDifference = difference;
            List<CustomerAnswerDetail> deleteList = oldList.stream().filter(e -> finalDifference.contains(e.getLangCode())).collect(Collectors.toList());

            deleteList.forEach(e -> {
                customerAnswerDetailService.delectById(e.getId());
            });
        }
    }

    /**
     * 删除缓存
     */
    private void deleteCache() {
        Arrays.stream(I18nConstant.LanguageCodeEnum.values()).forEach(e -> {
            String key = String.format(RedisKeyConstant.CUSTOMER_SERVICE_ANSWER, e.getCode());
            RBucket<Object> bucket = redissonClient.getBucket(key);
            if (bucket.isExists()) {
                bucket.delete();
            }
        });
    }


    /**
     * 处理问答详情
     *
     * @param detailVo
     * @param configId
     */
    public void handleAnswerDetail(CustomerAnswerDetailVo detailVo, Long configId, String jumpUrl, Integer preJumpType, Integer jumpType) {
        CustomerAnswerDetailQuery detailQuery = CustomerAnswerDetailQuery.builder()
                .langCode(detailVo.getLangCode())
                .typeTitle(detailVo.getTypeTitle())
                .build();
        CustomerAnswerDetail detail = customerAnswerDetailService.getByQuery(detailQuery);
        if (Objects.nonNull(detail) && !detail.getId().equals(detailVo.getId())) {
            throw new BaseException(detailVo.getLangCode() + " detail already exists.");
        }
        CustomerAnswerDetail customerAnswerDetail = BeanUtil.copyBean(detailVo, CustomerAnswerDetail.class);
        if (Objects.nonNull(detailVo.getId())) {
            // 更新
            customerAnswerDetail.setGmtModified(ZonedDateTime.now());
            if (Objects.equals(jumpType, preJumpType)) {
                customerAnswerDetail.setUrl(Objects.equals(jumpType, 1) ? jumpUrl : detail.getUrl());
            } else {
                customerAnswerDetail.setUrl(Objects.equals(jumpType, 1) ? jumpUrl : mallH5Url + "/customer/question/answering/" + customerAnswerDetail.getId());
            }
            customerAnswerDetailService.update(customerAnswerDetail);
        } else {
            // 新增
            customerAnswerDetail.setConfigId(configId);
            customerAnswerDetailService.insert(customerAnswerDetail);
            if (Objects.equals(jumpType, 1)) {
                customerAnswerDetail.setUrl(jumpUrl);
            } else {
                String url = mallH5Url + "/customer/question/answering/" + customerAnswerDetail.getId();
                customerAnswerDetail.setUrl(url);
            }
            customerAnswerDetailService.update(customerAnswerDetail);
        }
    }


    public CustomerAnswerRespDto publishCustomerAnswer(CustomerAnswerReqDto req) {
        CustomerAnswerConfig config = new CustomerAnswerConfig();
        config.setId(req.getId());
        config.setOpenStatus(req.getOpenStatus());
        config.setGmtModified(ZonedDateTime.now());
        CustomerAnswerRespDto dto = new CustomerAnswerRespDto();
        dto.setFlag(customerAnswerConfigService.update(config) > 0 ? true : false);
        // 删除缓存
        deleteCache();
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(CustomerAnswerDeleteReqDto req) {
        Long id = req.getId();
        CustomerAnswerConfig answerConfig = customerAnswerConfigService.getById(id);
        if (YesNoStatus.YES.getCode().equals(answerConfig.getOpenStatus())) {
            throw new BaseException("问题已开启，不可删除");
        }
        customerAnswerConfigService.deleteById(id);
        customerAnswerDetailService.delectByConfigId(id);
    }

}
