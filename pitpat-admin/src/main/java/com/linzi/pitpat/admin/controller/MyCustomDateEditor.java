package com.linzi.pitpat.admin.controller;

import com.linzi.pitpat.core.util.MapUtil;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import java.beans.PropertyEditorSupport;

public class MyCustomDateEditor extends PropertyEditorSupport {


    /**
     * Parse the ZonedDateTime from the given text, using the specified DateFormat.
     */
    @Override
    public void setAsText(@Nullable String text) throws IllegalArgumentException {
        if (!StringUtils.hasText(text)) {
            // Treat empty String as null value.
            setValue(null);
        } else {
            try {
                setValue(new Date(MapUtil.getLong(text)));
            } catch (Exception ex) {
                throw new IllegalArgumentException("Could not parse date: " + ex.getMessage(), ex);
            }
        }
    }

}
