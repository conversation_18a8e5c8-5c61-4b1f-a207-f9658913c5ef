package com.linzi.pitpat.admin.equipmentservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2FirmwarePackageCreateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2FirmwarePackagePageQueryDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2FirmwarePackageQueryDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2FirmwarePackageUpdateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.response.OtaV2AddrResponseDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.response.OtaV2FirmwarePackageConsoleDetailResponseDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.response.OtaV2FirmwarePackageConsoleResponseDto;
import com.linzi.pitpat.data.equipmentservice.manager.console.OtaV2FirmwarePackageConsoleManager;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 新版OTA固件包 服务类
 *
 * @since 2025年7月3日
 */
@Slf4j
@RestController
@RequestMapping("/otaV2FirmwarePackages")
@RequiredArgsConstructor
public class OtaV2FirmwarePackageConsoleController {

    private final OtaV2FirmwarePackageConsoleManager otaV2FirmwarePackageConsoleManager;

    /**
     * 分页查询新版OTA固件包列表
     */
    @PostMapping("/page")
    public Result<Page<OtaV2FirmwarePackageConsoleResponseDto>> findPage(@RequestBody OtaV2FirmwarePackagePageQueryDto pageQueryDto) {
        Page<OtaV2FirmwarePackageConsoleResponseDto> resp = otaV2FirmwarePackageConsoleManager.findPage(pageQueryDto);
        return CommonResult.success(resp);
    }

    /**
     * 创建新版OTA固件包
     */
    @Log(title = "创建新版OTA固件包", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/create")
    public Result<Long> create(@RequestBody @Validated OtaV2FirmwarePackageCreateRequestDto requestDto) {
        return CommonResult.success(otaV2FirmwarePackageConsoleManager.create(requestDto, SecurityUtils.getUsername()));
    }

    /**
     * 查询新版OTA固件包详情
     */
    @GetMapping("/get/{id}")
    public Result<OtaV2FirmwarePackageConsoleDetailResponseDto> get(@PathVariable("id") Long id) {
        return CommonResult.success(otaV2FirmwarePackageConsoleManager.getById(id));
    }

    /**
     * 更新新版OTA固件包
     */
    @Log(title = "更新新版OTA固件包", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/update")
    public Result<Long> update(@RequestBody @Validated OtaV2FirmwarePackageUpdateRequestDto requestDto) {
        return CommonResult.success(otaV2FirmwarePackageConsoleManager.update(requestDto, SecurityUtils.getUsername()));
    }

    /**
     * 按照ID 删除新版OTA固件包
     */
    @Log(title = "删除新版OTA固件包", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody OtaV2FirmwarePackageQueryDto queryDto) {
        otaV2FirmwarePackageConsoleManager.deleteFirmwarePackage(queryDto.getId());
        return CommonResult.success(true);
    }

    /**
     * 查询新版OTA固件包地址配置
     *
     * @param type 固件类型（十进制）
     */
    @GetMapping("/findOtaV2Addr")
    public Result<List<OtaV2AddrResponseDto>> findOtaV2Addr(@RequestParam(required = false) Integer type) {
        List<OtaV2AddrResponseDto> resp = otaV2FirmwarePackageConsoleManager.findOtaV2Addr(type);
        return CommonResult.success(resp);
    }

}
