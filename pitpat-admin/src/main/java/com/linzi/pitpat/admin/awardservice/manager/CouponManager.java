package com.linzi.pitpat.admin.awardservice.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCategory;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCouponConfigQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryService;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.mapper.CouponCurrencyMapper;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.CouponI8nDto;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.query.CouponsPageQuery;
import com.linzi.pitpat.data.awardservice.model.request.CouponDetailReqDto;
import com.linzi.pitpat.data.awardservice.model.request.CouponSaveReq;
import com.linzi.pitpat.data.awardservice.model.request.MallCouponCodeReqDto;
import com.linzi.pitpat.data.awardservice.model.vo.CouponGoodsVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponUpdateDetailVo;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallGoodsResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallProductResponseDto;
import com.linzi.pitpat.data.mallservice.model.entity.ProductDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.service.ProductService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.request.CouponCurrencyRequestDto;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class CouponManager {

    private final CouponService couponService;
    private final StringRedisTemplate redisTemplate;
    private final ActivityCouponConfigService activityCouponConfigService;
    private final CouponI18nService couponI18nService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsGoodsService znsGoodsService;
    private final CouponCurrencyMapper couponCurrencyMapper;
    private final AppRouteConfigService appRouteConfigService;
    private final ActivityCategoryService activityCategoryService;
    private final CouponCurrencyService couponCurrencyService;
    private final ProductService productService;

    /**
     * 新增修改卷模板
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrModify(CouponSaveReq req) {
        //参数校验
        checkParam(req);

        //更新修改优惠券
        Coupon coupon = fillCouponEntity(req);

        //更新优惠券限制
        fillActivitCouponConfigs(req, coupon);

        //加拿大临时支付方案
        if ("JIANADALINSHIZHIFUFANGAN".equals(req.getName())) {
            redisTemplate.opsForList().rightPush(RedisConstants.CANADA_TEMP_COUPONS, coupon.getId().toString());
        }

        // 保存国际化数据
        List<CouponI8nDto> values = req.getValues();
        if (!CollectionUtils.isEmpty(values)) {
            couponI18nService.saveBatch(coupon.getId(), values);
        }
        //保存多币种价格
        couponCurrencyService.saveOrModifyCouponCurrency(req.getOperateName(), coupon.getId(), req.getCurrencyList());
    }

    /**
     * 参数校验
     */
    private void checkParam(CouponSaveReq req) {
        if (req.getExpiryType() == 2) {
            if (DateUtil.compare(req.getGmtEnd(), ZonedDateTime.now()) < 0) {
                throw new BaseException("券的有效时间需在当前时间之后");
            }
        }
        if (StringUtils.hasText(req.getExchangeCode())) {
            //校验兑换码不能重复
            LambdaQueryWrapper<Coupon> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ne(Objects.nonNull(req.getId()), Coupon::getId, req.getId());
            queryWrapper.eq(Coupon::getExchangeCode, req.getExchangeCode());
            queryWrapper.last("limit 1");
            Coupon oldCoupon = couponService.findOneByQueryWrapper(queryWrapper);
            if (oldCoupon != null) {
                throw new BaseException("兑换码不能重复");
            }
        }

        if (!CollectionUtils.isEmpty(req.getGoodsIds())) {
            //校验spu绑定的券数量
            ActivityCouponConfigQuery query = ActivityCouponConfigQuery.builder()
                    .type(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type)
                    .couponConfigIds(req.getGoodsIds()).neCouponId(req.getId()).build();
            List<ActivityCouponConfig> couponConfigs = activityCouponConfigService.findListByQuery(query);
            Map<String, List<ActivityCouponConfig>> map = couponConfigs.stream().collect(Collectors.groupingBy(ActivityCouponConfig::getCouponConfig));
            for (Map.Entry<String, List<ActivityCouponConfig>> entry : map.entrySet()) {
                String goodsId = entry.getKey();
                List<ActivityCouponConfig> configList = entry.getValue();
                if (configList.size() >= 20) {
                    ZnsGoodsEntity goodsEntity = znsGoodsService.findById(Long.valueOf(goodsId));
                    throw new BaseException(goodsEntity.getTitle() + "关联的券超过20个");
                }
            }
        }
    }

    private void fillActivitCouponConfigs(CouponSaveReq req, Coupon coupon) {
        //删除优惠券配置老数据
        activityCouponConfigService.deleteByCouponId(coupon.getId());

        //保存新数据
        if (!CollectionUtils.isEmpty(req.getGoodsIds())) {
            //商品限制
            req.getGoodsIds().forEach(i -> saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type, String.valueOf(i), coupon));
        }
        if (!CollectionUtils.isEmpty(req.getActivityIds())) {
            //活动id限制
            req.getActivityIds().forEach(i -> saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_2.type, String.valueOf(i), coupon));
        }
        if (Objects.nonNull(req.getActivityType())) {
            //老活动类型限制
            saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_1.type, String.valueOf(req.getActivityType()), coupon);
        }
        if (!CollectionUtils.isEmpty(req.getTaskIds())) {
            //老活动聚合活动id限制
            req.getTaskIds().forEach(i -> saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_3.type, String.valueOf(i), coupon));
        }
        if (CollectionUtils.isEmpty(req.getActivityIds()) && Objects.isNull(req.getActivityType()) && CollectionUtils.isEmpty(req.getTaskIds())
                && MainActivityTypeEnum.OLD.getType().equals(req.getNewActivityType()) && CollectionUtils.isEmpty(req.getGoodsIds())) {
            //全场通用()
            saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_0.getType(), null, coupon);
        }
        //新活动类型排除old
        if (Objects.nonNull(req.getNewActivityType()) && !MainActivityTypeEnum.OLD.getType().equals(req.getNewActivityType())) {
            if (Objects.nonNull(req.getNewActivityId())) {
                if (MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(req.getNewActivityType())) {
                    //新聚合活动任务表id
                    req.getNewActivityId().forEach(i -> saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_6.type, i + "", coupon));
                } else {
                    //活动id
                    req.getNewActivityId().forEach(i -> saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_2.type, i + "", coupon));
                }
            } else {
                //新活动类型限制
                saveNewCouponConfig(CouponConstant.CouponConfigEnum.COUPON_CONFIG_5.type, req.getNewActivityType(), coupon);
            }
        }
    }

    /**
     * 保存卷限制明细
     */
    private void saveNewCouponConfig(int type, String i, Coupon coupon) {
        ActivityCouponConfig activityCouponConfig = new ActivityCouponConfig();
        activityCouponConfig.setType(type);
        activityCouponConfig.setCouponConfig(i);
        activityCouponConfig.setCouponId(coupon.getId());
        activityCouponConfigService.save(activityCouponConfig);
    }

    /**
     * 组装优惠券实体
     */
    private Coupon fillCouponEntity(CouponSaveReq req) {
        Coupon coupon = new Coupon();
        BeanUtils.copyProperties(req, coupon);
        coupon.setCountryCode(JsonUtil.writeString(req.getCountryCodeList()));
        if (!CollectionUtils.isEmpty(req.getValues())) {
            //使用默认语言标题填充优惠券标题
            CouponI8nDto couponI8nDto = req.getValues().stream().filter(e -> e.getLangCode().equals(req.getDefaultLangCode())).findFirst().orElse(req.getValues().get(0));
            coupon.setDescription(couponI8nDto.getCanUseDescription());
            coupon.setTitle(couponI8nDto.getTitle());
        }
        if (Objects.nonNull(req.getDiscount())) {
            coupon.setDiscount(req.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        coupon.setMinPicUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202304/iiJ13qGWf6FU6183.png");
        if (Objects.nonNull(req.getReceiveStart())) {
            //领取开始时间
            coupon.setReceiveStart(ZonedDateTimeUtil.convertFrom(req.getReceiveStart()));
        }
        if (Objects.nonNull(req.getReceiveEnd())) {
            //领取结束时间
            coupon.setReceiveEnd(ZonedDateTimeUtil.convertFrom(req.getReceiveEnd()));
        }
        if (CouponTypeEnum.MALL_COUPON.getCode().equals(req.getCouponType())) {
            //商城券默认启用
            coupon.setStatus(CouponConstant.CouponStatusEnum.STATUS_1.type);
            coupon.setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type);
            coupon.setType(CouponConstant.TypeEnum.TYPE_100.type); //券来源类型:默认商城
        } else {
            //赛事券默认关闭
            coupon.setStatus(CouponConstant.CouponStatusEnum.STATUS_N1.type);
            coupon.setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_1.type);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(req.getGoodsIds())) {
            //指定商品
            String useScopeIds = req.getGoodsIds().stream().map(String::valueOf).collect(Collectors.joining(","));
            coupon.setUseScopeId(useScopeIds);
        }
        if (Objects.nonNull(req.getId())) {
            //编辑
            coupon.setModifier(req.getOperateName());
            coupon.setGmtModified(ZonedDateTime.now());
            couponService.update(coupon);
        } else {
            //新增
            coupon.setCreator(req.getOperateName());
            couponService.create(coupon);
        }
        return coupon;
    }

    /**
     * 卷模板详情
     */
    public CouponUpdateDetailVo couponDetail(CouponDetailReqDto req) {
        Long couponId = req.getId();
        CouponUpdateDetailVo couponUpdateDetailVo = new CouponUpdateDetailVo();
        Coupon coupon = couponService.findById(couponId);
        BeanUtils.copyProperties(coupon, couponUpdateDetailVo);
        if (Objects.nonNull(coupon.getDiscount())) {
            couponUpdateDetailVo.setDiscount(coupon.getDiscount().multiply(new BigDecimal(100)));
        }
        if (Objects.nonNull(coupon.getReceiveStart())) {
            couponUpdateDetailVo.setReceiveStart(ZonedDateTimeUtil.convertFrom(coupon.getReceiveStart()));
        }
        if (Objects.nonNull(coupon.getReceiveEnd())) {
            couponUpdateDetailVo.setReceiveEnd(ZonedDateTimeUtil.convertFrom(coupon.getReceiveEnd()));
        }
        List<ActivityCouponConfig> typeConfigs = activityCouponConfigService.list(new QueryWrapper<ActivityCouponConfig>()
                .eq("is_delete", YesNoStatus.NO.getCode())
                .eq("coupon_id", coupon.getId()));
        if (!CollectionUtils.isEmpty(typeConfigs)) {
            ActivityCouponConfig activityCouponConfig = typeConfigs.get(0);
            if (typeConfigs.size() == 1) {
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_1.type.equals(activityCouponConfig.getType())) {
                    //老活动类型
                    couponUpdateDetailVo.setActivityType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_2.type.equals(activityCouponConfig.getType())) {
                    //新老活动id(兼容新老)
                    couponUpdateDetailVo.setActivityIds(Collections.singletonList(Long.valueOf(activityCouponConfig.getCouponConfig())));
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(activityNew.getActivityType());
                    couponUpdateDetailVo.setNewActivityType(activityNew.getMainType() == null ? MainActivityTypeEnum.OLD.getType() : activityNew.getMainType());
                    if (!MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
                        couponUpdateDetailVo.setNewActivityId(Collections.singletonList(Long.valueOf(activityCouponConfig.getCouponConfig())));
                    }
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_3.type.equals(activityCouponConfig.getType())) {
                    //老聚合活动的任务表id
                    couponUpdateDetailVo.setTaskIds(Collections.singletonList(Long.valueOf(activityCouponConfig.getCouponConfig())));
                    couponUpdateDetailVo.setCouponLimitationType(-4);
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_5.type.equals(activityCouponConfig.getType())) {
                    //新活动类型
                    couponUpdateDetailVo.setNewActivityType(activityCouponConfig.getCouponConfig());
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_6.type.equals(activityCouponConfig.getType())) {
                    //新聚合活动任务表id
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setNewActivityId(collect);
                    couponUpdateDetailVo.setNewActivityType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
                }
            } else {
                // 使用范围 list ids
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_1.type.equals(activityCouponConfig.getType())) {
                    //老活动类型
                    couponUpdateDetailVo.setActivityType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(Integer.valueOf(activityCouponConfig.getCouponConfig()));
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_2.type.equals(activityCouponConfig.getType())) {
                    //新老活动id(兼容新老)
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setActivityIds(collect);
                    ActivityTypeDto activityNew = runActivityService.getActivityNew(Long.valueOf(activityCouponConfig.getCouponConfig()));
                    couponUpdateDetailVo.setCouponLimitationType(activityNew.getActivityType());
                    couponUpdateDetailVo.setNewActivityType(activityNew.getMainType() == null ? MainActivityTypeEnum.OLD.getType() : activityNew.getMainType());
                    if (!MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
                        couponUpdateDetailVo.setNewActivityId(collect);
                    }
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_3.type.equals(activityCouponConfig.getType())) {
                    //老聚合活动的任务表id
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setTaskIds(collect);
                    couponUpdateDetailVo.setCouponLimitationType(-4);
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_5.type.equals(activityCouponConfig.getType())) {
                    //新活动类型
                    couponUpdateDetailVo.setNewActivityType(activityCouponConfig.getCouponConfig());
                }
                if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_6.type.equals(activityCouponConfig.getType())) {
                    //新聚合活动任务表id
                    List<Long> collect = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).collect(Collectors.toList());
                    couponUpdateDetailVo.setNewActivityId(collect);
                    couponUpdateDetailVo.setNewActivityType(MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType());
                }
            }

            //商城券使用商品范围
            if (CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type.equals(activityCouponConfig.getType())) {
                //商品id
                List<Long> goodsIds = typeConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).toList();
                List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(goodsIds);
                if (!CollectionUtils.isEmpty(goodsEntities)) {
                    List<CouponGoodsVo> goodsVos = goodsEntities.stream().map(item -> new CouponGoodsVo(item.getId(), item.getTitle(), item.getProductId())).toList();
                    couponUpdateDetailVo.setGoodsVos(goodsVos);
                    List<MallProductResponseDto> productResponseDtoList = new ArrayList<>();
                    goodsEntities.stream().collect(Collectors.groupingBy(ZnsGoodsEntity::getProductId)).forEach((k, v) -> {
                        MallProductResponseDto mallProductResponseDto = new MallProductResponseDto();
                        ProductDo productDo = productService.findById(k);
                        mallProductResponseDto.setProductId(k);
                        mallProductResponseDto.setGoodsName(Objects.nonNull(productDo) ? productDo.getTitle() : "");
                        mallProductResponseDto.setGoodsList(v.stream().map(item -> {
                            MallGoodsResponseDto mallGoodsResponseDto = new MallGoodsResponseDto();
                            mallGoodsResponseDto.setGoodsId(item.getId());
                            mallGoodsResponseDto.setGoodsName(item.getTitle());
                            mallGoodsResponseDto.setProductId(item.getProductId());
                            return mallGoodsResponseDto;
                        }).toList());
                        productResponseDtoList.add(mallProductResponseDto);
                    });
                    couponUpdateDetailVo.setProductList(productResponseDtoList);
                }
            }
        }

        if (!req.getIsUpdate()) {
            //查看详情内容
            couponUpdateDetailVo.setCouponContent(couponService.getCouponContent(coupon));
            couponUpdateDetailVo.setCouponValidityPeriod(couponService.getCouponValidityPeriod(coupon));
            couponUpdateDetailVo.setActivityCouponConfig(couponService.getActivityCouponConfig(coupon));
            couponUpdateDetailVo.setExpirationRemindConfig(couponService.geExpirationRemindConfig(coupon));
            AppRouteConfig appRouteConfig = appRouteConfigService.findById(coupon.getRouteId());
            if (Objects.nonNull(appRouteConfig)) {
                couponUpdateDetailVo.setRouteName(appRouteConfig.getTwoLevel());
            }
            ActivityCategory category = activityCategoryService.findById(coupon.getRouteId());
            if (Objects.nonNull(category)) {
                couponUpdateDetailVo.setRouteName("官方赛事列表-" + category.getTitle());
            }
        }

        // 国际化数据
        List<CouponI18n> couponI18ns = couponI18nService.selectCouponId(couponId);
        if (!CollectionUtils.isEmpty(couponI18ns)) {
            couponUpdateDetailVo.setValues(couponI18ns);
        }

        //封装币种金额
        couponUpdateDetailVo.setCurrencyList(getCurrencyList(couponId));
        couponUpdateDetailVo.setCountryCodeList(JsonUtil.readList(coupon.getCountryCode(), String.class));

        return couponUpdateDetailVo;
    }

    /**
     * 获取多币种金额
     *
     * @param couponId
     * @return
     */
    private List<CouponCurrencyRequestDto> getCurrencyList(Long couponId) {
        List<CouponCurrencyEntity> list = couponCurrencyMapper.selectByCouponIdAndIgnoreCurrency(couponId, null);
        if (CollectionUtils.isEmpty(list) || list.get(0) == null) {
            return new ArrayList<>();
        }
        return list.stream().map(o -> {
            CouponCurrencyRequestDto couponCurrency = new CouponCurrencyRequestDto();
            couponCurrency.setAmount(o.getAmount());
            couponCurrency.setMinTotalAmount(o.getMinTotalAmount());
            couponCurrency.setCurrencyCode(o.getCurrencyCode());
            couponCurrency.setCurrencySymbol(o.getCurrencySymbol());
            return couponCurrency;
        }).collect(Collectors.toList());
    }

    /**
     * 通过优惠券code查询商城优惠券
     */
    public Page<MallCouponDto> findMallCouponByCodes(MallCouponCodeReqDto req) {
        //查询商城优惠券
        CouponsPageQuery query = new CouponsPageQuery().setExchangeCodes(req.getExchangeCodes())
                .setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_2.type)
                .setStatus(CouponConstant.CouponStatusEnum.STATUS_1.type)
                .setCountryCode(req.getCountryCode())
                .setReceiveEndGt(ZonedDateTime.now()).setReceiveStartLe(ZonedDateTime.now())
                .setIsRetainPop(req.getIsRetainPop()).setIsAllGoodsCoupon(req.getIsAllGoodsCoupon());
        query.setPageNum(req.getPageNum());
        query.setPageSize(req.getPageSize());
        Page<Coupon> pageList = couponService.findPage(query);
        Page<MallCouponDto> result = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        List<Coupon> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        //转换优惠券Dto
        List<MallCouponDto> list = records.stream().map(MallCouponDto::new).toList();
        result.setRecords(list);
        return result;

    }
}
