package com.linzi.pitpat.admin.manager.message;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.model.Dto.request.ReachDataRequest;
import com.linzi.pitpat.admin.model.Dto.response.ReachDataExcelDto;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.dto.api.request.BasicPageDto;
import com.linzi.pitpat.data.messageservice.dto.request.EmailReachRequest;
import com.linzi.pitpat.data.messageservice.dto.response.EmailReachResp;
import com.linzi.pitpat.data.messageservice.model.entity.EmailReach;
import com.linzi.pitpat.data.messageservice.model.entity.EmailReachCondition;
import com.linzi.pitpat.data.messageservice.model.entity.EmailTemplate;
import com.linzi.pitpat.data.messageservice.model.entity.ReachPushEmail;
import com.linzi.pitpat.data.messageservice.model.entity.ReachRegisterData;
import com.linzi.pitpat.data.messageservice.service.EmailReachConditionService;
import com.linzi.pitpat.data.messageservice.service.EmailReachService;
import com.linzi.pitpat.data.messageservice.service.EmailTemplateService;
import com.linzi.pitpat.data.messageservice.service.ReachPushEmailService;
import com.linzi.pitpat.data.messageservice.service.ReachRegisterDataService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.shopify.ShopifyUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.file.FileUtils;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class EmailReachManager {
    @Autowired
    private EmailReachService emailReachService;
    @Autowired
    private EmailReachConditionService emailReachConditionService;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private ReachPushEmailService reachPushEmailService;
    @Autowired
    private ShopifyUserService shopifyUserService;
    @Autowired
    private ReachRegisterDataService reachRegisterDataService;
    @Autowired
    private ZnsUserEmailSendingRecordService znsUserEmailSendingRecordService;
    @Autowired
    private ZnsUserService znsUserService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 创建邮件触达
     *
     * @param request
     */
    public void addOrUpdateReach(MultipartFile file, EmailReachRequest request) {
        EmailReach emailReach = new EmailReach();
        //先删后添
        if (request.getReachId() != null) {
            emailReach = emailReachService.findById(request.getReachId());
            if (Objects.isNull(emailReach)) {
                throw new BaseException("编辑的邮件触达不存在");
            }
            if (!(Objects.equals(emailReach.getReachRangType(), request.getReachRangType()) && emailReach.getReachRangType() == 1 && Objects.isNull(file))) {
                reachPushEmailService.deleteByReachId(request.getReachId());
            }
            emailReachService.deleteById(request.getReachId());
            emailReachConditionService.deleteByReachId(request.getReachId());
            EmailTemplate emailTemplate = emailTemplateService.findByReachId(request.getReachId());
            emailTemplateService.deleteById(emailTemplate.getId());
            emailReach.setId(null);
        }
        //新增触达表
        EmailReach reachName = emailReachService.findByReachName(request.getReachName());
        if (Objects.nonNull(reachName)) {
            throw new BaseException("触达名称重复，请修改");
        }
        BeanUtil.copyPropertiesIgnoreNull(request, emailReach);
        emailReach.setModifier(SecurityUtils.getUsername());
        emailReach.setGmtModified(ZonedDateTime.now());
        emailReach.setFileName(Objects.nonNull(file) ? file.getOriginalFilename() : emailReach.getFileName());
        if (Objects.nonNull(emailReach.getId())) {
            emailReachService.update(emailReach);
        } else {
            emailReachService.insert(emailReach);
        }
        //条件表
        EmailReachCondition emailReachCondition = new EmailReachCondition();
        BeanUtil.copyPropertiesIgnoreNull(request, emailReachCondition);
        emailReachCondition.setReachId(emailReach.getId());
        emailReachConditionService.insert(emailReachCondition);
        //邮件内容
        EmailTemplate emailTemplate = new EmailTemplate();
        emailTemplate.setTitle(request.getEmailTitle());
        emailTemplate.setContent(request.getEmailContent());
        emailTemplate.setSendEmail(request.getSendEmail());
        emailTemplate.setSendEmailId(request.getSendEmailId());
        emailTemplate.setReachId(emailReach.getId());
        emailTemplate.setTemplateName(request.getReachName());
        emailTemplateService.save(emailTemplate);
        //邮件触达范围
        if (Objects.equals(request.getReachRangType(), 1)) {
            if (Objects.isNull(file)) {
                List<ReachPushEmail> list = reachPushEmailService.findByReachId(request.getReachId());
                Long newId = emailReach.getId();
                list.forEach(s -> s.setReachId(newId));
                reachPushEmailService.batchUpdate(list);
                return;
            }
            Long reachId = emailReach.getId();
            List<String> emails = FileUtils.readFirstColumn(file);
            List<ReachPushEmail> collect = emails.stream().map(email -> {
                ReachPushEmail reachPushEmail = new ReachPushEmail();
                reachPushEmail.setEmail(email);
                reachPushEmail.setEmailAddressEn(email);
                reachPushEmail.setReachId(reachId);
                return reachPushEmail;
            }).toList();
            taskExecutor.execute(() -> reachPushEmailService.batchInsert(collect));
        } else {
            Long reachId = emailReach.getId();
            ZonedDateTime date = DateUtil.addMonths(ZonedDateTime.now(), -1);
            List<ShopifyUserEntity> list = shopifyUserService.findListByTime(date);
            List<ReachPushEmail> collect = list.stream().filter(s -> Objects.nonNull(s.getEmailAddressEn()) && StringUtils.hasText(s.getEmailAddressEn())).map(ShopifyUserEntity -> {
                ReachPushEmail reachPushEmail = new ReachPushEmail();
                reachPushEmail.setEmail(ShopifyUserEntity.getEmailAddressEn());
                reachPushEmail.setEmailAddressEn(ShopifyUserEntity.getEmailAddressEn());
                reachPushEmail.setReachId(reachId);
                return reachPushEmail;
            }).toList();
            taskExecutor.execute(() -> reachPushEmailService.batchInsert(collect));
        }

    }

    /**
     * 邮件触达详情
     */
    public EmailReachResp detail(Long reachId) {
        EmailReach emailReach = emailReachService.findById(reachId);
        if (Objects.isNull(emailReach)) {
            throw new BaseException("没查询到结果，ID不正确");
        }
        EmailReachResp emailReachResp = new EmailReachResp();
        BeanUtil.copyPropertiesIgnoreNull(emailReach, emailReachResp);
        emailReachResp.setReachId(reachId);
        emailReachResp.setModifyTime(emailReach.getGmtModified());
        EmailReachCondition reachCondition = emailReachConditionService.findByReachId(reachId);
        if (Objects.nonNull(reachCondition)) {
            emailReachResp.setMaxTime(reachCondition.getMaxTime());
            emailReachResp.setNotPushDay(reachCondition.getNotPushDay());
            emailReachResp.setPurchaseDay(reachCondition.getPurchaseDay());
        }
        EmailTemplate emailTemplate = emailTemplateService.findByReachId(reachId);
        if (Objects.nonNull(emailTemplate)) {
            emailReachResp.setEmailTitle(emailTemplate.getTitle());
            emailReachResp.setEmailContent(emailTemplate.getContent());
            emailReachResp.setSendEmail(emailTemplate.getSendEmail());
            emailReachResp.setSendEmailId(emailTemplate.getSendEmailId());
        }
        List<ReachRegisterData> reachId30Day = reachRegisterDataService.findByReachId30Day(reachId);
        emailReachResp.setReachDate(reachId30Day);
        return emailReachResp;
    }

    /**
     * 改变触达状态
     *
     * @param reachId
     * @param status
     * @return
     */
    public void changeStatus(Long reachId, Integer status) {
        EmailReach emailReach = emailReachService.findById(reachId);
        if (Objects.isNull(emailReach)) {
            throw new BaseException("没查询到结果，ID不正确");
        }
        emailReach.setStatus(status);
        emailReachService.update(emailReach);
    }

    /**
     * 删除
     *
     * @param reachId
     * @return
     */
    public void delete(Long reachId) {
        EmailReach emailReach = emailReachService.findById(reachId);
        if (Objects.isNull(emailReach)) {
            throw new BaseException("没查询到结果，ID不正确");
        }
        emailReachService.deleteById(reachId);
    }

    /**
     * 列表
     *
     * @return
     */
    public Page<EmailReachResp> list(BasicPageDto request) {
        Page<EmailReach> page = emailReachService.page(new Page<>(request.getPageNum(), request.getPageSize()));
        List<EmailReach> reaches = page.getRecords();
        List<EmailReachResp> reachResp = new ArrayList<>();
        reaches.forEach(s -> {
            EmailReachResp emailReachResp = this.detail(s.getId());
            reachResp.add(emailReachResp);
        });
        Page<EmailReachResp> emailReachRespPage = new Page<>();
        emailReachRespPage.setTotal(page.getTotal());
        emailReachRespPage.setRecords(reachResp);
        emailReachRespPage.setCurrent(page.getCurrent());
        emailReachRespPage.setSize(page.getSize());
        return emailReachRespPage;
    }

    public List<ReachDataExcelDto> reachData(ReachDataRequest reachDataRequest) {
        List<ReachDataExcelDto> result = new ArrayList<>();
        String queryDate = reachDataRequest.getQueryDate();
        ZonedDateTime date = DateTimeUtil.parse(queryDate);
        List<ZnsUserEmailSendingRecordEntity> sendingRecordList = znsUserEmailSendingRecordService.findByReachIdAndDate(reachDataRequest.getReachId(), date);
        if (CollectionUtils.isEmpty(sendingRecordList)) {
            return result;
        }
        sendingRecordList.forEach(s -> {
            ReachDataExcelDto reachDataExcelDto = new ReachDataExcelDto();
            reachDataExcelDto.setEmailAddress(s.getEmailAddressEn());
            ZnsUserEntity znsUserEntity = znsUserService.findByEmail(s.getEmailAddressEn());
            reachDataExcelDto.setRegisterStatus(Objects.isNull(znsUserEntity) ? "未注册" : "已注册");
            List<ZnsUserEmailSendingRecordEntity> byReachIdAndEmail = znsUserEmailSendingRecordService.findByReachIdAndEmail(s.getReachId(), s.getEmailAddressEn());
            reachDataExcelDto.setReachCount(byReachIdAndEmail.size());
            result.add(reachDataExcelDto);
        });
        return result;
    }

}
