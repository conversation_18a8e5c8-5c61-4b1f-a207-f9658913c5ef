package com.linzi.pitpat.admin.courseservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.courseservice.model.entity.CourseCategory;
import com.linzi.pitpat.data.courseservice.model.entity.CourseI18nEntity;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseActionEntity;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.model.query.CourseActionQuery;
import com.linzi.pitpat.data.courseservice.model.query.CourseQuery;
import com.linzi.pitpat.data.courseservice.model.request.CourseListRequest;
import com.linzi.pitpat.data.courseservice.model.response.CourseBaseInfoI18nDto;
import com.linzi.pitpat.data.courseservice.model.response.CourseDetailI18nDto;
import com.linzi.pitpat.data.courseservice.model.vo.CourseDetailVoPo;
import com.linzi.pitpat.data.courseservice.service.CourseCategoryService;
import com.linzi.pitpat.data.courseservice.service.CourseI18nService;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseActionService;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 课程管理
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/course")
@Slf4j
public class CourseController extends BaseController {
    @Resource
    private ZnsCourseService courseService;
    @Resource
    private ZnsCourseActionService courseActionService;
    @Resource
    private CourseCategoryService courseCategoryService;
    @Resource
    private CourseI18nService courseI18nService;

    @PostMapping("/list")
    public Result<Page<ZnsCourseEntity>> list(@RequestBody CourseListRequest request) {
        CourseQuery query = CourseQuery.builder()
                .isDelete(0L)
                .id(request.getCourseId())
                .status(request.getStatus())
                .courseName(request.getCourseName())
                .categoryId(request.getCategoryId())
                .courseType(request.getCourseType())
                .recommended(request.getRecommended())
                .difficulty(request.getDifficulty())
                .minModifyStartTime(request.getModifyStartTime())
                .maxModifyStartTime(request.getModifyEndTime())
                .isPlusCourse(request.getIsPlusCourse())
                .deviceType(request.getDeviceType())
                .build();
        ArrayList<OrderItem> orders = Lists.newArrayList();
        if (StringUtils.hasText(request.getOrderType()) && StringUtils.hasText(request.getOrderField())) {
            orders.add("asc".equals(request.getOrderType()) ? OrderItem.asc(request.getOrderField()) : OrderItem.desc(request.getOrderField()));
        }
        if (StringUtils.hasText(request.getOrderType()) && StringUtils.hasText(request.getOrderField())) {
            orders.add(OrderItem.desc("modify_time"));
        }
        query.setOrders(orders);
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        Page<ZnsCourseEntity> page = courseService.findPage(query);
        return CommonResult.success(page);
    }

    @GetMapping("/dropList")
    public Result dropList() {
        CourseQuery query = CourseQuery.builder()
                .isDelete(0L)
                .status(1)
                .build();
        List<ZnsCourseEntity> list = courseService.findList(query);
        return CommonResult.success(list);
    }

    @GetMapping("/getInfo")
    public Result<CourseDetailVoPo> getInfo(Long id) {
        ZnsCourseEntity courseEntity = courseService.selectById(id);
        if (Objects.isNull(courseEntity)) {
            throw new BaseException("课程不存在");
        }
        CourseDetailVoPo vo = new CourseDetailVoPo();
        BeanUtils.copyProperties(courseEntity, vo);

        //获取课程基本信息多语言参数
        List<CourseI18nEntity> courseI18nList = courseI18nService.selectByCourseId(id);
        List<CourseBaseInfoI18nDto> baseInfoI18nList = BeanUtil.copyBeanList(courseI18nList, CourseBaseInfoI18nDto.class);
        if (CollectionUtils.isEmpty(baseInfoI18nList)) {
            //老数据默认英语
            baseInfoI18nList = new ArrayList<>(1);
            CourseBaseInfoI18nDto courseBaseInfoI18nDto = BeanUtil.copyBean(courseEntity, CourseBaseInfoI18nDto.class);
            courseBaseInfoI18nDto.setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
            courseBaseInfoI18nDto.setLanguageName(I18nConstant.LanguageCodeEnum.en_US.getName());
            baseInfoI18nList.add(courseBaseInfoI18nDto);
        }
        vo.setBaseInfoI18nList(baseInfoI18nList);

        //获取动作列表多语言参数
        CourseActionQuery build = CourseActionQuery.builder()
                .courseId(id)
                .isDelete(0).build();
        build.setOrders(Arrays.asList(OrderItem.asc("sort")));
        List<ZnsCourseActionEntity> list = courseActionService.findList(build);
        Map<String, List<ZnsCourseActionEntity>> languageMap = list.stream().collect(Collectors.groupingBy(ZnsCourseActionEntity::getLanguageCode));
        List<CourseDetailI18nDto> contentI18nList = new ArrayList<>();
        for (Map.Entry<String, List<ZnsCourseActionEntity>> entry : languageMap.entrySet()) {
            String languageCode = entry.getKey();
            List<ZnsCourseActionEntity> value = entry.getValue();
            Map<Integer, List<ZnsCourseActionEntity>> map = value.stream().collect(Collectors.groupingBy(ZnsCourseActionEntity::getStageType));
            CourseDetailI18nDto i18nDto = new CourseDetailI18nDto();
            i18nDto.setWarmUpPhase(map.get(1));
            i18nDto.setExercisePhase(map.get(2));
            i18nDto.setAdjustPhase(map.get(3));
            i18nDto.setActionList(map.get(0));
            i18nDto.setLanguageCode(languageCode);
            I18nConstant.LanguageCodeEnum languageCodeEnum = I18nConstant.LanguageCodeEnum.findByCode(languageCode);
            if (languageCodeEnum != null) {
                i18nDto.setLanguageName(languageCodeEnum.getName());
            }
            contentI18nList.add(i18nDto);
        }
        vo.setContentI18nList(contentI18nList);
        return CommonResult.success(vo);
    }

    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "课程管理", businessType = BusinessType.UPDATE)
    public Result edit(@RequestBody CourseDetailVoPo po) {
        po.fixLanguageCodeMiss();
        Result ajaxResult = checkCourse(po);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        courseService.editCourse(po, SecurityUtils.getUsername());
        courseActionService.editAction(po, SecurityUtils.getUsername());
        return CommonResult.success();
    }

    @GetMapping("/updateStatus")
    @Log(title = "课程管理", businessType = BusinessType.UPDATE)
    public Result updateStatus(Long id, Integer status) {
        ZnsCourseEntity delete = new ZnsCourseEntity();
        delete.setId(id);
        delete.setStatus(status);
        delete.setModifyTime(ZonedDateTime.now());
        delete.setModified(SecurityUtils.getUsername());
        courseService.updateById(delete);
        return CommonResult.success();
    }

    @GetMapping("/delete")
    @Log(title = "课程管理", businessType = BusinessType.DELETE)
    public Result delete(Long id) {
        courseService.delete(id, SecurityUtils.getUsername());

        return CommonResult.success();
    }

    @GetMapping("/testAcitivity")
    public Result testAcitivity() {
        return CommonResult.success();
    }

    /**
     * 检查课程
     *
     * @param po
     * @return
     */
    private Result checkCourse(CourseDetailVoPo po) {
        if (Objects.isNull(po.getCategoryId()) || po.getCategoryId() == 0) {
            return CommonResult.fail("类目不能为空");
        }
        if (Objects.isNull(po.getDifficulty())) {
            return CommonResult.fail("难度不能为空");
        }
        if (Objects.isNull(po.getDefaultParticipantsNumber())) {
            po.setDefaultParticipantsNumber(0);
        }
        if (Objects.isNull(po.getEstimatedHeatConsumption())) {
            return CommonResult.fail("预计消耗热量不能为空");
        }
        if (Objects.isNull(po.getContentI18nList())) {
            return CommonResult.fail("课程内容不能为空");
        }
        if (Objects.isNull(po.getBaseInfoI18nList())) {
            return CommonResult.fail("基本信息不能为空");
        }
        if (Objects.isNull(po.getCourseType())) {
            return CommonResult.fail("课程类型不能为空");
        }
        /*if (!StringUtils.hasText(po.getCourseDesc())) {
            return CommonResult.fail("课程说明不能为空");
        }
        if (!StringUtils.hasText(po.getTrainingSuggestions())) {
            return CommonResult.fail("训练建议不能为空");
        }
        if (!StringUtils.hasText(po.getIntendedFor())) {
            return CommonResult.fail("适用人群不能为空");
        }
        if (!StringUtils.hasText(po.getTabooPopulation())) {
            return CommonResult.fail("禁忌人群不能为空");
        }*/
        if (Objects.isNull(po.getIsPlusCourse())) {
            return CommonResult.fail("是否vip课程不能为空");
        }
        if (Objects.isNull(po.getDefaultLangCode())) {
            return CommonResult.fail("默认语言不能为空");
        }
        if (Integer.valueOf(0).equals(po.getRecommended())) {
            if (Objects.isNull(po.getRecommendPlaceSort())) {
                po.setRecommendPlaceSort(1);
            }
            if (!StringUtils.hasText(po.getRecommendPoster())) {
                return CommonResult.fail("推荐宣传图不能为空");
            }
        }
        if (StringUtils.hasText(po.getEquipmentType())) {
            po.setEquipmentTypeName(StringUtil.getEquipmentTypeName(po.getEquipmentType()));
        }
        if (po.getCourseType() == 0) {
            po.setCourseDuration(0);
        }
        CourseCategory category = courseCategoryService.selectCourseCategoryById(po.getCategoryId());
        po.setCategoryName(category.getCategoryName());

        //校验多语言内容
        Map<String, CourseDetailI18nDto> contentI18nMap = po.getContentI18nList().stream().collect(Collectors.toMap(CourseDetailI18nDto::getLanguageCode, Function.identity(), (k1, k2) -> k2));
        String defaultLangCode = po.getDefaultLangCode();
        for (CourseBaseInfoI18nDto baseInfoI18nDto : po.getBaseInfoI18nList()) {
            String languageCode = baseInfoI18nDto.getLanguageCode();
            CourseDetailI18nDto courseDetailI18nDto = contentI18nMap.get(languageCode);
            String languageName = baseInfoI18nDto.getLanguageName();
            I18nConstant.LanguageCodeEnum languageCodeEnum = I18nConstant.LanguageCodeEnum.findByCode(languageCode);
            if (languageCodeEnum != null) {
                languageName = languageCodeEnum.getName();
            }
            //判断是否必填
            boolean notEmpty = defaultLangCode.equals(languageCode);
            if (!notEmpty) {
                //不是必填,判断有没有填内容
                boolean isCourseName = StringUtils.hasText(baseInfoI18nDto.getCourseName());
                boolean isCourseDesc = StringUtils.hasText(baseInfoI18nDto.getCourseDesc());
                boolean isSuggestions = StringUtils.hasText(baseInfoI18nDto.getTrainingSuggestions());
                boolean isIntendedFor = StringUtils.hasText(baseInfoI18nDto.getIntendedFor());
                boolean isTabooPopulation = StringUtils.hasText(baseInfoI18nDto.getTabooPopulation());
                boolean isbBackgroundPicture = StringUtils.hasText(baseInfoI18nDto.getBackgroundPicture());
                boolean isActionList;
                if (po.getCourseType() == 0) {
                    //变速跑- 热身阶段/锻炼阶段/调整阶段只要填了就必填
                    isActionList = courseActionIsNotEmpty(courseDetailI18nDto.getAdjustPhase()) || courseActionIsNotEmpty(courseDetailI18nDto.getWarmUpPhase()) || courseActionIsNotEmpty(courseDetailI18nDto.getExercisePhase());
                } else {
                    //视频跑-课程内容只要填了就必填
                    isActionList = !CollectionUtils.isEmpty(courseDetailI18nDto.getActionList());
                }
                //只要有一个填了其他都是必填
                notEmpty = isCourseName || isCourseDesc || isSuggestions || isIntendedFor || isTabooPopulation || isbBackgroundPicture || isActionList;
            }
            if (!notEmpty) {
                //非必填不校验
                continue;
            }

            //校验基础信息
            checkBaseInfo(baseInfoI18nDto, languageName);

            //校验课程内容
            if (courseDetailI18nDto != null) {
                checkAction(courseDetailI18nDto, languageName, po, baseInfoI18nDto.getCourseVideo());
            }
        }
        return null;
    }

    /**
     * 课程内容是否为空,true: 不为空,false:为空
     * <p>
     * "actionName": "",
     * "actionDuration": "",
     * "velocity": "",
     * "gradient": ""
     *
     * @param courseActions
     * @return
     */
    private boolean courseActionIsNotEmpty(List<ZnsCourseActionEntity> courseActions) {
        if (CollectionUtils.isEmpty(courseActions)) {
            return false;
        }
        for (ZnsCourseActionEntity courseAction : courseActions) {
            boolean isActionName = StringUtils.hasText(courseAction.getActionName());
            boolean isDuration = !ObjectUtils.isEmpty(courseAction.getActionDuration());
            boolean isVelocity = !ObjectUtils.isEmpty(courseAction.getVelocity());
            boolean isGradient = !ObjectUtils.isEmpty(courseAction.getGradient());
            if (isActionName || isDuration || isVelocity || isGradient) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验课程内容
     *
     * @param courseDetailI18nDto
     * @param languageName
     */
    private void checkAction(CourseDetailI18nDto courseDetailI18nDto, String languageName, CourseDetailVoPo po, String courseVideo) {
        int courseType = po.getCourseType();  //课程类型，0：变速跑，1：视频
        Integer courseDuration = po.getCourseDuration(); // 课程时长
        if (courseType == 0) {
            //变速跑 - 检查课程动作
            int totalTime1 = checkCourseActionList(courseDetailI18nDto.getWarmUpPhase(), 1, po, languageName, "热身阶段");
            int totalTime2 = checkCourseActionList(courseDetailI18nDto.getExercisePhase(), 2, po, languageName, "锻炼阶段");
            int totalTime3 = checkCourseActionList(courseDetailI18nDto.getAdjustPhase(), 3, po, languageName, "调整阶段");

            //校验课程时间
            int totalTime = totalTime1 + totalTime2 + totalTime3;
            if (totalTime == 0 || totalTime % 60 != 0) {
                throw new BaseException(languageName + "的课程时长需为需为60的倍数，请调整任何一个动作时长");
            }
        } else {
            //视频 - 课程视频不能为空
            if (!StringUtils.hasText(courseVideo)) {
                throw new BaseException(languageName + "课程视频不能为空");
            }
            //校验课程内容
            checkVideoCourseActionList(courseDetailI18nDto, languageName, courseDuration);
        }
    }

    /**
     * 校验基础信息
     *
     * @param baseInfoI18nDto
     * @param languageName
     */
    private void checkBaseInfo(CourseBaseInfoI18nDto baseInfoI18nDto, String languageName) {
        if (!StringUtils.hasText(baseInfoI18nDto.getCourseName())) {
            throw new BaseException(languageName + "课程名称不能为空");
        }
        if (!StringUtils.hasText(baseInfoI18nDto.getCourseDesc())) {
            throw new BaseException(languageName + "课程介绍不能为空");
        }
        if (!StringUtils.hasText(baseInfoI18nDto.getTrainingSuggestions())) {
            throw new BaseException(languageName + "注意事项不能为空");
        }
        if (!StringUtils.hasText(baseInfoI18nDto.getIntendedFor())) {
            throw new BaseException(languageName + "适用人群不能为空");
        }
        if (!StringUtils.hasText(baseInfoI18nDto.getTabooPopulation())) {
            throw new BaseException(languageName + "禁忌人群不能为空");
        }
        if (!StringUtils.hasText(baseInfoI18nDto.getBackgroundPicture())) {
            throw new BaseException(languageName + "课程封面图不能为空");
        }
    }


    private void checkVideoCourseActionList(CourseDetailI18nDto courseDetailI18nDto, String languageName, Integer courseDuration) {
        List<ZnsCourseActionEntity> actionList = courseDetailI18nDto.getActionList();
        if (CollectionUtils.isEmpty(actionList)) {
            throw new BaseException(languageName + "课程内容不能为空");
        }
        Integer totalCourseDuration = 0;
        for (ZnsCourseActionEntity actionEntity : actionList) {
            if (!StringUtils.hasText(actionEntity.getActionName())) {
                throw new BaseException(languageName + "动作名称不能为空");
            }
            if (!StringUtils.hasText(actionEntity.getActionPicture())) {
                throw new BaseException(languageName + "动作封面图不能为空");
            }
            if (Objects.isNull(actionEntity.getActionDuration())) {
                throw new BaseException(languageName + "动作时长不能为空");
            }
            actionEntity.setIsRecordTime(1);
            totalCourseDuration += actionEntity.getActionDuration();
        }
        //校验课程时间
        if (!courseDuration.equals(totalCourseDuration)) {
            throw new BaseException(languageName + "阶段总时长和课程总时长时长不一致，请重新校正");
        }
    }

    /**
     * 检查动作组
     *
     * @param phaseActions
     * @param stageType
     * @param po
     */
    private Integer checkCourseActionList(List<ZnsCourseActionEntity> phaseActions, int stageType, CourseDetailVoPo po, String languageName, String contextName) {
        if (CollectionUtils.isEmpty(phaseActions)) {
            throw new BaseException(languageName + contextName + "不能为空", 500);
        }
        Integer courseDuration = po.getCourseDuration();
        int totalTime = 0;
        for (int i = 0; i < phaseActions.size(); i++) {
            ZnsCourseActionEntity actionEntity = phaseActions.get(i);
            checkCourseAction(actionEntity, languageName);
            actionEntity.setStageType(stageType);
            actionEntity.setSort(i);
            totalTime = totalTime + Optional.ofNullable(actionEntity.getActionDuration()).orElse(0);
            if (po.getDefaultLangCode().equals(actionEntity.getLanguageCode())) {
                //只计算默认语言的时长
                courseDuration = courseDuration + actionEntity.getActionDuration();
            }
        }
        po.setCourseDuration(courseDuration);
        return totalTime;
    }

    /**
     * 检查动作
     *
     * @param actionEntity
     */
    private void checkCourseAction(ZnsCourseActionEntity actionEntity, String languageName) {
        if (!StringUtils.hasText(actionEntity.getActionName())) {
            throw new BaseException(languageName + "动作名称不能为空", 500);
        }
        if (Objects.isNull(actionEntity.getActionDuration())) {
            throw new BaseException(languageName + "动作时长不能为空", 500);
        }
        if (Objects.isNull(actionEntity.getVelocity())) {
            throw new BaseException(languageName + "跑步速度不能为空", 500);
        }
        if (Objects.isNull(actionEntity.getGradient())) {
            throw new BaseException(languageName + "坡度不能为空", 500);
        }
    }
}
