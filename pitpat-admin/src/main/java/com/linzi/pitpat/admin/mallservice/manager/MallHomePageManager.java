package com.linzi.pitpat.admin.mallservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.mallservice.convert.HomeModuleGoodConverter;
import com.linzi.pitpat.admin.mallservice.convert.MallHomeModuleConverter;
import com.linzi.pitpat.admin.mallservice.convert.MallHomeModuleI8nConverter;
import com.linzi.pitpat.admin.mallservice.convert.MallHomePageConverter;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.mallservice.dto.request.HomeModuleGoodResponseDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallHomeModuleRequestDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallHomeRequestDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallModuleI8nRequestDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallProductRequestDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallGoodsResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallHomeJumpDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallHomeModuleResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallHomePageResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallProductResponseDto;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsI18nEntity;
import com.linzi.pitpat.data.mallservice.model.entity.HomeModuleGood;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomeModule;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomeModuleI8n;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomePage;
import com.linzi.pitpat.data.mallservice.model.entity.ProductDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsFileEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsI18nQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsQuery;
import com.linzi.pitpat.data.mallservice.model.query.HomeModuleGoodQuery;
import com.linzi.pitpat.data.mallservice.model.query.HomeModuleI8nQuery;
import com.linzi.pitpat.data.mallservice.model.query.HomeModuleQuery;
import com.linzi.pitpat.data.mallservice.model.query.ProductPageQuery;
import com.linzi.pitpat.data.mallservice.service.GoodsI18nService;
import com.linzi.pitpat.data.mallservice.service.HomeModuleGoodService;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleI8nService;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleService;
import com.linzi.pitpat.data.mallservice.service.MallHomePageService;
import com.linzi.pitpat.data.mallservice.service.ProductService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsFileService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.messageservice.dao.MallHomeModuleI8nMapper;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商城首页 聚合类
 *
 * @since 2024-10-31
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MallHomePageManager {

    private final MallHomePageService mallHomePageService;
    private final MallHomeModuleService mallHomeModuleService;
    private final MallHomeModuleI8nService mallHomeModuleI8nService;
    private final MallHomeModuleI8nMapper mallHomeModuleI8nMapper;
    private final HomeModuleGoodService homeModuleGoodService;
    private final MallHomePageConverter mallHomePageConverter;
    private final HomeModuleGoodConverter homeModuleGoodConverter;
    private final MallHomeModuleConverter mallHomeModuleConverter;
    private final MallHomeModuleI8nConverter mallHomeModuleI8nConverter;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final GoodsI18nService goodsI18nService;
    private final ProductService productService;
    private final ZnsGoodsFileService znsGoodsFileService;

    //添加首页配置
    @Transactional(rollbackFor = Exception.class)
    public Long saveHomePage(MallHomeRequestDto mallHomeRequestDto) {

        MallHomePage homePage = mallHomePageConverter.toEntry(mallHomeRequestDto);
        mallHomePageService.insert(homePage);

        List<MallHomeModuleRequestDto> modules = mallHomeRequestDto.getModules();
        if (CollectionUtils.isEmpty(modules)) {
            return homePage.getId();
        }
        for (MallHomeModuleRequestDto module : modules) {

            MallHomeModule mallHomeModule = new MallHomeModule();
            BeanUtils.copyProperties(module, mallHomeModule);
            mallHomeModule.setSourceEquipmentModel(JsonUtil.writeString(module.getSourceEquipmentModel()));
            mallHomeModule.setMallHomeId(homePage.getId());
            mallHomeModuleService.insert(mallHomeModule);
            Long moduleId = mallHomeModule.getId();

            List<MallModuleI8nRequestDto> i8nList = module.getI8nList();
            List<MallHomeModuleI8n> collect = i8nList.stream().map(s -> {
                MallHomeModuleI8n mallHomeModuleI8n = new MallHomeModuleI8n();
                BeanUtils.copyProperties(s, mallHomeModuleI8n);
                mallHomeModuleI8n.setModuleId(moduleId);
                mallHomeModuleI8n.setContent(JsonUtil.writeString(s.getContents()));
                return mallHomeModuleI8n;
            }).collect(Collectors.toList());

            mallHomeModuleI8nMapper.insert(collect);
        }
        return homePage.getId();
    }

    //修改首页配置
    @Transactional(rollbackFor = Exception.class)
    public void updateHomePage(MallHomeRequestDto mallHomeRequestDto) {
        MallHomePage mallHomePage = mallHomePageService.findById(mallHomeRequestDto.getId());
        if (Objects.isNull(mallHomePage)) {
            throw new BaseException("id不正确");
        }

        BeanUtils.copyProperties(mallHomeRequestDto, mallHomePage);
        //修改首页表
        mallHomePageService.update(mallHomePage);

        //删除首页对应数据表
        deleteById(mallHomeRequestDto.getId());

        //新增数据
        List<MallHomeModuleRequestDto> modules = mallHomeRequestDto.getModules();
        for (MallHomeModuleRequestDto module : modules) {

            MallHomeModule mallHomeModule = new MallHomeModule();
            BeanUtils.copyProperties(module, mallHomeModule);
            mallHomeModule.setSourceEquipmentModel(JsonUtil.writeString(module.getSourceEquipmentModel()));
            mallHomeModule.setMallHomeId(mallHomePage.getId());
            mallHomeModuleService.insert(mallHomeModule);
            Long moduleId = mallHomeModule.getId();

            List<MallModuleI8nRequestDto> i8nList = module.getI8nList();
            List<MallHomeModuleI8n> collect = i8nList.stream().map(s -> {
                MallHomeModuleI8n mallHomeModuleI8n = new MallHomeModuleI8n();
                mallHomeModuleI8n.setModuleId(moduleId);
                mallHomeModuleI8n.setLanguageCode(s.getLanguageCode());
                mallHomeModuleI8n.setLanguageName(s.getLanguageName());
                mallHomeModuleI8n.setTitle(s.getTitle());
                mallHomeModuleI8n.setContent(JsonUtil.writeString(s.getContents()));
                mallHomeModuleI8n.setVideoUrl(s.getVideoUrl());
                mallHomeModuleI8n.setVideoCoverUrl(s.getVideoCoverUrl());
                return mallHomeModuleI8n;
            }).collect(Collectors.toList());
            //模块i8n表新增
            mallHomeModuleI8nMapper.insert(collect);

            List<HomeModuleGoodResponseDto> goodsList = module.getGoodsList();
            //模块商品新增
            if (!CollectionUtils.isEmpty(goodsList)) {
                List<HomeModuleGood> goodsInfo = goodsList.stream().map(homeModuleGoodConverter::toEntry).collect(Collectors.toList());
                goodsInfo.forEach(s -> s.setModuleId(moduleId));
                homeModuleGoodService.savaBatch(goodsInfo);
            }
        }
    }

    //获取首页详情
    public MallHomePageResponseDto getHomeDetail(Long homePageId) {
        MallHomePage byId = mallHomePageService.findById(homePageId);
        if (Objects.isNull(byId)) {
            throw new BaseException("id不正确");
        }
        MallHomePageResponseDto pageResponseDto = mallHomePageConverter.toResponseDto(byId);
        List<MallHomeModuleResponseDto> modules = new ArrayList<>();
        List<MallHomeModule> list = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(homePageId));
        for (MallHomeModule mallHomeModule : list) {
            MallHomeModuleResponseDto responseDto = mallHomeModuleConverter.toResponseDto(mallHomeModule);
            List<MallHomeModuleI8n> listByQuery = mallHomeModuleI8nService.findListByQuery(new HomeModuleI8nQuery().setModuleId(mallHomeModule.getId()));
            List<MallModuleI8nRequestDto> i8nList = new ArrayList<>();
            listByQuery.forEach(s -> {
                MallModuleI8nRequestDto i8nRequest = mallHomeModuleI8nConverter.toResponseDto(s);
                i8nRequest.setContents(JsonUtil.readList(s.getContent(), MallHomeJumpDto.class));
                i8nList.add(i8nRequest);
            });
            List<HomeModuleGood> moduleGoods = homeModuleGoodService.findListByQuery(new HomeModuleGoodQuery().setModuleId(mallHomeModule.getId()));
            if (!CollectionUtils.isEmpty(moduleGoods)) {
                List<HomeModuleGoodResponseDto> goodResponseDtos = moduleGoods.stream().map(homeModuleGoodConverter::toResponseDto).collect(Collectors.toList());
                goodResponseDtos.forEach(s -> {
                    ZnsGoodsEntity goods = znsGoodsService.findById(s.getGoodsId());
                    s.setGoodsName(Objects.nonNull(goods) ? goods.getTitle() : null);
                    s.setProductId(Objects.nonNull(goods) ? goods.getProductId() : null);
                });
                responseDto.setGoodsList(goodResponseDtos);
            }
            responseDto.setI8nList(i8nList);
            modules.add(responseDto);
        }
        pageResponseDto.setModules(modules);
        return pageResponseDto;
    }

    //通过模块ID删除
    public void deleteById(Long id) {
        List<MallHomeModule> moduleList = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(id));

        if (!CollectionUtils.isEmpty(moduleList)) {
            //模块表删除
            mallHomeModuleService.deleteByHomeId(id);
            //i8n删除 && 商品表删除
            moduleList.forEach(s -> {
                mallHomeModuleI8nService.deleteByModuleId(s.getId());
                homeModuleGoodService.deleteByModuleId(s.getId());
            });
        }
    }

    public Page<MallProductResponseDto> productPage(MallProductRequestDto productRequestDto) {
        ProductPageQuery productPageQuery = new ProductPageQuery();
        BeanUtils.copyProperties(productRequestDto, productPageQuery);
        productPageQuery.setId(productRequestDto.getProductId());
        Page<ProductDo> page = productService.findPage(productPageQuery);
        Page<MallProductResponseDto> responsePage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ProductDo> productDoList = page.getRecords();
        if (CollectionUtils.isEmpty(productDoList)) {
            return responsePage;
        }
        List<MallProductResponseDto> collect = productDoList.stream().map(s -> {
            MallProductResponseDto mallProductResponseDto = new MallProductResponseDto();
            ZnsGoodsEntity defaultGoods = znsGoodsService.findById(s.getDefaultGoodId());
            mallProductResponseDto.setProductId(s.getId());
            if (Objects.nonNull(defaultGoods)) {
                GoodsI18nEntity defaultGoodsI8n = goodsI18nService.findByLanguageCodeOrDefault(
                        new GoodsI18nQuery().setGoodsId(defaultGoods.getId()).setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode()),
                        defaultGoods.getDefaultLangCode()
                );
                mallProductResponseDto.setStockCount(defaultGoods.getStockCount());
                mallProductResponseDto.setGoodsName(Objects.nonNull(defaultGoodsI8n) ? defaultGoodsI8n.getTitle() : null);
            }
            List<ZnsGoodsEntity> list = znsGoodsService.findList(new GoodsQuery().setProductId(s.getId()).setCountryCodeList(productRequestDto.getCountryCodeList()));
            List<MallGoodsResponseDto> goodsResponseDtoList = list.stream().map(goodsEntity -> {
                MallGoodsResponseDto mallGoodsResponseDto = new MallGoodsResponseDto();
                GoodsI18nEntity goodsI18nEntity = goodsI18nService.findByLanguageCodeOrDefault(
                        new GoodsI18nQuery().setGoodsId(goodsEntity.getId()).setLanguageCode(goodsEntity.getDefaultLangCode()),
                        goodsEntity.getDefaultLangCode()
                );
                List<ZnsGoodsFileEntity> i18nListFiles = znsGoodsFileService.getI18nListFiles(goodsEntity.getId(), goodsEntity.getDefaultLangCode());
                if (!CollectionUtils.isEmpty(i18nListFiles)) {
                    i18nListFiles.stream().filter(goodsFile -> Objects.equals(goodsFile.getFileType(), 3)).findFirst().ifPresent(goodsFile -> {
                        mallGoodsResponseDto.setVideoUrl(goodsFile.getFileUrl());
                    });
                    i18nListFiles.stream().filter(file -> Objects.equals(file.getFileType(), 1)).findFirst().ifPresent(file -> {
                        mallGoodsResponseDto.setGoodsCoverUrl(file.getFileUrl());
                    });
                }
                if (Objects.nonNull(goodsI18nEntity)) {
                    List<String> guideList = JsonUtil.readList(goodsI18nEntity.getShoppingGuide(), String.class);
                    if (!CollectionUtils.isEmpty(guideList)) {
                        guideList = guideList.stream()
                                .filter(StringUtils::hasText)
                                .collect(Collectors.toList());
                    }
                    mallGoodsResponseDto.setGuideTitle(guideList);
                }
                ZnsGoodsSkuEntity minPriceSku = znsGoodsSkuService.findMinPriceSku(goodsEntity.getId());
                mallGoodsResponseDto.setOriginalPrice(Objects.nonNull(minPriceSku) ? minPriceSku.getOriginalPrice() : goodsEntity.getOriginalPrice());
                mallGoodsResponseDto.setDiscountedPrice(Objects.nonNull(minPriceSku) ? minPriceSku.getSalePrice() : goodsEntity.getSalePrice());
                mallGoodsResponseDto.setGoodsId(goodsEntity.getId());
                mallGoodsResponseDto.setProductId(goodsEntity.getProductId());
                mallGoodsResponseDto.setGoodsName(Objects.nonNull(goodsI18nEntity) ? goodsI18nEntity.getTitle() : s.getTitle());
                mallGoodsResponseDto.setStockCount(goodsEntity.getStockCount());
                mallGoodsResponseDto.setCountryCode(goodsEntity.getCountryCode());
                mallGoodsResponseDto.setCountryName(goodsEntity.getCountryName());
                mallGoodsResponseDto.setStatus(goodsEntity.getStatus());
                return mallGoodsResponseDto;
            }).collect(Collectors.toList());
            mallProductResponseDto.setGoodsList(goodsResponseDtoList);
            return mallProductResponseDto;
        }).collect(Collectors.toList());
        responsePage.setRecords(collect);
        return responsePage;
    }

    public void changeStatus(Long id, Integer status) {
        MallHomePage hotspotPage = mallHomePageService.findById(id);
        if (Objects.isNull(hotspotPage)) {
            throw new BaseException("id不正确");
        }
        if (ZonedDateTime.now().isAfter(hotspotPage.getEndTime()) && Objects.equals(status, 0)) {
            throw new BaseException("配置已失效,无法开启");
        }
        List<MallHomeModule> listByQuery = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(id));
        if (CollectionUtils.isEmpty(listByQuery) && Objects.equals(status, 0)) {
            throw new BaseException("模块内容为空，无法开启");
        }
        hotspotPage.setStatus(status);
        mallHomePageService.update(hotspotPage);
    }
}
