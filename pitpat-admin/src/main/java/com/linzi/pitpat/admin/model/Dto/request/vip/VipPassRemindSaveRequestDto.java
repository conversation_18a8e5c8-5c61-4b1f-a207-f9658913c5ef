package com.linzi.pitpat.admin.model.Dto.request.vip;


import com.linzi.pitpat.admin.model.Dto.response.vip.VipPassRemindDetailResponseDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 16:08
 */
@Data
@NoArgsConstructor
public class VipPassRemindSaveRequestDto {
    /**
     * 内测/公开 0-内测 1-公开
     */
    private Integer betaVersion;
    /**
     * 提醒类型：提醒类型：1-首页弹窗；2-push；3-站内信；4-邮件
     */
    private Integer remindType;
    /**
     * 提醒明细
     */
    private List<VipPassRemindDetailResponseDto> details;

    /**
     * 当betaVersion为0才生效
     * 需要公开：0-无需公开；1-需要公开
     */
    private Integer open;

}
