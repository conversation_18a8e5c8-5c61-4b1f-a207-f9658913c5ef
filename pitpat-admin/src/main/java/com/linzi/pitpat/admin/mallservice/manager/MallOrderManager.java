package com.linzi.pitpat.admin.mallservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.query.PaypalPayQuery;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.converter.console.OrderConsoleConverter;
import com.linzi.pitpat.data.mallservice.converter.console.OrderLogisticsConsoleConverter;
import com.linzi.pitpat.data.mallservice.converter.console.OrderRemarkConverter;
import com.linzi.pitpat.data.mallservice.dto.console.request.OrderDeliveryRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.OrderRemarkRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.RefundAmountCalRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.RefundOrderSubmitRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderDetailResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderDetailsItemResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderGoodsListResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderItemRefundResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderListResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderLogisticsDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.RefundAmountCalResponseDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderListReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.erp.ErpOrderListReqDto;
import com.linzi.pitpat.data.mallservice.dto.response.erp.ErpOrderListRespDto;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.enums.OrderRefundConstant;
import com.linzi.pitpat.data.mallservice.model.entity.OrderCouponDetailDo;
import com.linzi.pitpat.data.mallservice.model.entity.OrderLogistics;
import com.linzi.pitpat.data.mallservice.model.entity.OrderLogisticsList;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRemark;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderItemQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderLogisticsQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderPageQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderRefundQuery;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.service.MallExchangeRateSwitchService;
import com.linzi.pitpat.data.mallservice.service.OrderCouponDetailService;
import com.linzi.pitpat.data.mallservice.service.OrderLogisticsListService;
import com.linzi.pitpat.data.mallservice.service.OrderLogisticsService;
import com.linzi.pitpat.data.mallservice.service.OrderRefundService;
import com.linzi.pitpat.data.mallservice.service.OrderRemarkService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.mallservice.util.SnowflakeOrderNoGenerator;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/14 13:54
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MallOrderManager {
    private final ZnsOrderService orderService;
    private final ZnsOrderItemService orderItemService;
    private final ZnsUserService userService;
    private final OrderRefundService orderRefundService;
    private final OrderRemarkService orderRemarkService;
    private final OrderLogisticsService orderLogisticsService;
    private final OrderLogisticsListService logisticsListService;
    private final OrderConsoleConverter orderConsoleConverter;
    private final OrderRemarkConverter orderRemarkConverter;
    private final OrderLogisticsConsoleConverter logisticsConsoleConverter;
    private final MallOrderBizService mallOrderBizService;
    private final OrderCouponDetailService orderCouponDetailService;
    private final CouponService couponService;
    private final UserCouponService userCouponService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final MallExchangeRateSwitchService mallExchangeRateSwitchService;
    private final CouponCurrencyService couponCurrencyService;
    private final PaypalPayService paypalPayService;
    private final RedissonClient redissonClient;

    /**
     * 订单列表
     *
     * @param po
     * @return
     */
    public Page<OrderListResponseDto> pageList(OrderListReqDto po) {
        OrderPageQuery orderPageQuery = orderConsoleConverter.toPageQuery(po);
        if (Objects.nonNull(po.getStatus()) && po.getStatus() == 1) {
            orderPageQuery.setStatusList(Lists.newArrayList(OrderStatusEnum.FINSHED.getStatus(), OrderStatusEnum.DELIVERED.getStatus()));
            orderPageQuery.setStatus(null);
        }
        if (Objects.equals(po.getOrderType(), 1)) {
            orderPageQuery.setSourceTypes(List.of(OrderConstant.OrderSourceEnum.SCORE_MALL.type));
        } else {
            orderPageQuery.setSourceTypes(OrderConstant.OrderSourceEnum.mallSourceTypeList());
        }
        Page page = orderService.findConsolePage(orderPageQuery);
        List<ZnsOrderEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        List<Long> orderIds = records.stream().map(ZnsOrderEntity::getId).collect(Collectors.toList());
        List<ZnsOrderItemEntity> itemList = orderItemService.findList(new OrderItemQuery().setOrderIds(orderIds));
        Map<Long, List<ZnsOrderItemEntity>> itemMap = itemList.stream().collect(Collectors.groupingBy(ZnsOrderItemEntity::getOrderId));
        List<OrderListResponseDto> recordDtoList = records.stream().map(r -> {
            List<ZnsOrderItemEntity> items = itemMap.get(r.getId());
            List<ZnsGoodsSkuEntity> list = znsGoodsSkuService.findList(new GoodsSkuQuery().setIds(items.stream().map(ZnsOrderItemEntity::getSkuId).collect(Collectors.toList())));
            OrderListResponseDto orderListResponseDto = orderConsoleConverter.toDto(r);
            //订单所有sku图片
            orderListResponseDto.setSkuImg(list.stream().map(ZnsGoodsSkuEntity::getPic).collect(Collectors.toList()));
            List<OrderGoodsListResponseDto> orderGoodsListResponseDtos = orderConsoleConverter.toItemDtoList(items);
            orderListResponseDto.setGoodsList(orderGoodsListResponseDtos);
            orderListResponseDto.setCurrency(mallExchangeRateSwitchService.getCurrencyByCountryCode(r.getCountryCode()));
            orderListResponseDto.setSourceType(r.getSourceType());
            return orderListResponseDto;
        }).collect(Collectors.toList());
        page.setRecords(recordDtoList);
        return page;
    }

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    public OrderDetailResponseDto detail(Long id) {
        ZnsOrderEntity order = orderService.findById(id);
        List<ZnsOrderItemEntity> itemList = orderItemService.findList(new OrderItemQuery().setOrderId(id));

        OrderDetailResponseDto orderDetailResponseDto = orderConsoleConverter.toDetailDto(order);
        ZnsUserEntity user = userService.findById(order.getUserId());
        orderDetailResponseDto.setEmailAddress(user.getEmailAddress());
        OrderRefundQuery orderRefundQuery = new OrderRefundQuery().setOrderId(id);
        orderRefundQuery.setOrders(List.of(OrderItem.desc("id")));
        OrderRefund orderRefund = orderRefundService.findOne(orderRefundQuery);
        if (orderRefund != null) {
            orderDetailResponseDto.setRefundId(orderRefund.getId());
        }

        List<OrderRemark> orderRemarks = orderRemarkService.selectOrderRemarkByList(order.getOrderNo());
        if (!CollectionUtils.isEmpty(orderRemarks)) {
            orderDetailResponseDto.setRemarks(orderRemarkConverter.toDtoList(orderRemarks));
        }
        List<OrderDetailsItemResponseDto> items = orderConsoleConverter.toDetailsItemDtoList(itemList);
        for (OrderDetailsItemResponseDto item : items) {
            //sku 实际支付 = 销售价总金额 - 优惠券总金额
            BigDecimal skuCouponAmount = Optional.ofNullable(item.getSkuCouponAmount()).orElse(BigDecimal.ZERO);
            BigDecimal actualAmount = item.getSalePrice().multiply(new BigDecimal(item.getCount())).subtract(skuCouponAmount).subtract(item.getAllowanceAmount());
            item.setActualAmount(actualAmount);
            item.setScore(order.getScore());
            itemList.stream().filter(s -> Objects.equals(s.getId(), item.getId())).findFirst().ifPresent(skuItem -> {
                item.setSkuImg(skuItem.getThumbnail());
            });
        }
        orderDetailResponseDto.setItemList(items);

        List<OrderLogistics> logisticsList = orderLogisticsService.findList(new OrderLogisticsQuery().setOrderNo(order.getOrderNo()).setDirectionType(0));
        if (!CollectionUtils.isEmpty(logisticsList)) {
            List<OrderLogisticsDto> logisticsDtoList = logisticsList.stream().map(l -> {
                OrderLogisticsDto orderLogisticsDto = logisticsConsoleConverter.toDto(l);
                OrderLogisticsQuery orderLogisticsQuery = new OrderLogisticsQuery().setLogisticsNo(orderLogisticsDto.getLogisticsNo());
                orderLogisticsQuery.setOrders(Lists.newArrayList(OrderItem.asc("logistics_time")));
                List<OrderLogisticsList> list = logisticsListService.findList(orderLogisticsQuery);
                orderLogisticsDto.setOrderLogisticsLists(logisticsConsoleConverter.toVoList(list));
                return orderLogisticsDto;
            }).collect(Collectors.toList());
            orderDetailResponseDto.setLogisticsList(logisticsDtoList);
        }

        //查询订单的使用的优惠券
        List<OrderCouponDetailDo> list = orderCouponDetailService.findListByOrderId(id);
        if (!CollectionUtils.isEmpty(list)) {
            OrderCouponDetailDo orderCouponDetailDo = list.get(0); //一个订单只能使用一个券，所以这里取第一张
            UserCoupon userCouponDo = userCouponService.findByQuery(new UserCouponQuery().setId(orderCouponDetailDo.getUserCouponId()));
            Optional.ofNullable(userCouponDo).map(item -> couponService.findDeletedById(item.getCouponId())).ifPresent(item -> {
                CouponCurrencyEntity couponCurrencyEntity = couponCurrencyService.selectByCouponIdAndCurrencyCode(item.getId(), order.getCurrencyCode());
                orderDetailResponseDto.setCouponId(item.getId());
                orderDetailResponseDto.setExchangeCode(item.getExchangeCode());
                orderDetailResponseDto.setDiscountMethod(item.getDiscountMethod());
                if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_2.type.equals(item.getDiscountMethod())) {
                    //折扣 ,优惠金额 = 折扣率 * 100%
                    orderDetailResponseDto.setCouponAmount(item.getDiscount().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                } else {
                    //金额 ,优惠金额 = 优惠券金额
                    orderDetailResponseDto.setCouponAmount(Objects.nonNull(couponCurrencyEntity.getAmount()) ? couponCurrencyEntity.getAmount() : item.getAmount());
                }
            });
        }

        //订单取消原因
        orderDetailResponseDto.setCancelReason(order.getCancelReason());
        orderDetailResponseDto.setCurrency(mallExchangeRateSwitchService.getCurrencyByCountryCode(order.getCountryCode()));
        orderDetailResponseDto.setScore(order.getScore());
        //三方流水号
        PaypalPayQuery build = PaypalPayQuery.builder().refId(order.getId()).build();
        build.setOrders(List.of(OrderItem.desc("gmt_create")));
        PaypalPay paypalPay = paypalPayService.findByQuery(build);
        orderDetailResponseDto.setTradeNo(Objects.nonNull(paypalPay) ? paypalPay.getTradeNo() : "");
        orderDetailResponseDto.setPayNo(Objects.nonNull(paypalPay) ? paypalPay.getPayNo() : "");
        orderDetailResponseDto.setSourceType(order.getSourceType());
        List<Integer> canRefundStatus = List.of(OrderStatusEnum.DELIVERED.getStatus(), OrderStatusEnum.WAIT_RECEIVE.getStatus());
        orderDetailResponseDto.setCanRefund(canRefundStatus.contains(order.getStatus()) && Objects.equals(order.getSourceType(), OrderConstant.OrderSourceEnum.NEW_PITPAT.type));
        return orderDetailResponseDto;
    }

    /**
     * 添加备注
     *
     * @param po
     * @param sysUser
     */
    public void addRemark(OrderRemarkRequestDto po, SysUser sysUser) {
        ZnsOrderEntity znsOrderEntity = orderService.findById(po.getOrderId());
        OrderRemark orderRemark = orderRemarkConverter.toDo(po);
        orderRemark.setOrderNo(znsOrderEntity.getOrderNo());
        orderRemark.setSysUserId(sysUser.getUserId());
        orderRemark.setSysUserName(sysUser.getNickName());
        orderRemarkService.insertOrderRemark(orderRemark);
    }

    public OrderLogistics delivery(OrderDeliveryRequestDto request) {
        log.info("OrderManager#delivery---发货，开始，po={}", request);
        ZnsOrderEntity order = orderService.findByOrderNo(request.getOrderNo());
        if (OrderStatusEnum.WAIT_SEND.getStatus().equals(order.getStatus())) {
            order.setStatus(OrderStatusEnum.WAIT_RECEIVE.getStatus());
        }
        order.setLogisticsNo(request.getLogisticCode());
        order.setGmtDeliver(new Date(request.getDeliverTime()));
        if (!StringUtils.hasText(request.getShipperName())) {
            order.setLogisticsCompay(request.getShipperName());
        } else {
            order.setLogisticsCompay(request.getShipperCode());
        }
        orderService.update(order);

        OrderLogistics orderExpress = orderLogisticsService.findOne(new OrderLogisticsQuery().setLogisticsNo(order.getLogisticsNo()).setOrderNo(request.getOrderNo()));
        if (orderExpress == null) {
            orderExpress = new OrderLogistics();
            orderExpress.setOrderId(order.getId()).setOrderNo(order.getOrderNo())
                    .setOrderItemId(request.getOrderItemId())
                    .setDirectionType(request.getDeliverType())
                    .setConsignerName(request.getDeliverConsignee())
                    .setConsignerMobile(request.getDeliverMobile())
                    .setShipperCode(request.getShipperCode())
                    .setShipperName(order.getLogisticsCompay())
                    .setAddress(order.getAddress())
                    .setLogisticCode(order.getLogisticsNo());
            orderLogisticsService.insertOrderLogistics(orderExpress);
        }
        return orderExpress;
    }

    /**
     * ERP查询-订单列表
     */
    public Page<ErpOrderListRespDto> erpOrderList(ErpOrderListReqDto req) {
        //查询订单列表
        OrderPageQuery pageQuery = new OrderPageQuery().setOrderNos(req.getOrderNos()).setSourceType(3)
                .setModifyStart(req.getStartTime()).setModifyEnd(req.getEndTime());
        pageQuery.setPageNum(req.getPageNum());
        pageQuery.setPageSize(req.getPageSize());
        Page<ZnsOrderEntity> page = orderService.findPage(pageQuery);
        Page<ErpOrderListRespDto> resp = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ZnsOrderEntity> records = page.getRecords();
        List<ErpOrderListRespDto> erpOrderList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(orderEntity -> erpOrderList.add(mallOrderBizService.convertErpOrderListRespDto(orderEntity)));
        }
        resp.setRecords(erpOrderList);
        return resp;
    }

    /**
     * 订单申请退款 获取商品明细
     *
     * @since 4.8.0
     */
    public List<OrderItemRefundResponseDto> applyRefundSkuList(Long orderId) {
        ZnsOrderEntity orderEntity = orderService.findById(orderId);
        if (orderEntity == null) {
            throw new BaseException("订单不存在！");
        }
        List<ZnsOrderItemEntity> orderItemEntities = orderItemService.selectListByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderItemEntities)) {
            throw new BaseException("订单明细不存在！");
        }
        Set<Long> goodsIds = orderItemEntities.stream().map(ZnsOrderItemEntity::getGoodsId).collect(Collectors.toSet());
        List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(new ArrayList<>(goodsIds));
        if (CollectionUtils.isEmpty(goodsEntities)) {
            throw new BaseException("商品不存在！");
        }
        Set<Long> skuIds = orderItemEntities.stream().map(ZnsOrderItemEntity::getSkuId).collect(Collectors.toSet());
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(new ArrayList<>(skuIds));
        if (CollectionUtils.isEmpty(skuEntities)) {
            throw new BaseException("sku不存在！");
        }
        //前置数据
        List<Long> orderItemIds = orderItemEntities.stream().map(ZnsOrderItemEntity::getId).collect(Collectors.toList());
        Map<Long, List<OrderRefund>> orderRefundMap = getOrderRefundMap(orderItemIds);//退款单
        Map<Long, ZnsGoodsEntity> goodsMap = goodsEntities.stream().collect(Collectors.toMap(ZnsGoodsEntity::getId, Function.identity(), (k1, k2) -> k2));
        Map<Long, ZnsGoodsSkuEntity> skuMap = skuEntities.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, Function.identity(), (k1, k2) -> k2));
        List<OrderItemRefundResponseDto> resp = orderConsoleConverter.toOrderItemRefundListResponseDto(orderItemEntities);
        //组装数据
        for (OrderItemRefundResponseDto orderItemDto : resp) {
            orderItemDto.setCurrency(I18nConstant.buildCurrency(orderEntity.getCurrencyCode()));
            ZnsGoodsEntity goodsEntity = goodsMap.get(orderItemDto.getGoodsId());
            ZnsGoodsSkuEntity skuEntity = skuMap.get(orderItemDto.getSkuId());
            if (goodsEntity != null) {
                orderItemDto.setIsSupportAfterSales(goodsEntity.getSupportAfterSales());
            }
            if (skuEntity != null) {
                orderItemDto.setThumbnail(skuEntity.getPic());
            }
            List<OrderRefund> itemOrderRefunds = orderRefundMap.get(orderItemDto.getOrderItemId());
            if (!CollectionUtils.isEmpty(itemOrderRefunds)) {
                int sum = itemOrderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus().contains(r.getStatus())).mapToInt(OrderRefund::getCount).sum();
                orderItemDto.setRefundCount(sum);
                orderItemDto.setRefundStatus(itemOrderRefunds.get(0).getStatus());
            }
            //不可退款原因
            String noSupportReason = getNoSupportReason(orderItemDto);
            if (StringUtils.hasText(noSupportReason)) {
                orderItemDto.setCanRefund(false);
                orderItemDto.setNoSupportReason(noSupportReason);
            } else {
                orderItemDto.setCanRefund(true);
            }
        }

        return resp;
    }

    /**
     * 根据订单明细id获取退款的那
     */
    private Map<Long, List<OrderRefund>> getOrderRefundMap(List<Long> orderItemIds) {
        OrderRefundQuery orderRefundQuery = new OrderRefundQuery().setOrderItemIds(orderItemIds);
        List<OrderRefund> orderRefunds = orderRefundService.findList(orderRefundQuery);
        Map<Long, List<OrderRefund>> orderRefundMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderRefunds)) {
            orderRefundMap = orderRefunds.stream()
                    .collect(Collectors.groupingBy(
                            OrderRefund::getOrderItemId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> {
                                        list.sort(Comparator.comparing(OrderRefund::getId).reversed());
                                        return list;
                                    }
                            )
                    ));
        }
        return orderRefundMap;
    }

    /**
     * 获取不可退款原因
     */
    private String getNoSupportReason(OrderItemRefundResponseDto orderItemDto) {
        if (orderItemDto.getIsSupportAfterSales() == 0) {
            return "商品不支持售后";
        }
        if (orderItemDto.getRefundCount() >= orderItemDto.getCount()) {
            return "商品已申请售后";
        }
        return null;
    }

    /**
     * 订单申请退款 计算退款金额
     */
    public RefundAmountCalResponseDto calRefundAmount(RefundAmountCalRequestDto req) {
        ZnsOrderEntity orderEntity = orderService.findById(req.getOrderId());
        if (orderEntity == null) {
            throw new BaseException("订单不存在！");
        }
        List<Long> skuIds = req.getRefundSkuVos().stream().map(OrderSkuVo::getSkuId).collect(Collectors.toList());
        OrderItemQuery orderItemQuery = new OrderItemQuery().setOrderId(req.getOrderId()).setSkuIds(skuIds);
        List<ZnsOrderItemEntity> orderItemEntities = orderItemService.findList(orderItemQuery);
        if (CollectionUtils.isEmpty(orderItemEntities)) {
            throw new BaseException("订单明细不存在！");
        }
        //校验退款数量
        List<Long> orderItemIds = orderItemEntities.stream().map(ZnsOrderItemEntity::getId).collect(Collectors.toList());
        Map<Long, List<OrderRefund>> orderRefundMap = getOrderRefundMap(orderItemIds);
        Map<Long, OrderSkuVo> skuVoMap = req.getRefundSkuVos().stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, Function.identity(), (k1, k2) -> k2));
        for (ZnsOrderItemEntity orderItemEntity : orderItemEntities) {
            List<OrderRefund> orderRefunds = orderRefundMap.getOrDefault(orderItemEntity.getId(), new ArrayList<>());
            //已退款数量
            int refundCount = orderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus().contains(r.getStatus()))
                    .map(OrderRefund::getCount).mapToInt(Integer::intValue).sum();
            OrderSkuVo orderSkuVo = skuVoMap.get(orderItemEntity.getSkuId());
            if (orderItemEntity.getCount() - refundCount < orderSkuVo.getCount()) {
                throw new BaseException("商品；" + orderItemEntity.getTitle() + "可退款数量不足！");
            }
        }

        //当前选中sku最大可退金额
        BigDecimal maxRefundAmount = getMaxRefundAmount(orderItemEntities, orderRefundMap);
        //订单剩余可退金额
        BigDecimal orderEnableRefundAmount = getOrderEnableRefundAmount(orderEntity);
        //sku退款金额明细
        List<RefundAmountCalResponseDto.refundSkuAmountVo> refundSkuAmountVos = getRefundSkuAmountVos(req.getRefundSkuVos(), orderItemEntities, orderRefundMap);
        //本次申请最大退款金额
        BigDecimal refundAmount = refundSkuAmountVos.stream().map(RefundAmountCalResponseDto.refundSkuAmountVo::getActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        RefundAmountCalResponseDto resp = new RefundAmountCalResponseDto();
        resp.setMaxRefundAmount(maxRefundAmount);
        resp.setOrderEnableRefundAmount(orderEnableRefundAmount);
        resp.setRefundAmount(refundAmount);
        resp.setRefundSkuAmountVos(refundSkuAmountVos);
        return resp;
    }

    /**
     * 获取 sku退款金额明细
     */
    private List<RefundAmountCalResponseDto.refundSkuAmountVo> getRefundSkuAmountVos(List<OrderSkuVo> refundSkuVos, List<ZnsOrderItemEntity> orderItemEntities, Map<Long, List<OrderRefund>> orderRefundMap) {
        Map<Long, ZnsOrderItemEntity> orderItemMap = orderItemEntities.stream().collect(Collectors.toMap(ZnsOrderItemEntity::getSkuId, Function.identity(), (k1, k2) -> k2));
        List<RefundAmountCalResponseDto.refundSkuAmountVo> result = new ArrayList<>();
        for (OrderSkuVo refundSkuVo : refundSkuVos) {
            ZnsOrderItemEntity orderItemEntity = orderItemMap.get(refundSkuVo.getSkuId());
            List<OrderRefund> orderRefunds = orderRefundMap.getOrDefault(orderItemEntity.getId(), new ArrayList<>());
            //已申请的退款金额
            BigDecimal sumAmount = orderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus()
                    .contains(r.getStatus())).map(OrderRefund::getApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //已申请的退款数量
            int sumCount = orderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus().contains(r.getStatus()))
                    .mapToInt(OrderRefund::getCount).sum();
            BigDecimal actualAmount = BigDecimal.ZERO; // sku实际支付金额
            BigDecimal couponAmount = BigDecimal.ZERO; // sku优惠券金额
            BigDecimal taxAmount = BigDecimal.ZERO; // sku税费金额
            BigDecimal postageAmount = BigDecimal.ZERO; // sku运费金额
            BigDecimal allowanceAmount = BigDecimal.ZERO; // sku津贴金额
            BigDecimal salePriceRate = orderItemEntity.getActualAmount().divide(new BigDecimal(orderItemEntity.getCount()), 2, RoundingMode.HALF_UP);
            BigDecimal couponRate = orderItemEntity.getSkuCouponAmount().divide(new BigDecimal(orderItemEntity.getCount()), 2, RoundingMode.HALF_UP);
            BigDecimal taxRate = orderItemEntity.getTaxAmount().divide(new BigDecimal(orderItemEntity.getCount()), 2, RoundingMode.HALF_UP);
            BigDecimal postageRate = orderItemEntity.getPostageAmount().divide(new BigDecimal(orderItemEntity.getCount()), 2, RoundingMode.HALF_UP);
            BigDecimal allowanceRate = orderItemEntity.getAllowanceAmount().divide(new BigDecimal(orderItemEntity.getCount()), 2, RoundingMode.HALF_UP);
            if (orderItemEntity.getCount() - sumCount == refundSkuVo.getCount()) {
                //全部退款，使用减法计算
                actualAmount = orderItemEntity.getActualAmount().subtract(sumAmount);
                couponAmount = orderItemEntity.getSkuCouponAmount().subtract(new BigDecimal(sumCount).multiply(couponRate));
                taxAmount = orderItemEntity.getTaxAmount().subtract(new BigDecimal(sumCount).multiply(taxRate));
                postageAmount = orderItemEntity.getPostageAmount().subtract(new BigDecimal(sumCount).multiply(postageRate));
                allowanceAmount = orderItemEntity.getAllowanceAmount().subtract(new BigDecimal(sumCount).multiply(allowanceRate));
            } else {
                //部分退款，使用单价计算
                actualAmount = salePriceRate.multiply(new BigDecimal(refundSkuVo.getCount()));
                couponAmount = couponRate.multiply(new BigDecimal(refundSkuVo.getCount()));
                taxAmount = taxRate.multiply(new BigDecimal(refundSkuVo.getCount()));
                postageAmount = postageRate.multiply(new BigDecimal(refundSkuVo.getCount()));
                allowanceAmount = allowanceRate.multiply(new BigDecimal(refundSkuVo.getCount()));
            }
            result.add(new RefundAmountCalResponseDto.refundSkuAmountVo(refundSkuVo.getSkuId(), refundSkuVo.getCount(), actualAmount, couponAmount, taxAmount, postageAmount, allowanceAmount));
        }
        return result;
    }

    /**
     * 订单剩余可退金额 = 订单实际支付 - 已退款
     */
    private BigDecimal getOrderEnableRefundAmount(ZnsOrderEntity orderEntity) {
        OrderItemQuery orderItemQuery = new OrderItemQuery().setOrderId(orderEntity.getId());
        List<ZnsOrderItemEntity> orderItems = orderItemService.findList(orderItemQuery);
        //退款单
        List<Long> orderItemIds = orderItems.stream().map(ZnsOrderItemEntity::getId).collect(Collectors.toList());
        OrderRefundQuery orderRefundQuery = new OrderRefundQuery().setOrderItemIds(orderItemIds);
        List<OrderRefund> orderRefunds = orderRefundService.findList(orderRefundQuery);
        //已退款总金额
        BigDecimal applyTotalAmount = orderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus()
                .contains(r.getStatus())).map(OrderRefund::getApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //剩余支付总金额
        return orderEntity.getActualAmount().subtract(applyTotalAmount);
    }

    /**
     * 选中sku最大可退金额 = 实际支付 - 已退款
     */
    private BigDecimal getMaxRefundAmount(List<ZnsOrderItemEntity> orderItemEntities, Map<Long, List<OrderRefund>> orderRefundMap) {
        BigDecimal maxRefundAmount = BigDecimal.ZERO;
        for (ZnsOrderItemEntity orderItemEntity : orderItemEntities) {
            List<OrderRefund> orderRefunds = orderRefundMap.get(orderItemEntity.getId());
            BigDecimal itemRefundAmount = orderItemEntity.getActualAmount();
            if (!CollectionUtils.isEmpty(orderRefunds)) {
                //sku已申请的退款金额
                BigDecimal applyAmount = orderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus()
                        .contains(r.getStatus())).map(OrderRefund::getApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                //sku剩余退款金额
                itemRefundAmount = itemRefundAmount.subtract(applyAmount);
            }
            maxRefundAmount = maxRefundAmount.add(itemRefundAmount);
        }
        return maxRefundAmount;
    }

    /**
     * 提交退款
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitRefund(RefundOrderSubmitRequestDto req, String userName) {
        //校验提交金额
        RefundAmountCalResponseDto calRespDto = getCheckRefundAmount(req);
        List<RefundAmountCalResponseDto.refundSkuAmountVo> refundSkuAmountVos = calRespDto.getRefundSkuAmountVos();
        Map<Long, RefundAmountCalResponseDto.refundSkuAmountVo> amountVoMap = refundSkuAmountVos.stream().collect(Collectors.toMap(RefundAmountCalResponseDto.refundSkuAmountVo::getSkuId, Function.identity(), (k1, k2) -> k2));

        //查询退款商品明细
        List<Long> skuIds = req.getRefundSkuVos().stream().map(OrderSkuVo::getSkuId).collect(Collectors.toList());
        OrderItemQuery orderItemQuery = new OrderItemQuery().setOrderId(req.getOrderId()).setSkuIds(skuIds);
        List<ZnsOrderItemEntity> orderItemEntities = orderItemService.findList(orderItemQuery);

        //计算sku分摊金额
        Map<Long, RefundAmountCalResponseDto.refundSkuAmountVo> amountVoMap2 = calSkuRefundAmount(amountVoMap, orderItemEntities, req, calRespDto.getRefundAmount());

        //更新订单状态
        ZnsOrderEntity order = orderService.findById(req.getOrderId());
        order.setAfterSaleStatus(1);
        order.setStatus(OrderStatusEnum.DELIVERED.getStatus()); //后台提交退款，订单直接改成已完成
        orderService.update(order);
        Map<Long, OrderSkuVo> skuVoMap = req.getRefundSkuVos().stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, Function.identity(), (k1, k2) -> k2));


        //给每个sku创建退款单
        for (ZnsOrderItemEntity orderItemEntity : orderItemEntities) {
            RLock lock = redissonClient.getLock(RedisConstants.APPLY_REFUND_ITEM_ID_KEY + orderItemEntity.getId());
            LockHolder.tryLock(lock, 5, 60, () -> {
                //已退款数量
                Integer refundCount = 0;
                List<OrderRefund> orderRefunds = orderRefundService.findList(new OrderRefundQuery().setOrderItemId(orderItemEntity.getId()).setStatusList(OrderRefundConstant.STATUS_ENUM.afterStatus()));
                if (!CollectionUtils.isEmpty(orderRefunds)) {
                    refundCount = orderRefunds.stream().map(OrderRefund::getCount).mapToInt(Integer::intValue).sum();
                }
                OrderSkuVo orderSkuVo = skuVoMap.get(orderItemEntity.getSkuId());
                //有可用的退款商品，提交退款
                if (orderItemEntity.getCount() - refundCount < orderSkuVo.getCount()) {
                    throw new BaseException("商品；" + orderItemEntity.getTitle() + "可退款数量不足！");
                }
                RefundAmountCalResponseDto.refundSkuAmountVo refundSkuAmountVo = amountVoMap2.get(orderItemEntity.getSkuId());
                String countryCode = StringUtils.hasText(order.getCountryCode()) ? order.getCountryCode() : I18nConstant.CountryCodeEnum.US.code;
                OrderRefund orderRefund = new OrderRefund();
                orderRefund.setRefundType(OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT.getCode());
                orderRefund.setRefundAmountType(OrderRefundConstant.REFUND_AMOUNT_TYPE_ENUM.REFUND_GOOD.getCode());
                orderRefund.setStatus(OrderRefundConstant.STATUS_ENUM.APPLY_REFUND.getCode());
                orderRefund.setPayStatus(OrderRefundConstant.PAY_STATUS_ENUM.PAY_NOT_REFUND.getCode());
                orderRefund.setApplyAmount(refundSkuAmountVo.getActualAmount());
                orderRefund.setPostageAmount(refundSkuAmountVo.getPostageAmount());
                orderRefund.setTaxAmount(refundSkuAmountVo.getTaxAmount());
                orderRefund.setCouponAmount(refundSkuAmountVo.getCouponAmount());
                orderRefund.setAllowanceAmount(refundSkuAmountVo.getAllowanceAmount());
                orderRefund.setRefundPics(JsonUtil.writeString(req.getRefundPics()));
                orderRefund.setReason(req.getReason());
                orderRefund.setRefundRemark(req.getRefundRemark());
                orderRefund.setDescription(req.getRefundRemark());
                orderRefund.setCreator(userName);
                orderRefund.setCount(orderSkuVo.getCount());
                orderRefund.setOrderItemId(orderItemEntity.getId());
                orderRefund.setCurrencyCode(order.getCurrencyCode());
                orderRefund.setCurrencySymbol(order.getCurrencySymbol());
                orderRefund.setSource(OrderRefundConstant.RefundSourceEnum.ADMIN.code);
                orderRefund.setRefundNo(SnowflakeOrderNoGenerator.buildOrderNo("TK", countryCode));
                orderRefund.setOrderNo(order.getOrderNo());
                orderRefund.setOrderId(orderItemEntity.getOrderId());
                orderRefund.setUserId(orderItemEntity.getUserId());
                orderRefund.setCountryCode(order.getCountryCode());
                orderRefundService.insertOrderRefund(orderRefund);
            });
        }
    }

    /**
     * 计算sku分摊金额
     */
    private Map<Long, RefundAmountCalResponseDto.refundSkuAmountVo> calSkuRefundAmount(Map<Long, RefundAmountCalResponseDto.refundSkuAmountVo> amountVoMap,
                                                                                       List<ZnsOrderItemEntity> orderItemEntities,
                                                                                       RefundOrderSubmitRequestDto req,
                                                                                       BigDecimal refundMaxAmount) {
        Map<Long, ZnsOrderItemEntity> skuItemMap = orderItemEntities.stream().collect(Collectors.toMap(ZnsOrderItemEntity::getSkuId, Function.identity(), (k1, k2) -> k2));
        Map<Long, OrderSkuVo> skuVoMap = req.getRefundSkuVos().stream().collect(Collectors.toMap(OrderSkuVo::getSkuId, Function.identity(), (k1, k2) -> k2));
        //本次申请的退款金额
        BigDecimal refundAmount = req.getRefundAmount();
        BigDecimal refundRate = req.getRefundAmount().divide(refundMaxAmount, 4, RoundingMode.HALF_UP);
        //sku实际支付总金额 = sum(实际支付金额  * (退款数量 /订单数量))
        BigDecimal skuTotalAmount = orderItemEntities.stream().map(item ->
                        item.getActualAmount().multiply(new BigDecimal(skuVoMap.get(item.getSkuId()).getCount()).divide(new BigDecimal(item.getCount()), RoundingMode.HALF_UP)))
                .reduce(new BigDecimal("0"), BigDecimal::add);
        BigDecimal sumActualAmount = BigDecimal.ZERO;
        BigDecimal sumCouponAmount = BigDecimal.ZERO;
        BigDecimal sumTaxAmount = BigDecimal.ZERO;
        BigDecimal sumPostageAmount = BigDecimal.ZERO;
        BigDecimal sumAllowanceAmount = BigDecimal.ZERO;
        for (int i = 0; i < req.getRefundSkuVos().size(); i++) {
            OrderSkuVo orderSkuVo = req.getRefundSkuVos().get(i);
            ZnsOrderItemEntity orderItemEntity = skuItemMap.get(orderSkuVo.getSkuId());
            RefundAmountCalResponseDto.refundSkuAmountVo refundSkuAmountVo = amountVoMap.get(orderSkuVo.getSkuId());
            BigDecimal actualAmount = refundSkuAmountVo.getActualAmount();
            BigDecimal couponAmount = refundSkuAmountVo.getCouponAmount().multiply(refundRate);
            BigDecimal taxAmount = refundSkuAmountVo.getTaxAmount().multiply(refundRate);
            BigDecimal postageAmount = refundSkuAmountVo.getPostageAmount().multiply(refundRate);
            BigDecimal allowanceAmount = refundSkuAmountVo.getAllowanceAmount().multiply(refundRate);
            if (i == req.getRefundSkuVos().size() - 1) {
                //最后一个用减法
                actualAmount = refundAmount.subtract(sumActualAmount).setScale(2, RoundingMode.HALF_UP);
                couponAmount = couponAmount.subtract(sumCouponAmount).setScale(2, RoundingMode.HALF_UP);
                taxAmount = taxAmount.subtract(sumTaxAmount).setScale(2, RoundingMode.HALF_UP);
                postageAmount = postageAmount.subtract(sumPostageAmount).setScale(2, RoundingMode.HALF_UP);
                allowanceAmount = allowanceAmount.subtract(sumAllowanceAmount).setScale(2, RoundingMode.HALF_UP);
            } else {
                //其他用按比例计算
                BigDecimal skuAmount = new BigDecimal(orderSkuVo.getCount()).divide(new BigDecimal(orderItemEntity.getCount()), RoundingMode.HALF_UP).multiply(orderItemEntity.getActualAmount());
                BigDecimal rate = skuAmount.divide(skuTotalAmount, 2, RoundingMode.HALF_UP);
                actualAmount = refundAmount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                couponAmount = couponAmount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                taxAmount = taxAmount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                postageAmount = postageAmount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
                allowanceAmount = allowanceAmount.multiply(rate).setScale(2, RoundingMode.HALF_UP);
            }
            //重置金额
            refundSkuAmountVo.setActualAmount(actualAmount);
            refundSkuAmountVo.setCouponAmount(couponAmount);
            refundSkuAmountVo.setTaxAmount(taxAmount);
            refundSkuAmountVo.setPostageAmount(postageAmount);
            refundSkuAmountVo.setAllowanceAmount(allowanceAmount);
            //小计
            sumActualAmount = sumActualAmount.add(actualAmount);
            sumCouponAmount = sumCouponAmount.add(couponAmount);
            sumTaxAmount = sumTaxAmount.add(taxAmount);
            sumPostageAmount = sumPostageAmount.add(postageAmount);
            sumAllowanceAmount = sumAllowanceAmount.add(allowanceAmount);
        }
        return amountVoMap;
    }

    /**
     * 获取校验退款金额
     */
    private RefundAmountCalResponseDto getCheckRefundAmount(RefundOrderSubmitRequestDto req) {
        RefundAmountCalRequestDto calReq = new RefundAmountCalRequestDto();
        calReq.setOrderId(req.getOrderId());
        calReq.setRefundSkuVos(req.getRefundSkuVos());
        RefundAmountCalResponseDto calRespDto = calRefundAmount(calReq);
        if (req.getRefundAmount().compareTo(calRespDto.getRefundAmount()) > 0) {
            throw new BaseException("超过当前商品最大可退金额");
        }
        return calRespDto;
    }
}
