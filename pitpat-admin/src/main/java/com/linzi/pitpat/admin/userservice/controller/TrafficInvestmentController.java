package com.linzi.pitpat.admin.userservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.TrafficInvestmentBizService;
import com.linzi.pitpat.data.userservice.converter.console.TrafficInvestmentConvert;
import com.linzi.pitpat.data.userservice.dto.console.request.traffic.investment.TrafficInvestmentActivityRequest;
import com.linzi.pitpat.data.userservice.dto.console.request.traffic.investment.TrafficInvestmentCreateRequest;
import com.linzi.pitpat.data.userservice.dto.console.request.traffic.investment.TrafficInvestmentEditRequest;
import com.linzi.pitpat.data.userservice.dto.console.request.traffic.investment.TrafficInvestmentOnSaleRequest;
import com.linzi.pitpat.data.userservice.dto.console.request.traffic.investment.TrafficInvestmentPageQueryRequest;
import com.linzi.pitpat.data.userservice.dto.console.request.traffic.investment.TrafficInvestmentStatisticsRequest;
import com.linzi.pitpat.data.userservice.dto.console.response.investment.TrafficInvestmentDetailResponse;
import com.linzi.pitpat.data.userservice.dto.console.response.investment.TrafficInvestmentListItemResponse;
import com.linzi.pitpat.data.userservice.dto.console.response.investment.TrafficInvestmentStatisticsResponse;
import com.linzi.pitpat.data.userservice.model.entity.TrafficInvestmentAwardStatisticsDo;
import com.linzi.pitpat.data.userservice.model.entity.TrafficInvestmentPackageDo;
import com.linzi.pitpat.data.userservice.service.TrafficInvestmentAwardStatisticsService;
import com.linzi.pitpat.data.userservice.service.TrafficInvestmentPackageService;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 投流包 管理crud
 *
 * @since 4.7.0
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/trafficInvestment")
public class TrafficInvestmentController {
    private final TrafficInvestmentBizService trafficInvestmentBizService;

    private final TrafficInvestmentPackageService trafficInvestmentPackageService;

    private final TrafficInvestmentConvert trafficInvestmentConvert;

    private final TrafficInvestmentAwardStatisticsService trafficInvestmentAwardStatisticsService;

    private final ISysConfigService iSysConfigService;
    /**
     * 投流包统计
     *
     * @since 4.7.0
     */
    @GetMapping("/statistics")
    public Result<TrafficInvestmentStatisticsResponse> statistics(TrafficInvestmentStatisticsRequest req) {
        TrafficInvestmentPackageDo byId = trafficInvestmentPackageService.findById(req.getId());
        if (byId == null) {
            return CommonResult.success();
        }
        TrafficInvestmentAwardStatisticsDo stat = trafficInvestmentAwardStatisticsService.getStat(req.getId(), null);
        TrafficInvestmentAwardStatisticsDo dailyStat = trafficInvestmentAwardStatisticsService.getStat(req.getId(), ZonedDateTime.now());

        TrafficInvestmentStatisticsResponse response = new TrafficInvestmentStatisticsResponse();
        response.setUseDeductionDayLimit(dailyStat.getTotalDeductionAmount());
        response.setDeductionDayLimit(byId.getDeductionDayLimit());
        response.setUseDeductionTotalLimit(stat.getTotalDeductionAmount());
        response.setDeductionTotalLimit(byId.getDeductionTotalLimit());
        response.setUseScoreDayLimit(dailyStat.getTotalScore());
        response.setScoreDayLimit(byId.getScoreDayLimit());
        response.setUseScoreTotalLimit(stat.getTotalScore());
        response.setScoreTotalLimit(byId.getScoreTotalLimit());
        return CommonResult.success(response);
    }

    /**
     * 创建投流包
     *
     * @return
     */
    @PostMapping("/create")
    public Result<Long> create(@RequestBody TrafficInvestmentCreateRequest req) {
        return CommonResult.success(trafficInvestmentBizService.createTrafficInvestmentPackage(req));
    }

    /**
     * 编辑投流包
     *
     * @since 4.7.0
     */
    @PostMapping("/edit")
    public Result<Long> edit(@RequestBody TrafficInvestmentEditRequest request) {
        return CommonResult.success(trafficInvestmentBizService.editTrafficInvestmentPackage(request));
    }

    @GetMapping("/detail")
    public Result<TrafficInvestmentDetailResponse> detail(@RequestParam Long id) {
        return CommonResult.success(trafficInvestmentBizService.detail(id));
    }


    @GetMapping("/page")
    public Result<Page<TrafficInvestmentListItemResponse>> page(TrafficInvestmentPageQueryRequest req) {
        Page<TrafficInvestmentPackageDo> page = trafficInvestmentPackageService.page(req);
        return CommonResult.success(trafficInvestmentConvert.toResponse(page));
    }

    @PostMapping("/delete")
    public Result<Integer> delete(@RequestBody TrafficInvestmentOnSaleRequest request) {
        return CommonResult.success(trafficInvestmentBizService.delete(request) ? 1 : 0);
    }

    /**
     * 上架
     *
     * @param
     * @return 1 成功 0 失败，存在重复的渠道
     */
    @PostMapping("/onSale")
    public Result<Integer> onSale(@RequestBody TrafficInvestmentOnSaleRequest request) {
        return CommonResult.success(trafficInvestmentBizService.onSale(request) ? 1 : 0);
    }

    /**
     * 下架
     *
     * @param id
     * @return
     */
    @PostMapping("/offSale")
    public Result<Long> offSale(@RequestBody TrafficInvestmentOnSaleRequest request) {
        return CommonResult.success(trafficInvestmentBizService.offSale(request.getId()));
    }

    /**
     * 渠道活动配置
     * @since 4.7.4
     */
    @PostMapping("/activityConfig")
    public Result<Boolean> activityConfig(@RequestBody @Validated TrafficInvestmentActivityRequest request) {
        return CommonResult.success(trafficInvestmentBizService.activityConfig(request));
    }

    /**
     * 渠道列表
     */
    @PostMapping("/channelList")
    public Result<List<String>> channelList() {
        String configValue = iSysConfigService.selectConfigByKey("traffic_investment_channel");
        if (StringUtils.hasText(configValue)) {
            List<String> channel = JsonUtil.readList(configValue, String.class);
            return CommonResult.success(channel);
        }
        return CommonResult.success();
    }
}
