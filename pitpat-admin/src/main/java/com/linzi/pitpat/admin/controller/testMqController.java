package com.linzi.pitpat.admin.controller;

import com.linzi.pitpat.data.activityservice.dto.SeasonBonusPoolResetEventDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityListDto;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.clubservice.model.ClubDisbandEvent;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.paymentservice.biz.listener.event.PaymentTradeDowngradedApplicationEvent;
import com.linzi.pitpat.data.paymentservice.biz.listener.event.PaymentTradeRefundApplicationEvent;
import com.linzi.pitpat.data.paymentservice.biz.listener.event.PaymentTradeStateChangeApplicationEvent;
import com.linzi.pitpat.data.paymentservice.enums.PaymentTradeStateEnum;
import com.linzi.pitpat.data.paymentservice.model.bo.PaymentTradeBo;
import com.linzi.pitpat.data.paymentservice.model.bo.PaymentTradeStateCheckResult;
import com.linzi.pitpat.data.robotservice.listener.event.UserCreateActivityEvent;
import com.linzi.pitpat.data.robotservice.listener.event.UserFinFollowEvent;
import com.linzi.pitpat.data.robotservice.listener.event.UserLoginEvent;
import com.linzi.pitpat.data.robotservice.listener.event.UserReportActivityEvent;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkUserTypeEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkSyncUserEvent;
import com.linzi.pitpat.data.userservice.dto.event.PutChannelUserDeductionReachLimitEvent;
import com.linzi.pitpat.data.userservice.dto.event.PutChannelUserReceiveLevelAwardEvent;
import com.linzi.pitpat.data.userservice.dto.event.PutChannelUserShouldUpgradeEvent;
import com.linzi.pitpat.data.userservice.dto.event.PutChannelUserUpgradeSuccessEvent;
import com.linzi.pitpat.data.userservice.dto.event.TrafficInvestmentStatUpdateEvent;
import com.linzi.pitpat.data.userservice.dto.event.UserExpSendEvent;
import com.linzi.pitpat.data.userservice.dto.event.UserLevelChangeEvent;
import com.linzi.pitpat.data.userservice.dto.event.UserScoreSendEvent;
import com.linzi.pitpat.data.userservice.model.entity.TrafficInvestmentAwardStatisticsDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.bo.UserExpIncreDto;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.util.Map;

@RestController
@RequestMapping({"/testmq"})
public class testMqController {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private QueueMessageService queueMessageService;

    @GetMapping("/testSend")
    public void test() {

        queueMessageService.sendDelayMessage("delay.content.exchange", "publish", "sss", 100L);

    }

    @GetMapping("/test")
    public void test(@RequestParam("id") Long id, @RequestParam("time") Integer time) {
        rabbitTemplate.convertAndSend("delayed_coupon_exchange_name_test", "content_publish", id, message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay(time);   // 毫秒为单位，指定此消息的延时时长 ,+ 1 尽量保证机器人跑完了，再发送消息
            return message;
        });

    }


    @GetMapping("/testSendEvent")
    public void testSendEvent() {
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(), new TurbolinkApplicationEvent(TurbolinkEventEnum.CREATE_ROOM, 1L, Map.of("isCreated", Boolean.TRUE.toString())));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkSyncUserEvent.getEventType(), new TurbolinkSyncUserEvent(1L, TurbolinkUserTypeEnum.VIP, this));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserLoginEvent.getEventType(), new UserLoginEvent(this, new ZnsUserEntity()));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserCreateActivityEvent.getEventType(), new UserCreateActivityEvent(this, new ZnsUserEntity(), new ActivityListDto()));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserReportActivityEvent.getEventType(), new UserReportActivityEvent(this, new HandleActivityRequest()));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserScoreSendEvent.getEventType(), UserScoreSendEvent.of(1L, 1));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserExpSendEvent.getEventType(), UserExpSendEvent.of(1L, new UserExpIncreDto()));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PutChannelUserReceiveLevelAwardEvent.getEventType(), PutChannelUserReceiveLevelAwardEvent.of(1L, 1));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserLevelChangeEvent.getEventType(), UserLevelChangeEvent.of(1L, 1, 0));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserFinFollowEvent.getEventType(),new UserFinFollowEvent(this, 1L, 2L));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PutChannelUserShouldUpgradeEvent.getEventType(), PutChannelUserShouldUpgradeEvent.of(1L, 2));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PutChannelUserUpgradeSuccessEvent.getEventType(), PutChannelUserUpgradeSuccessEvent.of(1L));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PutChannelUserDeductionReachLimitEvent.getEventType(), PutChannelUserDeductionReachLimitEvent.of(1L));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.SeasonBonusPoolResetEventDto.getEventType(),new SeasonBonusPoolResetEventDto(1, 2L));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.ClubDisbandEvent.getEventType(),new ClubDisbandEvent(this, 2L));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PaymentTradeStateChangeApplicationEvent.getEventType(), new PaymentTradeStateChangeApplicationEvent(this, new PaymentTradeBo(), new PaymentTradeStateCheckResult(PaymentTradeStateEnum.PAY_CANCEL)));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PaymentTradeRefundApplicationEvent.getEventType(), new PaymentTradeRefundApplicationEvent(this, "1", 1L));

        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PaymentTradeDowngradedApplicationEvent.getEventType(),new PaymentTradeDowngradedApplicationEvent(this, "1", 1L));
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TrafficInvestmentStatUpdateEvent.getEventType(), TrafficInvestmentStatUpdateEvent.of(new TrafficInvestmentAwardStatisticsDo(), new TrafficInvestmentAwardStatisticsDo()));












    }

}
