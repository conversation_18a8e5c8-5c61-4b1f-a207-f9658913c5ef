package com.linzi.pitpat.admin.mallservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentConfig;
import com.linzi.pitpat.data.equipmentservice.model.query.EquipmentConfigModelQuery;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.mallservice.converter.api.MallCategoryEquipmentModelConfigConverter;
import com.linzi.pitpat.data.mallservice.dto.console.EquipmentModelResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.MallCategoryEquipmentModelConfigCreateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.MallCategoryEquipmentModelConfigDetailQueryDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.MallCategoryEquipmentModelConfigPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.MallCategoryEquipmentModelConfigDetailResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.MallCategoryEquipmentModelConfigResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.ContentI8nDto;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategory;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryEquipmentModelConfigDetailDo;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryEquipmentModelConfigDo;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryPage;
import com.linzi.pitpat.data.mallservice.model.query.MallCategoryEquipmentModelConfigPageQuery;
import com.linzi.pitpat.data.mallservice.service.MallCategoryEquipmentModelConfigDetailService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryEquipmentModelConfigService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryPageService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryService;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商城类目设备型号管理
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MallCategoryEquipmentModelManager {

    private final MallCategoryEquipmentModelConfigService mallCategoryEquipmentModelConfigService;
    private final MallCategoryEquipmentModelConfigDetailService mallCategoryEquipmentModelConfigDetailService;
    private final EquipmentConfigService equipmentConfigService;
    private final MallCategoryPageService mallCategoryPageService;
    private final MallCategoryService mallCategoryService;
    private final MallCategoryEquipmentModelConfigConverter mallCategoryEquipmentModelConfigConverter;

    /**
     * 新增修改设备类目映射关系
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createOrModify(MallCategoryEquipmentModelConfigCreateRequestDto req) {
        MallCategoryEquipmentModelConfigDo configDo = mallCategoryEquipmentModelConfigConverter.toDo(req);
        if (req.getId() != null) {
            //删除已有映射关系详情
            mallCategoryEquipmentModelConfigDetailService.deleteByConfigId(req.getId());
            //更新设备类目映射关系
            mallCategoryEquipmentModelConfigService.updateSelective(configDo);
        } else {
            //新增设备类目映射关系
            mallCategoryEquipmentModelConfigService.create(configDo);
        }

        //保存详情
        List<EquipmentConfig> equipmentConfigs = equipmentConfigService.findList(new EquipmentConfigModelQuery().setEquipmentInfos(req.getEquipmentModels()));
        if (!CollectionUtils.isEmpty(equipmentConfigs)) {
            List<MallCategoryEquipmentModelConfigDetailDo> detailDos = equipmentConfigs.stream().
                    map(equipmentConfig -> new MallCategoryEquipmentModelConfigDetailDo(configDo.getId(), equipmentConfig.getEquipmentInfo(), equipmentConfig.getSubType())).toList();
            mallCategoryEquipmentModelConfigDetailService.batchCreate(detailDos);
        }
        return configDo.getId();
    }

    /**
     * 按照ID 查询详情
     */
    public MallCategoryEquipmentModelConfigDetailResponseDto getDetail(Long id) {
        MallCategoryEquipmentModelConfigDo mallCategoryEquipmentModelConfig = mallCategoryEquipmentModelConfigService.findById(id);
        if (mallCategoryEquipmentModelConfig == null) {
            throw new BaseException("设备类目映射关系不存在");
        }
        MallCategoryEquipmentModelConfigDetailResponseDto detailDto = mallCategoryEquipmentModelConfigConverter.toDetailDto(mallCategoryEquipmentModelConfig);
        //查询设备型号详情
        MallCategoryEquipmentModelConfigDetailQueryDto queryDto = new MallCategoryEquipmentModelConfigDetailQueryDto();
        queryDto.setConfigId(id);
        List<MallCategoryEquipmentModelConfigDetailDo> list = mallCategoryEquipmentModelConfigDetailService.findList(queryDto);
        if (!CollectionUtils.isEmpty(list)) {
            List<String> equipmentModels = list.stream().map(MallCategoryEquipmentModelConfigDetailDo::getEquipmentModel).distinct().toList();
            detailDto.setEquipmentModels(equipmentModels);
            if (Objects.equals(detailDto.getJumpType(), BannerJumpTypeEnum.CATEGORY_URL.getJumpType())) {
                //商城类目跳转
                if (detailDto.getCategoryId() != null) {
                    //查询主类目详情
                    MallCategoryPage categoryPage = mallCategoryPageService.findById(detailDto.getCategoryId());
                    if (categoryPage != null) {
                        detailDto.setCategoryPageCode(categoryPage.getCategoryPageCode());
                    }
                }
                if (detailDto.getSubCategoryId() != null) {
                    //查询子类目详情
                    MallCategory mallCategory = mallCategoryService.findById(detailDto.getSubCategoryId());
                    if (mallCategory != null) {
                        List<ContentI8nDto> contentI8nDtos = JsonUtil.readList(mallCategory.getName(), ContentI8nDto.class);
                        ContentI8nDto contentI8nDto = contentI8nDtos.stream().filter(item -> Objects.equals(item.getLanguageCode(), mallCategory.getDefaultLanguageCode())).findFirst().orElse(contentI8nDtos.get(0));
                        detailDto.setSubCategoryName(contentI8nDto.getContent());
                    }
                }
            }
        }
        return detailDto;
    }

    /**
     * 分页查询列表
     */
    public Page<MallCategoryEquipmentModelConfigResponseDto> findPage(MallCategoryEquipmentModelConfigPageQueryDto pageQueryDto) {
        //分页查询
        MallCategoryEquipmentModelConfigPageQuery pageQuery = mallCategoryEquipmentModelConfigConverter.toPageQuery(pageQueryDto);
        pageQuery.setOrders(List.of(OrderItem.desc("id")));
        Page<MallCategoryEquipmentModelConfigDo> page = mallCategoryEquipmentModelConfigService.findPage(pageQuery);
        log.info("mallCategoryEquipmentModelConfig find page {}", page);
        Page<MallCategoryEquipmentModelConfigResponseDto> result = mallCategoryEquipmentModelConfigConverter.toDtoPage(page);
        List<MallCategoryEquipmentModelConfigResponseDto> records = result.getRecords();
        //填充数据
        if (!CollectionUtils.isEmpty(records)) {
            List<Long> configIds = records.stream().map(MallCategoryEquipmentModelConfigResponseDto::getId).distinct().toList();
            //查询设备型号详情
            MallCategoryEquipmentModelConfigDetailQueryDto queryDto = new MallCategoryEquipmentModelConfigDetailQueryDto();
            queryDto.setConfigIds(configIds);
            List<MallCategoryEquipmentModelConfigDetailDo> list = mallCategoryEquipmentModelConfigDetailService.findList(queryDto);
            Map<Long, Set<String>> equipmentModelMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(list)) {
                equipmentModelMap = list.stream().collect(Collectors.groupingBy(MallCategoryEquipmentModelConfigDetailDo::getConfigId, Collectors.mapping(MallCategoryEquipmentModelConfigDetailDo::getEquipmentModel, Collectors.toSet())));
            }
            //查询主类目详情
            List<Long> categoryIds = records.stream().map(MallCategoryEquipmentModelConfigResponseDto::getCategoryId).distinct().toList(); //主类目id
            Map<Long, String> categoryNameMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(categoryIds)) {
                List<MallCategoryPage> categoryPages = mallCategoryPageService.findByIds(categoryIds);
                if (!CollectionUtils.isEmpty(categoryPages)) {
                    categoryNameMap = categoryPages.stream().collect(Collectors.toMap(MallCategoryPage::getId, MallCategoryPage::getRemark));
                }
            }
            //查询子类目详情
            List<Long> subCategoryIds = records.stream().map(MallCategoryEquipmentModelConfigResponseDto::getSubCategoryId).distinct().toList(); //子类目id
            Map<Long, String> subCategoryNameMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(subCategoryIds)) {
                List<MallCategory> mallCategories = mallCategoryService.findByIds(subCategoryIds);
                if (!CollectionUtils.isEmpty(mallCategories)) {
                    for (MallCategory mallCategory : mallCategories) {
                        List<ContentI8nDto> contentI8nDtos = JsonUtil.readList(mallCategory.getName(), ContentI8nDto.class);
                        ContentI8nDto contentI8nDto = contentI8nDtos.stream().filter(item -> Objects.equals(item.getLanguageCode(), mallCategory.getDefaultLanguageCode())).findFirst().orElse(contentI8nDtos.get(0));
                        subCategoryNameMap.put(mallCategory.getId(), contentI8nDto.getContent());
                    }
                }
            }

            //填充列表数据
            for (MallCategoryEquipmentModelConfigResponseDto record : records) {
                record.setEquipmentModels(new ArrayList<>(equipmentModelMap.getOrDefault(record.getId(), new HashSet<>())));
                if (Objects.equals(record.getJumpType(), 4)) {
                    record.setJumpValue(record.getH5Url());
                } else {
                    String jumpValue = categoryNameMap.getOrDefault(record.getCategoryId(), "-") + " > " + subCategoryNameMap.getOrDefault(record.getSubCategoryId(), "-");
                    record.setJumpValue(jumpValue);
                }
            }
        }
        return result;
    }

    /**
     * 获取设备型号列表
     */
    public List<EquipmentModelResponseDto> getEquipmentModelList(Long id) {
        //查询已选项
        MallCategoryEquipmentModelConfigDetailQueryDto queryDto = new MallCategoryEquipmentModelConfigDetailQueryDto();
        queryDto.setExcludeConfigId(id);
        List<MallCategoryEquipmentModelConfigDetailDo> detailDoList = mallCategoryEquipmentModelConfigDetailService.findList(queryDto);
        List<String> excludeModels = null;
        if (!CollectionUtils.isEmpty(detailDoList)) {
            excludeModels = detailDoList.stream().map(MallCategoryEquipmentModelConfigDetailDo::getEquipmentModel).distinct().toList();
        }

        //查询未选项
        EquipmentConfigModelQuery pageQuery = new EquipmentConfigModelQuery();
        pageQuery.setExcludeModels(excludeModels);
        List<EquipmentConfig> equipmentConfigList = equipmentConfigService.listEquipmentInfo(pageQuery);
        if (CollectionUtils.isEmpty(equipmentConfigList)) {
            return new ArrayList<>();
        }
        // 去重并创建 EquipmentModelResponseDto 对象
        return equipmentConfigList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(EquipmentConfig::getEquipmentInfo, config -> config, (k1, k2) -> k1))
                .values().stream().map(config -> new EquipmentModelResponseDto(config.getEquipmentType(), config.getEquipmentInfo()))
                .collect(Collectors.toList());
    }
    /**
     * 获取设备型号列表
     */
    public List<EquipmentModelResponseDto> getUniqProductCodeList() {
        List<EquipmentConfig> equipmentConfigList = equipmentConfigService.listEquipmentInfo(new EquipmentConfigModelQuery());
        if (CollectionUtils.isEmpty(equipmentConfigList)) {
            return new ArrayList<>();
        }
        // 去重并创建 EquipmentModelResponseDto 对象
        return equipmentConfigList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(EquipmentConfig::getEquipmentInfo, config -> config, (k1, k2) -> k1))
                .values().stream().map(config -> new EquipmentModelResponseDto(config.getEquipmentType(), config.getEquipmentInfo()))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        mallCategoryEquipmentModelConfigService.deleteById(id);
        //删除已有映射关系详情
        mallCategoryEquipmentModelConfigDetailService.deleteByConfigId(id);
    }
}
