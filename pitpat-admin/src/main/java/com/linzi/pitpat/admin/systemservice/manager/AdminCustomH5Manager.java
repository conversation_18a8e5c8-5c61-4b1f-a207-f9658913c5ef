package com.linzi.pitpat.admin.systemservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.systemservice.dto.console.response.CustomH5ModuleResponseDto;
import com.linzi.pitpat.data.systemservice.dto.console.response.CustomH5ModuleV2ResponseDto;
import com.linzi.pitpat.data.systemservice.model.entity.CustomH5;
import com.linzi.pitpat.data.systemservice.service.CustomH5Service;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminCustomH5Manager {

    private final CustomH5Service customH5Service;

    /**
     * 自定义H5页面配置支持多国家洗数,传id洗单条,不传id则全部
     */
    public void updateCustomH5(Long id) {
        if (id != null) {
            CustomH5 customH5 = customH5Service.selectById(id);
            if (customH5 == null){
                throw new BaseException("未找到该自定义H5页面");
            }
            updateCustomH5(customH5);
            return;
        }
        boolean exist = true;
        int pageNum = 1;
        while (exist) {
            Page<CustomH5> customH5Page = customH5Service.selectByCondition(Page.of(pageNum, 100), null, null);
            if (customH5Page.getTotal() <= 0 || CollectionUtils.isEmpty(customH5Page.getRecords())){
                exist = false;
            }else {
                pageNum = pageNum +1;
                for (CustomH5 customH5 : customH5Page.getRecords()) {
                    updateCustomH5(customH5);
                }
            }
        }
    }

    private void updateCustomH5(CustomH5 customH5) {
        if (!customH5.getData().contains("data")){
            log.info("[updateCustomH5]---自定义H5配置洗数,id={},data为空,无需处理",customH5.getId());
            return;
        }
        if (customH5.getData().contains("countryData")){
            log.info("[updateCustomH5]---自定义H5配置洗数,id={},无需处理",customH5.getId());
            return;
        }

        List<CustomH5ModuleResponseDto> list =  JsonUtil.readList(customH5.getData(), CustomH5ModuleResponseDto.class);
        List<CustomH5ModuleV2ResponseDto> v2List = List.of(new CustomH5ModuleV2ResponseDto("ALL", list));
        customH5.setData(JsonUtil.writeString(v2List));
        customH5Service.update(customH5);
        log.info("[updateCustomH5]---自定义H5配置洗数,id={},洗数完成",customH5.getId());
    }
}
