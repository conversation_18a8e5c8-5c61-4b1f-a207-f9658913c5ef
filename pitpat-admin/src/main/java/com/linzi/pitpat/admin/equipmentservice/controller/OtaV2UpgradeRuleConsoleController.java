package com.linzi.pitpat.admin.equipmentservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2UpgradeRuleCreateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2UpgradeRulePageQueryDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2UpgradeRuleUpdateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.request.OtaV2UpgradeRuleUpdateStateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.response.OtaV2UpgradeRuleConsoleDetailResponseDto;
import com.linzi.pitpat.data.equipmentservice.dto.console.response.OtaV2UpgradeRuleConsoleResponseDto;
import com.linzi.pitpat.data.equipmentservice.manager.console.OtaV2UpgradeRuleConsoleManager;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 新版ota升级规则 服务类
 *
 * @since 2025年7月3日
 */
@Slf4j
@RestController
@RequestMapping("/otaV2UpgradeRules")
@RequiredArgsConstructor
public class OtaV2UpgradeRuleConsoleController {

    private final OtaV2UpgradeRuleConsoleManager otaV2UpgradeRuleConsoleManager;

    /**
     * 分页查询新版ota升级规则列表
     */
    @PostMapping("/page")
    public Result<Page<OtaV2UpgradeRuleConsoleResponseDto>> findPage(@RequestBody OtaV2UpgradeRulePageQueryDto pageQueryDto) {
        Page<OtaV2UpgradeRuleConsoleResponseDto> resp = otaV2UpgradeRuleConsoleManager.findPage(pageQueryDto);
        return CommonResult.success(resp);
    }

    /**
     * 创建新版ota升级规则
     */
    @Log(title = "创建新版ota升级规则", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/create")
    public Result<Long> create(@RequestBody @Validated OtaV2UpgradeRuleCreateRequestDto requestDto) {
        Long id = otaV2UpgradeRuleConsoleManager.create(requestDto, SecurityUtils.getUsername());
        return CommonResult.success(id);
    }

    /**
     * 按照ID 查询新版ota升级规则
     */
    @GetMapping("/get/{id}")
    public Result<OtaV2UpgradeRuleConsoleDetailResponseDto> get(@PathVariable Long id) {
        OtaV2UpgradeRuleConsoleDetailResponseDto resp = otaV2UpgradeRuleConsoleManager.getById(id);
        return CommonResult.success(resp);
    }

    /**
     * 更新新版ota升级规则
     */
    @Log(title = "更新新版ota升级规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/update")
    public Result<Long> update(@RequestBody @Validated OtaV2UpgradeRuleUpdateRequestDto requestDto) {
        return CommonResult.success(otaV2UpgradeRuleConsoleManager.update(requestDto, SecurityUtils.getUsername()));
    }

    /**
     * 更新升级配置下架状态
     */
    @Log(title = "更新升级配置下架状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/updateState")
    public Result<Boolean> updateState(@RequestBody @Validated OtaV2UpgradeRuleUpdateStateRequestDto requestDto) {
        otaV2UpgradeRuleConsoleManager.updateState(requestDto, SecurityUtils.getUsername());
        return CommonResult.success(true);
    }


}
