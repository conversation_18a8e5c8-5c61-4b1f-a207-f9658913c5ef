package com.linzi.pitpat.admin.model.Dto.response.vip;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 15:09
 * @Description 会员提醒列表出参
 */
@Data
@NoArgsConstructor
public class VipPassRemindListResponseDto {
    /**
     * 提醒类型：1-首页弹窗；2-push；3-站内信；4-邮件
     */
    private Integer remindType;
    /**
     * 提醒明细
     */
    private List<VipPassRemindDetailResponseDto> details;
    /**
     * 内测提醒明细
     */
    private List<VipPassRemindDetailResponseDto> betaDetails;

}
