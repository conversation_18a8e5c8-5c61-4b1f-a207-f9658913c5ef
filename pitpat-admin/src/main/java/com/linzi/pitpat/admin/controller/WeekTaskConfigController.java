package com.linzi.pitpat.admin.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.dao.activity.WeekTaskConfigDao;
import com.linzi.pitpat.data.dao.activity.WeekTaskConfigListDao;
import com.linzi.pitpat.data.entity.activity.WeekTaskConfig;
import com.linzi.pitpat.data.entity.activity.WeekTaskConfigList;
import com.linzi.pitpat.data.entity.po.WeekTaskConfigDto;
import com.linzi.pitpat.data.entity.po.WeekTaskConfigResp;
import com.linzi.pitpat.data.resp.WeekTaskConfigType;
import com.linzi.pitpat.data.resp.WeekTaskDownDto;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.service.WeekUserAwardRecordService;
import com.linzi.pitpat.data.userservice.service.WeekUserDataService;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping({"/week/task", "/test/week/task"})
@Slf4j
public class WeekTaskConfigController {
    @Autowired
    private WeekTaskConfigDao weekTaskConfigDao;

    @Autowired
    private WeekUserDataService weekUserDataService;

    @Autowired
    private WeekTaskConfigListDao weekTaskConfigListDao;

    @Autowired
    private CouponService couponService;

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private WeekUserAwardRecordService weekUserAwardRecordService;


    @PostMapping("/list")
    public Result<Page<WeekTaskConfig>> list(@RequestBody WeekTaskConfigDto dto) {
        Page<WeekTaskConfig> page = weekTaskConfigDao.selectPageList(new Page(dto.getPageNum(), dto.getPageSize()), dto.getId());
        return CommonResult.success(page);
    }

    //  http://localhost:7771/admin/test/week/task/detail/downLoad?id=9
    @GetMapping("/detail/downLoad")
    public void downLoad(WeekTaskConfigDto dto, HttpServletResponse response, HttpServletRequest request) {
        WeekTaskConfig weekTaskConfig = weekTaskConfigDao.selectWeekTaskConfigById(dto.getId());
        List<WeekTaskDownDto> weekTaskDownDtos = weekUserDataService.selectDownLoadData(weekTaskConfig.getYearMonthN(), weekTaskConfig.getWeekN(), BigDecimal.ZERO, 0);
        if (weekTaskDownDtos != null && weekTaskDownDtos.size() > 0) {
            for (int i = 0; i < weekTaskDownDtos.size(); i++) {
                WeekTaskDownDto weekTaskDownDto = weekTaskDownDtos.get(i);
                weekTaskDownDto.setRank(i + 1);
                BigDecimal bigDecimal = weekTaskDownDto.getRunDistance() == null ? BigDecimal.ZERO : weekTaskDownDto.getRunDistance();
                weekTaskDownDto.setRunDistance(BigDecimalUtil.div(bigDecimal, new BigDecimal(1600)));
                weekTaskDownDto.setCountry(UserConstant.convertCountry(weekTaskDownDto.getCountry()));
            }
        }

        ExcelUtil<WeekTaskDownDto> util = new ExcelUtil<WeekTaskDownDto>(WeekTaskDownDto.class);
        util.exportExcel(weekTaskDownDtos, "周排行榜-明细-" + DateUtil.formateDateStr(ZonedDateTime.now(), DateUtil.YYYYMMDDHHMMSS), response, request);
    }


    @PostMapping("/toEdit")
    public Result edit(@RequestBody WeekTaskConfigDto dto) {
        WeekTaskConfig weekTaskConfig = weekTaskConfigDao.selectWeekTaskConfigById(dto.getId());
        List<WeekTaskConfigList> weekTaskConfigListList = weekTaskConfigListDao.selectWeekTaskConfigListByWeekTaskConfigId(
                weekTaskConfig.getId());
        return CommonResult.success(weekTaskConfigListList);
    }

    @PostMapping("/doEdit")
    public Result doEdit(@RequestBody WeekTaskConfigDto dto) {
        WeekTaskConfig weekTaskConfig = weekTaskConfigDao.selectWeekTaskConfigById(dto.getId());
        for (WeekTaskConfigList weekTaskConfigList : dto.getConfigList()) {
            if (weekTaskConfigList.getRunDistance() != null && weekTaskConfigList.getRunDistance().intValue() > 1600 * 1000) {
                return CommonResult.fail("你写的距离太大，不允许超过1000英里");
            }
        }
        weekTaskConfigListDao.deleteWeekTaskConfigListByMonthWeekN(weekTaskConfig.getId());
        for (WeekTaskConfigList weekTaskConfigList : dto.getConfigList()) {
            weekTaskConfigList.setYearMonthN(weekTaskConfig.getYearMonthN());
            weekTaskConfigList.setWeekN(weekTaskConfig.getWeekN());
            weekTaskConfigList.setWeekTaskConfigId(weekTaskConfig.getId());
            weekTaskConfigListDao.insertWeekTaskConfigList(weekTaskConfigList);
        }
        return CommonResult.success();
    }

    @PostMapping("/preView")
    public Result preView(@RequestBody WeekTaskConfigDto dto) {
        WeekTaskConfigResp resp = new WeekTaskConfigResp();

        WeekTaskConfig weekTaskConfig = weekTaskConfigDao.selectWeekTaskConfigById(dto.getId());

        WeekTaskConfigType type3 = weekTaskConfigListDao.selectWeekTaskConfigListByYearMonthWeekType(weekTaskConfig.getId(), 3);

        Coupon coupon = couponService.selectCouponById(type3.getCouponId());

        type3.setCoupon(coupon);

        Integer upCondition3 = weekUserDataService.selectWeekUserDataByYearMonthRunDistanceRunDay(type3.getYearMonthN(), type3.getWeekN(), type3.getRunDistance(), type3.getDayN());
        type3.setUpCondition(upCondition3);
        Integer getNum3 = weekUserAwardRecordService.selectWeekUserAwardRecordByTaskConfigListId(type3.getId());
        type3.setGetNum(getNum3);


        List<WeekTaskConfigType> type2 = weekTaskConfigListDao.selectWeekTaskConfigListByYearMonthWeekTypeList(weekTaskConfig.getId(), 2);
        for (WeekTaskConfigType weekTaskConfigType : type2) {
            Integer upCondition2 = weekUserDataService.selectWeekUserDataByYearMonthRunDistanceRunDay(weekTaskConfigType.getYearMonthN(), weekTaskConfigType.getWeekN(), null, weekTaskConfigType.getDayN());
            Integer getNum2 = weekUserAwardRecordService.selectWeekUserAwardRecordByTaskConfigListId(weekTaskConfigType.getId());
            weekTaskConfigType.setUpCondition(upCondition2);
            weekTaskConfigType.setGetNum(getNum2);
        }

        List<WeekTaskConfigType> type1 = weekTaskConfigListDao.selectWeekTaskConfigListByYearMonthWeekTypeList(weekTaskConfig.getId(), 1);
        for (WeekTaskConfigType weekTaskConfigType : type1) {
            Integer upCondition1 = weekUserDataService.selectWeekUserDataByYearMonthRunDistanceRunDay(weekTaskConfigType.getYearMonthN(), weekTaskConfigType.getWeekN(), weekTaskConfigType.getRunDistance(), null);
            Integer getNum1 = weekUserAwardRecordService.selectWeekUserAwardRecordByTaskConfigListId(weekTaskConfigType.getId());
            weekTaskConfigType.setUpCondition(upCondition1);
            weekTaskConfigType.setGetNum(getNum1);
        }


        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.rank_billboard_config.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        int rankPerson = MapUtil.getInteger(data.get("rankPerson"), 0);
        List<WeekTaskDownDto> weekUserDataList = weekUserDataService.selectDownLoadDataByLimit(weekTaskConfig.getYearMonthN(), weekTaskConfig.getWeekN(), rankPerson);

        resp.setDataType3(type3);
        resp.setDataType2(type2);
        resp.setDateType1(type1);
        weekUserDataList.forEach(e -> e.setCountry(UserConstant.convertCountry(e.getCountry())));
        resp.setWeekUserDataList(weekUserDataList);
        return CommonResult.success(resp);
    }

    @PostMapping("/couponList")
    public Result couponList(@RequestBody WeekTaskConfigDto dto) {
        List<Coupon> couponList = couponService.selectCouponByType(2);
        return CommonResult.success(couponList);
    }


    public static void main(String[] args) {
        WeekTaskConfigList weekTaskConfigList1 = new WeekTaskConfigList();
        weekTaskConfigList1.setType(1);
        weekTaskConfigList1.setCouponId(5l);
        weekTaskConfigList1.setDayN(5);

        WeekTaskConfigList weekTaskConfigList2 = new WeekTaskConfigList();
        weekTaskConfigList2.setType(2);
        weekTaskConfigList2.setCouponId(5l);
        weekTaskConfigList2.setRunDistance(new BigDecimal(30));

        WeekTaskConfigList weekTaskConfigList3 = new WeekTaskConfigList();
        weekTaskConfigList3.setType(3);
        weekTaskConfigList3.setCouponId(5l);
        weekTaskConfigList3.setRunDistance(new BigDecimal(30));
        weekTaskConfigList3.setDayN(5);
        List<WeekTaskConfigList> list = new ArrayList<>();
        list.add(weekTaskConfigList1);
        list.add(weekTaskConfigList2);
        list.add(weekTaskConfigList3);
        Map<String, Object> map = new HashMap<>();

        map.put("configList", list);
        map.put("id", 10);
        System.out.println(JsonUtil.writeString(map));

    }

}
