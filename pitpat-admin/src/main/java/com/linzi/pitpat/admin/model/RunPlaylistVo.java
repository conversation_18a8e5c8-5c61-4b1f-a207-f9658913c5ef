package com.linzi.pitpat.admin.model;

import com.linzi.pitpat.data.enums.MusicUsageStateEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class RunPlaylistVo {

    //主键
    private Long id;
    //歌单名称
    @NotBlank(message = "标题不能为空")
    private String title;
    //适合步频
    private Integer stepRate;
    /**
     * 状态:0-未上架，1-上架
     *
     * @see MusicUsageStateEnum
     */
    private Integer usageState;
    //歌曲数量
    private Integer musicNum;
    //备注
    private String remark;
    //修改人
    private String modifier;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;
    //音乐列表
    private List<RunMusicVo> musicVoList;

}
