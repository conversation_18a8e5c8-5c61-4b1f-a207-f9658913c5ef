package com.linzi.pitpat.admin.model;


import com.linzi.pitpat.data.equipmentservice.enums.TreadmillAudioSceneEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class TreadmillAudioVo {

    private Long id;
    //音频名称
    @NotBlank(message = "音频名称不能为空")
    private String audioName;
    //语言code，en_US：英语，fr_CA ：法语
    private String languageCode;
    //语言名称
    private String languageName;
    /**
     * 场景
     *
     * @see TreadmillAudioSceneEnum
     */
    private Integer scene;
    //音频文件名称
    private String fileName;
    //音频文件url
    private String url;
    //音频内容
    private String audioContent;
    //播报规则
    private String audioRule;

    //创建时间
    private ZonedDateTime gmtCreate;
    //修改时间
    private ZonedDateTime gmtModified;
    //创建者
    private String creator;
    //修改者
    private String modifier;

    // 音频序号
    private Integer orderNumber;

}
