package com.linzi.pitpat.admin.controller.activity;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.commom.service.TokenService;
import com.linzi.pitpat.admin.model.LoginUser;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.systemservice.dto.response.CustomH5Dto;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.model.entity.CustomH5;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.CustomH5Service;
import com.linzi.pitpat.lang.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * H5自定义页面
 */
@RestController
@RequestMapping({"/customh5", "/test/customh5"})
public class CustomH5Contoller extends BaseController {


    @Autowired
    private CustomH5Service customH5Service;

    @Resource
    private AppRouteConfigService appRouteConfigService;

    @Autowired
    private TokenService tokenService;

    /**
     * H5自定义页面 列表
     *
     * @param customH5
     * @return
     */
    @PostMapping("/list")
    public Result<Page<CustomH5>> searchRunActivityByCondition(@RequestBody CustomH5Dto customH5) {
        Page<CustomH5> customH5Page = customH5Service.selectByCondition(Page.of(customH5.getPageNum(), customH5.getPageSize()), customH5.getTitle(), customH5.getStatus());
        return CommonResult.success(customH5Page);
    }

    /**
     * H5自定义页面 增加更新
     *
     * @param customH5
     * @return
     */
    @PostMapping("/insertOrUpdate")
    @Log(title = "H5自定义", businessType = BusinessType.UPDATE)
    public Result insertOrUpdate(@RequestBody CustomH5 customH5) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getUser();
        if (sysUser != null) {
            customH5.setUpdatePerson(sysUser.getNickName());
        }
        if (customH5.getId() == null) {
            if (sysUser != null) {
                customH5.setCreatePerson(sysUser.getNickName());
            }
        }
        customH5.setGmtModified(ZonedDateTime.now());
        if (customH5.getId() == null) {
            customH5Service.insert(customH5);
        } else {
            customH5Service.update(customH5);
        }
        return CommonResult.success();
    }


    /**
     * H5自定义页面 增加更新
     *
     * @param customH5
     * @return
     */
    @PostMapping("/updateStatus")
    @Log(title = "H5自定义", businessType = BusinessType.UPDATE)
    public Result updateStatus(@RequestBody CustomH5 customH5) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getUser();
        String operateName = "";
        if (sysUser != null) {
            operateName = sysUser.getNickName();
        }
        customH5Service.updateState(customH5.getId(), customH5.getStatus(), operateName);
        return CommonResult.success();
    }


    /**
     * H5自定义页面 增加更新
     *
     * @param customH5
     * @return
     */
    @PostMapping("/appRouteConfigList")
    public Result<List<AppRouteConfig>> appRouteConfigList(@RequestBody CustomH5 customH5) {
        List<AppRouteConfig> appRouteConfigs = appRouteConfigService.selectAppRouteConfigAll();
        return CommonResult.success(appRouteConfigs);
    }


    /**
     * 通过id获取
     *
     * @return
     */
    @PostMapping("/getById")
    public Result<CustomH5> getById(@RequestBody CustomH5 customH5) {
        CustomH5 customH51 = customH5Service.selectById(customH5.getId());
        return CommonResult.success(customH51);
    }


}

