package com.linzi.pitpat.admin.mallservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.mallservice.convert.MallCategoryConverter;
import com.linzi.pitpat.admin.mallservice.convert.MallCategoryPageConverter;
import com.linzi.pitpat.admin.mallservice.dto.request.MallCategoryGoodsRequestDto;
import com.linzi.pitpat.admin.mallservice.dto.request.MallCategoryPageRequestDto;
import com.linzi.pitpat.admin.mallservice.dto.request.MallCategoryRequestDto;
import com.linzi.pitpat.admin.mallservice.dto.request.SortRequestDto;
import com.linzi.pitpat.admin.mallservice.dto.response.CategoryGoodsExcelDto;
import com.linzi.pitpat.admin.mallservice.dto.response.MallCategoryGoodsResponseDto;
import com.linzi.pitpat.admin.mallservice.dto.response.MallCategoryPageResponseDto;
import com.linzi.pitpat.admin.mallservice.dto.response.MallCategoryResponseDto;
import com.linzi.pitpat.admin.mallservice.manager.MallCategoryManager;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.mallservice.dto.request.MallCategoryPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallCategoryQueryDto;
import com.linzi.pitpat.data.mallservice.dto.response.ContentI8nDto;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategory;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryPage;
import com.linzi.pitpat.data.mallservice.service.MallCategoryGoodsService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryPageService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryService;
import com.linzi.pitpat.data.util.file.FileUtils;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商城类目页管理
 */

@Slf4j
@RestController
@RequestMapping("/mall/category")
@RequiredArgsConstructor
public class MallCategoryController extends BaseController {

    private final MallCategoryService mallCategoryService;
    private final MallCategoryPageService mallCategoryPageService;
    private final MallCategoryGoodsService mallCategoryGoodsService;
    private final MallCategoryConverter manageCategoryConverter;
    private final MallCategoryPageConverter mallCategoryPageConverter;
    private final MallCategoryManager mallCategoryManager;


    /**
     * 类目页列表
     *
     * @param pageRequestDto
     * @return
     */
    @PostMapping("/page/list")
    public Result<Page<MallCategoryPageResponseDto>> pageList(@RequestBody MallCategoryPageRequestDto pageRequestDto) {
        MallCategoryPageQueryDto mallCategoryPageQueryDto = new MallCategoryPageQueryDto();
        BeanUtils.copyProperties(pageRequestDto, mallCategoryPageQueryDto);
        Page<MallCategoryPage> mallCategoryList = mallCategoryPageService.findPageByQuery(mallCategoryPageQueryDto);
        List<MallCategoryPage> records = mallCategoryList.getRecords();
        Page<MallCategoryPageResponseDto> responsePageList = new Page<>(
                mallCategoryList.getCurrent(),
                mallCategoryList.getSize(),
                mallCategoryList.getTotal()
        );
        if (!CollectionUtils.isEmpty(records)) {
            List<MallCategoryPageResponseDto> collect = records.stream().map(mallCategoryPageConverter::toResponseDto).toList();
            responsePageList.setRecords(collect);
        }
        return CommonResult.success(responsePageList);
    }

    /**
     * 类目页创建
     *
     * @param pageRequestDto
     * @return
     */
    @PostMapping("/page/create")
    public Result<MallCategoryPageResponseDto> pageCreate(@RequestBody MallCategoryPageRequestDto pageRequestDto) {
        MallCategoryPage categoryPage = mallCategoryPageConverter.toEntry(pageRequestDto);
        categoryPage.setCreator(SecurityUtils.getUsername());
        mallCategoryPageService.insert(categoryPage);
        String prefix = "LM";
        // 将ID转换为8位，不足的部分填充0
        String paddedId = String.format("%08d", categoryPage.getId());
        categoryPage.setCategoryPageCode(prefix + paddedId);
        mallCategoryPageService.update(categoryPage);
        return CommonResult.success();
    }

    /**
     * 类目页修改
     *
     * @param pageRequestDto
     * @return
     */
    @PostMapping("/page/update")
    public Result<MallCategoryPageResponseDto> pageUpdate(@RequestBody MallCategoryPageRequestDto pageRequestDto) {
        MallCategoryPage page = mallCategoryPageService.findById(pageRequestDto.getId());
        if (Objects.isNull(page)) {
            throw new BaseException("数据不存在,查看ID是否正确");
        }
        page.setRemark(pageRequestDto.getRemark());
        page.setDefaultLanguageCode(pageRequestDto.getDefaultLanguageCode());
        mallCategoryPageService.update(page);
        return CommonResult.success();
    }

    /**
     * 类目页详情
     *
     * @param id
     * @return
     */
    @GetMapping("/page/detail")
    public Result<MallCategoryPageResponseDto> pageDetail(@RequestParam(required = false) Long id, @RequestParam(required = false) String categoryCode) {
        MallCategoryPage categoryPage = null;
        if (id != null) {
            categoryPage = mallCategoryPageService.findById(id);
        } else if (categoryCode != null) {
            categoryPage = mallCategoryPageService.findByCode(categoryCode);
        }
        MallCategoryPageResponseDto responseDto = mallCategoryPageConverter.toResponseDto(categoryPage);
        return CommonResult.success(responseDto);
    }

    /**
     * 类目页删除
     *
     * @param id
     * @return
     */
    @GetMapping("/page/delete")
    public Result<Void> pageDelete(@RequestParam Long id) {
        mallCategoryManager.deleteByCategoryPageId(id);
        return CommonResult.success();
    }

    /**
     * 类目页排序
     *
     * @param sortRequest
     * @return
     */
    @PostMapping("/page/sort")
    public Result<Void> pageSort(@RequestBody SortRequestDto sortRequest) {
        mallCategoryPageService.sort(sortRequest.getSorts());
        return CommonResult.success();
    }

    /**
     * 类目列表
     *
     * @param mallCategoryQueryDto
     * @return
     */
    @PostMapping("/list")
    public Result<List<MallCategoryResponseDto>> list(@RequestBody MallCategoryQueryDto mallCategoryQueryDto) {
        List<MallCategory> mallCategoryList = mallCategoryService.findListByQuery(mallCategoryQueryDto);
        if (CollectionUtils.isEmpty(mallCategoryList)) {
            return CommonResult.success();
        }
        List<MallCategoryResponseDto> collect = mallCategoryList.stream().map(manageCategoryConverter::toResponseDto).collect(Collectors.toList());
        for (MallCategoryResponseDto mallCategoryResponseDto : collect) {
            List<ContentI8nDto> contentI8nDtos = JsonUtil.readList(mallCategoryResponseDto.getName(), ContentI8nDto.class);
            contentI8nDtos.stream().filter(s -> Objects.equals(s.getLanguageCode(), mallCategoryResponseDto.getDefaultLanguageCode())).findFirst().ifPresent(s -> mallCategoryResponseDto.setName(s.getContent()));
        }
        return CommonResult.success(collect);
    }

    /**
     * 类目详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public Result<MallCategoryResponseDto> detail(@RequestParam Long id) {
        return CommonResult.success(mallCategoryManager.categoryDetail(id));
    }

    /**
     * 类目创建
     *
     * @param mallCategoryRequestDto
     * @return
     */
    @PostMapping("/create")
    public Result<Void> create(@RequestBody @Validated MallCategoryRequestDto mallCategoryRequestDto) {
        mallCategoryRequestDto.setCreator(SecurityUtils.getUsername());
        mallCategoryManager.categoryCreate(mallCategoryRequestDto);
        return CommonResult.success();
    }

    /**
     * 类目修改
     *
     * @param mallCategoryRequestDto
     * @return
     */
    @PostMapping("/update")
    public Result<Void> update(@RequestBody @Validated MallCategoryRequestDto mallCategoryRequestDto) {
        mallCategoryManager.categoryUpdate(mallCategoryRequestDto);
        return CommonResult.success();
    }

    /**
     * 类目删除
     *
     * @param categoryId
     * @return
     */
    @GetMapping("/delete")
    public Result<Void> delete(@RequestParam Long categoryId) {
        mallCategoryService.deleteById(categoryId);
        mallCategoryGoodsService.deleteByCategoryId(categoryId);
        return CommonResult.success();
    }

    /**
     * 类目列表排序
     *
     * @param sortRequest
     * @return
     */
    @PostMapping("/sort")
    public Result<Void> mallCategorySort(@RequestBody SortRequestDto sortRequest) {
        mallCategoryService.sort(sortRequest.getSorts());
        return CommonResult.success();
    }

    /**
     * 添加类目商品
     *
     * @param categoryGoodsRequestDto
     * @return
     */
    @PostMapping("/add/goods")
    public Result<Void> addGoods(@RequestBody MallCategoryGoodsRequestDto categoryGoodsRequestDto) {
        mallCategoryManager.addCategoryGoods(categoryGoodsRequestDto);
        return CommonResult.success();
    }

    /**
     * 导入商品
     *
     * @param excel
     */
    @PostMapping("/goods/importExcel")
    @Log(title = "上传商品", businessType = BusinessType.INSERT)
    public Result<List<MallCategoryGoodsResponseDto>> importExcel(@RequestParam("file") MultipartFile excel) throws Exception {

        List<String> emails = FileUtils.readFirstColumn(excel);
        if (CollectionUtils.isEmpty(emails)) {
            throw new BaseException("excel 数量为空！");
        }
        List<CategoryGoodsExcelDto> list = emails.stream().map(s -> {
            CategoryGoodsExcelDto categoryGoodsExcelDto = new CategoryGoodsExcelDto();
            categoryGoodsExcelDto.setId(Long.valueOf(s));
            return categoryGoodsExcelDto;
        }).collect(Collectors.toList());
        return CommonResult.success(mallCategoryManager.excelImportGoods(list));
    }

    public static void main(String[] args) {
        String prefix = "LM";
        // 将ID转换为8位，不足的部分填充0
        String paddedId = String.format("%08d", 123L);
        System.out.println(prefix + paddedId);
    }

}
