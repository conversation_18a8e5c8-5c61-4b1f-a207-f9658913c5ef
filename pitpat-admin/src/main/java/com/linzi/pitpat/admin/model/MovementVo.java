package com.linzi.pitpat.admin.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class MovementVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    //动作名称
    private String movementName;
    //动作英文名称
    private String movementEnName;
    //动作难度

    private String movementLevel;
    //适用性别 性别，1：男，2：女，0：通用
    private Integer applicableGender;
    //动作类型 0：无氧，1：有氧
    private Integer movementType;
    //锻炼部位
    private List<Integer> exercisePart;
    //锻炼肌肉
    private List<List<Integer>> exerciseMuscle;
    //所需器械
    private List<Integer> requireMachine;
    //预计消耗热量
    private Integer heatConsumption;
    //动作视频

    private String video;
    //视频时长
    private String videoDuration;
    //封面
    private String cover;
    //动作介绍


    private String movementIntroduction;
    //备注描述


    private String remark;

    //
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private ZonedDateTime updateTime;
    //
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private ZonedDateTime createTime;
}
