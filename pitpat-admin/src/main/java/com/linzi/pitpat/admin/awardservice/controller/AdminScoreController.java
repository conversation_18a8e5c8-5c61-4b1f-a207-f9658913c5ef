package com.linzi.pitpat.admin.awardservice.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.bussiness.ExchangeScoreProductBusiness;
import com.linzi.pitpat.admin.model.Dto.request.ExchangeReserveUpdateRequest;
import com.linzi.pitpat.admin.model.Dto.response.ExchangeReserveUpdateResponse;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleDto;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleStatusDto;
import com.linzi.pitpat.data.activityservice.model.dto.InsertExchangeScoreRuleDto;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.awardservice.biz.ScoreExchangeRuleBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.dto.consloe.ScoreRuleI18nRequestDto;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleList;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.query.ExchangeScoreRuleQuery;
import com.linzi.pitpat.data.awardservice.model.request.ExchangeScoreRecordReq;
import com.linzi.pitpat.data.awardservice.model.request.RegisterAfterConfigReq;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRecordResp;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRuleResp;
import com.linzi.pitpat.data.awardservice.model.resp.RegisterAfterConfigResp;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleI18nService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleListService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.ScoreConfigService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.mallservice.manager.console.GoodsConsoleManager;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.messageservice.model.entity.MessageTaskMsg;
import com.linzi.pitpat.data.messageservice.service.MessageTaskMsgService;
import com.linzi.pitpat.data.request.BaseReq;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 积分管理
 *
 * <AUTHOR>
 * @since 2023年4月27日14:16:25
 */
@RestController
@RequestMapping({"/score", "/test/score"})
@Slf4j
public class AdminScoreController {


    @Autowired
    private ScoreConfigService scoreConfigService;

    @Autowired
    private ExchangeScoreRuleListService exchangeScoreRuleListService;

    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;

    @Resource
    private ExchangeScoreAwardService scoreAwardService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private MessageTaskMsgService messageTaskMsgService;

    @Autowired
    private WearsService wearsService;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private ExchangeScoreProductBusiness exchangeScoreProductBusiness;

    @Resource
    private ExchangeScoreRuleService exchangeScoreRuleService;
    @Resource
    private ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    @Resource
    private ExchangeScoreRuleI18nService exchangeScoreRuleI18nService;

    @Resource
    private UserLevelRuleService userLevelRuleService;
    @Resource
    private GoodsConsoleManager goodsConsoleManager;
    @Resource
    private ScoreMallGoodsRelationService scoreMallGoodsRelationService;
    @Resource
    private ZnsGoodsService znsGoodsService;
    @Resource
    private ZnsGoodsSkuService znsGoodsSkuService;
    @Resource
    private ScoreExchangeRuleBizService scoreExchangeRuleBizService;

    /**
     * 积分获取任务数据获取
     *
     * @return
     */
    @PostMapping("/manager")
    public Result<List<ScoreConfig>> searchRunActivityByCondition(@RequestBody BaseReq baseReq) {
        List<ScoreConfig> scoreConfigList = scoreConfigService.list(new QueryWrapper<>());
        return CommonResult.success(scoreConfigList);
    }

    /**
     * 积分获取任务设置
     *
     * @return
     */
    @PostMapping("/updateScoreConfig")
    public Result updateScoreConfig(@RequestBody ScoreConfig scoreConfig) {
        scoreConfigService.updateById(scoreConfig);
        return CommonResult.success();
    }

    /**
     * 积分兑换管理列表
     *
     * @return
     */
    @PostMapping("/exchangeScoreRuleList")
    public Result<Page<ExchangeScoreRuleResp>> exchangeScoreRule(@RequestBody ExchangeScoreRuleDto exchangeScoreRuleDto) {
        Page<ExchangeScoreRuleResp> page = exchangeScoreRuleService.findPage(exchangeScoreRuleDto);

        List<ExchangeScoreRuleResp> records = page.getRecords();
        records.stream().forEach(item -> {
            // 兼容旧数据
            if (Objects.isNull(item.getExchangeType()) && Objects.nonNull(item.getCouponId())) {
                item.setExchangeType(ExchangeScoreTypeEnum.COUPON.getCode());
            }
            if (item.getExchangeAmount().compareTo(new BigDecimal(0)) == 0) {
                item.getExchangeAmount().setScale(2);
            }

            if (ExchangeScoreTypeEnum.COUPON.getCode().equals(item.getExchangeType())) {
                Long couponId = item.getCouponId();
                if (Objects.nonNull(couponId)) {
                    Coupon coupon = couponService.selectCouponById(couponId);
                    if (coupon.getQuota() == -1) {
                        item.setRemainQuota(-1); // 不限制
                    } else {
                        Integer leftNum = coupon.getQuota() - coupon.getQuotaSend();
                        item.setRemainQuota(leftNum);
                    }
                }
            }

            Long ruleId = item.getId();
            ExchangeScoreRuleI18n i18n = exchangeScoreRuleI18nService.selectOneByQuery(ExchangeScoreRuleQuery.builder().ruleId(ruleId).langCode(item.getDefaultLangCode()).build());
            if (Objects.nonNull(i18n)) {
                item.setActivityName(i18n.getActivityName());
            }
        });
        page.setRecords(records);
        return CommonResult.success(page);
    }


    /**
     * 积分兑换管理详情
     *
     * @return
     */
    @PostMapping("/getExchangeScoreRuleById")
    public Result<InsertExchangeScoreRuleDto> getExchangeScoreRuleById(@RequestBody ExchangeScoreRuleStatusDto exchangeScoreRuleDto) {
        ExchangeScoreRuleResp exchangeScoreRuleResp = new ExchangeScoreRuleResp();
        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.getById(exchangeScoreRuleDto.getId());
        BeanUtils.copyProperties(exchangeScoreRule, exchangeScoreRuleResp);

        ExchangeScoreAward award = scoreAwardService.getOne(Wrappers.<ExchangeScoreAward>lambdaQuery()
                .eq(ExchangeScoreAward::getIsDelete, 0)
                .eq(ExchangeScoreAward::getRuleId, exchangeScoreRule.getId()));
        if (Objects.isNull(award)) {
            if (exchangeScoreRule.getCouponId() == null) {
                throw new BaseException("未找到兑换商品信息");
            }
            // 兼容旧数据
            exchangeScoreRuleResp.setExchangeType(ExchangeScoreTypeEnum.COUPON.getCode());
            exchangeScoreRuleResp.setExchangeId(exchangeScoreRule.getCouponId());
        } else {
            exchangeScoreRuleResp.setExchangeType(award.getExchangeType());
            exchangeScoreRuleResp.setExchangeId(award.getExchangeId());
        }

        ExchangeScoreTypeEnum typeEnum = ExchangeScoreTypeEnum.findByType(exchangeScoreRuleResp.getExchangeType());
        switch (typeEnum) {
            case COUPON:
                // 优惠券
                Long couponId = exchangeScoreRuleResp.getExchangeId();
                Coupon coupon = couponService.selectCouponById(couponId);
                if (Objects.isNull(coupon)) {
                    throw new BaseException("查询不到绑定的优惠券信息");
                }
                exchangeScoreRuleResp.setCouponId(couponId);
                exchangeScoreRuleResp.setCouponType(coupon.getCouponType());
                exchangeScoreRuleResp.setName(coupon.getName());
                exchangeScoreRuleResp.setTitle(coupon.getTitle());
                exchangeScoreRuleResp.setExchangeName(typeEnum.getDesc());
                break;
            case CLOTHES:
                // 服装
                Long clothId = exchangeScoreRuleResp.getExchangeId();
                Wears wears = wearsService.selectWearsById(clothId);
                exchangeScoreRuleResp.setWearId(wears.getWearId());
                exchangeScoreRuleResp.setWearType(wears.getWearType());
                exchangeScoreRuleResp.setWearName(wears.getWearName());
                exchangeScoreRuleResp.setExchangeName(typeEnum.getDesc());
                exchangeScoreRuleResp.setExpiredTime(award.getExpiredTime());
                break;
            case MUSIC:
                // 音乐
                break;
            case PROPS:
                // 道具
                break;
            case PHYSICAL_REWARDS:
                // 实物奖励
                break;
            default:
                // 其他
                exchangeScoreRuleResp.setExchangeName(typeEnum.getDesc());
        }

        //查询多币种兑换规则(排除美元)
        List<CurrencyAmount> currencyAmountList = new ArrayList<>();
        List<ExchangeScoreRuleCurrencyEntity> list = exchangeScoreRuleCurrencyService.selectByRuleIdExcludeCurrency(exchangeScoreRule.getId(), null);
        List<CurrencyAmount> currencyOriginalAmountList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list) && list.get(0) != null) {
            list.forEach(o -> currencyAmountList.add(I18nConstant.buildCurrencyAmount(o.getCurrencyCode(), o.getExchangeAmount())));
            list.forEach(o -> currencyOriginalAmountList.add(I18nConstant.buildCurrencyAmount(o.getCurrencyCode(), o.getOriginalExchangeAmount())));
        }
        exchangeScoreRuleResp.setCurrencyAmountList(currencyAmountList);
        exchangeScoreRuleResp.setCurrencyOriginalExchangeAmountList(currencyOriginalAmountList);

        InsertExchangeScoreRuleDto insertExchangeScoreRuleDto = new InsertExchangeScoreRuleDto();
        insertExchangeScoreRuleDto.setExchangeScoreRule(exchangeScoreRuleResp);

        // 国际化数据
        List<ExchangeScoreRuleI18n> exchangeScoreRuleI18ns = exchangeScoreRuleI18nService.selectListByQuery(ExchangeScoreRuleQuery.builder().ruleId(exchangeScoreRuleDto.getId()).build());
        if (!CollectionUtils.isEmpty(exchangeScoreRuleI18ns)) {
            List<ScoreRuleI18nRequestDto> i18nInfo = BeanUtil.copyBeanList(exchangeScoreRuleI18ns, ScoreRuleI18nRequestDto.class);
            i18nInfo.stream().forEach(item -> {
                String langCode = item.getLangCode();
                // 详情介绍图
                List<ExchangeScoreRuleList> exchangeScoreRuleLists = exchangeScoreRuleListService.list(Wrappers.<ExchangeScoreRuleList>lambdaQuery().eq(ExchangeScoreRuleList::getIsDelete, 0).eq(ExchangeScoreRuleList::getExchangeScoreRuleId, exchangeScoreRuleDto.getId()).eq(ExchangeScoreRuleList::getLangCode, langCode));
                item.setDetailDescImgList(exchangeScoreRuleLists);
            });
            insertExchangeScoreRuleDto.setI18nInfo(i18nInfo);
        } else {
            List<ScoreRuleI18nRequestDto> i18nInfo = new ArrayList<>();
            ScoreRuleI18nRequestDto dto = new ScoreRuleI18nRequestDto();
            dto.setRuleId(exchangeScoreRuleDto.getId());
            dto.setLangCode(exchangeScoreRule.getDefaultLangCode());
            dto.setAdvertiseImage(exchangeScoreRule.getAdvertiseImage());
            dto.setActivityName(exchangeScoreRule.getActivityName());
            dto.setExchangeRule(exchangeScoreRule.getExchangeRule());
            List<ExchangeScoreRuleList> exchangeScoreRuleLists = exchangeScoreRuleListService.list(Wrappers.<ExchangeScoreRuleList>lambdaQuery()
                    .eq(ExchangeScoreRuleList::getIsDelete, 0)
                    .eq(ExchangeScoreRuleList::getExchangeScoreRuleId, exchangeScoreRuleDto.getId())
                    .eq(ExchangeScoreRuleList::getLangCode, exchangeScoreRule.getDefaultLangCode()));
            dto.setDetailDescImgList(exchangeScoreRuleLists);
            i18nInfo.add(dto);
            insertExchangeScoreRuleDto.setI18nInfo(i18nInfo);
        }
//        List<ExchangeScoreRuleList> exchangeScoreRuleListList = exchangeScoreRuleListDao.selectExchangeScoreRuleListByExchangeScoreRuleId(exchangeScoreRule.getId());
//        insertExchangeScoreRuleDto.setExchangeScoreRuleListList(exchangeScoreRuleListList);
        return CommonResult.success(insertExchangeScoreRuleDto);
    }


    /**
     * 删除积分兑换规则
     *
     * @return
     */
    @PostMapping("/deleteExchangeScoreRuleById")
    public Result deleteExchangeScoreRuleById(@RequestBody ExchangeScoreRuleStatusDto exchangeScoreRuleDto) {
        exchangeScoreRuleService.deleteExchangeScoreRuleById(exchangeScoreRuleDto.getId());
        return CommonResult.success();
    }

    /**
     * 等级列表查询
     *
     * @return
     */
    @GetMapping("/queryLevel")
    public Result<Map<Integer, String>> queryLevel() {
        List<UserLevelRule> all = userLevelRuleService.findAll();
        Map<Integer, String> collect = new LinkedHashMap<>();
        all.forEach(e -> collect.put(e.getLevel(), "LV " + e.getLevel()));
        return CommonResult.success(collect);
    }


    /**
     * 积分兑换管理插入或更新
     *
     * @return
     */
    @PostMapping("/exchangeScoreRuleInsertOrUpdate")
    @Log(title = "积分兑换管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public Result exchangeScoreRuleInsertOrUpdate(@RequestBody @Validated InsertExchangeScoreRuleDto exchangeScoreRuleDto) {
        String username = SecurityUtils.getUsername();
        scoreExchangeRuleBizService.exchangeScoreRuleInsertOrUpdate(exchangeScoreRuleDto, username);
        return CommonResult.success();
    }

    /**
     * 变更可兑换量
     *
     * @return
     */
    @PostMapping("/updateExchangeReserve")
    @Log(title = "积分商品变更可兑换量", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public Result<ExchangeReserveUpdateResponse> updateExchangeReserve(@RequestBody ExchangeReserveUpdateRequest request) {
        ExchangeReserveUpdateResponse response = new ExchangeReserveUpdateResponse();
        Boolean flag = exchangeScoreProductBusiness.updateExchangeReserve(request);
        response.setFlag(flag);
        return CommonResult.success(response);
    }

    /**
     * 更新上下架状态
     *
     * @return
     */
    @PostMapping("/exchangeScoreRuleStatus")
    @Log(title = "更新上下架状态", businessType = BusinessType.UPDATE)
    public Result exchangeScoreRuleStatus(@RequestBody ExchangeScoreRuleStatusDto exchangeScoreRuleStatusDto) {
        String username = SecurityUtils.getUsername();
        scoreExchangeRuleBizService.changeStatus(exchangeScoreRuleStatusDto, username);
        return CommonResult.success();
    }


    /**
     * 积分兑换记录列表
     *
     * @return
     */
    @Deprecated(since = "4.5.0") //admin 已不再使用，建议删掉
    @PostMapping("/exchangeScoreRecordList")
    public Result<Page<ExchangeScoreRecordResp>> exchangeScoreRecordList(@RequestBody ExchangeScoreRecordReq req) {
        Page<ExchangeScoreRecordResp> page = activityUserScoreDao.selectExchangeScoreRecordByCondition(PageHelper.ofPage(req), req.getEmailAddress(), req.getTitle(), -1);
        return CommonResult.success(page);
    }


    /**
     * 新用户注册后配置 列表
     *
     * @return
     */
    @PostMapping("/registerSendList")
    public Result<RegisterAfterConfigResp> registerSendList(@RequestBody ExchangeScoreRecordReq req) {
        List<MessageTaskMsg> registerAfterConfigs = messageTaskMsgService.findListByTask(Arrays.asList(-1, -2));
        for (MessageTaskMsg messageTaskMsg : registerAfterConfigs) {
            if (messageTaskMsg.getActivityType() == -4) {
                messageTaskMsg.setTaskConfigId(Long.valueOf(messageTaskMsg.getJumpValue()));
            }
        }
        List<MessageTaskMsg> msgList = messageTaskMsgService.findListByTask(Arrays.asList(-2));
        List<Coupon> coupons = couponService.selectCouponByIdRegisterAfterConfig(msgList);
        RegisterAfterConfigResp resp = new RegisterAfterConfigResp();
        resp.setRegisterAfterConfigs(registerAfterConfigs);
        resp.setCoupons(coupons);
        return CommonResult.success(resp);
    }


    /**
     * 批量 新用户注册配置插入或保存
     *
     * @return
     */
    @PostMapping("/batchInsertRegisterAfterConfig")
    @RepeatSubmit
    @Log(title = "新用户注册配置插入或保存", businessType = BusinessType.UPDATE)
    public Result batchInsertRegisterAfterConfig(@RequestBody RegisterAfterConfigReq req) {
        messageTaskMsgService.deleteRegisterTaskIds(Arrays.asList(-1, -2));
        if (req.getRegisterAfterConfigs() != null && req.getRegisterAfterConfigs().size() > 0) {
            for (MessageTaskMsg messageTaskMsg : req.getRegisterAfterConfigs()) {
                if (Objects.nonNull(messageTaskMsg.getActivityType()) && messageTaskMsg.getActivityType() == -4) {
                    messageTaskMsg.setJumpValue(messageTaskMsg.getTaskConfigId().toString());
                } else if (Objects.nonNull(messageTaskMsg.getType()) && messageTaskMsg.getType() == 4 && Objects.nonNull(messageTaskMsg.getActivityId())) {
                    messageTaskMsg.setJumpValue(messageTaskMsg.getActivityId().toString());
                }
                messageTaskMsgService.insert(messageTaskMsg);
            }
        }
        return CommonResult.success();
    }


}
