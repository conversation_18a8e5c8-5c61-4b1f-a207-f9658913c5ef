package com.linzi.pitpat.admin.mallservice.dto.request;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class MallHomePageRequestDto extends PageQuery {
    //id
    private Long id;
    //名称
    private String name;
    //生效开始时间
    private ZonedDateTime startTime;
    //生效结束时间
    private ZonedDateTime endTime;
    //创建开始
    private ZonedDateTime createStartTime;
    //创建结束时间
    private ZonedDateTime createEndTime;
    //所属国家类型多选 US：美国 "US,UK“
    private String countryType;
}
