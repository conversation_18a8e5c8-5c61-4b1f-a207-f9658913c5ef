package com.linzi.pitpat.admin.controller;

import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityRunExportVo;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDetailDao;
import com.linzi.pitpat.data.constants.CacheConstants;
import com.linzi.pitpat.data.entity.po.BaseTimePo;
import com.linzi.pitpat.data.entity.po.DataCachePo;
import com.linzi.pitpat.data.entity.po.DataCacheSwitch;
import com.linzi.pitpat.data.request.ExportHomeDataRequest;
import com.linzi.pitpat.data.service.activity.MaidianLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.data.vo.RegisterUserExportVo;
import com.linzi.pitpat.data.vo.UserLoginDataVo;
import com.linzi.pitpat.data.vo.UserRunDetailsExportVo;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/10/20 10:42
 */
@RestController
@RequestMapping({"/data", "/test/data"})
@RequiredArgsConstructor
public class DataController extends BaseController {
    @Resource
    private ZnsUserService userService;
    @Resource
    private ZnsUserLoginLogService userLoginLogService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private MaidianLogService maidianLogService;

    @Autowired
    private ZnsUserAccountDetailDao znsUserAccountDetailDao;

    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    private final RedissonClient redissonClient;


    @Resource
    private RedisUtil redisUtil;

    @PostMapping("/homeData")
    @RepeatSubmit
    @DataCache({"po.startTime", "po.endTime"})
    public Result homeData(@RequestBody BaseTimePo po) {
        Map<String, Object> map = new HashMap<>();
//        long userRegisterCount = userService.count(Wrappers.<ZnsUserEntity>lambdaQuery()
//                        .eq(ZnsUserEntity::getIsTest,0)
//                        .eq(ZnsUserEntity::getIsRobot,0)
//                        .eq(ZnsUserEntity::getIsDelete,0)
//                .ge(Objects.nonNull(po.getStartTime()), ZnsUserEntity::getCreateTime, po.getStartTime())
//                .le(Objects.nonNull(po.getEndTime()), ZnsUserEntity::getCreateTime, po.getEndTime()));
//        map.put("userRegisterCount",userRegisterCount);
//
//        int userLoginCount = userLoginLogService.userLoginCount(po.getStartTime(), po.getEndTime());
//        map.put("userLoginCount",userLoginCount);
//
//        Map<String, Object> runCount = userRunDataDetailsService.runCount(po.getStartTime(), po.getEndTime());
//        map.putAll(runCount);
//
//        int newUserRunCount = runActivityUserTaskService.getNewUserRunCount(po.getStartTime(), po.getEndTime());
//        map.put("newUserRunCount",newUserRunCount);
//
//        Map<String, Object> mallShareData = maidianLogService.getMallShareData(po.getStartTime(), po.getEndTime());
//        map.putAll(mallShareData);
//
//
//
//        // trade_type 交易类型: 比如1:保证金,2:提现,3:提现服务费,4:提现税费,5:充值,6:组队跑奖励,7:挑战跑奖励,8:排行赛奖励 ,9 官方组队奖励，10 官方累计奖励 ,11 2D跑步彩蛋奖励 12：新人福利 13：分销佣金 14 费用,15 3D跑步彩蛋奖励 16  3D鼓掌奖励 17 调查问卷奖励
//        //  trade_subtype 子交易类型，交易类型8：0：排行赛奖励-排名奖励，1：排行赛奖励-挑战成功奖励，2：排行赛奖励-挑战失败奖励，3：排行赛奖励-被挑战奖励,  如果trade_type = 16  ，则6. 发起鼓掌 ,7 发起加油， 8 发起击掌 ， 9 收到鼓掌，10，收到加油，11 收到击掌
//
//        BigDecimal sendAllAmount = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(6, 7, 8, 9, 10, 12)); // 总奖励发放
//        BigDecimal userDrawAmount = znsUserAccountDetailDao.selectByTodayTradeType( po.getStartTime(),po.getEndTime(), Arrays.asList(2)); // 用户提现金额
//        BigDecimal pk1v1Amount = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(7));   //1V1的PK赛事奖励
//        BigDecimal feiGuanFangAmount  = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(6));   //非官方多人赛事奖励
//        BigDecimal guanFangAmount = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(9));   //官方多人赛事奖励
//        BigDecimal paiHangSaiAmount = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(8));   // 排行挑战赛事奖励
//        BigDecimal lichengbeiAmount = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(10));  //里程碑赛事奖励
//        BigDecimal newPersonAmount  = znsUserAccountDetailDao.selectByTodayTradeType(po.getStartTime(),po.getEndTime(), Arrays.asList(12));  //新用户专区赛奖励
//
//        map.put("sendAllAmount",sendAllAmount);
//        map.put("userDrawAmount",userDrawAmount);
//        map.put("pk1v1Amount",pk1v1Amount);
//        map.put("feiGuanFangAmount",feiGuanFangAmount);
//        map.put("guanFangAmount",guanFangAmount);
//        map.put("paiHangSaiAmount",paiHangSaiAmount);
//        map.put("lichengbeiAmount",lichengbeiAmount);
//        map.put("newPersonAmount",newPersonAmount);
//
//        // 活动类型：1 组队跑,多人同跑  2 挑战跑,竞技跑 3 官方排行赛,官方赛事 4 官方组队跑，多人同跑,官方赛事 5 官方累计跑,官方赛事 6 新人福利活动
//        int runningUserCount = userRunDataDetailsDao.selectRunUsers(po.getStartTime(),po.getEndTime(),60,11062l);
//
//        int finishedUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),1 ,Arrays.asList(1,2,3,4,5));
//        int takeInUserCount = userRunDataDetailsDao.selectZnsoutCountTakeRunTime(po.getStartTime(),po.getEndTime(),null,Arrays.asList(1,2,3,4,5),60);
//
//        int freeRunningUserCount = userRunDataDetailsDao.selectFreeRunningUserCount(po.getStartTime(),po.getEndTime(),Arrays.asList(-1),0,0,0);
//        int courseRunningUserCount = userRunDataDetailsDao.selectCourseRunningUserCount(po.getStartTime(),po.getEndTime(),0);
//        int targetRunningUserCount = userRunDataDetailsDao.selectTargetRunningUserCount(po.getStartTime(),po.getEndTime());
//
//        int pkFinishedUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),1 ,Arrays.asList(2));
//        int pkTakeInUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),null,Arrays.asList(2));
//
//        int feiGuangFangFinishedUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),1 ,Arrays.asList(1));
//        int feiGuangFangTakeInUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),null,Arrays.asList(1));
//
//        int guangFangFinishedUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),1 ,Arrays.asList(4));
//        int guangFangTakeInUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),null,Arrays.asList(4));
//
//
//        int rankFinishedUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),1 ,Arrays.asList(3));
//        int rankTakeInUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),null,Arrays.asList(3));
//
//        int liChengBeiFinishedUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),1 ,Arrays.asList(5));
//        int liChengBeiTakeInUserCount = userRunDataDetailsDao.selectZnsoutCountTake(po.getStartTime(),po.getEndTime(),null,Arrays.asList(5));
//
//        int newPersonFinishedUserCount = runActivityUserTaskDao.selectZnsoutCountTakeNewPerson(po.getStartTime(),po.getEndTime(),1 ,0,11062l);
//        int newPersonTakeInUserCount = runActivityUserTaskDao.selectZnsoutCountTakeNewPerson(po.getStartTime(),po.getEndTime(),null,0,11062l);
//
//
//        map.put("runningUserCount",runningUserCount);       //跑步用户数
//        map.put("finishedUserCount",finishedUserCount);      //完成赛事用户
//        map.put("takeInUserCount",takeInUserCount);     //参加赛事用户
//
//
//        map.put("freeRunningUserCount",freeRunningUserCount);     //自由跑
//        map.put("courseRunningUserCount",courseRunningUserCount);     //课程跑
//        map.put("targetRunningUserCount",targetRunningUserCount);     //目标跑
//
//        map.put("pkFinishedUserCount",pkFinishedUserCount);       //1V1的PK赛事完成赛事用户
//        map.put("pkTakeInUserCount",pkTakeInUserCount);     //1V1的PK 参加赛事用户
//
//
//        map.put("feiGuangFangFinishedUserCount",feiGuangFangFinishedUserCount);       //非官方多人赛事完成赛事用户
//        map.put("feiGuangFangTakeInUserCount",feiGuangFangTakeInUserCount);     //非官方多人赛事 参加赛事用户
//
//
//        map.put("guangFangFinishedUserCount",guangFangFinishedUserCount);       //官方多人赛事完成赛事用户
//        map.put("guangFangTakeInUserCount",guangFangTakeInUserCount);     //官方多人赛事 参加赛事用户
//
//        map.put("rankFinishedUserCount",rankFinishedUserCount);       //排行挑战赛事完成赛事用户
//        map.put("rankTakeInUserCount",rankTakeInUserCount);     //排行挑战赛事参加赛事用户
//
//        map.put("liChengBeiFinishedUserCount",liChengBeiFinishedUserCount);       //里程碑赛事完成赛事用户
//        map.put("liChengBeiTakeInUserCount",liChengBeiTakeInUserCount);     //里程碑赛事加赛事用户
//
//        map.put("newPersonFinishedUserCount",newPersonFinishedUserCount);       //新用户完成赛事用户
//        map.put("newPersonTakeInUserCount",newPersonTakeInUserCount);     //新用户加赛事用户


        return CommonResult.success(ZonedDateTime.now());
    }

    @PostMapping("/exportHomeData")
    public void exportHomeData(@RequestBody ExportHomeDataRequest po, HttpServletResponse response, HttpServletRequest request) {
        if (!StringUtils.hasText(po.getDataType())) {
            return;
        }
        if (1 == 1) {
            return;
        }
        if ("userRegister".equals(po.getDataType())) {
            List<RegisterUserExportVo> registerUserData = userService.getRegisterUserData(po.getStartTime(), po.getEndTime());
            ExcelUtil<RegisterUserExportVo> util = new ExcelUtil<RegisterUserExportVo>(RegisterUserExportVo.class);
            util.exportExcel(registerUserData, "注册用户量-明细", response, request);
        } else if ("userLogin".equals(po.getDataType())) {
            List<UserLoginDataVo> list = userLoginLogService.getUserLoginData(po.getStartTime(), po.getEndTime());
            ExcelUtil<UserLoginDataVo> util = new ExcelUtil<UserLoginDataVo>(UserLoginDataVo.class);
            util.exportExcel(list, "每日登陆日志-明细", response, request);
        } else if ("userRun".equals(po.getDataType())) {
            List<UserRunDetailsExportVo> list = userRunDataDetailsService.getUserRunData(po.getStartTime(), po.getEndTime());
            ExcelUtil<UserRunDetailsExportVo> util = new ExcelUtil<UserRunDetailsExportVo>(UserRunDetailsExportVo.class);
            util.exportExcel(list, "用户跑步频次-明细", response, request);
        } else if ("activityRun".equals(po.getDataType())) {
            List<ActivityRunExportVo> list = userRunDataDetailsService.getActivityRunData(po.getStartTime(), po.getEndTime());
            ExcelUtil<ActivityRunExportVo> util = new ExcelUtil<ActivityRunExportVo>(ActivityRunExportVo.class);
            util.exportExcel(list, "用户参赛总频次-明细", response, request);
        }
    }


    //  http://localhost:7771/admin/test/data/cache/setAndGet?status=1
    // http://**************:7771/admin/test/data/cache/setAndGet?status=0 关闭缓存
    // http://**************:7771/admin/test/data/cache/setAndGet?status=1 开启缓存
    @RequestMapping("/cache/setAndGet")
    public Result cacheSet(DataCachePo po) {
        RBucket<DataCacheSwitch> bucket = redissonClient.getBucket(CacheConstants.redis_cache_key_switch);
        if (po != null && po.getStatus() != null) {
            // 如果设置为0，则系统没有缓存
            if (Objects.equals(po.getStatus(), 0)) {
                DataCacheSwitch cacheSwitch = new DataCacheSwitch(DateUtil.addDays(ZonedDateTime.now(), 1), 0);
                bucket.set(cacheSwitch, 1, TimeUnit.DAYS);
                //redisUtil.set(CacheConstants.redis_cache_key_switch, JsonUtil.writeString(cacheSwitch), RedisConstants.SECOND_OF_ONE_DAY, TimeUnit.SECONDS);
            } else {
                bucket.delete();
                //redisUtil.delete(CacheConstants.redis_cache_key_switch);
            }
        }
        DataCacheSwitch dataCacheSwitch = bucket.get();
        //DataCacheSwitch cachedSwitch = redisUtil.get(CacheConstants.redis_cache_key_switch);
        if (dataCacheSwitch == null) {
            dataCacheSwitch = new DataCacheSwitch(ZonedDateTime.now(), 1);    //设置系统有缓存
        }
        return CommonResult.success(dataCacheSwitch);
    }

}
