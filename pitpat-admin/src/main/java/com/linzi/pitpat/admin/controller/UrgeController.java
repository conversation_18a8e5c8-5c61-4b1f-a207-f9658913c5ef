package com.linzi.pitpat.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.bussiness.UrgeBusiness;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.model.dto.EggActivityConfigDto;
import com.linzi.pitpat.data.awardservice.service.UrgeActivityConfigService;
import com.linzi.pitpat.data.request.ColorEggQuery;
import com.linzi.pitpat.lang.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;


/**
 * @description: 激励类目控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/urge", "/test/urge"})
public class UrgeController extends BaseController {

    @Autowired
    private ZnsRunActivityService znsRunActivityService;
    @Autowired
    private UrgeActivityConfigService urgeActivityConfigService;
    @Resource
    private UrgeBusiness urgeBusiness;

    @PostMapping("/selectAll")
    public Result selectAll(@RequestBody ColorEggQuery query) {
        return urgeBusiness.selectAll(query);
    }


    /**
     * 激励统计
     *
     * @param query
     * @return
     */
    @PostMapping("/selectEveryDayType")
    public Result<Page<EggActivityConfigDto>> selectEveryDayType(@RequestBody ColorEggQuery query) {
        if (query.getGmtEndTime() == null) {
            query.setGmtEndTime(DateUtil.addHours(ZonedDateTime.now(), 8));
        }
        // 6. 发起鼓掌 ,7 发起加油， 8 发起击掌 ， 9 收到鼓掌，10，收到加油，11 收到击掌;
        if (Objects.equals(query.getUrgeType(), 1)) {
            query.setUrgeTypes(Arrays.asList(6, 9));
        } else if (Objects.equals(query.getUrgeType(), 2)) {
            query.setUrgeTypes(Arrays.asList(7, 10));
        } else if (Objects.equals(query.getUrgeType(), 3)) {
            query.setUrgeTypes(Arrays.asList(8, 11));
        }

        Page<EggActivityConfigDto> page = urgeActivityConfigService.selectPageByCondition(PageHelper.ofPage(query), 16, query.getGmtStartTime(), query.getGmtEndTime(), query.getUrgeTypes());
        List<EggActivityConfigDto> configDtos = page.getRecords();

        for (EggActivityConfigDto dto : configDtos) {
            BigDecimal allAmount = BigDecimalUtil.add(dto.getFreeRun(),
                    dto.getComposeRun(), dto.getChallengeRun(), dto.getRankRun(), dto.getGuanfangComposeRun());
            dto.setAllAmount(allAmount);
        }
        return CommonResult.success(page);
    }

}
