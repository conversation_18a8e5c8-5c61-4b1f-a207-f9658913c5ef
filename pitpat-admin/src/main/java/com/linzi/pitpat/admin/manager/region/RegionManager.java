package com.linzi.pitpat.admin.manager.region;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.mallservice.model.entity.MallExchangeRateSwitchDo;
import com.linzi.pitpat.data.mallservice.model.query.MallExchangeRateSwitchQuery;
import com.linzi.pitpat.data.mallservice.service.MallExchangeRateSwitchService;
import com.linzi.pitpat.data.systemservice.dto.request.RegisterCountryRequestDto;
import com.linzi.pitpat.data.systemservice.dto.response.AreaResponseDto;
import com.linzi.pitpat.data.systemservice.dto.response.CountryResponseDto;
import com.linzi.pitpat.data.systemservice.dto.response.RegisterCountryResponseDto;
import com.linzi.pitpat.data.systemservice.enums.RegionConstants;
import com.linzi.pitpat.data.systemservice.mapper.ZnsCountryDao;
import com.linzi.pitpat.data.systemservice.model.entity.ZnsCountryEntity;
import com.linzi.pitpat.data.systemservice.model.query.AreaQuery;
import com.linzi.pitpat.data.systemservice.model.query.CountryQuery;
import com.linzi.pitpat.data.systemservice.model.vo.AreaVo;
import com.linzi.pitpat.data.systemservice.model.vo.CountryVo;
import com.linzi.pitpat.data.systemservice.model.vo.LanguageVo;
import com.linzi.pitpat.data.systemservice.model.vo.MallCountryResponseDto;
import com.linzi.pitpat.data.systemservice.model.vo.MallCountryVo;
import com.linzi.pitpat.data.systemservice.model.vo.RegionResp;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.LanguageService;
import com.linzi.pitpat.data.systemservice.service.RegionService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 区域Manager类
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class RegionManager {

    private final RegionService regionService;
    private final AreaService areaService;
    private final LanguageService languageService;
    private final ZnsCountryService znsCountryService;
    private final ZnsCountryDao znsCountryDao;
    private final MallExchangeRateSwitchService mallExchangeRateSwitchService;

    /**
     * 采用新的区域表,次方法废弃
     *
     * @return
     */
    @Deprecated
    public List<RegionResp> allList() {
        List<RegionResp> result = regionService.selectListByState(RegionConstants.StateEnum.state_1.getCode());
        result.add(new RegionResp("all", "all", "全部国家", "全部国家"));
        return result;
    }

    /**
     * 所有可用国家
     *
     * @return
     */
    public List<RegionResp> allCountry(Integer state) {
        CountryQuery countryQuery = new CountryQuery();
        if (Objects.nonNull(state)) {
            countryQuery.setState(state);
        }
        List<CountryVo> list = znsCountryService.findByQuery(countryQuery);
        List<RegionResp> result = new ArrayList<>(list.size());
        if (!CollectionUtils.isEmpty(list)) {
            for (CountryVo countryVo : list) {
                RegionResp regionResp = new RegionResp();
                regionResp.setCountryCn(countryVo.getNameCn());
                regionResp.setCountryCode(countryVo.getCode());
                regionResp.setCountry(countryVo.getName());
                result.add(regionResp);
            }
        }
        return result;
    }

    /**
     * 国家列表
     *
     * @returnRegisterCountryRequestDto
     */
    @DataCache(value = {"countryCodes"})
    public List<CountryResponseDto> countryList(List<String> countryCodes) {
        //获取所有区域
        AreaQuery query = AreaQuery.builder().build();
        if (!CollectionUtils.isEmpty(countryCodes)) {
            query.setCountryCodes(countryCodes);
        }
        List<AreaVo> list = areaService.findI18NByQuery(query);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        //按层级封装返回数据
        List<CountryResponseDto> result = new ArrayList<>();
        Map<Long, List<AreaVo>> countryMap = list.stream().collect(Collectors.groupingBy(AreaVo::getCountryId));
        for (Map.Entry<Long, List<AreaVo>> entry : countryMap.entrySet()) {
            //国家
            List<AreaVo> areaVoList = entry.getValue();
            AreaVo areaVo = areaVoList.get(0);
            CountryResponseDto countryDto = new CountryResponseDto(areaVo.getCountryId(), areaVo.getCountryName(), areaVo.getCountryNameCn(), areaVo.getCountryCode());
            //区域
            List<AreaResponseDto> areaList = BeanUtil.copyBeanList(areaVoList, AreaResponseDto.class);
            countryDto.setAreaList(areaList);
            result.add(countryDto);
        }
        return result;
    }

    /**
     * 商城国家列表
     */
    public MallCountryResponseDto mallCountryList() {
        MallCountryResponseDto mallCountryResponseDto = new MallCountryResponseDto();
        List<MallExchangeRateSwitchDo> list = mallExchangeRateSwitchService.findList(new MallExchangeRateSwitchQuery().setIsDelete(0).setConfigSwitch(0));
        List<MallCountryVo> countryVos = list.stream().map(mallExchangeRateSwitchDo -> {
            MallCountryVo dto = new MallCountryVo();
            dto.setCountryCode(mallExchangeRateSwitchDo.getCountryCode());
            dto.setCountryName(mallExchangeRateSwitchDo.getCountryName());
            dto.setCurrency(I18nConstant.buildCurrency(mallExchangeRateSwitchDo.getCurrencyCode()));
            return dto;
        }).collect(Collectors.toList());
        mallCountryResponseDto.setMallCountries(countryVos);
        return mallCountryResponseDto;
    }

    public Page<RegisterCountryResponseDto> registerCountryList(RegisterCountryRequestDto req) {
        List<RegisterCountryResponseDto> result = new ArrayList<>();

        Page<ZnsCountryEntity> page = new Page<>(req.getPageNum(), req.getPageSize());
        QueryWrapper<ZnsCountryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        queryWrapper.like(Objects.nonNull(req.getCountry()), "name_cn", req.getCountry());
        queryWrapper.orderByDesc("state");
        queryWrapper.orderByDesc("gmt_create");
        Page<ZnsCountryEntity> znsCountryEntityPage = znsCountryDao.selectPage(page, queryWrapper);

        List<ZnsCountryEntity> znsCountryEntityList = znsCountryEntityPage.getRecords();

        if (!CollectionUtils.isEmpty(znsCountryEntityList)) {
            for (ZnsCountryEntity znsCountryEntity : znsCountryEntityList) {
                RegisterCountryResponseDto dto = new RegisterCountryResponseDto();
                dto.setCountryName(znsCountryEntity.getNameCn());
                dto.setTotalRegisterCount(znsCountryEntity.getRegisteredUsers());
                dto.setIsOpenRegister(znsCountryEntity.getState());
                AreaQuery query = AreaQuery.builder().build();
                query.setCountryCode(znsCountryEntity.getCode());
                List<AreaVo> areaVoList = areaService.findByCountryCode(query);
                //获取支持货币名称
                Set<String> currencyNames = new HashSet<>();
                Set<String> languages = new HashSet<>();
                if (!CollectionUtils.isEmpty(areaVoList)) {
                    currencyNames = areaVoList.stream()
                            .map(AreaVo::getCurrencyName)
                            .collect(Collectors.toSet());

                    List<Long> areaIds = areaVoList.stream()
                            .map(AreaVo::getId)
                            .collect(Collectors.toList());
                    //获取支持的语言
                    List<LanguageVo> languageVoList = languageService.findListByAreaIds(areaIds);
                    if (!CollectionUtils.isEmpty(languageVoList)) {
                        languages = languageVoList.stream().map(LanguageVo::getLanguageName).collect(Collectors.toSet());
                    }

                }
                dto.setCurrencyNameList(currencyNames);
                dto.setLanguageNameList(languages);
                result.add(dto);
            }
        }
        Page<RegisterCountryResponseDto> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(result);
        return resultPage;
    }

    public Boolean updateOpenRegisterStatus(RegisterCountryRequestDto req) {
        return znsCountryService.updateState(req) > 0;
    }
}
