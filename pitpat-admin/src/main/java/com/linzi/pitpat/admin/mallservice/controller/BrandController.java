package com.linzi.pitpat.admin.mallservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.mallservice.converter.console.BrandConsoleConverter;
import com.linzi.pitpat.data.mallservice.dto.console.request.BrandCreateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.BrandPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.BrandStatusRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.BrandUpdateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.BrandResponseDto;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsBrandEntity;
import com.linzi.pitpat.data.mallservice.model.query.BrandPageQuery;
import com.linzi.pitpat.data.mallservice.service.ZnsBrandService;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 品牌控制器
 *
 * @description: 品牌控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/brand")
@RequiredArgsConstructor
public class BrandController extends BaseController {
    @Resource
    private ZnsBrandService brandService;
    private final BrandConsoleConverter brandConsoleConverter;

    /**
     * 商品品牌列表
     *
     * @param pageQueryDto
     * @return
     */
    @PostMapping("/list")
    public Result<Page<BrandResponseDto>> list(@RequestBody BrandPageQueryDto pageQueryDto) {
        BrandPageQuery pageQuery = brandConsoleConverter.toPageQuery(pageQueryDto);
        pageQuery.setOrders(Lists.newArrayList(OrderItem.desc("id")));
        Page<ZnsBrandEntity> page = brandService.findPage(pageQuery);
        return CommonResult.success(brandConsoleConverter.toDtoPage(page));
    }

    /**
     * 增加品牌
     *
     * @param requestDto
     * @return
     */
    @Log(title = "品牌管理", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody @Validated BrandCreateRequestDto requestDto) {
        ZnsBrandEntity brand = brandConsoleConverter.toDo(requestDto);
        Result ajaxResult = brandService.checkBrandNameAllowed(brand);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        brand.setCreateBy(SecurityUtils.getUsername());
        brand.setUpdateBy(SecurityUtils.getUsername());
        brandService.insert(brand);
        return CommonResult.success();
    }

    /**
     * 编辑品牌
     *
     * @param requestDto
     * @return
     */
    @Log(title = "品牌管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody @Validated BrandUpdateRequestDto requestDto) {
        ZnsBrandEntity brand = brandConsoleConverter.toDo(requestDto);

        Result ajaxResult = brandService.checkBrandNameAllowed(brand);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }

        brand.setUpdateTime(ZonedDateTime.now());
        brand.setUpdateBy(SecurityUtils.getUsername());
        brandService.update(brand);
        return CommonResult.success();
    }

    /**
     * 删除品牌
     *
     * @param id
     * @return
     */
    @Log(title = "品牌管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable Long id) {
        Result ajaxResult = brandService.checkBrandStatusAllowed(id, "删除");
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        brandService.delete(id, SecurityUtils.getUsername());
        return CommonResult.success();
    }

    /**
     * 状态修改
     */
    @Log(title = "品牌管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public Result changeStatus(@RequestBody BrandStatusRequestDto requestDto) {
        ZnsBrandEntity brand = brandConsoleConverter.toDo(requestDto);

        if (brand.getStatus() == 0) {
            Result ajaxResult = brandService.checkBrandStatusAllowed(brand.getId(), "禁用");
            if (Objects.nonNull(ajaxResult)) {
                return ajaxResult;
            }
        }
        brand.setUpdateTime(ZonedDateTime.now());
        brand.setUpdateBy(SecurityUtils.getUsername());
        brandService.update(brand);
        return CommonResult.success();
    }
}
