package com.linzi.pitpat.admin.mallservice.dto.response;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class MallCategoryPageResponseDto {

    private Long id;
    //类目页ID
    private String categoryPageCode;
    //备注
    private String remark;

    private ZonedDateTime gmtCreate;

    private String creator;

    //默认语言
    private String defaultLanguageCode;
    /**
     * 国家类型码
     * @since 4.6.4
     */
    private List<String> countryCodeList;
}
