package com.linzi.pitpat.admin.outservice.dto.response;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 设备激活记录
 */
@Data
public class EquipmentActivateResponseDto {

    // 激活用户id
    private Long userId;
    // 蓝牙mac
    private String bluetoothMac;
    /**
     * 激活方式：app-软件激活，erp-erp激活，manual-用户手动激活
     * @see DeviceConstant.ActivateTypeEnum
     */
    private String activateType;
    // 激活时间
    private ZonedDateTime activateTime;

}
