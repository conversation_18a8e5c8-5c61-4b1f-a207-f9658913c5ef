package com.linzi.pitpat.admin.controller.test;

import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.manager.RankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.RunCheatManager;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.ActivityUserAwardService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayAwardStageConfigService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SeriesGameplayService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.TeamEffectiveGradeService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.query.ActivityUserScoreQuery;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategy;
import com.linzi.pitpat.data.awardservice.strategy.AwardProcessStrategyFactory;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.service.UserPushTokenService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TestManager {
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserService userService;
    private final EntryGameplayService entryGameplayService;
    private final SeriesGameplayService seriesGameplayService;
    private final ZnsUserRunRecordService userRunRecordService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final AwardActivityManager awardActivityManager;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final AppMessageService appMessageService;
    private final RedissonClient redissonClient;
    private final UserPushTokenService userPushTokenService;
    private final ActivityDisseminateService activityDisseminateService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final GameplayAwardStageConfigService gameplayAwardStageConfigService;
    private final ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    private final AwardConfigService awardConfigService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ActivityUserScoreDao activityUserScoreDao;
    private final ZnsUserAccountService userAccountService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    private final RankedActivityResultManager rankedActivityResultManager;
    private final ActivityTeamService activityTeamService;
    private final UserPropRecordService userPropRecordService;
    private final ZnsUserRunDataDetailsService runDataDetailsService;
    private final UserCouponService userCouponService;
    private final AwardConfigAmountService awardConfigAmountService;
    private final ActivityUserAwardService activityUserAwardService;
    private final TeamEffectiveGradeService teamEffectiveGradeService;
    private final RabbitTemplate rabbitTemplate;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    @Autowired
    private ActivityResultManager activityResultManager;
    @Resource
    private RunCheatManager runCheatManager;

    public void activityEnd(Long activityId) {
        deleteRedis(activityId);
        MainActivity mainActivity = mainActivityService.findById(activityId);
        activityResultManager.activityEnd(mainActivity, false);
    }

    private void deleteRedis(Long activityId) {
        AtomicInteger l = new AtomicInteger(0);
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityId);

        AwardQuery awardQuery = new AwardQuery();
        awardQuery.setActivityId(activityId);
        List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
        List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
        if (collect.size() > 0) {
            List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
            Map<Integer, List<AwardConfigDto>> listMap = awardConfigDtos.stream().collect(Collectors.groupingBy(AwardConfigDto::getAwardType));
            listMap.forEach((k, v) -> {
                AwardProcessStrategy awardProcessStrategy = AwardProcessStrategyFactory.get(k);
                List<Long> longList = v.stream().map(AwardConfigDto::getAwardConfigId).collect(Collectors.toList());
                for (Long i : longList) {
                    for (ZnsRunActivityUserEntity activityUser : allActivityUser) {
                        String key = RedisConstants.USERAWARD_SEND + activityId + "_" + activityUser.getUserId() + "_" + i;
                        boolean delete = redissonClient.getBucket(key).delete();
                        if (delete) {
                            l.getAndIncrement();
                        }
                    }
                }
            });
        }

        log.info("删除数量{}", l);
    }

    public void runEnd(Long detailId) {

        ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.findById(detailId);
        ActivityTypeDto activityNew = runActivityService.getActivityNew(details.getActivityId());
        deleteRedis(details.getActivityId());
        activityResultManager.runEnd(details, activityNew, false, false, false);
    }

    public void preventionCheatDeal(Long detailId) {
        ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.findById(detailId);
        ActivityTypeDto activityTypeDto = runActivityService.getActivityNew(userRunDataDetail.getActivityId());
        List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities = userRunDataDetailsSecondService.getSecondsList(userRunDataDetail.getId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        runCheatManager.preventionCheatDeal(userRunDataDetail, activityTypeDto, detailsSecondEntities);
        stopWatch.stop();
        log.info("preventionCheatDeal 耗时：" + stopWatch.getTotalTimeMillis());
    }

    @Transactional(rollbackFor = Exception.class)
    public void scoreRepair(Long userId) {
        //消耗积分支出记录
        ActivityUserScore exchangeScore = activityUserScoreService.findSumScore(new ActivityUserScoreQuery().setUserId(userId).setStatusList(List.of(1,2)).setIncome(-1).setNeSource(-1));
        //过期积分支出记录
        ActivityUserScore expireScore = activityUserScoreService.findSumScore(new ActivityUserScoreQuery().setUserId(userId).setStatus(1).setIncome(-1).setSource(-1));
        //兑换消耗积分收入记录
        ActivityUserScore exchangeUserScore = activityUserScoreService.findSumScore(new ActivityUserScoreQuery().setUserId(userId).setStatusList(List.of(1,2, 3)).setIncome(1));

        // 如果 消耗积分 > 过期积分 + 使用积分 1、删除过期支出数据 2、所有过期使用积分同步成积分值 3、未使用积分补齐差值
        if (exchangeScore.getScore() >= expireScore.getScore() + exchangeUserScore.getUseScore()) {
            activityUserScoreService.deleteByQuery(new ActivityUserScoreQuery().setUserId(userId).setStatus(1).setIncome(-1).setSource(-1));
            activityUserScoreService.syncUseScore(new ActivityUserScoreQuery().setUserId(userId).setStatus(-1).setIncome(1));
            int score = exchangeScore.getScore() - expireScore.getScore() - exchangeUserScore.getUseScore();
            if (score > 0) {
                activityUserScoreService.useActivityUserScore(score, null, userId, null);
            }
        } else if (exchangeScore.getScore() < expireScore.getScore() + exchangeUserScore.getUseScore() && exchangeScore.getScore() > exchangeUserScore.getUseScore()) {
            //-- 如果 兑换消耗积分 < 过期积分 + 使用积分 1、删除过期支出数据 2、所有过期使用积分同步成积分值 3、过期数据补齐差值
            activityUserScoreService.deleteByQuery(new ActivityUserScoreQuery().setUserId(userId).setStatus(1).setIncome(-1).setSource(-1));
            int score = exchangeScore.getScore() - exchangeUserScore.getUseScore();
            List<ActivityUserScore> list = activityUserScoreService.findList(new ActivityUserScoreQuery().setUserId(userId).setStatus(-1).setIncome(1));
            Integer sum = 0;
            for (ActivityUserScore activityUserScore : list) {
                if (Objects.isNull(activityUserScore.getUseScore()) || activityUserScore.getUseScore() < 0) {
                    activityUserScore.setUseScore(0);
                }
                if (sum >= score) {
                    activityUserScore.setUseScore(0);
                    addExpireRecord(activityUserScore);
                } else {
                    //直接使用过期的所有积分
                    int current = activityUserScore.getScore();
                    if (sum + activityUserScore.getScore() > score) {
                        current = score - sum;                //本次需要使用的积分
                        // 本次使用的积分加之前使用的积分= 最终使用的积分
                        activityUserScore.setUseScore(current);
                    } else {
                        activityUserScore.setUseScore(activityUserScore.getScore());
                    }
                    sum = sum + current;
                    if (Objects.isNull(activityUserScore.getExchangeTime())) {
                        activityUserScore.setExchangeTime(ZonedDateTime.now());
                    }
                    activityUserScore.setType(1);
                    activityUserScoreDao.updateById(activityUserScore);
                    log.info("ActivityUserScore id=" + activityUserScore.getId() + ",score=" + activityUserScore.getScore() + ",userScore = " + activityUserScore.getUseScore()
                            + ",status = " + activityUserScore.getStatus() + ",本次使用积分 ：" + current);
                    if (sum >= score) {
                        addExpireRecord(activityUserScore);
                    }
                }

            }

        }


    }

    @Transactional(rollbackFor = Exception.class)
    public void scoreRepairV2(Long userId) {
//        //总积分支出记录，不包括过期
//        ActivityUserScore exchangeScore = activityUserScoreService.findSumScore(new ActivityUserScoreQuery().setUserId(userId).setStatus(1).setIncome(-1).setNeSource(-1));
//        //总消耗积分收入记录
//        ActivityUserScore exchangeUserScore = activityUserScoreService.findSumScore(new ActivityUserScoreQuery().setUserId(userId).setStatusList(List.of(1,2,3,-1)).setIncome(1));
//
//        // 如果 兑换消耗积分 > 过期积分 + 使用积分 1、删除过期支出数据 2、所有过期使用积分同步成积分值 3、未使用积分补齐差值
//        if (exchangeScore.getScore() >= exchangeUserScore.getUseScore()) {
//            activityUserScoreService.syncUseScore(new ActivityUserScoreQuery().setUserId(userId).setStatus(-1).setIncome(1));
//            int score = exchangeScore.getScore() - expireScore.getScore() - exchangeUserScore.getUseScore();
//            if (score > 0) {
//                activityUserScoreService.useActivityUserScore(score, null, userId, null);
//            }
//        }


    }

    private void addExpireRecord(ActivityUserScore activityUserScore) {
        // 增加积分兑换记录
        ActivityUserScore add = new ActivityUserScore();
        add.setScore(MapUtil.getInteger(activityUserScore.getScore(), 0) -
                MapUtil.getInteger(activityUserScore.getUseScore(), 0));
        add.setStatus(1);
        add.setIncome(-1);
        add.setScoreConfigId(activityUserScore.getScoreConfigId());
        add.setSource(-1);
        add.setUserId(activityUserScore.getUserId());
        add.setExchangeOrderNo(OrderUtil.getBatchNo());
        add.setGmtCreate(Objects.isNull(activityUserScore.getExpireTime())?ZonedDateTime.now():activityUserScore.getExpireTime());
        add.setGmtModified(add.getGmtCreate());
        activityUserScoreService.insertOrUpdateActivityUserScore(add);
    }
}
