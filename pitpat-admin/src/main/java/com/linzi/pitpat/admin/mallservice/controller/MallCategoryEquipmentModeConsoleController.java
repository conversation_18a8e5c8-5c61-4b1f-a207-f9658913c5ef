package com.linzi.pitpat.admin.mallservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.mallservice.manager.MallCategoryEquipmentModelManager;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.mallservice.dto.console.EquipmentModelResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.MallCategoryEquipmentModelConfigCreateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.MallCategoryEquipmentModelConfigPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.MallCategoryEquipmentModelConfigQueryDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.MallCategoryEquipmentModelConfigDetailResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.MallCategoryEquipmentModelConfigResponseDto;
import com.linzi.pitpat.data.mallservice.service.MallCategoryEquipmentModelConfigService;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 服务类
 *
 * @since 2025年3月31日
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/categoryEquipmentModelConfigs")
public class MallCategoryEquipmentModeConsoleController {

    private final MallCategoryEquipmentModelConfigService mallCategoryEquipmentModelConfigService;
    private final MallCategoryEquipmentModelManager mallCategoryEquipmentModelManager;

    /**
     * 新增修改
     *
     * @param requestDto 表单
     * @return MallCategoryEquipmentModelConfig
     */
    @Log(title = "新增修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/createOrModify")
    public Result<Long> createOrModify(@RequestBody @Validated MallCategoryEquipmentModelConfigCreateRequestDto requestDto) {
        Long id = mallCategoryEquipmentModelManager.createOrModify(requestDto);
        return CommonResult.success(id);
    }

    /**
     * 按照ID 删除
     */
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @Validated MallCategoryEquipmentModelConfigQueryDto queryDto) {
        mallCategoryEquipmentModelManager.deleteById(queryDto.getId());
        return CommonResult.success(true);
    }

    /**
     * 按照ID 查询
     *
     * @param queryDto 查询对象
     */
    @PostMapping("/get")
    public Result<MallCategoryEquipmentModelConfigDetailResponseDto> findById(@RequestBody @Validated MallCategoryEquipmentModelConfigQueryDto queryDto) {
        MallCategoryEquipmentModelConfigDetailResponseDto resp = mallCategoryEquipmentModelManager.getDetail(queryDto.getId());
        return CommonResult.success(resp);
    }

    /**
     * 分页查询列表
     *
     * @param pageQueryDto 分查询对象
     * @return List<MallCategoryEquipmentModelConfig>
     */
    @PostMapping("/page")
    public Result<Page<MallCategoryEquipmentModelConfigResponseDto>> findPage(@RequestBody MallCategoryEquipmentModelConfigPageQueryDto pageQueryDto) {
        Page<MallCategoryEquipmentModelConfigResponseDto> resp = mallCategoryEquipmentModelManager.findPage(pageQueryDto);
        return CommonResult.success(resp);
    }

    /**
     * 获取设备型号列表
     *
     * @param id 映射关系id :编辑页传，新增页不用传
     */
    @GetMapping("/getEquipmentModelList")
    public Result<List<EquipmentModelResponseDto>> getEquipmentModelList(@RequestParam(required = false, value = "id") Long id) {
        List<EquipmentModelResponseDto> resp = mallCategoryEquipmentModelManager.getEquipmentModelList(id);
        return CommonResult.success(resp);
    }

    /**
     * 获取去重设备型号列表
     *
     */
    @GetMapping("/getUniqProductCodeList")
    public Result<List<EquipmentModelResponseDto>> getUniqProductCodeList() {
        List<EquipmentModelResponseDto> resp = mallCategoryEquipmentModelManager.getUniqProductCodeList();
        return CommonResult.success(resp);
    }
}
