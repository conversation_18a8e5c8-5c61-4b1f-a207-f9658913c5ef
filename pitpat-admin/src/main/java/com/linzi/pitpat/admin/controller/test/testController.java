package com.linzi.pitpat.admin.controller.test;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.awardservice.controller.AdminScoreController;
import com.linzi.pitpat.admin.model.Dto.request.AddRotPondReqDto;
import com.linzi.pitpat.admin.model.Dto.request.SendAmountRequestDto;
import com.linzi.pitpat.admin.model.Dto.request.UserAddBlackListRequestDto;
import com.linzi.pitpat.admin.model.Dto.request.UserJoinClubRequestDto;
import com.linzi.pitpat.admin.model.Dto.request.score.ScoreExpireDto;
import com.linzi.pitpat.admin.model.Dto.request.vip.VipSendDto;
import com.linzi.pitpat.admin.model.TablesBean;
import com.linzi.pitpat.admin.util.MysqlTableUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.listener.event.RunEndEvent;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.console.PolymerizationActivityManager;
import com.linzi.pitpat.data.activityservice.model.dto.ExchangeScoreRuleStatusDto;
import com.linzi.pitpat.data.activityservice.model.dto.InsertExchangeScoreRuleDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.AwardQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.quartz.UpdateTeamGradeTask;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.dto.consloe.ScoreRuleI18nRequestDto;
import com.linzi.pitpat.data.awardservice.manager.api.ScoreManager;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.mapper.CouponDao;
import com.linzi.pitpat.data.awardservice.mapper.ZnsUserAccountDao;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleList;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.query.ExchangeScoreRuleQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserScorePageQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRuleResp;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.clubservice.manager.ClubManager;
import com.linzi.pitpat.data.clubservice.manager.ClubMemberManager;
import com.linzi.pitpat.data.clubservice.manager.ClubPushManager;
import com.linzi.pitpat.data.clubservice.mapper.ClubActivityRoomRelationMapper;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityRoomRelationDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.model.entity.ClubRunDataDo;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityTeamQuery;
import com.linzi.pitpat.data.clubservice.model.query.ClubRunDataQuery;
import com.linzi.pitpat.data.clubservice.model.request.ClubMemberApplyReqDto;
import com.linzi.pitpat.data.clubservice.quartz.ClubTask;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubRunDataService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.filler.user.rundata.single.LastWeekWorkoutDistanceSingleDataFiller;
import com.linzi.pitpat.data.filler.user.rundata.single.LastWeekWorkoutTimeSingleDataFiller;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.manager.console.GoodsConsoleManager;
import com.linzi.pitpat.data.mallservice.mapper.ZnsAddressDao;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomeModule;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomePage;
import com.linzi.pitpat.data.mallservice.model.entity.ScoreMallGoodsRelationDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsAddressEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuQuery;
import com.linzi.pitpat.data.mallservice.model.query.HomeModuleQuery;
import com.linzi.pitpat.data.mallservice.model.query.MallHomePageQueryDto;
import com.linzi.pitpat.data.mallservice.model.query.OrderQuery;
import com.linzi.pitpat.data.mallservice.model.query.ScoreMallGoodsRelationQuery;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleService;
import com.linzi.pitpat.data.mallservice.service.MallHomePageService;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.paymentservice.mapper.PaymentTradeMapper;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentTradeDo;
import com.linzi.pitpat.data.quartz.SyncDbDataTask;
import com.linzi.pitpat.data.robotservice.query.RotNickQuery;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.mapper.AreaDao;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.VipUserSubscribeRecord;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.third.erp.ErpApiUtil;
import com.linzi.pitpat.data.userservice.dto.request.NpcCreateRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserCreateRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.dto.response.CommunityContentPicResponseDto;
import com.linzi.pitpat.data.userservice.enums.UserIdentityEnum;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.mapper.CommunityContentDao;
import com.linzi.pitpat.data.userservice.mapper.CommunityContentI18nMapper;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserAddressDao;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.model.entity.*;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserSubscribeRecordQuery;
import com.linzi.pitpat.data.userservice.service.CommunityContentI18nService;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.VipUserLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddressService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserBizService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserSubscribeRecordService;
import com.linzi.pitpat.data.util.*;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.listener.event.RunEndEvent;
import com.linzi.pitpat.data.activityservice.manager.console.PolymerizationActivityManager;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.quartz.UpdateTeamGradeTask;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.courseservice.model.entity.UserAiBaseinfo;
import com.linzi.pitpat.data.courseservice.service.UserAiBaseinfoService;
import com.linzi.pitpat.data.filler.user.rundata.single.LastWeekWorkoutDistanceSingleDataFiller;
import com.linzi.pitpat.data.filler.user.rundata.single.LastWeekWorkoutTimeSingleDataFiller;
import com.linzi.pitpat.data.messageservice.util.MailUtils;
import com.linzi.pitpat.data.quartz.script.FriendPKCleanScript;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.robotservice.biz.RobotBizService;
import com.linzi.pitpat.data.robotservice.core.RobotCacheQueueService;
import com.linzi.pitpat.data.robotservice.listener.RotFollowListener;
import com.linzi.pitpat.data.robotservice.model.domain.RobotCache;
import com.linzi.pitpat.data.robotservice.model.domain.RobotQuery;
import com.linzi.pitpat.data.robotservice.model.entity.RobotQueueConfig;
import com.linzi.pitpat.data.robotservice.model.entity.RobotQueueContentSnapshot;
import com.linzi.pitpat.data.robotservice.model.entity.RotNick;
import com.linzi.pitpat.data.robotservice.model.entity.RotPond;
import com.linzi.pitpat.data.robotservice.quartz.AutoRotPondTask;
import com.linzi.pitpat.data.robotservice.quartz.RotPondTask;
import com.linzi.pitpat.data.robotservice.quartz.script.RobotManageScript;
import com.linzi.pitpat.data.robotservice.quartz.script.RobotPatch;
import com.linzi.pitpat.data.robotservice.quartz.script.RotCleanHeadPortrait;
import com.linzi.pitpat.data.robotservice.quartz.script.RotInternationalizationScript;
import com.linzi.pitpat.data.robotservice.query.RotNickQuery;
import com.linzi.pitpat.data.robotservice.service.RobotQueueConfigService;
import com.linzi.pitpat.data.robotservice.service.RobotQueueContentSnapshotService;
import com.linzi.pitpat.data.robotservice.service.RotNickService;
import com.linzi.pitpat.data.robotservice.service.RotPondService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.mapper.AreaDao;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.request.UserCreateRequest;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.mapper.CommunityContentI18nMapper;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserAddressDao;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddressEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.quartz.UserGroupTask;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserBizService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import com.linzi.pitpat.lang.BaseDo;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/testTeam")
@Slf4j
public class testController {

    @Autowired
    private UpdateTeamGradeTask updateTeamGradeTask;

    @Autowired
    private ActivityStrategyContext activityStrategyContext;
    @Autowired
    private ZnsRunActivityService activityService;

    @Autowired
    private RotPondTask rotPondTask;
    @Autowired
    private AutoRotPondTask autoRotPondTask;
    @Resource
    private RotInternationalizationScript rotInternationalizationScript;
    @Resource
    private RobotPatch robotPatch;
    @Resource
    private RotNickService nickService;

    @Autowired
    private MailUtils mailUtils;
    @Resource
    private RotFollowListener rotFollowListener;
    @Resource
    private RotCleanHeadPortrait rotCleanHeadPortrait;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RotPondService rotPondService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ZnsUserService znsUserService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;
    @Resource
    private PolymerizationActivityPoleService polymerizationActivityPoleService;

    @Resource
    private ActivityTeamService activityTeamService;

    @Resource
    private MainActivityService mainActivityService;

    @Autowired
    private EntryGameplayService entryGameplayService;

    @Resource
    private RobotManageScript robotManageScript;

    @Resource
    private RobotBizService robotBizService;

    @Resource
    private RobotCacheQueueService queueService;

    @Resource
    private RobotQueueConfigService queueConfigService;

    @Resource
    private RobotQueueContentSnapshotService queueContentSnapshotService;

    @Autowired
    private ApplicationContext context;
    @Autowired
    private RoomIdBizService roomIdBizService;
    @Autowired
    private ZnsRunActivityUserService activityUserService;
    @Autowired
    private SubActivityService subActivityService;

    @Autowired
    private TestManager testManager;

    @Autowired
    private FriendPKCleanScript friendPKCleanScript;
    @Autowired
    private PolymerizationActivityManager polymerizationActivityManager;

    @Autowired
    private UserWearsBagService userWearsBagService;
    @Autowired
    private VipUserBizService vipUserbizService;
    @Autowired
    private ZnsUserAccountDao znsUserAccountDao;
    @Autowired
    private ZnsUserAccountDetailService znsUserAccountDetailService;
    @Autowired
    private ZnsUserDao znsUserDao;
    @Autowired
    private CommunityContentI18nMapper communityContentI18nMapper;
    @Autowired
    private UserBizService userBizService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private WearsService wearsService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ClubTask clubTask;
    @Autowired
    private CouponDao couponDao;
    @Autowired
    private CouponCurrencyService couponCurrencyService;
    @Autowired
    private ZnsGoodsService goodsService;
    @Autowired
    private ZnsGoodsSkuService znsGoodsSkuService;
    @Autowired
    private ZnsOrderService znsOrderService;
    @Autowired
    private MallOrderBizService mallOrderBizService;
    @Autowired
    private ScoreMallGoodsRelationService scoreMallGoodsRelationService;

    //全量皮肤发放，支持断点重续，皮肤待定义
    @GetMapping("/enableUserNewLevel")
    public void enableUserNewLevel() {

//        RSet<Long> rsest = redissonClient.getSet("enableUserNewLevel");
//        rsest.expire(1, TimeUnit.DAYS);
//        log.info("已发放发放皮肤数量{}", rsest.size());
//        List<Long> userIds = userService.findAllRealUserId();
//        log.info("共{}用户需要发放皮肤", userIds.size());
//        List<List<Long>> lists = ListUtils.partition(userIds, 50);
//        ExecutorService executor = Executors.newCachedThreadPool();
//        for (List<Long> list : lists) {
//            executor.execute(() -> {
//                for (Long userId : list) {
//                    if (!rsest.contains(userId)) {
//                        WearAwardDto wearAwardDto = new WearAwardDto();
//                        // TODO 待定
//                        wearAwardDto.setWearValue(null);
//                        wearAwardDto.setWearType(null);
//                        userWearsBagService.sendUserWear(userId, wearAwardDto, -1l);
//                        rsest.add(userId);
//                        log.info("用户{}发放皮肤成功,当前成功数量{}", userId, rsest.size());
//                    }
//                }
//            });
//        }
//        executor.shutdown();
//        try {
//            executor.awaitTermination(2, TimeUnit.HOURS);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//        log.info("皮肤发放完成,成功数量{}", rsest.size());
    }

    @GetMapping("/friendPKCleanScriptClean")
    public void friendPKCleanScriptClean() {
        friendPKCleanScript.clean();
    }

    @GetMapping("/batchSaveTest")
    public void batchSaveTest() {
        List<RobotQueueContentSnapshot> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            RobotQueueContentSnapshot snapshot = new RobotQueueContentSnapshot();
            snapshot.setBatchNo("7791");
            list.add(snapshot);
        }
        queueContentSnapshotService.saveBatch(list);

    }

    @GetMapping("/activityEnd")
    public void activityEnd(Long activityId) {
        testManager.activityEnd(activityId);

    }

    @GetMapping("/runEnd")
    public void runEnd(Long detailId) {
        testManager.runEnd(detailId);

    }

    /**
     * 测试作弊检测
     *
     * @return
     */
    @GetMapping("/preventionCheatDeal")
    public void preventionCheatDeal(Long detailId) {
        testManager.preventionCheatDeal(detailId);
    }


    /**
     * 测试作弊检测
     *
     * @return
     */
    @GetMapping("/preventionCheatDeal2")
    public void preventionCheatDeal2(Long detailId, Integer count) {
        if (Objects.isNull(count)) {
            count = 5;
        }
        for (Integer i = 0; i < count; i++) {
            executor.execute(() -> {
                testManager.preventionCheatDeal(detailId);
            });
        }

    }

    @GetMapping("/testLock")
    public void testLock(String key) throws InterruptedException {
        RLock lock = redissonClient.getLock(key);
        Boolean tryLock = lock.tryLock(1, 3600, TimeUnit.SECONDS);
        log.info("第一次{}", tryLock);

        new Thread(() -> {
            RLock lock1 = redissonClient.getLock(key);
            Boolean tryLock1 = null;
            try {
                tryLock1 = lock1.tryLock(1, 3600, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("第2次{}", tryLock1);
        }).start();

        Thread.sleep(100);
        new Thread(() -> {
            RLock lock2 = redissonClient.getFairLock(key);

            Boolean tryLock2 = null;
            try {
                tryLock2 = lock2.tryLock(1, 3600, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("第3次{}", tryLock2);
        }).start();

    }

    @GetMapping("/resetQueue")
    public void resetQueue(@RequestParam(required = false) String country, @RequestParam(required = false) String mode) {
        queueService.resetSize(country, mode);

    }


    @GetMapping("/getRoomId")
    public Result<List<Integer>> getRoomId(Long activityId, @RequestParam(required = false) Long userId) {

        List<Integer> list = new ArrayList<>();
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.nonNull(userId)) {
            ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(activityId, userId);
            if (activityUser != null) {
                if (activityUser.getTargetRunTime() > 0) {
                    list.add(activityUser.getTargetRunTime());
                } else if (activityUser.getTargetRunMileage() > 0) {
                    list.add(activityUser.getTargetRunMileage());
                }
            } else {
                throw new BaseException("该用户没报名");
            }
        } else {
            //目标
            list = subActivityService.getAllSingleActByMain(activityId).stream().map(SubActivity::getTarget)
                    .sorted().toList();
        }
        List<Integer> res = new ArrayList<>();
        for (Integer goal : list) {
            res.add(roomIdBizService.getRoomId(mainActivity, Arrays.asList(goal)));
        }

        return CommonResult.success(res);
    }

    @GetMapping("/printQueueRobots")
    public String printQueueRobots(@RequestParam(required = false) String country, @RequestParam(required = false) String mode) {

        List<RobotQueueConfig> configList = queueConfigService.getAll();
        if (StringUtils.hasText(country)) {
            configList = configList.stream().filter(k -> k.getCountry().equals(country)).collect(Collectors.toList());
        }
        if (StringUtils.hasText(mode)) {
            configList = configList.stream().filter(k -> k.getMode().equals(mode)).collect(Collectors.toList());
        }
        String batchNo = OrderUtil.getBatchNo();
        for (RobotQueueConfig config : configList) {
            List<RobotCache> robotCaches = queueService.readAll(config.getCountry(), config.getMode());
            List<RobotQueueContentSnapshot> collect = robotCaches.stream().map(k -> {
                RobotQueueContentSnapshot snapshot = new RobotQueueContentSnapshot();
                snapshot.setCountry(k.getCountry());
                snapshot.setRunMode(k.getMode());
                snapshot.setUserId(k.getUserId());
                snapshot.setEnterTime(k.getEnterTime());
                snapshot.setBatchNo(batchNo);
                return snapshot;
            }).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(collect)) {
                queueContentSnapshotService.saveBatch(collect);
            }
        }

        return batchNo;


    }


    @GetMapping("/deleteQueue")
    public void deleteQueue(@RequestParam(required = false) String country, @RequestParam(required = false) String mode) {
        queueService.deleteQueue(country, mode);
    }

    @GetMapping("/beanReflect")
    public void beanReflect(String bean, String method) throws Exception {
        Object bean1 = context.getBean(bean);
        Method method1 = bean1.getClass().getMethod(method);
        method1.invoke(bean1);
    }

    @GetMapping("/migratingRobotData")
    public void migratingRobotData() {
        robotManageScript.migratingRobotData();
    }

    @GetMapping("/acquireRot")
    public ZnsUserEntity acquireRot(String country, String mode, Long activityId) {

        RobotQuery query = RobotQuery.builder().countrys(JsonUtil.readList(country, String.class))
                .mode(mode).activityId(activityId).startTime(ZonedDateTime.now()).build();
        ZnsUserEntity rot = robotBizService.acquireRot(query);
        System.out.println(rot);
        return rot;
    }

    @GetMapping("/showQueue")
    public void showQueue(String country, String mode) {
        List<RobotCache> robotCaches = queueService.readAll(country, mode);
        System.out.println(robotCaches);
        System.out.println(robotCaches.size());
    }

    @GetMapping("/teamRank")
    public void teamRank(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());

        List<ActivityTeam> teams = activityTeamService.getTeamsByActivityId(activityId);
        if (!CollectionUtils.isEmpty(teams)) {
            for (int i = 0; i < teams.size(); i++) {
                //重置排名
                teams.get(i).setRank(1);
                //确认排名
                for (int j = 0; j < teams.size(); j++) {
                    if (teams.get(i).lowThan(teams.get(j), entryGameplay.getRankingBy(), entryGameplay.getTargetType())) {
                        teams.get(i).setRank(teams.get(i).getRank() + 1);
                    }
                }
            }
        }
        teams.forEach(System.out::println);
    }


    @GetMapping("/cleanHead")
    public void cleanHead() {
        rotCleanHeadPortrait.clean();
    }


    @GetMapping("/poly")
    public void poly(Long id, Long manual) {
        PolymerizationActivityPole pole = polymerizationActivityPoleService.findById(id);
        polymerizationActivityManager.executePolymerization(pole, manual == 0);


    }

    @GetMapping("/push")
    public void push(Long id, Long detailId) {
        RunDataRequest runData = new RunDataRequest();
        runData.setId_no(id);
        runData.setRunType(1);
        runData.setDataSource(1);
        runData.setRunDataDetailsId(detailId);

        RunEndEvent runEndEvent = new RunEndEvent(new Object(), runData);
        rotFollowListener.triggerHardwarePushTest(runEndEvent);


    }


    @GetMapping("/testGet")
    public void testGet(String ip) {
        RotNickQuery french = RotNickQuery.builder().language("French").build();
        List<RotNick> nicks = nickService.findList(french);
        for (RotNick nick : nicks) {
            String firstName = nick.getFirstName();
            System.out.println(firstName);

            //http://**************:8001
            String getUrl = null;
            String urlnew = null;
            try {
                String encodedParameter = URLEncoder.encode(firstName, "utf-8");
                getUrl = ip + "/add-robot?french=" + StringUtils.trimAllWhitespace(encodedParameter) + "&userId=99996&nid=" + nick.getId();
                urlnew = new String(getUrl.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                //   getUrl = "*********:7770/add-robot?french="+ StringUtil.trimAllWhitespace(name);
//            getUrl = URLEncoder.encode(getUrl,"UTF-8");
            } catch (Exception e) {
                e.printStackTrace();
            }

            //String result = HttpUtil.doGet(urlnew, 10000);
            String response = RestTemplateUtil.get(urlnew);

            System.out.println(response);
        }
    }


    @GetMapping("/cleanRotPicAndSyncAgain")
    public void cleanRotPicAndSyncAgain() {
        robotPatch.cleanRotPicAndSyncAgain();
    }

    @GetMapping("/batchAddRots")
    public void batchAddRotsProxy(String param) {
        rotInternationalizationScript.batchAddRotsProxy(param);
    }

    @GetMapping("/autoAddRotPondProxy")
    public void autoAddRotPondProxy(String rotPondAddMetaInfoJson, String rotPondAddsJson) {
        autoRotPondTask.autoAddRotPondProxy(rotPondAddMetaInfoJson, rotPondAddsJson);
        robotManageScript.migratingRobotData();
    }

    @GetMapping("/updatePicRandom")
    public void updatePicRandom() {
        rotInternationalizationScript.updatePicRandom();
    }

    @Resource
    private UserGroupTask groupTask;


    @GetMapping("/updateDataByLabelId")
    public void test39(Long id) {
        groupTask.updateDataByLabelId(id);

    }

    @GetMapping("/updateExpiredRotPod")
    public void testSyncExpiredRotPod() {
        String key = RedisConstants.ROT_LIMIT_JOIN_ACTIVITY;
        List<String> ignoreRots = null;
        if (redisTemplate.hasKey(key)) {
            ListOperations listOperations = redisTemplate.opsForList();
            ignoreRots = listOperations.range(key, 0, -1);
        }
        if (!CollectionUtils.isEmpty(ignoreRots)) {
            ignoreRots.forEach(userId -> {
                RotPond rotPond = new RotPond();
                rotPond.setExpireTime(DateUtil.endOfDate(ZonedDateTime.now()));
                rotPondService.updateUserExpireTime(Long.parseLong(userId), DateUtil.endOfDate(ZonedDateTime.now()));
            });
        }
    }

    @GetMapping("/initRot")
    public void initRot(String cur) {
        switch (cur) {
            case "initRotPondDB":
                rotPondTask.initRotPondDB();
            case "updateRotPondNick":
                rotPondTask.updateRotPondNick();
            case "initRotPicDB":
                rotPondTask.initRotPicDB();
            case "updateRotPondPic":
                rotPondTask.updateRotPondPic();
            case "initUpdateRotPicDB":
                rotPondTask.initUpdateRotPicDB();

            case "updateRotPondPicNew":
                rotPondTask.updateRotPondPicNew();
            case "syncPondAndIm":
                rotPondTask.syncPondAndIm();
            case "initRotNickDB":
                //rotPondTask.initRotNickDB();
            case "updateRotPondPic920":
                rotPondTask.updateRotPondPic920();


            default:
                break;
        }


    }


    @GetMapping("/testTeam")
    public void jobTest1() {
        updateTeamGradeTask.updateTeamGrade();

    }

    @GetMapping("/testTeamend")
    public void jobTest2() {
        ZnsRunActivityEntity entity = activityService.findById(304841L);
        activityStrategyContext.handleActivityFinished(entity);
    }

    @GetMapping("testMail")
    public void sendEmail(@RequestParam String email) throws Exception {
        mailUtils.sendMail(email, "content", "title", new ZnsUserEmailSendingRecordEntity());
    }


    @Resource
    private ZnsRunActivityService runActivityService;


    /**
     * 手动给机器人入池
     * http://localhost:7771/admin/testTeam/addRotPond
     * {
     * "rotPondAddMetaInfo": {
     * "country": "Thailand",
     * "hasPic": true,
     * "km": 1,
     * "language": "th_TH"
     * },
     * "rotPondAdds": [
     * {
     * "femaleNum": 900,
     * "maleNum": 2100,
     * "mode": "S+",
     * "totalNum": 3000
     * }
     * ]
     * }
     */
    @PostMapping("/addRotPond")
    public Result addRotPond(@RequestBody AddRotPondReqDto addRotPondReqDto) {
        autoRotPondTask.autoAddRotPond(addRotPondReqDto.getRotPondAddMetaInfo(), addRotPondReqDto.getRotPondAdds());
        return CommonResult.success();
    }

    @GetMapping("/fixMemberType")
    public Result fixMemberType(@RequestParam(required = false) Long userId) {
        UserQuery query = UserQuery.builder().build();
        List<ZnsUserEntity> list = znsUserService.findList(query);
        // 分段处理
        int BATCH_SIZE = 500;
        int totalSize = list.size();
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<ZnsUserEntity> batchList = list.subList(i, endIndex);

            // 更新 memberType 字段
            for (ZnsUserEntity znsUserEntity : batchList) {
                znsUserEntity.setMemberType(1);
            }
            // 异步执行批量更新
            executor.execute(() -> {
                try {
                    znsUserService.updateBatchById(batchList);
                } catch (Exception e) {
                    log.error("批量更新用户会员类型失败", e);
                }
            });
        }
        return CommonResult.success();
    }

    @PostMapping("/sendAmount")
    @Transactional
    public Result sendAmount(@RequestBody SendAmountRequestDto sendAmountRequestDto) {
        if (Objects.isNull(sendAmountRequestDto.getAmount()) || CollectionUtils.isEmpty(sendAmountRequestDto.getEmails())) {
            return CommonResult.fail("参数缺失");
        }
        List<ZnsUserEntity> znsUserEntities = znsUserDao.selectList(new QueryWrapper<ZnsUserEntity>().lambda().eq(ZnsUserEntity::getIsDelete, 0).in(ZnsUserEntity::getEmailAddress, sendAmountRequestDto.getEmails()));
        List<Long> userIds = znsUserEntities.stream().map(ZnsUserEntity::getId).toList();
        for (Long userId : userIds) {
            znsUserAccountDao.increaseAmount(sendAmountRequestDto.getAmount(), userId, true);
            znsUserAccountDetailService.addAccountDetail(userId, 1, AccountDetailTypeEnum.NEW_ACTIVITY_100, AccountDetailSubtypeEnum.NEW_ACTIVITY_AWARD, sendAmountRequestDto.getAmount(), sendAmountRequestDto.getActivityId(), sendAmountRequestDto.getRemark());
            log.info("发放用户ID：{},金额：{}", userId, sendAmountRequestDto.getAmount());
        }
        return CommonResult.success();
    }

    //添加永久会员
    @PostMapping("/addPermanentMember")
    public Result addPermanentMember(@RequestParam(required = true) Long userId, @RequestParam(required = false) Integer day) {
        vipUserbizService.addSpecifiedDayMember(userId, day);
        return CommonResult.success();
    }

    @Resource
    private ClubPushManager clubPushManager;

    @Resource
    private AppMessageService appMessageService;

    /**
     * 发送俱乐部 push
     *
     * @param clubId
     * @return
     */
    @PostMapping("/sendPush")
    public Result sendPush(@RequestParam(required = true) Long clubId) {
        ImMessageBo imMessageBo = new ImMessageBo();
        imMessageBo.setJumpType("0");
        imMessageBo.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/1740642187937.png");
        imMessageBo.setJumpValue("https://tkjgw.yijiesudai.com/#/store/order/RZseNy194K9vZH24608?isCusNavBar=true");
        imMessageBo.setRouteValue("https://tkjgw.yijiesudai.com/#/store/order/RZseNy194K9vZH24608?isCusNavBar=true");
        imMessageBo.setMsg("msg");
        appMessageService.sendIm("administrator", List.of(clubId), JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.TRUE);
        return CommonResult.success();
    }


    //添加永久会员online/local 调用online batch版本
    @PostMapping("/addPermanentMemberBatch")
    public Result addPermanentMemberBatch(@RequestBody VipSendDto vipSendDto) {
//        String host = "https://admin.pitpatfitness.com/admin";
//        String url = "/testTeam/addPermanentMember";
        List<Long> userIds = vipSendDto.getUserIds();
        Integer day = vipSendDto.getDay();
        Map<String, Object> map = new HashMap<>();
        userIds.forEach(i -> {
            vipUserbizService.addSpecifiedDayMember(i, day);
            // local 调用
//            String result = RestTemplateUtil.post(host+url+"?userId="+i+"&day="+day, map);
//            log.info(result);
        });
        return CommonResult.success();
    }

    @PostMapping("/cacheTest")
    public void setCacheTest() {
        ZnsUserEntity znsUserEntity = new ZnsUserEntity();
        znsUserEntity.setId(1L);
        redissonClient.getBucket("A").set(znsUserEntity);
    }

    @Resource
    private LastWeekWorkoutDistanceSingleDataFiller distanceSingleDataFiller;
    @Resource
    private LastWeekWorkoutTimeSingleDataFiller timeSingleDataFiller;
    @Resource
    private ZnsUserService userService;

    @GetMapping("/queryDataFiller")
    public Result queryDataFiller(@RequestParam("distance") int distance,
                                  @RequestParam("userId") Long userId,
                                  @RequestParam("now") ZonedDateTime now) {
        log.info("now:{},接收到的参数时间:{}", ZonedDateTime.now(), now);
        ZnsUserEntity byId = userService.findById(userId);
        if (distance == 1) {
            return CommonResult.success(distanceSingleDataFiller.queryData(byId, now));
        } else {
            return CommonResult.success(timeSingleDataFiller.queryData(byId, now));
        }
    }

    @GetMapping("/sendFreeToNewUser")
    public Result sendFreeToNewUser(Long startUserId) {
        Page page = new Page(0, 1000);
        while (true) {
            page.setCurrent(page.getCurrent() + 1);
            List<Long> noWearsUserIds = userService.findNoWearsUserIds(page, startUserId);
            if (CollectionUtils.isEmpty(noWearsUserIds)) {
                return CommonResult.success();
            }
            executor.execute(() -> {
                noWearsUserIds.forEach(userId -> {
                    //注册用户发免费服装
                    sendUserWears(userId);
                });
            });

        }
    }

    @GetMapping("/sendFreeToNewUserOne")
    public Result sendFreeToNewUserOne(Long userId) {
        sendUserWears(userId);
        return CommonResult.success();
    }

    /**
     * 创建用户
     *
     * @param req
     * @return
     */
    @PostMapping("/createNewUser")
    public Result<List<String>> createNewUser(@RequestBody UserCreateRequest req) {
        return CommonResult.success(userBizService.createNewUser(req));
    }


    @Resource
    private ZnsAddressDao znsAddressDao;
    @Resource
    private AreaDao areaDao;

    /**
     * 同步地址库州code
     *
     * @return
     */
    @PostMapping("/fileStateCode")
    public Result fileStateCode() {
        List<ZnsAddressEntity> addressEntityList = znsAddressDao.selectList(new QueryWrapper<ZnsAddressEntity>().lambda().eq(ZnsAddressEntity::getLevel, 1).eq(ZnsAddressEntity::getIsDelete, 0));
        for (ZnsAddressEntity znsAddressEntity : addressEntityList) {
            AreaEntity us = areaDao.selectOne(new QueryWrapper<AreaEntity>().lambda().eq(AreaEntity::getIsDelete, 0).eq(AreaEntity::getCountryCode, "US").eq(AreaEntity::getAreaName, znsAddressEntity.getName()), false);
            if (Objects.nonNull(us)) {
                znsAddressEntity.setStateCode(us.getAreaCode());
                znsAddressDao.updateById(znsAddressEntity);
            }
        }
        return CommonResult.success();
    }

    @Resource
    private ZnsUserAddressDao znsUserAddressDao;

    /**
     * 同步用户地址表信息
     *
     * @return
     */
    @PostMapping("/syncUserAddress")
    public Result syncUserAddress() {
        List<ZnsUserAddressEntity> addressEntityList = znsUserAddressDao.selectList(new QueryWrapper<ZnsUserAddressEntity>().lambda().eq(ZnsUserAddressEntity::getIsDelete, 0));
        for (ZnsUserAddressEntity znsUserAddressEntity : addressEntityList) {
            AreaEntity us = areaDao.selectOne(new QueryWrapper<AreaEntity>().lambda().eq(AreaEntity::getIsDelete, 0).eq(AreaEntity::getCountryCode, "US").eq(AreaEntity::getAreaName, znsUserAddressEntity.getProvince()), false);
            if (Objects.nonNull(us)) {
                znsUserAddressEntity.setStateCode(us.getAreaCode());
            }
            ZnsUserEntity znsUserEntity = znsUserDao.selectUserById(znsUserAddressEntity.getUserId());
            if (Objects.nonNull(znsUserEntity)) {
                znsUserAddressEntity.setFirstName(znsUserEntity.getFirstName());
                znsUserAddressEntity.setLastName(znsUserEntity.getLastName());
            }
            znsUserAddressDao.updateById(znsUserAddressEntity);
        }
        return CommonResult.success();
    }

    /**
     * 更新用户积分过期时间
     *
     * @return
     */
    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;
    @Autowired
    private ActivityUserScoreService activityUserScoreService;

    @PostMapping("/fixUserScore")
    public Result fixUserScore() {
        List<Long> fixUserId = activityUserScoreDao.getFixUserId();
        // 分段处理
        int BATCH_SIZE = 100;
        int totalSize = fixUserId.size();
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<Long> batchList = fixUserId.subList(i, endIndex);
            // 异步执行批量更新
            executor.execute(() -> {
                try {
                    List<ActivityUserScore> fixDo = activityUserScoreDao.getFixDo(batchList);
                    log.info("userID:{}", batchList);
                    for (ActivityUserScore activityUserScore : fixDo) {
                        activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(activityUserScore.getGmtCreate(), 13, TimeZone.getTimeZone("UTC-8")));
                        activityUserScore.setGmtModified(ZonedDateTime.now());
                    }
                    activityUserScoreService.updateBatchById(fixDo);
                } catch (Exception e) {
                    log.error("批量更新用户会员类型失败", e);
                }
            });
        }
        return CommonResult.success();
    }

    public List<Map<String, Object>> fetchBatch(int offset, int limit) {
        return jdbcTemplate.queryForList(
                "SELECT a.user_id FROM zns_activity_user_score a " +
                        "LEFT JOIN zns_user b ON a.user_id = b.id " +
                        "WHERE b.is_test = 0 AND b.is_robot = 0 AND b.is_delete = 0 AND a.is_delete = 0 " +
                        "AND a.status IN (1,3) AND a.expire_time is not null AND a.income =1 AND gmt_create > '2024-03-24 00:00:00' " +
                        "LIMIT ? OFFSET ?",
                limit, offset
        );
    }

    private void sendUserWears(Long userId) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.FREE_WEAR_TO_NEW_USER.getCode());
        if (Objects.nonNull(sysConfig) && StringUtils.hasText(sysConfig.getConfigValue())) {
            String configValue = sysConfig.getConfigValue();
            List<Long> wearIds = NumberUtils.stringToLong(configValue.split(","));
            log.info("给新用户发放免费服装,userId:{},服装列表：{}", userId, wearIds);
            for (Long wearId : wearIds) {
                Wears wears = wearsService.selectWearsById(wearId);
                if (Objects.nonNull(wears)) {
                    //查询是否发送过
                    UserWearsBag wearsBag = userWearsBagService.findByQuery(UserWearBagQuery.builder().userId(userId).wearType(wears.getWearType()).wearValue(wears.getWearId()).build());
                    if (Objects.nonNull(wearsBag)) {
                        continue;
                    }
                    UserWearsBag userWearsBag = new UserWearsBag();
                    userWearsBag.setWearType(wears.getWearType());
                    userWearsBag.setWearName(wears.getWearName());
                    userWearsBag.setWearImageUrl(wears.getWearImageUrl());
                    userWearsBag.setWearValue(wears.getWearId());
                    userWearsBag.setUserId(userId);
                    userWearsBagService.insert(userWearsBag);
                }
            }
        }
    }

    @Resource
    private ClubMemberManager clubMemberManager;


    @PostMapping("/joinClub")
    public Result joinClub(@RequestBody UserJoinClubRequestDto requestDto) {
        if (Objects.isNull(requestDto.getClubId()) || CollectionUtils.isEmpty(requestDto.getUserIds())) {
            return CommonResult.fail("参数缺失");
        }
        requestDto.getUserIds().forEach(e -> {
            ClubMemberApplyReqDto req = new ClubMemberApplyReqDto();
            req.setClubId(requestDto.getClubId());
            req.setUserId(e);
            clubMemberManager.submitJoinApply(req);
        });
        return CommonResult.success();
    }

    @Resource
    private TencentImUtil tencentImUtil;

    @PostMapping("/addBlackList")
    public Result addBlackList(@RequestBody UserAddBlackListRequestDto requestDto) {
        if (CollectionUtils.isEmpty(requestDto.getUserIds())) {
            return CommonResult.fail("参数缺失");
        }
        requestDto.getUserIds().forEach(e -> {
            tencentImUtil.setNoSpeaking(e.toString(), 4294967295l, 4294967295l);
        });
        return CommonResult.success();
    }


    @Resource
    private ClubService clubService;
    @Resource
    private ClubRunDataService clubRunDataService;
    @Resource
    private ClubMemberService clubMemberService;
    @Resource
    private ZnsUserRunDataService znsUserRunDataService;
    @Resource
    private ClubActivityTeamService clubActivityTeamService;
    @Resource
    private ActivityResultManager activityResultManager;
    @Resource
    private ActivityAwardConfigService activityAwardConfigService;
    @Resource
    private AwardConfigService awardConfigService;
    @Resource
    private ZnsRunActivityUserService znsRunActivityUserService;
    @Resource
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    @Autowired
    private ClubActivityRoomRelationMapper clubActivityRoomRelationMapper;

    /**
     * 同步俱乐部运动里程
     *
     * @return
     */
    @PostMapping("/syncClubRunData")
    @Transactional
    public Result syncClubRunData() {
        List<ClubActivityTeam> clubActivityTeamList = clubActivityTeamService.findListByQuery(ClubActivityTeamQuery.builder().build());
        if (CollectionUtils.isEmpty(clubActivityTeamList)) return CommonResult.success();
        for (ClubActivityTeam clubActivityTeam : clubActivityTeamList) {
            log.info("处理俱乐部团赛运动 clubActivityTeam :{}", clubActivityTeam);
            List<ZnsRunActivityUserEntity> activityUserEntities = activityUserService.findList(RunActivityUserQuery.builder().activityId(clubActivityTeam.getMainActivityId()).teamId(clubActivityTeam.getActivityTeamId()).isDelete(0).build());
            if (CollectionUtils.isEmpty(activityUserEntities)) continue;
            List<Long> userIds = activityUserEntities.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());

            List<ZnsUserRunDataDetailsEntity> detailsEntityList = znsUserRunDataDetailsService.findListByPageQuery(new UserRunDataDetailsQuery().setUserIds(userIds).setActivityId(clubActivityTeam.getMainActivityId()).setIsCheat(0));
            if (CollectionUtils.isEmpty(detailsEntityList)) continue;

            BigDecimal runMileage = detailsEntityList.stream().map(ZnsUserRunDataDetailsEntity::getRunMileage).reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("需要添加的运动里程 runMileage：{}", runMileage);
            clubRunDataService.addRunMileageAndRunCount(clubActivityTeam.getClubId(), runMileage, false);
        }
        return CommonResult.success();
    }

    /**
     * 同步俱乐部运动场次
     *
     * @return
     */
    @PostMapping("/syncClubRunCount")
    @Transactional
    public Result syncClubRunCount() {
        List<ClubActivityRoomRelationDo> clubActivityRoomRelationDos = clubActivityRoomRelationMapper.selectList(new QueryWrapper<ClubActivityRoomRelationDo>().lambda().eq(BaseDo::getIsDelete, 0).gt(ClubActivityRoomRelationDo::getSubActivityId, 1));
        Map<Long, List<ClubActivityRoomRelationDo>> clubMap = clubActivityRoomRelationDos.stream().collect(Collectors.groupingBy(ClubActivityRoomRelationDo::getClubId));
        executor.execute(() -> {
            clubMap.forEach((clubId, v) -> {
                List<Long> activityList = v.stream().map(ClubActivityRoomRelationDo::getSubActivityId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(activityList)) {
                    Long count = activityUserService.findCount(RunActivityUserQuery.builder().activityIds(activityList).isDelete(0).isComplete(1).build());
                    ClubRunDataDo clubRunDataDo = clubRunDataService.findByQuery(ClubRunDataQuery.builder().clubId(clubId).build());
                    if (Objects.nonNull(clubRunDataDo)) {
                        clubRunDataDo.setRunCount(clubRunDataDo.getRunCount() + count.intValue());
                        clubRunDataService.update(clubRunDataDo);
                    } else {
                        ClubRunDataDo clubRunDataDo1 = new ClubRunDataDo();
                        clubRunDataDo1.setClubId(clubId);
                        clubRunDataDo1.setRunCount(count.intValue());
                        clubRunDataService.create(clubRunDataDo1);
                    }
                }
            });
        });
        return CommonResult.success();
    }

    /**
     * 同步俱乐部团赛奖励
     *
     * @return
     */
    @PostMapping("/syncClubRankAward")
    @Transactional
    public Result syncClubRankAward() {
        List<ClubActivityTeam> clubActivityTeamList = clubActivityTeamService.findListByQuery(ClubActivityTeamQuery.builder().build());

        for (ClubActivityTeam clubActivityTeam : clubActivityTeamList) {
            ActivityTeam activityTeam = activityTeamService.findById(clubActivityTeam.getActivityTeamId());
            List<Long> memberList = clubMemberService.findByClubId(clubActivityTeam.getClubId());
            List<ZnsRunActivityUserEntity> list = znsRunActivityUserService.findList(RunActivityUserQuery.builder().activityId(clubActivityTeam.getMainActivityId()).teamId(clubActivityTeam.getActivityTeamId()).build());
            if (CollectionUtils.isEmpty(memberList) || Objects.isNull(activityTeam) || CollectionUtils.isEmpty(list)) {
                continue;
            }
            AwardQuery awardQuery = new AwardQuery();
            awardQuery.setActivityId(clubActivityTeam.getMainActivityId());
            List<ActivityAwardConfig> configs = activityAwardConfigService.selectConfigListsByActivityIdAndSubId(awardQuery);
            if (CollectionUtils.isEmpty(configs)) continue;
            List<Long> collect = configs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
            List<AwardConfigDto> awardConfigDtos = awardConfigService.selectAwardConfigDtoList(collect);
            awardConfigDtos = awardConfigDtos.stream().filter(a -> AwardSentTypeEnum.RANKING_BASED_REWARDS.getType().equals(a.getSendType())
                    || AwardSentTypeEnum.RANKING_HEAD_REWARD.getType().equals(a.getSendType())).collect(Collectors.toList());
            //计算奖金
            BigDecimal awardAmount = BigDecimal.ZERO;
            BigDecimal baseReward = activityResultManager.getAmountAward(getActivityUserTarget(list.get(0)), awardConfigDtos, AwardSentTypeEnum.RANKING_BASED_REWARDS.getType(), I18nConstant.CurrencyCodeEnum.USD.getCurrency(), activityTeam.getRank());
            BigDecimal headReward = activityResultManager.getAmountAward(getActivityUserTarget(list.get(0)), awardConfigDtos, AwardSentTypeEnum.RANKING_HEAD_REWARD.getType(), I18nConstant.CurrencyCodeEnum.USD.getCurrency(), activityTeam.getRank());

            if (Objects.nonNull(baseReward) && baseReward.compareTo(BigDecimal.ZERO) > 0) {
                awardAmount = awardAmount.add(baseReward);
            }
            if (Objects.nonNull(headReward) && headReward.compareTo(BigDecimal.ZERO) > 0) {
                awardAmount = BigDecimalUtil.multiply(headReward, new BigDecimal(memberList.size())).add(awardAmount);
            }
            log.info("俱乐部ID:{},活动ID:{},总奖金：{}", clubActivityTeam.getClubId(), clubActivityTeam.getMainActivityId(), awardAmount);
            ClubRunDataDo clubRunDataDo = clubRunDataService.findByQuery(ClubRunDataQuery.builder().clubId(clubActivityTeam.getClubId()).build());
            if (Objects.isNull(clubRunDataDo)) {
                ClubRunDataDo newClubRunDataDo = new ClubRunDataDo();
                newClubRunDataDo.setClubId(clubActivityTeam.getClubId());
                newClubRunDataDo.setTotalBonus(awardAmount);
                clubRunDataService.create(newClubRunDataDo);
            } else {
                clubRunDataDo.setTotalBonus(clubRunDataDo.getTotalBonus().add(awardAmount));
                clubRunDataService.update(clubRunDataDo);
            }
        }
        return CommonResult.success();
    }

    private Integer getActivityUserTarget(ZnsRunActivityUserEntity activityUser) {
        if (Objects.isNull(activityUser)) {
            return null;
        }
        if (Objects.nonNull(activityUser.getTargetRunTime()) && activityUser.getTargetRunTime() > 0) {
            return activityUser.getTargetRunTime();
        }
        if (Objects.nonNull(activityUser.getTargetRunMileage()) && activityUser.getTargetRunMileage() > 0) {
            return activityUser.getTargetRunMileage();
        }
        return null;
    }


    @Autowired
    private ScoreManager scoreManager;

    /**
     * 更新用户积分过期指定时间过期
     *
     * @return
     */
    @PostMapping("/scoreExpire")
    public Result ScoreExpire(@RequestBody @Validated ScoreExpireDto scoreExpireDto) {
        ZonedDateTime startTime = scoreExpireDto.getStartTime();
        ZonedDateTime endTime = DateUtil.addDays(startTime, 1);
        UserScorePageQuery userScorePageQuery = new UserScorePageQuery().setGeExpireTime(startTime).setLtExpireTime(endTime)
                .setStatusList(Arrays.asList(1, 3)).setIsRobot(0);
        userScorePageQuery.setPageNum(1);
        userScorePageQuery.setPageSize(1000);
        userScorePageQuery.setSearchCount(false);
        scoreManager.scoreExpire(userScorePageQuery);
        return CommonResult.success();
    }

    @PostMapping("/generate/club")
    public Result<Void> generateNewUserClub() {
        clubTask.generateNewUserClub();
        return CommonResult.success();
    }


    /**
     * 测试RepeatSubmit
     *
     * @param scoreExpireDto
     * @return
     */
    @RepeatSubmit
    @PostMapping("/testRepeatSubmit")
    public Result testRepeatSubmit(@RequestBody ScoreExpireDto scoreExpireDto) {
        return CommonResult.success();
    }

    @GetMapping("/checkTableDifferent")
    public Result<List<String>> checkTableDifferent() {
        List<TablesBean> tablesBeans = MysqlTableUtils.showTableNameList();
        List<String> whitelist = Arrays.asList("zns_whitelist_table1", "zns_whitelist_table2"); // 白名单表名列表
        List<TablesBean> znsList = tablesBeans.stream()
                .filter(s -> s.getTableName().contains("zns"))
                .filter(s -> !whitelist.contains(s.getTableName())).toList();
        List<Future<Void>> futures = new ArrayList<>();
        List<String> result = new ArrayList<>();
        ExecutorService executor = Executors.newFixedThreadPool(100);
        for (TablesBean tablesBean : znsList) {
            futures.add(executor.submit(() -> {
                List<String> differenList = MysqlTableUtils.checkTableConstruct(tablesBean.getTableName());
                result.addAll(differenList);
                return null;
            }));
        }
        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        executor.shutdown();
        return CommonResult.success(result);
    }

    @PostMapping("/coupon/currency")
    public Result<Void> couponCurrency() {
        List<Coupon> couponList = couponDao.selectList(new QueryWrapper<Coupon>().lambda().eq(Coupon::getIsDelete, 0).eq(Coupon::getCouponType, 100).ge(Coupon::getAmount, 0.00));
        couponList.forEach(coupon -> {
            executor.execute(() -> {
                CouponCurrencyEntity couponCurrencyEntity = new CouponCurrencyEntity();
                couponCurrencyEntity.setCouponId(coupon.getId());
                couponCurrencyEntity.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
                couponCurrencyEntity.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
                couponCurrencyEntity.setCurrencyName(I18nConstant.CurrencyCodeEnum.USD.getName());
                couponCurrencyEntity.setAmount(coupon.getAmount());
                couponCurrencyEntity.setMinTotalAmount(coupon.getMinTotalAmount());
                couponCurrencyService.create(couponCurrencyEntity);
            });
        });
        return CommonResult.success();
    }

    @Resource
    private GoodsConsoleManager goodsConsoleManager;
    @Resource
    private AdminScoreController adminScoreController;
    @Resource
    private ExchangeScoreRuleService exchangeScoreRuleService;
    /**
     * 积分商城实物商品处理
     * @return
     */
    @GetMapping("/scoreMall/goods")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> scoreMallGoods(@RequestParam Long ruleId,@RequestParam String SkuNo) {
        ExchangeScoreRuleStatusDto scoreRuleStatusDto=  new ExchangeScoreRuleStatusDto();
        scoreRuleStatusDto.setId(ruleId);
        Result<InsertExchangeScoreRuleDto> exchangeScoreRuleById = adminScoreController.getExchangeScoreRuleById(scoreRuleStatusDto);
        InsertExchangeScoreRuleDto scoreRuleByIdData = exchangeScoreRuleById.getData();
        if (Objects.isNull(scoreRuleByIdData) || Objects.isNull(scoreRuleByIdData.getExchangeScoreRule())) return CommonResult.fail("未找到兑换规则");
        ExchangeScoreRuleResp exchangeScoreRule = scoreRuleByIdData.getExchangeScoreRule();
        if (scoreRuleByIdData.getExchangeScoreRule().getExchangeReserve() < 0 ){
            exchangeScoreRule.setExchangeReserve(9999);
            scoreRuleByIdData.setExchangeScoreRule(exchangeScoreRule);
        }
        ExchangeScoreRule byId = exchangeScoreRuleService.getById(ruleId);
        byId.setSkuNo(SkuNo);
        exchangeScoreRuleService.updateById(byId);
        goodsConsoleManager.saveScoreGood(scoreRuleByIdData, ruleId, SkuNo,"admin");
        return CommonResult.success();
    }


    @Autowired
    private SyncDbDataTask syncDbDataTask;

    /**
     *
     * 同步device库数据到pitpat
     * @return
     */
    @PostMapping("/syncDbDataTask")
    public Result syncDbDataTask() {
        syncDbDataTask.exec();
        return CommonResult.success();
    }
    @GetMapping("/syncErpOrder")
    public Result<List<Long>> syncErpOrder(@RequestParam(value = "orderId", required = false)Long orderId) {
        List<Long> arrayList = new ArrayList<>();
        List<ZnsOrderEntity> list = znsOrderService.findListByQuery(new OrderQuery().setIds(List.of(orderId)).setSourceType(OrderConstant.OrderSourceEnum.NEW_PITPAT.getType()));
        list.forEach(orderEntity->
            executor.execute(() -> {
                ErpApiUtil.addOrModifyOrder(mallOrderBizService.convertErpOrderListRespDto(orderEntity));
            }
        ));
        return CommonResult.success(arrayList);
    }

    @GetMapping("/syncErpScoreSku")
    public Result<List<Long>> syncErpScoreSku() {
        List<Long> arrayList = new ArrayList<>();
        List<ScoreMallGoodsRelationDo> scoreGoodsList = scoreMallGoodsRelationService.findList(new ScoreMallGoodsRelationQuery());
        List<Long> goodsIdList = scoreGoodsList.stream().map(ScoreMallGoodsRelationDo::getGoodsId).distinct().collect(Collectors.toList());
        List<ZnsGoodsEntity> list = goodsService.findList(new GoodsQuery().setGoodsIds(goodsIdList));
        list.forEach(znsGoodsEntity -> {
            List<ZnsGoodsSkuEntity> skuList = znsGoodsSkuService.findList(new GoodsSkuQuery().setGoodsId(znsGoodsEntity.getId()));
            for (ZnsGoodsSkuEntity znsGoodsSkuEntity : skuList) {
                try {
                    goodsConsoleManager.syncErpSku(znsGoodsSkuEntity, znsGoodsEntity, 1);
                    arrayList.add(znsGoodsSkuEntity.getId());
                    log.info("同步商品sku：{}", znsGoodsSkuEntity.getId());
                    // 添加延时，确保ERP接口处理完成
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    log.error("同步商品sku延时被中断：{}", znsGoodsSkuEntity.getId(), e);
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    log.error("同步商品sku失败：{}", znsGoodsSkuEntity.getId(), e);
                }
            }
        });
        return CommonResult.success(arrayList);

    }

    @Autowired
    private ZnsUserAddressService znsUserAddressService;

    @GetMapping("/syncErpScoreOrder")
    public Result<Void> syncErpScoreOrder(Long userId, Long ruleId, Long userAddressId) {
        ZnsUserEntity znsUserEntity = userService.findById(userId);
        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(ruleId);
        ZnsUserAddressEntity userAddress = znsUserAddressService.findById(userAddressId);
        if (Objects.isNull(znsUserEntity) || Objects.isNull(exchangeScoreRule) || Objects.isNull(userAddress)) {
            return CommonResult.fail("参数不正确");
        }
        ScoreMallGoodsRelationDo relationDo = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setRuleId(ruleId).setCountryCode(userAddress.getCountryCode()));
        Long goodsId = Objects.nonNull(relationDo) ? relationDo.getGoodsId() : null;
        if (Objects.isNull(goodsId)) {
            return CommonResult.fail("未找到对应商品");
        }
        ScoreMallGoodsRelationDo userRelation = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setRuleId(ruleId).setCountryCode(I18nConstant.CountryCodeEnum.US.getCode()));
        BigDecimal goodsOriginalPrice = BigDecimal.ZERO;
        if (Objects.nonNull(userRelation)) {
            ZnsGoodsEntity goodsEntity = goodsService.findById(userRelation.getGoodsId());
            goodsOriginalPrice = Objects.nonNull(goodsEntity) ? goodsEntity.getOriginalPrice() : BigDecimal.ZERO;
        }
        //增加订单记录
        ZnsOrderEntity znsOrderEntity = mallOrderBizService.scoreMallFillOrder(znsUserEntity, exchangeScoreRule, userAddressId, goodsId, goodsOriginalPrice);
        CompletableFuture.runAsync(() -> ErpApiUtil.addOrModifyOrder(mallOrderBizService.convertErpOrderListRespDto(znsOrderEntity)));
        return CommonResult.success();
    }

    @Autowired
    private MallHomePageService homePageService;
    @Autowired
    private MallHomeModuleService mallHomeModuleService;
    @PostMapping("/syncHomeModule")
    public Result<Void> syncHomeModule() {
        List<MallHomePage> listByQuery = homePageService.findListByQuery(new MallHomePageQueryDto());
        for (MallHomePage mallHomePage : listByQuery) {
            List<MallHomeModule> modules = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(mallHomePage.getId()));
            for (MallHomeModule module : modules) {
                module.setTimeStyle(1);
                module.setStartTime(mallHomePage.getStartTime().toInstant().atZone(ZoneId.systemDefault()));
                module.setEndTime(mallHomePage.getEndTime().toInstant().atZone(ZoneId.systemDefault()));
                mallHomeModuleService.update(module);

            }
        }
        return CommonResult.success();
    }


    @Resource
    private ClubManager clubManager;


    /**
     * 赛事俱乐部初始化
     * @return
     */
    @GetMapping("/initActivityClub")
    public Result<Void> initActivityClub(@RequestParam Long userId) {
        clubManager.initActivityClub(userId);
        return CommonResult.success();
    }



}
