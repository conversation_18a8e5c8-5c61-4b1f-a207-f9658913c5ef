package com.linzi.pitpat.admin.bussiness;

import com.linzi.pitpat.admin.model.LoginUser;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.userservice.dto.request.label.LabelReq;
import com.linzi.pitpat.data.userservice.dto.request.label.SaveOrUpdateUserClassificationReq;
import com.linzi.pitpat.data.userservice.dto.request.label.UserClassificationReq;
import com.linzi.pitpat.data.userservice.dto.response.label.UserClassificationDetailVo;
import com.linzi.pitpat.data.userservice.dto.response.label.UserClassificationVo;
import com.linzi.pitpat.data.userservice.model.entity.label.LabelClassification;
import com.linzi.pitpat.data.userservice.model.query.UserClassificationQuery;
import com.linzi.pitpat.data.userservice.service.label.LabelClassificationService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserClassificationBusiness {
    @Autowired
    private LabelClassificationService labelClassificationService;


    public Result<List<UserClassificationVo>> labelList(UserClassificationReq req) {

        UserClassificationQuery query = UserClassificationQuery.builder().classificationNameLike(req.getClassificationName()).lastStr("ORDER BY order_num DESC , gmt_modified DESC").build();
        List<LabelClassification> userClassifications = labelClassificationService.findList(query);
        List<UserClassificationVo> result = userClassifications.stream().map(i -> {
            UserClassificationVo userClassificationVo = new UserClassificationVo();
            BeanUtils.copyProperties(i, userClassificationVo);
            return userClassificationVo;
        }).collect(Collectors.toList());
        return CommonResult.success(result);
    }

    public void saveOrUpdate(SaveOrUpdateUserClassificationReq req, LoginUser loginUser) {
        LabelClassification classification = new LabelClassification();
        classification.setCreateBy(loginUser.getUser().getUserName());
        if (Objects.nonNull(req.getId())) {
            classification = labelClassificationService.findById(req.getId());
        }
        classification.setClassificationName(req.getClassificationName());
        classification.setOrderNum(req.getOrderNum());
        classification.setParentId(req.getParentId());
        classification.setUpdateBy(loginUser.getUser().getUserName());
        classification.setStatus(req.getStatus());
        classification.setGmtModified(ZonedDateTime.now());
        if (classification.getId() == null) {
            labelClassificationService.insert(classification);
        } else {
            labelClassificationService.update(classification);
        }

    }

    public void delete(LabelReq req) {
        LabelClassification classification = labelClassificationService.findById(req.getId());
        labelClassificationService.deleteById(req.getId());
        if (classification.getParentId() == 0) {
            UserClassificationQuery query = UserClassificationQuery.builder().parentId(classification.getId()).build();
            List<LabelClassification> userClassifications = labelClassificationService.findList(query);
            if (!CollectionUtils.isEmpty(userClassifications)) {
                userClassifications.forEach(i -> {
                    labelClassificationService.deleteById(i.getId());
                });
            }
        }
    }

    public Result<UserClassificationDetailVo> getById(LabelReq req) {
        UserClassificationDetailVo userClassificationDetailVo = new UserClassificationDetailVo();
        LabelClassification classification = labelClassificationService.findById(req.getId());
        BeanUtils.copyProperties(classification, userClassificationDetailVo);
        if (classification.getParentId() != 0) {
            LabelClassification parentId = labelClassificationService.findById(classification.getParentId());
            userClassificationDetailVo.setParentName(parentId.getClassificationName());
        }
        return CommonResult.success(userClassificationDetailVo);
    }

    public Result<List<LabelClassification>> parentList() {
        UserClassificationQuery query = UserClassificationQuery.builder().parentId(0L).build();
        List<LabelClassification> userClassifications = labelClassificationService.findList(query);
        return CommonResult.success(userClassifications);
    }
}
