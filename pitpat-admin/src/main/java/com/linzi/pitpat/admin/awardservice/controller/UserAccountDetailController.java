package com.linzi.pitpat.admin.awardservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.awardservice.constant.enums.AccountConstant;
import com.linzi.pitpat.data.awardservice.dto.consloe.WithdrawalDetailVo;
import com.linzi.pitpat.data.awardservice.dto.consloe.request.ArtificialSendAwardRequestDto;
import com.linzi.pitpat.data.awardservice.manager.console.UserCouponSendBatchManager;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.AccountDetailPo;
import com.linzi.pitpat.data.awardservice.model.request.WithdrawalRequest;
import com.linzi.pitpat.data.awardservice.model.vo.AccountWithdrawalVo;
import com.linzi.pitpat.data.awardservice.service.AccountWithdrawalUserService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.vo.AbnormalDetailVo;
import com.linzi.pitpat.data.entity.vo.AuditDetailVo;
import com.linzi.pitpat.data.entity.vo.TradeDetailVo;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.SysRole;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysUserService;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.excel.service.ExcelService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

import static com.linzi.pitpat.admin.util.SecurityUtils.getLoginUser;

/**
 * 用户账户明细服务类
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/accountDetail")
@Slf4j
@RequiredArgsConstructor
public class UserAccountDetailController extends BaseController {
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private AppMessageService appMessageService;
    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private AccountWithdrawalUserService accountWithdrawalUserService;
    @Resource
    private ISysUserService sysUserService;

    private final RedissonClient redissonClient;

    private final UserCouponSendBatchManager userCouponSendBatchManager;

    private final ExcelService excelService;

    /**
     * 提现账单明细
     *
     * @param po
     * @return
     */
    @PostMapping("/withdrawal/list")
    public Result<Page<AccountWithdrawalVo>> userAccountWithdrawalList(@RequestBody AccountDetailPo po) {
        if (!StringUtils.hasText(po.getOrderFiled())) {
            po.setOrderFiled("d.create_time");
            po.setOrderType("desc");
        }
        Page<AccountWithdrawalVo> accountWithdrawalVoPage = userAccountDetailService.userAccountPage(po);
        return CommonResult.success(accountWithdrawalVoPage);
    }


    /**
     * 导出用户提现列表
     */
    @PreAuthorize("@ss.hasPermi('admin:accountDetail:withdrawal:exportList')")
    @PostMapping("/withdrawal/exportList")
    public void exportList(@RequestBody AccountDetailPo po, HttpServletResponse response) {
        List<AccountWithdrawalVo> sysUsers = userAccountDetailService.userAccountList(po);
        if (!CollectionUtils.isEmpty(sysUsers) && sysUsers.size() > 2000) {
            throw new BaseException("导出数据超过两千,请过滤后导出");
        }
        for (AccountWithdrawalVo sysUser : sysUsers) {
            if (Objects.nonNull(sysUser.getTradeStatus())){
                sysUser.setTradeStatusStr(AccountConstant.TradeStatusEnum.findTradeStatusByCode(sysUser.getTradeStatus()).getDesc());
            }
        }
        excelService.exportExcel(response, sysUsers, AccountWithdrawalVo.class, "用户提现");
    }

    /**
     * 提现申请审批同意
     */
    @PostMapping("/audit/agree")
    @Log(title = "提现申请审批同意", businessType = BusinessType.UPDATE)
    public Result agree(@RequestBody WithdrawalRequest req) {
        SysUser user = getLoginUser().getUser();
        if (Objects.isNull(user)) {
            return CommonResult.fail("操作用户信息获取失败");
        }
        Long userId = user.getUserId();
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = userAccountDetailService.selectById(req.getId());
        if (StringUtils.hasText(req.getRemark())) {
            znsUserAccountDetailEntity.setAuditRemark(req.getRemark());
        }
        znsUserAccountDetailEntity.setAuditId(userId);
        znsUserAccountDetailEntity.setAuditTime(ZonedDateTime.now());
        znsUserAccountDetailEntity.setAuditStatus(2);
        boolean result = userAccountDetailService.update(znsUserAccountDetailEntity);
        if (!result) {
            log.error("审批操作失败，操作人id：{} ，用户账号详情id：{}", userId, req.getId());
            return CommonResult.fail("同意审批操作失败");
        }
        log.info("审批同意操作，操作人id：{} ，用户账号详情id：{} ", userId, req.getId());
        return CommonResult.success();
    }

    /**
     * 提现申请审批拒绝
     */
    @PostMapping("/audit/Refuse")
    @Log(title = "提现申请审批拒绝", businessType = BusinessType.UPDATE)
    public Result refuse(@RequestBody WithdrawalRequest req) {
        SysUser user = getLoginUser().getUser();
        if (Objects.isNull(user)) {
            return CommonResult.fail("操作用户信息获取失败");
        }
        Long userId = user.getUserId();
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = userAccountDetailService.selectById(req.getId());
        if (StringUtils.hasText(req.getRemark())) {
            znsUserAccountDetailEntity.setAuditRemark(req.getRemark());
        }
        znsUserAccountDetailEntity.setAuditId(userId);
        znsUserAccountDetailEntity.setAuditTime(ZonedDateTime.now());
        znsUserAccountDetailEntity.setAuditStatus(1);
        boolean result = userAccountDetailService.update(znsUserAccountDetailEntity);
        if (!result) {
            log.error("审批操作失败，操作人id：{} ，用户账号详情id：{}", userId, req.getId());
            return CommonResult.fail("拒绝审批操作失败");
        }
        log.info("审批拒绝操作，操作人id：{} ，用户账号详情id：{} ", userId, req.getId());
        return CommonResult.success();
    }

    /**
     * 提现异常标记
     */
    @PostMapping("/exception/flag")
    @Log(title = "提现异常标记", businessType = BusinessType.UPDATE)
    public Result flag(@RequestBody WithdrawalRequest req) {
        SysUser user = getLoginUser().getUser();
        if (Objects.isNull(user)) {
            return CommonResult.fail("操作用户信息获取失败");
        }
        Long userId = user.getUserId();
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = userAccountDetailService.selectById(req.getId());
        if (StringUtils.hasText(req.getRemark())) {
            znsUserAccountDetailEntity.setAbnormalRemark(req.getRemark());
        }
        znsUserAccountDetailEntity.setAbnormalMarkersId(userId);
        znsUserAccountDetailEntity.setAbnormalRemarkTime(ZonedDateTime.now());
        znsUserAccountDetailEntity.setAbnormalStatus(1);
        boolean result = userAccountDetailService.update(znsUserAccountDetailEntity);
        if (!result) {
            log.error("异常标记操作失败，操作人id：{} ，用户账号详情id：{}", userId, req.getId());
            return CommonResult.fail("异常标记操作失败");
        }
        log.info("异常标记操作，操作人id：{} ，用户账号详情id：{} ", userId, req.getId());
        return CommonResult.success();
    }

    /**
     * 转账人账号列表
     */
    @PostMapping("/withdrawal/transferors")
    public Result<List<String>> transferorList() {
        List<String> result = accountWithdrawalUserService.selectUserList();
        if (!CollectionUtils.isEmpty(result)) {
            return CommonResult.success(result);
        }
        return CommonResult.fail("查询转账人信息失败");
    }

    /**
     * 详情
     */
    @PostMapping("/withdrawal/detail")
    public Result<WithdrawalDetailVo> auditDetail(@RequestBody WithdrawalRequest req) {
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = userAccountDetailService.selectById(req.getId());
        if (Objects.isNull(znsUserAccountDetailEntity)) {
            return CommonResult.fail("查询用户明细失败，明细id不存在");
        }
        ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(znsUserAccountDetailEntity.getUserId());
        WithdrawalDetailVo withdrawalDetailVo = new WithdrawalDetailVo();
        //填充审核详情
        if (znsUserAccountDetailEntity.getAuditStatus() != 0) {
            AuditDetailVo auditDetailVo = new AuditDetailVo();
            auditDetailVo.setAuditStatus(znsUserAccountDetailEntity.getAuditStatus());
            auditDetailVo.setRemark(znsUserAccountDetailEntity.getAuditRemark());
            SysUser sysUser = sysUserService.findByUserId((znsUserAccountDetailEntity.getAuditId()));
            auditDetailVo.setOperatorName(sysUser.getNickName());
            auditDetailVo.setOperatorTime(znsUserAccountDetailEntity.getAuditTime());
            //通过用户id查角色名称
            SysRole sysRole = sysUserService.selectUserRoleById(znsUserAccountDetailEntity.getAuditId());

            if (Objects.nonNull(sysRole)) {
                auditDetailVo.setRole(sysRole.getRoleName());
            }
            withdrawalDetailVo.setAuditDetailVo(auditDetailVo);
        }
        //填充异常详情
        if (znsUserAccountDetailEntity.getAbnormalStatus() == 1) {
            AbnormalDetailVo abnormalDetailVo = new AbnormalDetailVo();
            abnormalDetailVo.setAbnormalStatus(znsUserAccountDetailEntity.getAbnormalStatus());
            abnormalDetailVo.setRemark(znsUserAccountDetailEntity.getAbnormalRemark());
            SysUser sysUser = sysUserService.findByUserId((znsUserAccountDetailEntity.getAuditId()));
            abnormalDetailVo.setOperatorName(sysUser.getNickName());
            abnormalDetailVo.setOperatorTime(znsUserAccountDetailEntity.getAbnormalRemarkTime());
            //通过用户id查角色名称
            SysRole sysRole = sysUserService.selectUserRoleById(znsUserAccountDetailEntity.getAbnormalMarkersId());
            abnormalDetailVo.setOperatorTime(znsUserAccountDetailEntity.getAbnormalRemarkTime());
            if (Objects.nonNull(sysRole)) {
                abnormalDetailVo.setRole(sysRole.getRoleName());
            }
            withdrawalDetailVo.setAbnormalDetailVo(abnormalDetailVo);
        }
        //填转账详情
        if (znsUserAccountDetailEntity.getTradeStatus() == 2) {
            TradeDetailVo tradeDetailVo = new TradeDetailVo();
            tradeDetailVo.setTradeStatus(znsUserAccountDetailEntity.getTradeStatus());
            tradeDetailVo.setRemark(znsUserAccountDetailEntity.getTransferAccountsRemark());
            SysUser sysUser = sysUserService.findByUserId((znsUserAccountDetailEntity.getAuditId()));
            tradeDetailVo.setOperatorName(sysUser.getNickName());
            tradeDetailVo.setOperatorTime(znsUserAccountDetailEntity.getTradeSuccessTime());
            tradeDetailVo.setAmount(znsUserAccountDetailEntity.getActualAmount());
            tradeDetailVo.setActualPaypalAccount(znsUserAccountDetailEntity.getActualPaypalAccount());
            tradeDetailVo.setOurSidePaypalAccount(znsUserAccountDetailEntity.getOurSidePaypalAccount());
            //通过用户id查角色名称
            SysRole sysRole = sysUserService.selectUserRoleById(znsUserAccountDetailEntity.getTransferAccountsId());
            tradeDetailVo.setOperatorTime(znsUserAccountDetailEntity.getTradeSuccessTime());
            if (Objects.nonNull(sysRole)) {
                tradeDetailVo.setRole(sysRole.getRoleName());
            }
            tradeDetailVo.setCurrency(I18nConstant.buildCurrency(accountEntity.getCurrencyCode()));
            withdrawalDetailVo.setTradeDetailVo(tradeDetailVo);
        }
        return CommonResult.success(withdrawalDetailVo);
    }

    /**
     * 标记到账
     *
     * @return
     */
    @RepeatSubmit
    @PostMapping("/withdrawal/arrival")
    @Log(title = "标记到账", businessType = BusinessType.UPDATE)
    public Result arrival(@RequestBody AccountWithdrawalVo po) {
        if (!StringUtils.hasText(po.getActualPaypalAccount())) {
            return CommonResult.fail("实际到账PayPal账号不能为空");
        }
        if (!StringUtils.hasText(po.getOurSidePaypalAccount())) {
            return CommonResult.fail("我方PayPal账号不能为空");
        }
        if (Objects.isNull(po.getId())) {
            return CommonResult.fail("详情id不能为空");
        }
        if (Objects.isNull(po.getActualAmount())) {
            return CommonResult.fail("实际转账金额不能为空");
        }
        if (!StringUtils.hasText(po.getTransferAccountsRemark())) {
            return CommonResult.fail("转账备注不能为空");
        }
        SysUser user = getLoginUser().getUser();
        if (Objects.isNull(user)) {
            return CommonResult.fail("操作用户信息获取失败");
        }
        String detailKey = RedisConstants.WITHDRAWAL_ARRIVAL_KEY + po.getId();
        RLock lock = redissonClient.getLock(detailKey);
        lock.lock();
        if (!lock.isLocked()) {
            return CommonResult.fail("不能重复操作");
        }
        try {
            Long operatorId = user.getUserId();
            ZnsUserAccountDetailEntity detailEntity = userAccountDetailService.selectById(po.getId());
            if (Objects.isNull(detailEntity)) {
                return CommonResult.fail("记录不存在");
            }
            if (!AccountConstant.TradeStatusEnum.TRADE_STATUS_1.getCode().equals(detailEntity.getTradeStatus())) {
                return CommonResult.fail("记录状态不是处理中");
            }
            ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(detailEntity.getUserId());
            userAccountService.withdrawalArrival(detailEntity.getUserId(), detailEntity.getAmount(), po, operatorId);
            ActivityNotificationEnum notificationEnum = ActivityNotificationEnum.WITHDRAWAL_ARRIVAL;
            String content = String.format(
                    "%s 提现已到账，请及时查收 PayPal", accountEntity.getCurrencySymbol() + detailEntity.getAmount().toString());

            SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());

            Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);
            if (activityId.equals(detailEntity.getActivityId())) {
                ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(activityId, detailEntity.getUserId());
                if (znsRunActivityUserEntity != null) {
                    znsRunActivityUserEntity.setCompleteTime(ZonedDateTime.now());
                    znsRunActivityUserEntity.setIsComplete(1);
                    znsRunActivityUserService.updateById(znsRunActivityUserEntity);
                }
            }

            ImMessageBo bo = appMessageService.assembleImMessageAward(content, po.getId().toString(), "3");
            MessageBo messageBo = appMessageService.assembleMessage(po.getId(), content, "3", NoticeTypeEnum.WITHDRAWAL_ACCOUNT.getType());
            appMessageService.sendImAndPushUserIds(Arrays.asList(detailEntity.getUserId()), bo, messageBo);
            //任务金额处理
            if (detailEntity.getActivityType() == 7) {
                List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(detailEntity.getActivityId(), detailEntity.getUserId(), 2);
                for (RunActivityUserTask runActivityUserTask : runActivityUserTasks) {
                    runActivityUserTask.setAwardStatus(3);
                    runActivityUserTaskService.update(runActivityUserTask);
                }
            }
        } catch (Exception e) {
            log.error("提现审核-标记到账操作异常：", e);
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success();
    }

    /**
     * 手动发放金额
     *
     * @return
     */
    @RepeatSubmit
    @PostMapping("/artificialSendAward")
    @Log(title = "手动发放金额", businessType = BusinessType.OTHER)
    public Result artificialSendAward(@RequestParam(value = "file", required = false) MultipartFile file, @Validated ArtificialSendAwardRequestDto requestDto) {
        String result = userCouponSendBatchManager.artificialSendAward(file, requestDto, SecurityUtils.getUsername());
        return CommonResult.success(result);
    }
}
