/**
 * @description: 消息中心控制器
 * @author: yangpeng projectName: pitpat-server fileName: MessageCenterController.java packageName:
 * com.linzi.pitpat.admin.controller date: 2022-03-11 11:23 AM copyright(c) 2018-2020
 * 杭州霖扬网络科技有限公司版权所有
 */
package com.linzi.pitpat.admin.controller.message.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.entity.po.MessagePo;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.messageservice.model.entity.ZnsMessageEntity;
import com.linzi.pitpat.data.messageservice.model.query.MessageQuery;
import com.linzi.pitpat.data.messageservice.service.ZnsMessageService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * @description: 消息中心控制器
 * @author: yangpeng
 * @className: MessageCenterController
 * @packageName: com.linzi.pitpat.admin.controller
 * @version: V1.0
 * @date: 2022-03-11 11:23 AM
 **/
@Slf4j
@RestController
@RequestMapping("/message")
public class MessageCenterController {

    @Resource
    private ZnsMessageService messageService;

    /**
     * 消息列表
     */
    @PostMapping("/list")
    public Result<Page<ZnsMessageEntity>> messageList(@RequestBody MessagePo po) {
        Long userId = SecurityUtils.getUserId();
        if (userId != 1) {
            po.setUserId(userId);
        }
        Page<ZnsMessageEntity> result = messageService.messageList(po);
        return CommonResult.success(result);
    }

    @GetMapping("/confirmRead")
    @Log(title = "确认消息已读", businessType = BusinessType.UPDATE)
    public Result confirmRead(String messageIds) {
        if (!StringUtils.hasText(messageIds)) {
            return CommonResult.fail("已读消息id不能为空");
        }
        Long userId = SecurityUtils.getUserId();

        String[] split = StringUtil.split(messageIds, ",");
        List<Long> ids = NumberUtils.stringToLong(split);
        ZnsMessageEntity update = new ZnsMessageEntity();
        update.setIsRead(1);
        update.setModifieTime(ZonedDateTime.now());
        messageService.updateQuery(update, MessageQuery.builder().sysUserId(userId).ids(ids).build());

        return CommonResult.success();
    }
}
