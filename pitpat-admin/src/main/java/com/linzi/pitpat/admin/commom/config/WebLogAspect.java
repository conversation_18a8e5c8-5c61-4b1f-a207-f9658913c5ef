package com.linzi.pitpat.admin.commom.config;

import com.linzi.pitpat.admin.commom.service.TokenService;
import com.linzi.pitpat.admin.model.LoginUser;
import com.linzi.pitpat.framework.web.aspect.BaseLogAspect;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.ServletUtils;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 日志aop
 * Created by wutao on 2018/10/12.
 */
@Aspect
@Component
@Order(1)
public class WebLogAspect extends BaseLogAspect {
    @Override
    protected void appendExtendInfo(StringBuffer sb, HttpServletRequest request) {
        LoginUser loginUser = SpringContextUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
        if (Objects.nonNull(loginUser)) {
            sb.append("userName=").append(loginUser.getUsername()).append(BLANK_SPACE);
        }
    }


}
