/**
 * @description: 系统机器人
 * @author: yangpeng projectName: pitpat-server fileName: SysRobotController.java packageName:
 * com.linzi.pitpat.admin.controller.system date: 2022-02-08 10:44 AM copyright(c) 2018-2020
 * 杭州霖扬网络科技有限公司版权所有
 */
package com.linzi.pitpat.admin.systemservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.AesUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.entity.dto.RobotUserDto;
import com.linzi.pitpat.data.entity.po.RobotUserPo;
import com.linzi.pitpat.data.entity.vo.RobotUserReq;
import com.linzi.pitpat.data.entity.vo.RobotUserVo;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.ISysUserService;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserAddQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @description: 系统机器人
 * @author: yangpeng
 * @className: SysRobotController
 * @packageName: com.linzi.pitpat.admin.controller.system
 * @version: V1.0
 * @date: 2022-02-08 10:44 AM
 **/
@Slf4j
@RestController
@RequestMapping({"/system/robot", "/test/system/robot"})
public class SysRobotController extends BaseController {

    @Resource
    private ZnsUserService userService;
    @Resource
    private ZnsUserAddService userAddService;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Value("${user.password.aes}")
    public String aesKey;


    @PostMapping("/addRobot")
    @Log(title = "新建机器人", businessType = BusinessType.INSERT)
    public Result addRobot(@RequestBody UserRequest request) {
        // 查询负责人是否存在
        if (null == request.getFollowUserId()) {
            return CommonResult.fail("负责人Id参数缺失");
        }
        SysUser sysUser = sysUserService.findByUserId(request.getFollowUserId());
        if (null == sysUser) {
            return CommonResult.fail("负责人不存在");
        }
        if (StringUtil.isEmpty(request.getPassword())) {
            return CommonResult.fail("密码缺失");
        }
        //密码处理，使用对称加解密
        String aesPassword = AesUtil.aesEncryptString(request.getPassword(), aesKey);
        request.setPassword(aesPassword);
        // 调用api服务注册用户
        request.setIsRobot(1);
        if (Objects.nonNull(request.getBirthday())) {
            request.setBirthdayStr(DateUtil.parseDateToStr("dd/MM/yyyy", request.getBirthday()));
            request.setBirthday(null);
        }
        String countryCode = request.getCountryCode();
        String country = request.getCountry();
        String stateCode = request.getStateCode();
        String state = request.getState();
        I18nConstant.CountryCodeEnum countryCodeEnum = I18nConstant.CountryCodeEnum.findByCode(countryCode);
        if (countryCodeEnum == null) {
            countryCodeEnum = I18nConstant.CountryCodeEnum.US;
            country = countryCodeEnum.getEnName();
            countryCode = countryCodeEnum.getCode();
            stateCode = countryCodeEnum.getDefaultStateCode();
            state = countryCodeEnum.getDefaultState();
        }
        request.setCountry(country);
        request.setCountryCode(countryCode);
        request.setState(state);
        request.setStateCode(stateCode);
        Result checkParam = userService.registerCheckParam(request);
        if (checkParam != null) {
            return CommonResult.fail(checkParam.getMsg());
        }
        //3.5身高修改兼容前端
        if (Objects.nonNull(request.getHeight()) && request.getHeight() > 0) {
            request.setHeight(request.getHeight() * 1000);
        }
        if (Objects.nonNull(request.getWeight()) && request.getWeight() > 0) {
            request.setWeight(request.getWeight() * 1000);
        }


        Result result = userService.register(request, 999999);

        if (!CommonError.SUCCESS.getCode().equals(result.getCode())) {
            return CommonResult.fail(result.getMsg());
        }
        ZnsUserEntity userEntity = userService.findByEmail(request.getEmailAddress());
        if (Objects.nonNull(userEntity)) {
            // 机器人用户注册成功，新增机器人用户额外信息
            userAddService.addRobotUserAddInfo(userEntity.getId(), sysUser.getUserId(), sysUser.getUserName(), sysUser.getDeptId());
            return CommonResult.success();
        } else {
            return CommonResult.fail("机器人用户注册失败");
        }
    }

    /**
     * 机器人用户列表
     */
    @PostMapping("/list")
    public Result<Page<RobotUserDto>> robotList(@RequestBody RobotUserPo robotUserPo) {
        Page<RobotUserDto> page = userService.robotUserList(robotUserPo);
        return CommonResult.success(page);
    }

    /**
     * 启用/禁用 机器人
     */
    @GetMapping("/enable")
    public Result enableRobot(Long robotUserId, Integer status, Long otherRobotUserId) {
        return userService.enableRobotStatus(robotUserId, status, otherRobotUserId, SecurityUtils.getLoginUser().getUser());
    }

    @PostMapping("/editRobot")
    @Log(title = "编辑机器人", businessType = BusinessType.UPDATE)
    public Result editRobot(@RequestBody UserRequest request) {
        // 查询负责人是否存在
        if (null == request.getFollowUserId()) {
            return CommonResult.fail("负责人Id参数缺失");
        }
        SysUser sysUser = sysUserService.findByUserId(request.getFollowUserId());
        if (null == sysUser) {
            return CommonResult.fail("负责人不存在");
        }
        //3.5身高修改兼容前端
        if (Objects.nonNull(request.getHeight()) && request.getHeight() > 0) {
            request.setHeight(request.getHeight() * 1000);
        }
        if (Objects.nonNull(request.getWeight()) && request.getWeight() > 0) {
            request.setWeight(request.getWeight() * 1000);
        }
        Result result = userService.editRobotUser(request);
        if (null != result) {
            return result;
        }

        // 查询当前机器人的负责人
        ZnsUserAddEntity userAddEntity = userAddService.selectUserAddInfo(request.getId());
        if (null != userAddEntity) {
            // 修改机器人信息之后,判断改机器人的负责人是否更改，如果更改,则机器人跟进的用户的所属负责人也要更改
            if (request.getFollowUserId() > 0 && !userAddEntity.getFollowUserId().equals(request.getFollowUserId())) {
                //修改机器人负责人
                userAddEntity.setFollowUserId(sysUser.getUserId());
                userAddEntity.setFollowUserName(sysUser.getUserName());
                userAddEntity.setModifieTime(ZonedDateTime.now());
                userAddEntity.setUpdateFollowTime(ZonedDateTime.now());
                userAddService.update(userAddEntity);

                // 查询该机器人下的所有跟进用户
                UserAddQuery query = UserAddQuery.builder().followRobotId(request.getId()).build();
                List<ZnsUserAddEntity> userAddEntities = userAddService.findList(query);
                if (!CollectionUtils.isEmpty(userAddEntities)) {
                    for (ZnsUserAddEntity userAdd : userAddEntities) {
                        userAdd.setFollowUserId(sysUser.getUserId());
                        userAdd.setFollowUserName(sysUser.getUserName());
                    }
                    // 批量更新跟进用户的所属机器人
                    userAddService.updateBatchById(userAddEntities);
                }
            }
            return CommonResult.success();
        } else {
            // 机器人用户注册成功，新增机器人用户额外信息
            userAddService.addRobotUserAddInfo(request.getId(), sysUser.getUserId(), sysUser.getUserName(), sysUser.getDeptId());
            return CommonResult.success();
        }
    }


    /**
     * 获取负责跟进的机器人列表
     */
    @PostMapping("/followRobotList")
    public Result<List<RobotUserVo>> followRobotList(@RequestBody RobotUserReq robotUserReq) {
        List<RobotUserVo> robotUserVos = userService.followRobotUserList(robotUserReq);
        return CommonResult.success(robotUserVos);
    }


    @GetMapping("/recharge")
    @Log(title = "机器人充值", businessType = BusinessType.OTHER)
    public Result<Void> recharge(BigDecimal amount, Integer robotUserId) {
        if (Objects.isNull(amount) || Objects.isNull(robotUserId)) {
            return CommonResult.fail("请求参数不能为空");
        }
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return CommonResult.fail("充值金额需大于0");
        }
        //查询账户是否是机器人
        ZnsUserEntity user = userService.findById((long) robotUserId);
        if (Objects.isNull(user) || user.getIsRobot() == 0) {
            return CommonResult.fail("机器人用户不存在");
        }

        // 给用户余额发送奖励
        userAccountService.increaseAmount(amount, MapUtil.getLong(robotUserId), false);
        // 新增用户奖励余额明细
        String billNo = NanoId.randomNanoId();
        ;
        ZonedDateTime tradeTime = ZonedDateTime.now();
        userAccountDetailService.addRunActivityAccountDetail(robotUserId, AccountDetailTypeEnum.RECHARGE,
                AccountDetailSubtypeEnum.CHALLENGE_RUM.getType(), 1, amount, billNo, tradeTime, null);
        return CommonResult.success();
    }

}
