package com.linzi.pitpat.admin.controller.user.controller;

import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.request.ActiveRuleBlackConfigReq;
import com.linzi.pitpat.data.userservice.dto.request.NewUserActiveRuleConfigReq;
import com.linzi.pitpat.data.userservice.dto.request.OldUserActiveRuleConfigReq;
import com.linzi.pitpat.data.userservice.dto.request.UserActiveRuleConfigReqDto;
import com.linzi.pitpat.data.userservice.service.UserActiveRuleConfigService;
import com.linzi.pitpat.data.vo.user.BlackConfigVo;
import com.linzi.pitpat.data.vo.useractive.NewUserActiveRuleConfigVo;
import com.linzi.pitpat.data.vo.useractive.OldUserActiveRuleConfigVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 用户注册/活跃用户活动控制器
 */
@RestController
@RequestMapping("/user/active/config")
public class UserActiveRuleConfigController extends BaseController {

    @Autowired
    private UserActiveRuleConfigService userActiveRuleConfigService;
    @Autowired
    private ISysConfigService sysConfigService;


    /**
     * 更新发放规则配置
     *
     * @param req
     * @return
     */
    @RepeatSubmit
    @Log(title = "更新发放规则配置", businessType = BusinessType.UPDATE)
    @PostMapping("/updateState")
    public Result<Boolean> updateState(@RequestBody @Validated UserActiveRuleConfigReqDto req) {
        List<String> stateCodes = List.of(ConfigKeyEnums.NEWUSER_ACTIVATE_STATE.getCode(), ConfigKeyEnums.OLDUSER_ACTIVATE_STATE.getCode());
        if (!stateCodes.contains(req.getType())) {
            throw new BaseException("自动发券类型不对");
        }
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(req.getType());
        if (Objects.isNull(sysConfig)) {
            throw new BaseException("配置项缺失");
        }
        sysConfig.setConfigValue(req.getState() + "");
        sysConfig.setUpdateBy(SecurityUtils.getUsername());
        sysConfig.setUpdateTime(ZonedDateTime.now());
        sysConfigService.updateConfig(sysConfig);
        return CommonResult.success(true);
    }


    /**
     * 新用户发放规则配置
     *
     * @param newUserActivateRuleConfigReq
     * @return
     */
    @PostMapping("/newUser/update")
    public Result newUserConfigUpdate(@RequestBody @Validated NewUserActiveRuleConfigReq newUserActivateRuleConfigReq) {
        userActiveRuleConfigService.newUserConfigUpdate(newUserActivateRuleConfigReq);
        return CommonResult.success();
    }

    /**
     * 活跃用户发放规则配置
     *
     * @param
     * @return
     */
    @PostMapping("/oldUser/update")
    public Result oldUserConfigUpdate(@RequestBody OldUserActiveRuleConfigReq oldUserActivateRuleConfigReq) {
        userActiveRuleConfigService.oldUserConfigUpdate(oldUserActivateRuleConfigReq);
        return CommonResult.success();
    }


    /**
     * 活跃用户发放规则配置查询
     *
     * @param
     * @return
     */
    @PostMapping("/oldUser/query")
    public Result<OldUserActiveRuleConfigVo> oldUserConfigQuery() {
        OldUserActiveRuleConfigVo lists = userActiveRuleConfigService.oldUserConfigQuery();
        return CommonResult.success(lists);
    }

    /**
     * 新用户发放规则配置查询
     *
     * @param
     * @return
     */
    @PostMapping("/newUser/query")
    public Result<NewUserActiveRuleConfigVo> newUserConfigQuery() {
        NewUserActiveRuleConfigVo userActivateRuleConfigVo = userActiveRuleConfigService.newUserConfigQuery();
        return CommonResult.success(userActivateRuleConfigVo);
    }

    /**
     * 黑名单过滤配置
     *
     * @return
     * @tag 2.7.0
     */
    @PostMapping("/black/config/query")
    public Result<BlackConfigVo> blackConfigQuery() {
        BlackConfigVo blackConfigVo = userActiveRuleConfigService.blackConfigQuery();
        return CommonResult.success(blackConfigVo);
    }

    /**
     * 黑名单过滤配置变更
     *
     * @return
     * @tag 2.7.0
     */
    @PostMapping("/black/config/update")
    public Result<Boolean> blackConfigUpdate(@RequestBody @Validated ActiveRuleBlackConfigReq activeRuleBlackConfigReq) {
        boolean result = userActiveRuleConfigService.blackConfigUpdate(activeRuleBlackConfigReq);
        return CommonResult.success(result);
    }
}
