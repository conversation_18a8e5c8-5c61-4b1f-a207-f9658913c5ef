package com.linzi.pitpat.admin.equipment.manager;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.exception.AdminCommonError;
import com.linzi.pitpat.admin.model.TreadmillAudioVo;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillAudio;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillAudioQuery;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TreadmillAudioManager {

    @Resource
    private TreadmillAudioService treadmillAudioService;


    public Page<TreadmillAudioVo> listAudio(TreadmillAudioQuery queryPo) {
        TreadmillAudioQuery query = BeanUtil.copyBean(queryPo, TreadmillAudioQuery.class);
        query.setIsListQuery(true);
        Page<TreadmillAudio> page = treadmillAudioService.findPage(query);
        Page<TreadmillAudioVo> pageVo = new Page<>();
        List<TreadmillAudioVo> voList = page.getRecords().stream().map(e -> {
            TreadmillAudioVo treadmillAudioVo = BeanUtil.copyBean(e, TreadmillAudioVo.class);
            if (Objects.isNull(treadmillAudioVo.getGmtModified())) {
                treadmillAudioVo.setGmtModified(e.getGmtCreate());
            }
            if (!StringUtils.hasText(treadmillAudioVo.getModifier())) {
                treadmillAudioVo.setModifier(e.getCreator());
            }
            return treadmillAudioVo;
        }).collect(Collectors.toList());
        pageVo.setRecords(voList);
        pageVo.setTotal(page.getTotal());
        return pageVo;
    }


    public TreadmillAudioVo getById(Long id) {
        TreadmillAudio treadmillAudio = treadmillAudioService.getById(id);
        if (Objects.isNull(treadmillAudio)) {
            return null;
        }
        TreadmillAudioVo treadmillAudioVo = BeanUtil.copyBean(treadmillAudio, TreadmillAudioVo.class);
        treadmillAudioVo.setLanguageName(I18nConstant.LanguageCodeEnum.findByCode(treadmillAudioVo.getLanguageCode()).getName());
        return treadmillAudioVo;
    }


    public void save(TreadmillAudioVo vo) {
        String username = SecurityUtils.getUsername();
        String audioName = vo.getAudioName();
        TreadmillAudioQuery query = new TreadmillAudioQuery();
        query.setAudioName(audioName);
        TreadmillAudio oldAudio = treadmillAudioService.getByQuery(query);
        if (Objects.nonNull(vo.getId())) {
            //修改
            if (Objects.nonNull(oldAudio) && !vo.getId().equals(oldAudio.getId())) {
                log.info("音频名已存在,audioName:{}", audioName);
                throw new BaseException(AdminCommonError.NAME_ALREADY_EXISTS.getMsg(), AdminCommonError.NAME_ALREADY_EXISTS.getCode());
            }
            TreadmillAudio treadmillAudio = new TreadmillAudio();
            treadmillAudio.setId(vo.getId());
            treadmillAudio.setAudioName(audioName);
            treadmillAudio.setAudioContent(vo.getAudioContent());
            treadmillAudio.setAudioRule(vo.getAudioRule());
            treadmillAudio.setGmtModified(ZonedDateTime.now());
            treadmillAudio.setModifier(username);
            treadmillAudioService.update(treadmillAudio);
        } else {
            //新增
            if (Objects.nonNull(oldAudio)) {
                log.info("音频名已存在,audioName:{}", audioName);
                throw new BaseException(AdminCommonError.NAME_ALREADY_EXISTS.getMsg(), AdminCommonError.NAME_ALREADY_EXISTS.getCode());
            }
            TreadmillAudio treadmillAudio = BeanUtil.copyBean(vo, TreadmillAudio.class);
            treadmillAudio.setCreator(username);
            treadmillAudio.setGmtCreate(ZonedDateTime.now());
            treadmillAudioService.insert(treadmillAudio);
        }
    }


}
