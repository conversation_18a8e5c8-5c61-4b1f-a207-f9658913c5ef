package com.linzi.pitpat.admin.bussiness;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.model.LoginUser;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.userservice.dto.request.UserBlackListQueryReq;
import com.linzi.pitpat.data.userservice.dto.request.UserFreezeReq;
import com.linzi.pitpat.data.userservice.model.entity.UserBlacklist;
import com.linzi.pitpat.data.userservice.model.entity.UserBlacklistLog;
import com.linzi.pitpat.data.userservice.model.entity.UserStatusFreezeRecord;
import com.linzi.pitpat.data.userservice.model.entity.UserStatusFreezeRecordLog;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserBlacklistLogService;
import com.linzi.pitpat.data.userservice.service.UserBlacklistService;
import com.linzi.pitpat.data.userservice.service.UserStatusFreezeRecordLogService;
import com.linzi.pitpat.data.userservice.service.UserStatusFreezeRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.user.UserBlackListVo;
import com.linzi.pitpat.data.vo.user.UserFreezeListVo;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 用户管理业务
 */
@Service
@Slf4j
public class UserManagerBusiness implements UserManagerBusinessApi {

    @Autowired
    private UserBlacklistService userBlacklistService;

    @Autowired
    private UserStatusFreezeRecordService userStatusFreezeRecordService;

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private TencentImUtil tencentImUtil;

    @Autowired
    private UserStatusFreezeRecordLogService userStatusFreezeRecordLogService;

    @Autowired
    private UserBlacklistLogService userBlacklistLogService;

    public Page<UserFreezeListVo> freezeListQuery(UserBlackListQueryReq userBlackListQueryReq) {
        Page page = new Page<>(userBlackListQueryReq.getPageNum(), userBlackListQueryReq.getPageSize());
        Page<UserFreezeListVo> userFreezeVoPage = userStatusFreezeRecordService.freezeListQueryPage(page, userBlackListQueryReq);
        return userFreezeVoPage;
    }

    public Page<UserBlackListVo> blackListQuery(UserBlackListQueryReq userBlackListQueryReq) {
        Page page = new Page<>(userBlackListQueryReq.getPageNum(), userBlackListQueryReq.getPageSize());
        Page<UserBlackListVo> userFreezeVoPage = userBlacklistService.blackListQuery(page, userBlackListQueryReq);
        return userFreezeVoPage;
    }

    @Transactional
    public Result freezeByUserId(UserFreezeReq userFreezeReq, LoginUser loginUser) {
        ZnsUserEntity userByEmail = znsUserService.findById(userFreezeReq.getUserId());
        userByEmail.setUserStatus(userFreezeReq.getStatus());
        znsUserService.update(userByEmail);
        boolean save = false;
        if (YesNoStatus.YES.getCode().equals(userFreezeReq.getStatus())) {
            UserStatusFreezeRecord userStatusFreezeRecord = userStatusFreezeRecordService.getUserStatusFreezeRecord(userByEmail.getId());
            if (Objects.isNull(userStatusFreezeRecord)) {
                userStatusFreezeRecord = new UserStatusFreezeRecord();
                userStatusFreezeRecord.setEmailAddress(userFreezeReq.getEmail());
                userStatusFreezeRecord.setRemark(userFreezeReq.getRemark());
                userStatusFreezeRecord.setUserId(userByEmail.getId());
                userStatusFreezeRecord.setSystemUserName(loginUser.getUsername());
                userStatusFreezeRecord.setSystemUserId(loginUser.getUser().getUserId());
                save = userStatusFreezeRecordService.insert(userStatusFreezeRecord);
                addFreezeRecordLog(userFreezeReq, loginUser, userStatusFreezeRecord, userByEmail, YesNoStatus.YES.getCode());
            } else {
                userStatusFreezeRecord.setFreezeTime(userStatusFreezeRecord.getFreezeTime() + 1);
                userStatusFreezeRecord.setIsDelete(YesNoStatus.NO.getCode());
                userStatusFreezeRecord.setRemark(userFreezeReq.getRemark());
                userStatusFreezeRecord.setSystemUserId(loginUser.getUser().getUserId());
                userStatusFreezeRecord.setSystemUserName(loginUser.getUsername());
                save = userStatusFreezeRecordService.update(userStatusFreezeRecord);
                addFreezeRecordLog(userFreezeReq, loginUser, userStatusFreezeRecord, userByEmail, YesNoStatus.YES.getCode());
            }
        } else {
            UserStatusFreezeRecord userStatusFreezeRecord = userStatusFreezeRecordService.getUserStatusFreezeRecord(userByEmail.getId());
            userStatusFreezeRecord.setIsDelete(YesNoStatus.YES.getCode());
            userStatusFreezeRecord.setGmtModified(ZonedDateTime.now());
            userStatusFreezeRecord.setRemark(userFreezeReq.getRemark());
            userStatusFreezeRecord.setSystemUserId(loginUser.getUser().getUserId());
            userStatusFreezeRecord.setSystemUserName(loginUser.getUsername());
            save = userStatusFreezeRecordService.update(userStatusFreezeRecord);
            userStatusFreezeRecordService.deleteById(userStatusFreezeRecord.getId());
            String key = RedisConstants.USER_FREEZE_HOMEPAGE_POP + userStatusFreezeRecord.getUserId();
            redisUtil.delete(key);
            addFreezeRecordLog(userFreezeReq, loginUser, userStatusFreezeRecord, userByEmail, YesNoStatus.NO.getCode());
        }
        return CommonResult.success(save);
    }

    /**
     * 冻结操作记录add
     *
     * @param userFreezeReq
     * @param loginUser
     * @param userStatusFreezeRecord
     * @param userByEmail
     * @param type
     */
    private void addFreezeRecordLog(UserFreezeReq userFreezeReq, LoginUser loginUser, UserStatusFreezeRecord userStatusFreezeRecord, ZnsUserEntity userByEmail, Integer type) {
        UserStatusFreezeRecordLog userStatusFreezeRecordLog = new UserStatusFreezeRecordLog();
        userStatusFreezeRecordLog.setFreezeId(userStatusFreezeRecord.getId());
        userStatusFreezeRecordLog.setRemark(userFreezeReq.getRemark());
        userStatusFreezeRecordLog.setFreezeTime(userStatusFreezeRecord.getFreezeTime());
//        userStatusFreezeRecordLog.setEmailAddress(userFreezeReq.getEmail());
        userStatusFreezeRecordLog.setUserId(userByEmail.getId());
        userStatusFreezeRecordLog.setSystemUserName(loginUser.getUsername());
        userStatusFreezeRecordLog.setType(type);
        userStatusFreezeRecordLogService.insert(userStatusFreezeRecordLog);
    }

    public Result blackAddUser(UserFreezeReq userFreezeReq, LoginUser loginUser) {
        ZnsUserEntity userByEmail = znsUserService.findById(userFreezeReq.getUserId());
        boolean save;

        UserBlacklist userBlacklist = userBlacklistService.getUserBlacklist(userByEmail.getId());
        if (YesNoStatus.YES.getCode().equals(userFreezeReq.getStatus())) {
            if (Objects.isNull(userBlacklist)) {
                userBlacklist = new UserBlacklist();
                userBlacklist.setUserId(userByEmail.getId());
                userBlacklist.setRemark(userFreezeReq.getRemark());
                userBlacklist.setSystemUserName(loginUser.getUser().getUserName());
                userBlacklist.setSystemUserId(loginUser.getUser().getUserId());
                save = userBlacklistService.insert(userBlacklist);
                addBlackListLog(YesNoStatus.YES, userBlacklist, userFreezeReq, userByEmail);
            } else {
                userBlacklist.setIsDelete(YesNoStatus.NO.getCode());
                userBlacklist.setSystemUserName(loginUser.getUser().getUserName());
                userBlacklist.setSystemUserId(loginUser.getUser().getUserId());
                userBlacklist.setRemark(userFreezeReq.getRemark());
                save = userBlacklistService.update(userBlacklist);
                addBlackListLog(YesNoStatus.YES, userBlacklist, userFreezeReq, userByEmail);
            }
            tencentImUtil.setNoSpeaking(userByEmail.getId().toString(), 4294967295l, 4294967295l);
        } else {
            userBlacklist.setIsDelete(YesNoStatus.YES.getCode());
            userBlacklist.setRemark(userFreezeReq.getRemark());
            userBlacklist.setSystemUserName(loginUser.getUser().getUserName());
            userBlacklist.setSystemUserId(loginUser.getUser().getUserId());
            save = userBlacklistService.update(userBlacklist);
            userBlacklistService.deleteById(userBlacklist.getId());
            addBlackListLog(YesNoStatus.NO, userBlacklist, userFreezeReq, userByEmail);
            tencentImUtil.setNoSpeaking(userByEmail.getId().toString(), 0l, 0l);
        }
        return CommonResult.success(save);
    }

    /**
     * 黑名单操作记录add
     *
     * @param yes
     * @param userBlacklist
     * @param userFreezeReq
     * @param userByEmail
     */
    private void addBlackListLog(YesNoStatus yes, UserBlacklist userBlacklist, UserFreezeReq userFreezeReq, ZnsUserEntity userByEmail) {
        UserBlacklistLog userBlacklistLog = new UserBlacklistLog();
        userBlacklistLog.setType(yes.getCode());
        userBlacklistLog.setBlackId(userBlacklist.getId());
        userBlacklistLog.setRemark(userFreezeReq.getRemark());
        userBlacklistLog.setUserId(userByEmail.getId());
        userBlacklistLog.setSystemUserName(userBlacklist.getSystemUserName());
        userBlacklistLog.setSystemUserId(userBlacklist.getSystemUserId());
        userBlacklistLogService.insert(userBlacklistLog);
    }
}
