package com.linzi.pitpat.admin.equipment.manager;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.exception.AdminCommonError;
import com.linzi.pitpat.admin.model.Dto.request.TreadmillSoundpackDeleteHistoryReq;
import com.linzi.pitpat.admin.model.Dto.request.TreadmillSoundpackDownloadReq;
import com.linzi.pitpat.admin.model.Dto.request.TreadmillSoundpackPublishReq;
import com.linzi.pitpat.admin.model.Dto.response.TreadmillSoundpackUpdateResp;
import com.linzi.pitpat.admin.model.TreadmillAudioVo;
import com.linzi.pitpat.admin.model.TreadmillSoundpackHistoryVo;
import com.linzi.pitpat.admin.model.TreadmillSoundpackVo;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillAudio;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillAudioSoundpackRel;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillSoundpack;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillSoundpackHistory;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillAudioSoundpackRelQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillSoundpackHistoryQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillSoundpackQuery;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioSoundpackRelService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackHistoryService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
@Slf4j
public class TreadmillSoundpackManager {

    @Resource
    private TreadmillSoundpackService treadmillSoundpackService;

    @Resource
    private TreadmillAudioService treadmillAudioService;

    @Resource
    private TreadmillAudioSoundpackRelService treadmillAudioSoundpackRelService;

    @Resource
    private TreadmillSoundpackHistoryService treadmillSoundpackHistoryService;


    public Page<TreadmillSoundpackVo> listSoundpack(TreadmillSoundpackQuery queryPo) {
        TreadmillSoundpackQuery query = BeanUtil.copyBean(queryPo, TreadmillSoundpackQuery.class);
        Page<TreadmillSoundpack> page = treadmillSoundpackService.findPage(query);
        Page<TreadmillSoundpackVo> pageVo = new Page<>();
        pageVo.setTotal(page.getTotal());
        List<TreadmillSoundpack> records = page.getRecords();
        List<TreadmillSoundpackVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            for (TreadmillSoundpack record : records) {
                TreadmillSoundpackVo vo = BeanUtil.copyBean(record, TreadmillSoundpackVo.class);
                vo.setLanguageName(I18nConstant.LanguageCodeEnum.findByCode(record.getLanguageCode()).getName());
                if (Objects.isNull(vo.getGmtModified())) {
                    vo.setGmtModified(record.getGmtCreate());
                }
                if (!StringUtils.hasText(vo.getModifier())) {
                    vo.setModifier(record.getCreator());
                }
                if (Objects.nonNull(record.getLatestVersion())) {
                    TreadmillAudioSoundpackRelQuery relQuery = TreadmillAudioSoundpackRelQuery.builder().packageId(record.getId()).version(record.getLatestVersion()).build();
                    List<TreadmillAudioSoundpackRel> relList = treadmillAudioSoundpackRelService.findList(relQuery);
                    vo.setAudioNum(relList.size());
                }
                voList.add(vo);
            }
        }
        pageVo.setRecords(voList);
        return pageVo;
    }


    public TreadmillSoundpackVo getById(Long id) {
        TreadmillSoundpack treadmillSoundpack = treadmillSoundpackService.getById(id);
        if (Objects.isNull(treadmillSoundpack)) {
            return null;
        }
        TreadmillSoundpackVo vo = new TreadmillSoundpackVo();
        BeanUtils.copyProperties(treadmillSoundpack, vo);
        vo.setLanguageName(I18nConstant.LanguageCodeEnum.findByCode(vo.getLanguageCode()).getName());
        TreadmillSoundpackHistoryQuery query = TreadmillSoundpackHistoryQuery.builder().packageId(id).build();
        TreadmillSoundpackHistory history = treadmillSoundpackHistoryService.getByQuery(query);
        vo.setRemark(history.getRemark());
        fillAudioData(vo, history.getVersion());
        return vo;
    }


    /**
     * 填充音频数据
     *
     * @param vo
     * @param version
     */
    private void fillAudioData(TreadmillSoundpackVo vo, Integer version) {
        TreadmillAudioSoundpackRelQuery relQuery = TreadmillAudioSoundpackRelQuery.builder().packageId(vo.getId()).version(version).build();
        List<TreadmillAudioSoundpackRel> relList = treadmillAudioSoundpackRelService.findList(relQuery);
        List<Long> audioIdList = relList.stream().map(TreadmillAudioSoundpackRel::getAudioId).collect(Collectors.toList());
        Map<Long, Integer> audioIdMap = relList.stream().collect(Collectors.toMap(TreadmillAudioSoundpackRel::getAudioId, TreadmillAudioSoundpackRel::getOrderNumber));
        List<TreadmillAudio> audioList = treadmillAudioService.findByIds(audioIdList);

        List<TreadmillAudioVo> audioVoList = audioList.stream().map(e -> {
            TreadmillAudioVo treadmillAudioVo = BeanUtil.copyBean(e, TreadmillAudioVo.class);
            treadmillAudioVo.setOrderNumber(audioIdMap.get(e.getId()));
            return treadmillAudioVo;
        }).sorted(Comparator.comparing(TreadmillAudioVo::getOrderNumber)).collect(Collectors.toList());
        vo.setAudioVoList(audioVoList);
    }


    @Transactional(rollbackFor = Exception.class)
    public TreadmillSoundpackUpdateResp save(TreadmillSoundpackVo vo) {
        TreadmillSoundpackUpdateResp resp = new TreadmillSoundpackUpdateResp();
        resp.setFlag(false);
        String username = SecurityUtils.getUsername();
        String packageName = vo.getPackageName();
        Long packageId = vo.getId();
        TreadmillSoundpackQuery query = new TreadmillSoundpackQuery();
        query.setPackageName(packageName);
        TreadmillSoundpack oldTreadmillSoundpack = treadmillSoundpackService.getByQuery(query);
        Integer version;
        if (Objects.nonNull(packageId)) {
            //修改
            if (Objects.nonNull(oldTreadmillSoundpack) && !packageId.equals(oldTreadmillSoundpack.getId())) {
                log.info("音频包名已存在,packageName:{}", packageName);
                throw new BaseException(AdminCommonError.NAME_ALREADY_EXISTS.getMsg(), AdminCommonError.NAME_ALREADY_EXISTS.getCode());
            }
            TreadmillSoundpackHistoryQuery historyQuery = TreadmillSoundpackHistoryQuery.builder().packageId(packageId).build();
            TreadmillSoundpackHistory soundpackHistory = treadmillSoundpackHistoryService.getByQuery(historyQuery);
            Integer latestVersion = soundpackHistory.getVersion();
            version = latestVersion + 1;
            TreadmillSoundpack treadmillSoundpack = treadmillSoundpackService.getById(packageId);
            treadmillSoundpack.setGmtModified(ZonedDateTime.now());
            treadmillSoundpack.setModifier(username);
            treadmillSoundpackService.update(treadmillSoundpack);
        } else {
            //新增
            if (Objects.nonNull(oldTreadmillSoundpack)) {
                log.info("音频包名已存在,packageName:{}", packageName);
                throw new BaseException(AdminCommonError.NAME_ALREADY_EXISTS.getMsg(), AdminCommonError.NAME_ALREADY_EXISTS.getCode());
            }
            version = 1;
            TreadmillSoundpack treadmillSoundpack = BeanUtil.copyBean(vo, TreadmillSoundpack.class);
            treadmillSoundpack.setCreator(username);
            treadmillSoundpackService.insert(treadmillSoundpack);
            packageId = treadmillSoundpack.getId();
        }
        insertHistoryAndAudioRel(vo, username, packageId, version);
        resp.setFlag(true);
        return resp;
    }


    private void insertHistoryAndAudioRel(TreadmillSoundpackVo vo, String username, Long packageId, Integer version) {
        TreadmillSoundpackHistory treadmillSoundpackHistory = new TreadmillSoundpackHistory();
        treadmillSoundpackHistory.setPackageId(packageId);
        treadmillSoundpackHistory.setUsageState(YesNoStatus.NO.getCode());
        treadmillSoundpackHistory.setVersion(version);
        treadmillSoundpackHistory.setRemark(vo.getRemark());
        treadmillSoundpackHistory.setCreator(username);
        treadmillSoundpackHistoryService.insert(treadmillSoundpackHistory);
        List<TreadmillAudioVo> audioVoList = vo.getAudioVoList();
        List<TreadmillAudioSoundpackRel> collect = new ArrayList<>();
        for (TreadmillAudioVo e : audioVoList) {
            TreadmillAudioSoundpackRel rel = new TreadmillAudioSoundpackRel();
            rel.setPackageId(packageId);
            rel.setVersion(version);
            rel.setAudioId(e.getId());
            rel.setOrderNumber(e.getOrderNumber());
            rel.setCreator(username);
            collect.add(rel);
        }
        treadmillAudioSoundpackRelService.batchInsert(collect);
    }


    @Transactional(rollbackFor = Exception.class)
    public TreadmillSoundpackUpdateResp publishSoundpack(TreadmillSoundpackPublishReq req) {
        String username = SecurityUtils.getUsername();
        ZonedDateTime date = ZonedDateTime.now();
        TreadmillSoundpackHistory history = treadmillSoundpackHistoryService.getById(req.getHistoryId());
        Long packageId = history.getPackageId();
        Integer version = history.getVersion();
        TreadmillSoundpack treadmillSoundpack = treadmillSoundpackService.getById(packageId);
        history.setUsageState(YesNoStatus.YES.getCode());
        history.setModifier(username);
        history.setGmtModified(date);
        boolean i1 = treadmillSoundpackHistoryService.update(history) > 0 ? true : false;
        boolean i2 = true;
        if (Objects.isNull(treadmillSoundpack.getLatestVersion())
                || treadmillSoundpack.getLatestVersion() < version) {
            // 没有版本上架过 或者 要上架版本大于包的版本
            treadmillSoundpack.setLatestVersion(version);
            treadmillSoundpack.setGmtModified(date);
            treadmillSoundpack.setModifier(username);
            i2 = treadmillSoundpackService.update(treadmillSoundpack) > 0 ? true : false;
        }
        TreadmillSoundpackUpdateResp resp = new TreadmillSoundpackUpdateResp();
        resp.setFlag(i1 && i2);
        return resp;
    }


    public List<TreadmillSoundpackHistoryVo> listHistory(Long id) {
        TreadmillSoundpackHistoryQuery query = TreadmillSoundpackHistoryQuery.builder().packageId(id).build();
        List<TreadmillSoundpackHistory> list = treadmillSoundpackHistoryService.findList(query);
        List<TreadmillSoundpackHistoryVo> voList = list.stream().map(e -> {
            TreadmillSoundpackHistoryVo historyVo = BeanUtil.copyBean(e, TreadmillSoundpackHistoryVo.class);
            if (Objects.isNull(historyVo.getGmtModified())) {
                historyVo.setGmtModified(e.getGmtCreate());
            }
            return historyVo;
        }).collect(Collectors.toList());
        return voList;
    }


    public String downloadSoundpack(TreadmillSoundpackDownloadReq req) {
        String zipUrl = null;
        Long packageId = req.getPackageId();
        Integer version = req.getVersion();
        TreadmillSoundpackHistoryQuery query = TreadmillSoundpackHistoryQuery.builder().packageId(packageId).version(version).build();
        TreadmillSoundpackHistory soundpackHistory = treadmillSoundpackHistoryService.getByQuery(query);
        if (Objects.nonNull(soundpackHistory) && StringUtils.hasText(soundpackHistory.getPackageUrl())) {
            return soundpackHistory.getPackageUrl();
        }
        TreadmillSoundpack soundpack = treadmillSoundpackService.getById(packageId);
        TreadmillAudioSoundpackRelQuery relQuery = TreadmillAudioSoundpackRelQuery.builder().packageId(packageId).version(version).build();
        List<TreadmillAudioSoundpackRel> list = treadmillAudioSoundpackRelService.findList(relQuery);
        Map<Long, Integer> audioIdMap = list.stream().collect(Collectors.toMap(TreadmillAudioSoundpackRel::getAudioId, TreadmillAudioSoundpackRel::getOrderNumber));
        List<Long> audioIdList = list.stream().map(TreadmillAudioSoundpackRel::getAudioId).collect(Collectors.toList());
        List<TreadmillAudio> audioList = treadmillAudioService.findByIds(audioIdList);
        try {
            String zipFileName = soundpack.getPackageName() + soundpackHistory.getVersion() + ".zip";
            zipUrl = uploadZip2Aws(zipFileName, audioIdMap, audioList);
        } catch (Exception e) {
            log.error("文件下载失败，packageId：{}，version：{}，异常：", packageId, version, e);
        }
        if (StringUtils.hasText(zipUrl)) {
            soundpackHistory.setPackageUrl(zipUrl);
            treadmillSoundpackHistoryService.update(soundpackHistory);
        }
        return zipUrl;
    }


    /**
     * 下载音频并压缩上传到aws
     *
     * @param zipFileName
     * @param audioIdMap
     * @param audioList
     * @return
     * @throws Exception
     */
    private String uploadZip2Aws(String zipFileName, Map<Long, Integer> audioIdMap, List<TreadmillAudio> audioList) throws Exception {
        Path zipFilePath = Paths.get(zipFileName);
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFilePath.toFile()))) {
            // 下载文件并保存到压缩文件
            for (TreadmillAudio audio : audioList) {
                URL url = new URL(audio.getUrl());
                // 序号+音频名称。如：1aaa.wav
                String fileName = audioIdMap.get(audio.getId()) + audio.getAudioName() + audio.getFileName().substring(audio.getFileName().lastIndexOf("."));
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                try (InputStream inputStream = url.openStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zipOutputStream.write(buffer, 0, bytesRead);
                    }
                }
                zipOutputStream.closeEntry();
            }
        }
        // 将压缩文件转换为byte[]
        byte[] fileBytes = Files.readAllBytes(zipFilePath);
        String fileFolder = "soundpack";
        String zipUrl = AwsUtil.upload2Cloud(fileBytes, zipFileName, fileFolder);
        return zipUrl;
    }


    @Transactional(rollbackFor = Exception.class)
    public TreadmillSoundpackUpdateResp deleteHistory(TreadmillSoundpackDeleteHistoryReq req) {
        String username = SecurityUtils.getUsername();
        TreadmillSoundpackUpdateResp resp = new TreadmillSoundpackUpdateResp();
        resp.setFlag(false);
        TreadmillSoundpackHistory history = treadmillSoundpackHistoryService.findById(req.getHistoryId());
        Long packageId = history.getPackageId();
        Integer version = history.getVersion();
        TreadmillSoundpack treadmillSoundpack = treadmillSoundpackService.getById(packageId);
        if (Objects.isNull(treadmillSoundpack) || YesNoStatus.YES.getCode().equals(history.getUsageState())) {
            log.info("音频包历史版本不允许删除，historyId:{}", req.getHistoryId());
            return resp;
        }
        TreadmillSoundpackHistoryQuery query = TreadmillSoundpackHistoryQuery.builder().packageId(packageId).build();
        List<TreadmillSoundpackHistory> list = treadmillSoundpackHistoryService.findList(query);
        if (list.size() == 1) {
            // 只剩一个版本
            treadmillSoundpackService.delete(treadmillSoundpack.getId(), username);
        }
        treadmillAudioSoundpackRelService.deleteByPackageIdAndVersion(packageId, version);
        resp.setFlag(treadmillSoundpackHistoryService.deleteById(req.getHistoryId()) > 0);
        return resp;
    }


}
