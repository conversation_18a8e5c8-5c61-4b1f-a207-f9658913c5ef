package com.linzi.pitpat.admin.model;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class CustomerAnswerConfigVo {

    private Long id;
    //问题类别名称
    private String title;
    //序号
    private Integer orderNumber;
    //开启状态：0-未开启，1-已开启
    private Integer openStatus;
    //默认语言code
    private String defaultLangCode;
    //创建时间
    private ZonedDateTime gmtCreate;


}
