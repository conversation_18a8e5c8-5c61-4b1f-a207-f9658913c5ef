package com.linzi.pitpat.admin.model.Dto.request.vip;


import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/1/16 16:06
 */
@Data
@NoArgsConstructor
public class VipPassRemindDetailRequestDto {
    /**
     * 提醒类型：提醒类型：1-首页弹窗；2-push；3-站内信；4-邮件
     */
    @NotNull
    private Integer remindType;

    /**
     * 内测/公开 0-内测 1-公开
     */
    @NotNull
    private Integer betaVersion;
}
