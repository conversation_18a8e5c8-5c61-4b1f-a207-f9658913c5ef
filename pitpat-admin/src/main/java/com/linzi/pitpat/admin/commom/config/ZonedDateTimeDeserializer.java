package com.linzi.pitpat.admin.commom.config;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
class ZonedDateTimeDeserializer extends JsonDeserializer<ZonedDateTime> {

    private static final long serialVersionUID = 1L;
    private final DateTimeFormatter formatter;

    public ZonedDateTimeDeserializer(DateTimeFormatter formatter) {
        this.formatter = formatter;
    }

    @Override
    public ZonedDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 读取字符串并将其解析为 Asia/Shanghai 时区的时间
        try {
            long dateTimeStr = p.getValueAsLong();
            //兼容 admin 空值
            if(dateTimeStr == 0){
                return null;
            }
            ZonedDateTime shanghaiTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(dateTimeStr), ZoneId.of("UTC"));
            //.parse(dateTimeStr, formatter.withZone(ZoneId.of("Asia/Shanghai")));
            // 将时间转换为 UTC 时区
            //return shanghaiTime.withZoneSameInstant(ZoneId.of("UTC"));
            return shanghaiTime;
        } catch (Exception e) {
            log.error("解析 ZonedDateTime 失败,", e);
        }
        return null;
    }
}
