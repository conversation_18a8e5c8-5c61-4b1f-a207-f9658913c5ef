package com.linzi.pitpat.admin.activityservice.controller;


import com.linzi.pitpat.admin.activityservice.converter.EntryGameplayDtoConvert;
import com.linzi.pitpat.admin.activityservice.dto.request.EntryGameplayRequestDto;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.dto.console.FreeActivityDto;
import com.linzi.pitpat.data.activityservice.dto.console.FreeActivityTaskDto;
import com.linzi.pitpat.data.activityservice.dto.console.RouteDto;
import com.linzi.pitpat.data.activityservice.dto.console.RunTemplateDto;
import com.linzi.pitpat.data.activityservice.manager.FreeActivityManager;
import com.linzi.pitpat.data.activityservice.model.dto.EntryGameplayDto;
import com.linzi.pitpat.data.activityservice.service.RunTemplateService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.vo.RunRouteVO;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/freeActivity")
@Slf4j
@RestController
@RequiredArgsConstructor
public class FreeActivityController {


    private final FreeActivityManager freeActivityManager;

    private final ZnsRunRouteService runRouteService;

    private final RunTemplateService runTemplateService;

    /**
     * 创建编辑
     *
     * @param dto
     * @return
     */
    @PostMapping("/create")
    public Result create(@RequestBody FreeActivityDto dto) {
        freeActivityManager.edit(dto);
        return CommonResult.success();
    }


    /**
     * 回显
     *
     * @param activityId
     * @return
     */
    @GetMapping("/detail")
    public Result<FreeActivityDto> detail(Long activityId) {

        return CommonResult.success(freeActivityManager.getByActivityId(activityId));
    }


    /**
     * 查询地图
     *
     * @param
     * @return
     */
    @GetMapping("/getAllRoute")
    public Result<List<RunRouteVO>> getAllRoute(@RequestParam String mode) {

        return CommonResult.success(runRouteService.getFreeActivityRoute(mode));
    }

    /**
     * 查询速度模板
     *
     * @param
     * @return
     */
    @GetMapping("/getRunTemplate")
    public Result<List<RunTemplateDto>> getRunTemplate() {

        return CommonResult.success(runTemplateService.getAll());
    }
    /**
     * 列表
     *
     * @param
     * @return
     */
    @GetMapping("/list")
    public Result<List<FreeActivityTaskDto>> list() {

        return CommonResult.success(freeActivityManager.taskList());
    }


}
