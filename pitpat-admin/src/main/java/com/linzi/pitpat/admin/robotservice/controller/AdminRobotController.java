package com.linzi.pitpat.admin.robotservice.controller;


import com.linzi.pitpat.admin.commom.service.TokenService;
import com.linzi.pitpat.admin.model.LoginUser;
import com.linzi.pitpat.admin.robotservice.converter.RobotRunModeConverter;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.SpeedUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunRouteDao;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataEveryEverySecond;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.resp.RunActivityUserResp;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityResp;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataEveryEverySecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.entity.activity.RunPopList;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.enums.SocketEventEnums;
import com.linzi.pitpat.data.request.RobotActivityRequest;
import com.linzi.pitpat.data.robotservice.manager.RobotRunPlanManager;
import com.linzi.pitpat.data.robotservice.mapper.RobotRunPlanDao;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunMode;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunPlan;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunPlanQuery;
import com.linzi.pitpat.data.robotservice.service.RobotRunModeService;
import com.linzi.pitpat.data.robotservice.service.RobotRunPlanQueryService;
import com.linzi.pitpat.data.robotservice.service.RobotRunPlanService;
import com.linzi.pitpat.data.service.activity.RunPopListService;
import com.linzi.pitpat.data.service.activity.SysUserRobotService;
import com.linzi.pitpat.data.systemservice.mapper.SysUserMapper;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.model.entity.SysUserRobot;
import com.linzi.pitpat.data.systemservice.model.vo.PopVo;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.vo.SocketAllMsgVo;
import com.linzi.pitpat.dto.request.RobotPlanRunEndRequestDto;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import com.linzi.pitpat.framework.web.util.ServletUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping({"/robot", "/test/robot"})
@RequiredArgsConstructor
public class AdminRobotController {


    @Autowired
    private SysUserRobotService sysUserRobotService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ZnsUserService znsUserService;
    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;
    @Autowired
    private TokenService tokenService;

    @Autowired
    private RunPopListService runPopListService;

    @Value("${user.password.aes}")
    public String aesKey;

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Value("${admin.server.push}")
    private String pushUrl;


    @Value("${admin.server.gamepush}")
    private String gamepush;


    @Autowired
    private UserRunDataEveryEverySecondService userRunDataEveryEverySecondService;
    @Autowired
    private RobotRunPlanService robotRunPlanService;

    @Autowired
    private MindUserMatchService mindUserMatchService;

    @Autowired
    private SocketPushUtils socketPushUtils;

    @Autowired
    private ZnsUserAddService znsUserAddService;

    @Autowired
    private RobotRunPlanDao robotRunPlanDao;

    @Autowired
    private RobotRunPlanQueryService robotRunPlanQueryService;

    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Autowired
    private ZnsRunRouteDao znsRunRouteDao;
    private final RobotRunModeService robotRunModeService;
    private final RobotRunModeConverter robotRunModeConverter;
    private final RobotRunPlanManager robotRunPlanManager;

    @PostMapping("/createOrUpdate")
    @Log(title = "机器人管理", businessType = BusinessType.INSERT)
    public Result create(@RequestBody UserRequest userRequest) {
        userRequest.setUuid(OrderUtil.getUserPoolOrder());
        userRequest.setIsRobot(1);
        ZnsUserEntity user = znsUserService.createUserOrUpdate(userRequest, 9999);
        SysUserRobot sysUserRobot = sysUserRobotService.selectSysUserRobotByUserId(user.getId(), userRequest.getSysUserId());
        if (sysUserRobot == null) {
            sysUserRobot = new SysUserRobot();
        }
        sysUserRobot.setUserId(user.getId());
        sysUserRobot.setUserName(user.getFirstName());
        SysUser sysUser = sysUserMapper.selectUserById(userRequest.getSysUserId());
        sysUserRobot.setSysUserId(sysUser.getUserId());
        sysUserRobot.setSysUserName(sysUser.getNickName());
        sysUserRobotService.insertOrUpdateSysUserRobot(sysUserRobot);
        return CommonResult.success();
    }

    @PostMapping("/getById")
    public Result getById(@RequestBody UserRequest userRequest) {
        return CommonResult.success(znsUserService.findById(userRequest.getId()));
    }


    @PostMapping("/sys/all")
    public Result sysAll(@RequestBody UserRequest userRequest) {
        return CommonResult.success(sysUserMapper.selectAllUser());
    }


    @PostMapping("/activity")
    public Result activityRobot(@RequestBody RobotActivityRequest userRequest) {
        SysUser user = null;
        if ("dev".equals(SpringContextUtils.getActiveProfile())) {
            user = sysUserMapper.selectUserById(1l);
        } else {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            user = loginUser.getUser();
        }

        log.info("=========================" + userRequest);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(userRequest.getActivityId());
        List<ZnsRunActivityUserEntity> znsRunActivityEntityList = znsRunActivityUserService.selectUserByActivityId(userRequest.getActivityId());
        RunActivityResp runActivityResp = new RunActivityResp();
        List<RunActivityUserResp> runActivityUserResps = new ArrayList<>();
        ZonedDateTime currentDate = ZonedDateTime.now();
        if (StringUtils.hasText(userRequest.getCurrentDate())) {
            currentDate = DateUtil.formateDate(userRequest.getCurrentDate(), DateUtil.YYYY_MM_DD_HH_MM_SS);
        }
        runActivityResp.setTarget(znsRunActivityEntity.getRunMileage());
        int online = 0;
        for (ZnsRunActivityUserEntity znsRunActivityUserDto : znsRunActivityEntityList) {
            ZnsUserEntity znsUserEntity = znsUserService.findById(znsRunActivityUserDto.getUserId());
            ZnsUserAddEntity sysUserRobot = znsUserAddService.selectZnsUserAddByUserIdFollowUserId(znsUserEntity.getId(), user.getUserId());
            Integer isMyManager = sysUserRobot == null ? 0 : 1;
            // 判断是不是用户管理的机器人
            RunActivityUserResp base = new RunActivityUserResp(BigDecimal.ZERO, BigDecimal.ZERO, 0, znsUserEntity, isMyManager);
            if (Objects.equals(znsUserEntity.getIsRobot(), 1)) {                   //如果是机器人
                RunActivityUserResp runActivityUserResp = null;
                online++;
                List<RobotRunPlan> lastRobotRunPlan = robotRunPlanDao.selectRobotRunPlanByUserIdActivityIdCurrentDate(znsRunActivityUserDto.getUserId(), znsRunActivityEntity.getId(), currentDate);
                if (lastRobotRunPlan != null && lastRobotRunPlan.size() > 0) {
                    RobotRunPlan last = lastRobotRunPlan.get(lastRobotRunPlan.size() - 1);
                    // 如果活动已经结束了
                    if (currentDate.toInstant().toEpochMilli() > last.getEndTime().toInstant().toEpochMilli() && Objects.equals(last.getIsFinished(), 1)) {
                        log.info("机器人用户已经跑步结束 userId =" + znsUserEntity.getId() + ",activityId=" + userRequest.getActivityId());
                        runActivityUserResp = new RunActivityUserResp(
                                BigDecimal.ZERO,
                                new BigDecimal(last.getTargetMileage()),
                                DateUtil.betweenSecond(znsRunActivityEntity.getActivityStartTime(), last.getEndTime()),
                                znsUserEntity, isMyManager);
                    } else {
                        log.info("机器人用户跑步中 userId =" + znsUserEntity.getId() + ",activityId=" + userRequest.getActivityId());
                        BigDecimal speed = BigDecimal.ZERO;
                        BigDecimal runMileage = BigDecimal.ZERO;
                        int runSecond = 0;
                        for (int i = 0; i < lastRobotRunPlan.size(); i++) {
                            RobotRunPlan robotRunPlan = lastRobotRunPlan.get(i);
                            speed = robotRunPlan.getSpeed();
                            int currentRunSecond = robotRunPlan.getMaxV() - robotRunPlan.getMinV();
                            if (i == lastRobotRunPlan.size() - 1) {
                                currentRunSecond = DateUtil.betweenSecond(robotRunPlan.getStartTime(), currentDate);
                            }
                            Integer currRunMileage = SpeedUtil.mileage(robotRunPlan.getSpeed(), currentRunSecond);
                            runMileage = BigDecimalUtil.add(runMileage, new BigDecimal(currRunMileage));
                            runSecond = runSecond + currentRunSecond;
                        }
                        runActivityUserResp = new RunActivityUserResp(speed, runMileage, runSecond, znsUserEntity, isMyManager);
                    }
                } else {
                    log.info("没有plan数据 lastRobotRunPlan ");
                    runActivityUserResp = base;
                }
                runActivityUserResps.add(runActivityUserResp);
            } else {
                //String a = HttpUtil.doGet(pushUrl + "/websocket/checkOnLine?email=" + znsUserEntity.getEmailAddressEn(), 3000);
                String response = RestTemplateUtil.get(pushUrl + "/websocket/checkOnLine?email=" + znsUserEntity.getEmailAddressEn());
                SocketAllMsgVo socketAllMsgVo = JsonUtil.readValue(response, SocketAllMsgVo.class);
                if (Objects.equals(socketAllMsgVo.getData().getIsOnline(), 1)) {
                    online++;
                }
                RunActivityUserResp runActivityUserResp = new RunActivityUserResp();
                ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsService.selectByUserIdActivityId(znsUserEntity.getId(), userRequest.getActivityId());
                if (znsUserRunDataDetailsEntity != null && Objects.equals(znsUserRunDataDetailsEntity.getRunStatus(), 1)) { //如果跑步已经结束
                    log.info("真实用户已经跑步结束 userId =" + znsUserEntity.getId() + ",activityId=" + userRequest.getActivityId());
                    runActivityUserResp = new RunActivityUserResp(BigDecimal.ZERO, znsRunActivityEntity.getRunMileage(), znsUserRunDataDetailsEntity.getRunTime(), znsUserEntity, isMyManager);
                } else {
                    UserRunDataEveryEverySecond userRunDataEveryEverySecond = userRunDataEveryEverySecondService.selectUserRunDataEveryEverySecondByActivityIdUserIdGmtCreateOrderByIdDesc(
                            userRequest.getActivityId(), znsUserEntity.getId(), currentDate);
                    if (userRunDataEveryEverySecond != null) {
                        runActivityUserResp = new RunActivityUserResp(
                                BigDecimalUtil.divide(userRunDataEveryEverySecond.getV(), 1000),
                                new BigDecimal(userRunDataEveryEverySecond.getM()),
                                userRunDataEveryEverySecond.getR(), znsUserEntity, isMyManager);
                    } else {
                        log.info("真实用户UserRunDataEveryEverySecond 没有查询到数据 userId =" + znsUserEntity.getId() + ",activityId=" + userRequest.getActivityId());
                        RobotRunPlanQuery robotRunPlanQuery = robotRunPlanQueryService.selectRobotRunPlanQueryByActivityIdOrderByIdDesc(userRequest.getActivityId());
                        if (robotRunPlanQuery != null && StringUtils.hasText(robotRunPlanQuery.getRespContent())) {
                            log.info("真实用户RobotRunPlanQuery 已经查询到数据 userId =" + znsUserEntity.getId() + ",activityId=" + userRequest.getActivityId());
                            List<RunActivityUserResp> listResp = JsonUtil.readList(robotRunPlanQuery.getRespContent(), RunActivityUserResp.class);
                            for (RunActivityUserResp resp : listResp) {
                                if (Objects.equals(resp.getUserId(), znsUserEntity.getId())) {
                                    runActivityUserResp = resp;
                                }
                            }
                        } else {
                            log.info("真实用户RobotRunPlanQuery 没有查询到数据 userId =" + znsUserEntity.getId() + ",activityId=" + userRequest.getActivityId());
                            runActivityUserResp = base;
                        }
                    }
                }
                runActivityUserResps.add(runActivityUserResp);
            }
        }
        Collections.sort(runActivityUserResps);
        if (runActivityUserResps.size() > 0) {
            for (int i = runActivityUserResps.size() - 1; i >= 0; i--) {
                RunActivityUserResp runActivityUserResp = runActivityUserResps.get(i);
                runActivityUserResp.setRank(i + 1);     // 设置排名
            }
        }


        RobotRunPlanQuery robotRunPlanQuery = new RobotRunPlanQuery();
        robotRunPlanQuery.setLogNo(OrderUtil.getLogNo());
        robotRunPlanQuery.setActivityId(userRequest.getActivityId());
        robotRunPlanQuery.setRespContent(JsonUtil.writeString(runActivityUserResps));
        robotRunPlanQueryService.insertRobotRunPlanQuery(robotRunPlanQuery);

        runActivityResp.setCompleteRuleType(znsRunActivityEntity.getCompleteRuleType());
        runActivityResp.setNum(online);
        runActivityResp.setList(runActivityUserResps);
        return CommonResult.success(runActivityResp);
    }


    // 机器人调速度
    @PostMapping("/modify/v")
    public Result robotv(@RequestBody RobotActivityRequest userRequest) {
        //机器人的速度设定在 1～12之间
        if (userRequest.getV().compareTo(new BigDecimal(12)) > 0) {
            userRequest.setV(new BigDecimal(12));
        }
        if (userRequest.getV().compareTo(new BigDecimal(1)) < 0) {
            userRequest.setV(new BigDecimal(1));
        }

        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(userRequest.getActivityId());
        MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByActivityIdUserId(userRequest.getActivityId(), userRequest.getUserId());
        RobotRunPlan robotRunPlanLast = robotRunPlanService.selectRobotRunPlanByUserIdActivityIdOrderByIdDesc(userRequest.getUserId(), userRequest.getActivityId());
        ZonedDateTime currentDate = ZonedDateTime.now();
        if (StringUtils.hasText(userRequest.getCurrentDate())) {
            currentDate = DateUtil.formateDate(userRequest.getCurrentDate(), DateUtil.YYYY_MM_DD_HH_MM_SS);
        }
        if (currentDate.toInstant().toEpochMilli() > robotRunPlanLast.getEndTime().toInstant().toEpochMilli() && Objects.equals(robotRunPlanLast.getIsFinished(), 1)) {
            return CommonResult.fail("活动已经结束 ");
        }
        int minV = 0;
        // 转化为米/秒
        BigDecimal v = BigDecimalUtil.divide(BigDecimalUtil.multiply(userRequest.getV(), new BigDecimal(1000)), new BigDecimal(3600));
        // 计算最终剩下多少秒
        int maxV = BigDecimalUtil.divide(new BigDecimal(mindUserMatch.getTargetMileage()), v).intValue() + 1;
        // 如果已经有跑步计划了
        if (robotRunPlanLast != null) {
            // 活动开始到当前时间的秒数
            minV = DateUtil.betweenSecond(znsRunActivityEntity.getActivityStartTime(), currentDate);
            // 查询出之前所有计划
            List<RobotRunPlan> list = robotRunPlanService.selectRobotRunPlanByUserIdActivityId(robotRunPlanLast.getId(), robotRunPlanLast.getActivityId(), robotRunPlanLast.getUserId());
            int runMileage = 0;
            for (RobotRunPlan plan : list) {
                int planSecond = (plan.getMaxV() - plan.getMinV());
                runMileage += SpeedUtil.mileage(plan.getSpeed(), planSecond); // 总共跑了多少米
            }
            // 最后一次运行时间 RobotRunPlan 多少秒
            int betwwon = DateUtil.betweenSecond(robotRunPlanLast.getStartTime(), currentDate) - robotRunPlanLast.getMinV();
            // 当前运行多少米
            Integer currRunMileage = SpeedUtil.mileage(robotRunPlanLast.getSpeed(), betwwon);
            // 剩下多少米
            int remainMileage = robotRunPlanLast.getTargetMileage() - runMileage - currRunMileage;        // 还剩下多少米没有
            Integer remainSecond = BigDecimalUtil.divide(new BigDecimal(remainMileage), v).intValue() + 1;      // 计算剩下多少秒
            maxV = remainSecond + minV;
        }

        ZonedDateTime starteTime = DateUtil.addSeconds(znsRunActivityEntity.getActivityStartTime(), minV);
        if (robotRunPlanLast != null) {
            robotRunPlanLast.setEndTime(starteTime);
            robotRunPlanLast.setMaxV(minV);
            robotRunPlanLast.setIsFinished(0);
            robotRunPlanService.updateRobotRunPlanById(robotRunPlanLast);
        }

        RobotRunPlan current = robotRunPlanService.createPlan(currentDate, mindUserMatch.getId(), mindUserMatch.getUserId(), mindUserMatch.getRunMode(),
                userRequest.getActivityId(),
                minV, maxV
                , starteTime,
                DateUtil.addSeconds(znsRunActivityEntity.getActivityStartTime(), maxV),
                userRequest.getV(), mindUserMatch.getTargetMileage());

        robotRunPlanManager.sendSpeedMsg(current, mindUserMatch, currentDate);
        return CommonResult.success();
    }


    //
    @PostMapping("/pop")
    public Result pop(@RequestBody RobotActivityRequest userRequest) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(userRequest.getUserId());
        PopVo popVo = new PopVo();

        popVo.setUserId(userRequest.getUserId());
        popVo.setContent(znsUserEntity.getFirstName()
                + " " + DateUtil.dateStr(ZonedDateTime.now(), DateUtil.YYYY_MM_DD_HH_MM_SS)
                + " " + userRequest.getContent());
        popVo.setNickName(znsUserEntity.getFirstName());
        popVo.setHeadPortrait(znsUserEntity.getHeadPortrait());
        RunPopList runPopList = new RunPopList();
        runPopList.setActivityId(userRequest.getActivityId());
        runPopList.setUserId(userRequest.getUserId());
        runPopList.setContent(popVo.getContent());
        runPopList.setNickName(popVo.getNickName());
        runPopList.setHeadPortrait(popVo.getHeadPortrait());
        runPopListService.insertRunPopList(runPopList);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(userRequest.getActivityId());
        ZnsRunRouteEntity znsRunRouteEntity = znsRunRouteDao.selectRunRouteById(znsRunActivityEntity.getActivityRouteId());
        if (Objects.equals(znsRunRouteEntity.getRouteType(), 2)) { //进入3D
            GamePushUtils.pop(gamepush, znsRunActivityEntity.getId(), popVo.getContent());
        } else {
            socketPushUtils.push(userRequest.getActivityId(), SocketEventEnums.ROBOT_POP_MESSAGE.getCode(), BeanUtil.beanToMap(popVo));
        }
        return CommonResult.success();
    }


    @PostMapping("/poplist")
    public Result poplist(@RequestBody RobotActivityRequest userRequest) {
        List<RunPopList> runPopLists = runPopListService.selectRunPopListByActivityId(userRequest.getActivityId());
        return CommonResult.success(runPopLists);
    }

    /**
     * 获取机器人跑步模式
     *
     * @return
     */
    @PostMapping("/runMode/getRobotRunMode")
    public Result getRobotRunMode() {
        RobotRunMode robotRunMode = robotRunModeService.getRobotRunMode();
        return CommonResult.success(robotRunModeConverter.toResponseDto(robotRunMode));
    }

    /**
     * 跑步计划结束
     *
     * @param request
     * @return
     */
    @PostMapping("/robotPlan/robotPlanRunEnd")
    Result robotPlanRunEnd(@RequestBody RobotPlanRunEndRequestDto request) {
        robotRunPlanManager.runEnd(request.getMindUserMatchId(), request.getActivityId(), request.getUserId());
        return CommonResult.success();
    }
}
