package com.linzi.pitpat.admin.activityservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTaskRequestDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityTaskPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.AssistActivitityQuery;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityTaskConfigVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityTemplateListVo;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.lang.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 活动任务
 */
@RestController
@RequestMapping({"/activity/task", "/test/activity/task"})
public class ActivityTaskContoller {

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private AssistActivitityService assistActivitityService;
    @Resource
    private ActivityTaskConfigService activityTaskConfigService;

    /**
     * 活动任务列表接口
     *
     * @param activityTaskRequestDto
     * @return
     */
    @RequestMapping("/list")
    public Result<Page<ActivityTaskConfigVo>> list(@RequestBody final ActivityTaskRequestDto activityTaskRequestDto) {
        final ActivityTaskPageQuery pageQuery = new ActivityTaskPageQuery();
        pageQuery.setPageNum(activityTaskRequestDto.getPageNum());
        pageQuery.setPageSize(activityTaskRequestDto.getPageSize());
        pageQuery.setTaskNameLike(activityTaskRequestDto.getTaskName());
        pageQuery.setTemplateNameLike(activityTaskRequestDto.getTemplateName());

        if (activityTaskRequestDto.getStatus() != null) {
            if (Objects.equals(activityTaskRequestDto.getStatus(), 1)) {
                pageQuery.setGmtStartTimeLe(ZonedDateTime.now());
                pageQuery.setGmtEndTimeGe(ZonedDateTime.now());
            } else if (Objects.equals(activityTaskRequestDto.getStatus(), 0)) {
                pageQuery.setGmtStartTimeGt(ZonedDateTime.now());
            } else {
                pageQuery.setGmtEndTimeLt(ZonedDateTime.now());
            }
        }

        final Page<ActivityTaskConfig> page = activityTaskConfigService.findPage(pageQuery);
        final List<ActivityTaskConfig> activityTaskConfigs = page.getRecords();

        final List<ActivityTaskConfigVo> activityTaskConfigVos = new ArrayList<>();
        for (final ActivityTaskConfig activityTaskConfig : activityTaskConfigs) {
            final ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityTaskConfig.getActivityId());
            final ActivityTaskConfigVo vo = new ActivityTaskConfigVo();
            BeanUtils.copyProperties(activityTaskConfig, vo);
            Long assistActivityId = null;
            if (znsRunActivityEntity != null) {
                assistActivityId = znsRunActivityEntity.getAssistActivityId();
                activityTaskConfig.setTemplateName(znsRunActivityEntity.getActivityTitle());
                vo.setActivityNo(znsRunActivityEntity.getActivityNo());
            }
            vo.setHh(DateUtil.addHours8(activityTaskConfig.getHh()));
            if (Objects.equals(vo.getType(), 2)) {
                final String[] configs = vo.getConfig().split(",");
                final StringBuilder sb = new StringBuilder();
                if (configs != null && configs.length > 0) {
                    for (int i = 0; i < configs.length; i++) {
                        final String config = configs[i];
                        sb.append(DateUtil.addHours8(config));
                        if (i < configs.length - 1) {
                            sb.append(",");
                        }
                    }
                }
                vo.setConfig(sb.toString());
            }

            if (assistActivityId != null && assistActivityId > 0) {
                final AssistActivitity activitity = assistActivitityService.findOne(
                        AssistActivitityQuery.builder()
                                .select(List.of(AssistActivitity::getStartTime, AssistActivitity::getEndTime, AssistActivitity::getName))
                                .id(assistActivityId)
                                .build());
                vo.setAssistStartTime(activitity.getStartTime().toInstant().toEpochMilli());
                vo.setAssistEndTime(activitity.getEndTime().toInstant().toEpochMilli());
            }
            activityTaskConfigVos.add(vo);
        }
        final Page<ActivityTaskConfigVo> result = new Page<>();
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());
        result.setRecords(activityTaskConfigVos);

        return CommonResult.success(result);
    }

    /**
     * 新增或者修改活动任务列表
     *
     * @param activityTaskRequestDto
     * @return
     */
    @RequestMapping("/addOrUpdate")
    public Result addOrUpdate(@RequestBody final ActivityTaskRequestDto activityTaskRequestDto) {
        final Integer label = activityTaskRequestDto.getLabel();
        if (label == null) {
            return CommonResult.fail("活动标签类型不能为空!");
        }
        final Integer isShowCountDown = activityTaskRequestDto.getIsShowCountDown();
        if (isShowCountDown == null) {
            return CommonResult.fail("是否展示倒计时不能为空!");
        }
        if (activityTaskRequestDto.getIsShowRunTarget() == null) {
            return CommonResult.fail("是否展示跑步目标不能为空!");
        }
        if (activityTaskRequestDto.getIsShowMarquee() == null) {
            return CommonResult.fail("是否展示跑步灯不能为空!");
        }
        ActivityTaskConfig activityTaskConfig = new ActivityTaskConfig();
        if (activityTaskRequestDto.getId() != null) {
            activityTaskConfig = activityTaskConfigService.selectActivityTaskConfigById(activityTaskRequestDto.getId());
        }
        activityTaskConfig.setLabel(activityTaskRequestDto.getLabel());
        activityTaskConfig.setIsShowCountDown(activityTaskRequestDto.getIsShowCountDown());
        activityTaskConfig.setIsShowRunTarget(activityTaskRequestDto.getIsShowRunTarget());
        activityTaskConfig.setIsShowMarquee(activityTaskRequestDto.getIsShowMarquee());
        activityTaskConfig.setIsShowSpeedLimit(activityTaskRequestDto.getIsShowSpeedLimit());
        activityTaskConfig.setTaskName(activityTaskRequestDto.getTaskName());
        activityTaskConfig.setEntryLimit(activityTaskRequestDto.getEntryLimit());
        final ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityTaskRequestDto.getActivityId());
        final Long assistActivityId = znsRunActivityEntity.getAssistActivityId();
        final ZonedDateTime gmtStartTime = activityTaskRequestDto.getGmtStartTime();
        final ZonedDateTime gmtEndTime = activityTaskRequestDto.getGmtEndTime();
        if (assistActivityId != null && assistActivityId > 0) {
            final AssistActivitity activitity = assistActivitityService.findOne(
                    AssistActivitityQuery.builder()
                            .id(assistActivityId)
                            .build());
            final ZonedDateTime startTime = activitity.getStartTime();
            final ZonedDateTime endTime = activitity.getEndTime();
            // startTime<=gmtStartTime<gmtEndTime<=endTime
            if (startTime.isAfter(gmtStartTime)) {
                return CommonResult.fail("重复周期开始时间大于助力开始时间");
            }
            if (gmtEndTime.isAfter(endTime)) {
                return CommonResult.fail("重复周期结束时间大于助力结束时间");
            }
        }
        activityTaskConfig.setTemplateName(znsRunActivityEntity.getActivityTitle());
        activityTaskConfig.setGmtStartTime(gmtStartTime);
        activityTaskConfig.setActivityId(activityTaskRequestDto.getActivityId());

        activityTaskConfig.setGmtEndTime(gmtEndTime);
        activityTaskConfig.setActivityStartTime(znsRunActivityEntity.getActivityStartTime());
        activityTaskConfig.setActivityEndTime(znsRunActivityEntity.getActivityEndTime());
        activityTaskConfig.setTimeZone(activityTaskRequestDto.getTimeZone());
        activityTaskConfig.setType(activityTaskRequestDto.getType());
        activityTaskConfig.setTaskStartTime(activityTaskRequestDto.getTaskStartTime());
        activityTaskConfig.setTaskEndTime(activityTaskRequestDto.getTaskEndTime());
        if (Objects.equals(activityTaskRequestDto.getType(), 2)) {
            final String[] configs = activityTaskRequestDto.getConfig().split(",");
            final StringBuilder sb = new StringBuilder();
            if (configs != null && configs.length > 0) {
                for (int i = 0; i < configs.length; i++) {
                    final String config = configs[i];
                    sb.append(DateUtil.subHours8(config));
                    if (i < configs.length - 1) {
                        sb.append(",");
                    }
                }
            }
            activityTaskConfig.setConfig(sb.toString());
        } else {
            activityTaskConfig.setConfig(activityTaskRequestDto.getConfig());
        }
        activityTaskConfig.setTaskSort(activityTaskRequestDto.getTaskSort());
        activityTaskConfig.setTaskPic(activityTaskRequestDto.getTaskPic());
        if (activityTaskRequestDto.getTaskStartTime() != null) {
            activityTaskConfig.setHh(DateUtil.formateDateStr(activityTaskRequestDto.getTaskStartTime(), DateUtil.HH_MM));
        } else {
            activityTaskConfig.setHh(DateUtil.subHours8(activityTaskRequestDto.getHh()));
        }
        activityTaskConfigService.insertOrUpdateActivityTaskConfig(activityTaskConfig);
        return CommonResult.success();
    }

    /**
     * 启用禁用
     *
     * @param activityTaskRequestDto
     * @return
     */
    @RequestMapping("/useful")
    public Result useful(@RequestBody final ActivityTaskRequestDto activityTaskRequestDto) {
        final ActivityTaskConfig activityTaskConfig = activityTaskConfigService.selectActivityTaskConfigById(activityTaskRequestDto.getId());
        activityTaskConfig.setUseful(activityTaskRequestDto.getUseful());
        activityTaskConfigService.updateActivityTaskConfigById(activityTaskConfig);
        return CommonResult.success();
    }

    /**
     * 删除任务
     *
     * @param activityTaskRequestDto
     * @return
     */
    @RequestMapping("/delete")
    public Result delete(@RequestBody ActivityTaskRequestDto activityTaskRequestDto) {
        activityTaskConfigService.deleteById(activityTaskRequestDto.getId());
        return CommonResult.success();
    }

    /**
     * 活动模版列表
     *
     * @param activityTaskRequestDto
     * @return
     */
    @RequestMapping("/activityTemplateList")
    public Result<List<ActivityTemplateListVo>> activityTemplateList(@RequestBody final ActivityTaskRequestDto activityTaskRequestDto) {
        final Page<ZnsRunActivityEntity> page = znsRunActivityService.selectActivityByIsTemplate(new Page<>(1, 1000), 1, activityTaskRequestDto.getActivityTitle());

        final List<ActivityTemplateListVo> list = new ArrayList<>();
        final List<ZnsRunActivityEntity> rows = page.getRecords();
        for (final ZnsRunActivityEntity znsRunActivityEntity : rows) {
            final ActivityTemplateListVo activityEntity = new ActivityTemplateListVo();
            activityEntity.setActivityTitle(znsRunActivityEntity.getActivityTitle());
            activityEntity.setId(znsRunActivityEntity.getId());
            activityEntity.setActivityNo(znsRunActivityEntity.getActivityNo());
            activityEntity.setActivityStartTime(znsRunActivityEntity.getActivityStartTime());
            activityEntity.setActivityEndTime(znsRunActivityEntity.getActivityEndTime());
            if (znsRunActivityEntity.getAssistActivityId() > 0) {
                final AssistActivitity assistActivitity = assistActivitityService.selectAssistActivitityById(znsRunActivityEntity.getAssistActivityId());
                if (Objects.nonNull(assistActivitity)) {
                    activityEntity.setAssistActivityStartTime(assistActivitity.getStartTime());
                    activityEntity.setAssistActivityEndTime(assistActivitity.getEndTime());
                }
            }
            if (znsRunActivityEntity.getRateLimitType() == -1) {
                activityEntity.setIsRateLimit(0);
            } else {
                activityEntity.setIsRateLimit(1);
            }
            list.add(activityEntity);
        }
        return CommonResult.success(list);
    }


    @RequestMapping("/istemplate")
    public Result activityIstemplate(@RequestBody final ActivityTaskRequestDto activityTaskRequestDto) {
        final ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityTaskRequestDto.getActivityId());
        if (!StringUtils.hasText(znsRunActivityEntity.getCountry())) {
            return CommonResult.fail("活动国家未配置，不能设置成模板！");
        }
        if (Objects.equals(activityTaskRequestDto.getIsTemplate(), 0)) {
            final ActivityTaskConfig activityTaskConfig = activityTaskConfigService.selectActivityTaskConfigByActivityId(activityTaskRequestDto.getActivityId());
            if (activityTaskConfig != null) {
                return CommonResult.fail("取消设置为模板失败，该活动已被任务使用，不可取消设置");
            }
        }
        znsRunActivityEntity.setIsHomepage(0);
        znsRunActivityEntity.setIsTemplate(activityTaskRequestDto.getIsTemplate());
        znsRunActivityService.updateById(znsRunActivityEntity);
        return CommonResult.success();
    }

    public static void main(final String[] args) {
        System.out.println(DateUtil.subHours8("11:00"));
    }

}
