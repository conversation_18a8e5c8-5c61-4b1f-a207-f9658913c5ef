/**
 * @description: 活动管理控制器
 * @author: yangpeng projectName: pitpat-server fileName: ActivityManageController.java packageName:
 * com.linzi.pitpat.admin.controller date: 2022-02-21 4:05 PM copyright(c) 2018-2020
 * 杭州霖扬网络科技有限公司版权所有
 */
package com.linzi.pitpat.admin.activityservice.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.activityservice.manager.ActivityPropConfigManager;
import com.linzi.pitpat.admin.activityservice.manager.RunActivityPlaylistManager;
import com.linzi.pitpat.admin.activityservice.manager.ZnsRunActivityBussiness;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.EggActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityCreateSourceEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityUserListPo;
import com.linzi.pitpat.data.activityservice.manager.MindUserMatchManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityProcessManager;
import com.linzi.pitpat.data.activityservice.manager.console.ConsoleActivityPageManager;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityListDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityOfficialDto;
import com.linzi.pitpat.data.activityservice.model.dto.EncouragementDto;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.NewUserActivityConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityAwardConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityManagePo;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityConfigInfoVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityWearCacheInfo;
import com.linzi.pitpat.data.activityservice.model.vo.ExportVisibleUserVo;
import com.linzi.pitpat.data.activityservice.model.vo.GetActivityConfigInfoVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewUserActivityConfigTaskVo;
import com.linzi.pitpat.data.activityservice.model.vo.RewardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRuleUserListVo;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityTitleListVo;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunRouteListSimpleVo;
import com.linzi.pitpat.data.activityservice.model.vo.TeamAndRewardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.TeamConfig;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityBrandRightsInterestsService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.PacerConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityCheatService;
import com.linzi.pitpat.data.activityservice.service.RunActivityMedalService;
import com.linzi.pitpat.data.activityservice.service.RunActivityRuleUserService;
import com.linzi.pitpat.data.activityservice.service.RunActivityShowUserService;
import com.linzi.pitpat.data.activityservice.service.RunMilestoneStageConfigService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSourceEnum;
import com.linzi.pitpat.data.awardservice.model.dto.BrandRightsInterestListDto;
import com.linzi.pitpat.data.awardservice.model.vo.AwardRelation;
import com.linzi.pitpat.data.awardservice.model.vo.WearAward;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.AwardProcessService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.entity.dto.ActivityPropDto;
import com.linzi.pitpat.data.entity.dto.RunPlaylistDto;
import com.linzi.pitpat.data.enums.ActivityMusicSupportEnum;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.enums.NoticeTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.SocketEventEnums;
import com.linzi.pitpat.data.equipmentservice.dto.request.WalkDto;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentConfig;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.request.IdRequest;
import com.linzi.pitpat.data.request.activity.VisibleRangePo;
import com.linzi.pitpat.data.robotservice.manager.RobotRunPlanManager;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunMode;
import com.linzi.pitpat.data.robotservice.service.RobotRunModeService;
import com.linzi.pitpat.data.robotservice.service.RobotRunPlanService;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.data.vo.UserFriendMatchVo;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 活动管理
 * 活动管理描述
 *
 * @description: 活动管理控制器
 * @author: yangpeng
 * @className: 活动管理控制器
 * @packageName: com.linzi.pitpat.admin.controller
 * @version: V1.0
 * @date: 2022-02-21 4:05 PM
 **/
@Slf4j
@RestController
@RequestMapping({"/activity", "/test/activity"})
@RequiredArgsConstructor
public class ActivityManageController extends BaseController {

    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsUserService userService;
    @Resource
    private ZnsRunRouteService runRouteService;
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ZnsUserAddService userAddService;
    @Resource
    private ZnsRunActivityBussiness znsRunActivityBussiness;
    @Resource
    private ZnsCourseService courseService;
    @Resource
    private AppMessageService appMessageService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private RedisTemplate redisTemplate;
    @Value("${admin.server.gamepush}")
    private String gameDomain;
    @Resource
    private MindUserMatchService mindUserMatchService;
    @Resource
    private MindUserMatchManager mindUserMatchManager;
    @Resource
    private MindUserMatchBizService mindUserMatchBizService;
    @Resource
    private SocketPushUtils socketPushUtils;
    @Resource
    private UserFriendMatchService userFriendMatchService;
    @Resource
    private RobotRunModeService robotRunModeService;
    @Resource
    private TencentImUtil tencentImUtil;
    @Autowired
    private ISysConfigService sysConfigService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;
    @Resource
    private RobotRunPlanService robotRunPlanService;
    @Autowired
    private EggActivityBizService eggActivityBizService;
    @Autowired
    private RunActivityMedalService runActivityMedalService;
    @Resource
    private RunActivityRuleUserService runActivityRuleUserService;
    @Autowired
    private ActivityEquipmentConfigService activityEquipmentConfigService;
    @Resource
    private ActivityBrandRightsInterestsService activityBrandRightsInterestsService;
    @Autowired
    private EquipmentConfigService equipmentConfigService;
    @Resource
    private PacerConfigService pacerConfigService;

    @Autowired
    private RunMilestoneStageConfigService runMilestoneStageConfigService;
    @Resource
    private RunActivityShowUserService runActivityShowUserService;
    @Resource
    private ActivityTeamService activityTeamService;
    @Resource
    private AwardProcessService awardProcessService;

    @Resource
    private RunActivityPlaylistManager runActivityPlaylistManager;
    @Resource
    private RunActivityCheatService runActivityCheatService;

    @Resource
    private ActivityPropConfigManager activityPropConfigManager;

    @Resource
    private ActivityRateLimitService rateLimitService;

    @Resource
    private ActivityAwardConfigService activityAwardConfigService;

    @Resource
    private AwardConfigService awardConfigService;

    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private ActivityIDGenerateService activityIDGenerateService;
    @Resource
    private MainActivityService mainActivityService;
    @Resource
    private RunActivityBizService runActivityBizService;
    private final ConsoleActivityPageManager consoleActivityPageManager;
    private final RunActivityProcessManager runActivityProcessManager;
    private final RobotRunPlanManager robotRunPlanManager;

    /**
     * 非官方活动列表
     */
    @PostMapping("/list")
    public Result<Page<ActivityListDto>> activityList(@RequestBody final ActivityManagePo po) {
        if (Objects.isNull(po.getSort())) {
            po.setSort(2);
        }
        final Page<ActivityListDto> result = consoleActivityPageManager.runActivityList(po);
        return CommonResult.success(result);
    }

    /**
     * 获取活动配置详情
     *
     * @param activityType
     * @return
     */
    @GetMapping("/getActivityConfigInfo")
    public Result<ActivityConfigInfoVo> getActivityConfigInfo(final Integer activityType, final Integer subType) {
        if (Objects.isNull(activityType)) {
            return CommonResult.fail("活动类型不能为空");
        }
        final ActivityConfigInfoVo data = new ActivityConfigInfoVo();
        //获取路线详情
        final List<RunRouteListSimpleVo> routeList = runRouteService.runRouteList();
        data.setRoute(routeList);

        //查询活动类型对应活动配置
        final ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(activityType, subType);
        final GetActivityConfigInfoVo activity = new GetActivityConfigInfoVo();
        //活动介绍页
        if (null != activityConfig && StringUtils.hasText(activityConfig.getActivityConfig())) {
            activity.setActivityConfigId(activityConfig.getId());
            final Map<String, Object> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
            if (null != jsonObject) {
                // 跑步里程列表
                final String mileages = MapUtil.getString(jsonObject.get(ApiConstants.MILEAGE));
                if (StringUtils.hasText(mileages)) {
                    activity.setMileageList(NumberUtils.stringToDouble(mileages.split(",")));
                }
                // 跑步时长列表
                final String runTimes = MapUtil.getString(jsonObject.get(ApiConstants.RUN_TIME));
                if (StringUtils.hasText(runTimes)) {
                    activity.setRunTimeList(NumberUtils.stringToInt(runTimes.split(",")));
                }
                //跑步里程(英里)列表
                final String miles = MapUtil.getString(jsonObject.get(ApiConstants.MILES));
                if (StringUtils.hasText(miles)) {
                    activity.setMileList(NumberUtils.stringToDouble(miles.split(",")));
                }
                // 奖金池保证金列表
                final String margins = MapUtil.getString(jsonObject.get(ApiConstants.MARGIN));
                if (StringUtils.hasText(margins)) {
                    activity.setMarginList(NumberUtils.stringToDouble(margins.split(",")));
                }
                //费用配置列表
                final String costs = MapUtil.getString(jsonObject.get(ApiConstants.COST));
                if (StringUtils.hasText(costs)) {
                    activity.setCostList(NumberUtils.stringToDouble(costs.split(",")));
                }
                final String scoreCosts = MapUtil.getString(jsonObject.get(ApiConstants.SCORE_COST));
                if (StringUtils.hasText(scoreCosts)) {
                    activity.setScoreCostList(NumberUtils.stringToDouble(scoreCosts.split(",")));
                }
                // 活动奖励
                if (RunActivityTypeEnum.TEAM_RUN.getType().intValue() == activityConfig.getActivityType().intValue()) {
                    final String participateAward = MapUtil.getString(jsonObject.get(ApiConstants.PARTICIPATE_AWARD));
                    final String completeAward = MapUtil.getString(jsonObject.get(ApiConstants.COMPLETE_AWARD));
                    activity.setParticipateAward(participateAward);
                    activity.setCompleteAward(completeAward);
                } else if (RunActivityTypeEnum.CHALLENGE_RUN.getType().intValue() == activityConfig.getActivityType().intValue()) {
                    final String participateAward = MapUtil.getString(jsonObject.get(ApiConstants.PARTICIPATE_AWARD));
                    final String winnerAward = MapUtil.getString(jsonObject.get(ApiConstants.WINNER_AWARD));
                    activity.setParticipateAward(participateAward);
                    activity.setWinnerAward(winnerAward);
                } else if (RunActivityTypeEnum.OFFICIAL_ENENT.getType().intValue() == activityConfig.getActivityType().intValue()) {        //官方排行赛
                    final Map<String, Object> officialChallengeAward = JsonUtil.readValue(jsonObject.get("officialChallengeAward"));
                    activity.setOfficialChallengeAward(officialChallengeAward);
                    final Map<String, Object> officialEventAward = JsonUtil.readValue(jsonObject.get("officialEventAward"));
                    activity.setOfficialEventAward(officialEventAward);
                    final Map<String, Object> beChallengedAward = JsonUtil.readValue(jsonObject.get("beChallengedAward"));
                    activity.setBeChallengedAward(beChallengedAward);
                    final BigDecimal challengeFailureAward = MapUtil.getBigDecimal(jsonObject.get("challengeFailureAward"));
                    activity.setChallengeFailureAward(challengeFailureAward);
                }
                if (RunActivityTypeEnum.officialTypes().contains(activityConfig.getActivityType())) {
                    final List<Integer> rateLimitingConfig = JsonUtil.readList(jsonObject.get(ApiConstants.RATE_LIMITING_CONFIG), Integer.class);
                    activity.setRateLimitingConfig(rateLimitingConfig);
                    final List<Integer> rateLimitingMileConfig = JsonUtil.readList(jsonObject.get(ApiConstants.RATE_LIMITING_MILE_CONFIG), Integer.class);
                    activity.setRateLimitingMileConfig(rateLimitingMileConfig);
                }
            }
        }
        data.setActivity(activity);

        return CommonResult.success(data);
    }

    /**
     * 后台发起跑步活动2
     * 创建空闲机器人
     */
    @PostMapping("/launchActivity2")
    @Log(title = "活动管理", businessType = BusinessType.INSERT)
    public Result launchActivity2(@RequestBody final RunActivityRequest request) {
        request.setIsRobotStart(1);
        // 先判断机器人是否存在
        if (null == request.getRobotUserId()) {
            return CommonResult.fail("机器人用户Id参数缺失");
        }
        final ZnsUserEntity robotUser = userService.findById(request.getRobotUserId());
        if (null == robotUser) {
            return CommonResult.fail("机器人用户不存在");
        }

        final ZnsUserEntity shortageRobotUser = userService.doCreateRobot();
        userService.updateZnsUserRobotStatus(1, shortageRobotUser.getId());
        request.setRobotUserId(shortageRobotUser.getId());
        // 机器人用户注册成功，新增机器人用户额外信息
        final SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        userAddService.addRobotUserAddInfo(shortageRobotUser.getId(), sysUser.getUserId(), sysUser.getUserName(), sysUser.getDeptId());
        final Result ajaxResult = launchActivityDeal(request, shortageRobotUser);
        if (Integer.valueOf(ajaxResult.getCode()) == 200) {
            final Map<String, Object> map = new HashMap<>();
            map.put("robotId", shortageRobotUser.getId());
            map.put("robotName", shortageRobotUser.getFirstName());
            ajaxResult.setData(map);

        }
        return ajaxResult;
    }

    /**
     * 活动详情
     */
    @GetMapping("/detail")
    public String activityDetail(final Long activityId) {
        ZnsUserEntity userEntity = null;
        final ZnsRunActivityUserEntity activityLaunchUser = runActivityUserService.findActivityLaunchUser(activityId);
        if (null != activityLaunchUser) {
            userEntity = userService.findById(activityLaunchUser.getUserId());
        } else {
            userEntity = new ZnsUserEntity();
            userEntity.setId(0L);
        }
        final RunActivityDetailVO activityDetailVO = activityStrategyContext.activityDetail(activityId, userEntity, null);
        final Map<String, Object> data = JsonUtil.readValue(activityDetailVO);
        final Integer runMileage = MapUtil.getInteger(data.get("runMileage"));
        final Integer runTime = MapUtil.getInteger(data.get("runTime"));
        final List<RunActivityUserVO> activityUsers = activityDetailVO.getActivityUsers();
        final List<ZnsUserEntity> userEntities = userService.findByIds(activityUsers.stream().map(RunActivityUserVO::getUserId).collect(Collectors.toList()));
        final Map<Long, ZnsUserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        for (final RunActivityUserVO activityUser : activityUsers) {
            final ZnsUserEntity znsUserEntity = userEntityMap.get(activityUser.getUserId());
            if (Objects.nonNull(znsUserEntity)) {
                activityUser.setIsRobot(znsUserEntity.getIsRobot());
                activityUser.setIsTest(znsUserEntity.getIsTest());
            }
        }
        data.put("activityUsers", activityUsers);
        if (Objects.nonNull(data.get("runningGoalsUnit"))) {
            final Integer runningGoalsUnit = MapUtil.getInteger(data.get("runningGoalsUnit"));
            if (runningGoalsUnit == 0) {
                data.put("runningGoals", BigDecimalUtil.divide(new BigDecimal(runMileage), new BigDecimal(1000)).intValue());
            } else if (runningGoalsUnit == 1) {
                final BigDecimal runningGoals = BigDecimalUtil.divHalfUp(new BigDecimal(runMileage), new BigDecimal(1600), 4);
                data.put("runningGoals", runningGoals);
            } else if (runningGoalsUnit == 2) {
                data.put("runningGoals", BigDecimalUtil.divide(new BigDecimal(runTime), new BigDecimal(60)).intValue());
            }
        } else {
            final Integer completeRuleType = MapUtil.getInteger(data.get("completeRuleType"));
            if (completeRuleType == 1) {
                data.put("runningGoals", BigDecimalUtil.divide(new BigDecimal(runMileage), new BigDecimal(1000)).intValue());
            } else {
                data.put("runningGoals", BigDecimalUtil.divide(new BigDecimal(runTime), new BigDecimal(60)).intValue());
            }
        }
        //跟进信息
        final Map<String, Object> followInfo = userAddService.getFollowInfo(activityId);
        data.put("followInfo", followInfo);
        return JsonUtil.writeString(CommonResult.success(data));
    }

    /**
     * 后台发起跑步活动
     */
    @PostMapping("/launchActivity")
    @Log(title = "活动管理", businessType = BusinessType.INSERT)
    public Result launchActivity(@RequestBody final RunActivityRequest request) {
        request.setIsRobotStart(1);
        // 先判断机器人是否存在
        if (null == request.getRobotUserId()) {
            return CommonResult.fail("机器人用户Id参数缺失");
        }
        final ZnsUserEntity robotUser = userService.findById(request.getRobotUserId());
        if (null == robotUser) {
            return CommonResult.fail("机器人用户不存在");
        }
        //判断机器人是否已经匹配或进行中
        final List<MindUserMatch> matchList = mindUserMatchService.findBusyRots(request.getRobotUserId());

        if (!CollectionUtils.isEmpty(matchList)) {
            return CommonResult.fail(1001, "机器人用户已经匹配，正在占用中");
        }

        return launchActivityDeal(request, robotUser);
    }

    /**
     * 触发活动开始
     */
    @GetMapping("/trigger")
    public Result activityTrigger() {
        runActivityProcessManager.handleActivityStart();
        return CommonResult.success();
    }

    /**
     * 官方活动列表
     */
    @PostMapping("/officialList")
    public Result<Page<ActivityOfficialDto>> officialActivityList(@RequestBody final ActivityManagePo po) {
        final Page<ActivityOfficialDto> result = runActivityService.officialList(po);
        return CommonResult.success(result);
    }

    /**
     * 修改可见范围
     */
    @PostMapping("/visibleRange")
    public Result visibleRange(final MultipartFile file, final VisibleRangePo po) {
        return runActivityBizService.visibleRange(file, po);
    }

    /**
     * 可见用户列表导出
     *
     * @param po
     * @return {@link Result}
     */
    @PostMapping("/exportUser")
    public Result exportUser(@RequestBody final IdRequest po, final HttpServletResponse response, final HttpServletRequest request) {
        final List<ExportVisibleUserVo> list = runActivityShowUserService.selectExportVisibleUser(po.getId());
        final ExcelUtil<ExportVisibleUserVo> util = new ExcelUtil<ExportVisibleUserVo>(ExportVisibleUserVo.class);
        util.exportExcel(list, "活动可见用户列表", response, request);
        return CommonResult.success();
    }

    /**
     * 官方未结束活动列表下拉框
     */
    @PostMapping("/officialNoFinishedActivityList")
    public Result<Page<RunActivityTitleListVo>> officialNoFinishedActivityList(@RequestBody final PageQuery po) {
        final Page<RunActivityTitleListVo> runActivityTitleListVoPage = runActivityService.officialNoFinishedActivityList(po);
        return CommonResult.success(runActivityTitleListVoPage);
    }

    /**
     * 修改活动上下架状态
     */
    @PostMapping("/changeStatus")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    public Result changeStatus(@RequestBody final ZnsRunActivityEntity po) {
        if (Objects.isNull(po.getId()) || Objects.isNull(po.getStatus())) {
            return CommonResult.fail("id或上下架状态参数缺失");
        }
        //查询活动
        final ZnsRunActivityEntity activityEntity = runActivityService.findById(po.getId());
        if (Objects.isNull(activityEntity)) {
            return CommonResult.fail("活动不存在");
        }
        if (po.getStatus() == 1) {
            if (activityEntity.getStatus() != 0) {
                log.info("活动上架失败，活动id：{}，当前活动状态：{}", activityEntity.getId(), activityEntity.getStatus());
                return CommonResult.fail("当前活动状态不可上架");
            }
            //活动国家不能为空
            if (!StringUtils.hasText(activityEntity.getCountry())) {
                log.info("活动上架失败，活动id：{}，请配置可见范围中的国家范围", activityEntity.getId());
                return CommonResult.fail("请配置可见范围中的国家范围");
            }
            eggActivityBizService.doActivityEggPush(activityEntity.getId());
            //  不限
            if (Objects.equals(activityEntity.getApplicationStartLimit(), 0)) {     //当前时间加4分钟
                activityEntity.setApplicationStartTime(
                        DateUtil.formatMinites(ZonedDateTime.now()).plusMinutes(4));
            }
            runActivityService.updateById(activityEntity);
            runActivityService.updateStatus(activityEntity.getId(), po.getStatus(), null);
        } else if (po.getStatus() == -1) {
            if (activityEntity.getStatus() != 1) {
                log.info("活动下架失败，活动id：{}，当前活动状态：{}", activityEntity.getId(), activityEntity.getStatus());
                return CommonResult.fail("当前活动状态不可下架");
            }
            runActivityService.updateStatus(activityEntity.getId(), po.getStatus(), po.getOffRemark());
            //下架通知
            final ActivityNotificationEnum activityNotification = ActivityNotificationEnum.ACTIVITY_OFF_STATUS;
            final String content = String.format("%s 已下线，今晚23:59后将不再记录数据，奖金将于明日发放", activityEntity.getActivityTitle());
            final ImMessageBo bo = appMessageService.assembleImActivityMessage(activityEntity, content);
            final MessageBo messageBo = appMessageService.assembleMessage(activityEntity.getId(), content, "4", NoticeTypeEnum.OFF_SHELF_NOTICE.getType());
            messageBo.setActivityId(activityEntity.getId());
            //获取用户id集合
            final List<Long> userIds = runActivityUserService.findAllActivityUserIds(activityEntity.getId());
            appMessageService.sendImAndPushUserIds(userIds, bo, messageBo);
            //下架机器人处理
            final List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchByActivityId(activityEntity.getId());
            if (!CollectionUtils.isEmpty(mindUserMatches)) {
                for (final MindUserMatch mindUserMatch : mindUserMatches) {
                    if (mindUserMatch.getStatus() == 0 || mindUserMatch.getStatus() == 1) {
                        robotRunPlanManager.runEnd(mindUserMatch.getId(), activityEntity.getId(), mindUserMatch.getUserId());
                    }
                }
            }
        } else {
            return CommonResult.fail("上下架状态不正确");
        }
        return CommonResult.success();
    }

    /**
     * 删除活动
     */
    @DeleteMapping("/{id}")
    @Log(title = "活动管理", businessType = BusinessType.DELETE)
    public Result deleteActivity(@PathVariable final Long id) {
        if (Objects.isNull(id)) {
            return CommonResult.fail("id参数缺失");
        }
        //查询活动
        final ZnsRunActivityEntity activityEntity = runActivityService.findById(id);
        if (Objects.isNull(activityEntity)) {
            return CommonResult.fail("活动不存在");
        }
        if (activityEntity.getStatus() != 0) {
            return CommonResult.fail("只有未上架获得才可以删除");
        }
        runActivityService.deleteActivity(id);
        return CommonResult.success();
    }

    /**
     * 发起官方活动
     *
     * @param request
     * @return
     * @tag 2.4.0
     */
    @PostMapping("/launchOfficialActivity")
    @Log(title = "活动管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    public Result launchOfficialActivity(@RequestBody final ActivityOfficialDto request) {
        request.setRateLimitingUnit(1); // 统一用英里
        final Result ajaxResult = checkOfficialParam(request);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }

        //封装config
        final ZnsRunActivityEntity activity = new ZnsRunActivityEntity();
        BeanUtils.copyProperties(request, activity);
        final String config = encapsulationActivityConfig(request);
        activity.setActivityConfig(config);
        activity.setActualOffTime(null);
        activity.setOffRemark("");
        activity.setIsAddRobot(0);
        activity.setCountry(request.getCountry());
        activity.setStatus(0);
        activity.setUserCount(0);
        activity.setParentId(null);
        activity.setBatchNo(null);
        activity.setTaskConfigId(null);
        if (CollectionUtils.isEmpty(request.getPlaylistDtoList())) {
            activity.setMusicSupport(ActivityMusicSupportEnum.NOT_SUPPORT.getCode());
        }
        // 变速跑 rateLimitType = 2
        if (Objects.isNull(request.getRateLimitType())) {
            request.setRateLimitType(-1);
        }

        //id获取
        final Long activityID = activityIDGenerateService.generateActivityID();
        activity.setId(activityID);
        final boolean save = runActivityService.insert(activity) == 1;
        if (save) {
            //保存新表
            final MainActivity mainActivity = new MainActivity().setOldActivityId(activityID).setId(activityID).setMainType(MainActivityTypeEnum.OLD.getType())
                    .setOldType(activity.getActivityType()).setActivityStartTime(DateUtil.formatDate(activity.getActivityStartTime(), DateUtil.DATE_TIME_SHORT))
                    .setActivityEndTime(DateUtil.formatDate(activity.getActivityEndTime(), DateUtil.DATE_TIME_SHORT))
                    .setApplicationStartTime(DateUtil.formatDate(activity.getApplicationStartTime(), DateUtil.DATE_TIME_SHORT))
                    .setApplicationEndTime(DateUtil.formatDate(activity.getApplicationEndTime(), DateUtil.DATE_TIME_SHORT));
            mainActivityService.insert(mainActivity);
        }

        //服装奖励存缓存
        wearCache(request, activity);
        // 保存歌单
        if (!CollectionUtils.isEmpty(request.getPlaylistDtoList())) {
            final List<Long> playlistIds = request.getPlaylistDtoList().stream().map(RunPlaylistDto::getId).collect(Collectors.toList());
            runActivityPlaylistManager.saveRunPlaylist(playlistIds, activity.getId());
        }
        activity.setPropSupport(request.getPropSupport());
        //保存活动设备限制
        activityEquipmentConfigService.updateActivityEquipmentConfig(activity.getId(), request.getActivityEquipmentConfigs());
        activity.setIsNew(YesNoStatus.YES.getCode());// 3.0版本的官方赛都设为1
        //TODO 分布式ID 解决重复更新问题
        activity.setActivityNo("HD" + activity.getId());
        runActivityService.updateById(activity);
        //作弊开关处理
        runActivityCheatService.saveSwitchList(activity.getId(), request.getCheatSwitchList());
        //权益处理
        activityBrandRightsInterestsService.addOrUpdate(request.getBrandRightsInterestList(), activity.getId(), activity.getPrivilegeBrand());
        //配速员处理
        pacerConfigService.addPacerConfig(activity.getId(), request.getPacerDtos());
        if (request.getActivityObjectType() == 3) {
            //参与用户绑定
            runActivityRuleUserService.bindingRuleUser(activity.getId(), request.getRuleUserBatchNo(), request.getIsCopyAdd(), request.getActivityCopyId());
        }
        if (activity.getActivityType() == 4) {

            // 保存官方组队跑的限速
            rateLimitService.saveRateLimit(request, activity.getId());

            final int betweenSecond = DateUtil.betweenSecond(ZonedDateTime.now(), activity.getActivityEndTime());
            final List<Integer> runningGoals = runActivityService.runningGoalToSmallUnit(request.getRunningGoals(), request.getRunningGoalsUnit());
            for (final Integer runningGoal : runningGoals) {
                final Long activityGoalId = NumberUtils.getGoalImNumber(activity.getId(), runningGoal, activity.getCompleteRuleType());
                redisTemplate.opsForValue().set(RedisConstants.ACTIVITY_ID + activityGoalId.toString(), "1", betweenSecond, TimeUnit.SECONDS);
                //推送到游戏服务器端
                try {
                    GamePushUtils.addRoom(gameDomain, Long.valueOf(activityGoalId), 2, null);
                } catch (final Exception e) {
                    log.info("请求游戏服务器异常:{}", e.getMessage());
                }
            }
            // 保存道具数据
            final List<ActivityPropDto> activityPropDtoList = request.getActivityPropDtoList();
            if (YesNoStatus.YES.getCode().equals(request.getPropSupport())
                    && !CollectionUtils.isEmpty(activityPropDtoList)) {
                activityPropConfigManager.save(activityPropDtoList, activity.getId());
            }
        } else if (activity.getActivityType() == 3) {
            //推送到游戏服务器端
            try {
                GamePushUtils.addRoom(gameDomain, activity.getId(), 2, null);
            } catch (final Exception e) {
                log.info("请求游戏服务器异常:{}", e.getMessage());
            }

        } else if (activity.getActivityType() == 10) {
            //团队赛
            //默认未上架
            activity.setStatus(0);
            final TeamAndRewardConfig teamAndRewardConfig = request.getTeamAndRewardConfig();
            saveTeamAndRewardConfig(teamAndRewardConfig, activity);

        }
        log.info("官方活动新增成功");
        eggActivityBizService.doActivityEggPush(activity.getId());
        // 里程碑 保存指定主题模式配置信息
        if (YesNoStatus.YES.getCode().equals(request.getShowMode())) {
            runMilestoneStageConfigService.saveRunMilestoneStageConfigList(request.getShowLists(), request.getMilepostAward().size(), activity);
        }
        //处理活动勋章
        runActivityMedalService.dealActivityMedal(activity.getId(), request.getMedalConfigId());

        return CommonResult.success();
    }

    /**
     * 服装缓存
     *
     * @param request
     * @param activity
     */
    private void wearCache(final ActivityOfficialDto request, final ZnsRunActivityEntity activity) {
        if (!CollectionUtils.isEmpty(request.getMilepostWearsAward()) && activity.getActivityType().equals(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType())) {
            final Long activityId = activity.getId();
            final List<MilepostWearAwardDto> milepostWearsAward = request.getMilepostWearsAward();
            final List<WearAward> wearList = new ArrayList<>();
            for (final MilepostWearAwardDto milepostWearAwardDto : milepostWearsAward) {
                final WearAward wearAwardDto = new WearAward();
                BeanUtils.copyProperties(milepostWearAwardDto, wearAwardDto);
                wearAwardDto.setWearId(milepostWearAwardDto.getWearValue());
                wearList.add(wearAwardDto);
            }
            final RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
            final ActivityWearCacheInfo activityWearCacheInfo = new ActivityWearCacheInfo();
            activityWearCacheInfo.setActivityId(activityId);
            activityWearCacheInfo.setExpireTime(activity.getApplicationEndTime());
            activityWearCacheInfo.setWearList(wearList);
            list.iterator().forEachRemaining(info -> {
                if (info.getActivityId().equals(activityId)) {
                    list.remove(info);
                }
            });
            list.add(activityWearCacheInfo);
        }
    }


    /**
     * 编辑官方活动
     *
     * @tag 2.7.0
     */
    @PutMapping("/editOfficialActivity")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    public Result editOfficialActivity(@RequestBody final ActivityOfficialDto request) {
        request.setRateLimitingUnit(1);
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("活动id不能为空");
        }
        final ZnsRunActivityEntity activityEntity = runActivityService.findById(request.getId());
        if (Objects.isNull(activityEntity)) {
            return CommonResult.fail("活动不存在");
        }

        final ZnsRunActivityEntity activity = new ZnsRunActivityEntity();
        if (activityEntity.getStatus() == 0) {
            final Result ajaxResult = checkOfficialParam(request);
            if (Objects.nonNull(ajaxResult)) {
                return ajaxResult;
            }
            BeanUtils.copyProperties(request, activity);
            final String config = encapsulationActivityConfig(request);
            activity.setActivityConfig(config);

            activityEquipmentConfigService.updateActivityEquipmentConfig(activity.getId(), request.getActivityEquipmentConfigs());
            //权益处理
            activityBrandRightsInterestsService.addOrUpdate(request.getBrandRightsInterestList(), activity.getId(), activity.getPrivilegeBrand());
            //配速员处理
            pacerConfigService.addPacerConfig(activity.getId(), request.getPacerDtos());
            //作弊开关处理
            runActivityCheatService.saveSwitchList(activity.getId(), request.getCheatSwitchList());
            // 道具配置
            activityPropConfigManager.delete(request.getId());
            if (YesNoStatus.YES.getCode().equals(request.getPropSupport())) {
                activityPropConfigManager.save(request.getActivityPropDtoList(), activity.getId());
            }
            if (Objects.equals(activity.getActivityType(), RunActivityTypeEnum.TEAM_ACTIVITY.getType())) {
                //团队赛未上架能编辑队伍配置和奖励
                final TeamAndRewardConfig teamAndRewardConfig = request.getTeamAndRewardConfig();
                saveTeamAndRewardConfig(teamAndRewardConfig, activity);
            }
        } else if (activityEntity.getStatus() == 1) {
            if (activityEntity.getActivityState() == 0 || activityEntity.getActivityState() == 1) {
                if (request.getApplicationUserLimit() != -1) {
                    if (request.getApplicationUserLimit() < activityEntity.getApplicationUserLimit()) {
                        return CommonResult.fail("参赛人数限制只能改大，不能改小");
                    }
                    if (activityEntity.getApplicationUserLimit() == -1 && request.getApplicationUserLimit() > 0) {
                        return CommonResult.fail("参赛人数限制只能改大，不能改小");
                    }
                }

                activity.setId(request.getId());
                activity.setClassifyType(request.getClassifyType());
                activity.setApplicationUserLimit(request.getApplicationUserLimit());
                activity.setIsHomepage(request.getIsHomepage());
                activity.setRemark(request.getRemark());
                final Map<String, Object> object = JsonUtil.readValue(activityEntity.getActivityConfig());
                object.put("advertisingImage", request.getAdvertisingImage());
                object.put("detailsImage", request.getDetailsImage());
                object.put("coverImage", request.getCoverImage());
                object.put("themeActivityListBackGroundImageUrl", request.getThemeActivityListBackGroundImageUrl());
                activity.setActivityConfig(JsonUtil.writeString(object));

                // 添加助力活动相关参数
                activity.setAssistActivityId(request.getAssistActivityId());
                activity.setAssistActivityLinkPeriod(request.getAssistActivityLinkPeriod());
            } else {
                return CommonResult.fail("结束状态不可编辑活动");
            }
        } else if (activityEntity.getStatus() == -1) {
            return CommonResult.fail("下架状态不可编辑活动");
        }
        //更新服装奖励缓存
        final List<MilepostWearAwardDto> milepostWearsAward = request.getMilepostWearsAward();
        if (!CollectionUtils.isEmpty(milepostWearsAward)) {
            wearCache(request, activityEntity);
        } else {
            final RList<ActivityWearCacheInfo> list = redissonClient.getList(RedisConstants.ACTIVITY_WEAR_AWARD_CACHE_LIST);
            list.iterator().forEachRemaining(info -> {
                if (info.getActivityId().equals(request.getId())) {
                    list.remove(info);
                }
            });
        }
        // 保存歌单
        if (!CollectionUtils.isEmpty(request.getPlaylistDtoList())) {
            final List<Long> playlistIds = request.getPlaylistDtoList().stream().map(RunPlaylistDto::getId).collect(Collectors.toList());
            runActivityPlaylistManager.saveRunPlaylist(playlistIds, activity.getId());
        } else {
            activity.setMusicSupport(ActivityMusicSupportEnum.NOT_SUPPORT.getCode());
            runActivityPlaylistManager.deleteRunPlaylistByActivityId(activity.getId());
        }
        runActivityService.updateById(activity);
        //保存指定主题模式配置信息
        if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(request.getActivityType())) {
            runMilestoneStageConfigService.updateRunMilestoneStageConfigList(request.getShowLists(), request.getMilepostAward().size(), activity, request.getShowMode());
        }
        //处理活动勋章
        runActivityMedalService.dealActivityMedal(activity.getId(), request.getMedalConfigId());
        rateLimitService.saveRateLimit(request, activity.getId());
        log.info("官方活动编辑成功");
        return CommonResult.success();
    }

    /**
     * 添加特定用户
     *
     * @param activityId
     * @param file
     * @return
     */
    @PostMapping("/addRuleUser")
    public Result addRuleUser(final MultipartFile file, final Long activityId) {
        if (Objects.isNull(file)) {
            return CommonResult.success("");
        }
        //参与用户处理
        String batchNumber = "";
        if (Objects.nonNull(activityId)) {
            runActivityRuleUserService.updateRuleUser(activityId, file);
        } else {
            batchNumber = runActivityRuleUserService.addRuleUser(file);
        }
        final Map<String, Object> data = new HashMap<>();
        data.put("batchNumber", batchNumber);
        return CommonResult.success(data);
    }

    /**
     * 查询指定用户列表
     *
     * @param request
     * @return
     */
    @PostMapping("/user/list")
    public Result<Page<RunActivityRuleUserListVo>> userList(@RequestBody final ActivityUserListPo request) {
        final Page<RunActivityRuleUserListVo> page = runActivityRuleUserService.pageList(new Page<>(request.getPageNum(), request.getPageSize()),
                request.getActivityId());
        return CommonResult.success(page);
    }

    /**
     * 获取官方活动详情
     */
    @GetMapping("/officialActivityDetail")
    public Result<ActivityOfficialDto> officialActivityDetail(final Long activityId, final Integer isRealUser) {
        if (Objects.isNull(activityId)) {
            return CommonResult.fail("activityId不能为空");
        }
        final ActivityOfficialDto officialDto = runActivityBizService.getOfficialActivityDetail(activityId, isRealUser);
        return CommonResult.success(officialDto);
    }

    /**
     * 设备类型列表
     *
     * @param dto 请求参数
     * @return
     */
    @PostMapping("/equipment/type/list")
    public Result<List<EquipmentConfig>> equipmentTypeList(@RequestBody final WalkDto dto) {
        final List<EquipmentConfig> equipmentConfigList = equipmentConfigService.selectEquipmentConfigByTypeAndActivity(Arrays.asList(1, 2, 3), dto.getActivityId());
        if (dto.getContainDerivative() != null && dto.getContainDerivative() == 1){
            equipmentConfigList.addAll(sysConfigService.getDerivative());
        }
        return CommonResult.success(equipmentConfigList);
    }


    /**
     * 获取新人活动配置
     */
    @GetMapping("/getNewUserActivityConfig")
    public Result<NewUserActivityConfigDto> getNewUserActivityConfig() {
        final ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.findRunActivityConfig(6L);
        if (Objects.isNull(runActivityConfig)) {
            return CommonResult.fail("配置不存在");
        }
        final NewUserActivityConfigDto data = new NewUserActivityConfigDto();

        final Map<String, Object> object = JsonUtil.readValue(runActivityConfig.getActivityConfig());
        final List<NewUserActivityConfigTaskVo> taskVos = JsonUtil.readList(object.get("tasks"), NewUserActivityConfigTaskVo.class);
        final Object activityEquipmentConfigsObj = object.get("activityEquipmentConfigs");
        if (Objects.nonNull(activityEquipmentConfigsObj)) {
            final List<ActivityEquipmentConfig> activityEquipmentConfigs = JsonUtil.readList(object.get("activityEquipmentConfigs"), ActivityEquipmentConfig.class);
            data.setActivityEquipmentConfigs(activityEquipmentConfigs);
        }
        data.setNewUserExclusiveTime(MapUtil.getInteger(object.get("newUserExclusiveTime")));
        data.setTasks(taskVos);
        return CommonResult.success(data);
    }

    /**
     * 修改新人活动
     *
     * @param configDto
     * @return
     */
    @PostMapping("/editNewUserActivityConfig")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    public Result editNewUserActivityConfig(@RequestBody final NewUserActivityConfigDto configDto) {
        final Result ajaxResult = checkNewUserActivityConfig(configDto);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        final ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.findRunActivityConfig(6L);
        if (Objects.isNull(runActivityConfig)) {
            return CommonResult.fail("配置不存在");
        }
        final Map<String, Object> object = JsonUtil.readValue(runActivityConfig.getActivityConfig());
        object.put("newUserExclusiveTime", configDto.getNewUserExclusiveTime());
        object.put("tasks", configDto.getTasks());
        object.put("activityEquipmentConfigs", configDto.getActivityEquipmentConfigs());

        runActivityConfig.setActivityConfig(JsonUtil.writeString(object));
        runActivityConfigService.update(runActivityConfig);

        return CommonResult.success();
    }

    private Result checkNewUserActivityConfig(final NewUserActivityConfigDto configDto) {
        if (Objects.isNull(configDto.getNewUserExclusiveTime()) || configDto.getNewUserExclusiveTime() <= 0) {
            return CommonResult.fail("截止时间不能为空或小于1");
        }
        if (CollectionUtils.isEmpty(configDto.getTasks())) {
            return CommonResult.fail("关卡数据不能为空");
        }
        for (final NewUserActivityConfigTaskVo task : configDto.getTasks()) {
            if (Objects.isNull(task.getLevelName())) {
                return CommonResult.fail("关卡名称不能为空");
            }
            if (Objects.isNull(task.getTaskType())) {
                return CommonResult.fail("任务类型不能为空");
            }
            if (Objects.isNull(task.getWinReward())) {
                return CommonResult.fail("胜者奖金不能为空");
            }
            if (Objects.isNull(task.getFailReward())) {
                return CommonResult.fail("败者奖金不能为空");
            }
            if (task.getTaskType() == 1 && Objects.isNull(task.getRobotRunningMode())) {
                return CommonResult.fail("机器人跑步模式不能为空");
            }
            if (task.getTaskType() == 1 || task.getTaskType() == 3) {
                if (Objects.isNull(task.getMileageTarget())) {
                    return CommonResult.fail("里程目标不能为空");
                }
                //里程转为米
                task.setMileageTarget(task.getMileageTarget());
                task.setCourseName("");
                task.setCourseId(null);
            } else if (task.getTaskType() == 2) {
                if (Objects.isNull(task.getCourseId())) {
                    return CommonResult.fail("课程不能为空不能为空");
                }
                final ZnsCourseEntity course = courseService.selectById(task.getCourseId());
                task.setCourseName(course.getCourseName());
                final Boolean enableEncouragement = task.getEnableEncouragement();
                if (enableEncouragement) {
                    final List<EncouragementDto> encouragementList = task.getEncouragementList();
                    if (CollectionUtils.isEmpty(encouragementList)) {
                        return CommonResult.fail("开启鼓励时,鼓励内容列表不能为空!");
                    }
                    final int size = encouragementList.size();
                    if (size > 5) {
                        return CommonResult.fail("最多可增加5条鼓励!");
                    }
                    for (int i = 0; i < encouragementList.size(); i++) {
                        final int rowNumber = i + 1;
                        final EncouragementDto encouragementDto = encouragementList.get(i);
                        final Integer currentMiles = encouragementDto.getCurrentMiles();
                        if (currentMiles < 160) {
                            return CommonResult.fail("第" + rowNumber + "行当前里程不能小于0.1 miles!");
                        }
                        final String encouragementContent = encouragementDto.getEncouragementContent();
                        if (!StringUtils.hasText(encouragementContent)) {
                            return CommonResult.fail("第" + rowNumber + "行鼓励内容不能为空!");
                        }
                    }
                }
            }
            if (task.getTaskType() == 1) {
                final Integer mileageTarget = task.getMileageTarget();
                if (mileageTarget < 800) {
                    return CommonResult.fail("随机匹配竞技跑，最小值：0.5miles");
                }
            }

        }
        return null;
    }

    private String encapsulationActivityConfig(final ActivityOfficialDto request) {
        final Map<String, Object> object = new HashMap<>();
        object.put("advertisingImage", request.getAdvertisingImage());
        object.put("activityRule", request.getActivityRule());
        object.put("detailsImage", request.getDetailsImage());
        object.put("coverImage", request.getCoverImage());
        object.put("runningGoalsUnit", request.getRunningGoalsUnit());
        object.put("rateLimitingUnit", request.getRateLimitingUnit());
        object.put("homeBanner", request.getHomeBanner());
        object.put("advertisingConfigImage", request.getAdvertisingConfigImage());
        object.put("activityObjectErrorDesc", request.getActivityObjectErrorDesc());
        object.put("maxRunScore", request.getMaxRunScore());
        object.put("minRunScore", request.getMinRunScore());

        final ZnsRunActivityConfigEntity configEntity = runActivityConfigService.getByType(request.getActivityType(), null);
        if (request.getActivityType() == 3) {
            //挑战成功奖励配置
            object.put(ApiConstants.OFFICIAL_CHALLENGE_AWARD, request.getOfficialChallengeAward());
            object.put(ApiConstants.OFFICIAL_CHALLENGE_COUPON_AWARD, request.getOfficialChallengeCouponAward());
            object.put(ApiConstants.OFFICIAL_CHALLENGE_SCORE_AWARD, request.getOfficialChallengeScoreAward());
            //活动结束奖励配置
            object.put(ApiConstants.OFFICIAL_EVENT_AWARD, request.getOfficialEventAward());
            object.put(ApiConstants.OFFICIAL_EVENT_SCORE_AWARD, request.getOfficialEventScoreAward());
            object.put(ApiConstants.OFFICIAL_EVENT_COUPON_AWARD, request.getOfficialEventCouponAward());
            //挑战失败奖励配置
            object.put(ApiConstants.CHALLENGE_FAILURE_AWARD, request.getChallengeFailureAward());
            object.put(ApiConstants.CHALLENGE_FAILURE_COUPON_AWARD, request.getChallengeFailureCouponAward());
            object.put(ApiConstants.CHALLENGE_FAILURE_SCORE_AWARD, request.getChallengeFailureScoreAward());
            //被挑战奖励配置
            object.put(ApiConstants.BE_CHALLENGED_AWARD, request.getBeChallengedAward());
            object.put(ApiConstants.BE_CHALLENGED_COUPON_AWARD, request.getBeChallengedCouponAward());
            object.put(ApiConstants.BE_CHALLENGED_SCORE_AWARD, request.getBeChallengedScoreAward());
            object.put("runningGoals", runActivityService.runningGoalToSmallUnit(request.getRunningGoals(), request.getRunningGoalsUnit()));
            //挑战消耗积分
            object.put(ApiConstants.CHALLENGE_SCORE_CONSUME, request.getChallengeScoreConsumer());
            //官方赛事活动挑战榜中人奖励
            if (Objects.nonNull(configEntity) && StringUtils.hasText(configEntity.getActivityConfig())) {
                final Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
                String activityRankAward = MapUtil.getString(jsonObject.get(ApiConstants.ACTIVITY_RANK_AWARD));
                if (StringUtils.hasText(activityRankAward)) {
                    activityRankAward = activityRankAward.replaceAll("0.2", request.getChallengeFailureAward().toString());
                    object.put("activityRankAward", activityRankAward);
                }
            }
            final StringBuffer awardBuffer = new StringBuffer();
            awardBuffer.append("The 1st place: $" + request.getOfficialEventAward().get("1"));
            awardBuffer.append("\nThe 2nd place: $" + request.getOfficialEventAward().get("2"));
            awardBuffer.append("\nThe 3rd place: $" + request.getOfficialEventAward().get("3"));
            awardBuffer.append("\nThe 4th~6th place(total of 3):$" + request.getOfficialEventAward().get("4") + "/person");
            awardBuffer.append("\nThe 7th~10th place(total of 4):$" + request.getOfficialEventAward().get("5") + "/person");
            awardBuffer.append("\nThe 11th~20th place(total of 10):$" + request.getOfficialEventAward().get("6") + "/person");
            awardBuffer.append("\nThe 21st+: $" + request.getOfficialEventAward().get("7") + "/person");
            awardBuffer.append("\nBonus will be sent when game ends.");
            object.put("activityCompleteAward", awardBuffer.toString());

            object.put("mapRobotLimit", request.getMapRobotLimit());
            object.put("rankRobotMode", request.getRankRobotMode());
            setRobot(object, request);
        } else if (request.getActivityType() == 4) {                            //如果是官方多人同跑
            //数据库中存小单位，m或s
            object.put("runningGoals", runActivityService.runningGoalToSmallUnit(request.getRunningGoals(), request.getRunningGoalsUnit()));
            //转换大单位
            final List<Map<String, Object>> runningGoalsAward = request.getRunningGoalsAward();
            changeRunningGoals(request, runningGoalsAward);

            final List<Map<String, Object>> runningGoalScoreAward = request.getRunningGoalScoreAward();
            changeRunningGoals(request, runningGoalScoreAward);

            final List<Map<String, Object>> runningGoalCouponAward = request.getRunningGoalCouponAward();
            changeRunningGoals(request, runningGoalCouponAward);
            object.put("runningGoalsAward", runningGoalsAward);
            object.put("runningGoalScoreAward", runningGoalScoreAward);
            object.put("runningGoalCouponAward", runningGoalCouponAward);

            setRobot(object, request);

            final Random random = new Random();
            final Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
            Integer runBeforeEnter = MapUtil.getInteger(jsonObject.get("runBeforeEnter"));
            if (Objects.isNull(runBeforeEnter)) {
                runBeforeEnter = 5;
            }
            object.put("automaticAdmissionTime", random.nextInt(runBeforeEnter + 1));
            object.put("runBeforeEnter", runBeforeEnter);
            Integer lastEnterMinutes = MapUtil.getInteger(jsonObject.get(ApiConstants.TEAM_ACTIVITY_LAST_ENTER));
            if (null == lastEnterMinutes) {
                lastEnterMinutes = 30;
            }
            object.put(ApiConstants.TEAM_ACTIVITY_LAST_ENTER, lastEnterMinutes);
        } else if (request.getActivityType() == 5) {
            object.put("milepostAward", request.getMilepostAward());

            object.put("milepostCouponAward", request.getMilepostCouponAward());
            object.put("milepostWearsAward", request.getMilepostWearsAward());

            final Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
            object.put(ApiConstants.WARM_PROMPT, jsonObject.get(ApiConstants.WARM_PROMPT));
        } else if (RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(request.getActivityType())) {
            //do nothing
            final String themeActivityListBackGroundImageUrl = request.getThemeActivityListBackGroundImageUrl();
            object.put("themeActivityListBackGroundImageUrl", themeActivityListBackGroundImageUrl);
        } else {
            if (Objects.nonNull(configEntity) && StringUtils.hasText(configEntity.getActivityConfig())) {
                return configEntity.getActivityConfig();
            }
        }
        return JsonUtil.writeString(object);
    }

    private void setRobot(final Map<String, Object> object, final ActivityOfficialDto request) {
        //比赛结束前，投放抢占名次的机器人
        object.put("SRobotCountStart", request.getRobotSCountStart());
        object.put("ARobotCountStart", request.getRobotACountStart());
        object.put("BRobotCountStart", request.getRobotBCountStart());
        object.put("CRobotCountStart", request.getRobotCCountStart());
        object.put("DRobotCountStart", request.getRobotDCountStart());
        object.put("SPlusRobotCountStart", request.getRobotSPlusCountStart());


        object.put("E1RobotCountStart", request.getRobotE1CountStart());
        object.put("E2RobotCountStart", request.getRobotE2CountStart());
        object.put("E3RobotCountStart", request.getRobotE3CountStart());
        object.put("E4RobotCountStart", request.getRobotE4CountStart());
        object.put("E5RobotCountStart", request.getRobotE5CountStart());
        object.put("E6RobotCountStart", request.getRobotE6CountStart());

        object.put("E1RobotCountEnd", request.getRobotE1CountEnd());
        object.put("E2RobotCountEnd", request.getRobotE2CountEnd());
        object.put("E3RobotCountEnd", request.getRobotE3CountEnd());
        object.put("E4RobotCountEnd", request.getRobotE4CountEnd());
        object.put("E5RobotCountEnd", request.getRobotE5CountEnd());
        object.put("E6RobotCountEnd", request.getRobotE6CountEnd());

        object.put("SRobotCountEnd", request.getRobotSCountEnd());
        object.put("ARobotCountEnd", request.getRobotACountEnd());
        object.put("BRobotCountEnd", request.getRobotBCountEnd());
        object.put("CRobotCountEnd", request.getRobotCCountEnd());
        object.put("DRobotCountEnd", request.getRobotDCountEnd());
        object.put("SPlusRobotCountEnd", request.getRobotSPlusCountEnd());

        object.put("SRobotCount", NumberUtils.getRandomInt(request.getRobotSCountStart(), request.getRobotSCountEnd(), 1));
        object.put("ARobotCount", NumberUtils.getRandomInt(request.getRobotACountStart(), request.getRobotACountEnd(), 1));
        object.put("BRobotCount", NumberUtils.getRandomInt(request.getRobotBCountStart(), request.getRobotBCountEnd(), 1));
        object.put("CRobotCount", NumberUtils.getRandomInt(request.getRobotCCountStart(), request.getRobotCCountEnd(), 1));
        object.put("DRobotCount", NumberUtils.getRandomInt(request.getRobotDCountStart(), request.getRobotDCountEnd(), 1));
        object.put("SPlusRobotCount", NumberUtils.getRandomInt(request.getRobotSPlusCountStart(), request.getRobotSPlusCountEnd(), 1));

        object.put("E1RobotCount", NumberUtils.getRandomInt(request.getRobotE1CountStart(), request.getRobotE1CountEnd(), 1));
        object.put("E2RobotCount", NumberUtils.getRandomInt(request.getRobotE2CountStart(), request.getRobotE2CountEnd(), 1));
        object.put("E3RobotCount", NumberUtils.getRandomInt(request.getRobotE3CountStart(), request.getRobotE3CountEnd(), 1));
        object.put("E4RobotCount", NumberUtils.getRandomInt(request.getRobotE4CountStart(), request.getRobotE4CountEnd(), 1));
        object.put("E5RobotCount", NumberUtils.getRandomInt(request.getRobotE5CountStart(), request.getRobotE5CountEnd(), 1));
        object.put("E6RobotCount", NumberUtils.getRandomInt(request.getRobotE6CountStart(), request.getRobotE6CountEnd(), 1));

    }

    public void changeRunningGoals(final ActivityOfficialDto request, final List<Map<String, Object>> runningGoalsAward) {
        if (CollectionUtils.isEmpty(runningGoalsAward)) {
            return;
        }
        for (final Map<String, Object> objectMap : runningGoalsAward) {
            final Double goal = MapUtils.getDouble(objectMap, "goal");
            if (goal == null) {
                continue;
            }
            if (request.getRunningGoalsUnit() == 0) {
                final Double key = goal * 1000;
                objectMap.put("goal", key);
            } else if (request.getRunningGoalsUnit() == 1) {
                final Double key = goal * 1600;
                objectMap.put("goal", key);
            } else if (request.getRunningGoalsUnit() == 2) {
                final Double key = goal * 60;
                objectMap.put("goal", key);
            }
        }

    }

    /**
     * 检查官方活动参数
     *
     * @param request
     * @return
     */
    private Result checkOfficialParam(final ActivityOfficialDto request) {
        if (!StringUtils.hasText(request.getActivityTitle())) {
            return CommonResult.fail("活动名称不能为空");
        }
        if (Objects.isNull(request.getClassifyType()) || request.getClassifyType() == 0) {
            return CommonResult.fail("活动分类不能为空");
        }
        if (Objects.isNull(request.getActivityType()) || request.getActivityType() == 0) {
            return CommonResult.fail("活动类型不能为空");
        }
        if (Objects.isNull(request.getActivityObjectType())) {
            return CommonResult.fail("活动对象不能为空");
        }
        if (Objects.isNull(request.getActivityStartTime()) || Objects.isNull(request.getActivityEndTime())) {
            return CommonResult.fail("活动开始时间或结束不能为空");
        }
/*        if (Objects.isNull(request.getApplicationStartTime()) || Objects.isNull(request.getApplicationEndTime())) {
            return CommonResult.fail("报名开始时间或结束不能为空");
        }*/

        if (request.getActivityEndTime().compareTo(request.getActivityStartTime()) <= 0) {
            return CommonResult.fail("活动结束时间必须大于开始时间");
        }
        if (request.getActivityEndTime().compareTo(ZonedDateTime.now()) < 0) {
            return CommonResult.fail("活动结束时间必须大于当前时间");
        }
 /*       if (request.getApplicationEndTime().compareTo(request.getApplicationStartTime()) <= 0) {
            return CommonResult.fail("报名结束时间必须大于开始时间");
        }
        if (request.getActivityStartTime().compareTo(request.getApplicationStartTime()) < 0) {
            return CommonResult.fail("报名开始时间必须小于等于活动开始时间");
        }*/
        if (request.getActivityEndTime().compareTo(request.getApplicationEndTime()) < 0) {
            return CommonResult.fail("报名结束时间必须小于等于结束时间");
        }

        if (Objects.isNull(request.getApplicationUserLimit())
                &&
                //团队赛不校验
                !RunActivityTypeEnum.TEAM_ACTIVITY.getType().equals(request.getActivityType())) {
            return CommonResult.fail("参与用户数量限制不能为空");
        }
        if (Objects.isNull(request.getIsHomepage())) {
            return CommonResult.fail("是否首页展示不能为空");
        }
        if (Objects.isNull(request.getBonusRuleType())) {
            return CommonResult.fail("参与规则不能为空");
        }
        if (Arrays.asList(3, 5).contains(request.getBonusRuleType()) || request.getBonusRuleType() == 2) {
            if (Objects.isNull(request.getActivityEntryFee()) || request.getActivityEntryFee().compareTo(BigDecimal.ZERO) <= 0) {
                return CommonResult.fail("保证金或费用不能为空");
            }
        } else if (request.getBonusRuleType() == 1) {
            request.setActivityEntryFee(BigDecimal.ZERO);
        }
        if (!StringUtils.hasText(request.getActivityRule())) {
            return CommonResult.fail("活动规则不能为空");
        }
        if (!StringUtils.hasText(request.getAdvertisingImage())) {
            return CommonResult.fail("活动宣传图不能为空");
        }
        if (!StringUtils.hasText(request.getCoverImage())) {
            return CommonResult.fail("活动封面图不能为空");
        }
//        if (CollectionUtils.isEmpty(request.getDetailsImage())) {
//            return CommonResult.fail("活动详情图不能为空");
//        }
        if (Objects.isNull(request.getActivityRouteId()) || request.getActivityRouteId() == 0) {
            return CommonResult.fail("活动路线不能为空");
        }
        if (Objects.isNull(request.getRateLimitingUnit())) {
            request.setRateLimitingUnit(-1);
            request.setRateLimiting(-1);
        }
        if (request.getRateLimitingUnit() == -1) {
            request.setRateLimiting(-1);
        }
        if (Objects.isNull(request.getRunningGoalsUnit())) {
            request.setRunningGoalsUnit(0);
        }
        // 暂时只有累计跑有限速
        if (request.getActivityType().equals(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType()) && request.getRateLimitType() > 0) {
            if (Objects.isNull(request.getRateLimitMile()) || request.getRateLimitMile().compareTo(BigDecimal.ZERO) == 0) {
                return CommonResult.fail("限制速度不能为空");
            }
            if (request.getRateLimitType() == 1 &&
                    (request.getRateLimitMile().compareTo(new BigDecimal(0.6)) < 0 || request.getRateLimitMile().compareTo(new BigDecimal(12)) > 0)) {
                return CommonResult.fail("最小限速范围在0.6-12 (mile/h)之间");
            }

            request.setRateLimiting(SportsDataUnit.conversionVelocityToMetre(request.getRateLimitMile(), request.getRateLimitingUnit()).intValue());
        }

        if (request.getActivityType() == 4) {
            if (CollectionUtils.isEmpty(request.getRunningGoals())) {
                return CommonResult.fail("活动跑步目标不能为空");
            }
            if (CollectionUtils.isEmpty(request.getRunningGoalsAward())) {
                return CommonResult.fail("活动跑步目标奖励规则不能为空");
            }
        }

        if (request.getRunningGoalsUnit() == 0) {
            request.setCompleteRuleType(1);
            if (!CollectionUtils.isEmpty(request.getRunningGoals())) {
                request.setRunMileage(BigDecimalUtil.multiply(new BigDecimal(request.getRunningGoals().get(0)), new BigDecimal(1000)));
                request.setRunTime(0);
            }
        } else if (request.getRunningGoalsUnit() == 1) {
            request.setCompleteRuleType(1);
            if (!CollectionUtils.isEmpty(request.getRunningGoals())) {
                request.setRunMileage(BigDecimalUtil.multiply(new BigDecimal(request.getRunningGoals().get(0)), new BigDecimal(1600)));
                request.setRunTime(0);
            }
        } else if (request.getRunningGoalsUnit() == 2) {
            request.setCompleteRuleType(2);
            if (!CollectionUtils.isEmpty(request.getRunningGoals())) {
                request.setRunTime(BigDecimalUtil.multiply(new BigDecimal(request.getRunningGoals().get(0)), new BigDecimal(60)).intValue());
                request.setRunMileage(BigDecimal.ZERO);
            }
        }

        if (request.getActivityType() == 3) {
            if (CollectionUtils.isEmpty(request.getRunningGoals())) {
                return CommonResult.fail("活动跑步目标不能为空");
            }
            if (Objects.isNull(request.getOfficialChallengeAward()) || request.getOfficialChallengeAward().isEmpty()) {
                return CommonResult.fail("排行赛跑-挑战成功奖励金额配置不能为空");
            }
            if (Objects.isNull(request.getOfficialEventAward()) || request.getOfficialEventAward().isEmpty()) {
                return CommonResult.fail("排行赛跑-排名奖励不能为空");
            }

            if (Objects.isNull(request.getBeChallengedAward()) || request.getBeChallengedAward().isEmpty()) {
                return CommonResult.fail("排行赛跑-被挑战奖励金额配置不能为空");
            }
            if (Objects.isNull(request.getChallengeFailureAward())) {
                return CommonResult.fail("排行赛跑-挑战失败金额不能为空");
            }
        }
        if (request.getActivityType() == 5) {
            if (CollectionUtils.isEmpty(request.getMilepostAward())) {
                return CommonResult.fail("累计跑-里程碑奖励规则不能为空");
            }
            BigDecimal milepost = BigDecimal.ZERO;
            BigDecimal cumulativeAward = BigDecimal.ZERO;
            int scoreNum = 0;


            for (final MilepostAwardDto awardDto : request.getMilepostAward()) {
                if (Objects.nonNull(awardDto.getCumulativeAward())) {
                    if (awardDto.getCumulativeAward().compareTo(cumulativeAward) < 0) {
                        return CommonResult.fail("累计跑-里程碑奖励金额不符合规范，请检查");
                    }
                    final BigDecimal award = awardDto.getCumulativeAward().subtract(cumulativeAward);
                    awardDto.setAward(award);
                    cumulativeAward = awardDto.getCumulativeAward();
                }
                if (Objects.nonNull(awardDto.getScoreNum()) && awardDto.getScoreNum() != 0) {
                    if (awardDto.getScoreNum() - scoreNum < 0) {
                        return CommonResult.fail("累计跑-里程碑奖励积分不符合规范，请检查");
                    }
                    awardDto.setScore(awardDto.getScoreNum() - scoreNum);
                    scoreNum = awardDto.getScoreNum();
                }

                if (request.getRunningGoalsUnit() == 0) {
                    awardDto.setMilepost(awardDto.getMilepost().multiply(new BigDecimal(1000)));
                } else if (request.getRunningGoalsUnit() == 1) {
                    awardDto.setMilepost(awardDto.getMilepost().multiply(new BigDecimal(1600)));
                } else if (request.getRunningGoalsUnit() == 2) {
                    awardDto.setMilepost(awardDto.getMilepost().multiply(new BigDecimal(60)));
                }
                if (awardDto.getMilepost().compareTo(milepost) <= 0) {
                    return CommonResult.fail("累计跑-里程碑目标值不符合规范，请检查");
                }
                milepost = awardDto.getMilepost();
            }

            if (!CollectionUtils.isEmpty(request.getMilepostCouponAward())) {
                for (final MilepostAwardDto awardDto : request.getMilepostCouponAward()) {
                    if (request.getRunningGoalsUnit() == 0) {
                        awardDto.setMilepost(awardDto.getMilepost().multiply(new BigDecimal(1000)));
                    } else if (request.getRunningGoalsUnit() == 1) {
                        awardDto.setMilepost(awardDto.getMilepost().multiply(new BigDecimal(1600)));
                    } else if (request.getRunningGoalsUnit() == 2) {
                        awardDto.setMilepost(awardDto.getMilepost().multiply(new BigDecimal(60)));
                    }
                }
            }
            if (!CollectionUtils.isEmpty(request.getMilepostWearsAward())) {
                request.getMilepostWearsAward().forEach(wearsAward -> {
                    if (request.getRunningGoalsUnit() == 0) {
                        wearsAward.setMilepost(wearsAward.getMilepost().multiply(new BigDecimal(1000)));
                    } else if (request.getRunningGoalsUnit() == 1) {
                        wearsAward.setMilepost(wearsAward.getMilepost().multiply(new BigDecimal(1600)));
                    } else if (request.getRunningGoalsUnit() == 2) {
                        wearsAward.setMilepost(wearsAward.getMilepost().multiply(new BigDecimal(60)));
                    }
                });
            }

            if (request.getCompleteRuleType() == 1) {
                request.setRunMileage(milepost);
                request.setRunTime(0);
            } else {
                request.setRunTime(milepost.intValue());
                request.setRunMileage(BigDecimal.ZERO);
            }
        }
        if (!CollectionUtils.isEmpty(request.getMutexActivityIdList())) {
            request.setMutexActivityIds(request.getMutexActivityIdList().stream().map(i -> String.valueOf(i)).collect(Collectors.joining(",")));
        } else {
            request.setMutexActivityIds("-1");
        }
        if (!CollectionUtils.isEmpty(request.getBrandRightsInterestList())) {
            final BrandRightsInterestListDto dto = request.getBrandRightsInterestList().get(0);
            if (Objects.isNull(request.getPrivilegeBrand()) || request.getPrivilegeBrand() == -1) {
                request.setPrivilegeBrand(dto.getBrand());
            }
        }

        return null;
    }

    public void push(final Long activityId, final Long activityRouteId, final ZonedDateTime activityStartTime, final MindUserMatch m, final ZnsUserEntity currentUser, final ZnsUserEntity matchUser,
                     final Integer routeType, final BigDecimal activityEntryFee) {
        final Map<String, Object> da = new HashMap<>();


        da.put("activityId", activityId);
        da.put("activityStartTime", activityStartTime);
        da.put("userId", m.getUserId());
        da.put("userNickName", currentUser.getFirstName());
        da.put("userHeadPortrait", currentUser.getHeadPortrait());

        da.put("matchUserId", m.getMatchUserId());
        da.put("matchUserNickName", matchUser.getFirstName());
        da.put("matchUserHeadPortrait", matchUser.getHeadPortrait());
        da.put("activityRouteId", activityRouteId); //环形跑道
        da.put("startMinutes", 5);
        da.put("targetMileage", m.getTargetMileage());   // 目标里程
        da.put("targetTime", m.getTargetTime());   // 目标时长
        da.put("routeType", routeType);

        da.put("activityEntryFee", activityEntryFee);

        final String content = JsonUtil.writeString(da);
        log.info("推送的数据为：" + content);
        // 发送消息 ,  默认的房间号为-2，此时因为用户还没有进房间，因此默认的房间号为-2
        socketPushUtils.push(-2l, SocketEventEnums.MIND_MATCH_MESSAGE.getCode(), da);
    }

    public MindUserMatch build(final Long userId, final Integer status, final Integer isRobot, final Long mindUserMatchId, final ZonedDateTime fiveActivityTime, final BigDecimal runMileage, final Long activityId, final String runMode) {
        final MindUserMatch mindUserMatch = new MindUserMatch();
        mindUserMatch.setUserId(userId);
        mindUserMatch.setStatus(status);
        mindUserMatch.setIsRobot(isRobot);
        mindUserMatch.setMindUserMatchId(mindUserMatchId);
        mindUserMatch.setActivityEndTime(fiveActivityTime);
        mindUserMatch.setTargetMileage(runMileage.intValue());
        mindUserMatch.setActivityId(activityId);
        mindUserMatch.setRunMode(runMode);
        final String uniqueCode = OrderUtil.getUniqueCode("un");
        mindUserMatch.setUniqueCode(uniqueCode);
        return mindUserMatch;
    }

    private Result launchActivityDeal(final RunActivityRequest request, final ZnsUserEntity robotUser) {
        //跑步目标处理
        String activityTitle = "";
        if (request.getRunningGoalsUnit() == 0) {
            request.setCompleteRuleType(1);
            request.setRunMileage(BigDecimalUtil.multiply(new BigDecimal(1000), request.getRunningGoals()));
            activityTitle = request.getRunningGoals() + "Km ";
        } else if (request.getRunningGoalsUnit() == 1) {
            request.setCompleteRuleType(1);
            request.setRunMileage(BigDecimalUtil.multiply(new BigDecimal(1600), request.getRunningGoals()));
            activityTitle = request.getRunningGoals() + "Miles ";
        } else if (request.getRunningGoalsUnit() == 2) {
            request.setCompleteRuleType(2);
            final BigDecimal bigDecimal = new BigDecimal(60);
            request.setRunTime(bigDecimal.multiply(request.getRunningGoals()));
            activityTitle = request.getRunningGoals() + "min ";
        }
        if (request.getActivityConfigId() == 1) {
            activityTitle = activityTitle + "Team Run";
        } else if (request.getActivityConfigId() == 2 || request.getActivityConfigId() == 9) {
            activityTitle = activityTitle + "PK Run";
        }
        request.setActivityTitle(activityTitle);
        if (Objects.isNull(request.getActivityEntryFee())) {
            request.setActivityEntryFee(BigDecimal.ZERO);
        }
        if (request.getActivityConfigId() == 2 || request.getActivityConfigId() == 9) {
            final ZonedDateTime currentDate = DateUtil.formateDate(ZonedDateTime.now().plusMinutes(1), DateUtil.YYYY_MM_DD_HH_MM);
            final ZonedDateTime activityStartTime = currentDate.plusMinutes(sysConfigService.selectActivityStartMinite(robotUser.getId()));
            request.setActivityStartTime(activityStartTime.toInstant().toEpochMilli());
        }

        // 校验发起跑活动参数
        final Result errorResult = runActivityBizService.checkRunActivityParams(request, robotUser);
        if (null != errorResult) {
            log.info("发起活动失败,原因：" + errorResult.getMsg());
            return CommonResult.fail("发起活动失败,原因：" + errorResult.getMsg());
        }
        request.setCreateSource(ActivityCreateSourceEnum.MANAGEMENT_BACKEND.getStatusCode());
        final Result result = activityStrategyContext.doLaunchActivity(request, robotUser);
//        {"code":"200","msg":"success","data":{"activityId":60}}
        log.info("后台发起跑步活动结果:" + JsonUtil.writeString(result));

        final Integer code = result.getCode();
        final String msg = result.getMsg();
        if (200 == code) {
            final Map<String, Object> data = JsonUtil.readValue(result.getData());
            final Long activityId = MapUtil.getLong((data.get("activityId")));


            final BigDecimal awardAmount = MapUtil.getBigDecimal(data.get("awardAmount"));
            znsRunActivityBussiness.addFollowRecord(activityId);

            final ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
            final ZonedDateTime fiveActivityTime = DateUtil.addHours(znsRunActivityEntity.getActivityStartTime(), 5);

            final ZonedDateTime fiveMinutes = DateUtil.formatMinites(ZonedDateTime.now().plusMinutes(sysConfigService.selectActivityStartMinite(robotUser.getId()) + 1));

            final ZnsRunRouteEntity znsRunRouteEntity = runRouteService.selectRunRouteById(znsRunActivityEntity.getActivityRouteId());

            if (StringUtils.isEmpty(request.getRunMode())) {
                final RobotRunMode robotRunMode = robotRunModeService.getRobotRunMode();
                request.setRunMode(robotRunMode.getMode());
            }

            MindUserMatch robotMindUserMatch = null;
            if (znsRunActivityEntity.getActivityType() == 2) { // 2表示挑战跑
                //查询被邀请人
                final ZnsRunActivityUserEntity matchActivityUser = runActivityUserService.findActivityUser(activityId, request.getActivityUserIds().get(0));
                final ZnsUserEntity matchUser = userService.findById(matchActivityUser.getUserId());

                final MindUserMatch m = build(robotUser.getId(), 1, 1, matchUser.getId(), fiveActivityTime, znsRunActivityEntity.getRunMileage(), znsRunActivityEntity.getId(), request.getRunMode());
                m.setMatchUserId(matchUser.getId());
                mindUserMatchManager.insertMindUserMatch(m);

                robotMindUserMatch = m;

                final MindUserMatch n = build(matchUser.getId(), 1, 0, m.getId(), fiveActivityTime, znsRunActivityEntity.getRunMileage(), znsRunActivityEntity.getId(), "");
                n.setMatchUserId(robotUser.getId());
                mindUserMatchManager.insertMindUserMatch(n);
                m.setMindUserMatchId(n.getId());
                mindUserMatchBizService.updateMindUserMatchById(m);

                // 更新开始时间 ，因为这个才是整点的
                final UserFriendMatchVo userFriendMatchVo = new UserFriendMatchVo();
                userFriendMatchVo.setBonusRuleType(znsRunActivityEntity.getBonusRuleType());
                userFriendMatchVo.setActivityId(znsRunActivityEntity.getId());
                userFriendMatchVo.setActivityStartTime(znsRunActivityEntity.getActivityStartTime());
                userFriendMatchVo.setAwardAmount(awardAmount);
                userFriendMatchVo.setUserId(robotUser.getId());
                userFriendMatchVo.setMatchFriendId(matchUser.getId());
                userFriendMatchVo.setActivityEntryFee(request.getActivityEntryFee());
                userFriendMatchVo.setPassword(request.getPassword());
                userFriendMatchVo.setRunMileage(znsRunActivityEntity.getRunMileage());
                userFriendMatchVo.setRunTime(znsRunActivityEntity.getRunTime());
                userFriendMatchVo.setIsFriend(1);
                userFriendMatchService.friendMatch(userFriendMatchVo);
                push(activityId, znsRunActivityEntity.getActivityRouteId(), fiveMinutes, m, robotUser, matchUser, znsRunRouteEntity.getRouteType(), request.getActivityEntryFee());
                push(activityId, znsRunActivityEntity.getActivityRouteId(), fiveMinutes, n, matchUser, robotUser, znsRunRouteEntity.getRouteType(), request.getActivityEntryFee());


            } else if (znsRunActivityEntity.getActivityType() == 1) {
                // 4.3时再次确认activityType=1已无
                robotMindUserMatch = mindUserMatchBizService.addRobotMindUserMatch(robotUser.getId(), znsRunActivityEntity.getId(), znsRunActivityEntity.getRunMileage(), request.getRunMode(), fiveActivityTime);
                //推送消息
                final ImMessageBo bo = new ImMessageBo();
                bo.setActivityType("1");
                bo.setUserName(robotUser.getFirstName());
                final String routeConfig = znsRunRouteEntity.getRouteConfig();
                final Map<String, Object> jsonObject = JsonUtil.readValue(routeConfig);
                bo.setImage(MapUtil.getString(jsonObject.get("routeThumbnail")));
                bo.setDetailID(znsRunActivityEntity.getId().toString());
                bo.setChallengeType("1");
                bo.setMoney(awardAmount.setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                bo.setState("0");
                final long time = znsRunActivityEntity.getActivityStartTime().toInstant().toEpochMilli();
                bo.setTime(String.valueOf(time));
                bo.setActivityEntryFee(znsRunActivityEntity.getActivityEntryFee().toString());
                //最多两位小数显示
                final BigDecimal newRunningGoal = request.getRunningGoals().setScale(2, BigDecimal.ROUND_DOWN);
                bo.setDistance(newRunningGoal.toString());
                if (request.getRunningGoalsUnit() == 0) {
                    bo.setDistance(request.getRunningGoals().toString());
                    bo.setIsKph("true");
                } else if (request.getRunningGoalsUnit() == 1) {
                    bo.setIsKph("false");
                }
                bo.setBusinessID("match");
                final List<ZnsUserEntity> userEntities = userService.findByIds(request.getActivityUserIds());
                for (final ZnsUserEntity userEntity : userEntities) {
                    bo.setInviteUserName(userEntity.getFirstName());
                    bo.setFriendId(userEntity.getId().toString());
                    tencentImUtil.batchSendMsg(1, robotUser.getId().toString(), Arrays.asList(userEntity.getId().toString()), TencentImConstant.TIM_CUSTOM_ELEM, JsonUtil.writeString(bo));
                }
            }

            if (Objects.equals(znsRunRouteEntity.getRouteType(), 2)) {   // 表示3D路线
                // 47.110.167.249:8001/add-robot?userid=机器人id&roomid=要加入的房间号&roadid=跑道id&firstname=&lastname=&avatar=头像url&gender=性别(1：男，2：女)
                GamePushUtils.addRobot(gameDomain, activityId, znsRunActivityEntity.getActivityRouteId(), robotUser, znsRunActivityEntity.getId(), robotMindUserMatch.getId());
            }

            // 在线或离线推送
            socketPushUtils.onlinePush(activityId, robotUser, 1);
        } else {
            return CommonResult.fail(msg);
        }

        return CommonResult.success();
    }

    private void saveTeamAndRewardConfig(final TeamAndRewardConfig teamAndRewardConfig, final ZnsRunActivityEntity activity) {
        final List<TeamConfig> teamConfig = teamAndRewardConfig.getTeamConfig();
//        List<RewardConfig> rewardConfig = teamAndRewardConfig.getRewardConfig();
        //保存团队配置
        if (!CollectionUtils.isEmpty(teamConfig)) {
            final List<ActivityTeam> teamList = teamConfig.stream().map(k -> {
                final ActivityTeam team = new ActivityTeam();
                team.setActivityId(activity.getId());
                team.setTeamLogo(k.getTeamLogo());
                team.setTeamName(k.getTeamName());
                team.setMaxNum(teamAndRewardConfig.getTeamMaxNum());
                return team;
            }).collect(Collectors.toList());
            //查询该活动是否有队伍配置
            final List<ActivityTeam> teamsByActivityId = activityTeamService.getTeamsByActivityId(activity.getId());
            if (!CollectionUtils.isEmpty(teamsByActivityId)) {
                activityTeamService.deleteByActivityId(activity.getId());
            }
            activityTeamService.saveBatch(teamList);
        }
        //查询是否该活动有没有奖励配置,先删后改
        final List<ActivityAwardConfig> activityAwardConfigs = activityAwardConfigService.list(new QueryWrapper<ActivityAwardConfig>().lambda()
                .eq(ActivityAwardConfig::getActivityId, activity.getId())
                .eq(ActivityAwardConfig::getIsDelete, 0));
        if (!CollectionUtils.isEmpty(activityAwardConfigs)) {
            activityAwardConfigService.deleteActivityAwardConfigByActivityId(activity.getId());
            final List<Long> awards = activityAwardConfigs.stream().map(ActivityAwardConfig::getAwardId).collect(Collectors.toList());
            awardConfigService.deleteAllById(awards);
        }
        //保存奖励配置
        final List<AwardRelation> relations = genAwardRelations(teamAndRewardConfig, activity.getId());
        final long dur = activity.getActivityEndTime().toInstant().toEpochMilli() - ZonedDateTime.now().toInstant().toEpochMilli();
        awardProcessService.processForCache(relations, dur);
    }

    private List<AwardRelation> genAwardRelations(final TeamAndRewardConfig teamAndRewardConfig, final Long activityId) {

        final List<AwardRelation> list = new ArrayList<>();
        final List<RewardConfig> rewardConfigs = teamAndRewardConfig.getRewardConfig();
        for (final RewardConfig rewardConfig : rewardConfigs) {
            final AwardRelation relation = new AwardRelation();
            relation.setAwardSource(AwardSourceEnum.ACTIVITY);
            relation.setBindRefId(activityId);
            relation.setRank(rewardConfig.getRank());
            relation.setBaseReward(rewardConfig.getBaseReward());
            relation.setHeadReward(rewardConfig.getHeadReward());
            relation.setHeadScoreReward(rewardConfig.getHeadScoreReward());
            relation.setBaseScoreReward(rewardConfig.getBaseScoreReward());
            relation.setCouponIds(Arrays.asList(rewardConfig.getCouponId()));
            list.add(relation);
        }
        return list;
    }
}
