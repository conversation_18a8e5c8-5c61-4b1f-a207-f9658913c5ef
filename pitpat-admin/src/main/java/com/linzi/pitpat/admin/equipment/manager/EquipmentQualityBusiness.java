package com.linzi.pitpat.admin.equipment.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.equipment.converter.ErpEquipmentQualityConverter;
import com.linzi.pitpat.api.model.req.AddEquipmentQualityReq;
import com.linzi.pitpat.api.model.req.EquipmentQualityDetailReq;
import com.linzi.pitpat.api.model.req.EquipmentQualityExportReq;
import com.linzi.pitpat.api.model.req.EquipmentQualityListReq;
import com.linzi.pitpat.api.model.req.ExtraQualityAuditReq;
import com.linzi.pitpat.api.model.req.QualityDayConfigReq;
import com.linzi.pitpat.api.model.resp.EquipmentQualityDetailResp;
import com.linzi.pitpat.api.model.resp.EquipmentQualityExportResp;
import com.linzi.pitpat.api.model.resp.EquipmentQualityListResp;
import com.linzi.pitpat.api.model.resp.QualityDayConfigResp;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentQualityBiz;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentActivateRecordDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityAuditDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityDetailDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.query.EquipmentQualityAuditQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.ErpEquipmentQualityListQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillQuery;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentActivateRecordService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityAuditService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityDetailService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.vo.EquipmentQualityDayConfigVo;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.annotation.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 设备质保Business
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class EquipmentQualityBusiness {

    private final ZnsTreadmillService znsTreadmillService;
    private final ISysConfigService sysConfigService;
    private final EquipmentQualityAuditService equipmentQualityAuditService;
    private final EquipmentQualityDetailService equipmentQualityDetailService;
    private final EquipmentActivateRecordService equipmentActivateRecordService;
    private final EquipmentQualityBiz equipmentQualityBiz;
    private final ErpEquipmentQualityConverter erpEquipmentQualityConverter;

    /**
     * 获取质保天数配置
     *
     * @return
     */
    public QualityDayConfigResp getConfig() {
        //质保时间配置
        EquipmentQualityDayConfigVo qualityDayConfig = sysConfigService.selectConfigVoByKey(ConfigKeyEnums.EQUIPMENT_ACTIVATE_TIME_CONFIG.getCode(), EquipmentQualityDayConfigVo.class);
        Integer activateDayNum = qualityDayConfig == null ? 730 : Optional.ofNullable(qualityDayConfig.getActivateDayNum()).orElse(730); //激活质保天数
        Integer extraDayNum = qualityDayConfig == null ? 90 : Optional.ofNullable(qualityDayConfig.getExtraDayNum()).orElse(90); //额外质保天数
        return new QualityDayConfigResp(activateDayNum, extraDayNum);

    }

    /**
     * 新增修改保天数配置
     *
     * @param req
     */
    public void saveOrUpdateConfig(QualityDayConfigReq req) {
        if (Objects.isNull(req.getActivateDayNum())) {
            throw new BaseException("首保天数不能为空");
        }
        if (Objects.isNull(req.getExtraDayNum())) {
            throw new BaseException("app续保天数不能为空");
        }
        //查询配置
        SysConfig config = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.EQUIPMENT_ACTIVATE_TIME_CONFIG.getCode());
        EquipmentQualityDayConfigVo qualityDayConfig = JsonUtil.readValue(config.getConfigValue(), EquipmentQualityDayConfigVo.class);
        qualityDayConfig = Optional.ofNullable(qualityDayConfig).orElse(new EquipmentQualityDayConfigVo());
        //修改配置
        qualityDayConfig.setActivateDayNum(req.getActivateDayNum());
        qualityDayConfig.setExtraDayNum(req.getExtraDayNum());
        config.setConfigValue(JsonUtil.writeString(qualityDayConfig));
        log.info("[saveOrUpdateConfig]新增修改保天数配置=" + config.getConfigValue());
        sysConfigService.updateConfig(config);
    }

    /**
     * 额外质保审核
     *
     * @param req
     */
    public void extraQualityAudit(ExtraQualityAuditReq req) {
        log.info("[extraQualityAudit]----额外质保审核,开始，req={}", JsonUtil.writeString(req));
        if (CollectionUtils.isEmpty(req.getIds())) {
            throw new BaseException("审批数据不能为空");
        }
        if (req.getIds().size() > 500) {
            throw new BaseException("单次审核不能超过500个");
        }
        if (Objects.isNull(req.getAuditStatus())) {
            throw new BaseException("审核结果不能为空");
        }
        if (Objects.isNull(req.getAuditer())) {
            throw new BaseException("审核人不能为空");
        }
        if (DeviceConstant.AuditStatusEnum.REJECT.code.equals(req.getAuditStatus())
                && Objects.isNull(req.getRemark())) {
            throw new BaseException("失败原因不能为空");
        }

        //校验质保类型(激活质保、手工质保不不能审核)
        List<EquipmentQualityDetailDo> detailList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(req.getIds())) {
            detailList = equipmentQualityDetailService.findByIds(req.getIds());
        }
        if (CollectionUtils.isEmpty(detailList)) {
            throw new BaseException("设备不存在");
        }
        List<String> qualityTypes = List.of(DeviceConstant.QualityTypeEnum.ACTIVATE.code, DeviceConstant.QualityTypeEnum.MANUAL.code);
        EquipmentQualityDetailDo qualityDetailDo = detailList.stream().filter(item -> qualityTypes.contains(item.getQualityType())).findFirst().orElse(null);
        if (Objects.nonNull(qualityDetailDo)) {
            throw new BaseException("已选数据中存在不符合批量审核条件的记录");
        }

        //校验审核状态（已完成、审核不通过 不能审核）
        List<Long> qualityDetailIds = detailList.stream().map(EquipmentQualityDetailDo::getId).toList();
        EquipmentQualityAuditQuery auditQuery = new EquipmentQualityAuditQuery().setQualityDetailIds(qualityDetailIds);
        List<EquipmentQualityAuditDo> auditList = equipmentQualityAuditService.findList(auditQuery);
        List<String> auditStatusList = List.of(DeviceConstant.AuditStatusEnum.FINISHED.code, DeviceConstant.AuditStatusEnum.REJECT.code);
        EquipmentQualityAuditDo equipmentQualityAuditDo = auditList.stream().filter(item -> auditStatusList.contains(item.getAuditStatus())).findFirst().orElse(null);
        if (Objects.nonNull(equipmentQualityAuditDo)) {
            throw new BaseException("已选数据中存在不符合批量审核条件的记录");
        }

        //多线程执行审核
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        auditList.parallelStream().forEach(auditDo -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            try {
                doAudit(req, auditDo);
            } catch (Exception e) {
                log.error("[extraQualityAudit]----额外质保审核,审核失败，bluetoothMac={},req={}", auditDo.getBluetoothMac(), JsonUtil.writeString(req), e);
            }
        });
    }

    /**
     * 审核操作
     */
    private void doAudit(ExtraQualityAuditReq req, EquipmentQualityAuditDo equipmentQualityAuditDo) {
        //根据序列号查询额外质保记录
        if (equipmentQualityAuditDo != null) {
            if (DeviceConstant.AuditStatusEnum.REJECT.code.equals(req.getAuditStatus())) {
                //审核拒绝
                log.info("[extraQualityAudit]----额外质保审核,审核拒绝，bluetoothMac={},req={}", equipmentQualityAuditDo.getBluetoothMac(), JsonUtil.writeString(req));

                //计算提交截止时间
                EquipmentActivateRecordDo activateRecordDo = equipmentActivateRecordService.findByBluetoothMac(equipmentQualityAuditDo.getBluetoothMac()); //激活记录
                EquipmentQualityDayConfigVo qualityDayConfig = sysConfigService.selectConfigVoByKey(ConfigKeyEnums.EQUIPMENT_ACTIVATE_TIME_CONFIG.getCode(), EquipmentQualityDayConfigVo.class);
                int submitTimeOutDay = qualityDayConfig == null ? 30 : Optional.ofNullable(qualityDayConfig.getSubmitTimeOutDay()).orElse(30); //额外质保提交超时天数
                ZonedDateTime submitEndTime = DateUtil.addDays1(activateRecordDo.getActivateTime(), submitTimeOutDay);

                //更新数据
                EquipmentQualityAuditDo updateDo = new EquipmentQualityAuditDo();
                updateDo.setId(equipmentQualityAuditDo.getId());
                updateDo.setAuditStatus(DeviceConstant.AuditStatusEnum.REJECT.code);
                updateDo.setRemark(req.getRemark());
                updateDo.setAuditer(req.getAuditer());
                updateDo.setAuditTime(ZonedDateTime.now());
                //失败时间超过提交截止时间，则提交次数改成1（超时失败后只给1一次机会）
                if (ZonedDateTime.now().isAfter(submitEndTime) && equipmentQualityAuditDo.getLastSubmitStatus() == 0) {
                    updateDo.setLastSubmitStatus(1);
                }
                equipmentQualityAuditService.update(updateDo);
                return;
            }
            //额外质保审核通过
            equipmentQualityBiz.finishedExtraQualityAudit(equipmentQualityAuditDo, req);
        }
    }

    /**
     * ERP手动添加设备质保
     *
     * @param req
     */
    @RedisLock(value = "req.bluetoothMac", isTry = false)
    @Transactional(rollbackFor = Exception.class)
    public void addEquipmentQuality(AddEquipmentQualityReq req) {
        String bluetoothMac = req.getBluetoothMac();
        if (!StringUtils.hasText(req.getBluetoothMac())) {
            throw new BaseException("整机序列号不能为空");
        }
        //查询设备
        TreadmillQuery query = TreadmillQuery.builder().bluetoothMac(req.getBluetoothMac()).build();
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByQuery(query);
        if (treadmillEntity == null) {
            throw new BaseException("序列号查询不到设备");
        }
        //查询激活记录
        EquipmentActivateRecordDo activateRecordDo = equipmentActivateRecordService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (activateRecordDo != null && !DeviceConstant.ActivateTypeEnum.MANUAL.code.equals(activateRecordDo.getActivateType())
                && req.getDayNum() == null) {
            //续保质保天数不能为空
            throw new BaseException("续保质保天数不能为空");
        }

        //添加质保
        if (activateRecordDo == null || DeviceConstant.ActivateTypeEnum.MANUAL.code.equals(activateRecordDo.getActivateType())) {
            //添加首保(激活质保)
            log.info("[extraQualityAudit]----额外质保审核,添加首保，bluetoothMac={},req={}", bluetoothMac, JsonUtil.writeString(req));
            equipmentQualityBiz.erpActivateDevice(req, treadmillEntity, ZonedDateTime.now(), ZonedDateTime.now());
        } else {
            //添加续保(手动质保)
            log.info("[extraQualityAudit]----额外质保审核,添加续保，bluetoothMac={},req={}", bluetoothMac, JsonUtil.writeString(req));
            EquipmentQualityDetailDo qualityDetailDo = new EquipmentQualityDetailDo(treadmillEntity.getBluetoothMac(), treadmillEntity.getPrintId(), DeviceConstant.QualityTypeEnum.MANUAL.code,
                    DeviceConstant.CreateSourceEnum.ERP.code, DeviceConstant.QualityStatusEnum.QUALITY_STATUS_1.code,
                    req.getUserName(), req.getOrderNo(), req.getOrderTime(), req.getPhoneNumber(), req.getEmailAddress(), req.getDayNum(), req.getCreator(), req.getBuySource()
            );
            equipmentQualityBiz.modifyQualityTime(qualityDetailDo);
        }
    }

    /**
     * ERP质保列表查询
     *
     * @param req
     * @return
     */
    public Page<EquipmentQualityListResp> pageList(EquipmentQualityListReq req) {
        ErpEquipmentQualityListQuery query = erpEquipmentQualityConverter.toListQuery(req);
        Page<EquipmentQualityListResp> pageResp = equipmentQualityDetailService.selectPageList(Page.of(req.getPageNum(), req.getPageSize()), query);
        if (!CollectionUtils.isEmpty(pageResp.getRecords())) {
            //质保类型转成erp的类型
            for (EquipmentQualityListResp record : pageResp.getRecords()) {
                record.setErpAuditType(record.qualityTypeToErpType()); // 质保方式，1:首保，2：续保
                if (!StringUtils.hasText(record.getAuditStatus())) {
                    //没有审批状态就是无需审批
                    record.setAuditStatus("none");
                }
            }
        }
        return pageResp;
    }

    /**
     * 导出
     *
     * @param req
     * @return
     */
    public List<EquipmentQualityExportResp> export(EquipmentQualityExportReq req) {
        ErpEquipmentQualityListQuery query = erpEquipmentQualityConverter.toExportQuery(req);
        Long num = equipmentQualityDetailService.selectExportCount(query);
        if (num == 0) {
            throw new BaseException("导出失败，暂无可导出数据");
        }
        log.info("[export]----导出，req={},总数量={}", JsonUtil.writeString(req), num);
        if (num > 10000) {
            num = 10000L; //超过1w只查询1w条
        } else {
            num = null;
        }
        //查询导出记录
        List<EquipmentQualityListResp> list = equipmentQualityDetailService.selectExportList(query, num);
        List<EquipmentQualityExportResp> result = new ArrayList<>(list.size());
        for (EquipmentQualityListResp equipmentQualityListResp : list) {
            equipmentQualityListResp.setErpAuditType(equipmentQualityListResp.qualityTypeToErpType());
            EquipmentQualityExportResp resp = BeanUtil.copyBean(equipmentQualityListResp, EquipmentQualityExportResp.class);
            resp.setBuySource(equipmentQualityListResp.buildBuySourceType());
            resp.setAuditStatus(equipmentQualityListResp.buildAuditStatus());
            resp.setErpAuditType(equipmentQualityListResp.buildErpAuditType());
            resp.setCreateSource(equipmentQualityListResp.buildCreateSource());
            result.add(resp);
        }

        return result;
    }

    /**
     * 质保详情查询
     *
     * @param id
     * @return
     */
    public EquipmentQualityDetailResp detail(Long id) {
        return equipmentQualityDetailService.findDetailById(id);
    }

    /**
     * 修改质保
     *
     * @param req
     */
    public void updateQuality(EquipmentQualityDetailReq req) {
        if (req.getId() == null) {
            throw new BaseException("id 不能为空");
        }
        EquipmentQualityDetailDo qualityDetailDo = equipmentQualityDetailService.findById(req.getId());
        if (qualityDetailDo == null) {
            throw new BaseException("质保记录不存在不能为空");
        }
        if (StringUtils.hasText(req.getUserName())) {
            qualityDetailDo.setUserName(req.getUserName());
        }
        if (StringUtils.hasText(req.getBuySource())) {
            qualityDetailDo.setBuySource(req.getBuySource());
        }
        if (Objects.nonNull(req.getOrderTime())) {
            qualityDetailDo.setOrderTime(req.getOrderTime());
        }
        if (StringUtils.hasText(req.getOrderNo())) {
            qualityDetailDo.setOrderNo(req.getOrderNo());
        }
        if (StringUtils.hasText(req.getPhoneNumber())) {
            qualityDetailDo.setPhoneNumber(req.getPhoneNumber());
        }
        if (StringUtils.hasText(req.getEmailAddress())) {
            qualityDetailDo.setEmailAddress(req.getEmailAddress());
        }
        qualityDetailDo.setModifier(req.getModifier());
        equipmentQualityDetailService.update(qualityDetailDo);
    }
}
