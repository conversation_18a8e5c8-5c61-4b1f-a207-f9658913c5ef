package com.linzi.pitpat.admin.mallservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.admin.mallservice.dto.request.GoodsCommentActionRequestDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.enums.CommunityContentPublishStatusEnum;
import com.linzi.pitpat.data.enums.CommunityContentPublishTypeEnum;
import com.linzi.pitpat.data.enums.CommunityUserTypeEnum;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mallservice.converter.console.GoodsCommentBlockConsoleConverter;
import com.linzi.pitpat.data.mallservice.converter.console.GoodsCommentConsoleConverter;
import com.linzi.pitpat.data.mallservice.dto.console.request.GoodsCommentBlockCreateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.GoodsCommentImportRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.GoodsCommentPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.GoodsCommentResponseDto;
import com.linzi.pitpat.data.mallservice.enums.GoodsCommentConstant;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsCommentBlockDo;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsCommentDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsCommentPageQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsCommentQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuQuery;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsCommentMedia;
import com.linzi.pitpat.data.mallservice.service.GoodsCommentBlockService;
import com.linzi.pitpat.data.mallservice.service.GoodsCommentService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.systemservice.dto.request.CommunityPublishRequestDto;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.response.CommunityContentPicResponseDto;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContent;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContentI18nEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.service.CommunityContentI18nService;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.WeakHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsCommentManager {

    private final GoodsCommentService goodsCommentService;
    private final GoodsCommentBlockService goodsCommentBlockService;
    private final GoodsCommentConsoleConverter goodsCommentConsoleConverter;
    private final GoodsCommentBlockConsoleConverter goodsCommentBlockConsoleConverter;
    private final ZnsGoodsSkuService goodsSkuService;
    private final ZnsGoodsService goodsService;
    private final CommunityContentService communityContentService;
    private final ZnsUserService userService;
    private final ISysConfigService sysConfigService;
    private final UserKolService userKolService;
    private final CommunityContentI18nService communityContentI18nService;
    //private final CommunityTopicContentRelationService communityTopicContentRelationService;
    //private final CommunityTopicService communityTopicService;


    /**
     * 商品评论列表查询
     */
    @FillerMethod
    public Page<GoodsCommentResponseDto> pageList(GoodsCommentPageQueryDto pageQueryDto) {
        GoodsCommentPageQuery pageQuery = goodsCommentConsoleConverter.toPageQuery(pageQueryDto);
        Page<GoodsCommentDo> page = goodsCommentService.findPage(pageQuery);

        Page<GoodsCommentResponseDto> result = goodsCommentConsoleConverter.toDtoPage(page);

        List<GoodsCommentResponseDto> records = result.getRecords();
        fillProductInfo(records, result);
        result.setRecords(records);
        return result;
    }

    /**
     * 填充商品信息： productTitle 和 skuPic
     *
     * @param records
     * @param result
     */
    private void fillProductInfo(List<GoodsCommentResponseDto> records, Page<GoodsCommentResponseDto> result) {
        if (!CollectionUtils.isEmpty(records)) {

            List<Long> goodsIdList = records.stream().map(GoodsCommentResponseDto::getGoodsId).toList();
            List<Long> goodsSkuIdList = records.stream().map(GoodsCommentResponseDto::getSkuId).toList();

            List<ZnsGoodsEntity> goodsList = goodsService.findByIds(goodsIdList);
            List<ZnsGoodsSkuEntity> goodsSkuList = goodsSkuService.findByIds(goodsSkuIdList);

            Map<Long, ZnsGoodsEntity> goodsMap = new HashMap<>();
            Map<Long, ZnsGoodsSkuEntity> goodsSkuMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(goodsList)) {
                goodsMap = goodsList.stream().collect(Collectors.toMap(ZnsGoodsEntity::getId, Function.identity()));
            }
            if (!CollectionUtils.isEmpty(goodsSkuList)) {
                goodsSkuMap = goodsSkuList.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, Function.identity()));
            }
            List<GoodsCommentResponseDto> dtoRecords = result.getRecords();

            for (GoodsCommentResponseDto item : dtoRecords) {
                ZnsGoodsEntity product = goodsMap.get(item.getGoodsId());
                ZnsGoodsSkuEntity productSku = goodsSkuMap.get(item.getSkuId());
                if (Objects.nonNull(product) && Objects.nonNull(productSku)) {
                    item.setGoodsName(product.getTitle() + "-" + productSku.getPropertyValues());
                    item.setGoodsSkuImgUrl(productSku.getPic());
                }
            }
        }
    }

    /**
     * 商品评论操作
     *
     * @param requestDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void operate(GoodsCommentActionRequestDto requestDto) {
        GoodsCommentConstant.ConsoleActionEnum actionEnum = GoodsCommentConstant.ConsoleActionEnum.resolve(requestDto.getActionType());
        if (Objects.isNull(actionEnum)) {
            throw new BaseException("非法操作：" + requestDto.getActionType(), CommonError.PARAM_ERROR.getCode());
        }

        log.info("operate,action={}, ids={}", requestDto.getActionType(), requestDto.getIdList());
        List<Long> goodsCommentIdList = requestDto.getIdList();
        if (Objects.equals(GoodsCommentConstant.ConsoleActionEnum.BLOCK, actionEnum)) {
            //屏蔽
            List<GoodsCommentBlockDo> goodsCommentBlockDoList = goodsCommentBlockConsoleConverter.toDtoList(requestDto.getBlockReasonList());
            if (CollectionUtils.isEmpty(goodsCommentBlockDoList)) {
                throw new BaseException("屏蔽理由不能为空", CommonError.PARAM_ERROR.getCode());
            }

            List<GoodsCommentDo> updateGoodsCommentList = new ArrayList<>();
            requestDto.getIdList().forEach(commentId -> {
                List<GoodsCommentBlockDo> createBlockList = goodsCommentBlockDoList.stream().map(item -> {
                    item.setGoodsCommentId(commentId);
                    return item;
                }).toList();

                Optional<GoodsCommentBlockCreateRequestDto> optional = requestDto.getBlockReasonList().stream().filter(item -> Objects.equals(item.getDefaultLanguageCode(), item.getLanguageCode())).findFirst();
                if (optional.isEmpty()) {
                    throw new BaseException("默认屏蔽理由不能为空, commentId:" + commentId, CommonError.PARAM_ERROR.getCode());
                }
                GoodsCommentDo goodsComment = new GoodsCommentDo();
                goodsComment.setId(commentId);
                goodsComment.setIsShow(GoodsCommentConstant.ShowStatusEnum.HIDDEN.getCode());
                //同步屏蔽理由
                goodsComment.setDefaultBlockReason(optional.get().getBlockReason());
                goodsComment.setDefaultLanguageCode(optional.get().getLanguageCode());

                updateGoodsCommentList.add(goodsComment);
                goodsCommentBlockService.batchCreate(createBlockList);
            });
            //更新评论
            goodsCommentService.batchUpdate(updateGoodsCommentList);

        } else if (Objects.equals(GoodsCommentConstant.ConsoleActionEnum.UN_BLOCK, actionEnum)) {
            //取消屏蔽
            requestDto.getIdList().forEach(item -> {
                GoodsCommentDo goodsComment = new GoodsCommentDo();
                goodsComment.setId(item);
                goodsComment.setIsShow(GoodsCommentConstant.ShowStatusEnum.SHOW.getCode());
                goodsCommentService.updateSelective(goodsComment);
            });
            //删除屏蔽理由
            goodsCommentBlockService.deleteByCommentIds(goodsCommentIdList);
        } else if (Objects.equals(GoodsCommentConstant.ConsoleActionEnum.SYNCED, actionEnum)) {
            List<GoodsCommentDo> list = requestDto.getIdList().stream().map(item -> {
                GoodsCommentDo goodsComment = new GoodsCommentDo();
                goodsComment.setId(item);
                goodsComment.setSyncStatus(GoodsCommentConstant.ConsoleActionEnum.SYNCED.getCode());
                return goodsComment;
            }).toList();
            log.info("action={},update goodsCommentList={}", actionEnum, list);
            goodsCommentService.batchUpdate(list);
            //同步到社区
            publishContent(goodsCommentIdList);
            //同步
        } else if (Objects.equals(GoodsCommentConstant.ConsoleActionEnum.UN_SYNCED, actionEnum)) {
            List<GoodsCommentDo> existedGoodsCommentList = goodsCommentService.findListById(goodsCommentIdList);
            List<Long> commentIdList = existedGoodsCommentList.stream().map(GoodsCommentDo::getCommunityId).filter(item -> item > 0).toList();
            //取消同步
            List<GoodsCommentDo> list = requestDto.getIdList().stream().map(item -> {
                GoodsCommentDo goodsComment = new GoodsCommentDo();
                goodsComment.setId(item);
                goodsComment.setCommunityId(0L);
                goodsComment.setSyncStatus(GoodsCommentConstant.ConsoleActionEnum.UN_SYNCED.getCode());
                return goodsComment;
            }).toList();
            goodsCommentService.batchUpdate(list);
            //取消同步社区
            unPublishContent(existedGoodsCommentList);
            log.info("action={},delete commentIdList={}", actionEnum, commentIdList);
            if (!CollectionUtils.isEmpty(commentIdList)) {
                communityContentService.deleteByIds(commentIdList);
            }
        }
    }


    public void importExcel(List<GoodsCommentImportRequestDto> list) {
        List<String> userCodeList = list.stream().map(GoodsCommentImportRequestDto::getUserCode).toList();
        List<ZnsUserEntity> userList = userService.findList(UserQuery.builder().isRobot(1).isDelete(0).userCodeList(userCodeList).build());

        Map<String, ZnsUserEntity> userMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getUserCode, Function.identity()));
        list.forEach(item -> {
            item.setUserId(userMap.get(item.getUserCode()).getId());
            if (StringUtils.hasText(item.getContent())) {
                if (StringUtils.hasText(item.getVideoUrl())) {
                    item.setContentType(GoodsCommentConstant.ContentTypeEnum.VIDEO_TEXT.getCode());
                } else if (StringUtils.hasText(item.getImageUrl())) {
                    item.setContentType(GoodsCommentConstant.ContentTypeEnum.IMAGE_TEXT.getCode());
                } else {
                    item.setContentType(GoodsCommentConstant.ContentTypeEnum.TEXT.getCode());
                }
            } else {
                if (StringUtils.hasText(item.getVideoUrl())) {
                    item.setContentType(GoodsCommentConstant.ContentTypeEnum.VIDEO.getCode());
                } else if (StringUtils.hasText(item.getImageUrl())) {
                    item.setContentType(GoodsCommentConstant.ContentTypeEnum.IMAGE.getCode());
                }
            }
            if (StringUtils.hasText(item.getVideoUrl())) {
                GoodsCommentMedia commentMedia = new GoodsCommentMedia();
                commentMedia.setType(GoodsCommentConstant.ContentTypeEnum.VIDEO.getCode());
                commentMedia.setUrl(item.getImageUrl());
                commentMedia.setVideoUrl(item.getVideoUrl());
                item.setMedias(List.of(commentMedia));
            } else if (StringUtils.hasText(item.getImageUrl())) {
                String[] imgUrls = item.getImageUrl().split(";");
                List<GoodsCommentMedia> commentMediaList = Arrays.stream(imgUrls).map(url -> {
                    GoodsCommentMedia commentMedia = new GoodsCommentMedia();
                    commentMedia.setType(GoodsCommentConstant.ContentTypeEnum.IMAGE.getCode());
                    commentMedia.setUrl(url);
                    return commentMedia;
                }).toList();
                item.setMedias(commentMediaList);
            }

        });

        List<GoodsCommentDo> goodsCommentDoList = goodsCommentConsoleConverter.toDoListFromImport(list);
        goodsCommentService.batchCreate(goodsCommentDoList);
    }

    //校验并导入
    public List<String> validateExcel(List<GoodsCommentImportRequestDto> list) {
        List<String> msgList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            msgList.add("表格内容为空");
            return msgList;
        }
        if (list.size() > 1000) {
            msgList.add("表格内容超过1000上限");
            return msgList;
        }
        //其他情况下一起返回

        //userCode 校验
        List<String> userCodeList = list.stream().map(GoodsCommentImportRequestDto::getUserCode).toList();
        List<ZnsUserEntity> userList = userService.findList(UserQuery.builder().isRobot(1).isDelete(0).userCodeList(userCodeList).build());

        boolean validUserMsg = true;
        if (CollectionUtils.isEmpty(userList)) {
            msgList.add("userId有误，用户不存在或不是机器人");
            validUserMsg = false;
        } else {
            Map<String, ZnsUserEntity> userMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getUserCode, Function.identity()));
            String userCodeMsg = list.stream().filter(item -> Objects.isNull(userMap.get(item.getUserCode()))).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
            if (StringUtils.hasText(userCodeMsg)) {
                msgList.add("userId有误，用户不存在或不是机器人，请检查：" + userCodeMsg + " 行");
                validUserMsg = false;
            }
        }

        //spu\ sku
        List<Long> skuIds = list.stream().map(GoodsCommentImportRequestDto::getSkuId).toList();
        List<ZnsGoodsSkuEntity> goodsSkuList = goodsSkuService.findList(new GoodsSkuQuery().setIds(skuIds));

        if (CollectionUtils.isEmpty(goodsSkuList)) {
            msgList.add("skuid有误，没有获取到商品sku信息");
        } else {
            Map<Long, ZnsGoodsSkuEntity> skuMap = goodsSkuList.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, Function.identity()));

            String skuIdMsg = list.stream().filter(item -> Objects.isNull(skuMap.get(item.getSkuId()))).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
            if (StringUtils.hasText(skuIdMsg)) {
                msgList.add("skuid有误，没有获取到sku信息，请检查：" + skuIdMsg + " 行");
            } else {
                //skuid 错误信息为空则说明  skuid 都存在
                String spuIdMsg = list.stream().filter(item -> !Objects.equals(item.getGoodsId(), skuMap.get(item.getSkuId()).getGoodsId())).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
                if (StringUtils.hasText(spuIdMsg)) {
                    msgList.add("spuid有误，spu不存在或与sku不匹配，请检查：" + spuIdMsg + " 行");
                } else {
                    if (validUserMsg) {
                        Map<String, ZnsUserEntity> userMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getUserCode, Function.identity()));

                        //判断是否重复评价
                        spuIdMsg = list.stream().filter(item -> {
                            GoodsCommentDo existedGoodsComment = goodsCommentService.findByQuery(new GoodsCommentQuery().setUserId(userMap.get(item.getUserCode()).getId()).setGoodsId(item.getGoodsId()).setSkuId(item.getSkuId()).setIsDelete(0));
                            return Objects.nonNull(existedGoodsComment);
                        }).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));

                        if (StringUtils.hasText(spuIdMsg)) {
                            msgList.add("skuid有误，用户已经评价过该商品sku，请检查：" + spuIdMsg + " 行");
                        }
                    }
                }
            }
        }
        //内容检测


        //内容为空
        String contentMsg = list.stream().filter(item -> !StringUtils.hasText(item.getContent()) && !StringUtils.hasText(item.getImageUrl()) && !StringUtils.hasText(item.getVideoUrl())).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.hasText(contentMsg)) {
            msgList.add("评论内容，内容/图片/视频不能全为空，请检查：" + contentMsg + " 行");
        } else {
            //内容超过限制
            contentMsg = list.stream().filter(item -> item.getContent().length() > 150).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
            if (StringUtils.hasText(contentMsg)) {
                msgList.add("评论内容超过150字符限制请检查，请检查：" + contentMsg + " 行");
            }

            //敏感词检测
            if (!StringUtils.hasText(contentMsg)) {
                String json = sysConfigService.selectConfigByKey("goods.comment.sensitiveWord");//敏感词库
                WeakHashMap<String, Set<String>> sensitiveWordMap = JsonUtil.readValue(json, new TypeReference<>() {
                });
                if (Objects.nonNull(sensitiveWordMap)) {
                    Set<String> set = sensitiveWordMap.get(UserContextHolder.getLocal());
                    log.info("local={},sensitiveWord={}", UserContextHolder.getLocal(), set);
                    if (!CollectionUtils.isEmpty(set)) {

                        contentMsg = list.stream().filter(item -> set.stream().anyMatch(dict -> StringUtils.hasText(item.getContent()) && item.getContent().toLowerCase().contains(dict))).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
                        if (StringUtils.hasText(contentMsg)) {
                            msgList.add("评论内容有违禁词请检查，请检查：" + contentMsg + " 行");
                        }
                    }
                }
            }
        }
        //String mediaMsg = list.stream().filter(item -> !StringUtils.hasText(item.getImageUrl()) && !StringUtils.hasText(item.getVideoUrl())).map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
        //if (StringUtils.hasText(mediaMsg)) {
        //    msgList.add("视频/图片数据有问题，不能为都空，请检查：" + mediaMsg + " 行");
        //} else {
        String mediaMsg = list.stream().filter(item -> StringUtils.hasText(item.getVideoUrl()) && !StringUtils.hasText(item.getImageUrl()))
                .map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.hasText(mediaMsg)) {
            msgList.add("视频/图片数据有问题，视频封面不能为空，请检查：" + mediaMsg + " 行");
        } else {
            mediaMsg = list.stream().filter(item -> StringUtils.hasText(item.getImageUrl()) && !(item.getImageUrl().startsWith("http://") || item.getImageUrl().startsWith("https://")) ||
                            StringUtils.hasText(item.getVideoUrl()) && !(item.getVideoUrl().startsWith("http://") || item.getVideoUrl().startsWith("https://")))
                    .map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
            if (StringUtils.hasText(mediaMsg)) {
                msgList.add("视频/图片数据有问题，不是合法的url，请检查：" + mediaMsg + " 行");
            }
        }

        String scoreMsg = list.stream().filter(item -> Objects.isNull(item.getScore()))
                .map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.hasText(scoreMsg)) {
            msgList.add("评分数据有问题，评分不能为空，请检查：" + scoreMsg + " 行");
        } else {
            List<Double> socreList = List.of(0.5d, 1d, 1.5d, 2d, 2.5d, 3d, 3.5d, 4d, 4.5d, 5d);

            scoreMsg = list.stream().filter(item -> socreList.stream().noneMatch(score -> Objects.equals(item.getScore().doubleValue(), score)))
                    .map(item -> item.getIndex() + 1).sorted().map(String::valueOf).collect(Collectors.joining(","));
            if (StringUtils.hasText(scoreMsg)) {
                msgList.add("评分数据有问题，评分不在(0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5)范围内，请检查：" + scoreMsg + " 行");
            }
        }
        //}
        return msgList;
    }

    /**
     * 同步评价到社区
     *
     * @param ids
     */
    private void publishContent(List<Long> ids) {
        List<GoodsCommentDo> goodsCommentDoList = goodsCommentService.findListById(ids);
        if (!CollectionUtils.isEmpty(goodsCommentDoList)) {
            List<String> imageContents = List.of(GoodsCommentConstant.ContentTypeEnum.IMAGE.getCode(), GoodsCommentConstant.ContentTypeEnum.IMAGE_TEXT.code);
            List<String> videoContents = List.of(GoodsCommentConstant.ContentTypeEnum.VIDEO.getCode(), GoodsCommentConstant.ContentTypeEnum.VIDEO_TEXT.code);
            Set<Long> userIds = goodsCommentDoList.stream().map(GoodsCommentDo::getUserId).collect(Collectors.toSet());
            List<ZnsUserEntity> userList = userService.findByIds(userIds);
            Map<Long, ZnsUserEntity> userMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
            goodsCommentDoList.forEach(goodsComment -> {
                CommunityPublishRequestDto requestDto = new CommunityPublishRequestDto();
                requestDto.setSkuId(goodsComment.getSkuId());
                requestDto.setContent(goodsComment.getContent());
                requestDto.setContentType(GoodsCommentConstant.ContentTypeEnum.resolve(goodsComment.getContentType()).getIndex());
                //TODO 获取图片、视频的宽高
                if (imageContents.contains(goodsComment.getContentType())) {
                    List<CommunityContentPicResponseDto> communityContentPicResponseDtoList = goodsComment.getMedias().stream().map(media -> {
                        CommunityContentPicResponseDto communityContentPicResponseDto = new CommunityContentPicResponseDto();
                        communityContentPicResponseDto.setPic(media.getUrl());
                        return communityContentPicResponseDto;
                    }).toList();
                    requestDto.setUrls(communityContentPicResponseDtoList);
                }
                if (videoContents.contains(goodsComment.getContentType())) {
                    goodsComment.getMedias().forEach(media -> {
                        requestDto.setVideoUrl(media.getVideoUrl());
                        requestDto.setVideoCoverUrl(media.getUrl());
                    });
                }
                Long communityContentId = publishContent(requestDto, userMap.get(goodsComment.getUserId()), goodsComment.getSkuId());

                GoodsCommentDo updateGoodsCommentDo = new GoodsCommentDo();
                updateGoodsCommentDo.setId(goodsComment.getId());
                updateGoodsCommentDo.setCommunityId(communityContentId);
                goodsCommentService.update(updateGoodsCommentDo);
            });
        }
    }

    /**
     * 取消同步到社区的评价
     *
     * @param goodsCommentList
     */
    private void unPublishContent(List<GoodsCommentDo> goodsCommentList) {
        Set<Long> goodsCommentIdList = goodsCommentList.stream().map(GoodsCommentDo::getCommunityId).collect(Collectors.toSet());
        communityContentService.deleteByIds(goodsCommentIdList);
        goodsCommentIdList.forEach(communityContentI18nService::deleteByContentId);
    }


    private Long publishContent(CommunityPublishRequestDto publishRequestDto, ZnsUserEntity znsUser, Long skuId) {
        log.info("用户发布帖子:{},用户ID：{}", publishRequestDto, znsUser.getId());

        if (!StringUtils.hasText(publishRequestDto.getContent()) && !StringUtils.hasText(publishRequestDto.getVideoUrl()) && CollectionUtils.isEmpty(publishRequestDto.getUrls())) {
            throw new BaseException("Please enter the content");
        }
        CommunityContent communityContent = new CommunityContent();
        communityContent.setContent(publishRequestDto.getContent());
        communityContent.setUserId(znsUser.getId());
        communityContent.setUserName(znsUser.getFirstName());
        communityContent.setHeadPortrait(znsUser.getHeadPortrait());
        communityContent.setUserEmail(znsUser.getEmailAddress());
        communityContent.setPics(!CollectionUtils.isEmpty(publishRequestDto.getUrls()) ? JsonUtil.writeString(publishRequestDto.getUrls()) : null);
        communityContent.setRedirectType(BannerJumpTypeEnum.NO_JUMP.getJumpType());
        communityContent.setContentType(publishRequestDto.getContentType());
        communityContent.setPublishType(CommunityContentPublishTypeEnum.INSTANT.getCode());
        communityContent.setPublishStatus(CommunityContentPublishStatusEnum.PUBLISHED.getCode());
        communityContent.setUserType(CommunityUserTypeEnum.REAL_USER.getCode());
        communityContent.setPublishExpectTime(ZonedDateTime.now());
        communityContent.setPublishRealTime(ZonedDateTime.now());
        communityContent.setSkuId(publishRequestDto.getSkuId());
        communityContentService.insertCommunityContent(communityContent);
        CommunityContentI18nEntity communityContentI18nEntity = new CommunityContentI18nEntity();
        communityContentI18nEntity.setContentId(communityContent.getId());
        communityContentI18nEntity.setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
        communityContentI18nEntity.setLanguageName(I18nConstant.LanguageCodeEnum.en_US.getName());
        communityContentI18nEntity.setContent(publishRequestDto.getContent());
        communityContentI18nEntity.setVideoUrl(publishRequestDto.getVideoUrl());
        communityContentI18nEntity.setVideoCoverUrl(publishRequestDto.getVideoCoverUrl());
        communityContentI18nEntity.setVideoCoverHeight(publishRequestDto.getVideoCoverHeight());
        communityContentI18nEntity.setVideoCoverWidth(publishRequestDto.getVideoCoverWidth());
        communityContentI18nService.insert(communityContentI18nEntity);
        return communityContent.getId();
    }

}
