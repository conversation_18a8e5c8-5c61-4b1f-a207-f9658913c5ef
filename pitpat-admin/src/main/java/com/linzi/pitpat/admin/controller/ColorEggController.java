package com.linzi.pitpat.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.mapper.EggActivityConfigDao;
import com.linzi.pitpat.data.activityservice.model.entity.EggActivityConfig;
import com.linzi.pitpat.data.awardservice.model.dto.EggActivityConfigDto;
import com.linzi.pitpat.data.awardservice.model.resp.ColorEggResp;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.request.ColorEggQuery;
import com.linzi.pitpat.lang.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;


/**
 * @description: 类目控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/color/egg", "/test/color/egg"})
public class ColorEggController extends BaseController {

    @Autowired
    private ZnsUserAccountDetailService znsUserAccountDetailService;

    @Autowired
    private EggActivityConfigDao eggActivityConfigDao;

    @PostMapping("/selectAll")
    public Result<ColorEggResp> selectAll(@RequestBody ColorEggQuery query) {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime start = DateUtil.startOfDate(now);
        ZonedDateTime end = DateUtil.endOfDate(now);


        BigDecimal todayRobotAllAmount = BigDecimal.ZERO;//znsUserAccountDetailDao.selectAccountByGmtCreateIsRobotTradeType(ZonedDateTime.now(), 1, AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getCode());//今天机器人总金额 。
        BigDecimal todayUserAllAmount = znsUserAccountDetailService.selectAccountByGmtCreateIsRobotTradeType(start, end, 0, AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType());  //今天用户总金额

        BigDecimal todayAllAmount = todayRobotAllAmount.add(todayUserAllAmount); // 今天金币彩蛋总金额


        BigDecimal allAmount = znsUserAccountDetailService.selectAccountByTradeType(query.getUserId(), AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType()); // 金币彩蛋总收入
        Integer allCount = znsUserAccountDetailService.countAccountByTradeType(query.getUserId(), AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType());   // 金币彩蛋总拾取次数

        ZonedDateTime befor30Date = DateUtil.addDays(DateUtil.getStartOfDate(ZonedDateTime.now()), -30);

        BigDecimal all30Amount = znsUserAccountDetailService.selectAccountByCreateTimeTradeType(query.getUserId(), befor30Date, AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType());// 近30天金币彩蛋总收入
        Integer all30Count = znsUserAccountDetailService.countAccountByCreateTimeTradeType(query.getUserId(), befor30Date, AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType()); //近30天金币彩蛋总拾取次数

        BigDecimal allByActivityTypeAmount = znsUserAccountDetailService.selectAccountByTradeTypeSubType(query.getGmtStartTime(), query.getGmtEndTime(),
                AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType(), query.getActivityType());// 不同的类型

        return CommonResult.success(new ColorEggResp(todayAllAmount, todayRobotAllAmount, todayUserAllAmount, allAmount, allCount, all30Amount, all30Count, allByActivityTypeAmount));
    }

    @PostMapping("/selectModel")
    public Result selectModel(@RequestBody ColorEggQuery query) {
        List<EggActivityConfig> list = eggActivityConfigDao.selectEggActivityConfigByGroupByActivityType();
        return CommonResult.success(list);
    }


    @PostMapping("/selectEveryDayType")
    public Result<Page<EggActivityConfigDto>> selectEveryDayType(@RequestBody ColorEggQuery query) {
        if (query.getGmtEndTime() == null) {
            query.setGmtEndTime(DateUtil.addHours(ZonedDateTime.now(), 8));
        }
        Page<EggActivityConfigDto> page = eggActivityConfigDao.selectPageByCondition(PageHelper.ofPage(query), 15, query.getGmtStartTime(), query.getGmtEndTime(), query.getUrgeTypes());

        List<EggActivityConfigDto> records = page.getRecords();
        for (EggActivityConfigDto dto : records) {
            BigDecimal allAmount = BigDecimalUtil.add(dto.getFreeRun(),
                    dto.getComposeRun(), dto.getChallengeRun(), dto.getRankRun(), dto.getGuanfangComposeRun());
            dto.setAllAmount(allAmount);
        }
        page.setRecords(records);
        return CommonResult.success(page);
    }
}
