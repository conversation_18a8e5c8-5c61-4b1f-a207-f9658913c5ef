package com.linzi.pitpat.admin.equipmentservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.equipmentservice.dto.request.GroupImportDeviceDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.TreadmillGroupDeleteReq;
import com.linzi.pitpat.data.equipmentservice.dto.request.TreadmillGroupListReq;
import com.linzi.pitpat.data.equipmentservice.dto.request.TreadmillGroupSaveOrModifyReq;
import com.linzi.pitpat.data.equipmentservice.dto.request.TreadmillGroupShelfReq;
import com.linzi.pitpat.data.equipmentservice.dto.response.GroupImportRespDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.GroupImportTemplateRespDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.TreadmillGroupListResp;
import com.linzi.pitpat.data.equipmentservice.dto.response.TreadmillGroupResp;
import com.linzi.pitpat.data.equipmentservice.manager.TreadmillGroupManager;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.linzi.pitpat.admin.util.SecurityUtils.getLoginUser;

/**
 * 设备分组控制器
 */
@Slf4j
@RestController
@RequestMapping("/device/group")
@RequiredArgsConstructor
public class TreadmillGroupController extends BaseController {

    private final TreadmillGroupManager treadmillGroupManager;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 设备分组导入 模板url
     */
    @Value("${admin.server.template.url:https://pitpat-oss.s3.us-east-2.amazonaws.com/202403/iIl16f6QWgTN3450.xls}")
    private String templateUrl;

    /**
     * 分组管理列表
     *
     * @param req
     * @return
     */
    @PostMapping("/pageList")
    public Result<Page<TreadmillGroupListResp>> pageList(@RequestBody final TreadmillGroupListReq req) {
        final Page<TreadmillGroupListResp> list = treadmillGroupManager.pageList(req);
        return CommonResult.success(list);
    }

    /**
     * 新增修改分组
     *
     * @param req
     * @return
     */
    @PostMapping("/saveOrModify")
    @Log(title = "新增修改分组", businessType = BusinessType.INSERT)
    @RepeatSubmit
    public Result<Boolean> saveOrModify(@RequestBody @Valid final TreadmillGroupSaveOrModifyReq req) {
        final SysUser user = getLoginUser().getUser();
        final Boolean b = treadmillGroupManager.saveOrModify(req, user);
        return CommonResult.success(b);
    }

    /**
     * 修改分组状态
     *
     * @param req
     * @return
     */
    @PostMapping("/updateGroupState")
    @Log(title = "修改分组状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public Result<Boolean> updateGroupState(@RequestBody @Valid final TreadmillGroupShelfReq req) {
        final Boolean b = treadmillGroupManager.updateGroupState(req);
        return CommonResult.success(b);
    }

    /**
     * 删除分组
     *
     * @param req
     * @return
     */
    @PostMapping("/deleteGroup")
    @Log(title = "删除分组", businessType = BusinessType.DELETE)
    @RepeatSubmit
    public Result<Boolean> deleteGroup(@RequestBody @Valid final TreadmillGroupDeleteReq req) {
        final Boolean b = treadmillGroupManager.deleteGroup(req);
        return CommonResult.success(b);
    }

    /**
     * 所有可用的分组列表
     *
     * @return
     */
    @GetMapping("/allGroupList")
    public Result<List<TreadmillGroupResp>> allGroupList(@RequestParam(value = "equipmentMainType", required = false) String equipmentMainType) {
        final List<TreadmillGroupResp> list = treadmillGroupManager.allGroupList(equipmentMainType);
        return CommonResult.success(list);
    }

    /**
     * 获取导入模板
     *
     * @return
     */
    @GetMapping("/getImportTemplate")
    public Result<GroupImportTemplateRespDto> getImportTemplate() {
        return CommonResult.success(new GroupImportTemplateRespDto(templateUrl));
    }

    /**
     * 上传设备分组
     *
     * @param excel
     * @return
     * @throws Exception
     */
    @PostMapping("/importExcel")
    @Log(title = "上传设备分组", businessType = BusinessType.INSERT)
    public Result<GroupImportRespDto> importExcel(@RequestParam("file") final MultipartFile excel, final Long groupId) throws Exception {
        if (groupId == null) {
            throw new BaseException("分组id不能为空");
        }
        final ExcelUtil<GroupImportDeviceDto> excelUtil = new ExcelUtil<>(GroupImportDeviceDto.class);
        final List<GroupImportDeviceDto> list = excelUtil.importExcel(excel.getInputStream());
        //校验excel数量
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException("excel 数量为空！");
        }
        if (list.size() > 3000) {
            throw new BaseException("最多只能导入 3000条记录");
        }
        final RLock lock = redissonClient.getLock(RedisConstants.GROUP_IMPORT_DEVICE + groupId);
        final boolean b = lock.tryLock();
        if (!b) {
            throw new BaseException("该分组正在导入中，请稍候再试");
        }
        GroupImportRespDto respDto = new GroupImportRespDto();
        try {
            //导入设备分组
            respDto = treadmillGroupManager.importExcel(groupId, list, SecurityUtils.getLoginUser().getUsername());
        } catch (final Exception e) {
            log.error("导入设备分组异常：", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success(respDto);
    }

}
