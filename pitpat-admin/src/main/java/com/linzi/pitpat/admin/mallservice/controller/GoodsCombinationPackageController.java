package com.linzi.pitpat.admin.mallservice.controller;

import com.linzi.pitpat.core.web.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.mallservice.dto.api.request.CombinationPackageGoodsQueryDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.GoodsPackageConfigSkuRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.CombinationPackageSkuResponseDto;
import com.linzi.pitpat.data.mallservice.manager.console.GoodsCombinationPackageConsoleManager;
import com.linzi.pitpat.data.mallservice.model.entity.CombinationPackageSkuDo;
import com.linzi.pitpat.data.mallservice.model.query.CombinationPackageSkuQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsCombinationPackageQuery;
import com.linzi.pitpat.data.mallservice.service.CombinationPackageSkuService;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.data.mallservice.converter.api.GoodsCombinationPackageConverter;
import com.linzi.pitpat.data.mallservice.manager.api.GoodsCombinationPackageManager;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsCombinationPackageDo;
import com.linzi.pitpat.data.mallservice.model.query.GoodsCombinationPackagePageQuery;
import com.linzi.pitpat.data.mallservice.service.GoodsCombinationPackageService;

import com.linzi.pitpat.data.mallservice.dto.api.request.GoodsCombinationPackageCreateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.GoodsCombinationPackageUpdateRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.GoodsCombinationPackagePageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.GoodsCombinationPackageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsCombinationPackageResponseDto;

import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 商品组合包 服务类
 *
 * @since 2025年6月20日
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/goodsCombinationPackages")
public class GoodsCombinationPackageController {

    private final GoodsCombinationPackageService goodsCombinationPackageService;
    private final GoodsCombinationPackageManager goodsCombinationPackageManager;
    private final GoodsCombinationPackageConverter goodsCombinationPackageConverter;
    private final CombinationPackageSkuService combinationPackageSkuService;
    private final GoodsCombinationPackageConsoleManager goodsCombinationPackageConsoleManager;

    /**
     * 创建商品组合包
     *
     * @param requestDto 商品组合包表单
     * @return GoodsCombinationPackage
     */
    @PostMapping("/create")
    public Result<Long> create(@RequestBody @Validated GoodsCombinationPackageCreateRequestDto requestDto) {
        GoodsCombinationPackageDo goodsCombinationPackage = goodsCombinationPackageConverter.toDo(requestDto);
        return CommonResult.success(goodsCombinationPackageService.create(goodsCombinationPackage));
    }

    /**
     * 更新商品组合包
     *
     * @param requestDto 商品组合包表单
     * @return GoodsCombinationPackage
     */
    @PostMapping("/update")
    public Result<Long> update(@RequestBody @Validated GoodsCombinationPackageUpdateRequestDto requestDto) {
        GoodsCombinationPackageDo goodsCombinationPackage = goodsCombinationPackageConverter.toDo(requestDto);
        Long update = goodsCombinationPackageService.update(goodsCombinationPackage);
        List<CombinationPackageSkuDo> skuDoList = combinationPackageSkuService.findList(new CombinationPackageSkuQuery().setCombinationPackageId(goodsCombinationPackage.getId()));
        if (!CollectionUtils.isEmpty(skuDoList)) {
            skuDoList.forEach(combinationPackageSkuDo -> {
                combinationPackageSkuDo.setStartTime(goodsCombinationPackage.getStartTime());
                combinationPackageSkuDo.setEndTime(goodsCombinationPackage.getEndTime());
            });
            combinationPackageSkuService.batchUpdate(skuDoList);
        }
        return CommonResult.success(update);
    }

    /**
     * 按照ID 删除商品组合包
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody GoodsCombinationPackageQueryDto queryDto) {
        boolean affected = goodsCombinationPackageService.deleteById(queryDto.getId());
        combinationPackageSkuService.deleteByPackageId(queryDto.getId());
        return CommonResult.success(affected);
    }

    /**
     * 按照ID 修改商品组合包状态
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/changeStatus")
    public Result<Boolean> changeStatus(@RequestBody GoodsCombinationPackageUpdateRequestDto queryDto) {
        if (Objects.isNull(queryDto.getId()) || Objects.isNull(queryDto.getStatus())) {
            return CommonResult.fail("ID和状态不能为空");
        }
        goodsCombinationPackageService.updateSelective(
                new GoodsCombinationPackageDo()
                        .setId(queryDto.getId())
                        .setStatus(queryDto.getStatus())
        );
        List<CombinationPackageSkuDo> skuDoList = combinationPackageSkuService.findList(new CombinationPackageSkuQuery().setCombinationPackageId(queryDto.getId()));
        if (CollectionUtils.isEmpty(skuDoList) && Objects.equals(queryDto.getStatus(), 0)) {
            throw new RuntimeException("请先配置商品");
        }
        if (!CollectionUtils.isEmpty(skuDoList)) {
            skuDoList.forEach(combinationPackageSkuDo -> {
                combinationPackageSkuDo.setStatus(queryDto.getStatus());
            });
            combinationPackageSkuService.batchUpdate(skuDoList);
        }
        return CommonResult.success();
    }

    /**
     * 按照ID 查询商品组合包
     *
     * @param queryDto 查询对象
     * @return
     */
    @PostMapping("/get")
    public Result<GoodsCombinationPackageResponseDto> findById(@RequestBody @Validated GoodsCombinationPackageQueryDto queryDto) {
        return CommonResult.success(goodsCombinationPackageConsoleManager.details(queryDto.getId()));
    }


    /**
     * 分页查询商品组合包列表
     *
     * @param pageQueryDto 分查询对象
     * @return List<GoodsCombinationPackage>
     */
    @PostMapping("/page")
    public Result<Page<GoodsCombinationPackageResponseDto>> findPage(@RequestBody GoodsCombinationPackagePageQueryDto pageQueryDto) {
        GoodsCombinationPackagePageQuery pageQuery = goodsCombinationPackageConverter.toPageQuery(pageQueryDto);
        pageQuery.setOrders(List.of(OrderItem.desc("gmt_create")));
        if (Objects.nonNull(pageQuery.getSkuId())) {
            List<CombinationPackageSkuDo> list = combinationPackageSkuService.findList(new CombinationPackageSkuQuery().setSkuId(pageQuery.getSkuId()));
            if (CollectionUtils.isEmpty(list)) {
                return CommonResult.success(new Page<>());
            }
            pageQuery.setIds(list.stream().distinct().map(CombinationPackageSkuDo::getCombinationPackageId).collect(Collectors.toList()));
        }
        Page<GoodsCombinationPackageDo> page = goodsCombinationPackageService.findPage(pageQuery);
        log.info("goodsCombinationPackage find page {}", page);
        return CommonResult.success(goodsCombinationPackageConverter.toDtoPage(page));
    }

    /**
     * 组合包配置商品
     */
    @PostMapping("/configSku")
    public Result<Boolean> configSku(@RequestBody @Validated GoodsPackageConfigSkuRequestDto requestDto) {
        goodsCombinationPackageConsoleManager.configSku(requestDto);
        return CommonResult.success();
    }

    /**
     * 分页查询组合包商品SKu列表
     *
     * @param queryDto 查询对象
     * @return List<GoodsCombinationPackage>
     */
    @PostMapping("/goods/list")
    public Result<Page<CombinationPackageSkuResponseDto>> packageSkuList(@RequestBody @Validated CombinationPackageGoodsQueryDto queryDto) {
        return CommonResult.success(goodsCombinationPackageConsoleManager.packageSkuResponseDtoPage(queryDto));
    }
}
