package com.linzi.pitpat.admin.mallservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.biz.MallOrderPaymentBizService;
import com.linzi.pitpat.data.mallservice.converter.console.OrderRefundConsoleConverter;
import com.linzi.pitpat.data.mallservice.dto.console.request.OrderRefundAmountRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.OrderRefundDeliveryRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.request.OrderRefundReviewRequestDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderRefundDetailsResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderRefundGoodsResponseDto;
import com.linzi.pitpat.data.mallservice.dto.console.response.OrderRefundListResponseDto;
import com.linzi.pitpat.data.mallservice.dto.request.RefundListReqDto;
import com.linzi.pitpat.data.mallservice.enums.OrderRefundConstant;
import com.linzi.pitpat.data.mallservice.model.entity.OrderLogistics;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderItemQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderRefundPageQuery;
import com.linzi.pitpat.data.mallservice.service.MallExchangeRateSwitchService;
import com.linzi.pitpat.data.mallservice.service.OrderLogisticsService;
import com.linzi.pitpat.data.mallservice.service.OrderRefundService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.third.erp.ErpApiUtil;
import com.linzi.pitpat.data.third.erp.req.ErpOrderAfterItemDto;
import com.linzi.pitpat.data.third.erp.req.ErpOrderAfterReqDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/14 17:14
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MallOrderAfterMananger {
    private final OrderRefundConsoleConverter orderRefundConsoleConverter;
    private final OrderRefundService orderRefundService;
    private final ZnsOrderItemService orderItemService;
    private final ZnsUserService userService;
    private final OrderLogisticsService orderLogisticsService;
    private final MallOrderBizService mallOrderBizService;
    private final MallExchangeRateSwitchService mallExchangeRateSwitchService;
    private final MallOrderPaymentBizService mallOrderPaymentBizService;

    /**
     * 售后订单列表
     *
     * @param po
     * @return
     */
    public Page<OrderRefundListResponseDto> pageList(RefundListReqDto po) {
        OrderRefundPageQuery pageQuery = orderRefundConsoleConverter.toPageQuery(po);
        pageQuery.setOrders(List.of(OrderItem.desc("id")));
        pageQuery.setRefundTypeList(Lists.newArrayList(OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT_GOOD.getCode(), OrderRefundConstant.REFUND_TYPE_ENUM.EXCHANGE_GOOD.getCode()));
        Page page = orderRefundService.findPage(pageQuery);
        List<OrderRefund> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        List<Long> itemIds = records.stream().map(OrderRefund::getOrderItemId).collect(Collectors.toList());
        Map<Long, String> goodsTitle = orderItemService.findList(new OrderItemQuery().setIds(itemIds)).stream().collect(Collectors.toMap(ZnsOrderItemEntity::getId, ZnsOrderItemEntity::getTitle));
        List<OrderRefundListResponseDto> dtoList = records.stream().map(r -> {
            OrderRefundListResponseDto orderRefundListResponseDto = orderRefundConsoleConverter.toListDto(r);
            orderRefundListResponseDto.setRefundGoods(goodsTitle.get(r.getOrderItemId()));
            orderRefundListResponseDto.setCurrency(mallExchangeRateSwitchService.getCurrencyByCountryCode(r.getCountryCode()));
            return orderRefundListResponseDto;
        }).collect(Collectors.toList());
        page.setRecords(dtoList);
        return page;
    }

    /**
     * 售后订单详情
     *
     * @param id
     * @return
     */
    public OrderRefundDetailsResponseDto refundDetail(Long id) {
        OrderRefund orderRefund = orderRefundService.selectOrderRefundById(id);
        OrderRefundDetailsResponseDto orderRefundDetailsResponseDto = orderRefundConsoleConverter.toDetailsDto(orderRefund);
        ZnsOrderItemEntity znsOrderItemEntity = orderItemService.selectOrderItemById(orderRefund.getOrderItemId());
        OrderRefundGoodsResponseDto orderRefundGoodsResponseDto = orderRefundConsoleConverter.toDetailsGoodsInfo(znsOrderItemEntity);
        orderRefundGoodsResponseDto.setCount(orderRefund.getCount());
        BigDecimal totalSaleAmount = znsOrderItemEntity.getSalePrice().multiply(new BigDecimal(orderRefund.getCount())).setScale(2, RoundingMode.HALF_UP);
        BigDecimal totalPayAmount = totalSaleAmount.subtract(orderRefund.getCouponAmount()).subtract(orderRefund.getAllowanceAmount());//sku实付价 = 原销售价*数量 - 分摊的优惠券金额- 津贴
        orderRefundGoodsResponseDto.setActualAmount(totalPayAmount);
        orderRefundGoodsResponseDto.setRefundGoodsAmount(totalPayAmount);
        orderRefundDetailsResponseDto.setGoodsInfo(orderRefundGoodsResponseDto);

        ZnsUserEntity user = userService.findById(orderRefund.getUserId());
        orderRefundDetailsResponseDto.setFirstName(user.getFirstName());
        orderRefundDetailsResponseDto.setGoodsAmount(orderRefund.getApplyAmount().subtract(orderRefund.getPostageAmount()).subtract(orderRefund.getTaxAmount()).add(orderRefund.getCouponAmount()).add(orderRefund.getAllowanceAmount()));
        orderRefundDetailsResponseDto.setCurrency(mallExchangeRateSwitchService.getCurrencyByCountryCode(orderRefund.getCountryCode()));
        return orderRefundDetailsResponseDto;
    }

    /**
     * 售后审核
     *
     * @param request
     */
    public void review(OrderRefundReviewRequestDto request) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        OrderRefund orderRefundOld = orderRefundService.selectOrderRefundById(request.getId());
        if (OrderRefundConstant.STATUS_ENUM.CANCEL.getCode().equals(orderRefundOld.getStatus())) {
            return;
        }
        OrderRefund orderRefund = orderRefundConsoleConverter.toDo(request);
        orderRefund.setSysUserId(user.getUserId());
        orderRefund.setSysUserName(user.getUserName());
        if (request.getReviewStatus() == -1) {
            //审核拒绝
            orderRefund.setStatus(OrderRefundConstant.STATUS_ENUM.REJECT_REFUND.getCode());
            orderRefundService.updateOrderRefundById(orderRefund);
            log.info("[review]---售后审核,审核拒绝,request={}", JsonUtil.writeString(request));
            return;
        }

        //审核通过
        log.info("[review]---售后审核,审核通过,开始,request={}", JsonUtil.writeString(request));
        Integer status = null;
        if (OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT.getCode().equals(orderRefundOld.getRefundType())) {
            //仅退款
            String refundResult = mallOrderPaymentBizService.createRefundPayOrder(orderRefundOld.getRefundNo());
            if (!StringUtils.hasText(refundResult) || PayConstant.RefundPayStatusEnum.FAILED.type.equals(refundResult)) {
                //发起支付失败
                throw new BaseException("发起三方退款失败");
            }
            mallOrderPaymentBizService.dealRefundResult(orderRefundOld.getRefundNo(), PayConstant.RefundPayStatusEnum.findByCode(refundResult), null);
        }else if (OrderRefundConstant.REFUND_TYPE_ENUM.EXCHANGE_GOOD.getCode().equals(orderRefundOld.getRefundType())) {
            //换货 ，更新退款单为换货 中，等待物流完成发起退款
            status = OrderRefundConstant.STATUS_ENUM.EXCHANGEING.getCode();
            orderRefund.setStatus(status);
            orderRefundService.updateOrderRefundById(orderRefund);
        } else {
            //退货退款 ，更新退款单为退款中，等待物流完成发起退款
            status = OrderRefundConstant.STATUS_ENUM.REFUNDING.getCode();
            orderRefund.setStatus(status);
            orderRefundService.updateOrderRefundById(orderRefund);
        }

        //通知erp
        ErpOrderAfterReqDto req = new ErpOrderAfterReqDto().setOrderNo(orderRefundOld.getRefundNo()).setOrderSourceCode(orderRefund.getOrderNo());
        if (OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT.getCode().equals(orderRefundOld.getRefundType())) {
            req.setAfterSaleType(1); //仅退款
        } else if (OrderRefundConstant.REFUND_TYPE_ENUM.EXCHANGE_GOOD.getCode().equals(orderRefundOld.getRefundType())) {
            req.setAfterSaleType(2);
        } else if (OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT_GOOD.getCode().equals(orderRefundOld.getRefundType())) {
            req.setAfterSaleType(3);
        }
        req.setOrderItemList(List.of(new ErpOrderAfterItemDto().setOrderItemId(orderRefundOld.getOrderItemId().toString()).setNum(orderRefundOld.getCount())));
        ErpApiUtil.addOrUpdateOrderAfter(req);

        //售后审核通过给用户发送push
        mallOrderBizService.refundOrderPushMsg(orderRefund.getId(), status);
        log.info("[review]---售后审核,审核通过,结束,request={}", JsonUtil.writeString(request));
    }

    /**
     * 售后上传发货面单
     *
     * @param request
     */
    public void delivery(OrderRefundDeliveryRequestDto request) {
        OrderRefund orderRefund = orderRefundConsoleConverter.toDo(request);
        orderRefundService.updateOrderRefundById(orderRefund);

        OrderRefund refund = orderRefundService.selectOrderRefundById(request.getId());

        OrderLogistics orderExpress = orderLogisticsService.selectOrderLogisticsByLogisticCode(request.getLogisticsNo());
        if (orderExpress == null) {
            orderExpress = new OrderLogistics();
            orderExpress.setOrderId(refund.getOrderId()).setOrderNo(refund.getOrderNo())
                    .setOrderItemId(refund.getOrderItemId())
                    .setDirectionType(1)
                    .setShipperName(request.getLogisticsCompay())
                    .setLogisticCode(request.getLogisticsNo());
            orderLogisticsService.insertOrderLogistics(orderExpress);
        }

        if (StringUtils.hasText(request.getExchangeLogisticsNo())) {
            OrderLogistics exchangeOrderExpress = orderLogisticsService.selectOrderLogisticsByLogisticCode(request.getExchangeLogisticsNo());
            if (exchangeOrderExpress == null) {
                exchangeOrderExpress = new OrderLogistics();
                exchangeOrderExpress.setOrderId(refund.getOrderId()).setOrderNo(refund.getOrderNo())
                        .setOrderItemId(refund.getOrderItemId())
                        .setDirectionType(2)
                        .setLogisticCode(request.getExchangeLogisticsNo());
                orderLogisticsService.insertOrderLogistics(exchangeOrderExpress);
            }
        }
    }

    /**
     * 标记退款
     *
     * @param request
     */
    public void refund(OrderRefundAmountRequestDto request) {
        OrderRefund orderRefund = orderRefundConsoleConverter.toDo(request);
        orderRefund.setStatus(OrderRefundConstant.STATUS_ENUM.REFUND_SUCCESS.getCode());
        orderRefund.setPayStatus(OrderRefundConstant.PAY_STATUS_ENUM.PAY_REFUND_SUCCESS.getCode());
        orderRefund.setGmtRefund(ZonedDateTime.now());
        orderRefund.setGmtFinish(ZonedDateTime.now());
        orderRefundService.updateOrderRefundById(orderRefund);
    }
}
