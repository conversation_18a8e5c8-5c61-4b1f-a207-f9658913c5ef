package com.linzi.pitpat.admin.controller.message.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.enums.UserScopeEnum;
import com.linzi.pitpat.data.messageservice.dto.MessageFileUserParseDto;
import com.linzi.pitpat.data.messageservice.dto.request.MessageTaskContentRequest;
import com.linzi.pitpat.data.messageservice.dto.request.MessageTaskListRequest;
import com.linzi.pitpat.data.messageservice.dto.request.MessageTaskRequest;
import com.linzi.pitpat.data.messageservice.dto.request.MessageTaskTestSendPo;
import com.linzi.pitpat.data.messageservice.enums.MessageTaskMsgSendTypeEnum;
import com.linzi.pitpat.data.messageservice.enums.MessageTaskStatus;
import com.linzi.pitpat.data.messageservice.manager.MessageTaskMsgManager;
import com.linzi.pitpat.data.messageservice.manager.MessageTaskUserManager;
import com.linzi.pitpat.data.messageservice.model.entity.MessageTask;
import com.linzi.pitpat.data.messageservice.model.entity.MessageTaskMsg;
import com.linzi.pitpat.data.messageservice.model.query.ImSendRecordQuery;
import com.linzi.pitpat.data.messageservice.model.query.MessageTaskQuery;
import com.linzi.pitpat.data.messageservice.model.request.MessageEnableRequest;
import com.linzi.pitpat.data.messageservice.model.vo.MessageTaskDetailDataVo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageTaskDetailVo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageTaskListVo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageTaskUserListVo;
import com.linzi.pitpat.data.messageservice.quartz.PushTask;
import com.linzi.pitpat.data.messageservice.runner.MessageTaskDataFetcherFactory;
import com.linzi.pitpat.data.messageservice.runner.fetcher.MessageTaskDataFetcher;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.messageservice.service.ImSendRecordLogService;
import com.linzi.pitpat.data.messageservice.service.MessageTaskMsgService;
import com.linzi.pitpat.data.messageservice.service.MessageTaskService;
import com.linzi.pitpat.data.messageservice.service.MessageTaskUserService;
import com.linzi.pitpat.data.messageservice.service.ZnsPushSendRecordLogService;
import com.linzi.pitpat.data.systemservice.enums.PushRegionEnum;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.vo.RegionDto;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.PopRegionService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupService;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.data.util.file.FileUtils;
import com.linzi.pitpat.data.vo.task.HardwareRunPush;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.message.api.config.MessageSkipTaskTypeEnum;
import com.linzi.pitpat.message.service.MsgNonDisturbUserRefuseLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 站内信/push管理
 *
 * <AUTHOR>
 * @date 2022/12/30 15:35
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/messageTask")
public class MessageTaskController extends BaseController {
    @Resource
    private MessageTaskService messageTaskService;
    @Resource
    private MessageTaskMsgService messageTaskMsgService;
    @Resource
    private MessageTaskUserService messageTaskUserService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private AppMessageService appMessageService;
    @Resource
    private ImSendRecordLogService imSendRecordLogService;
    @Resource
    private ZnsPushSendRecordLogService pushSendRecordLogService;
    @Resource
    private ZnsRunActivityService znsRunActivityService;
    @Resource
    private ActivityTaskConfigService activityTaskConfigService;
    @Resource
    private AreaService areaService;
    @Resource
    private PopRegionService popRegionService;

    @Resource
    private UserGroupRelService userGroupRelService;

    @Resource
    private UserGroupService userGroupService;
    @Resource
    private MessageTaskUserManager messageTaskUserManager;

    private final MessageTaskMsgManager messageTaskMsgManager;

    private final MessageTaskDataFetcherFactory messageTaskDataFetcherFactory;

    private final MsgNonDisturbUserRefuseLogService msgNonDisturbUserRefuseLogService;

    /**
     * 获取硬件跑自动推送设置
     *
     * @return
     */
    @GetMapping("/getHardwareRunPushSetting")
    public Result<HardwareRunPush> getHardwareRunPushSetting() {
        HardwareRunPush hardwareRunPush = new HardwareRunPush();
        String config = sysConfigService.selectConfigByKey("hardware.run.push");
        if (StringUtil.isEmpty(config)) {
            return CommonResult.success(hardwareRunPush);
        }
        hardwareRunPush = JsonUtil.readValue(config, HardwareRunPush.class);
        return CommonResult.success(hardwareRunPush);
    }

    /**
     * 添加或更新硬件跑自动推送设置
     *
     * @return
     */
    @PostMapping("/addHardwareRunPushSetting")
    @Log(title = "站内信管理", businessType = BusinessType.INSERT)
    public Result addHardwareRunPushSetting(@RequestBody HardwareRunPush hardwareRunPush) {
        String pushStr = JsonUtil.writeString(hardwareRunPush);
        String configKey = "hardware.run.push";
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(configKey);
        if (sysConfig != null) {
            sysConfig.setConfigValue(pushStr);
            sysConfigService.updateConfig(sysConfig);
        } else {
            sysConfig = new SysConfig();
            sysConfig.setConfigName("硬件跑自动推送设置");
            sysConfig.setConfigKey("hardware.run.push");
            sysConfig.setConfigValue(pushStr);
            sysConfigService.insertConfig(sysConfig);
        }

        return CommonResult.success();
    }

    /**
     * 获取推送机器人
     *
     * @return
     */
    @GetMapping("/sendRobotList")
    public Result sendRobotList() {
        String config = sysConfigService.selectConfigByKey("message.send.robot");
        if (StringUtil.isEmpty(config)) {
            return CommonResult.success();
        }
        Map<String, Object> map = new HashMap<>();
        List<Map> maps = JsonUtil.readList(config, Map.class);
        map.put("list", maps);
        return CommonResult.success(map);
    }

    /**
     * 任务列表
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public Result<Page<MessageTaskListVo>> list(@RequestBody MessageTaskListRequest request) {

        Page<MessageTask> page = messageTaskService.page(new Page<>(request.getPageNum(), request.getPageSize()),
                MessageTaskQuery.builder()
                        .type(request.getType())
                        .title(request.getTitle())
                        .sendUserId(request.getSendUserId())
                        .gmtSendStart(request.getGmtSendStart())
                        .gmtSendEnd(request.getGmtSendEnd())
                        .id(request.getId())
                        .status(request.getStatus())
                        .build());
        page.getRecords().forEach(e -> e.setCountry(UserConstant.convertCountry(e.getCountry())));

        Map<String, Boolean> im24Hour = imSendRecordLogService.findLogByBatchNumberAndLastSendTime(page.getRecords().stream().map(MessageTask::getBatchNumber).toList(), DateUtil.addHours(ZonedDateTime.now(), -24));

        Page<MessageTaskListVo> resultPage = new Page<>();
        resultPage.setTotal(page.getTotal());
        resultPage.setRecords(page.getRecords().stream().map(item -> {
            MessageTaskListVo vo = new MessageTaskListVo();
            BeanUtils.copyProperties(item, vo);
            vo.setHave24hImMessage(im24Hour.get(item.getBatchNumber()));
            return vo;
        }).toList());
        return CommonResult.success(resultPage);
    }

    /**
     * 任务用户列表
     *
     * @param request
     * @return
     */
    @PostMapping("/user/list")
    public Result<Page<MessageTaskUserListVo>> userList(@RequestBody MessageTaskListRequest request) {
        Page<MessageTaskUserListVo> page = messageTaskUserManager.pageList(new Page<>(request.getPageNum(), request.getPageSize()),
                request.getTaskId());
        return CommonResult.success(page);
    }

    /**
     * 消息任务用户导出
     *
     * @param po
     * @param response
     * @param request
     * @return
     */
    @PostMapping("/user/exportList")
    @Log(title = "站内信管理", businessType = BusinessType.EXPORT, keyParameterFiled = "taskId")
    public Result userExportList(@RequestBody MessageTaskListRequest po, HttpServletResponse response, HttpServletRequest request) {
        List<MessageTaskUserListVo> list = messageTaskUserService.exportList(po.getTaskId());
        ExcelUtil<MessageTaskUserListVo> util = new ExcelUtil<MessageTaskUserListVo>(MessageTaskUserListVo.class);
        util.exportExcel(list, "用户数据", response, request);
        return CommonResult.success();
    }

    /**
     * 消息任务详情
     *
     * @param request
     * @return
     */
    @PostMapping("/detail")
    public Result<MessageTaskDetailVo> detail(@RequestBody MessageTaskRequest request) {
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }
        MessageTaskDetailVo vo = new MessageTaskDetailVo();
        MessageTask task = messageTaskService.findById(request.getId());
        BeanUtils.copyProperties(task, vo);
        vo.setRepeatSendDateStr(JsonUtil.writeString(vo.getRepeatSendDate()));
        List<MessageTaskMsg> messageTaskMsgs = messageTaskMsgService.selectMsgList(task.getId());
//        vo.setMsgList(messageTaskMsgs);
        // 处理国际化数据
        if (!CollectionUtils.isEmpty(messageTaskMsgs)) {
            List<MessageTaskContentRequest> sendContent = new ArrayList<>();
            vo.setSendContent(sendContent);
            List<String> allLangCode = Arrays.stream(I18nConstant.LanguageCodeEnum.values()).map(I18nConstant.LanguageCodeEnum::getCode).collect(Collectors.toList());
            Map<String, List<MessageTaskMsg>> collect = messageTaskMsgs.stream().collect(Collectors.groupingBy(MessageTaskMsg::getLangCode));
            for (String langCode : allLangCode) {
                MessageTaskContentRequest item = new MessageTaskContentRequest();
                item.setLangCode(langCode);
                List<MessageTaskMsg> langCodeMessageTask = collect.getOrDefault(langCode, new ArrayList<>());
                for (MessageTaskMsg messageTaskMsg : langCodeMessageTask) {
                    if (MessageTaskMsgSendTypeEnum.PUSH.equals(messageTaskMsg.getSendType())) {
                        item.setPush(messageTaskMsg);
                    } else {
                        item.addIM(messageTaskMsg);
                    }
                }
                sendContent.add(item);
            }
        }

        // 查询推送区域
        List<RegionDto> list = popRegionService.getRegionDtoList(PushRegionEnum.PUSH_MESSAGE_REGION.getType(), request.getId());
        if (!CollectionUtils.isEmpty(list)) {
            vo.setRegionDtos(list);
        } else {
            vo.setRegionDtos(List.of(RegionDto.buildGlobal()));
        }


        //查询数据

        //免打扰拒绝发送用户
        Integer nonDisturbUser = msgNonDisturbUserRefuseLogService.countTaskNonDisturbUser(task.getId(), "" + task.getId(), MessageSkipTaskTypeEnum.MESSAGE_TASK);
        if (task.getType() == 1) {
            fillImData(task, vo, nonDisturbUser);
        } else if (MessageTaskMsgSendTypeEnum.PUSH.getFrontId().equals(task.getType())) {
            fillPushData(task, vo, nonDisturbUser);
        } else {
            fillImData(task, vo, nonDisturbUser);
            fillPushData(task, vo, nonDisturbUser);
        }
        if (UserScopeEnum.GROUP_SELECTION.getCode().equals(task.getUserScope())) {
            vo.setUserGroups(userGroupService.listUserGroups(task.getGroupIdStr()));
        }

        return CommonResult.success(vo);
    }

    private void fillImData(MessageTask task, MessageTaskDetailVo vo, Integer nonDisturbUser) {
        Integer total = imSendRecordLogService.findDistinctCount(ImSendRecordQuery.builder().batchNumber(task.getBatchNumber()).build());
        Integer readNum = imSendRecordLogService.findDistinctCount(ImSendRecordQuery.builder().batchNumber(task.getBatchNumber()).isRead(1).build());

        int unreadNum = total - readNum;
        MessageTaskDetailDataVo imData = new MessageTaskDetailDataVo();
        imData.setTargetNumber(task.getSendCount());
        imData.setReachedNumber(total);
        imData.setReadNumber(readNum);
        imData.setUnreadNumber(unreadNum);
        imData.setNonDisturbedUserCount(nonDisturbUser);
        vo.setImData(imData);
        vo.setTargetNumber(task.getSendCount());
        vo.setReachedNumber(total);
        vo.setReadNumber(readNum);
        vo.setUnreadNumber(unreadNum);
        vo.setNonDisturbedUserCount(nonDisturbUser);
    }

    private void fillPushData(MessageTask task, MessageTaskDetailVo vo, Integer nonDisturbUser) {
        Map<String, Object> data = pushSendRecordLogService.getData(task.getBatchNumber());
        MessageTaskDetailDataVo pushData = new MessageTaskDetailDataVo();
        pushData.setTargetNumber(task.getSendCount());
        pushData.setReachedNumber(MapUtils.getInteger(data, "reachedNumber"));
        pushData.setReadNumber(MapUtils.getInteger(data, "readNum"));
        pushData.setUnreadNumber(MapUtils.getInteger(data, "unreadNum"));
        pushData.setNonDisturbedUserCount(nonDisturbUser);
        vo.setPushData(pushData);

        vo.setReachedNumber(MapUtils.getInteger(data, "reachedNumber"));
        vo.setReadNumber(MapUtils.getInteger(data, "readNum"));
        vo.setUnreadNumber(MapUtils.getInteger(data, "unreadNum"));
        vo.setTargetNumber(task.getSendCount());
        vo.setNonDisturbedUserCount(nonDisturbUser);
    }

    /**
     * 新增站内信
     *
     * @param file
     * @param request
     * @return
     */
    @PostMapping("/add")
    @Log(title = "站内信管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    public Result add(@RequestParam(value = "file", required = false) MultipartFile file, MessageTaskRequest request) {
        Result result = checkParam(request);
        if (Objects.nonNull(result)) {
            return result;
        }
        //文件内存读写，注意OOM风险
        messageTaskMsgManager.save(request, SecurityUtils.getUsername(), parseFileUser(file, request));
        return CommonResult.success();
    }

    /**
     * 编辑站内信
     *
     * @param file
     * @param request
     * @return
     */
    @PostMapping("/edit")
    @Log(title = "站内信管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public Result edit(MultipartFile file, MessageTaskRequest request) {
        Result result = checkParam(request);
        if (Objects.nonNull(result)) {
            return result;
        }
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }
        //文件内存读写，注意OOM风险
        MessageFileUserParseDto messageFileUserParseDto = parseFileUser(file, request);
        messageTaskMsgManager.edit(request.getId(), request, SecurityUtils.getUsername(), messageFileUserParseDto);
        return CommonResult.success();
    }

    private MessageFileUserParseDto parseFileUser(MultipartFile file, MessageTaskRequest request) {
        Integer userScope = request.getUserScope();
        if (UserScopeEnum.FILE_IMPORT.getCode().equals(userScope)) {
            if (Objects.nonNull(file)) {
                List<String> list = FileUtils.readFirstColumn(file);
                list = list.stream().distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    throw new BaseException("excel 文件数据解析为空，请修改后重试");
                }
                return new MessageFileUserParseDto(list, true);
            } else {
                return new MessageFileUserParseDto(null, false);
            }
        }
        return new MessageFileUserParseDto(null, false);
    }


    /**
     * 修改站内信状态
     *
     * @param request
     * @return
     */
    @PostMapping("/updateStatus")
    @Log(title = "站内信管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public Result updateStatus(@RequestBody MessageTask request) {
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }
        if (Objects.isNull(request.getStatus())) {
            return CommonResult.fail("状态不能为空");
        }
        MessageTask messageTask = messageTaskService.findById(request.getId());
        messageTask.checkAndSetStatus(request.getStatus());
        MessageTask task = new MessageTask();
        task.setId(request.getId());
        task.setFileName(messageTask.getFileName());
        task.setStatus(request.getStatus());
        task.setGmtModified(ZonedDateTime.now());
        messageTaskService.updateById(task);

        return CommonResult.success();
    }

    @PostMapping("/batchEnable")
    @Log(title = "站内信管理批量生效", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public Result batchEnable(@RequestBody MessageEnableRequest request) {
        List<Long> ids = request.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return CommonResult.fail("任务id为空");
        }
        messageTaskService.batchEnable(ids, SecurityUtils.getUsername());
        return CommonResult.success();
    }


    /**
     * 删除站内信
     *
     * @param request
     * @return
     */
    @PostMapping("/delete")
    @RepeatSubmit
    @Log(title = "站内信管理", businessType = BusinessType.DELETE)
    public Result delete(@RequestBody MessageTask request) {
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }
        MessageTask byId = messageTaskService.findById(request.getId());
        if (byId == null || !MessageTaskStatus.Pending.equals(byId.getStatus())) {
            throw new BaseException("当前状态不允许删除");
        }
        messageTaskService.deleteById(request.getId());

        return CommonResult.success();
    }

    @Autowired
    private PushTask pushTask;

    @PostMapping("/aa")
    public void AA() {
        pushTask.timingTask();
    }

    /**
     * 撤回站内信
     *
     * @param request
     * @return
     */
    @PostMapping("/recall")
    @Log(title = "站内信管理", businessType = BusinessType.REVOKE)
    public Result recall(@RequestBody MessageTask request) {
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }

        MessageTask messageTask = messageTaskService.findById(request.getId());
        if (messageTask.getType() == 2) {
            return CommonResult.fail("push不能撤回");
        }

        if (!MessageTaskStatus.Completed.equals(messageTask.getStatus())
                && !MessageTaskStatus.Cancel.equals(messageTask.getStatus())) {
            return CommonResult.fail("任务状态异常，无需撤回");
        }
        int minutes = DateUtil.betweenMinutes(messageTask.getCurrentSend(), ZonedDateTime.now());
        if (minutes > 24 * 60) {
            return CommonResult.fail("发送时间已超过24小时，无法撤回");
        }
        MessageTask task = new MessageTask();
        task.setId(request.getId());
        task.setStatus(MessageTaskStatus.Revoked);
        task.setGmtModified(ZonedDateTime.now());
        messageTaskService.updateById(task);

        appMessageService.recall(messageTask.getBatchNumber());
        return CommonResult.success();
    }

    /**
     * 测试发送站内信
     *
     * @param po
     * @return
     */
    @PostMapping("/testSend")
    @Log(title = "站内信管理", businessType = BusinessType.OTHER, keyParameterFiled = "taskId")
    public Result testSend(@RequestBody MessageTaskTestSendPo po) {
        if (Objects.isNull(po.getTaskId())) {
            return CommonResult.fail("id不能为空");
        }
        if (StringUtil.isEmpty(po.getEmails())) {
            return CommonResult.fail("发送用户不能为空");
        }
        MessageTask messageTask = messageTaskService.findById(po.getTaskId());
        if (!MessageTaskStatus.Pending.equals(messageTask.getStatus())) {
            return CommonResult.fail("只有未生效状态可以测试发送");
        }

        if (messageTask.getSendStatus() != 0) {
            return CommonResult.fail("只有未发送状态可以测试发送");
        }
        String[] email = StringUtil.split(po.getEmails(), ";");
        MessageTaskDataFetcher dataFetcher = messageTaskDataFetcherFactory.getTestModeDataFetcher(messageTask.getId(), Arrays.asList(email));
        messageTaskService.executeTask(dataFetcher);

        return CommonResult.success();
    }

    private Result checkParam(MessageTaskRequest request) {
        if (StringUtil.isEmpty(request.getTitle())) {
            return CommonResult.fail("标题不能为空");
        }
        if (Objects.isNull(request.getType())) {
            return CommonResult.fail("消息类型不能为空");
        }
        if (Objects.isNull(request.getSendUserId())) {
            request.setSendUserId(0L);
            request.setSendUserName("admin");
        }
        if (Objects.isNull(request.getGmtSend())) {
            return CommonResult.fail("发送时间不能为空");
        }
        if (request.getGmtSend().compareTo(ZonedDateTime.now()) <= 0) {
            return CommonResult.fail("推送时间不能小于当前时间");
        }
        if (!CollectionUtils.isEmpty(request.getRepeatSendDate())) {
            if (request.getRepeatSendDate().size() > 9) {
                return CommonResult.fail("重复推送时间，不可以大于9次");
            }
            ArrayList<ZonedDateTime> sortDate = new ArrayList<>(request.getRepeatSendDate());
            sortDate.sort(Comparator.naturalOrder());
            request.setRepeatSendDate(sortDate);
            //重复发送时间检查
            ZonedDateTime baseDate = request.getGmtSend();
            for (int i = 0; i < request.getRepeatSendDate().size(); i++) {
                ZonedDateTime date = request.getRepeatSendDate().get(i);
                if (date.toInstant().toEpochMilli() - baseDate.toInstant().toEpochMilli() - 6 * 60 * 60 * 1000 <= 0) {
                    return CommonResult.fail("重复推送太频繁，请修改。");
                }
                baseDate = date;
            }
        }

//        if (StringUtil.isEmpty(request.getMsgListStr())) {
//            return CommonResult.fail("消息不能为空");
//        }
//        List<MessageTaskMsg> messageTaskMsgs = JsonUtil.readValue(request.getMsgListStr()).toJavaList(MessageTaskMsg.class);
//        request.setMsgList(messageTaskMsgs);
        if (CollectionUtils.isEmpty(request.getSendContent())) {
            return CommonResult.fail("消息不能为空");
        }
        List<MessageTaskMsg> messageTaskMsgs = new ArrayList<>();
        for (MessageTaskContentRequest messageTaskContentRequest : request.getSendContent()) {
            String langCode = messageTaskContentRequest.getLangCode();
            List<MessageTaskMsg> im = messageTaskContentRequest.getIm();
            if (!CollectionUtils.isEmpty(im)) {
                for (MessageTaskMsg messageTaskMsg : im) {
                    messageTaskMsg.setLangCode(langCode);
                    messageTaskMsg.setSendType(MessageTaskMsgSendTypeEnum.IM);
                    messageTaskMsgs.add(messageTaskMsg);
                }
            }
            MessageTaskMsg push = messageTaskContentRequest.getPush();
            if (push != null && push.getType() != null && push.getType() != 0) {
                messageTaskContentRequest.getPush().setLangCode(langCode);
                messageTaskContentRequest.getPush().setSendType(MessageTaskMsgSendTypeEnum.PUSH);
                messageTaskMsgs.add(messageTaskContentRequest.getPush());
            }
        }
        request.setMsgList(messageTaskMsgs);


//        if (request.getIsAllUser() == 1 && !StringUtils.hasText(request.getCountry())){
//            //非指定用户-push消息国家必填
//            return CommonResult.fail("投放国家不能为空");
//        }
        if (CollectionUtils.isEmpty(messageTaskMsgs)) {
            return null;
        }
        MessageTaskMsg messageTaskMsg = messageTaskMsgs.get(0);
        if (messageTaskMsg == null) {
            return null;
        }
        if (messageTaskMsg.getActivityId() != null && messageTaskMsg.getActivityId() > 0) {
            // TODO 校验普通活动国家
//            ZnsRunActivityEntity activityEntity = znsRunActivityService.getById(messageTaskMsg.getActivityId());
//            if(UserConstant.notContainsCountry(activityEntity.getCountry(),request.getCountry())){
//                //活动国家不包含push国家
//                return CommonResult.fail("所选活动的投放国家与配置的投放国家不一致");
//            }
        }
        Long taskConfigId = messageTaskMsg.getTaskConfigId();
        if (Objects.nonNull(taskConfigId) && taskConfigId > 0) {
            //校验聚合活动国家
            ActivityTaskConfig activityTaskConfig = activityTaskConfigService.findById(taskConfigId);
            ZnsRunActivityEntity activityEntity = znsRunActivityService.findById(activityTaskConfig.getActivityId());

//            if(UserConstant.notContainsCountry(activityEntity.getCountry(),request.getCountry())){
//                //活动国家不包含push国家
//                return CommonResult.fail("所选活动的投放国家与配置的投放国家不一致");
//            }
        }

        return null;
    }

    /**
     * 同步历史push站内信数据
     *
     * @return
     */
    @PostMapping("/initHistoryData")
    @RepeatSubmit
    @Log(title = "同步历史push站内信数据", businessType = BusinessType.OTHER)
    public Result<Boolean> initHistoryPopData() {
        //  popRegionService.initHistoryData(PushRegionEnum.PUSH_MESSAGE_REGION.getType());
        return CommonResult.success(Boolean.TRUE);
    }
}
