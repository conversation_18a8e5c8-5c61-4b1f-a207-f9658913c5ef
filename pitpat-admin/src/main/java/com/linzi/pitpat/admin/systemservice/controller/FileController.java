package com.linzi.pitpat.admin.systemservice.controller;

import com.linzi.pitpat.admin.model.UploadFileReturnMd5Dto;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.request.file.UploadFileUrlReq;
import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 文件控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 */
@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {

    /**
     * 通用上传请求
     */
    @PostMapping("/upload")
    public Result uploadFile(MultipartFile file) {
        try {
            String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());
            Map<String, Object> map = AwsUtil.putS3Object(file, yyyyMM);

            Map<String, Object> ajax = new HashMap<>();
            ajax.put("fileName", map.get("fileName"));
            ajax.put("url", map.get("url"));
            return CommonResult.success(ajax);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 上传音乐
     *
     * @param file
     * @return
     */
    @PostMapping("/uploadMusic")
    public Result uploadMusic(MultipartFile file) {
        try {
            Map<String, Object> map = AwsUtil.putS3Object(file, "activityMusic");
            Map<String, Object> ajax = new HashMap<>();
            ajax.put("fileName", map.get("fileName"));
            ajax.put("url", map.get("url"));
            return CommonResult.success(ajax);
        } catch (Exception e) {
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        //try
        //{
        //    if (!FileUtils.checkAllowDownload(fileName))
        //    {
        //        throw new Exception(String.format("文件名称(%s)非法，不允许下载。 ", fileName));
        //    }
        //    String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
        //    String filePath = AdminConfig.getDownloadPath() + fileName;
        //    File file = new File(filePath);
        //    response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        //    FileUtils.setAttachmentResponseHeader(response, realFileName);
        //    //FileUtils.writeToStream(file, response.getOutputStream());
        //    if (delete)
        //    {
        //        //FileUtils.del(file);
        //    }
        //}
        //catch (Exception e)
        //{
        //    log.error("下载文件失败", e);
        //}
        throw new BizException("不支持本地文件下载");
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        //try
        //{
        //    if (!FileUtils.checkAllowDownload(resource))
        //    {
        //        throw new Exception(String.format("资源文件(%s)非法，不允许下载。 ", resource));
        //    }
        //    // 本地资源路径
        //    String localPath = AdminConfig.getProfile();
        //    // 数据库资源地址
        //    String downloadPath = localPath + subAfter(resource, Constants.RESOURCE_PREFIX);
        //    // 下载名称
        //    String downloadName = subAfter(downloadPath, "/");
        //    response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        //    File file = new File(downloadPath);
        //    FileUtils.setAttachmentResponseHeader(response, downloadName);
        //    //FileUtils.writeToStream(file, response.getOutputStream());
        //}
        //catch (Exception e)
        //{
        //    log.error("下载文件失败", e);
        //}
        throw new BizException("不支持本地文件下载");
    }

    /**
     * 上传文件返回MD5值
     *
     * @param file 文件
     * @return Result
     */
    @PostMapping("/uploadFileReturnMd5")
    public Result<UploadFileReturnMd5Dto> uploadFileReturnMd5(MultipartFile file) {
        try {
            String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());
            Map<String, Object> map = AwsUtil.putS3Object(file, yyyyMM);
            UploadFileReturnMd5Dto uploadFileReturnMd5Dto = new UploadFileReturnMd5Dto();
            uploadFileReturnMd5Dto.setFileName(String.valueOf(map.get("fileName")));
            uploadFileReturnMd5Dto.setUrl(String.valueOf(map.get("url")));
            uploadFileReturnMd5Dto.setFileMd5(DigestUtils.md5Hex(AwsUtil.getObjectFile(file)));
            uploadFileReturnMd5Dto.setSize(file.getSize());
            return CommonResult.success(uploadFileReturnMd5Dto);
        } catch (Exception e) {
            log.error("uploadFileReturnMd5 error", e);
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 上传文件返回MD5值
     *
     * @param file 文件
     * @return Result
     */
    @PostMapping("/uploadLoadReturnMd5")
    public Result<UploadFileReturnMd5Dto> uploadLoadReturnMd5(MultipartFile file) {
        try {
            String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());
            Map<String, Object> map = AwsUtil.putS3Object(file, yyyyMM);
            UploadFileReturnMd5Dto uploadFileReturnMd5Dto = new UploadFileReturnMd5Dto();
            uploadFileReturnMd5Dto.setFileName(String.valueOf(map.get("fileName")));
            uploadFileReturnMd5Dto.setUrl(String.valueOf(map.get("url")));
            uploadFileReturnMd5Dto.setFileMd5(DigestUtils.md5Hex(AwsUtil.getObjectFile(file)));
            uploadFileReturnMd5Dto.setSize(file.getSize());
            return CommonResult.success(uploadFileReturnMd5Dto);
        } catch (Exception e) {
            log.error("uploadFileReturnMd5 error", e);
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 创建文件上传一次性签名
     */
    @PostMapping("/uploadUrl")
    public Result<String> uploadUrl(@RequestBody @Validated UploadFileUrlReq dto) {
        String result = AwsUtil.createPreSignedPutUrl(dto.getFileName());
        return CommonResult.success(result);
    }

    /**
     * 创建文件上传一次性访问地址
     */
    @PostMapping("/getUrl")
    public Result<String> getUrl(@RequestBody @Validated UploadFileUrlReq dto) {
        String result = AwsUtil.createPreSignedGetUrl(dto.getFileName());
        return CommonResult.success(result);
    }

}
