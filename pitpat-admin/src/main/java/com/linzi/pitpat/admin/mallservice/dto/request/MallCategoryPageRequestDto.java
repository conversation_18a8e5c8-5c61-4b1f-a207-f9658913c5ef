package com.linzi.pitpat.admin.mallservice.dto.request;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class MallCategoryPageRequestDto extends PageQuery {

    private Long id;
    //类目页ID
    private String categoryPageCode;
    //备注
    private String remark;

    private ZonedDateTime createStartTime;

    private ZonedDateTime createEndTime;

    //默认语言
    private String defaultLanguageCode;

    /**
     * 国家类型码
     * @since 4.6.4
     */
    private List<String> countryCodeList;

}
