package com.linzi.pitpat.admin.commom.config;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.convert.converter.Converter;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;


@Configuration
// 表示通过aop框架暴露该代理对象,AopContext能够访问
@EnableAspectJAutoProxy(exposeProxy = true)
public class ApplicationConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            //builder.timeZone(TimeZone.getTimeZone("GMT+8"));
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            builder.serializerByType(ZonedDateTime.class, new ZonedDateTimeSerializer(dateTimeFormatter));
            builder.deserializerByType(ZonedDateTime.class, new ZonedDateTimeDeserializer(dateTimeFormatter));
        };
    }

    /**
     * description:java.util.Date转换器, 适用于 GetMapping 中的日期类型转换
     */
    @Bean
    public Converter<String, Date> dateConverter() {
        return new Converter<String, Date>() {
            @Override
            public Date convert(String source) {
                try {
                    return new Date(Long.parseLong(source));
                } catch (Exception e) {
                    return DateUtil.getDate(source);
                }
            }
        };
    }
}


