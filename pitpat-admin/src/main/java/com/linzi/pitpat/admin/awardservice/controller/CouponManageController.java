package com.linzi.pitpat.admin.awardservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.admin.awardservice.manager.CouponManager;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.request.CouponDetailReqDto;
import com.linzi.pitpat.data.awardservice.model.request.CouponReq;
import com.linzi.pitpat.data.awardservice.model.request.CouponSaveReq;
import com.linzi.pitpat.data.awardservice.model.request.CouponUpdateReq;
import com.linzi.pitpat.data.awardservice.model.request.CouponUpdateStatusReq;
import com.linzi.pitpat.data.awardservice.model.request.MallCouponCodeReqDto;
import com.linzi.pitpat.data.awardservice.model.request.OfficialTournamentReq;
import com.linzi.pitpat.data.awardservice.model.vo.ActivityListForCouponVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponAdminListVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponDetailVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponRouteConfigVo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponUpdateDetailVo;
import com.linzi.pitpat.data.awardservice.model.vo.OfficialTournamentTypeVo;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.entity.dto.message.UserCouponStatisticsDto;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.request.BaseReq;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 卷管理控制器
 */
@RestController
@RequestMapping("/coupon/manage")
@Slf4j
@RequiredArgsConstructor
public class CouponManageController extends BaseController {

    private final CouponService couponService;
    private final AppRouteConfigService appRouteConfigService;
    private final ZnsRunActivityService runActivityService;
    private final UserCouponService userCouponService;
    private final CouponManager couponManager;

    /**
     * 卷列表
     *
     * @param couponReq
     * @return
     * @tag 2.3
     */
    @PostMapping("/list")
    public Result<Page<CouponAdminListVo>> list(@RequestBody CouponReq couponReq) {
        Page<Coupon> couponPage = couponService.listAll(couponReq);
        Page<CouponAdminListVo> page = new Page<>();
        List<CouponAdminListVo> wrapperList = new ArrayList<>();
        page.setTotal(couponPage.getTotal());
        page.setCurrent(couponPage.getCurrent());
        List<Coupon> records = couponPage.getRecords();

        //查询优惠券已领取的数量（原字段统计有出入）
        Map<Long, Integer> map = new HashMap<>(); // key->优惠券Id，val->优惠券发放数量
        if (!CollectionUtils.isEmpty(records)) {
            List<Long> couponIds = records.stream().map(Coupon::getId).collect(Collectors.toList());
            List<UserCouponStatisticsDto> dtoList = userCouponService.selectCouponStatisticsDto(couponIds);
            map = dtoList.stream().collect(Collectors.toMap(UserCouponStatisticsDto::getCouponId, UserCouponStatisticsDto::getCouponNum));
        }
        for (Coupon record : records) {
            CouponAdminListVo couponAdminListVo = new CouponAdminListVo();
            BeanUtils.copyProperties(record, couponAdminListVo);
            // 后台管理展示券内容
            couponAdminListVo.setCouponContent(couponService.getCouponContent(record));
            couponAdminListVo.setCouponValidityPeriod(couponService.getCouponValidityPeriod(record));
            couponAdminListVo.setCountryCodeList(JsonUtil.readList(record.getCountryCode(),String.class));
            Integer couponNum = map.get(record.getId());
            if (couponNum != null) {
                record.setQuotaSend(couponNum);
            }
            if (record.getQuota() == -1) {
                couponAdminListVo.setRemainingNum(-1);
            } else {
                int remainingNum = record.getQuota() - record.getQuotaSend();
                remainingNum = Math.max(remainingNum, 0);
                couponAdminListVo.setRemainingNum(remainingNum);
            }
            if (Objects.nonNull(record.getReceiveEnd()) && record.getReceiveEnd().isBefore(ZonedDateTime.now())) {
                //已超过领取时间，券失效
                couponAdminListVo.setStatus(CouponConstant.CouponStatusEnum.STATUS_N1.type);
            }
            wrapperList.add(couponAdminListVo);
        }
        page.setRecords(wrapperList);
        return CommonResult.success(page);
    }

    /**
     * 新增修改卷模板
     */
    @PostMapping("/saveOrModify")
    @RepeatSubmit
    @Log(title = "新增修改卷模板", businessType = BusinessType.INSERT)
    public Result<Boolean> saveOrModify(@RequestBody CouponSaveReq couponSaveReq) {
        couponSaveReq.setOperateName(SecurityUtils.getUsername());
        couponManager.saveOrModify(couponSaveReq);
        return CommonResult.success(true);
    }

    /**
     * 更新卷模板(废弃，使用上面的接口，4.4.3只会可以删除)
     */
    @Deprecated(since = "4.4.3")
    @PostMapping("/update")
    @Log(title = "更新卷模板", businessType = BusinessType.UPDATE)
    public Result<Boolean> update(@RequestBody @Validated CouponUpdateReq couponUpdateReq) {
        couponUpdateReq.setOperateName(SecurityUtils.getUsername());
        return couponService.updateCouponConfig(couponUpdateReq);
    }


    /**
     * 卷状态更改
     *
     * @tag 2.3
     */
    @PostMapping("/changeStatus")
    @Log(title = "卷状态更改", businessType = BusinessType.UPDATE)
    public Result<Boolean> changeStatus(@RequestBody CouponUpdateStatusReq couponUpdateStatusReq) {
        boolean result = couponService.changeStatus(couponUpdateStatusReq);
        return CommonResult.success(result);
    }

    /**
     * 卷库存数量增加
     *
     * @param couponUpdateStatusReq
     * @return
     * @tag 2.3
     */
    @PostMapping("/quotaAdd")
    @RepeatSubmit
    @Log(title = "卷库存数量增加", businessType = BusinessType.UPDATE)
    public Result<Boolean> quotaAdd(@RequestBody @Validated CouponUpdateStatusReq couponUpdateStatusReq) {
        boolean result = couponService.quotaAdd(couponUpdateStatusReq);
        return CommonResult.success(result);
    }


    /**
     * 卷路由查询列表
     *
     * @tag 2.3
     */
    @PostMapping(value = "/couponRouteConfigList", consumes = "application/json", headers = {"Content-Type=application/json;charset=UTF-8", "Accept=application/json"})
    public Result<List<CouponRouteConfigVo>> couponRouteConfigList() {
        return CommonResult.success(appRouteConfigService.couponRouteConfigList());
    }

    /**
     * 卷路由查询列表
     *
     * @tag 3.0赛事分类作为券路由
     */
    @PostMapping(value = "/couponRouteConfigListV2", consumes = "application/json", headers = {"Content-Type=application/json;charset=UTF-8", "Accept=application/json"})
    public Result<List<CouponRouteConfigVo>> couponRouteConfigListV2() {
        return CommonResult.success(appRouteConfigService.couponRouteConfigListV2());
    }


    /**
     * 卷明细页面(废弃，使用下面的，4.4.3之后可以删除)
     */
    @Deprecated(since = "4.4.3")
    @PostMapping("/detail")
    public Result<CouponDetailVo> detail(@RequestBody BaseReq baseReq) {
        return CommonResult.success(couponService.couponDetail(baseReq.getId()));
    }

    /**
     * 卷模板详情
     */
    @PostMapping("/couponDetail")
    public Result<CouponUpdateDetailVo> couponDetail(@RequestBody CouponDetailReqDto req) {
        CouponUpdateDetailVo couponUpdateDetailVo = couponManager.couponDetail(req);
        return CommonResult.success(couponUpdateDetailVo);
    }

    /**
     * 官方赛事活动明细选择列表
     *
     * @tag 2.3
     */
    @PostMapping("/officialTournament")
    public Result<Page<ActivityListForCouponVo>> officialTournament(@RequestBody OfficialTournamentReq officialTournamentReq) {
        Page<ActivityListForCouponVo> page = runActivityService.officialTournament(officialTournamentReq);
        return CommonResult.success(page);
    }

    /**
     * 官方赛事活动类型
     *
     * @tag 2.3
     */
    @PostMapping(value = "/officialTournamentType", consumes = "application/json", headers = {"Content-Type=application/json;charset=UTF-8", "Accept=application/json"})
    public Result<List<OfficialTournamentTypeVo>> officialTournamentType() {
        List<OfficialTournamentTypeVo> list = Lists.newArrayList();
//        list.add(new OfficialTournamentTypeVo(RunActivityTypeEnum.OFFICIAL_ENENT.getCode(),RunActivityTypeEnum.OFFICIAL_ENENT.getName()));
//        list.add(new OfficialTournamentTypeVo(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getCode(),RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getName()));
//        list.add(new OfficialTournamentTypeVo(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getCode(),RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getName()));
//        list.add(new OfficialTournamentTypeVo(RunActivityTypeEnum.TASK_ACTIVITY.getCode(),RunActivityTypeEnum.TASK_ACTIVITY.getName()));
//        list.add(new OfficialTournamentTypeVo(RunActivityTypeEnum.H5_ACTIVITY.getCode(),RunActivityTypeEnum.H5_ACTIVITY.getName()));
        list.add(new OfficialTournamentTypeVo(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType(), RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getName()));
        return CommonResult.success(list);
    }

    /**
     * 通过优惠券code查询商城优惠券
     */
    @PostMapping("/findMallCouponByCodes")
    public Result<Page<MallCouponDto>> findMallCouponByCodes(@RequestBody MallCouponCodeReqDto req) {
        Page<MallCouponDto> list = couponManager.findMallCouponByCodes(req);
        return CommonResult.success(list);
    }

}
