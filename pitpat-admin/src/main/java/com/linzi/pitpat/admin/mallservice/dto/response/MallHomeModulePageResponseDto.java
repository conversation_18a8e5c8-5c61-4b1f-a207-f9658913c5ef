package com.linzi.pitpat.admin.mallservice.dto.response;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class MallHomeModulePageResponseDto {

    //模版ID
    private Long id;
    //商城首页ID
    private Long mallHomeId;
    //模块类型，0:图片，1:商品卡片，2文字tab
    private Integer type;
    //原商品ID
    private List<Long> sourceGoodsIds;
    //源设备型号
    private List<String> sourceEquipmentModel;

    private String creator;

    private ZonedDateTime gmtCreate;
}
