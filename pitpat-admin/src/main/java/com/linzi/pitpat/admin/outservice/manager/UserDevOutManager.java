package com.linzi.pitpat.admin.outservice.manager;

import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentActivateRecordService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityAuditService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityDetailService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityService;
import com.linzi.pitpat.data.equipmentservice.service.UserEquipmentShareLogService;
import com.linzi.pitpat.data.equipmentservice.service.UserEquipmentShareService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUserDevRequestDto;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户设备对外Manager
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class UserDevOutManager {

    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final ZnsTreadmillService znsTreadmillService;
    private final UserEquipmentShareService userEquipmentShareService;
    private final UserEquipmentShareLogService userEquipmentShareLogService;
    private final EquipmentQualityService equipmentQualityService;
    private final EquipmentQualityAuditService equipmentQualityAuditService;
    private final EquipmentQualityDetailService equipmentQualityDetailService;
    private final EquipmentActivateRecordService equipmentActivateRecordService;

    /**
     * 删除设备的绑定记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void delBindRecord(DelUserDevRequestDto req) {
        //删除绑定记录
        znsUserEquipmentService.delBindRecord(req);
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(req.getEquipmentNo());
        if (treadmillEntity == null){
            return;
        }
        //删除设备分享记录
        userEquipmentShareService.delByTreadmillId(treadmillEntity.getId());
        //删除设备分享日志
        userEquipmentShareLogService.delByTreadmillId(treadmillEntity.getId());
        //删除设备激活记录
        equipmentActivateRecordService.delByBluetoothMac(treadmillEntity.getBluetoothMac());
        //删除设备总质保
        equipmentQualityService.delByBluetoothMac(treadmillEntity.getBluetoothMac());
        //删除设备质保明细
        equipmentQualityDetailService.delByBluetoothMac(treadmillEntity.getBluetoothMac());
        //删除设备审核记录
        equipmentQualityAuditService.delByBluetoothMac(treadmillEntity.getBluetoothMac());
    }
}
