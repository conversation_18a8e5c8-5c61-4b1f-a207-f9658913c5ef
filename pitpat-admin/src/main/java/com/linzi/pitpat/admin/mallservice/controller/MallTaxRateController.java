package com.linzi.pitpat.admin.mallservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.manager.region.RegionManager;
import com.linzi.pitpat.admin.systemservice.converter.SysTaxRateConverter;
import com.linzi.pitpat.admin.systemservice.dto.request.AddOrModifyTaxRateReqDto;
import com.linzi.pitpat.admin.systemservice.dto.request.RemoveTaxRateReqDto;
import com.linzi.pitpat.admin.systemservice.dto.request.TaxRateListReqDto;
import com.linzi.pitpat.admin.systemservice.dto.response.TaxRateListRespDto;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.mallservice.model.entity.MallTaxRateDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.service.MallTaxRateService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.systemservice.dto.response.CountryResponseDto;
import com.linzi.pitpat.data.systemservice.enums.RegionConstants;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.ZnsCountryEntity;
import com.linzi.pitpat.data.systemservice.model.query.SysTaxRatePageQuery;
import com.linzi.pitpat.data.systemservice.model.query.SysTaxRateQuery;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 税率管理控制器
 */
@RestController
@RequestMapping("/system/taxRate")
@RequiredArgsConstructor
public class MallTaxRateController extends BaseController {

    private final MallTaxRateService sysTaxRateService;
    private final ZnsCountryService znsCountryService;
    private final AreaService areaService;
    private final SysTaxRateConverter sysTaxRateConverter;
    private final ZnsGoodsService znsGoodsService;
    private final RegionManager regionManager;


    /**
     * 获取国、省地址信息
     */
    @GetMapping("/getAddressInfo/{countryCode}")
    public Result<CountryResponseDto> getAddressInfo(@PathVariable(value = "countryCode", required = false) String countryCode) {
        countryCode = Optional.ofNullable(countryCode).orElse(I18nConstant.CountryCodeEnum.US.code);
        List<CountryResponseDto> list = regionManager.countryList(List.of(countryCode));
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException("countryCode 不存在");
        }
        return CommonResult.success(list.get(0));
    }

    /**
     * 税率管理列表查询
     */
    @PostMapping("/pageList")
    public Result<Page<TaxRateListRespDto>> pageList(@RequestBody TaxRateListReqDto req) {
        SysTaxRatePageQuery query = sysTaxRateConverter.toQuery(req);
        Page<MallTaxRateDo> pageDo = sysTaxRateService.findPage(query);
        Page<TaxRateListRespDto> resp = new Page<>(req.getPageNum(), req.getPageSize(), pageDo.getTotal());
        List<MallTaxRateDo> records = pageDo.getRecords();
        List<TaxRateListRespDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            resp.setRecords(list);
            return CommonResult.success(resp);
        }
        for (MallTaxRateDo sysTaxRateDo : records) {
            TaxRateListRespDto dto = getListRespDto(sysTaxRateDo);
            list.add(dto);
        }
        resp.setRecords(list);
        return CommonResult.success(resp);
    }

    /**
     * 转换商品信息
     */
    private TaxRateListRespDto getListRespDto(MallTaxRateDo sysTaxRateDo) {
        TaxRateListRespDto dto = sysTaxRateConverter.toDto(sysTaxRateDo);
        String goodsIdStr = dto.getGoodsIdStr();
        if (StringUtils.hasText(goodsIdStr)) {
            List<Long> goodsIds = Arrays.stream(goodsIdStr.split(",")).map(Long::valueOf).toList();
            dto.setGoodsIds(goodsIds);
            List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(goodsIds);
            if (!CollectionUtils.isEmpty(goodsEntities)) {
                List<String> goodsNames = goodsEntities.stream().map(ZnsGoodsEntity::getTitle).distinct().toList();
                dto.setGoodsNames(goodsNames);
            }
        }
        return dto;
    }

    /**
     * 新增或修改税率
     *
     * @param req
     * @return
     */
    @PostMapping("/saveOrModify")
    public Result<Boolean> saveOrModify(@Validated @RequestBody AddOrModifyTaxRateReqDto req) {
        ZnsCountryEntity countryEntity = znsCountryService.findByCountryCode(req.getCountryCode());
        if (countryEntity == null) {
            throw new BaseException("国家code不正确");
        }
        AreaEntity areaEntity = areaService.selectAreaByCode(req.getProvinceCode());
        if (areaEntity == null) {
            throw new BaseException("州code不正确");
        }
        //校验州是否重复
        SysTaxRateQuery query = new SysTaxRateQuery().setProvinceCode(req.getProvinceCode()).setNeId(req.getId());
        MallTaxRateDo sysTaxRateDo = sysTaxRateService.findByQuery(query);
        if (Objects.nonNull(sysTaxRateDo)) {
            throw new BaseException("州税率不能重复");
        }
        String username = SecurityUtils.getUsername();
        sysTaxRateDo = new MallTaxRateDo();
        sysTaxRateDo.setTaxRate(req.getTaxRate());
        sysTaxRateDo.setCountryCode(req.getCountryCode());
        sysTaxRateDo.setCountry(countryEntity.getNameCn());
        sysTaxRateDo.setProvinceCode(req.getProvinceCode());
        sysTaxRateDo.setProvince(areaEntity.getAreaNameCn());
        sysTaxRateDo.setState(RegionConstants.StateEnum.state_1.getCode());
        if (!CollectionUtils.isEmpty(req.getGoodsIds())) {
            sysTaxRateDo.setGoodsIdStr(req.getGoodsIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if (req.getId() == null) {
            //新增
            sysTaxRateDo.setCreator(username);
            sysTaxRateService.create(sysTaxRateDo);
        } else {
            //修改
            sysTaxRateDo.setId(req.getId());
            sysTaxRateDo.setModifier(username);
            sysTaxRateDo.setGmtModified(ZonedDateTime.now());
            sysTaxRateService.update(sysTaxRateDo);
        }
        return CommonResult.success(true);
    }

    /**
     * 获取税费管理详情
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public Result<TaxRateListRespDto> detail(@PathVariable("id") Long id) {
        MallTaxRateDo sysTaxRateDo = sysTaxRateService.findById(id);
        return CommonResult.success(getListRespDto(sysTaxRateDo));
    }


    /**
     * 删除税率
     *
     * @param req
     * @return
     */
    @PostMapping("/remove")
    public Result<Boolean> delete(@Validated @RequestBody RemoveTaxRateReqDto req) {
        sysTaxRateService.deleteById(req.getId());
        return CommonResult.success(true);
    }
}
