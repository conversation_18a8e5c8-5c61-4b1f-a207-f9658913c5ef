package com.linzi.pitpat.admin.systemservice.dto.response;

import com.linzi.pitpat.data.systemservice.enums.RegionConstants;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 税率列表返回数据
 */
@Data
public class TaxRateListRespDto {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 国家Code
     */
    private String countryCode;

    /**
     * 国家明成祖
     */
    private String country;

    /**
     * 州code
     */
    private String provinceCode;

    /**
     * 州名称
     */
    private String province;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 状态，0：停用，1：启用
     *
     * @see RegionConstants.StateEnum
     */
    private Integer state;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 适用商品Id，为空所有商品都适用，多个商品用,隔开
     */
    private String goodsIdStr;

    /**
     * 创建时间
     */
    private ZonedDateTime gmtCreate;

    /**
     * 更新者
     */
    private String modifier;

    /**
     * 更新时间
     */
    private ZonedDateTime gmtModified;

    /**
     * 商品名称，为空就是全部商品
     */
    private List<String> goodsNames;

    /**
     * 适用商品id，为空就是全部商品
     */
    private List<Long> goodsIds;
}
