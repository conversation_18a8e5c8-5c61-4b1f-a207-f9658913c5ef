package com.linzi.pitpat.admin.outservice.controller;

import com.linzi.pitpat.admin.outservice.manager.DeviceOutManager;
import com.linzi.pitpat.admin.outservice.manager.UserDevOutManager;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUpgradeRequestDto;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUserDevRequestDto;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备对外提供接口
 */
@RestController
@RequestMapping({"/out/dev"})
@RequiredArgsConstructor
public class DeviceOutController {


    private final DeviceOutManager deviceOutManager;


    /**
     * 主动同步device数据
     */
    @PostMapping("/syncData")
    public Result<Void> syncData() {
        deviceOutManager.syncData();
        return CommonResult.success();
    }

    /**
     * 删除设备的升级记录
     */
    @PostMapping("/delUpgradeRecord")
    public Result<Void> delUpgradeRecord(@RequestBody @Validated DelUpgradeRequestDto req) {
        deviceOutManager.delUpgradeRecord(req);
        return CommonResult.success();
    }

}
