package com.linzi.pitpat.admin.manager.vip;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.model.Dto.request.vip.VipPassRemindSaveRequestDto;
import com.linzi.pitpat.admin.model.Dto.request.vip.VipRightDetailRequestDto;
import com.linzi.pitpat.admin.model.Dto.response.vip.VipPassRemindDetailResponseDto;
import com.linzi.pitpat.admin.model.Dto.response.vip.VipPassRemindListResponseDto;
import com.linzi.pitpat.admin.model.Dto.response.vip.VipRightDetailResponseDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.enums.VipPassRemindEnum;
import com.linzi.pitpat.data.enums.VipStatusUserEnum;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.paymentservice.enums.PaymentMethodEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentTradeStateEnum;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentGooglePlayReceiptsDo;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentIosReceiptsDo;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentPaypalSubscriptionReceipts;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentTradeDo;
import com.linzi.pitpat.data.paymentservice.service.PaymentGoodsService;
import com.linzi.pitpat.data.paymentservice.service.PaymentGooglePlayReceiptsService;
import com.linzi.pitpat.data.paymentservice.service.PaymentIosReceiptsService;
import com.linzi.pitpat.data.paymentservice.service.PaymentPaypalSubscriptionReceiptsService;
import com.linzi.pitpat.data.paymentservice.service.PaymentTradeService;
import com.linzi.pitpat.data.systemservice.model.entity.VipUserSubscribeRecord;
import com.linzi.pitpat.data.userservice.dto.request.UserVipInfoListDto;
import com.linzi.pitpat.data.userservice.dto.request.VipRuleRequestDto;
import com.linzi.pitpat.data.userservice.dto.request.VipUserOrderDetailsQueryDto;
import com.linzi.pitpat.data.userservice.dto.response.VipRuleResponseDto;
import com.linzi.pitpat.data.userservice.dto.response.VipUserOpenRecordDto;
import com.linzi.pitpat.data.userservice.dto.response.VipUserOrderDetailsDto;
import com.linzi.pitpat.data.userservice.dto.response.VipUserPageDto;
import com.linzi.pitpat.data.userservice.enums.SubscribeStateEnum;
import com.linzi.pitpat.data.userservice.model.entity.VipUserLogDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.bo.VipUserRecordDto;
import com.linzi.pitpat.data.userservice.model.entity.label.UserGroupEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemind;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindBeta;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindI18nMsg;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindI18nMsgBeta;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContent;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContentBeta;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRule;
import com.linzi.pitpat.data.userservice.model.query.VipPassQuery;
import com.linzi.pitpat.data.userservice.model.query.VipRightQuery;
import com.linzi.pitpat.data.userservice.model.query.VipRuleQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserLogQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserSubscribeRecordQuery;
import com.linzi.pitpat.data.userservice.service.VipUserLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindBetaService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindI18nMsgBetaService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindI18nMsgService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindService;
import com.linzi.pitpat.data.userservice.service.vip.VipRightI18nContentBetaService;
import com.linzi.pitpat.data.userservice.service.vip.VipRightI18nContentService;
import com.linzi.pitpat.data.userservice.service.vip.VipRuleService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserSubscribeRecordService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 管理后台会员业务处理类
 *
 * <AUTHOR>
 * @Date 2023/8/9 下午5:24
 * @Description
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserVipAdminManager {

    private final VipUserService vipUserService;
    private final ZnsUserService znsUserService;
    private final VipPassRemindService vipPassRemindService;
    private final VipPassRemindBetaService vipPassRemindBetaService;
    private final VipPassRemindI18nMsgService vipPassRemindI18nMsgService;
    private final VipPassRemindI18nMsgBetaService vipPassRemindI18nMsgBetaService;
    private final VipRightI18nContentService vipRightI18nContentService;
    private final VipRuleService vipRuleService;
    private final UserGroupService userGroupService;
    private final VipRightI18nContentBetaService vipRightI18nContentBetaService;
    private final VipUserLogService vipUserLogService;
    private final VipUserSubscribeRecordService vipUserSubscribeRecordService;
    private final PaymentTradeService paymentTradeService;
    private final PaymentIosReceiptsService paymentIosReceiptsService;
    private final PaymentPaypalSubscriptionReceiptsService paymentPaypalSubscriptionReceiptsService;
    private final PaymentGooglePlayReceiptsService paymentGooglePlayReceiptsService;
    private final PaymentGoodsService paymentGoodsService;
    @Autowired
    private RedissonClient redissonClient;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;
    private static final String SPLIT_FLAG_STR = ",";


    public Page<VipUserPageDto> memberPage(UserVipInfoListDto dto) {
        VipUserQuery query = VipUserQuery.builder().emailAddress(dto.getEmailAddress()).emailStatus(dto.getEmailStatus())
                .vipStatus(dto.getVipStatus()).memberType(1).purchaseType(dto.getPurchaseType()).build();

        if (StringUtils.hasText(dto.getUserCode())) {
            ZnsUserEntity user = znsUserService.findByUserCode(dto.getUserCode());
            if (Objects.isNull(user)) {
                throw new BaseException("userCode不正确,没有查到对应用户");
            }
            query.setUserId(user.getId());
        }
        query.setPageNum(dto.getPageNum());
        query.setPageSize(dto.getPageSize());
        Page<VipUserRecordDto> page = vipUserService.findPageWithPaymentMethod(query, dto.getPaymentMethod());

        Page<VipUserPageDto> result = new Page<>();
        List<VipUserRecordDto> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            List<VipUserPageDto> vipUserPageDtos = records.stream().map(record -> {
                VipUserPageDto vipUserPageDto = new VipUserPageDto();
                BeanUtils.copyProperties(record, vipUserPageDto);
                ZnsUserEntity user = znsUserService.findById(record.getUserId());
                vipUserPageDto.setEmailAddress(record.getEmailAddressEn());
                vipUserPageDto.setUserName(Objects.nonNull(user) ? user.getFirstName() : record.getUserName());
                vipUserPageDto.setUserCode(Objects.nonNull(user) ? user.getUserCode() : null);
                //除了连续订阅，其余类型都返回3：其他
                vipUserPageDto.setPurchaseType(Objects.nonNull(record.getPurchaseType()) && (record.getPurchaseType() > 2 || record.getPurchaseType() < 0) ? 3 : record.getPurchaseType());

                if ((Objects.nonNull(record.getPurchaseType()) && record.getPurchaseType() <= 4)) {
                    VipUserSubscribeRecordQuery vipUserSubscribeRecordQuery = new VipUserSubscribeRecordQuery();
                    vipUserSubscribeRecordQuery.setGoodsId(record.getPurchaseType().longValue()).setUserId(record.getUserId())
                            .setSubscribeStateList(
                                    List.of(SubscribeStateEnum.SUBSCRIBE_SUCCESS.getCode()));
                    List<VipUserSubscribeRecord> vipUserSubscribeRecords = vipUserSubscribeRecordService.findListByQuery(vipUserSubscribeRecordQuery);
                    if (!CollectionUtils.isEmpty(vipUserSubscribeRecords)
                            && !VipStatusUserEnum.EXPIRED.getCode().equals(vipUserPageDto.getVipStatus())) {
                        VipUserSubscribeRecord vipUserSubscribeRecord = vipUserSubscribeRecords.get(0);
                        vipUserPageDto.setConsecutiveTimes(vipUserSubscribeRecord.getConsecutiveTimes());
                    } else {
                        vipUserPageDto.setConsecutiveTimes(null);
                        vipUserPageDto.setPaymentMethod(null);
                    }
                }
                VipUserLogQuery vipUserLogQuery = new VipUserLogQuery();
                vipUserLogQuery.setLimit(1).setUserId(record.getUserId());
                List<VipUserLogDo> vipUserLogList = vipUserLogService.findList(vipUserLogQuery);
                //最新的修改时间
                vipUserPageDto.setModifyTime(!CollectionUtils.isEmpty(vipUserLogList) ? Date.from(vipUserLogList.get(0).getGmtCreate().toInstant()) : null);
                return vipUserPageDto;
            }).toList();

            result.setRecords(vipUserPageDtos);
            result.setTotal(page.getTotal());
            result.setSize(page.getSize());
            result.setCurrent(page.getCurrent());
        }
        return result;
    }

    public List<VipPassRemindListResponseDto> vipPassRemindList() {
        List<VipPassRemindListResponseDto> result = new ArrayList<>();
        List<VipPassRemind> list = vipPassRemindService.getList(VipPassQuery.builder().build());
        List<VipPassRemindBeta> list2 = vipPassRemindBetaService.getList(VipPassQuery.builder().build());
        Map<Integer, List<VipPassRemind>> collect = list.stream().collect(Collectors.groupingBy(VipPassRemind::getRemindType));
        Map<Integer, List<VipPassRemindBeta>> collect2 = list2.stream().collect(Collectors.groupingBy(VipPassRemindBeta::getRemindType));

        VipRule vipRule = vipRuleService.getOneByQuery(VipRuleQuery.builder().build());

        for (VipPassRemindEnum passRemindEnum : VipPassRemindEnum.values()) {
            Integer remindType = passRemindEnum.getCode();
            VipPassRemindListResponseDto dto = new VipPassRemindListResponseDto();
            dto.setRemindType(remindType);

            List<VipPassRemind> vipPassReminds = collect.get(remindType);
            if (!CollectionUtils.isEmpty(vipPassReminds)) {
                List<VipPassRemindDetailResponseDto> details = BeanUtil.copyBeanList(vipPassReminds, VipPassRemindDetailResponseDto.class);
                details.stream().forEach(item -> item.setDefaultLangCode(vipRule.getDefaultLangCode()));
                dto.setDetails(details);
            }

            List<VipPassRemindBeta> beataVipPassReminds = collect2.get(remindType);
            if (!CollectionUtils.isEmpty(beataVipPassReminds)) {
                List<VipPassRemindDetailResponseDto> details = BeanUtil.copyBeanList(beataVipPassReminds, VipPassRemindDetailResponseDto.class);
                details.stream().forEach(item -> item.setDefaultLangCode(vipRule.getDefaultLangCode()));
                dto.setBetaDetails(details);
            }

            result.add(dto);
        }
        return result;
    }

    public List<VipPassRemindDetailResponseDto> vipPassRemindDetail(Integer remindType, Integer betaVersion) {
        List<VipPassRemindDetailResponseDto> result = new ArrayList<>();
        VipPassRemindDetailResponseDto before = new VipPassRemindDetailResponseDto();
        VipPassRemindDetailResponseDto after = new VipPassRemindDetailResponseDto();
        before.setBeforeAfter(-1);
        after.setBeforeAfter(1);
        result.add(before);
        result.add(after);
        if (betaVersion == 1) {
            // 公开
            List<VipPassRemind> list = vipPassRemindService.getList(VipPassQuery.builder().remindTypeList(Arrays.asList(remindType)).build());
            if (CollectionUtils.isEmpty(list)) return result;
            list = list.stream().sorted(Comparator.comparing(VipPassRemind::getBeforeAfter)).collect(Collectors.toList());
            for (int i = 0; i < list.size(); i++) {
                VipPassRemind item = list.get(i);
                VipPassRemindDetailResponseDto detail = result.get(i);
                BeanUtils.copyProperties(item, detail);
                List<VipPassRemindI18nMsg> i18nMsgs = vipPassRemindI18nMsgService.findByQuery(VipPassQuery.builder().remindId(item.getId()).build());
                detail.setI18nMsgList(i18nMsgs);
            }
        } else {
            // 内测
            List<VipPassRemindBeta> list = vipPassRemindBetaService.getList(VipPassQuery.builder().remindTypeList(Arrays.asList(remindType)).build());
            if (CollectionUtils.isEmpty(list)) return result;
            list = list.stream().sorted(Comparator.comparing(VipPassRemindBeta::getBeforeAfter)).collect(Collectors.toList());
            for (int i = 0; i < list.size(); i++) {
                VipPassRemindBeta item = list.get(i);
                VipPassRemindDetailResponseDto detail = result.get(i);
                BeanUtils.copyProperties(item, detail);
                List<VipPassRemindI18nMsgBeta> i18nMsgs = vipPassRemindI18nMsgBetaService.findByQuery(VipPassQuery.builder().remindId(item.getId()).build());
                detail.setI18nMsgList(BeanUtil.copyBeanList(i18nMsgs, VipPassRemindI18nMsg.class));
            }
        }
        return result;
    }

    public void saveVipPassRemind(VipPassRemindSaveRequestDto dto) {
        Integer remindType = dto.getRemindType();
        List<VipPassRemindDetailResponseDto> details = dto.getDetails();
        if (CollectionUtils.isEmpty(details)) return;
        Integer betaVersion = dto.getBetaVersion();
        if (Objects.isNull(betaVersion)) return;
        if (betaVersion == 1) {
            // 公开
            saveOrUpdateVipPassRemind(details, remindType, betaVersion);
        } else {
            // 内测
            saveOrUpdateVipPassRemind(details, remindType, betaVersion);
            Integer open = dto.getOpen();
            if (Objects.nonNull(open) && open == 1) {
                // 复制到公开
                saveOrUpdateVipPassRemind(details, remindType, 1);
            }
        }

    }

    public void saveOrUpdateVipPassRemind(List<VipPassRemindDetailResponseDto> details, Integer remindType, Integer betaVersion) {
        if (betaVersion == 1) {
            // 公开
            //删除原公共记录
            VipPassQuery query = VipPassQuery.builder().remindType(remindType).build();
            vipPassRemindService.deleteByQuery(query);
            for (VipPassRemindDetailResponseDto vipRemind : details) {
                //删除原记录多语言内容
                if (vipRemind.getId() != null) {
                    vipPassRemindI18nMsgService.deleteByQuery(VipPassQuery.builder().remindId(vipRemind.getId()).build());
                }
                //保存新记录
                VipPassRemind vipPassRemind = new VipPassRemind();
                BeanUtils.copyProperties(vipRemind, vipPassRemind);
                vipPassRemind.setRemindType(remindType);
                vipPassRemind.setId(null);
                Long remindId = vipPassRemindService.saveOrUpdateVipRemind(vipPassRemind);
                List<VipPassRemindI18nMsg> i18nMsgList = vipRemind.getI18nMsgList();
                i18nMsgList.forEach(item -> {
                    item.setId(null);
                    item.setRemindId(remindId);
                });
                //保存新记录多语言内容
                vipPassRemindI18nMsgService.saveBatchVipRemindI18n(i18nMsgList);
            }
        } else {
            // 内测
            for (VipPassRemindDetailResponseDto vipRemind : details) {
                VipPassRemindBeta vipPassRemindBeta = new VipPassRemindBeta();
                BeanUtils.copyProperties(vipRemind, vipPassRemindBeta);
                vipPassRemindBeta.setRemindType(remindType);
                Long betaRemindId = vipPassRemindBetaService.saveOrUpdateVipRemindBeta(vipPassRemindBeta);

                vipPassRemindI18nMsgBetaService.deleteByQuery(VipPassQuery.builder().remindId(betaRemindId).build());

                List<VipPassRemindI18nMsg> i18nMsgList = vipRemind.getI18nMsgList();
                i18nMsgList.forEach(item -> {
                    item.setId(null);
                    item.setRemindId(betaRemindId);
                });
                List<VipPassRemindI18nMsgBeta> vipPassRemindI18nMsgBetas = BeanUtil.copyBeanList(i18nMsgList, VipPassRemindI18nMsgBeta.class);
                vipPassRemindI18nMsgBetaService.saveBatchVipRemindI18n(vipPassRemindI18nMsgBetas);
            }
        }
    }

    public VipRightDetailResponseDto vipRightList() {
        VipRightDetailResponseDto result = new VipRightDetailResponseDto();
        VipRule vipRule = vipRuleService.getOneByQuery(VipRuleQuery.builder().build());
        if (Objects.isNull(vipRule)) return result;
        result.setDefaultLangCode(vipRule.getDefaultLangCode());

        String langName = I18nConstant.LanguageCodeEnum.findByCode(vipRule.getDefaultLangCode()).getName();
        result.setDefaultLangName(langName);

        String validLangCode = vipRule.getValidLangCodes();
        List<String> validLangCodeList = new ArrayList<>();
        if (StringUtils.hasText(validLangCode)) {
            validLangCode = validLangCode.replaceAll(", ", ",");
            validLangCodeList.addAll(Arrays.stream(validLangCode.split(",")).toList());
        }
        result.setValidLangCode(validLangCodeList);

        List<VipRightI18nContent> data = vipRightI18nContentService.findByQuery(VipRightQuery.builder().build());
        List<VipRightI18nContentBeta> betaData = vipRightI18nContentBetaService.findByQuery(VipRightQuery.builder().build());

        result.setData(data);
        result.setBetaData(betaData);
        return result;
    }

    @Transactional
    public void saveVipRight(VipRightDetailRequestDto dto) {
        List<VipRightI18nContent> data = dto.getData();
        if (dto.getBetaVersion() == 1) {
            // 公开
            vipRightI18nContentService.deleteByQuery(VipRightQuery.builder().build());
            data.stream().forEach(item -> {
                item.setId(null);
            });
            vipRightI18nContentService.insertBatch(data);
        } else {
            // 内测
            vipRightI18nContentBetaService.deleteByQuery(VipRightQuery.builder().build());
            List<VipRightI18nContentBeta> betaData = BeanUtil.copyBeanList(data, VipRightI18nContentBeta.class);
            betaData.stream().forEach(item -> {
                item.setId(null);
            });
            vipRightI18nContentBetaService.insertBatch(betaData);
            if (dto.getOpen() == 1) {
                // 需要公开
                vipRightI18nContentService.deleteByQuery(VipRightQuery.builder().build());
                data.stream().forEach(item -> {
                    item.setId(null);
                });
                vipRightI18nContentService.insertBatch(data);
            }
        }
    }

    /**
     * 会员规则详情
     */
    public VipRuleResponseDto vipRuleDetail() {
        VipRuleResponseDto dto = new VipRuleResponseDto();
        VipRule vipRule = vipRuleService.getOneByQuery(VipRuleQuery.builder().build());
        if (Objects.nonNull(vipRule)) {
            dto.setId(vipRule.getId());
            dto.setDefaultLangCode(vipRule.getDefaultLangCode());
            dto.setDefaultLangName(I18nConstant.LanguageCodeEnum.findByCode(vipRule.getDefaultLangCode()).getName());
            String betaGroupIds = vipRule.getBetaGroupIds();
            if (StringUtils.hasText(betaGroupIds)) {
                List<Long> groupIds = Arrays.asList(betaGroupIds.split(SPLIT_FLAG_STR)).stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
                List<UserGroupEntity> userGroupEntities = userGroupService.getByGroupIds(groupIds);
                dto.setGroups(userGroupEntities);
            }

            if (StringUtils.hasText(vipRule.getValidLangCodes())) {
                List<String> validLangCodes = Arrays.asList(vipRule.getValidLangCodes().split(SPLIT_FLAG_STR)).stream().collect(Collectors.toList());
                dto.setValidLangCodes(validLangCodes);
            }
        }
        return dto;
    }


    /**
     * 保存修改会员详情
     *
     * @param dto
     */
    public void saveOrUpdateVipRule(VipRuleRequestDto dto) {
        List<Long> betaGroupIds = dto.getBetaGroupIds();
        List<String> validLangCodes = dto.getValidLangCodes();
        // 调整配置语言之后 需要删除其他过期提醒表中已经不存在的语言的数据
        if (!CollectionUtils.isEmpty(validLangCodes)) {
            // 过期提醒
            vipPassRemindI18nMsgService.deleteByQuery(VipPassQuery.builder().validLangCode(validLangCodes).build());
            vipPassRemindI18nMsgBetaService.deleteByQuery(VipPassQuery.builder().validLangCode(validLangCodes).build());
            // 会员权益
            vipRightI18nContentService.deleteByQuery(VipRightQuery.builder().validLangCode(validLangCodes).build());
            vipRightI18nContentBetaService.deleteByQuery(VipRightQuery.builder().validLangCode(validLangCodes).build());

        }

        String groupIdsStr = betaGroupIds.stream().map(item -> item.toString()).collect(Collectors.joining(SPLIT_FLAG_STR));
        String validLangCodesStr = validLangCodes.stream().collect(Collectors.joining(SPLIT_FLAG_STR));
        VipRule vipRule = new VipRule();
        vipRule.setId(dto.getId());
        vipRule.setDefaultLangCode(dto.getDefaultLangCode());
        vipRule.setBetaGroupIds(groupIdsStr);
        vipRule.setValidLangCodes(validLangCodesStr);
        vipRuleService.saveOrUpdateVipRule(vipRule);
    }

    @FillerMethod
    public List<VipUserOpenRecordDto> findMemberRecord(Long userId) {
        VipUserLogQuery vipUserLogQuery = new VipUserLogQuery();
        vipUserLogQuery.setUserId(userId);
        List<VipUserLogDo> userLogServiceList = vipUserLogService.findList(vipUserLogQuery);
        if (!CollectionUtils.isEmpty(userLogServiceList)) {
            return userLogServiceList.stream().map(s -> {
                VipUserOpenRecordDto vipUserOpenRecordDto = new VipUserOpenRecordDto();
                vipUserOpenRecordDto.setPurchaseType(s.getSourceType());
                vipUserOpenRecordDto.setVipEndtime(Objects.nonNull(s.getVipEndtime()) ? Date.from(s.getVipEndtime().toInstant()) : null);
                vipUserOpenRecordDto.setVipStartTime(Date.from(s.getGmtCreate().toInstant()));
                vipUserOpenRecordDto.setValidDays(s.getEffectiveDays());
                vipUserOpenRecordDto.setTradeNo(s.getTradeNo());
                return vipUserOpenRecordDto;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public Page<VipUserOrderDetailsDto> findOrderDetails(VipUserOrderDetailsQueryDto queryDto) {
        Page<PaymentTradeDo> page = paymentTradeService.findOrderDetails(queryDto);
        List<PaymentTradeDo> records = page.getRecords();
        List<VipUserOrderDetailsDto> recordsDto = new ArrayList<>();
        Page<VipUserOrderDetailsDto> result = new Page<>();
        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(s -> {
                VipUserOrderDetailsDto vipUserOrderDetailsDto = new VipUserOrderDetailsDto();
                ZnsUserEntity user = znsUserService.findById(s.getUserId());
                vipUserOrderDetailsDto.setTradeNo(s.getTradeNo());
                vipUserOrderDetailsDto.setUserCode(Objects.nonNull(user) ? user.getUserCode() : null);
                vipUserOrderDetailsDto.setEmailAddress(Objects.nonNull(user) ? user.getEmailAddress() : null);
                vipUserOrderDetailsDto.setAmount(Objects.nonNull(s.getTotalAmount()) && Objects.equals(s.getIsTrialPeriod(), 0) ? s.getTotalAmount() : new BigDecimal(0));
                vipUserOrderDetailsDto.setPurchaseType(s.getGoodsId().intValue());
                vipUserOrderDetailsDto.setPayChannelType(s.getPaymentMethod());
                vipUserOrderDetailsDto.setOrderStatus(s.getPaymentTradeState());
                vipUserOrderDetailsDto.setPaymentTime(s.getPurchaseDate());
                //会员开通状态定义
                if (Objects.equals(s.getPaymentTradeState(), PaymentTradeStateEnum.PAY_SUCCESS.getCode())) {
                    vipUserOrderDetailsDto.setVipOpenStatus(1);
                } else if (Objects.equals(s.getPaymentTradeState(), PaymentTradeStateEnum.ADMIN_REFUND_SUCCESS.getCode())) {
                    vipUserOrderDetailsDto.setVipOpenStatus(2);
                } else {
                    vipUserOrderDetailsDto.setVipOpenStatus(0);
                }
                //第三方交易id填充
                if (Objects.equals(s.getPaymentMethod(), "APPLE_APP_STORE")) {
                    Optional<PaymentIosReceiptsDo> paymentIosReceiptsDo = paymentIosReceiptsService.selectByTradeNo(s.getTradeNo());
                    paymentIosReceiptsDo.ifPresent(i -> {
                        vipUserOrderDetailsDto.setTransactionId(i.getTransactionId());
                    });
                } else if (Objects.equals(s.getPaymentMethod(), PaymentMethodEnum.GOOGLE_PLAY_PAY.getCode())) {
                    Optional<PaymentGooglePlayReceiptsDo> paymentIosReceiptsDo = paymentGooglePlayReceiptsService.selectByTradeNo(s.getTradeNo());
                    paymentIosReceiptsDo.ifPresent(i -> {
                        vipUserOrderDetailsDto.setTransactionId(i.getTransactionId());
                    });
                } else {
                    PaymentPaypalSubscriptionReceipts firstByTradeNo = paymentPaypalSubscriptionReceiptsService.findFirstByTradeNo(s.getTradeNo());
                    vipUserOrderDetailsDto.setTransactionId(Objects.nonNull(firstByTradeNo) ? firstByTradeNo.getTransactionId() : null);
                }
                recordsDto.add(vipUserOrderDetailsDto);
            });
            result.setRecords(recordsDto);
            result.setTotal(page.getTotal());
            result.setSize(page.getSize());
            result.setCurrent(page.getCurrent());
        }
        return result;
    }
}
