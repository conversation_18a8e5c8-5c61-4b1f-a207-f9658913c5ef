package com.linzi.pitpat.admin.messageservice.controller;

import com.linzi.pitpat.admin.messageservice.converter.AppMessageConverter;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.dto.request.ActivityImAndPushRequestDto;
import com.linzi.pitpat.dto.request.ImAndPushRequestDto;
import com.linzi.pitpat.dto.request.ImbatchSendMsgRequestDto;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/1 11:30
 */
@Slf4j
@RestController
@RequestMapping("/appMessage")
@RequiredArgsConstructor
public class AppMessageController {
    private final AppMessageService appMessageService;
    private final AppMessageConverter appMessageConverter;
    private final TencentImUtil tencentImUtil;

    @PostMapping("/sendImbatchSendMsg")
    Result sendImbatchSendMsg(@RequestBody final ImbatchSendMsgRequestDto request) {
//        tencentImUtil.batchSendMsg(request.getSyncOtherMachine(), request.getFromUserId(), request.getToUserIds(), request.getMsgType(), request.getMsgContent());
        return CommonResult.success();
    }

    @PostMapping("/sendImAndPushUserIds")
    Result sendImAndPushUserIds(final ImAndPushRequestDto request) {
        final ImMessageBo bo = appMessageService.assembleImMessageAward(request.getContent(), request.getJumpValue(), request.getJumpType());
        final MessageBo messageBo = appMessageService.assembleMessage(request.getJumpId(), request.getContent(), request.getJumpType(), request.getNotificationType());
        appMessageService.sendImAndPushUserIds(request.getUserIds(), bo, messageBo);
        return CommonResult.success();
    }

    @PostMapping("/sendActivityImAndPushUserIds")
    Result sendActivityImAndPushUserIds(final ActivityImAndPushRequestDto request) {
        final ImMessageBo imMessageBo = appMessageService.assembleImActivityMessage(request);
        final MessageBo messageBo = appMessageService.assembleMessage(request.getJumpId(), request.getContent(), "4", request.getNotificationType());
        messageBo.setActivityId(request.getActivityId());
        appMessageService.sendImAndPushUserIds(request.getUserIds(), imMessageBo, messageBo);
        return CommonResult.success();
    }
}
