package com.linzi.pitpat.admin.model.Dto.response.vip;


import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContent;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContentBeta;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 17:03
 */
@Data
@NoArgsConstructor
public class VipRightDetailResponseDto {
    /**
     * id
     */
    private Long id;
    /**
     * 默认语言code
     */
    private String defaultLangCode;
    /**
     * 默认语言名称
     */
    private String defaultLangName;
    /**
     * 配置的语言列表
     */
    private List<String> validLangCode;
    /**
     * 国际化数据
     */
    private List<VipRightI18nContent> data;
    /**
     * 内测国际化数据
     */
    private List<VipRightI18nContentBeta> betaData;

}
