package com.linzi.pitpat.admin.commom.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
class ZonedDateTimeSerializer extends JsonSerializer<ZonedDateTime> {

    private static final long serialVersionUID = 1L;
    private final DateTimeFormatter formatter;

    public ZonedDateTimeSerializer(DateTimeFormatter formatter) {
        this.formatter = formatter;
    }

    @Override
    public void serialize(ZonedDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 将时间转换为 Asia/Shanghai 时区
        //ZonedDateTime shanghaiTime = value.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        //以下结果输出一致
        //log.info("zoned utcTime={}", value.toInstant().toEpochMilli());
        //log.info("zoned shanghaiTime={}", shanghaiTime.toInstant().toEpochMilli());
        // 序列化为格式化字符串
        gen.writeNumber(value.toInstant().toEpochMilli());
    }
}
