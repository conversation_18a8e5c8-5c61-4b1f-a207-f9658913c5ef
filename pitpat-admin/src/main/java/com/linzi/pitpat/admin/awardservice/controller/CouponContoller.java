package com.linzi.pitpat.admin.awardservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.resp.UserCouponResp;
import com.linzi.pitpat.data.awardservice.quartz.UpdateCouponStatusTask;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.request.CouponPageQuery;
import com.linzi.pitpat.data.util.file.ExcelUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.TimeZone;

@RestController
@RequestMapping({"/coupon", "/test/coupon"})
@Slf4j
public class CouponContoller {
    @Autowired
    private UserCouponService userCouponService;
    @Autowired
    private UpdateCouponStatusTask updateCouponStatusTask;
    @Autowired
    private UserCouponBizService userCouponBizService;

    @PostMapping("/selectList")
    public Result<Page<UserCouponResp>> selectEveryDayType(@RequestBody CouponPageQuery query) {
        Page<UserCouponResp> page = userCouponBizService.findPageByCouponPageQuery(query);
        return CommonResult.success(page);
    }


    @GetMapping("/selectListExport")
    public Result selectListExport(CouponPageQuery query, HttpServletResponse response, HttpServletRequest request) {
        query.setPageSize(100000);
        query.setPageNum(1);
        TimeZone timeZone = TimeZone.getTimeZone("GMT-8:00");
        if (query.getGmtStartTime() != null || query.getGmtEndTime() != null) {
            ZonedDateTime start = DateUtil.getDate2ByTimeZone(query.getGmtStartTime(), timeZone);
            ZonedDateTime end = DateUtil.getDate2ByTimeZone(query.getGmtEndTime(), timeZone);
            query.setGmtStartTime(start);
            query.setGmtEndTime(end);
        }
        Page<UserCouponResp> page = userCouponBizService.findPageByCouponPageQuery(query);
        if (page.getTotal() > 3000) {
            return CommonResult.fail("目前导出的优惠券数量已超过3000个,请筛选后重新导出表格");
        }
        ExcelUtil<UserCouponResp> util = new ExcelUtil<UserCouponResp>(UserCouponResp.class);
        util.exportExcel(page.getRecords(), "优惠券导出-明细", response, request);
        return CommonResult.success();
    }


    @PostMapping("/used")
    public Result used(@RequestBody CouponPageQuery query) {
        UserCoupon userCoupon = userCouponService.selectUserCouponById(query.getId());
        if (userCoupon != null && !Objects.equals(userCoupon.getStatus(), 2)) {
            userCoupon.setRemarks(query.getRemarks());
            userCoupon.setStatus(2);
            userCoupon.setGmtModified(ZonedDateTime.now());
            userCoupon.setGmtUse(ZonedDateTime.now());
            userCouponService.update(userCoupon);
        }
        return CommonResult.success();
    }

    @GetMapping("/updateCouponStatusTask")
    public Result updateCouponStatusTask() {
        updateCouponStatusTask.run();
        return CommonResult.success();
    }


}
