package com.linzi.pitpat.admin.model.Dto.request.vip;


import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContent;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 17:03
 */
@Data
@NoArgsConstructor
public class VipRightDetailRequestDto {
    /**
     * id
     */
    private Long id;
    /**
     * 内测/公开 0-内测 1-公开
     */
    private Integer betaVersion;
    /**
     * 当betaVersion为0才生效
     * 需要公开：0-无需公开；1-需要公开
     */
    private Integer open;
    /**
     * 国际化数据
     */
    private List<VipRightI18nContent> data;

}
