package com.linzi.pitpat.admin.outservice.manager;

import com.linzi.pitpat.data.equipmentservice.service.TreadmillUpgradeLogService;
import com.linzi.pitpat.data.quartz.SyncDbDataTask;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUpgradeRequestDto;
import com.linzi.pitpat.data.userservice.dto.console.request.DelUserDevRequestDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeviceOutManager {

    private final SyncDbDataTask syncDbDataTask;
    private final TreadmillUpgradeLogService treadmillUpgradeLogService;

    /**
     * 同步device数据
     */
    public void syncData() {
        syncDbDataTask.exec();
    }

    /**
     * 删除设备的升级记录
     */
    public void delUpgradeRecord(DelUpgradeRequestDto req) {
        treadmillUpgradeLogService.delByEquipmentNo(req.getEquipmentNo());
    }
}
