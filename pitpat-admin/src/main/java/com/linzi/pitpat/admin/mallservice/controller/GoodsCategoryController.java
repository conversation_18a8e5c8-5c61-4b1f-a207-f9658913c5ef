package com.linzi.pitpat.admin.mallservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.mallservice.dto.request.CategoryPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.request.CategoryReqDto;
import com.linzi.pitpat.data.mallservice.dto.response.CategoryRespDto;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsCategoryEntity;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsCategoryService;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.lang.Result;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @description: 类目控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/category")
public class GoodsCategoryController extends BaseController {
    @Resource
    private ZnsGoodsCategoryService goodsCategoryService;

    /**
     * 商品分类列表
     *
     * @param pageQueryDto
     * @return
     */
    @PostMapping("/page/list")
    public Result<Page> pageList(@RequestBody CategoryPageQueryDto pageQueryDto) {
        Page<CategoryRespDto> voList = goodsCategoryService.selectCategoryList(pageQueryDto);
        return CommonResult.success(voList);
    }

    @RequestMapping("/list")
    public Result<List<ZnsGoodsCategoryEntity>> oneLevelList() {
        List<ZnsGoodsCategoryEntity> list = goodsCategoryService.selectOneLevel();
        return CommonResult.success(list);
    }

    @RequestMapping("/dropList")
    public Result dropList() {
        List<ZnsGoodsCategoryEntity> list = goodsCategoryService.listByStatus(1);
        return CommonResult.success(list);
    }

    /**
     * 增加分类
     *
     * @param request
     * @return
     */
    @Log(title = "类目管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public Result add(@RequestBody CategoryReqDto request) {
        Result ajaxResult = goodsCategoryService.checkCategoryNameAllowed(request);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        ZnsGoodsCategoryEntity znsGoodsCategoryEntity = new ZnsGoodsCategoryEntity();
        if (request.getClassifyType() == 2) {
            znsGoodsCategoryEntity.setParentId(request.getParentId());
        } else {
            znsGoodsCategoryEntity.setParentId(0L);
        }
        znsGoodsCategoryEntity.setCategoryName(request.getCategoryName());
        znsGoodsCategoryEntity.setCreator(SecurityUtils.getUsername());
        znsGoodsCategoryEntity.setModified(SecurityUtils.getUsername());
        goodsCategoryService.insert(znsGoodsCategoryEntity);
        return CommonResult.success();
    }

    /**
     * 编辑分类
     *
     * @param request
     * @return
     */
    @Log(title = "类目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody CategoryReqDto request) {
        Result ajaxResult = goodsCategoryService.checkCategoryNameAllowed(request);
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }
        ZnsGoodsCategoryEntity znsGoodsCategoryEntity = goodsCategoryService.findById(request.getId());
        if (request.getClassifyType() == 2) {
            znsGoodsCategoryEntity.setParentId(request.getParentId());
        }
        znsGoodsCategoryEntity.setCategoryName(request.getCategoryName());
        znsGoodsCategoryEntity.setCreator(SecurityUtils.getUsername());
        znsGoodsCategoryEntity.setModified(SecurityUtils.getUsername());
        znsGoodsCategoryEntity.setModifyTime(ZonedDateTime.now());
        goodsCategoryService.update(znsGoodsCategoryEntity);
        return CommonResult.success();
    }

    /**
     * 分类详情
     *
     * @param id
     * @return
     */
    @Log(title = "类目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable Long id) {
        Result ajaxResult = goodsCategoryService.checkCategoryStatusAllowed(id, "删除");
        if (Objects.nonNull(ajaxResult)) {
            return ajaxResult;
        }
        goodsCategoryService.delete(id, SecurityUtils.getUsername());
        return CommonResult.success();
    }

    /**
     * 状态修改
     */
//    @PreAuthorize("@ss.hasPermi('system:category:changeStatus')")
    @Log(title = "类目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public Result changeStatus(@RequestBody CategoryReqDto request) {
        if (request.getStatus() == 0) {
            Result ajaxResult = goodsCategoryService.checkCategoryStatusAllowed(request.getId(), "禁用");
            if (Objects.nonNull(ajaxResult)) {
                return ajaxResult;
            }
        }
        if (Objects.isNull(request.getId())) {
            return CommonResult.fail("id不能为空");
        }
        ZnsGoodsCategoryEntity znsGoodsCategoryEntity = goodsCategoryService.findById(request.getId());
        znsGoodsCategoryEntity.setStatus(request.getStatus());
        znsGoodsCategoryEntity.setModifyTime(ZonedDateTime.now());
        znsGoodsCategoryEntity.setModified(SecurityUtils.getUsername());
        goodsCategoryService.update(znsGoodsCategoryEntity);
        return CommonResult.success();
    }
}
