package com.linzi.pitpat.admin.mallservice.convert;

import com.linzi.pitpat.admin.mallservice.dto.request.MallCategoryPageRequestDto;
import com.linzi.pitpat.admin.mallservice.dto.response.MallCategoryPageResponseDto;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MallCategoryPageConverter {

    @Mapping(target = "countryCode", source = "countryCodeList", qualifiedByName = "listToString")
    MallCategoryPage toEntry(MallCategoryPageRequestDto mallCategoryPageRequestDto);

    @Mapping(target = "countryCodeList", source = "countryCode", qualifiedByName = "stringToList")
    MallCategoryPageResponseDto toResponseDto(MallCategoryPage mallCategory);

    // 自定义转换方法
    @Named("listToString")
    default String listToString(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return JsonUtil.writeString(list);
    }

    @Named("stringToList")
    default List<String> stringToList(String str) {
        if (!StringUtils.hasText(str)) {
            return null;
        }
        return JsonUtil.readList(str, String.class);
    }
}
