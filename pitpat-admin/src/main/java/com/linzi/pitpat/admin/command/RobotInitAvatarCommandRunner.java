package com.linzi.pitpat.admin.command;

import com.google.common.io.ByteStreams;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.constants.CacheConstants;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
/**
 * 2.7 线上执行后，可以删掉本 commandRunner
 */
public class RobotInitAvatarCommandRunner {
    @Autowired
    private RedissonClient redissonClient;
    @javax.annotation.Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ZnsUserService znsUserService;
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static DecimalFormat df = new DecimalFormat("0");
    private static NumberFormat numberFormat = NumberFormat.getInstance();
    private final AtomicInteger atomicInteger = new AtomicInteger(0);

    //TODO prod 环境下 robot/list.xlsx
    private String listFilePath = "robot/list_dev.xlsx";
    private String prodListFilePath = "robot/list.xlsx";
    @javax.annotation.Resource
    private RedisTemplate<String, HashMap<String, String>> redisTemplate;


    public void run() {
        try {
            CompletableFuture<List<String>> femaleFutures
                    = CompletableFuture.supplyAsync(() -> uploadAvatar("robot/female/*.jpg"), taskExecutor);
            CompletableFuture<List<String>> maleFutures
                    = CompletableFuture.supplyAsync(() -> uploadAvatar("robot/male/*.jpg"), taskExecutor);

            List<String> femaleAvatars = femaleFutures.get();
            List<String> maleAvatars = maleFutures.get();
            List<String> avatars = new ArrayList<>();

            avatars.addAll(femaleAvatars);
            avatars.addAll(maleAvatars);

            if (avatars.size() < 306) {
                log.warn("数据头像有误： 应上传 306， 实际上传：{}", femaleAvatars.size());
            }
            batchUpdateRobots(avatars);
        } catch (Exception e) {
            log.error("初始化机器人报错：{}", e.getMessage(), e);
        }
    }

    private void batchUpdateRobots(List<String> avatars) throws IOException {
        List<ZnsUserEntity> znsUsers = parseUserFromXls();
        List<Long> userIds = znsUsers.stream().map(ZnsUserEntity::getId).collect(Collectors.toList());
        log.info("{}- {}", userIds.size(), userIds);

        IntStream.range(0, avatars.size()).forEach(index -> {
            ZnsUserEntity user = znsUsers.get(index);
            if (Objects.equals(user.getIsRobot(), 0) || Objects.nonNull(user.getHeadPortrait())) {
                log.info("数据有误，用户不是机器人：{}", user);
            } else {
                znsUsers.get(index).setHeadPortrait(avatars.get(index));
                log.info("处理机器人: {} 的头像: {}", user, avatars.get(index));
            }
        });
        znsUsers.forEach(item -> {
            log.info("{} - {}", item.getId(), item.getHeadPortrait());
        });
        //批量更新
        znsUserService.updateBatchById(znsUsers);
    }

    public static void main(String[] args) throws IOException {
//        List<ZnsUserEntity> znsUserEntities = parseUserFromXls();
//        List<Long> userIds = znsUserEntities.stream().map(ZnsUserEntity::getId).collect(Collectors.toList());
//        log.info("{}- {}", userIds.size(), userIds);
//        uploadPic("file:D:/fe/7dba4d25-0011-43b9-9b2c-7e6104fcc736.jpg");
    }

    public void uploadPic(String locationPattern) {
        String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());

        Resource[] resources = new Resource[0];
        try {
            resources = new PathMatchingResourcePatternResolver().getResources(locationPattern);
            System.out.println(resources.length);
        } catch (IOException e) {
            e.printStackTrace();
        }

        //可以先将头像放在List中
        List<String> avatars = new ArrayList<>();
        Map<String, String> robotList = new HashMap<>();

        AtomicInteger atomicInteger = new AtomicInteger(0);
        for (Resource resource : resources) {
            try {
                InputStream inputStream = resource.getInputStream();
                byte[] byteArray = ByteStreams.toByteArray(inputStream);
                //log.info("new file: {}", resource.getFilename());

                Map<String, Object> map = AwsUtil.putS3Object(byteArray, resource.getFilename(), yyyyMM);
                String avatar = String.valueOf(map.get("url"));
                avatars.add(avatar);
                log.info("seq: {}, {}, new file upload: {}- {}", atomicInteger.incrementAndGet(), locationPattern, avatar, resource.getFilename());

            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        System.out.println(avatars);
    }

    private List<ZnsUserEntity> parseUserFromXls() throws IOException {
        String filePath = "";
        if ("dev".equals(SpringContextUtils.getActiveProfile())) {
            filePath = listFilePath;
        } else {
            filePath = prodListFilePath;
        }
        ClassPathResource classPathResource = new ClassPathResource(listFilePath);
        List<List<String>> lists = readExcel(classPathResource);
        log.info("{}", lists.size());
        //female 147 -> 145 -> 145
        //male   159 -> 161 -> 159  //少两个
        //total  306 -> 306
        return lists.stream().map(item -> {
            ZnsUserEntity user = new ZnsUserEntity();
            int seq = BigDecimalUtil.valueOf(item.get(0)).intValue();
            String firstName = item.get(1);
            String gender = item.get(2);   //gender 1：男，2：女，0：未知
            String email = item.get(3);
            Long id = BigDecimalUtil.valueOf(item.get(4)).longValue();

            user.setId(id);
            user.setFirstName(firstName);
//            user.setEmailAddress(email);
            user.setEmailAddressEn(email);
            user.setGender(Objects.equals(gender, "男") ? 1 : 2);
            log.info("seq: {}, fistName : {}, gender: {}, email: {}, id: {}", seq, firstName, gender, email, id);
            return user;
        }).collect(Collectors.toList());

    }


    private List<String> uploadAvatar(String locationPattern) {
        String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());
        String key = CacheConstants.MEDAL_KEY_ + yyyyMM + "_" + locationPattern;
        log.info("开始处理 locationPattern: {}", locationPattern);
        RLock lock = redissonClient.getLock(key);
        try {
            if (LockHolder.tryLock(lock, 5, 60)) {
                //防止多个api 实例重复执行
                TimeUnit.SECONDS.sleep(5L);
                Resource[] resources = new PathMatchingResourcePatternResolver().getResources(locationPattern);

                //可以先将头像放在List中
                List<String> avatars = new ArrayList<>();

                BoundHashOperations<String, String, String> robotList = redisTemplate.boundHashOps("_202308_robot_list");
                redisTemplate.expire("_202308_robot_list", 6, TimeUnit.HOURS);

                for (Resource resource : resources) {
                    InputStream inputStream = resource.getInputStream();
                    byte[] byteArray = ByteStreams.toByteArray(inputStream);
                    if (StringUtil.isEmpty(robotList.get(resource.getFilename()))) {
                        Map<String, Object> map = AwsUtil.putS3Object(byteArray, resource.getFilename(), yyyyMM);
                        String avatar = String.valueOf(map.get("url"));
                        avatars.add(avatar);
                        robotList.put(resource.getFilename(), avatar);
                        log.info("seq: {}, {}, new file upload: {}- {}", atomicInteger.incrementAndGet(), locationPattern, avatar, resource.getFilename());
                    } else {
                        log.info("seq: {},{} exist in cached, so get from cached file: {}- {}", locationPattern, atomicInteger.incrementAndGet(), robotList.get(resource.getFilename()), resource.getFilename());
                        avatars.add(robotList.get(resource.getFilename()));
                    }
                }
                return avatars;
            } else {
                log.warn("没有获得锁");
            }
        } catch (Exception e) {
            log.info("上传机器人头像报错: {}", e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("删除机器人自动上传头像的分布式锁");
                lock.unlock();
            } else {
                log.warn("未获取到锁，无需释放");
            }
        }
        return null;
    }


    /**
     * 描述：读入excel文件，解析后返回
     *
     * @param file
     * @return
     * @throws IOException
     * <AUTHOR>
     * 2018年5月23日下午2:34:23
     */
    public static List<List<String>> readExcel(Resource file) throws IOException {
        //获得Workbook工作薄对象
        Workbook workbook = getWorkBook(file.getInputStream(), file.getFilename());
        //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
        List<List<String>> list = new ArrayList<>();
        if (workbook != null) {
            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
                //获得当前sheet工作表
                Sheet sheet = workbook.getSheetAt(sheetNum);
                if (sheet == null) {
                    continue;
                }
                //获得当前sheet的开始行
                int firstRowNum = sheet.getFirstRowNum();
                //获得当前sheet的结束行
                int lastRowNum = sheet.getLastRowNum();
                //循环除了第一行的所有行
                for (int rowNum = firstRowNum + 1; rowNum <= lastRowNum; rowNum++) {
                    //获得当前行
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    //获得当前行的开始列
                    int firstCellNum = row.getFirstCellNum();
                    if (firstCellNum == -1) {
                        continue;
                    }
                    //获得当前行的列数
                    int lastCellNum = row.getPhysicalNumberOfCells();
//                    String[] cells = new String[row.getPhysicalNumberOfCells()];
                    List<String> rowList = new ArrayList<>();
                    //循环当前行
                    for (int cellNum = firstCellNum; cellNum < lastCellNum; cellNum++) {
                        Cell cell = row.getCell(cellNum);
//                        cells[cellNum] = getCellValue(cell);
                        rowList.add(getCellValue(cell));
                    }
                    /*判断是否读取空行*/
                    Integer flag = 0;
                    for (int i = 0; i < rowList.size(); i++) {
                        String content = rowList.get(i);
                        if (" ".equals(content)) {
                            flag++;
                        }
                    }
                    if (flag != rowList.size()) {
                        list.add(rowList);
                    }
                }
            }
            //workbook.close();
        }
        return list;
    }

    public static Workbook getWorkBook(InputStream is, String fileName) {
        /*获得文件名  */
        /*创建Workbook工作薄对象，表示整个excel*/
        Workbook workbook = null;
        try {
            /*根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象  */
            if (fileName.endsWith("xls")) {
                /*2003*/
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith("xlsx")) {
                /*2007*/
                workbook = new XSSFWorkbook(is);
            }
        } catch (IOException e) {
            log.info(e.getMessage());
        }
        return workbook;
    }

    public static String getCellValue(Cell cell) {
        String cellValue = " ";
        if (cell == null) {
            return cellValue;
        }
        /*把数字当成String来读，避免出现1读成1.0的情况 */
        /*if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC){
            cell.setCellType(Cell.CELL_TYPE_STRING);
        }  */
        /*判断数据的类型 */
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC: //数字
                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                    //  如果是date类型则 ，获取该cell的date值
                    cellValue = sdf.format(HSSFDateUtil.getJavaDate(cell.getNumericCellValue()));
                } else { // 纯数字
                    //value = String.valueOf(cell.getStringCellValue());
                    //cellValue = df.format(cell.getNumericCellValue());
                    cellValue = String.valueOf(cell.getNumericCellValue());
//            		cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;
            case Cell.CELL_TYPE_STRING: //字符串
                cellValue = String.valueOf(cell.getStringCellValue());
                break;
            case Cell.CELL_TYPE_BOOLEAN: //Boolean
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case Cell.CELL_TYPE_FORMULA: //公式
                cellValue = String.valueOf(cell.getCellFormula());
                break;
            case Cell.CELL_TYPE_BLANK: //空值
                cellValue = " ";
                break;
            case Cell.CELL_TYPE_ERROR: //故障
                cellValue = " ";
                break;
            default:
                cellValue = " ";
                break;
        }
        return cellValue;
    }
}
