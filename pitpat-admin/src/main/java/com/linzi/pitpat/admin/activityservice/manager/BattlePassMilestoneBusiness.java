package com.linzi.pitpat.admin.activityservice.manager;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.activityservice.dto.response.ActivityEntryFeeDto;
import com.linzi.pitpat.admin.activityservice.dto.response.BattlePassMilestoneAmountDto;
import com.linzi.pitpat.admin.activityservice.dto.response.BattlePassMilestoneDataResponseDto;
import com.linzi.pitpat.admin.activityservice.dto.response.BattlePassMilestoneInfoResponseDto;
import com.linzi.pitpat.admin.activityservice.dto.response.BattlePassMilestoneLExportDto;
import com.linzi.pitpat.admin.activityservice.dto.response.BattlePassMilestoneListResponseDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityEntryFeeQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityPageQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.dto.MilestoneAwardDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBattlePass;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBattlePassService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/10 11:46
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BattlePassMilestoneBusiness {
    private final ZnsRunActivityService runActivityService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsUserService userService;
    private final UserCouponService userCouponService;
    private final UserWearsBattlePassService userWearsBattlePassService;
    private final ActivityEntryFeeService activityEntryFeeService;
    private final ZnsUserAccountService znsUserAccountService;

    public Page page(final PageQuery po) {
        final RunActivityPageQuery query = RunActivityPageQuery.builder()
                .select(List.of(ZnsRunActivityEntity::getId, ZnsRunActivityEntity::getActivityTitle, ZnsRunActivityEntity::getActivityState,
                        ZnsRunActivityEntity::getActivityStartTime, ZnsRunActivityEntity::getActivityEndTime, ZnsRunActivityEntity::getPayNum))
                .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                .isDelete(0)
                .build();
        query.setOrders(List.of(OrderItem.desc("activity_start_time")));
        query.setPageSize(po.getPageSize());
        query.setPageSize(po.getPageSize());
        final Page page = runActivityService.findPage(query);

        final List<ZnsRunActivityEntity> records = page.getRecords();

        final List<BattlePassMilestoneListResponseDto> collect = records.stream().map(a -> {
            final BattlePassMilestoneListResponseDto dto = new BattlePassMilestoneListResponseDto();
            BeanUtils.copyProperties(a, dto);
            final String activityStartTime = DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD_HH_MM_SS, a.getActivityStartTime());
            final String activityEndTime = DateUtil.parseDateToStr(DateUtil.YYYY_MM_DD_HH_MM_SS, a.getActivityEndTime());
            dto.setActivityStartTime(activityStartTime);
            dto.setActivityEndTime(activityEndTime);
            dto.setActivityId(a.getId());
            dto.setPayUserCount(a.getPayNum());
            return dto;
        }).collect(Collectors.toList());
        page.setRecords(collect);
        return page;
    }

    /**
     * 活动详情
     *
     * @param activityId
     * @return
     */
    public BattlePassMilestoneInfoResponseDto info(final Long activityId) {
        final BattlePassMilestoneInfoResponseDto dto = new BattlePassMilestoneInfoResponseDto();
        final ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
        BeanUtils.copyProperties(runActivity, dto);
        dto.setActivityId(runActivity.getId());
        // 报名费用查询
        final ActivityEntryFeeQuery activityEntryFeeQuery = ActivityEntryFeeQuery.builder().activityId(runActivity.getId()).build();
        final List<ActivityEntryFee> activityEntryFees = activityEntryFeeService.findList(activityEntryFeeQuery);
        dto.setActivityEntryFeeList(BeanUtil.copyBeanList(activityEntryFees, ActivityEntryFeeDto.class));
        //奖励添加
        final List<MilestoneAwardDto> awardConfigDetailsDtoList = activityAwardConfigService.getAwardConfigDetailsDtoList(activityId);
        dto.setAwardConfigDetailsDtoList(awardConfigDetailsDtoList);
        //数据明细
        final List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityId);
        if (CollectionUtils.isEmpty(allActivityUser)) {
            return dto;
        }
        //领取奖励
        final List<ZnsUserAccountDetailEntity> amountList = userAccountDetailService.findList(new UserAccountDetailByQuery()
                .setActivityId(activityId).setTradeStatus(2));
        final List<ActivityUserScore> scoreList = activityUserScoreService.list(Wrappers.<ActivityUserScore>lambdaQuery()
                .eq(ActivityUserScore::getActivityId, activityId)
                .eq(ActivityUserScore::getIsDelete, 0)
                .in(ActivityUserScore::getSource, Arrays.asList(11, 12))
                .ne(ActivityUserScore::getStatus, 0));
        //券
        final List<UserCoupon> couponList = userCouponService.findListByActivityIdAndStatus(activityId, 5);
        //服装
//        List<UserWearsBag> wearsBagList = userWearsBagService.list(Wrappers.<UserWearsBag>lambdaQuery()
//                .eq(UserWearsBag::getActivityId, activityId)
//                .ne(UserWearsBag::getStatus, 2)
//                .eq(UserWearsBag::getIsDelete, 0));
        final List<UserWearsBattlePass> userWearsBattlePasses = userWearsBattlePassService.list(Wrappers.<UserWearsBattlePass>lambdaQuery()
                .eq(UserWearsBattlePass::getActivityId, activityId)
                .eq(UserWearsBattlePass::getIsDelete, 0)
                .ne(UserWearsBattlePass::getStatus, 0)
        );

        final List<ZnsRunActivityUserEntity> payUser = allActivityUser.stream().filter(a -> a.getIsPay() == 1).collect(Collectors.toList());

        final BattlePassMilestoneDataResponseDto ordinaryData = getBattlePassMilestoneDataResponseDto(allActivityUser, amountList, scoreList, couponList, userWearsBattlePasses, 0);
        dto.setOrdinaryData(ordinaryData);
        final BattlePassMilestoneDataResponseDto advancedData = getBattlePassMilestoneDataResponseDto(payUser, amountList, scoreList, couponList, userWearsBattlePasses, 1);
        dto.setAdvancedData(advancedData);

        return dto;
    }

    private BattlePassMilestoneDataResponseDto getBattlePassMilestoneDataResponseDto(final List<ZnsRunActivityUserEntity> activityUsers, final List<ZnsUserAccountDetailEntity> amountList,
                                                                                     final List<ActivityUserScore> scoreList, final List<UserCoupon> couponList, final List<UserWearsBattlePass> wearsBagList,
                                                                                     final int awardCondition) {
        final BattlePassMilestoneDataResponseDto dto = new BattlePassMilestoneDataResponseDto();
        if (!CollectionUtils.isEmpty(activityUsers)) {
            dto.setUserCount(activityUsers.size());
        }

        final List<Long> userIds = new ArrayList<>();
        final I18nConstant.CurrencyCodeEnum[] values = I18nConstant.CurrencyCodeEnum.values();
        final List<BattlePassMilestoneAmountDto> amountDtoList = new ArrayList<>();
        for (final I18nConstant.CurrencyCodeEnum value : values) {
            final BattlePassMilestoneAmountDto amountDto = new BattlePassMilestoneAmountDto();
            amountDto.setCurrencyCode(value.getCode());
            amountDto.setCurrencySymbol(value.getSymbol());
            amountDto.setReceiveAmount(BigDecimal.ZERO);
            amountDto.setPayShouldAmount(BigDecimal.ZERO);
            amountDto.setPayAmount(BigDecimal.ZERO);
            amountDtoList.add(amountDto);
        }

        if (!CollectionUtils.isEmpty(amountList)) {
            final List<ZnsUserAccountDetailEntity> list = amountList.stream().filter(a -> awardCondition == 0 ? a.getTradeType().equals(AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ORDINARY_AWARD.getType()) : a.getTradeType().equals(AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ADVANCED_AWARD.getType())).collect(Collectors.toList());
            fillReceiveAmount(userIds, amountDtoList, list);
        }
        //积分
        if (!CollectionUtils.isEmpty(scoreList)) {
            final List<ActivityUserScore> list = scoreList.stream().filter(a -> awardCondition == 0 ? a.getSource() == 11 : a.getSource() == 12).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                Integer sumScore = 0;
                for (final ActivityUserScore activityUserScore : list) {
                    userIds.add(activityUserScore.getUserId());
                    sumScore = sumScore + activityUserScore.getScore();
                }
                dto.setReceiveScore(sumScore);
            }
        }
        //券
        if (!CollectionUtils.isEmpty(couponList)) {
            final List<UserCoupon> list = couponList.stream().filter(a -> awardCondition == 0 ? a.getSourceType().equals(CouponConstant.SourceTypeEnum.source_type_6.getType()) : a.getSourceType().equals(CouponConstant.SourceTypeEnum.source_type_8.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                for (final UserCoupon userCoupon : list) {
                    userIds.add(userCoupon.getUserId());
                }
            }
        }
        //服装
        if (!CollectionUtils.isEmpty(wearsBagList)) {
            final List<UserWearsBattlePass> list = wearsBagList.stream().filter(a -> awardCondition == 0 ? a.getSource() == 0 : a.getSource() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                for (final UserWearsBattlePass userWearsBag : list) {
                    userIds.add(userWearsBag.getUserId());
                }
            }
        }
        //领奖人数
        final List<Long> userIdsDis = userIds.stream().distinct().collect(Collectors.toList());
        dto.setReceiveUserCount(userIdsDis.size());
        //报名费。进阶
        if (awardCondition == 1) {
            fillPayAmount(amountList, amountDtoList);
            fillPayShouldAmount(activityUsers, amountDtoList);
        }
        dto.setAmountDtoList(amountDtoList);
        return dto;
    }

    /**
     * 填充金额
     *
     * @param userIds
     * @param amountDtoList
     * @param list
     */
    private void fillReceiveAmount(final List<Long> userIds, final List<BattlePassMilestoneAmountDto> amountDtoList, final List<ZnsUserAccountDetailEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> userIds.add(e.getUserId()));
            for (final BattlePassMilestoneAmountDto amountDto : amountDtoList) {
                BigDecimal receiveAmount = amountDto.getReceiveAmount();
                for (final ZnsUserAccountDetailEntity userAccountDetailEntity : list) {
                    final ZnsUserAccountEntity userAccountEntity = znsUserAccountService.getUserAccount(userAccountDetailEntity.getUserId());
                    if (userAccountEntity.getCurrencyCode().equals(amountDto.getCurrencyCode())) {
                        receiveAmount = receiveAmount.add(userAccountDetailEntity.getAmount());
                    }
                }
                amountDto.setReceiveAmount(receiveAmount);
            }
        }
    }


    /**
     * 填充实际支付金额
     *
     * @param amountList
     * @param amountDtoList
     */
    private void fillPayAmount(final List<ZnsUserAccountDetailEntity> amountList, final List<BattlePassMilestoneAmountDto> amountDtoList) {
        final List<ZnsUserAccountDetailEntity> list = amountList.stream().filter(a -> a.getTradeType().equals(AccountDetailTypeEnum.SECURITY_FUND.getType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list)) {
            for (final BattlePassMilestoneAmountDto amountDto : amountDtoList) {
                BigDecimal payAmount = amountDto.getPayAmount();
                for (final ZnsUserAccountDetailEntity userAccountDetailEntity : list) {
                    final ZnsUserAccountEntity userAccountEntity = znsUserAccountService.getUserAccount(userAccountDetailEntity.getUserId());
                    if (userAccountEntity.getCurrencyCode().equals(amountDto.getCurrencyCode())) {
                        payAmount = payAmount.add(userAccountDetailEntity.getAmount());
                    }
                }
                amountDto.setPayAmount(payAmount);
            }
        }
    }


    /**
     * 填充应该支付金额
     *
     * @param activityUsers
     * @param amountDtoList
     */
    private void fillPayShouldAmount(final List<ZnsRunActivityUserEntity> activityUsers, final List<BattlePassMilestoneAmountDto> amountDtoList) {
        if (CollectionUtils.isEmpty(activityUsers)) {
            return;
        }
        final ActivityEntryFeeQuery activityEntryFeeQuery = ActivityEntryFeeQuery.builder().activityId(activityUsers.get(0).getActivityId()).build();
        final List<ActivityEntryFee> activityEntryFees = activityEntryFeeService.findList(activityEntryFeeQuery);
        for (final BattlePassMilestoneAmountDto amountDto : amountDtoList) {
            final int userCount = activityUsers.stream().map(x -> {
                final ZnsUserAccountEntity userAccountEntity = znsUserAccountService.getUserAccount(x.getUserId());
                if (userAccountEntity.getCurrencyCode().equals(amountDto.getCurrencyCode())) {
                    return 1;
                } else {
                    return 0;
                }
            }).reduce(0, Integer::sum);
            if (NumberUtils.geZero(userCount)) {
                final ActivityEntryFee activityEntryFee = activityEntryFees.stream()
                        .filter(e -> amountDto.getCurrencyCode().equals(e.getCurrencyCode())).findFirst().orElse(null);
                final BigDecimal payShouldAmount = activityEntryFee.getEntryFee().multiply(new BigDecimal(userCount));
                amountDto.setPayShouldAmount(payShouldAmount);
            }
        }
    }

    /**
     * 数据导出
     *
     * @param activityId
     * @return
     */
    public List<BattlePassMilestoneLExportDto> exportList(final Long activityId) {
        final List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = runActivityUserService.selectByActivityId(activityId);
        if (CollectionUtils.isEmpty(znsRunActivityUserEntities)) {
            return null;
        }
        final List<Long> userIds = znsRunActivityUserEntities.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        final List<ZnsUserEntity> userEntityList = userService.findByIds(userIds);
        final Map<Long, ZnsUserEntity> userMap = userEntityList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity(), (x, y) -> x));
        final List<ZnsUserAccountDetailEntity> list = userAccountDetailService.findList(new UserAccountDetailByQuery()
                .setActivityId(activityId)
                .setTradeType(Arrays.asList(AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ORDINARY_AWARD.getType(), AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ADVANCED_AWARD.getType()))
                .setTradeStatus(2));
        Map<Long, List<ZnsUserAccountDetailEntity>> amountMap = null;
        if (!CollectionUtils.isEmpty(list)) {
            amountMap = list.stream().collect(Collectors.groupingBy(ZnsUserAccountDetailEntity::getUserId));
        }
        final List<ActivityUserScore> scoreList = activityUserScoreService.list(Wrappers.<ActivityUserScore>lambdaQuery()
                .eq(ActivityUserScore::getActivityId, activityId)
                .eq(ActivityUserScore::getIsDelete, 0)
                .in(ActivityUserScore::getSource, Arrays.asList(11, 12))
                .ne(ActivityUserScore::getStatus, 0));
        Map<Long, List<ActivityUserScore>> scoreMap = null;
        if (!CollectionUtils.isEmpty(scoreList)) {
            scoreMap = scoreList.stream().collect(Collectors.groupingBy(ActivityUserScore::getUserId));
        }

        final Map<Long, List<ZnsUserAccountDetailEntity>> finalAmountMap = amountMap;
        final Map<Long, List<ActivityUserScore>> finalScoreMap = scoreMap;

        return znsRunActivityUserEntities.stream().map(r -> {
            final BattlePassMilestoneLExportDto dto = new BattlePassMilestoneLExportDto();
            final ZnsUserEntity user = userMap.get(r.getUserId());
            dto.setNickname(user.getFirstName());
            dto.setEmailAddress(user.getEmailAddressEn());
            if (Objects.nonNull(finalAmountMap)) {
                final List<ZnsUserAccountDetailEntity> znsUserAccountDetailEntities = finalAmountMap.get(r.getUserId());
                if (!CollectionUtils.isEmpty(znsUserAccountDetailEntities)) {
                    final BigDecimal amount = znsUserAccountDetailEntities.stream().map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    final ZnsUserAccountEntity userAccountEntity = znsUserAccountService.getUserAccount(r.getUserId());
                    dto.setCurrencyCode(userAccountEntity.getCurrencyCode());
                    dto.setReceiveAmount(amount);
                }
            }
            if (Objects.nonNull(finalScoreMap)) {
                final List<ActivityUserScore> activityUserScores = finalScoreMap.get(r.getUserId());
                if (!CollectionUtils.isEmpty(activityUserScores)) {
                    final Integer score = activityUserScores.stream().mapToInt(ActivityUserScore::getScore).sum();
                    dto.setReceiveScore(score);
                }
            }

            dto.setIsPay(r.getIsPay());
            return dto;
        }).collect(Collectors.toList());
    }
}
