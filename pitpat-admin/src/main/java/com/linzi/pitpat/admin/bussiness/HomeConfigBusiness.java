package com.linzi.pitpat.admin.bussiness;


import com.google.common.collect.Sets;
import com.linzi.pitpat.admin.model.DeviceConnectTip;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class HomeConfigBusiness {

    @Resource
    private ISysConfigService sysConfigService;


    public List<DeviceConnectTip> getConnectTip() {
        SysConfig config = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.CONNECT_DEVICE_BLUETOOTH_TIPS.getCode());
        List<DeviceConnectTip> deviceConnectTipList = JsonUtil.readList(config.getConfigValue(), DeviceConnectTip.class);
        Set<String> collect = deviceConnectTipList.stream().map(DeviceConnectTip::getCode).collect(Collectors.toSet());
        Set<String> sourceCollect = Arrays.stream(I18nConstant.LanguageCodeEnum.values()).map(I18nConstant.LanguageCodeEnum::getCode).collect(Collectors.toSet());
        Set<String> difference = Sets.difference(sourceCollect, collect);
        if (!CollectionUtils.isEmpty(difference)) {
            for (String code : difference) {
                I18nConstant.LanguageCodeEnum languageCodeEnum = I18nConstant.LanguageCodeEnum.findByCode(code);
                DeviceConnectTip deviceConnectTip = new DeviceConnectTip();
                deviceConnectTip.setName(languageCodeEnum.getName());
                deviceConnectTip.setCode(languageCodeEnum.getCode());
                deviceConnectTip.setContent("");
                deviceConnectTipList.add(deviceConnectTip);
            }
        }
        return deviceConnectTipList;
    }


    public void saveConnectTip(List<DeviceConnectTip> list, String username) {
        SysConfig config = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.CONNECT_DEVICE_BLUETOOTH_TIPS.getCode());
        config.setConfigValue(JsonUtil.writeString(list));
        config.setUpdateTime(ZonedDateTime.now());
        config.setUpdateBy(username);
        sysConfigService.update(config);
    }
}
