package com.linzi.pitpat.admin.systemservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.admin.commom.BaseController;
import com.linzi.pitpat.admin.systemservice.converter.SysRoleConsoleConverter;
import com.linzi.pitpat.admin.systemservice.converter.SysUserConsoleConverter;
import com.linzi.pitpat.admin.util.SecurityUtils;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.annotation.Log;
import com.linzi.pitpat.data.enums.BusinessType;
import com.linzi.pitpat.data.systemservice.model.entity.SysRole;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.ISysPostService;
import com.linzi.pitpat.data.systemservice.service.ISysRoleService;
import com.linzi.pitpat.data.systemservice.service.ISysUserService;
import com.linzi.pitpat.data.userservice.enums.UserConstants;
import com.linzi.pitpat.dto.SysRoleConsoleDto;
import com.linzi.pitpat.dto.SysUserConsoleDto;
import com.linzi.pitpat.dto.request.SysUserQueryRequestDto;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;
    private final SysUserConsoleConverter sysUserConsoleConverter;
    private final SysRoleConsoleConverter sysRoleConsoleConverter;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public Result<Page<SysUser>> list(SysUser user) {
        Page<SysUser> page = userService.selectPageUserList(user);
        return CommonResult.success(page);
    }

    /**
     * 获取系统用户下拉框
     */
    @GetMapping("/selectList")
    public Result selectList() {
        List<Map<String, Object>> robotUserVos = userService.selectList(new SysUser());
        return CommonResult.success(robotUserVos);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public Result getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        Map<String, Object> ajax = new HashMap<>();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (Objects.nonNull(userId)) {
            ajax.put("user", userService.findByUserId(userId));
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", roleService.selectRoleListByUserId(userId));
        }
        return CommonResult.success(ajax);
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody SysUser user) {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return CommonResult.fail("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.hasText(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return CommonResult.fail("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.hasText(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return CommonResult.fail("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setUpdateTime(ZonedDateTime.now());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        if (StringUtils.hasText(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return CommonResult.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.hasText(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return CommonResult.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setUpdateTime(ZonedDateTime.now());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public Result remove(@PathVariable Long[] userIds) {
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public Result resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setUpdateTime(ZonedDateTime.now());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 重置admin密码
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetAdminPwd")
    public Result resetAdminPwd(@RequestBody SysUser user) {
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setUpdateTime(ZonedDateTime.now());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public Result changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        SysUser handoverUser = userService.checkHandoverUser(user.getHandoverUserId(), user.getStatus());
        user.setUpdateBy(SecurityUtils.getUsername());
        user.setUpdateTime(ZonedDateTime.now());
        return toAjax(userService.updateUserStatus(user, handoverUser));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public Result authRole(@PathVariable("userId") Long userId) {
        SysUser user = userService.findByUserId(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return CommonResult.success(ajax);
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public Result insertAuthRole(Long userId, Long[] roleIds) {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    @PostMapping("/selectUserById")
    public Result<SysUserConsoleDto> selectUserById(@RequestBody SysUserQueryRequestDto request) {
        SysUser sysUser = userService.findByUserId(request.getSysUserId());
        return CommonResult.success(sysUserConsoleConverter.toDto(sysUser));
    }

    @PostMapping("/selectUserRoleById")
    public Result<SysRoleConsoleDto> selectUserRoleById(@RequestBody SysUserQueryRequestDto request) {
        SysRole sysRole = userService.selectUserRoleById(request.getSysUserId());
        return CommonResult.success(sysRoleConsoleConverter.toDto(sysRole));
    }
}
