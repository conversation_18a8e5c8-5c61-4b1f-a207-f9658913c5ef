package com.linzi.pitpat.admin.controller.test;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.linzi.pitpat.admin.model.Dto.request.AddRotPondReqDto;
import com.linzi.pitpat.admin.systemservice.manager.AdminCustomH5Manager;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityAreaBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.manager.RunActivityProcessManager;
import com.linzi.pitpat.data.activityservice.manager.api.RankActivityManager;
import com.linzi.pitpat.data.activityservice.manager.console.PolymerizationActivityManager;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityUserDao;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityConfigResp;
import com.linzi.pitpat.data.activityservice.model.resp.TaskResp;
import com.linzi.pitpat.data.activityservice.quartz.UpdateTeamGradeTask;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.OfficialTeamRunActivityStrategy;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.service.AwardConfigService;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.courseservice.mapper.UserAiBaseinfoDao;
import com.linzi.pitpat.data.courseservice.model.entity.UserAiBaseinfo;
import com.linzi.pitpat.data.dao.sys.ScheduleJobLogMapper;
import com.linzi.pitpat.data.entity.dto.RunningData;
import com.linzi.pitpat.data.entity.dto.TestOffDto;
import com.linzi.pitpat.data.entity.dto.activity.AmountCompensationDto;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentCareBiz;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentCareMessageBiz;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentQualityBiz;
import com.linzi.pitpat.data.equipmentservice.model.entity.DeviceVersionEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentCareRuleDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.DeviceVersionService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentCareRuleService;
import com.linzi.pitpat.data.equipmentservice.service.RainmakerService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuQuery;
import com.linzi.pitpat.data.mallservice.service.GoodsCurrencyService;
import com.linzi.pitpat.data.mallservice.service.LogisticsQueryLogService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.messageservice.mapper.MessageTaskMsgDao;
import com.linzi.pitpat.data.messageservice.model.entity.MessageTaskMsg;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.messageservice.service.MessageTaskService;
import com.linzi.pitpat.data.messageservice.util.FirebaseUtils;
import com.linzi.pitpat.data.robotservice.quartz.AutoRotPondTask;
import com.linzi.pitpat.data.robotservice.quartz.script.RotInternationalizationScript;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserEquipmentStatisticsBizService;
import com.linzi.pitpat.data.userservice.manager.UserAccountUpgradeBizService;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.mapper.label.LabelManageSqlTemplateDao;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.model.entity.UserPushToken;
import com.linzi.pitpat.data.userservice.model.entity.UserRunRoute;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.quartz.VipTask;
import com.linzi.pitpat.data.userservice.service.UserCodeGenerator;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserPushTokenService;
import com.linzi.pitpat.data.userservice.service.UserRunRouteService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopCurrencyService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.im.GroupResultDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import com.lz.mybatis.plugins.interceptor.utils.PSqlParseUtil;
import com.lz.mybatis.plugins.interceptor.utils.t.PTuple11;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/test")
public class TestAdminController {
    @Resource
    private ZnsTreadmillService treadmillService;
    @Resource
    private CouponCurrencyService couponCurrencyService;
    @Resource
    private ZnsUserAccountDetailService znsUserAccountDetailService;
    @Resource
    private DeviceVersionService deviceVersionService;
    @Resource
    private RainmakerService rainmakerService;
    @Resource
    private ApplicationContext springContext;

    @Resource
    private ZnsUserService userService;
    @Resource
    private AppMessageService appMessageService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    @Resource
    private VipShopCurrencyService vipShopCurrencyService;
    @Resource
    private GoodsCurrencyService goodsCurrencyService;
    @Resource
    private RedisTemplate redisTemplate;
    @Value("${admin.server.gamepush}")
    private String gameDomain;
    @Autowired
    private AwardConfigService awardConfigService;
    @Resource
    private SocketPushUtils socketPushUtils;
    @Resource
    private VipTask vipTask;
    @Resource
    private LabelManageSqlTemplateDao labelManageSqlTemplateDao;
    @Resource
    private MessageTaskMsgDao messageTaskMsgDao;
    @Resource
    private MessageTaskService messageTaskService;
    @Autowired
    private PolymerizationActivityPoleService polymerizationActivityPoleService;
    @Resource
    private RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    @Resource
    private UserAiBaseinfoDao userAiBaseinfoDao;
    @Resource
    private UserAccountUpgradeBizService userAccountUpgradeBizService;
    @Resource
    private ActivityAreaBizService activityAreaBizService;
    @Resource
    private EquipmentQualityBiz equipmentQualityBiz;
    @Resource
    private EquipmentCareBiz equipmentCareBiz;
    @Resource
    private UserEquipmentStatisticsBizService userEquipmentStatisticsBizService;
    @Resource
    private UserBizService userManager;
    @Resource
    private PolymerizationActivityManager polymerizationActivityManager;


    @Autowired
    private UpdateTeamGradeTask updateTeamGradeTask;

    @Autowired
    private AdminCustomH5Manager adminCustomH5Manager;

    @GetMapping("/testTeam")
    public void jobTest1() {
        updateTeamGradeTask.updateTeamGrade();

    }


    // http://localhost:7771/admin/test/test3
    @RequestMapping("/test3")
    public String test3() {
//        List<OrderTestVo> orderEntities = orderDao.selectOrderInfo();
//        System.out.println(JsonUtil.writeString(orderEntities));
        return "bbb";
    }


    // http://localhost:7771/admin/test/updateVersion?equipmentNo=
    @RequestMapping("/updateVersion")
    public String updateVersion(@RequestParam(value = "equipmentNo", required = false) String equipmentNo) {
        if (StringUtil.isEmpty(equipmentNo)) {
            throw new BaseException("设备编码不能为空");
        }
        ZnsTreadmillEntity treadmillEntity = treadmillService.selectTreadmillLikeByUniqueCodeOrByBluetoothMac(equipmentNo);
        if (treadmillEntity == null) {
            throw new BaseException("设备不存在");
        }

        //查询最新版本
        DeviceVersionEntity deviceVersionEntity = deviceVersionService.selectLastVersion("");
        if (deviceVersionEntity == null) {
            throw new BaseException("最新固件不存在");
        }
        Integer version = deviceVersionEntity.getVersion();
        String ota_image_id = deviceVersionEntity.getOtaImageId();
        String pre = treadmillEntity.getRId() + "_shoudong_";
        String otaJobName = NanoId.randomNanoId();
        log.info("TestAdminController#updateVersion---------equipmentNo:{},升级固件接口入参,rId：{}，ota_image_id：{},version：{},otaJobName：{}", equipmentNo, treadmillEntity.getRId(),
                ota_image_id, version, otaJobName);
        Map<String, Object> res = rainmakerService.otaJob(treadmillEntity.getRId(), ota_image_id, version, otaJobName);
        log.info("TestAdminController#updateVersion---------equipmentNo:{},升级固件接口出参：{}", equipmentNo, JsonUtil.writeString(res));
        return String.valueOf(Optional.ofNullable(res.get("status")).orElse("提交升级"));
    }


    // http://localhost:7771/admin/test/test4
    @RequestMapping("/test4")
    public String test4() {
//        List<OrderTestVo2> orderEntities = orderDao.selectOrderInfo2();
//        System.out.println(JsonUtil.writeString(orderEntities));
        return "bbb";
    }

    @Autowired
    private LogisticsQueryLogService logisticsQueryLogService;

    // http://localhost:7771/admin/test/test5
    @RequestMapping("/test5")
    public String test5() {
        logisticsQueryLogService.fillOrderLogistics("273003736412");

        return "bbb";
    }


    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${zns.config.rabbitQueue.maidian}")
    private String maidianRabbitChannel;

    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    @Autowired
    private RankActivityManager rankActivityManager;
    @Autowired
    private RotInternationalizationScript rotInternationalizationScript;
    //    @Autowired
//    private RotNickScript rotNickScript;
    @Autowired
    private AutoRotPondTask autoRotPondTask;
    @Autowired
    private RunActivityBizService runActivityBizService;
    @Autowired
    private RunActivityProcessManager runActivityProcessManager;
    @Autowired
    private UserCodeGenerator userCodeGenerator;

    // http://localhost:7771/admin/test/test6
    @RequestMapping("/test6")
    public String test6() {
        String b = "{\"data\":[{\"email\":\"<EMAIL>\",\"userId\":206, \"nickName\":\"瞿贻晓\", \"sportId\":-1,\"r\":33, \"h\":70,\"v\":6000, \"g\":0,\"s\":0, \"m\":37,\"c\":3}], \"activityId\": -1 , \"gmtCreate\": \"2022-06-06 20:23:09\"}";
        log.info(b);
        rabbitTemplate.convertAndSend(maidianRabbitChannel, b);
        return "bbb";
    }

    @RequestMapping("/userCode/batch")
    public String testBatchCreateUserCode() {
        userCodeGenerator.generator();
        return "success";
    }


    // http://localhost:7771/admin/test/test55?date=2022-06-30 10:20:00&activityId=4060
    @RequestMapping("/test55")
    public String test55(String date, Long activityId) {
        //RotBotPushRunDataTask task = SpringContextUtils.getBean(RotBotPushRunDataTask.class);
        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
        //ZonedDateTime currentDate = DateUtil.formateDate(date,DateUtil.YYYY_MM_DD_HH_MM);
        //task.handlerActivity(znsRunActivityEntity, currentDate);
        return "bbb";
    }

    // http://localhost:7771/admin/test/testjob?bean=everyDayOneTask&method=run  dev

    // http://**********:7771/admin/test/testjob?bean=weekHonourTask&method=run test


    // http://**************:7771/admin/test/testjob?bean=weekTaskConfigTask&method=run
    @RequestMapping("/testjob")
    public String testJob(String bean, String method, Long id) throws Exception {
        Object task = SpringContextUtils.getBean(bean);
        Method[] methods = task.getClass().getDeclaredMethods();
        for (Method method1 : methods) {
            log.info("方法名称 ：" + method1.getName());
            if (method1.getName().equals(method)) {
                log.info("当前执行方法名为 " + method);
                method1.invoke(task, new Object[]{});
                break;
            }
        }
        return "sucess";
    }

    @Autowired
    private ActivityTaskConfigService activityTaskConfigService;
    // http://localhost:7771/admin/test/weekTaskConfigTask?activityTaskConfigId=136

    // http://************:7771/admin/test/autoCreateActivity?activityTaskConfigId=136

    // http://**************:7771/admin/test/autoCreateActivity?activityTaskConfigId=xx
    @RequestMapping("/autoCreateActivity")
    public String autoCreateActivity(Long activityTaskConfigId) {
        ZonedDateTime currentDate = DateUtil.formateDate(ZonedDateTime.now(), DateUtil.YYYY_MM_DD_HH_MM);
        ActivityTaskConfig activityTaskConfig = activityTaskConfigService.selectActivityTaskConfigById(activityTaskConfigId);
        runActivityProcessManager.autoCreateActivity(activityTaskConfig, currentDate);
        return "sucess";
    }

    @Value("${zns.config.rabbitQueue.delay_exchange_name}")
    private String delay_exchange_name;

    //http://localhost:7771/admin/test/test7
    @RequestMapping("/test7")
    public String test7() {
        // 通过广播模式发布延时消息 延时30分钟 持久化消息 消费后销毁 这里无需指定路由，会广播至每个绑定此交换机的队列
        rabbitTemplate.convertAndSend(delay_exchange_name, "", "xxxxxxxxxxx", message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay(10 * 1000);   // 毫秒为单位，指定此消息的延时时长
            return message;
        });
        return "bbb";
    }


    //http://localhost:7771/admin/test/test8
    @PostMapping("/test8")
    public String test8(@RequestBody TestOffDto dto) {
        System.out.println(dto);
        return "bbb";
    }


    @Autowired
    private ScheduleJobLogMapper scheduleJobLogMapper;

    @RequestMapping("/sendImAndPush")
    public Result sendImAndPush(String content, String activityUserIds, String h5Url, String title, String activityContent, Long activityId, String imageUrl,
                                String initiatorUserId) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey("daily.push.config");
        String config = sysConfig.getConfigValue();

        Map<String, Object> jsonObject = JsonUtil.readValue(config);

        ImMessageBo imMessageBo = new ImMessageBo();
        if (Objects.nonNull(activityId)) {
            ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
            activityContent = activityContent.replace("{activityTitle}", runActivity.getActivityTitle());
            imMessageBo = appMessageService.assembleImActivityMessage(runActivity, activityContent);
        } else {
            imMessageBo.setJumpType("0");
            imMessageBo.setJumpValue(h5Url);
            imMessageBo.setImageUrl(imageUrl);
            imMessageBo.setMsg(activityContent);
        }
        MessageBo messageBo = new MessageBo();
        messageBo.setTitle(title);
        messageBo.setContent(content);
        messageBo.setJumpType("4");
        messageBo.setCollapseKey("Activity notification");

        Map<String, Object> extras = new HashMap<>();
        extras.put("value", "");
        messageBo.setData(extras);

        List<Long> userIds = NumberUtils.stringToLong(StringUtil.split(activityUserIds, ","));
        //文案推送
//        appMessageService.sendImAndPushUserIds(initiatorUserId, userIds, messageBo, content);

        //活动推送
//        appMessageService.sendIm(initiatorUserId, userIds, JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0);

        return CommonResult.success();
    }

    @RequestMapping("/sendImAndPushText")
    public Result sendImAndPushText(String content, String activityUserIds, String initiatorUserId, String jumpType, String activityType, String jumpValue) {
        MessageBo message = new MessageBo();
        message.setJumpType(jumpType);
        message.setContent(content);
        message.setTitle("PitPat");
        message.setCollapseKey("PitPat");

        Map<String, Object> extras = new HashMap<>();
        extras.put("value", jumpValue);
        message.setData(extras);

        appMessageService.sendImAndPushUserIds(initiatorUserId, NumberUtils.stringToLong(activityUserIds.split(",")), message, content);
        return CommonResult.success();
    }

    @RequestMapping("/sendImAndPushTextAndImage")
    public Result sendImAndPushTextAndImage(String content, String activityUserIds, String initiatorUserId, String jumpType, String imageUrl, String jumpValue) {
        MessageBo message = new MessageBo();
        message.setJumpType(jumpType);
        message.setContent(content);
        message.setTitle("PitPat");
        message.setCollapseKey("PitPat");

        Map<String, Object> extras = new HashMap<>();
        extras.put("value", jumpValue);
        message.setData(extras);

//        appMessageService.sendImAndPushUserIds(initiatorUserId, NumberUtils.stringToLong(activityUserIds.split(",")), message, content);
//        if (StringUtils.hasText(imageUrl)) {
//            appMessageService.sendIm(initiatorUserId, NumberUtils.stringToLong(activityUserIds.split(",")), imageUrl, TencentImConstant.TIM_IMAGE_ELEM, "", 0);
//        }
        return CommonResult.success();
    }

    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ZnsUserAccountService userAccountService;


    @RequestMapping("/milepostRepair")
    public Result milepostRepair(Long activityId) {
        ZnsRunActivityEntity activity = runActivityService.findById(activityId);
        List<ZnsRunActivityUserEntity> list = runActivityUserService.findList(RunActivityUserQuery.builder()
                .activityId(activityId).isDelete(0)
                .build());

        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.success();
        }
        Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
        List<MilepostAwardDto> milepostAward = JsonUtil.readList(jsonObject.get("milepostAward"), MilepostAwardDto.class);

        List<Long> userIds = list.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        List<ZnsUserEntity> znsUserEntityList = userService.findByIds(userIds);
        Map<Long, ZnsUserEntity> userEntityMap = znsUserEntityList.stream().filter(u -> u.getIsRobot() == 0 && u.getIsTest() == 0 && u.getIsDelete() == 0)
                .collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        for (ZnsRunActivityUserEntity activityUserEntity : list) {
            ZnsUserEntity znsUserEntity = userEntityMap.get(activityUserEntity.getUserId());
            if (Objects.isNull(znsUserEntity)) {
                log.info("milepostRepair 结束，为机器人用户");
                continue;
            }
            BigDecimal runMileage = userRunDataDetailsService.getMilepost(activityUserEntity.getUserId(), activity.getActivityStartTime());
            if (Objects.isNull(runMileage) || runMileage.compareTo(activityUserEntity.getRunMileage()) <= 0) {
                log.info("milepostRepair 结束，milepost 小于等于已计算里程");
                continue;
            }
            activityUserEntity.setRunMileage(runMileage);
            for (int i = 0; i < milepostAward.size(); i++) {
                MilepostAwardDto milepostAwardDto = milepostAward.get(i);
                boolean isComplete = false;
                if (activity.getCompleteRuleType() == 1) {
                    if (runMileage.compareTo(milepostAwardDto.getMilepost()) >= 0) {
                        isComplete = true;
                    }
                }
                log.info("累计跑阶段目标：{}，完成规则类型：{}，是否完成：{}", milepostAwardDto.getMilepost(), activity.getCompleteRuleType(), isComplete);
                if (isComplete) {
                    ZnsUserAccountDetailEntity accountDetail = userAccountDetailService.getAccountDetail(activityUserEntity.getUserId(), activityUserEntity.getActivityId(),
                            AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD.getType(), i + 1);
                    if (Objects.nonNull(accountDetail)) {
                        log.info("累计跑奖励处理结束，奖励重复");
                        continue;
                    }
                    Currency userCurrency = userAccountService.getUserCurrency(activityUserEntity.getUserId());
                    BigDecimal award = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), milepostAwardDto.getAward());
                    // 给用户余额发送奖励
                    userAccountService.increaseAmount(award, activityUserEntity.getUserId(), true);
                    // 更新活动用户奖励金额
                    BigDecimal cumulativeAward = I18nConstant.currencyFormat(userCurrency.getCurrencyCode(), milepostAwardDto.getCumulativeAward());
                    activityUserEntity.setRunAward(cumulativeAward);
                    // 新增用户奖励余额明细
                    String billNo = NanoId.randomNanoId();
                    ;
                    ZonedDateTime tradeTime = ZonedDateTime.now();
                    userAccountDetailService.addRunActivityAccountDetail0131(activityUserEntity.getUserId(),
                            AccountDetailTypeEnum.OFFICIAL_CUMULATIVE_AWARD, i + 1, 1,
                            award, billNo, tradeTime, activityUserEntity.getActivityId(),
                            activityId, null, activityUserEntity.getActivityType(), activityId, "", null, null, runMileage, BigDecimal.ZERO);
                }
            }
            runActivityUserService.updateById(activityUserEntity);
        }
        return CommonResult.success();
    }


    // http://localhost:7771/admin/test/test40
    @RequestMapping("/test40")
    public Result test40() {
        ZnsUserEntity userEntity = userService.selectUserTest();
        System.out.println(userEntity);
        return CommonResult.success();
    }


    // http://localhost:7771/admin/test/test41
    @RequestMapping("/test41")
    public Result test41() {
        ZnsUserEntity userEntity = userService.selectUserTestUserName("znssan");
        System.out.println(userEntity);
        return CommonResult.success();
    }


    // http://localhost:7771/admin/test/test42
    @RequestMapping("/test42")
    public Result test42() {
        ZnsUserEntity param = new ZnsUserEntity();
        param.setId(2l);
        ZnsUserEntity userEntity = userService.selectUserTestUserNameUserInfo("quyixiao", param);
        System.out.println(userEntity);
        return CommonResult.success();
    }


    //http://localhost:7771/admin/test/test43
    @RequestMapping("/test43")
    public Result test43() {
        long count = znsUserRunDataDetailsService.getRouteCount(201l);
        System.out.println(count);
        return CommonResult.success();
    }

    @Autowired
    private ZnsUserDao znsUserDao;


    // http://localhost:7771/admin/test/test45
    @RequestMapping("/test45")
    public Result test45() {
        ZnsUserEntity user = new ZnsUserEntity();
        user.setId(72515l);

        ZnsUserEntity z = znsUserDao.selectByFirstName(user, "q");

        return CommonResult.success();
    }

    @RequestMapping("/addRoom")
    public Result addRoom(String activityIds) {
        if (StringUtil.isEmpty(activityIds)) {
            return CommonResult.success();
        }
        List<Long> ids = NumberUtils.stringToLong(activityIds.split(","));
        List<ZnsRunActivityEntity> znsRunActivityEntities = runActivityService.findByIds(ids);
        for (ZnsRunActivityEntity activity : znsRunActivityEntities) {
            if (activity.getActivityType() == 4) {
                int betweenSecond = DateUtil.betweenSecond(ZonedDateTime.now(), activity.getActivityEndTime());
                Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
                List<Map> list = JsonUtil.readList(jsonObject.get(ApiConstants.RUNNING_GOALS_AWARD), Map.class);
                for (Map<String, Object> map : list) {
                    Integer runningGoal = MapUtils.getInteger(map, "goal");
                    Long activityGoalId = NumberUtils.getGoalImNumber(activity.getId(), runningGoal, activity.getCompleteRuleType());
                    redisTemplate.opsForValue().set(RedisConstants.ACTIVITY_ID + activityGoalId.toString(), "1", betweenSecond, TimeUnit.SECONDS);
                    //推送到游戏服务器端
                    try {
                        GamePushUtils.addRoom(gameDomain, Long.valueOf(activityGoalId), 2, null);
                    } catch (Exception e) {
                        log.info("请求游戏服务器异常:{}", e.getMessage());
                    }
                }
            } else if (activity.getActivityType() == 3) {
                //推送到游戏服务器端
                try {
                    GamePushUtils.addRoom(gameDomain, activity.getId(), 2, null);
                } catch (Exception e) {
                    log.info("请求游戏服务器异常:{}", e.getMessage());
                }
            }
        }
        return CommonResult.success();
    }


    // https://tkjh5.yijiesudai.com/admin/test/test49?activityNo=HD1596377
    // https://admin.pitpatfitness.com/admin/test/test49?activityNo=15963
    @RequestMapping("/test49")
    public Result test49(String activityNo) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.foo_activity_config.getCode());
        Map<String, Object> map = JsonUtil.readValue(sysConfig.getConfigValue());
        if (activityNo.contains("HD")) {
            activityNo = activityNo.replaceAll("HD", "");
        }
        map.put("skipActivityId", MapUtil.getLong(activityNo, 0l));
        sysConfig.setConfigValue(JsonUtil.writeString(map));
        sysConfigService.update(sysConfig);
        return CommonResult.success();
    }

    @Resource
    private ZnsRunActivityUserDao runActivityUserDao;

    /**
     * 奖励补发
     *
     * @return
     */
    @RequestMapping("/test50")
    public Result test50(Integer pageSize, Integer pageNum) {
        //查询数据
        List<AmountCompensationDto> list = runActivityUserDao.amountCompensation(new Page<>(pageNum, pageSize));
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.success();
        }

        for (AmountCompensationDto dto : list) {
            ZnsRunActivityEntity runActivity = runActivityService.findById(dto.getActivityId());
            ZnsRunActivityUserEntity runActivityUser = runActivityUserDao.selectByActivityIdUserId(dto.getActivityId(), dto.getUserId());
            OfficialTeamRunActivityStrategy activityStrategy = springContext.getBean(OfficialTeamRunActivityStrategy.class);
            activityStrategy.completeAward(runActivityUser, null, runActivity);
        }
        return CommonResult.success();
    }

    @RequestMapping("/updateActivityCloth")
    public Result updateActivityCloth(Integer pageSize, Integer pageNum) {
        List<Integer> list1 = Lists.newArrayList(18, 11, 33, 36, 37, 38);

        List<Integer> list = Lists.newArrayList(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(), RunActivityTypeEnum.TASK_ACTIVITY.getType());

        RunActivityPageQuery.RunActivityPageQueryBuilder runActivityPageQueryBuilder = RunActivityPageQuery.builder()
                .select(List.of(ZnsRunActivityEntity::getId, ZnsRunActivityEntity::getActivityConfig, ZnsRunActivityEntity::getActivityType))
                .activityStateIn(Lists.newArrayList(0, 1))
                .statusIn(Lists.newArrayList(0, 1))
                .activityTypeIn(list);

        RunActivityPageQuery query = runActivityPageQueryBuilder.build();
        query.setPageSize(pageSize);
        query.setPageNum(pageNum);
        Page<ZnsRunActivityEntity> page1 = runActivityService.findPage(query);
        List<ZnsRunActivityEntity> records = page1.getRecords();

        Set<ZnsRunActivityEntity> updateRecords = new HashSet<>();

        int i = 0;
        if (!CollectionUtils.isEmpty(records)) {
            for (ZnsRunActivityEntity record : records) {
                try {
                    Map<String, Object> jsonObject = JsonUtil.readValue(record.getActivityConfig());
                    if (RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType().equals(record.getActivityType())) {
                        if (StringUtils.hasText(MapUtil.getString(jsonObject.get("milepostWearsAward")))) {
                            List<MilepostWearAwardDto> milepostWearsAward = JsonUtil.readList(jsonObject.get("milepostWearsAward"), MilepostWearAwardDto.class);
                            if (!CollectionUtils.isEmpty(milepostWearsAward)) {
                                for (MilepostWearAwardDto milepostWearAwardDto : milepostWearsAward) {
                                    if (list1.contains(milepostWearAwardDto.getWearValue()) && milepostWearAwardDto.getWearType() == 5) {
                                        milepostWearAwardDto.setWearType(8);
                                        i++;
                                        updateRecords.add(record);
                                    }
                                }
                                jsonObject.put("milepostWearsAward", milepostWearsAward);
                                record.setActivityConfig(JsonUtil.writeString(jsonObject));
                            }
                        }
                    }

                    if (RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(record.getActivityType())) {
                        ActivityConfigResp activityConfigResp = JsonUtil.readValue(record.getActivityConfig(), ActivityConfigResp.class);
                        List<TaskResp> tasks = activityConfigResp.getTasks();
                        if (!CollectionUtils.isEmpty(tasks)) {
                            for (TaskResp task : tasks) {
                                WearAwardDto wearAwardDto = task.getWearAwardDto();
                                if (Objects.nonNull(wearAwardDto)) {
                                    if (list1.contains(wearAwardDto.getWearValue()) && wearAwardDto.getWearType() == 5) {
                                        wearAwardDto.setWearType(8);
                                        i++;
                                        updateRecords.add(record);
                                    }
                                }
                            }
                            record.setActivityConfig(JsonUtil.writeString(activityConfigResp));
                        }
                    }
                } catch (Exception e) {
                    System.out.println("----" + record.getId());
                    throw new RuntimeException(e);
                }
            }
            StringBuilder sb = new StringBuilder();
            for (ZnsRunActivityEntity updateRecord : updateRecords) {
                sb.append(updateRecord.getId() + "-");
            }
            log.info("要修改的活动id,{}", sb.toString());
            runActivityService.updateBatchById(updateRecords);
        }
        return CommonResult.success(i);
    }


    /**
     * http://localhost:7771/admin/test/initCouponCurrency?currencyCode=
     * 初始化优惠券指定币种价格
     *
     * @param currencyCode 币种类型
     * @param couponId     指定优惠券id，若传只会初始化改券
     * @return
     */
    @RequestMapping("/initCouponCurrency")
    public Result<Boolean> initCouponCurrency(@RequestParam(value = "currencyCode") String currencyCode, @RequestParam(value = "couponId", required = false) Long couponId) {
        couponCurrencyService.initCouponCurrency(currencyCode, couponId);
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/initUserCouponCurrency?userId=
     * 初始化用户优惠券价格
     *
     * @param userId 指定用户id，若传只会初始化用户的券
     * @return
     */
    @RequestMapping("/initUserCouponCurrency")
    public Result<Boolean> initUserCouponCurrency(@RequestParam(value = "userId", required = false) Long userId) {
        couponCurrencyService.initUserCouponCurrency(userId);
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/vipPassRemind
     * 会员到期提醒
     *
     * @return
     */
    @Deprecated
    @RequestMapping("/vipPassRemind")
    public Result<Boolean> vipPassRemind() {
        vipTask.vipPassRemind();
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/initUserAccountDetail?userId=
     * 初始化用户资金明细金额
     *
     * @param userId 指定用户id，若传只会初始化用户的资金明细
     * @return
     */
    @RequestMapping("/initUserAccountDetail")
    public Result<Boolean> initUserAccountDetail(@RequestParam(value = "userId", required = false) Long userId) {
        znsUserAccountDetailService.initUserAccountDetail(userId);
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/initScoreCurrency?currencyCode=
     * 初始化积分兑换指定币种价格
     *
     * @param currencyCode        币种类型
     * @param exchangeScoreRuleId 指定兑换id，若传只会初始化该规则
     * @return
     */
    @RequestMapping("/initScoreCurrency")
    public Result<Boolean> initScoreCurrency(@RequestParam(value = "currencyCode") String currencyCode,
                                             @RequestParam(value = "exchangeScoreRuleId", required = false) Long exchangeScoreRuleId) {
        exchangeScoreRuleCurrencyService.initScoreCurrency(currencyCode, exchangeScoreRuleId);
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/initVipShopCurrency?currencyCode=
     * 初始化vip会员指定币种价格
     *
     * @param currencyCode 币种类型
     * @param vipShopId    指定会员id，若传只会初始化该会员
     * @return
     */
    @RequestMapping("/initVipShopCurrency")
    public Result<Boolean> initVipShopCurrency(@RequestParam(value = "currencyCode") String currencyCode, @RequestParam(value = "vipShopId", required = false) Long vipShopId) {
        vipShopCurrencyService.initVipShopCurrency(currencyCode, vipShopId);
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/initGoodsCurrency?currencyCode=
     * 初始化vip会员指定币种价格
     *
     * @param currencyCode 币种类型
     * @param goodsId      指定商品id，若传只会初始化该商品
     * @return
     */
    @RequestMapping("/initGoodsCurrency")
    public Result<Boolean> initGoodsCurrency(@RequestParam(value = "currencyCode") String currencyCode, @RequestParam(value = "goodsId", required = false) Long goodsId) {
        goodsCurrencyService.initGoodsCurrency(currencyCode, goodsId);
        return CommonResult.success(true);
    }

    /**
     * http://localhost:7771/admin/test/initUserUserRankedLevel?userId=
     * 用户段位进行初始化
     *
     * @param userId 指定开始用户id，若传只会初始化改用户之后的数据
     */
    @RequestMapping("/initUserUserRankedLevel")
    public Result<Boolean> initUserUserRankedLevel(@RequestParam(value = "userId", required = false) Long userId) {
        userId = userId == null || userId < 0 ? 0 : userId;
        rankActivityManager.initRankedData(userId);
        return CommonResult.success(true);
    }

    /**
     * 手动下载机器人昵称
     * http://localhost:7771/admin/test/uploadNick?countryCode=TH
     * @param countryCode 国家cdoe TH
     * @param gender 性别,1:男，2：女
     */
//    @RequestMapping("/uploadNick")
//    public Result uploadNick( @RequestParam(value="countryCode") String countryCode, @RequestParam(value="gender",required = false) Integer gender ) {
//        if (gender != null){
//            rotNickScript.uploadNick(gender,countryCode);
//        }else {
//            rotNickScript.uploadNick(1,countryCode);
//            rotNickScript.uploadNick(2,countryCode);
//        }
//        return CommonResult.success();
//    }

    /**
     * 手动创建机器人
     * http://localhost:7771/admin/test/batchAddRots?country=Thailand&num1=1000&num2=1000
     *
     * @param country 国家名
     * @param num1    男性数量
     * @param num2    女性数量
     */
    @RequestMapping("/batchAddRots")
    public Result batchAddRotsProxy(String country, Integer num1, Integer num2, String languageCode) {
        I18nConstant.LanguageCodeEnum languageCodeEnum = I18nConstant.LanguageCodeEnum.findByCode(languageCode);
        if (languageCodeEnum == null) {
            return CommonResult.success("语言code不存在");
        }
        rotInternationalizationScript.batchAddRotsProxy(country + "#" + num1 + "#" + num2 + "#" + languageCode);
        return CommonResult.success();
    }

    /**
     * 手动全量用户账户升级
     * http://localhost:7771/admin/test/accountUpgrade?userIdStr=1,2,3
     *
     * @param userIdStr 用户id字符串，多个用,后隔开，传了用户只会初始化指定用户
     */
    @RequestMapping("/accountUpgrade")
    public Result<String> accountUpgrade(@RequestParam(value = "userIdStr", required = false) String userIdStr) {
        List<Long> userIds = new ArrayList<>();
        if (StringUtils.hasText(userIdStr)) {
            userIdStr = userIdStr.replaceAll("，", ",");
            Arrays.stream(userIdStr.split(",")).forEach(item -> userIds.add(Long.valueOf(item)));
        }
        String str = userAccountUpgradeBizService.initAllUserAccount(userIds);
        return CommonResult.success(str);
    }

    /**
     * 手动全量初始化设备质保
     * http://localhost:7771/admin/test/initEquipmentQuality?equipmentNoStr=1,2,3
     *
     * @param equipmentNoStr 设备编号字符串，多个用,后隔开，传了用户只会初始化指定设备
     */
    @RequestMapping("/initEquipmentQuality")
    public Result<String> initEquipmentQuality(@RequestParam(value = "equipmentNoStr", required = false) String equipmentNoStr) {
        List<String> equipmentNos = new ArrayList<>();
        if (StringUtils.hasText(equipmentNoStr)) {
            equipmentNoStr = equipmentNoStr.replaceAll("，", ",");
            equipmentNos.addAll(Arrays.asList(equipmentNoStr.split(",")));
        }
        String str = equipmentQualityBiz.initEquipmentQuality(equipmentNos);
        return CommonResult.success(str);
    }

    /**
     * 手动全量初始化设备运动数据
     * http://localhost:7771/admin/test/initEquipmentRunData?equipmentNoStr=1,2,3
     *
     * @param equipmentNoStr 设备编号字符串，多个用,后隔开，传了用户只会初始化指定设备
     */
    @RequestMapping("/initEquipmentRunData")
    public Result<String> initEquipmentRunData(@RequestParam(value = "equipmentNoStr", required = false) String equipmentNoStr) {
        List<String> equipmentNos = new ArrayList<>();
        if (StringUtils.hasText(equipmentNoStr)) {
            equipmentNoStr = equipmentNoStr.replaceAll("，", ",");
            equipmentNos.addAll(Arrays.asList(equipmentNoStr.split(",")));
        }
        String str = equipmentCareBiz.initEquipmentRunData(equipmentNos);
        return CommonResult.success(str);
    }

    /**
     * 手动全量初始化设备奖励数据
     * http://localhost:7771/admin/test/initEquipmentAwardData?userIdStr=1,2,3
     *
     * @param userIdStr 设备编号字符串，多个用,后隔开，传了用户只会初始化指定用户
     */
    @RequestMapping("/initEquipmentAwardData")
    public Result<String> initEquipmentAwardData(@RequestParam(value = "userIdStr", required = false) String userIdStr) {
        List<Long> userIds = new ArrayList<>();
        if (StringUtils.hasText(userIdStr)) {
            userIdStr = userIdStr.replaceAll("，", ",");
            userIds.addAll(Arrays.asList(userIdStr.split(",")).stream().map(Long::valueOf).toList());
        }
        String str = userEquipmentStatisticsBizService.initEquipmentAwardData(userIds);
        return CommonResult.success(str);
    }

    /**
     * 手动全量初始化质保审核数据
     * http://localhost:7771/admin/test/initQualityAudit?bluetoothMacStr=1,2,3
     *
     * @param bluetoothMacStr 设备蓝牙字符串，多个用,后隔开，传了用户只会初始化指定设备
     */
    @RequestMapping("/initQualityAudit")
    public Result<String> initQualityAudit(@RequestParam(value = "bluetoothMacStr", required = false) String bluetoothMacStr) {
        List<String> bluetoothMacs = new ArrayList<>();
        if (StringUtils.hasText(bluetoothMacStr)) {
            bluetoothMacStr = bluetoothMacStr.replaceAll("，", ",");
            bluetoothMacs.addAll(Arrays.asList(bluetoothMacStr.split(",")));
        }
        String str = equipmentQualityBiz.initQualityAudit(bluetoothMacs);
        return CommonResult.success(str);
    }

    @Resource
    private EquipmentCareMessageBiz equipmentCareMessageBiz;
    @Resource
    private EquipmentCareRuleService equipmentCareRuleService;

    /**
     * 测试设备质保推送
     * http://localhost:7771/admin/test/testEquipmentCareMsg?careRuleId=1&equipmentId=1
     *
     * @param careRuleId  保养规则id
     * @param equipmentId 设备id
     */
    @RequestMapping("/testEquipmentCareMsg")
    public Result<String> testEquipmentCareMsg(@RequestParam(value = "careRuleId") Long careRuleId, @RequestParam(value = "equipmentId", required = false) Long equipmentId) {
        EquipmentCareRuleDo careRuleDo = equipmentCareRuleService.findById(careRuleId);
        if (careRuleDo == null) {
            return CommonResult.fail("保养规则不存在");
        }
        equipmentCareMessageBiz.pushMsgByCareRule(careRuleDo, equipmentId);
        return CommonResult.success("已触发");
    }

    /**
     * 手动全量活动区域升级
     * http://localhost:7771/admin/test/updateActivityArea?activityIdStr=1,2,3
     *
     * @param activityIdStr 活动id字符串，多个用,后隔开，传了用户只会初始化指定活动
     */
    @RequestMapping("/updateActivityArea")
    public Result<String> updateActivityArea(@RequestParam(value = "activityIdStr", required = false) String activityIdStr) {
        List<Long> activityIds = new ArrayList<>();
        if (StringUtils.hasText(activityIdStr)) {
            activityIdStr = activityIdStr.replaceAll("，", ",");
            Arrays.stream(activityIdStr.split(",")).forEach(item -> activityIds.add(Long.valueOf(item)));
        }
        String str = activityAreaBizService.initAllActivityArea(activityIds);
        return CommonResult.success(str);
    }

    /**
     * 重复邮箱处理
     * http://localhost:7771/admin/test/repeatEmail?email=<EMAIL>
     *
     * @param email 邮箱，传了邮箱只会初始化指定账户
     */
    @RequestMapping("/repeatEmail")
    public Result<String> repeatEmail(@RequestParam(value = "email", required = false) String email) {
        String str = userManager.repeatEmail(email);
        return CommonResult.success(str);
    }

    /**
     * 手动给机器人入池
     * http://localhost:7771/admin/testTeam/addRotPond
     *  @see testController#addRotPond(AddRotPondReqDto)
     * {
     *     "rotPondAddMetaInfo": {
     *         "country": "Thailand",
     *         "hasPic": true,
     *         "km": 1,
     *         "language": "th_TH"
     *     },
     *     "rotPondAdds": [
     *         {
     *             "femaleNum": 900,
     *             "maleNum": 2100,
     *             "mode": "S+",
     *             "totalNum": 3000
     *         }
     *     ]
     * }
     *
     *
     *
     */

    /**
     * 手动执行创建聚合活动任务
     * http://localhost:7771/admin/test/doPole?poleId=121&dayStr=2024-04-07 00:25:12
     *
     * @param poleId 聚合活动规则id
     * @param dayStr 指定执行时间
     */
    @RequestMapping("/doPole")
    public Result doPole(@RequestParam(value = "dayStr") String dayStr, @RequestParam(value = "poleId") Long poleId) {
        //执行时间，默认当前时间，可以指定时间
        log.info("PolymerizationActTask#doPole-----聚合活动创建测试 dayStr：" + dayStr + ",poleId:" + poleId);
        PolymerizationActivityPole pole = polymerizationActivityPoleService.findById(poleId);
        if (pole == null) {
            return CommonResult.fail("规则不存在");
        }
        ZonedDateTime executeDate = ZonedDateTime.now();
        if (StringUtils.hasText(dayStr) && DateUtil.isValidYYYYMMDDHHMMSS(dayStr)) {
            executeDate = DateUtil.parseStr2Date(dayStr, DateUtil.YYYY_MM_DD_HH_MM_SS);
        }
        String msg = polymerizationActivityManager.executePolymerization(pole, true, executeDate);
        return CommonResult.success(msg);
    }

    /**
     * 指定用户推送指定消息
     * https://yadmin.pitpatfitness.com/admin/test/testPush/55206/5151
     *
     * @param userId
     * @param msgId
     * @return
     */
    @RequestMapping("/testPush/{userId}/{msgId}")
    public Result testPush(@PathVariable("userId") Long userId, @PathVariable("msgId") Long msgId) {
        MessageTaskMsg messageTaskMsg = messageTaskMsgDao.selectById(msgId);
        log.info(JsonUtil.writeString(messageTaskMsg));
        if (messageTaskMsg != null) {
            messageTaskService.doPush(List.of(messageTaskMsg), null, Arrays.asList(userId), 2, OrderUtil.getBatchNo(), 0, 1);
        }
        return CommonResult.success();
    }


    @Resource
    private MainActivityService mainActivityService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;
//
//    @GetMapping("/addOldAct")
//    public void addOldAct (Long startId, Long endId) {
//        List<ZnsRunActivityEntity> list = runActivityService.list(Wrappers.<ZnsRunActivityEntity>lambdaQuery().ge(true, ZnsRunActivityEntity::getId, startId)
//                .le(true, ZnsRunActivityEntity::getId, endId).eq(ZnsRunActivityEntity::getIsDelete, 0));
//        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
//            return;
//        }
//        List<List<ZnsRunActivityEntity>> partition = ListUtils.partition(list, 100);
//        for (List<ZnsRunActivityEntity> entityList : partition) {
//            executor.execute(new Runnable() {
//                @Override
//                public void run() {
//                    for (ZnsRunActivityEntity activity : entityList) {
//                        MainActivity activityServiceById = mainActivityService.findById(activity.getId());
//                        if (Objects.nonNull(activityServiceById)) {
//                            continue;
//                        }
//                        //保存新表
//                        MainActivity mainActivity = new MainActivity().setOldActivityId(activity.getId()).setId(activity.getId())
//                                .setMainType(MainActivityTypeEnum.OLD.getType()).setOldType(activity.getActivityType())
//                                .setActivityStartTime(DateUtil.formatDate(activity.getActivityStartTime(), DateUtil.DATE_TIME_SHORT))
//                                .setActivityEndTime(DateUtil.formatDate(activity.getActivityEndTime(), DateUtil.DATE_TIME_SHORT))
//                                .setApplicationStartTime(DateUtil.formatDate(activity.getApplicationStartTime(), DateUtil.DATE_TIME_SHORT))
//                                .setApplicationEndTime(DateUtil.formatDate(activity.getApplicationEndTime(), DateUtil.DATE_TIME_SHORT));
//                        mainActivityService.insert(mainActivity);
//                    }
//                }
//            });
//        }
//    }


    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;


    @Value("${zns.config.rabbitQueue.run}")
    private String run;

    @GetMapping("/fixUserTask")
    public void fixUserTask() {
        RunActivityUserTask task = new RunActivityUserTask();
        task.setId(22621L);
        task.setIsUnlock(1);
        runActivityUserTaskService.update(task);
//        ZnsUserRunDataDetailsEntity userRunDataDetail = new ZnsUserRunDataDetailsEntity();
//        userRunDataDetail.setId(17229355L);
//        userRunDataDetail.setTaskId(22620L);
//        userRunDataDetailsService.updateById(userRunDataDetail);

        RunningData runningData = new RunningData(405237L, 596995L, 1, 17229355L, "");
        rabbitTemplate.convertAndSend(run, JsonUtil.writeString(runningData));

    }

    @Resource
    private UserRunRouteService userRunRouteService;


    @GetMapping("/migrateUserRouteData")
    public void migrateUserRouteData() {
        Long id = 0L;
        int limit = 200;
        List<ZnsUserRunDataDetailsEntity> runDataDetailsList = new ArrayList<>();
        while (true) {
            runDataDetailsList = znsUserRunDataDetailsService.listByIdLimit(id, limit);
            if (CollectionUtils.isEmpty(runDataDetailsList)) {
                return;
            }
            Set<String> runDataRouteList = runDataDetailsList.stream().map(e -> e.getUserId() + "_" + e.getRouteId()).collect(Collectors.toSet());
            List<UserRunRoute> oldList = userRunRouteService.findListByUserRoute(runDataRouteList);
            Set<String> runRouteList = oldList.stream().map(UserRunRoute::getUserRouteStr).collect(Collectors.toSet());
            Set<String> difference = Sets.difference(runDataRouteList, runRouteList);

            List<UserRunRoute> collect = difference.stream().map(e -> {
                UserRunRoute userRunRoute = new UserRunRoute();
                userRunRoute.setUserRouteStr(e);
                return userRunRoute;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                userRunRouteService.batchInsert(collect);
            }
            id = runDataDetailsList.get(runDataDetailsList.size() - 1).getId();
        }
    }

    /**
     * 真人用户运动记录初始化
     */
    @GetMapping("/migrateUserDetailsData")
    public void migrateUserDetailsData() {
        try {
            List<ZnsUserRunDataDetailsEntity> runDataDetailsList = znsUserRunDataDetailsService.findListByRealCondition();
            if (CollectionUtils.isEmpty(runDataDetailsList)) {
                return;
            }
            List<RealPersonRunDataDetails> collect = runDataDetailsList.stream().map(s -> {
                RealPersonRunDataDetails realPersonRunDataDetails = new RealPersonRunDataDetails();
                BeanUtils.copyProperties(s, realPersonRunDataDetails, "id");
                realPersonRunDataDetails.setRunDataDetailsId(s.getId());
                return realPersonRunDataDetails;
            }).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(collect)) {
                realPersonRunDataDetailsService.batchInsert(collect);
            }
        } catch (Exception e) {
            log.info("报错信息 e", e);
        }
    }

    /**
     * 测试主库查询
     * http://localhost:7771/admin/test/testMaster
     */
    @RequestMapping("/testMaster")
    public Result testMaster() {
        String sql = "SELECT rank FROM `pitpat`.`zns_ranked_level` WHERE id =1";
        List<Map<String, Object>> maps = labelManageSqlTemplateDao.executeSql(sql);
        return CommonResult.success(maps);
    }

    /**
     * 测试从库查询
     * http://localhost:7771/admin/test/testSlave
     */
    @RequestMapping("/testSlave")
    public Result testSlave() {
        String sql = "SELECT rank FROM `pitpat`.`zns_ranked_level` WHERE id =1";
        List<Map<String, Object>> maps = labelManageSqlTemplateDao.executeReadSql(sql);
        return CommonResult.success(maps);
    }

    /**
     * 安卓push测试
     * http://localhost:7771/admin/test/testAndroidPush
     */
    @Resource
    private UserPushTokenService userPushTokenService;
    @Value("${spring.profiles.active}")
    private String profile;

    @RequestMapping("/testAndroidPush")
    public Result testAndroidPush(@RequestParam(value = "content", required = false) String content,
                                  @RequestParam(value = "userIdStr", required = false) String userIdStr) {
        userIdStr = Optional.ofNullable(userIdStr).orElse("337126");
        content = Optional.ofNullable(content).orElse("我不知道我在哪里啊！！！ are you ok");
        List<Long> userIds = Arrays.stream(userIdStr.split(",")).map(Long::valueOf).toList();
        List<UserPushToken> userTokens = userPushTokenService.findListByUserIds(userIds);
        if (org.springframework.util.CollectionUtils.isEmpty(userTokens)) {
            return CommonResult.fail("用户不存在");
        }
        List<String> androidTokens = userTokens.stream().filter(item -> StringUtils.hasText(item.getAndroidPushToken()) && !"-1".equals(item.getAndroidPushToken())).map(UserPushToken::getAndroidPushToken).toList();
        if (org.springframework.util.CollectionUtils.isEmpty(userTokens)) {
            return CommonResult.fail("用户不是安卓手机");
        }
        try {
            FirebaseUtils.pushMultiple("Hello! boy", content, androidTokens, "PitPat", new HashMap<>(), profile, null, null, "");
        } catch (FirebaseMessagingException e) {
            return CommonResult.fail(e.getMessage());
        }
        return CommonResult.success("发生成功");
    }

    /**
     * AI训练heightStr,数据错误修复
     *
     * @return
     */
    @RequestMapping("/fixAi")
    public Result fixAi() {
        List<UserAiBaseinfo> userAiBaseinfos = userAiBaseinfoDao.selectList(
                new QueryWrapper<UserAiBaseinfo>().lambda().eq(UserAiBaseinfo::getIsDelete, 0).gt(UserAiBaseinfo::getId, 3357));
        for (UserAiBaseinfo userAiBaseinfo : userAiBaseinfos) {
            String heightStr = userAiBaseinfo.getHeightStr();
            if (heightStr != null && !heightStr.isEmpty()) {
                String newHeightStr = formatDecimalString(heightStr);
                userAiBaseinfo.setHeightStr(newHeightStr);
            }
            userAiBaseinfoDao.updateById(userAiBaseinfo);
        }
        return CommonResult.success();
    }

    @Resource
    private UserLevelService userLevelService;

    @Resource
    private UserLevelRuleService userLevelRuleService;


    @GetMapping("/updateLevel")
    public Result updateLevel(Long userId) {
        if (Objects.nonNull(userId)) {
            UserLevel userLevel = userLevelService.findByUserId(userId);
            upgradeLevel(userLevel);
        } else {
            int level = 15;
            List<UserLevel> userLevelList = userLevelService.findByLevel(level);
            for (UserLevel userLevel : userLevelList) {
                upgradeLevel(userLevel);
            }
        }
        return CommonResult.success();
    }

    private void upgradeLevel(UserLevel userLevel) {
        UserLevelRule userLevelRule = userLevelRuleService.findByExp(userLevel.getExperience());
        if (userLevelRule.getLevel() > userLevel.getLevel()) {
            Integer level = userLevelRule.getLevel() > userLevel.getLevel() ? userLevelRule.getLevel() : userLevel.getLevel();
            userLevel.setLevel(level);
            userLevel.setHistoryLevel(level);
            userLevel.setId(null);
            userLevelService.updateByUserId(userLevel);
        }
    }


    /**
     * 格式化数字字符串，确保至少保留一位小数。
     */
    private static String formatDecimalString(String numberStr) {
        BigDecimal number = new BigDecimal(numberStr);
        number = number.stripTrailingZeros(); // 移除尾部多余的零
        // 检查是否有小数部分
        if (number.scale() <= 0) {
            // 没有小数部分时，添加 ".0"
            return number.toPlainString() + ".0";
        }
        return number.toPlainString();
    }


    public static void main(String[] args) {
        String sql = "SELECT if(IFNULL(count(*),0) > 0,1,null) FROM zns_link_bluetooth_record connect1 WHERE connect1.is_delete=0 AND connect1.user_id = ? AND connect1.gmt_create >= '2024-05-14 08:00:00' AND connec1t.gmt_create <= '2024-05-14 08:00:00'";
        PTuple11 data = PSqlParseUtil.getParserInfo(sql).getData();
        System.out.println(data);
    }

    @GetMapping("rainmaker/get")
    public Result<String> getRainmakerLogin(@RequestParam String name) {
        log.info("map={}", name);
        return CommonResult.success(name);
    }

    @PutMapping("rainmaker")
    public Result<Map> rainmakerLogin(@RequestBody Map map) {
        log.info("map={}", map);
        return CommonResult.success(map);
    }

    @PostMapping("rainmaker/post")
    public Result<Map> postRainmakerLogin(@RequestBody Map map) {
        log.info("map={}", map);
        return CommonResult.success(map);
    }

    /**
     * 测试用户注销，删除socket
     * http://localhost:7771/admin/test/deleteUser?email=1212
     */
    @RequestMapping("/deleteUser")
    public Result<Boolean> deleteUser(@RequestParam String email) {
        socketPushUtils.deleteUser(email);
        return CommonResult.success(true);
    }

    /**
     * 自定义H5页面配置支持多国家洗数,传id洗单条,不传id则全部
     * http://localhost:7771/admin/test/updateCustomH5?id=1
     */
    @RequestMapping("/updateCustomH5")
    public Result<Boolean> updateCustomH5(@RequestParam(required = false) Long id) {
        log.info("[updateCustomH5]---自定义H5配置洗数,start");
        adminCustomH5Manager.updateCustomH5(id);
        log.info("[updateCustomH5]---自定义H5配置洗数,end");
        return CommonResult.success(true);
    }

    /**
     * 测试 app 日期返回值
     *
     * @return
     */
    @RequestMapping("date")
    public Result testDateTime() {
        return CommonResult.success(DateTimeDto.of());
    }

    record DateTimeDto(
            ZonedDateTime date,
            ZonedDateTime zonedDateTime
    ) {

        public static DateTimeDto of() {
            return new DateTimeDto(ZonedDateTime.now(),  ZonedDateTime.now());
        }
    }
    @Resource
    private ClubService clubService;

    @Resource
    private ClubMemberService clubMemberService;

    @Resource
    private TencentImUtil tencentImUtil;

    @Value("${spring.profiles.active}")
    private String envProfile;


    /**
     * 给已存在的俱乐部创建群聊
     */
    @RequestMapping("/dealClubGroup")
    public void dealClubGroup(@RequestParam(value = "id", required = false) Long id) {
        List<Club> clubList;
        if (Objects.nonNull(id)) {
            Club club = clubService.findById(id);
            clubList = Lists.newArrayList(club);
        } else {
            clubList = clubService.findNormalClubList();
        }
        for (Club club : clubList) {
            Long clubId = club.getId();
            String env = (envProfile.equals("prod") || envProfile.equals("pre")) ? "prod" : envProfile;//由于预发和线上使用同数据库
            String clubGroupId = ApiConstants.clubGroupId + clubId + env;
            Club club1 = new Club();
            club1.setId(clubId);
            club1.setClubGroupId(clubGroupId);
            clubService.update(club1);
            GroupResultDto resultDto = tencentImUtil.createGroup(club.getOwnerUserId(), clubGroupId, club.getName());
            if (Objects.equals(resultDto.getErrorCode(), 0)) {
                List<Long> memberIdList = clubMemberService.findByClubId(clubId);
                List<List<Long>> partition = Lists.partition(memberIdList, 100);
                for (List<Long> list : partition) {
                    tencentImUtil.addGroupMember(clubGroupId, list, 1);
                }
            }

        }
    }

    @Resource
    private ZnsGoodsSkuService goodsSkuService;
    @Resource
    private ZnsGoodsService goodsService;
    @Resource
    private TestManager testManager;

    /**
     * 初始化商品销量
     */
    @RequestMapping("/initGoodsSaleCount")
    public void initGoodsSaleCount() {
        List<ZnsGoodsSkuEntity> list = goodsSkuService.findList(new GoodsSkuQuery());
        Map<Long, List<ZnsGoodsSkuEntity>> listMap = list.stream().collect(Collectors.groupingBy(ZnsGoodsSkuEntity::getGoodsId));
        for (Map.Entry<Long, List<ZnsGoodsSkuEntity>> listEntry : listMap.entrySet()) {
            ZnsGoodsEntity goods = goodsService.findById(listEntry.getKey());
            Integer count = listEntry.getValue().stream().map(s -> s.getSaleCount()).collect(Collectors.summingInt(Integer::intValue));
            Integer initialSaleCount = listEntry.getValue().stream().map(s -> s.getInitialSaleCount()).collect(Collectors.summingInt(Integer::intValue));
            ZnsGoodsEntity update = new ZnsGoodsEntity();
            update.setId(goods.getId());
            update.setInitialSaleCount(initialSaleCount);
            update.setSaleCount(count);
            goodsService.updateById(update);
        }
    }


    /**
     * 积分修复
     *
     * @param userId
     */
    @RequestMapping("/scoreRepair")
    public void scoreRepair(Long userId) {
        testManager.scoreRepair(userId);
    }

    /**
     * 积分修复
     * @param userIdList
     */
    @RequestMapping("/scoreRepair/v2")
    public void scoreRepairV2(String userIdList) {
        List<Long> longs = NumberUtils.stringToLong(userIdList.split(","));
        for (Long userId : longs) {
            testManager.scoreRepair(userId);
        }
    }
}
