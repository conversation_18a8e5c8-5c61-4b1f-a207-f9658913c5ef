package com.linzi.pitpat.admin.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class RunMusicVo {

    //主键
    private Long id;
    //音乐名称
    @NotBlank(message = "标题不能为空")
    private String title;
    //音乐文件名称
    private String fileName;
    // 音乐文件展示名称
    private String displayedName;
    //音乐文件url
    private String fileUrl;
    //适合步频
    private Integer stepRate;
    //状态:0-禁用，1-启用
    private Integer usageState;
    //备注
    private String remark;
    //修改人
    private String modifier;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;

}
