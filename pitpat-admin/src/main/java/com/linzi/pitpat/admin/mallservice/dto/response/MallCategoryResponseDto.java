package com.linzi.pitpat.admin.mallservice.dto.response;

import com.linzi.pitpat.data.mallservice.dto.response.ContentI8nDto;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class MallCategoryResponseDto {

    private Long id;
    //创建人
    private String creator;
    //类目ID
    private String categoryCode;
    //默认语言code
    private String defaultLanguageCode;
    //备注
    private String remake;

    //类目名称
    private String name;

    //类目名称，多语言集合
    private List<ContentI8nDto> i8nNames;
    //排序
    private Integer sort;

    private ZonedDateTime createTime;

    private List<MallCategoryGoodsResponseDto> goodsResponseList;
}
