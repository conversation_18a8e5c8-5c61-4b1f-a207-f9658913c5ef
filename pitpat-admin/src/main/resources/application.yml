# Tomcat
server:
  port: 7771
  # tomcat 配置
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
  servlet:
    # 应用的访问路径
    context-path: /admin
spring:
  main:
    allow-circular-references: true
  application:
    name: pitpat-admin
  # 环境 dev|pre|online
  profiles:
    active: dev
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
    encoding: UTF-8
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 350MB
      # 设置总上传的文件大小
      max-request-size: 350MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  jackson:
    serialization:
      # 忽略无法转换的对象
      fail_on_empty_beans: false
      # 将日期写为时间戳，而不是 ISO-8601 字符串
      write-dates-as-timestamps: true
    # 确保反序列化时不会调整时区
    deserialization:
      fail_on_unknown_properties: false
      adjust-dates-to-context-time-zone: false
    # 默认时区，设为 UTC 可以减少很多混乱
    time-zone: UTC
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
      time: HH:mm:ss
      date: yyyy-MM-dd

## 项目相关配置
pitpat:
  # 名称
  name: pitpat
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径
  profile: ./pitpat/uploadPath
  # 获取ip地址开关
  addressEnabled: true

captcha:
  # 验证码开关
  enabled: true
  # 验证码类型 math 数组计算 char 字符验证
  type: char
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: circle
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml,classpath*:mapper/**/*Dao.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.linzi.pitpat.**.entity
  configuration:
    # 自动驼峰命名规则（camel case）映射
    # 如果您的数据库命名符合规则无需使用 @TableField 注解指定数据库字段名
    mapUnderscoreToCamelCase: true
    # 默认枚举处理类,如果配置了该属性,枚举将统一使用指定处理器进行处理
    # org.apache.ibatis.type.EnumTypeHandler : 存储枚举的名称
    # org.apache.ibatis.type.EnumOrdinalTypeHandler : 存储枚举的索引
    # com.baomidou.mybatisplus.extension.handlers.MybatisEnumTypeHandler : 枚举类需要实现IEnum接口或字段标记@EnumValue注解.
    defaultEnumTypeHandler: org.apache.ibatis.type.EnumTypeHandler
    # 当设置为 true 的时候，懒加载的对象可能被任何懒属性全部加载，否则，每个属性都按需加载。需要和 lazyLoadingEnabled 一起使用。
    aggressiveLazyLoading: true
    # MyBatis 自动映射策略
    # NONE：不启用自动映射
    # PARTIAL：只对非嵌套的 resultMap 进行自动映射
    # FULL：对所有的 resultMap 都进行自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做任何处理 (默认值)
    # WARNING：以日志的形式打印相关警告信息
    # FAILING：当作映射失败处理，并抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # Mybatis一级缓存，默认为 SESSION
    # SESSION session级别缓存，同一个session相同查询语句不会再次查询数据库
    # STATEMENT 关闭一级缓存
    localCacheScope: SESSION
    # 开启Mybatis二级缓存，默认为 true
    cacheEnabled: false
    # 更详细的日志输出 会有性能损耗
    # logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 是否打印 Logo banner
    banner: true
    # 是否初始化 SqlRunner
    enableSqlRunner: false
    dbConfig:
      # 主键类型
      # AUTO 数据库ID自增
      # NONE 空
      # INPUT 用户输入ID
      # ASSIGN_ID 全局唯一ID
      # ASSIGN_UUID 全局唯一ID UUID
      idType: AUTO
      # 表名前缀
      tablePrefix: null
      # 字段 format,例: %s,(对主键无效)
      columnFormat: null
      # 表名是否使用驼峰转下划线命名,只对表名生效
      tableUnderline: true
      # 大写命名,对表名和字段名均生效
      capitalMode: false
      # 全局的entity的逻辑删除字段属性名
      logicDeleteField: isDelete
      # 逻辑已删除值
      logicDeleteValue: 1
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略判断
      # NOT_NULL 非NULL判断
      # NOT_EMPTY 非空判断(只对字符串类型字段,其他类型字段依然为非NULL判断)
      # DEFAULT 默认的,一般只用于注解里
      # NEVER 不加入 SQL
      insertStrategy: NOT_EMPTY
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_EMPTY
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      whereStrategy: NOT_EMPTY

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
--- # redisson 缓存配置
redisson:
  cacheGroup:
    # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
    - groupId: redissonCacheMap
      # 组过期时间(脚本监控)
      ttl: 60000
      # 组最大空闲时间(脚本监控)
      maxIdleTime: 60000
      # 组最大长度
      maxSize: 0
    - groupId: testCache
      ttl: 1000
      maxIdleTime: 500
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: imeJwmgwyT1TMEqCr11BcJj5mcD7ifQh6Ohw7UCF28HwHDfL4m
  # 令牌有效期（默认30分钟）
  expireTime: 720
#web 配置
web:
  interceptor:
    token:
      header: Authorization
# 日志配置
logging:
  level:
    web: debug
    com.linzi.pitpat: info
    org.springframework: warn
    org.springframework.data.mongodb: DEBUG

management:
  server:
    port: 8853
  endpoints:
    web:
      discovery:
        enabled: true
      exposure:
        include: health,metrics
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
