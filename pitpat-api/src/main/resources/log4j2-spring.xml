<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xwEx</Property>
        <Property name="LOG_LEVEL_PATTERN">%5p [${spring:spring.application.name},%X{traceId},%X{spanId}]</Property>
        <Property name="LOG_DATEFORMAT_PATTERN">yyyy-MM-dd HH:mm:ss.SSS</Property>
        <Property name="CONSOLE_LOG_PATTERN">%clr{%d{${LOG_DATEFORMAT_PATTERN}}}{faint} %clr{${LOG_LEVEL_PATTERN}} %clr{%pid}{magenta} %clr{---}{faint} %clr{[%15.15t]}{faint} %clr{%-40.40c{1.}}{cyan} %clr{:}{faint} %sm%n${LOG_EXCEPTION_CONVERSION_WORD}</Property>
        <Property name="FILE_LOG_PATTERN">%d{${LOG_DATEFORMAT_PATTERN}} ${LOG_LEVEL_PATTERN} %pid --- [%t] %-40.40c{1.} : %sm%n${LOG_EXCEPTION_CONVERSION_WORD}</Property>
        <SpringProfile name="dev">
            <property name="LOG_HOME" value="${sys:user.home}/logs/${spring:spring.application.name}"/>
        </SpringProfile>
        <SpringProfile name="!dev">
            <property name="LOG_HOME" value="/home/<USER>/logs/${spring:spring.application.name}"/>
        </SpringProfile>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${CONSOLE_LOG_PATTERN}" charset="UTF-8"/>
        </Console>

        <RollingRandomAccessFile name="INFO_FILE" fileName="${LOG_HOME}/info.log" filePattern="${LOG_HOME}/logs/all.%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            </Filters>
            <Policies>
                <!-- 每天分割一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 或者当文件大小达到 1GB 时分割 -->
                <SizeBasedTriggeringPolicy size="1GB"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="WARN_FILE" fileName="${LOG_HOME}/warn.log" filePattern="${LOG_HOME}/logs/warn.%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="ACCEPT"/>
            </Filters>
            <Policies>
                <!-- 每天分割一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 或者当文件大小达到 1GB 时分割 -->
                <SizeBasedTriggeringPolicy size="1GB"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="ERROR_FILE" fileName="${LOG_HOME}/error.log" filePattern="${LOG_HOME}/logs/error.%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <!-- 每天分割一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 或者当文件大小达到 1GB 时分割 -->
                <SizeBasedTriggeringPolicy size="1GB"/>
            </Policies>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="SQL_FILE" fileName="${LOG_HOME}/sql.log" filePattern="${LOG_HOME}/logs/sql.%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${FILE_LOG_PATTERN}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            </Filters>
            <Policies>
                <!-- 每天分割一次 -->
                <TimeBasedTriggeringPolicy interval="1"/>
                <!-- 或者当文件大小达到 1GB 时分割 -->
                <SizeBasedTriggeringPolicy size="1GB"/>
            </Policies>
        </RollingRandomAccessFile>

        <!-- 钉钉错误日志推送 -->
        <DingTalk name="DingTalkAppender">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
        </DingTalk>

    </Appenders>

    <Loggers>
        <AsyncLogger name="com.linzi.pitpat.framework.db.interceptor" additivity="false" level="INFO">
            <SpringProfile name="dev">
                <AppenderRef ref="Console"/>
            </SpringProfile>
            <AppenderRef ref="SQL_FILE"/>
        </AsyncLogger>

        <Root level="INFO">
            <SpringProfile name="dev">
                <AppenderRef ref="Console"/>
            </SpringProfile>
            <AppenderRef ref="INFO_FILE"/>
            <AppenderRef ref="WARN_FILE"/>
            <AppenderRef ref="ERROR_FILE"/>
            <AppenderRef ref="DingTalkAppender"/>
        </Root>
    </Loggers>
</Configuration>
