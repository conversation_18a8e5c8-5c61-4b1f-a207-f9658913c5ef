
#1 RedisTemplate进行基础性配置
spring:
  redis:
    database: 1
    host: **********
    port: 6379
    password: 123456
    timeout: 10s
    pool:
      max-active: 1000
      max-wait: -1
      max-idle: 10
      min-idle: 5
    #2 redisson属性的进行自动化配置
    redisson:
      cacheGroup:
        # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
        - groupId: redissonCacheMap
          # 组过期时间ms
          ttl: 300000
          # 组最大空闲时间ms
          maxIdleTime: 360000
          maxSize: 500
          defaultGroup: true
        - groupId: wearIdRedisson
          ttl: 86400000
          maxIdleTime: 86400000
          maxSize: 500
      config: |
        #cpu数
        threads: 8
        #cpu数*2+1
        nettyThreads: 16
        codec: !<org.redisson.codec.JsonJacksonCodec> {}
        transportMode: "NIO"
        #2-1 单机模式配置
        singleServerConfig:
          address: "redis://**********:6379"
          password: 123456
          database: 1
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 6000
          retryAttempts: 3
          retryInterval: 1500
          clientName: ${spring.application.name}
          connectionMinimumIdleSize: 10
          connectionPoolSize: 40
          dnsMonitoringInterval: 5000
          subscriptionsPerConnection: 8
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 25
          
