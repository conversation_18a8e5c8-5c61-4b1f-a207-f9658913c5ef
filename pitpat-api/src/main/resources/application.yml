# Tomcat
server:
  port: 7770
#  servlet:
#    context-path: /renren-fast
spring:
  datasource:
    hikari:
      maximum-pool-size: 100
      connection-timeout: 30000 #default 30s
  application:
    name: pitpat-api
  main:
    allow-circular-references: true
  # 环境 dev|pre|online
  profiles:
    active: dev
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: true
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
--- # redisson 缓存配置
#redisson:
#  cacheGroup:
#    # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
#    - groupId: redissonCacheMap:
#      # 组过期时间(脚本监控)
#      ttl: 60000
#      # 组最大空闲时间(脚本监控)
#      maxIdleTime: 60000
#      # 组最大长度
#      maxSize: 0
#    - groupId: testCache
#      ttl: 1000
#      maxIdleTime: 500
#  pitpat_test


#
# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml,classpath*:mapper/**/*Dao.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.linzi.pitpat.**.entity
  configuration:
    # 自动驼峰命名规则（camel case）映射
    # 如果您的数据库命名符合规则无需使用 @TableField 注解指定数据库字段名
    mapUnderscoreToCamelCase: true
    # 默认枚举处理类,如果配置了该属性,枚举将统一使用指定处理器进行处理
    # org.apache.ibatis.type.EnumTypeHandler : 存储枚举的名称
    # org.apache.ibatis.type.EnumOrdinalTypeHandler : 存储枚举的索引
    # com.baomidou.mybatisplus.extension.handlers.MybatisEnumTypeHandler : 枚举类需要实现IEnum接口或字段标记@EnumValue注解.
    defaultEnumTypeHandler: org.apache.ibatis.type.EnumTypeHandler
    # 当设置为 true 的时候，懒加载的对象可能被任何懒属性全部加载，否则，每个属性都按需加载。需要和 lazyLoadingEnabled 一起使用。
    aggressiveLazyLoading: true
    # MyBatis 自动映射策略
    # NONE：不启用自动映射
    # PARTIAL：只对非嵌套的 resultMap 进行自动映射
    # FULL：对所有的 resultMap 都进行自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做任何处理 (默认值)
    # WARNING：以日志的形式打印相关警告信息
    # FAILING：当作映射失败处理，并抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # Mybatis一级缓存，默认为 SESSION
    # SESSION session级别缓存，同一个session相同查询语句不会再次查询数据库
    # STATEMENT 关闭一级缓存
    localCacheScope: SESSION
    # 开启Mybatis二级缓存，默认为 true
    cacheEnabled: false
    # 更详细的日志输出 会有性能损耗
    # logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 是否打印 Logo banner
    banner: true
    # 是否初始化 SqlRunner
    enableSqlRunner: false
    dbConfig:
      # 主键类型
      # AUTO 数据库ID自增
      # NONE 空
      # INPUT 用户输入ID
      # ASSIGN_ID 全局唯一ID
      # ASSIGN_UUID 全局唯一ID UUID
      idType: AUTO
      # 表名前缀
      tablePrefix: null
      # 字段 format,例: %s,(对主键无效)
      columnFormat: null
      # 表名是否使用驼峰转下划线命名,只对表名生效
      tableUnderline: true
      # 大写命名,对表名和字段名均生效
      capitalMode: false
      # 全局的entity的逻辑删除字段属性名
      logicDeleteField: isDelete
      # 逻辑已删除值
      logicDeleteValue: 1
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略判断
      # NOT_NULL 非NULL判断
      # NOT_EMPTY 非空判断(只对字符串类型字段,其他类型字段依然为非NULL判断)
      # DEFAULT 默认的,一般只用于注解里
      # NEVER 不加入 SQL
      insertStrategy: NOT_EMPTY
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_EMPTY
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      whereStrategy: NOT_EMPTY


logging:
  level:
    web: debug
    org.springframework.cloud.sleuth: debug
    org.springframework.data.mongodb: debug
    org.springframework.amqp.rabbit.core: debug

management:
  server:
    port: 8849
  endpoints:
    web:
      discovery:
        enabled: true
      exposure:
        include:  health,metrics,refresh
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
#web 配置
web:
  interceptor:
    token:
      header: token
