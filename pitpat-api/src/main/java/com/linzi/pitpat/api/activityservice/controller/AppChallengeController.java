package com.linzi.pitpat.api.activityservice.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.request.Challenge1v1PageDto;
import com.linzi.pitpat.api.activityservice.dto.request.ChallengeActivityPageDto;
import com.linzi.pitpat.api.activityservice.dto.request.ChallengeStatusDto;
import com.linzi.pitpat.api.activityservice.dto.request.ChallengeUserDto;
import com.linzi.pitpat.api.activityservice.dto.request.SendChallengeDto;
import com.linzi.pitpat.api.activityservice.dto.request.SendChallengeRejectDto;
import com.linzi.pitpat.api.activityservice.dto.response.ChallengePKChartResp;
import com.linzi.pitpat.api.activityservice.manager.PkChallengeManager;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ISelect;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.ChallengeMySelfPKDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.ChallengePKQueryDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ChallengeMySelfPKRespDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ChallengePKDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ChallengePKRecordDto;
import com.linzi.pitpat.data.activityservice.manager.ActivityMessageManager;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.resp.Challenge1v1PageResp;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeActivityDetailResp;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeActivityResp;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeUserHomeResp;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeUserResp;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkAwardVo;
import com.linzi.pitpat.data.activityservice.service.PkChallengeBizService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * APP和不在线用户比赛
 */
@RestController
@RequestMapping("/app/challenge")
@Slf4j
@RequiredArgsConstructor
public class AppChallengeController extends BaseAppController {

    @Autowired
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Autowired
    private PkChallengeRecordService pkChallengeRecordService;
    @Autowired
    private ActivityMessageManager activityMessageManager;
    @Resource
    private ZnsUserAccountService znsUserAccountService;
    @Resource
    private PkChallengeBizService pkChallengeBizService;
    @Resource
    private ZnsUserService znsUserService;

    @Resource
    private ISysConfigService sysConfigService;

    private final PkChallengeManager pkChallengeManager;

    /**
     * 和我的最佳配速匹配的用户列表
     */
    @PostMapping("/user/list")
    public Result<ChallengeUserHomeResp> userList(@RequestBody ChallengeUserDto challengeUserDto) {
        ChallengeUserHomeResp challengeUserHomeResp = new ChallengeUserHomeResp();
        List<ChallengeUserResp> list = new ArrayList<>();
        ZonedDateTime day7 = DateUtil.addDays(DateUtil.startOfDate(ZonedDateTime.now()), -7);
        Integer deviceType = 0;
        Integer average_pace = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileageMinRunTime(challengeUserDto.getUserId(), Constants.target_1_Mile, Constants.target_1_Mile, day7, 1, null, deviceType);
        ZnsUserRunDataDetailsEntity runDataDetailsEntity = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileage(challengeUserDto.getUserId(), Constants.target_1_Mile, Constants.target_1_Mile, day7, average_pace, null, deviceType);
        Integer minaveragePace = 342;
        if (runDataDetailsEntity != null) {
            minaveragePace = runDataDetailsEntity.getAveragePace();
        }

        //为了提升性能的做法
        List<ChallengeUserResp> temp = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileageRuntime(
                challengeUserDto.getUserId(),
                Constants.target_1_Mile,
                Constants.target_1_Mile,
                1,
                minaveragePace - 300,
                minaveragePace + 300,
                day7, null);
        List<Long> userIdList = temp.stream().map(ChallengeUserResp::getUserId).collect(Collectors.toList());
        Map<Long, ZnsUserEntity> znsUserEntities = znsUserService.selectZnsUserListByChallengeUserRespIds(temp);
        Collections.sort(temp);
        // 为了提升性能
        List<Long> userIds = new ArrayList<>();
        int i = 0;
        for (ChallengeUserResp challengeUserResp : temp) {
            if (userIds.contains(challengeUserResp.getUserId())) {
                continue;
            }
            ZnsUserEntity znsUserEntity = znsUserEntities.get(challengeUserResp.getUserId());
            if (Objects.equals(znsUserEntity.getAcceptChallenge(), 0)) {
                continue;
            }
            challengeUserResp.setHeadPortrait(znsUserEntity.getHeadPortrait());
            challengeUserResp.setFirstName(znsUserEntity.getFirstName());
            if (i > 49) {
                break;
            }
            list.add(challengeUserResp);
            userIds.add(challengeUserResp.getUserId());
            i++;
        }

        // 展示离线pk奖励，奖励不再按照跑步时间区分
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.OFFLINE_PK_CHALLENGE_AWARD.getCode());
        OfflinePkAwardVo offlinePkAwardVo = JsonUtil.readValue(sysConfig.getConfigValue(), OfflinePkAwardVo.class);
        for (ChallengeUserResp challengeUserResp : list) {
            challengeUserResp.setScore(offlinePkAwardVo.getScore());
            Currency userCurrency = getUserCurrency();
            challengeUserResp.setCurrency(userCurrency);
            CurrencyAmount otherCurrencyAmount = offlinePkAwardVo.getAmountList().stream()
                    .filter(e -> e.getCurrencyCode().equals(userCurrency.getCurrencyCode())).findFirst().orElse(null);
            challengeUserResp.setAwardAmount(otherCurrencyAmount.getAmount());
            challengeUserResp.setCouponNum(offlinePkAwardVo.getCouponNum());
        }


        challengeUserHomeResp.setTargetMile(Constants.target_1_Mile);
        challengeUserHomeResp.setList(list);
        return CommonResult.success(challengeUserHomeResp);
    }


    /**
     * 活动列表
     */
    @PostMapping("/activity/list")
    public Result<ChallengeActivityDetailResp> userList(@RequestBody ChallengeActivityPageDto dto) {
        Integer deviceType = 0;
        ChallengeActivityDetailResp activityDetailResp = new ChallengeActivityDetailResp();
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        activityDetailResp.setAcceptChallenge(znsUserEntity.getAcceptChallenge());
        PPageUtils pageUtils = PPageUtils.startPage(dto.getPageNum(), dto.getPageSize()).doSelect(new ISelect() {
            @Override
            public List doSelect(IPage page) {
                return pkChallengeRecordService.selectActivityByPage(page, dto.getUserId(), null);
            }
        });

        List<ChallengeActivityResp> respList = pageUtils.getRows();
        //去重。自己挑战自己时只显示一条记录
        Collection<ChallengeActivityResp> uniqueList = respList.stream()
                .collect(Collectors.toMap(
                        ChallengeActivityResp::getId, // 提取 id 作为键
                        Function.identity(),          // 保留对象本身作为值
                        (existing, replacement) -> existing.getChallengeType() > replacement.getChallengeType() ? existing : replacement
                ))
                .values();
        for (ChallengeActivityResp challengeActivityResp : uniqueList) {
            if (Objects.equals(challengeActivityResp.getChallengeType(), 1)) {
                challengeActivityResp.setActivityTitle("PK Run initiated by you");
            } else {
                PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoNEUserId(challengeActivityResp.getBatchNo(), challengeActivityResp.getUserId());
                if (pkChallengeRecord != null) {
                    ZnsUserEntity pkUser = znsUserService.findById(pkChallengeRecord.getUserId());
                    if (Objects.nonNull(pkUser)) {
                        challengeActivityResp.setActivityTitle("PK Run initiated by " + pkUser.getFirstName());
                    }
                }
            }
        }
        ZonedDateTime day7 = znsUserEntity.getCreateTime();
        Integer average_pace = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileageMinRunTime(dto.getUserId(), Constants.target_1_Mile, Constants.target_1_Mile, day7, 1, null, deviceType);
        ZnsUserRunDataDetailsEntity runDataDetailsEntity = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileage(dto.getUserId(), Constants.target_1_Mile, Constants.target_1_Mile, day7, average_pace, null, deviceType);
        if (runDataDetailsEntity != null) {
            activityDetailResp.setAveragePace(runDataDetailsEntity.getAveragePace());
            activityDetailResp.setExpireDate(DateUtil.addDays(runDataDetailsEntity.getCreateTime(), 7));
            activityDetailResp.setRunDataDetailsId(runDataDetailsEntity.getId());
        }
        activityDetailResp.setPageUtils(pageUtils);
        return CommonResult.success(activityDetailResp);
    }

    /**
     * 接受挑战修改
     */
    @PostMapping("/acceptChallenge")
    public Result acceptChallenge(@RequestBody ChallengeStatusDto dto) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());

        znsUserEntity.setAcceptChallenge(dto.getAcceptChallenge());
        znsUserService.update(znsUserEntity);
        return CommonResult.success();
    }


    /**
     * 根据批次号获取1 v 1 页面信息
     */
    @PostMapping("/get1v1PageInfo")
    public Result<Challenge1v1PageResp> get1v1PageInfo(@RequestBody Challenge1v1PageDto dto) {
        PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(dto.getBatchNo(), 1);
        PkChallengeRecord wasPkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(dto.getBatchNo(), 0);
        ZnsUserEntity znsUserEntity = znsUserService.findById(pkChallengeRecord.getUserId());
        ZnsUserEntity wasZnsUser = znsUserService.findById(wasPkChallengeRecord.getUserId());

        Challenge1v1PageResp challenge1v1PageResp = new Challenge1v1PageResp();
        challenge1v1PageResp.setChallengeUserFirstName(znsUserEntity.getFirstName());
        challenge1v1PageResp.setChallengeUserId(pkChallengeRecord.getUserId());
        challenge1v1PageResp.setChallengeUserHeadPortrait(znsUserEntity.getHeadPortrait());
        challenge1v1PageResp.setWasChallengeUserFirstName(wasZnsUser.getFirstName());
        challenge1v1PageResp.setWasChallengeUserId(wasPkChallengeRecord.getUserId());
        challenge1v1PageResp.setWasChallengeUserHeadPortrait(wasZnsUser.getHeadPortrait());
        challenge1v1PageResp.setLaunchUserFirstName(wasZnsUser.getFirstName());
        ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = userRunDataDetailsService.findById(pkChallengeRecord.getRunDataDetailsId());
        challenge1v1PageResp.setPace(znsUserRunDataDetailsEntity.getAveragePace());
        // app告知访问该接口为挑战者
        challenge1v1PageResp.setCurrency(I18nConstant.buildCurrency(pkChallengeRecord.getCurrencyCode()));
        challenge1v1PageResp.setAward(pkChallengeRecord.getAward());
        challenge1v1PageResp.setScore(wasPkChallengeRecord.getScore());
        challenge1v1PageResp.setCouponNum(wasPkChallengeRecord.getCouponNum());
        challenge1v1PageResp.setActivityId(pkChallengeRecord.getActivityId());
        challenge1v1PageResp.setStatus(pkChallengeRecord.getStatus());

        return CommonResult.success(challenge1v1PageResp);
    }

    /**
     * 发起挑战
     */
    @PostMapping("/sendChallenge")
    public Result<PkChallengeRecord> sendChallenge(@RequestBody SendChallengeDto dto) {
        Integer appVersion = getAppVersion();
        PkChallengeRecord pkChallengeRecordResult = null;
        for (Long temp : dto.getInviteUserId()) {
            Long userId = dto.getUserId();
            Long inviteUserId = temp;

            // 如果为2，则交换邀请和被邀请者
            if (Objects.equals(dto.getType(), 2)) {
                Long t = userId;
                userId = inviteUserId;
                inviteUserId = t;
            }

            PkChallengeRecord pkChallengeRecord = new PkChallengeRecord();
            pkChallengeRecord.setRunDataDetailsId(dto.getRunDataDetailsId());
            String bachNo = OrderUtil.getBatchNo();
            pkChallengeRecord.setStatus(0);
            pkChallengeRecord.setUserId(userId);
            pkChallengeRecord.setChallengeType(1);
            pkChallengeRecord.setBatchNo(bachNo);
            pkChallengeRecord.setType(dto.getType());
            pkChallengeRecord.setIsNewPk(appVersion >= 4000 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            // 离线pk不再按照跑步时间区分奖励
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.OFFLINE_PK_CHALLENGE_AWARD.getCode());
            OfflinePkAwardVo offlinePkAwardVo = JsonUtil.readValue(sysConfig.getConfigValue(), OfflinePkAwardVo.class);
            pkChallengeRecord.setScore(offlinePkAwardVo.getScore());
            pkChallengeRecord.setCouponId(offlinePkAwardVo.getCouponId());
            pkChallengeRecord.setCouponNum(offlinePkAwardVo.getCouponNum());
            pkChallengeRecord.setOfflineFriendType(dto.getOfflineFriendType());
            fillPkAward(userId, pkChallengeRecord, offlinePkAwardVo);
            pkChallengeRecordService.insertPkChallengeRecord(pkChallengeRecord);

            pkChallengeRecordResult = pkChallengeRecord;

            PkChallengeRecord pkChallengeRecord1 = new PkChallengeRecord();
            pkChallengeRecord1.setRunDataDetailsId(dto.getRunDataDetailsId());
            pkChallengeRecord1.setStatus(0);
            pkChallengeRecord1.setUserId(inviteUserId);
            pkChallengeRecord1.setChallengeType(0);              //被邀请的用户被
            pkChallengeRecord1.setBatchNo(bachNo);
            pkChallengeRecord1.setScore(offlinePkAwardVo.getScore());
            pkChallengeRecord1.setCouponId(offlinePkAwardVo.getCouponId());
            pkChallengeRecord1.setCouponNum(offlinePkAwardVo.getCouponNum());
            fillPkAward(inviteUserId, pkChallengeRecord1, offlinePkAwardVo);
            pkChallengeRecord1.setType(dto.getType());
            pkChallengeRecord1.setIsNewPk(appVersion >= 4000 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            pkChallengeRecord1.setOfflineFriendType(dto.getOfflineFriendType());


            pkChallengeRecordService.insertPkChallengeRecord(pkChallengeRecord1);
            Map<String, Object> params = new HashMap<>();
            params.put("batchNo", pkChallengeRecord.getBatchNo());
            Map<String, Object> replace = new HashMap<>();

            ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
            ZnsUserEntity znsUserEntityInvite = znsUserService.findById(inviteUserId);

            if (Objects.equals(dto.getType(), 1)) {          // 1 表示挑战他人， 2 表示邀请他人挑战自己
                log.info("挑战他人");
            } else {
                // 你邀请 {被挑战者昵称}参加「1mile 最佳配速挑战」
                replace.put("wasChallengeName", znsUserEntity.getFirstName());
//                activityMessageManager.sendPkChallengeIm(inviteUserId, Constants.invite_challenge_you_pace, replace, params, "lznative://main/offlinepkinvalid", 0l);

                replace.put("challengeName", znsUserEntityInvite.getFirstName());
//                activityMessageManager.sendPkChallengeIm(userId, Constants.invite_you_challenge, replace, params, "lznative://main/offlinepkinvalid", 0l);
            }
        }
        return CommonResult.success(pkChallengeRecordResult);
    }

    /**
     * 填充pk奖励金额
     *
     * @param userId
     * @param pkChallengeRecord
     * @param offlinePkAwardVo
     */
    private void fillPkAward(Long userId, PkChallengeRecord pkChallengeRecord, OfflinePkAwardVo offlinePkAwardVo) {
        Integer appVersion = getAppVersion();
        CurrencyAmount currencyAmount = offlinePkAwardVo.getAmountList().stream()
                .filter(e -> e.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())).findFirst().orElse(null);
        ZnsUserAccountEntity userAccount = znsUserAccountService.getUserAccount(userId);
        String currencyCode = userAccount.getCurrencyCode();
        pkChallengeRecord.setCurrencyCode(currencyCode);
        CurrencyAmount otherCurrencyAmount = offlinePkAwardVo.getAmountList().stream()
                .filter(e -> e.getCurrencyCode().equals(userAccount.getCurrencyCode())).findFirst().orElse(null);
        if (Objects.isNull(otherCurrencyAmount)) {
            BigDecimal award = I18nConstant.currencyFormat(currencyCode, currencyAmount.getAmount());
            pkChallengeRecord.setAward(award);
        } else {
            BigDecimal award = I18nConstant.currencyFormat(currencyCode, otherCurrencyAmount.getAmount());
            pkChallengeRecord.setAward(award);
        }
    }

    /**
     * 拒绝挑战
     */
    @PostMapping("/rejectChallenge")
    public Result rejectChallenge(@RequestBody SendChallengeRejectDto dto) {
        PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectBySendChallengeReject(dto.getBatchNo(), dto.getUserId());
        List<PkChallengeRecord> pkChallengeRecords = pkChallengeRecordService.selectPkChallengeRecordByBatchNo(dto.getBatchNo());
        for (PkChallengeRecord pk : pkChallengeRecords) {
            pk.setStatus(2);
            pkChallengeRecordService.updatePkChallengeRecordById(pk);
        }
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());

        Map<String, Object> replace = new HashMap<>();
        replace.put("wasChallengeName", znsUserEntity.getFirstName());
        Map<String, Object> params = new HashMap<>();
        params.put("batchNo", pkChallengeRecord.getBatchNo());
        params.put("targetMile", Constants.target_1_Mile);
//        activityMessageManager.sendPkChallengeIm(pkChallengeRecord.getUserId(), Constants.invite_challenge_you_reject, replace, params, "lznative://main/offlinepkinvalid", pkChallengeRecord.getActivityId());
        return CommonResult.success();
    }

    /**
     * 推荐挑战列表
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/recommended/list")
    public Result<List<ChallengePKDto>> recommendedList(@RequestBody ChallengePKQueryDto queryDto) {
        if (Objects.isNull(queryDto.getDeviceType())) {
            queryDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        Integer appVersion = getAppVersion();
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(pkChallengeBizService.findListByUser(queryDto, appVersion, loginUser));
    }

    /**
     * 好友挑战列表
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/friendChallenge/list")
    public Result<List<ChallengePKDto>> friendChallengeList(@RequestBody ChallengePKQueryDto queryDto) {
        if (Objects.isNull(queryDto.getDeviceType())) {
            queryDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        return CommonResult.success(pkChallengeBizService.findListByFriends(queryDto, getLoginUser(), getAppVersion()));
    }

    /**
     * 挑战记录
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/challengeRecord/list")
    public Result<PPageUtils<ChallengePKRecordDto>> challengeRecordList(@RequestBody ChallengePKQueryDto queryDto) {
        return CommonResult.success(pkChallengeBizService.findPKRecordList(queryDto));
    }


    /**
     * 挑战统计图表
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/challengeRecord/statistical/chart")
    public Result<ChallengePKChartResp> challengeStatisticalChart(@RequestBody ChallengePKQueryDto queryDto) {
        ZnsUserEntity loginUser = getLoginUser();
        ChallengePKChartResp challengePKChartResp = pkChallengeManager.challengeStatisticalChart(loginUser, queryDto);
        return CommonResult.success(challengePKChartResp);
    }


    /**
     * 挑战记录new
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/challengeRecord/mySelf/list")
    public Result<PPageUtils<ChallengePKRecordDto>> challengeRecordMySelfList(@RequestBody ChallengePKQueryDto queryDto) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(pkChallengeBizService.challengeRecordMySelfList(queryDto, loginUser.getId()));
    }


    /**
     * 发起挑战自己记录
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/pkMySelf")
    public Result<ChallengeMySelfPKRespDto> pkMySelf(@RequestBody ChallengeMySelfPKDto queryDto) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(pkChallengeBizService.selfPkChallengeRecordService(queryDto, loginUser));
    }

}

