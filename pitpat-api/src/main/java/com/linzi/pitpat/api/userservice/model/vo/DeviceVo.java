package com.linzi.pitpat.api.userservice.model.vo;

import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 设备信息
 */
@Data
public class DeviceVo {

    /**
     * 用户设备id
     *
     * @see ZnsUserEquipmentEntity#id
     */
    private Long id;

    /**
     * 设备类型，1：跑步机，2：走步机 3：走跑一体 40：划船机  20:动感单车 30：脚踏车 ,101：心率臂带、102：心率肩
     *
     * @see DeviceConstant.EquipmentTypeEnum
     */
    private Integer equipmentType;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 设备品牌
     */
    private String equipmentBrand;

    /**
     * 设备型号
     */
    private String equipmentModel;

    /**
     * 蓝牙mac
     */
    private String equipmentAddress;


    /**
     * 设备外观图
     */
    private String appearanceDrawing;

    /**
     * 最近连接时间
     */
    private ZonedDateTime connectTime;

    /**
     * 设备uuid
     */
    private String uuid;

    /**
     * 【4.3新增】质保结束时间
     */
    private ZonedDateTime qualityEndTime;

    /**
     * 【4.3新增】额外质保列表状态，none: 无，toSubmit：待领取 finished：已完成，waiting：待审核，reject：审核拒绝
     *
     * @see DeviceConstant.AuditListStatusEnum
     */
    private String auditStatus;

    /**
     * 【4.3新增】额外质保审核完成提示（已获得90天额外质保）
     */
    private String finishedMsg;

    /**
     * 【4.3新增】激活状态，0：无需激活，1：已激活，2：未激活
     *
     * @see DeviceConstant.ActivateStatusEnum
     */
    private Integer activateStatus;

    /**
     * 【4.3新增】整机序列号
     */
    private String printId;

    /**
     * 【4.3新增】是否显示注册按钮，true：显示，false：不显示
     */
    private Boolean showRegisterButton;

}
