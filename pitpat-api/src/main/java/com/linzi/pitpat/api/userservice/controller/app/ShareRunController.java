package com.linzi.pitpat.api.userservice.controller.app;



import java.time.ZonedDateTime;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.response.ShareReportResponseDto;
import com.linzi.pitpat.data.activityservice.manager.api.UserRunDataReportManager;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunRecordEntity;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsMileageService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.entity.app.AbTestConfig;
import com.linzi.pitpat.data.enums.AbTestTypeEnum;
import com.linzi.pitpat.data.enums.UnitEnum;
import com.linzi.pitpat.data.query.AbTestConfigQuery;
import com.linzi.pitpat.data.request.runData.ShareRequest;
import com.linzi.pitpat.data.resp.ShareConfigVo;
import com.linzi.pitpat.data.resp.ShareResp;
import com.linzi.pitpat.data.service.app.AbTestConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.runData.HeartRateChartData;
import com.linzi.pitpat.data.vo.runData.PaceRateChartData;
import com.linzi.pitpat.data.vo.runData.RunningReportVo;
import com.linzi.pitpat.data.vo.runData.ShareDataVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 单次数据报告-分享
 */
@RestController
@RequestMapping({"/app/share/run", "/h5/share/run"})
public class ShareRunController extends BaseAppController {
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    @Resource
    private UserRunDataReportManager userRunDataReportManager;
    @Resource
    private MainActivityBizService mainActivityBizService;
    @Autowired
    private ZnsRunActivityService znsRunActivityService;
    @Autowired
    private MindUserMatchService mindUserMatchService;

    @Autowired
    private UserFriendMatchService userFriendMatchService;

    @Autowired
    private ZnsUserAccountDetailService znsUserAccountDetailService;

    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    @Autowired
    private ZnsUserRunRecordService userRunRecordService;
    @Autowired
    private ZnsCourseService znsCourseService;
    @Autowired
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Autowired
    private ZnsUserRunDataDetailsMileageService userRunDataDetailsMileageService;
    @Autowired
    private AbTestConfigService abTestConfigService;
    @Autowired
    private ZnsUserService znsUserService;
    @Autowired
    private ZnsUserAccountService znsUserAccountService;
    @Autowired
    private MainActivityService activityService;

    /**
     * 跑步
     *
     * @param req
     * @return
     */
    @PostMapping("/data")
    public Result<ShareResp> runningReport(@RequestBody ShareRequest req) {
        ShareResp resp = new ShareResp();
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.share_report_data_config.getCode());
        ShareConfigVo vo = JsonUtil.readValue(sysConfig.getConfigValue(), ShareConfigVo.class);
        BigDecimal award = BigDecimal.ZERO;
        // 交易类型: 6:组队跑奖励,7:挑战跑奖励,8:排行赛奖励 ,9 官方组队奖励，10 官方累计奖励 12:新人多人pk
        // 活动类型：-1 目标跑/课程跑/训练营,1 非官方多人同跑  2 挑战跑 3 排行赛 4 官方组队跑 5 里程碑 6 新人福利活动
        ZnsUserRunDataDetailsEntity dataDetailsEntity = znsUserRunDataDetailsService.findById(req.getUserRunDataDetailsId());
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(dataDetailsEntity.getActivityId());
        ZnsUserEntity znsUserEntity = znsUserService.findById(req.getUserId());

        ZnsRunActivityUserEntity activityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(dataDetailsEntity.getActivityId(), req.getUserId());
        if (activityUserEntity != null) {
            resp.setCurrentDate(activityUserEntity.getCompleteTime());              //设置完赛时间
        }
        //老的活动类型 3.0之前
        if (znsRunActivityEntity != null && Arrays.asList(1, 2, 3, 4, 5, 6, 12).contains(znsRunActivityEntity.getActivityType())) {
            resp.setActivityType(znsRunActivityEntity.getActivityType());
            resp.setActivityState(znsRunActivityEntity.getActivityState());
            resp.setTitle(znsRunActivityEntity.getActivityTitle());
            Integer tradeType = znsRunActivityEntity.getActivityType() + 5;
            resp.setRank(activityUserEntity.getRank());
            if (Objects.equals(znsRunActivityEntity.getActivityType(), 1)) {
                resp.setTitle(vo.getFeiMultipleRunTitle());
            } else if (Objects.equals(znsRunActivityEntity.getActivityType(), 2)) {
                UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(dataDetailsEntity.getActivityId());
                MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByActivityIdUserId(dataDetailsEntity.getActivityId(), req.getUserId());
                if (userFriendMatch != null) {
                    resp.setTitle(vo.getFriendBattleTitle());
                } else if (mindUserMatch != null) {
                    resp.setTitle(vo.getRandomMatchingTitle());
                } else {
                    // 设置标题
                    resp.setTitle(BigDecimalUtil.divide(dataDetailsEntity.getDistanceTarget(), new BigDecimal(1600)) + vo.getCommonPkTitle());
                }
                if (dataDetailsEntity.getDistanceTarget() != null && dataDetailsEntity.getRunMileage() != null &&
                        dataDetailsEntity.getDistanceTarget().compareTo(dataDetailsEntity.getRunMileage()) > 0) {
                    resp.setIsFinished(0);               //如果没有跑完
                    resp.setRank(2);               //如果没有跑完,则当成失败处理
                }
            } else if (Objects.equals(znsRunActivityEntity.getActivityType(), 3)) {
                ZnsUserRunRecordEntity runRecordEntity = userRunRecordService.selectByRunDetailsId(req.getUserRunDataDetailsId());
                resp.setRank(runRecordEntity.getRank());
                Long challengedUserId = Objects.equals(runRecordEntity.getUserId(), req.getUserId())
                        ? runRecordEntity.getChallengedUserId() : runRecordEntity.getUserId();                    //设置
                resp.setChallengedUserId(challengedUserId);
                // 表示挑战, isChallengeSuccess 是否挑战成功，0：失败，1：成功
                if (runRecordEntity.getChallengedUserId() != null && runRecordEntity.getChallengedUserId() > 0) {
                    resp.setRank(Objects.equals(runRecordEntity.getIsChallengeSuccess(), 0) ? 2 : 1);
                }
                // 用户是否完成:0表示未完成，1表示完成
                resp.setActivityState(Objects.equals(runRecordEntity.getIsComplete(), 1) ? 2 : 1);
                resp.setCurrentDate(runRecordEntity.getCompleteTime());              //设置完赛时间
            } else if (Objects.equals(znsRunActivityEntity.getActivityType(), 4)) {
                resp.setIsPlusMember(znsUserEntity.getVipType() == 3 ? 1 : 0);
                AbTestConfig one = abTestConfigService.findOne(AbTestConfigQuery.builder().configKey(AbTestTypeEnum.OFFICIAL_TEAM_RUN_report.getCode()).userId(req.getUserId()).build());
                if (Objects.nonNull(one)) {
                    //官方多人同跑报告页ABtest
                    resp.setReportTest(one.getValue());
                }
            }
            // 如果是排行赛事，则用details_id 从account中取值
            // 交易类型: 比如1:保证金,2:提现,3:提现服务费,4:提现税费,5:充值,6:组队跑奖励,7:挑战跑奖励,8:排行赛奖励 ,9 官方组队奖励，10 官方累计奖励 ,11 2D跑步彩蛋奖励
            // 12：新人福利 13：分销佣金 14 费用,15 3D跑步彩蛋奖励 16  3D鼓掌奖励 17 调查问卷奖励 18邀请好友 19一周快乐跑 20优惠券 21世界杯打卡
            if (znsRunActivityEntity.getActivityType() == 12) {
                award = znsUserAccountDetailService.selectSumAward(dataDetailsEntity.getActivityId(), req.getUserId(), null, 2);
            } else {
                award = znsUserAccountDetailService.selectAccountByActivityIdUserIdTradeType(
                        Objects.equals(znsRunActivityEntity.getActivityType(), 3) ? dataDetailsEntity.getId() : dataDetailsEntity.getActivityId(),
                        req.getUserId(), Arrays.asList(tradeType, 12));
            }
        } else {
            if (dataDetailsEntity.getDistanceTarget() != null && dataDetailsEntity.getDistanceTarget().compareTo(BigDecimal.ZERO) > 0) {
                resp.setActivityType(-1);                   // 目标跑
            } else if (dataDetailsEntity.getCourseId() != null && dataDetailsEntity.getCourseId() > 0) {
                resp.setActivityType(-2);                   // 课程跑
                ZnsCourseEntity courseEntity = znsCourseService.selectById(dataDetailsEntity.getCourseId());
                resp.setTitle(courseEntity != null ? courseEntity.getCourseName() : "");
                resp.setCourseType(courseEntity != null ? courseEntity.getCourseType() : 0); // 0：变速跑，1：视频
            } else {
                resp.setActivityType(-1);                   // 自由跑
            }
        }

        if (resp.getCurrentDate() == null && Objects.equals(dataDetailsEntity.getRunStatus(), 1)) {            //对于那些没有跑完的处理
            resp.setCurrentDate(DateUtil.convertZonedDateTime2Date(dataDetailsEntity.getLastTime()));              //设置完成比赛时间
        }

        //所有活动都要返回客服id
        if (Objects.equals(znsUserEntity.getMemberType(), 0)) {
            SysConfig chatConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.chat_robot_user_ids.getCode());
            //填充普通聊天客服
            if (Objects.nonNull(chatConfig)) {
                String configValue = chatConfig.getConfigValue();
                String[] split = configValue.split(",");
                String chatRobotUserId = split[0];
                ZnsUserEntity customer = znsUserService.findById(Long.parseLong(chatRobotUserId));
                resp.setCustomerId(Long.parseLong(chatRobotUserId));
                resp.setCustomerName(customer.getFirstName());
            }
        }

        ZnsUserAccountEntity userAccount = znsUserAccountService.getUserAccount(znsUserEntity.getId());

        if (Objects.nonNull(userAccount)) {
            resp.setCurrency(I18nConstant.buildCurrency(userAccount.getCurrencyCode()));
        }
        resp.setAward(award);
        resp.setImageList(vo.getImageList());

        //如果查看的是系列赛的总数据，activityId填写了，且类型是系列赛.对数据进行覆盖
        boolean isSeries = false;
        if (req.getActivityId() != null) {
            MainActivity mainActivity = activityService.findById(req.getActivityId());
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(mainActivity.getMainType())) {
                isSeries = true;
            }
        }
        if (isSeries) {
            ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(req.getActivityId(), getZoneId());
            ZnsUserEntity userInfo = znsUserService.findById(req.getUserId());
            userInfo.setMeasureUnit(0);
            RunningReportVo detail = userRunDataReportManager.runningReportSeries(userInfo, activityNew.getMainActivity());
            PaceRateChartData pace = detail.getPace();
            if (pace != null) {
                resp.setAveragePace(pace.getAveragePace());
            }
            resp.setRunTimeMillisecond(detail.getRunTimeMillisecond());
            resp.setRunMileage(BigDecimal.valueOf(detail.getRunMileage()));
            resp.setRunTime(detail.getRunTime());
            resp.setKilocalorie(detail.getCalorie());
            resp.setStepNum(detail.getStepNum());
            resp.setFatConsumption(detail.getFatConsumption());
            HeartRateChartData heartRate = detail.getHeartRate();
            if (heartRate != null) {
                resp.setAverageHeartRate(heartRate.getAverageHeartRate());
            }
            resp.setActivityId(detail.getActivityId());
        } else {
            PaceRateChartData paceChartMap2 = userRunDataDetailsMileageService.getPaceChartMap2(dataDetailsEntity, 0); //查询用户设置的公里/英里数据
            resp.setAveragePace(paceChartMap2.getAveragePace());
            resp.setRunTimeMillisecond(dataDetailsEntity.getRunTimeMillisecond());
            resp.setRunMileage(dataDetailsEntity.getRunMileage());
            resp.setRunTime(dataDetailsEntity.getRunTime());
            resp.setKilocalorie(dataDetailsEntity.getKilocalorie());
            resp.setStepNum(dataDetailsEntity.getStepNum());
            resp.setFatConsumption(dataDetailsEntity.getFatConsumption());
            resp.setAverageHeartRate(dataDetailsEntity.getAverageHeartRate());
            resp.setActivityId(dataDetailsEntity.getActivityId());

        }
        //分享文案处理


        String dateStr = DateUtil.formatDate(resp.getCurrentDate(), "dd/MM");

        int runTime = resp.getRunTime();
        int hours = runTime / 3600;
        int minutes = (runTime % 3600) / 60;
        StringBuilder runTimeStr = new StringBuilder();
        boolean haveHour = false;
        if (hours > 0) {
            String unitHour = I18nMsgUtils.getMessage("report.share.time.unit.hour");
            runTimeStr.append(hours).append(" ").append(unitHour);
            haveHour = true;
        }
        if (minutes > 0) {
            if (haveHour) {
                runTimeStr.append(" ");
            }
            String unitMin = I18nMsgUtils.getMessage("report.share.time.unit.min");
            runTimeStr.append(minutes).append(" ").append(unitMin);
        }
        StringBuilder mileageStr = new StringBuilder();
        if (UnitEnum.DistanceUnit.KM.getCode() == req.getUnit()) {
            String unitKm = I18nMsgUtils.getMessage("report.share.distance.unit.km");
            String km = resp.getRunMileage().divide(BigDecimal.valueOf(1000), 2, RoundingMode.DOWN).toString();
            mileageStr.append(km).append(" ").append(unitKm);
        } else {
            String unitMi = I18nMsgUtils.getMessage("report.share.distance.unit.mi");
            String mi = resp.getRunMileage().divide(BigDecimal.valueOf(1600), 2, RoundingMode.DOWN).toString();
            mileageStr.append(mi).append(" ").append(unitMi);
        }

        List<String> i18ShareMsg = new ArrayList<>();
        i18ShareMsg.add(I18nMsgUtils.getMessage("report.share.sentence.1", dateStr, runTimeStr.toString(), mileageStr.toString()));
        i18ShareMsg.add(I18nMsgUtils.getMessage("report.share.sentence.2", dateStr, runTimeStr.toString(), mileageStr.toString()));
        i18ShareMsg.add(I18nMsgUtils.getMessage("report.share.sentence.3", dateStr, runTimeStr.toString(), mileageStr.toString()));
        resp.setShareMsg(i18ShareMsg);
        return CommonResult.success(resp);
    }

    /**
     * 走步
     *
     * @param req
     * @return
     */
    @PostMapping("/data/walk")
    public Result<ShareDataVo> walkReport(@RequestBody ShareRequest req) {
        ZnsUserEntity loginUser = getLoginUser();
        ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.findById(req.getUserRunDataDetailsId());
        ShareDataVo data = new ShareDataVo();
        userRunDataDetailsService.setBaseInfo(data, entity, loginUser.getReportFoodType());
        data.setNickname(loginUser.getFirstName());
        data.setHeadPortrait(loginUser.getHeadPortrait());
        data.setUserCode(loginUser.getUserCode());
        data.setCreateTime(loginUser.getCreateTime());
        return CommonResult.success(data);
    }


    /**
     * 跑步报告分享页-大图模式
     *
     * @param req
     * @return
     */
    @PostMapping("/data/report")
    public Result<ShareReportResponseDto> shareReport(@RequestBody ShareRequest req) {
        ShareReportResponseDto data = userRunDataReportManager.shareReport(req.getUserRunDataDetailsId(), req.getActivityId(), getLoginUser().getLanguageCode());
        return CommonResult.success(data);
    }


    public static void main(String[] args) {
        ShareConfigVo vo = new ShareConfigVo();
        List<String> list = new ArrayList<>();

        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/inP13n49MkFE9091.png");  // 默认
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iXe13n49Mwm26239.png");  // 1
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iMu13n49Mx3P9142.png");  // 2

        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iV913n49N5gr9651.png");  // 3
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iwO13n49N5Ab5408.png");  // 4
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/isT13n49N68R3810.png");  // 5


        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iUJ13n49NAb10235.png");  // 6
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iXE13n49NLp42321.png");      // 8

        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iY213n49NM5w6154.png");  // 9
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iMD13n49NY5L9602.png");  //10
        list.add("https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iK913n49NYO14057.png");  // 11


        vo.setImageList(list);
        vo.setCommonPkTitle(" Mile Ranking");
        vo.setRandomMatchingTitle("Random Matching");
        vo.setFriendBattleTitle("Friend Battle");
        vo.setFeiMultipleRunTitle("Multi-Player Running");

        System.out.println(JsonUtil.writeString(vo));
    }

}





