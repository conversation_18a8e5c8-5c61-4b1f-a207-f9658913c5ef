package com.linzi.pitpat.api.mananger;


import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.entity.dto.DelayDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GameRobotManager {

    @Autowired
    private MindUserMatchService mindUserMatchService;

    @Autowired
    private SocketPushUtils socketPushUtils;

    @Autowired
    private ZnsUserService userService;

    @Autowired
    private ZnsRunActivityService runActivityService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${zns.config.rabbitQueue.delay_exchange_name}")
    private String delay_exchange_name;


    //目前只关注pk赛，有需求自己改
    public void noticeRobotOnline(Long activityId) {
        List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchByActivityId(activityId);
        List<MindUserMatch> list = mindUserMatches.stream().filter(k -> k.getIsRobot() == 1).collect(Collectors.toList());
        for (MindUserMatch mindUserMatch : list) {
            ZnsUserEntity user = userService.findById(mindUserMatch.getId());
            socketPushUtils.onlinePush(activityId, user, 1);
        }
    }

    public void startPKActivity(Long activityId, Long timeStamp) {
        ZnsRunActivityEntity activity = runActivityService.findById(activityId);
        if (activity.getActivityState() == 2 || activity.getActivityState() == -1) {
            throw new BaseException("startPKActivity error: Activity state is error,activity id " + activityId + "state" + activityId, activity.getActivityState());
        }

        activity.setActivityStartTime(new Date(timeStamp));
        runActivityService.updateById(activity);

        ZonedDateTime now = ZonedDateTime.now();
        Integer delay = timeStamp - now.toInstant().toEpochMilli() >= 0 ? (int) (timeStamp - now.toInstant().toEpochMilli()) : 0;
        DelayDto delayDto = new DelayDto(Constants.OLD_ACTIVITY_START, activity.getId().toString());
        log.info("当前时间{}，活动开始时间{}", now, activity.getActivityStartTime());
        log.info("startPKActivity delay time {}", delay);

        // 通过广播模式发布延时消息 延时30分钟 持久化消息 消费后销毁 这里无需指定路由，会广播至每个绑定此交换机的队列
        rabbitTemplate.convertAndSend(delay_exchange_name, "", JsonUtil.writeString(delayDto), message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay(delay);
            return message;
        });

    }
}
