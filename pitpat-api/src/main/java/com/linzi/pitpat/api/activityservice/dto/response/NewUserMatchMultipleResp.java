package com.linzi.pitpat.api.activityservice.dto.response;

import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@ToString
public class NewUserMatchMultipleResp {
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 1 距离 2 时间
     */
    private Integer completeType;
    //是否支持音乐(0:支持，1：不支持)
    private Integer isMusic;
    /**
     * title
     */
    private String activityTitle;
    /**
     * routeId
     */
    private Long activityRouteId;
    /**
     * completeType = 1 目标距离
     */
    private BigDecimal targetMileage;
    /**
     * completeType = 2 目标时长
     */
    private Integer targetRunTime;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动类型
     *
     * @see RunActivityTypeEnum
     */
    private Integer activityType;

}
