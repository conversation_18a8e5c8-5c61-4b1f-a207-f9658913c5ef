package com.linzi.pitpat.api.activityservice.controller;


import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.request.MatchUserDto;
import com.linzi.pitpat.api.activityservice.dto.request.NewUserMultipleMatchReq;
import com.linzi.pitpat.api.activityservice.dto.response.NewUserMatchMultipleResp;
import com.linzi.pitpat.api.userservice.manager.NewUserMultipleBusiness;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.SpeedUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.biz.NewPersonPkBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.manager.ActivityMessageManager;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.manager.MindUserMatchManager;
import com.linzi.pitpat.data.activityservice.manager.RunActivityProcessManager;
import com.linzi.pitpat.data.activityservice.manager.RunRouteManager;
import com.linzi.pitpat.data.activityservice.manager.api.RunActivityUserManager;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPlaylistRel;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatchRule;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatchScore;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.request.MindUserMatchRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.FriendPKAwardConfig;
import com.linzi.pitpat.data.activityservice.model.vo.MindUserMatchVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkConfigVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchRuleService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchScoreService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.constants.CacheConstants;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.engine.match.BaseMatchHandler;
import com.linzi.pitpat.data.entity.dto.RunPlanDto;
import com.linzi.pitpat.data.enums.ActivityMusicSupportEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.SocketEventEnums;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.robotservice.biz.RobotBizService;
import com.linzi.pitpat.data.robotservice.manager.RobotRunPlanManager;
import com.linzi.pitpat.data.robotservice.mapper.RobotRunPlanDao;
import com.linzi.pitpat.data.robotservice.model.domain.RobotQuery;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunPlan;
import com.linzi.pitpat.data.robotservice.service.RobotRunPlanService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.mapper.SysUserMapper;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.SysUser;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.UserMatchModeRecord;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.vo.UserFriendMatchPushVo;
import com.linzi.pitpat.data.vo.UserFriendMatchVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 智能匹配
 */
@RestController
@RequestMapping({"/app/mind", "/h5/mind"})
@Slf4j
@RequiredArgsConstructor
public class MindUserMatchController extends BaseAppController {
    public static final int USER_WAIT_SECOND = 10;
    final static Random random = new Random();
    @Autowired
    private MindUserMatchService mindUserMatchService;
    @Resource
    private MindUserMatchManager mindUserMatchManager;
    @Resource
    private MindUserMatchBizService mindUserMatchBizService;
    @Autowired
    private MindUserMatchRuleService mindUserMatchRuleService;
    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private ZnsUserService znsUserService;
    @Autowired
    private ActivityStrategyContext activityStrategyContext;
    @Autowired
    private SocketPushUtils socketPushUtils;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private UserFriendMatchService userFriendMatchService;
    @Autowired
    private RobotRunPlanService robotRunPlanService;
    @Autowired
    private ZnsRunRouteService znsRunRouteService;
    @Autowired
    private ZnsUserAddService znsUserAddService;

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Value("${admin.server.gamepush}")
    private String gamepush;

    @Autowired
    private MindUserMatchScoreService mindUserMatchScoreService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;
    @Autowired
    private ActivityMessageManager activityMessageManager;
    @Autowired
    private PkChallengeRecordService pkChallengeRecordService;
    @Resource
    private RunActivityUserManager runActivityUserManager;
    @Resource
    private RunRouteManager runRouteManager;
    @Resource
    private NewPersonPkBizService newPersonPkBizService;
    @Resource
    private NewUserMultipleBusiness newUserMultipleBusiness;
    @Resource
    private ActivityPlaylistRelService activityPlaylistRelService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RobotBizService robotBizService;
    @Resource
    private RunActivityBizService runActivityBizService;
    @Resource
    private AwardActivityManager awardActivityManager;
    @Resource
    private RunActivityProcessManager runActivityProcessManager;
    @Autowired
    private RobotRunPlanDao robotRunPlanDao;
    @Autowired
    private RobotRunPlanManager robotRunPlanManager;

    /**
     * 查询未完赛的pk赛数量
     *
     * @param
     * @return
     */
    @PostMapping("/queryNoFinishPKActivityCount")
    public Result<Long> queryNoFinishPKActivityCount() {
        ZnsUserEntity loginUser = getLoginUser();
        ZonedDateTime now = ZonedDateTime.now();
        List<ZnsRunActivityEntity> list = znsRunActivityService.queryFriendPKPlan(loginUser, RunActivityTypeEnum.CHALLENGE_RUN, DateUtil.addDays(now, -30), DateUtil.addDays(now, 30));
        return CommonResult.success(list.stream().filter(k -> k.getActivityState() == 0 || k.getActivityState() == 1).count());

    }


    /**
     * 查询用户好友pk行程 范围 now - 0.5h -> now + 0.5h + 7d
     *
     * @param
     * @return
     */
    @PostMapping("/queryFriendPKPlan")
    public Result<List<ZonedDateTime>> queryFriendPKPlan() {
        ZnsUserEntity loginUser = getLoginUser();
        ZonedDateTime now = ZonedDateTime.now();
        List<ZnsRunActivityEntity> list = znsRunActivityService.queryFriendPKPlan(loginUser, RunActivityTypeEnum.CHALLENGE_RUN, now.minusMinutes(30),
                now.plusMinutes(7 * 24 * 60 + 30));
        return CommonResult.success(list.stream().filter(k -> Arrays.asList(0, 1).contains(k.getActivityState())).map(ZnsRunActivityEntity::getActivityStartTime).collect(Collectors.toList()));

    }


    /**
     * 返回好友pk奖励配置
     *
     * @param
     * @return
     */
    @PostMapping("/findFriendPKAwardConfig")
    public Result<FriendPKAwardConfig> findFriendPKAwardConfig() {
        FriendPKAwardConfig friendPKAwardConfig = awardActivityManager.getFriendPKAwardConfig();
        return CommonResult.success(friendPKAwardConfig);

    }

    /**
     * 开始智能匹配
     *
     * @param request
     * @return
     */
    @PostMapping("/match")
    public Result<MindUserMatchVo> match(@RequestBody MindUserMatchRequest request) {
        //加锁
        String key = RedisConstants.MIND_MATCH_KEY + request.getUserId();
        request.setAppVersion(getAppVersion());
        RLock lock = redissonClient.getLock(key);
        try {
            if (LockHolder.tryLock(lock, 120, 120)) {
                ZnsUserEntity znsUserEntity = znsUserService.findById(request.getUserId());
                MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByUserIdStatus(znsUserEntity.getId(), 0);
                if (mindUserMatch != null) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("activity.matching"));
                }
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.MIND_MATCH_WAIT_TIME.getCode());
                // 已经匹配成功
                mindUserMatch = new MindUserMatch();
                mindUserMatch.setUserId(znsUserEntity.getId());
                mindUserMatch.setStatus(0);
                String uniqueCode = OrderUtil.getUniqueCode("un");
                mindUserMatch.setTargetMileage(request.getTargetMileage());
                mindUserMatch.setTargetTime(request.getTargetRunTime());
                mindUserMatch.setUniqueCode(uniqueCode);
                mindUserMatchBizService.insertMindUserMatch(mindUserMatch);
                threadMatch(mindUserMatch, znsUserEntity.getId(), request.getTargetMileage(), uniqueCode, request.getMatchType(), request.getTaskId(), request.getCreateSource(), request.getSourceDetails(), request);
                MindUserMatchVo vo = new MindUserMatchVo();
                BeanUtils.copyProperties(mindUserMatch, vo);

                vo.setWaitTime(MapUtil.getInteger(sysConfig.getConfigValue(), 30));
                vo.setPossibleWaitSecond(30);
                return CommonResult.success(vo);
            }
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error"));
    }

    /**
     * 取消匹配
     *
     * @param request
     * @return
     */
    @PostMapping("/cancel/match")
    public Result cancelMatch(@RequestBody MindUserMatch request) {
        return mindUserMatchManager.cancelMatch(request);
    }


    /**
     * 线上未使用
     *
     * @param request
     * @return
     */
    @PostMapping("/run/end")
    public Result runEnd(@RequestBody MindUserMatch request) {
        MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByActivityIdUserIdOrderByIdDesc(request.getActivityId(), request.getUserId());
        RobotRunPlan robotRunPlan = robotRunPlanDao.selectRobotRunPlanByMindUserMatchIdOrderByIdDesc(mindUserMatch.getMindUserMatchId());
        return robotRunPlanManager.runEnd(mindUserMatch.getId(), request.getActivityId(), request.getUserId());
    }

    /**
     * 线上未使用
     *
     * @param request
     * @return
     */
    @PostMapping("/continue/run")
    public Result continueRun(@RequestBody MindUserMatch request) {
        RunPlanDto runPlanDto = new RunPlanDto();
        RobotRunPlan robotRunPlan = robotRunPlanService.selectRobotRunPlanByUserIdActivityIdOrderByIdDesc(request.getUserId(), request.getActivityId());
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(request.getActivityId());
        ZonedDateTime currentDate = ZonedDateTime.now();
        if (robotRunPlan != null) {
            List<RobotRunPlan> list = robotRunPlanService.selectRobotRunPlanByUserIdActivityId(robotRunPlan.getId(), robotRunPlan.getActivityId(), robotRunPlan.getUserId());
            // 已经跑了多少秒
            int runSecond = 0;
            // 0 未结束 ， 1 需要结束
            int isFinished = 0;
            // 还剩下多少秒
            int remainSecond = 0;
            // 已经跑了多少米
            int runMileage = 0;
            // 还剩下多少米
            int remainMileage = 0;
            for (RobotRunPlan plan : list) {
                int planSecond = (plan.getMaxV() - plan.getMinV());
                runMileage += SpeedUtil.mileage(plan.getSpeed(), planSecond); // 总共跑了多少米
            }
            BigDecimal v = BigDecimalUtil.divide(BigDecimalUtil.multiply(robotRunPlan.getSpeed(), new BigDecimal(1000)), new BigDecimal(3600));
            int betwwon = (int) DateUtil.betweenSecond(robotRunPlan.getStartTime(), currentDate) - robotRunPlan.getMinV();
            int alreay = runMileage + new BigDecimal(betwwon).multiply(v).intValue();
            remainMileage = robotRunPlan.getTargetMileage() - alreay;        // 还剩下多少米没有
            runSecond = (int) DateUtil.betweenSecond(znsRunActivityEntity.getActivityStartTime(), currentDate);
            remainSecond = BigDecimalUtil.divide(new BigDecimal(remainMileage), v).intValue() + 1;
            int run = BigDecimalUtil.multiply(new BigDecimal(robotRunPlan.getMaxV() - robotRunPlan.getMinV()), v).intValue() + 1;
            // 之前跑的距离 + 本次跑的距离 > 大于目标距离 ，如果大于 ，同 isFinished = 1
            if (run + runMileage >= robotRunPlan.getTargetMileage()) {
                isFinished = 1;
            }
            ZnsUserEntity znsUserEntity = znsUserService.findById(robotRunPlan.getUserId());


            runPlanDto = new RunPlanDto(runSecond
                    , isFinished, remainSecond, alreay, remainMileage,
                    robotRunPlan.getTargetMileage(), robotRunPlan.getSpeed(), znsUserEntity.getEmailAddressEn()
                    , robotRunPlan.getUserId(), currentDate, null, znsUserEntity.getHeadPortrait(), znsUserEntity.getFirstName(), robotRunPlan.getActivityId(), robotRunPlan.getMindUserMatchId());

        }
        return CommonResult.success(runPlanDto);
    }


    /**
     * 好友匹配/离线pk/新人1V1
     *
     * @param request
     * @return
     * @tag 2.5
     */
    @PostMapping("/friend/match/new")
    public Result friendMatch(@RequestBody UserFriendMatchVo request) {
        Integer appVersion = getAppVersion();
        String key = CacheConstants.FRIEND_MATCH_ + request.getUserId() + request.getBatchNo();
        List<Integer> newPkSubType = RunActivitySubTypeEnum.getNewPersonPkType();
        try {
            if (redissonClient.getBucket(key).trySet("1", 5L, TimeUnit.SECONDS)) {
                ZnsUserEntity matchUser = null;
                //新人引导pk
                if (Objects.nonNull(request.getActivityTypeSub()) && newPkSubType.contains(request.getActivityTypeSub())) {
                    NewPersonPkVo newPersonPkVo = null;
                    if (RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_PK.getType().equals(request.getActivityTypeSub())) {
                        newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(1L);
                    } else if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_RIDE_PK.getType())) {
                        newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(3L);
                    } else if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_ROWING_PK.getType())) {
                        newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(4L);
                    } else {
                        // 默认跑步机
                        newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(1L);
                    }
                    ZnsUserRunDataDetailsEntity details = znsUserRunDataDetailsService.findById(request.getRunDataDetailsId());
                    matchUser = znsUserService.findById(details.getUserId());

                    request.setRunMileage(details.getDistanceTarget());
                    request.setRunTime(details.getTimeTarget());
                    request.setMatchFriendId(matchUser.getId());
                    request.setActivityRouteId(newPersonPkVo.getRouteId().longValue());
                    request.setIsMusic(newPersonPkVo.getIsMusic());
                } else {
                    matchUser = znsUserService.findById(request.getMatchFriendId());
                    MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByUserIdStatusList(request.getMatchFriendId(), Arrays.asList(0, 1));
                    //如果是机器人 ， 并且好友正忙
                    if (Objects.equals(matchUser.getIsRobot(), 1)
                            && !StringUtils.hasText(request.getBatchNo())
                            && (mindUserMatch != null || Objects.equals(matchUser.getRobotCurrStatus(), 1))) {
                        return CommonResult.fail(I18nMsgUtils.getMessage("activity.robot.matching"));
                    }
                    //好友pk
                    if (Objects.nonNull(request.getActivityTypeSub()) && request.getActivityTypeSub() == 2 && StringUtils.isEmpty(request.getBatchNo())) {
                        if (runActivityUserManager.checkFriendMatchUserIds(request.getMatchFriendId())) {
                            return CommonResult.fail(I18nMsgUtils.getMessage("activity.friend.notmatching"));
                        }
                    }
                }
                //离线pk
                if (StringUtils.hasText(request.getBatchNo())) {
                    PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(request.getBatchNo(), 0);
                    request.setRunDataDetailsId(pkChallengeRecord.getRunDataDetailsId());
                    request.setActivityTypeSub(3);
                    if (appVersion < 4000) {
                        Result error = runActivityUserManager.checkEnrollCount(request.getUserId(), request.getMatchFriendId(), 2, 3, true);
                        if (error != null) return error;
                    }

                }
                // 同样也是封装活动创建参数，比如活动标题，
                //       a) ActivityConfigId , 同样也可以根据用户白名单设置路线 ，
                //       b) completeRuleType【完成规则类型：1表示完成跑步里程，2表示完成跑步时长】为2 ，
                //       c) runMileage【 跑步里程(m) 】
                //       d) activityEntryFee 【活动参赛费用】报名费用
                //       e) bonusRuleType【奖金规则类型：1表示免费参加，2表示保证金参加 】 ，根据用户上传的activityEntryFee来指定
                //       f) IsRobotStart 肯定不是机器人启动。
                //       g) 参赛的用户数 ActivityUserIds
                //       h) IsNowStart 活动是否立即启动。
                //       g) RunningGoalsUnit 跑步单位为公里
                //       i) IsPublic 是否是公开的 ，默认是1
                //       j) HasRobot 是否有机器人。
                //       k) Password 支付密码
                //       l) bonusRuleType 奖金规则类型：1表示免费参加，2表示保证金参加 3:费用
                //       m) 创建活动，这里需要注意的是获胜奖励winnerAward的计算 ， 如果 measureUnit【计量单位,0：公里/小时 ，1：英里/小时 】字段是公里，则会到zns_run_activity_config 中找到 completeAwardPerKm 字段 * 当前英里数为他的获胜奖励 ， 如果是英里为单位，则英里 * zns_run_activity_config 表中的 completeAwardPerMiles这个字段计算 获胜利奖励 ，当然如果这两个值 相乘 得到的值大于 zns_run_activity_config的maxAwardLimit字段时，则使用maxAwardLimit值作为最大奖励 。
                RunActivityRequest runActivity = new RunActivityRequest();
                if (Objects.nonNull(request.getEquipmentMainType())) {
                    runActivity.setEquipmentMainType(request.getEquipmentMainType());
                } else {
                    runActivity.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType());
                }
                ZnsUserEntity currentUser = znsUserService.findById(request.getUserId());

                //装填预约开始时间
                runActivity.setAppointmentStartTime(request.getAppointmentStartTime());
                runActivity.setActivityTitle(I18nMsgUtils.getMessage("activity.title.titleByUser", currentUser.getFirstName(), matchUser.getFirstName())); //  活动title
                if (Objects.nonNull(request.getActivityTypeSub()) && newPkSubType.contains(request.getActivityTypeSub())) {
                    if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_PK.getType())) {
                        runActivity.setActivityConfigId(11L);
                        runActivity.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType());
                    } else if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_RIDE_PK.getType())) {
                        runActivity.setActivityConfigId(23L);
                        runActivity.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.BIKE.getType());
                    } else if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_ROWING_PK.getType())) {
                        runActivity.setActivityConfigId(24L);
                        runActivity.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.ROWING.getType());
                    }
                } else {
                    runActivity.setActivityConfigId(9L);
                }
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.RUN_f_V_f_ROUTE.getCode());
                SysConfig sysConfigUserIds = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.RUN_f_V_f_ROUTE_USERIDS.getCode());

                if (Objects.nonNull(request.getActivityRouteId()) && request.getActivityRouteId() >= 0) {
                    runActivity.setActivityRouteId(request.getActivityRouteId());
                } else {
                    if (StringUtil.listcontains(sysConfigUserIds.getConfigValue(), currentUser.getId())) {                        //如果是吴林，则用104
                        runActivity.setActivityRouteId(MapUtil.getLong(sysConfigUserIds.getRemark(), 101l));
                    } else {
                        runActivity.setActivityRouteId(MapUtil.getLong(sysConfig.getConfigValue(), 2l));
                    }
                }
                runActivity.setSpecialEffectTheme(request.getSpecialEffectTheme());
                runActivity.setActivityTypeSub(request.getActivityTypeSub());

                // 离线PK的处理逻辑
                if (StringUtils.hasText(request.getBatchNo())) {
                    //  1 表示挑战他人， 2 表示邀请他人挑战自己
                    // 由我发起的挑战，标题最后应该是by   you
                    runActivity.setActivityTitle(I18nMsgUtils.getMessage("activity.pkRun.title")); // 在活动展示的时候显示
                    // 版本兼容地图旧离线pk 固定101
                    if (getAppVersion() < 40500) {
                        runActivity.setActivityRouteId(101l);
                    }
                    runActivity.setChallengeRunDataDetailsId(request.getRunDataDetailsId());
                    runActivity.setActivityTypeSub(3);
                    request.setActivityTypeSub(3);//前端不一定传，这里做一定补偿
                    //4.0后区分新老离线pk
                    if (appVersion >= 4000) {
                        runActivity.setIsNewPk(YesNoStatus.YES.getCode());
                    }
                } else if (newPkSubType.contains(request.getActivityTypeSub())) {
                    runActivity.setChallengeRunDataDetailsId(request.getRunDataDetailsId());
                    runActivity.setActivityTitle(I18nMsgUtils.getMessage("activity.pkRun.titleByYou", currentUser.getFirstName())); //  活动title "PK Run initiated by " + currentUser.getFirstName()
                }

                if (Objects.nonNull(request.getRunMileage()) && request.getRunMileage().compareTo(BigDecimal.ZERO) > 0) {
                    runActivity.setCompleteRuleType(1); //完成规则类型：1 表示完成跑步里程，2表示完成跑步时长
                    runActivity.setRunningGoalsUnit(1);//跑步目标单位，0：公里 ，1：英里，2：时间（min）
                } else if (Objects.nonNull(request.getRunTime()) && request.getRunTime() > 0) {
                    runActivity.setCompleteRuleType(2); //完成规则类型：1 表示完成跑步里程，2表示完成跑步时长
                    runActivity.setRunningGoalsUnit(2);
                }
                runActivity.setRunMileage(request.getRunMileage());
                runActivity.setRunTime(new BigDecimal(request.getRunTime()));
                runActivity.setActivityEntryFee(request.getActivityEntryFee());
                runActivity.setBonusRuleType(request.getActivityEntryFee().compareTo(BigDecimal.ZERO) > 0 ? 2 : 1);//奖金规则类型：1表示免费参加，2表示保证金参加
                runActivity.setIsRobotStart(0);//是否机器人发起: 0表示非机器人发起,1表示机器人发起
                runActivity.setActivityUserIds(Arrays.asList(matchUser.getId()));
                runActivity.setIsNowStart(0);//活动开始时间是否是现在: 1表示现在发起活动，0表示不是现在发起活动
                runActivity.setIsPublic(1);
                runActivity.setHasRobot(matchUser.getIsRobot());
                runActivity.setPassword(request.getPassword());
                if (Objects.nonNull(request.getRunningGoalsUnit())) {
                    runActivity.setRunningGoalsUnit(request.getRunningGoalsUnit());//跑步目标单位，0：公里 ，1：英里，2：时间（min）
                }
                request.setBonusRuleType(runActivity.getBonusRuleType());
                ZonedDateTime date = ZonedDateTime.now();
                ZonedDateTime currentDate = DateUtil.formateDate(date.plusMinutes(1), DateUtil.YYYY_MM_DD_HH_MM);
                ZonedDateTime activityStartTime = currentDate.plusMinutes(sysConfigService.selectActivityStartMinite(currentUser.getId()));

                // 11:06:37点击立即挑战，则比赛开始时间=11:07:00    现在的逻辑又加了一分钟
                // 11:06:50点击立即挑战，则比赛开始时间=11:08:00
                if (Objects.equals(request.getActivityTypeSub(), 3)) {
                    if (DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType().equals(request.getEquipmentMainType())) {
                        int second = DateUtil.betweenSecond(ZonedDateTime.now(), currentDate);
                        if (second <= 10) {
                            activityStartTime = DateUtil.formateDate(ZonedDateTime.now().plusMinutes(2), DateUtil.YYYY_MM_DD_HH_MM);
                        } else {
                            activityStartTime = currentDate;
                        }
                    } else {
                        activityStartTime = date;
                    }
                } else if (newPkSubType.contains(request.getActivityTypeSub())) {
                    activityStartTime = DateUtil.addSeconds(ZonedDateTime.now(), 10);
                }

                runActivity.setActivityStartTime(activityStartTime.toInstant().toEpochMilli());
                runActivity.setAppVersion(getAppVersion());
                runActivity.setCreateSource(request.getCreateSource());

                if (Objects.nonNull(runActivity.getAppointmentStartTime())) {
                    runActivity.setActivityStartTime(request.getAppointmentStartTime().toInstant().toEpochMilli());
                }

                Result result = activityStrategyContext.doLaunchActivity(runActivity, currentUser);

                if (CommonError.SUCCESS.getCode().equals(result.getCode()) && runActivity.getRobotAcceptSuccess() == 1) {
                    Map<String, Object> data = (Map<String, Object>) result.getData();
                    Long activityId = MapUtil.getLong(data.get("activityId"), -1L);
                    BigDecimal awardAmount = MapUtil.getBigDecimal(data.get("awardAmount"), BigDecimal.ZERO);
                    // 更新开始时间 ，因为这个才是整点的
                    ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
                    request.setActivityId(znsRunActivityEntity.getId());

                    request.setActivityStartTime(znsRunActivityEntity.getActivityStartTime());
                    request.setAwardAmount(awardAmount);
                    if (newPkSubType.contains(request.getActivityTypeSub())) {
                        NewPersonPkVo newPersonPkVo = null;
                        if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_PK.getType())) {
                            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(1L);
                        } else if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_RIDE_PK.getType())) {
                            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(3L);
                        } else if (request.getActivityTypeSub().equals(RunActivitySubTypeEnum.NEW_PERSON_GUIDANCE_ROWING_PK.getType())) {
                            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(4L);
                        } else {
                            // 默认跑步机
                            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(1L);
                        }
                        if (newPersonPkVo.getIsMusic().equals(0)) {
                            //歌单list id 绑定活动id
                            List<Long> musicList = newPersonPkVo.getMusicList();
                            if (!CollectionUtils.isEmpty(musicList)) {
                                musicList.forEach(i -> {
                                    ActivityPlaylistRel rel = new ActivityPlaylistRel();
                                    rel.setActivityId(activityId);
                                    rel.setPlaylistId(i);
                                    activityPlaylistRelService.save(List.of(rel));
                                });
                            }
                            znsRunActivityEntity.setMusicSupport(ActivityMusicSupportEnum.SUPPORT.getCode());
                            znsRunActivityService.updateById(znsRunActivityEntity);
                        }
                    }
                    if (Objects.equals(request.getActivityTypeSub(), 3)) {
                        runActivityUserManager.createShadowData(znsRunActivityEntity, request.getBatchNo());
                        // 挑战者发送消息
                        Map<String, Object> params = new HashMap<>();
                        params.put("batchNo", request.getBatchNo());
                        params.put("targetMile", Constants.target_1_Mile);
                        params.put("activityId", activityId);
                        Map<String, Object> replace = new HashMap<>();
                        PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(request.getBatchNo(), 1);
                        PkChallengeRecord wasChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(request.getBatchNo(), 0);
                        ZnsUserEntity znsUserEntity = znsUserService.findById(pkChallengeRecord.getUserId());
                        ZnsUserEntity wasUserEntity = znsUserService.findById(wasChallengeRecord.getUserId());

                        if (Objects.equals(pkChallengeRecord.getChallengeType(), 0)) {
                            replace.put("wasChallengeName", znsUserEntity.getFirstName());
//                            activityMessageManager.sendPkChallengeIm(wasUserEntity.getId(), Constants.invite_challenge_you_accept, replace, params, "lznative://main/offlinepkinvalid", activityId);
                        } else {
                            replace.put("challengeName", znsUserEntity.getFirstName());
//                            activityMessageManager.sendPkChallengeIm(wasUserEntity.getId(), Constants.challenge_you, replace, params, "lzrn://Race/BattleRaceDetailPage", activityId);
                        }
                    }

                    // 自动同意，所以直接向mindusermatch表加入数据即可
                    if (Objects.equals(matchUser.getIsRobot(), 1) &&
                            !newPkSubType.contains(request.getActivityTypeSub()) &&
                            !Objects.equals(request.getActivityTypeSub(), RunActivitySubTypeEnum.OFFLINE_PK_MATCHING.getType())) { // 离线pk，机器人 机器人不能参加上线操作
                        ZonedDateTime activityEndTime = DateUtil.addHours(ZonedDateTime.now(), 24);
                        MindUserMatch m = buildMindMatch(matchUser.getId(), 1, 1, matchUser.getId(), activityEndTime
                                , znsRunActivityEntity.getRunMileage(), znsRunActivityEntity.getId(), getModeByUserId(), znsRunActivityEntity.getRunTime());
                        m.setMatchUserId(currentUser.getId());
                        mindUserMatchBizService.insertMindUserMatch(m);

                        MindUserMatch n = buildMindMatch(currentUser.getId(), 1, 0, m.getId(), activityEndTime
                                , znsRunActivityEntity.getRunMileage(), znsRunActivityEntity.getId(), "", znsRunActivityEntity.getRunTime());
                        n.setMatchUserId(matchUser.getId());
                        mindUserMatchBizService.insertMindUserMatch(n);

                        m.setMindUserMatchId(n.getId());
                        mindUserMatchBizService.updateMindUserMatchById(m);

                        // 机器人在线或离线推送
                        socketPushUtils.onlinePush(activityId, matchUser, 1);

                        // 将机器人添加到房间
                        GamePushUtils.addRobot(gamepush, znsRunActivityEntity.getId(), znsRunActivityEntity.getActivityRouteId(), matchUser, znsRunActivityEntity.getId(), m.getId());

                    }
                    if (Objects.nonNull(request.getRunningGoalsUnit())) {
                        request.setRunningGoalsUnit(request.getRunningGoalsUnit());//跑步目标单位，0：公里 ，1：英里，2：时间（min）
                    }
                    setRunningGoalUnit(request);
                    return userFriendMatchService.friendMatch(request);
                }
                // 机器人自动接受失败,不返回错误
                if (CommonError.SUCCESS.getCode().equals(result.getCode()) && runActivity.getRobotAcceptSuccess() == 0) {
//                    result.setMsg("Your friend is busy,try to invite other friends");
                    Map<String, Object> data = (Map<String, Object>) result.getData();
                    Long activityId = MapUtil.getLong(data.get("activityId"), -1L);
                    BigDecimal awardAmount = MapUtil.getBigDecimal(data.get("awardAmount"), BigDecimal.ZERO);
                    // 更新开始时间 ，因为这个才是整点的
                    ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
                    request.setActivityId(znsRunActivityEntity.getId());
                    request.setActivityStartTime(znsRunActivityEntity.getActivityStartTime());
                    request.setAwardAmount(awardAmount);
                    request.setStatus(0);
                    request.setBonusRuleType(2);
                    request.setIsPop(0);

                    UserFriendMatchPushVo vo = userFriendMatchService.buildUserFriendMatchPushVo(request);
                    vo.setIsMusic(request.getIsMusic());
                    if (Objects.equals(request.getIsMusic(), 0)) {
                        vo.setPlayList(request.getPlayList());
                    }
                    return CommonResult.success(vo);
                }
                return CommonResult.fail(result.getMsg());
            } else {
                return CommonResult.fail(I18nMsgUtils.getMessage("common.operate.repeat"));
            }
        } catch (Exception e) {
            log.error("异常", e);
        }
        return CommonResult.fail(I18nMsgUtils.getMessage("common.params.unknown"));
    }

    private void friendMatchCheckVersion(UserFriendMatchVo request) {
        if (Objects.equals(request.getActivityTypeSub(), 2)) {
            Integer appVersion = getAppVersion();
            if (appVersion < 3060) {
                ZnsUserEntity user = znsUserService.findById(request.getMatchFriendId());
                if (Objects.nonNull(user) && Objects.equals(user.getIsRobot(), 0)) {
                    if (user.getAppVersion() >= 3060) {
                        throw new BaseException(I18nMsgUtils.getMessage("service.in.upgrade"));
                    }
                }
            }
        }

    }

    private void setRunningGoalUnit(UserFriendMatchVo request) {
        if (Objects.nonNull(request.getRunTime()) && request.getRunTime() > 0) {
            request.setRunningGoalsUnit(2);
            return;
        }
        if (Objects.nonNull(request.getRunningGoalsUnit())) {
            return;
        }
        request.setRunningGoalsUnit(1);
    }

    private String getModeByUserId() {
        List<String> list = Arrays.asList("S+", "S", "A", "B", "C", "D", "E1", "E2", "E3", "E4", "E5", "E6");
        Random random = new Random();
        int randomNex = random.nextInt(list.size());
        log.info("模式随机数为 ：" + randomNex);
        return list.get(randomNex);
    }

    public MindUserMatch buildMindMatch(Long userId, Integer status, Integer isRobot, Long mindUserMatchId, ZonedDateTime activityEndTime, BigDecimal runMileage, Long activityId, String runMode, Integer runTime) {
        MindUserMatch mindUserMatch = new MindUserMatch();
        mindUserMatch.setUserId(userId);
        mindUserMatch.setStatus(status);
        mindUserMatch.setIsRobot(isRobot);
        mindUserMatch.setMindUserMatchId(mindUserMatchId);
        mindUserMatch.setActivityEndTime(activityEndTime);
        mindUserMatch.setTargetMileage(runMileage.intValue());
        mindUserMatch.setTargetTime(runTime.intValue());
        mindUserMatch.setActivityId(activityId);
        mindUserMatch.setRunMode(runMode);
        String uniqueCode = OrderUtil.getUniqueCode("un");
        mindUserMatch.setUniqueCode(uniqueCode);
        return mindUserMatch;
    }

    /**
     * 线上未使用
     *
     * @param request
     * @return
     */
    @PostMapping("/friend/popfinished")
    public Result popfinished(@RequestBody UserFriendMatchVo request) {
        userFriendMatchService.updateUserFriendMatchIsPopById(ZonedDateTime.now(), 1, request.getId());
        return CommonResult.success();
    }

    /**
     * 线上未使用
     *
     * @param request
     * @return
     */
    @PostMapping("/friend/approval")
    public Result approval(@RequestBody UserFriendMatchVo request) {
        ZnsUserEntity znsUserEntity = getLoginUser();
        if (znsUserEntity != null) {
            request.setUserId(znsUserEntity.getId());
        }
        userFriendMatchService.approval(request);
        return CommonResult.success();
    }

    /**
     * 线上未使用
     *
     * @param request
     * @return
     */
    @PostMapping("/friend/success")
    public Result success(@RequestBody UserFriendMatchVo request) {
        UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchById(request.getId());
        userFriendMatch.setStatus(1);
        userFriendMatch.setActivityId(userFriendMatch.getActivityId());
        userFriendMatchService.updateUserFriendMatchById(userFriendMatch);
        return CommonResult.success();
    }

    public void threadMatch(MindUserMatch mindUserMatch, Long userId, Integer targetMileage, String uniqueCode, Integer matchType, Long taskId, Integer createSource, String sourceDetails, MindUserMatchRequest request) {
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        new Thread(new Runnable() {
            @Override
            public void run() {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                try {
                    Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                    doMatch(mindUserMatch, userId, targetMileage, uniqueCode, matchType, taskId, createSource, sourceDetails, request);
                } catch (Exception e) {
                    log.error("异常", e);
                }
            }
        }).start();
    }


    public void doMatch(MindUserMatch mindUserMatchOrigin, Long userId, Integer targetMileage, String uniqueCode, Integer matchType, Long taskId, Integer createSource, String sourceDetails, MindUserMatchRequest request) {
        List<MindUserMatchRule> mindUserMatchRuleList = mindUserMatchRuleService.selectMindUserMatchRuleIsDefaultStatusGroupByRuleHandler(0, 1);
        // 请求锁超时时间，毫秒
        long timeout = USER_WAIT_SECOND * 1000;
        // 系统当前时间，毫秒
        long nowTime = System.currentTimeMillis();
        Long maxId = 0l;
        List<MatchUserDto> matchUserDtos = new ArrayList<>();
        //只匹配机器人时不匹配真人
        if (Objects.isNull(matchType) || matchType != 1) {
            while ((System.currentTimeMillis() - nowTime) < timeout) {
                MindUserMatch m = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(userId, uniqueCode);   // 表示已经匹配成功
                if (m != null && Arrays.asList(1, -1).contains(m.getStatus())) {
                    log.info(" 用户已经匹配成功 userId = " + m.getUserId() + ", status = " + m.getStatus());
                    return;
                }
                log.info("开始匹配真实用户 ");
                List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchStatusTargetMileageUserId(maxId, 0, targetMileage, userId, request.getTargetRunTime());
                for (MindUserMatch mindUserMatch : mindUserMatches) {
                    if (isContains(matchUserDtos, mindUserMatch.getUserId())) {
                        continue;
                    }
                    // 花10 秒时间进行匹配，随机匹配优先匹配真实用户 。
                    //       a) 随机匹配分数计算
                    //       b) 两个用户的平均跑步时间相差越近，他的分数越高。
                    //       c) 7天(含)内有一起1v1过：2分 , 7-14(含)有一起1v1过：4分, 14-21(含)天有一起1v1过：6分, 21-30(含)天有一起1v1过：8分 , 30天以内没有一起1v1过：10分 , 也就是说，两个人 1 v 1 相差时间越长，分数越高。
                    //       d ) 机器人之间的匹配规则 7天(含)内有一起1v1过：2分 , 7-14(含)有一起1v1过：4分, 14-21(含)天有一起1v1过：6分, 21-30(含)天有一起1v1过：8分 , 30天以内没有一起1v1过：10分 , 也就是说，两个人 1 v 1 相差时间越长，分数越高
                    BigDecimal sumScore = calScore(mindUserMatchRuleList, userId, mindUserMatch.getUserId(), targetMileage);
                    // 如果分数大于0
                    if (sumScore.compareTo(BigDecimal.ZERO) > 0) {
                        matchUserDtos.add(new MatchUserDto(mindUserMatch.getUserId(), mindUserMatch.getUniqueCode(), sumScore));
                    }
                }
                // 每次请求等待一段时间
                try {
                    TimeUnit.MILLISECONDS.sleep(random.nextInt(1000) + 500);
                } catch (InterruptedException e) {
                    log.info("获取分布式锁休眠被中断：", e);
                }
            }
        }

        if (matchUserDtos.size() > 0) {                     //表明有匹配成功的数据
            Collections.sort(matchUserDtos);                // 对分数进行排序
            // 记录本次匹配的分数
            saveSore(userId, matchUserDtos, mindUserMatchOrigin.getId(), 0);
            log.info(" 匹配真实用户成功的数据 = " + JsonUtil.writeString(matchUserDtos));
            for (MatchUserDto matchUserDto : matchUserDtos) {
                // 进行匹配计算
                int result = updateMatchCommonSucess(userId, uniqueCode, matchUserDto, false);
                if (Objects.equals(result, -1)) {               // 表示发起匹配的人已经被取消掉了，直接返回
                    return;
                } else if (Objects.equals(result, 0)) {         // 如果此人被别人已经匹配，则过滤掉此人
                    continue;
                } else if (Objects.equals(result, 1)) {         // 如果双方都匹配成功，但活动没有创建时
                    // 创建活动
                    createActivityAndPush(userId, uniqueCode, targetMileage, taskId, createSource, sourceDetails, request.getRouteId(), request.getSpecialEffectTheme(), request.getTargetRunTime(), request.getAppVersion(), request.getRunningGoalsUnit());
                    return;
                }
            }
        } else {
            log.info("userId=" + userId + " 没有匹配到真实用户 ");
        }
        // 如果匹配不到用户 ，则开始匹配机器人
        List<MindUserMatchRule> defaultUserMatchRuleList = mindUserMatchRuleService.selectMindUserMatchRuleIsDefaultStatusGroupByRuleHandler(1, 1);
        List<MatchUserDto> roBotmatchUserDtos = new ArrayList<>();

        // 获取所有未被匹配的机器人
        List<ZnsUserEntity> znsUserEntities = new ArrayList<>();
        RobotQuery robotQuery = RobotQuery.builder().mode("N").build();
        log.info("随机pk开始挑选rot。userID{}", userId);
        for (int i = 0; i < 10; i++) {
            ZnsUserEntity user = robotBizService.acquireRot(robotQuery);
            if (Objects.nonNull(user)) {
                log.info("随机pk开始挑选rot success,userID{}", userId);
                znsUserEntities.add(user);
            }
        }
        log.info("随机pk挑选rot all success,rots{}", znsUserEntities);

        int flag = 0;
        for (ZnsUserEntity znsUserEntity : znsUserEntities) {
            BigDecimal calScore = calScore(defaultUserMatchRuleList, userId, znsUserEntity.getId(), targetMileage);
            if (calScore.compareTo(BigDecimal.ZERO) > 0) {
                if (calScore.compareTo(new BigDecimal(10)) >= 0) {
                    flag++;
                }
                roBotmatchUserDtos.add(new MatchUserDto(znsUserEntity.getId(), uniqueCode, calScore));
                if (flag >= 4) {
                    // 如果机器人的分数大于等于10的分数有4个，则不再计算得分
                    break;
                }
            }
        }
        if (roBotmatchUserDtos.size() > 0) { //表明有匹配成功的数据
            Collections.sort(roBotmatchUserDtos); // 对分数进行排序
            saveSore(userId, roBotmatchUserDtos, mindUserMatchOrigin.getId(), 1);            // 保存分数匹配数据
            log.info(" 匹配机器人成功的数据 = " + JsonUtil.writeString(roBotmatchUserDtos));
            for (MatchUserDto robot : roBotmatchUserDtos) {
                int result = updateMatchCommonSucess(userId, uniqueCode, robot, true);
                if (Objects.equals(result, -1)) {       //表示已经被取消掉了，直接返回
                    return;
                } else if (Objects.equals(result, 0)) {
                    continue;
                } else if (Objects.equals(result, 1)) {       //匹配成功
                    createActivityAndPush(userId, uniqueCode, targetMileage, taskId, createSource, sourceDetails, request.getRouteId(), request.getSpecialEffectTheme(), request.getTargetRunTime(), request.getAppVersion(), request.getRunningGoalsUnit());
                    return;
                }
            }
        } else {
            log.info("机器人匹配失败 userId= " + userId);
        }

        // 如果真实用户和机器人都没有被匹配到，此时只能创建一个机器人来和用户进行匹配
        ZnsUserEntity robot = createRobot(userId, uniqueCode);
        MindUserMatch m = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(userId, uniqueCode);
        MindUserMatch n = build(robot.getId(), 1, 1, m.getId());
        mindUserMatchBizService.insertMindUserMatch(n);

        m.setStatus(1);
        m.setMindUserMatchId(n.getId());
        mindUserMatchBizService.updateMindUserMatchById(m);
        createActivityAndPush(userId, uniqueCode, targetMileage, taskId, createSource, sourceDetails, request.getRouteId(), request.getSpecialEffectTheme(), request.getTargetRunTime(), request.getAppVersion(), request.getRunningGoalsUnit());
    }

    public MindUserMatch build(Long userId, Integer status, Integer isRobot, Long mindUserMatchId) {
        MindUserMatch mindUserMatch = new MindUserMatch();
        mindUserMatch.setUserId(userId);
        mindUserMatch.setStatus(status);
        mindUserMatch.setIsRobot(isRobot);
        mindUserMatch.setMindUserMatchId(mindUserMatchId);
        return mindUserMatch;
    }


    public boolean isContains(List<MatchUserDto> matchUserDtos, Long userId) {
        for (MatchUserDto matchUserDto : matchUserDtos) {
            if (matchUserDto.getUserId().equals(userId)) {
                return true;
            }
        }
        return false;
    }

    public void createActivityAndPush(Long userId, String uniqueCode, Integer targetMileage, Long taskId, Integer createSource, String sourceDetails, Long routeId, Integer specialEffectTheme, Integer targetRunTime, Integer appVersion, Integer runningGoalsUnit) {
        RunActivityRequest runActivity = new RunActivityRequest();
        ZnsUserEntity currentUser = znsUserService.findById(userId);
        MindUserMatch m = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(userId, uniqueCode);
        MindUserMatch n = mindUserMatchService.selectMindUserMatchById(m.getMindUserMatchId());
        if (m.getActivityId() != null) {
            log.info("m 活动已经创建过了 userId=" + m.getUserId());
            return;
        }
        if (n.getActivityId() != null) {
            log.info("n 活动已经创建过了 userId=" + m.getUserId());
            return;
        }

        ZnsUserEntity matchUser = znsUserService.findById(n.getUserId());
        runActivity.setActivityTitle(I18nMsgUtils.getMessage("activity.robot.match.title", currentUser.getFirstName(), matchUser.getFirstName())); //  活动title
        runActivity.setActivityConfigId(2l);
        //获取随机
        runRouteManager.setRandomRoute(RunActivityTypeEnum.CHALLENGE_RUN.getType(), RunActivitySubTypeEnum.RANDOM_MATCHING.getType(), currentUser.getId(), runActivity, appVersion, routeId);
        ZonedDateTime fiveMinutes = DateUtil.formatMinites(ZonedDateTime.now().plusMinutes(sysConfigService.selectActivityStartMinite(currentUser.getId())));
        String activityStartTime = DateUtil.dateStr(fiveMinutes, DateUtil.YYYY_MM_DD_HH_MM_SS);
        ZonedDateTime fiveActivityTime = DateUtil.addHours(fiveMinutes, 24);
        runActivity.setActivityStartTime(fiveMinutes.toInstant().toEpochMilli());
        if (Objects.nonNull(targetMileage) && targetMileage > 0) {
            runActivity.setCompleteRuleType(1); //完成规则类型：1表示完成跑步里程，2表示完成跑步时长
            runActivity.setRunningGoalsUnit(1);
        } else {
            runActivity.setCompleteRuleType(2); //完成规则类型：1表示完成跑步里程，2表示完成跑步时长
            runActivity.setRunningGoalsUnit(2);
        }
        if (Objects.nonNull(runningGoalsUnit)) {
            runActivity.setRunningGoalsUnit(runningGoalsUnit);
        }
        if (Objects.isNull(targetRunTime)) {
            targetRunTime = 0;
        }
        if (Objects.isNull(targetMileage)) {
            targetMileage = 0;
        }
        runActivity.setRunMileage(new BigDecimal(targetMileage));
        runActivity.setRunTime(new BigDecimal(targetRunTime));
        runActivity.setBonusRuleType(1);//奖金规则类型：1表示免费参加，2表示保证金参加
        runActivity.setActivityEntryFee(BigDecimal.ZERO);
        runActivity.setIsRobotStart(0);//是否机器人发起: 0表示非机器人发起,1表示机器人发起
        runActivity.setActivityUserIds(Arrays.asList(matchUser.getId()));
        runActivity.setIsNowStart(0);//活动开始时间是否是现在: 1表示现在发起活动，0表示不是现在发起活动
        runActivity.setIsPublic(1);
        runActivity.setActivityTypeSub(1);
        if (Objects.equals(n.getIsRobot(), 1)) {
            runActivity.setHasRobot(1);
            runActivity.setRobotUserId(n.getUserId());
        }
        runActivity.setRunMode(m.getRunMode()); // 机器人跑步模式
        runActivity.setChallengeRunType(1);             //
        runActivity.setCreateSource(createSource);
        runActivity.setSourceDetails(sourceDetails);
        runActivity.setAppVersion(appVersion);
        log.info("createActivityAndPush activity data info : " + JsonUtil.writeString(runActivity));

        Result result = activityStrategyContext.doLaunchActivity(runActivity, currentUser);
        if (CommonError.SUCCESS.getCode().equals(result.getCode())) {
            Map<String, Object> data = (Map<String, Object>) result.getData();
            Long activityId = MapUtil.getLong(data.get("activityId"), -1L);
            // 更新开始时间 ，因为这个才是整点的
            ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
            znsRunActivityEntity.setActivityStartTime(fiveMinutes);
            znsRunActivityService.updateById(znsRunActivityEntity);
            log.info("活动创建成功 " + activityId + " ,startTime = " + activityStartTime); // 活动开始时间
            m.setActivityId(activityId);
            m.setTargetMileage(targetMileage);
            m.setActivityEndTime(fiveActivityTime);
            m.setMatchUserId(n.getUserId());
            mindUserMatchBizService.updateMindUserMatchById(m);

            if (Objects.equals(n.getIsRobot(), 1)) {
                Random random = new Random();
                // 新增需求：当此用户除了新人活动的PK赛外，如果都从未赢过，则每次都匹配机器人的跑步模式1“大概率输”，让用户先赢一次（继仁需求）；然后再走以上的匹配逻辑
                // activityType: 活动类型：1 组队跑,多人同跑  2 挑战跑,竞技跑 3 官方排行赛,官方赛事 4 官方组队跑，多人同跑,官方赛事 5 官方累计跑,官方赛事 6 新人福利活动
                // 机器人跑步模式的设置
                //默认情况下是can_fail， can_fail_success， can_success
                //       a) 如果15天之内在一起跑步过，则是默认 can_fail， can_fail_success， can_success 随机一种
                //       b) 如果15天之内 1v1 1次
                //       c) 如果15天之内 1v1 2次
                //       d) 如果15天之内 1v1 3次,则去掉最大值，去掉最小值 ， 再计算平均值 。
                //       e) 如果平均速度大于 10.5，则是 S 和 A 模式中随机一个
                //       f) 如果平均速度 9.5 ~ 10.5 ,则 A， B 模式随机一个。
                //       g) 如果平均速度在 8.5 ~ 9.5 之间，则 B 和 C 模式中随机一个。
                //       h) 如果平均速度在7.5 ~ 8.5之间， 则是C 和D 模式中随机一个。
                //       i ) 如果平均速度在 0.1 ~ 7.5 之间 ， 则 跑步模式为 D 。
                ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectRunActivityUserByNQActivityTypeRank(userId, 2, 1, null);
                String runMode = "";
                if (znsRunActivityEntity.getCompleteRuleType() == 2) {
                    runMode = getModeByUserId();
                } else {
                    if (znsRunActivityUserEntity == null) {
                        runMode = "can_fail";                   // 大概率输
                    } else {
                        List<String> list = Arrays.asList("can_fail", "can_fail_success", "can_success");
                        int randomNex = random.nextInt(list.size());
                        log.info("模式随机数为 ：" + randomNex);
                        runMode = list.get(randomNex);
                        UserMatchModeRecord matchModeRecord = new UserMatchModeRecord();
                        matchModeRecord.setUserId(userId);
                        matchModeRecord.setMindUserMatchId(n.getId());
                        matchModeRecord.setActivityId(activityId);

                        String accMode = getModeByUserId(userId, activityId, matchModeRecord);
                        log.info("1v1最近15天计算出最终模式  userId= " + userId + " ，模式=" + accMode);
                        if (StringUtils.hasText(accMode)) {
                            runMode = accMode;
                        }
                    }
                }

                //任务绑定
                runMode = bindingTask(taskId, runMode, znsRunActivityEntity);
                n.setRunMode(runMode);
                ZnsRunRouteEntity znsRunRouteEntity = znsRunRouteService.selectRunRouteById(znsRunActivityEntity.getActivityRouteId());
                if (Objects.equals(znsRunRouteEntity.getRouteType(), 2)) {   // 表示3D路线
                    // 47.110.167.249:8001/add-robot?userid=机器人id&roomid=要加入的房间号&roadid=跑道id&firstname=&lastname=&avatar=头像url&gender=性别(1：男，2：女)
                    GamePushUtils.addRobot(gamepush, activityId, runActivity.getActivityRouteId(), matchUser, activityId, n.getId());
                }
            }

            n.setActivityEndTime(fiveActivityTime); // 默认当前时间加24个小时
            n.setTargetMileage(targetMileage);
            n.setActivityId(activityId);
            n.setMatchUserId(m.getUserId());
            mindUserMatchBizService.updateMindUserMatchById(n);
            // 在线或离线推送
            if (Objects.equals(n.getIsRobot(), 1)) {
                socketPushUtils.onlinePush(activityId, matchUser, 1);
            }
            ZnsRunRouteEntity znsRunRouteEntity = znsRunRouteService.selectRunRouteById(znsRunActivityEntity.getActivityRouteId());
            // 如果匹配到的是机器人，则直接同意即可。 如果匹配的是真实用户，还需要给真实用户发送一个socket推送，此时需要被匹配成功的用户进行弹窗提示，将机器人添加到房间中 。
            // 向socket推送，对方用户已经随机匹配成功，比如当前用户头像，用户名， 他们的路线id , 目标里程，匹配到的用户的头像， id , 昵称等 ，前端弹窗 。
            push(activityId, znsRunActivityEntity.getActivityRouteId(), fiveMinutes, m, currentUser, matchUser, znsRunRouteEntity.getRouteType());
            push(activityId, znsRunActivityEntity.getActivityRouteId(), fiveMinutes, n, matchUser, currentUser, znsRunRouteEntity.getRouteType());
        } else {
            log.info("活动创建失败:" + result.getMsg());
        }
    }

    /**
     * 任务绑定
     *
     * @param taskId
     * @param runMode
     * @param znsRunActivityEntity
     */
    private String bindingTask(Long taskId, String runMode, ZnsRunActivityEntity znsRunActivityEntity) {
        if (Objects.isNull(taskId) || taskId == 0) {
            return runMode;
        }
        RunActivityUserTask task = runActivityUserTaskService.findById(taskId);
        if (Objects.isNull(task)) {
            return runMode;
        }
        if (task.getTaskType() != 1) {
            return runMode;
        }
        if (task.getActivityType() == 6) {
            runMode = runActivityUserTaskService.getRunningMode(task.getRobotRunningMode());
            log.info("createActivityAndPush 获取runMode=" + runMode);
            //活动绑定
            task.setActivityId(znsRunActivityEntity.getId());
            runActivityUserTaskService.update(task);
            //取消上一场活动
            runActivityProcessManager.cancelLastActivityTask(task.getUserId(), task.getId(), znsRunActivityEntity.getId());
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(znsRunActivityEntity.getId(), task.getUserId());
        if (Objects.nonNull(activityUser)) {
            activityUser.setTaskId(taskId);
            runActivityUserService.updateById(activityUser);
        }
        return runMode;
    }

    public void saveSore(Long userId, List<MatchUserDto> matchUserDtos, Long id, Integer isRobot) {
        if (!CollectionUtils.isEmpty(matchUserDtos)) {
            for (MatchUserDto matchUserDto : matchUserDtos) {
                MindUserMatchScore mindUserMatchScore = new MindUserMatchScore();
                mindUserMatchScore.setMatchUserId(matchUserDto.getUserId());
                mindUserMatchScore.setUserId(userId);
                mindUserMatchScore.setMindUserMatchId(id);
                mindUserMatchScore.setIsRobot(isRobot);
                mindUserMatchScore.setScore(matchUserDto.getScore());
                mindUserMatchScoreService.insertMindUserMatchScore(mindUserMatchScore);
            }
        }
    }

    public String getModeByUserId(Long userId, Long activityId, UserMatchModeRecord matchModeRecord) {
        //防挫败处理
        String mode = runActivityUserManager.defeatPreventionMechanism(userId, activityId, matchModeRecord);
        log.info("getModeByUserId ------mode=" + mode);
        if (StringUtils.hasText(mode)) {
            return mode;
        }

        List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetails = znsUserRunDataDetailsService.selectByUserIdUserRunTimeRunMileage15(userId, 60,
                new BigDecimal(100), DateUtil.addDays(ZonedDateTime.now(), -15));
        if (CollectionUtils.isEmpty(znsUserRunDataDetails)) {
            log.info("1v1最近15天没有数据 userId=" + userId);
            return null;
        }
        BigDecimal avg = BigDecimal.ZERO;
        if (znsUserRunDataDetails.size() == 1) {
            log.info("1v1最近15天只有一条数据 userId = " + userId);
            avg = znsUserRunDataDetails.get(0).getAverageVelocity();
        } else if (znsUserRunDataDetails.size() == 2) {
            log.info("1v1最近15天只有2条数据 ");
            avg = BigDecimalUtil.divide(znsUserRunDataDetails.get(0).getAverageVelocity()
                            .add(znsUserRunDataDetails.get(1).getAverageVelocity())
                    , new BigDecimal(2));
        } else if (znsUserRunDataDetails.size() >= 3) {
            log.info("1v1最近15天只有多条数据 userId = " + userId);
            BigDecimal maxAvg = BigDecimal.ZERO;
            BigDecimal minAvg = new BigDecimal(1000000);
            BigDecimal sumAvg = BigDecimal.ZERO;
            for (ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity : znsUserRunDataDetails) {
                if (znsUserRunDataDetailsEntity.getAverageVelocity().compareTo(maxAvg) > 0) {
                    maxAvg = znsUserRunDataDetailsEntity.getAverageVelocity();
                }
                if (znsUserRunDataDetailsEntity.getAverageVelocity().compareTo(minAvg) < 0) {
                    minAvg = znsUserRunDataDetailsEntity.getAverageVelocity();
                }
                sumAvg = sumAvg.add(znsUserRunDataDetailsEntity.getAverageVelocity());
            }
            sumAvg = sumAvg.subtract(maxAvg).subtract(minAvg);
            avg = BigDecimalUtil.divide(sumAvg, new BigDecimal(znsUserRunDataDetails.size() - 2));
        }
        double v = avg.doubleValue();
        log.info("1v1最近15天的平均速度 = " + v + ",userId=" + userId);
        Random random = new Random();
        int randomv = random.nextInt(2);


        if (v > 10.5) {
            return randomv == 1 ? "S+" : "S";
        } else if (v > 9.5) {
            return randomv == 1 ? "S" : "A";
        } else if (v > 8.5) {
            return randomv == 1 ? "A" : "B";
        } else if (v > 7.5) {
            return randomv == 1 ? "B" : "C";
        } else if (v > 0.1) {
            return "C";
        }

        return null;
    }

    public void push(Long activityId, Long activityRouteId, ZonedDateTime activityStartTime, MindUserMatch m, ZnsUserEntity currentUser, ZnsUserEntity matchUser,
                     Integer routeType) {
        Map<String, Object> da = new HashMap<>();

        da.put("activityId", activityId);
        da.put("activityStartTime", activityStartTime);
        da.put("userId", m.getUserId());
        da.put("userNickName", currentUser.getFirstName());
        da.put("userHeadPortrait", currentUser.getHeadPortrait());

        da.put("matchUserId", m.getMatchUserId());
        da.put("matchUserNickName", matchUser.getFirstName());
        da.put("matchUserHeadPortrait", matchUser.getHeadPortrait());
        da.put("activityRouteId", activityRouteId); //环形跑道
        da.put("startMinutes", 5);
        da.put("targetMileage", m.getTargetMileage());   // 目标里程
        da.put("targetTime", m.getTargetTime());   // 目标时长
        da.put("routeType", routeType);

        String content = JsonUtil.writeString(da);
        log.info("推送的数据为：" + content);
        // 发送消息 ,  默认的房间号为-2，此时因为用户还没有进房间，因此默认的房间号为-2
        socketPushUtils.push(-2l, currentUser.getEmailAddressEn(), SocketEventEnums.MIND_MATCH_MESSAGE.getCode(), 2, da);
    }

    public BigDecimal calScore(List<MindUserMatchRule> mindUserMatchRuleList, Long userId, Long targetUserId, Integer targetMileage) {
        BigDecimal sumScore = BigDecimal.ZERO;
        for (MindUserMatchRule mindUserMatchRule : mindUserMatchRuleList) {
            BaseMatchHandler baseMatchHandler = SpringContextUtils.getBean(mindUserMatchRule.getRuleHandler(), BaseMatchHandler.class);
            Pair<Boolean, BigDecimal> pair = baseMatchHandler.match(userId, targetUserId, targetMileage);
            if (pair.getKey()) {
                log.info(" handler = " + mindUserMatchRule.getRuleHandler() + " userId = " + userId + ", score = " + pair.getValue());
                sumScore = sumScore.add(pair.getValue());
            } else {
                log.info(" handler = " + mindUserMatchRule.getRuleHandler() + " userId = " + userId + " 计算无效，没有分数");
            }
        }
        return sumScore;
    }

    // pair[0] = -1 表示已经取消
    // pair[0] = 1 表示已经匹配成功
    // pair[0] = 0 表示继续匹配
    public Integer updateMatchCommonSucess(Long userId, String uniqueCode, MatchUserDto matchUserDto, boolean isRobot) {
        MindUserMatch m = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(userId, uniqueCode);
        if (Arrays.asList(-1, 1).contains(m.getStatus())) {
            log.info(userId + " 已经被取消 或匹配成功, status = " + m.getStatus());
            return -1;
        }
        //加锁
        String key1 = RedisConstants.MIND_MATCH_KEY + userId;
        String key2 = RedisConstants.MIND_MATCH_KEY + matchUserDto.getUserId();
        RLock lock1 = redissonClient.getLock(key1);
        RLock lock2 = redissonClient.getLock(key2);
        RLock multiLock = redissonClient.getMultiLock(lock1, lock2);
        try {
            if (multiLock.tryLock(120, 120, TimeUnit.SECONDS)) {
                m = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(userId, uniqueCode);
                if (Arrays.asList(-1, 1).contains(m.getStatus())) {
                    log.info(userId + " 已经被取消 或匹配成功, status = " + m.getStatus());
                    return -1;
                }
                // 如果是机器人
                MindUserMatch n = mindUserMatchService.selectMindUserMatchByUniqueCodeUserIdStatus(matchUserDto.getUniqueCode(), matchUserDto.getUserId(), 1);   // 表示已经匹配成功
                if (n != null) {
                    // 此机器人已经被匹配了
                    log.info("此机器人已经被匹配了 " + n.getUserId());
                    return 0;
                }
                if (isRobot) {        //如果是机器人，则匹配成功
                    n = build(matchUserDto.getUserId(), 1, 1, m.getId());
                    mindUserMatchBizService.insertMindUserMatch(n);

                    // 更新匹配到的用户
                    m.setStatus(1);
                    m.setMindUserMatchId(n.getId());
                    mindUserMatchBizService.updateMindUserMatchById(m);
                    log.info("   userId=" + userId + ", targetUserId = " + matchUserDto.getUserId() + " 机器人匹配成功");
                    return 1;
                } else {
                    n = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(matchUserDto.getUserId(), matchUserDto.getUniqueCode());   // 表示已经匹配成功
                    if (Objects.equals(n.getStatus(), 0)) { //表示还未匹配成功
                        n.setStatus(1);

                        n.setMindUserMatchId(m.getId());
                        mindUserMatchBizService.updateMindUserMatchById(n);

                        m.setStatus(1);

                        m.setMindUserMatchId(n.getId());
                        mindUserMatchBizService.updateMindUserMatchById(m);
                        log.info("   userId=" + userId + ", targetUserId = " + matchUserDto.getUserId() + " 匹配成功");
                        return 1;
                    }
                }
            }
        } catch (Exception e) {
            log.error("异常", e);
        } finally {
            if (multiLock.isHeldByCurrentThread()) {
                log.info("updateMatchCommonSucess key1=" + key1 + " 释放锁");
                log.info("updateMatchCommonSucess key2=" + key2 + " 释放锁");
                multiLock.unlock();
            }
        }
        return 0;
    }


    public ZnsUserEntity createRobot(Long userId, String uniqueCode) {
        MindUserMatch m = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(userId, uniqueCode);
        ZnsUserEntity znsUserEntity = znsUserService.findById(m.getUserId());
        ZnsUserEntity user = znsUserService.doCreateRobot(znsUserEntity);

        log.info("创建新的机器人 userId= " + user.getId());
        ZnsUserAddEntity znsUserAddEntity = new ZnsUserAddEntity();
        znsUserAddEntity.setUserId(user.getId());
        Random random = new Random();
        List<SysUser> sysUsers = sysUserMapper.selectAllUser();
        int i = random.nextInt(sysUsers.size());
        SysUser sysUser = sysUsers.get(i);
        znsUserAddEntity.setFollowUserId(sysUser.getUserId());
        znsUserAddEntity.setFollowUserName(sysUser.getNickName());
        znsUserAddEntity.setDeptId(sysUser.getDeptId());
        znsUserAddService.insertZnsUserAddEntity(znsUserAddEntity);
        return user;
    }


    public String getModel() {
        Random random = new Random();
        List<String> list = Arrays.asList("can_fail", "can_fail_success", "can_success");
        int randomNex = random.nextInt(list.size());
        log.info("模式随机数为 ：" + randomNex);
        String runMode = list.get(randomNex);
        return runMode;
    }

    /**
     * 新人挑战多人版本发起
     *
     * @param req
     * @return
     */
    @PostMapping("/newUser/multiple/match")
    public Result<NewUserMatchMultipleResp> newUserMatchMultiple(@RequestBody NewUserMultipleMatchReq req) {
        RLock lock = redissonClient.getLock(CacheConstants.FRIEND_MATCH_ + req.getUserId());
        return LockHolder.tryLock(lock, () -> {
            try {
                NewUserMatchMultipleResp respVo = newUserMultipleBusiness.newUserMatchMultiple(req, getAppVersion());
                return CommonResult.success(respVo);
            } catch (Exception e) {
                log.error("newUserMatchMultiple异常", e);
                return CommonResult.success();
            }
        });
    }

    /**
     * 新人挑战配置获取
     *
     * @param
     * @return
     */
    @PostMapping("/newUser/multiple/config")
    public Result<NewPersonPkConfigVo> newUserMultipleConfig() {
        NewPersonPkConfigVo newPersonPkConfigVo = newUserMultipleBusiness.getNewUserMultipleConfig();
        return CommonResult.success(newPersonPkConfigVo);
    }

    /**
     * 新人挑战奖励配置获取
     *
     * @param
     * @return
     */
    @PostMapping("/newUser/multiple/award")
    public Result<NewPkMultipleConfigVo> newUserMultipleAward() {
        ZnsUserEntity loginUser = getLoginUser();
        NewPkMultipleConfigVo newPersonPkConfigVo = newUserMultipleBusiness.getNewUserMultipleAward(loginUser);
        return CommonResult.success(newPersonPkConfigVo);
    }


}
