package com.linzi.pitpat.api.dto.response.achievement;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 能量助力DTO
 *
 * @since 4.7.0
 */
@Data
public class EnergyDto {

    /**
     * 用户全部能量
     */
    private Integer allEnergy;

    /**
     * 我助力能量
     */
    private Integer myEnergy;

    /**
     * 查看用户id（当前用户）
     */
    private Long userId;

    /**
     * 数据用户id
     */
    private Long dataUserId;


    /**
     * 全部助力明细
     */
    private List<EnergyDetail> energyDetails;

    /**
     * 用户勋章数据
     */
    @Data
    @NoArgsConstructor
    public static class EnergyDetail {
        /**
         * 头像
         */
        private String headPortrait;

        /**
         * 姓名
         */
        private String firstName;

        /**
         * 用户助力总能量
         */
        private Integer totalEnergy;

        /**
         * 最近助力时间
         */
        private ZonedDateTime lastHelpTime;
    }


}
