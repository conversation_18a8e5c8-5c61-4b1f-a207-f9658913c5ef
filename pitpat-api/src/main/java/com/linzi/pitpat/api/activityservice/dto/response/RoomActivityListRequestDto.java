package com.linzi.pitpat.api.activityservice.dto.response;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2024/7/10 16:52
 */
@Data
public class RoomActivityListRequestDto {
    /**
     * 胜者/第一名
     */
    private String winner;
    /**
     * 头像
     */
    private String headPortrait;
    /**
     * 里程
     */
    private Integer runMileage;
    /**
     * 毫秒
     */
    private Integer runTimeMillisecond;
    /**
     * 活动开始时间
     */
    private ZonedDateTime startTime;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 目标距离
     */
    private Integer targetRunMileage;
    /**
     * 目标时间
     */
    private Integer targetRunTime;
    /**
     * 运动数据状态:0未完成 dnf，1表示完成(展示成绩) 2标识退赛（特殊用） 3:作弊 4：进行中 5：表示完成(不代表比赛完成，只是展示完成标识)
     */
    private Integer resultDataState;
    /**
     * 风控审核状态 1:审核中，0：无
     *
     * @version 4.4.8
     */
    private Integer isRiskReview;
}
