package com.linzi.pitpat.api.bussiness;


import com.linzi.pitpat.api.dto.request.UserActivityIMChatReqDto;
import com.linzi.pitpat.api.dto.request.UserIMChatReqDto;
import com.linzi.pitpat.api.dto.response.UserIMChatRespDto;
import com.linzi.pitpat.core.constants.enums.ApiSource;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.enums.UserChatAnswerEnum;
import com.linzi.pitpat.data.enums.UserChatStateEnum;
import com.linzi.pitpat.data.messageservice.model.entity.ImSendRecordLimitLog;
import com.linzi.pitpat.data.messageservice.model.query.ImSendRecordLimitLogQuery;
import com.linzi.pitpat.data.messageservice.service.ImSendRecordLimitLogService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserChatRelation;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserChatRelationService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.im.BlacklistResultDto;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RefreshScope
@Slf4j
@RequiredArgsConstructor
public class AppUserIMManager {
    private final UserChatRelationService userChatRelationService;
    private final ZnsUserFriendService znsUserFriendService;
    private final ISysConfigService sysConfigService;
    private final TencentImUtil tencentImUtil;
    private final ImSendRecordLimitLogService imSendRecordLimitLogService;
    private final ZnsUserService userService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    // 用户IM交流数量限制
    @Value("${user.chat.limit.num:5}")
    private Integer userChatLimitNum;


    public UserIMChatRespDto restrictMsg(UserIMChatReqDto dto) {
        String initiatorIdStr = dto.getInitiator();
        String respondentIdStr = dto.getRespondent();

        UserIMChatRespDto respDto = new UserIMChatRespDto();
        respDto.setFlag(false);
        //排除官方账号im.system.account
        if (excludeSystemAccount(initiatorIdStr, respondentIdStr)) {
            return null;
        }
        Long respondent = "administrator".equals(respondentIdStr) ? 0L : Long.parseLong(respondentIdStr);
        Long initiator = "administrator".equals(initiatorIdStr) ? 0L : Long.parseLong(initiatorIdStr);
        UserChatRelation chat = userChatRelationService.getByUser(initiator, respondent);
        if (Objects.isNull(chat)) {
            // 创建新聊天关系记录
            createNewChatRelation(initiator, respondent, respDto);
        } else {
            // 处理存在的聊天关系
            handleExistingChatRelation(chat, initiator, respondent, respDto);
        }
        return respDto;
    }


    private void createNewChatRelation(Long initiator, Long respondent, UserIMChatRespDto respDto) {
        UserChatRelation userChatRelation = new UserChatRelation();
        userChatRelation.setInitiator(initiator);
        userChatRelation.setRespondent(respondent);

        if (Objects.equals(znsUserFriendService.isFriend(initiator, respondent), 1)) {
            // 好友关系
            userChatRelation.setInitSpeakTimes(1);
            userChatRelation.setState(UserChatStateEnum.UNRESTRICTED.getCode());
            userChatRelation.setAnswer(UserChatAnswerEnum.NOT_CHAT.getCode());
            userChatRelationService.insert(userChatRelation);
        } else {
            userChatRelation.setInitSpeakTimes(1);
            userChatRelation.setState(UserChatStateEnum.LIMITED.getCode());
            userChatRelation.setAnswer(UserChatAnswerEnum.INITIATOR_SPEAKING.getCode());
            userChatRelationService.insert(userChatRelation);
            respDto.setFlag(true);
            respDto.setMsg(I18nMsgUtils.getMessage("im.restrict.msg_1"));
        }
    }

    private void handleExistingChatRelation(UserChatRelation chat, Long initiator, Long respondent, UserIMChatRespDto respDto) {
        if (chat.getInitiator().equals(initiator)) {
            handleInitiatorChatRelation(chat, initiator, respondent, respDto);
        } else if (chat.getInitiator().equals(respondent)) {
            handleRespondentChatRelation(chat, initiator, respondent);
        }
    }

    private void handleInitiatorChatRelation(UserChatRelation chat, Long initiator, Long respondent, UserIMChatRespDto respDto) {
        if (UserChatStateEnum.NOT_ALLOWED.getCode().equals(chat.getState())) {
            // 设置红点
            respDto.setRedDotFlag(true);
            respDto.setFlag(true);
            respDto.setMsg(I18nMsgUtils.getMessage("im.restrict.msg_2"));
        } else if (UserChatStateEnum.LIMITED.getCode().equals(chat.getState())) {
            chat.setInitSpeakTimes(chat.getInitSpeakTimes() + 1);
            if (chat.getInitSpeakTimes() >= userChatLimitNum) {
                chat.setState(UserChatStateEnum.NOT_ALLOWED.getCode());
                // 拉黑
                Integer resultCode = handleAddBlacklist(initiator, respondent);
                if (Objects.equals(resultCode, 0)) {
                    respDto.setFlag(true);
                    respDto.setMsg(I18nMsgUtils.getMessage("im.restrict.msg_2"));
                } else {
                    chat.setState(null);
                    log.error("IM拉黑失败,initiator:{}, respondent:{}", initiator, respondent);
                }
            }
            chat.setGmtModified(ZonedDateTime.now());
            userChatRelationService.update(chat);
        }
    }

    private void handleRespondentChatRelation(UserChatRelation chat, Long initiator, Long respondent) {
        if (UserChatStateEnum.NOT_ALLOWED.getCode().equals(chat.getState())
                || UserChatStateEnum.LIMITED.getCode().equals(chat.getState())) {
            // B回复A，双方说话达成无限制聊天
            chat.setRespSpeakTimes(1);
            chat.setState(UserChatStateEnum.UNRESTRICTED.getCode());
            chat.setAnswer(UserChatAnswerEnum.BOTH_SPEAKING.getCode());
            chat.setGmtModified(ZonedDateTime.now());
            userChatRelationService.update(chat);
            handleDeleteBlacklist(respondent, initiator);
        }
    }


    private Integer handleAddBlacklist(Long initiator, Long respondent) {
        Integer resultCode = -1;
        BlacklistResultDto addBlacklistResult = tencentImUtil.addBlacklist(respondent, List.of(initiator));
        if (Objects.equals(addBlacklistResult.getErrorCode(), 0)) {
            resultCode = addBlacklistResult.getResultItem().get(0).getResultCode();
        }
        return resultCode;
    }

    private Integer handleDeleteBlacklist(Long initiator, Long respondent) {
        Integer resultCode = -1;
        BlacklistResultDto addBlacklistResult = tencentImUtil.deleteBlacklist(respondent, List.of(initiator));
        if (Objects.equals(addBlacklistResult.getErrorCode(), 0)) {
            resultCode = addBlacklistResult.getResultItem().get(0).getResultCode();
        }
        return resultCode;
    }


    private boolean excludeSystemAccount(String initiator, String respondent) {
        String systemAccountList = sysConfigService.selectConfigByKey("im.system.account");
        List<String> list = JsonUtil.readList(systemAccountList, String.class);
        for (String accountStr : list) {
            if (accountStr.equals(initiator)
                    || accountStr.equals(respondent)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 互关注处理聊天关系
     *
     * @param loginUserId
     * @param friendId    被关注人
     */
    public void handleFollowChatRelation(Long loginUserId, Long friendId) {
        UserChatRelation chat = userChatRelationService.getByUser(loginUserId, friendId);
        if (Objects.isNull(chat)) {
            UserChatRelation userChatRelation = new UserChatRelation();
            userChatRelation.setInitiator(loginUserId);
            userChatRelation.setRespondent(friendId);
            userChatRelation.setState(UserChatStateEnum.UNRESTRICTED.getCode());
            userChatRelation.setAnswer(UserChatAnswerEnum.NOT_CHAT.getCode());
            userChatRelationService.insert(userChatRelation);
        } else {
            chat.setState(UserChatStateEnum.UNRESTRICTED.getCode());
            chat.setAnswer(UserChatAnswerEnum.NOT_CHAT.getCode());
            chat.setGmtModified(ZonedDateTime.now());
            userChatRelationService.update(chat);
        }
        // 无法确定谁拉黑谁，所以删除两侧黑名单
        handleDeleteBlacklist(loginUserId, friendId);
        handleDeleteBlacklist(friendId, loginUserId);
    }

    /**
     * 取关处理聊天关系
     *
     * @param loginUserId
     * @param friendId    被取关的人
     */
    public void handleUnFollowChatRelation(Long loginUserId, Long friendId) {
        UserChatRelation chat = userChatRelationService.getByUser(loginUserId, friendId);
        if (Objects.nonNull(chat) && UserChatStateEnum.UNRESTRICTED.getCode().equals(chat.getState())) {
            userChatRelationService.deleteById(chat.getId());
        }
    }


    public UserIMChatRespDto restrictActivityMsg(UserActivityIMChatReqDto dto, ApiSource apiSource) {
        UserIMChatRespDto respDto = new UserIMChatRespDto();
        respDto.setFlag(false);
        ImSendRecordLimitLog recordLimitLog = imSendRecordLimitLogService.findOneByQuery(new ImSendRecordLimitLogQuery().setUserId(dto.getUserId()).setActivityId(dto.getActivityId()));
        if (Objects.nonNull(recordLimitLog)) {
            return respDto;
        }
        ZnsUserEntity user = userService.findById(dto.getUserId());
        if (Objects.isNull(user)) {
            return respDto;
        }
        String chatId = "";
        if (ApiSource.H5.equals(apiSource)) {
            String value = sysConfigService.selectConfigByKey(ConfigKeyEnums.COMPETITION_ARBITRATION_ROBOT.getCode());
            Map<String, Object> data = JsonUtil.readValue(value, Map.class);
            chatId = MapUtils.getLong(data, "chatId").toString();
        } else {
            String chatRobotUserIds = "";
            if (Objects.equals(user.getMemberType(), 1)) {
                chatRobotUserIds = sysConfigService.selectConfigByKey(ConfigKeyEnums.chat_robot_vip_user_ids.getCode());
            }
            if (Objects.equals(user.getMemberType(), 0) || !StringUtils.hasText(chatRobotUserIds)) {
                chatRobotUserIds = sysConfigService.selectConfigByKey(ConfigKeyEnums.chat_robot_user_ids.getCode());
            }
            if (StringUtils.hasText(chatRobotUserIds)) {
                String[] split = chatRobotUserIds.split(",");
                chatId = split[0];
            }
        }

        if (!StringUtils.hasText(chatId)) {
            return respDto;
        }
        String activityTitle = activityDisseminateBizService.findActivityTitle(dto.getActivityId(), dto.getUserId(), "");
        String message = I18nMsgUtils.getLangMessage(user.getLanguageCode(), "user.cheat.activity.question", activityTitle, dto.getActivityId() + "");
        tencentImUtil.sendMsg(1, dto.getUserId() + "", chatId, TencentImConstant.TIM_TEXT_ELEM, message);
        respDto.setFlag(true);
        //保存一条记录
        imSendRecordLimitLogService.insert(new ImSendRecordLimitLog().setImSendType(1).setActivityId(dto.getActivityId()).setUserId(dto.getUserId()));
        return respDto;
    }
}
