package com.linzi.pitpat.api.mallservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.mallservice.dto.request.MallCategoryReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.CheckUserMallCountryRequestDto;
import com.linzi.pitpat.data.mallservice.dto.response.CheckUserMallCountryResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallCategoryRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallHomeGoodDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallHomePageRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallHomePopDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallIndexInfoResponseDto;
import com.linzi.pitpat.api.mallservice.mananger.MallHomePageManager;
import com.linzi.pitpat.core.util.IpUtil;
import com.linzi.pitpat.core.util.RegionUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.mallservice.dto.request.MallGoodsRequestDto;
import com.linzi.pitpat.data.mallservice.dto.response.GoodsPolicyListResponseDto;
import com.linzi.pitpat.data.mallservice.enums.GoodsConstant;
import com.linzi.pitpat.data.mallservice.model.entity.ShoppingCartDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsQuery;
import com.linzi.pitpat.data.mallservice.service.ShoppingCartService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.vo.AppBaseInfoVo;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商城首页服务类
 */
@RestController
@Slf4j
@RequestMapping("/app/mall/home")
@RequiredArgsConstructor
public class MallHomePageController extends BaseAppController {

    private final MallHomePageManager manageHomePageManager;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final ShoppingCartService shoppingCartService;

    /**
     * 商城首页配置信息
     */
    @PostMapping("/indexInfo")
    public Result<MallIndexInfoResponseDto> indexInfo() {
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        MallIndexInfoResponseDto resp = manageHomePageManager.indexInfo(user.getId(),getLanguageCode());
        return CommonResult.success(resp);
    }

    /**
     * 商城首页接口
     */
    @PostMapping("/list")
    public Result<List<MallHomePageRespDto>> homeList() {
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        return CommonResult.success(manageHomePageManager.homePageList(user, appBaseInfo.getUuid()));
    }

    /**
     * 类目页列表接口
     */
    @PostMapping("/category/list")
    public Result<List<MallCategoryRespDto>> categoryList(@RequestBody MallCategoryReqDto mallCategoryReq) {
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        return CommonResult.success(manageHomePageManager.categoryList(mallCategoryReq, user, appBaseInfo.getUuid()));
    }

    /**
     * 商品列表
     *
     * @param goodsRequestDto
     * @return
     */
    @PostMapping("/goods/list")
    public Result<List<MallHomeGoodDto>> goodsList(@RequestBody MallGoodsRequestDto goodsRequestDto) {
        GoodsQuery goodsQuery = new GoodsQuery();
        goodsQuery.setTitle(goodsRequestDto.getGoodsName());
        goodsQuery.setStatus(1);
        if (StringUtils.hasText(goodsRequestDto.getEquipmentModel())) {
            goodsQuery.setEquipmentModels(List.of(goodsRequestDto.getEquipmentModel()));
        }
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        Integer querySource = goodsRequestDto.getQuerySource();
        if (Objects.nonNull(goodsRequestDto.getCouponId())) {
            querySource = GoodsConstant.QuerySourceEnum.QUERY_SOURCE_1.code;
        }
        return CommonResult.success(manageHomePageManager.goodsList(goodsQuery, user, goodsRequestDto.getCouponId(), appBaseInfo.getUuid(), querySource));
    }

    /**
     * 购物车数量
     *
     * @return
     */
    @PostMapping("/shopping/cart")
    public Result<Integer> shoppingCartRed() {
        ZnsUserEntity user = getLoginUserOrDefaultUser();

        //查询购物车可用的sku数据
        List<ShoppingCartDo> carSkuList = shoppingCartService.findYesSkuByUserId(user.getId());
        if (CollectionUtils.isEmpty(carSkuList)) {
            return CommonResult.success(0);
        }

        //购物车查询sku（包含被逻辑删除的sku）
        Map<Long, ShoppingCartDo> shoppingCartMap = carSkuList.stream().collect(Collectors.toMap(ShoppingCartDo::getSkuId, Function.identity(), (k1, k2) -> k2));
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findShoppingCarByIds(shoppingCartMap.keySet());
        if (CollectionUtils.isEmpty(skuEntities)) {
            return CommonResult.success(0);
        }
        //查询goods
        List<Long> goodIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(goodIds);
        Map<Long, ZnsGoodsEntity> goodsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(goodsEntities)) {
            goodsMap = goodsEntities.stream().collect(Collectors.toMap(ZnsGoodsEntity::getId, Function.identity(), (k1, k2) -> k2));
        }
        List<ShoppingCartDo> enableList = new ArrayList<>(); //可用sku
        for (ZnsGoodsSkuEntity skuEntity : skuEntities) {
            ZnsGoodsEntity goodsEntity = goodsMap.get(skuEntity.getGoodsId());
            if (skuEntity.getIsDelete() != 0 || skuEntity.getStatus() != 1 || skuEntity.getStock() <= 0
                    || goodsEntity == null || goodsEntity.getStatus() != 1) {
                //被删除 or 未上架 or 没有库存
                continue;
            }
            enableList.add(shoppingCartMap.get(skuEntity.getId()));
        }

        int size = 0;
        if (!CollectionUtils.isEmpty(enableList)) {
            size = enableList.stream().mapToInt(ShoppingCartDo::getCount).sum();
        }
        return CommonResult.success(size);
    }

    /**
     * 商城首页弹窗
     *
     * @return
     */
    @PostMapping("/pop")
    public Result<MallHomePopDto> mallHomePop() {
        ZnsUserEntity user = getLoginUserNotNull();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String remoteIp = IpUtil.getRemoteIp(request);
        String countryName = RegionUtil.getCountry(remoteIp);
        log.info("[mallHomePop]--------用户={},ip={},国家={}", user.getId(),remoteIp, countryName);
        return CommonResult.success(manageHomePageManager.checkIpMallCountry(countryName,user,getLanguageCode()));
    }

    /**
     * 商品政策信息
     *
     * @return
     */
    @PostMapping("/goods/policy")
    public Result<List<GoodsPolicyListResponseDto>> goodsPolicy() {
        ZnsUserEntity loginUser = getLoginUserOrDefaultUser();
        return CommonResult.success(manageHomePageManager.getPolicies(loginUser));
    }


    /**
     * 用户商城国家校验
     * @param requestDto
     * @return
     * @since 474
     */
    @PostMapping("/userMallCountry/check")
    public Result<CheckUserMallCountryResponseDto> checkUserMallCountry(@RequestBody CheckUserMallCountryRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUserOrDefaultUser();
        return CommonResult.success(manageHomePageManager.checkUserMallCountry(loginUser,requestDto));
    }

}
