package com.linzi.pitpat.api.activityservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityPlaylistRequest;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityIdRequestDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityResultResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.RoomActivityListRequestDto;
import com.linzi.pitpat.api.activityservice.manager.RoomManager;
import com.linzi.pitpat.api.activityservice.manager.RunActivityManager;
import com.linzi.pitpat.api.activityservice.manager.RunPlaylistManager;
import com.linzi.pitpat.api.activityservice.vo.ActivityPlaylistVo;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomQueryDto;
import com.linzi.pitpat.data.activityservice.manager.ActivityUserManager;
import com.linzi.pitpat.data.activityservice.manager.RunCheatManager;
import com.linzi.pitpat.data.activityservice.manager.api.AppActivityManager;
import com.linzi.pitpat.data.activityservice.manager.api.AppActivityPageManager;
import com.linzi.pitpat.data.activityservice.manager.api.RunActivityUserManager;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityWatchUserNumRespDto;
import com.linzi.pitpat.data.activityservice.model.dto.AppActivityRateLimitDto;
import com.linzi.pitpat.data.activityservice.model.dto.PreventionCheatRuleResponseDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.query.MilestonePopQuery;
import com.linzi.pitpat.data.activityservice.model.request.ActivityIdRequest;
import com.linzi.pitpat.data.activityservice.model.request.ActivityPageRequest;
import com.linzi.pitpat.data.activityservice.model.request.ActivityParticipationRequest;
import com.linzi.pitpat.data.activityservice.model.request.ActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.ActivityUseCouponRequest;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.MilestonePopRequest;
import com.linzi.pitpat.data.activityservice.model.request.RankingResultsRequest;
import com.linzi.pitpat.data.activityservice.model.request.ReportUserRunStatusRequest;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.request.StartRunRequest;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityCheckDetailResp;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityUseCouponResp;
import com.linzi.pitpat.data.activityservice.model.resp.CanChallengeResp;
import com.linzi.pitpat.data.activityservice.model.resp.GroupRunRateLimitResp;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityCheckVo;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityPageVo;
import com.linzi.pitpat.data.activityservice.model.vo.MyActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.MyRecordsActivityListVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewerPKVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialCumulativeActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialTeamActivityListVO;
import com.linzi.pitpat.data.activityservice.model.vo.OfficialTeamActivityPageVo;
import com.linzi.pitpat.data.activityservice.model.vo.RaceTabVo;
import com.linzi.pitpat.data.activityservice.model.vo.RoomPropRouteConfigDto;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityIntroduceVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityTeamVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserVO;
import com.linzi.pitpat.data.activityservice.model.vo.SimpleRunActivityVO;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityTypeRuleVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.RecordActivityTypeVo;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityRateLimitService;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import com.linzi.pitpat.data.activityservice.service.MilestonePopService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunRecordService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.UserFriendManager;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.activity.MilestonePop;
import com.linzi.pitpat.data.enums.ActivityClassifyTypeEnum;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.request.CanChallengeRequest;
import com.linzi.pitpat.data.request.InviteRunnerRequest;
import com.linzi.pitpat.data.request.activity.DayRecordsRequest;
import com.linzi.pitpat.data.request.activity.GetCheatRuleRequest;
import com.linzi.pitpat.data.request.activity.GetOfficialActivityRequest;
import com.linzi.pitpat.data.request.activity.GetRankingRunDataDetailRequest;
import com.linzi.pitpat.data.request.course.TaskRequest;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.vo.MapListVo;
import com.linzi.pitpat.data.vo.RunRouteDetailVO;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.dto.request.NewPkActivityRequestDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 跑步活动接口
 *
 * @description: 跑步活动
 * @author: yangpeng
 * @className: RunActivityController
 * @packageName: com.linzi.pitpat.api.controller.app
 * @version: V1.0
 * @date: 2021-12-29 10:29 AM
 **/

@RestController
@RequestMapping("/app/runActivity")
@Slf4j
@RequiredArgsConstructor
public class RunActivityController extends BaseAppController {

    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Autowired
    private RunActivityUserManager runActivityUserManager;
    @Resource
    private ZnsRunRouteService runRouteService;
    @Resource
    private ZnsUserRunRecordService userRunRecordService;
    private final ZnsUserService userService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    private final ISysConfigService sysConfigService;
    @Value("${admin.server.gamepush}")
    private String gameDomain;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private UserRunDataDetailsSecondService userRunDataDetailsSecondService;
    @Autowired
    private ActivityEquipmentConfigService activityEquipmentConfigService;
    private final AppUpgradeService appUpgradeService;
    @Resource
    private AssistActivitityService assistActivitityService;

    @Autowired
    private UserCouponService userCouponService;

    @Autowired
    private PkChallengeRecordService pkChallengeRecordService;

    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;


    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    @Autowired
    private MilestonePopService milestonePopService;

    @Resource
    private RunPlaylistManager runPlaylistManager;

    @Autowired
    private ActivityRateLimitService rateLimitService;

    @Resource
    private ZnsUserAccountService znsUserAccountService;
    @Autowired
    private RunActivityManager activityManager;
    @Resource
    private ActivityAwardCurrencyBizService activityAwardCurrencyBizService;
    @Resource
    private AppActivityManager newActivityManager;
    private final MainActivityBizService mainActivityBizService;
    @Resource
    private SubActivityService subActivityService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final UserFriendManager userFriendManager;
    @Resource
    private UserFriendMatchService userFriendMatchService;

    private final ActivityUserManager activityUserManager;

    private final RunActivityBizService runActivityBizService;
    private final AppActivityPageManager appActivityPageManager;
    private final UserCouponManager userCouponManager;

    private final UserIdentityService userIdentityService;
    private final RunCheatManager runCheatManager;

    private final RoomManager roomManager;

    /**
     * 创建跑步跑活动
     */
    @PostMapping("/launchActivity")
    public Result launchActivity(@RequestBody RunActivityRequest runActivity) {
        // 运营后台调用创建跑步活动,则发起人就是机器人。如果是否是运营后台发起的跑步活动,则当前用户需要改成机器人
        ZnsUserEntity currentUser = null;
        if (null != runActivity.getIsRobotStart() && 1 == runActivity.getIsRobotStart().intValue()) {
            // 运营后台机器人发起的跑步活动
            currentUser = userService.findById(runActivity.getRobotUserId());
        } else {
            // APP用户发起的跑步活动
            currentUser = getLoginUser();
        }
        if (null == currentUser) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.creator.notExist"));
        }
        // 设置活动是否是机器人发起
        runActivity.setIsRobotStart(currentUser.getIsRobot());

        // 校验发起跑活动参数
        Result erroResult = runActivityBizService.checkRunActivityParams(runActivity, currentUser);
        if (null != erroResult) {
            return erroResult;
        }
        runActivity.setAppVersion(getAppVersion());
        return activityStrategyContext.doLaunchActivity(runActivity, currentUser);
    }

    /**
     * 创建新pk活动，内部调试用
     *
     * @param request
     * @return
     */
    @PostMapping("/launchNewPkActivity")
    public Result launchNewPkActivity(@RequestBody NewPkActivityRequestDto request) {
        ZnsRunActivityEntity activity = activityManager.createNewPkActivity(request, 0, null);
        return CommonResult.success(activity);
    }

    @PostMapping("/checkTeamRun")
    public Result checkTeamRun(@RequestBody ActivityRequest runActivity) {
        if (userIdentityService.isNpc(runActivity.getUserId())) {
            return CommonResult.success();
        }
        Integer dailyCount = runActivityUserService.findDailyCountByUserAndActivityType(runActivity.getUserId(), runActivity.getActivityType(), null);
        ZnsRunActivityConfigEntity configEntity = runActivityConfigService.getByType(runActivity.getActivityType(), null);
        Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
        Integer maxDailyCount = MapUtil.getInteger(jsonObject.get("maxDailyParticipations"));
        if (maxDailyCount - dailyCount <= 0) {
            return CommonResult.fail(I18nMsgUtils.getMessage("activity.enroll.daily.failed"));
        }
        return CommonResult.success();
    }

    /**
     * 发起活动页面相关配置数据
     */
    @PostMapping("/activityPage")
    public Result activityPage(@RequestBody ActivityRequest request) {
        Long activityConfigId = request.getActivityConfigId();
        // 校验活动类型是否存在
        if (null == activityConfigId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.id.notExit"));
        }
        ZnsRunActivityConfigEntity activityConfig = null;
        if (request.getActivityConfigId() != 2) {
            activityConfig = runActivityConfigService.findRunActivityConfig(activityConfigId);
        } else {
            activityConfig = runActivityConfigService.getByType(2, request.getActivityTypeSub());
        }

        if (null == activityConfig) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.config.notExit"));
        }
        ZnsUserEntity userEntity = getLoginUser();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        Map<String, Object> data = appActivityPageManager.runActivityPage(activityConfigId, userEntity, checkVersion, getAppVersion(), activityConfig, getAppVersion());
        return CommonResult.success(data);
    }

    /**
     * 发起活动页面相关配置数据 (新)
     *
     * @tag 2.6.0
     */
    @PostMapping("/activityPage/v1")
    public Result<ActivityPageVo> activityPageV1(@RequestBody ActivityPageRequest request) {
        Integer appVersion = getAppVersion();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), appVersion);

        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(request.getActivityType(), request.getActivityTypeSub());

        if (null == activityConfig) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        ZnsUserEntity userEntity = getLoginUser();

        //触发与好友的用户数据
        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activityConfig.getActivityType()) ||
                RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityConfig.getActivityType())) {
            userFriendManager.updateUserFriendData(userEntity.getId());
        }

        ActivityPageVo data = appActivityPageManager.runActivityPageV1(activityConfig, userEntity, checkVersion, appVersion);

        //获取语音开关
        Map<String, Integer> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
        Integer voiceSwitch = runActivityConfigService.getVoiceSwitch(jsonObject.get("voiceSwitch"), getLoginUser());
        data.setVoiceSwitch(voiceSwitch);
        return CommonResult.success(data);
    }

    /**
     * 活动详情
     */
    @PostMapping("/activityDetail")
    public Result<RunActivityDetailVO> activityDetail(@RequestBody ActivityRequest request) {
        Long activityId = request.getActivityId();
        // 校验活动类型是否存在
        if (null == activityId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        ZnsUserEntity userEntity = getLoginUser();
        if (null == userEntity) {
            // 运营后台请求过来会出现用户为空的情况
            ZnsRunActivityUserEntity activityLaunchUser = runActivityUserService.findActivityLaunchUser(activityId);
            if (null != activityLaunchUser) {
                userEntity = userService.findById(activityLaunchUser.getUserId());
            } else {
                userEntity = new ZnsUserEntity();
                userEntity.setId(0L);
            }
        }
        ZnsUserAccountEntity userAccount = znsUserAccountService.getByUserId(userEntity.getId());
        if (Objects.isNull(userAccount)) {
            return CommonResult.fail(ActivityError.RUN_DATA_NO_END.getCode(), ActivityError.RUN_DATA_NO_END.getMsg());
        }
        // 查询活动详情
        RunActivityDetailVO activityDetailVO = activityStrategyContext.activityDetail(activityId, userEntity, request.getActivityUserStatus());
        buildAssistActivity(userEntity, activityDetailVO);

        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(activityId);
        //非官方活动全球化
        List<Integer> activityTypeList = Arrays.asList(1, 2, 11, 12);
        if (!Objects.equals(znsRunActivityEntity.getActivityTypeSub(), 3)) {
            if (activityTypeList.contains(znsRunActivityEntity.getActivityType())) {
                Currency currency = new Currency();
                activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, znsRunActivityEntity.getId(), userEntity.getId());
                activityDetailVO.setCurrency(currency);
            } else {
                activityDetailVO.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode()));
            }
        }
        List<Map<String, Object>> mapList = activityDetailVO.getRunActivityHistoryUsers();
        if (mapList != null && mapList.size() > 0) {
            for (Map<String, Object> data : mapList) {
                Object obj = data.get("rewardTime");
                if (obj != null && obj instanceof String) {
                    log.info("map data是字符串类型 = " + obj);
                    ZonedDateTime time = DateTimeUtil.parse(obj.toString());
                    data.put("rewardTime", time != null ? time.toInstant().toEpochMilli() : null);
                } else if (obj != null && obj instanceof ZonedDateTime) {
                    log.info("map data是日期类型 = " + obj);
                    data.put("rewardTime", ((ZonedDateTime) obj).toInstant().toEpochMilli());
                } else {
                    log.info("map 不是字符串类型 = " + obj + "，class =" + (obj != null ? obj.getClass() : "null"));
                }
            }
        }

        //根据币种重设金额小数位
        String currencyCode = userAccount.getCurrencyCode();
        resetAmountScale(currencyCode, activityDetailVO);

        return CommonResult.success(activityDetailVO);
    }

    /**
     * 根据币种重设金额小数位
     *
     * @param currencyCode
     * @param activityDetailVO
     */
    private void resetAmountScale(String currencyCode, RunActivityDetailVO activityDetailVO) {
        List<RunActivityUserVO> activityUsers = activityDetailVO.getActivityUsers();
        if (!CollectionUtils.isEmpty(activityUsers)) {
            //参赛用户 - 金额小数位修改
            for (RunActivityUserVO activityUser : activityUsers) {
                BigDecimal runAward = I18nConstant.currencyFormat(currencyCode, activityUser.getRunAward());
                activityUser.setRunAward(runAward);
                BigDecimal myRewardAmount = I18nConstant.currencyFormat(currencyCode, activityUser.getMyRewardAmount());
                activityUser.setMyRewardAmount(myRewardAmount);
            }
        }

        //活动总奖金
        BigDecimal activityTotalBonus = I18nConstant.currencyFormat(currencyCode, activityDetailVO.getActivityTotalBonus());
        activityDetailVO.setActivityTotalBonus(activityTotalBonus);
    }


    /**
     * 用户可用券数量
     *
     * @param request
     * @return
     */
    @PostMapping("/countCanUseCoupon")
    public Result<ActivityUseCouponResp> countCanUseCoupon(@RequestBody ActivityUseCouponRequest request) {
        ActivityUseCouponResp resp = new ActivityUseCouponResp();
        ZnsUserEntity userEntity = getLoginUser();
        if (Objects.isNull(userEntity)) {
            resp.setCanUseCoupon(0);
            return CommonResult.success(resp);
        }
        Integer canUseCouponNum = userCouponManager.countUserCanUseCouponNum(request.getActivityId(), userEntity.getId());
        resp.setCanUseCoupon(canUseCouponNum);
        return CommonResult.success(resp);

    }


    /**
     * 里程碑节点弹窗
     *
     * @param request
     * @return
     */
    @PostMapping("/Popup")
    public Result milestonePopRecord(@RequestBody MilestonePopRequest request) {
        if (request.getActivityId() == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error")); //Activity ID does not exist."活动id参数不存在"
        }
        if (request.getUserId() == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error")); //Active does not exist "用户id参数不存在"
        }
        if (request.getMileageNode() == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error")); //Goal Run pop-up parameters do not exist,"里程碑弹窗节点参数不存在"
        }
        MilestonePop milestonePop = new MilestonePop();
        BeanUtils.copyProperties(request, milestonePop);
        MilestonePop milestonePopDB = milestonePopService.findOne(MilestonePopQuery.builder()
                .activityId(request.getActivityId())
                .userId(request.getUserId())
                .mileageNode(request.getMileageNode())
                .build());

        if (Objects.isNull(milestonePopDB)) {
            boolean result = milestonePopService.save(milestonePop);
            if (!result) {
                return CommonResult.fail(I18nMsgUtils.getMessage("activity.milestone.pop.saveFailed")); //Goal Run pop-up record failed to save
            }
            return CommonResult.success();
        }
        return CommonResult.fail(I18nMsgUtils.getMessage("activity.milestone.pop.hasSaved")); //The pop-up record has been saved
    }

    private void buildAssistActivity(ZnsUserEntity userEntity, RunActivityDetailVO activityDetailVO) {
        Long assistActivityId = activityDetailVO.getAssistActivityId();
        if (assistActivityId != null && assistActivityId > 0) {
            AssistActivitity activitity = assistActivitityService.selectAssistActivitityById(assistActivityId);
            if (activitity != null) {
                activityDetailVO.setAssistActivityUrl(activitity.getActivityUrl() + "?activityId=" + activityDetailVO.getActivityId());
                ZonedDateTime startTime = activitity.getStartTime();
                ZonedDateTime endTime = activitity.getEndTime();
                ZonedDateTime now = ZonedDateTime.now();
                if (now.isBefore(startTime) || now.isAfter(endTime) || activitity.getStatus() != 1) {
                    activityDetailVO.setAssistActivityId(0L);
                }
            } else {
                activityDetailVO.setAssistActivityId(0L);
            }
        }
    }

    /**
     * 活动详情--全部报名信息
     */
    @PostMapping("/activityDetail/moreEnrollUser")
    public Result moreEnrollUser(@RequestBody ActivityRequest request) {
        Map<String, Object> data = new HashMap<>();
        List<UserSimpleVo> activitySimpleUser = runActivityBizService.findActivitySimpleUser(request.getActivityId(), 999);
        data.put("enrollUsers", activitySimpleUser);
        return CommonResult.success(data);
    }

    /**
     * 活动帮助信息
     *
     * @return
     */
    @PostMapping("/activityDetail/activityHelp")
    public Result activityHelp() {
        String config = sysConfigService.selectConfigByKey("activity.detail.help");
        if (!StringUtils.hasText(config)) {
            return CommonResult.success();
        }
        Map<String, Object> jsonObject = JsonUtil.readValue(config);
        return CommonResult.success(jsonObject);
    }

    /**
     * 活动介绍页
     */
    @PostMapping("/activityIntroduce")
    public Result activityIntroduce(@RequestBody ActivityRequest request) {
        Long activityConfigId = request.getActivityConfigId();
        if (null == activityConfigId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.findRunActivityConfig(activityConfigId);
        if (null == activityConfig) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        // 查询活动介绍页
        ZnsUserEntity userEntity = getLoginUser();
        RunActivityIntroduceVO activityIntroduceVO = appActivityPageManager.runActivityIntroduce(activityConfigId, userEntity, activityConfig.getActivityType());
        return CommonResult.success(activityIntroduceVO);
    }

    /**
     * 官方赛事活动用户分页加载
     *
     * @param request
     * @return
     */
    @PostMapping("/activityUser")
    public Result activityUser(@RequestBody ActivityRequest request) {
        ZnsUserEntity userEntity = getLoginUser();
        ZnsRunActivityEntity activityEntity = runActivityService.findById(request.getActivityId());
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        //判断是否有其他完赛用户数据正在处理
        AtomicInteger times = new AtomicInteger();
        String key = RedisConstants.USER_RUN_ACTIVITY_DETAIL_PROCESS + request.getActivityId();
        //z最多等待1s
        while (redisTemplate.hasKey(key) && times.get() < 5) {
            try {
                times.getAndIncrement();
                log.info("活动{}正在等待计算其他用户结算数据{},等待次数{}", request.getActivityId(), redisTemplate.opsForSet().members(key),
                        times.get());
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
                log.error("睡眠被打断", e);
            }
        }
        Map<String, Object> data = activityUserManager.pageQueryActivityUsers(activityEntity, userEntity.getId(), request.getPageNum(), request.getPageSize(), request.getActivityUserStatus());
        return CommonResult.success(data);
    }

    /**
     * 获取防作弊规则
     *
     * @return
     */
    @PostMapping("/getPreventionCheatRule")
    public Result<PreventionCheatRuleResponseDto> getPreventionCheatRule(@RequestBody GetCheatRuleRequest request) {
        ZnsUserEntity loginUser = userService.findById(request.getUserId());

        Long activityId = null;
        if (Objects.isNull(request.getActivityId())) {
            Object o = redisTemplate.opsForValue().get(RedisConstants.USER_CURRENT_ACTIVITY_ID + request.getUserId());
            if (Objects.nonNull(o)) {
                activityId = Long.valueOf((String) o);
            }
        } else {
            activityId = request.getActivityId();
        }
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(activityId, getZoneId());

        PreventionCheatRuleResponseDto preventionCheatRule = runCheatManager.getPreventionCheatRule(loginUser.getId(), activityNew, request.getIsCourse());
        try {
            log.info("删除活动id缓存，key=" + RedisConstants.USER_CURRENT_ACTIVITY_ID + loginUser.getId());
            redisTemplate.delete(RedisConstants.USER_CURRENT_ACTIVITY_ID + loginUser.getId());
        } catch (Exception e) {
            log.info("getPreventionCheatRule 删除活动缓存失败");
        }
        return CommonResult.success(preventionCheatRule);
    }

    /**
     * 组队跑活动邀请更多好友
     */
    @PostMapping("/inviteRunner")
    public Result inviteRunner(@RequestBody InviteRunnerRequest request) {
        // 校验参数
        Result errorResult = runActivityBizService.checkInviteRunner(request);
        if (null != errorResult) {
            return errorResult;
        }
        ZnsUserEntity user = getLoginUser();
        ZnsRunActivityEntity activity = runActivityService.findById(request.getActivityId());
        if (RunActivityTypeEnum.TEAM_RUN.getType().equals(activity.getActivityType())) {
            Integer appVersion = getAppVersion();
            List<Long> activityUserIds = request.getActivityUserIds();
            for (Long activityUserId : activityUserIds) {
                Result error = runActivityUserManager.checkEnrollCount(user.getId(), activityUserId, 1, null, true);
                if (error != null) return error;
            }
        }

        // 添加活动参与者
        runActivityUserManager.addRunActivityUsers(request.getActivityId(), request.getActivityUserIds(), user.getId(), user.getFirstName(), activity, null, new RunActivityRequest());
        // 4. 更新活动参与人数
        activityStrategyContext.updateActivityUser(activity, user);
        return CommonResult.success();
    }

    /**
     * 获取每日活动参与情况
     *
     * @param request
     * @return
     */
    @PostMapping("/activityParticipation")
    public Result activityParticipation(@RequestBody ActivityParticipationRequest request) {
        Map<String, Object> data = new HashMap<>();
        ZnsUserEntity user = getLoginUser();
        ZonedDateTime startDateTime = DateUtil.parseStr2Date(request.getStartTime(), DateUtil.YYYY_MM_DD);
        ZonedDateTime endDateTime = DateUtil.getEndOfDate(DateUtil.parseStr2Date(request.getEndTime(), DateUtil.YYYY_MM_DD));
        Integer appVersion = getAppVersion();
        List<String> activityParticipation = runActivityService.getActivityParticipation(startDateTime, endDateTime, user.getId(), appVersion);
        data.put("list", activityParticipation);
        return CommonResult.success(data);
    }

    /**
     * 我参加的活动赛事纪录-活动类型
     *
     * @return
     */
    @PostMapping("/myRecordsActivityTypes")
    public Result<List<RecordActivityTypeVo>> myRecordsActivityTypes() {
        String languageCode = getLanguageCode();
        List<RecordActivityTypeVo> list = appActivityPageManager.myRecordsActivityTypes(languageCode);
        return CommonResult.success(list);
    }

    /**
     * 我参加的活动赛事纪录
     *
     * @param request
     * @return
     */
    @PostMapping("/myRecords")
    public Result<MapListVo<MyRecordsActivityListVo>> myRecords(@RequestBody ActivityRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        Integer appType = getAppType();
        List<MyRecordsActivityListVo> list = appActivityPageManager.getMyRecords(request.getPageNum(), request.getPageSize(), request.getActivityType(),
                loginUser, appType, TimeZone.getTimeZone(zoneId), getAppVersion());
        MapListVo<MyRecordsActivityListVo> map = new MapListVo();
        map.setList(list);
        return CommonResult.success(map);
    }

    /**
     * 我的参赛纪录--当日
     * myrace-日历
     *
     * @param request
     * @return
     */
    @PostMapping("/myDayRecords")
    public Result<MapListVo<RunActivityTeamVO>> myDayRecords(@RequestBody DayRecordsRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        Integer appVersion = getAppVersion();
        Integer appType = getAppType();
        String languageCode = loginUser.getLanguageCode(); // 本来应该传系统语言，但是由于H5只传了固定语言，所以这里只能用用户语言了
        boolean checkVersion = appUpgradeService.isCheckVersion(appType, appVersion);
        List<RunActivityTeamVO> list = appActivityPageManager.myDayRecords(DateUtil.parseStr2Date(request.getDate(), DateUtil.YYYY_MM_DD), loginUser, TimeZone.getTimeZone(zoneId), checkVersion, appVersion, appType, languageCode);
        MapListVo<RunActivityTeamVO> map = new MapListVo();
        map.setList(list);
        return CommonResult.success(map);
    }

    /**
     * 获取老的活动类型及赛事规则
     *
     * @param request
     * @return
     */
    @PostMapping("/getOldType")
    public Result<ActivityTypeRuleVo> getOldType(@RequestBody ActivityIdRequest request) {
        ActivityTypeRuleVo vo = activityManager.getActivityTypeRule(request.getActivityId(), getAppVersion(), getLoginUser().getId());
        return CommonResult.success(vo);
    }

    private List<RunActivityTeamVO> mockMyDayRecords() {
        RunActivityTeamVO vo = new RunActivityTeamVO();
        vo.setActivityTitle("123");
        vo.setTeamCurrentNum(10);
        vo.setTeamCurrentNum(5);
        vo.setActivityType(10);
        vo.setRank(2);
        vo.setId(123l);
        vo.setStartTime(ZonedDateTime.now());
        vo.setEndTime(ZonedDateTime.now());
        vo.setStatus(0);
        return Arrays.asList(vo);
    }

    /**
     * 我参加的历史活动
     *
     * @param request
     * @return
     */
    @PostMapping("/historyActivity")
    public Result historyActivity(@RequestBody ActivityRequest request) {
        ZnsUserEntity user = getLoginUser();
        List<RunActivityUserVO> list = runActivityUserService.findUserHistoryActivity(request.getPageNum(), request.getPageSize(),
                user.getId(), request.getActivityConfigId());
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return CommonResult.success(map);
    }

    /**
     * 用户接受、报名活动前检查
     *
     * @param request 活动实体
     */
    @PostMapping("/checkHandleActivity")
    public Result checkHandleActivity(@RequestBody HandleActivityRequest request) {
        ZnsUserEntity user = getLoginUser();
        //todo 3.0的加拿大用户不能参加老官方活动，下个版本可以去掉
        Result canJoin = activityStrategyContext.checkCanJoin(user, request.getActivityId(), getAppVersion());
        if (canJoin != null)
            return canJoin;
        Result result = activityStrategyContext.checkHandleActivity(request.getActivityId(), request.getUserStatus(), user);
        return result;
    }

    /**
     * 校验活动允许入场时间，目前针对官方多人同跑
     *
     * @param request
     * @return
     */
    @PostMapping("/checkWaitTime")
    public Result<ActivityCheckVo> checkWaitTime(@RequestBody ActivityIdRequest request) {
        ActivityCheckVo vo = runActivityUserManager.checkWaitTime(request.getActivityId());
        return CommonResult.success(vo);
    }


    /**
     * 用户取消/接受/拒绝 跑步活动
     * sign up 按钮
     *
     * @param request
     */
    @PostMapping("/handleActivity")
    public Result handleActivity(@RequestBody HandleActivityRequest request) {
        ZnsUserEntity user = getLoginUser();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        log.info("用户取消或接受或拒绝跑步活动 activityId = " + request.getActivityId() + ",userId = " + user.getId());
        //todo 3.0的加拿大用户不能参加老官方活动，下个版本可以去掉
        Result canJoin = activityStrategyContext.checkCanJoin(user, request.getActivityId(), getAppVersion());
        if (canJoin != null)
            return canJoin;
        request.setAppVersion(getAppVersion());

        //Todo 版本兼容问题，3.6版本后可去掉
        if (request.getUserStatus() != -1) {
            handleActivityCheckVersion(request.getActivityId(), getLoginUser());
        }


        Result result = activityStrategyContext.handleUserActivityState(request.getActivityId(), request.getUserStatus(), user,
                request.getPassword(), request.getRunningGoals(), true, request.getTaskId(), request, checkVersion);
        return result;
    }

    private void handleActivityCheckVersion(Long activityId, ZnsUserEntity loginUser) {
        if (getAppVersion() < 3060) {
            ZnsRunActivityEntity activity = runActivityService.findById(activityId);
            if (Objects.nonNull(activity)) {
                if (activity.getActivityType() == 2 && activity.getActivityTypeSub() == 2) {
                    String activityConfig = activity.getActivityConfig();
                    Map<String, String> map = JsonUtil.readValue(activityConfig);
                    String s = map.get(ConfigKeyEnums.FRIEND_PK_AWARD_CONFIG.getCode());
                    if (StringUtils.hasText(s)) {
                        //避免反复弹窗
                        UserFriendMatch friendMatch = userFriendMatchService.getByUserIdAndActivity(loginUser.getId(), activityId);
                        if (Objects.nonNull(friendMatch)) {
                            friendMatch.setIsPop(1);
                            userFriendMatchService.update(friendMatch);
                        }

                        throw new BaseException(I18nMsgUtils.getMessage("app.version.is.low"));
                    }
                }
            }
        }

    }

    /**
     * 获取活动状态
     *
     * @param request
     */
    @PostMapping("/getActivityState")
    public Result getActivityState(@RequestBody ActivityIdRequest request) {
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(request.getActivityId(), getZoneId());
        int count = 0;
        while (activityNew.getActivityState() == 0 && ZonedDateTime.now().compareTo(activityNew.getActivityStartTime()) >= 0 && count <= 10) {
            try {
                TimeUnit.MILLISECONDS.sleep(300);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("第{}次查询应该启动的活动{}", count, activityNew.getId());
            activityNew = mainActivityBizService.getActivityNew(request.getActivityId(), getZoneId());
            count++;
        }


        if (MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
            ZnsRunActivityEntity activityEntity = activityNew.getRunActivity();
            if (activityEntity.getActivityState() == 0) {
                if (activityEntity.getActivityStartTime().compareTo(ZonedDateTime.now()) <= 0) {
                    activityEntity.setActivityState(1);
                }
            }
            return CommonResult.success(activityEntity.getActivityState());
        } else {
            return CommonResult.success(activityNew.getActivityState());
        }
    }

    /**
     * 路线详情
     */
    @PostMapping("/routeDetail")
    public Result routeDetail(@RequestBody ActivityRequest request) {
        RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(request.getRouteId());
        return CommonResult.success(routeDetailVO);
    }

    /**
     * 用户进入活动详情前判断
     *
     * @param request
     */
    @PostMapping("/checkEnterDetail")
    public Result<ActivityCheckDetailResp> checkEnterDetail(@RequestBody ActivityIdRequest request) {
        ActivityCheckDetailResp activityCheckDetailResp = new ActivityCheckDetailResp();
        ZnsRunActivityEntity activityEntity = runActivityService.findById(request.getActivityId());
        if (activityEntity != null) {
            if (StringUtils.hasText(activityEntity.getBatchNo())) {
                // status :  状态，默认0,1：上架，-1下架
                // activityState : 活动状态：0表示未开始，1 表示进行中，2表示已结束，-1表示活动已取消
                ZnsRunActivityEntity running = runActivityService.selectActivityByBatchNoStatusActivityState(activityEntity.getBatchNo(), Arrays.asList(0, 1), Arrays.asList(0, 1));
                if (running == null) {
                    activityCheckDetailResp.setCheckCheatSwitch(0);
                }
            } else {
                if (Objects.equals(activityEntity.getStatus(), -1) || Arrays.asList(2, -1).contains(activityEntity.getActivityState())) {
                    activityCheckDetailResp.setCheckCheatSwitch(0);
                }
            }
        }
        return CommonResult.success(activityCheckDetailResp);

    }

    /**
     * 用户进入活动跑道前判断
     *
     * @param request
     */
    @PostMapping("/checkReportUserRun")
    public Result checkReportUserRun(@RequestBody ActivityIdRequest request) {
        Long activityId = request.getActivityId();
        if (null == activityId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error")); //参数缺失
        }
        ActivityTypeDto activityEntity = mainActivityBizService.getActivityNew(activityId, getZoneId());
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        if (StringUtils.hasText(request.getEquipmentNo()) || request.getEquipmentType() != null) {
            boolean flag = activityEquipmentConfigService.checkEquipmentType(request.getActivityId(), request.getEquipmentType(), request.getEquipmentNo());
            if (flag) {
                return CommonResult.fail(CommonError.DEV_TYPE_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.treadmill.typeMissMatch"));
            }
        }

        ZnsUserEntity user = getLoginUser();
        Result result = null;
        if (MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType())) {
            result = activityStrategyContext.checkReportUserRun(activityEntity.getRunActivity(), user);
        } else {
            result = newActivityManager.checkReportUserRun(activityEntity, user);
        }

        if (CommonError.SUCCESS.getCode().equals(result.getCode())) {
            Map<String, Object> preventionCheatRule = activityStrategyContext.isPopPreventionCheat(user.getId(), activityEntity);
            result.setData(preventionCheatRule);
            //推送3d房间号
            ZnsRunRouteEntity routeEntity = runRouteService.selectRunRouteById(activityEntity.getActivityRouteId());
            if (Objects.nonNull(routeEntity) && routeEntity.getRouteType() == 2) {
                if (activityEntity.getActivityType() == 4 && MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType())) {
                    ZnsRunActivityEntity runActivity = runActivityService.findById(activityEntity.getId());
                    Map<String, Object> jsonObjectConfig = JsonUtil.readValue(runActivity.getActivityConfig());
                    if (Objects.nonNull(jsonObjectConfig.get("runningGoals"))) {
                        List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
                        for (Integer runningGoal : runningGoals) {
                            Long activityGoalId = NumberUtils.getGoalImNumber(activityEntity.getId(), runningGoal, activityEntity.getTargetType());

                            //推送到游戏服务器端
                            for (int i = 0; i < 3; i++) {
                                try {
                                    GamePushUtils.addRoom(gameDomain, activityGoalId, 2, null);
                                    break;
                                } catch (Exception e) {
                                    log.info("请求游戏服务器异常:{}", e.getMessage());
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 用户进入活动跑道上报状态: 目前是当用户进入跑道的时候客户端会上报用户跑步中的状态。
     * 由于官方赛事用户可以重复跑，这样每次跑完需要更新zns_run_activity_user表中用户的最佳成绩。所以需要客户端上报用户跑步结束
     *
     * @param request
     */
    @PostMapping("/reportUserRunStatus")
    public Result reportUserRunStatus(@RequestBody ReportUserRunStatusRequest request) {
        Long activityId = request.getActivityId();
        Integer status = request.getStatus();
        if (null == activityId || null == status) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        ActivityTypeDto activityEntity = mainActivityBizService.getActivityNew(activityId, getZoneId());
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        if (!Arrays.asList(3, 4).contains(status)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.user.statusError")); //User status error
        }
        if (activityEntity.getActivityState() == -1) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.status.invalid"));
        }
        ZnsUserEntity user = getLoginUser();
        if (status == 3) {
            //活动缓存记录
            log.info("添加活动id缓存，key=" + RedisConstants.USER_CURRENT_ACTIVITY_ID + user.getId() + ",活动id=" + activityId);
            redisTemplate.opsForValue().set(RedisConstants.USER_CURRENT_ACTIVITY_ID + user.getId(), activityId.toString(), 5L, TimeUnit.MINUTES);
        }
        if (MainActivityTypeEnum.OLD.getType().equals(activityEntity.getMainType())) {
            return activityStrategyContext.handleReportUserRunStatus(activityEntity.getRunActivity(), user, status);
        } else {
            return newActivityManager.handleReportUserRunStatus(activityEntity, user, status);
        }
    }

    /**
     * 查询已完成的用户
     *
     * @param request
     */
    @PostMapping("/getCompleteActivityUser")
    public Result getCompleteActivityUser(@RequestBody ActivityIdRequest request) {
        Long activityId = request.getActivityId();
        if (null == activityId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(activityId, getZoneId());
        if (null == activityNew) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        Integer runningGoal = null;
        if (MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
            ZnsRunActivityEntity runActivity = activityNew.getRunActivity();
            if (runActivity.getActivityType() == 4) {
                ZnsUserEntity loginUser = getLoginUser();
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, loginUser.getId());
                if (Objects.nonNull(activityUser)) {
                    if (runActivity.getCompleteRuleType() == 1) {
                        runningGoal = activityUser.getTargetRunMileage();
                    } else {
                        runningGoal = activityUser.getTargetRunTime();
                    }
                    //活动缓存记录
                    log.info("添加活动id缓存，key=" + RedisConstants.USER_CURRENT_ACTIVITY_ID + loginUser.getId() + ",活动id=" + activityId);
                    redisTemplate.opsForValue().set(RedisConstants.USER_CURRENT_ACTIVITY_ID + loginUser.getId(), activityId.toString(), 5L, TimeUnit.MINUTES);
                } else {
                    Map<String, Object> jsonObject = JsonUtil.readValue(runActivity.getActivityConfig());
                    List<Integer> runningGoals = JsonUtil.readList(jsonObject.get("runningGoals"), Integer.class);
                    runningGoal = runningGoals.get(0);
                }
            }
        } else {
            if (activityNew.getTargetType() != 0) {
                ZnsUserEntity loginUser = getLoginUser();
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, loginUser.getId());
                if (Objects.nonNull(activityUser)) {
                    if (activityNew.getTargetType() == 1) {
                        runningGoal = activityUser.getTargetRunMileage();
                    } else {
                        runningGoal = activityUser.getTargetRunTime();
                    }
                    //活动缓存记录
                    log.info("添加活动id缓存，key=" + RedisConstants.USER_CURRENT_ACTIVITY_ID + loginUser.getId() + ",活动id=" + activityId);
                    redisTemplate.opsForValue().set(RedisConstants.USER_CURRENT_ACTIVITY_ID + loginUser.getId(), activityId.toString(), 5L, TimeUnit.MINUTES);
                } else {
                    //目标
                    List<Integer> goals = subActivityService.getAllSingleActByMain(activityNew.getId()).stream().map(SubActivity::getTarget)
                            .sorted().toList();
                    if (CollectionUtils.isEmpty(goals)) {
                        runningGoal = goals.get(0);
                    }
                }
            }
        }

        return runActivityUserService.getCompleteActivityUser(activityNew, runningGoal, request);
    }

    /**
     * 开始跑步
     *
     * @param request
     */
    @PostMapping("/startRun")
    public Result startRun(@RequestBody StartRunRequest request) {
        Long activityId = request.getActivityId();
        Long challengedUserId = request.getChallengedUserId();
        Integer challengeRank = request.getChallengeRank();

        if (null == activityId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        // 查找被挑战用户
        Long challengedDetailsId = 0L;
        if (null != challengedUserId && challengedUserId > 0) {
            ZnsRunActivityUserEntity challengedUser = runActivityUserService.findActivityUser(activityId, challengedUserId);
            if (null == challengedUser) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.user.challenged.notExist")); //The challenged user does not exist
            }
            challengedDetailsId = challengedUser.getRunDataDetailsId();
        }
        ZnsUserEntity user = getLoginUser();
        ActivityTypeDto activityTypeDto = mainActivityBizService.getActivityNew(activityId, getZoneId());
        if (MainActivityTypeEnum.OLD.getType().equals(activityTypeDto.getMainType())) {
            ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
            Result res = activityStrategyContext.checkRunActivityTime(activityEntity);
            if (Objects.nonNull(res)) {
                return res;
            }
        } else {
            Result res = newActivityManager.checkRunActivityTime(activityTypeDto, user);
            if (Objects.nonNull(res)) {
                return res;
            }
        }

        // 新增挑战记录
        Result result = userRunRecordService.addUserRunRecord(user, challengedUserId, activityId, challengedDetailsId, challengeRank, request.getTaskId(), activityTypeDto.getActivityType());
        // 处理跑步中
        runActivityUserService.updateActivityUserState(activityId, user.getId(), ActivityUserStateEnum.RUNING);
        //活动缓存记录
        log.info("添加活动id缓存，key=" + RedisConstants.USER_CURRENT_ACTIVITY_ID + user.getId() + ",活动id=" + activityId);
        redisTemplate.opsForValue().set(RedisConstants.USER_CURRENT_ACTIVITY_ID + user.getId(), activityId.toString(), 5L, TimeUnit.MINUTES);
        return result;
    }

    /**
     * 查询活动信息
     */
    @PostMapping("/activityInfo")
    public Result<RunActivityTeamVO> activityInfo(@RequestBody ActivityIdRequest request) {
        ZnsUserEntity userEntity = getLoginUser();
        RunActivityTeamVO runActivityTeamVO = appActivityPageManager.queryActivityInfo(request.getActivityId(), userEntity);
        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(request.getActivityId());
        List<Integer> typeList = Arrays.asList(1, 2, 5, 11, 12);
        if (typeList.contains(znsRunActivityEntity.getActivityType())) {
            Currency currency = new Currency();
            activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, request.getActivityId(), userEntity.getId());
            runActivityTeamVO.setCurrency(currency);
        } else {
            runActivityTeamVO.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode()));
        }
        return CommonResult.success(runActivityTeamVO);
    }

    /**
     * 活动结束上报
     */
    @PostMapping("/activityEnd")
    public Result activityEnd(@RequestBody ActivityIdRequest request) {
        Long activityId = request.getActivityId();
        if (null == activityId) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.error"));
        }
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        activityStrategyContext.handleActivityFinished(activityEntity);
        return CommonResult.success();
    }

    /**
     * 获取排行赛影子数据
     *
     * @param request
     * @return
     */
    @PostMapping("/getRankingRunDataDetail")
    public Result getRankingRunDataDetail(@RequestBody GetRankingRunDataDetailRequest request) {
        Long userId = request.getUserId();
        ZnsUserEntity userEntity = userService.findById(userId);
        if (Objects.isNull(userEntity)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.user.notExist"));
        }

        Long activityId = request.getActivityId();
        Long runDataDetailsId = null;
        if (StringUtils.hasText(request.getBatchNo())) {
            PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByBatchNoChallengeType(request.getBatchNo(), 1);
            ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsService.findById(pkChallengeRecord.getRunDataDetailsId());
            activityId = znsUserRunDataDetailsEntity.getActivityId();
            runDataDetailsId = znsUserRunDataDetailsEntity.getId();
        } else if (Objects.nonNull(request.getRunDataDetailsId()) && request.getRunDataDetailsId() > 0) {
            ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsService.findById(request.getRunDataDetailsId());
            activityId = znsUserRunDataDetailsEntity.getActivityId();
            runDataDetailsId = request.getRunDataDetailsId();
        } else {
            //查询用户的跑步记录id
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userId);
            if (Objects.isNull(activityUser)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.noData"));
            }
            if (Objects.isNull(activityUser.getRunDataDetailsId()) || activityUser.getRunDataDetailsId() == 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.noData"));
            }
            runDataDetailsId = activityUser.getRunDataDetailsId();
        }

        //活动缓存记录
        log.info("添加活动id缓存，key=" + RedisConstants.USER_CURRENT_ACTIVITY_ID + userEntity.getId() + ",活动id=" + activityId);
        redisTemplate.opsForValue().set(RedisConstants.USER_CURRENT_ACTIVITY_ID + userEntity.getId(), activityId.toString(), 5L, TimeUnit.MINUTES);
        Map<String, Object> data = new HashMap<>();
        data.put("nickname", userEntity.getFirstName());
        data.put("headPortrait", userEntity.getHeadPortrait());
        data.put("emailAddress", userEntity.getEmailAddressEn());
        ZnsUserRunDataDetailsEntity dataDetails = userRunDataDetailsService.findById(runDataDetailsId);
        Integer runTimeMillisecond = dataDetails.getRunTimeMillisecond();
        if (runTimeMillisecond == 0) {
            runTimeMillisecond = dataDetails.getRunTime() * 1000 + 999;
        }
        data.put("runTimeMillisecond", runTimeMillisecond);
        List<ZnsUserRunDataDetailsSecondEntity> secondEntities = userRunDataDetailsSecondService.getSecondsPageList(runDataDetailsId, request);
        data.put("list", secondEntities);
        return CommonResult.success(data);
    }

    /**
     * 获取排行赛影子数据/获取排行赛结果
     *
     * @param request
     * @return
     */
    @PostMapping("/rankingResults")
    public Result rankingResults(@RequestBody RankingResultsRequest request, HttpServletRequest httpServletRequest) {
        Integer type = request.getType();
        Long activityId = request.getActivityId();
        log.info("获取排行赛影子数据 type=" + type + ",activityId=" + activityId);
        ZnsUserEntity user = null;
        String emailAddress = null;
        if (request.getUserId() != null) {
            user = userService.findById(request.getUserId());
            emailAddress = user.getEmailAddressEn();
        } else {
            emailAddress = HeaderUtil.getEmail(httpServletRequest);
            if (StringUtils.isEmpty(emailAddress)) {
                return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
            }
            user = userService.findByEmail(emailAddress);
            Result result = checkTokenAndEmail(httpServletRequest, emailAddress);
            if (Objects.nonNull(result)) {
                return result;
            }
        }
        if (Objects.isNull(type)) {
            type = 2;
        }
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(activityId, getZoneId());
        if (Objects.isNull(activityNew)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.validate.notExist"));
        }
        if (!MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType())) {
            ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, user.getLanguageCode());
            if (Objects.nonNull(disseminate)) {
                activityNew.setActivityTitle(disseminate.getTitle());
            }
            SubActivity subActivity = subActivityService.getSingleActByMain(activityId);
            activityNew.setSubActivity(subActivity);
        }
        Map<String, Object> map = runActivityBizService.getRankingResults(activityNew, type, user);

        return CommonResult.success(map);
    }

    @Deprecated
    /**
     * 获取官方活动列表
     *
     * @param request
     * @return http://localhost:7770/app/runActivity/getOfficialActivity
     */
    @PostMapping("/getOfficialActivity")
    public Result getOfficialActivity(@RequestBody GetOfficialActivityRequest request) {
        Integer activityType = request.getActivityType();
        Integer classifyType = request.getClassifyType();
        Map<String, Object> data = new HashMap<>();
        List<OfficialActivityListVO> homePageOfficialActivity = appActivityPageManager.getHomePageOfficialActivity(getLoginUser(), 2, classifyType, activityType);
        if (!CollectionUtils.isEmpty(homePageOfficialActivity)) {
            Map<Integer, List<OfficialActivityListVO>> collect = homePageOfficialActivity.stream().collect(Collectors.groupingBy(OfficialActivityListVO::getClassifyType));
            List<Map<String, Object>> records = collect.entrySet().stream().map(e -> {
                Map<String, Object> map = new HashMap<>();
                map.put("classifyType", e.getKey());
                map.put("classifyName", ActivityClassifyTypeEnum.getByType(e.getKey()).getEnName());
                List<OfficialActivityListVO> value = e.getValue();
                value.stream().sorted(Comparator.comparing(OfficialActivityListVO::getActivityStartTime));
                map.put("list", value);
                return map;
            }).sorted((x, y) -> {
                Integer classifyType1 = MapUtils.getInteger(x, "classifyType");
                Integer classifyType2 = MapUtils.getInteger(y, "classifyType");
                return classifyType1.compareTo(classifyType2);
            }).collect(Collectors.toList());
            data.put("records", records);
        }
        return CommonResult.success(data);
    }

    /**
     * 获取个人赛事列表
     *
     * @param po
     * @return
     */
    @PostMapping("/getIndividualTeamRunActivity")
    public Result getIndividualTeamRunActivity(@RequestBody PageQuery po) {
        return CommonResult.success(appActivityPageManager.getIndividualTeamRunActivity(getLoginUser(), po.getPageNum(), po.getPageSize()));
    }

    /**
     * 获取比赛结果
     * iewidsds
     *
     * @param request
     * @return
     */
    @PostMapping("/getMatchResults")
    public Result getMatchResults(@RequestBody ActivityIdRequest request) throws Exception {
        ZnsUserEntity user = getLoginUser();
        Long activityId = request.getActivityId();
        Long userId = user.getId();
        //查询活动是否结束
        ZnsRunActivityEntity activityEntity = runActivityService.selectActivityById(activityId);
        for (int i = 0; i < 10; i++) {
            if (Objects.isNull(activityEntity) || activityEntity.getActivityState() != 2) {
                Thread.sleep(1000);
                log.info("第几次查询活动 " + i + "，activityId = " + activityEntity.getId() + ", activityState = " + activityEntity.getActivityState());
                activityEntity = runActivityService.selectActivityById(activityId);
            } else {
                break;
            }
        }
        if (Objects.isNull(activityEntity) || activityEntity.getActivityState() != 2) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.viewUnfinished"));
        }
        ZnsRunActivityUserEntity userEntity = znsRunActivityUserService.selectByActivityIdUserId(activityId, userId);
        for (int i = 0; i < 10; i++) {
            if (Objects.isNull(userEntity) || userEntity.getUserState() != 4) {
                Thread.sleep(1000);
                log.info("第几次查询 " + i + ",activityId=" + activityId + ",userId=" + userId + ", userState=" + userEntity.getUserState());
                userEntity = znsRunActivityUserService.selectByActivityIdUserId(activityId, userId);
            } else {
                break;
            }
        }

        if (Objects.isNull(userEntity) || userEntity.getUserState() != 4) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.viewUnfinished"));
        }
        return CommonResult.success(userEntity.getRank());
    }

    /**
     * 获取累计跑列表页
     *
     * @return
     * @tag 2.4
     */
    @PostMapping("/getOfficialCumulativeActivity")
    public Result<MapListVo<OfficialCumulativeActivityListVO>> getOfficialCumulativeActivity() {
        ZnsUserEntity user = getLoginUser();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        List<? extends SimpleRunActivityVO> list = activityStrategyContext.getActivityList(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType(), user, checkVersion, null, null, null, null);
        MapListVo map = new MapListVo();
        map.setList(list);
        return CommonResult.success(map);
    }

    /**
     * 获取官方多人同跑列表页
     *
     * @return
     * @tag 2.4
     */
    @PostMapping("/getOfficialTeamActivity")
    public Result<OfficialTeamActivityPageVo> getOfficialTeamActivity(@RequestBody TaskRequest request) {
        Integer source = request.getSource();
        ZnsUserEntity user = getLoginUser();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        List<OfficialTeamActivityListVO> list = (List<OfficialTeamActivityListVO>) activityStrategyContext.getActivityList(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType(), user, checkVersion, source, request.getCompleteRuleType(), request.getRunWalkStatus(), request.getRateLimitType());
        OfficialTeamActivityPageVo officialTeamActivityPageVo = getHomePageTeamActivityMapNew(list, user);
        return CommonResult.success(officialTeamActivityPageVo);
    }

    /**
     * 获取排行赛列表页（原首页列表数据）
     *
     * @return
     * @tag 2.4.1
     */
    @PostMapping("/getOfficialRankActivity")
    public Result<Page<ZnsRunActivityEntity>> getOfficialRankActivity(@RequestBody PageQuery request) {
        ZnsUserEntity user = getLoginUser();
        boolean testUser = sysConfigService.isTestUser(user.getEmailAddressEn());
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        Page<ZnsRunActivityEntity> page = runActivityService.getActivityByTypeAndStatePage(new Page<>(request.getPageNum(), request.getPageSize()), RunActivityTypeEnum.OFFICIAL_ENENT.getType(), testUser, checkVersion, false, user, false, null, null);
        List list = activityStrategyContext.homePageActivityList(page.getRecords(), RunActivityTypeEnum.OFFICIAL_ENENT.getType(), user.getId());
        page.setRecords(list);
        return CommonResult.success(page);
    }


    /**
     * 获取pk赛列表页
     *
     * @return
     * @tag 2.4.1
     */
    @PostMapping("/getPkActivity")
    public Result<Page<RunActivityTeamVO>> getPkActivity(@RequestBody PageQuery request) {
        ZnsUserEntity user = getLoginUser();
        boolean testUser = sysConfigService.isTestUser(user.getEmailAddressEn());
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        Page<RunActivityTeamVO> page = appActivityPageManager.getPkActivity(new Page<>(request.getPageNum(), request.getPageSize()), user, testUser, checkVersion);
        return CommonResult.success(page);
    }

    private OfficialTeamActivityPageVo getHomePageTeamActivityMapNew(List<OfficialTeamActivityListVO> list, ZnsUserEntity user) {
        OfficialTeamActivityPageVo officialTeamActivityPageVo = new OfficialTeamActivityPageVo();
        List<OfficialTeamActivityListVO> todayList = new ArrayList();
        List<OfficialTeamActivityListVO> moreList = new ArrayList();

        if (CollectionUtils.isEmpty(list)) {
            officialTeamActivityPageVo.setTodayList(todayList);
            officialTeamActivityPageVo.setMoreList(moreList);
            return officialTeamActivityPageVo;
        }
        //今日数据获取
        String zoneId = getZoneId();
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId));
        ZonedDateTime dayStartTime = DateUtil.startOfDate(now);
        ZonedDateTime dayEndTime = DateUtil.endOfDate(now);

        for (OfficialTeamActivityListVO simpleRunActivityVO : list) {
            if (UserConstant.notContainsCountry(simpleRunActivityVO.getCountry(), user.getCountry())) {
                //用户国家跟活动国家不一致
                continue;
            }
            Integer integer = userCouponService.countUserCouponByCondition(user.getId(), simpleRunActivityVO.getActivityId(), simpleRunActivityVO.getActivityType(), 0L, "");
            simpleRunActivityVO.setCanUserCoupon(integer > 0 ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            ZonedDateTime activityStartTime = DateUtil.getDate2ByTimeZone(simpleRunActivityVO.getActivityStartTime(), TimeZone.getTimeZone(zoneId));
            ZonedDateTime activityEndTime = DateUtil.getDate2ByTimeZone(simpleRunActivityVO.getActivityEndTime(), TimeZone.getTimeZone(zoneId));
            if ((Objects.nonNull(activityStartTime) && DateUtil.isBetweenE(activityStartTime, dayStartTime, dayEndTime))
                    || (Objects.nonNull(activityEndTime) && DateUtil.isBetweenE(activityEndTime, dayStartTime, dayEndTime))) {
                todayList.add(simpleRunActivityVO);
            } else {
                moreList.add(simpleRunActivityVO);
            }
        }
        officialTeamActivityPageVo.setTodayList(todayList);
        officialTeamActivityPageVo.setMoreList(moreList);
        return officialTeamActivityPageVo;
    }

    private Map<String, Object> getHomePageTeamActivityMap(List<OfficialTeamActivityListVO> list) {
        Map<String, Object> map = new HashMap<>();
        List<OfficialTeamActivityListVO> todayList = new ArrayList();
        List<OfficialTeamActivityListVO> moreList = new ArrayList();

        if (CollectionUtils.isEmpty(list)) {
            map.put("todayList", todayList);
            map.put("moreList", moreList);
            return map;
        }
        //今日数据获取
        String zoneId = getZoneId();
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId));
        ZonedDateTime dayStartTime = DateUtil.startOfDate(now);
        ZonedDateTime dayEndTime = DateUtil.endOfDate(now);

        for (OfficialTeamActivityListVO simpleRunActivityVO : list) {
            ZonedDateTime activityStartTime = DateUtil.getDate2ByTimeZone(simpleRunActivityVO.getActivityStartTime(), TimeZone.getTimeZone(zoneId));
            ZonedDateTime activityEndTime = DateUtil.getDate2ByTimeZone(simpleRunActivityVO.getActivityEndTime(), TimeZone.getTimeZone(zoneId));
            if ((Objects.nonNull(activityStartTime) && DateUtil.isBetweenE(activityStartTime, dayStartTime, dayEndTime))
                    || (Objects.nonNull(activityEndTime) && DateUtil.isBetweenE(activityEndTime, dayStartTime, dayEndTime))) {
                todayList.add(simpleRunActivityVO);
            } else {
                moreList.add(simpleRunActivityVO);
            }
        }
        map.put("todayList", todayList);
        map.put("moreList", moreList);
        return map;
    }

    /**
     * 获取非官方多人同跑列表页
     *
     * @return
     */
    @PostMapping("/getTeamActivity")
    public Result getTeamActivity() {
        ZnsUserEntity user = getLoginUser();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        List<OfficialTeamActivityListVO> list = (List<OfficialTeamActivityListVO>) activityStrategyContext.getActivityList(RunActivityTypeEnum.TEAM_RUN.getType(), user, checkVersion, null, null, null, null);
        ZnsUserAccountEntity userAccount = znsUserAccountService.getByUserId(user.getId());
        if (Objects.isNull(userAccount)) {
            return CommonResult.fail(ActivityError.RUN_DATA_NO_END.getCode(), ActivityError.RUN_DATA_NO_END.getMsg());
        }
        Map<String, Object> map = getHomePageTeamActivityMap(list);
        return CommonResult.success(map);
    }

    /**
     * 获取我的参赛-未进行或进行中的
     *
     * @return
     */
    @PostMapping("/getMyActivity")
    public Result<MyActivityVo> getMyActivity(@RequestBody ActivityIdRequest request) {
        ZnsUserEntity user = getLoginUser();
        Integer appVersion = getAppVersion();
        Integer appType = getAppType();
        boolean checkVersion = appUpgradeService.isCheckVersion(appType, appVersion);
        String languageCode = user.getLanguageCode(); // 本来应该传系统语言，但是由于H5只传了固定语言，所以这里只能用用户语言了
        MyActivityVo map = appActivityPageManager.getMyActivity(user, getZoneId(), request, checkVersion, appVersion, appType, languageCode);
        return CommonResult.success(map);
    }

    /**
     * 校验是否可以参加新人PK
     *
     * @return
     */
    @PostMapping("/checkNewerPk")
    public Result<Boolean> checkNewerPk() {
        ZnsUserEntity user = getLoginUser();
        NewerPKVo newerPKVo = appActivityPageManager.getNewerPKVo(user, null);
        if (!newerPKVo.getIsShowNewerPK()) {
            //不满足新人pk条件
            throw new BaseException(I18nMsgUtils.getMessage("newer.pk.activity.msg"));
        }
        return CommonResult.success(Boolean.TRUE);
    }


    /**
     * 获取我的赛事（悬浮窗）
     *
     * @return
     */
    @PostMapping("/raceTab")
    public Result<RaceTabVo> raceTab() {
        ZnsUserEntity loginUser = getLoginUser();
        RaceTabVo raceTabVo = null;
        if (Objects.nonNull(loginUser)) {
            raceTabVo = appActivityPageManager.getRaceTab(loginUser.getId(), getZoneId());
        }
        return CommonResult.success(raceTabVo);
    }


    /**
     * 活动钱包
     *
     * @param request
     * @return
     */
    @PostMapping("/activityWallet")
    public Result activityWallet(@RequestBody ActivityIdRequest request) {
        //查询奖励金额
        ZnsUserEntity user = getLoginUser();

        Map<String, Object> data = new HashMap<>();
        ZnsRunActivityEntity activity = runActivityService.findById(request.getActivityId());
        Map<String, Object> jsonObject = JsonUtil.readValue(activity.getActivityConfig());
        data.put("amount", MapUtil.getBigDecimal(jsonObject.get("award")));
        data.put("canCash", 0);

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(request.getActivityId(), user.getId());
        if (Objects.isNull(activityUser)) {
            data.put("desc", I18nMsgUtils.getMessage("activity.wallet.fragment", 0)); //"收集七个幸运魔法碎片以完成藏宝图拼图。 0/7"
            return CommonResult.success(data);
        }

        List<RunActivityUserTask> runActivityUserTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityId(request.getActivityId(), user.getId(), null);
        if (CollectionUtils.isEmpty(runActivityUserTasks)) {
            data.put("desc", I18nMsgUtils.getMessage("activity.wallet.fragment", 0));
            return CommonResult.success(data);
        }

        long count = runActivityUserTasks.stream().filter(t -> t.getStatus() == 1).count();
        data.put("desc", I18nMsgUtils.getMessage("activity.wallet.fragment", count)); //"收集七个幸运魔法碎片以完成藏宝图拼图。 "+count+"/7"
        if (count >= runActivityUserTasks.size()) {
            data.put("desc", I18nMsgUtils.getMessage("activity.wallet.fragment.finished"));
            data.put("canCash", 1);
        }

        return CommonResult.success(data);
    }

    /**
     * 查询一个活动的所有歌单歌曲
     *
     * @param request
     * @return
     */
    @PostMapping("/playlistAndMusic")
    public Result<ActivityPlaylistVo> listPlaylistAndMusic(@RequestBody ActivityPlaylistRequest request) {
        Assert.notNull(request.getActivityId(), CommonError.PARAM_LACK.getMsg());
        return CommonResult.success(runPlaylistManager.listPlaylistAndMusic(request.getActivityId()));
    }


    /**
     * 是否可挑战该用户
     *
     * @param request
     * @return
     */
    @PostMapping("/canChallenge")
    public Result<CanChallengeResp> canChallenge(@RequestBody CanChallengeRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        return runActivityUserManager.canChallenge(request, loginUser);

    }

    /**
     * 挑战该用户扣除积分
     *
     * @param request
     * @return
     */
    @PostMapping("/challengeAndDecreaseScore")
    public Result challengeAndDecreaseScore(@RequestBody CanChallengeRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        return runActivityUserManager.challengeAndDecreaseScore(request, loginUser);

    }

    @Value("${spring.profiles.active}")
    private String profile;

    /**
     * 获取指定活动限速数据
     *
     * @param request
     * @return
     */
    @PostMapping("/rateLimit")
    public Result<List<GroupRunRateLimitResp>> getActivityRateLimit(@RequestBody @Validated AppActivityRateLimitDto request) {
        Long activityId = request.getActivityId();
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(activityId, getZoneId());

        SubActivity subActivity = subActivityService.getSingleActByMain(activityId);
        activityNew.setSubActivity(subActivity);

        return CommonResult.success(rateLimitService.getRateLimit(activityNew));
    }

    /**
     * 校验指定活动观赛人数
     *
     * @param req
     * @return
     */
    @PostMapping("/checkWatchUserNum")
    public Result<ActivityWatchUserNumRespDto> checkWatchUserNum(@RequestBody @Validated AppActivityRateLimitDto req) {
        //校验指定活动观赛人数
        return CommonResult.success(newActivityManager.checkWatchUserNum(req));
    }

    /**
     * room获得比赛结果/结算页
     *
     * @param request
     * @return
     */
    @PostMapping("/getActivityResult")
    public Result<ActivityResultResponseDto> getActivityResult(@RequestBody @Validated ActivityIdRequestDto request) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(activityManager.getActivityResult(request.getActivityId(), loginUser.getId(), request.getIsFriendPopNeed()));
    }

    /**
     * 获得房间比赛记录
     *
     * @param request
     * @return
     */
    @PostMapping("/getRoomActivityList")
    public Result<List<RoomActivityListRequestDto>> getRoomActivityList(@RequestBody @Validated RoomQueryDto request) {
        return CommonResult.success(activityManager.getRoomActivityList(request.getId()));
    }


    /**
     * 用户赛道具模式地图配置获取
     *
     * @tag 4.4.3
     */
    @PostMapping("/room/prop/route/config")
    public Result<RoomPropRouteConfigDto> roomPropRouteConfig() {
        RoomPropRouteConfigDto roomPropRouteConfigDto = roomManager.getRoomPropRouteConfig();
        return CommonResult.success(roomPropRouteConfigDto);
    }
}
