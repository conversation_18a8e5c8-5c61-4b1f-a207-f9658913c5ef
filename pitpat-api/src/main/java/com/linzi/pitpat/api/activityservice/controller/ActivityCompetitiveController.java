package com.linzi.pitpat.api.activityservice.controller;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.converter.ActivityCompetitiveConvert;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityCompetitiveListScoreRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityDeviceMainTypeRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityEventQueryRequest;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityCompetitiveListPageResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityCompetitiveListResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityCompetitiveModuleResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityCompetitiveUserRankListResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityCompetitiveUserRankResponseDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonConfigBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.CompetitiveTabType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonSeasonSubType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.CompetitiveDisseminateJumpType;
import com.linzi.pitpat.data.activityservice.dto.api.request.CompetitiveSeasonActivityListReq;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SingleActivityIdRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.CompetitiveSeasonNameResp;
import com.linzi.pitpat.data.activityservice.manager.api.ApiCompetitiveActivityManager;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveListDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveUserRankListDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveBonusPoolDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveDisseminateDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveSeasonConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCompetitiveUserRankListQuery;
import com.linzi.pitpat.data.activityservice.model.request.CompetitiveSeasonSystemDefaultConfig;
import com.linzi.pitpat.data.activityservice.service.ActivityEventService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonConfigService;
import com.linzi.pitpat.data.activityservice.service.UserActivityClickRecordService;
import com.linzi.pitpat.data.activityservice.strategy.activity.ApiCompetitiveActivityDeviceManagerFactory;
import com.linzi.pitpat.data.activityservice.strategy.activity.ApiCompetitiveActivityDeviceManagerStrategy;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 竞技赛相关接口
 *
 * @since 4.0.0
 */
@RestController
@RequestMapping({"/app/activity/competitive", "/h5/activity/competitive"})
@RequiredArgsConstructor
@Slf4j
public class ActivityCompetitiveController extends BaseAppController {

    private final ApiCompetitiveActivityManager apiCompetitiveActivityManager;

    private final ActivityCompetitiveConvert activityCompetitiveConvert;

    private final AppUpgradeService appUpgradeService;

    private final ActivityEventService activityEventService;

    private final CompetitiveSeasonConfigBizService competitiveSeasonConfigBizService;

    private final CompetitiveSeasonConfigService competitiveSeasonConfigService;

    private final UserActivityClickRecordService userActivityClickRecordService;

    /**
     * 封面点击
     *
     * @return
     */
    @PostMapping("/coverClick")
    public Result<ActivityCompetitiveModuleResponseDto> coverClick(@RequestBody SingleActivityIdRequest request) {
        userActivityClickRecordService.click(getUserId(), request.getMainActivityId());
        return CommonResult.success();
    }

    /**
     * 月度竞技赛
     *
     * @return
     */
    @PostMapping("/monthly")
    public Result<ActivityCompetitiveModuleResponseDto> queryMonthlyCompetitiveActivity(@RequestBody(required = false) ActivityDeviceMainTypeRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        String languageCode = loginUser.getLanguageCode();
        ApiCompetitiveActivityDeviceManagerStrategy strategy = ApiCompetitiveActivityDeviceManagerFactory.getStrategyByType(requestDto == null ? DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType() : requestDto.getEquipmentMainType());
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        return CommonResult.success(ActivityCompetitiveModuleResponseDto.of(
                activityCompetitiveConvert.toResponseDtoList(
                        strategy.queryMonthlyCompetitiveActivity(loginUser, zoneId, languageCode, checkVersion, getAppVersion()))));
    }

    /**
     * 季度竞技赛
     *
     * @return
     */
    @PostMapping("/seasonal")
    public Result<ActivityCompetitiveModuleResponseDto> querySeasonalCompetitiveActivity(@RequestBody(required = false) ActivityDeviceMainTypeRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        ApiCompetitiveActivityDeviceManagerStrategy strategy = ApiCompetitiveActivityDeviceManagerFactory.getStrategyByType(requestDto == null ? DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType() : requestDto.getEquipmentMainType());
        Integer appVersion = getAppVersion();

        //兼容逻辑4043 默许系统配置的默认赛季的第一个开始的活动。 4043以后。获取的是所有季赛中配置了倒计时的第一个活动
        CompetitiveSeasonActivityListReq req = new CompetitiveSeasonActivityListReq();
        req.setSeasonType(ActivityCompetitiveSeasonType.SEASONAL);
        if (appVersion < 4043) {
            CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
            req.setSeasonId(competitiveSeasonSystemDefaultConfig.getSeasonActivitySeasonRankId());
        }
        List<ActivityCompetitiveListDto> seasonActivities = strategy.querySeasonalCompetitiveActivityList(loginUser, zoneId, getLanguageCode(), req);

        ActivityCompetitiveModuleResponseDto data = new ActivityCompetitiveModuleResponseDto();
        if (!CollectionUtils.isEmpty(seasonActivities)) {
            data.setHeroActivity(activityCompetitiveConvert.toResponseDto(seasonActivities.get(0)));
        }
        CompetitiveDisseminateDto competitiveDisseminateDto = strategy.queryCompetitiveDisseminate(loginUser, getLanguageCode(), ActivityCompetitiveSeasonType.SEASONAL);

        data.setCompetitiveDisseminateDto(competitiveDisseminateDto);
        //兼容逻辑4043 默认获取第一个活动，并删除宣传
        if (data.getHeroActivity() != null && appVersion < 4043) {
            //有活动不要宣传图
            data.setCompetitiveDisseminateDto(null);
            ActivityCompetitiveSeasonSeasonSubType subType = ActivityCompetitiveSeasonSeasonSubType.of(data.getHeroActivity().getCompetitiveSeasonSubType());
            if (subType != null) {
                data.setModuleName(I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(getLanguageCode()) ?
                        subType.getFrTitle().toUpperCase() :
                        subType.getEnTitle().toUpperCase());
            }
            String moduleTitle = I18nMsgUtils.getLangMessage(getLanguageCode(), "activity.competitive.hero.seasonal.module_name");
            data.setModuleName(moduleTitle);
        }
        //如果小于4043,有配置赛事宣传，有是跳转到赛事竞技。将url删除。防止跳转
        if (appVersion < 4043 && Objects.nonNull(data.getCompetitiveDisseminateDto())
                && CompetitiveDisseminateJumpType.EVENT_LIST.equals(data.getCompetitiveDisseminateDto().getEventJumpTarget())) {
            data.getCompetitiveDisseminateDto().setJumpUrl(null);
        }
        //填充isNew
        String languageCode = getLanguageCode();
        CompetitiveSeasonActivityListReq listReq = new CompetitiveSeasonActivityListReq();
        listReq.setSeasonType(ActivityCompetitiveSeasonType.SEASONAL);
        listReq.setEquipmentMainType(0);
        if (requestDto != null) {
            listReq.setEquipmentMainType(requestDto.getEquipmentMainType());

        }
        List<ActivityCompetitiveListDto> allActivities = strategy.querySeasonalCompetitiveActivityList(loginUser, zoneId, languageCode, listReq);
        if (allActivities.stream().anyMatch(k -> k.getIsNew() == 1)) {
            data.setIsNew(1);
        }
        //重新填充倒计时 source：4044
        if (appVersion >= 4043 && requestDto.getEquipmentMainType() == 0) {
            reFillCompetitiveCountDown(data, seasonActivities);
        }
        return CommonResult.success(data);
    }

    /**
     * 年度竞技赛
     *
     * @return
     */
    @PostMapping("/annual")
    public Result<ActivityCompetitiveModuleResponseDto> queryAnnualCompetitiveActivity(@RequestBody(required = false) ActivityDeviceMainTypeRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        ApiCompetitiveActivityDeviceManagerStrategy strategy = ApiCompetitiveActivityDeviceManagerFactory.getStrategyByType(requestDto == null ? DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType() : requestDto.getEquipmentMainType());
        Integer appVersion = getAppVersion();
        CompetitiveSeasonActivityListReq req = new CompetitiveSeasonActivityListReq();
        req.setSeasonType(ActivityCompetitiveSeasonType.ANNUAL);
        if (appVersion < 4043) {
            CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
            req.setSeasonId(competitiveSeasonSystemDefaultConfig.getAnnualActivitySeasonRankId());
        }

        List<ActivityCompetitiveListDto> seasonActivities = strategy.querySeasonalCompetitiveActivityList(loginUser, zoneId, getLanguageCode(), req);

        ActivityCompetitiveModuleResponseDto data = new ActivityCompetitiveModuleResponseDto();
        //兼容逻辑4043 默认获取第一个活动，并删除宣传
        if (!CollectionUtils.isEmpty(seasonActivities)) {
            data.setHeroActivity(activityCompetitiveConvert.toResponseDto(seasonActivities.get(0)));
        }
        CompetitiveDisseminateDto competitiveDisseminateDto = strategy.queryCompetitiveDisseminate(loginUser, getLanguageCode(), ActivityCompetitiveSeasonType.ANNUAL);
        data.setCompetitiveDisseminateDto(competitiveDisseminateDto);
        //兼容逻辑4043 默认获取第一个活动，并删除宣传
        if (data.getHeroActivity() != null && getAppVersion() < 4043) {
            String moduleTitle = I18nMsgUtils.getLangMessage(getLanguageCode(), "activity.competitive.hero.annual.module_name");
            data.setModuleName(moduleTitle);
            //有活动不要宣传图
            data.setCompetitiveDisseminateDto(null);
        }
        //如果小于4043,有配置赛事宣传，有是跳转到赛事竞技。将url删除。防止跳转
        if (appVersion < 4043 && Objects.nonNull(data.getCompetitiveDisseminateDto())
                && CompetitiveDisseminateJumpType.EVENT_LIST.equals(data.getCompetitiveDisseminateDto().getEventJumpTarget())) {
            data.getCompetitiveDisseminateDto().setJumpUrl(null);
        }
        //填充isNew
        String languageCode = getLanguageCode();
        CompetitiveSeasonActivityListReq listReq = new CompetitiveSeasonActivityListReq();
        listReq.setSeasonType(ActivityCompetitiveSeasonType.ANNUAL);
        listReq.setEquipmentMainType(0);
        if (requestDto != null) {
            listReq.setEquipmentMainType(requestDto.getEquipmentMainType());
        }
        List<ActivityCompetitiveListDto> allActivities = strategy.querySeasonalCompetitiveActivityList(loginUser, zoneId, languageCode, listReq);
        if (loginUser != null) {
            if (allActivities.stream().anyMatch(k -> k.getIsNew() == 1)) {
                data.setIsNew(1);
            }
        }
        //重新填充倒计时 source：4044
        if (appVersion >= 4043 && requestDto.getEquipmentMainType() == 0) {
            reFillCompetitiveCountDown(data, seasonActivities);
        }
        return CommonResult.success(data);
    }

    private void reFillCompetitiveCountDown(ActivityCompetitiveModuleResponseDto data, List<ActivityCompetitiveListDto> seasonActivities) {
        //主活动已开始，重新设置主活动倒计时
        seasonActivities.stream().filter(k -> k.getActivityStartTime()
                        .compareTo(ZonedDateTime.now()) > 0 && k.getCompetitiveCountDownDto() != null
                        && Integer.valueOf("1").equals(k.getCompetitiveCountDownDto().getCountDownSwitch()))
                .min(Comparator.comparing(ActivityCompetitiveListDto::getActivityStartTime).thenComparing(ActivityCompetitiveListDto::getActivityId))
                .ifPresent(dto -> data.setHeroActivity(activityCompetitiveConvert.toResponseDto(dto)));
    }

    /**
     * 竞技赛列表
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/seasonActivityList")
    public Result<ActivityCompetitiveListPageResponseDto> seasonActivityList(@RequestBody CompetitiveSeasonActivityListReq requestDto) {
        ActivityCompetitiveListPageResponseDto resp = new ActivityCompetitiveListPageResponseDto();
        if (Objects.isNull(requestDto)) {
            return CommonResult.success(resp);
        }
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        ApiCompetitiveActivityDeviceManagerStrategy strategy = ApiCompetitiveActivityDeviceManagerFactory.getStrategyByType(requestDto.getEquipmentMainType());
        if (Objects.isNull(requestDto.getSeasonId())) {
//            List<CompetitiveSeasonListResp> competitiveSeasonListResps = competitiveSeasonConfigBizService.queryCompetitiveSeasonList(getLanguageCode(), requestDto.getSeasonType());
//            if (!CollectionUtils.isEmpty(competitiveSeasonListResps)) {
//                requestDto.setSeasonId(competitiveSeasonListResps.get(0).getCompetitiveSeasonId());
//            }else{
//                return CommonResult.success(resp);
//            }
            CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
            if (ActivityCompetitiveSeasonType.ANNUAL.equals(requestDto.getSeasonType())) {
                requestDto.setSeasonId(competitiveSeasonSystemDefaultConfig.getAnnualActivitySeasonRankId());
            } else {
                requestDto.setSeasonId(competitiveSeasonSystemDefaultConfig.getSeasonActivitySeasonRankId());
            }
        }
        String languageCode = getLanguageCode();
        List<ActivityCompetitiveListDto> allActivities = strategy.querySeasonalCompetitiveActivityList(loginUser, zoneId, languageCode, requestDto);
        List<ActivityCompetitiveListResponseDto> responseDtoList = activityCompetitiveConvert.toResponseDtoList(allActivities);
        resp.fillAllTagActivities(responseDtoList);
        resp.resortActivities();
        competitiveSeasonConfigService.findBySeasonId(requestDto.getSeasonId()).ifPresent(item -> {
            CompetitiveSeasonConfigDto configDto = new CompetitiveSeasonConfigDto();
            configDto.setSeasonId(item.getSeasonId());
            configDto.setSeasonName(item.queryI18nSeasonName(languageCode));
            configDto.setYear(item.getYear());
            configDto.setSeasonType(item.getSeasonType());
            configDto.setSubSeasonType(item.getSubSeasonType());
            configDto.setStartTime(item.getStartTime());
            configDto.setEndTime(item.getEndTime());
            resp.setSeasonConfigDto(configDto);
        });
        return CommonResult.success(resp);
    }


    /**
     * 更多赛事
     *
     * @return
     */
    @PostMapping("/showInMore")
    public Result<ActivityCompetitiveModuleResponseDto> queryShowInMoreActivity(@RequestBody(required = false) ActivityDeviceMainTypeRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        if (Objects.isNull(requestDto)) {
            requestDto = new ActivityDeviceMainTypeRequestDto();
            requestDto.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType());
        }
        ActivityCompetitiveModuleResponseDto data = ActivityCompetitiveModuleResponseDto.of(
                activityCompetitiveConvert.toResponseDtoList(
                        apiCompetitiveActivityManager.queryShowInMoreActivity(loginUser, zoneId, loginUser.getLanguageCode(), checkVersion, requestDto.getEquipmentMainType(), getAppVersion())));
        data.setBatchSignUpButton(apiCompetitiveActivityManager.canBatchSignUp(loginUser, zoneId, checkVersion, requestDto.getEquipmentMainType(), null));
        //跑步模块下，如果全部已经结束，返回空列表
        if (Objects.equals(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType(), requestDto.getEquipmentMainType()) &&
                data.getActivityList().stream().allMatch(k -> k.getActivityState() == 2)) {
            data.setActivityList(new ArrayList<>());
        }
        return CommonResult.success(data);
    }

    /**
     * 用户积分
     *
     * @return
     */
    @PostMapping("/userRankScore")
    @FillerMethod
    public Result<ActivityCompetitiveUserRankResponseDto> userRankScore() {
        ZnsUserEntity loginUser = getLoginUser();
        ActivityCompetitiveUserRankResponseDto response = activityCompetitiveConvert.toResponseDtoList(
                apiCompetitiveActivityManager.userRankScore(loginUser));
        return CommonResult.success(response);
    }


    /**
     * 排名列表
     *
     * @return
     */
    @PostMapping("/userRank")
    public Result<ActivityCompetitiveUserRankListResponseDto> userRank(@RequestBody ActivityCompetitiveListScoreRequestDto req) {
        ZnsUserEntity loginUser = getLoginUser();
        ActivityCompetitiveUserRankListQuery query = new ActivityCompetitiveUserRankListQuery();
        query.setPageNum(req.getPageNum());
        query.setPageSize(req.getPageSize());
        query.setUserId(loginUser.getId());
        query.setSeasonId(req.getSeasonId());
        query.setSeasonType(ActivityCompetitiveSeasonType.of(req.getSeasonType()));
        query.setTabType(CompetitiveTabType.getByCode(req.getRankType()));
        ActivityCompetitiveUserRankListDto activityCompetitiveUserRankListDto = apiCompetitiveActivityManager.userRank(query);
        return CommonResult.success(activityCompetitiveConvert.toResponseDto(activityCompetitiveUserRankListDto));
    }

    /**
     * 轮询查询事件结果
     *
     * @return
     */
    @PostMapping("/activityEvent")
    public Result<Map<String, Object>> activityEvent(@RequestBody ActivityEventQueryRequest request) {
        List<String> eventKey = request.getEventKey();
        Map<String, Object> eventResult = new HashMap<>();
        for (String s : eventKey) {
            Object data = activityEventService.getData(s);
            eventResult.put(s, data);
        }
        return CommonResult.success(eventResult);
    }

    @PostMapping("/bonusPoolAmount")
    public Result<CompetitiveBonusPoolDto> queryBonusPoolAmount() {
        return CommonResult.success(apiCompetitiveActivityManager.getBonusPoolAmount());
    }

    /**
     * 竞技赛季列表
     *
     * @return
     */
    @PostMapping("/competitiveSeasonList")
    public Result<CompetitiveSeasonNameResp> queryCompetitiveSeasonList() {
        String languageCode = getLanguageCode();
        CompetitiveSeasonNameResp data = competitiveSeasonConfigBizService.queryCompetitiveSeasonList(languageCode);
        CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
        data.setDefaultTag(competitiveSeasonSystemDefaultConfig.getAnnualRankId(), competitiveSeasonSystemDefaultConfig.getSeasonRankId());
        return CommonResult.success(data);
    }

}
