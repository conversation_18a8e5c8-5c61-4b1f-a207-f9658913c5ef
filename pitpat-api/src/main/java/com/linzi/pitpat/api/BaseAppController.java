/*

 * @description:

 *

 * projectName: pitpat-server

 * fileName: BaseController.java

 * date 2021-10-07

 * copyright(c) 2018-2020 杭州霖扬网络科技有限公司版权所有

 */

package com.linzi.pitpat.api;


import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.ApiSource;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.TimeZoneVo;
import com.linzi.pitpat.core.util.IpUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.entity.dto.user.LoginUserDto;
import com.linzi.pitpat.data.systemservice.enums.SystemConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.vo.AppBaseInfoVo;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.util.IPhoneType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;

/**
 * description:
 *
 * <AUTHOR>
 * <p>
 * className BaseController
 * <p>
 * version V1.0
 * @date 2021-09-30
 **/
@Slf4j
public abstract class BaseAppController {

    @Resource
    private ZnsUserService znsUserService;
    @Resource
    private ZnsUserAccountService znsUserAccountService;
    @Resource
    private RedisUtil redisUtil;


    protected ZnsUserEntity getLoginUser() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        // String hasLogin = request.getHeader("hasAppLogin");
        //if (!Objects.equals(hasLogin, "OKey")) {
//            return null;
//        }
        String emailAddress = UserContextHolder.getEmail();
        log.info(" getLoginUser  email= " + emailAddress);
        if (!StringUtils.hasText(emailAddress)) {
            log.info("emailAddress " + emailAddress);
            return null;
        }
        ZnsUserEntity loginUser = znsUserService.findByEmail(emailAddress);
        if (loginUser == null) {
            return null;
        }
        String zoneId = Optional.ofNullable(getZoneId()).orElse("-8");
        Integer appVersion = Optional.ofNullable(getAppVersion()).orElse(Constants.H5_DEFAULT_APPVERSION);
        if (zoneId.equals(loginUser.getZoneId()) && appVersion.equals(loginUser.getAppVersion())) {
            //时区、app版本没有变更，则直接返回
            return loginUser;
        }
        loginUser.setZoneId(zoneId);
        loginUser.setZoneOffset(getUserTimeZone().getZoneOffset());
        loginUser.setAppVersion(appVersion);
        znsUserService.update(loginUser);
        return loginUser;
    }

    protected LoginUserDto getLoginUserDto() {
        ZnsUserEntity loginUser = getLoginUser();
        LoginUserDto dto = new LoginUserDto(loginUser, loginUser.getIsTest(), getAppType(), getAppVersion(), getZoneId(), loginUser.getIsPrivacy());

        return dto;
    }

    /**
     * 获取用户币种
     *
     * @return
     */
    protected Currency getUserCurrency() {
        ZnsUserEntity loginUser = getLoginUser();
        ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(loginUser.getId());
        if (accountEntity == null) {
            //默认使用美元
            return I18nConstant.CurrencyCodeEnum.USD.getCurrency();
        }
        return I18nConstant.buildCurrency(accountEntity.getCurrencyCode());
    }

    /**
     * 用户用户时区
     *
     * @return
     */
    protected TimeZoneVo getUserTimeZone() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String zoneId = Optional.ofNullable(request.getHeader(I18nConstant.ZONE_ID_HEAD_PARAM)).orElse("America/Los_Angeles"); //默认华盛顿州
        String zoneOffsetStr = request.getHeader(I18nConstant.ZONE_OFFSET_HEAD_PARAM);
        Integer zoneOffset = -8; //默认华盛顿 西8区
        if (StringUtils.hasText(zoneOffsetStr)) {
            try {
                //时区保留整数，有必要再优化
                zoneOffset = new BigDecimal(zoneOffsetStr).setScale(0, RoundingMode.HALF_UP).intValue();
            } catch (Exception e) {
                log.error("用户用户时区,时区：" + zoneOffsetStr + " 转换异常 ", e);
            }
        }
        return new TimeZoneVo(zoneId, zoneOffset);
    }

    protected ZnsUserEntity getLoginUserNotNull() {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            throw new RuntimeException(CommonError.NEED_LOGIN.getMsg());
        }
        return loginUser;
    }

    protected ZnsUserEntity getLoginUserOrDefaultUser() {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            //构造默认用户
            loginUser = new ZnsUserEntity();
            loginUser.setId(0L);
            loginUser.setIsTest(0);
            loginUser.setZoneId(getZoneId());
            loginUser.setAppVersion(getAppVersion());
            loginUser.setLanguageCode(getLanguageCode());
        }
        return loginUser;
    }

    /**
     * 获取设备类型，  1,"android", 2,"ios"
     *
     * @see SystemConstant.AppTypeEnum
     */
    protected Integer getAppType() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appType = request.getHeader("appType");
        return MapUtil.getInteger(appType, 0);
    }

    protected Integer getAppVersion() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appVersion = request.getHeader("appVersion");
        if (!StringUtils.hasText(appVersion)) {
            appVersion = request.getParameter("appVersion");
        }
        String uri = request.getRequestURI();
        if (!StringUtils.hasText(appVersion)) {
            Enumeration<String> headerNames = request.getHeaderNames();
            HashMap<String, String> map = new HashMap<>();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                map.put(headerName, headerValue);
            }
            log.info("BaseAppController#getAppVersion----------获取app版本，请求uri:" + uri + " appVersion为空,head参数：" + map);
            return Constants.H5_DEFAULT_APPVERSION;
        }
        try {
            return Integer.parseInt(appVersion);
        } catch (Exception e) {
            log.error("BaseAppController#getAppVersion----------获取app版本，请求uri:" + uri + ",转换appVersion：" + appVersion + "异常", e);
            return Constants.H5_DEFAULT_APPVERSION;
        }
    }

    /**
     * 获取用户语言，先从请求头获取不到查询用户表
     *
     * @return
     */
    public String getLanguageCode() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String languageCode = request.getHeader(I18nConstant.LANGUAGE_HEAD_PARAM);
        if (StringUtils.hasText(languageCode)) {
            return languageCode;
        }
        ZnsUserEntity userEntity = getLoginUser();
        if (userEntity != null) {
            //使用用户更新的语言
            return userEntity.getLanguageCode();
        }
        //使用默认语言-英语
        return I18nConstant.LanguageCodeEnum.en_US.getCode();
    }

    protected String getZoneId() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String zoneId = request.getHeader("zoneId");
        if (!StringUtils.hasText(zoneId)) {
            return "";
        }
        return zoneId;
    }

    protected String getIpAddr() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String ipAddr = IpUtil.getRemoteIp(request);
        if (!StringUtils.hasText(ipAddr)) {
            return "127.0.0.1";
        }
        return ipAddr;
    }

    protected Result checkTokenAndEmail(HttpServletRequest httpServletRequest, String emailAddress) {
        String token = httpServletRequest.getHeader("token");

        if (!StringUtils.hasText(token)) {
            return CommonResult.fail(CommonError.TOKEN_LACK.getCode(), I18nMsgUtils.getMessage("common.invalid.token"));
        }

        if (StringUtils.isEmpty(emailAddress)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }

        Object redisToken = redisUtil.get(ApiConstants.APP_LOGIN_TOKEN_KEY + emailAddress);
        if (Objects.isNull(redisToken) || !redisToken.toString().equals(token)) {
            return CommonResult.fail(CommonError.INVALID_TOKEN.getCode(), I18nMsgUtils.getMessage("common.invalid.token"));
        }
        return null;
    }


    public ZnsUserEntity getUser(Long userId, String email) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        if (znsUserEntity != null) {
            return znsUserEntity;
        }
        znsUserEntity = znsUserService.findByEmail(email);
        if (znsUserEntity != null) {
            return znsUserEntity;
        }
        return getLoginUser();
    }

    public Long getUserId() {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return null;
        }
        return loginUser.getId();
    }

    /**
     * 获取请求基础信息
     *
     * @return
     */
    protected AppBaseInfoVo getAppBaseInfo() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest httpServletRequest = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String uuid = httpServletRequest.getHeader("uuid");
        String deviceName = httpServletRequest.getHeader("deviceName");
        String systemVersion = httpServletRequest.getHeader("systemVersion");
        String appType = httpServletRequest.getHeader("appType");
        String zoneId = httpServletRequest.getHeader("zoneId");
        String ipAddr = httpServletRequest.getHeader("ipAddr");
        String appVersionStr = httpServletRequest.getHeader("appVersion");
        String channelSource = httpServletRequest.getHeader("channelSource");
        String materialToken = httpServletRequest.getHeader("materialToken");
        if ("2".equals(appType)) {
            String nameByType = IPhoneType.getNameByType(deviceName);
            if (StringUtils.hasText(nameByType)) {
                deviceName = nameByType;
            }
        }
        String zoneOffsetStr = httpServletRequest.getHeader(I18nConstant.ZONE_OFFSET_HEAD_PARAM);
        Integer zoneOffset = -8; //默认华盛顿 西8区
        if (StringUtils.hasText(zoneOffsetStr)) {
            try {
                //时区保留整数，有必要再优化
                zoneOffset = new BigDecimal(zoneOffsetStr).setScale(0, RoundingMode.HALF_UP).intValue();
            } catch (Exception e) {
                log.error("用户用户时区,时区：" + zoneOffsetStr + " 转换异常 ", e);
            }
        }
        String languageCode = Optional.ofNullable(httpServletRequest.getHeader(I18nConstant.LANGUAGE_HEAD_PARAM)).orElse(I18nConstant.LanguageCodeEnum.en_US.getCode());
        I18nConstant.LanguageCodeEnum languageCodeEnum = Optional.ofNullable(I18nConstant.LanguageCodeEnum.findByCode(languageCode)).orElse(I18nConstant.LanguageCodeEnum.en_US);
        AppBaseInfoVo baseInfoVo = new AppBaseInfoVo();
        baseInfoVo.setAppType(Integer.parseInt(appType));
        baseInfoVo.setUuid(uuid);
        baseInfoVo.setDeviceName(deviceName);
        baseInfoVo.setSystemVersion(systemVersion);
        baseInfoVo.setZoneId(zoneId);
        baseInfoVo.setZoneOffset(zoneOffset);
        baseInfoVo.setLanguageCode(languageCodeEnum.getCode());
        baseInfoVo.setLanguageName(languageCodeEnum.getName());
        baseInfoVo.setIpAddr(ipAddr);
        if (StringUtils.hasText(appVersionStr)) {
            baseInfoVo.setAppVersion(Integer.valueOf(appVersionStr));
        }
        baseInfoVo.setChannelSource(channelSource);
        baseInfoVo.setMaterialToken(materialToken);
        return baseInfoVo;
    }

    /**
     * 获取设备类型，  1,"android", 2,"ios"
     *
     * @see SystemConstant.AppTypeEnum
     */
    protected ApiSource getApiSource() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String apiSourceVal = request.getHeader("X-API-Source");
        ApiSource apiSource = ApiSource.reslove(apiSourceVal);
        return apiSource;
    }
}
