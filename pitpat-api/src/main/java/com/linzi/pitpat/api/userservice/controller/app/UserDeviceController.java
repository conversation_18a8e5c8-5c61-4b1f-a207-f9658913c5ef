package com.linzi.pitpat.api.userservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.userservice.dto.request.ExtraQualityDetailReqDto;
import com.linzi.pitpat.api.userservice.dto.request.ExtraQualityReqDto;
import com.linzi.pitpat.api.userservice.dto.request.HealthDeviceBindReqDto;
import com.linzi.pitpat.api.userservice.dto.request.UseVirtualRequestDto;
import com.linzi.pitpat.api.userservice.dto.request.UserDeviceReqDto;
import com.linzi.pitpat.api.userservice.dto.response.CheckExtraQualityRespDto;
import com.linzi.pitpat.api.userservice.dto.response.DeviceBindCheckRespDto;
import com.linzi.pitpat.api.userservice.dto.response.ExtraQualityDetailRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserDeviceListRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserDeviceNewVersionListRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserVirtualDeviceListRespDto;
import com.linzi.pitpat.api.userservice.dto.response.api.DeviceSimpleResponseDto;
import com.linzi.pitpat.api.userservice.manager.UserDeviceApiManager;
import com.linzi.pitpat.api.userservice.model.vo.HealthDeviceVo;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentQualityBiz;
import com.linzi.pitpat.data.systemservice.enums.SystemConstant;
import com.linzi.pitpat.data.userservice.dto.request.BindTreadmillRequest;
import com.linzi.pitpat.data.userservice.model.entity.UserDetailDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.query.UserEquipmentQuery;
import com.linzi.pitpat.data.userservice.service.UserDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 用户设备Controller
 */
@RestController
@RequestMapping("/app/userDevice")
@Slf4j
@RequiredArgsConstructor
public class UserDeviceController extends BaseAppController {

    private final UserDeviceApiManager userDeviceApiManager;
    private final ZnsUserEquipmentService userEquipmentService;
    private final UserDetailService userDetailService;
    private final EquipmentQualityBiz equipmentQualityBiz;


    /**
     * 用户设备列表
     *
     * @return
     */
    @PostMapping("/deviceList")
    public Result<UserDeviceListRespDto> deviceList() {
        ZnsUserEntity loginUser = getLoginUser();
        UserDeviceListRespDto respDto = userDeviceApiManager.findUserDevices(loginUser.getId(), getAppBaseInfo());
        return CommonResult.success(respDto);
    }


    /**
     * 绑定设备校验(是否第一次绑定)
     *
     * @return
     */
    @PostMapping("/checkDeviceBind")
    public Result<DeviceBindCheckRespDto> checkDeviceBind(@RequestBody HealthDeviceBindReqDto req) {
        if (Objects.isNull(req.getEquipmentType())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "equipmentType"));
        }
        //判断是否第一次
        ZnsUserEntity loginUser = getLoginUser();
        DeviceBindCheckRespDto respDto = new DeviceBindCheckRespDto();

        UserDetailDo userDetailDo = userDetailService.findByUserId(loginUser.getId());
        if (userDetailDo == null) {
            //没有用户详情
            respDto.setIsFirstBind(true);
        } else {
            String deviceGuideType = userDetailDo.getDeviceGuideType();
            if (!StringUtils.hasText(deviceGuideType)) {
                //没有引导记录
                respDto.setIsFirstBind(true);
            } else {
                //判断引导记录
                List<Integer> types = Arrays.stream(deviceGuideType.split(",")).map(Integer::valueOf).toList();
                respDto.setIsFirstBind(!types.contains(req.getEquipmentType()));
            }
        }

        //保存引导记录
        completeGuide(req.getEquipmentType());

        //返回结果
        return CommonResult.success(respDto);
    }

    /**
     * 完成健康设备引导
     *
     * @return
     */
    private void completeGuide(Integer equipmentType) {
        if (Objects.isNull(equipmentType)) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "equipmentType"));
        }
        Long userId = getLoginUser().getId();
        UserDetailDo userDetailDo = userDetailService.findByUserId(userId);
        if (userDetailDo == null) {
            //新增设备引导记录
            userDetailDo = new UserDetailDo();
            userDetailDo.setUserId(userId);
            userDetailDo.setDeviceGuideType(equipmentType + "");
            userDetailService.insert(userDetailDo);
        } else {
            //更新设备引导记录
            UserDetailDo updateDo = new UserDetailDo();
            updateDo.setId(userDetailDo.getId());
            String deviceGuideType = userDetailDo.getDeviceGuideType();
            if (StringUtils.hasText(deviceGuideType) && deviceGuideType.contains(equipmentType + "")) {
                //已有类型
                return;
            }
            if (!StringUtils.hasText(deviceGuideType)) {
                //没有额外信息
                deviceGuideType = equipmentType + "";
            } else {
                //追加内容
                deviceGuideType = deviceGuideType + "," + equipmentType;
            }
            updateDo.setDeviceGuideType(deviceGuideType);
            userDetailService.update(updateDo);
        }
    }

    /**
     * 绑定健康设备
     *
     * @return
     */
    @PostMapping("/bindHealthDevice")
    public Result<HealthDeviceVo> bindHealthDevice(@RequestBody HealthDeviceBindReqDto req) {
        Integer appType = getAppType();
        if (!StringUtils.hasText(req.getEquipmentAddress()) && SystemConstant.AppTypeEnum.ANDROID.code.equals(appType)) {
            //安卓 - 蓝牙地址不能为空
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "equipmentAddress"));
        }
        if (!StringUtils.hasText(req.getUuid()) && SystemConstant.AppTypeEnum.IOS.code.equals(appType)) {
            //ios- 蓝牙地址不能为空
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "uuid"));
        }
        ZnsUserEntity loginUser = getLoginUser();
        HealthDeviceVo vo = userDeviceApiManager.bindHealthDevice(loginUser.getId(), req, appType, getAppVersion());
        return CommonResult.success(vo);
    }

    /**
     * 用户设备删除
     *
     * @return
     */
    @PostMapping("/deleteDevice")
    public Result<Boolean> deleteDevice(@RequestBody UserDeviceReqDto req) {
        Integer appType = getAppType();
        if (!StringUtils.hasText(req.getEquipmentAddress()) && SystemConstant.AppTypeEnum.ANDROID.code.equals(appType)) {
            //安卓 - 蓝牙地址不能为空
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "equipmentAddress"));
        }
        if (!StringUtils.hasText(req.getUuid()) && SystemConstant.AppTypeEnum.IOS.code.equals(appType)) {
            //ios- 蓝牙地址不能为空
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "uuid"));
        }
        ZnsUserEntity loginUser = getLoginUser();
        UserEquipmentQuery query = UserEquipmentQuery.builder().userId(loginUser.getId()).equipmentAddress(req.getEquipmentAddress()).uuid(req.getUuid()).build();
        List<ZnsUserEquipmentEntity> list = userEquipmentService.findByList(query);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> userEquipmentService.deleteById(item.getId()));
        }
        return CommonResult.success(true);
    }

    /**
     * 设备激活 (废弃，使用绑定激活了)
     *
     * @see UserAppController#bindTreadmill(BindTreadmillRequest)
     */
    @Deprecated
    @PostMapping("/activateDevice")
    public Result<Boolean> activateDevice(@RequestBody ExtraQualityDetailReqDto req) {
        /*ZnsUserEntity loginUser = getLoginUser();
        userDeviceApiManager.activateDevice(loginUser.getId(),req);*/
        return CommonResult.success(true);
    }

    /**
     * 设备激活后-检查是否可以领取额外质保
     */
    @PostMapping("/checkReceiveExtraQuality")
    public Result<CheckExtraQualityRespDto> checkReceiveExtraQuality(@RequestBody ExtraQualityDetailReqDto req) {
        CheckExtraQualityRespDto resp = userDeviceApiManager.checkReceiveExtraQuality(req);
        return CommonResult.success(resp);
    }


    /**
     * 查询设备质保审核状态
     */
    @PostMapping("/getQualityAuditStatus")
    public Result<String> getQualityAuditStatus(@RequestBody ExtraQualityDetailReqDto req) {
        //额外质保列表状态，none: 无，toSubmit：待领取 finished：已完成，waiting：待审核，reject：审核拒绝
        String resp = userDeviceApiManager.getQualityAuditStatus(req);
        return CommonResult.success(resp);
    }

    /**
     * 新增修改额外质保
     */
    @PostMapping("/addOrUpdateExtraQuality")
    public Result<Boolean> addOrUpdateExtraQuality(@RequestBody ExtraQualityReqDto req) {
        ZnsUserEntity loginUser = getLoginUser();
        Long qualityDetailId = userDeviceApiManager.addOrUpdateExtraQuality(loginUser.getId(), req);
        //自动审核
        equipmentQualityBiz.autoAuditAsyn(List.of(qualityDetailId));
        return CommonResult.success(true);
    }

    /**
     * 获取额外质保详情
     */
    @PostMapping("/getExtraQualityDetail")
    public Result<ExtraQualityDetailRespDto> getExtraQualityDetail(@RequestBody ExtraQualityDetailReqDto req) {
        ExtraQualityDetailRespDto resp = userDeviceApiManager.getExtraQualityDetail(req);
        return CommonResult.success(resp);
    }

    /**
     * 用户设备列表(新)
     *
     * @return UserDeviceNewVersionListRespDto
     * @tag 4.4.9
     */
    @PostMapping("/deviceList/new")
    public Result<UserDeviceNewVersionListRespDto> deviceListNew() {
        ZnsUserEntity loginUser = getLoginUser();
        UserDeviceNewVersionListRespDto respDto = userDeviceApiManager.findUserSportDevices(loginUser);
        return CommonResult.success(respDto);
    }

    /**
     * 用户最新连接设备
     *
     * @return SportDeviceNewVo
     * @tag 4.7.0
     */
    @PostMapping("/device/last")
    public Result<DeviceSimpleResponseDto> deviceLast() {
        ZnsUserEntity loginUser = getLoginUser();
        DeviceSimpleResponseDto respDto = userDeviceApiManager.findDeviceLast(loginUser);
        return CommonResult.success(respDto);
    }

    /**
     * 用户虚拟设备列表
     *
     * @since 4.7.0
     */
    @PostMapping("/deviceList/virtual")
    public Result<UserVirtualDeviceListRespDto> deviceListVirtual() {
        ZnsUserEntity loginUser = getLoginUser();
        UserVirtualDeviceListRespDto respDto = userDeviceApiManager.deviceListVirtual(loginUser);
        return CommonResult.success(respDto);
    }


    /**
     * 使用虚拟设备
     *
     * @since 4.7.0
     */
    @PostMapping("/useVirtual")
    public Result<Void> useVirtual(@RequestBody @Validated UseVirtualRequestDto req) {
        userDeviceApiManager.useVirtualDevice(req.getUserEquipmentId(), getLoginUser());
        return CommonResult.success();
    }

}
