package com.linzi.pitpat.api.mallservice.mananger;

import com.linzi.pitpat.api.mallservice.dto.request.MallCategoryReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.CheckUserMallCountryRequestDto;
import com.linzi.pitpat.data.mallservice.dto.response.CheckUserMallCountryResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallCategoryRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallHomeGoodDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallHomePageRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallHomePopDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallImageDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallIndexInfoResponseDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallTextDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallVideoDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCouponConfigQuery;
import com.linzi.pitpat.data.activityservice.model.vo.EquipmentActivityVo;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.awardservice.biz.MallSkuCouponBizService;
import com.linzi.pitpat.data.awardservice.biz.MallCouponConvertComponent;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.query.CouponQuery;
import com.linzi.pitpat.data.awardservice.model.vo.SkuCouponAmountVo;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.mallservice.biz.GoodsLabelBizService;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallCategoryPageQueryDto;
import com.linzi.pitpat.data.mallservice.dto.request.MallCategoryQueryDto;
import com.linzi.pitpat.data.mallservice.dto.response.CategoryGoodsRespDto;
import com.linzi.pitpat.data.mallservice.dto.response.ContentI8nDto;
import com.linzi.pitpat.data.mallservice.dto.response.GoodsPolicyListResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallHomeJumpDto;
import com.linzi.pitpat.data.mallservice.enums.HomeModuleTypeEnum;
import com.linzi.pitpat.data.mallservice.enums.MallHomeModuleEnum;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsI18nEntity;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsPolicyI18nDo;
import com.linzi.pitpat.data.mallservice.model.entity.HomeModuleGood;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategory;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryGoods;
import com.linzi.pitpat.data.mallservice.model.entity.MallCategoryPage;
import com.linzi.pitpat.data.mallservice.model.entity.MallExchangeRateSwitchDo;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomeModule;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomeModuleI8n;
import com.linzi.pitpat.data.mallservice.model.entity.MallHomePage;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsFileEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsI18nQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsPolicyI18nQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsPolicyQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsQuery;
import com.linzi.pitpat.data.mallservice.model.query.HomeModuleGoodQuery;
import com.linzi.pitpat.data.mallservice.model.query.HomeModuleQuery;
import com.linzi.pitpat.data.mallservice.model.query.MallExchangeRateSwitchQuery;
import com.linzi.pitpat.data.mallservice.model.vo.AppGoodsLabelVo;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsAmountVo;
import com.linzi.pitpat.data.mallservice.service.GoodsI18nService;
import com.linzi.pitpat.data.mallservice.service.GoodsPolicyI18nService;
import com.linzi.pitpat.data.mallservice.service.GoodsPolicyService;
import com.linzi.pitpat.data.mallservice.service.HomeModuleGoodService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryGoodsService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryPageService;
import com.linzi.pitpat.data.mallservice.service.MallCategoryService;
import com.linzi.pitpat.data.mallservice.service.MallExchangeRateSwitchService;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleI8nService;
import com.linzi.pitpat.data.mallservice.service.MallHomeModuleService;
import com.linzi.pitpat.data.mallservice.service.MallHomePageService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsFileService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.MallJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.manager.RegionBizService;
import com.linzi.pitpat.data.systemservice.model.entity.CountryI18nEntity;
import com.linzi.pitpat.data.systemservice.model.entity.ZnsCountryEntity;
import com.linzi.pitpat.data.systemservice.model.query.CountryI18nQuery;
import com.linzi.pitpat.data.systemservice.model.query.CountryQuery;
import com.linzi.pitpat.data.systemservice.model.vo.CountryVo;
import com.linzi.pitpat.data.systemservice.model.vo.MallCountryVo;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.CountryI18nService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.model.entity.UserDetailDo;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.UserDetailService;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.time.ZoneId;


@Slf4j
@Component
@RequiredArgsConstructor
public class MallHomePageManager {

    private final MallCategoryService mallCategoryService;
    private final MallHomeModuleService mallHomeModuleService;
    private final HomeModuleGoodService homeModuleGoodService;
    private final ZnsGoodsService znsGoodsService;
    private final ZnsGoodsFileService znsGoodsFileService;
    private final GoodsI18nService goodsI18nService;
    private final MallHomeModuleI8nService mallHomeModuleI8nService;
    private final MallHomePageService mallHomePageService;
    private final MallExchangeRateSwitchService mallExchangeRateSwitchService;
    private final MallCategoryGoodsService mallCategoryGoodsService;
    private final AppRouteService appRouteService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final MallSkuCouponBizService mallCouponBizService;
    private final CouponService couponService;
    private final ActivityCouponConfigService activityCouponConfigService;
    private final GoodsPolicyService goodsPolicyService;
    private final UserBizService userBizService;
    private final GoodsPolicyI18nService goodsPolicyI18nService;
    private final GoodsLabelBizService goodsLabelBizService;
    private final MallCouponConvertComponent mallCouponConvertComponent;
    private final UserExtraService userExtraService;
    private final UserExtraBizService userExtraBizService;
    private final MainActivityBizService mainActivityBizService;
    private final ZnsUserService userService;
    private final ZnsCountryService znsCountryService;
    private final CountryI18nService countryI18nService;
    private final MallCategoryPageService mallCategoryPageService;

    private static final String GOODS_MIDDLE_URL_VALUE = "lznative://shop/productMiddlePageDetail";//商城商品中间页
    private final UserDetailService userDetailService;
    private final RegionBizService regionBizService;
    /**
     * 类目列表
     */
    public List<MallCategoryRespDto> categoryList(MallCategoryReqDto mallCategoryReq, ZnsUserEntity user, String uuid) {
        String categoryCode = mallCategoryReq.getCategoryCode();
        List<MallCategoryRespDto> result = new ArrayList<>();
        List<MallCategoryPage> categoryPageList = mallCategoryPageService.findListByQuery(new MallCategoryPageQueryDto().setCategoryPageCode(categoryCode));
        if (CollectionUtils.isEmpty(categoryPageList)) {
            return result;
        }
        Integer appVersion = user.getAppVersion();
        String mallCountryCode = null;
        if (appVersion >= VersionConstant.V4_7_5) {
            UserExtraDo extraDo = userExtraService.findByUserId(user.getId());
            if (Objects.nonNull(extraDo) && StringUtils.hasText(extraDo.getMallCountryCode())) {
                mallCountryCode = extraDo.getMallCountryCode();
            }
            List<String> countryCodes = JsonUtil.readList(categoryPageList.get(0).getCountryCode(), String.class);
            if (!CollectionUtils.isEmpty(countryCodes)) {
                if (!StringUtils.hasText(mallCountryCode)) {
                    mallCountryCode = countryCodes.get(0);
                } else if (!countryCodes.contains(mallCountryCode)) {
                    return result;
                }
            }
        }
        MallCategoryQueryDto mallCategoryQueryDto = new MallCategoryQueryDto();
        mallCategoryQueryDto.setCategoryCode(categoryCode);
        // mallCategoryQueryDto.setId(mallCategoryReq.getSubCategoryId());
        List<MallCategory> categoryList = mallCategoryService.findListByQuery(mallCategoryQueryDto);
        if (CollectionUtils.isEmpty(categoryList)) {
            return result;
        }
        //获取中间页ab分组结果，true：测试组，false：正常组
        boolean isTestGroup = false;
        if (user.getAppVersion() >= Constant.appVersion_40410 && !Objects.equals(mallCategoryReq.getQuerySource(), 2)) {
            //老版本不做测试组校验 && 设备型号跳转不做测试分组
            isTestGroup = userBizService.isTestGroupByPage(user, uuid, UserConstant.PageCodeEnum.GOODS_MIDDLE_PAGE);
        }
        if (!StringUtils.hasText(mallCountryCode)) {
            mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(user, null);
        }
        for (MallCategory mallCategory : categoryList) {

            MallCategoryRespDto mallCategoryRespDto = new MallCategoryRespDto();
            mallCategoryRespDto.setCategoryId(mallCategory.getId());
            List<ContentI8nDto> contentI8nDtos = JsonUtil.readList(mallCategory.getName(), ContentI8nDto.class);
            if (!CollectionUtils.isEmpty(contentI8nDtos)) {
                //类目标题
                contentI8nDtos.stream()
                        .filter(s -> Objects.equals(s.getLanguageCode(), user.getLanguageCode()) && StringUtils.hasText(s.getContent()))
                        .findFirst()
                        .or(() -> contentI8nDtos.stream()
                                .filter(s -> Objects.equals(s.getLanguageCode(), mallCategory.getDefaultLanguageCode()) && StringUtils.hasText(s.getContent()))
                                .findFirst()
                        )
                        .ifPresent(s -> mallCategoryRespDto.setCategoryName(s.getContent()));
            }
            //商品信息
            List<MallCategoryGoods> goodsList = mallCategoryGoodsService.findLByCategoryId(mallCategory.getId());
            if (!CollectionUtils.isEmpty(goodsList)) {
                //获取商品标签
                Set<Long> goodsIds = goodsList.stream().map(MallCategoryGoods::getGoodsId).collect(Collectors.toSet());
                Map<Long,List<AppGoodsLabelVo>> goodsLabelMap = goodsLabelBizService.findAppGoodsLabelMapByGoodsIds(goodsIds,user.getLanguageCode());
                List<CategoryGoodsRespDto> goodsResp = new ArrayList<>();
                for (MallCategoryGoods mallCategoryGoods : goodsList) {
                    CategoryGoodsRespDto goodsInfo = new CategoryGoodsRespDto();
                    ZnsGoodsEntity goods = znsGoodsService.findById(mallCategoryGoods.getGoodsId());
                    if (Objects.isNull(goods)) {
                        continue;
                    }
                    if (!mallCountryCode.equals(goods.getCountryCode())) {
                        //商品国家不匹配
                        continue;
                    }
                    //获取最小价格的sku
                    ZnsGoodsSkuEntity minPriceSku = znsGoodsSkuService.findMinPriceSku(goods.getId());
                    if (Objects.isNull(minPriceSku)) {
                        continue;
                    }
                    goodsInfo.setGoodsId(mallCategoryGoods.getGoodsId());
                    goodsInfo.setCoverUrl(determineCoverUrl(mallCategoryGoods.getGoodsCoverUrl(), mallCategoryGoods.getGoodsId(), user));

                    GoodsI18nEntity goodsI18nEntity = goodsI18nService.findByLanguageCodeOrDefault(new GoodsI18nQuery().setGoodsId(goods.getId()).setLanguageCode(user.getLanguageCode()), mallCategory.getDefaultLanguageCode());
                    goodsInfo.setOriginalPrice(minPriceSku.getOriginalPrice());
                    goodsInfo.setDiscountedPrice(minPriceSku.getSalePrice());
                    goodsInfo.setGoodsName(Objects.nonNull(goodsI18nEntity) ? goodsI18nEntity.getTitle() : goods.getTitle());
                    goodsInfo.setGuideTitle(getGuideList(goodsI18nEntity));
                    goodsInfo.setCurrencySymbol(I18nConstant.buildCurrency(minPriceSku.getCurrencyCode(), "USD").getCurrencySymbol());
                    //填充路由
                    PrimitiveForest primitiveForest = new PrimitiveForest();
                    primitiveForest.setJumpType(BannerJumpTypeEnum.MALL.getJumpType());
                    primitiveForest.setMallJumpType(MallJumpTypeEnum.GOODS_URL.getJumpType());
                    primitiveForest.setGoodsId(mallCategoryGoods.getGoodsId());
                    AppRoute route = appRouteService.findRoute(primitiveForest);
                    String jumpUrl = route.getJumpUrl();
                    if (isTestGroup) {
                        //测试组还要看商品是否有可用活动，有可用活动才能跳转中间页
                        List<EquipmentActivityVo> activityVoList = mainActivityBizService.equipmentActivityList(goods.getEquipmentModel(), user.getZoneId(), user.getId(), user.getStateCode(), user.getCountryCode(), 1, "en_US", false);
                        if (!CollectionUtils.isEmpty(activityVoList)) {
                            jumpUrl = GOODS_MIDDLE_URL_VALUE;
                        }
                    }
                    goodsInfo.setJumpUrl(jumpUrl);
                    goodsInfo.setJumpParam(route.getJumpParam());
                    //计算优惠券价格
                    SkuCouponAmountVo skuCouponAmountVo = mallCouponBizService.calSkuCouponAmount(minPriceSku.getId(), 1, user.getAppVersion());
                    if (Objects.nonNull(skuCouponAmountVo) && Objects.nonNull(skuCouponAmountVo.getCouponId())) {
                        goodsInfo.setOriginalPrice(skuCouponAmountVo.getCouponOriginalPrice());
                        goodsInfo.setDiscountedPrice(skuCouponAmountVo.getCouponSalePrice());
                        goodsInfo.setIsPredicted(true);
                    } else {
                        goodsInfo.setIsPredicted(false);
                    }
                    //填充币种+折扣
                    goodsInfo.setCurrencySymbol(I18nConstant.buildCurrency(minPriceSku.getCurrencyCode()).getCurrencySymbol());
                    //goodsInfo.setDiscount(new GoodsAmountVo(goodsInfo.getOriginalPrice(), goodsInfo.getDiscountedPrice()).calDiscount());
                    goodsInfo.setDiscount(new GoodsAmountVo(skuCouponAmountVo).calDiscount());
                    //填充商品标签
                    goodsInfo.setGoodsLabelVoList(goodsLabelMap.getOrDefault(goods.getId(),Collections.emptyList()));
                    goodsResp.add(goodsInfo);
                }
                mallCategoryRespDto.setGoodsInfoList(goodsResp);
            }
            result.add(mallCategoryRespDto);
        }
        return result;
    }

    //首页列表
    public List<MallHomePageRespDto> homePageList(ZnsUserEntity user, String uuid) {
        boolean newApp = user.getAppVersion() >= Constant.appVersion_4044;
        List<MallHomePageRespDto> result = new ArrayList<>();
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(user, null);
        MallHomePage homePage = mallHomePageService.findAppHomePage(user.getId(),mallCountryCode);
        boolean isDefaultHomePage = Objects.equals(homePage.getIsDefault(), 0);
        List<MallHomeModule> moduleList = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(homePage.getId()));
        //老app要过滤优惠券
        moduleList = moduleList.stream().filter(s -> newApp || !Objects.equals(s.getType(), 3)).toList();

        ZnsUserEquipmentEntity userEquipment = znsUserEquipmentService.selectByUserAndSort(user.getId());
        UserDetailDo userDetail = userDetailService.findByUserId(user.getId());

        // 根据timeStyle和用户时区过滤模块
        moduleList = filterModulesByTimeZone(moduleList, user);

        //用户可以看到的关联的模块
        List<MallHomeModule> relevantList = moduleList.stream().filter(s -> Objects.equals(s.getIsRelevant(), 0)).toList();
        List<MallHomeModule> retainedModuleList = relevantList.stream().filter(item -> StringUtils.hasText(item.getSourceEquipmentModel()))
                .filter(item -> predict(item, userDetail, userEquipment)).toList();

        //过滤后，没有任何模块可以展示当前用户, 查询默认的商城首页
        if (!isDefaultHomePage && CollectionUtils.isEmpty(retainedModuleList) && Objects.equals(moduleList.size(), relevantList.size())) {
            MallHomePage defaultMallHomePage = mallHomePageService.findDefaultMallHomePage(mallCountryCode);
            if (Objects.isNull(defaultMallHomePage)){
                defaultMallHomePage = mallHomePageService.findUSDefaultMallHomePage();
            }
            if (!defaultMallHomePage.getCountryType().equals(mallCountryCode)){
                //首页跟用户国家不一致，更新用户国家
                userExtraService.updateUserMallCountryCode(user.getId(), defaultMallHomePage.getCountryType());
            }
            moduleList = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(defaultMallHomePage.getId()));
            moduleList = filterModulesByTimeZone(moduleList, user);
            isDefaultHomePage = true;
        }

        //获取中间页ab分组结果，true：测试组，false：正常组
        boolean isTestGroup = false;
        if (user.getAppVersion() >= Constant.appVersion_40410) {
            //老版本不做测试组校验
            isTestGroup = userBizService.isTestGroupByPage(user, uuid, UserConstant.PageCodeEnum.GOODS_MIDDLE_PAGE);
        }
        //4.4.9老版本没有视频模块
        if (user.getAppVersion() < Constant.appVersion_40410) {
            moduleList = moduleList.stream().filter(s -> !Objects.equals(s.getType(), 4)).collect(Collectors.toList());
        }
        dealModuleList(user, moduleList, userDetail, userEquipment, mallCountryCode, isTestGroup, result);
        if (CollectionUtils.isEmpty(result) && !isDefaultHomePage){
            //如果没有任何模块展示,并且不是默认首页, 查询默认的商城首页
            MallHomePage defaultMallHomePage = mallHomePageService.findDefaultMallHomePage(mallCountryCode);
            if (Objects.isNull(defaultMallHomePage)){
                defaultMallHomePage = mallHomePageService.findUSDefaultMallHomePage();
            }
            if (!defaultMallHomePage.getCountryType().equals(mallCountryCode)){
                //首页跟用户国家不一致，更新用户国家
                userExtraService.updateUserMallCountryCode(user.getId(), defaultMallHomePage.getCountryType());
            }
            moduleList = mallHomeModuleService.findListByQuery(new HomeModuleQuery().setMallHomeId(defaultMallHomePage.getId()));
            dealModuleList(user, moduleList, userDetail, userEquipment, mallCountryCode, isTestGroup, result);
        }
        return result;
    }

    private void dealModuleList(ZnsUserEntity user, List<MallHomeModule> moduleList, UserDetailDo userDetail, ZnsUserEquipmentEntity userEquipment, String mallCountryCode, boolean isTestGroup, List<MallHomePageRespDto> result) {
        for (MallHomeModule mallHomeModule : moduleList) {
            MallHomePageRespDto mallHomePageRespDto = new MallHomePageRespDto();
            if (Objects.equals(mallHomeModule.getIsRelevant(), 0)) {
                if (!predict(mallHomeModule, userDetail, userEquipment)) {
                    continue;
                }
            }
            boolean flag = Objects.equals(mallHomeModule.getStyle(), 0);
            switch (mallHomeModule.getType()) {
                case 0:
                    mallHomePageRespDto.setType(flag ? HomeModuleTypeEnum.SINGLE_IMAGE.getCode()
                            : HomeModuleTypeEnum.MULTIPLE_IMAGE.getCode());
                    break;
                case 1:
                    mallHomePageRespDto.setType(flag ? HomeModuleTypeEnum.SINGLE_GOODS.getCode()
                            : HomeModuleTypeEnum.MULTIPLE_GOODS.getCode());
                    break;
                case 2:
                    mallHomePageRespDto.setType(HomeModuleTypeEnum.TEXT.getCode());
                    break;
                case 3:
                    mallHomePageRespDto.setType(flag ? HomeModuleTypeEnum.SINGLE_COUPON.getCode()
                            : HomeModuleTypeEnum.MULTIPLE_COUPON.getCode());
                    break;
                case 4:
                    mallHomePageRespDto.setType(HomeModuleTypeEnum.VIDEO.getCode());
                    break;
            }
            MallHomeModuleI8n mallHomeModuleI8n = mallHomeModuleI8nService.findListByLanguage(mallHomeModule.getId(), user.getLanguageCode(), mallHomeModule.getDefaultLanguageCode());
            mallHomePageRespDto.setTitle(mallHomeModuleI8n.getTitle());

            switch (HomeModuleTypeEnum.fromCode(mallHomePageRespDto.getType())) {
                case TEXT -> {
                    MallTextDto mallTextDto = buildMallTextDto(mallCountryCode, mallHomeModule, mallHomeModuleI8n);
                    mallHomePageRespDto.setMallTextDto(mallTextDto);
                }
                case SINGLE_IMAGE, MULTIPLE_IMAGE -> {
                    MallImageDto mallImageDto = buildMallImageDto(mallCountryCode, mallHomeModule, mallHomeModuleI8n, mallHomePageRespDto.getType());
                    mallHomePageRespDto.setMallImageDto(mallImageDto);
                }
                case SINGLE_GOODS, MULTIPLE_GOODS -> {
                    mallHomePageRespDto.setType(HomeModuleTypeEnum.SINGLE_GOODS.getCode());
                    List<MallHomeGoodDto> mallGoodDto = buildMallGoodDto(mallHomeModule, user, isTestGroup, mallCountryCode);
                    if (!CollectionUtils.isEmpty(mallGoodDto)) {
                        mallHomePageRespDto.setMallGoodDto(mallGoodDto);
                        if (mallGoodDto.size() > 1) {
                            mallHomePageRespDto.setType(HomeModuleTypeEnum.MULTIPLE_GOODS.getCode()); //多商品
                        }
                    }else {
                        mallHomePageRespDto = null;
                    }
                }
                case SINGLE_COUPON, MULTIPLE_COUPON -> {
                    mallHomePageRespDto.setType(HomeModuleTypeEnum.SINGLE_COUPON.getCode());
                    List<MallCouponDto> mallCouponDtoList = buildMallCouponDto(mallHomeModule, user, mallCountryCode);
                    if (!CollectionUtils.isEmpty(mallCouponDtoList)) {
                        mallHomePageRespDto.setMallCouponDtoList(mallCouponDtoList);
                        if (mallCouponDtoList.size() > 1) {
                            mallHomePageRespDto.setType(HomeModuleTypeEnum.MULTIPLE_COUPON.getCode()); //多张券
                        }
                    } else {
                        mallHomePageRespDto = null;
                    }
                }
                case VIDEO -> {
                    MallVideoDto mallVideoDto = buildVideoDto(mallHomeModule, mallHomeModuleI8n);
                    mallHomePageRespDto.setMallVideoDto(mallVideoDto);
                }
            }
            if (Objects.nonNull(mallHomePageRespDto)) {
                result.add(mallHomePageRespDto);
            }
        }
    }

    private boolean predict(MallHomeModule item, UserDetailDo userDetail, ZnsUserEquipmentEntity userEquipment) {
        List<String> equipmentList = JsonUtil.readList(item.getSourceEquipmentModel(), String.class);
        boolean isMatchNewMallUser = Objects.equals(item.getUserGroup(), MallHomeModuleEnum.UserGroupEnum.NEW_MALL_USER.getCode()) && (Objects.isNull(userDetail) || Objects.equals(YesNoStatus.NO.getCode(), userDetail.getMallPurchaseFlag()));
        boolean isMatchEquipmentModel = Objects.nonNull(userEquipment) && equipmentList.contains(userEquipment.getEquipmentModel());

        if (Objects.isNull(userDetail)) {
            log.info("商城首页人群分类判断，itemId={}-{}, relationType={}, isMatchNewMallUser={},isMatchEquipmentModel={}", item.getMallHomeId(), item.getId(), item.getRelationType(), isMatchNewMallUser, isMatchEquipmentModel);
        } else {
            log.info("商城首页人群分类判断，itemId={}-{}, userId={}, relationType={}, isMatchNewMallUser={}-{},isMatchEquipmentModel={}", item.getMallHomeId(), item.getId(), userDetail.getUserId(), item.getRelationType(), isMatchNewMallUser, userDetail.getMallPurchaseFlag(), isMatchEquipmentModel);
        }

        if (Objects.equals(item.getRelationType(), MallHomeModuleEnum.relationTypeEnum.AND.getCode())) {
            return isMatchNewMallUser && isMatchEquipmentModel;
        } else {
            return isMatchNewMallUser || isMatchEquipmentModel;
        }
    }

    private MallVideoDto buildVideoDto(MallHomeModule mallHomeModule, MallHomeModuleI8n mallHomeModuleI8n) {
        MallVideoDto mallVideoDto = new MallVideoDto();
        mallVideoDto.setVideoUrl(mallHomeModuleI8n.getVideoUrl());
        mallVideoDto.setVideoCoverUrl(mallHomeModuleI8n.getVideoCoverUrl());
        mallVideoDto.setWidth(mallHomeModule.getWidth());
        mallVideoDto.setVideoRatio(mallHomeModule.getVideoRatio());
        return mallVideoDto;
    }

    /**
     * 构建商城优惠券
     */
    private List<MallCouponDto> buildMallCouponDto(MallHomeModule mallHomeModule, ZnsUserEntity user, String mallCountryCode) {
        if (!StringUtils.hasText(mallHomeModule.getSourceCode())) {
            return new ArrayList<>();
        }
        //查询优惠券
        String sourceCodeStr = mallHomeModule.getSourceCode().replaceAll("，", ",");
        CouponQuery query = new CouponQuery().setIsDelete(0).setReceiveStartLe(ZonedDateTime.now()).setReceiveEndGt(ZonedDateTime.now())
                .setExchangeCodes(Arrays.stream(sourceCodeStr.split(",")).toList());
        List<Coupon> list = couponService.findList(query);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Currency currency = mallExchangeRateSwitchService.getCurrencyByCountryCode(mallCountryCode);
        return mallCouponConvertComponent.appConvertDto(list, user, false, currency.getCurrencyCode(), null);
    }

    private MallTextDto buildMallTextDto(String mallCountryCode, MallHomeModule mallHomeModule, MallHomeModuleI8n mallHomeModuleI8n) {
        MallTextDto mallTextDto = new MallTextDto();
        mallTextDto.setTextColor(mallHomeModule.getTextColor());
        mallTextDto.setBackgroundColor(mallHomeModule.getBackgroundColor());
        List<MallHomeJumpDto> mallHomeJumpDtos = JsonUtil.readList(mallHomeModuleI8n.getContent(), MallHomeJumpDto.class);
        fileRoute(mallCountryCode, mallHomeJumpDtos);
        mallTextDto.setContentList(mallHomeJumpDtos);
        return mallTextDto;
    }

    private MallImageDto buildMallImageDto(String mallCountryCode, MallHomeModule mallHomeModule, MallHomeModuleI8n mallHomeModuleI8n, Integer type) {
        MallImageDto mallImageDto = new MallImageDto();
        if (Objects.equals(type, 1)) {
            mallImageDto.setHeight(mallHomeModule.getHeight());
            mallImageDto.setWidth(mallHomeModule.getWidth());
        } else {
            mallImageDto.setAspectRatio(mallHomeModule.getAspectRatio());
        }

        List<MallHomeJumpDto> mallHomeJumpDtos = JsonUtil.readList(mallHomeModuleI8n.getContent(), MallHomeJumpDto.class);
        fileRoute(mallCountryCode, mallHomeJumpDtos);
        mallImageDto.setJumpDtoList(mallHomeJumpDtos);
        return mallImageDto;
    }

    private List<MallHomeGoodDto> buildMallGoodDto(MallHomeModule mallHomeModule, ZnsUserEntity user, boolean isTestGroup, String mallCountryCode) {
        List<HomeModuleGood> moduleGoods = homeModuleGoodService.findListByQuery(
                new HomeModuleGoodQuery().setModuleId(mallHomeModule.getId())
        );
        if (CollectionUtils.isEmpty(moduleGoods)) {
            return Collections.emptyList();
        }
        //获取商品标签
        Set<Long> goodsIds = moduleGoods.stream().map(HomeModuleGood::getGoodsId).collect(Collectors.toSet());
        Map<Long,List<AppGoodsLabelVo>> goodsLabelMap = goodsLabelBizService.findAppGoodsLabelMapByGoodsIds(goodsIds,user.getLanguageCode());
        //组装商品数据
        return moduleGoods.stream()
                .map(moduleGood -> {
                    ZnsGoodsEntity goods = null;
                    if (Objects.nonNull(moduleGood.getGoodsId())) {
                        goods = znsGoodsService.findById(moduleGood.getGoodsId());
                    } else if (Objects.nonNull(moduleGood.getProductId())) {
                        goods = znsGoodsService.findByProductIdAndCountryCode(moduleGood.getProductId(), mallCountryCode);
                    }
                    if (Objects.isNull(goods)) {
                        //商品国家不匹配
                        return null;
                    }
                    if (!mallCountryCode.equals(goods.getCountryCode()) || goods.getStockCount() <= 0 || !Objects.equals(goods.getStatus(),1)) {
                        //商品国家不匹配,库存不足，未上架
                        return null;
                    }
                    MallHomeGoodDto mallHomeGoodDto = new MallHomeGoodDto();
                    mallHomeGoodDto.setGoodsId(moduleGood.getGoodsId());
                    fillMallGoodDto(mallHomeGoodDto, moduleGood, goods, user, mallHomeModule, isTestGroup);
                    //填充标签
                    mallHomeGoodDto.setGoodsLabelVoList(goodsLabelMap.getOrDefault(moduleGood.getGoodsId(), Collections.emptyList()));

                    return mallHomeGoodDto;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void fillMallGoodDto(MallHomeGoodDto dto, HomeModuleGood moduleGood, ZnsGoodsEntity goods,
                                 ZnsUserEntity user, MallHomeModule mallHomeModule, boolean isTestGroup) {
        ZnsGoodsSkuEntity minPriceSku = znsGoodsSkuService.findMinPriceSku(goods.getId());
        GoodsI18nEntity goodsI18nEntity = goodsI18nService.findByLanguageCodeOrDefault(
                new GoodsI18nQuery().setGoodsId(goods.getId()).setLanguageCode(user.getLanguageCode()),
                mallHomeModule.getDefaultLanguageCode()
        );

        // 填充商品属性
        dto.setGuideTitle(getGuideList(goodsI18nEntity));
        dto.setGoodsName(Objects.nonNull(goodsI18nEntity) ? goodsI18nEntity.getTitle() : goods.getTitle());
        dto.setCoverUrl(determineCoverUrl(moduleGood.getCoverUrl(), goods.getId(), user));
        dto.setOriginalPrice(Objects.nonNull(minPriceSku) ? minPriceSku.getOriginalPrice() : goods.getOriginalPrice());
        dto.setDiscountedPrice(Objects.nonNull(minPriceSku) ? minPriceSku.getSalePrice() : goods.getSalePrice());
        dto.setVideoUrl(moduleGood.getVideoUrl());
        if (StringUtils.hasText(moduleGood.getVideoCoverUrl()) && user.getAppVersion() >= Constant.appVersion_40410) {
            dto.setCoverUrl(moduleGood.getVideoCoverUrl());
        }
        //计算券后价格
        dto.setIsPredicted(false);
        if (minPriceSku != null) {
            SkuCouponAmountVo skuCouponAmountVo = mallCouponBizService.calSkuCouponAmount(minPriceSku.getId(), 1, user.getAppVersion());
            if (Objects.nonNull(skuCouponAmountVo.getCouponId())) {
                dto.setOriginalPrice(skuCouponAmountVo.getCouponOriginalPrice());
                dto.setDiscountedPrice(skuCouponAmountVo.getCouponSalePrice());
                dto.setIsPredicted(true);
            }
            //填充币种+折扣
            dto.setCurrencySymbol(I18nConstant.buildCurrency(minPriceSku.getCurrencyCode()).getCurrencySymbol());
            dto.setDiscount(new GoodsAmountVo(skuCouponAmountVo).calDiscount());
        }


        // 填充路由
        goodsFillRoute(goods, dto, isTestGroup, user.getId());
    }

    private String determineCoverUrl(String url, Long goodsId, ZnsUserEntity user) {
        if (StringUtils.hasText(url)) {
            return url;
        }
        List<ZnsGoodsFileEntity> i18nListFiles = znsGoodsFileService.getI18nListFiles(goodsId, user.getLanguageCode());
        if (!CollectionUtils.isEmpty(i18nListFiles)) {
            return i18nListFiles.stream()
                    .filter(file -> Objects.equals(file.getFileType(), 1))
                    .map(ZnsGoodsFileEntity::getFileUrl)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }


    private void goodsFillRoute(ZnsGoodsEntity goodsEntity, MallHomeGoodDto mallHomeGoodDto, boolean isTestGroup, Long userId) {
        //填充路由
        Long goodsId = goodsEntity.getId();
        PrimitiveForest primitiveForest = new PrimitiveForest();
        primitiveForest.setJumpType(BannerJumpTypeEnum.MALL.getJumpType());
        primitiveForest.setMallJumpType(MallJumpTypeEnum.GOODS_URL.getJumpType());
        primitiveForest.setGoodsId(goodsId);
        AppRoute route = appRouteService.findRoute(primitiveForest);
        String jumpUrl = route.getJumpUrl();
        if (isTestGroup) {
            ZnsUserEntity user = userService.findById(userId);
            String stateCode = Optional.ofNullable(user).map(ZnsUserEntity::getStateCode).orElse(null);
            String countryCode = Optional.ofNullable(user).map(ZnsUserEntity::getCountryCode).orElse(null);
            String zoneId = Optional.ofNullable(user).map(ZnsUserEntity::getZoneId).filter(StringUtils::hasText).orElse("America/New_York");
            //测试组还要看商品是否有可用活动，有可用活动才能跳转中间页
            List<EquipmentActivityVo> activityVoList = mainActivityBizService.equipmentActivityList(goodsEntity.getEquipmentModel(), zoneId, userId, stateCode, countryCode, 1, "en_US", false);
            if (!CollectionUtils.isEmpty(activityVoList)) {
                jumpUrl = GOODS_MIDDLE_URL_VALUE;
            }
        }
        mallHomeGoodDto.setJumpUrl(jumpUrl);
        mallHomeGoodDto.setJumpParam(route.getJumpParam());
    }

    //商品列表
    public List<MallHomeGoodDto> goodsList(GoodsQuery goodsQuery, ZnsUserEntity user, Long couponId, String uuid, Integer querySource) {
        List<ZnsGoodsEntity> list;
        Integer appVersion = user.getAppVersion();
        if (Objects.isNull(couponId)) {
            list = znsGoodsService.findList(goodsQuery);
        } else {
            //根据优惠券查询
            ActivityCouponConfigQuery query = ActivityCouponConfigQuery.builder().couponId(couponId).type(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type).build();
            List<ActivityCouponConfig> couponConfigs = activityCouponConfigService.findListByQuery(query);
            if (CollectionUtils.isEmpty(couponConfigs)) {
                return Collections.emptyList();
            }
            List<Long> goodIds = couponConfigs.stream().map(ActivityCouponConfig::getCouponConfig).map(Long::valueOf).distinct().toList();
            GoodsQuery couponGoodsQuery = new GoodsQuery().setGoodsIds(goodIds).setStatus(1);
            list = znsGoodsService.findList(couponGoodsQuery);
        }
        //过滤商城国家
        final String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(user, null);
        list = list.stream().filter(item -> mallCountryCode.equals(item.getCountryCode()))
                .sorted(Comparator.comparing(ZnsGoodsEntity::getInitialSaleCount).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        boolean isTestGroup = false;  //商品中间页ab分组结果，true：测试组，false：正常组
        if (Objects.isNull(querySource) && appVersion >= Constant.appVersion_40410) {
            //不是跳转查询+ 4.4.9以上版本 需要判断是否为测试组
            isTestGroup = userBizService.isTestGroupByPage(user, uuid, UserConstant.PageCodeEnum.GOODS_MIDDLE_PAGE);
        }
        //获取商品标签
        Set<Long> goodsIds = list.stream().map(ZnsGoodsEntity::getId).collect(Collectors.toSet());
        Map<Long,List<AppGoodsLabelVo>> goodsLabelMap = goodsLabelBizService.findAppGoodsLabelMapByGoodsIds(goodsIds,user.getLanguageCode());
        List<MallHomeGoodDto> result = new ArrayList<>();
        for (ZnsGoodsEntity znsGoodsEntity : list) {
            MallHomeGoodDto goods = new MallHomeGoodDto();
            ZnsGoodsSkuEntity minPriceSku = znsGoodsSkuService.findMinPriceSku(znsGoodsEntity.getId());
            GoodsI18nEntity goodsI18nEntity = goodsI18nService.findByLanguageCodeOrDefault(new GoodsI18nQuery().setGoodsId(znsGoodsEntity.getId()).setLanguageCode(user.getLanguageCode()), znsGoodsEntity.getDefaultLangCode());
            List<ZnsGoodsFileEntity> i18nListFiles = znsGoodsFileService.getI18nListFiles(znsGoodsEntity.getId(), user.getLanguageCode());
            if (!CollectionUtils.isEmpty(i18nListFiles)) {
                List<ZnsGoodsFileEntity> collect = i18nListFiles.stream().filter(goodsFile -> Objects.equals(goodsFile.getFileType(), 1)).toList();
                goods.setCoverUrl(collect.get(0).getFileUrl());
            }
            List<String> guideList = getGuideList(goodsI18nEntity);
            goods.setGuideTitle(guideList);
            goods.setGoodsName(Objects.nonNull(goodsI18nEntity) ? goodsI18nEntity.getTitle() : znsGoodsEntity.getTitle());
            goods.setGoodsId(znsGoodsEntity.getId());
            goods.setOriginalPrice(Objects.nonNull(minPriceSku) ? minPriceSku.getOriginalPrice() : znsGoodsEntity.getOriginalPrice());
            goods.setDiscountedPrice(Objects.nonNull(minPriceSku) ? minPriceSku.getSalePrice() : znsGoodsEntity.getSalePrice());
            //填充路由
            goodsFillRoute(znsGoodsEntity, goods, isTestGroup, user.getId());
            //计算优惠券价格
            SkuCouponAmountVo skuCouponAmountVo = mallCouponBizService.calSkuCouponAmount(minPriceSku.getId(), 1, appVersion);
            if (Objects.nonNull(skuCouponAmountVo) && Objects.nonNull(skuCouponAmountVo.getCouponId())) {
                goods.setOriginalPrice(skuCouponAmountVo.getCouponOriginalPrice());
                goods.setDiscountedPrice(skuCouponAmountVo.getCouponSalePrice());
                goods.setIsPredicted(true);
            } else {
                goods.setIsPredicted(false);
            }
            //填充币种+折扣
            goods.setCurrencySymbol(I18nConstant.buildCurrency(minPriceSku.getCurrencyCode()).getCurrencySymbol());
            goods.setDiscount(new GoodsAmountVo(skuCouponAmountVo).calDiscount());
            //填充商品标签
            goods.setGoodsLabelVoList(goodsLabelMap.getOrDefault(znsGoodsEntity.getId(),Collections.emptyList()));
            result.add(goods);
        }
        return result;
    }

    @Nullable
    private static List<String> getGuideList(GoodsI18nEntity goodsI18nEntity) {
        List<String> guideList = new ArrayList<>();
        if (Objects.nonNull(goodsI18nEntity)) {
            guideList = JsonUtil.readList(goodsI18nEntity.getShoppingGuide(), String.class);
            if (!CollectionUtils.isEmpty(guideList)) {
                guideList = guideList.stream()
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toList());
            }
        }
        return guideList;
    }

    private void fileRoute(String mallCountryCode, List<MallHomeJumpDto> mallHomeJumpDtos) {

        if (CollectionUtils.isEmpty(mallHomeJumpDtos)) {
            return;
        }
        for (MallHomeJumpDto mallHomeJumpDto : mallHomeJumpDtos) {
            if (Objects.isNull(mallHomeJumpDto.getJumpType())) {
                continue;
            }
            PrimitiveForest primitiveForest = new PrimitiveForest();
            primitiveForest.setJumpType(mallHomeJumpDto.getJumpType());
            if (Objects.equals(mallHomeJumpDto.getJumpType(), 9)) {
                Long goodsId = StringUtils.hasText(mallHomeJumpDto.getJumpParam()) ? Long.parseLong(mallHomeJumpDto.getJumpParam()) : -1;
                if (Objects.nonNull(mallHomeJumpDto.getProductId())) {
                    //通过productId +  countryCode 查询商品
                    ZnsGoodsEntity goodsEntity = znsGoodsService.findByProductIdAndCountryCode(mallHomeJumpDto.getProductId(), mallCountryCode);
                    if (!Objects.isNull(goodsEntity)) {
                        goodsId = goodsEntity.getId();
                    }
                }
                ZnsGoodsEntity goodsEntity = znsGoodsService.findById(goodsId);
                if (Objects.isNull(goodsEntity) || !goodsEntity.getCountryCode().contains(mallCountryCode)) {
                    //商品不存在或者商品国家不匹配默认-1不跳转
                    goodsId = -1L;
                }
                if (Objects.nonNull(goodsId)) {
                    primitiveForest.setGoodsId(goodsId);
                }
            } else {
                primitiveForest.setCategoryCode(mallHomeJumpDto.getJumpParam());
            }
            primitiveForest.setJumpUrl(mallHomeJumpDto.getJumpParam());
            primitiveForest.setJumpType(mallHomeJumpDto.getJumpType());
            //填充路由
            AppRoute route = appRouteService.findRoute(primitiveForest);
            mallHomeJumpDto.setJumpUrl(route.getJumpUrl());
            mallHomeJumpDto.setJumpParam(route.getJumpParam());
        }
    }

    /**
     * 商品政策信息
     *
     * @return
     */
    public List<GoodsPolicyListResponseDto> getPolicies(ZnsUserEntity loginUser) {
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(loginUser, null);
        return goodsPolicyService.findList(new GoodsPolicyQuery().setIsDelete(0).setStatus(1).setCountryCode(mallCountryCode)).stream().map(goodsPolicy -> {
            GoodsPolicyListResponseDto dto = new GoodsPolicyListResponseDto();
            BeanUtils.copyProperties(goodsPolicy, dto);
            GoodsPolicyI18nDo goodsPolicyI18nDo = goodsPolicyI18nService.findByQuery(new GoodsPolicyI18nQuery().setPolicyId(goodsPolicy.getId()).setLanguageCode(loginUser.getLanguageCode()));
            if (Objects.isNull(goodsPolicyI18nDo)) {
                goodsPolicyI18nDo = goodsPolicyI18nService.findByQuery(new GoodsPolicyI18nQuery().setPolicyId(goodsPolicy.getId()).setLanguageCode(goodsPolicy.getDefaultLanguageCode()));
            }
            if (Objects.nonNull(goodsPolicyI18nDo)) {
                dto.setDefaultTitle(goodsPolicyI18nDo.getModuleTitle());
                dto.setDefaultSubTitle(goodsPolicyI18nDo.getModuleSubTitle());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 商城首页配置信息
     */
    public MallIndexInfoResponseDto indexInfo(Long userId, String languageCode) {
        userId = Optional.ofNullable(userId).orElse(0L);
        MallIndexInfoResponseDto resp = new MallIndexInfoResponseDto();
        MallHomePage defaultMallHomePage = mallHomePageService.findUSDefaultMallHomePage();
        if (Objects.equals(userId, 0L)) {
            //仅激活
            resp.setHomePageId(defaultMallHomePage.getId());
            resp.setCountryCode(I18nConstant.CountryCodeEnum.US.code);
            resp.setCountryName(I18nConstant.CountryCodeEnum.US.name);
            resp.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
            resp.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
            return resp;
        }
        //获取用户信息
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(userId, null);
        MallHomePage homePage = mallHomePageService.findAppHomePage(userId, mallCountryCode);
        if (homePage == null) {
            homePage = mallHomePageService.findUSDefaultMallHomePage();
            mallCountryCode = I18nConstant.CountryCodeEnum.US.code;
        }
        mallCountryCode = StringUtils.hasText(mallCountryCode) ? mallCountryCode : I18nConstant.CountryCodeEnum.US.code;
        resp.setHomePageId(homePage.getId());
        resp.setCountryCode(mallCountryCode);
        resp.setCountryName(I18nConstant.CountryCodeEnum.findByCode(mallCountryCode).enName);

        ZnsCountryEntity countryEntity = znsCountryService.findByCountryCode(mallCountryCode);
        if (Objects.nonNull(countryEntity)) {
            resp.setStandardCode(countryEntity.getStandardCode());
        }

        //查询国家I18n
        CountryI18nEntity countryI18nEntity = countryI18nService.findByQuery(CountryI18nQuery.builder().countryCode(mallCountryCode).languageCode(languageCode).build());
        if (Objects.nonNull(countryI18nEntity)) {
            resp.setCountryName(countryI18nEntity.getName());
        }

        //查询国家币种
        MallExchangeRateSwitchDo exchangeRateSwitchDo = mallExchangeRateSwitchService.findByQuery(new MallExchangeRateSwitchQuery().setAppSwitch(0).setCountryCode(mallCountryCode));
        if (exchangeRateSwitchDo != null) {
            Currency currency = I18nConstant.buildCurrency(exchangeRateSwitchDo.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
            resp.setCurrencySymbol(currency.getCurrencySymbol());
            resp.setCurrencyCode(currency.getCurrencyCode());
        }
        return resp;
    }

    /**
     * 商城校验ip对应的国家是否支持
     *
     * @param countryName 国家名：德国、英国、美国
     */
    public MallHomePopDto checkIpMallCountry(String countryName, ZnsUserEntity user,String languageCode) {
        MallHomePopDto result = new MallHomePopDto();
        //查询商城支持的国家
        List<String> supportCountryCodes = new ArrayList<>();
        if (user.getAppVersion() < VersionConstant.V4_6_4){
            //老版本只支持美国
            log.info("[mallHomePop]--------用户={},countryName={},老版本只支持美国", user.getId(),countryName);
            supportCountryCodes.add(I18nConstant.CountryCodeEnum.US.code);
        }else {
            //新版本取配置国家
            List<MallExchangeRateSwitchDo> list = mallExchangeRateSwitchService.findList(new MallExchangeRateSwitchQuery().setAppSwitch(0));
            if (CollectionUtils.isEmpty(list)) {
                //未配置，使用商城默认国家
                MallHomePage defaultMallHomePage = mallHomePageService.findUSDefaultMallHomePage();
                supportCountryCodes.add(defaultMallHomePage.getCountryType());
            } else {
                //有配置,查询配置国家
                supportCountryCodes.addAll(list.stream().map(MallExchangeRateSwitchDo::getCountryCode).collect(Collectors.toList()));
            }
            log.info("[mallHomePop]--------用户={},countryName={},新版本取配置国家={}", user.getId(),countryName,JsonUtil.writeString(supportCountryCodes));
        }

        //判断是否支持
        boolean support = supportCountryCodes.stream().filter(countryCode -> countryName.equals(I18nConstant.CountryCodeEnum.findByCode(countryCode).name)).findFirst().orElse(null) != null;
        if (!StringUtils.hasText(countryName) || !support) {
            log.info("[mallHomePop]--------用户={},countryName={},商城不支持当前国家，弹框提醒", user.getId(),countryName);
            //不支持改国家,返回提示I18N
            String supportCountryNames = "USA";
            List<ZnsCountryEntity> znsCountryEntities = znsCountryService.selectListByCodes(supportCountryCodes);
            if (!CollectionUtils.isEmpty(znsCountryEntities)){
                StringJoiner stringJoiner = new StringJoiner(",");
                for (ZnsCountryEntity znsCountryEntity : znsCountryEntities) {
                    CountryQuery query = new CountryQuery();
                    query.setCountryCode(znsCountryEntity.getCode());
                    query.setLanguageCode(languageCode);
                    List<CountryVo> i18nList = znsCountryService.findByQuery(query);
                    String supportCountryName = CollectionUtils.isEmpty(i18nList) ? znsCountryEntity.getName() : i18nList.get(0).getName();
                    stringJoiner.add(supportCountryName);
                }
                supportCountryNames = stringJoiner.toString();
            }
            result.setAddressPopContent(I18nMsgUtils.getMessage("mall.home.address.check.pop",supportCountryNames));
            return result;
        }
        return result;
    }

    /**
     * 根据timeStyle和用户时区过滤模块
     * @param moduleList 模块列表
     * @param user 用户信息
     * @return 过滤后的模块列表
     */
    private List<MallHomeModule> filterModulesByTimeZone(List<MallHomeModule> moduleList, ZnsUserEntity user) {
        if (CollectionUtils.isEmpty(moduleList) ) {
            return moduleList;
        }
        return moduleList.stream()
            .filter(module -> isModuleAvailableInUserTimeZone(module, user))
            .collect(Collectors.toList());
    }

    /**
     * 判断模块在用户时区是否可用
     * @param module 模块信息
     * @param user 用户信息
     * @return 是否可用
     */
    private boolean isModuleAvailableInUserTimeZone(MallHomeModule module, ZnsUserEntity user) {
        // 如果模块没有设置时间相关属性，默认可用
        if (module.getTimeStyle() == null) {
            return true;
        }
        // 获取当前时间
        ZonedDateTime now;
        if (Objects.equals(module.getTimeStyle(),0)){
            now = ZonedDateTime.now();
        }else {
            ZoneId userZoneId = ZoneId.of(user.getZoneId());
            now = ZonedDateTime.now(userZoneId);
        }
        if (module.getStartTime() != null && module.getEndTime() != null) {
            return now.isAfter(module.getStartTime()) && now.isBefore(module.getEndTime());
        }
        // 如果没有设置时间限制，默认可用
        return true;
    }

    public CheckUserMallCountryResponseDto checkUserMallCountry(ZnsUserEntity loginUser, CheckUserMallCountryRequestDto requestDto) {
        Long userId = loginUser.getId();
        List<MallCountryVo> mallCountries = regionBizService.mallCountryList(loginUser.getLanguageCode(), loginUser.getAppVersion());

        // 获取用户扩展信息
        UserExtraDo extraDo = userExtraService.findByUserId(userId);
        if (Objects.isNull(extraDo)) {
            return buildResponseDto(mallCountries, false, null, null);
        }

        // 检查用户商城国家代码
        String mallCountryCode = extraDo.getMallCountryCode();
        if (!StringUtils.hasText(mallCountryCode)) {
            return buildResponseDto(mallCountries, false, null, null);
        }

        // 检查商城国家是否开启
        boolean isCountryOpen = checkMallCountryStatus(mallCountryCode);
        if (!isCountryOpen) {
            return buildResponseDto(mallCountries, true, true, null);
        }

        // 根据页面类型检查商品或分类可用性
        boolean hasGoodsOrCategory = checkPageTypeAvailability(requestDto, mallCountryCode);

        return buildResponseDto(mallCountries, true, false, hasGoodsOrCategory);
    }

    /**
     * 构建响应DTO
     */
    private CheckUserMallCountryResponseDto buildResponseDto(List<MallCountryVo> mallCountries,
                                                           Boolean hasMallCountry,
                                                           Boolean mallCountryIsClose,
                                                           Boolean hasGoodsMallCountry) {
        CheckUserMallCountryResponseDto responseDto = new CheckUserMallCountryResponseDto();
        responseDto.setMallCountries(mallCountries);
        responseDto.setHasMallCountry(hasMallCountry);
        responseDto.setMallCountryIsClose(mallCountryIsClose);
        responseDto.setHasGoodsMallCountry(hasGoodsMallCountry);
        return responseDto;
    }

    /**
     * 检查商城国家状态
     */
    private boolean checkMallCountryStatus(String mallCountryCode) {
        MallExchangeRateSwitchQuery switchQuery = new MallExchangeRateSwitchQuery();
        switchQuery.setCountryCode(mallCountryCode);
        switchQuery.setAppSwitch(YesNoStatus.NO.getCode());

        MallExchangeRateSwitchDo mallExchangeRateSwitchDo = mallExchangeRateSwitchService.findByQuery(switchQuery);
        return Objects.nonNull(mallExchangeRateSwitchDo);
    }

    /**
     * 根据页面类型检查可用性
     */
    private boolean checkPageTypeAvailability(CheckUserMallCountryRequestDto requestDto, String mallCountryCode) {
        Integer pageType = requestDto.getPageType();

        if (MallJumpTypeEnum.GOODS_URL.getJumpType().equals(pageType)) {
            return checkGoodsAvailability(requestDto.getGoodsId(), mallCountryCode);
        }

        if (MallJumpTypeEnum.CATEGORY_URL.getJumpType().equals(pageType)) {
            return checkCategoryAvailability(requestDto.getCategoryCode(), mallCountryCode);
        }

        return true;
    }

    /**
     * 检查商品在指定国家的可用性
     */
    private boolean checkGoodsAvailability(Long goodsId, String mallCountryCode) {
        if (Objects.isNull(goodsId)) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "goodsId"));
        }

        ZnsGoodsEntity goodsDo = znsGoodsService.findByIdWithDeleted(goodsId);
        if (Objects.isNull(goodsDo)) {
            return false;
        }

        Long productId = goodsDo.getProductId();
        ZnsGoodsEntity targetGoods = znsGoodsService.findByProductIdAndCountryCode(productId, mallCountryCode);
        return Objects.nonNull(targetGoods);
    }

    /**
     * 检查类目code在指定国家的可用性
     */
    private boolean checkCategoryAvailability(String categoryCode, String mallCountryCode) {
        if (!StringUtils.hasText(categoryCode)) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "categoryCode"));
        }

        MallCategoryPageQueryDto mallCategoryPageQueryDto = new MallCategoryPageQueryDto();
        mallCategoryPageQueryDto.setCategoryPageCode(categoryCode);
        mallCategoryPageQueryDto.setCountryCodeList(List.of(mallCountryCode));

        return !CollectionUtils.isEmpty(mallCategoryPageService.findListByQuery(mallCategoryPageQueryDto));
    }
}
