package com.linzi.pitpat.api.activityservice.manager.impl;

import com.linzi.pitpat.api.activityservice.manager.AppTeamActivityService;
import com.linzi.pitpat.api.activityservice.vo.TeamActivityDetail;
import com.linzi.pitpat.api.activityservice.vo.TeamDetail;
import com.linzi.pitpat.api.activityservice.vo.TeamRank;
import com.linzi.pitpat.api.activityservice.vo.TeamUserInfo;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamJoinStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamTypeEnum;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityOfficialDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.TeamCallRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityTeamQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.TeamActivityCallRequest;
import com.linzi.pitpat.data.activityservice.model.request.TeamInfoRes;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.TeamCallRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSourceEnum;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.vo.AwardRelation;
import com.linzi.pitpat.data.awardservice.service.AwardProcessService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubActivityTeamEnrollStateEnum;
import com.linzi.pitpat.data.clubservice.manager.ClubMemberManager;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.service.ClubActivityInviteService;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.enums.ActivityUserStateEnum;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AppTeamActivityServiceImpl implements AppTeamActivityService {
    private final ActivityTeamService activityTeamService;
    private final ISysConfigService sysConfigService;
    private final ZnsRunActivityUserService activityUserService;
    private final ZnsUserService userService;
    private final AwardProcessService awardProcessService;
    private final MedalConfigService medalConfigService;
    private final ZnsUserRunDataDetailsService runDataDetailsService;
    private final TeamCallRecordService teamCallRecordService;
    private final AppMessageService appMessageService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final UserKolService userKolService;
    private final ClubActivityInviteService clubActivityInviteService;
    private final ClubActivityTeamService clubActivityTeamService;
    private final ClubService clubService;
    private final RunActivityBizService runActivityBizService;

    private final ActivityUserBizService activityUserBizService;
    private final ClubMemberManager clubMemberManager;

    @Override
    public List<TeamRank> getTeamRanks(Long activityId, ZnsUserEntity loginUser) {

        List<ActivityTeam> teams;
//        //查缓存
//        String key = RedisConstants.TEAM_ACTIVITY_TEAM_INFO +"#" +activityId;
//        Object o = redisTemplate.opsForValue().get(key);
//        if (Objects.nonNull(o)){
//             teams = JsonUtil.readValue(o.toString(), ActivityTeam.class);
//        }else {
        //查所有用户队伍
        teams = activityTeamService.findByActId(activityId);
//        }
        //计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();


        ActivityTeam currentTeam = getCurrentUserTeam(activityId);
        List<Long> clubTeamIds = new ArrayList<>();
        List<TeamRank> teamRankList = teams.stream().filter(k -> k.getIsOfficial() == 1 || k.getCurrentNum() > 0
                || ActivityTeamTypeEnum.CLUB.getCode().equals(k.getTeamType())
        ).map(
                t -> {
                    TeamRank teamRank = new TeamRank();
                    if (currentTeam != null) {
                        teamRank.setCurrentUserTeamId(currentTeam.getId());
                    }
                    teamRank.setTeamId(t.getId());
                    teamRank.setRank(t.getRank());
                    teamRank.setTeamName(t.getTeamName());
                    teamRank.setTeamLogo(t.getTeamLogo());
                    teamRank.setMaxNum(t.getMaxNum());
                    teamRank.setCurrentNum(t.getCurrentNum());
                    teamRank.setMileage(t.getMillage());
                    teamRank.setTeamType(t.getTeamType());
                    teamRank.setTeamManagerId(t.getTeamManagerId());
                    teamRank.setCanJoinState(ActivityTeamJoinStateEnum.ALLOW.getCode());
                    teamRank.setGmtCreate(t.getGmtCreate());
                    if (ActivityTeamTypeEnum.CLUB.getCode().equals(t.getTeamType())) {
                        clubTeamIds.add(t.getId());
                    }
                    return teamRank;
                }
        ).collect(Collectors.toList());

        Collections.sort(teamRankList, Comparator.comparingInt(TeamRank::getRank));
        stopWatch.stop();
        log.info("getTeamRanks 统计{}", stopWatch.getTotalTimeMillis());
        //存在俱乐部团队，有登录信息。设置可加入状态
        if (!CollectionUtils.isEmpty(clubTeamIds)) {
            return setClubTeamJoinState(activityId, loginUser, teamRankList, clubTeamIds);
        }
        if (sysConfigService.isMarathon(activityId)) {
            teamRankList = teamRankList.stream().sorted(Comparator.comparing(TeamRank::notFull).thenComparing(TeamRank::getGmtCreate))
                    .collect(Collectors.toList());
        }

        return teamRankList;
    }

    /**
     * 设置俱乐部队伍，用户是否可以参加的状态
     *
     * @param activityId   活动id
     * @param loginUser    当前登录用户
     * @param teamRankList 所有队伍列表 当前只有 仅俱乐部队伍  so clubTeamids 就是所有的teamRankList
     * @param clubTeamIds  俱乐部团队id
     * @return
     */
    private List<TeamRank> setClubTeamJoinState(Long activityId, ZnsUserEntity loginUser, List<TeamRank> teamRankList, List<Long> clubTeamIds) {
        if (loginUser != null) {
            Optional<Club> club = clubService.findByOwnerId(loginUser.getId());
            //获取队伍信息
            List<ClubActivityTeam> clubActivityTeams = clubActivityTeamService.findByTeamIds(clubTeamIds);
            Map<Long, ClubActivityTeam> clubActivityTeamMap = new HashMap<>();
            Map<Long, ClubActivityTeam> clubActivityTeamIdMap = new HashMap<>();
            for (ClubActivityTeam clubActivityTeam : clubActivityTeams) {
                clubActivityTeamMap.put(clubActivityTeam.getClubId(), clubActivityTeam);
                clubActivityTeamIdMap.put(clubActivityTeam.getActivityTeamId(), clubActivityTeam);
            }
            List<TeamRank> resultTeamRank = new ArrayList<>();
            for (TeamRank i : teamRankList) {
                ClubActivityTeam clubActivityTeam = clubActivityTeamIdMap.get(i.getTeamId());
                if (ClubActivityTeamEnrollStateEnum.JOINED.getCode().equals(clubActivityTeam.getState())) {
                    resultTeamRank.add(i);
                } else if (ClubActivityTeamEnrollStateEnum.CREATE.getCode().equals(clubActivityTeam.getState())
                        && i.getTeamManagerId().equals(loginUser.getId())) {
                    resultTeamRank.add(i);
                }
            }


            if (club.isPresent()) {
                //已经是俱乐部负责人
                if (club.get().isNormal()) {
                    ClubActivityTeam clubActivityTeam = clubActivityTeamMap.get(club.get().getId());

                    //kol只能加入自己的团队
                    resultTeamRank.forEach(t -> {
                        if (clubActivityTeam != null && clubActivityTeam.getActivityTeamId().equals(t.getTeamId())) {
                            t.setCanJoinState(ActivityTeamJoinStateEnum.ALLOW.getCode());
                        } else {
                            t.setCanJoinState(ActivityTeamJoinStateEnum.DENY.getCode());
                        }
                    });
                } else {
                    //kol 已经新建过俱乐部，只能加入自己的队伍
                    resultTeamRank.forEach(t -> {
                        t.setCanJoinState(ActivityTeamJoinStateEnum.DENY.getCode());
                    });
                }
            } else {
                //判断用户是否在队伍俱乐部
                List<Long> teamId = clubMemberManager.checkUserTeamClub(clubActivityTeams, loginUser.getId());
                resultTeamRank.forEach(t -> {
                    if (teamId.contains(t.getTeamId())) {
                        t.setCanJoinState(ActivityTeamJoinStateEnum.ALLOW.getCode());
                    } else {
                        t.setCanJoinState(ActivityTeamJoinStateEnum.DENY.getCode());
                    }
                });
            }
            //teamRank 设置上clubId
            resultTeamRank.forEach(t -> {
                clubActivityTeams.stream().filter(k -> k.getActivityTeamId().equals(t.getTeamId())).findAny().ifPresent(k -> t.setClubId(k.getClubId()));
            });
            return resultTeamRank;
        } else {
            //登录信息不存的时候，所有队伍都禁止加入
            teamRankList.forEach(t -> {
                t.setCanJoinState(ActivityTeamJoinStateEnum.DENY.getCode());
            });
        }

        return teamRankList;
    }

    private ActivityTeam getCurrentUserTeam(Long activityId) {
        ZnsUserEntity loginUser = getLoginUser();
        if (loginUser != null) {
            RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                    .isDelete(0).userStateIn(Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                            ActivityUserStateEnum.RUNING.getState(),
                            ActivityUserStateEnum.ENDED.getState()))
                    .activityId(activityId)
                    .userId(loginUser.getId())
                    .build();

            ZnsRunActivityUserEntity activityUserEntity = activityUserService.findOne(userQuery);
            if (activityUserEntity != null) {
                return activityTeamService.findById(activityUserEntity.getTeamId());
            }
        }

        return null;
    }

    @Override
    public TeamDetail queryTeamDetail(Long teamId, Long userId) {
        //计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        TeamDetail teamDetail = new TeamDetail();
        ActivityTeam team = activityTeamService.findById(teamId);
        //组装队伍基本信息
        teamDetail.setTeamId(teamId);
        teamDetail.setTeamName(team.getTeamName());
        teamDetail.setTeamLogo(team.getTeamLogo());
        teamDetail.setRank(team.getRank());
        teamDetail.setMaxNum(team.getMaxNum());
        teamDetail.setCurrentNum(team.getCurrentNum());
        //TODO 从detail表取还是redis
        teamDetail.setMileage(team.getMillage());
        RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                .teamId(teamId).activityId(team.getActivityId())
                .isDelete(0).userStateIn(Arrays.asList(ActivityUserStateEnum.ACCEPT.getState(),
                        ActivityUserStateEnum.RUNING.getState(),
                        ActivityUserStateEnum.ENDED.getState()))
                .build();

        List<ZnsRunActivityUserEntity> list = activityUserService.findList(userQuery);

//        Map<Long, BigDecimal> map = list.stream().
//                collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, ZnsRunActivityUserEntity::getRunMileage));
        Map<Long, BigDecimal> map = new HashMap<>();
        for (ZnsRunActivityUserEntity activityUserEntity : list) {
            map.put(activityUserEntity.getUserId(), activityUserEntity.getRunMileage());
        }
        if (!CollectionUtils.isEmpty(map.keySet())) {
            List<ZnsUserEntity> users = userService.findByIds(new ArrayList<>(map.keySet()));

            List<TeamUserInfo> infoList = users.stream().map(k -> {
                TeamUserInfo userInfo = new TeamUserInfo();
                userInfo.setUserId(k.getId());
                userInfo.setUserName(k.getFirstName());
                userInfo.setAvatar(k.getHeadPortrait());

                userInfo.setUserMileage(map.get(k.getId()).intValue());
                return userInfo;
            }).collect(Collectors.toList());
            teamDetail.setUserInfos(infoList);
        }
        stopWatch.stop();
        log.info("queryTeamDetail 统计{}", stopWatch.getTotalTimeMillis());
        //查询队伍关联用户
//        List userIds = activityUserService.list(wrapper).stream().map(ZnsRunActivityUserEntity::getUserId).
//                collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(userIds)) {
//            List<ZnsUserEntity> users = userService.listByIds(userIds);
//
//            List<TeamUserInfo> infoList = users.stream().map(k -> {
//                TeamUserInfo userInfo = new TeamUserInfo();
//                userInfo.setUserId(k.getId());
//                userInfo.setUserName(k.getFirstName());
//                userInfo.setAvatar(k.getHeadPortrait());
//
//                userInfo.setUserMileage(map.get(k.getId()).intValue());
//                return userInfo;
//            }).collect(Collectors.toList());
//            teamDetail.setUserInfos(infoList);
//        }
        // 团队赛明细出作弊提示 历史有旧提示

        List<ZnsUserRunDataDetailsEntity> runDetails = runDataDetailsService.findByActivityIdAndUserIdAndNoCheat(team.getActivityId(), userId);

        if (!CollectionUtils.isEmpty(runDetails)) {
            teamDetail.setIsCheat(YesNoStatus.YES.getCode());
        }
        return teamDetail;
    }


    @Override
    public TeamActivityDetail queryActivityDetail(Long activityId) {

        //计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        TeamActivityDetail activityDetail = new TeamActivityDetail();
        ActivityOfficialDto dto = runActivityBizService.getOfficialActivityDetail(activityId, 0);


        fill(activityDetail, dto);

        //补充队伍id
        if (getLoginUser() != null) {
            ActivityTeam team = activityUserBizService.getUserCurrentTeam(activityId, getLoginUser().getId());
            if (team != null) {
                activityDetail.setCurrentTeamId(team.getId());
            }

        }
        stopWatch.stop();
        log.info("queryActivityDetail 统计{}", stopWatch.getTotalTimeMillis());

        return activityDetail;
    }

    @Override
    public List<TeamRank> canCreateTeamList(Long activityId) {
        ActivityTeamQuery build = ActivityTeamQuery.builder()
                .activityId(activityId).isOfficial(0).currentNum(0).build();
        List<ActivityTeam> teamList = activityTeamService.findList(build);

        return teamList.stream().map(k -> {
            TeamRank teamRank = new TeamRank();
            teamRank.setTeamId(k.getId());
            teamRank.setTeamName(k.getTeamName());
            teamRank.setTeamLogo(k.getTeamLogo());
            return teamRank;
        }).collect(Collectors.toList());

    }

    @Override
    public void teamCall(TeamActivityCallRequest request, ZnsUserEntity loginUser) {


        ZnsRunActivityUserEntity activityUser = activityUserService.findActivityUser(request.getActivityId(), loginUser.getId());
        List<TeamCallRecord> todayCallRecord = teamCallRecordService.getTodayCallRecord(request.getTeamId(), null, null);
        List<TeamCallRecord> myTodayCallRecord = todayCallRecord.stream().filter(k -> Objects.equals(loginUser.getId(), k.getCallUserId())).collect(Collectors.toList());

        int count = 5;
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.TEAM_ACTIVITY_CALL_COUNT.getCode());
        if (sysConfig != null) {
            count = Integer.parseInt(sysConfig.getConfigValue());
        }
        if (activityUser.getTeamRole() == 0) {
            if (count - myTodayCallRecord.size() <= 0) {
                throw new BaseException(I18nMsgUtils.getMessage("activity.team.call.arrive.limit"));
            }
        }


        ZonedDateTime now = ZonedDateTime.now();
        Integer oneClickCall = request.getOneClickCall();
        List<Long> callUsers = new ArrayList<>();
        if (oneClickCall != null && oneClickCall == 1) {

            List<Long> activityUsers = activityUserBizService.getActUsersByTeamId(request.getTeamId())
                    .stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
            //今日已召唤
            activityUsers.removeAll(todayCallRecord.stream().map(TeamCallRecord::getSendUserId).collect(Collectors.toList()));
            callUsers = activityUsers.stream().filter(k -> {
                //排除有运动记录
                ZnsUserRunDataDetailsEntity runDataDetail = runDataDetailsService.getLatestUserActivityRunDataDetails(k, request.getActivityId());
                return runDataDetail == null || runDataDetail.getModifieTime().isBefore(DateUtil.startOfDate(now));
            }).collect(Collectors.toList());

        } else {
            callUsers.add(request.getUserId());
        }
        callUsers.remove(loginUser.getId());
        //召唤
        callUser(loginUser, callUsers, request.getTeamId(), request.getActivityId(), request.getOneClickCall());

    }

    private void callUser(ZnsUserEntity loginUser, List<Long> callUsers, Long teamId, Long activityId, Integer oneClickCall) {
        log.info("Calling user,loginUser:{},callUsers:{},teamId:{},activityId:{},oneClickCall:{}",
                loginUser, callUsers, teamId, activityId, oneClickCall);
        if (CollectionUtils.isEmpty(callUsers)) {
            log.info("callUsers is empty");
            return;
        }

        ActivityTeam team = activityTeamService.findById(teamId);
        String batchNo = OrderUtil.getBatchNo();
        String imContent = I18nMsgUtils.getMessage("activity.team.call.im.content", loginUser.getFirstName());
        String pushTitle = I18nMsgUtils.getMessage("activity.team.call.push.title", team.getTeamName());
        String pushContent = I18nMsgUtils.getMessage("activity.team.call.push.content", loginUser.getFirstName());

        List<TeamCallRecord> recordList = callUsers.stream().map(k -> {
            TeamCallRecord record = new TeamCallRecord();
            record.setCallUserId(loginUser.getId());
            record.setCallTeamId(teamId);
            record.setSendUserId(k);
            record.setCallTime(ZonedDateTime.now());
            record.setBatchNum(batchNo);
            record.setCallContent("imContent:" + imContent + " pushContent:" + pushContent);
            record.setOneClickCall(oneClickCall);
            return record;
        }).collect(Collectors.toList());

        //保存call记录
        teamCallRecordService.save(recordList);

        //站内信
        ImMessageBo imMessageBo = new ImMessageBo();
        imMessageBo.setJumpType("6");
        imMessageBo.setJumpValue("lznative://lzrace/EventDetails");
        Map<String, Object> params = new HashMap<>();
        params.put("activityId", activityId);
        imMessageBo.setParams(params);
        imMessageBo.setImageUrl(activityDisseminateBizService.findByActivityIdAndLanguage(activityId, loginUser.getLanguageCode()).getDisseminatePics());
        imMessageBo.setMsg(imContent);

        appMessageService.sendIm(loginUser.getId().toString(), callUsers, JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
//        //push
//        MessageBo messageBo = new MessageBo();
//        messageBo.setTitle(pushTitle);
//        messageBo.setContent(pushContent);
//        messageBo.setRouteType(6);
//        messageBo.setRouteValue(activityId.toString());
//        appMessageService.push(callUsers, messageBo,batchNo,0);

    }

    @Override
    public String queryTeamManagerName(Long teamId, Long userId) {

        List<ZnsRunActivityUserEntity> activityUserEntities = activityUserBizService.getActUsersByTeamId(teamId);
        List<ZnsRunActivityUserEntity> teamRole = activityUserEntities.stream().filter(k -> k.getTeamRole() == 1).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(teamRole)) {
            if (!userId.equals(teamRole.get(0).getUserId())) {
                ZnsUserEntity user = userService.findById(teamRole.get(0).getUserId());

                if (user != null) {
                    return user.getFirstName();
                }
            }
        }
        return null;
    }

    @Override
    public List<TeamInfoRes> queryTeamInfoByActivityId(Long activityId, Long userId) {
        List<TeamInfoRes> resList = new ArrayList<>();

        List<Integer> targets = subActivityService.getSingleActivityTargets(activityId);
        Integer target;
        if (!CollectionUtils.isEmpty(targets)) {
            target = targets.get(0);
        } else {
            target = 0;
        }
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (mainActivity == null) {
            return resList;
        }
        Integer targetType = mainActivity.getTargetType();

        String activityEndTime = mainActivity.getActivityEndTime();

        Integer timeStyle = mainActivity.getTimeStyle();
        Long activityEnd = DateUtil.getStampByZone(activityEndTime, Objects.equals(timeStyle, 0) ? "UTC" : "UTC-12");

        List<ZnsRunActivityUserEntity> allActivityUsers = activityUserService.findAllActivityUser(activityId);

        Map<Long, List<ZnsRunActivityUserEntity>> map = allActivityUsers.stream().collect(Collectors.groupingBy(ZnsRunActivityUserEntity::getTeamId));

        Boolean marathon = sysConfigService.isMarathon(activityId);
        map.forEach((k, v) -> {
            TeamInfoRes teamInfoRes = new TeamInfoRes();
            ActivityTeam team = activityTeamService.findById(k);
            teamInfoRes.setTeamName(team.getTeamName());
            teamInfoRes.setTeamId(team.getId());
            teamInfoRes.setTeamLogo(team.getTeamLogo());
            teamInfoRes.setTeamManagerId(team.getTeamManagerId());
            teamInfoRes.setMillage(team.getMillage());
            teamInfoRes.setRunTime(team.getRunTime());
            if (targetType == 1) {
                teamInfoRes.setTargetMillage(target);
            }
            if (targetType == 2) {
                teamInfoRes.setTargetRunTime(target);
            }
            teamInfoRes.setActivityEndTime(activityEnd);
            if (!CollectionUtils.isEmpty(v)) {
                teamInfoRes.setUserIds(v.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList()));
                if (teamInfoRes.getUserIds().contains(userId)) {
                    teamInfoRes.setIsSelfTeam(1);
                }
            }
            teamInfoRes.setIsMarathonTeam(marathon ? 1 : 0);
            resList.add(teamInfoRes);

        });
        return resList;


    }

    private void fill(TeamActivityDetail activityDetail, ActivityOfficialDto dto) {


        //state

        if (dto.getActivityState() == 0) {
            activityDetail.setActivityStatus(1);
        } else if (dto.getActivityState() == 1) {
            activityDetail.setActivityStatus(2);

        } else if (dto.getActivityState() == 2) {
            activityDetail.setActivityStatus(4);

        } else if (dto.getActivityState() == -1) {
            activityDetail.setActivityStatus(-1);

        }

        //activityDetail.setActivityStatus(dto.getActivityState());
        activityDetail.setActivityName(dto.getActivityTitle());
        activityDetail.setPic(dto.getAdvertisingImage());
        activityDetail.setDetailPic(dto.getDetailsImage());
        activityDetail.setActivityBegin(dto.getActivityStartTime().toInstant().toEpochMilli());
        activityDetail.setActivityEnd(dto.getActivityEndTime().toInstant().toEpochMilli());
        if (Objects.nonNull(dto.getApplicationStartTime())) {
            activityDetail.setSignBegin(dto.getApplicationStartTime().toInstant().toEpochMilli());
        }
        if (Objects.nonNull(dto.getApplicationEndTime())) {
            activityDetail.setSignEnd(dto.getApplicationEndTime().toInstant().toEpochMilli());
        }
        activityDetail.setRunWay(dto.getRouteTitle());
        activityDetail.setRunWayId(dto.getActivityRouteId());

        //活动对象，-1：不限，0：新用户 1:勋章用户 2：等级用户 3:特定用户
        String activityTarget = "Unlimited";
        if (dto.getActivityObjectType() == 0) {
            activityTarget = "New User";
        } else if (dto.getActivityObjectType() == 1) {
            MedalConfig medalConfig = medalConfigService.getById(dto.getDemandMedalConfigId());
            if (Objects.nonNull(medalConfig)) {
                activityTarget = medalConfig.getName() + "Medal Winner";
            }
        } else if (dto.getActivityObjectType() == 2) {
            activityTarget = "require level " + dto.getDemandUserLevel();
        } else if (dto.getMaxRunScore() != -1) {
            activityTarget = "Require points greater than " + dto.getMaxRunScore();
        } else if (dto.getMinRunScore() != -1) {
            activityTarget = "Require points less than" + dto.getMinRunScore();
        }
        activityDetail.setActivityTarget(activityTarget);
        // //活动设备
        List<ActivityEquipmentConfig> equipments = dto.getActivityEquipmentConfigs();

        for (ActivityEquipmentConfig activityEquipmentConfigDto : equipments) {
            if (Objects.equals(activityEquipmentConfigDto.getType(), 2)) {
                if (Objects.equals(activityEquipmentConfigDto.getEquipmentType(), 3)) {
                    if (Objects.equals(activityEquipmentConfigDto.getSubType(), 1)) {
                        activityEquipmentConfigDto.setEquipmentInfo(activityEquipmentConfigDto.getEquipmentInfo() + " (Walking Mode)");
                    } else if (Objects.equals(activityEquipmentConfigDto.getSubType(), 2)) {
                        activityEquipmentConfigDto.setEquipmentInfo(activityEquipmentConfigDto.getEquipmentInfo() + " (Running Mode)");
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(equipments)) {
            List<String> equipmentInfos = equipments.stream().map(ActivityEquipmentConfig::getEquipmentInfo).collect(Collectors.toList());
            activityDetail.setActivityEquipment(equipmentInfos);
        }

        //队伍奖励
        List<AwardRelation> rewards = awardProcessService.getRewards(AwardSourceEnum.ACTIVITY, dto.getId());
//        List<RewardConfig> rewardConfigs = rewards.stream().map(k -> {
//                    RewardConfig reward = new RewardConfig();
//                    reward.setRank(k.getRank());
//                    reward.setHeadReward(k.getHeadReward());
//                    reward.setBaseReward(k.getBaseReward());
//                    if (!CollectionUtils.isEmpty(k.getCouponIds())) {
//                        reward.setCouponId(k.getCouponIds().get(0));
//                        reward.setCouponNum(k.getCouponIds().size());
//                    }
//                    return reward;
//                }
//        ).collect(Collectors.toList());
        activityDetail.setRewardConfig(dto.getTeamAndRewardConfig().getRewardConfig());
        activityDetail.setRaceRules(dto.getActivityRule());
        activityDetail.setActivityFee(dto.getActivityEntryFee());
        activityDetail.setActivityEntryScore(dto.getActivityEntryScore());
        activityDetail.setBonusRuleType(dto.getBonusRuleType());
    }

    private ZnsUserEntity getLoginUser() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String emailAddress = HeaderUtil.getEmail(request);
        log.info(" getLoginUser  email= " + emailAddress);
        if (!StringUtils.hasText(emailAddress)) {
            log.info("emailAddress " + emailAddress);
            return null;

        }
        return userService.findByEmail(emailAddress);
    }
}
