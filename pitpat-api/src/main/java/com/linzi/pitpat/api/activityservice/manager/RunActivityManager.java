package com.linzi.pitpat.api.activityservice.manager;


import com.linzi.pitpat.api.activityservice.dto.RankedActivityConfigDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityResultResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityResultUserResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.RoomActivityListRequestDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.biz.AwardActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.BattlePassMilestoneBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.PayActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityMode;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ResultDataStateEnum;
import com.linzi.pitpat.data.activityservice.dto.api.ActivityVoteInfoReq;
import com.linzi.pitpat.data.activityservice.manager.api.RunActivityUserManager;
import com.linzi.pitpat.data.activityservice.model.dto.AddMainActivityUserDto;
import com.linzi.pitpat.data.activityservice.model.dto.NewPkActivityConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.H5ActivityFreeUser;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MainRunActivityRelationDo;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.RoomActivityExtInfoDo;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.SubActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.RoomActivityExtInfoQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserRunDataDetailsQuery;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityPackH5Resp;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityTeamVoteInfoResp;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityTeamVoteResult;
import com.linzi.pitpat.data.activityservice.model.vo.RoomResultUserFriendDto;
import com.linzi.pitpat.data.activityservice.model.vo.TeamConfig;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityTypeRuleVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MaxAwardVo;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityIDGenerateService;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.H5ActivityFreeUserService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MainRunActivityRelationService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.RoomActivityExtInfoService;
import com.linzi.pitpat.data.activityservice.service.RoomParticipantsService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.MilestoneAwardListDto;
import com.linzi.pitpat.data.awardservice.model.dto.VisualizationAwardConfigDetailsDto;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityRoomRelationDo;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityTeam;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityRoomRelationQuery;
import com.linzi.pitpat.data.clubservice.service.ClubActivityRoomRelationService;
import com.linzi.pitpat.data.clubservice.service.ClubActivityTeamService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.GameInfoDto;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.request.OpenActivityPackReq;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserBenefitTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.UserBenefitConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserFeedbackDataDo;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelBenefitRel;
import com.linzi.pitpat.data.userservice.model.entity.UserVote;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserBenefitConfigQuery;
import com.linzi.pitpat.data.userservice.model.query.UserFeedbackDataQuery;
import com.linzi.pitpat.data.userservice.model.query.UserVoteQuery;
import com.linzi.pitpat.data.userservice.service.UserBenefitConfigService;
import com.linzi.pitpat.data.userservice.service.UserFeedbackDataService;
import com.linzi.pitpat.data.userservice.service.UserLevelBenefitRelService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserVoteService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.dto.request.NewPkActivityRequestDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 2023/11/29 21:04
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RunActivityManager {
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ActivityEntryFeeService activityEntryFeeService;
    private final ZnsUserAccountService userAccountService;
    private final ISysConfigService sysConfigService;
    private final MainActivityService mainActivityService;
    private final EntryGameplayService entryGameplayService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ActivityTeamService activityTeamService;
    private final UserVoteService userVoteService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final RoomIdBizService roomIdBizService;
    private final RedissonClient redissonClient;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final SubActivityService subActivityService;
    private final PayActivityBizService payActivityBizService;
    private final ActivityFeeService activityFeeService;
    private final H5ActivityFreeUserService h5ActivityFreeUserService;
    private final AwardActivityBizService awardActivityBizService;
    private final ActivityIDGenerateService activityIDGenerateService;
    private final RunActivityUserManager runActivityUserManager;
    private final ZnsUserService userService;
    private final ActivityDisseminateService activityDisseminateService;
    private final RoomParticipantsService roomParticipantsService;

    private final BattlePassMilestoneBizService battlePassMilestoneBizService;
    private final ActivityUserBizService activityUserBizService;
    private final UserFeedbackDataService userFeedbackDataService;
    private final ZnsUserFriendService znsUserFriendService;
    private final MainRunActivityRelationService mainRunActivityRelationService;
    private final ClubActivityTeamService clubActivityTeamService;
    private final PropRunRankedActivityUserService runRankedActivityUserService;
    private final RoomActivityExtInfoService roomActivityExtInfoService;
    private final ClubActivityRoomRelationService clubActivityRoomRelationService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final MainActivityBizService mainActivityBizService;

    /**
     * 获取活动类型规则
     *
     * @param activityId
     * @param appVersion
     * @param userId
     * @return
     */
    public ActivityTypeRuleVo getActivityTypeRule(Long activityId, Integer appVersion, Long userId) {
        return mainActivityBizService.getActivityTypeRule(activityId);
    }

    public ActivityTeamVoteInfoResp getActivityTeamInfo(ActivityVoteInfoReq req) {
        ActivityTeamVoteInfoResp resp = new ActivityTeamVoteInfoResp();
        String key = String.format(RedisKeyConstant.ACTIVITY_TEAM_INFO, req.getActivityId());
        RBucket<Object> bucket = redissonClient.getBucket(key);
        if (Objects.nonNull(bucket.get())) {
            return JsonUtil.readValue(bucket.get().toString(), ActivityTeamVoteInfoResp.class);
        }
        MainActivity mainActivity = mainActivityService.findById(req.getActivityId());
        if (Objects.isNull(mainActivity)) {
            return resp;
        }
        String languageCode = LocaleContextHolder.getLocale().toString();
        ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
        resp.setDetailTopPromoPic(activityDisseminate.getDetailTopPromoPic());
        ZonedDateTime voteEndTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
        resp.setVoteStartTime(DateUtil.addDays1(voteEndTime, req.getVoteDayOffset()));
        resp.setVoteEndTime(voteEndTime);
        List<ActivityTeam> activityTeamList = activityTeamService.getTeamsByActivityId(req.getActivityId());
        if (CollectionUtils.isEmpty(activityTeamList)) {
            return resp;
        }

        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime activityEndTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        Map<Long, Integer> teamVote;
        if (now.isAfter(voteEndTime)) {
            // 投票结果
            teamVote = userVoteService.getTeamVoteResult(req.getActivityId()).stream()
                    .collect(Collectors.toMap(ActivityTeamVoteResult::getTeamId, ActivityTeamVoteResult::getVoteNum));
        } else {
            teamVote = new HashMap<>();
        }
        List<TeamConfig> collect = activityTeamList.stream().map(e -> {
            TeamConfig teamConfig = new TeamConfig();
            teamConfig.setTeamId(e.getId());
            teamConfig.setTeamLogo(e.getTeamLogo());
            teamConfig.setTeamName(e.getTeamName());
            teamConfig.setVoteNum(teamVote.get(e.getId()));
            if (Objects.isNull(teamConfig.getVoteNum()) && now.isAfter(voteEndTime)) {
                teamConfig.setVoteNum(0);
            }
            if (now.isAfter(activityEndTime)) {
                teamConfig.setRank(e.getRank());
            }
            return teamConfig;
        }).collect(Collectors.toList());
        resp.setTeamList(collect);
        fitCache(resp, bucket, voteEndTime, now, activityEndTime);
        return resp;
    }

    /**
     * 设置缓存
     *
     * @param resp
     * @param bucket
     * @param voteEndTime
     * @param now
     * @param activityEndTime
     */
    private static void fitCache(ActivityTeamVoteInfoResp resp, RBucket<Object> bucket, ZonedDateTime voteEndTime, ZonedDateTime now, ZonedDateTime activityEndTime) {
        if (now.isBefore(voteEndTime)) {
            long expire = (voteEndTime.toInstant().toEpochMilli() - now.toInstant().toEpochMilli()) / 1000;
            bucket.set(JsonUtil.writeString(resp), expire, TimeUnit.SECONDS);
        }
        if (now.isAfter(voteEndTime) && now.isBefore(activityEndTime)) {
            long expire = (now.toInstant().toEpochMilli() - voteEndTime.toInstant().toEpochMilli()) / 1000;
            bucket.set(JsonUtil.writeString(resp), expire, TimeUnit.SECONDS);
        }
        if (now.isAfter(activityEndTime)) {
            bucket.set(JsonUtil.writeString(resp), 2L, TimeUnit.DAYS);
        }
    }


    public ActivityTeamVoteInfoResp getActivityTeamVoteInfo(ActivityVoteInfoReq req, Long userId) {
        ActivityTeamVoteInfoResp resp = getActivityTeamInfo(req);
        UserVoteQuery query = UserVoteQuery.builder().userId(userId).activityId(req.getActivityId()).build();
        UserVote oldUserVote = userVoteService.getByQuery(query);
        resp.setUserVoteStatus(YesNoStatus.NO.getCode());
        if (Objects.nonNull(oldUserVote)) {
            resp.setUserVoteStatus(YesNoStatus.YES.getCode());
        }
        List<TeamConfig> teamList = resp.getTeamList();
        // 投票结果
        ZonedDateTime now = ZonedDateTime.now();
        if (now.isBefore(resp.getVoteEndTime())) {
            Map<Long, Integer> teamVote;
            if (YesNoStatus.YES.getCode().equals(resp.getUserVoteStatus())) {
                teamVote = userVoteService.getTeamVoteResult(req.getActivityId()).stream()
                        .collect(Collectors.toMap(ActivityTeamVoteResult::getTeamId, ActivityTeamVoteResult::getVoteNum));
            } else {
                teamVote = new HashMap<>();
            }
            for (TeamConfig teamConfig : teamList) {
                teamConfig.setVoteNum(teamVote.get(teamConfig.getTeamId()));
                if (Objects.isNull(teamConfig.getVoteNum())) {
                    teamConfig.setVoteNum(0);
                }
            }
        }
        resp.setTeamList(teamList);
        return resp;
    }

    public List<ActivityPackH5Resp> findActivityPackList(ZnsUserEntity user, String zoneId) {
        List<ActivityPackH5Resp> respList = new ArrayList<>();
        try {
            Currency userCurrency = userAccountService.getUserCurrency(user.getId());
            String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.H5_ACTIVITY_PACK_IDS.getCode());

            List<Long> activityIds = JsonUtil.readList(config, Long.class);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ZonedDateTime judgeDate = sdf.parse("2024-05-27 08:00:00");
            //配置中的系列赛活动ID&&也可能是单赛事
            for (Long activityId : activityIds) {
                ActivityPackH5Resp activityPackH5Resp = activityToH5Resp(user, zoneId, userCurrency, judgeDate, activityId, false);
                if (Objects.nonNull(activityPackH5Resp)) {
                    respList.add(activityPackH5Resp);
                }
                respList.sort(Comparator.comparing(ActivityPackH5Resp::getActivityStartTime));
            }
            respList.add(BattlePassCoverToH5Resp(user, zoneId, userCurrency, judgeDate, false));
        } catch (Exception e) {
            log.info("H5活动列表设置异常，e", e);
        }
        return respList;
    }

    public List<ActivityPackH5Resp> findActivityPackListV2(ZnsUserEntity user, String zoneId) {
        List<ActivityPackH5Resp> respList = new ArrayList<>();
        try {
            Currency userCurrency = userAccountService.getUserCurrency(user.getId());
            String july = sysConfigService.selectConfigByKey(ConfigKeyEnums.H5_ACTIVITY_PACK_IDS_JULY.getCode());

            List<Long> julyActivityIds = JsonUtil.readList(july, Long.class);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ZonedDateTime judgeDate = sdf.parse("2024-05-27 08:00:00");
            for (Long julyActivityId : julyActivityIds) {
                ActivityPackH5Resp activityPackH5Resp = activityToH5Resp(user, zoneId, userCurrency, judgeDate, julyActivityId, true);
                if (Objects.nonNull(activityPackH5Resp)) {
                    respList.add(activityPackH5Resp);
                }
                respList.sort(Comparator.comparing(ActivityPackH5Resp::getActivityStartTime));
            }
            respList.add(BattlePassCoverToH5Resp(user, zoneId, userCurrency, judgeDate, true));
        } catch (Exception e) {
            log.info("H5活动列表设置异常，e", e);
        }
        return respList;
    }

    public Boolean popH5ActivityPack(ZnsUserEntity loginUser) {
        try {
            String key = RedisConstants.ACTIVITY_PACK_OPEN_POP + loginUser.getId();
            String start = sysConfigService.selectConfigByKey(ConfigKeyEnums.H5_ACTIVITY_PACK_POP_STARTTIME.getCode());
            String end = sysConfigService.selectConfigByKey(ConfigKeyEnums.H5_ACTIVITY_PACK_POP_ENDTIME.getCode());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ZonedDateTime startTime = sdf.parse(start);
            ZonedDateTime endTime = sdf.parse(end);
            ZonedDateTime date = ZonedDateTime.now();
            boolean exists = redissonClient.getBucket(key).isExists();
            if (exists || !Objects.equals(loginUser.getCountryCode(), I18nConstant.CountryCodeEnum.US.getCode()) || date.isBefore(startTime) || date.isAfter(endTime)) {
                return false;
            }
            redissonClient.getBucket(key).set(1, 90, TimeUnit.DAYS);
        } catch (Exception e) {
            log.info("H5活动包弹窗失败,e", e);
            return false;
        }
        return true;
    }

    public Boolean popH5ActivityPackV2(ZnsUserEntity loginUser) {
        try {
            String key = RedisConstants.ACTIVITY_PACK_OPEN_POP_JULY + loginUser.getId();
            String start = sysConfigService.selectConfigByKey(ConfigKeyEnums.H5_ACTIVITY_PACK_POP_STARTTIME_JULY.getCode());
            String end = sysConfigService.selectConfigByKey(ConfigKeyEnums.H5_ACTIVITY_PACK_POP_ENDTIME_JULY.getCode());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ZonedDateTime startTime = sdf.parse(start);
            ZonedDateTime endTime = sdf.parse(end);
            ZonedDateTime date = ZonedDateTime.now();
            boolean exists = redissonClient.getBucket(key).isExists();
            if (exists || !Objects.equals(loginUser.getCountryCode(), I18nConstant.CountryCodeEnum.US.getCode()) || date.isBefore(startTime) || date.isAfter(endTime)) {
                return false;
            }
            redissonClient.getBucket(key).set(1, 31, TimeUnit.DAYS);
        } catch (Exception e) {
            log.info("H5活动包弹窗失败,e", e);
            return false;
        }
        return true;
    }

    //通过战令里程碑活动填充H5活动包列表
    private ActivityPackH5Resp BattlePassCoverToH5Resp(ZnsUserEntity user, String zoneId, Currency userCurrency, ZonedDateTime judgeDate, boolean isJuly) {
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), timeZone);
        ActivityPackH5Resp activityPackH5Resp = new ActivityPackH5Resp();
        //查询当前时间内的新里程碑
        ZnsRunActivityEntity battlePassActivityNow = runActivityService.findBattlePassActivityNow(now);
        if (Objects.isNull(battlePassActivityNow)) {
            return activityPackH5Resp;
        }
        List<ActivityEntryFee> feeList = activityEntryFeeService.findByActivityId(battlePassActivityNow.getId());
        if (!CollectionUtils.isEmpty(feeList)) {
            ActivityEntryFee activityEntryFee = feeList.stream().filter(e -> e.getCurrencyCode().equals(userCurrency.getCurrencyCode())).findFirst().orElse(null);
            activityPackH5Resp.setActivityEntryFee(Objects.isNull(activityEntryFee) ? new BigDecimal(0) : activityEntryFee.getEntryFee());
        }
        activityPackH5Resp.setActivityId(battlePassActivityNow.getId());
        activityPackH5Resp.setActivityStartTime(DateUtil.convertUtcToOtherDate(battlePassActivityNow.getActivityStartTime(), zoneId));
        activityPackH5Resp.setActivityEndTime(DateUtil.convertUtcToOtherDate(battlePassActivityNow.getActivityEndTime(), zoneId));
        LocalDate localDate = DateUtil.addDays(battlePassActivityNow.getActivityStartTime(), 2).toInstant().atZone(ZoneId.of(zoneId)).toLocalDate();
        // 获取当前月份
        Month currentMonth = localDate.getMonth();
        // 获取月份的英文名称
        String monthName = currentMonth.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
        activityPackH5Resp.setActivityTitle(monthName + " Goal Run+");
        activityPackH5Resp.setMainType(battlePassActivityNow.getActivityType().toString());
        activityPackH5Resp.setActivityType(battlePassActivityNow.getActivityType());
        activityPackH5Resp.setActivityState(YesNoStatus.YES.getCode());
        activityPackH5Resp.setIsFree(user.getCreateTime().toInstant().toEpochMilli() >= judgeDate.toInstant().toEpochMilli() ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
        ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(battlePassActivityNow.getId(), user.getId());
        if (Objects.nonNull(activityUser) && Objects.equals(activityUser.getIsPay(), 1)) {
            activityPackH5Resp.setIsEnroll(YesNoStatus.YES.getCode());
        } else {
            activityPackH5Resp.setIsEnroll(YesNoStatus.NO.getCode());
        }
        List<MilestoneAwardListDto> awardConfigDetailsDtoList = battlePassMilestoneBizService.getAwardConfigDetailsDtoList(battlePassActivityNow.getId(), user.getId(), 0, 1);

        if (!CollectionUtils.isEmpty(awardConfigDetailsDtoList)) {
            // 计算所有普通和进阶奖励的金额总和
            BigDecimal totalAmount = awardConfigDetailsDtoList.stream()
                    .flatMap(milestone -> Stream.concat(
                            milestone.getOrdinaryAwardList().stream(),
                            milestone.getAdvancedAwardList().stream()
                    ))
                    .map(VisualizationAwardConfigDetailsDto::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            activityPackH5Resp.setAwardAmount(totalAmount);
        }
        activityPackH5Resp.setCurrency(userCurrency);
        String key = RedisConstants.ACTIVITY_PACK_OPEN_TURE + user.getId();
        if (isJuly) {
            key = RedisConstants.ACTIVITY_PACK_OPEN_TRUE_NEW + user.getId();
        }
        activityPackH5Resp.setIsOpen(redissonClient.getBucket(key).isExists() ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());

        return activityPackH5Resp;
    }

    //通过新赛事活动填充H5活动包列表
    private ActivityPackH5Resp activityToH5Resp(ZnsUserEntity user, String zoneId, Currency userCurrency, ZonedDateTime judgeDate, Long activityId, boolean isJuly) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType())
                || Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType())) {
            return null;
        }
        if (Objects.nonNull(mainActivity)) {
            ActivityPackH5Resp activityPackH5Resp = new ActivityPackH5Resp();
            ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, user.getLanguageCode());
            ActivityFee feeEntry = activityFeeService.findFeeEntry(activityId, userCurrency.getCurrencyCode());
            activityPackH5Resp.setActivityId(activityId);
            ZonedDateTime startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
            ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
            //设置活动时间
            if (mainActivity.getTimeStyle() == 0) {
                activityPackH5Resp.setActivityStartTime(startTime);
                activityPackH5Resp.setActivityEndTime(endTime);
            } else {
                activityPackH5Resp.setActivityStartTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), zoneId)));
                activityPackH5Resp.setActivityEndTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), zoneId)));
                //各时区判断
                activityPackH5Resp.setActivityState(mainActivityService.getActivityState(mainActivity.getId(), zoneId));
            }
            activityPackH5Resp.setMainType(mainActivity.getMainType());
            if (Objects.equals(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getEnName(), mainActivity.getMainType())) {
                activityPackH5Resp.setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType());
            }
            if (Objects.equals(RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getEnName(), mainActivity.getMainType())) {
                activityPackH5Resp.setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType());
            }
            ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(activityId, user.getId());
            activityPackH5Resp.setIsEnroll(Objects.isNull(activityUser) ? YesNoStatus.NO.getCode() : YesNoStatus.YES.getCode());
            activityPackH5Resp.setCurrency(userCurrency);
            H5ActivityFreeUser freeUser = h5ActivityFreeUserService.findByUserId(user.getId());
            activityPackH5Resp.setIsFree(user.getCreateTime().toInstant().toEpochMilli() >= judgeDate.toInstant().toEpochMilli() || Objects.nonNull(freeUser) ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            activityPackH5Resp.setActivityTitle(Objects.nonNull(activityDisseminate) ? activityDisseminate.getTitle() : null);
            activityPackH5Resp.setActivityCoverImage(Objects.nonNull(activityDisseminate) ? activityDisseminate.getDisseminatePics() : null);
            List<SubActivity> allSubByMainActivityId = findAllSubByMainActivityId(mainActivity.getId());
            activityPackH5Resp.setTargetType(mainActivity.getTargetType());
            activityPackH5Resp.setActivityState(mainActivity.getActivityState());
            if (!CollectionUtils.isEmpty(allSubByMainActivityId)) {
                MainActivity sub = mainActivityService.findById(allSubByMainActivityId.get(0).getMainActivityId());
                int sum = allSubByMainActivityId.stream().mapToInt(SubActivity::getTarget).sum();
                activityPackH5Resp.setTarget(sum > 0 ? sum : -1);
                activityPackH5Resp.setTargetType(sub.getTargetType());
            }
            activityPackH5Resp.setActivityEntryFee(Objects.nonNull(feeEntry) ? feeEntry.getAmount() : null);
            MaxAwardVo maxAward = awardActivityBizService.findMaxAward(mainActivity.getId(), userCurrency.getCurrencyCode(), activityPackH5Resp.getTarget(), mainActivity.getMainType(), user.getId());
            activityPackH5Resp.setAwardAmount(maxAward.getMaxReward());
            String key = RedisConstants.ACTIVITY_PACK_OPEN_TURE + user.getId();
            if (isJuly) {
                key = RedisConstants.ACTIVITY_PACK_OPEN_TRUE_NEW + user.getId();
            }
            activityPackH5Resp.setIsOpen(redissonClient.getBucket(key).isExists() ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
            return activityPackH5Resp;
        }
        return null;
    }

    /**
     * 开启H5活动包
     */
    public void OpenActivityPack(OpenActivityPackReq req, ZnsUserEntity loginUser, String zoneId) {
        String lockKey = RedisConstants.ACTIVITY_PACK_OPEN + loginUser.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean flag = false;
        try {
            flag = lock.tryLock(1L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
            }
            if (!Objects.equals(loginUser.getCountryCode(), I18nConstant.CountryCodeEnum.US.getCode())) {
                throw new BaseException("Sorry, you are currently unable to participate in this activity.");
            }
            List<ActivityPackH5Resp> respList = findActivityPackList(loginUser, zoneId);
            //未报名活动列表
            List<ActivityPackH5Resp> unEnrollList = respList.stream().filter(s -> Objects.equals(s.getIsEnroll(), 0)).toList();
            if (CollectionUtils.isEmpty(unEnrollList)) {
                return;
            }
            Integer isFree = unEnrollList.get(0).getIsFree();
            //注册时间在5月27日之后免费
            if (Objects.equals(isFree, YesNoStatus.NO.getCode())) {
                //支付金额
                Result result = payActivityBizService.payH5ActivityPack(req.getPassword(), loginUser.getId());
                if (Objects.nonNull(result)) {
                    throw new BaseException(result.getMsg());
                }
            }
            //报名操作
            enrollActivityPack(loginUser, unEnrollList);
            String key = RedisConstants.ACTIVITY_PACK_OPEN_TURE + loginUser.getId();
            redissonClient.getBucket(key).set(1, 31, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("购买6月H5活动包异常", e);
            throw new BaseException(e.getMessage());
        } finally {
            if (flag) {
                lock.unlock();
            }
        }
    }

    /**
     * 开启H5活动包
     */
    public void OpenActivityPackV2(OpenActivityPackReq req, ZnsUserEntity loginUser, String zoneId) {
        String lockKey = RedisConstants.ACTIVITY_PACK_OPEN_NEW + loginUser.getId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean flag = false;
        try {
            flag = lock.tryLock(1L, TimeUnit.SECONDS);
            if (!flag) {
                throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
            }
            if (!Objects.equals(loginUser.getCountryCode(), I18nConstant.CountryCodeEnum.US.getCode())) {
                throw new BaseException("Sorry, you are currently unable to participate in this activity.");
            }
            List<ActivityPackH5Resp> respList = findActivityPackListV2(loginUser, zoneId);
            //未报名活动列表
            List<ActivityPackH5Resp> unEnrollList = respList.stream().filter(s -> Objects.equals(s.getIsEnroll(), 0)).toList();
            if (CollectionUtils.isEmpty(unEnrollList)) {
                return;
            }
            Integer isFree = unEnrollList.get(0).getIsFree();
            //注册时间在5月27日之后免费
            if (Objects.equals(isFree, YesNoStatus.NO.getCode())) {
                //支付金额
                Result result = payActivityBizService.payH5ActivityPack(req.getPassword(), loginUser.getId());
                if (Objects.nonNull(result)) {
                    throw new BaseException(result.getMsg());
                }
            }
            //报名操作
            enrollActivityPack(loginUser, unEnrollList);
            String key = RedisConstants.ACTIVITY_PACK_OPEN_TRUE_NEW + loginUser.getId();
            redissonClient.getBucket(key).set(1, 31, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("购买7月H5活动包异常", e);
            throw new BaseException(e.getMessage());
        } finally {
            if (flag) {
                lock.unlock();
            }
        }
    }

    private void enrollActivityPack(ZnsUserEntity loginUser, List<ActivityPackH5Resp> unEnrollList) {
        unEnrollList.forEach(s -> {
            if (Objects.equals(s.getMainType(), "11")) {
                ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(s.getActivityId());
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(s.getActivityId(), loginUser.getId());
                if (Objects.nonNull(activityUser)) {
                    activityUser.setIsPay(1);
                    runActivityUserService.updateById(activityUser);
                } else {
                    activityUserBizService.addOfficialActivityUser(znsRunActivityEntity, loginUser.getId(), null, null, null, null, true);
                }
                //数据解锁
                battlePassMilestoneBizService.rewardUnlocking(s.getActivityId(), loginUser.getId(), 1);
            } else {
                runActivityUserService.addMainActivityUser(new AddMainActivityUserDto().setUser(loginUser)
                        .setActivityId(s.getActivityId())
                        .setActivityType(s.getActivityType())
                        .setTarget(s.getTarget())
                        .setTargetType(s.getTargetType()));
            }
        });
    }

    public List<SubActivity> findAllSubByMainActivityId(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SINGLE.getType()) || Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SERIES_SUB.getType())) {
            return subActivityService.getAllSingleActByMain(activityId);
        } else if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SERIES_MAIN.getType())) {
            List<Long> segmentId = seriesActivityRelService.findSubActivityId(activityId);
            List<SubActivity> subActivities = new ArrayList<>();
            for (Long id : segmentId) {
                List<SubActivity> list = subActivityService.getAllSingleActByMain(id);
                subActivities.addAll(list);
            }
            return subActivities;
        } else {
            return new ArrayList<>();
        }
    }

    public ZnsRunActivityEntity createNewPkActivity(NewPkActivityRequestDto request, Integer roomMode, Long roomId) {
        //创建活动
        ZnsRunActivityEntity activityEntity = createNewActivity(request);
        //俱乐部用户赛绑定活动关系
        if (Objects.nonNull(request.getClubId()) && Objects.nonNull(request.getMainActivityId())) {
            MainRunActivityRelationDo mainRunActivityRelationDo = new MainRunActivityRelationDo();
            mainRunActivityRelationDo.setRunActivityId(activityEntity.getId());
            mainRunActivityRelationDo.setMainActivityId(request.getMainActivityId());
            mainRunActivityRelationDo.setClubId(request.getClubId());
            mainRunActivityRelationService.create(mainRunActivityRelationDo);
        }
        //俱乐部房间关联
        if (Objects.nonNull(roomId) && Objects.nonNull(request.getClubId())) {
            ClubActivityRoomRelationDo clubActivityRoomRelationDo = clubActivityRoomRelationService.findByQuery(ClubActivityRoomRelationQuery.builder().clubId(request.getClubId()).roomId(roomId).build());
            if (Objects.nonNull(clubActivityRoomRelationDo)) {
                clubActivityRoomRelationDo.setSubActivityId(activityEntity.getId());
                clubActivityRoomRelationService.update(clubActivityRoomRelationDo);
            }
        }
        //俱乐部用户赛绑定队伍
        Long activityTeamId = clubActivityTeamService.findByActivityIdAndClubId(request.getMainActivityId(), request.getClubId()).map(ClubActivityTeam::getActivityTeamId).orElse(null);
        //创建游戏活动房间
        roomIdBizService.createGameRoom(activityEntity, roomMode);
        List<Long> activityUserIds = request.getActivityUserIds();
        //添加活动参与者
        runActivityUserManager.addRunActivityUsers(activityEntity, activityUserIds, request.getInitiatorUserId(), activityTeamId);
        return activityEntity;
    }

    private ZnsRunActivityEntity createNewActivity(NewPkActivityRequestDto request) {
        ZnsRunActivityEntity activityEntity = new ZnsRunActivityEntity();
        activityEntity.setActivityRouteId(request.getActivityRouteId());
        activityEntity.setActivityStartTime(ZonedDateTime.now());
        activityEntity.setCompleteRuleType(request.getCompleteRuleType());
        if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode().equals(request.getCompleteRuleType())) {
            activityEntity.setRunMileage(request.getRunMileage());
        } else if (ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_2.getCode().equals(request.getCompleteRuleType())) {
            activityEntity.setRunTime(request.getRunTime());
        }
        activityEntity.setBonusRuleType(request.getBonusRuleType());
        if (ActivityConstants.BonusRuleTypeEnum.BOND.getCode().equals(request.getBonusRuleType())) {
            activityEntity.setActivityEntryFee(request.getActivityEntryFee());
        } else if (ActivityConstants.BonusRuleTypeEnum.SCORE.getCode().equals(request.getBonusRuleType())) {
            activityEntity.setActivityEntryScore(new BigDecimal(request.getActivityEntryScore()));
        }
        activityEntity.setActivityState(0);
        activityEntity.setActivityType(request.getActivityType());
        activityEntity.setActivityTypeSub(request.getActivityTypeSub());
        activityEntity.setUserCount(request.getActivityUserIds().size());
        activityEntity.setStatus(1);
        //id获取
        Long activityID = activityIDGenerateService.generateActivityID();
        activityEntity.setId(activityID);
        activityEntity.setActivityNo("HD" + activityID);
        String firstName = userService.findById(request.getInitiatorUserId()).getFirstName();
        activityEntity.setActivityTitle("PK - " + request.getAppRoomId());

        //获取配置
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(request.getActivityType(), request.getActivityTypeSub());
        NewPkActivityConfigDto newPkActivityConfigDto = JsonUtil.readValue(activityConfig.getActivityConfig(), NewPkActivityConfigDto.class);
        newPkActivityConfigDto.setAppRoomId(request.getAppRoomId());
        activityEntity.setActivityConfig(JsonUtil.writeString(newPkActivityConfigDto));
        activityEntity.setActivityConfigId(activityConfig.getId());
        boolean save = runActivityService.insert(activityEntity) == 1;

        if (save) {
            //保存新表
            MainActivity mainActivity = new MainActivity().setRemark(activityEntity.getActivityTitle()).setOldActivityId(activityID)
                    .setId(activityID).setActivityNo(activityEntity.getActivityNo()).setMainType(MainActivityTypeEnum.OLD.getType())
                    .setOldType(activityEntity.getActivityType()).setTargetType(activityEntity.getCompleteRuleType());
            mainActivityService.insert(mainActivity);
        }

        //国际化保存
        activityDisseminateService.saveActivityI18n(activityID, I18nMsgUtils.getLangMessage(I18nConstant.LanguageCodeEnum.en_US.getCode(), "activity.new.pk.title", firstName, request.getAppRoomId() + ""), I18nConstant.LanguageCodeEnum.en_US.getCode());
        activityDisseminateService.saveActivityI18n(activityID, I18nMsgUtils.getLangMessage(I18nConstant.LanguageCodeEnum.fr_CA.getCode(), "activity.new.pk.title", firstName, request.getAppRoomId() + ""), I18nConstant.LanguageCodeEnum.fr_CA.getCode());
        return activityEntity;
    }

    /**
     * 获取活动结果
     *
     * @param activityId
     * @param userId
     * @param isFriendPopNeed
     * @return
     */
    public ActivityResultResponseDto getActivityResult(Long activityId, Long userId, Integer isFriendPopNeed) {
        ActivityResultResponseDto result = new ActivityResultResponseDto();
        //查询活动
        ZnsRunActivityEntity activity = runActivityService.selectActivityById(activityId);
        result.setActivityState(activity.getActivityState());
        //查询活动用户
        List<ZnsRunActivityUserEntity> allActivityUser = runActivityUserService.findAllActivityUser(activityId);
        List<Long> userIds = allActivityUser.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        UserRunDataDetailsQuery query = new UserRunDataDetailsQuery().setUserIds(userIds).setActivityId(activityId);
        List<ZnsUserRunDataDetailsEntity> runDataDetailsEntityList = userRunDataDetailsService.findListByQuery(query);
        Map<Long, ZnsUserRunDataDetailsEntity> userRunMap = runDataDetailsEntityList.stream().collect(Collectors.toMap(ZnsUserRunDataDetailsEntity::getUserId, Function.identity(), (k1, k2) -> k1));
        List<Long> detailIdList = runDataDetailsEntityList.stream().map(ZnsUserRunDataDetailsEntity::getId).collect(Collectors.toList());
        Map<Long, UserFeedbackDataDo> feedbackDataDoMap = userFeedbackDataService.findList(new UserFeedbackDataQuery().setDetailIdList(detailIdList)).stream().collect(Collectors.toMap(UserFeedbackDataDo::getProblemDataId, Function.identity(), (k1, k2) -> k1));
        List<ActivityResultUserResponseDto> activityUserList = new ArrayList<>();

        List<ZnsUserEntity> userEntities = userService.findByIds(userIds);
        Map<Long, ZnsUserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity(), (k1, k2) -> k1));
        //活动已结束直接取排名
        for (ZnsRunActivityUserEntity znsRunActivityUserEntity : allActivityUser) {
            UserFeedbackDataDo userFeedbackDataDo = feedbackDataDoMap.get(znsRunActivityUserEntity.getRunDataDetailsId());
            ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = userRunMap.get(znsRunActivityUserEntity.getUserId());
            ZnsUserEntity user = userEntityMap.get(znsRunActivityUserEntity.getUserId());
            ActivityResultUserResponseDto userResponseDto = getActivityResultUser(activity, znsRunActivityUserEntity, znsUserRunDataDetailsEntity, user, userFeedbackDataDo);
            //设置自己的跑步明细id，只有跑步结束设置
            if (userId.equals(znsRunActivityUserEntity.getUserId()) && userResponseDto.getUserState() != 3
                    && Objects.nonNull(znsUserRunDataDetailsEntity) && znsUserRunDataDetailsEntity.getIsDelete() == 0) {
                result.setRunDataDetailsId(znsRunActivityUserEntity.getRunDataDetailsId());
                //是否超过15天
                Boolean serviceTimeOut = DateUtil.betweenDay(znsUserRunDataDetailsEntity.getCreateTime(), ZonedDateTime.now()) >= 15;
                result.setServiceTimeOut(serviceTimeOut);
            }
            activityUserList.add(userResponseDto);
        }

        //排序
        activityUserList.sort((o1, o2) -> {
            return compare(o1, o2, activity);
        });

        result.setActivityUserList(activityUserList);
        userFriendLikePopQuery(userId, result, activityUserList, activityId);
        // 不需要弹窗 前端参数控制
        if (YesNoStatus.NO.getCode().equals(isFriendPopNeed) || Objects.isNull(isFriendPopNeed)) {
            result.setIsPopFollowed(YesNoStatus.NO.getCode());
            result.setPopFollowedUserId(null);
            result.setPopFollowedUserIcon("");
            result.setPopFollowedUserNickname("");
        }
        //查询是否有风控检测中
        Integer riskReview = userRunDataDetailsCheatService.isRiskReview(Collections.singletonList(activity.getId()));
        result.setIsRiskReview(riskReview);
        return result;
    }

    private int compare(ActivityResultUserResponseDto o1, ActivityResultUserResponseDto o2, ZnsRunActivityEntity activity) {
        // 1. 首先按照排名排序，有排名的优先，null排在后面
        int rankCompare = compareRank(o1.getRank(), o2.getRank());
        if (rankCompare != 0) {
            return rankCompare;
        }

        // 2. 如果排名相同（包括都为null,-1），按照里程降序排序
        if (o1.getResultDataState() == 1 && o2.getResultDataState() == 1) {
            if (activity.getCompleteRuleType().equals(ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode())) {
                return o1.getRunTimeMillisecond().compareTo(o2.getRunTimeMillisecond());
            } else {
                return o2.getRunMileage().compareTo(o1.getRunMileage());
            }
        }

        // 3. 如果里程也相同，按照完赛状态排序
        return compareStatus(o1.getResultDataState(), o2.getResultDataState());
    }

    private int compareStatus(Integer state1, Integer state2) {
        return ResultDataStateEnum.findRankSort(state1) - ResultDataStateEnum.findRankSort(state2);
    }

    private int compareRank(Integer rank1, Integer rank2) {
        // 处理null值情况
        if (rank1 == null && rank2 == null) {
            return 0;
        }
        if (rank1 == -1 && rank2 == -1) {
            return 0;
        }
        if (rank1 == null || rank1 == -1) {
            return 1; // null排在后面
        }
        if (rank2 == null || rank2 == -1) {
            return -1;
        }
        return rank1.compareTo(rank2);
    }

    private ActivityResultUserResponseDto getActivityResultUser(ZnsRunActivityEntity activity, ZnsRunActivityUserEntity znsRunActivityUserEntity, ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity, ZnsUserEntity user, UserFeedbackDataDo userFeedbackDataDo) {
        ActivityResultUserResponseDto userResponseDto = new ActivityResultUserResponseDto();
        userResponseDto.setUserId(znsRunActivityUserEntity.getUserId());
        userResponseDto.setNickname(znsRunActivityUserEntity.getNickname());
        if (user != null) {
            userResponseDto.setHeadPortrait(user.getHeadPortrait());
        }
        userResponseDto.setRank(znsRunActivityUserEntity.getRank());
        userResponseDto.setRunMileage(znsRunActivityUserEntity.getRunMileage().intValue());
        userResponseDto.setRunTimeMillisecond(znsRunActivityUserEntity.getRunTimeMillisecond());
        RoomActivityExtInfoDo roomActivityExtInfoDo = roomActivityExtInfoService.findByQuery(new RoomActivityExtInfoQuery().setActivityId(activity.getId()));
        //模式普通模式.历史数据没有模式信息存储
        Integer activityMode = ActivityMode.NORMAL_MODE.getValue();
        if (Objects.nonNull(roomActivityExtInfoDo)) {
            //有模式数据
            activityMode = roomActivityExtInfoDo.getActivityMode();
        }
        // 道具模式假成绩覆盖展示
        if (ActivityMode.PROP_MODE.getValue().equals(activityMode)) {
            PropRunRankedActivityUser propActivityUser = runRankedActivityUserService
                    .findByActivityIdAndUserId(znsRunActivityUserEntity.getActivityId(), znsRunActivityUserEntity.getUserId());
            if (Objects.nonNull(propActivityUser)) {
                userResponseDto.setRunMileage(propActivityUser.getRunMileage().intValue());
                userResponseDto.setRunTimeMillisecond(propActivityUser.getRunTimeMils());
            }
        }
        if (ActivityConstants.BonusRuleTypeEnum.BOND.getCode().equals(activity.getBonusRuleType())) {
            userResponseDto.setAmountAward(znsRunActivityUserEntity.getRunAward());
        } else if (ActivityConstants.BonusRuleTypeEnum.SCORE.getCode().equals(activity.getBonusRuleType())) {
            userResponseDto.setScoreAward(znsRunActivityUserEntity.getRunAward().intValue());
        }

        //状态设置
        if (znsRunActivityUserEntity.getIsComplete() == 1) {
            userResponseDto.setUserState(1);
        } else {
            //是否跑步中
            if (Objects.isNull(znsUserRunDataDetailsEntity) || znsUserRunDataDetailsEntity.getRunStatus() == 1) {
                userResponseDto.setUserState(2);
            } else {
                userResponseDto.setUserState(3);
            }
        }
        //新状态设置
        if (Objects.nonNull(userFeedbackDataDo)) {
            userResponseDto.setAppealState(userFeedbackDataDo.getStatus());
        }
        Integer resultDataState = ResultDataStateEnum.stateConvert(znsRunActivityUserEntity.getIsComplete(), Objects.nonNull(znsUserRunDataDetailsEntity) ? znsUserRunDataDetailsEntity.getIsCheat() : znsRunActivityUserEntity.getIsCheat(),
                Objects.nonNull(znsUserRunDataDetailsEntity) ? znsUserRunDataDetailsEntity.getRunStatus() : -1, userResponseDto.getAppealState());
        userResponseDto.setResultDataState(resultDataState);
        return userResponseDto;
    }

    private void userFriendLikePopQuery(Long userId, ActivityResultResponseDto result, List<ActivityResultUserResponseDto> activityUserList, Long activityId) {
        result.setIsPopFollowed(YesNoStatus.NO.getCode());
        List<RoomResultUserFriendDto> friendList = new ArrayList<>();
        activityUserList = activityUserList.stream()
                .map(wrapper -> {
                    if (!userId.equals(wrapper.getUserId())) {
                        wrapper.setIsFollowed(YesNoStatus.YES.getCode());
                        Integer relationType = znsUserFriendService.getRelationType(userId, wrapper.getUserId());
                        if (UserConstant.RelationTypeEnum.RELATIONTYPE_0.getCode().equals(relationType) ||
                                UserConstant.RelationTypeEnum.RELATIONTYPE_DEFAULT.getCode().equals(relationType)) {
                            wrapper.setIsFollowed(YesNoStatus.NO.getCode());
                            friendList.add(new RoomResultUserFriendDto()
                                    .setUserId(wrapper.getUserId()).setHeadPortrait(wrapper.getHeadPortrait()).setNickname(wrapper.getNickname())
                                    .setIsPopFollowedType(UserConstant.RelationTypeEnum.RELATIONTYPE_0.getCode().equals(relationType) ? relationType : 1));
                        }
                        // 点赞标志
                        wrapper.setIsAllowLike(YesNoStatus.NO.getCode());
                        String likeSetKey = activityId + ":" + userId + ":likes";
                        // 检查用户 0 否 1 已点赞
                        boolean hasLiked = hasLiked(likeSetKey, wrapper.getUserId());
                        if (!hasLiked) {
                            // 无点赞可以点赞
                            wrapper.setIsAllowLike(YesNoStatus.YES.getCode());
                        }
                    } else {
                        wrapper.setIsFollowed(YesNoStatus.NO.getCode());
                        // 点赞标志
                        wrapper.setIsAllowLike(YesNoStatus.NO.getCode());
                    }
                    return wrapper;
                }).toList();
        result.setActivityUserList(activityUserList);
        if (!CollectionUtils.isEmpty(friendList)) {
            result.setIsPopFollowed(YesNoStatus.YES.getCode());
            Optional<RoomResultUserFriendDto> any = friendList.stream().filter(f -> f.getIsPopFollowedType().equals(0)).findAny();
            if (any.isPresent()) {
                any.ifPresent(friend -> {
                    result.setIsPopFollowedType(0);
                    result.setPopFollowedUserId(friend.getUserId());
                    result.setPopFollowedUserIcon(friend.getHeadPortrait());
                    result.setPopFollowedUserNickname(friend.getNickname());
                });
            } else {
                Optional<RoomResultUserFriendDto> any2 = friendList.stream().filter(f -> f.getIsPopFollowedType().equals(1)).findAny();
                any2.ifPresent(friend -> {
                    result.setIsPopFollowedType(1);
                    result.setPopFollowedUserId(friend.getUserId());
                    result.setPopFollowedUserIcon(friend.getHeadPortrait());
                    result.setPopFollowedUserNickname(friend.getNickname());
                });
            }
        }
    }

    // 检查用户 A 是否已经给用户 B 点赞
    public boolean hasLiked(String likeSetKey, Long toUserId) {
        // 存储点赞信息的集合键名
        RSet<Long> likeSet = redissonClient.getSet(likeSetKey);
        // 检查点赞用户是否在集合中
        return likeSet.contains(toUserId);
    }

    public List<RoomActivityListRequestDto> getRoomActivityList(Long roomId) {
        List<RoomActivityListRequestDto> list = new ArrayList<>();
        //查询房间关联活动id
        List<Long> activityIdList = roomParticipantsService.findActivityIdList(roomId);
        if (CollectionUtils.isEmpty(activityIdList)) {
            return list;
        }
        //查询活动用户
        RunActivityUserQuery query = RunActivityUserQuery.builder().activityIds(activityIdList).rank(1).build();
        List<ZnsRunActivityUserEntity> activityUserEntityList = runActivityUserService.findActivityUserByQuery(query);
        Map<Long, ZnsRunActivityUserEntity> activityUserMap = activityUserEntityList.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getActivityId, Function.identity(), (k1, k2) -> k1));
        List<Long> userIds = activityUserEntityList.stream().map(ZnsRunActivityUserEntity::getUserId).collect(Collectors.toList());
        Map<Long, ZnsUserEntity> userEntityMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userIds)) {
            List<ZnsUserEntity> userEntityList = userService.findByIds(userIds);
            userEntityMap = userEntityList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity(), (k1, k2) -> k1));
        }

        List<ZnsRunActivityEntity> activityEntities = runActivityService.findByIds(activityIdList);
        for (ZnsRunActivityEntity activityEntity : activityEntities) {
            if (activityEntity.getActivityState() != 2) {
                continue;
            }
            RoomActivityListRequestDto roomActivityListRequestDto = new RoomActivityListRequestDto();
            roomActivityListRequestDto.setActivityId(activityEntity.getId());
            roomActivityListRequestDto.setStartTime(activityEntity.getActivityStartTime());
            roomActivityListRequestDto.setTargetRunMileage(activityEntity.getRunMileage().intValue());
            roomActivityListRequestDto.setTargetRunTime(activityEntity.getRunTime());
            roomActivityListRequestDto.setStartTime(activityEntity.getActivityStartTime());
            //获胜者
            ZnsRunActivityUserEntity winner = activityUserMap.get(activityEntity.getId());
            if (Objects.nonNull(winner)) {
                roomActivityListRequestDto.setWinner(winner.getNickname());
                roomActivityListRequestDto.setRunMileage(winner.getRunMileage().intValue());
                roomActivityListRequestDto.setRunTimeMillisecond(winner.getRunTimeMillisecond());
                ZnsUserEntity user = userEntityMap.get(winner.getUserId());
                roomActivityListRequestDto.setHeadPortrait(Optional.ofNullable(user).map(ZnsUserEntity::getHeadPortrait).orElse(null));
                Integer resultDataState = ResultDataStateEnum.stateConvert(winner.getIsComplete(), winner.getIsCheat(), 1, null);
                roomActivityListRequestDto.setResultDataState(resultDataState);
            }
            //查询是否有风控检测中
            Integer riskReview = userRunDataDetailsCheatService.isRiskReview(Collections.singletonList(activityEntity.getId()));
            roomActivityListRequestDto.setIsRiskReview(riskReview);
            list.add(roomActivityListRequestDto);
        }
        return list;
    }


    @Transactional
    public Long enrollingNewUserActivity(ZnsRunActivityConfigEntity runActivityConfig, ZnsUserEntity user, String equipmentNo) {
        if (null == runActivityConfig) {
            log.error("新增跑步活动:活动配置不存在");
            return null;
        }
        ZonedDateTime now = ZonedDateTime.now();
        // 1. 创建组队活动
        ZnsRunActivityEntity runActivityEntity = new ZnsRunActivityEntity();
        runActivityEntity.setActivityState(1);
        runActivityEntity.setActivityConfigId(runActivityConfig.getId());
        runActivityEntity.setActivityRouteId(0L);
        runActivityEntity.setActivityTitle(RunActivityTypeEnum.NEW_USER_ACTIVITY.getName());
        Map<String, Object> object = JsonUtil.readValue(runActivityConfig.getActivityConfig());
        Integer newUserExclusiveTime = MapUtil.getInteger(object.get("newUserExclusiveTime"));
        if (Objects.isNull(newUserExclusiveTime)) {
            newUserExclusiveTime = 7;
        }
        runActivityEntity.setActivityStartTime(now);
        runActivityEntity.setActivityEndTime(DateUtil.addDays(now, newUserExclusiveTime));
        runActivityEntity.setActivityConfig(runActivityConfig.getActivityConfig());
        runActivityEntity.setUserCount(1);
        runActivityEntity.setActivityType(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType());
        runActivityEntity.setRemark("活动报名");
        runActivityService.insert(runActivityEntity);

        //2. 创建活动用户
        ZnsRunActivityUserEntity runActivityUserEntity = new ZnsRunActivityUserEntity();
        runActivityUserEntity.setIsRobot(user.getIsRobot());
        runActivityUserEntity.setIsTest(user.getIsTest());
        runActivityUserEntity.setActivityId(runActivityEntity.getId());
        runActivityUserEntity.setUserId(user.getId());

        runActivityUserEntity.setNickname(user.getFirstName());
        runActivityUserEntity.setUserType(1);
        runActivityUserEntity.setUserState(4);
        runActivityUserEntity.setActivityType(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType());
        runActivityUserEntity.setEquipmentNo(equipmentNo);
        runActivityUserService.save(runActivityUserEntity);
        return runActivityEntity.getId();
    }

    public GameInfoDto createGameInfo(ZnsUserEntity loginUser, ActivityTypeDto activityNew) {
        GameInfoDto gameInfoDto = new GameInfoDto();
        fillActionInfo(loginUser, gameInfoDto);

        if ("old".equals(activityNew.getMainType())) {
            ZnsRunActivityEntity runActivityEntity = activityNew.getRunActivity();
            gameInfoDto.setPropSupport(runActivityEntity.getPropSupport());
            gameInfoDto.setRunwayStyle(runActivityEntity.getRunwayStyle());
            RunActivityUserTask runActivityUserTask = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdNextLevel(loginUser.getId(), runActivityEntity.getId());
            if (Objects.nonNull(runActivityUserTask)) {
                gameInfoDto.setSpecialEffectTheme(runActivityUserTask.getSpecialEffectTheme());
                gameInfoDto.setRunwayStyle(runActivityUserTask.getRunwayStyle());
            } else {
                gameInfoDto.setSpecialEffectTheme(runActivityEntity.getSpecialEffectTheme());
            }
            gameInfoDto.setThemeDesc(runActivityEntity.getThemeDesc());

            //非官方同跑+PK赛,语音开关查询
            List<Integer> runTypes = List.of(RunActivityTypeEnum.TEAM_RUN.getType(), RunActivityTypeEnum.CHALLENGE_RUN.getType());
            if (runTypes.contains(runActivityEntity.getActivityType())) {
                Integer subType = RunActivityTypeEnum.TEAM_RUN.getType().equals(runActivityEntity.getActivityType()) ? null : runActivityEntity.getActivityTypeSub();
                ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(runActivityEntity.getActivityType(), subType);
                Map<String, Integer> jsonObject = JsonUtil.readValue(activityConfig.getActivityConfig());
                Integer voiceSwitch = runActivityConfigService.getVoiceSwitch(jsonObject.get("voiceSwitch"), loginUser);
                gameInfoDto.setIsVoiceUp(voiceSwitch);
            }
        } else {
            MainActivity mainActivity = activityNew.getMainActivity();
            SubActivity subActivity = subActivityService.getSingleActByMain(mainActivity.getId());
            gameInfoDto.setPropSupport(subActivity.getAllowProp());
            if (StringUtils.hasText(subActivity.getRunwaySty())) {
                gameInfoDto.setRunwayStyle(Integer.valueOf(subActivity.getRunwaySty()));
            }
            gameInfoDto.setSpecialEffectTheme(subActivity.getSpecialEffectTheme());
            //填充入场引言
            ActivityDisseminate activityDisseminate = new ActivityDisseminate();
            if (Objects.equals(mainActivity.getMainType(), MainActivityTypeEnum.SERIES_SUB.getType())) {
                MainActivity main = seriesActivityRelService.getMainActivityBySegmentActId(mainActivity.getId());
                activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(main.getId(), loginUser.getLanguageCode());
            } else {
                activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), loginUser.getLanguageCode());
            }
            gameInfoDto.setThemeDesc(Objects.nonNull(activityDisseminate) ? activityDisseminate.getAdmissionSpeech() : null);
            gameInfoDto.setIsVoiceUp(mainActivity.getIsVoiceUp());

            MainActivityTypeEnum typeEnum = MainActivityTypeEnum.findByType(mainActivity.getMainType());

            //段位赛语音开关
            if (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType())) {
                ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType(), null);
                RankedActivityConfigDto dto = JsonUtil.readValue(runActivityConfig.getActivityConfig(), RankedActivityConfigDto.class);
                Integer voiceSwitch = runActivityConfigService.getVoiceSwitch(dto.getVoiceSwitch(), loginUser);
                gameInfoDto.setIsVoiceUp(voiceSwitch);
                //道具赛
            } else if (MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType())) {
                ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.PROP_ACTIVITY.getType(), null);
                RankedActivityConfigDto dto = JsonUtil.readValue(runActivityConfig.getActivityConfig(), RankedActivityConfigDto.class);
                Integer voiceSwitch = runActivityConfigService.getVoiceSwitch(dto.getVoiceSwitch(), loginUser);
                gameInfoDto.setIsVoiceUp(voiceSwitch);
            }
        }
        return gameInfoDto;
    }

    private final UserLevelService userLevelService;

    private final UserLevelBenefitRelService userLevelBenefitRelService;

    private final UserBenefitConfigService userBenefitConfigService;

    private final RunActivityUserTaskService runActivityUserTaskService;

    private void fillActionInfo(ZnsUserEntity loginUser, GameInfoDto gameInfoDto) {
        gameInfoDto.setStartAction(0);
        gameInfoDto.setEndAction(0);
        UserLevel userLevel = userLevelService.findByUserId(loginUser.getId());
        List<UserLevelBenefitRel> levelBenefitRelList = userLevelBenefitRelService.findByLevel(userLevel.getLevel(), true);
        List<Long> benefitIdList = levelBenefitRelList.stream().map(UserLevelBenefitRel::getBenefitId).collect(Collectors.toList());
        List<Integer> benefitTypeList = List.of(UserBenefitTypeEnum.START_ACTION.getCode(), UserBenefitTypeEnum.END_ACTION.getCode());
        UserBenefitConfigQuery benefitConfigQuery = UserBenefitConfigQuery.builder().configIdList(benefitIdList).benefitTypeList(benefitTypeList).build();
        List<UserBenefitConfig> list = userBenefitConfigService.findList(benefitConfigQuery);
        for (UserBenefitConfig userBenefitConfig : list) {
            if (UserBenefitTypeEnum.START_ACTION.getCode().equals(userBenefitConfig.getBenefitType())) {
                gameInfoDto.setStartAction(1);
            }
            if (UserBenefitTypeEnum.END_ACTION.getCode().equals(userBenefitConfig.getBenefitType())) {
                gameInfoDto.setEndAction(1);
            }
        }
    }
}
