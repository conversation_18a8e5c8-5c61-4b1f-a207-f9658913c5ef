package com.linzi.pitpat.api.mallservice.mananger;


import com.linzi.pitpat.core.constants.PayConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.awardservice.model.dto.PaymentResultDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.config.PitpatConfig;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderPayEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderDetailVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderItemListVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderPayService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.service.pay.PaymentService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import com.paypal.http.HttpResponse;
import com.paypal.orders.LinkDescription;
import com.paypal.orders.Order;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class OrderPayBussiness {
    @Resource
    private PitpatConfig config;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ZnsOrderPayService znsOrderPayService;

    @Autowired
    private ZnsUserAccountService userAccountService;

    @Autowired
    private VipUserService vipUserService;

    @Autowired
    private ZnsUserAccountDetailService userAccountDetailService;

    @Autowired
    private VipShopService vipShopService;

    @Autowired
    private ZnsOrderService znsOrderService;

    @Autowired
    private ZnsGoodsService znsGoodsService;

    /**
     * 支付
     *
     * @param order
     * @param userId
     * @param payType 支付类型 0:余额；1:paypal;
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<PaymentResultDto> initiatePayment(OrderDetailVo order, Long userId, Integer payType, String payPassword, String emailAddress, String serialNum) {
        //校验支付订单
        ZnsOrderPayEntity orderPay = znsOrderPayService.getOrderPayByOrderId(order.getOrderId());
        if (orderPay != null && (orderPay.getStatus() == 2 || orderPay.getStatus() == 1)) {
            //重复支付
            throw new BaseException(I18nMsgUtils.getMessage("payment.repeated"));
        }

        //创建支付订单
        if (Objects.isNull(orderPay)) {
            String prefix = PayConstant.PayChannelEnum.PAY_CHANNEL_0.getType().equals(payType) ? "payP" : "balance";
            orderPay = createOrderPay(order.getActualAmount(), userId, payType, order.getOrderId(), prefix);
            znsOrderPayService.insert(orderPay);
        }

        //发起支付
        if (PayConstant.PayChannelEnum.PAY_CHANNEL_1.getType().equals(payType)) {
            //palpay支付
            PaymentResultDto paymentResultDto = palPay(order, orderPay);
            return CommonResult.success(paymentResultDto);
        } else if (PayConstant.PayChannelEnum.PAY_CHANNEL_0.getType().equals(payType)) {
            //余额支付
            Result<PaymentResultDto> paymentResultDto = palBalance(order, orderPay, payPassword);
            return paymentResultDto;
        }
        return null;
    }

    /**
     * 余额支付
     *
     * @param order
     * @param orderPay
     * @return
     */
    private Result<PaymentResultDto> palBalance(OrderDetailVo order, ZnsOrderPayEntity orderPay, String payPassword) {
        String returnUrl = config.getMallH5Url() + "/member";
        //校验支付密码
        Result passwordResult = userAccountService.checkPassword(orderPay.getUserId(), payPassword, false);
        if (null != passwordResult) {
            return passwordResult;
        }

        // 开始支付
        RunActivityPayRequest request = new RunActivityPayRequest();
        request.setUserId(orderPay.getUserId());
        request.setPayType(orderPay.getPayType());
        request.setPayPassword(payPassword);
        request.setAmount(order.getActualAmount()); // 实际付款金额
        request.setAccountDetailTypeEnum(AccountDetailTypeEnum.PAY_ORDER_SPEND); //支付商城订单
        request.setPrivilegeBrand(-1); // 特权品牌
        request.setBrandRightsInterests(-1); // 权益类型
        Result payResult = null;
        try {
            payResult = userAccountService.payByBalance(request);
        } catch (Exception e) {
            throw new BaseException(Optional.ofNullable(e.getMessage()).orElse("pay error, please try again"));
        }

        //支付失败
        if (!CommonError.SUCCESS.getCode().equals(payResult.getCode())) {
            throw new BaseException(Optional.ofNullable(payResult.getMsg()).orElse("pay error, balance not enough"));
        }

        //更新订单
        ZnsOrderEntity orderEntity = new ZnsOrderEntity();
        orderEntity.setGmtModified(ZonedDateTime.now());
        orderEntity.setId(order.getOrderId());
        orderEntity.setStatus(OrderStatusEnum.FINSHED.getStatus());
        znsOrderService.update(orderEntity);

        //更新支付订单
        ZnsOrderPayEntity updateEntity = new ZnsOrderPayEntity();
        updateEntity.setId(orderPay.getId());
        updateEntity.setStatus(1); //0：发起支付；1:支付成功；2:支付中；-1:支付失败
        znsOrderPayService.update(updateEntity);

        //更新实物库存
        List<OrderItemListVo> items = order.getItems();
        if (!CollectionUtils.isEmpty(items)) {
            for (OrderItemListVo orderItem : items) {
                if (orderItem.getGoodsType() == 1) {
                    Long goodsId = orderItem.getGoodsId();
                    Long skuId = orderItem.getSkuId();
                    Integer count = orderItem.getCount();
                    znsGoodsService.processingInventoryVolume(goodsId, skuId, count);
                }
            }
        }

        PaymentResultDto resp = new PaymentResultDto();
        resp.setApprove(returnUrl);
        //封装返回结果
        return CommonResult.success(resp);
    }

    /**
     * 发起palpay支付
     *
     * @param order
     * @param orderPay
     * @return
     */
    private PaymentResultDto palPay(OrderDetailVo order, ZnsOrderPayEntity orderPay) {
        ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(orderPay.getUserId());
        PaymentResultDto paymentResultDto = new PaymentResultDto();
        //发起paypal支付
        String returnUrl = config.getMallH5Url() + "/order/confirm?orderStatus=success&id=" + order.getOrderId();
        String cancelUrl = config.getMallH5Url() + "/order/confirm?orderStatus=fail&id=" + order.getOrderId();
        HttpResponse<Order> response = null;
        try {
            response = paymentService.createOrder(order.getActualAmount(), orderPay.getPayNo(), returnUrl, cancelUrl, accountEntity.getCurrencyCode(), false);
        } catch (Exception e) {
            log.error("OrderPayBussiness#initiatePayment----------发起paypal支付异常：", e);
            throw new BaseException(I18nMsgUtils.getMessage("payment.create.paypal.order"));
        }
        String approve = "";
        String tradeNo = response.result().id();

        orderPay.setTradeNo(tradeNo);
        znsOrderPayService.update(orderPay);

        paymentResultDto.setOrderId(tradeNo);
        if (response.statusCode() == 201) {
            log.info("Status Code = {}, Status = {}, OrderID = {}, Intent = {}", response.statusCode(), response.result().status(), response.result().id(), response.result().checkoutPaymentIntent());
            for (LinkDescription link : response.result().links()) {
                if ("approve".equals(link.rel())) {
                    approve = link.href();
                    paymentResultDto.setApprove(approve);
                } else if ("capture".equals(link.rel())) {
                    paymentResultDto.setCapture(link.href());
                } else if ("self".equals(link.rel())) {
                    paymentResultDto.setSelf(link.href());
                }
            }
        } else {
            throw new BaseException(I18nMsgUtils.getMessage("payment.create.paypal.order"));
        }
        if (!StringUtils.hasText(approve)) {
            throw new BaseException(I18nMsgUtils.getMessage("payment.create.paypal.order"));
        }

        return paymentResultDto;
    }


    @Transactional(rollbackFor = Exception.class)
    public Result initiatePaymentByOrder(BigDecimal amount, Long userId) {
        String billNo = NanoId.randomNanoId();
        ;
        Map<String, Object> map = new HashMap<>();
        ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(userId);
        try {
            String returnUrl = config.getMallH5Url() + "/wallet/confrimPay?orderStatus=success&billNo=" + billNo;
            String cancelUrl = config.getMallH5Url() + "/wallet/confrimPay?orderStatus=fail&billNo=" + billNo;
            log.info("returnUrl=====" + returnUrl + "，cancelUrl = " + cancelUrl);
            //创建支付
            HttpResponse<Order> response = paymentService.createOrder(amount, billNo, returnUrl, cancelUrl, accountEntity.getCurrencyCode(), false);
            String approve = "";
            String tradeNo = response.result().id();
            map.put("orderId", tradeNo);
            if (response.statusCode() == 201) {
                log.info("Status Code = {}, Status = {}, OrderID = {}, Intent = {}", response.statusCode(), response.result().status(), response.result().id(), response.result().checkoutPaymentIntent());
                for (LinkDescription link : response.result().links()) {
                    log.info("Links-{}: {}    \tCall Type: {}", link.rel(), link.href(), link.method());
                    if ("approve".equals(link.rel())) {
                        approve = link.href();
                        map.put("approve", approve);
                    } else if ("capture".equals(link.rel())) {
                        map.put("capture", link.href());
                    } else if ("self".equals(link.rel())) {
                        map.put("self", link.href());
                    }
                }
                String totalAmount = response.result().purchaseUnits().get(0).amountWithBreakdown().currencyCode() + ":" + response.result().purchaseUnits().get(0).amountWithBreakdown().value();
                log.info("Total Amount: {}", totalAmount);
                log.info("createOrder response body: {}", JsonUtil.writeString(response.result()));
            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Failed to create PayPal order");
            }
            if (!StringUtils.hasText(approve)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Failed to create PayPal order");
            }
            userAccountDetailService.addAccountDetail(userId, 1, AccountDetailTypeEnum.RECHARGE, amount, billNo, null, 0, tradeNo, null, amount, amount, null);
            return CommonResult.success(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private ZnsOrderPayEntity createOrderPay(BigDecimal actualAmount, Long userId, int payType, Long orderId, String prefix) {
        ZnsOrderPayEntity orderPayEntity = new ZnsOrderPayEntity();
        orderPayEntity.setAmount(actualAmount);
        orderPayEntity.setOrderId(orderId);
        orderPayEntity.setPayNo(NanoId.randomNanoId());
        orderPayEntity.setPayType(payType);
        orderPayEntity.setUserId(userId);
        return orderPayEntity;
    }


}
