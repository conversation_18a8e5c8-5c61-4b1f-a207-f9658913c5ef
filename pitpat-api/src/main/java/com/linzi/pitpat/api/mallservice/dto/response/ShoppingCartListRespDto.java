package com.linzi.pitpat.api.mallservice.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * 购物车商品vo对象
 */
@Data
@NoArgsConstructor
public class ShoppingCartListRespDto {

    /**
     * spuId
     */
    private Long goodsId;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品图片
     */
    private String skuImgUrl;

    /**
     * 商品规格
     */
    private List<String> propertyList;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 购物车数量
     */
    private Integer count;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 状态，0：不可用，1：可用
     */
    private Integer state;

    /**
     * 修改时间
     */
    private ZonedDateTime gmtModified;

    /**
     * 【4.4.3新增】是否预估销售价，true:是预估价，false：不是
     */
    private Boolean isPredicted;

    /**
     * 币种符号 $，C$
     * @since 4.7.0
     */
    private String currencySymbol;

    /**
     * 是否当支持当前国家，true：支持，false：不支持
     * @since  4.7.0
     */
    private Boolean isSupportCurrentCountry;

    /**
     * 当前用户国家Name（支持I18n）
     * @since  4.7.0
     */
    private String countryName;

    /**
     * 折扣值 30% -> 30
     * @since 4.7.0
     */
    private BigDecimal discount;

    /**
     * 商品组合包类型
     * @since 4.8.0
     * 组合商品类型，-1：非组合商品,0:主品,1：衍生品
     */
    private Integer combinationGoodsType;



    public ShoppingCartListRespDto(String currencySymbol,Long goodsId, Long skuId, String skuName, String skuImgUrl, List<String> propertyList, BigDecimal originalPrice, BigDecimal salePrice, Integer stock, Integer state) {
        this.goodsId = goodsId;
        this.skuId = skuId;
        this.skuName = skuName;
        this.skuImgUrl = skuImgUrl;
        this.propertyList = propertyList;
        this.originalPrice = originalPrice;
        this.salePrice = salePrice;
        this.stock = stock;
        this.state = state;
        this.currencySymbol = currencySymbol;
    }
}
