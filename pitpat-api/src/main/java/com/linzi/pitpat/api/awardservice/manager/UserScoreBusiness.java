package com.linzi.pitpat.api.awardservice.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linzi.pitpat.api.awardservice.dto.response.AwardDto;
import com.linzi.pitpat.api.awardservice.dto.response.UrgeAwardDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.model.entity.EggActivityConfig;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.activityservice.service.EggActivityConfigService;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreIdDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.ColorEggConfig;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreExchangeAddress;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeActivityConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.query.AddColorEggAward;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreTypeResp;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.ColorEggConfigService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.ScoreExchangeAddressService;
import com.linzi.pitpat.data.awardservice.service.UrgeActivityConfigService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.model.entity.ScoreMallGoodsRelationDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.query.ScoreMallGoodsRelationQuery;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.third.erp.ErpApiUtil;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.ExpUser;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddressEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddressService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserScoreBusiness {
    private final ColorEggConfigService colorEggConfigService;
    private final ZnsUserService znsUserService;
    private final EggActivityConfigService eggActivityConfigService;
    private final ISysConfigService sysConfigService;
    @Deprecated
    private final ExpUserService expUserService;
    private final UrgeActivityConfigService urgeActivityConfigService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ZnsUserAccountService userAccountService;
    private final AwardActivityManager awardActivityManager;
    private final UserTaskDetailService userTaskDetailService;
    private final UserTaskBizService userTaskBizService;
    private final MallOrderBizService mallOrderBizService;
    private final ZnsOrderItemService znsOrderItemService;
    private final ScoreMallGoodsRelationService scoreMallGoodsRelationService;
    private final ZnsUserAddressService znsUserAddressService;
    private final ZnsGoodsService znsGoodsService;
    private final ExchangeScoreRuleService exchangeScoreRuleService;
    private final UserLevelService userLevelService;
    private final ExchangeScoreAwardService exchangeScoreAwardService;
    private final ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    private final UserWearsBagService userWearsBagService;
    private final ScoreExchangeAddressService scoreExchangeAddressService;
    private final WearsService wearsService;
    private final CouponService couponService;
    private final UserCouponManager userCouponManager;

    public Result addScore(AddColorEggAward request) {
        // 激励奖励积分处理
        if (request.getUrgeActivityConfigId() != null) {
            // 6. 发起鼓掌 ,7 发起加油， 8 发起击掌 ， 9 收到鼓掌，10，收到加油，11 收到击掌
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.Applause_CheerUp_Highfive_amount.getCode());
            String[] values = sysConfig.getConfigValue().split(",");
            Integer applauseCountLimit = MapUtil.getInteger(values[0]);
            Integer cheerUpCountLimit = MapUtil.getInteger(values[1]);
            Integer highFiveCountLimit = MapUtil.getInteger(values[2]);
            BigDecimal awardAmountLimit = MapUtil.getBigDecimal(values[3], BigDecimal.ZERO);
            BigDecimal sum = activityUserScoreService.selectScoreByUserIdSourceType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(ScoreConstant.SourceTypeEnum.source_type_18.getType(), ScoreConstant.SourceTypeEnum.source_type_19.getType(), ScoreConstant.SourceTypeEnum.source_type_20.getType()));
            // 发起鼓掌
            Integer applauseCount = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_18.getType()));
            Integer applauseExpCount = expUserService.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(6, 9));
            // 发起加油
            Integer cheerUpCount = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_19.getType()));
            Integer cheerUpExpCount = expUserService.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(7, 10));
            // 发起击掌
            Integer highFiveCount = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_20.getType()));
            Integer highFiveExpCount = expUserService.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(8, 11));
            int applauseCountAll = applauseCount + applauseExpCount;
            int cheerUpCountAll = cheerUpCount + cheerUpExpCount;
            int highFiveCountAll = highFiveCount + highFiveExpCount;
            if (Arrays.asList(6, 9).contains(request.getUrgeType())) {
                applauseCountAll = applauseCountAll + 1;
                if (applauseCountAll > applauseCountLimit) {
                    log.info("超过次数限制 count =" + applauseCountAll + ",countLimit=" + applauseCountLimit);
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.count.exceed"));
                }
            } else if (Arrays.asList(7, 10).contains(request.getUrgeType())) {
                cheerUpCountAll = cheerUpCountAll + 1;
                if (cheerUpCountAll > cheerUpCountLimit) {
                    log.info("超过次数限制 count =" + cheerUpCountAll + ",countLimit=" + cheerUpCountLimit);
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.count.exceed"));
                }
            } else if (Arrays.asList(8, 11).contains(request.getUrgeType())) {
                highFiveCountAll = highFiveCountAll + 1;
                if (highFiveCountAll > highFiveCountLimit) {
                    log.info("超过次数限制 count =" + highFiveCountAll + ",countLimit=" + highFiveCountLimit);
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.count.exceed"));
                }
            }
            UrgeActivityConfig urgeActivityConfig = urgeActivityConfigService.selectUrgeActivityConfigById(request.getUrgeActivityConfigId());
            //类型 1 表示金额 ， 2 表示经验值
            if (Objects.equals(request.getType(), 1)) {
                sum = sum.add(request.getAwardValue());
                if (sum.compareTo(awardAmountLimit) > 0) {
                    log.info("超过积分限制 sum=" + sum);
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.score.exceed"));
                }
                log.info("本次获取的奖励金额是 awardScore=" + request.getAwardValue());
                activityUserScoreService.handleRunScore(request);
            } else {
                ExpUser expUser = new ExpUser();
                expUser.setType(request.getUrgeType());
                expUser.setExp(request.getAwardValue().intValue());
                expUser.setUserId(request.getUserId());
                expUser.setActivityId(request.getActivityId());
                expUser.setActivityType(urgeActivityConfig.getActivityType());
                expUser.setIsPop(0);
                expUserService.insertExpUser(expUser);
            }
            UrgeAwardDto awardDto = new UrgeAwardDto(applauseCountAll, cheerUpCountAll, highFiveCountAll, sum, applauseCountLimit, cheerUpCountLimit, highFiveCountLimit, awardAmountLimit);
            return CommonResult.success(awardDto);
        } else if (request.getColorEggConfigId() != null) {
            //墙奖励
            if (Objects.nonNull(request.getValueType()) && request.getValueType() == 0) {
                // 金币奖励
                EggActivityConfig eggActivityConfig = eggActivityConfigService.selectEggActivityConfigByIdGmtStartTimeGmtEndTime(request.getEggActivityConfigId(), ZonedDateTime.now(), ZonedDateTime.now(), 0);
                if (eggActivityConfig == null) {
                    log.info("活动已经失效 ");
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.activity.status.ended"));
                }
                ColorEggConfig colorEggConfig = colorEggConfigService.selectColorEggConfigById(request.getColorEggConfigId());
                BigDecimal sum = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRule(request.getUserId(),
                        AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType(), ZonedDateTime.now(), colorEggConfig.getAwardRule());
                Integer count = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRuleCount(request.getUserId(),
                        AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType(), ZonedDateTime.now(), colorEggConfig.getAwardRule());
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.egg_color_limit.getCode());
                String limits[] = sysConfig.getConfigValue().split(",");
                int countLimit = MapUtil.getInteger(limits[0], 99);
                BigDecimal amountLimit = MapUtil.getBigDecimal(limits[1], new BigDecimal(99));
                BigDecimal awardAmount = MapUtil.getBigDecimal(colorEggConfig.getAwardValue(), BigDecimal.ZERO);
                // 墙全球化奖励货币兼容
                BigDecimal currencyAwardAmount = this.getUserCurrencyAmount(request.getUserId(), awardAmount);
                BigDecimal currencyAmountLimit = this.getUserCurrencyAmount(request.getUserId(), amountLimit);
                sum = sum.add(currencyAwardAmount);
                count = count + 1;
                ZnsUserEntity znsUserEntity = znsUserService.findById(request.getUserId());
                log.info("次数金额较验的4个参数 sum=" + sum + ",amountLimit=" + currencyAmountLimit + "count =" + count + ",countLimit=" + countLimit);
                if (Objects.equals(znsUserEntity.getIsRobot(), 1)) {
                    log.info("机器人不做金额和数量的限制");
                } else {
                    if (sum.compareTo(currencyAmountLimit) > 0) {
                        log.info("超过金额限制 sum=" + sum + ",amountLimit=" + currencyAmountLimit);
                        return CommonResult.fail(I18nMsgUtils.getMessage("userScore.amount.exceed"));
                    }

                    if (count > countLimit) {
                        log.info("超过次数限制 count =" + count + ",countLimit=" + countLimit);
                        return CommonResult.fail(I18nMsgUtils.getMessage("userScore.count.exceed"));
                    }
                }
                log.info("本次获取的奖励金额是 awardAmount=" + awardAmount);
                awardActivityManager.handleTeamRunAward3D(awardAmount, request.getUserId(), eggActivityConfig.getId(),
                        AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD, eggActivityConfig.getActivityType(), request.getActivityId());
                AwardDto awardDto = new AwardDto(count, sum, countLimit, currencyAmountLimit);
                return CommonResult.success(awardDto);
            } else {
                // 积分墙
                EggActivityConfig eggActivityConfig = eggActivityConfigService.selectEggActivityConfigByIdGmtStartTimeGmtEndTime(request.getEggActivityConfigId(), ZonedDateTime.now(), ZonedDateTime.now(), 0);
                if (eggActivityConfig == null) {
                    log.info("活动已经失效 ");
                    return CommonResult.fail(I18nMsgUtils.getMessage("userScore.activity.status.ended"));
                }
                ColorEggConfig colorEggConfig = colorEggConfigService.selectColorEggConfigById(request.getColorEggConfigId());
                BigDecimal sum = activityUserScoreService.selectScoreByUserIdSourceType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                        Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_17.getType()));

                Integer count = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                        Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_17.getType()));
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.egg_score_color_limit.getCode());
                String[] limits = sysConfig.getConfigValue().split(",");
                int countLimit = MapUtil.getInteger(limits[0], 99);

                BigDecimal amountLimit = MapUtil.getBigDecimal(limits[1], new BigDecimal(99));
                BigDecimal awardAmount = MapUtil.getBigDecimal(colorEggConfig.getPointValue(), BigDecimal.ZERO);

                sum = sum.add(awardAmount);
                count = count + 1;

                ZnsUserEntity znsUserEntity = znsUserService.findById(request.getUserId());
                log.info("次数金额较验的4个参数 sum=" + sum + ",amountLimit=" + amountLimit + "count =" + count + ",countLimit=" + countLimit);
                if (Objects.equals(znsUserEntity.getIsRobot(), 1)) {
                    log.info("机器人不做金额和数量的限制");
                } else {
                    if (sum.compareTo(amountLimit) > 0) {
                        log.info("超过积分限制 sum=" + sum + ",amountLimit=" + amountLimit);
                        return CommonResult.fail(I18nMsgUtils.getMessage("userScore.amount.exceed"));
                    }

                    if (count > countLimit) {
                        log.info("超过次数限制 count =" + count + ",countLimit=" + countLimit);
                        return CommonResult.fail(I18nMsgUtils.getMessage("userScore.count.exceed"));
                    }
                }
                log.info("本次获取的奖励积分是 awardAmount=" + awardAmount);
                request.setAwardValue(awardAmount);
                activityUserScoreService.handleRunScore(request);
                AwardDto awardDto = new AwardDto(count, sum, countLimit, amountLimit);
                return CommonResult.success(awardDto);
            }
        }
        return CommonResult.success();
    }

    public Result remainAward(AddColorEggAward request) {
        if ("egg".equals(request.getRemainType())) {
            if (Objects.nonNull(request.getValueType()) && request.getValueType() == 0) {
                // 金币限制
                BigDecimal sum = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRule(request.getUserId(),
                        AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType(), ZonedDateTime.now(), "%Y-%m-%d");
                Integer count = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRuleCount(request.getUserId(),
                        AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getType(), ZonedDateTime.now(), "%Y-%m-%d");
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.egg_color_limit.getCode());
                String limits[] = sysConfig.getConfigValue().split(",");
                int countLimit = MapUtil.getInteger(limits[0], 99);
                BigDecimal amountLimit = MapUtil.getBigDecimal(limits[1], new BigDecimal(99));
                BigDecimal currencyAmountLimit = this.getUserCurrencyAmount(request.getUserId(), amountLimit);
                AwardDto awardDto = new AwardDto(count, sum, countLimit, currencyAmountLimit);
                return CommonResult.success(awardDto);
            } else {
                // 积分限制
                BigDecimal sum = activityUserScoreService.selectScoreByUserIdSourceType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                        Arrays.asList(ScoreConstant.SourceTypeEnum.source_type_17.getType()));
                Integer count = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                        Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_17.getType()));
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.egg_score_color_limit.getCode());
                String limits[] = sysConfig.getConfigValue().split(",");
                int countLimit = MapUtil.getInteger(limits[0], 99);
                BigDecimal amountLimit = MapUtil.getBigDecimal(limits[1], new BigDecimal(99));
                AwardDto awardDto = new AwardDto(count, sum, countLimit, amountLimit);
                return CommonResult.success(awardDto);
            }
        } else if ("urge".equals(request.getRemainType())) {
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.Applause_CheerUp_Highfive_amount.getCode());
            String values[] = sysConfig.getConfigValue().split(",");
            Integer applauseCountLimit = MapUtil.getInteger(values[0]);
            Integer cheerUpCountLimit = MapUtil.getInteger(values[1]);
            Integer highFiveCountLimit = MapUtil.getInteger(values[2]);
            BigDecimal awardAmountLimit = MapUtil.getBigDecimal(values[3], BigDecimal.ZERO);
            BigDecimal sum = activityUserScoreService.selectScoreByUserIdSourceType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(ScoreConstant.SourceTypeEnum.source_type_18.getType(), ScoreConstant.SourceTypeEnum.source_type_19.getType(), ScoreConstant.SourceTypeEnum.source_type_20.getType()));
            // 发起鼓掌
            Integer applauseCount = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_18.getType()));
            Integer applauseExpCount = expUserService.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(6, 9));
            // 发起加油
            Integer cheerUpCount = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_19.getType()));
            Integer cheerUpExpCount = expUserService.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(7, 10));
            // 发起击掌
            Integer highFiveCount = activityUserScoreService.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Collections.singletonList(ScoreConstant.SourceTypeEnum.source_type_20.getType()));
            Integer highFiveExpCount = expUserService.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
                    Arrays.asList(8, 11));
            UrgeAwardDto awardDto = new UrgeAwardDto(applauseCount + applauseExpCount, cheerUpCount + cheerUpExpCount, highFiveCount + highFiveExpCount,
                    sum, applauseCountLimit, cheerUpCountLimit, highFiveCountLimit, awardAmountLimit);
            return CommonResult.success(awardDto);
        }
        return null;
    }

    /**
     * 积分兑换
     * @param dto
     * @param currency
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExchangeScoreTypeResp scoreExchange(ScoreIdDto dto, Currency currency) {
        ExchangeScoreTypeResp resp = new ExchangeScoreTypeResp();
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());

        // 校验商品剩余可兑换量
        // 校验积分是否充足
        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(dto.getId());
        //兑换用户等级限制
        UserLevel userLevel = userLevelService.findByUserId(dto.getUserId());
        if (userLevel.getLevel() < exchangeScoreRule.getUserLevelLimit()) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"), CommonError.SYSTEM_ERROR.getCode());
        }
        if (exchangeScoreRule.getExchangeReserve() == 0) {
            throw new BaseException(I18nMsgUtils.getMessage("userScore.exchange.count.exceed"), CommonError.BUSINESS_ERROR.getCode());
        }
        if (exchangeScoreRule.getExchangeScore() > activityUserScoreService.getAllUserScore(dto.getUserId())) {
            throw new BaseException(I18nMsgUtils.getMessage("userScore.exchange.points.exceed"), CommonError.BUSINESS_ERROR.getCode());
        }
        Boolean flag = exchangeScoreRuleService.verifyUserScoreRuleDayExchange(dto.getUserId(), exchangeScoreRule.getId());
        if (!flag) {
            throw new BaseException(I18nMsgUtils.getMessage("userScore.exchange.count.exceed"), CommonError.BUSINESS_ERROR.getCode());
        }
        Long goodsId = null;
        BigDecimal goodsOriginalPrice = BigDecimal.ZERO;
        // 查询规则对应的兑换商品
        ExchangeScoreAward exchangeAward;
        if (Objects.isNull(exchangeScoreRule.getCouponId())) {
            Long ruleId = exchangeScoreRule.getId();
            exchangeAward = exchangeScoreAwardService.getOne(Wrappers.<ExchangeScoreAward>lambdaQuery().eq(ExchangeScoreAward::getIsDelete, 0).eq(ExchangeScoreAward::getRuleId, ruleId));
            if (Objects.isNull(exchangeAward)) {
                throw new BaseException(I18nMsgUtils.getMessage("common.params.error"), CommonError.BUSINESS_ERROR.getCode());
            }
            if (Objects.equals(exchangeAward.getExchangeType(), ExchangeScoreTypeEnum.PHYSICAL_REWARDS.getCode())){
                ZnsUserAddressEntity userAddress = znsUserAddressService.findById(dto.getUserAddressId());
                if (Objects.isNull(userAddress)){
                    throw new BaseException(I18nMsgUtils.getMessage("common.params.error"), CommonError.BUSINESS_ERROR.getCode());
                }
                //错误地址业务兜底，后续地址都更新后(addressIdStr会是空字符串)不需要再加这个判定，
                String addressIdStr = sysConfigService.selectConfigByKey("mistake_address_ids");
                if (StringUtils.hasText(addressIdStr)) {
                    List<Long> addressIdList = JsonUtil.readList(addressIdStr, Long.class);
                    if (!CollectionUtils.isEmpty(addressIdList) && addressIdList.contains(dto.getUserAddressId())) {
                        throw new BaseException(I18nMsgUtils.getMessage("mall.home.mistake.address.check"));
                    }
                }
                ScoreMallGoodsRelationDo relationDo = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setRuleId(ruleId).setCountryCode(userAddress.getCountryCode()));
                goodsId = Objects.nonNull(relationDo) ? relationDo.getGoodsId() : null;
                ScoreMallGoodsRelationDo userRelation = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setRuleId(ruleId).setCountryCode(I18nConstant.CountryCodeEnum.US.getCode()));
                if (Objects.nonNull(userRelation)){
                    ZnsGoodsEntity goodsEntity = znsGoodsService.findById(userRelation.getGoodsId());
                    goodsOriginalPrice = Objects.nonNull(goodsEntity) ? goodsEntity.getOriginalPrice() : BigDecimal.ZERO;
                }
            }
        } else {
            // 旧版本使用原来的券
            exchangeAward = new ExchangeScoreAward();
            exchangeAward.setExchangeType(ExchangeScoreTypeEnum.COUPON.getCode());
            exchangeAward.setExchangeId(exchangeScoreRule.getCouponId());
        }
        // 扣除余额
        BigDecimal exchangeAmount = exchangeScoreRule.getExchangeAmount();
        if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currency.getCurrencyCode())) {
            //不是美元，查询其他币种金额
            ExchangeScoreRuleCurrencyEntity currencyEntity = exchangeScoreRuleCurrencyService.findByRuleIdAndCurrencyCode(exchangeScoreRule.getId(), currency.getCurrencyCode());
            if (currencyEntity != null) {
                exchangeAmount = currencyEntity.getExchangeAmount();
            }
        }
        if (exchangeAmount.compareTo(new BigDecimal(0)) > 0) {
            if (!StringUtils.hasText(dto.getPassword())) {
                throw new BaseException(I18nMsgUtils.getMessage("common.page.expired"));
            } else {
                // 校验密码
                Result result = userAccountService.checkPassword(dto.getUserId(), dto.getPassword(), Boolean.TRUE);
                if (Objects.nonNull(result)) {
                    throw new BaseException(result.getMsg());
                }
                // 扣除余额
                RunActivityPayRequest request = new RunActivityPayRequest();
                request.setUserId(znsUserEntity.getId());
                request.setPayType(0);
                request.setPayPassword(dto.getPassword());
                request.setAccountDetailTypeEnum(AccountDetailTypeEnum.EXCHANGE_SCORE_SPEND);
                request.setPrivilegeBrand(-1); // 特权品牌
                request.setBrandRightsInterests(-1); // 权益类型
                request.setActivityId(exchangeScoreRule.getId()); // 活动id也暂时用积分规则id
                request.setOperationalActivityId(exchangeScoreRule.getId()); // 关联的积分兑换规则id
                request.setAmount(exchangeAmount); // 兑换所需金额
                Result payResult = userAccountService.payByBalance(request);// 使用余额支付
                if (Objects.nonNull(payResult) && payResult.getCode().equals(CommonError.BUSINESS_ERROR.getCode())) {
                    // 余额不足的情况
//                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("userAccount.balance.insufficient"), payResult.getData());
                }
            }
        }
        activityUserScoreService.useActivityUserScore(exchangeScoreRule, dto);
        // 增加积分兑换记录(相当于扣除积分)
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setExchangeScoreRuleId(exchangeScoreRule.getId());
        activityUserScore.setScore(exchangeScoreRule.getExchangeScore());
        activityUserScore.setAmount(exchangeAmount);
        activityUserScore.setStatus(1);
        activityUserScore.setExchangeTime(ZonedDateTime.now());
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setType(exchangeAward.getExchangeType());
        activityUserScore.setCouponId(exchangeScoreRule.getCouponId());
        activityUserScore.setIncome(-1);
        if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.COUPON.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N2.getType()); // 积分兑换券
        } else if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.CLOTHES.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N3.getType()); // 积分兑换商品（衣服）
        } else if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.PHYSICAL_REWARDS.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N6.getType()); // 积分兑换实物
        } else if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.ELSE.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N5.getType()); // 积分兑换其他
        }
        activityUserScore.setUserId(znsUserEntity.getId());
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);


        ExchangeScoreTypeEnum exchangeType = ExchangeScoreTypeEnum.findByType(exchangeAward.getExchangeType());
        // 判断商品类型 不同类型发送不同消息
        switch (exchangeType) {
            case COUPON: // 优惠券
                Coupon coupon = couponService.selectCouponById(exchangeScoreRule.getCouponId());
                if (!Objects.equals(coupon.getQuota(), -1) && coupon.getQuota() - coupon.getQuotaSend() <= 0) {
                    // 顾及到事务 需要抛错
                    throw new BaseException(I18nMsgUtils.getMessage("userScore.exchange.count.exceed"));
                }
                userCouponManager.exchangeUserCouponMilepost(coupon, null, znsUserEntity, 1, null, null);
                break;
            case CLOTHES: // 服装
                Long exchangeId = exchangeAward.getExchangeId();
                // 查询服装信息
                Wears wears = wearsService.selectWearsById(exchangeId);
                // 查询用户背包有没有该服装
                List<UserWearsBag> wearBag = userWearsBagService.findListByQuery(UserWearBagQuery.builder().userId(znsUserEntity.getId()).wearType(wears.getWearType()).wearValue(wears.getWearId()).build());

                if (CollectionUtils.isEmpty(wearBag)) {
                    // 放入用户背包
                    UserWearsBag userWearsBag = new UserWearsBag();
                    userWearsBag.setWearType(wears.getWearType());
                    userWearsBag.setWearName(wears.getWearName());
                    userWearsBag.setWearValue(wears.getWearId());
                    userWearsBag.setWearImageUrl(wears.getWearImageUrl());
                    userWearsBag.setSource(2);
                    userWearsBag.setUserId(znsUserEntity.getId());
                    userWearsBag.setStatus(0);
                    userWearsBag.setIsNew(0);
                    if (Objects.nonNull(exchangeAward.getExpiredTime())) {
                        userWearsBag.setExpiredTime(getAddHours(exchangeAward.getExpiredTime()));
                    }
                    userWearsBagService.insert(userWearsBag);
                } else {
                    // 存在限时服装 更新时间
                    UserWearsBag userWearsBag = wearBag.get(0);
                    if (Objects.nonNull(userWearsBag.getExpiredTime())) {
                        userWearsBag.setExpiredTime(DateUtil.addHours(userWearsBag.getExpiredTime(), exchangeAward.getExpiredTime() * 24));
                        userWearsBagService.update(userWearsBag);
                    }
                }
                break;
            case MUSIC:
                break;
            case PROPS:
                break;
            case PHYSICAL_REWARDS:
                //保存兑换地址表
                ScoreExchangeAddress scoreExchangeAddress = new ScoreExchangeAddress();
                scoreExchangeAddress.setUserId(dto.getUserId());
                scoreExchangeAddress.setAddressId(dto.getUserAddressId());
                scoreExchangeAddress.setRuleId(exchangeScoreRule.getId());
                scoreExchangeAddress.setUserScoreId(activityUserScore.getId());
                scoreExchangeAddressService.insert(scoreExchangeAddress);
                //增加订单记录
                ZnsOrderEntity znsOrderEntity = mallOrderBizService.scoreMallFillOrder(znsUserEntity, exchangeScoreRule, dto.getUserAddressId(),goodsId,goodsOriginalPrice);
                ErpApiUtil.addOrModifyOrder(mallOrderBizService.convertErpOrderListRespDto(znsOrderEntity));
                resp.setOrderNo(znsOrderEntity.getOrderNo());
                break;
            case ELSE:
                break;
            default:
                break;
        }
        Integer exchangeCount = activityUserScoreService.selectByExchangeScoreRuleIdSource(exchangeScoreRule.getId(), Arrays.asList(-2, -3));
        //剩余库存
        Integer remainNum = exchangeScoreRule.getRemainNum() - 1;
        ExchangeScoreRule rule = new ExchangeScoreRule();
        rule.setExchangeCount(exchangeCount);
        rule.setRemainNum(remainNum);
        rule.setId(exchangeScoreRule.getId());
        exchangeScoreRuleService.updateExchangeScoreByIdAndExchangeReserve(rule);
        exchangeScoreRuleService.setUserScoreRuleDayExchangeCache(dto.getUserId(), exchangeScoreRule.getId());
        userTaskDetailService.completeLevelTask(dto.getUserId(), UserExpObtainSubTypeEnum.POINTS_REDEMPTION.getCode(), false);//完成积分兑换
        userTaskBizService.completeEvent(new EventTriggerDto().setUser(znsUserEntity).setEventSubType(TaskConstant.TakEventSubTypeEnum.SCORE_EXCHANGE_GOODS.getCode()));
        resp.setExchangeType(exchangeAward.getExchangeType());
        return resp;
    }

    public BigDecimal getUserCurrencyAmount(Long userId, BigDecimal dollarAwardAmount) {
        Currency userCurrency = userAccountService.getUserCurrency(userId);
        // 官当活动兼容汇率计算
        if (!userCurrency.getCurrencyCode().equals(I18nConstant.CurrencyCodeEnum.USD.getCode())) {
            ExchangeRateConfigEntity exchangeRateConfigEntity = exchangeRateConfigService.selectByUsd2TargetCurrency(userCurrency.getCurrencyCode());
            if (Objects.nonNull(exchangeRateConfigEntity)) {
                // 处理货币汇率问题
                dollarAwardAmount = dollarAwardAmount.multiply(exchangeRateConfigEntity.getExchangeRate()).setScale(2, RoundingMode.HALF_UP);
            }
        }
        return dollarAwardAmount;
    }

    private ZonedDateTime getAddHours(Integer expiredTime) {
        ZonedDateTime date = ZonedDateTime.now();
        if (date.getMinutes() > 0) {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(DateUtil.addHours(ZonedDateTime.now(), 1), 0), 0), expiredTime * 24);
        } else {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(ZonedDateTime.now(), 0), 0), expiredTime * 24);
        }
    }
}
