package com.linzi.pitpat.api.equipment.controller.device;


import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.RunDataBizService;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.request.RunDataDeviceRequest;
import com.linzi.pitpat.data.request.RunDataOtherDeviceRequest;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.request.RunEndDto;
import com.linzi.pitpat.data.robotservice.manager.RobotRunPlanManager;
import com.linzi.pitpat.data.robotservice.service.RobotRunPlanService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.runData.DataProcessingVo;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 硬件数据同步服务
 **/
@RestController
@RequestMapping("/device/data")
@Slf4j
public class DeviceDataController extends BaseAppController {
    @Autowired
    private RobotRunPlanService robotRunPlanService;

    @Autowired
    private RunDataBizService runDataBizService;

    @Resource
    private ZnsUserService znsUserService;

    @Resource
    private ZnsUserEquipmentService znsUserEquipmentService;

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private TencentImUtil tencentImUtil;
    @Autowired
    private RobotRunPlanManager robotRunPlanManager;
    @Resource(name = "asyncExecutor")
    private ThreadPoolTaskExecutor executor;

    /**
     * 硬件数据同步
     *
     * @param servletRequest HttpServletRequest
     * @return Result<DataProcessingVo>
     */
    @PostMapping("/synchronize")
    public Result<DataProcessingVo> synchronize(HttpServletRequest servletRequest) throws IOException {

        StringBuilder stringBuilder = new StringBuilder();

        try (BufferedReader bufferedReader = servletRequest.getReader()) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            return CommonResult.fail("请求参数解析错误");
        }

        String requestBody = stringBuilder.toString();

        RunDataDeviceRequest request = null;
        try {
            if (StringUtils.hasText(requestBody)) {
                request = JsonUtil.getObjectMapper().readValue(requestBody, RunDataDeviceRequest.class);
            }
        } catch (Exception e) {
            //由于硬件问题，这个接口的错误日志过多，因此不在记录错误日志
            //log.error("/device/data/synchronize, msg={}", e.getMessage());
            return CommonResult.fail("请求参数解析错误");
        }
        if (Objects.isNull(request)) {
            return CommonResult.fail("请求参数错误");
        }
        if (!StringUtils.hasText(request.getUn())) {
            return CommonResult.fail("设备参数错误");
        }

        String key = String.format(RedisKeyConstant.USER_EQUIPMENT_CONNECT, request.getUn());
        RBucket<Object> bucket = redissonClient.getBucket(key);
        Long userId = null;
        if (Objects.isNull(bucket.get())) {
            ZnsUserEquipmentEntity userEquipment = znsUserEquipmentService.getRecentEquipmentConnected(request.getUn());
            if (Objects.nonNull(userEquipment) && Objects.nonNull(userEquipment.getUserId())) {
                bucket.set(userEquipment.getUserId(), 1L, TimeUnit.HOURS);
                userId = userEquipment.getUserId();
            }
        } else {
            userId = (Long) bucket.get();
        }
        if (Objects.nonNull(userId)) {
            // 将已设备绑定的用户设置到跑步数据上
            request.setId(userId);
        } else {
            log.error("硬件跑上传跑步数据，设备未绑定，unique_code:{},userId:{},activityId:{}", request.getUn(), request.getId(), request.getAid());
            return CommonResult.fail("设备未绑定");
        }

        RunDataRequest data = new RunDataRequest();
        data.setRun_status(request.getRs());
        data.setId_no(request.getId());
        data.setOrder_no(request.getOn());
        data.setUnique_code(request.getUn());
        data.setActivityId(request.getAid());
        data.setErrorCode(request.getE());
        data.setRunType(request.getRt());
        if (StringUtils.hasText(request.getV())) {
            data.setFirmwareVersion(Integer.valueOf(request.getV()));
        }
        if (!CollectionUtils.isEmpty(request.getD())) {
            List<ZnsUserRunDataDetailsSecondEntity> secondEntities = request.getD().stream().map(r -> {
                ZnsUserRunDataDetailsSecondEntity entity = new ZnsUserRunDataDetailsSecondEntity();
                entity.setRunTime(r.getR());
                entity.setVelocity(r.getV());
                entity.setMileage(r.getM());
                entity.setHeartRate(r.getH());
                entity.setGradient(r.getG());
                entity.setCalories(r.getC());
                entity.setStepNum(r.getS());
                entity.setNoLiveLoad(r.getNl());
                entity.setRunType(r.getRt());
                entity.setSensorStatus(r.getSs());
                entity.setSensorMax(r.getSm());
                entity.setSensorTrends(r.getSt());
                entity.setSensorSteps(r.getSs1());
                entity.setRealElectricity(r.getRe());
                entity.setRealRotate(r.getRr());
                entity.setRealElectricitySteps(r.getRes());
                entity.setControlVol(r.getCvl());
                entity.setControlStatus(r.getCls());
                entity.setBuzzerStatus(r.getBss());
                return entity;
            }).collect(Collectors.toList());
            data.setData(secondEntities);
        }
        data.setDataSource(1);
        Result result = runDataBizService.dataProcessing(data);

        return result;
    }

    /**
     * 新设备硬件数据上报
     *
     * @param servletRequest
     * @return
     * @throws IOException
     */
    @PostMapping("/report")
    public Result<DataProcessingVo> report(HttpServletRequest servletRequest) {
        log.info("/device/data/report start");
        StringBuilder stringBuilder = new StringBuilder();

        try (BufferedReader bufferedReader = servletRequest.getReader()) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            log.error("/device/data/report e", e);
        }

        String requestBody = stringBuilder.toString();
        log.info("/device/data/report={}", requestBody);

        RunDataOtherDeviceRequest request = null;
        try {
            request = JsonUtil.readValue(requestBody, RunDataOtherDeviceRequest.class);
        } catch (Exception e) {
            log.error("/device/data/report, msg={}", e.getMessage());
        }

        log.info("/device/data/report end");
        DataProcessingVo dataProcessingVo = new DataProcessingVo();
        dataProcessingVo.setDetailId(1L);
        return CommonResult.success(dataProcessingVo);
    }


//    public Result<DataProcessingVo> synchronize(RunDataDeviceRequest request) {
//        RunDataRequest data = new RunDataRequest();
//        data.setRun_status(request.getRs());
//        data.setId_no(request.getId());
//        data.setOrder_no(request.getOn());
//        data.setUnique_code(request.getUn());
//        data.setActivityId(request.getAid());
//        data.setErrorCode(request.getE());
//        data.setRunType(request.getRt());
//        log.info("Call /device/data/synchronize={}", request);
//        if (StringUtils.hasText(request.getV())) {
//            data.setFirmwareVersion(Integer.valueOf(request.getV()));
//        }
//        if (!CollectionUtils.isEmpty(request.getD())) {
//            List<ZnsUserRunDataDetailsSecondEntity> secondEntities = request.getD().stream().map(r -> {
//                ZnsUserRunDataDetailsSecondEntity entity = new ZnsUserRunDataDetailsSecondEntity();
//                entity.setRunTime(r.getR());
//                entity.setVelocity(r.getV());
//                entity.setMileage(r.getM());
//                entity.setHeartRate(r.getH());
//                entity.setGradient(r.getG());
//                entity.setCalories(r.getC());
//                entity.setStepNum(r.getS());
//                entity.setNoLiveLoad(r.getNl());
//                entity.setRunType(r.getRt());
//                entity.setSensorStatus(r.getSs());
//                entity.setSensorMax(r.getSm());
//                entity.setSensorTrends(r.getSt());
//                entity.setSensorSteps(r.getSs1());
//                entity.setRealElectricity(r.getRe());
//                entity.setRealRotate(r.getRr());
//                return entity;
//            }).collect(Collectors.toList());
//            data.setData(secondEntities);
//        }
//        data.setDataSource(1);
//        Result result = runDataBizService.dataProcessing(data);
//
//        return result;
//    }

    /**
     * 硬件跑步结束
     *
     * @param runEndDto
     * @return
     */
    @PostMapping("/run/end")
    public Result runEnd(@RequestBody RunEndDto runEndDto) {
        return robotRunPlanManager.runEnd(runEndDto.getMindUserMatchId(), runEndDto.getActivityId(), runEndDto.getUserId());
    }


    /**
     * 更改用户头像（同步IM）
     *
     * @param file 图片文件
     * @return Result
     */
    @RequestMapping("/change/HeadPortrait")
    public Result changeHeadPortraitReal(@RequestParam("file") MultipartFile file) {
        Map<String, Object> map = AwsUtil.putS3Object(file, "headPortrait");
        String headPortrait = (String) map.get("url");
        ZnsUserEntity user = getLoginUser();
        user.setHeadPortrait(headPortrait);
        znsUserService.update(user);
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        //更新im资料库
        executor.execute(() -> {
            try {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                tencentImUtil.portraitSet(user.getId().toString(), user.getHeadPortrait(), user.getFirstName(), user.getGender());
            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                OrderUtil.removeLogNo();
            }
        });
        return CommonResult.success(headPortrait);
    }


}

