package com.linzi.pitpat.api.activityservice.manager;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.model.entity.TrainingOfflinePkRecord;
import com.linzi.pitpat.data.activityservice.model.entity.TrainingUserInfo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkFriendListVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkMatchOpponentUserBeanVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkMatchOpponentUserListVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkRankVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkResultVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkUserRateAndTimeVo;
import com.linzi.pitpat.data.activityservice.service.TrainingOfflinePkRecordService;
import com.linzi.pitpat.data.activityservice.service.TrainingUserInfoService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.OfflinePkCurrencyEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.resp.course.OfflinePkChallengeRecordDto;
import com.linzi.pitpat.data.userservice.dto.request.FriendIdRequest;
import com.linzi.pitpat.data.userservice.enums.CountryFlagConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.query.UserEquipmentQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.FriendSimpleInfoVO;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.annotation.RedisLock;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class TrainingOfflinePkManager {

    private final TrainingUserInfoService trainingUserInfoService;
    private final TrainingOfflinePkRecordService trainingOfflinePkRecordService;
    private final ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ZnsUserService znsUserService;
    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final ZnsUserFriendService znsUserFriendService;
    private final ZnsUserAccountService znsUserAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityUserScoreService activityUserScoreService;
    private final RedisTemplate redisTemplate;


    public TrainingUserInfo getUserPkWinAmount(Long userId) {
        TrainingUserInfo trainingUserInfo = trainingUserInfoService.selectUserPkInfo(userId);
        if (Objects.isNull(trainingUserInfo)) {
            trainingUserInfo = new TrainingUserInfo();
            trainingUserInfo.setPkAllAward(BigDecimal.ZERO);
            trainingUserInfo.setPkTotalScore(0);
        }
        return trainingUserInfo;
    }

    public Page<OfflinePkChallengeRecordDto> challengeRecord(Long userId, PageQuery pagePo) {
        return trainingOfflinePkRecordService.selectChallengeRecord(userId, pagePo);
    }

    public OfflinePkUserRateAndTimeVo challengeInfo(Long userId) {
        OfflinePkUserRateAndTimeVo offlinePkUserRateAndTimeVo = trainingUserInfoService.selectUserWinRateAndTime(userId);
        Integer count = trainingOfflinePkRecordService.selectUserTodayTime(userId);
        offlinePkUserRateAndTimeVo.setNowDayTimes(count);
        Integer todayWin = trainingOfflinePkRecordService.selectUserTodayWinTime(userId);
        if (count == 0) {
            offlinePkUserRateAndTimeVo.setTodayPkWinRace(new BigDecimal(0));
        } else {
            offlinePkUserRateAndTimeVo.setTodayPkWinRace((new BigDecimal(todayWin).divide(new BigDecimal(count), 2, RoundingMode.HALF_UP)));
        }
        return offlinePkUserRateAndTimeVo;
    }

    public OfflinePkResultVo startPk(Long userId, Long opponentUserId, Integer pkTime) {
        OfflinePkResultVo offlinePkResultVo = new OfflinePkResultVo();
        offlinePkResultVo.setPkResult(-1);
        ZnsRunActivityConfigEntity znsRunActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.COURSE_OFFLINE_PK.getType(), null);
        Map<String, Object> jsonObject = JsonUtil.readValue(znsRunActivityConfig.getActivityConfig());
        jsonObject.get("everyDayPkRandomTime");
        Integer everyDayPkAllTime = MapUtil.getInteger(jsonObject.get("everyDayPkAllTime"));
        if (pkTime + 1 > everyDayPkAllTime) {
            throw new BaseException("You have reached the limit, please come back tomorrow.", 500);
        }
        Map<String, Object> self = getTodayUTC0RunMileageByUserId(userId);
        Map<String, Object> opponentUser = getTodayUTC0RunMileageByUserId(opponentUserId);
        BigDecimal selfTarget = (BigDecimal) self.get("runMileage");
        BigDecimal opponentUserTarget = (BigDecimal) opponentUser.get("runMileage");
        offlinePkResultVo.setSelfTarget(selfTarget);
        offlinePkResultVo.setOpponentUserTarget(opponentUserTarget);
        offlinePkResultVo.setAward(BigDecimal.ZERO);
        //用户币种
        ZnsUserAccountEntity userAccount = znsUserAccountService.getUserAccount(userId);
        if (selfTarget.compareTo(opponentUserTarget) >= 0 && selfTarget.compareTo(BigDecimal.ZERO) > 0) {
            offlinePkResultVo.setPkResult(YesNoStatus.YES.getCode());
            offlinePkResultVo.setAward(MapUtil.getBigDecimal(jsonObject.get(OfflinePkCurrencyEnum.findByCurrencyCode(userAccount.getCurrencyCode()).getType())));
            offlinePkResultVo.setScore(MapUtil.getInteger(jsonObject.get("pkScoreAward")));
            Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode());
            offlinePkResultVo.setCurrency(currency);
        }
        if (selfTarget.compareTo(opponentUserTarget) == 0 && selfTarget.compareTo(BigDecimal.ZERO) == 0) {
            offlinePkResultVo.setPkResult(0);
        }
        updatePkInfo(offlinePkResultVo, userId, opponentUserId, pkTime);
        return offlinePkResultVo;
    }


    private Map<String, Object> getTodayUTC0RunMileageByUserId(Long userId) {
        ZonedDateTime date = ZonedDateTime.now();

        ZonedDateTime dateStart = DateUtil.startOfDate(date);
        ZonedDateTime dateEnd = DateUtil.endOfDate(date);

        return znsUserRunDataDetailsService.getTodayUTC0RunMileageByUserId(userId, dateStart, dateEnd);
    }

    public OfflinePkFriendListVo friendList(Long userId) {
        OfflinePkFriendListVo offlinePkFriendListVo = new OfflinePkFriendListVo();
//        List<ZnsUserEquipmentEntity> znsUserEquipmentEntities = znsUserEquipmentService.list(new QueryWrapper<ZnsUserEquipmentEntity>().eq("user_id",userId).eq("is_delete",YesNoStatus.NO.getCode()));
//        List<Integer> equipments = znsUserEquipmentEntities.stream().map(ZnsUserEquipmentEntity::getEquipmentType).distinct().collect(Collectors.toList());
        List<FriendSimpleInfoVO> list = znsUserFriendService.getActiveUserForOfflinePk(userId);

        list = list.stream().sorted(Comparator.comparing(FriendSimpleInfoVO::getRunDayCount, Comparator.nullsFirst(Integer::compareTo)).reversed().thenComparing(FriendSimpleInfoVO::getRunMileageBigDecimal, Comparator.nullsFirst(BigDecimal::compareTo))).collect(Collectors.toList());
        offlinePkFriendListVo.setList(list);
        return offlinePkFriendListVo;
    }

    public OfflinePkMatchOpponentUserListVo randomUserForOfflinePkMatch(Long userId, Integer pkTime, Map<String, Object> jsonObject) {
        OfflinePkMatchOpponentUserListVo offlinePkMatchOpponentUserListVo = new OfflinePkMatchOpponentUserListVo();
        Integer configPkTime = MapUtil.getInteger(jsonObject.get("everyDayPkRandomTime"));
        UserEquipmentQuery query = UserEquipmentQuery.builder().userId(userId).build();
        List<ZnsUserEquipmentEntity> znsUserEquipmentEntities = znsUserEquipmentService.findList(query);
        List<Integer> equipments = znsUserEquipmentEntities.stream().map(ZnsUserEquipmentEntity::getEquipmentType).distinct().collect(Collectors.toList());
        // 超过次数
        if (pkTime >= configPkTime) {
            List<OfflinePkMatchOpponentUserBeanVo> list = znsUserService.randomUserForOfflinePkMatch(userId, equipments, 1);
            offlinePkMatchOpponentUserListVo.setList(ListUtils.getRandomElements(list, 6));
            // 优化没有好友条件下随机
            if (ListUtils.getRandomElements(list, 6).isEmpty()) {
                List<OfflinePkMatchOpponentUserBeanVo> otherList = znsUserService.randomUserForOfflinePkMatch(userId, equipments, 0);
                offlinePkMatchOpponentUserListVo.setList(ListUtils.getRandomElements(otherList, 6));
            }
        } else {
            List<OfflinePkMatchOpponentUserBeanVo> list = znsUserService.randomUserForOfflinePkMatch(userId, equipments, 0);
            offlinePkMatchOpponentUserListVo.setList(ListUtils.getRandomElements(list, 6));
        }
        return offlinePkMatchOpponentUserListVo;
    }

    public List<OfflinePkRankVo> rankList() {
        List<OfflinePkRankVo> offlinePkRankVos = new ArrayList<>();
        try {
            Object o = redisTemplate.opsForValue().get(RedisConstants.OFFLINE_PK_RANK);
            if (Objects.nonNull(o)) {
                offlinePkRankVos = JsonUtil.readList(o.toString(), OfflinePkRankVo.class);

            } else {
                offlinePkRankVos = trainingOfflinePkRecordService.getPkRankList();
            }
        } catch (Exception e) {
            log.error("OfflinePkRankVo缓存获取异常", e);
            offlinePkRankVos = trainingOfflinePkRecordService.getPkRankList();
        }
        for (OfflinePkRankVo offlinePkRankVo : offlinePkRankVos) {
            offlinePkRankVo.setFlag(CountryFlagConstant.FlagMap.get(offlinePkRankVo.getCountry()));
            ZnsUserAccountEntity userAccount = znsUserAccountService.getUserAccount(Long.valueOf(offlinePkRankVo.getUserId()));
            offlinePkRankVo.setCurrency(I18nConstant.buildCurrency(userAccount.getCurrencyCode()));
        }
        return offlinePkRankVos;

    }

    /**
     * @param userId
     * @return
     */
    public Integer getUserTimes(Long userId) {
        ZonedDateTime date = ZonedDateTime.now();
        String key = RedisConstants.OFFLINE_PK_USER_TIMES + DateUtil.formatDate(date, DateUtil.YYYYMMDD) + ":" + userId;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            Object object = redisTemplate.opsForValue().get(key);
            return Integer.valueOf((String) object);
        } else {
            ZnsRunActivityConfigEntity znsRunActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.COURSE_OFFLINE_PK.getType(), null);
            Map<String, Object> jsonObject = JsonUtil.readValue(znsRunActivityConfig.getActivityConfig());
            Integer everyDayPkRandomTime = MapUtil.getInteger(jsonObject.get("everyDayPkRandomTime"));
            redisTemplate.opsForValue().set(key, String.valueOf(everyDayPkRandomTime));
            return everyDayPkRandomTime;
        }
    }

    /**
     * 减少用户次数
     *
     * @param userId
     * @return
     */
    public void decrUserTimes(Long userId) {
        ZonedDateTime date = ZonedDateTime.now();
        String key = RedisConstants.OFFLINE_PK_USER_TIMES + DateUtil.formatDate(date, DateUtil.YYYYMMDD) + ":" + userId;
        Object object = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(object)) {
            if (Integer.parseInt(object.toString()) == 0) {
                log.info("次数到底减不了");
            } else {
                redisTemplate.opsForValue().decrement(key);
            }
        }
    }

    /**
     * 增加用户次数
     *
     * @param userId
     * @return
     */
    @RedisLock(value = "userId", isTry = true)
    public void incrUserTimes(Long userId) {
        ZonedDateTime date = ZonedDateTime.now();
        String key = RedisConstants.OFFLINE_PK_USER_TIMES + DateUtil.formatDate(date, DateUtil.YYYYMMDD) + ":" + userId;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            ZnsRunActivityConfigEntity znsRunActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.COURSE_OFFLINE_PK.getType(), null);
            Map<String, Object> jsonObject = JsonUtil.readValue(znsRunActivityConfig.getActivityConfig());
            Integer everyDayPkAllTime = MapUtil.getInteger(jsonObject.get("everyDayPkAllTime"));
            Integer count = trainingOfflinePkRecordService.selectUserTodayTime(userId);
            Object object = redisTemplate.opsForValue().get(key);
            // 剩余数量+已经消耗的数量 <= 配置总量
            int remainingCount = Integer.parseInt(object.toString());
            if ((count + remainingCount) >= everyDayPkAllTime) {
                throw new BaseException(I18nMsgUtils.getMessage("user.friend.add.limit"), 500);
            } else {
                redisTemplate.opsForValue().increment(key);
            }
        }
    }

    public Integer pkFriendAdd(ZnsUserEntity loginUser, FriendIdRequest request) {
        Integer relationType = null;
        try {
            incrUserTimes(loginUser.getId());
            relationType = znsUserFriendService.add(loginUser.getId(), request.getFriendId());
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("pkFriendAdd error", e);
            throw e;
        }
        return relationType;
    }


    /**
     * 修改训练场
     *
     * @param offlinePkResultVo
     * @param userId
     * @param opponentUserId
     * @param pkTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePkInfo(OfflinePkResultVo offlinePkResultVo, Long userId, Long opponentUserId, Integer pkTime) {
        BigDecimal award = offlinePkResultVo.getAward();
        Integer score = offlinePkResultVo.getScore();
        savePkResult(userId, offlinePkResultVo, opponentUserId, pkTime);
        long winCount = trainingOfflinePkRecordService.findWinCount(userId);


        TrainingUserInfo one = trainingUserInfoService.findByUserId(userId);
        boolean flag1 = Objects.nonNull(award) && award.compareTo(BigDecimal.ZERO) > 0;
        boolean flag2 = Objects.nonNull(score) && score > 0;
        if (Objects.isNull(one)) {
            TrainingUserInfo trainingUserInfo = new TrainingUserInfo();
            trainingUserInfo.setUserId(userId);
            trainingUserInfo.setPkAllAward(award);
            trainingUserInfo.setPkTotalScore(score);
            trainingUserInfo.setPkAllTime(1);
            if (offlinePkResultVo.getPkResult() != null && offlinePkResultVo.getPkResult() == 1) {
                trainingUserInfo.setPkWinRate(new BigDecimal(1).setScale(2));
            } else {
                trainingUserInfo.setPkWinRate(new BigDecimal(0).setScale(2));
            }
            //trainingUserInfo.setPkWinRate(new BigDecimal(1).divide(new BigDecimal(1)).setScale(2));
            trainingUserInfoService.save(trainingUserInfo);
        } else {
            if (flag1) {
                one.setPkAllAward(one.getPkAllAward().add(award));
            }
            if (flag2) {
                one.setPkTotalScore(one.getPkTotalScore() + score);
            }
            one.setPkAllTime(one.getPkAllTime() + 1);
            one.setPkWinRate((new BigDecimal(winCount).divide(new BigDecimal(one.getPkAllTime()), 2, BigDecimal.ROUND_HALF_UP)));
            trainingUserInfoService.update(one);
        }
        if (offlinePkResultVo.getPkResult() == 1) {
            if (flag1) {
                znsUserAccountService.increaseAmount(award, userId, true);
                userAccountDetailService.addAccountDetail(userId, 1, AccountDetailTypeEnum.OFFLINE_PK_AWARD, AccountDetailSubtypeEnum.OFFLINE_PK_AWARD, award, null, null);
            }
            if (flag2) {
                // 数据pk不属于活动主表的，用id=-7占位
                activityUserScoreService.increaseAmount(score, -7L, userId, 1, 0, ScoreConstant.SourceTypeEnum.source_type_15.getType());
            }
        }
    }

    private void savePkResult(Long userId, OfflinePkResultVo offlinePkResultVo, Long opponentUserId, Integer pkTime) {
        TrainingOfflinePkRecord trainingOfflinePkRecord = new TrainingOfflinePkRecord();
        trainingOfflinePkRecord.setUserId(userId);
        trainingOfflinePkRecord.setOpponentUserId(opponentUserId);
        trainingOfflinePkRecord.setPkResult(offlinePkResultVo.getPkResult());
        trainingOfflinePkRecord.setAward(offlinePkResultVo.getAward());
        trainingOfflinePkRecord.setScore(offlinePkResultVo.getScore());
        trainingOfflinePkRecord.setCurrencyCode(znsUserAccountService.getUserAccount(userId).getCurrencyCode());
        if (pkTime <= 2) {
            trainingOfflinePkRecord.setPkType(1);
        } else {
            trainingOfflinePkRecord.setPkType(2);
        }
        trainingOfflinePkRecordService.save(trainingOfflinePkRecord);
    }
}
