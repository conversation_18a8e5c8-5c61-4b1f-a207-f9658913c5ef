package com.linzi.pitpat.api.activityservice.dto.response;

import lombok.Data;

import java.util.List;

/**
 * @since 4.8.0
 */
@Data
public class GameLaActivityRunDataResponseDto {

    /**
     * 超越目标
     */
    private GameActivityUserDataDto exceedTargetUserInfo;
    /**
     * 排行榜用户信息list
     */
    private List<GameActivityUserDataDto> rankUserInfoList;
    /**
     * 统一起跑时间 s
     */
    private Integer startingWaitingTime = 30;

}
