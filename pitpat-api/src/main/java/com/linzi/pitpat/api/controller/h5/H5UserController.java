/*

 * @description:

 *

 * projectName: pitpat-server

 * fileName: H5UserController.java

 * date 2021-10-15

 * copyright(c) 2018-2020 杭州霖扬网络科技有限公司版权所有

 */

package com.linzi.pitpat.api.controller.h5;


import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.api.awardservice.dto.response.UserWearsInfoResponse;
import com.linzi.pitpat.api.awardservice.manager.UserWearsBusiness;
import com.linzi.pitpat.api.bussiness.AppUserBussiness;
import com.linzi.pitpat.api.dto.request.AddEnergyRequestDto;
import com.linzi.pitpat.api.dto.response.achievement.AchievementRespDto;
import com.linzi.pitpat.api.userservice.dto.request.EmailBindReqDto;
import com.linzi.pitpat.api.userservice.dto.request.EmailReqDto;
import com.linzi.pitpat.api.userservice.dto.request.UserAppRegisterReqDto;
import com.linzi.pitpat.api.userservice.manager.UserManager;
import com.linzi.pitpat.api.userservice.manager.UserRegisterApiManager;
import com.linzi.pitpat.api.userservice.manager.UserVipMananger;
import com.linzi.pitpat.api.userservice.manager.UserVoteBusiness;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.CommonConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.config.PitpatConfig;
import com.linzi.pitpat.data.entity.dto.MailDto;
import com.linzi.pitpat.data.entity.dto.user.SendVerificationCodeDto;
import com.linzi.pitpat.data.messageservice.service.ZnsMessageService;
import com.linzi.pitpat.data.request.ResetPasswordReq;
import com.linzi.pitpat.data.resp.UserMonthDataResp;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.EmailConfig;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.ZnsCountryEntity;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.EmailConfigService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.userservice.dto.request.GetWearsInformationPo;
import com.linzi.pitpat.data.userservice.dto.request.UserMonthDataDto;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserVipInfoAppDto;
import com.linzi.pitpat.data.userservice.dto.request.UserVoteReq;
import com.linzi.pitpat.data.userservice.dto.response.UserVoteResp;
import com.linzi.pitpat.data.userservice.dto.response.UserYearDataResp;
import com.linzi.pitpat.data.userservice.dto.response.VipRightReponseDto;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.manager.UserAccountUpgradeBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserLogoffLogEntity;
import com.linzi.pitpat.data.userservice.model.query.UserLogoffLogQuery;
import com.linzi.pitpat.data.userservice.model.vo.AppBaseInfoVo;
import com.linzi.pitpat.data.userservice.service.UserYearCountService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLogoffLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.LoginVO;
import com.linzi.pitpat.data.vo.user.UserVipInfoAppVo;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * h5用户服务类
 * description:
 *
 * <AUTHOR>
 * <p>
 * className H5UserController
 * <p>
 * version V1.0
 * @date 2021-10-15
 **/
@RestController
@RequestMapping("/h5/user")
@Slf4j
@RequiredArgsConstructor
public class H5UserController extends BaseH5Controller {

    @Resource
    private RedisUtil redisUtil;
    private final RedissonClient redissonClient;
    @Resource
    private UserAccountUpgradeBizService userAccountUpgradeBizService;
    @Resource
    private ZnsUserService userService;
    @Resource
    private AreaService areaService;
    @Resource
    private ZnsCountryService znsCountryService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ZnsUserLogoffLogService userLogoffLogService;
    @Resource
    private PitpatConfig config;
    @Resource
    private ZnsUserEmailSendingRecordService znsUserEmailSendingRecordService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private AppUserBussiness appUserBussiness;
    @Resource
    private ZnsMessageService messageService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private UserVipMananger userVipMananger;
    @Value("${zns.config.rabbitQueue.emailSend}")
    private String mailSend;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private UserWearsBusiness userWearsBusiness;
    @Resource
    private UserYearCountService userYearCountService;

    @Resource
    private UserVoteBusiness userVoteBusiness;
    @Resource
    private ClubMemberService clubMemberService;

    @Resource
    private UserManager userManager;
    @Resource
    private UserRegisterApiManager userRegisterApiManager;
    @Resource
    private EmailConfigService emailConfigService;

    /**
     * 重置密码
     *
     * @param body 请求参数
     * @return String
     */
    @PostMapping("/resetPassword")
    @SuppressWarnings("all")
    public Result resetPassword(@RequestBody ResetPasswordReq req) {
        String emailAddress = req.getEmailAddress().toLowerCase();
        String password = req.getPassword();
        String key = req.getKey();
        if (!StringUtils.hasText(emailAddress)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.register.email.address"));
        }
        if (!StringUtils.hasText(password)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.enter.password"));
        }
        if (!StringUtils.hasText(key) || key.length() >= 50) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.enter.key"));
        }
        String lockKey = ApiConstants.RESET_PWD_LOCK + emailAddress;
        RLock lock = redissonClient.getLock(lockKey);
        return LockHolder.tryLock(lock, 1, 20, () -> {
            String countKey = ApiConstants.RESET_PASSWORD_INVALID_COUNT_KEY + ZonedDateTimeUtil.format(ZonedDateTime.now(), ZonedDateTimeUtil.PATTERN_DATE) + "_" + emailAddress;
            RedisAtomicInteger rai = new RedisAtomicInteger(countKey, redisTemplate.getConnectionFactory());
            //设置1天的有效期，过期释放key
            redisTemplate.expire(countKey, 1L, TimeUnit.DAYS);
            if (rai.get() >= 150) {
                return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.verification.frequent"));
            }
            ZnsUserEntity user = userService.findByEmail(emailAddress);
            if (user == null) {
                log.info("reset password, email is not exist");
                rai.incrementAndGet();
                return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.password.expired"));
            }
            String tokenKey = ApiConstants.SEND_MAIL_TOKEN_KEY + key;
            String redisValue = redisUtil.get(tokenKey);
            if (!Objects.equals(redisValue, emailAddress)) {
                rai.incrementAndGet();
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.account.password.expired"));
            }
            boolean b = userService.resetPassword(emailAddress, password);
            if (b) {
                //重置密码成功，key失效
                redisUtil.delete(countKey);
                redisUtil.delete(tokenKey);
            }
            return CommonResult.success();
        });
    }

    /**
     * 是否可以注销
     *
     * @return
     */
    @PostMapping("/canLogoff")
    public Result canLogoff() {
        ZnsUserEntity user = getLoginUser();

        //查询金额是否大于0
        ZnsUserAccountEntity account = userAccountService.getUserAccount(user.getId());
        if (account.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            return CommonResult.fail(UserError.ACCOUNT_AMOUNT_EXISTENCE.getCode(), I18nMsgUtils.getMessage("userAccount.set.balance"));
        }
        //检查是否是俱乐部
        if (clubMemberService.existsInClub(user.getId())) {
            return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), I18nMsgUtils.getMessage("club.logoOff.should_exit_club"));
        }
        return CommonResult.success();
    }

    /**
     * 注销
     *
     * @param log
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/logoff")
    public Result logoff(@RequestBody ZnsUserLogoffLogEntity log, HttpServletRequest httpServletRequest) {
        if (!StringUtils.hasText(log.getRemark())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        ZnsUserEntity user = getLoginUser();

        //检查当日是否已申请
        UserLogoffLogQuery query = UserLogoffLogQuery.builder().userId(user.getId()).createTimeStart(DateUtil.getStartOfDate(ZonedDateTime.now())).build();
        ZnsUserLogoffLogEntity logEntity = userLogoffLogService.findByQuery(query);
        if (Objects.nonNull(logEntity)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.account.unregister"));
        }

        //检查是否是俱乐部
        if (clubMemberService.existsInClub(user.getId())) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("club.logoOff.should_exit_club"));
        }

        //提交注销申请
        ZnsUserLogoffLogEntity logoffLogEntity = new ZnsUserLogoffLogEntity();
        logoffLogEntity.setRemark(log.getRemark());
        logoffLogEntity.setUserId(user.getId());
        userLogoffLogService.insert(logoffLogEntity);

        //发送邮件到公司邮箱
        String title = "注销审核";
        String emailContent = String.format(CommonConstants.LOG_OFF_NOTIFICATION_TEMPLATE, user.getFirstName(), user.getEmailAddressEn(), DateTimeUtil.format(ZonedDateTime.now()), log.getRemark());
        String content = "<img style=\"margin-top: 20px;\" src=\"" + config.getMailIcon() + "\">\n" +
                "      <h2 style=\"font-size: 30px; color: #575767; line-height: 34px; text-align: justify; margin: 80px 0 40px;\">" + emailContent + "</h2>\n" +
                "      <h3 style=\"font-size: 24px; text-align: justify; line-height: 33px; font-weight: 600; color: #575767; margin-top: 10px;\">PitPat Team</h3>";
        try {

            String uuid = httpServletRequest.getHeader("uuid");
            EmailConfig emailConfig = emailConfigService.getByEmailSuffix("default", 2);
            ZnsUserEmailSendingRecordEntity sendingRecord = znsUserEmailSendingRecordService.insertNewRecord(emailConfig.getSendEmail(), "注销审核", title, content, uuid, 1);
            MailDto mailDto = new MailDto(emailConfig.getSendEmail(), content, title, sendingRecord);
            rabbitTemplate.convertAndSend(mailSend, JsonUtil.writeString(mailDto));

            messageService.logoffNotification(user, log.getRemark(), emailConfig.getSendEmail());
        } catch (Exception e) {
            e.printStackTrace();
        }


        return CommonResult.success();
    }

    /**
     * 登录
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/login")
    public Result login(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        if (!StringUtils.hasText(request.getEmailAddress())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter the account");
        }
        if (!StringUtils.hasText(request.getPassword())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter the password");
        }
        userService.encapsulateUserRequest(request, httpServletRequest);
        Integer appVersion = getAppVersion();
        Result<LoginVO> result = userService.loginNew(request, httpServletRequest, appVersion);
        if (result.getData() != null && CommonError.SUCCESS.getCode().equals(result.getCode())) {
            //更新用户app基本信息
            userService.updateUserAppBaseInfo(getAppBaseInfo(), result.getData().getUserId());
            if (StringUtils.hasText(request.getEquipmentNo())) {
                userRegisterApiManager.userEquipmentShareBanding(request.getEquipmentNo(), result.getData().getUserId());
            }
        }
        return result;
    }

    /**
     * H5用户注册第二版
     *
     * @param req
     * @since 4.0
     */
    @PostMapping("/registerV2")
    public Result<LoginVO> registerV2(@RequestBody UserAppRegisterReqDto req) {
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        LoginVO loginVO = userRegisterApiManager.emailRegister(req, appBaseInfo);
        return CommonResult.success(loginVO);
    }

    /**
     * 注册
     *
     * @param request
     * @param httpServletRequest
     * @see H5UserController#registerV2(UserAppRegisterReqDto)
     */
    @PostMapping("/register")
    public Result register(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        Result checkParam = userService.registerCheckParam(request);
        if (checkParam != null) {
            return checkParam;
        }
        Integer appVersion = getAppVersion();

        //填充国家
        String countryName = I18nConstant.CountryCodeEnum.US.enName;
        String countryCode = I18nConstant.CountryCodeEnum.US.code;
        if (StringUtils.hasText(request.getCountryCode())) {
            ZnsCountryEntity countryEntity = znsCountryService.findByCountryCode(request.getCountryCode());
            if (countryEntity != null) {
                countryName = countryEntity.getName();
                countryCode = countryEntity.getCode();
            }
        }
        request.setCountry(countryName);
        request.setCountryCode(countryCode);

        //填充州
        String stateCode = I18nConstant.CountryCodeEnum.US.defaultStateCode;
        String stateName = I18nConstant.CountryCodeEnum.US.defaultState;
        if (StringUtils.hasText(request.getStateCode())) {
            AreaEntity areaEntity = areaService.selectByAreaCode(request.getStateCode());
            if (areaEntity != null) {
                stateName = areaEntity.getAreaName();
                stateCode = areaEntity.getAreaCode();
            }
        }
        request.setState(stateName);
        request.setStateCode(stateCode);

        userService.encapsulateUserRequest(request, httpServletRequest);

        Result result = userService.register(request, appVersion);
        return result;
    }


    /**
     * 发送找回密码邮件
     *
     * @param request
     * @return
     */
    @SuppressWarnings("all")
    @PostMapping("/forgetPassword")
    public Result forgetPassword(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        return appUserBussiness.forgetPassword(request, httpServletRequest);
    }

    /**
     * 发送绑定邮箱邮件
     *
     * @param req
     * @return
     */
    @PostMapping("/sendBindEmailCode")
    public Result<SendVerificationCodeDto> sendBindEmailCode(@RequestBody EmailReqDto req) {
        SendVerificationCodeDto dto = userRegisterApiManager.sendBindEmailCode(req.getEmailAddress());
        Result<SendVerificationCodeDto> result = CommonResult.success(dto);
        result.setMsg(I18nMsgUtils.getMessage("user.send.success"));
        return result;
    }

    /**
     * 绑定邮箱
     *
     * @param req
     * @return
     */
    @PostMapping("/bindEmail")
    public Result<Boolean> bindEmail(@RequestBody EmailBindReqDto req) {
        ZnsUserEntity loginUser = getLoginUser();
        userRegisterApiManager.bindEmail(req, loginUser);
        return CommonResult.success(true);
    }

    /**
     * 登录获取用户信息
     *
     * @return
     */
    @PostMapping("/getInfo")
    public Result<LoginVO> getInfo() {
        ZnsUserEntity user = getLoginUser();
//		if (Objects.isNull(user)) {
//			return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), CommonError.NEED_LOGIN.getMsg());
//		}
        Integer appVersion = getAppVersion();
        String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
        String token = redisUtil.get(key);
        LoginVO loginVO = userService.encapsulateUserData(user.getId(), token, true, false, appVersion);
        SysConfig LifetimeMember = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.LIFETIME_MEMBER.getCode());
        String configValue = LifetimeMember.getConfigValue();
        List<String> list = JsonUtil.readList(configValue, String.class);
        //判断当前用户账号是否终生会员
        if (list.contains(user.getEmailAddressEn())) {
            loginVO.setIsLifetimeMember(1);
        } else {
            loginVO.setIsLifetimeMember(0);
        }
        return CommonResult.success(loginVO);
    }

    /**
     * 注册发送验证码
     *
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/sendVerificationCode")
    public Result sendVerificationCode(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        return appUserBussiness.sendVerificationCode(request, httpServletRequest);
    }

    /**
     * 获取穿戴信息
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/getWearsInformation")
    public Result<UserWearsInfoResponse> getWearsInformation(@RequestBody GetWearsInformationPo request, HttpServletRequest httpServletRequest) {
        return userWearsBusiness.getUserWears(request.getUserId(), request.getRoomId(), request.getActivityId(), httpServletRequest);
    }


    /**
     * 会员页信息
     *
     * @return
     */
    @PostMapping("/vipInfo")
    public Result<UserVipInfoAppVo> findById(@RequestBody @Validated UserVipInfoAppDto request) {
        return CommonResult.success(userVipMananger.findById(request.getUserId()));
    }

    /**
     * 会员权益规则
     *
     * @return
     */
    @PostMapping("/vipRule")
    public Result<VipRightReponseDto> vipRule() {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(userVipMananger.getVipRightRule(loginUser.getId()));
    }


    /**
     * 用户手动激活会员
     *
     * @return
     */
    @PostMapping("/promoteVip")
    public Result<UserVipInfoAppVo> updateById(@RequestBody @Validated UserVipInfoAppDto request) {
        return CommonResult.success(userVipMananger.updateById(request.getUserId(), request.getVipUserId()));
    }

    /**
     * 获取月报数据
     *
     * @return
     */
    @PostMapping("/monthData")
    public Result<UserMonthDataResp> monthData(@RequestBody UserMonthDataDto dto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (StringUtils.isEmpty(dto.getMonth())) {
            ZonedDateTime now = DateUtil.getNowDate();
            Integer year = now.getYear();
            Integer month = now.getMonth();
            dto.setMonth(year + "-" + month);
        }
        UserMonthDataResp resp = userService.getMothData(dto.getMonth(), loginUser);
        if (Objects.nonNull(resp)) {
            resp.setHeadPortrait(loginUser.getHeadPortrait());
        }
        return CommonResult.success(resp);
    }


    /**
     * 年度报告
     *
     * @return
     */
    @PostMapping("/yearData")
    public Result<UserYearDataResp> yearData() {
        ZnsUserEntity loginUser = getLoginUser();
        UserYearDataResp userYearDataResp = userYearCountService.yearData(loginUser);
        return CommonResult.success(userYearDataResp);
    }


    /**
     * 用户活动团队投票
     *
     * @param req
     * @return
     */
    @PostMapping("/activity/vote")
    public Result<UserVoteResp> userActivityVote(@Validated @RequestBody UserVoteReq req) {
        Long userId = getLoginUser().getId();
        Boolean flag = userVoteBusiness.userActivityVote(userId, req);
        return CommonResult.success(UserVoteResp.builder().flag(flag).build());
    }

    /**
     * 个人主页统计
     */
    @PostMapping("/achievementStatistic")
    public Result<AchievementRespDto> achievementStatistic() {
        AchievementRespDto respDto = userManager.achievementStatistic(getLoginUser());
        return CommonResult.success(respDto);
    }

    /**
     * 增加能量助力
     */
    @PostMapping("/addEnergy")
    public Result<Void> addEnergy(@Validated @RequestBody AddEnergyRequestDto req) {
        return CommonResult.success();
    }

}
