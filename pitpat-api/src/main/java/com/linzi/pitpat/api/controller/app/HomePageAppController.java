package com.linzi.pitpat.api.controller.app;

import com.google.api.client.util.Lists;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityNewPersonTypeRequestDto;
import com.linzi.pitpat.api.activityservice.manager.AppNewPersonPkPageManager;
import com.linzi.pitpat.api.activityservice.manager.PropUserRankedLevelManager;
import com.linzi.pitpat.api.bussiness.AppConfigPopManager;
import com.linzi.pitpat.api.dto.UserMedalDto;
import com.linzi.pitpat.api.dto.response.BattlePassAndTbInfoDto;
import com.linzi.pitpat.api.dto.vo.AppConfigPopVo;
import com.linzi.pitpat.api.dto.vo.HalfScreenPopVo;
import com.linzi.pitpat.api.dto.vo.HomeSystemPopDto;
import com.linzi.pitpat.api.dto.vo.HomeUserPopDto;
import com.linzi.pitpat.api.dto.vo.LimitedEditionPopVo;
import com.linzi.pitpat.api.dto.vo.MedalPopVo;
import com.linzi.pitpat.api.dto.vo.NewOrderPopVo;
import com.linzi.pitpat.api.dto.vo.NewPersonPopVo;
import com.linzi.pitpat.api.dto.vo.NewUserClubPopVo;
import com.linzi.pitpat.api.dto.vo.NewUserPkPopVo;
import com.linzi.pitpat.api.mananger.TbAndBattlePassManager;
import com.linzi.pitpat.api.userservice.manager.ExpManager;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.NewPersonPkBizService;
import com.linzi.pitpat.data.activityservice.biz.UserRankedLevelBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MatchTypeEnum;
import com.linzi.pitpat.data.activityservice.manager.api.NewPersonPkManager;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkVo;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.MedalI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsLog;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserWearsEntity;
import com.linzi.pitpat.data.awardservice.model.query.MedalI18nQuery;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.MedalI18nService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagLogService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.UserWearsLogService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserWearsService;
import com.linzi.pitpat.data.bussiness.home.HomepageBussiness;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.PopTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.AppVersionUpDto;
import com.linzi.pitpat.data.systemservice.dto.response.HomePageCustomizeAppResponseDto;
import com.linzi.pitpat.data.systemservice.dto.response.MedalDto;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.dto.response.VersionPageRespDto;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.HalfPopConfigH5;
import com.linzi.pitpat.data.systemservice.model.entity.HalfPopConfigI18n;
import com.linzi.pitpat.data.systemservice.model.entity.PopRecord;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.query.HalfPopQuery;
import com.linzi.pitpat.data.systemservice.model.query.HalfScreenCouponClaimReq;
import com.linzi.pitpat.data.systemservice.model.query.HalfScreenReq;
import com.linzi.pitpat.data.systemservice.model.vo.HalfScreenCouponClaimResp;
import com.linzi.pitpat.data.systemservice.model.vo.HomePageV2Po;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.HalfPopConfigH5Service;
import com.linzi.pitpat.data.systemservice.service.HalfPopConfigI18nService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.manager.api.UserTaskManager;
import com.linzi.pitpat.data.userservice.model.entity.ExpUser;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserLoginLogEntity;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserRelatedService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.vo.AppNewPersonPkPageConfigVo;
import com.linzi.pitpat.data.vo.BluePop;
import com.linzi.pitpat.data.vo.RobotBean;
import com.linzi.pitpat.data.vo.RobotSpeed;
import com.linzi.pitpat.data.vo.UserExpDetailDto;
import com.linzi.pitpat.data.vo.UserExpDto;
import com.linzi.pitpat.data.vo.UserFriendMatchPushVo;
import com.linzi.pitpat.data.vo.UserGuideVO;
import com.linzi.pitpat.data.vo.UserGuidedPageRouteVO;
import com.linzi.pitpat.data.vo.home.HomeNewUserTaskStatusVo;
import com.linzi.pitpat.data.vo.home.HomePageUserInfoRespDto;
import com.linzi.pitpat.data.vo.home.HomePageV3Vo;
import com.linzi.pitpat.data.vo.home.HomePageV4Vo;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 首页相关
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@Slf4j
@RequestMapping({"/app", "/h5/home"})
public class HomePageAppController extends BaseAppController {
    private static ZonedDateTime newUserVersion = DateTimeUtil.parse("2023-06-26 00:00:00");
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsUserService userService;
    @Resource
    private ZnsUserFriendService userFriendService;
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private UserMedalService userMedalService;
    @Autowired
    private MedalI18nService medalI18nService;
    @Autowired
    private UserFriendMatchService userFriendMatchService;
    @Autowired
    private ExpUserService expUserService;
    @Resource
    private PopRecordService popRecordService;
    @Autowired
    private MedalConfigService medalConfigService;
    @Autowired
    private ZnsUserService znsUserService;
    @Resource
    private RedisUtil redisUtil;
    @Value("${game.server.domain}")
    private String gameUrl;
    @Resource
    private AppUpgradeService appUpgradeService;
    @Value("${spring.profiles.active}")
    private String profile;
    @Autowired
    private ZnsRunActivityService znsRunActivityService;
    @Resource
    private HomepageBussiness homepageBussiness;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ZnsUserLoginLogService userLoginLogService;
    @Resource
    private UserWearsBagService userWearsBagService;
    @Resource
    private UserWearsBagLogService userWearsBagLogService;
    @Resource
    private NewPersonPkBizService newPersonPkBizService;
    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    @Resource
    private HalfPopConfigH5Service halfPopConfigH5Service;

    @Resource
    private UserCouponService userCouponService;

    @Resource
    private AppConfigPopManager appConfigPopManager;

    @Resource
    private AppRouteService appRouteService;
    @Resource
    private ZnsUserLoginLogService loginLogService;
    @Resource
    private UserRankedLevelBizService userRankedLevelBizService;
    @Resource
    private PropUserRankedLevelManager propUserRankedLevelManager;
    @Resource
    private HalfPopConfigI18nService halfPopConfigI18nService;
    @Resource
    private WearsService wearsService;

    @Resource
    private AppNewPersonPkPageManager appNewPersonPkPageManager;
    @Resource
    private UserWearsLogService userWearsLogService;
    @Resource
    private ZnsUserWearsService znsUserWearsService;
    @Resource
    private NewPersonPkManager newPersonPkManager;
    @Resource
    private UserCouponManager userCouponManager;

    @Resource
    private UserLevelService userLevelService;

    @Resource
    private UserLevelRuleService userLevelRuleService;

    @Value("${user.level.exp.ratio:5}")
    private Integer userLevelExpRatio;

    @Resource
    private ExpManager expManager;
    @Resource
    private UserRelatedService userRelatedService;

    @Resource
    private TbAndBattlePassManager tbAndBattlePassManager;
    @Resource
    private UserTaskManager userTaskManager;
    @Resource
    private UserExtraService userExtraService;

    @Resource
    private UserIdentityService userIdentityService;

    /**
     * 获取里程碑开关游戏配置
     *
     * @return
     */
    @PostMapping("/getBattlePassAndTbInfo")
    public Result<BattlePassAndTbInfoDto> getBattlePassAndTbInfo() {
        BattlePassAndTbInfoDto dto = tbAndBattlePassManager.getBattlePassAndTbInfo(getLoginUser(), getAppVersion());
        return CommonResult.success(dto);
    }


    /**
     * 是否开启新等级判断
     *
     * @return
     */
    @PostMapping("/enableUserNewLevel")
    public Result<Boolean> enableUserNewLevel() {
        return CommonResult.success(sysConfigService.enableUserNewLevel(getUserId()));
    }

    /**
     * 首页弹窗
     * todo 下版本删除
     *
     * @param medalDto
     * @return
     */
    @PostMapping("/home/<USER>")
    public Result match(@RequestBody MedalDto medalDto) {
        ZnsUserEntity loginUser = znsUserService.findById(medalDto.getUserId());
        List<UserMedal> userMedalList = userMedalService.selectUserMedalByUserIdObtainIsPop(medalDto.getUserId(), 1, 0, 0);
        Map<Long, MedalConfig> medalConfigMap = medalConfigService.selectAllMedalConfig();
        List<UserMedalDto> userMedalDtos = new ArrayList<>();
        String languageCode = getLanguageCode();
        for (UserMedal userMedal : userMedalList) {
            UserMedalDto userMedalDto = new UserMedalDto();
            BeanUtils.copyProperties(userMedal, userMedalDto);
            if (Objects.nonNull(medalConfigMap)) {
                MedalConfig medalConfig = medalConfigMap.get(userMedal.getMedalConfigId());
                if (Objects.nonNull(medalConfig)) {
                    userMedalDto.setUrl(medalConfig.getUrl());
                    // 查询国际化数据
                    MedalI18n medalI18n = medalI18nService.findByQuery(MedalI18nQuery.builder().medalId(userMedal.getMedalConfigId()).langCode(languageCode).defaultLangCode(medalConfig.getDefaultLangCode()).build());
                    userMedalDto.setName(Objects.nonNull(medalI18n) ? medalI18n.getName() : medalConfig.getName());
                    userMedalDto.setRemark(Objects.nonNull(medalI18n) ? medalI18n.getRemark() : medalConfig.getRemark());
                }
            }
            userMedalDtos.add(userMedalDto);
            userMedalService.updateUserMedalIsPopByUserId(1, userMedal.getId());
        }
        //获取用户在里程碑赛跑中的获取的服装奖励（未弹窗）
        List<UserWearsBagLog> userWearsBags = userWearsBagLogService.listUserWearsByUserIdAndIsNew(medalDto.getUserId(), 1);
        List<UserMedalDto> wearsAwardList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userWearsBags)) {
            //排序，最新的获取的最靠前
            wearsAwardList = userWearsBags.stream().sorted((o1, o2) -> o2.getSortNum() - o1.getSortNum()).map(userWearsBag -> {
                UserWearsBag byId = userWearsBagService.findByById(userWearsBag.getBagId());
                UserMedalDto userMedalDto = new UserMedalDto();
                if (StringUtils.hasText(byId.getWearImageUrl())) {
                    userMedalDto.setUrl(byId.getWearImageUrl());
                } else {
                    Wears wear = wearsService.getWearByWearIdAndType(byId.getWearType(), byId.getWearValue());
                    if (Objects.equals(loginUser.getGender(), 2)) {
                        userMedalDto.setUrl(wear.getWomenWearUrl());
                    } else {
                        userMedalDto.setUrl(wear.getMenWearUrl());
                    }
                }
                userMedalDto.setId(userWearsBag.getId());
                userMedalDto.setName(byId.getWearName());
                userMedalDto.setIsPop(0);
                userMedalDto.setRemark(String.format("You have received %s", byId.getWearName()));
                userMedalDto.setExpiredTime(userWearsBag.getExpiredTime());
                return userMedalDto;
            }).collect(Collectors.toList());

            //更新标记
            userWearsBags.forEach(userWearsBag -> {
                userWearsBag.setIsNew(YesNoStatus.NO.getCode());
                userWearsBag.setGmtModified(ZonedDateTime.now());
                userWearsBagLogService.updateById(userWearsBag);
            });
        }

        List<ExpUser> userExpList = expUserService.selectUserMedalByUserIdNotPop(medalDto.getUserId(), 0);
        for (ExpUser expUser : userExpList) {
            if (Objects.equals(expUser.getType(), 5)) {
                expUser.setExp(5);      //如果是每跑500m，最后弹窗是5 个经验值
            }
            ExpUser updateExpUser = new ExpUser();
            updateExpUser.setGmtModified(ZonedDateTime.now());
            updateExpUser.setIsPop(1);
            updateExpUser.setId(expUser.getId());
            expUserService.updateExpUserById(updateExpUser);

        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appType = request.getHeader("appType");
        String appVersion = request.getHeader("appVersion");
        AppVersionUpDto appDataVersion = appUpgradeService.selectUpgradeVersion(appType, appVersion);
        //部分用户强制升级判断处理
        appUpgradeService.forceUpgrade(appDataVersion, loginUser, appType, appVersion);
        List<UserFriendMatch> friendMatchList = userFriendMatchService.selectUserFriendMatchByMatchFriendIdStatusIsPopActivityStartTimeNeedPop(medalDto.getUserId(), 0, 0, ZonedDateTime.now(), 1);
        List<UserFriendMatchPushVo> userFriendMatchPushVos = new ArrayList<>();
        for (UserFriendMatch userFriendMatch : friendMatchList) {
            userFriendMatchPushVos.add(userFriendMatchService.buildUserFriendMatchPushVoV2(userFriendMatch));
        }
        List<Map<String, Object>> activityList = new ArrayList<>();
        //是否首次进入qpp
        ZnsUserLoginLogEntity lastLogin = loginLogService.getLastLogin(loginUser.getId());
        //首次弹窗
        ZonedDateTime now = ZonedDateTime.now();

        // 添加首页弹窗
        AppConfigPopVo homePageConfigPop = appConfigPopManager.getHomePageConfigPop(loginUser.getId());
        if (Objects.nonNull(homePageConfigPop)) {
            Map<String, Object> homePagePop = new HashMap<>();
            homePagePop.put("advertisingImage", homePageConfigPop.getPics());
            homePagePop.put("homePagePopId", homePageConfigPop.getId());
            // 组装跳转链接
            PrimitiveForest forest = BeanUtil.copyBean(homePageConfigPop, PrimitiveForest.class);
            forest.setUserId(loginUser.getId());
            forest.setJumpType(homePageConfigPop.getRedirectType());
            forest.setJumpUrl(homePageConfigPop.getActivityUrl());
            forest.setMainActivityType(homePageConfigPop.getMainActivityType());
            forest.setRunActivityId(homePageConfigPop.getRunActivityId());
            AppRoute route = appRouteService.findRoute(forest);
            homePagePop.put("url", route.getJumpUrl());
            homePagePop.put("jumpParam", route.getJumpParam());
            activityList.add(homePagePop);
        }

        Map<String, Object> data = new HashMap<>();
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.foo_activity_config.getCode());
        Map<String, Object> blueHatActivityConfigMap = JsonUtil.readValue(sysConfig.getConfigValue());
        Long blueActivityId = MapUtil.getLong(blueHatActivityConfigMap.get("activityId"), 0L);
        data.put("userMedalList", userMedalDtos);
        data.put("userExpList", userExpList);//3.8时app端反馈无该字段
        data.put("appDataVersion", appDataVersion);
        data.put("friendMatchList", userFriendMatchPushVos);
        data.put("activityList", activityList);
        data.put("wearsAwardList", wearsAwardList);

        BluePop bluePop = new BluePop();
        String blue_key = "blue_key_" + loginUser.getId();

        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(blueActivityId);
        if (ZonedDateTime.now().toInstant().toEpochMilli() > znsRunActivityEntity.getActivityStartTime().toInstant().toEpochMilli()
                && ZonedDateTime.now().toInstant().toEpochMilli() < znsRunActivityEntity.getActivityEndTime().toInstant().toEpochMilli()) {
            Object object1 = redisUtil.get(blue_key);
            if (object1 == null) {
                bluePop.setBlueIsPop(1);
                ZonedDateTime endOfDate = DateUtil.endOfDate(ZonedDateTime.now());
                long second = DateUtil.betweenSecond(ZonedDateTime.now(), endOfDate);
                log.info(" 活动 存储时间为 " + second);
                redisUtil.set(blue_key, "1", second, TimeUnit.SECONDS);
            }
        }
        bluePop.setBlueColor(blueHatActivityConfigMap.get("blueColor") + "");
        bluePop.setBluePopImage(blueHatActivityConfigMap.get("bluePopImage") + "");
        bluePop.setBlueUrl(blueHatActivityConfigMap.get("blueUrl") + "");


        Map<String, Object> jumpParam = new HashMap<>();
        jumpParam.put("activityType", 2);
        jumpParam.put("runPersonType", 2);
        jumpParam.put("fromType", 1);
        bluePop.setBlueText(blueHatActivityConfigMap.get("blueText") + "");

        bluePop.setJumpParam(JsonUtil.writeString(jumpParam));
        data.put("bluePop", bluePop);

        UserExpDto expDto = new UserExpDto();
        if (sysConfigService.enableUserNewLevel(loginUser.getId())) {
            UserLevel userLevel = userLevelService.findByUserId(loginUser.getId());
            UserExpDetailDto currentExpDetailDto = new UserExpDetailDto();
            currentExpDetailDto.setExp(userLevel.getExperience());
            currentExpDetailDto.setLevel(userLevel.getLevel());
            UserLevelRule currentRule = userLevelRuleService.findByLevel(userLevel.getLevel());
            currentExpDetailDto.setMin(currentRule.getExpLow());
            currentExpDetailDto.setMax(currentRule.getExpHigh());
            UserExpDetailDto historyExpDetailDto = new UserExpDetailDto();
            historyExpDetailDto.setExp(userLevel.getHistoryExperience());
            historyExpDetailDto.setLevel(userLevel.getHistoryLevel());
            UserLevelRule historyRule = userLevelRuleService.findByLevel(userLevel.getHistoryLevel());
            historyExpDetailDto.setMin(historyRule.getExpLow());
            historyExpDetailDto.setMax(historyRule.getExpHigh());
            expDto.setCurrentExp(currentExpDetailDto);
            expDto.setHistoryExp(historyExpDetailDto);
            Integer exp = userLevel.getExperience() - userLevel.getHistoryExperience();
            expDto.setExp(exp);
            if (Objects.equals(loginUser.getMemberType(), 1)) {
                expDto.setBasicExp(exp / userLevelExpRatio);
                expDto.setMemberBenefit(exp * (userLevelExpRatio - 1) / userLevelExpRatio);
            } else {
                expDto.setBasicExp(exp);
            }
//            userLevelService.updateHistoryUserExpByUserId(userLevel.getExperience(),loginUser.getId());
        } else {

            expDto.setHistoryExp(expUserService.getExpDetail(loginUser.getHistoryUserExp()));
            expDto.setCurrentExp(expUserService.getExpDetail(loginUser.getUserExp()));
            znsUserService.updateHistoryUserExpByUserId(loginUser.getUserExp(), loginUser.getId());
        }
        data.put("memberType", loginUser.getMemberType());
        data.put("userExp", expDto.getCurrentExp().getExp() - expDto.getHistoryExp().getExp() > 0 ? expDto : null);

        // 新卷弹窗
        PopRecord couponPopRecord = popRecordService.selectPopByDate(loginUser.getId(), now, PopTypeEnum.NEW_COUPON.getType(), 1);
        //获取新卷弹窗
        data.put("couponPop", Lists.newArrayList());
        if (Objects.isNull(couponPopRecord) && Objects.nonNull(lastLogin) && !Objects.equals(lastLogin.getLoginType(), 2)) {
            List<CouponPageVo> list = userCouponService.getUserNewCouponByUserId(loginUser.getId(), loginUser.getLanguageCode());
            if (!CollectionUtils.isEmpty(list)) {
                data.put("couponPop", list);
                popRecordService.addPop(loginUser.getId(), 3, 1, 0l);
            }
        }
        // 更新提醒弹窗
        AppConfigPopVo appConfigPopVo = appConfigPopManager.getRemindConfigPop(loginUser.getId());
        if (Objects.nonNull(appConfigPopVo)) {
            data.put("updateRemindPop", appConfigPopVo);
        }

        // 账户升级弹窗
        AppConfigPopVo accountUpdateConfigPop = appConfigPopManager.getAccountUpdateConfigPopV2(loginUser.getId());
        if (Objects.nonNull(accountUpdateConfigPop)) {
            data.put("accountUpdatePop", accountUpdateConfigPop);
        }

        // 会员过期提醒弹窗
        AppConfigPopVo vipPassRemindConfigPop = appConfigPopManager.getVipPassRemindConfigPop(loginUser.getId());
        if (Objects.nonNull(vipPassRemindConfigPop)) {
            data.put("vipPassRemindPop", vipPassRemindConfigPop);
        }


        //缓存判断是否有参加
        Object newUserOfflinePkRedis = redisTemplate.opsForValue().get(RedisConstants.NEW_USER_OFFLINE_PK_JOIN + loginUser.getId());
        Integer version = getAppVersion();
        if (Objects.nonNull(newUserOfflinePkRedis) && version < 4000) {
            //新人引导离线pk赛结果弹窗 4.0 以后不显示新人弹框
            PopRecord newUserOfflinePk = popRecordService.selectPopByDate(loginUser.getId(), null, PopTypeEnum.NEW_USER_OFFLINE_PK.getType(), 1);
            if (Objects.isNull(newUserOfflinePk)) {
                Long runDataDetailsId = Long.valueOf((String) newUserOfflinePkRedis);
                Map<String, Object> newUserOfflinePkPop = newPersonPkManager.getNewUserOfflinePkPop(runDataDetailsId);
                data.put("newUserOfflinePkPop", newUserOfflinePkPop);
                popRecordService.addPop(loginUser.getId(), PopTypeEnum.NEW_USER_OFFLINE_PK.getType(), 1, 0l);
            }
        }

        return CommonResult.success(data);
    }

    /**
     * 首页系统弹窗(强升弹窗，账户升级弹窗)
     *
     * @return
     */
    @PostMapping("/home/<USER>")
    public Result<HomeSystemPopDto> appVersionPop() {
        ZnsUserEntity loginUser = getLoginUser();
        HomeSystemPopDto homeSystemPopDto = new HomeSystemPopDto();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appType = request.getHeader("appType");
        String appVersion = request.getHeader("appVersion");
        if(userIdentityService.isNpc(loginUser.getId())){
            // npc 弹窗取消 4.7.0需求修改
            return CommonResult.success(homeSystemPopDto);
        }
        AppVersionUpDto appVersionUpDto = appUpgradeService.selectUpgradeVersion(appType, appVersion);
        //部分用户强制升级判断处理
        appUpgradeService.forceUpgrade(appVersionUpDto, loginUser, appType, appVersion);
        //强升弹窗
        homeSystemPopDto.setAppDataVersion(appVersionUpDto);
        // 账户升级弹窗
        homeSystemPopDto.setAccountUpdatePop(appConfigPopManager.getAccountUpdateConfigPopV2(loginUser.getId()));

        // 停服更新提醒弹窗
        AppConfigPopVo appConfigPopVo = appConfigPopManager.getRemindConfigPop(loginUser.getId());
        homeSystemPopDto.setUpdateRemindPop(appConfigPopVo);

        return CommonResult.success(homeSystemPopDto);
    }

    /**
     * 首页用户信息弹窗
     *
     * @return
     */
    @PostMapping("/home/<USER>")
    public Result<HomeUserPopDto> userExpPop() {
        ZnsUserEntity loginUser = getLoginUser();
        UserExpDto expDto = expManager.expPop(loginUser, true);
        HomeUserPopDto homeUserPopDto = new HomeUserPopDto();
        if (expDto.getCurrentExp().getExp() - expDto.getHistoryExp().getExp() > 0) {
            homeUserPopDto.setUserExpDto(expDto);
        }
        if (userExtraService.isPutChannelUser(loginUser.getId())) {
            //投流用户，其他弹窗
            return CommonResult.success(homeUserPopDto);
        }
        homeUserPopDto.setMemberType(loginUser.getMemberType());
        if(userIdentityService.isNpc(loginUser.getId())){
            // npc 弹窗取消 4.7.0需求修改
            return CommonResult.success(homeUserPopDto);
        }
        // 添加首页弹窗
        AppConfigPopVo homePageConfigPop = appConfigPopManager.getHomePageConfigPop(loginUser.getId());
        NewUserClubPopVo newUserClubPopVo = appConfigPopManager.getClubRecommendRespDtoList(loginUser.getId());
        NewUserPkPopVo newUserPkPopVo = appConfigPopManager.getNewUserPkPop(loginUser.getId());
        NewPersonPopVo newPersonPopVo = appConfigPopManager.getNewPersonPop(loginUser.getId());
        MedalPopVo medalPopVo = appConfigPopManager.getMedalPopVo(loginUser.getId(), getLanguageCode());
        LimitedEditionPopVo limitedEditionPopVo = appConfigPopManager.getLimitedEditionPop(loginUser.getId(),getLanguageCode());
        NewOrderPopVo newOrderPopVo = appConfigPopManager.getNewOrderPopVo(loginUser.getId(),getLanguageCode());
        homeUserPopDto.setNewPersonPopVo(newPersonPopVo);
        homeUserPopDto.setAppConfigPopVo(homePageConfigPop);
        homeUserPopDto.setNewUserClubPopVo(newUserClubPopVo);
        homeUserPopDto.setNewUserPkPopVo(newUserPkPopVo);
        homeUserPopDto.setMedalPopVo(medalPopVo);
        homeUserPopDto.setLimitedEditionPopVo(limitedEditionPopVo);
        homeUserPopDto.setNewOrderPopVo(newOrderPopVo);
        return CommonResult.success(homeUserPopDto);
    }


    //

    /**
     * gameurl
     *
     * @return
     */
    @PostMapping("/game/url")
    public Result gameurl() {
        Map<String, Object> data = new HashMap<>();
        //兼容新老服务器
        //TODO 下一个强生版本之后删除，兼容
        if (getAppVersion() >= 4030) {
            data.put("gameUrl", gameUrl);
        } else {
            data.put("gameUrl", gameUrl + ":9001");
        }
        return CommonResult.success(data);
    }


    /**
     * app下载地址
     *
     * @return
     */
    @PostMapping("/downLoadUrl")
    public Result downLoad() {
        Map<String, Object> data = new HashMap<>();
        SysConfig sysConfigAndroid = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.android_app_down_load_url.getCode());
        SysConfig sysConfigIos = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.ios_app_down_load_url.getCode());
        data.put("androidDownUrl", sysConfigIos != null ? sysConfigAndroid.getConfigValue() : "");
        data.put("iosDownUrl", sysConfigIos != null ? sysConfigIos.getConfigValue() : "");
        return CommonResult.success(data);
    }

    /**
     * 首页V3
     *
     * @return
     */
    @PostMapping("/homePage/v3")
    public Result<HomePageV3Vo> homePageV3(@RequestBody HomePageV2Po po) {
        ZnsUserEntity loginUser = getLoginUser();
        boolean testUser = sysConfigService.isTestUser(loginUser.getEmailAddressEn());
        Integer appType = getAppType();
        Integer appVersion = getAppVersion();
        //版本更新
        userService.updateAppVersion(loginUser, appVersion);
        boolean checkUser = appUpgradeService.isCheckVersion(appType, appVersion);

        HomePageV3Vo vo = homepageBussiness.getHomepage(loginUser, testUser, appType, checkUser, appVersion, po.getShowLocation(), getZoneId());
        return CommonResult.success(vo);
    }

    /**
     * 首页V4-获取用户信息
     *
     * @return
     * @tag 3.0.0
     */
    @PostMapping("/homePage/userInfo")
    public Result<HomePageUserInfoRespDto> homePageV3() {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        HomePageUserInfoRespDto vo = homepageBussiness.getHomepageUserInfo(loginUser, zoneId);
        vo.setUserExp(expManager.expPop(loginUser, false));
        HomeNewUserTaskStatusVo userTaskStatus = userTaskManager.getUserTaskStatus(loginUser, Arrays.asList(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode(), TaskConstant.TaskSceneTypeEnum.NEW_USER_EXTRA_REWARD_TASK.getCode()), getAppVersion());
        vo.setNewUserTaskStatus(userTaskStatus.getNewUserTaskStatus());
        vo.setNewUserTaskPop(userTaskStatus.getNewUserTaskPop());
        return CommonResult.success(vo);
    }

    /**
     * 首页V4
     *
     * @return
     * @tag 3.0.0
     */
    @PostMapping("/homePage/v4")
    public Result<HomePageV4Vo> homePageV4(@RequestBody HomePageV2Po po) {
        ZnsUserEntity loginUser = getLoginUser();
        boolean testUser = sysConfigService.isTestUser(loginUser.getEmailAddressEn());
        Integer appType = getAppType();
        Integer appVersion = getAppVersion();
        //版本更新
        userService.updateAppVersion(loginUser, appVersion);
        boolean checkUser = appUpgradeService.isCheckVersion(appType, appVersion);

        HomePageV4Vo vo = homepageBussiness.getHomepageV4(loginUser, testUser, appType, checkUser, appVersion, po.getShowLocation(), getZoneId());
        UserRankedLevel userRankedLevel = userRankedLevelBizService.findByUserId(loginUser.getId());
        if (Objects.isNull(userRankedLevel) || userRankedLevel.getRankedLevelId() == 0) {
            userRankedLevel = null;
        }
        vo.setUserRankedLevel(userRankedLevel);

        //填充道具赛
        vo.setPropUserRankedLevelHomePageDto(propUserRankedLevelManager.findPropUserRankedLevelHomePageDto(loginUser.getId()));

        //判断是否存在游戏中途退出需要更换服装
        UserWearsLog userWearsLog = userWearsLogService.findByUserId(loginUser.getId());
        if (Objects.nonNull(userWearsLog) && Objects.equals(userWearsLog.getSourceType(), 1)) {
            List<UserWearsLog> wearsLogs = userWearsLogService.findListByUserId(loginUser.getId());
            UserWearsLog beforeUserWearsLog = wearsLogs.get(1);
            ZnsUserWearsEntity znsUserWearsEntity = new ZnsUserWearsEntity();
            BeanUtils.copyProperties(beforeUserWearsLog, znsUserWearsEntity);
            znsUserWearsEntity.setId(beforeUserWearsLog.getUserWearId());
            beforeUserWearsLog.setId(null);
            znsUserWearsService.update(znsUserWearsEntity);
            userWearsLogService.insert(beforeUserWearsLog);
        }
        String email = UserContextHolder.getEmail();
        log.info(email);

        return CommonResult.success(vo);
    }

    /**
     * 新人引导PK页展示
     *
     * @return
     */
    @PostMapping("/getNewPersonPkPage")
    public Result<AppNewPersonPkPageConfigVo> getNewPersonPkPage() {
        AppNewPersonPkPageConfigVo vo = appNewPersonPkPageManager.getNewPersonPkPage();
        return CommonResult.success(vo);
    }

    /**
     * 进入首页,点击用户引导页面路由列表
     *
     * @return 用户引导页面路由列表
     */
    @PostMapping("/getUserGuidePageRouteList")
    public Result<UserGuideVO> getUserGuidePageRouteList(@RequestBody(required = false) ActivityNewPersonTypeRequestDto requestDto) {
        Integer appVersion = getAppVersion();
        Integer appType = getAppType();
        if (Integer.valueOf(3041).equals(appVersion) && Integer.valueOf(1).equals(appType)) {
            return CommonResult.success(null);
        }
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.success(null);
        }
        String value = sysConfigService.selectConfigByKey(ConfigKeyEnums.HOMEPAGE_USERGUIDE_PAGE_ROUTE.getCode());
        if (!StringUtils.hasText(value)) {
            return CommonResult.success(null);
        }
        List<UserGuideVO> userGuideVOList = JsonUtil.readList(value, UserGuideVO.class);
        // 随机获取,50%的概率
        int userGuidePageRouteIndex = new Random().nextInt(2);
        UserGuideVO userGuideVO = userGuideVOList.get(userGuidePageRouteIndex);
        int maskTypeIndex = getMaskType();
        ;
        userGuideVO.setMaskType(maskTypeIndex);
        List<UserGuidedPageRouteVO> userGuidePageRouteList = userGuideVO.getUserGuidePageRouteList();
        UserGuidedPageRouteVO userGuidedPageRouteVO = userGuidePageRouteList.get(0);

        // 判断是否满足新人活动条件
        ZnsRunActivityConfigEntity configEntity = runActivityConfigService.getByType(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType(), null);
        Map<String, Object> jsonObject = JsonUtil.readValue(configEntity.getActivityConfig());
        Result result = activityStrategyContext.checkNewUserActivityTime(jsonObject, loginUser, null);
        if (result != null) {
            // 如果用户不满足新人活动的时候,不展示新人URL地址,返回空串
            userGuidedPageRouteVO.setUrl("");
        }
        if (loginUser.getCreateTime().compareTo(newUserVersion) > 0) {
            userGuideVO.setIsNewUser(1);
        } else {
            userGuideVO.setIsNewUser(0);
        }
        Object o = redisUtil.get(RedisConstants.USER_GUIDE_PAGE_ROUTE_POP + loginUser.getId());
        if (Objects.isNull(o)) {
            userGuideVO.setIsNewUserPop(0);
            redisTemplate.opsForValue().set(RedisConstants.USER_GUIDE_PAGE_ROUTE_POP + loginUser.getId(), "1");
        } else {
            userGuideVO.setIsNewUserPop(1);
        }

        String config = sysConfigService.selectConfigByKey("h5.newUserActivity.url." + profile);
        userGuideVO.setXrUserUrl(config);
        //是否赛事引导弹窗
        Object o1 = redisUtil.get(RedisConstants.USER_GUIDE_PAGE_ACTIVITY_POP + loginUser.getId());
        if (Objects.isNull(o1)) {
            userGuideVO.setIsEventGuidancePop(0);
            redisTemplate.opsForValue().set(RedisConstants.USER_GUIDE_PAGE_ACTIVITY_POP + loginUser.getId(), "1");
        } else {
            //获取上次登录时间是否大于30天
            ZnsUserLoginLogEntity lastLogin = userLoginLogService.getLastLogin(loginUser.getId());
            if (Objects.nonNull(lastLogin) && DateUtil.daysBetween(lastLogin.getCreateTime(), ZonedDateTime.now()) > 30) {
                userGuideVO.setIsEventGuidancePop(0);
            } else {
                userGuideVO.setIsEventGuidancePop(1);
            }
        }
        //获取新人pk
        getNewPersonPkConfig(requestDto, userGuideVO);
        return CommonResult.success(userGuideVO);
    }

    private void getNewPersonPkConfig(ActivityNewPersonTypeRequestDto requestDto, UserGuideVO userGuideVO) {
        NewPersonPkVo newPersonPkVo = null;
        if (MatchTypeEnum.ONE_VS_ONE_NEWCOMER.getValue().equals(requestDto.getNewPkType())) {
            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(1L);
        } else if (MatchTypeEnum.ONE_VS_ONE_NEWCOMER_SINGLE_CYCLE.getValue().equals(requestDto.getNewPkType())) {
            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(3L);
        } else if (MatchTypeEnum.ONE_VS_ONE_NEWCOMER_SINGLE_ROWING.getValue().equals(requestDto.getNewPkType())) {
            newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(4L);
        }

        if (Objects.isNull(newPersonPkVo)) {
            return;
        }

        RobotSpeed robotSpeed = newPersonPkVo.getRobotSpeed();
        List<RobotBean> robotLists = robotSpeed.getRobotLists();

        if (MatchTypeEnum.ONE_VS_ONE_NEWCOMER.getValue().equals(requestDto.getNewPkType())) {
            if(!CollectionUtils.isEmpty(robotLists)){
                int index = new Random().nextInt(robotLists.size());
                userGuideVO.setChallengeRunDataDetailsId(robotLists.get(index).getDetailId());
            }
            List<RobotBean> walkRobotLists = robotSpeed.getWalkRobotLists();
            if(!CollectionUtils.isEmpty(walkRobotLists)){
                int index = new Random().nextInt(walkRobotLists.size());
                userGuideVO.setChallengeWalkDataDetailsId(walkRobotLists.get(index).getDetailId());
            }
        } else {
            if(!CollectionUtils.isEmpty(robotLists)){
                int index = new Random().nextInt(robotLists.size());
                userGuideVO.setChallengeDataDetailsId(robotLists.get(index).getDetailId());
            }
        }

        userGuideVO.setNewPersonGuideVo(newPersonPkVo);
    }

    private int getMaskType() {
        //概率334
        int userGuidePageRouteIndex = new Random().nextInt(10);
        userGuidePageRouteIndex = 8;
        if (userGuidePageRouteIndex < 3) {
            return 0;
        } else if (userGuidePageRouteIndex < 6) {
            return 1;
        } else {
            return 2;
        }
    }

    /**
     * 半屏弹窗配置获取接口
     *
     * @return
     */
    @PostMapping("/halfScreen/popH5")
    public Result<HalfScreenPopVo> halfScreenPopH5(@RequestBody @Validated HalfScreenReq halfScreenReq) {
        ZnsUserEntity loginUser = getLoginUser();
        HalfScreenPopVo halfScreenPopVo = new HalfScreenPopVo();
        halfScreenPopVo.setPopUrl("");
        // 根据用户所属人群 匹配符合条件的半屏弹窗
        HalfPopConfigH5 halfPopConfigH5 = halfPopConfigH5Service.selectHalfPopByTypeAndUser(halfScreenReq.getPopPositionType(), loginUser.getId());
        if (Objects.isNull(halfPopConfigH5)) {
            return CommonResult.success(halfScreenPopVo);
        }
        String popKey = "";
        TimeZone timeZone = TimeZone.getTimeZone("GMT-8:00");
        ZonedDateTime date = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), timeZone);

        String langCode = I18nMsgUtils.getLangCode();
        HalfPopConfigI18n i18nMsg = halfPopConfigI18nService.findByQuery(HalfPopQuery.builder().popId(halfPopConfigH5.getId()).langCode(langCode).defaultLangCode(halfPopConfigH5.getDefaultLangCode()).build());
        String popUrl = Objects.nonNull(i18nMsg) ? i18nMsg.getPopUrl() : halfPopConfigH5.getPopUrl();
        switch (halfPopConfigH5.getPopTimeType()) {
            // 0 默认展示 1 每天展示1次 2 每周展示1次 3每个版本展示一次
            case 0:
                halfScreenPopVo.setPopUrl(popUrl);
                break;
            case 1:
                popKey = RedisConstants.USER_H5_POP_HALF_FLAG_DAY + halfPopConfigH5.getPopPositionType() + ":" + loginUser.getId();
                String flag = redisUtil.get(popKey);
                if (StringUtils.isEmpty(flag)) {
                    // 今日未展示过
                    long between = DateUtil.between(date, DateUtil.endOfDate(date));
                    redisUtil.set(popKey, "1", between, TimeUnit.SECONDS);
                    halfScreenPopVo.setPopUrl(popUrl);
                }
                break;
            case 2:
                popKey = RedisConstants.USER_H5_POP_HALF_FLAG_WEEK + halfPopConfigH5.getPopPositionType() + ":" + loginUser.getId();
                if (StringUtils.isEmpty(redisUtil.get(popKey))) {
                    long between = DateUtil.between(date, DateUtil.endOfWeek(date));
                    redisUtil.set(popKey, "1", between, TimeUnit.SECONDS);
                    halfScreenPopVo.setPopUrl(popUrl);
                }
                break;
            case 3:
                popKey = RedisConstants.USER_H5_POP_HALF_FLAG_VERSION + ":" + getAppVersion() + halfPopConfigH5.getPopPositionType() + ":" + loginUser.getId();
                if (StringUtils.isEmpty(redisUtil.get(popKey))) {
                    redisTemplate.opsForValue().set(popKey, "1");
                    halfScreenPopVo.setPopUrl(popUrl);
                }
                break;
            default:
        }
        if (Objects.isNull(halfScreenPopVo.getPopUrl())) {
            halfScreenPopVo.setPopUrl("");
        }
        return CommonResult.success(halfScreenPopVo);
    }

    /**
     * 半屏卷领取
     *
     * @param halfScreenCouponClaimReq
     * @return
     */
    @PostMapping("/halfScreen/coupon/claim")
    public Result<HalfScreenCouponClaimResp> halfScreenPopCouponClaim(@RequestBody @Validated HalfScreenCouponClaimReq halfScreenCouponClaimReq) {
        ZnsUserEntity loginUser = getLoginUser();
        HalfScreenCouponClaimResp halfScreenCouponClaimResp = userCouponManager.sendUserHalfScreenPopCouponClaim(loginUser.getId(), halfScreenCouponClaimReq.getCouponId());
        return CommonResult.success(halfScreenCouponClaimResp);
    }

    /**
     * 确认当前用户需要显示的首页版本
     * 只需要根据用户表need_version_control字段为0就返回新页面即可
     *
     * @return
     */
    @PostMapping("/version/page")
    public Result<VersionPageRespDto> selectHomePage() {
        ZnsUserEntity loginUser = getLoginUser();
        Integer needVersionControl = loginUser.getNeedVersionControl(); // 0-返回3.0首页  1-返回2.12首页
        VersionPageRespDto dto = new VersionPageRespDto();
        dto.setVersionControl(needVersionControl);
        return CommonResult.success(dto);
    }

    /**
     * 首页自定义获取接口
     */
    @PostMapping("/home/<USER>")
    public Result<HomePageCustomizeAppResponseDto> homeCustomize() {
        HomePageCustomizeAppResponseDto homeCustomize = homepageBussiness.getHomeCustomize(getLoginUser(), getAppVersion(), getAppType());
        return CommonResult.success(homeCustomize);
    }

    /**
     * 俱乐部推荐弹窗记录
     */
    @PostMapping("/club/recommend/Pop")
    public Result<Void> clubRecommendPop() {
        ZnsUserEntity user = getLoginUserNotNull();
        userRelatedService.dealRecommendClubPop(user.getId());
        return CommonResult.success();
    }
}
