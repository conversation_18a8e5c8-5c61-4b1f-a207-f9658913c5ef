package com.linzi.pitpat.api.userservice.manager;


import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.enums.EmailStatusEnum;
import com.linzi.pitpat.data.enums.VipStatusUserEnum;
import com.linzi.pitpat.data.enums.VipTypeEnum;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.userservice.dto.response.VipRightReponseDto;
import com.linzi.pitpat.data.userservice.dto.response.VipShopListVo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContent;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRightI18nContentBeta;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRule;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipShop;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipShopCurrencyEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipShopI18n;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUser;
import com.linzi.pitpat.data.userservice.model.query.VipRightQuery;
import com.linzi.pitpat.data.userservice.model.query.VipRuleQuery;
import com.linzi.pitpat.data.userservice.model.query.VipShopQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupService;
import com.linzi.pitpat.data.userservice.service.vip.VipRightI18nContentBetaService;
import com.linzi.pitpat.data.userservice.service.vip.VipRightI18nContentService;
import com.linzi.pitpat.data.userservice.service.vip.VipRuleService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopCurrencyService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopI18nService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.vo.user.UserVipInfoAppVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/9 上午10:05
 * @Description
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserVipMananger {

    private final ZnsUserService znsUserService;
    private final VipUserService vipUserService;
    private final VipShopService vipShopService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final VipShopCurrencyService vipShopCurrencyService;
    private final ZnsUserAccountService userAccountService;
    private final VipShopI18nService vipShopI18nService;
    private final VipRightI18nContentService vipRightI18nContentService;
    private final VipRightI18nContentBetaService vipRightI18nContentBetaService;
    private final VipRuleService vipRuleService;
    private final UserGroupService userGroupService;

    /**
     * 会员信息
     *
     * @param userId
     * @return
     */
    public UserVipInfoAppVo findById(Long userId) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        if (Objects.isNull(znsUserEntity)) {
            throw new BaseException("Unable to activate, contact customer service");
        }
        return vipUserService.checkVipUser(znsUserEntity);
    }

    /**
     * 会员升级（手动）
     *
     * @param userId
     * @return
     */
    @Transactional
    public UserVipInfoAppVo updateById(Long userId, Long vipUserId) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        if (Objects.isNull(znsUserEntity)) {
            throw new BaseException("Unable to activate, contact customer service");
        }
        String email = znsUserEntity.getEmailAddressEn();
        if (!StringUtils.hasText(email)) {
            throw new BaseException("Unable to activate, contact customer service");
        }
        if (Objects.isNull(vipUserId) || vipUserId == -1) {
            throw new BaseException("can not find vip info");
        }

        VipUser vipUser = vipUserService.findById(vipUserId);
        if (Objects.isNull(vipUser)) {
            throw new BaseException("can not find vip info");
        }
        if (!VipStatusUserEnum.WAIT_ACTIVATED.getCode().equals(vipUser.getVipStatus())) {
            throw new BaseException("can not find wait activated vipinfo");
        }
        // 更新会员池信息
        ZonedDateTime nowDate = ZonedDateTime.now();
        List<VipUser> allList = new ArrayList<>();
        Integer validDays = vipUser.getValidDays();
        // 过期处理
        VipUserQuery query = VipUserQuery.builder().vipStatus(VipStatusUserEnum.ACTIVATED.getCode())
                .emailAddressEn(znsUserEntity.getEmailAddressEn()).emailStatus(EmailStatusEnum.CANCELLED.getCode())
                .vipType(vipUser.getVipType()).isPermanent(0).lastStr("ORDER BY vip_type DESC").build();
        List<VipUser> needPassVipList = vipUserService.finList(query);
        // 对非永久会员 累加剩余有效天数 做过期处理
        if (!CollectionUtils.isEmpty(needPassVipList)) {
            validDays += DateUtil.daysBetween(nowDate, needPassVipList.get(0).getVipEndtime());
            for (VipUser item : needPassVipList) {
                if (Objects.nonNull(item.getVipEndtime()) && item.getVipEndtime().isAfter(nowDate)) {
                    item.setVipEndtime(nowDate);
                    item.setVipStatus(VipStatusUserEnum.EXPIRED.getCode());
                    item.setValidDays(0);
                    allList.add(item);
                }
            }
        }

        vipUser.setVipStatus(VipStatusUserEnum.ACTIVATED.getCode());
        vipUser.setVipStarttime(nowDate);
        vipUser.setVipEndtime(DateUtil.addDays1(nowDate, validDays));
        allList.add(vipUser);
        vipUserService.updateBatchById(allList);
        return vipUserService.checkVipUser(znsUserEntity);
    }


    public VipUser findByEmail(String emailAddress) {
        return vipUserService.selectByEmail(emailAddress);
    }

    public boolean updateById(VipUser vipUser) {
        return vipUserService.update(vipUser);
    }


    /**
     * 获取会员购买列表页信息
     *
     * @return
     */
    public List<VipShopListVo> getVipShopList(ZnsUserEntity userEntity) {
        List<VipShopListVo> vipShopList = new ArrayList<>();

        VipShopQuery query = VipShopQuery.builder().status(1).build();
        if (Objects.nonNull(userEntity)) {
            query.setGeVipType(userEntity.getVipType());
        }
        query.setOrders(List.of(OrderItem.asc("sort")));
        List<VipShop> vipShops = vipShopService.selectList(query);
        if (Objects.nonNull(userEntity)) {
            // 过滤比当前用户身份小的会员商品
            vipShops = vipShops.stream().filter(item -> {
                // 用户当前是永久普通会员 则把普通会员年卡过滤
                if (VipTypeEnum.NORMAL_VIP.getCode().equals(item.getVipType())) {
                    if (VipTypeEnum.NORMAL_VIP.getCode().equals(userEntity.getVipType())) {
                        VipUser vipUser = vipUserService.selectVipUserById(userEntity.getVipId());
                        if (vipUser.getIsPermanent() == 1) {
                            return false;
                        }
                    }
                }
                return true;
            }).collect(Collectors.toList());

        }

        String langCode = I18nMsgUtils.getLangCode();
        //获取会员的货币信息
        Currency currency = userAccountService.getUserCurrency(userEntity.getId());
        if (!CollectionUtils.isEmpty(vipShops)) {
            for (VipShop vs : vipShops) {
                VipShopListVo resp = new VipShopListVo();
                BeanUtils.copyProperties(vs, resp);
                VipShopCurrencyEntity vipShopCurrency = vipShopCurrencyService.findByVipShopIdAndCurrencyCode(vs.getId(), currency.getCurrencyCode());
                //使用汇率填充新的价格
                resp.setPrice(vipShopCurrency.getPrice());
                resp.setReferencePrice(vipShopCurrency.getReferencePrice());
                resp.setCurrency(currency);
                if (vs.getValidDays() == 90) {
                    resp.setValidMonthYear(3);
                    resp.setValidMonthYearUnit(1);
                } else {
                    resp.setValidMonthYear(1);
                    resp.setValidMonthYearUnit(2);
                }
                resp.setStockEnough(1);
                if (Objects.nonNull(vs.getGoodsId()) && Objects.nonNull(vs.getSkuId())) {
                    Long goodsId = vs.getGoodsId();
                    Long skuId = vs.getSkuId();
                    ZnsGoodsSkuEntity effectiveSku = znsGoodsSkuService.getEffectiveSkuNotListed(skuId);
                    if (Objects.isNull(effectiveSku) || effectiveSku.getStock() <= 0) {
                        resp.setStockEnough(0);
                    }
                }
                // 国际化数据
                VipShopI18n vipShopI18n = vipShopI18nService.findByQuery(VipShopQuery.builder().shopId(vs.getId()).langCode(langCode).defaultLangCode(vs.getDefaultLangCode()).build());
                resp.setTitle(Objects.nonNull(vipShopI18n) ? vipShopI18n.getTitle() : vs.getTitle());
                resp.setImgUrl(Objects.nonNull(vipShopI18n) ? vipShopI18n.getImgUrl() : vs.getImgUrl());

                vipShopList.add(resp);
            }
        }
        return vipShopList;
    }

    /**
     * 获取会员权益规则
     */
    public VipRightReponseDto getVipRightRule(Long userId) {
        VipRightReponseDto result = new VipRightReponseDto();
        String langCode = I18nMsgUtils.getLangCode();
        ZnsUserEntity znsUser = znsUserService.findById(userId);
        VipRule vipRule = vipRuleService.getOneByQuery(VipRuleQuery.builder().build());
        VipRightI18nContent i18nContent = null;

        // 查询是否在内测用户群组中
        String betaGroupIds = vipRule.getBetaGroupIds();
        if (StringUtils.hasText(betaGroupIds)) {
            List<Long> groupIds = Arrays.asList(betaGroupIds.split(",")).stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
            List<ZnsUserEntity> groupUsers = userGroupService.getUserByGroupIds(groupIds);
            ZnsUserEntity matchUser = groupUsers.stream().filter(item -> item.getId().equals(userId)).findFirst().orElse(null);
            if (Objects.nonNull(matchUser)) {
                // 在内测用户群组中
                VipRightI18nContentBeta betaI18nContent = vipRightI18nContentBetaService.findOneByQuery(VipRightQuery.builder().vipType(znsUser.getVipType()).langCode(langCode).defaultLangCode(vipRule.getDefaultLangCode()).build());
                if (Objects.nonNull(betaI18nContent)) {
                    i18nContent = new VipRightI18nContent();
                    BeanUtils.copyProperties(betaI18nContent, i18nContent);
                }
            }
        }

        if (Objects.isNull(i18nContent)) {
            // 正式
            i18nContent = vipRightI18nContentService.findOneByQuery(VipRightQuery.builder().vipType(znsUser.getVipType()).langCode(langCode).defaultLangCode(vipRule.getDefaultLangCode()).build());
        }
        result.setContent(Objects.nonNull(i18nContent) ? i18nContent.getContent() : "");
        return result;
    }


}
