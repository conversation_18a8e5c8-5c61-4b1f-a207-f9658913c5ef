package com.linzi.pitpat.api.activityservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.api.client.util.Lists;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.manager.HallOfFameBestManager;
import com.linzi.pitpat.api.activityservice.manager.TrainingOfflinePkManager;
import com.linzi.pitpat.api.activityservice.manager.UserBestPbManager;
import com.linzi.pitpat.api.activityservice.manager.UserPaceConfigManager;
import com.linzi.pitpat.api.bussiness.AppUserIMManager;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.TrainingUserInfo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkBaseVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkFriendListVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkMatchBaseInfoVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkMatchOpponentUserListVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkRankVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkResultVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkRulesVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkStartVo;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkUserRateAndTimeVo;
import com.linzi.pitpat.data.activityservice.quartz.RouteUserNumberTask;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.TrainingOfflinePkRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigEntity;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.request.runData.DetailIdRequest;
import com.linzi.pitpat.data.resp.course.OfflinePkChallengeRecordDto;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.userservice.dto.request.FriendIdRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserPaceConfigRequestDto;
import com.linzi.pitpat.data.userservice.dto.request.UserPkPaceRequestDto;
import com.linzi.pitpat.data.userservice.dto.response.UserBestPaceDto;
import com.linzi.pitpat.data.userservice.dto.response.UserBestPbDto;
import com.linzi.pitpat.data.userservice.dto.response.UserPaceAndRouteConfigResponseDto;
import com.linzi.pitpat.data.userservice.dto.response.UserPaceConfigResponseDto;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 训练场离线pk赛 控制器
 */
@RestController
@Slf4j
@RequestMapping("/app/training/offline/pk")
@RequiredArgsConstructor
public class OfflinePkMatchController extends BaseAppController {

    private final TrainingOfflinePkManager trainingOfflinePkManager;
    private final ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    private final TrainingOfflinePkRecordService trainingOfflinePkRecordService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final RedissonClient redissonClient;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final ZnsUserAccountService znsUserAccountService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final ZnsUserService userService;
    private final AppUserIMManager appUserIMManager;
    private final HallOfFameBestManager hallOfFameBestManager;
    private final UserPaceConfigManager userPaceConfigManager;
    private final UserBestPbManager userBestPbManager;


    /**
     * 训练场数据pk赛用户基本信息获取
     *
     * @return
     * @tag 3.8.0
     */
    @PostMapping("/info")
    public Result<OfflinePkMatchBaseInfoVo> info() {
        String languageCode = getLanguageCode();
        ZnsUserEntity loginUser = getLoginUser();
        OfflinePkMatchBaseInfoVo offlinePkMatchBaseInfoVo = new OfflinePkMatchBaseInfoVo();
        offlinePkMatchBaseInfoVo.setHeadUrl(loginUser.getHeadPortrait());
        offlinePkMatchBaseInfoVo.setNickName(loginUser.getFirstName());
        TrainingUserInfo trainingUserInfo = trainingOfflinePkManager.getUserPkWinAmount(loginUser.getId());
        //用户币种
        ZnsUserAccountEntity userAccount = znsUserAccountService.getByUserId(loginUser.getId());
        offlinePkMatchBaseInfoVo.setOfflinePkWinAmount(trainingUserInfo.getPkAllAward());
        Currency currency = new Currency(userAccount.getCurrencyName(), userAccount.getCurrencyCode(), userAccount.getCurrencySymbol());
        offlinePkMatchBaseInfoVo.setCurrency(currency);
        offlinePkMatchBaseInfoVo.setPkTotalScore(trainingUserInfo.getPkTotalScore());
        List<OfflinePkRulesVo> rules = Lists.newArrayList();
        ZnsRunActivityConfigEntity znsRunActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.COURSE_OFFLINE_PK.getType(), null);
        Map<String, Object> jsonObject = JsonUtil.readValue(znsRunActivityConfig.getActivityConfig());
        offlinePkMatchBaseInfoVo.setEveryDayPkRandomTime(MapUtil.getInteger(jsonObject.get("everyDayPkRandomTime"))); //TODO i18n 配置
        offlinePkMatchBaseInfoVo.setEveryDayPkAllTime(MapUtil.getInteger(jsonObject.get("everyDayPkAllTime"))); //TODO i18n 配置
        rules.add(new OfflinePkRulesVo(I18nMsgUtils.getMessage("activity.offlinePK.rule.playTitle"), I18nMsgUtils.getMessage("activity.offlinePK.rule.playContent")));
        rules.add(new OfflinePkRulesVo(I18nMsgUtils.getMessage("activity.offlinePK.rule.winTitle"), I18nMsgUtils.getMessage("activity.offlinePK.rule.winContent")));
        // $0.8 & points 100   $%s & points %s
        String rewardsStr = getRewardsStr(jsonObject, currency, languageCode); //i18n 配置
        rules.add(new OfflinePkRulesVo(I18nMsgUtils.getMessage("activity.offlinePK.rule.rewards.title"), rewardsStr));
        rules.add(new OfflinePkRulesVo(I18nMsgUtils.getMessage("activity.offlinePK.rule.others.title"),
                I18nMsgUtils.getMessage("activity.offlinePK.rule.others.content", MapUtil.getString(jsonObject.get("everyDayPkAllTime")), MapUtil.getString(jsonObject.get("everyDayPkRandomTime")), MapUtil.getInteger(jsonObject.get("everyDayPkRandomTime")) + 1)));
        offlinePkMatchBaseInfoVo.setRules(rules);
        offlinePkMatchBaseInfoVo.setPkTime(trainingOfflinePkRecordService.selectUserTodayTime(loginUser.getId()));

        offlinePkMatchBaseInfoVo.setShowContent(assmble(getLoginUser()));

        String shareContent = I18nMsgUtils.getMessage("pk.match.content.shareContent");
        offlinePkMatchBaseInfoVo.setCommunityShareContent(shareContent);
        // 剩余pk次数
        offlinePkMatchBaseInfoVo.setRemainingPkTime(trainingOfflinePkManager.getUserTimes(loginUser.getId()));
        return CommonResult.success(offlinePkMatchBaseInfoVo);
    }

    private String assmble(ZnsUserEntity user) {

        String message;
        ZonedDateTime startOfDate = DateUtil.getStartOfDate(ZonedDateTime.now());

        List<ZnsUserRunDataDetailsEntity> list = znsUserRunDataDetailsService.findByUserIdAndCreateTime(user.getId(), startOfDate);
        int sum = list.stream().mapToInt(k -> k.getRunMileage().intValue()).sum();

        if (sum == 0) {
            message = I18nMsgUtils.getMessage("activity.offlinePK.rule.showContent");
        } else {
            List<Map<String, Object>> todayRunMilages = znsUserRunDataDetailsService.getAllUserMilageBytime(startOfDate);
            List<BigDecimal> totals = todayRunMilages.stream().map(k -> (BigDecimal) k.get("total")).collect(Collectors.toList());
            BigDecimal current = BigDecimal.valueOf(sum);
            totals.add(current);
            Collections.sort(totals, Collections.reverseOrder());
            int rank = totals.indexOf(current);

            Long count = userService.findAllRealUserCount();
            //BigDecimal divide = BigDecimalUtil.divide(count - rank, count);
            BigDecimal divide = new BigDecimal(count - rank).divide(new BigDecimal(count), 2, BigDecimal.ROUND_DOWN);
            int proportion = divide.multiply(new BigDecimal("100")).setScale(0, BigDecimal.ROUND_DOWN).intValue();

            if (proportion < 50) {
                proportion = 50;
            } else if (proportion <= 85) {
                proportion = proportion + 10;
            } else if (proportion < 95) {
                proportion = proportion + 5;
            } else {
                proportion = 99;
            }

            message = I18nMsgUtils.getMessage("activity.offlinePK.rule.showContent.exceed", proportion);
            //"Today’s exercise mileage exceeded "+proportion+"% of PitPat runner";

        }
        return message;
    }

    private String getRewardsStr(Map<String, Object> jsonObject, Currency currency, String languageCode) {
        BigDecimal pkWinAward = MapUtil.getBigDecimal(jsonObject.get("pkWinAward")); // 转成对应货币的价格
        Integer pkScoreAward = MapUtil.getInteger(jsonObject.get("pkScoreAward"));
        if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currency.getCurrencyCode())) {
            // 如果当前用户不是美元账户-转换币种计算
            ExchangeRateConfigEntity exchangeRate = exchangeRateConfigService.selectBySourceCurrencyAndTargetCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode(), currency.getCurrencyCode());
            //币种格式化
            pkWinAward = I18nConstant.currencyFormat(currency.getCurrencyCode(), exchangeRate.getExchangeRate().multiply(pkWinAward));
        }

        boolean flag1 = Objects.nonNull(pkWinAward) && pkWinAward.compareTo(BigDecimal.ZERO) > 0;
        boolean flag2 = Objects.nonNull(pkScoreAward) && pkScoreAward > 0;


        String rewards = "";
        String points = I18nConstant.LanguageCodeEnum.th_TH.getCode().equals(languageCode) ? "& คะแนน " : "& points";
        if (flag1 && flag2) {
            rewards = I18nMsgUtils.getMessage("activity.offlinePK.rule.reward.content", currency.getCurrencySymbol() + I18nConstant.currencyFormat(currency.getCurrencyCode(), pkWinAward) + points + pkScoreAward);
        } else if (flag1 && !flag2) {
            rewards = I18nMsgUtils.getMessage("activity.offlinePK.rule.reward.content", currency.getCurrencySymbol() + I18nConstant.currencyFormat(currency.getCurrencyCode(), pkWinAward));
        } else if (!flag1 && flag2) {
            rewards = I18nMsgUtils.getMessage("activity.offlinePK.rule.reward.content", "points " + pkScoreAward);
        }
        return rewards;
    }


    /**
     * 离线pk赛用挑战记录场次/胜率信息
     *
     * @return
     * @tag 3.8.0
     */
    @PostMapping("/challengeRecord/info")
    public Result<OfflinePkUserRateAndTimeVo> challengeInfo() {
        ZnsUserEntity loginUser = getLoginUser();
        OfflinePkUserRateAndTimeVo offlinePkUserRateAndTimeVo = trainingOfflinePkManager.challengeInfo(loginUser.getId());
        return CommonResult.success(offlinePkUserRateAndTimeVo);
    }

    /**
     * 离线pk赛用挑战记录
     *
     * @return
     * @tag 2.6
     */
    @PostMapping("/challengeRecord/list")
    public Result<Page<OfflinePkChallengeRecordDto>> challengeRecord(@RequestBody PageQuery pagePo) {
        ZnsUserEntity loginUser = getLoginUser();
        //用户账户类别
        List<ZnsUserAccountEntity> userAccounts = znsUserAccountService.getUserAccounts(loginUser.getId());
        Page<OfflinePkChallengeRecordDto> list = trainingOfflinePkManager.challengeRecord(loginUser.getId(), pagePo);
        List<OfflinePkChallengeRecordDto> records = list.getRecords();
        if (CollectionUtils.isEmpty(userAccounts)) {
            return CommonResult.fail("当前账号不存在");
        }
        ZnsUserAccountEntity userAccount = znsUserAccountService.getUserAccount(loginUser.getId());
        for (OfflinePkChallengeRecordDto record : records) {
            //因为最新的记录在最上，只要不满足下诉条件，则userAccount就可替换
            if (record.getGmtCreate().isBefore(userAccount.getCreateTime())) {
                userAccount = userAccounts.stream().filter(a -> a.getCreateTime().isBefore(record.getGmtCreate())).findFirst().orElse(userAccount);
            }
            record.setCurrency(new Currency(userAccount.getCurrencyName(), userAccount.getCurrencyCode(), userAccount.getCurrencySymbol()));
        }
        list.setRecords(records);
        return CommonResult.success(list);
    }

    /**
     * 对手列表
     *
     * @tag 2.6
     */
    @PostMapping("/opponent/list")
    public Result<OfflinePkMatchOpponentUserListVo> opponentList(@RequestBody OfflinePkBaseVo offlinePkBaseVo) {
        ZnsUserEntity loginUser = getLoginUser();
        ZnsRunActivityConfigEntity znsRunActivityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.COURSE_OFFLINE_PK.getType(), null);
        Map<String, Object> jsonObject = JsonUtil.readValue(znsRunActivityConfig.getActivityConfig());
        OfflinePkMatchOpponentUserListVo list = trainingOfflinePkManager.randomUserForOfflinePkMatch(loginUser.getId(), offlinePkBaseVo.getPkTime(), jsonObject);
        return CommonResult.success(list);
    }

    /**
     * 发起Pk（数据pk）
     *
     * @tag 2.6
     */
    @PostMapping("/start")
    public Result<OfflinePkResultVo> startPk(@RequestBody OfflinePkStartVo offlinePkStartVo) {
        ZnsUserEntity loginUser = getLoginUser();
        String key = RedisConstants.OFFLINE_START_PK + loginUser.getId();
        Result<OfflinePkResultVo> result = null;
        try {
            RLock lock = redissonClient.getLock(key);
            result = LockHolder.tryLock(lock, () -> {
                OfflinePkResultVo offlinePkResultVo = trainingOfflinePkManager.startPk(loginUser.getId(), offlinePkStartVo.getOpponentUserId(), offlinePkStartVo.getPkTime());
                trainingOfflinePkManager.decrUserTimes(loginUser.getId());
                return CommonResult.success(offlinePkResultVo);
            });
        } catch (Exception e) {
            log.error("PK is error", e);
        }
        return result;
    }


    /**
     * 好友列表
     *
     * @tag 2.6
     */
    @PostMapping("/friend/list")
    public Result<OfflinePkFriendListVo> friendList() {
        ZnsUserEntity loginUser = getLoginUser();
        OfflinePkFriendListVo offlinePkResultVo = trainingOfflinePkManager.friendList(loginUser.getId());
        return CommonResult.success(offlinePkResultVo);
    }

    /**
     * 排行榜
     *
     * @tag 2.11
     */
    @PostMapping("/rank/list")
    public Result<List<OfflinePkRankVo>> rankList() {
        return CommonResult.success(trainingOfflinePkManager.rankList());
    }

    /**
     * 用户最佳 1 Mile配速
     */
    @PostMapping("/best/pace")
    public Result<UserSimpleVo> bestPace(@RequestBody UserPkPaceRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(requestDto.getDeviceType())) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        return CommonResult.success(realPersonRunDataDetailsService.getUserBestPaceByRunMileage(loginUser.getId(), 1600, com.google.common.collect.Lists.newArrayList(requestDto.getDeviceType())));
    }

    /**
     * 用户最佳 Mile配速 列表
     */
    @PostMapping("/pace/list")
    public Result<List<UserBestPaceDto>> bestPaceList(@RequestBody UserPkPaceRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(requestDto.getDeviceType())) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        return CommonResult.success(userBestPbManager.findBestPaceListByUser(loginUser.getId(), requestDto.getDeviceType(), getAppVersion(), requestDto.getMeasureUnit()));
    }

    /**
     * 用户关注获取次数
     *
     * @param request
     * @return
     * @tag 3.8.0
     */
    @PostMapping("/add")
    public Result<Integer> add(@RequestBody FriendIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        if (request.getFriendId().equals(loginUser.getId())) {
            return CommonResult.success(-1);
        }
        ZnsUserEntity znsUserEntity = userService.findById(request.getFriendId());

        if (znsUserEntity == null) {
            //好友不存在
            throw new BaseException(I18nMsgUtils.getMessage("user.friend.not.exist"));
        }
        if (UserConstant.PrivacyEnum.ISPRIVACY_1.getCode().equals(znsUserEntity.getIsPrivacy())) {
            //私密账号不能关注
            throw new BaseException(I18nMsgUtils.getMessage("user.private.mode"));
        }
        // relationType 0表示粉丝，1表示互相关注，2表示我关注的人
        Integer relationType = trainingOfflinePkManager.pkFriendAdd(loginUser, request);
        if (Objects.equals(relationType, 1)) {
            appUserIMManager.handleFollowChatRelation(loginUser.getId(), request.getFriendId());
        }
        return CommonResult.success(relationType);
    }


    /**
     * 名人堂 最佳 Mile配速 列表
     *
     * @tag 4.5.0
     */
    @PostMapping("/hallOfFame/pace/list")
    @FillerMethod
    public Result<List<UserBestPaceDto>> hallOfFameBestPaceList(@RequestBody UserPkPaceRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(requestDto.getDeviceType())) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        return CommonResult.success(hallOfFameBestManager.findBestPaceListByUser(loginUser, requestDto.getDeviceType()));
    }

    private void replaceUnSupportDeviceType(UserPkPaceRequestDto requestDto) {
        if (requestDto.getDeviceType().equals(EquipmentDeviceTypeEnum.BICYCLE.getCode()) && getAppVersion() < VersionConstant.V4_7_1) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
    }


    /**
     * 个人配速config 获取 rn 版本
     *
     * @tag 4.5.0
     */
    @PostMapping("/paceConfig/list")
    public Result<UserPaceConfigResponseDto> paceConfigList(@RequestBody UserPkPaceRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(requestDto.getDeviceType())) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        replaceUnSupportDeviceType(requestDto);
        return CommonResult.success(userPaceConfigManager.paceConfigList(requestDto, loginUser));
    }

    /**
     * 个人配速 + 地图 config 获取 app 版本
     *
     * @tag 4.5.0
     */
    @PostMapping("/paceConfigAndRoute/list")
    public Result<UserPaceAndRouteConfigResponseDto> paceConfigAndRouteList(@RequestBody UserPkPaceRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(requestDto.getDeviceType())) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        replaceUnSupportDeviceType(requestDto);
        return CommonResult.success(userPaceConfigManager.paceConfigAndRouteList(requestDto, loginUser));
    }

    /**
     * 个人配速config 保存
     *
     * @tag 4.5.0
     */
    @PostMapping("/paceConfig/save")
    public Result<Void> paceConfigSave(@RequestBody UserPaceConfigRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        userPaceConfigManager.paceConfigSave(requestDto, loginUser);
        return CommonResult.success();
    }

    /**
     * 我的最佳pb list
     *
     * @tag 4.5.0
     */
    @PostMapping("/myBestPb")
    public Result<UserBestPbDto> myBestPb(@RequestBody UserPaceConfigRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (requestDto.getDeviceType().equals(EquipmentDeviceTypeEnum.BICYCLE.getCode()) && getAppVersion() < VersionConstant.V4_7_1) {
            requestDto.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        return CommonResult.success(userBestPbManager.myBestPb(loginUser, requestDto));
    }

    private final RouteUserNumberTask routeUserNumberTask;

    /**
     * 同步游戏地图人数
     *
     * @tag 4.5.0
     */
    @PostMapping("/syncRouteNumber")
    public Result<Void> syncRouteNumber(@RequestBody UserPaceConfigRequestDto requestDto) {
        ZnsUserEntity loginUser = getLoginUser();
        routeUserNumberTask.run();
        return CommonResult.success();
    }


    /**
     * 发送挑战对手成功消息
     *
     * @tag 4.6.0
     * @since 4.6.0
     */
    @PostMapping("/sendSuccessIm")
    public Result<Void> sendSuccessIm(@RequestBody DetailIdRequest detailIdRequest) {
        ZnsUserEntity loginUser = getLoginUser();
        userBestPbManager.sendSuccessIm(loginUser, detailIdRequest);
        return CommonResult.success();
    }

}
