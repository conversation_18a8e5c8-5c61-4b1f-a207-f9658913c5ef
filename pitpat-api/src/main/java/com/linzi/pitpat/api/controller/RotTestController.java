package com.linzi.pitpat.api.controller;

import com.linzi.pitpat.api.mallservice.mananger.PayPalBussiness;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityRankDto;
import com.linzi.pitpat.data.activityservice.listener.event.RunEndEvent;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.robotservice.listener.event.UserFinFollowEvent;
import com.linzi.pitpat.data.robotservice.listener.event.UserLoginEvent;
import com.linzi.pitpat.data.robotservice.listener.event.UserReportActivityEvent;
import com.linzi.pitpat.data.robotservice.quartz.RobotTask;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.impl.ZnsUserServiceImpl;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping({"rotTest"})
@Slf4j
public class RotTestController {

    @Autowired
    private ZnsUserServiceImpl userService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ZnsUserRunDataDetailsService dataDetailsService;

    @Autowired
    private RobotTask robotTask;
    @Autowired
    private PaypalPayService paypalPayService;

    @Autowired
    private PayPalBussiness payBussiness;

    @Autowired
    private MainActivityService mainActivityService;

    @Autowired
    private ZnsRunActivityUserService runActivityUserService;

    @GetMapping("/testRank")
    public Result<List<SeriesActivityRankDto>> testRank(Long mainActivityId, String rankBy) {
        List<SeriesActivityRankDto> list = new ArrayList<>();
        ZonedDateTime now = ZonedDateTime.now();


        List<ZnsRunActivityUserEntity> allActivityUserList = runActivityUserService.findAllActivityUser(mainActivityId);


        List<ZnsUserEntity> users = userService.findByIds(allActivityUserList.stream().map(ZnsRunActivityUserEntity::getUserId).toList());
        Map<Long, List<ZnsUserEntity>> userMap = users.stream().collect(Collectors.groupingBy(ZnsUserEntity::getId));
        //预处理
        for (int i = 0; i < allActivityUserList.size(); i++) {
            ZnsRunActivityUserEntity runActivityUser = allActivityUserList.get(i);

            if (Objects.equals(runActivityUser.getIsComplete(), 0)) {
                BigDecimal runMileage = runActivityUser.getRunMileage();
                Integer runTime = runActivityUser.getRunTime();
                Integer propRunTime = runActivityUser.getPropRunTime();
                if (Objects.isNull(propRunTime)) {
                    propRunTime = runTime;
                }
                //有成绩未完赛给0
                if (runMileage.compareTo(BigDecimal.ZERO) > 0) {
                    runMileage = BigDecimal.ZERO;
                    propRunTime = Integer.MAX_VALUE;
                } else {
                    //没有成绩 退赛 给负数
                    runMileage = BigDecimal.valueOf(-1);
                    propRunTime = -1;
                }

                runActivityUser.setCompleteTime(DateUtil.addDays(now, 9999));
                runActivityUser.setRunMileage(runMileage);
                runActivityUser.setPropRunTime(propRunTime);
            }
            //更新名字
            runActivityUser.setNickname(userMap.get(runActivityUser.getUserId()).get(0).getFirstName());
            //配速
            runActivityUser.setAverageVelocity(SportsDataUnit.getVelocity(runActivityUser.getPropRunTime(), runActivityUser.getRunMileage()));
        }

        //预排序
        allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getCreateTime))
                .sorted(Comparator.comparing(ZnsRunActivityUserEntity::getNickname)).collect(Collectors.toList());


        //用户排名当前单选
        if ("1".equals(rankBy)) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getRunMileage).reversed()).collect(Collectors.toList());
        } else if ("2".equals(rankBy)) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getPropRunTime)).collect(Collectors.toList());
        } else if ("3".equals(rankBy)) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getCompleteTime)).collect(Collectors.toList());
        } else if ("4".equals(rankBy)) {
            allActivityUserList = allActivityUserList.stream().sorted(Comparator.comparing(ZnsRunActivityUserEntity::getAverageVelocity).reversed()).collect(Collectors.toList());
        }
        for (int i = 0; i < allActivityUserList.size(); i++) {
            ZnsRunActivityUserEntity runActivityUser = allActivityUserList.get(i);
            runActivityUser.setRank(i + 1);
        }

        list = allActivityUserList.stream().map(k -> {
            SeriesActivityRankDto rankDto = new SeriesActivityRankDto();
            rankDto.setNickname(k.getNickname());
            rankDto.setRank(k.getRank());
            rankDto.setUserId(k.getUserId());
            rankDto.setRunTime(k.getPropRunTime());
            rankDto.setAverageVelocity(k.getAverageVelocity());
            rankDto.setRunMileage(k.getRunMileage().intValue());
            rankDto.setCompleteTime(k.getCompleteTime());
            rankDto.setCompleteState(k.getIsComplete());
            if (k.getRunMileage().compareTo(BigDecimal.valueOf(-1)) == 0) {
                rankDto.setCompleteState(2);
            }
            return rankDto;
        }).collect(Collectors.toList());

        System.out.println(list);
        return CommonResult.success(list);

    }

    @RequestMapping("/buy")
    public Result buy(String type, Long id) {
        PaypalPay pay = paypalPayService.getById(id);
        switch (type) {
            case "coupon":
                payBussiness.buyCoupon(pay);
                break;
            case "vip":
                payBussiness.buyVip(pay);
                break;
            case "scoreExchange":
                payBussiness.scoreExchange(pay);
                break;
            case "exchangeWear":
                payBussiness.exchangeWear(pay);
                break;
            case "battlePass":
                payBussiness.buyBattlePass(pay);
                break;
        }
        return CommonResult.success();
    }

    @GetMapping("/testTime")
    public void testTime() {
        ZonedDateTime date = ZonedDateTime.now();
        log.info("date=={}", date);
        ZonedDateTime startOfDate = DateUtil.getStartOfDate(date);
        log.info("startOfDate=={}", startOfDate);
    }

    @GetMapping("/checkRot")
    public Result<Boolean> checkRot(Long rotId) {
        log.info("开始check，rotId{}", rotId);
        return CommonResult.success(userService.checkRotInfo(rotId));
    }

    @GetMapping("/cache")
    public Object cache(String key) {
        Object o = redisTemplate.opsForValue().get(key);
        return o;
    }

    @GetMapping("/automaticEnrollingTeam")
    public void automaticEnrollingTeam() {
        robotTask.automaticEnrollingTeam(ZonedDateTime.now());
    }

    @GetMapping("/newAdd")
    public void newAdd() {
        robotTask.automaticAdmission();
    }

    @GetMapping("/addEnter")
    public void addEnter() {
        robotTask.automaticEnrolling();
    }

    @GetMapping("/mainActivity")
    public void mainActivity(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        List<MainActivity> list = new ArrayList<>();
        list.add(mainActivity);
        robotTask.automaticAdmissionNewActivity(list, ZonedDateTime.now());
    }
}
