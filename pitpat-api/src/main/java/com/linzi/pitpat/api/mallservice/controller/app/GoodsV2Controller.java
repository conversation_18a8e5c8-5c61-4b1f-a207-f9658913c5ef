package com.linzi.pitpat.api.mallservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.IpUtil;
import com.linzi.pitpat.core.util.RegionUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsDetailApiResponseDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsJumpInfoResponseDTO;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsSkuDetailApiResponseDto;
import com.linzi.pitpat.data.mallservice.dto.request.GoodsInfoReq;
import com.linzi.pitpat.data.mallservice.dto.response.AllowanceGoodsRespDto;
import com.linzi.pitpat.data.mallservice.dto.response.MallCategoryRespDto;
import com.linzi.pitpat.data.mallservice.manager.api.GoodsManager;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 商品相关接口V2
 *
 * <AUTHOR>
 * @date 2024/11/5 10:56
 */
@RestController
@Slf4j
@RequestMapping("/app/mall/goods")
@RequiredArgsConstructor
public class GoodsV2Controller extends BaseAppController {
    private final GoodsManager goodsManager;

    /**
     * 商品详情
     */
    @PostMapping("/info")
    public Result<GoodsDetailApiResponseDto> goodsInfo(@RequestBody GoodsInfoReq req) {
        String zoneId = getZoneId();
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        return CommonResult.success(goodsManager.goodsInfo(req, getLanguageCode(), zoneId, user, false));
    }
    /**
     * 商品详情-投流接口，无验签
     * @since 4.7.2
     */
    @PostMapping("/info/surrender")
    public Result<GoodsDetailApiResponseDto> goodsInfoSurrender(@RequestBody GoodsInfoReq req) {
        String zoneId = getZoneId();
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        return CommonResult.success(goodsManager.goodsInfo(req, getLanguageCode(), zoneId, user, false));
    }

    /**
     * 商品首页跳转类型
     *
     * @param req
     * @return
     */
    @PostMapping("/jumpInfo")
    public Result<GoodsJumpInfoResponseDTO> jumpInfo(@RequestBody GoodsInfoReq req) {
        return CommonResult.success(goodsManager.goodsJumpInfo(req.getGoodsId()));
    }


    /**
     * 商品sku列表
     */
    @PostMapping("/sku/list")
    public Result<List<GoodsSkuDetailApiResponseDto>> skuList(@RequestBody GoodsInfoReq req) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(goodsManager.skuList(req, getLanguageCode(), loginUser));
    }
    /**
     * 商品sku列表--投流接口，无验签
     * @since 4.7.2
     */
    @PostMapping("/sku/list/surrender")
    public Result<List<GoodsSkuDetailApiResponseDto>> skuListSurrender(@RequestBody GoodsInfoReq req) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(goodsManager.skuList(req, getLanguageCode(), loginUser));
    }
    /**
     * 津贴可用商品列表
     */
    @PostMapping("/allowance/list")
    public Result<AllowanceGoodsRespDto> allowanceGoodsList() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String countryName = RegionUtil.getCountry(IpUtil.getRemoteIp(request));
        String countryCode = null;
        for (I18nConstant.CountryCodeEnum value : I18nConstant.CountryCodeEnum.values()) {
            if (Objects.equals(countryName,value.getName())){
                countryCode =  value.getCode();
                break;
            }
        }
        return CommonResult.success(goodsManager.allowanceGoodsList(getLoginUserOrDefaultUser(),countryCode));
    }
    /**
     * 投流商品列表
     */
    @PostMapping("/surrender/list")
    public Result<List<MallCategoryRespDto>> surrenderGoodsList() {
        return CommonResult.success(goodsManager.surrenderGoodsList(getLoginUserOrDefaultUser().getLanguageCode()));
    }
}
