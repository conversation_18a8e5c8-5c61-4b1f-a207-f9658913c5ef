package com.linzi.pitpat.api.activityservice.vo;

import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamJoinStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityTeamTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class TeamRank {

    //当前用户队伍id（先放这里吧）
    private Long currentUserTeamId;

    //队伍id
    private Long teamId;

    //排名
    private Integer rank;

    //队伍名称
    private String teamName;

    //队伍logo
    private String teamLogo;

    //最大人数
    private Integer maxNum;

    //当前人数
    private Integer currentNum;

    //里程
    private Integer mileage;

    //是否官方队伍 1是 0不是 默认1
    private Integer isOfficial;
    /**
     * 是否可以加入的状态 "ALLOW" 允许加入 "DENY" 不允许加入
     *
     * @see ActivityTeamJoinStateEnum
     */
    private String canJoinState;
    /**
     * x
     * 队伍类型'队伍类型
     *
     * @see ActivityTeamTypeEnum
     */
    private String teamType;

    /**
     * 俱乐部id
     */
    private Long clubId;
    /**
     * 队长Id
     */
    private Long teamManagerId;
    private ZonedDateTime gmtCreate;

    public Boolean notFull() {

        return currentNum < maxNum;

    }


}
