package com.linzi.pitpat.api.activityservice.controller;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.response.marathon.MarathonInvitePopMallGoodsResponse;
import com.linzi.pitpat.api.mallservice.dto.request.ReceiveCouponRequestDto;
import com.linzi.pitpat.api.mallservice.mananger.MallCouponManager;
import com.linzi.pitpat.api.mallservice.mananger.MallHomePageManager;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.marathon.MarathonInvitePopButtonStatus;
import com.linzi.pitpat.data.activityservice.constant.enums.marathon.MarathonInviteeTypeStatus;
import com.linzi.pitpat.data.activityservice.dto.api.request.marathon.MarathonInvitePopRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.marathon.MarathonInviteUrlCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.marathon.MarathonInviteUrlResponse;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTeam;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.service.ActivityTeamService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MarathonInvitationRecordsService;
import com.linzi.pitpat.data.activityservice.service.MarathonInvitationUrlService;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mallservice.dto.response.RecommendGoodsRespDto;
import com.linzi.pitpat.data.mallservice.manager.api.GoodsManager;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 马拉松邀请
 *
 * @since 4.6.1
 */
@RestController
@RequestMapping({"/app/marathon"})
@RequiredArgsConstructor
@Slf4j
public class MarathonInviteController extends BaseAppController {

    private final MallCouponManager couponManager;

    private final MallHomePageManager mallHomePageManager;

    private final MallCouponManager mallCouponManager;

    private final MarathonInvitationUrlService marathonInvitationUrlService;

    private final ISysConfigService sysConfigService;

    private final ActivityTeamService activityTeamService;

    private final MarathonInvitationRecordsService marathonInvitationRecordsService;

    private final MainActivityService mainActivityService;

    private final GoodsManager goodsManager;

    /**
     * 创建马拉松邀请链接
     *
     * @since 4.6.1
     */
    @PostMapping("/createInviteUrl")
    public Result<MarathonInviteUrlResponse> createInviteUrl(@RequestBody MarathonInviteUrlCreateRequest request) {
        if (request.getActivityId() == null || request.getTeamId() == null) {
            return CommonResult.fail(CommonError.PARAM_LACK);
        }
        ZnsUserEntity loginUser = getLoginUser();
        String url = marathonInvitationUrlService.getOrCreateInvitationUrl(request.getActivityId(), request.getTeamId(), loginUser.getId());
        MarathonInviteUrlResponse data = new MarathonInviteUrlResponse(url);
        data.setActivityId(request.getActivityId());
        data.setTeamId(request.getTeamId());
        data.setInviteId(loginUser.getId());
        return CommonResult.success(data);
    }

    /**
     * 马拉松邀请弹框
     *
     * @since 4.6.1
     */
    @PostMapping("/invitePop")
    @FillerMethod
    public Result<MarathonInvitePopMallGoodsResponse> marathonInvitePop(@RequestBody MarathonInvitePopRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        if(Objects.isNull(request.getTeamId())||0L==request.getTeamId()){
            return CommonResult.success();
        }
        String couponIdStr = sysConfigService.selectConfigByKey("marathon.invite.couponId", true);
        Long couponId = Long.parseLong(couponIdStr);

        Long activityId = request.getActivityId();
        Boolean marathon = sysConfigService.isMarathon(activityId);
        if (!marathon) {
            throw new RuntimeException("Activity is Not Marathon Activity");
        }

        MarathonInvitePopMallGoodsResponse response = new MarathonInvitePopMallGoodsResponse();
        ReceiveCouponRequestDto couponRequest = new ReceiveCouponRequestDto();
        couponRequest.setCouponId(couponId);
        response.setCouponDto(couponManager.couponDetail(couponRequest, loginUser));
        List<RecommendGoodsRespDto> recommendGoodsRespDtos = goodsManager.goodsRespByCouponId(couponId, loginUser);
        response.setMallHomeGoodDtoList(recommendGoodsRespDtos);
        response.setInviteId(request.getInviteId());
        response.setIsNew(request.getNewRegister());
        response.setInviteId(request.getInviteId());
        response.setActivityTeamId(request.getTeamId());
        //保存邀请记录状态
//        request.setNewRegister(isNewRegister(loginUser, request.getSource()) ? 1 : 0);
        request.setNewRegister(0);
        MarathonInviteeTypeStatus invitation = marathonInvitationRecordsService.createInvitation(request.getInviteId(), request.getActivityId(), request.getTeamId(), loginUser.getId(), request.isNewRegister() ? MarathonInviteeTypeStatus.NEW : MarathonInviteeTypeStatus.OLD);
//        if (invitation.isNew()) {
//            response.setIsNew(1);
//            //发放优惠券
//            ReceiveCouponRequestDto receiveCouponRequestDto = new ReceiveCouponRequestDto();
//            receiveCouponRequestDto.setCouponId(couponId);
//            receiveCouponRequestDto.setIsRetainPop(false);
//            mallCouponManager.receive(receiveCouponRequestDto, loginUser);
//        } else {
            response.setIsNew(0);
//        }
        MainActivity mainActivity = mainActivityService.findById(activityId);
        boolean b = mainActivity.canSignUp(getZoneId());
        if (!b) {
            response.setButtonStatus(MarathonInvitePopButtonStatus.TIME_IS_OUT);
        } else {
            ActivityTeam byId = activityTeamService.findById(request.getTeamId());
            response.setButtonStatus(byId.teamIsFull() ? MarathonInvitePopButtonStatus.TEAM_IS_FULL : MarathonInvitePopButtonStatus.NORMAL);
        }
        return CommonResult.success(response);
    }

//    private boolean isNewRegister(ZnsUserEntity user, String source) {
//        return false;
//
//        if (!"third".equals(source)) {
//            return false;
//        }
//        String newUserDateRangeJson = sysConfigService.selectConfigByKey("marathon.invite.newUser.dateRange", true);
//        List<String> rangeDateStrs = JsonUtil.readList(newUserDateRangeJson, String.class);
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.systemDefault());
//        ZonedDateTime start = ZonedDateTime.parse(rangeDateStrs.get(0), formatter);
//        ZonedDateTime end = ZonedDateTime.parse(rangeDateStrs.get(1), formatter);
//        return start.isBefore(ZonedDateTimeUtil.convertFrom(user.getCreateTime())) && end.isAfter(ZonedDateTimeUtil.convertFrom(user.getCreateTime()));
//    }
}
