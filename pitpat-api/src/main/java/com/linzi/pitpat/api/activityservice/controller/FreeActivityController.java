package com.linzi.pitpat.api.activityservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.request.FreeChallengeActivityTopListRequest;
import com.linzi.pitpat.api.activityservice.dto.request.FreeChallengeActivityTopLocalListRequest;
import com.linzi.pitpat.core.util.PageHelper;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeChallengeActivityTopListResponse;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.manager.api.FreeChallengeActivityManager;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 自由挑战跑活动
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@RestController
@RequestMapping("/app/activity/free")
@Slf4j
@RequiredArgsConstructor
public class FreeActivityController extends BaseAppController {
    private final FreeChallengeActivityManager freeChallengeActivityManager;
    /**
     * 获取自由挑战跑排行榜
     * @param request
     * @return
     * @since 4.7.4
     */
    @PostMapping("/topList")
    @FillerMethod
    public Result<FreeChallengeActivityTopListResponse> topList(@RequestBody FreeChallengeActivityTopListRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(freeChallengeActivityManager.topList(request.getActivityId(), request.getIsRefresh(), loginUser.getId(), Page.of((long)request.getPageNum(), (long)request.getPageSize())));
    }

    /**
     * 获取自由挑战跑排行榜
     * @param request
     * @return
     * @since 4.7.4
     */
    @PostMapping("/topList/local")
    @FillerMethod
    public Result<FreeChallengeActivityTopListResponse> topListLocal(@RequestBody FreeChallengeActivityTopLocalListRequest request) {
        return CommonResult.success(freeChallengeActivityManager.topListLocal(request.getDeviceLocation(), Page.of((long)request.getPageNum(), (long)request.getPageSize())));
    }
}
