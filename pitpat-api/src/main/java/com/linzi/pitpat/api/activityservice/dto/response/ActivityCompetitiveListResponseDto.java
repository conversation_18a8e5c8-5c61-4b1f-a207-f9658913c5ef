package com.linzi.pitpat.api.activityservice.dto.response;

import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CompetitiveCountDownDto;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.CoverButtonEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsCategoryDetailDo;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class ActivityCompetitiveListResponseDto {

    //活动ID
    private Long activityId;

    //赛事类型 single单赛事 series-main系列赛主赛事 series-sub系列赛阶段赛事old老赛事 single-polymerization-running
    private String mainType;

    //赛事封面
    private String activityCoverImage;

    //赛事标题
    private String activityTitle;

    private ZonedDateTime gmtCreate;
    /**
     * 竞技赛图标
     */
    private String competitiveIconUrl;
    /**
     * 活动状态： 0 已上架，不可报名 1 进行中 2 已结束  4  可报名 3 下架 -1 未上架
     */
    private Integer activityState;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;

    //报名开始时间
    private ZonedDateTime applicationStartTime;
    //报名结束时间
    private ZonedDateTime applicationEndTime;

    //	string	跳转路径/路由
    private String url;
    //n	string	跳转参数，json字符串
    private String jumpParam;

    /**
     * 竞技赛类型
     *
     * @see ActivityCompetitiveSeasonType
     */
    private String competitiveSeasonType;

    /**
     * @see com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonSeasonSubType
     * competitiveSeasonType 季度子类型
     * SEASONAL
     * ANNUAL 年度写年份
     */
    private String competitiveSeasonSubType;

    /**
     * 竞技赛年份
     */
    private Integer competitiveSeasonYear;

    /**
     * icon url
     */
    private String competitiveSeasonIconUrl;

    // 竞技赛倒计时配置
    private CompetitiveCountDownDto competitiveCountDownDto;

    /**
     * 奖金是否发放完成
     */
    private Integer awardSendFinish;
    /**
     * 集锦id
     */
    private Long highlightsId;

    /**
     * 0 不是 1 是
     */
    private Integer isNew = 0;
    /**
     * 用户状态 0未报名 1已报名
     */
    private Integer userState = 0;

    /**
     * 集锦是否生效 0不生效 1生效
     */
    private Integer highlightState = 0;
    /**
     * 集锦封面视频
     */
    private String highlightsCoverDetail;
    /**
     * 集锦视频
     */
    private List<ActivityHighlightsCategoryDetailDo> categoryDetails;
    /**
     * 按钮
     */
    private List<CoverButtonEnum> button;
    /**
     * 前x位用户名
     */
    private List<String> usernames;

    /**
     * 是否是待报名状态
     */
    private boolean unRegisterActivity;
    //是否外卡用户 0不是 1是
    private Integer isForeign = 0;

    //达到竞技分要求 0 达到了  1 未达到
    private Integer notReachCompetitionScoreThreshold;
}
