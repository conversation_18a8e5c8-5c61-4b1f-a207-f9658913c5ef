package com.linzi.pitpat.api.config;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;


/**
 * 程序注解配置
 *
 * <AUTHOR> Li
 */
@Slf4j
@Configuration
// 表示通过aop框架暴露该代理对象,AopContext能够访问
@EnableAspectJAutoProxy(exposeProxy = true)
public class ApplicationConfig {

    /**
     * 处理 POST 请求的 request body 和 response body
     *
     * @return
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            builder.serializerByType(ZonedDateTime.class, new ZonedDateTimeSerializer(dateTimeFormatter));
            builder.deserializerByType(ZonedDateTime.class, new ZonedDateTimeDeserializer(dateTimeFormatter));
        };
    }


    /**
     * 处理  GET 参数转换
     * description:java.util.Date转换器
     * 接收毫秒级时间戳字符串——>Date
     */
    @Bean
    public Converter<String, Date> dateConverter() {
        return new Converter<String, Date>() {
            @Override
            public Date convert(String source) {
                try {
                    long longTimeStamp = Long.parseLong(source);
                    if (longTimeStamp <= 0) {
                        return null;
                    }
                    return new Date(longTimeStamp);
                } catch (Exception e) {
                    if (StringUtils.hasText(source)) {
                        return DateUtil.parseDate(source);
                    }
                }
                return null;
            }
        };
    }


    /**
     * 处理 GET 参数转换
     * description:java.util.Date转换器
     * 接收毫秒级时间戳字符串——>Date
     */
    @Bean
    public Converter<String, ZonedDateTime> ZonedDateConverter() {
        return new Converter<String, ZonedDateTime>() {
            @Override
            public ZonedDateTime convert(String source) {
                try {
                    long dateTimeStr = Long.parseLong(source);
                    ZonedDateTime shanghaiTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(dateTimeStr), ZoneId.of("UTC"));
                    log.info("utcTime={},{}", shanghaiTime, shanghaiTime.toEpochSecond());
                    return shanghaiTime;
                } catch (Exception e) {
                    log.error("解析 ZonedDateTime 失败,", e);
                }
                return null;
            }
        };
    }

}





