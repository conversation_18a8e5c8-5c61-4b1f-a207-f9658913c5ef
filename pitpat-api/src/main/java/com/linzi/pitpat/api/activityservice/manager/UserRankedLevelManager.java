package com.linzi.pitpat.api.activityservice.manager;

import com.linzi.pitpat.api.activityservice.dto.RankedActivityConfigDto;
import com.linzi.pitpat.api.activityservice.dto.request.RankedActivityConfigRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.RankedActivityQueryDto;
import com.linzi.pitpat.api.activityservice.dto.response.RankedActivityConfigResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.UserRankedLevelMatchResponseDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.MindUserMatchBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RankedLevelEnums;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityPaceSetting;
import com.linzi.pitpat.data.activityservice.dto.console.request.ActivityTargetAwardDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.BrandRightDto;
import com.linzi.pitpat.data.activityservice.dto.console.request.EnableActStatusRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.RotSetting;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityFeeAndAwardCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityReportCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleActivityRuleCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.console.request.SingleDistributionCreateRequest;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.Gameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.RankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.RunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevelLog;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedMatchEntity;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedMatchLineEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.UserRankedMatchQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardConfigDto;
import com.linzi.pitpat.data.activityservice.model.query.award.ActivityAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.AmountCurrencyDto;
import com.linzi.pitpat.data.activityservice.model.query.award.CouponAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.vo.MatchRankedLevelVo;
import com.linzi.pitpat.data.activityservice.service.GameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.RankedLevelService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelLogService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.UserRankedMatchLineService;
import com.linzi.pitpat.data.activityservice.service.UserRankedMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.RankedConstant;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.AutomaticAdmissionDealNewActivityDto;
import com.linzi.pitpat.data.entity.dto.DelayDto;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.RouteShowLocationEnum;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.RunRouteVO;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 用户段位信息
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserRankedLevelManager {
    private final UserRankedLevelService userRankedLevelService;
    private final RankedLevelService rankedLevelService;
    private final ZnsRunRouteService runRouteService;
    private final ZnsRunActivityConfigService znsRunActivityConfigService;
    private final ZnsUserService userService;
    private final MindUserMatchBizService mindUserMatchBizService;
    private final ISysConfigService sysConfigService;
    private final RunRankedActivityUserService runRankedActivityUserService;
    private final MindUserMatchService mindUserMatchService;
    private final GameplayService gameplayService;
    private final RedissonClient redissonClient;
    private final ZnsRunActivityUserService runActivityUserService;
    private final MainActivityService mainActivityService;
    private final UserRankedLevelLogService userRankedLevelLogService;
    private final UserRankedMatchService userRankedMatchService;
    private final UserRankedMatchLineService userRankedMatchLineService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final MainActivityBizService mainActivityBizService;
    private final RabbitTemplate rabbitTemplate;
    @Value("${zns.config.rabbitQueue.delay_exchange_name}")
    private String delayExchangeName;

    /**
     * 投放机器人延迟队列
     */
    @Value("${" + RabbitQueueConstants.RANK_MATCH_PUT_IN_ROBOT_DELAY_EXCHANGE_V2 + "}")
    private String rankMatchPutInRobotDelayExchange;

    private static final Random random = new Random();


    private UserRankedLevelLog getUserRankedLevelLog(RankedActivityQueryDto queryDto) {
        int loop = 0;
        UserRankedLevelLog userRankedLevelLog = null;
        while (loop++ < 5) {

            userRankedLevelLog = userRankedLevelLogService.findByUserIdAndActivityId(queryDto.getUserId(), queryDto.getActivityId());
            if (Objects.isNull(userRankedLevelLog)) {
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (InterruptedException e) {
                    log.error("获取 UserRankedLevelLog 中断", e);
                }
                log.info("第{} 获取 UserRankedLevelLog， userId={}, activityId={}", loop, queryDto.getUserId(), queryDto.getActivityId());
            } else {
                break;
            }
        }
        return userRankedLevelLog;
    }

    public RankedActivityConfigResponseDto findRankedActivityConfig(Integer appVersion, ZnsUserEntity user) {
        ZnsRunActivityConfigEntity runActivityConfig = znsRunActivityConfigService.getByType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType(), null);
        RankedActivityConfigDto dto = JsonUtil.readValue(runActivityConfig.getActivityConfig(), RankedActivityConfigDto.class);

        String sysConfig = sysConfigService.selectConfigByKey("new_ranked_activity_config");
        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);

        RankedActivityConfigResponseDto responseDto = new RankedActivityConfigResponseDto();
        responseDto.setMilesTarget(dto.getMilesTarget());

        //获取路线详情
        List<RunRouteVO> runRouteVoList = runRouteService.runRouteListVo(2, RouteShowLocationEnum.getAllList(), appVersion);
        runRouteVoList = runRouteVoList.stream().filter(item -> dto.getRouteList().contains(item.getRouteId())).toList();
        responseDto.setRouteList(runRouteVoList);

        //获取语音开关
        Integer voiceSwitch = znsRunActivityConfigService.getVoiceSwitch(dto.getVoiceSwitch(), user);
        dto.setVoiceSwitch(voiceSwitch);

        responseDto.setWaitTime(MapUtil.getInteger(rankedSysConfig.get("waitTime")));
        responseDto.setPossibleWaitSecond(MapUtil.getInteger(rankedSysConfig.get("possibleWaitSecond")));
        return responseDto;
    }


    private Long getEpochMilli(String timeZone, MainActivity rankActivity) {
        String activityStartTime = rankActivity.getActivityStartTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        ZonedDateTime zonedDateTime = ZonedDateTime.parse(activityStartTime, formatter.withZone(ZoneOffset.UTC));

        log.info("zonedDateTime={}", zonedDateTime.toInstant().toEpochMilli());

        log.info("zonedDateTime2={}", zonedDateTime.toInstant().toEpochMilli());
        return zonedDateTime.toInstant().toEpochMilli();
    }

    private List<RankedLevel> listMatchedRankedLevelList(UserRankedLevel myUserRankedLevel, BigDecimal minHiddenScore, BigDecimal maxHiddenScore) {
        List<RankedLevel> allRankedLevelList = rankedLevelService.findList();
        List<RankedLevel> rankedLevels = allRankedLevelList.stream()
                .filter(item -> item.getLevel().equals(myUserRankedLevel.getLevel()))
                .toList();
        //如果根据用户计算出的隐藏分无法获得匹配机器人对应的段位信息，则使用当前发起者的段位信息
        if (CollectionUtils.isEmpty(rankedLevels)) {
            rankedLevels = allRankedLevelList.stream().filter(item -> Objects.equals(item.getLevel(), myUserRankedLevel.getRank()) && Objects.equals(item.getRank(), myUserRankedLevel.getRank())).toList();
        }
        rankedLevels.forEach(item -> log.info("level={}", item));
        return rankedLevels;
    }

    private BigDecimal getUserHideScore(UserRankedLevel userRankedLevel, Map<String, Object> rankedSysConfig) {
        BigDecimal userScore = getUserScore(userRankedLevel, rankedSysConfig);

        //机器人段位：当前发起人段位中位数*0.8 + 当前发起人隐藏分*0.2 +- 3
        RankedLevel rankedLevel = rankedLevelService.findById(userRankedLevel.getRankedLevelId());
        if (Objects.isNull(rankedLevel)) {
            rankedLevel = rankedLevelService.findByLevelAndRank(RankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank());
        }

        //段位中位数的隐藏分
        BigDecimal hiddenScore = rankedLevel.getMaxScore().add(rankedLevel.getMinScore()).divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.8));
        //加上用户隐藏分* 0.2
        hiddenScore = hiddenScore.add(userScore.multiply(BigDecimal.valueOf(0.2D))).setScale(2, RoundingMode.HALF_UP);
        return hiddenScore;
    }

    /**
     * 获取用户的隐藏分，可能会触发放挫败机制
     *
     * @param userRankedLevel
     * @param rankedSysConfig
     */
    private BigDecimal getUserScore(UserRankedLevel userRankedLevel, Map<String, Object> rankedSysConfig) {
        BigDecimal userScore = userRankedLevel.getScore();
        //判断是否需要启动防挫败机制
        List<RunRankedActivityUser> userRankedActivityDataList = runRankedActivityUserService.findListByUserId(userRankedLevel.getUserId());
        if (CollectionUtils.isEmpty(userRankedActivityDataList) || userRankedActivityDataList.size() < 10) {
            int rackedActivityCount = 0;
            if (!CollectionUtils.isEmpty(userRankedActivityDataList)) {
                rackedActivityCount = userRankedActivityDataList.size();
            }
            log.info("数据不足，不需要启动防挫败机制,userId={}, score={}, 完赛次数={}", userRankedLevel.getUserId(), userScore, rackedActivityCount);
        } else {
            double defaultAverageRank = MapUtils.getDouble(rankedSysConfig, "defaultAverageRank");
            double averageRank = userRankedActivityDataList.stream().mapToInt(RunRankedActivityUser::getRank).average().orElse(defaultAverageRank);
            //平均10场活动排名靠后,触发防挫败机制
            if (Double.compare(averageRank, defaultAverageRank) > 0) {
                //8 未排位赛参赛用户数量
                userScore = userRankedLevel.getScore().multiply(BigDecimal.valueOf(8).subtract(BigDecimal.valueOf(averageRank)).divide(BigDecimal.valueOf(7), 2, RoundingMode.HALF_UP).add(BigDecimal.valueOf(0.5))).setScale(2, RoundingMode.HALF_UP); //((8 - averageRank) / 7 + 0.5))
                log.info("{}*((8-{})/7+0.5) = {}", userRankedLevel.getScore(), averageRank, userScore);
                log.info("用户触发排位赛启挫败机制,userId={}, userScore={},calcUserScore={}, averageRank={} ,defaultAverageRank={}", userRankedLevel.getUserId(), userRankedLevel.getScore(), userScore, averageRank, defaultAverageRank);
            }
        }
        log.info("getUserScore={}", userScore);
        return userScore;
    }


    /**
     * 构造新活动对象
     *
     * @param dto
     * @return
     */
    private SingleActivityCreateRequest buildSingleActivityCreateRequest(RankedActivityConfigRequestDto dto) {
        SingleActivityCreateRequest request = new SingleActivityCreateRequest();

        String sysConfig = sysConfigService.selectConfigByKey("new_ranked_activity_config");
        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);

        ZnsRunActivityConfigEntity runActivityConfig = znsRunActivityConfigService.getByType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType(), null);
        RankedActivityConfigDto runActivityTemplate = JsonUtil.readValue(runActivityConfig.getActivityConfig(), RankedActivityConfigDto.class);

        //赛事规则实体
        request.setSingleActivityRuleCreateRequest(buildSingleActivityRuleCreateRequest(dto, rankedSysConfig, runActivityTemplate));
        //赛事报名实体
        request.setSingleActivityReportCreateRequest(buildSingleActivityReportCreateRequest(dto, runActivityTemplate));
        //赛事费用奖励实体
        request.setSingleActivityFeeAndAwardCreateRequest(buildSingleActivityFeeAndAwardCreateRequest(dto, runActivityTemplate));
        //赛事说明宣发实体
        request.setSingleActivityDistributionCreateRequest(buildSingleActivityDistributionCreateRequest(dto, runActivityTemplate));

        log.info("奖励 json={}", request);
        return request;
    }

    private SingleActivityDistributionCreateRequest buildSingleActivityDistributionCreateRequest(RankedActivityConfigRequestDto dto, RankedActivityConfigDto runActivityTemplate) {
        SingleActivityDistributionCreateRequest request = new SingleActivityDistributionCreateRequest();

        List<SingleDistributionCreateRequest> createRequestList = Arrays.stream(I18nConstant.LanguageCodeEnum.VALUES).map(item -> {
            SingleDistributionCreateRequest createRequest = new SingleDistributionCreateRequest();
            createRequest.setShowMode(0);
            createRequest.setIsDefault(Objects.equals(item.getCode(), I18nConstant.LanguageCodeEnum.en_US.getCode()) ? 1 : 0);
            createRequest.setLanguageCode(item.getCode());

            String title = I18nMsgUtils.getLangMessage(item.getCode(), "rankedActivity.title");
            createRequest.setTitle(title);
            createRequest.setCategoryType(5);//段位匹配赛
            createRequest.setMarquee(0);
            createRequest.setEnableRankBeforeEnd(1);
            createRequest.setShowUserSameAct(1);
            return createRequest;
        }).toList();

        request.setDistributionRequests(createRequestList);
        return request;
    }

    private SingleActivityFeeAndAwardCreateRequest buildSingleActivityFeeAndAwardCreateRequest(RankedActivityConfigRequestDto dto, RankedActivityConfigDto runActivityTemplate) {

        ActivityTargetAwardDto activityTargetAwardDto = new ActivityTargetAwardDto();
        //跑步里程
        activityTargetAwardDto.setTarget(dto.getMileTarget());

        //完赛奖励
        RankedActivityConfigDto.Award basicAward = runActivityTemplate.getBasicAward();
        ActivityAwardConfigDto activityAwardConfigDto = new ActivityAwardConfigDto();

        activityAwardConfigDto.setType(AwardSentTypeEnum.COMPLETING_THE_GAME.getType());
        activityAwardConfigDto.setAwardLists(List.of(buildActivityAward(dto, basicAward)));

        //排名
        List<RankedActivityConfigDto.Award> rankingAwardList = runActivityTemplate.getRankingAwardList();
        ActivityAwardConfigDto rankingActivityAward = new ActivityAwardConfigDto();
        rankingActivityAward.setType(AwardSentTypeEnum.RANKING_BASED_REWARDS.getType());
        rankingActivityAward.setAwardLists(rankingAwardList.stream().map(item -> buildActivityAward(dto, item)).toList());

        //段位
        List<RankedActivityConfigDto.Award> rankAwardList = runActivityTemplate.getRankedAwardList();
        ActivityAwardConfigDto rankActivityAward = new ActivityAwardConfigDto();
        rankActivityAward.setType(AwardSentTypeEnum.RANK_AWARD.getType());
        rankActivityAward.setAwardLists(rankAwardList.stream().map(item -> buildActivityAward(dto, item)).toList());

        //开始构建request
        SingleActivityFeeAndAwardCreateRequest request = new SingleActivityFeeAndAwardCreateRequest();
        List empty = List.of();

        request.setLunchChallengeScore(null);
        request.setCouponId(null);
        request.setAllowCoupon(0);
        request.setType("free");

        List<CurrencyAmount> amounts = Arrays.stream(I18nConstant.CurrencyCodeEnum.VALUES).map(item -> {
            Currency currency = I18nConstant.buildCurrency(item.getCode());
            CurrencyAmount currencyAmount = new CurrencyAmount();
            BeanUtils.copyProperties(currency, currencyAmount);
            currencyAmount.setAmount(BigDecimal.ZERO);
            return currencyAmount;
        }).toList();

        request.setAmounts(amounts);
        //request.setAmounts(empty);
        request.setScore(0);
        BrandRightDto brandRightDto = new BrandRightDto();
        brandRightDto.setBrandRightAwardDtos(List.of());
        request.setBrandRightDto(brandRightDto);


        //构建奖励

        activityTargetAwardDto.setAwardDtos(List.of(activityAwardConfigDto, rankingActivityAward, rankActivityAward));
        request.setTargetAwardDtos(List.of(activityTargetAwardDto));

        return request;
    }

    private static ActivityAwardDto buildActivityAward(RankedActivityConfigRequestDto dto, RankedActivityConfigDto.Award basicAward) {
        ActivityAwardDto activityAwardDto = new ActivityAwardDto();
        setAwardBasicInfo(dto, activityAwardDto, basicAward);
        //积分
        activityAwardDto.setScore(basicAward.getScore());

        if (!CollectionUtils.isEmpty(basicAward.getAmountList())) {
            //现金
            List<AmountCurrencyDto> amountList = new ArrayList<>();
            for (RankedActivityConfigDto.Award.AmountInfo item : basicAward.getAmountList()) {
                Currency currency = I18nConstant.buildCurrency(item.getCurrencyCode());
                if (currency != null) {
                    AmountCurrencyDto amountCurrencyDto = new AmountCurrencyDto();
                    BeanUtils.copyProperties(currency, amountCurrencyDto);
                    amountCurrencyDto.setAmount(BigDecimal.valueOf(item.getAmount()));
                    amountList.add(amountCurrencyDto);
                }
            }
            activityAwardDto.setAmountLists(amountList);
        }

        //服装
        RankedActivityConfigDto.Award.WearAward wearAwardConf = basicAward.getWearAwardDto();
        WearAwardDto wearAwardDto = new WearAwardDto();

        if (!Objects.isNull(wearAwardConf)) {
            BeanUtils.copyProperties(wearAwardConf, wearAwardDto);
            activityAwardDto.setWearAwardDto(wearAwardDto);
        }

        //优惠券
        RankedActivityConfigDto.Award.CouponAward couponAwardConf = basicAward.getCouponAwardDto();

        CouponAwardDto couponAwardDto = new CouponAwardDto();

        if (!Objects.isNull(couponAwardConf)) {
            BeanUtils.copyProperties(couponAwardConf, couponAwardDto);
            activityAwardDto.setCouponAwardDto(couponAwardDto);
        }

        return activityAwardDto;
    }

    private static void setAwardBasicInfo(RankedActivityConfigRequestDto dto, ActivityAwardDto awardDto, RankedActivityConfigDto.Award basicAward) {
        awardDto.setTarget(dto.getMileTarget());
        awardDto.setTargetType(1);
        awardDto.setRankMin(Optional.ofNullable(basicAward.getRankMin()).orElse(0));
        awardDto.setRankMax(Optional.ofNullable(basicAward.getRankMax()).orElse(0));
    }

    private SingleActivityReportCreateRequest buildSingleActivityReportCreateRequest(RankedActivityConfigRequestDto dto, RankedActivityConfigDto runActivityTemplate) {
        SingleActivityReportCreateRequest request = new SingleActivityReportCreateRequest();
        List empty = List.of();
        request.setEquipments(empty);
        request.setTimeStyle(0);
        //这里先随便设置下时间，后面会另外更新（只是防止创建活动报错）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime now = ZonedDateTime.now().withSecond(0).plusHours(8L);//+8个小时是为了兼容现在的时间是用字符串存储的，
        request.setApplicationStartTime(formatter.format(now)); //报名开始时间 = 现在
        ZonedDateTime applicationEndTime = now.plusMinutes(5); //报名结束时间 = 报名开始时间+5分钟
        request.setApplicationEndTime(formatter.format(applicationEndTime));
        request.setActivityStartTime(formatter.format(applicationEndTime));//活动开始时间 = 报名结束时间
        ZonedDateTime activityEndTime = applicationEndTime.plusMinutes(10);
        request.setActivityEndTime(formatter.format(activityEndTime));//活动结束时间 = 活动开始时间 + 10分钟
        request.setGroupIds(empty);
        request.setRotSetting(new RotSetting());
        request.setHasPacer(0);
        request.setClotheType("0");
        request.setMutexActivityIdList(empty);
        request.setAreas(empty);
        request.setPaceSetting(List.of(new ActivityPaceSetting()));

        return request;
    }

    /**
     * 赛事规则实体
     *
     * @param dto
     * @param rankedSysConfig
     * @param runActivityTemplate
     * @return
     */
    private SingleActivityRuleCreateRequest buildSingleActivityRuleCreateRequest(RankedActivityConfigRequestDto dto, Map<String, Object> rankedSysConfig, RankedActivityConfigDto runActivityTemplate) {
        Gameplay gameplay = gameplayService.findById(MapUtils.getLong(rankedSysConfig, "gamePlayId"));

        SingleActivityRuleCreateRequest request = new SingleActivityRuleCreateRequest();

        List empty = List.of();
        request.setPlayId(gameplay.getId());
        request.setRemark("段位匹配赛");
        request.setTargets(List.of(dto.getMileTarget()));//目标

        request.setSpeedLimit(-1);
        request.setRateLimit(empty);
        request.setAllowProp(0);
        request.setActivityPropConfig(empty);
        request.setCheatSwitch(-1);
        request.setEnterLimit(runActivityTemplate.getPlayerCount());
        request.setUserEnterLimit(-1);
        request.setRouteId(dto.getRouteId());
        request.setSpecialEffectTheme(0);
        request.setRunwaySty("0");
        request.setMusicListId(empty);
        request.setRateLimit(empty);
        request.setTargetType(1);
        if (Objects.equals(runActivityTemplate.getCheatSwitch(), 1)) {
            //开启反作弊
            request.setCheatSwitch(1);
        }


        return request;
    }


    private UserRankedLevel buildUserRankedLevel(ZnsUserEntity user, UserRankedLevel userRankedLevel, RankedLevel rankedLevel) {
        if (Objects.isNull(userRankedLevel)) {
            userRankedLevel = new UserRankedLevel();
            userRankedLevel.setUserId(user.getId());
            userRankedLevel.setIsRobot(user.getIsRobot());
            userRankedLevel.setLevelProgress(BigDecimal.ZERO);
        }
        setRankLevel(userRankedLevel, rankedLevel);
        return userRankedLevel;
    }

    private void setRankLevel(UserRankedLevel userRankedLevel, RankedLevel rankedLevel) {
        userRankedLevel.setRankedLevelId(rankedLevel.getId());
        userRankedLevel.setName(rankedLevel.getName());
        userRankedLevel.setLevel(rankedLevel.getLevel());
        userRankedLevel.setRank(rankedLevel.getRank());
    }

    public MindUserMatch buildMindMatch(Long userId, Integer status, Integer isRobot, Long mindUserMatchId, ZonedDateTime activityEndTime, BigDecimal runMileage, Long activityId, String runMode, Integer runTime) {
        MindUserMatch mindUserMatch = new MindUserMatch();
        mindUserMatch.setUserId(userId);
        mindUserMatch.setStatus(status);
        mindUserMatch.setIsRobot(isRobot);
        mindUserMatch.setMindUserMatchId(mindUserMatchId);
        mindUserMatch.setActivityEndTime(activityEndTime);
        mindUserMatch.setTargetMileage(runMileage.intValue());
        mindUserMatch.setTargetTime(runTime);
        mindUserMatch.setActivityId(activityId);
        mindUserMatch.setRunMode(runMode);
        String uniqueCode = OrderUtil.getUniqueCode("un");
        mindUserMatch.setUniqueCode(uniqueCode);
        return mindUserMatch;
    }

    private static BigDecimal getBigDecimalRandom(BigDecimal min, BigDecimal max, Integer step) {

        List<BigDecimal> bigDecimals = new ArrayList<>();
        for (BigDecimal i = MapUtil.getBigDecimal(min, BigDecimal.ZERO);
             i.compareTo(MapUtil.getBigDecimal(max, BigDecimal.ZERO)) <= 0;
             i = i.add(MapUtil.getBigDecimal(step, BigDecimal.ZERO))) {
            bigDecimals.add(i);
        }
        Random random = new Random();
        int r = random.nextInt(bigDecimals.size());
        return bigDecimals.get(r);
    }

    @Transactional
    public void cancelMatchUser(ZnsUserEntity user) {
        Supplier<Void> supplier = () -> {
            MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchByUserIdUniqueCode(user.getId(), null);
            if (Objects.isNull(mindUserMatch)) {
                log.error("mindUserMatch不存在， userId={}", user.getId());
                return null;
            }
            if (!Arrays.asList(0, 1).contains(mindUserMatch.getStatus())) {
                log.error("{}， userId={}", I18nMsgUtils.getMessage("common.params.error"), user.getId()); // "已经取消过了"
                return null;
            }

            mindUserMatch.setStatus(-1);
            updateMindUserMatchById(mindUserMatch);

            //更新其他对手的状态
            List<MindUserMatch> mindUserMatches = mindUserMatchService.selectMindUserMatchByMatchId(mindUserMatch.getId());
            mindUserMatches.forEach(item -> {
                item.setStatus(-1);
                updateMindUserMatchById(item);
            });

            EnableActStatusRequest request = new EnableActStatusRequest();
            request.setStatus(1);
            request.setActivityIds(List.of(mindUserMatch.getActivityId()));
            //取消活动
            mainActivityBizService.changeStatus(request);
            return null;
        };
        RLock lock = redissonClient.getLock(RedisConstants.RANKED_ACTIVITY_MATCH_KEY + user.getId());
        LockHolder.tryLock(lock, 5, 60, supplier::get);
    }


    public int updateMindUserMatchById(MindUserMatch mindUserMatch) {
        // status 0 , 1 表示机器人被使用， -1 ， 2 表示机器人恢复使用
        int status = Arrays.asList(0, 1).contains(mindUserMatch.getStatus()) ? 1 : 2;
        // 2 , 1
        ZnsUserEntity znsUserEntity = userService.findById(mindUserMatch.getUserId());
        if (znsUserEntity != null && !Objects.equals(znsUserEntity.getRobotCurrStatus(), status)) {     //如果状态不相等，则更新，如果相等，则不更新
            boolean flag = true;
            if (Objects.equals(status, 2)) {    //如果将状态为2，则看当前有没有在跑的数据，如果有，则不更新用户当前跑步状态
                MindUserMatch mindUserMatchNew = mindUserMatchService.selectMindUserMatchByIdStatusList(mindUserMatch.getId(), znsUserEntity.getId(), Arrays.asList(0, 1));
                if (mindUserMatchNew != null) {
                    log.info(" 正在跑步的mindUserMatchNewId = " + mindUserMatchNew.getId() + "，当前mindmatchId = " + mindUserMatch.getId() + ",userId=" + mindUserMatch.getUserId());
                    flag = false;
                }
            }
            if (flag) {
                userService.updateZnsUserRobotStatus(status, mindUserMatch.getUserId());
            }
        }
        return mindUserMatchBizService.updateMindUserMatchById(mindUserMatch);
    }

    /**
     * 活动发起者
     *
     * @param activity
     * @param userId
     * @param dto
     */
    public void addRunActivityLaunchUser(MainActivity activity, Long userId, RankedActivityConfigRequestDto dto) {
        if (null == activity || null == activity.getId() || null == userId) {
            log.error("添加活动的发起人用户失败");
            return;
        }
        ZnsUserEntity userEntity = userService.findById(userId);
        if (null == userEntity) {
            log.error("添加活动的发起人用户不存在,用户id=" + userId);
            return;
        }
        ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
        activityUser.setIsRobot(userEntity.getIsRobot());
        activityUser.setIsTest(userEntity.getIsTest());

        activityUser.setActivityId(activity.getId());
        activityUser.setUserId(userId);
        activityUser.setNickname(userEntity.getFirstName());
        // 活动发起者
        activityUser.setUserType(1);
        activityUser.setUserState(1);
        activityUser.setTargetRunMileage(dto.getMileTarget());
        activityUser.setTargetRunTime(null);
        activityUser.setActivityType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType());
        runActivityUserService.save(activityUser);
    }

    /**
     * 活动参与人报名活动
     *
     * @param rankActivity
     * @param userId
     * @param targetRunMileage
     * @param mindUserMatches
     */
    public void addRunActivityUsers(MainActivity rankActivity, Long userId, Integer targetRunMileage, List<MindUserMatch> mindUserMatches) {
        List<Long> userIds = mindUserMatches.stream().map(MindUserMatch::getUserId).toList();
        List<ZnsUserEntity> userEntities = userService.findByIds(userIds);
        Map<Long, ZnsUserEntity> userEntityMap = userEntities.stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
        mindUserMatches.forEach(mindUserMatch -> {
            if (Objects.equals(mindUserMatch.getUserId(), userId)) {
                log.warn("活动发起者已经报名了");
            } else {
                ZnsUserEntity user = userEntityMap.get(mindUserMatch.getUserId());
                ZnsRunActivityUserEntity activityUser = new ZnsRunActivityUserEntity();
                activityUser.setActivityId(rankActivity.getId());
                activityUser.setUserId(mindUserMatch.getUserId());
                activityUser.setIsRobot(mindUserMatch.getIsRobot());
                activityUser.setIsTest(user.getIsTest());

                activityUser.setNickname(user.getFirstName());
                activityUser.setActivityType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType());

                //1v1 匹配直接接受
                activityUser.setUserState(1);

                // 活动参与者
                activityUser.setInviterUserId(userId);

                activityUser.setTargetRunMileage(targetRunMileage);
                activityUser.setTargetRunTime(null);

                runActivityUserService.save(activityUser);
            }
        });

    }

    private void sendJoinDelayNewMsg(List<MindUserMatch> mindUserMatcheList, ZonedDateTime startTime, Integer goal) {
        Instant now = Instant.now();
        long delay = startTime.toInstant().toEpochMilli() - now.toEpochMilli();
        for (MindUserMatch mindUserMatch : mindUserMatcheList) {
            if (mindUserMatch.getIsRobot() == 0) {
                log.warn("不是机器人，忽略， activityId={}, userId={}", mindUserMatch.getActivityId(), mindUserMatch.getUserId());
                continue;
            }
            try {
                AutomaticAdmissionDealNewActivityDto dto = new AutomaticAdmissionDealNewActivityDto(mindUserMatch.getId(), DateUtil.getDateYYYY_MM_DD_HH_MM_SS(), "段位赛机器人延迟队列", 0, goal);
                DelayDto delayDto = new DelayDto(Constants.ROBOT_ENTRANCE_NEW, JsonUtil.writeString(dto));
                log.info("段位赛延迟队列发送消息 == activityId= " + mindUserMatch.getActivityId() + ",activityUserId=" + mindUserMatch.getId() + " ,data=" + JsonUtil.writeString(delayDto));
                long randomDelay = random.nextLong(5000L);
                log.info("随机延迟时间,randomDelay={}ms, delay={}", randomDelay, delay);

                // 通过广播模式发布延时消息 延时到活动开始分钟 持久化消息 消费后销毁 这里无需指定路由，会广播至每个绑定此交换机的队列
                rabbitTemplate.convertAndSend(delayExchangeName, "", JsonUtil.writeString(delayDto), message -> {
                    message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                    message.getMessageProperties().setDelay(Integer.parseInt(String.valueOf(delay + randomDelay)));   // 毫秒为单位，指定此消息的延时时长 ,+ 1 尽量保证机器人跑完了，再发送消息
                    return message;
                });
            } catch (Exception e) {
                log.error("automaticAdmissionDealNew发送消息队列异常", e);
            }
        }
    }

    // 找到最接近指定 float 值的整数
    //private static int findClosestInteger(List<Integer> integerList, float targetValue) {
    //    if (integerList == null || integerList.isEmpty()) {
    //        throw new IllegalArgumentException("Integer list cannot be null or empty");
    //    }
    //
    //    int closestInteger = integerList.get(0);
    //    double minDifference = Math.abs(Math.floor(targetValue) - closestInteger);
    //
    //    for (int currentInteger : integerList) {
    //        float currentDifference = Math.abs(targetValue - currentInteger);
    //        if (currentDifference < minDifference) {
    //            minDifference = currentDifference;
    //            closestInteger = currentInteger;
    //        }
    //    }
    //
    //    return closestInteger;
    //}

    private static int findClosestInteger(List<Integer> integerList, float targetValue) {
        if (integerList == null || integerList.isEmpty()) {
            throw new IllegalArgumentException("Integer list cannot be null or empty");
        }

        int closestInteger = integerList.get(0);
        double minDifference = Math.abs(Math.floor(targetValue) - closestInteger);

        for (int currentInteger : integerList) {
            float currentDifference = Math.abs(targetValue - currentInteger);
            if (currentDifference < minDifference) {
                minDifference = currentDifference;
                closestInteger = currentInteger;
            }
        }

        return closestInteger;
    }

    private static String findClosestInteger(Map<String, String> spadeMap, Double targetValue) {
        String defaultKey = "2~2.99"; //3.1要求段位赛机器人最低速度是2km/h 20240124
        for (String range : spadeMap.keySet()) {
            String[] ranges = range.split("~");
            Double min = Double.valueOf(ranges[0]);
            Double max = Double.valueOf(ranges[1]);

            if (targetValue.compareTo(min) > -1 && targetValue.compareTo(max) < 1) {
                return spadeMap.get(range);
            }
        }
        return spadeMap.get(defaultKey);
    }

    /**
     * 创建段位赛并匹配对手第二版，可以匹配真实用户
     *
     * @param dto
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public UserRankedLevelMatchResponseDto matchUserV2(RankedActivityConfigRequestDto dto, ZnsUserEntity user) {

        UserRankedLevel rankedLevel = userRankedLevelService.findByUserId(user.getId());
        Boolean enablePlacementRank = false;
        if (dto.getAppVersion() >= 3070) {
            //是否开启定位赛
            enablePlacementRank = Objects.isNull(rankedLevel) || rankedLevel.getIsInPlacement() == 1;
        }

        MainActivity rankActivity = null;
        //获取已有的的活动 不是定位赛先匹配
        if (!enablePlacementRank) {
            rankActivity = findAndEnterActivity(dto, user);
        }
        if (rankActivity == null) {
            //没有已有的活动，创建新活动
            rankActivity = createAndEnterActivity(dto, user, enablePlacementRank);
        }

        //查询用户语言活动标题
        I18nConstant.LanguageCodeEnum language = I18nConstant.LanguageCodeEnum.findByCode(LocaleContextHolder.getLocale().toString());
        ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(rankActivity.getId(), Optional.ofNullable(language).orElse(I18nConstant.LanguageCodeEnum.en_US).getCode());
        String activityTitle = activityDisseminate == null ? "rankActivity" : activityDisseminate.getTitle();

        //封装返回结果
        UserRankedLevelMatchResponseDto responseDto = new UserRankedLevelMatchResponseDto();
        Long roomId = NumberUtils.getGoalImNumber(rankActivity.getId(), dto.getMileTarget(), rankActivity.getTargetType()); //游戏房间
        responseDto.setActivityTitle(activityTitle);
        responseDto.setActivityId(rankActivity.getId());
        responseDto.setRoomNumber(roomId);
        responseDto.setActivityStartTime(getEpochMilli(dto.getTimeZone(), rankActivity));
        return responseDto;
    }


    /**
     * 用户创建新活动参加
     *
     * @param dto
     * @param user
     * @param enablePlacementRank
     * @return
     */
    private MainActivity createAndEnterActivity(RankedActivityConfigRequestDto dto, ZnsUserEntity user, Boolean enablePlacementRank) {

        //创建并开启活动
        MainActivity rankActivity = createAndStartActivity(dto, user.getId());

        //创建排位赛
        UserRankedMatchEntity rankedMatchEntity = createRankedMatch(rankActivity, dto, user, enablePlacementRank);

        //创建用户匹配记录
        Long userId = user.getId();
        Long activityId = rankActivity.getId();
        ZonedDateTime activityEndTime = DateUtil.addHours(ZonedDateTime.now(), 24);
        MindUserMatch myMindUserMatch = buildMindMatch(userId, 1, 0, null, activityEndTime
                , BigDecimal.valueOf(rankedMatchEntity.getTargetRunMileage()), activityId, "", 0);
        mindUserMatchBizService.insertMindUserMatch(myMindUserMatch);

        //创建排位明细
        MatchRankedLevelVo matchRankedLevelVo = getUserMatchRankedLevelVo(userId);
        UserRankedMatchLineEntity lineEntity = new UserRankedMatchLineEntity(userId, activityId, rankedMatchEntity.getId(), matchRankedLevelVo, RankedConstant.LineStatusEnum.LINE_STATUS_2.code, myMindUserMatch.getId(), UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode());
        userRankedMatchLineService.insert(lineEntity);

        //活动发起人报名活动
        addRunActivityLaunchUser(rankActivity, userId, dto);

        //发布延迟消息，真人匹配结束后开始投放机器人
        ZonedDateTime waitUserEndTime = rankedMatchEntity.getWaitUserEndTime();
        int waitSecond = DateUtil.betweenSecond(ZonedDateTime.now(), waitUserEndTime);
        if (waitSecond < 5) {
            //如果等待时间小于5秒则，延迟5秒，防止事务没提交，但是延迟队列开始消费，查不到数据
            waitSecond = 5;
        }
        log.info("UserRankedLevelManager#matchUserV2-----创建段位赛并匹配对手第二版，userId:{},activityId:{},延迟消息时间：{}秒", user.getId(), rankActivity.getId(), waitSecond);
        final int waitMillisecond = (waitSecond + 3) * 1000; //延迟3秒做开始 匹配机器人，保证用户进入房间
        rabbitTemplate.convertAndSend(rankMatchPutInRobotDelayExchange, RabbitQueueConstants.RANK_MATCH_PUT_IN_ROBOT_DELAY_KEY, rankedMatchEntity.getId(), message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay(waitMillisecond);// 毫秒为单位，指定此消息的延时时长,
            return message;
        });

        return rankActivity;
    }

    /**
     * 创建排位匹配主表
     *
     * @param rankActivity
     * @param dto
     * @param user
     * @param enablePlacementRank
     * @return
     */
    private UserRankedMatchEntity createRankedMatch(MainActivity rankActivity, RankedActivityConfigRequestDto dto, ZnsUserEntity user, Boolean enablePlacementRank) {
        //获取参与的段位区间
        Long userId = user.getId();
        Long activityId = rankActivity.getId();

        //获取用户 排位赛段位信息
        MatchRankedLevelVo matchRankedLevelVo = getUserMatchRankedLevelVo(userId);


        //活动开始时间
        String activityStartTimeStr = rankActivity.getActivityStartTime();
        ZonedDateTime activityStartTime = Optional.ofNullable(DateTimeUtil.parse(activityStartTimeStr)).orElse(DateUtil.addSeconds(ZonedDateTime.now(), 70));

        //机器人最晚进入时间（活动开始前5秒）
        ZonedDateTime waitRobotEndTime = DateUtil.addSeconds(activityStartTime, -5);

        //用户最晚进入时间（活动报名时间+用户等待时间）
        String applicataionStartTimeStr = rankActivity.getApplicationStartTime();
        ZonedDateTime applicationStartTime = Optional.ofNullable(DateTimeUtil.parse(applicataionStartTimeStr)).orElse(ZonedDateTime.now());
        ZnsRunActivityConfigEntity runActivityConfig = znsRunActivityConfigService.getByType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType(), null);
        RankedActivityConfigDto runActivityTemplate = JsonUtil.readValue(runActivityConfig.getActivityConfig(), RankedActivityConfigDto.class);
        Integer waitUserTime = Optional.ofNullable(runActivityTemplate.getWaitUserTime()).orElse(30); //等待用户时间（秒）
        ZonedDateTime waitUserEndTime = DateUtil.addSeconds(applicationStartTime, waitUserTime);

        //创建匹配主信息
        UserRankedMatchEntity userRankedMatchEntity = new UserRankedMatchEntity();
        userRankedMatchEntity.setUserId(userId);
        userRankedMatchEntity.setActivityStartTime(activityStartTime);
        userRankedMatchEntity.setActivityId(activityId);
        userRankedMatchEntity.setCountryCode(Optional.ofNullable(user.getCountryCode()).orElse(I18nConstant.CountryCodeEnum.US.code));
        userRankedMatchEntity.setMatchRankedLevelMinSeq(matchRankedLevelVo.getMatchRankedLevelMinSeq());
        userRankedMatchEntity.setMatchRankedLevelMaxSeq(matchRankedLevelVo.getMatchRankedLevelMaxSeq());
        userRankedMatchEntity.setUserNum(runActivityTemplate.getPlayerCount());
        userRankedMatchEntity.setState(RankedConstant.MatchStateEnum.MATCH_STATE_1.code);
        userRankedMatchEntity.setTargetRunMileage(dto.getMileTarget());
        userRankedMatchEntity.setRouteId(dto.getRouteId());
        userRankedMatchEntity.setWaitRobotEndTime(waitRobotEndTime);
        userRankedMatchEntity.setWaitUserEndTime(waitUserEndTime);
        if (enablePlacementRank) {
            userRankedMatchEntity.setIsPlacement(1);
        }
        userRankedMatchService.insert(userRankedMatchEntity);
        return userRankedMatchEntity;
    }

    /**
     * 获取用户 排位赛段位信息
     *
     * @param userId
     * @return
     */
    private MatchRankedLevelVo getUserMatchRankedLevelVo(Long userId) {
        UserRankedLevel myUserRankedLevel = userRankedLevelService.findByUserId(userId);
        String sysConfig = sysConfigService.selectConfigByKey("new_ranked_activity_config");
        Map<String, Object> rankedSysConfig = JsonUtil.readValue(sysConfig);
        BigDecimal hiddenScore = getUserHideScore(myUserRankedLevel, rankedSysConfig);//获取用户的隐藏分
        BigDecimal minHiddenScore = hiddenScore.add(BigDecimal.valueOf(-15));
        BigDecimal maxHiddenScore = hiddenScore.add(BigDecimal.valueOf(15));
        log.info("UserRankedLevelManager#matchUserV2-----创建排位匹配主表，userId:{}，hiddenScore range= {},{}", userId, minHiddenScore, maxHiddenScore);
        if (minHiddenScore.compareTo(BigDecimal.TEN) < 0) {
            minHiddenScore = BigDecimal.TEN;
            log.info("UserRankedLevelManager#matchUserV2-----创建排位匹配主表，userId:{}，minHiddenScore 太小，重置={}", userId, minHiddenScore);
        }
        List<RankedLevel> rankedLevels = listMatchedRankedLevelList(myUserRankedLevel, minHiddenScore, maxHiddenScore); //获取隐藏分对应的段位
        Integer matchRankedLevelMinSeq = rankedLevels.stream().map(RankedLevel::getSeq).min(Integer::compareTo).orElse(101); //最小段位索引
        Integer matchRankedLevelMaxSeq = rankedLevels.stream().map(RankedLevel::getSeq).max(Integer::compareTo).orElse(701); //最大段位索引
        return new MatchRankedLevelVo(matchRankedLevelMinSeq, matchRankedLevelMaxSeq);
    }

    /**
     * 创建并开启活动
     *
     * @param dto
     * @return
     */
    private MainActivity createAndStartActivity(RankedActivityConfigRequestDto dto, Long userId) {
        SingleActivityCreateRequest rankActivityRequest = buildSingleActivityCreateRequest(dto);
        MainActivity rankActivity = mainActivityBizService.createSingleActivity(rankActivityRequest, MainActivityTypeEnum.RANK);
        //发布活动
        EnableActStatusRequest request = new EnableActStatusRequest();
        request.setStatus(0);
        request.setActivityIds(List.of(rankActivity.getId()));
        mainActivityBizService.changeStatus(request);

        //开始活动
        rankActivity.setActivityState(MainActivityStateEnum.STARTED.getCode());
        mainActivityService.update(rankActivity);

        //因为创建活动时间太长，这里重新计算活动开始时间
        resetActivityTime(dto, userId, rankActivity.getId());
        return mainActivityService.findById(rankActivity.getId());
    }

    /**
     * 重新计算活动开始时间
     *
     * @param dto
     * @param userId
     */
    private void resetActivityTime(RankedActivityConfigRequestDto dto, Long userId, Long activityId) {
        ZnsRunActivityConfigEntity runActivityConfig = znsRunActivityConfigService.getByType(RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType(), null);
        RankedActivityConfigDto runActivityTemplate = JsonUtil.readValue(runActivityConfig.getActivityConfig(), RankedActivityConfigDto.class);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime now = ZonedDateTime.now();
        String applicationStartTimeStr = formatter.format(now);  //报名开始时间：现在
        Integer waitTotalTime = Optional.ofNullable(runActivityTemplate.getWaitTotalTime()).orElse(70);
        ZonedDateTime startDateTime = now.plusSeconds(waitTotalTime);
        String activityStartTimeStr = formatter.format(startDateTime); //报名结束时间 = 活动开始时间 =（现在+等待结束时间）
        BigDecimal km = BigDecimal.valueOf(dto.getMileTarget()).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
        int seconds = km.multiply(BigDecimal.valueOf(3600)).intValue();
        ZonedDateTime endDateTime = startDateTime.plusSeconds(seconds + 60);
        String activityEndTimeStr = formatter.format(endDateTime); //活动结束时间 =（活动开始时间+配置时间+1min）
        log.info("UserRankedLevelManager#matchUserV2-----创建并开启活动 userId:{}, activityId:{},报名开始时间：{} ，报名结束时间：{} ，活动开始时间：{}，活动结束时间：{}", userId, activityId, applicationStartTimeStr, activityStartTimeStr, activityStartTimeStr, activityEndTimeStr);
        MainActivity rankActivity = new MainActivity();
        rankActivity.setId(activityId);
        rankActivity.setApplicationStartTime(applicationStartTimeStr);
        rankActivity.setApplicationEndTime(activityStartTimeStr);
        rankActivity.setActivityStartTime(activityStartTimeStr);
        rankActivity.setActivityEndTime(activityEndTimeStr);
        mainActivityService.update(rankActivity);
    }

    /**
     * 用户参加已有的的活动
     *
     * @param dto
     * @param user
     * @return
     */
    private MainActivity findAndEnterActivity(RankedActivityConfigRequestDto dto, ZnsUserEntity user) {
        //获取用户段位信息
        Long userId = user.getId();

        //获取用户 排位赛段位信息
        MatchRankedLevelVo matchRankedLevelVo = getUserMatchRankedLevelVo(userId);

        //用户段位索引+目标+地图+结束时间+非自己创建 查询满足条件的排位赛
        UserRankedMatchQuery query = UserRankedMatchQuery.builder()
                .countryCode(Optional.ofNullable(user.getCountryCode()).orElse(I18nConstant.CountryCodeEnum.US.code))
                .matchRankedLevelVo(matchRankedLevelVo).ignoreUserId(userId)
                .routeId(dto.getRouteId()).targetRunMileage(dto.getMileTarget()).waitUserEndTime(ZonedDateTime.now()).build();
        List<UserRankedMatchEntity> list = userRankedMatchService.findByQuery(query);

        //校验赛事是否可以参与
        if (!CollectionUtils.isEmpty(list)) {
            for (UserRankedMatchEntity rankedMatchEntity : list) {
                MainActivity activity = checkAndEnterActivity(rankedMatchEntity, userId, matchRankedLevelVo);
                if (activity != null) {
                    return activity;
                }
            }
        }
        return null;
    }

    /**
     * 校验并参与排位赛
     *
     * @param rankedMatchEntity  已有的匹配
     * @param userId             用户
     * @param matchRankedLevelVo 用户段位信息
     * @return 活动
     */
    private MainActivity checkAndEnterActivity(UserRankedMatchEntity rankedMatchEntity, Long userId, MatchRankedLevelVo matchRankedLevelVo) {
        Long activityId = rankedMatchEntity.getActivityId();
        log.info("UserRankedLevelManager#matchUserV2-----校验并参与排位赛，userId:{},activityId:{},处理开始", userId, activityId);
        Supplier<MainActivity> supplier = () -> {
            if (rankedMatchEntity.getWaitUserEndTime().isBefore(ZonedDateTime.now())) {
                //真人匹配时间已经结束
                log.info("UserRankedLevelManager#matchUserV2-----校验并参与排位赛，userId:{},activityId:{},真人匹配时间已经结束", userId, activityId);
                return null;
            }

            //查询排位明细
            List<UserRankedMatchLineEntity> list = userRankedMatchLineService.findListByMatchId(rankedMatchEntity.getId());
            if (!CollectionUtils.isEmpty(list)) {
                UserRankedMatchLineEntity lineEntity = list.stream().filter(item -> item.getUserId().equals(userId)).findFirst().orElse(null);
                if (lineEntity != null) {
                    //已经在排位中了。不能重复进入
                    log.info("UserRankedLevelManager#matchUserV2-----校验并参与排位赛，userId:{},activityId:{},不能重复进入", userId, activityId);
                    return null;
                }
                long count = list.stream().filter(item -> RankedConstant.LineStatusEnum.LINE_STATUS_2.code.equals(item.getMatchStatus())).count();
                if (count >= rankedMatchEntity.getUserNum()) {
                    //进入排位的人数已满
                    log.info("UserRankedLevelManager#matchUserV2-----校验并参与排位赛，userId:{},activityId:{},人数已满", userId, activityId);
                    return null;
                }
            }

            //查询排位赛发起人的排位明细
            UserRankedMatchLineEntity lineEntity = userRankedMatchLineService.findByMatchIdAndUserId(rankedMatchEntity.getId(), rankedMatchEntity.getUserId());

            //创建用户匹配记录
            MainActivity mainActivity = mainActivityService.findById(activityId);
            ZonedDateTime activityEndTime = DateUtil.addHours(ZonedDateTime.now(), 24);
            MindUserMatch myMindUserMatch = buildMindMatch(userId, 1, 0, lineEntity.getMindUserMatchId(), activityEndTime
                    , BigDecimal.valueOf(rankedMatchEntity.getTargetRunMileage()), mainActivity.getId(), "", 0);
            myMindUserMatch.setMatchUserId(rankedMatchEntity.getUserId());
            mindUserMatchBizService.insertMindUserMatch(myMindUserMatch);

            //创建排位明细
            UserRankedMatchLineEntity myLineEntity = new UserRankedMatchLineEntity(userId, activityId, rankedMatchEntity.getId(), matchRankedLevelVo, RankedConstant.LineStatusEnum.LINE_STATUS_2.code, myMindUserMatch.getId(), UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode());
            userRankedMatchLineService.insert(myLineEntity);

            //活动参与人报名活动
            addRunActivityUsers(mainActivity, rankedMatchEntity.getUserId(), rankedMatchEntity.getTargetRunMileage(), List.of(myMindUserMatch));
            log.info("UserRankedLevelManager#matchUserV2-----校验并参与赛事，userId:{},activityId:{},处理完成", userId, activityId);
            return mainActivity;
        };

        //同一匹配同一时间只有一个人可以加入
        RLock lock = redissonClient.getLock(RedisConstants.RANKED_ACTIVITY_MATCH_CHECK_KEY + rankedMatchEntity.getId());
        return LockHolder.tryLock(lock, 5, 60, supplier::get);
    }
}

