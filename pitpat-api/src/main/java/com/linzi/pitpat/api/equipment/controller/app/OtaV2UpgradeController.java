package com.linzi.pitpat.api.equipment.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.equipmentservice.dto.api.request.OtaV2UpgradeReportRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.api.request.OtaV2UpgradeUpdateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.api.response.OtaV2UpgradeReportResponseDto;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.manager.api.OtaV2UpgradeManager;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 新版ota升级规则 服务类
 *
 * @since 2025年7月3日
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/otaV2")
public class OtaV2UpgradeController extends BaseAppController {

    private final OtaV2UpgradeManager otaV2UpgradeManager;

    /**
     * 上报设备版本
     */
    @PostMapping("/reportVersion")
    public Result<OtaV2UpgradeReportResponseDto> reportVersion(@RequestBody @Valid OtaV2UpgradeReportRequestDto req) {
        OtaV2UpgradeReportResponseDto resp = otaV2UpgradeManager.reportVersion(req,getLanguageCode(),DeviceConstant.UpWayEnum.BLUETOOTH_UP.getCode());
        return CommonResult.success(resp);
    }

    /**
     * 更新升级状态
     */
    @PostMapping("/updateUpgradeState")
    public Result<Void> updateUpgradeState(@RequestBody @Valid OtaV2UpgradeUpdateRequestDto req) {
        otaV2UpgradeManager.updateUpgradeState(req, DeviceConstant.UpWayEnum.BLUETOOTH_UP.getCode(),getLanguageCode());
        return CommonResult.success();
    }

}
