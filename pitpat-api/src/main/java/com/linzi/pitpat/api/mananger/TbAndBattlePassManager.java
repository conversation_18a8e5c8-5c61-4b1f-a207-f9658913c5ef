package com.linzi.pitpat.api.mananger;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.activityservice.dto.request.MilestoneDataRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.MilestoneDataUploadRequestDto;
import com.linzi.pitpat.api.activityservice.dto.response.MilestoneDataResponseDto;
import com.linzi.pitpat.api.activityservice.manager.BattlePassMilestoneManager;
import com.linzi.pitpat.api.dto.response.BattlePassAndTbInfoDto;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.entity.dto.user.LoginUserDto;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.manager.TurbolinkCampaignUserManager;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBitSet;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TbAndBattlePassManager {

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private BattlePassMilestoneManager battlePassMilestoneManager;

    @Resource
    private ZnsUserAccountService userAccountService;

    @Resource
    private AppMessageService appMessageService;

    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;

    @Resource
    private ISysConfigService sysConfigService;

    @Autowired
    private TurbolinkCampaignUserManager turbolinkCampaignUserManager;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;

    private final static String CONTENT = """
            Dear User,
            
            We want to inform you that the Milestone feature will soon be retired. If you participated in the Milestone challenge this month and haven’t claimed your reward yet, please do so as soon as possible.             
            Additionally, users who made payments for this feature will receive a refund as compensation.Stay tuned—an exciting new game section is coming soon to enhance your experience with some fun surprises!                                   
            Thank you for your continued support!
            
            Best regards,
            
            PitPat
            """;
    private final static String NEW_CONTENT = """
            Dear User,
            
            We want to inform you that the Goal Run+ feature has now been retired. If you participated in the Goal Run challenge, your rewards have already been credited to your account. Additionally, users who made payments for this feature will receive a refund as compensation.
            
            For those using an earlier version of PitPat, the Goal Run feature may still be accessible, but no further rewards will be available.
            
            To enhance your experience, we’re excited to announce a brand-new Farm Mini-Game, packed with fun surprises! To enjoy this new feature, please upgrade to the latest version of the app.
            
            Stay tuned for more exciting updates!
            
            Best regards,
            
            The PitPat Team
            """;


    @Transactional
    public BattlePassAndTbInfoDto getBattlePassAndTbInfo(ZnsUserEntity loginUser, Integer appVersion) {

        BattlePassAndTbInfoDto dto = new BattlePassAndTbInfoDto();
        //里程碑开关
        String battlePassSwitch = sysConfigService.selectConfigByKey("battle_pass_enable");
        dto.setBattlePassSwitch("enable".equals(battlePassSwitch) ? 1 : 0);

        //组黄活动链接
        if (dto.getBattlePassSwitch() == 0) {
            RLock lock = redissonClient.getLock(RedisConstants.BATTLE_SETTLE + loginUser.getId());
            try {
                if (lock.tryLock()) {
                    log.info("用户{}获取battlePassAndTbInfo", loginUser.getId());
                    //里程碑结算
                    settlementBattlePass(loginUser, appVersion);
                }
            } catch (Exception e) {
                log.error("获取battlePassAndTbInfo失败", e);
                throw new RuntimeException(e);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.info("释放锁");
                }
            }
            String currentUserCampaignLink = turbolinkCampaignUserManager.getCurrentUserCampaignLink(loginUser.getId());
            dto.setTurbolinkLink(currentUserCampaignLink);
        }


        return dto;
    }


    public void settlementBattlePass(ZnsUserEntity loginUser, Integer appVersion) {
        if (appVersion < 4030) {
            return;
        }
        RBitSet bitSetT = redissonClient.getBitSet(RedisConstants.CANCEL_BATTLE_PASS);
        if (!bitSetT.get(loginUser.getId())) {
            log.info("该用户第一次升级版本 userId:{}", loginUser.getId());
            //查询当前里程碑
            ZnsRunActivityEntity currentBattlePass = battlePassMilestoneManager.getCurrentBattlePass(loginUser.getIsTest(), ZonedDateTime.now());
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(currentBattlePass.getId(), loginUser.getId());
            if (activityUser != null) {
                //构造入参
                LoginUserDto loginUserDto = new LoginUserDto();
                loginUserDto.setLoginUser(loginUser);
                //查询待上传数据
                uploadBattlePassData(currentBattlePass, loginUserDto);
                //里程碑奖励下发
                BattlePassAwardSend(currentBattlePass, loginUserDto);
                //站内信消息通知
//                messageNotify(loginUser);
                //付费里程碑退费
                BattlePassAwardSendRefund(loginUser, currentBattlePass);
            }
            //记录
            bitSetT.set(loginUser.getId(), true);
        }
    }

    private void BattlePassAwardSendRefund(ZnsUserEntity loginUser, ZnsRunActivityEntity currentBattlePass) {
        log.info("BattlePassAwardSendRefund");
        UserAccountDetailByQuery userAccountDetailByQuery = new UserAccountDetailByQuery();
        userAccountDetailByQuery.setUserId(loginUser.getId()).setActivityId(currentBattlePass.getId()).setType(2);
        List<ZnsUserAccountDetailEntity> list = userAccountDetailService.findList(userAccountDetailByQuery);
        if (!CollectionUtils.isEmpty(list) && list.get(0).getAmount().compareTo(BigDecimal.ZERO) > 0) {
            log.info("用户里程碑退款{}", list);
            userAccountService.refundBalance(list.get(0), "Goal Run+ refund");
        }
    }

//    private void messageNotify(ZnsUserEntity loginUser) {
//        log.info("站内信消息通知");
//        List<Long> ids = new ArrayList<>();
//        ids.add(loginUser.getId());
//        appMessageService.sendIm(null, ids, NEW_CONTENT, TencentImConstant.TIM_TEXT_ELEM, OrderUtil.getBatchNo(), 0, Boolean.FALSE);
//    }

    private void BattlePassAwardSend(ZnsRunActivityEntity currentBattlePass, LoginUserDto loginUserDto) {
        Long historyAwardId = battlePassMilestoneManager.hashHistoryAward(currentBattlePass.getId(), ZonedDateTime.now(), loginUserDto.getLoginUser());
        if (historyAwardId > 0) {
            log.info("History award start received :{}", historyAwardId);
            battlePassMilestoneManager.awardReceive(historyAwardId, loginUserDto);
        }
        log.info("now award start received :{}", currentBattlePass.getId());
        battlePassMilestoneManager.awardReceive(currentBattlePass.getId(), loginUserDto);
    }

    private void uploadBattlePassData(ZnsRunActivityEntity currentBattlePass, LoginUserDto loginUserDto) {
        log.info("uploadBattlePassData");
        MilestoneDataRequestDto request = new MilestoneDataRequestDto();
        request.setDataStatus(0);
        request.setPageNum(1);
        request.setPageSize(1000000);
        Page<MilestoneDataResponseDto> page = battlePassMilestoneManager.dataList(request, loginUserDto);
        List<MilestoneDataResponseDto> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {

            //里程碑数据上传
            MilestoneDataUploadRequestDto uploadRequestDto = new MilestoneDataUploadRequestDto();
            uploadRequestDto.setActivityId(currentBattlePass.getId());
            uploadRequestDto.setDetailIds(records.stream().map(MilestoneDataResponseDto::getId).collect(Collectors.toList()));

            battlePassMilestoneManager.dataUpload(uploadRequestDto, loginUserDto);
            log.info("Milestone data upload success :{}", uploadRequestDto.getDetailIds());
        }
    }


}
