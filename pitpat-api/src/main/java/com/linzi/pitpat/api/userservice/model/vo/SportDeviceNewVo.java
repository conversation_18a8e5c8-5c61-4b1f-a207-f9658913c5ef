package com.linzi.pitpat.api.userservice.model.vo;

import com.linzi.pitpat.data.equipmentservice.enums.TreadmillConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运动设备VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SportDeviceNewVo extends SportDeviceVo {
    /**
     * 是否支持蜂鸣器语音
     */
    private Boolean isSupportBuzzerSound;
    /**
     * 是否支持其他语音
     */
    private Boolean isSupportOtherSound;

    /**
     * 蓝牙芯片类型，lx：乐芯，xzx：芯中芯，xzx_bicycle：芯中芯(脚踏)，xzx_rowing：芯中芯(划船机)，xzx_bike：芯中芯(单车)
     *
     * @see TreadmillConstant.BluetoothChipTypeEnum
     */
    private String bluetoothChipType;


    /**
     * 成员
     */
    private Integer inviteUserNum;

    /**
     * 设备运行里程(m)
     */
    private Integer equipmentRunMile;

    /**
     * 设备运行时间(s)
     */
    private Integer equipmentRunTime;
    /**
     * 用户身份，1：绑定人，0：共享成员
     */
    private Integer userType;

    /**
     * 设备昵称
     */
    private String equipmentNickName;
    /**
     * 设备id search name
     */
    private String equipmentSearchName;
    /**
     * 是否运行share
     * @see com.linzi.pitpat.core.constants.enums.YesNoStatus
     */
    private Integer isAllowShare;


}
