package com.linzi.pitpat.api.activityservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityPolymerizationBizService;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserAwardBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCategoryI18n;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCategoryItem;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PolymerizationActivityPole;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityCategoryVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.ActivityUserAwardVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.CategoryActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MyRaceCalendarSubActivityListVo;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryI18nService;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryItemService;
import com.linzi.pitpat.data.activityservice.service.ActivityCategoryService;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PolymerizationActivityPoleService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/8 17:05
 */
@Component
@RequiredArgsConstructor
public class ActivityCategoryManager {
    private final ActivityCategoryService activityCategoryService;
    private final ActivityCategoryI18nService activityCategoryI18nService;
    private final ActivityCategoryItemService activityCategoryItemService;
    private final MainActivityService mainActivityService;
    private final SubActivityService subActivityService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final ActivityFeeService activityFeeService;
    private final ZnsUserAccountService userAccountService;
    private final ActivityEquipmentConfigService activityEquipmentConfigService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final UserGroupRelService userGroupRelService;
    private final RotationAreaBizService rotationAreaBizService;
    private final PolymerizationActivityPoleService polymerizationActivityPoleService;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;
    private final ActivityPolymerizationBizService activityPolymerizationBizService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ActivityUserAwardBizService activityUserAwardBizService;

    public List<ActivityCategoryVo> list(String langCode) {
        List<ActivityCategoryVo> list = new ArrayList<>();
        List<Long> ids = activityCategoryService.findIdList();
        if (CollectionUtils.isEmpty(ids)) {
            return list;
        }

        for (Long id : ids) {
            ActivityCategoryVo vo = new ActivityCategoryVo();
            vo.setCategoryId(id);
            ActivityCategoryI18n categoryI18n = activityCategoryI18nService.findByCategoryId(id, langCode);
            if (Objects.isNull(categoryI18n)) {
                continue;
            }
            vo.setTitle(categoryI18n.getLangName());
            list.add(vo);
        }

        return list;
    }

    /**
     * 活动列表 1：表示全部
     *
     * @param categoryId
     * @param user
     * @param zoneId
     * @param checkUser
     * @return
     */
    public List<CategoryActivityVo> activityList(Long categoryId, ZnsUserEntity user, String zoneId, Page page, boolean checkUser, String languageCode) {
        List<CategoryActivityVo> list = new ArrayList<>();
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(user.getId());
        Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
        //查询用户所属人群
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(user.getId());
        String currentTime = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone(zoneId), DateUtil.DATE_TIME_SHORT);
        //临时修复
        if (CollectionUtils.isEmpty(groupsByUserId)) {
            groupsByUserId = List.of(0L);
        }
        List<Long> activityIds = null;
        List<MainActivity> mainActivityList = null;
        Map<Long, ActivityCategoryItem> itemMap = null;
        if (Objects.nonNull(categoryId) && categoryId > 1) {
            //查询类目下活动
            List<ActivityCategoryItem> categoryItemList = activityCategoryItemService.findListByCategoryId(categoryId);
            if (CollectionUtils.isEmpty(categoryItemList)) {
                return list;
            }

            itemMap = categoryItemList.stream().collect(Collectors.toMap(ActivityCategoryItem::getActivityId, Function.identity(), (x, y) -> x));
            //聚合活动处理
            activityIds = dealPolyActivityId(categoryItemList);
            mainActivityList = mainActivityService.findListByIdsAndState(page, activityIds, user.getStateCode(), user.getCountryCode(), groupsByUserId, currentTime, checkUser);
        } else {
            mainActivityList = mainActivityService.findAllNoEndActivity(page,
                    DateUtil.getCurrentTime(timeZone), activityIds, Arrays.asList(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()), user.getStateCode(), user.getCountryCode(), groupsByUserId, true, checkUser);
            if (!CollectionUtils.isEmpty(mainActivityList)) {
                activityIds = mainActivityList.stream().map(MainActivity::getId).collect(Collectors.toList());
                List<ActivityCategoryItem> categoryItemList = activityCategoryItemService.findListByActivityIds(activityIds);
                if (!CollectionUtils.isEmpty(categoryItemList)) {
                    itemMap = categoryItemList.stream().collect(Collectors.toMap(ActivityCategoryItem::getActivityId, Function.identity(), (x, y) -> x));
                }
            }
        }
        if (CollectionUtils.isEmpty(mainActivityList)) {
            return list;
        }
        List<MyRaceCalendarSubActivityListVo> subActivityListVos = subActivityService.findListByActivityIds(activityIds);
        Map<Long, List<MyRaceCalendarSubActivityListVo>> subActivityListVoMap = subActivityListVos.stream().collect(Collectors.groupingBy(MyRaceCalendarSubActivityListVo::getMainActivityId));

        //按上架时间排序
        mainActivityList = mainActivityList.stream().sorted(Comparator.comparing(MainActivity::getOnlineTime).reversed()).collect(Collectors.toList());
        for (MainActivity mainActivity : mainActivityList) {
            CategoryActivityVo vo = new CategoryActivityVo();
            vo.setActivityId(mainActivity.getId());
            vo.setMainType(mainActivity.getMainType());
            ZonedDateTime startTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
            ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
            vo.setActivityState(mainActivity.getActivityState());
            //设置活动时间
            if (mainActivity.getTimeStyle() == 0) {
                vo.setActivityStartTime(startTime);
                vo.setActivityEndTime(endTime);
            } else {
                vo.setActivityStartTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityStartTime(), zoneId)));
                vo.setActivityEndTime(new Date(DateUtil.getStampByZone(mainActivity.getActivityEndTime(), zoneId)));
                //各时区判断
                vo.setActivityState(mainActivityService.getActivityState(mainActivity.getId(), zoneId));
            }

            //聚合活动处理
            if (MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(mainActivity.getMainType())
                    || MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType().equals(mainActivity.getMainType())) {
                ActivityPolymerizationRecord polymerizationRecord = activityPolymerizationRecordService.findByActivityId(mainActivity.getId());
                if (Objects.nonNull(polymerizationRecord)) {
                    PolymerizationActivityPole pole = polymerizationActivityPoleService.findById(polymerizationRecord.getPolyId());
                    if (Objects.nonNull(pole)) {
                        vo.setActivityStartTime(pole.getTaskStartTime());
                        vo.setActivityEndTime(pole.getTaskEndTime());
                    }
                }
            }

            List<MyRaceCalendarSubActivityListVo> myRaceCalendarSubActivityListVos = subActivityListVoMap.get(mainActivity.getId());
            //设置目标
            if (!CollectionUtils.isEmpty(myRaceCalendarSubActivityListVos) && myRaceCalendarSubActivityListVos.size() == 1) {
                vo.setTarget(myRaceCalendarSubActivityListVos.get(0).getTarget());
            }
            vo.setTargetType(mainActivity.getTargetType());
            //设置图片、标题
            ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivity.getId(), languageCode);
            if (Objects.nonNull(disseminate)) {
                vo.setActivityCoverImage(disseminate.getDisseminatePics());
                vo.setActivityTitle(disseminate.getTitle());
                vo.setIsShowMarquee(disseminate.getMarquee());
            }

            //设置标签
            if (Objects.nonNull(itemMap)) {
                ActivityCategoryItem categoryItem = itemMap.get(mainActivity.getId());
                if (Objects.nonNull(categoryItem)) {
                    vo.setSign(categoryItem.getSign());
                }
            }

            //设置费用
            ActivityFee feeEntry = activityFeeService.findFeeEntry(mainActivity.getId(), userAccount.getCurrencyCode());
            if (Objects.nonNull(feeEntry)) {
                vo.setActivityEntryFee(feeEntry.getAmount());
                vo.setActivityEntryScore(feeEntry.getScore());
            }
            vo.setCurrency(currency);
            //设置设备信息
            List<ActivityEquipmentConfig> equipments = activityEquipmentConfigService.findByActId(mainActivity.getId());
            List<Integer> equipmentInfos = equipments.stream().filter(a -> Objects.nonNull(a.getEquipmentType())).map(ActivityEquipmentConfig::getEquipmentType).distinct().toList();
            vo.setDeviceType(equipmentInfos);

            //设置用户缉奖励信息
            List<Long> activityIdList = new ArrayList<>();
            activityIdList.add(vo.getActivityId());
            if (MainActivityTypeEnum.SERIES_MAIN.getType().equals(vo.getMainType())) {
                List<Long> subActivityId = seriesActivityRelService.findSubActivityId(vo.getActivityId());
                if (!CollectionUtils.isEmpty(subActivityId)) {
                    activityIdList.addAll(subActivityId);
                }
            }
            List<ActivityUserAwardVo> userAward = activityUserAwardBizService.findUserAward(activityIdList);
            vo.setAwardUserList(userAward);
            //查询参与用户
            List<UserSimpleVo> activitySimpleUser = runActivityUserService.findActivitySimpleUser(vo.getActivityId(), 3, mainActivity.getOldType());
            vo.setParticipantList(activitySimpleUser);
            Integer activityUserCount = runActivityUserService.findActivityUserCount(vo.getActivityId());
            vo.setParticipantCount(activityUserCount);
            //路由获取
            RotationArea route = rotationAreaBizService.getNewActivityRoute(vo.getActivityId(), mainActivity.getMainType(), 2);
            vo.setUrl(route.getUrl());
            vo.setJumpParam(route.getJumpParam());
            vo.setIsMemberActivity(mainActivity.getIsMemberActivity());
            list.add(vo);
        }

        return list;
    }

    private List<Long> dealPolyActivityId(List<ActivityCategoryItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        List<Long> list = new ArrayList<>();
        List<Long> activityIds = items.stream().map(ActivityCategoryItem::getActivityId).collect(Collectors.toList());
        List<MainActivity> activityList = mainActivityService.findListByIds(activityIds);
        Map<Long, MainActivity> activityMap = activityList.stream().collect(Collectors.toMap(MainActivity::getId, Function.identity(), (x, y) -> x));
        for (ActivityCategoryItem item : items) {
            MainActivity mainActivity = activityMap.get(item.getActivityId());
            if (MainActivityTypeEnum.SINGLE_POLYMERIZATION.getType().equals(item.getMainType())) {
                Long runningActivityId = activityPolymerizationBizService.findRunningActivityIdByParentId(mainActivity.getId());
                if (Objects.nonNull(runningActivityId)) {
                    list.add(runningActivityId);
                } else {
                    list.add(mainActivity.getId());
                }
            } else {
                list.add(mainActivity.getId());
            }
        }
        return list;
    }
}
