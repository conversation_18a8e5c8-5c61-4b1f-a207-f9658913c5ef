package com.linzi.pitpat.api.activityservice.manager;


import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityStateEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.PropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.SeriesActivityRel;
import com.linzi.pitpat.data.activityservice.model.entity.UserPropDetail;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.PropConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.CouponAwardDto;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.PropConfigService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.WearsI18n;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.query.WearsI18nQuery;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsI18nService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentLimitEditionAwardDo;
import com.linzi.pitpat.data.request.runData.DetailIdRequest;
import com.linzi.pitpat.data.service.prop.UserPropDetailService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.api.response.LimitedEditionAwardResponse;
import com.linzi.pitpat.data.userservice.dto.api.response.traffic.investment.TrafficInvestmentAwardResponse;
import com.linzi.pitpat.data.userservice.model.entity.RealPersonRunDataDetails;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.runData.AwardPopWearsVo;
import com.linzi.pitpat.data.vo.runData.ReportPopInfoVo;
import com.linzi.pitpat.data.vo.runData.ReportPopProp;
import com.linzi.pitpat.data.vo.runData.RunningReportPopVo;
import com.linzi.pitpat.data.vo.runData.ShareInfoVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户跑步报告处理类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserRunningReportManager {

    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsUserAccountDetailService znsUserAccountDetailService;
    private final UserCouponService userCouponService;
    private final UserPropDetailService userPropDetailService;
    private final PropConfigService propConfigService;
    private final ZnsUserAccountService znsUserAccountService;
    private final MainActivityService mainActivityService;
    private final UserWearsBagService userWearsBagService;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final WearsI18nService wearsI18nService;
    private final WearsService wearsService;
    private final ZnsUserService znsUserService;
    private final RedissonClient redissonClient;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final MedalConfigService medalConfigService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final ISysConfigService sysConfigService;
    private final ActivityParamsService activityParamsService;

    /**
     * 获取奖励弹窗
     *
     * @param request
     * @param detail
     * @param entity
     * @param loginUser
     * @param appVersion
     */
    public void getUserReportPopInfo(DetailIdRequest request, RunningReportPopVo detail, ZnsUserRunDataDetailsEntity entity, ZnsUserEntity loginUser, Integer appVersion) {
        detail.setIsAllAward(YesNoStatus.NO.getCode());
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.error("getUserReportPopInfo InterruptedException:", e);
        }
        Long activityId = request.getActivityId();
        MainActivity mainActivity = mainActivityService.findById(activityId);
        List<String> mainTypeList = MainActivityTypeEnum.awardPopActivityTypes();
        if (Objects.isNull(mainActivity) || !mainTypeList.contains(mainActivity.getMainType())) {
            return;
        }
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime endTime = DateTimeUtil.parse(mainActivity.getActivityEndTime());
        if (Objects.nonNull(request.getIsGameFinished()) && YesNoStatus.YES.getCode().equals(request.getIsGameFinished())
                && DateUtil.lt(now, endTime)
                && MainActivityStateEnum.STARTED.getCode().equals(mainActivity.getActivityState())) {
            String awardPopKey = String.format(RedisKeyConstant.USER_REWARD_POP, entity.getId());
            Object awardPopValue = redissonClient.getBucket(awardPopKey).get();
            if (Objects.isNull(awardPopValue)) {
                //表示未发奖
                return;
            }
            String currencyCode = znsUserAccountService.getUserAccount(loginUser.getId()).getCurrencyCode();
            String languageCode = I18nMsgUtils.getLangCode();
            ReportPopInfoVo reportPopInfoVo = new ReportPopInfoVo();
            ZnsRunActivityEntity znsRunActivityEntity = new ZnsRunActivityEntity();
            znsRunActivityEntity.setId(activityId);
            reportPopInfoVo.setCurrency(I18nConstant.buildCurrency(currencyCode));
            reportPopInfoVo.setCouponNum(getUserCouponWithActivity(znsRunActivityEntity, entity));
            reportPopInfoVo.setAmount(getUserEggAmountWithActivity(znsRunActivityEntity, entity));
            reportPopInfoVo.setScore(getUserScoreWithActivity(znsRunActivityEntity, entity));
            reportPopInfoVo.setWearsVoList(getUserWearsByActivityId(activityId, loginUser.getId(), languageCode, entity.getCreateTime()));

            detail.setReportPopInfo(reportPopInfoVo);
            if(!mainActivity.getMainType().equals(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType())){
                ShareInfoVo shareInfoVo = getNewShareInfoVo(mainActivity, languageCode);
                detail.setShareInfoVo(shareInfoVo);
            }

            detail.setIsAllAward(YesNoStatus.YES.getCode());
            if(mainActivity.getMainType().equals(MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType())){
                FreeActivityConfig config = activityParamsService.findCacheOne(entity.getActivityId(), ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
                detail.setReportPopInfo(null);
                ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(entity.getActivityId(), loginUser.getId());
                if(Objects.nonNull(activityUser) && activityUser.getRank() >=0){
                    String key = RedisConstants.USER_LA_AWARD + activityUser.getActivityId() + ":" + loginUser.getId();
                    if (redissonClient.getBucket(key).isExists()) {
                        return;
                    }
                    // 道具模式不需要查询奖励弹窗
                    if (config.getMode().equals(FreeActivityModeEnum.PROP.getCode())) {
                        return;
                    }
                    String awardJson = sysConfigService.selectConfigByKey("limited.edition.award", true);
                    List<TrafficInvestmentAwardResponse> baseInfo = JsonUtil.readList(awardJson, TrafficInvestmentAwardResponse.class);
                    // 上榜奖励弹窗
                    List<AwardConfigDto> awardConfigDtoList = activityAwardConfigService.selectCacheAwardConfigDtoListBySendTypes(entity.getActivityId(), Collections.singletonList(AwardSentTypeEnum.ONLINE_RANK.getType()), null);
                    List<LimitedEditionAwardResponse> list = awardConfigDtoList.stream().map(d -> {
                        LimitedEditionAwardResponse limitedEditionAwardResponse = new LimitedEditionAwardResponse();

                        baseInfo.stream().filter(a -> Objects.equals(a.getType().getType(), d.getAwardType())).findFirst().ifPresent(a -> {
                            String titleKey = "limited.edition.award." + a.getType().getType() + ".title";
                            limitedEditionAwardResponse.setTitle(I18nMsgUtils.getLangMessage(languageCode, titleKey, d.getAmount() != null ? d.getAmount().toString() : ""));
                            if (AwardTypeEnum.SCORE.getType().equals(d.getAwardType())) {
                                limitedEditionAwardResponse.setTitle(I18nMsgUtils.getLangMessage(languageCode, titleKey, d.getScore() != null ? d.getScore().toString() : ""));
                            }
                            limitedEditionAwardResponse.setImageUrl(a.getImageUrl());
                        });
                        if (AwardTypeEnum.MEDAL.getType().equals(d.getAwardType())) {
                            MedalConfig medalConfig = medalConfigService.selectMedalConfigById(d.getMedalId());
                            if(Objects.nonNull(medalConfig)){
                                d.setMedalName(medalConfig.getName());
                                d.setMedalImageUrl(medalConfig.getUrl());
                            }
                        }
                        if(AwardTypeEnum.WEAR.getType().equals(d.getAwardType())){
                            Wears wear = wearsService.findWearByTypeId(Integer.valueOf(d.getWearType()), d.getWearValue());
                            d.setWearImageUrl(wear.getWearImageUrl());
                            d.setWearName(wear.getWearName());
                        }
                        limitedEditionAwardResponse.setFreeChallengeAward(d);
                        return limitedEditionAwardResponse;
                    }).toList();
                    detail.setLaAwards(list);
                    redissonClient.getBucket(key).set("1");
                }
            }
            log.info("新赛事完赛奖励弹窗,detailId:{}", entity.getId());
        }
    }

    private ShareInfoVo getShareInfoVo(RunningReportPopVo detail, ZnsRunActivityEntity znsRunActivityEntity) {
        ShareInfoVo shareInfoVo = new ShareInfoVo();
        shareInfoVo.setShareImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202309/idV13zcaRObj3379.png");
        shareInfoVo.setShareMsg("I got great rewards in \"" + znsRunActivityEntity.getActivityTitle() + "\", come and join me!");
        shareInfoVo.setShareUrl(detail.getActivityType().equals(RunActivityTypeEnum.OFFICIAL_CUMULATIVE_RUN.getType()) ? "lzrn://Race/MileStoneList" : mallH5Url + "/operational/runActivity/" + znsRunActivityEntity.getId() + "/0");
        return shareInfoVo;
    }

    private ShareInfoVo getNewShareInfoVo(MainActivity mainActivity, String languageCode) {
        ShareInfoVo shareInfoVo = new ShareInfoVo();
        shareInfoVo.setShareImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202309/idV13zcaRObj3379.png");
        String shareUrl = "";
        Long activityId = mainActivity.getId();
        if (MainActivityTypeEnum.singleTypes().contains(mainActivity.getMainType())) {
            shareUrl = "lznative://lzrace/EventDetails";
        } else if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainActivity.getMainType())) {
            SeriesActivityRel seriesActivityRel = seriesActivityRelService.findBySubId(activityId);
            activityId = seriesActivityRel.getParentActivityId();
            shareUrl = mallH5Url + "/series/activity/" + seriesActivityRel.getParentActivityId();
        }
        shareInfoVo.setShareUrl(shareUrl);
        ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityId, languageCode);
        String shareMsg = String.format(I18nMsgUtils.getMessage("activity.award.pop.msg"), activityDisseminate.getTitle());
        shareInfoVo.setShareMsg(shareMsg);
        return shareInfoVo;
    }


    private Integer getUserScoreWithActivity(ZnsRunActivityEntity znsRunActivityEntity, ZnsUserRunDataDetailsEntity entity) {
        return activityUserScoreService.sumScoreWithTime(znsRunActivityEntity.getId(), entity.getUserId(), 1, entity.getCreateTime());
    }

    private BigDecimal getUserEggAmountWithActivity(ZnsRunActivityEntity znsRunActivityEntity, ZnsUserRunDataDetailsEntity entity) {
        return znsUserAccountDetailService.selectSumAmountWithUserIdAndActivityIdAndTime(znsRunActivityEntity.getId(), entity);
    }

    private Integer getUserCouponWithActivity(ZnsRunActivityEntity znsRunActivityEntity, ZnsUserRunDataDetailsEntity entity) {
        return userCouponService.getCountUserCouponByActivityIdAndUserId(znsRunActivityEntity.getId(), entity.getUserId(), entity.getCreateTime(), entity.getModifieTime());
    }

    private List<AwardPopWearsVo> getUserWearsByActivityId(Long activityId, Long userId, String languageCode, ZonedDateTime createTime) {
        List<AwardPopWearsVo> list = new ArrayList<>();
        List<UserWearsBag> userWearsBagList = userWearsBagService.findList(userId, activityId, createTime);
        if (CollectionUtils.isEmpty(userWearsBagList)) {
            return list;
        }
        Integer gender = znsUserService.findById(userId).getGender();
        Map<String, List<UserWearsBag>> collect = new HashMap<>();
        for (UserWearsBag userWearsBag : userWearsBagList) {
            String key = userWearsBag.getWearType() + "_" + userWearsBag.getWearValue();
            if (collect.containsKey(key)) {
                collect.get(key).add(userWearsBag);
            } else {
                List<UserWearsBag> newUserWearsBagList = new ArrayList<>();
                newUserWearsBagList.add(userWearsBag);
                collect.put(key, newUserWearsBagList);
            }
        }
        collect.forEach((k, v) -> {
            UserWearsBag userWearsBag = v.get(0);
            AwardPopWearsVo awardPopWearsVo = new AwardPopWearsVo();
            awardPopWearsVo.setWearNum(v.size());
            Wears wears = wearsService.getWearByWearIdAndType(userWearsBag.getWearType(), userWearsBag.getWearValue());
            WearsI18nQuery query = WearsI18nQuery.builder().clothId(wears.getId()).langCode(languageCode).defaultLangCode(I18nConstant.LanguageCodeEnum.en_US.getCode()).build();
            WearsI18n wearsI18n = wearsI18nService.findByQuery(query);
            if (gender == 2) {
                awardPopWearsVo.setWearImageUrl(wears.getWomenWearUrl());
            } else {
                awardPopWearsVo.setWearImageUrl(wears.getMenWearUrl());
            }
            awardPopWearsVo.setWearName(Objects.nonNull(wearsI18n) ? wearsI18n.getName() : null);
            list.add(awardPopWearsVo);
        });
        return list;
    }

    private List<ReportPopProp> getUserPropListWithActivity(ZnsRunActivityEntity znsRunActivityEntity, ZnsUserRunDataDetailsEntity entity) {
        List<ReportPopProp> list = new ArrayList<>();
        List<UserPropDetail> propList = userPropDetailService.getUserPropListWithActivity(znsRunActivityEntity, entity);
        Map<Long, List<UserPropDetail>> collect = propList.stream().collect(Collectors.groupingBy(UserPropDetail::getPropId));
        collect.forEach((k, v) -> {
            ReportPopProp reportPopProp = new ReportPopProp();
            reportPopProp.setPropNum(v.stream().mapToInt(UserPropDetail::getPropNum).sum());
            PropConfig one = propConfigService.findOne(PropConfigQuery.builder()
                    .isDelete(YesNoStatus.NO.getCode()).propId(k)
                    .build());
            reportPopProp.setPropImageUrl(one.getPropImageUrl());
            reportPopProp.setPropName(one.getPropName());
            reportPopProp.setPropType(one.getPropType());
            list.add(reportPopProp);
        });
        return list;
    }
}
