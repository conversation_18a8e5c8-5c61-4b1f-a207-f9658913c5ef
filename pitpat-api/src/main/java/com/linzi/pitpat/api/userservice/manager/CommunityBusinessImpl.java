package com.linzi.pitpat.api.userservice.manager;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.activityservice.manager.UserActivityManager;
import com.linzi.pitpat.api.aspect.annotation.CommunityInteract;
import com.linzi.pitpat.api.userservice.converter.CommunityInteractConverter;
import com.linzi.pitpat.api.userservice.dto.response.ContentCanCreateDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CommunityRecommendActivityDto;
import com.linzi.pitpat.data.activityservice.model.dto.UserInGameDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.biz.MallSkuCouponBizService;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleI18n;
import com.linzi.pitpat.data.awardservice.model.query.ExchangeScoreRuleQuery;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleI18nService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubStateEnum;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.communityservice.dto.api.request.CommunityInteractReadRequestDto;
import com.linzi.pitpat.data.communityservice.dto.api.request.CommunityReportRequest;
import com.linzi.pitpat.data.communityservice.dto.api.response.CommunityInteractNewMsgResponseDto;
import com.linzi.pitpat.data.communityservice.dto.api.response.CommunityInteractPageResponseDto;
import com.linzi.pitpat.data.communityservice.dto.api.response.ContentClubInfo;
import com.linzi.pitpat.data.communityservice.dto.api.response.ContentGoodInfo;
import com.linzi.pitpat.data.communityservice.enums.HeatTypeEnum;
import com.linzi.pitpat.data.communityservice.model.entity.CommunityInteractDo;
import com.linzi.pitpat.data.communityservice.model.entity.CommunityTopicContentRelationDo;
import com.linzi.pitpat.data.communityservice.model.entity.CommunityTopicDo;
import com.linzi.pitpat.data.communityservice.model.query.CommunityInteractPageQuery;
import com.linzi.pitpat.data.communityservice.model.query.CommunityInteractQuery;
import com.linzi.pitpat.data.communityservice.service.CommunityCommentService;
import com.linzi.pitpat.data.communityservice.service.CommunityInteractService;
import com.linzi.pitpat.data.communityservice.service.CommunityReportService;
import com.linzi.pitpat.data.communityservice.service.CommunitySelectService;
import com.linzi.pitpat.data.communityservice.service.CommunityTopicContentRelationService;
import com.linzi.pitpat.data.communityservice.service.CommunityTopicService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.constant.CommunityConstant;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.CommunityContentPublishStatusEnum;
import com.linzi.pitpat.data.enums.CommunityContentPublishTypeEnum;
import com.linzi.pitpat.data.enums.CommunityContentShareSourceEnum;
import com.linzi.pitpat.data.enums.CommunityContentTypeEnum;
import com.linzi.pitpat.data.enums.CommunityFollowStatusEnum;
import com.linzi.pitpat.data.enums.CommunityUserTypeEnum;
import com.linzi.pitpat.data.enums.CourseDifficultyEnum;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsLanguageTitleVo;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsSkuLanguageVo;
import com.linzi.pitpat.data.mallservice.service.GoodsI18nService;
import com.linzi.pitpat.data.mallservice.service.GoodsSkuI18nService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.dto.request.CommunityContentPo;
import com.linzi.pitpat.data.systemservice.dto.request.CommunityPublishRequestDto;
import com.linzi.pitpat.data.systemservice.dto.request.MentionContentDto;
import com.linzi.pitpat.data.systemservice.dto.response.AppCommunityContentLikedVo;
import com.linzi.pitpat.data.systemservice.dto.response.AppCommunityContentVo;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.dto.response.CommunityContentPicResponseDto;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContent;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContentI18nEntity;
import com.linzi.pitpat.data.userservice.model.entity.UserKol;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.CommunityQuery;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.CommunityContentI18nService;
import com.linzi.pitpat.data.userservice.service.CommunityContentLikedService;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.UserBlacklistService;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.file.FileUtils;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.PageQuery;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommunityBusinessImpl implements CommunityBusiness {

    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    private static final String OPERATIONAL_ACTIVITY_URL = "/operational/runActivity/";

    private static final String OFFICIAL_ENENT_ROUTE_URL = "lzrn://Race/RankingRaceDetailPage";
    private static final String TEAM_RUN_ROUTE_URL = "lzrn://Race/FreeRaceDetailPage";
    private static final String CHALLENGE_RUN_ROUTE_URL = "lzrn://Race/BattleRaceDetailPage";
    private static final String OFFICIAL_TEAM_RUN_ROUTE_URL = "lzrn://Race/EventDetailPage";
    private static final String AGGREGATION_ACTIVITY_ROUTE_URL = "lzrn://Race/AggregatedDetailPage";
    private static final String COURSE_ROUTE_URL = "lzrn://Course/CourseDetailPage";
    private static final String TEAM_COMPLETION_ROUTE_URL = "lznative://lzrace/teamrun";
    private static final String NEW_ACTIVITY_URL = "lznative://lzrace/EventDetails";


    @Autowired
    private CommunityContentService communityContentService;

    @Resource
    private AppRouteConfigService appRouteConfigService;

    @Resource
    private ZnsRunActivityService runActivityService;

    @Autowired
    private ZnsUserService znsUserService;

    @Resource
    private CommunityContentLikedService communityContentLikedService;

    @Resource
    private CommunityContentI18nService communityContentI18nService;

    @Resource
    private ZnsUserFriendService znsUserFriendService;

    @Resource
    private AppRouteService appRouteService;

    @Resource
    private ZnsCourseService znsCourseService;
    @Resource
    private UserKolService userKolService;

    @Resource
    private UserBlacklistService userBlacklistService;

    @Resource
    private ClubService clubService;

    @Resource
    private UserLevelService userLevelService;

    @Resource
    private CommunityTopicContentRelationService communityTopicContentRelationService;

    @Resource
    private CommunityTopicService communityTopicService;


    @Resource
    private CommunityReportService communityReportService;
    @Resource
    private AppMessageService appMessageService;

    @Resource
    private UserActivityManager userActivityManager;
    @Resource
    private CommunityCommentService communityCommentService;
    @Resource
    private CommunitySelectService communitySelectService;


    @Resource
    private MallSkuCouponBizService mallCouponBizService;
    @Resource
    private GoodsI18nService goodsService;
    @Resource
    private GoodsSkuI18nService goodsSkuService;

    @Resource
    private ZnsGoodsSkuService znsGoodsSkuService;

    @Value("${communityContent.defaultLinkPic:1}")
    private String CommunityContentDefaultLinkPic;

    @Resource
    private MainActivityBizService mainActivityBizService;

    @Resource
    private ExchangeScoreRuleI18nService exchangeScoreRuleI18nService;

    @Resource
    private ExchangeScoreRuleService exchangeScoreRuleService;

    //举报客服
    @Value("${community.reportCustomer}")
    private String reportCustomer;


    @Value("${spring.profiles.active}")
    private String profile;


    @Resource(name = "completeFutureExecutor")
    private ThreadPoolTaskExecutor completeFutureExecutor;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommunityInteractService communityInteractService;
    @Resource
    private CommunityInteractConverter communityInteractConverter;

    @Resource
    private ActivityDisseminateService activityDisseminateService;

    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private UserExtraBizService userExtraBizService;
    @Resource
    private UserTaskBizService userTaskBizService;


    @FillerMethod
    @Override
    public Page<AppCommunityContentVo> list(CommunityContentPo po, Long loginUserId, Integer appVersion, String languageCode, Long userId) {
        //分页查询社区内容
        Boolean isEmpty = false;
        Page<CommunityContent> page = new Page<>(po.getPageNum(), po.getPageSize());
        CommunityQuery communityQuery = CommunityQuery.builder().publishStatus(CommunityContentPublishStatusEnum.PUBLISHED.getCode())
                .languageCode(languageCode).topicId(po.getTopicId()).sortType(po.getSortType()).isHome(po.getIsHome()).build();
        if (po.getSource() != null && po.getSource() == 0) {
            communityQuery.setUserId(userId);// 查询的帖子创建人id
        } else if (po.getSource() != null && po.getSource() == 1) {
            List<Long> ids = communityContentLikedService.findContentIdsByUserId(loginUserId);
            ids.addAll(communityCommentService.findContentIdsByUserId(loginUserId));
            ids = ids.stream().distinct().collect(Collectors.toList());
            communityQuery.setContentIds(ids);
            if (CollectionUtils.isEmpty(ids)) {
                isEmpty = true;
            }

        }
        //版本控制视频内容
        if (appVersion < 4030) {
            communityQuery.setNeContentTypes(List.of(4, 5));
        }
        //地板版不显示

        //4.5.0之前不展示同步的商品评价
        if (appVersion < Constant.appVersion_40500) {
            communityQuery.setSkuId(0L);
        }
        Page<CommunityContent> pageList = new Page<>();
        if (po.getIsNewHomePage() != null && po.getIsNewHomePage() == 1) {
            pageList = communityContentService.findNewHomePage(page, communityQuery);
        } else {
            if (!isEmpty) {
                pageList = communityContentService.findPage(page, communityQuery);
            }
        }


        //重新组装page，保证do不会出现在controller
        Page<AppCommunityContentVo> pageVo = new Page<>(po.getPageNum(), po.getPageSize());
        pageVo.setTotal(pageList.getTotal());
        Integer appType = getAppType();
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(loginUserId, null);
        Map<String, String> map = MDC.getCopyOfContextMap();
        //赛事3.0老版本控制
        pageVo.setRecords(pageList.getRecords().stream()
                .filter(e -> !(Objects.equals(e.getRedirectType(), 8) && appVersion < 3000))
                .map(e -> {
                    Optional.ofNullable(map).ifPresent(MDC::setContextMap);
                    return contentDoToVO(e, loginUserId, appType, languageCode);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        //显示商品评价同步过来的内容
        fillSkuInfo(pageVo, mallCountryCode);

        //主页查询
        if (po.getIsHome() == 1) {
            Integer pageNum = po.getPageNum();
            //默认size为10
            List<CommunityRecommendActivityDto> recommendActivityDtoList = mainActivityBizService.getCommunityRecommendActivityDtoList(languageCode, 6);
            CommunityRecommendActivityDto activityDto1 = null;
            CommunityRecommendActivityDto activityDto2 = null;
            int startIndex = (pageNum - 1) * 2;
            for (int i = 0; i < recommendActivityDtoList.size(); i++) {
                if (i == startIndex) {
                    activityDto1 = recommendActivityDtoList.get(i);
                }
                if (i == startIndex + 1) {
                    activityDto2 = recommendActivityDtoList.get(i);
                }
            }

            List<AppCommunityContentVo> records = pageVo.getRecords();
            for (int i = 0; i < records.size(); i++) {
                if (i == 4 && activityDto1 != null) {
                    AppCommunityContentVo contentVo = records.get(i);
                    contentVo.setType(1);
                    contentVo.setCommunityRecommendActivityDto(activityDto1);
                }
                if (i == 9 && activityDto2 != null) {
                    AppCommunityContentVo contentVo = records.get(i);
                    contentVo.setType(1);
                    contentVo.setCommunityRecommendActivityDto(activityDto2);
                }
            }

            //加入精选
            if (pageNum == 1) {
                CommunityContent selectContent1 = null;
                CommunityContent selectContent2 = null;
                List<CommunityContent> selectContents = communitySelectService.findSelectContent(2);
                for (int i = 0; i < selectContents.size(); i++) {
                    if (i == 0) {
                        selectContent1 = selectContents.get(i);
                    }
                    if (i == 1) {
                        selectContent2 = selectContents.get(i);
                    }
                }
                for (int i = 0; i < records.size(); i++) {
                    if (i == 1 && selectContent1 != null) {
                        records.set(i, contentDoToVO(selectContent1, loginUserId, appType, languageCode));
                    }
                    if (i == 5 && selectContent2 != null) {
                        records.set(i, contentDoToVO(selectContent2, loginUserId, appType, languageCode));

                    }
                }
            }

        }

        return pageVo;
    }

    /**
     * 如果是商品评价同步过来的，需要填充商品信息
     *
     * @param pageVo Page<AppCommunityContentVo>
     */
    private void fillSkuInfo(Page<AppCommunityContentVo> pageVo, String mallCountryCode) {
        List<AppCommunityContentVo> records = pageVo.getRecords();
        fillSkuInfoList(records, mallCountryCode);
    }

    private void fillSkuInfoList(List<AppCommunityContentVo> records, String mallCountryCode) {
        List<Long> skuIdList = records.stream().map(AppCommunityContentVo::getSkuId).filter(Objects::nonNull).filter(item -> item > 0).collect(Collectors.toSet()).stream().toList();
        if (CollectionUtils.isEmpty(skuIdList)) {
            return;
        }
        List<GoodsSkuLanguageVo> goodsSkuList = goodsSkuService.findLanguageList(skuIdList, UserContextHolder.getLocal());
        if (!CollectionUtils.isEmpty(goodsSkuList)) {

            List<Long> goodsIdList = goodsSkuList.stream().map(GoodsSkuLanguageVo::getGoodsId).collect(Collectors.toSet()).stream().toList();
            List<GoodsLanguageTitleVo> goodsList = goodsService.findLanguageTitleList(goodsIdList, UserContextHolder.getLocal());

            if (!CollectionUtils.isEmpty(goodsList)) {
                Map<Long, GoodsLanguageTitleVo> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsLanguageTitleVo::getGoodsId, Function.identity()));
                Map<Long, GoodsSkuLanguageVo> goodsSkuMap = goodsSkuList.stream().collect(Collectors.toMap(GoodsSkuLanguageVo::getSkuId, Function.identity()));

                records.forEach(item -> {
                    if (Objects.nonNull(item.getSkuId()) && item.getSkuId() > 0) {
                        ContentGoodInfo goodInfo = new ContentGoodInfo();
                        goodInfo.setSkuId(item.getSkuId());
                        item.setGoodInfo(goodInfo);
                        GoodsSkuLanguageVo goodsSku = goodsSkuMap.get(item.getSkuId());
                        if (Objects.nonNull(goodsSku)) {
                            GoodsLanguageTitleVo goods = goodsMap.get(goodsSku.getGoodsId());
                            if (Objects.nonNull(goods)) {
                                goodInfo.setGoodId(goodsSku.getGoodsId());
                                goodInfo.setSkuPic(goodsSku.getPic());
                                goodInfo.setGoodName(goods.getTitle());
                                List<Coupon> couponList = mallCouponBizService.findAllCouponBySku(List.of(item.getSkuId()), true, mallCountryCode);
                                goodInfo.setIsHasCoupons(CollectionUtils.isEmpty(couponList) ? 0 : 1);
                                ZnsGoodsSkuEntity minPriceSku = znsGoodsSkuService.findMinPriceSku(goods.getGoodsId());
                                if (minPriceSku != null) {
                                    goodInfo.setSkuId(minPriceSku.getId());
                                }
                            }
                        } else {
                            log.error("[fillSkuInfo] communityId={}, skuId={} fail to get sku or goods info,", item.getId(), item.getSkuId());
                        }
                    }
                });
            }
        }
    }

    @Override
    public void shareContent(CommunityContentPo po, Integer appVersion, ZnsUserEntity znsUser) {
        log.info("用户社区分享报告:{}", po);
        CommunityContent communityContent = new CommunityContent();
        if (appVersion >= 4030) {
            communityContent.setPics(JsonUtil.writeString(po.getPicsInfo()));
        } else {
            List<String> pics = po.getPics();
            List<CommunityContentPicResponseDto> picsInfo = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pics)) {
                for (String pic : pics) {
                    CommunityContentPicResponseDto communityContentPicResponseDto = FileUtils.realPic(pic);
                    picsInfo.add(communityContentPicResponseDto);
                }
                communityContent.setPics(JsonUtil.writeString(picsInfo));
            }
        }
        Integer shareSource = po.getShareSource();
        String contentParam = po.getContentParam();
        fitCommunityContent(communityContent, shareSource, contentParam);
        communityContent.setUserId(po.getUserId());
        communityContent.setUserName(po.getUserName());
        communityContent.setHeadPortrait(po.getHeadPortrait());
        communityContent.setUserEmail(po.getUserEmail());
        communityContent.setRedirectType(BannerJumpTypeEnum.NO_JUMP.getJumpType());
        communityContent.setContentType(CommunityContentTypeEnum.TEXT_AND_IMAGE.getCode());
        communityContent.setPublishType(CommunityContentPublishTypeEnum.INSTANT.getCode());
        communityContent.setPublishStatus(CommunityContentPublishStatusEnum.PUBLISHED.getCode());
        communityContent.setUserType(CommunityUserTypeEnum.REAL_USER.getCode());
        communityContent.setPublishExpectTime(ZonedDateTime.now());
        communityContent.setPublishRealTime(ZonedDateTime.now());
        communityContentService.insertCommunityContent(communityContent);

        // 发送动态Im 通知粉丝
//        noticeUserFansIm(znsUser);
    }


    /**
     * 设置社区内容
     *
     * @param communityContent
     * @param shareSource
     * @param contentParam
     */
    private static void fitCommunityContent(CommunityContent communityContent, Integer shareSource, String contentParam) {
        if (CommunityContentShareSourceEnum.EVENT_REPORT.getCode().equals(shareSource)) {
            String content = "I tried the %s, and it was amazing. Let me show you what I get from it! #share my running data.";
            communityContent.setContent(String.format(content, JsonUtil.readValue(contentParam).get("title")));
        } else if (CommunityContentShareSourceEnum.FREE_RUN.getCode().equals(shareSource)) {
            Map<Integer, String> map = new HashMap<>();
            map.put(1, "Taking control of your health means taking control of your life.");
            map.put(2, "Only by persevering can you be lucky enough.");
            map.put(3, "It may take a while and can be hard to reach when you first see it, but never give up and keep working, it will come closer.");
            communityContent.setContent(map.get(new Random().nextInt(3) + 1));
        } else if (CommunityContentShareSourceEnum.COURSE_RUN.getCode().equals(shareSource)) {
            String content = "I want to share one of my favorite lessons that helped me burn %s calories today. #share my running data.";
            communityContent.setContent(String.format(content, JsonUtil.readValue(contentParam).get("kilocalorie")));
        } else if (CommunityContentShareSourceEnum.OFFLINE_PK.getCode().equals(shareSource)) {
//            String content = "I tried the Run %s miles to win $%s ,and it was amazing. Let me show you what I get from it! #share my running data.";
//            communityContent.setContent(String.format(content, JsonUtil.readValue(contentParam).get("mileage"),JsonUtil.readValue(contentParam).get("award")));
            communityContent.setContent("Share today's victory, come join me to participate in the daily exercise mileage PK and win rewards easily.#Daily PK to win Reward#");
        } else if (CommunityContentShareSourceEnum.MONTH_REPORT.getCode().equals(shareSource)) {
            if (StringUtils.hasText(contentParam)) {
                communityContent.setContent(contentParam);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CommunityInteract(action = "帖子like", idExpression = "#contentId", type = InteractionType.COMMUNITY_LIKE)
    public AppCommunityContentLikedVo toggleLike(Long contentId, Long loginUserId) {
        AppCommunityContentLikedVo vo = new AppCommunityContentLikedVo();
        Integer likedStatus = communityContentLikedService.getLikedStatus(contentId, loginUserId);
        if (CommunityConstant.LIKED == likedStatus) {
            // 取消点赞
            communityContentLikedService.unlikePost(contentId, loginUserId);
            communityContentService.unlikePost(contentId);
            vo.setLikedStatus(CommunityConstant.UNLIKED);
        } else {
            // 点赞
            communityContentLikedService.likePost(contentId, loginUserId);
            communityContentService.likePost(contentId);
            vo.setLikedStatus(CommunityConstant.LIKED);
            communitySelectService.joinSelect(contentId);
            // 完成点赞事件
            userTaskBizService.completeEvent(new EventTriggerDto().setUserId(loginUserId).setEventSubType(TaskConstant.TakEventSubTypeEnum.SOCIAL_LIKE_POST.getCode()));
            //增加话题热度
            List<CommunityTopicDo> communityTopicDoList = communityTopicContentRelationService.findByTopicByContentId(contentId);
            for (CommunityTopicDo communityTopicDo : communityTopicDoList) {
                communityTopicService.increaseHeat(HeatTypeEnum.like, communityTopicDo);
            }
            //添加到有效话题集合
            List<Long> topicIds = communityTopicDoList.stream()
                    .filter(x -> YesNoStatus.YES.getCode().equals(x.getIsShow()))
                    .map(CommunityTopicDo::getId).collect(Collectors.toList());
            redissonClient.getSet(RedisKeyConstant.COMMUNITY_VALID_TOPIC).addAll(topicIds);
        }
        return vo;
    }

    @Override
    @CommunityInteract(action = "关注用户发帖", type = InteractionType.USER_POST)
    public Long publishContent(CommunityPublishRequestDto publishRequestDto, ZnsUserEntity znsUser, Integer appVersion) {
        log.info("用户发布帖子:{},用户ID：{}", publishRequestDto, znsUser.getId());

        if (!StringUtils.hasText(publishRequestDto.getContent()) && !StringUtils.hasText(publishRequestDto.getVideoUrl()) && CollectionUtils.isEmpty(publishRequestDto.getUrls()) && (publishRequestDto.getRunActivityId() == null || publishRequestDto.getRunActivityId() == 0)) {
            throw new BaseException("Please enter the content");
        }
        // 校验内容是否有敏感词
        Boolean hotWordsFlag = sysConfigService.isHotWords(UserContextHolder.getLocal(), publishRequestDto.getContent());
        CommunityContent communityContent = new CommunityContent();
        communityContent.setContent(publishRequestDto.getContent());
        communityContent.setMentionContent(JsonUtil.writeString(publishRequestDto.getMentionContentList()));
        communityContent.setUserId(znsUser.getId());
        communityContent.setUserName(znsUser.getFirstName());
        communityContent.setHeadPortrait(znsUser.getHeadPortrait());
        communityContent.setUserEmail(znsUser.getEmailAddress());
        communityContent.setPics(!CollectionUtils.isEmpty(publishRequestDto.getUrls()) ? JsonUtil.writeString(publishRequestDto.getUrls()) : null);
        communityContent.setRedirectType(BannerJumpTypeEnum.NO_JUMP.getJumpType());
        communityContent.setContentType(publishRequestDto.getContentType());
        communityContent.setPublishType(CommunityContentPublishTypeEnum.INSTANT.getCode());
        communityContent.setPublishStatus(CommunityContentPublishStatusEnum.PUBLISHED.getCode());
        communityContent.setUserType(CommunityUserTypeEnum.REAL_USER.getCode());
        communityContent.setPublishExpectTime(ZonedDateTime.now());
        communityContent.setPublishRealTime(ZonedDateTime.now());
        communityContent.setSkuId(publishRequestDto.getSkuId());
        communityContent.setClubId(publishRequestDto.getClubId());
        if (Objects.nonNull(publishRequestDto.getRunActivityId())) {
            communityContent.setRunActivityId(publishRequestDto.getRunActivityId());
            Optional<MainActivity> mainActivity = mainActivityBizService.getMainActivity(publishRequestDto.getRunActivityId());
            if (mainActivity.isPresent()) {
                communityContent.setMainActivityType(mainActivity.get().getMainType());
                communityContent.setRunActivityId(mainActivity.get().getId());
                communityContent.setRedirectType(8);
                List<ActivityDisseminate> byActId = activityDisseminateService.findByActId(mainActivity.get().getId());
                if (!CollectionUtils.isEmpty(byActId)) {
                    byActId.stream().filter(language -> language.getLanguageCode().equals(znsUser.getLanguageCode())).findFirst().ifPresentOrElse(dis -> {
                        String coverPic = dis.getDetailTopPromoPic();
                        if (StringUtils.hasText(coverPic)) {
                            CommunityContentPicResponseDto pic = new CommunityContentPicResponseDto();
                            pic.setPic(coverPic);
                            communityContent.setPics(JsonUtil.writeString(Collections.singletonList(pic)));
                        }

                    }, () -> {
                        byActId.stream().filter(defaultLanguage -> defaultLanguage.getIsDefault() == 1).findAny().ifPresent(dis -> {
                                    String coverPic = dis.getDetailTopPromoPic();
                                    if (StringUtils.hasText(coverPic)) {
                                        CommunityContentPicResponseDto pic = new CommunityContentPicResponseDto();
                                        pic.setPic(coverPic);
                                        communityContent.setPics(JsonUtil.writeString(Collections.singletonList(pic)));
                                    }
                                }
                        );
                    });
                }
            }
        }
        communityContentService.insertCommunityContent(communityContent);
        CommunityContentI18nEntity communityContentI18nEntity = new CommunityContentI18nEntity();
        communityContentI18nEntity.setContentId(communityContent.getId());
        communityContentI18nEntity.setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
        communityContentI18nEntity.setLanguageName(I18nConstant.LanguageCodeEnum.en_US.getName());
        communityContentI18nEntity.setContent(publishRequestDto.getContent());
        communityContentI18nEntity.setVideoUrl(publishRequestDto.getVideoUrl());
        communityContentI18nEntity.setVideoCoverUrl(publishRequestDto.getVideoCoverUrl());
        communityContentI18nEntity.setVideoCoverHeight(publishRequestDto.getVideoCoverHeight());
        communityContentI18nEntity.setVideoCoverWidth(publishRequestDto.getVideoCoverWidth());
        communityContentI18nEntity.setPics(communityContent.getPics());
        communityContentI18nEntity.setMainActivityType(communityContent.getMainActivityType());
        communityContentI18nEntity.setRunActivityId(communityContent.getRunActivityId());
        communityContentI18nEntity.setRedirectType(communityContent.getRedirectType());
        communityContentI18nService.insert(communityContentI18nEntity);
        List<Long> topicIds = publishRequestDto.getTopicIds();
        if (StringUtils.hasText(publishRequestDto.getCreateTopic())) {
            Long topicId = communityTopicService.creatAppTopic(publishRequestDto.getCreateTopic(), znsUser);
            topicIds.add(topicId);
        }
        if (!CollectionUtils.isEmpty(topicIds)) {
            List<CommunityTopicContentRelationDo> list = topicIds.stream().map(topicId -> {
                CommunityTopicContentRelationDo relationDo = new CommunityTopicContentRelationDo();
                relationDo.setContentId(communityContent.getId());
                relationDo.setTopicId(topicId);
                return relationDo;
            }).collect(Collectors.toList());
            communityTopicContentRelationService.batchCreate(list);
            //添加到有效话题集合
            redissonClient.getSet(RedisKeyConstant.COMMUNITY_VALID_TOPIC).addAll(topicIds);
        }
        // 完成发帖事件
        userTaskBizService.completeEvent(new EventTriggerDto().setUser(znsUser).setEventSubType(TaskConstant.TakEventSubTypeEnum.SOCIAL_POST.getCode()));
        if (hotWordsFlag) {
            communityReportService.reportContent(communityContent.getId(), "系统判断", Long.parseLong(reportCustomer));
            throw new BaseException(I18nMsgUtils.getMessage("goods.comment.validate.content.sensitiveWord"), UserError.COMMUNITY_CONTENT_SENSITIVE_WORD_FAIL.getCode());
        }
        return communityContent.getId();
//        noticeUserFansIm(znsUser);
    }

    private void noticeUserFansIm(ZnsUserEntity znsUser) {
        Integer fansCount = znsUserFriendService.getFansCount(znsUser.getId());
        if (fansCount > 0) {
            List<Long> userFans = znsUserFriendService.getFansUserIdList(znsUser.getId());
            CompletableFuture.runAsync(() -> userFans.forEach(i -> {
                        String key = RedisConstants.USER_FANS_NOTICE_KEY + "_" + znsUser.getId() + "_" + i;
                        if (!redissonClient.getBucket(key).isExists()) {
                            redissonClient.getBucket(key).set("1", 24, TimeUnit.HOURS);
                            // 发送自定义消息
                            ImMessageBo imMessageBo = new ImMessageBo();
                            Map<String, Object> params = new HashMap<>();
                            imMessageBo.setParams(params);
                            imMessageBo.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/1744274632127.png");
                            imMessageBo.setJumpValue("lznative://main/main");
                            HashMap<String, Object> hashMap = new HashMap<>();
//                            hashMap.put("home_page", 2);
//                            hashMap.put("discoverType", 0);
                            hashMap.put("pageKe", "message");
                            hashMap.put("subKey", "community");
                            imMessageBo.setParams(hashMap);
                            imMessageBo.setJumpData(hashMap);
                            imMessageBo.setMsg(I18nMsgUtils.getMessage("user.share.notice.fans.im.content"));
                            imMessageBo.setUserName(znsUser.getFirstName());
                            imMessageBo.setHandleType(0);
                            imMessageBo.setJumpType("6");
                            imMessageBo.setBusinessID("system_notification");
                            String jsonStr = JsonUtil.writeString(imMessageBo);
                            appMessageService.sendIm("administrator", Collections.singletonList(i), jsonStr, TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
                        } else {
                            log.info("已存在24内发送的消息标识 friendId:{}", i);
                        }
                    }), completeFutureExecutor)
                    .exceptionally(ex -> {
                        // 处理 CompletableFuture 执行过程中的异常
                        log.error("任务执行过程中出现异常", ex);
                        return null;
                    });
        }
    }

    @Override
    public Boolean canCreate(ZnsUserEntity userEntity) {
        UserKol kol = userKolService.findByUserIdSigned(userEntity.getId());
        return Objects.nonNull(kol);
    }

    @Override
    public ContentCanCreateDto canCreateV2(ZnsUserEntity user) {
        boolean inBlackList = userBlacklistService.isInBlackList(user.getId());
        ContentCanCreateDto contentCanCreateDto = new ContentCanCreateDto();
        contentCanCreateDto.setCanCreate(!inBlackList);
        contentCanCreateDto.setIsinBlackList(inBlackList);
        return contentCanCreateDto;
    }

    @Override
    public void reportContent(CommunityReportRequest request, ZnsUserEntity user) {
        //保存
        communityReportService.reportContent(request.getId(), request.getReportContent(), user.getId());
        log.info("站内信消息通知");
        List<Long> ids = new ArrayList<>();
        ids.add(user.getId());
        appMessageService.sendIm(reportCustomer, ids, I18nMsgUtils.getMessage("community.report.notice"), TencentImConstant.TIM_TEXT_ELEM, OrderUtil.getBatchNo(), 0, false);
    }

    @Override
    public void deleteContent(Long contentId) {
        communityContentService.deleteContent(contentId);
    }


    private AppCommunityContentVo contentDoToVO(CommunityContent communityContent, Long loginUserId, Integer appType, String languageCode) {
        try {
            Long userId = communityContent.getUserId();
            AppCommunityContentVo vo = new AppCommunityContentVo();
            BeanUtils.copyProperties(communityContent, vo);
            //todo 版本上线后可以去除，避免预发报错
            List<CommunityContentPicResponseDto> pics = null;
            try {
                pics = JsonUtil.readList(communityContent.getPics(), CommunityContentPicResponseDto.class);
            } catch (Exception e) {
                List<String> list = JsonUtil.readList(communityContent.getPics(), String.class);
                pics = list.stream().map(s -> {
                    CommunityContentPicResponseDto communityContentPicResponseDto = new CommunityContentPicResponseDto();
                    communityContentPicResponseDto.setPic(s);
                    communityContentPicResponseDto.setWidth(100);
                    communityContentPicResponseDto.setHeight(100);
                    return communityContentPicResponseDto;
                }).collect(Collectors.toList());
            }
            vo.setPicsInfo(pics);
            if (!CollectionUtils.isEmpty(pics)) {
                List<String> list = pics.stream().map(CommunityContentPicResponseDto::getPic).collect(Collectors.toList());
                vo.setPics(list);
            }
            CommunityContentPicResponseDto build = new CommunityContentPicResponseDto();
            CommunityContentI18nEntity i18nEntity = communityContentI18nService.selectByContentIdAndLanguageCode(communityContent.getId(), languageCode);
            if (Objects.isNull(i18nEntity)) {
                i18nEntity = communityContentI18nService.selectByContentIdAndLanguageCode(communityContent.getId(), communityContent.getDefaultLangCode());
            }
            if (Objects.nonNull(i18nEntity)) {
                vo.setVideoUrl(i18nEntity.getVideoUrl());
                build.setPic(i18nEntity.getVideoCoverUrl());
                build.setHeight(Objects.nonNull(i18nEntity.getVideoCoverHeight()) ? i18nEntity.getVideoCoverHeight() : 100);
                build.setWidth(Objects.nonNull(i18nEntity.getVideoCoverWidth()) ? i18nEntity.getVideoCoverWidth() : 100);
                vo.setVideoInfo(build);
            }
            ZonedDateTime publishDate = communityContent.getPublishRealTime();
            vo.setPublishTime(publishDate.toInstant().toEpochMilli());
            vo.setLikedNum(communityContent.getDisplayedLikedNum());
            vo.setLikedStatus(communityContentLikedService.getLikedStatus(communityContent.getId(), loginUserId));
            // 设置关注状态
            fitFollowStatus(loginUserId, userId, vo);
            //组装用户信息
            ZnsUserEntity znsUser = znsUserService.findById(userId);
            if (znsUser != null) {
                vo.setUserName(znsUser.getFirstName());
                vo.setHeadPortrait(znsUser.getHeadPortrait());
                vo.setMemberType(znsUser.getMemberType());
                vo.setUserId(znsUser.getId());
            }
            if (CommunityUserTypeEnum.AUTHORITY.getCode().equals(communityContent.getUserType())
                    || BannerJumpTypeEnum.NEW_ACTIVITY.getJumpType().equals(communityContent.getRedirectType())) {
                // 目前只针对官方内容
                assembleRoute(vo, communityContent, loginUserId, appType, i18nEntity);
            }
            // 该内容置顶
            if (communityContent.getTopSort() > 0) {
                vo.setTopStatus(1);
            } else {
                vo.setTopStatus(0);
            }
            //组装俱乐部信息
            log.info("{}组装俱乐部信息:{}", communityContent.getId(), communityContent.getClubId());
            if (communityContent.getClubId() != null) {
                Club club = clubService.findById(communityContent.getClubId());
                if (club != null && ClubStateEnum.NORMAL.getCode().equals(club.getState())) {
                    ContentClubInfo clubInfo = new ContentClubInfo();
                    clubInfo.setClubName(club.getName());
                    clubInfo.setClubLogo(club.getLogo());
                    clubInfo.setClubId(club.getId());
                    clubInfo.setOriginalClubLevel(club.getClubLevel());
                    vo.setClubInfo(clubInfo);
                }
            }
            //填充用户等级
            vo.setUserLevel(userLevelService.findByUserId(userId).getLevel());
            //todo 填充商品信息
            if (Objects.nonNull(communityContent.getSkuId())) {

            }
            Optional<UserInGameDto> optionalUserInGameDto = userActivityManager.checkUserInCompetitiveGame(userId);
            optionalUserInGameDto.ifPresent(userInGameDto -> {
                if (userInGameDto.isInGame()) {
                    ZonedDateTime startOfYesterday = ZonedDateTime.now().minusDays(1).with(LocalTime.MIN);
                    ZonedDateTime yesterdayDate = Date.from(startOfYesterday.toInstant());
                    if (communityContent.getPublishRealTime().compareTo(yesterdayDate) > 0) {
                        vo.setIsWatch(1);
                    }
                } else {
                    vo.setIsWatch(0);
                }
            });
            vo.setCommentNum(communityCommentService.getCommentNum(communityContent.getId()));
            vo.setTopicInfos(communityTopicService.findByContentIdAndLanguageCode(communityContent.getId(), languageCode));
            vo.setIsEnableComment(communityContent.getIsEnableComment());

            return vo;
        } catch (Exception e) {
            log.info("[AppCommunityContentVo] ERROR :{}", communityContent.getPics(), e);
            return null;
        }
    }

    private void fitFollowStatus(Long loginUserId, Long userId, AppCommunityContentVo vo) {
        Integer relationType = znsUserFriendService.getRelationType(loginUserId, userId);
        // -1表示取关，0表示粉丝，1表示互相关注，2表示我关注的人
        if (relationType > 0) {
            vo.setFollowStatus(CommunityFollowStatusEnum.FOLLOWING.getCode());
        } else if (relationType <= 0) {
            vo.setFollowStatus(CommunityFollowStatusEnum.NOT_FOLLOWING.getCode());
        }
        if (loginUserId.equals(userId)) {
            vo.setFollowStatus(CommunityFollowStatusEnum.SELF.getCode());
        }
    }

    private void assembleRoute(AppCommunityContentVo vo, CommunityContent content, Long userId, Integer appType, CommunityContentI18nEntity i18nEntity) {
        PrimitiveForest forest = getPrimitiveForest(i18nEntity, userId);
        AppRoute route = appRouteService.findRoute(forest, appType);
        if (Objects.nonNull(route)) {
            vo.setJumpUrl(route.getJumpUrl());
            vo.setJumpParam(route.getJumpParam());
        }
        vo.setLinkPic(content.getJumpImg());
        if (BannerJumpTypeEnum.COURSE_URL.getJumpType().equals(content.getRedirectType())) {
            ZnsCourseEntity znsCourseEntity = znsCourseService.selectById(content.getCourseId());
            if (znsCourseEntity != null) {
                CourseDifficultyEnum difficultyEnum = CourseDifficultyEnum.getNameByDifficulty(znsCourseEntity.getDifficulty());
                String difficultyName = I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(I18nMsgUtils.getLangCode()) ? difficultyEnum.getNameFr() : difficultyEnum.getName();
                vo.setLinkDesc(difficultyName);
                vo.setLinkPic(znsCourseEntity.getBackgroundPicture());
            }
        }
        vo.setLinkTitle(content.getTitle());
        if (BannerJumpTypeEnum.POINTS_MALL.getJumpType().equals(content.getRedirectType())) {
            ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(i18nEntity.getGoodsId());
            if (Objects.nonNull(exchangeScoreRule)) {
                ExchangeScoreRuleQuery query = ExchangeScoreRuleQuery.builder().ruleId(i18nEntity.getGoodsId())
                        .langCode(I18nMsgUtils.getLangCode()).defaultLangCode(exchangeScoreRule.getDefaultLangCode()).build();
                ExchangeScoreRuleI18n exchangeScoreRuleI18n = exchangeScoreRuleI18nService.selectOneByQuery(query);
                vo.setLinkTitle(exchangeScoreRuleI18n.getActivityName());
                vo.setLinkPic(exchangeScoreRuleI18n.getAdvertiseImage());
            }
        }
        if (StringUtil.isEmpty(vo.getLinkPic())) {
            vo.setLinkPic(CommunityContentDefaultLinkPic);
        }
    }

    private PrimitiveForest getPrimitiveForest(CommunityContentI18nEntity content, Long userId) {
        PrimitiveForest forest = new PrimitiveForest();
        forest.setUserId(userId);
        forest.setJumpType(content.getRedirectType());
        forest.setRouteId(content.getRouteId());
        forest.setRunActivityId(content.getRunActivityId());
        forest.setJumpUrl(content.getActivityUrl());
        forest.setMallJumpType(content.getMallJumpType());
        forest.setCategoryCode(content.getCategoryCode());
        forest.setGoodsId(content.getGoodsId());
        forest.setCourseId(content.getCourseId());
        forest.setMainActivityType(content.getMainActivityType());
        return forest;
    }

    /**
     * 获取设备类型，  1,"android", 2,"ios"
     *
     * @return
     */
    private Integer getAppType() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appType = request.getHeader("appType");
        return MapUtil.getInteger(appType, 0);
    }

    @Override
    @FillerMethod
    public AppCommunityContentVo getDetail(Long id, String languageCode, ZnsUserEntity loginUser) {
        CommunityContent communityContent = communityContentService.findByIdJoinI18n(id, loginUser.getLanguageCode());
        if (Objects.isNull(communityContent)) {
            throw new BaseException("", UserError.CONTENT_NOT_EXIST.getCode());
        }
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(loginUser.getId(), null);
        AppCommunityContentVo appCommunityContentVo = contentDoToVO(communityContent, loginUser.getId(), UserContextHolder.getAppType(), languageCode);
        if (appCommunityContentVo != null) {
            fillSkuInfoList(List.of(appCommunityContentVo), mallCountryCode);
        }
        if (communityContent.getUserType().equals(CommunityUserTypeEnum.AUTHORITY.getCode())) {
            CommunityContentI18nEntity i18nEntity = communityContentI18nService.selectByContentIdAndLanguageCode(communityContent.getId(), I18nConstant.LanguageCodeEnum.en_US.getCode());
            if (Objects.nonNull(i18nEntity)) {
                appCommunityContentVo.setTitle(i18nEntity.getTitle());
                appCommunityContentVo.setContent(i18nEntity.getContent());
            }
        }
        List<MentionContentDto> mentionContentDtoList = JsonUtil.readList(communityContent.getMentionContent(), MentionContentDto.class);
        appCommunityContentVo.setMentionContentList(mentionContentDtoList);
        return appCommunityContentVo;
    }

    @Override
    @FillerMethod
    public Page<AppCommunityContentVo> myFollowedContent(PageQuery pageQuery, String languageCode, ZnsUserEntity loginUser) {
        List<Long> followAndFriendUserIdList = znsUserFriendService.findFollowAndFriendUserIdList(loginUser.getId());
        if (CollectionUtils.isEmpty(followAndFriendUserIdList)) {
            return Page.of(pageQuery.getPageNum(), pageQuery.getPageSize());
        }
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(loginUser.getId(), null);
        LambdaQueryWrapper<CommunityContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommunityContent::getPublishStatus, CommunityContentPublishStatusEnum.PUBLISHED.getCode())
                .in(CommunityContent::getUserId, followAndFriendUserIdList)
                .eq(CommunityContent::getIsShow, 1)
                .orderByDesc(CommunityContent::getGmtCreate);
        Page<CommunityContent> pageList = communityContentService.pageList(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()), wrapper);
        Page<AppCommunityContentVo> pageVo = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        pageVo.setTotal(pageList.getTotal());
        Integer appType = getAppType();
        Map<String, String> map = MDC.getCopyOfContextMap();
        //赛事3.0老版本控制
        pageVo.setRecords(pageList.getRecords().stream()
                .map(e -> {
                    Optional.ofNullable(map).ifPresent(MDC::setContextMap);
                    return contentDoToVO(e, loginUser.getId(), appType, languageCode);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        fillSkuInfo(pageVo, mallCountryCode);
        return pageVo;
    }

    @Override
    public CommunityInteractNewMsgResponseDto interactNewMsg(ZnsUserEntity loginUser, String languageCode) {
        var communityInteractNewMsgResponseDto = new CommunityInteractNewMsgResponseDto();
        communityInteractNewMsgResponseDto.setRedDotFlag(Boolean.FALSE);
        CommunityInteractQuery communityInteractQuery = new CommunityInteractQuery().
                setIsRead(YesNoStatus.NO.getCode())
                .setRecipientId(loginUser.getId());
        communityInteractQuery.setOrders(List.of(OrderItem.desc("gmt_create")));
        CommunityInteractDo communityInteractContent = communityInteractService.findByQuery(communityInteractQuery);
        if (Objects.nonNull(communityInteractContent)) {
            communityInteractNewMsgResponseDto.setRedDotFlag(Boolean.TRUE);
            communityInteractNewMsgResponseDto.setCommunityInteract(communityInteractConverter.toDto(communityInteractContent));
        }
        return communityInteractNewMsgResponseDto;
    }

    @Override
    public Boolean interactReadContent(CommunityInteractReadRequestDto requestDto, ZnsUserEntity loginUser) {
        if (Objects.nonNull(requestDto.getId())) {
            CommunityInteractDo interactServiceById = communityInteractService.findById(requestDto.getId());
            if (Objects.nonNull(interactServiceById)) {
                interactServiceById.setIsRead(YesNoStatus.YES.getCode());
                communityInteractService.update(interactServiceById);
            }
        } else {
            CommunityInteractQuery communityInteractQuery = new CommunityInteractQuery().
                    setIsRead(YesNoStatus.NO.getCode())
                    .setRecipientId(loginUser.getId());
            List<CommunityInteractDo> communityInteractContents = communityInteractService.findList(communityInteractQuery);
            communityInteractContents.forEach(communityInteractContent -> communityInteractContent.setIsRead(YesNoStatus.YES.getCode()));
            communityInteractService.batchUpdate(communityInteractContents);
        }
        return true;
    }

    @Override
    public CommunityInteractPageResponseDto interactPageList(PageQuery dto, ZnsUserEntity loginUser) {
        var communityInteractPageResponseDto = new CommunityInteractPageResponseDto();
        var communityInteractQuery = new CommunityInteractQuery().setIsRead(YesNoStatus.NO.getCode()).setRecipientId(loginUser.getId());
        communityInteractPageResponseDto.setUnReadNum(communityInteractService.findCount(communityInteractQuery));
        CommunityInteractPageQuery communityInteractPageQuery = new CommunityInteractPageQuery()
                .setRecipientId(loginUser.getId());
        communityInteractPageQuery.setPageSize(dto.getPageSize());
        communityInteractPageQuery.setPageNum(dto.getPageNum());
        communityInteractPageQuery.setOrders(List.of(OrderItem.desc("gmt_create")));
        communityInteractPageResponseDto.setPageDto(communityInteractConverter.toPageDto(communityInteractService.findPage(communityInteractPageQuery)));
        return communityInteractPageResponseDto;
    }
}
