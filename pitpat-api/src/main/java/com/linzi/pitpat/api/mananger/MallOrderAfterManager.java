package com.linzi.pitpat.api.mananger;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.biz.RefundRemarkBizService;
import com.linzi.pitpat.data.mallservice.converter.api.OrderRefundConverter;
import com.linzi.pitpat.data.mallservice.dto.api.request.OrderItemRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.OrderRefundRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderAfterApplyPageResponseDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderAfterSaleDetailResponseDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderRefundListResponseDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.RefundRemarkResponseDto;
import com.linzi.pitpat.data.mallservice.enums.OrderRefundConstant;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderRefundPageQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderRefundQuery;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsSkuLanguageVo;
import com.linzi.pitpat.data.mallservice.service.GoodsSkuI18nService;
import com.linzi.pitpat.data.mallservice.service.OrderRefundService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.mallservice.util.SnowflakeOrderNoGenerator;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/12 10:39
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MallOrderAfterManager {
    private final OrderRefundService orderRefundService;
    private final ZnsOrderService orderService;
    private final ZnsOrderItemService orderItemService;
    private final GoodsSkuI18nService goodsSkuI18nService;
    private final RefundRemarkBizService refundRemarkBizService;
    private final MallOrderBizService mallOrderBizService;
    private final OrderRefundConverter orderRefundConverter;
    private final RedissonClient redissonClient ;

    /**
     * 售后列表
     *
     * @param query
     * @param userId
     * @param languageCode
     * @return
     */
    public Page<OrderRefundListResponseDto> pageList(PageQuery query, Long userId, String languageCode) {
        OrderRefundPageQuery pageQuery = orderRefundConverter.toPageQuery(query);
        pageQuery.setUserId(userId).setIsUserDelete(0);
        pageQuery.setRefundTypeList(Lists.newArrayList(OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT_GOOD.getCode(), OrderRefundConstant.REFUND_TYPE_ENUM.EXCHANGE_GOOD.getCode()));
        pageQuery.setOrders(List.of(OrderItem.desc("id")));
        Page page = orderRefundService.findPage(pageQuery);
        List<OrderRefund> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        List<Long> orderIds = records.stream().map(OrderRefund::getOrderId).toList();
        List<ZnsOrderEntity> orderEntities = orderService.findByOrderIds(orderIds);
        Map<Long, ZnsOrderEntity> orderMap = orderEntities.stream().collect(Collectors.toMap(ZnsOrderEntity::getId, v -> v));

        List<OrderRefundListResponseDto> collect = records.stream().map(r -> {
            OrderRefundListResponseDto orderRefundListResponseDto = orderRefundConverter.toDto(r);
            ZnsOrderItemEntity znsOrderItemEntity = orderItemService.selectOrderItemById(r.getOrderItemId());
            if (Objects.isNull(znsOrderItemEntity)) {
                return orderRefundListResponseDto;
            }
            List<GoodsSkuLanguageVo> goodsSkuLanguageVoList = goodsSkuI18nService.findLanguageList(List.of(znsOrderItemEntity.getSkuId()), languageCode);
            if (CollectionUtils.isEmpty(goodsSkuLanguageVoList)) {
                return orderRefundListResponseDto;
            }
            GoodsSkuLanguageVo goodsSkuLanguageVo = goodsSkuLanguageVoList.get(0);
            orderRefundListResponseDto.setThumbnail(goodsSkuLanguageVo.getPic());
            orderRefundListResponseDto.setPropertyValues(goodsSkuLanguageVo.getPropertyValues());
            orderRefundListResponseDto.setTitle(goodsSkuLanguageVo.getTitle());
            ZnsOrderEntity orderEntity = orderMap.get(r.getOrderId());
            if (Objects.nonNull(orderEntity)) {
                orderRefundListResponseDto.setCurrencySymbol(orderEntity.getCurrencySymbol());
            }
            return orderRefundListResponseDto;
        }).collect(Collectors.toList());
        page.setRecords(collect);

        return page;
    }

    /**
     * 售后详情
     *
     * @param id
     * @param languageCode
     * @return
     */
    public OrderAfterSaleDetailResponseDto detail(Long id, String languageCode) {
        OrderRefund orderRefund = orderRefundService.selectOrderRefundById(id);
        ZnsOrderItemEntity znsOrderItemEntity = orderItemService.selectOrderItemById(orderRefund.getOrderItemId());
        GoodsSkuLanguageVo goodsSkuLanguageVo = goodsSkuI18nService.findLanguageOne(znsOrderItemEntity.getSkuId(), languageCode);
        OrderAfterSaleDetailResponseDto orderAfterSaleDetailResponseDto = orderRefundConverter.toDetailDto(orderRefund);
        if (Objects.nonNull(goodsSkuLanguageVo)) {
            orderAfterSaleDetailResponseDto.setThumbnail(goodsSkuLanguageVo.getPic());
            orderAfterSaleDetailResponseDto.setTitle(goodsSkuLanguageVo.getTitle());
            orderAfterSaleDetailResponseDto.setPropertyValues(goodsSkuLanguageVo.getPropertyValues());
        }
        orderAfterSaleDetailResponseDto.setGoodsAmount(orderRefund.getApplyAmount().subtract(orderRefund.getPostageAmount()).subtract(orderRefund.getTaxAmount()).add(orderRefund.getCouponAmount()).add(orderRefund.getAllowanceAmount()));
        ZnsOrderEntity orderEntity = orderService.findById(orderRefund.getOrderId());
        orderAfterSaleDetailResponseDto.setCurrencySymbol(orderEntity.getCurrencySymbol());
        return orderAfterSaleDetailResponseDto;
    }

    /**
     * 删除售后申请
     *
     * @param id
     */
    public void delete(Long id) {
        OrderRefund orderRefund = orderRefundService.selectOrderRefundById(id);
        if (!OrderRefundConstant.STATUS_ENUM.canDeleteStatus().contains(orderRefund.getStatus())) {
            log.info("delete 失败，当前状态不可删除，id：{}，status:{}", id, orderRefund.getStatus());
            return;
        }
        OrderRefund update = new OrderRefund();
        update.setId(id);
        update.setIsUserDelete(1);
        orderRefundService.updateOrderRefundById(update);
    }

    /**
     * 申请售后
     *
     * @param request
     * @return
     */
    public Long apply(OrderRefundRequestDto request) {
        ZnsOrderItemEntity orderItem = orderItemService.selectOrderItemById(request.getOrderItemId());
        ZnsOrderEntity order = orderService.findById(orderItem.getOrderId());
        RLock lock = redissonClient.getLock(RedisConstants.APPLY_REFUND_ITEM_ID_KEY + request.getOrderItemId());
        AtomicReference<Long> orderRefundId = new AtomicReference<>(0L);
        LockHolder.tryLock(lock, 5, 60, () -> {
            //已退款数量
            Integer refundCount = 0;
            List<OrderRefund> orderRefunds = orderRefundService.findList(new OrderRefundQuery().setOrderItemId(request.getOrderItemId()).setStatusList(OrderRefundConstant.STATUS_ENUM.afterStatus()));
            if (!CollectionUtils.isEmpty(orderRefunds)) {
                for (OrderRefund orderRefund : orderRefunds) {
                    refundCount = refundCount + orderRefund.getCount();
                }
            }
            //有可用的退款商品，提交退款
            if (orderItem.getCount() - refundCount > 0) {
                order.setAfterSaleStatus(1);
                orderService.update(order);
                OrderRefund orderRefund = orderRefundConverter.toDo(request);
                String countryCode = StringUtils.hasText(order.getCountryCode()) ? order.getCountryCode() : I18nConstant.CountryCodeEnum.US.code;
                orderRefund.setRefundNo(SnowflakeOrderNoGenerator.buildOrderNo("TK", countryCode));
                orderRefund.setOrderNo(order.getOrderNo());
                orderRefund.setOrderId(orderItem.getOrderId());
                orderRefund.setUserId(orderItem.getUserId());
                orderRefund.setCountryCode(order.getCountryCode());
                orderRefundService.insertOrderRefund(orderRefund);
                //用户申请退款，钉钉通知
                mallOrderBizService.orderRefundMsg(orderRefund.getId());
                orderRefundId.set(orderRefund.getId());
            }
        });
        return orderRefundId.get();
    }

    /**
     * 取消售后申请
     *
     * @param id
     */
    public void cancel(Long id) {
        OrderRefund orderRefund = orderRefundService.selectOrderRefundById(id);
        if (!OrderRefundConstant.STATUS_ENUM.APPLY_REFUND.getCode().equals(orderRefund.getStatus())) {
            log.info("cancel 失败，当前状态不可取消，id：{}，status:{}", id, orderRefund.getStatus());
            return;
        }
        OrderRefund update = new OrderRefund();
        update.setId(id);
        update.setStatus(OrderRefundConstant.STATUS_ENUM.CANCEL.getCode());
        update.setGmtClose(ZonedDateTime.now());
        orderRefundService.updateOrderRefundById(update);
    }

    /**
     * 售后申请页
     *
     * @param request
     * @param languageCode
     * @return
     */
    public OrderAfterApplyPageResponseDto applyPage(OrderItemRequestDto request, String languageCode) {
        OrderAfterApplyPageResponseDto response = new OrderAfterApplyPageResponseDto();
        List<RefundRemarkResponseDto> refundRemarkResponseDtos = refundRemarkBizService.remarkList(languageCode);
        response.setRefundRemarkList(refundRemarkResponseDtos);
        ZnsOrderItemEntity orderItem = orderItemService.selectOrderItemById(request.getOrderItemId());
        response.setGoodsAmount(orderItem.getSalePrice());
        BigDecimal count = new BigDecimal(orderItem.getCount());
        response.setTaxAmount(orderItem.getTaxAmount().divide(count, 2, RoundingMode.DOWN));
        response.setPostageAmount(orderItem.getPostageAmount().divide(count, 2, RoundingMode.DOWN));
        response.setSkuCouponAmount(orderItem.getSkuCouponAmount().divide(count, 2, RoundingMode.DOWN));
        BigDecimal totalGoodsAmount = orderItem.getSalePrice().multiply(count);
        BigDecimal totalTaxAmount = orderItem.getTaxAmount();
        BigDecimal totalPostageAmount = orderItem.getPostageAmount();
        Integer refundCount = 0;

        List<OrderRefund> orderRefunds = orderRefundService.findList(new OrderRefundQuery().setOrderItemId(request.getOrderItemId()).setStatusList(OrderRefundConstant.STATUS_ENUM.afterStatus()));
        if (!CollectionUtils.isEmpty(orderRefunds)) {
            for (OrderRefund orderRefund : orderRefunds) {
                BigDecimal goodsAmount = orderRefund.getApplyAmount().subtract(orderRefund.getTaxAmount()).subtract(orderRefund.getPostageAmount()).add(orderRefund.getCouponAmount());
                totalGoodsAmount = totalGoodsAmount.subtract(goodsAmount);
                totalTaxAmount = totalTaxAmount.subtract(orderRefund.getTaxAmount());
                totalPostageAmount = totalPostageAmount.subtract(orderRefund.getPostageAmount());
                refundCount = refundCount + orderRefund.getCount();
            }
        }
        response.setAllowanceAmount(orderItem.getAllowanceAmount());
        if (orderItem.getAllowanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal allowanceSingle = orderItem.getAllowanceAmount().divide(new BigDecimal(orderItem.getCount()), 1, RoundingMode.HALF_UP);
            //全部退完
            if (Objects.equals(orderItem.getCount(), refundCount)) {
                totalGoodsAmount = totalGoodsAmount.subtract(orderItem.getAllowanceAmount());
                response.setAllowanceAmount(BigDecimal.ZERO);
            } else {
                //部分退款
                totalGoodsAmount = totalGoodsAmount.subtract(allowanceSingle.multiply(new BigDecimal(refundCount)));
                response.setAllowanceAmount(orderItem.getAllowanceAmount().subtract(allowanceSingle.multiply(new BigDecimal(refundCount))));
            }
            response.setAllowanceAmountAverage(allowanceSingle);

        }
        //BigDecimal maxApplyGoodsAmount = totalGoodsAmount.subtract(totalSkuCouponAmount);
        response.setMaxApplyGoodsAmount(totalGoodsAmount).setMaxApplyTaxAmount(totalTaxAmount).setMaxApplyPostageAmount(totalPostageAmount)
                .setRefundCount(refundCount).setMaxRefundCount(orderItem.getCount() - refundCount);
        ZnsOrderEntity orderEntity = orderService.findById(orderItem.getOrderId());
        response.setCurrencySymbol(orderEntity.getCurrencySymbol());
        return response;
    }
}
