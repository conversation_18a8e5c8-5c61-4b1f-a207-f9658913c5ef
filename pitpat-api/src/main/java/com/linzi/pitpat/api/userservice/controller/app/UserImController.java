package com.linzi.pitpat.api.userservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.dto.request.ImSendMsgRequest;
import com.linzi.pitpat.data.messageservice.model.vo.HideImVo;
import com.linzi.pitpat.data.messageservice.model.vo.ImKeyListVo;
import com.linzi.pitpat.data.messageservice.service.ImReceiveUserCallbackRecordLogService;
import com.linzi.pitpat.data.messageservice.service.ImSendRecordLogService;
import com.linzi.pitpat.data.resp.CallbackAfterSendMsgResp;
import com.linzi.pitpat.data.resp.ImageInfoArray;
import com.linzi.pitpat.data.resp.MsgConfigVo;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.vo.CustomerServiceInfo;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.UserImOnlineLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * im接口
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/app/im", "/h5/im"})
@Slf4j
public class UserImController extends BaseAppController {
    @Resource
    private TencentImUtil tencentImUtil;
    @Resource
    private ZnsUserService userService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ImSendRecordLogService imSendRecordLogService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private UserImOnlineLogService userImOnlineLogService;

    @Autowired
    private ZnsUserEquipmentService znsUserEquipmentService;

    @Autowired
    private ZnsTreadmillService znsTreadmillService;
    @Resource
    private ZnsUserAddService userAddService;

    /**
     * 获取腾讯云用户签名
     *
     * @return
     */
    @RequestMapping("/getUserSig")
    public Result getUserSig() {
        ZnsUserEntity loginUser = getLoginUser();
        Map<String, Object> data = new HashMap<>();
        data.put("userSig", tencentImUtil.getTxCloudUserSig(loginUser.getId().toString(), false));
        return CommonResult.success(data);
    }

    /**
     * 消息发送
     *
     * @param request
     * @return
     */
    @PostMapping("/sendMsg")
    public Result sendMsg(@RequestBody ImSendMsgRequest request) {
        tencentImUtil.sendMsg(request.getSyncOtherMachine(), request.getFromUserId(), request.getToUserId(), request.getMsgType(), request.getMsgContent());
        return CommonResult.success();
    }

    /**
     * 设置头像
     *
     * @return
     */
    @RequestMapping("/portraitSet")
    public Result portraitSet() {
        ZnsUserEntity loginUser = getLoginUser();
        tencentImUtil.portraitSet(loginUser.getId().toString(), loginUser.getHeadPortrait(), loginUser.getFirstName(), loginUser.getGender());
        return CommonResult.success();
    }


    /**
     * 获取头像
     *
     * @param map
     * @return
     */
    @PostMapping("/portraitGet")
    public Result portraitGet(@RequestBody Map<String, Object> map) {
        String userIds = MapUtils.getString(map, "userIds");
        List<String> strings = StringUtil.splitToList(userIds, ",");
        String res = tencentImUtil.portraitGet(strings);
        return CommonResult.success(res);
    }

    /**
     * 回调接口
     *
     * @return
     */
    @RequestMapping("/callback")
    public Map<String, Object> callback(@RequestBody Map<String, Object> params) {
        log.info("腾讯IM onlineStatus回调参数：" + params);
        Map<String, Object> res = new HashMap<>();
        res.put("ActionStatus", "OK");
        res.put("ErrorCode", 0);
        res.put("ErrorInfo", "");

        Object callbackCommand = params.get("CallbackCommand");
        if (Objects.isNull(callbackCommand)) {
            res.put("ActionStatus", "FAIL");
            res.put("ErrorCode", 1);
            res.put("ErrorInfo", I18nMsgUtils.getMessage("im.callback.command"));
            return res;
        }
        String callbackCommandStr = (String) callbackCommand;

        //在线状态回调
        if ("State.StateChange".equals(callbackCommandStr)) {
            onlineStatus(params, res);
        } else if ("C2C.CallbackAfterMsgReport".equals(callbackCommandStr)) {
            //消息已读回调
            msgRead(params, res);
        } else if ("C2C.CallbackAfterSendMsg".equals(callbackCommandStr)) {      // 腾讯文档 ： https://cloud.tencent.com/document/product/269/2716
            callbackAfterSendMsg(params);
        }
        return res;
    }

    /**
     * 隐藏im消息列表
     *
     * @return
     */
    @PostMapping("/hideImList")
    public Result<HideImVo> hideImList() {
        ZnsUserEntity user = getLoginUser();
        HideImVo vo = new HideImVo();
        List<ImKeyListVo> imKeyListVos = imSendRecordLogService.selectByUserIdAndShow(user.getId(), 0);
        vo.setImKeyListVos(imKeyListVos);
        String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.IM_HIDE_CONFIG_LIST.getCode());
        if (StringUtils.hasText(config)) {
            vo.setMessageStrList(StringUtil.splitToList(config, ","));
        }
        return CommonResult.success(vo);
    }

    public void callbackAfterSendMsg(Map<String, Object> params) {
        try {
            CallbackAfterSendMsgResp resp = JsonUtil.readValue(params, CallbackAfterSendMsgResp.class);
            if (!StringUtils.hasText(resp.getTo_Account())) {
                return;
            }
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.callbackAfterSendMsg_config.getCode());
            MsgConfigVo vo = JsonUtil.readValue(sysConfig.getConfigValue(), MsgConfigVo.class);
            CustomerServiceInfo co = null;
            for (CustomerServiceInfo customerServiceInfo : vo.getList()) {
                if (customerServiceInfo.getToAccount().equals(resp.getTo_Account())) {
                    co = customerServiceInfo;
                    break;
                }
            }
            ZnsUserEntity znsUserEntity = userService.findById(MapUtil.getLong(resp.getFrom_Account()));
            if (znsUserEntity == null) {
                log.info(" user 为空 " + resp.getFrom_Account());
                return;
            }
            ZnsUserAddEntity userAdd = userAddService.getUserAdd(znsUserEntity.getId());
            if (co != null) {
                String nickName = co.getName();
                String moible = co.getMobiles();

                sendDdingDing(resp, znsUserEntity, nickName, moible, vo.getToken(), userAdd);
            }
            saveImCallBackInfo(resp, znsUserEntity);
        } catch (Exception e) {
            log.error("异常callbackAfterSendMsg", e);
        }
    }

    @Autowired
    private ImReceiveUserCallbackRecordLogService imReceiveUserCallbackRecordLogService;

    private void saveImCallBackInfo(CallbackAfterSendMsgResp resp, ZnsUserEntity znsUserEntity) {
        imReceiveUserCallbackRecordLogService.saveImCallBackInfo(resp, znsUserEntity.getId());
    }


    public void sendDdingDing(CallbackAfterSendMsgResp resp, ZnsUserEntity znsUserEntity, String nickName, String moible, String token, ZnsUserAddEntity userAdd) {
        if (resp.getMsgBody() == null || resp.getMsgBody().size() == 0) {
            log.info("消息体为空 ");
            return;
        }
        String text = null;
        try {
            text = resp.getMsgBody().get(0).getMsgContent().getText();
        } catch (Exception e) {
            log.error("钉钉消息获取文本异常", e);
        }

        String ImageInfoItem = null;
        try {
            List<ImageInfoArray> ImageInfoArray = resp.getMsgBody().get(0).getMsgContent().getImageInfoArray();
            if (!CollectionUtils.isEmpty(ImageInfoArray)) {
                ImageInfoItem = ImageInfoArray.get(0).getURL();
            }
        } catch (Exception e) {
            log.error("钉钉消息获取文本异常", e);
        }
        String VideoUrl = null;
        try {
            VideoUrl = resp.getMsgBody().get(0).getMsgContent().getVideoUrl();
        } catch (Exception e) {
            log.error("钉钉消息获取文本异常", e);
        }
        StringBuilder sb = new StringBuilder();
        if (StringUtils.hasText(text)) {
            sb.append(text).append("\n");
        }
        if (StringUtils.hasText(ImageInfoItem)) {
            sb.append(ImageInfoItem).append("\n");
        }
        if (StringUtils.hasText(VideoUrl)) {
            sb.append(VideoUrl).append("\n");
        }

        String contentSb = sb.toString();


        StringBuilder sbBatchno = new StringBuilder();
        if (StringUtils.hasText(contentSb) && StringUtil.containsList(contentSb, "bracelet", "wristband", "watch")) {
            List<ZnsUserEquipmentEntity> znsUserEquipmentEntities = znsUserEquipmentService.selectByUserId(znsUserEntity.getId());
            sbBatchno.append("batch no：").append("\n");
            for (ZnsUserEquipmentEntity znsUserEquipmentEntity : znsUserEquipmentEntities) {
                ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.findById(znsUserEquipmentEntity.getEquipmentId());
                if (znsTreadmillEntity != null) {
                    sbBatchno.append(znsTreadmillEntity.getBatchNumber()).append("\n");
                }
            }
        }

        String userName = znsUserEntity.getFirstName();
        String email = znsUserEntity.getEmailAddressEn();
        String followUserName = Objects.isNull(userAdd) ? "" : userAdd.getFollowUserName();
        String followRobotName = Objects.isNull(userAdd) ? "" : userAdd.getFollowRobotName();
        String content = "【Station message】：\n"
                + "User name：" + userName + "\n"
                + "Email：" + email + "\n"
                + "Content：\n" + contentSb +
                "Official account nickname：official customer service\n" + sbBatchno.toString() +
                "Follow up person：" + followUserName + "\n"
                + "Follow up robot：" + followRobotName + "\n";
        DingTalkUtils.sendMsg(DingTalkRequestDto.of(token, null, content, moible));// https://oapi.dingtalk.com/robot/send?access_token=dc46825406f43bc0d3186ad9b768b4b8afe8df053de41ff23de5086523facef0

    }

    /**
     * 退出账号登录
     *
     * @param userId
     * @return
     */
    @GetMapping("/logoff")
    public String logoff(Long userId) {
        tencentImUtil.accountDelete(Arrays.asList(String.valueOf(userId)));
        ZnsUserEntity user = userService.findById(userId);
        if (Objects.nonNull(user)) {
            //退出登录
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            redisUtil.delete(key);
        }
        //删除聊天记录
        return CommonResult.success().toString();
    }

    private void onlineStatus(Map<String, Object> params, Map<String, Object> res) {
        Object info = params.get("Info");
        if (Objects.isNull(info)) {
            res.put("ActionStatus", "FAIL");
            res.put("ErrorCode", 1);
            res.put("ErrorInfo", "没有获取到用户上下线信息");
            return;
        }
        Map<String, Object> infoMap = (Map<String, Object>) info;
        Object To_Account = infoMap.get("To_Account");
        Object Action = infoMap.get("Action");
        Long eventTime = MapUtils.getLong(infoMap, "EventTime");
        if (Objects.isNull(eventTime)) {
            eventTime = MapUtils.getLong(params, "EventTime");
        }
        Boolean result = userService.updateOnlineStatus((String) To_Account, (String) Action);
        userImOnlineLogService.addImOnlineLog(infoMap, eventTime);
        if (!result) {
            log.info("用户在线状态修改失败，用户：" + To_Account);
            res.put("ActionStatus", "FAIL");
            res.put("ErrorCode", 1);
            res.put("ErrorInfo", "用户在线状态修改失败");
        }
    }

    /**
     * 消息已读处理
     *
     * @param params
     * @param res
     */
    private void msgRead(Map<String, Object> params, Map<String, Object> res) {
        String report_Account = MapUtils.getString(params, "Report_Account");
        String peer_Account = MapUtils.getString(params, "Peer_Account");
        Long lastReadTime = MapUtils.getLong(params, "LastReadTime") * 1000;
        Integer unreadMsgNum = MapUtils.getInteger(params, "UnreadMsgNum");
        //已读消息处理配置特殊账号
        String config = sysConfigService.selectConfigByKey("pitpat.im.system");
        if (StringUtil.isEmpty(config)) {
            log.info("msgRead 处理结束，无配置特殊账号");
            return;
        }
        List<String> userIds = StringUtil.splitToList(config, ",");
        if (!userIds.contains(peer_Account)) {
            log.info("msgRead 处理结束，会话对端 UserID: " + peer_Account + " 不属于特殊账号");
            return;
        }
        try {
            imSendRecordLogService.msgRead(report_Account, peer_Account, lastReadTime, unreadMsgNum, peer_Account);
        } catch (Exception e) {
            res.put("ActionStatus", "FAIL");
            res.put("ErrorCode", 1);
            res.put("ErrorInfo", e.getMessage());
        }
    }
}
