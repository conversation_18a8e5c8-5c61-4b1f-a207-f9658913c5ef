package com.linzi.pitpat.api.userservice.dto.request;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 邮箱绑定请求参数
 */
@Data
public class UserFeedbackRequestDto {

    //反馈类型: 0：问题，1：建议
    private Integer feedbackType;

    //问题类型: 0：数据同步问题，1：其他' 2:比赛成绩申诉
    private Integer problemType;

    //问题数据详情ID
    private Long problemDataId;

    //问题图片集合
    private List<String> problemUrl;
    //问题数据详情ID集合
    private List<Long> problemDataIds;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 国家
     */
    private String country;
    /**
     * 问题描述
     */
    private String problemDesc;
    /**
     * first name
     */
    private String firstName;
    /**
     * last name
     */
    private String lastName;
    /**
     * 地址
     */
    private String address;
    /**
     * 城市
     */
    private String city;
    /**
     * 国家国家;州;邦
     */
    private String state;
    /**
     * 邮政编码
     */
    private String zipCode;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 电子邮件地址
     */
    private String emailAddress;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 型号
     */
    private String modelNumber;
    /**
     * 购买地点
     */
    private String purchaseLocation;
    /**
     * 购买日期
     */
    private ZonedDateTime purchaseDate;
}
