package com.linzi.pitpat.api.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.bussiness.OperationalActivityManager;
import com.linzi.pitpat.data.entity.operational.OperationalActivity;
import com.linzi.pitpat.data.entity.operational.OperationalActivityUser;
import com.linzi.pitpat.data.entity.po.RotationArea;
import com.linzi.pitpat.data.request.activity.OperationalActivityEnrollPo;
import com.linzi.pitpat.data.request.activity.OperationalActivityInfoPo;
import com.linzi.pitpat.data.request.activity.UserTaskIdReq;
import com.linzi.pitpat.data.service.operational.OperationalActivityService;
import com.linzi.pitpat.data.service.operational.OperationalActivityUserService;
import com.linzi.pitpat.data.systemservice.model.query.OperationalActivityReq;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.operational.OperationalActivityInfoVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 运营活动
 *
 * <AUTHOR>
 * @date 2023/3/2 14:56
 */
@RestController
@Slf4j
@RequestMapping({"/app/operational/activity", "/h5/operational/activity"})
public class OperationalActivityController extends BaseAppController {
    @Resource
    private OperationalActivityService operationalActivityService;
    @Resource
    private OperationalActivityUserService operationalActivityUserService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private AppUpgradeService appUpgradeService;
    @Resource
    protected ZnsUserService userService;
    @Resource
    private OperationalActivityManager operationalActivityManager;

    /**
     * 运营活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/info")
    public Result<OperationalActivityInfoVo> info(@RequestBody OperationalActivityInfoPo request) {
        if (!StringUtils.hasText(request.getActivityNo())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        ZnsUserEntity loginUser = getLoginUser();
        Long userId = 0L;
        if (Objects.nonNull(loginUser)) {
            userId = loginUser.getId();
        }

        OperationalActivityInfoVo vo = new OperationalActivityInfoVo();
        boolean isNumber = NumberUtils.isNumber(request.getActivityNo());

        if (!isNumber) {
            OperationalActivity operationalActivity = operationalActivityService.selectByActivityNo(request.getActivityNo());
            if (Objects.isNull(operationalActivity)) {
                return CommonResult.fail("This event was over");
            }
            BeanUtils.copyProperties(operationalActivity, vo);

            //兼容历史数据
            if (operationalActivity.getOperationalActivityType() == 1) {
                if (Objects.nonNull(loginUser)) {
                    OperationalActivityUser operationalActivityUser = operationalActivityUserService.selectByUserIdAndOperationalId(operationalActivity.getId(), loginUser.getId());
                    if (Objects.isNull(operationalActivityUser)) {
                        vo.setUserState(0);
                    } else {
                        vo.setUserState(operationalActivityUser.getUserState());
                    }
                } else {
                    vo.setUserState(0);
                }
            } else {
                //配置活动处理
                operationalActivityService.packageRunActivity(vo, operationalActivity, userId);
            }
        } else {
            //配置活动处理
            OperationalActivity operationalActivity = new OperationalActivity();
            operationalActivity.setOperationalActivityType(2);
            operationalActivity.setRunActivityId(Long.valueOf(request.getActivityNo()));
            BeanUtils.copyProperties(operationalActivity, vo);
            operationalActivityService.packageRunActivity(vo, operationalActivity, userId);
        }

        return CommonResult.success(vo);
    }

    /**
     * 报名
     *
     * @param request
     * @return
     */
    @PostMapping("/enroll")
    public Result enroll(@RequestBody OperationalActivityEnrollPo request) {
        if (!StringUtils.hasText(request.getActivityNo())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        ZnsUserEntity user = getLoginUser();
        Integer appVersion = getAppVersion();
        return operationalActivityManager.enrolling(user, request, appVersion);
    }

    /**
     * 检查是否可以执行任务
     *
     * @param req
     * @return
     */
    @PostMapping("/checkExecuteTask")
    public Result checkExecuteTask(@RequestBody UserTaskIdReq req) {
        ZonedDateTime now = ZonedDateTime.now();
        //查询任务
        RunActivityUserTask userTask = runActivityUserTaskService.selectRunActivityUserTaskById(req.getTaskId());
        if (Objects.isNull(userTask)) {
            log.warn("checkExecuteTask 任务不存在");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        }
        //查询活动
        ZnsRunActivityEntity activity = runActivityService.findById(userTask.getActivityId());
        //检查时间
        if (now.compareTo(activity.getActivityStartTime()) < 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.event.apply.time.notStart"));
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(userTask.getActivityId(), userTask.getUserId());
        if (Objects.nonNull(activityUser.getUserActivityEndTime()) && now.compareTo(activityUser.getUserActivityEndTime()) > 0) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.event.apply.time.ended"));
        }
        if (userTask.getIsUnlock() == 0) {
            log.warn("checkExecuteTask 任务未解锁");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.event.condition.locked"));
        }

        Integer userScore = userService.getAllUserScore(userTask.getUserId());
        //若报名费包含积分，则判断用户积分是否足够
        if (!checkUserActivityEntryScore(activity, userScore)) {
            return CommonResult.fail(ActivityError.ACTIVITY_LACK_SCORE.getCode(), I18nMsgUtils.getMessage("activity.racePoint.insufficient"));
        }
        return CommonResult.success();
    }

    /**
     * 根据 showLocation 获取banner
     *
     * @param searchReq OperationalActivitySearchReq
     * @return Result<List < RotationArea>>
     */
    @PostMapping("/searchByShowLocation")
    public Result<List<RotationArea>> getBannerShowLocation(@RequestBody OperationalActivityReq searchReq) {
        boolean checkUser = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        List<RotationArea> rotationAreas = operationalActivityService.selectHomeMapList(getLoginUserOrDefaultUser(), getAppVersion(), checkUser, getAppType(), searchReq.getShowLocation());
        return CommonResult.success(rotationAreas);
    }

    /**
     * 检查用户积分是否大于活动报名所需积分
     *
     * @param activityEntity 活动
     * @param userScore      用户积分
     * @return true 用户积分足够， false 用户积分不足
     */
    private boolean checkUserActivityEntryScore(ZnsRunActivityEntity activityEntity, Integer userScore) {
        boolean containsEntryScore = Arrays.asList(4, 5).contains(activityEntity.getBonusRuleType());
        boolean legalEntryScore = Objects.nonNull(activityEntity.getActivityEntryScore()) && activityEntity.getActivityEntryScore().compareTo(BigDecimal.ZERO) > 0;
        if (containsEntryScore && legalEntryScore) {
            return BigDecimal.valueOf(userScore).compareTo(activityEntity.getActivityEntryScore()) >= 0;
        }
        return true;
    }

}
