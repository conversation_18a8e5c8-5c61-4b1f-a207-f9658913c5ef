package com.linzi.pitpat.api.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.linzi.pitpat.DingTalkService;
import com.linzi.pitpat.api.activityservice.manager.UserRankedLevelManager;
import com.linzi.pitpat.api.dto.UserTestDto;
import com.linzi.pitpat.api.dto.req.SendClothesReq;
import com.linzi.pitpat.api.dto.request.AddUserExpRequest;
import com.linzi.pitpat.api.equipment.controller.device.DeviceDataController;
import com.linzi.pitpat.api.mananger.MallOrderManager;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.EggActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.UserRankChangePushBizService;
import com.linzi.pitpat.data.activityservice.biz.award.ActivityUserAwardCalculator;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.ActivityStageBusiness;
import com.linzi.pitpat.data.activityservice.manager.ActivityUserManager;
import com.linzi.pitpat.data.activityservice.manager.FreeActivityManager;
import com.linzi.pitpat.data.activityservice.manager.PropRankActivityManager;
import com.linzi.pitpat.data.activityservice.manager.RunEndActivityManager;
import com.linzi.pitpat.data.activityservice.mapper.MindUserMatchDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityUserDao;
import com.linzi.pitpat.data.activityservice.model.dto.MilepostWearAwardDto;
import com.linzi.pitpat.data.activityservice.model.dto.OfficialCumulativeRunDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityRunRankTempDo;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedMatchEntity;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataDetailsCheat;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunDataEveryEverySecond;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsMinuteEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunAwardQuery;
import com.linzi.pitpat.data.activityservice.model.vo.FriendPkAward;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunEndActivityVo;
import com.linzi.pitpat.data.activityservice.quartz.ActivityStageTask;
import com.linzi.pitpat.data.activityservice.quartz.ProActivityTempRankCalTask;
import com.linzi.pitpat.data.activityservice.quartz.RoomCreateTask;
import com.linzi.pitpat.data.activityservice.quartz.RoomDismissTask;
import com.linzi.pitpat.data.activityservice.quartz.RoomNpcCreateAndDismissTask;
import com.linzi.pitpat.data.activityservice.quartz.RoomOrderStartTask;
import com.linzi.pitpat.data.activityservice.quartz.UserRewardAccountExpiredTask;
import com.linzi.pitpat.data.activityservice.service.ActivityRunRankTempService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.GameplayAwardStageConfigService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedMatchService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataEveryEverySecondService;
import com.linzi.pitpat.data.activityservice.service.UserRunOptimalRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.BaseOfficialActivityStrategy;
import com.linzi.pitpat.data.activityservice.strategy.ChallengeRunActivityStrategy;
import com.linzi.pitpat.data.awardservice.biz.UserWearsBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.bussiness.BatchEncryptTask;
import com.linzi.pitpat.data.bussiness.UserOnlineBussiness;
import com.linzi.pitpat.data.clubservice.autoconfigure.OpenAiProperties;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberRoleEnum;
import com.linzi.pitpat.data.clubservice.model.entity.ClubMember;
import com.linzi.pitpat.data.clubservice.model.query.ClubMemberQuery;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.constants.MongodbConstant;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.dao.activity.EveryDayDao;
import com.linzi.pitpat.data.entity.activity.EveryDay;
import com.linzi.pitpat.data.entity.dto.DelayDto;
import com.linzi.pitpat.data.entity.dto.RobotFinishDto;
import com.linzi.pitpat.data.entity.dto.RobotUsedInfo158DTO;
import com.linzi.pitpat.data.entity.dto.RunningData;
import com.linzi.pitpat.data.entity.dto.UserActiveCouponQueueDto;
import com.linzi.pitpat.data.entity.vo.SocketRoomUserVo;
import com.linzi.pitpat.data.enums.ActivityNotificationEnum;
import com.linzi.pitpat.data.enums.EmailStatusEnum;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.SocketEventEnums;
import com.linzi.pitpat.data.enums.VipStatusUserEnum;
import com.linzi.pitpat.data.equipmentservice.manager.api.UserEquipmentShareManager;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.RainmakerService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.dto.request.OrderCancelReasonReq;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.manager.console.GoodsConsoleManager;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.query.GoodsQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuQuery;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.messageservice.mapper.ImSendRecordLogDao;
import com.linzi.pitpat.data.messageservice.mapper.MessageTaskMsgDao;
import com.linzi.pitpat.data.messageservice.model.entity.ImSendRecordLog;
import com.linzi.pitpat.data.messageservice.model.entity.MessageTaskMsg;
import com.linzi.pitpat.data.messageservice.model.query.ImSendRecordQuery;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.model.vo.MessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.messageservice.service.ImSendRecordLogService;
import com.linzi.pitpat.data.messageservice.service.MessageTaskService;
import com.linzi.pitpat.data.messageservice.util.FirebaseUtils;
import com.linzi.pitpat.data.paymentservice.biz.PayPalPaymentBizService;
import com.linzi.pitpat.data.paymentservice.biz.client.PayPalExecutor;
import com.linzi.pitpat.data.paymentservice.biz.client.paypalmodel.SubscriptionsTransactionRecord;
import com.linzi.pitpat.data.paymentservice.script.PayPalDataMigrationScript;
import com.linzi.pitpat.data.quartz.SpeedCalcV2Task;
import com.linzi.pitpat.data.request.BaseReq;
import com.linzi.pitpat.data.request.CalcPlacementScoreReqDto;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.request.TestDto;
import com.linzi.pitpat.data.robotservice.manager.RobotRunPlanManager;
import com.linzi.pitpat.data.robotservice.model.entity.RobotRunPlan;
import com.linzi.pitpat.data.robotservice.model.entity.RotPic;
import com.linzi.pitpat.data.robotservice.query.RotPicQuery;
import com.linzi.pitpat.data.robotservice.service.RobotRunPlanService;
import com.linzi.pitpat.data.robotservice.service.RotPicService;
import com.linzi.pitpat.data.service.activity.MonthRunDataService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.third.aliyun.imageseg.ImageSegService;
import com.linzi.pitpat.data.turbolink.facade.converter.TurbolinkAwardConverter;
import com.linzi.pitpat.data.turbolink.facade.dto.TurbolinkAwardCallbackDto;
import com.linzi.pitpat.data.turbolink.manager.TurbolinkCampaignUserManager;
import com.linzi.pitpat.data.turbolink.model.dto.app.TurbolinkSendAwardDto;
import com.linzi.pitpat.data.turbolink.quartz.TurbolinkTask;
import com.linzi.pitpat.data.userservice.biz.UserExpLevelBizService;
import com.linzi.pitpat.data.userservice.dto.request.UserEnReq;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserSendBagReq;
import com.linzi.pitpat.data.userservice.enums.ShopifyConstant;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.enums.UserGenderEnum;
import com.linzi.pitpat.data.userservice.manager.api.UserTaskManager;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserDao;
import com.linzi.pitpat.data.userservice.mapper.ZnsUserEquipmentDao;
import com.linzi.pitpat.data.userservice.model.entity.UserDetailDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUser;
import com.linzi.pitpat.data.userservice.model.query.UserQuery;
import com.linzi.pitpat.data.userservice.quartz.RainMakerBatchCreateTask;
import com.linzi.pitpat.data.userservice.quartz.UserYearTask;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import com.linzi.pitpat.data.userservice.service.UserDetailService;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserPushTokenService;
import com.linzi.pitpat.data.userservice.service.UserThirdOauthService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.impl.shopify.strategy.AbstractSycnStrategy;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyPitpatUploadRecordService;
import com.linzi.pitpat.data.userservice.service.shopify.ShopifyUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.util.PkFriendAwardUtil;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.FollowImMessageBo;
import com.linzi.pitpat.data.vo.runData.RunningLevelVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.framework.redis.util.RedisCache;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.trace.api.constants.RabbitQueueConstant;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.BuiltinExchangeType;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RBitSet;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.time.ZonedDateTime;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.linzi.pitpat.data.userservice.service.impl.shopify.strategy.AbstractSycnStrategy.SYCN_MAP;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/device/test", "/tst", "/test", "/app/test"})
@Slf4j
public class TestController extends DeviceDataController {

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ZnsUserService userService;
    @Resource
    private TencentImUtil tencentImUtil;
    @Resource
    private AppMessageService appMessageService;
    @Resource
    private RedisCache redisCache;
    @Autowired
    private ZnsRunActivityUserDao znsRunActivityUserDao;
    @Autowired
    private RainmakerService rainmakerService;

    @Autowired
    private MindUserMatchDao mindUserMatchDao;
    @Resource
    private ZnsUserFriendService userFriendService;

    @Value("${admin.server.gamepush}")
    private String gameDomain;

    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private ISysConfigService sysConfigService;
    @Autowired
    private ZnsUserDao znsUserDao;
    @Autowired
    private ShopifyPitpatUploadRecordService shopifyPitpatUploadRecordService;

    @Resource
    private UserRankedLevelManager userRankedLevelManager;

    @Resource
    private UserExpLevelBizService userExpLevelBizService;
    @Resource
    private SocketPushUtils socketPushUtils;
    @Resource
    private ActivityResultManager activityResultManager;
    @Resource
    private MainActivityService mainActivityService;

    @Resource
    private ZnsRunActivityUserService runActivityUserService;

    @Resource
    private ActivityUserScoreService activityUserScoreService;

    @Resource
    private ChallengeRunActivityStrategy challengeRunActivityStrategy;
    @Resource
    private ActivityUserManager activityUserManager;
    @Resource
    private UserWearsBizService userWearsBizService;
    @Autowired
    private RobotRunPlanService robotRunPlanService;
    @Autowired
    private MindUserMatchService mindUserMatchService;
    @Autowired
    private RobotRunPlanManager robotRunPlanManager;

    @Autowired
    private PropUserRankedMatchService propUserRankedMatchService;

    @Autowired
    private PropRankActivityManager rankedActivityManage;

    @Autowired
    private RoomCreateTask userRaceCreateTask;

    @Autowired
    private RotPicService rotPicService;

    @Autowired
    private RainMakerBatchCreateTask rainMakerBatchCreateTask;

    @Autowired
    private PayPalPaymentBizService payPalPaymentBizService;

    @Autowired
    private PayPalDataMigrationScript payPalDataMigrationScript;

    @Autowired
    private TurbolinkTask turbolinkTask;

    @Autowired
    private TurbolinkCampaignUserManager turbolinkCampaignUserManager;

    @Autowired
    private ActivityStageBusiness activityStageBusiness;

    @Autowired
    private ActivityStageTask activityStageTask;

    @Autowired
    private PayPalExecutor payPalExecutor;
    @Autowired
    private UserDetailService userDetailService;
    @Autowired
    private ZnsGoodsService goodsService;
    @Autowired
    private ZnsGoodsSkuService skuService;
    @Autowired
    private GoodsConsoleManager goodsConsoleManager;
    @Autowired
    private ZnsOrderService znsOrderService;
    @Autowired
    private MallOrderBizService mallOrderBizService;
    @Autowired
    private ScoreMallGoodsRelationService scoreMallGoodsRelationService;
    @Autowired
    private FreeActivityManager freeActivityManager;

    @GetMapping("/freeInit")
    public Result freeInit() {
        freeActivityManager.init();
        return CommonResult.success();

    }

    @GetMapping("/nextFree")
    public Result nextFree() {
        freeActivityManager.generateNextActivity();
        return CommonResult.success();

    }

    @GetMapping("/syncErpSku")
    public Result<List<Long>> syncErpSku() {
        List<Long> arrayList = new ArrayList<>();
        List<ZnsGoodsEntity> list = goodsService.findList(new GoodsQuery().setCountryCodeList(List.of(I18nConstant.CountryCodeEnum.DE.getCode(), I18nConstant.CountryCodeEnum.UK.getCode())));
        for (ZnsGoodsEntity znsGoodsEntity : list) {
            List<ZnsGoodsSkuEntity> skuList = skuService.findList(new GoodsSkuQuery().setGoodsId(znsGoodsEntity.getId()));
            for (ZnsGoodsSkuEntity znsGoodsSkuEntity : skuList) {
                goodsConsoleManager.syncErpSku(znsGoodsSkuEntity, znsGoodsEntity, 1);
                arrayList.add(znsGoodsSkuEntity.getId());
                log.info("同步商品sku：{}", znsGoodsSkuEntity.getId());
            }
        }
        return CommonResult.success(arrayList);

    }

    @Autowired
    private UserRankChangePushBizService userRankChangePushBizService;
    @Autowired
    private ActivityUserAwardCalculator activityUserAwardCalculator;

    @GetMapping("/hotTopic")
    public Result hotTopic(String key) {
        if (Objects.equals(key, "hello")) {
            throw new BizException(CommonError.BUSINESS_ERROR);
        }
        RScoredSortedSet<Long> weekSet = redissonClient.getScoredSortedSet(key);
        return CommonResult.success(weekSet);
    }

    @GetMapping("/subscriptionRecord")
    public Result subscriptionRecord(String pid) {
        SubscriptionsTransactionRecord subscriptionsInfo1 = payPalExecutor.subscriptionRecord(pid);
        System.out.println(subscriptionsInfo1);
        return CommonResult.success(subscriptionsInfo1);
    }

    @GetMapping("/activityStagePushProxy")
    public void reRankStage(ZonedDateTime now) {
        if (now == null) {
            now = ZonedDateTime.now();
        }
        log.info("当前时间：{}", now);
        activityStageTask.activityStagePushProxy(now);
    }

    @GetMapping("/reRankStage")
    public void reRankStage(Long activityId, Long stageId) {
        if (stageId == null) {
            activityStageBusiness.reRank(activityId);
        } else {
            activityStageBusiness.reRank(activityId, stageId, false);
        }
    }

    @GetMapping("/syncLocalCampaign")
    public void syncLocalCampaign() {
        turbolinkTask.syncLocalCampaign();
    }

    @GetMapping("/campaignEnd")
    public void campaignEnd() {
        turbolinkTask.campaignEnd();
    }

    @GetMapping("/removeBitmap")
    public void removeBitmap(Long offset) {
        RBitSet bitSet = redissonClient.getBitSet(RedisConstants.CANCEL_BATTLE_PASS);
        if (offset != null) {
            bitSet.set(offset, false);
        }
    }

    @Resource
    private EntryGameplayService entryGameplayService;
    @Resource
    private GameplayAwardStageConfigService gameplayAwardStageConfigService;

    @GetMapping("/breakingRecord")
    public void breakingRecord(Long mainActivityId) {
        MainActivity mainActivity = mainActivityService.findById(mainActivityId);
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(mainActivity.getPlayId());
        List<Integer> stageList = gameplayAwardStageConfigService.findStageList(entryGameplay.getGameplayId(), 1);
        activityResultManager.doSendRecordBreakingAwadrd(stageList, entryGameplay.getRankingBy(), mainActivity, mainActivity.getAwardSendStatus());
    }

    @GetMapping("/judgeInBitmap")
    public Result judgeInBitmap(Long offset) {
        RBitSet bitSet = redissonClient.getBitSet(RedisConstants.CANCEL_BATTLE_PASS);
        if (offset != null) {
            return CommonResult.success(bitSet.get(offset));
        }
        return CommonResult.success(Boolean.FALSE);
    }

    @GetMapping("/payPalDataMigrationScript")
    public void payPalDataMigrationScript(String subscriptionId) {
        payPalDataMigrationScript.run();
    }


    @GetMapping("/paypalRefund")
    public void paypalRefund(String subscriptionId) {
        payPalPaymentBizService.checkRefund(subscriptionId);
    }


    @GetMapping("/testUpdateMatchLineStateBySocket")
    public void testUpdateMatchLineStateBySocket(Long id) {

        PropUserRankedMatchEntity byId = propUserRankedMatchService.findById(id);
        rankedActivityManage.updateMatchLineStateBySocket(byId, "jo123b");

    }

    @GetMapping("/testOnline")
    public void testOnline(Long roomId) {
        while (true) {
            List<SocketRoomUserVo.UserVo> roomUserVoList = socketPushUtils.getOnLineRobot(roomId);
            for (SocketRoomUserVo.UserVo userVo : roomUserVoList) {
                log.info("+++++++++" + userVo.getEmail() + "-" + userVo.getUserId());
            }
            log.info("-----------");
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

    }

    @GetMapping("/getNewFriendPkAward")
    public Result<FriendPkAward> getNewFriendPkAward(Long activityId) {

        ZnsRunActivityEntity activity = runActivityService.findById(activityId);
        FriendPkAward newFriendPkAward = PkFriendAwardUtil.getNewFriendPkAward(activity);
        return CommonResult.success(newFriendPkAward);


    }

    @GetMapping("/handleRunActivityEnd")
    public void handleRunActivityEnd(Long activityId) {

        ZnsRunActivityEntity activity = runActivityService.findById(activityId);
        challengeRunActivityStrategy.handleRunActivityEnd(activity);


    }

    @RequestMapping("/testBestGradeExpirationReminder")
    public void testBestGradeExpirationReminder(String users) {

        List<Long> list = JsonUtil.readValue(users, new TypeReference<List<Long>>() {
        });

        ZonedDateTime day5 = DateUtil.addDays(DateUtil.startOfDate(ZonedDateTime.now()), -5);
        ZonedDateTime day4 = DateUtil.addDays(DateUtil.startOfDate(ZonedDateTime.now()), -4);

        List<Long> userIds = userRunDataDetailsService.selectBestGradeExpiration(Constants.target_1_Mile, Constants.target_1_Mile, day5, day4, 1);
        log.info("需要推送的用户名单为{}", userIds);

        for (Long userId : userIds) {
            if (list.contains(userId)) {
                reminderAndPush(userId, "bestGradeExpirationReminder" + ZonedDateTime.now().toInstant().toEpochMilli());
            }

        }


    }

    private void reminderAndPush(Long userId, String batchNo) {
        ZnsUserEntity user = userService.findById(userId);
        if (Objects.nonNull(user)) {
            String languageCode = user.getLanguageCode();
            MessageBo messageBo = new MessageBo();
            messageBo.setCollapseKey("pitpat");
            messageBo.setTitle(I18nMsgUtils.getLangMessage(languageCode, "bestGradeExpirationReminder.title"));
            messageBo.setContent(I18nMsgUtils.getLangMessage(languageCode, "bestGradeExpirationReminder.content"));
            messageBo.setJumpType("5");
            messageBo.setRouteType(1);
            messageBo.setRouteValue("lznative://main/offlinpkrecord");
            appMessageService.push(List.of(userId), messageBo, batchNo);
        }

    }

    @RequestMapping("/testOnlyPush")
    public void updateSeriesActivityGrade(String routeValue, Long userId) {

    }

    @RequestMapping("/updateSeriesActivityGrade")
    public void updateSeriesActivityGrade(Long activityId, Long userId) {

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userId);
        activityResultManager.updateSeriesActivityGrade(activityUser);
    }

    @RequestMapping("/getIntWithLock")
    public String getRandomIntWithLock(@RequestParam Long time) {

        String lockKey = "UserRankedLevelManagerTest:20";
        Random random = new Random();
        Supplier<Integer> supplier = () -> {
            try {
                TimeUnit.SECONDS.sleep(time);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return random.nextInt(10);
        };
        RLock lock = redissonClient.getLock(lockKey);

        Integer result = LockHolder.tryLock(lock, 0, 30, supplier::get);
        log.info("result={}", result);
        return "result " + result;
    }

    /**
     * 同步shopif用户到pitpat
     *
     * @return
     */
    @RequestMapping("/sycnShopifyUser")
    public Result sycnShopifyToPitpat(@RequestParam(required = false, value = "brandName") String brandName) {
        AbstractSycnStrategy abstractSycnStrategy = SYCN_MAP.get(brandName);
        if (abstractSycnStrategy == null) {
            abstractSycnStrategy = SYCN_MAP.get(ShopifyConstant.BrandEnum.SUPERUN.getCode());
        }
        abstractSycnStrategy.sycnShopifyToPitpat(ShopifyConstant.SyncTypeEnum.TYPE_1);
        return CommonResult.success();
    }

    /**
     * 同步shopif订单到pitpat
     *
     * @return
     */
    @RequestMapping("/sycnShopifyOrder")
    public Result sycnShopifyOrder(@RequestParam(required = false, value = "brandName") String brandName) {
        AbstractSycnStrategy abstractSycnStrategy = SYCN_MAP.get(brandName);
        if (abstractSycnStrategy == null) {
            abstractSycnStrategy = SYCN_MAP.get(ShopifyConstant.BrandEnum.SUPERUN.getCode());
        }
        abstractSycnStrategy.sycnShopifyToPitpat(ShopifyConstant.SyncTypeEnum.TYPE_2);
        return CommonResult.success();
    }

    /**
     * 同步pitpat用户到shopify
     *
     * @return
     */
    @RequestMapping("/sycnPitpatUser")
    public Result sycnPitpatUser() {
        shopifyPitpatUploadRecordService.sycnPitpatToShopify();
        return CommonResult.success();
    }

    /**
     * 同步shopify退款订单到pitpat
     *
     * @return
     */
    @RequestMapping("/sycnRefundOrder")
    public Result sycnRefundOrder(@RequestParam(required = false, value = "brandName") String brandName) {
        AbstractSycnStrategy abstractSycnStrategy = SYCN_MAP.get(brandName);
        if (abstractSycnStrategy == null) {
            abstractSycnStrategy = SYCN_MAP.get(ShopifyConstant.BrandEnum.SUPERUN.getCode());
        }
        abstractSycnStrategy.sycnShopifyRefundOrder();
        return CommonResult.success();
    }


    @Resource(name = "asyncExecutor")
    private Executor executor;
    @Resource
    private UserPushTokenService userPushTokenService;

    @RequestMapping("/getGrowLevelProgress")
    public Result getGrowLevelProgress(Integer target, Integer rank) throws Exception {
        BigDecimal growLevelProgress = sysConfigService.getGrowLevelProgress(BigDecimal.valueOf(target), rank);
        System.out.println(growLevelProgress);
        return CommonResult.success(growLevelProgress);
    }

    @RequestMapping("/updateLatestRunTime")
    public Result updateLatestRunTime(Long startId) throws Exception {
        List<ZnsUserEntity> list = userService.findList(UserQuery.builder().build());
//        for (ZnsUserEntity znsUserEntity : list) {
//            ZnsUserRunDataDetailsEntity last = userRunDataDetailsService.getOne(Wrappers.<ZnsUserRunDataDetailsEntity>lambdaQuery().eq(ZnsUserRunDataDetailsEntity::getUserId, znsUserEntity.getId()).eq(ZnsUserRunDataDetailsEntity::getIsDelete, 0).orderByDesc(ZnsUserRunDataDetailsEntity::getId).last("limit 1"));
//            if (Objects.nonNull(last)) {
//                userAddService.updateLatestRunTime(last.getUserId(), last.getModifieTime());
//            }
//        }
        return CommonResult.success();

    }

    @RequestMapping("/push")
    public Result push(String deviceToken, String alertTitle, String alertBody) {
//        List<String> deviceTokens = new ArrayList<>();
//        deviceTokens.add(deviceToken);
////        { \"aps\" : {\"alert\" : \"你好，来玩吗\", \"sound\" : \"default\", \"badge\" :1},\"liguoxin\":\"liguoxin\" }"
//        Map<String, Object> map = new HashMap<>();
//        map.put("jumpType",1);
//        map.put("jumpValue",1);
//
//        IosPushyUtils.push(deviceTokens,alertTitle,alertBody,false,map,1);
//        String token = "dB0NcZTsRf-Rpcwx-VfZwm:APA91bF2emm0at7fVDgObsnig-X_3DIdVVBsPHkD10GtvMZ1omvAKwbqI4JzaCIUTieBSf5iT5qxAJo63IQ2-AZ6tHqxB5jVLPyN4vBkXdYaZ3SWvwTGFHIUt8mnM-oLju0MPP2yx8Vv";
//        FirebaseUtils.pushSingle("ceshi", "ceshi", token, "通知", new HashMap<>(), "dev", null, "", null);
//        FirebaseUtils.send(token);

//
        // 手机弹窗消息
//        log.info("=====================================================");
//        MessageBo messageBo = new MessageBo();
//        messageBo.setTitle("新人福利活动");
//        messageBo.setContent("新人专属活动将在3小时后结束，不要错过丰厚奖励！");
//        messageBo.setActivityType(RunActivityTypeEnum.NEW_USER_ACTIVITY.getCode());
//        messageBo.setRouteType(1);
//        messageBo.setRouteValue("2");
//        List<Long> userIds = new ArrayList<>();
//        userIds.add(90698L);
////        appMessageService.sendImAndPush(userIds, "",messageBo,TencentImConstant.TIM_CUSTOM_ELEM,"",0);
//        appMessageService.push(userIds,messageBo,"",1);
//        log.info("=====================================================");
//
//        MessageTaskMsg messageTaskMsg = new MessageTaskMsg();
//        messageTaskMsg.setTitle("新人福利活动");
//        messageTaskMsg.setContent("新人专属活动将在3小时后结束，不要错过丰厚奖励！");
//        messageTaskMsg.setJumpType(3);
//        messageTaskMsg.setActivityType(6);
//        messageTaskMsg.setJumpValue("https://tkjgw.yijiesudai.com/#/newPeople");
//
//        messageTaskService.sendPush(userIds,"",messageTaskMsg,0);

        tencentImUtil.portraitSet("343530", "", "Membership Services", 2);

        return CommonResult.success();
    }

    // http://localhost:7770/device/test/portraitSet?id=277
    // http://************:7770/device/test/portraitSet?id=277
    @RequestMapping("/portraitSet")
    public Result portraitSet(Long id) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(id);
        tencentImUtil.portraitSet(id + "", znsUserEntity.getHeadPortrait(), znsUserEntity.getFirstName(), znsUserEntity.getGender());
        return CommonResult.success();
    }

    @RequestMapping("/sendImAndPush")
    public Result sendImAndPush(String value, String activityUserIds, String initiatorUserId, String jumpType, String activityType, String jumpValue) {
        List<ZnsUserEntity> users = userService.findByIds(NumberUtils.stringToLong(activityUserIds.split(",")));

        ActivityNotificationEnum activityNotification = ActivityNotificationEnum.getActivityNotification(1, 1);
        ZonedDateTime startTime = ZonedDateTime.now();
        String s = "###" + startTime.toInstant().toEpochMilli();
        String content = String.format(activityNotification.getNotificationContent(), "xiaoming", s, "10km", new BigDecimal(3.245));

        MessageBo message = new MessageBo();
        message.setJumpType(jumpType);
        message.setContent(content);
        message.setTitle("PitPat");
        message.setCollapseKey("Activity notification");

        Map<String, Object> extras = new HashMap<>();
        extras.put("value", value);
        message.setData(extras);

        ImMessageBo bo = new ImMessageBo();
        bo.setActivityType(activityType);
        bo.setJumpValue(jumpValue);
        bo.setJumpType(jumpType);
        bo.setImageUrl("");
        bo.setMsg(content);

//        appMessageService.sendImAndPush(users, bo, message, initiatorUserId);
        return CommonResult.success();
    }

    @RequestMapping("/launchActivityNotice")
    public Result launchActivityNotice() {
        ImMessageBo bo = new ImMessageBo();
        bo.setActivityType("1");
        bo.setUserName("panp");
        bo.setImage("");
        bo.setDetailID("182");
        bo.setChallengeType("1");
        bo.setMoney("11");
        bo.setState("0");
        long time = ZonedDateTime.now().toInstant().toEpochMilli();
        bo.setTime(String.valueOf(time));
        bo.setActivityEntryFee("1");
        bo.setDistance("1");
        bo.setIsKph("true");
        bo.setInviteUserName("潘昊");
        bo.setFriendId("14");
        bo.setBusinessID("match");
//        tencentImUtil.batchSendMsg(2, "administrator", Arrays.asList("14"), TencentImConstant.TIM_CUSTOM_ELEM, JsonUtil.writeString(bo));
        return CommonResult.success();
    }

    // http://34.218.245.170:7770/device/test/adminMsgWithDraw?fromUserId=administrator&toUserIds=334563,194984,334761,101998,334454,213925,334421,334481,334099,334654,334081,334516,101989,334070,334643,101364,334384,334222,308515,334166,334093,100511,245642,334207,100435,284715,284707,334356,334174,334040,324755,100450,334416,334018,125400,185953,102066,334526,102003,334138,334591,100449,100464,334158,101740,100463,334146,106340,205597,334262,334342,334665,334249,145989,100448,333941,333956,102006,251937,333939,101552,334078,112518,197490,334223,334457,116816,102010,333961,334025,162063,334082,334228,162677,303465,101959,334007,101528,147341&msgKey=458379388_372094521_1671152697
    //
    @RequestMapping("/adminMsgWithDraw")
    public Result adminMsgWithDraw(String fromUserId, String toUserIds, String msgKey) {
        String[] split = toUserIds.split(",");
        for (String toUserId : split) {
            tencentImUtil.adminMsgWithDraw(fromUserId, toUserId, msgKey);
        }
        return CommonResult.success();
    }

    /**
     * 消息撤回
     *
     * @param fromUserId
     * @param message
     * @param startCreateTime
     * @param endCreateTime
     * @return
     */
    @RequestMapping("/imMsgWithDraw")
    public Result imMsgWithDraw(String fromUserId, String message, String startCreateTime, String endCreateTime) {
        ZonedDateTime startTime = DateTimeUtil.parse(startCreateTime);
        ZonedDateTime endTime = DateTimeUtil.parse(endCreateTime);
        List<ImSendRecordLog> list = imSendRecordLogService.findList(
                ImSendRecordQuery.builder().fromUserId(Long.valueOf(fromUserId)).imMessage(message).startTime(startTime).endTime(endTime).build());
        for (ImSendRecordLog imSendRecordLog : list) {
            tencentImUtil.adminMsgWithDraw(fromUserId, imSendRecordLog.getUserId().toString(), imSendRecordLog.getMsgKey());
        }

        return CommonResult.success();
    }

    private final static String NEW_CONTENT = """
            Dear User,
            
            We want to inform you that the Goal Run+ feature has now been retired. If you participated in the Goal Run challenge, your rewards have already been credited to your account. Additionally, users who made payments for this feature will receive a refund as compensation.
            
            For those using an earlier version of PitPat, the Goal Run feature may still be accessible, but no further rewards will be available.
            
            To enhance your experience, we’re excited to announce a brand-new Farm Mini-Game, packed with fun surprises! To enjoy this new feature, please upgrade to the latest version of the app.
            
            Stay tuned for more exciting updates!
            
            Best regards,
            
            The PitPat Team
            """;

    //tb撤回重发
    @RequestMapping("/tbReSend")
    public Result tbReSend() {

        List<ImSendRecordLog> list = imSendRecordLogService.findtbReSend();
        log.info("符合要求条数{}", list.size());
        ExecutorService executor = Executors.newFixedThreadPool(50);
        AtomicInteger count = new AtomicInteger();
        for (ImSendRecordLog imSendRecordLog : list) {
            executor.execute(() -> {
                tencentImUtil.adminMsgWithDraw("administrator", imSendRecordLog.getUserId().toString(), imSendRecordLog.getMsgKey());
                log.info("成功撤回{}条", count.incrementAndGet());
            });

        }

        return CommonResult.success();
    }

    //粉丝消息撤回
    @RequestMapping("/fansReSend")
    public Result fansReSend() {
        int page = 0;
        int size = 10000;
        AtomicInteger count = new AtomicInteger();
        while (true) {
            List<ImSendRecordLog> list = imSendRecordLogService.findFansReSend(page * size, size);
            log.info("fansReSend符合要求条数{}", list.size());
            if (CollectionUtils.isEmpty(list)) {
                return CommonResult.success();
            }
            for (ImSendRecordLog imSendRecordLog : list) {
                tencentImUtil.adminMsgWithDraw("administrator", imSendRecordLog.getUserId().toString(), imSendRecordLog.getMsgKey());
                log.info("fansReSend成功撤回{}条", count.incrementAndGet());
            }
            page++;
        }
    }

//    @RequestMapping("/sportData")
//    public Result sportData(Integer time, String unique_code, Integer order_no, Long id_no, Long activityId) throws InterruptedException {
//        RunDataDeviceRequest runDataRequest = new RunDataDeviceRequest();
//        runDataRequest.setUn(unique_code);
//        runDataRequest.setOn(order_no);
//        runDataRequest.setId(id_no);
//        runDataRequest.setAid(activityId);
//        List<RunDataDeviceSecondRequest> list = new ArrayList<>();
//        Integer lastStepNum = 0;
//        BigDecimal lastMileage = BigDecimal.ZERO;
//        Integer g = RandomUtils.nextInt(1, 101);
//        int startTime = time / 3;
//        int endTime = startTime + startTime;
//
//        for (Integer i = 1; i <= time; i++) {
//            RunDataDeviceSecondRequest data = new RunDataDeviceSecondRequest();
//            data.setR(i);
//            Integer velocity = getVelocity(i);
//            data.setV(new BigDecimal(velocity));
//            lastMileage = getMileage(velocity, i, lastMileage);
//            data.setM(lastMileage);
//            data.setH(getHeartRate(velocity));
//            lastStepNum = getStepNum(lastStepNum, velocity);
//            data.setS(lastStepNum);
//            data.setG(getG(i, startTime, endTime, g));
//            data.setC(getC(i));
//            runDataRequest.setRs(0);
//            list.add(data);
//            if (i.equals(time)) {
//                runDataRequest.setRs(1);
//            }
//            if (i.equals(time)) {
//                runDataRequest.setRs(1);
//                runDataRequest.setD(list);
//                synchronize(runDataRequest);
//            } else if (i % 60 == 0) {
//                runDataRequest.setD(list);
//                synchronize(runDataRequest);
//                Thread.sleep(1000);
//                list = new ArrayList<>();
//            }
//        }
//
//        return CommonResult.success();
//    }

    private BigDecimal getC(Integer i) {
        return new BigDecimal(i / 12);
    }

    private Integer getG(Integer i, Integer startTime, Integer endTime, Integer g) {
        if (i >= startTime && i < endTime) {
            return g;
        }
        return 0;
    }


    /**
     * 补存数据
     *
     * @param startId
     * @param id
     * @param userId
     * @return
     */
    @RequestMapping("/replenishmentData")
    public Result replenishmentData(Long startId, Long id, Long userId) {
//        List<ZnsUserRunDataDetailsEntity> list = userRunDataDetailsService.list(Wrappers.<ZnsUserRunDataDetailsEntity>lambdaQuery()
//                .eq(ZnsUserRunDataDetailsEntity::getIsDelete, 0)
//                .eq(ZnsUserRunDataDetailsEntity::getRunStatus, 1)
//                .eq(Objects.nonNull(userId), ZnsUserRunDataDetailsEntity::getUserId, userId)
//                .eq(Objects.nonNull(id), ZnsUserRunDataDetailsEntity::getId, id)
//                .ge(Objects.nonNull(startId), ZnsUserRunDataDetailsEntity::getId, startId)
//        );
        List<ZnsUserRunDataDetailsEntity> list = null;
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.success();
        }
        for (ZnsUserRunDataDetailsEntity userRunDataDetail : list) {
            ZnsUserEntity userEntity = userService.findById(userRunDataDetail.getUserId());
            if (Objects.isNull(userEntity)) {
                continue;
            }
            Query query = new Query().addCriteria(new Criteria().and("runDataDetailsId").is(userRunDataDetail.getId()));
            List<ZnsUserRunDataDetailsMinuteEntity> entityList = mongoTemplate.find(query, ZnsUserRunDataDetailsMinuteEntity.class,
                    MongodbConstant.ZNS_USER_RUN_DATA_DETAILS_MINUTE);
            List<ZnsUserRunDataDetailsSecondEntity> detailsSecondEntities = entityList.stream().map(ZnsUserRunDataDetailsMinuteEntity::getDataDetails).flatMap(Collection::stream)
                    .collect(Collectors.toList());
            Double averFallingGradient = 0.0;
            if (!CollectionUtils.isEmpty(detailsSecondEntities)) {
                Double averHeartRate = detailsSecondEntities.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getHeartRate).average().orElse(0D);
                Double averStepFrequency = detailsSecondEntities.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getStepFrequency).average().orElse(0D);
                userRunDataDetail.setAverageHeartRate(averHeartRate.intValue());
                userRunDataDetail.setAverageStepFrequency(averStepFrequency.intValue());
                //运动详情设置心率区间
                userRunDataDetailsService.setDetailHeartRateTimeMap(userRunDataDetail, detailsSecondEntities, userEntity);
                //爬坡相关计算
                List<ZnsUserRunDataDetailsSecondEntity> climbingList = detailsSecondEntities.stream().filter(s -> s.getGradient() > 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(climbingList)) {
                    averFallingGradient = climbingList.stream().mapToInt(ZnsUserRunDataDetailsSecondEntity::getGradient).average().orElse(0D);
                    BigDecimal airlineDistance = SportsDataUnit.getComputeMileage(climbingList.size(), userRunDataDetail.getAverageVelocity());
                    userRunDataDetail.setClimbingMileage(SportsDataUnit.getClimbingMileage(averFallingGradient, airlineDistance));
                }
            }
            //计算步幅
            Integer averageStride = SportsDataUnit.getStride(userRunDataDetail.getStepNum(), userRunDataDetail.getRunMileage());
            userRunDataDetail.setAverageStride(averageStride);
            //计算能力值
            userRunDataDetail.setCapabilityValue(SportsDataUnit.getCapabilityValue(userRunDataDetail.getRunMileage(), userRunDataDetail.getRunTime()));
            //预计卡路里计算
            Integer calorieTarget = SportsDataUnit.getCalorieTarget(userRunDataDetail.getDistanceTarget(), userRunDataDetail.getTimeTarget(), userRunDataDetail.getAveragePace(),
                    userEntity.getWeight());
            userRunDataDetail.setCalorieTarget(calorieTarget);
            userRunDataDetail.setFatConsumption(SportsDataUnit.getFatConsumption(userRunDataDetail.getKilocalorie()));
            userRunDataDetailsService.update(userRunDataDetail);
        }

        return CommonResult.success();
    }

    private BigDecimal getMileage(Integer velocity, Integer i, BigDecimal lastMileage) {
        BigDecimal mileage = BigDecimal.ZERO;
        if (Integer.valueOf(8000).equals(velocity)) {
            mileage = new BigDecimal(2.22);
        } else if (Integer.valueOf(12000).equals(velocity)) {
            mileage = i % 3 == 0 ? new BigDecimal(4) : new BigDecimal(3);
        } else if (Integer.valueOf(16000).equals(velocity)) {
            mileage = new BigDecimal(4.44);
        } else if (Integer.valueOf(6000).equals(velocity)) {
            mileage = i % 3 == 0 ? new BigDecimal(2) : new BigDecimal(1.5);
        }
        return lastMileage.add(mileage).setScale(1, BigDecimal.ROUND_HALF_DOWN);
    }

    private Integer getStepNum(Integer lastStepNum, Integer velocity) {
        Integer random = getRandom(2, 4);
        if (Integer.valueOf(8000).equals(velocity)) {
            random = getRandom(2, 3);
        } else if (Integer.valueOf(12000).equals(velocity)) {
            random = getRandom(3, 4);
        } else if (Integer.valueOf(16000).equals(velocity)) {
            random = getRandom(3, 6);
        } else if (Integer.valueOf(6000).equals(velocity)) {
            random = getRandom(2, 2);
        }
        return random + lastStepNum;
    }

    private Integer getHeartRate(Integer velocity) {
        if (Integer.valueOf(8000).equals(velocity)) {
            return getRandom(50, 70);
        } else if (Integer.valueOf(12000).equals(velocity)) {
            return getRandom(50, 120);
        } else if (Integer.valueOf(16000).equals(velocity)) {
            return getRandom(40, 150);
        } else if (Integer.valueOf(6000).equals(velocity)) {
            return getRandom(30, 70);
        }
        return 0;
    }

    public static Integer getRandom(Integer bound, Integer start) {
        Random rand = new Random();
        return rand.nextInt(bound) + start;
    }

    private Integer getVelocity(Integer i) {
        if (i <= 300) {
            return 8000;
        } else if (300 < i && i <= 1600) {
            return 12000;
        } else if (1600 < i && i <= 3200) {
            return 16000;
        } else {
            return 10000;
        }

    }

    @Autowired
    private ZnsUserService znsUserService;

    // http://************:7770/device/test/test3
    @RequestMapping("/test3")
    public String test3() {

//        ZnsUserEntity znsUserEntity = znsUserService.selectZnsUserListById(1l);

//        znsUserService.updateZnsUserEntityById(znsUserEntity);
        return "isodids";
    }


    // http://localhost:7770/device/test/test4
    @RequestMapping("/test4")
    public String test4() {
        // 计算当前时间往前推一个月的时间作为开始时间,此参数单独用于优化sql，并无实际意义
        String startTime = ZonedDateTime.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        List<Map<String, Object>> runActivityHistoryUsers = activityUserManager.getRunActivityHistoryUsers(RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType(), null, startTime);
        return "isodids";
    }


    // localhost:7770/device/test/vipDing?userId=90718&runDataDetailsId=88877
    @RequestMapping("/vipDing")
    public String vipDing(@RequestParam("userId") Long userId, @RequestParam("runDataDetailsId") Long runDataDetailsId) {
        activityUserManager.sendPlusVipDingTalkMessages(runDataDetailsId, userId, "");
        return "vipDing";
    }


    @Autowired
    private UserMedalService userMedalService;

    // http://localhost:7770/device/test/test5
    @RequestMapping("/test5")
    public String test5() {
        ZnsUserEntity user = new ZnsUserEntity();
        user.setId(2L);
        user.setIsRobot(0);
        userMedalService.deliverMedal(user, 2794l, 1, null);
        return "isodids";
    }

    // http://localhost:7770/device/test/deliverMedal?userId=644&activityId=86902
    @RequestMapping("/deliverMedal")
    public String deliverMedal(@RequestParam("userId") Long userId, @RequestParam("activityId") Long activityId) {
        ZnsUserEntity user = new ZnsUserEntity();
        user.setId(userId);
        user.setIsRobot(0);
        userMedalService.deliverMedal(user, activityId, 1, null);
        return "isodids";
    }

    @Autowired
    private ExpUserService expUserService;

    // http://localhost:7770/device/test/test6
    @RequestMapping("/test6")
    public String test6() {
        expUserService.deliverExp(2l, 2794l, 1);
        return "isodids";
    }


    // http://localhost:7770/device/test/test7?activityId=-1&userId=2&exp=5
    // http://*************:7770/device/test/test7?activityId=-1&userId=2&exp=5
    @RequestMapping("/test7")
    public String test7(Long activityId, Long userId, Integer exp) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("exp", exp);
        data.put("nickName", znsUserEntity.getFirstName());
        data.put("headPortrait", znsUserEntity.getHeadPortrait());
        socketPushUtils.testPush(activityId, SocketEventEnums.RUN_500_MESSAGE.getCode(), data);
        return "sucess";
    }

    // http://localhost:7770/device/test/test7?activityId=-1&userId=2&exp=5

    // http://*************:7770/device/test/test8?activityId=-1&userId=2
    @RequestMapping("/test8")
    public String test8(Long activityId, Long userId) {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "进惠");
        data.put("remark", "xxx");
        data.put("userId", userId);
        data.put("url", "bb");
        socketPushUtils.testPush(1l, SocketEventEnums.PUSH_MEDAL_MESSAGE.getCode(), data);
        return "sucess";
    }

    // http://localhost:7770/device/test/test10
    @RequestMapping("/test10")
    public Result test10() {
//        List<ZnsUserEntity> znsUserEntity = znsUserService.selectUserAll();
//        for (ZnsUserEntity znsUserEntity1 : znsUserEntity) {
//            userMedalService.initMedal(znsUserEntity1.getId());
//        }
        return CommonResult.success();
    }


    // http://************:7770/device/test/test11
    @RequestMapping("/test11")
    public Result test11(String getUrl) {
        log.info("url=" + getUrl);
        //String result = HttpUtil.doGet(getUrl, 10000);
        String response = RestTemplateUtil.get(getUrl);
        log.info("result = " + response);
        return CommonResult.success();
    }

    @RequestMapping("/test12")
    public Result test12() {
        Integer currentRank = runActivityUserService.getCurrentRank(182L, 1, 267L, 200, BigDecimal.valueOf(1000));
        return CommonResult.success(currentRank);
    }


    @Value("${zns.config.rabbitQueue.run}")
    private String run;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    // http://************:7770/device/test/test13?activityId=267&userId=120&isFinished=1
    @RequestMapping("/test13")
    public Result test13(Long activityId, Long userId, Integer isFinished) {
        RunningData runningData = new RunningData(activityId, userId, isFinished, 1l, "");
        log.info("RunDataEndListener 发送跑完数据：" + runningData);
        rabbitTemplate.convertAndSend(run, JsonUtil.writeString(runningData));
        return CommonResult.success();
    }

    // http://************:7770/device/test/testUserInfo?userIds=2
    // http://************:7770/device/test/testUserInfo?userIds=371
    @RequestMapping("/testUserInfo")
    public Result testUserInfo(String userIds) {
        if (StringUtil.isEmpty(userIds)) {
            return CommonResult.fail("没有用户id");
        }
        String userId[] = userIds.split(",");
        for (String id : userId) {
            ZnsUserEntity znsUserEntity = znsUserService.findById(MapUtil.getLong(id, -1l));
            if (znsUserEntity != null) {

                tencentImUtil.portraitSet(id, znsUserEntity.getHeadPortrait(), znsUserEntity.getFirstName(), znsUserEntity.getGender());
            } else {
                log.info("userId = " + id + " ,异常 ");
            }
        }
        return CommonResult.success();
    }

    @RequestMapping("/testUserInfo2")
    public Result testUserInfo2(Long startId, Long endId) throws InterruptedException {

        Long userId = startId;
        while (userId <= endId) {
            ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
            if (znsUserEntity != null) {
                if (znsUserEntity.getIsRobot() != 1) {
                    Thread.sleep(100);
                    tencentImUtil.portraitSet(userId.toString(), znsUserEntity.getHeadPortrait(), znsUserEntity.getFirstName(), znsUserEntity.getGender());
                }
            } else {
                log.info("userId = " + userId + " ,异常 ");
            }
            userId++;
        }

        return CommonResult.success();
    }

    // http://************:7770/device/test/test14?username=<EMAIL>&password=Qq123456
    @RequestMapping("/test14")
    public Result test14(String username, String password) {
        Map<String, Object> object = new HashMap<>();
        object.put("user_name", username);
        object.put("password", password);
        log.info("rainmaker登录，rainmaker请求信息：{}", object);
        //String res = com.linzi.pitpat.data.util.HttpUtil.doPost("https://p51zc9nzn3.execute-api.us-east-1.amazonaws.com/dev/v1/custom_login", JsonUtil.writeString(object), 10000);
        String response = RestTemplateUtil.post("https://p51zc9nzn3.execute-api.us-east-1.amazonaws.com/dev/v1/custom_login", JsonUtil.writeString(object));
        log.info("rainmaker登录，rainmaker返回信息：{}", response);
        return CommonResult.success();
    }

    @RequestMapping("/redis/test15")
    public Result test15(String redisKey) {
        return CommonResult.success(redisCache.getCacheObject(redisKey));
    }

    @RequestMapping("/redis/test16")
    public Result test16(String redisKey, String value, int expire, TimeUnit timeUnit) {
        //token保存一年
        redisCache.setCacheObject(redisKey, value, expire, timeUnit);
        return CommonResult.success(redisCache.getCacheObject(redisKey));
    }

    @RequestMapping("/redis/deleteRedis")
    public Result deleteRedis(String redisKey) {
        //token保存一年
        redisCache.deleteObject(redisKey);
        return CommonResult.success();
    }

    static class Solution {

        public static boolean validSquare(int[] p1, int[] p2, int[] p3, int[] p4) {
            if (p1.length != 2 || p1.length != 2 || p3.length != 2 || p4.length != 2) {
                return false;
            }
            //计算点与点之间的距离
            double v1 = Math.pow((p1[0] - p2[0]), 2) + Math.pow((p1[1] - p2[1]), 2);
            double v2 = Math.pow((p1[0] - p3[0]), 2) + Math.pow((p1[1] - p3[1]), 2);
            double v3 = Math.pow((p1[0] - p4[0]), 2) + Math.pow((p1[1] - p4[1]), 2);
            double v4 = Math.pow((p2[0] - p3[0]), 2) + Math.pow((p2[1] - p3[1]), 2);
            double v5 = Math.pow((p2[0] - p4[0]), 2) + Math.pow((p2[1] - p4[1]), 2);
            double v6 = Math.pow((p3[0] - p4[0]), 2) + Math.pow((p3[1] - p4[1]), 2);

            List<Double> list = new ArrayList<>();
            list.add(v1);
            list.add(v2);
            list.add(v3);
            list.add(v4);
            list.add(v5);
            list.add(v6);

            Map<Double, List<Double>> collect = list.stream().collect(Collectors.groupingBy(a -> a));
            if (collect.size() > 2) {
                return false;
            }
            List<Double> doubles = new ArrayList<>(collect.keySet());

            Double aDouble1 = doubles.get(0);
            Double aDouble2 = doubles.get(1);

            if (aDouble1 * 2 != aDouble2 && aDouble2 * 2 != aDouble1) {
                return false;
            }
            return true;
        }

        public static void main(String[] args) {
            List<Long> list = JsonUtil.readValue("[123,22]", List.class);
            System.out.println(list);

        }
    }


    // https://tkjapi.yijiesudai.com/device/test/test16
    @PostMapping("/test16")
    public Result test16(@RequestBody TestDto testDto) {
        log.info("==============" + JsonUtil.writeString(testDto));
        return CommonResult.success();
    }

    @Value("${admin.server.gamepush}")
    private String gamepush;

    // https://tkjapi.yijiesudai.com/device/test/addRobot?userId=654&roomId=-1&roadId=101
    @GetMapping("/addRobot")
    public Result addRobot(Long userId, Long roomId, Long roadId, Long activityId) {
        ZnsUserEntity matchUser = znsUserService.findById(userId);
        GamePushUtils.addRobot(gamepush, roomId, roadId, matchUser, activityId, 0l);
        return CommonResult.success();
    }

    // https://tkjapi.yijiesudai.com/device/test/setSpeed?userId=654&speed=12&distance=100&remainDistance=100000000
    @GetMapping("/setSpeed")
    public Result setSpeed(Long userId, BigDecimal speed, Integer distance, Integer remainDistance) {
        GamePushUtils.setRobotSpeed(gamepush, userId, BigDecimalUtil.divide(speed, new BigDecimal(3.6)), distance, remainDistance);
        return CommonResult.success();
    }


    // https://tkjapi.yijiesudai.com/device/test/removeRoot?userId=654
    @GetMapping("/removeRoot")
    public Result setSpeed(Long userId) {
        GamePushUtils.deleteRobot(gamepush, 0l, userId, 0l);
        return CommonResult.success();
    }


    public static void main(String[] args) {
        //[123, 34, 83, 82, 111, 98, 111, 116, 67, 111, 117, 110, 116, 34, 58, 45, 49, 125]
//        int[] a = {123, 34, 83, 82, 111, 98, 111, 116, 67, 111, 117, 110, 116, 34, 58, 45, 49, 125};
//        for (int i = 0; i < a.length; i++) {
//            System.out.println((char) a[i]);
//        }
//        System.out.println(new Date(1726087200000l));
        List<Integer> times = new ArrayList<>();

        for (int i = 0; i < 157; i++) {
            times.add(getRandomTime());
        }
        System.out.println(times);
        System.out.println();

        Map<String, Object> jsonObjectConfig = JsonUtil.readValue(
                "{\"runningGoalsUnit\":0,\"runningGoals\":[500,800,1000,2000,3000],\"rateLimitingUnit\":-1,\"CRobotCount\":4,\"ARobotCount\":5,\"SPlusRobotCount\":1,\"SRobotCountStart\":1,\"CRobotCountEnd\":6,\"SRobotCount\":3,\"DRobotCountStart\":2,\"DRobotCountEnd\":7,\"advertisingImage\":\"https://pitpat-oss.s3.us-east-2.amazonaws.com/202301/iG213lAjw1sb4775.png\",\"coverImage\":\"https://pitpat-oss.s3.us-east-2.amazonaws.com/202301/iHx13lAjw25l0313.png\",\"runningGoalsAward\":[{\"4\":\"4\",\"5\":\"5\",\"6\":\"6\",\"7\":\"7\",\"8\":\"8\",\"9\":\"9\",\"10\":\"10\",\"thirdAward\":\"3\",\"goal\":500.0,\"award\":\"1\",\"secondAward\":\"2\",\"firstAward\":\"1\"},{\"7\":\"0\",\"thirdAward\":\"0\",\"goal\":800.0,\"award\":\"0\",\"secondAward\":\"0\",\"firstAward\":\"0\"},{\"4\":\"4\",\"5\":\"0\",\"6\":\"6\",\"7\":\"\",\"8\":\"\",\"9\":\"\",\"10\":\"\",\"thirdAward\":\"0\",\"goal\":1000.0,\"award\":\"1\",\"secondAward\":\"1\",\"firstAward\":\"0\"},{\"4\":\"0\",\"5\":\"0\",\"thirdAward\":\"0\",\"goal\":2000.0,\"award\":\"0\",\"secondAward\":\"0\",\"firstAward\":\"0\"},{\"4\":\"5\",\"thirdAward\":\"4\",\"goal\":3000.0,\"award\":\"1\",\"secondAward\":\"3\",\"firstAward\":\"2\"}],\"ARobotCountStart\":1,\"BRobotCount\":2,\"runningGoalCouponAward\":[{\"4\":{\"couponName\":\"参赛必胜券: 参赛必胜券—1\",\"num\":1,\"couponId\":1},\"goal\":500.0,\"thirdAward\":{\"couponName\":\"参赛必胜券: 幸运现金劵-2\",\"num\":1,\"couponId\":3},\"award\":{\"couponName\":\"参赛必胜券: 幸运现金劵-3\",\"num\":1,\"couponId\":4},\"firstAward\":{\"couponName\":\"参赛必胜券: 幸运现金劵-3\",\"num\":1,\"couponId\":4}},{\"4\":{\"couponName\":\"参赛必胜券: 参赛必胜券—1\",\"num\":1,\"couponId\":1},\"8\":{\"couponName\":\"参赛必胜券: 参赛必胜券—1\",\"num\":1,\"couponId\":1},\"goal\":800.0},{\"4\":{\"couponName\":\"参赛必胜券: 参赛必胜券—1\",\"num\":1,\"couponId\":1},\"6\":{\"couponName\":\"参赛必胜券: 参赛必胜券—1\",\"num\":1,\"couponId\":1},\"goal\":1000.0,\"firstAward\":{\"couponName\":\"参赛必胜券: 参赛必胜券—1\",\"num\":1,\"couponId\":1}}],\"DRobotCount\":3,\"BRobotCountStart\":1,\"teamRunLastEnter\":60,\"SPlusRobotCountEnd\":2,\"automaticAdmissionTime\":3,\"runningGoalScoreAward\":[{\"4\":\"\",\"goal\":500.0,\"thirdAward\":\"\",\"award\":\"\",\"secondAward\":\"\",\"firstAward\":\"\"},{\"4\":\"4\",\"5\":\"0\",\"6\":\"6\",\"7\":\"\",\"8\":\"\",\"9\":\"\",\"10\":\"\",\"goal\":800.0,\"thirdAward\":\"3\",\"award\":\"1\",\"secondAward\":\"2\",\"firstAward\":\"1\"},{\"4\":\"4\",\"5\":\"0\",\"6\":\"6\",\"7\":\"\",\"8\":\"\",\"9\":\"\",\"10\":\"\",\"goal\":1000.0,\"thirdAward\":\"3\",\"award\":\"1\",\"secondAward\":\"0\",\"firstAward\":\"0\"},{\"4\":\"0\",\"5\":\"0\",\"goal\":2000.0,\"thirdAward\":\"0\",\"award\":\"0\",\"secondAward\":\"0\",\"firstAward\":\"0\"},{\"4\":\"5\",\"goal\":3000.0,\"thirdAward\":\"4\",\"award\":\"1\",\"secondAward\":\"3\",\"firstAward\":\"2\"}],\"SPlusRobotCountStart\":1,\"BRobotCountEnd\":6,\"ARobotCountEnd\":6,\"SRobotCountEnd\":4,\"detailsImage\":[\"https://pitpat-oss.s3.us-east-2.amazonaws.com/202301/itz13lAjw2KJ8221.png\"],\"activityRule\":\"无规则\",\"advertisingConfigImage\":\"https://pitpat-oss.s3.us-east-2.amazonaws.com/202302/iTk13nh6i2mf2443.png\",\"CRobotCountStart\":1,\"runBeforeEnter\":5}");

        RunActivityDetailVO activityDetailVO = new RunActivityDetailVO();

        activityDetailVO.setRunningGoalCouponAward(BaseOfficialActivityStrategy.getRunningGoalsScoreAward(jsonObjectConfig, ApiConstants.RUNNING_GOAL_COUPON_AWARD));

        log.info("{}", activityDetailVO);

    }


    // http://localhost:7770/device/test/redis/test17?planId=18974
    @RequestMapping("/redis/test117")
    public Result test16(Long planId) {
        RobotRunPlan robotRunPlan = robotRunPlanService.selectRobotRunPlanById(planId);
        MindUserMatch mindUserMatch = mindUserMatchService.selectMindUserMatchById(robotRunPlan.getMindUserMatchId());
        robotRunPlanManager.sendSpeedMsg(robotRunPlan, mindUserMatch, robotRunPlan.getEndTime());

        return CommonResult.success();
    }


    @Autowired
    private ZnsRunActivityDao znsRunActivityDao;

    @Autowired
    private UserOnlineBussiness userOnlineBussiness;
    @Autowired
    private EggActivityBizService eggActivityBizService;

    // https://tkjapi.yijiesudai.com/device/test/test18
    // 【【【【【【【【【【【【【【【【【【【【【【这个接口不能删除】】】】】】】】】】】】】】】】】】】】】】】】】】】】】】】
    // https://tkjapi.yijiesudai.com/device/test/test18
    // http://localhost:7770/device/test/test18
    // http://**********:7770/device/test/test18
    // http://*************:7770/device/test/test18
    // https://api.pitpatfitness.com/device/test/test18
    @RequestMapping("/test18")
    public Result test18() {
        eggActivityBizService.eggActivityPush(-1l, 101l, null);
        eggActivityBizService.eggActivityPush(-1l, 201l, null);

        eggActivityBizService.eggActivityPush(-1l, 301l, null);
        eggActivityBizService.eggActivityPush(-1l, 401l, null);
        eggActivityBizService.eggActivityPush(-1l, 501l, null);
        eggActivityBizService.eggActivityPush(-1l, 502l, null);
        eggActivityBizService.eggActivityPush(-1l, 601l, null);
        eggActivityBizService.eggActivityPush(-1l, 602l, null);
        eggActivityBizService.eggActivityPush(-1l, 603l, null);
        eggActivityBizService.eggActivityPush(-1l, 1101l, null);

        userOnlineBussiness.eggActivityPushDearRunSuperRun(-4l);

        userOnlineBussiness.eggActivityPushDearRunSuperRun(-5l);

        List<ZnsRunActivityEntity> znsRunActivityEntityList = znsRunActivityDao.selectActivityByStatusActivityStartTimeActivityState(1, ZonedDateTime.now(), Arrays.asList(0, 1));
        for (ZnsRunActivityEntity activityEntity : znsRunActivityEntityList) {
            if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
                Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
                List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
                for (Integer runingGoal : runningGoals) {
                    eggActivityBizService.eggActivityPush(activityEntity.getId(), activityEntity.getActivityRouteId(), runingGoal);
                }
            } else {
                eggActivityBizService.eggActivityPush(activityEntity.getId(), activityEntity.getActivityRouteId(), null);
            }
        }
        return CommonResult.success();
    }

    @Autowired
    private EveryDayDao everyDayDao;


    // http://localhost:7770/device/test/test19
    // http://************:7770/device/test/test19
    @RequestMapping("/test19")
    public Result test19() {
        List<EveryDay> list = everyDayDao.selectAll();
        for (EveryDay everyDay : list) {
            everyDay.setGmtEndDay(DateUtil.getEndOfDateEndWithSecond(everyDay.getGmtDay()));
            everyDayDao.updateEveryDayById(everyDay);
        }
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/redis/test24
    @RequestMapping("/redis/test24")
    public Result test24() {
        EveryDay everyDay = new EveryDay();
        ZonedDateTime date = DateUtil.addDays(ZonedDateTime.now(), 0);
        date = DateUtil.getStartOfDate(date);
        everyDay.setGmtDay(date);
        everyDayDao.insertEveryDay(everyDay);
        return CommonResult.success();
    }

    @Autowired
    private ImSendRecordLogService imSendRecordLogService;

    // http://localhost:7770/device/test/redis/test25
    @RequestMapping("/redis/test25")
    public Result test25() {
        imSendRecordLogService.addRecordLog(110l, "8932832", "32", new ImMessageBo());
        return CommonResult.success();
    }

    // http://localhost:7770/device/test/redis/test26
    @RequestMapping("/redis/test26")
    public Result test26() {
        UserRunDataEveryEverySecondService userRunDataEveryEverySecondService = SpringContextUtils.getBean(UserRunDataEveryEverySecondService.class);
        List<UserRunDataEveryEverySecond> list = new ArrayList<>();
        UserRunDataEveryEverySecond userRunDataEveryEverySecond1 = new UserRunDataEveryEverySecond();
        UserRunDataEveryEverySecond userRunDataEveryEverySecond2 = new UserRunDataEveryEverySecond();

        userRunDataEveryEverySecond1.setActivityId(1l);
        userRunDataEveryEverySecond2.setActivityId(2l);

        list.add(userRunDataEveryEverySecond1);
        list.add(userRunDataEveryEverySecond2);
        //userRunDataEveryEverySecondService.saveBatch(list);
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/redis/test27
    @RequestMapping("/redis/test27")
    public Result test27() {
        ZnsUserEntity znsUserEntity = userService.findById(1l);
        ZnsUserEntity znsUserEntity2 = userService.findById(2l);

        List<ZnsUserEntity> znsUserEntities = new ArrayList<>();
        znsUserEntities.add(znsUserEntity);
        znsUserEntities.add(znsUserEntity2);

        userService.updateBatchById(znsUserEntities);

        return CommonResult.success();
    }

    // http://localhost:7770/device/test/redis/test28
    @RequestMapping("/redis/test28")
    public Result test28() {

        String text = "慢SQL时间 " + ZonedDateTime.now() + " \n " +
                "MapperId : " + "xxx" + "\n" +
                "执行时间 : " + "98329823" + "\n" +
                "SQL : " + "select " + " \n " +
                "如果你不想在群里报消息提醒，拿着【MapperId】配置到 zns_slow_sql_config，最好配置容忍时间!!!";
        DingTalkUtils.sendMsg(DingTalkRequestDto.of("f0b7999a0198aedf3cf96c5d23e8395f4f82618162dabc83667e40aa12a65dd4", null, text));

        return CommonResult.success();
    }

    // 【【【【【【【【【【【【【【【【【【【【【【这个接口不能删除】】】】】】】】】】】】】】】】】】】】】】】】】】】】】】】
    @RequestMapping("/test29")
    public Result test29() {
        List<ZnsRunActivityEntity> znsRunActivityEntityList = znsRunActivityDao.selectActivityByStatusActivityStartTimeActivityState(1, ZonedDateTime.now(), Arrays.asList(0, 1));

        znsRunActivityEntityList = znsRunActivityEntityList.subList(0, 3);

        for (ZnsRunActivityEntity activityEntity : znsRunActivityEntityList) {
            try {
                List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = znsRunActivityUserDao.selectUserByActivityId(activityEntity.getId());

                String userIds = null;
                int mode = 2;
                if (RunActivityTypeEnum.CHALLENGE_RUN.getType().equals(activityEntity.getActivityType())) {//挑战跑必须传两个用户id
                    userIds = znsRunActivityUserEntities.get(0).getUserId() + "," + znsRunActivityUserEntities.get(1).getUserId();
                    mode = 3;
                }

                GamePushUtils.addRoom(gameDomain, activityEntity.getId(), mode, userIds);

                eggActivityBizService.doActivityEggPush(activityEntity.getId());
            } catch (Exception e) {
                log.error("异常", e);
                continue;
            }
        }

        List<MindUserMatch> mindUserMatches = mindUserMatchDao.selectMindUserMatchRuleByStatusIsRoot(1, 1);
        for (MindUserMatch mindUserMatch : mindUserMatches) {
            try {
                ZnsUserEntity znsUserEntity = znsUserService.findById(mindUserMatch.getUserId());
                ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityDao.selectActivityById(mindUserMatch.getActivityId());
                Integer targetRunMileage = null;
                if (Objects.equals(znsRunActivityEntity.getActivityType(), 4)) {
                    ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserDao.selectByActivityIdUserId(znsRunActivityEntity.getId(), mindUserMatch.getUserId());
                    targetRunMileage = znsRunActivityUserEntity.getTargetRunMileage();
                }
                userOnlineBussiness.robotUpOnline(znsRunActivityEntity, znsUserEntity, mindUserMatch, targetRunMileage);
            } catch (Exception e) {
                log.error("上线异常", e);
                continue;
            }
        }
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/redis/test30
    // http://************:7770/device/test/redis/test30
    // https://pitpat.pitpatfitness.com/device/test/redis/test30
    @RequestMapping("/redis/test30")
    public Result test30() {
        System.out.println(DateUtil.getTime(ZonedDateTime.now()));

        return CommonResult.success();
    }

    @RequestMapping("/redis/test31")
    public Result test31() {
        for (int i = 0; i < 100; i++) {
            Boolean res = redisTemplate.opsForValue().getBit(RedisConstants.RUN_DATA_DETAIL_SECOND_KEY + 1, i);
            System.out.println(i + "----------" + res);
            //设置redis标记
            redisTemplate.opsForValue().setBit(RedisConstants.RUN_DATA_DETAIL_SECOND_KEY + 1, i, true);
            redisTemplate.expire(RedisConstants.RUN_DATA_DETAIL_SECOND_KEY + 1, 2L, TimeUnit.HOURS);
            Boolean res2 = redisTemplate.opsForValue().getBit(RedisConstants.RUN_DATA_DETAIL_SECOND_KEY + 1, i);
            System.out.println(i + "----------" + res2);
        }

        return CommonResult.success();
    }


    // http://localhost:7770/device/test/redis/test34
    @RequestMapping("/redis/test34")
    public Result test34() {

        List<ZnsUserEntity> list1 = znsUserDao.selectByCreateTime(DateUtil.addDays(ZonedDateTime.now(), -3), ZonedDateTime.now());
        System.out.println(list1);

        List<ZnsUserEntity> list2 = znsUserDao.selectByCreateTimeStr("2022-11-01", "2022-11-05 00:00:59");
        System.out.println(list2);
        return CommonResult.success();
    }


    // 【【【【【【【【【【【【【【【【【【【【【【这个接口不能删除】】】】】】】】】】】】】】】】】】】】】】】】】】】】】】】
    // http://localhost:7770/device/test/test32
    // http://************:7770/device/test/test32
    // http://*************:7770/device/test/test32
    // https://api.pitpatfitness.com/device/test/test32
    @RequestMapping("/test32")
    public Result test32() {
        eggActivityBizService.urgeActivityUrgePush(-1l, null, null);
        List<ZnsRunActivityEntity> znsRunActivityEntityList = znsRunActivityDao.selectActivityByStatusActivityStartTimeActivityState(1, ZonedDateTime.now(), Arrays.asList(0, 1));
        for (ZnsRunActivityEntity activityEntity : znsRunActivityEntityList) {
            eggActivityBizService.doActivityUrgePush(activityEntity.getId());
        }
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test33
    @RequestMapping("/test33")
    public Result test33() {
        String key = "max_user_robot_idxxxxxx";
        redisCache.setCacheObject(key, 10l, 30, TimeUnit.SECONDS);
        Long maxUserId = MapUtil.getLong(redisCache.getCacheObject(key));
        System.out.println(maxUserId);

        return CommonResult.success();
    }

    @Value("${zns.config.rabbitQueue.delay_exchange_name}")
    private String delay_exchange_name;


    // http://localhost:7770/device/test/test35
    @RequestMapping("/test35")
    public Result test35() {
        log.info("delay_exchange_name=" + delay_exchange_name);
        RobotFinishDto robotFinishDto = new RobotFinishDto(10l, 20l,
                DateUtil.dateStr(ZonedDateTime.now(), DateUtil.YYYY_MM_DD_HH_MM_SS), 10l);
        DelayDto delayDto = new DelayDto(Constants.ROBOT_RUNNING, JsonUtil.writeString(robotFinishDto));
        log.info(" 机器人中途发送消息 = " + JsonUtil.writeString(delayDto));
        Integer secondRabbit = 5;
        // 通过广播模式发布延时消息 延时30分钟 持久化消息 消费后销毁 这里无需指定路由，会广播至每个绑定此交换机的队列
        rabbitTemplate.convertAndSend(delay_exchange_name, "", JsonUtil.writeString(delayDto), message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay((secondRabbit + 1) * 1000);   // 毫秒为单位，指定此消息的延时时长 ,+ 1 尽量保证机器人跑完了，再发送消息
            return message;
        });

        return CommonResult.success();
    }


    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @RequestMapping("/test36")
    public Result test36() throws InterruptedException {
        final Integer[] a = {0};
        log.info("00000000000-" + System.currentTimeMillis());
        String key = "aaaaaaaaaaaaaaaaaaaaa";
        taskExecutor.execute(() -> {
            log.info("thread 1");
            RLock lock = redissonClient.getLock(key);
            boolean tryLock = lock.tryLock();
            if (!tryLock) {
                log.info("a1=======" + a[0] + " " + System.currentTimeMillis());
                return;
            }
            try {
                Thread.sleep(110000l);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            System.out.println("a1=" + a[0] + " " + System.currentTimeMillis());
            if (a[0] == 1) {
                lock.unlock();
                return;
            }
            a[0] = 1;
            lock.unlock();
        });
        Thread.sleep(10000l);
        log.info("111111111111111----" + System.currentTimeMillis());
        taskExecutor.execute(() -> {
            log.info("thread 2");
            RLock lock = redissonClient.getLock(key);
            boolean tryLock = lock.tryLock();
            if (!tryLock) {
                log.info("a2=======" + a[0] + " " + System.currentTimeMillis());
                return;
            }
//                lock.tryLock(60l,60l ,TimeUnit.SECONDS);
            log.info("a2=" + a[0] + " " + System.currentTimeMillis());
            if (a[0] == 1) {
                lock.unlock();
                return;
            }
            a[0] = 1;
            lock.unlock();
        });

        Thread.sleep(50000l);
        log.info("222222222222---" + System.currentTimeMillis());

        taskExecutor.execute(() -> {
            log.info("thread 3");
            RLock lock = redissonClient.getLock(key);
            boolean tryLock = lock.tryLock();
            if (!tryLock) {
                System.out.println("a3=======" + a[0] + " " + System.currentTimeMillis());
                return;
            }
            System.out.println("a3=" + a[0] + " " + System.currentTimeMillis());
            if (a[0] == 1) {
                lock.unlock();
                return;
            }
            a[0] = 1;
            lock.unlock();
        });

        return CommonResult.success();
    }

    @RequestMapping("/test37")
    public Result test37(Long detailsId) {
        String config = sysConfigService.selectConfigByKey("check.cheat.rule");
        Map<String, Object> jsonObject = JsonUtil.readValue(config);
        BigDecimal cheatCountRate = BigDecimalUtil.divide(MapUtil.getBigDecimal(jsonObject.get("cheatCountRate")), new BigDecimal(100));
        List<Map> rules = JsonUtil.readList(jsonObject.get("rule"), Map.class);

        int totalCheatCount = 0;
        int totalCount = 0;
        //List<UserRunDataDetailsCheat> list = userRunDataDetailsCheatService.list(Wrappers.<UserRunDataDetailsCheat>lambdaQuery().eq(UserRunDataDetailsCheat::getRunDataDetailsId, detailsId));
        List<UserRunDataDetailsCheat> list = new ArrayList<>();

        for (UserRunDataDetailsCheat cheat : list) {
            boolean checkCheat = checkCheat(rules, cheat.getAverageVelocity(), cheat.getAverageHeartRate());
            cheat.setIsCheat(BooleanUtils.toInteger(checkCheat));
            if (cheat.getIsStatistics() == 1) {
                totalCount++;
            }
            if (cheat.getIsCheat() == 1 && cheat.getIsStatistics() == 1) {
                totalCheatCount++;
            }
        }
//        userRunDataDetailsCheatService.updateBatchById(list);
        BigDecimal cheatRate = BigDecimalUtil.divHalfDown(new BigDecimal(totalCheatCount), new BigDecimal(totalCount), 2);
        ZnsUserRunDataDetailsEntity userRunDataDetail = new ZnsUserRunDataDetailsEntity();
        userRunDataDetail.setId(detailsId);
        userRunDataDetail.setCheatRate(cheatRate);
        if (cheatRate.compareTo(cheatCountRate) >= 0) {
            userRunDataDetail.setIsCheat(1);
        }
        userRunDataDetailsService.update(userRunDataDetail);
        if (cheatRate.compareTo(cheatCountRate) >= 0) {
            CommonResult.success(1);
        }
        return CommonResult.success(0);
    }

    private boolean checkCheat(List<Map> rules, BigDecimal velocity, int averageRate) {
        for (Map rule : rules) {
            Integer minVelocity = MapUtils.getInteger(rule, "minVelocity");
            Integer minHeartRate = MapUtils.getInteger(rule, "minHeartRate");
            if (velocity.compareTo(new BigDecimal(minVelocity)) > 0) {
                if (averageRate <= minHeartRate) {
                    log.info("checkCheat true,velocity:" + velocity + ",minVelocity:" + minVelocity + ",averageRate:" + averageRate + ",minHeartRate:" + minHeartRate);
                    return true;
                }
            }
        }
        return false;
    }

    // http://localhost:7770/device/test/test38
    // http://************:7770/device/test/test38
    @RequestMapping("/test38")
    public Result test38() {
        List<ZnsRunActivityUserEntity> list = znsRunActivityUserDao.selectRunActivityUser(null);
        for (ZnsRunActivityUserEntity znsRunActivityUserEntity : list) {
            ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityDao.selectActivityById(znsRunActivityUserEntity.getActivityId());
            if (znsRunActivityEntity != null) {
                znsRunActivityUserEntity.setActivityType(znsRunActivityEntity.getActivityType());
                znsRunActivityUserDao.updateById(znsRunActivityUserEntity);
            }
        }
        return CommonResult.success();
    }

    @Resource
    protected ZnsRunActivityService runActivityService;


    @Autowired
    private ZnsRunActivityDao runActivityDao;

    // http://localhost:7770/device/test/test39
    // http://**********:7770/device/test/test39
    @RequestMapping("/test39")
    public Result test39(Integer activityType) {
//        RunActivityQuery.builder()
//                .status(1).activityStateIn(Arrays.asList(0, 1)).isDelete(0).minActivityStartTime(ZonedDateTime.now())
//
//        //查询所有赛事
//        List<ZnsRunActivityEntity> list = runActivityService.list(Wrappers.<ZnsRunActivityEntity>lambdaQuery()
//                .eq(ZnsRunActivityEntity::getStatus, 1)
//                                 .eq(Objects.nonNull(activityType), ZnsRunActivityEntity::getActivityType, activityType)
//                .in(ZnsRunActivityEntity::getActivityState, )
//                .eq(ZnsRunActivityEntity::getIsDelete, 0)
//                .ge(ZnsRunActivityEntity::getActivityStartTime, ZonedDateTime.now())
//                .like(ZnsRunActivityEntity::getIsTest, 1)
//                .likeLeft(ZnsRunActivityEntity::getRunMileage, 1)
//                .isNull(ZnsRunActivityEntity::getBatchNo)
//                .orderByAsc(ZnsRunActivityEntity::getActivityStartTime)
//                .orderByDesc(ZnsRunActivityEntity::getId));
//
//
//        List<ZnsRunActivityEntity> list2 = runActivityDao.selectActivityByCondition(
//                1, activityType, Arrays.asList(0, 1), ZonedDateTime.now(), 0, new BigDecimal(0), null);

        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test40
    @RequestMapping("/test40")
    public Result test40() {
        ZnsUserEntity userEntity = userService.selectUserTest();
        System.out.println(JsonUtil.writeString(userEntity));
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test41
    @RequestMapping("/test41")
    public Result test41() {
        ZnsUserEntity userEntity = userService.selectUserTestUserName("znssan");
        System.out.println(JsonUtil.writeString(userEntity));
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test42
    @RequestMapping("/test42")
    public Result test42() {
        ZnsUserEntity param = new ZnsUserEntity();
        param.setId(2l);
        ZnsUserEntity userEntity = userService.selectUserTestUserNameUserInfo("quyixiao", param);
        System.out.println(JsonUtil.writeString(userEntity));
        return CommonResult.success();
    }


    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    // http://localhost:7770/device/test/test43
    @RequestMapping("/test43")
    public Result test43() {
        long count = znsUserRunDataDetailsService.getRouteCount(201l);
        System.out.println(count);
        return CommonResult.success();
    }

    // http://localhost:7770/device/test/test45
    @RequestMapping("/test45")
    public Result test45() {
        ZnsUserEntity user = new ZnsUserEntity();
        user.setId(72515l);

        ZnsUserEntity z = znsUserDao.selectByFirstName(user, "q");

        return CommonResult.success();
    }

    @Autowired
    private ImageSegService imageSegService;

    @RequestMapping("/aa")
    public void aa() throws IOException {
        String s = imageSegService.segBodyCrop("https://pitpat-oss.s3.us-east-2.amazonaws.com/1741780289196.png");
        log.info("AAA:{}", s);
    }

    @Autowired
    private MonthRunDataService monthRunDataService;

    // http://localhost:7770/device/test/test46?id=10
    @RequestMapping("/test46")
    public Result test46() {
        monthRunDataService.saveMonthRunData(ZonedDateTime.now(), 206l);
        return null;
    }


    // http://************:7770/device/test/test47
    // http://*************:7770/device/test/test47
    @RequestMapping("/test47")
    public Result test47() {
        List<ZnsUserEntity> znsUserEntities = znsUserDao.selectUserByAll(0);
        for (ZnsUserEntity znsUserEntity : znsUserEntities) {
            ZonedDateTime date = DateUtil.addMonths(ZonedDateTime.now(), -7);
            for (int i = 0; i < 10; i++) {
                ZonedDateTime currentDate = DateUtil.addMonths(date, i);
                if (currentDate.toInstant().toEpochMilli() > ZonedDateTime.now().toInstant().toEpochMilli()) {
                    break;
                }
                monthRunDataService.saveMonthRunData(currentDate, znsUserEntity.getId());
            }
        }
        return null;
    }


    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    // http://localhost:7770/device/test/selectActivityByGuanFangZhuDui
    @PostMapping("/selectActivityByGuanFangZhuDui")             //官方组队跑
    public Result selectActivityByGuanFangZhuDui(@RequestBody RunAwardQuery query) {
        return CommonResult.success(activityUserManager.doSelectActivityByGuanFangZhuDui(query));
    }


    // http://localhost:7770/device/test/getMapTest0?id=10
    @RequestMapping("/getMapTest0")
    public Result getMapTest(Long id) {
        List<ZnsRunActivityEntity> znsRunActivityEntities = znsRunActivityDao.selectByIds(Arrays.asList(1l, 2l, 3l, 4l));
        Map<String, ZnsRunActivityUserEntity> znsRunActivityUserEntities = znsRunActivityUserDao.selectByZnsRunActivityEntity(znsRunActivityEntities);
        System.out.println(JsonUtil.writeString(znsRunActivityUserEntities));
        return null;
    }


    // http://localhost:7770/device/test/getMapTest1?id=10
    // http://**********:7770/device/test/getMapTest1?id=10
    @RequestMapping("/getMapTest1")
    public Result getMapTestList(Long id) {
        List<ZnsRunActivityEntity> znsRunActivityEntities = znsRunActivityDao.selectByIds(Arrays.asList(1l, 2l, 3l, 4l));
        Map<Long, List<ZnsRunActivityUserEntity>> znsRunActivityUserEntities = znsRunActivityUserDao.selectByZnsRunActivityEntityList(znsRunActivityEntities);
        System.out.println(JsonUtil.writeString(znsRunActivityUserEntities));
        return null;
    }


    // http://localhost:7770/device/test/getMapTest2?id=10
    @RequestMapping("/getMapTest2")
    public Result getMapTestListxx(Long id) {
        Map<Long, List<ZnsRunActivityUserEntity>> znsRunActivityUserEntities = znsRunActivityUserDao.selectByZnsRunActivityEntityXX(Arrays.asList(1l, 2l, 3l, 4l));
        System.out.println(JsonUtil.writeString(znsRunActivityUserEntities));
        return null;
    }

    // http://localhost:7770/device/test/getMapTest3?id=10
    // http://**********:7770/device/test/getMapTest3?id=10
    // https://tkjapi.yijiesudai.com/device/test/getMapTest3?id=10
    @RequestMapping("/getMapTest3")
    public Result getMapTestListCC(Long id) {
        Map<Long, ZnsRunActivityUserEntity> znsRunActivityUserEntities = znsRunActivityUserDao.selectByZnsRunActivityEntityXXCC(Arrays.asList(1l, 2l, 3l, 4l));
        System.out.println(JsonUtil.writeString(znsRunActivityUserEntities));
        return null;
    }

    @Autowired
    private ZnsUserEquipmentDao znsUserEquipmentDao;
    @Autowired
    private ZnsTreadmillService znsTreadmillService;


    // http://localhost:7770/device/test/sbBatchno?id=10
    @RequestMapping("/sbBatchno")
    public Result sbBatchno(Long id) {
        String contentSb = "I received my treadmill last Friday and the bracelet won’t charge";

        StringBuilder sbBatchno = new StringBuilder();
        if (StringUtils.hasText(contentSb) && contentSb.contains("bracelet")) {
            List<ZnsUserEquipmentEntity> znsUserEquipmentEntities = znsUserEquipmentDao.selectByUserId(9847l);
            sbBatchno.append("批次号：").append("\n");
            for (ZnsUserEquipmentEntity znsUserEquipmentEntity : znsUserEquipmentEntities) {
                ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.findById(znsUserEquipmentEntity.getEquipmentId());
                if (znsTreadmillEntity != null) {
                    sbBatchno.append(znsTreadmillEntity.getBatchNumber()).append("\n");
                }
            }
        }

        System.out.println(sbBatchno.toString());
        return null;
    }

    @RequestMapping("/tstSendPush1")
    public Result tstSendPush1(String title, String content, String ImageUrl, Integer RouteType, String RouteValue, String userIdStr, String batchNumber) {
        MessageBo messageBo = new MessageBo();
        messageBo.setTitle(title);
        messageBo.setContent(content);
        messageBo.setJumpType("5");
        messageBo.setCollapseKey("Activity notification");
        messageBo.setJumpImage(ImageUrl);
        messageBo.setRouteType(RouteType);
        messageBo.setRouteValue(RouteValue);
        List<Long> userIds = NumberUtils.stringToLong(userIdStr.split(","));
        List<List<Long>> partition = ListUtils.partition(userIds, 200);
        for (List<Long> ids : partition) {
            appMessageService.push(ids, messageBo, batchNumber);
        }
        return CommonResult.success();
    }

    // http://localhost:7770/device/test/test48
    // http://**********:7770/device/test/test48
    @RequestMapping("/test48")
    public Result test48() {
        ImMessageBo imMessageBo = new ImMessageBo();
        imMessageBo.setJumpType("6");
        imMessageBo.setJumpValue("lzrn://Race/SelectRaceGoals");

        Map<String, Object> params = new HashMap<>();
        params.put("runPersonType", 1);
        params.put("activityType", 2);
        params.put("fromType", 1);

        imMessageBo.setParams(params);
        imMessageBo.setImageUrl("https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
        imMessageBo.setMsg("哈哈在测试");
        List<Long> userIds = Arrays.asList(206l, 205l, 2l);
        //活动推送
//        appMessageService.sendIm("administrator", userIds, JsonUtil.writeString(imMessageBo), TencentImConstant.TIM_CUSTOM_ELEM, "", 0);
        return CommonResult.success();
    }

    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;


    @RequestMapping("/test49")
    public Result test49() {
//        ZnsRunActivityConfigEntity config = runActivityConfigService.getById(6L);
        ZnsRunActivityConfigEntity config = null;
        Map<String, Object> jsonObject = JsonUtil.readValue(config.getActivityConfig());
        jsonObject.put("popImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/202304/iYz13qx76xJ04386.png");
        jsonObject.put("backgroundImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/202304/iJA13qx76WRU9031.png");
        config.setActivityConfig(JsonUtil.writeString(jsonObject));
        //runActivityConfigService.updateById(config);
        return CommonResult.success();
    }


    @Value("${zns.config.rabbitQueue.dataEnd}")
    private String dataEnd;


    // http://************:7770/device/test/test50?runDataDetailsId=22312758L
    @RequestMapping("/test50")
    public Result test50(@RequestParam Long runDataDetailsId) {
        RunDataRequest runData = new RunDataRequest();
        log.info("userRunDataHandle跑步完处理 ：" + JsonUtil.writeString(runData));
        runData.setRunDataDetailsId(runDataDetailsId);
//        runData.setRunDataDetailsId(runDataDetailsId);
        runData.setUnique_code("进行测试rabbit");
        rabbitTemplate.convertAndSend(dataEnd, JsonUtil.writeString(runData));
        return CommonResult.success();
    }

    @RequestMapping("/test51")
    public Result test51() {
        RobotFinishDto robotFinishDto = new RobotFinishDto(-1l, -3l,
                "测试延迟队列", -4l);
        DelayDto delayDto = new DelayDto(Constants.ROBOT_FINISHED, JsonUtil.writeString(robotFinishDto));
        log.info(" 延迟队列发送消息 = " + JsonUtil.writeString(delayDto));             // 加60秒

        Integer second = 60;

        // 通过广播模式发布延时消息 延时30分钟 持久化消息 消费后销毁 这里无需指定路由，会广播至每个绑定此交换机的队列
        rabbitTemplate.convertAndSend(delay_exchange_name, "", JsonUtil.writeString(delayDto), message -> {
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            message.getMessageProperties().setDelay((second + 1) * 1000);   // 毫秒为单位，指定此消息的延时时长 ,+ 1 尽量保证机器人跑完了，再发送消息
            return message;
        });

        return CommonResult.success();
    }

    // http://************:7770/device/test/test52
    @RequestMapping("/test52")
    public Result test52() {
        try {
            ConnectionFactory factory = new ConnectionFactory();

            factory.setUsername("rz_rabbit");
            factory.setPassword("OiSRumH%3W0Am.kJ");

//Replace the URL with your information
            factory.setHost("b-6c1f30f1-adff-4719-99b4-8e2c00451111.mq.us-west-2.amazonaws.com");
            factory.setPort(5671);

// Allows client to establish a connection over TLS
            factory.useSslProtocol();
            log.info("ccccccccccccccccccc");

// Create a connection
            Connection conn = factory.newConnection();

            log.info("bbbbbbbbbbbbbbbbbbbbbbb");
// Create a channel
            Channel channel = conn.createChannel();

            log.info("----------------------");

            byte[] messageBodyBytes = "Hello, world!".getBytes();
            channel.basicPublish("aaa", "bbbb", new AMQP.BasicProperties().builder()
                            .contentType("text/plain")
                            .userId("userId")
                            .build(),
                    messageBodyBytes);
        } catch (Exception e) {
            log.error("异常", e);
        }

        return CommonResult.success();
    }


    // http://************:7770/device/test/test53
    @RequestMapping("/test53")
    public Result test53() {
        try {
            //1 创建连接工厂
            ConnectionFactory connectionFactory = new ConnectionFactory();
            //2 设置参数
            connectionFactory.setHost("b-6c1f30f1-adff-4719-99b4-8e2c00451111.mq.us-west-2.amazonaws.com");//设置注解
            connectionFactory.setPort(5671);//设置端口
            connectionFactory.setVirtualHost("/");//设置虚拟及
            connectionFactory.setUsername("rz_rabbit");
            connectionFactory.setPassword("OiSRumH%3W0Am.kJ");

            //3 创建连接
            Connection connection = connectionFactory.newConnection();
            //4 创建channel
            Channel channel = connection.createChannel();
            channel.exchangeDeclare("producer-customer-test", BuiltinExchangeType.DIRECT, true);
            //5 创建队列Queen
            /**参数介绍：
             * queueDeclare(String queue, boolean durable, boolean exclusive, boolean autoDelete, Map<String, Object> arguments)
             * queue：队列名称   durable：是否持久化   exclusive是否独占，只能有一个消费者监听队列。         * queue：队列名称   durable：是否持久化   exclusive是否独占，只能有一个消费者监听队列。
             * autoDelete:   当没有consumer时候是否删除队列
             *  internal:设置是否是内置的，如果设置为true，则表示是内置的交换器，审稿意见程序无法直接
             *  把消息发送到这个接口中，只能通过交换器路由到交换器这种方式
             *   arguments：配置的基本参数
             */
            channel.queueDeclare("hello_world", true, false, false, null);
            channel.queueBind("hello_world", "producer-customer-test", "routing_key_1");
            //6 发送消息到队列
            /**参数介绍：
             * basicPublish(String exchange, String routingKey, boolean mandatory, BasicProperties props, byte[] body)
             *exchange： 交换机的名称，简单模式下交换机会使用默认的    routingKey：路由名称,如果使用默认交换机要和队列名相同
             *props 配置信息    body：真是发送的消息数据
             */
            String constant = "大家好这是在测试";
            for (int i = 0; i < 3; i++) {
                String body = constant + i;
                channel.basicPublish("producer-customer-test", "routing_key_1", null, body.getBytes());
            }
            //7 释放连接资源
            channel.close();
            connection.close();
        } catch (Exception e) {
            log.error("异常", e);
        }

        return CommonResult.success();
    }

    @RequestMapping("/test54")  // http://localhost:7770/device/test/test54
    public Result test54() {
        try {
            int a = 1;
            int b = 2;
            int c = a / 0;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("异常,e {} ", e);
        }

        return null;
    }

    // http://************:7770/device/test/test55
    // http://localhost:7770/device/test/test55
    @RequestMapping("/test55")
    public Result test55() {

        ZnsUserEntity znsUserEntity = znsUserDao.selectUserById(1l);

        znsUserEntity.setFirstName("penghua_2");

        znsUserDao.updateZnsUserEntityById(znsUserEntity);

        znsUserEntity.setId(null);

        znsUserDao.insertOrUpdateUser(znsUserEntity);
        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test56
    @RequestMapping("/test56")
    public Result test56() {

        ZnsUserEntity znsUserEntity = znsUserDao.selectUserById(1l);

        znsUserEntity.setFirstName("penghu'a_2");

        znsUserDao.updateZnsUserEntityById(znsUserEntity);
        ;

        return CommonResult.success();
    }


    // http://************:7770/device/test/test57
    // http://**********:7770/device/test/test57
    // http://localhost:7770/device/test/test57
    @RequestMapping("/test57")
    public Result test57() {
        ZnsUserEntity znsUserEntity = znsUserDao.selectUserById(1l);
        ZnsUserEntity znsUserEntity1 = znsUserDao.selectUserById(2l);
        ZnsUserEntity znsUserEntity2 = znsUserDao.selectUserById(3l);
        List<ZnsUserEntity> list = new ArrayList<>();

        list.add(znsUserEntity);
        list.add(znsUserEntity1);
        list.add(znsUserEntity2);
        znsUserService.updateBatchById(list);

        return CommonResult.success();
    }

    // http://localhost:7770/device/test/test58
    @RequestMapping("/test58")
    public Result test58() {
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityDao.selectActivityById(55219l);
        znsRunActivityEntity.setIsDelete(1);
        znsRunActivityDao.updateById(znsRunActivityEntity);
        return CommonResult.success();
    }

    @Autowired
    private UserRunDataDetailsSecondService userRunDataDetailsSecondService;

    // http://localhost:7770/device/test/test59
    @RequestMapping("/test59")
    public Result test59() {
        String message = "{\"activityType\":3,\"averageHeartRate\":0,\"averagePace\":623,\"averageStepFrequency\":0,\"averageVelocity\":5.77,\"createTime\":1683879790124,\"dataDetails\":[{\"heartRate\":0,\"mileage\":1594,\"pace\":623,\"runDataDetailsId\":21264909,\"runTime\":901,\"stepFrequency\":0,\"stepNum\":0,\"velocity\":5.77},{\"heartRate\":0,\"mileage\":1595,\"pace\":623,\"runDataDetailsId\":21264909,\"runTime\":902,\"stepFrequency\":0,\"stepNum\":0,\"velocity\":5.77},{\"heartRate\":0,\"mileage\":1597,\"pace\":623,\"runDataDetailsId\":21264909,\"runTime\":903,\"stepFrequency\":0,\"stepNum\":0,\"velocity\":5.77},{\"heartRate\":0,\"mileage\":1598,\"pace\":623,\"runDataDetailsId\":21264909,\"runTime\":904,\"stepFrequency\":0,\"stepNum\":0,\"velocity\":5.77},{\"heartRate\":0,\"mileage\":1600,\"pace\":623,\"runDataDetailsId\":21264909,\"runTime\":905,\"stepFrequency\":0,\"stepNum\":0,\"velocity\":5.77},{\"heartRate\":0,\"mileage\":1600.00,\"pace\":623,\"runDataDetailsId\":21264909,\"runTime\":905,\"stepFrequency\":0,\"stepNum\":0,\"velocity\":5.77}],\"id\":49633802,\"isDelete\":0,\"isRobot\":1,\"runDataDetailsId\":21264909,\"runMileage\":1600.00,\"runStatus\":1,\"runTime\":905}";

        ZnsUserRunDataDetailsMinuteEntity entity = JsonUtil.readValue(message, ZnsUserRunDataDetailsMinuteEntity.class);
        //非机器人或机器人排行赛存
        if (Integer.valueOf(0).equals(entity.getIsRobot()) || Integer.valueOf(3).equals(entity.getActivityType())) {
            userRunDataDetailsSecondService.saveSecondList(entity.getDataDetails(), entity.getRunDataDetailsId(), entity.getRunStatus());
        }

        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test60
    // http://**********:7770/device/test/test60
    @RequestMapping("/test60")
    public Result test60() {

        znsUserDao.updateHistoryUserExpByIds(3, Arrays.asList(1, 2, 3));
        return CommonResult.success();
    }


    @Autowired
    private ImSendRecordLogDao imSendRecordLogDao;


    // http://localhost:7770/device/test/test61
    // http://**********:7770/device/test/test61
    @RequestMapping("/test61")
    public Result test61() {

        //查询1小时内
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime start = DateUtil.addHours(now, -1);
        Long startTime = Long.valueOf(start.toInstant().toEpochMilli()) / 1000;
        Long nowTime = Long.valueOf(now.toInstant().toEpochMilli()) / 1000;
        //查询保存单聊消息

        //修改lastReadTime时间之前的消息为已读
        ImSendRecordLog readRecord = new ImSendRecordLog();
        readRecord.setImReadTime(ZonedDateTime.now());
        readRecord.setIsRead(1);
        imSendRecordLogDao.update(readRecord, Wrappers.<ImSendRecordLog>lambdaUpdate()
                .eq(ImSendRecordLog::getUserId, 10)
                .eq(ImSendRecordLog::getIsRead, 0)
                .le(ImSendRecordLog::getImMsgTime, ZonedDateTime.now())
                .eq(ImSendRecordLog::getIsDelete, 0));

        return CommonResult.success();
    }


    @RequestMapping("/test62")
    public Result test62() {
        List<ZnsRunActivityEntity> znsRunActivityEntityList = znsRunActivityDao.selectActivityByStatusActivityStartTimeActivityState(1, ZonedDateTime.now(), Arrays.asList(0, 1));
        for (ZnsRunActivityEntity activity : znsRunActivityEntityList) {
            if (activity.getActivityType() == 4) {
                int betweenSecond = DateUtil.betweenSecond(ZonedDateTime.now(), activity.getActivityEndTime());
                Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activity.getActivityConfig());
                List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
                for (Integer runningGoal : runningGoals) {
                    Long activityGoalId = NumberUtils.getGoalImNumber(activity.getId(), runningGoal, activity.getCompleteRuleType());
                    redisTemplate.opsForValue().set(RedisConstants.ACTIVITY_ID + activityGoalId.toString(), "1", betweenSecond, TimeUnit.SECONDS);
                    //推送到游戏服务器端
                    try {
                        GamePushUtils.addRoom(gameDomain, activityGoalId, 2, null);
                    } catch (Exception e) {
                        log.info("请求游戏服务器异常:{}", e.getMessage());
                    }
                }
            } else if (activity.getActivityType() == 3) {
                //推送到游戏服务器端
                try {
                    GamePushUtils.addRoom(gameDomain, activity.getId(), 2, null);
                } catch (Exception e) {
                    log.info("请求游戏服务器异常:{}", e.getMessage());
                }
            }
        }
        return CommonResult.success();
    }


    @RequestMapping("/getRobot158AndSetTime")
    public Result getCreateRobotAndSetTime(Long time) {
        /**
         *  String key = ConfigKeyEnums.ROBOT_158_USED_INFO.getCode();
         String configStr =  sysConfigService.selectConfigByKey(key);
         if (StringUtil.isEmpty(configStr)){
         RobotUsedInfo158DTO dto = new RobotUsedInfo158DTO();
         // 需要修改 获取158个机器人id
         List<Long> ids = userService.getNotMatchsRobotIds();
         List<Long> usedIds =new ArrayList<>();
         dto.setIds(ids);
         dto.setUsedIds(usedIds);
         SysConfig config = new SysConfig();
         config.setConfigKey(key);
         config.setConfigValue(JsonUtil.writeString(dto));
         sysConfigService.insertConfig(config);
         }*/
        String timeKey = ConfigKeyEnums.ROBOT_158_USED_INFO.getCode();
        String timeStr = sysConfigService.selectConfigByKey(timeKey);
        if (StringUtil.isEmpty(timeStr)) {
            List<Integer> times = new ArrayList<>();

            for (int i = 0; i < 157; i++) {
                times.add(getRandomTime());
            }
//            String atStr = StringUtil.join(times,",");
//            RobotUsedInfo158DTO dto = new RobotUsedInfo158DTO();
//            dto.setTimes(times);
//            dto.setTime(time);
//            SysConfig config = new SysConfig();
//            config.setConfigKey(timeKey);
//            config.setConfigValue(JsonUtil.writeString(dto));
//            sysConfigService.insertConfig(config);
        }
        return null;
    }


    @RequestMapping("/getFollowUserByRobot")
    public Result getFollowUserByRobot() {
        FollowImMessageBo bo = new FollowImMessageBo();
        bo.setMsg("test");
//        bo.setPhoto("https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iqX13st9X6d49801.jpg");
        bo.setUserName("Caton Nelson");

        userFriendService.add(82498L, 2L);
        userFriendService.add(82498L, 205L);

//        tencentImUtil.sendMsg(1, "82498", "2", "TIMCustomElem", JsonUtil.writeString(bo));
//        tencentImUtil.sendMsg(1, "82498", "205", "TIMCustomElem", JsonUtil.writeString(bo));

        return null;
    }


    @Autowired
    private MessageTaskMsgDao messageTaskMsgDao;

    @Autowired
    private MessageTaskService messageTaskService;

    // http://localhost:7770/device/test/testxxx?userId=205
    // http://**********:7770/device/test/test60
    @RequestMapping("/testxxx")
    public Result testxxx(Long userId) {
        List<MessageTaskMsg> msgList = messageTaskMsgDao.selectByTaskId(-1l);
        if (!CollectionUtils.isEmpty(msgList)) {
//            messageTaskService.doPush(msgList, null, Arrays.asList(userId), 1, OrderUtil.getBatchNo(), 1, 1);
        }
        return CommonResult.success();
    }

    /**
     * 指定用户推送指定消息
     *
     * @param userId
     * @param taskMsgId
     * @return
     */
    @RequestMapping("/testPush/{userId}/{taskMsgId}")
    public Result testPush(@PathVariable("userId") Long userId, @PathVariable("taskMsgId") Long taskMsgId) {
        List<MessageTaskMsg> msgList = messageTaskMsgDao.selectByTaskId(taskMsgId);
        if (!CollectionUtils.isEmpty(msgList)) {
//            messageTaskService.doPush(msgList, null, Arrays.asList(userId), 1, OrderUtil.getBatchNo(), 1, 1);
        }
        return CommonResult.success();
    }


    private static Integer getRandomTime() {
        Random random = new Random();

        int s = random.nextInt(42) + 3;
        return Integer.valueOf(s);
    }

    // http://localhost:7770/device/test/test88?userId=205
    // http://**********:7770/device/test/test60
    @PostMapping("/test88")
    public Result test88(@RequestBody UserTestDto dto) {
        try {
            System.out.println(JsonUtil.writeString(dto));
            int i = 0;
            int j = 1;
            int c = j / i;
        } catch (Exception e) {
            log.error("异常", e);
        }
        return CommonResult.success();
    }


    @Value("${zns.config.rabbitQueue.old_user_send_coupon_queue}")
    private String old_user_send_coupon_queue;

    @PostMapping("/oldUser/sendCoupon/test")
    public void testSend(@RequestBody UserActiveCouponQueueDto userActiveCouponQueueDto) {
        userActiveCouponQueueDto.setReSendFlag(YesNoStatus.NO.getCode());
        //活跃用户运动记录活动配置天数
        rabbitTemplate.convertAndSend(old_user_send_coupon_queue, JsonUtil.writeString(userActiveCouponQueueDto));
    }


    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;

    // http://************:7770/device/test/test90
    // http://localhost:7770/device/test/test90
    @RequestMapping("/test90")
    public Result test90() {

        List<ActivityUserScore> activityUserScores = activityUserScoreDao.selectActivityUserScoreByActivityIdExpireTime(null);
        for (ActivityUserScore activityUserScore : activityUserScores) {
            activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(activityUserScore.getGmtCreate(), 4, TimeZone.getTimeZone("UTC-8")));
            activityUserScore.setSendTime(activityUserScore.getGmtCreate());
            activityUserScore.setAwardTime(activityUserScore.getGmtCreate());
            activityUserScoreService.update(activityUserScore);
        }

        return CommonResult.success();
    }


    // http://localhost:7770/device/test/test91
    @RequestMapping("/test91")
    public Result test91() {

        ZnsRunActivityEntity activityEntity = znsRunActivityDao.selectActivityById(10l);
        activityEntity.setActivityStartTime(ZonedDateTime.now());
        znsRunActivityDao.updateById(activityEntity);
        return CommonResult.success();
    }

    @RequestMapping("/robotTest")
    public Result task() {
        String key = ConfigKeyEnums.ROBOT_158_USED_INFO.getCode();
        SysConfig config = sysConfigService.selectSysConfigByKey(key);
        RobotUsedInfo158DTO dto = getRobotUsedInfo158DTO(config);
        if (dto == null) {
            log.info("robotFollowUser158Task 结束，配置数据缺失");
            return CommonResult.fail("配置数据缺失");
        }
        Long time = dto.toInstant().toEpochMilli();
        List<Integer> times = dto.getTimes();
        List<Long> ids = dto.getIds();//未使用的机器人id
        List<Long> usedIds = dto.getUsedIds();//使用的机器人id
        Integer indexTime = times.get(0);
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime runTime = now.plusHours(indexTime);

        if (CollectionUtils.isEmpty(ids)) {
            log.info("robotFollowUser158Task 机器人不足，请及时添加");
            return CommonResult.fail("不够");
        }
        long epochSecond = runTime.toEpochSecond();
        System.out.println(epochSecond);
        //老用户关注
        if (runTime.isAfter(now) && runTime.isBefore(now.plusHours(1)) && !CollectionUtils.isEmpty(ids)) {
            Long lastId = 0L;
            List<Long> usedRobots = getOldUserIdAndFollowUser(ids, lastId);
            ids.removeAll(usedRobots);
            usedIds.addAll(usedRobots);
            times.remove(indexTime);
            dto.setIds(ids);
            dto.setTime(runTime.toEpochSecond());
            dto.setTimes(times);
            dto.setUsedIds(usedIds);
            config.setConfigValue(JsonUtil.writeString(dto));
            sysConfigService.updateConfig(config);
        }
        //机器人关注新人处理逻辑
//        dealNewUserWithRobotFollow(usedIds);
        return CommonResult.success("111");
    }

    private RobotUsedInfo158DTO getRobotUsedInfo158DTO(SysConfig config) {
        if (config == null) {
            return null;
        }
        if (StringUtil.isEmpty(config.getConfigValue())) {
            log.info("robotFollowUser158Task 结束，无配置数据");
            return null;
        }
        RobotUsedInfo158DTO dto = JsonUtil.readValue(config.getConfigValue(), RobotUsedInfo158DTO.class);
        List<Integer> times = dto.getTimes();
        List<Long> ids = dto.getIds();
        if (CollectionUtils.isEmpty(times) || CollectionUtils.isEmpty(ids)) {
            log.info("robotFollowUser158Task 结束，配置数据缺失");
            return null;
        }
        return dto;

    }

    /**
     * 获取老用户并关注
     *
     * @param robotIds
     * @param lastId   获取用户
     * @return
     */
    private List<Long> getOldUserIdAndFollowUser(List<Long> robotIds, Long lastId) {
        List<Long> userIds = getRealUserByLastId(lastId);
        //本次任务需要使用的机器人id
        List<Long> useRobot = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userIds)) {
            for (int i = 0; i < robotIds.size(); i++) {
                Random random = new Random();
                int randomNumber = random.nextInt(300) + 1; // 生成的随机数范围是（0, 300]
                useRobot.add(robotIds.get(i));
                //这个机器人关注的用户id
                ArrayList<Long> currentIds = new ArrayList<>();
                for (int j = 0; j < randomNumber; j++) {
                    if (j > userIds.size() - 1) {
                        break;
                    }
                    currentIds.add(userIds.get(j));
                }
                userFriendService.addList(robotIds.get(i), currentIds);
                userIds.removeAll(currentIds);
                if (CollectionUtils.isEmpty(userIds)) {
                    break;
                }
            }
        }
        return useRobot;
    }

    /**
     * 获取注册时间大于4个小时以上的真实用户id
     */
    private List<Long> getRealUserByLastId(Long lastId) {
        UserQuery query = UserQuery.builder().startUserId(lastId).isRobot(UserConstant.RoboTypeEnum.IS_ROBOT_0.getCode())
                .isTest(UserConstant.TestUserEnum.IS_TEST_0.getCode()).createTimeEnd(DateUtil.addHours(DateUtil.getNowDate(), 4)).build();
        List<ZnsUserEntity> entityList = znsUserService.findList(query);
//        .last("limit 100")
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList.stream().map(ZnsUserEntity::getId).collect(Collectors.toList());

    }


    @RequestMapping("/testPushRankPush")
    public Result testPushRankPush() {
//        ZnsRunActivityUserEntity activityUser = runActivityUserService.getById(13373578L);
//        ZnsRunActivityEntity activityEntity = runActivityService.getById(82853L);
//        List<Long> userIds = Arrays.asList(activityUser.getUserId());
//        String content = "Congratulations, you have achieved " + activityUser.getRank() + "th in " + activityEntity.getActivityTitle() + ", keep up the good work!";
//        MessageBo messageBo = new MessageBo();
//        messageBo.setTitle("Hello!" + activityUser.getNickname());
//        messageBo.setContent(content);
//        messageBo.setJumpType("5");
//        messageBo.setRouteType(1);
//        Map<String, Object> extras = new HashMap<>();
//
//        UserPushToken userToken = userPushTokenService.findByUserId(activityUser.getUserId());
//        if (StringUtils.hasText(userToken.getIosPushToken()) && !"-1".equals(userToken.getIosPushToken())) {
//            messageBo.setRouteValue("lznative://LeeRunRoute/detail");
//            extras.put("type", 1);
//            extras.put("detailId", activityUser.getRunDataDetailsId());
//        }
//        if (StringUtils.hasText(userToken.getAndroidPushToken()) && !"-1".equals(userToken.getAndroidPushToken())) {
//            messageBo.setRouteValue("lznative://main/runningreport");
//            extras.put("activityId", activityEntity.getId());
//            extras.put("REPORT_DETAIL_ID", activityUser.getRunDataDetailsId());
//            extras.put("REPORT_DETAIL_TYPE", activityEntity.getActivityType());
//        }
//        messageBo.setData(extras);
//        appMessageService.push(userIds, messageBo, "");
        return CommonResult.success();
    }

    private Random random = new Random(10);

    @GetMapping("/testUserFreeze")
    public Result testUserFreeze(@RequestParam(value = "seq") String seq) {
        try {
            int timeout = random.nextInt(3, 10);
            log.info("timeout ={}", timeout);
            TimeUnit.SECONDS.sleep(timeout);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return CommonResult.success(seq);
    }

    @PostMapping("/testUserFreeze")
    public Result testUserFreeze2(@RequestBody Map map) {
        try {
            int timeout = random.nextInt(3, 10);
            log.info("timeout ={}", timeout);
            TimeUnit.SECONDS.sleep(timeout);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return CommonResult.success(map);
    }

    @RequestMapping("/testSumIds")
    public Result testSumIds() {
        List<Long> detailIds = Lists.newArrayList();
        detailIds.add(22213389L);
        detailIds.add(22213375L);
        int sumTime = userRunDataDetailsService.sumUserTaskTimeByIds(detailIds);
        return CommonResult.success(sumTime);
    }

    @Autowired
    private UserWearsBagService userWearsBagService;

    /**
     * 发送用户时装道具
     *
     * @return
     */
    @RequestMapping("/testSendClothes")
    public Result testSendClothes(@RequestBody SendClothesReq sendClothesReq) {
        UserWearsBag userWearsBag = UserWearsBag.builder()
                .wearType(sendClothesReq.getWearType())
                .wearName(sendClothesReq.getWearName())
                .wearValue(sendClothesReq.getWearValue())
                .wearImageUrl(sendClothesReq.getWearImageUrl())
                .expiredTime(null)
                .isNew(0)
                .userId(sendClothesReq.getUserId())
                .activityId(sendClothesReq.getActivityId())
                .build();
        Integer aLong = userWearsBagService.insert(userWearsBag);
        return CommonResult.success(aLong);
    }

    @RequestMapping("/testCheatRule")
    public Result testCheatRule(Long detailsId, Long activityId) {
//        ZnsUserRunDataDetailsEntity userRunDataDetails = userRunDataDetailsService.getById(detailsId);
//        ZnsRunActivityEntity activityEntity = runActivityService.getById(activityId);
//        ActivityTypeDto activityTypeDto = runActivityService.getActivityNew(activityId);
//
//        //防作弊处理
//        activityStrategyContext.preventionCheatDeal(userRunDataDetails, activityTypeDto, null);
        return CommonResult.success();
    }


    @RequestMapping("/updateBagNull")
    public Result updateBagNull(@RequestBody BaseReq id) {
        UserWearsBag userWearsBag = userWearsBagService.findByById(id.getId());
        userWearsBag.setExpiredTime(null);
        userWearsBagService.update(userWearsBag);
        return CommonResult.success();
    }

    @RequestMapping("/sendUserWearMilepost")
    public Result sendUserWearMilepost(@RequestBody UserSendBagReq req) {
        MilepostWearAwardDto milepostWearsAwardDto = new MilepostWearAwardDto();
        milepostWearsAwardDto.setMilepost(BigDecimal.ZERO);
        BeanUtils.copyProperties(req, milepostWearsAwardDto);
        OfficialCumulativeRunDto officialCumulativeRunDto = new OfficialCumulativeRunDto();
        officialCumulativeRunDto.setUserId(req.getUserId());
        userWearsBizService.sendUserWearMilepost(milepostWearsAwardDto, officialCumulativeRunDto);
        return CommonResult.success();
    }

    @RequestMapping("/testPushGame")
    public Result testPushGame(@RequestBody BaseReq req) {
        ZnsRunActivityEntity activityEntity = znsRunActivityDao.selectActivityById(req.getId());
        if (RunActivityTypeEnum.OFFICIAL_TEAM_RUN.getType().equals(activityEntity.getActivityType())) {
            Map<String, Object> jsonObjectConfig = JsonUtil.readValue(activityEntity.getActivityConfig());
            List<Integer> runningGoals = JsonUtil.readList(jsonObjectConfig.get("runningGoals"), Integer.class);
            for (Integer runingGoal : runningGoals) {
                eggActivityBizService.eggActivityPush(activityEntity.getId(), activityEntity.getActivityRouteId(), runingGoal);
            }
        } else {
            eggActivityBizService.eggActivityPush(activityEntity.getId(), activityEntity.getActivityRouteId(), null);
        }
        return CommonResult.success();
    }

    @RequestMapping("/testAdd")
    public Result testAdd() {
        //路线缓存添加
        Object c = redisTemplate.opsForValue().get(RedisConstants.RUN_ROUTE_COUNT + 101);
        if (Objects.isNull(c)) {
            Integer count = runActivityService.getCountByRouteId(101L);
            redisTemplate.opsForValue().increment(RedisConstants.RUN_ROUTE_COUNT + 101, count);
        } else {
            redisTemplate.opsForValue().increment(RedisConstants.RUN_ROUTE_COUNT + 101);
        }
        System.out.println(c);
        Object o = redisTemplate.opsForValue().get(RedisConstants.RUN_ROUTE_COUNT + 101);
        System.out.println(Integer.valueOf((String) o));
        return CommonResult.success();
    }

    @RequestMapping("/testActivityEnd")
    public Result testActivityEnd(Long activityId) {
        MainActivity mainActivity = mainActivityService.findById(activityId);
        activityResultManager.activityEnd(mainActivity, true);
        return CommonResult.success();
    }

    @RequestMapping("/testRunEnd")
    public Result testRunEnd(Long id) {
        ZnsUserRunDataDetailsEntity details = userRunDataDetailsService.findById(id);
        ActivityTypeDto activityNew = runActivityService.getActivityNew(details.getActivityId());
        activityResultManager.runEnd(details, activityNew, true, false, true);
        return CommonResult.success();
    }

    @RequestMapping("/testLog")
    public Result testLog() {
        log.info("11111111111111");
        String spanId = MDC.get("spanId");
        String spanIdNew = UUID.randomUUID().toString().replace("-", "");
        MDC.put("spanId", spanId + "_" + spanIdNew);
        log.info("22222222222222");
        MDC.remove("spanId");
        log.info("33333333333333");
        return CommonResult.success();
    }

    @Autowired
    private UserYearTask userYearTask;

    @PostMapping("/userYearTask")
    public Result userYearTask() {
        userYearTask.userYearCountTask();
        return CommonResult.success();
    }

    @PostMapping("/testSes")
    public Result testSes(HttpServletRequest servletRequest) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = servletRequest.getReader();
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            stringBuilder.append(line);
        }

        String requestBody = stringBuilder.toString();
        log.info("request:" + requestBody);
        // 获取所有的请求参数
        Enumeration<String> parameterNames = servletRequest.getParameterNames();

        // 打印请求参数
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String[] paramValues = servletRequest.getParameterValues(paramName);

            // 对于每个参数，打印参数名和参数值
            for (String paramValue : paramValues) {
                log.info("参数名: " + paramName + ", 参数值: " + paramValue);
            }
        }
        return CommonResult.success(requestBody);
    }

    @PostMapping("/userIdGet")
    public Result<ZnsUserEntity> userIdGet(@RequestBody UserRequest req) {
        ZnsUserEntity user = userService.testEmailAddressEnWhereAndOrderByEmailAddressEn(req.getEmailAddress());
        return CommonResult.success(user);
    }

    @PostMapping("/userIdSave")
    public Result<ZnsUserEntity> userIdSave(@RequestBody UserRequest req) {
        userService.createUserOrUpdate(req, 3500);
        return CommonResult.success();
    }

    @PostMapping("/userIdUpdate")
    public Result<ZnsUserEntity> userIdUpdate(@RequestBody BaseReq req) {
        ZnsUserEntity user = userService.findById(req.getId());
        userService.update(user);
        return CommonResult.success();
    }

    @Autowired
    private BatchEncryptTask batchEncryptTask;

    @PostMapping("/batchEncrypt")
    public Result batchEncrypt(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/batchEncrypt1")
    public Result batchEncrypt1(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run1(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/batchEncrypt2")
    public Result batchEncrypt2(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run2(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/batchEncrypt3")
    public Result batchEncrypt3(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run3(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/batchEncrypt4")
    public Result batchEncrypt4(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run4(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/batchEncrypt5")
    public Result batchEncrypt5(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run5(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/batchEncrypt6")
    public Result batchEncrypt6(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run6(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @PostMapping("/cacheTest")
    public void setCacheTest() {
        ZnsUserEntity znsUserEntity = new ZnsUserEntity();
        znsUserEntity.setId(1L);
        redissonClient.getBucket("A").set(znsUserEntity);
    }

    @PostMapping("/batchEncrypt7")
    public Result batchEncrypt7(@RequestBody @Validated UserEnReq req) {
        batchEncryptTask.run7(req.getStartId(), req.getEndId());
        return CommonResult.success();
    }

    @Autowired
    private RoomDismissTask roomDismissTask;

    @PostMapping("/roomDismissTask")
    public Result roomDismissTask(@RequestBody @Validated UserEnReq req) {
        roomDismissTask.dismissRoom();
        return CommonResult.success();
    }

    @Autowired
    private RoomOrderStartTask roomOrderStartTask;

    @PostMapping("/roomOrderStartTask")
    public Result roomOrderStartTask(@RequestBody @Validated UserEnReq req) {
        roomOrderStartTask.roomOrderStartRoom();
        return CommonResult.success();
    }

    @PostMapping("/testFirebaseUtils")
    public void testFirebaseUtils(String token) {
        Map<String, String> map = new HashMap<>();
        map.put("jumpType", "5");
        map.put("jumpImage", "jumpImage");
        map.put("jumpValue", "jumpValue");

        FirebaseUtils.pushSingle("pitpat", "测试123456", token, "Activity notification",
                map, "dev", new ArrayList<>(), "", "QUEUE_PUSH_DELIVER_tes");
    }

    @PostMapping("/testError")
    public void testError(Integer num) {
        if (num == 1) {
            throw new RuntimeException("测试异常");
        }
        if (num == 2) {
            throw new BaseException("测试异常");
        }

        RLock lock = redissonClient.getLock(NanoId.randomNanoId());
        try {
            boolean lock1 = lock.tryLock(1, 500, TimeUnit.SECONDS);
            if (lock1) {
                if (num < 10) {
                    TimeUnit.SECONDS.sleep(60);
                }
            } else {
                throw new BaseException("not get locked");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    @RequestMapping("add-robot")
    public Result<Map> addRobot(Long userid, Long roomid, Long roadid, String firstname, String lastname, Integer gender, String avatar) {

        log.info("{},{},{},{},{},{},{}", userid, roomid, roadid, firstname, lastname, gender, avatar);

        return CommonResult.success(Map.of("userId", userid, "roomid", roomid, "roadid", roadid, "firstname", firstname, "lastname", lastname, "gender", gender, "avatar", avatar));
    }

    @RequestMapping("setBitRoom")
    public Result setBitRoom(Long userId) {
        redisTemplate.opsForValue().setBit(RedisConstants.APP_ROOM_ONLINE_USERS, userId, true);
        return CommonResult.success();
    }

    @RequestMapping("removeBitRoom")
    public Result removeBitRoom(Long userId) {
        redisTemplate.opsForValue().setBit(RedisConstants.APP_ROOM_ONLINE_USERS, userId, false);
        return CommonResult.success();
    }

    @RequestMapping("runNpcTask")
    public Result runNpcTask() {
        userRaceCreateTask.runNpcTask();
        return CommonResult.success();
    }

    @RequestMapping("runTask")
    public Result runTask() {
        userRaceCreateTask.runTask();
        return CommonResult.success();
    }

    @RequestMapping("/npc/pic")
    public Result updateNpcPic() {
        String config = sysConfigService.selectConfigByKey("visual_user_activity_npc_config");
        List<Long> userIds = JsonUtil.readList(config, Long.class);
        if (CollectionUtils.isEmpty(userIds)) {
            return CommonResult.fail("未配置npc用户");
        }
        List<ZnsUserEntity> userList = userService.findByIds(userIds);

        List<RotPic> femaleList = rotPicService.findListByQuery(RotPicQuery.builder().keyWord("woman").useTime(0).lastSql("limit 100").build());
        List<RotPic> maleList = rotPicService.findListByQuery(RotPicQuery.builder().keyWord("man").useTime(0).lastSql("limit 100").build());

        AtomicInteger femaleCounter = new AtomicInteger();

        userList.stream().filter(item -> Objects.equals(UserGenderEnum.female.getCode(), item.getGender())).forEach(item -> {
            ZnsUserEntity user = new ZnsUserEntity();
            user.setId(item.getId());
            user.setHeadPortrait(femaleList.get(femaleCounter.getAndIncrement()).getPic());
            userService.update(user);
        });

        AtomicInteger maleCounter = new AtomicInteger();
        userList.stream().filter(item -> Objects.equals(UserGenderEnum.male.getCode(), item.getGender())).forEach(item -> {
            ZnsUserEntity user = new ZnsUserEntity();
            user.setId(item.getId());
            user.setHeadPortrait(maleList.get(maleCounter.getAndIncrement()).getPic());
            userService.update(user);
        });

        log.info("femaleList pic={}", femaleList);
        log.info("maleList pic={}", maleList);

        List<RotPic> list = Stream.concat(femaleList.stream(), maleList.stream()).map(item -> {
            RotPic rotPic = new RotPic();
            rotPic.setId(item.getId());
            rotPic.setUseTime(1);
            return rotPic;
        }).toList();

        rotPicService.updateBatch(list, 200);
        return CommonResult.success();
    }

    /**
     * 重新获取rainmaker信息
     *
     * @return
     */
    @RequestMapping("/customLogin")
    public String customLogin(@RequestParam String email) {
        //@pitpat.virtualemail
        //@virtual.email
        Map<String, Object> object = rainmakerService.customLogin(email);
        return CommonResult.success(object).toString();
    }

    @RequestMapping("rainmaker/batch")
    public Result batchCreateRaimakerUserId() {
        rainMakerBatchCreateTask.init();
        return CommonResult.success();
    }

    @Autowired
    private RoomNpcCreateAndDismissTask runNpcCreateAndDismissTask;


    @RequestMapping("/runNpcCreateAndDismissTask")
    public Result runNpcCreateAndDismissTask() {
        runNpcCreateAndDismissTask.runNpcCreateAndDismissTask();
        return CommonResult.success();
    }

    @RequestMapping("/dismissAllNpcRoom")
    public Result dismissAllNpcRoom() {
        runNpcCreateAndDismissTask.dismissAllNpcRoom();
        return CommonResult.success();
    }

    @Autowired
    private MessageSource messageSource;

    @RequestMapping("/i18n")
    public void getI18n() {
        Arrays.stream(I18nConstant.LanguageCodeEnum.VALUES).forEach(item -> {
            String message = I18nMsgUtils.getLangMessage(item.getCode(), "activity.notify.remark.ActivityNotificationEnum.USER_DATA_BROADCASTING", 45, 22, 18);
            log.info("{}-> {}", item.getCode(), message);
        });

        Arrays.stream(I18nConstant.LanguageCodeEnum.VALUES).forEach(item -> {
            String[] arr = item.getCode().split("_");
            String message = messageSource.getMessage("activity.notify.remark.ActivityNotificationEnum.USER_DATA_BROADCASTING", new Object[]{2.5f, 2, 6f, 2.7f},
                    new Locale(arr[0], arr[1]));
            log.info("{}-> {}", item.getCode(), message);
        });


    }

    /**
     * 使用阿里云的 redis ,这段代码未按照预期执行
     * 使用本地的redis 符合预期，
     *
     * @param sleep
     * @return
     * @throws InterruptedException
     */
    @RequestMapping("/lock")
    public Result testLock(@RequestParam Boolean sleep) throws InterruptedException {
        RLock lock = redissonClient.getLock("lock");
        //如果请求本身比较耗时， 那么增加 等待时间将使得 后续请求会执行成功
        //比如 这里模拟的耗时3秒， 如果等待时间小于3秒，那么后续请求将无法获取到锁，因为锁已经被释放了
        try {
            if (lock.tryLock(1, TimeUnit.SECONDS)) {
                if (sleep) {
                    TimeUnit.SECONDS.sleep(5);
                }
                log.info("获取锁成功");
                return CommonResult.success("获取锁成功");
            } else {
                throw new BaseException("Lock Fail, Wait Timeout,key=" + lock.getName() + ", tName={}" + Thread.currentThread().getName());
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("手动释放锁🔒😻----000-, lock={}", lock.isLocked());
                lock.unlock();
            }
        }
    }

    @RequestMapping("/rlock")
    public void testRlock() throws InterruptedException {
        String key = "LVWMd8LmnedITUhH2G5uZ";//NanoId.randomNanoId();
        RLock lock = redissonClient.getLock(key);
        lock.tryLock(); //scheduleExpirationRenewal(threadId);
        //LockHolder.tryLock(lock,()->{});

        String key1 = "LVWMd8LmnedITUhH2G5uZ1";//NanoId.randomNanoId();
        RLock lock1 = redissonClient.getLock(key1);
        lock1.tryLock(60, TimeUnit.SECONDS);//scheduleExpirationRenewal(threadId);
        //LockHolder.tryLock(lock1,60,()->{});

        String key2 = "LVWMd8LmnedITUhH2G5uZ2";//NanoId.randomNanoId();
        RLock lock2 = redissonClient.getLock(key2);
        lock2.tryLock(10, 30, TimeUnit.SECONDS);
        //LockHolder.tryLock(lock2,10,30,()->{});

        String key3 = "LVWMd8LmnedITUhH2G5uZ3";//NanoId.randomNanoId();
        RLock lock3 = redissonClient.getLock(key3);
        lock3.tryLock(10, -1, TimeUnit.SECONDS);
        //LockHolder.tryLock(lock3,10,-1,()->{});
    }

    /**
     * 测试 app 日期返回值
     *
     * @return
     */
    @GetMapping("/date")
    public Result testDateTime(@RequestParam(required = false) ZonedDateTime date,
                               @RequestParam(required = false) ZonedDateTime zonedDateTime, @RequestParam(required = false) Instant instant) {
        DateTimeDto dateTimeDto = new DateTimeDto(date,  zonedDateTime);
        log.info("instant,{}", instant);
        log.info("dateTimeDto:{}", dateTimeDto);
        return CommonResult.success(dateTimeDto);
    }

    /**
     * 测试 app 日期返回值
     *
     * @return
     */
    @PostMapping("/date")
    public Result postDateTime(@RequestBody DateTimeDto dto) {
        //return CommonResult.success(DateTimeDto.of());
        return CommonResult.success(dto);
    }

    record DateTimeDto(
            ZonedDateTime date,
            ZonedDateTime zonedDateTime
    ) {
        public static DateTimeDto of() {
            return new DateTimeDto(ZonedDateTime.now(), ZonedDateTime.now());
        }
    }

    @RequestMapping("/translate/number")
    public Result<List<RunningLevelVo>> translateNumber() {
        RunningLevelVo runningLevelVo = new RunningLevelVo();
        BigDecimal decimal = new BigDecimal("0.5532");
        runningLevelVo.setCapabilityValue(decimal);
        runningLevelVo.setChangeRate(decimal);
        runningLevelVo.setExceedRate(decimal);
        runningLevelVo.setBeforeCapabilityValue(decimal);
        //NumberAware.processFields(runningLevelVo);
        return CommonResult.success(List.of(runningLevelVo));
    }

    @Autowired
    private JdbcTemplate jdbcTemplate;


    @RequestMapping("/queryOrderUsers")
    public Result<List<Long>> createUserDetail() {
        String sql = " SELECT DISTINCT(o.user_id) from zns_order o INNER JOIN zns_user u on o.user_id = u.id and u.is_robot=0 and u.is_delete=0 and o.source_type = 3   where `status` in (1, 3, 4, 6) or (status = 5 and close_type = 2) and o.source_type = 3 ";

        List<Long> userIds = jdbcTemplate.query(sql, (rs, rowNum) -> rs.getLong("user_id"));
        List<UserDetailDo> userDetailList = userDetailService.findByUserIds(userIds);
        Map<Long, UserDetailDo> userDetailMap = userDetailList.stream().collect(Collectors.toMap(UserDetailDo::getUserId, Function.identity()));

        userIds.forEach(userId -> {
            UserDetailDo userDetail = userDetailMap.get(userId);
            if (Objects.isNull(userDetail)) {
                userDetail = new UserDetailDo();
                userDetail.setUserId(userId);
                userDetail.setMallPurchaseFlag(1);//已经购买了
                userDetailService.insert(userDetail);
                log.info("UserDetail is null, create, detail={},{}", userDetail.getUserId(), userDetail.getMallPurchaseFlag());
            } else {
                userDetail.setMallPurchaseFlag(1);//已经购买了
                userDetailService.update(userDetail);
                log.info("UserDetail existed, update, detail={},{}", userDetail.getUserId(), userDetail.getMallPurchaseFlag());
            }
        });

        return CommonResult.success(userIds);
    }

    /**
     * 导出指定时间范围内的数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/export/csv")
    public ResponseEntity<byte[]> exportToCsv(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        byte[] csvFile = generateCsv(startDate, endDate);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment; filename=" + startDate + "_equipment_register.csv");
        return ResponseEntity.ok().headers(headers).contentType(MediaType.parseMediaType("application/csv")).body(csvFile);
    }

    public byte[] generateCsv(String startDateStr, String endDateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime startDate = null, endDate = null;
        try {
            startDate = sdf.parse(startDateStr + " 00:00:00");
            endDate = sdf.parse(endDateStr + " 23:59:59");
        } catch (Exception e) {
            // Handle parsing error
        }

        Timestamp startTimestamp = new Timestamp(startDate.toInstant().toEpochMilli());
        Timestamp endTimestamp = new Timestamp(endDate.toInstant().toEpochMilli());

        String query = "SELECT DATE(date_add(u.create_time, interval 8 hour)) AS 注册北京日期, t.product_code AS 型号, pb.supplier AS 工厂名称, t.batch_number AS 批次号, u.email_address AS 用户邮箱, t.print_id AS 设备码, t.brand AS 品牌 FROM zns_user_equipment eq JOIN (SELECT equipment_id, MAX(id) AS max_id FROM zns_user_equipment GROUP BY equipment_id) ue2 ON eq.equipment_id = ue2.equipment_id AND eq.id = ue2.max_id LEFT JOIN zns_user u ON u.id = eq.user_id LEFT JOIN zns_treadmill t ON t.id = eq.equipment_id LEFT JOIN zns_equipment_production_batch pb ON pb.batch_number = t.batch_number WHERE date_add(u.create_time, interval 8 hour) > ? AND date_add(u.create_time, interval 8 hour) < ? ORDER BY u.create_time DESC, t.product_code ASC";

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (OutputStreamWriter writer = new OutputStreamWriter(outputStream, "UTF-8")) {
            writer.write("注册北京日期,型号,工厂名称,批次号,用户邮箱,设备码,品牌\n");
            jdbcTemplate.query(query, (rs, rowNum) -> {
                try {
                    writer.write(rs.getString("注册北京日期") + "," + rs.getString("型号") + "," + rs.getString("工厂名称") + "," + rs.getString("批次号") + "," + rs.getString("用户邮箱") + "," + rs.getString("设备码") + "," + rs.getString("品牌") + "\n");
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                return null;
            }, startTimestamp, endTimestamp);
            writer.flush();
        } catch (Exception e) {
            // Handle exception
        }

        return outputStream.toByteArray();
    }

    @Resource
    private TurbolinkAwardConverter turbolinkAwardConverter;

    /**
     * 给用户补发重复完成奖励
     *
     * @param dto
     */
    @PostMapping("/turoblink/sendAward")
    public void turbolinkCampaignUserManager(@RequestBody TurbolinkAwardCallbackDto dto) {
        TurbolinkSendAwardDto awardDto = turbolinkAwardConverter.convert(dto);
        turbolinkCampaignUserManager.doSendAward(awardDto.getUserId(), awardDto.getTurbolinkAward());
    }

    //@PostMapping("/import")
    //public Result importUsers(@RequestParam("file") MultipartFile file) {
    //    // 1. 检查文件类型
    //    String filename = file.getOriginalFilename();
    //    if (filename == null || (!filename.endsWith(".xlsx") && !filename.endsWith(".xls"))) {
    //        return CommonResult.fail("请上传Excel文件");
    //    }
    //
    //    try {
    //        // 2. 解析Excel
    //        List<UserImportDO> userList = ExcelUtils.parseExcel(file, UserImportDO.class);
    //
    //        // 3. 验证数据
    //        List<String> errors = ExcelValidator.validate(userList);
    //        if (!errors.isEmpty()) {
    //            return CommonResult.fail("数据验证失败：" + String.join("; ", errors));
    //        }
    //
    //        // 4. 处理导入数据
    //        log.info("开始导入{}条用户数据", userList.size());
    //        for (UserImportDO user : userList) {
    //            log.info("导入用户数据: {}", user);
    //            // TODO: 调用service处理数据保存
    //        }
    //
    //        return CommonResult.success("成功导入" + userList.size() + "条数据");
    //
    //    } catch (Exception e) {
    //        log.error("用户数据导入失败", e);
    //        return CommonResult.fail("导入失败：" + e.getMessage());
    //    }
    //}
    //
    //
    //@Data
    //public static class UserImportDO {
    //
    //    @ExcelColumn(value = "用户ID")
    //    private Long id;
    //
    //    @ExcelColumn(value = "用户名", required = true)
    //    private String username;
    //
    //    @ExcelColumn(value = "邮箱", required = true)
    //    private String email;
    //
    //    @ExcelColumn(value = "年龄")
    //    private Integer age;
    //
    //    @ExcelColumn(value = "积分")
    //    private BigDecimal score;
    //
    //    @ExcelColumn(value = "注册时间", dateFormat = "yyyy-MM-dd")
    //    private ZonedDateTime registerTime;
    //
    //    @ExcelColumn(value = "是否启用")
    //    private Boolean enabled;
    //
    //    @ExcelColumn(value = "身高")
    //    private Double height;
    //
    //    @ExcelColumn(value = "体重")
    //    private Float weight;
    //
    //    @ExcelColumn(value = "会员等级")
    //    private Short memberLevel;
    //
    //    @ExcelColumn(value = "登录次数")
    //    private int loginCount;
    //
    //    @ExcelColumn(value = "成长值")
    //    private long growthValue;
    //
    //    @ExcelColumn(value = "是否在线")
    //    private boolean online;
    //
    //    @ExcelColumn(value = "余额")
    //    private double balance;
    //
    //    @ExcelColumn(value = "经验值")
    //    private float experience;
    //
    //    @ExcelColumn(value = "等级")
    //    private short level;
    //}


    /**
     * 投放机器人延迟队列
     */
    @Value("${" + RabbitQueueConstants.PROP_RANK_MATCH_PUT_IN_ROBOT_DELAY_EXCHANGE_V2 + "}")
    private String propRankMatchPutInRobotDelayExchange;

    /**
     * 投放机器人延迟队列
     */
    @Value("${" + RabbitQueueConstants.RANK_MATCH_PUT_IN_ROBOT_DELAY_EXCHANGE_V2 + "}")
    private String rankMatchPutInRobotDelayExchange;

    @Value("${" + RabbitQueueConstant.TRACE_LOG_EXCHANGE + "}")
    private String traceLogExchangeName;

    @GetMapping("/produceMqMsg")
    public void produceMq() {
        //延迟1分钟
        IntStream.range(0, 2).forEach(i -> {
            rabbitTemplate.convertAndSend(propRankMatchPutInRobotDelayExchange, RabbitQueueConstants.PROP_RANK_MATCH_PUT_IN_ROBOT_DELAY_KEY, i, message -> {
                message.getMessageProperties().setDelay(3000);// 毫秒为单位，指定此消息的延时时长,
                return message;
            });
        });


        IntStream.range(0, 2).forEach(i -> {
            rabbitTemplate.convertAndSend(rankMatchPutInRobotDelayExchange, RabbitQueueConstants.RANK_MATCH_PUT_IN_ROBOT_DELAY_KEY, i, message -> {
                message.getMessageProperties().setDelay(3000);// 毫秒为单位，指定此消息的延时时长
                return message;
            });
        });
    }

    @Autowired
    private OpenAiProperties openAiProperties;

    @GetMapping("/openAi/billing")
    public Result<Map> testOpenAi() {
        //可以每天获取一次，累计到本地，每周或每月在钉钉群通知，当超过阈值也钉钉通知
        // https://platform.openai.com/docs/api-reference/usage/costs_object
        long epochSecond = LocalDate.of(2025, 1, 8).toEpochSecond(LocalTime.MIN, ZoneOffset.UTC);//开始使用
        String response = RestTemplateUtil.get("https://api.openai.com/v1/organization/costs?start_time=1735660800&limit=20", Map.of(
                "Authorization", "Bearer *************************************************************************************************************************************",
                "Accept", "application/json"
        ));
        return CommonResult.success(JsonUtil.readValue(response));
    }

    @Autowired
    private RunEndActivityManager runEndActivityManager;

    @GetMapping("riskTimeout")
    public Result riskTimeout() {
        String message = "{\"userRunDataDetail\":{\"id\":24524994,\"isDelete\":0,\"createTime\":1740071256000,\"modifieTime\":1740071372000,\"userId\":95477,\"uniqueCode\":\"202\",\"treadmillId\":11646,\"runStatus\":1,\"kilocalorie\":19.00,\"fatConsumption\":2,\"runMileage\":234.00,\"climbingMileage\":0.00,\"runTime\":116,\"averagePace\":497,\"averageVelocity\":7.23,\"averageStepFrequency\":0,\"averageStride\":0,\"stepNum\":0,\"averageHeartRate\":0,\"type\":0,\"maxHeartRate\":0,\"maxPace\":450,\"maxStepFrequency\":0,\"lastTime\":1740071372000,\"reportCopywriting\":\"Fall in love with the process and the results will come.\",\"capabilityValue\":53.60,\"distanceTarget\":500.00,\"timeTarget\":0,\"calorieTarget\":41440,\"warmUpHeartRateTime\":0,\"fatBurningHeartRateTime\":0,\"aerobicHeartRateTime\":0,\"anaerobicHeartRateTime\":0,\"limitHeartRateTime\":0,\"routeId\":1401,\"activityId\":417738,\"subActivityId\":0,\"courseId\":0,\"taskId\":0,\"dataSource\":0,\"zoneId\":\"Asia/Shanghai\",\"ipAddr\":\"**************\",\"appVersion\":4048,\"isCheat\":0,\"cheatRate\":0.00,\"runTimeMillisecond\":116457,\"firmwareVersion\":23,\"isTest\":0,\"isRobot\":0,\"activityType\":18,\"unActivityType\":0,\"runType\":2,\"runMileageRanking\":125,\"mileageExceedPercentage\":0.00,\"isCountMilestone\":-1,\"heartRateDeviceType\":0,\"isCompensated\":0,\"trainingId\":0,\"deviceType\":0,\"averageTreadFrequency\":0.00},\"activityTypeDto\":{\"id\":417738,\"activityType\":18,\"mainType\":\"prop\",\"activityTitle\":\"Lucky Dash\",\"activityStartTime\":1740071252000,\"activityEndTime\":1740073112000,\"activityState\":1,\"waitTime\":0,\"timeStyle\":0}}";
        runEndActivityManager.riskTimeout(JsonUtil.readValue(message, RunEndActivityVo.class));
        return CommonResult.success();
    }

    @Autowired
    private ClubMemberService clubMemberService;
    @Resource
    private VipUserService vipUserService;
    @Resource
    private UserThirdOauthService userThirdOauthService;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ShopifyUserService shopifyUserService;
    @Resource
    private UserEquipmentShareManager userEquipmentShareManager;
    @Resource
    private UserRunOptimalRecordService userRunOptimalRecordService;


    /**
     * 直接用户注销,忽略账户金额
     *
     * @param userId
     * @return
     */
    @GetMapping("/logoff")
    public Result logoff(Long userId) {
        ZnsUserEntity userEntity = userService.findByIdWithoutLogicDelete(userId);
        if (Objects.isNull(userEntity)) {
            return CommonResult.fail("用户不存在");
        }
        if (userEntity.getIsDelete() == 1) {
            return CommonResult.fail("当前用户已注销");
        }

        List<ClubMember> clubMemberList = clubMemberService.findListByQuery(ClubMemberQuery.builder().userId(userId).build());
        if (!CollectionUtils.isEmpty(clubMemberList)) {
            if (clubMemberList.stream()
                    .anyMatch(item ->
                            ClubMemberRoleEnum.OWNER.getCode().equals(item.getRole()))) {
                return CommonResult.fail("存在俱乐部数据，请退出俱乐部后再注销账号。");
            }
            List<Long> clubIds = clubMemberList.stream().map(ClubMember::getId).toList();
            log.info("quit club, ids={} ", clubIds);
            clubIds.forEach(id -> {
                clubMemberService.deleteById(id);
            });
        }

        // 查询用户是否在会员池中
        List<VipUser> vipUserList = vipUserService.selectListByEmail(userEntity.getEmailAddressEn());
        if (!CollectionUtils.isEmpty(vipUserList)) {
            for (VipUser item : vipUserList) {
                item.setEmailStatus(EmailStatusEnum.CANCELLED.getCode());
                item.setVipStatus(VipStatusUserEnum.EXPIRED.getCode());
            }
            vipUserService.updateBatchById(vipUserList);
        }

        userService.deleteById(userEntity.getId());
        //删除im账户
        tencentImUtil.accountDelete(Arrays.asList(String.valueOf(userId)));
        ZnsUserEntity user = userService.findById(userId);
        if (Objects.nonNull(user)) {
            //退出登录
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            redisUtil.delete(key);
        }
        //删除三方账户
        userThirdOauthService.deleteUserThirdOauth(userEntity.getId());

        //socket断开连接
        socketPushUtils.deleteUser(userEntity.getEmailAddressEn());

        //注销用户-更新shopify
        CompletableFuture.runAsync(() -> shopifyUserService.deleteShopifyUser(userEntity.getId()))
                .exceptionally(ex -> {
                    // 处理 CompletableFuture 执行过程中的异常
                    log.error("注销用户-更新shopify出现异常", ex);
                    return null;
                });
        //用户注销分享设备移除
        CompletableFuture.runAsync(() -> userEquipmentShareManager.userEquipmentShareBinderRemove(userEntity.getId()), taskExecutor)
                .exceptionally(ex -> {
                    // 处理 CompletableFuture 执行过程中的异常
                    log.error("用户注销分享设备移除过程中出现异常", ex);
                    return null;
                });
        //用户最佳挑战成绩delete
        CompletableFuture.runAsync(() -> userRunOptimalRecordService.deleteByUserId(userEntity.getId()), taskExecutor)
                .exceptionally(ex -> {
                    // 处理 CompletableFuture 执行过程中的异常
                    log.error("用户注销最佳挑战成绩移除过程中出现异常", ex);
                    return null;
                });
        return CommonResult.success();
    }

    @PostMapping("/addUserExp")
    public void addUserExp(@RequestBody AddUserExpRequest request) {
        if (request.getSign().equals("Xxx12311!1231!1")) {
            userExpLevelBizService.sendUserExpAndAward(request.getUserId(), request.getExp());
        }
    }

    @Resource
    private UserExtraService userExtraService;


    @Autowired
    private UserRewardAccountExpiredTask userRewardAccountExpiredTask;

    @PostMapping("/userRewardAccountExpiredTask")
    public void userRewardAccountExpiredTask() {
        userRewardAccountExpiredTask.processExpiredRewards();
    }

    @Resource
    private ActivityRunRankTempService activityRunRankTempService;

    @PostMapping("/activityPushTest/{activityid}/{tempId}")
    public void rankChangePush(@PathVariable("activityid") Long activityid,
                               @PathVariable("tempId") Long tempId) {
        if (tempId == 0) {

            userRankChangePushBizService.sendUserRankChangePush(activityid, ZonedDateTime.now());
            return;
        }

        ActivityRunRankTempDo byId = activityRunRankTempService.findById(tempId);
        activityUserAwardCalculator.startCalculate(activityid, byId);
    }


    @PostMapping("/sendPush/{tempId}")
    public void sendPush(@PathVariable("tempId") Long tempId) {
        userRankChangePushBizService.sendPush(tempId);
    }

    @Resource
    private ProActivityTempRankCalTask rankCalTask;

    @PostMapping("/calTempTask")
    public void calTempTask() {
        rankCalTask.calTempRank();
    }
    @Resource
    private UserTaskManager userTaskManager;

    /**
     * 手动完成新人关联任务任务
     *
     * @param userId
     */
    @PostMapping("/completeRelatedTask/{userId}")
    public void completeRelatedTask(@PathVariable("userId") Long userId) {
        ZnsUserEntity loginUser = userService.findById(userId);
        userTaskManager.completeRelatedTask(loginUser, 1);
    }

    @Resource
    private MallOrderManager mallOrderManager;
    @Resource
    private ZnsOrderService orderService;

    /**
     * 取消订单
     */
    @PostMapping("/order/cancel/excludeErp")
    @Transactional(rollbackFor = Throwable.class)
    public Result<Boolean> cancel(@Validated @RequestBody OrderCancelReasonReq req) {
        Long orderId = req.getOrderId();
        String cancelReason = req.getCancelReason();

        //查询原订单
        ZnsOrderEntity orderEntity = orderService.findById(orderId);
        if (PayConstant.PayTypeEnum.ZERO_PAY.getType().equals(orderEntity.getPayType())) {
            //0元购订单不让取消
            throw new BaseException(I18nMsgUtils.getMessage("order.cannel.zero"));
        }
        if (OrderStatusEnum.NEW.getStatus().equals(orderEntity.getStatus())) {
            mallOrderBizService.closeOrder(orderId, OrderConstant.CloseTypeEnum.CLOSE_TYPE_1);
        }
        if (OrderStatusEnum.WAIT_SEND.getStatus().equals(orderEntity.getStatus())) {
            //待发货需要通知erp并退款,先生成退款单
            boolean b = mallOrderManager.doRefundExcludeErp(orderEntity);
            if (!b) {
                log.info("[cancel]---取消订单,orderId={},生成退款单失败", orderId);
                throw new BaseException(I18nMsgUtils.getMessage("order.cancel.fail"));
            }
        }
        //更新订单取消原因
        ZnsOrderEntity updateEntity = new ZnsOrderEntity(orderId);
        updateEntity.setCancelReason(cancelReason);
        orderService.update(updateEntity);

        //用户取消订单，钉钉通知
        mallOrderBizService.orderCancelMsg(orderId);
        return CommonResult.success(true);
    }

    @Resource
    private DingTalkService dingTalkService;

    @GetMapping("dingtalk/dingMsg")
    public Result<String> sendDingMsg(@RequestParam String content, @RequestParam List<String> userIdList) {
        dingTalkService.sendDingMsg(content, 1, userIdList);
        return CommonResult.success();
    }

    @GetMapping("dingtalk/userId")
    public Result<String> getUserId(@RequestParam String mobile) {
        String userId = dingTalkService.getUserId(mobile);
        return CommonResult.success(userId);
    }

    @PostMapping("/calcPlacementScore")
    public Result<Void> calcPlacementScore(@RequestBody CalcPlacementScoreReqDto reqDto) {
        Long activityId = reqDto.getActivityId();
        MainActivity mainActivity = mainActivityService.findById(activityId);
        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.findActivityUser(activityId, reqDto.getUserId());
        activityResultManager.calcPlacementScoreAndSaveUserPlacementScore(mainActivity, znsRunActivityUserEntity);
        return CommonResult.success();
    }

    @Resource
    private SpeedCalcV2Task speedCalcV2Task;

    @PostMapping("/speedCalcV2Task")
    public Result<Void> speedCalcV2Task() {
        speedCalcV2Task.run();
        return CommonResult.success();
    }

}

