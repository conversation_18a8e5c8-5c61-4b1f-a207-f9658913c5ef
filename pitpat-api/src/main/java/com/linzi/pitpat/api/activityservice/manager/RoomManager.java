package com.linzi.pitpat.api.activityservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.activityservice.converter.RoomConfigConverter;
import com.linzi.pitpat.api.activityservice.converter.RoomConverter;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.DingTalkTokenEnum;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.RandomUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.biz.RoomUserDataBiz;
import com.linzi.pitpat.data.activityservice.constant.enums.CreateTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomConfigTicketTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomCreateType;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomTicketStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RoomTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.RoomTipAndUrlDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomCheckQueryDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomCreateRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomHandleAcceptDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomInviteGroupUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomInviteUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomPayTicketDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomQueryDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomTicketBaseDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomTicketUseDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.RoomUpdateRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.ImParamDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.RoomEventRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomCheckCompanionResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomCheckPassEnterResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomCheckResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomIndexResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomInviteStatusQueryResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomJoinResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomMemberResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomMyRaceResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomPayTicketResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomRandomResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomRoomActivityResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomTagDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomTicketCheckResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RoomTicketRefundResponseDto;
import com.linzi.pitpat.data.activityservice.model.dto.RoomCombineDto;
import com.linzi.pitpat.data.activityservice.model.entity.CompanionUserDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.RoomActivityExtInfoDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomBookingDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomConfigDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomInviteRecordDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomModeRouteI18nDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomParticipantsDo;
import com.linzi.pitpat.data.activityservice.model.entity.RoomTicketLogDo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.query.CompanionUserQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomActivityExtInfoQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomBookingQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomInviteRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomModeRouteI18nQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomPageQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomParticipantsQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomQuery;
import com.linzi.pitpat.data.activityservice.model.query.RoomTicketLogQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.activityservice.model.vo.RoomModeDto;
import com.linzi.pitpat.data.activityservice.model.vo.RoomPropRouteConfigDto;
import com.linzi.pitpat.data.activityservice.model.vo.RoomRouteDto;
import com.linzi.pitpat.data.activityservice.model.vo.RoomRouteModeConfigVo;
import com.linzi.pitpat.data.activityservice.model.vo.VisualUserActivityConfigVo;
import com.linzi.pitpat.data.activityservice.service.CompanionUserService;
import com.linzi.pitpat.data.activityservice.service.RoomActivityExtInfoService;
import com.linzi.pitpat.data.activityservice.service.RoomBookingService;
import com.linzi.pitpat.data.activityservice.service.RoomConfigService;
import com.linzi.pitpat.data.activityservice.service.RoomInviteRecordService;
import com.linzi.pitpat.data.activityservice.service.RoomModeRouteI18nService;
import com.linzi.pitpat.data.activityservice.service.RoomParticipantsService;
import com.linzi.pitpat.data.activityservice.service.RoomService;
import com.linzi.pitpat.data.activityservice.service.RoomTicketLogService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.activityservice.strategy.room.RoomEventStrategy;
import com.linzi.pitpat.data.activityservice.strategy.room.RoomEventStrategyFactory;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.clubservice.manager.ClubPushManager;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.entity.ClubActivityRoomRelationDo;
import com.linzi.pitpat.data.clubservice.model.query.ClubActivityRoomRelationQuery;
import com.linzi.pitpat.data.clubservice.service.ClubActivityRoomRelationService;
import com.linzi.pitpat.data.clubservice.service.ClubMemberService;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.CompanionStatusEnum;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.SocketEventEnums;
import com.linzi.pitpat.data.equipmentservice.biz.UserEquipmentBizService;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.enums.SocketEventEnum;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.turbolink.enums.TurbolinkEventEnum;
import com.linzi.pitpat.data.turbolink.listener.TurbolinkApplicationEvent;
import com.linzi.pitpat.data.userservice.biz.PutChannelUserBizService;
import com.linzi.pitpat.data.userservice.dto.api.response.traffic.investment.HomeLevelLockResponse;
import com.linzi.pitpat.data.userservice.enums.CountryFlagConstant;
import com.linzi.pitpat.data.userservice.enums.PutChannelUserLevelStatus;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.enums.UserGenderEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.SocketPushUtils;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.RunRouteDetailVO;
import com.linzi.pitpat.dto.request.NewPkActivityRequestDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.linzi.pitpat.data.activityservice.constant.enums.CreateTypeEnum.COMPANION;

/**
 * @since 2024年7月8日
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RoomManager {

    private final SocketPushUtils socketPushUtils;
    private final RoomService roomService;
    private final RoomConfigService roomConfigService;
    private final RoomParticipantsService roomParticipantsService;
    private final RoomConverter roomConverter;
    private final RoomConfigConverter roomConfigConverter;
    private final RedisTemplate redisTemplate;
    private final ISysConfigService sysConfigService;
    private final RoomBookingService roomBookingService;
    private final ZnsUserService znsUserService;
    private final RunActivityManager activityManager;
    private final ZnsUserAccountService userAccountService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ZnsUserService userService;
    private final RoomTicketLogService roomTicketLogService;
    private final RoomInviteRecordService roomInviteRecordService;
    private final AppMessageService appMessageService;
    private final ZnsUserFriendService znsUserFriendService;
    private final ZnsRunRouteService runRouteService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final RedissonClient redissonClient;
    private final CompanionUserService companionUserService;
    private final ClubService clubService;
    private final QueueMessageService queueMessageService;
    private final ZnsRunActivityService runActivityService;
    private final ClubActivityRoomRelationService clubActivityRoomRelationService;


    private final ZnsRunActivityConfigService runActivityConfigService;
    private final RoomModeRouteI18nService roomModeRouteI18nService;
    private final RoomActivityExtInfoService roomActivityExtInfoService;

    @Resource
    private TencentImUtil tencentImUtil;
    @Resource
    private ThreadPoolTaskExecutor createRoomExecutor;
    @Resource
    private ThreadPoolTaskExecutor roomTagExecutor;

    private final ZnsRunActivityUserService runActivityUserService;
    private final ClubMemberService clubMemberService;
    private final ClubPushManager clubPushManager;
    private final RoomUserDataBiz roomUserDataBiz;

    private final PutChannelUserBizService putChannelUserBizService;
    private final UserEquipmentBizService userEquipmentBizService;

    /**
     * 创建房间 房间相关配置保存
     *
     * @param room
     * @param roomConfigDo
     * @param loginUser
     * @param opponentUserId
     * @param appVersion
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public RoomResponseDto createRoom(RoomDo room, RoomConfigDo roomConfigDo, ZnsUserEntity loginUser, List<Long> opponentUserId, Integer appVersion) {
        // 4.4.4 版本以下开启开关进行强制升级
        if (appVersion < 4044 && getRoomVersionControlSwitch()) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.version.notice"));
        }
        preCheck(loginUser, room);
        RoomResponseDto roomResponseDto = new RoomResponseDto();
        room.setOwnerUserId(loginUser.getId());
        room.setRoomNumber(genRoomNumber());
        if (room.getRoomType() == 1) {
            room.setPasswordHash(String.valueOf(RandomUtil.randomInt(1000, 9999)));
        }
        if (room.getTimeType() == 0) {
            room.setStartTime(ZonedDateTime.now());
        }
        // 4.1.0 陪跑员查询房间确认
        CompanionUserDo companionUser = companionUserService.findByQuery(new CompanionUserQuery().setUserId(loginUser.getId()).setCompanionStatus(CompanionStatusEnum.PASSED.getStatus()));
        if (Objects.nonNull(companionUser)) {
            room.setCreateType(COMPANION.getCode());
        }
        roomService.create(room);
        roomConfigDo.setRoomId(room.getId());
        roomConfigService.create(roomConfigDo);
        roomResponseDto.setId(room.getId());
        if (room.getTimeType() == 0) {
            socketPushUtils.createRoom(room, loginUser.getId(), loginUser.getEmailAddressEn());
            if (room.getRoomMemberType() == 0 && !CollectionUtils.isEmpty(opponentUserId)) {
                ZnsUserEntity userServiceById = znsUserService.findById(opponentUserId.get(0));
                Map<String, Object> data = new HashMap<>();
                data.put("userId", room.getOwnerUserId());
                data.put("room", room);
                data.put("roomConfig", roomConfigDo);
                data.put("hImage", loginUser.getHeadPortrait());
                data.put("nickName", loginUser.getFirstName());
                RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
                data.put("routePic", routeDetailVO.getRouteThumbnail());
                RoomInviteRecordDo roomInviteRecordDo = new RoomInviteRecordDo()
                        .setInviteUserId(loginUser.getId())
                        .setTargetUserId(opponentUserId.get(0))
                        .setRoomId(room.getId())
                        .setStartTime(room.getStartTime())
                        .setStatus(0);
                roomInviteRecordService.create(roomInviteRecordDo);
                Integer appType = userServiceById.getAppSystem();
//                appMessageService.sendRoomPushAndIm(roomInviteRecordDo, room, roomConfigDo, appType, routeDetailVO.getRouteThumbnail(), loginUser);
                socketPushUtils.push(-2l, userServiceById.getEmailAddressEn(), SocketEventEnums.USER_ROOM_INVITE_MESSAGE.getCode(), 2, data);
            }
        } else {
            // 预约逻辑用户(增加房间创建者的预约记录)
            RoomBookingDo roomStarter = new RoomBookingDo();
            roomStarter.setUserId(loginUser.getId());
            roomStarter.setRoomId(room.getId());
            roomStarter.setUserType(0);
            roomStarter.setStartTime(room.getStartTime());
            roomBookingService.create(roomStarter);
            room.setRoomStatus(RoomStatusEnum.ORDERED_NOT_STARTED.getCode());
            roomService.update(room);
            if (!CollectionUtils.isEmpty(opponentUserId)) {
                opponentUserId.forEach(i -> {
                    RoomBookingDo roomBookingDo = new RoomBookingDo();
                    roomBookingDo.setUserId(i);
                    roomBookingDo.setRoomId(room.getId());
                    roomBookingDo.setUserType(1);
                    roomBookingDo.setStartTime(room.getStartTime());
                    roomBookingService.create(roomBookingDo);
                });
            }
        }
        createRoomDingTalkReminder(room, loginUser);
        //发布创建房间成功tb事件
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(), new TurbolinkApplicationEvent(TurbolinkEventEnum.CREATE_ROOM, loginUser.getId(),
                Map.of("isCreated", Boolean.TRUE.toString())));
        return roomResponseDto;
    }


    private void createRoomDingTalkReminder(RoomDo room, ZnsUserEntity loginUser) {
        if (room.getRoomType() == 1) {
            log.info("用户私密房间不进行通知roomNumber:{}", room.getRoomNumber());
            return;
        }
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        String token = DingTalkTokenEnum.PITPAT_CREATE_ROOM.getToken();
        String secret = DingTalkTokenEnum.PITPAT_CREATE_ROOM.getSecret();
        if (!EnvUtils.isReallyOnline(SpringContextUtils.getActiveProfile())) {
            log.warn("当前环境不是线上，忽略提醒,env={}", SpringContextUtils.getActiveProfile());
            token = DingTalkTokenEnum.PITPAT_CREATE_ROOM_TEST.getToken();
            secret = DingTalkTokenEnum.PITPAT_CREATE_ROOM_TEST.getSecret();
        }
        String finalToken = token;
        String finalSecret = secret;
        createRoomExecutor.submit(() -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            log.info("发送钉钉通知,有用户创建了房间，userId={}, roomNumber={}", loginUser.getId(), room.getRoomNumber());
            if (!Objects.equals(loginUser.getIsRobot(), 0)) {
                log.warn("当前用户不是普通用户，忽略提醒，userId={}, isRobot={}", loginUser.getId(), loginUser.getIsRobot());
                return;
            }
            if (Objects.equals(room.getRoomType(), 1)) {
                log.warn("私密房间不做通知发送 roomId:{}", room.getId());
                return;
            }
            try {
                String msgTpl = """
                        ### User creates a room.
                        #### Room number: %s
                        #### Gender: %s
                        """;
                String title = "有用户创建房间";
                String text = String.format(msgTpl,
                        room.getRoomNumber(), UserGenderEnum.resolve(loginUser.getGender()).getEnName());
                DingTalkUtils.sendMsg(DingTalkRequestDto.ofMarkdown(finalToken, finalSecret, text));
                // 4.6.1 查询用户数据
                roomUserDataBiz.sendUserDataNotice(room, loginUser, finalToken, finalSecret, 1);
            } catch (Exception e) {
                log.error("用户创建房间提醒失败", e);
            }
        });
    }


    private Long genRoomNumber() {
        String key = RedisConstants.APP_ROOM_NUMBER;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            redisTemplate.opsForValue().increment(key);
        } else {
            redisTemplate.opsForValue().set(key, "100001");
        }
        return Long.parseLong(Objects.requireNonNull(redisTemplate.opsForValue().get(key)).toString());
    }

    /**
     * 验证
     *
     * @param loginUser
     * @param room
     */
    private void preCheck(ZnsUserEntity loginUser, RoomDo room) {
        //NPC 不能创建房间
        String npcConfig = sysConfigService.selectConfigByKey("visual_user_activity_npc_config");
        List<Long> userIds = JsonUtil.readList(npcConfig, Long.class);
        if (!CollectionUtils.isEmpty(userIds) && userIds.contains(loginUser.getId())) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.create.limit"));
        }

        //是否存在未解散的房间
        RoomQuery roomQuery = RoomQuery.builder().ownerUserId(loginUser.getId()).build();
        roomQuery.setOrders(List.of(OrderItem.desc("id")));
        RoomDo query = roomService.findByQuery(roomQuery);
        if (query != null && query.getRoomStatus() != 2) {
            if (query.getTimeType() == 0 && room.getTimeType() == 0) {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.create.already"));
            } else {
                if (room.getTimeType() == 1) {
                    long between = ChronoUnit.HOURS.between(ZonedDateTime.now(), room.getStartTime());
                    if (between < 1L)
                        throw new BaseException(I18nMsgUtils.getMessage("room.error.order.check"));
                }
            }
        }

        //是否超过当日限制
        List<RoomDo> list = roomService.findList(RoomQuery.builder().ownerUserId(loginUser.getId())
                .gmtCreateStartTime(ZonedDateTime.now().withHour(0).withMinute(0).withSecond(0))
                .gmtCreateEndTime(ZonedDateTime.now().withHour(23).withMinute(59).withSecond(59)).build());
        VisualUserActivityConfigVo config = new VisualUserActivityConfigVo();
        String configStr = sysConfigService.selectConfigByKey("visual_user_activity_config");
        config = JsonUtil.readValue(configStr, VisualUserActivityConfigVo.class);
        if (list.size() >= Objects.requireNonNull(config).getOneDayLimitCreateRoomNum()) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.create.limit"));
        }
    }

    /**
     * 解散房间
     *
     * @param roomId
     * @param loginUser
     * @return
     */
    public boolean dismissRoom(Long roomId, ZnsUserEntity loginUser) {
        RoomDo roomDo = roomService.findById(roomId);
        RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
        if (Objects.nonNull(roomDo) && roomDo.getRoomStatus() != 2) {
            if (Objects.equals(roomDo.getCreateType(), RoomCreateType.npc_type.getCode())) {
                log.error("npc 房间不能解散， roomId={}, roomNumber={}", roomDo.getId(), roomDo.getRoomNumber());
                throw new BaseException(I18nMsgUtils.getMessage("room.error.dismiss.check"));
            }
            if (roomDo.getOwnerUserId().equals(loginUser.getId())) {
                roomDo.setRoomStatus(2);
                roomService.update(roomDo);
                socketPushUtils.dismissRoom(roomId, loginUser.getId());
                if (roomConfigDo.getTicketType() > 0) {
                    List<RoomTicketLogDo> ticketLogs = roomTicketLogService.findList(RoomTicketLogQuery.builder().roomId(roomDo.getId()).ticketStatus(RoomTicketStatusEnum.NO_USE.getCode()).build());
                    if (!CollectionUtils.isEmpty(ticketLogs)) {
                        ticketLogs.forEach(ticket -> refundRoomTicket(ticket.getUserId(), ticket));
                    }
                }
            } else {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.dismiss.check"));
            }
        }
        return true;
    }

    /**
     * 首页信息获取
     *
     * @return
     */
    public RoomIndexResponseDto indexList() {
        Long robotRoomCount = roomService.findCount(RoomQuery.builder().roomStatus(1).createType(RoomCreateType.robot_type.getCode()).build());
        var dto = new RoomIndexResponseDto();
        dto.setWaitingRoomNum(0);
        Long aLong = countOnlineUsers();
        if (aLong != null) {
            dto.setWaitingRoomNum(aLong.intValue());
        }
        if (robotRoomCount != null) {
            dto.setWaitingRoomNum(dto.getWaitingRoomNum() + robotRoomCount.intValue());
        }
        // 扩展10倍数
        dto.setWaitingRoomNum(dto.getWaitingRoomNum() * 10);
        return dto;
    }

    /**
     * 统计在线人数
     *
     * @return 在线用户数量
     */
    public Long countOnlineUsers() {
        String key = RedisConstants.APP_ROOM_ONLINE_USERS;
        return (Long) redisTemplate.execute(new RedisCallback<Long>() {
            @Override
            public Long doInRedis(RedisConnection redisConnection) throws DataAccessException {
                return redisConnection.bitCount(key.getBytes());
            }
        });
    }

    public RoomResponseDto findById(Long roomId, ZnsUserEntity loginUser) {
        RoomDo roomDo = roomService.findById(roomId);
        if (Objects.isNull(roomDo)) {
            return new RoomResponseDto();
        }
        RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
        RoomResponseDto combineDto = roomConverter.toCombineDto(roomDo, roomConfigDo);
        // 房主密码需要返回
        if (roomDo.getOwnerUserId().equals(loginUser.getId()) && roomDo.getRoomType() == 1) {
            combineDto.setPasswordHash(roomDo.getPasswordHash());
        }
        RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
        combineDto.setRoutePic(routeDetailVO.getRouteThumbnail());
        VisualUserActivityConfigVo config = new VisualUserActivityConfigVo();
        String configStr = sysConfigService.selectConfigByKey("visual_user_activity_config");
        config = JsonUtil.readValue(configStr, VisualUserActivityConfigVo.class);
        combineDto.setIsVoiceUp(config.getIsVoiceUp());
        if (combineDto.getCreateType().equals(COMPANION.getCode())) {
            CompanionUserDo companionUserDo = companionUserService.findByQuery(new CompanionUserQuery().setUserId(roomDo.getOwnerUserId()));
            if (Objects.nonNull(companionUserDo)) {
                combineDto.setCompanionLevel(companionUserDo.getCompanionLevel());
                combineDto.setCompanionStatus(companionUserDo.getCompanionStatus());
            }
        }
        ClubActivityRoomRelationDo clubActivityRoomRelationDo = clubActivityRoomRelationService.findByQuery(ClubActivityRoomRelationQuery.builder().roomId(roomId).build());
        if (Objects.nonNull(clubActivityRoomRelationDo)) {
            combineDto.setClubId(clubActivityRoomRelationDo.getClubId());
            combineDto.setActivityId(clubActivityRoomRelationDo.getActivityId());
        }
        // 查询房间tips sysConfig配置
        String roomTipsConfig = sysConfigService.selectConfigByKey(ConfigKeyEnums.ROOM_TIPS_CONFIG.getCode());
        if (StringUtils.hasText(roomTipsConfig)) {
            RoomTipAndUrlDto tipAndUrlDto = JsonUtil.readValue(roomTipsConfig, RoomTipAndUrlDto.class);
            if (Objects.nonNull(tipAndUrlDto)) {
                combineDto.setRoomTipsPopUrl(tipAndUrlDto.getRoomTipsPopUrl());
                combineDto.setRoomTipsH5Url(tipAndUrlDto.getRoomTipsH5Url());
                combineDto.setRoomTipsBannerUrl(tipAndUrlDto.getRoomTipsBannerUrl());
                combineDto.setTips(tipAndUrlDto.getTips());
                combineDto.setCountDownWaitTime(tipAndUrlDto.getCountDownWaitTime());
            }
        }
        return combineDto;
    }

    public static String memberKey = RedisConstants.APP_ROOM_MEMBER_MAP;
    public static String memberStatusKey = RedisConstants.APP_ROOM_MEMBER_STATUS;

    public List<RoomMemberResponseDto> findSocketMembers(RoomQueryDto queryDto) {
        Long roomId = queryDto.getId();
        //获取所有房间内用户
        Set<String> users = redisTemplate.opsForSet().members(memberKey + String.valueOf(roomId));
        assert users != null;
        List<RoomMemberResponseDto> lists = new ArrayList<>();
        users.forEach(i -> {
            RoomMemberResponseDto roomMemberResponseDto = new RoomMemberResponseDto();
            ZnsUserEntity email = znsUserService.findByEmail(i);
            roomMemberResponseDto.setEmail(i);
            roomMemberResponseDto.setStatus(0);
            roomMemberResponseDto.setUserId(email.getId());
            roomMemberResponseDto.setNickName(email.getFirstName());
            roomMemberResponseDto.setHeadPortrait(email.getHeadPortrait());
            Object object = redisTemplate.opsForHash().get(memberStatusKey + roomId, String.valueOf(email.getId()));
            if (Objects.nonNull(object)) {
                roomMemberResponseDto.setStatus(Integer.valueOf(object.toString()));
            }
            lists.add(roomMemberResponseDto);
        });
        return lists;
    }

    public RoomResponseDto getByActivityId(Long activityId) {
        RoomParticipantsDo query = roomParticipantsService.findByQuery(RoomParticipantsQuery.builder().activityId(activityId).build());
        RoomDo roomDo = roomService.findById(query.getRoomId());
        RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
        RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
        RoomResponseDto combineDto = roomConverter.toCombineDto(roomDo, roomConfigDo);
        combineDto.setRoutePic(routeDetailVO.getRouteThumbnail());
        ZnsRunActivityEntity one = runActivityService.findOne(RunActivityQuery.builder()
                .id(activityId).build());
        combineDto.setTargetMileage(one.getRunMileage());
        combineDto.setTargetTime(one.getRunTime());
        combineDto.setTargetType(one.getRunTime() > 0 ? 1 : 0);
        combineDto.setStartTime(DateUtil.toZonedDateTime(one.getActivityStartTime()));
        RunRouteDetailVO activityRouteId = runRouteService.runRouteDetail(one.getActivityRouteId());
        combineDto.setRoutePic(activityRouteId.getRouteThumbnail());
        // 获取当前活动时候的模式
        RoomActivityExtInfoDo roomActivityExtInfoDo = roomActivityExtInfoService.findByQuery(new RoomActivityExtInfoQuery().setActivityId(activityId));
        //兼容老活动没有的使用房间默认模式
        if (Objects.nonNull(roomActivityExtInfoDo)) {
            combineDto.setRoomMode(roomActivityExtInfoDo.getActivityMode());
        }
        return combineDto;
    }

    public RoomCheckResponseDto checkBeforeRoomCreate(ZnsUserEntity loginUser, RoomQueryDto queryDto) {
        RoomCheckResponseDto roomCheckResponseDto = new RoomCheckResponseDto();
        roomCheckResponseDto.setStatus(0);
        if (Objects.nonNull(queryDto.getId())) {
            RoomDo roomDo = roomService.findById(queryDto.getId());
            if (roomDo.getRoomStatus() == 2) {
                roomCheckResponseDto.setStatus(2);
                roomCheckResponseDto.setMsg(I18nMsgUtils.getMessage("room.error.already.close"));
            } else {
                List<RoomParticipantsDo> list = roomParticipantsService.findList(RoomParticipantsQuery.builder().userId(loginUser.getId())
                        .startTime(ZonedDateTime.now().withHour(0).withMinute(0).withSecond(0))
                        .endTime(ZonedDateTime.now().withHour(23).withMinute(59).withSecond(59)).build());
                VisualUserActivityConfigVo config = new VisualUserActivityConfigVo();
                String configStr = sysConfigService.selectConfigByKey("visual_user_activity_config");
                config = JsonUtil.readValue(configStr, VisualUserActivityConfigVo.class);
                if (list.size() >= Objects.requireNonNull(config).getOneDayLimitJoinRoomNum()) {
                    roomCheckResponseDto.setStatus(1);
                    roomCheckResponseDto.setMsg(I18nMsgUtils.getMessage("room.error.join.limit"));
                }
            }
        }
        return roomCheckResponseDto;
    }

    public RoomCheckResponseDto checkPassWord(RoomCheckQueryDto queryDto, ZnsUserEntity loginUser) {
        RoomCheckResponseDto roomCheckResponseDto = new RoomCheckResponseDto();
        RoomDo roomDo = roomService.findById(queryDto.getRoomId());
        if (roomDo.getPasswordHash().equals(queryDto.getPassword())) {
            roomCheckResponseDto.setCheckPassWordStatus(0);
            // 用户房间密码验证标志set
            redisTemplate.opsForHash().putIfAbsent(RedisConstants.APP_ROOM_USER_PASS + queryDto.getRoomId(), loginUser.getId() + "", "1");
        } else {
            roomCheckResponseDto.setCheckPassWordStatus(1);
            roomCheckResponseDto.setMsg(I18nMsgUtils.getMessage("room.error.password.check"));
        }
        return roomCheckResponseDto;
    }

    public RoomRandomResponseDto randomRoom(ZnsUserEntity loginUser) {
        RoomRandomResponseDto roomRandomResponseDto = new RoomRandomResponseDto();
        String roomSingleKey = RedisConstants.APP_ROOM_SINGLE_SET;
        Set<String> roomIds = redisTemplate.opsForSet().members(roomSingleKey);

        if (roomIds != null && !roomIds.isEmpty()) {
            CompanionUserDo companionUser = companionUserService.findByQuery(new CompanionUserQuery().setUserId(loginUser.getId()));
            if (Objects.isNull(companionUser)) {
                // 非陪跑员用户 增加查询陪跑员房间获取逻辑
                Set<String> companionUserRoomIdList = getCompanionUserRoomIdList();
                Optional<String> any = companionUserRoomIdList.parallelStream().filter(roomIds::contains).findAny();
                if (any.isPresent()) {
                    redisTemplate.opsForSet().remove(roomSingleKey, any.get());
                    roomRandomResponseDto.setRoomId(Long.valueOf(any.get()));
                    log.info("陪跑员房间获取随机 roomId:{}", roomRandomResponseDto.getRoomId());
                    return roomRandomResponseDto;
                }
            }

            Set<Long> npcRoomIdList = getNpcRoomIdList();
            //优先查找 npc 房间 去除匹配 1v1 的逻辑。改为所有npc房间
            if (!CollectionUtils.isEmpty(npcRoomIdList)) {
                ArrayList<Long> objects = new ArrayList<>(npcRoomIdList);
                Collections.shuffle(objects);
                Long aLong = objects.get(0);
                roomRandomResponseDto.setRoomId(aLong);
                return roomRandomResponseDto;
            }

            Object object = redisTemplate.opsForSet().pop(roomSingleKey);
            if (Objects.nonNull(object)) {
                Long randomRoomId = Long.parseLong(object.toString());
                roomRandomResponseDto.setRoomId(randomRoomId);
            } else {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.random.miss"), CommonError.BUSINESS_ERROR.getCode());
            }
        } else {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.random.miss"), CommonError.BUSINESS_ERROR.getCode());
        }
        return roomRandomResponseDto;
    }

    private Set<String> getCompanionUserRoomIdList() {
        RoomQuery query = RoomQuery.builder().createType(CreateTypeEnum.COMPANION.getCode()).roomStatus(RoomStatusEnum.NOT_STARTED.getCode()).build();
        List<RoomDo> companionUserRoomList = roomService.findList(query);
        return companionUserRoomList.stream().map(RoomDo::getId).map(String::valueOf).collect(Collectors.toSet());
    }

    private Set<Long> getNpcRoomIdList() {
        RSet<Long> npcRoomIdList = redissonClient.getSet(RedisConstants.APP_NPC_ROOM);

        if (CollectionUtils.isEmpty(npcRoomIdList)) {
            RoomQuery query = RoomQuery.builder().createType(RoomCreateType.npc_type.getCode()).roomStatus(0).build();
            List<RoomDo> npcRoomList = roomService.findList(query);

            //缓存到 Redis
            if (!CollectionUtils.isEmpty(npcRoomList)) {
                npcRoomIdList.addAll(npcRoomList.stream().map(RoomDo::getId).collect(Collectors.toSet()));
                npcRoomIdList.expire(15, TimeUnit.MINUTES);
            }
        }
        return npcRoomIdList;
    }

    @SneakyThrows
    public RoomRoomActivityResponseDto startActivityInRoom(ZnsUserEntity loginUser, Long roomId, List<Long> opponentUserIds) {
        List<RoomMemberResponseDto> roomMembers = socketPushUtils.getRoomMembers(roomId);
        RoomDo roomDo = roomService.findById(roomId);
        if (CollectionUtils.isEmpty(roomMembers)) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.no.members"), CommonError.BUSINESS_ERROR.getCode());
        }
        if (roomMembers.size() < 2) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.members.limit"), CommonError.BUSINESS_ERROR.getCode());
        }
        if (roomMembers.stream().anyMatch(e -> e.getStatus().equals(0) && !e.getUserId().equals(roomDo.getOwnerUserId()))) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.ready.check"), CommonError.BUSINESS_ERROR.getCode());
        }
        //房间陪跑员数量校验check
        checkCompanionUserNumCheck(loginUser, opponentUserIds, roomDo);
        //准备状态校验
        if (roomMembers.stream().anyMatch(e -> !e.getStatus().equals(1))) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.ready.check"), CommonError.BUSINESS_ERROR.getCode());
        }
        RoomRoomActivityResponseDto roomRoomActivityResponseDto = new RoomRoomActivityResponseDto();
        RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
        NewPkActivityRequestDto dto = new NewPkActivityRequestDto();
        dto.setActivityTypeSub(roomDo.getRoomMemberType() == 0 ? RunActivitySubTypeEnum.NEW_1_V_1_PK.getType() : RunActivitySubTypeEnum.NEW_TEAM_PK.getType());
        opponentUserIds.add(loginUser.getId());
        dto.setActivityUserIds(opponentUserIds.stream().distinct().toList());
        dto.setActivityType(RunActivityTypeEnum.NEW_PK_ACTIVITY.getType());
        dto.setActivityRouteId(roomConfigDo.getRouteId());
        dto.setActivityStartTime(ZonedDateTime.now().toInstant().toEpochMilli());
        if (roomConfigDo.getTargetType() == 0) {
            dto.setRunMileage(roomConfigDo.getTargetMileage());
            dto.setCompleteRuleType(1);
        } else {
            dto.setRunTime(roomConfigDo.getTargetTime());
            dto.setCompleteRuleType(2);
        }
        //查询房间号
        dto.setAppRoomId(roomDo.getRoomNumber());
        dto.setBonusRuleType(roomConfigCoverBonusRuleType(roomConfigDo.getTicketType()));
        dto.setActivityEntryFee(roomConfigDo.getTicketPrice());
        dto.setActivityEntryScore(roomConfigDo.getTicketPrice().intValue());
        dto.setInitiatorUserId(roomDo.getOwnerUserId());
        RLock lock = redissonClient.getLock(RedisConstants.LOCK_ROOM_ACTIVITY_START + roomId);
        try {
            if (!lock.tryLock(2, TimeUnit.SECONDS)) {
                throw new BaseException(CommonError.DUPLICATE_REQUEST.getMsg(), CommonError.DUPLICATE_REQUEST.getCode());
            }
            ZnsRunActivityEntity newPkActivity = activityManager.createNewPkActivity(dto, roomDo.getRoomMode(), roomId);
            roomRoomActivityResponseDto.setActivityId(newPkActivity.getId());
            roomRoomActivityResponseDto.setActivityStartTime(newPkActivity.getActivityStartTime());
            // socket 消息推送
            if (!opponentUserIds.isEmpty()) {
                socketPushUtils.startGame(roomId, roomRoomActivityResponseDto, loginUser);
                List<RoomParticipantsDo> listUser = new ArrayList<>();
                opponentUserIds.stream().distinct().forEach(i -> {
                    var roomParticipantsDo = new RoomParticipantsDo();
                    roomParticipantsDo.setActivityId(newPkActivity.getId()).setUserId(i).setRoomId(roomId).setJoinTime(ZonedDateTime.now());
                    listUser.add(roomParticipantsDo);
                });
                roomParticipantsService.batchCreate(listUser);
                roomDo.setRoomStatus(1);
                roomDo.setActivityNum(roomDo.getActivityNum() + 1);
                roomService.update(roomDo);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return roomRoomActivityResponseDto;
    }

    /**
     * 校验
     *
     * @param loginUser
     * @param opponentUserIds
     * @param roomDo
     */
    private void checkCompanionUserNumCheck(ZnsUserEntity loginUser, List<Long> opponentUserIds, RoomDo roomDo) {
        opponentUserIds.add(loginUser.getId());
        List<Long> list = opponentUserIds.stream().distinct().toList();
        List<CompanionUserDo> companionUserDos = companionUserService.findList(new CompanionUserQuery().setUserIds(list).setCompanionStatus(CompanionStatusEnum.PASSED.getStatus()));
        if (!CollectionUtils.isEmpty(companionUserDos)) {
            if (companionUserDos.size() > 1) {
                String users = companionUserDos.stream().filter(k -> !k.getUserId().equals(roomDo.getOwnerUserId())).map(e -> znsUserService.findById(e.getUserId()).getFirstName()).collect(Collectors.joining(","));
                throw new BaseException(I18nMsgUtils.getMessage("room.error.companion.limit", users), CommonError.BUSINESS_ERROR.getCode());
            }
        }
    }

    private Integer roomConfigCoverBonusRuleType(Integer ticketType) {
        Integer bonusRuleType = 1;
        if (ticketType == 1) {
            bonusRuleType = 2;
        }
        if (ticketType == 2) {
            bonusRuleType = 4;
        }
        return bonusRuleType;
    }

    public RoomPayTicketResponseDto payTicket(RoomPayTicketDto queryDto, ZnsUserEntity loginUser) {
        RoomPayTicketResponseDto roomPayTicketResponseDto = new RoomPayTicketResponseDto();
        roomPayTicketResponseDto.setPayStatus(YesNoStatus.NO.getCode());
        RoomDo roomDo = roomService.findById(queryDto.getRoomId());
        RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
        if (roomConfigDo.getTicketType() > 0) {
            Long detailId = 0L;
            // 支付流程
            if (roomConfigDo.getTicketType() == 1) {
                //校验支付密码
                Result payCheckResult = userAccountService.checkPassword(loginUser.getId(), queryDto.getPassword(), false);
                if (Objects.nonNull(payCheckResult)) {
                    if (payCheckResult.getCode().equals(UserError.PAY_PASSWORD_ERROR.getCode())) {
                        throw new BaseException(payCheckResult.getMsg());
                    }
                }
                Result result = useUserAmount(queryDto.getPassword(), loginUser, roomConfigDo.getTicketPrice());
                Object data = result.getData();
                Map<Object, Object> map = JsonUtil.readValue(data);
                Object object = map.get("detailId");
                detailId = Long.valueOf(object.toString());
            }
            if (roomConfigDo.getTicketType() == 2) {
                detailId = useUserScore(roomDo.getId(), loginUser, roomConfigDo.getTicketPrice().intValue());
            }
            if (detailId > 0) {
                RoomTicketLogDo ticketLogDo = RoomTicketLogDo.builder()
                        .roomId(roomDo.getId())
                        .roomNumber(roomDo.getRoomNumber())
                        .detailId(detailId)
                        .userId(loginUser.getId())
                        .ticketPrice(roomConfigDo.getTicketPrice())
                        .ticketStatus(0)
                        .ticketType(roomConfigDo.getTicketType()).build();
                roomTicketLogService.create(ticketLogDo);
                roomPayTicketResponseDto.setPayStatus(YesNoStatus.YES.getCode());
                //更新支付记录标题
                if (RoomConfigTicketTypeEnum.AMOUNT.getCode().equals(roomConfigDo.getTicketType())) {
                    ZnsUserAccountDetailEntity update = new ZnsUserAccountDetailEntity();
                    update.setTitle("PK - " + roomDo.getRoomNumber());
                    update.setId(detailId);
                    userAccountDetailService.updateById(update);
                }
            }
        } else {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.pay.check"), CommonError.BUSINESS_ERROR.getCode());
        }
        return roomPayTicketResponseDto;
    }

    public RoomTicketCheckResponseDto payTicketCheckInRoom(RoomTicketBaseDto queryDto, ZnsUserEntity loginUser) {
        RoomTicketLogDo byQuery = roomTicketLogService.findByQuery(RoomTicketLogQuery.builder().userId(loginUser.getId()).roomId(queryDto.getRoomId()).ticketStatus(0).build());
        RoomTicketCheckResponseDto roomTicketCheckResponseDto = new RoomTicketCheckResponseDto();
        if (Objects.nonNull(byQuery)) {
            roomTicketCheckResponseDto.setPayStatus(1);
            return roomTicketCheckResponseDto;
        } else {
            roomTicketCheckResponseDto.setPayStatus(0);
            return roomTicketCheckResponseDto;
        }
    }

    public void payTicketUse(RoomTicketUseDto queryDto, ZnsUserEntity loginUser) {
        RLock lock = redissonClient.getLock(RedisConstants.LOCK_ROOM_TICKET_USE + loginUser.getId() + ":" + queryDto.getRoomId() + ":" + queryDto.getActivityId());
        try {
            if (!lock.tryLock(2, TimeUnit.SECONDS)) {
                throw new BaseException(CommonError.DUPLICATE_REQUEST.getMsg(), CommonError.DUPLICATE_REQUEST.getCode());
            }
            RoomDo roomDo = roomService.findById(queryDto.getRoomId());
            RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
            RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
            // 需要支付才会校验
            if (roomConfigDo.getTicketType() > 0) {
                // 未使用
                RoomTicketLogDo byQuery = roomTicketLogService.findByQuery(RoomTicketLogQuery.builder().userId(loginUser.getId()).roomId(queryDto.getRoomId()).ticketStatus(0).build());
                if (Objects.nonNull(byQuery)) {
                    if (byQuery.getTicketStatus() == 0) {
                        byQuery.setTicketStatus(1);
                        byQuery.setActivityId(queryDto.getActivityId());
                        roomTicketLogService.update(byQuery);
                        //更新支付记录
                        if (RoomConfigTicketTypeEnum.AMOUNT.getCode().equals(byQuery.getTicketType())) {
                            ZnsUserAccountDetailEntity detail = userAccountDetailService.selectById(byQuery.getDetailId());
                            if (Objects.nonNull(detail)) {
                                ZnsUserAccountDetailEntity update = new ZnsUserAccountDetailEntity();
                                update.setActivityId(queryDto.getActivityId());
                                update.setId(detail.getId());
                                userAccountDetailService.updateById(update);
                            }
                        } else if (RoomConfigTicketTypeEnum.SCORE.getCode().equals(byQuery.getTicketType())) {
                            ActivityUserScore detail = activityUserScoreService.getById(byQuery.getDetailId());
                            if (Objects.nonNull(detail)) {
                                ActivityUserScore update = new ActivityUserScore();
                                update.setActivityId(queryDto.getActivityId());
                                update.setId(detail.getId());
                                activityUserScoreService.updateById(update);
                            }
                        }
                    }
                } else {
                    RoomTicketLogDo useTicketLogDo = roomTicketLogService.findByQuery(RoomTicketLogQuery.builder().userId(loginUser.getId()).roomId(queryDto.getRoomId()).activityId(queryDto.getActivityId()).build());
                    if (!Objects.nonNull(useTicketLogDo)) {
                        throw new BaseException(I18nMsgUtils.getMessage("room.error.no.pay"), CommonError.BUSINESS_ERROR.getCode());
                    }
                }
            }
        } catch (InterruptedException e) {
            throw new BaseException(CommonError.DUPLICATE_REQUEST.getMsg(), CommonError.DUPLICATE_REQUEST.getCode());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public RoomTicketRefundResponseDto payTicketRefund(RoomTicketBaseDto queryDto, Long userId) {
        RoomTicketRefundResponseDto roomTicketRefundResponseDto = new RoomTicketRefundResponseDto();
        roomTicketRefundResponseDto.setPayStatus(YesNoStatus.NO.getCode());
        RoomTicketLogDo byQuery = roomTicketLogService.findByQuery(RoomTicketLogQuery.builder().userId(userId).roomId(queryDto.getRoomId()).ticketStatus(0).build());
        if (Objects.nonNull(byQuery)) {
            if (byQuery.getTicketStatus() == 0) {
                refundRoomTicket(byQuery.getUserId(), byQuery);
                roomTicketRefundResponseDto.setPayStatus(YesNoStatus.YES.getCode());
            }
        }
        return roomTicketRefundResponseDto;
    }

    public void refundRoomTicket(Long userId, RoomTicketLogDo byQuery) {
        RLock lock = redissonClient.getLock(RedisConstants.LOCK_ROOM_TICKET_REFUND + byQuery.getId());
        try {
            if (!lock.tryLock(2, TimeUnit.SECONDS)) {
                log.warn("refundRoomTicket lock get error");
                return;
            }
            long refundDetailId = 0L;
            if (byQuery.getTicketType() == 1) {
                //退钱
                ZnsUserAccountDetailEntity userAccountDetailEntity = userAccountDetailService.selectById(byQuery.getDetailId());
                if (null != userAccountDetailEntity && userAccountDetailEntity.getRefundStatus() == 0) {
                    userAccountService.refundBalance(userAccountDetailEntity, "room dismiss refundBalance");
                    log.info("room dismiss退款{}", userAccountDetailEntity);
                    refundDetailId = userAccountDetailEntity.getId();
                }
            }
            if (byQuery.getTicketType() == 2) {
                //退积分
                ActivityUserScore activityUserScore = activityUserScoreService.getRefundActivityUserScore(new MainActivity(), userId, byQuery.getTicketPrice().intValue(), true);
                activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_33.getType());
                activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);
                refundDetailId = activityUserScore.getId();
            }
            byQuery.setTicketStatus(RoomTicketStatusEnum.REFUND.getCode());
            byQuery.setRefundDetailId(refundDetailId);
            roomTicketLogService.update(byQuery);
        } catch (Exception e) {
            log.error("refundRoomTicket error", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public List<RoomMyRaceResponseDto> myRaceRoom(ZnsUserEntity loginUser) {
        List<RoomMyRaceResponseDto> myRaceResponseDtoList = new ArrayList<>();
        List<RoomInviteRecordDo> roomInviteRecordServiceList = roomInviteRecordService.findList(RoomInviteRecordQuery.builder().userId(loginUser.getId()).status(1).build());
        if (!CollectionUtils.isEmpty(roomInviteRecordServiceList)) {
            roomInviteRecordServiceList.forEach(bookingService -> {
                RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(bookingService.getRoomId()).build());
                if (Objects.nonNull(roomDo)) {
                    RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
                    if (roomDo.getRoomStatus() != 2) {
                        // 房间排除解散状态的房间
                        RoomMyRaceResponseDto combineMyRaceDto = roomConverter.toCombineMyRaceDto(roomDo, roomConfigDo);
                        ZnsUserEntity own = znsUserService.findById(roomDo.getOwnerUserId());
                        combineMyRaceDto.setOwnerUserName(own.getFirstName());
                        myRaceResponseDtoList.add(combineMyRaceDto);
                    }
                }
            });
        }
        // 登录用户自己的房间add
        List<RoomDo> roomDoList = roomService.findList(RoomQuery.builder().ownerUserId(loginUser.getId()).roomStatusLists(List.of(RoomStatusEnum.NOT_STARTED.getCode(), RoomStatusEnum.IN_PROGRESS.getCode())).build());
        if (!CollectionUtils.isEmpty(roomDoList)) {
            roomDoList.forEach(roomDo -> {
                RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
                RoomMyRaceResponseDto combineMyRaceDto = roomConverter.toCombineMyRaceDto(roomDo, roomConfigDo);
                combineMyRaceDto.setOwnerUserName(loginUser.getFirstName());
                myRaceResponseDtoList.add(combineMyRaceDto);
            });
        }
        // 登录用户自己的预约房间
        List<RoomDo> roomDoOrderedList = roomService.findList(RoomQuery.builder().ownerUserId(loginUser.getId()).roomStatus(RoomStatusEnum.ORDERED_NOT_STARTED.getCode()).build());
        if (!CollectionUtils.isEmpty(roomDoOrderedList)) {
            roomDoOrderedList.forEach(roomDo -> {
                RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
                RoomMyRaceResponseDto combineMyRaceDto = roomConverter.toCombineMyRaceDto(roomDo, roomConfigDo);
                combineMyRaceDto.setOwnerUserName(loginUser.getFirstName());
                myRaceResponseDtoList.add(combineMyRaceDto);
            });
        }
        return myRaceResponseDtoList.stream().sorted(Comparator.comparing(RoomMyRaceResponseDto::getStartTime)).collect(Collectors.toList());
    }

    /**
     * 邀请用户
     *
     * @param dto
     * @param loginUser
     */
    public void inviteUser(RoomInviteUserDto dto, ZnsUserEntity loginUser) {
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(dto.getRoomId()).build());
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
        dto.getOpponentUserIds().forEach(i -> {
            RoomInviteRecordDo roomInviteRecordDo = roomInviteRecordService.findByQuery(RoomInviteRecordQuery.builder().userId(i).roomId(dto.getRoomId()).build());
            if (Objects.nonNull(roomInviteRecordDo)) {
                roomInviteRecordDo.setStatus(0);
                roomInviteRecordService.update(roomInviteRecordDo);
            } else {
                RoomInviteRecordDo roomInviteRecordDoNew = new RoomInviteRecordDo()
                        .setInviteUserId(loginUser.getId())
                        .setTargetUserId(i)
                        .setRoomId(dto.getRoomId())
                        .setStartTime(roomDo.getStartTime())
                        .setStatus(0);
                roomInviteRecordService.create(roomInviteRecordDoNew);
                roomInviteRecordDo = roomInviteRecordDoNew;
            }
            // 房主邀请密码直接给了
            if (roomDo.getOwnerUserId().equals(loginUser.getId())) {
                roomInviteRecordDo.setPasswordHash(roomDo.getPasswordHash());
            }
            RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
            ZnsUserEntity userServiceById = znsUserService.findById(i);
            Integer appType = userServiceById.getAppSystem();
            appMessageService.sendRoomPushAndIm(roomInviteRecordDo, roomDo, roomConfigDo, appType, routeDetailVO.getRouteThumbnail(), loginUser);
            sendSocket(roomInviteRecordDo, roomDo, roomConfigDo, loginUser);
        });

    }

    public void inviteGroupUser(RoomInviteGroupUserDto dto, ZnsUserEntity loginUser) {
        List<Long> clubIdList = dto.getClubIdList();
        if (CollectionUtils.isEmpty(clubIdList)) {
            return;
        }
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(dto.getRoomId()).build());
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
        RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
        String routeThumbnail = routeDetailVO.getRouteThumbnail();
        ImMessageBo imMessageBo = new ImMessageBo();
        Map<String, Object> params = new HashMap<>();
        params.putAll(JsonUtil.readValue(roomDo));
        params.putAll(JsonUtil.readValue(roomConfigDo));
        params.put("ticketPrice", roomConfigDo.getTicketPrice().toString());
        params.put("targetMileage", roomConfigDo.getTargetMileage() != null ? roomConfigDo.getTargetMileage().toString() : "");
        params.put("targetTime", roomConfigDo.getTargetTime());
        params.put("routePic", routeThumbnail);
        imMessageBo.setParams(params);
        imMessageBo.setBusinessID("userRace");
        imMessageBo.setUserName(loginUser.getFirstName());
        imMessageBo.setHandleType(0);
        for (Long clubId : clubIdList) {
            Club club = clubService.findById(clubId);
            if (Objects.nonNull(club)) {
                if (sysConfigService.enableClubGroup(loginUser.getId())) {
                    tencentImUtil.sendGroupMsg(club.getClubGroupId(), club.getName(), String.valueOf(loginUser.getId()), TencentImConstant.TIM_CUSTOM_ELEM, JsonUtil.writeString(imMessageBo), club.getName());
                }
            }
        }
    }


    private void sendSocket(RoomInviteRecordDo roomInviteRecordDo, RoomDo roomDo, RoomConfigDo roomConfigDo, ZnsUserEntity loginUser) {
        ZnsUserEntity userServiceById = znsUserService.findById(roomInviteRecordDo.getTargetUserId());
        Map<String, Object> data = new HashMap<>();
        data.put("userId", roomInviteRecordDo.getInviteUserId());
        data.put("room", roomDo);
        data.put("roomConfig", roomConfigDo);
        data.put("hImage", loginUser.getHeadPortrait());
        data.put("nickName", loginUser.getFirstName());
        RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
        data.put("routePic", routeDetailVO.getRouteThumbnail());
        socketPushUtils.push(-2L, userServiceById.getEmailAddressEn(), SocketEventEnums.USER_ROOM_INVITE_MESSAGE.getCode(), 2, data);
    }


    private Long useUserScore(Long roomId, ZnsUserEntity user, Integer score) {
        //若报名费包含积分，则判断用户积分是否足够
        if (!checkUserActivityEntryScore(score, user)) {
            throw new BaseException(I18nMsgUtils.getMessage("activity.racePoint.insufficient"));
        }
        ActivityUserScore activityUserScore = getActivityUserScore(user.getId(), score, false);
        activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);
        //抵扣积分
        activityUserScoreService.useActivityUserScore(activityUserScore.getScore(), 0L, user.getId(), 0);
        log.info("用户={}报名房间={}消耗了积分={}", user.getId(), roomId, score);
        return activityUserScore.getId();
    }


    private boolean checkUserActivityEntryScore(Integer score, ZnsUserEntity user) {
        Integer userScore = userService.getAllUserScore(user.getId());
        return userScore >= score;
    }

    private ActivityUserScore getActivityUserScore(Long userId, Integer score, boolean refund) {
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setExchangeScoreRuleId(-1L);
        activityUserScore.setScore(score);
        activityUserScore.setStatus(1);
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setType(4); //活动使用;
        activityUserScore.setIncome(refund ? 1 : -1); //退款会增加积分， 报名则会扣除积分
        activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_33.getType());
        activityUserScore.setUserId(userId);
        activityUserScore.setExpireTime(refund ? DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")) : null);
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        return activityUserScore;
    }

    private Result useUserAmount(String password, ZnsUserEntity user, BigDecimal payAmount) {
        // 开始支付
        RunActivityPayRequest request = new RunActivityPayRequest();
        request.setUserId(user.getId());
        request.setPayType(0);
        request.setPayPassword(password);
        request.setAmount(payAmount);
        request.setActivityType(17);
        request.setAccountDetailTypeEnum(AccountDetailTypeEnum.SECURITY_FUND);
        Result payResult = userAccountService.payByBalance(request);
        if (null != payResult && !CommonError.SUCCESS.getCode().equals(payResult.getCode())) {
            log.warn("payByBalance failed:", payResult.getMsg());
            // 支付失败
            throw new BaseException(I18nMsgUtils.getMessage("room.error.pay.failed"));
        }
        return payResult;
    }


    public void handleAccept(RoomHandleAcceptDto dto, ZnsUserEntity loginUser) {
        RoomInviteRecordDo roomInviteRecordDo = roomInviteRecordService.findByQuery(RoomInviteRecordQuery.builder().userId(loginUser.getId()).roomId(dto.getRoomId()).inviteUserId(dto.getInviteUserId()).build());
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(dto.getRoomId()).build());
        if (Objects.nonNull(roomDo) && roomDo.getRoomStatus() != 2) {
            if (Objects.nonNull(roomInviteRecordDo)) {
                roomInviteRecordDo.setStatus(dto.getAcceptStatus());
                roomInviteRecordService.update(roomInviteRecordDo);
                if (dto.getAcceptStatus() == 2) {
                    ZnsUserEntity user = znsUserService.findById(roomInviteRecordDo.getInviteUserId());
                    Map<String, Object> data = new HashMap<>();
                    data.put("msg", loginUser.getFirstName() + I18nMsgUtils.getMessage("room.msg.invite.reject"));
                    data.put("appRoomId", roomDo.getId());
                    socketPushUtils.push(-2l, user.getEmailAddressEn(), SocketEventEnums.USER_ROOM_INVITE_REFUSE_MESSAGE.getCode(), 2, data);
                }
            } else {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.invite.check"), CommonError.BUSINESS_ERROR.getCode());
            }
        } else {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.exist.check"), CommonError.BUSINESS_ERROR.getCode());
        }
    }

    public List<ZonedDateTime> queryFriendPKPlan(Long userId) {
        List<RoomBookingDo> bookingServiceList = roomBookingService.findList(RoomBookingQuery.builder().userId(userId).build());
        return bookingServiceList.stream().map(RoomBookingDo::getStartTime).collect(Collectors.toList());
    }

    public Page<RoomCombineDto> findFriendPage(RoomPageQuery pageQuery, ZnsUserEntity loginUser) {
        List<Long> friendsList = znsUserFriendService.findRealManFriendsIdList(loginUser.getId());
        if (CollectionUtils.isEmpty(friendsList)) {
            return new Page<>();
        }
        pageQuery.setFriedIds(friendsList);
        pageQuery.setUserId(loginUser.getId());
        Page<RoomCombineDto> page = roomService.findRoomAndRoomConfigPage(pageQuery);
        return page;
    }

    /**
     * 用户是否已经输入过正确房间密码
     *
     * @param queryDto
     * @param loginUser
     * @return
     */
    public RoomCheckPassEnterResponseDto checkPassEnter(RoomCheckQueryDto queryDto, ZnsUserEntity loginUser) {
        var roomCheckPassEnterResponseDto = new RoomCheckPassEnterResponseDto();
        roomCheckPassEnterResponseDto.setEnterPassWordStatus(0);
        if (redisTemplate.opsForHash().hasKey(RedisConstants.APP_ROOM_USER_PASS + queryDto.getRoomId(), loginUser.getId() + "")) {
            roomCheckPassEnterResponseDto.setEnterPassWordStatus(1);
        }
        return roomCheckPassEnterResponseDto;
    }

    /**
     * im 房间邀请记录query status
     *
     * @param dto
     * @param loginUser
     * @return
     */
    public RoomInviteStatusQueryResponseDto acceptQuery(RoomTicketBaseDto dto, ZnsUserEntity loginUser) {
        var roomInviteStatusQueryResponseDto = new RoomInviteStatusQueryResponseDto();
        RoomInviteRecordDo roomInviteRecordDo = roomInviteRecordService.findByQuery(RoomInviteRecordQuery.builder().userId(loginUser.getId()).roomId(dto.getRoomId()).build());
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(dto.getRoomId()).build());
        if (Objects.nonNull(roomDo)) {
            roomInviteStatusQueryResponseDto.setRoomStatus(roomDo.getRoomStatus());
            if (Objects.nonNull(roomInviteRecordDo)) {
                roomInviteStatusQueryResponseDto.setAcceptStatus(roomInviteRecordDo.getStatus());
            } else {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.invite.check"), CommonError.BUSINESS_ERROR.getCode());
            }
        } else {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.exist.check"), CommonError.BUSINESS_ERROR.getCode());
        }
        return roomInviteStatusQueryResponseDto;
    }

    public RoomCheckCompanionResponseDto checkMemberCompanion(Long roomId, ZnsUserEntity loginUser) {
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(roomId).build());
        RoomCheckCompanionResponseDto roomCheckCompanionResponseDto = new RoomCheckCompanionResponseDto();
        roomCheckCompanionResponseDto.setCanJoin(YesNoStatus.YES.getCode());
        // 房主不进行校验直接pass
        if (loginUser.getId().equals(roomDo.getOwnerUserId())) {
            return roomCheckCompanionResponseDto;
        }
        CompanionUserDo currentUser = companionUserService.findByQuery(new CompanionUserQuery().setUserId(loginUser.getId()).setCompanionStatus(CompanionStatusEnum.PASSED.getStatus()));
        CompanionUserDo roomOwnerUser = companionUserService.findByQuery(new CompanionUserQuery().setUserId(roomDo.getOwnerUserId()).setCompanionStatus(CompanionStatusEnum.PASSED.getStatus()));
        if (Objects.nonNull(currentUser) && Objects.nonNull(roomOwnerUser)) {
            roomCheckCompanionResponseDto.setCanJoin(YesNoStatus.NO.getCode());
        } else {
            if (Objects.nonNull(currentUser) && roomDo.getCreateType().equals(COMPANION.getCode())) {
                roomCheckCompanionResponseDto.setCanJoin(YesNoStatus.NO.getCode());
            }
        }
        return roomCheckCompanionResponseDto;
    }

    /**
     * 加入房间rest
     *
     * @param roomId
     * @param loginUser
     * @param appVersion
     * @param appType
     * @return
     */
    public RoomJoinResponseDto joinAppRoom(Long roomId, ZnsUserEntity loginUser, Integer appVersion, Integer appType) {
        RoomJoinResponseDto roomJoinResponseDto = new RoomJoinResponseDto();
        // 版本兼容
        // todo 强升版本后去除
        if (appVersion < 4044 && getRoomVersionControlSwitch()) {
            if (appType == 2) {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.version.notice"));
            } else {
                // 安卓旧版本单独修改
                roomJoinResponseDto.setErrorCode(1006);
                roomJoinResponseDto.setMsg(I18nMsgUtils.getMessage("room.error.version.notice"));
                return roomJoinResponseDto;
            }
        }
        // 加入房间socket
        Result joinAppRoom = socketPushUtils.joinAppRoom(roomId, loginUser);
        if (!joinAppRoom.getCode().equals(CommonError.SUCCESS.getCode())) {
            roomJoinResponseDto.setErrorCode(joinAppRoom.getCode());
            roomJoinResponseDto.setMsg(joinAppRoom.getMsg());
        }
        return roomJoinResponseDto;
    }

    private boolean getRoomVersionControlSwitch() {
        String switchConfig = sysConfigService.selectConfigByKey(ConfigKeyEnums.ROOM_VERSION_CONTROL_SWITCH.getCode());
        if (StringUtils.hasText(switchConfig) && Integer.parseInt(switchConfig) == 1) {
            return true;
        }
        return false;
    }

    public void updateRoomConfig(RoomUpdateRequestDto requestDto, ZnsUserEntity loginUser) {
        Long roomId = requestDto.getId();
        RoomDo room = roomConverter.toDo(requestDto);
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(roomId).build());
        if (!roomDo.getRoomType().equals(room.getRoomType())
                && requestDto.getRoomType().equals(RoomTypeEnum.PRIVATE.getCode())) {
            room.setPasswordHash(String.valueOf(RandomUtil.randomInt(1000, 9999)));
        }
        room.setId(roomId);
        RoomConfigDo roomConfigDo = roomConfigConverter.toDo(requestDto);
        RoomConfigDo byQuery = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomId).build());
        roomService.update(room);
        roomConfigDo.setId(byQuery.getId());
        roomConfigService.update(roomConfigDo);
        Result sendRoomMsg = socketPushUtils.changeRoomConfig(room, loginUser);
        if (!sendRoomMsg.getCode().equals(CommonError.SUCCESS.getCode())) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.update.notice"));
        }
    }

    @SneakyThrows
    public RoomResponseDto createRoomV2(RoomDo room, RoomConfigDo roomConfigDo, ZnsUserEntity loginUser, RoomCreateRequestDto requestDto) {
        List<Long> of = requestDto.getOpponentUserIds();
        preCheck(loginUser, room);
        RoomResponseDto roomResponseDto = new RoomResponseDto();
        room.setOwnerUserId(loginUser.getId());
        room.setRoomNumber(genRoomNumber());
        if (room.getRoomType() == 1) {
            room.setPasswordHash(String.valueOf(RandomUtil.randomInt(1000, 9999)));
        }
        if (room.getTimeType() == 0) {
            room.setStartTime(ZonedDateTime.now());
        }
        // 4.1.0 陪跑员查询房间确认
        CompanionUserDo companionUser = companionUserService.findByQuery(new CompanionUserQuery().setUserId(loginUser.getId()).setCompanionStatus(CompanionStatusEnum.PASSED.getStatus()));
        if (Objects.nonNull(companionUser)) {
            room.setCreateType(COMPANION.getCode());
        }
        roomService.create(room);
        //判断是不是俱乐部创建(俱乐部内创建&&俱乐部赛创建)
        if (Objects.nonNull(requestDto.getClubId())) {
            ClubActivityRoomRelationDo clubActivityRoomRelationDo = new ClubActivityRoomRelationDo();
            clubActivityRoomRelationDo.setClubId(requestDto.getClubId());
            clubActivityRoomRelationDo.setRoomId(room.getId());
            clubActivityRoomRelationDo.setActivityId(requestDto.getActivityId());
            clubActivityRoomRelationDo.setIsClubEvent(YesNoStatus.YES.getCode());
            clubActivityRoomRelationService.create(clubActivityRoomRelationDo);
//            clubPushManager.sendClubActivityNotice(requestDto.getClubId());
        }
        roomConfigDo.setRoomId(room.getId());
        roomConfigService.create(roomConfigDo);
        roomResponseDto.setId(room.getId());
        if (room.getTimeType() == 0) {
            String roomGroupId = "room:" + room.getId().toString();
            RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.CREATE_APP_ROOM_MSG.getEvent());
            ImParamDto imParamDto = ImParamDto.builder().build();
            strategyByEvent.handlerEvent(loginUser, room.getId(), imParamDto, roomGroupId);
            if (room.getRoomMemberType() == 0 && !CollectionUtils.isEmpty(of)) {
                ZnsUserEntity userServiceById = znsUserService.findById(of.get(0));
                Map<String, Object> data = new HashMap<>();
                data.put("userId", room.getOwnerUserId());
                data.put("room", room);
                data.put("roomConfig", roomConfigDo);
                data.put("hImage", loginUser.getHeadPortrait());
                data.put("nickName", loginUser.getFirstName());
                RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
                data.put("routePic", routeDetailVO.getRouteThumbnail());
                RoomInviteRecordDo roomInviteRecordDo = new RoomInviteRecordDo()
                        .setInviteUserId(loginUser.getId())
                        .setTargetUserId(of.get(0))
                        .setRoomId(room.getId())
                        .setStartTime(room.getStartTime())
                        .setStatus(0);
                roomInviteRecordService.create(roomInviteRecordDo);
                Integer appType = userServiceById.getAppSystem();
//                appMessageService.sendRoomPushAndIm(roomInviteRecordDo, room, roomConfigDo, appType, routeDetailVO.getRouteThumbnail(), loginUser);
                // sendMsg invite Event socket
                RoomEventStrategy strategyByEventInvite = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.INVITE_USER_ROOM_MSG.getEvent());
                ImParamDto imParamDtoInvite = ImParamDto.builder().targetUserId(roomInviteRecordDo.getTargetUserId()).build();
                strategyByEventInvite.handlerEvent(loginUser, room.getId(), imParamDtoInvite, roomGroupId);
            }
        } else {
            // 预约逻辑用户(增加房间创建者的预约记录)
            RoomBookingDo roomStarter = new RoomBookingDo();
            roomStarter.setUserId(loginUser.getId());
            roomStarter.setRoomId(room.getId());
            roomStarter.setUserType(0);
            roomStarter.setStartTime(room.getStartTime());
            roomBookingService.create(roomStarter);
            room.setRoomStatus(RoomStatusEnum.ORDERED_NOT_STARTED.getCode());
            roomService.update(room);
            if (!CollectionUtils.isEmpty(of)) {
                of.forEach(i -> {
                    RoomBookingDo roomBookingDo = new RoomBookingDo();
                    roomBookingDo.setUserId(i);
                    roomBookingDo.setRoomId(room.getId());
                    roomBookingDo.setUserType(1);
                    roomBookingDo.setStartTime(room.getStartTime());
                    roomBookingService.create(roomBookingDo);
                });
            }
        }
        createRoomDingTalkReminder(room, loginUser);
        //发布创建房间成功tb事件
        queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.TurbolinkApplicationEvent.getEventType(),new TurbolinkApplicationEvent(TurbolinkEventEnum.CREATE_ROOM, loginUser.getId(),
                Map.of("isCreated", Boolean.TRUE.toString())));
        return roomResponseDto;
    }

    public void dismissRoomV2(Long roomId, ZnsUserEntity loginUser) {
        String roomGroupId = "room:" + roomId.toString();
        RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.DISMISS_ROOM_MSG.getEvent());
        ImParamDto imParamDto = ImParamDto.builder().build();
        strategyByEvent.handlerEvent(loginUser, roomId, imParamDto, roomGroupId);
    }

    /**
     * socket event im send 统一
     *
     * @param loginUser
     * @param roomEventRequestDto
     */
    public void socketEventIm(ZnsUserEntity loginUser, RoomEventRequestDto roomEventRequestDto) {
        String roomGroupId = "room:" + roomEventRequestDto.getRoomId().toString();
        RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(roomEventRequestDto.getSocketEvent());
        strategyByEvent.handlerEvent(loginUser, roomEventRequestDto.getRoomId(), roomEventRequestDto.getImParam(), roomGroupId);
    }

    public void inviteUserV2(RoomInviteUserDto dto, ZnsUserEntity loginUser) {
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(dto.getRoomId()).build());
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
        //用户发送邀请信息 异步优化
        CompletableFuture.runAsync(() -> dto.getOpponentUserIds().forEach(i -> {
                    RoomInviteRecordDo roomInviteRecordDo = roomInviteRecordService.findByQuery(RoomInviteRecordQuery.builder().userId(i).roomId(dto.getRoomId()).build());
                    if (Objects.nonNull(roomInviteRecordDo)) {
                        roomInviteRecordDo.setStatus(0);
                        roomInviteRecordService.update(roomInviteRecordDo);
                    } else {
                        RoomInviteRecordDo roomInviteRecordDoNew = new RoomInviteRecordDo()
                                .setInviteUserId(loginUser.getId())
                                .setTargetUserId(i)
                                .setRoomId(dto.getRoomId())
                                .setStartTime(roomDo.getStartTime())
                                .setStatus(0);
                        roomInviteRecordService.create(roomInviteRecordDoNew);
                        roomInviteRecordDo = roomInviteRecordDoNew;
                    }
                    // 房主邀请密码直接给了
                    if (roomDo.getOwnerUserId().equals(loginUser.getId())) {
                        roomInviteRecordDo.setPasswordHash(roomDo.getPasswordHash());
                    }
                    RunRouteDetailVO routeDetailVO = runRouteService.runRouteDetail(roomConfigDo.getRouteId());
                    ZnsUserEntity userServiceById = znsUserService.findById(i);
                    Integer appType = userServiceById.getAppSystem();
                    appMessageService.sendRoomPushAndIm(roomInviteRecordDo, roomDo, roomConfigDo, appType, routeDetailVO.getRouteThumbnail(), loginUser);
                    String roomGroupId = "room:" + dto.getRoomId().toString();
                    // sendMsg Event socket
                    RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.INVITE_USER_ROOM_MSG.getEvent());
                    ImParamDto imParamDto = ImParamDto.builder().targetUserId(roomInviteRecordDo.getTargetUserId()).build();
                    strategyByEvent.handlerEvent(loginUser, dto.getRoomId(), imParamDto, roomGroupId);
                }), createRoomExecutor)
                .exceptionally(ex -> {
                    // 处理 CompletableFuture 执行过程中的异常
                    log.error("inviteUserV2 error", ex);
                    return null;
                });
    }

    public void handleAcceptV2(RoomHandleAcceptDto dto, ZnsUserEntity loginUser) {
        RoomInviteRecordDo roomInviteRecordDo = roomInviteRecordService.findByQuery(RoomInviteRecordQuery.builder().userId(loginUser.getId()).roomId(dto.getRoomId()).inviteUserId(dto.getInviteUserId()).build());
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(dto.getRoomId()).build());
        if (Objects.nonNull(roomDo) && roomDo.getRoomStatus() != 2) {
            if (Objects.nonNull(roomInviteRecordDo)) {
                roomInviteRecordDo.setStatus(dto.getAcceptStatus());
                roomInviteRecordService.update(roomInviteRecordDo);
                if (dto.getAcceptStatus() == 2) {
                    ZnsUserEntity user = znsUserService.findById(roomInviteRecordDo.getInviteUserId());
                    String roomGroupId = "room:" + dto.getRoomId().toString();
                    RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.INVITE_USER_ROOM_REFUSE_MSG.getEvent());
                    ImParamDto imParamDto = ImParamDto.builder().targetUserId(roomInviteRecordDo.getInviteUserId()).build();
                    strategyByEvent.handlerEvent(user, dto.getRoomId(), imParamDto, roomGroupId);
                }
            } else {
                throw new BaseException(I18nMsgUtils.getMessage("room.error.invite.check"), CommonError.BUSINESS_ERROR.getCode());
            }
        } else {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.exist.check"), CommonError.BUSINESS_ERROR.getCode());
        }
    }

    @SneakyThrows
    public RoomRoomActivityResponseDto startActivityInRoomV2(ZnsUserEntity loginUser, Long roomId, List<Long> opponentUserIds) {

        HomeLevelLockResponse homeLevelLockResponse = putChannelUserBizService.homeLevelLock(loginUser);
        HomeLevelLockResponse.Level homeLevelLockResponseUser = homeLevelLockResponse.getUser();
        if (homeLevelLockResponseUser != null && PutChannelUserLevelStatus.LOCKED.equals(homeLevelLockResponseUser.getLevelStatus())) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.home.level.lock"));
        }

        List<RoomMemberResponseDto> roomMembers = socketPushUtils.getRoomMembers(roomId);
        RoomDo roomDo = roomService.findById(roomId);
        if (CollectionUtils.isEmpty(roomMembers)) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.no.members"), CommonError.BUSINESS_ERROR.getCode());
        }
        if (roomMembers.size() < 2) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.members.limit"), CommonError.BUSINESS_ERROR.getCode());
        }
        if (roomMembers.stream().anyMatch(e -> e.getStatus().equals(0) && !e.getUserId().equals(roomDo.getOwnerUserId()))) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.ready.check"), CommonError.BUSINESS_ERROR.getCode());
        }
        //房间陪跑员数量校验check
        checkCompanionUserNumCheck(loginUser, opponentUserIds, roomDo);
        // 房主 参赛次数limit check
        List<RoomParticipantsDo> list = roomParticipantsService.findList(RoomParticipantsQuery.builder().userId(loginUser.getId())
                .startTime(ZonedDateTime.now().withHour(0).withMinute(0).withSecond(0))
                .endTime(ZonedDateTime.now().withHour(23).withMinute(59).withSecond(59)).build());
        VisualUserActivityConfigVo config = new VisualUserActivityConfigVo();
        String configStr = sysConfigService.selectConfigByKey("visual_user_activity_config");
        config = JsonUtil.readValue(configStr, VisualUserActivityConfigVo.class);
        if (list.size() >= Objects.requireNonNull(config).getOneDayLimitJoinRoomNum()) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.join.limit"), CommonError.BUSINESS_ERROR.getCode());
        }
        //准备状态校验
        if (roomMembers.stream().anyMatch(e -> !e.getStatus().equals(1))) {
            throw new BaseException(I18nMsgUtils.getMessage("room.error.ready.check"), CommonError.BUSINESS_ERROR.getCode());
        }
        ClubActivityRoomRelationDo clubActivityRoomRelationDo = clubActivityRoomRelationService.findByQuery(ClubActivityRoomRelationQuery.builder().roomId(roomId).build());
        RoomRoomActivityResponseDto roomRoomActivityResponseDto = new RoomRoomActivityResponseDto();
        RoomConfigQuery configQuery = RoomConfigQuery.builder().roomId(roomDo.getId()).build();
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(configQuery);
        NewPkActivityRequestDto dto = new NewPkActivityRequestDto();
        dto.setActivityTypeSub(roomDo.getRoomMemberType() == 0 ? RunActivitySubTypeEnum.NEW_1_V_1_PK.getType() : RunActivitySubTypeEnum.NEW_TEAM_PK.getType());
        opponentUserIds.add(loginUser.getId());
        dto.setActivityUserIds(opponentUserIds.stream().distinct().toList());
        dto.setActivityType(RunActivityTypeEnum.NEW_PK_ACTIVITY.getType());
        dto.setActivityRouteId(roomConfigDo.getRouteId());
        dto.setActivityStartTime(ZonedDateTime.now().toInstant().toEpochMilli());
        if (roomConfigDo.getTargetType() == 0) {
            dto.setRunMileage(roomConfigDo.getTargetMileage());
            dto.setCompleteRuleType(1);
        } else {
            dto.setRunTime(roomConfigDo.getTargetTime());
            dto.setCompleteRuleType(2);
        }
        //查询房间号
        dto.setAppRoomId(roomDo.getRoomNumber());
        dto.setBonusRuleType(roomConfigCoverBonusRuleType(roomConfigDo.getTicketType()));
        dto.setActivityEntryFee(roomConfigDo.getTicketPrice());
        dto.setActivityEntryScore(roomConfigDo.getTicketPrice().intValue());
        dto.setInitiatorUserId(roomDo.getOwnerUserId());
        dto.setClubId(Objects.nonNull(clubActivityRoomRelationDo) ? clubActivityRoomRelationDo.getClubId() : null);
        dto.setMainActivityId(Objects.nonNull(clubActivityRoomRelationDo) && Objects.nonNull(clubActivityRoomRelationDo.getActivityId()) ? clubActivityRoomRelationDo.getActivityId() : null);
        RLock lock = redissonClient.getLock(RedisConstants.LOCK_ROOM_ACTIVITY_START + roomId);
        try {
            if (!lock.tryLock(2, TimeUnit.SECONDS)) {
                throw new BaseException(CommonError.DUPLICATE_REQUEST.getMsg(), CommonError.DUPLICATE_REQUEST.getCode());
            }

            ZnsRunActivityEntity newPkActivity = activityManager.createNewPkActivity(dto, roomDo.getRoomMode(), roomId);
            roomRoomActivityResponseDto.setActivityId(newPkActivity.getId());
            roomRoomActivityResponseDto.setActivityStartTime(newPkActivity.getActivityStartTime());
            // 房间活动扩展信心save
            saveRoomActivityExtInfo(roomId, newPkActivity, roomDo);
            // socket 消息推送
            if (!opponentUserIds.isEmpty()) {
                RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.START_GAME_MSG.getEvent());
                String roomGroupId = "room:" + roomId;
                ImParamDto imParamDto = ImParamDto.builder().activityId(newPkActivity.getId()).build();
                strategyByEvent.handlerEvent(loginUser, roomId, imParamDto, roomGroupId);
                List<RoomParticipantsDo> listUser = new ArrayList<>();
                opponentUserIds.stream().distinct().forEach(i -> {
                    var roomParticipantsDo = new RoomParticipantsDo();
                    roomParticipantsDo.setActivityId(newPkActivity.getId()).setUserId(i).setRoomId(roomId).setJoinTime(ZonedDateTime.now());
                    listUser.add(roomParticipantsDo);
                });
                roomParticipantsService.batchCreate(listUser);
                roomDo.setRoomStatus(1);
                roomDo.setActivityNum(roomDo.getActivityNum() + 1);
                roomService.update(roomDo);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return roomRoomActivityResponseDto;
    }

    private void saveRoomActivityExtInfo(Long roomId, ZnsRunActivityEntity newPkActivity, RoomDo roomDo) {
        RoomActivityExtInfoDo roomActivityExtInfoDo = new RoomActivityExtInfoDo();
        roomActivityExtInfoDo.setActivityId(newPkActivity.getId());
        roomActivityExtInfoDo.setRoomId(roomId);
        roomActivityExtInfoDo.setActivityMode(roomDo.getRoomMode());
        roomActivityExtInfoService.create(roomActivityExtInfoDo);
    }

    public void updateRoomConfigV2(RoomUpdateRequestDto requestDto, ZnsUserEntity loginUser) {
        Long roomId = requestDto.getId();
        RoomDo room = roomConverter.toDo(requestDto);
        RoomDo roomDo = roomService.findByQuery(RoomQuery.builder().id(roomId).build());
        if (!roomDo.getRoomType().equals(room.getRoomType())
                && requestDto.getRoomType().equals(RoomTypeEnum.PRIVATE.getCode())) {
            room.setPasswordHash(String.valueOf(RandomUtil.randomInt(1000, 9999)));
        }
        room.setId(roomId);
        RoomConfigDo roomConfigDo = roomConfigConverter.toDo(requestDto);
        RoomConfigDo byQuery = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomId).build());
        roomService.update(room);
        roomConfigDo.setId(byQuery.getId());
        roomConfigService.update(roomConfigDo);
        String roomGroupId = "room:" + roomId;
        RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.CHANGE_ROOM_CONFIG_MSG.getEvent());
        ImParamDto imParamDto = ImParamDto.builder().roomMemberType(room.getRoomMemberType()).roomType(room.getRoomType()).build();
        strategyByEvent.handlerEvent(loginUser, roomId, imParamDto, roomGroupId);
    }

    public void endRoomBeforeLogout(ZnsUserEntity loginUser, Long roomId) {
        RoomDo roomDo = roomService.findById(roomId);
        if (roomDo.getOwnerUserId().equals(loginUser.getId())) {
            //房主解散
            dismissRoomV2(roomId, loginUser);
        } else {
            //普通用户退出
            ImParamDto imParamDto = ImParamDto.builder().build();
            String roomGroupId = "room:" + roomId;
            RoomEventStrategy strategyByEvent = RoomEventStrategyFactory.getStrategyByEvent(SocketEventEnum.LEAVE_APP_ROOM_MSG.getEvent());
            strategyByEvent.handlerEvent(loginUser, roomId, imParamDto, roomGroupId);
        }
        RoomConfigDo roomConfigDo = roomConfigService.findByQuery(RoomConfigQuery.builder().roomId(roomDo.getId()).build());
        if (roomConfigDo.getTicketType() != 0) {
            // 门票查询
            RoomTicketLogDo byQuery = roomTicketLogService.findByQuery(RoomTicketLogQuery.builder().userId(loginUser.getId()).roomId(roomId).ticketStatus(0).build());
            if (Objects.nonNull(byQuery)) {
                //退门票
                if (byQuery.getTicketStatus() == 0) {
                    refundRoomTicket(byQuery.getUserId(), byQuery);
                }
            }
        }
    }

    public RoomResponseDto wrapperUserCountry(RoomResponseDto roomResponseDto, ZnsUserEntity ownerUser) {
        if (Objects.isNull(ownerUser)) {
            return roomResponseDto;
        }
        roomResponseDto.setCountry(ownerUser.getCountry());
        I18nConstant.CountryCodeEnum countryEnum = I18nConstant.CountryCodeEnum.findByEnName(ownerUser.getCountry());
        String flag = CountryFlagConstant.FlagMap.get(Optional.ofNullable(countryEnum).orElse(I18nConstant.CountryCodeEnum.DEFAULT).enName);
        //设置用户国旗
        roomResponseDto.setCountryImage(flag);
        //用户设备
        return roomResponseDto;
    }


    //用户房间标签
    public RoomResponseDto wrapperRoomTag(RoomResponseDto roomResponseDto, ZnsUserEntity loginUser, ZnsUserEntity ownerUser) {
        List<RoomConstants.RoomTagTypeEnum> tagLists = new ArrayList<>(List.of(
                RoomConstants.RoomTagTypeEnum.COMMON_EVENT,
                RoomConstants.RoomTagTypeEnum.COMMON_FRIEND,
                RoomConstants.RoomTagTypeEnum.COMMON_CLUB
        ));
        Collections.shuffle(tagLists);
        List<RoomConstants.RoomTagTypeEnum> finalTagLists = tagLists.subList(0, 2);
        List<RoomTagDto> roomTags = new ArrayList<>();
        if (ownerUser.getIsRobot().equals(YesNoStatus.YES.getCode()) ||
                roomResponseDto.getOwnerUserId().equals(loginUser.getId())) {
            // 机器人/自己本人房间 不做逻辑
            roomResponseDto.setRoomTags(roomTags);
            return roomResponseDto;
        }
        // 距离必获取 直接从结果获取 废除 redis geohash 结果后获取距离不满足产品要求
        Long distance = roomResponseDto.getDistance();
        if (Objects.nonNull(distance)) {
            RoomTagDto roomTagDto = new RoomTagDto();
            roomTagDto.setTagType(RoomConstants.RoomTagTypeEnum.DISTANCE.getCode());
            roomTagDto.setParam("" + distance);
            roomTags.add(roomTagDto);
        }
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (RoomConstants.RoomTagTypeEnum tag : finalTagLists) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                RoomTagDto roomTagDto = new RoomTagDto();
                if (tag.getCode() == RoomConstants.RoomTagTypeEnum.COMMON_CLUB.getCode()) {
                    Long clubId = clubMemberService.findCommonClubNumberByUserId(loginUser.getId(), roomResponseDto.getOwnerUserId());
                    if (Objects.nonNull(clubId)) {
                        Club club = clubService.findById(clubId);
                        if (Objects.nonNull(club)) {
                            roomTagDto.setParam(club.getName());
                        }
                    }
                }
                if (tag.getCode() == RoomConstants.RoomTagTypeEnum.COMMON_FRIEND.getCode()) {
                    Long friendOne = znsUserFriendService.findCommonFriendOne(loginUser.getId(), roomResponseDto.getOwnerUserId());
                    if (Objects.nonNull(friendOne)) {
                        ZnsUserEntity znsUserEntity = znsUserService.findById(friendOne);
                        if (Objects.nonNull(znsUserEntity)) {
                            roomTagDto.setParam(znsUserEntity.getFirstName());
                        }
                    }
                }
                if (tag.getCode() == RoomConstants.RoomTagTypeEnum.COMMON_EVENT.getCode()) {
                    List<Long> activities = runActivityUserService.queryActivityidsByUserId30DaysNoDS(loginUser.getId());
                    List<Long> ownerActivities = runActivityUserService.queryActivityidsByUserId30DaysNoDS(roomResponseDto.getOwnerUserId());
                    // 使用 Stream 的 filter 和 collect 方法找出交集元素
                    List<Long> intersection = activities.stream()
                            .filter(ownerActivities::contains)
                            .toList();
                    if (!intersection.isEmpty()) {
                        roomTagDto.setParam("" + intersection.size());
                    }
                    // 返回交集元素的数量
                }
                if (StringUtils.hasText(roomTagDto.getParam())) {
                    roomTagDto.setTagType(tag.getCode());
                    roomTags.add(roomTagDto);
                }
            }, roomTagExecutor);
            futures.add(future);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();
        // 返回截取两个
        if (roomTags.size() > 2) {
            Collections.shuffle(roomTags);
            roomResponseDto.setRoomTags(roomTags.subList(0, 2));
        } else {
            roomResponseDto.setRoomTags(roomTags);
        }
        return roomResponseDto;
    }


    public RoomResponseDto wrapperIsFullMemberOne(RoomResponseDto roomResponseDto) {
        if (roomResponseDto.getCreateType() != 1) {
            roomResponseDto.setIsFullMember(YesNoStatus.NO.getCode());
            if (roomResponseDto.getRoomMemberType() == 0) {
                Set users = redisTemplate.opsForSet().members(RedisConstants.APP_ROOM_MEMBER_MAP + roomResponseDto.getId());
                if (Objects.requireNonNull(users).size() > 1) {
                    roomResponseDto.setIsFullMember(YesNoStatus.YES.getCode());
                }
            }
        } else {
            roomResponseDto.setIsFullMember(YesNoStatus.YES.getCode());
        }
        return roomResponseDto;
    }

    public RoomPropRouteConfigDto getRoomPropRouteConfig() {
        RoomPropRouteConfigDto roomPropRouteConfigDto = new RoomPropRouteConfigDto();
        List<RoomModeDto> modeList = new ArrayList<>();
        // 国际化标题
        String langCode = I18nMsgUtils.getLangCode();
        List<RoomModeRouteI18nDo> routeI18nServiceList = roomModeRouteI18nService.findList(new RoomModeRouteI18nQuery().setLangCode(langCode));
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(RunActivityTypeEnum.NEW_PK_ACTIVITY.getType(), RunActivitySubTypeEnum.NEW_1_V_1_PK.getType());
        if (null == activityConfig) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.error"), CommonError.BUSINESS_ERROR.getCode());
        }
        // 活动配置
        String config = activityConfig.getActivityConfig();
        RoomRouteModeConfigVo modeConfigVo = JsonUtil.readValue(config, RoomRouteModeConfigVo.class);
        List<RoomModeDto> modeListConfig = null;
        if (modeConfigVo != null) {
            modeListConfig = modeConfigVo.getModeList();
            Map<String, RoomModeDto> collect = modeListConfig.stream().collect(Collectors.toMap(RoomModeDto::getModeName, Function.identity(),
                    (existingValue, newValue) -> existingValue));
            routeI18nServiceList.forEach(routeType -> {
                RoomModeDto roomModeDto = new RoomModeDto();
                roomModeDto.setModeName(routeType.getModeName());
                roomModeDto.setModeIntroduce(routeType.getModeIntroduce());
                roomModeDto.setModeImage(routeType.getModeImage());
                RoomModeDto modeDto = collect.get(routeType.getModeName());
                List<RoomRouteDto> routeList = modeDto.getRouteList();
                routeList.forEach(route -> {
                    RunRouteDetailVO runRouteDetailVO = runRouteService.runRouteDetail(route.getRouteId());
                    route.setRouteTitle(runRouteDetailVO.getRouteTitle());
                    route.setRouteThumbnail(runRouteDetailVO.getRouteThumbnail());
                });
                roomModeDto.setRouteList(routeList);
                modeList.add(roomModeDto);
            });
            roomPropRouteConfigDto.setModeList(modeList);
        }
        roomPropRouteConfigDto.setModeList(modeList);
        return roomPropRouteConfigDto;
    }

    public List<RoomResponseDto> wrapperRoomResponseDto(List<RoomResponseDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        List<Long> ownerUserList = list.stream().map(RoomResponseDto::getOwnerUserId).toList();
        Map<Long, ZnsUserEntity> userEntityMap = userService.findByIds(ownerUserList).stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        return list.stream()
                .map(this::wrapperIsFullMemberOne)
                .map(dto -> this.wrapperUserCountry(dto, userEntityMap.get(dto.getOwnerUserId())))
                .map(dto -> userEquipmentBizService.wrapperUserEquipment(dto, userEntityMap.get(dto.getOwnerUserId())))
                .collect(Collectors.toList());
    }

    public List<RoomResponseDto> wrapperRoomPageResponseDto(List<RoomResponseDto> list, ZnsUserEntity loginUser) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        List<Long> ownerUserList = list.stream().map(RoomResponseDto::getOwnerUserId).toList();
        Map<Long, ZnsUserEntity> userEntityMap = userService.findByIds(ownerUserList).stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        return list.stream()
                .map(this::wrapperIsFullMemberOne)
                .map(dto -> this.wrapperUserCountry(dto, userEntityMap.get(dto.getOwnerUserId())))
                .map(dto -> userEquipmentBizService.wrapperUserEquipment(dto, userEntityMap.get(dto.getOwnerUserId())))
                .map(dto -> wrapperRoomTag(dto, loginUser, userEntityMap.get(dto.getOwnerUserId())))
                .collect(Collectors.toList());
    }
}
