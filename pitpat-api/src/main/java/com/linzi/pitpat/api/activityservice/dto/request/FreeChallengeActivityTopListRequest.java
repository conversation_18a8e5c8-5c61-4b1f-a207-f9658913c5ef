package com.linzi.pitpat.api.activityservice.dto.request;

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/7/2
 */
@Data
public class FreeChallengeActivityTopListRequest extends PageQuery {
    /**
     * 活动id，对应不同榜单，设备位置
     */
    private Long activityId;
    /**
     * 是否刷新,0不刷新    1刷新
     */
    private Integer isRefresh = 1;
}
