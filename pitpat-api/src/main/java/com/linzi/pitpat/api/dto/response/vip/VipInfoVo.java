package com.linzi.pitpat.api.dto.response.vip;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
public class VipInfoVo {
    //邮箱
    private String email;
    //头像
    private String headPortrait;
    //昵称
    private String userName;
    //会员过期时间
    private ZonedDateTime expiredTime;
    //会员类型
    private Integer memberType;

    //能够更换的会员套餐
    List<VipComboDto> vipCombos;

    //是否已免费试用过 false:未试用，true:试用
    private Boolean isFreeUsedForMonth = false;

    /**
     * 是否显示paypal
     */
    private Boolean showPaypal;
}
