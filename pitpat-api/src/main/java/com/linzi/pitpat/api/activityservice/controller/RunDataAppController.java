package com.linzi.pitpat.api.activityservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.converter.RunDataDetailsHeartRateConverter;
import com.linzi.pitpat.api.activityservice.converter.UserRunDataDetailsHeartRateConverter;
import com.linzi.pitpat.api.activityservice.dto.request.UploadHeartRateRequest;
import com.linzi.pitpat.api.activityservice.manager.UserRunningReportManager;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ListUtils;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RunDataBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.request.ActivityDataDetailsListRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.ActivityDataDetailsResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.RunSpeedLimitNoticeResponseDto;
import com.linzi.pitpat.data.activityservice.manager.RunEndActivityManager;
import com.linzi.pitpat.data.activityservice.manager.api.RealPersonRunDataDetailsManager;
import com.linzi.pitpat.data.activityservice.manager.api.UserRunDataManager;
import com.linzi.pitpat.data.activityservice.manager.api.UserRunDataReportManager;
import com.linzi.pitpat.data.activityservice.model.entity.PkChallengeRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsHeartRate;
import com.linzi.pitpat.data.activityservice.model.request.RiskCheatResultRequestDto;
import com.linzi.pitpat.data.activityservice.model.resp.SeriesRunningReportResp;
import com.linzi.pitpat.data.activityservice.model.vo.ChallengeRunRunningReportListVO;
import com.linzi.pitpat.data.activityservice.service.PkChallengeRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.CalorieConsumptionFoodCalibration;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.enums.RunMileageCopyWritingEnum;
import com.linzi.pitpat.data.request.RunDataStatisticsRequest;
import com.linzi.pitpat.data.request.runData.ChallengeRunRequest;
import com.linzi.pitpat.data.request.runData.DataConversionPo;
import com.linzi.pitpat.data.request.runData.DetailIdRequest;
import com.linzi.pitpat.data.request.runData.RunTypePo;
import com.linzi.pitpat.data.robotservice.service.RobotManageControlService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.request.UserIdRequest;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.MapListVo;
import com.linzi.pitpat.data.vo.RunDataDayDetailVO;
import com.linzi.pitpat.data.vo.report.ActivityRunningReportBaseVo;
import com.linzi.pitpat.data.vo.report.ChallengeRunReportVo;
import com.linzi.pitpat.data.vo.report.RankingRunReportVo;
import com.linzi.pitpat.data.vo.runData.CourseReportVo;
import com.linzi.pitpat.data.vo.runData.DataConversionVo;
import com.linzi.pitpat.data.vo.runData.LastIdAndStatusVo;
import com.linzi.pitpat.data.vo.runData.RunRecordDetailsVo;
import com.linzi.pitpat.data.vo.runData.RunningRecordListVo;
import com.linzi.pitpat.data.vo.runData.RunningRecordVo;
import com.linzi.pitpat.data.vo.runData.RunningReportPopVo;
import com.linzi.pitpat.data.vo.runData.RunningReportVo;
import com.linzi.pitpat.data.vo.runData.ShareDataVo;
import com.linzi.pitpat.data.vo.runData.TotalRunRecordVo;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.user.api.dto.FinishCourseUserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 运动数据接口
 *
 * @description: 运动数据
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/app/runData")
@Slf4j
@RequiredArgsConstructor
public class RunDataAppController extends BaseAppController {

    @Resource
    private UserRunDataReportManager userRunDataReportManager;
    @Resource
    private RunDataBizService runDataBizService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ZnsUserService userService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;
    private final ISysConfigService sysConfigService;
    @Resource
    private ZnsUserAccountService userAccountService;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RedisTemplate redisTemplate;

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private PkChallengeRecordService pkChallengeRecordService;

    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    @Autowired
    private UserRunningReportManager userRunningReportManager;

    @Resource
    private ActivityAwardCurrencyBizService activityAwardCurrencyBizService;
    @Resource
    private MainActivityBizService mainActivityBizService;
    @Resource
    private RealPersonRunDataDetailsManager realPersonRunDataDetailsManager;
    private final RunEndActivityManager runEndActivityManager;
    @Resource
    private ZnsUserFriendService znsUserFriendService;
    private final RunDataDetailsHeartRateConverter runDataDetailsHeartRateConverter;
    private final UserRunDataManager userRunDataManager;
    private final UserRunDataDetailsHeartRateConverter userRunDataDetailsHeartRateConverter;
    private final UserBizService userBizService;
    /**
     * 运动明细详情
     *
     * @param request
     * @return
     */
    @PostMapping("/detail")
    public Result<RunDataDayDetailVO> detail(@RequestBody DetailIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();

        RunDataDayDetailVO detail = userRunDataDetailsService.getDetailById(request.getDetailId(), loginUser.getMeasureUnit());

        return CommonResult.success(detail);
    }

    /**
     * 跑步报告
     *
     * @param request
     * @return
     */
    @PostMapping("/runningReport")
    public Result<RunningReportVo> runningReport(@RequestBody DetailIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        List<Integer> types = Lists.newArrayList(0, 1);
        if (request.getMeasureUnit() != null && types.contains(request.getMeasureUnit())) {
            //根据跑步机配速查询报告
            loginUser.setMeasureUnit(request.getMeasureUnit());
        }

        ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.findById(request.getDetailId());
        if (Objects.isNull(entity)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        if (entity.getRunStatus() != 1) {
            //防止数据未处理完
            entity = userRunDataDetailsService.waitingProcessing(entity, 500);
            if (Objects.isNull(entity) || entity.getRunStatus() != 1) {
                return CommonResult.fail(ActivityError.RUN_DATA_NO_END.getCode(), I18nMsgUtils.getMessage("activity.run.data.no.generated"));
            }
        }
        if (entity.getIsDelete() == 1) {
            return CommonResult.fail(ActivityError.RUN_DATA_IS_DELETE.getCode(), I18nMsgUtils.getMessage("activity.run.report.no.yet"));
        }
        if (Objects.isNull(request.getActivityId()) || request.getActivityId() <= 1) {
            request.setActivityId(entity.getActivityId());
        }
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(request.getActivityId(), getZoneId());
        RunningReportVo detail = userRunDataReportManager.getRunningReport(entity, loginUser, request.getActivityId(), getAppVersion(), activityNew);
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        if (Objects.nonNull(activityNew)) {
            detail.setMainType(activityNew.getMainType());
        }
        if (Objects.nonNull(request.getActivityId()) && request.getActivityId() > 1) {
            detail.setActivityId(request.getActivityId());
        }

        return CommonResult.success(detail);
    }


    /**
     * 奖励弹窗
     *
     * @param request
     * @return
     */
    @PostMapping("/runningReportPop")
    public Result<RunningReportPopVo> runningReportPop(@RequestBody DetailIdRequest request) {
        ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.findById(request.getDetailId());
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(entity)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        if (entity.getRunStatus() != 1) {
            //防止数据未处理完
            entity = userRunDataDetailsService.waitingProcessing(entity, 500);
            if (Objects.isNull(entity) || entity.getRunStatus() != 1) {
                return CommonResult.fail(ActivityError.RUN_DATA_NO_END.getCode(), I18nMsgUtils.getMessage("activity.run.data.no.generated"));
            }
        }
        if (entity.getIsDelete() == 1) {
            return CommonResult.fail(ActivityError.RUN_DATA_IS_DELETE.getCode(), I18nMsgUtils.getMessage("activity.run.report.no.yet"));
        }
        RunningReportPopVo detail = new RunningReportPopVo();
        detail.setReportDate(entity.getCreateTime().toInstant().toEpochMilli());
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(request.getActivityId(), getZoneId());
        if (Objects.nonNull(activityNew)) {
            detail.setActivityType(activityNew.getActivityType());
            if (MainActivityTypeEnum.singleTypes().contains(activityNew.getMainType())) {
                detail.setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType());
            } else if (MainActivityTypeEnum.SERIES_SUB.getType().equals(activityNew.getMainType())) {
                detail.setActivityType(RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType());
            }
        }
        detail.setActivityId(entity.getActivityId());
        // 获取奖励弹窗
        userRunningReportManager.getUserReportPopInfo(request, detail, entity, loginUser, getAppVersion());
        return CommonResult.success(detail);
    }


    /**
     * 课程报告
     *
     * @param request
     * @return
     */
    @PostMapping("/courseReport")
    public Result<CourseReportVo> courseReport(@RequestBody DetailIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        String languageCode = getLanguageCode();
        ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.findById(request.getDetailId());
        if (Objects.isNull(entity)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        }
        if (entity.getRunStatus() != 1) {
            //防止数据未处理完
            entity = userRunDataDetailsService.waitingProcessing(entity, 500);
            if (Objects.isNull(entity) || entity.getRunStatus() != 1) {
                return CommonResult.fail(ActivityError.RUN_DATA_NO_END.getCode(), ActivityError.RUN_DATA_NO_END.getMsg());
            }
        }
        if (entity.getIsDelete() == 1) {
            return CommonResult.fail(ActivityError.RUN_DATA_IS_DELETE.getCode(), ActivityError.RUN_DATA_IS_DELETE.getMsg());
        }

        CourseReportVo detail = userRunDataReportManager.getCourseReport(entity, loginUser, languageCode);
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        }
        getSameCourseUserLists(loginUser, entity, detail);
        // 2.5.添加状态字段
        if (entity.getRunTime() >= detail.getCourseDuration()) {
            detail.setTargetStatus(1);
            detail.setToast(I18nMsgUtils.getMessage("course.report.status.success.toast"));
        } else {
            detail.setTargetStatus(2);
            detail.setToast(I18nMsgUtils.getMessage("course.report.status.failed.toast"));
        }

        return CommonResult.success(detail);
    }

    /**
     * 获取相同课程用户列表。不包含自己 5个随机，机器人不起真人
     *
     * @param loginUser
     * @param entity
     * @param detail
     */
    private void getSameCourseUserLists(ZnsUserEntity loginUser, ZnsUserRunDataDetailsEntity entity, CourseReportVo detail) {
        String key = RedisConstants.SAME_COURSE_USER_LISTS_FLAG + loginUser.getId() + ":" + detail.getCourseId();
        String s = redisUtil.get(key);
        if (!StringUtils.hasText(s)) {
            redisTemplate.opsForValue().setIfAbsent(key, "1");

            List<FinishCourseUserDto> userLists = userService.selectEndCourseByCourseIdListNew(entity.getCourseId(), loginUser.getId());
            List<FinishCourseUserDto> finallyList = Lists.newArrayList();
            for (FinishCourseUserDto userList : userLists) {
                Integer friend = znsUserFriendService.isFocusFriend(loginUser.getId(), userList.getUserId());
                userList.setIsFriend(friend);
                if (friend == 0 && userList.getIsRobot() == 0) {
                    finallyList.add(userList);
                }
            }
            if (finallyList.size() >= 5) {
                Collections.shuffle(finallyList);
                finallyList = userLists.subList(0, 5);
            } else {
                List<ZnsUserEntity> robotList = userBizService.findRandomRobotListLimit(5 - finallyList.size());
//                List<ZnsUserEntity> robotList = userService.selectRobotUserListLimit(5 - finallyList.size());
                List<FinishCourseUserDto> collect = Lists.newArrayList();
                for (ZnsUserEntity znsUserEntity : robotList) {
                    FinishCourseUserDto finishCourseUserDto = new FinishCourseUserDto();
                    BeanUtils.copyProperties(znsUserEntity, finishCourseUserDto);
                    finishCourseUserDto.setUserId(znsUserEntity.getId());
                    Integer friend = znsUserFriendService.isFocusFriend(loginUser.getId(), znsUserEntity.getId());

                    finishCourseUserDto.setIsFriend(friend);
                    finishCourseUserDto.setNickName(znsUserEntity.getFirstName());
                    collect.add(finishCourseUserDto);
                }
                finallyList.addAll(collect);
            }
            detail.setSameCourseUserLists(finallyList);
        }
    }

    /**
     * 运动记录概览
     * 已废弃，后续版本可以删掉
     *
     * @param request
     * @return
     */
    @Deprecated(since = "4.3.0")
    @PostMapping("/runningRecord")
    public Result<RunningRecordVo> runningRecord(@RequestBody RunDataStatisticsRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        RunningRecordVo detail = userRunDataDetailsService.getRunningRecord(request, loginUser, zoneId);
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        }
        return CommonResult.success(detail);
    }

    /**
     * 运动统计
     *
     * @param request
     * @return
     */
    @PostMapping("/runningStatistics")
    public Result<List<TotalRunRecordVo>> runningStatistics(@RequestBody RunDataStatisticsRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        List<TotalRunRecordVo> statistics = realPersonRunDataDetailsManager.findRunningStatistics(request, loginUser, zoneId);
        return CommonResult.success(statistics);
    }

    /**
     * 运动记录分页
     *
     * @param request
     * @return
     */
    @PostMapping("/TotalRunRecordVo")
    public Result runningRecordV2(@RequestBody RunDataStatisticsRequest request) throws ParseException {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        Map<String, Object> runningRecordV2 = realPersonRunDataDetailsManager.getRunningRecordV2(request, loginUser, zoneId);
        return CommonResult.success(runningRecordV2);
    }

    /**
     * 运动记录 列表
     *
     * @return
     */
    @PostMapping("/runDataDetail/list")
    public Result<Page<RunRecordDetailsVo>> runDataDetailList(@RequestBody PageQuery pageQuery) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.equals(loginUser.getIsRobot(), 1)) {
            return CommonResult.success();
        }
        return CommonResult.success(realPersonRunDataDetailsManager.getDetailsVoList(pageQuery, loginUser, DateUtil.addDays(ZonedDateTime.now(), -30), ZonedDateTime.now()));
    }

    /**
     * 运动记录列表-月
     *
     * @param request
     * @return
     */
    @PostMapping("/runningRecordList")
    public Result<MapListVo<RunningRecordListVo>> runningRecordList(@RequestBody RunDataStatisticsRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        request.setStatisticsType(1);
        String zoneId = getZoneId();
        MapListVo map = new MapListVo();
        String languageCode = getLanguageCode();
        List<RunningRecordListVo> list = userRunDataReportManager.getRunningRecordList(loginUser.getId(), request, zoneId, languageCode);
        map.setList(list);
        return CommonResult.success(map);
    }

    /**
     * 获取最近的跑步明细id
     *
     * @return
     */
    @PostMapping("/lastId")
    public Result<LastIdAndStatusVo> lastId() {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), CommonError.NEED_LOGIN.getMsg());
        }
        ZnsUserRunDataDetailsEntity detail = userRunDataDetailsService.getRunDataDetailsLast(loginUser.getId());
        LastIdAndStatusVo data = new LastIdAndStatusVo();
        if (Objects.isNull(detail) || detail.getIsDelete() == 1) {
            data.setDetailId(null);
            data.setRunStatus(-1);
        } else {
            data.setDetailId(detail.getId());
            data.setRunStatus(detail.getRunStatus());
        }
        return CommonResult.success(data);
    }

    /**
     * 数据刷新（线上没有调用）
     *
     * @param request
     * @return
     */
    @PostMapping("/dataRefresh")
    public Result dataRefresh(@RequestBody UserIdRequest request) {
        runDataBizService.userRunDataHandle(request.getUserId(), userService.findById(request.getUserId()));

        return CommonResult.success();
    }

    /**
     * 组队跑跑步报告
     *
     * @param request
     * @return
     */
    @RequestMapping("/teamRun")
    public Result teamRun(@RequestBody DetailIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();
        ActivityRunningReportBaseVo detail = activityStrategyContext.getActivityRunningReport(request.getDetailId(),
                loginUser, RunActivityTypeEnum.TEAM_RUN.getType(), zoneId, request.getActivityId());
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        }
        return CommonResult.success(detail);
    }

    /**
     * PK赛跑步报告
     *
     * @param request
     * @return
     */
    @RequestMapping("/challengeRun")
    public Result<ActivityRunningReportBaseVo> challengeRun(@RequestBody ChallengeRunRequest request) {
        Integer activityType = request.getActivityType();
        Long detailId = request.getDetailId();
        String zoneId = getZoneId();
        ZnsUserEntity loginUser = getLoginUser();
        ActivityRunningReportBaseVo detail = null;
        if (activityType == 2) {
            // PK赛：随机pk、好友pk、离线pk
            if (request.getActivityId() != null && request.getActivityId() > 0) {
                ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(request.getActivityId());
                // 如果是离线pk
                if (znsRunActivityEntity != null && Objects.equals(znsRunActivityEntity.getActivityTypeSub(), 3)) {
                    PkChallengeRecord pkChallengeRecord = pkChallengeRecordService.selectPkChallengeRecordByActivityIdChallengeType(request.getActivityId(), 1);
                    ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsService.selectByActivityIdUserId(znsRunActivityEntity.getId(), pkChallengeRecord.getUserId());

                    if (znsUserRunDataDetailsEntity != null && Objects.equals(znsUserRunDataDetailsEntity.getRunStatus(), 0)) {
                        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.challenged.opponent.not.finished"));
                    }
                    ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), pkChallengeRecord.getUserId());
                    if (znsRunActivityUserEntity != null && Objects.equals(znsRunActivityUserEntity.getUserState(), 3)) {
                        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.challenged.opponent.not.finished"));
                    }
                } else {
                    log.info("非离线 pk, activityId " + znsRunActivityEntity.getId());
                }
            }
            //判断是否有进行跑步中的数据
            int challengeRunning = userRunDataDetailsService.getChallengeRunning(detailId);
            if (challengeRunning > 0) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("activity.challenged.opponent.not.finished"));
            }

            detail = activityStrategyContext.getActivityRunningReport(detailId, loginUser, RunActivityTypeEnum.CHALLENGE_RUN.getType(), zoneId, request.getActivityId());
            if (Objects.isNull(detail)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
            }
        } else if (activityType == 3) {
            //排行赛
            detail = activityStrategyContext.getActivityRunningReport(detailId, loginUser, RunActivityTypeEnum.OFFICIAL_ENENT.getType(), zoneId, request.getActivityId());
            if (Objects.isNull(detail)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
            }
        }

        //兼容老版本竞技值的展示
        List<ChallengeRunRunningReportListVO> list = null;
        if (detail instanceof ChallengeRunReportVo) {
            list = ((ChallengeRunReportVo) detail).getList();
        } else if (detail instanceof RankingRunReportVo) {
            list = ((RankingRunReportVo) detail).getList();
        }
        if (!CollectionUtils.isEmpty(list)) {
            for (ChallengeRunRunningReportListVO listVO : list) {
                Long dataDetailId = listVO.getUserRunDataDetailId();
                if (dataDetailId == null) {
                    continue;
                }
                BigDecimal capabilityValue;
                ZnsUserRunDataDetailsEntity entity = userRunDataDetailsService.findById(dataDetailId);
                if (entity == null) {
                    continue;
                }
                capabilityValue = SportsDataUnit.getCapabilityValue(entity.getRunMileage(), entity.getRunTime());
                listVO.setCapabilityValue(capabilityValue);
                Currency currency = new Currency();
                activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, request.getActivityId(), loginUser.getId());
                listVO.setCurrency(currency);
                //重设配速 TODO 由于前端计算一遍导致精度有点偏差，后续再处理
               /* PaceRateChartData paceData = userRunDataDetailsService.getPaceChartMap2(entity, 0);
                listVO.setMaxPace(paceData.getMaxPace());
                listVO.setAveragePace(paceData.getAveragePace());*/
            }
        }

        return CommonResult.success(detail);
    }

    /**
     * 获取数据1-游戏端获取历史最大心率、最长运动时长等
     *
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/getRunData1")
    public Result getRunData1(HttpServletRequest httpServletRequest) {
        String emailAddress = HeaderUtil.getEmail(httpServletRequest);
        if (StringUtils.isEmpty(emailAddress)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        ZnsUserEntity user = userService.findByEmail(emailAddress);

        Result result = checkTokenAndEmail(httpServletRequest, emailAddress);
        if (Objects.nonNull(result)) {
            return result;
        }
        Map<String, Object> map = userRunDataDetailsService.getRunData1(user.getId());
        return CommonResult.success(map);
    }

    /**
     * 获取数据2-游戏端获取历史总和数据
     *
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/getRunData2")
    public Result getRunData2(HttpServletRequest httpServletRequest) {
        String emailAddress = HeaderUtil.getEmail(httpServletRequest);
        if (StringUtils.isEmpty(emailAddress)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        ZnsUserEntity user = userService.findByEmail(emailAddress);

        Result result = checkTokenAndEmail(httpServletRequest, emailAddress);
        if (Objects.nonNull(result)) {
            return result;
        }
        Map<String, Object> map = userRunDataReportManager.getRunData2(user.getId());
        return CommonResult.success(map);
    }

    /**
     * 数据分享-总
     *
     * @return
     */
    @PostMapping("/getShareData")
    public Result<ShareDataVo> getShareData(@RequestBody RunTypePo po) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(po.getRunType())) {
            po.setRunType(1);
        }
        ShareDataVo data = userRunDataReportManager.getShareData(loginUser.getId(), po.getRunType(), loginUser);
        data.setNickname(loginUser.getFirstName());
        data.setHeadPortrait(loginUser.getHeadPortrait());
        data.setUserCode(loginUser.getUserCode());
        data.setCreateTime(loginUser.getCreateTime());
        String config = sysConfigService.selectConfigByKey("share.data.config");
        Map<String, Object> object = JsonUtil.readValue(config);
        List<String> copyWritingList = JsonUtil.readList(object.get("copyWritingList"), String.class);
        data.setChickenSoup(ListUtils.random(copyWritingList));
        List<String> backgroundList = JsonUtil.readList(object.get("backgroundList"), String.class);
        data.setBackground(backgroundList);
        ZnsUserAccountEntity account = userAccountService.getByUserId(loginUser.getId());
        if (Objects.nonNull(account)) {
            data.setTotalBonus(account.getTotalBonus());
            Currency currency = I18nConstant.buildCurrency(account.getCurrencyCode());
            data.setCurrency(currency);
        } else {
            data.setTotalBonus(BigDecimal.ZERO);
            Currency currency = I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode());
            data.setCurrency(currency);
        }

        Integer totalRunMileage = data.getTotalRunMileage();
        //获得英里
        Integer miles = 0;
        if (Objects.nonNull(totalRunMileage)) {
            miles = totalRunMileage / 1600;
        }
        data.setRunMileageCopyWriting(RunMileageCopyWritingEnum.getRunMileageCopyWriting(miles));
        return CommonResult.success(data);
    }

    /**
     * 数据转换
     *
     * @param po
     * @return
     */
    @PostMapping("/dataConversion")
    public Result<DataConversionVo> dataConversion(@RequestBody DataConversionPo po) {
        DataConversionVo vo = new DataConversionVo();
        if (Objects.nonNull(po.getCalories())) {
            CalorieConsumptionFoodCalibration calorieConsumptionFoodCalibration = CalorieConsumptionFoodCalibration.getCalorieConsumptionFoodCalibration(po.getCalories());
            vo.setFoodNum(new BigDecimal(calorieConsumptionFoodCalibration.getNum()));
            vo.setFoodName(calorieConsumptionFoodCalibration.getFoodName());
            vo.setFoodType(calorieConsumptionFoodCalibration.getFoodType());
        }
        if (Objects.nonNull(po.getRunTime()) && Objects.nonNull(po.getMileage())) {
            Integer pace = SportsDataUnit.getPace(po.getRunTime(), BigDecimalUtil.divHalfUp(po.getMileage(), new BigDecimal(1000), 4));
            vo.setPace(pace);
        }

        return CommonResult.success(vo);
    }

    /**
     * 系列赛报告页面
     *
     * @param request
     * @return
     * @tag 3.1.0
     */
    @PostMapping("/runningReport/series")
    public Result<RunningReportVo> runningReportSeries(@RequestBody DetailIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(request.getActivityId(), getZoneId());
        if (activityNew.getMainType().equals(MainActivityTypeEnum.OLD.getType())) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        RunningReportVo detail = userRunDataReportManager.runningReportSeries(loginUser, activityNew.getMainActivity());
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        detail.setMainType(activityNew.getMainType());
        if (Objects.nonNull(request.getActivityId()) && request.getActivityId() > 1) {
            detail.setActivityId(request.getActivityId());
        }
        detail.setId(request.getDetailId());
        return CommonResult.success(detail);
    }

    /**
     * 心率上传
     *
     * @return
     */
    @PostMapping("/upload/heartRate")
    public Result uploadHeartRate(@RequestBody UploadHeartRateRequest request) {
        ZnsUserRunDataDetailsHeartRate znsUserRunDataDetailsHeartRate = runDataDetailsHeartRateConverter.toDo(request);
        ZnsUserRunDataDetailsHeartRate userRunDataDetailsHeartRate = userRunDataManager.saveHeartRate(znsUserRunDataDetailsHeartRate);
        return CommonResult.success(userRunDataDetailsHeartRateConverter.toHeartRateChartData(userRunDataDetailsHeartRate));
    }

    /**
     * 速度上限提示弹窗
     *
     * @param request
     * @return
     */
    @PostMapping("/speedLimit/notice")
    public Result<RunSpeedLimitNoticeResponseDto> speedLimitNotice(@RequestBody UserIdRequest request) {
        RunSpeedLimitNoticeResponseDto responseDto = userRunDataReportManager.speedLimitNotice(request.getUserId());
        return CommonResult.success(responseDto);
    }

    /**
     * 系列赛报告页面(新)
     *
     * @param request
     * @return
     * @tag 4.2.3
     */
    @PostMapping("/runningReport/series/v2")
    public Result<SeriesRunningReportResp> runningReportSeriesV2(@RequestBody DetailIdRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(request.getActivityId(), getZoneId());
        if (activityNew.getMainType().equals(MainActivityTypeEnum.OLD.getType())) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        SeriesRunningReportResp detail = userRunDataReportManager.runningReportSeriesV2(loginUser, activityNew.getMainActivity());
        if (Objects.isNull(detail)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        return CommonResult.success(detail);
    }

    /**
     * 活动运动列表
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/activity/data/list")
    public Result<ActivityDataDetailsResponseDto> activityDataList(@RequestBody ActivityDataDetailsListRequestDto requestDto) {
        return CommonResult.success(realPersonRunDataDetailsManager.getActivityDataDetailsList(requestDto, getLoginUser()));
    }

    /**
     * 风控结果回调
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/risk/result")
    public Result riskResult(@RequestBody RiskCheatResultRequestDto requestDto) {
        runEndActivityManager.riskResult(requestDto);
        return CommonResult.success();
    }
}
