package com.linzi.pitpat.api.controller.h5;

import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.UserAccountSimpleVo;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 奖金池
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/h5/bonusPool")
@Slf4j
public class BonusPoolController extends BaseH5Controller {
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;

    @PostMapping("/page")
    public Result page() {
        ZnsUserEntity loginUser = getLoginUser();
        Map<String, Object> data = new HashMap<>();
        //活动展示
        List<Map<String, Object>> rotationArea = new ArrayList<>();

        //已发放奖励
        ZonedDateTime startDate = DateTimeUtil.parse("2022-08-01 09:00:00");
        BigDecimal issuedAward = userAccountDetailService.getIssuedAward(startDate);
        data.put("issuedAward", issuedAward);
        Integer issuedUserCount = userAccountDetailService.getIssuedUserCount(startDate);
        //基数
        Integer baseIssuedUserCount = 0;

        //奖金池数据
        String bonusPoolData = sysConfigService.selectConfigByKey("bonus.pool.data");
        if (!StringUtils.hasText(bonusPoolData)) {
            data.put("addIssuedAward", BigDecimal.ZERO);
            data.put("addIssuedUserCount", 0);
            data.put("bonusPoolAmount", BigDecimal.ZERO);
            data.put("surplusBonusPoolAmount", BigDecimal.ZERO);
        } else {
            Map<String, Object> bonusDataObject = JsonUtil.readValue(bonusPoolData);
            BigDecimal lastIssuedAward = MapUtil.getBigDecimal(bonusDataObject.get("lastIssuedAward"));
            if (Objects.isNull(lastIssuedAward)) {
                lastIssuedAward = BigDecimal.ZERO;
            }
            data.put("addIssuedAward", issuedAward.subtract(lastIssuedAward));
            Integer lastIssuedUserCount = MapUtil.getInteger(bonusDataObject.get("lastIssuedUserCount"));
            if (Objects.isNull(lastIssuedUserCount)) {
                lastIssuedUserCount = 0;
            }
            baseIssuedUserCount = MapUtil.getInteger(bonusDataObject.get("baseIssuedUserCount"));

            data.put("addIssuedUserCount", issuedUserCount - lastIssuedUserCount);
            BigDecimal bonusPoolAmount = MapUtil.getBigDecimal(bonusDataObject.get("bonusPoolAmount"));
            if (Objects.isNull(bonusPoolAmount)) {
                bonusPoolAmount = BigDecimal.ZERO;
            }
            data.put("bonusPoolAmount", bonusPoolAmount);
            BigDecimal surplusBonusPoolAmount = bonusPoolAmount.subtract(issuedAward);
            data.put("surplusBonusPoolAmount", surplusBonusPoolAmount.compareTo(BigDecimal.ZERO) > 0 ? surplusBonusPoolAmount : BigDecimal.ZERO);
            data.put("bonusPoolSource", bonusDataObject.get("bonusPoolSource"));
            //获取其他配置活动
            List<Integer> activityIds = JsonUtil.readList(bonusDataObject.get("activityIds"), Integer.class);
            if (!CollectionUtils.isEmpty(activityIds)) {
                List<ZnsRunActivityEntity> znsRunActivityEntities = runActivityService.findByIds(activityIds.stream().map(Integer::longValue).toList());
                for (ZnsRunActivityEntity znsRunActivityEntity : znsRunActivityEntities) {
                    String config = znsRunActivityEntity.getActivityConfig();
                    Map<String, Object> jsonObject = JsonUtil.readValue(config);

                    Map<String, Object> activity = new HashMap<>();
                    activity.put("advertisingImage", jsonObject.get(ApiConstants.ADVERTISING_IMAGE));
                    activity.put("activityType", znsRunActivityEntity.getActivityType());
                    activity.put("activityId", znsRunActivityEntity.getId());
                    activity.put("activityStartTime", znsRunActivityEntity.getActivityStartTime());
                    rotationArea.add(activity);
                }
            }
        }
        data.put("rotationArea", rotationArea);
        data.put("issuedUserCount", issuedUserCount + baseIssuedUserCount);

        //新获得奖金数据
        List<UserAccountSimpleVo> lastUserAccountDetail = userAccountDetailService.getLastUserAccountDetail(startDate);
        data.put("lastUserAccountDetail", lastUserAccountDetail);

        //获取好友排行榜数据
        List<UserAccountSimpleVo> friendRanking = userAccountService.getFriendRankingList(loginUser.getId());
        data.put("friendRankingList", friendRanking);

        //获取全部排行榜数据
        List<UserAccountSimpleVo> allUserRanking = userAccountService.getAllUserRankingList(loginUser.getId());
        nickNameEncryption(allUserRanking);
        data.put("allUserRankingList", allUserRanking);

        //获取个人数据
        data.put("nickname", loginUser.getFirstName());
        data.put("headPortrait", loginUser.getHeadPortrait());
        data.put("userId", loginUser.getId());
        data.put("friendRanking", userAccountService.getFriendRanking(loginUser.getId()));
        data.put("allUserRanking", userAccountService.getAllUserRanking(loginUser.getId()));
        ZnsUserAccountEntity account = userAccountService.getUserAccount(loginUser.getId());
        if (Objects.nonNull(account)) {
            data.put("totalBonus", account.getTotalBonus());
        } else {
            data.put("totalBonus", BigDecimal.ZERO);
        }
        //获取累计参赛
        Integer takeTartInMatchCount = runActivityUserService.getTakeTartInMatchCount(loginUser.getId());
        data.put("takeTartInMatchCount", takeTartInMatchCount);

        ZnsUserAccountDetailEntity lastOneUserAccountDetail = userAccountDetailService.getLastOneUserAccountDetail(loginUser.getId());
        if (Objects.nonNull(lastOneUserAccountDetail)) {
            data.put("lastAwardTime", lastOneUserAccountDetail.getCreateTime());
        }

        return CommonResult.success(data);
    }

    private void nickNameEncryption(List<UserAccountSimpleVo> userAccountDetails) {
        if (CollectionUtils.isEmpty(userAccountDetails)) {
            return;
        }
        for (UserAccountSimpleVo userAccountDetail : userAccountDetails) {
            String nickname = userAccountDetail.getNickname();
            if (nickname.length() == 2) {
                String name = StringUtil.left(nickname, 1);
                nickname = StringUtil.rightPad(name, nickname.length(), "*");
            } else if (nickname.length() < 5 && nickname.length() > 2) {
                int repNum = nickname.length() - 2;
                nickname = StringUtil.left(nickname, 1).concat(getReplaceStr(repNum)).concat(StringUtil.right(nickname, 1));
            } else if (nickname.length() < 7) {
                int repNum = nickname.length() - 4;
                nickname = StringUtil.left(nickname, 2).concat(getReplaceStr(repNum)).concat(StringUtil.right(nickname, 2));
            } else if (nickname.length() > 6 && nickname.length() % 2 == 1) {
                int repNum = 3;
                int len = (nickname.length() - repNum) / 2;
                nickname = StringUtil.left(nickname, len).concat(getReplaceStr(repNum)).concat(StringUtil.right(nickname, len));
            } else if (nickname.length() > 6 && nickname.length() % 2 == 0) {
                int repNum = 4;
                int len = (nickname.length() - repNum) / 2;
                nickname = StringUtil.left(nickname, len).concat(getReplaceStr(repNum)).concat(StringUtil.right(nickname, len));
            }
            userAccountDetail.setNickname(nickname);
        }
    }

    private String getReplaceStr(int repNum) {
        String s = "";
        for (int i = 0; i < repNum; i++) {
            s = s + "*";
        }
        return s;
    }
}
