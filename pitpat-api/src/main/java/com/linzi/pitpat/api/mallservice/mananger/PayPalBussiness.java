package com.linzi.pitpat.api.mallservice.mananger;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityUserBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.dto.api.InitPayResp;
import com.linzi.pitpat.data.awardservice.dto.api.PaypalRequest;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreIdDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.ExchangeWearQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.model.query.WearQuery;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.config.PitpatConfig;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.mallservice.biz.MallOrderPaymentBizService;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderDetailVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderItemListVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.paymentservice.enums.PaymentGoodsTypeEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentMethodEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentRefTypeEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentTradeSourceEnum;
import com.linzi.pitpat.data.paymentservice.enums.PaymentTradeStateEnum;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentGoodsDo;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentTradeDo;
import com.linzi.pitpat.data.paymentservice.service.PaymentGoodsService;
import com.linzi.pitpat.data.paymentservice.service.PaymentTradeService;
import com.linzi.pitpat.data.service.pay.PaymentService;
import com.linzi.pitpat.data.userservice.dto.request.UserVipInfoDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipShop;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserSubscribeBizService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import com.paypal.http.HttpResponse;
import com.paypal.orders.Capture;
import com.paypal.orders.LinkDescription;
import com.paypal.orders.Order;
import com.paypal.orders.PurchaseUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class PayPalBussiness {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private PaypalPayService paypalPayService;

    @Resource
    private PitpatConfig config;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private VipUserService vipUserService;

    @Autowired
    private VipShopService vipShopService;

    @Autowired
    private ZnsUserService userService;

    @Autowired
    private ExchangeScoreAwardService exchangeScoreAwardService;

    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;

    @Autowired
    private UserWearsBagService userWearsBagService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private ActivityUserScoreService activityUserScoreService;


    @Autowired
    private WearsService wearsService;

    @Resource
    protected ZnsRunActivityUserService runActivityUserService;
    @Resource
    protected ZnsRunActivityService runActivityService;
    @Resource
    protected ExchangeScoreRuleService exchangeScoreRuleService;

    @Resource
    protected ZnsOrderService orderService;
    @Autowired
    private ZnsGoodsService znsGoodsService;

    @Autowired
    private ZnsOrderService znsOrderService;
    @Autowired
    private UserCouponManager userCouponManager;
    @Autowired
    private UserCouponBizService userCouponBizService;
    //    private static final String RETURN_URL = "/app/paypal/successPayment";
//    private static final String CANCEL_URL = "/app/paypal/failPayment";
    private static final String RETURN_URL = "/#/interimPay/loading";
    private static final String CANCEL_URL = "/#/interimPay/loading";

    private static final String ACCOUNT_RETURN_URL = "/app/pay/successPayment";
    private static final String ACCOUNT_CANCEL_URL = "/app/pay/failPayment";

    @Autowired
    private VipUserSubscribeBizService vipUserSubscribeBizService;

    @Autowired
    private PaymentGoodsService paymentGoodsService;

    @Autowired
    private ZnsUserAccountService userAccountService;
    @Autowired
    private ZnsUserAccountDetailService userAccountDetailService;

    @Autowired
    private PaymentTradeService paymentTradeService;

    private final ActivityUserBizService activityUserBizService;
    private final MallOrderPaymentBizService mallOrderPaymentBizService;

    /**
     * 创建支付
     *
     * @param type 购买类型 类型："coupon":优惠券,"vip":会员,"scoreExchange":积分商城,"exchangeWear": 我的形象皮肤,"battlePass":新里程碑 ,"mallOrder":商城订单 ,"refundOrder":退款单
     * @see PayConstant.BuyTypeEnum
     */
    public InitPayResp initiatePayment(BigDecimal amount,String type, Long refId) {
        String billNo = NanoId.randomNanoId();
        String currencyCode = I18nConstant.CurrencyCodeEnum.USD.getCode();
        InitPayResp initPayResp = new InitPayResp();
        try {
            //创建支付
            String returnUrl = config.getMallH5Url() + "/interimPay/loading?orderStatus=success&billNo=" + billNo;
            String cancelUrl = config.getMallH5Url() + "/interimPay/loading?orderStatus=fail&billNo=" + billNo;
            if (PayConstant.BuyTypeEnum.MALL_ORDER.type.equals(type)) {
                //商城订单跳转页面
                ZnsOrderEntity orderEntity = znsOrderService.findById(refId);
                returnUrl = config.getMallH5Url() + "/store/payment/loading?orderStatus=success&isCusNavBar=true&orderNo=" + orderEntity.getOrderNo();
                cancelUrl = config.getMallH5Url() + "/store/payment/loading?orderStatus=fail&isCusNavBar=true&orderNo=" + orderEntity.getOrderNo();
                currencyCode = orderEntity.getCurrencyCode();
            }
            if (PayConstant.BuyTypeEnum.H5_MALL_ORDER.type.equals(type)){
                //H5投流订单跳转页面
                ZnsOrderEntity orderEntity = znsOrderService.findById(refId);
                returnUrl = config.getSurrenderH5Url() + "/payment/loading?orderStatus=success&isCusNavBar=true&orderNo=" + orderEntity.getOrderNo();
                cancelUrl = config.getSurrenderH5Url() + "/payment/loading?orderStatus=fail&isCusNavBar=true&orderNo=" + orderEntity.getOrderNo();
                currencyCode = orderEntity.getCurrencyCode();
            }
            HttpResponse<Order> response = paymentService.createOrder(amount, billNo, returnUrl, cancelUrl, currencyCode, PayConstant.BuyTypeEnum.MALL_ORDER.type.equals(type) || PayConstant.BuyTypeEnum.H5_MALL_ORDER.type.equals(type));
            String approve = "";
            String tradeNo = response.result().id();
            initPayResp.setTradeNo(tradeNo);
            if (response.statusCode() == 201) {
                log.info("Status Code = {}, Status = {}, OrderID = {}, Intent = {}", response.statusCode(), response.result().status(), response.result().id(), response.result().checkoutPaymentIntent());
                for (LinkDescription link : response.result().links()) {
                    log.info("Links-{}: {}    \tCall Type: {}", link.rel(), link.href(), link.method());
                    if ("approve".equals(link.rel())) {
                        approve = link.href();
                        initPayResp.setApprove(approve);
                    } else if ("capture".equals(link.rel())) {
                        initPayResp.setCapture(link.href());
                    } else if ("self".equals(link.rel())) {
                        initPayResp.setSelf(link.href());
                    }
                }
                String totalAmount = response.result().purchaseUnits().get(0).amountWithBreakdown().currencyCode() + ":" + response.result().purchaseUnits().get(0).amountWithBreakdown().value();
                log.info("Total Amount: {}", totalAmount);
                log.info("createOrder response body: {}", JsonUtil.writeString(response.result()));
                initPayResp.setSuccess(true);
            } else {
                log.info("Failed to create PayPal order");

            }
            if (!StringUtils.hasText(approve)) {
                log.info("Failed to create PayPal order");
                initPayResp.setSuccess(false);
            }
            initPayResp.setBillNo(billNo);
            initPayResp.setCurrencyCode(currencyCode);
            return initPayResp;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Result initiatePayment(BigDecimal amount, Long userId) {
        String billNo = NanoId.randomNanoId();
        ;
        Map<String, Object> map = new HashMap<>();
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(userId);
        try {
            //创建支付
            HttpResponse<Order> response = paymentService.createOrder(amount, billNo, config.getUrl() + ACCOUNT_RETURN_URL, config.getUrl() + ACCOUNT_CANCEL_URL, userAccount.getCurrencyCode(), false);
            String approve = "";
            String tradeNo = response.result().id();
            map.put("orderId", tradeNo);
            if (response.statusCode() == 201) {
                log.info("Status Code = {}, Status = {}, OrderID = {}, Intent = {}", response.statusCode(), response.result().status(), response.result().id(), response.result().checkoutPaymentIntent());
                for (LinkDescription link : response.result().links()) {
                    log.info("Links-{}: {}    \tCall Type: {}", link.rel(), link.href(), link.method());
                    if ("approve".equals(link.rel())) {
                        approve = link.href();
                        map.put("approve", approve);
                    } else if ("capture".equals(link.rel())) {
                        map.put("capture", link.href());
                    } else if ("self".equals(link.rel())) {
                        map.put("self", link.href());
                    }
                }
                String totalAmount = response.result().purchaseUnits().get(0).amountWithBreakdown().currencyCode() + ":" + response.result().purchaseUnits().get(0).amountWithBreakdown().value();
                log.info("Total Amount: {}", totalAmount);
                log.info("createOrder response body: {}", JsonUtil.writeString(response.result()));
            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.paypal.createFailed"));
            }
            if (!StringUtils.hasText(approve)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.paypal.createFailed"));
            }

            userAccountDetailService.addAccountDetail(userId, 1, AccountDetailTypeEnum.RECHARGE, amount, billNo, null, 0, tradeNo, null, amount, amount, null);
            return CommonResult.success(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Result payment(String tradeNo, boolean isWebhook) {
        String lockKey = RedisConstants.USER_ACCOUNT + tradeNo;
        RLock lock = redissonClient.getLock(lockKey);
        boolean accountLock = lock.tryLock();
        if (accountLock) {
            try {
                PaypalPay pay = paypalPayService.lambdaQuery().eq(PaypalPay::getTradeNo, tradeNo).one();
                if (Objects.isNull(pay)) {
                    return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), "PaypalPay，tradeNo=" + tradeNo);
                }
                if (pay.getStatus() == 1) {
                    log.info("paypal转账付款成功,重复调用");
                    return CommonResult.success();
                }
                PaypalPay updatePay = new PaypalPay(pay.getId());
                //paypal webhook回调
                if (isWebhook) {
                    log.info("webhook回调paypal转账付款成功,tradeNo:{}", tradeNo);
                    updatePay.setStatus(1);
                    paypalPayService.updateById(updatePay);
                    // 成功逻辑
                    doSuccessPay(pay);
                    return CommonResult.success();
                }

                String payStatus = null;
                Integer status;
                try {
                    payStatus = capturePayment(tradeNo);
                } catch (Exception e) {
                    log.info("支付失败，tradeNo：{}", tradeNo);
                    log.error("支付失败", e);
                    updatePay.setStatus(-1);
                    paypalPayService.updateById(updatePay);
                    return CommonResult.fail("pay error");
                }
                if ("COMPLETED".equals(payStatus)) {
                    log.info("paypal转账付款成功,tradeNo:{}", tradeNo);
                    status = 1;
                    // 成功逻辑
                    doSuccessPay(pay);

                } else if ("PENDING".equals(payStatus)) {
                    log.info("paypal转账支付中,tradeNo:{}", tradeNo);
                    status = 2;

                } else {
                    log.info("支付异常,tradeNo:{}", tradeNo);
                    return CommonResult.fail("pay error");
                }
                updatePay.setStatus(status);
                paypalPayService.updateById(updatePay);
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                log.info("获取锁 后删除锁" + lockKey);
                // redisLock.releaseLock(lockKey);
                if (lock.isHeldByCurrentThread()) { //判断锁是否存在，和是否当前线程加的锁。
                    lock.unlock();
                }

            }

        }

        return CommonResult.success();
    }

    private void doSuccessPay(PaypalPay pay) {
        String type = pay.getType();

        switch (type) {
            case "coupon":
                buyCoupon(pay);
                break;
            case "vip":
                buyVip(pay);
                break;
            case "scoreExchange":
                scoreExchange(pay);
                break;
            case "exchangeWear":
                exchangeWear(pay);
                break;
            case "battlePass":
                buyBattlePass(pay);
                break;
            case "vip-month":
                buyVipMonth(pay);
                break;
            case "mallOrder", "h5MallOrder":
                mallOrderPaymentBizService.dealPaymentResult(pay.getRefId(), pay.getPayNo(), PayConstant.OrderPayStatusEnum.COMPLETED, null, pay.getTradeNo(), 1);
                break;
        }
    }

    private void buyVipMonth(PaypalPay pay) {
        log.info("Buy Vip,pay:{}", pay.getPayNo());
        PaymentGoodsDo goodsDo = paymentGoodsService.findById(pay.getRefId());
        ZonedDateTime now = ZonedDateTime.now();
        Long refId = vipUserSubscribeBizService.createUserBuyMemberOrder(pay.getUserId(), pay.getRefId(), goodsDo.getGoodsType(), PaymentMethodEnum.PAYPAL_WEB);
        vipUserSubscribeBizService.paySuccess(refId, pay.getPayNo(), now.plusDays(31), now, true, false, false);
        //同步新trade
        syncNewTrade(pay, refId);

    }

    public void syncNewTrade(PaypalPay pay, Long refId) {
        PaymentTradeDo tradeDo = new PaymentTradeDo();
        tradeDo.setTradeNo(OrderUtil.getUniqueCode(pay.getUserId() + "."));
        tradeDo.setUserId(pay.getUserId());
        tradeDo.setPaymentMethod(PaymentMethodEnum.PAYPAL_WEB.getCode());
        tradeDo.setPaymentTradeState(PaymentTradeStateEnum.PAY_SUCCESS.getCode());
        tradeDo.setGoodsId(pay.getRefId());
        tradeDo.setGoodsType(PaymentGoodsTypeEnum.SINGLE.getCode());
        tradeDo.setPaymentTradeSource(PaymentTradeSourceEnum.PEOPLE_APP.getCode());
        tradeDo.setPurchaseDate(ZonedDateTime.now());
        tradeDo.setDealDate(ZonedDateTime.now());
        tradeDo.setStatusChangeDate(ZonedDateTime.now());
        tradeDo.setRefId(refId.toString());
        tradeDo.setRefType(PaymentRefTypeEnum.PREMIER_MEMBER.getCode());
        tradeDo.setTotalAmount(pay.getAmount());
        paymentTradeService.create(tradeDo);

        pay.setNewTradeNo(tradeDo.getTradeNo());
        log.info("同步的tradeNo为{}", tradeDo.getTradeNo());
        paypalPayService.updateById(pay);
    }

    public void exchangeWear(PaypalPay pay) {
        Long userId = pay.getUserId();
        String info = pay.getInfo();
        ExchangeWearQuery query = JsonUtil.readValue(info, ExchangeWearQuery.class);

        // 防止重复提交
        String lockKey = RedisConstants.EXCHANGE_SCORE_RULE + userId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean getLock = lock.tryLock();
        try {
            if (!getLock) {
                log.info("start...Task 获取锁失败");
                return;
            }
            log.info("获取锁 成功 " + lockKey);
            List<WearQuery> exchangeList = query.getExchangeList();
            for (WearQuery wearQuery : exchangeList) {

                // 校验积分是否充足
                ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(wearQuery.getRuleId());

                ScoreIdDto scoreIdDto = new ScoreIdDto();
                scoreIdDto.setUserId(userId);
                scoreIdDto.setPassword(query.getPassword());
                scoreIdDto.setId(wearQuery.getRuleId());
                activityUserScoreService.useActivityUserScore(exchangeScoreRule, scoreIdDto);
                ExchangeScoreAward exchangeAward = exchangeScoreAwardService.getOne(Wrappers.<ExchangeScoreAward>lambdaQuery().eq(ExchangeScoreAward::getIsDelete, 0).eq(ExchangeScoreAward::getRuleId, wearQuery.getRuleId()));

                // 增加积分兑换记录(相当于扣除积分)
                ActivityUserScore activityUserScore = new ActivityUserScore();
                activityUserScore.setExchangeScoreRuleId(exchangeScoreRule.getId());
                activityUserScore.setScore(exchangeScoreRule.getExchangeScore());
                activityUserScore.setAmount(exchangeScoreRule.getExchangeAmount());
                activityUserScore.setStatus(1);
                activityUserScore.setExchangeTime(ZonedDateTime.now());
                activityUserScore.setSendTime(ZonedDateTime.now());
                activityUserScore.setType(2);
                activityUserScore.setIncome(-1);
                activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N3.getType()); // 积分兑换商品（衣服）
                activityUserScore.setUserId(userId);
                activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
                //增加积分兑换记录
                activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);
                // 查询用户背包有没有该服装
                List<UserWearsBag> wearBag = userWearsBagService.findListByQuery(UserWearBagQuery.builder().userId(userId).wearType(wearQuery.getWearType()).wearValue(wearQuery.getWearId()).build());

                if (CollectionUtils.isEmpty(wearBag)) {
                    // 放入用户背包
                    UserWearsBag userWearsBag = new UserWearsBag();
                    userWearsBag.setWearType(wearQuery.getWearType());
                    userWearsBag.setWearName(wearQuery.getWearName());
                    userWearsBag.setWearValue(wearQuery.getWearId());
                    userWearsBag.setWearImageUrl(wearQuery.getWearImageUrl());
                    userWearsBag.setSource(2);
                    userWearsBag.setUserId(userId);
                    userWearsBag.setStatus(0);
                    userWearsBag.setIsNew(0);
                    if (Objects.nonNull(exchangeAward.getExpiredTime())) {
                        userWearsBag.setExpiredTime(getAddHours(exchangeAward.getExpiredTime()));
                    }
                    userWearsBagService.insert(userWearsBag);
                } else {
                    // 存在限时服装 更新时间
                    UserWearsBag userWearsBag = wearBag.get(0);
                    if (Objects.nonNull(userWearsBag.getExpiredTime())) {
                        userWearsBag.setExpiredTime(DateUtil.addHours(userWearsBag.getExpiredTime(), exchangeAward.getExpiredTime() * 24));
                        userWearsBagService.update(userWearsBag);
                    }
                }
                Integer exchangeCount = activityUserScoreDao.selectByExchangeScoreRuleIdSource(exchangeScoreRule.getId(), Arrays.asList(-3));
                //剩余库存
                Integer remainNum = exchangeScoreRule.getRemainNum() - 1;
                ExchangeScoreRule rule = new ExchangeScoreRule();
                rule.setExchangeCount(exchangeCount);
                rule.setRemainNum(remainNum);
                rule.setId(exchangeScoreRule.getId());
                exchangeScoreRuleService.updateExchangeScoreByIdAndExchangeReserve(rule);
                exchangeScoreRuleService.setUserScoreRuleDayExchangeCache(userId, exchangeScoreRule.getId());
            }

        } catch (Exception e) {
            log.error("startTask 处理失败，e:", e);
        } finally {
            if (getLock) {
                log.info("startTask获取锁 后删除锁" + lockKey);
                if (lock.isHeldByCurrentThread()) {
                    //判断锁是否存在，和是否当前线程加的锁。
                    lock.unlock();
                }
            }
        }

    }

    public void buyCoupon(PaypalPay pay) {
        userCouponBizService.sendUserCouponSource(pay.getRefId(), pay.getUserId(), -1l, 9, false);
    }

    public void buyBattlePass(PaypalPay pay) {
        Long userId = pay.getUserId();
        Long refId = pay.getRefId();
        String info = pay.getInfo();
        ZnsUserEntity user = userService.findById(userId);

        Map<String, Object> json = new HashMap<>();
        if (StringUtils.hasText(info)) {
            json = JsonUtil.readValue(info);
            refId = MapUtil.getLong(json.get("activityId"));
        }

        ZnsRunActivityEntity activityEntity = runActivityService.findById(refId);
        // 查询是否已报名
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(refId, user.getId());

        if (Objects.nonNull(activityUser)) {
            if (json.get("discountPayType") != null) {
                activityUser.setDiscount(MapUtil.getInteger(json.get("discountPayType")));
            }
            activityUser.setIsPay(1);
            runActivityUserService.updateById(activityUser);
            runActivityService.addOfficialActivityUserPayCount(activityEntity.getId());
        }

        if (Objects.isNull(activityUser)) {
            // 添加官方赛事活动用户
            log.info("添加官方赛事活动用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
            activityUserBizService.addOfficialActivityUser(activityEntity, user.getId(), null, null, null, null, false);
            //修改官方赛事活动参与用户
            log.info("修改官方赛事活动参与用户 activityId = " + activityEntity.getId() + ",userId=" + user.getId());
            runActivityService.addOfficialActivityUserCount(activityEntity.getId());
        }
    }

    public void scoreExchange(PaypalPay pay) {
        Long userId = pay.getUserId();
        String info = pay.getInfo();
        Map<String, Object> jsonObject = JsonUtil.readValue(info);
        Long refId = MapUtil.getLong(jsonObject.get("id"));

        ZnsUserEntity user = userService.findById(userId);

        String lockKey = RedisConstants.EXCHANGE_SCORE_RULE + userId;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isLocked()) {
            // 后台是否在编辑
            throw new BaseException("The system is busy, please try again later");
        }

        // 校验积分是否充足
        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(refId);

        // 查询规则对应的兑换商品
        ExchangeScoreAward exchangeAward;
        if (Objects.isNull(exchangeScoreRule.getCouponId())) {
            Long ruleId = exchangeScoreRule.getId();
            exchangeAward = exchangeScoreAwardService.getOne(Wrappers.<ExchangeScoreAward>lambdaQuery().eq(ExchangeScoreAward::getIsDelete, 0).eq(ExchangeScoreAward::getRuleId, ruleId));
            if (Objects.isNull(exchangeAward)) {
                throw new BaseException(I18nMsgUtils.getMessage("userScore.exchange.points.exceed.nothing"));
            }
        } else {
            // 旧版本使用原来的券
            exchangeAward = new ExchangeScoreAward();
            exchangeAward.setExchangeType(ExchangeScoreTypeEnum.COUPON.getCode());
            exchangeAward.setExchangeId(exchangeScoreRule.getCouponId());
        }

        ScoreIdDto scoreIdDto = new ScoreIdDto();
        scoreIdDto.setUserId(userId);
        activityUserScoreService.useActivityUserScore(exchangeScoreRule, scoreIdDto);
        // 增加积分兑换记录(相当于扣除积分)
        ActivityUserScore activityUserScore = new ActivityUserScore();
        activityUserScore.setExchangeScoreRuleId(exchangeScoreRule.getId());
        activityUserScore.setScore(exchangeScoreRule.getExchangeScore());
        activityUserScore.setAmount(exchangeScoreRule.getExchangeAmount());
        activityUserScore.setStatus(1);
        activityUserScore.setExchangeTime(ZonedDateTime.now());
        activityUserScore.setSendTime(ZonedDateTime.now());
        activityUserScore.setType(exchangeAward.getExchangeType());
        activityUserScore.setCouponId(exchangeScoreRule.getCouponId());
        activityUserScore.setIncome(-1);
        if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.COUPON.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N2.getType()); // 积分兑换券
        } else if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.CLOTHES.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N3.getType()); // 积分兑换商品（衣服）
        } else if (exchangeAward.getExchangeType().equals(ExchangeScoreTypeEnum.ELSE.getCode())) {
            activityUserScore.setSource(ScoreConstant.SourceTypeEnum.source_type_N5.getType()); // 积分兑换其他
        }
        activityUserScore.setUserId(user.getId());
        activityUserScore.setExchangeOrderNo(OrderUtil.getBatchNo());
        activityUserScoreService.insertOrUpdateActivityUserScore(activityUserScore);


        ExchangeScoreTypeEnum exchangeType = ExchangeScoreTypeEnum.findByType(exchangeAward.getExchangeType());
        // 判断商品类型 不同类型发送不同消息
        switch (exchangeType) {
            case COUPON: // 优惠券
                Coupon coupon = couponService.selectCouponById(exchangeScoreRule.getCouponId());
                if (!Objects.equals(coupon.getQuota(), -1) && coupon.getQuota() - coupon.getQuotaSend() <= 0) {
                    // 顾及到事务 需要抛错
                    throw new BaseException(I18nMsgUtils.getMessage("common.page.inventory"));
                }
//                userCouponService.sendUserCoupon(exchangeScoreRule.getCouponId(), dto.getUserId(), null);
                userCouponManager.exchangeUserCouponMilepost(coupon, null, user, 1, null, null);
                break;
            case CLOTHES: // 服装
                Long exchangeId = exchangeAward.getExchangeId();
                // 查询服装信息
                Wears wears = wearsService.selectWearsById(exchangeId);
                // 查询用户背包有没有该服装
                List<UserWearsBag> wearBag = userWearsBagService.findListByQuery(UserWearBagQuery.builder().userId(userId).wearType(wears.getWearType()).wearValue(wears.getWearId()).build());

                if (CollectionUtils.isEmpty(wearBag)) {
                    // 放入用户背包
                    UserWearsBag userWearsBag = new UserWearsBag();
                    userWearsBag.setWearType(wears.getWearType());
                    userWearsBag.setWearName("");
                    userWearsBag.setWearValue(wears.getWearId());
                    userWearsBag.setWearImageUrl(""); // 图片暂不确定
                    userWearsBag.setSource(2);
                    userWearsBag.setUserId(user.getId());
                    userWearsBag.setStatus(0);
                    userWearsBag.setIsNew(0);
                    if (Objects.nonNull(exchangeAward.getExpiredTime())) {
                        userWearsBag.setExpiredTime(getAddHours(exchangeAward.getExpiredTime()));
                    }
                    userWearsBagService.insert(userWearsBag);
                } else {
                    // 存在限时服装 更新时间
                    UserWearsBag userWearsBag = wearBag.get(0);
                    if (Objects.nonNull(userWearsBag.getExpiredTime())) {
                        userWearsBag.setExpiredTime(DateUtil.addHours(userWearsBag.getExpiredTime(), exchangeAward.getExpiredTime() * 24));
                        userWearsBagService.update(userWearsBag);
                    }
                }
                break;
            case MUSIC:
                break;
            case PROPS:
                break;
            case PHYSICAL_REWARDS:
                break;
            default:
                break;
        }
        Integer exchangeCount = activityUserScoreDao.selectByExchangeScoreRuleIdSource(exchangeScoreRule.getId(), Arrays.asList(-2, -3));
        //剩余库存
        Integer remainNum = exchangeScoreRule.getRemainNum() - 1;
        ExchangeScoreRule rule = new ExchangeScoreRule();
        rule.setExchangeCount(exchangeCount);
        rule.setRemainNum(remainNum);
        rule.setId(exchangeScoreRule.getId());
        exchangeScoreRuleService.updateExchangeScoreByIdAndExchangeReserve(rule);
        exchangeScoreRuleService.setUserScoreRuleDayExchangeCache(userId, exchangeScoreRule.getId());
    }

    public void buyVip(PaypalPay pay) {
        Long userId = pay.getUserId();
        Long refId = pay.getRefId();
        String info = pay.getInfo();
        Map<String, Object> json = JsonUtil.readValue(info);
        Long orderId = MapUtil.getLong(json.get("orderId"));

        ZnsUserEntity user = userService.findById(pay.getUserId());

        //更新订单
        ZnsOrderEntity orderEntity = new ZnsOrderEntity();
        orderEntity.setGmtModified(ZonedDateTime.now());
        orderEntity.setId(orderId);
        orderEntity.setStatus(OrderStatusEnum.FINSHED.getStatus());
        znsOrderService.update(orderEntity);

        //查询订单
        OrderDetailVo vo = orderService.orderInfo(orderId, user.getLanguageCode());

        //更新实物库存
        List<OrderItemListVo> items = vo.getItems();
        if (!CollectionUtils.isEmpty(items)) {
            for (OrderItemListVo orderItem : items) {
                if (orderItem.getGoodsType() == 1) {
                    Long goodsId = orderItem.getGoodsId();
                    Long skuId = orderItem.getSkuId();
                    Integer count = orderItem.getCount();
                    znsGoodsService.processingInventoryVolume(goodsId, skuId, count);
                }
            }
        }

        //发放vip(1实物商品；2虚拟商品)
        OrderItemListVo orderItemListVo = vo.getItems().stream().filter(e -> Optional.ofNullable(e.getGoodsType()).orElse(0) == 2).findFirst().orElse(null);
        if (orderItemListVo != null) {
            Long goodsId = orderItemListVo.getGoodsId();
            VipShop vipShop = vipShopService.selectVipShopById(goodsId);
            Integer validDays = vipShop.getValidDays(); // 有效天数

            UserVipInfoDto dto = new UserVipInfoDto();
            dto.setAddType(1);
            dto.setEffectiveDays(validDays);
            dto.setEmailAddress(user.getEmailAddressEn());
            dto.setVipType(vipShop.getVipType());
            dto.setSource("加拿大App内购买");
            vipUserService.addVipUser(dto, null, null);
        }


    }

    public String capturePayment(String tradeNo) {

        PaypalPay pay = paypalPayService.lambdaQuery().eq(PaypalPay::getTradeNo, tradeNo).one();

        String lockKey = RedisConstants.USER_ACCOUNT + pay.getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean accountLock = lock.tryLock();

        try {

            if (accountLock) {

                //扣款
                HttpResponse<Order> response = paymentService.captureOrder(tradeNo, pay.isMallOrder());

                for (PurchaseUnit purchaseUnit : response.result().purchaseUnits()) {
                    for (Capture capture : purchaseUnit.payments().captures()) {

                        log.info("Capture id: {}", capture.id());
                        log.info("status: {}", capture.status());
                        log.info("invoice_id: {}", capture.invoiceId());
                        log.info("状态为{},tradeNo:{}", capture.status(), tradeNo);
                        return capture.status();
                    }
                }

            }
        } catch (Exception e) {
            log.error("capturePayment error", e);
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("capturePayment获取锁 后删除锁,{}", lockKey);
                lock.unlock();
            }
        }

        return null;
    }

    public Result failPayment(String tradeNo) {
        PaypalPay pay = paypalPayService.lambdaQuery().eq(PaypalPay::getTradeNo, tradeNo).one();
        doFailPayment(tradeNo, pay.isMallOrder());
        //设置为取消
        if (pay.getStatus() == 0) {
            pay.setStatus(3);
            paypalPayService.updateById(pay);
        } else {
            return CommonResult.fail("cant fail pay");
        }
        return CommonResult.success();


    }

    private String doFailPayment(String tradeNo, boolean isMallOrder) {

        try {
            //查询支付信息
            HttpResponse<Order> response = paymentService.ordersGet(tradeNo, isMallOrder);
            for (PurchaseUnit purchaseUnit : response.result().purchaseUnits()) {
                return purchaseUnit.invoiceId();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private ZonedDateTime getAddHours(Integer expiredTime) {
        ZonedDateTime date = ZonedDateTime.now();
        if (date.getMinutes() > 0) {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(DateUtil.addHours(ZonedDateTime.now(), 1), 0), 0), expiredTime * 24);
        } else {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(ZonedDateTime.now(), 0), 0), expiredTime * 24);
        }
    }

    public Result checkInitiatePayment(PaypalRequest request) {
        String type = request.getType();

        switch (type) {
            case "coupon":
                return null;
            case "vip":
                return null;
            case "scoreExchange":
                return checkScoreExchange(request);
            case "exchangeWear":
                return checkExchangeWear(request);
            case "battlePass":
                return checkBuyBattlePass(request);
        }

        return null;
    }

    private Result checkBuyBattlePass(PaypalRequest request) {
        return null;
    }

    private Result checkScoreExchange(PaypalRequest request) {
        String info = request.getInfo();
        ScoreIdDto dto = JsonUtil.readValue(info, ScoreIdDto.class);

        String lockKey = RedisConstants.EXCHANGE_SCORE_RULE + dto.getId();
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isLocked()) {
            // 后台是否在编辑
            throw new BaseException("The system is busy, please try again later");
        }


        // 校验积分是否充足
        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(dto.getId());
        if (exchangeScoreRule.getExchangeScore() > userService.getAllUserScore(dto.getUserId())) {
            return CommonResult.fail("Not enough points");
        }

        // 查询规则对应的兑换商品
        ExchangeScoreAward exchangeAward;
        if (Objects.isNull(exchangeScoreRule.getCouponId())) {
            Long ruleId = exchangeScoreRule.getId();
            exchangeAward = exchangeScoreAwardService.getOne(Wrappers.<ExchangeScoreAward>lambdaQuery().eq(ExchangeScoreAward::getIsDelete, 0).eq(ExchangeScoreAward::getRuleId, ruleId));
            if (Objects.isNull(exchangeAward)) {
                return CommonResult.fail(I18nMsgUtils.getMessage("userScore.exchange.points.exceed.nothing"));
            }
        }
        if (exchangeScoreRule.getExchangeReserve() == 0) {
            return CommonResult.fail("Insufficient inventory");
        }
        Boolean flag = exchangeScoreRuleService.verifyUserScoreRuleDayExchange(dto.getUserId(), exchangeScoreRule.getId());
        if (!flag) {
            return CommonResult.fail(ActivityError.REDEMPTIONS_EXHAUSTED_ERROR.getCode(), ActivityError.REDEMPTIONS_EXHAUSTED_ERROR.getMsg());
        }
        return null;
    }

    private Result checkExchangeWear(PaypalRequest request) {
        String info = request.getInfo();
        ExchangeWearQuery query = JsonUtil.readValue(info, ExchangeWearQuery.class);
        if (CollectionUtils.isEmpty(query.getExchangeList())) {
            CommonResult.success("无兑换服装");
        }
        List<WearQuery> exchangeList = query.getExchangeList();
        for (WearQuery wearQuery : exchangeList) {
            if (wearQuery.getWearType() == null || wearQuery.getWearId() == null) {
                return CommonResult.fail("传入的服装ID或者类型为空");
            }
            if (wearQuery.getRuleId() == null) {
                return CommonResult.fail("兑换规则ID为空");
            }
            // 校验积分是否充足
            ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(wearQuery.getRuleId());
            if (exchangeScoreRule.getExchangeScore() > userService.getAllUserScore(request.getUserId())) {
                return CommonResult.fail("Not enough points");
            }
            if (exchangeScoreRule.getExchangeReserve() == 0) {
                return CommonResult.fail("Insufficient inventory");
            }
            Boolean flag = exchangeScoreRuleService.verifyUserScoreRuleDayExchange(request.getUserId(), exchangeScoreRule.getId());
            if (!flag) {
                return CommonResult.fail(ActivityError.REDEMPTIONS_EXHAUSTED_ERROR.getCode(), ActivityError.REDEMPTIONS_EXHAUSTED_ERROR.getMsg());
            }
            //检验兑换人次数
            //Integer exchangePersonCount = activityUserScoreDao.selectExchangePersonCountByUserIdExchangeScoreRuleId(request.getUserId(), exchangeScoreRule.getId(), -1);
//            if (exchangePersonCount >= exchangeScoreRule.getExchangePersonCount()) {
//                return CommonResult.fail("Exceed the redeemable limit");
//            }
        }
        return null;
    }

    public String webhook(HttpServletRequest request) {
        try {
            String body = getBody(request);
            Map<String, String> headersInfo = getHeadersInfo(request);
            log.info("webhook 接受参数：" + body);
            log.info("webhook headers：" + headersInfo);
            boolean res = paymentService.validateReceivedEvent(body, headersInfo, true);
            if (!res) {
                log.error("paypal webhook 验签失败");
                return "fail";
            }
            Map<String, Object> object = JsonUtil.readValue(body);
            String event_type = MapUtil.getString(object.get("event_type"));
            Map<String, Object> resource = JsonUtil.readValue(object.get("resource"));

            String payNo = MapUtil.getString(resource.get("custom_id"));
            PaypalPay paypalPay = paypalPayService.lambdaQuery().eq(PaypalPay::getPayNo, payNo).one();
            if (Objects.isNull(paypalPay)) {
                //如果查询不到，应该就不是商城订单。
                return paymentService.webhook(body, request, false);
            }

            if ("PAYMENT.CAPTURE.COMPLETED".equals(event_type)) {
                //扣款成功事件
                if ("COMPLETED".equals(MapUtil.getString(resource.get("status"))) || "APPROVED".equals(MapUtil.getString(resource.get("status")))) {
                    payment(paypalPay.getTradeNo(), true);
                }
            } else if ("PAYMENT.CAPTURE.REFUNDED".equals(event_type)) {
                //退款事件
                /*
                Enum:	             Description
                COMPLETED	        此次付款的资金已存入收款人的 PayPal 账户.
                DECLINED	        已拒绝
                PARTIALLY_REFUNDED	部分退款.
                PENDING	            付款中
                REFUNDED	        退还给付款人的金额大于或等于付款的金额
                FAILED	            付款时出现错误
                {
                      "id": "WH-3V039447GR250571G-8N128607XR372250T",
                      "create_time": "2024-11-15T12:34:56Z",
                      "resource_type": "capture",
                      "event_type": "PAYMENT.CAPTURE.REFUNDED",
                      "resource": {
                            "id": "7D8F7F28XY2732923",
                            "status": "REFUNDED",
                            "invoice_id": "INV-123456",
                            "payment_mode": "INSTANT",
                            "parent_payment": "PAY-1234567890ABCDEF",
                            "reason": "REFUND_FAILURE",  // 退款失败的原因
                        }
                    }
                */
                String refundNo = MapUtil.getString(resource.get("invoice_id")); //退款单No
                String status = MapUtil.getString(resource.get("status"));
                String failReason = "";
                PayConstant.RefundPayStatusEnum refundStatusEnum = PayConstant.RefundPayStatusEnum.PENDING; //退款中
                if ("COMPLETED".equals(status) || "REFUNDED".equals(status)) {
                    refundStatusEnum = PayConstant.RefundPayStatusEnum.COMPLETED; //退款成功
                } else if ("FAILED".equals(status)) {
                    refundStatusEnum = PayConstant.RefundPayStatusEnum.FAILED; //退款失败
                    failReason = MapUtil.getString(resource.get("reason"), "webhook fail");
                }
                mallOrderPaymentBizService.dealRefundResult(refundNo, refundStatusEnum, failReason);
            } else {
                if ("CHECKOUT.ORDER.VOIDED".equals(event_type)) {
                    //取消扣款事件
                    failPayment(paypalPay.getTradeNo());
                } else {
                    log.warn("webhook 当前事件不处理，事件类型：" + event_type);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.info("validateReceivedEvent error,e:{}", e);
            return "fail";
        } catch (Exception e) {
            e.printStackTrace();
            log.info("validateReceivedEvent error,e:{}", e);
            return "fail";
        }

        return "success";
    }

    private String getBody(HttpServletRequest request) throws IOException {
        String body;
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        try {
            InputStream inputStream = request.getInputStream();
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
        } catch (IOException ex) {
            throw ex;
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        body = stringBuilder.toString();
        return body;
    }

    private static Map<String, String> getHeadersInfo(HttpServletRequest request) {
        Map<String, String> map = new HashMap<String, String>();
        @SuppressWarnings("rawtypes")
        Enumeration headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = (String) headerNames.nextElement();
            String value = request.getHeader(key);
            map.put(key, value);
        }
        return map;
    }
}
