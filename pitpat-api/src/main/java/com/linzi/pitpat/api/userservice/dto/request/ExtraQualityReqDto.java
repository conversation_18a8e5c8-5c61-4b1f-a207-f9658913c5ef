package com.linzi.pitpat.api.userservice.dto.request;

import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 额外质保请求参数
 */
@Data
public class ExtraQualityReqDto {

    /**
     * 额外质保id(编辑的时候需要传)
     */
    private Long id;

    /**
     * 整机序列号
     */
    private String printId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 何处购买，amazon：亚马逊，tiktok：TikTok，deerRun：DeerRun官网，supeRun：SupeRun官网，walmart：沃尔玛
     *
     * @see DeviceConstant.BuySourceEnum
     */
    private String buySource;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 购买日期
     */
    private ZonedDateTime orderTime;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 邮箱地址
     */
    private String emailAddress;

    /**
     * 设备唯一编码
     */
    private String equipmentNo;

}
