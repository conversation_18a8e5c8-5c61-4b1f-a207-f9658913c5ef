package com.linzi.pitpat.api.userservice.manager;

import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.communityservice.dto.api.request.SubmitCommentDto;
import com.linzi.pitpat.data.communityservice.enums.CommunityInteractionTypeEnum;
import com.linzi.pitpat.data.communityservice.model.entity.CommunityCommentDo;
import com.linzi.pitpat.data.communityservice.model.entity.CommunityInteractDo;
import com.linzi.pitpat.data.communityservice.model.query.CommunityCommentQuery;
import com.linzi.pitpat.data.communityservice.service.CommunityCommentService;
import com.linzi.pitpat.data.enums.CommunityContentTypeEnum;
import com.linzi.pitpat.data.systemservice.dto.request.MentionContentDto;
import com.linzi.pitpat.data.userservice.model.entity.CommunityContent;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class CommentHandler extends InteractionHandler {


    private final ZnsUserService znsUserService;
    private final CommunityContentService communityContentService;
    private final CommunityCommentService communityCommentService;

    @Override
    public void handle(JoinPoint joinPoint, String annotation, Object result) {
        String email = UserContextHolder.getEmail();
        ZnsUserEntity znsUserEntity = znsUserService.findByEmail(email);
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取方法参数值
        Object[] args = joinPoint.getArgs();
        CommunityInteractDo communityInteractDo = new CommunityInteractDo();
        for (Object arg : args) {
            if (arg instanceof SubmitCommentDto request) { // 判断参数类型
                Long parentId = request.getParentId();
                CommunityContent communityContent;
                if (Objects.nonNull(parentId) && parentId > 0) {
                    log.info("parentId 有值: " + parentId);
                    // 有值时的处理逻辑
                    // 回复评论
                    CommunityCommentQuery communityQuery = new CommunityCommentQuery().setId(parentId);
                    CommunityCommentDo communityCommentDo = communityCommentService.findByQuery(communityQuery);
                    communityContent = communityContentService.findById(communityCommentDo.getContentId());
                    communityInteractDo.setIsRead(YesNoStatus.NO.getCode());
                    ZnsUserEntity recipientIdUserEntity = znsUserService.findById(communityCommentDo.getCommentUser());
                    communityInteractDo.setSenderId(znsUserEntity.getId());
                    communityInteractDo.setFirstName(znsUserEntity.getFirstName());
                    communityInteractDo.setHeadPortrait(znsUserEntity.getHeadPortrait());
                    communityInteractDo.setRecipientId(recipientIdUserEntity.getId());
                    communityInteractDo.setContentText(request.getCommentText());
                    communityInteractDo.setInteractType(CommunityInteractionTypeEnum.REPLY_COMMENT.getCode());
                    communityInteractDo.setPostId(communityCommentDo.getContentId());
                    communityInteractDo.setPostType(CommunityContentTypeEnum.TEXT_ONLY.getCode());
                    communityInteractDo.setRelEntityId(communityCommentDo.getId());
                    communityInteractDo.setRelEntityContent(communityCommentDo.getCommentText());
                    communityInteractService.create(communityInteractDo);
                } else {
                    log.info("parentId 为空或无效值");
                    // 无值时的处理逻辑
                    // 评论帖子
                    communityContent = communityContentService.findById(Long.valueOf(annotation));
                    insertCommunityInteract(communityContent, znsUserEntity, Lists.newArrayList(communityContent.getUserId()), CommunityInteractionTypeEnum.COMMENT_POST.getCode(), request.getCommentText());
                }
                // 评论@
                List<MentionContentDto> mentionContentList = request.getMentionContentList();
                if (!CollectionUtils.isEmpty(mentionContentList)) {
                    Integer interactType = CommunityInteractionTypeEnum.COMMENT_MENTION.getCode();
                    List<Long> recipientIdList = mentionContentList.stream().filter(i -> Objects.equals(1, i.getType())).map(MentionContentDto::getId).collect(Collectors.toList());
                    insertCommunityInteract(communityContent, znsUserEntity, recipientIdList, interactType, request.getCommentText());
                }
            }
        }

    }
}    