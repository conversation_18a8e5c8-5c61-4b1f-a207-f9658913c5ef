package com.linzi.pitpat.api.activityservice.controller;


import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.mapper.OneWeekConfigDao;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.OneWeekConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.FestivalActivityWalletVo;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.activity.OneWeekConfig;
import com.linzi.pitpat.data.request.ParticipateInDto;
import com.linzi.pitpat.data.resp.BlueHatHomeResp;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 节日活动接口
 */
@RestController
@RequestMapping("/app/blue/hat")
@Slf4j
@RequiredArgsConstructor
public class BlueHatController extends BaseAppController {

    @Autowired
    private ZnsUserService znsUserService;
    @Autowired
    private ZnsUserAccountService znsUserAccountService;

    @Autowired
    private OneWeekConfigService oneWeekConfigService;

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private RunActivityUserTaskService runActivityUserTaskService;

    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    @Autowired
    private ActivityStrategyContext activityStrategyContext;

    @Autowired
    private OneWeekConfigDao oneWeekConfigDao;
    @Resource
    private RedisTemplate redisTemplate;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 报名节日活动
     *
     * @param dto
     * @return
     */
    @PostMapping("/join")
    public Result participateIn(@RequestBody ParticipateInDto dto) {
        Long activityId = oneWeekConfigService.getActivityId(ConfigKeyEnums.blue_hat_activity_config.getCode());
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());

        ZonedDateTime currentDate = znsRunActivityEntity.getActivityStartTime();
        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(dto.getUserId());
        if (CollectionUtils.isEmpty(userTasks)) {
            for (int i = 0; i < 4; i++) {
                RunActivityUserTask runActivityUserTask = new RunActivityUserTask();
                ZonedDateTime date = DateUtil.addDays(currentDate, i);
                String MatchingName = "";
                BigDecimal award = BigDecimal.ZERO;
                if (Objects.equals(i, 0)) {
                    MatchingName = "Random Matching";
                    runActivityUserTask.setTaskType(1);
                    award = new BigDecimal(8);
                } else if (Objects.equals(i, 1)) {
                    MatchingName = "Random Matching";
                    award = new BigDecimal(10);
                    runActivityUserTask.setTaskType(1);
                } else if (Objects.equals(i, 2)) {
                    MatchingName = "Random Matching";
                    award = new BigDecimal(15);
                    runActivityUserTask.setTaskType(1);
                } else {
                    MatchingName = "Rich Bonus Run";
                    award = new BigDecimal(18);
                    runActivityUserTask.setTaskType(3);
                }
                if (accountEntity != null) {
                    award = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), award);
                }
                runActivityUserTask.setAward(award);
                runActivityUserTask.setCourseName(MatchingName);
                runActivityUserTask.setGmtCreate(DateUtil.startOfDate(date));
                runActivityUserTask.setGmtModified(DateUtil.startOfDate(date));
                runActivityUserTask.setUserId(dto.getUserId());
                runActivityUserTask.setStatus(0);
                runActivityUserTask.setActivityId(activityId);
                runActivityUserTask.setLevel(i + 1);
                runActivityUserTask.setActivityType(7);
                runActivityUserTaskService.insertOrUpdateRunActivityUserTask(runActivityUserTask);
            }
            ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), znsUserEntity.getId());
            // 如果run_activity_user 为空，则报名
            if (znsRunActivityUserEntity == null) {
                activityStrategyContext.handleUserActivityState(znsRunActivityEntity.getId(), 1, znsUserEntity, null, null, false, null, null, false);
            }
        }
        return CommonResult.success();
    }

    /**
     * 节日活动首页
     *
     * @param dto
     * @return
     */
    @PostMapping("/home")
    public Result home(@RequestBody ParticipateInDto dto) {
        Long activityId = oneWeekConfigService.getActivityId(ConfigKeyEnums.blue_hat_activity_config.getCode());
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
        boolean isFoo = false;
        if (ZonedDateTime.now().toInstant().toEpochMilli() > znsRunActivityEntity.getActivityEndTime().toInstant().toEpochMilli()) {
            isFoo = true;
            activityId = oneWeekConfigService.getActivityId(ConfigKeyEnums.foo_activity_config.getCode());
            ZnsUserEntity user = znsUserService.findById(dto.getUserId());
            oneWeekConfigService.fooJoin(user);
        }

        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, dto.getUserId());
        if (CollectionUtils.isEmpty(userTasks)) {
            if (isFoo) {
                fooReport(dto.getUserId());
            } else {
                ParticipateInDto participateInDto = new ParticipateInDto();
                participateInDto.setUserId(dto.getUserId());
                participateIn(participateInDto);
            }
            userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, dto.getUserId());
        }

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appVersionStr = request.getHeader("appVersion");
        int appVersion = MapUtil.getInteger(appVersionStr, 203);
        BlueHatHomeResp resp = new BlueHatHomeResp();
        Integer status = 1;
        for (RunActivityUserTask runActivityUserTask : userTasks) {
            if (Arrays.asList(0, 2).contains(runActivityUserTask.getStatus())) {
                status = 0;
                break;
            }
        }
        List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = znsRunActivityUserService.selectByTaskIds(dto.getUserId(), userTasks, Arrays.asList(1, 3), Arrays.asList(0, 1), 1, Arrays.asList(2, 3, 5), Arrays.asList(1, 4),
                ZonedDateTime.now().minusMinutes(30));
        resp.setReportNum(!CollectionUtils.isEmpty(znsRunActivityUserEntities) ? znsRunActivityUserEntities.size() : 0);
        Long fooActivityId = oneWeekConfigService.getActivityId(ConfigKeyEnums.foo_activity_config.getCode());
        ZnsRunActivityEntity fooRunActivityEntity = znsRunActivityService.selectActivityById(fooActivityId);

        OneWeekConfig oneWeekConfig = oneWeekConfigDao.selectOneWeekConfigByActivityIdUserId(fooActivityId, dto.getUserId());
        if (oneWeekConfig != null) {
            resp.setFooActivityJoin(1);
        }
        if (isFoo) {
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.foo_activity_config.getCode());

            Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
            Long skipActivityId = MapUtil.getLong(data.get("skipActivityId"), 0l);
            // 活动类型：1 组队跑,多人同跑  2 挑战跑,竞技跑 3 官方排行赛,官方赛事 4 官方组队跑，多人同跑,官方赛事 5 官方累计跑,官方赛事 6 新人福利活动
            resp.setSkipActivityId(skipActivityId);
            ZnsRunActivityEntity skipActivity = znsRunActivityService.selectActivityById(skipActivityId);
            if (Objects.equals(skipActivity.getActivityType(), 3)) {
                userTasks.get(3).setTaskType(5);                    //排行赛事
            } else if (Objects.equals(skipActivity.getActivityType(), 4)) {
                userTasks.get(3).setTaskType(4);                    // 聚合官方多人跑
            } else {
                userTasks.get(3).setTaskType(100);                    // 聚合官方多人跑
            }
        }

        resp.setFooActivityStartTime(fooRunActivityEntity.getActivityStartTime());
        resp.setFooActivityEndTime(fooRunActivityEntity.getActivityEndTime());
        resp.setFooApplicationStartTime(fooRunActivityEntity.getApplicationStartTime());
        resp.setFooApplicationEndTime(fooRunActivityEntity.getApplicationEndTime());

        Integer days = DateUtil.daysBetween(
                DateUtil.endOfDate(userTasks.get(0).getGmtCreate()),
                DateUtil.endOfDate(ZonedDateTime.now())) + 1;
        resp.setUserTasks(userTasks);
        resp.setStatus(status);
        resp.setDays(days);
        Object hasNewAward = redisTemplate.opsForValue().get(RedisConstants.FESTIVAL_HAS_NEW_AWARD_KEU + dto.getUserId());
        log.info(" dealUserTask报名活动  home " + RedisConstants.FESTIVAL_HAS_NEW_AWARD_KEU + ",hasNewAward=" + hasNewAward);
        if (Objects.nonNull(hasNewAward)) {
            resp.setHasNewAward(Integer.valueOf((String) hasNewAward));
        }
        return CommonResult.success(resp);
    }

    public void fooReport(Long userId) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.foo_activity_config.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        Long activityId = MapUtil.getLong(data.get("activityId"), 0l);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        ZonedDateTime currentDate = znsRunActivityEntity.getActivityStartTime();
        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        if (!CollectionUtils.isEmpty(userTasks)) {
            return;
        }
        ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(userId);
        for (int i = 0; i < 4; i++) {
            RunActivityUserTask runActivityUserTask = new RunActivityUserTask();
            ZonedDateTime date = DateUtil.addDays(currentDate, i);
            String MatchingName = "";
            BigDecimal award = BigDecimal.ZERO;
            if (Objects.equals(i, 0)) {
                MatchingName = "Random Matching";
                runActivityUserTask.setTaskType(3);
                award = new BigDecimal(2);
            } else if (Objects.equals(i, 1)) {
                MatchingName = "Random Matching";
                award = new BigDecimal(4);
                runActivityUserTask.setTaskType(3);
            } else if (Objects.equals(i, 2)) {
                MatchingName = "Random Matching";
                award = new BigDecimal(7);
                runActivityUserTask.setTaskType(3);
            } else {
                MatchingName = "Rich Bonus Run";
                award = new BigDecimal(10);
                ZonedDateTime earlyTime = DateUtil.formateDate(data.get("earlyTime") + "", DateUtil.YYYY_MM_DD_HH_MM_SS);
                OneWeekConfig oneWeekConfig = oneWeekConfigDao.selectOneWeekConfigByActivityUserIdType(activityId, earlyTime, userId, 3);
                if (oneWeekConfig != null) {
                    award = new BigDecimal(20);
                }
                runActivityUserTask.setTaskType(3);
            }
            if (accountEntity != null) {
                award = I18nConstant.currencyFormat(accountEntity.getCurrencyCode(), award);
            }
            runActivityUserTask.setAward(award);
            runActivityUserTask.setCourseName(MatchingName);
            runActivityUserTask.setGmtCreate(DateUtil.startOfDate(date));
            runActivityUserTask.setGmtModified(DateUtil.startOfDate(date));
            runActivityUserTask.setUserId(userId);
            runActivityUserTask.setStatus(0);
            runActivityUserTask.setActivityId(activityId);
            runActivityUserTask.setLevel(i + 1);
            runActivityUserTask.setActivityType(7);
            runActivityUserTaskService.insertOrUpdateRunActivityUserTask(runActivityUserTask);
        }
        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), znsUserEntity.getId());
        // 如果run_activity_user 为空，则报名
        if (znsRunActivityUserEntity == null) {
            activityStrategyContext.handleUserActivityState(znsRunActivityEntity.getId(), 1, znsUserEntity, null, null, false, null, null, false);
        }
    }


    @PostMapping("/registerUsers")
    public Result registerUsers(@RequestBody ParticipateInDto dto) {
        String[] userIds = dto.getUserIds().split(",");
        Long activityId = oneWeekConfigService.getActivityId(ConfigKeyEnums.blue_hat_activity_config.getCode());
        for (String userIdStr : userIds) {
            try {
                Long userId = MapUtil.getLong(userIdStr, 0l);
                ZnsUserEntity user = znsUserService.findById(userId);
                log.info("删除接口  email = " + user.getEmailAddressEn());
                oneWeekConfigService.insertOneWeekConfigByActivityIdUserIdType(activityId, user.getId(), 2);
                //token保存一年
            } catch (Exception e) {
                log.error("异常", e);
            }
        }
        return CommonResult.success();
    }

    /**
     * 获取节日钱包信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/wallet")
    public Result wallet(@RequestBody ParticipateInDto dto) {
        ZnsUserEntity user = getLoginUser();

        dto.setActivityKey(ConfigKeyEnums.foo_activity_config.getCode());

        Long activityId = oneWeekConfigService.getActivityId(dto.getActivityKey());

        FestivalActivityWalletVo festivalActivityWalletVo = runActivityUserTaskService.selectFestivalActivityWallet(user.getId(), activityId, 7, 0);
        redisTemplate.delete(RedisConstants.FESTIVAL_HAS_NEW_AWARD_KEU + user.getId());
        return CommonResult.success(festivalActivityWalletVo);
    }


    public static void main(String[] args) {

        Integer days = DateUtil.daysBetween(
                DateUtil.endOfDate(DateUtil.addDays(ZonedDateTime.now(), -2)),
                DateUtil.endOfDate(ZonedDateTime.now())) + 1;
        System.out.println(days);
    }
}
