package com.linzi.pitpat.api.bussiness.impl;


import com.linzi.pitpat.api.bussiness.AppConfigPopManager;
import com.linzi.pitpat.api.dto.UserMedalDto;
import com.linzi.pitpat.api.dto.vo.AppConfigPopVo;
import com.linzi.pitpat.api.dto.vo.LimitedEditionPopVo;
import com.linzi.pitpat.api.dto.vo.MedalPopVo;
import com.linzi.pitpat.api.dto.vo.NewOrderPopVo;
import com.linzi.pitpat.api.dto.vo.NewPersonPopVo;
import com.linzi.pitpat.api.dto.vo.NewUserClubPopVo;
import com.linzi.pitpat.api.dto.vo.NewUserPkPopVo;
import com.linzi.pitpat.api.systemservice.converter.UserPopConverter;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.data.activityservice.biz.NewPersonPkBizService;
import com.linzi.pitpat.data.activityservice.model.dto.NewUserPkPopDto;
import com.linzi.pitpat.data.activityservice.model.query.award.CouponAwardDto;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entity.LimitedEditionUserAwardDo;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.MedalI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.query.LimitedEditionUserAwardQuery;
import com.linzi.pitpat.data.awardservice.model.query.MedalI18nQuery;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.LimitedEditionUserAwardService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.MedalI18nService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.clubservice.constant.enums.ClubMemberStateEnum;
import com.linzi.pitpat.data.clubservice.manager.ClubManager;
import com.linzi.pitpat.data.clubservice.manager.ClubMemberManager;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.request.ClubSearchQuery;
import com.linzi.pitpat.data.clubservice.model.response.UserClubListRespDto;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.PopTypeEnum;
import com.linzi.pitpat.data.enums.RouteConfigEnum;
import com.linzi.pitpat.data.enums.UserScopeEnum;
import com.linzi.pitpat.data.enums.VipPassRemindEnum;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentLimitEditionAwardDo;
import com.linzi.pitpat.data.equipmentservice.event.LimitedEditionUserAwardEvent;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderQuery;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.enums.BannerJumpTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigPopFunctionEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigPopStatusEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigPopTriggerFrequencyEnum;
import com.linzi.pitpat.data.systemservice.enums.PopPositionEnum;
import com.linzi.pitpat.data.systemservice.enums.PushRegionEnum;
import com.linzi.pitpat.data.systemservice.manager.RegionBizService;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.model.entity.ConfigPop;
import com.linzi.pitpat.data.systemservice.model.entity.ConfigPopI18n;
import com.linzi.pitpat.data.systemservice.model.entity.ConfigPopRecord;
import com.linzi.pitpat.data.systemservice.model.entity.PopRecord;
import com.linzi.pitpat.data.systemservice.model.query.ConfigPopRecordQuery;
import com.linzi.pitpat.data.systemservice.model.query.PopUserQuery;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.ConfigPopI18nService;
import com.linzi.pitpat.data.systemservice.service.ConfigPopRecordService;
import com.linzi.pitpat.data.systemservice.service.ConfigPopService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.systemservice.service.PopUserService;
import com.linzi.pitpat.data.userservice.dto.api.response.LimitedEditionAwardResponse;
import com.linzi.pitpat.data.userservice.dto.api.response.traffic.investment.TrafficInvestmentAwardResponse;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.model.entity.UserAccountUpgradeRecordDo;
import com.linzi.pitpat.data.userservice.model.entity.UserRelatedDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemind;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindBeta;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindI18nMsg;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindI18nMsgBeta;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipPassRemindRecord;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipRule;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUserPassRecord;
import com.linzi.pitpat.data.userservice.model.query.UserAccountUpgradeRecordQuery;
import com.linzi.pitpat.data.userservice.model.query.UserGroupRelQuery;
import com.linzi.pitpat.data.userservice.model.query.VipPassQuery;
import com.linzi.pitpat.data.userservice.model.query.VipRuleQuery;
import com.linzi.pitpat.data.userservice.model.vo.TrafficInvestmentUserMedalIdsDto;
import com.linzi.pitpat.data.userservice.service.UserAccountUpgradeRecordService;
import com.linzi.pitpat.data.userservice.service.UserRelatedService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindBetaService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindI18nMsgBetaService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindI18nMsgService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindRecordService;
import com.linzi.pitpat.data.userservice.service.vip.VipPassRemindService;
import com.linzi.pitpat.data.userservice.service.vip.VipRuleService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserPassRecordService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service("appConfigPopManager")
public class AppConfigPopManagerImpl implements AppConfigPopManager {

    @Resource
    private ConfigPopService configPopService;

    @Resource
    private PopUserService popUserService;

    @Resource
    private ConfigPopRecordService configPopRecordService;
    @Resource
    private ConfigPopI18nService configPopI18nService;
    @Resource
    private ZnsUserService znsUserService;
    @Resource
    private ZnsUserAccountService znsUserAccountService;
    @Resource
    private VipPassRemindService vipPassRemindService;
    @Resource
    private VipPassRemindBetaService vipPassRemindBetaService;
    @Resource
    private VipRuleService vipRuleService;
    @Resource
    private UserGroupService userGroupService;

    @Resource
    private VipPassRemindI18nMsgService vipPassRemindI18nMsgService;
    @Resource
    private VipPassRemindI18nMsgBetaService vipPassRemindI18nMsgBetaService;
    @Resource
    private VipUserPassRecordService vipUserPassRecordService;
    @Resource
    private AppRouteService appRouteService;
    @Resource
    private VipPassRemindRecordService vipPassRemindRecordService;
    @Resource
    private RegionBizService regionBizService;

    @Resource
    private UserGroupRelService userGroupRelService;

    @Resource
    private UserAccountUpgradeRecordService userAccountUpgradeRecordService;
    @Resource
    private ClubMemberManager clubMemberManager;
    @Resource
    private ClubService clubService;
    @Resource
    private UserRelatedService userRelatedService;
    @Resource
    private PopRecordService popRecordService;
    @Resource
    private ClubManager clubManager;
    @Resource
    private NewPersonPkBizService newPersonPkBizService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private UserPopConverter userPopConverter;
    @Resource
    private UserMedalService userMedalService;
    @Resource
    private MedalConfigService medalConfigService;
    @Resource
    private MedalI18nService medalI18nService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private LimitedEditionUserAwardService limitedEditionUserAwardService;
    @Resource
    private ZnsOrderService znsOrderService;
    @Resource
    private ZnsOrderItemService znsOrderItemService;
    @Resource
    private ZnsGoodsSkuService znsGoodsSkuService;
    @Resource
    private CouponService couponService;
    @Resource
    private CouponI18nService couponI18nService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private AppRouteConfigService appRouteConfigService;

    @Override
    public AppConfigPopVo getHomePageConfigPop(Long userId) {
        // 无特殊用途的普通首页弹窗
        List<ConfigPop> popList = getUserValidConfigPopList(userId, PopPositionEnum.HOME_PAGE_POP.getCode(), ConfigPopFunctionEnum.NONE.getCode());
        log.info("获取到的普通首页弹窗数据------------------------>{}", popList.toString());
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        String langCode = I18nMsgUtils.getLangCode();
        if (!CollectionUtils.isEmpty(popList)) {
            popList = popList.stream().filter(item -> {
                // 判断当前用户是否需要做版本控制 为1表示显示2.12的页面
                if (znsUserEntity.getNeedVersionControl() == 1) {
                    Integer redirectType = item.getRedirectType();
                    if (Objects.isNull(redirectType)) {
                        ConfigPopI18n popI18n = configPopI18nService.selectByPopId(item.getId(), langCode, item.getDefaultLangCode());
                        redirectType = popI18n.getRedirectType();
                    }
                    // 判断该弹窗跳转类型是不是3.0赛事 如果是则过滤掉
                    if (BannerJumpTypeEnum.NEW_ACTIVITY.getJumpType().equals(redirectType)) {
                        return false;
                    }
                }

                // 查询弹窗记录
                ConfigPopRecordQuery configPopRecordQuery = ConfigPopRecordQuery.builder().userId(userId).configPopId(item.getId()).build();
                ConfigPopRecord configPopRecord = configPopRecordService.findByQuery(configPopRecordQuery);
                if (!Objects.isNull(configPopRecord)) {
                    // 判断执行频率
                    ZonedDateTime popCreateTime = configPopRecord.getCreateTime();
                    Calendar calendar = getCalendar(popCreateTime);
                    ZonedDateTime now = Date.from(ZonedDateTime.now().toInstant());
                    Integer triggerFrequency = item.getTriggerFrequency();
                    if (Objects.equals(ConfigPopTriggerFrequencyEnum.ONLY_ONCE.getCode(), triggerFrequency)
                            || (Objects.equals(ConfigPopTriggerFrequencyEnum.ONCE_A_DAY.getCode(), triggerFrequency) && DateUtil.addDays(calendar.toInstant().toEpochMilli(), 1).isAfter(now))
                            || (Objects.equals(ConfigPopTriggerFrequencyEnum.ONCE_A_WEEK.getCode(), triggerFrequency) && DateUtil.addDays(calendar.toInstant().toEpochMilli(), 7).isAfter(now))) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(popList)) return null;
            // 获取第一个弹窗
            ConfigPop configPop = popList.stream()
                    .sorted(Comparator.comparing(ConfigPop::getPriority, Comparator.reverseOrder()).thenComparing(ConfigPop::getGmtCreate, Comparator.reverseOrder())).findFirst().get();
            AppConfigPopVo appConfigPopVo = BeanUtil.copyBean(configPop, AppConfigPopVo.class);
            // 添加弹窗记录
            ConfigPopRecord record = new ConfigPopRecord();
            record.setUserId(userId);
            record.setConfigPopId(configPop.getId());
            configPopRecordService.insertConfigPopRecord(record);

            if (Objects.nonNull(appConfigPopVo)) {
                ConfigPopI18n popI18n = configPopI18nService.selectByPopId(appConfigPopVo.getId(), langCode, appConfigPopVo.getDefaultLangCode());
                if (Objects.nonNull(popI18n) && StringUtils.hasText(popI18n.getPics())) {
                    BeanUtils.copyProperties(popI18n, appConfigPopVo, "id");
                }
                AppRouteConfig appRouteConfig = appRouteConfigService.findById(appConfigPopVo.getRouteId());
                ZnsUserEntity userEntity = znsUserService.findById(userId);
                if (Objects.nonNull(appRouteConfig) && Objects.equals(appRouteConfig.getOneLevel(), RouteConfigEnum.FREE_CHALLENGE.getOneLevel()) && Objects.equals(appRouteConfig.getOneLevel(), RouteConfigEnum.FREE_CHALLENGE.getTwoLevel())
                        && Objects.nonNull(userEntity) && userEntity.getAppVersion() < VersionConstant.V4_7_5) {
                    return null;
                }
                appConfigPopVo.setAdvertisingImage(appConfigPopVo.getPics());
                appConfigPopVo.setHomePagePopId(appConfigPopVo.getId());
                // 组装跳转链接
                PrimitiveForest forest = BeanUtil.copyBean(appConfigPopVo, PrimitiveForest.class);
                forest.setUserId(userId);
                forest.setJumpType(appConfigPopVo.getRedirectType());
                forest.setJumpUrl(appConfigPopVo.getActivityUrl());
                forest.setMainActivityType(appConfigPopVo.getMainActivityType());
                forest.setRunActivityId(appConfigPopVo.getRunActivityId());
                if (Objects.nonNull(popI18n)) {
                    forest.setMallJumpType(popI18n.getMallJumpType());
                    forest.setCategoryCode(popI18n.getCategoryCode());
                    forest.setGoodsId(popI18n.getGoodsId());
                }
                AppRoute route = appRouteService.findRoute(forest);
                appConfigPopVo.setUrl(route.getJumpUrl());
                appConfigPopVo.setJumpParam(route.getJumpParam());
            }

            return appConfigPopVo;
        }
        return null;
    }

    @Override
    public AppConfigPopVo getRemindConfigPop(Long userId) {
        List<ConfigPop> popList = getUserValidConfigPopList(userId, PopPositionEnum.REMIND_POP.getCode(), ConfigPopFunctionEnum.NONE.getCode());
        if (CollectionUtils.isEmpty(popList)) {
            return null;
        }
        // 2.10版本暂时简写
        return BeanUtil.copyBean(popList.get(0), AppConfigPopVo.class);
    }

    /**
     * 币种统一升级弹框
     *
     * @param userId
     */
    @Override
    public AppConfigPopVo getAccountUpdateConfigPopV2(Long userId) {
        log.info("AppConfigPopManagerImpl#getAccountUpdateConfigPopV2----当前用户{},币种统一升级弹框开始", userId);
        //查询弹框记录
        UserAccountUpgradeRecordQuery query = UserAccountUpgradeRecordQuery.builder().userId(userId).popTimeIsNull(true).build();
        UserAccountUpgradeRecordDo upgradeRecordDo = userAccountUpgradeRecordService.findByQuery(query);
        if (upgradeRecordDo == null) {
            //没有升级记录
            return null;
        }
        //弹框内容
        AppConfigPopVo appConfigPopVo = new AppConfigPopVo();
        appConfigPopVo.setPopType(PopPositionEnum.HOME_PAGE_POP.getCode()); //首页弹框
        String content = I18nMsgUtils.getMessage("home.pop.update.account.msg.v2", upgradeRecordDo.getExchangeRate());
        appConfigPopVo.setContent(content);
        //更新弹框时间
        upgradeRecordDo.setPopTime(ZonedDateTime.now());
        userAccountUpgradeRecordService.update(upgradeRecordDo);
        log.info("AppConfigPopManagerImpl#getAccountUpdateConfigPopV2----当前用户{},币种统一升级弹框结束", userId);
        return appConfigPopVo;
    }


    @Override
    public AppConfigPopVo getVipPassRemindConfigPop(Long userId) {
        AppConfigPopVo configPopVo = new AppConfigPopVo();
        ZnsUserEntity znsUser = znsUserService.findById(userId);
        ZonedDateTime nowDate = DateUtil.getNowDate();
        Integer lastVipType = null;
        // 语言
        String langCode = I18nMsgUtils.getLangCode();
        List<VipPassRemind> list = null;
        boolean isBeta = false;
        //查询用户的内测版本
        List<VipPassRemindBeta> betaList = getBetaVipPassRemind(userId);
        if (!CollectionUtils.isEmpty(betaList)) {
            //内测可用,直接用内测
            list = BeanUtil.copyBeanList(betaList, VipPassRemind.class);
            isBeta = true;
        } else {
            //查询公测版本
            list = vipPassRemindService.getList(VipPassQuery.builder().remindTypeList(Arrays.asList(VipPassRemindEnum.HOME_POP.getCode())).status(1).build());
        }
        if (CollectionUtils.isEmpty(list)) {
            //没有可用提醒
            log.info("HomePageAppController#match-----会员到期提醒弹框,userId:{},没有可用提醒", userId);
            return null;
        }
        // 到期前提醒
        VipPassRemind beforeRemind = vipPassRemindService.getBefore(list, nowDate, znsUser);
        // 到期后提醒
        VipPassRemind afterRemind = vipPassRemindService.getAfter(list, nowDate, znsUser);
        // 优先过期前提醒
        VipPassRemind sendRemind = Objects.nonNull(beforeRemind) ? beforeRemind : afterRemind;
        if (Objects.nonNull(sendRemind)) {
            Integer vipType = znsUser.getVipType();
            if (sendRemind.getBeforeAfter() == 1) {
                // 如果是到期后提醒 取得类型是过期前的会员等级
                VipUserPassRecord vipUserPassRecord = vipUserPassRecordService.getOneByQuery(VipPassQuery.builder().userId(znsUser.getId()).build());
                vipType = vipUserPassRecord.getVipType();
            }
            Long remindId = sendRemind.getId();
            VipPassRemindI18nMsg i18nMsg = null;
            if (isBeta) {
                //内测版
                List<VipPassRemindI18nMsgBeta> betaI18nList = vipPassRemindI18nMsgBetaService.findByQuery(VipPassQuery.builder().remindId(remindId).vipType(vipType).langCode(langCode).defaultLangCode(sendRemind.getDefaultLangCode()).build());
                if (!CollectionUtils.isEmpty(betaI18nList)) {
                    i18nMsg = BeanUtil.copyBean(betaI18nList.get(0), VipPassRemindI18nMsg.class);
                }
            } else {
                //公测版
                i18nMsg = vipPassRemindI18nMsgService.findOneByQuery(VipPassQuery.builder().remindId(remindId).vipType(vipType).langCode(langCode).defaultLangCode(sendRemind.getDefaultLangCode()).build());
            }
            if (Objects.isNull(i18nMsg)) {
                return null;
            }
            // 组装跳转链接
            PrimitiveForest forest = new PrimitiveForest();
            forest.setUserId(userId);
            forest.setJumpType(sendRemind.getJumpType());
            forest.setJumpUrl(sendRemind.getJumpValue());
            forest.setRouteId(sendRemind.getRouteId());
            AppRoute route = appRouteService.findRoute(forest);
            configPopVo.setPics(i18nMsg.getPics());
            configPopVo.setContent(i18nMsg.getPopContent());
            configPopVo.setUrl(route.getJumpUrl());
            configPopVo.setJumpParam(route.getJumpParam());

            // 增加提醒记录
            VipPassRemindRecord record = new VipPassRemindRecord();
            record.setUserId(znsUser.getId());
            record.setRemindId(sendRemind.getId());
            record.setMsgId(i18nMsg.getId());
            record.setType(VipPassRemindEnum.HOME_POP.getCode());
            record.setResult(1);
            record.setBeforeAfter(sendRemind.getBeforeAfter());
            vipPassRemindRecordService.insert(record);
        }

        return configPopVo;
    }

    @Override
    @FillerMethod
    public NewUserClubPopVo getClubRecommendRespDtoList(Long userId) {
        UserRelatedDo byUserId = userRelatedService.findByUserId(userId);
        if (Objects.nonNull(byUserId) && Objects.equals(byUserId.getRecommendClubPop(), 1)) {
            return null;
        }
        if (!clubMemberManager.checkNewUerClub(userId)) return null;
        ClubSearchQuery clubSearchQuery = new ClubSearchQuery();
        clubSearchQuery.setIsRecommend(1);
        List<Club> list = clubService.findListByQuery(clubSearchQuery);
        List<UserClubListRespDto> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            Map<Long, ClubMemberStateEnum> joinState = clubManager.getJoinState(list.stream().map(Club::getId).collect(Collectors.toList()), userId);
            Collections.shuffle(list); // 先打乱顺序
            result = list.stream()
                    .limit(5).map(s -> {
                        UserClubListRespDto userClubListRespDto = new UserClubListRespDto();
                        BeanUtils.copyProperties(s, userClubListRespDto);
                        userClubListRespDto.setClubId(s.getId());
                        userClubListRespDto.setOwner(Objects.equals(userId, s.getOwnerUserId()));
                        ZnsUserEntity byId = znsUserService.findById(s.getOwnerUserId());
                        userClubListRespDto.setOwnerName(Objects.nonNull(byId) ? byId.getFirstName() : null);
                        userClubListRespDto.setUserJoinState(joinState.get(s.getId()).getCode());
                        return userClubListRespDto;
                    }).collect(Collectors.toList());
        }
        NewUserClubPopVo newUserClubPopVo = new NewUserClubPopVo();
        newUserClubPopVo.setClubRecommendRespDtoList(result);
        return newUserClubPopVo;
    }

    @Override
    public NewUserPkPopVo getNewUserPkPop(Long userId) {
        //缓存判断是否有参加
        Object newUserOfflinePkRedis = redisTemplate.opsForValue().get(RedisConstants.NEW_USER_OFFLINE_PK_JOIN + userId);
        if (Objects.nonNull(newUserOfflinePkRedis)) {
            //新人引导离线pk赛结果弹窗 4.0 以后不显示新人弹框
            PopRecord newUserOfflinePk = popRecordService.selectPopByDate(userId, null, PopTypeEnum.NEW_USER_OFFLINE_PK.getType(), 1);
            if (Objects.isNull(newUserOfflinePk)) {
                Long runDataDetailsId = Long.valueOf((String) newUserOfflinePkRedis);
                NewUserPkPopDto popDto = newPersonPkBizService.findNewUserPkPop(runDataDetailsId);
                popRecordService.addPop(userId, PopTypeEnum.NEW_USER_OFFLINE_PK.getType(), 1, 0l);
                if (Objects.equals(popDto.getIsCheat(), 1)) {
                    return userPopConverter.toVo(popDto);
                }
            }
        }
        return null;
    }

    @Override
    public NewPersonPopVo getNewPersonPop(Long userId) {
        NewPersonPopVo newPersonPopVo = new NewPersonPopVo();
        ZnsUserEntity user = znsUserService.findById(userId);
        boolean newUser = TaskConstant.TaskIdentityTypeEnum.isNewUser(user.getCreateTime());
        if (!newUser) {
            return null;
        }
        RBucket<Object> bucket = redissonClient.getBucket(RedisKeyConstant.NEW_PERSON_POP + userId);
        if (Objects.nonNull(bucket.get())) {
            return null;
        }
        Club myNewClub = clubService.findMyNewClub(userId).stream().findFirst().orElse(null);
        if (Objects.isNull(myNewClub)) {
            return null;
        }
        newPersonPopVo.setClubName(myNewClub.getName());
        newPersonPopVo.setDescription(I18nMsgUtils.getMessage("club.new.person.pop"));
        newPersonPopVo.setUrl("lznative://lzrace/ClubHome");
        newPersonPopVo.setJumpParam("{\"clubId\":" + myNewClub.getId() + "}");
        long secondsInDay = ZonedDateTimeUtil.calculateRemainingSecondsInDay(ZonedDateTime.now());
        bucket.set("1", secondsInDay, TimeUnit.SECONDS);
        return newPersonPopVo;
    }


    @Override
    public MedalPopVo getMedalPopVo(Long userId, String languageCode) {
        String s = sysConfigService.selectConfigByKey("trafficInvestment.medal.ids", true);
        TrafficInvestmentUserMedalIdsDto medalPopVo = JsonUtil.readValue(s, TrafficInvestmentUserMedalIdsDto.class);
        List<Long> medalConfigIds = new ArrayList<>();
        medalConfigIds.add(medalPopVo.getUpgrade());
        medalConfigIds.add(medalPopVo.getBind());
        List<UserMedal> userMedalList = userMedalService.selectUserMedalByUserIdObtainIsPop(userId, 1, 0, 0, medalConfigIds);
        if (CollectionUtils.isEmpty(medalConfigIds)) {
            return null;
        }
        List<MedalConfig> medalConfigs = medalConfigService.selectMedalConfigListByIds(userMedalList.stream().map(UserMedal::getMedalConfigId).collect(Collectors.toList()));

        Map<Long, MedalConfig> medalConfigMap = new HashMap<>();
        medalConfigs.forEach(medalConfig -> medalConfigMap.put(medalConfig.getId(), medalConfig));
        List<UserMedalDto> userMedalDtos = new ArrayList<>();
        for (UserMedal userMedal : userMedalList) {
            UserMedalDto userMedalDto = new UserMedalDto();
            BeanUtils.copyProperties(userMedal, userMedalDto);
            if (!CollectionUtils.isEmpty(medalConfigMap)) {
                MedalConfig medalConfig = medalConfigMap.get(userMedal.getMedalConfigId());
                if (Objects.nonNull(medalConfig)) {
                    userMedalDto.setUrl(medalConfig.getUrl());
                    // 查询国际化数据
                    MedalI18n medalI18n = medalI18nService.findByQuery(MedalI18nQuery.builder().medalId(userMedal.getMedalConfigId()).langCode(languageCode).defaultLangCode(medalConfig.getDefaultLangCode()).build());
                    userMedalDto.setName(Objects.nonNull(medalI18n) ? medalI18n.getName() : medalConfig.getName());
                    userMedalDto.setRemark(Objects.nonNull(medalI18n) ? medalI18n.getRemark() : medalConfig.getRemark());
                }
            }
            userMedalDtos.add(userMedalDto);
            userMedalService.updateUserMedalIsPopByUserId(1, userMedal.getId());
        }
        if (!CollectionUtils.isEmpty(userMedalDtos)) {
            MedalPopVo resultPop = new MedalPopVo();
            resultPop.setUserMedalDtos(userMedalDtos);
            return resultPop;
        }
        return null;
    }

    /**
     * 查询用户会员到期提醒的内测版本
     *
     * @param userId
     * @return
     */
    private List<VipPassRemindBeta> getBetaVipPassRemind(Long userId) {
        List<VipPassRemindBeta> betaList = vipPassRemindBetaService.getList(VipPassQuery.builder().remindTypeList(Arrays.asList(VipPassRemindEnum.HOME_POP.getCode())).status(1).build());
        if (CollectionUtils.isEmpty(betaList)) {
            return null;
        }
        VipRule vipRule = vipRuleService.getOneByQuery(VipRuleQuery.builder().build());
        String betaGroupIds = vipRule.getBetaGroupIds();
        if (!StringUtils.hasText(betaGroupIds)) {
            return null;
        }
        List<Long> groupIds = Arrays.asList(betaGroupIds.split(",")).stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
        List<ZnsUserEntity> groupUsers = userGroupService.getUserByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(groupUsers)) {
            return null;
        }
        Set<Long> groupUserId = groupUsers.stream().map(ZnsUserEntity::getId).collect(Collectors.toSet());
        if (!groupUserId.contains(userId)) {
            return null;
        }
        return betaList;
    }

    /**
     * 查询该用户生效的弹窗列表
     *
     * @param userId
     * @return
     */
    private List<ConfigPop> getUserValidConfigPopList(Long userId, Integer configPopType, Integer popFunction) {
        log.info("开始获取弹窗列表------------------------>用户id：{},弹窗类型：{},弹窗功能:{}", userId, configPopType, popFunction);
        // 根据推送地区进行筛选
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        List<ConfigPop> resultList = new ArrayList<>();

        // 所有可用的弹窗
//        LambdaQueryWrapper<ConfigPop> configPopWrapper = new LambdaQueryWrapper<>();
//        configPopWrapper
//                .eq(ConfigPop::getPopType, configPopType)
//                .eq(ConfigPop::getPopFunction,popFunction)
//                .eq(ConfigPop::getPopStatus, ConfigPopStatusEnum.PROGRESS_STATE.getCode())
//                .eq(ConfigPop::getDeleted, 0);


        ZonedDateTime current = DateUtil.addHours(ZonedDateTime.now(), -8);
        List<ConfigPop> popList = configPopService.getUserValidConfigPopList(configPopType, popFunction, ConfigPopStatusEnum.PROGRESS_STATE.getCode(), ZonedDateTime.now(), DateUtil.getDate2ByTimeZone(current,
                TimeZone.getTimeZone(znsUserEntity.getZoneId())));
        if (CollectionUtils.isEmpty(popList)) {
            return resultList;
        }

        //校验首页弹框区域信息
        List<Long> allPopIds = popList.stream().map(ConfigPop::getId).collect(Collectors.toList());
        Map<Long, Boolean> regionMap = regionBizService.checkUserPopRegion(userId, allPopIds, PushRegionEnum.HOME_POP_REGION.getType());
        Iterator<ConfigPop> iterator = popList.iterator();
        while (iterator.hasNext()) {
            ConfigPop next = iterator.next();
            boolean regionResult = regionMap.getOrDefault(next.getId(), false);
            if (!regionResult) {
                // 不包含用户区域的剔除
                log.info("AppConfigPopManagerImpl#getUserValidConfigPopList--------校验首页弹框区域不符合，userId={},id={}", userId, next.getId());
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(popList)) {
            return resultList;
        }
        Iterator<ConfigPop> iterator3 = popList.iterator();
        while (iterator3.hasNext()) {
            ConfigPop next = iterator3.next();
            Integer userScope = next.getUserScope();
            String groupIdStr = next.getGroupIdStr();
            Long popId = next.getId();
            if (UserScopeEnum.GROUP_SELECTION.getCode().equals(userScope)
                    && StringUtils.hasText(groupIdStr)) {
                List<Long> groupIds = JsonUtil.readList(groupIdStr, Long.class);
                if (!CollectionUtils.isEmpty(groupIds)) {
                    UserGroupRelQuery query = UserGroupRelQuery.builder().build();
                    query.setGroupIds(groupIds);
                    query.setUserId(userId);
                    if (userGroupRelService.countByQuery(query) < 1) {
                        iterator3.remove();
                    }
                }
            }
            if (UserScopeEnum.FILE_IMPORT.getCode().equals(userScope)) {
                //fileImportRow 默认值为 -1 表示导入空文件，此时相当于全量用户导入，均可看到该弹窗
                if (next.getFileImportRow() > 0) {
                    // 当前弹窗有用户限制数据
                    PopUserQuery popUserQuery = PopUserQuery.builder().popType(configPopType).userId(userId).popupId(popId).build();
                    if (Objects.isNull(popUserService.getByQuery(popUserQuery))) {
                        iterator3.remove();
                    }
                }
            }
        }

        return popList;
    }

    /**
     * 获取弹窗记录当天的0点
     *
     * @param popCreateTime
     * @return
     */
    private static Calendar getCalendar(ZonedDateTime popCreateTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(popCreateTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar;
    }

    /**
     * 检查用户是否符合账户升级弹窗要求
     *
     * @param userId
     * @return
     */
    private boolean needUpdateAccount(Long userId) {
        // 用户注册地需要为加拿大
        ZnsUserEntity znsUserEntity = znsUserService.findById(userId);
        log.info("用户数据库版本为：{}，数据库国家为：{}", znsUserEntity.getAppVersion(), znsUserEntity.getCountryCode());
        // 用户当前app版本在3.0.0之上
        if (znsUserEntity.getAppVersion() >= 3000 && I18nConstant.CountryCodeEnum.CA.getCode().equals(znsUserEntity.getCountryCode())) {
            ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(userId);
            if (Objects.isNull(accountEntity)) {
                return false;
            }
            return I18nConstant.CurrencyCodeEnum.USD.getCode().equals(accountEntity.getCurrencyCode());
        }
        return false;
    }

    @Override
    public LimitedEditionPopVo getLimitedEditionPop(Long userId, String languageCode) {
        var limitedEditionPopVo = new LimitedEditionPopVo();
        String key = RedisConstants.USER_EQUIPMENT_LIMIT_EDITION_CLAIM_STATUS + userId;
        RBucket<Long> bucket = redissonClient.getBucket(key);
        if (bucket.isExists()) {
            Long limitConfigId = bucket.get();
            List<LimitedEditionAwardResponse> arrayList = new ArrayList<>();
            List<LimitedEditionUserAwardDo> limitedEditionUserAwardServiceList = limitedEditionUserAwardService.findList(new LimitedEditionUserAwardQuery()
                    .setClaimStatus(YesNoStatus.NO.getCode()).setUserId(userId).setLimitConfigId(limitConfigId));
            if (CollectionUtils.isEmpty(limitedEditionUserAwardServiceList)) {
                return null;
            }
            for (LimitedEditionUserAwardDo limitedEditionUserAwardDo : limitedEditionUserAwardServiceList) {
                limitedEditionUserAwardDo.setClaimStatus(YesNoStatus.YES.getCode());
                var response = new LimitedEditionAwardResponse();
                response.setType(limitedEditionUserAwardDo.getAwardType());
                response.setAmount(limitedEditionUserAwardDo.getAmount());
                response.setAwardJson(limitedEditionUserAwardDo.getAwardJson());
                arrayList.add(response);
            }
            fillTitleAndUrl(arrayList, languageCode);
            limitedEditionPopVo.setAwards(arrayList);
            limitedEditionPopVo.setDesc(I18nMsgUtils.getLangMessage(languageCode, "limited.edition.title.desc"));
            // 发送奖励 mq
            applicationEventPublisher.publishEvent(LimitedEditionUserAwardEvent.of(userId, limitedEditionUserAwardServiceList));
            // 更新领取状态
            limitedEditionUserAwardService.batchUpdate(limitedEditionUserAwardServiceList);
            redissonClient.getBucket(key).delete();
            return limitedEditionPopVo;
        }
        return null;
    }

    @Override
    public NewOrderPopVo getNewOrderPopVo(Long userId, String languageCode) {
        var newOrderPopVo = new NewOrderPopVo();
        String key = RedisConstants.USER_NEW_H5_ORDER_POP + userId;
        RBucket<Long> bucket = redissonClient.getBucket(key);
        if (bucket.isExists()) {
            return null;
        }
        var orderQuery = new OrderQuery().setUserId(userId).setSourceType(OrderConstant.OrderSourceEnum.H5_ORDER.getType());
        orderQuery.addOrderItem(OrderItem.desc("gmt_create"));
        ZnsOrderEntity znsOrderEntity = znsOrderService.findByQuery(orderQuery);
        if (Objects.isNull(znsOrderEntity)) {
            return null;
        }
        if (znsOrderEntity.getStatus().equals(OrderConstant.OrderStatusEnum.STATUS_0.getCode())) {
            newOrderPopVo.setDesc(I18nMsgUtils.getLangMessage(languageCode, "new.order.pop.title.unpay"));
        } else if (znsOrderEntity.getStatus().equals(OrderConstant.OrderStatusEnum.STATUS_1.getCode()) ||
                znsOrderEntity.getStatus().equals(OrderConstant.OrderStatusEnum.STATUS_3.getCode()) ||
                znsOrderEntity.getStatus().equals(OrderConstant.OrderStatusEnum.STATUS_4.getCode())
        ) {
            newOrderPopVo.setDesc(I18nMsgUtils.getLangMessage(languageCode, "new.order.pop.title.pay"));
        } else {
            return null;
        }
        newOrderPopVo.setOrderNo(znsOrderEntity.getOrderNo());
        List<ZnsOrderItemEntity> znsOrderItemEntities = znsOrderItemService.selectListByOrderId(znsOrderEntity.getId());
        if (CollectionUtils.isEmpty(znsOrderItemEntities)) {
            return null;
        }
        var orderItemEntity = znsOrderItemEntities.get(0);
        var znsGoodsSkuEntity = znsGoodsSkuService.findById(orderItemEntity.getSkuId());
        if (Objects.isNull(znsGoodsSkuEntity)) {
            return null;
        }
        newOrderPopVo.setUrl(znsGoodsSkuEntity.getPic());
        redissonClient.getBucket(key).set("1");
        return newOrderPopVo;
    }

    private void fillTitleAndUrl(List<LimitedEditionAwardResponse> awards, String languageCode) {
        if (CollectionUtils.isEmpty(awards)) {
            return;
        }
        String awardJson = sysConfigService.selectConfigByKey("limited.edition.award", true);
        List<TrafficInvestmentAwardResponse> baseInfo = JsonUtil.readList(awardJson, TrafficInvestmentAwardResponse.class);
        for (LimitedEditionAwardResponse award : awards) {
            baseInfo.stream().filter(a -> a.getType().equals(award.getType())).findFirst().ifPresent(a -> {
                String titleKey = "limited.edition.award." + a.getType().getType() + ".title";
                String descKey = "limited.edition.award." + a.getType().getType() + ".desc";
                award.setTitle(I18nMsgUtils.getLangMessage(languageCode, titleKey, award.getAmount() != null ? award.getAmount().toString() : ""));
                award.setDesc(I18nMsgUtils.getLangMessage(languageCode, descKey));
                if (a.getType().equals(AwardTypeEnum.COUPON)) {
                    EquipmentLimitEditionAwardDo equipmentLimitEditionAwardDo = JsonUtil.readValue(award.getAwardJson(), EquipmentLimitEditionAwardDo.class);
                    CouponAwardDto couponAwardDto = equipmentLimitEditionAwardDo.getCouponInfo();
                    Coupon coupon = couponService.findById(couponAwardDto.getCouponId());
                    CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                            .couponId(couponAwardDto.getCouponId()).langCode(LocaleContextHolder.getLocale().toString()).defaultLangCode(coupon.getDefaultLangCode()).build());
                    String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
                    award.setTitle(I18nMsgUtils.getLangMessage(languageCode, titleKey, couponTitle));
                }
                award.setImageUrl(a.getImageUrl());
            });
        }


    }
}
