package com.linzi.pitpat.api.dto.request.region;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.mallservice.converter.api.AreaConverter;
import com.linzi.pitpat.data.mallservice.mapper.ZnsAddressDao;
import com.linzi.pitpat.data.mallservice.model.entity.MallExchangeRateSwitchDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsAddressEntity;
import com.linzi.pitpat.data.mallservice.model.query.MallExchangeRateSwitchQuery;
import com.linzi.pitpat.data.mallservice.service.MallExchangeRateSwitchService;
import com.linzi.pitpat.data.mallservice.service.ZnsAddressService;
import com.linzi.pitpat.data.systemservice.dto.request.AreaRequestDto;
import com.linzi.pitpat.data.systemservice.dto.response.AreaResponseDto;
import com.linzi.pitpat.data.systemservice.dto.response.CityResponseDto;
import com.linzi.pitpat.data.systemservice.enums.RegionConstants;
import com.linzi.pitpat.data.systemservice.manager.RegionBizService;
import com.linzi.pitpat.data.systemservice.model.query.AreaQuery;
import com.linzi.pitpat.data.systemservice.model.query.CountryQuery;
import com.linzi.pitpat.data.systemservice.model.vo.AreaVo;
import com.linzi.pitpat.data.systemservice.model.vo.CountryVo;
import com.linzi.pitpat.data.systemservice.model.vo.MallCountryResponseDto;
import com.linzi.pitpat.data.systemservice.model.vo.MallCountryVo;
import com.linzi.pitpat.data.systemservice.model.vo.RegionResp;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 区域Manager类
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class RegionManager {

    private final ZnsCountryService znsCountryService;
    private final AreaService areaService;
    private final ZnsAddressDao znsAddressDao;
    private final MallExchangeRateSwitchService mallExchangeRateSwitchService;
    private final ZnsAddressService znsAddressService;
    private final AreaConverter areaConverter;
    private final UserExtraService userExtraService;
    private final RegionBizService regionBizService;

    @DataCache(value = {"languageCode", "appVersion"})
    public List<RegionResp> allList(String languageCode, Integer appVersion) {
        CountryQuery query = new CountryQuery();
        query.setState(RegionConstants.StateEnum.state_1.getCode());
        query.setLanguageCode(languageCode);
        List<CountryVo> list = znsCountryService.findByQuery(query);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(vo -> new RegionResp(vo.getName(), vo.getCode(), vo.getName())).toList();
    }

    /**
     * 查询区域列表
     *
     * @param languageCode
     * @param req
     * @return
     */
    @DataCache(value = {"languageCode", "req.shortCountry"})
    public List<AreaResponseDto> areaList(String languageCode, AreaRequestDto req) {
        //查询国家币种
        Currency currency = I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode());
        MallExchangeRateSwitchDo exchangeRateSwitchDo = mallExchangeRateSwitchService.findByQuery(new MallExchangeRateSwitchQuery().setAppSwitch(0).setCountryCode(req.getShortCountry()));
        if (Objects.nonNull(exchangeRateSwitchDo)){
            currency = I18nConstant.buildCurrency(exchangeRateSwitchDo.getCurrencyCode(),I18nConstant.CurrencyCodeEnum.USD.getCode());
        }

        //查询区域I18N
        Integer state = Objects.equals(req.getSource(), 2) ? RegionConstants.StateEnum.state_1.getCode() : null; //商城只查询启用的
        AreaQuery query = AreaQuery.builder().countryCode(req.getShortCountry()).state(state).languageCode(languageCode).build();
        List<AreaVo> list = areaService.findI18NByQuery(query);

        //组装返回结果
        List<AreaResponseDto> resp =  new ArrayList<>();
        for (AreaVo areaVo : list) {
            AreaResponseDto areaResponseDto = areaConverter.voToDto(areaVo);
            if (Objects.nonNull(currency)){
                areaResponseDto.setCurrencyCode(currency.getCurrencyCode());
                areaResponseDto.setCurrencySymbol(currency.getCurrencySymbol());
                areaResponseDto.setCurrencyName(currency.getCurrencyName());
            }
            resp.add(areaResponseDto);
        }
        return resp;
    }

    public List<CityResponseDto> cityList(String statCode) {
        ZnsAddressEntity state = znsAddressService.findByStatCode(statCode);
        if (Objects.isNull(state)) {
            return new ArrayList<>();
        }
        List<ZnsAddressEntity> list = znsAddressDao.selectList(new QueryWrapper<ZnsAddressEntity>().lambda().eq(ZnsAddressEntity::getIsDelete, 0).eq(ZnsAddressEntity::getParentId, state.getId()));
        return list.stream().map(s -> {
            CityResponseDto cityResponseDto = new CityResponseDto();
            cityResponseDto.setName(s.getName());
            cityResponseDto.setId(s.getId());
            return cityResponseDto;
        }).collect(Collectors.toList());
    }

    /**
     * 商城可用国家列表
     */
    public MallCountryResponseDto mallCountryList(String languageCode, Integer appVersion) {
        MallCountryResponseDto resp = new MallCountryResponseDto();
        List<MallCountryVo> mallCountries = regionBizService.mallCountryList(languageCode, appVersion);
        resp.setMallCountries(mallCountries);  //返回数据
        return resp;
    }

    /**
     * 修改用户商城国家
     */
    public void modifyMallCountry(Long userId, String countryCode) {
        UserExtraDo extraDo = userExtraService.findByUserId(userId);
        if (Objects.isNull(extraDo)){
            UserExtraDo userExtra = new UserExtraDo();
            userExtra.setUserId(userId);
            userExtra.setMallCountryCode(countryCode);
            userExtraService.create(userExtra);
            return;
        }
        UserExtraDo updateExtraDo = new UserExtraDo();
        updateExtraDo.setId(extraDo.getId());
        updateExtraDo.setMallCountryCode(countryCode);
        userExtraService.update(updateExtraDo);
    }
}
