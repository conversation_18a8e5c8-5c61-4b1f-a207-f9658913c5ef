package com.linzi.pitpat.api.mallservice.dto.response;

import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 创建订单返回参数
 */
@Data
@NoArgsConstructor
public class CreateOrderRespDto {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 支付截止时间
     */
    private ZonedDateTime gmtPayEnd;

    /**
     * 订单状态：0：待付款；1：完成；2:支付中；3:待发货；4：待收货；5:关闭订单；
     *
     * @see OrderConstant.OrderStatusEnum
     */
    private Integer status;


    public CreateOrderRespDto(Long orderId, String orderNo, ZonedDateTime gmtPayEnd) {
        this.orderId = orderId;
        this.orderNo = orderNo;
        this.gmtPayEnd = gmtPayEnd;
    }
    public CreateOrderRespDto(Long orderId, String orderNo, ZonedDateTime gmtPayEnd,Integer status) {
        this.orderId = orderId;
        this.orderNo = orderNo;
        this.gmtPayEnd = gmtPayEnd;
        this.status = status;
    }
}
