package com.linzi.pitpat.api.userservice.dto.response;

import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 额外质保详情返回参数
 */
@Data
public class ExtraQualityDetailRespDto {

    /**
     * 额外质保id
     */
    private Long id;

    /**
     * 整机序列号
     */
    private String printId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 何处购买，amazon：亚马逊，tiktok：TikTok，deerRun：DeerRun官网，supeRun：SupeRun官网，walmart：沃尔玛
     *
     * @see DeviceConstant.BuySourceEnum
     */
    private String buySource;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 购买日期
     */
    private ZonedDateTime orderTime;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 邮箱地址
     */
    private String emailAddress;

    /**
     * 审核状态，finished：已完成，waiting：待审核，reject：审核拒绝
     *
     * @see DeviceConstant.AuditStatusEnum
     */
    private String auditStatus;

    /**
     * 失败原因
     */
    private String remark;

    /**
     * 设备唯一编码
     */
    private String equipmentNo;

    /**
     * 是否仅查看,ture:仅查看，不能提交
     */
    private Boolean isViewed = false;
}
