package com.linzi.pitpat.api.userservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentBrandConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentBrandConfigService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.vo.BrandPopResp;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.request.BrandPopPo;
import com.linzi.pitpat.data.userservice.dto.request.ChangeBrandPo;
import com.linzi.pitpat.data.userservice.dto.request.ChangeUserThemePo;
import com.linzi.pitpat.data.userservice.dto.request.PraiseDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.data.vo.user.EquipmentBrandConfigVo;
import com.linzi.pitpat.data.vo.user.UserThemeListVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户主题管理
 *
 * <AUTHOR>
 * @date 2023/5/4 15:45
 */
@RestController
@RequestMapping("/app/user/theme")
@Slf4j
public class UserThemeController extends BaseAppController {
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsUserService userService;
    @Autowired
    private ZnsUserEquipmentService userEquipmentService;

    @Autowired
    private EquipmentBrandConfigService equipmentBrandConfigService;

    @Value("${admin.server.gamepush}")
    private String gamepush;


    @Autowired
    private ZnsTreadmillService znsTreadmillService;

    /**
     * 主题列表
     *
     * @return
     * @tag 2.2
     */
    @PostMapping("/list")
    public Result<List<UserThemeListVo>> themeList() {
        String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.USER_THEME_LIST.getCode());
        if (!StringUtils.hasText(config)) {
            return CommonResult.success(new ArrayList<>());
        }
        ZnsUserEntity user = getLoginUser();
        List<UserThemeListVo> list = JsonUtil.readList(config, UserThemeListVo.class);
        for (UserThemeListVo userThemeListVo : list) {
            if (userThemeListVo.getThemeType().equals(user.getUserTheme())) {
                userThemeListVo.setIsCheck(1);
            } else {
                userThemeListVo.setIsCheck(0);
            }
        }
        return CommonResult.success(list);
    }

    /**
     * 主题切换
     *
     * @param po
     * @return
     * @tag 2.2
     */
    @PostMapping("/change")
    public Result changeTheme(@RequestBody @Validated ChangeUserThemePo po) {
        ZnsUserEntity user = getLoginUser();
        return userService.changeTheme(user.getId(), po.getThemeType());
    }

    /**
     * 品牌弹窗
     *
     * @param po
     * @return
     * @tag 2.2
     */
    @PostMapping("/brandPopPo")
    public Result<BrandPopResp> brandPopPo(@RequestBody @Validated BrandPopPo po) {
        ZnsUserEntity znsUserEntity = userService.findById(po.getUserId());
        ZnsUserEquipmentEntity znsUserEquipmentEntity = userEquipmentService.selectByUserIdOrderByConnectTime(znsUserEntity.getId());
        BrandPopResp resp = new BrandPopResp();
        if (znsUserEquipmentEntity != null) {
            ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillLikeByUniqueCode(znsUserEquipmentEntity.getEquipmentNo());
            if (znsTreadmillEntity != null) {
                if (StringUtils.hasText(znsTreadmillEntity.getBrand())) {
                    UserThemeListVo userThemeListVo = equipmentBrandConfigService.getTheme(znsTreadmillEntity.getBrand());
                    if (!znsUserEntity.getUserTheme().equals(userThemeListVo.getThemeType()) //如果主题类型和当前主题类型不一样
                            && znsTreadmillEntity.getPopCount() <= 0) {//如果没有弹窗过
                        resp = new BrandPopResp(1, userThemeListVo);
                    }
                } else {
                    resp = new BrandPopResp(2);
                }
            }
        }
        return CommonResult.success(resp);
    }

    /**
     * 修改品牌
     *
     * @param po
     * @return
     * @tag 2.2
     */
    @PostMapping("/changeBrand")
    public Result<UserThemeListVo> changeBrand(@RequestBody @Validated ChangeBrandPo po) {
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillLikeByUniqueCodeOrByBluetoothMac(po.getEquipmentNo());
        if (Objects.isNull(znsTreadmillEntity)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("treadmill.notExist"));
        }
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.version_app_2_2_0.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        int day = MapUtil.getInteger(data.get("equipmentBrandModifyLimit"), 365);
        if (znsTreadmillEntity.getBrandSetTime() != null) {
            ZonedDateTime endDate = DateUtil.addDays(znsTreadmillEntity.getBrandSetTime(), day);
            if (endDate.toInstant().toEpochMilli() > ZonedDateTime.now().toInstant().toEpochMilli()) {
                if (day == 1) {
                    return CommonResult.fail(I18nMsgUtils.getMessage("treadmill.change.day.exceed", day));
                } else {
                    return CommonResult.fail(I18nMsgUtils.getMessage("treadmill.change.days.exceed", day));
                }
            }
        }
        znsTreadmillEntity.setBrandSetTime(ZonedDateTime.now());
        znsTreadmillEntity.setBrand(po.getBrand());
        znsTreadmillService.update(znsTreadmillEntity);
        UserThemeListVo userThemeListVo = equipmentBrandConfigService.getTheme(znsTreadmillEntity.getBrand());
        return CommonResult.success(userThemeListVo);
    }

    /**
     * 设备品牌列表
     *
     * @param po
     * @return
     * @tag 2.2
     */
    @PostMapping("/equipmentBrandList")
    public Result<EquipmentBrandConfigVo> equipmentBrandList(@RequestBody BrandPopPo po) {
        EquipmentBrandConfigVo vo = new EquipmentBrandConfigVo();
        List<EquipmentBrandConfig> equipmentBrandConfigs = equipmentBrandConfigService.selectEquipmentBrandConfigByAll();
        vo.setEquipmentBrandConfigs(equipmentBrandConfigs);
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.version_app_2_2_0.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        vo.setWarnMsg(I18nMsgUtils.getMessage("treadmill.change.days.exceed", MapUtil.getInteger(data.get("equipmentBrandModifyLimit"), 365)));
        return CommonResult.success(vo);
    }

    /**
     * 游戏弹幕点赞
     *
     * @param po
     * @return
     * @tag 2.2
     */
    @PostMapping("/praise")
    public Result<EquipmentBrandConfigVo> praise(@RequestBody PraiseDto po) {
        GamePushUtils.popSignleUser(gamepush, po.getDanmu(), po.getUserId(), 1);
        return CommonResult.success();
    }


}
