package com.linzi.pitpat.api.controller.h5;

import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.mallservice.converter.api.UserAddressConverter;
import com.linzi.pitpat.data.mallservice.dto.request.CheckZipCodeRequestDto;
import com.linzi.pitpat.data.mallservice.dto.request.LocationReqDto;
import com.linzi.pitpat.data.mallservice.dto.response.CheckZipCodeResponseDto;
import com.linzi.pitpat.data.mallservice.model.entity.MallExchangeRateSwitchDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsAddressEntity;
import com.linzi.pitpat.data.mallservice.model.query.MallExchangeRateSwitchQuery;
import com.linzi.pitpat.data.mallservice.model.query.UserAddressListQuery;
import com.linzi.pitpat.data.mallservice.service.LogisticsNonsupportPostcodeService;
import com.linzi.pitpat.data.mallservice.service.MallExchangeRateSwitchService;
import com.linzi.pitpat.data.mallservice.service.ZnsAddressService;
import com.linzi.pitpat.data.request.AddressLibReq;
import com.linzi.pitpat.data.request.BaseReq;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.ZnsCountryEntity;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.userservice.UserAddressBizService;
import com.linzi.pitpat.data.userservice.dto.request.UserAddressRequestDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddressEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddressService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.vo.UserAddressVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户地址相关接口
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/h5/address", "/app/address"})
@Slf4j
public class AddressController extends BaseH5Controller {
    @Resource
    private ZnsUserAddressService userAddressService;
    @Resource
    private ZnsAddressService addressService;
    @Resource
    private AreaService areaService;
    @Resource
    private ZnsCountryService znsCountryService;
    @Resource
    private UserAddressConverter userAddressConverter;
    @Resource
    private UserAddressBizService userAddressBizService;
    @Resource
    private ISysConfigService iSysConfigService;
    @Resource
    private LogisticsNonsupportPostcodeService logisticsNonsupportPostcodeService;
    @Resource
    private MallExchangeRateSwitchService mallExchangeRateSwitchService;
    @PostMapping("/list")
    public Result<List<UserAddressVo>> list(@RequestBody UserAddressListQuery query) {
        ZnsUserEntity user = getLoginUser();
        if (!StringUtils.hasText(query.getDataSource())) {
            query.setDataSource("mall");
        }
        List<ZnsUserAddressEntity> list = userAddressService.findByUserId(user.getId(), query);
        if (!CollectionUtils.isEmpty(list)) {
            List<UserAddressVo> result = list.stream().map(s -> {
                UserAddressVo vo = userAddressConverter.toVo(s);
                ZnsAddressEntity byStatCode = addressService.findByStatCode(s.getStateCode());
                if (byStatCode != null){
                    AreaEntity areaEntity = areaService.selectAreaByCode(byStatCode.getStateCode());
                    if (areaEntity != null){
                        ZnsCountryEntity countryEntity = znsCountryService.findByCountryCode(areaEntity.getCountryCode());
                        vo.setOceanStateCode(areaEntity.getStandardAreaCode());
                        vo.setCountryCode(countryEntity.getCode());
                    }
                }
                if (I18nConstant.CountryCodeEnum.UK.code.equals(vo.getCountryCode())){
                    //英国在钱海那边简称是 GB
                    vo.setCountryCode("GB");
                }
                return vo;
            }).toList();
            if ("mall".equals(query.getDataSource())) {
                //商城地址必须要有 名字跟州code
                result = result.stream().filter(s -> StringUtils.hasText(s.getFirstName())
                        && StringUtils.hasText(s.getLastName()) && StringUtils.hasText(s.getStateCode())).toList();
            }
            if (getAppVersion() < VersionConstant.V4_6_4) {
                //老版本用户收货地址国家只展示美国
                result = result.stream().filter(item -> item.getCountryCode().equals(I18nConstant.CountryCodeEnum.US.code)).collect(Collectors.toList());
            }
            return CommonResult.success(result);
        }
        return CommonResult.success(new ArrayList<>());
    }


    /**
     * 获取用户默认地址
     *
     * @param req
     * @return
     */
    @PostMapping("/defaultAddress")
    public Result<UserAddressVo> defaultAddress(@RequestBody BaseReq req) {
        ZnsUserEntity user = getLoginUser();
        UserAddressVo userAddressVo = new UserAddressVo();
        ZnsUserAddressEntity defaultAddress = userAddressService.getDefaultAddress(user.getId());
        if (Objects.nonNull(defaultAddress)) {
            BeanUtil.copyPropertiesIgnoreNull(defaultAddress, userAddressVo);
            if (!StringUtils.hasText(userAddressVo.getDetailAddress())) {
                // 兼容商城老地址无详细地址的情况
                userAddressVo.setDetailAddress(userAddressVo.getStreet());
            }
            AreaEntity areaEntity = areaService.selectAreaByCode(defaultAddress.getStateCode());
            userAddressVo.setStandardAreaCode(areaEntity.getStandardAreaCode());
            userAddressVo.setOceanStateCode(areaEntity.getStandardAreaCode());
        } else {
            return CommonResult.success();
        }
        return CommonResult.success(userAddressVo);
    }


    /**
     * 设置该地址为默认地址
     *
     * @param req
     * @return
     */
    @PostMapping("/setDefaultAddress")
    public Result<Void> setDefaultAddress(@RequestBody BaseReq req) {
        ZnsUserEntity user = getLoginUser();
        ZnsUserAddressEntity znsUserAddress = userAddressService.findById(req.getId());
        if (Objects.isNull(znsUserAddress)) {
            return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), CommonError.PARAM_ERROR.getMsg());
        }
        userAddressService.setUpNoDefault(user.getId(), req.getId());
        znsUserAddress.setIsDefault(1);
        userAddressService.update(znsUserAddress);
        return CommonResult.success();
    }


    @PostMapping("/edit")
    public Result edit(@RequestBody UserAddressRequestDto requestDto) {
        ZnsUserEntity user = getLoginUser();
        if (!StringUtils.hasText(requestDto.getDataSource())) {
            requestDto.setDataSource("mall");
        }
        Result result = checkParam(requestDto);
        if (Objects.nonNull(result)) {
            return result;
        }
        //查询是否有地址
        long count = userAddressService.findCountByUserId(user.getId());
        if (count == 0) {
            requestDto.setIsDefault(1);
        }
        requestDto.setUserId(user.getId());
        ZnsUserAddressEntity entity = userAddressConverter.toEntity(requestDto);
        entity.setGmtModified(ZonedDateTime.now());
        if (requestDto.getId() == null) {
            userAddressService.insert(entity);
            requestDto.setId(entity.getId());
        } else {
            SysConfig sysConfig = iSysConfigService.selectSysConfigByKey("mistake_address_ids");
            if (Objects.nonNull(sysConfig) && StringUtils.hasText(sysConfig.getConfigValue())) {
                List<Long> addressIdList = JsonUtil.readList(sysConfig.getConfigValue(), Long.class);
                if (!CollectionUtils.isEmpty(addressIdList) && addressIdList.contains(requestDto.getId())) {
                    addressIdList.remove(requestDto.getId());
                    String address = JsonUtil.writeString(addressIdList);
                    sysConfig.setConfigValue(address);
                    iSysConfigService.updateConfig(sysConfig);
                }
            }
            userAddressService.update(entity);
        }
        log.info("新增或编辑用户地址，地址id：" + requestDto.getId());
        //是否为默认【1默认/0非默认】(一个用户只有一个默认地址)
        if (requestDto.getIsDefault() == 1) {
            userAddressService.setUpNoDefault(user.getId(), requestDto.getId());
        }
        return CommonResult.success(requestDto.getId());
    }

    @PostMapping("/delete")
    public Result delete(@RequestBody BaseReq req) {
        ZnsUserEntity loginUser = getLoginUser();
        userAddressService.deleteByIdAndUserId(req.getId(), loginUser.getId());
        return CommonResult.success();
    }

    @RequestMapping("/addressLib")
    public Result addressLib(@RequestBody AddressLibReq req) {
        Map<String, Object> map = new HashMap<>();
        List<ZnsAddressEntity> list = addressService.getAddressLib(req.getParentId(), req.getLevel());
        map.put("list", list);
        return CommonResult.success(map);
    }

    /**
     * 通过查询条件获取地址
     *
     * @param req 经纬度 && 地址名称
     * @return
     */
    @PostMapping("/getOnlineAddress")
    public Result<List<UserAddressVo>> getOnlineAddress(@RequestBody LocationReqDto req) {
        return CommonResult.success(userAddressBizService.searchGeoByCondition(req));
    }

    /**
     * 校验邮编是否支持
     *
     */
    @PostMapping("/checkZipCode")
    public Result<CheckZipCodeResponseDto> checkZipCode(@RequestBody CheckZipCodeRequestDto req) {
        if (!StringUtils.hasText(req.getZipCode())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "zipCode"));
        }
        if (!StringUtils.hasText(req.getCountryCode())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "countryCode"));
        }
        Boolean isValid = logisticsNonsupportPostcodeService.isSupportPostCode(req.getCountryCode(),null,req.getZipCode());
        CheckZipCodeResponseDto responseDto = new CheckZipCodeResponseDto();
        responseDto.setIsValid(isValid);
        return CommonResult.success(responseDto);
    }


    private Result checkParam(UserAddressRequestDto address) {
        if (!StringUtils.hasText(address.getCity()) ||
                !StringUtils.hasText(address.getProvince()) ||
                !StringUtils.hasText(address.getConsignee()) ||
                !StringUtils.hasText(address.getConsigneeMobile()) ||
                !StringUtils.hasText(address.getCountry())||
                !StringUtils.hasText(address.getZipCode())
        ) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        if (!logisticsNonsupportPostcodeService.isSupportPostCode(address.getCountryCode(),null,address.getZipCode())){
            return CommonResult.fail(38001,I18nMsgUtils.getMessage("coupon.check.country"));
        }
        MallExchangeRateSwitchDo exchangeRateSwitchDo = mallExchangeRateSwitchService.findByQuery(new MallExchangeRateSwitchQuery().setAppSwitch(0).setCountryCode(address.getCountryCode()));
        if (exchangeRateSwitchDo != null && StringUtils.hasText(exchangeRateSwitchDo.getPhoneNumsLimit())){
            //判断手机号长度配置
            List<Long> phoneNumsLimit = JsonUtil.readList(exchangeRateSwitchDo.getPhoneNumsLimit(), Long.class);
            Long mobileNum = phoneNumsLimit.stream().filter(item -> (long)Optional.ofNullable(address.getConsigneeMobile()).map(String::length).orElse(0) == item).findFirst().orElse(null);
            if (mobileNum == null){
                //手机号码长度不在限制范围内
                return CommonResult.fail(38002,I18nMsgUtils.getMessage("coupon.check.country"));
            }
        }
        return null;
    }
}
