package com.linzi.pitpat.api.equipment.mananger;

import com.linzi.pitpat.api.activityservice.dto.RankedActivityConfigDto;
import com.linzi.pitpat.api.dto.response.JudgeAllResponse;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.ActivityEquipmentVersionDto;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityEquipmentConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEquipmentConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityFee;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.vo.activity.EquipmentJudgeActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.MainActivityVO;
import com.linzi.pitpat.data.activityservice.service.ActivityEquipmentConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityFeeService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentJudgeDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentVersionRequest;
import com.linzi.pitpat.data.equipmentservice.dto.request.WalkDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.WalkJudageDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.DeviceVersionResp;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillConstant;
import com.linzi.pitpat.data.equipmentservice.manager.EquipmentManager;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.label.UserGroupRelService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * 活动设备校验Manager
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EquipmentJudgeManager {

    private final EquipmentManager equipmentManager;
    private final ZnsUserEquipmentService znsUserEquipmentService;
    private final ActivityFeeService activityFeeService;
    private final ZnsUserAccountService znsUserAccountService;
    private final ZnsUserService znsUserService;
    private final MainActivityService mainActivityService;
    private final UserGroupRelService userGroupRelService;
    private final ActivityEquipmentConfigService activityEquipmentConfigService;
    private final UserFriendMatchService userFriendMatchService;
    private final ActivityDisseminateBizService activityDisseminateBizService;

    private final EquipmentConfigService equipmentConfigService;
    private final ZnsRunActivityConfigService znsRunActivityConfigService;
    private final ZnsCourseService znsCourseService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ZnsTreadmillService znsTreadmillService;

    /**
     * 用户设备是否可用于活动判定
     *
     * @param languageCode    语言
     * @param activityTypeDto 活动
     * @param appVersion      app版本
     * @param dto             入参
     * @param zoneId          时区
     * @return
     */
    public EquipmentJudgeDto judgeEquipment(String languageCode, ActivityTypeDto activityTypeDto, Integer appVersion, WalkDto dto, String zoneId) {
        Long activityId = dto.getActivityId();
        log.info("WalkController#judgeByActivityId--------judgeEquipment开始,用户id={}，活动id={}，活动类型={}，appVersion={}", dto.getUserId(), activityId, activityTypeDto.getMainType(), appVersion);
        EquipmentJudgeDto result = new EquipmentJudgeDto();
        Long userId = dto.getUserId();

        //获取pk类型 0:不是pk赛， 1 随机pk ， 2 好友pk
        Integer pkType = getPkType(activityTypeDto);
        log.info("WalkController#judgeByActivityId--------judgeEquipment获取pk类型,活动id={}，类型={}", activityId, pkType);
        result.setPkType(pkType);

        //获取活动支持的设备
        List<ActivityEquipmentConfigDto> activityEquipmentConfigs = getEquipmentConfigList(activityTypeDto.getId(), activityTypeDto.getMainType(), pkType, dto.getActivityTypeSub());
        log.info("WalkController#judgeByActivityId--------judgeEquipment活动id={}，配置数量={}", activityId, Optional.ofNullable(activityEquipmentConfigs).orElse(new ArrayList<>()).size());
        result.setActivityEquipmentConfigs(activityEquipmentConfigs);

        //校验活动是否支持用户设备，true：支持，false：不支持
        boolean isSupport = checkEquipmentJudge(userId, appVersion, pkType, activityEquipmentConfigs, activityTypeDto);
        log.info("WalkController#judgeByActivityId--------judgeEquipment活动id={}，是否支持={}", activityId, isSupport);
        Integer equipmentPop = isSupport ? TreadmillConstant.EquipmentJudgeEnum.POP_0.code : TreadmillConstant.EquipmentJudgeEnum.POP_1.code;
        result.setEquipmentPop(equipmentPop);

        //设置弹框内容
        if (!isSupport) {
            setPopContext(result, languageCode, userId, zoneId, appVersion, activityEquipmentConfigs, activityTypeDto);
        }

        //活动配置的固件版本号
        MainActivity mainActivity = activityTypeDto.getMainActivity();
        if (mainActivity != null) {
            result.setEquipmentVersion(mainActivity.getEquipmentVersion());
        }
        return result;
    }

    /**
     * 用户推荐赛事
     * 优先级：系列赛>单赛事>聚合赛
     * 数量：3
     *
     * @return
     */
    public List<EquipmentJudgeActivityVo> getRecommendActivityList(List<MainActivityVO> mainActivityVos, String languageCode) {
        //获取三个赛事
        List<EquipmentJudgeActivityVo> result = new ArrayList<>();
        Map<String, List<MainActivityVO>> map = mainActivityVos.stream().collect(Collectors.groupingBy(MainActivityVO::getMainType));
        List<MainActivityVO> seriesVOS = map.get(MainActivityTypeEnum.SERIES_MAIN.getType()); //系列赛
        List<MainActivityVO> singleVOS = map.get(MainActivityTypeEnum.SINGLE.getType()); //单赛事
        List<MainActivityVO> polyVOS = map.get(MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType()); //聚合赛
        EquipmentJudgeActivityVo activityVO1 = extractFirstNonNull(languageCode, seriesVOS, singleVOS, polyVOS);
        result.add(activityVO1);
        EquipmentJudgeActivityVo activityVO2 = extractFirstNonNull(languageCode, singleVOS, seriesVOS, polyVOS);
        if (activityVO2 != null) {
            result.add(activityVO2);
        }
        EquipmentJudgeActivityVo activityVO3 = extractFirstNonNull(languageCode, polyVOS, seriesVOS, singleVOS);
        if (activityVO3 != null) {
            result.add(activityVO3);
        }
        return result;
    }

    /**
     * 取第一个不为空的对象
     *
     * @param lists
     * @return
     */
    @SafeVarargs
    private EquipmentJudgeActivityVo extractFirstNonNull(String languageCode, List<MainActivityVO>... lists) {
        for (List<MainActivityVO> list : lists) {
            if (!CollectionUtils.isEmpty(list)) {
                MainActivityVO mainActivityVO = list.remove(0);
                EquipmentJudgeActivityVo activityVo = new EquipmentJudgeActivityVo();
                activityVo.setActivityId(mainActivityVO.getId());
                activityVo.setMainType(mainActivityVO.getMainType());
                ActivityDisseminate disseminate = activityDisseminateBizService.findByActivityIdAndLanguage(mainActivityVO.getId(), languageCode);
                if (Objects.nonNull(disseminate)) {
                    activityVo.setActivityCoverImage(disseminate.getDisseminatePics());
                    activityVo.setActivityTitle(disseminate.getTitle());
                }
                return activityVo;
            }
        }
        return null;
    }

    /**
     * 设置弹框内容
     *
     * @param result                   返回结果
     * @param appVersion               版本号
     * @param activityEquipmentConfigs 设备活动配置
     * @param activityTypeDto          活动
     */
    private void setPopContext(EquipmentJudgeDto result, String languageCode, Long userId, String zoneId, Integer appVersion,
                               List<ActivityEquipmentConfigDto> activityEquipmentConfigs, ActivityTypeDto activityTypeDto) {
        Long activityId = activityTypeDto.getId();
        if (appVersion < 3050) {
            //老版本app使用默认弹框
            log.info("WalkController#judgeByActivityId--------setPopContext活动id={}，老版本app使用默认弹框", activityId);
            result.setPopType(TreadmillConstant.EquipmentJudgeTypeEnum.POP_TYPE_0.code);
            String equipmentPopMsg = ActivityEquipmentConfigDto.equipmentConfigToPopMsg(activityEquipmentConfigs);
            result.setEquipmentPopMsg(equipmentPopMsg);
            return;
        }

        //老赛事
        if (MainActivityTypeEnum.OLD.getType().equals(activityTypeDto.getMainType())) {
            //使用默认弹框
            log.info("WalkController#judgeByActivityId--------setPopContext活动id={}，3.5版本老赛事,使用默认弹框", activityId);
            result.setPopType(TreadmillConstant.EquipmentJudgeTypeEnum.POP_TYPE_0.code);
            String equipmentPopMsg = ActivityEquipmentConfigDto.equipmentConfigToPopMsg(activityEquipmentConfigs);
            result.setEquipmentPopMsg(equipmentPopMsg);
            return;
        }

        //用户未绑定设备
        ZnsUserEquipmentEntity userEquipmentEntity = znsUserEquipmentService.findUserMaxEquipmentVersion(userId); //用户设备最大版本号
        if (Objects.isNull(userEquipmentEntity)) {
            log.info("WalkController#judgeByActivityId--------setPopContext活动id={}，3.5版本新活动,用户未绑定设备", activityId);
            result.setPopType(TreadmillConstant.EquipmentJudgeTypeEnum.POP_TYPE_1.code);
            return;
        }

        //型号是否符合（ true：支持，false：不支持）
        boolean isSupport = checkEquipmentConfigs(activityEquipmentConfigs, userId);
        if (isSupport) {
            //型号支持，判断版本号
            MainActivity mainActivity = activityTypeDto.getMainActivity();

            ZnsUserEquipmentEntity first = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, mainActivity.getEquipmentMainType());
            boolean versionCheckResult = false;
            if (activityTypeDto.isBikeActivity()) {
                long count = activityEquipmentConfigs.stream().filter(activityEquipmentConfig -> activityEquipmentConfig.getEquipmentType().equals(DeviceConstant.EquipmentTypeEnum.TYPE_20.getCode())).count();
                if(CollectionUtils.isEmpty(activityEquipmentConfigs)||(count!=activityEquipmentConfigs.size()&& count!=0)){
                    //所有型号都可以。或者两种类型的设备都有
                    ZnsUserEquipmentEntity second = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType());
                    boolean a = activityTypeDto.checkUserEquipmentAllowJoinActivity(first == null ? null : first.getEquipmentVersion(), mainActivity.getEquipmentMainType());
                    boolean b = activityTypeDto.checkUserEquipmentAllowJoinActivity(second == null ? null : second.getEquipmentVersion(), DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType());
                    versionCheckResult = a || b;
                }else if(count==0){
                    //只有脚踏机
                    ZnsUserEquipmentEntity second = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType());
                    versionCheckResult = activityTypeDto.checkUserEquipmentAllowJoinActivity(second == null ? null : second.getEquipmentVersion(), DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType());
                }else{
                    versionCheckResult = activityTypeDto.checkUserEquipmentAllowJoinActivity(first == null ? null : first.getEquipmentVersion(), mainActivity.getEquipmentMainType());
                }
            } else {
                versionCheckResult = activityTypeDto.checkUserEquipmentAllowJoinActivity(first == null ? null : first.getEquipmentVersion(), mainActivity.getEquipmentMainType());
            }
            if (!versionCheckResult) {
                //版本号不符合使用默认弹框
                log.info("WalkController#judgeByActivityId--------setPopContext活动id={}，版本号不符合使用默认弹框", activityId);
                result.setPopType(TreadmillConstant.EquipmentJudgeTypeEnum.POP_TYPE_0.code);
                String equipmentPopMsg = ActivityEquipmentConfigDto.equipmentConfigToPopMsg(activityEquipmentConfigs);
                result.setEquipmentPopMsg(equipmentPopMsg);


                if (activityTypeDto.isBikeActivity()) {
                    List<ActivityEquipmentVersionDto> prepare = ActivityEquipmentVersionDto.prepare(activityEquipmentConfigs, activityTypeDto);
                    if (!CollectionUtils.isEmpty(prepare)) {
                        //自行车，写入老字段
                        prepare.stream().filter(item -> item.getEquipmentMainType().equals(DeviceConstant.EquipmentMainTypeEnum.BIKE.getType())).findFirst().ifPresent(item -> {
                            result.setEquipmentPopMsg(item.getEquipmentPopMsg());
                        });
                        result.setEquipmentVersions(prepare);
                    }
                }
                return;
            }

        }

        //查询用户设备列表的所有型号
        List<String> equipmentModels = znsUserEquipmentService.selectEquipmentModelByUserId(userId);
        if (CollectionUtils.isEmpty(equipmentModels)) {
            return;
        }


        //推荐赛事判断
        List<MainActivityVO> mainActivityVos = getRecommendActivityIds(userId, zoneId, null, equipmentModels);
        if (CollectionUtils.isEmpty(mainActivityVos)) {
            //没有可推荐的赛事
            log.info("WalkController#judgeByActivityId--------setPopContext活动id={}，3.5版本新活动,没有可推荐的赛事", activityId);
            result.setPopType(TreadmillConstant.EquipmentJudgeTypeEnum.POP_TYPE_3.code);
            return;
        }
        //有赛事可以推荐
        result.setPopType(TreadmillConstant.EquipmentJudgeTypeEnum.POP_TYPE_2.code);
        List<EquipmentJudgeActivityVo> recommendActivityList = getRecommendActivityList(mainActivityVos, languageCode);
        result.setRecommendActivityList(recommendActivityList);
        log.info("WalkController#judgeByActivityId--------setPopContext活动id={}，3.5版本新活动,推荐的赛事数量={}", activityId, recommendActivityList.size());
    }

    /**
     * 校验用户设备是否符合活动设备要求
     *
     * @param activityEquipmentConfigs 活动设备配置
     * @return true：支持，false：不支持
     */
    private boolean checkEquipmentConfigs(List<ActivityEquipmentConfigDto> activityEquipmentConfigs, Long userId) {
        if (CollectionUtils.isEmpty(activityEquipmentConfigs)) {
            //未配置型号，支持
            return true;
        }
        //设备限制校验
        for (ActivityEquipmentConfig activityEquipmentConfig : activityEquipmentConfigs) {
            if (Objects.equals(activityEquipmentConfig.getType(), 1)) {
                //限制设备类型
                ZnsUserEquipmentEntity znsUserEquipmentEntities = znsUserEquipmentService.selectByUserIdEquipmentType(userId, MapUtil.getInteger(activityEquipmentConfig.getEquipmentInfo(), 0));
                if (znsUserEquipmentEntities != null) {
                    //设备满足要求
                    return true;
                }
            }
            if (Objects.equals(activityEquipmentConfig.getType(), 2)) {
                //限制设备型号
                ZnsUserEquipmentEntity znsUserEquipmentEntities = znsUserEquipmentService.selectByUserIdEquipmentModel(userId, activityEquipmentConfig.getEquipmentInfo());
                if (znsUserEquipmentEntities != null) {
                    //设备满足要求
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 校验活动是否支持用户设备，
     *
     * @param pkType                   0:不是pk赛， 1 随机pk ， 2 好友pk
     * @param activityEquipmentConfigs 活动设备配置
     * @param activityTypeDto          活动
     * @return true：支持，false：不支持
     */
    private boolean checkEquipmentJudge(Long userId, Integer appVersion, Integer pkType, List<ActivityEquipmentConfigDto> activityEquipmentConfigs, ActivityTypeDto activityTypeDto) {
        Long activityId = activityTypeDto.getId();
        //校验设备版本号、费用、pk赛等
        if (MainActivityTypeEnum.OLD.getType().equals(activityTypeDto.getMainType())) {
            //老赛事校验
            if (Objects.equals(pkType, 2)) {
                //好友对战，所有设备都支持
                log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，好友对战所有设备都支持", activityId);
                return true;
            }
        } else {
            //新赛事校验
            if (appVersion >= 3050) {
                //3.5版本开始，免费活动所有设备都支持
                MainActivity mainActivity = activityTypeDto.getMainActivity();
                ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(userId);
                ActivityFee feeEntry = activityFeeService.findFeeEntry(mainActivity.getId(), accountEntity.getCurrencyCode());
                if (Objects.isNull(feeEntry) || "free".equals(feeEntry.getType())) {
                    //免费
                    log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，免费活动所有设备都支持", activityId);
                    return true;
                }
            }
            //查询用户设备最大版本号
            ZnsUserEquipmentEntity userEquipmentEntity = znsUserEquipmentService.findUserMaxEquipmentVersion(userId);
            if (userEquipmentEntity == null) {
                //用户未绑定设备
                log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，用户未绑定设备", activityId);
                return false;
            }
            //检查用户设备是否可以参赛
            if (!checkUserEquipmentAllowJoinActivity(userId, appVersion, activityTypeDto, activityEquipmentConfigs))
                return false;


//            //新赛事校验活动版本号
//            MainActivity mainActivity = activityTypeDto.getMainActivity();
//            Integer equipmentVersion = mainActivity.getEquipmentVersion();
//            if (Objects.nonNull(equipmentVersion)) {
//                ZnsUserEquipmentEntity userActivityEquipmentEntity = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, mainActivity.getEquipmentMainType());
//                if (Objects.nonNull(userActivityEquipmentEntity)) {
//                    if (userActivityEquipmentEntity.getEquipmentVersion() < equipmentVersion) {
//                        //用户最大版本号 < 活动版本号
//                        log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，用户最大版本号 < 活动版本号", activityId);
//                        return false;
//                    }
//                } else {
//                    log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，用户未绑定符合活动类型大类的设备", activityId);
//                    return false;
//                }
//            }
        }

        //校验用户设备是否满足活动要求
        if (CollectionUtils.isEmpty(activityEquipmentConfigs)) {
            //活动未配置设备,所有设备都支持
            log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，活动未配置设备,所有设备都支持", activityId);
            return true;
        }
        boolean result = checkEquipmentConfigs(activityEquipmentConfigs, userId);
        if (!result) {
            log.info("WalkController#judgeByActivityId--------checkEquipmentJudge活动id={}，用户设备型号不支持", activityId);
        }
        return result;
    }

    /**
     * 检查用户拥有的设备，是否可以参加当前比赛
     *
     * @since 4.7.1
     */

    private boolean checkUserEquipmentAllowJoinActivity(Long userId, Integer appVersion, ActivityTypeDto activityTypeDto, List<ActivityEquipmentConfigDto> activityEquipmentConfigs) {
        if (CollectionUtils.isEmpty(activityEquipmentConfigs)) {
            return true;
        }
        if (activityTypeDto.isBikeActivity()) {
            long bikeCount = activityEquipmentConfigs.stream().filter(a -> a.getEquipmentType().equals(DeviceConstant.EquipmentTypeEnum.TYPE_20.getCode())).count();
            long deskBikeCount = activityEquipmentConfigs.stream().filter(a -> a.getEquipmentType().equals(DeviceConstant.EquipmentTypeEnum.TYPE_30.getCode())).count();
            if (bikeCount > 0 && deskBikeCount > 0) {
                //两个设备都有。走先判断单车在判断脚踏机的逻辑
            } else if (bikeCount > 0) {
                ZnsUserEquipmentEntity firstUserEquipment = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, activityTypeDto.getMainActivity().getEquipmentMainType());
                return activityTypeDto.checkUserEquipmentAllowJoinActivity(Objects.isNull(firstUserEquipment) ? null : firstUserEquipment.getEquipmentVersion(),
                        activityTypeDto.getMainActivity().getEquipmentMainType());
            } else if (deskBikeCount > 0) {
                return checkDeskBikeEquipment(userId, appVersion, activityTypeDto);
            }
        }
        Integer equipmentMainType = activityTypeDto.getMainActivity().getEquipmentMainType();
        ZnsUserEquipmentEntity firstUserEquipment = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, equipmentMainType);
        if (!activityTypeDto.checkUserEquipmentAllowJoinActivity(
                Objects.isNull(firstUserEquipment) ? null : firstUserEquipment.getEquipmentVersion(),
                equipmentMainType)) {
            if (activityTypeDto.isBikeActivity()) {
                return checkDeskBikeEquipment(userId, appVersion, activityTypeDto);
            }
            return false;
        }
        return true;
    }

    private boolean checkDeskBikeEquipment(Long userId, Integer appVersion, ActivityTypeDto activityTypeDto) {
        //自行车活动，查询叫脚踏车的信息
        //自行车检查没用通过，小于指定版本，要求用户升级，不进行检查
        if (VersionConstant.V4_7_1 > appVersion) {
            throw new BizI18nException("activity.enroll.desk.bike.old.version");
        }
        ZnsUserEquipmentEntity deskBikeEquipment = znsUserEquipmentService.findUserMaxEquipmentVersionByActivityDeviceType(userId, DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType());
        return activityTypeDto.checkUserEquipmentAllowJoinActivity(Objects.isNull(deskBikeEquipment) ? null : deskBikeEquipment.getEquipmentVersion(), DeviceConstant.EquipmentMainTypeEnum.BICYCLE.getType());
    }


    /**
     * 获取pk类型
     *
     * @param activityTypeDto
     * @return 0:不是pk赛， 1 随机pk ， 2 好友pk
     */
    private Integer getPkType(ActivityTypeDto activityTypeDto) {
        if (!MainActivityTypeEnum.OLD.getType().equals(activityTypeDto.getMainType())) {
            //新赛事，不是pk赛
            return 0;
        }
        ZnsRunActivityEntity znsRunActivityEntity = activityTypeDto.getRunActivity();
        if (!Objects.equals(znsRunActivityEntity.getActivityType(), 2)) {
            //不是pk赛
            return 0;
        }

        //是否有好对匹配记录，有就是好友pk，没有就是随机pk
        UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(znsRunActivityEntity.getId());
        return userFriendMatch != null ? 2 : 1;
    }

    /**
     * 获取活动支持的设备列表
     *
     * @param activityId      活动id
     * @param pkType          pk类型 0:不是pk赛， 1 随机pk ， 2 好友pk
     * @param activityTypeSub 子类型， 1 随机pk ,  2 好友对战，3 离线pk
     * @return
     */
    private List<ActivityEquipmentConfigDto> getEquipmentConfigList(Long activityId, String mainType, Integer pkType, Integer activityTypeSub) {
        if (Objects.equals(pkType, 2)) {
            //好友pk
            return equipmentConfigService.wrapperEquipmentConfigDto();
        } else if (Objects.equals(pkType, 1)) {
            //随机pk
            ZnsRunActivityConfigEntity znsRunActivityConfigEntity = znsRunActivityConfigService.selectByActivityType(2, activityTypeSub);
            if (znsRunActivityConfigEntity == null) {
                return null;
            }
            Map<String, Object> data = JsonUtil.readValue(znsRunActivityConfigEntity.getActivityConfig());
            if (data == null || data.get("activityEquipmentConfigs") == null) {
                return null;
            }
            return JsonUtil.readList(data.get("activityEquipmentConfigs"), ActivityEquipmentConfigDto.class);
        } else if (MainActivityTypeEnum.SERIES_SUB.getType().equals(mainType)) {
            //系列赛-查询主活动的设备配置
            MainActivity mainActivity = seriesActivityRelService.getMainActivityBySegmentActId(activityId);
            return activityEquipmentConfigService.selectActivityEquipmentConfigDtoByActivityId(mainActivity.getId());
        } else {
            //其他赛事
            return activityEquipmentConfigService.selectActivityEquipmentConfigDtoByActivityId(activityId);
        }
    }

    /**
     * 获取用户设备版本可以推荐的活动id
     *
     * @param userId
     * @param zoneId
     * @param equipmentVersion
     * @return
     */
    public List<MainActivityVO> getRecommendActivityIds(Long userId, String zoneId, Integer equipmentVersion, List<String> equipmentModels) {
        //赛事类型：系列赛、单赛事、聚合赛
        List<String> types = Arrays.asList(MainActivityTypeEnum.SINGLE.getType(), MainActivityTypeEnum.SERIES_MAIN.getType(), MainActivityTypeEnum.SINGLE_POLYMERIZATION_RUNNING.getType());
        //用户所在分组id
        List<Long> groupsByUserId = userGroupRelService.getGroupsByUserId(userId);
        if (CollectionUtils.isEmpty(groupsByUserId)) {
            groupsByUserId = List.of(0L);
        }
        //用户州code
        ZnsUserEntity user = znsUserService.findById(userId);
        String stateCode = user.getStateCode();
        //用户时区当前时间(精确到分，这样可以走缓存)
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        String currentTime = DateUtil.getDate3ByTimeZone(ZonedDateTime.now(), timeZone, DateUtil.YYYY_MM_DD_HH_MM);
        //报名开始时间(精确到分，这样可以走缓存)
        String applyStartTime = DateUtil.formateDateStr(DateUtil.addDays1(ZonedDateTime.now(), -1), DateUtil.YYYY_MM_DD_HH_MM);
        //活动最晚开始时间(精确到分，这样可以走缓存)
        String startEndTime = DateUtil.formateDateStr(DateUtil.addDays1(ZonedDateTime.now(), 3), DateUtil.YYYY_MM_DD_HH_MM);
        return mainActivityService.findCanApplyActivityId(currentTime, types, stateCode, user.getCountryCode(), groupsByUserId, true, equipmentVersion, applyStartTime, startEndTime, equipmentModels);
    }

    /**
     * 进入游戏前的校验
     *
     * @param znsUserEquipmentEntity 用户设备
     * @param dto                    入参
     */
    public JudgeAllResponse judgeBeforeGame(ZnsUserEquipmentEntity znsUserEquipmentEntity, WalkJudageDto dto,
                                            ActivityTypeDto activityTypeDto, ZnsUserEntity user, String productCode, Integer appVersion) {
        if (dto.getCourseId() != null && dto.getCourseId() > 0) {
            //课程跑校验设备
            log.info("WalkController#judgeAll------judgeBeforeGame,课程跑校验设备,courseId={}", dto.getCourseId());
            return courseCheckJudge(dto, znsUserEquipmentEntity, productCode);
        } else if (dto.getActivityId() != null && dto.getActivityId() > 0) {
            //根据活动id校验设备
            log.info("WalkController#judgeAll------judgeBeforeGame,根据活动id校验设备,activityId={}", dto.getActivityId());
            return activityIdCheckJudge(dto, znsUserEquipmentEntity, user, activityTypeDto, productCode, appVersion);
        } else if (dto.getActivityType() != null) {
            //根据活动type校验设备
            log.info("WalkController#judgeAll------judgeBeforeGame,根据活动type校验设备,activityType={}", dto.getActivityType());
            return activityTypeCheckJudge(dto, znsUserEquipmentEntity, user, productCode);
        }
        //无需弹框
        JudgeAllResponse response = new JudgeAllResponse();
        response.setEquipmentPop(0);
        response.setSubType(dto.getSubType());
        return response;
    }

    /**
     * 根据活动type校验
     *
     * @param dto                    入参
     * @param znsUserEquipmentEntity 用户设备
     * @param user                   用户
     * @param productCode            型号
     */
    private JudgeAllResponse activityTypeCheckJudge(WalkJudageDto dto, ZnsUserEquipmentEntity znsUserEquipmentEntity, ZnsUserEntity user, String productCode) {
        JudgeAllResponse response = new JudgeAllResponse();
        Integer activityType = dto.getActivityType();
        if (RunActivityTypeEnum.NEW_RANKED_ACTIVITY.getType().equals(activityType)
                || RunActivityTypeEnum.PROP_ACTIVITY.getType().equals(activityType)) {
            //段位赛道具赛校验
            return rankedCheckJudge(znsUserEquipmentEntity, user, MainActivityTypeEnum.findByType(RunActivityTypeEnum.findByType(activityType).getNewActivityType()));
        }
        //其他赛事
        int subType = dto.getSubType();
        int equipmentPop = 1;
        if (RunActivityTypeEnum.FREE_RUNNING.getType().equals(activityType) || RunActivityTypeEnum.FREE_WALK.getType().equals(activityType)) {
            equipmentPop = 0;
            response.setEquipmentPop(equipmentPop);
            response.setSupportRunMode(YesNoStatus.YES.getCode());
            response.setSupportWalkMode(YesNoStatus.YES.getCode());
            return response;
        }
        List<ActivityEquipmentConfigDto> activityEquipmentConfigs = new ArrayList<>();
        Integer pkType = dto.getPkType();
        List<Integer> newActivityTypes = List.of(RunActivityTypeEnum.NEW_ACTIVITY_ENTRY_TYPER.getType(), RunActivityTypeEnum.NEW_ACTIVITY_SERIES_TYPER.getType());
        if (newActivityTypes.contains(activityType)) {
            //3.0新活动 暂时无需校验（因为活动配置都是根据活动单独校验，没有根据活动类型配置）
            log.info("WalkController#judgeAll------activityTypeCheckJudge,3.0新活动暂时无需校验,activityType={},", activityType);
            equipmentPop = 0;
        } else {
            //查询设备配置
            ZnsRunActivityConfigEntity znsRunActivityConfigEntity = null;
            if (Objects.equals(dto.getActivityType(), 2)) {
                znsRunActivityConfigEntity = znsRunActivityConfigService.selectByActivityType(dto.getActivityType(), dto.getActivityTypeSub());
            } else {
                znsRunActivityConfigEntity = znsRunActivityConfigService.selectByActivityType(dto.getActivityType(), null);
            }
            Map<String, Object> data = JsonUtil.readValue(znsRunActivityConfigEntity.getActivityConfig());
            if (data != null && data.get("activityEquipmentConfigs") != null) {
                activityEquipmentConfigs = JsonUtil.readList(data.get("activityEquipmentConfigs"), ActivityEquipmentConfigDto.class);// 设备配置
            }
            log.info("WalkController#judgeAll--------activityTypeCheckJudge，获取活动设备配置，activityType={}，配置数量={}", activityType, Optional.ofNullable(activityEquipmentConfigs).orElse(new ArrayList<>()).size());

            //校验设备
            if (Objects.equals(RunActivityTypeEnum.FREE_WALK.getType(), activityType)) {
                //自由跑-走步
                if (Objects.equals(subType, 2)) { // 跑步形态 , 1 走步形态 , 2. 跑步形态
                    subType = 1;
                } else {
                    log.info("WalkController#judgeAll------activityTypeCheckJudge,自由走步形态无需弹框,activityType={},subType={}", activityType, subType);
                    equipmentPop = 0;
                }
                if (znsUserEquipmentEntity != null) {
                    if (Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 2)) {
                        log.info("WalkController#judgeAll------activityTypeCheckJudge,设备类型是2无需弹框,activityType={}", activityType);
                        equipmentPop = 0;
                    } else if (Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 1)) {
                        log.info("WalkController#judgeAll------activityTypeCheckJudge,设备类型是1需要弹框,activityType={}", activityType);
                        equipmentPop = 1;
                    }
                }
            } else {
                if (!CollectionUtils.isEmpty(activityEquipmentConfigs)) {
                    for (ActivityEquipmentConfig activityEquipmentConfig : activityEquipmentConfigs) {
                        if (activityEquipmentConfig.getEquipmentInfo().equals(productCode)) {
                            // 如果设备类型大于0 ，并且 设备类型走跑一体机，并且他们跑步状态不相等
                            if (dto.getSubType() > 0 && Objects.equals(activityEquipmentConfig.getEquipmentType(), 3)
                                    && !Objects.equals(dto.getSubType(), activityEquipmentConfig.getSubType())) {
                                subType = activityEquipmentConfig.getSubType();
                            } else {
                                equipmentPop = 0;
                                break;
                            }
                        }
                    }
                } else {
                    equipmentPop = 0;
                }
            }
        }
        if (Objects.equals(pkType, 2)) {
            //好友对战，无需弹框
            log.info("WalkController#judgeAll------activityTypeCheckJudge,好友对战无需弹框,activityType={}", activityType);
            equipmentPop = 0;
        }
        response.setEquipmentPop(equipmentPop);
        response.setSubType(subType);
        fillSupportModel(response, activityEquipmentConfigs, productCode, false); //填充设备支持模式、弹框信息
        return response;
    }

    /**
     * 通过活动id校验设备是否可用
     *
     * @param dto                    入参
     * @param znsUserEquipmentEntity 设备
     * @param user                   用户
     * @param activityTypeDto        活动
     */
    private JudgeAllResponse activityIdCheckJudge(WalkJudageDto dto, ZnsUserEquipmentEntity znsUserEquipmentEntity, ZnsUserEntity user, ActivityTypeDto activityTypeDto, String productCode, Integer appVersion) {
        MainActivity mainActivity = activityTypeDto.getMainActivity();
        if (Objects.nonNull(mainActivity) &&
                (MainActivityTypeEnum.RANK.getType().equals(mainActivity.getMainType()) || MainActivityTypeEnum.PROP.getType().equals(mainActivity.getMainType()))) {
            //段位赛道具赛校验
            return rankedCheckJudge(znsUserEquipmentEntity, user, MainActivityTypeEnum.findByType(mainActivity.getMainType()));
        }
        if (Objects.nonNull(mainActivity) &&
                (MainActivityTypeEnum.FREE_CHALLENGE_MAIN.getType().equals(mainActivity.getMainType()) || MainActivityTypeEnum.FREE_CHALLENGE_SUB.getType().equals(mainActivity.getMainType()))) {
            //段位赛道具赛校验
            return freeChallengeJudge(znsUserEquipmentEntity, user, mainActivity.getEquipmentMainType());
        }
        if (RunActivityTypeEnum.NEW_USER_PROP.getType().equals(activityTypeDto.getActivityType())) {
            return new JudgeAllResponse().setEquipmentUpdateFlag(0);
        }
        if (MainActivityTypeEnum.PLACEMENT.getType().equals(mainActivity.getMainType())) {
            return placementActivityJudge(znsUserEquipmentEntity, user, mainActivity.getEquipmentMainType());
        }
        //其他赛事
        int equipmentPop = 1;
        Integer subType = 0;
        Integer equipmentUpdateFlag = 0; // 弹框内容，0：无需操作，1：需要升级，2：更换设备
        Integer activityVersion = null;
        Long activityId = activityTypeDto.getId();
        Integer activityTypeSub = activityTypeDto.getActivityTypeSub();
        if (Objects.equals(activityTypeDto.getActivityType(), 2) && activityTypeSub != null && activityTypeSub > 0) {
            dto.setActivityTypeSub(activityTypeSub);
        }

        //获取pk类型
        Integer pkType = getPkType(activityTypeDto);
        List<ActivityEquipmentConfigDto> activityEquipmentConfigs = null;
        if (Objects.equals(pkType, 2)) {
            //好友对战不用校验设备
            log.info("WalkController#judgeAll--------activityIdCheckJudge活动id={}，好友对战不用校验设备", activityId);
            equipmentPop = 0;
        } else {
            //其他活动
            activityEquipmentConfigs = getEquipmentConfigList(activityId, activityTypeDto.getMainType(), pkType, dto.getActivityTypeSub());  //获取活动设备配置
            log.info("WalkController#judgeAll--------activityIdCheckJudge活动id={}，配置数量={}", activityId, Optional.ofNullable(activityEquipmentConfigs).orElse(new ArrayList<>()).size());
            if (!CollectionUtils.isEmpty(activityEquipmentConfigs)) {
                //活动设备配置校验
                for (ActivityEquipmentConfig activityEquipmentConfig : activityEquipmentConfigs) {
                    if (Objects.equals(activityEquipmentConfig.getEquipmentInfo(), productCode)) {
                        // 如果设备类型大于0 ，并且 设备类型走跑一体机，并且他们跑步状态不相等
                        if (dto.getSubType() > 0 && Objects.equals(activityEquipmentConfig.getEquipmentType(), 3)
                                && !Objects.equals(dto.getSubType(), activityEquipmentConfig.getSubType())) {
                            subType = activityEquipmentConfig.getSubType();
                        } else {
                            equipmentPop = 0;
                            break;
                        }
                    }
                }
            } else {
                //没有配置，无需弹框
                equipmentPop = 0;
            }
        }

        //设备满足配置，新活动需要再校验固件版本号
        if (Objects.nonNull(mainActivity) && equipmentPop == 0) {
            activityVersion = mainActivity.getEquipmentVersion(); //活动最低版本
            Integer userVersion = znsUserEquipmentEntity.getEquipmentVersion(); //用户设备版本号
            if (DeviceConstant.EquipmentTypeEnum.TYPE_30.getCode().equals(znsUserEquipmentEntity.getEquipmentType())) {
                //客户使用的是脚踏机
                Integer deskBikeEquipmentVersion = activityTypeDto.getDeskBikeEquipmentVersion();
                activityVersion = deskBikeEquipmentVersion;
                if (VersionConstant.V4_7_1 > appVersion) {
                    throw new BizI18nException("activity.join.desk.bike.old.version");
                }
            }
            log.info("WalkController#judgeAll--------activityIdCheckJudge活动id={}，校验固件版本号,activityVersion={},userVersion={}", activityId, activityVersion, userVersion);
            if (Objects.nonNull(activityVersion)) {
                if (userVersion < activityVersion) {
                    //版本不符合
                    equipmentPop = 1; //需要弹框
                    equipmentUpdateFlag = getUpdateFlag(znsUserEquipmentEntity, user); // 弹框内容，0：无需操作，1：需要升级，2：更换设备
                }
            }
        }

        //返回参数封装
        JudgeAllResponse response = new JudgeAllResponse();
        response.setEquipmentPop(equipmentPop);
        response.setSubType(subType);
        response.setEquipmentUpdateFlag(equipmentUpdateFlag);
        response.setEquipmentVersion(activityVersion);
        fillSupportModel(response, activityEquipmentConfigs, productCode, false); //填充设备支持模式、弹框信息
        //如果是单车，需要对脚踏机，进行填充
        if (activityTypeDto.isBikeActivity()) {
            List<ActivityEquipmentVersionDto> prepare = ActivityEquipmentVersionDto.prepare(activityEquipmentConfigs, activityTypeDto);
            response.setEquipmentVersions(prepare);
            if (!CollectionUtils.isEmpty(prepare)) {
                //自行车，写入老字段
                prepare.stream().filter(item -> item.getEquipmentMainType().equals(DeviceConstant.EquipmentMainTypeEnum.BIKE.getType())).findFirst().ifPresent(item -> {
                    response.setEquipmentPopMsg(item.getEquipmentPopMsg());
                });
            }
        }
        return response;
    }

    private JudgeAllResponse placementActivityJudge(ZnsUserEquipmentEntity znsUserEquipmentEntity, ZnsUserEntity user, Integer equipmentMainType) {
        if (znsUserEquipmentEntity == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "userEquipment"));
        }
        int equipmentPop = 0; // 是否弹窗，1：弹，0：不弹
        String equipmentPopMsg = "";
        //校验是否是跑步机
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.findById(znsUserEquipmentEntity.getEquipmentId());
        if (!znsTreadmillEntity.getEquipmentMainType().equals(DeviceConstant.EquipmentMainTypeEnum.findEnumByType(equipmentMainType).getCode())) {
            //版本不符合，需要弹框
            equipmentPop = 1;
            equipmentPopMsg = I18nMsgUtils.getMessage("placement.equipment.not.match");
        }
        //构建返回内容
        JudgeAllResponse response = new JudgeAllResponse();
        response.setEquipmentPop(equipmentPop);
        response.setEquipmentPopMsg(equipmentPopMsg);
        return response;
    }

    private JudgeAllResponse freeChallengeJudge(ZnsUserEquipmentEntity znsUserEquipmentEntity, ZnsUserEntity user, Integer equipmentMainType) {
        if (znsUserEquipmentEntity == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "userEquipment"));
        }
        int equipmentPop = 0; // 是否弹窗，1：弹，0：不弹
        int equipmentUpdateFlag = 0; // 是否需要更新设备，1：需要升级，0：不处理,2:更换设备
        //校验是否是跑步机
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.findById(znsUserEquipmentEntity.getEquipmentId());
        if (!znsTreadmillEntity.getEquipmentMainType().equals(DeviceConstant.EquipmentMainTypeEnum.findEnumByType(equipmentMainType).getCode())) {
            //版本不符合，需要弹框
            equipmentPop = 1;
            equipmentUpdateFlag = 2;
            log.info("WalkController#judgeAll-------freeChallengeJudge段位赛校验设备,弹框内容：equipmentUpdateFlag:{}", equipmentUpdateFlag);
        }
        //构建返回内容
        JudgeAllResponse response = new JudgeAllResponse();
        response.setEquipmentPop(equipmentPop);
        response.setEquipmentUpdateFlag(equipmentUpdateFlag);
        return response;
    }

    /**
     * 课程跑校验设备
     *
     * @param dto                    入参
     * @param znsUserEquipmentEntity 设备
     */
    private JudgeAllResponse courseCheckJudge(WalkJudageDto dto, ZnsUserEquipmentEntity znsUserEquipmentEntity, String productCode) {
        Long courseId = dto.getCourseId();
        JudgeAllResponse response = new JudgeAllResponse();
        ZnsCourseEntity znsCourseEntity = znsCourseService.selectById(courseId);
        if (znsCourseEntity == null) {
            //课程不存在
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "courseId"));
        }
        int subType = 0;
        if (StringUtil.isEmpty(znsCourseEntity.getEquipmentType())) {
            //未限制设备类型,无需弹框
            log.info("WalkController#judgeAll------courseCheckJudge,未限制设备类型,无需弹框,courseId={}", courseId);
            response.setEquipmentPop(TreadmillConstant.EquipmentJudgeEnum.POP_0.code);
            response.setSubType(subType);
            return response;
        }

        //校验课程支持的设备类型
        String[] equipmentTypes = StringUtil.split(znsCourseEntity.getEquipmentType(), ",");  //  0：无器械，1：跑步机，2：走步机，3：瑜伽垫, 4 走跑一体机（跑步形态） , 5 走跑一体机（走步形态）
        log.info("WalkController#judgeAll------courseCheckJudge,校验课程支持的设备类型,courseId={},equipmentTypes={}", courseId, Arrays.toString(equipmentTypes));
        int equipmentPop = 1;
        boolean equipmentTypeFlag = false;
        List<ActivityEquipmentConfigDto> activityEquipmentConfigs = new ArrayList<>();
        for (String equipmentType : equipmentTypes) {
            //  0：无器械，1：跑步机，2：走步机，3：瑜伽垫, 4 走跑一体机（跑步形态） , 5 走跑一体机（走步形态）
            if ("1".equals(equipmentType) && Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 1)) {
                log.info("WalkController#judgeAll------courseCheckJudge,校验课程支持的设备类型,跑步机不用弹框,courseId={}", courseId);
                equipmentPop = 0;
                break;
            } else if ("2".equals(equipmentType) && Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 2)) {
                log.info("WalkController#judgeAll------courseCheckJudge,校验课程支持的设备类型,走步机不用弹框,courseId={}", courseId);
                equipmentPop = 0;
                break;
            } else if (Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 3)) {
                equipmentTypeFlag = true;
                int compareSubType = 0;
                if ("4".equals(equipmentType)) {
                    // 跑步形态
                    compareSubType = 2;
                    subType = 2;
                }
                if ("5".equals(equipmentType)) {
                    // 走步形态
                    compareSubType = 1;
                    subType = 1;
                }
                log.info("WalkController#judgeAll------courseCheckJudge,校验课程支持的设备类型,胸带校验，courseId={},compareSubType={},subType={}", courseId, compareSubType, subType);
                if (Objects.equals(dto.getSubType(), compareSubType)) {
                    equipmentPop = 0;
                    break;
                }
            } else if ("7".equals(equipmentType) || "6".equals(equipmentType)) {
                // 40600 版本
                if (Objects.nonNull(UserContextHolder.getAppVersion()) && UserContextHolder.getAppVersion() >= 40600 &&
                        (Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 40)
                                || (Objects.equals(znsUserEquipmentEntity.getEquipmentType(), 20)))) {
                    equipmentPop = 0;
                }
            }
            //组装活动设备配置
            ActivityEquipmentConfigDto activityEquipmentConfigDto = new ActivityEquipmentConfigDto();
            activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName(equipmentType));
            activityEquipmentConfigDto.setEquipmentInfo(equipmentType);
            activityEquipmentConfigDto.setSubType(0);
            activityEquipmentConfigDto.setEquipmentType(MapUtil.getInteger(equipmentType, -1));
            // equipmentType  4 走跑一体机（跑步形态） , 5 走跑一体机（走步形态）
            if (Objects.equals(MapUtil.getInteger(equipmentType, -1), 4)) {
                activityEquipmentConfigDto.setEquipmentType(3);
                activityEquipmentConfigDto.setEquipmentInfo("3");
                activityEquipmentConfigDto.setSubType(2);
                activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName("3") + " (Running Mode)");
            } else if (Objects.equals(MapUtil.getInteger(equipmentType, -1), 5)) {
                activityEquipmentConfigDto.setEquipmentType(3);
                activityEquipmentConfigDto.setEquipmentInfo("3");
                activityEquipmentConfigDto.setSubType(1);
                activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName("3") + " (Walking Mode)");
            }
            activityEquipmentConfigDto.setType(1);
            activityEquipmentConfigs.add(activityEquipmentConfigDto);
        }

        //封装返回数据
        response.setEquipmentPop(equipmentPop);
        response.setSubType(subType);
        fillSupportModel(response, activityEquipmentConfigs, productCode, equipmentTypeFlag); //填充设备支持模式、弹框信息
        return response;
    }

    /**
     * 填充设备支持模式、弹框信息
     *
     * @param response                 返回对象
     * @param activityEquipmentConfigs 活动设备配置
     * @param equipmentTypeFlag        是否是胸带 true：是，false：不是
     */
    private void fillSupportModel(JudgeAllResponse response, List<ActivityEquipmentConfigDto> activityEquipmentConfigs, String productCode, boolean equipmentTypeFlag) {
        Integer subType = response.getSubType();
        boolean flag = false;
        Integer supportRunMode = null;
        Integer supportWalkMode = null;
        String equipmentPopMsg = ActivityEquipmentConfigDto.equipmentConfigToPopMsg(activityEquipmentConfigs);
        if (!CollectionUtils.isEmpty(activityEquipmentConfigs)) {
            for (ActivityEquipmentConfigDto activityEquipmentConfigDto : activityEquipmentConfigs) {
                if (StringUtils.hasText(productCode) && productCode.equals(activityEquipmentConfigDto.getEquipmentInfo())) {
                    flag = true;
                }
                if (subType > 0 && Objects.equals(activityEquipmentConfigDto.getEquipmentType(), 3)) {
                    if (activityEquipmentConfigDto.getSubType() == 1) {
                        supportWalkMode = 1;
                    } else if (activityEquipmentConfigDto.getSubType() == 2) {
                        supportRunMode = 1;
                    }
                }
            }
        }
        log.info("WalkController#judgeAll------fillSupportModel,填充设备支持模式、弹框信息，subType={},flag={},equipmentTypeFlag={}", subType, flag, equipmentTypeFlag);
        if (subType > 0 && (flag || equipmentTypeFlag)) {
            equipmentPopMsg = Objects.equals(subType, 1) ? "Please adjust your device to Walkingpad mode and try again" : "Please adjust your device to treadmill mode and try again";
        }
        response.setEquipmentPopMsg(equipmentPopMsg);
        response.setSupportRunMode(supportRunMode); // 是否支持跑步形态，1：支持，0：不支持
        response.setSupportWalkMode(supportWalkMode); //否支持走步形态，1：支持，0：不支持
    }


    /**
     * 段位赛校验设备
     *
     * @param znsUserEquipmentEntity
     * @param user
     */
    private JudgeAllResponse rankedCheckJudge(ZnsUserEquipmentEntity znsUserEquipmentEntity, ZnsUserEntity user, MainActivityTypeEnum typeEnum) {
        if (znsUserEquipmentEntity == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "userEquipment"));
        }
        int equipmentPop = 0; // 是否弹窗，1：弹，0：不弹
        Integer equipmentUpdateFlag = 0; // 是否需要更新设备，1：需要升级，0：不处理,2:更换设备

        //校验段位赛道具赛最低版本号
        Integer minVersion = getRankMinOrPropVersion(typeEnum);
        Integer equipmentVersion = Optional.ofNullable(znsUserEquipmentEntity.getEquipmentVersion()).orElse(999);
        if (Objects.nonNull(minVersion) && equipmentVersion < minVersion) {
            //版本不符合，需要弹框
            equipmentPop = 1;
            equipmentUpdateFlag = getUpdateFlag(znsUserEquipmentEntity, user);
            log.info("WalkController#judgeAll-------rankedCheck段位赛校验设备,配置最低版本号：{},设备版本号：{},弹框内容：{}", minVersion, equipmentVersion, equipmentUpdateFlag);
        }

        //构建返回内容
        JudgeAllResponse response = new JudgeAllResponse();
        response.setEquipmentPop(equipmentPop);
        response.setEquipmentUpdateFlag(equipmentUpdateFlag);
        return response;
    }

    /**
     * 获取段位赛道具赛设备最低版本号
     *
     * @return
     */
    private Integer getRankMinOrPropVersion(MainActivityTypeEnum typeEnum) {
        ZnsRunActivityConfigEntity configEntity = znsRunActivityConfigService.getByType(typeEnum.getOldType(), null);
        if (configEntity == null || !StringUtils.hasText(configEntity.getActivityConfig())) {
            log.info("WalkController#judgeAll-------getRankMinVersion获取段位赛设备最低版本号,段位赛配置为空");
            return null;
        }
        RankedActivityConfigDto configDto = JsonUtil.readValue(configEntity.getActivityConfig(), RankedActivityConfigDto.class);
        if (configDto == null) {
            log.info("WalkController#judgeAll-------getRankMinVersion获取段位赛设备最低版本号,段位赛配置转换异常");
            return null;
        }
        Integer deviceMinVersion = configDto.getDeviceMinVersion();
        log.info("WalkController#judgeAll-------getRankMinVersion获取段位赛设备最低版本号：" + deviceMinVersion);
        return deviceMinVersion;
    }

    /**
     * 获取升级标记
     *
     * @param znsUserEquipmentEntity
     * @return 1：需要升级，0：不处理,2:更换设备
     */
    private Integer getUpdateFlag(ZnsUserEquipmentEntity znsUserEquipmentEntity, ZnsUserEntity user) {
        EquipmentVersionRequest equipmentVersionRequest = new EquipmentVersionRequest();
        equipmentVersionRequest.setNeedPush(YesNoStatus.NO.getCode());
        equipmentVersionRequest.setCurrentVersion(znsUserEquipmentEntity.getEquipmentVersion());
        equipmentVersionRequest.setEquipmentNo(znsUserEquipmentEntity.getEquipmentNo());
        equipmentVersionRequest.setUserId(znsUserEquipmentEntity.getUserId());
        equipmentVersionRequest.setNeedSelectStatus(YesNoStatus.NO.getCode());
        DeviceVersionResp deviceVersionResp = equipmentManager.checkTreadmillVersion(equipmentVersionRequest, user);
        if (deviceVersionResp.getVersion() != null) {
            if (deviceVersionResp.getVersion() > znsUserEquipmentEntity.getEquipmentVersion()) {
                //需要升级固件
                return 1;
            } else {
                // 不可升级的场景 当前跑步机版本过低，不支持升级，请更换设备。
                return 2;
            }
        }
        return 0;
    }

}
