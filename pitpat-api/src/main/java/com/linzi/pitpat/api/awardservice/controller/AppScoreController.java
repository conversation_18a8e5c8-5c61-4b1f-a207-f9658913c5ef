package com.linzi.pitpat.api.awardservice.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.awardservice.manager.UserScoreBusiness;
import com.linzi.pitpat.api.dto.request.ProductCategoryRequest;
import com.linzi.pitpat.api.dto.response.ScoreProductCategoryResp;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.AppType;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.*;
import com.linzi.pitpat.core.util.dto.West8StartEndDto;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityPayRequest;
import com.linzi.pitpat.data.awardservice.constant.enums.ExchangeScoreTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConfigTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.dto.api.ScoreListByMonthDto;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.manager.api.ScoreManager;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityUserScoreDto;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreIdDto;
import com.linzi.pitpat.data.awardservice.model.dto.ScoreListDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreAward;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRuleList;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreConfig;
import com.linzi.pitpat.data.awardservice.model.entry.ScoreExchangeAddress;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.query.ExchangeScoreRuleQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreResp;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreRuleResp;
import com.linzi.pitpat.data.awardservice.model.resp.ExchangeScoreTypeResp;
import com.linzi.pitpat.data.awardservice.model.resp.ScoreConfigResp;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreAwardService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleCurrencyService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleI18nService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleListService;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.ScoreConfigService;
import com.linzi.pitpat.data.awardservice.service.ScoreExchangeAddressService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.po.WalkMonthDto;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.dto.request.OrderTaxReqDto;
import com.linzi.pitpat.data.mallservice.dto.response.OrderAmountRespDto;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.model.entity.ScoreMallGoodsRelationDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderItemQuery;
import com.linzi.pitpat.data.mallservice.model.query.ScoreMallGoodsRelationQuery;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.enums.SystemConstant;
import com.linzi.pitpat.data.systemservice.model.entity.AppUpgrade;
import com.linzi.pitpat.data.systemservice.model.query.AppUpgradeQuery;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.third.erp.ErpApiUtil;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddressEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserExtraQuery;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddressService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * app 我的积分
 */
@RestController
@RequestMapping("/app/my/score")
@Slf4j
@RequiredArgsConstructor
public class AppScoreController extends BaseAppController {

    private final ScoreConfigService scoreConfigService;
    private final ActivityUserScoreService activityUserScoreService;
    private final ISysConfigService sysConfigService;
    private final ExchangeScoreRuleCurrencyService exchangeScoreRuleCurrencyService;
    private final CouponService couponService;
    private final ZnsUserService znsUserService;
    private final ExchangeScoreRuleListService exchangeScoreRuleListService;
    private final ExchangeScoreAwardService exchangeScoreAwardService;
    private final ZnsUserAccountService userAccountService;
    private final AppUpgradeService appUpgradeService;
    private final RedissonClient redissonClient;
    private final RedisTemplate redisTemplate;
    private final ExchangeScoreRuleService exchangeScoreRuleService;
    private final ExchangeScoreRuleI18nService exchangeScoreRuleI18nService;
    private final ScoreManager scoreManager;
    private final UserLevelService userLevelService;
    private final UserExtraService userExtraService;
    private final ScoreMallGoodsRelationService scoreMallGoodsRelationService;
    private final UserScoreBusiness userScoreBusiness;

    /**
     * 积分列表获取首页
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public Result<ExchangeScoreResp> list(@RequestBody ScoreListDto dto) {
        West8StartEndDto west8StartEndDto = NumberUtils.getWest8StartEndTime();
        List<ScoreConfig> scoreConfigList = scoreConfigService.list(new QueryWrapper<>());
        List<ScoreConfigResp> scoreConfigResps = new ArrayList<>();
        for (ScoreConfig scoreConfig : scoreConfigList) {
            ScoreConfigResp scoreConfigResp = scoreConfigService.selectScoreConfigByUserId(scoreConfig.getId(),
                    scoreConfig.getCalStyle(), dto.getUserId(), west8StartEndDto.getStartTime(), west8StartEndDto.getEndTime());
            //一生只能领取一次的完成后不显示
            if ("life".equals(scoreConfig.getCalStyle()) && scoreConfigResp.getNum() > 0 && scoreConfigResp.getStatus() == 0) {
                ActivityUserScore activityUserScore = activityUserScoreService.selectExchangePersonCountByScoreConfigIdUserId(scoreConfig.getId(), dto.getUserId());
                if (activityUserScore.getAwardTime() != null && west8StartEndDto.getStartTime().toInstant().toEpochMilli() > activityUserScore.getAwardTime().toInstant().toEpochMilli()) {
                    continue;
                }
            }
            // 标题 i18n
            String title = I18nMsgUtils.getMessage("scoreExchange.config.ScoreConfigTypeEnum." + ScoreConfigTypeEnum.resolve(scoreConfigResp.getEnTitle()));
            if (!StringUtils.hasText(title)) {
                log.warn("积分列表I18n未找到词条={}", title);
            }
            scoreConfigResp.setEnTitle(title);
            scoreConfigResps.add(scoreConfigResp);
        }

        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        PPageUtils<ExchangeScoreRuleResp> pageData = getExchangeScoreRuleRespPPageData(pageNum, pageSize, 0, null);

        ExchangeScoreResp exchangeScoreResp = new ExchangeScoreResp();
        exchangeScoreResp.setScoreConfigResps(scoreConfigResps);
        exchangeScoreResp.setExchangeScoreRulePageUtils(pageData);
        String runScoreRule = sysConfigService.getMapValByKey(ConfigKeyEnums.version_app_2_0_5.getCode(), SystemConstant.RUN_SCORE_RULE_NEW + "_" + getLanguageCode(), SystemConstant.RUN_SCORE_RULE_NEW + "_" + I18nConstant.LanguageCodeEnum.en_US.getCode());
        exchangeScoreResp.setRunScoreRule(runScoreRule);
        //下个月过期
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(ZonedDateTime.now(), TimeZone.getTimeZone("UTC-8"));
        ZonedDateTime addMonths = DateUtil.addMonths(now, 1);
        int monthExpireScore = activityUserScoreService.selectActivityUserScoreByUserIdMonthExpire(dto.getUserId(), DateUtil.getFirstOfMonth(addMonths),
                DateUtil.getEndOfMonth(addMonths), Arrays.asList(1, 3));
        exchangeScoreResp.setMonthExpireScore(monthExpireScore);

        exchangeScoreResp.setAllScore(activityUserScoreService.getAllUserScore(dto.getUserId()));
        return CommonResult.success(exchangeScoreResp);
    }


    /**
     * 通过id 查找
     *
     * @param dto
     * @return
     */
    @PostMapping("/getById")
    public Result<ExchangeScoreRuleResp> getById(@RequestBody ScoreIdDto dto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.lotLogin"));
        }
        UserExtraDo userExtraDo = userExtraService.findByQuery(new UserExtraQuery().setUserId(loginUser.getId()));
        Long userId = loginUser.getId();
        ExchangeScoreRuleResp exchangeScoreRuleResp = new ExchangeScoreRuleResp();
        ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.getById(dto.getId());
        BeanUtils.copyProperties(exchangeScoreRule, exchangeScoreRuleResp);
        setExpiryTime(exchangeScoreRuleResp);

        //币种
        Currency currency = getUserCurrency();
        exchangeScoreRuleResp.setCurrency(currency);
        if (!I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currency.getCurrencyCode())) {
            //不是美元需要获取币种价格
            ExchangeScoreRuleCurrencyEntity scoreRuleCurrency = exchangeScoreRuleCurrencyService.findByRuleIdAndCurrencyCode(exchangeScoreRule.getId(), currency.getCurrencyCode());
            if (scoreRuleCurrency != null) {
                exchangeScoreRuleResp.setExchangeAmount(scoreRuleCurrency.getExchangeAmount());
                exchangeScoreRuleResp.setOriginalExchangeAmount(scoreRuleCurrency.getOriginalExchangeAmount());
            }
        }

        ExchangeScoreRuleList exchangeScoreRuleList = exchangeScoreRuleListService.selectExchangeScoreRuleListByExchangeScoreRuleIdLimit1(exchangeScoreRuleResp.getId());
        if (Objects.nonNull(exchangeScoreRuleList)) {
            exchangeScoreRuleResp.setExchangeRuleImage(exchangeScoreRuleList.getImageUrl());
        }
        // 查看当前积分总数
        Integer allUserScore = activityUserScoreService.getAllUserScore(userId);
        String userDayExchangeKey = String.format(RedisKeyConstant.USER_DAY_PRODUCT_EXCHANGE, loginUser.getId(), exchangeScoreRule.getId());
        Object userDayExchangeNum = redisTemplate.opsForValue().get(userDayExchangeKey);
        Integer exchangePersonDayLimit = exchangeScoreRule.getExchangePersonDayLimit();
        exchangeScoreRuleResp.setRedeemable(false);
        if (allUserScore >= exchangeScoreRuleResp.getExchangeScore()) {
            if (Objects.isNull(userDayExchangeNum)
                    || exchangePersonDayLimit == -1
                    || Integer.parseInt(userDayExchangeNum.toString()) < exchangePersonDayLimit) {
                // 今日未兑换，每日兑换次数不限，当日兑换次数小于每日兑换上限
                exchangeScoreRuleResp.setRedeemable(true);
            }
        }
        //兑换用户等级限制
        UserLevel userLevel = userLevelService.findByUserId(userId);
        if (userLevel.getLevel() < exchangeScoreRule.getUserLevelLimit()) {
            exchangeScoreRuleResp.setRedeemable(false);
        }

        //会员商品非会员不可兑换
        if (exchangeScoreRuleResp.getBelongTo() == 2 && !Objects.equals(loginUser.getMemberType(), 1)) {
            exchangeScoreRuleResp.setRedeemable(false);
        }
        if (allUserScore < exchangeScoreRuleResp.getExchangeScore()) {
            // 积分不足
            exchangeScoreRuleResp.setPointsEnough(0);
        } else {
            exchangeScoreRuleResp.setPointsEnough(1);
        }

        // 国际化数据
        String langCode = I18nMsgUtils.getLangCode();
        ExchangeScoreRuleI18n i18n = exchangeScoreRuleI18nService.selectOneByQuery(ExchangeScoreRuleQuery.builder().ruleId(dto.getId()).langCode(langCode).defaultLangCode(exchangeScoreRule.getDefaultLangCode()).build());
        if (Objects.nonNull(i18n)) {
            exchangeScoreRuleResp.setExchangeRule(i18n.getExchangeRule());
            exchangeScoreRuleResp.setActivityName(i18n.getActivityName());
            exchangeScoreRuleResp.setAdvertiseImage(i18n.getAdvertiseImage());
            exchangeScoreRuleResp.setTextContent(i18n.getTextContent());
        }
        ExchangeScoreAward scoreAward = exchangeScoreAwardService.findByRuleId(dto.getId());
        exchangeScoreRuleResp.setExchangeType(Objects.nonNull(scoreAward) ? scoreAward.getExchangeType() : null);
        //商品ID
        if (Objects.nonNull(userExtraDo)) {
            String mallCountryCode = userExtraDo.getMallCountryCode();
            ScoreMallGoodsRelationDo relationDo = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setCountryCode(mallCountryCode).setRuleId(dto.getId()));
            exchangeScoreRuleResp.setGoodsId(Objects.nonNull(relationDo) ? relationDo.getGoodsId() : null);
        }
        return CommonResult.success(exchangeScoreRuleResp);
    }

    /**
     * 积分兑换接口
     *
     * @param dto
     * @return
     */
    //TODO 单元测试
    @PostMapping("/couponExchange")
    public Result<ExchangeScoreTypeResp> couponExchange(@RequestBody ScoreIdDto dto) {
        String lockKey = RedisConstants.EXCHANGE_SCORE_RULE + dto.getId();
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isLocked()) {
            // 后台是否在编辑
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        // 用户兑换加锁
        String productKeyLock = String.format(RedisKeyConstant.PRODUCT_EXCHANGE, dto.getId());
        RLock productLock = redissonClient.getLock(productKeyLock);
        try {
            if (!productLock.tryLock(5L, TimeUnit.SECONDS)) {
                throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"), CommonError.SYSTEM_ERROR.getCode());
            }
            Currency userCurrency = getUserCurrency();
            ExchangeScoreTypeResp exchangeScoreTypeResp = userScoreBusiness.scoreExchange(dto, userCurrency);
            return CommonResult.success(exchangeScoreTypeResp);
        } catch (InterruptedException e) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"), CommonError.SYSTEM_ERROR.getCode());
        } finally {
            if (productLock.isHeldByCurrentThread()) {
                productLock.unlock();
            }
        }
    }

    private ZonedDateTime getAddHours(Integer expiredTime) {
        ZonedDateTime date = ZonedDateTime.now();
        if (date.getMinutes() > 0) {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(DateUtil.addHours(ZonedDateTime.now(), 1), 0), 0), expiredTime * 24);
        } else {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(ZonedDateTime.now(), 0), 0), expiredTime * 24);
        }
    }

    /**
     * 发送积分,设备连接 和 签到领积分需要调用这个接口
     *
     * @return
     */
    @PostMapping("/sendScore")
    public Result<ScoreConfig> sendScore(@RequestBody ScoreIdDto dto) {
        ScoreConfig scoreConfig = scoreConfigService.selectScoreConfigById(dto.getId());
        return activityUserScoreService.sendScore(dto.getUserId(), scoreConfig.getType(), null, null, null);
    }

    /**
     * 领取积分
     *
     * @param dto
     * @return
     */
    @PostMapping("/awardScore")
    public Result<ActivityUserScore> awardScore(@RequestBody ScoreIdDto dto) {
        ZonedDateTime expireTime = DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8"));
        ActivityUserScore activityUserScoreDto = new ActivityUserScore();
        West8StartEndDto west8StartEndDto = NumberUtils.getWest8StartEndTime();

        ZonedDateTime startTime = west8StartEndDto.getStartTime();
        ScoreConfig scoreConfig = scoreConfigService.selectScoreConfigById(dto.getId());
        if ("life".equals(scoreConfig.getCalStyle())) {
            startTime = null;
        }
        // 标识为已经领取
        List<ActivityUserScore> activityUserScores = activityUserScoreService.selectActivityUserScoreByScoreConfigIdUserId(
                startTime, west8StartEndDto.getEndTime(), dto.getId(), dto.getUserId(), 0);

        Integer sum = 0;
        for (ActivityUserScore activityUserScore : activityUserScores) {
            activityUserScoreService.updateScoreStatusExpireTime(ZonedDateTime.now(), expireTime, ZonedDateTime.now(), 1, activityUserScore.getId());
            sum = sum + activityUserScore.getScore();
            break;
        }
        activityUserScoreDto.setScore(sum);
        return CommonResult.success(activityUserScoreDto);
    }

    /**
     * 通过月份查找用户积分数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/listByMonth")
    public Result<ScoreListByMonthDto> scoreList(@RequestBody WalkMonthDto dto) {
        ZnsUserEntity loginUser = getLoginUser();
        ScoreListByMonthDto scoreList = new ScoreListByMonthDto();
        String oldQueryMonth = dto.getQueryMonth();
        //查询月份 + 1个月
        if (StringUtils.hasText(dto.getQueryMonth()) && dto.getQueryMonth().contains("-")) {
            String as[] = dto.getQueryMonth().split("-");
            String queryMonth = as[0];
            int c = MapUtil.getInteger(as[1]) + 1;
            if (c > 12) {
                c = 1;
                queryMonth = String.valueOf(Integer.parseInt(queryMonth) + 1);
            }
            if ((c + "").length() == 1) {
                queryMonth = queryMonth + "-" + "0" + c;
            } else {
                queryMonth = queryMonth + "-" + c;
            }
            dto.setQueryMonth(queryMonth);
        }

        dto.setQueryMonth(DateUtil.parseFormatDate(dto.getQueryMonth(), DateUtil.YYYY_MM_DD_HH_MM_SS));

        PPageUtils pageUtils = PPageUtils.startPage(dto.getPageNum(), dto.getPageSize())
                .doSelect(new ISelect() {
                    @Override
                    public List doSelect(IPage page) {
                        return activityUserScoreService.selectActivityUserScoreByUserIdMonthApp(page, dto.getUserId(),
                                dto.getQueryMonth(), dto.getIncome(), dto.getSource(), 0
                        );
                    }
                });
        HashMap<String, Integer> hashSets = new LinkedHashMap();
        List<ActivityUserScoreDto> walkMonthResps = new ArrayList<>();
        List<ActivityUserScoreDto> list = pageUtils.getRows();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月");
        List<ActivityUserScoreDto> couponList = list.stream().filter(item -> Objects.nonNull(item.getCouponId())).collect(Collectors.toList());
        Map<Long, Coupon> couponScoreMap = couponService.selectCouponScoreDto(couponList);

        // 取最早一条记录的月份
        ZonedDateTime lastTime = ZonedDateTime.now();
        ActivityUserScore firstActivityUserScore = activityUserScoreService.selectFirstActivityUserScore(dto.getUserId(), dto.getIncome(), dto.getSource(), 0);
        if (Objects.nonNull(firstActivityUserScore)) {
            lastTime = firstActivityUserScore.getGmtCreate();
        }

        for (ActivityUserScoreDto activityUserScoreDto : list) {
            activityUserScoreDto.setScoreTitle(scoreManager.getScoreTitle(activityUserScoreDto.getSource(), activityUserScoreDto.getActivityId(), getUserId(),
                    activityUserScoreDto.getId(), activityUserScoreDto.getIncome()));

            String yearMonth = simpleDateFormat.format(activityUserScoreDto.getGmtCreate());
            if (activityUserScoreDto.getExchangeScoreRuleId() != null && activityUserScoreDto.getExchangeScoreRuleId() > 0) {
                ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.getById(activityUserScoreDto.getExchangeScoreRuleId());
                //TODO i18n 积分商城
                activityUserScoreDto.setTitle(exchangeScoreRule.getActivityName());
                activityUserScoreDto.setMinPicUrl(exchangeScoreRule.getAdvertiseImage());
                ExchangeScoreRuleI18n i18n = exchangeScoreRuleI18nService.selectOneByQuery(ExchangeScoreRuleQuery.builder().ruleId(activityUserScoreDto.getExchangeScoreRuleId()).langCode(loginUser.getLanguageCode()).defaultLangCode(exchangeScoreRule.getDefaultLangCode()).build());
                if (Objects.nonNull(i18n)) {
                    activityUserScoreDto.setTitle(i18n.getActivityName());
                    activityUserScoreDto.setMinPicUrl(i18n.getAdvertiseImage());
                }
            } else {
                activityUserScoreDto.setMinPicUrl(activityUserScoreDto.getMinPicUrl());
            }
            Integer walkMonthResp = hashSets.get(yearMonth);
            if (walkMonthResp == null) {
                // 获取积分
                int acquireScore = activityUserScoreService.selectActivityUserScoreByUserIdMonth(dto.getUserId(),
                        DateUtil.getFirstOfMonth(activityUserScoreDto.getGmtCreate()),
                        DateUtil.getEndOfDate(activityUserScoreDto.getGmtCreate()), 1, 0);
                // 消费积分
                int consumeScore = activityUserScoreService.selectActivityUserScoreByUserIdMonth(dto.getUserId(),
                        DateUtil.getFirstOfMonth(activityUserScoreDto.getGmtCreate()),
                        DateUtil.getEndOfDate(activityUserScoreDto.getGmtCreate()), -1, 0);

                ActivityUserScoreDto monthActivityUserScore = new ActivityUserScoreDto();
                monthActivityUserScore.setMonthStr(yearMonth);
                monthActivityUserScore.setAcquireScore(acquireScore);
                monthActivityUserScore.setConsumeScore(consumeScore);
                monthActivityUserScore.setDataType(1);
                monthActivityUserScore.setGmtCreate(DateUtil.parseStr2Date(yearMonth, "yyyy年MM月"));
                walkMonthResps.add(monthActivityUserScore);
                hashSets.put(yearMonth, 1);
            }

            if (Objects.nonNull(activityUserScoreDto.getCouponId())) {
                Coupon coupon = couponScoreMap.get(activityUserScoreDto.getCouponId());
                activityUserScoreDto.setCouponType(coupon.getCouponType());
                activityUserScoreDto.setCouponAmount(coupon.getAmount());
                activityUserScoreDto.setPicture(coupon.getPicture());
            }


            activityUserScoreDto.setDataType(2);
            activityUserScoreDto.setCurrency(userAccountService.getUserCurrency(loginUser.getId()));
            walkMonthResps.add(activityUserScoreDto);
        }

        scoreList.setLastDateTime(lastTime);
        pageUtils.setRows(walkMonthResps);
        scoreList.setPage(pageUtils);
        scoreList.setAllScore(activityUserScoreService.getAllUserScore(dto.getUserId()));
        String runScoreRule = sysConfigService.getMapValByKey(ConfigKeyEnums.version_app_2_0_5.getCode(), SystemConstant.RUN_SCORE_RULE_NEW + "_" + getLanguageCode(), SystemConstant.RUN_SCORE_RULE_NEW + "_" + I18nConstant.LanguageCodeEnum.en_US.getCode());
        scoreList.setRunScoreRule(runScoreRule);
        return CommonResult.success(scoreList);
    }


    /**
     * 兑换商品类别查询
     *
     * @return
     */
    @PostMapping("/listProductCategory")
    public Result<List<ScoreProductCategoryResp>> listProductCategory() {
        ScoreProductCategoryResp resp1 = ScoreProductCategoryResp.builder()
                .categoryCode(ExchangeScoreTypeEnum.COUPON.getCode())
                .categoryDesc(I18nMsgUtils.getMessage("scoreExchange.type.ExchangeScoreTypeEnum.COUPON")).build();
        ScoreProductCategoryResp resp2 = ScoreProductCategoryResp.builder()
                .categoryCode(ExchangeScoreTypeEnum.CLOTHES.getCode())
                .categoryDesc(I18nMsgUtils.getMessage("scoreExchange.type.ExchangeScoreTypeEnum.CLOTHES")).build();
        ScoreProductCategoryResp resp3 = ScoreProductCategoryResp.builder()
                .categoryCode(ExchangeScoreTypeEnum.PHYSICAL_REWARDS.getCode())
                .categoryDesc(I18nMsgUtils.getMessage("scoreExchange.type.ExchangeScoreTypeEnum.PHYSICAL_REWARDS")).build();
        ScoreProductCategoryResp resp4 = ScoreProductCategoryResp.builder()
                .categoryCode(ExchangeScoreTypeEnum.ELSE.getCode())
                .categoryDesc(I18nMsgUtils.getMessage("scoreExchange.type.ExchangeScoreTypeEnum.ELSE")).build();
        List<ScoreProductCategoryResp> list;
        list = Lists.newArrayList(resp1, resp2, resp3, resp4);
        return CommonResult.success(list);
    }

    /**
     * 兑换商品分类查询
     *
     * @return
     */
    @PostMapping("/listProduct")
    public Result<PPageUtils<ExchangeScoreRuleResp>> listProductByCategory(@RequestBody ProductCategoryRequest request) {
        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();
        Integer categoryCode = request.getCategoryCode();
        PPageUtils<ExchangeScoreRuleResp> pageData = getExchangeScoreRuleRespPPageData(pageNum, pageSize, categoryCode, request.getBelongTo());
        return CommonResult.success(pageData);
    }

    private PPageUtils<ExchangeScoreRuleResp> getExchangeScoreRuleRespPPageData(Integer pageNum, Integer pageSize, Integer categoryCode, Integer belongTo) {
        Currency currency = getUserCurrency();
        PPageUtils<ExchangeScoreRuleResp> pageUtils = PPageUtils.startPage(pageNum, pageSize)
                .doSelect(page -> {
                    // ios审核状态下不展示金额不为0的数据
                    Integer appVersion = getAppVersion();
                    Integer inReview = 0; // 是否审核中，0：否；1：是
                    AppUpgradeQuery appUpgradeQuery = AppUpgradeQuery.builder().versionCode(appVersion).appType(AppType.IOS.getName()).status(2).build();
                    AppUpgrade appInfo = appUpgradeService.findByQuery(appUpgradeQuery);
                    if (Objects.nonNull(appInfo)) {
                        // 标识
                        inReview = 1;
                    }
                    return exchangeScoreRuleService.selectAppNewPageList(page, inReview, categoryCode, currency.getCurrencyCode(), belongTo, appVersion);
                });

        //设置币种、币种金额
        for (ExchangeScoreRuleResp row : pageUtils.getRows()) {
            row.setCurrency(currency);
            row.setExchangeAmount(row.getCurrencyAmount());
            setExpiryTime(row);
            // 国际化数据
            String langCode = I18nMsgUtils.getLangCode();
            String defaultLangCode = row.getDefaultLangCode();
            ExchangeScoreRuleI18n i18n = exchangeScoreRuleI18nService.selectOneByQuery(ExchangeScoreRuleQuery.builder().ruleId(row.getId()).langCode(langCode).defaultLangCode(defaultLangCode).build());
            if (Objects.nonNull(i18n)) {
                row.setActivityName(i18n.getActivityName());
                row.setExchangeRule(i18n.getExchangeRule());
                row.setAdvertiseImage(i18n.getAdvertiseImage());
            }

        }
        return pageUtils;
    }

    private void setExpiryTime(ExchangeScoreRuleResp row) {
        ExchangeScoreAward scoreAward = exchangeScoreAwardService.findByRuleId(row.getId());
        if (Objects.isNull(scoreAward)) {
            return;
        }
        //过期时间展示
        ExchangeScoreTypeEnum typeEnum = ExchangeScoreTypeEnum.findByType(scoreAward.getExchangeType());
        switch (typeEnum) {
            case COUPON:
                // 优惠券
                Long couponId = scoreAward.getExchangeId();
                Coupon coupon = couponService.selectCouponById(couponId);
                if (Objects.isNull(coupon)) {
                    log.info("查询不到绑定的优惠券信息");
                    return;
                }
                row.setExpiryType(coupon.getExpiryType());
                row.setValidDays(coupon.getValidDays());
                row.setGmtStart(coupon.getGmtStart());
                row.setGmtEnd(coupon.getGmtEnd());
                break;
            case CLOTHES:
                // 服装
                row.setExpiryType(1);
                row.setValidDays(scoreAward.getExpiredTime());
                break;
            case MUSIC:
                // 音乐
                break;
            case PROPS:
                // 道具
                break;
            case PHYSICAL_REWARDS:
                // 实物奖励
                row.setExpiryType(1);
                row.setValidDays(-1);
                break;
            default:
                break;
        }
    }


    public static void main(String[] args) {
//        String a = "2023-12";
//        String as [] = a.split("-");
//        String queryMonth =as[0];
//        int c = NumberUtils.objToInteger(as[1]) + 1 ;
//        if(c > 12 ){
//            c = 1 ;
//        }
//        if((c+"").length()==1){
//            queryMonth = queryMonth + "-" +"0"+ c  ;
//        }else{
//            queryMonth = queryMonth + "-" +c ;
//        }
//        System.out.println(queryMonth);
        System.out.println(ZonedDateTime.now());
        TimeZone timeZone = TimeZone.getTimeZone("utc-8");
        ZonedDateTime date2ByTimeZone = DateUtil.getDate2ByTimeZone(DateUtil.startOfDate(ZonedDateTime.now()), timeZone);
        System.out.println(date2ByTimeZone);
    }
}
