package com.linzi.pitpat.api.activityservice.dto.response;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.awardservice.model.dto.MilestoneAwardListDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/8 11:30
 */
@Data
@Accessors(chain = true)
public class BattlePassMilestoneResponseDto {
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动开始时间
     */
    private ZonedDateTime activityStartTime;
    /**
     * 活动结束时间
     */
    private ZonedDateTime activityEndTime;

    /**
     * 里程碑app端展示时间：MM/dd - MM/dd
     */
    private String milestoneTime;
    /**
     * 运动里程（m）
     */
    private Integer runMileage;
    /**
     * 运动时长（s）
     */
    private Integer runTime;
    /**
     * 待上传数据数量
     */
    private Integer toUploadCount;
    /**
     * 奖励明细
     */
    private List<MilestoneAwardListDto> awardConfigDetailsDtoList;

    private Currency currency;

    /**
     * 活动参赛费用/进阶里程碑费用(原价)
     */
    private BigDecimal activityEntryFee;
    /**
     * 是否有历史未领取奖励
     */
    private Integer hashHistoryAward;
    /**
     * 历史活动id
     */
    private Long historyActivityId;
    /**
     * 是否有待领取奖励，1：是，0：否
     */
    private Integer hashUnclaimedAward;
    /**
     * 里程碑手册用户类型，0：普通，1：进阶用户
     */
    private Integer battlePassMilestoneUserType;
    /**
     * 购买价（折扣后的价格）
     */
    private BigDecimal discountFee;

    /**
     * 新用户进阶里程碑折扣
     */
    private BigDecimal discount;

    //0:首月折扣，1：次月折扣，2：第三月折扣，当有折扣时（discountType = 1 ）。
    private Integer discountPayType;

    /**
     * 默认的抵扣券id
     */
    private Long defaultCouponId;
    /**
     * 默认抵扣券的用户券表id
     */
    private Long defaultUserCouponId;

    //0:无优惠，1：有折扣，2：优惠券
    private Integer discountType;

    public BattlePassMilestoneResponseDto() {
        this.runMileage = 0;
        this.runTime = 0;
        this.toUploadCount = 0;
        this.hashHistoryAward = 0;
    }

    public BattlePassMilestoneResponseDto(Long activityId, ZonedDateTime activityStartTime, ZonedDateTime activityEndTime, Integer runMileage, Integer runTime, Integer toUploadCount, List<MilestoneAwardListDto> awardConfigDetailsDtoList, BigDecimal activityEntryFee, Integer hashHistoryAward, Integer battlePassMilestoneUserType) {
        this.activityId = activityId;
        this.activityStartTime = activityStartTime;
        this.activityEndTime = activityEndTime;
        this.runMileage = runMileage;
        this.runTime = runTime;
        this.toUploadCount = toUploadCount;
        this.awardConfigDetailsDtoList = awardConfigDetailsDtoList;
        this.activityEntryFee = activityEntryFee;
        this.hashHistoryAward = hashHistoryAward;
        this.battlePassMilestoneUserType = battlePassMilestoneUserType;
    }
}
