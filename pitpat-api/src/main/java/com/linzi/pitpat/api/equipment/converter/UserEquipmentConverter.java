package com.linzi.pitpat.api.equipment.converter;

import com.linzi.pitpat.api.userservice.dto.response.api.DeviceSimpleResponseDto;
import com.linzi.pitpat.api.userservice.model.vo.VirtualDeviceVo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.dto.UserEquipmentDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserEquipmentConverter {

    List<UserEquipmentDto> toDtoList(List<ZnsUserEquipmentEntity> znsUserEquipmentEntities);

    List<ZnsUserEquipmentEntity> toEntityList(List<UserEquipmentDto> dtos);

    @Mapping(target = "userEquipmentId", source = "id")
    @Mapping(target = "equipmentAddress", source = "equipmentAddress")
    @Mapping(target = "connectTime", source = "connectTime", qualifiedByName = "dateToZonedDateTime")
    @Mapping(target = "appearanceDrawing", source = "equipmentImage")
    VirtualDeviceVo toVirtualDeviceDto(ZnsUserEquipmentEntity userEquipmentEntity);

    @Named("dateToZonedDateTime")
    default ZonedDateTime dateToZonedDateTime(ZonedDateTime date) {
        return date != null ? ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()) : null;
    }

    DeviceSimpleResponseDto toSimpleRespDto(ZnsUserEquipmentEntity equipment);
}
