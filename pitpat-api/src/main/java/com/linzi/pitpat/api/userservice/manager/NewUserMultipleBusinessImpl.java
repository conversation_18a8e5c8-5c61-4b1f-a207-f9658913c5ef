package com.linzi.pitpat.api.userservice.manager;

import com.linzi.pitpat.api.activityservice.dto.request.NewUserMultipleMatchReq;
import com.linzi.pitpat.api.activityservice.dto.response.NewUserMatchMultipleResp;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.biz.NewPersonPkBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityCreateSourceEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPlaylistRel;
import com.linzi.pitpat.data.activityservice.model.entity.NewTeamDetailRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.request.RunActivityRequest;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkConfigVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPersonPkVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.service.ActivityPlaylistRelService;
import com.linzi.pitpat.data.activityservice.service.NewPersonPkConfigService;
import com.linzi.pitpat.data.activityservice.service.NewTeamDetailRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.enums.ActivityMusicSupportEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.resp.UserGameRankAwardDto;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.lang.Result;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class NewUserMultipleBusinessImpl implements NewUserMultipleBusiness {
    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private NewPersonPkConfigService newPersonPkConfigService;
    @Resource
    private NewPersonPkBizService newPersonPkBizService;

    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;

    @Resource
    private ZnsUserService znsUserService;

    @Resource
    private ActivityStrategyContext activityStrategyContext;

    @Resource
    private ZnsRunActivityService znsRunActivityService;

    @Resource
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    @Resource
    private ZnsUserAccountService znsUserAccountService;

    @Override
    public NewPersonPkConfigVo getNewUserMultipleConfig() {
        var sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.new_person_pk_config.getCode());
        if (Objects.isNull(sysConfig)) {
            throw new BaseException("new_person_pk_config 系统配置缺失");
        }
        var newPersonPkConfigVo = new NewPersonPkConfigVo();
        String configValue = sysConfig.getConfigValue();
        newPersonPkConfigVo.setType(Integer.valueOf(configValue));
        return newPersonPkConfigVo;
    }

    @Override
    public NewPkMultipleConfigVo getNewUserMultipleAward(ZnsUserEntity user) {
        var newPkMultipleConfigVo = new NewPkMultipleConfigVo();
        var sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.new_person_pk_config.getCode());
        if (Objects.isNull(sysConfig)) {
            throw new BaseException("new_person_pk_config 系统配置缺失");
        }
        String configValue = sysConfig.getConfigValue();
        String config = "";
        ZnsRunActivityConfigEntity activityConfig;
        if (configValue.equals("2")) {
            activityConfig = runActivityConfigService.getByType(12, null);
        } else {
            activityConfig = runActivityConfigService.getByType(2, 4);
        }
        config = activityConfig.getActivityConfig();
        newPkMultipleConfigVo = JsonUtil.readValue(config, NewPkMultipleConfigVo.class);
        String currencyCode = znsUserAccountService.getUserAccount(user.getId()).getCurrencyCode();
        List<UserGameRankAwardDto> userRankAward = newPkMultipleConfigVo.getUserRankAward();
        //填充排名多币种奖励
        if (!CollectionUtils.isEmpty(userRankAward)) {
            for (UserGameRankAwardDto userGameRankAwardDto : userRankAward) {
                UserGameAwardDto awardDto = userGameRankAwardDto.getAwardDto();
                if (!CollectionUtils.isEmpty(awardDto.getAmountList())) {
                    List<CurrencyAmount> list = awardDto.getAmountList().stream().filter(s -> s.getCurrencyCode().equals(currencyCode)).toList();
                    list.forEach(item -> item.setAmount(I18nConstant.currencyFormat(item.getCurrencyCode(), item.getAmount())));
                    awardDto.setAmountList(list);
                    BigDecimal amount = list.get(0).getAmount();
                    amount = I18nConstant.currencyFormat(currencyCode, amount);
                    awardDto.setAmount(amount);
                }
            }
        }
        //完成奖励多币种
        newPkMultipleConfigVo.setFinishAward(fillCurrency(newPkMultipleConfigVo.getFinishAward(), currencyCode));
        newPkMultipleConfigVo.setUnFinishAward(fillCurrency(newPkMultipleConfigVo.getUnFinishAward(), currencyCode));
        newPkMultipleConfigVo.setCurrency(I18nConstant.buildCurrency(currencyCode));
        return newPkMultipleConfigVo;
    }

    private static UserGameAwardDto fillCurrency(UserGameAwardDto userGameAwardDto, String currencyCode) {
        if (Objects.nonNull(userGameAwardDto)) {
            if (!CollectionUtils.isEmpty(userGameAwardDto.getAmountList())) {
                List<CurrencyAmount> list = userGameAwardDto.getAmountList().stream().filter(s -> s.getCurrencyCode().equals(currencyCode)).toList();
                list.forEach(item -> item.setAmount(I18nConstant.currencyFormat(item.getCurrencyCode(), item.getAmount())));
                userGameAwardDto.setAmountList(list);
                BigDecimal amount = list.get(0).getAmount();
                amount = I18nConstant.currencyFormat(currencyCode, amount);
                userGameAwardDto.setAmount(amount);
                return userGameAwardDto;
            }
        }
        return null;
    }

    @Resource
    private NewTeamDetailRecordService newTeamDetailRecordService;

    @Resource
    private ActivityPlaylistRelService activityPlaylistRelService;

    @Override
    public NewUserMatchMultipleResp newUserMatchMultiple(NewUserMultipleMatchReq req, Integer appVersion) {
        var respVo = new NewUserMatchMultipleResp();
        RunActivityRequest runActivity = new RunActivityRequest();
        runActivity.setActivityTitle("Newcomer Multiplayer PK Tournament"); //活动title
        //新人引导 多人vs
        NewPersonPkVo newPersonPkVo = newPersonPkBizService.selectNewPersonPkVo(2L);
        ZnsRunActivityConfigEntity activityConfig = runActivityConfigService.getByType(12, null);
        runActivity.setActivityConfigId(activityConfig.getId());
        ZnsUserEntity currentUser = znsUserService.findById(req.getUserId());
        // routeId
        runActivity.setActivityRouteId(Long.valueOf(newPersonPkVo.getRouteId()));
        // 活动配置
        String config = activityConfig.getActivityConfig();
        NewPkMultipleConfigVo newPkMultipleConfigVo = JsonUtil.readValue(config, NewPkMultipleConfigVo.class);
        newPkMultipleConfigVo.setIsMusic(newPersonPkVo.getIsMusic());
        if (newPkMultipleConfigVo.getCompleteType() == 1 && newPkMultipleConfigVo.getRunMileage().compareTo(BigDecimal.ZERO) > 0) {
            runActivity.setCompleteRuleType(1); //完成规则类型：1 表示完成跑步里程，2表示完成跑步时长
            runActivity.setRunningGoalsUnit(0);//跑步目标单位，0：公里 ，1：英里，2：时间（min）
            runActivity.setRunMileage(newPkMultipleConfigVo.getRunMileage());
            runActivity.setRunTime(BigDecimal.ZERO);
        } else if (newPkMultipleConfigVo.getCompleteType() == 2 && Objects.nonNull(newPkMultipleConfigVo.getRunTime()) && newPkMultipleConfigVo.getRunTime() > 0) {
            runActivity.setCompleteRuleType(2); //完成规则类型：1 表示完成跑步里程，2表示完成跑步时长
            runActivity.setRunningGoalsUnit(2);
            runActivity.setRunTime(new BigDecimal(newPkMultipleConfigVo.getRunTime()).multiply(new BigDecimal(60)));
            runActivity.setRunMileage(BigDecimal.ZERO);
        }
        runActivity.setActivityEntryFee(BigDecimal.ZERO);
        runActivity.setBonusRuleType(1);//奖金规则类型：1表示免费参加，2表示保证金参加
        runActivity.setIsRobotStart(0);//是否机器人发起: 0表示非机器人发起,1表示机器人发起
        runActivity.setIsNowStart(0);//活动开始时间是否是现在: 1表示现在发起活动，0表示不是现在发起活动
        runActivity.setIsPublic(0); //0 不公开
        ZonedDateTime activityStartTime = DateUtil.addSeconds(ZonedDateTime.now(), 30);
        ;
        runActivity.setActivityStartTime(activityStartTime.toInstant().toEpochMilli());
        runActivity.setAppVersion(appVersion);
        runActivity.setCreateSource(ActivityCreateSourceEnum.NEWCOMER_ACTIVITIES.getStatusCode());
        Result result = activityStrategyContext.doLaunchActivity(runActivity, currentUser);
        if (CommonError.SUCCESS.getCode().equals(result.getCode())) {
            Map<String, Object> data = (Map<String, Object>) result.getData();
            Long activityId = MapUtil.getLong(data.get("activityId"), -1L);
            // 更新开始时间 ，因为这个才是整点的
            ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
            respVo.setActivityId(znsRunActivityEntity.getId());
            respVo.setActivityStartTime(znsRunActivityEntity.getActivityStartTime());
            //保存参赛detail_ids
            Integer detailNum = newPkMultipleConfigVo.getDetailNum();
            List<Long> detailIds = newPkMultipleConfigVo.getDetailIds();
            Collections.shuffle(detailIds);
            detailIds = detailIds.subList(0, detailNum);
            detailIds.forEach(i -> {
                var detailsId = znsUserRunDataDetailsService.findById(i);
                var record = new NewTeamDetailRecord();
                record.setActivityId(activityId);
                record.setUserId(req.getUserId());
                record.setCompleteRuleType(runActivity.getCompleteRuleType());
                record.setDetailId(i);
                record.setNum(detailNum);
                record.setDetailUserId(detailsId.getUserId());
                newTeamDetailRecordService.save(record);
            });
            if (Objects.equals(newPersonPkVo.getIsMusic(), 0)) {
                //歌单list id 绑定活动id
                List<Long> musicList = newPersonPkVo.getMusicList();
                if (!CollectionUtils.isEmpty(musicList)) {
                    musicList.forEach(i -> {
                        var rel = new ActivityPlaylistRel();
                        rel.setActivityId(activityId);
                        rel.setPlaylistId(i);
                        activityPlaylistRelService.save(List.of(rel));
                    });
                }
                znsRunActivityEntity.setMusicSupport(ActivityMusicSupportEnum.SUPPORT.getCode());
                znsRunActivityService.updateById(znsRunActivityEntity);
            }
        }
        respVo.setActivityRouteId(runActivity.getActivityRouteId());
        respVo.setIsMusic(newPkMultipleConfigVo.getIsMusic());
        respVo.setCompleteType(newPkMultipleConfigVo.getCompleteType());
        respVo.setTargetMileage(runActivity.getRunMileage());
        respVo.setTargetRunTime(runActivity.getRunTime().intValue());
        respVo.setActivityType(RunActivityTypeEnum.NEW_USER_PK_MANY.getType());
        respVo.setActivityTitle(runActivity.getActivityTitle());
        return respVo;
    }

}
