package com.linzi.pitpat.api.activityservice.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityBaseRequest;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityPropObtainRequest;
import com.linzi.pitpat.api.activityservice.dto.request.SpeedPropUseRequest;
import com.linzi.pitpat.api.activityservice.manager.ActivityPropManager;
import com.linzi.pitpat.api.activityservice.vo.ActivityPropVo;
import com.linzi.pitpat.api.activityservice.vo.PropUrgeConfigVo;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityPropEffectEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityPropStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityPropUrgeConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPropConfig;
import com.linzi.pitpat.data.activityservice.model.entity.PropManage;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.SpeedPropRecord;
import com.linzi.pitpat.data.activityservice.model.entity.UserPropRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityPropConfigService;
import com.linzi.pitpat.data.activityservice.service.PropManageService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.SpeedPropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserPropRecordService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.entry.UrgeConfig;
import com.linzi.pitpat.data.awardservice.service.UrgeConfigService;
import com.linzi.pitpat.data.domian.query.SpeedPropRecordQuery;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ActivityPropManagerImpl implements ActivityPropManager {

    private final UrgeConfigService urgeConfigService;
    private final ActivityPropConfigService activityPropConfigService;
    private final PropManageService propManageService;
    private final UserPropRecordService userPropRecordService;
    private final RunActivityUserTaskService runActivityUserTaskService;
    private final ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    private final MainActivityBizService mainActivityBizService;
    private final ZnsUserService userService;
    private final SpeedPropRecordService speedPropRecordService;

    @Override
    public List<ActivityPropVo> queryActivityProp(ActivityBaseRequest request) {
        List<ActivityPropVo> voList = new ArrayList<>();
        List<ActivityPropConfig> list = activityPropConfigService.selectActivityPropConfigByActivityId(request.getActivityId());
        if (CollectionUtils.isEmpty(list)) {
            return voList;
        }
        List<Long> propIdList = list.stream().map(ActivityPropConfig::getPropId).collect(Collectors.toList());
        List<Integer> urgeConfigTypeList = propIdList.stream().map(e -> ActivityPropUrgeConfigEnum.getUrgeConfigTypeByPropId(e)).collect(Collectors.toList());
        Map<Integer, UrgeConfig> urgeConfigMap = urgeConfigService.list(Wrappers.<UrgeConfig>lambdaQuery().in(UrgeConfig::getType, urgeConfigTypeList))
                .stream().collect(Collectors.toMap(UrgeConfig::getType, Function.identity()));
        Map<Long, PropManage> propManageMap = propManageService.selectPropManageByPropIds(propIdList)
                .stream().collect(Collectors.toMap(PropManage::getPropId, Function.identity()));
        voList = list.stream().map(e -> {
            ActivityPropVo vo = new ActivityPropVo();
            vo.setPropId(e.getPropId());
            Integer triggerLimit = e.getTriggerLimit();
            vo.setTriggerLimit(triggerLimit);
            if (triggerLimit >= 0) {
                int i = userPropRecordService.countUserPropAcquiredTimes(request.getUserId(), e.getPropId(), request.getActivityId());
                vo.setTriggerLimit(triggerLimit - i);
            } else if (triggerLimit < 0) {
                // 兼容游戏端未处理-1，使用9999表示不限次数
                vo.setTriggerLimit(9999);
            }
            PropManage propManage = propManageMap.get(e.getPropId());
            vo.setTitle(propManage.getTitle());
            List<String> effectValueList = JsonUtil.readList(propManage.getEffectValue(), String.class);
            vo.setEffectValue(Joiner.on(",").join(effectValueList));
            vo.setImageUrl(propManage.getImageUrl());
            UrgeConfig urgeConfig = urgeConfigMap.get(ActivityPropUrgeConfigEnum.getUrgeConfigTypeByPropId(e.getPropId()));
            vo.setConfigVo(BeanUtil.copyBean(urgeConfig, PropUrgeConfigVo.class));
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    public Boolean obtainProp(ActivityPropObtainRequest request) {
        UserPropRecord userPropRecord = new UserPropRecord();
        userPropRecord.setUserId(request.getUserId());
        userPropRecord.setGainActivityId(request.getGainActivityId());
        userPropRecord.setPropId(request.getPropId());
        ZnsUserEntity user = userService.findById(request.getUserId());
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(request.getGainActivityId(), user.getZoneId());

        if (Objects.isNull(activityNew)) {
            return false;
        }
        ZnsUserRunDataDetailsEntity runDataDetails = znsUserRunDataDetailsService.getLatestUserActivityRunDataDetails(request.getUserId(), request.getGainActivityId());
        if (Objects.isNull(runDataDetails)) {
            return false;
        }
        userPropRecord.setGainRunDataDetailsId(runDataDetails.getId());
        Integer level = null;
        if (MainActivityTypeEnum.OLD.getType().equals(activityNew.getMainType()) && RunActivityTypeEnum.TASK_ACTIVITY.getType().equals(activityNew.getActivityType())) {
            if (Objects.isNull(request.getGainTaskId())) {
                return false;
            }
            RunActivityUserTask activityUserTask = runActivityUserTaskService.selectRunActivityUserTaskById(request.getGainTaskId());
            if (Objects.isNull(activityUserTask)) {
                return false;
            }
            level = activityUserTask.getLevel();
            userPropRecord.setGainLevel(activityUserTask.getLevel());
        }
        PropManage propManage = propManageService.selectPropManageByPropIds(Lists.newArrayList(request.getPropId())).get(0);
        Integer effectCode = propManage.getEffectCode();
        if (ActivityPropEffectEnum.REDUCE_FINISH_TIME.getCode().equals(effectCode)) {
            // 减少完赛时间的道具，获得即用
            userPropRecord.setUseActivityId(request.getGainActivityId());
            userPropRecord.setUseRunDataDetailsId(runDataDetails.getId());
            userPropRecord.setUseLevel(level);
            userPropRecord.setUseTime(ZonedDateTime.now());
            userPropRecord.setPropStatus(ActivityPropStatusEnum.USED.getCode());
        }
        userPropRecord.setEffectCode(effectCode);
        List<String> effectValueList = JsonUtil.readList(propManage.getEffectValue(), String.class);
        if (!effectValueList.contains(request.getActualEffectValue())) {
            return false;
        }
        userPropRecord.setActualEffectValue(request.getActualEffectValue());
        return userPropRecordService.insertUserPropRecord(userPropRecord) > 0;
    }

    @Override
    public Boolean useSpeedProp(SpeedPropUseRequest request) {
        SpeedPropRecordQuery query = SpeedPropRecordQuery.builder().activityId(request.getActivityId()).userId(request.getUserId()).build();
        Integer speedValue = speedPropRecordService.querySpeedValue(query);
        if (speedValue > 0) {
            // 一个活动只会使用一次道具
            return false;
        }
        SpeedPropRecord speedPropRecord = new SpeedPropRecord();
        speedPropRecord.setUserId(request.getUserId());
        speedPropRecord.setActivityId(request.getActivityId());
        speedPropRecord.setEffectValue(request.getSpeedValue());
        return speedPropRecordService.insert(speedPropRecord) > 0;
    }


}
