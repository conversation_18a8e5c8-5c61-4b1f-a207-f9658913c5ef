package com.linzi.pitpat.api.activityservice.manager;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.activityservice.dto.request.MilestoneDataRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.MilestoneDataUploadRequestDto;
import com.linzi.pitpat.api.activityservice.dto.response.BattlePassMilestoneAwardReceiveResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.BattlePassMilestoneAwardResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.BattlePassMilestoneCouponResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.BattlePassMilestoneHomepagePromptDto;
import com.linzi.pitpat.api.activityservice.dto.response.BattlePassMilestoneResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.BattlePassMilestoneWearResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.MilestoneDataResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.MilestoneDataUploadResponseDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.biz.BattlePassMilestoneBizService;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityEntryFee;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.ActivityEntryFeeQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.request.HandleActivityRequest;
import com.linzi.pitpat.data.activityservice.service.ActivityAwardConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityEntryFeeService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.activityservice.strategy.BattlePassCumulativeRunActivityStrategy;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardTypeEnum;
import com.linzi.pitpat.data.awardservice.model.dto.AwardConfigDto;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.AwardConfigAmountCurrency;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponCurrencyEntity;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBattlePass;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.AwardConfigAmountCurrencyQuery;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserWearBagQuery;
import com.linzi.pitpat.data.awardservice.model.vo.UserMilestoneCoupon;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountCurrencyDataService;
import com.linzi.pitpat.data.awardservice.service.AwardConfigAmountService;
import com.linzi.pitpat.data.awardservice.service.CouponCurrencyService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagLogService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBattlePassService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.entity.dto.user.LoginUserDto;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.enums.CouponTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/8 15:53
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BattlePassMilestoneManager {
    private final ZnsRunActivityService runActivityService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ActivityStrategyContext activityStrategyContext;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ActivityAwardConfigService activityAwardConfigService;
    private final CouponService couponService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserCouponService userCouponService;
    private final CouponI18nService couponI18nService;
    private final UserWearsBagService userWearsBagService;
    private final ApplicationContext springContext;
    private final ZnsUserAccountService userAccountService;
    private final AppMessageService appMessageService;
    private final UserWearsBattlePassService userWearsBattlePassService;
    private final UserWearsBagLogService userWearsBagLogService;
    private final ActivityEntryFeeService activityEntryFeeService;
    private final AwardConfigAmountService awardConfigAmountService;
    private final AwardConfigAmountCurrencyDataService awardConfigAmountCurrencyDataService;
    private final CouponCurrencyService couponCurrencyService;
    private final AreaService areaService;
    private final ActivityAwardCurrencyBizService activityAwardCurrencyBizService;
    private final BattlePassMilestoneBizService battlePassMilestoneBizService;
    private final RedissonClient redissonClient;

    /**
     * 战令里程碑详情
     *
     * @param userDto
     * @return
     */
    public BattlePassMilestoneResponseDto findInfo(LoginUserDto userDto) {
        BattlePassMilestoneResponseDto dto = new BattlePassMilestoneResponseDto();
        ZonedDateTime date = ZonedDateTime.now();
        String zoneId = getUserZoneId(userDto.getLoginUser());
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        int hour = timeZone.getRawOffset() / (60 * 60 * 1000);
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(date, timeZone);
        ZonedDateTime startOfMonth = DateUtil.addHours(DateUtil.getFirstOfMonth(now), -hour);
        ZonedDateTime endOfMonth = DateUtil.addHours(DateUtil.getEndOfMonth(now), -hour);


        //查询当前时间内的新里程碑
        ZnsRunActivityEntity runActivity = getCurrentBattlePass(userDto.getLoginUser().getIsTest(), now);

        if (Objects.isNull(runActivity)) {
            return dto;
        }
        ZnsUserEntity loginUser = userDto.getLoginUser();
        String activityStartTimeStr = DateUtil.parseDateToStr(DateUtil.MM_DD, runActivity.getActivityStartTime());
        String activityEndTimeStr = DateUtil.parseDateToStr(DateUtil.MM_DD, runActivity.getActivityEndTime());
        dto.setMilestoneTime(activityStartTimeStr + " - " + activityEndTimeStr);
        dto.setActivityId(runActivity.getId()).setActivityStartTime(runActivity.getActivityStartTime()).setActivityEndTime(runActivity.getActivityEndTime());
        String currencyCode = userAccountService.getUserCurrency(loginUser.getId()).getCurrencyCode();

        fillEntryFeeCurrency(dto, runActivity, loginUser);

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(runActivity.getId(), userDto.getUserId());
        if (Objects.isNull(activityUser)) {
            activityStrategyContext.handleUserActivityState(runActivity.getId(), 1, userDto.getLoginUser(), "", null, true, null, new HandleActivityRequest(), false);
            activityUser = runActivityUserService.findActivityUser(runActivity.getId(), userDto.getUserId());
        }
        dto.setRunMileage(activityUser.getRunMileage().intValue());
        dto.setRunTime(activityUser.getRunTime());
        dto.setHashUnclaimedAward(activityUser.getHashUnclaimedAward());
        //是否能使用券
        couponUse(userDto, dto, runActivity);
        //是否付费
        if (activityUser.getIsPay() == 1) {
            dto.setBattlePassMilestoneUserType(1);
        } else {
            dto.setBattlePassMilestoneUserType(0);
            // 非付费用户访问里程碑页面 做首次记录，用于首页里程碑红点判断
            String key = String.format(RedisKeyConstant.MILESTONE_USER_VIEW, runActivity.getId(), loginUser.getId());
            if (!redissonClient.getBucket(key).isExists()) {
                redissonClient.getBucket(key).set("1", 31, TimeUnit.DAYS);// 31天保证当月一定存在
            }
        }

        //是否有历史奖励
        Long historyAward = hashHistoryAward(runActivity.getId(), now, userDto.getLoginUser());
        dto.setHashHistoryAward(historyAward > 0 ? 1 : 0);
        if (historyAward > 0) {
            dto.setHistoryActivityId(historyAward);
        }

        //待上传数据

        long count = userRunDataDetailsService.countWaitRunData(userDto.getUserId(), startOfMonth, endOfMonth);
        dto.setToUploadCount((int) count);

        //奖励
        dto.setAwardConfigDetailsDtoList(battlePassMilestoneBizService.getAwardConfigDetailsDtoList(runActivity.getId(), userDto.getUserId(), dto.getRunMileage(), dto.getBattlePassMilestoneUserType()));
        return dto;
    }

    public ZnsRunActivityEntity getCurrentBattlePass(Integer isTest, ZonedDateTime now) {
        RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                .isDelete(0)
                .activityStateIn(Arrays.asList(0, 1))
                .maxActivityStartTime(now).minActivityEndTime(now);
        if (Objects.equals(0, isTest)) {
            runActivityQueryBuilder.isTest(0);
        }
        ZnsRunActivityEntity runActivity = runActivityService.findOne(runActivityQueryBuilder.build());
        return runActivity;
    }

    /**
     * 获取用户注册时区
     *
     * @param loginUser
     * @return
     */
    private String getUserZoneId(ZnsUserEntity loginUser) {
        String stateCode = loginUser.getStateCode();
        if (!StringUtils.hasText(stateCode)) {
            return loginUser.getZoneId();
        }
        AreaEntity area = areaService.selectAreaByCode(stateCode);
        return area.getZoneId();
    }

    /**
     * 设置入参费
     *
     * @param dto
     * @param runActivity
     * @param loginUser
     */
    private void fillEntryFeeCurrency(BattlePassMilestoneResponseDto dto, ZnsRunActivityEntity runActivity, ZnsUserEntity loginUser) {
        dto.setActivityEntryFee(runActivity.getActivityEntryFee());
        Currency currency = new Currency();
        currency.setCurrencyName(I18nConstant.CurrencyCodeEnum.USD.getName());
        currency.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
        currency.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
        ActivityEntryFeeQuery activityEntryFeeQuery = ActivityEntryFeeQuery.builder().activityId(runActivity.getId()).build();
        List<ActivityEntryFee> activityEntryFees = activityEntryFeeService.findList(activityEntryFeeQuery);
        if (!CollectionUtils.isEmpty(activityEntryFees)) {
            String currencyCode = userAccountService.getUserAccount(loginUser.getId()).getCurrencyCode();
            I18nConstant.CurrencyCodeEnum currencyCodeEnum = I18nConstant.CurrencyCodeEnum.findByCode(currencyCode);
            currency.setCurrencyName(currencyCodeEnum.getName());
            currency.setCurrencyCode(currencyCode);
            currency.setCurrencySymbol(currencyCodeEnum.getSymbol());
            ActivityEntryFee activityEntryFee = activityEntryFees.stream().filter(e -> e.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
            if (activityEntryFee != null) {
                BigDecimal entryFee = activityEntryFee.getEntryFee();
                entryFee = I18nConstant.currencyFormat(currencyCode, entryFee);
                dto.setActivityEntryFee(entryFee);
            }
        }
        dto.setCurrency(currency);
    }

    //是否有优惠券抵扣
    private void couponUse(LoginUserDto userDto, BattlePassMilestoneResponseDto dto, ZnsRunActivityEntity runActivity) {
        List<UserMilestoneCoupon> userMilestoneCoupons = userCouponService.selectUserMilestoneCoupon(userDto.getUserId());
        String currencyCode = userAccountService.getUserCurrency(userDto.getUserId()).getCurrencyCode();
        if (!CollectionUtils.isEmpty(userMilestoneCoupons)) {
            List<UserMilestoneCoupon> collect = userMilestoneCoupons.stream().sorted(Comparator.comparing(UserMilestoneCoupon::getExpireTime).thenComparing(UserMilestoneCoupon::getGmtCreate)).collect(Collectors.toList());
            dto.setDiscountType(2);
            dto.setDefaultCouponId(collect.get(0).getCouponId());
            dto.setDefaultUserCouponId(collect.get(0).getUserCouponId());
            BigDecimal payAmount = dto.getActivityEntryFee().subtract(collect.get(0).getCouponAmount());
            if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
                dto.setDiscountFee(BigDecimal.ZERO);
            } else {
                payAmount = I18nConstant.currencyFormat(currencyCode, payAmount);
                dto.setDiscountFee(payAmount);
            }
        } else {
            dto.setDiscountType(0);
            BigDecimal activityEntryFee = I18nConstant.currencyFormat(currencyCode, dto.getActivityEntryFee());
            dto.setDiscountFee(activityEntryFee);
        }
    }


    /**
     * 是否有历史待领取奖励
     *
     * @param activityId
     * @param now
     * @param user
     * @return 大于0表述有，返回活动id
     */
    public Long hashHistoryAward(Long activityId, ZonedDateTime now, ZnsUserEntity user) {
        //查询当前时间内的新里程碑
        RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                .isDelete(0).maxIdLt(activityId);
        if (user.getIsTest() == 0) {
            runActivityQueryBuilder.isTest(0);
        }
        RunActivityQuery runActivityQuery = runActivityQueryBuilder.build();
        runActivityQuery.setOrders(List.of(OrderItem.desc("activity_start_time")));
        ZnsRunActivityEntity runActivity = runActivityService.findOne(runActivityQuery);

        if (Objects.isNull(runActivity)) {
            return 0L;
        }

        //是否上个月
        ZonedDateTime lastMonth = DateUtil.addMonths(now, -1);
        if (!DateUtil.isSameMonth(lastMonth, runActivity.getActivityStartTime())) {
            return 0L;
        }

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(runActivity.getId(), user.getId());
        if (Objects.isNull(activityUser)) {
            return 0L;
        }

        //是否付费
        if (activityUser.getIsPay() != 1) {
            return 0L;
        }

        return activityUser.getHashUnclaimedAward() > 0 ? runActivity.getId() : 0L;
    }

    /**
     * 待领取奖励数据
     *
     * @param loginUser
     * @param activityId
     * @param isPay
     * @return
     */
    public BattlePassMilestoneAwardResponseDto awardUnclaimed(LoginUserDto loginUser, Long activityId, Integer isPay) {
        BattlePassMilestoneAwardResponseDto dto = new BattlePassMilestoneAwardResponseDto();
        dto.setActivityId(activityId);

        //查询当前时间内的新里程碑
        ZnsRunActivityEntity runActivity = runActivityService.findById(activityId);
        if (Objects.isNull(runActivity)) {
            return dto;
        }
        String currencyCode = userAccountService.getUserCurrency(loginUser.getUserId()).getCurrencyCode();

        //查询待领取奖励
        BigDecimal sumAward = userAccountDetailService.sumAward(activityId, loginUser.getUserId(), battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.AMOUNT.getType()), 3);
        sumAward = I18nConstant.currencyFormat(currencyCode, sumAward);
        dto.setAwardAmount(sumAward);
        // 设置币种
        Currency currency = new Currency();
        activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, activityId, loginUser.getUserId());
        dto.setCurrency(currency);
        //积分
        Integer sumScore = activityUserScoreService.sumScore(activityId, loginUser.getUserId(), battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.SCORE.getType()), 0);
        dto.setAwardScore(sumScore);
        //券
        List<UserCoupon> couponList = userCouponService.findListBySourceType(loginUser.getUserId(), activityId, battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.COUPON.getType()), 5);
        if (!CollectionUtils.isEmpty(couponList)) {
            List<BattlePassMilestoneCouponResponseDto> awardCouponList = new ArrayList<>();
            List<Long> couponIds = couponList.stream().map(UserCoupon::getCouponId).collect(Collectors.toList());
            List<Coupon> coupons = couponService.findListByIds(couponIds);
            Map<Long, Coupon> couponMap = coupons.stream().collect(Collectors.toMap(Coupon::getId, Function.identity()));

            Map<Long, List<UserCoupon>> userCouponMap = couponList.stream().collect(Collectors.groupingBy(UserCoupon::getCouponId));
            for (Map.Entry<Long, List<UserCoupon>> couponEntry : userCouponMap.entrySet()) {
                BattlePassMilestoneCouponResponseDto couponResponseDto = new BattlePassMilestoneCouponResponseDto();
                Coupon coupon = couponMap.get(couponEntry.getKey());
                if (Objects.nonNull(coupon)) {
                    int size = couponEntry.getValue().size();
                    fillCouponInfo(currency, size, couponResponseDto, coupon);
                }
                awardCouponList.add(couponResponseDto);
            }
            dto.setAwardCouponList(awardCouponList);
        }
        //服装
        List<UserWearsBattlePass> userWearsBattlePasses = userWearsBattlePassService.list(Wrappers.<UserWearsBattlePass>lambdaQuery()
                .eq(UserWearsBattlePass::getActivityId, activityId)
                .eq(UserWearsBattlePass::getUserId, loginUser.getUserId())
                .eq(UserWearsBattlePass::getStatus, YesNoStatus.NO.getCode())
                .in(UserWearsBattlePass::getSource, battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.WEAR.getType()))
                .eq(UserWearsBattlePass::getIsDelete, 0)
                .orderByAsc(UserWearsBattlePass::getSource, UserWearsBattlePass::getMilepostId));
        if (!CollectionUtils.isEmpty(userWearsBattlePasses)) {
            List<BattlePassMilestoneWearResponseDto> awardWearList = userWearsBattlePasses.stream().map(w -> {
                BattlePassMilestoneWearResponseDto wearResponseDto = new BattlePassMilestoneWearResponseDto(w.getWearType(), w.getWearName(), w.getWearValue(), w.getWearImageUrl(), w.getExpiredTime());
                return wearResponseDto;
            }).collect(Collectors.toList());
            dto.setAwardWearList(awardWearList);
        }
        return dto;
    }

    public BattlePassMilestoneAwardResponseDto history(LoginUserDto loginUser) {
        ZonedDateTime date = ZonedDateTime.now();
        String zoneId = getUserZoneId(loginUser.getLoginUser());
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(date, timeZone);
        //查询当前时间内的新里程碑
        RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                .isDelete(0).maxActivityStartTime(now).minActivityEndTime(now);
        if (loginUser.getLoginUser().getIsTest() == 0) {
            runActivityQueryBuilder.isTest(0);
        }
        ZnsRunActivityEntity runActivity = runActivityService.findOne(runActivityQueryBuilder.build());
        if (Objects.isNull(runActivity)) {
            return new BattlePassMilestoneAwardResponseDto();
        }
        //查询上个月id
        Long historyId = hashHistoryAward(runActivity.getId(), now, loginUser.getLoginUser());
        if (historyId <= 0) {
            return new BattlePassMilestoneAwardResponseDto();
        }

        //此步骤一定为进阶用户
        return awardUnclaimed(loginUser, historyId, 1);
    }

    /**
     * 查询货币种类
     *
     * @param userId
     * @return
     */
    private Currency getCurrency(Long userId) {
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(userId);
        Currency currency = I18nConstant.buildCurrency(userAccount.getCurrencyCode());
        return currency;
    }

    public BattlePassMilestoneAwardReceiveResponseDto awardReceive(Long activityId, LoginUserDto loginUserDto) {
        BattlePassMilestoneAwardReceiveResponseDto dto = new BattlePassMilestoneAwardReceiveResponseDto();

        Currency currency = getCurrency(loginUserDto.getUserId());

        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, loginUserDto.getUserId());

        //查询待领取奖励-变成已领取
        BattlePassMilestoneAwardResponseDto awardUnclaimed = awardUnclaimed(loginUserDto, activityId, activityUser.getIsPay());
        dto.setObtainedAward(awardUnclaimed);
        Integer runMileage = 0;
        if (Objects.nonNull(activityUser)) {
            runMileage = activityUser.getRunMileage().intValue();
        }

        //领取奖励
        claimRewards(activityId, loginUserDto.getLoginUser());

        //判断是否进阶用户
        if (!Objects.equals(activityUser.getIsPay(), 1)) {
            //获取进阶可获得奖励
            List<AwardConfigDto> activityTypeAwardConfigs = activityAwardConfigService.selectAwardConfigDtoList(activityId, null, null);
            if (!CollectionUtils.isEmpty(activityTypeAwardConfigs)) {
                Integer finalRunMileage = runMileage;
                List<AwardConfigDto> dtoList = activityTypeAwardConfigs.stream().filter(a -> a.getAwardCondition() == 1 && a.getTargetMileage() <= finalRunMileage).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(dtoList)) {
                    Map<Integer, List<AwardConfigDto>> awardMap = dtoList.stream().collect(Collectors.groupingBy(AwardConfigDto::getAwardType));
                    BattlePassMilestoneAwardResponseDto unclaimedAward = new BattlePassMilestoneAwardResponseDto();
                    unclaimedAward.setCurrency(currency);
                    for (Map.Entry<Integer, List<AwardConfigDto>> awardEntry : awardMap.entrySet()) {
                        Integer awardType = awardEntry.getKey();
                        if (AwardTypeEnum.AMOUNT.getType().equals(awardType)) {
                            BigDecimal sumAmount = BigDecimal.ZERO;
                            for (AwardConfigDto e : awardEntry.getValue()) {
                                Long awardAmountId = awardConfigAmountService.findByAwardConfigId(e.getAwardConfigId()).getId();
                                AwardConfigAmountCurrencyQuery awardConfigAmountCurrencyQuery = AwardConfigAmountCurrencyQuery.builder().awardAmountIdList(List.of(awardAmountId)).build();
                                List<AwardConfigAmountCurrency> list = awardConfigAmountCurrencyDataService.findList(awardConfigAmountCurrencyQuery);
                                AwardConfigAmountCurrency awardConfigAmountCurrency = list.stream().filter(x -> currency.getCurrencyCode().equals(x.getCurrencyCode())).findFirst().orElse(null);
                                sumAmount = sumAmount.add(awardConfigAmountCurrency.getAmount());
                            }
                            sumAmount = I18nConstant.currencyFormat(currency.getCurrencyCode(), sumAmount);
                            unclaimedAward.setAwardAmount(sumAmount);
                        } else if (AwardTypeEnum.SCORE.getType().equals(awardType)) {
                            Integer sumScore = awardEntry.getValue().stream().mapToInt(AwardConfigDto::getScore).sum();
                            unclaimedAward.setAwardScore(sumScore);
                        } else if (AwardTypeEnum.WEAR.getType().equals(awardType)) {
                            List<BattlePassMilestoneWearResponseDto> wearResponseDtoList = awardEntry.getValue().stream().map(a -> {
                                BattlePassMilestoneWearResponseDto wearResponseDto = new BattlePassMilestoneWearResponseDto();
                                wearResponseDto.setWearType(Integer.valueOf(a.getWearType())).setWearValue(a.getWearValue()).setWearImageUrl(a.getWearImageUrl());
                                wearResponseDto.setExpiredTime(a.getExpiredTime());
                                return wearResponseDto;
                            }).collect(Collectors.toList());
                            unclaimedAward.setAwardWearList(wearResponseDtoList);
                        } else if (AwardTypeEnum.COUPON.getType().equals(awardType)) {
                            List<Coupon> couponList = awardEntry.getValue().stream().map(a -> {
                                List<Long> couponIds = NumberUtils.stringToLong2(a.getCouponIds());
                                Coupon coupon = couponService.selectCouponById(couponIds.get(0));
                                return coupon;
                            }).collect(Collectors.toList());
                            Map<Long, List<Coupon>> couponMap = couponList.stream().collect(Collectors.groupingBy(Coupon::getId));

                            List<BattlePassMilestoneCouponResponseDto> awardCouponList = new ArrayList<>();
                            for (Map.Entry<Long, List<Coupon>> couponEntry : couponMap.entrySet()) {
                                BattlePassMilestoneCouponResponseDto couponResponseDto = new BattlePassMilestoneCouponResponseDto();
                                Coupon coupon = couponEntry.getValue().get(0);
                                int size = couponEntry.getValue().size();
                                fillCouponInfo(currency, size, couponResponseDto, coupon);
                                awardCouponList.add(couponResponseDto);
                            }
                            unclaimedAward.setAwardCouponList(awardCouponList);
                        }
                    }
                    dto.setAdvancedAward(unclaimedAward);
                }
            }
        }

        return dto;
    }

    /**
     * 填充券信息
     *
     * @param currency
     * @param size
     * @param couponResponseDto
     * @param coupon
     */
    private void fillCouponInfo(Currency currency, int size, BattlePassMilestoneCouponResponseDto couponResponseDto, Coupon coupon) {
        CouponCurrencyEntity couponCurrencyEntity = couponCurrencyService.selectByCouponIdAndCurrencyCode(coupon.getId(), currency.getCurrencyCode());
        couponResponseDto.setCurrency(currency);
        couponResponseDto.setCouponAmount(couponCurrencyEntity.getAmount());
        couponResponseDto.setCouponId(coupon.getId());
        couponResponseDto.setCouponType(coupon.getCouponType());
        couponResponseDto.setCouponNum(size);
    }

    private void claimRewards(Long activityId, ZnsUserEntity loginUser) {
        //查询所有奖励
        ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, loginUser.getId());
        Integer isPay = activityUser.getIsPay();

        //金额
        List<ZnsUserAccountDetailEntity> amountList = userAccountDetailService.findList(new UserAccountDetailByQuery()
                .setActivityId(activityId)
                .setUserId(loginUser.getId())
                .setTradeType(battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.AMOUNT.getType()))
                .setTradeStatus(3));

        if (!CollectionUtils.isEmpty(amountList)) {
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (ZnsUserAccountDetailEntity userAccountDetailEntity : amountList) {
                userAccountDetailEntity.setTradeStatus(2);
                userAccountDetailEntity.setCreateTime(ZonedDateTime.now());
                userAccountDetailEntity.setModifieTime(ZonedDateTime.now());
                userAccountDetailService.updateById(userAccountDetailEntity);
                totalAmount = totalAmount.add(userAccountDetailEntity.getAmount());
            }
            userAccountService.increaseAmount(totalAmount, loginUser.getId(), true);
        }

        //积分
        List<ActivityUserScore> scoreList = activityUserScoreService.list(Wrappers.<ActivityUserScore>lambdaQuery().eq(ActivityUserScore::getActivityId, activityId)
                .eq(ActivityUserScore::getUserId, loginUser.getId()).eq(ActivityUserScore::getStatus, 0).in(ActivityUserScore::getSource, battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.SCORE.getType())));
        if (!CollectionUtils.isEmpty(scoreList)) {
            for (ActivityUserScore activityUserScore : scoreList) {
                activityUserScore.setStatus(1);
                activityUserScore.setGmtCreate(ZonedDateTime.now());
                activityUserScore.setGmtModified(ZonedDateTime.now());
                activityUserScore.setSendTime(ZonedDateTime.now());
                activityUserScore.setAwardTime(ZonedDateTime.now());
                activityUserScore.setExpireTime(DateUtil.addMonthsFirstDay(ZonedDateTime.now(), 13, TimeZone.getTimeZone("UTC-8")));
                activityUserScoreService.updateById(activityUserScore);
            }
        }

        //券
        List<UserCoupon> couponList = userCouponService.findListBySourceType(loginUser.getId(), activityId, battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.COUPON.getType()), 5);
        if (!CollectionUtils.isEmpty(couponList)) {
            for (UserCoupon userCoupon : couponList) {
                userCoupon.setStatus(0);
                userCoupon.setGmtCreate(ZonedDateTime.now());
                userCoupon.setGmtModified(ZonedDateTime.now());
                //券使用时间
                Coupon coupon = couponService.selectCouponById(userCoupon.getCouponId());
                CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder()
                        .couponId(coupon.getId()).langCode(LocaleContextHolder.getLocale().toString()).defaultLangCode(coupon.getDefaultLangCode()).build());
                String couponTitle = Objects.nonNull(couponI18n) && StringUtils.hasText(couponI18n.getTitle()) ? couponI18n.getTitle() : coupon.getTitle();
                if (coupon.getExpiryType() == 1) {
                    userCoupon.setGmtStart(ZonedDateTime.now());
                    userCoupon.setGmtEnd(DateUtil.addDays(ZonedDateTime.now(), coupon.getValidDays()));
                } else if (coupon.getExpiryType() == 2) {
                    userCoupon.setGmtStart(coupon.getGmtStart());
                    userCoupon.setGmtEnd(coupon.getGmtEnd());
                }
                userCouponService.update(userCoupon);
//                String msg;
//                if (coupon.getCouponType().equals(CouponTypeEnum.AMAZON_COUPON.getCode()) || coupon.getCouponType().equals(CouponTypeEnum.OFFICIAL_WEBSITE_DISCOUNT_COUPON.getCode())) {
//                    String couponTypeName = CouponTypeEnum.findByType(coupon.getCouponType()).getName();
////                    String zoneId = getUserZoneId(loginUser);
//                    String dateStr = DateUtil.convertTimeZoneToString(userCoupon.getGmtEnd(), "UTC", loginUser.getZoneId());
//                    String expiryString = coupon.getExpiryType() == 1 ? coupon.getValidDays() + " days" : "before " + dateStr;
//                    if (!StringUtil.isEmpty(loginUser.getLanguageCode())) {
//                        msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg2", couponTypeName, expiryString);
//                    } else {
//                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg2", couponTypeName, expiryString);
//                    }
//
//                } else {
//                    if (!StringUtil.isEmpty(loginUser.getLanguageCode())) {
//                        msg = I18nMsgUtils.getMessage("push.content.user.coupon.msg1", couponTitle);
//                    } else {
//                        msg = I18nMsgUtils.getLangMessage(coupon.getDefaultLangCode(), "push.content.user.coupon.msg1", couponTitle);
//                    }
//                }
//                appMessageService.sendImTextWithNoJump(loginUser, coupon, msg, userCoupon);
                couponService.addQuotaSend(coupon.getId());
            }
        }
        //服装
        List<UserWearsBattlePass> userWearsBattlePasses = userWearsBattlePassService.list(Wrappers.<UserWearsBattlePass>lambdaQuery()
                .eq(UserWearsBattlePass::getActivityId, activityId)
                .eq(UserWearsBattlePass::getUserId, loginUser.getId())
                .eq(UserWearsBattlePass::getStatus, YesNoStatus.NO.getCode())
                .in(UserWearsBattlePass::getSource, battlePassMilestoneBizService.getSourceList(isPay, AwardTypeEnum.WEAR.getType()))
                .eq(UserWearsBattlePass::getIsDelete, 0));
        if (!CollectionUtils.isEmpty(userWearsBattlePasses)) {
            for (UserWearsBattlePass wearsBag : userWearsBattlePasses) {
                // 更新未领取记录
                wearsBag.setStatus(YesNoStatus.YES.getCode());
                userWearsBattlePassService.updateById(wearsBag);
                //服装获取记录表
                UserWearsBagLog userWearsBagLog = new UserWearsBagLog();
                // 执行服装时间 处理逻辑
                UserWearBagQuery userWearBagQuery = UserWearBagQuery.builder()
                        .userId(loginUser.getId())
                        .wearType(wearsBag.getWearType())
                        .wearValue(wearsBag.getWearValue())
                        .status(0)
                        .build();
                // 查询用户已经拥有的时装
                UserWearsBag existUserWearsBag = userWearsBagService.findByQuery(userWearBagQuery);
                if (Objects.isNull(existUserWearsBag)) {
                    UserWearsBag userWearsBagNew = UserWearsBag.builder()
                            .wearType(wearsBag.getWearType())
                            .wearName(wearsBag.getWearName())
                            .wearValue(wearsBag.getWearValue())
                            .isNew(0)
                            .userId(loginUser.getId())
                            .activityId(activityId)
                            .milepostId(wearsBag.getMilepostId())
                            .expiredTime(wearsBag.getExpiredTime() == null ? null : userWearsBagService.getAddHours(wearsBag.getExpiredTime()))
                            .wearImageUrl(wearsBag.getWearImageUrl())
                            .build();
                    userWearsBagService.insert(userWearsBagNew);
                    userWearsBagLog.setBagId(userWearsBagNew.getId());
                    // 不进行弹窗
                    userWearsBagLog.setIsNew(YesNoStatus.NO.getCode());
                } else {
                    // 用户限时道具 根据道具是否有有效期 进行永久化。或者增加时间
                    if (Objects.nonNull(existUserWearsBag.getExpiredTime())) {
                        if (Objects.nonNull(wearsBag.getExpiredTime())) {
                            log.info("existUserWearsBag time:{},wearsBag time:{}", existUserWearsBag.getExpiredTime(), wearsBag.getExpiredTime());
                            existUserWearsBag.setExpiredTime(DateUtil.addHours(existUserWearsBag.getExpiredTime(), wearsBag.getExpiredTime() * 24));
                        } else {
                            existUserWearsBag.setExpiredTime(null);
                        }
                        userWearsBagService.update(existUserWearsBag);
                    }
                    userWearsBagLog.setBagId(existUserWearsBag.getId());
                }
                userWearsBagLog.setSortNum(wearsBag.getMilepostId());
                userWearsBagLog.setActivityId(wearsBag.getActivityId());
                userWearsBagLog.setUserId(wearsBag.getUserId());
                userWearsBagLog.setIsNew(YesNoStatus.NO.getCode());
                if (Objects.nonNull(wearsBag.getExpiredTime())) {
                    userWearsBagLog.setExpiredTime(wearsBag.getExpiredTime());
                } else {
                    userWearsBagLog.setExpiredTime(0);
                }
                userWearsBagLogService.insert(userWearsBagLog);
            }
        }
        ZnsRunActivityUserEntity update = new ZnsRunActivityUserEntity();
        update.setHashUnclaimedAward(0);
        update.setId(activityUser.getId());
        runActivityUserService.updateById(update);
    }


    public Page dataList(MilestoneDataRequestDto request, LoginUserDto loginUserDto) {
        ZonedDateTime date = ZonedDateTime.now();
        String zoneId = getUserZoneId(loginUserDto.getLoginUser());
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        int hour = timeZone.getRawOffset() / (60 * 60 * 1000);
        ZonedDateTime now = DateUtil.getDate2ByTimeZone(date, timeZone);
        ZonedDateTime startOfMonth = DateUtil.addHours(DateUtil.getFirstOfMonth(now), -hour);
        ZonedDateTime endOfMonth = DateUtil.addHours(DateUtil.getEndOfMonth(now), -hour);

        Page page = userRunDataDetailsService.page(new Page<>(request.getPageNum(), request.getPageSize()), loginUserDto.getUserId()
                , startOfMonth, endOfMonth, request.getDataStatus());
        List<ZnsUserRunDataDetailsEntity> records = page.getRecords();
        List<MilestoneDataResponseDto> list = records.stream().map(d -> {
            MilestoneDataResponseDto dto = new MilestoneDataResponseDto();
            dto.setId(d.getId()).setDataSource(d.getDataSource()).setIsCountMilestone(d.getIsCountMilestone()).setKilocalorie(d.getKilocalorie())
                    .setRunTime(d.getRunTime()).setLastTime(d.getLastTime()).setRunMileage(d.getRunMileage()).setRunStatus(d.getRunStatus())
                    .setFatConsumption(d.getFatConsumption()).setAveragePace(d.getAveragePace()).setUnActivityType(d.getUnActivityType())
                    .setRunType(d.getRunType());
            return dto;
        }).collect(Collectors.toList());
        page.setRecords(list);
        return page;
    }

    public MilestoneDataUploadResponseDto dataUpload(MilestoneDataUploadRequestDto request, LoginUserDto loginUserDto) {
        MilestoneDataUploadResponseDto dto = new MilestoneDataUploadResponseDto();
        if (CollectionUtils.isEmpty(request.getDetailIds())) {
            return dto;
        }

        dto.setTotalCount(request.getDetailIds().size());
        Integer successCount = 0;
        BattlePassCumulativeRunActivityStrategy activityStrategy = springContext.getBean(BattlePassCumulativeRunActivityStrategy.class);

        for (Long detailId : request.getDetailIds()) {
            try {
                ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.findById(detailId);
                //检查数据状态
                if (userRunDataDetail.getIsCountMilestone() == 1) {
                    continue;
                }
                activityStrategy.handleBattlePassCumulativeRunActivity(userRunDataDetail, loginUserDto.getLoginUser(), request.getActivityId());

                //修改数据状态
                ZnsUserRunDataDetailsEntity runData = new ZnsUserRunDataDetailsEntity();
                runData.setId(detailId);
                runData.setIsCountMilestone(1);
                userRunDataDetailsService.update(runData);
                successCount += 1;
            } catch (Exception e) {
                log.error("dataUpload error,e=", e);
                dto.setSuccessCount(successCount);
                return dto;
            }
        }
        dto.setSuccessCount(successCount);
        return dto;
    }

    public BattlePassMilestoneHomepagePromptDto getHomepagePrompt(LoginUserDto loginUser) {
        ZnsUserEntity userEntity = loginUser.getLoginUser();
        BattlePassMilestoneHomepagePromptDto dto = new BattlePassMilestoneHomepagePromptDto();
        dto.setRedDotFlag(false);
        ZonedDateTime date = ZonedDateTime.now();
        String zoneId = getUserZoneId(userEntity);
        ZonedDateTime timeZoneDate = DateUtil.getDate2ByTimeZone(date, TimeZone.getTimeZone(zoneId));
        RunActivityQuery.RunActivityQueryBuilder runActivityQueryBuilder = RunActivityQuery.builder()
                .activityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType())
                .isDelete(0).activityState(ActivityStateEnum.IN_PROGRESS.getState())
                .maxActivityStartTime(timeZoneDate).minActivityEndTime(timeZoneDate);
        if (Objects.equals(0, userEntity.getIsTest())) {
            runActivityQueryBuilder.isTest(0);
        }
        ZnsRunActivityEntity runActivity = runActivityService.findOne(runActivityQueryBuilder.build());
        if (Objects.isNull(runActivity)) {
            return dto;
        } else {
            Long activityId = runActivity.getId();
            Long userId = userEntity.getId();
            ZnsRunActivityUserEntity activityUser = runActivityUserService.findActivityUser(activityId, userId);
            if (Objects.isNull(activityUser)) {
                // 给未报名用户报名
                activityStrategyContext.handleUserActivityState(activityId, 1, userEntity, "", null, true, null, new HandleActivityRequest(), false);
                activityUser = runActivityUserService.findActivityUser(activityId, userId);
            }
            Boolean awardUnclaimedFlag = getAwardUnclaimedFlag(loginUser, activityId, activityUser.getIsPay()); // 当月活动未领取奖励标志
            dto.setRedDotFlag(awardUnclaimedFlag);
            String key = String.format(RedisKeyConstant.MILESTONE_USER_VIEW, runActivity.getId(), userId);
            if (YesNoStatus.YES.getCode().equals(activityUser.getIsPay())) {
                //付费用户
                if (!dto.getRedDotFlag()) {
                    // 当月奖励已领，查询前一个月奖励是否领取（进阶里程碑可以领取上月未领奖励）
                    Long lastActivityId = hashHistoryAward(activityId, timeZoneDate, loginUser.getLoginUser());
                    dto.setRedDotFlag(lastActivityId > 0);
                }
            } else if (!redissonClient.getBucket(key).isExists()) {
                // 非付费用户，没有程碑页面访问记录
                dto.setRedDotFlag(true);
            }
        }
        return dto;
    }

    /**
     * 活动奖励未领取标志
     *
     * @param loginUser
     * @param activityId
     * @param isPay
     * @return true-有未领取的奖励
     */
    private Boolean getAwardUnclaimedFlag(LoginUserDto loginUser, Long activityId, Integer isPay) {
        BattlePassMilestoneAwardResponseDto awardResponseDto = awardUnclaimed(loginUser, activityId, isPay);
        if (Objects.nonNull(awardResponseDto.getAwardAmount())
                && awardResponseDto.getAwardAmount().compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (Objects.nonNull(awardResponseDto.getAwardScore()) && awardResponseDto.getAwardScore() > 0) {
            return true;
        }
        if (!CollectionUtils.isEmpty(awardResponseDto.getAwardCouponList())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(awardResponseDto.getAwardWearList())) {
            return true;
        }
        return false;
    }


}
