package com.linzi.pitpat.api.awardservice.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.util.ISelect;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.model.dto.MedalPosDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.MedalI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.model.query.MedalI18nQuery;
import com.linzi.pitpat.data.awardservice.model.vo.MedalVo;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.MedalI18nService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.entity.dto.UserMedalDto;
import com.linzi.pitpat.data.systemservice.dto.response.MedalDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@Slf4j
@RequestMapping({"/app/medal", "/h5/medal"})
@RequiredArgsConstructor
public class MedalController extends BaseAppController {

    private final UserMedalService userMedalService;
    private final MedalConfigService medalConfigService;
    private final ZnsUserService znsUserService;
    private final MedalI18nService medalI18nService;

    @PostMapping("/list")
    public Result list(@RequestBody MedalDto medalDto) {
        //查询所有的勋章
        List<MedalConfig> medalConfigList = medalConfigService.selectAllMedalConfig(null);
        Map<Long, MedalConfig> medalConfigMap = new HashMap<>();
        if (medalDto.getUserId() == null) {
            ZnsUserEntity znsUserEntity = znsUserService.findByEmail(medalDto.getEmail());
            if (znsUserEntity != null) {
                medalDto.setUserId(znsUserEntity.getId());
            }
        }
        for (MedalConfig medalConfig : medalConfigList) {
            medalConfigMap.put(medalConfig.getId(), medalConfig);
        }

        //初始化用户勋章
        userMedalService.initMedal(medalDto.getUserId());

        //查询用户勋章
        PPageUtils pageUtils = PPageUtils.startPage(medalDto.getPageNum(), medalDto.getPageSize()).doSelect(new ISelect() {
            @Override
            public List doSelect(IPage page) {
                return userMedalService.selectUserMedalByUserIdList(page, medalDto.getUserId(), medalDto.getObtain());
            }
        });
        List<UserMedalDto> userMedalList = pageUtils.getRows();
        String langCode = I18nMsgUtils.getLangCode();

        //封装返回参数
        if (!CollectionUtils.isEmpty(userMedalList)) {
            List<UserMedal> updateList = new ArrayList<>();
            for (UserMedalDto userMedal : userMedalList) {
                MedalConfig medalConfig = medalConfigMap.get(userMedal.getMedalConfigId());
                if (medalConfig == null) {
                    continue;
                }
                userMedalService.setTarget(medalConfig, userMedal);
                userMedal.setUrl(medalConfig.getUrl());
                // 查询国际化数据
                MedalI18n medalI18n = medalI18nService.findByQuery(MedalI18nQuery.builder().medalId(userMedal.getMedalConfigId()).langCode(langCode).defaultLangCode(medalConfig.getDefaultLangCode()).build());
                userMedal.setName(Objects.nonNull(medalI18n) ? medalI18n.getName() : medalConfig.getName());
                userMedal.setRemark(Objects.nonNull(medalI18n) ? medalI18n.getRemark() : medalConfig.getRemark());
                if (Objects.equals(userMedal.getObtain(), 1)) {
                    userMedal.setCurrentProcess1(userMedal.getTargetProcess1());
                    userMedal.setCurrentProcess2(userMedal.getTargetProcess2());
                }
                //封装更新数据
                UserMedal updateEntity = new UserMedal();
                updateEntity.setId(userMedal.getId());
                updateEntity.setGmtModified(ZonedDateTime.now());
                updateEntity.setTargetProcess1(userMedal.getTargetProcess1());
                updateEntity.setTargetProcess2(userMedal.getTargetProcess2());
                updateEntity.setHistoryProcess1(userMedal.getCurrentProcess1());
                updateEntity.setHistoryProcess2(userMedal.getCurrentProcess2());
                updateList.add(updateEntity);
                //转换数据
                userMedalService.convert(userMedal);
            }
            //批量更新
            userMedalService.updateBatchById(updateList);
        }

        //勋章未展示改为已展示
        userMedalService.updateIsShow(medalDto.getUserId());
        return CommonResult.success(pageUtils);
    }


    @PostMapping("/pos")
    public Result pos(@RequestBody MedalVo data) {
        ZnsUserEntity znsUserEntity = getUser(null, data.getEmail());
        userMedalService.updateUserMedalIsHomePagePosByUserId(0, 10000, znsUserEntity.getId());
        for (MedalPosDto medalPosDto : data.getData()) {
            UserMedal userMedal = userMedalService.selectUserMedalById(medalPosDto.getId());
            userMedal.setPos(medalPosDto.getPos());
            userMedal.setIsShowHomePage(1);
            userMedalService.updateUserMedalById(userMedal);
        }
        return CommonResult.success();
    }


    public static void main(String[] args) {
        MedalVo data = new MedalVo();
        List<MedalPosDto> medalPosDtos = new ArrayList<>();
        MedalPosDto medalPosDto = new MedalPosDto();
        medalPosDto.setId(42l);
        medalPosDto.setPos(1);
        medalPosDtos.add(medalPosDto);
        data.setData(medalPosDtos);
        System.out.println(data);
    }


}
