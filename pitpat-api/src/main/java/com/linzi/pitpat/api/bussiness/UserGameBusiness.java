package com.linzi.pitpat.api.bussiness;

import com.linzi.pitpat.api.dto.req.UserGameBaseReq;
import com.linzi.pitpat.api.dto.req.UserGameQueryDetailIdsReq;
import com.linzi.pitpat.api.dto.response.UserGameDetailIdsResp;
import com.linzi.pitpat.api.dto.response.UserGameFlagResp;
import com.linzi.pitpat.api.dto.response.UserGameReportRanksReq;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.NewTeamDetailRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.vo.ExpRewardVo;
import com.linzi.pitpat.data.activityservice.model.vo.NewPkMultipleConfigVo;
import com.linzi.pitpat.data.activityservice.query.NewTeamDetailRecordQuery;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.NewTeamDetailRecordService;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.CouponAwardDto;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.FetchRuleTypeEnum;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.resp.UserGameAwardDto;
import com.linzi.pitpat.data.resp.UserGameAwardResp;
import com.linzi.pitpat.data.resp.UserGameRankAwardDto;
import com.linzi.pitpat.data.systemservice.service.RegionService;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserExpConfigQuery;
import com.linzi.pitpat.data.userservice.service.UserExpConfigService;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class UserGameBusiness {

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private NewTeamDetailRecordService newTeamDetailRecordService;

    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Autowired
    private RegionService regionService;

    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    @Resource
    protected ZnsUserAccountService userAccountService;
    @Resource
    protected ZnsUserAccountDetailService userAccountDetailService;

    @Resource
    private ActivityUserScoreService activityUserScoreService;
    @Autowired
    protected UserCouponService userCouponService;
    @Resource
    private UserCouponBizService userCouponBizService;
    @Resource
    private UserLevelRuleService userLevelRuleService;
    @Resource
    private UserLevelService userLevelService;
    @Resource
    private UserExpConfigService userExpConfigService;
    @Resource
    private EntryGameplayService entryGameplayService;
    @Resource
    private MainActivityService mainActivityService;
    @Resource
    private RealPersonRunDataDetailsService realPersonRunDataDetailsService;

    public UserGameFlagResp getUserInfo(UserGameBaseReq req) {
        UserGameFlagResp useGameFlagResp = new UserGameFlagResp();
        useGameFlagResp.setUserFlag(YesNoStatus.NO.getCode());
        ZnsUserEntity znsUserEntity = znsUserService.findById(req.getUserId());
        if (DateUtil.betweenHours(znsUserEntity.getCreateTime(), ZonedDateTime.now()) <= 7 * 24) {
            useGameFlagResp.setUserFlag(YesNoStatus.YES.getCode());
        }
        //查询玩法规则
        MainActivity byId = mainActivityService.findById(req.getActivityId());
        if (Objects.isNull(byId)) return useGameFlagResp;
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(byId.getPlayId());
        //累计参数和完赛规则才返回
        if (Objects.nonNull(entryGameplay)
                && (Objects.equals(FetchRuleTypeEnum.ACCUMULATED_PARTICIPATION_DATA.getType(), entryGameplay.getFetchRule())
                || Objects.equals(FetchRuleTypeEnum.ACCUMULATED_COMPLETION_DATA.getType(), entryGameplay.getFetchRule()))) {
            useGameFlagResp.setTargetType(byId.getTargetType());
            ZnsRunActivityUserEntity runActivityUser = znsRunActivityUserService.selectByActivityIdUserId(req.getActivityId(), req.getUserId());
            if (Objects.equals(byId.getTargetType(), 1)) {
                useGameFlagResp.setRunMileageOrTime(Objects.nonNull(runActivityUser) ? runActivityUser.getRunMileage() : new BigDecimal(0));
            } else {
                useGameFlagResp.setRunMileageOrTime(Objects.nonNull(runActivityUser) ? new BigDecimal(runActivityUser.getRunTime()) : new BigDecimal(0));
            }
        }

        return useGameFlagResp;
    }

    public List<UserGameDetailIdsResp> getDetailIds(UserGameQueryDetailIdsReq req) {
        List<NewTeamDetailRecord> list = newTeamDetailRecordService.findListByQuery(
                NewTeamDetailRecordQuery.builder().activityId(req.getActivityId()).build());

        List<UserGameDetailIdsResp> userGameDetailIdsResp = new ArrayList<>();
        list.forEach(i -> {
            var gameDetailIdsResp = new UserGameDetailIdsResp();
            gameDetailIdsResp.setDetailId(i.getDetailId());
            gameDetailIdsResp.setUserId(i.getDetailUserId());
            var znsUserEntity = znsUserService.findById(i.getDetailUserId());
            var detail = znsUserRunDataDetailsService.findById(i.getDetailId());
            gameDetailIdsResp.setGender(znsUserEntity.getGender());
            gameDetailIdsResp.setAverageVelocity(detail.getAverageVelocity());
            var region = regionService.findByCountry(znsUserEntity.getCountry());
            if (Objects.nonNull(region)) {
                gameDetailIdsResp.setCountry(region.getShortCountry());
            }
            gameDetailIdsResp.setNickName(znsUserEntity.getFirstName());
            userGameDetailIdsResp.add(gameDetailIdsResp);
        });
        return userGameDetailIdsResp;
    }

    /**
     * 活动奖励配置
     *
     * @param req
     * @return
     */
    public UserGameAwardResp getReward(UserGameQueryDetailIdsReq req) {
        var znsRunActivity = znsRunActivityService.findById(req.getActivityId());
        ZnsUserEntity user = znsUserService.findById(req.getUserId());
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(req.getUserId());
        String config = znsRunActivity.getActivityConfig();
        // 活动配置
        NewPkMultipleConfigVo newPkMultipleConfigVo = JsonUtil.readValue(config, NewPkMultipleConfigVo.class);
        UserGameAwardResp award = new UserGameAwardResp();
        UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
        UserGameAwardDto unFinishAward = newPkMultipleConfigVo.getUnFinishAward();
        fillCurrencyAmount(userAccount, finishAward);
        fillCurrencyAmount(userAccount, unFinishAward);
        List<UserGameRankAwardDto> userRankAward = newPkMultipleConfigVo.getUserRankAward();
        if (!CollectionUtils.isEmpty(userRankAward)) {
            for (UserGameRankAwardDto userGameRankAwardDto : userRankAward) {
                UserGameAwardDto awardDto = userGameRankAwardDto.getAwardDto();
                fillCurrencyAmount(userAccount, awardDto);
                userGameRankAwardDto.setAwardDto(awardDto);
            }
        }
        award.setFinishAward(finishAward);
        award.setUnFinishAward(unFinishAward);
        award.setUserGameRankAwardDto(userRankAward);
        award.setCurrency(I18nConstant.buildCurrency(userAccount.getCurrencyCode()));
        //查询用户经验奖励
        ExpRewardVo expRewardVo = getUserExpRewardVo(user);
        award.setExpRewardVo(expRewardVo);
        return award;
    }

    /**
     * 获取用户经验奖励
     *
     * @param loginUser
     * @return
     */
    private ExpRewardVo getUserExpRewardVo(ZnsUserEntity loginUser) {
        ExpRewardVo result = new ExpRewardVo();
        UserLevel userLevel = userLevelService.findByUserId(loginUser.getId());
        if (userLevel == null) {
            return result;
        }
        Integer beforeExp = userLevel.getHistoryExperience();
        Integer currentExp = userLevel.getExperience();

        //获取新人任务经验(预计)
        UserExpConfigQuery userExpConfigQuery = UserExpConfigQuery.builder()
                .obtainSubType(UserExpObtainSubTypeEnum.DAILY_RACE.getCode())
                .obtainTypeList(List.of(UserExpObtainTypeEnum.EVENT_EXPERIENCE.getCode())).build();
        List<UserExpConfig> list = userExpConfigService.findList(userExpConfigQuery);
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            UserExpConfig userExpConfig = list.get(0);
            beforeExp = currentExp;
            Integer incrExperience = Objects.equals(loginUser.getMemberType(), 1) ? userExpConfig.getBasicExp() + userExpConfig.getMemberBenefit() : userExpConfig.getBasicExp();
            currentExp = currentExp + incrExperience;
        }
        result.setBeforeExp(beforeExp);
        result.setBeforeLevel(userLevel.getHistoryLevel());
        result.setCurrentExp(currentExp);
        result.setCurrentLevel(userLevel.getLevel());
        UserLevelRule beforeLevel = userLevelRuleService.findByLevel(userLevel.getHistoryLevel());
        if (beforeLevel != null) {
            result.setBeforeMaxExp(beforeLevel.getExpHigh());
        }
        UserLevelRule currentLevel = userLevelRuleService.findByLevel(userLevel.getLevel());
        if (currentLevel != null) {
            result.setCurrentMaxExp(currentLevel.getExpHigh());
        }
        return result;
    }

    private static void fillCurrencyAmount(ZnsUserAccountEntity userAccount, UserGameAwardDto userGameAwardDto) {
        if (Objects.nonNull(userGameAwardDto)) {
            List<CurrencyAmount> amountList = userGameAwardDto.getAmountList();
            if (!CollectionUtils.isEmpty(amountList)) {
                List<CurrencyAmount> resultList = amountList.stream().filter(s -> s.getCurrencyCode().equals(userAccount.getCurrencyCode())).collect(Collectors.toList());
                resultList.forEach(item -> item.setAmount(I18nConstant.currencyFormat(item.getCurrencyCode(), item.getAmount())));
                userGameAwardDto.setAmountList(resultList);
                CurrencyAmount currencyAmount = amountList.stream().filter(s -> s.getCurrencyCode().equals(userAccount.getCurrencyCode())).findFirst().orElse(null);
                if (Objects.nonNull(currencyAmount)) {
                    BigDecimal amount = I18nConstant.currencyFormat(currencyAmount.getCurrencyCode(), currencyAmount.getAmount());
                    userGameAwardDto.setAmount(amount);
                }
            }
        }
    }

    public void reportRank(UserGameReportRanksReq req) {
        Integer status = req.getStatus();
        var activityId = req.getActivityId();
        var userId = req.getUserId();
        var runActivity = znsRunActivityService.findById(activityId);
        // 没有完成发送未完赛奖励
        String activityConfig = runActivity.getActivityConfig();
        var newPkMultipleConfigVo = JsonUtil.readValue(activityConfig, NewPkMultipleConfigVo.class);
        //活动发起人其他都是影子机器人
        ZnsRunActivityUserEntity self = znsRunActivityUserService.findActivityUser(activityId, userId);
        if (YesNoStatus.YES.getCode().equals(status)) {
            //完成并且结束排名 发放奖励
            if (!CollectionUtils.isEmpty(req.getRankLists())) {
                // 完赛奖励对用户
                if (YesNoStatus.YES.getCode().equals(newPkMultipleConfigVo.getIsFinishAward())) {
                    UserGameAwardDto finishAward = newPkMultipleConfigVo.getFinishAward();
                    if (Objects.nonNull(finishAward)) {
                        sendNewPkAwardALl(finishAward, self, AccountDetailTypeEnum.PK_RUN_BONUS_C, ScoreConstant.SourceTypeEnum.source_type_24.getType());
                    }
                }
                // 排名更新
                req.getRankLists().forEach(i -> {
                    if (userId.equals(i.getUserId())) {
                        self.setRank(i.getRank());
                        self.setSubState(1);
                        self.setUserState(4);
                        self.setIsComplete(YesNoStatus.YES.getCode());
                        self.setCompleteTime(ZonedDateTime.now());
                        self.setUserType(1);
                        znsRunActivityUserService.updateById(self);
                        //排名奖励
                        // 完赛奖励对用户
                        if (YesNoStatus.YES.getCode().equals(newPkMultipleConfigVo.getIsUserRankAward())) {
                            List<UserGameRankAwardDto> userRankAward = newPkMultipleConfigVo.getUserRankAward();
                            var awardDto = userRankAward.stream().filter(k -> k.getRank().equals(i.getRank())).findFirst().orElse(null);
                            if (Objects.nonNull(awardDto)) {
                                sendNewPkAwardALl(awardDto.getAwardDto(), self, AccountDetailTypeEnum.PK_RUN_BONUS_RANK, ScoreConstant.SourceTypeEnum.source_type_26.getType());
                            }
                        }
                    } else {
                        NewTeamDetailRecord detailIdVo = newTeamDetailRecordService.findOneByQuery(
                                NewTeamDetailRecordQuery.builder().activityId(req.getActivityId()).
                                        detailUserId(i.getUserId()).userId(userId).build());
                        detailIdVo.setRank(i.getRank());
                        newTeamDetailRecordService.update(detailIdVo);
                    }
                });
            }
        } else {
            // 未完赛奖励对用户
            if (YesNoStatus.YES.getCode().equals(newPkMultipleConfigVo.getIsUnFinishAward())) {
                UserGameAwardDto unFinishAward = newPkMultipleConfigVo.getUnFinishAward();
                if (Objects.nonNull(unFinishAward)) {
                    sendNewPkAwardALl(unFinishAward, self, AccountDetailTypeEnum.PK_RUN_BONUS_UNC, ScoreConstant.SourceTypeEnum.source_type_25.getType());
                }
            }
        }
        //活动结束更新
        runActivity.setActivityState(ActivityStateEnum.FINISHED.getState());
        runActivity.setActivityEndTime(ZonedDateTime.now());
        znsRunActivityService.updateById(runActivity);
    }

    public void sendNewPkAwardALl(UserGameAwardDto finishAward, ZnsRunActivityUserEntity self, AccountDetailTypeEnum accountType, Integer type) {
        if (!CollectionUtils.isEmpty(finishAward.getAmountList())) {
            String currencyCode = userAccountService.getByUserId(self.getUserId()).getCurrencyCode();
            BigDecimal amount = finishAward.getAmountList().stream().filter(s -> Objects.equals(s.getCurrencyCode(), currencyCode)).findFirst().get().getAmount();
            if (Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) > 0) {
                // 给用户余额发送奖励
                userAccountService.increaseAmount(amount, self.getUserId(), true);
                // 新增用户奖励余额明细
                String billNo = NanoId.randomNanoId();
                ;
                ZonedDateTime tradeTime = ZonedDateTime.now();
                userAccountDetailService.addRunActivityAccountDetail0131(self.getUserId(), accountType,
                        0, 1, amount, billNo, tradeTime,
                        self.getActivityId(), self.getActivityId(), null, self.getActivityType(),
                        0L, "", null, null, null, BigDecimal.ZERO);
            }
        }
        if (Objects.nonNull(finishAward.getScore()) && finishAward.getScore() > 0) {
            Integer score = finishAward.getScore();
            activityUserScoreService.increaseAmount(score, self.getActivityId(), self.getUserId(), 1, 0, type);
        }
        if (Objects.nonNull(finishAward.getCouponAwardDto())) {
            CouponAwardDto awardDto = finishAward.getCouponAwardDto();
            userCouponBizService.sendUserCouponSource(awardDto.getCouponId(), self.getUserId(), self.getActivityId(), CouponConstant.SourceTypeEnum.source_type_6.getType(), false);
        }
    }
}
