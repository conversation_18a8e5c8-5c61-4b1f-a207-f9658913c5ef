package com.linzi.pitpat.api.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.mallservice.mananger.PayBussiness;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.manager.AwardActivityManager;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.vo.AssistantInfo;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityUserTaskVo;
import com.linzi.pitpat.data.activityservice.service.OneWeekConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.awardservice.model.request.PayRequest;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserPaypalAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.request.OneWeekJudgeStatusDto;
import com.linzi.pitpat.data.request.ParticipateInDto;
import com.linzi.pitpat.data.request.ToolsConfig;
import com.linzi.pitpat.data.resp.DrawShowResp;
import com.linzi.pitpat.data.resp.OneWeekHomeDetailDto;
import com.linzi.pitpat.data.resp.OneWeekHomeDetailResp;
import com.linzi.pitpat.data.resp.OneWeekHomePageResp;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 一周快乐跑活动接口
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/app/one/week", "/h5/one/week"})
@Slf4j
public class OneWeekController extends BaseAppController {

    @Autowired
    private ZnsRunActivityService znsRunActivityService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private RunActivityUserTaskService runActivityUserTaskService;

    @Resource
    private RedisUtil redisUtil;

    @Autowired
    private OneWeekConfigService oneWeekConfigService;

    @Autowired
    private ZnsRunActivityUserService znsRunActivityUserService;

    @Autowired
    private ActivityStrategyContext activityStrategyContext;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private ZnsUserPaypalAccountService userPaypalAccountService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    @Resource
    private PayBussiness payBussiness;
    @Autowired
    private AwardActivityManager awardActivityManager;

    /**
     * 判断参赛资格
     *
     * @param dto
     * @return
     */
    @PostMapping("/judgeStatus")
    public Result judgeStatus(@RequestBody ParticipateInDto dto) {
        ZnsUserEntity user = znsUserService.findById(dto.getUserId());
        return userAccountService.judgetStatus(dto,user);
    }


    /**
     * 参与一周快乐跑活动
     *
     * @param dto
     * @return
     */
    @PostMapping("/participateIn")
    public Result participateIn(@RequestBody ParticipateInDto dto) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfig.getConfigValue(), 0l);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        if (CollectionUtils.isEmpty(userTasks)) {
            for (int i = 0; i < 7; i++) {
                ZonedDateTime currentDate = ZonedDateTime.now();
                RunActivityUserTask runActivityUserTask = new RunActivityUserTask();
                ZonedDateTime date = DateUtil.addDays(currentDate, i);
                runActivityUserTask.setGmtCreate(date);
                runActivityUserTask.setGmtModified(date);
                runActivityUserTask.setUserId(dto.getUserId());
                runActivityUserTask.setTaskType(3); //任务类型，1：随机匹配竞技跑，2：完成指定课程，3：完成目标里程
                runActivityUserTask.setMileageTarget(znsRunActivityEntity.getRunMileage().intValue());
                runActivityUserTask.setStatus(0);
                runActivityUserTask.setActivityId(activityId);
                runActivityUserTask.setLevel(i + 1);
                runActivityUserTask.setActivityType(7);
                runActivityUserTaskService.insertOrUpdateRunActivityUserTask(runActivityUserTask);
            }
            ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(znsRunActivityEntity.getId(), znsUserEntity.getId());
            // 如果run_activity_user 为空，则报名
            if (znsRunActivityUserEntity == null) {
                activityStrategyContext.handleUserActivityState(znsRunActivityEntity.getId(), 1, znsUserEntity, null, null, false, null, null, false);
            }
        }
        return CommonResult.success();
    }

    /**
     * 一周快乐跑活动首页
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @PostMapping("/homePage")
    public Result homePage(@RequestBody ParticipateInDto dto) throws Exception {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_serven_day_tips.getCode());
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        Map<String, Object> map = JsonUtil.readValue(sysConfig.getConfigValue());
        int daysBetween = DateUtil.daysBetweenYYYYMMDD(!CollectionUtils.isEmpty(userTasks) ? userTasks.get(0).getGmtCreate() : ZonedDateTime.now(), ZonedDateTime.now()) + 1;
        Object template = map.get(daysBetween + "");
        if (template == null) {
            if (daysBetween >= 7) {
                template = map.get("7") + "";
                daysBetween = 7;
            } else {
                template = map.get("1") + "";
                daysBetween = 1;
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("firstName", znsUserEntity.getFirstName());
        ExpressionParser parser = new SpelExpressionParser();
        TemplateParserContext parserContext = new TemplateParserContext();

        String cs[] = template.toString().split("#");
        String preTitle = cs[0];// 前置文案

        if (preTitle.endsWith(",") || preTitle.endsWith("，")) {
            preTitle = preTitle.substring(0, preTitle.length() - 1);
        }
        String rearTitle = "#" + cs[1]; // 后置文案

        rearTitle = parser.parseExpression(rearTitle, parserContext).getValue(params, String.class);
        RunActivityUserTask runActivityUserTask = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserIdLevel(activityId, znsUserEntity.getId(), daysBetween);

        List<RunActivityUserTaskVo> userTaskList = new ArrayList<>();
        for (RunActivityUserTask userTask : userTasks) {
            RunActivityUserTaskVo vo = new RunActivityUserTaskVo();
            BeanUtils.copyProperties(vo, userTask);
            userTaskList.add(vo);
        }
        OneWeekHomePageResp resp = new OneWeekHomePageResp();
        resp.setPreTitle(preTitle);
        resp.setRearTitle(rearTitle);

        if (daysBetween == 1) {
            resp.setPreTitle(preTitle + "," + znsUserEntity.getFirstName());
            resp.setRearTitle(rearTitle.replaceAll(znsUserEntity.getFirstName(), ""));
        }
        resp.setTodayNum(daysBetween);
        resp.setUserTasks(userTaskList);
        resp.setActivityId(znsRunActivityEntity.getId());

        resp.setMileageTarget(getNotFinishTarget(userTasks, daysBetween));

        Long routeIds[] = new Long[]{101l, 201l, 301l, 401l};
        Random random = new Random();
        int index = random.nextInt(routeIds.length);

        resp.setRouteId(routeIds[index]);
        if (runActivityUserTask != null) {
            resp.setTaskId(runActivityUserTask.getId());
        }
        if (ZonedDateTime.now().toInstant().toEpochMilli() >= znsRunActivityEntity.getActivityStartTime().toInstant().toEpochMilli()
                && ZonedDateTime.now().toInstant().toEpochMilli() <= znsRunActivityEntity.getActivityEndTime().toInstant().toEpochMilli()) {
            resp.setActivityStarting(1); //活动结束
        }
        // sys_config配置
        resp.setConfig(map);

        resp.setIconImage(map.get(daysBetween + "_image") + "");

        SysConfig configValue = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.chat_robot_user_ids.getCode());
        String usreIds[] = configValue.getConfigValue().split(",");
        ZnsUserEntity znsUserEntityCustome = znsUserService.findById(MapUtil.getLong(usreIds[0]));
        AssistantInfo assistantInfo = new AssistantInfo(znsUserEntityCustome.getId() + "", znsUserEntityCustome.getFirstName());
        resp.setAssistantInfo(assistantInfo);
        String key = "one_week_money_" + znsUserEntity.getId();
        Integer value = redisUtil.get(key);

        List<ToolsConfig> toolsConfigs = JsonUtil.readList(map.get("toolsConfig"), ToolsConfig.class);
        ToolsConfig toolsConfig = toolsConfigs.get(2);
        if (value != null) {
            toolsConfig.setUnreadNumber(value);
        } else {
            if (userAccountService.isFinished(userTasks)) {
                toolsConfig.setUnreadNumber(1);
                redisUtil.set(key, 0, 30, TimeUnit.DAYS);
            }
        }

        map.put("toolsConfig", toolsConfigs);
        resp.setConfig(map);

        RunActivityUserTask runActivityUserTask7 = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserIdLevel(activityId, znsUserEntity.getId(), 7);
        resp.setRunEndTime(runActivityUserTask7 == null ? DateUtil.addDays(ZonedDateTime.now(), 7) : DateUtil.endOfDate(runActivityUserTask7.getGmtCreate()));
        return CommonResult.success(resp);
    }


    public BigDecimal getNotFinishTarget(List<RunActivityUserTask> userTasks, Integer daysBetween) {
        BigDecimal mileageTarget = BigDecimal.ZERO;
        for (RunActivityUserTask userTask : userTasks) {
            if (daysBetween >= userTask.getLevel()) {
                BigDecimal temp = BigDecimalUtil.subtract(userTask.getMileageTarget(), userTask.getRunMileage());
                mileageTarget = mileageTarget.add(temp.compareTo(BigDecimal.ZERO) > 0 ? temp : BigDecimal.ZERO);
            }
        }
        return mileageTarget;
    }


    @PostMapping("/start")
    public Result start(@RequestBody ParticipateInDto dto) throws Exception {
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);
        RunActivityUserTask runActivityUserTask = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserIdLevel(activityId, znsUserEntity.getId(), 7);
        if (ZonedDateTime.now().toInstant().toEpochMilli() > DateUtil.endOfDate(runActivityUserTask.getGmtCreate()).toInstant().toEpochMilli()) {
            return CommonResult.success(new OneWeekJudgeStatusDto(0));
        } else {
            return CommonResult.success(new OneWeekJudgeStatusDto(1));
        }
    }

    @PostMapping("/homeDetail")
    public Result homeDetail(@RequestBody ParticipateInDto dto) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);

        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);
        List<OneWeekHomeDetailDto> oneWeekHomeDetailDtos = runActivityUserTaskService.selectRunDataByActivityIdUserId(znsRunActivityEntity.getId(), znsUserEntity.getId());
        OneWeekHomeDetailResp resp = new OneWeekHomeDetailResp();
        for (OneWeekHomeDetailDto oneWeekHomeDetailDto : oneWeekHomeDetailDtos) {
            oneWeekHomeDetailDto.setIsRepair(DateUtil.isSameDay(oneWeekHomeDetailDto.getGmtCreate(), oneWeekHomeDetailDto.getTaskTime()) ? 0 : 1);
        }
        resp.setUserTasks(oneWeekHomeDetailDtos);
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_serven_day_tips.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());

        int daysBetween = DateUtil.daysBetween(znsRunActivityEntity.getActivityStartTime(), ZonedDateTime.now()) + 1;
        resp.setDayNum(daysBetween);
        resp.setImgSource(data);

        return CommonResult.success(resp);
    }


    @PostMapping("/drawShow")
    public Result drawShow(@RequestBody ParticipateInDto dto) {
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);

        int finished = 0;
        int notfinish = 0;

        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_serven_day_tips.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        BigDecimal amount = MapUtil.getBigDecimal(data.get("award"), new BigDecimal(80));

        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        for (RunActivityUserTask userTask : userTasks) {
            if (Objects.equals(userTask.getStatus(), 1)) {
                finished = finished + 1;
            } else {
                notfinish = notfinish + 1;
            }
        }

        DrawShowResp resp = new DrawShowResp();
        resp.setImgSource(data);
        resp.setFinished(finished);
        resp.setNotFinish(notfinish);
        resp.setAmount(amount);
        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(activityId, dto.getUserId());
        if (znsRunActivityUserEntity != null) {
            resp.setIsComplete(znsRunActivityUserEntity.getIsComplete());
            if (Objects.equals(znsRunActivityUserEntity.getIsComplete(), 1)) {
                resp.setAmount(BigDecimal.ZERO);
            }
        }
        return CommonResult.success(resp);
    }


    @PostMapping("/doDraw")
    public Result doDraw(@RequestBody ParticipateInDto dto) {
        Integer appVersion = getAppVersion();
        Integer appType = getAppType();
        if (Integer.valueOf(203).equals(appVersion) && Integer.valueOf(1).equals(appType)) {
            ZnsUserEntity loginUser = getLoginUser();
            PayRequest request = new PayRequest();
            request.setPayPassword(dto.getPayPassword());
            request.setTaxRate(dto.getTaxRate());
            request.setServiceRate(dto.getServiceRate());
            request.setAmount(dto.getAmount());
            return payBussiness.cash(request, loginUser);
        }
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);

        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_serven_day_tips.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        BigDecimal amount = MapUtil.getBigDecimal(data.get("award"), new BigDecimal(80));
        ;
        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        if (!userAccountService.isFinished(userTasks)) {
            amount = BigDecimal.ZERO;
        }
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            RunActivityUserTask runActivityUserTask = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserIdLevel(activityId, znsUserEntity.getId(), 7);
            if (runActivityUserTask != null) {
                if (DateUtil.daysBetweenYYYYMMDD(runActivityUserTask.getGmtCreate(), ZonedDateTime.now()) > 15) {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "out of draw time");
                }
            }

            ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserService.selectByActivityIdUserId(activityId, dto.getUserId());
            if (znsRunActivityUserEntity != null && Arrays.asList(1, 2).contains(znsRunActivityUserEntity.getIsComplete())) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "提现失败,已经申请过提现");
            }
            //查询账户
            Result result = userAccountService.checkPassword(dto.getUserId(), dto.getPayPassword(), false);
            if (Objects.nonNull(result)) {
                if (result.getCode().equals(UserError.PAY_PASSWORD_ERROR.getCode())) {
                    result.setData(null);
                }
                return result;
            }

            awardActivityManager.handleTeamRunAward3D(amount, znsUserEntity.getId(), activityId, AccountDetailTypeEnum.ONE_WEEK_ACTIVITY, 0, activityId);
            PayRequest request = new PayRequest();
            request.setAmount(amount);
            request.setUserId(znsUserEntity.getId());
            request.setPayType(0);
            request.setServiceRate(dto.getServiceRate());
            request.setTaxRate(dto.getTaxRate());
            request.setActivityId(activityId);

            //获取绑定账户
            String payAccount = userPaypalAccountService.getPayAccount(znsUserEntity.getId());
            if (!StringUtils.hasText(payAccount)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "请先绑定账户");
            }
            RLock lock = redissonClient.getLock(RedisConstants.USER_CASH + znsUserEntity.getId());
            try {
                if (lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                    Result resp = userAccountService.withdrawalApply(request.getAmount(), znsUserEntity.getId(), payAccount, request);
                    if (CommonError.SUCCESS.getCode().equals(resp.getCode())) {
                        znsRunActivityUserEntity.setIsComplete(2);                      //设置为提现中
                        znsRunActivityUserService.updateById(znsRunActivityUserEntity);
                    }
                    return resp;
                } else {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "提现中，请勿重复点击");
                }
            } catch (Exception e) {
                log.error("提现异常，e={}", e.getMessage(), e);
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "提现失败");
            }

        }
        return CommonResult.success("提现申请成功");
    }


    @PostMapping("/everyDayPop")
    public Result everyDayPop(@RequestBody ParticipateInDto dto) throws Exception {
        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);

        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
        RunActivityUserTaskVo userTaskTemp = new RunActivityUserTaskVo();
        userTaskTemp.setIsUnlock(0);

        for (RunActivityUserTask userTask : userTasks) {
            if (Objects.equals(userTask.getIsUnlock(), 1)) {
                runActivityUserTaskService.updateRunActivityUserTaskIsUnlockById(0, userTask.getId()); //更新弹窗为0
                RunActivityUserTaskVo vo = new RunActivityUserTaskVo();
                BeanUtils.copyProperties(vo, userTask);
                if (userTask.getTaskTime() != null) {
                    int daysBetweenCurr = DateUtil.daysBetweenYYYYMMDD(userTasks.get(0).getGmtCreate(), userTask.getTaskTime()) + 1; // 如果是第7天
                    if (daysBetweenCurr != 7) {
                        if (Objects.equals(daysBetweenCurr, userTask.getLevel())) {   // 如果是当前跑
                            daysBetweenCurr = 1;
                        } else if (daysBetweenCurr > userTask.getLevel()) {           //如果是补跑
                            daysBetweenCurr = -1;
                        }
                    }
                    vo.setFinishedStatus(daysBetweenCurr); //   -1 补充卡完成任务 0 当前完成任务 第7 天完成任务
                }
                userTaskTemp = vo;

                ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity = znsUserRunDataDetailsService.selectByUserIdActivityId(userTask.getUserId(), userTask.getActivityId());
                if (znsUserRunDataDetailsEntity != null) {
                    userTaskTemp.setCurrRunMileage(znsUserRunDataDetailsEntity.getRunMileage());
                }
            }
        }
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_serven_day_tips.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        userTaskTemp.setModalBgImage(data.get("modalBgImage") + "");
        userTaskTemp.setModalIconImage(data.get("modalIconImage") + "");
        int daysBetween = DateUtil.daysBetweenYYYYMMDD(!CollectionUtils.isEmpty(userTasks) ? userTasks.get(0).getGmtCreate() : ZonedDateTime.now(), ZonedDateTime.now()) + 1;
        if (daysBetween >= 7 && userAccountService.isFinished(userTasks)) {
            userTaskTemp.setModalIconImage(data.get("modalIconImage1") + "");
        }
        userTaskTemp.setMileageTarget(getNotFinishTarget(userTasks, daysBetween).intValue());
        return CommonResult.success(userTaskTemp);
    }

    @PostMapping("/copywrite")
    public Result copywrite(@RequestBody ParticipateInDto dto) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_serven_day_tips.getCode());

        SysConfig sysConfigActivity = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfigActivity.getConfigValue(), 0l);
        ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityService.selectActivityById(activityId);


        Integer activityStatus = 0;
        if (ZonedDateTime.now().toInstant().toEpochMilli() >= znsRunActivityEntity.getActivityStartTime().toInstant().toEpochMilli()
                && ZonedDateTime.now().toInstant().toEpochMilli() <= znsRunActivityEntity.getActivityEndTime().toInstant().toEpochMilli()) {
            activityStatus = 1;
        }

        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        data.put("activityStatus", activityStatus);

        ZnsUserEntity znsUserEntity = znsUserService.findById(dto.getUserId());
        if (znsUserEntity != null) {
            List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByActivityIdUserId(activityId, znsUserEntity.getId());
            if (!CollectionUtils.isEmpty(userTasks)) {
                if (ZonedDateTime.now().toInstant().toEpochMilli() > DateUtil.endOfDate(userTasks.get(6).getGmtCreate()).toInstant().toEpochMilli()) {
                    data.put("userStatus", 0);
                } else {
                    data.put("userStatus", 1);
                }
            }
        }
        return CommonResult.success(data);
    }

    @PostMapping("/registerUsers")
    public Result registerUsers(@RequestBody ParticipateInDto dto) {
        String[] userIds = dto.getUserIds().split(",");
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.one_week_run_activity_id.getCode());
        Long activityId = MapUtil.getLong(sysConfig.getConfigValue(), 0l);
        for (String userIdStr : userIds) {
            try {
                Long userId = MapUtil.getLong(userIdStr, 0l);
                ZnsUserEntity user = znsUserService.findById(userId);
                String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
                log.info("删除接口  email = " + user.getEmailAddressEn());
                oneWeekConfigService.insertOneWeekConfigByActivityIdUserId(activityId, user.getId());
                //token保存一年
                redisUtil.delete(key);
            } catch (Exception e) {
                log.error("异常", e);
            }
        }
        return CommonResult.success();
    }


    public static void main(String[] args) {
        List<String> activityRules = Arrays.asList("1. The system can activate it by adding a piece of \"lucky magic shard\" every day, and it will automatically activate the 1km running mileage on the day. If it is completed, the running requirement (1km) of the day will be completed.",
                "2. Within seven days of the event, you can \"refill the card\" to reactivate the \"lucky magic shards\" that have not been activated on the day, and run to complete it. This is valid for the event requirements.",
                "3. The activity period is one week, and the start and end time is the first activation of \"Lucky Magic Fragment\", which will be the activity time for 7 consecutive days.",
                "4. During the start and end time of the event, activate seven \"Lucky Magic Fragments\" and complete the 1km run every day for seven days. Congratulations, you have completed the event, and you can withdraw cash in the \"Wallet\" on the homepage of the event. The withdrawal deadline is 30 days after you complete the activity. ",
                "5. After the event, log in again to enter the APP. However, the activity window still has a shortcut entry in the APP, and you can continue to experience the activity. It is only a way of running, and the data is not used as an activity result.",
                "6. After the event ends, the event will continue to be launched at a later date, and more interesting gameplay will be added at that time.",
                "7. You cannot experience other content of the APP during your participation in the activity. You can cancel your participation in the current activity through customer service and enter the APP normally.",
                "8. After the event is over, you can enter the APP and place the event entry in the APP, you can still continue to run through the event, but you will not enjoy the event rewards. Of course, the possibility of developing this feature in the future is not ruled out. It will be a more interesting running game. You can also send your wishes to our customer service, because PitPat is always with you.",
                "9. When you encounter any difficulties and questions, please contact PitPat customer service and we will answer and solve the problem for you.");

        List<String> activityIntroduction = Arrays.asList("Dear Friends, Merry Christmas! This invitation is from Santa Claus, who is flying towards you with his reindeers and carrying out presents on their way! Ho Ho Ho…",
                "1. Join us to One Week Happy Run and complete relevant requirements then you can get a reward of ",
                "2.The event duration is 7 days, PitPat will activate 1 Lucky Magic Fragment for you each day and you just need to enjoy the run. By the end of the event, you have to complete running 7 miles then you could unlock your rewards and withdraw it",
                "3. You would have make-up chances if you miss the run. Activate the Lucky Magic Fragment before the game ends and complete the miles, it counts too!",
                "4. You will have to withdraw your rewards within 15 days",
                "5. You will have 15 days to access to the event after it ended. We would like to kindly remind you that you will have to withdraw your rewards within 15days. ",
                "6. PitPat is always dedicated to serve you a better and joyful running experience");
        Map<String, Object> data = new HashMap<>();

        data.put("activityRules", activityRules);
        data.put("activityIntroduction", activityIntroduction);
        data.put("reward", "$80");

        data.put("1", "Hello,#{[firstName]} PitPat would like to invite to run together.");
        data.put("2", "Sunshine,#{[firstName]} Day two is a sailing day.");
        data.put("3", "Moonlight,#{[firstName]}Your running sound pierced the tranquility of the night.");
        data.put("4", "Habits,#{[firstName]}'s Day four running becomes your new habit.");
        data.put("5", "Healthy,#{[firstName]}'s fifth day persistant running is so healthy and beautiful.");
        data.put("6", "Come on,#{[firstName]} today makes tomorrow even more exciting.");
        data.put("7", "Good luck,#{[firstName]} has successfully completed the seven-day happy run.");

        data.put("1_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/iOc10OtqLyqS9009.png");
        data.put("2_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/i7C10OtqMhKi4052.png");
        data.put("3_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/iIw10OtqMo6S5784.png");
        data.put("4_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/iPq10OtqMKtS6273.png");
        data.put("5_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/ism10OtqMSAU0412.png");
        data.put("6_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/202212/iOo10OHCEYWe4733.png");
        data.put("7_image", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/iOa10OtqNfTG0701.png");

        data.put("backgroundImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221206/iQd10Osm76gJ6340.png");

        data.put("backgroundAudio", "https://pitpat-oss.s3.us-east-2.amazonaws.com/audio/merry_bg.mp3");


        List<ToolsConfig> toolsImage = new ArrayList<>();

        toolsImage.add(new ToolsConfig("https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iWd10OnUZX2Z5784.png", 0, ""));
        toolsImage.add(new ToolsConfig("https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/i7j10OnV0eSD0430.png", 0, "https://yh5.pitpatfitness.com/#/christmas/page"));
        toolsImage.add(new ToolsConfig("https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iK810OnV0FVD3513.png", 0, ""));
        toolsImage.add(new ToolsConfig("https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iaH10OnV0YqG4850.png", 0, ""));

        data.put("toolsConfig", toolsImage);


        Map<String, String> welcomeImgSource = new HashMap<>();
        welcomeImgSource.put("weekRunWelcomeBg", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/ikX10OmTIYKr7351.png");
        welcomeImgSource.put("weekRunWelcomeBack", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iNB10OmU3J286004.png");
        welcomeImgSource.put("weekRunParticipateButton", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iy010OmU3K6T4300.png");
        welcomeImgSource.put("weekRunDecorate", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iU410OmU67DF2573.png");

        data.put("welcomeImgSource", welcomeImgSource);

        data.put("uncompletedDescription", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221209/iRr10OvD5pvB0736.png");
        data.put("completedDescription", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221213/iVl10OzXtnhG9238.png");
        data.put("imgBg", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/iRi10OnV6BGf7251.png");
        data.put("opnButton", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/iN610OnVb9x90250.png");


        Map<String, String> introductorySource = new HashMap<>();
        introductorySource.put("introductoryTitle", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iTl10OnV0gjI5618.png");
        introductorySource.put("unclosedSubtitle", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/idd10OnV0y9e9170.png");
        introductorySource.put("closedSubtitle", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iU910OnV1pDz5026.png");
        introductorySource.put("firstButton", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iNn10OnUBz0K5963.png");
        introductorySource.put("secondButton", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iKo10OnUEL1I5728.png");
        introductorySource.put("introductoryBg", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/ioS10OnUETPm0171.png");
        introductorySource.put("mascot", "https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iUd10OnUEVQm4622.png");
        data.put("introductorySource", introductorySource);
        data.put("award", 80);


        data.put("backIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221208/ieQ10Ouvg5X28551.png");
        data.put("backIconDraw", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221208/iyq10OuvaUh03584.png");
        data.put("disabledOpnButton", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221208/iwZ10OuxLFzA9309.png");
        data.put("cardBg", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/ieH10OnVhZ3u9064.png");
        data.put("distanceIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/iCL10OnVkmdN9041.png");
        data.put("runningTimeIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/iA410OnVknA52758.png");
        data.put("caloriesIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/iCF10OnVlZsa3022.png");
        data.put("fatBurningIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/i2210OnVkojl0968.png");
        data.put("stepsIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221207/iz510OtsjHCE5395.png");
        data.put("paceAvgIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221202/iQl10OnVm74k0858.png");

        data.put("completedIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221205/iaR10OrbFd4b9002.png");
        data.put("uncompletedIcon", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221205/iHe10OrbFc645722.png");
        data.put("completedDesc", "You have obtained today's magic fragments");
        data.put("uncompletedDesc", "You did not receive today's Lucky Magic Shard.");


        data.put("modalBgImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221206/i7B10OskHIN86850.png");
        data.put("modalIconImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221204/iLG10Oq5Q8Vc1721.png");
        data.put("modalIconImage1", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221204/ijF10Oq5PMTv6884.png");


        data.put("withdrawingDescription", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221209/icF10OvDr1WP2936.png");
        data.put("afterWithdrawalDescription", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221209/iTk10OvDtHwW9139.png");

        data.put("endActivityPopDesc2", "Congratulations, you have withdraw the rewards of $80 from \"Happy Christmas Run\" successfully. The event is no longer available, Thank you so much for your participation!");
        data.put("endActivityPopDesc3", "Hi PitPater, You didn't complete your withdrawal in 15 days which is the time limit, therefore, you are no longer able to redeem it. The event is no longer available, Thank you for your participation");
        data.put("endActivityPopDesc4", "Hi PitPater, You have not met the requirements to win the rewards. The event is no longer available, Thank you for your participation");
        data.put("modalBgImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221206/i7B10OskHIN86850.png");
        data.put("modalIconImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/20221204/iLG10Oq5Q8Vc1721.png");

        System.out.println(JsonUtil.writeString(data));

    }

}

