package com.linzi.pitpat.api.activityservice.controller;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.manager.RunActivityUserTaskManager;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.service.OneWeekConfigService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.awardservice.model.request.PayRequest;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserPaypalAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.activity.OneWeekConfig;
import com.linzi.pitpat.data.entity.po.task.GetTaskInfoPo;
import com.linzi.pitpat.data.request.ParticipateInDto;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.PopRecord;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.vo.task.TaskInfoVo;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/8 13:49
 */
@RestController
@RequestMapping({"/app/festival", "/h5/festival"})
@Slf4j
@RequiredArgsConstructor
public class FestivalActivityController extends BaseAppController {
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private ZnsUserAccountService userAccountService;
    private final RedissonClient redissonClient;
    @Resource
    private ZnsUserPaypalAccountService userPaypalAccountService;
    @Resource
    private PopRecordService popRecordService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private OneWeekConfigService oneWeekConfigService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private RunActivityUserTaskManager runActivityUserTaskManager;

    @PostMapping("/withdraw")
    public Result withdraw(@RequestBody ParticipateInDto dto) {
        ZnsUserEntity user = getLoginUser();
        //判断是否提现中
        List<RunActivityUserTask> cashTasks = runActivityUserTaskService.selectRunActivityUserTaskByUserIdAndTypeStatus(user.getId(), 7, 1, 2);
        if (!CollectionUtils.isEmpty(cashTasks)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("payment.status.withdrawing"));
        }
        List<RunActivityUserTask> userTasks = runActivityUserTaskService.selectRunActivityUserTaskByUserIdAndTypeStatus(user.getId(), 7, 1, 1);
        if (CollectionUtils.isEmpty(userTasks)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("payment.amount.insufficient"));
        }
        BigDecimal amount = userTasks.stream().map(RunActivityUserTask::getAward).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return CommonResult.fail(I18nMsgUtils.getMessage("payment.amount.insufficient"));
        }
        runActivityUserTaskManager.awardReceived(userTasks);
        //查询账户
        Result result = userAccountService.checkPassword(dto.getUserId(), dto.getPayPassword(), false);
        if (Objects.nonNull(result)) {
            if (result.getCode().equals(UserError.PAY_PASSWORD_ERROR.getCode())) {
                result.setData(null);
            }
            return result;
        }

        PayRequest request = new PayRequest();
        request.setAmount(amount);
        request.setUserId(user.getId());
        request.setPayType(0);
        request.setServiceRate(dto.getServiceRate());
        request.setTaxRate(dto.getTaxRate());
        request.setActivityId(userTasks.get(0).getActivityId());

        //获取绑定账户
        String payAccount = userPaypalAccountService.getPayAccount(user.getId());
        if (!StringUtils.hasText(payAccount)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("userAccount.bind"));
        }
        RLock lock = redissonClient.getLock(RedisConstants.USER_CASH + user.getId());
        try {
            if (!lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
            }
            Result resp = userAccountService.withdrawalApply(request.getAmount(), user.getId(), payAccount, request);
            if (CommonError.SUCCESS.getCode().equals(resp.getCode())) {
                for (RunActivityUserTask userTask : userTasks) {
                    //设置为提现中
                    userTask.setAwardStatus(2);
                    runActivityUserTaskService.update(userTask);
                }
            }
            return resp;
        } catch (Exception e) {
            log.error("提现异常", e);
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
    }

    @PostMapping("/getTaskInfo")
    public Result getTaskInfo(@RequestBody GetTaskInfoPo po) {
        if (Objects.isNull(po.getId())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        RunActivityUserTask userTask = runActivityUserTaskService.findById(po.getId());
        if (Objects.isNull(userTask)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        TaskInfoVo vo = new TaskInfoVo();
        Long userId = userTask.getUserId();
        BeanUtils.copyProperties(userTask, vo);
        PopRecord popRecord = popRecordService.selectPopRecordByTaskId(userTask.getUserId(), po.getId(), userTask.getLevel());
        String sysConfig = sysConfigService.selectConfigByKey(ConfigKeyEnums.foo_activity_config.getCode());
        Map<String, Object> jsonObject = JsonUtil.readValue(sysConfig);
        Map<String, Object> pop = JsonUtil.readValue(jsonObject.get("pop"));
        vo.setIsPoP(0);
        if (Objects.nonNull(popRecord)) {
            return CommonResult.success(vo);
        }
        //查询是否提前报名activityId, znsRunActivityEntity.getActivityStartTime(), userId, 3
        ZnsRunActivityEntity znsRunActivityEntity = runActivityService.selectActivityById(userTask.getActivityId());
        ZonedDateTime earlyTime = DateUtil.formateDate(jsonObject.get("earlyTime") + "", DateUtil.YYYY_MM_DD_HH_MM_SS);
        OneWeekConfig oneWeekConfig = oneWeekConfigService.selectOneWeekConfigByActivityUserIdType(userTask.getActivityId(),
                earlyTime, userTask.getUserId(), 3);
        if (userTask.getStatus() == 1) {
            vo.setIsPoP(1);
            try {
                Map<String, Object> leverPop = JsonUtil.readValue(pop.get(userTask.getLevel() + "earlyEnrolling"));
                log.info("=====leverPop======" + JsonUtil.writeString(leverPop));
                if (Objects.isNull(oneWeekConfig) || Objects.isNull(leverPop)) {     //如果没有提前报名 或者 没有提前报名的奖励配置
                    leverPop = JsonUtil.readValue(pop.get(userTask.getLevel()));
                    log.info("new  leverPop = " + JsonUtil.writeString(leverPop));
                }
                vo.setPopTitle(MapUtil.getString(leverPop.get("popTitle")));
                vo.setPopContent(MapUtil.getString(leverPop.get("popContent")));
                vo.setBackground(MapUtil.getString(leverPop.get("background")));
                popRecordService.addPop(userId, userTask.getLevel(), 2, po.getId());
            } catch (Exception e) {
                log.error("activity_config 解析失败", e);
            }

        } else {
            Long detailsId = po.getDetailsId();
            Map<String, Object> leverPop = JsonUtil.readValue(pop.get("unComplete"));
            if (Objects.isNull(leverPop)) {
                log.info("leverPop is null userId=" + userId);
                return CommonResult.success(vo);
            }
            if (Objects.isNull(detailsId)) {
                log.info("detailsId is null userId = " + userId);
                vo.setIsPoP(0);
            } else if (detailsId == 0) {
                log.info("detailsId is 0 userId = " + userId);
                vo.setPopTitle(MapUtil.getString(leverPop.get("popTitle")));
                vo.setPopContent(MapUtil.getString(leverPop.get("popContent")));
                vo.setBackground(MapUtil.getString(leverPop.get("background")));
                popRecordService.addPop(userId, 0, 2, po.getId());
                vo.setIsPoP(1);
            } else {
                //查询完成状态
                ZnsRunActivityUserEntity activityUser = runActivityUserService.findAllActivityUserByRunDetailId(detailsId);
                if (Objects.isNull(activityUser) || activityUser.getIsComplete() == 0) {
                    log.info(" not finished task userId = " + userId);
                    vo.setPopTitle(MapUtil.getString(leverPop.get("popTitle")));
                    vo.setPopContent(MapUtil.getString(leverPop.get("popContent")));
                    vo.setBackground(MapUtil.getString(leverPop.get("background")));
                    popRecordService.addPop(userId, 0, 2, po.getId());
                    vo.setIsPoP(1);
                } else {
                    log.info("not reason userId  = " + userId);
                }
            }
        }
        return CommonResult.success(vo);
    }
}
