package com.linzi.pitpat.api.userservice.manager;

import com.linzi.pitpat.api.equipment.converter.UserEquipmentConverter;
import com.linzi.pitpat.api.userservice.converter.EquipmentQualityAuditConverter;
import com.linzi.pitpat.api.userservice.dto.request.ExtraQualityDetailReqDto;
import com.linzi.pitpat.api.userservice.dto.request.ExtraQualityReqDto;
import com.linzi.pitpat.api.userservice.dto.request.HealthDeviceBindReqDto;
import com.linzi.pitpat.api.userservice.dto.response.CheckExtraQualityRespDto;
import com.linzi.pitpat.api.userservice.dto.response.ExtraQualityDetailRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserDeviceListRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserDeviceNewVersionListRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserVirtualDeviceListRespDto;
import com.linzi.pitpat.api.userservice.dto.response.api.DeviceSimpleResponseDto;
import com.linzi.pitpat.api.userservice.model.vo.DeviceVo;
import com.linzi.pitpat.api.userservice.model.vo.HealthDeviceVo;
import com.linzi.pitpat.api.userservice.model.vo.SportDeviceNewVo;
import com.linzi.pitpat.api.userservice.model.vo.SportDeviceVo;
import com.linzi.pitpat.api.userservice.model.vo.VirtualDeviceVo;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentQualityBiz;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillMeasureEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentActivateRecordDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentBrandConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityAuditDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityDetailDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentRunDataDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillBuzzerSupport;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillNfcLog;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillWhiteListDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.UserEquipmentShareDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsEquipmentProductionBatchEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.query.EquipmentQualityDetailQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.EquipmentRunDataQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillAppearanceConfigQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillBuzzerSupportQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillWhiteListQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.UserEquipmentShareQuery;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentRunDataVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.VirtualEquipmentImageVo;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentActivateRecordService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentBrandConfigService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigMoreService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityAuditService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityDetailService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentRunDataService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAppearanceConfigService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillBuzzerSupportService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillNfcLogService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillWhiteListService;
import com.linzi.pitpat.data.equipmentservice.service.UserEquipmentShareService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.enums.SystemConstant;
import com.linzi.pitpat.data.systemservice.model.vo.EquipmentQualityDayConfigVo;
import com.linzi.pitpat.data.systemservice.model.vo.HealthDeviceAppearanceDrawingVo;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.query.UserEquipmentQuery;
import com.linzi.pitpat.data.userservice.model.vo.AppBaseInfoVo;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.annotation.RedisLock;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums.HEALTH_DEVICE_APPEARANCE_DRAWING;

/**
 * 用户设备 api Manager类
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserDeviceApiManager {

    private final ZnsUserEquipmentService userEquipmentService;
    private final TreadmillNfcLogService treadmillNfcLogService;
    private final ZnsTreadmillService znsTreadmillService;
    private final ZnsEquipmentProductionBatchService znsEquipmentProductionBatchService;
    private final EquipmentBrandConfigService equipmentBrandConfigService;
    private final ISysConfigService sysConfigService;
    private final RedissonClient redissonClient;
    private final EquipmentConfigMoreService equipConfigMoreService;
    private final EquipmentQualityService equipmentQualityService;
    private final EquipmentQualityAuditService equipmentQualityAuditService;
    private final EquipmentQualityDetailService equipmentQualityDetailService;
    private final EquipmentActivateRecordService equipmentActivateRecordService;
    private final ISysConfigService iSysConfigService;
    private final EquipmentQualityBiz equipmentQualityBiz;
    private final EquipmentQualityAuditConverter equipmentQualityAuditConverter;
    private final TreadmillBuzzerSupportService treadmillBuzzerSupportService;
    private final RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    private final UserEquipmentShareService userEquipmentShareService;
    private final UserEquipmentConverter userEquipmentConverter;

    private final UserTaskBizService userTaskBizService;

    private final EquipmentRunDataService equipmentRunDataService;
    private final TreadmillAppearanceConfigService treadmillAppearanceConfigService;
    private final TreadmillWhiteListService treadmillWhiteListService;

    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;


    /**
     * 查询用户设备列表
     *
     * @param userId
     */
    public UserDeviceListRespDto findUserDevices(Long userId, AppBaseInfoVo appBaseInfoVo) {
        Integer appType = appBaseInfoVo.getAppType();
        Integer appVersion = appBaseInfoVo.getAppVersion();
        List<ZnsUserEquipmentEntity> list = userEquipmentService.selectByUserId(userId);
        UserDeviceListRespDto respDto = new UserDeviceListRespDto();
        if (CollectionUtils.isEmpty(list)) {
            return respDto;
        }
        return converterDeviceList(list, appType, appVersion);
    }


    /**
     * 转换返回结果
     *
     * @param list
     * @return
     */
    private UserDeviceListRespDto converterDeviceList(List<ZnsUserEquipmentEntity> list, Integer appType, Integer appVersion) {
        UserDeviceListRespDto result = new UserDeviceListRespDto();
        Map<Integer, List<ZnsUserEquipmentEntity>> map = list.stream().collect(Collectors.groupingBy(ZnsUserEquipmentEntity::getDeviceCategory));
        //健康设备
        List<HealthDeviceVo> healths = getHealthDeviceVos(appType, map.get(DeviceConstant.DeviceCategoryEnum.CATEGORY_2.code));
        result.setHealths(healths);
        //运动设备
        List<SportDeviceVo> sports = getSportDeviceVos(map.get(DeviceConstant.DeviceCategoryEnum.CATEGORY_1.code), appVersion);
        result.setSports(sports);
        return result;
    }

    /**
     * 组装运动设备
     *
     * @param sportEntities
     * @return
     */
    private List<SportDeviceVo> getSportDeviceVos(List<ZnsUserEquipmentEntity> sportEntities, Integer appVersion) {
        List<SportDeviceVo> sports = new ArrayList<>();
        if (CollectionUtils.isEmpty(sportEntities)) {
            return sports;
        }
        //NFC
        List<String> bluetoothMacs = sportEntities.stream().map(ZnsUserEquipmentEntity::getEquipmentAddress).distinct().toList();
        List<TreadmillNfcLog> treadmillNfcLogs = treadmillNfcLogService.findAllByBluetoothMacs(bluetoothMacs);
        Map<String, TreadmillNfcLog> nfcMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(treadmillNfcLogs)) {
            nfcMap = treadmillNfcLogs.stream().collect(Collectors.toMap(TreadmillNfcLog::getBluetoothMac, Function.identity(), (x, y) -> x));
        }
        //设备
        List<Long> equipmentIds = sportEntities.stream().map(ZnsUserEquipmentEntity::getEquipmentId).distinct().toList();
        List<ZnsTreadmillEntity> treadmillEntities = znsTreadmillService.findByIds(equipmentIds);
        Map<Long, ZnsTreadmillEntity> treadmillMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(treadmillEntities)) {
            treadmillMap = treadmillEntities.stream().collect(Collectors.toMap(ZnsTreadmillEntity::getId, Function.identity(), (x, y) -> x));
        }
        //批次
        List<String> batchNumbers = treadmillEntities.stream().map(ZnsTreadmillEntity::getBatchNumber).distinct().toList();
        List<ZnsEquipmentProductionBatchEntity> batchEntities = znsEquipmentProductionBatchService.listByBatchNumber(batchNumbers);
        Map<String, ZnsEquipmentProductionBatchEntity> batchMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(batchEntities)) {
            batchMap = batchEntities.stream().collect(Collectors.toMap(ZnsEquipmentProductionBatchEntity::getBatchNumber, Function.identity(), (x, y) -> x));
        }
        List<Long> qualityAuditIds = new ArrayList<>(); //记录已经展示过质保天数的审核id
        for (ZnsUserEquipmentEntity sportEntity : sportEntities) {
            if (Objects.isNull(sportEntity.getEquipmentId())) {
                continue;
            }
            ZnsTreadmillEntity znsTreadmillEntity = treadmillMap.get(sportEntity.getEquipmentId());
            if (znsTreadmillEntity == null) {
                continue;
            }
            if (appVersion >= 4030
                    && DeviceConstant.ActivateStatusEnum.ACTIVATE_STATUS_2.code.equals(znsTreadmillEntity.getActivateStatus())) {
                //新版本不显示未激活的
                continue;
            }
            SportDeviceVo sportDeviceVo = getSportDeviceVo(sportEntity, znsTreadmillEntity, nfcMap, batchMap, qualityAuditIds);
            sports.add(sportDeviceVo);
        }
        //按connectTime倒序
        sports = sports.stream().sorted(Comparator.comparing(DeviceVo::getConnectTime, Comparator.reverseOrder())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(qualityAuditIds)) {
            //更新额外质保提示展示状态 为已展示
            equipmentQualityAuditService.updateShowStatusByIds(qualityAuditIds, DeviceConstant.ShowFinishedStatusEnum.SHOW_STATUS_2.code);
        }
        return sports;
    }

    /**
     * 运动设备数据封装
     */
    private SportDeviceVo getSportDeviceVo(ZnsUserEquipmentEntity sportEntity, ZnsTreadmillEntity znsTreadmillEntity, Map<String, TreadmillNfcLog> nfcMap,
                                           Map<String, ZnsEquipmentProductionBatchEntity> batchMap, List<Long> qualityAuditIds) {
        //设备信息
        SportDeviceVo sportDeviceVo = BeanUtil.copyBean(sportEntity, SportDeviceVo.class);
        String brandName = StringUtils.hasText(znsTreadmillEntity.getBrand()) ? znsTreadmillEntity.getBrand() : "unknown";
        sportDeviceVo.setEquipmentBrand(brandName);
        //型号展示重置
        String equipmentModel = equipConfigMoreService.findEquipmentModel(sportDeviceVo.getEquipmentModel(), brandName);
        sportDeviceVo.setEquipmentModel(equipmentModel);
        sportDeviceVo.setSupportNfc(Objects.nonNull(nfcMap.get(sportEntity.getEquipmentAddress())));
        sportDeviceVo.setUuid(sportEntity.getUuid());
        sportDeviceVo.setConnectTime(Optional.ofNullable(sportEntity.getConnectTime()).orElse(sportEntity.getCreateTime()));
        //设备名称
        if (Objects.equals(sportEntity.getEquipmentType(), DeviceConstant.EquipmentTypeEnum.TYPE_1.code)) {
            sportDeviceVo.setEquipmentTitle("Treadmill");
        } else if (Objects.equals(sportEntity.getEquipmentType(), DeviceConstant.EquipmentTypeEnum.TYPE_2.code)) {
            sportDeviceVo.setEquipmentTitle("Walking Pad");
        } else if (Objects.equals(sportEntity.getEquipmentType(), DeviceConstant.EquipmentTypeEnum.TYPE_3.code)) {
            sportDeviceVo.setEquipmentTitle("2-in-1");
        } else if (Objects.equals(sportEntity.getEquipmentType(), DeviceConstant.EquipmentTypeEnum.TYPE_20.code)) {
            sportDeviceVo.setEquipmentTitle("Indoor Exercise Bike");
        } else if (Objects.equals(sportEntity.getEquipmentType(), DeviceConstant.EquipmentTypeEnum.TYPE_40.code)) {
            sportDeviceVo.setEquipmentTitle("Indoor Rowing Machine");
        } else if (Objects.equals(sportEntity.getEquipmentType(), DeviceConstant.EquipmentTypeEnum.TYPE_30.code)) {
            sportDeviceVo.setEquipmentTitle("Under Desk Bike");
        }
        //设备外观图
        ZnsEquipmentProductionBatchEntity productionBatchEntity = batchMap.get(znsTreadmillEntity.getBatchNumber());
        sportDeviceVo.setEquipmentType(DeviceConstant.EquipmentTypeEnum.TYPE_1.getCode());// 默认为跑步机
        sportDeviceVo.setMeasureUnit(znsTreadmillEntity.getMeasureUnit()); //设备当前的单位
        if (productionBatchEntity != null) {
            sportDeviceVo.setAppearanceDrawing(productionBatchEntity.getAppearanceDrawing());
            //限定款设备 图片替换配置
            var treadmillAppearanceConfigDo = treadmillAppearanceConfigService.findByQuery(new TreadmillAppearanceConfigQuery()
                    .setProductCode(znsTreadmillEntity.getProductCode()).setAppearanceCode(znsTreadmillEntity.getAppearanceCode()));
            if(Objects.nonNull(treadmillAppearanceConfigDo) && StringUtils.hasText(treadmillAppearanceConfigDo.getPicUrl())){
                sportDeviceVo.setAppearanceDrawing(treadmillAppearanceConfigDo.getPicUrl());
            }
            sportDeviceVo.setEquipmentType(productionBatchEntity.getEquipmentType());
            sportDeviceVo.setMeasuringSystem(productionBatchEntity.getMeasuringSystem()); //设备支持的单位
            // 产品需求R1设备固定英制
            if ("R1".equals(productionBatchEntity.getTreadmillModel())) {
                sportDeviceVo.setMeasuringSystem(TreadmillMeasureEnum.IMPERIAL_UNITS.getCode());//R1默认英制
                sportDeviceVo.setMeasureUnit(TreadmillMeasureEnum.IMPERIAL_UNITS.getCode()); //R1默认英制
            }
            //脚踏使用设备的公英值替换批次支持的公英值
            if (DeviceConstant.EquipmentMainTypeEnum.BICYCLE.code.equals(productionBatchEntity.getEquipmentMainType())) {
                sportDeviceVo.setMeasuringSystem(sportDeviceVo.getMeasureUnit());//脚踏使用设备的公英值
            }
        }
        //logo图片
        EquipmentBrandConfig equipmentBrandConfig = equipmentBrandConfigService.selectEquipmentBrandConfigByBrand(znsTreadmillEntity.getBrand());
        if (equipmentBrandConfig != null) {
            sportDeviceVo.setLogoImage(equipmentBrandConfig.getLogoImage());
        }
        sportDeviceVo.setPrintId(znsTreadmillEntity.getPrintId());
        //质保结束时间
        EquipmentQualityDo equipmentQualityDo = equipmentQualityService.findByBluetoothMac(znsTreadmillEntity.getBluetoothMac());
        if (equipmentQualityDo != null) {
            sportDeviceVo.setQualityEndTime(equipmentQualityDo.getQualityEndTime());
        }
        //额外质保审核状态
        String auditStatus = getAuditListStatus(znsTreadmillEntity);
        //最后一次失败没有查看，用户还可以看
        EquipmentQualityAuditDo qualityAuditDo = equipmentQualityAuditService.findByBluetoothMac(znsTreadmillEntity.getBluetoothMac());
        if (qualityAuditDo != null && DeviceConstant.AuditStatusEnum.REJECT.code.equals(qualityAuditDo.getAuditStatus()) && qualityAuditDo.getLastSubmitStatus() == 2) {
            log.info("[getAuditListStatus]---额外质保列表状态,equipmentNo={},最后一次失败没有查看", znsTreadmillEntity.getUniqueCode());
            auditStatus = DeviceConstant.AuditListStatusEnum.REJECT.code;
        }

        //额外质保提示
        if (qualityAuditDo != null && DeviceConstant.ShowFinishedStatusEnum.SHOW_STATUS_1.code.equals(qualityAuditDo.getShowFinishedStatus())) {
            //未展示
            qualityAuditIds.add(qualityAuditDo.getId());
            //查询质保记录
            EquipmentQualityDetailDo qualityDetailDo = equipmentQualityDetailService.findById(qualityAuditDo.getQualityDetailId());
            int extraDayNum = qualityDetailDo == null ? 90 : Optional.ofNullable(qualityDetailDo.getQualityDayNum()).orElse(90);
            //展示内容
            String message = I18nMsgUtils.getMessage("extra.quality.message", extraDayNum);
            sportDeviceVo.setFinishedMsg(message);
        }

        //注册按钮显示状态
        EquipmentActivateRecordDo activateRecordDo = equipmentActivateRecordService.findByBluetoothMac(znsTreadmillEntity.getBluetoothMac());
        String qualityStatus = qualityAuditDo != null ? qualityAuditDo.getAuditStatus() : "";
        if (activateRecordDo != null && DateUtil.betweenDay(DateUtil.startOfDate(activateRecordDo.getActivateTime()), DateUtil.startOfDate(ZonedDateTime.now())) <= 30
                && !DeviceConstant.AuditStatusEnum.FINISHED.code.equals(qualityStatus)) {
            //设备激活时间≤30天 & 设备未成功领取过额外质保
            sportDeviceVo.setShowRegisterButton(true);
        } else {
            sportDeviceVo.setShowRegisterButton(false);
        }

        sportDeviceVo.setAuditStatus(auditStatus);
        sportDeviceVo.setActivateStatus(znsTreadmillEntity.getActivateStatus()); //激活状态
        return sportDeviceVo;
    }

    /**
     * 组装健康设备
     *
     * @param appType
     * @param healthEntities
     * @return
     */
    private List<HealthDeviceVo> getHealthDeviceVos(Integer appType, List<ZnsUserEquipmentEntity> healthEntities) {
        List<HealthDeviceVo> healths = new ArrayList<>();
        if (CollectionUtils.isEmpty(healthEntities)) {
            return healths;
        }
        //外观图
        String configVal = sysConfigService.selectConfigByKey(HEALTH_DEVICE_APPEARANCE_DRAWING.getCode());
        Map<Integer, String> drawingMap = new HashMap<>();
        if (StringUtils.hasText(configVal)) {
            List<HealthDeviceAppearanceDrawingVo> drawingVos = JsonUtil.readList(configVal, HealthDeviceAppearanceDrawingVo.class);
            if (!CollectionUtils.isEmpty(drawingVos)) {
                drawingMap.putAll(drawingVos.stream().collect(Collectors.toMap(HealthDeviceAppearanceDrawingVo::getEquipmentType, HealthDeviceAppearanceDrawingVo::getAppearanceDrawing, (k1, k2) -> k2)));
            }
        }
        //设备信息
        for (ZnsUserEquipmentEntity healthEntity : healthEntities) {
            if (SystemConstant.AppTypeEnum.IOS.code.equals(appType) && !StringUtils.hasText(healthEntity.getUuid())) {
                //ios设备 uuid不能为空
                continue;
            }
            if (SystemConstant.AppTypeEnum.ANDROID.code.equals(appType) && !StringUtils.hasText(healthEntity.getEquipmentAddress())) {
                //安卓设备 蓝牙不能为空
                continue;
            }
            HealthDeviceVo healthDeviceVo = BeanUtil.copyBean(healthEntity, HealthDeviceVo.class);
            //外观图
            healthDeviceVo.setAppearanceDrawing(drawingMap.getOrDefault(healthDeviceVo.getEquipmentType(), "https://pitpat-oss.s3.us-east-2.amazonaws.com/202407/i9nGK69VfZD50QE4.png"));
            healthDeviceVo.setConnectTime(Optional.ofNullable(healthEntity.getConnectTime()).orElse(healthEntity.getCreateTime()));
            healths.add(healthDeviceVo);
        }
        //按connectTime倒序
        healths = healths.stream().sorted(Comparator.comparing(DeviceVo::getConnectTime, Comparator.reverseOrder())).collect(Collectors.toList());
        return healths;
    }

    /**
     * 额外质保列表状态
     *
     * @return none: 无，toSubmit：待领取 finished：已完成，waiting：待审核，reject：审核拒绝
     * @see DeviceConstant.AuditListStatusEnum
     */
    public String getAuditListStatus(ZnsTreadmillEntity znsTreadmillEntity) {
        return equipmentQualityBiz.getAuditListStatus(znsTreadmillEntity);
    }


    /**
     * 绑定健康设备
     *
     * @param req
     * @param appVersion
     * @return
     */
    public HealthDeviceVo bindHealthDevice(Long userId, HealthDeviceBindReqDto req, Integer appType, Integer appVersion) {
        HealthDeviceVo result = new HealthDeviceVo();
        String uniqueKey = SystemConstant.AppTypeEnum.ANDROID.code.equals(appType) ? req.getEquipmentAddress() : req.getUuid();
        String bindKey = "BIND_HEALTH_DEVICE_" + userId + "_" + uniqueKey;
        RLock lock = redissonClient.getLock(bindKey);
        boolean b;
        try {
            b = lock.tryLock(300, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("UserDeviceApiManager#bindHealthDevice---绑定健康设备，userId=" + userId + ",equipmentAddress=" + uniqueKey + ",获取锁异常");
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemError"));
        }
        if (!b) {
            //获取不到锁
            log.error("UserDeviceApiManager#bindHealthDevice---绑定健康设备，userId=" + userId + ",equipmentAddress=" + uniqueKey + ",获取不到锁");
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        try {
            UserEquipmentQuery query = UserEquipmentQuery.builder().userId(userId)
                    .equipmentType(req.getEquipmentType()).uuid(req.getUuid()).equipmentAddress(req.getEquipmentAddress()).lastStr("ORDER BY id DESC LIMIT 1").build();
            ZnsUserEquipmentEntity userEquipment = userEquipmentService.findByQuery(query);
            if (userEquipment != null) {
                //已绑,更新连接时间
                BeanUtils.copyProperties(userEquipment, result);
                userEquipment.setConnectTime(ZonedDateTime.now());
                userEquipmentService.updateUserEquipment(userEquipment);
                return result;
            }
            //新增设备绑定记录
            ZnsUserEquipmentEntity newUserEquipment = BeanUtil.copyBean(req, ZnsUserEquipmentEntity.class);
            newUserEquipment.setEquipmentAddress(req.getEquipmentAddress());
            newUserEquipment.setUuid(req.getUuid());
            newUserEquipment.setUserId(userId);
            newUserEquipment.setEquipmentType(Optional.ofNullable(req.getEquipmentType()).orElse(DeviceConstant.EquipmentTypeEnum.TYPE_101.code));
            newUserEquipment.setEquipmentId(0L);
            newUserEquipment.setDeviceCategory(DeviceConstant.DeviceCategoryEnum.CATEGORY_2.code); //健康设备
            newUserEquipment.setConnectTime(ZonedDateTime.now());
            newUserEquipment.setConnectionVersion(appVersion);
            userEquipmentService.insert(newUserEquipment);
            BeanUtils.copyProperties(newUserEquipment, result);
            return result;
        } catch (Exception e) {
            log.error("UserDeviceApiManager#bindHealthDevice---绑定健康设备，userId=" + userId + ",equipmentAddress=" + uniqueKey + ",异常", e);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemError"));
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 激活设备
     *
     * @param userId
     * @param req
     */
    public void activateDevice(Long userId, ExtraQualityDetailReqDto req) {
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(req.getEquipmentNo().trim());
        if (treadmillEntity == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "treadmill"));
        }
        //pitpat激活设备
        equipmentQualityBiz.pitpatActivateDevice(userId, treadmillEntity, DeviceConstant.ActivateTypeEnum.APP);
    }

    /**
     * 新增修改额外质保
     *
     * @param userId
     * @param req
     */
    @RedisLock(value = "req.equipmentNo")
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdateExtraQuality(Long userId, ExtraQualityReqDto req) {
        String equipmentNo = req.getEquipmentNo().trim();
        log.info("[addOrUpdateExtraQuality]---新增修改额外质保,userId={},equipmentNo={}, 开始", userId, equipmentNo);
        if (!StringUtils.hasText(equipmentNo)) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "equipmentNo"));
        }
        //查询设备
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(equipmentNo);
        if (treadmillEntity == null) {
            log.info("[addOrUpdateExtraQuality]---新增修改额外质保,userId={},equipmentNo={}, 设备不存在", userId, equipmentNo);
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "treadmill"));
        }

        //重复提交校验
        EquipmentQualityAuditDo qualityAuditDo = equipmentQualityAuditService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (qualityAuditDo != null && DeviceConstant.AuditStatusEnum.WAITING.code.equals(qualityAuditDo.getAuditStatus())) {
            //不能重复提交
            log.info("[addOrUpdateExtraQuality]---新增修改额外质保,userId={},equipmentNo={} 不能重复提交", userId, equipmentNo);
            throw new BaseException(I18nMsgUtils.getMessage("extra.quality.repeat.message"));
        }

        //校验状态
        List<String> allowCodes = List.of(DeviceConstant.AuditListStatusEnum.TO_SUBMIT.code, DeviceConstant.AuditListStatusEnum.REJECT.code);
        String listStatus = getAuditListStatus(treadmillEntity);
        if (!allowCodes.contains(listStatus)) {
            //状态不允许
            log.info("[addOrUpdateExtraQuality]---新增修改额外质保,userId={},equipmentNo={},listStatus={} 状态不允许", userId, equipmentNo, listStatus);
            throw new BaseException(I18nMsgUtils.getMessage("extra.quality.state.message"));
        }

        //获取质保时间配置
        EquipmentQualityDayConfigVo qualityDayConfig = iSysConfigService.selectConfigVoByKey(ConfigKeyEnums.EQUIPMENT_ACTIVATE_TIME_CONFIG.getCode(), EquipmentQualityDayConfigVo.class);
        int extraDayNum = qualityDayConfig == null ? 90 : Optional.ofNullable(qualityDayConfig.getExtraDayNum()).orElse(90); //额外质保天数


        //保存质保明细
        EquipmentQualityDetailDo qualityDetailDo = new EquipmentQualityDetailDo(treadmillEntity.getBluetoothMac(), treadmillEntity.getPrintId(), DeviceConstant.QualityTypeEnum.EXTRA.code,
                DeviceConstant.CreateSourceEnum.PITPAT_APP.code, DeviceConstant.QualityStatusEnum.QUALITY_STATUS_0.code,
                req.getUserName(), req.getBuySource(), req.getOrderNo(), req.getOrderTime(), req.getPhoneNumber(), req.getEmailAddress(), extraDayNum);

        //审核数据
        EquipmentQualityAuditDo newEntity = new EquipmentQualityAuditDo(userId, treadmillEntity.getPrintId(),
                treadmillEntity.getBluetoothMac(), DeviceConstant.AuditStatusEnum.WAITING.getCode(), ZonedDateTime.now());

        if (qualityAuditDo != null) {
            //更新质保明细
            qualityDetailDo.setId(qualityAuditDo.getQualityDetailId());
            equipmentQualityDetailService.update(qualityDetailDo);

            //更新审核记录
            newEntity.setId(qualityAuditDo.getId());
            newEntity.setAuditNum(qualityAuditDo.getAuditNum() + 1);
            if (qualityAuditDo.getLastSubmitStatus() == 1) {
                newEntity.setLastSubmitStatus(2); //最后一次机会被使用
            }
            equipmentQualityAuditService.update(newEntity);
            userTaskBizService.completeEvent(new EventTriggerDto().setUserId(userId).setEventSubType(TaskConstant.TakEventSubTypeEnum.DEVICE_QUALITY.getCode()));
            log.info("[addOrUpdateExtraQuality]---新增修改额外质保,userId={},equipmentNo={}  编辑完成", userId, equipmentNo);
            return qualityAuditDo.getQualityDetailId();
        } else {
            //新增质保明细
            Long qualityDetailId = equipmentQualityDetailService.create(qualityDetailDo);

            //新增审核记录
            newEntity.setAuditNum(1);
            newEntity.setQualityDetailId(qualityDetailId); //明细id
            equipmentQualityAuditService.create(newEntity);
            userTaskBizService.completeEvent(new EventTriggerDto().setUserId(userId).setEventSubType(TaskConstant.TakEventSubTypeEnum.DEVICE_QUALITY.getCode()));
            log.info("[addOrUpdateExtraQuality]---新增修改额外质保,userId={},equipmentNo={}  新增完成", userId, equipmentNo);
            return qualityDetailId;
        }
    }

    /**
     * 获取额外质保详情
     *
     * @param req
     * @return
     */
    public ExtraQualityDetailRespDto getExtraQualityDetail(ExtraQualityDetailReqDto req) {
        //查询设备
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(req.getEquipmentNo());
        if (treadmillEntity == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "treadmill"));
        }
        //查询审批信息
        EquipmentQualityAuditDo qualityAuditDo = equipmentQualityAuditService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (qualityAuditDo == null) {
            return new ExtraQualityDetailRespDto();
        }
        ExtraQualityDetailRespDto dto = equipmentQualityAuditConverter.toDto(qualityAuditDo);

        //查询额外质保明细
        EquipmentQualityDetailDo qualityDetailDo = equipmentQualityDetailService.findById(qualityAuditDo.getQualityDetailId());
        BeanUtils.copyProperties(qualityDetailDo, dto);
        if (DeviceConstant.AuditStatusEnum.REJECT.code.equals(qualityAuditDo.getAuditStatus()) && qualityAuditDo.getLastSubmitStatus() == 2) {
            //最后一次提交失败，更新查看记录
            EquipmentQualityAuditDo updateDo = new EquipmentQualityAuditDo();
            updateDo.setId(qualityAuditDo.getId());
            updateDo.setLastSubmitStatus(3); //已查看
            equipmentQualityAuditService.update(updateDo);
            //当前数据为只能查看
            dto.setIsViewed(true);
        }
        dto.setEquipmentNo(treadmillEntity.getUniqueCode());
        return dto;
    }

    /**
     * 检查是否可以领取额外质保
     *
     * @param req
     */
    public CheckExtraQualityRespDto checkReceiveExtraQuality(ExtraQualityDetailReqDto req) {
        String equipmentNo = req.getEquipmentNo();
        log.info("[checkReceiveExtraQuality]---检查是否可以领取额外质保,equipmentNo={}, 开始", equipmentNo);
        //查询设备
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(equipmentNo);
        if (treadmillEntity == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "treadmill"));
        }

        //查询首保记录
        EquipmentQualityDetailQuery query = new EquipmentQualityDetailQuery().setBluetoothMac(treadmillEntity.getBluetoothMac()).setQualityType(DeviceConstant.QualityTypeEnum.ACTIVATE.code);
        EquipmentQualityDetailDo qualityDetailDo = equipmentQualityDetailService.findByQuery(query);
        if (qualityDetailDo != null) {
            //已经领取过首保
            log.info("[checkReceiveExtraQuality]---检查是否可以领取额外质保,equipmentNo={}, 已经领取过首保", equipmentNo);
            return new CheckExtraQualityRespDto(false);
        }

        //是否已弹框过
        EquipmentActivateRecordDo activateRecordDo = equipmentActivateRecordService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (activateRecordDo.getIsPop() == 1) {
            //已经弹过不在弹
            return new CheckExtraQualityRespDto(false);
        }

        //质保时间配置
        EquipmentQualityDayConfigVo qualityDayConfig = iSysConfigService.selectConfigVoByKey(ConfigKeyEnums.EQUIPMENT_ACTIVATE_TIME_CONFIG.getCode(), EquipmentQualityDayConfigVo.class);
        int submitTimeOutDay = qualityDayConfig == null ? 30 : Optional.ofNullable(qualityDayConfig.getSubmitTimeOutDay()).orElse(30); //额外质保提交超时天数
        int betweenDay = DateUtil.betweenDay(DateUtil.startOfDate(activateRecordDo.getActivateTime()), DateUtil.startOfDate(ZonedDateTime.now()));//激活时间到现在天数
        if (betweenDay > submitTimeOutDay) {
            //已超时，不能提交
            log.info("[checkReceiveExtraQuality]---检查是否可以领取额外质保,equipmentNo={}, 已超时，不能提交", equipmentNo);
            return new CheckExtraQualityRespDto(false);
        }

        //查询审核记录
        EquipmentQualityAuditDo equipmentQualityAuditDo = equipmentQualityAuditService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (equipmentQualityAuditDo != null) {
            //已有记录，不能提交
            log.info("[checkReceiveExtraQuality]---检查是否可以领取额外质保,equipmentNo={}, 已有记录，不能提交", equipmentNo);
            return new CheckExtraQualityRespDto(false);
        }

        //跟新为已弹框
        EquipmentActivateRecordDo updateDo = new EquipmentActivateRecordDo();
        updateDo.setId(activateRecordDo.getId());
        updateDo.setIsPop(1);
        updateDo.setGmtModified(ZonedDateTime.now());
        equipmentActivateRecordService.update(updateDo);

        //未超时，可以提交
        return new CheckExtraQualityRespDto(true);
    }

    /**
     * 查询设备质保审核状态，none: 无，toSubmit：待领取 finished：已完成，waiting：待审核，reject：审核拒绝
     *
     * @see DeviceConstant.AuditListStatusEnum
     */
    public String getQualityAuditStatus(ExtraQualityDetailReqDto req) {
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(req.getEquipmentNo().trim());
        if (Objects.isNull(treadmillEntity)) {
            return DeviceConstant.AuditListStatusEnum.NONE.code;
        }
        return getAuditListStatus(treadmillEntity);
    }

    public UserDeviceNewVersionListRespDto findUserSportDevices(ZnsUserEntity loginUser) {
        UserDeviceNewVersionListRespDto result = new UserDeviceNewVersionListRespDto();
        UserEquipmentQuery query = UserEquipmentQuery.builder()
                .userId(loginUser.getId())
                .deviceCategory(DeviceConstant.DeviceCategoryEnum.CATEGORY_1.getCode())
                .build();
        query.setLastStr(" limit 99");
        List<ZnsUserEquipmentEntity> list = userEquipmentService.findByList(query);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        //运动设备
        List<SportDeviceNewVo> sports = getSportDeviceVosNew(list, loginUser);
        result.setSports(sports);
        return result;
    }

    /**
     * 用户运动设备
     *
     * @param sportEntities
     * @param loginUser
     * @return
     */
    private List<SportDeviceNewVo> getSportDeviceVosNew(List<ZnsUserEquipmentEntity> sportEntities, ZnsUserEntity loginUser) {
        List<SportDeviceNewVo> sports = new ArrayList<>();
        if (CollectionUtils.isEmpty(sportEntities)) {
            return sports;
        }
        //NFC
        List<String> bluetoothMacs = sportEntities.stream().map(ZnsUserEquipmentEntity::getEquipmentAddress).distinct().toList();
        List<TreadmillNfcLog> treadmillNfcLogs = treadmillNfcLogService.findAllByBluetoothMacs(bluetoothMacs);
        Map<String, TreadmillNfcLog> nfcMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(treadmillNfcLogs)) {
            nfcMap = treadmillNfcLogs.stream().collect(Collectors.toMap(TreadmillNfcLog::getBluetoothMac, Function.identity(), (x, y) -> x));
        }
        //设备
        List<Long> equipmentIds = sportEntities.stream().map(ZnsUserEquipmentEntity::getEquipmentId).distinct().toList();
        List<ZnsTreadmillEntity> treadmillEntities = znsTreadmillService.findByIds(equipmentIds);
        Map<Long, ZnsTreadmillEntity> treadmillMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(treadmillEntities)) {
            treadmillMap = treadmillEntities.stream().collect(Collectors.toMap(ZnsTreadmillEntity::getId, Function.identity(), (x, y) -> x));
        }
        //批次
        List<String> batchNumbers = treadmillEntities.stream().map(ZnsTreadmillEntity::getBatchNumber).distinct().toList();
        List<ZnsEquipmentProductionBatchEntity> batchEntities = znsEquipmentProductionBatchService.listByBatchNumber(batchNumbers);
        Map<String, ZnsEquipmentProductionBatchEntity> batchMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(batchEntities)) {
            batchMap = batchEntities.stream().collect(Collectors.toMap(ZnsEquipmentProductionBatchEntity::getBatchNumber, Function.identity(), (x, y) -> x));
        }
        //查询设备分享信息
        List<UserEquipmentShareDo> equipmentShareListsAll = userEquipmentShareService.findList(new UserEquipmentShareQuery()
                .setTreadmillList(equipmentIds));
        Map<Long, List<UserEquipmentShareDo>> shareMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(equipmentShareListsAll)) {
            shareMap = equipmentShareListsAll.stream().collect(Collectors.groupingBy(UserEquipmentShareDo::getTreadmillId));
        }

        List<Long> qualityAuditIds = new ArrayList<>(); //记录已经展示过质保天数的审核id
        for (ZnsUserEquipmentEntity sportEntity : sportEntities) {
            SportDeviceNewVo sportDeviceNewVo = new SportDeviceNewVo();
            if (Objects.isNull(sportEntity.getEquipmentId())) {
                continue;
            }
            ZnsTreadmillEntity znsTreadmillEntity = treadmillMap.get(sportEntity.getEquipmentId());
            if (znsTreadmillEntity == null) {
                continue;
            }
            SportDeviceVo sportDeviceVo = getSportDeviceVo(sportEntity, znsTreadmillEntity, nfcMap, batchMap, qualityAuditIds);
            BeanUtils.copyProperties(sportDeviceVo, sportDeviceNewVo);
            String bluetoothChipType = znsTreadmillEntity.getFirmwareType();
            sportDeviceNewVo.setBluetoothChipType(bluetoothChipType);
            sportDeviceNewVo.setIsSupportBuzzerSound(false);
            sportDeviceNewVo.setIsSupportOtherSound(false);
            if ((DeviceConstant.FirmwareTypeEnum.XZX_SELF.getCode().equals(bluetoothChipType)
                    || DeviceConstant.FirmwareTypeEnum.XZX_PURCHASE.getCode().equals(bluetoothChipType))
                    && Objects.nonNull(znsTreadmillEntity.getEquipmentVersion()) && znsTreadmillEntity.getEquipmentVersion() >= 30
                    && znsTreadmillEntity.getEquipmentMainType().equals(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getCode())) {
                sportDeviceNewVo.setIsSupportOtherSound(true);
            }
            if (StringUtils.hasText(znsTreadmillEntity.getProductCode())) {
                TreadmillBuzzerSupportQuery query = TreadmillBuzzerSupportQuery.builder().productCode(znsTreadmillEntity.getProductCode()).build();
                TreadmillBuzzerSupport treadmillBuzzerSupport = treadmillBuzzerSupportService.findCacheByQuery(query);
                if (Objects.nonNull(treadmillBuzzerSupport)
                        && Objects.nonNull(treadmillBuzzerSupport.getSupportTime())
                        && znsTreadmillEntity.getCreateTime().isAfter(treadmillBuzzerSupport.getSupportTime())) {
                    //在支持时间之后才支持蜂鸣器
                    sportDeviceNewVo.setIsSupportBuzzerSound(true);
                }
            }
            if(!treadmillWhiteListService.checkEquipmentWhiteList(sportEntity.getEquipmentId())){
                sportDeviceNewVo.setIsAllowShare(YesNoStatus.YES.getCode());
                // 设备人当前身份
                List<UserEquipmentShareDo> equipmentShareLists = shareMap.get(sportEntity.getEquipmentId());
                if (!CollectionUtils.isEmpty(equipmentShareLists)) {
                    Optional.ofNullable(equipmentShareLists.stream()
                                    .filter(i -> i.getUserId().equals(sportEntity.getUserId()))
                                    .findAny()
                                    .orElseGet(() -> {
                                        if (loginUser.getUserType() != 1) {
                                            UserEquipmentShareDo shareInfo = new UserEquipmentShareDo();
                                            shareInfo.setUserId(sportEntity.getUserId());
                                            shareInfo.setTreadmillId(sportEntity.getEquipmentId());
                                            shareInfo.setUserType(0);
                                            userEquipmentShareService.create(shareInfo);
                                            return shareInfo;
                                        } else {
                                            return null;
                                        }
                                    }))
                            .ifPresent(equipmentShareDo -> {
                                if (Objects.nonNull(equipmentShareDo.getUserType())) {
                                    sportDeviceNewVo.setUserType(equipmentShareDo.getUserType());
                                } else {
                                    sportDeviceNewVo.setUserType(0);
                                }
                            });
                } else {
                    if (loginUser.getUserType() != 1) {
                        //加锁
                        String key = RedisConstants.SHARE_ADD_LOCK + loginUser.getId() + "treadmill:" + sportEntity.getEquipmentId();
                        RLock lock = redissonClient.getLock(key);
                        try {
                            //没有分享过首次链接绑定
                            if (LockHolder.tryLock(lock, 3, 30)) {
                                UserEquipmentShareDo equipmentShareDo = new UserEquipmentShareDo();
                                equipmentShareDo.setUserId(sportEntity.getUserId());
                                equipmentShareDo.setTreadmillId(sportEntity.getEquipmentId());
                                equipmentShareDo.setUserType(1);
                                userEquipmentShareService.create(equipmentShareDo);
                            }
                        } catch (Exception e) {
                            log.error("设备列表绑定请求并发异常e", e);
                        } finally {
                            if (lock.isHeldByCurrentThread()) {
                                lock.unlock();
                            }
                        }
                        sportDeviceNewVo.setUserType(1);
                    }
                }
                // 分享设备人数
                sportDeviceNewVo.setInviteUserNum(userEquipmentShareService.findShareUserNum(sportEntity.getUserId(), sportEntity.getEquipmentId()));
            }else {
                sportDeviceNewVo.setIsAllowShare(YesNoStatus.NO.getCode());
            }

            //设备统计数据
            EquipmentRunDataQuery equipmentRunDataQuery = new EquipmentRunDataQuery();
            equipmentRunDataQuery.setEquipmentId(sportEntity.getEquipmentId());
            EquipmentRunDataDo equipmentRunDataDo = equipmentRunDataService.findByQuery(equipmentRunDataQuery);
            if (Objects.nonNull(equipmentRunDataDo)) {
                sportDeviceNewVo.setEquipmentRunMile(equipmentRunDataDo.getTotalRunMileage().intValue());
                sportDeviceNewVo.setEquipmentRunTime(equipmentRunDataDo.getTotalRunTime());
            }
            sportDeviceNewVo.setEquipmentNickName(StringUtils.hasText(znsTreadmillEntity.getEquipmentNickName()) ? znsTreadmillEntity.getEquipmentNickName() : znsTreadmillEntity.getProductCode() + "-" + znsTreadmillEntity.getBluetoothMac().substring(znsTreadmillEntity.getBluetoothMac().length() - 4));
            sportDeviceNewVo.setEquipmentSearchName(sportEntity.getEquipmentName() + "-" + sportEntity.getEquipmentAddress());
            sports.add(sportDeviceNewVo);
        }
        //按connectTime倒序
        sports = sports.stream().sorted(Comparator.comparing(DeviceVo::getConnectTime, Comparator.reverseOrder())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(qualityAuditIds)) {
            //更新额外质保提示展示状态 为已展示
            equipmentQualityAuditService.updateShowStatusByIds(qualityAuditIds, DeviceConstant.ShowFinishedStatusEnum.SHOW_STATUS_2.code);
        }
        return sports;
    }


    /**
     * 用户删除设备增加分享设备数据处理逻辑
     *
     * @param equipmentEntity
     * @param userId
     */
    public void deleteUserEquipment(ZnsUserEquipmentEntity equipmentEntity, Long userId) {
        UserEquipmentShareQuery shareQuery = new UserEquipmentShareQuery().setUserId(userId).setTreadmillId(equipmentEntity.getEquipmentId());
        UserEquipmentShareDo equipmentShareDo = userEquipmentShareService.findByQuery(shareQuery);
        if (Objects.nonNull(equipmentShareDo)) {
            userEquipmentShareService.deleteById(equipmentShareDo.getId());
            if (equipmentShareDo.getUserType() == 1) {
                // 重置设备昵称
                ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findById(equipmentShareDo.getTreadmillId());
                treadmillEntity.setEquipmentNickName(treadmillEntity.getProductCode() + "-" + treadmillEntity.getBluetoothMac().substring(treadmillEntity.getBluetoothMac().length() - 4));
                znsTreadmillService.update(treadmillEntity);
                //设备主绑定人删除，则查看有没有分享者.变更主绑定人
                UserEquipmentShareQuery query = new UserEquipmentShareQuery()
                        .setTreadmillId(equipmentEntity.getEquipmentId());
                query.setOrders(List.of(OrderItem.asc("id")));
                UserEquipmentShareDo equipmentShareOtherDo = userEquipmentShareService.findByQuery(query);
                if (Objects.nonNull(equipmentShareOtherDo) && equipmentShareOtherDo.getUserType() == 0) {
                    equipmentShareOtherDo.setUserType(1);
                    userEquipmentShareService.update(equipmentShareOtherDo);
                }
            }
        }
        userEquipmentService.deleteById(equipmentEntity.getId());
        String key = String.format(RedisKeyConstant.USER_EQUIPMENT_CONNECT, equipmentEntity.getEquipmentNo());
        redissonClient.getBucket(key).delete();
    }

    /**
     * 用户虚拟设备列表
     */
    public UserVirtualDeviceListRespDto deviceListVirtual(ZnsUserEntity user) {
        //查询用户虚拟设备
        UserEquipmentQuery query = UserEquipmentQuery.builder().userId(user.getId()).deviceCategory(DeviceConstant.DeviceCategoryEnum.CATEGORY_3.code).build();
        List<ZnsUserEquipmentEntity> virtualEquipments = userEquipmentService.findList(query);
        if (CollectionUtils.isEmpty(virtualEquipments)) {
            return null;
        }
        List<Long> treadmillIds = virtualEquipments.stream().map(ZnsUserEquipmentEntity::getEquipmentId).toList();
        //查询虚拟设备图片
        Map<String, VirtualEquipmentImageVo> imgVoMap = new HashMap<>();
        List<VirtualEquipmentImageVo> equipmentImageVos = sysConfigService.selectConfigListByKey(ConfigKeyEnums.VIRTUAL_DEVICE_IMAGES.getCode(), VirtualEquipmentImageVo.class);
        if (!CollectionUtils.isEmpty(equipmentImageVos)) {
            imgVoMap = equipmentImageVos.stream().collect(Collectors.toMap(VirtualEquipmentImageVo::getEquipmentMainType, Function.identity()));
        }
        //查询虚拟设备
        Map<Long, ZnsTreadmillEntity> treadmillMap = new HashMap<>();
        List<ZnsTreadmillEntity> treadmillEntities = znsTreadmillService.findByIds(treadmillIds);
        if (!CollectionUtils.isEmpty(treadmillEntities)) {
            treadmillMap = treadmillEntities.stream().collect(Collectors.toMap(ZnsTreadmillEntity::getId, Function.identity()));
        }
        //查询用户设备跑步数据
        Map<Long, EquipmentRunDataVo> runDataMap = new HashMap<>();
        List<EquipmentRunDataVo> statisticsRunDatas = realPersonRunDataDetailsService.findUsersEquipmentRunDataVoByTreadmillIds(treadmillIds, user.getId());
        if (!CollectionUtils.isEmpty(statisticsRunDatas)) {
            runDataMap = statisticsRunDatas.stream().collect(Collectors.toMap(EquipmentRunDataVo::getTreadmillId, Function.identity()));
        }
        //封装数据
        List<VirtualDeviceVo> virtualDeviceVos = new ArrayList<>();
        for (ZnsUserEquipmentEntity virtualEquipment : virtualEquipments) {
            VirtualDeviceVo virtualDeviceVo = userEquipmentConverter.toVirtualDeviceDto(virtualEquipment);
            //设备信息
            ZnsTreadmillEntity treadmillEntity = treadmillMap.get(virtualEquipment.getEquipmentId());
            if (Objects.nonNull(treadmillEntity)) {
                virtualDeviceVo.setMeasureUnit(treadmillEntity.getMeasureUnit());
                virtualDeviceVo.setMeasuringSystem(2);
                VirtualEquipmentImageVo virtualEquipmentImageVo = imgVoMap.get(treadmillEntity.getEquipmentMainType());
                if (Objects.nonNull(virtualEquipmentImageVo)) {
                    virtualDeviceVo.setDeviceDescImgUrl(virtualEquipmentImageVo.getImgUrl());
                    String linkUrl = mallH5Url + virtualEquipmentImageVo.getLinkUrl();
                    virtualDeviceVo.setDeviceDescImgLinkUrl(linkUrl);
                }
            }
            //运动信息
            EquipmentRunDataVo equipmentRunDataVo = runDataMap.get(virtualEquipment.getEquipmentId());
            if (Objects.nonNull(equipmentRunDataVo)) {
                virtualDeviceVo.setRunMileage(equipmentRunDataVo.getTotalRunMileage());
                virtualDeviceVo.setRunTime(equipmentRunDataVo.getTotalRunTime());
            }
            virtualDeviceVos.add(virtualDeviceVo);
        }
        //返回结果，按使用状态倒序
        UserVirtualDeviceListRespDto result = new UserVirtualDeviceListRespDto();
        result.setVirtualDeviceVos(virtualDeviceVos.stream().sorted(Comparator.comparing(VirtualDeviceVo::getUseState).reversed()).toList());
        return result;
    }

    /**
     * 使用虚拟设备
     */
    @Transactional(rollbackFor = Exception.class)
    public void useVirtualDevice(Long userEquipmentId, ZnsUserEntity user) {
        ZnsUserEquipmentEntity userEquipmentEntity = userEquipmentService.findById(userEquipmentId);
        if (Objects.isNull(userEquipmentEntity)) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "UserEquipment"));
        }
        if (!DeviceConstant.DeviceCategoryEnum.CATEGORY_3.code.equals(userEquipmentEntity.getDeviceCategory())) {
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemError"));
        }
        //更新使用中为未使用
        UserEquipmentQuery query = UserEquipmentQuery.builder().userId(user.getId())
                .deviceCategory(DeviceConstant.DeviceCategoryEnum.CATEGORY_3.code)
                .useState(DeviceConstant.UseStateEnum.USEING.code).build();
        List<ZnsUserEquipmentEntity> list = userEquipmentService.findByList(query);
        if (!CollectionUtils.isEmpty(list)) {
            for (ZnsUserEquipmentEntity userEquipment : list) {
                ZnsUserEquipmentEntity updateUserEquipment = new ZnsUserEquipmentEntity();
                updateUserEquipment.setId(userEquipment.getId());
                updateUserEquipment.setUseState(DeviceConstant.UseStateEnum.UNUSED.code);
                userEquipmentService.update(updateUserEquipment);
            }
        }

        //当前更新使用中
        ZnsUserEquipmentEntity updateUserEquipment = new ZnsUserEquipmentEntity();
        updateUserEquipment.setId(userEquipmentEntity.getId());
        updateUserEquipment.setUseState(DeviceConstant.UseStateEnum.USEING.code);
        updateUserEquipment.setConnectTime(ZonedDateTime.now());
        userEquipmentService.update(updateUserEquipment);
    }

    public DeviceSimpleResponseDto findDeviceLast(ZnsUserEntity loginUser) {
        ZnsUserEquipmentEntity equipment = userEquipmentService.findByQuery(UserEquipmentQuery.builder().userId(loginUser.getId()).lastStr("ORDER BY connect_time DESC").build());
        if (equipment == null) {
            return new DeviceSimpleResponseDto();
        }
        return userEquipmentConverter.toSimpleRespDto(equipment);
    }
}
