package com.linzi.pitpat.api.controller.h5;


import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.api.activityservice.manager.RunActivityManager;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.TimeZoneVo;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityAwardCurrencyBizService;
import com.linzi.pitpat.data.activityservice.biz.RunActivityBizService;
import com.linzi.pitpat.data.activityservice.dto.api.ActivityDetailReq;
import com.linzi.pitpat.data.activityservice.dto.api.ActivityVoteInfoReq;
import com.linzi.pitpat.data.activityservice.manager.ActivityUserManager;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityEquipmentConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityUserTask;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserQuery;
import com.linzi.pitpat.data.activityservice.model.request.ActivityRequest;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityPackH5Resp;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityTeamVoteInfoResp;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityDetailVO;
import com.linzi.pitpat.data.activityservice.model.vo.RunActivityRewardDetailVO;
import com.linzi.pitpat.data.activityservice.service.H5ActivityFreeUserService;
import com.linzi.pitpat.data.activityservice.service.RunActivityUserTaskService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.strategy.ActivityStrategyContext;
import com.linzi.pitpat.data.activityservice.strategy.NewUserActivityStrategy;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.enums.ActivityClassifyTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.request.AddressLibReq;
import com.linzi.pitpat.data.request.OpenActivityPackReq;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * h5活动接口
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/h5/activity")
@Slf4j
@RequiredArgsConstructor
public class ActivityController extends BaseH5Controller {
    @Resource
    private ZnsRunActivityConfigService runActivityConfigService;
    @Resource
    private ZnsRunActivityService runActivityService;
    @Resource
    private RunActivityUserTaskService runActivityUserTaskService;
    @Resource
    private ActivityStrategyContext activityStrategyContext;
    @Resource
    private ZnsCourseService courseService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private ZnsRunActivityConfigService znsRunActivityConfigService;

    @Resource
    private NewUserActivityStrategy newUserActivityStrategy;

    @Resource
    private RunActivityBizService runActivityBizService;

    @Resource
    private ActivityAwardCurrencyBizService activityAwardCurrencyBizService;

    @Resource
    private RunActivityManager runActivityManager;

    @Resource
    private H5ActivityFreeUserService h5ActivityFreeUserService;
    @Resource
    private ActivityUserManager activityUserManager;

    /**
     * 新人福利页
     *
     * @return
     */
    @PostMapping("/newUserActivityInfo")
    public Result newUserActivityInfo() {
        ZnsUserEntity user = getLoginUser();

        Map<String, Object> data = new HashMap<>();
        //新人任务跑道配置
        String config = sysConfigService.selectConfigByKey("newUserActivity.route");

        //为了保留泛型信息，此处使用了 TypeReference
        List<Map<String, Object>> objectList = JsonUtil.readValue(config, new TypeReference<>() {
        });
        Map<Integer, Map<String, Object>> taskType = objectList.stream().filter(Objects::nonNull).collect(Collectors.toMap(obj -> MapUtil.getInteger(obj.get("taskType")), obj -> obj));
        //查询是否已报名
        ZnsRunActivityEntity newUserActivityInfo = runActivityService.getNewUserActivityInfo(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType(), user.getId());

        ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.findRunActivityConfig(6L);
        //视频配置添加
        long lastEnterRunTime = 0;
        if (Objects.nonNull(runActivityConfig)) {
            Map<String, Object> object = JsonUtil.readValue(runActivityConfig.getActivityConfig());
            lastEnterRunTime = newUserActivityStrategy.getLastEnterRunTime(object);
            data.put("advertisingVideo", object.get(ApiConstants.ADVERTISING_VIDEO));
            data.put("advertisingImage", object.get(ApiConstants.ADVERTISING_IMAGE));
        }
        BigDecimal award = BigDecimal.ZERO;
        if (Objects.nonNull(newUserActivityInfo)) {
            data.put("isEnroll", 1);
            data.put("activityStartTime", newUserActivityInfo.getActivityStartTime());
            data.put("activityEndTime", newUserActivityInfo.getActivityEndTime());

            //查询用户任务完成情况
            List<RunActivityUserTask> userTasks = runActivityUserTaskService.getUserTasks(user.getId(), 6);
            for (int i = 0; i < userTasks.size(); i++) {
                RunActivityUserTask taskVo = userTasks.get(i);
                if (taskVo.getStatus() == 1 || taskVo.getStatus() == 2) {
                    taskVo.setAward(taskVo.getAward());
                    award = award.add(taskVo.getAward());
                } else {
                    taskVo.setAward(taskVo.getWinReward());
                }
                Integer taskTypeCode = taskVo.getTaskType();
                if (taskTypeCode == 4) {
                    dealWithTaskType4(lastEnterRunTime, taskVo);
                }

                if (Objects.nonNull(taskVo.getCourseId()) && taskVo.getCourseId() > 0) {
                    ZnsCourseEntity course = courseService.selectById(taskVo.getCourseId());
                    if (Objects.nonNull(course)) {
                        taskVo.setCourseDuration(course.getCourseDuration());
                    }
                }
                Map<String, Object> object = taskType.get(taskTypeCode);
                if (object != null) {
                    taskVo.setRouteId(MapUtil.getInteger(object.get("routeId")));
                    taskVo.setRouteType(MapUtil.getInteger(object.get("routeType")));
                }
            }
            data.put("activityConfigId", newUserActivityInfo.getActivityConfigId());

            data.put("tasks", userTasks);
        } else {
            data.put("isEnroll", 0);
            //查询所有关卡
            if (null == runActivityConfig) {
                log.warn("新人福利配置缺失");
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
            }
            data.put("activityConfigId", runActivityConfig.getId());

            Map<String, Object> object = JsonUtil.readValue(runActivityConfig.getActivityConfig());
            List<RunActivityUserTask> taskVos = JsonUtil.readList(object.get("tasks"), RunActivityUserTask.class);
            for (RunActivityUserTask taskVo : taskVos) {
                taskVo.setAward(taskVo.getWinReward());
                if (Objects.nonNull(taskVo.getCourseId()) && taskVo.getCourseId() > 0) {
                    ZnsCourseEntity course = courseService.selectById(taskVo.getCourseId());
                    if (Objects.nonNull(course)) {
                        taskVo.setCourseDuration(course.getCourseDuration());
                    }
                }
                Map<String, Object> object1 = taskType.get(taskVo.getTaskType());
                if (object1 != null) {
                    taskVo.setRouteId(MapUtil.getInteger(object1.get("routeId")));
                    taskVo.setRouteType(MapUtil.getInteger(object1.get("routeType")));
                }

            }
            data.put("tasks", taskVos);
        }

        ZnsRunActivityConfigEntity znsRunActivityConfigEntity = znsRunActivityConfigService.selectByActivityType(6, null);
        Map<String, Object> json = JsonUtil.readValue(znsRunActivityConfigEntity.getActivityConfig());
        List<ActivityEquipmentConfigDto> activityEquipmentConfigs = null;
        if (json != null && json.get("activityEquipmentConfigs") != null) {
            activityEquipmentConfigs = JsonUtil.readList(json.get("activityEquipmentConfigs"), ActivityEquipmentConfigDto.class);// 设备配置
            for (ActivityEquipmentConfigDto activityEquipmentConfig : activityEquipmentConfigs) {
                if (Objects.equals(activityEquipmentConfig.getEquipmentType(), 3)) {
                    // 跑步形态 , 1 走步形态 , 2. 跑步形态
                    if (Objects.equals(activityEquipmentConfig.getSubType(), 1)) {
                        activityEquipmentConfig.setEquipmentInfo(activityEquipmentConfig.getEquipmentInfo() + " (Walking Mode)");
                    } else if (Objects.equals(activityEquipmentConfig.getSubType(), 2)) {
                        activityEquipmentConfig.setEquipmentInfo(activityEquipmentConfig.getEquipmentInfo() + " (Running Mode)");
                    }
                }
            }
        }

        data.put("award", award);
        data.put("activityEquipmentConfigs", activityEquipmentConfigs);
        return CommonResult.success(data);
    }

    /**
     * 处理完成官方多人同跑的情况
     *
     * @param lastEnterRunTime
     * @param taskVo
     */
    private void dealWithTaskType4(long lastEnterRunTime, RunActivityUserTask taskVo) {
        // 类型为:完成一次官方多人同跑
        Long activityId = taskVo.getActivityId();
        if (activityId == null || activityId == 0) {
            // 1、未关联一场官方多人同跑比赛，显示「start」，点击进入列表页
            taskVo.setCanEnterDetailPage(0);
        } else {
            ZnsRunActivityEntity runActivity = runActivityService.findOne(RunActivityQuery.builder()
                    .select(List.of(ZnsRunActivityEntity::getActivityStartTime))
                    .id(activityId).isDelete(0)
                    .build());
            if (runActivity == null) {
                taskVo.setCanEnterDetailPage(0);
            } else {
                ZonedDateTime activityStartTime = runActivity.getActivityStartTime();
                long time = activityStartTime.toInstant().toEpochMilli();
                long currentTimeMillis = System.currentTimeMillis();
                if (currentTimeMillis < time + lastEnterRunTime) {
                    // 2、关联了一场官方多人同跑比赛，且这场比赛还可以进入（当前时间<活动开始时间+xx分钟
                    // （这里的xx分钟迟到时间是变量，当前配置是30分钟，记得要根据变量变））显示「start」，点击进入此场活动的详情页
                    RunActivityUserQuery userQuery = RunActivityUserQuery.builder()
                            .taskId(taskVo.getId())
                            .isDelete(0)
                            .build();
                    userQuery.addOrderByDesc("id");
                    ZnsRunActivityUserEntity runActivityUser = runActivityUserService.findOne(userQuery);
                    if (runActivityUser != null && (runActivityUser.getUserState() == 1 || runActivityUser.getUserState() == 3)) {
                        taskVo.setCanEnterDetailPage(1);
                    } else {
                        taskVo.setCanEnterDetailPage(0);

                        // 取消关卡与活动的关联
                        runActivityUserTaskService.cancle(taskVo.getId());

                    }
                    taskVo.setActivityId(activityId);
                } else {
                    // 3、关联了一场官方多人同跑比赛，且这场比赛不可以进入。则去掉之前场比赛和新人福利的关系，
                    // 新人福利页，显示「start」，点击进入此场活动列表页，可以重新选择活动
                    taskVo.setCanEnterDetailPage(0);

                    // 取消关卡与活动的关联
                    runActivityUserTaskService.cancle(taskVo.getId());

                }
            }
        }
    }

    /**
     * 报名
     *
     * @param map
     * @return
     */
    @PostMapping("/enrolling")
    public Result enrolling(@RequestBody Map<String, Object> map) {
        String equipmentNo = MapUtils.getString(map, "equipmentNo");
        Long activityConfigId = MapUtils.getLong(map, "activityConfigId");

        ZnsUserEntity user = getLoginUser();
        ZnsRunActivityConfigEntity runActivityConfig = runActivityConfigService.findRunActivityConfig(activityConfigId);

        Result result = activityStrategyContext.checkEnrollingNewUserActivity(runActivityConfig, user, equipmentNo);
        if (Objects.nonNull(result)) {
            return result;
        }

        //加锁
        RLock lock = redissonClient.getLock(RedisConstants.ACTIVITY_PARTICIPATION_KEY + user.getId());
        if (LockHolder.tryLock(lock, 0, 10)) {
            Long activityId = runActivityManager.enrollingNewUserActivity(runActivityConfig, user, equipmentNo);
            if (Objects.nonNull(activityId)) {
                //创建任务列表
                runActivityUserTaskService.addUserTasks(user.getId(), runActivityConfig.getActivityConfig(), activityId, 6);
            }
            return CommonResult.success();
        } else {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.signup.newcomer.activity"));
        }
    }

    /**
     * 检查是否可以执行任务
     *
     * @param req
     * @return
     */
    @PostMapping("/checkExecuteTask")
    public Result checkExecuteTask(@RequestBody AddressLibReq req) {
        ZnsUserEntity user = getLoginUser();

        //查询任务
        RunActivityUserTask userTask = runActivityUserTaskService.selectRunActivityUserTaskByLevel(user.getId(), req.getLevel(), 6);
        if (Objects.isNull(userTask)) {
            log.warn("checkExecuteTask 任务不存在");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        if (userTask.getIsUnlock() == 0) {
            log.warn("checkExecuteTask 任务未解锁");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("task.limit.one.day"));
        }

        ZnsRunActivityEntity newUserActivityInfo = runActivityService.getNewUserActivityInfo(RunActivityTypeEnum.NEW_USER_ACTIVITY.getType(), user.getId());
        if (ZonedDateTime.now().isAfter(newUserActivityInfo.getActivityEndTime())) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("task.event.ended"));
        }

        //检查当天是否已执行任务，每天只能完成一关
        RunActivityUserTask userTaskTime = runActivityUserTaskService.selectRunActivityUserTaskByTime(user.getId(), ZonedDateTime.now(), 6);
        if (Objects.nonNull(userTaskTime)) {
            log.warn("checkExecuteTask 当天已执行任务，每天只能完成一关");
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("task.limit.one.day"));
        }
        return CommonResult.success();
    }

    /**
     * 活动详情
     */
    @PostMapping("/activityDetail")
    public Result<RunActivityDetailVO> activityDetail(@RequestBody ActivityDetailReq req) {
        // 校验活动类型是否存在
        if (null == req.getActivityId()) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "活动ID不存在");
        }
        ZnsUserEntity userEntity = new ZnsUserEntity();
        userEntity.setId(0L);
        userEntity.setMeasureUnit(1);
        // 查询活动详情
        RunActivityDetailVO activityDetailVO = activityStrategyContext.activityDetail(req.getActivityId(), userEntity, null);
        //非官方活动全球化
        List<Integer> activityTypeList = RunActivityTypeEnum.newNoOfficialTypes();
        if (!Objects.equals(activityDetailVO.getActivityTypeSub(), 3)) {
            if (activityTypeList.contains(activityDetailVO.getActivityType())) {
                Currency currency = new Currency();
                activityAwardCurrencyBizService.fillAwardAmountCurrency(currency, activityDetailVO.getActivityId(), userEntity.getId());
                activityDetailVO.setCurrency(currency);
            } else {
                activityDetailVO.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD.getCode()));
            }
        }
        return CommonResult.success(activityDetailVO);
    }

    /**
     * 查询活动队伍信息
     *
     * @param req
     * @return
     */
    @PostMapping("/team/voteInfo/visitor")
    public Result<ActivityTeamVoteInfoResp> getActivityTeamInfo(@Validated @RequestBody ActivityVoteInfoReq req) {
        ActivityTeamVoteInfoResp resp = runActivityManager.getActivityTeamInfo(req);
        return CommonResult.success(resp);
    }

    /**
     * 查询活动队伍信息，已登录就查该接口
     *
     * @param req
     * @return
     */
    @PostMapping("/team/voteInfo")
    public Result<ActivityTeamVoteInfoResp> getActivityTeamVoteInfo(@Validated @RequestBody ActivityVoteInfoReq req) {
        Long userId = getLoginUser().getId();
        ActivityTeamVoteInfoResp resp = runActivityManager.getActivityTeamVoteInfo(req, userId);
        return CommonResult.success(resp);
    }

    /**
     * 线上未使用
     *
     * @param request
     * @return
     */
    @PostMapping("/activityUser")
    public Result activityUser(@RequestBody ActivityRequest request) {
        ZnsUserEntity userEntity = new ZnsUserEntity();
        userEntity.setId(0L);
        userEntity.setMeasureUnit(1);

        ZnsRunActivityEntity activityEntity = runActivityService.findById(request.getActivityId());
        if (null == activityEntity) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "活动不存在");
        }
        Map<String, Object> data = activityUserManager.pageQueryActivityUsers(activityEntity, userEntity.getId(), request.getPageNum(), request.getPageSize(), request.getActivityUserStatus());
        return CommonResult.success(data);
    }

    /**
     * 随机活动推荐
     */
    @PostMapping("/randomActivity")
    public Result randomActivity() {
        ZnsUserEntity loginUser = getLoginUser();
        Map<String, Object> map = new HashMap<>();
        ZnsRunActivityEntity randomActivity = runActivityService.getRandomActivity(ActivityClassifyTypeEnum.TOP_RECOMMENDATION.getType(), loginUser.getEmailAddressEn());
        if (Objects.nonNull(randomActivity)) {
            map.put("activityId", randomActivity.getId());
            map.put("activityTitle", randomActivity.getActivityTitle());
            map.put("activityType", randomActivity.getActivityType());
            Map<String, Object> jsonObject = JsonUtil.readValue(randomActivity.getActivityConfig());
            map.put("maxReward", runActivityBizService.getPreMaxReward(jsonObject, randomActivity.getActivityType(), randomActivity.getUserCount(),
                    BigDecimal.ZERO, null, randomActivity.getId(), loginUser));
        }
        return CommonResult.success(map);
    }

    /**
     * 活动奖励明细数据
     *
     * @param req
     * @return
     */
    @PostMapping("/activityRewardDetail")
    public Result<RunActivityRewardDetailVO> activityRewardDetail(@RequestBody @Validated ActivityDetailReq req) {
        // 查询活动详情
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            loginUser = new ZnsUserEntity();
            loginUser.setId(0L);
        }
        RunActivityRewardDetailVO activityDetailVO = activityStrategyContext.activityRewardDetail(req.getActivityId(), loginUser);
        return CommonResult.success(activityDetailVO);
    }


    /**
     * 返回支持观看的最后一场比赛
     *
     * @return
     */
    @PostMapping("/view")
    public Result<ZnsRunActivityEntity> view(@RequestBody @Validated ActivityDetailReq request) {
        ZnsUserEntity loginUser = getLoginUser();
        ZnsRunActivityEntity znsRunActivityRunning = runActivityService.selectActivityById(request.getActivityId());
        // 根据批次号获取活动列表
        List<ZnsRunActivityEntity> notStartCountList = runActivityService.selectActivityByBatchNoActivityStateDesc(znsRunActivityRunning.getBatchNo(), Arrays.asList(1));
        if (!CollectionUtils.isEmpty(notStartCountList)) {
            // 上面方法默认根据开始时间正排，需要重新排下序
            notStartCountList = notStartCountList.stream().sorted(Comparator.comparing(ZnsRunActivityEntity::getActivityStartTime).reversed()).collect(Collectors.toList());
            Map<String, ZnsRunActivityUserEntity> znsRunActivityUserEntities = runActivityUserService.selectByZnsRunActivityEntity(notStartCountList);
            for (ZnsRunActivityEntity activityEntity : notStartCountList) {
                if (znsRunActivityUserEntities.get(activityEntity.getId() + "_" + loginUser.getId()) == null) {
                    return CommonResult.success(activityEntity);
                }
            }
        }
        return CommonResult.fail(I18nMsgUtils.getMessage("common.race.no.watch"));
    }

    /**
     * H5活动包列表
     *
     * @return
     */
    @PostMapping("/pack/list")
    public Result<List<ActivityPackH5Resp>> packList() {
        ZnsUserEntity loginUser = getLoginUser();
        TimeZoneVo userTimeZone = getUserTimeZone();
        return CommonResult.success(runActivityManager.findActivityPackList(loginUser, userTimeZone.getZoneId()));
    }

    /**
     * H5活动包列表
     *
     * @return
     */
    @PostMapping("/pack/listV2")
    public Result<List<ActivityPackH5Resp>> packListV2() {
        ZnsUserEntity loginUser = getLoginUser();
        TimeZoneVo userTimeZone = getUserTimeZone();
        return CommonResult.success(runActivityManager.findActivityPackListV2(loginUser, userTimeZone.getZoneId()));
    }

    /**
     * 线上未使用
     *
     * @param req
     * @return
     */
    @PostMapping("/pack/open")
    public Result packOpen(@RequestBody OpenActivityPackReq req) {
        ZnsUserEntity loginUser = getLoginUser();
        TimeZoneVo userTimeZone = getUserTimeZone();
        runActivityManager.OpenActivityPack(req, loginUser, userTimeZone.getZoneId());
        return CommonResult.success();
    }

    /**
     * 线上未使用
     *
     * @param req
     * @return
     */
    @PostMapping("/pack/openV2")
    public Result packOpenV2(@RequestBody OpenActivityPackReq req) {
        ZnsUserEntity loginUser = getLoginUser();
        TimeZoneVo userTimeZone = getUserTimeZone();
        runActivityManager.OpenActivityPackV2(req, loginUser, userTimeZone.getZoneId());
        return CommonResult.success();
    }

    /**
     * 线上未使用
     *
     * @param file
     * @return
     */
    @PostMapping("/pack/import")
    public Result importH5FreeUser(@RequestParam(required = true) MultipartFile file) {
        h5ActivityFreeUserService.importFreeUser(file);
        return CommonResult.success();
    }
}
