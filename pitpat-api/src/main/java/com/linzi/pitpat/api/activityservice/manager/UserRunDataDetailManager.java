package com.linzi.pitpat.api.activityservice.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityStageRunData;
import com.linzi.pitpat.api.activityservice.dto.response.GameActivityResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameActivityRunDataResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameActivityUserDataDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameActivityUserRunDataDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameLaActivityRunDataResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameUserRunDataDto;
import com.linzi.pitpat.api.activityservice.dto.response.UserGameReplayData;
import com.linzi.pitpat.api.activityservice.dto.response.UserGameReplayUserData;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.enums.FreeActivityModeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.EntryGameplay;
import com.linzi.pitpat.data.activityservice.model.entity.FreeActivityConfig;
import com.linzi.pitpat.data.activityservice.model.entity.RunActivityStageUser;
import com.linzi.pitpat.data.activityservice.model.entity.RunTemplateDo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsSecondEntity;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityUserPageQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.EntryGameplayService;
import com.linzi.pitpat.data.activityservice.service.RunActivityStageUserService;
import com.linzi.pitpat.data.activityservice.service.RunTemplateService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsSecondService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.entity.dto.GameInfoDto;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelLogDo;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillWhiteListService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelLogQuery;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserRunDataDetailManager {

    private final ZnsUserRunDataDetailsService userRunDataDetailsService;

    private final UserRunDataDetailsSecondService userRunDataDetailsSecondService;

    private final MainActivityBizService mainActivityBizService;

    private final ZnsUserService userService;

    private final RunActivityManager activityManager;

    private final RunActivityStageUserService runActivityStageUserService;

    private final EntryGameplayService entryGameplayService;

    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final ActivityParamsService activityParamsService;
    private final RunTemplateService runTemplateService;
    private final TreadmillWhiteListService treadmillWhiteListService;

    private final UserPlacementLevelLogService userPlacementLevelLogService;

    public List<ZnsUserRunDataDetailsEntity> queryUserRunTimesData(Long activityId, String userEmail) {
        if (!StringUtils.hasText(userEmail)) {
            throw new BizException("Email Is Null");
        }
        ZnsUserEntity byEmail = userService.findByEmail(userEmail);
        if (byEmail == null) {
            throw new BizException("Not Found User");
        }
        return userRunDataDetailsService.getUserDetailByActivityId(byEmail.getId(), activityId);
    }

    public UserGameReplayData queryUserGameReplayData(Long detailId) {
        //本次跑步信息
        ZnsUserRunDataDetailsEntity byId = userRunDataDetailsService.findById(detailId);
        if (byId == null) {
            throw new BizException("Not Found Detail");
        }
        //查询统一事件一起玩的用户
        List<ZnsUserRunDataDetailsEntity> allUserDetails = userRunDataDetailsService.findPlayUserDetailIds(detailId);

        UserGameReplayData result = new UserGameReplayData();
        result.setDetailId(detailId);
        result.setRouteId(byId.getRouteId());
        result.setUserId(byId.getUserId());
        result.setActivityId(byId.getActivityId());
        Map<Long, List<UserGameReplayUserData>> data = new HashMap<>();
        result.setData(data);
        List<ZnsUserRunDataDetailsSecondEntity> secondsList = userRunDataDetailsSecondService.getSecondsList(detailId);
        ZonedDateTime baseTime = byId.getCreateTime();
        Integer masterRunTime = byId.getRunTime();
        data.put(byId.getUserId(), composedUserData(baseTime, masterRunTime, baseTime, secondsList, true));
        if (!CollectionUtils.isEmpty(allUserDetails)) {
            for (ZnsUserRunDataDetailsEntity otherUser : allUserDetails) {
                if (data.containsKey(otherUser.getUserId())) {
                    continue;
                }
                List<ZnsUserRunDataDetailsSecondEntity> otherSecond = userRunDataDetailsSecondService.getSecondsList(otherUser.getId());
                data.put(otherUser.getUserId(), composedUserData(baseTime, masterRunTime, otherUser.getCreateTime(), otherSecond, false));
            }
        }
        //设置规则相关数据
        result.setRule(activityManager.getActivityTypeRule(byId.getActivityId(), null, null));
        //设置游戏开场需要的数据
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(byId.getActivityId(), byId.getZoneId());
        ZnsUserEntity user = userService.findById(byId.getUserId());
        GameInfoDto gameInfoDto = activityManager.createGameInfo(user, activityNew);
        result.setGameInfoDto(gameInfoDto);
        return result;
    }

    private List<UserGameReplayUserData> composedUserData(ZonedDateTime baseTime, Integer masterRunTime, ZonedDateTime userStartTime, List<ZnsUserRunDataDetailsSecondEntity> secondsList, boolean isMaster) {
        List<UserGameReplayUserData> result = new ArrayList<>();
        long masterTimeLine = baseTime.toInstant().toEpochMilli();
        long masterFinishLine = masterTimeLine + masterRunTime * 1000;
        long diffTime = -1;
        BigDecimal currentVelocity = new BigDecimal(0);
        for (ZnsUserRunDataDetailsSecondEntity second : secondsList) {
            long timeLine = userStartTime.toInstant().toEpochMilli() + second.getRunTime() * 1000L;
            if (masterTimeLine > timeLine) {
                //主开始前skip
                continue;
            }
            if (timeLine > masterFinishLine) {
                //master is finish ,skip
                continue;
            }
            if (-1 == diffTime) {
                //初始化当前用户的时间线
                diffTime = (timeLine - masterTimeLine) / 1000;
            }
            if (!currentVelocity.equals(second.getVelocity())) {
                currentVelocity = second.getVelocity();
                UserGameReplayUserData item = new UserGameReplayUserData();
                item.setBaseTime(diffTime + second.getRunTime() - 1);
                item.setRunTime(second.getRunTime());
                item.setVelocity(currentVelocity);
                item.setMileage(second.getMileage());
                result.add(item);
            }
        }
        if (isMaster) {
            //主角 增加一个最后的结束节点
            UserGameReplayUserData item = new UserGameReplayUserData();
            ZnsUserRunDataDetailsSecondEntity lastSecond = secondsList.get(secondsList.size() - 1);
            item.setBaseTime(diffTime + lastSecond.getRunTime() - 1);
            item.setRunTime(lastSecond.getRunTime());
            item.setVelocity(new BigDecimal(0));
            item.setMileage(lastSecond.getMileage());
            result.add(item);
        }
        return result;
    }

    public ActivityStageRunData queryUserActivityStageReplayData(Long activityId, ZnsUserEntity user) {
        ActivityStageRunData result = new ActivityStageRunData();
        Map<Long, List<UserGameReplayUserData>> data = new HashMap<>();

        List<RunActivityStageUser> activityStageUsers = runActivityStageUserService.findByActId(activityId);
        Map<Long, Long> bestGradeMap = activityStageUsers.stream()
                .filter(k -> !k.getUserId().equals(user.getId()))
                .filter(k -> k.getIsBest() == 1 && k.getRunDataDetailsId() != null).collect(Collectors.toMap(RunActivityStageUser::getUserId, RunActivityStageUser::getRunDataDetailsId, (k1, k2) -> k2));

        bestGradeMap.forEach((userId, detailId) -> {
            List<ZnsUserRunDataDetailsSecondEntity> secondsList = userRunDataDetailsSecondService.getSecondsList(detailId);
            List<UserGameReplayUserData> userDataList = new ArrayList<>();
            BigDecimal preVelocity = BigDecimal.ZERO;
            for (ZnsUserRunDataDetailsSecondEntity second : secondsList) {
                BigDecimal currentVelocity = second.getVelocity();
                if (preVelocity.compareTo(currentVelocity) != 0) {
                    UserGameReplayUserData userGameReplayUserData = new UserGameReplayUserData();
                    userGameReplayUserData.setRunTime(second.getRunTime());
                    userGameReplayUserData.setVelocity(second.getVelocity());
                    userGameReplayUserData.setMileage(second.getMileage());
                    userDataList.add(userGameReplayUserData);
                    //记录变化速度
                    preVelocity = currentVelocity;
                }
            }
            data.put(userId, userDataList);
        });
        result.setData(data);
        return result;

    }

    public GameActivityRunDataResponseDto queryUserActivityReplayData(Long activityId, ZnsUserEntity loginUser) {
        GameActivityRunDataResponseDto gameActivityRunDataResponseDto = new GameActivityRunDataResponseDto();
        List<GameActivityUserRunDataDto> userRunDataList = new ArrayList<>();
        List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = znsRunActivityUserService.selectUserByActivityId(activityId);
        znsRunActivityUserEntities = znsRunActivityUserEntities.stream()
                .filter(k -> !k.getUserId().equals(loginUser.getId()))
                .filter(k -> k.getIsComplete() == 1 && k.getRunDataDetailsId() != null)
                .collect(Collectors.toList());
        znsRunActivityUserEntities.forEach(activityUser -> {
            var gameActivityUserRunDataDto = new GameActivityUserRunDataDto();
            gameActivityUserRunDataDto.setUserId(activityUser.getUserId());
            gameActivityUserRunDataDto.setBestRunTime(activityUser.getRunTime());
            gameActivityUserRunDataDto.setBestMileage(activityUser.getRunMileage());
            List<ZnsUserRunDataDetailsSecondEntity> secondsList = userRunDataDetailsSecondService.getSecondsList(activityUser.getRunDataDetailsId());
            List<UserGameReplayUserData> userDataList = new ArrayList<>();
            BigDecimal preVelocity = BigDecimal.ZERO;
            List<UserGameReplayUserData> runDatas = new ArrayList<>();
            for (ZnsUserRunDataDetailsSecondEntity second : secondsList) {
                BigDecimal currentVelocity = second.getVelocity();
                if (preVelocity.compareTo(currentVelocity) != 0) {
                    UserGameReplayUserData userGameReplayUserData = new UserGameReplayUserData();
                    userGameReplayUserData.setRunTime(second.getRunTime());
                    userGameReplayUserData.setVelocity(second.getVelocity());
                    userGameReplayUserData.setMileage(second.getMileage());
                    userDataList.add(userGameReplayUserData);
                    //记录变化速度
                    preVelocity = currentVelocity;
                    runDatas.add(userGameReplayUserData);
                }
            }
            gameActivityUserRunDataDto.setRunData(runDatas);
            userRunDataList.add(gameActivityUserRunDataDto);
        });
        gameActivityRunDataResponseDto.setUserRunDataList(userRunDataList);
        return gameActivityRunDataResponseDto;
    }

    public GameActivityResponseDto queryActivityData(Long activityId, ZnsUserEntity loginUser) {
        GameActivityResponseDto responseDto = new GameActivityResponseDto();
        var activityNew = mainActivityBizService.getActivityNew(activityId, loginUser.getZoneId());
        if(Objects.isNull(activityNew)){
            return responseDto;
        }
        responseDto.setMainType(activityNew.getMainType());
        if(Objects.isNull(activityNew.getMainActivity())){
            return responseDto;
        }
        Long playId = activityNew.getMainActivity().getPlayId();
        EntryGameplay entryGameplay = entryGameplayService.findByGameplayId(playId);
        responseDto.setEntryCount(entryGameplay.getEntryCount());
        responseDto.setFetchRule(entryGameplay.getFetchRule());
        responseDto.setTargetType(entryGameplay.getTargetType());
        responseDto.setRankingBy(entryGameplay.getRankingBy());
        responseDto.setCompetitionFormat(entryGameplay.getCompetitionFormat());
        return responseDto;
    }
    public GameLaActivityRunDataResponseDto queryLaActivityReplayData(Long activityId, ZnsUserEntity loginUser) {
        var gameLaActivityRunDataResponseDto = new GameLaActivityRunDataResponseDto();
        FreeActivityConfig config = activityParamsService.findCacheOne(activityId, ActivitySettingConfigEnum.FREE_ACTIVITY_CONFIG, FreeActivityConfig.class);
        if (Objects.nonNull(config)) {
            if (config.getMode().equals(FreeActivityModeEnum.PROP.getCode())) {
                return gameLaActivityRunDataResponseDto;
            }
            Long rubTemplateId = config.getRubTemplateId();
            RunTemplateDo runTemplateDo = runTemplateService.findById(rubTemplateId);
            // 超越目标数据
            if(Objects.nonNull(runTemplateDo)){
                GameActivityUserDataDto gameActivityUserDataDto = new GameActivityUserDataDto();
                gameActivityUserDataDto.setDetailId(runTemplateDo.getDetailId());
                gameLaActivityRunDataResponseDto.setExceedTargetUserInfo(gameActivityUserDataDto);
            }
        }
        //获取排行榜数据 随机2个
        List<GameActivityUserDataDto> rankUserInfoList = new ArrayList<>();
        RunActivityUserPageQuery pageQuery = RunActivityUserPageQuery.builder().activityId(activityId).isComplete(1).minRank(0).build();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(10);
        pageQuery.setOrders(ActivityConstants.FREE_CHALLENGE_RANK_COMPETE);
        Page<ZnsRunActivityUserEntity> userPage = znsRunActivityUserService.findPage(pageQuery);
        List<ZnsRunActivityUserEntity> records = userPage.getRecords();
        if(!CollectionUtils.isEmpty(records)){
            Collections.shuffle(records);
            // 直接截取前2个元素（自动处理不足2个的情况）
            List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = records.subList(0, Math.min(2, records.size()));
            List<GameActivityUserDataDto> gameActivityUserDataDtos = znsRunActivityUserEntities.stream().map(k -> {
                GameActivityUserDataDto gameActivityUserDataDto = new GameActivityUserDataDto();
                gameActivityUserDataDto.setDetailId(k.getRunDataDetailsId());
                gameActivityUserDataDto.setUserId(k.getUserId());
                return gameActivityUserDataDto;
            }).toList();
            rankUserInfoList.addAll(gameActivityUserDataDtos);
        }
        // 其他排名用户 3个
        List<ZnsRunActivityUserEntity> list = znsRunActivityUserService.findAllActivityUser(activityId).stream()
                .filter(k -> k.getIsComplete() == 1)
                .filter(k -> k.getRank() >= 0)
                .filter(k -> !rankUserInfoList.stream()
                        .map(GameActivityUserDataDto::getUserId)
                        .collect(Collectors.toSet())
                        .contains(k.getUserId())) // 过滤掉已存在的用户ID
                .collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(list)){
            // 直接截取前3个元素（自动处理不足3个的情况）
            Collections.shuffle(list);
            List<ZnsRunActivityUserEntity> znsRunActivityUserEntities = list.subList(0, Math.min(3, list.size()));
            List<GameActivityUserDataDto> gameActivityUserDataDtos = znsRunActivityUserEntities.stream().map(k -> {
                GameActivityUserDataDto gameActivityUserDataDto = new GameActivityUserDataDto();
                gameActivityUserDataDto.setDetailId(k.getRunDataDetailsId());
                gameActivityUserDataDto.setUserId(k.getUserId());
                return gameActivityUserDataDto;
            }).toList();
            rankUserInfoList.addAll(gameActivityUserDataDtos);
        }
        gameLaActivityRunDataResponseDto.setRankUserInfoList(rankUserInfoList);
        return gameLaActivityRunDataResponseDto;
    }

    public GameUserRunDataDto queryReplayDataByDetailId(Long detailId, ZnsUserEntity loginUser) {
        var gameUserRunDataDto = new GameUserRunDataDto();
        ZnsUserRunDataDetailsEntity detailsEntity = userRunDataDetailsService.findById(detailId);
        if(Objects.isNull(detailsEntity)){
            return gameUserRunDataDto;
        }
        gameUserRunDataDto.setUserId(detailsEntity.getUserId());
        gameUserRunDataDto.setUserLaType(1);
        // LA 设备判定
        Long treadmillId = detailsEntity.getTreadmillId();
        if(treadmillWhiteListService.checkEquipmentWhiteList(treadmillId)){
            gameUserRunDataDto.setUserLaType(2);
        }

        List<ZnsUserRunDataDetailsSecondEntity> secondsList = userRunDataDetailsSecondService.getSecondsList(detailId);
        List<UserGameReplayUserData> userDataList = new ArrayList<>();
        BigDecimal preVelocity = BigDecimal.ZERO;
        List<UserGameReplayUserData> runDatas = new ArrayList<>();
        for (ZnsUserRunDataDetailsSecondEntity second : secondsList) {
            BigDecimal currentVelocity = second.getVelocity();
            if (preVelocity.compareTo(currentVelocity) != 0) {
                UserGameReplayUserData userGameReplayUserData = new UserGameReplayUserData();
                userGameReplayUserData.setRunTime(second.getRunTime());
                userGameReplayUserData.setVelocity(second.getVelocity());
                userGameReplayUserData.setMileage(second.getMileage());
                userDataList.add(userGameReplayUserData);
                //记录变化速度
                preVelocity = currentVelocity;
                runDatas.add(userGameReplayUserData);
            }
        }
        gameUserRunDataDto.setRunData(runDatas);
        return gameUserRunDataDto;
    }

    public GameActivityRunDataResponseDto queryUserPlacementActivityReplayData(Long activityId, ZnsUserEntity loginUser) {
        GameActivityRunDataResponseDto gameActivityRunDataResponseDto = new GameActivityRunDataResponseDto();
        List<GameActivityUserRunDataDto> userRunDataList = new ArrayList<>();
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(activityId,loginUser.getZoneId());
        if (activityNew.getActivityType().equals(MainActivityTypeEnum.PLACEMENT.getOldType())) {
            ZnsRunActivityUserEntity activityUser = znsRunActivityUserService.findActivityUser(activityId, loginUser.getId());
            UserPlacementLevelLogQuery userPlacementLevelLogQuery = new UserPlacementLevelLogQuery();
            userPlacementLevelLogQuery.setUserId(loginUser.getId());
            userPlacementLevelLogQuery.setScenario(loginUser.getGender());
            userPlacementLevelLogQuery.setCurrentAcitivytId(activityId);
            userPlacementLevelLogQuery.setTargetMileage(activityUser.getTargetRunMileage());
            userPlacementLevelLogQuery.addOrderByDesc("id");
            UserPlacementLevelLogDo userPlacementLevelLogDo = userPlacementLevelLogService.findByQuery(userPlacementLevelLogQuery);
            Long detailId = userPlacementLevelLogDo.getDetailId();
            var gameActivityUserRunDataDto = new GameActivityUserRunDataDto();
            gameActivityUserRunDataDto.setUserId(loginUser.getId());
            List<ZnsUserRunDataDetailsSecondEntity> secondsList = userRunDataDetailsSecondService.getSecondsList(detailId);
            List<UserGameReplayUserData> userDataList = new ArrayList<>();
            BigDecimal preVelocity = BigDecimal.ZERO;
            List<UserGameReplayUserData> runDatas = new ArrayList<>();
            for (ZnsUserRunDataDetailsSecondEntity second : secondsList) {
                BigDecimal currentVelocity = second.getVelocity();
                if (preVelocity.compareTo(currentVelocity) != 0) {
                    UserGameReplayUserData userGameReplayUserData = new UserGameReplayUserData();
                    userGameReplayUserData.setRunTime(second.getRunTime());
                    userGameReplayUserData.setVelocity(second.getVelocity());
                    userGameReplayUserData.setMileage(second.getMileage());
                    userDataList.add(userGameReplayUserData);
                    //记录变化速度
                    preVelocity = currentVelocity;
                    runDatas.add(userGameReplayUserData);
                }
            }
            gameActivityUserRunDataDto.setRunData(runDatas);
            userRunDataList.add(gameActivityUserRunDataDto);
        }
        gameActivityRunDataResponseDto.setUserRunDataList(userRunDataList);
        return gameActivityRunDataResponseDto;
    }
}
