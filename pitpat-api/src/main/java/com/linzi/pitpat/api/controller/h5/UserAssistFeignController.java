package com.linzi.pitpat.api.controller.h5;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.api.model.req.AssistInitiatorDetailRequest;
import com.linzi.pitpat.api.model.req.HelpFriendAssistanceRequest;
import com.linzi.pitpat.api.model.req.IsHelpFriendAssistSuccessRequest;
import com.linzi.pitpat.api.model.resp.AssistInitiatorDetailResp;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.dto.UserAssistSearchDto;
import com.linzi.pitpat.data.activityservice.model.entity.AssistActivitity;
import com.linzi.pitpat.data.activityservice.model.entity.AssistInitiatorReward;
import com.linzi.pitpat.data.activityservice.model.entity.UserAssist;
import com.linzi.pitpat.data.activityservice.model.entity.UserAssistRecord;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.AssistActivitityQuery;
import com.linzi.pitpat.data.activityservice.model.query.RunActivityQuery;
import com.linzi.pitpat.data.activityservice.service.AssistActivitityService;
import com.linzi.pitpat.data.activityservice.service.AssistInitiatorRewardService;
import com.linzi.pitpat.data.activityservice.service.UserAssistRecordService;
import com.linzi.pitpat.data.activityservice.service.UserAssistService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.biz.CurrencyBizService;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.GamePushUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 暴露给独立站的接口
 *
 * <AUTHOR> 李兴海
 * @date : 2023/5/9 9:47
 */
@Slf4j
@RestController
@RequestMapping("/h5")
public class UserAssistFeignController extends BaseH5Controller {

    @Resource
    private ZnsUserService znsUserService;

    @Resource
    private UserAssistService userAssistService;

    @Resource
    private AssistActivitityService assistActivitityService;

    @Resource
    private UserAssistRecordService userAssistRecordService;

    @Resource
    private AssistInitiatorRewardService assistInitiatorRewardService;

    @Resource
    private ZnsRunActivityService runActivityService;

    @Resource
    private UserCouponService userCouponService;

    @Resource
    private ZnsUserEquipmentService userEquipmentService;

    private static final int MAX_ASSIST_TIMES = 5;

    @Value("${admin.server.gamepush}")
    private String gameDomain;

    @Resource
    private AppMessageService appMessageService;

    @Resource
    private CouponService couponService;

    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private CurrencyBizService currencyBizService;
    @Resource(name = "asyncExecutor")
    private Executor executor;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取助力发起者详情数据
     *
     * @param assistInitiatorDetailRequest
     * @return
     */
    @PostMapping("/assist/getAssistInitiatorDetail")
    public Result getAssistInitiatorDetail(@RequestBody AssistInitiatorDetailRequest assistInitiatorDetailRequest) {
        log.info("请求参数为:{}", assistInitiatorDetailRequest);
        AssistInitiatorDetailResp assistInitiatorDetailResp = new AssistInitiatorDetailResp();
        Integer assistActivityLinkPeriod = null;

        String inviteCode = assistInitiatorDetailRequest.getInviteCode();
        if (!StringUtils.hasText(inviteCode)) {
            log.error("assist activity invite code is empty!");
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.not.exist"));
        }
        // 参数验证:
        // 查询发起的用户助力记录

        UserAssist userAssist = userAssistService.findByInviteCode(inviteCode);
        if (userAssist == null) {
            log.error("assist activity invite is not existed");
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemError"));
        }

        Long runActivityId = userAssist.getRunActivityId();
        if (runActivityId != null) {
            ZnsRunActivityEntity znsRunActivity = runActivityService.findOne(RunActivityQuery.builder()
                    .select(List.of(ZnsRunActivityEntity::getId,
                            ZnsRunActivityEntity::getAssistActivityLinkPeriod))
                    .isDelete(0).status(1).id(runActivityId)
                    .build());
            if (znsRunActivity == null) {
                log.error("run activity is not existed!");
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemError"));
            }
            assistActivityLinkPeriod = znsRunActivity.getAssistActivityLinkPeriod();
        }

        Long assistInitiatorId = userAssist.getAssistInitiatorId();

        AssistActivitity assistActivitity = assistActivitityService.findOne(AssistActivitityQuery.builder().isDelete(0).status(1).id(userAssist.getAssistActivityId()).build());
        if (assistActivitity == null) {
            log.error("assist activity is not existed!");
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.systemError"));
        }

        ZonedDateTime createTime = userAssist.getCreateTime();
        long time = createTime.toInstant().toEpochMilli();
        if (assistActivityLinkPeriod != null) {
            time += assistActivityLinkPeriod * 60 * 60 * 1000;
            long currentTimeMillis = System.currentTimeMillis();
            log.info("系统当前时间为:{},邀请的创建时间为:{},过期时间长度为:{}", new Date(currentTimeMillis), createTime, assistActivityLinkPeriod);
            if (currentTimeMillis > time) {
                assistInitiatorDetailResp.setShareUrlIsExpire(Boolean.TRUE);
                return CommonResult.fail(I18nMsgUtils.getMessage("assist.link.expired"));
            } else {
                assistInitiatorDetailResp.setShareUrlIsExpire(Boolean.FALSE);
            }
        }

        assistInitiatorDetailResp.setUserAssistId(userAssist.getId());
        // 获取助力发起人用户信息
        ZnsUserEntity user = znsUserService.findById(assistInitiatorId);
        String lastName = user.getFirstName();
        String headPortrait = user.getHeadPortrait();
        Long id = user.getId();
        assistInitiatorDetailResp.setLastName(lastName);
        assistInitiatorDetailResp.setHeadPortrait(headPortrait);
        assistInitiatorDetailResp.setAssistInitiatorUserId(id);
        assistInitiatorDetailResp.setSingleLuckyDrawId(assistActivitity.getSingleLuckyDrawId());
        assistInitiatorDetailResp.setInviteFriendHelpId(userAssist.getId());
        assistInitiatorDetailResp.setUserAssistId(userAssist.getId());
        return CommonResult.success(assistInitiatorDetailResp);
    }

    /**
     * 帮助朋友进行助力
     *
     * @return
     */
    @PostMapping("/assist/help")
    public Result helpFriendAssistance(@RequestBody HelpFriendAssistanceRequest helpFriendAssistanceRequest) {
        log.info("请求参数为:{}", helpFriendAssistanceRequest);

        Long userAssistId = helpFriendAssistanceRequest.getUserAssistId();
        if (userAssistId == null) {
            log.error("user assist id is empty");
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        String helperId = helpFriendAssistanceRequest.getHelperId();
        if (!StringUtils.hasText(helperId)) {
            log.error("helper id is empty");
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        UserAssist userAssist = userAssistService.findById(userAssistId);

        if (userAssist == null) {
            log.error("assist invite is not existed");
            return CommonResult.fail(UserError.GENERAL_SYSTEM_ERROR.getMsg());
        }

        Long runActivityId = userAssist.getRunActivityId();
        ZnsRunActivityEntity runActivityEntity;
        if (runActivityId != null) {
            runActivityEntity = runActivityService.findOne(RunActivityQuery.builder()
                    .select(List.of(ZnsRunActivityEntity::getId, ZnsRunActivityEntity::getActivityTitle))
                    .id(runActivityId).status(1).isDelete(0)
                    .build());
            if (runActivityEntity == null) {
                log.error("run activity is not existed or deleted");
                return CommonResult.fail(UserError.GENERAL_SYSTEM_ERROR.getMsg());
            }
        } else {
            runActivityEntity = null;
        }

        Long assistActivityId = userAssist.getAssistActivityId();
        if (assistActivityId == null) {
            log.error("assist invite id is not existed");
            return CommonResult.fail(UserError.GENERAL_SYSTEM_ERROR.getMsg());
        }

        AssistActivitity assistActivitity = assistActivitityService.findOne(AssistActivitityQuery.builder().isDelete(0).status(1).id(assistActivityId).build());
        if (assistActivitity == null) {
            log.error("assist activity is not existed");
            return CommonResult.fail(UserError.GENERAL_SYSTEM_ERROR.getMsg());
        }

        String key = RedisConstants.INVITE_FRIEND_HELP_ID + userAssistId + "#" + RedisConstants.HELPER_ID + helperId;
        RLock lock = redissonClient.getLock(key);
        try {
            if (!lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                return CommonResult.fail(CommonError.SYSTEM_ERROR.getMsg());
            }
            long before = System.currentTimeMillis();
            UserAssistSearchDto userAssistSearchDto = new UserAssistSearchDto();
            userAssistSearchDto.setAssistInitiatorId(userAssist.getAssistInitiatorId());
            userAssistSearchDto.setAssistActivityId(assistActivityId);
            userAssistSearchDto.setRunActivityId(runActivityId);
            userAssistSearchDto.setHelperId(helperId);
            int userAssistByCondition = userAssistService.getUserAssistByCondition(userAssistSearchDto);
            if (userAssistByCondition > 0) {
                log.info("you have asstist your friend successfully!");
                return CommonResult.success(Boolean.TRUE);
            }
            UserAssist updateUserAssist = new UserAssist();
            updateUserAssist.setId(userAssistId);
            Integer count = userAssist.getActualAssistTimes();
            updateUserAssist.setActualAssistTimes(count == null ? 1 : count + 1);
            userAssistService.update(updateUserAssist);

            // 求和

            UserAssist assistServiceOne = userAssistService.findOneByUserAndActivity(userAssist.getAssistInitiatorId(),
                    assistActivityId, runActivityId);
            Integer actualAssistTimes = 0;
            if (assistServiceOne != null && assistServiceOne.getActualAssistTimes() != null) {
                actualAssistTimes = assistServiceOne.getActualAssistTimes();
            }

            // 记录助力日志记录信息,并记录文案
            UserAssistRecord userAssistRecord = new UserAssistRecord();
            userAssistRecord.setHelperId(helperId);
            userAssistRecord.setUserAssistId(userAssistId);
            String helpCopyWriting = helpFriendAssistanceRequest.getHelpCopyWriting();
            userAssistRecord.setHelpCopyWriting(helpCopyWriting);
            userAssistRecord.setAssistChannel(helpFriendAssistanceRequest.getAssistChannel());
            userAssistRecordService.save(userAssistRecord);

            // 发起人助力记录表中的实际助力次数需要进行更新,并且最多达到最大助力次数,此后不在叠加
            List<AssistInitiatorReward> assistInitiatorRewardList = assistInitiatorRewardService.list(
                    new QueryWrapper<AssistInitiatorReward>().lambda()
                            .eq(AssistInitiatorReward::getIsDelete, 0)
                            .eq(AssistInitiatorReward::getAssistActivitityId, assistActivityId)
            );
            if (CollectionUtils.isEmpty(assistInitiatorRewardList)) {
                log.error("Assist Initiator Reward List is empty!");
                return CommonResult.fail(UserError.GENERAL_SYSTEM_ERROR.getMsg());
            }
            List<Coupon> couponList = couponService.findListByStatus(1);
            Map<Integer, Long> assistTimes2CouponIdMap = assistInitiatorRewardList.stream().collect(Collectors.toMap(AssistInitiatorReward::getAssistTimes, AssistInitiatorReward::getCouponId));
            Map<Long, Coupon> couponId2CouponMap = couponList.stream().collect(Collectors.toMap(Coupon::getId, coupon -> coupon));
            // 获取最大助力次数
            int maxAssistTimes = MAX_ASSIST_TIMES;
            if (!CollectionUtils.isEmpty(assistInitiatorRewardList)) {
                maxAssistTimes = assistInitiatorRewardList
                        .stream()
                        .max(Comparator.comparingInt(AssistInitiatorReward::getAssistTimes))
                        .map(AssistInitiatorReward::getAssistTimes)
                        .orElseGet(() -> MAX_ASSIST_TIMES);
            }
            if (actualAssistTimes > maxAssistTimes) {
                return CommonResult.success(Boolean.TRUE);
            }
            // 当助力成功之后,发送站内信
            Integer finalActualAssistTimes = actualAssistTimes;

            String initiatorUserId = "administrator";
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
            executor.execute(() -> {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                Long assistInitiatorId = userAssist.getAssistInitiatorId();
                List<Long> userIds = Lists.newArrayList(assistInitiatorId);
                String activityTitle;
                if (runActivityEntity != null && runActivityEntity.getActivityTitle() != null) {
                    activityTitle = runActivityEntity.getActivityTitle();
                } else {
                    activityTitle = "";
                }
                String tencentImConstant = TencentImConstant.TIM_CUSTOM_ELEM;
                String batchNumber = "";
                Map<String, Object> paramMap = Maps.newHashMap();
                paramMap.put("activityId", runActivityId);

                String content = "Your friend has successfully prop-up for you at " + activityTitle + " events.";
                ImMessageBo imMessageBo = new ImMessageBo();
                imMessageBo.setJumpType("0");
                imMessageBo.setParams(paramMap);
                imMessageBo.setJumpValue(assistActivitity.getActivityUrl() + "?activityId=" + runActivityId);
                imMessageBo.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202305/izl13slseh5R3048.png");
                imMessageBo.setMsg(content);
                // 助力成功发送一次站内信
                appMessageService.sendIm(initiatorUserId, userIds, JsonUtil.writeString(imMessageBo), tencentImConstant, batchNumber, 0, Boolean.FALSE);

                if (StringUtils.hasText(helpCopyWriting)) {
                    content = "Receive messages from friends: " + helpCopyWriting;
                    ImMessageBo imMessageBo1 = new ImMessageBo();
                    imMessageBo1.setJumpType("0");
                    imMessageBo.setParams(paramMap);
                    imMessageBo1.setJumpValue(assistActivitity.getActivityUrl() + "?activityId=" + runActivityId);
                    imMessageBo1.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202305/i2V13sjnxiGD7261.png");
                    imMessageBo1.setMsg(content);
                    appMessageService.sendIm(initiatorUserId, userIds, JsonUtil.writeString(imMessageBo1), tencentImConstant, batchNumber, 0, Boolean.FALSE);

                    if (runActivityId != null) {

                        ZnsUserRunDataDetailsEntity userRunDataDetails = userRunDataDetailsService
                                .findAssistInitiatorIdAndActId(userAssist.getAssistInitiatorId(), runActivityId);
                        log.info("用户跑步状态为:{},助力人:{},官方活动:{}", userRunDataDetails != null ? userRunDataDetails.getRunStatus() : null, initiatorUserId, runActivityId);
                        if (userRunDataDetails != null && userRunDataDetails.getRunStatus() == 0) {
                            // 发送弹幕
                            String encode = null;
                            try {
                                content = "Messages: \n" + helpCopyWriting;
                                encode = URLEncoder.encode(content, "UTF-8");
                            } catch (UnsupportedEncodingException e) {
                                log.error("编码错误!", e);
                            }
                            GamePushUtils.popSignleUser(gameDomain, encode, assistInitiatorId, 0);
                        }
                    }
                }
                Long couponId = assistTimes2CouponIdMap.get(finalActualAssistTimes);
                if (couponId != null) {
                    Coupon coupon = couponId2CouponMap.get(couponId);
                    if (coupon != null) {
                        // 给用户发送优惠券
                        CurrencyAmount currencyAmount = currencyBizService.getUserCouponCurrencyAmount(coupon, assistInitiatorId);
                        UserCoupon userCoupon = BeanUtil.copyBean(currencyAmount, UserCoupon.class);
                        userCoupon.setUserId(assistInitiatorId);
                        userCoupon.setCouponId(couponId);
                        userCoupon.setCouponMainType(coupon.getCouponMainType());
                        userCoupon.setGmtStart(coupon.getGmtStart());
                        userCoupon.setGmtEnd(coupon.getGmtEnd());
                        userCoupon.setStatus(0);
                        userCoupon.setSourceType(4);
                        userCoupon.setAmount(currencyAmount.getAmount());
                        userCoupon.setDiscount(coupon.getDiscount());
                        // 获取当前用户的设备信息
                        ZnsUserEquipmentEntity userEquipment = userEquipmentService.getUserEquipmentOne(assistInitiatorId);
                        userCoupon.setEquipmentNo(userEquipment != null ? userEquipment.getEquipmentNo() : null);
                        userCoupon.setActivityId(runActivityEntity != null ? runActivityEntity.getId() : null);
                        userCoupon.setIsNew(1);
                        userCouponService.insert(userCoupon);

                        String title = coupon.getTitle();
                        content = "Congratulations! " + finalActualAssistTimes + " friends have successfully prop up for you, and here is your reward " + title + ".";

                        ImMessageBo imMessageBo3 = new ImMessageBo();
                        imMessageBo3.setJumpType("0");
                        imMessageBo3.setParams(paramMap);
                        imMessageBo3.setJumpValue(assistActivitity.getActivityUrl() + "?activityId=" + runActivityId);
                        imMessageBo3.setImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202305/ivB13sjnywT83787.png");
                        imMessageBo3.setMsg(content);
                        appMessageService.sendIm(initiatorUserId, userIds, JsonUtil.writeString(imMessageBo3), tencentImConstant, batchNumber, 0, Boolean.FALSE);
                    }
                }
            });
            long after = System.currentTimeMillis();
            log.info("助力接口共耗时时间:{}", (after - before) * 1.0 / 1000);
        } catch (Exception e) {
            log.error("助力失败!", e);
        }
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 是否帮助朋友进行助力成功
     *
     * @return
     */
    @PostMapping("/assist/isHelpFriendAssistSuccess")
    public Result isHelpFriendAssistSuccess(@RequestBody IsHelpFriendAssistSuccessRequest isHelpFriendAssistSuccessRequest) {
        log.info("请求参数为:{}", JsonUtil.writeString(isHelpFriendAssistSuccessRequest));
        Long userAssistId = isHelpFriendAssistSuccessRequest.getUserAssistId();
        if (userAssistId == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("assist.user.empty"));
        }
        String helperId = isHelpFriendAssistSuccessRequest.getHelperId();
        if (!StringUtils.hasText(helperId)) {
            return CommonResult.fail(I18nMsgUtils.getMessage("assist.helper.empty"));
        }

        UserAssistRecord assistRecord = userAssistRecordService.findOneByUserAndHelper(userAssistId, helperId);
        if (assistRecord == null) {
            return CommonResult.success(Boolean.FALSE);
        }
        return CommonResult.success(Boolean.TRUE);
    }

}
