package com.linzi.pitpat.api.activityservice.controller.pro;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ProActivityRaceBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.pro.QueryModeType;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.pro.RaceProModuleActivityRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.pro.ApiRaceProActivityPageQueryRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProModuleActivityItemResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProModuleCount;
import com.linzi.pitpat.data.activityservice.dto.api.response.pro.RaceProUserResponse;
import com.linzi.pitpat.data.activityservice.manager.api.ProActivityRaceManager;
import com.linzi.pitpat.data.activityservice.model.query.ProActivityUserCardQuery;
import com.linzi.pitpat.data.activityservice.service.ProActivityUserCardService;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelQuery;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelService;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 4.8 Race 主页面接口
 *
 * @since 4.8.0
 */
@RestController
@Slf4j
@RequestMapping("/app/pro/activity/race")
@RequiredArgsConstructor
public class ProActivityRaceController extends BaseAppController {

    private final ProActivityRaceBizService proActivityRaceBizService;

    private final ProActivityUserCardService proActivityUserCardService;

    private final ProActivityRaceManager proActivityRaceManager;

    private final UserPlacementLevelService userPlacementLevelService;

    /**
     * 职业赛用户信息
     *
     * @return
     */
    @PostMapping("/user")
    public Result<RaceProUserResponse> userInfo() {
        ZnsUserEntity loginUser = getLoginUser();
        RaceProUserResponse userResponse = new RaceProUserResponse();
        userResponse.setUserId(loginUser.getId());
        userResponse.setFirstName(loginUser.getFirstName());
        userResponse.setHeadPortrait(loginUser.getHeadPortrait());
        userResponse.setGender(loginUser.getGender());

        ProActivityUserCardQuery userCardQuery = new ProActivityUserCardQuery();
        userCardQuery.setUserId(loginUser.getId());
        boolean b = proActivityUserCardService.existsByQuery(userCardQuery);
        userResponse.setHaveProActivityCard(b ? 1 : 0);

        userResponse.setHavePlacementLevel(0);
        UserPlacementLevelQuery placementLevelQuery = new UserPlacementLevelQuery();
        placementLevelQuery.setUserId(loginUser.getId());
        UserPlacementLevelDo byQuery = userPlacementLevelService.findByQuery(placementLevelQuery);
        if (byQuery != null) {
            userResponse.setHavePlacementLevel(1);
            userResponse.setPlacementScore(byQuery.getLevelScore());
            userResponse.setPlacementLevelCode(byQuery.getLevelCode());
            userResponse.setHavePlacementLevel(1);
        }
        return CommonResult.success(userResponse);
    }

    /**
     * 模块数量和时间统计
     * 返回数据顺序，就是前端显示的模块顺序
     *
     * @return
     */
    @PostMapping("/moduleCount")
    public Result<List<RaceProModuleCount>> proModuleCount() {
        ApiRaceProActivityPageQueryRequest query = new ApiRaceProActivityPageQueryRequest();
        query.setLanguageCode(getLanguageCode());
        query.setLoginUser(getLoginUser());
        query.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType());
        query.setZoneId(getZoneId());
        query.setQueryModel(QueryModeType.RACE);
        return CommonResult.success(proActivityRaceBizService.proModuleCount(query));
    }

    /**
     * 活动列表
     *
     * @return
     */
    @PostMapping("/list")
    public Result<Page<RaceProModuleActivityItemResponse>> proModuleItemList(@RequestBody @Validated RaceProModuleActivityRequest request) {
        ApiRaceProActivityPageQueryRequest query = new ApiRaceProActivityPageQueryRequest();
        query.setLanguageCode(getLanguageCode());
        query.setLoginUser(getLoginUser());
        query.setEquipmentMainType(request.getEquipmentMainType());
        query.setProActivityType(request.getProActivityType());
        query.setQueryModel(Integer.valueOf(1).equals(request.getIsHome()) ? QueryModeType.HOME : QueryModeType.RACE);
        query.setZoneId(getZoneId());
        query.setShouldShowBullet(request.getShouldShowBullet());
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        return CommonResult.success(proActivityRaceManager.raceActivityList(query));
    }

    /**
     * 历史赛事
     *
     * @param request
     * @return
     */
    @PostMapping("/history")
    public Result<Page<RaceProModuleActivityItemResponse>> proHistoryActivity(@RequestBody @Validated RaceProModuleActivityRequest request) {
        ApiRaceProActivityPageQueryRequest query = new ApiRaceProActivityPageQueryRequest();
        query.setLanguageCode(getLanguageCode());
        query.setLoginUser(getLoginUser());
        query.setZoneId(getZoneId());
        query.setEquipmentMainType(request.getEquipmentMainType());
        query.setProActivityType(request.getProActivityType());
        query.setQueryModel(QueryModeType.HISTORY);
        query.setShouldShowBullet(0);
        query.setPageSize(request.getPageSize());
        query.setPageNum(request.getPageNum());
        return CommonResult.success(proActivityRaceManager.raceActivityList(query));
    }

//    @PostMapping("/historyTabCount")
//    public Result<List<RaceProModuleCount>> historyTabCount() {
//        ApiRaceProActivityPageQueryRequest query = new ApiRaceProActivityPageQueryRequest();
//        query.setLanguageCode(getLanguageCode());
//        query.setLoginUser(getLoginUser());
//        query.setEquipmentMainType(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType());
//        query.setQueryModel(QueryModeType.HISTORY);
//        query.setZoneId(getZoneId());
//        return CommonResult.success(proActivityRaceBizService.proModuleCount(query));
//    }
}
