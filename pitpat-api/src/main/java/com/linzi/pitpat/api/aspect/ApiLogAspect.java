package com.linzi.pitpat.api.aspect;

import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.aspect.BaseLogAspect;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2021-11-05 14:31
 **/
@Aspect
@Component
@Order(1)
@Slf4j
public class ApiLogAspect extends BaseLogAspect {
    @Resource
    private RedisUtil redisUtil;

    @Override
    protected void appendExtendInfo(StringBuffer sb, HttpServletRequest request) {
        if (request != null) {
            String email = HeaderUtil.getEmail(request);
            String userId = redisUtil.get(ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + email) + "";

            sb.append("appType=").append("2".equals(request.getHeader("appType")) ? "ios" : "android").append(BLANK_SPACE)
                    .append("uuid=").append(request.getHeader("uuid")).append(BLANK_SPACE)
                    .append("materialToken=").append(request.getHeader("materialToken")).append(BLANK_SPACE)
                    .append("device=").append(request.getHeader("deviceName")).append(BLANK_SPACE)
                    .append("appVersion=").append(request.getHeader("appVersion")).append(BLANK_SPACE)
                    .append("sysVersion=").append(request.getHeader("systemVersion")).append(BLANK_SPACE)
                    .append("userId=").append(userId).append(BLANK_SPACE)
                    .append("email=").append(email).append(BLANK_SPACE);
        }
    }
}
