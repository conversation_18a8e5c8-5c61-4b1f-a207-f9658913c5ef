package com.linzi.pitpat.api.mallservice.mananger;


import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.data.awardservice.biz.MallCouponComponent;
import com.linzi.pitpat.data.awardservice.biz.MallCouponConvertComponent;
import com.linzi.pitpat.data.awardservice.biz.MallSkuCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponExchangeFailEnum;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.mallservice.dto.api.request.AppMallRetainPopAddCountRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.AppMallRetainPopListRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.AppMallRetainPopInfoResponseDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.AppMallRetainPopListResponseDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.enums.MallPopTriggerFrequencyEnum;
import com.linzi.pitpat.data.mallservice.model.entity.MallPopRecordDo;
import com.linzi.pitpat.data.mallservice.model.entity.MallRetainPopDo;
import com.linzi.pitpat.data.mallservice.model.entity.MallRetainPopI18nDo;
import com.linzi.pitpat.data.mallservice.model.query.MallPopRecordQuery;
import com.linzi.pitpat.data.mallservice.model.query.MallRetainPopGoodsRelQuery;
import com.linzi.pitpat.data.mallservice.model.query.MallRetainPopI18nQuery;
import com.linzi.pitpat.data.mallservice.model.query.MallRetainPopQuery;
import com.linzi.pitpat.data.mallservice.service.MallPopRecordService;
import com.linzi.pitpat.data.mallservice.service.MallRetainPopGoodsRelService;
import com.linzi.pitpat.data.mallservice.service.MallRetainPopI18nService;
import com.linzi.pitpat.data.mallservice.service.MallRetainPopService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class AppMallRetainPopManager {

    private final MallPopRecordService mallPopRecordService;
    private final MallRetainPopI18nService mallRetainPopI18nService;
    private final MallRetainPopService mallRetainPopService;
    private final MallRetainPopGoodsRelService mallRetainPopGoodsRelService;
    private final MallSkuCouponBizService mallCouponBizService;
    private final MallCouponComponent mallCouponComponent;
    private final MallCouponConvertComponent mallCouponConvertComponent;
    private final CouponService couponService;


    public AppMallRetainPopListResponseDto getInfoByPage(AppMallRetainPopListRequestDto requestDto, ZnsUserEntity user) {
        AppMallRetainPopListResponseDto responseDto = new AppMallRetainPopListResponseDto();
        MallRetainPopQuery popQuery = new MallRetainPopQuery();
        popQuery.setTriggerPageType(requestDto.getTriggerPageType());
        popQuery.setState(YesNoStatus.YES.getCode());
        popQuery.setIsValidTime(true);
        List<MallRetainPopDo> retainPopDoList = mallRetainPopService.findList(popQuery);
        if (CollectionUtils.isEmpty(retainPopDoList)) {
            return responseDto;
        }
        List<MallRetainPopDo> allGoodsPopList = retainPopDoList.stream().filter(mallRetainPopDo -> mallRetainPopDo.getGoodsRange() == 0).collect(Collectors.toList());
        List<MallRetainPopDo> partGoodsPopList = retainPopDoList.stream().filter(mallRetainPopDo -> mallRetainPopDo.getGoodsRange() == 1).collect(Collectors.toList());
        retainPopDoList = null;
        List<MallRetainPopDo> stayPopList = new ArrayList<>();
        List<MallRetainPopDo> leavePopList = new ArrayList<>();
        fillStayAndLeavePopList(requestDto, allGoodsPopList, partGoodsPopList, stayPopList, leavePopList);

        Comparator<MallRetainPopDo> comparing = Comparator.comparing(MallRetainPopDo::getPriority, Comparator.reverseOrder()).thenComparing(MallRetainPopDo::getGmtCreate, Comparator.reverseOrder());
        AppMallRetainPopInfoResponseDto stayPopInfo = fillPopInfoResponseDto(user, stayPopList, comparing);
        responseDto.setStayPopInfo(stayPopInfo);
        AppMallRetainPopInfoResponseDto leavePopInfo = fillPopInfoResponseDto(user, leavePopList, comparing);
        responseDto.setLeavePopInfo(leavePopInfo);
        return responseDto;
    }


    /**
     * 填充弹框信息
     *
     * @param user
     * @param popList
     * @param comparing
     * @return
     */
    private AppMallRetainPopInfoResponseDto fillPopInfoResponseDto(ZnsUserEntity user, List<MallRetainPopDo> popList, Comparator<MallRetainPopDo> comparing) {
        AppMallRetainPopInfoResponseDto popInfo = new AppMallRetainPopInfoResponseDto();
        if (!CollectionUtils.isEmpty(popList)) {
            MallRetainPopDo popDo = popList.stream().sorted(comparing).findFirst().orElse(null);
            MallPopRecordQuery mallPopRecordQuery = new MallPopRecordQuery();
            mallPopRecordQuery.setPopId(popDo.getId());
            mallPopRecordQuery.setUserId(user.getId());
            mallPopRecordQuery.addOrderItem(OrderItem.desc("id"));
            MallPopRecordDo popRecordDo = mallPopRecordService.findByQuery(mallPopRecordQuery);
            boolean flag = true;
            if (Objects.nonNull(popRecordDo)) {
                if ((MallPopTriggerFrequencyEnum.ONCE_PER_DAY.getCode().equals(popDo.getTriggerFrequency())
                        && Duration.between(ZonedDateTimeUtil.startOfDay(popRecordDo.getGmtCreate()), ZonedDateTimeUtil.startOfDay()).toDays() < 1)
                        || MallPopTriggerFrequencyEnum.ONCE_ONLY.getCode().equals(popDo.getTriggerFrequency())) {
                    // 仅触发一次 或者 一天一次且当天已触发  ,则不展示
                    flag = false;
                }
            }
            if (flag) {
                MallRetainPopI18nQuery popI18nQuery = new MallRetainPopI18nQuery();
                popI18nQuery.setPopId(popDo.getId());
                popI18nQuery.setLanguageCode(user.getLanguageCode());
                popI18nQuery.setDefaultLanguageCode(popDo.getDefaultLanguageCode());
                MallRetainPopI18nDo popI18nDo = mallRetainPopI18nService.findByLanguageCode(popI18nQuery);
                popInfo.setTitle(popI18nDo.getTitle());
                popInfo.setContent(popI18nDo.getContent());
                popInfo.setId(popDo.getId());
                popInfo.setDelayTime(popDo.getDelayTime());
                popInfo.setPopType(popDo.getPopType());
                if (Objects.nonNull(popDo.getCouponId())) {
                    Coupon coupon = couponService.findById(popDo.getCouponId());
                    CouponExchangeFailEnum couponExchangeFailEnum = mallCouponConvertComponent.checkSendCoupon(coupon, user, false, true);
                    if (Objects.nonNull(couponExchangeFailEnum)) {
                        popInfo = null;
                    } else {
                        MallCouponDto mallCouponDto = mallCouponConvertComponent.appConvertDto(coupon, user, false, null);
                        popInfo.setCoupon(mallCouponDto);
                    }
                }
            }
        }
        return popInfo;
    }

    /**
     * 填充停留和离开弹框列表
     *
     * @param requestDto
     * @param allGoodsPopList
     * @param partGoodsPopList
     * @param stayPopList
     * @param leavePopList
     */
    private void fillStayAndLeavePopList(AppMallRetainPopListRequestDto requestDto, List<MallRetainPopDo> allGoodsPopList, List<MallRetainPopDo> partGoodsPopList, List<MallRetainPopDo> stayPopList, List<MallRetainPopDo> leavePopList) {
        for (MallRetainPopDo mallRetainPopDo : allGoodsPopList) {
            if (YesNoStatus.YES.getCode().equals(mallRetainPopDo.getTriggerNodeType())) {
                leavePopList.add(mallRetainPopDo);
            } else {
                stayPopList.add(mallRetainPopDo);
            }
        }
        for (MallRetainPopDo mallRetainPopDo : partGoodsPopList) {
            MallRetainPopGoodsRelQuery popGoodsRelQuery = new MallRetainPopGoodsRelQuery();
            popGoodsRelQuery.setPopId(mallRetainPopDo.getId());
            popGoodsRelQuery.setGoodsIdList(requestDto.getGoodsIdList());
            if (Objects.nonNull(mallRetainPopGoodsRelService.findByQuery(popGoodsRelQuery))) {
                if (YesNoStatus.YES.getCode().equals(mallRetainPopDo.getTriggerNodeType())) {
                    leavePopList.add(mallRetainPopDo);
                } else {
                    stayPopList.add(mallRetainPopDo);
                }
            }
        }
        allGoodsPopList = null;
        partGoodsPopList = null;
    }

    public void addRetainPopCount(AppMallRetainPopAddCountRequestDto requestDto, Long userId) {
        MallPopRecordDo mallPopRecord = new MallPopRecordDo();
        mallPopRecord.setPopId(requestDto.getPopId());
        mallPopRecord.setUserId(userId);
        mallPopRecordService.create(mallPopRecord);
    }
}
