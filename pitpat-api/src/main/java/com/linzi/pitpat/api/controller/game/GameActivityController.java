package com.linzi.pitpat.api.controller.game;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.converter.ActivityCompetitiveConvert;
import com.linzi.pitpat.api.activityservice.dto.QueryDetailRunDataRequest;
import com.linzi.pitpat.api.activityservice.dto.request.ActivityCompetitiveUserLikesRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.GameAiCommentaryRequest;
import com.linzi.pitpat.api.activityservice.dto.request.GameRomeStartRequest;
import com.linzi.pitpat.api.activityservice.dto.request.PropRankedActivityQueryDto;
import com.linzi.pitpat.api.activityservice.dto.request.QueryActivityStageRunDataRequest;
import com.linzi.pitpat.api.activityservice.dto.request.UserGameReplayRequest;
import com.linzi.pitpat.api.activityservice.dto.request.UserInGameStatusRequest;
import com.linzi.pitpat.api.activityservice.dto.request.UserRunDataTimesRequest;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityCompetitiveGameUserRankListResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.ActivityStageRunData;
import com.linzi.pitpat.api.activityservice.dto.response.GameActivityResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameActivityRunDataResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameAiCommentaryResponse;
import com.linzi.pitpat.api.activityservice.dto.response.GameLaActivityRunDataResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.GameUserRunDataDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedSettleRespDto;
import com.linzi.pitpat.api.activityservice.dto.response.UserGameReplayData;
import com.linzi.pitpat.api.activityservice.manager.PropRunRankedActivityUserManager;
import com.linzi.pitpat.api.activityservice.manager.UserRunDataDetailManager;
import com.linzi.pitpat.api.model.req.ActivityIdRequest;
import com.linzi.pitpat.api.userservice.dto.request.GameLevelRunExpRequestDto;
import com.linzi.pitpat.api.userservice.dto.response.GameLevelRunExpResponseDto;
import com.linzi.pitpat.api.userservice.manager.ExpManager;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonConfigBizService;
import com.linzi.pitpat.data.activityservice.biz.CompetitiveSeasonRankLikeCountBizService;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.UserInGameBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivitySettingConfigEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.competitive.ActivityCompetitiveSeasonType;
import com.linzi.pitpat.data.activityservice.converter.api.UserGamePropOperationLogConverter;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.PropActivityUserGradeReportDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.PropActivityUserRecordDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.WatchGameRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.ActivityRecordRecordingResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.RecordBreakingUserDto;
import com.linzi.pitpat.data.activityservice.manager.AppFreeActivityBiz;
import com.linzi.pitpat.data.activityservice.manager.PropRankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.api.ApiCompetitiveActivityManager;
import com.linzi.pitpat.data.activityservice.manager.api.RecordBreakingManager;
import com.linzi.pitpat.data.activityservice.manager.api.UserGamePropManager;
import com.linzi.pitpat.data.activityservice.model.dto.competitive.ActivityCompetitiveGameUserRankListDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityParams;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityStage;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonDo;
import com.linzi.pitpat.data.activityservice.model.entity.CompetitiveSeasonRankDo;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.request.CompetitiveSeasonSystemDefaultConfig;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.ActivityStageService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonRankService;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.game.model.UserWatchGame;
import com.linzi.pitpat.data.game.service.UserWatchGameService;
import com.linzi.pitpat.data.mapstruct.GameActivityConvert;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 游戏活动服务类
 **/
@RestController
@RequestMapping("/game/activity")
@RequiredArgsConstructor
@Slf4j
public class GameActivityController extends BaseAppController {

    private final UserInGameBizService userInGameService;
    @Autowired
    private UserWatchGameService userWatchGameService;
    @Autowired
    private PropRankedActivityResultManager propRankedActivityResultManager;

    @Autowired
    private UserGamePropOperationLogConverter userGamePropOperationLogConverter;
    @Autowired
    private UserGamePropManager userGamePropManager;
    @Autowired
    private PropRunRankedActivityUserManager propRunRankedActivityUserManager;

    @Resource
    private ExpManager expManager;
    @Resource
    private UserRunDataDetailManager userRunDataDetailManager;
    private final ApiCompetitiveActivityManager apiCompetitiveActivityManager;
    private final ActivityCompetitiveConvert activityCompetitiveConvert;
    private final CompetitiveSeasonRankLikeCountBizService competitiveSeasonRankLikeCountBizService;
    private final AppFreeActivityBiz appFreeActivityBiz;

    /**
     * 活动观赛
     *
     * @return
     */
    @PostMapping("/watch")
    public Result watchGame(@RequestBody WatchGameRequest request) {
        UserWatchGame userWatchGame = GameActivityConvert.INSTANCE.WatchGameRequestToEntity(request);
        userWatchGameService.insert(userWatchGame);
        return CommonResult.success();
    }

    /**
     * 用户道具获取使用记录
     *
     * @return Result<>
     */
    @PostMapping("/user/propRecord")
    public Result propRecord(@RequestBody PropActivityUserRecordDto recordDto) {
        userGamePropManager.saveGamePropOperationLog(userGamePropOperationLogConverter.toDos(recordDto));
        return CommonResult.success();
    }

    /**
     * 用户成绩上报
     *
     * @return Result<>
     */
    @PostMapping("/user/gradeReport")
    public Result gradeReport(@RequestBody PropActivityUserGradeReportDto reportDto) {
        propRankedActivityResultManager.gradeReport(reportDto);
        return CommonResult.success();
    }

    /**
     * 获取用户道具段位赛结算信息
     *
     * @return Result<UserRankedSettleRespDto>
     */
    @PostMapping("/getUserRankedSettle")
    public Result<PropUserRankedSettleRespDto> getUserRankedSettle(@RequestBody @Valid PropRankedActivityQueryDto queryDto) {
        String languageCode = getLanguageCode();
        ZnsUserEntity loginUser = getLoginUser();
        // queryDto.setUserId(loginUser.getId());
        PropUserRankedSettleRespDto respDto = propRunRankedActivityUserManager.getUserRankedSettle(queryDto, languageCode);
        return CommonResult.success(respDto);
    }


    /**
     * 游戏内跑步等级经验
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/runExpNotice")
    public Result<GameLevelRunExpResponseDto> gameRunExpNotice(@RequestBody GameLevelRunExpRequestDto requestDto) {
        GameLevelRunExpResponseDto responseDto = expManager.gameRunExpNotice(requestDto);
        return CommonResult.success(responseDto);
    }

    /**
     * 一个人再一个活动中的多次跑步记录
     *
     * @param request
     * @return
     */
    @PostMapping("/queryUserRunTimesData")
    public Result<List<ZnsUserRunDataDetailsEntity>> queryUserRunTimesData(@RequestBody UserRunDataTimesRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryUserRunTimesData(request.getActivityId(), request.getUserEmail()));
    }

    /**
     * 活动重放
     *
     * @param request
     * @return
     */
    @PostMapping("/queryUserGameReplayData")
    public Result<UserGameReplayData> queryUserGameReplayData(@RequestBody UserGameReplayRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryUserGameReplayData(request.getDetailId()));
    }

    /**
     * 阶段活动查询影子数据
     *
     * @param request
     * @return
     */
    @PostMapping("/queryUserActivityStageReplayData")
    public Result<ActivityStageRunData> queryActivityStageRunData(@RequestBody QueryActivityStageRunDataRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryUserActivityStageReplayData(request.getActivityId(), getLoginUser()));
    }

    /**
     * 竞技赛排名列表
     *
     * @return
     */
    @PostMapping("/competitive/userRank")
    public Result<ActivityCompetitiveGameUserRankListResponseDto> userRank(@Validated @RequestBody ActivityIdRequest req) {
        ZnsUserEntity loginUser = getLoginUser();
        ActivityCompetitiveGameUserRankListDto activityCompetitiveGameUserRankListDto = apiCompetitiveActivityManager.userRank(req.getActivityId(), getLanguageCode(), loginUser.getId());
        return CommonResult.success(activityCompetitiveConvert.toResponseDto(activityCompetitiveGameUserRankListDto));
    }

    /**
     * 竞技赛排名用户点赞
     * 每5秒给一次
     *
     * @return 当前点赞数
     */
    @PostMapping("/competitive/likes")
    public Result likes(@Validated @RequestBody ActivityCompetitiveUserLikesRequestDto req) {
        competitiveSeasonRankLikeCountBizService.addLikes(req.getUserLikesList(), req.getUserLikesList().get(0).getSeasonId(), req.getActivityId());
        return CommonResult.success();
    }


    /**
     * 用户进入游戏，与退出游戏状态同步
     *
     * @param req
     * @return
     */
    @PostMapping("/user/gameStatus")
    public Result userInGameStatus(@RequestBody UserInGameStatusRequest req) {
        userInGameService.userStateChanged(req.getUserId(), req.getRoomId(), Objects.equals(req.getInGame(), 1), req.getActivityId(), req.getRouteId());
        return CommonResult.success();
    }

    private final ActivityParamsService activityParamsService;
    private final RecordBreakingManager recordBreakingManager;
    private final CompetitiveSeasonService competitiveSeasonService;
    private final MainActivityBizService mainActivityBizService;
    private final CompetitiveSeasonConfigBizService competitiveSeasonConfigBizService;
    private final CompetitiveSeasonRankService competitiveSeasonRankService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ActivityStageService activityStageService;

    /**
     * 用户进入游戏，获取ai解说相关信息
     *
     * @param req
     * @return
     */
    @FillerMethod
    @PostMapping("/user/aiCommentary")
    public Result<GameAiCommentaryResponse> aiCommentary(@RequestBody GameAiCommentaryRequest req) {
        GameAiCommentaryResponse response = new GameAiCommentaryResponse();

        Optional<MainActivity> mainActivity = mainActivityBizService.getMainActivity(req.getActivityId());
        if (mainActivity.isEmpty()) {
            response.setIsAiCommentary(0);
            return CommonResult.success(response);
        }
        Optional<ActivityParams> oneByMainActivityAndParamType = activityParamsService.findOneByMainActivityAndParamType(mainActivity.get().getId(), ActivitySettingConfigEnum.IS_AI_COMMENTARY);
        if (oneByMainActivityAndParamType.isEmpty() || "0".equals(oneByMainActivityAndParamType.get().getParamValue())) {
            response.setIsAiCommentary(0);
            return CommonResult.success(response);
        }
        response.setIsAiCommentary(1);
        List<ActivityStage> byActId = activityStageService.findByActId(req.getActivityId());
        response.setActivityStages(byActId);
        //排名条件
        //排行榜上榜人员
        Optional<CompetitiveSeasonDo> byActivityId = competitiveSeasonService.findByActivityId(mainActivity.get().getId());
        Long seasonRankId = null;
        Long annualRankId = null;
        if (byActivityId.isPresent()) {
            CompetitiveSeasonDo competitiveSeasonDo = byActivityId.get();
            if (ActivityCompetitiveSeasonType.MONTHLY.equals(competitiveSeasonDo.getCompetitiveSeasonType())) {
                CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
                seasonRankId = competitiveSeasonSystemDefaultConfig.getSeasonRankId();
                annualRankId = competitiveSeasonSystemDefaultConfig.getAnnualRankId();
            } else if (ActivityCompetitiveSeasonType.SEASONAL.equals(competitiveSeasonDo.getCompetitiveSeasonType())) {
                seasonRankId = competitiveSeasonDo.getSeasonId();
                annualRankId = Long.valueOf(competitiveSeasonDo.getCompetitiveSeasonYear());
            } else if (ActivityCompetitiveSeasonType.ANNUAL.equals(competitiveSeasonDo.getCompetitiveSeasonType())) {
                annualRankId = competitiveSeasonDo.getSeasonId();
            }
        } else {
            CompetitiveSeasonSystemDefaultConfig competitiveSeasonSystemDefaultConfig = competitiveSeasonConfigBizService.getCompetitiveSeasonSystemDefaultConfig();
            seasonRankId = competitiveSeasonSystemDefaultConfig.getSeasonRankId();
            annualRankId = competitiveSeasonSystemDefaultConfig.getAnnualRankId();
        }

        if (seasonRankId != null) {
            List<CompetitiveSeasonRankDo> seasonRankUser = competitiveSeasonRankService.getSeasonRankUser(seasonRankId, 100L, req.getUserIds());
            response.setSeasonRankTop100User(seasonRankUser);
        }
        if (annualRankId != null) {
            List<CompetitiveSeasonRankDo> annualRankUser = competitiveSeasonRankService.getSeasonRankUser(annualRankId, 100L, req.getUserIds());
            response.setAnnualRankTop100User(annualRankUser);
        }
        //年榜前100的用户的最佳记录
        if (!CollectionUtils.isEmpty(response.getAnnualRankTop100User())) {
            List<ZnsRunActivityUserEntity> activityUsers = znsRunActivityUserService.findActivityUsers(req.getActivityId(), response.getAnnualRankTop100User().stream().map(CompetitiveSeasonRankDo::getUserId).toList());
            List<GameAiCommentaryResponse.BestUserRecord> userBestRecord = new ArrayList<>();
            for (ZnsRunActivityUserEntity activityUser : activityUsers) {
                Integer targetRunTime = activityUser.getTargetRunTime();
                Integer targetRunMileage = activityUser.getTargetRunMileage();
                if (targetRunTime != null && targetRunTime > 0) {
                    //目标是时间
                    ZnsUserRunDataDetailsEntity bestTargetRunTime = userRunDataDetailsService.findBestTargetRunTime(activityUser.getUserId(), targetRunTime);
                    if (bestTargetRunTime != null) {
                        GameAiCommentaryResponse.BestUserRecord record = new GameAiCommentaryResponse.BestUserRecord();
                        record.setUserId(activityUser.getUserId());
                        record.setTarget(targetRunTime);
                        record.setTargetType(ActivityConstants.TargetTypeEnum.TARGETTYPE_2.getCode());
                        record.setRunMileage(bestTargetRunTime.getRunMileage());
                        userBestRecord.add(record);
                    }
                } else if (targetRunMileage != null && targetRunMileage > 0) {
                    //目标是距离
                    ZnsUserRunDataDetailsEntity bestTargetRunMilleage = userRunDataDetailsService.findBestTargetRunMilleage(activityUser.getUserId(), targetRunMileage);
                    if (bestTargetRunMilleage != null) {
                        GameAiCommentaryResponse.BestUserRecord record = new GameAiCommentaryResponse.BestUserRecord();
                        record.setUserId(activityUser.getUserId());
                        record.setTarget(targetRunMileage);
                        record.setTargetType(ActivityConstants.TargetTypeEnum.TARGETTYPE_1.getCode());
                        record.setRunTime(bestTargetRunMilleage.getRunTime());
                        userBestRecord.add(record);
                    }
                }
            }
            response.setBestUserRecords(userBestRecord);
        }
        //当前活动的最佳记录
        ActivityRecordRecordingResponse activityRecordRecordingResponse = recordBreakingManager.queryRecordBreakingByActivity(mainActivity.get().getId());
        response.setActivityTopRecord(activityRecordRecordingResponse);
        //用户在荣誉榜
        List<RecordBreakingUserDto> recordBreakingUserDtos = recordBreakingManager.userInRecordBreaking(req.getUserIds());
        response.setUserRecordBreaking(recordBreakingUserDtos);
        List<RecordBreakingUserDto> weekRecordBreakingUserDtos = recordBreakingManager.userBreakingRecord(req.getUserIds(), ZonedDateTime.now().minusDays(7));
        response.setWeekUserRecordBreaking(weekRecordBreakingUserDtos);
        response.setMainActivityId(mainActivity.get().getId());
        return CommonResult.success(response);
    }


    /**
     * 活动用户影子数据
     *
     * @param request
     * @return GameActivityRunDataResponseDto
     * @tag 4.7.0
     * @since 4.7.0
     */
    @PostMapping("/queryUserActivityReplayData")
    public Result<GameActivityRunDataResponseDto> queryUserActivityReplayData(@RequestBody QueryActivityStageRunDataRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryUserActivityReplayData(request.getActivityId(), getLoginUser()));
    }


    /**
     * 活动数据
     *
     * @param request
     * @return GameActivityResponseDto
     * @tag 4.7.0
     * @since 4.7.0
     */
    @PostMapping("/queryActivityData")
    public Result<GameActivityResponseDto> queryActivityData(@RequestBody QueryActivityStageRunDataRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryActivityData(request.getActivityId(), getLoginUser()));
    }


    /**
     * LA活动用户影子数据detail
     *
     * @param request
     * @return GameActivityRunDataResponseDto
     * @tag 4.8.0
     * @since 4.8.0
     */
    @PostMapping("/queryLaActivityReplayData")
    public Result<GameLaActivityRunDataResponseDto> queryLaActivityReplayData(@RequestBody QueryActivityStageRunDataRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryLaActivityReplayData(request.getActivityId(), getLoginUser()));
    }

    /**
     * 用户影子数据detailId获取
     *
     * @param request
     * @return GameActivityRunDataResponseDto
     * @tag 4.8.0
     * @since 4.8.0
     */
    @PostMapping("/queryReplayDataByDetailId")
    public Result<GameUserRunDataDto> queryReplayDataByDetailId(@RequestBody @Validated QueryDetailRunDataRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryReplayDataByDetailId(request.getDetailId(), getLoginUser()));
    }

    /**
     * 活动开赛
     * @since 4.7.4
     * @param req
     * @return
     */
    @PostMapping("/room/startGame")
    public Result userInGameStatus(@RequestBody GameRomeStartRequest req) {
        appFreeActivityBiz.roomGameStart(req.getRoomId(),req.getActivityId());
        return CommonResult.success();
    }

    /**
     * 定级赛用户影子数据
     *
     * @param request
     * @return GameActivityRunDataResponseDto
     * @tag 4.8.0
     * @since 4.8.0
     */
    @PostMapping("/queryUserPlacementActivityReplayData")
    public Result<GameActivityRunDataResponseDto> queryUserPlacementActivityReplayData(@RequestBody QueryActivityStageRunDataRequest request) {
        return CommonResult.success(userRunDataDetailManager.queryUserPlacementActivityReplayData(request.getActivityId(), getLoginUser()));
    }


}
