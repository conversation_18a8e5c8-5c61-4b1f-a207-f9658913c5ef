package com.linzi.pitpat.api.controller.h5;


import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.api.dto.request.QuestionReportAgreeRequestDto;
import com.linzi.pitpat.api.dto.request.QuestionReportRequestDto;
import com.linzi.pitpat.api.dto.response.QuestionReportResponseDto;
import com.linzi.pitpat.api.mananger.QuestionReportManager;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 问卷调查接口
 *
 * <AUTHOR>
 * @Date 2023/12/27 16:11
 */
@Slf4j
@RequestMapping("/h5/question/report")
@RestController
public class QuestionReportController extends BaseH5Controller {

    @Autowired
    private QuestionReportManager questionReportManager;

    /**
     * 获取该问卷调查信息
     *
     * @return
     */
    @PostMapping("/info")
    public Result<QuestionReportResponseDto> getQuestionReportInfo(@RequestBody QuestionReportRequestDto dto) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(questionReportManager.getInfo(loginUser.getId(), dto.getConfigId()));
    }

    /**
     * 提交问卷
     *
     * @return
     */
    @PostMapping("/submit")
    public Result<Boolean> submitQuestionReport(@RequestBody @Validated QuestionReportAgreeRequestDto dto) {
        ZnsUserEntity loginUser = getLoginUser();
        questionReportManager.submitQuestionReport(dto.getIsAgree(), loginUser.getId());
        // 如果用户提交了问卷
        return CommonResult.success();
    }

}

