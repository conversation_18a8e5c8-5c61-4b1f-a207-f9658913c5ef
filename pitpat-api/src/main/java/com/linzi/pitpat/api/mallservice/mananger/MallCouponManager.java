package com.linzi.pitpat.api.mallservice.mananger;

import com.linzi.pitpat.api.mallservice.dto.request.ReceiveCouponRequestDto;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.awardservice.biz.MallCouponComponent;
import com.linzi.pitpat.data.awardservice.biz.MallCouponConvertComponent;
import com.linzi.pitpat.data.awardservice.biz.MallSkuCouponBizService;
import com.linzi.pitpat.data.awardservice.biz.MallUserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponExchangeFailEnum;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.vo.SkuCouponAmountVo;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsDiscountAmountDetailVo;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsDiscountAmountRespDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsInfoCouponRespDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.MallCouponDto;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderPickCouponDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderDiscountAmountReqDto;
import com.linzi.pitpat.data.mallservice.dto.response.OrderSkuAmountRespDto;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 商品优惠券Manager
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MallCouponManager {

    private final MallSkuCouponBizService mallCouponBizService;
    private final MallCouponComponent mallCouponComponent;
    private final MallCouponConvertComponent mallCouponConvertComponent;
    private final MallUserCouponBizService mallUserCouponBizService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final CouponService couponService;
    private final RedissonClient redissonClient;

    /**
     * 计算商品优惠金额
     */
    public GoodsInfoCouponRespDto calGoodsInfoDiscount(OrderSkuVo req, ZnsUserEntity user) {
        //计算商品券后单价
        SkuCouponAmountVo skuCouponAmountVo = mallCouponBizService.calSkuCouponAmount(req.getSkuId(), req.getCount(), user.getAppVersion());

        //查询商品优惠券列表
        List<OrderPickCouponDto> skuCouponList = mallCouponBizService.findSkuCouponList(req.getSkuId(), req.getCount(), user);

        //封装总价
        BigDecimal minTotalAmount = mallCouponComponent.getMinTotalAmount();
        GoodsInfoCouponRespDto respDto = new GoodsInfoCouponRespDto(skuCouponAmountVo, minTotalAmount, skuCouponList);

        //最佳优惠券
        Long couponId = skuCouponAmountVo.getCouponId();
        if (couponId != null) {
            Coupon coupon = couponService.findById(couponId);
            MallCouponDto mallCouponDto = mallCouponConvertComponent.appConvertDto(coupon, user, false, null);
            respDto.setBestMallCouponDto(mallCouponDto);
        }

        //封装明细
        ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(req.getSkuId());
        GoodsDiscountAmountDetailVo detailVo = new GoodsDiscountAmountDetailVo(skuEntity.getCurrencyCode(), req.getSkuId(), req.getCount(), skuCouponAmountVo, minTotalAmount);
        respDto.setSkuDiscountAmountDetails(List.of(detailVo));
        //包含衍生品总金额计算
        BigDecimal derivativesTotalAmount = respDto.getTotalSaleAmount();
        if(!CollectionUtils.isEmpty(req.getDerivativesSkuIdList())){
            for (Long derivativesSkuId : req.getDerivativesSkuIdList()) {
                ZnsGoodsSkuEntity derivativesSkuEntity = znsGoodsSkuService.findById(derivativesSkuId);
                if (Objects.nonNull(derivativesSkuEntity)) {
                    derivativesTotalAmount = derivativesTotalAmount.add(derivativesSkuEntity.getSalePrice());
                }
            }
        }
        respDto.setDerivativesTotalAmount(derivativesTotalAmount);
        return respDto;
    }

    /**
     * 计算订单优惠金额
     */
    public GoodsDiscountAmountRespDto calOrderDiscountAmount(OrderDiscountAmountReqDto req, ZnsUserEntity user) {
        //券后价格
        OrderSkuAmountRespDto respDto = mallUserCouponBizService.calOrderCouponAmount(req.getSkuList(), user, req.getCouponId());
        MallCouponDto mallCouponDto = respDto.getMallCouponDto();

        //组装结果
        GoodsDiscountAmountRespDto resp = new GoodsDiscountAmountRespDto();
        resp.setOriginalTotalAmount(respDto.getOriginalTotalAmount());
        resp.setTotalSaleAmount(respDto.getTotalSaleAmount());
        resp.setTotalDiscountAmount(respDto.getTotalDiscountAmount());
        resp.setSkuDiscountAmountDetails(respDto.getSkuDiscountAmountDetails());
        resp.setBestMallCouponDto(mallCouponDto);
        resp.setAllDiscountAmount(respDto.getAllDiscountAmount());
        resp.setOrderTotalAmount(respDto.getTotalSaleAmount().subtract(respDto.getCouponAmount())); //订销售价总金额 = 销售价总金额 - 优惠券金额
        resp.setCouponAmount(respDto.getCouponAmount());//优惠券金额
        return resp;
    }

    /**
     * 领取优惠券
     */
    public MallCouponDto receive(ReceiveCouponRequestDto req, ZnsUserEntity user) {
        if (Objects.isNull(req.getCouponId())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "couponId"));
        }
        Coupon coupon = couponService.findById(req.getCouponId());
        if (coupon == null) {
            throw new BaseException(I18nMsgUtils.getMessage("common.operate.reenter"));
        }
        try {
            sendMallCouponDto(user, coupon, req.getIsRetainPop());
        } catch (BaseException e) {
            if (ActivityError.MALL_ORDER_COUPON_USER_MAX_FAIL.getCode().equals(e.getCode())) {
                //超过用户最大限制，前端不做提示，所以msg这里返回空,code返2001 安卓才忽略提示
                throw new BaseException("", UserError.PERSONAL_ADDRESS_MISSING.getCode());
            }
            throw e;
        } catch (Exception e) {
            throw new BaseException(CommonError.BUSINESS_ERROR.getMsg());
        }
        return mallCouponConvertComponent.appConvertDto(coupon, user, true, null);
    }


    /**
     * 优惠券详情查询
     */
    public MallCouponDto couponDetail(ReceiveCouponRequestDto req, ZnsUserEntity user) {
        if (Objects.isNull(req.getCouponId())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "couponId"));
        }
        Coupon coupon = couponService.findById(req.getCouponId());
        if (coupon == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "coupon"));
        }
        return mallCouponConvertComponent.appConvertDto(coupon, user, false, null);
    }

    /**
     * 发放商城优惠券
     */
    public Map<String, Object> sendMallCouponDto(ZnsUserEntity user, Coupon coupon, Boolean isRetainPop) {
        Long userId = user.getId();
        Long couponId = coupon.getId();
        Integer userLimit = coupon.getLimitCount();//每个人限制领取张数，-1不限制
        Integer quota = coupon.getQuota();//优惠券发放总数 -1:不限制
        String key = "";
        if (!Objects.equals(quota, -1)) {
            //券总数领取数有限制
            key = couponId + "";
        } else if (!Objects.equals(userLimit, -1)) {
            //用户领取数有限制,券总数领取数无限制
            key = couponId + "_" + userId;
        }
        log.info("[receive],领取优惠券,couponId={},userId={},锁key={}", couponId, userId, key);
        CouponExchangeFailEnum exchangeFailEnum;
        if (StringUtils.hasText(key)) {
            RLock lock = redissonClient.getLock(RedisConstants.MALL_COUPON_SEND_USER + key);
            boolean b = false;
            try {
                b = lock.tryLock(30, TimeUnit.SECONDS);//获取锁等待30s
                if (!b) {
                    //获取锁失败
                    log.info("[receive],获取锁失败,couponId={},userId={}", couponId, userId);
                    throw new BaseException(CommonError.BUSINESS_ERROR.getMsg());
                }
                return mallUserCouponBizService.sendCouponToUser(coupon, user, false, isRetainPop);
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                log.error("[receive],领取异常,couponId={},userId={},", couponId, userId, e);
                throw new BaseException(e.getMessage());
            } finally {
                if (b && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            //没有限制随便领取
            return mallUserCouponBizService.sendCouponToUser(coupon, user, false, isRetainPop);
        }
    }
}

