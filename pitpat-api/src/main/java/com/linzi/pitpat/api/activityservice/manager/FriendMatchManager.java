package com.linzi.pitpat.api.activityservice.manager;


import com.linzi.pitpat.api.activityservice.dto.request.FriendMatchRequest;
import com.linzi.pitpat.api.activityservice.dto.request.SingleMatchRequest;
import com.linzi.pitpat.api.dto.response.FriendMatchActivityInfoResp;
import com.linzi.pitpat.api.dto.response.FriendMatchActivityStatusResp;
import com.linzi.pitpat.api.dto.response.FriendMatchSingleActivityInfoResp;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityConstants;
import com.linzi.pitpat.data.activityservice.constant.enums.FriendMatchAppActivityStatusEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.model.vo.PKAward;
import com.linzi.pitpat.data.activityservice.service.UserFriendMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserFriendMatch;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.UserFriendMatchPushVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 好友匹配业务管理
 */
@Component
@RequiredArgsConstructor
public class FriendMatchManager {
    private final ZnsUserService znsUserService;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final UserFriendMatchService userFriendMatchService;
    private final ZnsRunActivityService znsRunActivityService;
    private final ISysConfigService sysConfigService;
    private final ZnsRunActivityConfigService runActivityConfigService;
    private final ZnsRunActivityService runActivityService;


    /**
     * 好友匹配列表
     *
     * @param loginUser
     * @return
     */
    public List<Long> friendMatchInfoList(ZnsUserEntity loginUser) {
        List<Long> longList = znsRunActivityUserService.selectSubActivityIdsByUserId(loginUser.getId());
        if (CollectionUtils.isEmpty(longList)) {
            return longList;
        }
        return userFriendMatchService.friendMatchInfoList(longList, loginUser);
    }

    public FriendMatchSingleActivityInfoResp friendMatchInfoSingle(ZnsUserEntity loginUser, SingleMatchRequest req) {
        FriendMatchSingleActivityInfoResp response = new FriendMatchSingleActivityInfoResp();
        Long friendId = Objects.equals(req.getFriendMatchId(), "administrator") ? 0L : Long.parseLong(req.getFriendMatchId());
        List<UserFriendMatch> startPks = userFriendMatchService.findStartPks(loginUser.getId(), -1, friendId, 2);

        List<UserFriendMatch> beInviteds = userFriendMatchService.findBeInviteds(friendId, loginUser.getId());
        if (CollectionUtils.isEmpty(startPks) && CollectionUtils.isEmpty(beInviteds)) {
            return response;
        }
        // 筛选活动
        List<ZnsRunActivityEntity> finalActivitys = new ArrayList<>();
        if (!CollectionUtils.isEmpty(startPks)) {
            List<ZnsRunActivityEntity> activitys = znsRunActivityService.findByIds(startPks.stream().map(UserFriendMatch::getActivityId).toList());
            activitys = activitys.stream().filter(activity -> activity.getActivityState().equals(ActivityStateEnum.NOT_START.getState())).toList();
            finalActivitys.addAll(activitys);
        }
        if (!CollectionUtils.isEmpty(beInviteds)) {
            List<ZnsRunActivityEntity> activitys = znsRunActivityService.findByIds(beInviteds.stream().map(UserFriendMatch::getActivityId).toList());
            activitys = activitys.stream().filter(activity -> activity.getActivityState().equals(ActivityStateEnum.NOT_START.getState())).toList();
            finalActivitys.addAll(activitys);
        }
        if (CollectionUtils.isEmpty(finalActivitys)) {
            return response;
        }
        ZnsRunActivityEntity activity = finalActivitys.stream().distinct().min(Comparator.comparing(ZnsRunActivityEntity::getActivityStartTime)).orElse(finalActivitys.get(0));
        UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(activity.getId());
        setActivityInfo(response, userFriendMatch);
        response.setIsSender(YesNoStatus.NO.getCode());
        if (userFriendMatch.getMatchFriendId().equals(req.getFriendMatchId())) {
            response.setIsSender(YesNoStatus.YES.getCode());
        }
        return response;
    }

    public void setActivityInfo(FriendMatchSingleActivityInfoResp response, UserFriendMatch match) {
        if (match != null) {
            ZnsRunActivityEntity activity = znsRunActivityService.findById(match.getActivityId());
            response.setStatus(match.getStatus());
            response.setStartTime(activity.getActivityStartTime());
            response.setActivityId(match.getActivityId());
            if (activity.getActivityState() == 2) {
                response.setStatus(2);
            }
        } else {
            response.setStatus(-1); // 假设-1表示未找到匹配
        }
    }

    public FriendMatchActivityInfoResp friendMatchActivityInfoSingle(ZnsUserEntity loginUser, FriendMatchRequest req) {
        FriendMatchActivityInfoResp friendMatchActivityInfoResp = new FriendMatchActivityInfoResp();
        Currency currency = new Currency();
        currency.setCurrencyName(I18nConstant.CurrencyCodeEnum.USD.getName());
        currency.setCurrencyCode(I18nConstant.CurrencyCodeEnum.USD.getCode());
        currency.setCurrencySymbol(I18nConstant.CurrencyCodeEnum.USD.getSymbol());
        friendMatchActivityInfoResp.setCurrency(currency);
        ZnsUserEntity matchUser = znsUserService.findById(req.getFriendMatchId());
        // 好友信息
        friendMatchActivityInfoResp.setFriendId(req.getFriendMatchId());
        friendMatchActivityInfoResp.setFriendHeadPortrait(matchUser.getHeadPortrait());
        friendMatchActivityInfoResp.setFriendName(matchUser.getFirstName());
        if (req.getActivityId() != null) {
            //再来一次 使用前一次配置
            ZnsRunActivityEntity activity = znsRunActivityService.findById(req.getActivityId());
            // 活动信息
            friendMatchActivityInfoResp.setGoal(activity.getRunMileage());
            friendMatchActivityInfoResp.setMargin(activity.getActivityEntryFee());
            friendMatchActivityInfoResp.setTime(activity.getRunTime());
            friendMatchActivityInfoResp.setActivityRouteId(activity.getActivityRouteId());
            //奖励部分
            PKAward participationAward = znsRunActivityService.getPKAwardConfig(activity, 1);
            PKAward winnerAwardConfig = znsRunActivityService.getPKAwardConfig(activity, 2);
            if (participationAward != null && winnerAwardConfig != null) {
                friendMatchActivityInfoResp.setPointStr(getPointStr(participationAward, winnerAwardConfig));
                friendMatchActivityInfoResp.setCouponNumStr(getCountNum(participationAward, winnerAwardConfig));
                friendMatchActivityInfoResp.setCashStr(friendMatchActivityInfoResp.getMargin().multiply(new BigDecimal("2")).add(getCashAmount(participationAward, winnerAwardConfig)));
            }
        } else {
            // 查询当前用户发起过的pk赛
            ZnsRunActivityEntity oldActivity = znsRunActivityUserService.getUserLatestPkActivityByUserId(loginUser.getId());
            if (Objects.nonNull(oldActivity)) {
                // 活动信息
                friendMatchActivityInfoResp.setGoal(oldActivity.getRunMileage());
                friendMatchActivityInfoResp.setMargin(oldActivity.getActivityEntryFee());
                friendMatchActivityInfoResp.setTime(oldActivity.getRunTime());
                friendMatchActivityInfoResp.setActivityRouteId(oldActivity.getActivityRouteId());
                //奖励部分
                PKAward participationAward = znsRunActivityService.getPKAwardConfig(oldActivity, 1);
                PKAward winnerAwardConfig = znsRunActivityService.getPKAwardConfig(oldActivity, 2);
                if (participationAward != null && winnerAwardConfig != null) {
                    friendMatchActivityInfoResp.setPointStr(getPointStr(participationAward, winnerAwardConfig));
                    friendMatchActivityInfoResp.setCouponNumStr(getCountNum(participationAward, winnerAwardConfig));
                    friendMatchActivityInfoResp.setCashStr(friendMatchActivityInfoResp.getMargin().multiply(new BigDecimal("2")).add(getCashAmount(participationAward, winnerAwardConfig)));
                }
            } else {
                // 使用默认配置
                friendMatchActivityInfoResp.setGoal(new BigDecimal("1600"));
                friendMatchActivityInfoResp.setMargin(BigDecimal.ZERO);
                friendMatchActivityInfoResp.setTime(0);
                SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.RUN_f_V_f_ROUTE.getCode());
                // 默认线路获取逻辑
                friendMatchActivityInfoResp.setActivityRouteId(MapUtil.getLong(sysConfig.getConfigValue(), 1001L));
                //奖励部分
                ZnsRunActivityEntity activity = new ZnsRunActivityEntity();
                ZnsRunActivityConfigEntity config = runActivityConfigService.getByType(2, 2);
                String activityConfig = config.getActivityConfig();
                activity.setActivityConfig(activityConfig);
                activity.setRunMileage(friendMatchActivityInfoResp.getGoal());
                activity.setCompleteRuleType(ActivityConstants.CompleteRuleTypeEnum.COMPLETERULETYPE_1.getCode());
                PKAward participationAward = znsRunActivityService.getPKAwardConfig(activity, 1);
                PKAward winnerAwardConfig = znsRunActivityService.getPKAwardConfig(activity, 2);
                friendMatchActivityInfoResp.setPointStr(getPointStr(participationAward, winnerAwardConfig));
                friendMatchActivityInfoResp.setCouponNumStr(getCountNum(participationAward, winnerAwardConfig));
                friendMatchActivityInfoResp.setCashStr(getCashAmount(participationAward, winnerAwardConfig));
            }
        }
        return friendMatchActivityInfoResp;
    }

    private int getPointStr(PKAward participationAward, PKAward winnerAwardConfig) {
        int score = 0;
        if (Objects.nonNull(participationAward.getScore())) {
            score = score + participationAward.getScore();
        }
        if (Objects.nonNull(winnerAwardConfig.getScore())) {
            score = score + winnerAwardConfig.getScore();
        }
        return score;
    }

    private Integer getCountNum(PKAward participationAward, PKAward winnerAwardConfig) {
        int num = 0;
        if (Objects.nonNull(participationAward.getCouponId())) {
            num += 1;
        }
        if (Objects.nonNull(winnerAwardConfig.getCouponId())) {
            num += 1;
        }
        return num;
    }

    private BigDecimal getCashAmount(PKAward participationAward, PKAward winnerAwardConfig) {
        List<CurrencyAmount> participationAmountList = participationAward.getAmountList();
        List<CurrencyAmount> winamountList = winnerAwardConfig.getAmountList();
        BigDecimal winnerAmountCurrency = BigDecimal.ZERO;
        BigDecimal participationAwardAmountCurrency = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(winamountList)) {
            for (CurrencyAmount currencyAmount : winamountList) {
                if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                    winnerAmountCurrency = currencyAmount.getAmount();
                }
            }
        }
        if (!CollectionUtils.isEmpty(participationAmountList)) {
            for (CurrencyAmount currencyAmount : participationAmountList) {
                if (I18nConstant.CurrencyCodeEnum.USD.getCode().equals(currencyAmount.getCurrencyCode())) {
                    participationAwardAmountCurrency = currencyAmount.getAmount();
                }
            }
        }
        return winnerAmountCurrency.add(participationAwardAmountCurrency);
    }

    public UserFriendMatchPushVo approaching(ZnsUserEntity loginUser, FriendMatchRequest req) {
        ZnsRunActivityEntity serviceById = znsRunActivityService.findById(req.getActivityId());
        UserFriendMatch friendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(req.getActivityId());
        return userFriendMatchService.buildUserFriendMatchPushVo(friendMatch);
    }

    public FriendMatchActivityStatusResp checkActivityStatus(Long activityId, ZnsUserEntity loginUser) {
        FriendMatchActivityStatusResp friendMatchActivityStatusResp = new FriendMatchActivityStatusResp();
        ZnsRunActivityEntity activityEntity = runActivityService.findById(activityId);
        UserFriendMatch userFriendMatch = userFriendMatchService.selectUserFriendMatchByActivityId(activityId);
        if (activityEntity.getActivityState().equals(ActivityStateEnum.NOT_START.getState())) {
            friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.WAITING_TO_ACCEPT.getCode());
            if (Objects.nonNull(userFriendMatch)) {
                // 匹配成功
                if (userFriendMatch.getStatus() == 1) {
                    friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.ACCEPTED.getCode());
                }
            }
            // 超过活动开始时间 状态还没有即时更改 也是过期活动
            if (ZonedDateTime.now().isAfter(activityEntity.getActivityStartTime())) {
                friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.EXPIRED.getCode());
            }
        }
        if (activityEntity.getActivityState().equals(ActivityStateEnum.FINISHED.getState())) {
            friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.FINISHED.getCode());
        }
        // 取消的活动check 用户 match 表数据
        if (activityEntity.getActivityState().equals(ActivityStateEnum.CANCELED.getState())) {
            friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.EXPIRED.getCode());
            if (Objects.nonNull(userFriendMatch)) {
                // 活动取消。匹配拒绝 = 已拒绝
                if (userFriendMatch.getStatus() == -1) {
                    friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.REJECTED.getCode());
                }
                if (userFriendMatch.getStatus() == 1) {
                    friendMatchActivityStatusResp.setAppImShowStatus(FriendMatchAppActivityStatusEnum.FINISHED.getCode());
                }
            }
        }
        return friendMatchActivityStatusResp;
    }
}
