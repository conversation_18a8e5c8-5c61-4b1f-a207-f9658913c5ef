package com.linzi.pitpat.api.trainingplanservice.manager;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunRouteEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsRunRouteService;
import com.linzi.pitpat.data.courseservice.biz.CourseBizService;
import com.linzi.pitpat.data.courseservice.model.entity.CourseI18nEntity;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseActionEntity;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseActionService;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.trainingplanservice.biz.TrainingPlanBizService;
import com.linzi.pitpat.data.trainingplanservice.biz.UserTrainingPlanDetailBizService;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanCourseRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanDetailRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanModifyRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanNoticeSetRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanSaveRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanStatusChangeRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.AppUserTrainingPlanStopRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.request.WeekPlanCourseRequestDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppPlanWeekDetailDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppTrainingPlanCourseDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppTrainingPlanDetailResponseDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppUserTrainingPlanCourseResponseDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppUserTrainingPlanDetailResponseDto;
import com.linzi.pitpat.data.trainingplanservice.dto.api.response.AppUserTrainingPlanResponseDto;
import com.linzi.pitpat.data.trainingplanservice.enums.TrainingPlanStatusEnum;
import com.linzi.pitpat.data.trainingplanservice.model.entity.TrainingPlan;
import com.linzi.pitpat.data.trainingplanservice.model.entity.UserTrainingPlan;
import com.linzi.pitpat.data.trainingplanservice.model.entity.UserTrainingPlanDetail;
import com.linzi.pitpat.data.trainingplanservice.model.entity.UserTrainingPlanNotice;
import com.linzi.pitpat.data.trainingplanservice.model.query.UserTrainingPlanPageQuery;
import com.linzi.pitpat.data.trainingplanservice.model.query.UserTrainingPlanQuery;
import com.linzi.pitpat.data.trainingplanservice.service.TrainingPlanService;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanDetailService;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanNoticeService;
import com.linzi.pitpat.data.trainingplanservice.service.UserTrainingPlanService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.PageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AppUserTrainingPlanManager {

    @Resource
    private TrainingPlanService trainingPlanService;
    @Resource
    private UserTrainingPlanService userTrainingPlanService;
    @Resource
    private UserTrainingPlanDetailService userTrainingPlanDetailService;
    @Resource
    private UserTrainingPlanNoticeService userTrainingPlanNoticeService;
    @Resource
    private UserTrainingPlanDetailBizService userTrainingPlanDetailBizService;
    @Resource
    private CourseBizService courseBizService;
    @Resource
    private ZnsCourseService znsCourseService;
    @Resource
    private ZnsRunRouteService znsRunRouteService;
    @Resource
    private TrainingPlanBizService trainingPlanBizService;
    @Resource
    private ZnsCourseActionService znsCourseActionService;


    public AppUserTrainingPlanDetailResponseDto getInfo(ZnsUserEntity loginUser, AppUserTrainingPlanDetailRequestDto requestDto) {
        Long planId = requestDto.getPlanId();
        Long loginUserId = loginUser.getId();
        Long userPlanId = requestDto.getUserPlanId();
        AppUserTrainingPlanDetailResponseDto responseDto = new AppUserTrainingPlanDetailResponseDto();
        UserTrainingPlan userTrainingPlan;
        if (Objects.nonNull(userPlanId)) {
            // 用户训练计划记录的详情查询
            userTrainingPlan = userTrainingPlanService.findById(userPlanId);
        } else {
            UserTrainingPlanQuery userTrainingPlanQuery = UserTrainingPlanQuery.builder()
                    .userId(loginUserId)
                    .planId(planId)
                    .planStatus(TrainingPlanStatusEnum.IN_PROGRESS.getCode())
                    .build();
            userTrainingPlan = userTrainingPlanService.findByQuery(userTrainingPlanQuery);
        }
        if (Objects.nonNull(userTrainingPlan)) {
            fillAppUserTrainingPlanDetailResponseDto(loginUser, responseDto, userTrainingPlan);
        } else {
            responseDto = getUnSaveTrainingPlanDetailResponseDto(loginUser, planId, responseDto);
        }
        responseDto.setUserMemberType(loginUser.getMemberType());
        return responseDto;

    }


    private AppUserTrainingPlanDetailResponseDto getUnSaveTrainingPlanDetailResponseDto(ZnsUserEntity loginUser, Long planId, AppUserTrainingPlanDetailResponseDto responseDto) {
        List<AppTrainingPlanDetailResponseDto> trainingPlanInfos = trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(planId), loginUser.getLanguageCode());
        AppTrainingPlanDetailResponseDto appTrainingPlanDetailResponseDto = trainingPlanInfos.get(0);
        if (Objects.nonNull(appTrainingPlanDetailResponseDto)) {
            Map<Long, CourseI18nEntity> courseI18nEntityMap = courseBizService.getAppTrainingPlanDetailResponseDtoCourseMap(appTrainingPlanDetailResponseDto, loginUser.getLanguageCode());
            for (AppPlanWeekDetailDto appPlanWeekDetailDto : appTrainingPlanDetailResponseDto.getWeekList()) {
                appPlanWeekDetailDto.getCourseList().forEach(e -> {
                    CourseI18nEntity courseI18nEntity = courseI18nEntityMap.get(e.getCourseId());
                    if (Objects.nonNull(courseI18nEntity)) {
                        e.setCourseName(courseI18nEntity.getCourseName());
                        e.setCourseDesc(courseI18nEntity.getCourseDesc());
                    }
                });
            }
            responseDto = BeanUtil.copyBean(appTrainingPlanDetailResponseDto, AppUserTrainingPlanDetailResponseDto.class);
            responseDto.setPlanId(planId);
        }
        return responseDto;
    }

    private void fillAppUserTrainingPlanDetailResponseDto(ZnsUserEntity loginUser, AppUserTrainingPlanDetailResponseDto responseDto, UserTrainingPlan userTrainingPlan) {
        AppTrainingPlanDetailResponseDto trainingPlanDetailResponseDto = trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(userTrainingPlan.getPlanId()), loginUser.getLanguageCode()).get(0);
        Map<Long, CourseI18nEntity> courseMap = courseBizService.getAppTrainingPlanDetailResponseDtoCourseMap(trainingPlanDetailResponseDto, loginUser.getLanguageCode());
        responseDto.setMemberType(trainingPlanDetailResponseDto.getMemberType());
        responseDto.setCoverUrl(trainingPlanDetailResponseDto.getCoverUrl());
        responseDto.setDegree(trainingPlanDetailResponseDto.getDegree());
        responseDto.setDegreeName(trainingPlanDetailResponseDto.getDegreeName());
        responseDto.setWeekNum(trainingPlanDetailResponseDto.getWeekNum());
        responseDto.setCourseNum(trainingPlanDetailResponseDto.getCourseNum());
        responseDto.setTitle(trainingPlanDetailResponseDto.getTitle());
        responseDto.setIntroduce(trainingPlanDetailResponseDto.getIntroduce());
        responseDto.setPlanId(userTrainingPlan.getPlanId());
        responseDto.setUserPlanId(userTrainingPlan.getId());
        responseDto.setPlanStatus(userTrainingPlan.getPlanStatus());
        responseDto.setNoticeStatus(userTrainingPlan.getNoticeStatus());
        responseDto.setNoticeTimeStr(userTrainingPlan.getNoticeTimeStr());
        responseDto.setCompletedTraining(userTrainingPlan.getCompletedTraining());
        responseDto.setTotalTraining(userTrainingPlan.getTotalTraining());
        responseDto.setStartDay(userTrainingPlan.getStartDay());
        List<AppPlanWeekDetailDto> weekList = new ArrayList<>();
        List<UserTrainingPlanDetail> userTrainingPlanDetailList = userTrainingPlanDetailService.findByUserPlanId(userTrainingPlan.getId());
        List<List<UserTrainingPlanDetail>> partition = Lists.partition(userTrainingPlanDetailList, trainingPlanDetailResponseDto.getCourseNum());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(userTrainingPlan.getStartDay(), formatter);
        for (int i = 0; i < partition.size(); i++) {
            AppPlanWeekDetailDto appPlanWeekDetailDto = new AppPlanWeekDetailDto();
            String weekStartDay = startDate.plusDays(i * 7).format(formatter);
            appPlanWeekDetailDto.setWeekStartDay(weekStartDay);
            AppPlanWeekDetailDto planWeekDetailDto = trainingPlanDetailResponseDto.getWeekList().get(i);
            appPlanWeekDetailDto.setWeekIntroduce(planWeekDetailDto.getWeekIntroduce());
            // 训练日课程信息填充
            List<AppTrainingPlanCourseDto> courseList = partition.get(i).stream().map(e -> {
                AppTrainingPlanCourseDto appTrainingPlanCourseDto = new AppTrainingPlanCourseDto();
                appTrainingPlanCourseDto.setTrainingId(e.getId());
                appTrainingPlanCourseDto.setCourseId(e.getCourseId());
                CourseI18nEntity courseI18nEntity = courseMap.get(e.getCourseId());
                appTrainingPlanCourseDto.setCourseName(courseI18nEntity.getCourseName());
                appTrainingPlanCourseDto.setCourseDesc(courseI18nEntity.getCourseDesc());
                appTrainingPlanCourseDto.setTrainingDay(e.getTrainingDay());
                appTrainingPlanCourseDto.setTrainingStatus(e.getTrainingStatus());
                appTrainingPlanCourseDto.setRunDataDetailId(e.getRunDataDetailId());
                return appTrainingPlanCourseDto;
            }).collect(Collectors.toList());
            // 组装训练周期中的每一天（包括信息日）
            List<AppTrainingPlanCourseDto> totalList = new ArrayList<>();
            LocalDate weekStartDate = LocalDate.parse(weekStartDay, formatter);
            for (int j = 0; j < 7; j++) {
                String trainingDay = weekStartDate.plusDays(j).format(formatter);
                AppTrainingPlanCourseDto appTrainingPlanCourseDto = new AppTrainingPlanCourseDto();
                AppTrainingPlanCourseDto courseDto = courseList.stream().filter(e -> Objects.equals(e.getTrainingDay(), trainingDay)).findFirst().orElse(null);
                if (Objects.nonNull(courseDto)) {
                    appTrainingPlanCourseDto = courseDto;
                } else {
                    appTrainingPlanCourseDto.setTrainingDay(trainingDay);
                    appTrainingPlanCourseDto.setCourseName(I18nMsgUtils.getMessage("training.plan.rest.day"));
                }
                totalList.add(appTrainingPlanCourseDto);
            }
            appPlanWeekDetailDto.setCourseList(totalList);
            weekList.add(appPlanWeekDetailDto);
        }
        responseDto.setWeekList(weekList);
    }


    public AppUserTrainingPlanCourseResponseDto getCourse(ZnsUserEntity loginUser, AppUserTrainingPlanCourseRequestDto requestDto) {
        Long userPlanId = requestDto.getUserPlanId();
        Long trainingId = requestDto.getTrainingId();
        Long courseId = requestDto.getCourseId();
        AppUserTrainingPlanCourseResponseDto responseDto = new AppUserTrainingPlanCourseResponseDto();
        if (Objects.nonNull(userPlanId)) {
            // 查询用户训练信息
            UserTrainingPlan userTrainingPlan = userTrainingPlanService.findById(userPlanId);
            if (Objects.nonNull(userTrainingPlan)) {
                TrainingPlan trainingPlan = trainingPlanService.findByIdNoDelete(userTrainingPlan.getPlanId());
                responseDto.setDeviceType(trainingPlan.getDeviceType());
                responseDto.setPlanStatus(userTrainingPlan.getPlanStatus());
                responseDto.setCompletedTraining(userTrainingPlan.getCompletedTraining());
                responseDto.setTotalTraining(userTrainingPlan.getTotalTraining());
            }
            if (Objects.nonNull(trainingId)) {
                UserTrainingPlanDetail userTrainingPlanDetail = userTrainingPlanDetailService.findById(trainingId);
                responseDto.setTrainingDay(userTrainingPlanDetail.getTrainingDay());
                responseDto.setTrainingStatus(userTrainingPlanDetail.getTrainingStatus());
                responseDto.setRunDataDetailId(userTrainingPlanDetail.getRunDataDetailId());
            }
        }
        if (Objects.nonNull(courseId) && courseId > 0) {// ios原因，需要同时判断课程id大于0
            ZnsCourseEntity znsCourseEntity = znsCourseService.getI18nCourseById(courseId, loginUser.getLanguageCode());
            if (Objects.nonNull(znsCourseEntity)) {
                // 组装课程信息
                ZnsRunRouteEntity runRouteEntity = znsRunRouteService.selectRunRouteById(znsCourseEntity.getRouteId());
                responseDto.setCourseId(courseId);
                responseDto.setRouteId(znsCourseEntity.getRouteId());
                responseDto.setRouteType(runRouteEntity.getRouteType());
                List<ZnsCourseActionEntity> courseActionList = znsCourseActionService.selectI18nActionByCourseId(courseId, loginUser.getLanguageCode(), znsCourseEntity.getDefaultLangCode());
                if (!CollectionUtils.isEmpty(courseActionList)) {
                    Integer courseDuration = courseActionList.stream().map(ZnsCourseActionEntity::getActionDuration).reduce(0, Integer::sum);
                    responseDto.setCourseDuration(courseDuration);
                }
                responseDto.setCourseName(znsCourseEntity.getCourseName());
                responseDto.setCourseDesc(znsCourseEntity.getCourseDesc());
            }
        } else {
            responseDto.setCourseName(I18nMsgUtils.getMessage("training.plan.rest.day"));
            responseDto.setCourseDesc(I18nMsgUtils.getMessage("training.plan.rest.introduce"));
        }
        return responseDto;
    }


    public Long save(ZnsUserEntity loginUser, AppUserTrainingPlanSaveRequestDto requestDto) {
        UserTrainingPlan userTrainingPlan = new UserTrainingPlan();
        userTrainingPlan.setUserId(loginUser.getId());
        userTrainingPlan.setPlanId(requestDto.getPlanId());
        userTrainingPlan.setNoticeStatus(YesNoStatus.NO.getCode());
        userTrainingPlan.setPlanStatus(TrainingPlanStatusEnum.IN_PROGRESS.getCode());
        userTrainingPlan.setStartDay(requestDto.getStartDay());
        userTrainingPlan.setCompletedTraining(0);
        TrainingPlan trainingPlan = trainingPlanService.findById(requestDto.getPlanId());
        Map<String, Integer> periodMap = JsonUtil.readValue(trainingPlan.getPeriod(), Map.class);
        Integer weekNum = periodMap.get("weekNum");
        userTrainingPlan.setTotalTraining(weekNum * periodMap.get("courseNum"));
        userTrainingPlan.setEndDay(DateUtil.stringDayAddDays(userTrainingPlan.getStartDay(), weekNum * 7));
        userTrainingPlanService.insert(userTrainingPlan);
        List<AppTrainingPlanDetailResponseDto> trainingPlanInfoList = trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(requestDto.getPlanId()), loginUser.getLanguageCode());
        List<AppPlanWeekDetailDto> weekList = trainingPlanInfoList.get(0).getWeekList();
        List<WeekPlanCourseRequestDto> weekCourseList = requestDto.getWeekCourseList();
        List<UserTrainingPlanDetail> detailList = new ArrayList<>();
        for (int i = 0; i < weekNum; i++) {
            List<AppTrainingPlanCourseDto> courseList = weekList.get(i).getCourseList();
            for (int j = 0; j < weekCourseList.size(); j++) {
                UserTrainingPlanDetail userTrainingPlanDetail = new UserTrainingPlanDetail();
                userTrainingPlanDetail.setCourseId(courseList.get(j).getCourseId());
                String trainingDay = DateUtil.stringDayAddDays(weekCourseList.get(j).getTrainingDay(), i * 7);
                userTrainingPlanDetail.setTrainingDay(trainingDay);
                userTrainingPlanDetail.setTrainingStatus(YesNoStatus.NO.getCode());
                userTrainingPlanDetail.setUserPlanId(userTrainingPlan.getId());
                detailList.add(userTrainingPlanDetail);
            }
        }
        userTrainingPlanDetailService.batchInsert(detailList);
        return userTrainingPlan.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    public void modify(ZnsUserEntity loginUser, AppUserTrainingPlanModifyRequestDto requestDto) {
        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findById(requestDto.getUserPlanId());
        if (Objects.isNull(userTrainingPlan)) {
            return;
        }
        List<AppTrainingPlanDetailResponseDto> trainingPlanInfoList = trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(userTrainingPlan.getPlanId()), loginUser.getLanguageCode());
        Integer weekNum = trainingPlanInfoList.get(0).getWeekNum();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(userTrainingPlan.getStartDay(), formatter);
        Integer currentWeekNum = requestDto.getWeekNum();
        String nextStartDay = startDate.plusDays(currentWeekNum * 7).format(formatter);
        if (userTrainingPlanDetailService.existNextWeekFinishedTraining(nextStartDay, requestDto.getUserPlanId())) {
            log.error("用户切换日期异常调整训练日，userPlanId:{},currentWeekNum:{}", requestDto.getUserPlanId(), currentWeekNum);
            throw new BaseException(CommonError.BUSINESS_ERROR.getMsg());
        }
        // 删除老的训练日和提醒日
        userTrainingPlanDetailBizService.deleteNextWeekUnFinishTrainingAndNotice(nextStartDay, requestDto.getUserPlanId());
        // 插入新的训练日和提醒日
        List<UserTrainingPlanNotice> noticeList = new ArrayList<>();
        Long userPlanId = userTrainingPlan.getId();
        Integer noticeStatus = userTrainingPlan.getNoticeStatus();
        List<AppPlanWeekDetailDto> weekList = trainingPlanInfoList.get(0).getWeekList();
        List<WeekPlanCourseRequestDto> weekCourseList = requestDto.getWeekCourseList();
        for (int i = 1; i <= weekNum - currentWeekNum; i++) {
            List<AppTrainingPlanCourseDto> courseList = weekList.get(currentWeekNum + i - 1).getCourseList();
            for (int j = 0; j < weekCourseList.size(); j++) {
                UserTrainingPlanDetail userTrainingPlanDetail = new UserTrainingPlanDetail();
                userTrainingPlanDetail.setCourseId(courseList.get(j).getCourseId());
                String trainingDay = DateUtil.stringDayAddDays(weekCourseList.get(j).getTrainingDay(), i * 7);
                userTrainingPlanDetail.setTrainingDay(trainingDay);
                userTrainingPlanDetail.setTrainingStatus(YesNoStatus.NO.getCode());
                userTrainingPlanDetail.setUserPlanId(userPlanId);
                userTrainingPlanDetailService.insert(userTrainingPlanDetail);
                if (Objects.equals(YesNoStatus.YES.getCode(), noticeStatus)) {
                    UserTrainingPlanNotice userTrainingPlanNotice = new UserTrainingPlanNotice();
                    userTrainingPlanNotice.setPlanId(userTrainingPlan.getPlanId());
                    userTrainingPlanNotice.setUserId(userTrainingPlan.getUserId());
                    userTrainingPlanNotice.setNoticeDate(trainingDay + " " + userTrainingPlan.getNoticeTimeStr());
                    userTrainingPlanNotice.setDetailId(userTrainingPlanDetail.getId());
                    noticeList.add(userTrainingPlanNotice);
                }
            }
        }
        userTrainingPlanNoticeService.batchInsert(noticeList);
    }


    public Boolean check(Long userId) {
        Boolean flag = false;
        UserTrainingPlanQuery query = UserTrainingPlanQuery.builder()
                .userId(userId).planStatus(TrainingPlanStatusEnum.IN_PROGRESS.getCode()).build();
        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findByQuery(query);
        if (Objects.nonNull(userTrainingPlan)) {
            flag = true;
        }
        return flag;
    }


    public void stop(Long userId, AppUserTrainingPlanStopRequestDto requestDto) {
        UserTrainingPlanQuery query = UserTrainingPlanQuery.builder()
                .userId(userId).userPlanId(requestDto.getUserPlanId()).planStatus(TrainingPlanStatusEnum.IN_PROGRESS.getCode()).build();
        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findByQuery(query);
        if (Objects.nonNull(userTrainingPlan)) {
            UserTrainingPlan entity = new UserTrainingPlan();
            entity.setId(requestDto.getUserPlanId());
            entity.setPlanStatus(TrainingPlanStatusEnum.TERMINATED.getCode());
            userTrainingPlanService.update(entity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void setNoticeTime(ZnsUserEntity loginUser, AppUserTrainingPlanNoticeSetRequestDto requestDto) {
        Long userPlanId = requestDto.getUserPlanId();
        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findById(userPlanId);
        if (Objects.nonNull(userTrainingPlan) && TrainingPlanStatusEnum.IN_PROGRESS.getCode().equals(userTrainingPlan.getPlanStatus())) {
            UserTrainingPlan entity = new UserTrainingPlan();
            entity.setId(requestDto.getUserPlanId());
            entity.setNoticeStatus(requestDto.getNoticeStatus());
            entity.setNoticeTimeStr(requestDto.getNoticeTime());
            userTrainingPlanService.update(entity);
            List<UserTrainingPlanDetail> planDetailList = userTrainingPlanDetailService.findByUserPlanId(userPlanId);
            List<Long> userTrainingPlanDetailIdList = planDetailList.stream().map(UserTrainingPlanDetail::getId).collect(Collectors.toList());
            List<UserTrainingPlanNotice> noticeList = userTrainingPlanNoticeService.findByPlanDetailIdList(userTrainingPlanDetailIdList);
            if (!CollectionUtils.isEmpty(noticeList)) {
                // 表示已设置过提醒时间，就把之前的都删除
                userTrainingPlanNoticeService.deleteByDetailIdList(planDetailList.stream().map(UserTrainingPlanDetail::getId).collect(Collectors.toList()));
            }
            List<UserTrainingPlanNotice> collect = planDetailList.stream().map(e -> {
                UserTrainingPlanNotice userTrainingPlanNotice = new UserTrainingPlanNotice();
                userTrainingPlanNotice.setPlanId(userTrainingPlan.getPlanId());
                userTrainingPlanNotice.setUserId(loginUser.getId());
                userTrainingPlanNotice.setDetailId(e.getId());
                userTrainingPlanNotice.setNoticeDate(e.getTrainingDay() + " " + requestDto.getNoticeTime());
                return userTrainingPlanNotice;
            }).collect(Collectors.toList());
            userTrainingPlanNoticeService.batchInsert(collect);
        }
    }


    public AppUserTrainingPlanDetailResponseDto getProgressInfo(ZnsUserEntity loginUser) {
        UserTrainingPlanQuery query = UserTrainingPlanQuery.builder()
                .userId(loginUser.getId()).planStatus(TrainingPlanStatusEnum.IN_PROGRESS.getCode()).build();
        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findByQuery(query);
        AppUserTrainingPlanDetailResponseDto responseDto = new AppUserTrainingPlanDetailResponseDto();
        if (Objects.isNull(userTrainingPlan)) {
            return null;
        }
        fillAppUserTrainingPlanDetailResponseDto(loginUser, responseDto, userTrainingPlan);
        return responseDto;
    }


    public Page<AppUserTrainingPlanResponseDto> listRecord(ZnsUserEntity loginUser, PageQuery pageQuery) {
        Page<AppUserTrainingPlanResponseDto> responseDtoPage = new Page<>();
        UserTrainingPlanPageQuery planPageQuery = BeanUtil.copyBean(pageQuery, UserTrainingPlanPageQuery.class);
        planPageQuery.setUserId(loginUser.getId());
        planPageQuery.setPlanStatusList(List.of(TrainingPlanStatusEnum.COMPLETED.getCode(), TrainingPlanStatusEnum.NOT_COMPLETED.getCode(), TrainingPlanStatusEnum.TERMINATED.getCode()));
        Page<UserTrainingPlan> pageList = userTrainingPlanService.pageList(planPageQuery);
        List<UserTrainingPlan> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return responseDtoPage;
        }
        List<AppUserTrainingPlanResponseDto> responseDtoList = records.stream().map(e -> {
            AppTrainingPlanDetailResponseDto appTrainingPlanDetailResponseDto = trainingPlanBizService.findTrainingPlanInfo(Lists.newArrayList(e.getPlanId()), loginUser.getLanguageCode()).get(0);
            AppUserTrainingPlanResponseDto responseDto = BeanUtil.copyBean(appTrainingPlanDetailResponseDto, AppUserTrainingPlanResponseDto.class);
            responseDto.setPlanId(e.getPlanId());
            responseDto.setUserPlanId(e.getId());
            responseDto.setPlanStatus(e.getPlanStatus());
            return responseDto;
        }).collect(Collectors.toList());
        responseDtoPage.setRecords(responseDtoList);
        responseDtoPage.setTotal(pageList.getTotal());
        return responseDtoPage;
    }


    public void changeStatus(ZnsUserEntity loginUser, AppUserTrainingPlanStatusChangeRequestDto requestDto) {
        Long planId = requestDto.getPlanId();
        UserTrainingPlanQuery query = UserTrainingPlanQuery.builder()
                .userId(loginUser.getId()).planId(planId).planStatus(TrainingPlanStatusEnum.IN_PROGRESS.getCode()).build();
        UserTrainingPlan userTrainingPlan = userTrainingPlanService.findByQuery(query);
        if (Objects.nonNull(userTrainingPlan)) {
            String endDay = userTrainingPlan.getEndDay();
            ZonedDateTime endDate = DateUtil.getDateByDateStrAndZone(endDay, loginUser.getZoneId());
            ZonedDateTime currentDate = DateUtil.getDateByDateStrAndZone(requestDto.getCurrentDay(), loginUser.getZoneId());
            if (currentDate.compareTo(endDate) >= 0) {
                UserTrainingPlan entity = new UserTrainingPlan();
                entity.setId(userTrainingPlan.getId());
                entity.setPlanStatus(TrainingPlanStatusEnum.NOT_COMPLETED.getCode());
                userTrainingPlanService.update(entity);
            }
        }
    }


}
