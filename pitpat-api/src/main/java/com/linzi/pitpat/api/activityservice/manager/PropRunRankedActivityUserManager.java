package com.linzi.pitpat.api.activityservice.manager;

import com.linzi.pitpat.api.activityservice.dto.request.PropRankedActivityQueryDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelAwardResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedSettleRespDto;
import com.linzi.pitpat.api.activityservice.vo.AppleRunDataVo;
import com.linzi.pitpat.api.activityservice.vo.PropRunRankedActivityUserDetailVo;
import com.linzi.pitpat.api.activityservice.vo.PropRunRankedActivityUserDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.data.activityservice.constant.enums.ResultDataStateEnum;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankedLevelStatisticsVo;
import com.linzi.pitpat.data.activityservice.manager.PropRankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityUserAward;
import com.linzi.pitpat.data.activityservice.model.entity.GamePropDo;
import com.linzi.pitpat.data.activityservice.model.entity.PropRunRankedActivityUser;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.query.GamePropQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserGamePropRecordQuery;
import com.linzi.pitpat.data.activityservice.model.query.award.CouponAwardDto;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.activityservice.model.vo.PropRankedUserListVo;
import com.linzi.pitpat.data.activityservice.model.vo.UserPropRecordVo;
import com.linzi.pitpat.data.activityservice.service.ActivityUserAwardService;
import com.linzi.pitpat.data.activityservice.service.GamePropService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserGamePropRecordService;
import com.linzi.pitpat.data.activityservice.service.UserRunDataDetailsCheatService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsMileageService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.constant.enums.PropRankedConstant;
import com.linzi.pitpat.data.awardservice.model.entry.ActivityUserScore;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBagLog;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.WearsI18n;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.CouponI18nQuery;
import com.linzi.pitpat.data.awardservice.model.query.WearsI18nQuery;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagLogService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsI18nService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.enums.PropStatusEnum;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.UserFeedbackDataDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserFeedbackDataQuery;
import com.linzi.pitpat.data.userservice.service.UserFeedbackDataService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.vo.runData.PaceRateChartData;
import com.linzi.pitpat.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户段位参赛信息
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PropRunRankedActivityUserManager {

    private final PropRunRankedActivityUserService propRunRankedActivityUserService;
    private final ActivityUserAwardService activityUserAwardService;
    private final UserWearsBagService userWearsBagService;
    private final UserWearsBagLogService userWearsBagLogService;
    private final WearsI18nService wearsI18nService;
    private final WearsService wearsService;

    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;

    private final UserCouponService userCouponService;
    private final CouponService couponService;
    private final CouponI18nService couponI18nService;

    private final ActivityUserScoreService activityUserScoreService;

    private final ZnsRunActivityUserService runActivityUserService;
    private final ZnsUserRunDataDetailsService userRunDataDetailsService;
    private final ZnsUserRunDataDetailsMileageService userRunDataDetailsMileageService;
    private final PropRankedActivityResultManager propRankedActivityResultManager;
    private final PropUserRankedLevelManager propUserRankedLevelManager;
    private final ZnsUserService userService;
    private final ZnsUserFriendService znsUserFriendService;
    private final UserGamePropRecordService userGamePropRecordService;
    private final GamePropService gamePropService;
    private final UserFeedbackDataService userFeedbackDataService;
    private final UserRunDataDetailsCheatService userRunDataDetailsCheatService;
    private final ISysConfigService sysConfigService;


    public PropRunRankedActivityUserDetailVo findRankedActivityUserDataDetail(PropRankedActivityQueryDto queryDto, ZnsUserEntity loginUser) {
        PropRunRankedActivityUserDetailVo vo = new PropRunRankedActivityUserDetailVo();

        List<PropRunRankedActivityUser> rankedActivityUserList = propRunRankedActivityUserService.findListByActivityId(queryDto.getActivityId());
        List<PropRunRankedActivityUserDto> userUserDataDetails = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rankedActivityUserList)) {
            List<ZnsRunActivityUserEntity> activityUserList = runActivityUserService.findAllActivityUser(queryDto.getActivityId());
            if (!CollectionUtils.isEmpty(activityUserList)) {
                userUserDataDetails = BeanUtil.copyBeanList(rankedActivityUserList, PropRunRankedActivityUserDto.class);
                Map<Long, ZnsRunActivityUserEntity> activityUserMap = activityUserList.stream().collect(Collectors.toMap(ZnsRunActivityUserEntity::getUserId, Function.identity(), (a, b) -> b));
                userUserDataDetails = userUserDataDetails.stream().filter(item -> Objects.nonNull(activityUserMap.get(item.getUserId()))).map(item -> {
                    Currency currency = userAccountService.getCurrency(item.getUserId(), queryDto.getActivityId(), true);
                    item.setCurrency(currency);
                    BigDecimal runAward = activityUserMap.get(item.getUserId()).getRunAward();
                    I18nConstant.currencyFormat(currency.getCurrencyCode(), runAward);
                    item.setRunAward(runAward);
                    ZnsUserRunDataDetailsEntity userRunDataDetail = userRunDataDetailsService.findByIdActually(item.getRunDataDetailsId());
                    //根据用户的跑步机里程转换单位
                    if (Objects.equals(item.getUserId(), loginUser.getId())) {
                        PaceRateChartData paceRateChartData = userRunDataDetailsMileageService.getPaceChartMap2(userRunDataDetail, loginUser.getMeasureUnit());
                        item.setPace(paceRateChartData.getAveragePace());
                        //是否超过15天
                        Boolean serviceTimeOut = DateUtil.betweenDay(userRunDataDetail.getCreateTime(), ZonedDateTime.now()) >= 15;
                        vo.setServiceTimeOut(serviceTimeOut);
                        //查询成绩反馈
                        UserFeedbackDataDo userFeedbackDataDo = userFeedbackDataService.findByQuery(new UserFeedbackDataQuery().setDetailId(item.getRunDataDetailsId()));
                        //新状态设置
                        if (Objects.nonNull(userFeedbackDataDo)) {
                            item.setAppealState(userFeedbackDataDo.getStatus());
                        }
                    }
                    Integer resultDataState = ResultDataStateEnum.stateConvert(item.getIsComplete(), userRunDataDetail.getIsCheat(), Objects.nonNull(userRunDataDetail) ? userRunDataDetail.getRunStatus() : -1, item.getAppealState());
                    item.setResultDataState(resultDataState);
                    //跑步状态
                    int isComplete = 2; //运动中,不是数据库备注,只是前端展示使用临时定义
                    if (userRunDataDetail.getRunStatus() == 1) {
                        //跑步结束,0表示未完成(逃跑)，1表示完成
                        isComplete = item.getIsComplete() == 1 ? 1 : 0;
                    }

                    item.setIsComplete(isComplete);
                    //运动毫秒时
                    item.setRunTimeMillisecond(Long.valueOf(item.getRunTimeMils()));
                    //是否作弊
                    item.setIsCheat(userRunDataDetail.getIsCheat());
                    return item;
                }).toList();
            }
        }

        PropRunRankedActivityUser runRankedActivityUser = userUserDataDetails.stream().filter(item -> Objects.equals(item.getUserId(), queryDto.getUserId())).findFirst().orElse(null);
        if (runRankedActivityUser != null) {
            vo.setRunDataDetailsId(runRankedActivityUser.getRunDataDetailsId());
            AppleRunDataVo appleRunDataVo = getAppleRunDataVo(runRankedActivityUser.getRunDataDetailsId());
            vo.setRunData(appleRunDataVo);
            //货币
            vo.setAmount(runRankedActivityUser.getRunAward());
        }

        Currency currency = userAccountService.getCurrency(queryDto.getUserId(), queryDto.getActivityId(), true);
        vo.setCurrency(currency);
        //排行榜显示顺序应该为：已完赛、跑步中、作弊、逃跑
        List<PropRunRankedActivityUserDto> completeList = userUserDataDetails.stream().filter(item -> Objects.equals(item.getIsComplete(), 1) && Objects.equals(item.getIsCheat(), 0))
                .sorted(Comparator.comparing(PropRunRankedActivityUserDto::getRunTimeMillisecond).thenComparing(PropRunRankedActivityUserDto::getId)).toList(); //已完赛
        for (int i = 0; i < completeList.size(); i++) {
            PropRunRankedActivityUserDto activityUserDto = completeList.get(i);
            activityUserDto.setRank(i + 1);
        }
        List<PropRunRankedActivityUserDto> userDtos = new ArrayList<>(completeList);//已完赛
        List<PropRunRankedActivityUserDto> runingList = userUserDataDetails.stream().filter(item -> Objects.equals(item.getIsComplete(), 2) && Objects.equals(item.getIsCheat(), 0))
                .sorted(Comparator.comparing(PropRunRankedActivityUserDto::getId)).toList();
        userDtos.addAll(runingList);//跑步中
        List<PropRunRankedActivityUserDto> cheatList = userUserDataDetails.stream().filter(item -> Objects.equals(item.getIsCheat(), 1)).toList();
        userDtos.addAll(cheatList);//作弊
        List<PropRunRankedActivityUserDto> failList = userUserDataDetails.stream().filter(item -> Objects.equals(item.getIsComplete(), 0) && Objects.equals(item.getIsCheat(), 0))
                .sorted(Comparator.comparing(PropRunRankedActivityUserDto::getId)).toList();
        userDtos.addAll(failList);//逃跑
        fillUserFollowData(loginUser, userDtos);
        vo.setUserUserDataDetails(userDtos);

        //优惠券
        List<UserCoupon> userCouponList = userCouponService.selectUserCouponByActivityIdUserId(queryDto.getActivityId(), queryDto.getUserId());

        if (!CollectionUtils.isEmpty(userCouponList)) {
            List<CouponPageVo> list = userCouponList.stream().map(item -> {
                CouponPageVo couponPageVo = new CouponPageVo();
                BeanUtils.copyProperties(item, couponPageVo);
                return couponPageVo;
            }).toList();
            vo.setCouponAwardList(list);
        }
        //服装
        List<UserWearsBag> wearsBagList = userWearsBagService.findList(queryDto.getUserId(), queryDto.getActivityId());
        vo.setWearAwardList(wearsBagList);

        //积分
        List<ActivityUserScore> activityUserScoreList = activityUserScoreService.selectActivityUserScoreByActivityIdUserId(queryDto.getActivityId(), queryDto.getUserId());

        int score = 0;
        if (!CollectionUtils.isEmpty(activityUserScoreList)) {
            score = activityUserScoreList.stream().mapToInt(ActivityUserScore::getScore).sum();
        }
        vo.setScore(score);


        //查询是否有风控检测中
        Integer riskReview = userRunDataDetailsCheatService.isRiskReview(Collections.singletonList(queryDto.getActivityId()));
        vo.setIsRiskReview(riskReview);

        //设置虚拟设备
        List<ZnsUserRunDataDetailsEntity> detailsEntityList = userRunDataDetailsService.getUserDetailByActivityId(queryDto.getUserId(), queryDto.getActivityId());
        if (!CollectionUtils.isEmpty(detailsEntityList)) {
            vo.setVirtual(sysConfigService.isVirtualRecord(detailsEntityList.get(0).getId()) ? 1 : 0);
        }
        return vo;
    }

    /**
     * 设置用户关注信息及账号私密类型
     *
     * @param loginUser
     * @param userDtos
     */
    private void fillUserFollowData(ZnsUserEntity loginUser, List<PropRunRankedActivityUserDto> userDtos) {
        List<Long> friendIds = userDtos.stream().map(PropRunRankedActivityUserDto::getUserId).collect(Collectors.toList());
        List<ZnsUserEntity> userList = userService.findByIds(friendIds);
        Map<Long, ZnsUserEntity> userMap = userList.stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
        List<Long> followingIdList = znsUserFriendService.getFollowingIdList(loginUser.getId(), friendIds);
        userDtos.forEach(e -> {
            ZnsUserEntity user = userMap.get(e.getUserId());
            if (Objects.nonNull(user)) {
                e.setIsPrivacy(user.getIsPrivacy());
            }
            e.setIsFollow(followingIdList.contains(e.getUserId()));
            if (Objects.equals(loginUser.getId(), e.getUserId())) {
                // 本人不展示关注按钮
                e.setIsFollow(null);
            }
            //填充道具信息
            Map<Long, GamePropDo> propMap = gamePropService.findList(GamePropQuery.builder().build()).stream().collect(Collectors.toMap(GamePropDo::getPropId, Function.identity()));

            UserGamePropRecordQuery query = UserGamePropRecordQuery.builder().userId(e.getUserId()).activityId(e.getActivityId()).build();
            List<UserPropRecordVo> propRecordList = userGamePropRecordService.findList(query).stream().map(k -> {
                UserPropRecordVo propRecordVo = new UserPropRecordVo();
                propRecordVo.setPropId(k.getPropId());
                propRecordVo.setPropStatus(k.getPropStatus());
                return propRecordVo;
            }).collect(Collectors.toList());
            for (UserPropRecordVo recordDo : propRecordList) {
                GamePropDo gameProp = propMap.get(recordDo.getPropId());
                if (gameProp != null) {
                    recordDo.setPropName(gameProp.getPropName());
                    if (PropStatusEnum.USED.getCode().equals(recordDo.getPropStatus())) {
                        recordDo.setImageUrl(gameProp.getImageUrl());
                    } else {
                        recordDo.setImageUrl(gameProp.getFreeImageUrl());
                    }
                    recordDo.setRemark(gameProp.getRemark());
                }
            }
            e.setPropRecordVos(propRecordList);

        });
    }

    /**
     * 获取苹果健康-运动数据
     *
     * @param runDataDetailsId
     * @return
     */
    private AppleRunDataVo getAppleRunDataVo(Long runDataDetailsId) {
        ZnsUserRunDataDetailsEntity runDataDetailsEntity = userRunDataDetailsService.findById(runDataDetailsId);
        if (runDataDetailsEntity == null) {
            return null;
        }
        AppleRunDataVo appleRunDataVo = new AppleRunDataVo();
        appleRunDataVo.setRunMileage(runDataDetailsEntity.getRunMileage());
        appleRunDataVo.setCalorie(runDataDetailsEntity.getKilocalorie());
        appleRunDataVo.setRunType(runDataDetailsEntity.getRunType());
        appleRunDataVo.setStepNum(runDataDetailsEntity.getStepNum());
        appleRunDataVo.setRunTimeMillisecond(runDataDetailsEntity.getRunTimeMillisecond());
        return appleRunDataVo;
    }

    /**
     * 统计用户段位数据
     *
     * @param user 当前登录用户
     * @return UserRankedLevelStatisticsVo
     */
    public PropUserRankedLevelStatisticsVo statisticsUserRankedData(ZnsUserEntity user) {
        return propRunRankedActivityUserService.statisticsUserRankedData(user);
    }

    /**
     * 获取用户获取的最新的段位奖励配置
     *
     * @param activityId
     * @param userId
     * @return
     */
    public PropUserRankedLevelAwardResponseDto findUserAward(Long activityId, Long userId, String languageCode) {
        //构造结果对象
        ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(userId);
        PropUserRankedLevelAwardResponseDto result = new PropUserRankedLevelAwardResponseDto();
        result.setCurrency(I18nConstant.buildCurrency(accountEntity.getCurrencyCode()));
        result.setAmount(BigDecimal.ZERO); //金额奖励
        result.setScore(0); //积分奖励
        List<CouponAwardDto> couponAwardList = new ArrayList<>();//券奖励
        result.setCouponAwardList(couponAwardList);
        List<WearAwardDto> wearAwardList = new ArrayList<>(); //服装奖励
        result.setWearAwardList(wearAwardList);

        //查询获取的奖励
        List<ActivityUserAward> awardList = activityUserAwardService.findByActivityId(activityId, userId);
        if (CollectionUtils.isEmpty(awardList)) {
            return result;
        }
        //排除段位奖励
        awardList = awardList.stream().filter(itme -> !AwardSentTypeEnum.RANK_AWARD.getType().equals(itme.getSendType())).toList();
        //金额奖励
        List<Long> accountDestailIds = awardList.stream().filter(item -> Objects.nonNull(item.getUserAccountDetailId()) && item.getUserAccountDetailId() != -1).map(ActivityUserAward::getUserAccountDetailId).toList();
        if (!CollectionUtils.isEmpty(accountDestailIds)) {
            List<ZnsUserAccountDetailEntity> list = userAccountDetailService.listByIds(accountDestailIds);
            //奖励金额累计
            BigDecimal amount = list.stream().map(ZnsUserAccountDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setAmount(amount);
        }

        //积分奖励
        List<Long> userScoreIds = awardList.stream().filter(item -> Objects.nonNull(item.getUserScoreId()) && item.getUserScoreId() != -1).map(ActivityUserAward::getUserScoreId).toList();
        if (!CollectionUtils.isEmpty(userScoreIds)) {
            List<ActivityUserScore> list = activityUserScoreService.listByIds(userScoreIds);
            Integer score = list.stream().map(ActivityUserScore::getScore).reduce(0, Integer::sum);
            result.setScore(score);
        }

        //券奖励
        List<Long> userCouponIds = awardList.stream().filter(item -> Objects.nonNull(item.getUserCouponId()) && item.getUserCouponId() != -1).map(ActivityUserAward::getUserCouponId).toList();
        if (!CollectionUtils.isEmpty(userCouponIds)) {
            List<UserCoupon> list = userCouponService.findListByIds(userCouponIds);
            List<Long> couponIds = list.stream().map(UserCoupon::getCouponId).toList();
            List<Coupon> coupons = couponService.findListByIds(couponIds);
            Map<Long, Coupon> couponMap = coupons.stream().collect(Collectors.toMap(Coupon::getId, Function.identity()));
            for (UserCoupon userCoupon : list) {
                CouponAwardDto couponAwardDto = new CouponAwardDto();
                couponAwardDto.setCouponId(userCoupon.getCouponId());
                couponAwardDto.setNum(1);
                Coupon coupon = couponMap.get(userCoupon.getCouponId());
                if (coupon != null) {
                    CouponI18n couponI18n = couponI18nService.findDefaultByQuery(CouponI18nQuery.builder().couponId(userCoupon.getCouponId()).langCode(languageCode).defaultLangCode(coupon.getDefaultLangCode()).build());
                    if (couponI18n != null) {
                        couponAwardDto.setCouponTitle(couponI18n.getTitle());
                        couponAwardDto.setCouponName(couponI18n.getDescription());
                    } else {
                        couponAwardDto.setCouponTitle(coupon.getTitle());
                        couponAwardDto.setCouponName(coupon.getDescription());
                    }
                }
                couponAwardList.add(couponAwardDto);
            }
        }

        //服装奖励
        List<Long> userWearsIds = awardList.stream().filter(item -> Objects.nonNull(item.getUserWearsBaglogId()) && item.getUserWearsBaglogId() != -1).map(ActivityUserAward::getUserWearsBaglogId).toList();
        if (!CollectionUtils.isEmpty(userWearsIds)) {
            List<UserWearsBagLog> userWearsBagLogs = userWearsBagLogService.listByIds(userWearsIds);
            List<Long> bagIds = userWearsBagLogs.stream().map(UserWearsBagLog::getBagId).toList();
            List<UserWearsBag> wearsBags = userWearsBagService.findListByIds(bagIds);
            Map<Long, UserWearsBag> bagMap = wearsBags.stream().collect(Collectors.toMap(UserWearsBag::getId, Function.identity()));
            for (UserWearsBagLog userWearsBagLog : userWearsBagLogs) {
                WearAwardDto wearAwardDto = new WearAwardDto();
                UserWearsBag userWearsBag = bagMap.get(userWearsBagLog.getBagId());
                if (userWearsBag != null) {
                    Wears wear = wearsService.getWearByWearIdAndType(userWearsBag.getWearType(), userWearsBag.getWearValue());
                    if (wear == null) {
                        continue;
                    }
                    wearAwardDto.setWearName(userWearsBag.getWearName());
                    WearsI18nQuery query = WearsI18nQuery.builder().clothId(wear.getId()).langCode(languageCode).defaultLangCode(wear.getDefaultLangCode()).build();
                    WearsI18n i18n = wearsI18nService.findByQuery(query);
                    if (i18n != null) {
                        wearAwardDto.setWearName(i18n.getName());
                    }
                    wearAwardDto.setWearValue(userWearsBag.getWearValue());
                    wearAwardDto.setWearImageUrl(userWearsBag.getWearImageUrl());
                    wearAwardDto.setWearType(userWearsBag.getWearType());
                }
                wearAwardDto.setExpiredTime(userWearsBagLog.getExpiredTime());
                wearAwardList.add(wearAwardDto);
            }
        }
        return result;
    }

    /**
     * 获取用户段位赛结算信息
     *
     * @param queryDto
     * @return
     */
    public PropUserRankedSettleRespDto getUserRankedSettle(PropRankedActivityQueryDto queryDto, String languageCode) {
        //查询用户奖励发放状态
        PropRunRankedActivityUser rankedActivityUser = propRunRankedActivityUserService.findByActivityIdAndUserId(queryDto.getActivityId(), queryDto.getUserId());
        if (rankedActivityUser == null) {
            throw new BaseException("rankedActivityUser not exist");
        }
        PropUserRankedSettleRespDto result = new PropUserRankedSettleRespDto();

//        if (!PropRankedConstant.AwardSendEnum.AWARD_SEND_2.code.equals(rankedActivityUser.getAwardSendStatus())) {
//            //奖励未发放，重新发一次
////            try {
////                propRankedActivityResultManager.rankAwardSettle(new PropRankedUserQuery(queryDto.getActivityId(), queryDto.getUserId()), "api");
////            }catch (BaseException e){
////               log.warn("getUserRankedSettle acquire lock failed {}",e.getMessage());
////            }
//
//            rankedActivityUser = propRunRankedActivityUserService.findByActivityIdAndUserId(queryDto.getActivityId(), queryDto.getUserId());
//            if (!PropRankedConstant.AwardSendEnum.AWARD_SEND_2.code.equals(rankedActivityUser.getAwardSendStatus())) {
//                //发放奖励超时
//                result.setIsSettleTimeOut(true);
//                return result;
//            }
//        }

        //获取用户
        PropUserRankedLevel userRankedLevel = propUserRankedLevelManager.findByUserId(queryDto.getUserId(), true);
        PropUserRankedLevelDto userRankedLevelDto = BeanUtil.copyBean(userRankedLevel, PropUserRankedLevelDto.class);
        userRankedLevelDto.setRankedLevelChange(rankedActivityUser.getRankedLevelChange());
        userRankedLevelDto.setRankedScoreChange(rankedActivityUser.getRankedScoreChange());
        result.setUserRankedLevel(userRankedLevelDto);
        if (userRankedLevel != null) {
            result.setSegment(propRunRankedActivityUserService.getCurrentRankSegment(queryDto.getUserId()));
            PropRunRankedActivityUser runRankedActivityUser = propRunRankedActivityUserService.findByActivityIdAndUserId(queryDto.getActivityId(), queryDto.getUserId());
            if (runRankedActivityUser != null) {
                result.setIsInPlacement(runRankedActivityUser.getIsPlacement());
            }
        }
        //获取奖励
        if (PropRankedConstant.AwardSendEnum.AWARD_SEND_2.code.equals(rankedActivityUser.getAwardSendStatus())) {
            PropUserRankedLevelAwardResponseDto userAvailableAward = findUserAward(queryDto.getActivityId(), queryDto.getUserId(), languageCode);
            result.setUserRankedLevelAward(userAvailableAward);
        }


        //获取段位赛排名
        List<PropRankedUserListVo> userList = runActivityUserService.findPropUserListVoList(queryDto.getActivityId());
        for (int i = 0; i < userList.size(); i++) {
            PropRankedUserListVo rankedUserListVo = userList.get(i);
            rankedUserListVo.setRunTime(NumberUtils.formatTime(rankedUserListVo.getRunTimeMillisecond()));
            if (rankedUserListVo.getUserId().equals(queryDto.getUserId())) {
                userRankedLevelDto.setRanking(rankedUserListVo.getRank());
            }
            //作弊
            if (Objects.equals(rankedUserListVo.getIsCheat(), 1)) {
                rankedUserListVo.setStatus(4);
            }
        }

        //加了作弊重新排序
        List<PropRankedUserListVo> sortList = userList.stream().filter(item -> Arrays.asList(1, 2).contains(item.getStatus())).collect(Collectors.toList()); //完成,进行中
        sortList.addAll(userList.stream().filter(item -> Objects.equals(item.getStatus(), 4)).toList()); //作弊
        sortList.addAll(userList.stream().filter(item -> Objects.equals(item.getStatus(), 3)).toList()); //逃跑
        //重新定义排名
        for (int i = 0; i < sortList.size(); i++) {
            PropRankedUserListVo rankedUserListVo = sortList.get(i);
            if (Objects.equals(rankedUserListVo.getStatus(), 1)) {
                //跑步完成。定义排名
                rankedUserListVo.setRank(i + 1);
            } else {
                //跑步未完成，排名都是-1
                rankedUserListVo.setRank(-1);
            }
        }

        result.setUserList(sortList);

        return result;
    }

}
