package com.linzi.pitpat.api.mallservice.controller.h5;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.mallservice.mananger.PayPalBussiness;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.RedisConstants;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.awardservice.dto.api.InitPayResp;
import com.linzi.pitpat.data.awardservice.dto.api.PayPalConfirmRequest;
import com.linzi.pitpat.data.awardservice.dto.api.PaypalRequest;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.vo.CanadaCouponVo;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RestController
@Slf4j
@RequestMapping({"h5/canada/paypal", "app/paypal"})
@RequiredArgsConstructor
public class PayPalController extends BaseAppController {

    private final PayPalBussiness payPalBussiness;
    private final PaypalPayService paypalPayService;
    private final CouponService couponService;
    private final ZnsUserAccountService znsUserAccountService;
    private final RedissonClient redissonClient;

    /**
     * 获取PayPal收银台地址
     *
     * @param request
     * @return
     */
    @PostMapping("/initiatePayment")
    public Result<InitPayResp> initiatePayment(@RequestBody PaypalRequest request) {

        ZnsUserEntity loginUser = getLoginUser();
        request.setUserId(loginUser.getId());
        Result errRes = payPalBussiness.checkInitiatePayment(request);
        if (Objects.nonNull(errRes)) {
            return errRes;
        }
        if (!StringUtils.hasText(request.getAmount())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist","amount"));
        }
        BigDecimal payAmount = new BigDecimal(request.getAmount().replaceAll(",", "."));

        RLock lock = redissonClient.getLock(RedisConstants.USER_PAY + loginUser.getId());
        try {
            if (LockHolder.tryLock(lock, 1, 3)) {
                InitPayResp initPayResp = payPalBussiness.initiatePayment(payAmount, request.getType(), request.getRefId());
                if (initPayResp.getSuccess()) {
                    //生成paypal支付记录
                    PaypalPay paypalPay = new PaypalPay();
                    paypalPay.setAmount(payAmount);
                    paypalPay.setType(request.getType());
                    paypalPay.setRefId(request.getRefId());
                    paypalPay.setInfo(request.getInfo());
                    paypalPay.setPayNo(initPayResp.getBillNo());
                    paypalPay.setTradeNo(initPayResp.getTradeNo());
                    paypalPay.setUserId(loginUser.getId());
                    paypalPay.setStatus(PayConstant.PayStatusEnum.PAY_STATUS_0.type); //发起支付
                    paypalPay.setPayType(PayConstant.PayTypeEnum.PAYPAL.type);
                    I18nConstant.CurrencyCodeEnum currencyCodeEnum = Optional.ofNullable(I18nConstant.CurrencyCodeEnum.findByCode(initPayResp.getCurrencyCode())).orElse(I18nConstant.CurrencyCodeEnum.USD);
                    paypalPay.setCurrencyCode(currencyCodeEnum.getCode());
                    paypalPay.setCurrencySymbol(currencyCodeEnum.getSymbol());
                    paypalPayService.save(paypalPay);
                }
                return CommonResult.success(initPayResp);
            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "充值中，请勿重复点击");
            }
        } catch (Exception e) {
            log.error("充值异常，e", e);
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "充值失败");
        }
    }

    /**
     * PayPal验证成功后确认扣款
     *
     * @param request
     * @return
     */
    @PostMapping("/successPayment")
    public Result successPayment(@RequestBody PayPalConfirmRequest request) {
        String tradeNo = request.getTradeNo();
        if (!StringUtils.hasText(tradeNo)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        return payPalBussiness.payment(tradeNo, false);
    }

    @PostMapping("/getPaymentInfo")
    public Result getPaymentInfo(@RequestBody PayPalConfirmRequest request) {
        String tradeNo = request.getTradeNo();
        if (!StringUtils.hasText(tradeNo)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        PaypalPay paypalPay = paypalPayService.lambdaQuery().eq(PaypalPay::getTradeNo, tradeNo).one();
        ZnsUserAccountEntity accountEntity = znsUserAccountService.getByUserId(paypalPay.getUserId());

        HashMap<String, Object> map = new HashMap<>();
        map.put("type", paypalPay.getType());
        map.put("amount", paypalPay.getAmount().toString());
        map.put("currency", I18nConstant.buildCurrency(accountEntity.getCurrencyCode()));
        return CommonResult.success(map);
    }

    /**
     * PayPal确认失败
     *
     * @param request
     * @return
     */
    @PostMapping("/failPayment")
    public Result failPayment(@RequestBody PayPalConfirmRequest request) {
        String tradeNo = request.getTradeNo();
        if (!StringUtils.hasText(tradeNo)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        return payPalBussiness.failPayment(tradeNo);
    }

    /**
     * 加拿大用户卷列表
     *
     * @param
     * @return
     */
    @PostMapping("/couponList")
    public Result<List<CanadaCouponVo>> couponList() {
        String languageCode = getLanguageCode();
        ZnsUserEntity user = getLoginUser();
        List<CanadaCouponVo> list = couponService.getCanadaTempCoupons(languageCode, user.getId());
        return CommonResult.success(list);
    }
}
