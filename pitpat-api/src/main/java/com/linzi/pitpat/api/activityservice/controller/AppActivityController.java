package com.linzi.pitpat.api.activityservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.manager.signup.ActivitySignUpManager;
import com.linzi.pitpat.api.mallservice.mananger.MallCategoryModelManager;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.converter.ActivityConverter;
import com.linzi.pitpat.data.activityservice.dto.api.request.ActivityEquipmentJumpRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.ActivityRankJumpRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.ActivityRequestDto;
import com.linzi.pitpat.data.activityservice.dto.api.request.MarathonTeamCreateRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.ActivityVideoUploadRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.EnrollActivityRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.EnrollSignupActivityRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.MarathonTeamJoinRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.QueryActivityEquipmentRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.ReportUsersRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SeriesActivityRankRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SingleActivityIdRequest;
import com.linzi.pitpat.data.activityservice.dto.api.request.activity.SingleRunDetailIdRequest;
import com.linzi.pitpat.data.activityservice.dto.api.response.EnrollActivityResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.SignupFirstCheckResponseDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityAwardReviewDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ActivityVideoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ApiActivityEquipmentInfoDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CardUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.CompetitionScheduleDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.HistoryRewardDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.HomeRecommendActivityDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyActRecordDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyRaceDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.MyTeamActRecordDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.RankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.ReportUserDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SeriesActivityRankResponse;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SingleActivityDetailDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.SingleStageRankDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.activity.TeamPersonalRankResultDto;
import com.linzi.pitpat.data.activityservice.manager.ActivityLimitManager;
import com.linzi.pitpat.data.activityservice.manager.ActivityVideoViewManger;
import com.linzi.pitpat.data.activityservice.manager.api.AppActivityManager;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityPolymerizationRecord;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityAwardPopRespDto;
import com.linzi.pitpat.data.activityservice.model.resp.ActivityWatchInfoRespDto;
import com.linzi.pitpat.data.activityservice.model.vo.activity.EquipmentRecommendVo;
import com.linzi.pitpat.data.activityservice.service.ActivityPolymerizationRecordService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.request.ActivityShareDto;
import com.linzi.pitpat.data.request.CommonShareDto;
import com.linzi.pitpat.data.resp.CommonShareRespDto;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.dto.request.ActivityQueryDto;
import com.linzi.pitpat.dto.response.PolymerizationActivityResponseDto;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 单赛事-控制器
 */
@Slf4j
@RestController
@RequestMapping({"/app/act/activity", "/h5/act/activity"})
@RequiredArgsConstructor
public class AppActivityController extends BaseAppController {
    private final AppActivityManager appActivityManager;
    private final ActivityPolymerizationRecordService activityPolymerizationRecordService;
    private final ActivityConverter activityConverter;
    private final RedissonClient redissonClient;
    private final MallCategoryModelManager mallCategoryModelManager;
    private final ActivitySignUpManager activitySignUpManager;
    private final ActivityLimitManager activityLimitManager;
    private final ActivityVideoViewManger activityVideoViewManger;

    /**
     * 打开上传视频参赛页
     *
     * @param
     * @return
     * @tag 4.7.0
     * @since 4.7.0
     */
    @PostMapping("/video/list")
    public Result<List<ActivityVideoDto>> videoList(@RequestBody SingleActivityIdRequest request) {

        return CommonResult.success(activityVideoViewManger.getMyVideoList(request.getMainActivityId(), getLoginUser()));
    }

    /**
     * 上传审核视频
     *
     * @param
     * @return
     * @tag 4.7.0
     * @since 4.7.0
     */
    @PostMapping("/upload/video")
    public Result uploadVideo(@RequestBody ActivityVideoUploadRequest request) {
        activityVideoViewManger.upload(request.getDetailId(), request.getVideo());
        return CommonResult.success();
    }


    /**
     * 马拉松创建团队
     *
     * @param
     * @return
     * @since 4.6.1
     */
    @PostMapping("/marathonCreateTeam")
    public Result marathonCreateTeam(@RequestBody MarathonTeamCreateRequest request) {
        if(VersionConstant.V4_7_5 >getAppVersion()){
            throw new BizI18nException("marathon.474.create.team.upgrade");
        }
        appActivityManager.createMarathonTeam(request, getLoginUser(), getAppVersion());

        return CommonResult.success();
    }

    /**
     * 申请加入马拉松团队
     *
     * @param
     * @return
     * @since 4.6.1
     */
    @PostMapping("/marathonTeamJoin")
    public Result marathonTeamJoin(@RequestBody MarathonTeamJoinRequest request) {
        if(VersionConstant.V4_7_5 >getAppVersion()){
            throw new BizI18nException("marathon.474.create.team.upgrade");
        }
        appActivityManager.joinMarathonTeam(request.getTeamId(), getLoginUser());
        return CommonResult.success();
    }

    /**
     * 赛事设备推荐
     *
     * @param
     * @return
     */
    @PostMapping("/equipmentRecommend")
    public Result<EquipmentRecommendVo> equipmentRecommend(@RequestBody SingleRunDetailIdRequest request) {
        return CommonResult.success(activityLimitManager.equipmentRecommend(request.getDetailId(), getLanguageCode(), request.getSubType()));
    }

    /**
     * 获取用户活动中设备信息
     * @param request
     * @return
     */

    /**
     * 活动阶段点击进入游戏（传活动id即可）
     *
     * @param
     * @return
     */
    @PostMapping("/stageActivityEnter")
    public Result stageActivityEnter(@RequestBody SingleActivityIdRequest request) {
        appActivityManager.stageActivityEnter(request.getMainActivityId(), getLoginUser());
        return CommonResult.success();
    }

    //获取用户活动中设备信息
    @PostMapping("/queryActivityEquipmentInfo")
    public Result<ApiActivityEquipmentInfoDto> queryActivityEquipmentInfo(@RequestBody QueryActivityEquipmentRequest request) {

        return CommonResult.success(appActivityManager.queryActivityEquipmentInfo(request.getMainActivityId(), request.getUserId(), getLoginUser()));
    }


    /**
     * 用户报名前检查（第一步）z
     *
     * @param
     * @return
     */
    @PostMapping("/signupFirstCheck")
    public Result<SignupFirstCheckResponseDto> signupFirstCheck(@RequestBody EnrollSignupActivityRequest request) {
        SignupFirstCheckResponseDto responseDto = appActivityManager.signupFirstCheck(request.getMainActivityId(), getLoginUser());
        return CommonResult.success(responseDto);
    }

    /**
     * 用户报名前检查（第三步），第二步是设备校验（/app/walk/equipment/judgeByActivityId）
     *
     * @param
     * @return
     */
    @PostMapping("/signupPreCheck")
    public Result signupPreCheck(@RequestBody EnrollSignupActivityRequest request) {
        try {
            Integer appVersion = getAppVersion();
            appActivityManager.signupPreCheck(request.getMainActivityId(), request.getTeamId(), getCurrentUser(), appVersion);
        } catch (Exception e) {
            log.info("signupPreCheck 异常,e=", e);
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), e.getMessage());
        }
        return CommonResult.success();
    }

    /**
     * 单赛事-myRace相关信息
     *
     * @param
     * @return
     */
    @PostMapping("/singleMyRace")
    public Result<MyRaceDto> myRace(@Validated @RequestBody SingleActivityIdRequest request) {
        MyRaceDto myRaceDto = appActivityManager.myRace(request.getMainActivityId(), getCurrentUser());
        appActivityManager.cleanupAward(myRaceDto, getAppVersion());
        return CommonResult.success(myRaceDto);
    }

    /**
     * 单赛事-myRaceTeam相关信息
     *
     * @param
     * @return
     */
    @PostMapping("/singleTeamMyRace")
    public Result<MyTeamActRecordDto> teamMyRace(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.teamMyRace(request, getCurrentUser()));
    }


    /**
     * 单赛事-历史奖励
     *
     * @param
     * @return
     */
    @PostMapping("/singleHistoryReward")
    public Result<List<HistoryRewardDto>> historyReward(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.historyReward(request.getMainActivityId(), getCurrentUser()));
    }

    /**
     * 单赛事-参赛选手
     *
     * @param
     * @return
     */
    @PostMapping("/singleReportUsers")
    public Result<Page<ReportUserDto>> reportUsers(@RequestBody ReportUsersRequest request) {
        return CommonResult.success(appActivityManager.reportUsers(request, getCurrentUser()));
    }

    /**
     * 单赛事-排行榜
     *
     * @param
     * @return
     */
    @PostMapping("/singleRank")
    public Result<List<RankDto>> singleRank(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.singleRank(request.getMainActivityId(), getCurrentUser()));
    }

    /**
     * 单赛-团赛-个人-排行榜
     *
     * @param
     * @return
     * @tag 4.6.0
     * @since 4.6.0
     */
    @PostMapping("/team/single/personal/rank")
    public Result<TeamPersonalRankResultDto> teamSinglePersonalRank(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.teamSinglePersonalRank(request.getMainActivityId(), getCurrentUser()));
    }


    /**
     * 单赛事-阶段排行榜
     *
     * @param
     * @return
     */
    @PostMapping("/singleStageRank")
    public Result<List<SingleStageRankDto>> singleStageRank(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.singleStageRank(request.getMainActivityId()));
    }


    /**
     * 单赛事-活动相关信息
     *
     * @param
     * @return
     */
    @PostMapping("/singleActivityDetail")
    public Result<SingleActivityDetailDto> singleActivityDetail(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.singleActivityDetail(request.getMainActivityId(), getCurrentUser(), request.getIsPolyList(), getAppVersion()));
    }

    /**
     * 活得资格卡用户
     *
     * @param
     * @return
     */
    @PostMapping("/getCardUserDto")
    public Result<List<CardUserDto>> getCardUserDto(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.getCardUserDto(request.getMainActivityId()));
    }



    /**
     * 我的比赛-本场赛事记录
     *
     * @param
     * @return
     */
    @PostMapping("/myActRecord")
    public Result<List<MyActRecordDto>> myActRecord(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.MyActRecordDto(new Page<>(request.getPageNum(), request.getPageSize()), request.getMainActivityId(), getCurrentUser()));
    }

    /**
     * 单赛事-赛事日程（聚合）
     *
     * @param
     * @return
     */
    @PostMapping("/competitionSchedule")
    public Result<List<CompetitionScheduleDto>> competitionSchedule(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.competitionSchedule(request, getCurrentUser()));
    }

    /**
     * 单赛事-活动报名
     *
     * @param
     * @return
     */
    @PostMapping("/enrollActivity")
    public Result<EnrollActivityResponseDto> enrollActivity(@RequestBody EnrollActivityRequest request) {
        RLock lock = redissonClient.getLock(RedisConstants.ACTIVITY_PARTICIPATION_KEY + request.getMainActivityId() + getCurrentUser().getId());
        return LockHolder.tryLock(lock, 3, () -> appActivityManager.enrollActivity(request, getCurrentUser(), getAppVersion(), false));
    }

    /**
     * 系列赛-系列赛关卡报名（进入前校验）
     *
     * @param
     * @return
     */
    @PostMapping("/seriesLevelEnroll")
    public Result seriesLevelEnroll(@RequestBody SingleActivityIdRequest request) {
        appActivityManager.seriesLevelEnroll(request.getMainActivityId(), getCurrentUser());
        return CommonResult.success();
    }

    private ZnsUserEntity getCurrentUser() {
        if (getLoginUser() == null) {
            ZnsUserEntity user = new ZnsUserEntity();
            user.setZoneId(getZoneId());
            user.setLanguageCode(getLanguageCode());
            user.setGender(0);
            return user;
        } else {
            return getLoginUser();
        }
    }

    /**
     * 活动参赛包是否装扮接口
     *
     * @param
     * @return
     */
    @PostMapping("/activity/dress")
    public Result dress(@RequestBody ActivityRequestDto request) {
        appActivityManager.activityDress(request, getCurrentUser());
        return CommonResult.success();
    }

    /**
     * 活动分享成功回调
     *
     * @param
     * @return
     */
    @PostMapping("/shareSuccessCallback")
    public Result shareSuccessCallback(@RequestBody SingleActivityIdRequest request) {
        appActivityManager.shareSuccessCallback(request.getMainActivityId(), getCurrentUser());
        return CommonResult.success();
    }

    /**
     * 查询活动用户分享积分
     *
     * @param
     * @return
     */
    @PostMapping("/queryShareAward")
    public Result<Integer> queryShareAward(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.queryShareAward(request.getMainActivityId(), getCurrentUser()));
    }

    /**
     * 系列赛-排行榜
     *
     * @param
     * @return
     */
    @PostMapping("/seriesActivityRank")
    public Result<SeriesActivityRankResponse> seriesActivityRank(@RequestBody SeriesActivityRankRequest request) {
        return CommonResult.success(appActivityManager.seriesActivityRank(request, getLoginUser()));
    }

    /**
     * 系列赛-阶段排行榜
     *
     * @param
     * @return
     */
    @PostMapping("/seriesStageActivityRank")
    public Result<List<SingleStageRankDto>> seriesStageActivityRank(@RequestBody SeriesActivityRankRequest request) {
        return CommonResult.success(appActivityManager.seriesStageActivityRank(request.getMainActivityId()));
    }


    /**
     * 查找聚合活动的详情
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/polymerization/findById")
    com.linzi.pitpat.lang.Result<PolymerizationActivityResponseDto> findPolymerizationActivityByActivityId(@RequestBody ActivityQueryDto queryDto) {
        ActivityPolymerizationRecord entity = activityPolymerizationRecordService.findByActivityId(queryDto.getActivityId());
        return CommonResult.success(activityConverter.toDto(entity));
    }

    /**
     * 赛事-奖励审核状态
     *
     * @param
     * @return
     * @tag 3.7.0
     */
    @PostMapping("/singleActivityDetail/reviewStatus")
    public Result<ActivityAwardReviewDto> reviewStatusGet(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.reviewStatusGet(request.getMainActivityId()));
    }

    //获取分享链接
    @PostMapping("/getShareUrl")
    public Result<String> getShareUrl(@RequestBody ActivityShareDto dto) {

        String url = appActivityManager.getShareUrl(dto.getActivityId(), dto.getJumpUrl(), getLanguageCode());

        return CommonResult.success(url);
    }

    private final MainActivityBizService mainActivityBizService;

    /**
     * 首页活动推荐。
     *
     * @return
     */
    @PostMapping("/homeRecommendActivity")
    @FillerMethod
    public Result<HomeRecommendActivityDto> homeRecommendActivity() {
        Optional<HomeRecommendActivityDto> homeRecommendActivityDto = mainActivityBizService.getHomeRecommendActivityDto(getLanguageCode());
        return CommonResult.success(homeRecommendActivityDto.orElse(null));
    }

    /**
     * common 获取通用分享链接.不包含业务
     *
     * @param dto
     * @return
     */
    @PostMapping("/getShareCommonUrl")
    public Result<CommonShareRespDto> getShareCommonUrl(@RequestBody CommonShareDto dto) {
        CommonShareRespDto commonUrl = appActivityManager.getShareCommonUrl(dto);
        return CommonResult.success(commonUrl);
    }

    /**
     * 获取 活动排行榜跳转路由
     *
     * @since 4.5.1
     */
    @PostMapping("/findRankAppRoute")
    public Result<AppRoute> findRankAppRoute(@RequestBody ActivityRankJumpRequestDto dto) {
        AppRoute resp = mallCategoryModelManager.findRankAppRoute(dto);
        return CommonResult.success(resp);
    }

    /**
     * 获取 型号配置跳转路由
     *
     * @since 4.5.1
     */
    @PostMapping("/findEquipmentAppRoute")
    public Result<AppRoute> findEquipmentAppRoute(@RequestBody ActivityEquipmentJumpRequestDto dto) {
        AppRoute resp = mallCategoryModelManager.findEquipmentAppRoute(dto);
        return CommonResult.success(resp);
    }


    /**
     * 获取活动观看信息
     *
     * @param request
     * @return
     */
    @PostMapping("/watchInfo")
    public Result<ActivityWatchInfoRespDto> watchInfo(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.getWatchInfoRespDto(request.getMainActivityId(), getCurrentUser()));
    }

    /**
     * 马拉松挑战-奖励弹窗
     *
     * @since 4.6.1
     */
    @PostMapping("/award/pop")
    public Result<ActivityAwardPopRespDto> awardPop(@RequestBody SingleActivityIdRequest request) {
        return CommonResult.success(appActivityManager.awardPopInfo(request.getMainActivityId(), getLoginUser()));
    }
}
