package com.linzi.pitpat.api.mallservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.mallservice.dto.response.CreatOceanpayOrderReqDto;
import com.linzi.pitpat.api.mallservice.mananger.OceanpayManager;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.model.query.PaypalPayQuery;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.mallservice.biz.MallOrderPaymentBizService;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.service.OrderRefundService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.third.oceanpay.OceanpaymentUtil;
import com.linzi.pitpat.data.third.oceanpay.constant.NoticePushStatusEnum;
import com.linzi.pitpat.data.third.oceanpay.constant.NoticeTypeEnum;
import com.linzi.pitpat.data.third.oceanpay.constant.OceanpayNoticeResultEnum;
import com.linzi.pitpat.data.third.oceanpay.dto.OceanPayConfigMap;
import com.linzi.pitpat.data.third.oceanpay.dto.request.AuthReqDto;
import com.linzi.pitpat.data.third.oceanpay.dto.response.AuthRespDto;
import com.linzi.pitpat.data.third.oceanpay.dto.response.TransactionNoticeRespDto;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 钱海支付 控制器
 */
@RestController
@Slf4j
@RequestMapping("/app/oceanpay")
@RequiredArgsConstructor
public class OceanpayController extends BaseAppController {

    public static final String OCEAN_CALL_BACK_STR = "receive-ok";

    private final OceanpayManager oceanpayManager;
    private final ZnsOrderService znsOrderService;
    private final OrderRefundService orderRefundService;
    private final PaypalPayService paypalPayService;
    private final MallOrderPaymentBizService mallOrderPaymentBizService;
    private final RedissonClient redissonClient;
    private final OceanPayConfigMap oceanPayConfigMap;

    /**
     * 创建钱海支付订单(钱海返回收银台地址后请求)
     */
    @PostMapping("/createPayOrder")
    public Result<Boolean> createPayOrder(@RequestBody CreatOceanpayOrderReqDto req) {
        ZnsUserEntity user = getLoginUser();
        ZnsOrderEntity orderEntity = znsOrderService.findByOrderNo(req.getOrderNo());
        if (Objects.isNull(orderEntity)) {
            //订单不存在
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "order"));
        }
        if (Objects.isNull(req.getMethods())) {
            req.setMethods(PayConstant.PayTypeEnum.OCEANPAY.getType());
        }
        String orderNo = orderEntity.getOrderNo();
        String key = OrderConstant.CREATE_PAY_ORDER_PREFIX + orderNo;
        RLock lock = redissonClient.getLock(key);
        lock.lock();
        if (!lock.isLocked()) {
            //不能重复支付
            throw new BaseException(I18nMsgUtils.getMessage("payment.repeated"));
        }
        try {
            //创建钱海支付订单
            oceanpayManager.createPayOrder(req, user.getId());
        } catch (Exception e) {
            log.error("[createPayOrder]---创建钱海支付订单,orderNo={},异常", orderNo, e);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"), CommonError.BUSINESS_ERROR.getCode());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success(true);
    }

    /**
     * 钱海支付回调接口
     */
    @RequestMapping("/notify")
    public String oceanPayNotify(@RequestBody String xml, @RequestParam Integer methods) {
        //解析对象
        log.info("[oceanPayNotify]---钱海支付回调接口 支付方式：{},xml={}", methods, xml);
        if (methods == null) {
            methods = PayConstant.PayTypeEnum.OCEANPAY.getType();
        }
        TransactionNoticeRespDto transactionNoticeRespDto = OceanpaymentUtil.parseResponse(xml, TransactionNoticeRespDto.class);
        if (Objects.isNull(transactionNoticeRespDto)) {
            log.info("[oceanPayNotify]---钱海支付回调接口 xml={},解析数据异常", xml);
            return "";
        }
        String orderNo = transactionNoticeRespDto.getOrder_number();
        ZnsOrderEntity orderEntity = znsOrderService.findByOrderNo(orderNo);
        if (Objects.isNull(orderEntity)) {
            log.info("[oceanPayNotify]---钱海支付回调接口 xml={},订单不存在", xml);
            return "";
        }
        Optional<TransactionNoticeRespDto> noticeRespDto = OceanpaymentUtil.parseNoticeXml(methods, xml, Objects.equals(orderEntity.getSourceType(), OrderConstant.OrderSourceEnum.H5_ORDER.type));
        if (noticeRespDto.isEmpty()) {
            log.info("[oceanPayNotify]---钱海支付回调接口 xml={},解析数据异常", xml);
            return "";
        }
        TransactionNoticeRespDto respDto = noticeRespDto.get();
        //处理钱海支付结果 TODO 校验支付金额跟订单金额 取消订单支付中的发起退款
        Boolean result = null;
        List<String> refundTypes = List.of(NoticeTypeEnum.REFUND.code, NoticeTypeEnum.PARTIALREFUND.code);
        if (NoticeTypeEnum.TRANSACTION.code.equals(respDto.getNotice_type())) {
            //支付回调
            PaypalPayQuery query = PaypalPayQuery.builder().payNo(orderEntity.getOrderNo()).type(PayConstant.BuyTypeEnum.MALL_ORDER.type).build();
            PaypalPay paypalPay = paypalPayService.findByQuery(query);
            if (Objects.isNull(paypalPay)) {
                log.error("[oceanPayNotify]---钱海支付回调接口,支付单不存在，无法处理.orderNo={}", orderEntity.getOrderNo());
                return OCEAN_CALL_BACK_STR;
            }
            if (!StringUtils.hasText(respDto.getOrder_amount()) || paypalPay.getAmount().compareTo(new BigDecimal(respDto.getOrder_amount())) != 0) {
                log.error("[oceanPayNotify]---钱海支付回调接口,支付金额={}，回调金额={}，不一致.orderNo={}", paypalPay.getAmount(), respDto.getOrder_amount(), orderEntity.getOrderNo());
                PaypalPay updatePaypalPay = new PaypalPay(paypalPay.getId());
                updatePaypalPay.setFailReason("支付金额跟回调金额不一致,traceId=" + MDC.get("traceId"));
                updatePaypalPay.setStatus(PayConstant.PayStatusEnum.PAY_STATUS_N1.type);
                paypalPayService.update(updatePaypalPay);
                ZnsOrderEntity updateOrderEntity = new ZnsOrderEntity(orderEntity.getId());
                updateOrderEntity.setCloseDec("支付金额跟回调金额不一致,traceId=" + MDC.get("traceId"));
                updateOrderEntity.setCloseType(OrderConstant.CloseTypeEnum.CLOSE_TYPE_5.code);
                updateOrderEntity.setStatus(OrderStatusEnum.CLOSE_ORDER.getStatus());
                znsOrderService.update(updateOrderEntity);
                return OCEAN_CALL_BACK_STR;
            }
            String payNo = paypalPay.getPayNo();
            OceanpayNoticeResultEnum noticeResultEnum = OceanpayNoticeResultEnum.findByCode(respDto.getPayment_status());
            log.info("[oceanPayNotify]---钱海支付回调接口 orderNo={},payNo={} ,支付结果={}，开始处理", orderNo, payNo, noticeResultEnum.message);
            PayConstant.OrderPayStatusEnum payStatusEnum = PayConstant.OrderPayStatusEnum.PENDING; //付款中
            if (noticeResultEnum == OceanpayNoticeResultEnum.RESULT_0) {
                payStatusEnum = PayConstant.OrderPayStatusEnum.FAILED; //支付失败
            } else if (noticeResultEnum == OceanpayNoticeResultEnum.RESULT_1) {
                payStatusEnum = PayConstant.OrderPayStatusEnum.COMPLETED; //支付成功
            } else if (noticeResultEnum == OceanpayNoticeResultEnum.RESULT_N1) {
                //klarna 待授权状态，
                String paymentDetails = respDto.getPayment_details();
                if (paymentDetails.startsWith("80000:")) {
                    //用户授权成功，进行允许预授权
                    AuthReqDto req = new AuthReqDto(oceanPayConfigMap.getOceanpayConfig(methods,Objects.equals(orderEntity.getSourceType(), OrderConstant.OrderSourceEnum.H5_ORDER.type)));
                    req.setPayment_id(respDto.getPayment_id());
                    req.setPayment_authType("1");
                    Optional<AuthRespDto> auth = OceanpaymentUtil.auth(req);
                    if (auth.isEmpty() || !auth.get().isSuccess()) {
                        //授权失败或者请求失败,需要重试。
                        return "";
                    } else {
                        //授权成功
                        return OCEAN_CALL_BACK_STR;
                    }
                }
            }
            result = mallOrderPaymentBizService.dealPaymentResult(orderEntity.getId(), paypalPay.getPayNo(), payStatusEnum, respDto.getPayment_details(), respDto.getPayment_id(), methods);
        } else if (refundTypes.contains(respDto.getNotice_type())) {
            //退款回调
            OrderRefund orderRefund = orderRefundService.findByRefundNo(respDto.getRefund_number());
            if (Objects.isNull(orderRefund)) {
                log.info("[oceanPayNotify]---钱海支付回调接口 xml={},退款单不存在", xml);
                return "";
            }
            PaypalPayQuery query = PaypalPayQuery.builder().refId(orderRefund.getId()).type(PayConstant.BuyTypeEnum.REFUND_ORDER.type).build();
            PaypalPay paypalPay = paypalPayService.findByQuery(query);
            if (Objects.isNull(paypalPay)) {
                log.error("[oceanPayNotify]---钱海支付回调接口,退款支付单不存在，无法处理.refundNo = {}", orderRefund.getRefundNo());
                return OCEAN_CALL_BACK_STR;
            }
            String payNo = paypalPay.getPayNo();
            NoticePushStatusEnum pushStatusEnum = NoticePushStatusEnum.findByCode(respDto.getPush_status());
            log.info("[oceanPayNotify]---钱海支付回调接口 orderNo={},payNo={} ,退款结果={}，开始处理", orderNo, payNo, pushStatusEnum.message);
            PayConstant.RefundPayStatusEnum refundStatusEnum = PayConstant.RefundPayStatusEnum.PENDING; //退款中
            if (pushStatusEnum == NoticePushStatusEnum.RESULT_0) {
                refundStatusEnum = PayConstant.RefundPayStatusEnum.FAILED; //退款失败
            } else if (pushStatusEnum == NoticePushStatusEnum.RESULT_1) {
                refundStatusEnum = PayConstant.RefundPayStatusEnum.COMPLETED; //退款成功
            }
            result = mallOrderPaymentBizService.dealRefundResult(payNo, refundStatusEnum, respDto.getPush_details());
        } else {
            log.info("[oceanPayNotify]---钱海支付回调接口 orderNo={}，交易类型不是transaction", orderNo);
        }

        //返回结果
        String resp = Optional.ofNullable(result).orElse(false) ? OCEAN_CALL_BACK_STR : "";
        log.info("[oceanPayNotify]---钱海支付回调接口,处理结束, orderNumber={}，resp={}", orderNo, resp);
        return resp;
    }

}
