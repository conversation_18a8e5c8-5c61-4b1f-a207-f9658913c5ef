package com.linzi.pitpat.api.activityservice.manager;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.NewPersonPkPageConfigStatusEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.NewPersonPkPageStyleTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.NewPersonPkPageConfig;
import com.linzi.pitpat.data.activityservice.model.entity.NewPersonPkPageConfigDetail;
import com.linzi.pitpat.data.activityservice.model.query.NewPersonPkPageConfigDetailQuery;
import com.linzi.pitpat.data.activityservice.model.query.NewPersonPkPageConfigQuery;
import com.linzi.pitpat.data.activityservice.service.NewPersonPkPageConfigDetailService;
import com.linzi.pitpat.data.activityservice.service.NewPersonPkPageConfigService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.request.MaidianLogRequest;
import com.linzi.pitpat.data.vo.AppNewPersonPkPageConfigVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.Random;

@Service
@RequiredArgsConstructor
public class AppNewPersonPkPageManager {

    private final NewPersonPkPageConfigService newPersonPkPageConfigService;
    private final NewPersonPkPageConfigDetailService newPersonPkPageConfigDetailService;


    public AppNewPersonPkPageConfigVo getNewPersonPkPage() {
        NewPersonPkPageConfigQuery configQuery = NewPersonPkPageConfigQuery.builder().configStatus(NewPersonPkPageConfigStatusEnum.IN_USE.getCode()).build();
        NewPersonPkPageConfig config = newPersonPkPageConfigService.findByQuery(configQuery);
        if (Objects.isNull(config)) {
            return null;
        }
        NewPersonPkPageConfigDetailQuery detailQuery = new NewPersonPkPageConfigDetailQuery();
        detailQuery.setConfigId(config.getId());
        detailQuery.setLangCode(LocaleContextHolder.getLocale().toString());
        int num = new Random().nextInt(100) + 1;
        if (num <= config.getOneRatio()) {
            detailQuery.setStyleType(NewPersonPkPageStyleTypeEnum.ONE.getCode());
        } else {
            detailQuery.setStyleType(NewPersonPkPageStyleTypeEnum.TWO.getCode());
        }
        AppNewPersonPkPageConfigVo vo = new AppNewPersonPkPageConfigVo();
        vo.setConfigId(config.getId());
        NewPersonPkPageConfigDetail configDetail = newPersonPkPageConfigDetailService.findByQuery(detailQuery);
        if (Objects.isNull(configDetail)) {
            // 使用默认语言
            detailQuery.setLangCode(config.getDefaultLangCode());
            configDetail = newPersonPkPageConfigDetailService.findByQuery(detailQuery);
            if (Objects.isNull(configDetail)) {
                return null;
            }
        }
        BeanUtils.copyProperties(configDetail, vo);
        return vo;
    }


    public void addRecord(MaidianLogRequest request) {
        if (!StringUtils.hasText(request.getKeyValue())) {
            return;
        }
        AppNewPersonPkPageConfigVo vo = JsonUtil.readValue(request.getKeyValue(), AppNewPersonPkPageConfigVo.class);
        NewPersonPkPageConfigQuery configQuery = NewPersonPkPageConfigQuery.builder().configId(vo.getConfigId()).build();
        NewPersonPkPageConfig config = newPersonPkPageConfigService.findByQuery(configQuery);
        if (Objects.isNull(config)) {
            return;
        }
        if (NewPersonPkPageStyleTypeEnum.ONE.getCode().equals(vo.getStyleType())) {
            if (Constants.EX_EVENTDETAILPAGE_NEWPEOPLEPK.equals(request.getButton())) {
                config.setOneExposure(Objects.isNull(config.getOneExposure()) ? 1 : config.getOneExposure() + 1);
            } else if (Constants.BTN_EVENTDETAILPAGE_NEWPEOPLEPK_LATER.equals(request.getButton())) {
                config.setOneLeftClick(Objects.isNull(config.getOneLeftClick()) ? 1 : config.getOneLeftClick() + 1);
            } else if (Constants.BTN_EVENTDETAILPAGE_NEWPEOPLEPK_CONNECT.equals(request.getButton())) {
                config.setOneRightClick(Objects.isNull(config.getOneRightClick()) ? 1 : config.getOneRightClick() + 1);
            }
        } else if (NewPersonPkPageStyleTypeEnum.TWO.getCode().equals(vo.getStyleType())) {
            if (Constants.EX_EVENTDETAILPAGE_NEWPEOPLEPK.equals(request.getButton())) {
                config.setTwoExposure(Objects.isNull(config.getTwoExposure()) ? 1 : config.getTwoExposure() + 1);
            } else if (Constants.BTN_EVENTDETAILPAGE_NEWPEOPLEPK_LATER.equals(request.getButton())) {
                config.setTwoLeftClick(Objects.isNull(config.getTwoLeftClick()) ? 1 : config.getTwoLeftClick() + 1);
            } else if (Constants.BTN_EVENTDETAILPAGE_NEWPEOPLEPK_CONNECT.equals(request.getButton())) {
                config.setTwoRightClick(Objects.isNull(config.getTwoRightClick()) ? 1 : config.getTwoRightClick() + 1);
            }
        }
        config.setGmtModified(ZonedDateTime.now());
        newPersonPkPageConfigService.update(config);
    }
}
