package com.linzi.pitpat.api.userservice.manager;


import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.userservice.dto.request.UserVoteReq;
import com.linzi.pitpat.data.userservice.model.entity.UserVote;
import com.linzi.pitpat.data.userservice.model.query.UserVoteQuery;
import com.linzi.pitpat.data.userservice.service.UserVoteService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserVoteBusiness {


    @Resource
    private UserVoteService userVoteService;

    @Resource
    private MainActivityService mainActivityService;

    @Resource
    private RedissonClient redissonClient;

    public Boolean userActivityVote(Long userId, UserVoteReq req) {
        Boolean flag = false;
        Long activityId = req.getActivityId();
        Long teamId = req.getTeamId();
        String key = String.format(RedisKeyConstant.USER_ACTIVITY_VOTE, userId, activityId);
        MainActivity mainActivity = mainActivityService.findById(req.getActivityId());
        if (Objects.isNull(mainActivity)) {
            return false;
        }
        ZonedDateTime voteEndTime = DateTimeUtil.parse(mainActivity.getActivityStartTime());
        ZonedDateTime voteStartTime = DateUtil.addDays1(voteEndTime, req.getVoteDayOffset());
        ZonedDateTime now = ZonedDateTime.now();
        if (now.isBefore(voteStartTime) || now.isAfter(voteEndTime)) {
            log.info("用户活动投票不在投票时间内,userId:{},activityId:{}", userId, activityId);
            return false;
        }

        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.tryLock(1L, 3L, TimeUnit.SECONDS)) {
                UserVoteQuery query = UserVoteQuery.builder().userId(userId).activityId(activityId).build();
                UserVote oldUserVote = userVoteService.getByQuery(query);
                if (Objects.isNull(oldUserVote)) {
                    UserVote userVote = new UserVote();
                    userVote.setUserId(userId);
                    userVote.setActivityId(activityId);
                    userVote.setTeamId(teamId);
                    userVoteService.insert(userVote);
                    flag = true;
                }
            }
        } catch (Exception e) {
            log.error("用户活动投票异常,userId:{},activityId:{},teamId:{},e:", userId, activityId, teamId, e);
            return false;
        }
        return flag;
    }


}
