package com.linzi.pitpat.api.mallservice.controller.h5;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.api.mananger.MallOrderManager;
import com.linzi.pitpat.core.constants.enums.DingTalkTokenEnum;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.mallservice.dto.request.GenerateOrderRequest;
import com.linzi.pitpat.data.mallservice.dto.request.OrderGoodsReq;
import com.linzi.pitpat.data.mallservice.dto.request.OrderInfoReq;
import com.linzi.pitpat.data.mallservice.dto.request.OrderListRequest;
import com.linzi.pitpat.data.mallservice.dto.response.ZnsOrderRespDto;
import com.linzi.pitpat.data.mallservice.model.entity.OrderLogisticsList;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderDetailVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderListVo;
import com.linzi.pitpat.data.mallservice.service.LogisticsQueryLogService;
import com.linzi.pitpat.data.mallservice.service.OrderLogisticsListService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddressEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipShop;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddressService;
import com.linzi.pitpat.data.userservice.service.vip.VipShopService;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.linzi.pitpat.data.constants.RedisConstants.GENERATE_ORDER_KEY;

/**
 * 订单管理相关接口
 *
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/h5/order")
@Slf4j
@RequiredArgsConstructor
public class OrderController extends BaseH5Controller {
    private final ZnsOrderService orderService;
    private final RedissonClient redissonClient;
    private final ZnsUserAddressService userAddressService;
    private final ZnsOrderService znsOrderService;
    private final LogisticsQueryLogService logisticsQueryLogService;
    private final OrderLogisticsListService logisticsListService;
    private final VipShopService vipShopService;
    private final MallOrderManager mallOrderManager;
    @Value("${spring.profiles.active}")
    private String profile;

    /**
     * 订单列表
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public Result<Page<OrderListVo>> list(@RequestBody OrderListRequest request) {
        ZnsUserEntity user = getLoginUser();
        String languageCode = getLanguageCode();
        return CommonResult.success(orderService.orderList(request, user.getId(), languageCode));
    }

    /**
     * 订单详情
     *
     * @param req
     * @return
     */
    @PostMapping("/orderInfo")
    public Result<OrderDetailVo> orderInfo(@RequestBody OrderInfoReq req) {
        String languageCode = getLanguageCode();
        return CommonResult.success(orderService.orderInfo(req.getOrderId(), languageCode));
    }

    /**
     * 下单
     *
     * @param request
     * @return
     */
    @PostMapping("/generateOrder")
    public Result<ZnsOrderRespDto> generateOrder(@RequestBody GenerateOrderRequest request) {
        if (Objects.isNull(request.getGoodsType())) {
            request.setGoodsType(1); // 兼容传统下单
        }
        // 季卡 不需要校验地址
        if (request.getGoodsType() != 2 && Objects.isNull(request.getAddressId())) {
            log.error("实物商品和混合商品需要填写地址");
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("order.generate.address"));
        }
        if ((request.getGoodsType() == 2 || request.getGoodsType() == 3) && Objects.isNull(request.getVirtualGoodsId())) {
            // 虚拟商品传对应id
            log.error("虚拟商品需要传对应id");
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("order.generate.virtual.goods.id"));
        }
        if (request.getGoodsType() == 1 && (Objects.isNull(request.getGoodsId()) || Objects.isNull(request.getSkuId()))) {
            // 实物商品id
            log.error("实物商品需要传商品和skuid");
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("order.generate.virtual.goods.sku.id"));
        }
        if (Objects.isNull(request.getCount())) {
            log.error("需要传商品数量");
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("order.generate.count"));
        }
        ZnsUserAddressEntity addressEntity = null;
        if (Objects.isNull(request.getAddressId())) {
            addressEntity = new ZnsUserAddressEntity();
            addressEntity.setCity("");
            addressEntity.setCountry("");
            addressEntity.setProvince("");
            addressEntity.setDetailAddress("");
            addressEntity.setConsigneeMobile("");
            addressEntity.setConsignee("");
        } else {
            addressEntity = userAddressService.findById(request.getAddressId());
        }

        String languageCode = getLanguageCode();
        ZnsUserEntity user = getLoginUser();
        String key = GENERATE_ORDER_KEY + user.getId();
        RLock lock = redissonClient.getLock(key);
        try {
            if (LockHolder.tryLock(lock, 1, 5)) {
                List<OrderGoodsReq> list = new ArrayList<>();
                if (request.getGoodsType() == 2 || request.getGoodsType() == 3) {
                    // 虚拟商品
                    OrderGoodsReq req = new OrderGoodsReq();
                    req.setGoodsType(2); // 虚拟
                    req.setGoodsId(request.getVirtualGoodsId());
                    req.setSkuId(-1L);
                    req.setCount(1);
                    list.add(req);

                    if (request.getGoodsType() == 3) {
                        Long virtualGoodsId = request.getVirtualGoodsId();
                        // 会员商品
                        VipShop vipShop = vipShopService.selectVipShopById(virtualGoodsId);
                        if (Objects.nonNull(vipShop)) {
                            // 判断是否绑定实物商品，获取对应的实物商品
                            Long goodsId = vipShop.getGoodsId();
                            Long skuId = vipShop.getSkuId();
                            request.setGoodsId(goodsId);
                            request.setSkuId(skuId);
                        }
                    }

                }
                // 实物商品
                if (Objects.nonNull(request.getGoodsId())) {
                    OrderGoodsReq req = new OrderGoodsReq();
                    req.setGoodsId(request.getGoodsId());
                    req.setSkuId(request.getSkuId());
                    req.setGoodsType(1); // 实物
                    req.setCount(1);
                    list.add(req);
                }
                if (CollectionUtils.isEmpty(list)) {
                    return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("order.generate.product.pay"));
                }
                Result<ZnsOrderRespDto> result = orderService.generateOrder(list, user.getId(), addressEntity, request.getInviteUserCode(), getUserCurrency(), languageCode);
                ZnsOrderRespDto znsOrderEntity = result.getData();
                if (Objects.nonNull(znsOrderEntity)) {
                    DingTalkTokenEnum tokenEnum = DingTalkTokenEnum.PITPAT_ORDER_NOTIFICATION;
                    DingTalkUtils.sendMsgOnline(DingTalkRequestDto.of(tokenEnum.getToken(), tokenEnum.getSecret(), "【订单通知】：" + "\n用户名：" + user.getFirstName() + "\n邮箱：" + user.getEmailAddressEn() +
                            "\n购买的商品名称：" + znsOrderEntity.getGoodName() + "\n商品数量：" + request.getCount() + "\n订单金额：" + "$" + znsOrderEntity.getOrderAmount()
                            + "\n订单编号：" + znsOrderEntity.getOrderNo(), "17335897917,18625205568,19157971615"), profile);
                }
                return result;
            }
        } catch (Exception e) {
            log.error("创建订单失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success();
    }


    /**
     * 取消订单
     *
     * @param req
     * @return
     */
    @PostMapping("/cancel")
    public Result cancel(@RequestBody OrderInfoReq req) {
        if (Objects.isNull(req.getOrderId())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        RLock lock = redissonClient.getLock(RedisConstants.ORDER_KEY + req.getOrderId());
        return LockHolder.tryLock(lock, 1, 5, () -> {
            //查询原订单
            ZnsOrderEntity orderEntity = orderService.findById(req.getOrderId());
            if (!Objects.equals(orderEntity.getStatus(), OrderStatusEnum.NEW.getStatus())) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
            }
            orderService.updateStatus(req.getOrderId(), OrderStatusEnum.CLOSE_ORDER, 1);
            return CommonResult.success();
        });
    }

    /**
     * 确认收货
     *
     * @param req
     * @return
     */
    @PostMapping("/confirmReceive")
    public Result confirmReceive(@RequestBody OrderInfoReq req) {
        if (Objects.isNull(req.getOrderId())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        String key = RedisConstants.ORDER_KEY + req.getOrderId();
        RLock lock = redissonClient.getLock(key);
        return LockHolder.tryLock(lock, 1, 5, () -> orderService.confirmReceive(req.getOrderId()));
    }

    /**
     * 删除订单
     *
     * @param req
     * @return
     */
    @PostMapping("/delete")
    public Result delete(@RequestBody OrderInfoReq req) {
        if (Objects.isNull(req.getOrderId())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        RLock lock = redissonClient.getLock(RedisConstants.ORDER_KEY + req.getOrderId());
        LockHolder.tryLock(lock, 1, 5, () -> {
            orderService.deleteOrder(req.getOrderId());
        });
        return CommonResult.success();
    }


    /**
     * 物流信息
     *
     * @param po
     * @return
     */
    @PostMapping("/query/logistics")
    public Result queryLogistic(@RequestBody ZnsOrderEntity po) {
        ZnsOrderEntity znsOrderEntity = znsOrderService.selectOrderLogisticNo(po.getLogisticsNo());
        logisticsQueryLogService.fillOrderLogistics(po.getLogisticsNo());
        List<OrderLogisticsList> list = logisticsListService.selectLogisticsQueryLogByOrderNo(znsOrderEntity.getOrderNo());
        Map<String, Object> data = new HashMap<>();
        data.put("logisticsList", list);
        data.put("order", znsOrderEntity);
        return CommonResult.success(data);
    }
}
