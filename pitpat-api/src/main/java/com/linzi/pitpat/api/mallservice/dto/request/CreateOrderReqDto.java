package com.linzi.pitpat.api.mallservice.dto.request;

import com.linzi.pitpat.data.mallservice.dto.request.OrderAmountReqDto;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 创建订单返回参数
 */
@Data
public class CreateOrderReqDto extends OrderAmountReqDto {

    /**
     * 重复生成的订单id
     */
    private Long orderId;


    /**
     * 是否购物车下单，false：不是，ture： 是
     */
    @NotNull
    private Boolean isShoppingCart;

    /**
     * 前端订单总金额（校验后端计算跟前端计算是否一致）
     */
    @NotNull
    private String orderTotalAmount;

    /**
     * 运费ID
     */
    @NotNull
    private Long freightConfigId;

    /**
     * @see com.linzi.pitpat.data.awardservice.constant.enums.PayConstant.PayTypeEnum
     * 支付方式：1:paypal支付；2：钱海支付-信用卡
     * OCEANPAY(2, "钱海支付-信用卡","Credit Card"),
     * OCEANPAY_GOOGLE(3, "钱海支付-google","GooglePay"),
     * OCEANPAY_APPLE(4, "钱海支付-apple","ApplePay"),
     * OCEANPAY_AFTERPAY(5, "钱海支付-AfterPay","Afterpay"),
     * OCEANPAY_KLARNA(6, "钱海支付-Klarna","Klarna"),
     */
    @NotNull
    private Integer payType;

    /**
     * 津贴金额
     * @since 4.7.0
     */
    private String allowanceAmount;

    /**
     * 订单来源类型，默认0，0：电商购买，1：分享，2：app内购买, 3:新商城,4：积分商城,5:H5购买
     * @since 4.7.2
     * @see OrderConstant.OrderSourceEnum
     */
    private Integer sourceType;

    /**
     * @since 4.8.0
     * 是否包含组合包
     */
    private Boolean containCombinationPackage;
}
