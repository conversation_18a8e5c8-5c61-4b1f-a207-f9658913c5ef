package com.linzi.pitpat.api.activityservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.dto.FreeRoomNumberDto;
import com.linzi.pitpat.data.activityservice.dto.api.FreeActivityConfigDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeRoomDto;
import com.linzi.pitpat.data.activityservice.dto.api.response.FreeRoomRespDto;
import com.linzi.pitpat.data.activityservice.manager.AppFreeActivityBiz;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app/freeActivity")
@Slf4j
@RequiredArgsConstructor
public class AppFreeActivityController extends BaseAppController {

    private final AppFreeActivityBiz appFreeActivityBiz;
    /**
     * 配置信息
     *
     * @param
     * @return
     */
    @PostMapping("/freeActivityConfig")
    public Result<FreeActivityConfigDto> freeActivityConfig() {

        return CommonResult.success(appFreeActivityBiz.getFreeActivityConfig());
    }

    /**
     * 房间列表
     * 排序第一优先级：等待中 > 进行中
     * 第二优先级：创建时间（创建时间早的优先展示）
     * 不显示已结束的活动
     * 展示前3个加入房间的用户头像
     *
     * @param query 分页查询参数
     * @return 房间分页数据
     */
    @PostMapping("/roomPage")
    public Result<Page<FreeRoomDto>> roomPage(@RequestBody PageQuery query) {
        Page<FreeRoomDto> result = appFreeActivityBiz.roomPage(query);
        return CommonResult.success(result);
    }

    /**
     * 创建房间
     *
     * @param
     * @return
     */
    @PostMapping("/createRoom")
    public Result<Long> createRoom() {
        Integer appVersion = getAppVersion();
        return CommonResult.success(appFreeActivityBiz.createRoom(getLoginUser(),appVersion));
    }

    /**
     * 加入
     *
     * @param dto 房间号DTO
     * @return 房间号
     */
    @PostMapping("/joinRoom")
    public Result<Long> joinRoom(@RequestBody FreeRoomNumberDto dto) {
        Long roomNumber = appFreeActivityBiz.joinRoom(dto.getRoomNumber(),getLoginUser());
        return CommonResult.success(roomNumber);
    }
    /**
     * 房间详情
     * @return FreeRoomRespDto
     */
    @PostMapping("/roomDetails")
    public Result<FreeRoomRespDto> roomDetails(@RequestBody @Validated FreeRoomNumberDto dto) {
        return CommonResult.success(appFreeActivityBiz.roomDetail(dto.getRoomNumber()));
    }
}
