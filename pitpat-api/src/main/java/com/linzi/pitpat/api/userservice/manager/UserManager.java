package com.linzi.pitpat.api.userservice.manager;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.dto.response.achievement.AchievementRespDto;
import com.linzi.pitpat.api.dto.response.achievement.BaseAchievementDto;
import com.linzi.pitpat.api.dto.response.achievement.BestPaceAchievementDto;
import com.linzi.pitpat.api.dto.response.achievement.BestPowerAchievementDto;
import com.linzi.pitpat.api.dto.response.achievement.PlatformActivityAchievementDto;
import com.linzi.pitpat.api.dto.response.achievement.RankedActivityAchievementDto;
import com.linzi.pitpat.api.dto.response.achievement.UserActivityAchievementDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.activityservice.biz.ActivityVerificationBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.constant.enums.RankedLevelEnums;
import com.linzi.pitpat.data.activityservice.model.entity.RankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.vo.UnfinishedActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.UserRankedLevelStatisticsVo;
import com.linzi.pitpat.data.activityservice.service.RankedLevelService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.SeriesActivityRelService;
import com.linzi.pitpat.data.activityservice.service.TrainingUserInfoService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.constant.enums.AccountConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.MedalConstant;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.MedalConfigQuery;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.enums.RunActivitySubTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.filler.base.FillContext;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.service.exchangeRate.ExchangeRateConfigService;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.dto.request.UserCountryRegionRequest;
import com.linzi.pitpat.data.userservice.dto.response.CheckChangeCountryRegionResp;
import com.linzi.pitpat.data.userservice.dto.response.SurrenderChannelActivityRespDto;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserExtraParamsKeyEnum;
import com.linzi.pitpat.data.userservice.model.entity.TrafficInvestmentPackageActivityDo;
import com.linzi.pitpat.data.userservice.model.entity.UserChangeCountryRecordDo;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraParamsDo;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelDo;
import com.linzi.pitpat.data.userservice.model.entity.UserPlacementLevelLogDo;
import com.linzi.pitpat.data.userservice.model.entity.UserDetailDo;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.model.entity.UserTitleDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserLoginLogEntity;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelLogQuery;
import com.linzi.pitpat.data.userservice.model.query.UserPlacementLevelQuery;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.TrafficInvestmentPackageActivityService;
import com.linzi.pitpat.data.userservice.service.UserChangeCountryRecordService;
import com.linzi.pitpat.data.userservice.service.UserExtraParamsService;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelLogService;
import com.linzi.pitpat.data.userservice.service.UserPlacementLevelService;
import com.linzi.pitpat.data.userservice.service.UserDetailService;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.UserTitleService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.user.GetUserInfoVo;
import com.linzi.pitpat.data.vo.user.UserRecommendListVo;
import com.linzi.pitpat.data.vo.useractive.PowerAchievementVo;
import com.linzi.pitpat.data.vo.useractive.UserRunDataAchievementVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/28 15:18
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserManager {
    private final ZnsUserService userService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService userAccountDetailService;
    private final ZnsUserLoginLogService userLoginLogService;
    private final AreaService areaService;
    private final ZnsRunActivityUserService runActivityUserService;
    private final ExchangeRateConfigService exchangeRateConfigService;
    private final UserCouponService userCouponService;
    private final TrainingUserInfoService trainingUserInfoService;
    private final SeriesActivityRelService seriesActivityRelService;
    private final ZnsUserRunDataDetailsService znsUserRunDataDetailsService;
    private final UserMedalService userMedalService;
    private final MedalConfigService medalConfigService;
    private final UserRankedLevelService userRankedLevelService;
    private final RankedLevelService rankedLevelService;
    private final RunRankedActivityUserService runRankedActivityUserservice;
    private final ZnsRunActivityUserService znsRunActivityUserService;
    private final ZnsUserService znsUserService;
    private final ActivityUserScoreService activityUserScoreService;
    private final UserChangeCountryRecordService userChangeCountryRecordService;
    private final UserTaskDetailService userTaskDetailService;
    private final UserTitleService userTitleService;
    private final UserTaskBizService userTaskBizService;
    private final UserExtraParamsService userExtraParamsService;
    private final ActivityVerificationBizService activityVerificationBizService;
    private final UserPlacementLevelService userPlacementLevelService;
    private final UserPlacementLevelLogService userPlacementLevelLogService;

    private final TrafficInvestmentPackageActivityService trafficInvestmentPackageActivityService;
    private final UserExtraService userExtraService;
    private final UserDetailService userDetailService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource(name = "asyncExecutor")
    private ThreadPoolTaskExecutor executor;


    // 创建一个固定大小的线程池(IO密集型任务)
    private ExecutorService fixedExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    public GetUserInfoVo getUserInfo(Long userId, Integer appVersion) {
        ZnsUserEntity user = userService.findById(userId);
        if (Objects.isNull(user)) {
            throw new RuntimeException(I18nMsgUtils.getMessage("user.account.correct.msg"));
        }

        GetUserInfoVo getUserInfoVo = new GetUserInfoVo();
        getUserInfoVo.setNotifyState(user.getNotifyState());
        getUserInfoVo.setFirstName(user.getFirstName());
        getUserInfoVo.setLastName(user.getLastName());
        getUserInfoVo.setHeadPortrait(user.getHeadPortrait());
        getUserInfoVo.setGender(user.getGender());
        getUserInfoVo.setOnlineStatus(user.getOnlineStatus());
        getUserInfoVo.setCountryCode(user.getCountryCode());
        if (appVersion < 3080) {
            getUserInfoVo.setRemainingAddrModifications(user.getRemainingAddrModifications());
        } else {
            //用户是否可以修改国家,true：可以，false：不行
            Boolean b = userChangeCountryRecordService.checkUserCanChangeCountry(user.getId());
            getUserInfoVo.setRemainingAddrModifications(b ? 1 : 0);
        }

        //获取上次登录时间
        ZnsUserLoginLogEntity lastLogin = userLoginLogService.getLastLogin(user.getId());
        if (Objects.nonNull(lastLogin)) {
            getUserInfoVo.setLastLoginTime(lastLogin.getCreateTime());
        }
        //币种，现在都是美元
        getUserInfoVo.setCurrency(I18nConstant.buildCurrency(I18nConstant.CurrencyCodeEnum.USD));

        //查询用户称号
        UserTitleDo userTitleDo = userTitleService.findByUserId(userId);
        if (Objects.nonNull(userTitleDo)) {
            getUserInfoVo.setUserTitle(userTitleDo.getUserTitle());
            getUserInfoVo.setTitleImgId(userTitleDo.getTitleImgId());
        }
        return getUserInfoVo;
    }

    public CheckChangeCountryRegionResp preCheckChangeCountryRegion(ZnsUserEntity user, UserCountryRegionRequest request, Integer appVersion) {
        return preCheckChangeCountryRegion(user, request, false, appVersion);
    }

    /**
     * 检查国家区域是否一致
     *
     * @param stateCode
     * @param stateCode1
     * @return
     */
    private boolean checkCountryRegionConsistency(String stateCode, String stateCode1) {
        if (!StringUtils.hasText(stateCode)) {
            return false;
        }
        if (Objects.equals(stateCode, stateCode1)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 检查币种是否一致
     *
     * @param stateCode
     * @param stateCode1
     * @return
     */
    private boolean isSameCurrency(String stateCode, String stateCode1) {
        AreaEntity areaEntity1 = areaService.selectAreaByCode(stateCode);
        AreaEntity areaEntity2 = areaService.selectAreaByCode(stateCode1);
        if (areaEntity1.getCurrencyCode().equals(areaEntity2.getCurrencyCode())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 修改国家、地区
     *
     * @param loginUser
     * @param request
     * @param appVersion
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeCountryRegion(ZnsUserEntity loginUser, UserCountryRegionRequest request, Integer appVersion) {
        CheckChangeCountryRegionResp regionResp = preCheckChangeCountryRegion(loginUser, request, true, appVersion);
        Long userId = loginUser.getId();
        if (regionResp.isRegionTheSame()) {
            log.info("changeCountryRegion，userId:{} 区域相同，无需修改", userId);
            return;
        }
        if (!regionResp.isCurrencyTheSame()) {
            //余额检查
            if (regionResp.getAccountBalance().compareTo(BigDecimal.ZERO) > 0) {
                throw new BaseException(I18nMsgUtils.getMessage("user.account.balance.have.msg"));
            }

            //提现中余额检查
            if (regionResp.getWithdrawBalance().compareTo(BigDecimal.ZERO) > 0) {
                throw new BaseException(I18nMsgUtils.getMessage("user.account.withdraw.balance.have.msg"));
            }
        }
        AreaEntity areaEntity = areaService.selectAreaByCode(request.getStateCode());

        //保存用户修改记录(缓存记录30天)
        UserChangeCountryRecordDo recordDo = new UserChangeCountryRecordDo(userId, loginUser.getCountryCode(), areaEntity.getCountryCode(), loginUser.getStateCode(), areaEntity.getAreaCode());
        userChangeCountryRecordService.create(recordDo);


        //重新设置请求国家区域数据，只存英语
        request.setState(areaEntity.getAreaName());
        request.setCountry(areaEntity.getCountryName());

        //修改用户区域
        userService.updateUserRegion(loginUser, request);
        log.info("changeCountryRegion 修改用户区域成功");

        ZnsUserEntity userEntity = userService.findById(userId);
        if (StringUtils.hasText(userEntity.getCountry()) && UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code.equals(userEntity.getEmailType())) {
            // 补充个人邮箱和国家信息
            userTaskDetailService.completeLevelTask(userId, UserExpObtainSubTypeEnum.COMPLETE_PROFILE.getCode(), false);
            userTaskBizService.completeEvent(new EventTriggerDto().setUser(userEntity).setEventSubType(TaskConstant.TakEventSubTypeEnum.USER_IMPROVE_INFO.getCode()));
        }

        if (appVersion >= 3080) {
            log.info("changeCountryRegion userId:{} 3.8及以上不涉及币种变动", userId);
            //3.8及以上不涉及币种变动
            return;
        }

        ZnsUserAccountEntity account = userAccountService.getByUserId(userId);
        //修改账户
        if (!regionResp.isCurrencyTheSame()) {
            // 账户明细绑定accountId
            userAccountDetailService.bindUserAccountId(account.getId(), userId);
            //删除原账户
            userAccountService.deleteById(account.getId());
            //新增账户
            ZnsUserEntity user = znsUserService.findById(userId);
            ZnsUserAccountEntity userAccount = userAccountService.addUserAccountByOld(user, account, appVersion);
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    //处理原账户新累计跑待领取明细
                    handleOriginalAccountNewAccumulatedDetails(userId, account, userAccount);
                    //券币种金额修改
                    modifyCouponCurrencyAmount(userId, account, userAccount);
                    //数据pk金额数据清空
                    trainingUserInfoService.cleanUpTrainingUserInfoAmount(userId);
                    //积分兑换金额处理
                    modifyScoreCurrencyAmount(userId, account.getCurrencyCode(), userAccount.getCurrencyCode());
                }
            });
        }
        //清除缓存
        activityVerificationBizService.clearAreaAndGroupCheckCache(userId);
    }

    private void modifyScoreCurrencyAmount(Long userId, String oldCurrencyCode, String newCurrencyCode) {
        log.info("modifyCurrencyAmount start");
        BigDecimal exchangeRate = exchangeRateConfigService.getExchangeRate(oldCurrencyCode, newCurrencyCode);
        activityUserScoreService.changeScoreExchangeAmount(userId, exchangeRate);
    }

    /**
     * 修改用户优惠券金额的货币单位
     *
     * @param userId     用户ID
     * @param oldAccount 原账户信息，包含旧的货币单位
     * @param newAccount 新账户信息，包含新的货币单位
     */
    private void modifyCouponCurrencyAmount(Long userId, ZnsUserAccountEntity oldAccount, ZnsUserAccountEntity newAccount) {
        log.info("modifyCouponCurrencyAmount start");
        //币种汇率转换
        BigDecimal exchangeRate = exchangeRateConfigService.getExchangeRate(oldAccount.getCurrencyCode(), newAccount.getCurrencyCode());
        Long count = userCouponService.getAllCouponCount(userId);
        if (count <= 0) {
            return;
        }
        //分页处理
        int pageSize = 500;
        int total = (int) (count / pageSize);
        for (int i = 0; i <= total; i++) {
            Page<UserCoupon> page = userCouponService.selectUserCouponPage(new Page<>(i + 1, pageSize), userId);
            List<UserCoupon> userCoupons = page.getRecords();
            if (CollectionUtils.isEmpty(userCoupons)) {
                continue;
            }
            List<UserCoupon> couponList = new ArrayList<>();
            for (UserCoupon u : userCoupons) {
                if (u.getCurrencyCode().equals(newAccount.getCurrencyCode())) {
                    continue;
                }
                UserCoupon userCoupon = new UserCoupon();
                userCoupon.setId(u.getId());
                userCoupon.setCurrencyCode(newAccount.getCurrencyCode());
                userCoupon.setCurrencySymbol(newAccount.getCurrencySymbol());
                userCoupon.setCurrencyName(newAccount.getCurrencyName());
                userCoupon.setAmount(u.getAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_DOWN));
                couponList.add(userCoupon);
            }
            if (!CollectionUtils.isEmpty(couponList)) {
                userCouponService.updateBatchByIds(couponList);
            }
        }
    }


    private void handleOriginalAccountNewAccumulatedDetails(Long userId, ZnsUserAccountEntity account, ZnsUserAccountEntity userAccount) {
        log.info("handleOriginalAccountNewAccumulatedDetails start");
        // 查询待领取奖励明细
        UserAccountDetailByQuery userAccountDetailByQuery = new UserAccountDetailByQuery().setUserId(userId).setTradeStatus(AccountConstant.TradeStatusEnum.TRADE_STATUS_3.getCode())
                .setActivityType(RunActivityTypeEnum.BATTLE_PASS_CUMULATIVE_RUN.getType()).setUserAccountId(account.getId());
        List<ZnsUserAccountDetailEntity> userAccountDetails = userAccountDetailService.getUserAccountDetailByQuery(userAccountDetailByQuery);
        if (CollectionUtils.isEmpty(userAccountDetails)) {
            return;
        }
        //币种汇率转换
        BigDecimal exchangeRate = exchangeRateConfigService.getExchangeRate(account.getCurrencyCode(), userAccount.getCurrencyCode());
        for (ZnsUserAccountDetailEntity userAccountDetail : userAccountDetails) {
            ZnsUserAccountDetailEntity update = new ZnsUserAccountDetailEntity();
            update.setId(userAccountDetail.getId());
            update.setAmount(userAccountDetail.getAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_DOWN));
            userAccountDetailService.updateById(update);
        }
    }

    private CheckChangeCountryRegionResp preCheckChangeCountryRegion(ZnsUserEntity user, UserCountryRegionRequest request, boolean isCacheActivity, Integer appVersion) {
        CheckChangeCountryRegionResp regionResp = new CheckChangeCountryRegionResp();

        //校验用户是否可以修改国家，true：可以，false：不行
        boolean canChange = (appVersion >= 3080) ? userChangeCountryRecordService.checkUserCanChangeCountry(user.getId()) : user.getRemainingAddrModifications() > 0;
        if (!canChange) {
            throw new BaseException(I18nMsgUtils.getLangMessage(user.getLanguageCode(), "common.params.systemError"));
        }

        //检查国家区域是否一致
        boolean regionTheSame = checkCountryRegionConsistency(user.getStateCode(), request.getStateCode());
        regionResp.setRegionTheSame(regionTheSame);
        if (regionTheSame) {
            return regionResp;
        }
        if (appVersion >= 3080) {
            //3.8以上版本不用校验币种相关
            return regionResp;
        }

        //检查币种是否一致
        boolean isSameCurrency = isSameCurrency(user.getStateCode(), request.getStateCode());
        regionResp.setCurrencyTheSame(isSameCurrency);
        if (isSameCurrency) {
            return regionResp;
        }

        // 检查是否有未结束活动
        List<UnfinishedActivityVo> unfinishedActivityList = runActivityUserService.getUnfinishedActivities(user.getId());
        if (!CollectionUtils.isEmpty(unfinishedActivityList)) {
            regionResp.setHasUnFinishedActivity(1);
            // 活动id 缓存
            if (isCacheActivity) {
                setUnfinishedActivityCache(user.getId(), unfinishedActivityList);
            }
        }

        // 余额
        ZnsUserAccountEntity account = userAccountService.getByUserId(user.getId());
        BigDecimal withdrawBalance = userAccountDetailService.getAllAmountByStatus(user.getId(), Arrays.asList(0, 1), 2, null, account.getId());
        regionResp.setWithdrawBalance(withdrawBalance);
        regionResp.setAccountBalance(account.getAmount().compareTo(withdrawBalance) > 0 ? account.getAmount().subtract(withdrawBalance) : BigDecimal.ZERO);

        return regionResp;
    }

    private void setUnfinishedActivityCache(Long userId, List<UnfinishedActivityVo> unfinishedActivityList) {
        if (CollectionUtils.isEmpty(unfinishedActivityList)) {
            return;
        }
        UnfinishedActivityVo unfinishedActivityVo = unfinishedActivityList.stream().sorted(Comparator.comparing(UnfinishedActivityVo::getEndTime).reversed()).findFirst().orElse(null);
        if (Objects.isNull(unfinishedActivityVo)) {
            return;
        }

        String key = RedisConstants.USER_NO_FINISH_ACTIVITY + userId;
        //可能有随时区变化的活动，统一加25小时
        ZonedDateTime endTime = DateUtil.addHours(unfinishedActivityVo.getEndTime(), 25);
        int expireTime = DateUtil.betweenSecond(ZonedDateTime.now(), endTime);
        if (expireTime <= 0) {
            return;
        }
        List<String> activityList = unfinishedActivityList.stream().map(a -> String.valueOf(a.getActivityId())).collect(Collectors.toList());
        unfinishedActivityList.stream().filter(u -> MainActivityTypeEnum.SERIES_MAIN.getType().equals(u.getMainType())).forEach(ma -> {
            List<Long> subActivityId = seriesActivityRelService.findSubActivityId(ma.getActivityId());
            if (!CollectionUtils.isEmpty(subActivityId)) {
                activityList.addAll(subActivityId.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        });
        redisTemplate.opsForList().rightPushAll(key, activityList);
        redisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
    }

    /**
     * 用户成就统计
     *
     * @param user
     * @return
     */
    public AchievementRespDto achievementStatistic(ZnsUserEntity user) {
        Long userId = user.getId();
        log.info("UserManager#achievementStatistic------用户成就统计开始,userId=" + userId);
        //设备度量类型 0：公制，1：英制
        Integer measureUnit = user.getMeasureUnit();
        Integer divisor = Objects.equals(measureUnit, 0) ? 1000 : 1600; //米转公英制除数
        //个人跑步记录最小id
        Long minDetailId = znsUserRunDataDetailsService.getUserMinActivityRunDetailId(userId);
        AchievementRespDto result;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (minDetailId != null) {
            //查询个人成就数据
            result = findAchievementRespDto(user, minDetailId, divisor);
        } else {
            //没有跑步记录，使用默认数据
            result = defaultAchievementRespDto(user, minDetailId, divisor);
        }
        //计算完赛总次数
        PlatformActivityAchievementDto platformAchievement = result.getPlatformAchievement();
        UserActivityAchievementDto userAchievement = result.getUserAchievement();
        RankedActivityAchievementDto rankedAchievement = result.getRankedAchievement();
        long totalNum = platformAchievement.totalNum() + userAchievement.totalNum() + rankedAchievement.getActivityCont();
        result.setMetricType(measureUnit);
        result.setFinishActivityNum(totalNum);
        stopWatch.stop();
        log.info("UserManager#achievementStatistic------用户成就统计结束,userId={},总耗时={}s", userId, stopWatch.getTotalTimeSeconds());
        return result;
    }

    /**
     * 默认成就数据
     *
     * @param user
     * @param minDetailId 最小运动记录id
     * @param divisor     米转公英制除数
     */
    private AchievementRespDto defaultAchievementRespDto(ZnsUserEntity user, Long minDetailId, Integer divisor) {
        log.info("UserManager#achievementStatistic------defaultAchievementRespDto使用默认数据,userId={}", user.getId());
        AchievementRespDto result = new AchievementRespDto();
        //基础成就数据
        result.setBaseAchievement(getBaseAchievement(user, minDetailId, divisor));
        //平台赛事成就数据
        result.setPlatformAchievement(new PlatformActivityAchievementDto(0, 0, 0));
        //用户赛事成就数据
        result.setUserAchievement(new UserActivityAchievementDto(0, 0, 0));
        //段位赛成就数据
        result.setRankedAchievement(new RankedActivityAchievementDto(0L, BigDecimal.ZERO, BigDecimal.ZERO, 1L, RankedLevelEnums.AMATEUR_LEVEL_1.getName(), RankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank(), user.getAppVersion()));
        //最佳配速成就数据
        result.setBestPaceAchievement(new BestPaceAchievementDto(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));
        //最佳耐力成就信息
        result.setBestPowerAchievement(new BestPowerAchievementDto(BigDecimal.ZERO, BigDecimal.ZERO, 0));
        return result;
    }

    /**
     * 查询个人成就数据
     *
     * @param user
     * @param minDetailId
     * @return
     */
    private AchievementRespDto findAchievementRespDto(ZnsUserEntity user, Long minDetailId, Integer divisor) {
        final Long userId = user.getId();
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        //基础成就数据
        CompletableFuture<BaseAchievementDto> baseFuture = CompletableFuture.supplyAsync(() -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            BaseAchievementDto baseAchievementDto = getBaseAchievement(user, minDetailId, divisor);
            stopWatch.stop();
            log.info("UserManager#achievementStatistic------findAchievementRespDto基础成就数据,userId={},耗时={}s", userId, stopWatch.getTotalTimeSeconds());
            return baseAchievementDto;
        }, fixedExecutor);

        //平台赛事成就数据
        CompletableFuture<PlatformActivityAchievementDto> platformFuture = CompletableFuture.supplyAsync(() -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            PlatformActivityAchievementDto platformActivityAchievementDto = getPlatformAchievement(userId);
            stopWatch.stop();
            log.info("UserManager#achievementStatistic------findAchievementRespDto平台赛事成就数据,userId={},耗时={}s", userId, stopWatch.getTotalTimeSeconds());
            return platformActivityAchievementDto;
        }, fixedExecutor);

        //用户赛事成就数据
        CompletableFuture<UserActivityAchievementDto> userFuture = CompletableFuture.supplyAsync(() -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            UserActivityAchievementDto userActivityAchievementDto = getUserAchievement(userId);
            stopWatch.stop();
            log.info("UserManager#achievementStatistic------findAchievementRespDto用户赛事成就数据,userId={},耗时={}s", userId, stopWatch.getTotalTimeSeconds());
            return userActivityAchievementDto;
        }, fixedExecutor);

        //段位赛成就数据
        CompletableFuture<RankedActivityAchievementDto> rankedFuture = CompletableFuture.supplyAsync(() -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            RankedActivityAchievementDto rankedActivityAchievementDto = getRankedAchievement(user);
            stopWatch.stop();
            log.info("UserManager#achievementStatistic------findAchievementRespDto段位赛成就数据,userId={},耗时={}s", userId, stopWatch.getTotalTimeSeconds());
            return rankedActivityAchievementDto;
        }, fixedExecutor);

        //最佳配速成就数据
        CompletableFuture<BestPaceAchievementDto> paceFuture = CompletableFuture.supplyAsync(() -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            int deviceType = EquipmentDeviceTypeEnum.TREADMILL.getCode();//只查跑步
            BestPaceAchievementDto bestPaceAchievementDto = getPaceAchievement(userId, minDetailId, divisor, deviceType);
            stopWatch.stop();
            log.info("UserManager#achievementStatistic------findAchievementRespDto最佳配速成就数据,userId={},耗时={}s", userId, stopWatch.getTotalTimeSeconds());
            return bestPaceAchievementDto;
        }, fixedExecutor);

        //最佳耐力成就信息
        CompletableFuture<BestPowerAchievementDto> powerFuture = CompletableFuture.supplyAsync(() -> {
            Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            int deviceType = EquipmentDeviceTypeEnum.TREADMILL.getCode();//只查跑步
            BestPowerAchievementDto bestPowerAchievementDto = getPowerAchievement(userId, minDetailId, deviceType);
            stopWatch.stop();
            log.info("UserManager#achievementStatistic------findAchievementRespDto最佳耐力成就信息,userId={},耗时={}s", userId, stopWatch.getTotalTimeSeconds());
            return bestPowerAchievementDto;
        }, fixedExecutor);

        // 组合所有CompletableFuture，等待它们都完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(baseFuture, platformFuture, userFuture, rankedFuture, paceFuture, powerFuture);

        //组装返回值
        AchievementRespDto result = new AchievementRespDto();
        try {
            // 等待所有任务完成，获取它们的返回值
            allFutures.get();
            BaseAchievementDto baseAchievement = baseFuture.get();
            PlatformActivityAchievementDto platformAchievement = platformFuture.get();
            UserActivityAchievementDto userAchievement = userFuture.get();
            RankedActivityAchievementDto rankedAchievement = rankedFuture.get();
            BestPaceAchievementDto bestPaceAchievement = paceFuture.get();
            BestPowerAchievementDto bestPowerAchievement = powerFuture.get();
            result.setBaseAchievement(baseAchievement);
            result.setPlatformAchievement(platformAchievement);
            result.setUserAchievement(userAchievement);
            result.setRankedAchievement(rankedAchievement);
            result.setBestPaceAchievement(bestPaceAchievement);
            result.setBestPowerAchievement(bestPowerAchievement);
        } catch (InterruptedException | ExecutionException e) {
            log.error("UserManager#achievementStatistic------findAchievementRespDto查询个人成就数据异常,userId=" + userId, e);
            return defaultAchievementRespDto(user, minDetailId, divisor);
        }
        return result;
    }

    /**
     * 最佳耐力成就信息
     *
     * @param userId
     * @param minDetailId 最小跑步记录id
     * @param deviceType
     * @return
     */
    private BestPowerAchievementDto getPowerAchievement(Long userId, Long minDetailId, Integer deviceType) {
        //单次跑步最长距离、最长时间
        PowerAchievementVo powerAchievementVo = znsUserRunDataDetailsService.getUserMaxDistance(userId, minDetailId, deviceType);
        BestPowerAchievementDto bestPowerAchievementDto = new BestPowerAchievementDto();
        bestPowerAchievementDto.setMaxDistance(powerAchievementVo.getMaxDistance());
        bestPowerAchievementDto.setMaxTime(powerAchievementVo.getMaxTime());

        //周跑量达到10mile/16km的次数
        Integer tenMileNum = znsUserRunDataDetailsService.getTenMileNum(userId, minDetailId, deviceType);
        bestPowerAchievementDto.setTenMileNum(tenMileNum);
        return bestPowerAchievementDto;
    }

    /**
     * 最佳配速成就数据
     *
     * @param userId
     * @param minDetailId 最小跑步记录id
     * @param divisor     米转公英制除数
     * @param deviceType
     */
    private BestPaceAchievementDto getPaceAchievement(Long userId, Long minDetailId, Integer divisor, Integer deviceType) {
        // 公英里转换倍数
        BigDecimal rate = BigDecimal.valueOf(divisor).divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP);
        // 1mile/1km最佳配速
        BigDecimal oneBestPace = znsUserRunDataDetailsService.getUserBestPaceByRunMileage(userId, minDetailId, divisor, deviceType);
        // 2mile/2km最佳配速
        BigDecimal twoBestPace = znsUserRunDataDetailsService.getUserBestPaceByRunMileage(userId, minDetailId, divisor * 2, deviceType);
        // 3mile/3km最佳配速
        BigDecimal threeBestPace = znsUserRunDataDetailsService.getUserBestPaceByRunMileage(userId, minDetailId, divisor * 3, deviceType);
        oneBestPace = oneBestPace.multiply(rate).setScale(0, RoundingMode.DOWN);
        twoBestPace = twoBestPace.multiply(rate).setScale(0, RoundingMode.DOWN);
        threeBestPace = threeBestPace.multiply(rate).setScale(0, RoundingMode.DOWN);
        return new BestPaceAchievementDto(oneBestPace, twoBestPace, threeBestPace);
    }

    /**
     * 段位赛成就数据
     *
     * @param user
     * @return
     */
    private RankedActivityAchievementDto getRankedAchievement(ZnsUserEntity user) {
        //查询段位信息
        RankedActivityAchievementDto rankedAchievement;
        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(user.getId());
        RankedLevelEnums rankedLevelEnum;
        if (userRankedLevel == null) {
            //未开启段位，查询最小段位
            RankedLevel rankedLevel = rankedLevelService.findByLevelAndRank(RankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank());
            rankedAchievement = new RankedActivityAchievementDto(rankedLevel.getId(), rankedLevel.getName(), rankedLevel.getLevel(), rankedLevel.getRank());
            rankedLevelEnum = RankedLevelEnums.resolve(rankedLevel.getLevel(), rankedLevel.getRank());
        } else {
            rankedLevelEnum = RankedLevelEnums.resolve(userRankedLevel.getLevel(), userRankedLevel.getRank());
            rankedAchievement = new RankedActivityAchievementDto(userRankedLevel.getRankedLevelId(), userRankedLevel.getName(), userRankedLevel.getLevel(), userRankedLevel.getRank());
        }

        //段位名称i18n
        rankedLevelEnum = Optional.ofNullable(rankedLevelEnum).orElse(RankedLevelEnums.AMATEUR_LEVEL_1);
        rankedAchievement.setName(I18nMsgUtils.getMessage("rankedActivity.rank.RankedLevelEnums." + rankedLevelEnum, user.getLanguageCode()));

        //查询赛事数据
        UserRankedLevelStatisticsVo statisticsVo = runRankedActivityUserservice.statisticsUserRankedData(user);
        rankedAchievement.setAverageRank(statisticsVo.getAverageRank());
        rankedAchievement.setActivityCont(statisticsVo.getActivityCont());
        rankedAchievement.setCompleteRate(statisticsVo.getCompleteRate());
        return rankedAchievement;
    }

    /**
     * 获取用户赛事成就数据
     *
     * @param userId
     */
    private UserActivityAchievementDto getUserAchievement(Long userId) {
        //好友PK赛 完赛次数
        Integer friendActivityNum = runActivityUserService.getUserPkActivityNum(userId, RunActivityTypeEnum.CHALLENGE_RUN.getType(), RunActivitySubTypeEnum.FRIENDS_MATCHING.getType());
        //随机PK 完赛数量
        Integer randomActivityNum = runActivityUserService.getUserPkActivityNum(userId, RunActivityTypeEnum.CHALLENGE_RUN.getType(), RunActivitySubTypeEnum.RANDOM_MATCHING.getType());
        //非官方同跑完赛数量
        Integer multiActivityNum = runActivityUserService.getUserActivityNum(userId, RunActivityTypeEnum.TEAM_RUN.getType());

        return new UserActivityAchievementDto(friendActivityNum, randomActivityNum, multiActivityNum);
    }

    /**
     * 获取平台赛事成就数据
     *
     * @param userId
     * @return
     */
    private PlatformActivityAchievementDto getPlatformAchievement(Long userId) {
        //官方赛完赛次数
        Integer officialActivityNum = runActivityUserService.getUserOfficialActivityNum(userId);
        //主题赛完赛数量
        Integer themeActivityNum = runActivityUserService.getUserThemeActivityNum(userId);
        //团队赛完赛次数
        Integer teamActivityNum = runActivityUserService.getUserTeamActivityNum(userId);

        return new PlatformActivityAchievementDto(officialActivityNum, themeActivityNum, teamActivityNum);
    }

    /**
     * 获取基础成就数据
     *
     * @param user
     */
    private BaseAchievementDto getBaseAchievement(ZnsUserEntity user, Long minDetailId, Integer divisor) {
        BaseAchievementDto result = new BaseAchievementDto();

        //用户
        int days = DateUtil.daysBetween(user.getCreateTime(), ZonedDateTime.now());//注册天数
        result.setRegisterDays(days + 1);
        result.setFirstName(user.getFirstName());
        result.setLastName(user.getLastName());
        result.setHeadPortrait(user.getHeadPortrait());

        //运动
        Integer exercisesDays = 0; //运动天数
        Integer exercisesMinutes = 0; //运动时长(min)
        Integer exercisesMileage = 0;//运动里程(mi/km)
        if (minDetailId != null) {
            //查询用户跑步成就数据汇总
            UserRunDataAchievementVo userRunDataAchievementVo = znsUserRunDataDetailsService.selectUserRunDataVo(user.getId(), divisor, user.getCreateTime());
            exercisesDays = userRunDataAchievementVo.getExercisesDays();
            exercisesMinutes = userRunDataAchievementVo.getExercisesMinutes();
            exercisesMileage = userRunDataAchievementVo.getExercisesMileage();
        }
        result.setExercisesDays(exercisesDays);
        result.setExercisesMinutes(exercisesMinutes);
        result.setExercisesMileage(exercisesMileage);

        //勋章
        int obtain; //0 未获得，1 已经获得
        Integer obtainMedalNum = 0;
        List<String> obtainImgUrls = userMedalService.findUserObtainMedal(user.getId(), null);
        if (CollectionUtils.isEmpty(obtainImgUrls)) {
            //没有已获得的图片，取四张实力勋章
            obtain = 0;
            MedalConfigQuery query = MedalConfigQuery.builder().type(MedalConstant.MedalTypeEnum.type_1.getType()).build();
            List<MedalConfig> medalConfigList = medalConfigService.findByQuery(query);
            if (!CollectionUtils.isEmpty(medalConfigList)) {
                obtainImgUrls = medalConfigList.subList(0, 4).stream().map(MedalConfig::getUrl).collect(Collectors.toList());
            }
        } else {
            obtain = 1;
            obtainMedalNum = obtainImgUrls.size();
            //展示4张已获得的勋章
            int endIndex = Math.min(obtainImgUrls.size(), 4);
            obtainImgUrls = obtainImgUrls.subList(0, endIndex);
        }
        if (!CollectionUtils.isEmpty(obtainImgUrls)) {
            List<BaseAchievementDto.UserMedalDto> medalUrls = obtainImgUrls.stream().map(url -> new BaseAchievementDto.UserMedalDto(url, obtain)).toList();
            result.setMedalUrls(medalUrls);
        }
        result.setObtainMedalNum(obtainMedalNum);
        return result;
    }

    @Autowired
    @Qualifier("completeFutureExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * @return
     * @since 3.6.0
     * 添加好友的流程中，推荐好友
     * 推荐总数量10人（需要剔除机器人和内部账号）
     * <p>
     * 优先推荐“最近同跑”
     * 不足则用“附近”补充
     * 附近的人也不足用“最近活跃”补充
     * 整体排序依据：运动总里程(降序) > ID（降序）
     */
    @FillerMethod
    public List<UserRecommendListVo> getRecommendUserList(Long userId) throws ExecutionException, InterruptedException {
        //获取10个最近同跑
        int maxRecommendUserCount = 10;
        CompletableFuture<List<Long>> recentStrangeUserIdFuture = CompletableFuture.supplyAsync(() -> {
            return znsRunActivityUserService.queryPlayedTogetherRecentlyStranger(userId, maxRecommendUserCount);
        }, threadPoolTaskExecutor);
        CompletableFuture<List<Long>> nearStateUserIdFuture = CompletableFuture.supplyAsync(() -> {
            return znsUserService.queryNearStateRealUserStranger(userId, maxRecommendUserCount, new ArrayList<>());
        }, threadPoolTaskExecutor);
        CompletableFuture<List<Long>> activeUserIdFuture = CompletableFuture.supplyAsync(() -> {
            return znsUserService.getActiveUserWithRealStrangeUser(userId, maxRecommendUserCount, new ArrayList<>());
        }, threadPoolTaskExecutor);

        List<Long> resultUserIds = recentStrangeUserIdFuture.get();
        //获取最近同跑
        if (resultUserIds.size() < maxRecommendUserCount) {
            List<Long> longs = nearStateUserIdFuture.get();
            longs.removeAll(resultUserIds);
            resultUserIds.addAll(longs);
        }
        //获取附近的人
        if (resultUserIds.size() < maxRecommendUserCount) {
            List<Long> longs = activeUserIdFuture.get();
            longs.removeAll(resultUserIds);
            resultUserIds.addAll(longs);
        }

        FillContext.setUserId(userId);
        resultUserIds = resultUserIds.subList(0, 10);
        List<UserRecommendListVo> userInfo = znsUserService.findByIds(resultUserIds).stream().map(i -> {
            UserRecommendListVo vo = new UserRecommendListVo();
            vo.setFriendId(i.getId());
            vo.setNickname(i.getFirstName());
            vo.setHeadPortrait(i.getHeadPortrait());
            return vo;
        }).toList();
        List<UserRecommendListVo> userRecommendListVos = new ArrayList<>();
        for (Long l : resultUserIds) {
            for (UserRecommendListVo item : userInfo) {
                if (item.getFriendId().equals(l)) {
                    userRecommendListVos.add(item);
                }
            }
        }
        return userRecommendListVos;
    }

    /**
     * 修改用户性别
     * @since 4.8.0
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGender(Long userId, Integer gender) {
        UserExtraParamsDo userExtraParams =  userExtraParamsService.findUserExtraParams(userId, UserExtraParamsKeyEnum.UPDATE_GENDER_RECORD);
        if (userExtraParams != null){
            log.info("[updateGender]---用户已有修改记录不能重复修改，userId={}",userId);
            return false;
        }
        // 查询用户历史性别
        ZnsUserEntity user = znsUserService.findById(userId);
        //修改用户性别
        ZnsUserEntity updateUser = new ZnsUserEntity();
        updateUser.setId(userId);
        updateUser.setGender(gender);
        znsUserService.update(updateUser);
        //保存修改记录
        userExtraParamsService.create(new UserExtraParamsDo(userId, UserExtraParamsKeyEnum.UPDATE_GENDER_RECORD.code, "1"));
        if (user.getGender() != 0 && !user.getGender().equals(gender)) {
            removeUserPlacementScore(userId, gender, user);
        }
        return true;
    }

    /**
     * 移除用户该性别下所有定级赛成绩
     * @since  4.8.0
     */
    private void removeUserPlacementScore(Long userId, Integer gender, ZnsUserEntity user) {
        UserPlacementLevelDo userPlacementLevelDo = userPlacementLevelService.findByQuery(new UserPlacementLevelQuery().setUserId(userId).setScenario(user.getGender()));
        if(Objects.nonNull(userPlacementLevelDo)){
            userPlacementLevelService.deleteById(userPlacementLevelDo.getId());
        }
        List<UserPlacementLevelLogDo> list = userPlacementLevelLogService.findList(new UserPlacementLevelLogQuery().setUserId(userId).setScenario(gender));
        if (!CollectionUtils.isEmpty(list)) {
            userPlacementLevelLogService.deleteBatch(list);
        }
    }

    /**
     * 获取用户投流渠道活动
     */
    public SurrenderChannelActivityRespDto channelActivity(ZnsUserEntity userEntity){
        UserExtraDo userExtraDo = userExtraService.findByUserId(userEntity.getId());
        SurrenderChannelActivityRespDto result = new SurrenderChannelActivityRespDto();
        result.setStatus(2);
        if (Objects.isNull(userExtraDo)) {
            return result;
        }
        if(!StringUtils.hasText(userExtraDo.getMaterialToken()) || !StringUtils.hasText(userExtraDo.getChannelCode())){
            return result;
        }
        //查询投流渠道活动
        UserDetailDo userDetailDo = userDetailService.findByUserId(userEntity.getId());
        if (StringUtils.hasText(userExtraDo.getH5Url())) {
            //判断是否参与过活动
            if (Objects.nonNull(userDetailDo) && Objects.equals(userDetailDo.getTrafficInvestmentActivityFlag(),1)){
                result.setStatus(1);
            }else {
                result.setStatus(0);
                //插入参与记录
                if (Objects.isNull(userDetailDo)) {
                    userDetailDo = new UserDetailDo();
                    userDetailDo.setUserId(userEntity.getId());
                    userDetailDo.setTrafficInvestmentActivityFlag(1);
                    userDetailService.insert(userDetailDo);
                } else {
                    userDetailDo.setTrafficInvestmentActivityFlag(1);
                    userDetailService.update(userDetailDo);
                }
            }
            result.setH5Url(userExtraDo.getH5Url());
        }
        return result;
    }
}
