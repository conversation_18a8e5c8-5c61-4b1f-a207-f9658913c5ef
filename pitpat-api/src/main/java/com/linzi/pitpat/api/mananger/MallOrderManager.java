package com.linzi.pitpat.api.mananger;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.linzi.pitpat.api.mallservice.dto.request.AddShoppingCartReq;
import com.linzi.pitpat.api.mallservice.dto.request.CreateOrderReqDto;
import com.linzi.pitpat.api.mallservice.dto.request.CreateOrderZeroReqDto;
import com.linzi.pitpat.api.mallservice.dto.request.OrderSettleInfoReqDto;
import com.linzi.pitpat.api.mallservice.dto.response.BatchAddShoppingCartResp;
import com.linzi.pitpat.data.mallservice.dto.api.request.OrderReqDto;
import com.linzi.pitpat.api.mallservice.dto.response.CreatOceanpayOrderReqDto;
import com.linzi.pitpat.api.mallservice.dto.response.CreateOrderRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallOrderSkuRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.OrderSettleInfoRespDto;
import com.linzi.pitpat.api.mallservice.mananger.OceanpayManager;
import com.linzi.pitpat.api.mallservice.model.vo.OrderShippingVo;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityCouponConfig;
import com.linzi.pitpat.data.activityservice.model.entity.RewardAccountDo;
import com.linzi.pitpat.data.activityservice.model.query.ActivityCouponConfigQuery;
import com.linzi.pitpat.data.activityservice.model.query.RewardAccountQuery;
import com.linzi.pitpat.data.activityservice.model.query.UserCouponQuery;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.activityservice.service.RewardAccountService;
import com.linzi.pitpat.data.awardservice.biz.MallCouponComponent;
import com.linzi.pitpat.data.awardservice.biz.MallUserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.awardservice.constant.enums.UserCouponConstant;
import com.linzi.pitpat.data.awardservice.model.entity.TurbolinkDrawDo;
import com.linzi.pitpat.data.awardservice.model.entry.ExchangeScoreRule;
import com.linzi.pitpat.data.awardservice.model.entry.PaypalPay;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.model.query.PaypalPayQuery;
import com.linzi.pitpat.data.awardservice.model.query.TurbolinkDrawQuery;
import com.linzi.pitpat.data.awardservice.service.ExchangeScoreRuleService;
import com.linzi.pitpat.data.awardservice.service.PaypalPayService;
import com.linzi.pitpat.data.awardservice.service.TurbolinkDrawService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.constants.RabbitQueueConstants;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.biz.MallOrderPaymentBizService;
import com.linzi.pitpat.data.mallservice.biz.MallTaxRateBizService;
import com.linzi.pitpat.data.mallservice.biz.OrderRefundBizService;
import com.linzi.pitpat.data.mallservice.biz.ShoppingCartBizService;
import com.linzi.pitpat.data.mallservice.biz.SkuPreDeductBizService;
import com.linzi.pitpat.data.mallservice.converter.api.OrderApiConverter;
import com.linzi.pitpat.data.mallservice.converter.api.UserAddressConverter;
import com.linzi.pitpat.data.mallservice.dto.api.response.GoodsDiscountAmountDetailVo;
import com.linzi.pitpat.data.mallservice.dto.api.response.OrderPickCouponDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderAmountReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderCheckOutReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderListRequest;
import com.linzi.pitpat.data.mallservice.dto.response.MallOrderPickCouponResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.OrderAmountRespDto;
import com.linzi.pitpat.data.mallservice.dto.response.OrderSkuAmountRespDto;
import com.linzi.pitpat.data.mallservice.dto.response.PayMethodDto;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.enums.OrderRefundConstant;
import com.linzi.pitpat.data.mallservice.manager.PaymentConfigManager;
import com.linzi.pitpat.data.mallservice.model.entity.MallFreightConfig;
import com.linzi.pitpat.data.mallservice.model.entity.MallFreightConfigI8n;
import com.linzi.pitpat.data.mallservice.model.entity.MallTaxRateDo;
import com.linzi.pitpat.data.mallservice.model.entity.OrderCancelReasonConfigDo;
import com.linzi.pitpat.data.mallservice.model.entity.OrderCancelReasonConfigI18nDo;
import com.linzi.pitpat.data.mallservice.model.entity.OrderLogistics;
import com.linzi.pitpat.data.mallservice.model.entity.OrderRefund;
import com.linzi.pitpat.data.mallservice.model.entity.ScoreMallGoodsRelationDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderItemEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderCancelReasonConfigI18nQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderCancelReasonConfigQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderItemQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderLogisticsQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderPageQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderRefundQuery;
import com.linzi.pitpat.data.mallservice.model.query.ScoreMallGoodsRelationQuery;
import com.linzi.pitpat.data.mallservice.model.vo.CurrencyInatallmentVo;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsSkuLanguageVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderDetailVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderItemListVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderListVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.model.vo.PayOrderDetailVo;
import com.linzi.pitpat.data.mallservice.service.GoodsSkuI18nService;
import com.linzi.pitpat.data.mallservice.service.MallFreightConfigI8nService;
import com.linzi.pitpat.data.mallservice.service.MallFreightConfigService;
import com.linzi.pitpat.data.mallservice.service.OrderCancelReasonConfigI18nService;
import com.linzi.pitpat.data.mallservice.service.OrderCancelReasonConfigService;
import com.linzi.pitpat.data.mallservice.service.OrderLogisticsService;
import com.linzi.pitpat.data.mallservice.service.OrderRefundService;
import com.linzi.pitpat.data.mallservice.service.ScoreMallGoodsRelationService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderItemService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.mallservice.util.SnowflakeOrderNoGenerator;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.third.erp.ErpApiUtil;
import com.linzi.pitpat.data.third.erp.req.ErpOrderSourceCodeDto;
import com.linzi.pitpat.data.third.erp.resp.ErpResponsDto;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddressEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddressService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商城订单 Manager
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MallOrderManager {

    private final ShoppingCartBizService shoppingCartBizService;
    private final ZnsOrderService orderService;
    private final ZnsOrderItemService orderItemService;
    private final OrderApiConverter orderApiConverter;
    private final GoodsSkuI18nService goodsSkuI18nService;
    private final MallTaxRateBizService sysTaxRateBizService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final OrderRefundService orderRefundService;
    private final RedissonClient redissonClient;
    private final SkuPreDeductBizService skuPreDeductBizService;
    private final MallOrderBizService mallOrderBizService;
    private final OrderRefundBizService orderRefundBizService;
    private final MallOrderPaymentBizService mallOrderPaymentBizService;
    private final ISysConfigService iSysConfigService;
    private final MallFreightConfigI8nService mallFreightConfigI8nService;
    private final MallFreightConfigService mallFreightConfigService;
    private final ZnsUserAddressService znsUserAddressService;
    private final OrderLogisticsService orderLogisticsService;
    private final RabbitTemplate rabbitTemplate;
    private final MallUserCouponBizService mallUserCouponBizService;
    private final MallCouponComponent mallCouponComponent;
    private final UserCouponService userCouponService;
    private final OrderCancelReasonConfigI18nService orderCancelReasonConfigI18nService;
    private final OrderCancelReasonConfigService orderCancelReasonConfigService;
    private final ActivityCouponConfigService couponConfigService;
    private final PaymentConfigManager paymentConfigManager;
    private final ExchangeScoreRuleService exchangeScoreRuleService;
    private final ScoreMallGoodsRelationService scoreMallGoodsRelationService;
    private final RewardAccountService rewardAccountService;
    private final PaypalPayService paypalPayService;

    private final OceanpayManager oceanpayManager;
    private final UserExtraBizService userExtraBizService;
    private final UserAddressConverter userAddressConverter;
    private final TurbolinkDrawService turbolinkDrawService;

    /**
     * 订单支付超时延迟队列
     */
    @Value("${" + RabbitQueueConstants.MALL_ORDER_CLOSE_DELAY_EXCHANGE + "}")
    private String mall_order_close_delay_exchange;
    /**
     * 订单push延迟队列
     */
    @Value("${" + RabbitQueueConstants.MALL_ORDER_PUSH_DELAY_EXCHANGE + "}")
    private String mall_order_push_delay_exchange;

    /**
     * 订单支付超时延迟队列
     */
    @Value("${" + RabbitQueueConstants.MALL_ORDER_CLOSE_DELAY_KEY + "}")
    private String mall_order_close_delay_key;

    /**
     * 订单支付提醒push延迟队列
     */
    @Value("${" + RabbitQueueConstants.MALL_ORDER_PUSH_DELAY_KEY + "}")
    private String mall_order_push_delay_key;

    /**
     * 订单列表
     */
    public Page<OrderListVo> orderList(OrderListRequest request, Long userId, String languageCode, Integer appVersion) {
        OrderPageQuery query = orderApiConverter.toPageQuery(request);
        if (OrderStatusEnum.FINSHED.getStatus().equals(request.getStatus())) {
            query.setStatus(null);
            query.setStatusList(List.of(OrderStatusEnum.FINSHED.getStatus(), OrderStatusEnum.DELIVERED.getStatus()));
        }
        query.setUserId(userId).setIsUserDelete(0).setOrders(List.of(OrderItem.desc("id")));
        Page page = orderService.findPage(query);

        List<ZnsOrderEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        List<Long> orderIds = records.stream().map(ZnsOrderEntity::getId).collect(Collectors.toList());
        List<ZnsOrderItemEntity> orderItems = orderItemService.findList(new OrderItemQuery().setOrderIds(orderIds).setLanguageCode(languageCode));
        Map<Long, List<ZnsOrderItemEntity>> orderItemEntityMap = orderItems.stream().collect(Collectors.groupingBy(ZnsOrderItemEntity::getOrderId));
        List<Long> skuIds = orderItems.stream().map(ZnsOrderItemEntity::getSkuId).collect(Collectors.toList());
        List<GoodsSkuLanguageVo> skuLanguageVos = goodsSkuI18nService.findLanguageList(skuIds, languageCode);
        Map<Long, GoodsSkuLanguageVo> goodsSkuLanguageVoMap = skuLanguageVos.stream().collect(Collectors.toMap(GoodsSkuLanguageVo::getSkuId, Function.identity(), (v1, v2) -> v2));


        List<OrderListVo> collect = records.stream().map(r -> {
            OrderListVo orderListVo = orderApiConverter.toDto(r);
            orderListVo.setScoreMallOrder(Objects.equals(r.getSourceType(), OrderConstant.OrderSourceEnum.SCORE_MALL.type));
            List<ZnsOrderItemEntity> znsOrderItemEntities = orderItemEntityMap.get(r.getId());
            if (CollectionUtils.isEmpty(znsOrderItemEntities)) {
                return null;
            }
            AtomicReference<Integer> commentStatus = new AtomicReference<>(0);
            List<OrderItemListVo> itemEntityList = znsOrderItemEntities.stream().map(t -> {
                OrderItemListVo itemListVo = getItemListVo(goodsSkuLanguageVoMap.get(t.getSkuId()), t, r.getCurrencyCode());
                //1. 所有商品都已经申请后 2. 所有商品均已经申请了评价
                log.info("[goodsComment] itemListVo={},orderItem info,orderCount={},orderRefundCount={}, commentId{}", itemListVo.getOrderItemId(), itemListVo.getCount(), itemListVo.getRefundCount(), itemListVo.getCommentId());
                //没有申请售后
                if (itemListVo.getRefundCount() < itemListVo.getCount()) {
                    //没有评价
                    if (Objects.isNull(itemListVo.getCommentId()) || itemListVo.getCommentId() <= 0) {
                        commentStatus.set(1);
                    }
                }
                return itemListVo;
            }).collect(Collectors.toList());
            //已完成的订单才能评价
            log.info("[goodsComment] orderId={},orderStatus={}, commentStatus={}", orderListVo.getOrderId(), orderListVo.getStatus(), commentStatus.get());
            if (!List.of(OrderStatusEnum.FINSHED.getStatus(), OrderStatusEnum.DELIVERED.getStatus()).contains(orderListVo.getStatus())) {
                commentStatus.set(0);
            }
            orderListVo.setCommentStatus(commentStatus.get());
            orderListVo.setItems(itemEntityList);
            orderListVo.setCurrency(I18nConstant.buildCurrency(r.getCurrencyCode()));
            if (Objects.equals(r.getSourceType(), OrderConstant.OrderSourceEnum.SCORE_MALL.type)) {
                orderListVo.setScore(r.getScore());
                ZnsOrderItemEntity znsOrderItemEntity = znsOrderItemEntities.get(0);
                ScoreMallGoodsRelationDo relationDo = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setGoodsId(znsOrderItemEntity.getGoodsId()));
                if (Objects.nonNull(relationDo)) {
                    ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(relationDo.getRuleId());
                    orderListVo.setIsVipOnly(Objects.nonNull(exchangeScoreRule) ? Objects.equals(exchangeScoreRule.getBelongTo(), 2) : Boolean.FALSE);
                }
            }
            return orderListVo;
        }).collect(Collectors.toList());
        page.setRecords(collect);

        return page;
    }

    /**
     * 订单详情
     */
    public OrderDetailVo orderInfo(String orderNo, String languageCode) {
        ZnsOrderEntity orderEntity = orderService.findByOrderNo(orderNo);
        OrderDetailVo vo = orderApiConverter.toDetailDto(orderEntity);
        vo.setScoreMallOrder(Objects.equals(orderEntity.getSourceType(), OrderConstant.OrderSourceEnum.SCORE_MALL.type));
        List<ZnsOrderItemEntity> orderItems = orderItemService.findList(new OrderItemQuery().setOrderIds(List.of(orderEntity.getId())).setLanguageCode(languageCode));
        List<Long> skuIds = orderItems.stream().map(ZnsOrderItemEntity::getSkuId).collect(Collectors.toList());
        List<GoodsSkuLanguageVo> skuLanguageVos = goodsSkuI18nService.findLanguageList(skuIds, languageCode);
        Map<Long, GoodsSkuLanguageVo> goodsSkuLanguageVoMap = skuLanguageVos.stream().collect(Collectors.toMap(GoodsSkuLanguageVo::getSkuId, Function.identity(), (v1, v2) -> v2));
        BigDecimal totalOriginalPrice = BigDecimal.ZERO;
        List<OrderItemListVo> itemEntityList = new ArrayList<>();
        int commentStatus = 0;
        for (ZnsOrderItemEntity orderItem : orderItems) {
            totalOriginalPrice = totalOriginalPrice.add(orderItem.getOriginalPrice().multiply(new BigDecimal(orderItem.getCount())));
            OrderItemListVo itemListVo = getItemListVo(goodsSkuLanguageVoMap.get(orderItem.getSkuId()), orderItem, orderEntity.getCurrencyCode());
            //1. 所有商品都已经申请后 2. 所有商品均已经申请了评价
            log.info("[goodsComment] itemListVo={},orderItem info,orderCount={},orderRefundCount={}, commentId{}", itemListVo.getOrderItemId(), itemListVo.getCount(), itemListVo.getRefundCount(), itemListVo.getCommentId());
            //没有申请售后
            if (itemListVo.getRefundCount() < itemListVo.getCount()) {
                //没有评价
                if (Objects.isNull(itemListVo.getCommentId()) || itemListVo.getCommentId() <= 0) {
                    commentStatus = 1;
                }
            }
            itemEntityList.add(itemListVo);
        }
        //已完成的订单才能评价
        log.info("[goodsComment] orderId={},orderStatus={}, commentStatus={}", vo.getOrderId(), vo.getStatus(), commentStatus);
        if (!List.of(OrderStatusEnum.FINSHED.getStatus(), OrderStatusEnum.DELIVERED.getStatus()).contains(vo.getStatus())) {
            commentStatus = 0;
        }
        vo.setCommentStatus(commentStatus);
        vo.setGoodsOriginalAmount(totalOriginalPrice);
        vo.setDiscountAmount(totalOriginalPrice.compareTo(vo.getGoodsAmount()) > 0 ? totalOriginalPrice.subtract(vo.getGoodsAmount()) : BigDecimal.ZERO);
        vo.setItems(itemEntityList);
        if (OrderStatusEnum.DELIVERED.getStatus().equals(vo.getStatus())) {
            int deliverDay = orderRefundBizService.getDeliverDay();
            vo.setApplyAfterEndDate(DateUtil.addDays(orderEntity.getGmtTakeOver(), deliverDay));
        }
        List<OrderLogistics> logisticsList = orderLogisticsService.findList(new OrderLogisticsQuery().setOrderNo(orderEntity.getOrderNo()).setDirectionType(0));
        if (!CollectionUtils.isEmpty(logisticsList)) {
            vo.setLogisticsNoList(logisticsList.stream().map(OrderLogistics::getLogisticCode).collect(Collectors.toList()));
        }
        PayOrderDetailVo payOrderDetailVo = payOrderInfo(orderNo);
        if (payOrderDetailVo != null) {
            vo.setPaypalPayState(payOrderDetailVo.getStatus());
        }
        vo.setCurrency(I18nConstant.buildCurrency(orderEntity.getCurrencyCode()));
        if (Objects.equals(orderEntity.getSourceType(), OrderConstant.OrderSourceEnum.SCORE_MALL.type)) {
            vo.setScore(orderEntity.getScore());
            ZnsOrderItemEntity znsOrderItemEntity = orderItems.get(0);
            ScoreMallGoodsRelationDo relationDo = scoreMallGoodsRelationService.findByQuery(new ScoreMallGoodsRelationQuery().setGoodsId(znsOrderItemEntity.getGoodsId()));
            if (Objects.nonNull(relationDo)) {
                ExchangeScoreRule exchangeScoreRule = exchangeScoreRuleService.selectExchangeScoreRuleById(relationDo.getRuleId());
                vo.setIsVipOnly(Objects.nonNull(exchangeScoreRule) ? Objects.equals(exchangeScoreRule.getBelongTo(), 2) : Boolean.FALSE);
            }
        }
        return vo;
    }

    private OrderItemListVo getItemListVo(GoodsSkuLanguageVo goodsSkuLanguageVo, ZnsOrderItemEntity t, String currencyCode) {
        OrderItemListVo orderItemListVo = orderApiConverter.toItemDto(t);
        if (Objects.nonNull(goodsSkuLanguageVo)) {
            if (StringUtils.hasText(goodsSkuLanguageVo.getPic())) {
                orderItemListVo.setThumbnail(goodsSkuLanguageVo.getPic());
            }
            orderItemListVo.setPropertyValues(goodsSkuLanguageVo.getPropertyValues());
            orderItemListVo.setSupportAfterSales(goodsSkuLanguageVo.getSupportAfterSales());
            orderItemListVo.setTitle(StringUtils.hasText(goodsSkuLanguageVo.getTitle()) ? goodsSkuLanguageVo.getTitle() : t.getTitle());
        }

        OrderRefundQuery orderRefundQuery = new OrderRefundQuery().setOrderItemId(orderItemListVo.getOrderItemId());
        orderRefundQuery.setOrders(Lists.newArrayList(OrderItem.desc("id")));
        List<OrderRefund> orderRefunds = orderRefundService.findList(orderRefundQuery);
        if (!CollectionUtils.isEmpty(orderRefunds)) {
            int sum = orderRefunds.stream().filter(r -> OrderRefundConstant.STATUS_ENUM.afterStatus().contains(r.getStatus())).mapToInt(OrderRefund::getCount).sum();
            orderItemListVo.setRefundCount(sum);
            orderItemListVo.setRefundId(orderRefunds.get(0).getId());
        } else {
            orderItemListVo.setRefundCount(0);
        }
        orderItemListVo.setCurrency(I18nConstant.buildCurrency(currencyCode));
        return orderItemListVo;
    }

    /**
     * 创建订单
     */
    public CreateOrderRespDto createOrder(CreateOrderReqDto req, ZnsUserEntity user) {
        Long userId = user.getId();

        //获取校验优惠券
        UserCoupon userCoupon = null;
        Long userCouponId = Optional.ofNullable(req.getUserCouponId()).orElse(req.getCouponId()); //兼容老代码，后期删除couponId
        if (Objects.nonNull(userCouponId)) {
            userCoupon = userCouponService.findById(userCouponId);
            if (userCoupon == null || !UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type.equals(userCoupon.getStatus()) || ZonedDateTime.now().isAfter(userCoupon.getGmtEnd())) {
                throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "userCoupon"), ActivityError.MALL_ORDER_COUPON_EXPIRE_FAIL.getCode());
            }
        }

        //校验参数
        checkParam(req);
        //校验前后端金额是否一致
        OrderAmountRespDto orderAmountDto = calculateOrderTotalAmount(req, user);
        String orderTotalAmountStr = req.getOrderTotalAmount().replaceAll(",", ".");
        if (orderAmountDto.getOrderTotalAmount().compareTo(new BigDecimal(orderTotalAmountStr)) != 0) {
            log.info("[createOrder]---创建订单校验参数,req={},userId={},商品价格有变动", JsonUtil.writeString(req), userId);
            throw new BaseException(I18nMsgUtils.getMessage("order.check.price"));
        }
        //获取锁
        String key = OrderConstant.CREATE_ORDER_PREFIX + userId;
        RLock lock = redissonClient.getLock(key);
        if (!lock.tryLock()) {
            log.info("[createOrder]---创建订单,req={},userId={},获取锁失败", JsonUtil.writeString(req), userId);
            throw new BaseException(I18nMsgUtils.getMessage("order.check.repeat"));
        }
        //"pitpat"+国家代码+8位(随机数+字母)
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(userId, null);
        String orderNo = SnowflakeOrderNoGenerator.buildOrderNo("pitpat", mallCountryCode);
        try {
            Long orderId = req.getOrderId();
            if (Objects.nonNull(orderId)) {
                //删除重复的订单
                mallOrderBizService.closeOrder(orderId, OrderConstant.CloseTypeEnum.CLOSE_TYPE_5); // 关闭订单释放库存
                orderService.remove(orderId);
                //删除支付订单，防止支付补偿
                paypalPayService.deleteByRefIdAndType(orderId, PayConstant.BuyTypeEnum.MALL_ORDER.type);
            }
            //预占库存
            skuPreDeduct(req.getSkuList(), orderNo, userId);

            OrderReqDto orderReqDto = new OrderReqDto(orderNo, userId, req.getFreightConfigId(), req.getPayType(), orderAmountDto, req, userCoupon, req.getSourceType());
            //创建订单
            ZnsOrderEntity orderEntity = mallOrderBizService.createOrder(orderReqDto);
            //订单关闭延迟消息
            long delayTime = orderEntity.getGmtPayEnd().toInstant().toEpochMilli() - ZonedDateTime.now().toInstant().toEpochMilli();
            rabbitTemplate.convertAndSend(mall_order_close_delay_exchange, mall_order_close_delay_key, orderEntity.getId(), message -> {
                message.getMessageProperties().setDelay((int) delayTime);// 毫秒为单位，指定此消息的延时时长
                return message;
            });
            long pushDelayTime = 10 * 60 * 1000;
            rabbitTemplate.convertAndSend(mall_order_push_delay_exchange, mall_order_push_delay_key, orderEntity.getId(), message -> {
                message.getMessageProperties().setDelay((int) pushDelayTime);// 毫秒为单位，指定此消息的延时时长
                return message;
            });
            //支付方式非Paypal。创建支付单
            if (!PayConstant.PayTypeEnum.PAYPAL.getType().equals(req.getPayType())
                    && !PayConstant.PayTypeEnum.PAYPAL_PAY_LATER.getType().equals(req.getPayType())) {
                //创建钱海支付订单
                CreatOceanpayOrderReqDto reqDto = new CreatOceanpayOrderReqDto();
                reqDto.setOrderNo(orderEntity.getOrderNo());
                reqDto.setMethods(req.getPayType());
                log.info("创建钱海支付单:{}", reqDto.getOrderNo());
                oceanpayManager.createPayOrder(reqDto, user.getId());
            }
            //更新购物车
            if (req.getIsShoppingCart()) {
                CompletableFuture.runAsync(() -> decreaseShoppingCart(userId, req.getSkuList()));
            }
            //同步订单给ERP
            CompletableFuture.runAsync(() -> ErpApiUtil.addOrModifyOrder(mallOrderBizService.convertErpOrderListRespDto(orderEntity)));
            //返回参数
            return new CreateOrderRespDto(orderEntity.getId(), orderEntity.getOrderNo(), orderEntity.getGmtPayEnd());
        } catch (BaseException e) {
            log.error("[createOrder]---创建订单,skuList={},userId={},BaseException异常", JsonUtil.writeString(req.getSkuList()), userId);
            skuPreDeductBizService.rollbackSkuPreDeduct(orderNo); //回滚预占库存
            throw e;
        } catch (Exception e) {
            log.error("[createOrder]---创建订单,skuList={},userId={},异常", JsonUtil.writeString(req.getSkuList()), userId, e);
            skuPreDeductBizService.rollbackSkuPreDeduct(orderNo); //回滚预占库存
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"), CommonError.BUSINESS_ERROR.getCode());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取校验优惠券
     */
    private UserCoupon getAndCheckUserCoupon(Long couponId, Long userId) {
        if (Objects.isNull(couponId)) {
            return null;
        }
        UserCouponQuery query = new UserCouponQuery().setCouponId(couponId).setUserId(userId)
                .setCouponStatus(UserCouponConstant.UserCouponStatusEnum.USE_STATUS_0.type).setTime(ZonedDateTime.now());
        query.setOrders(List.of(OrderItem.asc("gmt_start")));
        UserCoupon userCoupon = userCouponService.findByQuery(query);
        if (userCoupon == null) {
            log.info("[createOrder][getAndCheckUserCoupon]---创建订单获取校验优惠券,couponId={},userId={},用户券不存在或已失效", couponId, userId);
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "userCoupon"), ActivityError.MALL_ORDER_COUPON_EXPIRE_FAIL.getCode());
        }
        return userCoupon;
    }

    /**
     * 增加商品销量
     */
    private void addGoodsSaleCount(Long orderId) {
        List<ZnsOrderItemEntity> orderItemEntities = orderItemService.selectListByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderItemEntities)) {
            return;
        }
        Map<Long, Integer> goodsCountMap = orderItemEntities.stream().collect(Collectors.groupingBy(ZnsOrderItemEntity::getGoodsId, Collectors.summingInt(ZnsOrderItemEntity::getCount)));
        for (Map.Entry<Long, Integer> entry : goodsCountMap.entrySet()) {
            Long goodsId = entry.getKey();
            Integer count = entry.getValue();
            znsGoodsService.addGoodsSaleCount(goodsId, count);
        }
    }

    /**
     * 扣减购物车sku数量
     */
    private void decreaseShoppingCart(Long userId, List<OrderSkuVo> skuList) {
        for (OrderSkuVo orderSkuVo : skuList) {
            shoppingCartBizService.modifyShoppingCar(userId, List.of(orderSkuVo.getSkuId()), orderSkuVo.getCount(), OrderConstant.CarUpdateTypeEnum.DECREASE);
        }
    }

    /**
     * 预占库存
     */
    private void skuPreDeduct(List<OrderSkuVo> skuList, String orderNo, Long userId) {
        for (OrderSkuVo orderSkuVo : skuList) {
            Long skuId = orderSkuVo.getSkuId();
            String key = OrderConstant.SKU_STOCK_CHANGE_PREFIX + skuId;
            RLock lock = redissonClient.getLock(key);
            try {
                boolean isLocked = lock.tryLock(3, 30, TimeUnit.SECONDS);
                if (!isLocked) {
                    throw new RuntimeException(skuId + "预占库存,获取锁异常");
                }
                checkStatusAndStockForZeroOrder(orderSkuVo);
                //预占库存
                skuPreDeductBizService.skuPreDeduct(skuId, orderSkuVo.getCount(), orderNo, userId);
            } catch (BaseException e) {
                log.error("[createOrder]---预占库存,skuList={},业务失败", JsonUtil.writeString(skuList), e);
                throw e;
            } catch (Exception e) {
                log.error("[createOrder]---预占库存,skuList={},异常失败", JsonUtil.writeString(skuList), e);
                throw new RuntimeException(e);
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }


    /**
     * 校验商品状态、库存
     */
    public void checkStatusAndStockForZeroOrder(OrderSkuVo orderSkuVo) {
        Long skuId = orderSkuVo.getSkuId();
        ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(skuId);
        if (skuEntity == null) {
            //商品不存在
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},sku不存在", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.found"), UserError.GOODS_NO_STOCK.getCode());
        }
        ZnsGoodsEntity goodsEntity = znsGoodsService.findById(skuEntity.getGoodsId());
        if (goodsEntity == null) {
            //商品不存在
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},spu不存在", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.found"), UserError.GOODS_NO_STOCK.getCode());
        }
        if (skuEntity.getStatus() != 1 || goodsEntity.getStatus() != 1) {
            //商品不是上架中
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},商品不是上架中", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.takenoff"), UserError.GOODS_NO_STOCK.getCode());
        }
        if (skuEntity.getStock() < orderSkuVo.getCount()) {
            //库存不足
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},库存不足", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.stock"), UserError.GOODS_NO_STOCK.getCode());
        }
    }

    /**
     * 校验商品状态、库存
     */
    public void checkStatusAndStock(OrderSkuVo orderSkuVo, Long userId) {
        Long skuId = orderSkuVo.getSkuId();
        ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(skuId);
        if (skuEntity == null) {
            //商品不存在
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},sku不存在", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.found"), UserError.GOODS_NO_STOCK.getCode());
        }
        ZnsGoodsEntity goodsEntity = znsGoodsService.findById(skuEntity.getGoodsId());
        if (goodsEntity == null) {
            //商品不存在
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},spu不存在", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.found"), UserError.GOODS_NO_STOCK.getCode());
        }
        if (skuEntity.getStatus() != 1 || goodsEntity.getStatus() != 1) {
            //商品不是上架中
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},商品不是上架中", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.takenoff"), UserError.GOODS_NO_STOCK.getCode());
        }
        if (skuEntity.getStock() < orderSkuVo.getCount()) {
            //库存不足
            log.info("[checkStatusAndStock]---校验商品状态、库存,skuId={},库存不足", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.stock"), UserError.GOODS_NO_STOCK.getCode());
        }
        String userMallCurrencyCode = userExtraBizService.findUserCurrencyCode(userId, null);
        if (!userMallCurrencyCode.equals(skuEntity.getCurrencyCode())) {
            //地区暂不支持销售
            log.info("[checkParam]---创建订单校验参数,skuId={}，userMallCurrencyCode={},goodsCurrencyCode={},用户币种和商品币种不一致", skuId, userMallCurrencyCode, skuEntity.getCurrencyCode());
            throw new BaseException(I18nMsgUtils.getMessage("coupon.check.country"), UserError.SALES_NOT_SUPPORTED_ERROR.getCode());
        }
    }
    /**
     * 批量校验衍生品状态，并且收集成功和失败的
     */
    public BatchAddShoppingCartResp batchCheckStatusAndStock(List<AddShoppingCartReq> addShoppingCartReqList, Long userId) {
        BatchAddShoppingCartResp batchAddShoppingCartResp = new BatchAddShoppingCartResp();
        if (CollectionUtils.isEmpty(addShoppingCartReqList)) {
            return batchAddShoppingCartResp;
        }
        List<Long> canAddSkuIds = new ArrayList<>();
        List<Long> canNotAddSkuIds = new ArrayList<>();

        for (AddShoppingCartReq orderSkuVo : addShoppingCartReqList) {
            try {
                checkStatusAndStock(new OrderSkuVo(orderSkuVo.getSkuId(), orderSkuVo.getCount()), userId);
                canAddSkuIds.add(orderSkuVo.getSkuId());
            } catch (BaseException e) {
                canNotAddSkuIds.add(orderSkuVo.getSkuId());
            }
        }
        batchAddShoppingCartResp.setCanAddSkuIdList(canAddSkuIds);
        batchAddShoppingCartResp.setCannotAddSkuIdList(canNotAddSkuIds);
        return batchAddShoppingCartResp;
    }

    /**
     * 创建订单 校验参数
     */
    private void checkParam(CreateOrderReqDto req) {
        if (Objects.isNull(req.getFreightConfigId())) {
            log.info("[checkParam]---创建订单校验参数,req={},运费ID不存在", JsonUtil.writeString(req));
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "freightConfigId"));
        }
        if (Objects.isNull(req.getIsShoppingCart())) {
            log.info("[checkParam]---创建订单校验参数,req={},是否购物车下单不存在", JsonUtil.writeString(req));
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "isShoppingCart"));
        }
        if (!StringUtils.hasText(req.getOrderTotalAmount())) {
            log.info("[checkParam]---创建订单校验参数,req={},前端总金额不存在", JsonUtil.writeString(req));
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "orderTotalAmount"));
        }
        if (Objects.isNull(req.getAddressId())) {
            log.info("[checkParam]---创建订单校验参数,req={},地址Id不存在", JsonUtil.writeString(req));
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "addressId"));
        }
        if (CollectionUtils.isEmpty(req.getSkuList())) {
            log.info("[checkParam]---创建订单校验参数,req={},商品信息不存在", JsonUtil.writeString(req));
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "skuList"));
        }
        //错误地址业务兜底，后续地址都更新后(addressIdStr会是空字符串)不需要再加这个判定，
        String addressIdStr = iSysConfigService.selectConfigByKey("mistake_address_ids");
        if (StringUtils.hasText(addressIdStr)) {
            List<Long> addressIdList = JsonUtil.readList(addressIdStr, Long.class);
            if (!CollectionUtils.isEmpty(addressIdList) && addressIdList.contains(req.getAddressId())) {
                throw new BaseException(I18nMsgUtils.getMessage("mall.home.mistake.address.check"));
            }
        }
    }

    /**
     * 取消订单
     *
     * @param orderId
     */
    @Transactional(rollbackFor = Throwable.class)
    public boolean cancel(Long orderId, String cancelReason) {
        //查询原订单
        ZnsOrderEntity orderEntity = orderService.findById(orderId);
        if (PayConstant.PayTypeEnum.ZERO_PAY.getType().equals(orderEntity.getPayType())) {
            //0元购订单不让取消
            throw new BaseException(I18nMsgUtils.getMessage("order.cannel.zero"));
        }
        if (OrderStatusEnum.NEW.getStatus().equals(orderEntity.getStatus())) {
            mallOrderBizService.closeOrder(orderId, OrderConstant.CloseTypeEnum.CLOSE_TYPE_1);
        }
        if (OrderStatusEnum.WAIT_SEND.getStatus().equals(orderEntity.getStatus())) {
            //待发货需要通知erp并退款,先生成退款单
            boolean b = doRefund(orderEntity);
            if (!b) {
                log.info("[cancel]---取消订单,orderId={},生成退款单失败", orderId);
                return b;
            }
        }
        //更新订单取消原因
        ZnsOrderEntity updateEntity = new ZnsOrderEntity(orderId);
        updateEntity.setCancelReason(cancelReason);
        orderService.update(updateEntity);

        //用户取消订单，钉钉通知
        mallOrderBizService.orderCancelMsg(orderId);
        return true;
    }

    private boolean doRefund(ZnsOrderEntity orderEntity) {
        OrderRefund refund = new OrderRefund();
        refund.setOrderId(orderEntity.getId());
        refund.setOrderNo(orderEntity.getOrderNo());
        refund.setUserId(orderEntity.getUserId());
        refund.setApplyAmount(orderEntity.getActualAmount());
        refund.setRefundType(OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT.getCode());
        refund.setIsUserDelete(1);
        refund.setRefundAmountType(OrderRefundConstant.REFUND_AMOUNT_TYPE_ENUM.REFUND_GOOD.getCode());
        String countryCode = StringUtils.hasText(orderEntity.getCountryCode()) ? orderEntity.getCountryCode() : I18nConstant.CountryCodeEnum.US.code;
        refund.setRefundNo(SnowflakeOrderNoGenerator.buildOrderNo("TK", countryCode));
        refund.setReason("order cancel");
        refund.setPayStatus(OrderRefundConstant.PAY_STATUS_ENUM.PAY_REFUNDING.getCode());
        refund.setPostageAmount(orderEntity.getPostage());
        refund.setTaxAmount(orderEntity.getTaxAmount());
        refund.setCouponAmount(orderEntity.getCouponAmount());
        orderRefundService.insertOrderRefund(refund);
        String result = ErpApiUtil.cancelOrder(new ErpOrderSourceCodeDto().setOrderSourceCode(refund.getOrderNo()));
        ErpResponsDto erpResponsDto = JsonUtil.readValue(result, ErpResponsDto.class);
        if (Integer.valueOf(1).equals(erpResponsDto.getData())) {
            mallOrderBizService.closeOrder(orderEntity.getId(), OrderConstant.CloseTypeEnum.CLOSE_TYPE_1);

            refund.setPayStatus(OrderRefundConstant.PAY_STATUS_ENUM.PAY_REFUNDING.getCode());
            orderRefundService.updateOrderRefundById(refund);

            String refundResult = mallOrderPaymentBizService.createRefundPayOrder(refund.getRefundNo());
            if (!StringUtils.hasText(refundResult) || PayConstant.RefundPayStatusEnum.FAILED.type.equals(refundResult)) {
                //发起支付失败
                throw new BaseException("The refund application failed");
            }
            mallOrderPaymentBizService.dealRefundResult(refund.getRefundNo(), PayConstant.RefundPayStatusEnum.findByCode(refundResult), null);
            return true;
        } else {
            refund.setStatus(OrderRefundConstant.STATUS_ENUM.REJECT_REFUND.getCode());
            refund.setRefundRemark("Delivered");
            orderRefundService.updateOrderRefundById(refund);
            log.info("[doRefund]---取消订单,erp返回异常,orderId={},erp返回={}", orderEntity.getId(), erpResponsDto.getMsg());
            return false;
        }
    }
    public boolean doRefundExcludeErp(ZnsOrderEntity orderEntity) {
        OrderRefund refund = new OrderRefund();
        refund.setOrderId(orderEntity.getId());
        refund.setOrderNo(orderEntity.getOrderNo());
        refund.setUserId(orderEntity.getUserId());
        refund.setApplyAmount(orderEntity.getActualAmount());
        refund.setRefundType(OrderRefundConstant.REFUND_TYPE_ENUM.REFUND_AMOUNT.getCode());
        refund.setIsUserDelete(1);
        refund.setRefundAmountType(OrderRefundConstant.REFUND_AMOUNT_TYPE_ENUM.REFUND_GOOD.getCode());
        String countryCode = StringUtils.hasText(orderEntity.getCountryCode()) ? orderEntity.getCountryCode() : I18nConstant.CountryCodeEnum.US.code;
        refund.setRefundNo(SnowflakeOrderNoGenerator.buildOrderNo("TK", countryCode));
        refund.setReason("order cancel");
        refund.setPayStatus(OrderRefundConstant.PAY_STATUS_ENUM.PAY_REFUNDING.getCode());
        refund.setPostageAmount(orderEntity.getPostage());
        refund.setTaxAmount(orderEntity.getTaxAmount());
        refund.setCouponAmount(orderEntity.getCouponAmount());
        orderRefundService.insertOrderRefund(refund);
        mallOrderBizService.closeOrder(orderEntity.getId(), OrderConstant.CloseTypeEnum.CLOSE_TYPE_1);
        refund.setPayStatus(OrderRefundConstant.PAY_STATUS_ENUM.PAY_REFUNDING.getCode());
        orderRefundService.updateOrderRefundById(refund);

        String refundResult = mallOrderPaymentBizService.createRefundPayOrder(refund.getRefundNo());
        if (!StringUtils.hasText(refundResult) || PayConstant.RefundPayStatusEnum.FAILED.type.equals(refundResult)) {
            //发起支付失败
            throw new BaseException("The refund application failed");
        }
        mallOrderPaymentBizService.dealRefundResult(refund.getRefundNo(), PayConstant.RefundPayStatusEnum.findByCode(refundResult), null);
        return true;
    }


    /**
     * 计算订单总金额 = 商品总金额 + 税额 + 运费 + 小费 - 优惠券金额 - 津贴
     */
    public OrderAmountRespDto calculateOrderTotalAmount(OrderAmountReqDto req, ZnsUserEntity user) {
        //校验sku状态、数量
        List<OrderSkuVo> skuList = req.getSkuList();
        for (OrderSkuVo skuVo : skuList) {
            checkStatusAndStock(skuVo, user.getId());
        }

        //计算税费
        BigDecimal taxAmount = BigDecimal.ZERO; //税额
        BigDecimal taxRate = BigDecimal.ZERO; //税率
        MallTaxRateDo sysTaxRateDo = sysTaxRateBizService.findTaxDoByAddressId(req.getAddressId());
        if (sysTaxRateDo != null) {
            taxRate = sysTaxRateDo.getTaxRate();
            taxAmount = sysTaxRateBizService.calculateTax(req);
        }
        OrderAmountRespDto resp = getOrderAmountRespDto(req, taxAmount, taxRate);
        RewardAccountDo rewardAccountDo = rewardAccountService.findByQuery(new RewardAccountQuery().setUserId(user.getId()));
        if (user.getAppVersion() < Constant.appVersion_4044 || Optional.ofNullable(req.getNotUseCoupon()).orElse(false)) {
            //4.4.4之前 or 不使用券，用老方法
            List<GoodsDiscountAmountDetailVo> skuAmountDetails = new ArrayList<>(); //sku价格明细
            //订单金额
            BigDecimal orderTotalAmount = BigDecimal.ZERO;
            for (OrderSkuVo orderSkuVo : skuList) {
                ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(orderSkuVo.getSkuId());
                orderTotalAmount = orderTotalAmount.add(skuEntity.getSalePrice().multiply(new BigDecimal(orderSkuVo.getCount())));
                skuAmountDetails.add(new GoodsDiscountAmountDetailVo(skuEntity.getCurrencyCode(), skuEntity.getId(), orderSkuVo.getCount(), skuEntity.getOriginalPrice(), skuEntity.getSalePrice(), BigDecimal.ZERO, BigDecimal.ZERO));
            }
            resp.setSkuDiscountAmountDetails(skuAmountDetails);
            resp.setCurrencyCode(skuAmountDetails.get(0).getCurrencyCode());
            resp.setCurrencySymbol(skuAmountDetails.get(0).getCurrencySymbol());
            dealAllowanceAmount(req, rewardAccountDo, resp, orderTotalAmount);
            return resp;
        }

        //校验券是否合适
        Long couponId = req.getCouponId();
        if (Objects.nonNull(req.getUserCouponId())) {
            UserCoupon userCoupon = userCouponService.findById(req.getUserCouponId());
            if (userCoupon != null) {
                couponId = userCoupon.getCouponId();
            }
        }
        Set<UserCouponConstant.UnAvailableReasonEnum> unAvailableReasonEnums = mallCouponComponent.checkUserCouponByCouponIdAndSku(user, couponId, skuList);
        if (!CollectionUtils.isEmpty(unAvailableReasonEnums)) {
            //指定券不可用,适用最佳优惠券
            couponId = null;
        }

        //计算券后订单金额
        OrderSkuAmountRespDto respDto = mallUserCouponBizService.calOrderCouponAmount(skuList, user, couponId);

        //新税费使用 券后销售价计算
        BigDecimal newTaxAmount = BigDecimal.ZERO;
        if (sysTaxRateDo != null) {
            BigDecimal raxRate = sysTaxRateDo.getTaxRate().divide(new BigDecimal("100"), 5, RoundingMode.HALF_UP); //10% -> 0.1
            BigDecimal postageAmount = Optional.ofNullable(resp.getPostageAmount()).orElse(BigDecimal.ZERO);
            newTaxAmount = (resp.getTotalSaleAmount().subtract(respDto.getCouponAmount()).add(postageAmount)).multiply(raxRate).setScale(2, RoundingMode.HALF_UP);
            newTaxAmount = newTaxAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : newTaxAmount;
        }

        //总金额 = 原订单金额（销售价+运费+小费+税率） - 优惠券金额
        BigDecimal orderTotalAmount = resp.getOrderTotalAmount().subtract(resp.getTaxAmount()).add(newTaxAmount).subtract(respDto.getCouponAmount());

        orderTotalAmount = dealAllowanceAmount(req, rewardAccountDo, resp, orderTotalAmount);
        resp.setOrderTotalAmount(orderTotalAmount);
        resp.setTaxAmount(newTaxAmount);
        resp.setAllDiscountAmount(resp.getTotalDiscountAmount().add(respDto.getCouponAmount())); //所有优惠总金额（商品优惠+优惠券）
        resp.setCouponAmount(respDto.getCouponAmount());
        resp.setMallCouponDto(respDto.getMallCouponDto());
        resp.setSkuDiscountAmountDetails(respDto.getSkuDiscountAmountDetails());
        resp.setCurrencyCode(respDto.getSkuDiscountAmountDetails().get(0).getCurrencyCode());
        resp.setCurrencySymbol(respDto.getSkuDiscountAmountDetails().get(0).getCurrencySymbol());
        return resp;
    }

    private BigDecimal dealAllowanceAmount(OrderAmountReqDto req, RewardAccountDo rewardAccountDo, OrderAmountRespDto resp, BigDecimal orderTotalAmount) {
        if (Objects.nonNull(rewardAccountDo) && rewardAccountDo.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0) {
            //可用津贴总数
            resp.setUserAllowanceAmount(rewardAccountDo.getAvailableAmount());

            // 只有当订单金额 > 0.01时才考虑使用津贴
            if (orderTotalAmount.compareTo(new BigDecimal("0.01")) > 0 && req.getUseAllowance()) {
                // 计算最大可使用的津贴金额（保证扣减后 >=0.01）
                BigDecimal maxAllowance = orderTotalAmount.subtract(new BigDecimal("0.01"));

                // 实际使用的津贴 = min(可用津贴, 最大可使用的津贴)
                BigDecimal usedAllowance = rewardAccountDo.getAvailableAmount().min(maxAllowance);

                // 扣减订单金额（此时不会低于0.01）
                orderTotalAmount = orderTotalAmount.subtract(usedAllowance);
                resp.setAllowanceAmount(usedAllowance);
            }
        }
        return orderTotalAmount;
    }

    /**
     * 获取订单总金额
     *
     * @param req
     * @param taxAmount
     * @param taxRate
     * @return
     */
    private OrderAmountRespDto getOrderAmountRespDto(OrderAmountReqDto req, BigDecimal taxAmount, BigDecimal taxRate) {
        //计算商品总价
        BigDecimal totalSaleAmount = BigDecimal.ZERO; // 销售价总金额
        BigDecimal originalTotalAmount = BigDecimal.ZERO; // 商品原价总金额
        BigDecimal totalDiscountAmount = BigDecimal.ZERO;  //优惠总金额
        List<Long> skuIds = req.getSkuList().stream().map(OrderSkuVo::getSkuId).distinct().toList();
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        Map<Long, ZnsGoodsSkuEntity> skuMap = skuEntities.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, Function.identity(), (k1, k2) -> k1));
        for (OrderSkuVo skuVo : req.getSkuList()) {
            ZnsGoodsSkuEntity skuEntity = skuMap.get(skuVo.getSkuId());
            //销售价金额
            BigDecimal skuTotalAmount = skuEntity.getSalePrice().multiply(new BigDecimal(skuVo.getCount())).setScale(2, RoundingMode.HALF_UP);
            totalSaleAmount = totalSaleAmount.add(skuTotalAmount);
            // 商品原价总金额
            BigDecimal originalPrice = Optional.ofNullable(skuEntity.getOriginalPrice()).orElse(skuEntity.getSalePrice());
            BigDecimal originalAmount = originalPrice.multiply(new BigDecimal(skuVo.getCount())).setScale(2, RoundingMode.HALF_UP);
            originalTotalAmount = originalTotalAmount.add(originalAmount);
            //商品优惠金额
            BigDecimal discountAmount = originalAmount.subtract(totalSaleAmount);
            discountAmount = discountAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : discountAmount;
            totalDiscountAmount = totalDiscountAmount.add(discountAmount);
        }

        //总金额 = 商品总金额 + 税额 + 运费 + 小费
        BigDecimal postageAmount = Optional.ofNullable(req.getPostageAmount()).filter(StringUtils::hasText)
                .map(item -> new BigDecimal(item.replaceAll(",", "."))).orElse(BigDecimal.ZERO);
        BigDecimal totalAmount = totalSaleAmount.add(taxAmount)
                .add(postageAmount)
                .add(Optional.ofNullable(req.getTipAmount()).orElse(BigDecimal.ZERO));

        return new OrderAmountRespDto(totalAmount, taxAmount, Optional.ofNullable(req.getTipAmount()).orElse(BigDecimal.ZERO),
                totalSaleAmount, totalDiscountAmount, postageAmount, taxRate, originalTotalAmount);
    }

    /**
     * 结算页信息
     */
    public OrderSettleInfoRespDto settleInfo(OrderSettleInfoReqDto req, String languageCode, Long userId, Integer appVersion,Boolean isH5Surrender) {
        //填充支付方式
        String countryCode = userExtraBizService.findUserMallCountryCodeOrDefault(userId, null);
        OrderSettleInfoRespDto resp = fillPaymentType(userId, appVersion, countryCode);

        //分期金额配置
        List<CurrencyInatallmentVo> installmentConfigList = iSysConfigService.selectConfigListByKey(ConfigKeyEnums.CURRENCY_PAYMENT_INSTALLMENT_CONFIG.getCode(), CurrencyInatallmentVo.class);
        if (CollectionUtils.isEmpty(installmentConfigList)) {
            return resp;
        }
        //根据国家查询币种,获取指定币种配置
        String currencyCode = isH5Surrender ? I18nConstant.CurrencyCodeEnum.USD.getCode() : userExtraBizService.findUserCurrencyCode(userId, null);

        //查询分期配置
        CurrencyInatallmentVo currencyInatallmentVo = installmentConfigList.stream().filter(item -> currencyCode.equals(item.getCurrencyCode())).findFirst().orElse(null);
        if (Objects.nonNull(currencyInatallmentVo) && !CollectionUtils.isEmpty(currencyInatallmentVo.getPeriodConfig())) {
            currencyInatallmentVo.getPeriodConfig().forEach(item -> item.setCurrencySymbol(I18nConstant.buildCurrency(currencyCode, I18nConstant.CurrencyCodeEnum.USD.getCode()).getCurrencySymbol()));
            resp.setPaymentInstallmentDtos(currencyInatallmentVo.getPeriodConfig());
        }
        if (Objects.isNull(req.getAddressId())) {
            //没有收获地址Id表示 是订单详情页查询支付方式，直接返回就行
            return resp;
        }

        //查询商城运费
        ZnsUserAddressEntity userAddress = znsUserAddressService.findById(req.getAddressId());
        if (userAddress == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "user address"));
        }
        List<OrderShippingVo> orderShippingVos = new ArrayList<>();
        List<MallFreightConfig> list = mallFreightConfigService.findAll();
        List<MallFreightConfig> freightConfigs = list.stream().filter(itme -> !StringUtils.hasText(itme.getApplicationCountry()) || itme.getApplicationCountry().contains(countryCode)).toList();
        if (CollectionUtils.isEmpty(freightConfigs)) {
            resp.setOrderShippingVos(orderShippingVos);
            return resp;
        }

        for (MallFreightConfig freightConfig : freightConfigs) {
            Long freightConfigId = freightConfig.getId();
            MallFreightConfigI8n freightConfigI8n = mallFreightConfigI8nService.findByConfigIdAndDefaultLangCode(freightConfigId, languageCode, freightConfig.getDefaultLanguageCode());
            if (freightConfigI8n == null) {
                continue;
            }
            String currencySymbol = I18nConstant.CurrencyCodeEnum.USD.getSymbol();
            BigDecimal amount = freightConfig.getAmount();
            if (StringUtils.hasText(freightConfig.getCurrency())) {
                CurrencyAmount currencyAmount = JsonUtil.readList(freightConfig.getCurrency(), CurrencyAmount.class).stream().filter(item -> item.getCurrencyCode().equals(currencyCode)).findFirst().orElse(null);
                if (currencyAmount != null) {
                    currencySymbol = currencyAmount.getCurrencySymbol();
                    amount = currencyAmount.getAmount();
                }
            }
            OrderShippingVo shippingVo = new OrderShippingVo(currencySymbol, freightConfigId, freightConfigI8n.getName(), freightConfigI8n.getTimelinessInfo(), amount, freightConfig.getIsDefault());
            orderShippingVos.add(shippingVo);
        }
        resp.setOrderShippingVos(orderShippingVos);
        return resp;
    }

    /**
     * 填充支付方式
     */
    private OrderSettleInfoRespDto fillPaymentType(Long userId, Integer appVersion, String countryCode) {
        //查询支付配置
        OrderSettleInfoRespDto resp = new OrderSettleInfoRespDto();
        List<PayMethodDto> list = paymentConfigManager.getCanusePayMethod(countryCode,new ArrayList<>(),false);
        if (CollectionUtils.isEmpty(list)) {
            resp.setPayMethodDtos(List.of());
            resp.setPaymentTypes(List.of());
            return resp;
        }

        //查询支付白名单
        List<String> keyList = Lists.newArrayList(ConfigKeyEnums.OCEANPAY_WHITE_LIST_CONFIG.getCode(), ConfigKeyEnums.PAYPAL_PAY_LATER_WHITE_LIST_CONFIG.getCode(),
                ConfigKeyEnums.OCEANPAY_GOOGLE_WHITE_LIST_CONFIG.getCode(), ConfigKeyEnums.OCEANPAY_APPLE_WHITE_LIST_CONFIG.getCode(),
                ConfigKeyEnums.OCEANPAY_AFTERPAY_WHITE_LIST_CONFIG.getCode(), ConfigKeyEnums.OCEANPAY_KLARNA_WHITE_LIST_CONFIG.getCode());
        Map<String, String> whiteMap = iSysConfigService.selectConfigValueListByKeys(keyList, true);

        //校验支付白名单
        List<String> paymentTypes = new ArrayList<>();
        List<PayMethodDto> payMethodDtos = new ArrayList<>();
        for (PayMethodDto payMethodDto : list) {
            PayConstant.PayTypeEnum payTypeEnum = PayConstant.PayTypeEnum.findByType(payMethodDto.getPayType());
            if (payTypeEnum == null) {
                continue;
            }
            if (payTypeEnum == PayConstant.PayTypeEnum.PAYPAL) {
                //paypal是默认支付,只要开启都会有，不用判断白名单
                paymentTypes.add(PayConstant.PayTypeEnum.PAYPAL.code);
                payMethodDtos.add(payMethodDto);
                continue;
            }
            //根据不同支付方式判断白名单
            String whiteListStr = null;
            switch (payTypeEnum) {
                case OCEANPAY -> whiteListStr = whiteMap.get(ConfigKeyEnums.OCEANPAY_WHITE_LIST_CONFIG.getCode());
                case PAYPAL_PAY_LATER -> whiteListStr = whiteMap.get(ConfigKeyEnums.PAYPAL_PAY_LATER_WHITE_LIST_CONFIG.getCode());
                case OCEANPAY_GOOGLE -> whiteListStr = whiteMap.get(ConfigKeyEnums.OCEANPAY_GOOGLE_WHITE_LIST_CONFIG.getCode());
                case OCEANPAY_APPLE -> whiteListStr = whiteMap.get(ConfigKeyEnums.OCEANPAY_APPLE_WHITE_LIST_CONFIG.getCode());
                case OCEANPAY_AFTERPAY -> whiteListStr = whiteMap.get(ConfigKeyEnums.OCEANPAY_AFTERPAY_WHITE_LIST_CONFIG.getCode());
                case OCEANPAY_KLARNA -> whiteListStr = whiteMap.get(ConfigKeyEnums.OCEANPAY_KLARNA_WHITE_LIST_CONFIG.getCode());
            }
            if (checkWhiteUser(whiteListStr, userId)) {
                //符合白名单
                paymentTypes.add(payTypeEnum.code);
                payMethodDtos.add(payMethodDto);
            }
        }

        if (appVersion < 4047) {
            //过滤掉新的支付方式
            paymentTypes.removeIf(k -> PayConstant.PayTypeEnum.OCEANPAY_GOOGLE.code.equals(k)
                    || PayConstant.PayTypeEnum.OCEANPAY_APPLE.code.equals(k)
                    || PayConstant.PayTypeEnum.OCEANPAY_AFTERPAY.code.equals(k)
                    || PayConstant.PayTypeEnum.OCEANPAY_KLARNA.code.equals(k)
                    || PayConstant.PayTypeEnum.PAYPAL_PAY_LATER.code.equals(k));
        }
        //填充支付方式
        resp.setPaymentTypes(paymentTypes);
        resp.setPayMethodDtos(payMethodDtos);
        return resp;
    }

    /**
     * 判断白名单
     *
     * @return 未配置白名单||是白名单 true
     */
    private boolean checkWhiteUser(String whiteListStr, Long userId) {
        if (!StringUtils.hasText(whiteListStr)) {
            return true;
        }
        List<Long> whiteList = JsonUtil.readList(whiteListStr, Long.class);
        return CollectionUtils.isEmpty(whiteList) || whiteList.contains(userId);
    }

    /**
     * 查询订单sku信息
     */
    public List<MallOrderSkuRespDto> listOrderSkuInfo(OrderCheckOutReqDto req) {
        List<Long> skuIds = req.getSkuList().stream().map(OrderSkuVo::getSkuId).toList();
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        Map<Long, ZnsGoodsSkuEntity> skuMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuEntities)) {
            skuMap = skuEntities.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, Function.identity()));
        }
        List<Long> goodsIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        Map<Long, ZnsGoodsEntity> goodsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(goodsIds)) {
            List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(goodsIds);
            goodsMap = goodsEntities.stream().collect(Collectors.toMap(ZnsGoodsEntity::getId, Function.identity()));
        }
        List<MallOrderSkuRespDto> result = new ArrayList<>();
        for (OrderSkuVo orderSkuVo : req.getSkuList()) {
            ZnsGoodsSkuEntity skuEntity = skuMap.get(orderSkuVo.getSkuId());
            ZnsGoodsEntity goodsEntity = goodsMap.get(Optional.ofNullable(skuEntity).map(ZnsGoodsSkuEntity::getGoodsId).orElse(0L));
            if (skuEntity == null || goodsEntity == null) {
                result.add(new MallOrderSkuRespDto(orderSkuVo, 0, -1));
            } else {
                Integer skuStatus = Objects.equals(skuEntity.getStatus(), 1) && Objects.equals(goodsEntity.getStatus(), 1) ? 1 : -1;
                result.add(new MallOrderSkuRespDto(orderSkuVo, skuEntity.getStock(), skuStatus));
            }
        }
        return result;
    }

    /**
     * 下单选择优惠券列表
     */
    public MallOrderPickCouponResponseDto pickCouponList(OrderCheckOutReqDto req, ZnsUserEntity user) {
        MallOrderPickCouponResponseDto result = new MallOrderPickCouponResponseDto();
        List<OrderPickCouponDto> list = mallUserCouponBizService.findUserCouponBySkus(req.getSkuList(), user);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<OrderPickCouponDto> availableCoupons = list.stream().filter(item -> Objects.equals(item.getIsAvailable(), 1)).toList();
        List<OrderPickCouponDto> unAvailableCoupons = list.stream().filter(item -> Objects.equals(item.getIsAvailable(), 0)).toList();
        result.setAvailableCoupons(sortCouponList(availableCoupons, req.getSkuList()));//按优惠金额从大到小排序
        result.setUnAvailableCoupons(unAvailableCoupons);
        return result;
    }

    /**
     * /按优惠金额从大到小排序
     */
    private List<OrderPickCouponDto> sortCouponList(List<OrderPickCouponDto> availableCoupons, List<OrderSkuVo> skuVos) {
        if (CollectionUtils.isEmpty(availableCoupons)) {
            return availableCoupons;
        }

        //查询spuId
        List<Long> skuIds = skuVos.stream().map(OrderSkuVo::getSkuId).distinct().toList();
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findByIds(skuIds);
        List<Long> spuIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        Map<Long, Long> spuIdMap = skuEntities.stream().collect(Collectors.toMap(ZnsGoodsSkuEntity::getId, ZnsGoodsSkuEntity::getGoodsId, (k1, k2) -> k2));

        //查询优惠券可用的spu, key -> couponId，val -> spuIdList
        ActivityCouponConfigQuery query = ActivityCouponConfigQuery.builder().couponConfigIds(spuIds).type(CouponConstant.CouponConfigEnum.COUPON_CONFIG_100.type).build();
        List<ActivityCouponConfig> couponConfigs = couponConfigService.findListByQuery(query);
        Map<Long, List<Long>> couponMap = couponConfigs.stream().collect(Collectors.groupingBy(ActivityCouponConfig::getCouponId, Collectors.mapping(item -> Long.valueOf(item.getCouponConfig()), Collectors.toList())));

        BigDecimal totalSaleAmount = mallCouponComponent.calTotalSaleAmount(skuVos);

        for (OrderPickCouponDto couponDto : availableCoupons) {
            //计算优惠券可用的sku销售价总金额
            BigDecimal saleAmount = totalSaleAmount;
            if (!CouponConstant.UseScopeEnum.USE_SCOPE_1.type.equals(couponDto.getUseScope())) {
                //不是通用券，过滤指定sku
                List<Long> couponSpuIds = Optional.ofNullable(couponMap.get(couponDto.getCouponId())).orElse(new ArrayList<>());
                List<OrderSkuVo> skuList = skuVos.stream().filter(item -> couponSpuIds.contains(spuIdMap.getOrDefault(item.getSkuId(), 0L))).toList();
                if (CollectionUtils.isEmpty(skuList)) {
                    couponDto.setCouponAmount(BigDecimal.ZERO);
                    continue;
                }
                saleAmount = mallCouponComponent.calTotalSaleAmount(skuList);
            }
            //校验优惠券门槛
            BigDecimal minTotalAmount = Optional.ofNullable(couponDto.getMinTotalAmount()).orElse(BigDecimal.ZERO);
            if (minTotalAmount.compareTo(BigDecimal.ZERO) > 0 && minTotalAmount.compareTo(saleAmount) > 0) {
                //未达可用门槛(门槛不为0)
                couponDto.setCouponAmount(BigDecimal.ZERO);
                continue;
            }
            //计算优惠券金额
            BigDecimal amount;
            if (CouponConstant.DiscountMethodEnum.DISCOUNT_METHOD_1.type.equals(couponDto.getDiscountMethod())) {
                //按金额计算优惠
                amount = Optional.ofNullable(couponDto.getAmount()).orElse(BigDecimal.ZERO);
            } else {
                //这里计算的优惠金额，直接用折扣（打9折 -> 0.1 ） 如果商品 100，优惠金额 = 100 * 0.1 = 10
                amount = couponDto.getDiscount().multiply(saleAmount).setScale(2, RoundingMode.HALF_UP);
            }
            couponDto.setCouponAmount(amount);
        }
        //按优惠金额排序，越大越在前
        return availableCoupons.stream().sorted(Comparator.comparing(OrderPickCouponDto::getCouponAmount, Comparator.reverseOrder())).toList();
    }

    /**
     * 支付订单详情
     *
     * @param orderNo
     * @return
     */
    public PayOrderDetailVo payOrderInfo(String orderNo) {
        ZnsOrderEntity orderEntity = orderService.findByOrderNo(orderNo);
        if (orderEntity == null) {
            return new PayOrderDetailVo();
        }
        PaypalPayQuery query = PaypalPayQuery.builder().refId(orderEntity.getId()).typeList(PayConstant.BuyTypeEnum.findMallType()).build();
        query.setOrders(List.of(OrderItem.desc("id")));
        List<PaypalPay> list = paypalPayService.findListByQuery(query);
        if (CollectionUtils.isEmpty(list)) {
            return new PayOrderDetailVo();
        }
        //优先使用已完成的
        PaypalPay paypalPay = list.stream().filter(item -> Objects.equals(item.getStatus(), PayConstant.PayStatusEnum.PAY_STATUS_1.type)).findFirst().orElse(null);
        if (paypalPay == null) {
            //未完成，使用最新的
            paypalPay = list.get(0);
        }
        return new PayOrderDetailVo(paypalPay);
    }

    /**
     * 取消订单原因配置列表
     */
    public List<String> cancelReasonList(String languageCode) {
        //查询可用的原因配置
        OrderCancelReasonConfigQuery query = new OrderCancelReasonConfigQuery().setState(1).setType(1);
        query.setOrders(List.of(OrderItem.desc("sort"), OrderItem.desc("id")));
        List<OrderCancelReasonConfigDo> configDos = orderCancelReasonConfigService.findList(query);
        if (CollectionUtils.isEmpty(configDos)) {
            return new ArrayList<>();
        }

        //查询i18n
        List<Long> cancelConfigIds = configDos.stream().map(OrderCancelReasonConfigDo::getId).toList();
        OrderCancelReasonConfigI18nQuery i18nQuery = new OrderCancelReasonConfigI18nQuery()
                .setLanguageCode(languageCode).setCancelConfigIds(cancelConfigIds);
        List<OrderCancelReasonConfigI18nDo> configI18nDos = orderCancelReasonConfigI18nService.findList(i18nQuery);
        Map<Long, String> reasonMap = configI18nDos.stream().collect(Collectors.toMap(OrderCancelReasonConfigI18nDo::getCancelConfigId, item -> Optional.ofNullable(item.getReason()).orElse("")));

        //封装结果
        return configDos.stream().map(item -> StringUtils.hasText(reasonMap.get(item.getId())) ? reasonMap.get(item.getId()) : item.getDefaultCancelReason()).toList();
    }

    public CreateOrderRespDto createOrderZero(CreateOrderZeroReqDto req, ZnsUserEntity user) {
        //后端金额0元购
        Long userId = user.getId();
        //保存用户地址
        if (!"United States".equals(req.getCountry())) {
            log.info("[createOrderZero]---创建订单,req={},userId={},实物奖品当前只支持美国地区领取", JsonUtil.writeString(req), userId);
            throw new BaseException(I18nMsgUtils.getMessage("order.check.zero.country"));
        }
        ZnsUserAddressEntity entity = userAddressConverter.toEntity(req);
        znsUserAddressService.insert(entity);
        //获取锁
        String key = OrderConstant.CREATE_ORDER_PREFIX + userId;
        RLock lock = redissonClient.getLock(key);
        if (!lock.tryLock()) {
            log.info("[createOrderZero]---创建订单,req={},userId={},获取锁失败", JsonUtil.writeString(req), userId);
            throw new BaseException(I18nMsgUtils.getMessage("order.check.repeat"));
        }
        String orderNo = NanoId.randomNanoId();
        List<OrderSkuVo> skuList = new ArrayList<>();
        //系统配置获取固定sku 0元购 sku id 数量固定1
        String skuConfigId = iSysConfigService.selectConfigByKey(ConfigKeyEnums.TURBOLINK_DRAW_GOODS_SKU_ID.getCode());
        skuList.add(new OrderSkuVo(Long.valueOf(skuConfigId), 1));
        try {
            //预占库存
            skuPreDeduct(skuList, orderNo, userId);
            OrderAmountRespDto orderAmountDto = new OrderAmountRespDto();
            orderAmountDto.setAllowanceAmount(BigDecimal.ZERO);
            orderAmountDto.setTipAmount(BigDecimal.ZERO);
            orderAmountDto.setOrderTotalAmount(BigDecimal.ZERO);
            orderAmountDto.setTotalSaleAmount(BigDecimal.ZERO);
            orderAmountDto.setTotalDiscountAmount(BigDecimal.ZERO);
            //创建订单
            ZnsOrderEntity orderEntity = mallOrderBizService.createOrderZero(orderNo, userId, orderAmountDto, entity.getId(), req.getPayType(), skuList);
            //修改实物抽奖状态
            TurbolinkDrawDo turbolinkDrawDo = turbolinkDrawService.findByQuery(new TurbolinkDrawQuery().setUserId(userId));
            turbolinkDrawDo.setDrawStatus(YesNoStatus.YES.getCode());
            turbolinkDrawDo.setOrderId(orderEntity.getId());
            turbolinkDrawService.update(turbolinkDrawDo);
            //同步订单给ERP
            CompletableFuture.runAsync(() -> ErpApiUtil.addOrModifyOrder(mallOrderBizService.convertErpOrderListRespDto(orderEntity)));
            //返回参数
            return new CreateOrderRespDto(orderEntity.getId(), orderEntity.getOrderNo(), orderEntity.getGmtPayEnd());
        } catch (Exception e) {
            log.error("[createOrderZero]---创建订单,skuList={},userId={},异常", JsonUtil.writeString(skuList), userId, e);
            skuPreDeductBizService.rollbackSkuPreDeduct(orderNo); //回滚预占库存
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"), CommonError.BUSINESS_ERROR.getCode());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public CreateOrderRespDto H5SurrenderOrderList(Long userId){
        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setUserId(userId).setOrders(List.of(OrderItem.desc("id")));
        ZnsOrderEntity orderEntity = orderService.findByQuery(orderQuery);
        if (Objects.nonNull(orderEntity)) {
            return new CreateOrderRespDto(orderEntity.getId(), orderEntity.getOrderNo(), orderEntity.getGmtPayEnd(),orderEntity.getStatus());
        } else {
            return null;
        }
    }
}
