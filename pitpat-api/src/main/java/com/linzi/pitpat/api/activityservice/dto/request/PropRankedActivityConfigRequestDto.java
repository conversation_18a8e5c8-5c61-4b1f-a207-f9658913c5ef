package com.linzi.pitpat.api.activityservice.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 活动配置模板
 */

@Data
@NoArgsConstructor
public class PropRankedActivityConfigRequestDto {
    /**
     * 参赛选手
     */
    private int playerCount;
    /**
     * 目标历程
     */
    private Integer mileTarget;

    /**
     * 线路信息
     */
    private Long routeId;

    /**
     * 用户时区（从请求头获取）
     */
    @JsonIgnore
    private String timeZone;

    /**
     * 设备蓝牙固件版本号
     */
    private Integer versionNo;

    /**
     * 设备唯一编码
     */
    private String equipmentNo;

    private Integer appVersion;

    //超时测试
    private ZonedDateTime timeOut;
}
