package com.linzi.pitpat.api.dto.response;

import com.linzi.pitpat.data.vo.user.GetUserInfoVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

@ToString
@Data
@NoArgsConstructor
public class UserGameFlagResp extends GetUserInfoVo {
    /**
     * 用户标签 0 老用户 1 新用户
     */
    private Integer userFlag;

    /**
     * 里程 m或者时长值 s
     */
    private BigDecimal runMileageOrTime;

    /**
     * 0：无，1：里程，2：时长',
     */
    private Integer targetType;

    /**
     *  用户类型 1 online 2 la
     */
    private Integer userLaType;
}
