package com.linzi.pitpat.api.mallservice.controller.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.mallservice.dto.request.BatchAddShoppingCartReq;
import com.linzi.pitpat.api.mallservice.dto.request.CreateOrderReqDto;
import com.linzi.pitpat.api.mallservice.dto.request.CreateOrderZeroReqDto;
import com.linzi.pitpat.api.mallservice.dto.request.OrderSettleInfoReqDto;
import com.linzi.pitpat.api.mallservice.dto.response.BatchAddShoppingCartResp;
import com.linzi.pitpat.api.mallservice.dto.response.CreateOrderRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.MallOrderSkuRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.OrderSettleInfoRespDto;
import com.linzi.pitpat.api.mananger.MallOrderManager;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.constant.enums.PayConstant;
import com.linzi.pitpat.data.mallservice.biz.MallOrderBizService;
import com.linzi.pitpat.data.mallservice.converter.api.OrderLogisticsConverter;
import com.linzi.pitpat.data.mallservice.dto.api.request.OrderInfoRequestDto;
import com.linzi.pitpat.data.mallservice.dto.api.request.OrderLogisticsQueryReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderAmountReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderCancelReasonReq;
import com.linzi.pitpat.data.mallservice.dto.request.OrderCheckOutReqDto;
import com.linzi.pitpat.data.mallservice.dto.request.OrderInfoReq;
import com.linzi.pitpat.data.mallservice.dto.request.OrderListRequest;
import com.linzi.pitpat.data.mallservice.dto.response.MallOrderPickCouponResponseDto;
import com.linzi.pitpat.data.mallservice.dto.response.OrderAmountRespDto;
import com.linzi.pitpat.data.mallservice.dto.response.PayMethodDto;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.manager.MallOrderLogisticsManager;
import com.linzi.pitpat.data.mallservice.manager.PaymentConfigManager;
import com.linzi.pitpat.data.mallservice.model.entity.OrderLogisticsList;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsOrderEntity;
import com.linzi.pitpat.data.mallservice.model.query.OrderLogisticsQuery;
import com.linzi.pitpat.data.mallservice.model.query.OrderQuery;
import com.linzi.pitpat.data.mallservice.model.vo.OrderDetailVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderListVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderLogisticsListVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderSkuVo;
import com.linzi.pitpat.data.mallservice.model.vo.PayOrderDetailVo;
import com.linzi.pitpat.data.mallservice.service.OrderLogisticsListService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商城订单
 */
@RestController
@Slf4j
@RequestMapping("/app/mallOrder")
@RequiredArgsConstructor
public class MallOrderController extends BaseAppController {

    private final MallOrderManager mallOrderManager;
    private final ZnsOrderService orderService;
    private final OrderLogisticsListService logisticsListService;
    private final OrderLogisticsConverter logisticsConverter;
    private final MallOrderBizService mallOrderBizService;
    private final MallOrderLogisticsManager mallOrderLogisticsManager;
    private final ISysConfigService iSysConfigService;
    private final PaymentConfigManager paymentConfigManager;

    /**
     * 订单sku检查请求参数（进入结算页前请求）
     */
    @PostMapping("/checkOut")
    public Result<Boolean> checkOut(@RequestBody OrderCheckOutReqDto req) {
        for (OrderSkuVo orderSkuVo : req.getSkuList()) {
            mallOrderManager.checkStatusAndStock(orderSkuVo,getUserId());
        }
        return CommonResult.success(true);
    }
    /**
     * 订单sku检查请求参数（进入结算页前请求）
     * 组合商品中衍生品校验
     * @since 4.8.0
     */
    @PostMapping("/derivatives/checkOut")
    public Result<BatchAddShoppingCartResp> derivativesCheckOut(@RequestBody BatchAddShoppingCartReq req) {
        return CommonResult.success(mallOrderManager.batchCheckStatusAndStock(req.getAddShoppingCartReqList(), getUserId()));
    }


    /**
     * 结算页信息
     */
    @PostMapping("/settleInfo")
    public Result<OrderSettleInfoRespDto> settleInfo(@RequestBody OrderSettleInfoReqDto req) {
        String languageCode = getLanguageCode();
        OrderSettleInfoRespDto resp = mallOrderManager.settleInfo(req, languageCode, getLoginUser().getId(), getAppVersion(),false);
        return CommonResult.success(resp);
    }
    /**
     * 结算页信息-投流接口，无验签
     */
    @PostMapping("/settleInfo/surrender")
    public Result<OrderSettleInfoRespDto> settleInfoSurrender(@RequestBody OrderSettleInfoReqDto req) {
        String languageCode = getLanguageCode();
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        OrderSettleInfoRespDto resp = mallOrderManager.settleInfo(req, languageCode, user.getId(), getAppVersion(), true);
        String h5SurrenderTestUser = iSysConfigService.selectConfigByKey("h5_surrender_test_user");
        List<Long> userIdList = JsonUtil.readList(h5SurrenderTestUser, Long.class);
        if (!CollectionUtils.isEmpty(userIdList) && userIdList.contains(user.getId())) {
            String payTypeTest = iSysConfigService.selectConfigByKey("h5_surrender_pay_type_test");
            List<Integer> payTypeTestList = JsonUtil.readList(payTypeTest, Integer.class);
            List<PayMethodDto> list = paymentConfigManager.getCanusePayMethod(I18nConstant.CountryCodeEnum.US.getCode(),payTypeTestList,true);
            // 测试用户
            resp.setPayMethodDtos(list);
            resp.setPaymentTypes(list.stream().map(PayMethodDto::getPayName).collect(Collectors.toList()));
        } else {
            if (CollectionUtils.isEmpty(userIdList) || !userIdList.contains(user.getId())) {
                String payType = iSysConfigService.selectConfigByKey("h5_surrender_pay_type");
                List<Integer> payTypeList = JsonUtil.readList(payType, Integer.class);
                List<PayMethodDto> list = paymentConfigManager.getCanusePayMethod(I18nConstant.CountryCodeEnum.US.getCode(),payTypeList,true);
                // 正式用户
                resp.setPayMethodDtos(list);
                resp.setPaymentTypes(list.stream().map(PayMethodDto::getPayName).collect(Collectors.toList()));
            }
        }
        return CommonResult.success(resp);
    }

    /**
     * 计算订单总金额
     */
    @PostMapping("/calculateOrderTotalAmount")
    public Result<OrderAmountRespDto> calculateOrderTotalAmount(@RequestBody OrderAmountReqDto req) {
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        OrderAmountRespDto resp = mallOrderManager.calculateOrderTotalAmount(req, user);
        return CommonResult.success(resp);
    }
    /**
     * 计算订单总金额-投流接口，无验签
     * @since 4.7.2
     */
    @PostMapping("/calculateOrderTotalAmount/surrender")
    public Result<OrderAmountRespDto> calculateOrderTotalAmountSurrender(@RequestBody OrderAmountReqDto req) {
        ZnsUserEntity user = getLoginUserOrDefaultUser();
        OrderAmountRespDto resp = mallOrderManager.calculateOrderTotalAmount(req, user);
        return CommonResult.success(resp);
    }

    /**
     * 下单选择优惠券列表
     */
    @PostMapping("/pickCouponList")
    public Result<MallOrderPickCouponResponseDto> pickCouponList(@RequestBody OrderCheckOutReqDto req) {
        MallOrderPickCouponResponseDto resp = mallOrderManager.pickCouponList(req, getLoginUserOrDefaultUser());
        return CommonResult.success(resp);
    }


    /**
     * 创建订单
     */
    @PostMapping("/createOrder")
    public Result<CreateOrderRespDto> createOrder(@RequestBody CreateOrderReqDto req) {
        ZnsUserEntity user = getLoginUser();
        CreateOrderRespDto resp = mallOrderManager.createOrder(req, user);
        return CommonResult.success(resp);
    }


    /**
     */
    @PostMapping("/createOrderZero")
    public Result<CreateOrderRespDto> createOrderZero(@RequestBody CreateOrderZeroReqDto req) {
        ZnsUserEntity user = getLoginUser();
        CreateOrderRespDto resp = mallOrderManager.createOrderZero(req, user);
        return CommonResult.success(resp);
    }

    /**
     * 查询订单sku信息
     */
    @PostMapping("/listOrderSkuInfo")
    public Result<List<MallOrderSkuRespDto>> listOrderSkuInfo(@RequestBody OrderCheckOutReqDto req) {
        List<MallOrderSkuRespDto> resp = mallOrderManager.listOrderSkuInfo(req);
        return CommonResult.success(resp);
    }


    /**
     * 订单列表
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public Result<Page<OrderListVo>> list(@RequestBody OrderListRequest request) {
        ZnsUserEntity user = getLoginUser();
        String languageCode = getLanguageCode();
        return CommonResult.success(mallOrderManager.orderList(request, user.getId(), languageCode, getAppVersion()));
    }

    /**
     * 订单详情
     *
     * @param req
     * @return
     */
    @PostMapping("/orderInfo")
    public Result<OrderDetailVo> orderInfo(@Validated @RequestBody OrderInfoRequestDto req) {
        String languageCode = getLanguageCode();
        return CommonResult.success(mallOrderManager.orderInfo(req.getOrderNo(), languageCode));
    }

    /**
     * 支付订单详情
     */
    @PostMapping("/payOrderInfo")
    public Result<PayOrderDetailVo> payOrderInfo(@Validated @RequestBody OrderInfoRequestDto req) {
        return CommonResult.success(mallOrderManager.payOrderInfo(req.getOrderNo()));
    }

    /**
     * 取消订单原因配置列表
     */
    @PostMapping("/cancelReasonList")
    public Result<List<String>> cancelReasonList() {
        List<String> resp = mallOrderManager.cancelReasonList(getLanguageCode());
        return CommonResult.success(resp);
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel")
    public Result<Boolean> cancel(@Validated @RequestBody OrderCancelReasonReq req) {
        boolean cancel = mallOrderManager.cancel(req.getOrderId(), req.getCancelReason());
        if (!cancel) {
            return CommonResult.fail(UserError.ORDER_CANCEL_FAIL.getCode(), I18nMsgUtils.getMessage("order.cancel.fail"));
        }
        return CommonResult.success(true);
    }

    /**
     * 确认收货
     *
     * @param req
     * @return
     */
    @PostMapping("/confirmReceive")
    public Result confirmReceive(@Validated @RequestBody OrderInfoReq req) {
        mallOrderBizService.confirmReceive(req.getOrderId());
        return CommonResult.success();
    }

    /**
     * 删除订单
     *
     * @param req
     * @return
     */
    @PostMapping("/delete")
    public Result delete(@Validated @RequestBody OrderInfoReq req) {
        orderService.deleteOrder(req.getOrderId());
        return CommonResult.success();
    }


    /**
     * 物流信息
     *
     * @param po
     * @return
     */
    @PostMapping("/query/logistics")
    public Result<List<OrderLogisticsListVo>> queryLogistic(@Validated @RequestBody OrderLogisticsQueryReqDto po) {
        OrderLogisticsQuery orderLogisticsQuery = logisticsConverter.toQuery(po);
        orderLogisticsQuery.setOrders(List.of(OrderItem.desc("logistics_time")));
        List<OrderLogisticsList> list = logisticsListService.findList(orderLogisticsQuery);
        return CommonResult.success(logisticsConverter.toVoList(list));
    }

    /**
     * 刷新物流信息
     *
     * @param po
     * @return
     */
    @PostMapping("/refresh/logistics")
    public Result<List<OrderLogisticsListVo>> refreshLogistic(@Validated @RequestBody OrderLogisticsQueryReqDto po) {
        List<OrderLogisticsListVo> list = mallOrderLogisticsManager.refreshLogistic(po);
        return CommonResult.success(list);
    }

    /**
     * H5投流订单
     */
    @PostMapping("/h5/surrender/OrderList")
    public Result<CreateOrderRespDto> H5SurrenderOrderList() {
        ZnsUserEntity user = getLoginUser();
        if (Objects.isNull(user)) {
            return CommonResult.success();
        }
        return CommonResult.success(mallOrderManager.H5SurrenderOrderList(user.getId()));
    }
}
