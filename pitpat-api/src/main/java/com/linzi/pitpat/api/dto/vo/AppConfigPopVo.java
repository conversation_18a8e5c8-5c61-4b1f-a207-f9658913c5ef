package com.linzi.pitpat.api.dto.vo;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class AppConfigPopVo {

    //主键
    private Long id;
    //弹窗类型：1-首页弹窗，2-提醒弹窗
    private Integer popType;
    //弹窗标题
    private String title;
    //弹窗内容
    private String content;
    //生效用户类型：1-全部用户，0-表格导入用户
    private String validUserType;
    //生效开始时间
    private ZonedDateTime validStartTime;
    //生效结束时间
    private ZonedDateTime validEndTime;
    //触发频次：0-仅一次，1-一天一次，2-一周一次，3-每次都触发
    private Integer triggerFrequency;
    //弹窗状态：-1-已结束，0-未开始，1-进行中
    private Integer popStatus;
    //弹窗图片
    private String pics;

    //跳转路径类别：1-无跳转，2-赛事，3-链接，4-h5链接，5-页面，6-课程 8：新赛事3.0
    private Integer redirectType;
    //活动赛事的类型
    private Integer runActivityType;
    //活动地址/l路由/路由id
    private String activityUrl;
    //关联的赛事ID
    private Long runActivityId;
    //页面/路由id
    private Long routeId;
    //课程id
    private Long courseId;
    //活动任务id
    private Long taskConfigId;
    //课程名称
    private String courseName;
    // 默认语言code
    private String defaultLangCode;

    //3.0赛事类型
    private String mainActivityType;
    // 跳转链接
    private String url;
    // 跳转链接参数
    private String jumpParam;

    private String advertisingImage;

    private Long homePagePopId;
}
