package com.linzi.pitpat.api.userservice.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.userservice.dto.request.LevelBenefitConfigRequestDto;
import com.linzi.pitpat.api.userservice.dto.request.UserLevelTaskCompleteRequestDto;
import com.linzi.pitpat.api.userservice.dto.request.UserLevelTaskExpClaimRequestDto;
import com.linzi.pitpat.api.userservice.dto.response.LevelBenefitConfigResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.LevelExpObtainNoteResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.UserLevelInfoResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.UserLevelTaskCompleteResponseDto;
import com.linzi.pitpat.api.userservice.manager.ExpManager;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.entity.dto.ExpTypeDto;
import com.linzi.pitpat.data.entity.dto.ExpUserDto;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.manager.api.UserTaskManager;
import com.linzi.pitpat.data.userservice.model.entity.ExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.ExpPop;
import com.linzi.pitpat.data.userservice.model.entity.ExpType;
import com.linzi.pitpat.data.userservice.model.entity.ExpUser;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ExpConfigService;
import com.linzi.pitpat.data.userservice.service.ExpPopService;
import com.linzi.pitpat.data.userservice.service.ExpTypeService;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.vo.UserExpDetailDto;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户经验控制器
 *
 * <AUTHOR>
 * @date 2021/3/29
 */
@RestController
@Slf4j
@RequestMapping({"/app/exp", "/h5/exp"})
public class ExpController extends BaseAppController {


    @Autowired
    private ExpConfigService expConfigService;


    @Autowired
    private ExpTypeService expTypeService;

    @Autowired
    private ExpUserService expUserService;

    @Autowired
    private ExpPopService expPopService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private UserLevelService userLevelService;

    @Resource
    private ExpManager expManager;
    @Resource
    private UserTaskManager userTaskManager;

    @RequestMapping("/init")
    public String init() {
        List<ExpConfig> config = expConfigService.selectAllExpConfig();
        int sum = 0;
        for (int i = 0; i < config.size(); i++) {
            ExpConfig expConfig = config.get(i);
            sum += expConfig.getV();
            expConfig.setLevel(i + 1);
            expConfig.setLv(sum);
            expConfigService.updateExpConfigById(expConfig);
        }
        return "success";
    }


    @PostMapping("/home")
    public Result type(@RequestBody ExpUserDto expUserDto) {
        List<ExpType> expTypes = expTypeService.selectExpTypeByGroupDescription();
        ZnsUserEntity znsUserEntity = getUser(expUserDto.getUserId(), expUserDto.getEmail());
        Map<String, Object> map = new HashMap<>();
        if (sysConfigService.enableUserNewLevel(znsUserEntity.getId())) {
            UserLevel userLevel = userLevelService.findByUserId(znsUserEntity.getId());
            map.put("level", userLevel.getLevel());
        } else {
            UserExpDetailDto expDetailDto = expUserService.getExpDetail(znsUserEntity.getUserExp());
            map.put("minV", expDetailDto.getExp() - expDetailDto.getMin());
            map.put("maxV", expDetailDto.getMax() - expDetailDto.getMin());
            map.put("level", expDetailDto.getLevel());
        }

        List<ExpTypeDto> expTypeDtos = new ArrayList<>();
        Map<Long, ExpPop> popMap = expPopService.selectExpPopMap();
        for (ExpType expType : expTypes) {
            ExpTypeDto expTypeDto = new ExpTypeDto();
            BeanUtils.copyProperties(expType, expTypeDto);
            ExpPop expPop = popMap.get(MapUtil.getLong(expType.getType()));
            expTypeDto.setCondition(expPop.getDescription());
            expTypeDtos.add(expTypeDto);
        }
        map.put("list", expTypeDtos);

        return CommonResult.success(map);
    }

    @PostMapping("/user/list")
    public Result userList(@RequestBody ExpUserDto expUserDto) {
        ZnsUserEntity znsUserEntity = getUser(expUserDto.getUserId(), expUserDto.getEmail());
        Map<Integer, ExpType> config = expTypeService.selectMapExpType();
        List<ExpUser> list = expUserService.selectExpUserByUserIdGmtCreateExpGTZero(znsUserEntity.getId(),
                DateUtil.startOfDate(DateUtil.addDays(ZonedDateTime.now(), -expUserDto.getDays())), 0);
        Map<String, List<ExpUser>> data = new LinkedHashMap<>();
        ZonedDateTime currentDate = ZonedDateTime.now();
        ZonedDateTime yesDay = DateUtil.addDays(currentDate, -1);
        for (ExpUser expUser : list) {
            String title = DateUtil.dateStr(expUser.getGmtCreate(), DateUtil.YYYY_MM_DD_DOT);
            if (DateUtil.isSameDay(currentDate, expUser.getGmtCreate())) {
                title = "Today";
            } else if (DateUtil.isSameDay(yesDay, expUser.getGmtCreate())) {
                title = "Yesterday";
            }
            ExpType expType = config.get(expUser.getType());
            List<ExpUser> expUsers = data.get(title);
            if (expUsers == null) {
                expUsers = new ArrayList<>();
            }
            expUser.setRemark(expType.getRemark());
            expUsers.add(expUser);
            data.put(title, expUsers);
        }
        return CommonResult.success(data);
    }


    /**
     * 新版等级页面（新等级开启后）
     *
     * @return
     */
    @PostMapping("/userLevel/info")
    public Result<UserLevelInfoResponseDto> userLevelInfo() {
        Long userId = getUserId();
        UserLevelInfoResponseDto dto = expManager.userLevelInfo(userId);
        dto.setNewUserTaskInfo(userTaskManager.findNewUserTaskInfo(userId, getAppType()));
        return CommonResult.success(dto);
    }


    /**
     * 一种权益详情
     *
     * @return
     */
    @PostMapping("/benefit/info")
    public Result<List<LevelBenefitConfigResponseDto>> benefitInfo(@RequestBody LevelBenefitConfigRequestDto requestDto) {
        Long userId = getUserId();
        List<LevelBenefitConfigResponseDto> list = expManager.benefitInfo(requestDto, userId);
        return CommonResult.success(list);
    }


    /**
     * 等级经验获取说明
     *
     * @return
     */
    @PostMapping("/level/obtainNote")
    public Result<LevelExpObtainNoteResponseDto> levelObtainNote() {
        Long userId = getUserId();
        LevelExpObtainNoteResponseDto dto = expManager.levelObtainNote(userId);
        return CommonResult.success(dto);
    }

    /**
     * 等级奖励领取
     *
     * @return
     */
    @PostMapping("/level/award/claim")
    public Result<Boolean> claimLevelAward() {
        Long userId = getUserId();
        Boolean flag = expManager.claimLevelAward(userId);
        return CommonResult.success(flag);
    }

    /**
     * 是否展示等级开启动画
     *
     * @return true-展示，false-不展示
     */
    @PostMapping("/level/action/open")
    public Result<Boolean> openLevelAction() {
        Long userId = getUserId();
        Boolean flag = expManager.openLevelAction(userId);
        return CommonResult.success(flag);
    }

    /**
     * 领取任务经验
     *
     * @param
     * @return
     */
    @PostMapping("/task/exp/claim")
    public Result<Boolean> claimTaskExp(@RequestBody UserLevelTaskExpClaimRequestDto requestDto) {
        Long userId = getUserId();
        Boolean flag = expManager.claimTaskExp(userId, requestDto.getTaskDetailId());
        return CommonResult.success(flag);
    }


    /**
     * 老用户发放新等级奖励
     *
     * @return true-发成功
     */
    @PostMapping("/level/new/award")
    public Result<Boolean> newLevelAward() {
        ZnsUserEntity loginUser = getLoginUser();
        Boolean flag = expManager.newLevelAward(loginUser);
        return CommonResult.success(flag);
    }


    /**
     * 完成任务
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/task/complete")
    public Result<UserLevelTaskCompleteResponseDto> completeTask(@RequestBody UserLevelTaskCompleteRequestDto requestDto) {
        Long userId = getUserId();
        Boolean flag = expManager.completeTask(userId, requestDto.getSubType());
        UserLevelTaskCompleteResponseDto responseDto = new UserLevelTaskCompleteResponseDto();
        responseDto.setFlag(flag);
        return CommonResult.success(responseDto);
    }


}
