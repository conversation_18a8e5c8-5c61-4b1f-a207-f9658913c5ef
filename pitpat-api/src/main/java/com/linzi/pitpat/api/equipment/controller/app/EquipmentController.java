package com.linzi.pitpat.api.equipment.controller.app;

import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.equipment.converter.TreadmilConverter;
import com.linzi.pitpat.api.equipment.converter.UserEquipmentConverter;
import com.linzi.pitpat.api.equipment.dto.request.TreadmillMeasureUnitRequestDto;
import com.linzi.pitpat.api.mananger.EquipmentInstallVideoManager;
import com.linzi.pitpat.api.userservice.manager.UserDeviceApiManager;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.RunDataBizService;
import com.linzi.pitpat.data.activityservice.service.PkChallengeBizService;
import com.linzi.pitpat.data.activityservice.strategy.rundata.EquipmentRunDataHandleStrategyFactory;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentCareBiz;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentFirmwareBiz;
import com.linzi.pitpat.data.equipmentservice.dto.api.request.EquipmentSportNoGenerateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.api.request.GameEquipmentSportNoGenerateRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.api.response.EquipmentInfoV2ResponseDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentAutoUpgradeRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentCareRequest;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentInfoRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentInstallVideosRequest;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentNoRequest;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentScheduleReqDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentVersionRequest;
import com.linzi.pitpat.data.equipmentservice.dto.response.DeviceVersionResp;
import com.linzi.pitpat.data.equipmentservice.dto.response.EquipmentCareNewRespDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.EquipmentCareRespDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.EquipmentInformRespDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.InstallUrlDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.InstallVideoRespDto;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.enums.EquipmentDeviceTypeEnum;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillConstant;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillMeasureEnum;
import com.linzi.pitpat.data.equipmentservice.manager.EquipmentManager;
import com.linzi.pitpat.data.equipmentservice.manager.TreadmillManager;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentActivateRecordDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentBrandConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityAuditDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentQualityDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentSpeedConfigDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillBuzzerSupport;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillSoundpackBind;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillUpgradeLog;
import com.linzi.pitpat.data.equipmentservice.model.entity.UserEquipmentShareDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsEquipmentProductionBatchEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.query.EquipmentSpeedConfigQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillBuzzerSupportQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.UserEquipmentShareQuery;
import com.linzi.pitpat.data.equipmentservice.model.vo.DeviceVersionUpVo;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentCareDetailsVO;
import com.linzi.pitpat.data.equipmentservice.model.vo.UserEquipmentVO;
import com.linzi.pitpat.data.equipmentservice.service.DeviceVersionService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentActivateRecordService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentBrandConfigService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigMoreService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityAuditService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentQualityService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentSpeedConfigService;
import com.linzi.pitpat.data.equipmentservice.service.RainmakerService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillBuzzerSupportService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackBindService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillUpgradeLogService;
import com.linzi.pitpat.data.equipmentservice.service.UserEquipmentShareService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.request.RunDataRequest;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.vo.runData.DataProcessingVo;
import com.linzi.pitpat.dto.UserEquipmentDto;
import com.linzi.pitpat.dto.request.UserEquipmentQueryRequestDto;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 设备数据处理
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/app/equipment",  "/game/equipment"})
@Slf4j
@RequiredArgsConstructor
public class EquipmentController extends BaseAppController {
    @Resource
    private ZnsTreadmillService treadmillService;
    @Resource
    private ZnsEquipmentProductionBatchService equipmentProductionBatchService;
    @Resource
    private RunDataBizService runDataBizService;
    @Resource
    private RainmakerService rainmakerService;
    @Resource
    private TreadmillUpgradeLogService treadmillUpgradeLogService;
    @Resource
    private ZnsUserEquipmentService znsUserEquipmentService;
    @Resource
    private EquipmentBrandConfigService equipmentBrandConfigService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Resource
    private TreadmillSoundpackBindService treadmillSoundpackBindService;

    @Resource
    private TreadmillBuzzerSupportService treadmillBuzzerSupportService;

    private final EquipmentConfigMoreService equipConfigMoreService;
    @Resource
    private TreadmilConverter treadmilConverter;
    @Resource
    private UserDeviceApiManager userDeviceApiManager;
    private final UserEquipmentConverter userEquipmentConverter;
    private final EquipmentActivateRecordService equipmentActivateRecordService;
    private final EquipmentQualityService equipmentQualityService;
    private final EquipmentQualityAuditService equipmentQualityAuditService;
    private final EquipmentConfigService equipmentConfigService;
    private final EquipmentCareBiz equipmentCareManager;
    private final EquipmentInstallVideoManager equipmentInstallVideoManager;
    private final EquipmentFirmwareBiz equipmentFirmwareBiz;

    private final ISysConfigService sysConfigService;
    private final PkChallengeBizService pkChallengeBizService;

    private final UserEquipmentShareService userEquipmentShareService;
//    @Value("${pitpat.api.mallH5Url}")
//    private String mallH5Url;

    private final EquipmentSpeedConfigService equipmentSpeedConfigService;
    private final TreadmillManager treadmillManager;

    @PostMapping("/speed/config")
    public Result<EquipmentSpeedConfigDo> getConfig(@RequestBody EquipmentSpeedConfigQuery query) {
        EquipmentSpeedConfigDo byQuery = equipmentSpeedConfigService.getConfig(query);
        return CommonResult.success(byQuery);
    }

    /**
     * 获取设备信息V2
     * @since 4.7.5
     */
    @PostMapping("/infoV2")
    public Result<EquipmentInfoV2ResponseDto> getTreadmillInfoV2(@RequestBody @Valid EquipmentInfoRequestDto request) {
        EquipmentInfoV2ResponseDto responseDto = treadmillManager.getTreadmillInfoV2(request,getLoginUser());
        return CommonResult.success(responseDto);
    }

    /**
     * 设置自动更新
     * @since 4.7.5
     */
    @PostMapping("/setAutoUpgrade")
    public Result<Void> setAutoUpgrade(@RequestBody @Valid EquipmentAutoUpgradeRequestDto request) {
        treadmillManager.setAutoUpgrade(request);
        return CommonResult.success();
    }

    /**
     * 获取设备信息
     *
     * @param request
     * @return
     */
    @PostMapping("/info")
    public Result<UserEquipmentVO> getTreadmillInfo(@RequestBody EquipmentNoRequest request) {
        String equipmentNo = request.getEquipmentNo();
        if (!StringUtils.hasText(equipmentNo)) {
            log.info("[getTreadmillInfo]---查询设备信息,equipmentNo={},设备号为空", equipmentNo);
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "please enter equipmentNo");
        }
        ZnsTreadmillEntity treadmillEntity = treadmillService.findByUniqueCode(equipmentNo.trim());
        if (Objects.isNull(treadmillEntity)) {
            log.info("[getTreadmillInfo]---查询设备信息,equipmentNo={},设备不存在", equipmentNo);
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), CommonError.SYSTEM_ERROR.getMsg());
        }
        equipmentNo = treadmillEntity.getUniqueCode();

        //绑定关系
        ZnsUserEquipmentEntity userEquipment = znsUserEquipmentService.selectByEquipmentNoUserId(equipmentNo, request.getUserId());
        if (userEquipment == null) {
            log.info("[getTreadmillInfo]---查询设备信息,equipmentNo={},用户未绑定设备", equipmentNo);
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), CommonError.SYSTEM_ERROR.getMsg());
        }

        //查询设备信息
        UserEquipmentVO equipmentVo = equipmentProductionBatchService.selectEquipmentVo(treadmillEntity.getBatchNumber());
        String configValue = sysConfigService.selectConfigByKey(ConfigKeyEnums.QUALITY_NOTICE_INFO.getCode());
        if (StringUtils.hasText(configValue)) {
            List<String> list = JsonUtil.readList(configValue, String.class);
            if (list.contains(treadmillEntity.getProductCode())) {
                equipmentVo.setQualityNotice(I18nMsgUtils.getMessage("quality.notice.info"));
            }
        }
        equipmentVo.setEquipmentNo(treadmillEntity.getUniqueCode());
        String bluetoothChipType = treadmillEntity.getFirmwareType();
        equipmentVo.setBluetoothChipType(bluetoothChipType);
        equipmentVo.setBluetoothMac(treadmillEntity.getBluetoothMac());
        equipmentVo.setEquipmentName(userEquipment.getEquipmentName());
        equipmentVo.setEquipmentAddress(userEquipment.getEquipmentAddress());
        equipmentVo.setEquipmentModel(userEquipment.getEquipmentModel());
        equipmentVo.setPrintId(treadmillEntity.getPrintId());
        if (StringUtils.hasText(userEquipment.getEquipmentModel())) {
            EquipmentConfig equipmentConfig = equipmentConfigService.findByEquipmentInfo(userEquipment.getEquipmentModel());
            if (Objects.nonNull(equipmentConfig)) {
                equipmentVo.setModelRemark(equipmentConfig.getModelRemark());
            }
        }
        //型号展示重置
        String equipmentModel = equipConfigMoreService.findEquipmentModel(equipmentVo.getEquipmentModel(), treadmillEntity.getBrand());
        equipmentVo.setEquipmentModel(equipmentModel);
        equipmentVo.setEquipmentVersion(userEquipment.getEquipmentVersion());
        equipmentVo.setMeasureUnit(treadmillEntity.getMeasureUnit());
        equipmentVo.setIsSysSet(treadmillEntity.getIsSysSet());
        //预览图
        EquipmentBrandConfig equipmentBrandConfig = equipmentBrandConfigService.selectEquipmentBrandConfigByBrand(treadmillEntity.getBrand());
        if (equipmentBrandConfig != null) {
            equipmentVo.setPreviewImage(equipmentBrandConfig.getPreviewImage());
        }
        TreadmillSoundpackBind treadmillSoundpackBind = treadmillSoundpackBindService.getByEquipmentNo(equipmentNo);
        if (Objects.nonNull(treadmillSoundpackBind)) {
            equipmentVo.setSoundpackVersion(treadmillSoundpackBind.getVersion());
        }
        equipmentVo.setIsSupportBuzzerSound(false);
        equipmentVo.setIsSupportOtherSound(false);
        if ((DeviceConstant.FirmwareTypeEnum.XZX_SELF.getCode().equals(bluetoothChipType)
                || DeviceConstant.FirmwareTypeEnum.XZX_PURCHASE.getCode().equals(bluetoothChipType))
                && Objects.nonNull(userEquipment.getEquipmentVersion()) && userEquipment.getEquipmentVersion() >= 30
                && DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getCode().equals(equipmentVo.getEquipmentMainType())) {
            equipmentVo.setIsSupportOtherSound(true);
        }
        if (StringUtils.hasText(treadmillEntity.getProductCode())) {
            TreadmillBuzzerSupportQuery query = TreadmillBuzzerSupportQuery.builder().productCode(treadmillEntity.getProductCode()).build();
            TreadmillBuzzerSupport treadmillBuzzerSupport = treadmillBuzzerSupportService.getByQuery(query);
            if (Objects.nonNull(treadmillBuzzerSupport)
                    && Objects.nonNull(treadmillBuzzerSupport.getSupportTime())
                    && treadmillEntity.getCreateTime().after(treadmillBuzzerSupport.getSupportTime())) {
                //在支持时间之后才支持蜂鸣器
                equipmentVo.setIsSupportBuzzerSound(true);
            }
        }
        //公英制支持查询
        ZnsEquipmentProductionBatchEntity productionBatch = equipmentProductionBatchService.getByBatchNumber(treadmillEntity.getBatchNumber());
        if (Objects.nonNull(productionBatch)) {
            equipmentVo.setMeasuringSystem(productionBatch.getMeasuringSystem());
            if (Objects.nonNull(productionBatch.getResistanceNum())) {
                // 档位设置
                equipmentVo.setMinResistanceNum(1);
                equipmentVo.setMaxResistanceNum(productionBatch.getResistanceNum());
            }
        }
        //查询设备质保结束时间
        EquipmentQualityDo equipmentQualityDo = equipmentQualityService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (equipmentQualityDo != null) {
            equipmentVo.setQualityEndTime(equipmentQualityDo.getQualityEndTime());
        }
        //查询设备激活方式
        EquipmentActivateRecordDo activateRecordDo = equipmentActivateRecordService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (activateRecordDo != null) {
            equipmentVo.setActivateType(activateRecordDo.getActivateType());
        }

        //查询设备额外质保审核状态
        String listStatus = userDeviceApiManager.getAuditListStatus(treadmillEntity);
        EquipmentQualityAuditDo qualityAuditDo = equipmentQualityAuditService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
        if (qualityAuditDo != null && DeviceConstant.AuditStatusEnum.REJECT.code.equals(qualityAuditDo.getAuditStatus()) && qualityAuditDo.getLastSubmitStatus() == 2) {
            //最后一次失败用户未查看，还可以再看一次,但
            listStatus = DeviceConstant.AuditListStatusEnum.REJECT.code;
        }
        equipmentVo.setAuditStatus(listStatus);
        equipmentVo.setEquipmentNickName(StringUtils.hasText(treadmillEntity.getEquipmentNickName()) ? treadmillEntity.getEquipmentNickName() : treadmillEntity.getProductCode() + "-" + treadmillEntity.getBluetoothMac().substring(treadmillEntity.getBluetoothMac().length() - 4));
        equipmentVo.setEquipmentSearchName(userEquipment.getEquipmentName() + "-" + userEquipment.getEquipmentAddress());
        equipmentVo.setEquipmentMainType(treadmillEntity.getEquipmentMainType());
        // 设备人当前身份
        UserEquipmentShareDo equipmentShare = userEquipmentShareService.findByQuery(new UserEquipmentShareQuery()
                .setTreadmillId(treadmillEntity.getId()).setUserId(getLoginUser().getId()));
        if (!Objects.isNull(equipmentShare)) {
            //没有分享过首次链接绑定
            equipmentVo.setUserType(equipmentShare.getUserType());
        }
        if ("R1".equals(equipmentModel)) {
            equipmentVo.setMeasuringSystem(TreadmillMeasureEnum.IMPERIAL_UNITS.getCode());//R1默认英制
            equipmentVo.setMeasureUnit(TreadmillMeasureEnum.IMPERIAL_UNITS.getCode());//R1默认英制
        }
        if (DeviceConstant.EquipmentMainTypeEnum.BICYCLE.code.equals(treadmillEntity.getEquipmentMainType())) {
            equipmentVo.setMeasuringSystem(treadmillEntity.getMeasureUnit());//脚踏使用设备的公英值替换批次支持的公英值
            equipmentVo.setMeasureUnit(treadmillEntity.getMeasureUnit());//脚踏使用设备的公英值
        }
        return CommonResult.success(equipmentVo);
    }


    /**
     * app数据上传
     *
     * @param data
     * @return
     */
    @PostMapping("/data")
    public Result<DataProcessingVo> data(@RequestBody RunDataRequest data) {
        if (Objects.isNull(data.getDataSource())) {
            data.setDataSource(0);
        }
        if (Objects.isNull(data.getDeviceType())) {
            data.setDeviceType(EquipmentDeviceTypeEnum.TREADMILL.getCode());
        }
        data.virtualDataTransform();
        Result<DataProcessingVo> result = EquipmentRunDataHandleStrategyFactory.getStrategyByDeviceType(data.getDeviceType()).primaryRunDataProcessing(data);
        if (Objects.nonNull(data.getTargetDetailId())) {
            pkChallengeBizService.saveSelfPkChallengeRecord(data.getId_no(), data.getTargetDetailId(), result.getData(), data.getDeviceType());
        }
        return result;
    }

    /**
     * 设备运动编号生成
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/generateSportNo")
    public Result<Integer> generateSportNo(@Validated @RequestBody EquipmentSportNoGenerateRequestDto requestDto) {
        Integer sportNo = equipmentManager.generateSportNo(requestDto);
        return CommonResult.success(sportNo);
    }

    /**
     * 游戏设备运动编号生成
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/generateGameSportNo")
    public Result<Integer> generateGameSportNo(@Validated @RequestBody GameEquipmentSportNoGenerateRequestDto requestDto) {
        Integer sportNo = equipmentManager.generateGameSportNo(requestDto);
        return CommonResult.success(sportNo);
    }

    /**
     * 获取设备升级状态校验是否可以升级
     *
     * @param request
     * @return
     */
    @PostMapping("/checkVersion")
    public Result<DeviceVersionResp> checkVersion(@RequestBody EquipmentVersionRequest request) {
        log.info("EquipmentController#checkVersion---------equipmentNo:{},入参：{}", request.getEquipmentNo(), request);
        //查询设备升级状态校验是否可以升级
        DeviceVersionResp resp = equipmentManager.checkTreadmillVersion(request, getLoginUser());
        return CommonResult.success(resp);
    }

    /**
     * 设备蓝牙版本升级
     *
     * @param request
     * @return
     */
    @PostMapping("/updateVersion")
    public Result updateVersion(@RequestBody EquipmentVersionRequest request) {
        log.info("EquipmentController#updateVersion---------equipmentNo:{},设备版本升级入参：{}", request.getEquipmentNo(), request);
        String equipmentNo = request.getEquipmentNo().trim();
        ZnsUserEntity loginUser = getLoginUser();
        ZnsTreadmillEntity treadmillEntity = treadmillService.findByUniqueCode(equipmentNo);
        if (treadmillEntity == null) {
            log.info("EquipmentController#updateVersion---------equipmentNo:{},设备不存在", equipmentNo);
            return CommonResult.success();
        }

        //查询设备的最新版本蓝牙固件
        DeviceVersionUpVo deviceVersionEntity = equipmentFirmwareBiz.selectLastVersionByTreadmill(treadmillEntity);
        if (deviceVersionEntity == null) {
            log.info("EquipmentController#updateVersion---------equipmentNo:{},没有最新版本", equipmentNo);
            return CommonResult.success();
        }
        Integer version = deviceVersionEntity.getVersion();
        //非设备信息页推送im消息
        if (Objects.equals(version, request.getCurrentVersion())) {
            return CommonResult.success();
        }
        request.setEquipmentNo(treadmillEntity.getUniqueCode());
        String ota_image_id = deviceVersionEntity.getOtaImageId();
        String versionDesc = deviceVersionEntity.getVersionDesc();
        String request_status = "failure";
        String otaJobId = "";
        try {
            //升级固件
            String pre = treadmillEntity.getRId() + "_";
            String otaJobName = NanoId.randomNanoId();
            log.info("EquipmentController#updateVersion---------equipmentNo:{},升级固件接口入参,rId：{}，ota_image_id：{},version：{},otaJobName：{}", equipmentNo, treadmillEntity.getRId(), ota_image_id, version, otaJobName);
            Map<String, Object> res = rainmakerService.otaJob(treadmillEntity.getRId(), ota_image_id, version, otaJobName);
            log.info("EquipmentController#updateVersion---------equipmentNo:{},升级固件接口出参：{}", equipmentNo, res);
            request_status = MapUtil.getString(res.get("status"));
            // 重复发送，确保到达
            if ("failure".equals(request_status)) {
                String description = MapUtil.getString(res.get("description"));
                if ("Unauthorized".equals(description)) {
                    log.info("EquipmentController#updateVersion---------equipmentNo:{},升级固件接口第二次入参,rId：{}，ota_image_id：{},version：{},otaJobName：{}", equipmentNo, treadmillEntity.getRId(), ota_image_id, version, otaJobName);
                    res = rainmakerService.otaJob(treadmillEntity.getRId(), ota_image_id, version, otaJobName);
                    log.info("EquipmentController#updateVersion---------equipmentNo:{},升级固件接口第二次出参：{}", equipmentNo, res);
                    request_status = MapUtil.getString(res.get("status"));
                }
            }
            otaJobId = MapUtil.getString(res.get("ota_job_id"));
        } catch (Exception e) {
            log.info("EquipmentController#updateVersion---------equipmentNo:" + equipmentNo + ",升级固件接口异常：", e);
        }

        //添加升级记录
        if (!"failure".equals(request_status)) {
            TreadmillUpgradeLog treadmillUpgradeLog = treadmillUpgradeLogService.addUpgradeLog(equipmentNo, request.getCurrentVersion(), version, versionDesc, loginUser.getId(), "active", otaJobId, BigDecimal.ZERO, deviceVersionEntity.getId());
            equipmentManager.endSchedule(treadmillUpgradeLog);
            // 更新跑步机的版本信息
            ZnsUserEquipmentEntity znsUserEquipmentEntity = znsUserEquipmentService.selectByEquipmentNoUserId(equipmentNo, loginUser.getId());
            znsUserEquipmentEntity.setEquipmentVersion(request.getCurrentVersion());
            znsUserEquipmentService.updateUserEquipment(znsUserEquipmentEntity);

            ZnsTreadmillEntity entity = treadmillService.findByUniqueCode(equipmentNo);
            if (entity != null) {
                ZnsTreadmillEntity updateEntity = new ZnsTreadmillEntity();
                updateEntity.setId(entity.getId());
                updateEntity.setEquipmentVersion(request.getCurrentVersion());
                treadmillService.update(updateEntity);
            }
        }
        return CommonResult.success();
    }


    /**
     * 上报OTA模块版本
     *
     * @param req
     * @return
     */
    @PostMapping("/reportInform")
    public Result<Boolean> reportInform(@RequestBody @Valid EquipmentInformRespDto req) {
        //上报OTA模块版本
        equipmentManager.reportInform(req);
        return CommonResult.success(true);
    }


    /**
     * 上报OTA升级进度
     *
     * @param req
     * @return
     */
    @PostMapping("/reportSchedule")
    public Result<Boolean> reportSchedule(@RequestBody @Valid EquipmentScheduleReqDto req) {
        //上报OTA升级进度
        equipmentManager.reportSchedule(req, getLoginUser().getId());
        return CommonResult.success(true);
    }

    /**
     * 更新设备遥控器状态
     *
     * @param req
     * @return
     */
    @PostMapping("/updateRcStatus")
    public Result updateRcStatus(@RequestBody EquipmentNoRequest req) {
        String equipmentNo = req.getEquipmentNo().trim();
        log.info("EquipmentController#updateRcStatus--------更新设备遥控器状态,equipmentNo:{}，factoryRcStatus:{}，", req.getEquipmentNo(), req.getFactoryRcStatus());
        if (!StringUtils.hasText(equipmentNo)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "please enter equipmentNo");
        }
        ZnsTreadmillEntity treadmillEntity = treadmillService.selectTreadmillLikeByUniqueCodeOrByBluetoothMac(equipmentNo);
        if (Objects.isNull(treadmillEntity)) {
            return CommonResult.fail(CommonError.SYSTEM_ERROR.getCode(), "treadmill not register");
        }
        TreadmillConstant.FactoryRcStatusEnum rcStatusEnum = TreadmillConstant.FactoryRcStatusEnum.findByCode(req.getFactoryRcStatus());
        Integer factoryRcStatus = Objects.isNull(rcStatusEnum) ? TreadmillConstant.FactoryRcStatusEnum.RC_STATUS_1.code : rcStatusEnum.code;
        //更新设备遥控器绑定状态
        equipmentManager.updateRcStatusByTreadmillId(treadmillEntity.getId(), factoryRcStatus);
        return CommonResult.success();
    }

    /**
     * 修改设备公英制
     *
     * @param request
     * @return
     */
    @PostMapping("/measureUnit")
    public Result updateMeasurementUnit(@RequestBody TreadmillMeasureUnitRequestDto request) {
        ZnsTreadmillEntity znsTreadmillEntity = treadmilConverter.toDo(request);
        treadmillService.updateMeasurementUnit(znsTreadmillEntity);
        return CommonResult.success();
    }

    /**
     * 获取用户设备列表
     *
     * @param request
     * @return
     */
    @PostMapping("/getUserEquipmentList")
    public Result<List<UserEquipmentDto>> getUserEquipmentList(@RequestBody UserEquipmentQueryRequestDto request) {
        List<ZnsUserEquipmentEntity> znsUserEquipmentEntities = znsUserEquipmentService.selectByUserId(request.getUserId());
        return CommonResult.success(userEquipmentConverter.toDtoList(znsUserEquipmentEntities));
    }

    /**
     * 设备保养信息
     *
     * @param request
     * @return
     */
    @PostMapping("/care/info")
    public Result<List<EquipmentCareRespDto>> equipmentCareInfo(@RequestBody EquipmentNoRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        return CommonResult.success(equipmentCareManager.equipmentCareInfo(loginUser, request.getEquipmentNo()));
    }

    /**
     * 设备保养信息(new)
     *
     * @param request
     * @return
     */
    @PostMapping("/care/info/new")
    public Result<EquipmentCareNewRespDto> equipmentCareInfoNew(@RequestBody EquipmentNoRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        EquipmentCareNewRespDto equipmentCareNewRespDto = equipmentCareManager.equipmentCareInfoNew(loginUser, request.getEquipmentNo());
        return CommonResult.success(equipmentCareNewRespDto);
    }

    /**
     * 推送记录
     *
     * @param request
     * @return
     */
    @PostMapping("/im/history")
    public Result<List<EquipmentCareDetailsVO>> imHistory(@RequestBody EquipmentCareRequest request) {
        ZnsUserEntity user = getLoginUserNotNull();
        return CommonResult.success(equipmentCareManager.getEquipmentCareDetails(user.getId(), request.getProductCode()));
    }

    /**
     * 保养项目标识修改
     *
     * @param request
     * @return
     */
    @PostMapping("/care/flag")
    public Result<Void> careFlag(@RequestBody @Validated EquipmentCareRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        equipmentCareManager.careFlag(loginUser, request.getEquipmentNo(), request.getCareType());
        return CommonResult.success();
    }

    /**
     * 根据设备蓝牙获取安装视频url
     *
     * @param request
     * @return
     */
    @PostMapping("/install/url")
    public Result<InstallUrlDto> getInstallUrl(@RequestBody @Validated EquipmentInstallVideosRequest request) {
        InstallUrlDto installUrlDto = equipmentInstallVideoManager.getH5Url(request.getBluetoothMac());
        return CommonResult.success(installUrlDto);
    }


    /**
     * 根据设备蓝牙获取安装视频文件list
     *
     * @param request
     * @return
     */
    @PostMapping("/install/video/categories")
    public Result<InstallVideoRespDto> getInstallVideoList(@RequestBody @Validated EquipmentInstallVideosRequest request) {
        InstallVideoRespDto installVideoRespDto = equipmentInstallVideoManager.getInstallVideoList(request.getBluetoothMac());
        return CommonResult.success(installVideoRespDto);
    }
}



