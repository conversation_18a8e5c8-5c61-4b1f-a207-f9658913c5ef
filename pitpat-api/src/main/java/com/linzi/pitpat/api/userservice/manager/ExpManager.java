package com.linzi.pitpat.api.userservice.manager;

import com.linzi.pitpat.api.userservice.dto.request.GameLevelRunExpRequestDto;
import com.linzi.pitpat.api.userservice.dto.request.LevelBenefitConfigRequestDto;
import com.linzi.pitpat.api.userservice.dto.response.GameLevelRunExpResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.GameRunExperienceResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.LevelBenefitConfigResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.LevelExpObtainNoteResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.RunExpObtainResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.UserBenefitResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.UserExpTaskResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.UserLevelAwardRecordRespDto;
import com.linzi.pitpat.api.userservice.dto.response.UserLevelBasicResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.UserLevelInfoResponseDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.model.query.award.WearAwardDto;
import com.linzi.pitpat.data.awardservice.constant.enums.ScoreConstant;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.service.ActivityUserScoreService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.UserExpLevelBizService;
import com.linzi.pitpat.data.userservice.dto.event.UserExpSendEvent;
import com.linzi.pitpat.data.userservice.dto.response.UserLevelBenefitResponseDto;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserLevelAwardTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserLevelTaskStatusEnum;
import com.linzi.pitpat.data.userservice.model.entity.UserBenefitConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserBenefitI18n;
import com.linzi.pitpat.data.userservice.model.entity.UserExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserExpConfigI18n;
import com.linzi.pitpat.data.userservice.model.entity.UserExpDetail;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelAwardRecord;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelBenefitRel;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.model.entity.UserRelatedDo;
import com.linzi.pitpat.data.userservice.model.entity.UserTaskDetail;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.bo.UserExpIncreDto;
import com.linzi.pitpat.data.userservice.model.query.UserBenefitConfigQuery;
import com.linzi.pitpat.data.userservice.model.query.UserBenefitI18nQuery;
import com.linzi.pitpat.data.userservice.model.query.UserExpConfigI18nQuery;
import com.linzi.pitpat.data.userservice.model.query.UserExpConfigQuery;
import com.linzi.pitpat.data.userservice.model.vo.UserExpDetailStatVo;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import com.linzi.pitpat.data.userservice.service.UserBenefitConfigService;
import com.linzi.pitpat.data.userservice.service.UserBenefitI18nService;
import com.linzi.pitpat.data.userservice.service.UserExpConfigI18nService;
import com.linzi.pitpat.data.userservice.service.UserExpConfigService;
import com.linzi.pitpat.data.userservice.service.UserExpDetailService;
import com.linzi.pitpat.data.userservice.service.UserLevelAwardRecordService;
import com.linzi.pitpat.data.userservice.service.UserLevelBenefitRelService;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserRelatedService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserBizService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.vo.UserExpDetailDto;
import com.linzi.pitpat.data.vo.UserExpDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class ExpManager {

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private UserLevelService userLevelService;

    @Resource
    private UserLevelRuleService userLevelRuleService;

    @Resource
    private ZnsUserService znsUserService;

    @Resource
    private CouponService couponService;

    @Resource
    private WearsService wearsService;

    @Resource
    private UserLevelAwardRecordService userLevelAwardRecordService;

    @Resource
    private UserBenefitConfigService userBenefitConfigService;

    @Resource
    private UserBenefitI18nService userBenefitI18nService;

    @Resource
    private UserLevelBenefitRelService userLevelBenefitRelService;

    @Resource
    private UserExpConfigService userExpConfigService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ActivityUserScoreService activityUserScoreService;

    @Resource
    private UserWearsBagService userWearsBagService;

    @Resource
    private VipUserBizService vipUserbizService;

    @Resource
    private UserTaskDetailService userTaskDetailService;

    @Resource
    private UserExpConfigI18nService userExpConfigI18nService;

    @Resource
    private UserExpDetailService userExpDetailService;

    @Resource
    private UserExpLevelBizService userExpLevelBizService;

    @Autowired
    private ExpUserService expUserService;

    @Resource(name = "completeFutureExecutor")
    private ThreadPoolTaskExecutor completeFutureExecutor;

    @Resource
    private UserRelatedService userRelatedService;



    @Value("${level.award.amount.url:https://pitpat-oss.s3.us-east-2.amazonaws.com/202406/iLf16kl1he9G7901.png}")
    private String levelAwardAmountUrl;

    @Value("${level.award.score.url:https://pitpat-oss.s3.us-east-2.amazonaws.com/202406/iQe16kkZUtBo7069.png}")
    private String levelAwardScoreUrl;

    @Value("${level.award.member.url:https://pitpat-oss.s3.us-east-2.amazonaws.com/202406/iAe16kkZU6YM1539.png}")
    private String levelAwardMemberUrl;


    @Value("${user.newLevel.date:2024-08-20 00:00:00}")
    private String newLevelUserDate;
    @Resource
    private QueueMessageService queueMessageService;

    //用户首页经验弹窗
    public UserExpDto expPop(ZnsUserEntity loginUser, Boolean popFlag) {
        UserExpDto expDto = new UserExpDto();
        Long userId = loginUser.getId();
        if (sysConfigService.enableUserNewLevel(userId)) {
            UserLevel userLevel = userLevelService.findByUserId(userId);
            Integer level = userLevel.getLevel();
            UserExpDetailDto currentExpDetailDto = new UserExpDetailDto();
            currentExpDetailDto.setExp(userLevel.getExperience());
            currentExpDetailDto.setLevel(level);
            UserLevelRule currentRule = userLevelRuleService.findByLevel(level);
            currentExpDetailDto.setMin(currentRule.getExpLow());
            currentExpDetailDto.setMax(currentRule.getExpHigh());
            UserExpDetailDto historyExpDetailDto = new UserExpDetailDto();
            historyExpDetailDto.setExp(userLevel.getHistoryExperience());
            historyExpDetailDto.setLevel(userLevel.getHistoryLevel());
            UserLevelRule historyRule = userLevelRuleService.findByLevel(userLevel.getHistoryLevel());
            historyExpDetailDto.setMin(historyRule.getExpLow());
            historyExpDetailDto.setMax(historyRule.getExpHigh());
            expDto.setCurrentExp(currentExpDetailDto);
            expDto.setHistoryExp(historyExpDetailDto);
            if (level > userLevel.getHistoryLevel()) {
                // 只在升级的这一次展示
                fillUserNewBenefit(loginUser, expDto, level);
            }
            Integer exp = userLevel.getExperience() - userLevel.getHistoryExperience();
            boolean flag = popFlag && exp > 0;
            ZonedDateTime popTime = getAndSetPopTime(userId, flag);
            UserExpDetailStatVo userExpDetailStatVo = userExpDetailService.statUserExpByUserAndTime(userId, popTime);
            expDto.setExp(userExpDetailStatVo.getTotalExperience() + userExpDetailStatVo.getTotalMemberBenefit());
            expDto.setBasicExp(userExpDetailStatVo.getTotalExperience());
            expDto.setMemberBenefit(userExpDetailStatVo.getTotalMemberBenefit());
            String sysConfig = sysConfigService.selectConfigByKey(ConfigKeyEnums.NEW_LEVEL_UPPER_LIMIT.getCode());
            int levelUpper = Integer.parseInt(sysConfig);
            expDto.setMaxLevel(levelUpper);
            if (flag) {
                userLevelService.updateHistoryUserExpByUserId(userLevel.getExperience(), userId);
            }
        } else {

            expDto.setHistoryExp(expUserService.getExpDetail(loginUser.getHistoryUserExp()));
            expDto.setCurrentExp(expUserService.getExpDetail(loginUser.getUserExp()));
            if (popFlag) {
                znsUserService.updateHistoryUserExpByUserId(loginUser.getUserExp(), userId);
            }
        }
        return expDto;
    }

    /**
     * 获取并设置经验弹窗时间
     *
     * @param userId
     * @param flag
     * @return
     */
    @Nullable
    private ZonedDateTime getAndSetPopTime(Long userId, Boolean flag) {
        ZonedDateTime popTime = null;
        UserRelatedDo userRelatedDo = userRelatedService.findByUserId(userId);
        if (Objects.nonNull(userRelatedDo)) {
            popTime = userRelatedDo.getExpPopTime();
            if (flag) {
                userRelatedDo.setExpPopTime(ZonedDateTime.now());
                userRelatedService.update(userRelatedDo);
            }
        } else {
            if (flag) {
                UserRelatedDo userRelatedDo1 = new UserRelatedDo();
                userRelatedDo1.setUserId(userId);
                userRelatedDo1.setExpPopTime(ZonedDateTime.now());
                userRelatedService.create(userRelatedDo1);
            }
        }
        return popTime;
    }

    /**
     * 填充新增权益
     *
     * @param loginUser
     * @param expDto
     * @param level
     */
    private void fillUserNewBenefit(ZnsUserEntity loginUser, UserExpDto expDto, Integer level) {
        List<UserLevelBenefitResponseDto> benefitList = new ArrayList<>();
        List<UserLevelBenefitRel> userLevelBenefitRelList = userLevelBenefitRelService.findNewBenefitsByLevel(level);
        if (!CollectionUtils.isEmpty(userLevelBenefitRelList)) {
            List<Long> benefitIdList = userLevelBenefitRelList.stream().map(UserLevelBenefitRel::getBenefitId).collect(Collectors.toList());
            List<UserBenefitConfig> userBenefitConfigList = userBenefitConfigService.findList(UserBenefitConfigQuery.builder().configIdList(benefitIdList).build());
            List<UserBenefitI18n> benefitI18nList = userBenefitI18nService.findList(UserBenefitI18nQuery.builder().benefitIdList(benefitIdList).languageCode(loginUser.getLanguageCode()).build());
            Map<Long, String> benefitIdNameMap = benefitI18nList.stream().collect(Collectors.toMap(UserBenefitI18n::getBenefitId, UserBenefitI18n::getName));
            for (UserBenefitConfig userBenefitConfig : userBenefitConfigList) {
                UserLevelBenefitResponseDto levelBenefitResponseDto = BeanUtil.copyBean(userBenefitConfig, UserLevelBenefitResponseDto.class);
                levelBenefitResponseDto.setName(benefitIdNameMap.get(userBenefitConfig.getId()));
                benefitList.add(levelBenefitResponseDto);
            }
        }
        expDto.setBenefitList(benefitList);
    }


    public UserLevelInfoResponseDto userLevelInfo(Long userId) {
        UserLevelInfoResponseDto responseDto = new UserLevelInfoResponseDto();

        ZnsUserEntity znsUser = znsUserService.findById(userId);
        String languageCode = znsUser.getLanguageCode();
        UserLevel userLevel = userLevelService.findByUserId(userId);

        // 用户等级基础信息
        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> fillUserLevelBasic(responseDto, znsUser, userLevel), completeFutureExecutor);

        CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
            // 用户拥有的权益
            fillUserBenefit(responseDto, languageCode, userLevel);
            // 用户未解锁权益
            fillUnlockedBenefit(responseDto, languageCode, userLevel);
        }, completeFutureExecutor);

        // 用户待领取的等级奖励
        CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> fillUserUnclaimedLevelAward(responseDto, znsUser), completeFutureExecutor);

        // 用户任务
        CompletableFuture<Void> future4 = CompletableFuture.runAsync(() -> fillUserTaskInfo(responseDto, userId, languageCode), completeFutureExecutor);

        try {
            CompletableFuture.allOf(future1, future2, future3, future4).get();
        } catch (Exception e) {
            log.error("查询用户等级信息异常，userId:{},异常：", userId, e);
        }
        return responseDto;
    }


    /**
     * 获取等级权益信息
     *
     * @param languageCode
     * @param userLevel
     * @param flag
     * @return
     */
    private List<UserBenefitResponseDto> getUserBenefitResponseDtoList(String languageCode, UserLevel userLevel, Boolean flag) {
        List<UserBenefitResponseDto> userBenefitList = new ArrayList<>();
        List<UserLevelBenefitRel> levelBenefitRelList = userLevelBenefitRelService.findByLevel(userLevel.getLevel(), flag);
        if (!CollectionUtils.isEmpty(levelBenefitRelList)) {
            List<Long> benefitIdList = levelBenefitRelList.stream().map(UserLevelBenefitRel::getBenefitId).collect(Collectors.toList());
            UserBenefitConfigQuery benefitConfigQuery = UserBenefitConfigQuery.builder().configIdList(benefitIdList).build();
            List<UserBenefitConfig> userBenefitConfigList = userBenefitConfigService.findList(benefitConfigQuery);
            Map<Long, UserBenefitConfig> benefitConfigMap = userBenefitConfigList.stream().collect(Collectors.toMap(UserBenefitConfig::getId, v -> v, (v1, v2) -> v1));
            UserBenefitI18nQuery benefitI18nQuery = UserBenefitI18nQuery.builder().benefitIdList(benefitIdList).languageCode(languageCode).build();
            List<UserBenefitI18n> benefitI18nList = userBenefitI18nService.findList(benefitI18nQuery);
            Map<Long, String> i18nMap = benefitI18nList.stream().collect(Collectors.toMap(UserBenefitI18n::getBenefitId, UserBenefitI18n::getName, (v1, v2) -> v1));
            for (UserLevelBenefitRel levelBenefitRel : levelBenefitRelList) {
                UserBenefitResponseDto userBenefitResponseDto = BeanUtil.copyBean(benefitConfigMap.get(levelBenefitRel.getBenefitId()), UserBenefitResponseDto.class);
                userBenefitResponseDto.setLevel(levelBenefitRel.getLevel());
                userBenefitResponseDto.setName(i18nMap.get(levelBenefitRel.getBenefitId()));
                userBenefitList.add(userBenefitResponseDto);
            }
        }
        return userBenefitList;
    }


    private void fillUserUnclaimedLevelAward(UserLevelInfoResponseDto responseDto, ZnsUserEntity znsUser) {
        List<UserLevelAwardRecordRespDto> userLevelAwardRecordList = new ArrayList<>();
        List<UserLevelAwardRecord> awardRecordList = userLevelAwardRecordService.findByUserId(znsUser.getId());
        if (!CollectionUtils.isEmpty(awardRecordList)) {
            // 金额
            Integer amount = awardRecordList.stream().filter(e -> UserLevelAwardTypeEnum.CASH.getCode().equals(e.getAwardType())).collect(Collectors.summingInt(UserLevelAwardRecord::getAwardValue));
            if (Objects.nonNull(amount) && amount > 0) {
                UserLevelAwardRecordRespDto awardRecord = new UserLevelAwardRecordRespDto();
                awardRecord.setAwardType(UserLevelAwardTypeEnum.CASH.getCode());
                awardRecord.setAwardImage(levelAwardAmountUrl);
                awardRecord.setAwardValue(new BigDecimal(amount).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString());
                userLevelAwardRecordList.add(awardRecord);
            }
            // 积分
            Integer points = awardRecordList.stream().filter(e -> UserLevelAwardTypeEnum.POINTS.getCode().equals(e.getAwardType())).collect(Collectors.summingInt(UserLevelAwardRecord::getAwardValue));
            if (Objects.nonNull(points) && points > 0) {
                UserLevelAwardRecordRespDto awardRecord = new UserLevelAwardRecordRespDto();
                awardRecord.setAwardType(UserLevelAwardTypeEnum.POINTS.getCode());
                awardRecord.setAwardImage(levelAwardScoreUrl);
                awardRecord.setAwardValue(points.toString());
                userLevelAwardRecordList.add(awardRecord);
            }
            // 会员时长
            Integer membershipDuration = awardRecordList.stream().filter(e -> UserLevelAwardTypeEnum.MEMBERSHIP_DURATION.getCode().equals(e.getAwardType())).collect(Collectors.summingInt(UserLevelAwardRecord::getAwardValue));
            if (Objects.nonNull(membershipDuration) && membershipDuration > 0) {
                UserLevelAwardRecordRespDto awardRecord = new UserLevelAwardRecordRespDto();
                awardRecord.setAwardType(UserLevelAwardTypeEnum.MEMBERSHIP_DURATION.getCode());
                awardRecord.setAwardImage(levelAwardMemberUrl);
                awardRecord.setAwardValue(membershipDuration.toString());
                userLevelAwardRecordList.add(awardRecord);
            }
            // 券
            List<UserLevelAwardRecord> couponAwardList = awardRecordList.stream().filter(e -> UserLevelAwardTypeEnum.COUPON.getCode().equals(e.getAwardType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(couponAwardList)) {
                Map<Long, List<Integer>> collect = couponAwardList.stream().collect(Collectors.groupingBy(UserLevelAwardRecord::getEntityId, Collectors.mapping(UserLevelAwardRecord::getAwardValue, Collectors.toList())));
                for (Long entityId : collect.keySet()) {
                    UserLevelAwardRecordRespDto couponAward = new UserLevelAwardRecordRespDto();
                    couponAward.setAwardType(UserLevelAwardTypeEnum.COUPON.getCode());
                    couponAward.setEntityId(entityId);
                    Integer awardValue = collect.get(entityId).stream().reduce(Integer::sum).get();//数量统计
                    couponAward.setAwardValue(String.valueOf(awardValue));
                    Coupon coupon = couponService.selectCouponById(entityId);
                    couponAward.setAwardImage(coupon.getPicture());
                    userLevelAwardRecordList.add(couponAward);
                }
            }
            // 服装
            List<UserLevelAwardRecord> clothingAwardList = awardRecordList.stream().filter(e -> UserLevelAwardTypeEnum.CLOTHING.getCode().equals(e.getAwardType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(clothingAwardList)) {
                Map<Long, List<Integer>> collect = clothingAwardList.stream().collect(Collectors.groupingBy(UserLevelAwardRecord::getEntityId, Collectors.mapping(UserLevelAwardRecord::getAwardValue, Collectors.toList())));
                for (Long entityId : collect.keySet()) {
                    UserLevelAwardRecordRespDto clothingAward = new UserLevelAwardRecordRespDto();
                    clothingAward.setAwardType(UserLevelAwardTypeEnum.CLOTHING.getCode());
                    clothingAward.setEntityId(entityId);
                    Integer awardValue = collect.get(entityId).stream().reduce(Integer::sum).get();//数量统计
                    clothingAward.setAwardValue(String.valueOf(awardValue));
                    Wears wears = wearsService.selectWearsById(entityId);
                    if (Objects.equals(znsUser.getGender(), 2)) {
                        clothingAward.setAwardImage(wears.getWomenWearUrl());
                    } else {
                        clothingAward.setAwardImage(wears.getMenWearUrl());
                    }
                    userLevelAwardRecordList.add(clothingAward);
                }
            }
            responseDto.setUserLevelAwardRecordList(userLevelAwardRecordList);
        }
    }

    private void fillUserBenefit(UserLevelInfoResponseDto responseDto, String languageCode, UserLevel userLevel) {
        List<UserBenefitResponseDto> userBenefitList = getUserBenefitResponseDtoList(languageCode, userLevel, true);
        List<UserBenefitResponseDto> seriesBenefit = userBenefitList.stream().filter(e -> YesNoStatus.YES.getCode().equals(e.getSeriesBenefit())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(seriesBenefit)) {
            // 同系列权益仅展示最高权益
            Map<Integer, List<UserBenefitResponseDto>> benefitTypeMap = seriesBenefit.stream().collect(Collectors.groupingBy(UserBenefitResponseDto::getBenefitType));
            for (Integer benefitType : benefitTypeMap.keySet()) {
                List<UserBenefitResponseDto> userBenefitResponseList = benefitTypeMap.get(benefitType);
                userBenefitResponseList.sort(Comparator.comparing(UserBenefitResponseDto::getSeriesLevel));
                seriesBenefit.remove(userBenefitResponseList.get(userBenefitResponseList.size() - 1));
            }
            userBenefitList.removeAll(seriesBenefit);
        }
        userBenefitList.sort(Comparator.comparing(UserBenefitResponseDto::getBenefitType));
        responseDto.setUserBenefitList(userBenefitList);
    }


    private void fillUserTaskInfo(UserLevelInfoResponseDto responseDto, Long userId, String languageCode) {
        List<UserExpTaskResponseDto> userTaskList = new ArrayList<>();
        // 查询未完成 或 已完成待领取 任务
        List<UserTaskDetail> taskDetailList = userTaskDetailService.findShowTaskDetailList(userId);
        if (!CollectionUtils.isEmpty(taskDetailList)) {
            List<Long> configIdList = taskDetailList.stream().map(UserTaskDetail::getConfigId).collect(Collectors.toList());
            UserExpConfigQuery userExpConfigQuery = UserExpConfigQuery.builder().configIdList(configIdList).build();
            Map<Long, String> configIdImageMap = userExpConfigService.findList(userExpConfigQuery).stream().collect(Collectors.toMap(UserExpConfig::getId, UserExpConfig::getImageUrl));
            UserExpConfigI18nQuery expConfigI18nQuery = UserExpConfigI18nQuery.builder().configIdList(configIdList).langCode(languageCode).build();
            Map<Long, UserExpConfigI18n> collect = userExpConfigI18nService.findList(expConfigI18nQuery).stream().collect(Collectors.toMap(UserExpConfigI18n::getConfigId, e -> e));
            for (UserTaskDetail userTaskDetail : taskDetailList) {
                UserExpTaskResponseDto taskResponseDto = new UserExpTaskResponseDto();
                taskResponseDto.setTaskDetailId(userTaskDetail.getId());
                taskResponseDto.setObtainType(UserExpObtainTypeEnum.TASK.getCode());
                taskResponseDto.setObtainSubType(userTaskDetail.getSubType());
                taskResponseDto.setBasicExp(userTaskDetail.getExperience());
                taskResponseDto.setCalStyle(userTaskDetail.getCalStyle());
                taskResponseDto.setTaskStatus(userTaskDetail.getTaskStatus());
                UserExpConfigI18n userExpConfigI18n = collect.get(userTaskDetail.getConfigId());
                taskResponseDto.setTitle(userExpConfigI18n.getTitle());
                taskResponseDto.setDescription(userExpConfigI18n.getDescription());
                taskResponseDto.setImageUrl(configIdImageMap.get(userTaskDetail.getConfigId()));
                userTaskList.add(taskResponseDto);
            }
        }
        responseDto.setUserTaskList(userTaskList);
    }


    private void fillUnlockedBenefit(UserLevelInfoResponseDto responseDto, String languageCode, UserLevel userLevel) {
        List<UserBenefitResponseDto> unlockedBenefitList = getUserBenefitResponseDtoList(languageCode, userLevel, false);
        unlockedBenefitList.sort(Comparator.comparing(UserBenefitResponseDto::getLevel));
        responseDto.setUnlockedBenefitList(unlockedBenefitList);
    }

    private void fillUserLevelBasic(UserLevelInfoResponseDto responseDto, ZnsUserEntity znsUser, UserLevel userLevel) {
        UserLevelBasicResponseDto userLevelBasic = new UserLevelBasicResponseDto();
        Integer level = userLevel.getLevel();
        userLevelBasic.setLevel(level);
        userLevelBasic.setExperience(userLevel.getExperience());
        UserLevelRule currentLevelRule = userLevelRuleService.findByLevel(level);
        userLevelBasic.setLevelMinExperience(currentLevelRule.getExpLow());
        userLevelBasic.setLevelMaxExperience(currentLevelRule.getExpHigh());
        userLevelBasic.setLevelIconUrl(currentLevelRule.getIconUrl());
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.NEW_LEVEL_UPPER_LIMIT.getCode());
        int levelUpper = Integer.parseInt(sysConfig.getConfigValue());
        if (level >= levelUpper) {
            userLevelBasic.setNeedExp(0);
        } else {
            UserLevelRule nextLevelRule = userLevelRuleService.findByLevel(level + 1);
            userLevelBasic.setNeedExp(nextLevelRule.getExpLow() - userLevel.getExperience());
        }
        userLevelBasic.setMemberType(znsUser.getMemberType());
        ZonedDateTime date = DateTimeUtil.parse(newLevelUserDate);
        userLevelBasic.setIsNewLevelUser(true);
        if (date.isAfter(znsUser.getCreateTime())) {
            userLevelBasic.setIsNewLevelUser(false);
        }
        responseDto.setUserLevelBasic(userLevelBasic);
    }


    public List<LevelBenefitConfigResponseDto> benefitInfo(LevelBenefitConfigRequestDto requestDto, Long userId) {
        Integer benefitType = requestDto.getBenefitType();
        UserLevel userLevel = userLevelService.findByUserId(userId);
        Integer level = userLevel.getLevel();
        ZnsUserEntity znsUser = znsUserService.findById(userId);
        if (!I18nConstant.LanguageCodeEnum.en_US.getCode().equals(znsUser.getLanguageCode())
                && !I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(znsUser.getLanguageCode())) {
            znsUser.setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
        }
        List<UserBenefitResponseDto> userBenefitList = getUserBenefitResponseDtoList(znsUser.getLanguageCode(), userLevel, true);
        List<UserBenefitResponseDto> collect = userBenefitList.stream().filter(e -> e.getBenefitType().equals(benefitType)).collect(Collectors.toList());
        List<LevelBenefitConfigResponseDto> list = collect.stream().map(e -> {
            LevelBenefitConfigResponseDto dto = BeanUtil.copyBean(e, LevelBenefitConfigResponseDto.class);
            dto.setIsHold(level >= e.getLevel());
            return dto;
        }).collect(Collectors.toList());
        return list;
    }

    public LevelExpObtainNoteResponseDto levelObtainNote(Long userId) {
        ZnsUserEntity znsUser = znsUserService.findById(userId);
        if (!I18nConstant.LanguageCodeEnum.en_US.getCode().equals(znsUser.getLanguageCode())
                && !I18nConstant.LanguageCodeEnum.fr_CA.getCode().equals(znsUser.getLanguageCode())) {
            znsUser.setLanguageCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
        }
        LevelExpObtainNoteResponseDto responseDto = new LevelExpObtainNoteResponseDto();
        List<Integer> obtainTypeList = List.of(UserExpObtainTypeEnum.RUNNING_MILEAGE.getCode(), UserExpObtainTypeEnum.EVENT_EXPERIENCE.getCode());
        UserExpConfigQuery query = UserExpConfigQuery.builder().obtainTypeList(obtainTypeList).build();
        List<UserExpConfig> list = userExpConfigService.findList(query);

        List<UserExpConfig> runMileageConfigList = list.stream().filter(e -> UserExpObtainTypeEnum.RUNNING_MILEAGE.getCode().equals(e.getObtainType())).collect(Collectors.toList());
        List<RunExpObtainResponseDto> runMileageList = runMileageConfigList.stream().map(e -> {
            RunExpObtainResponseDto runMileageExp = BeanUtil.copyBean(e, RunExpObtainResponseDto.class);
            runMileageExp.setMemberTotalExp(e.getBasicExp() + e.getMemberBenefit());
            return runMileageExp;
        }).collect(Collectors.toList());
        responseDto.setExpList(runMileageList);

        List<UserExpConfig> eventConfigList = list.stream().filter(e -> UserExpObtainTypeEnum.EVENT_EXPERIENCE.getCode().equals(e.getObtainType())).collect(Collectors.toList());
        List<Long> configIdList = eventConfigList.stream().map(UserExpConfig::getId).collect(Collectors.toList());
        UserExpConfigI18nQuery expConfigI18nQuery = UserExpConfigI18nQuery.builder().langCode(znsUser.getLanguageCode()).configIdList(configIdList).build();
        Map<Long, UserExpConfigI18n> collect = userExpConfigI18nService.findList(expConfigI18nQuery).stream().collect(Collectors.toMap(UserExpConfigI18n::getConfigId, e -> e));
        List<RunExpObtainResponseDto> eventList = eventConfigList.stream().map(e -> {
            RunExpObtainResponseDto eventExp = BeanUtil.copyBean(e, RunExpObtainResponseDto.class);
            eventExp.setMemberTotalExp(e.getBasicExp() + e.getMemberBenefit());
            UserExpConfigI18n userExpConfigI18n = collect.get(e.getId());
            eventExp.setTitle(userExpConfigI18n.getTitle());
            eventExp.setDescription(userExpConfigI18n.getDescription());
            return eventExp;
        }).sorted(Comparator.comparing(RunExpObtainResponseDto::getObtainSubType)).collect(Collectors.toList());
        responseDto.setEventExpList(eventList);

        return responseDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean claimLevelAward(Long userId) {
        Boolean flag = false;
        String lockKey = String.format(RedisKeyConstant.USER_CLAIM_LEVEL_AWARD, userId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                // 3.8仅实现积分奖励的领取
                List<UserLevelAwardRecord> awardRecordList = userLevelAwardRecordService.findByUserId(userId);
                if (CollectionUtils.isEmpty(awardRecordList)) {
                    return false;
                }
                Integer points = awardRecordList.stream().filter(e -> UserLevelAwardTypeEnum.POINTS.getCode().equals(e.getAwardType())).collect(Collectors.summingInt(UserLevelAwardRecord::getAwardValue));
                if (points > 0) {
                    activityUserScoreService.increaseAmount(points, null, userId, null, 0, ScoreConstant.SourceTypeEnum.source_type_32.getType());
                }
                userLevelAwardRecordService.claimLevelAward(userId);
                flag = true;
            }
        } catch (Exception e) {
            log.error("用户等级奖励领取失败，userId:{}，异常：", userId, e);
            throw new BaseException(I18nMsgUtils.getMessage("level.award.claim.error"), ActivityError.LEVEL_AWARD_CLAIM_ERROR.getCode());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return flag;
    }


    public Boolean openLevelAction(Long userId) {
        Boolean flag = false;
        UserLevel userLevel = userLevelService.findByUserId(userId);
        if (YesNoStatus.NO.getCode().equals(userLevel.getIsShowAction())) {
            flag = true;
            userLevelService.updateShowAction(userId);
        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean newLevelAward(ZnsUserEntity loginUser) {
        Long userId = loginUser.getId();
        Boolean flag = userExpLevelBizService.getNewLevelAwardFlag(loginUser);
        if (flag) {
            WearAwardDto wearAwardDto = new WearAwardDto();
            wearAwardDto.setWearValue(62);
            wearAwardDto.setWearType(8);
            wearAwardDto.setWearImageUrl("https://pitpat-oss.s3.us-east-2.amazonaws.com/202407/is0BmZE1Mc2G06nc.png");
            userWearsBagService.sendOnceWear(userId, wearAwardDto);
            vipUserbizService.addSpecifiedDayMember(userId, 31);
        }
        return flag;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean claimTaskExp(Long userId, Long taskDetailId) {
        UserTaskDetail userTaskDetail = userTaskDetailService.findById(taskDetailId);
        if (!userId.equals(userTaskDetail.getUserId())) {
            log.error("领取经验该任务不是本人任务,userId:{},taskDetailId:{}", userId, taskDetailId);
            return false;
        }
        if (UserLevelTaskStatusEnum.COMPLETED.getCode().equals(userTaskDetail.getTaskStatus())
                && YesNoStatus.NO.getCode().equals(userTaskDetail.getAwardStatus())) {
            userTaskDetail.setAwardStatus(YesNoStatus.YES.getCode());
            userTaskDetail.setGmtModified(ZonedDateTime.now());
            userTaskDetailService.update(userTaskDetail);
            UserExpDetail userExpDetail = new UserExpDetail();
            userExpDetail.setConfigId(userTaskDetail.getConfigId());
            userExpDetail.setUserId(userId);
            userExpDetail.setExperience(userTaskDetail.getExperience());
            userExpDetail.setRefId(taskDetailId);
            userExpDetailService.insert(userExpDetail);
            userExpLevelBizService.sendUserExpAndAward(userId, userTaskDetail.getExperience());
            UserExpIncreDto increDto = new UserExpIncreDto();
            UserExpConfig userExpConfig = userExpConfigService.findById(userTaskDetail.getConfigId());
            increDto.addExpItem(userTaskDetail.getExperience(), userExpConfig);
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserExpSendEvent.getEventType(),UserExpSendEvent.of(userId,increDto));
            return true;
        }
        return false;
    }


    /**
     * 完成新手任务
     *
     * @param userId
     * @param subType
     * @return
     */
    public Boolean completeTask(Long userId, Integer subType) {
        boolean flag = userTaskDetailService.completeLevelTask(userId, subType, false);
        return flag;
    }


    public GameLevelRunExpResponseDto gameRunExpNotice(GameLevelRunExpRequestDto requestDto) {
        GameLevelRunExpResponseDto responseDto = new GameLevelRunExpResponseDto();
        Long userId = requestDto.getUserId();
        responseDto.setUserLevel(userLevelService.findByUserId(userId).getLevel());
        Integer memberType = znsUserService.findById(userId).getMemberType();
        List<Integer> obtainTypeList = List.of(UserExpObtainTypeEnum.RUNNING_MILEAGE.getCode());
        UserExpConfigQuery query = UserExpConfigQuery.builder().obtainTypeList(obtainTypeList).build();
        List<UserExpConfig> list = userExpConfigService.findList(query);
        List<GameRunExperienceResponseDto> expList = list.stream().map(e -> {
            GameRunExperienceResponseDto gameRunExperienceResponseDto = new GameRunExperienceResponseDto();
            gameRunExperienceResponseDto.setRunMileage(e.getObtainValue());
            gameRunExperienceResponseDto.setMileageExp(Objects.equals(memberType, 1) ? e.getBasicExp() + e.getMemberBenefit() : e.getBasicExp());
            return gameRunExperienceResponseDto;
        }).collect(Collectors.toList());
        responseDto.setExpList(expList);
        return responseDto;
    }


}
