package com.linzi.pitpat.api.mananger;

import com.linzi.pitpat.api.dto.response.vip.VipActivationRecordVo;
import com.linzi.pitpat.api.dto.response.vip.VipComboDto;
import com.linzi.pitpat.api.dto.response.vip.VipConfigVo;
import com.linzi.pitpat.api.dto.response.vip.VipInfoVo;
import com.linzi.pitpat.api.dto.response.vip.VipRightsInterestsDto;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.enums.VipPurchaseTypeEnum;
import com.linzi.pitpat.data.paymentservice.model.entity.PaymentGoodsDo;
import com.linzi.pitpat.data.paymentservice.service.PaymentGoodsService;
import com.linzi.pitpat.data.systemservice.model.entity.VipUserSubscribeRecord;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.model.entity.VipUserLogDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUser;
import com.linzi.pitpat.data.userservice.model.query.VipUserLogQuery;
import com.linzi.pitpat.data.userservice.model.query.VipUserQuery;
import com.linzi.pitpat.data.userservice.service.VipUserLogService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserSubscribeRecordService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class VipUserManager {
    @Autowired
    private ISysConfigService iSysConfigService;
    @Autowired
    private VipUserService vipUserService;
    @Autowired
    private PaymentGoodsService paymentGoodsService;
    @Autowired
    private VipUserSubscribeRecordService vipUserSubscribeRecordService;
    @Autowired
    private VipUserLogService vipUserLogService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * app购买vip。是否支持paypal
     */
    public static final String PAYPAL_ENABLE = "app.vip.paypal.enable";

    public VipConfigVo getVipConfig(ZnsUserEntity loginUser, Integer appType, Integer appVersion) {
        VipConfigVo vipConfigVo = new VipConfigVo();
        String value = iSysConfigService.selectConfigByKey("vip_rights_interests");
        List<VipRightsInterestsDto> rights = JsonUtil.readList(value, VipRightsInterestsDto.class);
        if (appVersion >= 4030) {
            rights.removeIf(k -> "unlock_battle_pass_milestone".equals(k.getRightsName()));
        }
        if (!CollectionUtils.isEmpty(rights)) {
            rights.forEach(s -> s.setRightsName(I18nMsgUtils.getMessage(s.getRightsName())));
        }
        vipConfigVo.setVipRightsInterests(rights);

        //是否有免费试用
        VipUserQuery build = VipUserQuery.builder().userId(loginUser.getId()).memberType(1).isDelete(0).build();
        VipUser vipUser = vipUserService.findByQuery(build);
        if (Objects.nonNull(vipUser)) {
            vipConfigVo.setIsFreeUsedForMonth(Objects.equals(vipUser.getIsTrialVip(), 1));
        } else {
            vipConfigVo.setIsFreeUsedForMonth(Boolean.FALSE);
        }
        List<PaymentGoodsDo> listByState = paymentGoodsService.findListByState();
        List<VipComboDto> vipComboDtos = getVipComboDtos(appType, listByState, vipUser);
        vipComboDtos.stream().filter(k -> VipPurchaseTypeEnum.PITPAT_SUBSCRIBE_YEAR.getCode().equals(k.getType())).findFirst().ifPresent(
                vipComboDto -> {
                    Long expireIn = getVipMemberDiscountExpireIn(loginUser.getEmailAddress());
                    if (expireIn != null) {
                        PaymentGoodsDo paymentGoodsDo = paymentGoodsService.findDiscountSubscribeYear();
                        if (paymentGoodsDo != null) {
                            vipComboDto.setOriginalAmount(new BigDecimal(vipComboDto.getAmount().intValue()));
                            vipComboDto.setAmount(paymentGoodsDo.getDiscountPrice());
                            vipComboDto.setDiscountDeadline(expireIn);
                            vipComboDto.setGoodsId(Math.toIntExact(paymentGoodsDo.getId()));
                            if (Objects.equals(appType, 1)) {
                                //1 = android
                                boolean contains = paymentGoodsDo.getGoodsGooglePlayDiscountCode().contains("!");
                                if (contains) {
                                    String[] split = paymentGoodsDo.getGoodsGooglePlayDiscountCode().split("!");
                                    vipComboDto.setGooglePlayCode(split[0]);
                                    vipComboDto.setGoogleSubscriptionId(split[1]);
                                }
                                if (Objects.nonNull(vipUser) && Objects.equals(vipUser.getIsTrialVip(), 1)) {
                                    //没有试用的商品
                                    vipComboDto.setPlanId(paymentGoodsDo.getGoodsPaypalActualDiscountCode());
                                } else {
                                    vipComboDto.setPlanId(paymentGoodsDo.getGoodsPaypalDiscountCode());
                                }
                            } else {
                                vipComboDto.setPlanId(paymentGoodsDo.getGoodsIosDiscountCode());
                            }
                        }

                    }
                }
        );


        vipConfigVo.setVipCombos(vipComboDtos);

        String s = iSysConfigService.selectConfigByKey(PAYPAL_ENABLE, true);
        vipConfigVo.setShowPaypal("1".equals(s));
        return vipConfigVo;
    }

    /**
     * 获取用户会员购买折扣倒计时
     *
     * @param emailAddress
     * @return
     */
    private Long getVipMemberDiscountExpireIn(String emailAddress) {
        String vipVisitCountKey = String.format("vip_member:discount:%s", emailAddress);
        // 获取访问计数器和折扣状态
        RMap<String, Long> vipVisitCountCache = redissonClient.getMap(vipVisitCountKey);

        if (!CollectionUtils.isEmpty(vipVisitCountCache)) {
            String result = String.join(",", vipVisitCountCache.entrySet().stream()
                    .map(item -> item.getKey() + ":" + item.getValue())
                    .collect(Collectors.toList()));
            log.info("vipMemberDiscount email={}, key={}, visitCount={}", emailAddress, vipVisitCountKey, result);
        }

        long expireIn = vipVisitCountCache.getOrDefault("expireIn", 0L) - ZonedDateTime.now().toEpochSecond();
        return expireIn > 0 ? expireIn : null;
    }

    public VipInfoVo getVipInfo(ZnsUserEntity user, Integer appType) {
        VipInfoVo vipInfoVo = new VipInfoVo();
        vipInfoVo.setUserName(user.getFirstName());
        if (UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code.equals(user.getEmailType())) {
            //只有真实账号才返回游戏
            vipInfoVo.setEmail(user.getEmailAddressEn());
        }
        vipInfoVo.setHeadPortrait(user.getHeadPortrait());
        vipInfoVo.setMemberType(user.getMemberType());
        VipUser vipUser = vipUserService.selectActivationVipByUserId(user.getId());
        List<PaymentGoodsDo> listByState = paymentGoodsService.findListByState();
        List<VipComboDto> vipComboDtos = getVipComboDtos(appType, listByState, vipUser);
        if (Objects.nonNull(vipUser)) {
            List<VipUserSubscribeRecord> listByUser = vipUserSubscribeRecordService.findActivationListByUser(user.getId());
            if (!CollectionUtils.isEmpty(listByUser)) {
                listByUser = listByUser.stream().filter(s -> Objects.nonNull(s.getSubscribeEnd()) && ZonedDateTime.now().isBefore(s.getSubscribeEnd()) && Objects.nonNull(s.getVipEndTime()) && ZonedDateTime.now().isBefore(s.getVipEndTime())).collect(Collectors.toList());
                //连续包月和包年，已经订阅不展示
                Set<Long> purchasedGoods = listByUser.stream()
                        .map(VipUserSubscribeRecord::getGoodsId)
                        .filter(goodsId -> goodsId == 1 || goodsId == 2)
                        .collect(Collectors.toSet());
                vipComboDtos = vipComboDtos.stream()
                        .filter(s -> !purchasedGoods.contains(s.getType().longValue()))
                        .toList();
            }
            vipInfoVo.setExpiredTime(vipUser.getVipEndtime());
        }
        vipInfoVo.setVipCombos(vipComboDtos);

        //有没有体验过免费试用
        VipUserQuery build = VipUserQuery.builder().userId(user.getId()).memberType(1).isDelete(0).build();
        VipUser oldVipUser = vipUserService.findByQuery(build);
        if (Objects.nonNull(oldVipUser)) {
            vipInfoVo.setIsFreeUsedForMonth(Objects.equals(oldVipUser.getIsTrialVip(), 1));
        } else {
            vipInfoVo.setIsFreeUsedForMonth(Boolean.FALSE);
        }
        String s = iSysConfigService.selectConfigByKey(PAYPAL_ENABLE, true);
        vipInfoVo.setShowPaypal("1".equals(s));
        return vipInfoVo;
    }

    private static List<VipComboDto> getVipComboDtos(Integer appType, List<PaymentGoodsDo> listByState, VipUser vipUser) {
        List<VipComboDto> vipComboDtos = listByState.stream().map(s -> {
            VipComboDto vipComboDto = new VipComboDto();
            vipComboDto.setType(s.getId().intValue());
            vipComboDto.setGoodsId(s.getId().intValue());
            vipComboDto.setAmount(s.getPrice());
            if (Objects.equals(appType, 1)) {
                if (Objects.nonNull(vipUser) && Objects.equals(vipUser.getIsTrialVip(), 1)) {
                    vipComboDto.setPlanId(s.getGoodsPaypalActualCode());
                } else {
                    vipComboDto.setPlanId(s.getGoodsPaypalCode());
                }
                boolean contains = s.getGoodsGooglePlayCode().contains("!");
                if (contains) {
                    String[] split = s.getGoodsGooglePlayCode().split("!");
                    vipComboDto.setGooglePlayCode(split[0]);
                    vipComboDto.setGoogleSubscriptionId(split[1]);
                } else {
                    vipComboDto.setGooglePlayCode(s.getGoodsGooglePlayCode());
                }
            } else {
                vipComboDto.setPlanId(s.getGoodsIosCode());
            }
            return vipComboDto;
        }).collect(Collectors.toList());
        return vipComboDtos;
    }

    public List<VipActivationRecordVo> getVipRecord(ZnsUserEntity user) {
        List<VipUserSubscribeRecord> listByUser = vipUserSubscribeRecordService.findListByUser(user.getId());
        List<VipUserLogDo> logList = vipUserLogService.findList(new VipUserLogQuery().setUserId(user.getId()).setSourceType(VipPurchaseTypeEnum.PITPAT_PLATFORM_SEND.getCode()));
        if (CollectionUtils.isEmpty(listByUser) && CollectionUtils.isEmpty(logList)) {
            return new ArrayList<>();
        }
        List<VipActivationRecordVo> list = new ArrayList<>();
        for (VipUserSubscribeRecord vipUserSubscribeRecord : listByUser) {
            //包年包月还未生效，不展示记录
            if (Objects.equals(vipUserSubscribeRecord.getIsShow(), YesNoStatus.NO.getCode())) {
                continue;
            }
            VipActivationRecordVo vipActivationRecordVo = new VipActivationRecordVo();
            vipActivationRecordVo.setPurchaseType(vipUserSubscribeRecord.getGoodsId().intValue());
            vipActivationRecordVo.setVipEndTime(vipUserSubscribeRecord.getVipEndTime());
            vipActivationRecordVo.setPurchaseTime(vipUserSubscribeRecord.getSubscribeStart());
            vipActivationRecordVo.setCreateTime(vipUserSubscribeRecord.getGmtCreate());
            list.add(vipActivationRecordVo);
        }
        if (!CollectionUtils.isEmpty(logList)) {
            for (VipUserLogDo vipUserLogDo : logList) {
                VipActivationRecordVo vipActivationRecordVo = new VipActivationRecordVo();
                vipActivationRecordVo.setPurchaseType(VipPurchaseTypeEnum.PITPAT_PLATFORM_SEND.getCode());
                vipActivationRecordVo.setVipEndTime(Objects.nonNull(vipUserLogDo.getVipEndtime()) ? Date.from(vipUserLogDo.getVipEndtime().toInstant()) : null);
                vipActivationRecordVo.setPurchaseTime(Date.from(vipUserLogDo.getGmtCreate().toInstant()));
                vipActivationRecordVo.setCreateTime(Date.from(vipUserLogDo.getGmtCreate().toInstant()));
                list.add(vipActivationRecordVo);
            }
        }
        return list.stream().sorted(Comparator.comparing(VipActivationRecordVo::getCreateTime).reversed()).collect(Collectors.toList());
    }
}
