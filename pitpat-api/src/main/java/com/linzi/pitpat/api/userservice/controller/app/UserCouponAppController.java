package com.linzi.pitpat.api.userservice.controller.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.bussiness.UserCouponBusiness;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.ActivityDisseminateBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityCouponConfigService;
import com.linzi.pitpat.data.activityservice.service.ActivityTaskConfigService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.awardservice.biz.UserCouponBizService;
import com.linzi.pitpat.data.awardservice.constant.enums.CouponConstant;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.request.ClaimCouponReq;
import com.linzi.pitpat.data.awardservice.model.request.UserCouponListRequestDto;
import com.linzi.pitpat.data.awardservice.model.resp.ClaimUserCouponResp;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageV2Vo;
import com.linzi.pitpat.data.awardservice.model.vo.CouponPageVo;
import com.linzi.pitpat.data.awardservice.model.vo.UserCouponDiKou;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.courseservice.model.request.CouponExchangeRequest;
import com.linzi.pitpat.data.courseservice.model.request.CourseListRequest;
import com.linzi.pitpat.data.courseservice.model.request.CourseUpdateRequest;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.request.course.UseCouponRequest;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 卷相关控制器
 *
 * <AUTHOR>
 * @date 2022/12/26 14:57
 */
@Slf4j
@RestController
@RequestMapping({"/app/userCoupon", "/h5/userCoupon"})
@RequiredArgsConstructor
public class UserCouponAppController extends BaseAppController {
    private final UserCouponService userCouponService;
    private final ZnsRunActivityService znsRunActivityService;

    private final UserCouponBusiness userCouponBusiness;
    private final ActivityDisseminateBizService activityDisseminateBizService;
    private final UserCouponManager userCouponManager;
    private final UserCouponBizService userCouponBizService;
    private final ActivityCouponConfigService activityCouponConfigService;
    private final ActivityTaskConfigService activityTaskConfigService;

    /**
     * 用户卷列表（4.4.3版本后使用下面的接口，老版本app还在继续使用，不能删除，等4.4.3版本后有强升的再删）
     */
    @Deprecated
    @PostMapping("/list")
    public Result<Page<CouponPageVo>> list(@RequestBody CourseListRequest po) {
        ZnsUserEntity loginUser = getLoginUser();
        Currency currency = getUserCurrency();
        po.setCouponMainType(CouponConstant.CouponMainTypeEnum.COUPON_MAIN_TYPE_1.type);
        Page<CouponPageVo> userCouponList = userCouponManager.getUserCouponList(po, loginUser, getAppType());
        userCouponList.getRecords().forEach(o -> o.setCurrency(currency));
        return CommonResult.success(userCouponList);
    }

    /**
     * 我的卷列表第二版
     */
    @PostMapping("/listV2")
    public Result<Page<CouponPageV2Vo>> listV2(@RequestBody UserCouponListRequestDto req) {
        ZnsUserEntity user = getLoginUser();
        Page<CouponPageV2Vo> resp = userCouponManager.getUserCouponListV2(req, user, getAppType());
        return CommonResult.success(resp);
    }


    private void wrapperDesc(PPageUtils userCouponList) {
        List<UserCouponDiKou> records = userCouponList.getRows();
        if (Objects.isNull(records)) {
            return;
        }
        List<UserCouponDiKou> recordsNew = new ArrayList<>();
        records.forEach(i -> {
            //过滤掉过期的优惠券
            if (i.getGmtEnd().isBefore(ZonedDateTime.now())) {
                return;
            }
            i.setVerifyType(0);
            Optional.ofNullable(activityCouponConfigService.getConfigByCouponId(i.getCouponId())).ifPresent(k -> {
                String verifyDesc = "";
                switch (k.getType()) {
                    case 1:
                        // 跑步类型
                        verifyDesc = "Only available in " + RunActivityTypeEnum.findByType(Integer.valueOf(k.getCouponConfig())).getEnName();
                        break;
                    case 2:
                        // 活动id
                        ActivityTypeDto activityNew = znsRunActivityService.getActivityNew(Long.valueOf(k.getCouponConfig()));
                        if (Objects.nonNull(activityNew)) {
                            if (activityNew.getMainType().equals(MainActivityTypeEnum.OLD.getType())) {
                                verifyDesc = "Only available in " + activityNew.getActivityTitle();
                            } else {
                                String languageCode = I18nMsgUtils.getLangCode();
                                ActivityDisseminate activityDisseminate = activityDisseminateBizService.findByActivityIdAndLanguage(activityNew.getId(), languageCode);
                                verifyDesc = I18nMsgUtils.getMessage("coupon.cannot.use.tips.first") + activityDisseminate.getTitle();
                            }
                        }
                        break;
                    case 3:
                        ActivityTaskConfig taskConfigServiceById = activityTaskConfigService.findById(Long.valueOf(k.getCouponConfig()));
                        if (Objects.nonNull(taskConfigServiceById)) {
                            verifyDesc = "Only available in " + taskConfigServiceById.getTemplateName();
                        }
                        break;
                    case 4:
                        ZnsRunActivityEntity znsRunActivityRunning = znsRunActivityService.selectActivityByBatchNo(k.getCouponConfig(), Arrays.asList(1));
                        if (Objects.nonNull(znsRunActivityRunning)) {
                            verifyDesc = "Only available in " + znsRunActivityRunning.getActivityTitle();
                        }
                        break;
                    case 5, 6:
                        verifyDesc = I18nMsgUtils.getMessage("coupon.cannot.use.tips.all");
                        break;
                    default:

                }
                i.setVerifyDesc(verifyDesc);
                i.setVerifyType(k.getType());
            });
            recordsNew.add(i);
        });
        userCouponList.setRows(recordsNew);
    }


    /**
     * 用户卷标志更新接口
     *
     * @param po
     * @return
     */
    @PostMapping("/update/isNew")
    public Result<Boolean> updateIsNew(@RequestBody CourseUpdateRequest po) {
        ZnsUserEntity loginUser = getLoginUser();
        boolean flag = userCouponService.updateIsNew(po, loginUser.getId());
        return CommonResult.success(flag);
    }

    /**
     * 发放用户优惠券
     *
     * @param po
     * @return
     */
    @PostMapping("/sendUserCoupon")
    public Result sendUserCoupon(@RequestBody CourseUpdateRequest po) {
        ZnsUserEntity loginUser = getLoginUser();
        return userCouponBizService.sendUserCoupon(po, loginUser.getId());
    }

    /**
     * 兑换券
     *
     * @param request
     * @return
     */
    @PostMapping("/exchange")
    public Result exchange(@RequestBody @Validated CouponExchangeRequest request) {
        if (request == null || StringUtil.isEmpty(request.getExchangeCode())) {
            return CommonResult.fail(I18nMsgUtils.getMessage("common.operate.reenter"));
        }
        return userCouponBusiness.exchangeCoupon(request, getLoginUser());
    }

    /**
     * 使用券
     *
     * @return
     */
    @PostMapping("/useCoupon")
    public Result useCoupon(@RequestBody UseCouponRequest request) {
        if (Objects.isNull(request.getUserCouponId())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        return userCouponBizService.useCoupon(request.getUserCouponId(), request.getActivityId(), null);
    }

    /**
     * 预先跑步开始前使用券
     *
     * @return
     * @tag 2.0.5
     */
    @PostMapping("/pre/useCoupon")
    public Result preUseCoupon(@RequestBody UseCouponRequest request) {
        if (Objects.isNull(request.getUserCouponId())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        return userCouponManager.preUseCoupon(request.getUserCouponId(), request.getActivityId(), "pre_use");
    }

    /**
     * 可使用的券列表
     *
     * @return
     */
    @PostMapping("/canUserCouponList")
    public Result<PPageUtils<UserCouponDiKou>> canUserCouponList(@RequestBody UseCouponRequest request) {
        PPageUtils<UserCouponDiKou> pageUtils = userCouponManager.selectUserCouponByUseCouponRequest(request);
        wrapperDesc(pageUtils);
        return CommonResult.success(pageUtils);
    }


    /**
     * 免费领取优惠券
     *
     * @param claimCouponReq
     * @return
     */
    @PostMapping("/claimCoupons")
    public Result claimCoupons(@RequestBody ClaimCouponReq claimCouponReq) {
        try {
            ZnsUserEntity loginUser = getLoginUser();
            Long userId = loginUser.getId();
            claimCouponReq.setUserId(userId);
            claimCouponReq.setZnsUserEntity(loginUser);
            ClaimUserCouponResp claimUserCouponResp = userCouponManager.claimCoupons(claimCouponReq);
            return CommonResult.success(claimUserCouponResp);
        } catch (Exception e) {
            log.error("免费领取优惠券失败!错误信息为:{},请求参数为:{}", e.getMessage(), claimCouponReq);
            return CommonResult.fail(e.getMessage());
        }
    }

    /**
     * 获取可用优惠券数量
     *
     * @param request
     * @return
     */
    @PostMapping("/getCouponCount")
    public Result getCouponCount(@RequestBody UseCouponRequest request) {
        try {
            ZnsUserEntity loginUser = getLoginUser();
            Long userId = loginUser.getId();
            request.setUserId(userId);
            Integer count = userCouponManager.getCouponCount(request);
            return CommonResult.success(count);
        } catch (Exception e) {
            log.error("获取可用优惠券数量失败!错误信息为:{},请求参数为:{}", e.getMessage(), request);
            return CommonResult.fail(e.getMessage());
        }
    }

}
