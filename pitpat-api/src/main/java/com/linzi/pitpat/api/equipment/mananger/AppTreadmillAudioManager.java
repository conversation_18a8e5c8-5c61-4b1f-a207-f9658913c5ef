package com.linzi.pitpat.api.equipment.mananger;


import com.linzi.pitpat.api.dto.request.TreadmillAudioUpdateStatusQueryReq;
import com.linzi.pitpat.api.dto.request.TreadmillAudioUpdateStatusReq;
import com.linzi.pitpat.api.dto.request.TreadmillSoundpackCheckVersionReq;
import com.linzi.pitpat.api.dto.response.TreadmillAudioLatestQueryResp;
import com.linzi.pitpat.api.dto.response.TreadmillAudioUpdateStatusQueryResp;
import com.linzi.pitpat.api.dto.response.TreadmillAudioUpdateStatusResp;
import com.linzi.pitpat.api.dto.response.TreadmillSoundpackCheckVersionResp;
import com.linzi.pitpat.api.dto.vo.AppTreadmillAudioVo;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillAudioUpgradeStatusEnum;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillAudio;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillAudioSoundpackRel;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillSoundpack;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillSoundpackBind;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillSoundpackUpgradeLog;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillAudioSoundpackRelQuery;
import com.linzi.pitpat.data.equipmentservice.model.query.TreadmillSoundpackUpgradeLogQuery;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillAudioSoundpackRelService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackBindService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillSoundpackUpgradeLogService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.query.UserEquipmentQuery;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AppTreadmillAudioManager {

    @Resource
    private TreadmillAudioService treadmillAudioService;

    @Resource
    private TreadmillSoundpackService treadmillSoundpackService;

    @Resource
    private TreadmillAudioSoundpackRelService treadmillAudioSoundpackRelService;


    @Resource
    private TreadmillSoundpackBindService treadmillSoundpackBindService;

    @Resource
    private TreadmillSoundpackUpgradeLogService treadmillSoundpackUpgradeLogService;

    @Resource
    private ZnsUserEquipmentService znsUserEquipmentService;

    @Resource
    private RedissonClient redissonClient;


    public TreadmillSoundpackCheckVersionResp checkVersion(TreadmillSoundpackCheckVersionReq req) {
        String key = String.format(RedisKeyConstant.EQUIPMENT_SOUNDPACK_CHECK, req.getEquipmentNo());
        RLock lock = redissonClient.getLock(key);
        try {
            if (!lock.tryLock(1L, 2L, TimeUnit.SECONDS)) {
                log.error("设备音频包短时间重复校验版本，equipmentNo:{}", req.getEquipmentNo());
                throw new BaseException(CommonError.DUPLICATE_REQUEST.getMsg(), CommonError.DUPLICATE_REQUEST.getCode());
            }
        } catch (InterruptedException e) {
            log.error("音频包最新版本校验获取锁中断,e:", e);
            throw new BaseException(CommonError.SYSTEM_ERROR.getMsg(), CommonError.SYSTEM_ERROR.getCode());
        }
        TreadmillSoundpackCheckVersionResp resp = new TreadmillSoundpackCheckVersionResp();
        TreadmillSoundpack treadmillSoundpack = treadmillSoundpackService.getById(req.getPackageId());
        if (Objects.isNull(treadmillSoundpack.getLatestVersion())) {
            log.error("该音频包未发布，packageId:{}", req.getPackageId());
            throw new BaseException(ActivityError.AUDIO_PACKAGE_NOT_PUBLISH.getMsg(), ActivityError.AUDIO_PACKAGE_NOT_PUBLISH.getCode());
        }
        TreadmillSoundpackBind treadmillSoundpackBind = treadmillSoundpackBindService.getByEquipmentNo(req.getEquipmentNo());
        if (Objects.isNull(treadmillSoundpackBind)) {
            initTreadmillSoundpackBind(req);
        } else if (!Objects.equals(req.getVersion(), treadmillSoundpackBind.getVersion())) {
            log.info("设备绑定表数据与设备中数据不一致，packageId:{},version:{},req:{}", treadmillSoundpackBind.getPackageId(), treadmillSoundpackBind.getVersion(), req);
            syncTreadmillSoundpackBind(req, treadmillSoundpackBind.getId());
        }
        resp.setIsLatest(treadmillSoundpack.getLatestVersion() <= req.getVersion());
        resp.setVersion(treadmillSoundpack.getLatestVersion());
        return resp;
    }

    /**
     * 同步音频包设备绑定数据
     *
     * @param req
     * @param bindId
     */
    private void syncTreadmillSoundpackBind(TreadmillSoundpackCheckVersionReq req, Long bindId) {
        TreadmillSoundpackBind treadmillSoundpackBind = new TreadmillSoundpackBind();
        treadmillSoundpackBind.setId(bindId);
        treadmillSoundpackBind.setEquipmentNo(req.getEquipmentNo());
        treadmillSoundpackBind.setVersion(req.getVersion());
        treadmillSoundpackBind.setGmtModified(ZonedDateTime.now());
        treadmillSoundpackBindService.update(treadmillSoundpackBind);

    }

    /**
     * 初始化音频包设备绑定
     *
     * @param req
     */
    private void initTreadmillSoundpackBind(TreadmillSoundpackCheckVersionReq req) {
        TreadmillSoundpackBind treadmillSoundpackBind = new TreadmillSoundpackBind();
        treadmillSoundpackBind.setEquipmentNo(req.getEquipmentNo());
        treadmillSoundpackBind.setPackageId(req.getPackageId());
        treadmillSoundpackBind.setVersion(req.getVersion());
        treadmillSoundpackBindService.insert(treadmillSoundpackBind);
    }


    public TreadmillAudioLatestQueryResp queryLatest(String equipmentNo) {
        TreadmillAudioLatestQueryResp resp = new TreadmillAudioLatestQueryResp();
        TreadmillSoundpackBind treadmillSoundpackBind = treadmillSoundpackBindService.getByEquipmentNo(equipmentNo);
        if (Objects.isNull(treadmillSoundpackBind)) {
            log.error("该跑步机未绑定音频包，equipmentNo:{}", equipmentNo);
            return resp;
        }
        Long packageId = treadmillSoundpackBind.getPackageId();
        TreadmillSoundpack treadmillSoundpack = treadmillSoundpackService.getById(packageId);
        Integer version = treadmillSoundpack.getLatestVersion();
        resp.setPackageId(packageId);
        resp.setVersion(version);
        TreadmillAudioSoundpackRelQuery relQuery = TreadmillAudioSoundpackRelQuery.builder().packageId(packageId).version(version).build();
        List<TreadmillAudioSoundpackRel> relList = treadmillAudioSoundpackRelService.findList(relQuery);
        List<Long> audioIdList = relList.stream().map(TreadmillAudioSoundpackRel::getAudioId).collect(Collectors.toList());
        List<TreadmillAudio> audioList = treadmillAudioService.findByIds(audioIdList);
        Map<Long, Integer> audioIdMap = relList.stream().collect(Collectors.toMap(TreadmillAudioSoundpackRel::getAudioId, TreadmillAudioSoundpackRel::getOrderNumber));
        List<AppTreadmillAudioVo> audioVoList = audioList.stream().map(e -> {
            AppTreadmillAudioVo appTreadmillAudioVo = BeanUtil.copyBean(e, AppTreadmillAudioVo.class);
            appTreadmillAudioVo.setOrderNumber(audioIdMap.get(e.getId()));
            return appTreadmillAudioVo;
        }).sorted(Comparator.comparing(AppTreadmillAudioVo::getOrderNumber)).collect(Collectors.toList());
        resp.setAudioList(audioVoList);
        return resp;
    }


    public TreadmillAudioUpdateStatusResp notifyUpdateStatus(TreadmillAudioUpdateStatusReq req) {
        String equipmentNo = req.getEquipmentNo();
        Integer upgradeStatus = req.getUpgradeStatus();
        Integer version = req.getVersion();
        Long packageId = req.getPackageId();
        UserEquipmentQuery userEquipmentQuery = UserEquipmentQuery.builder().equipmentNo(equipmentNo).build();
        ZnsUserEquipmentEntity userEquipment = znsUserEquipmentService.getByQuery(userEquipmentQuery);
        if (Objects.isNull(userEquipment)) {
            log.error("该跑步机未绑定用户,equipmentNo:{}", equipmentNo);
            throw new BaseException(ActivityError.USER_DEVICE_IS_NOT_BOUND.getMsg(), ActivityError.USER_DEVICE_IS_NOT_BOUND.getCode());
        }
        Long userId = userEquipment.getUserId();
        TreadmillAudioUpdateStatusResp resp = new TreadmillAudioUpdateStatusResp();
        resp.setFlag(false);
        TreadmillSoundpackBind treadmillSoundpackBind = treadmillSoundpackBindService.getByEquipmentNo(equipmentNo);
        if (Objects.isNull(treadmillSoundpackBind)) {
            return resp;
        }
        TreadmillSoundpackUpgradeLogQuery query = new TreadmillSoundpackUpgradeLogQuery();
        query.setPackageId(packageId);
        query.setCurrentVersion(treadmillSoundpackBind.getVersion());
        query.setUpgradeVersion(version);
        query.setEquipmentNo(equipmentNo);
        TreadmillSoundpackUpgradeLog upgradeLog = treadmillSoundpackUpgradeLogService.getByQuery(query);
        if (TreadmillAudioUpgradeStatusEnum.IN_PROGRESS.getCode().equals(upgradeStatus)) {
            if (Objects.isNull(upgradeLog) || TreadmillAudioUpgradeStatusEnum.FAILURE.getCode().equals(upgradeLog.getUpgradeStatus())) {
                // 未升级过或升级失败，可进行升级
                TreadmillSoundpackUpgradeLog log = new TreadmillSoundpackUpgradeLog();
                log.setEquipmentNo(equipmentNo);
                log.setPackageId(treadmillSoundpackBind.getPackageId());
                log.setCurrentVersion(treadmillSoundpackBind.getVersion());
                log.setUpgradeVersion(version);
                log.setUpgradeStatus(TreadmillAudioUpgradeStatusEnum.IN_PROGRESS.getCode());
                log.setUserId(userId);
                resp.setFlag(treadmillSoundpackUpgradeLogService.insert(log) > 0 ? true : false);
            }
        } else if (TreadmillAudioUpgradeStatusEnum.COMPLETED.getCode().equals(upgradeStatus)
                || TreadmillAudioUpgradeStatusEnum.FAILURE.getCode().equals(upgradeStatus)) {
            upgradeLog.setUpgradeStatus(upgradeStatus);
            upgradeLog.setGmtModified(ZonedDateTime.now());
            boolean flag = treadmillSoundpackUpgradeLogService.update(upgradeLog) > 0 ? true : false;
            resp.setFlag(flag);
            if (TreadmillAudioUpgradeStatusEnum.COMPLETED.getCode().equals(upgradeStatus) && flag) {
                treadmillSoundpackBind.setVersion(version);
                treadmillSoundpackBind.setGmtModified(ZonedDateTime.now());
                treadmillSoundpackBindService.update(treadmillSoundpackBind);
            }
        }
        return resp;
    }


    public TreadmillAudioUpdateStatusQueryResp queryUpdateStatus(TreadmillAudioUpdateStatusQueryReq req, Long userId) {
        TreadmillSoundpackBind treadmillSoundpackBind = treadmillSoundpackBindService.getByEquipmentNo(req.getEquipmentNo());
        TreadmillSoundpackUpgradeLogQuery query = new TreadmillSoundpackUpgradeLogQuery();
        query.setPackageId(treadmillSoundpackBind.getPackageId());
        query.setUpgradeVersion(req.getVersion());
        query.setEquipmentNo(req.getEquipmentNo());
        query.setUserId(userId);
        TreadmillSoundpackUpgradeLog upgradeLog = treadmillSoundpackUpgradeLogService.getByQuery(query);
        TreadmillAudioUpdateStatusQueryResp resp = new TreadmillAudioUpdateStatusQueryResp();
        resp.setUpgradeStatus(0);
        if (Objects.nonNull(upgradeLog)) {
            resp.setUpgradeStatus(upgradeLog.getUpgradeStatus());
        }
        return resp;
    }


}
