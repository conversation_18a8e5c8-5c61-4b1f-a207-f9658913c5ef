package com.linzi.pitpat.api.mallservice.mananger;

import com.linzi.pitpat.api.mallservice.dto.request.AddShoppingCartReq;
import com.linzi.pitpat.api.mallservice.dto.request.BatchAddShoppingCartReq;
import com.linzi.pitpat.api.mallservice.dto.request.UpdateShoppingCartReq;
import com.linzi.pitpat.api.mallservice.dto.response.BatchAddShoppingCartResp;
import com.linzi.pitpat.api.mallservice.dto.response.GoodsPackageCartRespDto;
import com.linzi.pitpat.api.mallservice.dto.response.ShoppingCartListRespDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.data.awardservice.biz.MallSkuCouponBizService;
import com.linzi.pitpat.data.enums.CombinatonGoodsTypeEnum;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.mallservice.biz.ShoppingCartBizService;
import com.linzi.pitpat.data.mallservice.converter.api.GoodsSkuApiConverter;
import com.linzi.pitpat.data.mallservice.dto.response.RecommendSkuRespDto;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.model.entity.CombinationPackageSkuDo;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsI18nEntity;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsSkuI18nDo;
import com.linzi.pitpat.data.mallservice.model.entity.ShoppingCartDo;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsEntity;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.query.CombinationPackageSkuQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsI18nQuery;
import com.linzi.pitpat.data.mallservice.model.query.GoodsSkuI18nQuery;
import com.linzi.pitpat.data.mallservice.model.vo.GoodsAmountVo;
import com.linzi.pitpat.data.mallservice.service.CombinationPackageSkuService;
import com.linzi.pitpat.data.mallservice.service.GoodsI18nService;
import com.linzi.pitpat.data.mallservice.service.GoodsSkuI18nService;
import com.linzi.pitpat.data.mallservice.service.ShoppingCartService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsService;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.systemservice.model.query.CountryQuery;
import com.linzi.pitpat.data.systemservice.model.vo.CountryVo;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class ShoppingCartManager {

    private final ShoppingCartService shoppingCartService;
    private final ZnsGoodsSkuService znsGoodsSkuService;
    private final ZnsGoodsService znsGoodsService;
    private final ShoppingCartBizService shoppingCartBiz;
    private final GoodsI18nService goodsI18nService;
    private final GoodsSkuI18nService goodsSkuI18nService;
    private final ZnsCountryService znsCountryService;
    private final UserExtraBizService userExtraBizService;
    private final MallSkuCouponBizService mallCouponBizService;
    private final CombinationPackageSkuService combinationPackageSkuService;
    private final GoodsSkuApiConverter goodsSkuApiConverter;
    private final UserExtraService userExtraService;
    private final ZnsUserService userService;

    /**
     * 用户购物车商品列表
     */
    public List<ShoppingCartListRespDto> shoppingCartSkuList(ZnsUserEntity user) {
        //查询购物车可用的sku数据
        List<ShoppingCartDo> carSkuList = shoppingCartService.findYesSkuByUserId(user.getId());
        if (CollectionUtils.isEmpty(carSkuList)) {
            return new ArrayList<>();
        }

        //购物车查询sku（包含被逻辑删除的sku）
        Map<Long, ShoppingCartDo> shoppingCartMap = carSkuList.stream().collect(Collectors.toMap(ShoppingCartDo::getSkuId, Function.identity(), (k1, k2) -> k2));
        List<ZnsGoodsSkuEntity> skuEntities = znsGoodsSkuService.findShoppingCarByIds(shoppingCartMap.keySet());
        if (CollectionUtils.isEmpty(skuEntities)) {
            return new ArrayList<>();
        }
        //查询goods
        List<Long> goodIds = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findByIds(goodIds);
        Map<Long, ZnsGoodsEntity> goodsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(goodsEntities)) {
            goodsMap = goodsEntities.stream().collect(Collectors.toMap(ZnsGoodsEntity::getId, Function.identity(), (k1, k2) -> k2));
        }
        List<ZnsGoodsSkuEntity> enableEntities = new ArrayList<>(); //可用sku
        List<ZnsGoodsSkuEntity> closeEntities = new ArrayList<>(); //停用sku
        for (ZnsGoodsSkuEntity skuEntity : skuEntities) {
            ZnsGoodsEntity goodsEntity = goodsMap.get(skuEntity.getGoodsId());
            if (skuEntity.getIsDelete() != 0 || skuEntity.getStatus() != 1 || skuEntity.getStock() <= 0
                    || goodsEntity == null || goodsEntity.getStatus() != 1) {
                //被删除 or 未上架 or 没有库存
                closeEntities.add(skuEntity);
            } else {
                enableEntities.add(skuEntity);
            }
        }

        //封装购物车返回参数
        List<ShoppingCartListRespDto> enableShoppingCarVoList = getShoppingCarVoList(user.getId(),enableEntities, 1); //上架商品
        //按修改时间倒序
        enableShoppingCarVoList.forEach(item -> item.setGmtModified(shoppingCartMap.get(item.getSkuId()).getGmtModified()));
        enableShoppingCarVoList.sort(Comparator.comparing(ShoppingCartListRespDto::getGmtModified).reversed());
        List<ShoppingCartListRespDto> closeShoppingCarVoList = getShoppingCarVoList(user.getId(),closeEntities, 0); //下架商品

        //返回对象
        enableShoppingCarVoList.addAll(closeShoppingCarVoList);
        enableShoppingCarVoList.forEach(item -> item.setCount(shoppingCartMap.get(item.getSkuId()).getCount()));
        return enableShoppingCarVoList;
    }
    public List<GoodsPackageCartRespDto> shoppingCartSkuListV2(ZnsUserEntity user) {
        List<ShoppingCartListRespDto> cartList = shoppingCartSkuList(user);
        if (CollectionUtils.isEmpty(cartList)) {
            return new ArrayList<>();
        }

        List<GoodsPackageCartRespDto> result = new ArrayList<>();

        // 1. 分离可用和不可用商品
        List<ShoppingCartListRespDto> validList = cartList.stream().filter(item -> Objects.equals(item.getState(), 1)).collect(Collectors.toList());

        List<ShoppingCartListRespDto> invalidList = cartList.stream().filter(item -> Objects.equals(item.getState(), 0)).collect(Collectors.toList());

        // 2. 查询所有可用商品的组合包信息
        Map<Long, List<CombinationPackageSkuDo>> skuToCombinationMap = new HashMap<>();
        if (!validList.isEmpty()) {
            List<Long> skuIds = validList.stream().map(ShoppingCartListRespDto::getSkuId).collect(Collectors.toList());
            List<CombinationPackageSkuDo> combinationList = combinationPackageSkuService.findList(new CombinationPackageSkuQuery().setSkuIds(skuIds).setStatus(0).setLeStartTime(ZonedDateTime.now()).setGeEndTime(ZonedDateTime.now()));

            // 构建SKU到组合包信息的映射
            skuToCombinationMap = combinationList.stream().collect(Collectors.groupingBy(CombinationPackageSkuDo::getSkuId));
        }

        // 3. 用于存储组合包，key为组合包ID，value为对应的GoodsPackageCartRespDto
        Map<Long, GoodsPackageCartRespDto> packageMap = new LinkedHashMap<>();

        // 4. 记录已分配的SKU，避免衍生品重复分配
        Set<Long> assignedSkuIds = new HashSet<>();

        // 5. 预处理：识别购物车中所有的组合包，并记录每个组合包最早出现的位置
        Map<Long, Integer> packageFirstAppearanceIndex = new HashMap<>();
        Set<Long> validPackageIds = new HashSet<>(); // 购物车中存在的所有组合包ID

        for (int i = 0; i < validList.size(); i++) {
            ShoppingCartListRespDto cartItem = validList.get(i);
            List<CombinationPackageSkuDo> combinationInfos = skuToCombinationMap.get(cartItem.getSkuId());
            if (!CollectionUtils.isEmpty(combinationInfos)) {
                // 获取该SKU所属的所有组合包信息
                for (CombinationPackageSkuDo info : combinationInfos) {
                    Long packageId = info.getCombinationPackageId();

                    // 记录每个组合包最早出现的位置
                    packageFirstAppearanceIndex.putIfAbsent(packageId, i);

                    // 记录所有购物车中存在的组合包
                    validPackageIds.add(packageId);
                }
            }
        }

        // 6. 按照cartList的顺序处理每个可用商品，记录每个位置的处理结果
        List<ProcessedItem> processedItems = new ArrayList<>();

        for (int i = 0; i < validList.size(); i++) {
            ShoppingCartListRespDto cartItem = validList.get(i);
            Long skuId = cartItem.getSkuId();
            List<CombinationPackageSkuDo> combinationInfos = skuToCombinationMap.get(skuId);

            if (assignedSkuIds.contains(skuId)) {
                // 该SKU已被处理（作为组合包的一部分），跳过
                continue;
            }

            if (CollectionUtils.isEmpty(combinationInfos)) {
                // 非组合商品，创建单独的包装对象
                GoodsPackageCartRespDto singlePackage = new GoodsPackageCartRespDto();
                singlePackage.setCombinationPackageId(null);
                singlePackage.setType(0); // 普通商品
                cartItem.setCombinationGoodsType(-1);
                singlePackage.setShoppingCartList(List.of(cartItem));
                processedItems.add(new ProcessedItem(i, singlePackage));
                assignedSkuIds.add(skuId);
            } else {
                // 组合商品，找到应该归属的组合包
                Long selectedPackageId = findBestPackageForItem(combinationInfos, validPackageIds);

                if (selectedPackageId != null && !packageMap.containsKey(selectedPackageId)) {
                    // 第一次遇到这个组合包，创建完整的组合包对象
                    GoodsPackageCartRespDto combinationPackage = createCombinationPackage(selectedPackageId, validList, skuToCombinationMap, assignedSkuIds);
                    processedItems.add(new ProcessedItem(packageFirstAppearanceIndex.get(selectedPackageId), combinationPackage));
                    packageMap.put(selectedPackageId, combinationPackage);
                }
            }
        }

        // 7. 按照原始位置顺序排序并构建结果
        processedItems.sort((a, b) -> Integer.compare(a.originalPosition, b.originalPosition));
        for (ProcessedItem item : processedItems) {
            result.add(item.packageDto);
        }

        // 9. 处理不可用商品，每个都是单独的对象
        for (ShoppingCartListRespDto invalidItem : invalidList) {
            GoodsPackageCartRespDto invalidPackage = new GoodsPackageCartRespDto();
            invalidPackage.setCombinationPackageId(-1L);
            invalidPackage.setType(0);
            invalidItem.setCombinationGoodsType(-1);
            invalidPackage.setShoppingCartList(List.of(invalidItem));
            result.add(invalidPackage);
        }

        // 10. 处理推荐商品（通过第一个组合商品的主品SKU来推荐）
        if (!result.isEmpty()) {
            // 找到第一个组合商品
            GoodsPackageCartRespDto firstCombinationPackage = result.stream()
                .filter(pkg -> Objects.equals(pkg.getType(), 1))
                .findFirst()
                .orElse(null);

            if (firstCombinationPackage != null) {
                // 按照cartList顺序找到该组合包中最早出现的主品SKU
                ShoppingCartListRespDto mainProduct = findFirstMainProductInOrder(firstCombinationPackage, cartList);
                if (mainProduct != null) {
                    addRecommendationProducts(result, firstCombinationPackage, user);
                }
            }
        }

        return result;
    }

    /**
     * 为商品找到最佳的组合包（选择购物车中存在的组合包）
     */
    private Long findBestPackageForItem(List<CombinationPackageSkuDo> combinationInfos,
                                       Set<Long> validPackageIds) {
        // 获取该商品所属的组合包ID列表
        Set<Long> itemPackageIds = combinationInfos.stream()
                .map(CombinationPackageSkuDo::getCombinationPackageId)
                .collect(Collectors.toSet());

        // 选择购物车中存在的组合包（只要商品属于组合包就返回该组合包）
        for (Long packageId : itemPackageIds) {
            if (validPackageIds.contains(packageId)) {
                return packageId;
            }
        }

        // 如果没有找到有效的组合包，返回null（当作普通商品处理）
        return null;
    }

    /**
     * 按照cartList顺序找到组合包中最早出现的主品
     */
    private ShoppingCartListRespDto findFirstMainProductInOrder(GoodsPackageCartRespDto combinationPackage,
                                                              List<ShoppingCartListRespDto> cartList) {
        // 获取组合包中所有主品的SKU ID
        Set<Long> mainProductSkuIds = combinationPackage.getShoppingCartList().stream()
            .filter(item -> Objects.equals(item.getCombinationGoodsType(), 0)) // 0:主品
            .map(ShoppingCartListRespDto::getSkuId)
            .collect(Collectors.toSet());

        // 按照cartList的顺序，找到第一个出现的主品
        for (ShoppingCartListRespDto cartItem : cartList) {
            if (mainProductSkuIds.contains(cartItem.getSkuId())) {
                // 返回组合包中对应的主品对象（带有正确的combinationGoodsType）
                return combinationPackage.getShoppingCartList().stream()
                    .filter(item -> Objects.equals(item.getSkuId(), cartItem.getSkuId()) &&
                                   Objects.equals(item.getCombinationGoodsType(), 0))
                    .findFirst()
                    .orElse(null);
            }
        }

        return null;
    }

    /**
     * 创建完整的组合包对象，包含该组合包的所有商品
     */
    private GoodsPackageCartRespDto createCombinationPackage(Long packageId,
                                                            List<ShoppingCartListRespDto> validList,
                                                            Map<Long, List<CombinationPackageSkuDo>> skuToCombinationMap,
                                                            Set<Long> assignedSkuIds) {
        GoodsPackageCartRespDto combinationPackage = new GoodsPackageCartRespDto();
        combinationPackage.setCombinationPackageId(packageId);
        combinationPackage.setType(1); // 组合包商品
        combinationPackage.setShoppingCartList(new ArrayList<>());

        // 遍历所有商品，找出属于该组合包的商品
        for (ShoppingCartListRespDto cartItem : validList) {
            List<CombinationPackageSkuDo> combinationInfos = skuToCombinationMap.get(cartItem.getSkuId());
            if (!CollectionUtils.isEmpty(combinationInfos)) {
                // 检查该商品是否属于当前组合包
                Optional<CombinationPackageSkuDo> matchingInfo = combinationInfos.stream()
                    .filter(info -> info.getCombinationPackageId().equals(packageId))
                    .findFirst();

                if (matchingInfo.isPresent()) {
                    // 设置商品类型
                    cartItem.setCombinationGoodsType(matchingInfo.get().getCombinationGoodsType());
                    combinationPackage.getShoppingCartList().add(cartItem);
                    assignedSkuIds.add(cartItem.getSkuId());
                }
            }
        }

        return combinationPackage;
    }

    /**
     * 添加推荐商品
     */
    private void addRecommendationProducts(List<GoodsPackageCartRespDto> result,
                                         GoodsPackageCartRespDto combinationPackage,
                                         ZnsUserEntity user) {
        //要排除的衍生品
        List<Long> excludeList = combinationPackage.getShoppingCartList().stream().filter(s -> Objects.equals(s.getCombinationGoodsType(), 1)).map(ShoppingCartListRespDto::getSkuId).toList();
        // 找到主品
        combinationPackage.getShoppingCartList().stream()
            .filter(s -> Objects.equals(s.getCombinationGoodsType(), 0)).max(Comparator.comparing(ShoppingCartListRespDto::getGmtModified))
            .ifPresent(item -> {
                // 查询推荐商品
                List<CombinationPackageSkuDo> derivativesList = combinationPackageSkuService.findList(
                    new CombinationPackageSkuQuery()
                        .setCombinationPackageId(combinationPackage.getCombinationPackageId())
                        .setCombinationGoodsType(CombinatonGoodsTypeEnum.DERIVATIVES_TYPE.getType())
                        .setNeSkuIds(excludeList).setLocationType(1)
                );
                ZnsGoodsEntity goods = znsGoodsService.findById(item.getGoodsId());
                List<RecommendSkuRespDto> recommendList = derivativesList.stream().map(sku -> {
                     ZnsGoodsSkuEntity goodsSku = znsGoodsSkuService.findById(sku.getSkuId());
                     ZnsGoodsEntity goodsEntity = znsGoodsService.findById(sku.getGoodsId());
                     if (Objects.isNull(goodsSku) || Objects.isNull(goodsEntity) ||
                         !Objects.equals(goodsSku.getStatus(), 1) || goodsSku.getStock() <= 0) {
                         return null;
                     }
                     Map<Long, String> map = goodsSkuI18nService.findListOrDefault(
                         new GoodsSkuI18nQuery().setGoodsId(sku.getGoodsId()).setLangCode(user.getLanguageCode()),
                         goodsEntity.getDefaultLangCode()
                     ).stream().collect(Collectors.toMap(GoodsSkuI18nDo::getSkuId, GoodsSkuI18nDo::getPropertyValues, (o1, o2) -> o1));

                     RecommendSkuRespDto recommendDto = goodsSkuApiConverter.toRecommendDto(goodsSku, map.get(goodsSku.getId()));
                     recommendDto.setCurrencySymbol(I18nConstant.buildCurrency(goodsSku.getCurrencyCode()).getCurrencySymbol());
                     recommendDto.setDiscount(new GoodsAmountVo(recommendDto.getOriginalPrice(), recommendDto.getSalePrice()).calDiscount());
                     return recommendDto;
                 }).filter(Objects::nonNull).collect(Collectors.toList());

                if (!recommendList.isEmpty()) {
                    GoodsPackageCartRespDto recommendCart = new GoodsPackageCartRespDto();
                    recommendCart.setCombinationPackageId(0L);
                    recommendCart.setType(2); // 推荐商品
                    recommendCart.setShoppingCartList(recommendList.stream().map(s -> {
                        ShoppingCartListRespDto shoppingCartListRespDto = new ShoppingCartListRespDto();
                        BeanUtil.copyPropertiesIgnoreNull(s, shoppingCartListRespDto);
                        return shoppingCartListRespDto;
                                         }).collect(Collectors.toList()));
                     //推荐设备型号
                     recommendCart.setEquipmentModel(Objects.nonNull(goods) ? goods.getEquipmentModel() : null);
                     result.add(recommendCart);
                }
            });
    }
    /**
     * 加入购物车
     */
    public void addShoppingCar(AddShoppingCartReq req, Long userId) {
        //校验sku状态
        Long skuId = req.getSkuId();
        chcekSkuStatus(skuId);

        //校验sku库存
        List<ShoppingCartDo> shoppingCartDos = shoppingCartService.findByUserIdAndSkuId(userId, List.of(skuId));
        int carSkuCount = 0; //sku购物车已有数量
        if (!CollectionUtils.isEmpty(shoppingCartDos) && OrderConstant.CarSkuStateEnum.YES.code.equals(shoppingCartDos.get(0).getState())) {
            carSkuCount = shoppingCartDos.get(0).getCount();
        }
        ZnsGoodsSkuEntity goodsSkuEntity = znsGoodsSkuService.findById(skuId);
        if (carSkuCount + req.getCount() > goodsSkuEntity.getStock()) {
            //库存不足
            log.info("[addShoppingCar]---校验商品状态、库存,skuId={},库存不足", skuId);
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.stock"), UserError.GOODS_NO_STOCK.getCode());
        }

        //加入购物车
        shoppingCartBiz.modifyShoppingCar(userId, List.of(skuId), req.getCount(), OrderConstant.CarUpdateTypeEnum.INCREASE);
    }
    /**
     * 批量加衍生品,加入购物车-校验不报错
     */
    public BatchAddShoppingCartResp batchAddShoppingCar(BatchAddShoppingCartReq req, Long userId) {
        if (CollectionUtils.isEmpty(req.getAddShoppingCartReqList())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "skuId"));
        }
        List<Long> canAddSkuIdList = new ArrayList<>(); //能够加购的skuId集合
        List<Long> cannotAddSkuIdList = new ArrayList<>(); //无法加购的skuId集合
        BatchAddShoppingCartResp batchAddShoppingCartResp = new BatchAddShoppingCartResp();
        //批量校验sku状态和库存
        for (AddShoppingCartReq addShoppingCartReq : req.getAddShoppingCartReqList()) {

            Long skuId = addShoppingCartReq.getSkuId();
            try {
                //校验sku状态
                chcekSkuStatus(skuId);
                //校验sku库存
                List<ShoppingCartDo> shoppingCartDos = shoppingCartService.findByUserIdAndSkuId(userId, List.of(skuId));
                int carSkuCount = 0; //sku购物车已有数量
                if (!CollectionUtils.isEmpty(shoppingCartDos) && OrderConstant.CarSkuStateEnum.YES.code.equals(shoppingCartDos.get(0).getState())) {
                    carSkuCount = shoppingCartDos.get(0).getCount();
                }
                ZnsGoodsSkuEntity goodsSkuEntity = znsGoodsSkuService.findById(skuId);
                if (carSkuCount + addShoppingCartReq.getCount() > goodsSkuEntity.getStock()) {
                    //库存不足
                    log.info("[addShoppingCar]---校验商品状态、库存,skuId={},库存不足", skuId);
                    throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.stock"), UserError.GOODS_NO_STOCK.getCode());
                }
                //加入购物车
                shoppingCartBiz.modifyShoppingCar(userId, List.of(skuId), addShoppingCartReq.getCount(), OrderConstant.CarUpdateTypeEnum.INCREASE);
                canAddSkuIdList.add(skuId);
            } catch (BaseException e) {
                log.info("[batchAddShoppingCar]---批量校验商品状态、库存,skuId={},异常={}", skuId, e.getMessage());
                cannotAddSkuIdList.add(skuId);
            }
        }
        batchAddShoppingCartResp.setCanAddSkuIdList(canAddSkuIdList);
        batchAddShoppingCartResp.setCannotAddSkuIdList(cannotAddSkuIdList);
        return batchAddShoppingCartResp;
    }

    /**
     * 购物车商品信息
     *
     * @param state : sku状态，0：不可用，1：可用
     */
    private List<ShoppingCartListRespDto> getShoppingCarVoList(Long userId, List<ZnsGoodsSkuEntity> skuEntities, Integer state) {
        List<ShoppingCartListRespDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuEntities)) {
            return result;
        }
        List<Long> goodsIdList = skuEntities.stream().map(ZnsGoodsSkuEntity::getGoodsId).distinct().toList();
        List<ZnsGoodsEntity> goodsEntities = znsGoodsService.findCarGoodsByIds(goodsIdList);
        Map<Long, ZnsGoodsEntity> goodsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(goodsEntities)) {
            goodsMap = goodsEntities.stream().collect(Collectors.toMap(ZnsGoodsEntity::getId, Function.identity(), (k1, k2) -> k1));
        }
        ZnsUserEntity znsUser = userService.findById(userId);
        String countryCode = null;
        if (znsUser.getAppVersion() >= VersionConstant.V4_7_5) {
            countryCode = I18nConstant.CountryCodeEnum.US.code;
            UserExtraDo extraDo = userExtraService.findByUserId(userId);
            if (Objects.nonNull(extraDo) && StringUtils.hasText(extraDo.getMallCountryCode())) {
                countryCode = extraDo.getMallCountryCode();
            }
        } else {
            countryCode = userExtraBizService.findUserMallCountryCodeOrDefault(userId, null);
        }
        for (ZnsGoodsSkuEntity skuEntity : skuEntities) {
            ZnsGoodsEntity znsGoodsEntity = goodsMap.get(skuEntity.getGoodsId());
            if (znsGoodsEntity == null) {
                continue;
            }
            //查询goods I18N
            String languageCode = StringUtils.hasText(I18nMsgUtils.getLangCode()) ? I18nMsgUtils.getLangCode() : I18nConstant.LanguageCodeEnum.en_US.getCode();
            GoodsI18nQuery goodsI18nQuery = new GoodsI18nQuery().setGoodsId(znsGoodsEntity.getId()).setLanguageCode(languageCode);
            GoodsI18nEntity goodsI18nEntity = goodsI18nService.findByLanguageCodeOrDefault(goodsI18nQuery, znsGoodsEntity.getDefaultLangCode());
            String skuName = Objects.isNull(goodsI18nEntity) ? znsGoodsEntity.getTitle() : goodsI18nEntity.getTitle();
            //查询型号I18N
            List<String> propertyList = Arrays.stream(skuEntity.getPropertyValues().split(",")).toList();
            GoodsSkuI18nQuery query = new GoodsSkuI18nQuery().setSkuId(skuEntity.getId()).setLangCode(languageCode);
            GoodsSkuI18nDo oneOrDefault = goodsSkuI18nService.findOneOrDefault(query, znsGoodsEntity.getDefaultLangCode());
            if (Objects.nonNull(oneOrDefault) && StringUtils.hasText(oneOrDefault.getPropertyValues())) {
                propertyList = Arrays.stream(oneOrDefault.getPropertyValues().split(",")).toList();
            }
            //封转返回数据
            Currency currency = I18nConstant.buildCurrency(skuEntity.getCurrencyCode(), I18nConstant.CurrencyCodeEnum.USD.getCode());
            ShoppingCartListRespDto shoppingCarVo = new ShoppingCartListRespDto(currency.getCurrencySymbol(),skuEntity.getGoodsId(), skuEntity.getId(), skuName, skuEntity.getPic(),
                    propertyList, skuEntity.getOriginalPrice(), skuEntity.getSalePrice(), skuEntity.getStock(), state);

            //查询商品是否支持用户国家
            Boolean isSupportCurrentCountry = countryCode.equals(znsGoodsEntity.getCountryCode());
            shoppingCarVo.setIsSupportCurrentCountry(isSupportCurrentCountry);
            if (!isSupportCurrentCountry){
                //不支持，查询用户的国家名I18n
                CountryQuery countrycQuery = new CountryQuery();
                countrycQuery.setLanguageCode(languageCode);
                countrycQuery.setCountryCode(countryCode);
                List<CountryVo> i18nList = znsCountryService.findByQuery(countrycQuery);
                if (!CollectionUtils.isEmpty(i18nList)){
                    shoppingCarVo.setCountryName(i18nList.get(0).getName());
                }
            }
            //填充折扣
            shoppingCarVo.setDiscount(new GoodsAmountVo(shoppingCarVo.getOriginalPrice(), shoppingCarVo.getSalePrice()).calDiscount());
            result.add(shoppingCarVo);
        }
        return result;
    }

    /**
     * 更新购物车
     *
     * @param req
     * @param userId
     */
    public void updateShoppingCar(UpdateShoppingCartReq req, Long userId) {
        if (CollectionUtils.isEmpty(req.getSkuIds())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "skuId"));
        }
        OrderConstant.CarUpdateTypeEnum updateTypeEnum = OrderConstant.CarUpdateTypeEnum.findByCode(req.getType());
        if (updateTypeEnum == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "updateTypeEnum"));
        }
        if (OrderConstant.CarUpdateTypeEnum.MODIFY == updateTypeEnum && req.getCount() == null) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "count"));
        }
        if (OrderConstant.CarUpdateTypeEnum.REMOVE != updateTypeEnum) {
            //修改购物车，校验sku状态
            for (Long skuId : req.getSkuIds()) {
                chcekSkuStatus(skuId);
            }
        }
        shoppingCartBiz.modifyShoppingCar(userId, req.getSkuIds(), req.getCount(), updateTypeEnum);

    }

    /**
     * 校验sku状态
     */
    private void chcekSkuStatus(Long skuId) {
        ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(skuId);
        if (skuEntity == null) {
            //sku不存在
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.found"), UserError.GOODS_NO_STOCK.getCode());
        }
        if (skuEntity.getStatus() != 1) {
            //sku未上架
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.takenoff"), UserError.GOODS_NO_STOCK.getCode());
        }
        ZnsGoodsEntity goodsEntity = znsGoodsService.findById(skuEntity.getGoodsId());
        if (goodsEntity == null) {
            //spu不存在
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.not.found"), UserError.GOODS_NO_STOCK.getCode());
        }
        if (goodsEntity.getStatus() != 1) {
            //spu未上架
            throw new BaseException(I18nMsgUtils.getMessage("order.generate.product.takenoff"), UserError.GOODS_NO_STOCK.getCode());
        }
    }

    /**
     * 校验sku状态 - 不抛异常版本
     */
    private boolean chcekSkuStatusWithoutException(Long skuId) {
        try {
            ZnsGoodsSkuEntity skuEntity = znsGoodsSkuService.findById(skuId);
            if (skuEntity == null) {
                //sku不存在
                log.info("[chcekSkuStatusWithoutException]---sku不存在,skuId={}", skuId);
                return false;
            }
            if (skuEntity.getStatus() != 1) {
                //sku未上架
                log.info("[chcekSkuStatusWithoutException]---sku未上架,skuId={}", skuId);
                return false;
            }
            ZnsGoodsEntity goodsEntity = znsGoodsService.findById(skuEntity.getGoodsId());
            if (goodsEntity == null) {
                //spu不存在
                log.info("[chcekSkuStatusWithoutException]---spu不存在,skuId={},goodsId={}", skuId, skuEntity.getGoodsId());
                return false;
            }
            if (goodsEntity.getStatus() != 1) {
                //spu未上架
                log.info("[chcekSkuStatusWithoutException]---spu未上架,skuId={},goodsId={}", skuId, skuEntity.getGoodsId());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.warn("[chcekSkuStatusWithoutException]---校验sku状态异常,skuId={}, error={}", skuId, e.getMessage());
            return false;
        }
    }

    /**
     * 按组合包对商品进行分组
     */
    private Map<Long, List<ShoppingCartListRespDto>> groupByPackage(List<ShoppingCartListRespDto> validList, List<CombinationPackageSkuDo> combinationInfos) {

        Map<Long, List<ShoppingCartListRespDto>> result = new LinkedHashMap<>();

        // 1. 构建SKU到组合包信息的映射
        Map<Long, List<CombinationPackageSkuDo>> skuToCombinationMap = combinationInfos.stream()
                .collect(Collectors.groupingBy(CombinationPackageSkuDo::getSkuId));

        // 2. 记录已分配的SKU，避免重复分配
        Set<Long> assignedSkuIds = new HashSet<>();

        // 3. 先处理主品（主品只能在一个组合包）
        for (ShoppingCartListRespDto item : validList) {
            Long skuId = item.getSkuId();
            List<CombinationPackageSkuDo> infos = skuToCombinationMap.get(skuId);

            if (!CollectionUtils.isEmpty(infos)) {
                // 查找主品
                Optional<CombinationPackageSkuDo> mainGoodsInfo = infos.stream().filter(info -> Objects.equals(info.getCombinationGoodsType(), 0)).findFirst();// 0:主品
                if (mainGoodsInfo.isPresent()) {
                    Long packageId = mainGoodsInfo.get().getCombinationPackageId();
                    item.setCombinationGoodsType(0);
                    result.computeIfAbsent(packageId, k -> new ArrayList<>()).add(item);
                    assignedSkuIds.add(skuId);
                }
            }
        }

        // 4. 处理衍生品（按validList顺序，归属到列表最靠前的主商品组合）
        for (ShoppingCartListRespDto item : validList) {
            Long skuId = item.getSkuId();
            // 跳过已分配的SKU
            if (assignedSkuIds.contains(skuId)) {
                continue;
            }
            List<CombinationPackageSkuDo> infos = skuToCombinationMap.get(skuId);

            if (CollectionUtils.isEmpty(infos)) {
                // 如果没有组合包信息，直接跳过
                item.setCombinationGoodsType(-1);
                result.computeIfAbsent(-1L, k -> new ArrayList<>()).add(item);
                assignedSkuIds.add(skuId);
                continue;
            }
            // 查找衍生品
            List<CombinationPackageSkuDo> derivativeInfos = infos.stream().filter(info -> info.getCombinationGoodsType() == 1).collect(Collectors.toList());
            if (!derivativeInfos.isEmpty()) {
                // 按照result中已有组合包的顺序，找到第一个匹配的组合包
                Long selectedPackageId = findMatchingPackageInOrder(derivativeInfos, result);
                if (Objects.nonNull(selectedPackageId)) {
                    item.setCombinationGoodsType(1);
                    result.computeIfAbsent(selectedPackageId, k -> new ArrayList<>()).add(item);
                }else {
                    item.setCombinationGoodsType(-1);
                    result.computeIfAbsent(-1L, k -> new ArrayList<>()).add(item);
                }
                assignedSkuIds.add(skuId);
            }
        }

        // 5. 处理不在组合包的商品（放到-1组合包）
        for (ShoppingCartListRespDto item : validList) {
            if (!assignedSkuIds.contains(item.getSkuId())) {
                item.setCombinationGoodsType(-1);
                result.computeIfAbsent(-1L, k -> new ArrayList<>()).add(item);
            }
        }
        return result;
    }

    /**
     * 按照result中已有组合包的顺序，找到第一个匹配的组合包
     */
    private Long findMatchingPackageInOrder(List<CombinationPackageSkuDo> derivativeInfos,
                                           Map<Long, List<ShoppingCartListRespDto>> result) {
        // 获取衍生品所属的组合包ID列表
        Set<Long> derivativePackageIds = derivativeInfos.stream()
                .map(CombinationPackageSkuDo::getCombinationPackageId)
                .collect(Collectors.toSet());

        // 按照result中的顺序（LinkedHashMap保证插入顺序），找到第一个匹配的组合包
        for (Long packageId : result.keySet()) {
            if (derivativePackageIds.contains(packageId)) {
                return packageId;
            }
        }
        return null;
    }
    /**
     * 处理结果项，记录原始位置和对应的包装对象
     */
    private static class ProcessedItem {
        final int originalPosition;
        final GoodsPackageCartRespDto packageDto;

        ProcessedItem(int originalPosition, GoodsPackageCartRespDto packageDto) {
            this.originalPosition = originalPosition;
            this.packageDto = packageDto;
        }
    }
}
