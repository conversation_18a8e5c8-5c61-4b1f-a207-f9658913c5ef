package com.linzi.pitpat.api.mallservice.controller.h5;


import com.linzi.pitpat.api.BaseH5Controller;
import com.linzi.pitpat.api.mallservice.mananger.OrderPayBussiness;
import com.linzi.pitpat.core.constants.PayConstant;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.awardservice.dto.api.PaymentRequest;
import com.linzi.pitpat.data.awardservice.model.dto.PaymentResultDto;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.request.PayRequest;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.mallservice.dto.request.OrderPayRequest;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsGoodsSkuEntity;
import com.linzi.pitpat.data.mallservice.model.vo.OrderDetailVo;
import com.linzi.pitpat.data.mallservice.model.vo.OrderItemListVo;
import com.linzi.pitpat.data.mallservice.service.ZnsGoodsSkuService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderPayService;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.request.TokenReq;
import com.linzi.pitpat.data.resp.h5.SuccessReduceResp;
import com.linzi.pitpat.data.service.pay.PaymentService;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipShop;
import com.linzi.pitpat.data.userservice.service.vip.VipShopService;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 支付相关接口
 *
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping("/h5/pay")
@Slf4j
@RequiredArgsConstructor
public class PayController extends BaseH5Controller {

    private final ZnsOrderPayService orderPayService;
    private final ZnsOrderService orderService;
    private final ZnsGoodsSkuService goodsSkuService;
    private final OrderPayBussiness orderPayBussiness;
    private final PaymentService paymentService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserAccountDetailService znsUserAccountDetailService;
    private final VipShopService vipShopService;
    private final RedissonClient redissonClient;

    /**
     * 支付
     *
     * @param request
     * @return
     */
    @PostMapping("/payOrder")
    public Result<PaymentResultDto> payOrder(@RequestBody OrderPayRequest request) {
        if (Objects.isNull(request.getPayType())) {
            request.setPayType(PayConstant.PayChannelEnum.PAY_CHANNEL_1.getType()); // 兼容传统购买流程
        }
        ZnsUserEntity user = getLoginUser();
        String languageCode = getLanguageCode();
        //查询订单
        OrderDetailVo vo = orderService.orderInfo(request.getOrderId(), languageCode);
        if (vo.getGmtPayEnd().isBefore(ZonedDateTime.now())) {
            return CommonResult.fail(I18nMsgUtils.getMessage("payment.timeout"));
        }
        RLock lock = redissonClient.getLock(RedisConstants.USER_PAY + user.getId());
        try {
            if (lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                String serialNum = null;
                for (OrderItemListVo item : vo.getItems()) {
                    if (item.getGoodsType() == 1) {
                        ZnsGoodsSkuEntity sku = goodsSkuService.findById(item.getSkuId());
                        Result result = goodsSkuService.checkSku(sku, item.getCount());
                        if (Objects.nonNull(result)) {
                            return result;
                        }
                    } else {
                        // 虚拟商品
                        Long vipShopId = item.getGoodsId();
                        VipShop vipShop = vipShopService.selectVipShopById(vipShopId);
                        serialNum = vipShop.getSerialNum();
                    }
                }

                return orderPayBussiness.initiatePayment(vo, user.getId(), request.getPayType(), request.getPayPassword(), user.getEmailAddressEn(), serialNum);

            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.click.repeatedly"));
            }
        } catch (Exception e) {
            log.error("支付异常，e={}", e);
            if (e instanceof BaseException && ((BaseException) e).getCode() == 3004) {
                return CommonResult.fail(UserError.PAY_PASSWORD_ERROR_LIMIT.getCode(), I18nMsgUtils.getMessage("payment.password.error"));
            }
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.failure"));
        }
    }

    /**
     * 用户充值
     *
     * @param request
     * @return
     */
    @PostMapping("/payByOrderId")
    public Result payOrderByOrderId(@RequestBody OrderPayRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        RLock lock = redissonClient.getLock(RedisConstants.USER_PAY + loginUser.getId());
        try {
            if (lock.tryLock(1, 3, TimeUnit.SECONDS)) {
                return orderPayBussiness.initiatePaymentByOrder(request.getAmount(), loginUser.getId());
            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.recharge.inprogress"));
            }
        } catch (Exception e) {
            log.error("充值异常，e={}", e);
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("payment.recharge.failed"));
        }
    }

    /**
     * 确认扣款
     *
     * @param
     * @return
     */
    @PostMapping("/successPayment")
    public Result successPayment(@RequestBody TokenReq token) {
        if (!StringUtils.hasText(token.getToken())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        return orderPayService.capturePayment(token.getToken());
    }

    /**
     * 取消扣款
     *
     * @param
     * @return
     */
    @PostMapping("/failPayment")
    public Result failPayment(@RequestBody TokenReq token) {
        if (!StringUtils.hasText(token.getToken())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        return orderPayService.failPayment(token.getToken());
    }


    /**
     * 扣款成功
     *
     * @param
     * @return
     */
    @PostMapping(value = "/successReduceDetail")
    public Result<SuccessReduceResp> successReduceDetail(@RequestBody PaymentRequest request) {
        ZnsUserAccountDetailEntity accountDetailEntity = znsUserAccountDetailService.selectAccountDetailByBillNo(request.getBillNo());
        SuccessReduceResp resp = new SuccessReduceResp();
        resp.setAmount(accountDetailEntity.getAmount());
        resp.setCurrency(getUserCurrency());
        return CommonResult.success(resp);
    }

    /**
     * 扣款成功
     *
     * @param
     * @return
     */
    @PostMapping(value = "/successReduceAmount")
    public Result successReduceAmount(@RequestBody PaymentRequest request) {
        String token = request.getBillNo();
        if (!StringUtils.hasText(token)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        return userAccountService.capturePaymentByBillNo(token);
    }

    /**
     * 扣款失败
     *
     * @param
     * @return
     */
    @PostMapping(value = "/failReduceAmount")
    public Result failReduceAmount(@RequestBody PaymentRequest request) {
        String token = request.getBillNo();
        if (!StringUtils.hasText(token)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        return userAccountService.failPaymentByBillNo(token);
    }

    /**
     * 提现申请
     *
     * @param request
     * @return
     * @throws IOException
     */
    @PostMapping("/cash")
    public Result cash(@RequestBody PayRequest request) throws IOException {
        ZnsUserEntity loginUser = getLoginUser();
        return paymentService.cash(loginUser, request, false);
    }

}
