package com.linzi.pitpat.api.userservice.controller.app;

import com.linzi.pitpat.api.userservice.manager.UserManager;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkMatchOpponentUserBeanVo;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.user.converter.UserAddConverter;
import com.linzi.pitpat.data.user.converter.UserConverter;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserAddEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.ZnsUserAddService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.user.api.dto.FinishCourseUserDto;
import com.linzi.pitpat.user.api.dto.UserAddEntityDto;
import com.linzi.pitpat.user.api.dto.UserEntityDto;
import com.linzi.pitpat.user.api.dto.request.CourseUserQueryRequestDto;
import com.linzi.pitpat.user.api.dto.request.PkMatchRequestDto;
import com.linzi.pitpat.user.api.dto.request.UserQueryDto;
import com.linzi.pitpat.user.api.dto.request.UserRobotStatusRequestDto;
import com.linzi.pitpat.user.api.dto.response.OfflinePkMatchOpponentUserResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * user api 控制器
 *
 * @since May 23 20424
 */
@Slf4j
@RestController
@RequestMapping("/app/user")
@RequiredArgsConstructor
public class UserController {

    private final ZnsUserService userService;
    private final UserManager userManager;
    private final UserConverter userConverter;
    private final ZnsUserAddService userAddService;
    private final UserAddConverter userAddConverter;
    private final ISysConfigService sysConfigService;
    private final UserLevelService userLevelService;
    private final UserBizService userBizService;
    /**
     * 根据邮箱查找用户
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/findByEmail")
    public Result<UserEntityDto> findByEmail(@RequestBody UserQueryDto queryDto) {
        ZnsUserEntity user = userService.findByEmail(queryDto.getEmail());
        UserEntityDto dto = userConverter.toDto(user);
        return CommonResult.success(dto);
    }

    /**
     * 根据用户id查找用户
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/findById")
    public Result<UserEntityDto> findById(@RequestBody UserQueryDto queryDto) {
        ZnsUserEntity user = userService.findById(queryDto.getId());
        UserEntityDto dto = userConverter.toDto(user);
        return CommonResult.success(dto);
    }

    /**
     * 根据用户id查找用户
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/listByIds")
    public Result<List<UserEntityDto>> listByIds(@RequestBody UserQueryDto queryDto) {
        List<ZnsUserEntity> list = userService.findByIds(queryDto.getIds());
        List<UserEntityDto> dtoList = userConverter.toDtoList(list);
        return CommonResult.success(dtoList);
    }

    /**
     * 修改用户
     *
     * @param request
     */
    @PostMapping("/updateById")
    public Result updateById(@RequestBody UserEntityDto request) {
        ZnsUserEntity znsUserEntity = userConverter.toEntity(request);
        userService.update(znsUserEntity);
        if (request.getGender() != null) {
            userManager.updateGender(znsUserEntity.getId(), request.getGender());
        }
        return CommonResult.success();
    }

    /**
     * 创建机器人
     *
     * @param request
     * @return
     */
    @PostMapping("/doCreateRobot")
    public Result<UserEntityDto> doCreateRobot(@RequestBody UserEntityDto request) {
        ZnsUserEntity znsUserEntity = userConverter.toEntity(request);
        ZnsUserEntity user = userService.doCreateRobot(znsUserEntity);
        return CommonResult.success(userConverter.toDto(user));
    }

    /**
     * 查询课程结束用户明细
     *
     * @param request
     * @return
     */
    @PostMapping("/selectEndCourseByCourseIdListNew")
    public Result<List<FinishCourseUserDto>> selectEndCourseByCourseIdListNew(@RequestBody CourseUserQueryRequestDto request) {
        List<FinishCourseUserDto> dtoList = userService.selectEndCourseByCourseIdListNew(request.getCourseId(), request.getUserId());
        return CommonResult.success(dtoList);
    }

    @PostMapping("/selectRobotUserListLimit")
    public Result<List<UserEntityDto>> selectRobotUserListLimit(@RequestBody UserQueryDto request) {
        List<ZnsUserEntity> userEntityList = userBizService.findRandomRobotListLimit(request.getLimit());
        return CommonResult.success(userConverter.toDtoList(userEntityList));
    }

    @PostMapping("/selectRobotCount")
    public Result<Long> selectRobotCount() {
        return CommonResult.success(userService.selectRobotCount());
    }

    @PostMapping("/insertZnsUserAddEntity")
    public Result insertZnsUserAddEntity(@RequestBody UserAddEntityDto request) {
        ZnsUserAddEntity znsUserAddEntity = userAddConverter.toEntity(request);
        userAddService.insertZnsUserAddEntity(znsUserAddEntity);
        return CommonResult.success();
    }

    @PostMapping("/randomUserForOfflinePkMatch")
    public Result<List<OfflinePkMatchOpponentUserResponseDto>> randomUserForOfflinePkMatch(@RequestBody PkMatchRequestDto request) {
        List<OfflinePkMatchOpponentUserBeanVo> list = userService.randomUserForOfflinePkMatch(request.getUserId(),
                request.getEquipments(), request.getMatchFriend());
        return CommonResult.success(userConverter.toOfflinePkMatchOpponentUserResponseDto(list));
    }

    @PostMapping("/getShortageRobotUser")
    public Result<UserEntityDto> getShortageRobotUser(@RequestBody UserQueryDto request) {
        if (Objects.nonNull(request.getIsFree())) {
            ZnsUserEntity user = userService.getShortageRobotUser(request.getIsFree());
            return CommonResult.success(userConverter.toDto(user));
        } else {
            ZnsUserEntity user = userService.getShortageRobotUser(request.getId());
            return CommonResult.success(userConverter.toDto(user));
        }
    }

    @PostMapping("/updateZnsUserRobotStatus")
    public Result updateZnsUserRobotStatus(@RequestBody UserRobotStatusRequestDto request) {
        userService.updateZnsUserRobotStatus(request.getStatus(), request.getUserId());
        return CommonResult.success();
    }
}
