package com.linzi.pitpat.api.controller.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.dto.response.LoginPageRespDto;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityHighlightsDo;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityHighlightsService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.entity.po.GetResourceConfigPo;
import com.linzi.pitpat.data.enums.MDEventTypeEnum;
import com.linzi.pitpat.data.mallservice.model.entity.ZnsAddressEntity;
import com.linzi.pitpat.data.mallservice.service.ZnsAddressService;
import com.linzi.pitpat.data.request.AddressLibRequest;
import com.linzi.pitpat.data.request.ContactusMessageRequest;
import com.linzi.pitpat.data.request.GetVersionRequest;
import com.linzi.pitpat.data.request.MaidianLogRequest;
import com.linzi.pitpat.data.service.activity.MaidianLogService;
import com.linzi.pitpat.data.systemservice.dto.request.AppGameResourceUpgradeQueryReq;
import com.linzi.pitpat.data.systemservice.dto.request.AppRnUpgradeQueryNewDto;
import com.linzi.pitpat.data.systemservice.dto.request.AppRnUpgradeQueryNewReqDto;
import com.linzi.pitpat.data.systemservice.dto.response.AppGameUpgradeDto;
import com.linzi.pitpat.data.systemservice.dto.response.AppSupportInfoResponse;
import com.linzi.pitpat.data.systemservice.dto.response.AppVersionUpDto;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.manager.api.SysConfigManager;
import com.linzi.pitpat.data.systemservice.mapper.AppUpgradeDao;
import com.linzi.pitpat.data.systemservice.model.entity.AppRnUpgrade;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.query.AppRnUpgradeQuery;
import com.linzi.pitpat.data.systemservice.model.vo.ContactUsListVO;
import com.linzi.pitpat.data.systemservice.model.vo.CustomerServiceVo;
import com.linzi.pitpat.data.systemservice.service.AppCommonConfigService;
import com.linzi.pitpat.data.systemservice.service.AppRnUpgradeService;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserActiveRuleConfigService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.MapListVo;
import com.linzi.pitpat.data.vo.RegisterEmailTips;
import com.linzi.pitpat.data.vo.system.AgreementListVo;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.trace.api.dto.request.TraceLogCreateRequestDto;
import com.linzi.pitpat.trace.api.interfaces.TraceLogApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * app系统管理
 *
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/app/system", "/h5/system"})
@Slf4j
public class AppSystemController extends BaseAppController {
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsAddressService addressService;
    @Resource
    private MaidianLogService maidianLogService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Autowired
    private AppCommonConfigService appCommonConfigService;
    @Resource
    private ZnsUserService userService;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private AppUpgradeService appUpgradeService;
    @Resource
    private ZnsUserLoginLogService znsUserLoginLogService;

    @Resource
    private TraceLogApi traceLogApi;
    @Resource
    private ActivityHighlightsService activityHighlightsService;

    /**
     * 获取协议列表
     *
     * @return
     */
    @Autowired
    private AppUpgradeDao appUpgradeDao;
    @Resource(name = "trackerMaiDianExecutor")
    private ThreadPoolTaskExecutor executor;
    @Autowired
    private UserActiveRuleConfigService userActiveRuleConfigService;
    @Autowired
    private SysConfigManager sysConfigManager;

    /**
     * 协议列表
     *
     * @return
     */
    @PostMapping("/agreement/list")
    public Result<MapListVo<AgreementListVo>> list() {
        MapListVo<AgreementListVo> data = new MapListVo();
        String config = sysConfigService.selectConfigByKey("app.agreement");
        if (StringUtils.hasText(config)) {
            data.setList(JsonUtil.readList(config, AgreementListVo.class));
        } else {
            data.setList(new ArrayList<>());
        }
        return CommonResult.success(data);
    }

    /**
     * 获取最新版本号
     *
     * @param requestMap
     * @return
     */
    @PostMapping("/version")
    public Result getVersion(@RequestBody GetVersionRequest requestMap) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appType = request.getHeader("appType");
        String appVersion = request.getHeader("appVersion");
        AppVersionUpDto appVersionUpDto = appUpgradeService.selectUpgradeVersion(appType, appVersion);
        appUpgradeService.forceUpgrade(appVersionUpDto, getLoginUser(), appType, appVersion);
        return CommonResult.success(appVersionUpDto);
    }

    /**
     * 地址库
     *
     * @param addressLibRequest
     * @return
     */
    @PostMapping("/addressLib")
    public Result addressLib(@RequestBody AddressLibRequest addressLibRequest) {
        Long parentId = addressLibRequest.getParentId();
        Integer level = addressLibRequest.getLevel();

        Map<String, Object> map = new HashMap<>();
        List<ZnsAddressEntity> list = addressService.getAddressLib(parentId, level);
        map.put("list", list);
        return CommonResult.success(map);
    }

    /**
     * 埋点
     *
     * @param request
     * @return
     */
    @PostMapping("/maidian/log")
    public Result maidianLog(@RequestBody MaidianLogRequest request) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest httpServletRequest = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String eventType = request.getEventType();
        //if (Objects.isNull(eventType) || Objects.equals(MDEventTypeEnum.OPEN_APP.getType(), eventType)){
        //    //addOpenAppLog(request,httpServletRequest);
        //} else if (Objects.equals(MDEventTypeEnum.HELP_CENTER_QUESTION_CLICK.getType(), eventType)){
        //    //addQuestionClickLog( request,httpServletRequest);
        //} else if (Objects.equals(MDEventTypeEnum.SOFT_LOGIN_APP.getType(), eventType)){
        //    //TODO 该类型不做分析日志记录
        //    //addLoginAppLog( request,httpServletRequest);
        //} else {
        //    maidianLogService.addLog(request,httpServletRequest);
        //    //if (Constants.PAGE_EVENTDETAILPAGE_NEWPEOPLEPK.equals(request.getPage())) {
        //    //    executor.submit(() -> appNewPersonPkPageBusiness.addRecord(request));
        //    //}
        //}
        maidianLogService.addLog(request, httpServletRequest);
        //数据双写至新的服务, 触发其他业务逻辑，比如用户登录活动逻辑，里程碑逻辑，走 trace-log-service 到 mq 去处理
        try {
            String switchConfig = sysConfigService.selectConfigByKey("trace_log_config");
            Map<String, Boolean> config = JsonUtil.readValue(switchConfig, new TypeReference<>() {
            });
            if (Objects.equals(config.get("enable"), true)) {
                TraceLogCreateRequestDto traceLogCreateRequestDto = new TraceLogCreateRequestDto();
                BeanUtils.copyProperties(request, traceLogCreateRequestDto);

                log.info("request dto={}", traceLogCreateRequestDto);
                traceLogApi.create(traceLogCreateRequestDto);
            } else {
                log.info("未开启数据双写");
            }
        } catch (Exception e) {
            log.error("trace log 数据双写失败，msg={}", e.getMessage(), e);
        }
        return CommonResult.success();
    }


    private void addLoginAppLog(MaidianLogRequest request, HttpServletRequest httpServletRequest) {
        String emailAddress = HeaderUtil.getEmail(httpServletRequest);

        //记录登录日志
        UserRequest userRequest = new UserRequest();
        userRequest.setEmailAddress(emailAddress);
//        userRequest.setEmailAddressEn(emailAddress);
        userService.encapsulateUserRequest(userRequest, httpServletRequest);
        userRequest.setLoginType(4);
        ZnsUserEntity user = userService.findByEmail(emailAddress);
        if (Objects.nonNull(user)) {
            userRequest.setId(user.getId());
        }
        znsUserLoginLogService.saveLog(userRequest);
        if (Objects.nonNull(user)) {
            //用户登录活动逻辑
            executor.submit(() -> userActiveRuleConfigService.loginUserActive(user));
        }
    }

    /**
     * 帮助中心点击日志
     */
    private void addQuestionClickLog(MaidianLogRequest request, HttpServletRequest httpServletRequest) {
        if (Objects.isNull(request.getRefId())) {
            return;
        }
        request.setRefType(2);
        request.setEventType(MDEventTypeEnum.HELP_CENTER_QUESTION_CLICK.getType());
        maidianLogService.addLog(request, httpServletRequest);
    }

    private void addOpenAppLog(MaidianLogRequest request, HttpServletRequest httpServletRequest) {
        String emailAddress = HeaderUtil.getEmail(httpServletRequest);
        String user_id_key = ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + emailAddress;
        String userIdStr = redisUtil.get(user_id_key);
        Long userId;
        if (StringUtils.hasText(userIdStr)) {
            userId = Long.parseLong(userIdStr);
        } else {
            ZnsUserEntity user = userService.findByEmail(emailAddress);
            if (Objects.nonNull(user)) {
                userId = user.getId();
            } else {
                return;
            }
        }


        //查询是否有运动中app，
        ZnsUserRunDataDetailsEntity lastRunDataDetailByStatus = userRunDataDetailsService.getLastRunDataDetailByStatus(userId, 0);
        if (Objects.isNull(lastRunDataDetailByStatus)) {
            return;
        }

        //记录登录日志
        UserRequest userRequest = new UserRequest();
        userRequest.setEmailAddress(emailAddress);
        userService.encapsulateUserRequest(userRequest, httpServletRequest);
        userRequest.setLoginType(3);
        userRequest.setId(userId);
        znsUserLoginLogService.saveLog(userRequest);

        //查询两分钟内有更新数据
        if (lastRunDataDetailByStatus.getLastTime().compareTo(ZonedDateTime.now().minusMinutes(2)) > 0) {
            request.setRefType(1);
            request.setRefId(lastRunDataDetailByStatus.getId());
            request.setEventType("RUN_LOGIN");
            maidianLogService.addLog(request, httpServletRequest);
        }
    }

    /**
     * 联系我页面信息
     *
     * @return
     */
    @PostMapping("/contactus")
    public Result getContactUsInfo() {
        ContactUsListVO result = appCommonConfigService.getContractUsInfo(getLoginUser());
        return CommonResult.success(result);
    }

    /**
     * 聊天机器人发送的第一句话
     *
     * @param contactusMessageRequest
     * @return
     */
    @PostMapping("/contactusMessage")
    public Result contactusMessage(@RequestBody ContactusMessageRequest contactusMessageRequest) {
        if ("administrator".equals(contactusMessageRequest.getFromUserId())) {
            Map<String, Object> map = new HashMap<>();
            map.put("content", "");
            return CommonResult.success(map);
        }
        String langCode = I18nMsgUtils.getLangCode();

        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.chat_robot_user_first_service.getCode());
        String content = sysConfig.getConfigValue();
        List<Map<String, Object>> mapList = JsonUtil.readValue(content, new TypeReference<List<Map<String, Object>>>() {
        });
        var hashMap = mapList.stream().filter(map -> Objects.nonNull(map.get(langCode))).findFirst().orElse(null);
        String result = hashMap == null ? "Dear PitPater  Glad to be a service" : (String) hashMap.get(langCode);
        Map<String, Object> map = new HashMap<>();
        map.put("content", result);
        return CommonResult.success(map);
    }

    /**
     * 获取系统配置
     *
     * @return
     */
    @PostMapping("/config")
    public Result config() {
        ZnsUserEntity loginUser = getLoginUser();
        log.info("当前登录用户信息为：{}", loginUser);
        return CommonResult.success(sysConfigManager.findApiConfig(loginUser, getAppVersion(), getAppType()));
    }

    /**
     * 保存系统配置
     *
     * @param data
     * @return
     */
    @PostMapping("/add/config")
    public Result addConfig(Map<String, Object> data) {
        String configKey = data.get("configKey").toString();
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(configKey);
        if (sysConfig == null) {
            sysConfig = new SysConfig();
            sysConfig.setConfigKey(configKey);
            sysConfig.setConfigType("sys");
            sysConfig.setConfigValue(JsonUtil.writeString(data));
            sysConfig.setConfigName("sys");
            sysConfig.setCreateTime(ZonedDateTime.now());
            sysConfig.setUpdateTime(ZonedDateTime.now());
            sysConfigService.insertConfig(sysConfig);
        } else {
            sysConfig.setConfigValue(JsonUtil.writeString(data));
            sysConfigService.updateConfig(sysConfig);
        }
        return CommonResult.success(data);
    }

    /**
     * 根据key获取系统配置
     *
     * @param data
     * @return
     */
    @PostMapping("/get/config")
    public Result getConfig(@RequestBody Map<String, Object> data) {

        String configKey = data.get("configKey").toString();

        String value = sysConfigService.selectSysConfigByKey(configKey).getConfigValue();

        Map<String, Object> map = JsonUtil.readValue(value);

        return CommonResult.success(map);
    }


    /**
     * 获取资源配置
     *
     * @param po
     * @return
     */
    @PostMapping("/getResourceConfig")
    public Result getResourceConfig(@RequestBody GetResourceConfigPo po) {
        if (StringUtil.isEmpty(po.getResourceKey())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }
        String value = sysConfigService.selectConfigByKey(po.getResourceKey());
        try {
            //兼容历史数据
            if (ConfigKeyEnums.register_email_tips.getCode().equals(po.getResourceKey())) {
                Map<String, Object> data = new HashMap<>();
                SysConfig lianxi = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.register_email_tips.getCode());
                RegisterEmailTips emailTips = JsonUtil.readValue(lianxi.getConfigValue(), RegisterEmailTips.class);

                data.put("emailTips", emailTips.getEmailTips());
                data.put("emailTipsSwitch", emailTips.getEmailTipsSwitch());
                data.put("mobileTipsSwitch", emailTips.getMobileTipsSwitch());
                data.put("mobileTips", emailTips.getMobileTips());

                //如果其他地方配置了，用其他地方的
                SysConfig contractUtel = sysConfigService.selectSysConfigByKey("contract.us.tel");
                List<ContactUsListVO.Channel> channels = JsonUtil.readList(contractUtel.getConfigValue(), ContactUsListVO.Channel.class);
                for (ContactUsListVO.Channel channel : channels) {
                    if (channel.getTitle().equals("Phone Call")) {
                        data.put("mobileTips", channel.getRefValue());
                    }
                }

                return CommonResult.success(data);
            } else if (ConfigKeyEnums.CONNECT_DEVICE_BLUETOOTH_TIPS.getCode().equals(po.getResourceKey())) {
                List<Map> maps = JsonUtil.readList(value, Map.class);
                String languageCode = LocaleContextHolder.getLocale().toString();
                Map map = maps.stream().filter(e -> languageCode.equals(e.get("code"))).findFirst().orElse(null);
                String content = "";
                if (Objects.nonNull(map)) {
                    content = map.get("content").toString();
                }
                Map<String, String> resultMap = new HashMap<>();
                resultMap.put(po.getResourceKey(), content);
                return CommonResult.success(resultMap);
            } else {
                if (value.startsWith("{") || value.startsWith("[")) {
                    Map<String, Object> map = JsonUtil.readValue(value);
                    return CommonResult.success(map);
                }
            }
        } catch (Exception e) {
            log.info("getResourceConfig 解析json错误");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("value", value);
        return CommonResult.success(map);
    }

    @Autowired
    private AppRnUpgradeService appRnUpgradeService;

    /**
     * 获取最新的rn更新包 1122
     *
     * @param appRnUpgradeQueryNewReqDto
     * @return
     */
    @PostMapping("/getRnZipConfig")
    public Result<AppRnUpgradeQueryNewDto> getRnZipConfig(@RequestBody AppRnUpgradeQueryNewReqDto appRnUpgradeQueryNewReqDto) {
        AppRnUpgradeQueryNewDto appRnUpgradeQueryNewDto = new AppRnUpgradeQueryNewDto();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        String appTypeResult = appRnUpgradeQueryNewReqDto.getAppType();
        Integer appType = getAppType();
        Integer appVersion = getAppVersion();
        if (Objects.nonNull(appType)) {
            appTypeResult = appType != 1 ? "I" : "A";
        }
        AppRnUpgradeQuery appRnUpgradeQuery = AppRnUpgradeQuery.builder().appType(appTypeResult).minVersion(appVersion).maxVersion(appVersion).gmtRelease(DateUtil.getNowDate()).status(YesNoStatus.YES.getCode()).type(1).build();
        AppRnUpgrade one = appRnUpgradeService.findByQuery(appRnUpgradeQuery);
        if (Objects.nonNull(one)) {
            BeanUtils.copyProperties(one, appRnUpgradeQueryNewDto);
            String jsonStr = one.getConfigJson().replaceAll("\\\\n|\\\\r|\\\\t", "");
            Map<String, Object> jsonObject = JsonUtil.readValue(jsonStr);
            appRnUpgradeQueryNewDto.setConfigJson(JsonUtil.writeString(jsonObject));
        }
        return CommonResult.success(appRnUpgradeQueryNewDto);
    }

    /**
     * 获取客服
     *
     * @param po
     * @return
     * @tag 2.6
     */
    @PostMapping("/getCustomerService")
    public Result<CustomerServiceVo> getCustomerService(@RequestBody GetResourceConfigPo po) {
        CustomerServiceVo vo = new CustomerServiceVo();
        String value = sysConfigService.selectConfigByKey(po.getResourceKey());
        if (ConfigKeyEnums.COMPETITION_ARBITRATION_ROBOT.getCode().equals(po.getResourceKey())) {
            Map<String, Object> data = JsonUtil.readValue(value, Map.class);
            Long userId = MapUtils.getLong(data, "chatId");
            vo.setUserId(userId);
            ZnsUserEntity znsUser = userService.findById(userId);
            if (Objects.nonNull(znsUser)) {
                vo.setNickname(znsUser.getFirstName());
                vo.setHeadPortrait(znsUser.getHeadPortrait());
            }
            vo.setGreetings(MapUtils.getString(data, "greetings"));
        } else if (ConfigKeyEnums.chat_robot_user_ids.getCode().equals(po.getResourceKey())) {
            String[] split = value.split(",");
            String chatRobotUserId = split[0];
            long userId = Long.parseLong(chatRobotUserId);
            vo.setUserId(userId);
            ZnsUserEntity znsUser = userService.findById(userId);
            if (Objects.nonNull(znsUser)) {
                vo.setNickname(znsUser.getFirstName());
                vo.setHeadPortrait(znsUser.getHeadPortrait());
            }
        } else if (ConfigKeyEnums.chat_robot_vip_user_ids.getCode().equals(po.getResourceKey())) {
            String[] split = value.split(",");
            String chatRobotUserId = split[0];
            long userId = Long.parseLong(chatRobotUserId);
            vo.setUserId(userId);
            ZnsUserEntity znsUser = userService.findById(userId);
            if (Objects.nonNull(znsUser)) {
                vo.setNickname(znsUser.getFirstName());
                vo.setHeadPortrait(znsUser.getHeadPortrait());
            }
        }
        return CommonResult.success(vo);
    }

    /**
     * 获取登录页 配置数据
     *
     * @tag 4.4
     */
    @PostMapping("/getLoginPageData")
    public Result<LoginPageRespDto> getLoginPageData() {
        LoginPageRespDto resp = new LoginPageRespDto();

        //赛事锦集开关
        ActivityHighlightsDo defaultHighlight = activityHighlightsService.findDefaultHighlight();
        if (Objects.nonNull(defaultHighlight)) {
            resp.setHighlightsActivityId(defaultHighlight.getActivityId());
        }

        //商城开关
        String mallSwitchKey = "mall_home_config_switch";
        SysConfig mallConfig = sysConfigService.selectSysConfigByKey(mallSwitchKey);
        Integer mallSwitch = (mallConfig == null || !StringUtils.hasText(mallConfig.getConfigValue())) ? 0 : Integer.parseInt(mallConfig.getConfigValue());
        resp.setMallSwitch(mallSwitch);

        //app首页客服电话
        String customerMobileNumberKey = "app_login_customer_mobile_number";
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(customerMobileNumberKey);
        if (sysConfig != null) {
            resp.setCustomerMobileNumber(sysConfig.getConfigValue());
        }
        return CommonResult.success(resp);
    }


    /**
     * 获取最新的game资源包
     *
     * @param appRnUpgradeQueryNewReqDto
     * @return
     */
    @PostMapping("/getGameResource")
    public Result<AppGameUpgradeDto> getGameResource(@RequestBody AppGameResourceUpgradeQueryReq appRnUpgradeQueryNewReqDto) {
        Integer appVersion = getAppVersion();
        AppGameUpgradeDto appGameUpgradeDto = new AppGameUpgradeDto();
        String appTypeResult = appRnUpgradeQueryNewReqDto.getAppType();
        Integer appType = getAppType();
        if (Objects.nonNull(appType)) {
            appTypeResult = appType != 1 ? "I" : "A";
        }
        AppRnUpgradeQuery appRnUpgradeQuery = AppRnUpgradeQuery.builder()
                .appType(appTypeResult).gmtRelease(DateUtil.getNowDate()).minVersion(appVersion)
                .status(YesNoStatus.YES.getCode()).type(2).build();
        AppRnUpgrade one = appRnUpgradeService.findByQueryForGame(appRnUpgradeQuery);
        if (Objects.nonNull(one)) {
            BeanUtils.copyProperties(one, appGameUpgradeDto);
        }
        return CommonResult.success(appGameUpgradeDto);
    }

    /**
     * 获取全部客服电话
     */
    @PostMapping("/getSupportNumber")
    public Result<AppSupportInfoResponse> getSupportDetail() {
        AppSupportInfoResponse appSupportInfoResponse = sysConfigService.selectConfigVoByKey(ConfigKeyEnums.SUPPORT_PHONE_NUMBER.getCode(), AppSupportInfoResponse.class);
        return CommonResult.success(appSupportInfoResponse);
    }
}






