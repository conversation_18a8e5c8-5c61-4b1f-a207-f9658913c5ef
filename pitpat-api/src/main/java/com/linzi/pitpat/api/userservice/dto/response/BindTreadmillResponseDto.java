package com.linzi.pitpat.api.userservice.dto.response;

import com.linzi.pitpat.data.equipmentservice.enums.TreadmillConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentSpeedConfigDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.vo.user.UserThemeListVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/28 11:32
 */
@Data
@Accessors(chain = true)
public class BindTreadmillResponseDto {

    private Integer firstBindTop;

    private String bindSuccessImage;

    private String bindTopText;
    private String bindBottomText;
    private String logoImage;
    private String brandName;
    private UserThemeListVo brandTheme;
    private Integer popCount;
    private boolean showNfcPop;
    private Integer factoryRcStatus;
    private Integer measureUnit;
    /**
     * 是否异常公英制设备
     */
    private Boolean measureUnitIsError;

    /**
     * 【4.4新增】是否显示质保弹框，true：弹框，false：不弹
     */
    private Boolean showQualityPop;

    /**
     * 【4.4新增】质保天数
     */
    private Integer qualityDayNum;

    /**
     * 【4.4新增】质保天数类型,1:新发放质保的天数，2：已有质保剩余天数
     */
    private Integer dayNumType;
    /**
     * 【4.4新增】最大速度(走步形态，公里/小时)
     */
    private BigDecimal maxSpeed;
    /**
     * 【4.4新增】最大速度(跑步形态，公里/小时)
     */
    private BigDecimal maxMaxSpeed;
    /**
     * 【4.4新增】最大速度(走步形态，英里/小时)
     */
    private BigDecimal maxSpeedMph;

    /**
     * 【4.4新增】最大速度(跑步形态，英里/小时)
     */
    private BigDecimal maxMaxSpeedMph;

    /**
     * 批次公英值单位 0公制，1英制 2公英制
     */
    private Integer batchMeasureUnit;
    /**
     * 升级状态，active：进行中, cancelled：已取消, finished：已完成, triggered：触发 ,fail：失败
     *
     * @see TreadmillConstant.UpgradeStatusEnum
     */
    private String upgradeStatus;

    // 运动直径 厘米（目前脚踏车用）
    private BigDecimal sportDiameter;
    /**
     * 是否支持设备锁 0：不支持 1：支持，默认不支持
     * @since 4.6.5
     */
    private Integer supportDeviceLock;

    /**
     * 单车配置
     * @since 4.7.3
     */
    private EquipmentSpeedConfigDo equipmentSpeedConfigDo;

    /**
     * 是否可以share 不可以 = la 设备 可以 = online 设备
     * @see com.linzi.pitpat.core.constants.enums.YesNoStatus
     */
    private Integer isAllowShare;

    public void setSupportDeviceLock(ZnsTreadmillEntity treadmillEntity) {
        String productCode = treadmillEntity.getProductCode();
        if ("R2".equals(productCode) || "S2".equals(productCode)) {
            this.supportDeviceLock = 1;
        } else {
            this.supportDeviceLock = 0;
        }
    }
}
