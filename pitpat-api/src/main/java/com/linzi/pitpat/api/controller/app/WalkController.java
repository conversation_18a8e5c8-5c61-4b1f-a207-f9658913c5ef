package com.linzi.pitpat.api.controller.app;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.manager.signup.ActivitySignUpManager;
import com.linzi.pitpat.api.dto.response.JudgeAllResponse;
import com.linzi.pitpat.api.equipment.mananger.EquipmentJudgeManager;
import com.linzi.pitpat.api.userservice.manager.UserDeviceApiManager;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.ISelect;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.constant.enums.MainActivityTypeEnum;
import com.linzi.pitpat.data.activityservice.dto.api.request.signup.RamCheckRequest;
import com.linzi.pitpat.data.activityservice.model.dto.ActivityEquipmentConfigDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityConfigEntity;
import com.linzi.pitpat.data.activityservice.service.ActivityParamsService;
import com.linzi.pitpat.data.activityservice.service.MainActivityService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityConfigService;
import com.linzi.pitpat.data.awardservice.dto.api.ScoreListDto;
import com.linzi.pitpat.data.awardservice.mapper.ActivityUserScoreDao;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityUserScoreDto;
import com.linzi.pitpat.data.bussiness.RotationAreaBizService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.courseservice.model.entity.ZnsCourseEntity;
import com.linzi.pitpat.data.courseservice.service.ZnsCourseService;
import com.linzi.pitpat.data.entity.po.WalkIdDto;
import com.linzi.pitpat.data.entity.po.WalkPageHomeResp;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentJudgeDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.WalkDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.WalkJudageDto;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentBrandConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillNfcLog;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsEquipmentProductionBatchEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentListVo;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentBrandConfigService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigMoreService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillNfcLogService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.PPageUtils;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 走步页面配置
 *
 * <AUTHOR>
 * @date 2023/7/1
 * 4 17:53
 */
@RestController
@RequestMapping({"/app/walk", "/h5/walk"})
@Slf4j
@RequiredArgsConstructor
public class WalkController extends BaseAppController {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ZnsUserEquipmentService userEquipmentService;

    @Autowired
    private ActivityUserScoreDao activityUserScoreDao;

    @Autowired
    private ZnsRunActivityConfigService znsRunActivityConfigService;

    @Autowired
    private EquipmentJudgeManager equipmentJudgeManager;

    @Autowired
    private ZnsTreadmillService znsTreadmillService;

    @Autowired
    private ZnsEquipmentProductionBatchService znsEquipmentProductionBatchService;

    @Autowired
    private EquipmentBrandConfigService equipmentBrandConfigService;

    @Autowired
    private EquipmentConfigService equipmentConfigService;

    @Autowired
    private ZnsCourseService znsCourseService;
    @Resource
    private RotationAreaBizService rotationAreaBizService;

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private TreadmillNfcLogService treadmillNfcLogService;

    @Resource
    private MainActivityBizService mainActivityBizService;
    @Resource
    private EquipmentConfigMoreService equipConfigMoreService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ActivityParamsService activityParamsService;
    @Resource
    private ISysConfigService configService;
    @Resource
    private ActivitySignUpManager activitySignUpManager;

    private final UserDeviceApiManager userDeviceApiManager;

    private final MainActivityService mainActivityService;


    /**
     * 首页走步赛事(已废弃)
     *
     * @param dto
     * @return
     */
    @PostMapping("/pageHome")
    @Deprecated
    public Result<WalkPageHomeResp> pageHome(@RequestBody WalkDto dto) {
//        SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.version_app_2_0_5.getCode());
//        Map<String, Object> data = JSONObject.parseObject(sysConfig.getConfigValue(), Map.class);
//        boolean checkUser = appUpgradeService.isCheckVersion(getAppType(),getAppVersion());
//
//        List<RotationArea> rotationAreas = operationalActivityService.selectHomeMapList(getLoginUser(),getAppVersion(),checkUser,getAppType(),2);
//        WalkPageHomeResp resp = new WalkPageHomeResp();
//        resp.setRotationArea(rotationAreas);
//        resp.setWalkConnectImage(data.get("walkConnectImage") + "");
//        resp.setWalkNotConnectImage(data.get("walkNotConnectImage") + "");
//        ZonedDateTime startTime = DateUtil.startOfDate(ZonedDateTime.now());
//        ZonedDateTime endTime = DateUtil.endOfDate(ZonedDateTime.now());
//        BigDecimal distance = znsUserRunDataDetailsDao.selectRunDistanceByDateRunType(startTime, endTime, dto.getUserId(), 2);
//        Integer runTime = znsUserRunDataDetailsDao.selectRunRunTimeByDateRunType(startTime, endTime, dto.getUserId(), 2);
//        BigDecimal kilocalorie = znsUserRunDataDetailsDao.selectRunKilocalorieByDateRunType(startTime, endTime, dto.getUserId(), 2);
//
//        //里程跑和时长跑
//        List<HomepageItemListVo> homepageItemRunMileage = homeItemService.homepageItemList(3);
//        resp.setRunMileages(homepageItemRunMileage);
//        List<HomepageItemListVo> homepageItemRunTime = homeItemService.homepageItemList(4);
//        resp.setRunTimes(homepageItemRunTime);
//
//        resp.setDistance(distance);
//        resp.setRunTime(runTime);
//        resp.setKilocalorie(kilocalorie);
        WalkPageHomeResp resp = new WalkPageHomeResp();
        return CommonResult.success(resp);
    }

    /**
     * 获取用户绑定的设备列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/list")
    public Result<List<EquipmentListVo>> equipmentList(@RequestBody WalkDto dto) {
        List<ZnsUserEquipmentEntity> znsUserEquipmentEntities = userEquipmentService.selectByUserId(dto.getUserId());
        for (ZnsUserEquipmentEntity znsUserEquipmentEntity : znsUserEquipmentEntities) {
            if (!StringUtils.hasText(znsUserEquipmentEntity.getEquipmentAddress())) {
                log.info(" 删除的数据为 znsUserEquipmentEntity json : {},uerId = {}", znsUserEquipmentEntity, dto.getUserId());
                userEquipmentService.deleteZnsUserEquipmentEntityById(znsUserEquipmentEntity.getId());
            }
        }
        List<EquipmentListVo> equipmentListDtos = userEquipmentService.selectEquipmentListVoByUserId(dto.getUserId());

        Map<String, TreadmillNfcLog> treadmillNfcMap = findTreadmillNfcMap(equipmentListDtos);

        for (EquipmentListVo equipmentListDto : equipmentListDtos) {
            if (Objects.equals(equipmentListDto.getEquipmentType(), 1)) {
                equipmentListDto.setEquipmentTitle("Treadmill");
            } else if (Objects.equals(equipmentListDto.getEquipmentType(), 2)) {
                equipmentListDto.setEquipmentTitle("Walking Pad");
            } else if (Objects.equals(equipmentListDto.getEquipmentType(), 3)) {
                equipmentListDto.setEquipmentTitle("2-in-1");
            }
            ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillByUniqueCode(equipmentListDto.getEquipmentNo());
            if (znsTreadmillEntity != null) {
                ZnsEquipmentProductionBatchEntity productionBatchEntity = znsEquipmentProductionBatchService.selectByBatchNumber(znsTreadmillEntity.getBatchNumber());
                if (productionBatchEntity != null) {
                    equipmentListDto.setAppearanceDrawing(productionBatchEntity.getAppearanceDrawing());
                }
                EquipmentBrandConfig equipmentBrandConfig = equipmentBrandConfigService.selectEquipmentBrandConfigByBrand(znsTreadmillEntity.getBrand());
                if (equipmentBrandConfig != null) {
                    equipmentListDto.setLogoImage(equipmentBrandConfig.getLogoImage());
                }
                //是否支持NFC
                equipmentListDto.setSupportNfc(Objects.nonNull(treadmillNfcMap.get(znsTreadmillEntity.getBluetoothMac())));
            }
            //型号展示重置
            String equipmentModel = equipConfigMoreService.findEquipmentModel(equipmentListDto.getEquipmentModel(), znsTreadmillEntity.getBrand());
            equipmentListDto.setEquipmentModel(equipmentModel);
        }


        return CommonResult.success(equipmentListDtos);
    }

    private Map<String, TreadmillNfcLog> findTreadmillNfcMap(List<EquipmentListVo> equipmentListDtos) {
        Map<String, TreadmillNfcLog> treadmillNfcLogMap = new HashMap<>();
        if (CollectionUtils.isEmpty(equipmentListDtos)) {
            return treadmillNfcLogMap;
        }
        List<String> equipmentNoList = equipmentListDtos.stream().map(EquipmentListVo::getEquipmentNo).toList();
        List<ZnsTreadmillEntity> allByUniqueCodes = znsTreadmillService.findAllByUniqueCodes(equipmentNoList);


        if (!CollectionUtils.isEmpty(allByUniqueCodes)) {
            List<String> bluetoothMacs = allByUniqueCodes.stream().map(ZnsTreadmillEntity::getBluetoothMac).toList();
            List<TreadmillNfcLog> treadmillNfcLogs = treadmillNfcLogService.findAllByBluetoothMacs(bluetoothMacs);
            treadmillNfcLogMap = treadmillNfcLogs.stream().collect(Collectors.toMap(TreadmillNfcLog::getBluetoothMac, Function.identity(), (x, y) -> x));
        }
        return treadmillNfcLogMap;
    }

    /**
     * 删除绑定的设备
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/delete")
    public Result equipmentDelete(@RequestBody WalkDto dto) {
        ZnsUserEquipmentEntity userEquipment = userEquipmentService.selectByEquipmentNoUserId(dto.getEquipmentNo(), dto.getUserId());
        if (userEquipment != null) {
            userDeviceApiManager.deleteUserEquipment(userEquipment, dto.getUserId());
        }
        return CommonResult.success();
    }

    /**
     * 积分列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/score/list")
    public Result<ScoreListDto> scoreList(@RequestBody WalkDto dto) {
        ScoreListDto scoreListDto = new ScoreListDto();
        ZonedDateTime date6MonthPre = DateUtil.addMonths(ZonedDateTime.now(), -6);

        dto.setQueryMonth(DateUtil.parseFormatDate(dto.getQueryMonth(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        PPageUtils pageUtils = PPageUtils.startPage(dto.getPageNum(), dto.getPageSize())
                .doSelect(new ISelect() {
                    @Override
                    public List doSelect(IPage page) {
                        return activityUserScoreDao.selectActivityUserScoreByUserId(page, dto.getUserId(), date6MonthPre,
                                dto.getQueryMonth(), dto.getIncome(), dto.getSource()
                        );
                    }
                });

        scoreListDto.setPage(pageUtils);
        scoreListDto.setAllScore(znsUserService.getAllUserScore(dto.getUserId()));
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.version_app_2_0_5.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        scoreListDto.setRunScoreRule(data.get("runScoreRule") + "");

        // 获取积分
        int acquireScore = activityUserScoreDao.selectActivityUserScoreByUserIdMonth(dto.getUserId(),
                DateUtil.getFirstOfMonth(ZonedDateTime.now()), DateUtil.getEndOfDate(ZonedDateTime.now()), 1, 0);
        // 消费积分
        int consumeScore = activityUserScoreDao.selectActivityUserScoreByUserIdMonth(dto.getUserId(),
                DateUtil.getFirstOfMonth(ZonedDateTime.now()), DateUtil.getEndOfDate(ZonedDateTime.now()), -1, 0);
        scoreListDto.setAcquireScore(acquireScore);
        scoreListDto.setConsumeScore(consumeScore);
        return CommonResult.success(scoreListDto);
    }


    /**
     * 积分 id 获取积分详情
     *
     * @param dto
     * @return
     */
    @PostMapping("/score/id")
    public Result<ActivityUserScoreDto> scoreById(@RequestBody WalkIdDto dto) {
        ActivityUserScoreDto activityUserScoreDto = activityUserScoreDao.selectActivityUserScoreDtoById(dto.getId());
        return CommonResult.success(activityUserScoreDto);
    }

    /**
     * 线上未使用
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/type/list")
    public Result equipmentTypeList(@RequestBody WalkDto dto) {
        List<EquipmentConfig> equipmentConfigList = equipmentConfigService.selectEquipmentConfigByType(Arrays.asList(1, 2, 3));
        return CommonResult.success(equipmentConfigList);
    }

    /**
     * 进入游戏，和报名的时候ram 校验
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/enterRamCheck")
    public Result<EquipmentJudgeDto> enterRamCheck(@RequestBody RamCheckRequest dto) {
        Integer appVersion = getAppVersion();
        dto.setLanguageCode(getLanguageCode());
        if (!StringUtils.hasText(dto.getLanguageCode())) {
            ZnsUserEntity loginUser = getLoginUser();
            if (loginUser != null) {
                dto.setLanguageCode(loginUser.getLanguageCode());
            }
        }
        if (VersionConstant.V4_5_0 > appVersion) {
            EquipmentJudgeDto dto1 = new EquipmentJudgeDto();
            dto1.setEquipmentPop(0);
            return CommonResult.success(dto1);
        }
        return CommonResult.success(activitySignUpManager.enterRamCheck(dto));
    }

    /**
     * 校验设备是否可用于活动
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/judgeByActivityId")
    public Result<EquipmentJudgeDto> judgeByActivityId(@RequestBody WalkDto dto) {
        String zoneId = getZoneId();
        if (Objects.isNull(dto.getUserId())) {
            ZnsUserEntity user = getLoginUser();
            dto.setUserId(user.getId());
        }
        Integer appVersion = getAppVersion();
        String languageCode = getLanguageCode();

        return CommonResult.success(activitySignUpManager.judgeByActivityId(languageCode, appVersion, dto, zoneId));
    }


    /**
     * 活动类型可参与设备类型
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/judgeActivityType")
    public Result judgeActivityType(@RequestBody WalkDto dto) {
        List<ActivityEquipmentConfigDto> activityEquipmentConfigs = null;
        ZnsRunActivityConfigEntity znsRunActivityConfigEntity = null;
        if (Objects.equals(dto.getActivityType(), 2)) {
            znsRunActivityConfigEntity = znsRunActivityConfigService.selectByActivityType(dto.getActivityType(), dto.getActivityTypeSub());
        } else {
            znsRunActivityConfigEntity = znsRunActivityConfigService.selectByActivityType(dto.getActivityType(), null);
        }

        Map<String, Object> data = JsonUtil.readValue(znsRunActivityConfigEntity.getActivityConfig());
        if (data != null && data.get("activityEquipmentConfigs") != null) {
            activityEquipmentConfigs = JsonUtil.readList(data.get("activityEquipmentConfigs"), ActivityEquipmentConfigDto.class);// 设备配置
        }
        log.info("activityEquipmentConfigs judgeActivityType = {}", activityEquipmentConfigs);
        List<ZnsUserEquipmentEntity> userEquipmentEntities = userEquipmentService.selectByUserId(dto.getUserId());
        Integer equipmentPop = 1;
        if (StringUtils.hasText(dto.getCourseId())) {                                    //
            log.info("课程跑处理 courseId = " + dto.getCourseId());
            Long courseId = MapUtil.getLong(dto.getCourseId(), 0L);
            ZnsCourseEntity znsCourseEntity = znsCourseService.selectById(courseId);
            if (StringUtils.hasText(znsCourseEntity.getEquipmentType())) {
                String equipmentTypes[] = StringUtil.split(znsCourseEntity.getEquipmentType(), ",");
                log.info("equipmentTypes = " + Arrays.toString(equipmentTypes));
                for (String equipmentType : equipmentTypes) {
                    ZnsUserEquipmentEntity znsUserEquipmentEntity = null;
                    if (Arrays.asList(4, 5).contains(MapUtil.getInteger(equipmentType))) {            //0：无器械，1：跑步机，2：走步机，3：瑜伽垫, 4 走跑一体机（跑步形态） , 5 走跑一体机（走步形态）
                        znsUserEquipmentEntity = userEquipmentService.selectByUserIdEquipmentType(dto.getUserId(), 3);
                    } else {
                        znsUserEquipmentEntity = userEquipmentService.selectByUserIdEquipmentType(dto.getUserId(), MapUtil.getInteger(equipmentType, -1));
                    }
                    if (znsUserEquipmentEntity != null) {
                        equipmentPop = 0;
                        break;
                    }
                }
                activityEquipmentConfigs = new ArrayList<>();
                for (String equipmentType : equipmentTypes) {
                    ActivityEquipmentConfigDto activityEquipmentConfigDto = new ActivityEquipmentConfigDto();
                    activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName(equipmentType));
                    activityEquipmentConfigDto.setEquipmentInfo(equipmentType);
                    activityEquipmentConfigDto.setSubType(0);
                    activityEquipmentConfigDto.setEquipmentType(MapUtil.getInteger(equipmentType, -1));
                    // equipmentType  4 走跑一体机（跑步形态） , 5 走跑一体机（走步形态）
                    if (Objects.equals(MapUtil.getInteger(equipmentType, -1), 4)) {
                        activityEquipmentConfigDto.setEquipmentType(3);
                        activityEquipmentConfigDto.setEquipmentInfo("3");
                        activityEquipmentConfigDto.setSubType(2);
                        activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName("3") + " (Running Mode)");
                    } else if (Objects.equals(MapUtil.getInteger(equipmentType, -1), 5)) {
                        activityEquipmentConfigDto.setEquipmentType(3);
                        activityEquipmentConfigDto.setEquipmentInfo("3");
                        activityEquipmentConfigDto.setSubType(1);
                        activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName("3") + " (Walking Mode)");
                    }
                    activityEquipmentConfigDto.setType(1);
                    activityEquipmentConfigs.add(activityEquipmentConfigDto);
                }
            }
        } else {
            log.info("非课程跑数据处理 ");
            if (dto.getEquipmentConfigList() != null && dto.getEquipmentConfigList().size() > 0) {
                for (ZnsUserEquipmentEntity znsUserEquipmentEntity : userEquipmentEntities) {
                    EquipmentConfig equipmentConfig = getEquipmentConfig(dto.getEquipmentConfigList(), znsUserEquipmentEntity);
                    // 如果用户连接的设备中有要求的设备 ，则允许参加
                    if (equipmentConfig != null) {
                        equipmentPop = 0;
                        break;
                    }
                }
            } else {
                if (activityEquipmentConfigs != null && activityEquipmentConfigs.size() > 0) {
                    for (ActivityEquipmentConfigDto activityEquipmentConfigDto : activityEquipmentConfigs) {
                        ZnsUserEquipmentEntity znsUserEquipmentEntity = userEquipmentService.selectByUserIdEquipmentModel(dto.getUserId(), activityEquipmentConfigDto.getEquipmentInfo());
                        if (znsUserEquipmentEntity != null) {
                            equipmentPop = 0;
                            break;
                        }
                    }
                }
            }
            if (activityEquipmentConfigs != null && activityEquipmentConfigs.size() > 0) {
                for (ActivityEquipmentConfigDto activityEquipmentConfigDto : activityEquipmentConfigs) {
                    activityEquipmentConfigDto.setEquipmentTypeName(equipmentConfigService.getEquipmentTypeName(activityEquipmentConfigDto.getEquipmentType() + ""));
                    EquipmentConfig equipmentConfig = equipmentConfigService.selectEquipmentConfigByTypeEquipmentInfo(2, activityEquipmentConfigDto.getEquipmentInfo());
                    Long equipmentConfigId = equipmentConfig != null ? MapUtil.getLong(equipmentConfig.getId(), 0l) : 0l;
                    activityEquipmentConfigDto.setEquipmentChildId(equipmentConfigId);
                }
            }
        }
        if (Objects.equals(dto.getPkType(), 2)) {                     //好友对战为0
            equipmentPop = 0;
            activityEquipmentConfigs = equipmentConfigService.wrapperEquipmentConfigDto();
        }

        if (activityEquipmentConfigs == null || activityEquipmentConfigs.size() == 0) { //如果没有配置，则不弹窗
            equipmentPop = 0;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("equipmentPop", equipmentPop);
        map.put("activityEquipmentConfigs", activityEquipmentConfigs);
        String equipmentPopMsg = ActivityEquipmentConfigDto.equipmentConfigToPopMsg(activityEquipmentConfigs);
        map.put("equipmentPopMsg", equipmentPopMsg);
        List<String> equipmentModels = userEquipmentService.selectEquipmentModelByUserId(dto.getUserId());
        map.put("equipmentModels", equipmentModels);
        return CommonResult.success(map);
    }

    @PostMapping("/equipmentById")
    public Result equipmentById(@RequestBody WalkDto dto) {
        EquipmentConfig equipmentEntity = null;
        if (Objects.equals(dto.getId(), -1l)) {
            ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillByUniqueCode(dto.getEquipmentNo());
            if (znsTreadmillEntity != null) {
                equipmentEntity = equipmentConfigService.selectEquipmentConfigByTypeEquipmentInfo(2, znsTreadmillEntity.getProductCode());
            }
        } else {
            equipmentEntity = equipmentConfigService.selectEquipmentConfigById(dto.getId());
        }
        if (equipmentEntity == null) {
            equipmentEntity = equipmentConfigService.selectEquipmentConfigByIsDefult(1);
        }
        return CommonResult.success(equipmentEntity);
    }

    public EquipmentConfig getEquipmentConfig(List<EquipmentConfig> equipmentConfigList, ZnsUserEquipmentEntity activityEquipmentConfig) {
        if (equipmentConfigList != null && equipmentConfigList.size() > 0) {
            for (EquipmentConfig equipmentConfig : equipmentConfigList) {
                if (equipmentConfig.getEquipmentInfo().equals(activityEquipmentConfig.getEquipmentModel())) {
                    return equipmentConfig;
                }
            }
        }
        return null;
    }


    /**
     * 根据subType来判断是否弹出修改主题的弹窗或者是比赛前检查弹窗，使用于蓝牙弹窗BlueDialogManager
     *
     * @param dto
     * @return
     */
    @PostMapping("/equipment/judgeAll")
    public Result<JudgeAllResponse> judgeAll(@RequestBody WalkJudageDto dto) {
        String zoneId = getZoneId();
        //查询设备
        String equipmentNo = dto.getEquipmentNo().trim();
        log.info("WalkController#judgeAll------入参：equipmentNo={},activityId={},activityType={},courseId={},subType={}", equipmentNo, dto.getActivityId(), dto.getActivityType(), dto.getCourseId(), dto.getSubType());
        dto.setEquipmentNo(equipmentNo);
        if (!StringUtils.hasText(equipmentNo)) {
            throw new BaseException("please bind Treadmill");
        }
        if (Objects.equals(dto.getActivityId(), 1L)) {
            //兼容ios activityId= 1 的情况
            dto.setActivityId(null);
            dto.setActivityType(-1);
        }
        if (Objects.equals(dto.getActivityType(), 0)) {
            //兼容未知情况下客户端传0的情况
            dto.setActivityType(-1);
        }
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillByUniqueCode(equipmentNo);

        //校验设备是否激活(33版本以上才有)
        Integer equipmentVersion = znsTreadmillEntity.getEquipmentVersion();
        if (DeviceConstant.ActivateStatusEnum.ACTIVATE_STATUS_2.code.equals(znsTreadmillEntity.getActivateStatus())
                && Optional.ofNullable(equipmentVersion).orElse(27) >= 33) {
            //未激活，提示升级app
            throw new BaseException(I18nMsgUtils.getMessage("equipment.noActivate.message"));
        }

        ZnsUserEntity user = getLoginUser();
        Long userId = dto.getUserId();
        if (userId != null) {
            user = znsUserService.findById(userId);
        }
        ZnsUserEquipmentEntity znsUserEquipmentEntity = userEquipmentService.selectByEquipmentNoUserId(equipmentNo, user.getId());
        if (znsUserEquipmentEntity == null) {
            throw new BaseException("please bind Treadmill");
        }
        ActivityTypeDto activityTypeDto = null;
        if (dto.getActivityId() != null && dto.getActivityId() > 0) {
            activityTypeDto = mainActivityBizService.getActivityNew(dto.getActivityId(), zoneId);
        }
        String productCode = znsTreadmillEntity.getProductCode();

        //进入游戏前的校验
        JudgeAllResponse response = equipmentJudgeManager.judgeBeforeGame(znsUserEquipmentEntity, dto, activityTypeDto, user, productCode, getAppVersion());
        if (sysConfigService.isVirtualEquipment(dto.getEquipmentNo())) {
            MainActivity mainActivity = mainActivityService.findById(dto.getActivityId());
            if (mainActivity != null) {
                if (MainActivityTypeEnum.IsNewActivity(mainActivity.getMainType())) {
                    response.setEquipmentPop(1);

                }
            }
        }
        return CommonResult.success(response);
    }

}
