/*

 * @description:

 * projectName: pitpat-server

 * fileName: UserController.java

 * date 2021-09-28

 * copyright(c) 2018-2020 杭州霖扬网络科技有限公司版权所有

 */

package com.linzi.pitpat.api.userservice.controller.app;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.manager.UserActivityManager;
import com.linzi.pitpat.api.awardservice.dto.response.UserWearsInfoResponse;
import com.linzi.pitpat.api.awardservice.manager.UserWearsBusiness;
import com.linzi.pitpat.api.bussiness.AppUserBussiness;
import com.linzi.pitpat.api.enums.EquipmentTypeEnum;
import com.linzi.pitpat.api.userservice.converter.UserFeedbackConverter;
import com.linzi.pitpat.api.userservice.dto.request.EmailBindReqDto;
import com.linzi.pitpat.api.userservice.dto.request.EmailReqDto;
import com.linzi.pitpat.api.userservice.dto.request.UserAppRegisterReqDto;
import com.linzi.pitpat.api.userservice.dto.request.UserFeedbackRequestDto;
import com.linzi.pitpat.api.userservice.dto.response.BindTreadmillResponseDto;
import com.linzi.pitpat.api.userservice.dto.response.SaveUserInfoResponseDto;
import com.linzi.pitpat.api.userservice.manager.CommunityBusiness;
import com.linzi.pitpat.api.userservice.manager.UserFeedbackApiManager;
import com.linzi.pitpat.api.userservice.manager.UserGeoManager;
import com.linzi.pitpat.api.userservice.manager.UserManager;
import com.linzi.pitpat.api.userservice.manager.UserRegisterApiManager;
import com.linzi.pitpat.api.userservice.manager.UserVipMananger;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.entity.TimeZoneVo;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.SportsDataUnit;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.activityservice.constant.enums.ActivityError;
import com.linzi.pitpat.data.activityservice.constant.enums.RankedLevelEnums;
import com.linzi.pitpat.data.activityservice.model.dto.RankedActivityAchievementDto;
import com.linzi.pitpat.data.activityservice.model.dto.UserInGameDto;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityDisseminate;
import com.linzi.pitpat.data.activityservice.model.entity.RankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.UserRankedLevel;
import com.linzi.pitpat.data.activityservice.model.vo.UserRecentActivityVo;
import com.linzi.pitpat.data.activityservice.model.vo.activity.UserRankedLevelStatisticsVo;
import com.linzi.pitpat.data.activityservice.service.ActivityDisseminateService;
import com.linzi.pitpat.data.activityservice.service.RankedLevelService;
import com.linzi.pitpat.data.activityservice.service.RunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.UserRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityUserService;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.dto.api.UserWearsBagDto;
import com.linzi.pitpat.data.awardservice.model.entry.MedalConfig;
import com.linzi.pitpat.data.awardservice.model.entry.MedalI18n;
import com.linzi.pitpat.data.awardservice.model.entry.UserMedal;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsBag;
import com.linzi.pitpat.data.awardservice.model.entry.UserWearsLog;
import com.linzi.pitpat.data.awardservice.model.entry.Wears;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserWearsEntity;
import com.linzi.pitpat.data.awardservice.model.query.MedalI18nQuery;
import com.linzi.pitpat.data.awardservice.service.MedalConfigService;
import com.linzi.pitpat.data.awardservice.service.MedalI18nService;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.UserRunCertificateService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.UserWearsLogService;
import com.linzi.pitpat.data.awardservice.service.WearsService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserWearsService;
import com.linzi.pitpat.data.clubservice.constant.enums.UserKolClubAuthorizationEnum;
import com.linzi.pitpat.data.clubservice.manager.ClubManager;
import com.linzi.pitpat.data.clubservice.model.entity.Club;
import com.linzi.pitpat.data.clubservice.model.request.ClubSearchQuery;
import com.linzi.pitpat.data.clubservice.service.ClubService;
import com.linzi.pitpat.data.constant.RedisKeyConstant;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.dao.activity.MonthRunDataDao;
import com.linzi.pitpat.data.entity.activity.MonthRunData;
import com.linzi.pitpat.data.entity.dto.UserMedalDto;
import com.linzi.pitpat.data.entity.dto.UserRecentActivityDto;
import com.linzi.pitpat.data.entity.dto.user.SendVerificationCodeDto;
import com.linzi.pitpat.data.entity.po.SendVerificationCodeFailPo;
import com.linzi.pitpat.data.entity.userservice.UserLevel;
import com.linzi.pitpat.data.entity.vo.MyHomepageVo;
import com.linzi.pitpat.data.entity.vo.OtherPersonPageVo;
import com.linzi.pitpat.data.entity.vo.UserRunDataVo;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.enums.OrderStatusEnum;
import com.linzi.pitpat.data.enums.UserWearTypeEnum;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentVersionRequest;
import com.linzi.pitpat.data.equipmentservice.dto.response.DeviceVersionResp;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.manager.EquipmentManager;
import com.linzi.pitpat.data.equipmentservice.manager.UserEquipmentManager;
import com.linzi.pitpat.data.equipmentservice.manager.api.UserEquipmentShareManager;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentBrandConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentConfig;
import com.linzi.pitpat.data.equipmentservice.model.entity.EquipmentSpeedConfigDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.TreadmillNfcLog;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsEquipmentProductionBatchEntity;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.query.EquipmentSpeedConfigQuery;
import com.linzi.pitpat.data.equipmentservice.model.vo.EquipmentQualityPopVo;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentBrandConfigService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentConfigService;
import com.linzi.pitpat.data.equipmentservice.service.EquipmentSpeedConfigService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillNfcLogService;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillWhiteListService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsEquipmentProductionBatchService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.filler.base.FillerMethod;
import com.linzi.pitpat.data.mallservice.enums.OrderConstant;
import com.linzi.pitpat.data.mallservice.model.query.OrderQuery;
import com.linzi.pitpat.data.mallservice.service.ZnsOrderService;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.resp.BindDevResp;
import com.linzi.pitpat.data.service.prop.UserPropBagService;
import com.linzi.pitpat.data.systemservice.dto.request.CommunityContentPo;
import com.linzi.pitpat.data.systemservice.dto.response.AppCommunityContentVo;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.AreaEntity;
import com.linzi.pitpat.data.systemservice.model.entity.AreaI18nEntity;
import com.linzi.pitpat.data.systemservice.model.entity.CountryI18nEntity;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.model.entity.ZnsCountryEntity;
import com.linzi.pitpat.data.systemservice.model.query.AreaI18nQuery;
import com.linzi.pitpat.data.systemservice.model.query.CountryI18nQuery;
import com.linzi.pitpat.data.systemservice.model.vo.ContactUsListVO;
import com.linzi.pitpat.data.systemservice.service.AppCommonConfigService;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.AreaI18nService;
import com.linzi.pitpat.data.systemservice.service.AreaService;
import com.linzi.pitpat.data.systemservice.service.CountryI18nService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.systemservice.service.ZnsCountryService;
import com.linzi.pitpat.data.userservice.biz.UserExtraBizService;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.dto.api.request.UpdateUser3dStyleImgRequest;
import com.linzi.pitpat.data.userservice.dto.api.request.UserGeoReq;
import com.linzi.pitpat.data.userservice.dto.event.PutChannelUserShouldUpgradeEvent;
import com.linzi.pitpat.data.userservice.dto.request.BindDevRequest;
import com.linzi.pitpat.data.userservice.dto.request.BindTreadmillRequest;
import com.linzi.pitpat.data.userservice.dto.request.FriendIdRequest;
import com.linzi.pitpat.data.userservice.dto.request.GetWearsInformationPo;
import com.linzi.pitpat.data.userservice.dto.request.MeasureUnitRequest;
import com.linzi.pitpat.data.userservice.dto.request.NotifyStateSaveRequest;
import com.linzi.pitpat.data.userservice.dto.request.SurrenderChannelActivityReqDto;
import com.linzi.pitpat.data.userservice.dto.request.ThirdUserCheckReqDto;
import com.linzi.pitpat.data.userservice.dto.request.ThirdUserNoThirdEmailReqDto;
import com.linzi.pitpat.data.userservice.dto.request.ThirdUserReqDto;
import com.linzi.pitpat.data.userservice.dto.request.UserCountryRegionRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserIdRequest;
import com.linzi.pitpat.data.userservice.dto.request.UserMetricReq;
import com.linzi.pitpat.data.userservice.dto.request.UserRequest;
import com.linzi.pitpat.data.userservice.dto.response.CheckChangeCountryRegionResp;
import com.linzi.pitpat.data.userservice.dto.response.SurrenderChannelActivityRespDto;
import com.linzi.pitpat.data.userservice.enums.CountryFlagConstant;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.enums.UserError;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.enums.UserIdentityEnum;
import com.linzi.pitpat.data.userservice.manager.UserAccountUpgradeBizService;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.model.entity.ExpConfig;
import com.linzi.pitpat.data.userservice.model.entity.UserIdentityDo;
import com.linzi.pitpat.data.userservice.model.entity.UserKol;
import com.linzi.pitpat.data.userservice.model.entity.UserLevelRule;
import com.linzi.pitpat.data.userservice.model.entity.UserTitleDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.model.entity.vip.VipUser;
import com.linzi.pitpat.data.userservice.model.query.UserEquipmentQuery;
import com.linzi.pitpat.data.userservice.model.query.UserIdentityQuery;
import com.linzi.pitpat.data.userservice.model.query.UserPropBagQuery;
import com.linzi.pitpat.data.userservice.model.query.UserRunCertificateQuery;
import com.linzi.pitpat.data.userservice.model.vo.AppBaseInfoVo;
import com.linzi.pitpat.data.userservice.model.vo.ClubInfoDto;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.CommunityContentService;
import com.linzi.pitpat.data.userservice.service.ExpConfigService;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import com.linzi.pitpat.data.userservice.service.UserDetailService;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.UserKolService;
import com.linzi.pitpat.data.userservice.service.UserLevelRuleService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.UserTitleService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFeedbackService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.impl.ZnsUserEquipmentServiceImpl;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.LoginVO;
import com.linzi.pitpat.data.vo.ThirdLoginVO;
import com.linzi.pitpat.data.vo.UserAbTestGroupVo;
import com.linzi.pitpat.data.vo.UserExpDetailDto;
import com.linzi.pitpat.data.vo.UserExpDto;
import com.linzi.pitpat.data.vo.user.GetUserInfoVo;
import com.linzi.pitpat.data.vo.user.UserThemeListVo;
import com.linzi.pitpat.data.vo.useractive.PowerAchievementVo;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.context.UserContextHolder;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户相关注册/登录/信息 控制器
 *
 * <AUTHOR>
 * <p>
 * className UserController
 * <p>
 * version V1.0
 * @date 2021-09-28
 **/
@RestController
@RequestMapping("/app/user")
@Slf4j
@RequiredArgsConstructor
public class UserAppController extends BaseAppController {

    @Resource
    private UserDetailService userDetailService;

    @Resource(name = "asyncExecutor")
    private ThreadPoolTaskExecutor executor;
    @Resource
    private ZnsUserService znsUserService;
    @Resource
    private ZnsCountryService znsCountryService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ZnsUserEquipmentService userEquipmentService;
    @Resource
    private ZnsTreadmillService treadmillService;
    @Resource
    private ZnsUserFriendService userFriendService;
    @Resource
    private TencentImUtil tencentImUtil;
    @Resource
    private ZnsUserWearsService userWearsService;
    @Resource
    private ExpConfigService expConfigService;
    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private UserMedalService userMedalService;
    @Resource
    private MedalI18nService medalI18nService;
    @Resource
    private MedalConfigService medalConfigService;
    @Resource
    private AppUserBussiness appUserBussiness;
    @Autowired
    private ExpUserService expUserService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private AppCommonConfigService appCommonConfigService;
    @Resource
    private ZnsRunActivityUserService runActivityUserService;
    @Value("${spring.profiles.active}")
    private String profile;
    @Resource
    private ZnsUserEmailSendingRecordService userEmailSendingRecordService;
    @Autowired
    private MonthRunDataDao monthRunDataDao;
    @Resource
    private AppUpgradeService appUpgradeService;
    @Autowired
    private EquipmentConfigService equipmentConfigService;
    @Autowired
    private EquipmentBrandConfigService equipmentBrandConfigService;
    @Autowired
    private UserCouponService userCouponService;
    @Resource
    private UserManager userManager;

    @Resource
    private UserWearsBagService userWearsBagService;
    @Resource
    private UserVipMananger userVipMananger;

    @Resource
    private UserWearsBusiness userWearsBusiness;

    @Resource
    private WearsService wearsService;

    @Resource
    private VipUserService vipUserService;

    @Resource
    private UserPropBagService userPropBagService;

    @Resource
    private UserRunCertificateService userRunCertificateService;

    @Resource
    private TreadmillNfcLogService treadmillNfcLogService;
    @Resource
    private AreaService areaService;
    @Resource
    private UserWearsLogService userWearsLogService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserKolService userKolService;
    @Resource
    private ClubService clubService;
    @Resource
    private ZnsEquipmentProductionBatchService znsEquipmentProductionBatchService;

    @Resource
    private AreaI18nService areaI18nService;

    @Resource
    private CountryI18nService countryI18nService;

    @Resource
    private UserRankedLevelService userRankedLevelService;

    @Resource
    private RunRankedActivityUserService runRankedActivityUserservice;

    @Resource
    private RankedLevelService rankedLevelService;

    @Resource
    private ActivityDisseminateService activityDisseminateService;

    @Resource
    private UserLevelService userLevelService;

    @Resource
    private UserLevelRuleService userLevelRuleService;
    @Resource
    private UserRegisterApiManager userRegisterApiManager;

    @Resource
    private UserTitleService userTitleService;

    @Resource
    private UserTaskDetailService userTaskDetailService;
    @Resource
    private UserActivityManager userActivityManager;
    @Resource
    private ClubManager clubManager;
    @Resource
    private UserEquipmentManager userEquipmentManager;
    @Resource
    private UserFeedbackConverter userFeedbackConverter;
    @Resource
    private AppMessageService appMessageService;
    // 普通客服：test-9876,online-100428
    @Value("${im.customerService.idStr:100428}")
    private String customerServiceIdStr;
    @Resource
    private UserBizService userBizService;
    @Resource
    private ZnsOrderService orderService;

    // plus客服：test-777,online-343530
    @Value("${im.plusService.idStr:343530}")
    private String plusServiceIdStr;
    @Resource
    private UserGeoManager userGeoManager;
    @Resource
    private UserFeedbackApiManager userFeedbackApiManager;
    @Resource
    private EquipmentManager equipmentManager;
    @Resource
    private UserTaskBizService userTaskBizService;

    @Resource
    private CommunityContentService communityContentService;

    private final UserEquipmentShareManager userEquipmentShareManager;


    private final CommunityBusiness communityBusiness;

    private final UserExtraService userExtraService;
    private final UserExtraBizService userExtraBizService;

    private final UserIdentityService userIdentityService;

    private final EquipmentSpeedConfigService equipmentSpeedConfigService;
    private final QueueMessageService queueMessageService;
    @Autowired
    private ZnsUserEquipmentServiceImpl znsUserEquipmentService;
    private final TreadmillWhiteListService treadmillWhiteListService;

    /**
     * 这个接口给第三方rainmaker使用，以后不再更新
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/login")
    public Result login(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        if (!StringUtils.hasText(request.getEmailAddress())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.enter.account"));
        }
        if (!StringUtils.hasText(request.getPassword())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.enter.password"));
        }
        String uuid = httpServletRequest.getHeader("uuid");
        request.setUuid(uuid);
        Integer appVersion = getAppVersion();
        log.info("rainmaker 用户登陆接口调用 email =" + request.getEmailAddress() + ",uuid=" + request.getUuid());
        znsUserService.login(request, appVersion);
        return CommonResult.success();
    }


    /**
     * 因为之前的登陆接口被三方占用了，因此写了新的登陆接口供app使用
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/loginNew")
    public Result<LoginVO> loginNew(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        if (!StringUtils.hasText(request.getEmailAddress())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter the account");
        }
        if (!StringUtils.hasText(request.getPassword())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter the password");
        }

        znsUserService.encapsulateUserRequest(request, httpServletRequest);
        Integer appVersion = getAppVersion();

        Result<LoginVO> result = znsUserService.loginNew(request, httpServletRequest, appVersion);
        LoginVO loginVO = result.getData();
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        if (result.getData() != null && CommonError.SUCCESS.getCode().equals(result.getCode())) {
            //更新时区、语言
            znsUserService.updateUserAppBaseInfo(appBaseInfo, result.getData().getUserId());
        }
        if (Objects.equals(result.getCode(), CommonError.SUCCESS.getCode())) {
            //登录成功
            ZnsUserEntity tempUser = znsUserService.findTempUserByUuid(appBaseInfo.getUuid()); // 查询临时用户
            if (Objects.nonNull(tempUser)) {
                //临时账号数据迁移到真实账号
                userBizService.removeTempUserToRealUser(tempUser.getId(), result.getData().getUserId(), getAppVersion());
            }
        }
        executor.submit(() -> {
            if (Objects.nonNull(loginVO)) {
                userTaskBizService.initUserTask(loginVO.getUserId(), appVersion);
                if (appVersion < VersionConstant.V4_6_4) {
                    //老版本用户商城默认美国
                    userExtraService.updateUserMallCountryCode(loginVO.getUserId(), I18nConstant.CountryCodeEnum.US.code);
                }
            }
        });
        return result;
    }

    /**
     * 退出登录
     */
    @PostMapping("/rLogout")
    public Result rLogout(HttpServletRequest request) {
        ZnsUserEntity user = getLoginUser();
        if (user != null) {
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            Object o = redisUtil.get(key);
            String token = request.getHeader("token");
            if (o.toString().equals(token)) {
                redisUtil.delete(key);
                return CommonResult.success();
            }
        }
        return CommonResult.fail(CommonError.INVALID_TOKEN.getCode(), CommonError.INVALID_TOKEN.getMsg());
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Result logout() {
        ZnsUserEntity user = getLoginUser();
        if (user != null) {
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            redisUtil.delete(key);
        }
        return CommonResult.success();
    }

    /**
     * 用户注册第二版
     *
     * @param req
     * @since 4.0
     */
    @PostMapping("/registerV2")
    public Result<LoginVO> registerV2(@RequestBody UserAppRegisterReqDto req) {
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        req.setSource(0); //来源：0-app
        LoginVO loginVO = userRegisterApiManager.emailRegister(req, appBaseInfo);
        return CommonResult.success(loginVO);
    }

    /**
     * 用户注册
     *
     * @param request
     * @param httpServletRequest
     * @see UserAppController#registerV2(UserAppRegisterReqDto)
     */
    @Deprecated(since = "v4.2.0", forRemoval = true)
    @PostMapping("/register")
    public Result register(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        Result checkParam = znsUserService.registerCheckParam(request);
        if (checkParam != null) {
            return checkParam;
        }
        Integer appVersion = getAppVersion();

        //填充国家
        String countryName = I18nConstant.CountryCodeEnum.US.enName;
        String countryCode = I18nConstant.CountryCodeEnum.US.code;
        if (StringUtils.hasText(request.getCountryCode())) {
            ZnsCountryEntity countryEntity = znsCountryService.findByCountryCode(request.getCountryCode());
            if (countryEntity != null) {
                countryName = countryEntity.getName();
                countryCode = countryEntity.getCode();
            }
        }
        request.setCountry(countryName);
        request.setCountryCode(countryCode);

        //填充州
        String stateCode = I18nConstant.CountryCodeEnum.US.defaultStateCode;
        String stateName = I18nConstant.CountryCodeEnum.US.defaultState;
        if (StringUtils.hasText(request.getStateCode())) {
            AreaEntity areaEntity = areaService.selectByAreaCode(request.getStateCode());
            if (areaEntity != null) {
                stateName = areaEntity.getAreaName();
                stateCode = areaEntity.getAreaCode();
            }
        }
        request.setState(stateName);
        request.setStateCode(stateCode);

        //注册
        znsUserService.encapsulateUserRequest(request, httpServletRequest);
        return znsUserService.register(request, appVersion);
    }

    /**
     * 注册发送验证码
     *
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/sendVerificationCode")
    public Result sendVerificationCode(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        return appUserBussiness.sendVerificationCode(request, httpServletRequest);
    }

    /**
     * 注册发送验证码
     *
     * @return
     */
    @PostMapping("/sendVerificationCodeFail")
    public Result sendVerificationCodeFail(@RequestBody SendVerificationCodeFailPo request) {
        userEmailSendingRecordService.sendVerificationCodeFail(request);
        return CommonResult.success();
    }

    /**
     * 用户信息
     *
     * @return
     */
    @PostMapping("/getInfo")
    public Result<LoginVO> getInfo() {
        ZnsUserEntity user = znsUserService.findById(getUserId());

        //更新用户app基本信息
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        znsUserService.updateUserAppBaseInfo(appBaseInfo, user.getId());

        Integer appVersion = getAppVersion();
        String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
        String token = redisUtil.get(key);
        LoginVO loginVO = znsUserService.encapsulateUserData(user.getId(), token, true, false, appVersion);

        executor.submit(() -> {
            if (appVersion >= 4000) {
                userTaskBizService.initUserTask(loginVO.getUserId(), appVersion);
                // 完成每日登录
                userTaskDetailService.completeLevelTask(user.getId(), UserExpObtainSubTypeEnum.LOGIN_BEHAVIOR.getCode(), true);
                userTaskBizService.completeEvent(new EventTriggerDto().setUser(user).setEventType(TaskConstant.TakEventTypeEnum.LOGIN.getCode()));
            }
            if (appVersion < VersionConstant.V4_6_4) {
                //老版本用户商城默认美国
                userExtraService.updateUserMallCountryCode(user.getId(), I18nConstant.CountryCodeEnum.US.code);
            }
        });

        //获取用户abTest分组
        List<UserAbTestGroupVo> userAbTestGroup = userBizService.findUserAbTestGroup(user, appBaseInfo.getUuid());
        loginVO.setAbTestGroupVos(userAbTestGroup);
        return CommonResult.success(loginVO);

    }

    /**
     * 保存用户信息
     *
     * @param request
     * @return
     */
    @PostMapping("/saveInfo")
    public Result<SaveUserInfoResponseDto> saveInfo(@RequestBody UserRequest request) {
        ZnsUserEntity user = znsUserService.findById(getUserId());
        if (user == null) {
            return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), "no user");
        }
        SaveUserInfoResponseDto resp = new SaveUserInfoResponseDto();
        if (request.getGender() != null && getAppVersion() < VersionConstant.V4_8_0) {
            if (!request.getGender().equals(user.getGender()) && getAppVersion() < VersionConstant.V4_7_5){
                throw new BaseException(I18nMsgUtils.getMessage("user.gender.update.after.version"));
            }
            if (!user.getGender().equals(request.getGender())) {
                RBucket<String> bucket = redissonClient.getBucket(RedisConstants.GENDER_LIMIT_KEY + user.getId());
                if (bucket.isExists()) {
                    throw new BizI18nException("user.gender.update.fail");
                }
            }
            user.setGender(request.getGender());
        }
        if (StringUtils.hasText(request.getBirthdayStr())) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            try {
                user.setBirthday(sdf.parse(request.getBirthdayStr()));
            } catch (ParseException e) {
                //用户生日计算错误
                log.error("saveInfo setBirthday error", e);
            }
        }
        if (request.getHeight() != null && request.getHeight() > 0) {
            user.setHeight(request.getHeight());
        }
        if (request.getWeight() != null && request.getWeight() > 0) {
            user.setWeight(request.getWeight());
        }
        if (StringUtils.hasText(request.getFirstName())) {
            if (request.getFirstName().length() > 30) {
                return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter first name/nickname in 30 characters or less");
            }
            if (StringUtil.containsEmoji(request.getFirstName())) {
                return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), "Name cannot contain any special characters.");
            }
            user.setFirstName(request.getFirstName());
            // 修改会员信息
            VipUser vipUser = userVipMananger.findByEmail(user.getEmailAddressEn());
            if (Objects.nonNull(vipUser)) {
                vipUser.setUserName(request.getFirstName());
                userVipMananger.updateById(vipUser);
            }

        }

        //设置私密账号
        if (request.getIsPrivacy() != null) {
            user.setIsPrivacy(request.getIsPrivacy());
        }

        user.setModifieTime(ZonedDateTime.now());
        user.setCountry(request.getCountry());
        user.setState(request.getState());
        user.setCity(request.getCity());
        user.setAddress(request.getAddress());
        user.setSearchValue(user.getFirstName() + "," + user.getUserCode());
        //验证邀请码
        if (StringUtils.hasText(request.getInvitationCode())) {
            ZnsUserEntity invitationUser = znsUserService.findByUserCode(request.getInvitationCode());
            if (Objects.isNull(invitationUser)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.account.invitation"));
            }
        }
        if (!StringUtils.hasText(user.getInvitationCode()) && StringUtils.hasText(request.getInvitationCode())) {
            user.setInvitationCode(request.getInvitationCode());
            user.setInvitedTime(ZonedDateTime.now());
        }
        znsUserService.update(user);
        if (StringUtils.hasText(user.getCountry()) && UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code.equals(user.getEmailType())) {
            // 补充个人邮箱和国家信息
            userTaskDetailService.completeLevelTask(user.getId(), UserExpObtainSubTypeEnum.COMPLETE_PROFILE.getCode(), false);
            userTaskBizService.completeEvent(new EventTriggerDto().setUser(user).setEventSubType(TaskConstant.TakEventSubTypeEnum.USER_IMPROVE_INFO.getCode()));
        }
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        //im设置资料
        executor.execute(() -> {
            try {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                tencentImUtil.portraitSet(user.getId().toString(), user.getHeadPortrait(), user.getFirstName(), user.getGender());
            } catch (Exception e) {
                log.error("异常", e);
            } finally {
                OrderUtil.removeLogNo();
            }
        });
        if (request.getGender() != null && getAppVersion() >= VersionConstant.V4_8_0){
           boolean succUpdateGender = userManager.updateGender(getUserId(), request.getGender());
           resp.setSuccUpdateGender(succUpdateGender);
        }
        return CommonResult.success(resp);
    }

    /**
     * 修改用户计量单位
     *
     * @param request
     * @return
     */
    @PostMapping("/measureUnit")
    public Result updateMeasurementUnit(@RequestBody MeasureUnitRequest request) {
        Integer measureUnit = request.getMeasureUnit();
        if (Objects.isNull(measureUnit)) {
            measureUnit = 0;
        }
        ZnsUserEntity user = getLoginUser();
        if (user == null) {
            return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), "no user");
        }
        ZnsUserEntity update = new ZnsUserEntity();
        update.setId(user.getId());
        update.setMeasureUnit(measureUnit);
        update.setModifieTime(ZonedDateTime.now());
        znsUserService.update(update);

        return CommonResult.success();
    }

    /**
     * 修改用户头像
     *
     * @param file
     * @return
     */
    @RequestMapping("/changeHeadPortrait")
    public Result changeHeadPortrait(@RequestParam("file") MultipartFile file) {
        Map<String, Object> map = AwsUtil.putS3Object(file, "headPortrait");
        String headPortrait = (String) map.get("url");
        ZnsUserEntity user = getLoginUser();
        user.setHeadPortrait(headPortrait);
        znsUserService.update(user);
        String logNo = OrderUtil.getLogNo();
        long time = OrderUtil.getExeTime();
        //更新im资料库
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    OrderUtil.addLogNo("child_" + logNo, time);
                    tencentImUtil.portraitSet(user.getId().toString(), user.getHeadPortrait(), user.getFirstName(), user.getGender());
                } catch (Exception e) {
                    log.error("异常", e);
                } finally {
                    OrderUtil.removeLogNo();
                }
            }
        });
        return CommonResult.success(headPortrait);
    }

    /**
     * 上传社区内容图片
     *
     * @param file
     * @return
     */
    @RequestMapping("/uploadCommunityContentImage")
    public Result uploadCommunityContentImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> map = AwsUtil.putS3Object(file, "communityContentImage");
        return CommonResult.success((String) map.get("url"));
    }


    /**
     * 头像上传接口
     *
     * @param file
     * @return
     */
    @RequestMapping("/change/HeadPortrait")
    public Result changeHeadPortraitReal(@RequestParam("file") MultipartFile file) {
        Map<String, Object> map = AwsUtil.putS3Object(file, "headPortrait");
        String headPortrait = (String) map.get("url");
        ZnsUserEntity user = getLoginUser();
        user.setHeadPortrait(headPortrait);
        znsUserService.update(user);
        String logNo = OrderUtil.getLogNo();
        long time = OrderUtil.getExeTime();
        //更新im资料库
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    OrderUtil.addLogNo("child_" + logNo, time);
                    tencentImUtil.portraitSet(user.getId().toString(), user.getHeadPortrait(), user.getFirstName(), user.getGender());
                } catch (Exception e) {
                    log.error("异常", e);
                } finally {
                    OrderUtil.removeLogNo();
                }
            }
        });
        return CommonResult.success(headPortrait);
    }


    /**
     * 用户反馈
     *
     * @param requestDto
     * @return
     */
    @PostMapping("/feedback")
    public Result feedback(@RequestBody UserFeedbackRequestDto requestDto) {
        if (!StringUtils.hasText(requestDto.getProblemDesc())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.feedback"));
        }
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), CommonError.NEED_LOGIN.getMsg());
        }
        requestDto.setUserId(loginUser.getId());
        requestDto.setNickname(loginUser.getFirstName());
        requestDto.setCountry(loginUser.getCountry());
        if (Objects.isNull(requestDto.getPurchaseDate()) || requestDto.getPurchaseDate().compareTo(DateTimeUtil.parse("2021-12-12 00:00:00")) < 0) {
            requestDto.setPurchaseDate(null);
        }
        boolean feedback = userFeedbackApiManager.feedback(requestDto, loginUser);
        if (!feedback) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        }
        return CommonResult.success();
    }

    /**
     * 发送找回密码邮件
     *
     * @param request
     * @return
     */
    @SuppressWarnings("all")
    @PostMapping("/forgetPassword")
    public Result forgetPassword(@RequestBody UserRequest request, HttpServletRequest httpServletRequest) {
        return appUserBussiness.forgetPassword(request, httpServletRequest);
    }

    /**
     * 发送绑定邮箱邮件
     *
     * @param req
     * @return
     */
    @PostMapping("/sendBindEmailCode")
    public Result<SendVerificationCodeDto> sendBindEmailCode(@RequestBody EmailReqDto req) {
        SendVerificationCodeDto dto = userRegisterApiManager.sendBindEmailCode(req.getEmailAddress());
        Result<SendVerificationCodeDto> result = CommonResult.success(dto);
        result.setMsg(I18nMsgUtils.getMessage("user.send.success"));
        return result;
    }

    /**
     * 绑定邮箱
     *
     * @param req
     * @return
     */
    @PostMapping("/bindEmail")
    public Result<Boolean> bindEmail(@RequestBody EmailBindReqDto req) {
        ZnsUserEntity loginUser = getLoginUser();
        userRegisterApiManager.bindEmail(req, loginUser);
        return CommonResult.success(true);
    }

    /**
     * 获取国家列表
     */
    @PostMapping("/getCountryList")
    public Result getCountryList() {
        List<String> countryList = znsCountryService.getCountryList();
        Map<String, Object> data = new HashMap<>(3);
        data.put("countryList", countryList);
        return CommonResult.success(data);
    }

    /**
     * 绑定跑步机
     *
     * @return
     */
    @PostMapping("/bindTreadmill")
    public Result<BindTreadmillResponseDto> bindTreadmill(@RequestBody BindTreadmillRequest request) {
        String equipmentNo = request.getEquipmentNo().trim();
        log.info("[bindTreadmill]----绑定跑步机, equipmentNo={},开始", equipmentNo);
        if (!StringUtils.hasText(equipmentNo)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("treadmill.bind.equipmentno"));
        }
        ZnsUserEntity loginUser = getLoginUser();
        if (loginUser == null) {
            loginUser = znsUserService.findById(request.getUserId());
        }
        BindTreadmillResponseDto resp = new BindTreadmillResponseDto();


        request.setAppVersion(getAppVersion());
        String lockKey = ApiConstants.USER_BIND_TREADMILL + loginUser.getEmailAddressEn();
        ZnsTreadmillEntity treadmillEntity = treadmillService.getTreadmillByUniqueCode(equipmentNo);
        if (treadmillEntity == null) {
            log.info("[bindTreadmill]----绑定跑步机, equipmentNo={},equipmentNo没有查询到设备", equipmentNo);
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("treadmill.bind.equipmentno"));
        }
        //查找设备配置
        if (EquipmentTypeEnum.BIKE.getType().equals(treadmillEntity.getEquipmentType())
        || EquipmentTypeEnum.BICYCLE.getType().equals(treadmillEntity.getEquipmentType())){
            EquipmentSpeedConfigDo equipmentSpeedConfigDo = equipmentSpeedConfigService.getConfig(new EquipmentSpeedConfigQuery().setEquipmentInfo(treadmillEntity.getProductCode()));
            resp.setEquipmentSpeedConfigDo(equipmentSpeedConfigDo);
        }

        resp.setSupportDeviceLock(treadmillEntity);
        if (getAppVersion() >= 4048) {
            Result result = checkNewVersionUp(resp, treadmillEntity, equipmentNo, request.getEquipmentVersion());
            if (Objects.nonNull(result)) {
                return result;
            }
            if (Objects.equals(request.getEquipmentStatus(), 3)) {
                //设备未激活直接返回
                return CommonResult.fail(ActivityError.DEVICE_NOT_ACTIVE);
            }
        }

        if (Objects.equals(request.getEquipmentStatus(), 3)) {
            //设备未激活直接返回
            return CommonResult.success(resp);
        }

        //更新设备激活信息
        EquipmentQualityPopVo qualityPopVo = userEquipmentManager.updateActivateInfo(treadmillEntity, request.getEquipmentVersion(), request.getEquipmentStatus(), loginUser.getId());

        //前端从设备获取的类型作为默认值
        Integer measureUnit = request.getMeasureUnit();   //0：公制 ，1：英制
        Boolean measureUnitIsError = false;
        ZnsEquipmentProductionBatchEntity batchEntity = znsEquipmentProductionBatchService.getByBatchNumber(treadmillEntity.getBatchNumber());
        if (batchEntity != null) {
            resp.setMaxSpeed(batchEntity.getMaxSpeed()).setMaxMaxSpeed(batchEntity.getMaxMaxSpeed()).setMaxSpeedMph(batchEntity.getMaxSpeedMph()).setMaxMaxSpeedMph(batchEntity.getMaxMaxSpeedMph());
            resp.setSportDiameter(batchEntity.getSportDiameter());
            if (batchEntity.getMaxSpeedMph().compareTo(BigDecimal.ZERO) == 0) {
                resp.setMaxSpeedMph(SportsDataUnit.kphToMph(batchEntity.getMaxSpeed()));
            }
            if (batchEntity.getMaxMaxSpeedMph().compareTo(BigDecimal.ZERO) == 0) {
                resp.setMaxMaxSpeedMph(SportsDataUnit.kphToMph(batchEntity.getMaxMaxSpeed()));
            }
            if (!Objects.equals(batchEntity.getMeasuringSystem(), 2)) {
                if (measureUnit != batchEntity.getMeasuringSystem()) { //measuringSystem 不等于2的情况下measureUnit应和其一直
                    measureUnitIsError = true;
                }
                //从批次表替换设备单位
                measureUnit = batchEntity.getMeasuringSystem();  // 0公制，1英制
            }

            //设置批次设备单位
            List<String> kphEquipmentNos = sysConfigService.selectConfigListByKey(ConfigKeyEnums.KPH_EQUIPMENT_NO_LIST.getCode(), String.class);
            if (!CollectionUtils.isEmpty(kphEquipmentNos) && kphEquipmentNos.contains(equipmentNo)) {
                //公制设备特殊处理
                resp.setBatchMeasureUnit(0); //0公制
            } else {
                resp.setBatchMeasureUnit(batchEntity.getMeasuringSystem());
            }
        }
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!LockHolder.tryLock(lock, 60, 60)) {
                log.info("bindTreadmill 没有获得锁，锁失败 " + lockKey);
                return CommonResult.success(resp);
            }
            log.info("bindTreadmill 获得锁成功 " + lockKey);
            SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.version_app_2_0_5.getCode());
            Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());

            //版本兼容
            if (getAppVersion() < 3080) {
                loginUser.setMeasureUnit(measureUnit);
                ZnsUserEntity update = new ZnsUserEntity();
                update.setId(loginUser.getId());
                update.setMeasureUnit(measureUnit);
                update.setModifieTime(ZonedDateTime.now());
                znsUserService.update(update);
            }

            ZnsUserEquipmentEntity userEquipmentOne = userEquipmentService.getUserEquipmentOneDelete(loginUser.getId());
            if (Objects.isNull(userEquipmentOne) && loginUser.getManualSetMetric() == 0) {
                //修改用户计量单位,如果是第一次连接
                ZnsUserEntity update = new ZnsUserEntity();
                update.setId(loginUser.getId());
                update.setMeasureUnit(measureUnit);
                loginUser.setMeasureUnit(measureUnit);
                update.setModifieTime(ZonedDateTime.now());
                znsUserService.update(update);
            } else {
                //如果不是返回前端用户单位
                measureUnit = loginUser.getMeasureUnit();
            }
            //查询设备
            log.info("equipmentNo======before====" + equipmentNo + ",之前");
            if (StringUtils.hasText(equipmentNo)) {
                equipmentNo = equipmentNo.trim();
            }
            log.info("equipmentNo=====after=====" + equipmentNo + ",之后");

            if (!SpringContextUtils.isOnline() && treadmillEntity == null) {
                log.info("非线上环境 并且  treadmillEntity == null  ");
                treadmillEntity = treadmillService.selectTreadmillLikeByUniqueCode(equipmentNo);
                //productCode补偿
                treadmillService.compensateProductCode(treadmillEntity);
            }
            if (treadmillEntity != null) {
                //更新设备最近连接时间
                ZnsTreadmillEntity updateEntity = new ZnsTreadmillEntity();
                updateEntity.setId(treadmillEntity.getId());
                updateEntity.setEquipmentVersion(request.getEquipmentVersion());
                updateEntity.setLastConnectTime(ZonedDateTime.now());
                updateEntity.setModifyTime(ZonedDateTime.now());
                if (request.getIsNewOta() != null){
                    updateEntity.setIsNewOta(request.getIsNewOta());
                }
                if (Objects.equals(treadmillEntity.getEquipmentMainType(), DeviceConstant.EquipmentMainTypeEnum.BICYCLE.code)) {
                    //脚踏需要更新公英值单位
                    updateEntity.setMeasureUnit(request.getMeasureUnit());
                }
                treadmillService.update(updateEntity);
            }
            log.info("==bindTreadmill===" + JsonUtil.writeString(treadmillEntity));

            if (request.getEquipmentConfigId() != null) {                // 前端没有EquipmentModel，给他们打补钉
                EquipmentConfig equipmentConfig = equipmentConfigService.selectEquipmentConfigById(request.getEquipmentConfigId());
                if (equipmentConfig == null) {
                    if (treadmillEntity != null) {
                        request.setEquipmentModel(treadmillEntity.getProductCode());
                    } else {
                        equipmentConfig = equipmentConfigService.selectEquipmentConfigByIsDefult(1);
                        request.setEquipmentModel(equipmentConfig.getEquipmentInfo());
                    }
                } else {
                    request.setEquipmentModel(equipmentConfig.getEquipmentInfo());
                }
            }
            ZnsUserEquipmentEntity userEquipment = null;
            if (loginUser.getId() != null && treadmillEntity != null && treadmillEntity.getId() != null) {
                userEquipment = userEquipmentService.getUserEquipment(loginUser.getId(), treadmillEntity.getId());
            }
            String key = String.format(RedisKeyConstant.USER_EQUIPMENT_CONNECT, equipmentNo);
            redissonClient.getBucket(key).delete();
            resp.setIsAllowShare(YesNoStatus.YES.getCode());
            if (getAppVersion() >= 40500) {
                if (!treadmillWhiteListService.checkEquipmentWhiteList(treadmillEntity.getId())) {
                    //LA 设备白名单 跳过
                    userEquipmentShareManager.bindEquipmentShare(treadmillEntity, loginUser, userEquipment);
                }else {
                    resp.setIsAllowShare(YesNoStatus.NO.getCode());
                }
            }
            ZnsUserEquipmentEntity result = userEquipmentManager.saveUserEquipment(loginUser.getId(), treadmillEntity, request.getEquipmentType(), request);
            if (result != null) {
                // 完成蓝牙连接任务
                userTaskDetailService.completeLevelTask(loginUser.getId(), UserExpObtainSubTypeEnum.FIRST_BLUETOOTH_CONNECTION.getCode(), false);
                userTaskBizService.completeEvent(new EventTriggerDto().setUser(loginUser).setEventSubType(TaskConstant.TakEventSubTypeEnum.DEVICE_BLUETOOTH.getCode()));
                if (Objects.isNull(treadmillEntity.getUserId()) || treadmillEntity.getUserId() == 0) {
                    treadmillEntity.setUserId(loginUser.getId());
                    treadmillEntity.setEquipmentVersion(request.getEquipmentVersion());
                    treadmillService.update(treadmillEntity);
                }
                if (userEquipment != null) {
                    resp.setFirstBindTop(0);
                } else {
                    resp.setFirstBindTop(1);
                }
                resp.setBindSuccessImage(data.get("bindSuccessImage") + "");
                resp.setBindTopText(data.get("bindTopText") + "");
                resp.setBindBottomText(data.get("bindBottomText") + "");

                EquipmentBrandConfig equipmentBrandConfig = equipmentBrandConfigService.selectEquipmentBrandConfigByBrand(treadmillEntity.getBrand());
                if (equipmentBrandConfig != null) {
                    resp.setLogoImage(equipmentBrandConfig.getLogoImage());
                }
                if ("unknown".equals(treadmillEntity.getBrand())) {
                    resp.setBrandName("");
                } else {
                    resp.setBrandName(treadmillEntity.getBrand());
                }

                UserThemeListVo userThemeListVo = equipmentBrandConfigService.getTheme(treadmillEntity.getBrand());
                resp.setBrandTheme(userThemeListVo);
                //不再弹窗主题推荐
                resp.setPopCount(1);//  popCount = 1 表示已经弹过

                //返回nfc信息
                boolean showNfcPop = getTreadmillNfc(request, userEquipment, treadmillEntity);
                resp.setShowNfcPop(showNfcPop);
                resp.setFactoryRcStatus(treadmillEntity.getFactoryRcStatus());
                resp.setMeasureUnit(measureUnit);
                resp.setMeasureUnitIsError(measureUnitIsError);
                //返回质保弹框信息
                resp.setShowQualityPop(qualityPopVo.getShowQualityPop());
                resp.setQualityDayNum(qualityPopVo.getQualityDayNum());
                resp.setDayNumType(qualityPopVo.getDayNumType());

                log.info("[投流用户] 连接真实设备 ，发送转正事件:userId:{}", loginUser.getId());
                queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.PutChannelUserShouldUpgradeEvent.getEventType(),PutChannelUserShouldUpgradeEvent.of(loginUser.getId(), 2));
                //更新老版ota记录
                treadmillEntity = treadmillService.findByUniqueCode(equipmentNo);
                equipmentManager.updateSuccUpgradeLog(treadmillEntity);
                return CommonResult.success(resp);
            } else {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
            }
        } catch (BaseException e) {
            log.error("bindTreadmill业务异常", e);
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("bindTreadmill异常", e);
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("bindTreadmill 删除锁 " + lockKey);
                lock.unlock();
            }
        }
    }

    /**
     * 检查用户是否连接过脚踏机
     *
     * @return true 连接过 false 没有
     */
    @PostMapping("/checkUserConnectedDeskBike")
    public Result<Boolean> checkUserConnectedDeskBike() {
        Long userId = getUserId();
        UserEquipmentQuery query = UserEquipmentQuery.builder()
                .userId(userId)
                .equipmentType(DeviceConstant.EquipmentTypeEnum.TYPE_30.getCode())
                .connectionVersionLt(VersionConstant.V4_7_1)
                .build();
        return CommonResult.success(znsUserEquipmentService.exists(query));
    }

    /**
     * 新版本升级检测，特殊升级任务强制升级
     *
     * @param resp
     * @param treadmillEntity
     * @param equipmentNo
     * @return
     */
    private Result<BindTreadmillResponseDto> checkNewVersionUp(BindTreadmillResponseDto resp, ZnsTreadmillEntity treadmillEntity, String equipmentNo, Integer equipmentVersion) {
        Integer currentVersion = Optional.ofNullable(equipmentVersion).orElse(treadmillEntity.getEquipmentVersion());
        if (Objects.isNull(currentVersion)) {
            return null;
        }
        //特殊分组设备强制升级处理
        EquipmentVersionRequest equipmentVersionRequest = new EquipmentVersionRequest();
        equipmentVersionRequest.setEquipmentNo(equipmentNo);
        equipmentVersionRequest.setCurrentVersion(currentVersion);
        equipmentVersionRequest.setNeedSelectStatus(1);
        DeviceVersionResp deviceVersionResp = equipmentManager.checkTreadmillVersion(equipmentVersionRequest, getLoginUser());
        if (!DeviceConstant.UpTypeEnum.upType_2.getCode().equals(deviceVersionResp.getUpType())) {
            return null;
        }
        if (deviceVersionResp.getVersion() <= currentVersion) {
            return null;
        }
        //检测是否特定升级任务
        if (!deviceVersionResp.isSpecial()) {
            //不是特定任务
            return null;
        }
        //设备未激活直接返回
        resp.setUpgradeStatus(deviceVersionResp.getUpgradeStatus());
        return CommonResult.fail(ActivityError.DEVICE_FORCE_UPGRADE.getCode(), ActivityError.DEVICE_FORCE_UPGRADE.getMsg(), resp);
    }

    private boolean getTreadmillNfc(BindTreadmillRequest request, ZnsUserEquipmentEntity userEquipment, ZnsTreadmillEntity treadmillEntity) {
        boolean showNfcPop = false;

        //App首页请求且 首次连接设备 则需要展示弹窗
        if (request.getIsNfcPop() == 1 && Objects.isNull(userEquipment)) {
            TreadmillNfcLog treadmillNfc = treadmillNfcLogService.findByBluetoothMac(treadmillEntity.getBluetoothMac());
            //设备支持nfc
            showNfcPop = Objects.nonNull(treadmillNfc);
        }
        return showNfcPop;
    }

    /**
     * 绑定设备图片
     *
     * @param request
     * @return
     */
    @PostMapping("/bindDevImage")
    public Result<BindDevResp> bindDevImage(@RequestBody BindDevRequest request) {
        ZnsUserEquipmentEntity userEquipment = userEquipmentService.findById(request.getId());

        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.version_app_2_2_0.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());

        int day = MapUtil.getInteger(data.get("equipmentBrandModifyLimit"), 365);

        ZonedDateTime endDate = DateUtil.addDays(ZonedDateTime.now(), day);
        if (userEquipment.getEquipmentImageUpdateTime() != null && userEquipment.getEquipmentImageUpdateTime().toInstant().toEpochMilli() < endDate.toInstant().toEpochMilli()) {
            return CommonResult.success(new BindDevResp(0, I18nMsgUtils.getMessage("treadmill.bind.modified", day)));
        }
        if (StringUtils.hasText(request.getEquipmentImage())) {
            userEquipment.setEquipmentImage(request.getEquipmentImage());
            userEquipment.setEquipmentImageUpdateTime(ZonedDateTime.now());
            userEquipmentService.updateUserEquipment(userEquipment);
        }
        return CommonResult.success(new BindDevResp());
    }

    /**
     * 保存用户的通知订阅状态
     *
     * @return
     */
    @PostMapping("/saveNotifyState")
    public Result<Boolean> saveNotifyState(@RequestBody NotifyStateSaveRequest req) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), CommonError.NEED_LOGIN.getMsg());
        }

        if (Objects.isNull(req.getState()) || req.getState() == -1) {
            return CommonResult.fail(CommonError.PARAM_LACK.getMsg());
        }

        znsUserService.saveNotifyState(loginUser.getId(), req.getState());
        return CommonResult.success(true);
    }

    /**
     * 我的页面
     *
     * @return
     * @tag 2.4.0
     */
    @PostMapping("/myHomepage")
    public Result<MyHomepageVo> myHomepage() {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.fail(CommonError.NEED_LOGIN.getCode(), CommonError.NEED_LOGIN.getMsg());
        }
        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        MyHomepageVo myHomepageVo = new MyHomepageVo();
        getMyHomePageNew(myHomepageVo, loginUser);
        //是否盛和版本
        if (checkVersion) {
            myHomepageVo.setShc(1);
        } else {
            myHomepageVo.setShc(0);
        }
        //赛事奖金
        ZnsUserAccountEntity userAccount = userAccountService.getByUserId(loginUser.getId());
        if (Objects.nonNull(userAccount)) {
            myHomepageVo.setTotalBonus(userAccount.getTotalBonus());
        } else {
            myHomepageVo.setTotalBonus(BigDecimal.ZERO);
        }
        //赛事奖金货币信息
        myHomepageVo.setCurrency(I18nConstant.buildCurrency(userAccount.getCurrencyCode()));

        List<Map<String, Object>> mapList = userMedalService.selectHomePageList(loginUser.getId());
        myHomepageVo.setUserMedalList(mapList);
        //更新历史经验值
        ZnsUserEntity updateUserEntity = new ZnsUserEntity();
        updateUserEntity.setId(loginUser.getId());
        updateUserEntity.setHistoryUserExp(loginUser.getUserExp());
        znsUserService.update(updateUserEntity);
        //添加商城和分销开关
        myHomepageVo.setMallSwitch(sysConfigService.selectConfigByKey("mall.switch"));
        //分销开关
        myHomepageVo.setDistributionSwitch(sysConfigService.selectConfigByKey("distribution.switch"));
        ContactUsListVO result = appCommonConfigService.getContractUsInfo(loginUser);
        myHomepageVo.setContactUs(result);
        myHomepageVo.setMyRunScore(znsUserService.getAllUserScore(loginUser.getId()));
        String monthStr = DateUtil.formateDateStr(DateUtil.addMonths(ZonedDateTime.now(), -1), DateUtil.YYYY_MM);
        MonthRunData monthRunData = monthRunDataDao.selectMonthRunDataByUserIdMonth(loginUser.getId(), monthStr);
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.rank_billboard_config.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        int showMonthBoardDay = MapUtil.getInteger(data.get("showMonthBoardDay"), 3);
        // 如果已经点过
        if (monthRunData != null && Objects.equals(monthRunData.getMonthHonourPop(), 1)) {
            myHomepageVo.setShowMonthBoard(0);
        } else {
            if (ZonedDateTime.now().toInstant().toEpochMilli() < DateUtil.addDays(DateUtil.startOfDate(DateUtil.getFirstOfMonth(ZonedDateTime.now())), showMonthBoardDay).toInstant().toEpochMilli()) {
                myHomepageVo.setShowMonthBoard(1);
            } else {
                myHomepageVo.setShowMonthBoard(0);
            }
        }
        // 卷角标
        String mallCountryCode = userExtraBizService.findUserMallCountryCodeOrDefault(loginUser, null);
        Integer userCouponCount = userCouponService.countUserCanUserCoupon(loginUser.getId(), getAppVersion(), mallCountryCode);
        myHomepageVo.setUserCouponCount(userCouponCount);

        SysConfig member = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.HOME_PAGE_MEMBER.getCode());

        SysConfig memberAmount;
        if (Objects.equals(loginUser.getMemberType(), 1)) {
            //付费会员
            memberAmount = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.HOME_PAGE_MEMBER_AMOUNT.getCode());
        } else {
            //普通会员
            memberAmount = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.HOME_PAGE_MEMBER.getCode());
        }

        //道具橱窗数量
        Long userPropBagNum = userPropBagService.countByQuery(UserPropBagQuery.builder().uerId(loginUser.getId()).isDelete(YesNoStatus.NO.getCode()).build());
        myHomepageVo.setUserPropBagNum(userPropBagNum);

        //我的证书数量
        Long certificateNum = userRunCertificateService.countByQuery(UserRunCertificateQuery.builder().userId(loginUser.getId()).isDelete(YesNoStatus.NO.getCode()).build());
        myHomepageVo.setCertificateNum(certificateNum);

        myHomepageVo.setMemberShipUrl(member.getConfigValue());
        myHomepageVo.setMemberAmountUrl(memberAmount.getConfigValue());

        boolean userLevelClubFlag = clubManager.checkUserLevelClubFlag(loginUser.getId());
        //用户是否是kol
        UserKol userKol = userKolService.findByUserIdSigned(loginUser.getId());
        myHomepageVo.setKol(userKol != null);
        if (myHomepageVo.isKol() || userLevelClubFlag) {
            //kol是否拥有俱乐部
            myHomepageVo.setHasClub(clubService.findByOwnerId(loginUser.getId()).isPresent());
            boolean kolClubCreateFlag = !myHomepageVo.isHasClub() && myHomepageVo.isKol() && UserKolClubAuthorizationEnum.YES.getCode() == userKol.getClubAuthorization();
            boolean userLevelClubCreateFlag = !myHomepageVo.isHasClub() && userLevelClubFlag;
            myHomepageVo.setCanCreateClub(kolClubCreateFlag || userLevelClubCreateFlag);
        } else {
            myHomepageVo.setHasClub(false);
            myHomepageVo.setCanCreateClub(false);
        }
        myHomepageVo.setMemberType(loginUser.getMemberType());
        //付费会员需要有过期时间
        if (Objects.equals(loginUser.getMemberType(), 1)) {
            VipUser vipUser = vipUserService.selectActivationVipByUserId(loginUser.getId());
            myHomepageVo.setMemberExpireTime(Objects.nonNull(vipUser) ? vipUser.getVipEndtime() : null);
        }
        myHomepageVo.setNoPayOrderNum(orderService.count(new OrderQuery().setStatus(OrderStatusEnum.NEW.getStatus()).setUserId(loginUser.getId()).setIsUserDelete(0).setSourceType(OrderConstant.OrderSourceEnum.NEW_PITPAT.getType())));

        //是否在竞技赛中
        Optional<UserInGameDto> userInGameDto = userActivityManager.checkUserInCompetitiveGame(loginUser.getId());
        if (userInGameDto.isPresent()) {
            myHomepageVo.setInGame(userInGameDto.get().isInGame() ? 1 : 0);
        } else {
            myHomepageVo.setInGame(0);
        }
        myHomepageVo.setMyInteractionNum(communityContentService.getMyInteractionNum(loginUser.getId()));

        List<ClubInfoDto> myClubList = fillMyClubList(loginUser);
        myHomepageVo.setMyClubList(myClubList);
        List<AppCommunityContentVo> myCommunityList = fillmyCommunityList(loginUser, loginUser);
        myHomepageVo.setMyCommunityList(myCommunityList);
        return CommonResult.success(myHomepageVo);
    }

    /**
     * 查询社区帖子信息
     *
     * @param loginUser 登录人
     * @param user      要查看的用户
     * @return
     */
    private List<AppCommunityContentVo> fillmyCommunityList(ZnsUserEntity loginUser, ZnsUserEntity user) {
        CommunityContentPo po = new CommunityContentPo();
        po.setSource(0);
        po.setSortType(1);
        Page<AppCommunityContentVo> page = communityBusiness.list(po, loginUser.getId(), loginUser.getAppVersion(), loginUser.getLanguageCode(), user.getId());
        return page.getRecords();
    }

    private List<ClubInfoDto> fillMyClubList(ZnsUserEntity loginUser) {
        List<ClubInfoDto> myClubList = new ArrayList<>();
        ClubSearchQuery query = new ClubSearchQuery();
        query.setOwnerUserid(loginUser.getId());
        query.setType(0);
        Page<Club> clubPage = clubService.findPageByQuery(query);
        if (!CollectionUtils.isEmpty(clubPage.getRecords())) {
            myClubList = clubPage.getRecords().stream().map(e -> {
                ClubInfoDto clubInfoDto = new ClubInfoDto();
                clubInfoDto.setClubId(e.getId());
                clubInfoDto.setClubName(e.getName());
                clubInfoDto.setClubLogo(e.getLogo());
                clubInfoDto.setClubLevel(e.getClubLevel().substring(2));//去除前缀LV
                return clubInfoDto;
            }).collect(Collectors.toList());
        }
        return myClubList;
    }


    private void getMyHomePageNew(MyHomepageVo myHomepageVo, ZnsUserEntity user) {
        myHomepageVo.setNickname(user.getFirstName());
        myHomepageVo.setHeadPortrait(user.getHeadPortrait());
        myHomepageVo.setAvatarFrame(user.getAvatarFrame());
        myHomepageVo.setUserCode(user.getUserCode());
        myHomepageVo.setFollowCount(userFriendService.getFollowCount(user.getId()));
        myHomepageVo.setFansCount(userFriendService.getFansCount(user.getId()));
        UserExpDto expDto = new UserExpDto();
        //新版本重写用户等级经验
        if (sysConfigService.enableUserNewLevel(user.getId())) {
            UserLevel userLevel = userLevelService.findByUserId(user.getId());
            Integer level = userLevel.getLevel();
            myHomepageVo.setHistoryUserLevel(userLevel.getHistoryLevel());
            myHomepageVo.setUserLevel(level);
            myHomepageVo.setCurrentExp(userLevel.getExperience());
            myHomepageVo.setHistoryExp(userLevel.getHistoryExperience());
            UserLevelRule userLevelRule = userLevelRuleService.findByLevel(level);
            UserExpDetailDto currentExpDetailDto = new UserExpDetailDto();
            currentExpDetailDto.setMin(userLevelRule.getExpLow());
            currentExpDetailDto.setMax(userLevelRule.getExpHigh());
            currentExpDetailDto.setExp(userLevel.getExperience());
            currentExpDetailDto.setLevel(level);
            UserExpDetailDto historyExpDetailDto = new UserExpDetailDto();
            historyExpDetailDto.setExp(userLevel.getHistoryExperience());
            historyExpDetailDto.setLevel(userLevel.getHistoryLevel());
            expDto.setCurrentExp(currentExpDetailDto);
            expDto.setHistoryExp(historyExpDetailDto);
//            userLevelService.updateHistoryUserExpByUserId(userLevel.getExperience(),user.getId());
            myHomepageVo.setLevelMaxExp(userLevelRule.getExpHigh());
            myHomepageVo.setLevelMinExp(userLevelRule.getExpLow());
            myHomepageVo.setUserExp(expDto);
        } else {
            myHomepageVo.setUserLevel(user.getUserLevel());
            expDto.setHistoryExp(expUserService.getExpDetail(user.getHistoryUserExp()));
            expDto.setCurrentExp(expUserService.getExpDetail(user.getUserExp()));
            znsUserService.updateHistoryUserExpByUserId(user.getUserExp(), user.getId());
            myHomepageVo.setUserExp(expDto);
            ExpConfig expConfigMax = expConfigService.selectExpConfigLv(user.getHistoryUserExp());
            myHomepageVo.setHistoryUserLevel(expConfigMax.getLevel());
            ExpConfig expConfig = expConfigService.selectExpConfigByLevel(user.getUserLevel());
            if (Objects.nonNull(expConfig)) {
                myHomepageVo.setLevelMaxExp(expConfig.getLv());
            } else {
                myHomepageVo.setLevelMaxExp(0);
            }
            ExpConfig expLowConfig = expConfigService.selectExpConfigByLevel(user.getUserLevel() - 1);
            if (Objects.nonNull(expLowConfig)) {
                myHomepageVo.setLevelMinExp(expLowConfig.getLv() + 1);
            } else {
                myHomepageVo.setLevelMinExp(0);
            }
        }
        //总记录统计
        Map<String, Object> totalStatistics = userRunDataDetailsService.getTotalRecordMap(user.getId());
        myHomepageVo.setRunDays(MapUtils.getInteger(totalStatistics, "totalCount"));
        Integer totalRunCount = runActivityUserService.findAllActivityUserCountByUserId(user.getId());
        myHomepageVo.setTotalRunCount(totalRunCount);
        myHomepageVo.setRunMileage(MapUtils.getDouble(totalStatistics, "run_mileage").intValue());
        long totalMedalCount = userMedalService.findCount(new QueryWrapper<UserMedal>().eq("user_id", user.getId()).eq("is_delete", 0).eq("obtain", 1).eq("is_valid", 0));
        myHomepageVo.setTotalMedalCount((int) totalMedalCount);
        long noShowMedalCount = userMedalService.findCount(new QueryWrapper<UserMedal>().eq("user_id", user.getId()).eq("is_delete", 0).eq("obtain", 1).eq("is_valid", 0).eq("is_show", 0));
        myHomepageVo.setHasNoShowMedal(noShowMedalCount > 0 ? 1 : 0);
        UserIdentityDo userIdentityDo = userIdentityService.findByQuery(UserIdentityQuery.builder().userId(user.getId()).identityType(UserIdentityEnum.KOC.getCode()).build());
        if (Objects.nonNull(userIdentityDo)) {
            myHomepageVo.setIdentityType(userIdentityDo.getIdentityType().getCode());
            myHomepageVo.setIdentityCredential(userIdentityDo.getIdentityCredential());
        }
    }


    @FillerMethod
    private OtherPersonPageVo getBasicMyHomePage(ZnsUserEntity user, ZnsUserEntity loginUser) {
        OtherPersonPageVo vo = new OtherPersonPageVo();
        vo.setUserId(user.getId());
        vo.setNickname(user.getFirstName());
        vo.setHeadPortrait(user.getHeadPortrait());
        vo.setUserCode(user.getUserCode());
        vo.setMemberType(user.getMemberType());
        vo.setFollowCount(userFriendService.getFollowCount(user.getId()));
        vo.setFansCount(userFriendService.getFansCount(user.getId()));
        vo.setUserLevel(user.getUserLevel());
        //新版本重写用户等级经验
        if (sysConfigService.enableUserNewLevel(user.getId())) {
            UserLevel userLevel = userLevelService.findByUserId(user.getId());
            vo.setUserLevel(userLevel.getLevel());
        }
        vo.setIsPrivacy(user.getIsPrivacy());
        vo.setFlag(CountryFlagConstant.FlagMap.get(user.getCountry()));
        if (StringUtils.hasText(user.getCountryCode())) {
            CountryI18nQuery countryI18nQuery = CountryI18nQuery.builder()
                    .countryCode(user.getCountryCode()).languageCode(loginUser.getLanguageCode()).build();
            CountryI18nEntity countryI18nEntity = countryI18nService.findByQuery(countryI18nQuery);
            if (Objects.nonNull(countryI18nEntity)) {
                vo.setCountry(countryI18nEntity.getName());
            }
        }
        if (StringUtils.hasText(user.getStateCode())) {
            AreaI18nQuery areaI18nQuery = AreaI18nQuery.builder()
                    .areaCode(user.getStateCode()).languageCode(loginUser.getLanguageCode()).build();
            AreaI18nEntity areaI18nEntity = areaI18nService.findByQuery(areaI18nQuery);
            if (Objects.nonNull(areaI18nEntity)) {
                vo.setState(areaI18nEntity.getAreaName());
            }
        }
        vo.setAvatarFrame(user.getAvatarFrame());
        return vo;
    }

    /**
     * 他人我的页面
     *
     * @param request
     * @return
     */
    @PostMapping("/otherHomepage")
    @FillerMethod
    public Result<OtherPersonPageVo> otherHomepage(@RequestBody FriendIdRequest request) {
        Long friendId = request.getFriendId();
        ZnsUserEntity znsUser = znsUserService.findById(friendId);
        if (Objects.isNull(znsUser)) {
            log.info("用户不存在");
            return CommonResult.fail(UserError.USER_LOGGED_OFF.getCode(), I18nMsgUtils.getMessage("user.deactivated"));
        }
        if (znsUser.getIsDelete() == 1) {
            log.info("用户已注销");
            return CommonResult.fail(UserError.USER_LOGGED_OFF.getCode(), I18nMsgUtils.getMessage("user.deactivated"));
        }
        //判断当前用户是否是粉丝
        ZnsUserEntity loginUser = getLoginUser();
        String languageCode = getLanguageCode();
        OtherPersonPageVo res = getBasicMyHomePage(znsUser, loginUser);
        Integer relationType = userFriendService.getRelationType(loginUser.getId(), friendId);
        res.setRelationType(relationType);

        //查询用户称号
        UserTitleDo userTitleDo = userTitleService.findByUserId(friendId);
        if (Objects.nonNull(userTitleDo)) {
            res.setUserTitle(userTitleDo.getUserTitle());
        }
        if (!loginUser.getId().equals(friendId)
                && UserConstant.PrivacyEnum.ISPRIVACY_1.getCode().equals(znsUser.getIsPrivacy())
                && !UserConstant.RelationTypeEnum.RELATIONTYPE_1.getCode().equals(relationType)
                && !UserConstant.RelationTypeEnum.RELATIONTYPE_2.getCode().equals(relationType)) {
            // 主页的用户为私密账号，当前人未关注主页用户，则返回基础信息
            return CommonResult.success(res);
        }

        //总记录统计
        Map<String, Object> totalStatistics = userRunDataDetailsService.getTotalRecordMap(friendId);

        res.setRunDays(MapUtils.getInteger(totalStatistics, "totalCount"));
        BigDecimal runMileage = new BigDecimal(totalStatistics.get("run_mileage").toString());
        res.setRunMileage(runMileage);
        UserRunDataVo userRunDataVo = userRunDataDetailsService.userRunDataStatistics(ZonedDateTimeUtil.startOfMonth(), friendId, null);
        res.setMonthRunDays(userRunDataVo.getRunDays());
        res.setMonthRunMileage(userRunDataVo.getSumRunMileage());
        //个人跑步记录最小id
        Long minDetailId = userRunDataDetailsService.getUserMinActivityRunDetailId(friendId);
        BigDecimal oneBestPace = BigDecimal.ZERO;
        Integer deviceType = 0; // 部分数据只查跑步数据
        res.setRankedAchievement(new RankedActivityAchievementDto(0L, BigDecimal.ZERO, BigDecimal.ZERO, 1L, RankedLevelEnums.AMATEUR_LEVEL_1.getName(), RankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank(), loginUser.getAppVersion()));
        if (Objects.nonNull(minDetailId)) {
            oneBestPace = userRunDataDetailsService.getUserBestPaceByRunMileage(friendId, minDetailId, 1600, deviceType);
            res.setRankedAchievement(getRankedAchievement(znsUser, getAppVersion()));
        }
        res.setOneBestPace(oneBestPace.multiply(new BigDecimal("1.6")).setScale(0, RoundingMode.DOWN));//只要1mile最佳配速
        PowerAchievementVo powerAchievementVo = userRunDataDetailsService.getUserMaxDistance(friendId, minDetailId, null);
        res.setMaxDistance(powerAchievementVo.getMaxDistance());

        fillUserRecentActivityList(friendId, loginUser, res, relationType);

        long totalMedalCount = userMedalService.findCount(new QueryWrapper<UserMedal>().eq("user_id", friendId).eq("is_delete", 0).eq("obtain", 1).eq("is_valid", 0));
        res.setTotalMedalCount(totalMedalCount);
        List<UserMedal> list = userMedalService.findList(new QueryWrapper<UserMedal>().eq("user_id", znsUser.getId()).eq("is_delete", 0).eq("obtain", 1).eq("is_valid", 0).eq("is_show_home_page", 1).orderByAsc("pos"));
        if (!CollectionUtils.isEmpty(list)) {
            List<UserMedalDto> userMedalList = new ArrayList<>();
            List<Long> medalConfigIds = list.stream().map(UserMedal::getMedalConfigId).collect(Collectors.toList());
            List<MedalConfig> medalConfigs = medalConfigService.selectMedalConfigListByIds(medalConfigIds);
            Map<Long, MedalConfig> medalConfigMap = medalConfigs.stream().collect(Collectors.toMap(MedalConfig::getId, Function.identity()));
            for (UserMedal userMedal : list) {
                UserMedalDto userMedalDto = new UserMedalDto();
                BeanUtils.copyProperties(userMedal, userMedalDto);
                MedalConfig medalConfig = medalConfigMap.get(userMedal.getMedalConfigId());
                if (Objects.isNull(medalConfig)) {
                    continue;
                }
                userMedalService.setTarget(medalConfig, userMedal);
                userMedalDto.setUrl(medalConfig.getUrl());

                // 查询国际化数据
                MedalI18n medalI18n = medalI18nService.findByQuery(MedalI18nQuery.builder().medalId(userMedal.getMedalConfigId()).langCode(languageCode).defaultLangCode(medalConfig.getDefaultLangCode()).build());
                userMedalDto.setName(Objects.nonNull(medalI18n) ? medalI18n.getName() : medalConfig.getName());
                userMedalDto.setRemark(Objects.nonNull(medalI18n) ? medalI18n.getRemark() : medalConfig.getRemark());

                List<Map<String, Object>> processList = new ArrayList<>();
                Map<String, Object> process1 = new HashMap<>();
                Map<String, Object> process2 = new HashMap<>();

                process1.put("currentProcess", userMedalDto.getCurrentProcess1());
                process1.put("targetProcess", userMedalDto.getTargetProcess1());


                process2.put("currentProcess", userMedalDto.getCurrentProcess2());
                process2.put("targetProcess", userMedalDto.getTargetProcess2());

                userMedalService.convert(userMedal);                        //转化字段

                process1.put("showProcess", BigDecimalUtil.removeEndOfZero(BigDecimalUtil.getScale(userMedal.getCurrentProcess1(), 2))
                        + "/" + BigDecimalUtil.removeEndOfZero(BigDecimalUtil.getScale(userMedal.getTargetProcess1(), 2))
                );

                process2.put("showProcess", BigDecimalUtil.removeEndOfZero(BigDecimalUtil.getScale(userMedal.getCurrentProcess2(), 2))
                        + "/" + BigDecimalUtil.removeEndOfZero(BigDecimalUtil.getScale(userMedal.getTargetProcess2(), 2))
                );

                processList.add(process1);

                if (Objects.equals(userMedal.getProcessNum(), 2)) { //如果有两个进度条
                    processList.add(process2);
                }
                userMedalDto.setProcessList(processList);
                userMedalList.add(userMedalDto);
            }
            res.setUserMedalList(userMedalList);
        }
        Optional<UserInGameDto> userInGameDto = userActivityManager.checkUserInCompetitiveGame(friendId);
        if (userInGameDto.isPresent()) {
            res.setInGame(userInGameDto.get().isInGame() ? 1 : 0);
        } else {
            res.setInGame(0);
        }
        List<ClubInfoDto> myClubList = fillMyClubList(znsUser);
        res.setMyClubList(myClubList);
        List<AppCommunityContentVo> myCommunityList = fillmyCommunityList(loginUser, znsUser);
        res.setMyCommunityList(myCommunityList);
        return CommonResult.success(res);
    }

    /**
     * 填充用户最近比赛信息
     *
     * @param friendId
     * @param loginUser
     * @param res
     * @param relationType
     */
    private void fillUserRecentActivityList(Long friendId, ZnsUserEntity loginUser, OtherPersonPageVo res, Integer relationType) {
        res.setUserRecentActivityList(new ArrayList<>());
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.ACTIVITY_OFFICIAL_EXCLUDED_USER.getCode());
        List<Long> userIdList = JsonUtil.readList(sysConfig.getConfigValue(), Long.class);
        if (userIdList.contains(friendId)) {
            // 不参加单赛事和系列赛的用户直接过滤掉
            return;
        }
        int limit = 3;
        if (loginUser.getId().equals(friendId)
                || UserConstant.RelationTypeEnum.RELATIONTYPE_1.getCode().equals(relationType)
                || UserConstant.RelationTypeEnum.RELATIONTYPE_2.getCode().equals(relationType)) {
            limit = 10;
        }
        List<UserRecentActivityDto> userRecentActivityList = new ArrayList<>();
        List<UserRecentActivityVo> enrollActivityUserList = runActivityUserService.queryEnrollActivity(friendId, limit);
        List<UserRecentActivityDto> list1 = enrollActivityUserList.stream().map(e -> {
            UserRecentActivityDto dto = new UserRecentActivityDto();
            dto.setActivityId(e.getActivityId());
            dto.setUserDate(e.getCreateTime());
            dto.setMainType(e.getMainType());
            return dto;
        }).collect(Collectors.toList());
        userRecentActivityList.addAll(list1);
        if (!org.springframework.util.CollectionUtils.isEmpty(list1)) {
            List<UserRecentActivityVo> obtainedRankActivityUserList = runActivityUserService.queryObtainedRankActivity(friendId, limit);
            List<UserRecentActivityDto> list2 = obtainedRankActivityUserList.stream().map(e -> {
                UserRecentActivityDto dto = new UserRecentActivityDto();
                dto.setActivityId(e.getActivityId());
                dto.setUserRank(e.getRank());
                ZonedDateTime activityEndTime = DateTimeUtil.parse(e.getActivityEndTime());
                dto.setUserDate(activityEndTime);
                dto.setMainType(e.getMainType());
                return dto;
            }).collect(Collectors.toList());
            userRecentActivityList.addAll(list2);
        }
        if (CollectionUtils.isEmpty(userRecentActivityList)) {
            return;
        }
        List<Long> activityIds = userRecentActivityList.stream().map(UserRecentActivityDto::getActivityId).collect(Collectors.toList());
        Map<Long, ActivityDisseminate> mapByActIdsAndLanguage = activityDisseminateService.findMapByActIdsAndLanguage(activityIds, loginUser.getLanguageCode());
        userRecentActivityList = userRecentActivityList.stream().map(e -> {
            ActivityDisseminate activityDisseminate = mapByActIdsAndLanguage.get(e.getActivityId());
            if (Objects.nonNull(activityDisseminate)) {
                e.setPicture(activityDisseminate.getDisseminatePics());
                e.setTitle(activityDisseminate.getTitle());
            }
            return e;
        }).sorted(Comparator.comparing(UserRecentActivityDto::getUserDate).reversed()).limit(limit).collect(Collectors.toList());
        res.setUserRecentActivityList(userRecentActivityList);
    }

    /**
     * 段位赛数据
     *
     * @param znsUser
     * @param appVersion
     * @return
     */
    private RankedActivityAchievementDto getRankedAchievement(ZnsUserEntity znsUser, Integer appVersion) {
        //查询段位信息
        RankedActivityAchievementDto rankedAchievement;
        UserRankedLevel userRankedLevel = userRankedLevelService.findByUserId(znsUser.getId());
        RankedLevelEnums rankedLevelEnum;
        if (userRankedLevel == null) {
            //未开启段位，查询最小段位
            RankedLevel rankedLevel = rankedLevelService.findByLevelAndRank(RankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), RankedLevelEnums.AMATEUR_LEVEL_1.getRank());
            rankedAchievement = new RankedActivityAchievementDto(rankedLevel.getId(), rankedLevel.getName(), rankedLevel.getLevel(), rankedLevel.getRank());
            rankedLevelEnum = RankedLevelEnums.resolve(rankedLevel.getLevel(), rankedLevel.getRank());
        } else {
            rankedLevelEnum = RankedLevelEnums.resolve(userRankedLevel.getLevel(), userRankedLevel.getRank());
            rankedAchievement = new RankedActivityAchievementDto(userRankedLevel.getRankedLevelId(), userRankedLevel.getName(), userRankedLevel.getLevel(), userRankedLevel.getRank());
        }

        //段位名称i18n
        rankedLevelEnum = Optional.ofNullable(rankedLevelEnum).orElse(RankedLevelEnums.AMATEUR_LEVEL_1);
        rankedAchievement.setName(I18nMsgUtils.getMessage("rankedActivity.rank.RankedLevelEnums." + rankedLevelEnum));

        //查询赛事数据
        UserRankedLevelStatisticsVo statisticsVo = runRankedActivityUserservice.statisticsUserRankedData(znsUser);
        rankedAchievement.setAverageRank(statisticsVo.getAverageRank());
        rankedAchievement.setActivityCont(statisticsVo.getActivityCont());
        rankedAchievement.setCompleteRate(statisticsVo.getCompleteRate());
        return rankedAchievement;
    }

    /**
     * 获取用户信息
     *
     * @param request
     * @param httpServletRequest 已废弃，新的查看
     * @see UserAppController#getInfo()
     */
    @Deprecated(since = "v3.8.0", forRemoval = true)
    @PostMapping("/getUserInfo")
    public Result<GetUserInfoVo> getUserInfo(@RequestBody UserIdRequest request, HttpServletRequest httpServletRequest) {
        String emailAddress = UserContextHolder.getEmail();
        Result result = checkTokenAndEmail(httpServletRequest, emailAddress);
        if (Objects.nonNull(result)) {
            return result;
        }
        GetUserInfoVo userInfo = userManager.getUserInfo(request.getUserId(), getAppVersion());
        return CommonResult.success(userInfo);
    }

    /**
     * 穿戴信息保存
     *
     * @param body
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/saveWearsInformation")
    public Result saveWearsInformation(@RequestBody ZnsUserWearsEntity body, HttpServletRequest httpServletRequest) {
        String emailAddress = HeaderUtil.getEmail(httpServletRequest);
        ZnsUserEntity user = znsUserService.findByEmail(emailAddress);
        String lockKey = RedisConstants.USER_CHANGE_WEAR + user.getId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean tryLock = lock.tryLock(2, 2, TimeUnit.SECONDS);
            if (!tryLock) {
                log.info("获取锁失败");
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getMsg());
            }
        } catch (InterruptedException e) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getMsg());
        }

        Result result = checkTokenAndEmail(httpServletRequest, emailAddress);
        if (Objects.nonNull(result)) {
            return result;
        }

        if (Objects.isNull(user)) {
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "Please log in with the correct Email or Password");
        }

        body.setUserId(user.getId());
        //查询是否已存在穿戴信息
        ZnsUserWearsEntity userWears = userWearsService.getByUserId(user.getId());
        if (Objects.nonNull(body.getSuit()) && body.getSuit() >= 0) {
            // 表示穿套装
            Wears wear = wearsService.getWearByWearIdAndType(UserWearTypeEnum.SUIT.getType(), body.getSuit());
            if (Objects.isNull(wear)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());
            }
            fitSuitBody(body, wear);
        }
        //背部服饰默认-1，兼容前端传值
        if (Objects.equals(body.getBackDecoration(), 0)) {
            body.setBackDecoration(-1);
        }
        if (Objects.equals(body.getHairColor(), -1)) {
            body.setHead(0);
        }
        if (Objects.isNull(userWears)) {
            userWearsService.insert(body);
        } else {
            body.setId(userWears.getId());
            body.setModifieTime(ZonedDateTime.now());
            userWearsService.updateById(body);
        }
        if (Objects.nonNull(body.getGender())) {
            ZnsUserEntity userEntity = znsUserService.findById(user.getId());
            if (!body.getGender().equals(userEntity.getGender()) && getAppVersion() < VersionConstant.V4_7_5){
                throw new BaseException(I18nMsgUtils.getMessage("user.gender.update.after.version"));
            }
            user.setGender(body.getGender());
            znsUserService.update(user);
        }
        UserWearsLog userWearsLog = new UserWearsLog();
        userWearsLog.setUserWearId(body.getId());
        userWearsLog.setCreateTime(ZonedDateTime.now());
        BeanUtil.copyPropertiesIgnoreNull(body, userWearsLog);
        userWearsLog.setId(null);
        userWearsLogService.insert(userWearsLog);
        return CommonResult.success();
    }

    /**
     * 穿套装去除相应部分服装
     *
     * @param body
     * @param wear
     */
    private void fitSuitBody(ZnsUserWearsEntity body, Wears wear) {
        List<Integer> attireTypeIncludedList = JsonUtil.readList(wear.getAttireTypeIncluded(), Integer.class);
        for (Integer wearType : attireTypeIncludedList) {
            switch (UserWearTypeEnum.findByType(wearType)) {
                case HAIR_COLOR:
                    body.setHairColor(0);
                    break;
                case SKIN_COLOUR:
                    body.setSkinColour(0);
                    break;
                case HEAD:
                    body.setHead(0);
                    break;
                case FACE_DECORATION:
                    body.setFaceDecoration(0);
                    break;
                case JACKET:
                    body.setJacket(0);
                    break;
                case TROUSERS:
                    body.setTrousers(0);
                    break;
                case SHOES:
                    body.setShoes(0);
                    break;
                case BACK_DECORATION:
                    body.setBackDecoration(-1);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 获取穿戴信息
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/getWearsInformation")
    public Result<UserWearsInfoResponse> getWearsInformation(@RequestBody GetWearsInformationPo request, HttpServletRequest httpServletRequest) {
        return userWearsBusiness.getUserWears(request.getUserId(), request.getRoomId(), request.getActivityId(), httpServletRequest);
    }

    /**
     * 更新用户3d形象截图
     *
     * @param request
     * @return
     */
    @PostMapping("/updateUser3DStyleImg")
    public Result<Void> updateUser3DStyleImg(@RequestBody UpdateUser3dStyleImgRequest request) {
        if (StringUtils.hasText(request.getD3StyleImg())) {
            userDetailService.updateUser3DStyle(getUserId(), getAppType(), request.getD3StyleImg(), request.getWearSign());
        }
        return CommonResult.success();
    }

    /**
     * 获取用户服装背包
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/getWearsBag")
    public Result<List<UserWearsBagDto>> listWearsBag(@RequestBody GetWearsInformationPo request, HttpServletRequest httpServletRequest) {
        String emailAddress = HeaderUtil.getEmail(httpServletRequest);
        Result result = checkTokenAndEmail(httpServletRequest, emailAddress);
        if (Objects.nonNull(result)) {
            return result;
        }
        List<UserWearsBag> userWearsBags = userWearsBagService.findListByUserIdAndIsNew(request.getUserId(), null);
        //临时限制，升级数据是，要过滤这些数据，升级完成后删除该过滤条件
        //Map<Integer, List<Integer>> includesWearValues = getIncludesWearValues();

        List<UserWearsBagDto> userWearsBagDtos = userWearsBags.stream()
                //保留需要类型对应的值
                // .filter(userWearsBag -> !CollectionUtils.isEmpty(includesWearValues.get(userWearsBag.getWearType())) && includesWearValues.get(userWearsBag.getWearType()).contains(userWearsBag.getWearValue()))
                .map(userWearsBag -> UserWearsBagDto.builder()
                        .id(userWearsBag.getId())
                        .userId(userWearsBag.getUserId())
                        .wearType(userWearsBag.getWearType())
                        .wearValue(userWearsBag.getWearValue())
                        .wearName(userWearsBag.getWearName())
                        .wearImageUrl(userWearsBag.getWearImageUrl())
                        .status(userWearsBag.getStatus())
                        .expiredTime(userWearsBag.getExpiredTime()).build()).collect(Collectors.toList());
        return CommonResult.success(userWearsBagDtos);
    }

    /**
     * 2.6 新上背包，只需要一下属性
     * 构造过滤器，保留客户端需要的值类型
     *
     * @return Map<Integer, List < Integer>>
     */
    private static Map<Integer, List<Integer>> getIncludesWearValues() {
        //过滤 发色
        Map<Integer, List<Integer>> includesWearValues = new HashMap<>();
        //皮肤 2
        includesWearValues.put(UserWearTypeEnum.SKIN_COLOUR.getType(), Arrays.asList(6));
        //头发 1
        includesWearValues.put(UserWearTypeEnum.HAIR_COLOR.getType(), Arrays.asList(6, 7));
        //上衣 5
        includesWearValues.put(UserWearTypeEnum.JACKET.getType(), Arrays.asList(6, 7));
        //裤子 6
        includesWearValues.put(UserWearTypeEnum.TROUSERS.getType(), Arrays.asList(6, 7));
        //鞋子 7
        includesWearValues.put(UserWearTypeEnum.SHOES.getType(), Arrays.asList(6));
        return includesWearValues;
    }

    /**
     * 三方注册&login V2
     *
     * @param req
     * @since 4.0
     */
    @PostMapping("/third/registerAndLoginV2")
    public Result<ThirdLoginVO> thirdRegisterV2(@RequestBody @Validated ThirdUserReqDto req) {
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        ThirdLoginVO loginVO = userRegisterApiManager.thirdRegisterOrLogin(req, appBaseInfo);
        return CommonResult.success(loginVO);
    }

    /**
     * 三方注册没有邮箱的场景 V2
     *
     * @param req
     */
    @PostMapping("/third/emailAddV2")
    public Result<ThirdLoginVO> thirdEmailAddV2(@RequestBody @Validated ThirdUserNoThirdEmailReqDto req) {
        if (!StringUtils.hasText(req.getManualEmail())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.register.email.address"));
        }
        req.setManualEmail(req.getManualEmail().toLowerCase());//保证邮箱小写
        ThirdLoginVO loginVO = userRegisterApiManager.thirdRegisterAddEmail(req, getAppBaseInfo());
        return CommonResult.success(loginVO);
    }

    /**
     * 三方注册&login
     *
     * @param request
     * @param httpServletRequest
     * @see UserAppController#thirdRegisterV2(ThirdUserReqDto)
     */
    @Deprecated(since = "v4.2.0", forRemoval = true)
    @PostMapping("/third/registerAndLogin")
    public Result<ThirdLoginVO> thirdRegister(@RequestBody @Validated ThirdUserReqDto request, HttpServletRequest httpServletRequest) {
        if (!StringUtils.hasText(request.getThirdEmailAddress())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "third email not exist");
        }
        //低版本注册不校验国家
        if (!StringUtils.hasText(request.getCountry())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter country");
        }
        //3.0以上需要选省
        if (!StringUtils.hasText(request.getState())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter state");
        }
        if (!StringUtils.hasText(request.getStateCode())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter stateCode");
        }
        if (!StringUtils.hasText(request.getCountryCode())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter countryCode");
        }

        I18nConstant.CountryCodeEnum countryEnum = null;
        if (StringUtils.hasText(request.getCountryCode())) {
            //用国家code查询国家
            countryEnum = I18nConstant.CountryCodeEnum.findByCode(request.getCountryCode());
        }
        if (countryEnum == null) {
            //在用国家名查询
            countryEnum = I18nConstant.CountryCodeEnum.findByEnName(request.getCountry());
        }
        if (countryEnum == null) {
            //默认美国
            countryEnum = I18nConstant.CountryCodeEnum.US;
        }
        //邮箱不为空，保证邮箱小写
        request.setThirdEmailAddress(request.getThirdEmailAddress().toLowerCase());
        UserRequest userRequest = new UserRequest();
        znsUserService.encapsulateUserRequest(userRequest, httpServletRequest);
        Integer appVersion = Optional.ofNullable(request.getAppVersion()).orElse(getAppVersion());
        userRequest.setCountry(countryEnum.getEnName());
        userRequest.setCountryCode(countryEnum.getCode());
        String stateCode = !StringUtils.hasText(request.getStateCode()) ? countryEnum.getDefaultStateCode() : request.getStateCode();
        AreaEntity areaEntity = areaService.selectByAreaCode(stateCode);
        if (Objects.nonNull(areaEntity)) {
            userRequest.setState(areaEntity.getAreaName());
            userRequest.setStateCode(areaEntity.getAreaCode());
        } else {
            userRequest.setState(countryEnum.getDefaultState());
            userRequest.setStateCode(countryEnum.getDefaultStateCode());
        }
        BeanUtils.copyProperties(userRequest, request);
        String languageCode = getLanguageCode();
        TimeZoneVo timeZoneVo = getUserTimeZone();
        Result<ThirdLoginVO> result = znsUserService.thirdRegister(request, getAppBaseInfo());
        if (result.getData() != null && CommonError.SUCCESS.getCode().equals(result.getCode())) {
            //账户升级, 4.2版本注释，后期看到可以删除
            // userAccountUpgradeBizService.accountUpgrade(result.getData().getUserId(),appVersion);
        }
        return result;
    }

    /**
     * 三方注册没有邮箱的场景
     *
     * @param request
     * @param httpServletRequest
     * @see UserAppController#thirdEmailAddV2(ThirdUserNoThirdEmailReqDto)
     */
    @Deprecated(since = "v4.2.0", forRemoval = true)
    @PostMapping("/third/emailAdd")
    public Result<ThirdLoginVO> thirdEmailAdd(@RequestBody @Validated ThirdUserNoThirdEmailReqDto request, HttpServletRequest httpServletRequest) {
        if (org.springframework.util.StringUtils.hasText(request.getManualEmail())) {
            //邮箱不为空，保证邮箱小写
            request.setManualEmail(request.getManualEmail().toLowerCase());
        }
        UserRequest userRequest = new UserRequest();
        String verificationCode = request.getVerificationCode();
        znsUserService.encapsulateUserRequest(userRequest, httpServletRequest);
        Integer appVersion = Optional.ofNullable(request.getAppVersion()).orElse(getAppVersion());
        if (!StringUtils.hasText(request.getCountry())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter country");
        }
        if (!StringUtils.hasText(request.getStateCode())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter stateCode");
        }
        if (!StringUtils.hasText(request.getCountryCode())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Please enter countryCode");
        }
        //填充国家
        String countryName = I18nConstant.CountryCodeEnum.US.enName;
        String countryCode = I18nConstant.CountryCodeEnum.US.code;
        if (StringUtils.hasText(request.getCountryCode())) {
            ZnsCountryEntity countryEntity = znsCountryService.findByCountryCode(request.getCountryCode());
            if (countryEntity != null) {
                countryName = countryEntity.getName();
                countryCode = countryEntity.getCode();
            }
        }
        userRequest.setCountry(countryName);
        userRequest.setCountryCode(countryCode);

        //填充州
        String stateCode = I18nConstant.CountryCodeEnum.US.defaultStateCode;
        String stateName = I18nConstant.CountryCodeEnum.US.defaultState;
        if (StringUtils.hasText(request.getStateCode())) {
            AreaEntity areaEntity = areaService.selectByAreaCode(request.getStateCode());
            if (areaEntity != null) {
                stateName = areaEntity.getAreaName();
                stateCode = areaEntity.getAreaCode();
            }
        }
        userRequest.setState(stateName);
        userRequest.setStateCode(stateCode);

        BeanUtils.copyProperties(userRequest, request);
        request.setVerificationCode(verificationCode);
        Result<ThirdLoginVO> result = znsUserService.emailAdd(request, getAppBaseInfo());
        if (result.getData() != null && CommonError.SUCCESS.getCode().equals(result.getCode())) {
            //账户升级, 4.2版本注释，后期看到可以删除
            // userAccountUpgradeBizService.accountUpgrade(result.getData().getUserId(),appVersion);
        }
        return result;

    }

    /**
     * 文件上传非登录状态使用
     *
     * @param file
     * @return
     */
    @RequestMapping("/change/HeadPortrait/noLogin")
    public Result<String> changeHeadPortraitRealNoLogin(@RequestParam(value = "file", required = false) MultipartFile file) {
        if (Objects.isNull(file)) {
            return CommonResult.success("");
        }
        Map<String, Object> map = AwsUtil.putS3Object(file, "headPortrait");
        String headPortrait = (String) map.get("url");
        return CommonResult.success(headPortrait);
    }


    /**
     * 三方id check & 登录
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/third/thirdIdCheck")
    public Result<ThirdLoginVO> thirdIdCheck(@RequestBody @Validated ThirdUserCheckReqDto request, HttpServletRequest httpServletRequest) {
        UserRequest userRequest = new UserRequest();
        znsUserService.encapsulateUserRequest(userRequest, httpServletRequest);
        BeanUtils.copyProperties(userRequest, request);
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        Result<ThirdLoginVO> result = znsUserService.thirdIdCheck(request, appBaseInfo);
        if (Objects.nonNull(result.getData()) && Objects.equals(result.getData().getIsNewUser(), 0)) {
            //老账号登录
            ZnsUserEntity tempUser = znsUserService.findTempUserByUuid(appBaseInfo.getUuid()); // 查询临时用户
            if (Objects.nonNull(tempUser)) {
                //临时账号数据迁移到真实账号
                userBizService.removeTempUserToRealUser(tempUser.getId(), result.getData().getUserId(), getAppVersion());
            }
        }
        return result;
    }

    /**
     * 预校验国家
     *
     * @param request
     * @return
     */
    @PostMapping("/preCheckChangeCountryRegion")
    public Result<CheckChangeCountryRegionResp> preCheckChangeCountryRegion(@RequestBody UserCountryRegionRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        Integer appVersion = getAppVersion();
        CheckChangeCountryRegionResp regionResp = userManager.preCheckChangeCountryRegion(loginUser, request, appVersion);
        return CommonResult.success(regionResp);
    }

    /**
     * 更改国家
     *
     * @param request
     * @return
     */
    @PostMapping("/changeCountryRegion")
    public Result changeCountryRegion(@RequestBody UserCountryRegionRequest request) {
        userManager.changeCountryRegion(getLoginUser(), request, getAppVersion());
        return CommonResult.success();
    }

    /**
     * 完成新用户流程
     *
     * @return
     */
    @PostMapping("completeNewUserFlow")
    public Result<Boolean> completeNewUserFlow() {
        ZnsUserEntity user = getLoginUser();
        znsUserService.completeNewUserFlow(user);
        return CommonResult.success(true);
    }

    /**
     * 公英制修改
     *
     * @param req
     * @return
     */
    @PostMapping("metricTypeChange")
    public Result<Boolean> metricTypeChange(@RequestBody UserMetricReq req) {
        ZnsUserEntity user = getLoginUser();
        //版本上线时兼容,todo app版本正式发布后可删除
        if (Objects.isNull(req.getMeasureUnit())) {
            znsUserService.changeMetricType(user, req.getMetricType());
        } else {
            znsUserService.changeMeasureUnit(user, req.getMeasureUnit());
        }
        return CommonResult.success(true);
    }


    /**
     * 查询/注册临时账号
     *
     * @since 4.3
     */
    @PostMapping("/getOrRegisterTempUser")
    public Result<LoginVO> getOrRegisterTempUser() {
        AppBaseInfoVo appBaseInfo = getAppBaseInfo();
        LoginVO loginVO = userRegisterApiManager.getOrRegisterTempUser(appBaseInfo);
        return CommonResult.success(loginVO);
    }


    /**
     * 用户地理位置信息上报存储
     *
     * @param request
     */
    @PostMapping("/geo/upload")
    public Result geoUpload(@RequestBody UserGeoReq request) {
        ZnsUserEntity loginUser = getLoginUser();
        userGeoManager.updateUserGeoInfo(loginUser, request);
        return CommonResult.success();
    }
    /**
     * 投流渠道活动
     *
     * @param request
     */
    @PostMapping("/channel/activity")
    public Result<SurrenderChannelActivityRespDto> channelActivity(@RequestBody SurrenderChannelActivityReqDto request) {
        return CommonResult.success(userManager.channelActivity(getLoginUser()));
    }

    /**
     * 注册验证码校验
     */
    @PostMapping("/verificationCodeCheck")
    public Result<Void> registerVerificationCodeCheck(@RequestBody UserAppRegisterReqDto request) {
        znsUserService.checkVerificationCodeNoDelete(request.getEmailAddress(), request.getVerificationCode(), request.getVerificationCodeSendCount(), ApiConstants.REGISTER_CODE_SEND_MAIL_TOKEN_KEY);
        return CommonResult.success();
    }
}
