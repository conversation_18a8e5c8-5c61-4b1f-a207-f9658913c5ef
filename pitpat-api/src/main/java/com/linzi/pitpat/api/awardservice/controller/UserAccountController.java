package com.linzi.pitpat.api.awardservice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.awardservice.converter.UserAccountConverter;
import com.linzi.pitpat.api.awardservice.dto.response.AwardDto;
import com.linzi.pitpat.api.awardservice.dto.response.UrgeAwardDto;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.DateTimeUtil;
import com.linzi.pitpat.data.awardservice.constant.enums.AwardSentTypeEnum;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountEntity;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserPaypalAccountEntity;
import com.linzi.pitpat.data.awardservice.model.query.AddColorEggAward;
import com.linzi.pitpat.data.awardservice.model.vo.ColorEggConfigVo;
import com.linzi.pitpat.data.awardservice.service.ColorEggConfigService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserPaypalAccountService;
import com.linzi.pitpat.data.config.PitpatConfig;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.dto.MailDto;
import com.linzi.pitpat.data.enums.AccountDetailSubtypeEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeCustomEnum;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import com.linzi.pitpat.data.enums.EmailSendingRecordTypeEnum;
import com.linzi.pitpat.data.enums.RunActivityTypeEnum;
import com.linzi.pitpat.data.request.IdRequest;
import com.linzi.pitpat.data.request.PasswordRequest;
import com.linzi.pitpat.data.request.TestDto;
import com.linzi.pitpat.data.request.account.DetailRequest;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.AppUpgradeService;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.UserIdentityService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import com.linzi.pitpat.user.api.dto.UserAccountDto;
import com.linzi.pitpat.user.api.dto.request.AddAccountDetailRequestDto;
import com.linzi.pitpat.user.api.dto.request.UserIdRequestDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户账户接口
 *
 * @description: 用户账户控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@RequestMapping({"/app/account", "/h5/account"})
@Slf4j
@RequiredArgsConstructor
public class UserAccountController extends BaseAppController {
    @Resource
    private ZnsUserAccountService userAccountService;
    @Resource
    private UserAccountConverter userAccountConverter;
    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;
    @Resource
    private ZnsUserPaypalAccountService userPaypalAccountService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ZnsUserEmailSendingRecordService znsUserEmailSendingRecordService;
    @Resource
    private PitpatConfig config;
    @Value("${spring.profiles.active}")
    private String profile;
    @Autowired
    private ColorEggConfigService colorEggConfigService;
    private final RedissonClient redissonClient;


    @Resource
    private AppUpgradeService appUpgradeService;
    @Value("${zns.config.rabbitQueue.emailSend}")
    private String mailSend;
    @Resource
    private RabbitTemplate rabbitTemplate;

    private final UserIdentityService userIdentityService;

    /**
     * 账户余额
     *
     * @return
     */
    @PostMapping("/info")
    public Result info() {
        Map<String, Object> map = new HashMap<>();
        ZnsUserEntity loginUser = getLoginUserNotNull();
        ZnsUserAccountEntity account = userAccountService.getByUserId(loginUser.getId());
        map.put("totalIncomeAmount", account.getTotalIncomeAmount());
        map.put("totalExpenditureAmount", account.getTotalExpenditureAmount());
        map.put("enabled", account.getEnabled());
        map.put("remarks", account.getRemarks());
        map.put("currency", getUserCurrency());

        ZnsUserPaypalAccountEntity paypalAccount = userPaypalAccountService.getPaypalAccount(loginUser.getId());
        if (Objects.nonNull(paypalAccount)) {
            map.put("paypalAccount", paypalAccount.getPaypalAccount());
            map.put("paypalUserName", paypalAccount.getPaypalUserName());

            map.put("ppzfAccount", paypalAccount.getPaypalAccount());
            map.put("ppzfUserName", paypalAccount.getPaypalUserName());
        }

        //获取当前支出中金额
        List<Integer> paying = Arrays.asList(0, 1);
        BigDecimal allAmountByStatus = userAccountDetailService.getAllAmountByStatus(loginUser.getId(), paying, 2, null, account.getId());
        account.setAmount(account.getAmount().compareTo(allAmountByStatus) > 0 ? account.getAmount().subtract(allAmountByStatus) : BigDecimal.ZERO);
        map.put("amount", account.getAmount()); //账户资金页接口不做小数处理
        //查询单次最大限额
        String cashLimit = sysConfigService.selectConfigByKey("amount.cash.limit");
        if (StringUtils.hasText(cashLimit)) {
            Map<String, Object> object = JsonUtil.readValue(cashLimit);
            map.put("cashMaxLimit", object.get("max"));
            map.put("cashMinLimit", object.get("min"));
        }
        //查询单次充值最大限额,泰铢单独处理
        if (I18nConstant.CurrencyCodeEnum.THB.getCode().equals(account.getCurrencyCode())) {
            String cashLimitThb = sysConfigService.selectConfigByKey("amount.cash.limit.thb");
            if (StringUtils.hasText(cashLimitThb)) {
                Map<String, Object> object = JsonUtil.readValue(cashLimitThb);
                map.put("cashMaxLimit", object.get("max"));
                map.put("cashMinLimit", object.get("min"));
            }
        }

        String rechargeLimit = sysConfigService.selectConfigByKey("amount.payment.max.single");
        if (StringUtils.hasText(rechargeLimit)) {
            map.put("rechargeLimit", new BigDecimal(rechargeLimit));
        }
        //查询首次支付时间
        map.put("firstPaymentTime", userAccountDetailService.getFirstTime(loginUser.getId()));

        boolean checkVersion = appUpgradeService.isCheckVersion(getAppType(), getAppVersion());
        boolean isNpc = userIdentityService.isNpc(loginUser.getId());
        map.put("npcRole", isNpc);
        if (checkVersion) {
            map.put("payAmountSwitch", 0);
            map.put("cashAmountSwitch", 0);
            //是否展示交易明细，1:展示交易明细，0：不展示交易明细
            map.put("transactionRecord", 0);
        } else {
            //充值提现开关
            String payAmountSwitch = sysConfigService.selectConfigByKey("pay.amount.switch");
            map.put("payAmountSwitch", StringUtils.hasText(payAmountSwitch) ? Integer.parseInt(payAmountSwitch) : 1);
            String cashAmountSwitch = sysConfigService.selectConfigByKey("cash.amount.switch");
            map.put("cashAmountSwitch", StringUtils.hasText(cashAmountSwitch) ? Integer.parseInt(cashAmountSwitch) : 1);
            String transactionRecord = sysConfigService.selectConfigByKey(ConfigKeyEnums.TRANSACTION_RECORD_SWITCH.getCode());
            map.put("transactionRecord", StringUtils.hasText(transactionRecord) ? Integer.parseInt(transactionRecord) : 1);
        }
        //NPC禁止提现
        if (isNpc) {
            map.put("cashAmountSwitch", 0);
        }
        if (isNpc) {
            map.put("cashAmountSwitch", 0);
        }
        //税率
        String config = sysConfigService.selectConfigByKey("paypal.cash.config");
        Map<String, Object> object = JsonUtil.readValue(config);
        BigDecimal serviceRate = MapUtil.getBigDecimal(object.get("serviceRate"));
        BigDecimal taxRate = MapUtil.getBigDecimal(object.get("taxRate"));
        map.put("serviceRate", serviceRate.toString());
        map.put("taxRate", taxRate.toString());

        return CommonResult.success(map);
    }

    /**
     * 余额明细
     *
     * @param request
     * @return
     */
    @PostMapping("/detail")
    public Result detail(@RequestBody DetailRequest request) throws ParseException {
        Map<String, Object> data = new HashMap<>();
        ZnsUserEntity loginUser = getLoginUser();
        String zoneId = getZoneId();

        ZonedDateTime startTime = userAccountDetailService.getStartTime(loginUser.getId(), request.getType(), request.getYear(), request.getMonth());
        ZnsUserAccountEntity accountEntity = userAccountService.getByUserId(loginUser.getId());

        Page page = userAccountDetailService.getAccountDetail(request.getPageNum(), request.getPageSize(), loginUser.getId(), request.getType(), startTime, accountEntity.getId());
        List<ZnsUserAccountDetailEntity> accountDetail = page.getRecords();
        if (CollectionUtils.isEmpty(accountDetail)) {
            return CommonResult.success(data);
        }
        //排序
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月", Locale.ENGLISH);
        //按月分组
        Map<String, List<ZnsUserAccountDetailEntity>> listMap = accountDetail.stream().collect(Collectors.groupingBy(d -> simpleDateFormat.format(d.getCreateTime())));
        List<Map<String, Object>> list = new ArrayList<>();
        List<Map.Entry<String, List<ZnsUserAccountDetailEntity>>> entryList = listMap.entrySet().stream().sorted((x, y) -> {
            try {
                ZonedDateTime xDate = simpleDateFormat.parse(x.getKey().toString());
                ZonedDateTime yDate = simpleDateFormat.parse(y.getKey().toString());
                return yDate.compareTo(xDate);
            } catch (ParseException e) {
                e.printStackTrace();
                return 0;
            }
        }).collect(Collectors.toList());

        Currency currency = getUserCurrency();
        for (Map.Entry<String, List<ZnsUserAccountDetailEntity>> stringListEntry : entryList) {
            Map<String, Object> map = new HashMap<>();
            map.put("dataType", 1);
            map.put("years", stringListEntry.getKey());
            map.put("currency", currency);
            ZonedDateTime date = DateTimeUtil.parseYearMonthWithPattern(stringListEntry.getKey(), "yyyy年MM月");
            //TODO i18n 时区 临时方案
            ZonedDateTime date2ByTimeZone = DateUtil.getTimeZoneDate(DateUtil.addDays1(date, 2), TimeZone.getTimeZone(zoneId));
            map.put("createTime", date2ByTimeZone);
            List<ZnsUserAccountDetailEntity> detailList = stringListEntry.getValue().stream().sorted(Comparator.comparing(ZnsUserAccountDetailEntity::getCreateTime).reversed()).collect(Collectors.toList());

            List<Map<String, Object>> detailListMap = new ArrayList<>();
            for (ZnsUserAccountDetailEntity znsUserAccountDetailEntity : detailList) {
                Map<String, Object> detailMap = getDetailMap(znsUserAccountDetailEntity);
                detailMap.put("currency", currency);
                detailListMap.add(detailMap);
            }
            Map<String, Object> monthData = userAccountDetailService.getMonthData(loginUser.getId(), stringListEntry.getKey(), accountEntity.getId());
            if (Objects.nonNull(monthData)) {
                map.putAll(monthData);
            }
            list.add(map);
            list.addAll(detailListMap);
        }
        data.put("list", list);
        data.put("total", page.getTotal());
        data.put("current", page.getCurrent());
        data.put("size", page.getSize());
        return CommonResult.success(data);
    }

    private Map<String, Object> getDetailMap(ZnsUserAccountDetailEntity znsUserAccountDetailEntity) {
        Map<String, Object> map = new HashMap<>();
        map.put("dataType", 0);
        map.put("id", znsUserAccountDetailEntity.getId());
        map.put("createTime", znsUserAccountDetailEntity.getCreateTime());
        if (AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType().equals(znsUserAccountDetailEntity.getTradeType())) {
            AccountDetailSubtypeEnum accountDetailSubtype = AccountDetailSubtypeEnum.getAccountDetailSubtype(AccountDetailTypeEnum.findByType(znsUserAccountDetailEntity.getTradeType()), znsUserAccountDetailEntity.getTradeSubtype());
            if (Objects.nonNull(accountDetailSubtype)) {
                //翻译remark
                map.put("title", getI18nTypeName(accountDetailSubtype.getName()));
                map.put("remark", getI18nTypeName(accountDetailSubtype.getName()));
            } else {
                map.put("title", znsUserAccountDetailEntity.getTitle());
                map.put("remark", getI18nTypeName(znsUserAccountDetailEntity.getRemark()));
            }
        } else if (AccountDetailTypeEnum.NEW_ACTIVITY_100.getType().equals(znsUserAccountDetailEntity.getTradeType())) {
            map.put("title", I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailTypeEnum." + (znsUserAccountDetailEntity.getTradeType() + znsUserAccountDetailEntity.getTradeSubtype())));
            map.put("remark", I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailTypeEnum." + (znsUserAccountDetailEntity.getTradeType() + znsUserAccountDetailEntity.getTradeSubtype())));
        } else if (AccountDetailTypeEnum.TURBOLINK_AWARD.getType().equals(znsUserAccountDetailEntity.getTradeType())) {
            map.put("title", getI18nTypeName(znsUserAccountDetailEntity.getTitle()));
            map.put("remark", getI18nTypeName(znsUserAccountDetailEntity.getRemark()));
        } else if (AccountDetailTypeEnum.OPERATIONAL_ACTIVITY_AWARD.getType().equals(znsUserAccountDetailEntity.getTradeType()) ||
                (AccountDetailTypeEnum.INVITE_NEWUSER_JOIN_TEAM.getType().equals(znsUserAccountDetailEntity.getTradeType()))) {
            map.put("title", I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailTypeEnum." + AccountDetailTypeEnum.findByType(znsUserAccountDetailEntity.getTradeType())));
            map.put("remark", I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailTypeEnum." + AccountDetailTypeEnum.findByType(znsUserAccountDetailEntity.getTradeType())));
        } else {
            map.put("title", znsUserAccountDetailEntity.getTitle());
            map.put("remark", getI18nTypeName(znsUserAccountDetailEntity.getRemark()));
        }
        //新pk活动处理
        if (RunActivityTypeEnum.NEW_PK_ACTIVITY.getType().equals(znsUserAccountDetailEntity.getActivityType())) {
            map.put("title", znsUserAccountDetailEntity.getTitle());
            map.put("remark", getI18nTypeName(znsUserAccountDetailEntity.getRemark()));
        }
        map.put("billNo", znsUserAccountDetailEntity.getBillNo());
        map.put("amount", znsUserAccountDetailEntity.getAmount());
        map.put("serviceFee", znsUserAccountDetailEntity.getServiceFee());
        map.put("taxesFee", znsUserAccountDetailEntity.getTaxesFee());
        map.put("type", znsUserAccountDetailEntity.getType());
        map.put("tradeStatus", znsUserAccountDetailEntity.getTradeStatus());
        map.put("refundStatus", znsUserAccountDetailEntity.getRefundStatus());

        return map;
    }

    public String getI18nTypeName(String remark) {
        AccountDetailTypeEnum accountDetailType = AccountDetailTypeEnum.resolve(remark);
        if (Objects.nonNull(accountDetailType)) {
            return I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailTypeEnum." + accountDetailType);
        }

        AccountDetailSubtypeEnum accountDetailSubtype = AccountDetailSubtypeEnum.resolve(remark);
        if (Objects.nonNull(accountDetailSubtype)) {
            return I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailSubtypeEnum." + accountDetailSubtype);
        }

        AccountDetailTypeCustomEnum accountDetailTypeCustom = AccountDetailTypeCustomEnum.resolve(remark);
        if (Objects.nonNull(accountDetailTypeCustom)) {
            return I18nMsgUtils.getMessage("accountDetail.remark.AccountDetailTypeCustomEnum." + accountDetailTypeCustom);
        }

        if (Objects.isNull(accountDetailType) && Objects.isNull(accountDetailSubtype) && Objects.isNull(accountDetailTypeCustom)) {
            log.warn("accountDetail.remark 语言国际化未找到词={}", remark);
        }
        return remark;
    }

    /**
     * 明细详情
     *
     * @param request
     * @return
     */
    @PostMapping("/detail/info")
    public Result detailInfo(@RequestBody IdRequest request) {
        ZnsUserAccountDetailEntity detailEntity = userAccountDetailService.selectById(request.getId());
        if (detailEntity.getTradeType().equals(AccountDetailTypeEnum.RECHARGE.getType())) {
            detailEntity.setPayType(1);
        } else {
            detailEntity.setPayType(0);
        }
        detailEntity.setCurrency(getUserCurrency());
        detailEntity.setPpzfAccount(detailEntity.getPaypalAccount());

        if (AccountDetailTypeEnum.OFFICIAL_EVENT_AWARD.getType().equals(detailEntity.getTradeType())) {
            AccountDetailSubtypeEnum accountDetailSubtype = AccountDetailSubtypeEnum.getAccountDetailSubtype(AccountDetailTypeEnum.findByType(detailEntity.getTradeType()), detailEntity.getTradeSubtype());
            if (Objects.nonNull(accountDetailSubtype)) {
                detailEntity.setTitle(accountDetailSubtype.getName());
                detailEntity.setRemark(accountDetailSubtype.getName());
            }
        } else if (AccountDetailTypeEnum.NEW_ACTIVITY_100.getType().equals(detailEntity.getTradeType())) {
            AwardSentTypeEnum sentTypeEnum = AwardSentTypeEnum.findByType(detailEntity.getTradeSubtype());
            //翻译remark
            detailEntity.setRemark(getI18nSendType(sentTypeEnum));
            return CommonResult.success(detailEntity);
        }
        //翻译remark
        detailEntity.setRemark(getI18nTypeName(detailEntity.getRemark()));

        return CommonResult.success(detailEntity);
    }

    private String getI18nSendType(AwardSentTypeEnum sentTypeEnum) {
        return I18nMsgUtils.getMessage("accountDetail.remark.AwardSentTypeEnum." + sentTypeEnum);
    }

    /**
     * 绑定paypal支付账户
     *
     * @param body
     * @return
     */
    @PostMapping("/bindingPayAccount")
    public Result bindingPayAccount(@RequestBody ZnsUserPaypalAccountEntity body) {
        if (!StringUtils.hasText(body.getPaypalAccount())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "paypal账户不能为空");
        }
        Long userId = getLoginUser().getId();
//        if (!StringUtils.hasText(body.getPhone())) {
//            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "手机号码不能为空");
//        }
        //检查是否真实paypal用户，目前没找到文档

        //检查手机号区域，美国
//        if (!PhoneUtils.isEnUs(body.getPhone())){
//            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "手机号不正确");
//        }

        //检查手机号和paypal账户绑定，目前没找到文档
        Long id = userPaypalAccountService.addPayAccount(userId, body);
        //删除之前绑定的账户
        userPaypalAccountService.deleteNeId(id, userId);
        return CommonResult.success();
    }

    /**
     * 设置密码
     *
     * @param request
     * @return
     */
    @PostMapping("/addPassword")
    public Result addPassword(@RequestBody PasswordRequest request) {
        if (!StringUtils.hasText(request.getNewPassword())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "密码不能为空");
        }
        ZnsUserEntity user = getLoginUser();
        //查询账户
        ZnsUserAccountEntity account = userAccountService.getByUserId(user.getId());

        if (StringUtils.hasText(account.getPassword())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), "Password already exists");
        }
        userAccountService.setUpPassword(user.getId(), request.getNewPassword());
        return CommonResult.success();
    }

    /**
     * 校验支付密码
     *
     * @param request
     * @return
     */
    @PostMapping("/checkPassword")
    public Result checkPassword(@RequestBody PasswordRequest request) {
        ZnsUserEntity loginUser = getLoginUser();
        //查询账户
        Result result = userAccountService.checkPassword(loginUser.getId(), request.getPassword(), false);

        if (Objects.nonNull(result)) {
            return result;
        }

        return CommonResult.success();
    }

    /**
     * 修改密码
     *
     * @param request
     * @return
     */
    @PostMapping("/renewPassword")
    public Result renewPassword(@RequestBody PasswordRequest request) {
        if (request.getOldPassword().equals(request.getNewPassword())) {
            return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), "新密码与当前密码相同，请重新设置");
        }
        ZnsUserEntity loginUser = getLoginUser();

        //查询账户
        Result result = userAccountService.checkPassword(loginUser.getId(), request.getOldPassword(), false);

        if (Objects.nonNull(result)) {
            return result;
        }

        userAccountService.setUpPassword(loginUser.getId(), request.getNewPassword());
        return CommonResult.success();
    }

    /**
     * 重置密码
     *
     * @param request
     * @return
     */
    @PostMapping("/resetPassword")
    public Result resetPassword(@RequestBody PasswordRequest request) {
        if (!StringUtils.hasText(request.getNewPassword())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.enter.password"));
        }
        ZnsUserEntity user = getLoginUser();

        //修改密码
        userAccountService.setUpPassword(user.getId(), request.getNewPassword());
        redisUtil.delete(ApiConstants.PAY_SEND_MAIL_TOKEN_KEY + user.getEmailAddressEn().toLowerCase());
        redisUtil.delete(RedisConstants.USER_PAY_ERROR_PASSWORD_COUNT + user.getId());
        return CommonResult.success();
    }

    /**
     * 重置密码-发送邮件
     *
     * @param request
     * @return
     */
    @PostMapping("/sendVerificationCode")
    public Result sendVerificationCode(@RequestBody PasswordRequest request, HttpServletRequest httpServletRequest) {
        ZnsUserEntity user = getLoginUser();

        String message = I18nMsgUtils.getMessage("user.sending.has.sent");
        Result result = CommonResult.success(message);
        RLock lock = redissonClient.getLock(ApiConstants.PAY_SEND_MAIL_LOCK + user.getEmailAddressEn());
        try {
            if (!lock.tryLock(1, 2, TimeUnit.SECONDS)) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.mailbox.incorrect"));
            }
            //校验发送次数
            String countKey = ApiConstants.PAY_SEND_MAIL_COUNT_KEY + ZonedDateTimeUtil.format(ZonedDateTime.now(), ZonedDateTimeUtil.PATTERN_DATE) + user.getEmailAddressEn();
            RedisAtomicInteger rai = new RedisAtomicInteger(countKey, redisTemplate.getConnectionFactory());
            //设置1天的有效期，过期释放key
            redisTemplate.expire(countKey, 1L, TimeUnit.DAYS);
            int count = rai.incrementAndGet();
            if (count > 5) {
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.sending.times.exceeded"));
            }
            if (user != null) {
                //生成验证码
                String code = EnvUtils.isOnline(profile) ? NanoId.randomNanoDigitId(6) : "888888";

                // 发送邮件逻辑
                String title = I18nMsgUtils.getMessage("user.sending.reset.title");
                String content = "<img style=\"margin-top: 20px;\" src=\"" + config.getMailIcon() + "\">\n" +
                        "      <h2 style=\"font-size: 30px; color: #575767; line-height: 34px; text-align: justify; margin: 80px 0 40px;\">Hi," + user.getFirstName() + "!</h2>\n" +
                        "      <p style=\"width: 880px; font-size: 24px; color: #575767; line-height: 34px; text-align: justify;\"> We heard that you forgot your password. If you didn't mean to reset it, simply ignore this email—your password is still the same.</p>\n" +
                        "      <a style=\"width: 386px; height: 80px;display: block;cursor: pointer;text-decoration: none; line-height: 80px; text-align: center; background: #eb2246; color: #ffffff; font-size: 26px; margin: 60px 0 60px; border-radius: 5px;\" target=\"_blank\">" + code + "</a>\n" +
                        "      <p style=\"font-size: 24px; text-align: justify; line-height: 28px; color: #575767;\">Keep moving.</p>\n" +
                        "      <h3 style=\"font-size: 24px; text-align: justify; line-height: 33px; font-weight: 600; color: #575767; margin-top: 10px;\">PitPat Team</h3>";
                //记录发送邮件
                ZnsUserEmailSendingRecordEntity emailSend = new ZnsUserEmailSendingRecordEntity();
                emailSend.setIsDelete(0);
                emailSend.setEmailAddressEn(user.getEmailAddressEn());
                String uuid = httpServletRequest.getHeader("uuid");
                if (StringUtils.hasText(uuid)) {
                    emailSend.setUniqueCode(uuid);
                }
                emailSend.setContent(I18nMsgUtils.getMessage("user.sending.reset.pay.password"));
                emailSend.setType(EmailSendingRecordTypeEnum.RESET_PAY_PASSWORD.getCode());
                emailSend.setMailTitle(title);
                emailSend.setMailContent(content);
                znsUserEmailSendingRecordService.insert(emailSend);

//                mailUtils.sendMail(user.getEmailAddress(), content, title, emailSend);
                MailDto mailDto = new MailDto(user.getEmailAddressEn(), content, title, emailSend);
                rabbitTemplate.convertAndSend(mailSend, JsonUtil.writeString(mailDto));

                String tokenKey = ApiConstants.PAY_SEND_MAIL_TOKEN_KEY + user.getEmailAddressEn();
                redisUtil.set(tokenKey, code, 60 * 5L, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("send mail error, {}", e.getMessage(), e);
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), I18nMsgUtils.getMessage("user.sending.error"));
        }
        return result;
    }


    /**
     * 校验验证码
     *
     * @param request
     * @return
     */
    @PostMapping("/checkVerificationCode")
    public Result checkVerificationCode(@RequestBody PasswordRequest request) {
        ZnsUserEntity user = getLoginUser();

        if (!StringUtils.hasText(request.getCode())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.enter.code"));
        }

        Result result = checkVerificationCode(user.getEmailAddressEn(), request.getCode());
        if (Objects.nonNull(result)) {
            return result;
        }

        return CommonResult.success();
    }

    /**
     * 获取彩蛋
     *
     * @return
     */
    @PostMapping("/colorEggConfig")
    public Result colorEggConfig() {
        ZnsUserEntity loginUser = getLoginUser();
        List<ColorEggConfigVo> colorEggConfigVos = colorEggConfigService.selectColorEggConfigByStatusGmtStartGmtEnd(1, ZonedDateTime.now(), ZonedDateTime.now());
        for (ColorEggConfigVo colorEggConfig : colorEggConfigVos) {
            BigDecimal maxValue = MapUtil.getBigDecimal(colorEggConfig.getMaxValue(), BigDecimal.ZERO);
            BigDecimal sum = userAccountDetailService.selectAccountDetailByTradeTypeRef(loginUser.getId(), AccountDetailTypeEnum.RUN_COLOR_EGG_AWARD.getType(), colorEggConfig.getId(), ZonedDateTime.now(), colorEggConfig.getAwardRule());
            colorEggConfig.setRemain(BigDecimalUtil.subtract(maxValue, sum));
        }
        Map<String, Object> map = new HashMap<>();
        map.put("list", colorEggConfigVos);
        return CommonResult.success(map);
    }


    @PostMapping("/remainAward")
    public Result remainAward(@RequestBody AddColorEggAward request) {
        if ("egg".equals(request.getRemainType())) {
            AwardDto awardDto = new AwardDto(0, BigDecimal.ZERO, 0, BigDecimal.ZERO);
            return CommonResult.success(awardDto);
//            BigDecimal sum = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRule(request.getUserId(),
//                    AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getCode(),ZonedDateTime.now(), "%Y-%m-%d");
//            Integer count = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRuleCount(request.getUserId(),
//                    AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getCode(), ZonedDateTime.now(),"%Y-%m-%d");
//
//            SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.egg_color_limit.getCode());
//            String limits[] = sysConfig.getConfigValue().split(",");
//
//            int countLimit = MapUtil.getInteger(limits[0], 99);
//            BigDecimal amountLimit = MapUtil.getBigDecimal(limits[1], new BigDecimal(99));
//            AwardDto awardDto = new AwardDto(count , sum, countLimit, amountLimit);
//            return CommonResult.success(awardDto);
        } else if ("urge".equals(request.getRemainType())) {


//            SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.Applause_CheerUp_Highfive_amount.getCode());
//            String values[] = sysConfig.getConfigValue().split(",");
//            Integer applauseCountLimit = NumberUtils.objToInteger(values[0]);
//            Integer cheerUpCountLimit = NumberUtils.objToInteger(values[1]);
//            Integer highFiveCountLimit = NumberUtils.objToInteger(values[2]);
//            BigDecimal awardAmountLimit = MapUtil.getBigDecimal(values[3], BigDecimal.ZERO);
//
//            BigDecimal sum = znsUserAccountDetailDao.selectAccountByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), null);
//
//            Integer applauseCount = znsUserAccountDetailDao.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), Arrays.asList(6,9));  // 发起鼓掌
//            Integer applauseExpCount = expUserDao.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    Arrays.asList(6,9));
//
//            Integer cheerUpCount = znsUserAccountDetailDao.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), Arrays.asList(7,10));  //
//            Integer cheerUpExpCount = expUserDao.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    Arrays.asList(7,10));
//
//            Integer highFiveCount = znsUserAccountDetailDao.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), Arrays.asList(8,11));  //
//            Integer highFiveExpCount = expUserDao.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    Arrays.asList(8,11));
//
//            UrgeAwardDto awardDto = new UrgeAwardDto(applauseCount + applauseExpCount, cheerUpCount + cheerUpExpCount, highFiveCount + highFiveExpCount,
//                    sum, applauseCountLimit, cheerUpCountLimit, highFiveCountLimit, awardAmountLimit);
            UrgeAwardDto awardDto = new UrgeAwardDto(0, 0, 0,
                    BigDecimal.ZERO, 0, 0, 0, BigDecimal.ZERO);
            return CommonResult.success(awardDto);
        }
        return null;
    }

    /**
     * 下发激励(已关闭)
     *
     * @param request
     * @return
     */
    @PostMapping("/addAward")
    public Result addCoid(@RequestBody AddColorEggAward request) {
        return CommonResult.fail("out of count limit");
//        if(request.getUrgeActivityConfigId() !=null){
//            // 6. 发起鼓掌 ,7 发起加油， 8 发起击掌 ， 9 收到鼓掌，10，收到加油，11 收到击掌
//            SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.Applause_CheerUp_Highfive_amount.getCode());
//            String values[] = sysConfig.getConfigValue().split(",");
//            Integer applauseCountLimit = NumberUtils.objToInteger(values[0]);
//            Integer cheerUpCountLimit = NumberUtils.objToInteger(values[1]);
//            Integer highFiveCountLimit = NumberUtils.objToInteger(values[2]);
//            BigDecimal awardAmountLimit = MapUtil.getBigDecimal(values[3], BigDecimal.ZERO);
//            BigDecimal sum = znsUserAccountDetailDao.selectAccountByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), null);
//
//            Integer applauseCount = znsUserAccountDetailDao.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), Arrays.asList(6,9));  // 发起鼓掌
//            Integer applauseExpCount = expUserDao.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    Arrays.asList(6,9));
//
//            Integer cheerUpCount = znsUserAccountDetailDao.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), Arrays.asList(7,10));  //
//            Integer cheerUpExpCount = expUserDao.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    Arrays.asList(7, 10));
//
//            Integer highFiveCount = znsUserAccountDetailDao.countByUserIdTradeTypeSubType(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    AccountDetailTypeEnum.INSPIRED_BONUS.getCode(), Arrays.asList(8, 11));  //
//            Integer highFiveExpCount = expUserDao.countExpUserByUserIdStartTimeEndTimeTypes(request.getUserId(), DateUtil.getStartOfDate(ZonedDateTime.now()), ZonedDateTime.now(),
//                    Arrays.asList(8, 11));
//
//            Integer applauseCountAll = applauseCount + applauseExpCount;
//            Integer cheerUpCountAll = cheerUpCount + cheerUpExpCount;
//            Integer highFiveCountAll = highFiveCount + highFiveExpCount;
//            if (Objects.equals(request.getUrgeType(), 6, 9)) {
//                applauseCountAll = applauseCountAll + 1;
//                if (applauseCountAll > applauseCountLimit) {
//                    log.info("超过次数限制 count =" + applauseCountAll + ",countLimit=" + applauseCountLimit);
//                    return CommonResult.fail("out of count limit");
//                }
//            } else if (Objects.equals(request.getUrgeType(), 7, 10)) {
//                cheerUpCountAll = cheerUpCountAll + 1;
//                if (cheerUpCountAll > cheerUpCountLimit) {
//                    log.info("超过次数限制 count =" + cheerUpCountAll + ",countLimit=" + cheerUpCountLimit);
//                    return CommonResult.fail("out of count limit");
//                }
//            } else if (Objects.equals(request.getUrgeType(), 8,11)) {
//                highFiveCountAll =highFiveCountAll + 1;
//                if (highFiveCountAll > highFiveCountLimit) {
//                    log.info("超过次数限制 count =" + highFiveCountAll + ",countLimit=" + highFiveCountLimit);
//                    return CommonResult.fail("out of count limit");
//                }
//            }
//            UrgeActivityConfig urgeActivityConfig =  urgeActivityConfigDao.selectUrgeActivityConfigById(request.getUrgeActivityConfigId());
//            if (Objects.equals(request.getCode(), 1)) {  //类型 1 表示金额 ， 2 表示经验值
//
//                sum = sum.add(request.getAwardValue());
//                if (sum.compareTo(awardAmountLimit) > 0) {
//                    log.info("超过金额限制 sum=" + sum);
//                    return CommonResult.fail("out of amount limit ");
//                }
//                log.info("本次获取的奖励金额是 awardAmount=" + request.getAwardValue());
//                znsRunActivityService.handleTeamRunAward3D(request.getAwardValue(), request.getUserId(),MapUtil.getLong(urgeActivityConfig.getActivityType() ,-2),
//                        AccountDetailTypeEnum.INSPIRED_BONUS, request.getUrgeType(), request.getActivityId());
//            } else {
//                ZnsRunActivityEntity znsRunActivityEntity = znsRunActivityDao.selectActivityById(request.getActivityId());
//                ExpUser expUser = new ExpUser();
//                expUser.setType(request.getUrgeType());
//                expUser.setExp(request.getAwardValue().intValue());
//                expUser.setUserId(request.getUserId());
//                expUser.setActivityId(request.getActivityId());
//                expUser.setActivityType(urgeActivityConfig.getActivityType());
//                expUser.setIsPop(0);
//                expUserDao.insertExpUser(expUser);
//            }
//            UrgeAwardDto awardDto = new UrgeAwardDto(applauseCountAll, cheerUpCountAll, highFiveCountAll, sum, applauseCountLimit, cheerUpCountLimit, highFiveCountLimit, awardAmountLimit);
//            return CommonResult.success(awardDto);
//        } else if (request.getColorEggConfigId() != null) {
//            EggActivityConfig eggActivityConfig = eggActivityConfigDao.selectEggActivityConfigByIdGmtStartTimeGmtEndTime(request.getEggActivityConfigId(), ZonedDateTime.now(), ZonedDateTime.now(), 0);
//            if (eggActivityConfig == null) {
//                log.info("活动已经失效 ");
//                return CommonResult.fail("activity is end !");
//            }
//            ColorEggConfig colorEggConfig = colorEggConfigService.selectColorEggConfigById(request.getColorEggConfigId());
//
//            BigDecimal sum = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRule(request.getUserId(),
//                    AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getCode(), ZonedDateTime.now(), colorEggConfig.getAwardRule());
//
//            Integer count = userAccountDetailService.selectAccountDetailByUserIdTypeRefIdsDateRuleCount(request.getUserId(),
//                    AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD.getCode(), ZonedDateTime.now(), colorEggConfig.getAwardRule());
//
//            SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.egg_color_limit.getCode());
//
//            String limits[] = sysConfig.getConfigValue().split(",");
//            int countLimit = MapUtil.getInteger(limits[0], 99);
//
//            BigDecimal amountLimit = MapUtil.getBigDecimal(limits[1], new BigDecimal(99));
//            BigDecimal awardAmount = MapUtil.getBigDecimal(colorEggConfig.getAwardValue(), BigDecimal.ZERO);
//
//            sum = sum.add(awardAmount);
//            count = count + 1;
//
//            ZnsUserEntity znsUserEntity = znsUserService.selectUserById(request.getUserId());
//            log.info("次数金额较验的4个参数 sum=" + sum + ",amountLimit=" + amountLimit +"count =" + count + ",countLimit=" + countLimit);
//            if(Objects.equals(znsUserEntity.getIsRobot(),1)){
//                log.info("机器人不做金额和数量的限制");
//            }else{
//                if (sum.compareTo(amountLimit) > 0) {
//                    log.info("超过金额限制 sum=" + sum + ",amountLimit=" + amountLimit);
//                    return CommonResult.fail("out of amount limit ");
//                }
//
//                if (count > countLimit) {
//                    log.info("超过次数限制 count =" + count + ",countLimit=" + countLimit);
//                    return CommonResult.fail("out of count limit");
//                }
//            }
//
//            log.info("本次获取的奖励金额是 awardAmount=" + awardAmount);
//            znsRunActivityService.handleTeamRunAward3D(awardAmount, request.getUserId(), eggActivityConfig.getId(),
//                    AccountDetailTypeEnum.RUN_COLOR_EGG_3D_AWARD, eggActivityConfig.getActivityType(), request.getActivityId());
//
//            AwardDto awardDto = new AwardDto(count, sum, countLimit, amountLimit);
//
//            return CommonResult.success(awardDto);
//        } else {
//            ColorEggConfig colorEggConfig = colorEggConfigService.selectColorEggConfigById(request.getId());
//            ZnsUserEntity loginUser = getLoginUser();
//            BigDecimal awardValue = MapUtil.getBigDecimal(colorEggConfig.getAwardValue(), BigDecimal.ZERO);
//            BigDecimal maxValue = MapUtil.getBigDecimal(colorEggConfig.getMaxValue(), BigDecimal.ZERO);
//
//            BigDecimal sum = userAccountDetailService.selectAccountDetailByTradeTypeRef(loginUser.getId(), AccountDetailTypeEnum.RUN_COLOR_EGG_AWARD.getCode(), colorEggConfig.getId(), ZonedDateTime.now(), colorEggConfig.getAwardRule());
//            if (BigDecimalUtil.add(awardValue, sum).compareTo(maxValue) > 0) {
//                return CommonResult.fail("在活动期间内已经超过限制");
//            }
//            znsRunActivityService.handleTeamRunAward(awardValue, loginUser.getId(), colorEggConfig.getId(),
//                    AccountDetailTypeEnum.RUN_COLOR_EGG_AWARD, AccountDetailSubtypeEnum.RUN_COLOR_EGG_AWARD);
//
//        }
//        return CommonResult.success();
    }


    // https://tkjapi.yijiesudai.com/app/account/log
    @PostMapping("/log")
    public Result appLog(@RequestBody TestDto testDto) {
        log.info("app日志数据 " + testDto.getContent());
        return CommonResult.success();
    }

    @PostMapping("/getAccountByUserId")
    public Result<UserAccountDto> getAccountByUserId(@RequestBody UserIdRequestDto request) {
        ZnsUserAccountEntity userAccount = userAccountService.getUserAccount(request.getUserId());
        return CommonResult.success(userAccountConverter.toDto(userAccount));
    }

    @PostMapping("/addAccountDetail")
    public Result addAccountDetail(@RequestBody AddAccountDetailRequestDto request) {
        AccountDetailTypeEnum detailTypeEnum = AccountDetailTypeEnum.findByType(request.getDetailTypeEnum());
        AccountDetailSubtypeEnum subtypeEnum = AccountDetailSubtypeEnum.getAccountDetailSubtype(detailTypeEnum, request.getSubtypeEnum());
        userAccountDetailService.addAccountDetailAddActivityId(request.getUserId(), request.getType(), detailTypeEnum, subtypeEnum, request.getAmount(),
                request.getBillNo(), request.getPayAccount(), request.getTradeStatus(), request.getTradeNo(), request.getRefId(), request.getTaxAmount(),
                request.getServiceAmount(), request.getActivityId(), request.getActivityType(), request.getUserCouponId(),
                request.getPrivilegeBrand(), request.getBrandRightsInterests());
        return CommonResult.success();
    }

    /**
     * 校验验证码
     *
     * @param emailAddress
     * @param code
     * @return
     */
    private Result checkVerificationCode(String emailAddress, String code) {
        //获取缓存
        String tokenKey = ApiConstants.PAY_SEND_MAIL_TOKEN_KEY + emailAddress;
        String redisCode = redisUtil.get(tokenKey);

        if (!StringUtils.hasText(redisCode)) {
            return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), I18nMsgUtils.getMessage("user.account.verification.expired"));
        }
        //22 23:59 4

        if (!redisCode.equals(code)) {
            String countKey = ApiConstants.RESET_PASSWORD_INVALID_COUNT_KEY + ZonedDateTimeUtil.format(ZonedDateTime.now(), ZonedDateTimeUtil.PATTERN_DATE) + "_" + emailAddress;
            RedisAtomicInteger rai = new RedisAtomicInteger(countKey, redisTemplate.getConnectionFactory());
            //设置1天的有效期，过期释放key
            redisTemplate.expire(countKey, 1L, TimeUnit.DAYS);
            if (rai.get() >= 5) {
                return CommonResult.fail(CommonError.PARAM_LACK.getCode(), I18nMsgUtils.getMessage("user.account.verification.frequent"));
            }
            return CommonResult.fail(CommonError.PARAM_ERROR.getCode(), I18nMsgUtils.getMessage("user.account.verification.incorrect"));
        }
        return null;
    }
}
