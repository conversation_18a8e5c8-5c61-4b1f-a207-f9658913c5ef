package com.linzi.pitpat.api.controller.h5;


import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.mallservice.dto.request.MallPersonalizedRequestDto;
import com.linzi.pitpat.data.mallservice.dto.response.HomePersonalizedResponseDto;
import com.linzi.pitpat.data.mallservice.model.entity.PersonalizedPage;
import com.linzi.pitpat.data.mallservice.model.entity.PersonalizedPageI18n;
import com.linzi.pitpat.data.mallservice.service.PersonalizedPageI18nService;
import com.linzi.pitpat.data.mallservice.service.PersonalizedPageService;
import com.linzi.pitpat.data.systemservice.dto.api.request.AppCustomH5CheckCountryRequestDto;
import com.linzi.pitpat.data.systemservice.dto.api.request.AppCustomH5CouponPopRequestDto;
import com.linzi.pitpat.data.systemservice.dto.api.request.AppCustomH5RequestDto;
import com.linzi.pitpat.data.systemservice.dto.api.response.AppCustomH5CheckCountryResponseDto;
import com.linzi.pitpat.data.systemservice.dto.api.response.AppCustomH5ResponseDto;
import com.linzi.pitpat.data.systemservice.manager.api.ApiCustomH5Manager;
import com.linzi.pitpat.data.systemservice.model.entity.AppRouteConfig;
import com.linzi.pitpat.data.systemservice.model.entity.CustomH5;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * h5 自定义
 */
@RestController
@RequestMapping({"/h5/customh5", "/test/customh5"})
public class ApiCustomH5Contoller extends BaseAppController {

    @Autowired
    private AppRouteConfigService appRouteConfigService;

    @Autowired
    private PersonalizedPageService personalizedPageService;

    @Autowired
    private PersonalizedPageI18nService personalizedPageI18nService;

    @Resource
    private ApiCustomH5Manager apiCustomH5Manager;

    /**
     * 通过id获取
     * @since 474
     * @return
     */
    @PostMapping("/getById")
    public Result<AppCustomH5ResponseDto> getById(@RequestBody AppCustomH5RequestDto requestDto) {
        ZnsUserEntity znsUser = getLoginUserNotNull();
        AppCustomH5ResponseDto appCustomH5ResponseDto = apiCustomH5Manager.getById(requestDto.getId(), znsUser);
        return CommonResult.success(appCustomH5ResponseDto);
    }

    /**
     * 校验用户商城国家是否需要修改
     * @since 474
     */
    @PostMapping("/checkMallCountry")
    public Result<AppCustomH5CheckCountryResponseDto> checkMallCountry(@RequestBody AppCustomH5CheckCountryRequestDto requestDto) {
        AppCustomH5CheckCountryResponseDto resp = apiCustomH5Manager.checkMallCountry(getLoginUserNotNull(), requestDto);
        return CommonResult.success(resp);
    }


    /**
     * 券弹窗
     * @since 474
     * @return
     */
    @PostMapping("/popCoupon")
    public Result<Void> popCoupon(@RequestBody AppCustomH5CouponPopRequestDto requestDto) {
        ZnsUserEntity znsUser = getLoginUserNotNull();
        apiCustomH5Manager.popCoupon(requestDto, znsUser);
        return CommonResult.success();
    }


    /**
     * 路由配置列表
     *
     * @param customH5
     * @return
     */
    @PostMapping("/appRouteConfigList")
    public Result<List<AppRouteConfig>> appRouteConfigList(@RequestBody CustomH5 customH5) {
        List<AppRouteConfig> appRouteConfigs = appRouteConfigService.selectAppRouteConfigAll();
        return CommonResult.success(appRouteConfigs);
    }

    /**
     * 商城个性化页面详情
     *
     * @param mallPersonalizedRequestDto
     * @return
     */
    @PostMapping("/personalized/detail")
    public Result<HomePersonalizedResponseDto> detail(@RequestBody MallPersonalizedRequestDto mallPersonalizedRequestDto) {
        ZnsUserEntity loginUser = getLoginUserOrDefaultUser();
        PersonalizedPage personalizedPage = personalizedPageService.findById(mallPersonalizedRequestDto.getId());
        if (Objects.isNull(personalizedPage)) {
            return null;
        }
        HomePersonalizedResponseDto homePersonalizedResponseDto = new HomePersonalizedResponseDto();
        BeanUtils.copyProperties(personalizedPage, homePersonalizedResponseDto);

        List<PersonalizedPageI18n> i8nList = personalizedPageI18nService.findListByPageId(personalizedPage.getId());
        if (!CollectionUtils.isEmpty(i8nList)) {
            PersonalizedPageI18n personalizedPageI18n = i8nList.stream()
                    .filter(s -> Objects.equals(loginUser.getLanguageCode(), s.getLanguageCode())).findFirst()
                    .or(() -> i8nList.stream().filter(s -> Objects.equals(personalizedPage.getDefaultLanguageCode(), s.getLanguageCode())).findFirst())
                    .orElse(new PersonalizedPageI18n());
            homePersonalizedResponseDto.setContent(personalizedPageI18n.getContent());
            homePersonalizedResponseDto.setPageTitle(personalizedPageI18n.getPageTitle());
        }
        return CommonResult.success(homePersonalizedResponseDto);
    }
    /**
     * 商城个性化页面详情--H5投流不验签
     *
     * @param mallPersonalizedRequestDto
     * @return
     */
    @PostMapping("/personalized/detail/surrender")
    public Result<HomePersonalizedResponseDto> detailSurrender(@RequestBody MallPersonalizedRequestDto mallPersonalizedRequestDto) {
        ZnsUserEntity loginUser = getLoginUserOrDefaultUser();
        PersonalizedPage personalizedPage = personalizedPageService.findById(mallPersonalizedRequestDto.getId());
        if (Objects.isNull(personalizedPage)) {
            return null;
        }
        HomePersonalizedResponseDto homePersonalizedResponseDto = new HomePersonalizedResponseDto();
        BeanUtils.copyProperties(personalizedPage, homePersonalizedResponseDto);

        List<PersonalizedPageI18n> i8nList = personalizedPageI18nService.findListByPageId(personalizedPage.getId());
        if (!CollectionUtils.isEmpty(i8nList)) {
            PersonalizedPageI18n personalizedPageI18n = i8nList.stream()
                    .filter(s -> Objects.equals(loginUser.getLanguageCode(), s.getLanguageCode())).findFirst()
                    .or(() -> i8nList.stream().filter(s -> Objects.equals(personalizedPage.getDefaultLanguageCode(), s.getLanguageCode())).findFirst())
                    .orElse(new PersonalizedPageI18n());
            homePersonalizedResponseDto.setContent(personalizedPageI18n.getContent());
            homePersonalizedResponseDto.setPageTitle(personalizedPageI18n.getPageTitle());
        }
        return CommonResult.success(homePersonalizedResponseDto);
    }


}

