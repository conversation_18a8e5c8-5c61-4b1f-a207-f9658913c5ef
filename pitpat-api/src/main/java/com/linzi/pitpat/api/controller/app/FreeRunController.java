package com.linzi.pitpat.api.controller.app;


import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.dto.FreeRunDto;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.SpeedUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.service.MindUserMatchService;
import com.linzi.pitpat.data.activityservice.service.ZnsRunActivityService;
import com.linzi.pitpat.data.entity.dto.AppExposurePageDto;
import com.linzi.pitpat.data.entity.dto.RunPlanDto;
import com.linzi.pitpat.data.request.course.ExposureRequest;
import com.linzi.pitpat.data.resp.ExposureResp;
import com.linzi.pitpat.data.systemservice.dto.response.AppRoute;
import com.linzi.pitpat.data.systemservice.dto.response.PrimitiveForest;
import com.linzi.pitpat.data.systemservice.model.entity.ReportPageConfig;
import com.linzi.pitpat.data.systemservice.service.AppRouteConfigService;
import com.linzi.pitpat.data.systemservice.service.AppRouteService;
import com.linzi.pitpat.data.systemservice.service.ReportPageConfigService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 自由跑接口
 */
@RestController
@RequestMapping({"/app/free/run"})
@Slf4j
public class FreeRunController extends BaseAppController {


    @Autowired
    private MindUserMatchService mindUserMatchService;

    @Autowired
    private ZnsUserService znsUserService;

    @Resource
    private AppRouteService appRouteService;

    /**
     * 用户列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/userlist")
    public Result list(@RequestBody FreeRunDto dto) {
        List<RunPlanDto> runPlanDtos = new ArrayList<>();
        ZonedDateTime currentDate = ZonedDateTime.now();
        // 活动id 默认为-1
        List<MindUserMatch> mindUserMatches = mindUserMatchService.selectRunRobCountByActivityIdStatusRountIdRountType(dto.getActivityId(), 1, null, 2);
        Map<Long, ZnsUserEntity> userEntityMap = znsUserService.selectUserByMindMatch(mindUserMatches);
        for (MindUserMatch mindUserMatch : mindUserMatches) {
            Integer runSecond = DateUtil.betweenSecond(mindUserMatch.getGmtCreate(), currentDate);
            int allSecond = DateUtil.betweenSecond(mindUserMatch.getGmtCreate(), mindUserMatch.getActivityEndTime());
            int runMileage = SpeedUtil.mileage(mindUserMatch.getV(), runSecond);
            int remainMileage = mindUserMatch.getTargetMileage() - runMileage;
            ZnsUserEntity znsUserEntity = userEntityMap.get(mindUserMatch.getUserId());
            RunPlanDto runPlanDto = new RunPlanDto(runSecond,
                    1,
                    allSecond - runSecond,
                    runMileage,
                    remainMileage,
                    mindUserMatch.getTargetMileage(),
                    mindUserMatch.getV(),
                    znsUserEntity.getEmailAddressEn(),
                    znsUserEntity.getId(),
                    currentDate,
                    mindUserMatch.getActivityEndTime(), znsUserEntity.getHeadPortrait(), znsUserEntity.getFirstName(), mindUserMatch.getActivityId(), mindUserMatch.getId()
            );
            runPlanDtos.add(runPlanDto);
        }

        return CommonResult.success(runPlanDtos);
    }

    @Resource
    private ReportPageConfigService reportPageConfigService;
    @Resource
    private AppRouteConfigService appRouteConfigService;
    private static final String OFFICIAL_ENENT_ROUTE_URL = "lzrn://Race/RankingRaceDetailPage";
    private static final String TEAM_RUN_ROUTE_URL = "lzrn://Race/FreeRaceDetailPage";
    private static final String CHALLENGE_RUN_ROUTE_URL = "lzrn://Race/BattleRaceDetailPage";
    private static final String OFFICIAL_TEAM_RUN_ROUTE_URL = "lzrn://Race/EventDetailPage";
    private static final String AGGREGATION_ACTIVITY_ROUTE_URL = "lzrn://Race/AggregatedDetailPage";
    private static final String TEAM_COMPLETION_ROUTE_URL = "lznative://lzrace/teamrun";
    private static final String OPERATIONAL_ACTIVITY_URL = "/operational/runActivity/";
    private static final String NEW_ACTIVITY_URL = "lznative://lzrace/EventDetails";

    @Value("${pitpat.api.mallH5Url}")
    private String mallH5Url;

    @Resource
    private ZnsRunActivityService znsRunActivityService;

    /**
     * 报告页面
     *
     * @param request
     * @return
     */
    @PostMapping("/report/page")
    public Result<ExposureResp> reportPage(@RequestBody ExposureRequest request) {
        ExposureResp exposureResp = new ExposureResp();
        ZnsUserEntity user = getLoginUser();
        AppExposurePageDto appExposurePageDto = new AppExposurePageDto();
        List<ReportPageConfig> reportPageConfigs = reportPageConfigService.findList();
        appExposurePageDto.setPkType1("0");
        if (CollectionUtils.isEmpty(reportPageConfigs)) {
            CommonResult.success(exposureResp);
        }
        reportPageConfigs.forEach(i -> {
            appExposurePageDto.setRecommend0("Recommendation");
            appExposurePageDto.setRecommend1("Can you make money by running?");
            Map<String, Object> jsonObject = JsonUtil.readValue(i.getJumpParam());
            Integer jumpType = MapUtil.getInteger(jsonObject.get("jumpType"));
            if (i.getId() == 1) {
                AppRoute route = getAppRoute(user, jumpType, jsonObject);
                if (Objects.nonNull(route)) {
                    appExposurePageDto.setRouteValue1(route.getJumpUrl());
                    appExposurePageDto.setRouteParam1(route.getJumpParam());
                }
                appExposurePageDto.setRecommend2(i.getContent());
                appExposurePageDto.setRecommend_pic1(i.getImageUrl());
            }
            if (i.getId() == 2) {
                // 组装跳转链接
                AppRoute route = getAppRoute(user, jumpType, jsonObject);
                if (Objects.nonNull(route)) {
                    appExposurePageDto.setRouteValue2(route.getJumpUrl());
                    appExposurePageDto.setRouteParam2(route.getJumpParam());
                }
                appExposurePageDto.setRecommend3(i.getContent());
                appExposurePageDto.setRecommend_pic2(i.getImageUrl());
            }
        });
        exposureResp.setConfig(appExposurePageDto);
        return CommonResult.success(exposureResp);
    }

    private AppRoute getAppRoute(ZnsUserEntity user, Integer jumpType, Map<String, Object> jsonObject) {
        // 组装跳转链接
        PrimitiveForest forest = new PrimitiveForest();
        forest.setUserId(user.getId());
        forest.setJumpType(jumpType);
        forest.setJumpUrl(MapUtil.getString(jsonObject.get("activityUrl")));
        forest.setMainActivityType(MapUtil.getString(jsonObject.get("mainActivityType")));
        forest.setRunActivityId(MapUtil.getLong(jsonObject.get("runActivityId")));
        forest.setMallJumpType(MapUtil.getInteger(jsonObject.get("mallJumpType")));
        forest.setCategoryCode(MapUtil.getString(jsonObject.get("categoryCode")));
        forest.setGoodsId(MapUtil.getLong(jsonObject.get("goodsId")));
        AppRoute route = appRouteService.findRoute(forest);
        return route;
    }

}
