package com.linzi.pitpat.api.activityservice.manager;

import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.biz.RoomIdBizService;
import com.linzi.pitpat.data.activityservice.biz.UserInGameBizService;
import com.linzi.pitpat.data.activityservice.model.dto.UserInGameDto;
import com.linzi.pitpat.data.activityservice.model.entity.MainActivity;
import com.linzi.pitpat.data.activityservice.service.CompetitiveSeasonService;
import com.linzi.pitpat.data.activityservice.service.SubActivityService;
import com.linzi.pitpat.data.enums.ActivityStateEnum;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserActivityManager {

    private final ZnsUserService userService;

    private final MainActivityBizService mainActivityBizService;

    private final UserInGameBizService userInGameService;

    private final CompetitiveSeasonService competitiveSeasonService;


    /**
     * 用户是否正在跑步机竞技赛中
     *
     * @param userId
     * @return
     */
    public Optional<UserInGameDto> checkUserInCompetitiveGame(Long userId) {
        ZnsUserEntity byId = userService.findById(userId);
        if (byId == null || byId.getIsRobot() == 1 || byId.getIsTest() == 1 || byId.getIsDelete() != 0) {
            return Optional.of(UserInGameDto.notIn(userId));
        }
        UserInGameDto inGame = userInGameService.isInGame(userId);
        //1. 在游戏中
        if (inGame.isInGame()) {
            Optional<MainActivity> mainActivityOp = mainActivityBizService.getMainActivity(inGame.getActivityId());
            if (mainActivityOp.isPresent()) {
                MainActivity activity = mainActivityOp.get();
                if (DeviceConstant.EquipmentMainTypeEnum.ROWING.getType().equals(activity.getEquipmentMainType())) {
                    return Optional.of(UserInGameDto.notIn(userId));
                }
                //2. 获取主活动,活动进行中
                if (ActivityStateEnum.IN_PROGRESS.getState().equals(activity.getActivityState())) {
                    //3. 跑步机活动
                    if (DeviceConstant.EquipmentMainTypeEnum.TREADMILL.getType().equals(activity.getEquipmentMainType())) {
                        boolean b = competitiveSeasonService.checkActivityIsCompetitive(activity.getId());
                        //3. 是竞技赛
                        if (b) {
                            UserInGameDto in = UserInGameDto.in(userId, inGame.getRoomId());
                            in.setActivityId(inGame.getActivityId());
                            in.setMainActivityId(activity.getId());
                            in.setRouteId(inGame.getRouteId());
                            return Optional.of(in);
                        }
                    }
                }
            }
        }
        return Optional.of(UserInGameDto.notIn(userId));
    }
}
