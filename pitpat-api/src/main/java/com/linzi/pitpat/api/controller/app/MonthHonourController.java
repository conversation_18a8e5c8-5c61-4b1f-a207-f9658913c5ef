package com.linzi.pitpat.api.controller.app;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.dto.WeekDayDto;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.manager.UserCouponManager;
import com.linzi.pitpat.data.awardservice.model.entry.Coupon;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.config.Constant;
import com.linzi.pitpat.data.dao.activity.MonthRunDataDao;
import com.linzi.pitpat.data.dao.activity.WeekTaskConfigDao;
import com.linzi.pitpat.data.dao.activity.WeekTaskConfigListDao;
import com.linzi.pitpat.data.entity.activity.MonthHonourConfig;
import com.linzi.pitpat.data.entity.activity.MonthRunData;
import com.linzi.pitpat.data.entity.activity.WeekTaskConfig;
import com.linzi.pitpat.data.entity.activity.WeekTaskConfigList;
import com.linzi.pitpat.data.entity.honour.MonthHonourI18n;
import com.linzi.pitpat.data.query.honour.MonthHonourI18nQuery;
import com.linzi.pitpat.data.request.HonourReq;
import com.linzi.pitpat.data.resp.ExchangeTopResp;
import com.linzi.pitpat.data.resp.HonourResp;
import com.linzi.pitpat.data.resp.MonthHonourResp;
import com.linzi.pitpat.data.resp.MyWeekResp;
import com.linzi.pitpat.data.resp.WeekDataResp;
import com.linzi.pitpat.data.resp.WeekExchangePage;
import com.linzi.pitpat.data.resp.WeekTaskConfigType;
import com.linzi.pitpat.data.service.activity.MonthHonourConfigService;
import com.linzi.pitpat.data.service.activity.MonthRunDataService;
import com.linzi.pitpat.data.service.honour.MonthHonourI18nService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.enums.CountryFlagConstant;
import com.linzi.pitpat.data.userservice.model.entity.MonthUserConfig;
import com.linzi.pitpat.data.userservice.model.entity.WeekUserAwardRecord;
import com.linzi.pitpat.data.userservice.model.entity.WeekUserData;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.MonthUserConfigService;
import com.linzi.pitpat.data.userservice.service.WeekUserAwardRecordService;
import com.linzi.pitpat.data.userservice.service.WeekUserDataService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import com.linzi.pitpat.data.userservice.service.ZnsUserFriendService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.util.dto.WeekDayResp;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 周荣誉榜
 */
@RestController
@RequestMapping({"/app/month/honour", "/h5/month/honour"})
@Slf4j
public class MonthHonourController {

    @Autowired
    private MonthRunDataDao monthRunDataDao;

    @Autowired
    private ZnsUserService znsUserService;

    @Autowired
    private MonthHonourConfigService monthHonourConfigService;
    @Autowired
    private MonthUserConfigService monthUserConfigService;

    @Autowired
    private WeekUserDataService weekUserDataService;

    @Autowired
    private ISysConfigService sysConfigService;


    @Autowired
    private WeekTaskConfigListDao weekTaskConfigListDao;

    @Autowired
    private WeekUserAwardRecordService weekUserAwardRecordService;
    @Autowired
    private CouponService couponService;

    @Autowired
    private WeekTaskConfigDao weekTaskConfigDao;

    @Autowired
    private ZnsUserEquipmentService userEquipmentService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ZnsUserRunDataDetailsService znsUserRunDataDetailsService;

    @Autowired
    private MonthRunDataService monthRunDataService;
    @Autowired
    private MonthHonourI18nService monthHonourI18nService;

    @Resource
    private ZnsUserFriendService znsUserFriendService;
    @Resource
    private UserCouponManager userCouponManager;

    /**
     * 月度荣誉榜
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public Result match(@RequestBody HonourReq request) {
        List<Long> userIds = new ArrayList<>();
        ExpressionParser parser = new SpelExpressionParser();
        TemplateParserContext parserContext = new TemplateParserContext();
        Map<String, Object> params = new HashMap<>();
        MonthHonourResp monthHonourResp = new MonthHonourResp();
        List<HonourResp> honourRespList = new ArrayList<>();
        Integer popType = 0;
        String langCode = I18nMsgUtils.getLangCode();

        String month = request.getYearMonth();
        if (!StringUtils.hasText(month)) {
            month = DateUtil.formateDateStr(DateUtil.addMonths(ZonedDateTime.now(), -1), DateUtil.YYYY_MM);
        }
        List<String> monthList = new ArrayList<>();
        ZonedDateTime currentDate = ZonedDateTime.now();
        ZonedDateTime datePre6 = DateUtil.addMonths(currentDate, -6);
        for (int i = 1; i < 10; i++) {
            currentDate = DateUtil.addMonths(ZonedDateTime.now(), -i);
            String monthStr = DateUtil.formateDateStr(currentDate, DateUtil.YYYY_MM);
            if (currentDate.toInstant().toEpochMilli() < datePre6.toInstant().toEpochMilli()) {
                break;
            }
            monthList.add(monthStr);
        }

        monthHonourResp.setMonthList(monthList);
        //涉略最广
        //跑过各种比赛，用过各种课程
        //跑步次数达#{[runCount]}次
        //里程达#{[firstName]}miles

        MonthRunData dataBestRun = monthRunDataDao.selectMonthRunDataByMonthBestRun(month, 0);   //最佳跑者
        userIds.add(dataBestRun.getUserId());

        int popType1 = popTypeCal(dataBestRun, 1, request.getUserId());
        if (popType1 > 0) {
            popType = popType1;
        }

        MonthHonourConfig monthHonourConfig1 = monthHonourConfigService.selectMonthHonourConfigByType(1);
        params.put("runCount", dataBestRun.getRunCount());
        params.put("runDistance", BigDecimalUtil.div(dataBestRun.getRunDistance(), 1600));
        MonthHonourI18n i18n1 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig1.getId()).langCode(langCode).defaultLangCode(monthHonourConfig1.getDefaultLangCode()).build());
        String name1 = Objects.nonNull(i18n1) ? i18n1.getName() : monthHonourConfig1.getEnName();
        String remark1 = Objects.nonNull(i18n1) ? i18n1.getRemark() : monthHonourConfig1.getEnRemark();
        String bestRunRemark = parser.parseExpression(remark1, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name1, dataBestRun.getUserId(), 1, bestRunRemark));


        //最能跑者
        //里程达#{[runDistance]}miles
        //时长达 #{[runTime]} mins
        MonthHonourConfig monthHonourConfig2 = monthHonourConfigService.selectMonthHonourConfigByType(2);
        MonthRunData chengjiBashi = monthRunDataDao.selectMonthRunDataByChengjiBashi(month, 0);       //

        int popType2 = popTypeCal(chengjiBashi, 2, request.getUserId());
        if (popType2 > 0) {
            popType = popType2;
        }
        params.put("runDistance", BigDecimalUtil.div(chengjiBashi.getRunDistance(), 1600));
        params.put("runTime", BigDecimalUtil.div(chengjiBashi.getRunTime(), 60, 1000));
        MonthHonourI18n i18n2 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig2.getId()).langCode(langCode).defaultLangCode(monthHonourConfig2.getDefaultLangCode()).build());
        String name2 = Objects.nonNull(i18n2) ? i18n2.getName() : monthHonourConfig2.getEnName();
        String remark2 = Objects.nonNull(i18n2) ? i18n2.getRemark() : monthHonourConfig2.getEnRemark();
        String chengjiBashiRemark = parser.parseExpression(remark2, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name2, chengjiBashi.getUserId(), 2, chengjiBashiRemark));
        userIds.add(chengjiBashi.getUserId());

        //最能跑者
        //时长达#{[runDistance]}mins
        //里程达#{[runTime]}miles
        MonthHonourConfig monthHonourConfig3 = monthHonourConfigService.selectMonthHonourConfigByType(3);
        MonthRunData shiJianChangHe = monthRunDataDao.selectMonthRunDataByShiJianChangHe(month, 0);       //
        int popType3 = popTypeCal(shiJianChangHe, 3, request.getUserId());

        if (popType3 > 0) {
            popType = popType3;
        }

        params.put("runTime", BigDecimalUtil.div(shiJianChangHe.getRunTime(), 60, 1000));
        params.put("runDistance", BigDecimalUtil.div(shiJianChangHe.getRunDistance(), 1600));
        MonthHonourI18n i18n3 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig3.getId()).langCode(langCode).defaultLangCode(monthHonourConfig3.getDefaultLangCode()).build());
        String name3 = Objects.nonNull(i18n3) ? i18n3.getName() : monthHonourConfig3.getEnName();
        String remark3 = Objects.nonNull(i18n3) ? i18n3.getRemark() : monthHonourConfig3.getEnRemark();
        String shiJianChangHeRemark = parser.parseExpression(remark3, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name3, shiJianChangHe.getUserId(), 3, shiJianChangHeRemark));
        userIds.add(shiJianChangHe.getUserId());

        //平均速度最快
        //单次跑 #{[runDistance]} miles，平均速度最高 #{[maxVelocity]} miles/h
        MonthHonourConfig monthHonourConfig4 = monthHonourConfigService.selectMonthHonourConfigByType(4);
        MonthRunData huanYinJiao = monthRunDataDao.selectMonthRunDataByHuanYinJiao(month, 0);       //
        int popType4 = popTypeCal(huanYinJiao, 4, request.getUserId());
        if (popType4 > 0) {
            popType = popType4;
        }

        params.put("runDistance", BigDecimalUtil.div(huanYinJiao.getMaxVelocityDistance(), 1600));
        params.put("maxVelocity", BigDecimalUtil.div(BigDecimalUtil.div(huanYinJiao.getMaxVelocity(), 1.6)));
        MonthHonourI18n i18n4 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig4.getId()).langCode(langCode).defaultLangCode(monthHonourConfig4.getDefaultLangCode()).build());
        String name4 = Objects.nonNull(i18n4) ? i18n4.getName() : monthHonourConfig4.getEnName();
        String remark4 = Objects.nonNull(i18n4) ? i18n4.getRemark() : monthHonourConfig4.getEnRemark();
        String huanYinJiaoRemark = parser.parseExpression(remark4, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name4, huanYinJiao.getUserId(), 4, huanYinJiaoRemark));
        userIds.add(huanYinJiao.getUserId());

        //最佳参赛者
        //参加官方多人同跑 #{[guanfangMulCompileCount]} 次
        //参赛里程 #{[guanfangMulRunDistance]} miles
        //参赛时长 #{[guanfangMulRunTime]} mins
        MonthHonourConfig monthHonourConfig5 = monthHonourConfigService.selectMonthHonourConfigByType(5);
        MonthRunData saiShiWang = monthRunDataDao.selectMonthRunDataBySaiShiWang(month, 0);       //赛事王

        int popType5 = popTypeCal(saiShiWang, 5, request.getUserId());
        if (popType5 > 0) {
            popType = popType5;
        }

        params.put("guanfangMulCompileCount", saiShiWang.getGuanfangMulCompileCount());
        params.put("guanfangMulRunDistance", BigDecimalUtil.div(saiShiWang.getGuanfangMulRunDistance(), 1.6, 1000));
        params.put("guanfangMulRunTime", BigDecimalUtil.div(saiShiWang.getGuanfangMulRunTime(), 60, 1000));
        MonthHonourI18n i18n5 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig5.getId()).langCode(langCode).defaultLangCode(monthHonourConfig5.getDefaultLangCode()).build());
        String name5 = Objects.nonNull(i18n5) ? i18n5.getName() : monthHonourConfig5.getEnName();
        String remark5 = Objects.nonNull(i18n5) ? i18n5.getRemark() : monthHonourConfig5.getEnRemark();
        String saiShiWangRemark = parser.parseExpression(remark5, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name5, saiShiWang.getUserId(), 5, saiShiWangRemark));
        userIds.add(saiShiWang.getUserId());

        //以一敌百
        //参加官方排行赛上榜次数最多，达 #{[guanfangRankUpPositionCount]} 次
        //完赛次数 #{[guanfangRankCompleteCount]} 次
        //最佳排名第 #{[guanfangRankBestPosition]} 名
        MonthHonourConfig monthHonourConfig6 = monthHonourConfigService.selectMonthHonourConfigByType(6);
        MonthRunData Ovs100 = monthRunDataDao.selectMonthRunDataBy1vs100(month, 0);       //
        int popType6 = popTypeCal(Ovs100, 6, request.getUserId());
        if (popType6 > 0) {
            popType = popType6;
        }

        params.put("guanfangRankUpPositionCount", Ovs100.getGuanfangRankUpPositionCount());
        params.put("guanfangRankCompleteCount", Ovs100.getGuanfangRankCompleteCount());
        params.put("guanfangRankBestPosition", Ovs100.getGuanfangRankBestPosition());
        MonthHonourI18n i18n6 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig6.getId()).langCode(langCode).defaultLangCode(monthHonourConfig6.getDefaultLangCode()).build());
        String name6 = Objects.nonNull(i18n6) ? i18n6.getName() : monthHonourConfig6.getEnName();
        String remark6 = Objects.nonNull(i18n6) ? i18n6.getRemark() : monthHonourConfig6.getEnRemark();
        String Ovs100Remark = parser.parseExpression(remark6, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name6, Ovs100.getUserId(), 6, Ovs100Remark));
        userIds.add(Ovs100.getUserId());


        //工作自由
        //周一到周五累计跑步 #{[workDayRunCount]} 次
        //总里程 #{[workDayRunDistance]} miles
        MonthHonourConfig monthHonourConfig7 = monthHonourConfigService.selectMonthHonourConfigByType(7);
        MonthRunData gongzhuoZhiYou = monthRunDataDao.selectMonthRunDataByGongzhuoZhiYou(month, 0);       //
        int popType7 = popTypeCal(gongzhuoZhiYou, 7, request.getUserId());
        if (popType7 > 0) {
            popType = popType7;
        }

        params.put("workDayRunCount", gongzhuoZhiYou.getWorkDayRunCount());
        params.put("workDayRunDistance", BigDecimalUtil.div(gongzhuoZhiYou.getWorkDayRunDistance(), 1600));
        MonthHonourI18n i18n7 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig7.getId()).langCode(langCode).defaultLangCode(monthHonourConfig7.getDefaultLangCode()).build());
        String name7 = Objects.nonNull(i18n7) ? i18n7.getName() : monthHonourConfig7.getEnName();
        String remark7 = Objects.nonNull(i18n7) ? i18n7.getRemark() : monthHonourConfig7.getEnRemark();
        String gongzhuoZhiYouRemark = parser.parseExpression(remark7, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name7, gongzhuoZhiYou.getUserId(), 7, gongzhuoZhiYouRemark));
        userIds.add(gongzhuoZhiYou.getUserId());
        //假期健康
        //周六到周日累计跑步 #{[weekendRunCount]} 次
        //总里程 #{[weekendRunDistance]} miles

        MonthHonourConfig monthHonourConfig8 = monthHonourConfigService.selectMonthHonourConfigByType(8);
        MonthRunData jiangKuangJiaQi = monthRunDataDao.selectMonthRunDataByJiangKuangJiaQi(month, 0);

        int popType8 = popTypeCal(jiangKuangJiaQi, 8, request.getUserId());
        if (popType8 > 0) {
            popType = popType8;
        }

        params.put("weekendRunCount", jiangKuangJiaQi.getWeekendRunCount());
        params.put("weekendRunDistance", BigDecimalUtil.div(jiangKuangJiaQi.getWeekendRunDistance(), 1600));
        MonthHonourI18n i18n8 = monthHonourI18nService.findByQuery(MonthHonourI18nQuery.builder().configId(monthHonourConfig8.getId()).langCode(langCode).defaultLangCode(monthHonourConfig8.getDefaultLangCode()).build());
        String name8 = Objects.nonNull(i18n8) ? i18n8.getName() : monthHonourConfig8.getEnName();
        String remark8 = Objects.nonNull(i18n8) ? i18n8.getRemark() : monthHonourConfig8.getEnRemark();
        String jiangKuangJiaQiRemark = parser.parseExpression(remark8, parserContext).getValue(params, String.class);
        honourRespList.add(new HonourResp(name8, jiangKuangJiaQi.getUserId(), 8, jiangKuangJiaQiRemark));
        userIds.add(jiangKuangJiaQi.getUserId());
        monthHonourResp.setMonth(month);
        monthHonourResp.setHonourRespList(honourRespList);
        Map<Long, ZnsUserEntity> maps = znsUserService.selectMapZnsUserListByUserIds(userIds);

        List<HonourResp> list = monthHonourResp.getHonourRespList();
        for (HonourResp honourResp : list) {
            ZnsUserEntity znsUserEntity = maps.get(honourResp.getUserId());
            honourResp.setHeadPortrait(znsUserEntity.getHeadPortrait());
            honourResp.setFirstName(znsUserEntity.getFirstName());
        }
        MonthUserConfig monthUserConfig = monthUserConfigService.selectMonthUserConfigByUserId(request.getUserId());
        monthHonourResp.setMonthHonourStatus(monthUserConfig == null ? 0 : monthUserConfig.getStatus());

        String monthPre = DateUtil.formateDateStr(DateUtil.addMonths(ZonedDateTime.now(), -1), DateUtil.YYYY_MM);
        if (monthPre.equals(month)) {
            monthHonourResp.setPopType(popType);
        } else {
            monthHonourResp.setPopType(0);          //1. 有个后补需求，如果查询荣誉榜时查的不是上个月的榜单，即使上榜了没弹过窗，也不能弹窗。
        }
        return CommonResult.success(monthHonourResp); // 是这样的，但是还是要相信这是一个东西 的，不然，mac pro 如何中英文切换
    }

    public Integer popTypeCal(MonthRunData monthRunData, Integer type, Long userId) {
        if (monthRunData == null) {
            return 0;
        }
        if (Objects.equals(type, 1) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop1(), 0)) {
            monthRunData.setPop1(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }

        if (Objects.equals(type, 2) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop2(), 0)) {
            monthRunData.setPop2(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }

        if (Objects.equals(type, 3) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop3(), 0)) {
            monthRunData.setPop3(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }

        if (Objects.equals(type, 4) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop4(), 0)) {
            monthRunData.setPop4(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }

        if (Objects.equals(type, 5) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop5(), 0)) {
            monthRunData.setPop5(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }
        if (Objects.equals(type, 6) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop6(), 0)) {
            monthRunData.setPop6(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }

        if (Objects.equals(type, 7) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop7(), 0)) {
            monthRunData.setPop7(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;

        }
        if (Objects.equals(type, 8) &&
                Objects.equals(monthRunData.getUserId(), userId) &&
                Objects.equals(monthRunData.getPop8(), 0)) {
            monthRunData.setPop8(1);
            monthRunDataDao.updateMonthRunDataById(monthRunData);
            return type;
        }
        return 0;
    }

    /**
     * 设置月度荣誉榜
     *
     * @param request
     * @return
     */
    @PostMapping("/setStatus")
    public Result setStatus(@RequestBody HonourReq request) {
        MonthUserConfig monthUserConfig = monthUserConfigService.selectMonthUserConfigByUserId(request.getUserId());
        if (monthUserConfig == null) {
            monthUserConfig = new MonthUserConfig();
        }


        MonthRunData monthRunData = monthRunDataService.saveMonthRunData(ZonedDateTime.now(), request.getUserId());

        monthUserConfig.setStatus(request.getStatus());
        monthUserConfig.setUserId(request.getUserId());

        if (Objects.equals(request.getStatus(), 0)) {
            ZonedDateTime nextMonth = DateUtil.addMonths1(ZonedDateTime.now(), 1);
            monthUserConfig.setAddTime(DateUtil.getFirstOfMonth(nextMonth));
        }
        monthRunData.setIsAddRank(request.getStatus());
        monthRunDataDao.updateMonthRunDataById(monthRunData);

        monthUserConfigService.insertOrUpdateMonthUserConfig(monthUserConfig);
        Result result = CommonResult.success();
        if (Objects.equals(request.getStatus(), 1)) {
            result.setMsg(I18nMsgUtils.getMessage("monthHonor.withdraw.success"));
        } else {
            result.setMsg(I18nMsgUtils.getMessage("monthHonor.join.success"));
        }
        return result;
    }

    /**
     * 周荣誉榜-设置浮窗荣誉状态
     *
     * @param request
     * @return
     */
    @PostMapping("/setMonthHonourPopStatus")
    public Result setMonthHonourPopStatus(@RequestBody HonourReq request) {
        String monthStr = DateUtil.formateDateStr(DateUtil.addMonths(ZonedDateTime.now(), -1), DateUtil.YYYY_MM);
        MonthRunData monthRunData = monthRunDataDao.selectMonthRunDataByUserIdMonth(request.getUserId(), monthStr);

        if (monthRunData == null) {
            monthRunData = monthRunDataService.saveMonthRunData(DateUtil.addMonths(ZonedDateTime.now(), -1), request.getUserId());
        }

        monthRunDataDao.updateMonthRunDataMonthHonourPopById(ZonedDateTime.now(), 1, monthRunData.getId());
        return CommonResult.success();
    }

    /**
     * 周排行榜报告
     *
     * @param request
     * @return
     */
    @PostMapping("/monthImage")
    public Result monthImage(@RequestBody HonourReq request) {
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.rank_billboard_config.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        return CommonResult.success(data);
    }

    /**
     * 周排行榜报告
     *
     * @param request
     * @return
     */
    @PostMapping("/weekDataReport")
    public Result weekDataReport(@RequestBody HonourReq request) {
        ZonedDateTime preWeek = DateUtil.addDays(ZonedDateTime.now(), -7);
        if (request.getWeekStart() != null) {
            preWeek = request.getWeekStart();
            log.info("第一次时间 ：" + DateUtil.formateDateStr(preWeek, DateUtil.YYYY_MM_DD_HH_MM_SS));
            preWeek = DateUtil.addDays(preWeek, 7);
            log.info("第二次时间 ：" + DateUtil.formateDateStr(preWeek, DateUtil.YYYY_MM_DD_HH_MM_SS));
        }
        return doWeekData(request, preWeek);
    }

    /**
     * 周排行榜
     *
     * @param request
     * @return
     */
    @PostMapping("/weekData")
    public Result weekData(@RequestBody HonourReq request) {
        return doWeekData(request, ZonedDateTime.now());
    }

    public Result doWeekData(HonourReq request, ZonedDateTime preWeek) {
        MyWeekResp resp = new MyWeekResp();
        WeekUserData userData = weekUserDataService.init(preWeek, request.getUserId());
        ZnsUserEntity currentUser = znsUserService.findById(request.getUserId());
        WeekDataResp weekDataRespUser = new WeekDataResp();
        BeanUtils.copyProperties(userData, weekDataRespUser);
        weekDataRespUser.setHeadPortrait(currentUser.getHeadPortrait());
        weekDataRespUser.setFirstName(currentUser.getFirstName());
        weekDataRespUser.setCountry(currentUser.getCountry());
        I18nConstant.CountryCodeEnum countryEnum = I18nConstant.CountryCodeEnum.findByEnName(currentUser.getCountry());
        String flag = CountryFlagConstant.FlagMap.get(Optional.ofNullable(countryEnum).orElse(I18nConstant.CountryCodeEnum.US).enName);
        weekDataRespUser.setFlag(flag); //设置用户国旗

        WeekDayDto weekDayDto = DateUtil.getNumWeek(preWeek); // 查上一周的数据
        SysConfig sysConfig = sysConfigService.selectSysConfigByKey(ConfigKeyEnums.rank_billboard_config.getCode());
        Map<String, Object> data = JsonUtil.readValue(sysConfig.getConfigValue());
        List<String> backImages = JsonUtil.readList(data.get("backImages"), String.class);
        int rankPerson = MapUtil.getInteger(data.get("rankPerson"), 0);
        resp.setRankPerson(rankPerson);

        List<WeekUserData> weekUserDataList = weekUserDataService.selectWeekUserDataByYearMonth(weekDayDto.getCurrentMonth(), weekDayDto.getWeekN(), BigDecimal.ZERO, 0l, rankPerson);
        Map<Long, ZnsUserEntity> znsUserEntityMap = znsUserService.selectZnsUserEntityByObjectUserId(weekUserDataList);
        List<WeekDataResp> weekDataResps = new ArrayList<>();
        int i = 1;

        List<Long> followingIdList = new ArrayList<>();
        Map<Long, ZnsUserEntity> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(weekUserDataList)) {
            List<Long> friendIds = weekUserDataList.stream().map(WeekUserData::getUserId).collect(Collectors.toList());
            followingIdList = znsUserFriendService.getFollowingIdList(currentUser.getId(), friendIds);
            userMap = znsUserService.findByIds(friendIds).stream().collect(Collectors.toMap(ZnsUserEntity::getId, user -> user));
        }
        for (WeekUserData weekUserData : weekUserDataList) {
            WeekDataResp weekDataResp = new WeekDataResp();
            weekDataResp.setRank(i);
            weekDataResp.setUserId(weekUserData.getUserId());
            weekDataResp.setRunDistance(weekUserData.getRunDistance());
            ZnsUserEntity znsUserEntity = znsUserEntityMap.get(weekUserData.getUserId());
            weekDataResp.setFirstName(znsUserEntity.getFirstName());
            weekDataResp.setHeadPortrait(znsUserEntity.getHeadPortrait());
            weekDataResp.setCountry(znsUserEntity.getCountry());
            I18nConstant.CountryCodeEnum userCountryEnum = I18nConstant.CountryCodeEnum.findByEnName(znsUserEntity.getCountry());
            String userFlag = CountryFlagConstant.FlagMap.get(Optional.ofNullable(userCountryEnum).orElse(I18nConstant.CountryCodeEnum.US).enName);
            weekDataResp.setFlag(userFlag); //设置用户国旗
            ZnsUserEntity user = userMap.get(weekUserData.getUserId());
            if (Objects.nonNull(user)) {
                weekDataResp.setIsPrivacy(user.getIsPrivacy());
            }
            weekDataResp.setIsFollow(followingIdList.contains(weekUserData.getUserId()));
            if (Objects.equals(currentUser.getId(), weekUserData.getUserId())) {
                // 本人不展示关注按钮
                weekDataResp.setIsFollow(null);
            }
            weekDataResps.add(weekDataResp);
            i++;
        }
        BigDecimal runDistance = userData.getRunDistance();
        //避免测试用户空指针
        if (Objects.nonNull(runDistance) && runDistance.compareTo(BigDecimal.ZERO) > 0) {
            int rank = weekUserDataService.selectRunDistanceRank(weekDayDto.getCurrentMonth(), weekDayDto.getWeekN(), runDistance);
            resp.setRank(rank + 1);
        }

        Integer isUpBoard = 0;
        int count = 1;
        WeekDayDto weekDayPreWeek = DateUtil.getNumWeek(DateUtil.addDays(ZonedDateTime.now(), -7)); // 查上一周的数据
        List<WeekUserData> weekUserDataListPre = weekUserDataService.selectWeekUserDataByYearMonth(weekDayPreWeek.getCurrentMonth(), weekDayPreWeek.getWeekN(), BigDecimal.ZERO, 0l, rankPerson);
        for (WeekUserData weekUserData : weekUserDataListPre) {
            if (Objects.equals(weekDataRespUser.getUserId(), weekUserData.getUserId())) {
                isUpBoard = 1;
                resp.setPreRank(count);
            }
            count++;
        }

        resp.setIsUpBoard(isUpBoard);
        resp.setUserData(weekDataRespUser);
        resp.setUserDataList(weekDataResps);
        resp.setWeekStart(DateUtil.formateDateStr(weekDayDto.getWeekStart(), DateUtil.YYYY_MM_DD));
        resp.setWeekEnd(DateUtil.formateDateStr(weekDayDto.getWeekEnd(), DateUtil.YYYY_MM_DD));
        resp.setRunDistance(userData.getRunDistance());
        Random random = new Random();
        resp.setBackImage(backImages.get(random.nextInt(backImages.size())));
        WeekDayDto currentWeek = DateUtil.getNumWeek(ZonedDateTime.now()); // 查询这一周的数据

        Object obj = redisUtil.get(Constant.week_config_pop + request.getUserId());
        if (obj != null) {
            resp.setIsPop(0);                       //已经弹过窗了
        } else {
            log.info("本周第一次弹窗 userId=" + request.getUserId());
            resp.setIsPop(1);
            redisUtil.set(Constant.week_config_pop + request.getUserId(), "1",
                    DateUtil.betweenSecond(ZonedDateTime.now(), DateUtil.endOfDate(currentWeek.getWeekEnd())),
                    TimeUnit.SECONDS);
        }

        return CommonResult.success(resp);
    }

    /**
     * 兑换福利
     *
     * @param request
     * @return
     */
    @PostMapping("/exchangeAward")
    public Result exchangeAward(@RequestBody HonourReq request) {
        WeekExchangePage page = new WeekExchangePage();
        WeekDayDto weekDayDto = DateUtil.getNumWeek(ZonedDateTime.now());
        WeekUserData weekUserData = weekUserDataService.selectWeekUserDataByYearMonthWeekNUserId(weekDayDto.getCurrentMonth(), weekDayDto.getWeekN(), request.getUserId());

        WeekTaskConfig weekTaskConfig = weekTaskConfigDao.selectWeekTaskConfigByMonth(weekDayDto.getCurrentMonth(), weekDayDto.getWeekN());

        WeekTaskConfigType userDataType3 = weekTaskConfigListDao.selectWeekTaskConfigListByYearMonthWeekType(weekTaskConfig.getId(), 3);

        Map<Long, WeekUserAwardRecord> weekUserAwardRecordMap = weekUserAwardRecordService.selectWeekUserAwardRecordByYearMonthWeeekUserId(weekDayDto.getCurrentMonth(), weekDayDto.getWeekN(), request.getUserId());

        userDataType3.setIsAward(isAward(weekUserAwardRecordMap, userDataType3.getId()));
        List<WeekTaskConfigType> userDataType2 = weekTaskConfigListDao.selectWeekTaskConfigListByYearMonthWeekTypeList(weekTaskConfig.getId(), 2);
        List<WeekTaskConfigType> userDataType1 = weekTaskConfigListDao.selectWeekTaskConfigListByYearMonthWeekTypeList(weekTaskConfig.getId(), 1);
        List<WeekTaskConfigType> all = new ArrayList<>();
        all.addAll(userDataType1);
        all.addAll(userDataType2);

        Map<Long, Coupon> couponMap = couponService.selectCouponByWeekTaskConfigTypeId(all);

        String preCouponName = I18nMsgUtils.getMessage("monthHonor.send.title"); //TODO 1i8n 优惠券 title 配置
        for (WeekTaskConfigType weekTaskConfigType : userDataType2) {
            weekTaskConfigType.setIsAward(isAward(weekUserAwardRecordMap, weekTaskConfigType.getId()));
            Coupon coupon = couponMap.get(weekTaskConfigType.getCouponId());
            weekTaskConfigType.setCouponName(preCouponName + " " + coupon.getTitle());
            weekTaskConfigType.setRealCouponName(coupon.getTitle());
        }

        for (WeekTaskConfigType weekTaskConfigType : userDataType1) {
            weekTaskConfigType.setIsAward(isAward(weekUserAwardRecordMap, weekTaskConfigType.getId()));
            Coupon coupon = couponMap.get(weekTaskConfigType.getCouponId());
            weekTaskConfigType.setCouponName(preCouponName + " " + coupon.getTitle());
            weekTaskConfigType.setRealCouponName(coupon.getTitle());
        }

        page.setUserDataType3(userDataType3);
        page.setUserDataType2(userDataType2);
        page.setUserDataType1(userDataType1);

        page.setRunDistance(weekUserData == null ? BigDecimal.ZERO : weekUserData.getRunDistance());
        page.setRunDay(weekUserData == null ? 0 : weekUserData.getRunDay());

        page.setWeekDayDto(weekDayDto);
        Coupon coupon = couponService.selectCouponById(userDataType3.getCouponId());
        page.setCouponName(preCouponName + coupon.getTitle());
        page.setRealCouponName(coupon.getTitle());
        return CommonResult.success(page);
    }

    /**
     * 兑换福利顶部
     *
     * @param request
     * @return
     */
    @PostMapping("/exchangeTop")
    public Result exchangeTop(@RequestBody HonourReq request) {
        ExchangeTopResp resp = new ExchangeTopResp();
        List<String> monthList = new ArrayList<>();

        for (int i = 0; i < 6; i++) {
            ZonedDateTime month = DateUtil.addMonths(ZonedDateTime.now(), -i);
            monthList.add(DateUtil.formateDateStr(month, DateUtil.YYYY_MM));
        }


        String currentMonthStr = DateUtil.formateDateStr(ZonedDateTime.now(), DateUtil.YYYY_MM);
        if (StringUtils.hasText(request.getYearMonth())) {
            currentMonthStr = request.getYearMonth();
        }
        ZonedDateTime currentMonth = DateUtil.formateDate(currentMonthStr, DateUtil.YYYY_MM);
        List<WeekDayDto> weekDayDtoList = DateUtil.getWeekDays(currentMonth);
        List<WeekDayResp> weekDayResps = new ArrayList<>();
        for (WeekDayDto weekDayDto : weekDayDtoList) {
            WeekDayResp weekDayResp = new WeekDayResp();
            BeanUtils.copyProperties(weekDayDto, weekDayResp);
            BigDecimal runDistance = znsUserRunDataDetailsService.selectRunDistance(weekDayDto.getWeekStart(), weekDayDto.getWeekEnd(), request.getUserId(), null);
            List<ZnsUserRunDataDetailsEntity> znsUserRunDataDetailsEntities = znsUserRunDataDetailsService.selectRunDistanceList(weekDayDto.getWeekStart(), weekDayDto.getWeekEnd(), request.getUserId(), null);
            Set<Integer> set = new HashSet<>();
            for (ZnsUserRunDataDetailsEntity znsUserRunDataDetailsEntity : znsUserRunDataDetailsEntities) {
                weekDayDto = DateUtil.getNumWeek(znsUserRunDataDetailsEntity.getCreateTime());
                set.add(weekDayDto.getWeek());
            }
            weekDayResp.setWeekRunDistance(runDistance);
            weekDayResp.setRunWeekDays(set);
            weekDayResps.add(weekDayResp);
        }
        ZonedDateTime currentMonthStart = DateUtil.getFirstOfMonth(currentMonth);
        ZonedDateTime currentMonthEnd = DateUtil.getEndOfMonth(currentMonth);
        BigDecimal monthRunDistance = znsUserRunDataDetailsService.selectRunDistance(currentMonthStart, currentMonthEnd, request.getUserId(), null);
        Integer monthRunDays = znsUserRunDataDetailsService.selectRunDayNotInActivityTypeDataSource(currentMonthStart, currentMonthEnd, request.getUserId(), null, null);
        resp.setMonthRunDistance(monthRunDistance);
        resp.setMonthRunDays(monthRunDays);
        resp.setWeekDayDtos(weekDayResps);
        resp.setMonthList(monthList);
        return CommonResult.success(resp);
    }

    /**
     * 兑换福利
     *
     * @param request
     * @return
     */
    @PostMapping("/award")
    public Result award(@RequestBody HonourReq request) {
        WeekTaskConfigList userDataType = weekTaskConfigListDao.selectWeekTaskConfigListById(request.getConfigTypeId());
        WeekUserAwardRecord weekUserAwardRecord = weekUserAwardRecordService.selectWeekUserAwardRecordByUserId(userDataType.getId(), request.getUserId());
        if (weekUserAwardRecord != null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("honour.month.not.allow")); //"已经领取过了
        }
        WeekUserData weekUserData = weekUserDataService.selectWeekUserDataByYearMonthWeekNUserId(userDataType.getYearMonthN(), userDataType.getWeekN(), request.getUserId());
        if (weekUserData == null) {
            return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error"));
        }
        if (Objects.equals(userDataType.getType(), 3)) {
            if (weekUserData.getRunDistance().compareTo(userDataType.getRunDistance()) < 0
                    || weekUserData.getRunDay() < userDataType.getDayN()) {
                log.info("距离或者天数没有达到 ");
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error"));
            }
        } else if (Objects.equals(userDataType.getType(), 1)) {
            if (weekUserData.getRunDistance().compareTo(userDataType.getRunDistance()) < 0) {
                log.info("距离没有达到 ");
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error"));
            }
        } else if (Objects.equals(userDataType.getType(), 2)) {
            if (weekUserData.getRunDay() < userDataType.getDayN()) {
                log.info("天数没有达到");
                return CommonResult.fail(I18nMsgUtils.getMessage("common.params.error"));
            }
        }
        weekUserAwardRecord = new WeekUserAwardRecord();
        weekUserAwardRecord.setTaskConfigListId(userDataType.getId());
        weekUserAwardRecord.setYearMonthN(userDataType.getYearMonthN());
        weekUserAwardRecord.setWeekN(userDataType.getWeekN());
        weekUserAwardRecord.setDayN(userDataType.getDayN());
        weekUserAwardRecord.setRunDistance(userDataType.getRunDistance());
        weekUserAwardRecord.setUserId(request.getUserId());
        weekUserAwardRecord.setType(userDataType.getType());
        weekUserAwardRecord.setTaskConfigId(userDataType.getWeekTaskConfigId());
        weekUserAwardRecordService.insertWeekUserAwardRecord(weekUserAwardRecord);

        Integer weekChallengeGetNum = weekUserAwardRecordService.selectWeekUserAwardRecordByYearMonthWeekNType(userDataType.getYearMonthN(), userDataType.getWeekN(), Arrays.asList(3));
        Integer weekAwardNum = weekUserAwardRecordService.selectWeekUserAwardRecordByYearMonthWeekNType(userDataType.getYearMonthN(), userDataType.getWeekN(), Arrays.asList(1, 2));
        weekUserDataService.updateWeekUserDataByWeekChallengeGetNumId(ZonedDateTime.now(), weekChallengeGetNum, weekAwardNum, weekUserData.getId());

        Coupon coupon = couponService.selectCouponById(userDataType.getCouponId());
        ZnsUserEquipmentEntity userEquipment = userEquipmentService.getUserEquipmentOne(request.getUserId());
        ZnsUserEntity znsUserEntity = znsUserService.findById(request.getUserId());
        userCouponManager.exchangeUserCoupon(coupon, userEquipment, znsUserEntity, 1, null);
        return CommonResult.success();
    }

    public int isAward(Map<Long, WeekUserAwardRecord> weekUserAwardRecordMap, Long key) {
        if (weekUserAwardRecordMap == null) {
            return 0;
        }
        if (weekUserAwardRecordMap.get(key) != null) {
            return 1;
        }
        return 0;
    }


    //public static void main(String[] args) {
    //
    //
    //    ZonedDateTime date = new Date(1679241600000l);
    //
    //    System.out.println(DateUtil.formateDateStr(date,DateUtil.YYYY_MM_DD_HH_MM_SS));
    //
    //    WeekDayDto weekDayDto = DateUtil.getNumWeek(date);
    //    System.out.println(weekDayDto.getWeekN());
    //
    //    List<String> list = new ArrayList<>();
    //    list.add("https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    list.add("https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    list.add("https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    list.add("https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    list.add("https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    Map<String, Object> map = new HashMap<>();
    //    map.put("backImages", list);
    //    map.put("rankPerson", 20);
    //
    //    map.put("monthJumpType", "6");
    //    map.put("monthJumpValue", "https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    map.put("monthImageUrl", "https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //
    //    map.put("weekJumpType", "4");
    //    map.put("weekJumpValue", "https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //    map.put("weekImageUrl", "https://img9.doubanio.com/view/photo/large/public/p1781165485.webp");
    //
    //
    //    map.put("monthPageWidth", 750);
    //    map.put("monthPageHigh", 2142);
    //    map.put("monthPageImage", "https://pitpat-oss.s3.us-east-2.amazonaws.com/202303/iAn13oGwBTv77226.png");
    //
    //
    //    System.out.println(BigDecimalUtil.div(new BigDecimal(15724.00), 1.6,1000));
    //
    //}

}
