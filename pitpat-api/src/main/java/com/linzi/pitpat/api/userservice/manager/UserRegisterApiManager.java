package com.linzi.pitpat.api.userservice.manager;

import com.linzi.pitpat.api.userservice.dto.request.EmailBindReqDto;
import com.linzi.pitpat.api.userservice.dto.request.UserAppRegisterReqDto;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.core.util.PatternUtils;
import com.linzi.pitpat.data.awardservice.service.UserMedalService;
import com.linzi.pitpat.data.awardservice.service.UserWearsBagService;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountService;
import com.linzi.pitpat.data.constant.VersionConstant;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.entity.dto.MailDto;
import com.linzi.pitpat.data.entity.dto.user.SendVerificationCodeDto;
import com.linzi.pitpat.data.enums.EmailSendingRecordTypeEnum;
import com.linzi.pitpat.data.enums.EmailType;
import com.linzi.pitpat.data.enums.MessageSupportedEventTypeEnum;
import com.linzi.pitpat.data.equipmentservice.biz.VirtualEquipmentBiz;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.UserEquipmentShareDo;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.model.query.UserEquipmentShareQuery;
import com.linzi.pitpat.data.equipmentservice.service.UserEquipmentShareService;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.messageservice.service.ZnsMessageService;
import com.linzi.pitpat.data.robotservice.listener.event.UserLoginEvent;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.service.ISysConfigService;
import com.linzi.pitpat.data.userservice.biz.PutChannelUserBizService;
import com.linzi.pitpat.data.userservice.biz.TrafficInvestmentBizService;
import com.linzi.pitpat.data.userservice.biz.UserRainmakerBizService;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import com.linzi.pitpat.data.userservice.dto.event.UserLevelChangeEvent;
import com.linzi.pitpat.data.userservice.dto.request.CreatUserReqDto;
import com.linzi.pitpat.data.userservice.dto.request.ThirdCreatUserReqDto;
import com.linzi.pitpat.data.userservice.dto.request.ThirdUserNoThirdEmailReqDto;
import com.linzi.pitpat.data.userservice.dto.request.ThirdUserReqDto;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.enums.UserConstant;
import com.linzi.pitpat.data.userservice.enums.UserExpObtainSubTypeEnum;
import com.linzi.pitpat.data.userservice.model.entity.TrafficInvestmentPackageActivityDo;
import com.linzi.pitpat.data.userservice.model.entity.TrafficInvestmentPackageDo;
import com.linzi.pitpat.data.userservice.model.entity.UserExtraDo;
import com.linzi.pitpat.data.userservice.model.entity.UserThirdOauth;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEmailSendingRecordEntity;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.TrafficInvestmentPackageActivityQuery;
import com.linzi.pitpat.data.userservice.model.query.UserThirdOauthQuery;
import com.linzi.pitpat.data.userservice.model.vo.AppBaseInfoVo;
import com.linzi.pitpat.data.userservice.model.vo.EventTriggerDto;
import com.linzi.pitpat.data.userservice.service.TrafficInvestmentPackageActivityService;
import com.linzi.pitpat.data.userservice.service.TrafficInvestmentPackageService;
import com.linzi.pitpat.data.userservice.service.UserActiveRuleConfigService;
import com.linzi.pitpat.data.userservice.service.UserDetailService;
import com.linzi.pitpat.data.userservice.service.UserExtraService;
import com.linzi.pitpat.data.userservice.service.UserLevelService;
import com.linzi.pitpat.data.userservice.service.UserTaskDetailService;
import com.linzi.pitpat.data.userservice.service.UserThirdOauthService;
import com.linzi.pitpat.data.userservice.service.ZnsUserEmailSendingRecordService;
import com.linzi.pitpat.data.userservice.service.ZnsUserLoginLogService;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserBizService;
import com.linzi.pitpat.data.userservice.service.vip.VipUserService;
import com.linzi.pitpat.data.util.BeanUtil;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.LoginVO;
import com.linzi.pitpat.data.vo.ThirdLoginVO;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * api 用户注册Manager类
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserRegisterApiManager {

    private final RabbitTemplate rabbitTemplate;
    private final ZnsUserService userService;
    private final RedissonClient redissonClient;
    private final RedisUtil redisUtil;
    private final UserActiveRuleConfigService userActiveRuleConfigService;
    private final QueueMessageService queueMessageService;
    private final ISysConfigService sysConfigService;
    private final ZnsUserEmailSendingRecordService znsUserEmailSendingRecordService;
    private final UserThirdOauthService userThirdOauthService;
    private final UserTaskDetailService userTaskDetailService;
    private final VipUserService vipUserService;
    private final ZnsUserAccountService userAccountService;
    private final ZnsUserLoginLogService znsUserLoginLogService;
    private final TencentImUtil tencentImUtil;
    private final ZnsMessageService znsMessageService;
    private final UserMedalService userMedalService;
    private final AppMessageService appMessageService;
    private final UserRainmakerBizService userRainmakerBizService;
    private final UserTaskBizService userTaskBizService;
    private final UserExtraService userExtraService;
    private final VipUserBizService vipUserBizService;
    private final UserWearsBagService userWearsBagService;
    @Value("${spring.profiles.active}")
    private String profile;
    @Value("${zns.config.rabbitQueue.emailSend}")
    private String mailSend;
    private final ZnsTreadmillService znsTreadmillService;
    private final UserEquipmentShareService userEquipmentShareService;
    private final UserDetailService userDetailService;

    private final VirtualEquipmentBiz virtualEquipmentBiz;
    private final ZnsUserService znsUserService;
    private final TrafficInvestmentPackageService trafficInvestmentPackageService;
    private final UserLevelService userLevelService;
    private final PutChannelUserBizService putChannelUserBizService;
    private final TrafficInvestmentBizService trafficInvestmentBizService;
    private final TrafficInvestmentPackageActivityService trafficInvestmentPackageActivityService;
    /**
     * 发送绑定邮箱验证码锁key
     */
    private final static String SEND_BIND_EMAIL_KEY = "send:bind:email:key:";
    /**
     * 发送绑定邮箱验证码次数key
     */
    private final static String SEND_BIND_EMAIL_TIMES = "send:bind:email:times:";


    /**
     * 绑定邮箱发送验证码
     *
     * @param emailAddress
     */
    public SendVerificationCodeDto sendBindEmailCode(String emailAddress) {
        //获取验证码发送次数
        Integer count = getSendCodeCount(emailAddress);

        //获取锁
        emailAddress = emailAddress.toLowerCase();
        String lockKey = SEND_BIND_EMAIL_KEY + emailAddress;
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            //没有获取到锁，说明邮件已发送
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},没有获取到锁", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.send.success"));
        }
        //发送验证码
        SendVerificationCodeDto result = new SendVerificationCodeDto();
        try {
            //生成验证码
            String code = EnvUtils.isOnline(profile) ? NanoId.randomNanoId(NanoId.DEFAULT_NUMBER, 6) : "888888";
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},code:{}", emailAddress, code);
            // 发送邮件逻辑
            String title = code + " is your PitPat email verification code";
            String content = "Dear user<br/>" +
                    "Welcome to PitPat. Your email is binding for the PitPat application. " +
                    "The verification code for this email is " + code + ", which is valid for 30 minutes. " +
                    "Thank you for your use. If you have any questions, please contact the official customer <NAME_EMAIL>";
            //记录发送邮件
            ZnsUserEmailSendingRecordEntity emailSend = new ZnsUserEmailSendingRecordEntity();
            emailSend.setIsDelete(0);
            emailSend.setEmailAddress(emailAddress);
            emailSend.setEmailAddressEn(emailAddress);
            emailSend.setContent("send bind email code");
            emailSend.setType(EmailSendingRecordTypeEnum.BING_EMAIL.getCode());
            emailSend.setMailTitle(title);
            emailSend.setMailContent(content);
            znsUserEmailSendingRecordService.insert(emailSend);

            MailDto mailDto = new MailDto(emailAddress, content, title, emailSend, EmailType.REGISTER.getType());
            rabbitTemplate.convertAndSend(mailSend, JsonUtil.writeString(mailDto));

            String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.REGISTER_VERIFICATION_CODE_CLOSE_COUNT.getCode());
            if (StringUtils.hasText(config)) {
                Integer sendCount = Integer.valueOf(config);
                result.setVerificationCodeSendCount(sendCount);
            }
            //token缓存（30分钟）
            String tokenKey = ApiConstants.BIND_EMAIL_SEND_CODE_TOKEN + emailAddress;
            redisUtil.set(tokenKey, code, 30L, TimeUnit.MINUTES);
            redisUtil.set(SEND_BIND_EMAIL_TIMES + emailAddress, count + 1, 1L, TimeUnit.DAYS);  //发送次数缓存1天
        } catch (Exception e) {
            log.error("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:" + emailAddress + ",处理异常", e);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        } finally {
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},发送完成", emailAddress);
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return result;
    }

    /**
     * 获取验证码发送次数
     *
     * @param emailAddress
     */
    private Integer getSendCodeCount(String emailAddress) {
        if (!StringUtils.hasText(emailAddress)) {
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},邮箱为空", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.not.exist"));
        }
        emailAddress = emailAddress.toLowerCase();
        if (!PatternUtils.isEmail(emailAddress)) {
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},邮箱格式不正确", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.mailbox.incorrect"));
        }
        ZnsUserEntity userEntity = userService.findByEmail(emailAddress);
        if (userEntity != null) {
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},邮箱已注册", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.register.email.null"));
        }
        //校验发送次数
        Integer count = Optional.ofNullable((Integer) redisUtil.get(SEND_BIND_EMAIL_TIMES + emailAddress)).orElse(0);
        if (count > 10) {
            log.info("UserApiManager#sendBindEmailCode------发送绑定邮箱验证码,emailAddress:{},当日次数超10次", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.sending.times.exceeded"));
        }
        return count;
    }

    /**
     * 绑定邮箱
     *
     * @param req
     */
    public void bindEmail(EmailBindReqDto req, ZnsUserEntity user) {
        Long userId = user.getId();
        String emailAddress = req.getEmailAddress();
        if (!StringUtils.hasText(emailAddress)) {
            log.info("UserApiManager#bindEmail------绑定邮箱,emailAddress:{},userId:{},邮箱为空", emailAddress, userId);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.not.exist"));
        }
        if (Objects.isNull(userId)) {
            log.info("UserApiManager#bindEmail------绑定邮箱,emailAddress:{},userId:{},用户为空", emailAddress, userId);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.not.exist"));
        }
        final String emailAddressLower = emailAddress.toLowerCase();

        //判断邮箱是否存在
        ZnsUserEntity oldUser = userService.findByEmail(emailAddressLower);
        if (oldUser != null) {
            log.info("UserApiManager#bindEmail------绑定邮箱,emailAddress:{},邮箱已注册", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.register.email.null"));
        }

        //校验验证码
        userService.checkVerificationCode(emailAddressLower, req.getVerificationCode(), req.getVerificationCodeSendCount(), ApiConstants.BIND_EMAIL_SEND_CODE_TOKEN);

        //查询用户三方账户
        List<UserThirdOauth> list = userThirdOauthService.findListByUserId(userId);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> item.setThirdEmailAddress(emailAddressLower));
            userThirdOauthService.batchUpdate(list);
        }

        //绑定邮箱
        ZnsUserEntity updateEntity = new ZnsUserEntity();
        updateEntity.setId(userId);
        updateEntity.setEmailAddress(emailAddressLower);
        updateEntity.setEmailAddressEn(emailAddressLower);
        updateEntity.setEmailType(UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code);
        userService.update(updateEntity);


        ZnsUserEntity userEntity = userService.findById(userId);
        if (StringUtils.hasText(userEntity.getCountry()) && UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code.equals(userEntity.getEmailType())) {
            // 补充个人邮箱和国家信息
            userTaskDetailService.completeLevelTask(userId, UserExpObtainSubTypeEnum.COMPLETE_PROFILE.getCode(), false);
            userTaskBizService.completeEvent(new EventTriggerDto().setUser(user).setEventSubType(TaskConstant.TakEventSubTypeEnum.USER_IMPROVE_INFO.getCode()));
        }

        //保存新token
        String oldTokenKey = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
        String oldToken = redisUtil.get(oldTokenKey);
        String newTokenKey = ApiConstants.APP_LOGIN_TOKEN_KEY + updateEntity.getEmailAddressEn();
        redisUtil.set(newTokenKey, oldToken, 365, TimeUnit.DAYS); //token保存一年
        String newUserIdKey = ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + updateEntity.getEmailAddressEn();
        redisUtil.set(newUserIdKey, userId + "", 365, TimeUnit.DAYS);

        //删除老token
        redisUtil.delete(oldTokenKey);
        String oldUserIdKey = ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + user.getEmailAddressEn();
        redisUtil.delete(oldUserIdKey);
        log.info("UserApiManager#bindEmail------绑定邮箱,emailAddress:{},userId:{},完成", emailAddress, userId);
    }

    /**
     * 邮箱用户注册
     *
     * @param req
     * @param appBaseInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginVO emailRegister(UserAppRegisterReqDto req, AppBaseInfoVo appBaseInfo) {
        //注册参数校验
        registerParamCheck(req);

        //获取锁
        String emailAddress = req.getEmailAddress().toLowerCase();
        String lockKey = ApiConstants.REGISTER_LOCK + emailAddress;
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},获取不到锁", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        try {
            CreatUserReqDto reqDto = BeanUtil.copyBean(req, CreatUserReqDto.class);
            reqDto.setEmailType(UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code); //真实邮箱
            //创建用户
            ZnsUserEntity user = registerEmailUser(reqDto, appBaseInfo);
            //发布用户注册事件
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserLoginEvent.getEventType(),new UserLoginEvent(this, user));
            //封装登录信息
            String token = NanoId.randomNanoId();
            LoginVO vo = userService.encapsulateUserData(user.getId(), token, false, true, appBaseInfo.getAppVersion());
            //注册后自动登录
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            redisUtil.set(key, token, 365, TimeUnit.DAYS); //token保存一年
            String user_id_key = ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + user.getEmailAddressEn();
            redisUtil.set(user_id_key, user.getId() + "", 365, TimeUnit.DAYS);
            userTaskBizService.initUserTask(vo.getUserId(), appBaseInfo.getAppVersion());

            if (StringUtils.hasText(req.getEquipmentNo())) {
                userEquipmentShareBanding(req.getEquipmentNo(), user.getId());
            }
            return vo;
        } catch (Exception e) {
            log.error("UserApiManager#emailRegister------邮箱用户注册,emailAddress:" + emailAddress + ",注册异常", e);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 用户分享设备绑定逻辑
     *
     * @param equipmentNo
     * @param userId
     */
    public void userEquipmentShareBanding(String equipmentNo, Long userId) {
        ZnsTreadmillEntity treadmillEntity = znsTreadmillService.findByUniqueCode(equipmentNo);
        if (Objects.nonNull(treadmillEntity)) {
            // 设备人当前身份 被分享者
            List<UserEquipmentShareDo> equipmentShareLists = userEquipmentShareService.findList(new UserEquipmentShareQuery()
                    .setTreadmillId(treadmillEntity.getId()));
            if (!CollectionUtils.isEmpty(equipmentShareLists)) {
                List<Long> userLists = equipmentShareLists.stream().map(UserEquipmentShareDo::getUserId).toList();
                if (!CollectionUtils.isEmpty(userLists) &&
                        !userLists.contains(userId) && userLists.size() <= 10) {
                    UserEquipmentShareDo shareInfo = new UserEquipmentShareDo();
                    shareInfo.setUserId(userId);
                    shareInfo.setTreadmillId(treadmillEntity.getId());
                    shareInfo.setUserType(0);
                    userEquipmentShareService.create(shareInfo);
                }
            }
        }
    }


    /**
     * 注册参数校验
     *
     * @param req
     */
    private void registerParamCheck(UserAppRegisterReqDto req) {
        String emailAddress = req.getEmailAddress();
        if (!StringUtils.hasText(emailAddress)) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},邮箱为空", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.register.email.address"));
        }
        //保证邮箱都是小写
        emailAddress = emailAddress.toLowerCase();
        req.setEmailAddress(emailAddress);
        //判断邮箱是否存在
        ZnsUserEntity user = userService.findByEmail(emailAddress);
        if (user != null) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},邮箱已注册1", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.register.email.null"));
        }
        if (!PatternUtils.isEmail(emailAddress)) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},邮箱格式不正确", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.mailbox.incorrect"));
        }
        if (emailAddress.length() > 100) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},邮箱长度大于100", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.register.limit.max"));
        }
        if (!StringUtils.hasText(req.getPassword())) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},密码是空的", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.enter.password"));
        }
        String codeSwitch = sysConfigService.selectConfigByKey("register.verification.code.switch");
        if (StringUtils.isEmpty(codeSwitch) || "0".equals(codeSwitch)) {
            log.info("UserApiManager#emailRegister------邮箱用户注册,emailAddress:{},验证码未开启", emailAddress);
            return;
        }
        //校验验证码
        userService.checkVerificationCode(req.getEmailAddress(), req.getVerificationCode(), req.getVerificationCodeSendCount(), ApiConstants.REGISTER_CODE_SEND_MAIL_TOKEN_KEY);
    }

    /**
     * 三方注册&login
     *
     * @param req
     * @param appBaseInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ThirdLoginVO thirdRegisterOrLogin(ThirdUserReqDto req, AppBaseInfoVo appBaseInfo) {
        if (StringUtils.hasText(req.getHeadPicUrl()) && req.getHeadPicUrl().length() > 256) {
            //头像url太长，把图片上传到S3,重新赋值
            String picUrl = uploadPicToS3(req.getHeadPicUrl());
            req.setHeadPicUrl(picUrl);
        }
        //获取更新老用户
        ZnsUserEntity user = getOrUpdateOldUser(req, appBaseInfo);
        Integer isNewUser = Objects.isNull(user) ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode();
        log.info("thirdRegisterOrLogin isNewUser:{}", isNewUser);
        //获取锁
        String thirdUniqueId = req.getThirdUniqueId();
        String lockKey = ApiConstants.REGISTER_LOCK + thirdUniqueId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            log.info("UserApiManager#emailRegister------三方注册,thirdUniqueId:{},获取不到锁", thirdUniqueId);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        try {
            //创建新用户
            if (isNewUser.equals(YesNoStatus.YES.getCode())) {
                //三方账户注册用户
                user = registerThirdUser(convertReqDto(req), appBaseInfo);
                //发布用户注册事件
                queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserLoginEvent.getEventType(),new UserLoginEvent(this, user));
            }
            //创建登录信息
            String token = NanoId.randomNanoId();
            LoginVO vo = userService.encapsulateUserData(user.getId(), token, false, true, appBaseInfo.getAppVersion());
            ThirdLoginVO thirdLoginVO = BeanUtil.copyBean(vo, ThirdLoginVO.class);
            thirdLoginVO.setIsNewUser(isNewUser);

            //注册后自动登录
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            redisUtil.set(key, token, 365, TimeUnit.DAYS); //token保存一年
            String user_id_key = ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + user.getEmailAddressEn();
            redisUtil.set(user_id_key, user.getId() + "", 365, TimeUnit.DAYS);
            userTaskBizService.initUserTask(vo.getUserId(), appBaseInfo.getAppVersion());
            return thirdLoginVO;
        } catch (Exception e) {
            log.error("UserApiManager#emailRegister------三方注册,thirdUniqueId:" + thirdUniqueId + ",注册异常", e);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 把图片上传到S3
     *
     * @param headPicUrl
     * @return
     */
    private String uploadPicToS3(String headPicUrl) {
        String yyyyMM = DateUtil.parseDateToStr("yyyyMM", ZonedDateTime.now());
        try {
            // 从网络 URL 获取图片输入流
            URL url = new URL(headPicUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            try (InputStream inputStream = connection.getInputStream()) {
                byte[] imageBytes = inputStream.readAllBytes();
                // 上传对象到 S3
                Map<String, Object> map = AwsUtil.putS3Object(imageBytes, OrderUtil.getUniqueCode() + ".png", yyyyMM);
                String avatar = String.valueOf(map.get("url"));
                log.info("UserApiManager#emailRegister#uploadPicToS3------三方注册,把图片={}上传到S3完成", avatar);
                return avatar;
            }
        } catch (Exception e) {
            log.info("UserApiManager#emailRegister#uploadPicToS3------三方注册,把图片={}上传到S3异常", headPicUrl, e);
            return headPicUrl.substring(0, 1000);
        }
    }

    /**
     * 获取更新 老用户
     *
     * @param req
     * @param appBaseInfo
     * @return
     */
    private ZnsUserEntity getOrUpdateOldUser(ThirdUserReqDto req, AppBaseInfoVo appBaseInfo) {
        ZnsUserEntity user = null;
        //查询三方信息
        UserThirdOauthQuery query = UserThirdOauthQuery.builder().thirdUniqueId(req.getThirdUniqueId()).provider(req.getProvider()).lastStr("limit 1").build();
        UserThirdOauth userThirdOauth = userThirdOauthService.findByQuery(query);
        if (userThirdOauth != null) {
            //用三方id查询用户
            user = userService.findById(userThirdOauth.getUserId());
        } else if (StringUtils.hasText(req.getThirdEmailAddress())) {
            //用邮箱查询用户
            if (!PatternUtils.isEmail(req.getThirdEmailAddress())) {
                log.info("UserApiManager#emailRegister------ 三方注册,emailAddress:{},邮箱格式不正确", req.getThirdEmailAddress());
                throw new BaseException(I18nMsgUtils.getMessage("user.mailbox.incorrect"));
            }
            String emailAddress = req.getThirdEmailAddress().toLowerCase();
            req.setThirdEmailAddress(emailAddress);
            user = userService.findByEmail(emailAddress);
        }

        if (user == null) {
            return null;
        }
        //更新用户三方信息
        updateUserThirdInfo(user, appBaseInfo, req);
        return user;
    }

    /**
     * 转换dto参数
     *
     * @param req
     * @return
     */
    private ThirdCreatUserReqDto convertReqDto(ThirdUserReqDto req) {
        String emailAddress = req.getThirdEmailAddress();
        ThirdCreatUserReqDto reqDto = new ThirdCreatUserReqDto();
        reqDto.setSource(0); //来源: app
        if (StringUtils.hasText(emailAddress)) {
            //真实邮箱
            reqDto.setEmailAddress(emailAddress);
            reqDto.setEmailType(UserConstant.EmailTypeEnum.EMAIL_TYPE_1.code);
        } else {
            //虚拟邮箱
            reqDto.setEmailType(UserConstant.EmailTypeEnum.EMAIL_TYPE_0.code);
        }
        reqDto.setFirstName(req.getNickName());
        reqDto.setProvider(req.getProvider());
        reqDto.setThirdUniqueId(req.getThirdUniqueId());
        reqDto.setHeadPicUrl(req.getHeadPicUrl());
        reqDto.setVirtualDevice(req.getVirtualDevice());
        return reqDto;
    }

    /**
     * 保存用户三方信息
     *
     * @param user
     * @param req
     */
    private void updateUserThirdInfo(ZnsUserEntity user, AppBaseInfoVo appBaseInfoVo, ThirdUserReqDto req) {
        //更新用户
        ZnsUserEntity updateUser = new ZnsUserEntity();
        updateUser.setId(user.getId());
        updateUser.setAppSystem(appBaseInfoVo.getAppType());
        updateUser.setZoneId(appBaseInfoVo.getZoneId());
        updateUser.setZoneOffset(appBaseInfoVo.getZoneOffset());
        updateUser.setLanguageName(appBaseInfoVo.getLanguageName());
        updateUser.setLanguageCode(appBaseInfoVo.getLanguageCode());
        updateUser.setAppVersion(appBaseInfoVo.getAppVersion());
        if (StringUtils.hasText(req.getNickName()) || StringUtils.hasText(req.getHeadPicUrl())) {
            updateUser.setFirstName(req.getNickName());
            updateUser.setHeadPortrait(req.getHeadPicUrl());
        }
        userService.update(updateUser);

        //查询三方信息
        UserThirdOauthQuery query = UserThirdOauthQuery.builder().thirdUniqueId(req.getThirdUniqueId()).provider(req.getProvider()).lastStr("limit 1").build();
        UserThirdOauth one = userThirdOauthService.findByQuery(query);
        if (one != null) {
            return;
        }

        //保存三方信息
        userThirdOauthService.createThirdUserInfo(req.getThirdUniqueId(), req.getProvider(), user);

        //用户发券
        CompletableFuture.runAsync(() -> userActiveRuleConfigService.loginUserActive(user));
    }

    /**
     * 邮箱用户注册
     *
     * @param reqDto
     * @param appBaseInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ZnsUserEntity registerEmailUser(CreatUserReqDto reqDto, AppBaseInfoVo appBaseInfo) {
        String emailAddress = reqDto.getEmailAddress().toLowerCase();
        ZnsUserEntity user = userService.findByEmail(emailAddress);
        if (user != null) {
            log.info("UserRegisterBizService#registerUser------邮箱用户注册,emailAddress:{},邮箱已注册", emailAddress);
            throw new BaseException(I18nMsgUtils.getMessage("user.account.register.email.null"));
        }

        user = userService.findTempUserByUuid(appBaseInfo.getUuid()); //查询临时账号
        if (Objects.nonNull(user)) {
            //临时账号转正
            user = userService.tempUserToRealUser(user, reqDto);
            vipUserBizService.updateMemberUser(user);
        } else {
            //创建用户
            user = userService.createUser(reqDto, appBaseInfo);
            String rainmakerUserId = userRainmakerBizService.getRainMakerUserId(user);
            if (StringUtils.hasText(rainmakerUserId)) {
                //保存rainmaker用户id
                ZnsUserEntity updateUser = new ZnsUserEntity();
                updateUser.setId(user.getId());
                updateUser.setRainmakerUserId(rainmakerUserId);
                userService.update(updateUser);
            }
            //新用户vip处理
            vipUserService.newUserVip(user);
            //创建用户账户
            userAccountService.addUserAccount(user.getId(), appBaseInfo.getAppVersion());
            //保存登录记录
            znsUserLoginLogService.saveLog(user.getId(), 2, reqDto.getSource(), appBaseInfo);//2：注册登录

        }

        //渠道处理
        saveUserChannel(user.getId(), appBaseInfo, reqDto.getVirtualDevice());
        //异步初始化用户数据
        ZnsUserEntity finalUser = user;
        Map<String, String> map = MDC.getCopyOfContextMap();
        CompletableFuture.runAsync(() -> afterRegister(finalUser, map));
        return user;
    }

    public void saveUserChannel(Long userId, AppBaseInfoVo appBaseInfo, String virtualDevice) {
        //原生不处理
        if (Constants.ORGANIC.equals(appBaseInfo.getChannelSource())) {
            log.info("原生不处理");
            return;
        }
        if (appBaseInfo.getAppVersion() == null || appBaseInfo.getAppVersion() < VersionConstant.V4_6_4) {
            log.info("[用户注册][投流包] 版本过低:{}", appBaseInfo.getAppVersion());
            return;
        }
        Optional<TrafficInvestmentPackageDo> packageOp = trafficInvestmentBizService.matchPackage(appBaseInfo.getChannelSource());
        if (packageOp.isEmpty()) {
            log.info("[用户注册][投流包] 没有匹配到渠道信息");
            return;
        }
        TrafficInvestmentPackageActivityDo packageActivity = trafficInvestmentPackageActivityService.findByQuery(new TrafficInvestmentPackageActivityQuery().setPackageId(packageOp.get().getId()).setMaterialToken(appBaseInfo.getMaterialToken()));
        String h5Url = Objects.nonNull(packageActivity) ? packageActivity.getH5Url() : null;
        log.info("[用户注册][投流包] packageId:{}", packageOp.get().getId());
        boolean savedUserChannel = userExtraService.saveUserChannel(new UserExtraDo()
                .setUserId(userId).setChannelCode(appBaseInfo.getChannelSource())
                .setTrafficInvestmentPackageId(packageOp.get().getId())
                .setUuid(appBaseInfo.getUuid()).setMaterialToken(appBaseInfo.getMaterialToken()).setH5Url(h5Url));
        if (savedUserChannel) {
            log.info("[用户注册][渠道][{}],userId:{},virtualDevice:{}", appBaseInfo.getChannelSource(), userId, virtualDevice);
            if (!StringUtils.hasText(virtualDevice)) {
                virtualDevice = "0";
            }
            String virtualDeviceValue = virtualDevice.equals(String.valueOf(DeviceConstant.EquipmentMainTypeEnum.TREADMILL.type)) ?
                    DeviceConstant.EquipmentMainTypeEnum.TREADMILL.code : DeviceConstant.EquipmentMainTypeEnum.BIKE.code;
            virtualEquipmentBiz.sendVirtualEquipment(userId, virtualDeviceValue, appBaseInfo.getAppVersion());
//            log.info("[用户注册]投流用户，跳过新人挑战:{}", userId);
//            znsUserService.completeNewUserFlow(userId);
            log.info("[用户注册][投流用户] 初始化用户等级，发放一级投流奖励");
            userLevelService.findByUserId(userId);
            queueMessageService.sendMessageEvent(MessageSupportedEventTypeEnum.UserLevelChangeEvent.getEventType(),UserLevelChangeEvent.of(userId, 1, 0));
//            log.info("savedUserChannel success,发放会员奖励");

//            String config = sysConfigService.selectConfigByKey(ConfigKeyEnums.CHANNEL_MEMBER_CONFIG.code);
//            ChannelMemberConfigDto channelMemberConfigDto = JsonUtil.readValue(config, ChannelMemberConfigDto.class);
//            if (channelMemberConfigDto != null && channelMemberConfigDto.getChannelSwitch() == 1) {
//                vipUserBizService.addSpecifiedDayMember(userId, channelMemberConfigDto.getMemberDays());
//                if (Objects.nonNull(channelMemberConfigDto.getWearAwardDto())) {
//                    userWearsBagService.sendUserWear(userId, channelMemberConfigDto.getWearAwardDto(), null);
//                }
//            }
        } else {
            log.info("[用户注册][渠道]不符合投流用户要求,userId:{}", userId);
        }
    }

    /**
     * 三方账户注册
     *
     * @param reqDto
     * @param appBaseInfo
     * @return
     */
    public ZnsUserEntity registerThirdUser(ThirdCreatUserReqDto reqDto, AppBaseInfoVo appBaseInfo) {
        log.info("UserRegisterBizService#registerThirdUser------三方账户注册,thirdUniqueId:{}", reqDto.getThirdUniqueId());
        UserThirdOauthQuery query = UserThirdOauthQuery.builder().thirdUniqueId(reqDto.getThirdUniqueId()).provider(reqDto.getProvider()).lastStr("limit 1").build();
        UserThirdOauth userThirdOauth = userThirdOauthService.findByQuery(query);
        if (userThirdOauth != null) {
            log.info("UserRegisterBizService#registerThirdUser------三方账户注册,thirdUniqueId:{},三方账号已注册", reqDto.getThirdUniqueId());
            throw new BaseException(I18nMsgUtils.getMessage("user.account.login"));
        }

        ZnsUserEntity user = userService.findTempUserByUuid(appBaseInfo.getUuid()); //查询临时账号
        if (Objects.nonNull(user)) {
            //临时账号转正
            log.info("registerThirdUser 临时账号转正");
            user = userService.tempUserToRealUser(user, reqDto);
            vipUserBizService.updateMemberUser(user);
        } else {
            //创建用户
            user = userService.createUser(reqDto, appBaseInfo);
            String rainmakerUserId = userRainmakerBizService.getRainMakerUserId(user);
            if (StringUtils.hasText(rainmakerUserId)) {
                //保存rainmaker用户id
                ZnsUserEntity updateUser = new ZnsUserEntity();
                updateUser.setId(user.getId());
                updateUser.setRainmakerUserId(rainmakerUserId);
                userService.update(updateUser);
            }
            //新用户vip处理
            vipUserService.newUserVip(user);
            //创建用户账户
            userAccountService.addUserAccount(user.getId(), appBaseInfo.getAppVersion());
            //保存登录记录
            znsUserLoginLogService.saveLog(user.getId(), 2, 0, appBaseInfo);//2：注册登录

        }
        //渠道处理
        saveUserChannel(user.getId(), appBaseInfo, reqDto.getVirtualDevice());
        //保存用户跟三方账号关系
        userThirdOauthService.createThirdUserInfo(reqDto.getThirdUniqueId(), reqDto.getProvider(), user);

        //异步初始化用户数据
        Map<String, String> map = MDC.getCopyOfContextMap();
        ZnsUserEntity finalUser = user;
        CompletableFuture.runAsync(() -> afterRegister(finalUser, map));
        return user;
    }

    /**
     * 注册成功后初始化其他数据
     *
     * @param user
     */
    private void afterRegister(ZnsUserEntity user, Map<String, String> copyOfContextMap) {
        Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
        log.info("UserRegisterBizService#afterRegister------注册成功后,emailAddress:{},开始", user.getEmailAddressEn());
        //更新im资料库
        tencentImUtil.accountImport(user.getId().toString(), user.getFirstName(), user.getHeadPortrait());
        //新用户注册通知
        znsMessageService.userRegistrationNotification(user);
        // 初始化勋章
        userMedalService.initMedal(user.getId());
        //用户通知
//        appMessageService.registerNotification(user);
        //新用户发去券
        userActiveRuleConfigService.registerActive(user);
        //添加账户明细
        userDetailService.computeIfAbsent(user.getId());
        log.info("UserRegisterBizService#afterRegister------注册成功后,emailAddress:{},结束", user.getEmailAddressEn());
    }

    /**
     * 三方注册没有邮箱的场景 V2
     *
     * @param req
     * @param appBaseInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ThirdLoginVO thirdRegisterAddEmail(ThirdUserNoThirdEmailReqDto req, AppBaseInfoVo appBaseInfo) {
        if (!StringUtils.hasText(req.getVerificationCode())) {
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "verificationCode"));
        }
        //校验验证码
        userService.checkVerificationCode(req.getManualEmail(), req.getVerificationCode(), req.getVerificationCodeSendCount(), ApiConstants.REGISTER_CODE_SEND_MAIL_TOKEN_KEY);

        /* String codeSwitch = sysConfigService.selectConfigByKey("register.verification.code.switch");
        if ("1".equals(codeSwitch)) {
            log.info("[thirdRegisterAddEmail]------三方注册没有邮箱的场景V2,emailAddress:{},验证码已开启", req.getManualEmail());
            if (!StringUtils.hasText(req.getVerificationCode())) {
                throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "verificationCode"));
            }
            //校验验证码
            userService.checkVerificationCode(req.getManualEmail(), req.getVerificationCode(), req.getVerificationCodeSendCount(),ApiConstants.REGISTER_CODE_SEND_MAIL_TOKEN_KEY);
        }else {
            log.info("[thirdRegisterAddEmail]------三方注册没有邮箱的场景V2,emailAddress:{},验证码未开启", req.getManualEmail());
        }*/

        //三方注册登录
        ThirdUserReqDto reqDto = BeanUtil.copyBean(req, ThirdUserReqDto.class);
        reqDto.setThirdEmailAddress(req.getManualEmail());
        return thirdRegisterOrLogin(reqDto, appBaseInfo);
    }

    /**
     * 查询、注册临时账号
     *
     * @param appBaseInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginVO getOrRegisterTempUser(AppBaseInfoVo appBaseInfo) {
        //获取锁
        String uuid = appBaseInfo.getUuid();
        if (!StringUtils.hasText(uuid)) {
            log.info("UserApiManager#getOrRegisterTempUser------查询、注册临时账号,uuid为空");
            throw new BaseException(I18nMsgUtils.getMessage("param.not.exist", "uuid"));
        }
        String lockKey = ApiConstants.REGISTER_LOCK + uuid;
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            log.info("UserApiManager#getOrRegisterTempUser------查询、注册临时账号,uuid:{},获取不到锁", uuid);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        }
        try {
            ZnsUserEntity user = userService.findTempUserByUuid(uuid); //查询临时用户
            if (Objects.isNull(user)) {
                //创建临时用户
                user = createTempUser(appBaseInfo);
            }
            //创建登录信息
            String token = NanoId.randomNanoId();
            LoginVO vo = userService.encapsulateUserData(user.getId(), token, false, true, appBaseInfo.getAppVersion());

            //注册后自动登录
            String key = ApiConstants.APP_LOGIN_TOKEN_KEY + user.getEmailAddressEn();
            redisUtil.set(key, token, 365, TimeUnit.DAYS); //token保存一年
            String user_id_key = ApiConstants.APP_LOGIN_TOKEN_USER_ID_KEY + user.getEmailAddressEn();
            redisUtil.set(user_id_key, user.getId() + "", 365, TimeUnit.DAYS);
            return vo;
        } catch (Exception e) {
            log.error("UserApiManager#getOrRegisterTempUser------查询、注册临时账号,uuid:" + uuid + ",注册异常", e);
            throw new BaseException(I18nMsgUtils.getMessage("common.params.systemBusy"));
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 创建新用户
     *
     * @param appBaseInfo
     * @return
     */
    private ZnsUserEntity createTempUser(AppBaseInfoVo appBaseInfo) {
        //创建用户
        CreatUserReqDto reqDto = new CreatUserReqDto();
        reqDto.setUserType(UserConstant.UserTypeEnum.USER_TYPE_1.code); // 临时用户
        reqDto.setEmailType(UserConstant.EmailTypeEnum.EMAIL_TYPE_0.code); //虚拟邮箱
        reqDto.setSource(0); // 来源：app
        ZnsUserEntity user = userService.createUser(reqDto, appBaseInfo);
        String rainmakerUserId = userRainmakerBizService.getRainMakerUserId(user);
        if (StringUtils.hasText(rainmakerUserId)) {
            //保存rainmaker用户id
            ZnsUserEntity updateUser = new ZnsUserEntity();
            updateUser.setId(user.getId());
            updateUser.setRainmakerUserId(rainmakerUserId);
            userService.update(updateUser);
        }
        //新用户vip处理
        vipUserService.newUserVip(user);
        //创建用户账户
        userAccountService.addUserAccount(user.getId(), appBaseInfo.getAppVersion());
        //保存登录记录
        znsUserLoginLogService.saveLog(user.getId(), 2, 0, appBaseInfo);//2：注册登录
        return user;
    }
}
