package com.linzi.pitpat.api.dto.response.vip;

import com.linzi.pitpat.data.enums.VipPurchaseTypeEnum;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class VipActivationRecordVo {

    //有效期时间
    private ZonedDateTime vipEndTime;
    /**
     * 开通方式 就是订阅商品的id
     *
     * @see VipPurchaseTypeEnum
     */
    private Integer purchaseType;

    //开通时间
    private ZonedDateTime purchaseTime;
    //创建时间
    private ZonedDateTime createTime;
}
