package com.linzi.pitpat.api.activityservice.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.api.BaseAppController;
import com.linzi.pitpat.api.activityservice.dto.request.PropRankedActivityConfigRequestDto;
import com.linzi.pitpat.api.activityservice.dto.request.PropRankedActivityQueryDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropRankedActivityConfigResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelAwardResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelMatchResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelResponseDto;
import com.linzi.pitpat.api.activityservice.dto.response.PropUserRankedLevelStatisticsDto;
import com.linzi.pitpat.api.activityservice.manager.PropRunRankedActivityUserManager;
import com.linzi.pitpat.api.activityservice.manager.PropUserRankedLevelManager;
import com.linzi.pitpat.api.activityservice.vo.PropRunRankedActivityUserDetailVo;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.constant.enums.PropRankedLevelEnums;
import com.linzi.pitpat.data.activityservice.converter.api.UserGamePropOperationLogConverter;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankDataDto;
import com.linzi.pitpat.data.activityservice.dto.PropUserRankedLevelStatisticsVo;
import com.linzi.pitpat.data.activityservice.manager.PropRankActivityManager;
import com.linzi.pitpat.data.activityservice.manager.PropRankedActivityResultManager;
import com.linzi.pitpat.data.activityservice.manager.api.UserGamePropManager;
import com.linzi.pitpat.data.activityservice.model.entity.PropRankedLevel;
import com.linzi.pitpat.data.activityservice.model.entity.PropUserRankedLevel;
import com.linzi.pitpat.data.activityservice.query.PropRankedActivityPageQuery;
import com.linzi.pitpat.data.activityservice.service.PropRankedLevelService;
import com.linzi.pitpat.data.activityservice.service.PropRunRankedActivityUserService;
import com.linzi.pitpat.data.activityservice.service.PropUserRankedLevelService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.lang.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 道具赛接口
 */
@Slf4j
@RestController
@RequestMapping("/app/ack/PropActivity")
@RequiredArgsConstructor
public class PropRankedActivityController extends BaseAppController {

    private final PropUserRankedLevelManager propUserRankedLevelManager;

    private final PropUserRankedLevelService propUserRankedLevelService;

    private final PropRunRankedActivityUserService propRunRankedActivityUserService;

    private final PropRunRankedActivityUserManager propRunRankedActivityUserManager;
    private final PropRankActivityManager propRankActivityManager;
    private final PropRankedActivityResultManager propRankedActivityResultManager;

    private final PropRankedLevelService propRankedLevelService;
    private final UserGamePropOperationLogConverter userGamePropOperationLogConverter;

    private final UserGamePropManager userGamePropManager;

    /**
     * 获取用户段位信息
     *
     * @return Result<UserRankedLevel>
     */
    @PostMapping("/getUserRankedLevel")
    public Result<PropUserRankedLevelResponseDto> getUserRankedLevel(@RequestBody PropRankedActivityQueryDto queryDto) {
        ZnsUserEntity loginUser = getLoginUser();
        if (Objects.isNull(queryDto.getUserId())) {
            queryDto.setUserId(loginUser.getId());
        }
        String languageCode = getLanguageCode();
        PropUserRankedLevelResponseDto responseDto = new PropUserRankedLevelResponseDto();
        PropUserRankedLevel userRankedLevel = propUserRankedLevelManager.findByUserId(queryDto.getUserId(), false);
        responseDto.setIsOpen((Objects.nonNull(userRankedLevel) && userRankedLevel.getRankedLevelId() != 0) ? YesNoStatus.YES.getCode() : YesNoStatus.NO.getCode());
        if (Objects.isNull(userRankedLevel)) {
            userRankedLevel = propUserRankedLevelManager.initUserRankedLevel(loginUser.getId());
        } else if (userRankedLevel.getRankedLevelId() == 0) {
            PropRankedLevel rankedLevel = propRankedLevelService.findByLevelAndRank(PropRankedLevelEnums.AMATEUR_LEVEL_1.getLevel(), PropRankedLevelEnums.AMATEUR_LEVEL_1.getRank());
            //初始化道具赛段位
            userRankedLevel.setRankedLevelId(rankedLevel.getId());
            propUserRankedLevelService.update(userRankedLevel);
        }
        PropUserRankedLevelDto userRankedLevelDto = new PropUserRankedLevelDto();
        BeanUtils.copyProperties(userRankedLevel, userRankedLevelDto);

        boolean updateFlag = false;
        if (Objects.nonNull(userRankedLevel)) {
            if (Objects.equals(queryDto.getShowAward(), Boolean.TRUE) && Objects.equals(userRankedLevel.getIsNewLevel(), Boolean.TRUE)) {
                //仅弹窗一次
                updateFlag = true;

                //获取新道具赛段位拿到的奖励
                PropUserRankedLevelAwardResponseDto userAvailableAward = propRunRankedActivityUserManager.findUserAward(userRankedLevel.getActivityId(), userRankedLevel.getUserId(), languageCode);
                responseDto.setUserRankedLevelAward(userAvailableAward);
            }
            if (Objects.equals(userRankedLevel.getShowAwardPop(), Boolean.TRUE)) {
                updateFlag = true;
                //仅弹窗一次
                responseDto.setShowAwardPop(true);
            }
        }
        if (updateFlag) {
            PropUserRankedLevel updateUserRankedLevel = new PropUserRankedLevel();
            updateUserRankedLevel.setId(userRankedLevel.getId());
            updateUserRankedLevel.setIsNewLevel(false);
            updateUserRankedLevel.setShowAwardPop(false);
            propUserRankedLevelService.update(updateUserRankedLevel);
        }

        responseDto.setUserRankedLevel(userRankedLevelDto);
        responseDto.setIsInPlacement(userRankedLevel.getIsInPlacement());
        responseDto.setSegment(propRunRankedActivityUserService.getCurrentRankSegment(loginUser.getId()));
        return CommonResult.success(responseDto);
    }


    /**
     * 获取道具赛段位赛配置数据
     *
     * @return
     */
    @PostMapping("/getRankedConfig")
    public Result<PropRankedActivityConfigResponseDto> getRankedConfig() {
        PropRankedActivityConfigResponseDto dto = propUserRankedLevelManager.findRankedActivityConfig(getAppVersion(), getLoginUser());
        return CommonResult.success(dto);
    }

    /**
     * 创建道具段位赛并匹配对手可以匹配真实用户
     *
     * @return
     */
    @PostMapping("/match")
    public Result<PropUserRankedLevelMatchResponseDto> matchUser(@RequestBody PropRankedActivityConfigRequestDto dto) {
        //测试超时
        if (dto.getTimeOut() != null) {
            try {
                Thread.sleep(dto.getTimeOut().toInstant().toEpochMilli() - ZonedDateTime.now().toInstant().toEpochMilli());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return null;
        }
        long startTime = System.nanoTime();
        ZnsUserEntity loginUser = getLoginUser();
        dto.setTimeZone(getZoneId());
        dto.setAppVersion(getAppVersion());
        PropUserRankedLevelMatchResponseDto responseDto = propUserRankedLevelManager.matchUser(dto, loginUser, getAppVersion());
        Duration timeTakenToReady = Duration.ofNanos(System.nanoTime() - startTime);
        log.info("UserRankedLevelManager#matchUser-----创建道具段位赛并匹配对手，userId:{},activityId:{},耗时：{}秒", loginUser.getId(), responseDto.getActivityId(), timeTakenToReady.toSeconds());
        return CommonResult.success(responseDto);
    }

    /**
     * 取消活动匹配
     *
     * @return
     */
    @PostMapping("/cancel/match")
    public Result<Boolean> cancelMatchUser() {
        ZnsUserEntity loginUser = getLoginUser();
        propUserRankedLevelManager.cancelMatchUser(loginUser);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 获取当前用户道具段位赛列表（分页）
     *
     * @return
     */
    @PostMapping("/getRankedData")
    public Result<Page<PropUserRankDataDto>> findUserRankedDataPage(@RequestBody PropRankedActivityPageQuery query) {
        ZnsUserEntity loginUser = getLoginUser();
//        RankedActivityPageQuery queryDto = new RankedActivityPageQuery();
        query.setUserId(loginUser.getId());
        Page<PropUserRankDataDto> page = propRankActivityManager.findPage(query, getAppVersion());
        return CommonResult.success(page);
    }

    /**
     * 统计用户当前道具段位赛的信息
     *
     * @return
     */
    @PostMapping("/statisticsUserRankedData")
    public Result<PropUserRankedLevelStatisticsDto> statisticsUserRankedData() {
        ZnsUserEntity loginUser = getLoginUser();

        PropUserRankedLevelStatisticsVo statisticsVo = propRunRankedActivityUserManager.statisticsUserRankedData(loginUser);

        PropUserRankedLevelStatisticsDto response = new PropUserRankedLevelStatisticsDto();
        BeanUtils.copyProperties(statisticsVo, response);

        return CommonResult.success(response);
    }


    /**
     * 获取道具段位赛的活动参数用户活动列表
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/getRankedDataDetail")
    public Result<PropRunRankedActivityUserDetailVo> getRankedDataDetail(@RequestBody PropRankedActivityQueryDto queryDto) {
        ZnsUserEntity loginUser = getLoginUser();
        queryDto.setUserId(loginUser.getId());
        PropRunRankedActivityUserDetailVo detailVo = propRunRankedActivityUserManager.findRankedActivityUserDataDetail(queryDto, loginUser);
        return CommonResult.success(detailVo);
    }


}
