package com.linzi.pitpat.api.equipment.controller.device;


import com.linzi.pitpat.api.dto.request.EquipmentBlueMacRequest;
import com.linzi.pitpat.api.dto.request.TreadmillPrintRequest;
import com.linzi.pitpat.api.dto.request.UpdateRcStatusReqDto;
import com.linzi.pitpat.api.dto.request.UploadingNFCRequest;
import com.linzi.pitpat.api.dto.response.EquipmentBrandResponse;
import com.linzi.pitpat.core.util.ZonedDateTimeUtil;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.equipmentservice.dto.request.DevReportRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.DevStartRequestDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.EquipmentScheduleReqDto;
import com.linzi.pitpat.data.equipmentservice.dto.request.OtaImageDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.DeviceOatDownV2ResponseDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.EquipmentInformRespDto;
import com.linzi.pitpat.data.equipmentservice.dto.response.TreadmillDownLoadRespDto;
import com.linzi.pitpat.data.equipmentservice.enums.TreadmillConstant;
import com.linzi.pitpat.data.equipmentservice.manager.EquipmentManager;
import com.linzi.pitpat.data.equipmentservice.manager.api.OtaV2UpgradeManager;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.TreadmillNfcLogService;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 设备数据管理
 *
 * @description: 跑步机控制器
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@RestController
@Slf4j
@RequestMapping("/device/treadmill")

public class TreadmillController extends BaseDeviceController {

    @Resource
    private TreadmillNfcLogService treadmillNfcLogService;
    @Resource
    private EquipmentManager equipmentManager;
    @Resource
    private OtaV2UpgradeManager otaV2UpgradeManager;


    /**
     * 获取打印编码
     *
     * @param unicode
     * @return
     */
    @Deprecated(since = "4.4.2", forRemoval = true)
    @GetMapping("/getPrintCode")
    public Result getPrintCode(String unicode) {
        if (!StringUtils.hasText(unicode)) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }

        //
        ZnsTreadmillEntity treadmillEntity = treadmillService.findByUniqueCode(unicode);
        if (treadmillEntity != null) {
            //获取整机编码时间戳（yyyyMMddHH）UTC+8
            String yyyyMMddHH = ZonedDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern(ZonedDateTimeUtil.PATTERN_DATETIME_DATE_HOUR));
            log.info("getPrintCode-------yyyyMMddHH:" + yyyyMMddHH);
            String printId = treadmillEntity.getPrintId();
            log.info("getPrintCode-------老printId:" + printId);
            Integer printIdNum = treadmillEntity.getPrintIdNum();
            if (!Objects.equals(printIdNum, 0)) {
                //已有整机编码去掉整机编码
                printId = printId.substring(0, printId.length() - 10);
                log.info("getPrintCode-------截取之后的printId:" + printId);
            }
            //拼接整机编码
            printId = printId + yyyyMMddHH;
            log.info("getPrintCode-------新printId:" + printId);

            //更新整机编码
            ZnsTreadmillEntity updateEntity = new ZnsTreadmillEntity();
            updateEntity.setId(treadmillEntity.getId());
            updateEntity.setPrintId(printId);
            updateEntity.setPrintIdNum(printIdNum + 1); //更新次数加1
            updateEntity.setModifyTime(ZonedDateTime.now());
            treadmillService.update(updateEntity);

            //返回打印编码
            return CommonResult.success(printId);
        }
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), CommonError.BUSINESS_ERROR.getMsg());

    }

    /**
     * 工厂自适应App获取设备（跑步机、走步机等）品牌、型号
     *
     * @param request
     * @return
     */
    @Deprecated(since = "4.4.2", forRemoval = true)
    @PostMapping("/getEquipmentBrandByBlueMac")
    public Result<EquipmentBrandResponse> getEquipmentBrandByBlueMac(@RequestBody EquipmentBlueMacRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getBlueMac())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        EquipmentBrandResponse response = new EquipmentBrandResponse();
        ZnsTreadmillEntity treadmillEntity = treadmillService.selectTreadmillByBluetoothMac(request.getBlueMac());
        if (Objects.nonNull(treadmillEntity)) {
            response.setBrand(treadmillEntity.getBrand());
            response.setProductCode(treadmillEntity.getProductCode());
        }
        return CommonResult.success(response);
    }

    /**
     * 设备NFC记录保存
     *
     * @param request
     * @return
     */
    @PostMapping("/uploadingNFCAndMacInfo")
    public Result uploadingNFCAndMacInfo(@RequestBody UploadingNFCRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getBlueMac())) {
            return CommonResult.fail(CommonError.PARAM_LACK.getCode(), CommonError.PARAM_LACK.getMsg());
        }
        String trimBlueMac = request.getBlueMac().replace(":", "");
        log.info("trim `:` from blueMac,{}->{}", request.getBlueMac(), trimBlueMac);
        boolean res = treadmillNfcLogService.saveLog(request.getBlueMac(), request.getNfcId());
        if (!res) {
            return CommonResult.fail("保存失败");
        }

        return CommonResult.success();
    }

    /**
     * 设备启动固定url（烧录到硬件）
     *
     * @param token
     * @param version 蓝牙固件当前版本
     * @return
     */
    @GetMapping("/start")
    public Result<TreadmillDownLoadRespDto> start(@RequestParam(name = "token", required = false) String token, @RequestParam(name = "version") Integer version, //蓝牙模组版本号
                                                  @RequestParam(name = "wifiMac", required = false) String wifiMac, @RequestParam(name = "blMac") String blMac) {
        ZnsTreadmillEntity treadmillEntity = getTreadmillByWifiMacAndBlMac(wifiMac, blMac);
        if (treadmillEntity == null) {
            //设备未注册
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "设备未注册");
        }

        //设备启动,版本上报
        List<OtaImageDto> otaImageList = List.of(new OtaImageDto(version, TreadmillConstant.OtaImageTypeEnum.BLUETOOTH.code));
        EquipmentInformRespDto req = new EquipmentInformRespDto();
        req.setEquipmentNo(treadmillEntity.getUniqueCode());
        req.setOtaImageList(otaImageList);
        equipmentManager.reportInform(req);

        //设备启动,请求是否升级
        return equipmentFirmwareBiz.treadmillStart(token, version, treadmillEntity);
    }

    /**
     * 设备启动上报版本V2（烧录到硬件）
     *
     * @param req {"token":"11","wifiMac":"WI7844s","blMac":"BL7844s","versions":[{type:256,"ver":26},{type:1025,"ver":10},{type:513,"ver":10}]}
     * @since ota411
     */
    @PostMapping("/startV2")
    public Result<DeviceOatDownV2ResponseDto> startV2(@RequestBody DevStartRequestDto req) {
        ZnsTreadmillEntity treadmillEntity = getTreadmillByWifiMacAndBlMac(req.getWifiMac(), req.getBlMac());
        if (treadmillEntity == null) {
            //设备未注册
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "设备未注册");
        }
        DeviceOatDownV2ResponseDto resp = otaV2UpgradeManager.treadmillStartV2(req, treadmillEntity);
        return CommonResult.success(resp);
    }

    /**
     * 硬件下载ota包固定url（烧录到硬件）
     *
     * @param token
     * @param version 蓝牙固件当前版本
     * @return
     */
    @GetMapping("/getOtaInfo")
    public Result<TreadmillDownLoadRespDto> getOtaInfo(@RequestParam(name = "token", required = false) String token,
                                                       @RequestParam(name = "version") Integer version,
                                                       @RequestParam(name = "wifiMac", required = false) String wifiMac,
                                                       @RequestParam(name = "blMac") String blMac
    ) {
        ZnsTreadmillEntity treadmillEntity = getTreadmillByWifiMacAndBlMac(wifiMac, blMac);
        if (treadmillEntity == null) {
            //设备未注册
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "设备未注册");
        }
        //设备启动,请求是否升级
        return equipmentFirmwareBiz.getOtaInfo(token, version, treadmillEntity);
    }

    /**
     * 上报OTA升级进度V2（烧录到硬件）
     *
     * @param req {"token":"1111qqq","wifiMac":"WI7844s","blMac":"BL7844s","type":256,"ver":26,"state":1,"schedule":1,"failCode":200 }
     * @since ota411
     */
    @PostMapping("/reportScheduleV2")
    public Result<Void> reportScheduleV2(@RequestBody DevReportRequestDto req) {
        ZnsTreadmillEntity treadmillEntity = getTreadmillByWifiMacAndBlMac(req.getWifiMac(), req.getBlMac());
        if (treadmillEntity == null) {
            //设备未注册
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "设备未注册");
        }
        otaV2UpgradeManager.reportScheduleV2(req,treadmillEntity);
        return CommonResult.success();
    }

    /**
     * 上报OTA升级进度固定url（烧录到硬件）
     *
     * @param wifiMac  wifi物理地址
     * @param blMac    蓝牙物流地址
     * @param schedule 完成进度，80%：80
     * @param version  蓝牙固件当前版本 28
     * @param status   升级状态，active：进行中, cancelled：取消, finished：完成, fail：失败
     * @return
     */
    @GetMapping("/reportSchedule")
    public Result<Boolean> reportSchedule(@RequestParam(name = "token", required = false) String token, @RequestParam(name = "wifiMac", required = false) String wifiMac, @RequestParam(name = "blMac") String blMac, @RequestParam(name = "schedule") BigDecimal schedule, @RequestParam(name = "version") Integer version,
                                          //升级类型:1蓝牙，2上控，3下控，4按键板
                                          @RequestParam(name = "type", required = false, defaultValue = "1") Integer type, @RequestParam(name = "status", required = false) String status) {
        log.info("TreadmillController#reportSchedule--------上报OTA升级进度开始,wifiMac:{}，blMac:{}，schedule:{}，version:{}，status:{}，", wifiMac, blMac, schedule, version, status);
        ZnsTreadmillEntity treadmillEntity = getTreadmillByWifiMacAndBlMac(wifiMac, blMac);
        if (treadmillEntity == null) {
            //设备未注册
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "设备未注册");
        }
        //上报OTA升级进度
        EquipmentScheduleReqDto req = new EquipmentScheduleReqDto();
        req.setSchedule(schedule);
        req.setVersion(version);
        req.setStatus(status);
        req.setEquipmentNo(treadmillEntity.getUniqueCode());
        req.setOtaImageType(TreadmillConstant.OtaImageTypeEnum.BLUETOOTH.code);
        req.setEquipmentId(treadmillEntity.getId());
        equipmentManager.reportSchedule(req);
        return CommonResult.success();
    }

    /**
     * 更新设备遥控器状态
     *
     * @param req
     * @return
     */
    @PostMapping("/updateRcStatus")
    public Result updateRcStatus(@RequestBody UpdateRcStatusReqDto req) {
        log.info("TreadmillController#updateRcStatus--------更新设备遥控器状态,blMac:{}，factoryRcStatus:{}，", req.getBlMac(), req.getFactoryRcStatus());
        ZnsTreadmillEntity treadmillEntity = getTreadmillByWifiMacAndBlMac(null, req.getBlMac());
        if (treadmillEntity == null) {
            //设备未注册
            return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "treadmill not register");
        }
        TreadmillConstant.FactoryRcStatusEnum rcStatusEnum = TreadmillConstant.FactoryRcStatusEnum.findByCode(req.getFactoryRcStatus());
        Integer factoryRcStatus = Objects.isNull(rcStatusEnum) ? TreadmillConstant.FactoryRcStatusEnum.RC_STATUS_1.code : rcStatusEnum.code;
        //更新设备遥控器绑定状态
        equipmentManager.updateRcStatusByTreadmillId(treadmillEntity.getId(), factoryRcStatus);
        return CommonResult.success();
    }

    /**
     * 设备编码打印校验
     *
     * @param request
     * @return
     */
    @Deprecated(since = "4.4.2", forRemoval = true)
    @PostMapping("/treadmillPrint")
    public Result treadmillPrint(@RequestBody @Valid TreadmillPrintRequest request) {
        boolean print = equipmentManager.reportPrint(request.getBlueMac(), request.getPrintId());
        if (!print) {
            return CommonResult.fail("打印失败，编号重复打印");
        }
        return CommonResult.success();
    }

    public static void main(String[] args) {
        String bluetoothMac = "58:CF:79:16:6E:6A";
        String trimBlueMac = bluetoothMac.replace(":", "");
        log.info("trim `:` from blueMac,{}->{}", bluetoothMac, trimBlueMac);
    }
}
