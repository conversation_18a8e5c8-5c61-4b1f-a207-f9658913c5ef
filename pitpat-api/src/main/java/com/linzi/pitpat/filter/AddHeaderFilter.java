package com.linzi.pitpat.filter;

import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Optional;

/**
 * 系统语言拦截器，替换语言code
 */
@Component
@Slf4j
public class AddHeaderFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) servletRequest;
        Enumeration<String> headers = req.getHeaders(I18nConstant.LANGUAGE_HEAD_PARAM);
        String rawLanguage = headers.hasMoreElements() ? headers.nextElement() : null;
        String language = Optional.ofNullable(rawLanguage).orElse(I18nConstant.LanguageCodeEnum.en_US.getCode());
        if (language.startsWith("en")) {
            language = I18nConstant.LanguageCodeEnum.en_US.getCode();
        } else if (language.startsWith("fr")) {
            language = I18nConstant.LanguageCodeEnum.fr_CA.getCode();
        } else if (language.startsWith("de")) {
            language = I18nConstant.LanguageCodeEnum.de_DE.getCode();
        } else {
            language = I18nConstant.LanguageCodeEnum.en_US.getCode();
        }
        if(log.isDebugEnabled()){
            log.debug("use [UserContextHolder], AddHeaderFilter#doFilter-------替换语言code, {}=> {}：", rawLanguage, language);
        }
        HeaderMapRequestWrapper requestWrapper = new HeaderMapRequestWrapper(req);
        requestWrapper.addHeader(I18nConstant.LANGUAGE_HEAD_PARAM, language);
        filterChain.doFilter(requestWrapper, servletResponse);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
