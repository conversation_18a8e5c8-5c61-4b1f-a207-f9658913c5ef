package com.linzi.pitpat.filter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class demoTest {
    public static void main(String[] args) {
        demo();
    }

    public static void demo() {
        ProcessBuilder processBuilder = new ProcessBuilder();
        List<String> command = new ArrayList<>();
        command.add("ping");
        command.add("www.baidu.com");
        processBuilder.command(command);

        try {
            Process process = processBuilder.start();
            try (
                    InputStreamReader ir = new InputStreamReader(process.getInputStream(), "GBK");
                    BufferedReader br = new BufferedReader(ir)
            ) {
                String line;
                while ((line = br.readLine()) != null) {
                    System.out.println(line);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
