package com.linzi.pitpat.manager;

import com.linzi.pitpat.data.systemservice.enums.PopRecordConstant;
import com.linzi.pitpat.data.systemservice.model.query.PopRecordQuery;
import com.linzi.pitpat.data.systemservice.service.PopRecordService;
import com.linzi.pitpat.data.userservice.biz.TaskAwardBizService;
import com.linzi.pitpat.data.userservice.enums.TaskConstant;
import com.linzi.pitpat.data.userservice.manager.api.UserTaskManager;
import com.linzi.pitpat.data.userservice.mapper.TaskPackageMapper;
import com.linzi.pitpat.data.userservice.model.entity.TaskPackageDo;
import com.linzi.pitpat.data.userservice.model.entity.UserTaskDo;
import com.linzi.pitpat.data.userservice.model.entity.UserTaskRecordDo;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.model.query.UserTaskQuery;
import com.linzi.pitpat.data.userservice.model.query.UserTaskRecordQuery;
import com.linzi.pitpat.data.userservice.service.UserTaskRecordService;
import com.linzi.pitpat.data.userservice.service.UserTaskService;
import com.linzi.pitpat.data.userservice.service.impl.TaskPackageServiceImpl;
import com.linzi.pitpat.data.vo.home.HomeNewUserTaskStatusVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
class UserTaskManagerTestV2 {

    @Mock
    private UserTaskService userTaskService;
    @Mock
    private UserTaskRecordService userTaskRecordService;
    @Mock
    private PopRecordService popRecordService;
    @Mock
    private TaskAwardBizService taskAwardBizService;
    @Mock
    private RedissonClient redissonClient;
    @Mock
    private RLock rLock;

    @InjectMocks
    private UserTaskManager userTaskManager;

    private ZnsUserEntity newUser;
    private ZnsUserEntity oldUser;
    private final Long taskDetailId = 1001L;
    @ParameterizedTest(name = "Test {index}: {0} + {1} = {2}")
    @CsvSource({
            "2, 3, 5",
            "-1, 1, 0",
            "0, 0, 0"
    })
    void testAdd(int a, int b, int expected) {
        assertEquals(expected, a+b);
    }

    @BeforeEach
    void setup() {
        newUser = new ZnsUserEntity();
        newUser.setId(100249537L);
        newUser.setCreateTime(ZonedDateTime.now()); // 新用户

        oldUser = new ZnsUserEntity();
        oldUser.setId(14L);
        oldUser.setCreateTime(new Date(System.currentTimeMillis() - 86400000 * 16)); // 超过15天
    }

    // 测试用例 1: 旧版本直接返回NONE
    @Test
    void testOldVersion() {
        HomeNewUserTaskStatusVo result = userTaskManager.getUserTaskStatus(
                newUser,
                List.of(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode()),
                40410
        );
        assertEquals(TaskConstant.UserTaskTotalStatusEnum.NONE.getCode(), result.getNewUserTaskStatus());
    }

    // 测试用例 2: 新用户存在未领取奖励
    @Test
    void testNewUserWithUnclaimed() {
        // Mock数据
        when(userTaskService.findList(any(UserTaskQuery.class)))
                .thenReturn(Collections.singletonList(new UserTaskDo()));
        when(userTaskRecordService.findByQuery(any(UserTaskRecordQuery.class)))
                .thenReturn(new UserTaskRecordDo());

        HomeNewUserTaskStatusVo result = userTaskManager.getUserTaskStatus(
                newUser,
                List.of(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode()),
                40411
        );

        assertEquals(TaskConstant.UserTaskTotalStatusEnum.COMPLETED_UNCLAIMED.getCode(), result.getNewUserTaskStatus());
    }

    // 测试用例 3: 新用户存在未开始任务
    @Test
    void testNewUserWithIncomplete() {
        UserTaskDo task = new UserTaskDo();
        task.setStatus(TaskConstant.UserTaskStatus.NOT_STARTED.getCode());

        when(userTaskService.findList(any(UserTaskQuery.class)))
                .thenReturn(Collections.singletonList(task));
        when(userTaskRecordService.findByQuery(any(UserTaskRecordQuery.class)))
                .thenReturn(null);

        HomeNewUserTaskStatusVo result = userTaskManager.getUserTaskStatus(
                newUser,
                List.of(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode()),
                40411
        );

        assertEquals(TaskConstant.UserTaskTotalStatusEnum.INCOMPLETE.getCode(), result.getNewUserTaskStatus());
    }

    // 测试用例 4: 老用户存在未完成任务
    @Test
    void testOldUserWithIncompleteTask() {
        UserTaskDo task = new UserTaskDo();
        task.setStatus(TaskConstant.UserTaskStatus.NOT_STARTED.getCode());

        when(userTaskService.findList(any(UserTaskQuery.class)))
                .thenReturn(Collections.singletonList(task));
        when(popRecordService.findByQuery(any(PopRecordQuery.class)))
                .thenReturn(null);

        HomeNewUserTaskStatusVo result = userTaskManager.getUserTaskStatus(
                oldUser,
                List.of(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode()),
                40411
        );

        assertNotNull(result.getNewUserTaskPop());
        verify(popRecordService).addPop(anyLong(), eq(PopRecordConstant.PopRecordTypeEnum.NEW_USER_TASK_NOT_COMPLETED.getType()), anyInt(), anyLong());
    }

    // 测试用例 5: 老用户存在未领取奖励
    @Test
    void testOldUserWithUnclaimedRewards() {
        when(userTaskService.findList(any(UserTaskQuery.class)))
                .thenReturn(Collections.emptyList());
        when(userTaskRecordService.findByQuery(any(UserTaskRecordQuery.class)))
                .thenReturn(new UserTaskRecordDo());
        when(popRecordService.findByQuery(any(PopRecordQuery.class)))
                .thenReturn(null);

        HomeNewUserTaskStatusVo result = userTaskManager.getUserTaskStatus(
                oldUser,
                List.of(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode()),
                40411
        );

        assertTrue(result.getNewUserTaskPop().contains("not.all.rewarded"));
        verify(popRecordService).addPop(anyLong(), eq(PopRecordConstant.PopRecordTypeEnum.NEW_USER_TASK_NOT_ALL_REWARDED.getType()), anyInt(), anyLong());
    }

    // 测试用例 6: 所有任务已完成
    @Test
    void testAllTasksCompleted() {
        UserTaskDo task = new UserTaskDo();
        task.setStatus(TaskConstant.UserTaskStatus.COMPLETED.getCode());

        when(userTaskService.findList(any(UserTaskQuery.class)))
                .thenReturn(Collections.singletonList(task));
        when(userTaskRecordService.findByQuery(any(UserTaskRecordQuery.class)))
                .thenReturn(null);
        when(popRecordService.findByQuery(any(PopRecordQuery.class)))
                .thenReturn(null);

        HomeNewUserTaskStatusVo result = userTaskManager.getUserTaskStatus(
                oldUser,
                List.of(TaskConstant.TaskSceneTypeEnum.NEW_USER_TASK.getCode()),
                40411
        );

        assertTrue(result.getNewUserTaskPop().contains("all.rewarded"));
        verify(popRecordService).addPop(anyLong(), eq(PopRecordConstant.PopRecordTypeEnum.NEW_USER_TASK_ALL_REWARDED.getType()), anyInt(), anyLong());
    }

    @InjectMocks
    @Spy
    private TaskPackageServiceImpl taskPackageService;

    @Mock
    private TaskPackageMapper taskPackageMapper;
    @Test
    void testQuery() {
        //创建一个假的返回对象
        TaskPackageDo userPO = new TaskPackageDo();
        userPO.setStatus(2);
        //方法打桩：当调用方法mockUserMapper.getByCode("001")时，返回固定值userPO
//        doReturn(userPO).when(taskPackageMapper).selectById(1L);
        //调用要测试的方法
        TaskPackageDo result = taskPackageService.findById(1L);
        //断言方法返回值是否等于100
        assertEquals(2,result.getStatus() );
    }
}
