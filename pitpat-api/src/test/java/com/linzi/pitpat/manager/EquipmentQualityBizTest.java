package com.linzi.pitpat.manager;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.api.model.req.AddEquipmentQualityReq;
import com.linzi.pitpat.data.equipmentservice.biz.EquipmentQualityBiz;
import com.linzi.pitpat.data.equipmentservice.enums.DeviceConstant;
import com.linzi.pitpat.data.equipmentservice.model.entity.ZnsTreadmillEntity;
import com.linzi.pitpat.data.equipmentservice.service.ZnsTreadmillService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEquipmentEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserEquipmentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;

public class EquipmentQualityBizTest extends PitpatApiApplicationTests {

    @Autowired
    private EquipmentQualityBiz equipmentQualityBiz;
    @Autowired
    private ZnsTreadmillService znsTreadmillService;
    @Autowired
    private ZnsUserEquipmentService userEquipmentService;

    @Test
    public void pitpatActivateDeviceTest() {
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillByBluetoothMac("C4A9B881CA66");
        equipmentQualityBiz.pitpatActivateDevice(82954L, znsTreadmillEntity, DeviceConstant.ActivateTypeEnum.APP);
    }

    @Test
    public void erpActivateDeviceTest() {
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillByBluetoothMac("C4A9B881CA66");
        AddEquipmentQualityReq req = new AddEquipmentQualityReq();
        req.setBluetoothMac("C4A9B881CA66");
        req.setDayNum(730);
        req.setCreator("admin");
        req.setAuditer("zl");
        req.setAuditTime(ZonedDateTime.now());
        req.setAuditStatus("finished");
        req.setOrderTime(ZonedDateTime.now());
        req.setBuySource("TikTok");
        equipmentQualityBiz.erpActivateDevice(req, znsTreadmillEntity, ZonedDateTime.now(), ZonedDateTime.now());
    }

    @Test
    public void activateByFirstConnectRecordTest() {
        ZnsTreadmillEntity znsTreadmillEntity = znsTreadmillService.selectTreadmillByBluetoothMac("C4A9B881CA66");
        ZnsUserEquipmentEntity firstConnectRecord = userEquipmentService.findFirstConnectRecord(znsTreadmillEntity.getUniqueCode());
        equipmentQualityBiz.activateByFirstConnectRecord(firstConnectRecord, znsTreadmillEntity, DeviceConstant.ActivateTypeEnum.MANUAL);
    }
}
