package com.linzi.pitpat.manager;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.biz.MainActivityBizService;
import com.linzi.pitpat.data.activityservice.manager.ActivityResultManager;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.vo.ActivityTeamUserRankVo;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.awardservice.model.dto.ActivityTypeDto;
import com.linzi.pitpat.data.constants.TencentImConstant;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.messageservice.service.AppMessageService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class ActivityResultlManagerTest extends PitpatApiApplicationTests {

    @Resource
    private ZnsUserRunDataDetailsService userRunDataDetailsService;
    @Resource
    private ZnsUserService userService;
    @Resource
    private ActivityResultManager activityResultManager;
    @Resource
    private MainActivityBizService mainActivityBizService;
    @Resource
    private AppMessageService appMessageService;
    @Test
    public void testRunEnd() throws Exception {
        ZnsUserRunDataDetailsEntity dataDetails = userRunDataDetailsService.findById(24284912L);
        ZnsUserEntity user = userService.findById(dataDetails.getUserId());
        ActivityTypeDto activityNew = mainActivityBizService.getActivityNew(dataDetails.getActivityId(), user.getZoneId());
        activityResultManager.runEnd(dataDetails,activityNew, true,false, false);
    }

    @Test
    public void teamActivityRank() {
        ArrayList<Long> list = new ArrayList<>();
        list.add(83832L);
        List<ActivityTeamUserRankVo> activityTeamUserRankVos = activityResultManager.teamActivityRank(306336L, list);
        System.out.println(activityTeamUserRankVos);
    }

    @Test
    public void testPush() {
        String message = "free.challenge.activity.ranked.desc.noTop";
        // 发送自定义消息
        ImMessageBo imMessageBo = new ImMessageBo();
        imMessageBo.setJumpType("0");
        imMessageBo.setJumpValue("https://tkjgw.yijiesudai.com/#/free-challenge/" + 40433 + "?isCusNavBar=true&isLightStatusBarStyle=1");
        Map<String, Object> params = new HashMap<>();
        imMessageBo.setParams(params);
        imMessageBo.setMsg(message);
        imMessageBo.setBusinessID("system_notification");
        imMessageBo.setImage("https://pitpat-oss.s3.us-east-2.amazonaws.com/1752464102187.png");
        String jsonStr = JsonUtil.writeString(imMessageBo);
        appMessageService.sendIm("administrator", Collections.singletonList(14l), jsonStr, TencentImConstant.TIM_CUSTOM_ELEM, "", 0, Boolean.FALSE);
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = BigDecimal.valueOf(3600).divide(BigDecimal.valueOf(3121),new MathContext(3,RoundingMode.HALF_UP));
        BigDecimal bigDecimal1 = BigDecimal.valueOf(3600).divide(BigDecimal.valueOf(3121),2, RoundingMode.HALF_UP);
        log.info("{}",bigDecimal);
        log.info("{}",bigDecimal1);
    }
}
