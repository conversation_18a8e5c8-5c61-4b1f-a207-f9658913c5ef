package com.linzi.pitpat.manager;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.data.activityservice.manager.api.RealPersonRunDataDetailsManager;
import com.linzi.pitpat.data.request.RunDataStatisticsRequest;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.data.userservice.service.ZnsUserService;
import com.linzi.pitpat.data.vo.runData.TotalRunRecordVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/5/26
 */
public class RealPersonRunDataDetailsManagerTest extends PitpatApiApplicationTests {
    @Autowired
    private RealPersonRunDataDetailsManager realPersonRunDataDetailsManager;
    @Autowired
    private ZnsUserService userService;

    @Test
    public void testGetRunningRecordV2() {
        RunDataStatisticsRequest request = new RunDataStatisticsRequest();
        request.setStatisticsType(0);
        request.setDeviceType(-1);
        request.setStartTime(DateUtil.addDays(ZonedDateTime.now(), -10));
        request.setEndTime(ZonedDateTime.now());
        ZnsUserEntity loginUser = userService.findById(14l);
        List<TotalRunRecordVo> statistics = realPersonRunDataDetailsManager.findRunningStatistics(request, loginUser, loginUser.getZoneId());
        System.out.println(statistics);
    }

    @Test
    public void getRunningRecordV2() throws ParseException {
        ZnsUserEntity loginUser = userService.findById(14l);
        RunDataStatisticsRequest request = new RunDataStatisticsRequest();
        request.setStatisticsType(0);
        request.setStartTime(DateUtil.addDays(ZonedDateTime.now(), -10));
        request.setEndTime(ZonedDateTime.now());
        Map<String, Object> runningRecordV2 = realPersonRunDataDetailsManager.getRunningRecordV2(request, loginUser, loginUser.getZoneId());
        System.out.println(runningRecordV2);
    }
}
