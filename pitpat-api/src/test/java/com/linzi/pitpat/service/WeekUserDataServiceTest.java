package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.userservice.service.WeekUserDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.ZonedDateTime;

@Slf4j

public class WeekUserDataServiceTest extends PitpatApiApplicationTests {

    @Resource
    private WeekUserDataService weekUserDataService;

    @Test
    public void testInit() {
        weekUserDataService.init(ZonedDateTime.now(), 644L);
    }


}
