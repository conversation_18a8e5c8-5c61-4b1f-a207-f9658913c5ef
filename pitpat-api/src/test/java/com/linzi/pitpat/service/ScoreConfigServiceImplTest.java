package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.awardservice.model.resp.ScoreConfigResp;
import com.linzi.pitpat.data.awardservice.service.ScoreConfigService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.ZonedDateTime;

public class ScoreConfigServiceImplTest extends PitpatApiApplicationTests {

    @Resource
    private ScoreConfigService scoreConfigService;

    @Test
    public void selectScoreConfigByUserId() {
        ScoreConfigResp configResp = scoreConfigService.selectScoreConfigByUserId(1L,"day",505L,ZonedDateTime.now(),ZonedDateTime.now());
        assert configResp != null;
    }
}