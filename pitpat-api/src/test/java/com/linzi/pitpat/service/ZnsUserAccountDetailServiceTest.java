package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;

public class ZnsUserAccountDetailServiceTest extends PitpatApiApplicationTests {

    @Autowired
    private ZnsUserAccountDetailService znsUserAccountDetailService;
    @Test
    public void testUpdateAccountDetail() {
        ZnsUserAccountDetailEntity znsUserAccountDetailEntity = new ZnsUserAccountDetailEntity();
        znsUserAccountDetailEntity.setAuditRemark("test");
        znsUserAccountDetailEntity.setAuditId(505L);
        znsUserAccountDetailEntity.setAuditTime(ZonedDateTime.now());
        znsUserAccountDetailEntity.setAuditStatus(2);
        boolean update = znsUserAccountDetailService.update(znsUserAccountDetailEntity);
        assert !update;
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme