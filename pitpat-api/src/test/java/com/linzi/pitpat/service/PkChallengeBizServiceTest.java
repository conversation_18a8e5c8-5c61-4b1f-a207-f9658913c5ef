package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunOptimalRecordDo;
import com.linzi.pitpat.data.activityservice.model.query.UserRunOptimalRecordQuery;
import com.linzi.pitpat.data.activityservice.service.RealPersonRunDataDetailsService;
import com.linzi.pitpat.data.activityservice.service.UserRunOptimalRecordService;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.systemservice.mapper.SysConfigMapper;
import com.linzi.pitpat.data.systemservice.model.entity.SysConfig;
import com.linzi.pitpat.data.vo.UserSimpleVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/6/27
 */
public class PkChallengeBizServiceTest extends PitpatApiApplicationTests {
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private UserRunOptimalRecordService userRunOptimalRecordService;
    @Autowired
    private RealPersonRunDataDetailsService realPersonRunDataDetailsService;
    @Test
    public void testFindListByUser() {
        long userId = 205l;
        List<Integer> deviceTypeList = List.of(0, 1, 2);
        SysConfig sysConfig = sysConfigMapper.selectByConfigKey(ConfigKeyEnums.CEO_USER_ID.getCode());
        UserRunOptimalRecordDo userSimpleVo = userRunOptimalRecordService.findByQuery(UserRunOptimalRecordQuery.builder().userId(userId).deviceTypeList(deviceTypeList).runMileage(1600).build());
        System.out.println(JsonUtil.writeString(userSimpleVo));
        UserRunOptimalRecordDo ceo = userRunOptimalRecordService.findByQuery(UserRunOptimalRecordQuery.builder().userId(Long.valueOf(sysConfig.getConfigValue())).deviceTypeList(deviceTypeList).runMileage(1600).build());
        System.out.println(JsonUtil.writeString(ceo));
        UserSimpleVo userSimpleVo1 = realPersonRunDataDetailsService.getUserBestPaceByRunMileage(userId, 1600, deviceTypeList);
        System.out.println(JsonUtil.writeString(userSimpleVo1));
        UserSimpleVo ceo1 = realPersonRunDataDetailsService.getUserBestPaceByRunMileage(Long.valueOf(sysConfig.getConfigValue()), 1600, deviceTypeList);
        System.out.println(JsonUtil.writeString(ceo1));
    }
}
