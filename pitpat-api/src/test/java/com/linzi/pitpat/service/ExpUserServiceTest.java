package com.linzi.pitpat.service;


import java.time.ZonedDateTime;
import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.userservice.service.ExpUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ExpUserServiceTest  extends PitpatApiApplicationTests {

    @Autowired
    private ExpUserService expUserService;
    @Test
    public void TestMpUpdate(){
//        ExpUser expUser = new ExpUser();
//        expUser.setGmtModified(ZonedDateTime.now());
//        expUser.setIsPop(1);
//        expUser.setId(110L);

//        expUserService.updateById(expUser);
    }

}
