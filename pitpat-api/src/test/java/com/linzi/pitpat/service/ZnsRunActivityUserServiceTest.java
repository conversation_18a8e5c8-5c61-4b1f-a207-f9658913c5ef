package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityUserDao;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityUserEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/8/9 17:56
 */
public class ZnsRunActivityUserServiceTest extends PitpatApiApplicationTests {
    @Autowired
    private ZnsRunActivityUserDao znsRunActivityUserDao;

    @Test
    public void test() {
//        ZnsRunActivityUserEntity znsRunActivityUserEntity = znsRunActivityUserDao.selectRunActivityUserByNQActivityTypeRankOld(14L, 4, 1, null);
//        System.out.println(znsRunActivityUserEntity);
        ZnsRunActivityUserEntity znsRunActivityUserEntity1 = znsRunActivityUserDao.selectRunActivityUserByNQActivityTypeRank(14L, 4, 1);
        System.out.println(znsRunActivityUserEntity1);
    }
}
