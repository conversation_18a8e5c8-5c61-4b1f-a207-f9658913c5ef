package com.linzi.pitpat.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.awardservice.model.resp.ScoreConfigResp;
import com.linzi.pitpat.data.awardservice.service.ScoreConfigService;
import com.linzi.pitpat.data.userservice.manager.UserBizService;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/6/25
 */
public class UserBizServiceTest extends PitpatApiApplicationTests {
    @Resource
    private UserBizService userBizService;

    @Test
    public void selectScoreConfigByUserId() {
        List<ZnsUserEntity> randomRobotListLimit = userBizService.findRandomRobotListLimit(5);
        System.out.println(JsonUtil.writeString(randomRobotListLimit));
    }
}
