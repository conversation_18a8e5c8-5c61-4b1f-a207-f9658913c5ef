package com.linzi.pitpat.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.data.activityservice.mapper.ActivityTaskConfigDao;
import com.linzi.pitpat.data.activityservice.model.entity.ActivityTaskConfig;
import com.linzi.pitpat.data.activityservice.model.query.ActivityTaskPageQuery;
import com.linzi.pitpat.data.activityservice.service.impl.ActivityTaskConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ActivityTaskConfigServiceImplTest {

    @Mock
    private ActivityTaskConfigDao activityTaskConfigDao;

    @InjectMocks
    private ActivityTaskConfigServiceImpl activityTaskConfigService;

    private ActivityTaskPageQuery pageQuery;
    private Page<ActivityTaskConfig> mockPage;

    @BeforeEach
    void setUp() {
        pageQuery = new ActivityTaskPageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(10);
        mockPage = new Page<>(1, 10);
    }

    @Test
    void findPage_WhenStatusIs1_ShouldSetCorrectTimeConditions() {
        // 准备测试数据
        ZonedDateTime now = ZonedDateTime.now();
        pageQuery.setGmtEndTimeGe(now);
        pageQuery.setGmtStartTimeLe(now);
        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());


        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        log.info(wrapper.getTargetSql());
        assertTrue(wrapper.getTargetSql().contains("`gmt_start_time` <= ?"));
        assertTrue(wrapper.getTargetSql().contains("`gmt_end_time` >= ?"));
    }

    @Test
    void findPage_WhenStatusIs0_ShouldSetCorrectTimeConditions() {
        // 准备测试数据
        ZonedDateTime now = ZonedDateTime.now();
        pageQuery.setGmtStartTimeGt(now);
        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());

        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        assertTrue(wrapper.getTargetSql().contains("`gmt_start_time` > ?"));
    }

    @Test
    void findPage_WhenStatusIs2_ShouldSetCorrectTimeConditions() {
        // 准备测试数据
        ZonedDateTime now = ZonedDateTime.now();
        pageQuery.setGmtEndTimeLt(now);
        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());

        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        assertTrue(wrapper.getTargetSql().contains("`gmt_end_time` < ?"));
    }

    @Test
    void findPage_WhenStatusIsNull_ShouldNotSetTimeConditions() {
        // 准备测试数据

        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());

        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        assertFalse(wrapper.getTargetSql().contains("`gmt_start_time`"));
        assertFalse(wrapper.getTargetSql().contains("`gmt_end_time`"));
    }

    @Test
    void findPage_ShouldAlwaysSetIsDeleteAndOrderBy() {
        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());

        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        assertTrue(wrapper.getTargetSql().contains("`is_delete` = ?"));
        assertTrue(wrapper.getTargetSql().contains("ORDER BY `id` DESC"));
    }

    @Test
    void findPage_WhenStatusIsNull_ShouldNotSetNameConditions() {
        // 准备测试数据

        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());

        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        assertFalse(wrapper.getTargetSql().contains("`task_name`"));
        assertFalse(wrapper.getTargetSql().contains("`template_name_like`"));
    }

    @Test
    void findPage_ShouldAlwaysSetTaskNameAndOrderBy() {
        // 准备测试数据
        pageQuery.setTaskNameLike("name");
        // 执行测试
        activityTaskConfigService.findPage(pageQuery);

        // 验证调用
        ArgumentCaptor<Page<ActivityTaskConfig>> pageCaptor = ArgumentCaptor.forClass(Page.class);
        ArgumentCaptor<QueryWrapper<ActivityTaskConfig>> wrapperCaptor = ArgumentCaptor.forClass(QueryWrapper.class);
        verify(activityTaskConfigDao).selectPage(pageCaptor.capture(), wrapperCaptor.capture());

        // 验证查询条件
        QueryWrapper<ActivityTaskConfig> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);
        assertTrue(wrapper.getTargetSql().contains("`task_name` LIKE ?"));
    }


}
