package com.linzi.pitpat.service;

import com.linzi.pitpat.data.activityservice.mapper.UserRunOptimalRecordMapper;
import com.linzi.pitpat.data.activityservice.model.entity.UserRunOptimalRecordDo;
import com.linzi.pitpat.data.activityservice.model.query.UserRunOptimalRecordPageQuery;
import com.linzi.pitpat.data.activityservice.service.impl.UserRunOptimalRecordServiceImpl;
import com.linzi.pitpat.data.userservice.biz.UserTaskBizService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserRunOptimalRecordServiceImplTest {

    @Mock
    private UserRunOptimalRecordMapper mockUserRunOptimalRecordMapper;
    @Mock
    private UserTaskBizService mockUserTaskBizService;
    @InjectMocks
    private UserRunOptimalRecordServiceImpl userRunOptimalRecordServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        userRunOptimalRecordServiceImplUnderTest = new UserRunOptimalRecordServiceImpl(mockUserRunOptimalRecordMapper,
                mockUserTaskBizService);
    }

    @Test
    void testDeleteByUserId() {
        // Setup
        // Configure UserRunOptimalRecordMapper.selectPaceList(...).
        final UserRunOptimalRecordDo userRunOptimalRecordDo = new UserRunOptimalRecordDo();
        userRunOptimalRecordDo.setId(0L);
        userRunOptimalRecordDo.setUserId(0L);
        userRunOptimalRecordDo.setBestPace(0);
        userRunOptimalRecordDo.setRunDataDetailsId(0L);
        userRunOptimalRecordDo.setIsTest(0);
        userRunOptimalRecordDo.setIsRobot(0);
        userRunOptimalRecordDo.setRunMileage(0);
        userRunOptimalRecordDo.setDeviceType(0);
        userRunOptimalRecordDo.setRunTime(0);
        final List<UserRunOptimalRecordDo> userRunOptimalRecordDos = List.of(userRunOptimalRecordDo);
        when(mockUserRunOptimalRecordMapper.findRandomList(UserRunOptimalRecordPageQuery.builder()
                .userId(0L)
                .build())).thenReturn(userRunOptimalRecordDos);

        // Run the test
        userRunOptimalRecordServiceImplUnderTest.deleteByUserId(0L);
        // Verify the results
        verify(mockUserRunOptimalRecordMapper).deleteByIds(List.of("value"));
    }

}
