package com.linzi.pitpat.util;

import com.linzi.pitpat.core.util.DateUtil;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.TimeZone;

public class TimeZoneTest {


    @Test
    public void testConvert() {
        TimeZone timeZone = TimeZone.getTimeZone("America/Los_Angeles");
        System.out.println(timeZone);

        //System.out.println(ZoneId.getAvailableZoneIds());
        ZonedDateTime now = new Date(1717398000000l);
        now = DateUtil.addMonths1(now, -1);

        ZonedDateTime todayStart = DateUtil.getDate2ByTimeZone(now, timeZone);

        System.out.println(DateUtil.formateDate(now, "yyyy-MM-dd HH:mm:ss"));
        System.out.println("时间戳" + todayStart.toInstant().toEpochMilli());

        System.out.println(DateUtil.formateDate(todayStart, "yyyy-MM-dd HH:mm:ss"));

        ZonedDateTime end = DateUtil.addMonths1(now, 7);
        ZonedDateTime endString = DateUtil.getDate2ByTimeZone(end, timeZone);
        System.out.println(DateUtil.formateDate(endString, "yyyy-MM-dd HH:mm:ss"));
        System.out.println("时间戳" + endString.toInstant().toEpochMilli());

        ZoneId losAngelesZoneId = ZoneId.of("America/Los_Angeles");

        System.out.println(ZonedDateTime.now().withZoneSameInstant(losAngelesZoneId));

        // 获取America/Los_Angeles时区
        //ZoneId losAngelesZoneId = ZoneId.of("America/Los_Angeles");

        // 创建一个指定日期和时间的ZonedDateTime对象
        ZonedDateTime specificDateTimeLosAngeles = ZonedDateTime.now().withZoneSameInstant(losAngelesZoneId);

        // 获取偏移量
        ZoneOffset offset = specificDateTimeLosAngeles.getOffset();

        System.out.println(specificDateTimeLosAngeles);
        // 输出偏移量
        System.out.println("Offset for America/Los_Angeles on 2024-08-07 at 12:00: " + offset);

        // 如果需要毫秒偏移量，可以转换
        long offsetMillis = offset.getTotalSeconds() * 1000L;
        System.out.println("Offset in milliseconds: " + offsetMillis);

        //ZoneId losAngelesZoneId = ZoneId.of("America/Los_Angeles");

        // 夏令时开始时间
        LocalDate startDay = LocalDate.of(2024, 6, 10);
        LocalTime startTime = LocalTime.of(2, 0); // 实际上是2:00 AM，但因为时钟跳过这一小时，所以这里表示的是1:59 AM之后的瞬间
        ZonedDateTime startDateTime = ZonedDateTime.of(startDay, startTime, losAngelesZoneId);
        System.out.println("Start of Daylight Saving Time: " + startDateTime + startDateTime.getOffset());
        System.out.println(startDay);
        // 夏令时结束时间
        LocalDate endDay = LocalDate.of(2024, 11, 10);
        LocalTime endTime = LocalTime.of(2, 0); // 实际上是2:00 AM，但由于时钟回拨，这里表示的是3:00 AM
        ZonedDateTime endDateTime = ZonedDateTime.of(endDay, endTime, losAngelesZoneId);
        System.out.println("End of Daylight Saving Time: " + endDateTime + endDateTime.getOffset());
    }

}
