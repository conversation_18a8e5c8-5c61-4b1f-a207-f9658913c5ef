package com.linzi.pitpat.util;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.data.activityservice.manager.RankedActivityResultManager;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.constants.CacheConstants;
import com.linzi.pitpat.data.constants.RedisConstants;
import com.linzi.pitpat.data.entity.po.DataCacheSwitch;
import com.linzi.pitpat.framework.redis.util.lock.LockHolder;
import com.mongodb.assertions.Assertions;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
public class RedissonClientTest extends PitpatApiApplicationTests {

    @Autowired
    private RankedActivityResultManager rankedActivityResultManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ZnsUserAccountDetailService userAccountDetailService;

    @Test
    public void testA() throws InterruptedException {
        Supplier<Integer> supplier = () -> {
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return null;
        };
        Supplier<Integer> supplier1 = () -> {
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return null;
        };

        Supplier<Integer> supplier2 = () -> {
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return null;
        };
        TimeUnit.MINUTES.sleep(5);
    }

    @Test
    public void testClient() {
        RBucket<ZonedDateTime> demo = redissonClient.getBucket("demo");
        if (!demo.isExists() || Objects.isNull(demo.get())) {
            System.out.printf("数据为空");
        }
        System.out.println(demo.get());
        demo.set(ZonedDateTime.now());
        demo.expire(10, TimeUnit.SECONDS);
        System.out.println(demo.get());
    }

    @Test
    public void testFistTimeCharge() {
        System.out.println(userAccountDetailService.getFirstTime(2L));
    }


    @Test
    public void testBucket() {
        // 如果设置为0，则系统没有缓存
        RBucket<DataCacheSwitch> bucket = redissonClient.getBucket(CacheConstants.redis_cache_key_switch);
        DataCacheSwitch cacheSwitch = new DataCacheSwitch(DateUtil.addDays(ZonedDateTime.now(), 1), 0);

        bucket.set(cacheSwitch, 1, TimeUnit.DAYS);
        Assertions.assertNotNull(bucket.get());
        log.info("cachedSwitch={}", bucket.get());

        bucket.delete();
        Assertions.assertNull(bucket.get());
        log.info("cachedSwitch={}", bucket.get());

        bucket.set(cacheSwitch, 1, TimeUnit.DAYS);
        DataCacheSwitch cachedSwitch = bucket.get();
        if (cachedSwitch == null) {
            cachedSwitch = new DataCacheSwitch(ZonedDateTime.now(), 1);    //设置系统有缓存
        }
        org.junit.jupiter.api.Assertions.assertEquals(cacheSwitch, cachedSwitch);
        log.info("cachedSwitch={}", cachedSwitch);
    }


    @Test
    public void test(){
        RSet<Object> set = redissonClient.getSet(NanoId.randomNanoId());

        log.info("set={},{}",set, set.isEmpty());
        set.add(1);
    }

    @Test
    public void testRedisConsistency() throws Exception {
        String demoKey = "ly_demo_key";
        redisTemplate.opsForValue().set(demoKey, "hello,world");

        Object object = redisTemplate.opsForValue().get(demoKey);
        log.info("object={}", object);
        //redisTemplate.delete(demoKey);

        RBucket<Object> bucket = redissonClient.getBucket(demoKey);
        Object response = bucket.get();
        log.info("response={}", response);

        bucket.set("world, hello");

        log.info("response1={}", bucket.get());
    }

    @Test
    public void testLock() throws InterruptedException {
        RLock lock = redissonClient.getLock(NanoId.randomNanoId());
        LockHolder.tryLock(lock, 5, 60,  () -> {
            log.info("lock={}", lock.isHeldByCurrentThread());
        });

        TimeUnit.SECONDS.sleep(10);
    }

    @Test
    public void testCounter(){
        RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.USER_RUN_DATA_COUNT+1);
        count();

        atomicLong.getAndIncrement();

        atomicLong.getAndIncrement();
        count();

        atomicLong.getAndIncrement();
        count();
        atomicLong.delete();
    }

    private long count() {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.USER_RUN_DATA_COUNT+1);
        long count = atomicLong.get();
        if (count <= 10) {
            count =10;
            atomicLong.set(count);
            atomicLong.expire(1, TimeUnit.HOURS);
        }
        log.info("{}", count);
        return count;
    }
}
