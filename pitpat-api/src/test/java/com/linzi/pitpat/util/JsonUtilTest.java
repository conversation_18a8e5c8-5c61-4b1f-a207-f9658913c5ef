package com.linzi.pitpat.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.PitpatApiApplication;
import com.linzi.pitpat.constants.ApiConstants;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.data.robotservice.model.entity.DelayTimeSetting;
import com.linzi.pitpat.data.systemservice.model.vo.ContactUsListVO;
import com.linzi.pitpat.data.vo.im.BlacklistResultDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class JsonUtilTest extends PitpatApiApplication {

    @Test
    public void testWriteJson() {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("msgtype", "text");
        contentMap.put("text", Map.of("content", "content"));

        String jsonString = JsonUtil.writeString(contentMap);
        log.info("{}", jsonString);

        Map<String, Object> map = JsonUtil.readValue(jsonString);
        assertEquals(contentMap.get("msgType"), map.get("msgType"));
    }


    @Test
    public void testReadMap() {
        String str = "{\"rule\":[{\"a\":2},{\"a\":2,\"b\":3}]}";
        Map<String, Object> map = JsonUtil.readValue(str);

        Map<String, Object> mapObject = JsonUtil.readValue(map);
        assertEquals(map.size(), mapObject.size());

        List<Map<String, Long>> list = JsonUtil.readValue(map.get("rule"), new TypeReference<List<Map<String, Long>>>() {
        });
        log.info("{}", list);
        log.info("{}", list.get(0).get("a"));

        assertEquals(list.get(0).get("a"), 2L);
    }

    @Test
    public void testReadList() {
        String str = "[1456858,1456860,1456862]";
        List<Integer> list = JsonUtil.readList(str, Integer.class);
        List<Long> areaIds = JsonUtil.readValue(str, new TypeReference<List<Long>>() {
        });

        List<Long> areaIdsObject = JsonUtil.readList(areaIds, Long.class);
        log.info("{}", list);
        Assertions.assertEquals(areaIdsObject, areaIds);
        Assertions.assertNotEquals(list, areaIdsObject);

        List<Map<String, Object>> list5 = JsonUtil.readValue("[{\"id\":9,\"name\":\"Duration\",\"values\":[\"30 Days\"]}]", new TypeReference<List<Map<String, Object>>>() {
        });

        list5.forEach(map -> map.keySet().forEach(item -> {
            log.info("key={},val={}", item, map.get(item));
        }));
    }

    @Test
    public void testMapToJson() {
        Map<String, Object> map = new HashMap<>();
        map.put("activityId", 123L);
        map.put("createTime", ZonedDateTime.now());
        map.put("activityTitle", "春季健康跑");
        map.put("activityStartTime", ZonedDateTime.now());
        map.put("activityEndTime", ZonedDateTime.now());
        map.put("completeRuleType", 1);
        map.put("runMileage", new BigDecimal("5000"));
        map.put("bonusRuleType", 2);

        log.info("{}", JsonUtil.writeString(map));
        log.info("{}", map);
    }


    @Test
    public void testReadValueWithString() {
        String json = "{\"name\":\"John\", \"age\":30}";
        Person person = JsonUtil.readValue(json, Person.class);
        assertNotNull(person);
        assertEquals("John", person.getName());
        assertEquals(30, person.getAge());
    }

    @Test
    public void testReadValueWithObject() {
        Person person = new Person("John", 30);
        String json = JsonUtil.writeString(person);
        Person parsedPerson = JsonUtil.readValue(json, Person.class);
        assertNotNull(parsedPerson);
        assertEquals("John", parsedPerson.getName());
        assertEquals(30, parsedPerson.getAge());
    }

    @Test
    public void testReadValueWithClass() {
        String json = "{\"name\":\"John\", \"age\":30}";
        Person person = JsonUtil.readValue(json, Person.class);
        assertNotNull(person);
        assertEquals("John", person.getName());
        assertEquals(30, person.getAge());
    }

    @Test
    public void testReadValueListWithTypeReference() {
        String json = "[{\"name\":\"John\", \"age\":30},{\"name\":\"John\", \"age\":30},{\"name\":\"John\", \"age\":30},{\"name\":\"John\", \"age\":30}]";
        List<Person> personList = JsonUtil.readList(json, Person.class);

        List<Person> personList2 = JsonUtil.readValue(json, new TypeReference<List<Person>>() {
        });

        log.info("class array,{}", personList);

        assertNotNull(personList);
        assertEquals(personList, personList2);

        assertEquals(4, personList.size());
        assertEquals("John", personList.get(0).getName());
        assertEquals(30, personList.get(0).getAge());
    }

    @Test
    public void testWriteString() {
        Person person = new Person("John", 30);
        String json = JsonUtil.writeString(person);
        assertNotNull(json);
        assertEquals("{\"name\":\"John\",\"age\":30}", json);
    }

    @Test
    public void testWriteBytes() {
        Person person = new Person("John", 30);
        byte[] json = JsonUtil.writeBytes(person);
        assertNotNull(json);
        String jsonString = new String(json);
        assertEquals("{\"name\":\"John\",\"age\":30}", jsonString);
    }

    @Test
    public void testWriteListClass() {
        String json = "{\"channelList\":[{\"photo\":\"https://example.com/channel1_photo.jpg\",\"order\":1,\"type\":0,\"title\":\"联系我们\",\"buttonDes\":\"点击聊天\",\"refValue\":\"https://example.com/chat\",\"userId\":\"U123456789\",\"firstName\":\"张三\"},{\"photo\":\"https://example.com/channel2_photo.jpg\",\"order\":2,\"type\":1,\"title\":\"客户服务邮箱\",\"buttonDes\":\"发送邮件\",\"refValue\":\"<EMAIL>\",\"userId\":\"U987654321\",\"firstName\":\"李四\"},{\"photo\":\"https://example.com/channel3_photo.png\",\"order\":3,\"type\":2,\"title\":\"24小时热线\",\"buttonDes\":\"立即拨打\",\"refValue\":\"************\",\"userId\":\"U111222333\",\"firstName\":\"王五\"}]} ";
        String jsonItem = """
                 [{
                      "photo": "https://example.com/channel1_photo.jpg",
                      "order": 1,
                      "type": 0,
                      "title": "联系我们",
                      "buttonDes": "点击聊天",
                      "refValue": "https://example.com/chat",
                      "userId": "U123456789",
                      "firstName": "张三"
                    },
                    {
                      "photo": "https://example.com/channel2_photo.jpg",
                      "order": 2,
                      "type": 1,
                      "title": "客户服务邮箱",
                      "buttonDes": "发送邮件",
                      "refValue": "<EMAIL>",
                      "userId": "U987654321",
                      "firstName": "李四"
                    },
                    {
                      "photo": "https://example.com/channel3_photo.png",
                      "order": 3,
                      "type": 2,
                      "title": "24小时热线",
                      "buttonDes": "立即拨打",
                      "refValue": "************",
                      "userId": "U111222333",
                      "firstName": "王五"
                    }]
                """;
        ContactUsListVO contactUsListVO = JsonUtil.readValue(json, ContactUsListVO.class);
        List<ContactUsListVO.Channel> channels = JsonUtil.readList(jsonItem, ContactUsListVO.Channel.class);
        channels.forEach(item -> {
            log.info("item={}", item);
        });
        log.info("item={}", contactUsListVO);

    }

    @Test
    public void testNullValue() {
        String json = """
                {
                  "booleanValue": null,
                  "byteValue": null,
                  "shortValue": null,
                  "intValue": null,
                  "longValue": "",
                  "floatValue": null,
                  "doubleValue": null,
                  "charValue": null,
                  "stringValue": "Hello, World!",
                  "nullValue": []
                }
                """;

        Map<String, Object> map = JsonUtil.readValue(json);

        Map<String, Object> map1 = JsonUtil.readValue(json, new TypeReference<Map<String, Object>>() {
        });

        Map<String, Object> map2 = JsonUtil.readValue(map1, new TypeReference<>() {
        });

        assertNotEquals(map1.size(), map2.size());

        log.info("map={}, {}", map, StringUtil.isEmpty(String.valueOf(map.get("charValue"))));

        Map<String, Object> data = new HashMap<String, Object>();
        data.put("string", null);
        data.put("array", new String[]{});
        data.put("map", Map.of());
        log.info("{}", JsonUtil.writeString(data));
    }

    @Test
    public void testjsonWrite() throws JsonProcessingException {
        String json = "{\"content\":\"PK competition is in progress! Come and clock in! Win $30 for finishing 7 levels!\",\"userIds\":\"2,205\",\"title\":\"PK competition is coming!\",\"activityContent\":\"I find a fun activity「Stage Activities」\",\"imageUrl\":\"https://pitpat-oss.s3.us-east-2.amazonaws.com/headPortrait/iJW10J5umDXA0291.jpg\",\"h5Url\":\"https://tkjh52.ldxinyong.com/#/newPeople\"}";

        String arrayJson1 = "[[1,12],[1,13],[1,14],[2,15],[2,16],[2,17],[3,18],[3,19],[3,20],[4,21],[4,22],[5,23],[5,24],[5,25],[5,42],[6,26],[6,27],[6,28],[6,29],[6,30],[7,31],"
                + "[7,32],[7,33],[7,34],[7,35],[7,36],[8,37],[8,38],[9,39],[10,40],[11,41]]";

        String arrayJson2 = "[[1,12],[1,13],[1,14],[2,15],[2,16],[2,17],[3,18],[3,19],[3,20],[4,21],[4,22],[5,23],[5,24],[5,25],[5,42],[6,26],[6,27],[6,28],[6,29],[6,30],[7,31],"
                + "[7,32],[7,33],[7,34],[7,35],[7,36],[8,37],[8,38],[9,39],[10,40],[11,41]]";

        String arrayJson = """
                [[1,12],[1,13],[1,14],[2,15],[2,16],[2,17],[3,18],[3,19],[3,20],[4,21],[4,22],[5,23],[5,24],[5,25],[5,42],[6,26],[6,27],[6,28],[6,29],[6,30],[7,31],[7,32],[7,33],[7,34],[7,35],[7,36],[8,37],[8,38],[9,39],[10,40],[11,41]]
                """;

        Map<String, Object> map = JsonUtil.readValue(json);
        log.info("{}", map);

        List<List> lists1 = JsonUtil.readList(arrayJson1, List.class);
        List<List<Long>> lists = JsonUtil.readValue(arrayJson
                , new TypeReference<List<List<Long>>>() {
                });
        log.info("{}", JsonUtil.writeString(lists));

        Assertions.assertEquals(lists.size(), lists1.size());
    }


    @Test
    public void testParseLong() {
        String json = "[1,2,3,4,5,6,7]";

        //正常
        List<Long> userIds = JsonUtil.readList(json, Long.class);

        List<Long> userIds2 = JsonUtil.readValue(json, new TypeReference<List<Long>>() {
        });

        log.info("{}", userIds);
        Assertions.assertEquals(userIds, userIds2);
    }

    @Test
    void testParsMap() {
        String json = """
                {
                  "stringData": "Hello, World!",
                  "intData": 42,
                  "doubleData": 3.14,
                  "boolData": true,
                  "charData": "A",
                  "stringList": ["Item1", "Item2", "Item3"],
                  "stringIntMap": {
                    "Key1": 100,
                    "Key2": 200
                  },
                  "dateData": "2024-05-10"
                }
                """;

        //接受值并不会按照预期返回 <String,String>而是 <String,Object>
        Map<String, String> stringMap = JsonUtil.readValue(json);

        //正常
        Map<String, Object> objectMap = JsonUtil.readValue(json);
        List<String> stringList = JsonUtil.readList(objectMap.get("stringList"), String.class);
        Map<String, Integer> stringIntMap = JsonUtil.readValue(objectMap.get("stringIntMap")); //默认Integer 等同于一下
//        Map<String, Integer> stringIntMap = JsonUtil.readValue(objectMap.get("stringIntMap"), new TypeReference<Map<String, Integer>>() {
//        }); //默认Integer
        assertEquals(stringList.size(), 3);
        assertEquals(stringIntMap.size(), 2);

        //这种情况下会报错，因为  stringList 和 stringIntMap 无法转为String
        //Map<String, String> errorMap = JsonUtil.readValue(json, new TypeReference<Map<String, String>>() {
        //});

        log.info("{}", objectMap);
    }

    @Test
    void testParsListMap() {
        String json = """
                [ {
                   "stringData": "Hello, World!",
                   "intData": 42,
                   "doubleData": 3.14,
                   "boolData": true,
                   "charData": "A",
                   "stringList": ["Item1", "Item2", "Item3"],
                   "stringIntMap": {
                     "Key1": 100,
                     "Key2": 200
                   },
                   "dateData": "2024-05-10"
                 }, {
                   "stringData": "Hello, World!",
                   "intData": 42,
                   "doubleData": 3.14,
                   "boolData": true,
                   "charData": "A",
                   "stringList": ["Item1", "Item2", "Item3"],
                   "stringIntMap": {
                     "Key1": 100,
                     "Key2": 200
                   },
                   "dateData": "2024-05-10"
                 }]
                """;

        //正常,实际上这里泛型，并未生效，只是推断出来的默认类型
        List<Map<String, Object>> objectMap = JsonUtil.readValue(json, new TypeReference<List<Map<String, Object>>>() {
        });

        //错误，类型不匹配
        //List<Map<String,Object>> ListObject =JsonUtil.readList(json, Map.class);
        //正常，不指定泛型类型，
        List<Map> ListObject = JsonUtil.readList(json, Map.class);

        //正常
        List<Map<String, Object>> list = JsonUtil.readValue(json, new TypeReference<>() {
        });

        List<String> stringList = JsonUtil.readList(objectMap.get(0).get("stringList"), String.class);
        Map<String, Integer> stringIntMap = JsonUtil.readValue(objectMap.get(0).get("stringIntMap")); //默认Integer 等同于一下
        //Map<String, Integer> stringIntMap = JsonUtil.readValue(objectMap.get(0).get("stringIntMap"), new TypeReference<Map<String, Integer>>() {
        //}); //默认Integer
        assertEquals(stringList.size(), 3);
        assertEquals(stringIntMap.size(), 2);

        //这种情况下会报错，因为  stringList 和 stringIntMap 无法转为String
        //List<Map<String, String>> errorMap = JsonUtil.readValue(json, new TypeReference<List<Map<String, String>>>() {
        //});

        log.info("{}", objectMap);
    }

    @Test
    public void testArrayJson() {
        String json = """
                [{"max":259200000,"min":10800000,"type":"login"},{"max":0,"min":0,"type":"achieveMileage"},{"max":300000,"min":0,"type":"firstRun"},{"max":300000,"min":0,"type":"firstReport"},{"max":259200000,"min":0,"type":"rotFollowBack"},{"max":86400000,"min":1800000,"type":"delayRotReport"},{"max":86400000,"min":1800000,"type":"teamRunRotReceive"}]
                """;

        //分为两步解析为Class
        List<Map<String, Object>> list1 = JsonUtil.readValue(json, new TypeReference<List<Map<String, Object>>>() {
        });
        List<DelayTimeSetting> list2 = list1.stream().map(k -> JsonUtil.readValue(k, DelayTimeSetting.class)).toList();

        //一步解析为Class
        List<DelayTimeSetting> list = JsonUtil.readList(json, DelayTimeSetting.class);
        DelayTimeSetting delayTimeSetting = list.stream()
                .filter(k -> k.getType().equals("teamRunRotReceive")).findFirst().get();

        log.info("{}", delayTimeSetting);
    }

    @Test
    public void testMapJson() {
        String json = """
                {"routeThumbnail":"https://pitpat-oss.s3.us-east-2.amazonaws.com/202311/iVK13D0X9Qw76125.png","globalRouteMap":"https://pitpat-oss.s3.us-east-2.amazonaws.com/202311/iVK13D0X9Qw76125.png","routeMap":["https://pitpat-oss.s3.us-east-2.amazonaws.com/202311/iVK13D0X9Qw76125.png","https://pitpat-oss.s3.us-east-2.amazonaws.com/202310/iro13BcvjBYa4930.png"],"oneSentenceDesc":"The beach is located on the east side of the Central City. It has a beautiful coastal scenery.","oneParagraphDesc":"The beach is located on the east side of the Central City. It has a beautiful coastal scenery."}
                """;
        Map<String, Object> routeConfig = JsonUtil.readValue(json);
        if (null != routeConfig) {
            List<String> routeMapList = JsonUtil.readList(routeConfig.get(ApiConstants.ROUTE_MAP), String.class);
            if (null != routeMapList && routeMapList.size() > 0) {
                // 路线宣传图
                log.info("{}", routeMapList);
            }
        }
    }

    @Test
    public void testClassJson() {
        String json = """
                {"ResultItem":[{"To_Account":"95477","ResultCode":30001,"ResultInfo":"Err_SNS_BlackListDelete_Not_BlackList"}],"Fail_Account":["95477"],"ActionStatus":"OK","ErrorCode":0,"ErrorInfo":"","ErrorDisplay":""}
                """;
        BlacklistResultDto blacklistResultDto = JsonUtil.readValue(json, BlacklistResultDto.class);
        log.info("{}", blacklistResultDto);
        new BlacklistResultDto.Item();
    }

    @Test
    public void testDates() {
        testDate();
        testLocalDate();
        testZonedDate();
    }

    private void testDate() {
        val string = JsonUtil.writeString(ZonedDateTime.now());
        log.info("string={}", string);
        val zonedDateTime = JsonUtil.readValue(string, ZonedDateTime.class);
        log.info("date={}", zonedDateTime);

    }

    public void testLocalDate() {
        val string = JsonUtil.writeString(ZonedDateTime.now());
        log.info("string={}", string);
        ZonedDateTime zonedDateTime = JsonUtil.readValue(string, ZonedDateTime.class);
        log.info("ZonedDateTime={}", zonedDateTime);

        zonedDateTime = JsonUtil.readValue("\"2024-06-27 10:07:51\"", ZonedDateTime.class);
        log.info("ZonedDateTime={}", zonedDateTime);
    }


    public void testZonedDate() {
        val string = JsonUtil.writeString(ZonedDateTime.now());
        log.info("string={}", string);
        val zonedDateTime = JsonUtil.readValue(string, ZonedDateTime.class);
        log.info("zonedDateTime={}", zonedDateTime);
    }


}

@Data
@AllArgsConstructor
@NoArgsConstructor
class Person {

    private String name;
    private int age;
}
