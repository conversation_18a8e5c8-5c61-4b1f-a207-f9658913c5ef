package com.linzi.pitpat.util;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.datetype.ZonedDateTimeDeserializer;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.mapper.CompetitiveSeasonMapper;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityUserDao;
import com.linzi.pitpat.data.dao.exchangeRate.ExchangeRateConfigHistoryDao;
import com.linzi.pitpat.data.entity.exchangeRate.ExchangeRateConfigHistoryEntity;
import com.linzi.pitpat.data.userservice.mapper.label.LabelUserLogDao;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
public class ZonedDateTimeDeserializerTest  extends PitpatApiApplicationTests {

    @Autowired
    private ExchangeRateConfigHistoryDao exchangeRateConfigHistoryDao;
    @Autowired
    private LabelUserLogDao labelUserLogDao;
    @Autowired
    private ZnsRunActivityUserDao znsRunActivityUserDao;

    @Autowired
    private CompetitiveSeasonMapper competitiveSeasonMapper;


    private final List<DateTimeFormatter> list = List.of(DateTimeFormatter.ISO_INSTANT, DateTimeFormatter.ISO_OFFSET_DATE_TIME, DateTimeFormatter.ISO_ZONED_DATE_TIME);

    @Data
    static class DateVo {
        //The ISO instant formatter that formats or parses an instant in UTC, such as '2011-12-03T10:15:30Z
        //DateTimeFormatter.ISO_INSTANT
        private ZonedDateTime isoInstant;

        //The ISO date-time formatter that formats or parses a date-time with an offset, such as '2011-12-03T10:15:30+01:00'.
        //DateTimeFormatter.ISO_OFFSET_DATE_TIME
        private ZonedDateTime isoOffsetDateTime;

        //The ISO-like date-time formatter that formats or parses a date-time with offset and zone, such as '2011-12-03T10:15:30+01:00[Europe/ Paris]'.
        private ZonedDateTime isoZonedDateTime;

        private ZonedDateTime dateTime;

        @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
        private ZonedDateTime dateTimeWithAnnotation;

        private ZonedDateTime timestamp;
    }

    private ZonedDateTimeDeserializer deserializer;


    @Test
    public void deserializeWithStandardFormat() {
        String json = """
                {
                "isoInstant":"2011-12-03T10:15:30Z",
                "isoOffsetDateTime":"2011-12-03T10:15:30+01:00",
                "isoZonedDateTime":"2011-12-03T10:15:30+01:00[Europe/Paris]",
                "dateTimeWithAnnotation":"2011-12-03 10:15:30",
                "timestamp":"1729786258"
                }
                """;

        DateVo dateVo = JsonUtil.readValue(json, DateVo.class);
        log.info("dateVo={}", dateVo);
        log.info("serial={}", JsonUtil.writeString(dateVo));
    }


    @Test
    public void deserializeWithCustomFormat() {
        String json = """
                {
                "isoInstant":"2011-12-03T10:15:30Z",
                "isoOffsetDateTime":"2011-12-03T10:15:30+01:00",
                "isoZonedDateTime":"2011-12-03T10:15:30+01:00[Europe/Paris]",
                "dateTime":"2011-12-03 10:15:30",
                "dateTimeWithAnnotation":"2011-12-03 10:15:30",
                "timestamp":"1729786258"
                }
                """;

        DateVo dateVo = JsonUtil.readValue(json, DateVo.class);
        log.info("dateVo={}", dateVo);
        log.info("serial={}", JsonUtil.writeString(dateVo));
    }

    @Test
    public void testDb(){

        ExchangeRateConfigHistoryEntity data = exchangeRateConfigHistoryDao.selectBySourceCodeAndTargetCodeAndDate("USD", "USD",  ZonedDateTime.now());
        log.info("data={}", data);
    }

    @Test
    public void testDb1 (){
        labelUserLogDao.deleteLabelLogByLabelIdAndDate(1L, ZonedDateTime.now());

        znsRunActivityUserDao.findCompanionCount(1L, ZonedDateTime.now().plusDays(10),2);

        znsRunActivityUserDao.countServiceUserNum(1L, ZonedDateTime.now().minusDays(10));
    }
}
