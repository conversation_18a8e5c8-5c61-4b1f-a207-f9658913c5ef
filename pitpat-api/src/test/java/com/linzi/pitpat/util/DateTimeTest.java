package com.linzi.pitpat.util;

import com.linzi.pitpat.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZonedDateTime;
import java.util.TimeZone;

@Slf4j
public class DateTimeTest {
    @Test
    public void testAddMinute() {
        ZonedDateTime fiveMinutes = DateUtil.formatMinites(ZonedDateTime.now().plusMinutes(1 + 1));
        String activityStartTime = DateUtil.dateStr(fiveMinutes, DateUtil.YYYY_MM_DD_HH_MM_SS);
        log.info("{}", activityStartTime);


        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime now = ZonedDateTime.now().withSecond(0);
        ZonedDateTime end = now.plusMinutes(1L);

        Duration duration = Duration.between(ZonedDateTime.now(), end);
        log.info("duration={}", duration.getSeconds());
        log.info("{}  -  {}", formatter.format(now.withSecond(0)), formatter.format(end.withSecond(0)));
        if (duration.getSeconds() <= 30) {
            end = end.plusMinutes(1L);
        }

        log.info("{}  -  {}", formatter.format(now.withSecond(0)), formatter.format(end.withSecond(0)));
    }

    @Test
    public void test() {
        // Your date-time string in the format "yyyy-MM-dd HH:mm:ss"
        String dateTimeString = "2023-12-14 15:30:45";

        // Define the formatter for the specified pattern
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // Parse the string and create a ZonedDateTime instance
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTimeString, formatter.withZone(ZoneId.systemDefault()));
        Instant instant = zonedDateTime.toInstant();
        // Print the ZonedDateTime
        System.out.println("Parsed ZonedDateTime: " + instant.toEpochMilli());


        // Your date string in the format "yyyy-MM-dd HH:mm:ss"
        String dateString = "2023-12-14 15:30:45";

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        ZonedDateTime date = null;
        try {
            date = dateFormat.parse(dateString);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        // Print the ZonedDateTime
        System.out.println("Parsed ZonedDateTime: " + date.toInstant().toEpochMilli());

    }

    @Test
    public void testConvertToZonedTime() {
        // Your date string in the format "yyyy-MM-dd HH:mm:ss"
        String dateString = "2023-12-16 11:04:00";
        //TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // Parse the string and create a ZonedDateTime instance
        ZonedDateTime dateTime = ZonedDateTime.parse(dateString, formatter);

        ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        Instant instant = zonedDateTime.toInstant();
        log.info("instant= {}", instant.toEpochMilli());


    }

    @Test
    public void testConvertTimeZone(){
        String dateString = "2023-12-16 03:04:00";
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = ZonedDateTime.parse(dateString, formatter);

        ZonedDateTime zonedDateTime1 = dateTime.withZoneSameInstant(ZoneOffset.UTC);
        ZonedDateTime zonedDateTime2 = dateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));

        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        ZoneOffset standardOffset = zoneId.getRules().getStandardOffset(Instant.now());
        log.info("{}",  standardOffset.getTotalSeconds());

        ZonedDateTime zonedDateTime3 = zonedDateTime1.plusSeconds(standardOffset.getTotalSeconds());

        log.info("a={}",zonedDateTime1);
        log.info("b={}",zonedDateTime3);

        log.info("a={}",zonedDateTime1.toEpochSecond());
        log.info("b={}",zonedDateTime2.toEpochSecond());
        log.info("c={}",zonedDateTime3.toEpochSecond());


        // 将本地时间转换为带时区信息的ZonedDateTime（假设转换为+8时区）
        ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));

        // 获取时间戳
        long timestamp = zonedDateTime.toInstant().toEpochMilli();

        System.out.println("ZonedDateTime: " + dateTime);
        System.out.println("Timestamp in +8 timezone: " + timestamp);
    }

    @Test
    public void testZoneOffset(){
        // 指定时区
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");

        // 获取当前时间在指定时区的 ZonedDateTime
        ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);

        // 获取时区偏移量
        ZoneOffset zoneOffset = zonedDateTime.getOffset();

        // 或者，如果只需要偏移量而不需要具体时间信息
        OffsetDateTime offsetDateTime = OffsetDateTime.now(zoneId);
        ZoneOffset zoneOffsetFromOffsetDateTime = offsetDateTime.getOffset();

        // 输出时区偏移量
        System.out.println("时区偏移量: " + zoneOffset);
        System.out.println("时区偏移量 (from OffsetDateTime): " + zoneOffsetFromOffsetDateTime);

    }

    @Test
    public void testAdd(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime now = ZonedDateTime.now().withSecond(0);
        ZonedDateTime end = now.plus(Duration.ofDays(1L));

        // 11:06:37点击立即挑战，则比赛开始时间=11:07:00
        // 11:06:50点击立即挑战，则比赛开始时间=11:08:00
        now = now.plusMinutes(1L);
        Duration duration = Duration.between(ZonedDateTime.now(), now);
        if (duration.getSeconds() <= 30) {
            now = now.plusMinutes(1L);
        }

        log.info(formatter.format(now));
    }
}
