package com.linzi.pitpat.util;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.BigDecimalUtil;
import com.linzi.pitpat.data.awardservice.model.entry.CouponI18n;
import com.linzi.pitpat.data.awardservice.service.CouponI18nService;
import com.linzi.pitpat.data.awardservice.service.CouponService;
import com.linzi.pitpat.data.messageservice.quartz.PushTask;
import com.linzi.pitpat.data.systemservice.quartz.BottomPopTask;
import com.linzi.pitpat.data.systemservice.quartz.HomePageConfigPopStatusTask;
import com.linzi.pitpat.data.userservice.quartz.VipTask;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Slf4j
public class I18nMsgTest extends PitpatApiApplicationTests {

    @Resource
    private BottomPopTask bottomPopTask;

    @Resource
    private HomePageConfigPopStatusTask homePageConfigPopStatusTask;

    @Resource
    private CouponService couponService;

    @Resource
    private CouponI18nService couponI18nService;
    @Resource
    private PushTask pushTask;
    @Resource
    private MessageSource messageSource;
    @Resource
    private VipTask vipTask;

    @Test
    public void testEn() {
        Arrays.stream(I18nConstant.LanguageCodeEnum.VALUES).forEach(item -> {

            String content = String.format(I18nMsgUtils.getLangMessage(item.getCode(), "activity.notify.remark.ActivityNotificationEnum.USER_DATA_BROADCASTING"), BigDecimalUtil.divide(BigDecimal.valueOf(2200), new BigDecimal(1600)),
                    20, 30,
                    22.056f, 21.556f);

            log.info("lang={}, content={}", item.getCode(), content);
        });

    }

    @Test
    public void getAllMsg() {
        //指定语言
        Arrays.stream(I18nConstant.LanguageCodeEnum.VALUES).forEach(item -> {
            String message = I18nMsgUtils.getLangMessage(item.getCode(), "activity.stage.wait.start.title");
            log.info(item.getCode(), message);
        });
    }


    @Test
    void testTask() {
        pushTask.timingTask();
    }

    @Test
    void configPop() {
//        homePageConfigPopStatusTask.scheduledTask();
        List<CouponI18n> saveList = new ArrayList<>();
        List<CouponI18n> couponI18ns = couponI18nService.selectAllList();
        List<Long> couponIds = couponI18ns.stream().map(CouponI18n::getCouponId).distinct().collect(Collectors.toList());
//        List<Coupon> list = couponService.listByQueryWrapper(Wrappers.<Coupon>lambdaQuery().eq(Coupon::getIsDelete, 0).notIn(Coupon::getId, couponIds));
//        list.stream().forEach(item -> {
//            // 英语
//            CouponI18n couponI18nEn = new CouponI18n();
//            couponI18nEn.setCouponId(item.getId());
//            couponI18nEn.setLangCode(I18nConstant.LanguageCodeEnum.en_US.getCode());
//            couponI18nEn.setLangName(I18nConstant.LanguageCodeEnum.en_US.getName());
//            couponI18nEn.setTitle(item.getTitle());
//            couponI18nEn.setCanUseDescription(item.getCanUseDescription());
//            couponI18nEn.setDescription(item.getDescription());
//            saveList.add(couponI18nEn);
//
//            CouponI18n couponI18nFr = new CouponI18n();
//            couponI18nFr.setCouponId(item.getId());
//            couponI18nFr.setLangCode(I18nConstant.LanguageCodeEnum.fr_CA.getCode());
//            couponI18nFr.setLangName(I18nConstant.LanguageCodeEnum.fr_CA.getName());
//            couponI18nFr.setTitle(item.getTitle());
//            couponI18nFr.setCanUseDescription(item.getCanUseDescription());
//            couponI18nFr.setDescription(item.getDescription());
//            saveList.add(couponI18nFr);
//
//        });
//        couponI18nService.saveAll(saveList);
    }


    @Test
    void testMsg() {
        String panp = I18nMsgUtils.getLangMessage(I18nConstant.LanguageCodeEnum.en_US.getCode(), "activity.new.pk.title", "panp", 1 + "");
        System.out.println(panp);
    }

    @Test
    public void testLang() {
        String key = "user.vip.expire.remind";
        String message = I18nMsgUtils.getMessage(key);
        String langMessage = I18nMsgUtils.getLangMessage(LocaleContextHolder.getLocale().getLanguage(), key);
        String langMessage1 = I18nMsgUtils.getLangMessage(Locale.CHINA.toString(), key);
        log.info("{},{},{}", message, langMessage, langMessage1);

        langMessage = I18nMsgUtils.getLangMessage("zh_CN", key);
        langMessage1 = I18nMsgUtils.getLangMessage(Locale.CHINA.toString(), key);
        log.info("{},{},{}", message, langMessage, langMessage1);
    }


}
