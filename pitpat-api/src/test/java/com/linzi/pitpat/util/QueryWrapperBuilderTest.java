package com.linzi.pitpat.util;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.mallservice.mapper.GoodsCommentMapper;
import com.linzi.pitpat.data.mallservice.model.entity.GoodsCommentDo;
import com.linzi.pitpat.framework.db.mybatis.wrapper.QueryWrapperBuilder;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.PageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serial;
import java.time.ZonedDateTime;
import java.time.ZonedDateTime;
import java.util.List;

@Slf4j
public class QueryWrapperBuilderTest extends PitpatApiApplicationTests {

    @Autowired
    private GoodsCommentMapper goodsCommentMapper;

    @Test
    public void testQuery() {
        GoodsCommentQuery query = new GoodsCommentQuery();
        query.setContent("setContent");
        query.setContentEq("setContentEq");
        query.setContentNe("setContentNe");

        query.setContentGt("setContentGt");
        query.setContentGe("setContentGe");
        query.setContentLt("setContentLt");
        query.setContentLe("setContentLe");
        query.setContentList(List.of("1L"));
        query.setContentLike("setContentLike");
        query.setContentLikeLeft("setContentLikeLeft");
        query.setContentLikeRight("setContentLikeRight");

        query.setSkuId(1L);
        query.setSkuIdEq(1L);
        query.setSkuIdNe(1L);
        query.setSkuIdGt(1L);
        query.setSkuIdGe(1L);
        query.setSkuIdLt(1L);
        query.setSkuIdLe(1L);

        query.setGoodsId(2L);
        query.setGmtModified(ZonedDateTime.now());
        query.setGmtModifiedGe(ZonedDateTime.now());
        query.setSkuIdList(List.of(1L));

        query.setOrders(List.of(OrderItem.asc("id"), OrderItem.desc("skuId"), OrderItem.desc("gmtModified")));
        ;
        Wrapper<GoodsCommentDo> queryWrapper = buildQueryWrapper(query);

        GoodsCommentDo byQuery = goodsCommentMapper.selectOne(queryWrapper);

        log.info("byQuery={}", byQuery);
        log.info("getCustomSqlSegment={}", queryWrapper.getCustomSqlSegment());
        log.info("getTargetSql={}", queryWrapper.getTargetSql());
        log.info("getSqlSegment={}", queryWrapper.getSqlSegment());
        /**
         * SELECT
         * 	id,
         * 	creator,
         * 	modifier,
         * 	user_id,
         * 	order_id,
         * 	goods_id,
         * 	sku_id,
         * 	content,
         * 	medias,
         * 	score,
         * 	source,
         * 	content_type,
         * 	sync_status,
         * 	is_show,
         * 	like_count,
         * 	community_id,
         * 	default_language_code,
         * 	default_block_reason,
         * 	is_delete,
         * 	gmt_create,
         * 	gmt_modified
         * FROM
         * 	zns_goods_comment
         * WHERE
         * 	is_delete = 0
         * 	AND (
         * 		`goods_id` = 2
         * 		AND `sku_id` = 1
         * 		AND `gmt_modified` = '2025-02-23 16:19:17'
         * 		AND `gmt_modified` >= '2025-02-23 16:19:17'
         * 		AND `sku_id` = 1
         * 		AND `sku_id` <> 1
         * 		AND `sku_id` > 1
         * 		AND `sku_id` >= 1
         * 		AND `sku_id` < 1 AND `sku_id` <= 1 AND `sku_id` IN (1) AND `content` = 'setContent' AND `content` = 'setContentEq' AND `content` <> 'setContentNe' AND `content` > 'setContentGt' AND `content` >= 'setContentGe'
         * 		AND `content` < 'setContentLt'
         * 		AND `content` <= 'setContentLe'
         * 		AND `content` LIKE '%setContentLike%'
         * 		AND `content` LIKE '%setContentLikeLeft'
         * 		AND `content` LIKE 'setContentLikeRight%'
         * 	AND `content` IN ('1L'))
         * ORDER BY
         * 	`id` ASC,
         * 	`sku_id` DESC,
         * 	`gmt_modified` DESC
         */
    }


    private static Wrapper<GoodsCommentDo> buildQueryWrapper(GoodsCommentQuery query) {
        QueryWrapper<GoodsCommentDo> build = QueryWrapperBuilder.build(query, GoodsCommentDo.class);
        log.info("build={}", build);
        return build;
    }


    /**
     * 商品评价表列表查询对象
     *
     * @since 2025年2月13日
     */
    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class GoodsCommentQuery extends PageQuery {

        @Serial
        private static final long serialVersionUID = 2239555821658955938L;
        private Long id;
        private Long userId;
        private Long goodsId;
        private Long skuId;
        private ZonedDateTime gmtModified;
        private ZonedDateTime gmtModifiedGe;
        private ZonedDateTime aa;
        //add
        private Long skuIdEq;
        private Long skuIdNe;
        private Long skuIdGt;
        private Long skuIdGe;
        private Long skuIdLt;
        private Long skuIdLe;

        private Long skuIdLike;

        private List<Long> skuIdList;


        private String content;
        private String contentEq;
        private String contentNe;
        private String contentGt;
        private String contentGe;
        private String contentLt;
        private String contentLe;

        private String contentLike;
        private String contentLikeLeft;
        private String contentLikeRight;

        private List<String> contentList;
    }

}
