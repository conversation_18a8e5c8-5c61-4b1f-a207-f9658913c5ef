package com.linzi.pitpat.util;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.messageservice.model.vo.ImMessageBo;
import com.linzi.pitpat.data.util.TencentImUtil;
import com.linzi.pitpat.data.vo.FollowImMessageBo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.List;

public class TencentImUtilTest extends PitpatApiApplicationTests {

    @Autowired
    private TencentImUtil tencentImUtil;

    @Test
    public void testGetTxCloudUserSig(){
        tencentImUtil.accountImport("83410", "young","");
    }


    @Test
    public void testMultiAccountImport(){
        tencentImUtil.multiAccountImport(List.of("83410"));
    }


    @Test
    public void testAccountDelete(){
        tencentImUtil.accountDelete(List.of("83410"));
    }

    @Test
    public void testAccountCheck(){
        tencentImUtil.accountCheck(List.of("83410"));
    }

    @Test
    public void  testAddBlacklist(){
        tencentImUtil.addBlacklist(1L, List.of(83410L));
    }

    @Test
    public void testDeleteBlacklist(){
        tencentImUtil.deleteBlacklist(1L, List.of(83410L));
    }


    @Test
    public void testSendMsg() {
        FollowImMessageBo bo = new FollowImMessageBo();
        bo.setMsg("you are the best");
        bo.setUserName("Caton Nelson");

//        tencentImUtil.sendMsg(1, "82498", "83410", "TIMCustomElem", JsonUtil.writeString(bo));

    }

    @Test
    public void testBatchSendMsg(){
        ImMessageBo bo = new ImMessageBo();
        bo.setActivityType("1");
        bo.setUserName("panp");
        bo.setImage("");
        bo.setDetailID("182");
        bo.setChallengeType("1");
        bo.setMoney("11");
        bo.setState("0");
        long time = ZonedDateTime.now().toInstant().toEpochMilli();
        bo.setTime(String.valueOf(time));
        bo.setActivityEntryFee("1");
        bo.setDistance("1");
        bo.setIsKph("true");
        bo.setInviteUserName("潘昊");
        bo.setFriendId("14");
        bo.setBusinessID("match");
//        tencentImUtil.batchSendMsg(2, "82498", Arrays.asList("83410"), TencentImConstant.TIM_CUSTOM_ELEM, JsonUtil.writeString(bo));
    }

    @Test
    public void testAdminMsgWithDraw(){
        tencentImUtil.adminMsgWithDraw("82498","83410","1847095770_900575686_1721289739");
    }

    @Test
    public void testPortraitSet(){
        tencentImUtil.portraitSet("83410", "https://deerruntreadmill.com/cdn/shop/articles/D_a8568aaa-d314-457b-9498-7f787dcc5e0b.jpg", "John", 1);
    }

    @Test
    public void testPortraitGet(){
        tencentImUtil.portraitGet(List.of("83410"));
    }

    @Test

    public  void testAdminGetRoamMsg(){
        tencentImUtil.adminGetRoamMsg("82498","83410","1847095770_900575686_1721289739", 1721290167L,1721290567L);
    }

    @Test
    public void testGetContactList(){
       tencentImUtil.getContactList("83410", 0, 0, 0, 0, 7);
    }
}
