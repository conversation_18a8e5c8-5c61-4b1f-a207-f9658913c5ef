package com.linzi.pitpat.controller;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.api.activityservice.manager.RunActivityManager;
import com.linzi.pitpat.dto.request.NewPkActivityRequestDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/9 18:22
 */
public class RunActivityControllerTest extends PitpatApiApplicationTests {

    @Autowired
    private RunActivityManager activityManager;
    /**
     * 创建新pk活动
     * @return
     */
    @Test
    public void launchNewPkActivity() {
        NewPkActivityRequestDto request = new NewPkActivityRequestDto();
        List<Long> ids = new ArrayList<>();
        ids.add(14l);
        ids.add(205l);
        request.setActivityUserIds(ids);
        request.setActivityType(17);
        request.setActivityEntryScore(100);
        request.setActivityRouteId(101l);
        request.setCompleteRuleType(1);
        request.setActivityStartTime(ZonedDateTime.now().toInstant().toEpochMilli());
        request.setRunMileage(new BigDecimal(1000));
        request.setAppRoomId(1000l);
        request.setBonusRuleType(4);
        request.setActivityTypeSub(6);
        request.setInitiatorUserId(263l);
        activityManager.createNewPkActivity(request, 0,null);
    }
}
