package com.linzi.pitpat.controller;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.api.activityservice.dto.request.ChallengeUserDto;
import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.activityservice.manager.ActivityMessageManager;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsUserRunDataDetailsEntity;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeUserHomeResp;
import com.linzi.pitpat.data.activityservice.model.resp.ChallengeUserResp;
import com.linzi.pitpat.data.activityservice.model.vo.OfflinePkAwardVo;
import com.linzi.pitpat.data.activityservice.service.ZnsUserRunDataDetailsService;
import com.linzi.pitpat.data.constants.Constants;
import com.linzi.pitpat.data.systemservice.enums.ConfigKeyEnums;
import com.linzi.pitpat.data.user.converter.UserConverter;
import com.linzi.pitpat.data.userservice.model.entity.ZnsUserEntity;
import com.linzi.pitpat.dto.SysConfigDto;
import com.linzi.pitpat.dto.request.SysConfigQueryRequestDto;
import com.linzi.pitpat.interfaces.SysConfigApi;
import com.linzi.pitpat.user.api.dto.UserEntityDto;
import com.linzi.pitpat.user.api.dto.request.UserQueryDto;
import com.linzi.pitpat.user.api.interfaces.UserApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/8 11:18
 */
@Slf4j
public class AppChallengeControllerTest extends PitpatApiApplicationTests {
    @Autowired
    private ZnsUserRunDataDetailsService userRunDataDetailsService;

    @Autowired
    private ActivityMessageManager activityMessageManager;
    @Autowired
    private SysConfigApi sysConfigApi;
    @Autowired
    private UserApi userApi;
    @Autowired
    private UserConverter userAppConverter;

    /**
     * 和我的最佳配速匹配的用户列表
     */
    @Test
    public void userList() {
        ChallengeUserDto challengeUserDto = new ChallengeUserDto();
        challengeUserDto.setUserId(14l);
        ChallengeUserHomeResp challengeUserHomeResp = new ChallengeUserHomeResp();
        List<ChallengeUserResp> list = new ArrayList<>();
        ZonedDateTime day7 = DateUtil.addDays(DateUtil.startOfDate(ZonedDateTime.now()), -7);

        Integer average_pace = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileageMinRunTime(challengeUserDto.getUserId(), Constants.target_1_Mile, Constants.target_1_Mile, day7,1, null, 0);
        ZnsUserRunDataDetailsEntity runDataDetailsEntity = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileage(challengeUserDto.getUserId(), Constants.target_1_Mile, Constants.target_1_Mile, day7, average_pace, null,0 );
        Integer minaveragePace = 342;
        if (runDataDetailsEntity != null) {
            minaveragePace = runDataDetailsEntity.getAveragePace();
        }

        //为了提升性能的做法
        List<ChallengeUserResp> temp = userRunDataDetailsService.selectMinRunTimeByUserIdDistanceTargetRunMileageRuntime(
                challengeUserDto.getUserId(),
                Constants.target_1_Mile,
                Constants.target_1_Mile,
                1,
                minaveragePace - 300,
                minaveragePace + 300,
                day7,null);
        List<Long> userIdList = temp.stream().map(ChallengeUserResp::getUserId).collect(Collectors.toList());
        List<UserEntityDto> dtoList = userApi.listByIds(new UserQueryDto().setIds(userIdList)).getData();
        Map<Long, ZnsUserEntity> znsUserEntities = userAppConverter.toEntityList(dtoList).stream().collect(Collectors.toMap(ZnsUserEntity::getId, Function.identity()));
        Collections.sort(temp);
        // 为了提升性能
        List<Long> userIds = new ArrayList<>();
        int i = 0;
        for (ChallengeUserResp challengeUserResp : temp) {
            if (userIds.contains(challengeUserResp.getUserId())) {
                continue;
            }
            ZnsUserEntity znsUserEntity = znsUserEntities.get(challengeUserResp.getUserId());
            if(Objects.equals(znsUserEntity.getAcceptChallenge(), 0 )){
                continue;
            }
            challengeUserResp.setHeadPortrait(znsUserEntity.getHeadPortrait());
            challengeUserResp.setFirstName(znsUserEntity.getFirstName());
            if (i > 49) {
                break;
            }
            list.add(challengeUserResp);
            userIds.add(challengeUserResp.getUserId());
            i++;
        }

        // 展示离线pk奖励，奖励不再按照跑步时间区分
        SysConfigDto sysConfigDto = sysConfigApi.selectSysConfigByKey(new SysConfigQueryRequestDto(ConfigKeyEnums.OFFLINE_PK_CHALLENGE_AWARD.getCode())).getData();
        OfflinePkAwardVo offlinePkAwardVo = JsonUtil.readValue(sysConfigDto.getConfigValue(), OfflinePkAwardVo.class);
        for (ChallengeUserResp challengeUserResp : list) {
            challengeUserResp.setScore(offlinePkAwardVo.getScore());
            Currency userCurrency = I18nConstant.CurrencyCodeEnum.USD.getCurrency();
            challengeUserResp.setCurrency(userCurrency);
            CurrencyAmount otherCurrencyAmount = offlinePkAwardVo.getAmountList().stream()
                    .filter(e -> e.getCurrencyCode().equals(userCurrency.getCurrencyCode())).findFirst().orElse(null);
            challengeUserResp.setAwardAmount(otherCurrencyAmount.getAmount());
            challengeUserResp.setCouponNum(offlinePkAwardVo.getCouponNum());
        }


        challengeUserHomeResp.setTargetMile(Constants.target_1_Mile);
        challengeUserHomeResp.setList(list);
        log.info("{}"+challengeUserHomeResp);
    }

}
