package com.linzi.pitpat.controller;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.api.controller.h5.ApiCustomH5Contoller;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.data.systemservice.dto.api.request.AppCustomH5RequestDto;
import com.linzi.pitpat.data.systemservice.dto.api.response.AppCustomH5ResponseDto;
import com.linzi.pitpat.lang.Result;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class ApiCustomH5ContollerTest extends PitpatApiApplicationTests {
    @Resource
    private ApiCustomH5Contoller apiCustomH5Contoller;

    @Test
    void getById_ReturnsSuccessResult() {
        // Arrange
        AppCustomH5RequestDto requestDto = new AppCustomH5RequestDto();
        requestDto.setId(3443463L);

        Result<AppCustomH5ResponseDto> result = apiCustomH5Contoller.getById(requestDto);
        System.out.println(JsonUtil.writeString(result));
    }
}
