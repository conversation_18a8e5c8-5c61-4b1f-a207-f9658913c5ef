package com.linzi.pitpat;

import com.linzi.pitpat.data.listener.QueueConstants;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;
import java.util.stream.LongStream;

public class QueueMessageServiceTest extends PitpatApiApplicationTests {
    @Autowired
    private QueueMessageService queueMessageService;

    @Test
    public void sendContentText() {
        LongStream.range(0,10).forEach(i -> {
            try {
                TimeUnit.SECONDS.sleep(i);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            queueMessageService.sendDelayMessage(QueueConstants.contentDelayExchange, QueueConstants.contentPublishKey, i, 3_000L *i);
        });
    }
}
