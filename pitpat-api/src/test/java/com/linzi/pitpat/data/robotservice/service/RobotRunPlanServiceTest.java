package com.linzi.pitpat.data.robotservice.service;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.activityservice.mapper.MindUserMatchDao;
import com.linzi.pitpat.data.activityservice.mapper.ZnsRunActivityDao;
import com.linzi.pitpat.data.activityservice.model.entity.MindUserMatch;
import com.linzi.pitpat.data.activityservice.model.entity.ZnsRunActivityEntity;
import com.linzi.pitpat.data.robotservice.manager.RobotRunPlanManager;
import com.linzi.pitpat.data.robotservice.service.impl.RobotRunPlanServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;

public class RobotRunPlanServiceTest extends PitpatApiApplicationTests {
    @Autowired
    private RobotRunPlanServiceImpl robotRunPlanService;

    @Autowired
    private ZnsRunActivityDao znsRunActivityDao;
    @Autowired
    private MindUserMatchDao mindUserMatchDao;
    @Autowired
    private RobotRunPlanManager robotRunPlanManager;
    @Test
    public void doCreatePlanByRunTime(){
        ZnsRunActivityEntity activityEntity = znsRunActivityDao.selectActivityById(309042L);
        MindUserMatch mindUserMatch = mindUserMatchDao.selectMindUserMatchById(13255094L);
        robotRunPlanManager.doCreatePlanByRunTime(mindUserMatch, activityEntity, ZonedDateTime.now());

    }
    @Test
    public void doCreatePlanByRunMileage(){
        ZnsRunActivityEntity activityEntity = znsRunActivityDao.selectActivityById(309042L);
        MindUserMatch mindUserMatch = mindUserMatchDao.selectMindUserMatchById(13255094L);
        robotRunPlanManager.doCreatePlanByRunMileage(mindUserMatch, activityEntity, ZonedDateTime.now());

    }
}
