
package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.data.awardservice.model.entry.ZnsUserAccountDetailEntity;
import com.linzi.pitpat.data.awardservice.model.query.UserAccountDetailByQuery;
import com.linzi.pitpat.data.awardservice.service.ZnsUserAccountDetailService;
import com.linzi.pitpat.data.enums.AccountDetailTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(SpringExtension.class)
public class AccountServiceTest extends PitpatApiApplicationTests {

    @Autowired
    private ZnsUserAccountDetailService znsUserAccountDetailService;

    @Test
    public void listByIds_ValidIds_ReturnsEntities() {
        List<Long> ids = new ArrayList<>();
        ids.add(1488600L);
        ids.add(1488601L);
        List<ZnsUserAccountDetailEntity> result = znsUserAccountDetailService.listByIds(ids);
        assertEquals(2, result.size());
    }

    @Test
    public void findList_ValidQuery_ReturnsEntities() {
        UserAccountDetailByQuery query = new UserAccountDetailByQuery().setActivityId(1743969L).setTradeStatus(2);
        UserAccountDetailByQuery query2 = new UserAccountDetailByQuery().setActivityId(311462L).setUserId(345L).setUserIds(Arrays.asList(345L,346L,347L)).setTradeType(Arrays.asList(AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ORDINARY_AWARD.getType(),AccountDetailTypeEnum.BATTLE_PASS_COMPLETION_ADVANCED_AWARD.getType())).setTradeStatus(2).setStartTime(ZonedDateTime.now()).setEndTime(ZonedDateTime.now());
        List<ZnsUserAccountDetailEntity> result = znsUserAccountDetailService.findList(query);
        List<ZnsUserAccountDetailEntity> result2 = znsUserAccountDetailService.findList(query2);
        assertEquals(1, result.size());
        assertFalse(result2.isEmpty());
    }
    // Add other test methods as needed
}