package com.linzi.pitpat.data.util;

import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import org.junit.jupiter.api.Test;

public class DingTalkUtilsTest {

    @Test
    public void sendTest() {
        String at = "6383c04b3b80ae816ed14f80db9e176fb4727d2f9149e62a4cdf4bed158978c3";
        String se = "SEC8dbd8493bef79a3a42bdb9f055329eb54a75eaa296576866e4124f04d425f970";
        DingTalkUtils.sendMsg(DingTalkRequestDto.of(at, se, "测试消息"));
    }
}
