package com.linzi.pitpat.data.activityservice.biz;

import com.linzi.pitpat.PitpatApiApplicationTests;
import com.linzi.pitpat.core.constants.enums.YesNoStatus;
import com.linzi.pitpat.data.awardservice.model.entry.UserCoupon;
import com.linzi.pitpat.data.awardservice.service.UserCouponService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.time.ZonedDateTime;
import java.util.List;

class UserCouponServiceTest extends PitpatApiApplicationTests {

    @Autowired
    private UserCouponService userCouponService;
    @Test
    void insert() {
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setCouponId(20L);
        userCoupon.setUserId(82925L);
        userCoupon.setStatus(5);
        ZonedDateTime now = ZonedDateTime.now();
        userCoupon.setGmtStart(now);
        userCoupon.setSourceType(8);
        userCoupon.setAmount(new BigDecimal("2.00"));
        userCoupon.setIsNew(YesNoStatus.YES.getCode());
        userCouponService.insert(userCoupon);
    }

    @Test
    void findListByIds() {
        List<Long> userCouponIds = Arrays.asList(1L,2L,3L);
        userCouponService.findListByIds(userCouponIds);
    }


    @Test
    void findCount() {
//        userCouponService.findCount()
    }

    @Test
    void updateBatchByIds() {
    }

    @Test
    void findListByTimeAndStatus() {
    }

    @Test
    void findListBySourceType() {
    }

    @Test
    void findListByActivityIdAndStatus() {
    }

    @Test
    void findByQuery() {
    }
}