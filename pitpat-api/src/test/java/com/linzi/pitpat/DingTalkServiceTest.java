package com.linzi.pitpat;

import com.linzi.pitpat.data.quartz.ServiceMonitoringTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class DingTalkServiceTest extends PitpatApiApplicationTests {

    @Autowired
    private DingTalkService dingTalkService;

    @Test
    public void testGetToken(){
        dingTalkService.getToken();
    }

    @Test
    public void testSendDingTalk(){
        dingTalkService.sendDingMsg("msg",1, List.of("16889536420004122"));
    }

    @Test
    public void testGetUserId(){
        dingTalkService.getUserId("18404904315");
        dingTalkService.getUserId("18672349815");
    }

    @Test
    public void testUserIdList() {
        List<String> mobiles = ServiceMonitoringTask.DingtalkUserEnum.getMobiles();
        List<String> userIds = ServiceMonitoringTask.DingtalkUserEnum.getUserIds();
        System.out.println(userIds);
        System.out.println(mobiles);
    }
}
