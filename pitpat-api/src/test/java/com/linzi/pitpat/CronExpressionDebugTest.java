package com.linzi.pitpat;

import org.junit.jupiter.api.Test;
import org.springframework.scheduling.support.CronExpression;

import java.time.ZonedDateTime;

/**
 * Debug test to understand the actual behavior of the cron expression
 */
class CronExpressionDebugTest {

    @Test
    void debugCronExpression() {
        CronExpression cronExpression = CronExpression.parse("0 */1 20-23,0-9 * * ?");

        // Test various times to understand the pattern
        ZonedDateTime[] testTimes = {
            ZonedDateTime.of(2024, 1, 15, 0, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 1, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 9, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 10, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 15, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 19, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 20, 0, 0, 0, java.time.ZoneId.systemDefault()),
            ZonedDateTime.of(2024, 1, 15, 23, 0, 0, 0, java.time.ZoneId.systemDefault())
        };

        System.out.println("=== Cron Expression Debug: 0 */1 20-23,0-9 * * ? ===");

        for (ZonedDateTime testTime : testTimes) {
            ZonedDateTime next = cronExpression.next(testTime);
            System.out.printf("From %s -> Next: %s%n", testTime, next);
        }

        // Test what happens at exact execution times
        System.out.println("\n=== Testing exact execution times ===");
        ZonedDateTime exactTime = ZonedDateTime.of(2024, 1, 15, 20, 0, 0, 0, java.time.ZoneId.systemDefault());
        ZonedDateTime nextFromExact = cronExpression.next(exactTime);
        System.out.printf("From exact 20:00 -> Next: %s%n", nextFromExact);

        // Test one minute after
        ZonedDateTime oneMinuteAfter = exactTime.plusMinutes(1);
        ZonedDateTime nextFromOneMinute = cronExpression.next(oneMinuteAfter);
        System.out.printf("From 20:01 -> Next: %s%n", nextFromOneMinute);
    }
}
