#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期工具类替换脚本
将 DateUtil.formateDate("日期字符串", DateUtil.YYYY_MM_DD_HH_MM_SS) 
替换为 DateTimeUtil.parse("日期字符串")
"""

import os
import re
import sys
from pathlib import Path

def replace_date_util_calls(file_path):
    """
    替换单个文件中的DateUtil调用
    
    Args:
        file_path: 文件路径
        
    Returns:
        tuple: (是否有修改, 修改次数)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 正则表达式匹配 DateUtil.formateDate("日期字符串", DateUtil.YYYY_MM_DD_HH_MM_SS)
        # 支持多种格式的日期字符串和常量
        pattern = r'DateUtil\.formateDate\s*\(\s*"([^"]+)"\s*,\s*DateUtil\.[A-Z_]+\s*\)'
        
        def replacement(match):
            date_string = match.group(1)
            return f'DateTimeUtil.parse("{date_string}")'
        
        # 执行替换
        content = re.sub(pattern, replacement, content)
        
        # 检查是否有修改
        if content != original_content:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 计算修改次数
            modifications = len(re.findall(pattern, original_content))
            return True, modifications
        
        return False, 0
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False, 0

def scan_and_replace(root_dir, file_extensions=None, dry_run=False):
    """
    扫描目录并替换文件中的DateUtil调用
    
    Args:
        root_dir: 根目录路径
        file_extensions: 文件扩展名列表，默认为['.java']
        dry_run: 是否为试运行模式（只检查不修改）
    """
    if file_extensions is None:
        file_extensions = ['.java']
    
    root_path = Path(root_dir)
    if not root_path.exists():
        print(f"错误: 目录 {root_dir} 不存在")
        return
    
    total_files = 0
    modified_files = 0
    total_modifications = 0
    
    print(f"开始扫描目录: {root_dir}")
    print(f"文件类型: {', '.join(file_extensions)}")
    print(f"模式: {'试运行' if dry_run else '实际修改'}")
    print("-" * 60)
    
    # 遍历所有文件
    for file_path in root_path.rglob('*'):
        if file_path.is_file() and file_path.suffix in file_extensions:
            total_files += 1
            
            if dry_run:
                # 试运行模式：只检查不修改
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    pattern = r'DateUtil\.formateDate\s*\(\s*"([^"]+)"\s*,\s*DateUtil\.[A-Z_]+\s*\)'
                    matches = re.findall(pattern, content)
                    
                    if matches:
                        modified_files += 1
                        total_modifications += len(matches)
                        print(f"📄 {file_path}")
                        for i, date_str in enumerate(matches, 1):
                            print(f"   {i}. DateUtil.formateDate(\"{date_str}\", DateUtil.YYYY_MM_DD_HH_MM_SS)")
                            print(f"      -> DateTimeUtil.parse(\"{date_str}\")")
                        print()
                        
                except Exception as e:
                    print(f"检查文件 {file_path} 时出错: {e}")
            else:
                # 实际修改模式
                has_changes, modifications = replace_date_util_calls(file_path)
                
                if has_changes:
                    modified_files += 1
                    total_modifications += modifications
                    print(f"✅ 已修改: {file_path} ({modifications} 处)")
    
    # 输出统计信息
    print("-" * 60)
    print(f"扫描完成!")
    print(f"总文件数: {total_files}")
    print(f"{'需要修改' if dry_run else '已修改'}的文件数: {modified_files}")
    print(f"{'需要修改' if dry_run else '已修改'}的代码行数: {total_modifications}")
    
    if dry_run and modified_files > 0:
        print("\n💡 这是试运行结果，如需实际修改请使用 --execute 参数")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='替换DateUtil.formateDate调用为DateTimeUtil.parse')
    parser.add_argument('directory', nargs='?', default='.', 
                       help='要扫描的目录路径 (默认: 当前目录)')
    parser.add_argument('--extensions', '-e', nargs='+', default=['.java'],
                       help='文件扩展名 (默认: .java)')
    parser.add_argument('--dry-run', '-d', action='store_true',
                       help='试运行模式，只检查不修改')
    parser.add_argument('--execute', '-x', action='store_true',
                       help='执行实际修改')
    
    args = parser.parse_args()
    
    # 如果既没有指定dry-run也没有指定execute，默认为dry-run
    if not args.dry_run and not args.execute:
        args.dry_run = True
        print("⚠️  默认为试运行模式，如需实际修改请使用 --execute 参数\n")
    
    scan_and_replace(
        root_dir=args.directory,
        file_extensions=args.extensions,
        dry_run=args.dry_run
    )

if __name__ == "__main__":
    main()
