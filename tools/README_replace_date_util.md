# DateUtil 替换工具

这个脚本用于批量替换Java代码中的日期格式化方法调用。

## 功能说明

将以下格式的代码：
```java
DateUtil.formateDate("2023-09-20 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS)
```

替换为：
```java
DateTimeUtil.parse("2023-09-20 00:00:00")
```

## 使用方法

### 1. 试运行模式（推荐先使用）
```bash
# 扫描当前目录
python tools/replace_date_util.py

# 扫描指定目录
python tools/replace_date_util.py /path/to/your/project

# 明确指定试运行模式
python tools/replace_date_util.py --dry-run
```

### 2. 实际修改模式
```bash
# 执行实际修改
python tools/replace_date_util.py --execute

# 修改指定目录
python tools/replace_date_util.py /path/to/your/project --execute
```

### 3. 高级选项
```bash
# 指定文件扩展名
python tools/replace_date_util.py --extensions .java .kt

# 组合使用
python tools/replace_date_util.py /path/to/project --extensions .java --execute
```

## 参数说明

- `directory`: 要扫描的目录路径（可选，默认为当前目录）
- `--extensions, -e`: 文件扩展名列表（默认：.java）
- `--dry-run, -d`: 试运行模式，只检查不修改
- `--execute, -x`: 执行实际修改
- `--help, -h`: 显示帮助信息

## 使用示例

### 示例1：检查当前项目
```bash
cd /Users/<USER>/Projects/profressional/linzikg/pitpat-server-dev
python tools/replace_date_util.py --dry-run
```

### 示例2：修改特定模块
```bash
python tools/replace_date_util.py pitpat-common/pitpat-data --execute
```

### 示例3：检查多种文件类型
```bash
python tools/replace_date_util.py --extensions .java .groovy --dry-run
```

## 输出示例

### 试运行模式输出：
```
开始扫描目录: .
文件类型: .java
模式: 试运行
------------------------------------------------------------
📄 pitpat-common/pitpat-data/src/main/java/com/example/Service.java
   1. DateUtil.formateDate("2023-09-20 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS)
      -> DateTimeUtil.parse("2023-09-20 00:00:00")
   2. DateUtil.formateDate("2024-01-01 12:30:45", DateUtil.YYYY_MM_DD_HH_MM_SS)
      -> DateTimeUtil.parse("2024-01-01 12:30:45")

------------------------------------------------------------
扫描完成!
总文件数: 1250
需要修改的文件数: 15
需要修改的代码行数: 28

💡 这是试运行结果，如需实际修改请使用 --execute 参数
```

### 实际修改模式输出：
```
开始扫描目录: .
文件类型: .java
模式: 实际修改
------------------------------------------------------------
✅ 已修改: pitpat-common/pitpat-data/src/main/java/com/example/Service.java (2 处)
✅ 已修改: pitpat-api/src/main/java/com/example/Controller.java (1 处)

------------------------------------------------------------
扫描完成!
总文件数: 1250
已修改的文件数: 15
已修改的代码行数: 28
```

## 支持的模式匹配

脚本会匹配以下格式的代码：

```java
// 基本格式
DateUtil.formateDate("2023-09-20 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS)

// 带空格的格式
DateUtil.formateDate( "2023-09-20 00:00:00" , DateUtil.YYYY_MM_DD_HH_MM_SS )

// 不同的常量
DateUtil.formateDate("2023-09-20", DateUtil.YYYY_MM_DD)
DateUtil.formateDate("2023-09-20 12:30:45", DateUtil.YYYY_MM_DD_HH_MM_SS)
```

## 注意事项

1. **备份重要文件**：在执行实际修改前，建议先备份重要文件
2. **先试运行**：建议先使用试运行模式检查结果
3. **版本控制**：确保代码在版本控制系统中，方便回滚
4. **测试验证**：修改后请运行测试确保功能正常

## 安全特性

- 默认为试运行模式，避免意外修改
- 支持指定目录和文件类型
- 详细的修改日志输出
- 异常处理，避免脚本崩溃

## 故障排除

### 权限问题
```bash
chmod +x tools/replace_date_util.py
```

### 编码问题
脚本使用UTF-8编码，确保文件编码正确。

### Python版本
需要Python 3.6+版本。
