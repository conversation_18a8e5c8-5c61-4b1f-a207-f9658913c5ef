# DateUtil 替换工具 - 快速开始

## 🚀 快速使用

### 1. 检查需要修改的文件（推荐先执行）
```bash
python3 tools/replace_date_util.py --dry-run
```

### 2. 执行实际修改
```bash
python3 tools/replace_date_util.py --execute
```

### 3. 修改特定目录
```bash
# 只修改 pitpat-common 模块
python3 tools/replace_date_util.py pitpat-common --execute

# 只修改 pitpat-data 模块
python3 tools/replace_date_util.py pitpat-common/pitpat-data --execute
```

## 📝 替换示例

**替换前：**
```java
ZonedDateTime updatedAt = DateUtil.formateDate("2023-09-20 00:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
```

**替换后：**
```java
ZonedDateTime updatedAt = DateTimeUtil.parse("2023-09-20 00:00:00");
```

## ⚠️ 注意事项

1. **先试运行**：使用 `--dry-run` 查看会修改哪些文件
2. **备份代码**：确保代码已提交到Git
3. **测试验证**：修改后运行测试确保功能正常

## 🔍 常用命令

```bash
# 查看帮助
python3 tools/replace_date_util.py --help

# 检查整个项目
python3 tools/replace_date_util.py . --dry-run

# 修改整个项目
python3 tools/replace_date_util.py . --execute

# 只检查Java文件
python3 tools/replace_date_util.py --extensions .java --dry-run
```
